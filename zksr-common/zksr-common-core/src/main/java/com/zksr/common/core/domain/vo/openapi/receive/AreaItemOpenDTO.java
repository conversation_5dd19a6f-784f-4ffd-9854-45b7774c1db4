package com.zksr.common.core.domain.vo.openapi.receive;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 城市上架商品对象 prdt_area_item
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AreaItemOpenDTO {
    /** 上下架 */
    @ApiModelProperty(value = "0:下架;1:上架")
    private Integer type;

    /** 商品单位类型 */
    @ApiModelProperty(value = "EA:小单位;IP:中单位;CS:大单位，enum:AreaItemOpenDTOItemUnitEnum")
    private String itemUnit;


    /** 城市上架商品id */
    @ApiModelProperty(value = "城市上架商品id")
    private Long areaItemId;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 城市id */
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /** 城市展示分类id */
    @ApiModelProperty(value = "城市展示分类id")
    private Long areaClassId;

    /** 上下架状态 */
    @ApiModelProperty(value = "上下架状态")
    private Integer shelfStatus;

    /** 商品SPU id */
    @ApiModelProperty(value = "商品SPU ID")
    private Long spuId;

    /** 商品SPU 编号 */
    @ApiModelProperty(value = "商品SPU 编号")
    private String spuNo;

    /** 商品sku id */
    @ApiModelProperty(value = "商品sku id")
    private Long skuId;

    /** 排序序号 */
    @ApiModelProperty(value = "排序序号")
    private Integer sortNum;

    /** 上架时间 */
    @ApiModelProperty(value = "上架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelfDate; 		 // 上架时间

    /** 入驻商id */
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 小单位-上下架状态 */
    @ApiModelProperty(value = "小单位-上下架状态,0-未上架, 1-已上架")
    private Integer minShelfStatus;

    /** 中单位-上下架状态 */
    @ApiModelProperty(value = "中单位-上下架状态，0-未上架, 1-已上架")
    private Integer midShelfStatus;

    /** 大单位-上下架状态 */
    @ApiModelProperty(value = "大单位-上下架状态，0-未上架, 1-已上架")
    private Integer largeShelfStatus;

    /** 0-普通商品, 1-组合商品 */
    @ApiModelProperty(value = "0-普通商品, 1-组合商品")
    private Integer itemType = NumberPool.INT_ZERO;



}
