package com.zksr.common.core.enums.request;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 开发能力资源类型
 * @date 2024/10/18 11:02
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OpensourceMerchantType {

    SUPPLIER("supplier","入驻商"),
    SUPER_SUPPLIER_AUTH("superSupplier,supplier","统配入驻商权限"),
    SUPER_SUPPLIER("superSupplier","统配入驻商"),
    SUPPLIER_HDFK_PAY("supplierHdfkPay,supplier","货到付款清账权限"),
    ;

    private final String type;

    private final String name;

}
