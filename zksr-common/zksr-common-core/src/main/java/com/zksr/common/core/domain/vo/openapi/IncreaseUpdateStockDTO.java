package com.zksr.common.core.domain.vo.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class IncreaseUpdateStockDTO {


    /**
     * 请求ID
     */
    @ApiModelProperty(value = "幂等ID")
    private String requestId;

    @ApiModelProperty(value = "调整明细")
    private List<Detail> details;

    /**
     * 最后库存更新时间
     */
//    @ApiModelProperty(value = "最后库存更新时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date lastUpdateTime;


    /**
     * 入驻商ID
     */
    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    /**
     * 平台商ID
     */
//        @ApiModelProperty(value = "平台商ID")
//        private Long sysCode;

    @Data
    public static class Detail {

        /**
         * ERP商品编号
         */
        @ApiModelProperty(value = "ERP商品编号")
        private String erpItemNo;

        /**
         * 更新库存：调增正数，调减负数
         */
        @ApiModelProperty(value = "更新库存：调增正数，调减负数")
        private BigDecimal updateStock;

        /**
         * 来源
         */
        @ApiModelProperty(value = "来源")

        private String source;

    }
}
