package com.zksr.common.core.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 活动次数限制规则枚举 （字典：activity_times_rule）
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2025/2/22 9:51
 */
@Getter
public enum SpActivityTimesRuleEnum {
    RULE_LEVEL_NO(-1,"类型错误"),
    RULE_LEVEL_0(0,"每日一次"),
    RULE_LEVEL_1(1,"活动期间仅一次"),
//    RULE_LEVEL_2(2,"仅活动期间内首单"), 作废
    RULE_LEVEL_3(3,"新人首单"),
    RULE_LEVEL_4(4,"商品级别每日一次"),
    RULE_LEVEL_5(5,"商品级别仅一次"),
    ;

    @Getter
    private Integer type;
    @Getter
    private String name;
    SpActivityTimesRuleEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * @param timesRule 活动次数限制规则类型
     * @return 每日一次
     */
    public static boolean isRuleLevel0(Integer timesRule) {
        return Objects.equals(timesRule, RULE_LEVEL_0.getType());
    }

    /**
     * @param timesRule 活动次数限制规则类型
     * @return 每日一次 或 仅一次
     */
    public static boolean isRuleLevel0OrLevel1(Integer timesRule) {
        return Objects.equals(timesRule, RULE_LEVEL_0.getType()) || Objects.equals(timesRule, RULE_LEVEL_1.getType());
    }

    /**
     * @param timesRule 活动次数限制规则类型
     * @return 仅一次
     */
    public static boolean isRuleLevel1(Integer timesRule) {
        return Objects.equals(timesRule, RULE_LEVEL_1.getType());
    }

    /**
     * @param timesRule 活动次数限制规则类型
     * @return 商品级别每日一次
     */
    public static boolean isRuleLevel4(Integer timesRule) {
        return Objects.equals(timesRule, RULE_LEVEL_4.getType());
    }

    /**
     * @param timesRule 活动次数限制规则类型
     * @return 商品级别仅一次
     */
    public static boolean isRuleLevel5(Integer timesRule) {
        return Objects.equals(timesRule, RULE_LEVEL_5.getType());
    }

    /**
     * @param timesRule 活动次数限制规则类型
     * @return 商品级别每日一次 或 商品级别仅一次
     */
    public static boolean isRuleLevel4OrLevel5(Integer timesRule) {
        return Objects.equals(timesRule, RULE_LEVEL_4.getType()) || Objects.equals(timesRule, RULE_LEVEL_5.getType());
    }

    public boolean validateSkuLimit() {
        return this.type == 0 || this.type == 1 || this.type == 3;
    }

    public static SpActivityTimesRuleEnum formValue(Integer type) {
        for (SpActivityTimesRuleEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return SpActivityTimesRuleEnum.RULE_LEVEL_NO;
    }
}
