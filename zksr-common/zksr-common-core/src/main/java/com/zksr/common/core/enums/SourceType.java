package com.zksr.common.core.enums;

import com.zksr.common.core.utils.ToolUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 数据来源类型
 */
@Getter
@AllArgsConstructor
public enum SourceType {
    SHOPPINGMALL(1L, "商城程序"),
    PC(2L, "后台程序"),
    COLONELAPP(3L, "业务员程序"),
    SUPPLIERAPP(4L, "入驻商程序"),
    OTHERSYSTEM(5L, "第三方系统"),
            ;
    private Long type;
    private String name;

    public static boolean isPc(Long type){
        if (ToolUtil.isEmpty(type)) {
            return false;
        }
        return Objects.equals(type, PC.getType());
    }
}
