package com.zksr.common.core.domain.vo.openapi;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AfterPushDTO{

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 创建时间 字符串格式*/
    private String createTimeString;

    /** 入驻商订单id */
    private Long supplierAfterId;

    /** 平台商id */
    @Excel(name = "平台商id")
    private Long sysCode;

    /** 入驻商售后单编号 */
    @Excel(name = "入驻商售后单编号")
    private String supplierAfterNo;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    private String supplierOrderNo;

    /** 订单id */
    @Excel(name = "订单id")
    private Long orderId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 售后单id */
    @Excel(name = "售后单id")
    private Long afterId;

    /** 售后单编号 */
    @Excel(name = "售后单编号")
    private String afterNo;

    /** 配送费 */
    @Excel(name = "配送费")
    private BigDecimal transAmt;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    private BigDecimal subDiscountAmt;

    /** 退款金额;实际退款金额 */
    @Excel(name = "退款金额;实际退款金额")
    private BigDecimal subRefundAmt;

    /** 支付公司收取的支付费率;从订单 */
    @Excel(name = "支付公司收取的支付费率;从订单")
    private BigDecimal payRate;

    /** 支付平台手续费;(refund_amt*pay_rate) 四舍五入 */
    @Excel(name = "支付平台手续费;(sub_refund_amt*pay_rate) 四舍五入")
    private BigDecimal subRefundFee;

    /** 售后收货手机号 */
    @Excel(name = "售后收货手机号")
    private String returnPhone;

    /** 售后收货地址 */
    @Excel(name = "售后收货地址")
    private String returnAddr;

    /** 外部订单号 */
    @Excel(name = "外部售后订单号")
    private String sourceOrderNo;

    /** 订单类型 */
    @Excel(name = "订单类型 SheetTypeConstants")
    private String transNo;

    /** 业务员id */
    private Long colonelId;

    /** 门店id */
    private Long branchId;

    @Excel(name = "门店编号")
    private String branchNo;

    /** 退款方式（数据字典）;从订单表 0-在线支付 1-储值支付 2-货到付款 */
    @Excel(name = "退款方式", readConverterExp = "数=据字典")
    private String payWay;

    /** 对接唯一编码 */
    @Excel(name = "对接唯一编码")
    private String sendCode;

    // ===============门店信息====================
    /** 门店地址 */
    private String branchAddr;

    /** 联系电话 */
    private String contactPhone;

    /** 门店名称 */
    private String branchName;

    // ===========================================

    /** 入驻商订单对应第三方源订单编号 */
    @Excel(name = "入驻商订单对应第三方源订单编号")
    private String supplierSourceNo;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 推送状态 0未推送 1已推送 2已接收*/
    @Excel(name = "推送状态")
    private Integer pushStatus;


    //需要计算的售后订单销售参数数据======================================
    /** 售后订单总优惠金额 */
    @Excel(name = "售后订单总优惠金额 详情优惠金额之和")
    private BigDecimal afterDiscountAmt;

    @Excel(name="售后订单退货数量总和")
    private BigDecimal returnSumUnitQty;

    /** 订单详情 */
    private List<AfterDetailPushDTO> detailList;
}
