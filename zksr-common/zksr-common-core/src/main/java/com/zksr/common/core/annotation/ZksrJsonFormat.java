package com.zksr.common.core.annotation;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 默认json格式化
 * @date 2025/2/24 8:59
 */

@Target({ElementType.FIELD, ElementType.METHOD})
@JsonFormat
@Retention(RetentionPolicy.RUNTIME)
public @interface ZksrJsonFormat {

    @AliasFor(
            annotation = JsonFormat.class
    )
    String pattern() default YYYY_MM_DD_HH_MM_SS;

    @AliasFor(
            annotation = JsonFormat.class
    )
    String timezone() default TIMEZONE;
}
