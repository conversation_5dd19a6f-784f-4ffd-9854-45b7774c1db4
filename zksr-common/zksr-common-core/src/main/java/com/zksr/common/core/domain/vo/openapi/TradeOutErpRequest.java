package com.zksr.common.core.domain.vo.openapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("b2b推送订单到erp 请求参数")
public class TradeOutErpRequest {

    @ApiModelProperty(value = "第三方单据号")
    private String orderNo;

    @ApiModelProperty(value = "门店编号")
    private String branchNo;

    @ApiModelProperty(value = "配送中心编号")
    private String dcBranchNo;

    @ApiModelProperty(value = "订单金额")
    private String totalAmt;

    @ApiModelProperty(value = "订单实付金额")
    private String realAmt;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "商品明细")
    private List<TradeDetail> detailList;

    @ApiModelProperty(value = "配送方式 1，司机配送；2，车销配送")
    private String distributionType;

}
