package com.zksr.common.core.enums;

import com.zksr.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  OpenAPI 接收订单物流枚举
 */
@Getter
@AllArgsConstructor
public enum ReceiveLogisticsStatusEnum {
    // 配送状态
    DCK(1, "待出库"),
    DPS(2, "待配送"),
    PSZ(3, "配送中"),
    YPS(4, "已配送"),
    YSH(5, "已收货"),
    ;
    /**
     * 编码
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String name;

    public static ReceiveLogisticsStatusEnum getReceiveLogisticsStatus(Integer code){
        for (ReceiveLogisticsStatusEnum e : ReceiveLogisticsStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new ServiceException("Invalid key: " + code);
    }
}
