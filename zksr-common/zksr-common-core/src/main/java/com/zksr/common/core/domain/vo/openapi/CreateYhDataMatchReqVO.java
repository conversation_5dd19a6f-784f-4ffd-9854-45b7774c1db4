package com.zksr.common.core.domain.vo.openapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 要货单数据匹配查询
 * @date 2024/12/10 17:28
 */
@Data
@ApiModel(description = "要货单数据匹配查询")
public class CreateYhDataMatchReqVO {

    @ApiModelProperty("要货单批次号(系统唯一)")
    @NotEmpty(message = "要货单批次号(系统唯一)")
    private String yhBatchNo;
}
