package com.zksr.common.core.constant;
/**
 * <AUTHOR>
 * @version 1.0
 * @description: 状态通用常量
 * @date 2024/3/5 16:02
 */
public class StatusConstants {
    /**
     * 是
     */
    public static String STATUS_1 = "Y";

    /**
     * 否
     */
    public static String STATUS_2 = "N";

    /**
     * 是
     */
    public static Integer FLAG_TRUE = 1;

    /**
     * 否
     */
    public static Integer FLAG_FALSE = 0;

    /**
     *  停用
     */
    public static Integer STATE_DISABLE = 0;

    /**
     * 启用
     */
    public static Integer STATE_ENABLE = 1;

    /**
     *  未审核
     */
    public static Integer AUDIT_STATE_0 = 0;

    /**
     * 已审核
     */
    public static Integer AUDIT_STATE_1 = 1;

    /**
     * 已取消
     */
    public static Integer AUDIT_STATE_2 = 2;

    /**
     * 未结算
     */
    public static Integer SETTLE_STATE_0 = 0;

    /**
     * 已结算
     */
    public static Integer SETTLE_STATE_1 = 1;

    /**
     * 结算中
     */
    public static Integer SETTLE_STATE_2 = 2;

    /**
     * 待处理 结算
     */
    public static Integer SETTLE_STATE_3 = 3;

    /**
     * 价格计算保留小数位 （2）
     */
    public static Integer PRICE_RESERVE_2 = 2;

    /**
     * 价格计算保留小数位 （6）
     */
    public static Integer PRICE_RESERVE_6 = 6;

    /**
     * 计算保留小数位 （3）
     */
    public static Integer PRICE_RESERVE_3 = 3;

    /**
     * 操作类型 - 新增
     */
    public static Long OPER_TYPE_ADD = 1L;

    /**
     * 操作类型 - 修改
     */
    public static Long OPER_TYPE_UPDATE = 2L;

    /**
     * 时间配置项 - 小时
     */
    public static Integer TIME_HOUR = 0;

    /**
     * 时间配置项 - 分钟
     */
    public static Integer TIME_MINUTE = 1;

    /**
     * 全国商品
     */
    public static Integer ITEM_TYPE_0 = 0;

    /**
     * 本地商品
     */
    public static Integer ITEM_TYPE_1 = 1;

    /**
     * 成功
     */
    public static Integer STATUS_SUCCESS = 0;

    /**
     * 失败
     */
    public static Integer STATUS_FAIL = 1;

    /**
     * 进行中
     */
    public static Integer STATUS_GOING = 2;

    /**
     *  售后单 - 未取消
     */
    public static Long ISCANCEL_0 = 0L;

    /**
     *  售后单 - 已取消
     */
    public static Long ISCANCEL_1 = 1L;

    /**
     * 售后单（售后阶段） - 发货前退款
     */
    public static Long AFTER_PHASE_1 = 1L;

    /**
     * 售后单（售后阶段） - 发货后退款
     */
    public static Long AFTER_PHASE_2 = 2L;

    /**
     * 售后单（退款类型） - 退全款
     */
    public static Long REFUND_TYPE_1 = 1L;
    /**
     * 售后单（退款类型） - 退部分款
     */
    public static Long REFUND_TYPE_2 = 2L;
    /**
     * 售后单（退款类型） - 不退款
     */
    public static Long REFUND_TYPE_3 = 3L;

    /** 是否为自动审核 -- 否*/
    public static final Integer REGISTER_APPROVE_FLAG_0 = 0;

    /** 是否为自动审核 -- 是*/
    public static final Integer REGISTER_APPROVE_FLAG_1 = 1;

    /**
     * 冻结流水操作
     */
    public static final String FREEZE_FLOW = "1";

    /**
     * 释放冻结流水操作
     */
    public static final String FREEZE_FLOW_RELEASE = "2";

    /**
     * 业务员App订单类型标识--销售
     */
    public static final Integer COLONEL_APP_ORDER_TYPE =1;




    /** 用户类型 0 新用户  */
    public static final int COLONEL_USER_TYPE_0 = 0;

    /** 用户类型 1老用户*/
    public static final int COLONEL_USER_TYPE_1 = 1;

    public static final Integer DEL_FLAG_0 = 0;

    /**
     *  操作指令状态 - 作废
     */
    public static final Integer COMMAND_STATUS_0 = 0;
    /**
     *  操作指令状态 - 进行中
     */
    public static final Integer COMMAND_STATUS_1 = 1;
    /**
     *  操作指令状态 - 完成
     */
    public static final Integer COMMAND_STATUS_2 = 2;

    /**
     *  操作指令 - 订单下单增加锚点指令
     */
    public static final int COMMAND_OPERATE_1 = 1;
    /**
     *  操作指令状态 - 业务员增加普通指令
     */
    public static final int COMMAND_OPERATE_2 = 2;
    /**
     *  操作指令状态 - 更新普通执行执行结果（加入购物车）
     */
    public static final int COMMAND_OPERATE_3 = 3;

    /**
     *  操作指令状态 - 更新普通执行执行结果（下单更新）
     */
    public static final int COMMAND_OPERATE_4 = 4;
}
