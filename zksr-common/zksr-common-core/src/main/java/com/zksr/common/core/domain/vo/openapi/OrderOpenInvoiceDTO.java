package com.zksr.common.core.domain.vo.openapi;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 入驻商销售订单  发票信息
 */
@Data
@Accessors(chain = true)
public class OrderOpenInvoiceDTO {
    /** ID主键 */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long id;

    /** 用户发票id */
    @Excel(name = "用户发票id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberInvoiceId;

    /** 用户id */
    @Excel(name = "用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /** 入驻商订单ID */
    @Excel(name = "入驻商订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierOrderId;

    /** 入驻商订单号 */
    @Excel(name = "入驻商订单号")
    private String supplierOrderNo;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 发票类型,10电子普通发票20专用发票 */
    @Excel(name = "发票类型,10电子普通发票20专用发票")
    private Long invoiceType;

    /** 发票抬头类型,10个人20单位 */
    @Excel(name = "发票抬头类型,10个人20单位")
    private Long titleType;

    /** 发票抬头 */
    @Excel(name = "发票抬头")
    private String invoiceTitle;

    /** 纳税人识别码 */
    @Excel(name = "纳税人识别码")
    private String taxpayerCode;

    /** 单位地址 */
    @Excel(name = "单位地址")
    private String companyAddress;

    /** 单位电话 */
    @Excel(name = "单位电话")
    private String companyPhone;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 开户银行 */
    @Excel(name = "开户银行")
    private String bankName;

    /** 银行账户 */
    @Excel(name = "银行账户")
    private String bankAccount;

    /** 发票PDF文件下载/访问URL  */
    @Excel(name = "发票PDF文件下载/访问URL ")
    private String pdfUrl;

    /** 是否删除：0-否，1-是 */
    @TableLogic
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;
}
