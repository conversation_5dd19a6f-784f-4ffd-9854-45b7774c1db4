package com.zksr.common.core.utils.cert;

import org.bouncycastle.asn1.ASN1Encodable;
import org.bouncycastle.asn1.ASN1OctetString;
import org.bouncycastle.asn1.ASN1Primitive;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.pkcs.PKCSObjectIdentifiers;
import org.bouncycastle.crypto.DataLengthException;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.engines.SM4Engine;
import org.bouncycastle.crypto.modes.CBCBlockCipher;
import org.bouncycastle.crypto.paddings.PKCS7Padding;
import org.bouncycastle.crypto.paddings.PaddedBufferedBlockCipher;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.crypto.params.ParametersWithIV;
import org.bouncycastle.jcajce.util.BCJcaJceHelper;
import org.bouncycastle.jcajce.util.JcaJceHelper;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.Hex;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.AlgorithmParameters;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.ECGenParameterSpec;
import java.security.spec.ECParameterSpec;
import java.security.spec.ECPrivateKeySpec;


public class PKCS12_SM2_N implements ASN1Encodable, PKCSObjectIdentifiers{

    private final static String CURVE_NAME = "sm2p256v1";

    private ASN1Sequence privateInfo = null;

    public PKCS12_SM2_N() {}

    public PKCS12_SM2_N(byte[] data) throws Exception {
        load(data);
    }

    public void load(byte[] data) throws Exception {
        ASN1Sequence seq = getDERSequenceFrom(data);
        parseSM2(seq);
    }

    public void parseSM2(ASN1Sequence seq) throws Exception {
        parseSM2Certs((ASN1Sequence) seq.getObjectAt(1), (ASN1Sequence) seq.getObjectAt(2));
    }

    private void parseSM2Certs(ASN1Sequence privateInfo, ASN1Sequence publicInfo) {
        this.privateInfo = privateInfo;
    }

    public static ASN1Sequence getDERSequenceFrom(byte[] encoding) throws Exception {
        if (isDERSequence(encoding)) {
            return ASN1Sequence.getInstance(encoding);
        }
        if (isBERSequence(encoding)) {
            return ASN1Sequence.getInstance(encoding);
        }
        byte[] data;
        try {
            data = Base64.decode(encoding);
        } catch (Exception e) {
            throw new Exception("encoding required base64 encoding", e);
        }
        return ASN1Sequence.getInstance(data);
    }

    public static boolean isBERSequence(byte[] encoding) throws Exception {
        if (encoding == null) {
            throw new Exception("encoding should not be null");
        }
        if (encoding.length < 4) {
            throw new Exception("encoding length less than 4");
        }
        if (encoding[0] != 48) {
            return false;
        }
        int offset = 1;
        int length = encoding[(offset++)] & 0xFF;
        if (length != 128) {
            return false;
        }
        return (encoding[(encoding.length - 1)] == 0) && (encoding[(encoding.length - 2)] == 0);
    }

    public static boolean isDERSequence(byte[] encoding) throws Exception {
        if (encoding == null) {
            throw new Exception("encoding should not be null");
        }
        if (encoding.length < 2) {
            throw new Exception("encoding length less than 4");
        }
        if (encoding[0] != 48) {
            return false;
        }
        int offset = 1;
        int length = encoding[(offset++)] & 0xFF;
        if (length == 128) {
            return false;
        }
        if (length > 127) {
            int dLength = length & 0x7F;
            if (dLength > 4) {
                return false;
            }
            length = 0;
            int next = 0;
            for (int i = 0; i < dLength; i++) {
                next = encoding[(offset++)] & 0xFF;
                length = (length << 8) + next;
            }
            if (length < 0) {
                return false;
            }
        }
        return encoding.length == offset + length;
    }


    public PrivateKey getPrivateKey(String password) throws Exception {
        String hex = decrypt(password);
        BigInteger d = new BigInteger(1, Hex.decode(hex));
        JcaJceHelper jcaJceHelper = new BCJcaJceHelper();
        AlgorithmParameters parameters = jcaJceHelper.createAlgorithmParameters("EC");
        parameters.init(new ECGenParameterSpec(CURVE_NAME));
        ECParameterSpec parameterSpec = parameters.getParameterSpec(ECParameterSpec.class);
        ECPrivateKeySpec keySpec = new ECPrivateKeySpec(d, parameterSpec);
        KeyFactory factory = jcaJceHelper.createKeyFactory("EC");
        return factory.generatePrivate(keySpec);
    }


    public String decrypt(String password) throws Exception {
        if (password == null) {
            throw new Exception("SM2File password should not be null");
        }
        if (this.privateInfo == null) {
            throw new Exception("SM2File invalid : privateInfo=null");
        }
        ASN1OctetString priOctString = (ASN1OctetString) this.privateInfo.getObjectAt(2);
        byte[] encryptedData;
        try {
            encryptedData = priOctString.getOctets();
        } catch (Exception e) {
            throw new Exception("SM2File decoding failure", e);
        }
        byte[] dBytes = SM4DecryptDBytes(password, encryptedData);

        return Hex.toHexString(dBytes).substring(0, 64);
    }

    private byte[] SM4DecryptDBytes(String password, byte[] encryptedData) throws Exception {
        if ((password == null) || (password.length() == 0)) {
            throw new Exception("SM2File password should not be null");
        }
        byte[] passwordBytes;
        try {
            passwordBytes = password.getBytes("UTF8");
        } catch (UnsupportedEncodingException e) {
            throw new Exception("SM2File password decoding failure", e);
        }
        if ((encryptedData == null) || (encryptedData.length == 0)) {
            throw new Exception("SM2File encryptedData should not be null");
        }
        if ((encryptedData.length < 32) || (encryptedData.length > 64)) {
            throw new Exception("SM2File EncryptedData required length in [32-64] ");
        }
        byte[] encoding = null;
        if ((encryptedData.length == 32) || (encryptedData.length == 48)) {
            encoding = encryptedData;
        } else {
            try {
                encoding = Base64.decode(encryptedData);
            } catch (Exception e) {
                throw new Exception("SM2File EncryptedData required base64 ");
            }
        }
        byte[] iv;
        byte[] sm4;
        try {
            byte[] hash = KDF(passwordBytes);
            iv = new byte[16];
            System.arraycopy(hash, 0, iv, 0, 16);
            sm4 = new byte[16];
            System.arraycopy(hash, 16, sm4, 0, 16);
        } catch (Exception e) {
            throw new Exception("SM2File KDF failure", e);
        }
        try {
            PaddedBufferedBlockCipher cipher = new PaddedBufferedBlockCipher(new CBCBlockCipher(new SM4Engine()),
                    new PKCS7Padding());
            ParametersWithIV params = new ParametersWithIV(new KeyParameter(sm4), iv);
            cipher.init(false, params);

            int outLength = cipher.getOutputSize(encoding.length);
            byte[] out = new byte[outLength];
            int dataLength = cipher.processBytes(encoding, 0, encoding.length, out, 0);
            int lastLength = cipher.doFinal(out, dataLength);
            int realLength = dataLength + lastLength;
            byte[] dBytes = null;
            if (realLength < outLength) {
                dBytes = new byte[realLength];
                System.arraycopy(out, 0, dBytes, 0, realLength);
            }
            return out;
        } catch (DataLengthException e) {
            throw new Exception("SM2File SM2PrivateKey decrypt failure with IllegalDataLength", e);
        } catch (IllegalArgumentException e) {
            throw new Exception("SM2File SM2PrivateKey decrypt failure with IllegalArgument", e);
        } catch (IllegalStateException e) {
            throw new Exception("SM2File SM2PrivateKey decrypt failure with IllegalState", e);
        } catch (InvalidCipherTextException e) {
            throw new Exception("SM2File SM2PrivateKey decrypt failure with InvalidCipherText", e);
        } catch (Exception e) {
            throw new Exception("SM2File SM2PrivateKey decrypt failure", e);
        }
    }

    private static byte[] KDF(byte[] z) {
        byte[] ct = { 0, 0, 0, 1 };
        SM3Digest sm3 = new SM3Digest();
        sm3.update(z, 0, z.length);
        sm3.update(ct, 0, ct.length);
        byte[] hash = new byte[32];
        sm3.doFinal(hash, 0);
        return hash;
    }

    @Override
    public ASN1Primitive toASN1Primitive() {
        return null;
    }

}