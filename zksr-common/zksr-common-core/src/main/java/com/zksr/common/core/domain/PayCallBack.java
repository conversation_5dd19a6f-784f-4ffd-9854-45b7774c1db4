package com.zksr.common.core.domain;

import com.zksr.common.core.annotation.Excel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PayCallBack {
    
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    
    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    private String supplierOrderNo;
    
    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;
    
    @ApiModelProperty(value = "订单类型", hidden = true)
    private String orderType;
    
    @ApiModelProperty(value = "appid", hidden = true)
    private String appid;
    
    @ApiModelProperty(value = "platform", hidden = true)
    private String platform;
    
    @ApiModelProperty(value = "分销模式")
    private String distributionMode;
    
    
    
}
