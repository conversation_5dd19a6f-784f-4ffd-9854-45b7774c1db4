package com.zksr.common.core.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * 商户类型
 */
@Getter
public enum MerchantTypeEnum {
    SOFTWARE("software", "ST","软件商"),
    PARTNER("partner", "PT","平台商"),
    PARTNER_FREE("partner-fee","PF", "平台商手续费补贴账户"),
    PARTNER_COLONEL("partner-colonel", "PC", "平台商统一业务员分润提成账户"),
    DC("dc", "DC","运营商"),
    SUPPLIER("supplier", "SP","入驻商"),
    COLONEL("colonel", "CL","业务员"),
    BRANCH("branch", "BH","门店储值"),
    MEMBER("member", "BR","用户"),
    BRANCH_DEBT("branch-debt", "BT","门店欠款账户"),
    ;
    private String type;
    private String withdrawPrefix;
    private String name;

    MerchantTypeEnum(String type, String withdrawPrefix, String name) {
        this.type = type;
        this.withdrawPrefix = withdrawPrefix;
        this.name = name;
    }

    /**
     * @param merchantType  商户类型
     * @return  是否是大区手续费账户
     */
    public static boolean isPartnerFeeAccount(String merchantType) {
        return Objects.equals(merchantType, PARTNER_FREE.getType());
    }

    /**
     * @param merchantType  商户类型
     * @return  是否是入驻商
     */
    public static boolean isSupplier(String merchantType) {
        return Objects.equals(merchantType, SUPPLIER.getType());
    }

    /**
     * @param merchantType  商户类型
     * @return  是否是运营商
     */
    public static boolean isDc(String merchantType) {
        return Objects.equals(merchantType, DC.getType());
    }

    public static boolean isBranchDebt(String merchantType) {
        return Objects.equals(merchantType, BRANCH_DEBT.getType());
    }

    /**
     * 需要关注账户缓存商户
     * @param merchantType
     * @return
     */
    public static boolean isCacheAccount(String merchantType) {
        return MerchantTypeEnum.isSupplier(merchantType) || MerchantTypeEnum.isBranchDebt(merchantType);
    }

    /**
     * 获取商户类型名称
     * @param merchantType  商户类型
     * @return
     */
    public static String getName(String merchantType) {
        for (MerchantTypeEnum value : values()) {
            if (value.getType().equals(merchantType)) {
                return value.getName();
            }
        }
        return null;
    }

    public static boolean isColonel(String merchantType) {
        return Objects.equals(merchantType, COLONEL.getType());
    }

    @JsonValue
    public String getType() {
        return type;
    }

    @JsonCreator
    public static MerchantTypeEnum fromValue(String value) {
        for (MerchantTypeEnum merchantTypeEnum : MerchantTypeEnum.values()) {
            if (merchantTypeEnum.getType().equals(value)) {
                return merchantTypeEnum;
            }
        }
        return null;
    }
}
