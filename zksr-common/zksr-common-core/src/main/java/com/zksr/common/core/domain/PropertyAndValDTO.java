package com.zksr.common.core.domain;

import com.alibaba.fastjson2.JSONArray;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.StringJoiner;

/**
*
 * 规格名称 - 规格值对应 实体
* <AUTHOR>
* @date 2024/3/5 10:59
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class PropertyAndValDTO {

    /** 规格名称id */
    private Long propertyId;

    /** 规格名称 */
    private String propertyName;

    /** 规格值id */
    private Long propertyValId;

    /** 规格值名称 */
    private String valName;

    public PropertyAndValDTO(String valName) {
        this.valName = valName;
    }

    /**
     * 商品规格值转换
     * @return
     */
    public static String getProperties(String properties){
        if (StringUtils.isEmpty(properties) || properties.equals("{}") || properties.equals(StringPool.NULL)) {
            return StringPool.EMPTY;
        }
        StringJoiner val = new StringJoiner("-");
        //获取规格属性信息
        try {
            List<PropertyAndValDTO> propertyList = JSONArray.parseArray(properties, PropertyAndValDTO.class);
            if (ToolUtil.isNotEmpty(propertyList)) {
                propertyList.forEach(property -> {
                    if(ToolUtil.isNotEmpty(property.getValName())) {
                        val.add(property.getValName());
                    }
                });
            }
            return val.toString();
        } catch (Exception e) {
            log.error(" PropertyAndValDTO.getProperties 失败，", e);
            return "";
        }
    }

    /**
     * 商品规格值转换
     * @return
     */
    public static String getPropertiesSpuName(String properties,String spuName){
        if (StringUtils.isEmpty(properties) || properties.equals("{}") || properties.equals(StringPool.NULL)) {
            return spuName;
        }
        StringJoiner val = new StringJoiner("-");
        val.add(spuName);
        //获取规格属性信息
        try {
            List<PropertyAndValDTO> propertyList = JSONArray.parseArray(properties, PropertyAndValDTO.class);
            if (ToolUtil.isNotEmpty(propertyList)) {
                propertyList.forEach(property -> {
                    if(ToolUtil.isNotEmpty(property.getValName())) {
                        val.add(property.getValName());
                    }
                });
            }
            return val.toString();
        } catch (Exception e) {
            log.error(" PropertyAndValDTO.getPropertiesSpuName 失败，", e);
            return "";
        }
    }
}
