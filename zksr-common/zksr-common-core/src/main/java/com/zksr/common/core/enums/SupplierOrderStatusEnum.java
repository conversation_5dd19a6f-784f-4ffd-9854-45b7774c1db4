package com.zksr.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 入驻商订单状态
 */
@Getter
@AllArgsConstructor
public enum SupplierOrderStatusEnum {

    UNPAID(0, "待付款"),
//    PAID(1, "已支付"),

    WAIT_DELIVER(10, "待发货"),
    WAIT_RECEIVED(20, "待收货"),
    RECEIVED(21, "已收货"),
    AFTER(30, "售后中"),
//    COMPLETED(40, "已完成"),

    CANCELED(50, "已取消");


    // 编码
    int code;

    // 描述
    String msg;


}
