package com.zksr.common.core.domain.vo.openapi.receive;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/5/30 15:51
 * @注释
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("接收销售退货回传明细实体")
public class AfterSalesReturnDetailVO {

    /** 入驻商订单行号 */
    @ApiModelProperty(value = "入驻商退货订单行号")
    @NotNull(message = "入驻商退货订单行号不能为空")
    private Long lineNum;

    /** ERP商品编号 */
    @Excel(name = "ERP商品编号")
    @ApiModelProperty(value = "ERP商品编号")
    @NotNull(message = "ERP商品编号不能为空")
    private String erpItemNo;

    /** 退货实收数量 */
    @Excel(name = "退货实收数量")
    @ApiModelProperty(value = "退货实收数量")
    @NotNull(message = "退货实收数量不能为空")
    private BigDecimal returnQty;
}
