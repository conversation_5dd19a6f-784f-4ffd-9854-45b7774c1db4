package com.zksr.common.core.domain.erp.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ErpBranchDTO {

    /** 操作类型 （新增、修改、删除） */
    private Integer opType;

    /** 店铺编码 */
    private String branchNo;

    /** 店铺名称 */
    private String branchName;

    /** 经销商编号 */
    private String dcBranchNo;

    /** 联系人 */
    private String branchMan;

    /** 手机号 */
    private String branchTel;

    /** 地址 */
    private String address;

    /** 经度 */
    private String lng;

    /** 纬度 */
    private String lat;

    /** 业务员名称 */
    private String employeeName;

    /** 业务员手机号 */
    private String employeePhone;
}
