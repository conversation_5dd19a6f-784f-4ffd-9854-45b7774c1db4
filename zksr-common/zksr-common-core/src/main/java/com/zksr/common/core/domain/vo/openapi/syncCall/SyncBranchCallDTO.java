package com.zksr.common.core.domain.vo.openapi.syncCall;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "门店信息返回实体类")
public class SyncBranchCallDTO {


    @ApiModelProperty(value = "门店编号")
    private String branchNo;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店联系人")
    private String contactName;

    @ApiModelProperty(value = "门店联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    private Long userId;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "业务员手机号")
    private String colonelPhone;
}
