package com.zksr.common.core.domain.erp;

import com.zksr.common.core.enums.request.OperationType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ApiDataModel<T> {
    private OperationType type;
    private T data;

    public ApiDataModel() {
    }

    public ApiDataModel(OperationType type, T data) {
        this.type = type;
        this.data = data;
    }
}
