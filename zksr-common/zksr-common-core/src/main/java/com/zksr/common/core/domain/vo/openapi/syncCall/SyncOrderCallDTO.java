package com.zksr.common.core.domain.vo.openapi.syncCall;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SyncOrderCallDTO", description = "订单返回数据传输对象")
public class SyncOrderCallDTO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 入驻商订单id */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "入驻商订单id")
    private Long supplierOrderId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    /** 订单id */
    @Excel(name = "订单id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 配送费 */
    @Excel(name = "配送费")
    @ApiModelProperty(value = "配送费")
    private BigDecimal transAmt;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 优惠金额 **/
    @Excel(name = "优惠金额")
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal subDiscountAmt;

    /** 支付金额 **/
    @Excel(name = "支付金额")
    @ApiModelProperty(value = "支付金额")
    private BigDecimal subPayAmt;

    /** 订单金额 未减去优惠的订单金额 **/
    @Excel(name = "订单金额")
    @ApiModelProperty(value = "订单金额")
    private BigDecimal subOrderAmt;

    /** 订单商品总数量 **/
    @Excel(name = "订单商品总数量")
    @ApiModelProperty(value = "订单商品总数量")
    private BigDecimal subOrderNum;

    /** 外部订单号 */
    @Excel(name = "外部订单号")
    @ApiModelProperty(value = "外部订单号")
    private String sourceOrderNo;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    /** 支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款 */
    @Excel(name = "支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款")
    @ApiModelProperty(value = "支付方式", example = "0-在线支付, 1-储值支付, 2-货到付款")
    private String payWay;

    /** 订单类型 */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    /** 业务员id */
    @Excel(name = "业务员id")
    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    /** 业务员id */
    @Excel(name = "业务员名称")
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    /** 业务员手机号 */
    @Excel(name = "业务员手机号")
    @ApiModelProperty(value = "业务员手机号")
    private String colonelPhone;

    /** 下单用户是否本身是业务员(0-否 1-是) */
    @Excel(name = "下单方式 0自主下单（商城下单） 1业务员下单")
    @ApiModelProperty(value = "下单方式", example = "0-自主下单, 1-业务员下单")
    private Integer colonelFlag;

    /** 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消 */
    @Excel(name = "支付状态", readConverterExp = "数=据字典sys_pay_state")
    @ApiModelProperty(value = "支付状态", example = "0-未支付, 1-已支付, 2-未付款取消")
    private Integer payState;

    /** 推送状态 0未推送 1已推送 2已接收 */
    @Excel(name = "推送状态")
    @ApiModelProperty(value = "推送状态", example = "0-未推送, 1-已推送, 2-已接收")
    private Integer pushStatus;

    /** 订单详情 */
    @ApiModelProperty(value = "订单详情")
    private List<SyncOrderDetailCallDTO> detailList;
}
