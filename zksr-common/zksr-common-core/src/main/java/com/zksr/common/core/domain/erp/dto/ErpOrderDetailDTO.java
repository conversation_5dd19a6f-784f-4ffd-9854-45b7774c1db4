package com.zksr.common.core.domain.erp.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.BigDecimalSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ErpOrderDetailDTO {

    /** 入驻商订单行号 */
    private Long lineNo;

    /** 商品编号 */
    private String itemNo;

    /** 产品单位 */
    private String itemUnit;

    /** 产品单价(最小单位的销售价) */
    private BigDecimal itemPrice;

    /** 商品包装(0 小包装 1 中包装  2 大包装) */
    private String packageType;

    /** 商品条码 */
    private String itemBarcode;

    /** 商品总数量 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal detailQty;

    /** 商品实际数量 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realQty;

    /** 最小单位数量 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal minDetailQty;

    /** 商品总金额 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal detailPrice;

    /** 下单类型 0 正常品 1 赠品 2 陈列兑付  */
    private String itemType;
}
