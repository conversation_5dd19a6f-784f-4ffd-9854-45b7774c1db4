package com.zksr.common.core.enums;

/**
 * @Author: chenyj8
 * @Desciption: 美的付退款通知状态
 */
public enum MideaPayRefundStatusEnum {
    NOT_EXIST("NOT_EXIST", "订单不存在"),
    WAIT_PAY("WAIT_PAY", "未退款"),
    PAYING("PAYING", "退款中"),
    SUCCESS("SUCCESS", "退款成功"),
    FAIL("FAIL", "退款失败"),
    ;

    private String code;

    private String desc;


    MideaPayRefundStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getValue() {
        return desc;
    }

}
