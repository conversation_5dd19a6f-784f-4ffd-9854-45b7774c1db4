package com.zksr.common.core.enums;

import lombok.Getter;
import org.bouncycastle.pqc.crypto.newhope.NHSecretKeyProcessor;

@Getter
public enum ComplainTypeEnum {

    PRODUCT("1","商品"),
    LOGISTICS("2","物流"),
    COLONEL("3","业务员"),
    DRIVER("4","司机");

    /**
     * 编码
     */
    private final String type;
    /**
     * 名字
     */
    private final String name;

    ComplainTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String matchingName(String type){
        for (ComplainTypeEnum value : values()) {
            if (value.getType().equals(type)){
                return value.getName();
            }
        }
        return "";
    }
}
