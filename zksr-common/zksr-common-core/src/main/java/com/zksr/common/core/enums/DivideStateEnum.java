package com.zksr.common.core.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 分账状态
 */
@Getter
public enum DivideStateEnum {
    UNDEFINED(0, "未分账"),
    FINISH(1, "已分账"),
    PROCESSING(2, "分账中"),
    FAIL(3, "失败"),
    NONE(4, "无需处理"),
    ;

    @JsonValue
    private Integer state;
    private String name;

    DivideStateEnum(Integer state, String name) {
        this.state = state;
        this.name = name;
    }

    public static DivideStateEnum parseState(Integer divideState) {
        for (DivideStateEnum value : values()) {
            if (value.getState().equals(divideState)) {
                return value;
            }
        }
        return null;
    }
}
