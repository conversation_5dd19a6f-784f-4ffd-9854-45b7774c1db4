package com.zksr.common.core.constant;

import com.zksr.common.core.pool.NumberPool;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单类型常量
 * @date 2024/3/7 15:53
 */
public class OrderTypeConstants {
    // 有效值列表
    public static final Set<Integer> VALID_ORDER = new HashSet<>();
    static {
        VALID_ORDER.add(NumberPool.INT_ZERO);
        VALID_ORDER.add(NumberPool.INT_ONE);
        VALID_ORDER.add(NumberPool.INT_TWO);
        VALID_ORDER.add(NumberPool.INT_THREE);
    }
    /** 商城订单 */
    public static final Integer MALL = NumberPool.INT_ZERO;
    /** 入驻商充值 */
    public static final Integer SUPPLIER_CHARGE = NumberPool.INT_ONE;
    /** 门店充值 */
    public static final Integer BRANCH_CHARGE = NumberPool.INT_TWO;
    /** 货到付款 */
    public static final Integer PAY_DELIVERY = NumberPool.INT_THREE;

    public static boolean supplierPaySource(Integer orderType) {
        if (Objects.isNull(orderType)) {
            return false;
        }
        return orderType.equals(MALL) || orderType.equals(PAY_DELIVERY);
    }

    public static boolean useShopAppId(Integer orderType) {
        return OrderTypeConstants.MALL.equals(orderType) || OrderTypeConstants.PAY_DELIVERY.equals(orderType) || OrderTypeConstants.BRANCH_CHARGE.equals(orderType);
    }
}
