package com.zksr.common.core.utils.signature;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import java.security.*;

/**
 * @Description: RSA密钥对生成工具
 * 用于生成美的API签名所需的RSA密钥对
 * @Date: 2025/07/17
 */
@Slf4j
public class RSAKeyPairGenerator {

    private static final String KEY_ALGORITHM = "RSA";
    private static final int KEY_SIZE = 2048; // 推荐使用2048位密钥

    /**
     * 密钥对信息
     */
    public static class KeyPairInfo {
        private final String privateKey;
        private final String publicKey;
        private final String algorithm;
        private final int keySize;

        public KeyPairInfo(String privateKey, String publicKey, String algorithm, int keySize) {
            this.privateKey = privateKey;
            this.publicKey = publicKey;
            this.algorithm = algorithm;
            this.keySize = keySize;
        }

        public String getPrivateKey() {
            return privateKey;
        }

        public String getPublicKey() {
            return publicKey;
        }

        public String getAlgorithm() {
            return algorithm;
        }

        public int getKeySize() {
            return keySize;
        }

        @Override
        public String toString() {
            return "KeyPairInfo{" +
                    "algorithm='" + algorithm + '\'' +
                    ", keySize=" + keySize +
                    ", privateKey='" + privateKey + '\'' +
                    ", publicKey='" + publicKey + '\'' +
                    '}';
        }
    }

    /**
     * 生成RSA密钥对
     *
     * @return 密钥对信息
     */
    public static KeyPairInfo generateKeyPair() {
        return generateKeyPair(KEY_SIZE);
    }

    /**
     * 生成指定长度的RSA密钥对
     *
     * @param keySize 密钥长度（推荐2048）
     * @return 密钥对信息
     */
    public static KeyPairInfo generateKeyPair(int keySize) {
        try {
            // 创建密钥对生成器
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(KEY_ALGORITHM);
            keyPairGenerator.initialize(keySize);

            // 生成密钥对
            KeyPair keyPair = keyPairGenerator.generateKeyPair();

            // 获取私钥和公钥
            PrivateKey privateKey = keyPair.getPrivate();
            PublicKey publicKey = keyPair.getPublic();

            // 转换为Base64编码的字符串
            String privateKeyBase64 = Base64.encodeBase64String(privateKey.getEncoded());
            String publicKeyBase64 = Base64.encodeBase64String(publicKey.getEncoded());

            log.info("成功生成{}位RSA密钥对", keySize);

            return new KeyPairInfo(privateKeyBase64, publicKeyBase64, KEY_ALGORITHM, keySize);

        } catch (NoSuchAlgorithmException e) {
            log.error("不支持的算法: {}", KEY_ALGORITHM, e);
            throw new RuntimeException("生成密钥对失败：不支持的算法", e);
        } catch (Exception e) {
            log.error("生成密钥对失败", e);
            throw new RuntimeException("生成密钥对失败", e);
        }
    }

    /**
     * 验证密钥对是否匹配
     *
     * @param privateKeyBase64 Base64编码的私钥
     * @param publicKeyBase64  Base64编码的公钥
     * @return 是否匹配
     */
    public static boolean validateKeyPair(String privateKeyBase64, String publicKeyBase64) {
        try {
            // 测试数据
            String testData = "Hello, Midea API Signature Test!";

            // 使用私钥签名
            String signature = MideaPayApiSignatureUtils.sign(testData, privateKeyBase64);
            if (signature == null) {
                log.error("私钥签名失败");
                return false;
            }

            // 使用公钥验签
            boolean isValid = MideaPayApiSignatureUtils.verify(testData, publicKeyBase64, signature);

            log.info("密钥对验证结果: {}", isValid ? "匹配" : "不匹配");
            return isValid;

        } catch (Exception e) {
            log.error("验证密钥对失败", e);
            return false;
        }
    }

    /**
     * 格式化输出密钥对
     *
     * @param keyPairInfo 密钥对信息
     */
    public static void printKeyPair(KeyPairInfo keyPairInfo) {
        System.out.println("RSA密钥对生成完成");
        System.out.println("算法: " + keyPairInfo.getAlgorithm());
        System.out.println("密钥长度: " + keyPairInfo.getKeySize() + " bits");
        System.out.println();

        System.out.println("私钥 (Private Key):");
        System.out.println(formatKey(keyPairInfo.getPrivateKey()));
        System.out.println();

        System.out.println("公钥 (Public Key):");
        System.out.println(formatKey(keyPairInfo.getPublicKey()));
        System.out.println();

        System.out.println("配置示例:");
        System.out.println("# application.yml 配置");
        System.out.println("zksr:");
        System.out.println("  midea:");
        System.out.println("    api:");
        System.out.println("      private-key: |");
        System.out.println("        " + keyPairInfo.getPrivateKey());
        System.out.println("      public-key: |");
        System.out.println("        " + keyPairInfo.getPublicKey());
        System.out.println();

        System.out.println("Java代码示例:");
        System.out.println("// 私钥（用于签名）");
        System.out.println("String privateKey = \"" + keyPairInfo.getPrivateKey() + "\";");
        System.out.println();
        System.out.println("// 公钥（用于验签）");
        System.out.println("String publicKey = \"" + keyPairInfo.getPublicKey() + "\";");
        System.out.println();
    }

    /**
     * 格式化密钥字符串（每64个字符换行）
     */
    private static String formatKey(String key) {
        if (key == null || key.length() <= 64) {
            return key;
        }

        StringBuilder formatted = new StringBuilder();
        for (int i = 0; i < key.length(); i += 64) {
            if (i > 0) {
                formatted.append("\n");
            }
            formatted.append(key.substring(i, Math.min(i + 64, key.length())));
        }
        return formatted.toString();
    }

    /**
     * 主方法 - 用于生成密钥对
     */
    public static void main(String[] args) {
        try {
            System.out.println("正在生成RSA密钥对...");

            // 生成密钥对
            KeyPairInfo keyPairInfo = generateKeyPair();

            // 验证密钥对
            boolean isValid = validateKeyPair(keyPairInfo.getPrivateKey(), keyPairInfo.getPublicKey());
            if (!isValid) {
                System.err.println("❌ 密钥对验证失败！");
                return;
            }

            // 输出密钥对
            printKeyPair(keyPairInfo);

            System.out.println("✅ 密钥对生成并验证成功！");
            System.out.println("请将私钥配置到您的应用中，公钥提供给美的平台进行验签。");

        } catch (Exception e) {
            System.err.println("❌ 生成密钥对失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
