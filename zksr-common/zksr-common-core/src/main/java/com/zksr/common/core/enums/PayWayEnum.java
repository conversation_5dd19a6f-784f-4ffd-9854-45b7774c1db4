package com.zksr.common.core.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付方式
 * @date 2024/3/8 11:35
 */
@Getter
public enum PayWayEnum {
    ONLINE("0", "在线支付"),
    WALLET("1", "储值支付"),
    MOCK("2", "模拟支付"),
    OFFLINE("6", "线下支付"),
    ;
    private String payWay;
    private String name;

    PayWayEnum(String payWay, String name) {
        this.payWay = payWay;
        this.name = name;
    }
}
