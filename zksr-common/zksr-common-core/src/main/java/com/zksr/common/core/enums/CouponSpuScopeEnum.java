package com.zksr.common.core.enums;

import lombok.Getter;

@Getter
public enum CouponSpuScopeEnum {
    NONE(-1, "无"),
    ALL(0, "全场券"),
    SUPPLIER(1, "入驻商券"),
    CLASS(2, "品类券"),
    BRAND(3, "品牌券"),
    SKU(4, "商品券"),
    ;
    private Integer scope;
    private String name;

    CouponSpuScopeEnum(Integer scope, String name) {
        this.scope = scope;
        this.name = name;
    }

    public static boolean isSupplier(Integer scope) {
        return SUPPLIER.getScope().equals(scope);
    }

    public static CouponSpuScopeEnum formValue(Integer scope) {
        for (CouponSpuScopeEnum value : values()) {
            if (value.getScope().equals(scope)) {
                return value;
            }
        }
        return CouponSpuScopeEnum.NONE;
    }

    public static boolean isAll(Integer spuScope) {
        return ALL.getScope().equals(spuScope);
    }
}
