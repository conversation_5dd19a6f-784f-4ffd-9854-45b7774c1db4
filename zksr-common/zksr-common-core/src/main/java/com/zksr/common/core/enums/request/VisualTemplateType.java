package com.zksr.common.core.enums.request;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
*  接口模板类型
* @date 2024/7/12 15:08
* <AUTHOR>
*/
@Getter
@AllArgsConstructor
public enum VisualTemplateType {

    BRANCH_TYPE(501,"门店信息"),

    ORDER_TYPE(502,"销售订单信息"),

    AFTER_TYPE(503,"售后订单信息"),

    RECEIPT_TYPE(504,"收款单信息"),

    BRANCH_VALUE_INFO_TYPE(505,"门店储值信息"),
    ;

    private final Integer type;

    private final String info;
}
