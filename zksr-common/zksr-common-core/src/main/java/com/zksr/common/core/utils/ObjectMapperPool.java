package com.zksr.common.core.utils;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.ObjectPool;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

@Slf4j
public class ObjectMapperPool{

    private static ObjectPool<ObjectMapper> objectPool = null;
    private static ObjectMapper defaultObjectMapper = null;
    private static ObjectMapper noNullObjectMapper = null;

    public static String SERVICE;
    public static Integer OBJMAP_POOL_TYPE;

    static {
        {
            defaultObjectMapper = new ObjectMapper();
            JavaTimeModule timeModule = new JavaTimeModule();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            timeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));
            timeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));
            defaultObjectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false);
            defaultObjectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
            defaultObjectMapper.registerModule(timeModule);
        }
        {
            noNullObjectMapper = new ObjectMapper();
            JavaTimeModule timeModule = new JavaTimeModule();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            timeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));
            timeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));
            noNullObjectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false);
            noNullObjectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
            noNullObjectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            noNullObjectMapper.registerModule(timeModule);
        }

        PooledObjectFactory<ObjectMapper> factory = new ObjectMapperFactory();
        GenericObjectPoolConfig<ObjectMapper> config = new GenericObjectPoolConfig();
        config.setMaxTotal(500);
        config.setMinIdle(1);
        config.setMaxIdle(5);
        objectPool = new GenericObjectPool<>(factory, config);
    }

    public static ObjectMapper get(){
        if(Objects.equals(OBJMAP_POOL_TYPE, 2)){
            try {
                return objectPool.borrowObject();
            }catch (Exception e){
                log.error(e.getMessage(), e);
            }
        }
        return defaultObjectMapper;
    }

    public static ObjectMapper getNoNull(){
        return noNullObjectMapper;
    }

    public static ObjectMapper getDefault(){
        return defaultObjectMapper;
    }

    public static void close(ObjectMapper objectMapper){
        if(Objects.equals(OBJMAP_POOL_TYPE, 2)) {
            if (objectMapper == null || defaultObjectMapper == objectMapper) {
                return;
            }
            try {
                objectPool.returnObject(objectMapper);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    private static class ObjectMapperFactory extends BasePooledObjectFactory<ObjectMapper> {

        @Override
        public ObjectMapper create() throws Exception {
            ObjectMapper objectMapper = new ObjectMapper();
            JavaTimeModule timeModule = new JavaTimeModule();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            timeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));
            timeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false);
            objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
            objectMapper.registerModule(timeModule);
            return objectMapper;
        }

        @Override
        public PooledObject<ObjectMapper> wrap(ObjectMapper objectMapper) {
            return new DefaultPooledObject<>(objectMapper);
        }
    }

    @Value("${spring.application.name:null}")
    public void setService(String service) {
        this.SERVICE = service;
    }

    @Value("${spring.application.objmapPoolType:1}")
    public void setObjmapPooltype(int type) {
        this.OBJMAP_POOL_TYPE = type;
    }

}