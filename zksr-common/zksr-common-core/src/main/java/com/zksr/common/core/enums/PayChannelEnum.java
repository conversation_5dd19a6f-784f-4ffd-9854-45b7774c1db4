package com.zksr.common.core.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.zksr.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付方式
 * @date 2024/3/8 8:23
 */
@Getter
@AllArgsConstructor
public enum PayChannelEnum {
    // 记账使用
    NONE("none", "无平台记账使用", false, 0),

    // 支付平台
    MOCK("mock", "模拟支付", true, 0),
    HLB("hlb", "合利宝", true, 1),
    MIDEA_PAY("mideaPay", "美的付", true, 1),
    WX_B2B_PAY("wxb2b", "微信B2B支付", true, 1),

    // 具体支付方式
    HLB_WX_LITE("hlb_wx_xcx", "微信小程序支付", true, 0),
    MIDEA_PAY_WX_LITE("midea_pay_wx_xcx", "美的付微信小程序支付", true, 0),
    WX_B2B_LETE("wx_b2b_pay_wx_xcx", "微信小程序b2b支付", false, 0),
    WALLET("wallet", "储值支付", true, 1),
    ;
    /**
     * 编码
     */
    private final String code;

    /**
     * 名字
     */
    private final String name;

    /**
     * 是否支持在线分账
     */
    private final boolean onlineSupportDivide;

    /**
     * 分账模式:  0-入驻商充值分账, 1-支付直接分账
     */
    private final int divideModel;


    public static boolean isMock(String platform) {
        return Objects.equals(MOCK.getCode(), platform);
    }

    public static boolean onLineDivide(String platform) {
        for (PayChannelEnum payChannelEnum : values()) {
            if (payChannelEnum.getCode().equals(platform)) {
                return payChannelEnum.isOnlineSupportDivide();
            }
        }
        return false;
    }

    /**
     * 获取当前支付方式 属性【是否支持在线分账】值
     * @param platform
     * @return
     */
    public static boolean getPayOnlineSupportDivide(String platform) {
        Set<PayChannelEnum> payChannelEnumSet = EnumSet.allOf(PayChannelEnum.class);   //获取枚举字典集合
        return payChannelEnumSet
                .stream()
                .filter(payChannelEnum -> payChannelEnum.getCode().equals(platform))
                .findFirst()
                .map(PayChannelEnum::isOnlineSupportDivide)
                .orElse(false);
    }

    public static boolean isB2b(String platform) {
        if (StringUtils.isEmpty(platform)) {
            return false;
        }
        return Objects.equals(platform, WX_B2B_PAY.getCode());
    }

    public static PayChannelEnum parse(String platform) {
        for (PayChannelEnum channelEnum : values()) {
            if (channelEnum.getCode().equals(platform)) {
                return channelEnum;
            }
        }
        return null;
    }

    /**
     * 是否是钱包支付体系
     */
    public static boolean isWallet(String platform) {
        return WALLET.getCode().equals(platform);
    }

    /**
     * 是否是合利宝支付
     * @param platform
     * @return
     */
    public static boolean isHlb(String platform) {
        if (StringUtils.isEmpty(platform)) {
            return false;
        }
        return Objects.equals(platform, HLB.getCode());
    }


    @JsonValue
    public String getCode() {
        return code;
    }

    @JsonCreator
    public static PayChannelEnum fromValue(String value) {
        for (PayChannelEnum payChannelEnum : PayChannelEnum.values()) {
            if (payChannelEnum.getCode().equals(value)) {
                return payChannelEnum;
            }
        }
        return NONE;
    }
}
