package com.zksr.common.core.domain.vo.openapi.receive;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomDateDeserializer;
import com.zksr.common.core.web.CustomDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/5/30 14:26
 * 商品库存
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("接收商品库存实体")
public class PrdInventoryVO {

    /** 入驻商编号 */
    @Excel(name = "入驻商ID")
    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    /** 可用库存数量（最小单位） */
    @Excel(name = "可用库存数量（最小单位）")
    @ApiModelProperty(value = "可用库存数量（最小单位）")
    @NotNull(message = "可用库存数量不能为空")
    private BigDecimal stock;

    /** 外部编码 */
    @Excel(name = "外部编码")
    @ApiModelProperty(value = "外部编码")
    @NotBlank(message = "外部编码不能为空")
    private String sourceNo;

    @Excel(name = "最后库存更新时间")
    @ApiModelProperty(value = "最后库存更新时间 yyyy-MM-dd HH:mm:ss 或 yyyy-MM-dd HH:mm:ss.SSS")
    @NotNull(message = "最后库存更新时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
/*    @JsonDeserialize(using = CustomDateDeserializer.class)
    @JsonSerialize(using = CustomDateSerializer.class)*/
    private Date lastUpdateTime;
    /**
     * 来源
     */
    private String source;
    /**
     * 平台商ID
     */
    private Long sysCode;

}
