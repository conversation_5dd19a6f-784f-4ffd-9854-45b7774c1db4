package com.zksr.common.core.erpUtils;

import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.config.AnntoProxyConfig;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.MimeTypeUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import static com.zksr.common.core.enums.request.VisualSettingConstants.APPLICATION_JSON;
import static com.zksr.common.core.enums.request.VisualSettingConstants.APPLICATION_URLENCODED;

/**
 * http 网络请求工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpUtils {

    public static final String FORM = "application/x-www-form-urlencoded";
    public static final String JSON = "application/json;charset=UTF-8";

    /**
     * 请求方式 - post
     */
    private static final String REQUEST_POST = "POST";
    /**
     * 请求方式 - get
     */
    private static final String REQUEST_GET = "GET";
    /**
     * 超时时间
     */
    public static final int TIME_OUT = 15 * 1000;

    /**
     * 超时时间 10 秒
     */
    public static final int TIME_OUT_TEN = 10*1000;


    /**
     * 发送post网络请求
     *
     * @param path   请求路径
     * @param params 请求参数(Map<String, String>)
     * @return 响应结果
     * @throws Exception Exception
     */
    public static String sendPost(String path, Map<String, String> params) throws Exception {
        return sendPost(path, getChangeResult(params), null, JSON);
    }

    /**
     * 发送post网络请求
     *
     * @param path   请求路径
     * @param params 请求参数(Map<String, String>)
     * @return 响应结果
     * @throws Exception Exception
     */
    public static String sendPost(String path, String params) throws Exception {
        return sendPost(path, params, null, JSON);
    }

    /**
     * 发送post网络请求
     *
     * @param path   请求路径
     * @param params 请求参数(JSONObject<String, Object>)
     * @return 响应结果
     * @throws Exception Exception
     */
    public static String sendPost(String path, JSONObject params, JSONObject headerParam) throws Exception {
        return sendPost(path, params.toJSONString(), headerParam, JSON);
    }

    /**
     * 发送post网络请求
     *
     * @param path        请求路径
     * @param data        参数
     * @param headerParam 请求头参数
     * @param timeOut     timeOut
     * @return 响应结果
     * @throws Exception Exception
     */
    public static String sendPost(String path, Object data, Map<String, Object> headerParam, int timeOut) throws Exception {
        return sendPost(path, JSONObject.toJSONString(data), headerParam, JSON, timeOut);
    }

    /**
     * 发送post网络请求
     *
     * @param path        请求路径
     * @param data        参数
     * @param headerParam 请求头参数
     * @param timeOut     timeOut
     * @return 响应结果
     * @throws Exception Exception
     */
    public static String sendPost(String path, String data, Map<String, Object> headerParam, int timeOut) throws Exception {
        return sendPost(path, data, headerParam, JSON, timeOut);
    }

    /**
     * 发送post网络请求
     *
     * @param path     请求路径
     * @param params   请求参数(Map<String, String>)
     * @param dataType 数据类型
     * @return String
     * @throws Exception
     */
    public static String sendPost(String path, Map<String, String> params, String dataType) throws Exception {
        return sendPost(path, getChangeResult(params), null, dataType);
    }

    /**
     * 发送Post请求
     *
     * @param path        地址
     * @param postString  post参数
     * @param headerParam 头部参数
     * @param dataType    数据类型
     * @return String
     * @throws Exception 网络异常时抛出
     */
    private static String sendPost(String path, String postString, Map<String, Object> headerParam, String dataType) throws Exception {
        return getStringFromResponse(send(path, postString, headerParam, dataType, REQUEST_POST, TIME_OUT));
    }


    /**
     * 发送Post请求
     *
     * @param path        地址
     * @param postString  post参数
     * @param headerParam 头部参数
     * @param dataType    数据类型
     * @param timeOut     timeout
     * @return String
     * @throws Exception 网络异常时抛出
     */
    private static String sendPost(String path, String postString, Map<String, Object> headerParam, String dataType, int timeOut) throws Exception {
        return getStringFromResponse(send(path, postString, headerParam, dataType, REQUEST_POST, timeOut));
    }


    /**
     * 发送get请求
     *
     * @param path       地址
     * @param postString 请求参数
     * @param dataType   数据类型
     * @return InputStream InputStream
     * @throws Exception 网络异常时抛出
     */
    private static InputStream sendGetReturnInputStream(String path, String postString, String dataType) throws Exception {
        return send(path, postString, null, dataType, REQUEST_GET);
    }

    /**
     * * 发送post 请求
     *
     * @param path        请求路径
     * @param postString  请求参数(Map<String, String>)
     * @param dataType    传输数据类型
     * @param headerParam 请求头部参数
     * @return String
     * @throws Exception Exception
     */
    public static InputStream send(String path, String postString, Map<String, Object> headerParam, String dataType, String requestMethod) throws Exception {
        return send(path, postString, headerParam, dataType, requestMethod, TIME_OUT);
    }

    /**
     * * 发送post 请求
     *
     * @param path        请求路径
     * @param postString  请求参数(Map<String, String>)
     * @param dataType    传输数据类型
     * @param headerParam 请求头部参数
     * @param timeOut     超时时间
     * @return String
     * @throws Exception Exception
     */
    public static InputStream send(String path, String postString, Map<String, Object> headerParam, String dataType, String requestMethod, int timeOut) throws Exception {
        // 得到实体的二进制数据，以便计算长度
        byte[] entityData = postString.getBytes(StandardCharsets.UTF_8);
        URL url = new URL(path);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod(requestMethod);
        conn.setConnectTimeout(timeOut);
        // 如果通过post提交数据，必须设置允许对外输出数据
        conn.setDoOutput(true);
        // Content-Type: application/json
        // Content-Length: 38
        if (dataType == null || "".equals(dataType)) {
            dataType = JSON;
        }
        conn.setRequestProperty("Content-Type", dataType);
        conn.setRequestProperty("Accept", dataType);
        // 传输的数据长度
        conn.setRequestProperty("Content-Length", String.valueOf(entityData.length));
        //设置请求头参数
        setHeaderParam(conn, headerParam);
        OutputStream outStream = conn.getOutputStream();
        outStream.write(entityData);
        // 把内存中的数据刷新输送给对方
        outStream.flush();
        outStream.close();
        if (conn.getResponseCode() == 200) {
            return conn.getInputStream();
        } else {
            System.out.println("responseError request url:" + path + ", param is:" + postString + ", responseCode is:" + conn.getResponseCode());
        }
        return null;
    }

    private static String getStringFromResponse(InputStream inputStream) {
        if (inputStream != null) {
            return dealResponseResult(inputStream);
        }
        return null;
    }

    /**
     * 设置请求头参数
     *
     * @param conn        连接对象
     * @param headerParam 参数
     */
    private static void setHeaderParam(HttpURLConnection conn, Map<String, Object> headerParam) {
        if (conn == null || headerParam == null) {
            return;
        }
        for (String next : headerParam.keySet()) {
            conn.setRequestProperty(next, headerParam.get(next).toString());
        }
    }

    /**
     * 处理服务器的响应结果（将输入流转化成字符串）
     *
     * @param inputStream inputStream
     * @return String
     */
    public static String dealResponseResult(InputStream inputStream) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] data = new byte[1024];
        int len;
        try {
            while ((len = inputStream.read(data)) != -1) {
                byteArrayOutputStream.write(data, 0, len);
            }
            String resultStr = byteArrayOutputStream.toString();
            return new String(resultStr.getBytes(getEncoding(resultStr)), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error(" HttpUtils.dealResponseResult失败,", e);
        }
        return null;
    }

    /**
     * 将map转成String ,例如?title=dsfdsf&timelength=23&method=save
     *
     * @param params params
     * @return String
     */
    private static String getChangeResult(Map<String, String> params) {
        StringBuilder sb = new StringBuilder();
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                sb.append(entry.getKey()).append('=').append(entry.getValue()).append('&');
            }
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * 获取请求对象中的参数转换为JSON
     *
     * @param request request
     * @return JSONObject
     * @throws IOException IOException
     */
    public static JSONObject getParameters(HttpServletRequest request) throws IOException {
        BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
        StringBuilder responseStrBuilder = new StringBuilder();
        String inputStr;
        while ((inputStr = streamReader.readLine()) != null) {
            responseStrBuilder.append(inputStr);
        }
        return JSONObject.parseObject(responseStrBuilder.toString());
    }

    /**
     * 发送get请求
     *
     * @param url url
     * @return String
     */
    public static String sendGet(String url) {
        try {
            String content = null;
            URLConnection urlConnection = new URL(url).openConnection();
            HttpURLConnection connection = (HttpURLConnection) urlConnection;
            connection.setRequestMethod("GET");
            //连接
            connection.connect();
            //得到响应码
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader
                        (connection.getInputStream(), StandardCharsets.UTF_8));
                StringBuilder bs = new StringBuilder();
                String l;
                while ((l = bufferedReader.readLine()) != null) {
                    bs.append(l).append("\n");
                }
                content = bs.toString();
            }
            return content;
        } catch (Exception ex) {
            log.error(" HttpUtils.sendGet异常，", ex);
        }
        return null;
    }

    public static String getEncoding(String str) {
        String encode = "GB2312";
        try {
            if (isEncoding(str, encode)) { // 判断是不是GB2312
                return encode;
            }
        } catch (Exception ignored) {
        }
        encode = "ISO-8859-1";
        try {
            if (isEncoding(str, encode)) { // 判断是不是ISO-8859-1
                return encode;
            }
        } catch (Exception ignored) {
        }
        encode = "UTF-8";
        try {
            if (isEncoding(str, encode)) { // 判断是不是UTF-8
                return encode;
            }
        } catch (Exception ignored) {
        }
        encode = "GBK";
        try {
            if (isEncoding(str, encode)) { // 判断是不是GBK
                return encode;
            }
        } catch (Exception ignored) {
        }
        return ""; // 如果都不是，说明输入的内容不属于常见的编码格式。
    }

    public static boolean isEncoding(String str, String encode) {
        try {
            if (str.equals(new String(str.getBytes(), encode))) {
                return true;
            }
        } catch (UnsupportedEncodingException e) {
            log.error(" HttpUtils.isEncoding异常，", e);
        }
        return false;
    }

    /**
     * simplePost
     *
     * @param url         接口地址
     * @param headerMap   请求头参数
     * @param jsonStr     请求体参数
     * @param contentType 请求数据类型
     * @return 响应消息
     */
    public static String simplePost(String url, Map<String, String> headerMap, String jsonStr, String contentType) {
        HttpRequest request = HttpUtil.createPost(url);
        AnntoProxyConfig anntoProxyConfig = SpringUtils.getBean(AnntoProxyConfig.class);
        if (anntoProxyConfig.isEnable()) {
            log.info("http开启代理, url: {}", url);
            request.setHttpProxy(anntoProxyConfig.getHost(), anntoProxyConfig.getPort());
        }
        if (StringUtils.isNotEmpty(headerMap)) {
            request = request.headerMap(headerMap, true);
        }
        log.info("请求地址：{},请求头信息:{},请求数据:{},数据类型:{}",url,headerMap,jsonStr,contentType);
        request.contentType(contentType)
                .disableCache()
                .timeout(TIME_OUT).setConnectionTimeout(TIME_OUT);
        //组装请求体数据
        if(APPLICATION_JSON.equalsIgnoreCase(contentType)){
            request.body(JSONUtil.toJsonStr(jsonStr).getBytes(StandardCharsets.UTF_8));
        }else if(APPLICATION_URLENCODED.equalsIgnoreCase(contentType)){
            //request.header("Content-Type", contentType).form(JSONObject.parseObject(jsonStr));
            Map map = JSONObject.parseObject(jsonStr, Map.class);
            StringBuilder sb = new StringBuilder();
            map.forEach((key, value) -> {
                    sb.append(key).append(StringPool.EQUALS).append(value).append(StringPool.AMPERSAND);
            });

            String data = sb.substring(0, sb.length() - 1);
            //request.setUrl(url + "?" + data);
            request.body(data.getBytes(StandardCharsets.UTF_8));
        }
        HttpResponse httpResponse = request.execute();
        log.info("第三方系统接口响应结果：{}",httpResponse);
        if (httpResponse.isOk()) {
            return httpResponse.body();
        } else {
            log.error(String.format("status:%s,response:%s", httpResponse.getStatus(), httpResponse.body()));
            throw new HttpException("第三方接口请求失败，错误信息：{}",httpResponse.body());
        }
    }

    /*
     * simplePost
     * @param url       接口地址
     * @param jsonStr   请求参数
     * @param jsonStr   请求参数
     * @param timeout   超时毫秒数
     * @return
     */
    public static String simplePost(String url, Map<String,String> headerMap,String jsonStr, int timeout) {
        HttpRequest request = HttpUtil.createPost(url);
        if (StringUtils.isNotEmpty(headerMap)){
            request = request.headerMap(headerMap, true);
        }
        HttpResponse httpResponse = request
                .contentType(MimeTypeUtils.APPLICATION_JSON_VALUE)
                .disableCache()
                .body(JSONUtil.toJsonStr(jsonStr).getBytes(StandardCharsets.UTF_8))
                .timeout(timeout).setConnectionTimeout(timeout)
                .execute();
        if (httpResponse.isOk()) {
            return httpResponse.body();
        } else {
            log.error(String.format("status:%s,response:%s", httpResponse.getStatus(), httpResponse.body()));
            throw new HttpException("第三方接口请求失败，错误信息：{}",httpResponse.body());
        }
    }

    /**
     * 发送GET请求
     *
     * @param url       请求的URL
     * @param headers   请求头参数
     * @param params    请求参数
     * @param timeout   超时时间（单位：毫秒）
     * @return          响应结果字符串
     */
    public static String sendGet(String url, Map<String, String> headers, Map<String, Object> params, int timeout) {
        // 构建请求
        HttpRequest request = HttpUtil.createGet(url)
                .addHeaders(headers) // 添加请求头
                .form(params)        // 添加请求参数
                .timeout(timeout);   // 设置超时时间
        // 执行请求并获取响应
        HttpResponse httpResponse = request.execute();
        if (httpResponse.isOk()) {
            return httpResponse.body();
        } else {
            log.error(String.format("status:%s,response:%s", httpResponse.getStatus(), httpResponse.body()));
            throw new HttpException("接口请求失败");
        }
    }

}
