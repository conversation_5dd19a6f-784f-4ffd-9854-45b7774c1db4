package com.zksr.common.core.enums;

/**
 * @Author: chenyj8
 * @Desciption: 美的付终端类型
 */
public enum TerminalTypeEnum {
    PC("PC", "PC端"),
    MOBILE("MOBILE", "移动端"),

    ;


    private String value;

    private String desc;

    TerminalTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
