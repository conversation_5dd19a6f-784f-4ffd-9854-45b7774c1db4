package com.zksr.common.core.domain.vo.openapi;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.business.AccountBusiType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
* 门店储值信息 对外接口信息
* @date 2025/3/10 14:53
* <AUTHOR>
*/
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BranchValueInfoOpenDTO {

    @ApiModelProperty(value = "门店编号")
    private String branchNo;

    /** 门店id */
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    @ApiModelProperty(value = "充值、提现总金额")
    private BigDecimal totaltAmt;

    @ApiModelProperty(value = "赠送总金额")
    private BigDecimal giveAmt;


    /**
     * 业务类型(待枚举，根据busi_type找busi出处) {@link AccountBusiType}
     */
    @Excel(name = "业务类型(withdraw_success 提现：  BRANCH_RECHARGE 充值： branch_recharge （包含branch_recharge_give 门店充值赠送）)")
    private String busiType;

    /** 对接唯一编码 */
    @Excel(name = "对接唯一编码")
    private String sendCode;

}
