package com.zksr.common.core.domain.vo.accInvoiceSetting;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.MerchantTypeEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "发票简单绑定信息")
@Data
public class AccInvoiceSetttingSimpleBindVO {
    
    @ApiModelProperty(value = "支付平台", required = true)
    private String platform;
    
    /** 是否开通电子发票（0 否 1 是) */
    @Excel(name = "是否开通电子发票")
    @ApiModelProperty("是否开通电子发票（0 否 1 是)")
    private Integer enableElectronicInvoice;
    
    /** 是否开票（0 否 1 是) */
    @Excel(name = "是否开票")
    @ApiModelProperty("是否开票（0 否 1 是)")
    private Integer enableInvoice;
    
    /** 开票周期(1-按天 2-按月 3-按季度 4-按年度 5-手动) */
    @Excel(name = "开票周期")
    @ApiModelProperty("开票周期(1-按天 2-按月 3-按季度 4-按年度 5-手动)")
    private Integer invoicePeriod;
    
    /** 单位名称 */
    @Excel(name = "单位名称")
    @ApiModelProperty("单位名称")
    private String unitName;
    
    
    /** 纳税人识别号 */
    @Excel(name = "纳税人识别号")
    @ApiModelProperty("纳税人识别号")
    private String taxpayerNo;
    
    /** 单位地址 */
    @Excel(name = "单位地址")
    @ApiModelProperty("单位地址")
    private String unitAddress;
    
    /** 单位电话 */
    @Excel(name = "单位电话")
    @ApiModelProperty("单位电话")
    private String unitPhone;
    
    /** 开户银行 */
    @Excel(name = "开户银行")
    @ApiModelProperty("开户银行")
    private String bankName;
    
    /** 银行账户 */
    @Excel(name = "银行账户")
    @ApiModelProperty("银行账户")
    private String bankAccount;
    
    /** 收票人手机 */
    @Excel(name = "收票人手机")
    @ApiModelProperty("收票人手机")
    private String recipientPhone;
    
    /** 开票人邮箱 */
    @Excel(name = "开票人邮箱")
    @ApiModelProperty("开票人邮箱")
    private String invoiceEmail;
    
    /** 开票内容 */
    @Excel(name = "开票内容")
    @ApiModelProperty("开票内容")
    private String invoiceContent;
    
}
