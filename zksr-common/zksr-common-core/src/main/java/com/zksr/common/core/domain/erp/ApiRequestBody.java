package com.zksr.common.core.domain.erp;

import com.alibaba.fastjson2.annotation.JSONField;
import com.zksr.common.core.enums.request.RequestType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ApiRequestBody<T> {

    private String reqId;
    private String reqTime;
    private String bizData;
    private RequestType requestType;

    /**
     * 请求方
     */
    @JSONField(serialize = false)
    private int to;

    /**
     * 来源方
     */
    @JSONField(serialize = false)
    private int from;

    private String strategyId;

    @JSONField(serialize = false)
    private String shipperNo;

    private ApiDataModel<T> dataModel;

}
