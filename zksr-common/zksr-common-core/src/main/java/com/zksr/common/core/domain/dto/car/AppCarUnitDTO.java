package com.zksr.common.core.domain.dto.car;

import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车商品组合唯一key
 * @date 2024/5/26 11:27
 */
@Data
@ApiModel(description = "购物车商品组合唯一key")
@AllArgsConstructor
@NoArgsConstructor
public class AppCarUnitDTO {

    @ApiModelProperty(value = "itemId")
    private Long itemId;

    @ApiModelProperty(value = "单位类型, 1-最小单位, 2-中单位, 3-大单位")
    private Integer unitSize;

    public static AppCarUnitDTO convert(String obj) {
        String string = String.valueOf(obj);
        if (StringUtils.isEmpty(string)) {
            return null;
        }
        String[] data = string.split(StringPool.UNDERSCORE);
        return new AppCarUnitDTO(Long.valueOf(data[0]), Integer.parseInt(data[1]));
    }


    public static AppCarUnitDTO convert(AppCarIdDTO carIdDTO) {
        return new AppCarUnitDTO(carIdDTO.itemId(), carIdDTO.getUnitSize());
    }

    public static AppCarUnitDTO build(Long itemId, Integer unitSize) {
        return new AppCarUnitDTO(itemId, unitSize);
    }

    @Override
    public String toString() {
        return StringUtils.format("{}_{}", itemId, unitSize);
    }
}
