package com.zksr.common.core.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 促销类型枚举类(字典prm_no)
 * @Author: liuxingyu
 * @Date: 2024/5/14 9:51
 */
@Getter
public enum PrmNoEnum {
    SK("SK","秒杀"),
    BG("BG","买赠"),
    FG("FG","满赠"),
    FD("FD","满减"),
    SP("SP","特价"),
    CB("CB","组合促销"),
    ;

    @Getter
    private String type;
    @Getter
    private String name;
    PrmNoEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * @param prmNo 活动类型
     * @return 是否满减
     */
    public static boolean isFd(String prmNo) {
        return Objects.equals(prmNo, FD.getType());
    }

    /**
     * @param prmNo 活动类型
     * @return 是否满赠
     */
    public static boolean isFg(String prmNo) {
        return Objects.equals(prmNo, FG.getType());
    }

    /**
     * @param prmNo 活动类型
     * @return 是否买赠
     */
    public static boolean isBg(String prmNo) {
        return Objects.equals(prmNo, BG.getType());
    }

    /**
     * @param prmNo 活动类型
     * @return 是否秒杀
     */
    public static boolean isSk(String prmNo) {
        return Objects.equals(prmNo, SK.getType());
    }

    /**
     * @param prmNo 活动类型
     * @return 是否特价
     */
    public static boolean isSp(String prmNo) {
        return Objects.equals(prmNo, SP.getType());
    }

    /**
     * 是否组合促销
     * @param prmNo 活动类型
     * @return 是否组合促销
     */
    public static boolean isCb(String prmNo) {
        return Objects.equals(prmNo, CB.getType());
    }

    public static PrmNoEnum formValue(String prmNo) {
        for (PrmNoEnum value : values()) {
            if (value.getType().equals(prmNo)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isSpOrSk(String prmNo) {
        return isSp(prmNo) || isSk(prmNo);
    }
}
