package com.zksr.common.core.domain.vo.openapi.receive;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/5/30 15:12
 * @注释
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("接收订单出库回传详情实体")
public class OrderOutboundReturnDetailVO {

    /** 行号 */
    @Excel(name = "行号")
    @ApiModelProperty(value = "行号")
    @NotNull(message = "行号不能为空")
    private Long lineNum;

    /** ERP商品编号 */
    @Excel(name = "ERP商品编号")
    @ApiModelProperty(value = "ERP商品编号")
    @NotNull(message = "ERP商品编号不能为空")
    private String erpItemNo;

    /** 发货单位数量(最小单位) */
    @Excel(name = "发货单位数量")
    @ApiModelProperty(value = "发货单位数量")
    @NotNull(message = "订单发货单位数量不能为空")
    private BigDecimal sendUnitQty;



/*    *//** skuId(不必要传  用于转换) *//*
    @Excel(name = "skuId")
    @ApiModelProperty(value = "skuId")
    private String skuId;*/

    /** 快递公司编码 */
    @Excel(name = "快递公司编码")
    @ApiModelProperty(value = "快递公司编码")
    private String expressComNo;

    /** 物流单号(多个用逗号隔开) */
    @Excel(name = "物流单号(多个用逗号隔开)")
    @ApiModelProperty(value = "物流单号(多个用逗号隔开)")
    private String expressNo;

//    /** 备注 */
//    @Excel(name = "备注")
//    @ApiModelProperty(value = "备注")
//    private String memo;

}
