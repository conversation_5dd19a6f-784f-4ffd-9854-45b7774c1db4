package com.zksr.common.core.domain.vo.openapi;


import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AfterDetailPushDTO {
    /** 售后单明细id */
    @TableId
    private Long supplierAfterDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    private Long sysCode;

    /** 售后单编号 */
    @Excel(name = "售后单编号")
    private String afterNo;

    /** 售后单id */
    @Excel(name = "售后单id")
    private Long afterId;

    /** 售后单明细编号 */
    @Excel(name = "售后单明细编号")
    private String supplierAfterDtlNo;

    /** 入驻商售后单id */
    @Excel(name = "入驻商售后单id")
    private Long supplierAfterId;

    /** 入驻商售后单编号 */
    @Excel(name = "入驻商售后单编号")
    private String supplierAfterNo;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 0-本地配送商品 1-全国一件代发商品 */
    @Excel(name = "0-本地配送商品 1-全国一件代发商品")
    private Long itemType;

    /** 上架商品id */
    @Excel(name = "上架商品id")
    private Long itemId;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    private Long spuId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    private Long skuId;

    /** 退货原因 */
    @Excel(name = "退货原因")
    private String reason;

    /** 原申请退货最小单位数量 */
    @Excel(name = "原申请退货最小单位数量")
    private BigDecimal originalReturnQty;

    /** 原申请退货金额 */
    @Excel(name = "原申请退货金额")
    private BigDecimal originalReturnAmt;

    /** 退货数量 */
    @Excel(name = "退货数量")
    private BigDecimal returnQty;

    /** 退货价 */
    @Excel(name = "退货价")
    private BigDecimal returnPrice;

    /** 退货金额 */
    @Excel(name = "退货金额")
    private BigDecimal returnAmt;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundAmt;

    /** 退货说明 */
    @Excel(name = "退货说明")
    private String descr;

    /** 待定：售后类型(数据字典);待定：1仅退款 2退货退款 3换货 4补发 */
    @Excel(name = "待定：售后类型(数据字典);待定：1仅退款 2退货退款 3换货 4补发")
    private Long afterType;

    /** 售后阶段(数据字典);1发货前退款 2发货后退款 */
    @Excel(name = "售后阶段(数据字典);1发货前退款 2发货后退款")
    private Long afterPhase;

    /** 退款类型(数据字典);1退全款 2退部分款  3不退款 */
    @Excel(name = "退款类型(数据字典);1退全款 2退部分款  3不退款")
    private Long refundType;

    /** 审核状态(数据字典);0待审核 1同意 2拒绝 */
    @Excel(name = "审核状态(数据字典);0待审核 1同意 2拒绝")
    private Long approveState;

    /** 退货状态(数据字典);0-未退货 1-退货中 2-退货完成  3-无需退货 */
    @Excel(name = "退货状态(数据字典);0-未退货 1-退货中 2-退货完成  3-无需退货")
    private Long returnState;

    /** 退款状态(数据字典);0-未退款 1-退款中 2-退款完成  3-退款失败 */
    @Excel(name = "退款状态(数据字典);0-未退款 1-退款中 2-退款完成  3-退款失败")
    private Long refundState;

    /** 退款总金额;包含本次售后单和之前未取消的售后单的退款金额之和 */
    @Excel(name = "退款总金额;包含本次售后单和之前未取消的售后单的退款金额之和")
    private BigDecimal orderdtlRefundAmt;

    /** 退款方式（数据字典）;0-在线支付 1-储值支付 2-货到付款 */
    @Excel(name = "退款方式", readConverterExp = "数=据字典")
    private String payway;

    /** 支付平台(数据字典);从订单表 */
    @Excel(name = "支付平台(数据字典);从订单表")
    private String platform;

    /** 订单支付时间;从订单表 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单支付时间;从订单表", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    /** 订单完成时间;从订单表 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单完成时间;从订单表", width = 30, dateFormat = "yyyy-MM-dd")
    private Date finishTime;

    /** 退货完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退货完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date returnTime;

    /** 退款完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退款完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date refundTime;

    /** 是否已取消 0未取消 1已取消 */
    @Excel(name = "是否已取消 0未取消 1已取消")
    private Long isCancel;

    /** 来源;1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序 */
    @Excel(name = "来源;1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序")
    private Long source;

    /** 是否已经同步 1-是 0-否 */
    @Excel(name = "是否已经同步 1-是 0-否")
    private Integer syncFlag;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 用于发起退款流程的退款单号 */
    @Excel(name = "退款单号")
    private String refundNo;

    /** 精准成交价（6位小数） */
    @Excel(name = "精准退款成交价")
    private BigDecimal exactReturnPrice;

    /** 精准商品金额（6位小数） */
    @Excel(name = "精准退款商品金额")
    private BigDecimal exactReturnAmt;

    /** 优惠劵优惠金额(分摊的) */
    @Excel(name = "售后优惠劵优惠金额(分摊的)")
    private BigDecimal returnCouponDiscountAmt;

    /** 优惠劵优惠金额(不分摊的) */
    @Excel(name = "售后优惠劵优惠金额(不分摊的)")
    private BigDecimal returnCouponDiscountAmt2;

    /** 活动优惠金额(不分摊的) */
    @Excel(name = "售后活动优惠金额(分摊的)")
    private BigDecimal returnActivityDiscountAmt;

    /** 是否是赠品 1-是  0-否 */
    @Excel(name = "是否是赠品 1-是  0-否")
    private Integer giftFlag;

    /** 入驻商售后订单行号 */
    @Excel(name = "入驻商售后订单行号")
    private Long lineNum;

    /** 订单购买单位;从订单，最小单位的单位，中单位的单位，大单位的单位 */
    @Excel(name = "订单购买单位")
    private String orderUnit ;

    /** 单位大小 */
    @Excel(name = "单位大小")
    private Integer orderUnitType ;

    /** 订单购买单位数量;从订单，购买的是中单位，即为中单位数量。小单位、大单位同理 */
    @Excel(name = "订单购买单位数量")
    private Long orderUnitQty ;

    /** 订单换算数量;从订单，小单位为1，中、大单位的换算数量 */
    @Excel(name = "订单换算数量")
    private Long orderUnitSize ;

    /** 售后单位;最小单位的单位，中单位的单位，大单位的单位 */
    @Excel(name = "售后单位")
    private String returnUnit ;

    /** 单位大小 */
    @Excel(name = "单位大小")
    private Integer returnUnitType ;

    /** 售后单位数量;发货的是中单位，即为中单位数量。小单位、大单位同理 */
    @Excel(name = "售后单位数量")
    private BigDecimal returnUnitQty ;

    /** 售后单位换算数量;小单位为1，中、大单位的换算数量 */
    @Excel(name = "售后单位换算数量")
    private Long returnUnitSize ;

    /** 商品spu名称 */
    @Excel(name = "商品spu名称")
    private String spuName;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @ApiModelProperty(value = "快递公司编码")
    private String expressCom;

    /** 同步库存标识 0否 1是*/
    @Excel(name = "同步库存标识 0否 1是")
    private Integer syncStock;

    //第三方需要的定义参数数据======================================
    /** 商品的外部编码 */
    @Excel(name = "商品的外部编码")
    private String itemSourceNo;

    /** 商品条码 */
    private String itemBarcode;

    /** 退货原单价 取对应销售订单的原销售价 */
    private BigDecimal itemSalePrice;

    /** 对应入驻商订单详情行号 */
    private Long supplierOrderLineNum;

}
