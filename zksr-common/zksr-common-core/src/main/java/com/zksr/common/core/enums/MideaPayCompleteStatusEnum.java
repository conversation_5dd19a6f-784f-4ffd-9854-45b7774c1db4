package com.zksr.common.core.enums;

/**
 * @Author: chenyj8
 * @Desciption: 美的付交易通知状态
 */
public enum MideaPayCompleteStatusEnum {
    NOT_EXIST("NOT_EXIST", "订单不存在"),
    SUCCESS("SUCCESS", "成功"),
    FAIL("FAIL", "失败"),
    WAIT_PAY("WAIT_PAY", "订单已接收"),
    PAYING("PAYING", "支付中"),
    COMPLETE("COMPLETE", "支付完成"),
    ;

    private String code;

    private String desc;


    MideaPayCompleteStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getValue() {
        return desc;
    }

}
