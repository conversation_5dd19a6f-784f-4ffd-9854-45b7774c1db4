package com.zksr.common.core.domain.vo.openapi;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 要货单数据匹配状态结果
 * @date 2024/12/10 17:29
 */
@Data
@NoArgsConstructor
public class CreateYhDataMatchRespVO {

    @ApiModelProperty("要货单批次号(系统唯一)")
    @NotEmpty(message = "要货单批次号(系统唯一)")
    private String yhBatchNo;

    @ApiModelProperty("批次状态,0-未处理,1-已接受,  batchState=1的时候, 才有详情数据, 详情状态")
    private Integer batchState = 1;

    @ApiModelProperty("批次记录")
    private List<YhDtl> dtlList = new ArrayList<>();

    @Data
    public static class YhDtl {

        @ApiModelProperty("行号")
        private Integer lineNum;

        @ApiModelProperty("价格,不含促销")
        private BigDecimal currentPrice;

        @ApiModelProperty("有效库存")
        private Long currentAvailableQty;

        @ApiModelProperty("与最小单位转换比例, 匹配单位1个等于多少个最小单位")
        private BigDecimal stockConvert;

        @ApiModelProperty("要货原国条码")
        private String posBarcode;

        @ApiModelProperty("匹配备注: 库存不足,未上架, 匹配失败")
        private String matchRes;

        @ApiModelProperty("匹配状态: 0-未匹配, 1-匹配成功, 2-匹配失败")
        private Integer matchState;
    }

    public CreateYhDataMatchRespVO(String yhBatchNo) {
        this.yhBatchNo = yhBatchNo;
    }
}
