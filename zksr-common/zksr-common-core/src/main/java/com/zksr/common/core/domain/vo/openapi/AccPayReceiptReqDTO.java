package com.zksr.common.core.domain.vo.openapi;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
*  获取收款单支付流水信息入参
* @date 2024/10/19 17:34
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccPayReceiptReqDTO {

    /**
     * 订单类型
     * 来源：{@link com.zksr.common.core.constant.SheetTypeConstants.XSS}、  {@link com.zksr.common.core.constant.SheetTypeConstants.SHS}
     */
    @ApiModelProperty(value = "单据类型  XSS：销售订单  SHS：售后单 ")
    private String sheetType;

    @ApiModelProperty(value = "线上支付销售订单号")
    private String sheetNo;

    /**
     * 支付方式 {@link com.zksr.common.core.enums.OrderPayWayEnum}
     */
    @ApiModelProperty(value = "支付方式")
    private String payWay;

    @ApiModelProperty(value = "货到付款/售后退款单号集合")
    private List<String> hdfkNoList;


}
