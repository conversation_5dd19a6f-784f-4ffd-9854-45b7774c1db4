package com.zksr.common.core.constant;

public class OpenApiConstants {

    /**
     * 对接系统类型
     */
    public static final String SYSTEM_TYPE_ERP="ERP";
    public static final String SYSTEM_TYPE_WMS="WMS";

    /**
     * b2b订单接口类型
     */
    public static final String SHEET_MODEL_TYPE_TH="TH"; //退货单
    public static final String SHEET_MODEL_TYPE_XS="XS"; //销售单

    /** 收款方式 - 在线支付 */
    public static final String  COLLECTED_WAY_ZXZF ="ZXZF"; // 在线支付

    /** 收款方式 - 货到收款 */
    public static final String COLLECTED_WAY_HDSK ="HDSK"; // 货到收款

    /** 日志状态 - 取消 */
    public static final Integer LOG_STATUS_CANCEL = -2; // 取消

    /** 日志状态 - 失败 */
    public static final Integer LOG_STATUS_FAIN = -1; // 失败

    /** 日志状态 - 待处理 */
    public static final Integer LOG_STATUS_WAIT = 0; // 待处理

    /** 日志状态 - 成功 */
    public static final Integer LOG_STATUS_SUCCES = 1; // 成功

    /** 日志状态 - 异常 */
    public static final Integer LOG_STATUS_ERR = -2; //  -2异常（接收时使用）

    /**
     * 请求响应状态码 操作成功
     */
    public static final Integer SUCCESS_0 = 0;

    /**
     * 请求响应状态码 操作成功
     */
    public static final Integer SUCCESS_1 = 200;

    /**
     * 请求响应状态码 操作失败
     */
    public static final Integer ERROR = 500;

    /**
     * 请求响应状态码 操作成功
     */
    public static final String RECEIVE_SUCCESS = "接收成功";

    /**
     * 请求响应状态码 操作成功
     */
    public static final String PROCESS_SUCCESS = "处理成功";

    /**
     * 请求响应状态码 操作成功
     */
    public static final String PROCESS_FAIN = "处理失败";

    /** 请求状态 - 未组装数据 */
    public static final Integer REQ_STATUS_0 = 0;

    /** 请求状态 - 组装数据推送中/接收中 */
    public static final Integer REQ_STATUS_1 = 1;

    /** 请求状态 - 推送/接收完成 */
    public static final Integer REQ_STATUS_2 = 2;

    /** 日志类型 - 同步数据 */
    public static final Integer LOG_TYPE_SYNC = 1;

    /** 日志类型 - 接收数据 */
    public static final Integer LOG_TYPE_RECEIVE = 2;

    /** 日志重发 - 否 */
    public static final Long LOG_IS_RETRY_0 = 0L;

    /** 日志重发 - 是 */
    public static final Long LOG_IS_RETRY_1 = 1L;

    /** 订单- 同步标识  未推送*/
    public static final Integer ORDER_SYNC_FLAG_0 = 0;

    /** 订单 - 同步标识 已推送*/
    public static final Integer ORDER_SYNC_FLAG_1 = 1;

    /** 订单 - 同步标识 已接收*/
    public static final Integer ORDER_SYNC_FLAG_2 = 2;

    /** 标识  否*/
    public static final Integer OPEN_FLAG_0 = 0;

    /** 标识  是*/
    public static final Integer OPEN_FLAG_1 = 1;

    /**
     * OpenSOURCE 商城显示物流信息 第三方
     */
    public static final String OPEN_SOURCE_LOGISTICS_INFO_0 = "0";

    /**
     * OpenSOURCE 商城显示物流信息 本地
     */
    public static final String OPEN_SOURCE_LOGISTICS_INFO_1 = "1";

    public static final String DEFAULT_ORG_CODE = "100";

}
