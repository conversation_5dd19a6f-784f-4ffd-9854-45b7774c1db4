package com.zksr.common.core.domain.vo.openapi.receive;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
* 订单发货前取消请求体
* @date 2024/12/4 10:14
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("订单发货前取消请求体")
public class OrderCancelVO {

    /** B2B入驻商订单号(入驻商订单编号) */
    @Excel(name = "入驻商订单号")
    @ApiModelProperty(value = "入驻商订单号")
    @NotNull(message = "B2B入驻商销售订单编号不能为空")
    private String supplierOrderNo;

}
