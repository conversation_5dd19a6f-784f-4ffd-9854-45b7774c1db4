package com.zksr.common.core.constant;

/**
 * Token的Key常量
 *
 * <AUTHOR>
 */
public class TokenConstants
{
    /**
     * 令牌自定义标识
     */
    public static final String AUTHENTICATION = "Authorization";

    /**
     * 令牌前缀
     */
    public static final String PREFIX = "Bearer ";

    /**
     * 令牌秘钥
     */
    public final static String SECRET = "zksrabcdefghijklmnopqrstuvwxyz";


    /**
     * 商城端令牌自定义标识
     */
    public static final String MALL_AUTHORIZATION = "MALLAuthorization";

    /**
     * 商城端令牌前缀
     */
    public static final String MALL_PREFIX = "MALLBearer ";

    /**
     * 商城端令牌秘钥
     */
    public final static String MALL_SECRET = "MALLzksrabcdefghijklmnopqrstuvwxyz";

    /**
     * openapi端令牌自定义标识
     */
    public static final String OPENAPI_AUTHORIZATION = "OPENAPIAuthorization";

    /**
     * openapi端令牌前缀
     */
    public static final String OPENAPI_PREFIX = "OPENAPIBearer ";

    /**
     * openapi端令牌秘钥  TODO做成可自定义的
     */
    public final static String OPENAPI_SECRET = "OPENAPIzksrabcdefghijklmnopqrstuvwxyz";

    /**
     * saasToken自定义标识
     */
    public static final String SAAS_ACCESS_TOKEN = "X-Access-Token";

}
