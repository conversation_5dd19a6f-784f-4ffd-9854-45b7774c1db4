package com.zksr.common.core.utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 地图工具
 * @date 2024/9/3 15:24
 */
public class GeoUtils {

    /**
     * 判断坐标是否在围栏面里面
     * @param pointX    经度, 经度长一点
     * @param pointY    纬度, 纬度低
     * @param polygon   围栏面
     * @return
     */
    public static boolean isPointInPolygon(double pointX, double pointY, List<Point> polygon) {
        int n = polygon.size();
        boolean inside = false;

        // 闭合多边形，添加第一个点到最后
        if (n < 3) return false; // 至少需要三个点才能形成多边形
        Point p1 = polygon.get(0);
        for (int i = 1; i <= n; i++) {
            Point p2 = polygon.get(i % n);

            if (pointY > Math.min(p1.y, p2.y)) {
                if (pointY <= Math.max(p1.y, p2.y)) {
                    if (pointX <= Math.max(p1.x, p2.x)) {
                        if (p1.y != p2.y) {
                            double xints = (pointY - p1.y) * (p2.x - p1.x) / (p2.y - p1.y) + p1.x;

                            if (p1.x == p2.x || pointX <= xints) {
                                inside = !inside;
                            }
                        }
                    }
                }
            }

            p1 = p2;
        }
        return inside;
    }

    /**
     * 计算两点之间的距离（米）
     * @param point1 第一个点
     * @param point2 第二个点
     * @return 距离，单位为米
     */
    public static double calculateDistanceInMeters(Point point1, Point point2) {
        final int RADIUS_EARTH = 6371000; // 地球平均半径，单位为米
        double lat1 = Math.toRadians(point1.y);
        double lon1 = Math.toRadians(point1.x);
        double lat2 = Math.toRadians(point2.y);
        double lon2 = Math.toRadians(point2.x);

        double dlon = lon2 - lon1;
        double dlat = lat2 - lat1;
        double a = Math.pow(Math.sin(dlat / 2), 2)
                + Math.cos(lat1) * Math.cos(lat2)
                * Math.pow(Math.sin(dlon / 2), 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double distance = RADIUS_EARTH * c;
        return distance;
    }

    /**
     * 验证经纬度是否在有效范围内
     * @param lon   经度
     * @param lat   维度
     * @return true 有效
     */
    public static boolean isValidCoordinate(Double lon, Double lat) {
        if (Objects.isNull(lon) || Objects.isNull(lat)) {
            return false;
        }
        return (lon >= -180 && lon <= 180) && (lat >= -90 && lat <= 90);
    }

    /**
     * 验证经纬度是否在有效范围内
     * @param lon   经度
     * @param lat   维度
     * @return true 有效
     */
    public static boolean isValidCoordinate(BigDecimal lon, BigDecimal lat) {
        if (Objects.isNull(lon) || Objects.isNull(lat)) {
            return false;
        }
        return (lon.doubleValue() >= -180 && lon.doubleValue() <= 180) && (lat.doubleValue() >= -90 && lat.doubleValue() <= 90);
    }

    public static class Point {
        double x, y;
        public Point(double lon, double lat) {
            this.x = lon;
            this.y = lat;
        }
    }
}
