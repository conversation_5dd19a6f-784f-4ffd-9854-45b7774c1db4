package com.zksr.common.core.domain.vo.openapi.syncCall;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("获取订单信息传参实体类")
public class SyncOrderPageReqDTO extends PageParam {
    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "结束时间")
    private String endTime;
}
