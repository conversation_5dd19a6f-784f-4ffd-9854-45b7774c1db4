package com.zksr.common.core.domain.vo.account;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/14 10:12
 */
@ApiModel(description = "商户简单绑定信息")
@Data
public class PlatformSimpleBindVO {

    @ApiModelProperty(value = "商户ID", hidden = true)
    private Long merchantId;

    @ApiModelProperty(value = "商户类型", required = true)
    private MerchantTypeEnum merchantType;

    @ApiModelProperty(value = "支付平台", required = true)
    private String platform;

    @ApiModelProperty(value = "支付商户号", required = true, notes = "支付商户号", example = "E00001")
    private String altMchNo;

    /** 商户KEY */
    @ApiModelProperty("商户KEY")
    private String altMchKey;

    /**
     * 支付平台进件单号
     */
    @ApiModelProperty(value = "支付平台进件单号", required = true, notes = "支付平台进件单号", example = "2024042010100")
    private String thirdOrderNo;

    /**
     * 进件平台商户名称
     */
    @ApiModelProperty(value = "支付平台进件商户名称", required = true, notes = "支付平台进件商户名称", example = "XXX有限公司")
    private String altMchName;
}
