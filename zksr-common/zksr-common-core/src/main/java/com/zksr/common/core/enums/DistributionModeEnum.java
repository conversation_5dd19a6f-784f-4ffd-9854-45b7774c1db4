package com.zksr.common.core.enums;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

/**
 * 分销模式
 */
@Getter
public enum DistributionModeEnum {
    DICT_CODE("distribution_mode","数据字典"),
    
    O2O("O2O", "O2O");

    private final String code;
    private final String name;

    DistributionModeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public static String getValue(String key){
        if (StringUtils.isEmpty(key)){
            return  null;
        }
        DistributionModeEnum[] values = DistributionModeEnum.values();
        for(DistributionModeEnum value : values){
            if(value.getCode().equals(key)){
                return value.getName();
            }
        }
        return null;
    }
    
    public static DistributionModeEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (DistributionModeEnum enums : DistributionModeEnum.values()) {
            
            if (StringUtils.equals(enums.getName(), name)) {
                return enums;
            }
        }
        return null;
    }
    
    
    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (DistributionModeEnum enums : DistributionModeEnum.values()) {
            
            if (StringUtils.equals(enums.getName(), name)) {
                return enums.getCode();
            }
        }
        return null;
    }
} 