package com.zksr.common.core.erpUtils;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;


public class SecretUtil {

    //private static String publicKey = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIvqaGd7hWcInImx1nS8ZSiU6k3qOjkkAsQKPRMY7jcZC90VQS58FaQGUFr1YNjjjhgtbo3crF1VMgTleZint9UCAwEAAQ";

    //private static String privateKey = "MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAi-poZ3uFZwicibHWdLxlKJTqTeo6OSQCxAo9ExjuNxkL3RVBLnwVpAZQWvVg2OOOGC1ujdysXVUyBOV5mKe31QIDAQABAkAAjY2aExfk31jXBnflgDUaIvSGGE3gme7U3YF-SNPwLcymDDDzJBdym2tZBNlw1pDqhtoUffPO1ENps5ogo1XBAiEA23VhHi2doTE9pD5QjJq_pyCBS0gpPC6a4IykVAbticUCIQCjNnDtTO-qHuZelQkxdnkmImdWVH7BctuvZ2_FdJsm0QIgDLfStwMBvZOy9M-_r0ZQkQjrNUerTBePVjAQXeZA4dkCIGjlz5SksxCKxZMahIsW0uxwhiwVKFaaxJkqiMrGlAVRAiAhtAkpbeJDjwxLEEwNn6L3PsuDXEGttUG0D-Bma4CpDg";

    /**
     * 解密数据
     * @param bizData
     * @return
     * @throws InvalidKeySpecException
     * @throws NoSuchAlgorithmException
     */
    public static String decode(String bizData, String privateKey) throws InvalidKeySpecException, NoSuchAlgorithmException {
        return RSAUtils.privateDecrypt(bizData, RSAUtils.getPrivateKey(privateKey));
    }

    /**
     * 加密json数据
     *
     * @param data json data
     * @return 加密后数据
     * @throws InvalidKeySpecException  InvalidKeySpecException
     * @throws NoSuchAlgorithmException NoSuchAlgorithmException
     */
    public static String encrypt(Object data,String publicKey) throws InvalidKeySpecException, NoSuchAlgorithmException {
        return RSAUtils.publicEncrypt(JsonUtil.toJsonStr(data), RSAUtils.getPublicKey(publicKey));
    }


//    public static void main(String[] args) throws Exception {
//        ApiRequestBody body = new ApiRequestBody();
//        body.setReqId("1").setReqTime(DateUtils.dateTimeNow()).setRequestType(RequestType.CONSUMER);
//        ApiDataModel<WmsBaseConsumerDto> dataModel = new ApiDataModel<>();
//        dataModel.setType(OperationType.DELETE);
//        WmsBaseConsumerDto consumerDto = new WmsBaseConsumerDto();
//        consumerDto.setConsumerNo("111");
//        dataModel.setData(consumerDto);
//        body.setBizData(RSAUtils.privateEncrypt(JacksonUtils.toJson(dataModel), RSAUtils.getPrivateKey(privateKey)));
//        System.out.println("加密body: \r\n" + JacksonUtils.toJson(body));
//        /*ApiDataModel<ApiRequestBody> decode = SecretUtil.decode(body, ApiRequestBody.class);
//        System.out.println("解密后文字: \r\n" + JacksonUtils.toJson(decode.getData()));*/
//    }
}
