package com.zksr.common.core.enums;

import com.zksr.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
* 售后物流状态
* @date 2025/2/20 9:45
* <AUTHOR>
*/
@Getter
@AllArgsConstructor
public enum AfterLogisticsStatusEnum {
    // 配送状态
    FQSH(1, "门店发起售后", "1"),
    SJYTY(2, "商家已同意","2"),
    LOGISTCS(3, "已上传退款物流凭证","3"),
    DQRSH(11, "待确认收货", "11"),
    QRSH(12, "已确认收货","12"),
    YRK(13, "已入库","13"),
    TYTK(21, "商家同意退款","6"),
    TKCG(22, "退款成功","8"),
//    TK_FAIL(23, "退款失败", "7"),
    SUPPLIER_CANCEL(23, "拒绝售后", "9"),
    SUPPLIER_REFUSE(24, "售后取消", "10"),

    ;
    /**
     * 编码
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String name;

    private final String handleStateValue;

    public static AfterLogisticsStatusEnum getAfterLogisticsStatusEnum(Integer code){
        for (AfterLogisticsStatusEnum e : AfterLogisticsStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new ServiceException("Invalid key: " + code);
    }

    public static List<AfterLogisticsStatusEnum> getAfterLogisticsList(String handleState){
        List<AfterLogisticsStatusEnum> afterLogisticsStatusEnum = new ArrayList<>();
        for (AfterLogisticsStatusEnum e : AfterLogisticsStatusEnum.values()) {
            if (e.getHandleStateValue().contains(handleState)) {
                afterLogisticsStatusEnum.add(e);
            }
        }
        return afterLogisticsStatusEnum;
    }

    public static AfterLogisticsStatusEnum getAfterLogisticsStatusEnum(String handleState){
        for (AfterLogisticsStatusEnum e : AfterLogisticsStatusEnum.values()) {
            if (e.getHandleStateValue().equals(handleState)) {
                return e;
            }
        }
        return null;
    }
}
