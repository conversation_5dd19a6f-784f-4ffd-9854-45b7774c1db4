package com.zksr.common.core.domain.vo.openapi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* 销售订单信息同步 MQ入参
* @date 2024/7/12 10:25
* <AUTHOR>
*/
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SyncOrderSendDTO {
    /** 订单编号 */
    private String orderNo;


    /** 入驻商ID （用于获取延时发送配置） */
    private Long supplierId;


}
