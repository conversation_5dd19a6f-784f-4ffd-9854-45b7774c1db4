package com.zksr.common.core.enums;

import com.zksr.common.core.exception.ServiceException;

/**
 * MQ延时推送时间枚举
 */
public enum DelayTimeLevelEnum {
    // 枚举常量：等级和对应的秒数
    LEVEL_1(1, 1),         // 1秒
    LEVEL_2(2, 5),         // 5秒
    LEVEL_3(3, 10),        // 10秒
    LEVEL_4(4, 30),        // 30秒
    LEVEL_5(5, 60),        // 1分钟
    LEVEL_6(6, 120),       // 2分钟
    LEVEL_7(7, 180),       // 3分钟
    LEVEL_8(8, 240),       // 4分钟
    LEVEL_9(9, 300),       // 5分钟
    LEVEL_10(10, 360),     // 6分钟
    LEVEL_11(11, 420),     // 7分钟
    LEVEL_12(12, 480),     // 8分钟
    LEVEL_13(13, 540),     // 9分钟
    LEVEL_14(14, 600),     // 10分钟
    LEVEL_15(15, 1200),    // 20分钟
    LEVEL_16(16, 1800),    // 30分钟
    LEVEL_17(17, 3600),    // 1小时
    LEVEL_18(18, 7200);    // 2小时

    // 枚举字段
    private final int level;   // 等级
    private final int seconds; // 对应的秒数

    // 构造方法
    DelayTimeLevelEnum(int level, int seconds) {
        this.level = level;
        this.seconds = seconds;
    }

    // 获取延时等级
    public int getLevel() {
        return level;
    }

    // 获取对应的秒数
    public int getSeconds() {
        return seconds;
    }

    // 根据等级获取枚举实例
    public static DelayTimeLevelEnum fromLevel(int level) {
        for (DelayTimeLevelEnum delayTimeLevel : DelayTimeLevelEnum.values()) {
            if (delayTimeLevel.getLevel() == level) {
                return delayTimeLevel;
            }
        }
        throw new ServiceException("无效的延时消息等级: " + level);
    }

    // 根据等级获取对应的秒数
    public static int getSecondsByLevel(int level) {
        return fromLevel(level).getSeconds();
    }
}