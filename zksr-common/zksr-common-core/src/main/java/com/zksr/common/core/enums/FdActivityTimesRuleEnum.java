package com.zksr.common.core.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 活动次数限制规则枚举 （字典：fd_activity_times_rule）
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/05 9:51
 */
@Getter
public enum FdActivityTimesRuleEnum {
    RULE_LEVEL_NO(-1,"类型错误"),
    RULE_LEVEL_0(0,"每日一次"),
    ;

    @Getter
    private Integer type;
    @Getter
    private String name;
    FdActivityTimesRuleEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * @param timesRule 活动次数限制规则类型
     * @return 每日一次
     */
    public static boolean isRuleLevel0(Integer timesRule) {
        return Objects.equals(timesRule, RULE_LEVEL_0.getType());
    }

    public static FdActivityTimesRuleEnum formValue(Integer type) {
        for (FdActivityTimesRuleEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return FdActivityTimesRuleEnum.RULE_LEVEL_NO;
    }
}
