package com.zksr.common.core.enums.branch;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* 门店生命周期类型枚举
* @date 2025/3/7 15:59
* <AUTHOR>
*/
@Getter
@AllArgsConstructor
public enum BranchLifecycleTypeEnum {
    NEW_CUSTOMER(1, "新店"),
    ACTIVE(2, "活跃"),
    SILENT(3, "沉默"),
    LOST(4, "流失"),

    PUBLIC_SEA(999, "公海"),
    ;
    private final Integer type;
    private final String name;

    public static String getNameByType(Integer type) {
        for (BranchLifecycleTypeEnum value : BranchLifecycleTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getName();
            }
        }
        return null;
    }
}
