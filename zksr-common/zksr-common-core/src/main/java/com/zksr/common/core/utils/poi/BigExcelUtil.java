package com.zksr.common.core.utils.poi;

import cn.hutool.poi.excel.BigExcelWriter;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.text.Convert;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.reflect.ReflectUtils;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RegExUtils;
import org.apache.poi.ss.usermodel.DateUtil;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@SuppressWarnings("all")
@Slf4j
public class BigExcelUtil<T extends BaseEntity> extends ExcelUtil<T> {

    /**
     * 导出类型（EXPORT:导出数据；IMPORT：导入模板）
     */
    public Excel.Type type;

    public BigExcelUtil(Class<T> clazz) {
        super(clazz);
    }

    /**
     * 最小最大键ID 导出, 效率高
     * @param response
     * @param sheetName
     * @param callback
     */
    public void exportExcelFunMinId(HttpServletResponse response, String sheetName, Callback<Long, List<T>> callback) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        BigExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getBigWriter();
        writer.setOnlyAlias(true);
        List<Object[]> fields = this.getFields();
        fields = fields.stream().sorted(Comparator.comparing(objects -> ((Excel) objects[1]).sort())).collect(Collectors.toList());
        for (Object[] field : fields) {
            Field field1 = (Field) field[0];
            Excel excel = (Excel) field[1];
            writer.addHeaderAlias(field1.getName(), excel.name());
        }
        Long minId = -1L;
        for (;;) {
            List<T> dataList = callback.call(minId);
            if (Objects.nonNull(dataList)) {
                try {
                    writer.write(importExcel(dataList));
                } catch (Exception e) {
                    log.error(" BigExcelUtil.exportExcelFunMinId失败1，", e);
                }
            }
            if (dataList.size() < 10000) {
                break;
            }
            //minId = dataList.get(dataList.size() - 1).getMinExportId();
        }
        try {
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((sheetName+ ".xlsx").getBytes(), "iso-8859-1"));
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            writer.close();
        } catch (Exception e) {
            log.error(" BigExcelUtil.exportExcelFunMinId失败2，", e);
        }
    }

    /**
     * 针对无主键排序, 自定义排序采用分页导出
     * @param response
     * @param sheetName
     * @param callback
     */
    /*public void exportExcelFunLimit(HttpServletResponse response, String sheetName, Supplier<List<T>> supplier) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        BigExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getBigWriter();
        writer.setOnlyAlias(true);
        List<Object[]> fields = this.getFields();
        fields = fields.stream().sorted(Comparator.comparing(objects -> ((Excel) objects[1]).sort())).collect(Collectors.toList());
        for (Object[] field : fields) {
            Field field1 = (Field) field[0];
            Excel excel = (Excel) field[1];
            writer.addHeaderAlias(field1.getName(), excel.name());
        }
        int page = 1;
        for (;;) {
            PageHelper.startPage(page, 10000, false);
            List<T> dataList = supplier.get();
            if (Objects.nonNull(dataList)) {
                try {
                    writer.write(importExcel(dataList));
                } catch (Exception e) {

                }
            }
            if (dataList.size() < 10000) {
                break;
            }
            page++;
        }
        try {
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((sheetName+ ".xlsx").getBytes(), "iso-8859-1"));
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            writer.close();
        } catch (Exception e) {

        }
    }*/

    /**
     * 采用传统分页导出数据
     * @param savePath      临时存储目录
     * @param sheetName     页名称
     * @param supplier      数据提供
     * @return
     */
    public long exportExcelFunLimit(String savePath, String sheetName, ExportSupplier<List<T>> supplier) {
        BigExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getBigWriter(savePath);
        writer.setOnlyAlias(true);
        List<Object[]> fields = this.getFields();
        fields = fields.stream().sorted(Comparator.comparing(objects -> ((Excel) objects[1]).sort())).collect(Collectors.toList());
        for (Object[] field : fields) {
            Field field1 = (Field) field[0];
            Excel excel = (Excel) field[1];
            writer.addHeaderAlias(field1.getName(), excel.name());
        }
        return exportExcelFunLimit(writer, savePath, sheetName, supplier);
    }

    /**
     * 采用传统分页导出数据
     * @param writer        导出实现类
     * @param savePath      临时存储目录
     * @param sheetName     页名称
     * @param supplier      数据提供
     * @return
     */
    public long exportExcelFunLimit(BigExcelWriter writer, String savePath, String sheetName, ExportSupplier<List<T>> supplier) {
        int page = 1;
        int rows = 5000;
        long total = 0;
        for (;;) {
            Page<Object> startPage = PageHelper.startPage(page, rows, page == 1);
            List<T> dataList = supplier.get(writer);
            if (Objects.nonNull(dataList)) {
                try {
                    writer.write(importExcel(dataList));
                } catch (Exception e) {
                    log.error(" BigExcelUtil.exportExcelFunLimit1，", e);
                }
            }
            // 小于1w 是到头了, 大于1w是没受分页控制, 总数等于0 是分页未生效
            if (Objects.isNull(dataList) || dataList.size() < rows || dataList.size() > rows || startPage.getTotal() == 0L) {
                break;
            }
            if (page == 1) {
                total = startPage.getTotal();
            }
            page++;
        }
        try {
            writer.flush();
            writer.close();
        } catch (Exception e) {
            log.error(" BigExcelUtil.exportExcelFunLimit2，", e);
        }
        return total;
    }

    /**
     * 对excel表单指定表格索引名转换成list
     * @return 转换后集合
     */
    public List<Map> importExcel(List<T> sourceList) throws Exception
    {
        this.type = Excel.Type.IMPORT;
        List<Map> list = new ArrayList<Map>();
        if (sourceList.size() > 0) {
            for (T entity : sourceList) {
                Map<String, Object> rowData = new HashMap<>();
                for (Object[] entry : this.getFields())
                {
                    // 如果不存在实例则新建.
                    entity = (entity == null ? clazz.newInstance() : entity);
                    // 从map中得到对应列的field.
                    Field field = (Field) entry[0];
                    Excel attr = (Excel) entry[1];
                    Object val = ReflectUtils.invokeGetter(entity, field.getName());
                    // 取得类型,并根据对象类型设置值.
                    Class<?> fieldType = field.getType();
                    if (String.class == fieldType)
                    {
                        String s = Convert.toStr(val);
                        if (StringUtils.endsWith(s, ".0"))
                        {
                            val = StringUtils.substringBefore(s, ".0");
                        }
                        else
                        {
                            String dateFormat = field.getAnnotation(Excel.class).dateFormat();
                            if (StringUtils.isNotEmpty(dateFormat))
                            {
                                val = parseDateToStr(dateFormat, val);
                            }
                            else
                            {
                                val = Convert.toStr(val);
                            }
                        }
                    }
                    else if ((Integer.TYPE == fieldType || Integer.class == fieldType) && StringUtils.isNumeric(Convert.toStr(val)))
                    {
                        val = Convert.toInt(val);
                    }
                    else if ((Long.TYPE == fieldType || Long.class == fieldType) && StringUtils.isNumeric(Convert.toStr(val)))
                    {
                        val = Convert.toLong(val);
                    }
                    else if (Double.TYPE == fieldType || Double.class == fieldType)
                    {
                        val = Convert.toDouble(val);
                    }
                    else if (Float.TYPE == fieldType || Float.class == fieldType)
                    {
                        val = Convert.toFloat(val);
                    }
                    else if (BigDecimal.class == fieldType)
                    {
                        if (Objects.nonNull(val)) {
                            val = Convert.toBigDecimal(val).doubleValue();
                        }
                    }
                    else if (Date.class == fieldType)
                    {
                        if (val instanceof String)
                        {
                            val = DateUtils.parseDate(val);
                        }
                        if (val instanceof Date)
                        {
                            String dateFormat = field.getAnnotation(Excel.class).dateFormat();
                            if (StringUtils.isNotEmpty(dateFormat))
                            {
                                val = parseDateToStr(dateFormat, val);
                            } else {
                                val = parseDateToStr("yyyy-MM-dd HH:mm:ss", val);
                            }
                        }
                        else if (val instanceof Double)
                        {
                            val = DateUtil.getJavaDate((Double) val);
                        }
                    }
                    else if (Boolean.TYPE == fieldType || Boolean.class == fieldType)
                    {
                        val = Convert.toBool(val, false);
                    }
                    if (StringUtils.isNotNull(fieldType))
                    {
                        String propertyName = field.getName();
                        if (StringUtils.isNotEmpty(attr.targetAttr()))
                        {
                            propertyName = field.getName() + "." + attr.targetAttr();
                        }
                        else if (StringUtils.isNotEmpty(attr.readConverterExp()))
                        {
                            val = reverseByExp(Convert.toStr(val), attr.readConverterExp(), attr.separator());
                        }
                        else if (!attr.handler().equals(ExcelHandlerAdapter.class))
                        {
                            val = dataFormatHandlerAdapter(val, attr);
                        }
                        //ReflectUtils.invokeSetter(entity, propertyName, val);
                    }
                    Object cellVo = setCellVo(val, attr);
                    rowData.put(field.getName(), cellVo);
                }
                list.add(rowData);
            }
        }
        return list;
    }

    /**
     * 设置单元格信息
     *
     * @param value 单元格值
     * @param attr 注解相关
     * @param cell 单元格信息
     */
    public Object setCellVo(Object value, Excel attr)
    {
        if (Excel.ColumnType.STRING == attr.cellType())
        {
            String cellValue = Convert.toStr(value);
            // 对于任何以表达式触发字符 =-+@开头的单元格，直接使用tab字符作为前缀，防止CSV注入。
            if (StringUtils.startsWithAny(cellValue, FORMULA_STR))
            {
                cellValue = RegExUtils.replaceFirst(cellValue, FORMULA_REGEX_STR, "\t$0");
            }
            if (value instanceof Collection && StringUtils.equals("[]", cellValue))
            {
                cellValue = StringUtils.EMPTY;
            }
            return StringUtils.isNull(cellValue) ? attr.defaultValue() : cellValue + attr.suffix();
        }
        else if (Excel.ColumnType.NUMERIC == attr.cellType())
        {
            if (StringUtils.isNotNull(value) && !"".equals(value))
            {
                if (value instanceof Integer) {
                    return value;
                }
                if (value instanceof Long) {
                    return value;
                }
                if (StringUtils.contains(Convert.toStr(value), ".")) {
                    return Convert.toDouble(value);
                } else {
                    return Convert.toStr(value);
                }
            }
        }
        else if (Excel.ColumnType.IMAGE == attr.cellType())
        {
            /*ClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) cell.getColumnIndex(), cell.getRow().getRowNum(), (short) (cell.getColumnIndex() + 1), cell.getRow().getRowNum() + 1);
            String imagePath = Convert.toStr(value);
            if (StringUtils.isNotEmpty(imagePath))
            {
                byte[] data = ImageUtils.getImage(imagePath);
                getDrawingPatriarch(cell.getSheet()).createPicture(anchor,
                        cell.getSheet().getWorkbook().addPicture(data, getImageType(data)));
            }*/
        }
        return value;
    }
}
