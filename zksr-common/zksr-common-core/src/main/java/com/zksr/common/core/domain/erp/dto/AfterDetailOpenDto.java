package com.zksr.common.core.domain.erp.dto;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.BigDecimalSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AfterDetailOpenDto {

    private String itemNo;

    private BigDecimal detailQty;

    private String remake;

    private String externalLineNo;
    /** 产品单价(最小单位的销售价) */
    private BigDecimal itemPrice;
    /** 商品包装(0 小包装 1 中包装  2 大包装) */
    private String packageType;
    /** 商品实际数量 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal realQty;
    /** 最小单位数量 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal minDetailQty;
    /** 商品总金额 */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal detailPrice;
    /** 下单类型 0 正常品 1 赠品 2 陈列兑付  */
    private String itemType;
    /** 商品条码 */
    private String itemBarcode;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @ApiModelProperty(value = "快递公司编码")
    private String expressCom;
}
