package com.zksr.common.core.utils.http;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.zksr.common.core.enums.MideaApiRespEnum;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.signature.MideaPayApiSignatureUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * @Description: 美的API HTTP客户端
 * @Date: 2025/07/16
 */
@Slf4j
public class MideaApiHttpClient {

    private final RestTemplate restTemplate;
    private final String merchantId;
    private final String appId;
    private final String version;
    private final String privateKey;
    private final String publicKey;
    private final String baseUrl;

    public MideaApiHttpClient(RestTemplate restTemplate, String merchantId, String appId, String version,
                             String privateKey, String publicKey, String baseUrl) {
        this.restTemplate = restTemplate;
        this.merchantId = merchantId;
        this.appId = appId;
        this.version = version;
        this.privateKey = privateKey;
        this.publicKey = publicKey;
        this.baseUrl = baseUrl;
        
        // 验证必要参数
//        MideaApiSignatureUtils.validateSignatureParams(merchantId, appId, privateKey);
    }

    /**
     * 发送GET请求
     */
    public <T> MideaApiResponse<T> get(String uri, Map<String, String> queryParams) {
        return sendRequest(HttpMethod.GET, uri, queryParams, null);
    }

    /**
     * 发送POST请求
     */
    public <T> MideaApiResponse<T> post(String uri, Object requestBody) {
        return sendRequest(HttpMethod.POST, uri, null, requestBody);
    }

    /**
     * 发送POST请求（带查询参数）
     */
    public <T> MideaApiResponse<T> post(String uri, Map<String, String> queryParams, Object requestBody) {
        return sendRequest(HttpMethod.POST, uri, queryParams, requestBody);
    }

    /**
     * 发送文件上传POST请求
     */
    public <T> MideaApiResponse<T> postFile(String uri, Map<String, String> queryParams, MultipartFile file) {
        return sendFileRequest(HttpMethod.POST, uri, queryParams, file);
    }

    /**
     * 发送带签名的HTTP请求
     */
    private <T> MideaApiResponse<T> sendRequest(HttpMethod method, String uri, Map<String, String> queryParams,
                                              Object requestBody) {
        try {
            // 1. 生成请求ID
            String requestId = MideaPayApiSignatureUtils.generateRequestId();

            // 2. 构建完整的请求参数
            Map<String, String> fullParams = MideaPayApiSignatureUtils.buildFullRequestParams(
                    merchantId, appId, version, requestId, queryParams);

            // 3. 解析请求体
            String requestBodyStr = MideaPayApiSignatureUtils.parseRequestBodyForSignature(requestBody);

            // 4. 生成签名
            String signature = MideaPayApiSignatureUtils.generateRequestSignature(fullParams, requestBodyStr, privateKey);

            // 5. 构建请求头
            HttpHeaders headers = buildRequestHeaders(requestId, signature);

            // 6. 构建请求URL
            String fullUrl = buildRequestUrl(uri, queryParams);

            // 7. 构建请求实体
            HttpEntity<?> requestEntity = new HttpEntity<>(requestBody, headers);

            log.info("发送美的API请求: {} {}", method, fullUrl);
            log.debug("请求头: {}", headers);
            log.debug("请求体: {}", requestBodyStr);
            log.debug("签名参数: {}", fullParams);

            // 8. 发送请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(fullUrl, method, requestEntity, String.class);

            // 9. 验证响应签名
            boolean signatureValid = verifyResponseSignature(responseEntity);

            // 10. 构建响应对象
            return new MideaApiResponse<T>()
                    .setSuccess(true)
                    .setStatusCode(responseEntity.getStatusCodeValue())
                    .setHeaders(responseEntity.getHeaders())
                    .setBody(responseEntity.getBody())
                    .setSignatureValid(signatureValid)
                    .setRequestId(requestId);

        } catch (Exception e) {
            log.error("发送美的API请求失败: {} {}", method, uri, e);
            return new MideaApiResponse<T>()
                    .setSuccess(false)
                    .setErrorMessage(e.getMessage())
                    .setSignatureValid(false);
        }
    }

    /**
     * 发送文件上传请求
     */
    private <T> MideaApiResponse<T> sendFileRequest(HttpMethod method, String uri, Map<String, String> queryParams, MultipartFile file) {
        try {
            // 1. 生成请求ID
            String requestId = MideaPayApiSignatureUtils.generateRequestId();

            // 2. 构建完整的请求参数（文件上传不包含查询参数）
            Map<String, String> fullParams = MideaPayApiSignatureUtils.buildFullRequestParams(
                    merchantId, appId, version, requestId, queryParams);

            // 3. 文件上传不参与签名，使用空字符串
            String signature = MideaPayApiSignatureUtils.generateRequestSignature(fullParams, "", privateKey);

            // 4. 构建请求头（文件上传使用multipart/form-data）
            HttpHeaders headers = buildFileUploadHeaders(requestId, signature);

            // 5. 构建请求URL
            String fullUrl = buildRequestUrl(uri, queryParams);

            // 6. 构建multipart请求体
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", file.getResource());

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            log.info("发送美的API文件上传请求: {} {}", method, fullUrl);
            log.debug("文件名: {}, 文件大小: {} bytes", file.getOriginalFilename(), file.getSize());

            // 7. 发送请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(fullUrl, method, requestEntity, String.class);

            // 8. 验证响应签名
            boolean signatureValid = verifyResponseSignature(responseEntity);

            // 9. 构建响应对象
            return new MideaApiResponse<T>()
                    .setSuccess(true)
                    .setStatusCode(responseEntity.getStatusCodeValue())
                    .setHeaders(responseEntity.getHeaders())
                    .setBody(responseEntity.getBody())
                    .setSignatureValid(signatureValid)
                    .setRequestId(requestId);

        } catch (Exception e) {
            log.error("发送美的API文件上传请求失败: {} {}", method, uri, e);
            return new MideaApiResponse<T>()
                    .setSuccess(false)
                    .setErrorMessage(e.getMessage())
                    .setSignatureValid(false);
        }
    }

    /**
     * 构建请求头
     */
    private HttpHeaders buildRequestHeaders(String requestId, String signature) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("version", version);
        headers.set("request-id", requestId);
        headers.set("request-signature", signature);
        return headers;
    }

    /**
     * 构建文件上传请求头
     */
    private HttpHeaders buildFileUploadHeaders(String requestId, String signature) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.set("version", version);
        headers.set("request-id", requestId);
        headers.set("request-signature", signature);
        return headers;
    }

    /**
     * 构建请求URL
     */
    private String buildRequestUrl(String uri, Map<String, String> queryParams) {
        String url = getFullUrl(uri);
        StringBuilder fullUrl = new StringBuilder(url);

        if (queryParams != null && !queryParams.isEmpty()) {
            fullUrl.append("?");
            queryParams.entrySet().stream()
                    .filter(entry -> StringUtils.isNotBlank(entry.getKey()) && StringUtils.isNotBlank(entry.getValue()))
                    .forEach(entry -> {
                        if (fullUrl.charAt(fullUrl.length() - 1) != '?') {
                            fullUrl.append("&");
                        }
                        fullUrl.append(entry.getKey()).append("=").append(entry.getValue());
                    });
        }

        return fullUrl.toString();
    }

    /**
     * 验证响应签名
     */
    private <T> boolean verifyResponseSignature(ResponseEntity<T> responseEntity) {
        try {
            // 获取响应头中的签名
            HttpHeaders responseHeaders = responseEntity.getHeaders();
            String responseSignature = responseHeaders.getFirst("response-signature");

            // 如果没有响应签名，跳过验签
            if (StringUtils.isBlank(responseSignature)) {
                log.debug("响应头中没有签名信息，跳过验签");
                return true;
            }

            // 获取响应体
            String responseBody = responseEntity.getBody() != null ?
                    JSON.toJSONString(responseEntity.getBody()) : "";

            // 验证签名
            return MideaPayApiSignatureUtils.verifyResponseSignature(responseBody, responseSignature, publicKey);

        } catch (Exception e) {
            log.error("验证响应签名失败", e);
            return false;
        }
    }

    /**
     * 获取完整的接口URL
     */
    public String getFullUrl(String path) {
        if (baseUrl.endsWith("/") && path.startsWith("/")) {
            return baseUrl + path.substring(1);
        } else if (!baseUrl.endsWith("/") && !path.startsWith("/")) {
            return baseUrl + "/" + path;
        } else {
            return baseUrl + path;
        }
    }

    /**
     * 美的API响应对象
     */
    @Data
    @Accessors(chain = true)
    public static class MideaApiResponse<T> {
        private boolean success;
        private int statusCode;
        private HttpHeaders headers;
        private String body;
        private boolean signatureValid;
        private String errorMessage;
        private String requestId;

        public boolean isOk() {
            return success && statusCode >= 200 && statusCode < 300;
        }

        public boolean isSignatureValid() {
            return signatureValid;
        }

        /**
         * 解析美的API标准响应
         */
        public MideaStandardResponse<T> parseStandardResponse() {
            try {
                return JSON.parseObject(body, new TypeReference<MideaStandardResponse<T>>() {});
            } catch (Exception e) {
                log.error("解析美的API标准响应失败", e);
                return null;
            }
        }
    }

    /**
     * 美的API标准响应格式
     */
    @Data
    public static class MideaStandardResponse<T> {
        private String code;
        private String message;
        private T data;

        public boolean isSuccess() {
            return MideaApiRespEnum.SUCCESS.getCode().equals(code);
        }

        public boolean isFailure() {
            return MideaApiRespEnum.FAIL.getCode().equals(code);
        }

        public boolean isDuplicateRequest() {
            return MideaApiRespEnum.DUPLICATE_REQUEST.getCode().equals(code);
        }

        public boolean isParameterError() {
            return MideaApiRespEnum.PARAMETER_ERROR.getCode().equals(code) || MideaApiRespEnum.PARAMETER_ERROR_2.getCode().equals(code);
        }

        public boolean isQueryFailure() {
            return MideaApiRespEnum.QUERY_FAILURE.getCode().equals(code);
        }

        public boolean isInternalSystemError() {
            return MideaApiRespEnum.INTERNAL_SYSTEM_ERROR.getCode().equals(code);
        }

        public boolean isSignatureVerificationFailure() {
            return MideaApiRespEnum.SIGNATURE_VERIFICATION_FAILURE.getCode().equals(code);
        }
    }

    /**
     * 构建器模式创建客户端
     */
    public static class Builder {
        private RestTemplate restTemplate;
        private String merchantId;
        private String appId;
        private String version = "1.0";
        private String privateKey;
        private String publicKey;
        private String baseUrl;

        public Builder restTemplate(RestTemplate restTemplate) {
            this.restTemplate = restTemplate;
            return this;
        }

        public Builder merchantId(String merchantId) {
            this.merchantId = merchantId;
            return this;
        }

        public Builder appId(String appId) {
            this.appId = appId;
            return this;
        }

        public Builder version(String version) {
            this.version = version;
            return this;
        }

        public Builder privateKey(String privateKey) {
            this.privateKey = privateKey;
            return this;
        }

        public Builder publicKey(String publicKey) {
            this.publicKey = publicKey;
            return this;
        }

        public Builder baseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
            return this;
        }

        public MideaApiHttpClient build() {
            if (restTemplate == null) {
                restTemplate = new RestTemplate();
            }
            return new MideaApiHttpClient(restTemplate, merchantId, appId, version, privateKey, publicKey, baseUrl);
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
