package com.zksr.common.core.utils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
    public static final String MAX_DATE = "2199-01-01";

    public static final String YYYY = "yyyy";

    public static final String TIMEZONE = "Asia/Shanghai";

    public static final String YYYYMM = "yyyyMM";

    public static final String YYYY_MM = "yyyy-MM";

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";

    public static final String DATETIME_MAX = "2099-12-31 23:59:59";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM",
            "yyyy-MM-dd HH:mm:ss.SSS"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate()
    {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate()
    {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime()
    {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow()
    {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format)
    {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date)
    {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date)
    {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts)
    {
        try
        {
            return new SimpleDateFormat(format).parse(ts);
        }
        catch (ParseException e)
        {
            log.error(" dateTime异常,", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate()
    {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算时间差
     *
     * @param endDate 最后时间
     * @param startTime 开始时间
     * @return 时间差（天/小时/分钟）
     */
    public static String timeDistance(Date endDate, Date startTime)
    {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startTime.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor)
    {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor)
    {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /** 当前日期增加天数 */
    public static Date getDateAdd(int addDays){
        LocalDate currentDate = LocalDate.now();
        LocalDate newDate = currentDate.plusDays(addDays);
        //使用LocalDate.atTime方法将日期格式从LocalDate格式转为Date格式
        Instant instant = newDate.atTime(LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);

    }

    /** 某个日期增加天数 */
    public static Date getDateAddByDate(Date date,int addDays){
        // 显式指定时区，确保一致性
        ZoneId zoneId = ZoneId.of("UTC");

        // 转换并增加天数
        LocalDate localDate = date.toInstant().atZone(zoneId).toLocalDate();
        LocalDate newDate = localDate.plusDays(addDays);

        // 转换回 Date 对象
        Instant instant = newDate.atStartOfDay(zoneId).toInstant();
        return Date.from(instant);

    }

    /**
     * 当前日期增加时间（秒）
     * @param date 日期
     * @param second 增加的秒数
     * @return 返回增加秒数后的日期
     */
    public static Date getDateAddSecond(Date date, int second){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.SECOND, second);
        return calendar.getTime();
    }

    /**
     * 当前时间增加时间（秒）
     * @param second 增加的秒数
     * @return 返回增加秒数后的日期
     */
    public static Date getDateAddSecondByNow(int second){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.SECOND, second);
        return calendar.getTime();
    }


    public static Date getDateScopeByMonth(int months){
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, months);
        return c.getTime();
    }

    public static Date getDateScopeByYear(int year){
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.YEAR, year);
        return c.getTime();
    }

    /**
     * 获取系统当前时间
     * 时间格式：yyyy-MM-dd HH:mm:ss.SSS
     * @return
     */
    public static String getSystemDate(){
        // 获取当前时间（精确到纳秒）
        Instant now = Instant.now();

        // 创建一个日期时间格式器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS_SSS);

        // 设置时区，例如中国的北京时间（ZoneId.of("Asia/Shanghai")）
        ZoneId zoneId = ZoneId.systemDefault();// 如果不需要转换时区，可以直接使用系统默认

        // 将Instant转换为带有时区的ZonedDateTime，然后格式化
        String formattedDateTime = now.atZone(zoneId).format(formatter);

        return formattedDateTime;
    }

    /**
     * 获取当天结束的剩余秒数
     * @param currentDate
     * @return
     */
    public static long getRemainSecondsOneDay(Date currentDate) {
        LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
        return ChronoUnit.SECONDS.between(currentDateTime, midnight);
    }

    /**
     * 获取当前日期的最后时间
     * @return 返回增加后的时间
     */
    public static Date getDayEndDate(){
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 获取当前日期的开始时间
     * @return 返回增加后的时间
     */
    public static Date getDayStartDate(){
        // 获取当前日期的开始时间，并指定时区为系统默认时区
        ZonedDateTime startOfDay = LocalDate.now()
                .atStartOfDay(ZoneId.systemDefault());

        // 将 ZonedDateTime 转换为 Date 对象
        return Date.from(startOfDay.toInstant());
    }

    /**
     * 获取两个时间内相差的秒数
     * @param startTime 开始时间
     * @param startTime 结束时间
     * @return
     */
    public static long getRemainSecond(Date startTime, Date endTime) {
        // 开始时间
        LocalDateTime timeStart = LocalDateTime.ofInstant(startTime.toInstant(), ZoneId.systemDefault());
        // 结束时间
        LocalDateTime timeEnd = LocalDateTime.ofInstant(endTime.toInstant(), ZoneId.systemDefault());

        // 计算剩余时间，并转换为秒
        return ChronoUnit.SECONDS.between(timeStart, timeEnd);
    }

    /**
     * 获取两个时间内相差的天/时/分/秒
     * @param startTime 开始时间
     * @param startTime 结束时间
     * @return
     */
    public static long getRemain(Date startTime, Date endTime,ChronoUnit chronoUnit) {
        // 开始时间
        LocalDateTime timeStart = LocalDateTime.ofInstant(startTime.toInstant(), ZoneId.systemDefault());
        // 结束时间
        LocalDateTime timeEnd = LocalDateTime.ofInstant(endTime.toInstant(), ZoneId.systemDefault());

        // 计算剩余时间，并转换为秒
        return chronoUnit.between(timeStart, timeEnd);
    }

    /**
     * 判断字符是否是时间格式
     * @param input
     * @return
     */
    public static boolean isTimeString(String input, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        dateFormat.setLenient(false);
        try {
            dateFormat.parse(input);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 根据时间区间 获取区间内年月列表
     * @param startDate 开始时间
     * @param endDate   结算时间
     * @return
     */
    public static List<String> getMonthRange(String startDate, String endDate) {
        String [] startDateArray = startDate.split("-");
        String [] endDateArray = endDate.split("-");
        LocalDate startLocalDate = LocalDate.of(Integer.parseInt(startDateArray[0]),Integer.parseInt(startDateArray[1]),Integer.parseInt(startDateArray[2]));
        LocalDate endLocalDate = LocalDate.of(Integer.parseInt(endDateArray[0]),Integer.parseInt(endDateArray[1]), Integer.parseInt(endDateArray[2]));

        List<String> monthList = new ArrayList<>();
        YearMonth startYearMonth = YearMonth.from(startLocalDate);
        YearMonth endYearMonth = YearMonth.from(endLocalDate);
        YearMonth currentYearMonth = startYearMonth;
        while (!currentYearMonth.isAfter(endYearMonth)) {
            monthList.add(currentYearMonth.format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM)));
            currentYearMonth = currentYearMonth.plusMonths(1);
        }
        return monthList;
    }


    /**
     *  得到当前日期往前或往后时间段的年月日期 (yyyy-MM)
     * @param num
     * @return
     */
    public static String getYearMonthRange(Integer num, Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, num);
        int year = calendar.get(Calendar.YEAR);
        // 获取当前月份，从0开始，所以需要加1
        int month = calendar.get(Calendar.MONTH) + 1;
        return year + "-" + (month > 9 ? month : "0" + month);
    }

    /**
     * 判断两个日期是否相等
     * @param date1
     * @param date2
     * @return
     */
    public static boolean dateIsEquals(String date1, String date2){
        if(StringUtils.isNotBlank(date1) && StringUtils.isNotBlank(date2)){
            Date d1 = DateUtils.parseDate(date1);
            Date d2 = DateUtils.parseDate(date2);
            if(d1 == null || d2 == null){
                return false;
            }
            return d1.equals(d2);
        }
        return false;
    }

    /**
     * 获取当月还剩多少秒
     * @return
     */
    public static long getRemainSecondThisMonth() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 获取本月最后一天
        LocalDate lastDayOfMonth = now.toLocalDate().withDayOfMonth(now.toLocalDate().lengthOfMonth());
        LocalDateTime endOfMonth = lastDayOfMonth.atTime(23, 59, 59);

        // 计算剩余时间（秒）
        long remainingSeconds = ChronoUnit.SECONDS.between(now, endOfMonth);
        return remainingSeconds;
    }


    /**
     *  得到当前日期月 第一天
     * @param dateTime
     * @return
     */
    public static String getMonthFirstDay(String dateTime) {
        int year = Integer.parseInt(dateTime.split("-")[0]);
        int month = Integer.parseInt(dateTime.split("-")[1]);
        return LocalDate.of(year, month, 1).toString();
    }

    /**
     *  得到当前日期月 最后天
     * @param dateTime
     * @return
     */
    public static String getMonthLastDay(String dateTime) {
        int year = Integer.parseInt(dateTime.split("-")[0]);
        int month = Integer.parseInt(dateTime.split("-")[1]);
        LocalDate firstDay = LocalDate.of(year, month, 1);
        return firstDay.with(TemporalAdjusters.lastDayOfMonth()).toString();
    }

    /**
     *  验证时间是否在时间区间内
     * @param dateTime 验证时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    public static boolean isBetween(String startTime, String endTime, String dateTime) {
        // 开始时间
        Date startDate = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, startTime);
        // 结束时间
        Date endDate = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime);
        // 验证时间
        Date date = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, dateTime);
        // 获取两个时间内相差的秒数， 大于 0 则在时间区间内
        return getRemainSecond(startDate, date) > 0 && getRemainSecond(date,endDate) > 0;
    }

    /**
     * 获取距离当前日期某个差值的日期
     * @param dayCount
     * @return
     */
    public static String getDataByDayCount(Integer dayCount){
        Calendar   cal   =   Calendar.getInstance();
        cal.add(Calendar.DATE,   dayCount);//-1代表今天的日期减一即为昨天
        return new SimpleDateFormat( YYYY_MM_DD).format(cal.getTime());
    }

    /**
     * 计算截团日
     * @param cutTimeStr    截团HH:mm
     * @param nowDate       计算时间
     * @return  截团日 yyyy-MM-dd
     */
    public static String getCutDate(String cutTimeStr, Date nowDate) {
        DateTime date = DateUtil.date(nowDate);
        DateTime cutTime = DateUtil.parse(StringUtils.format("{} {}", DateUtil.formatDate(date), cutTimeStr), "yyyy-MM-dd HH:mm");
        if (cutTime.getTime() > date.getTime()) {
            // 还没有到今天的团, 那截团日就是今天
            return DateUtil.formatDate(date);
        } else {
            // 次日+1天
            return DateUtil.formatDate(DateUtil.offsetDay(date, 1));
        }
    }
}
