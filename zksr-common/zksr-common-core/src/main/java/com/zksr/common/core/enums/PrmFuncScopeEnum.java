package com.zksr.common.core.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/9/5 9:24
 */
@Getter
public enum PrmFuncScopeEnum {
    GLOBAL(1, "平台活动", "global"),
    LOCAL(2, "运营商活动", "local"),
            ;
    private Integer funcScope;
    private String name;
    private String productType;

    PrmFuncScopeEnum(Integer funcScope, String name, String productType) {
        this.funcScope = funcScope;
        this.name = name;
        this.productType = productType;
    }

    public static PrmFuncScopeEnum formValue(Integer funcScope) {
        if (Objects.isNull(funcScope)) {
            return null;
        }
        for (PrmFuncScopeEnum value : values()) {
            if (value.getFuncScope() == funcScope) {
                return value;
            }
        }
        return null;
    }
}
