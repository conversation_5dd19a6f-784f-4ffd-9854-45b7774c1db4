package com.zksr.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 通用消息场景
 * @date 2024/6/13 15:27
 */
@Getter
@AllArgsConstructor
public enum CommonMessageSceneEnum {
    NEW_ORDER_PUBLISH(0, "用户新订单通知"),
    LOCAL_DELIVERY_ORDER_PUBLISH(1, "订单配送通知(无效已作废, 重复)"),
    LOCAL_DELIVERY_ORDER(2, "门店订单发货(本地)"),
    PAY_WAIT_PAY(3, "订单待付款提醒"),
    ORDER_CANCEL(4, "订单取消通知"),
    GLOBAL_ORDER_DELIVER(6, "订单发货通知(全国)"),
    ORDER_RECEIVE(7, "订单收货通知"),
    AFTER_CREATED(8, "退货申请"),
    AFTER_AUDIT(9, "退货申请审核"),
    AFTER_REFUND_FINISH(11, "退货完成通知"),
    BRANCH_REGISTER(12, "门店注册成功"),
    BRANCH_ACTIVE(13, "订货提醒, 用户长时间未下单激活"),
    ACTIVITY_NOTIFY(14, "促销活动提醒"),
    ;
    /**
     * 场景值
     */
    private Integer scene;

    /**
     * 场景名称
     */
    private String sceneName;

    public static CommonMessageSceneEnum parseScene(Integer scene) {
        for (CommonMessageSceneEnum messageEnum : values()) {
            if (messageEnum.getScene().equals(scene)) {
                return messageEnum;
            }
        }
        return null;
    }
}
