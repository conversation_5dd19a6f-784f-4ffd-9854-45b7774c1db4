package com.zksr.common.core.domain.vo.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
* 区域门店信息初始化 请求实体
* @date 2025/1/20 11:07
* <AUTHOR>
*/
@Data
@ApiModel("区域门店信息初始化 请求实体")
@NoArgsConstructor
@AllArgsConstructor
public class MemBranchSyncReqVO {
    private static final long serialVersionUID = 1L;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @NotNull(message = "请选择初始化需要的区域信息")
    private Long areaId;

    /** 入驻商ID */
    @Excel(name = "入驻商ID")
    @ApiModelProperty(value = "入驻商ID")
    @NotNull(message = "请选择需要推送的入驻商信息")
    private Long supplierId;

}
