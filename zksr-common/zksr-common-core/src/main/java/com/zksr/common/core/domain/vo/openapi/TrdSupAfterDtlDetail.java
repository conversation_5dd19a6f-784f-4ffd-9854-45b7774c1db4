package com.zksr.common.core.domain.vo.openapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("b2b推送订单到erp的退货单详细")
public class TrdSupAfterDtlDetail {

    @ApiModelProperty(value = "商品编号")
    private String itemNo;

    @ApiModelProperty(value = "商品数量")
    private String itemQty;

    @ApiModelProperty(value = "商品金额")
    private String itemAmt;

    @ApiModelProperty(value = "商品单价")
    private String itemPrice;

    @ApiModelProperty(value = "行号")
    private String line;
}
