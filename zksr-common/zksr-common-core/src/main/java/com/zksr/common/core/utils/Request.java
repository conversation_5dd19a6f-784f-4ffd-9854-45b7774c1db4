package com.zksr.common.core.utils;

import lombok.Data;
import org.apache.http.entity.ContentType;

import java.io.Serializable;
import java.util.Map;

/**
 * Description:
 * User: hewf5
 * Date: 2021-05-11 11:10
 */
@Data
public class Request implements Serializable {
    private String url;
    private String params;
    private Map<String, String> headers;
    private ContentType contentType;

    public Request(String url, String params, Map<String, String> headers, ContentType contentType) {
        this.url = url;
        this.params = params;
        this.headers = headers;
        this.contentType = contentType;
    }
}