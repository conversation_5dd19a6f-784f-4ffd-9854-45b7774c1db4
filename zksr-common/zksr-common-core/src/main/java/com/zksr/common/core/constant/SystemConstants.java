package com.zksr.common.core.constant;

/**
 * system常量
 */
public class SystemConstants {

    /**
     * /100是系统内置的平台管理员角色,一般不允许将该角色赋给非平台账号(非平台账号指sys_code字段有值)
     */
//    public static final Long DEFAULT_PARTNER_ROLE_ID = 100L;

    public static final String FUNC_SCOPE_SOFTWARE = "software";
    public static final String FUNC_SCOPE_PARTNER = "partner";
    public static final String FUNC_SCOPE_DC = "dc";
    public static final String FUNC_SCOPE_SUPPLIER = "supplier";
    public static final String FUNC_SCOPE_COLONEL = "colonel";

    public static final String AREA_ID = "area_id";
    public static final String DC_ID = "dc_id";
    public static final String MERCHANT_ID = "merchant_id";
    public static final String SUPPLIER_ID = "supplier_id";
    public static final String BRANCH_ID = "branch_id";

    public static final String SYSTEM_USER = "system";
}
