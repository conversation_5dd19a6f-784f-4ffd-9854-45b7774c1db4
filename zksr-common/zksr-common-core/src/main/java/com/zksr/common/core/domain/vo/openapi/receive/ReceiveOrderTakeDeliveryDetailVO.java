package com.zksr.common.core.domain.vo.openapi.receive;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 *
 * OpenAPI 接收拒收退货单 详情
 * <AUTHOR>
 * @date 2024/6/9 11:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("接收拒收退货单详情实体")
public class ReceiveOrderTakeDeliveryDetailVO {

    /** 入驻商销售订单行号 */
    @ApiModelProperty(value = "入驻商销售订单行号")
    @NotNull(message = "入驻商销售订单行号不能为空")
    private Long lineNum;

    /** ERP商品编号 */
    @Excel(name = "ERP商品编号")
    @ApiModelProperty(value = "ERP商品编号")
    @NotNull(message = "ERP商品编号不能为空")
    private String erpItemNo;

    /** 拒收数量 */
    @Excel(name = "拒收数量")
    @ApiModelProperty(value = "拒收数量")
    private BigDecimal rejectableQty = BigDecimal.ZERO;

    /** 收货数量 */
    @Excel(name = "收货数量")
    @ApiModelProperty(value = "收货数量")
    @NotNull(message = "订单收货数量不能为空")
    private BigDecimal receiveQty;
}
