package com.zksr.common.core.enums;

/**
 * @Author: chenyj8
 * @Desciption: 美的付支付类型
 */
public enum MideaPayBarCodeEnum {
    SCAN_CODE("SCAN_CODE", "扫码"),
    OFFICIAL_ACCT("OFFICIAL_ACCT", "公众号/服务窗"),
    MINI_PROGRAM("MINI_PROGRAM", "小程序"),
    APP("APP", "app"),
    H5("H5", "H5支付"),
    ;


    private String value;

    private String desc;

    MideaPayBarCodeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
