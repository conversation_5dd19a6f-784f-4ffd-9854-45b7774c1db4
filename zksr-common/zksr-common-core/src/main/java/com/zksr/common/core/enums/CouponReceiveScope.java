package com.zksr.common.core.enums;

import lombok.Getter;

@Getter
public enum CouponReceiveScope {
    NONE(-1, "无"),
    ALL(0, "全部可领取"),
    CHANNEL(1, "指定渠道"),
    AREA(2, "指定城市"),
    BRANCH(3, "指定门店"),
    ;
    private Integer scope;
    private String name;

    CouponReceiveScope(Integer scope, String name) {
        this.scope = scope;
        this.name = name;
    }

    public static CouponReceiveScope formValue(Integer scope) {
        for (CouponReceiveScope value : values()) {
            if (value.getScope().equals(scope)) {
                return value;
            }
        }
        return CouponReceiveScope.NONE;
    }
}
