package com.zksr.common.core.enums;

import com.zksr.common.core.utils.ToolUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum TrdDiscountTypeEnum {
    COUPON("COUPON", "优惠劵"),
    BG("BG","买赠"),
    FG("FG","满赠"),
    FD("FD","满减"),
    SP("SP","特价"),
    SK("SK","秒杀"),
    CB("CB","组合促销"),
    ;

    @Getter
    private String type;
    @Getter
    private String name;

    /**
     * 根据类型获取优惠类型名称
     * @param type
     * @return
     */
    public static String getDiscountTypeName(String type){
        if (ToolUtil.isEmpty(type)){
            return "优惠类型匹配失败";
        }

        for (TrdDiscountTypeEnum value : TrdDiscountTypeEnum.values()) {
            if(value.type.equals(type)){
                return value.name;
            }
        }
        return "优惠类型匹配失败";
    }

    public static TrdDiscountTypeEnum getDiscountType(String type){
        return Stream.of(TrdDiscountTypeEnum.values())
                .filter(p -> p.type.equals(type))
                .findAny()
                .orElse(null)
                ;
    }
}
