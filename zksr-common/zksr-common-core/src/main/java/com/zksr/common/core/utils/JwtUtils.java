package com.zksr.common.core.utils;

import java.util.Map;
import java.util.regex.Pattern;

import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.constant.TokenConstants;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.text.Convert;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

/**
 * Jwt工具类
 *
 * <AUTHOR>
 */
public class JwtUtils
{
    public static String secret = TokenConstants.SECRET;

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    public static String createToken(Map<String, Object> claims)
    {
        String token = Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.HS512, secret).compact();
        return token;
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    public static Claims parseToken(String token)
    {
        return Jwts.parser().setSigningKey(secret).parseClaimsJws(token).getBody();
    }

    /**
     * 根据令牌获取用户标识
     *
     * @param token 令牌
     * @return 用户ID
     */
    public static String getUserKey(String token) {
        Claims claims = parseToken(token);
        return getValue(claims, SecurityConstants.USER_KEY);
    }

    /**
     * 根据令牌获取用户标识
     *
     * @param claims 身份信息
     * @return 用户ID
     */
    public static String getUserKey(Claims claims)
    {
        return getValue(claims, SecurityConstants.USER_KEY);
    }

    /**
     * 根据令牌获取用户ID
     *
     * @param token 令牌
     * @return 用户ID
     */
    public static String getUserId(String token)
    {
        Claims claims = parseToken(token);
        return getValue(claims, SecurityConstants.DETAILS_USER_ID);
    }

    /**
     * 根据身份信息获取用户ID
     *
     * @param claims 身份信息
     * @return 用户ID
     */
    public static String getUserId(Claims claims)
    {
        return getValue(claims, SecurityConstants.DETAILS_USER_ID);
    }

    /**
     * 根据令牌获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public static String getUserName(String token)
    {
        Claims claims = parseToken(token);
        return getValue(claims, SecurityConstants.DETAILS_USERNAME);
    }

    /**
     * 根据身份信息获取用户名
     *
     * @param claims 身份信息
     * @return 用户名
     */
    public static String getUserName(Claims claims)
    {
        return getValue(claims, SecurityConstants.DETAILS_USERNAME);
    }


    /**
     * 根据令牌获取平台商id
     *
     * @param token 令牌
     * @return 用户名
     */
    public static String getSysCode(String token) {
        Claims claims = parseToken(token);
        return getValue(claims, SecurityConstants.SYS_CODE);
    }

    /**
     * 根据身份信息获取平台商id
     *
     * @param claims 身份信息
     * @return 用户名
     */
    public static String getSysCode(Claims claims) {
        return getValue(claims, SecurityConstants.SYS_CODE);
    }

    /**
     * 根据身份信息获取运营商id
     *
     * @param claims 身份信息
     * @return 用户名
     */
    public static String getDcId(Claims claims) {
        return getValue(claims, SecurityConstants.DC_ID);
    }

    /**
     * 根据身份信息获取键值
     *
     * @param claims 身份信息
     * @param key 键
     * @return 值
     */
    public static String getValue(Claims claims, String key)
    {
        return Convert.toStr(claims.get(key), "");
    }

    /**
     * 密码设置校验
     * @param password
     */
    public static void passwordCheck(String password){
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            throw new ServiceException("密码长度必须在8到16个字符之间");
        }
        if (!Pattern.matches(UserConstants.PASSWORD_PATTERN, password)) {
            throw new ServiceException("必须包含至少一个字母、一个数字和一个特殊字符，长度在8到16个字符之间");
        }
    }

    /**
     * 密码设置校验提示，适用导入账户
     * @param password
     */
    public static String passwordCheckByTips(String password){
        StringBuilder tips = new StringBuilder("");
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            tips.append("密码长度必须在8到16个字符之间.");
        }
        if (!Pattern.matches(UserConstants.PASSWORD_PATTERN, password)) {
            tips.append("必须包含至少一个字母、一个数字和一个特殊字符，长度在8到16个字符之间");
        }
        return tips.toString();
    }
}
