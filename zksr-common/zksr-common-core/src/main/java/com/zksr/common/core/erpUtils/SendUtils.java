package com.zksr.common.core.erpUtils;

import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.constant.OpenApiConstants;
import com.zksr.common.core.domain.erp.ApiRequestBody;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.zksr.common.core.constant.OpenApiConstants.SUCCESS_1;

public class SendUtils {

    public static final int TIME_OUT = 1000 * 100;
    public static String ERP_URL = "http://localhost:8070/erp11/gateway/wms/wms_api!api.action";
    private static final Log LOGGER = LogFactory.getLog(SendUtils.class);


    public static CommonResult<String> send(ApiRequestBody<Object> data, String url, String publicKey, Map<String, Object> headerParam) throws Exception {
        data.setBizData(SecretUtil.encrypt(data.getDataModel(),publicKey));
        LOGGER.info("下发至ERP：" + JsonUtils.toJsonString(data) + "请求头信息：" + JsonUtils.toJsonString(headerParam)+ "请求地址" + url);
        return formatResult(HttpUtils.sendPost(StringUtils.isEmpty(url) ? ERP_URL : url, JsonUtils.toJsonString(data), headerParam, TIME_OUT));
    }

    @SuppressWarnings("unchecked")
    private static CommonResult<String> formatResult(String resultStr) {
        if (resultStr == null || "".equals(resultStr)) {
            return CommonResult.error(500,"无返回结果！");
        }
        //处理响应结果
        Map resultData = JSONObject.parseObject(resultStr,Map.class);
        if(OpenApiConstants.SUCCESS_0.equals(resultData.get("responseCode"))|| SUCCESS_1.equals(resultData.get("responseCode"))){
            return CommonResult.success(resultStr);
        }else{
            return CommonResult.error(500,resultStr);
        }

    }


}
