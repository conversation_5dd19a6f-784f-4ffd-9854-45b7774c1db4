package com.zksr.common.core.domain.vo.openapi.syncCall;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SyncOrderDetailCallDTO", description = "订单详情返回数据传输对象")
public class SyncOrderDetailCallDTO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("入驻商订单明细id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long supplierOrderDtlId;

    @ApiModelProperty("入驻商订单明细编号")
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    @ApiModelProperty("ERP商品编号")
    @Excel(name = "ERP商品编号")
    private String erpItemNo;

    /** 商品sku id */
    private Long skuId;

    @ApiModelProperty("商品spu 名称")
    @Excel(name = "商品spu 名称")
    private String spuName;

    @ApiModelProperty("封面图（url）")
    @Excel(name = "封面图", readConverterExp = "u=rl")
    private String thumb;

    @ApiModelProperty("封面视频（url）")
    @Excel(name = "封面视频", readConverterExp = "u=rl")
    private String thumbVideo;

    @ApiModelProperty("详情信息(富文本)")
    @Excel(name = "详情信息(富文本)")
    private String details;

    @ApiModelProperty("订单状态（数据字典）")
    @Excel(name = "*订单状态", readConverterExp = "数=据字典")
    private Long deliveryState;

    @ApiModelProperty("商品数量")
    @Excel(name = "商品数量")
    private BigDecimal totalNum;

    @ApiModelProperty("合计金额（price*total_num）")
    @Excel(name = "合计金额", readConverterExp = "p=rice*total_num")
    private BigDecimal totalAmt;

    @ApiModelProperty("商品单价")
    @Excel(name = "商品单价")
    private BigDecimal price;

    @ApiModelProperty("是否是赠品 1-是 0-否")
    @Excel(name = "是否是赠品 1-是  0-否")
    private Integer giftFlag;

    @ApiModelProperty("备注")
    @Excel(name = "备注")
    private String memo;

    @ApiModelProperty("spu规格")
    @Excel(name = "spu规格")
    private String specName;

    @ApiModelProperty("商品类型 0：全国商品 1：本地商品")
    @Excel(name = "商品类型 0：全国商品 1：本地商品")
    private Integer itemType;

    @ApiModelProperty("入驻商订单行号")
    @Excel(name = "入驻商订单行号")
    private Long lineNum;

    @ApiModelProperty("入驻商上架商品id 全国")
    @Excel(name = "入驻商上架商品id")
    private Long supplierItemId;

    @ApiModelProperty("城市上架商品id")
    @Excel(name = "城市上架商品id")
    private Long areaItemId;

    @ApiModelProperty("订单购买单位")
    @Excel(name = "订单购买单位")
    private String orderUnit;

    @ApiModelProperty("购买单位大小")
    @Excel(name = "购买单位大小")
    private Integer orderUnitType;

    @ApiModelProperty("订单购买单位数量")
    @Excel(name = "订单购买单位数量")
    private Long orderUnitQty;

    @ApiModelProperty("订单购买换算数量")
    @Excel(name = "订单购买换算数量")
    private BigDecimal orderUnitSize;

    @ApiModelProperty("订单购买单位价格")
    @Excel(name = "订单购买单位价格")
    private BigDecimal orderUnitPrice;

    @ApiModelProperty("优惠劵优惠金额(分摊的)")
    @Excel(name = "优惠劵优惠金额(分摊的)")
    private BigDecimal couponDiscountAmt;

    @ApiModelProperty("优惠劵优惠金额(不分摊的)")
    @Excel(name = "优惠劵优惠金额(不分摊的)")
    private BigDecimal couponDiscountAmt2;

    @ApiModelProperty("活动优惠金额(分摊的)")
    @Excel(name = " 活动优惠金额(分摊的)")
    private BigDecimal activityDiscountAmt;

    @ApiModelProperty("平台商品牌id")
    @Excel(name = "平台商品牌id")
    private Long brandId;

    //=========================拒收========================
//    @ApiModelProperty("拒收数量;最小单位")
//    @Excel(name = "拒收数量")
//    private Long rejectQty;
//
//    @ApiModelProperty("拒收单位;最小单位的单位，中单位的单位，大单位的单位")
//    @Excel(name = "拒收单位")
//    private Integer rejectUnit;
//
//    @ApiModelProperty("单位大小")
//    @Excel(name = "单位大小")
//    private Long rejectUnitType;
//
//    @ApiModelProperty("拒收单位数量")
//    @Excel(name = "拒收单位数量")
//    private Long rejectUnitQty;
//
//    @ApiModelProperty("拒收单位换算数量")
//    @Excel(name = "拒收单位换算数量")
//    private Long rejectUnitSize;

    @ApiModelProperty("收货是否收款 0否 1是")
    @Excel(name = "收货是否收款 0否 1是")
    private Integer isProceeds;

    @ApiModelProperty("货到付款单ID")
    @Excel(name = "货到付款单ID")
    private Long hdfkPayId;

    @ApiModelProperty("同步库存标识 0否 1是")
    @Excel(name = "同步库存标识 0否 1是")
    private Integer syncStock;

    @ApiModelProperty("管理分类Id")
    @Excel(name = "管理分类Id")
    private Long categoryId;

    /** 精准成交价（6位小数） */
    @ApiModelProperty("最小单位精准单价（6位小数）")
    @Excel(name = "最小单位精准单价")
    private BigDecimal exactPrice;

    /** 精准商品金额（6位小数） */
    @ApiModelProperty("精准商品金额（6位小数）")
    @Excel(name = "精准商品金额")
    private BigDecimal exactTotalAmt;

    /** 原销售价*/
    @ApiModelProperty("原销售价")
    @Excel(name = "原销售价")
    private BigDecimal salePrice;
}

