package com.zksr.common.core.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;

@Slf4j
public class JsonUtils {

    public static ObjectMapper getObjectMapper(){
        return ObjectMapperPool.getDefault();
    }

    public static String toJsonString(Object o) {
        if(o == null){
            return null;
        }
        ObjectMapper objectMapper = null;
        try {
            objectMapper = ObjectMapperPool.get();
            return objectMapper.writeValueAsString(o);
        }catch (JsonProcessingException e){
            //throw new RuntimeException(e);
            log.error(" json格式化失败，", e);
        }finally {
            ObjectMapperPool.close(objectMapper);
        }
        return "json格式化失败";
    }

    public static String toNoNullJsonString(Object o) {
        if(o == null){
            return null;
        }
        try {
            ObjectMapper objectMapper = ObjectMapperPool.getNoNull();
            return objectMapper.writeValueAsString(o);
        }catch (JsonProcessingException e){
            log.error(" toNoNullJsonString异常,", e);
            throw new RuntimeException(e);
        }
    }

    public static <T> T convertObject(Object origin, Class<T> valueType) {
        if(origin == null) return null;
        if(origin.getClass() == valueType){
            String str = toJsonString(origin);
            return toJavaClass(str, valueType);
        }else{
            ObjectMapper objectMapper = null;
            try {
                objectMapper = ObjectMapperPool.get();
                return objectMapper.convertValue(origin, valueType);
            }catch (Exception e){
                log.error(" convertObject异常,", e);
                throw new RuntimeException(e);
            }finally {
                ObjectMapperPool.close(objectMapper);
            }
        }
    }

    public static <T> List<T> convertList(Collection origin, Class<T> valueType) {
        if(origin == null) return null;
        if(CollectionUtils.isEmpty(origin)) return new ArrayList<>();
        ObjectMapper objectMapper = null;
        try {
            objectMapper = ObjectMapperPool.get();
            return objectMapper.convertValue(origin, getCollectionType(ArrayList.class, valueType));
        }catch (Exception e){
            log.error(" convertList异常,", e);
            throw new RuntimeException(e);
        }finally {
            ObjectMapperPool.close(objectMapper);
        }
    }

    public static <T> T toJavaClass(String s, Class<T> valueType) {
        ObjectMapper objectMapper = null;
        try {
            objectMapper = ObjectMapperPool.get();
            return objectMapper.readValue(s, valueType);
        }catch (IOException e){
            log.error("json format error", e);
            throw new RuntimeException(e);
        }finally {
            ObjectMapperPool.close(objectMapper);
        }
    }

    public static <T> T toJavaClass(Map<String, String> params, Class<T> valueType) {

        ObjectMapper objectMapper = null;
        try {
            objectMapper = ObjectMapperPool.get();
            String s = objectMapper.writeValueAsString(params);
            return objectMapper.readValue(s, valueType);
        }catch (IOException e){
            log.error("json format error", e);
            throw new RuntimeException(e);
        }finally {
            ObjectMapperPool.close(objectMapper);
        }
    }

    public static <T> T toJavaType(String s, JavaType type) {
        ObjectMapper objectMapper = null;
        try {
            objectMapper = ObjectMapperPool.get();
            return objectMapper.readValue(s, type);
        }catch (IOException e){
            log.error("json format error", e);
            throw new RuntimeException(e);
        }finally {
            ObjectMapperPool.close(objectMapper);
        }
    }

    public static <T> List<T> toJavaList(String s, Class<T> valueType){
        ObjectMapper objectMapper = null;
        try {
            objectMapper = ObjectMapperPool.get();
            return objectMapper.readValue(s, getCollectionType(ArrayList.class, valueType));
        }catch (IOException e){
            log.error(" toJavaList异常,", e);
            throw new RuntimeException(e);
        }finally {
            ObjectMapperPool.close(objectMapper);
        }
    }

    public static <T> Set<T> toJavaSet(String s, Class<T> valueType){
        ObjectMapper objectMapper = null;
        try {
            objectMapper = ObjectMapperPool.get();
            return objectMapper.readValue(s, getCollectionType(HashSet.class, valueType));
        }catch (IOException e){
            log.error(" toJavaSet,", e);
            throw new RuntimeException(e);
        }finally {
            ObjectMapperPool.close(objectMapper);
        }
    }


    public static JavaType getCollectionType(Class<?> collectionClass, Class<?>... elementClasses) {
        ObjectMapper objectMapper = null;
        try {
            objectMapper = ObjectMapperPool.get();
            return objectMapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);
        }catch (Exception e){
            log.error(" getCollectionType异常,", e);
            throw new RuntimeException(e);
        }finally {
            ObjectMapperPool.close(objectMapper);
        }
    }

    public static JavaType getJavaType(Class<?> parametrized, Class... parameterClasses) {
        ObjectMapper objectMapper = null;
        try {
            objectMapper = ObjectMapperPool.get();
            return objectMapper.getTypeFactory().constructParametricType(parametrized, parameterClasses);
        }catch (Exception e){
            log.error(" getJavaType异常,", e);
            throw new RuntimeException(e);
        }finally {
            ObjectMapperPool.close(objectMapper);
        }
    }

    public static JavaType getJavaType(Class<?> parametrized, JavaType type) {
        ObjectMapper objectMapper = null;
        try {
            objectMapper = ObjectMapperPool.get();
            return objectMapper.getTypeFactory().constructParametricType(parametrized, type);
        }catch (Exception e){
            log.error(" getJavaType异常,", e);
            throw new RuntimeException(e);
        }finally {
            ObjectMapperPool.close(objectMapper);
        }
    }

}
