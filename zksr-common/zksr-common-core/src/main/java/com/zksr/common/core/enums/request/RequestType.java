package com.zksr.common.core.enums.request;


/**
 * ERP请求类型
 *
 * <AUTHOR>
 */

public enum RequestType {


    /** ================================B2B推送给ERP==================================== */
    /**
     * EMPTY
     */
    EMPTY(0, "empty"),

    SYNC_B2B_CONSUMER(410,"接收B2B平台门店数据"),



    SYNC_B2B_ORDER(402, "发送B2B订单数据"),

    SYNC_B2B_AFTER_SHEET(406,"售后入库单"),

    SYNC_B2B_PAY(411,"发送b2b收款单"),

    SYNC_B2B_ORDER_CANCEL(557,"B2B取消订单"),

    B2B_STOCK_OCCUPY_QUERY(559, "B2B查询库存预占"),
    B2B_STOCK_OCCUPY_CREATE(560, "B2B创建库存预占"),
    B2B_STOCK_OCCUPY_ROLLBACK(561, "B2B回滚库存预占"),
    ;
    private final String info;
    private final int code;
    RequestType(int code, String info) {
        this.info = info;
        this.code = code;
    }

    public String getInfo() {
        return info;
    }

    public int getCode() {
        return code;
    }

    public static RequestType findByCode(int code) {
        for (RequestType value : RequestType.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return RequestType.EMPTY;
    }
}
