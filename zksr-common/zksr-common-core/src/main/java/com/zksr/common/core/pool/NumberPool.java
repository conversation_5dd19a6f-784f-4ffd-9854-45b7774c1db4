package com.zksr.common.core.pool;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义数字`串池
 * @date 2024/3/1 11:12
 */
public interface NumberPool {
    int LOWER_GROUND = -1;
    long LOWER_GROUND_LONG = -1L;
    long LONG_ZERO = 0L;
    long LONG_ONE = 1L;
    long LONG_TWO = 2L;
    long LONG_THREE = 3L;
    long LONG_FOUR = 4L;
    long LONG_FIVE = 5L;
    long THOUSAND = 1000L;
    long L_NUM5000 = 5000L;

    int INT_ZERO = 0;
    int INT_ONE = 1;
    int INT_TWO = 2;
    int INT_THREE = 3;
    int INT_FOUR = 4;
    int INT_SIX = 6;
    int INT_SEVEN = 7;
    int INT_NUM200 = 200;
    int INT_NUM5000 = 5000;

    int INT_EIGHTEEN = 18;

    double DOUBLE_ZERO = 0D;

    // b2b支付, 最大分账比例
    BigDecimal B2B_PAY_NAX_RATE = new BigDecimal("0.99");
    BigDecimal MAX_PRICE = new BigDecimal("999999.99");

    /**
     * 促销活动数量最大数值
     */
     BigDecimal BIGDECIMAL_MAX = new BigDecimal("999999");
     BigDecimal BIGDECIMAL_GROUND = new BigDecimal("-1");
}
