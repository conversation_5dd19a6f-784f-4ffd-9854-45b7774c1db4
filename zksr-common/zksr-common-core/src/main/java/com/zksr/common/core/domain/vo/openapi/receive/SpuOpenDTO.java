package com.zksr.common.core.domain.vo.openapi.receive;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/30 14:36
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpuOpenDTO {


    /**
     * 操作类型
     * ADD 新增 UPDATE 修改
     */
    @ApiModelProperty(value = "0 新增 1 修改", required = true)
    private Integer type;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    @Pattern(regexp = "^[a-zA-Z0-9]+$", message = "商品编码只能包含数字和英文字母，不允许特殊字符")
    private String spuNo;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String spuName;


    /**
     * 基本规格
     */
    @ApiModelProperty(value = "基本规格")
    private String specName;  //size

    /**
     * 小单位（传单位名称）
     */
    @ApiModelProperty(value = "小单位（传单位名称）")
    private String minUnit;  //unit  spu?

    /**
     * 小条码（国际条码）
     */
    @ApiModelProperty(value = "小条码（国际条码）")
    private String barcode;

    /**
     * 商品状态 启用/停用
     */
    @ApiModelProperty(value = "商品状态（启用/停用）状态 1-启用 0-停用")
    private String status;


    /**
     * 最新生产日期
     */
    @ApiModelProperty(name = "最新生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date latestDate;
    /**
     * 最旧生产日期
     */
    @ApiModelProperty(name = "最旧生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date oldestDate;

    /**
     * 保质期（默认天）
     */
    @ApiModelProperty(value = "保质期（默认天）")
    private Integer expirationDate;  //sku  spu

    /**
     * 小单位建议零售价个（价格信息暂无）
     */
    @ApiModelProperty(value = "小单位建议零售价")
    private BigDecimal suggestPrice; //sku

    /**
     * 小单位建议进货价
     */
    @ApiModelProperty(value = "小单位建议进货价")
    private BigDecimal costPrice;  //sku

    /**
     * 小单位建议标准价
     */
    @ApiModelProperty(value = "小单位建议标准价")
    private BigDecimal markPrice;  //sku


    /**
     * 中单位（传单位名称）
     */
    @ApiModelProperty(value = "中单位（传单位名称）数据字典（sys_prdt_unit）")
    private String midUnit;  //spu

    /**
     * 中单位条码
     */
    @ApiModelProperty(value = "中单位条码")
    private String midBarcode; //sku

    /**
     * 中单位换算数量（换算成最小单位）
     */
    @ApiModelProperty(name = "中单位换算数量（换算成最小单位）")
    private BigDecimal midSize;  //spu

    /**
     * 中单位建议零售价
     */
    @ApiModelProperty(value = "中单位建议零售价")
    private BigDecimal midSuggestPrice; //sku

    /**
     * 中单位建议进货价
     */
    @ApiModelProperty(value = "中单位建议进货价")
    private BigDecimal midCostPrice;  //sku

    /**
     * 中单位建议标准价
     */
    @ApiModelProperty(value = "中单位建议标准价")
    private BigDecimal midMarkPrice;  //sku


    /**
     * 大单位（传单位名称）
     */
    @ApiModelProperty(value = "大单位（传单位名称）")
    private String largeUnit; //spu

    /**
     * 大单位条码
     */
    @ApiModelProperty(value = "大单位-国际条码")
    private String largeBarcode; //sku

    /**
     * 大单位换算数量
     */
    @ApiModelProperty(value = "大单位换算数量")
    private BigDecimal largeSize;  //spu

    /**
     * 大单位建议零售价
     */
    @ApiModelProperty(value = "大单位建议零售价")
    private BigDecimal largeSuggestPrice;   //sku

    /**
     * 大单位建议进货价
     */
    @ApiModelProperty(value = "大单位-成本价(供货价)")
    private BigDecimal largeCostPrice;  //sku

    /**
     * 大单位建议标准价
     */
    @ApiModelProperty(value = "大单位建议标准价")
    private BigDecimal largeMarkPrice;  //sku


    /**
     * 外部来源编号
     */
    @ApiModelProperty(value = "外部来源资源编号")
    private String sourceNo;  //spu sku

    /**
     * ERP11.0需求 品牌：传文字，如果B2B没有此品牌 如果没有则为空
     */
    @ApiModelProperty(value = "品牌名称")
    private String brandName; //spu

    /**
     * ERP11.0需求 最末级类别:传文字，匹配B2B入驻商第三级管理类别名称，如果没有则为空
     */
    @ApiModelProperty(value = "管理类别名称")
    private String catgoryName;

    /**
     * ERP11.0 商品图片信息 没有则为空
     */
    @ApiModelProperty(value = "商品图片")
    private String picUrl;

    /**
     * ERP11.0 商品图片详情信息
     */
    @ApiModelProperty(value = "商品图片详情信息 ")
    private String images;

    /**
     * 限购
     */
    @ApiModelProperty(value = "限购")
    private Long maxOq;

    @ApiModelProperty(value = "中单位-限购")
    private Long midMaxOq;

    @ApiModelProperty(value = "大单位-限购")
    private Long largeMaxOq;

    /**
     * 起订
     */
    @ApiModelProperty(value = "起订")
    private Long minOq;

    @ApiModelProperty(value = "中单位-起订")
    private Long midMinOq;

    @ApiModelProperty(value = "大单位-起订")
    private Long largeMinOq;

    /**
     * 订货组数
     */
    @ApiModelProperty(value = "订货组数")
    private Long jumpOq;

    @ApiModelProperty(value = "中单位-订货组数")
    private Long midJumpOq;

    @ApiModelProperty(value = "大单位-订货组数")
    private Long largeJumpOq;

    /** SPU辅助的商品编号 */
    @Excel(name = "SPU辅助的商品编号")
    @ApiModelProperty(value = "SPU辅助的商品编号")
    private String auxiliarySpuNo;

    @ApiModelProperty("商品计价方式类型(字典：spu_pricing_way) 默认为普通商品 1：普通商品 2：称重商品")
    private Integer pricingWay = 1;
}
