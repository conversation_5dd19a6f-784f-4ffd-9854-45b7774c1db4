package com.zksr.common.core.domain.vo.openapi.receive;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 *  订单取消接收通知实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("订单取消接收通知实体")
@Accessors(chain = true)
public class OrderCancelReceiveCallbackVO {
    /** 订单号(入驻商订单编号) */
    @ApiModelProperty(value = "B2B入驻商销售订单编号")
    @NotNull(message = "B2B入驻商销售订单编号不能为空")
    private String supplierOrderNo;

    @ApiModelProperty(value = "取消结果", example = "0:成功, 其他:失败")
    private Integer cancelResult;

    /** 外部订单号 */
    @ApiModelProperty(value = "外部(ERP)销售订单号")
    private String sourceOrderNo;
}
