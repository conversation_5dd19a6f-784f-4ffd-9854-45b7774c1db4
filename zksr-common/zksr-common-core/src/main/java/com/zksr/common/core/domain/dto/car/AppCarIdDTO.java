package com.zksr.common.core.domain.dto.car;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>   
 * @version 1.0
 * @description: 购物车carid 定义
 * @date 2024/3/26 16:25
 */
@Data
@ApiModel(description ="购物车商品carId定义")
public class AppCarIdDTO {
    /**
     * 商品类型参见 {@link ProductType}
     */
    @ApiModelProperty(value = "local 本地商品, global 全国商品", notes = "0")
    @NotNull(message = "[type]商品类型必填")
    @NotEmpty(message = "[type]商品类型必填")
    private String type;

    @ApiModelProperty(value = "门店ID", notes = "1")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "入驻商ID", notes = "2")
    @JsonSerialize(using = CustomLongSerialize.class)
    @NotEmpty(message = "[supplierId]入驻商ID必填")
    private Long supplierId;

    @ApiModelProperty(value = "全国商品上架ID", notes = "3")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierItemId;

    @ApiModelProperty(value = "城市商品上架ID", notes = "4")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaItemId;

    @ApiModelProperty(value = "skuId", notes = "5")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    @ApiModelProperty(value = "spuId", notes = "6")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /**
     * 参见枚举 {@link com.zksr.common.core.enums.UnitTypeEnum}
     */
    @ApiModelProperty(value = "单位类型, 1-最小单位, 2-中单位, 3-大单位", notes = "7")
    private Integer unitSize = 1;

    @ApiModelProperty(value = "unit, 单位")
    private String unit = StringPool.ZERO;

    protected static List<String> fieldSort = new ArrayList<>();

    static {
        fieldSort.add("type");
        fieldSort.add("branchId");
        fieldSort.add("supplierId");
        fieldSort.add("supplierItemId");
        fieldSort.add("areaItemId");
        fieldSort.add("skuId");
        fieldSort.add("spuId");
        fieldSort.add("unitSize");
    }

    public AppCarIdDTO() {

    }

    public AppCarIdDTO(String type, Long branchId, Long supplierId, Long supplierItemId, Long areaItemId, Long skuId, Long spuId, Integer unitSize) {
        this.type = type;
        this.branchId = branchId;
        this.supplierId = supplierId;
        this.supplierItemId = supplierItemId;
        this.areaItemId = areaItemId;
        this.skuId = skuId;
        this.spuId = spuId;
        this.unitSize = unitSize;
    }

    /**
     * 解析字符串获取数据
     * @param value
     * @return
     */
    public static AppCarIdDTO build(String value) {
        String[] dataSegment = value.split(StringPool.UNDERSCORE);
        JSONObject entries = new JSONObject();
        for (int index = 0; index < dataSegment.length; index++) {
            entries.put(fieldSort.get(index), dataSegment[index]);
        }
        return entries.toBean(AppCarIdDTO.class);
    }

    /**
     * 获取购物车商品ID
     * @return
     */
    public String getId() {
        JSONObject entries = new JSONObject(this);
        ArrayList<String> ids = new ArrayList<>();
        for (String field : fieldSort) {
            if (entries.containsKey(field)) {
                ids.add(entries.getStr(field));
            } else {
                ids.add(StringPool.DEFAULT);
            }
        }
        return StringUtils.join(ids, StringPool.UNDERSCORE);
    }

    public void setSupplierItemId(Long supplierItemId) {
        if (Objects.isNull(supplierItemId) || supplierItemId <= 0) {
            this.supplierItemId = -1L;
            return;
        }
        this.supplierItemId = supplierItemId;
    }

    public void setAreaItemId(Long areaItemId) {
        if (Objects.isNull(areaItemId) || areaItemId <= 0) {
            this.areaItemId = -1L;
            return;
        }
        this.areaItemId = areaItemId;
    }

    /**
     * 自动返回全国还是本地上架ID
     * @return
     */
    public Long itemId() {
        if (ProductType.isGlobal(type)) {
            return supplierItemId;
        }
        return areaItemId;
    }

    @JsonIgnore
    public String getUniqueKey() {
        return StringUtils.format("{}_{}", itemId(), unitSize);
    }

    @JsonIgnore
    public AppCarUnitDTO getUnitDTO() {
        return new AppCarUnitDTO(skuId, unitSize);
    }

    public boolean validateSku() {
        return Objects.nonNull(skuId) && skuId > NumberPool.LONG_ZERO;
    }
}
