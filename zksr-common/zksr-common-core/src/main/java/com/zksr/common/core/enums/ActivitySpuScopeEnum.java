package com.zksr.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivitySpuScopeEnum {
    NONE(-1, "无"),
    ALL(0, "全场券"),
    SUPPLIER(1, "入驻商"),
    CLASS(2, "品类"),
    BRAND(3, "品牌"),
    SKU(4, "商品"),
    ;
    private Integer scope;
    private String name;

    public static ActivitySpuScopeEnum formValue(Long applyType) {
        for (ActivitySpuScopeEnum value : values()) {
            if (value.getScope() == applyType.intValue()) {
                return value;
            }
        }
        return ActivitySpuScopeEnum.NONE;
    }
}
