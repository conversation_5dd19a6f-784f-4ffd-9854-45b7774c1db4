package com.zksr.common.core.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum ProductType {
    LOCAL("local", "本地商品",1),
    RETAIL("retail", "零售商品",1),
    GLOBAL("global", "全国商品",0),
    GROUP("group", "本地+全国",null);

    @JsonValue
    private String type;
    private String name;
    private Integer code;

    ProductType(String type, String name, Integer code) {
        this.type = type;
        this.name = name;
        this.code = code;
    }

    public static boolean isGlobal(String type) {
        return Objects.equals(GLOBAL.getType(), type);
    }
    
    public static boolean isRetail(String type) {
        return Objects.equals(RETAIL.getType(), type);
    }

    public static  ProductType getProduct(Integer code) {
        if (Objects.equals(code, GLOBAL.getCode())) {
            return GLOBAL;
        } else if (Objects.equals(code, LOCAL.getCode())) {
            return LOCAL;
        }
        return GROUP;
    }

    @JsonCreator
    public static ProductType formValue(String productType) {
        for (ProductType value : values()) {
            if (value.getType().equals(productType)) {
                return value;
            }
        }
        return null;
    }
}
