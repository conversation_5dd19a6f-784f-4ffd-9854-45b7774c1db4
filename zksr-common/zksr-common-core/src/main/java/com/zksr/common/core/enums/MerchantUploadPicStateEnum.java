package com.zksr.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户资质上传状态
 * @date 2024/7/15 15:44
 */
@Getter
@AllArgsConstructor
public enum MerchantUploadPicStateEnum {
    SUCCESS("SUCCESS", "成功"),
    DOING("DOING", "处理中"),
    FAIL("FAIL", "失败"),
            ;
    /**
     * 状态
     */
    private final String state;

    /**
     * 名字
     */
    private final String desc;

    public static boolean fail(String state) {
        return FAIL.getState().equals(state);
    }
}
