package com.zksr.common.core.erpUtils;

import com.zksr.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class RSAUtils {
    public static final String CHARSET = "UTF-8";
    public static final String RSA_ALGORITHM = "RSA"; // ALGORITHM ['ælgərɪð(ə)m] 算法的意思

    public static Map<String, String> createKeys(int keySize) {
        // 为RSA算法创建一个KeyPairGenerator对象
        KeyPairGenerator kpg;
        try {
            kpg = KeyPairGenerator.getInstance(RSA_ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            throw new ServiceException("No such algorithm-->[" + RSA_ALGORITHM + "]");
        }

        // 初始化KeyPairGenerator对象,密钥长度
        kpg.initialize(keySize);
        // 生成密匙对
        KeyPair keyPair = kpg.generateKeyPair();
        // 得到公钥
        Key publicKey = keyPair.getPublic();
        String publicKeyStr = Base64.encodeBase64URLSafeString(publicKey.getEncoded());
        // 得到私钥
        Key privateKey = keyPair.getPrivate();
        String privateKeyStr = Base64.encodeBase64URLSafeString(privateKey.getEncoded());
        // map装载公钥和私钥
        Map<String, String> keyPairMap = new HashMap<String, String>();
        keyPairMap.put("publicKey", publicKeyStr);
        keyPairMap.put("privateKey", privateKeyStr);
        // 返回map
        return keyPairMap;
    }

    /**
     *  生成 公钥私钥方式 / 和 + 不转义
     * @param keySize
     * @return
     */
    public static Map<String, String> createKeysNew(int keySize) {
        // 为RSA算法创建一个KeyPairGenerator对象
        KeyPairGenerator kpg;
        try {
            kpg = KeyPairGenerator.getInstance(RSA_ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            throw new ServiceException("No such algorithm-->[" + RSA_ALGORITHM + "]");
        }

        // 初始化KeyPairGenerator对象,密钥长度
        kpg.initialize(keySize);
        // 生成密匙对
        KeyPair keyPair = kpg.generateKeyPair();
        // 得到公钥
        Key publicKey = keyPair.getPublic();
        String publicKeyStr = new String(Base64.encodeBase64(publicKey.getEncoded()));
        // 得到私钥
        Key privateKey = keyPair.getPrivate();
        String privateKeyStr = new String(Base64.encodeBase64(privateKey.getEncoded()));
        // map装载公钥和私钥
        Map<String, String> keyPairMap = new HashMap<String, String>();
        keyPairMap.put("publicKey", publicKeyStr);
        keyPairMap.put("privateKey", privateKeyStr);
        // 返回map
        return keyPairMap;
    }

    /**
     * 得到公钥
     * @param publicKey  密钥字符串（经过base64编码）
     * @throws Exception
     */
    public static RSAPublicKey getPublicKey(String publicKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        // 通过X509编码的Key指令获得公钥对象
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKey));
        RSAPublicKey key = (RSAPublicKey) keyFactory.generatePublic(x509KeySpec);
        return key;
    }

    /**
     * 得到私钥
     * @param privateKey  密钥字符串（经过base64编码）
     * @throws Exception
     */
    public static RSAPrivateKey getPrivateKey(String privateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        // 通过PKCS#8编码的Key指令获得私钥对象
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));
        RSAPrivateKey key = (RSAPrivateKey) keyFactory.generatePrivate(pkcs8KeySpec);
        return key;
    }

    /**
     * 公钥加密
     * @param data
     * @param publicKey
     * @return
     */
    public static String publicEncrypt(String data, RSAPublicKey publicKey) {
        try {
            Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            return Base64.encodeBase64URLSafeString(rsaSplitCodec(cipher, Cipher.ENCRYPT_MODE, data.getBytes(CHARSET), publicKey.getModulus().bitLength()));
        } catch (Exception e) {

            throw new ServiceException("加密字符串[" + data + "]时遇到异常");
        }
    }

    /**
     * 私钥解密
     * @param data
     * @param privateKey
     * @return
     */

    public static String privateDecrypt(String data, RSAPrivateKey privateKey) {
        try {
            Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            return new String(rsaSplitCodec(cipher, Cipher.DECRYPT_MODE, Base64.decodeBase64(data), privateKey.getModulus().bitLength()), CHARSET);
        } catch (Exception e) {
            log.error(" 解密字符串异常,", e);
            throw new RuntimeException("解密字符串[" + data + "]时遇到异常", e);
        }
    }

    /**
     * 私钥加密
     * @param data
     * @param privateKey
     * @return
     */

    public static String privateEncrypt(String data, RSAPrivateKey privateKey) {
        try {
            Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
            //每个Cipher初始化方法使用一个模式参数opmod，并用此模式初始化Cipher对象。此外还有其他参数，包括密钥key、包含密钥的证书certificate、算法参数params和随机源random。
            cipher.init(Cipher.ENCRYPT_MODE, privateKey);
            return Base64.encodeBase64URLSafeString(rsaSplitCodec(cipher, Cipher.ENCRYPT_MODE, data.getBytes(CHARSET), privateKey.getModulus().bitLength()));
        } catch (Exception e) {
            throw new RuntimeException("加密字符串[" + data + "]时遇到异常", e);
        }
    }

    /**
     * 公钥解密
     * @param data
     * @param publicKey
     * @return
     */

    public static String publicDecrypt(String data, RSAPublicKey publicKey) {
        try {
            Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, publicKey);
            return new String(rsaSplitCodec(cipher, Cipher.DECRYPT_MODE, Base64.decodeBase64(data), publicKey.getModulus().bitLength()), CHARSET);
        } catch (Exception e) {
            throw new RuntimeException("解密字符串[" + data + "]时遇到异常", e);
        }
    }

    //rsa切割解码  , ENCRYPT_MODE,加密数据   ,DECRYPT_MODE,解密数据
    private static byte[] rsaSplitCodec(Cipher cipher, int opmode, byte[] datas, int keySize) {
        int maxBlock = 0;  //最大块
        if (opmode == Cipher.DECRYPT_MODE) {
            maxBlock = keySize / 8;
        } else {
            maxBlock = keySize / 8 - 11;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] buff;
        int i = 0;
        try {
            while (datas.length > offSet) {
                if (datas.length - offSet > maxBlock) {
                    //可以调用以下的doFinal（）方法完成加密或解密数据：
                    buff = cipher.doFinal(datas, offSet, maxBlock);
                } else {
                    buff = cipher.doFinal(datas, offSet, datas.length - offSet);
                }
                out.write(buff, 0, buff.length);
                i++;
                offSet = i * maxBlock;
            }
        } catch (Exception e) {
            throw new RuntimeException("加解密阀值为[" + maxBlock + "]的数据时发生异常", e);
        }
        byte[] resultDatas = out.toByteArray();
        IOUtils.closeQuietly(out);
        return resultDatas;
    }

    /**
     * 示例方法
     * @param args
     */
    public static void main(String[] args) throws Exception{

        //        ApiRequestBody body = new ApiRequestBody();
//        body.setReqId("1").setReqTime(DateUtils.dateTimeNow()).setRequestType(RequestType.CONSUMER);
//        ApiDataModel<WmsBaseConsumerDto> dataModel = new ApiDataModel<>();
//        dataModel.setType(OperationType.DELETE);
//        WmsBaseConsumerDto consumerDto = new WmsBaseConsumerDto();
//        consumerDto.setConsumerNo("111");
//        dataModel.setData(consumerDto);
//        body.setBizData(RSAUtils.privateEncrypt(JacksonUtils.toJson(dataModel), RSAUtils.getPrivateKey(privateKey)));
//        System.out.println("加密body: \r\n" + JacksonUtils.toJson(body));
//        /*ApiDataModel<ApiRequestBody> decode = SecretUtil.decode(body, ApiRequestBody.class);
//        System.out.println("解密后文字: \r\n" + JacksonUtils.toJson(decode.getData()));*/



//        //生成密钥对
//        Map<String, String> keys = RSAUtils.createKeys(512);
//        //公钥
//        String publicKey = keys.get("publicKey");
//        System.out.println("加密公钥：" + publicKey);
//        //私钥
//        String privateKey = keys.get("privateKey");
//        System.out.println("解密私钥：" + privateKey);
//        //请求数据
//        Map<String,Object> data = new HashMap<>();
//        data.put("reqTime",new Date());
//        data.put("data","示例数据");
//        data.put("reqId",123456789);
//        //公钥加密数据
//        RSAPublicKey publicKeyRsa = RSAUtils.getPublicKey(publicKey);
//        String encryptData = RSAUtils.publicEncrypt(JSONUtil.toJsonStr(data), publicKeyRsa);
//        System.out.println("加密前数据：" + JSONUtil.toJsonStr(data));
//        System.out.println("加密后数据：" + encryptData);
        //私钥解密
        RSAPrivateKey privateKeyRsa = RSAUtils.getPrivateKey("MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAhRfPtsF18UNX3OVjIP1yZ_g1krLWHn6WQaG9rrVuRfxbtl0uXMJvKWx44dY7ow7FLDPMHcFVTRodK_p-dLs7AQIDAQABAkBjdn0Llh6fM_WopmDjmaTzICJhRHcSCQYffFE2DG6hq7cA5wIfFohf1GiS_7q11lcz-uURN0v1Da5si5sb9FIVAiEAxkgI-nQVCrKSEZ-0rBWZgudivEaDD7nGT1zDqc_e9pMCIQCr1e1HZlBduY9-cWLk0mwMfLPegMr9mpg-OAj_VFpQmwIhAJPN3Mt9vrWzuUjj6RUiIMXA4AwC7-3ICPjd6IjsCIN7AiBh4har2WhmO1G2ITjIR-Usp8x-LrdzByE6MAlQCOj5iQIgTmaGyl6UbTIDi98hOFQD2T1OrDSuSq5NlnUpZqF_rRM");
        String DecryptData = RSAUtils.privateDecrypt("ZwiGUp3alrkt88ohlDTnkyZgorulolVA3r6tCeA-pE4UI4pxvhiN4GVN-31nBHie_WzwDkqotinhLLZnoBB5tjkXfZiIAslyy-e6h2VGzDU7S2vR6oQ_FoR0dJmPOuW5ehUwCVdMtBeAKlxwTH3SHnEIBFvyZ75UT5UkCntSFZRf5dBmjairZO_8IyAV_EVPeOkNnfJh0OssJPAV8zZWlewz3FfgeAmAEXAbNXy4LB6SUCerO-YcUDUZawmdJm8rW3gEgzI2Mwv9-1Zy99Q8P6AWdSeHBqmnbRKHNfLrjAZ01UxE4B9rhZtbOOC-8f2d33-N0pIBFPEu2xO4DGNM7g", privateKeyRsa);
        System.out.println("解密后数据：" + DecryptData);
    }

}

