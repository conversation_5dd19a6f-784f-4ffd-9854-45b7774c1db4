package com.zksr.common.core.domain.vo.openapi;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 *  b2b发送erp收款单MQ实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class SyncReceiptSendDTO {

    /**
     * 订单类型
     * 来源：{@link com.zksr.common.core.constant.SheetTypeConstants.XSS}、  {@link com.zksr.common.core.constant.SheetTypeConstants.SHS}
     */
    @ApiModelProperty(value = "单据类型  XSS：销售订单  SHS：售后单 ")
    private String sheetType;

    @ApiModelProperty(value = "销售入驻商订单号/售后入驻商订单 ")
    private String supplierSheetNo;


    /** 是否收款 收款单使用  0否1是*/
    private Integer isProceeds = 1;

    /**
     * 明细单号（订单或售后单）
     */
    @ApiModelProperty(value = "明细单号（订单或售后单）")
    private List<Long> supplierDtlIdList;

    public SyncReceiptSendDTO(String sheetType, String supplierSheetNo) {
        this.sheetType = sheetType;
        this.supplierSheetNo = supplierSheetNo;
    }
}
