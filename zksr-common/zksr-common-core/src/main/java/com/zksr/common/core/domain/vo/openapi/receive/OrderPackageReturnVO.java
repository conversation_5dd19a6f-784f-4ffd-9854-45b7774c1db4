package com.zksr.common.core.domain.vo.openapi.receive;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.zksr.common.core.annotation.Excel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("包裹回传")
public class OrderPackageReturnVO {

    /** 订单号(入驻商订单编号) */
    @Excel(name = "B2B入驻商销售订单号")
    @ApiModelProperty(value = "B2B入驻商销售订单号")
    @NotNull(message = "B2B入驻商销售订单编号不能为空")
    private String supplierOrderNo;
    
    /** 包裹列表 */
    @Excel(name = "包裹列表")
    @ApiModelProperty(value = "包裹列表")
    private List<PackageInfo> packageList;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PackageInfo {
        /** 运单号 */
        @ApiModelProperty(value = "运单号")
        private String expressNo;
        
        /** 快递公司编码 */
        @ApiModelProperty(value = "快递公司编码")
        private String expressComNo;
        
        /** 包裹商品明细 */
        @ApiModelProperty(value = "包裹商品明细")
        private List<PackageItemInfo> packageItems;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PackageItemInfo {
        /** 商品编号 */
        @ApiModelProperty(value = "商品编号")
        private String erpItemNo;
        
        /** 商品编号 */
        @ApiModelProperty(value = "商品名称")
        private String itemName;
        
        /** 总数量 */
        @ApiModelProperty(value = "总数量")
        private Integer totalQty;
        
        /** 商品行号 */
        @ApiModelProperty(value = "商品行号")
        private Integer lineNum;
        
        /** id */
        @ApiModelProperty(value = "id")
        private String id;

        /** 套件标识 0散件 1套件 */
        @ApiModelProperty(value = "套件标识 0散件 1套件")
        private Integer bomFlag;

        /** 子件商品名称 */
        @ApiModelProperty(value = "子件商品名称，bomFlag为0时，需要传")
        private String subItemName;

        /** 子件商品编码 */
        @ApiModelProperty(value = "子件商品编码，bomFlag为0时，需要传")
        private String subItemNo;

        /** 单位用量 */
        @ApiModelProperty(value = "单位用量，一个bom中子件的数量")
        private BigDecimal subQtyBom;
    }


}
