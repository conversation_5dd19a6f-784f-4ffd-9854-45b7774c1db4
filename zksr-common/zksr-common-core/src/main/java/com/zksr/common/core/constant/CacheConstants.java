package com.zksr.common.core.constant;

import com.zksr.common.core.utils.StringUtils;

/**
 * 缓存常量信息
 * 
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 缓存有效期，默认720（分钟）
     */
    public final static long EXPIRATION = 720;

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;

    /**
     * 密码最小错误次数
     */
    public final static int PASSWORD_MIN_RETRY_COUNT = 3;
    /**
     * 密码最大错误次数
     */
    public final static int PASSWORD_MAX_RETRY_COUNT = 5;

    /**
     * 密码锁定时间，默认10（分钟）
     */
    public final static long PASSWORD_LOCK_TIME = 10;

    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * SAAS权限缓存前缀
     */
    public final static String SAAS_LOGIN_USER_TOKEN_KEY = "saas:login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 平台商字典管理
     */
    public static final String SYS_PARTNER_DICT_KEY = "sys_partner_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 登录IP黑名单 cache key
     */
    public static final String SYS_LOGIN_BLACKIPLIST = SYS_CONFIG_KEY + "sys.login.blackIPList";

    /**
     * SKU库存
     */
    public static final String SKU_STOCK = "sku_stock:";

    /**
     * 库存数量
     */
    public static final String STOCK_QTY = "stock_qty";

    /**
     * 已售数量
     */
    public static final String REAL_SALE_QTY = ":real_sale_qty";

    /**
     * 订单单号
     */
    public static final String ORDERNO_SEQUENCE = "orderno_sequence:";

    /**
     * 门店余额订单充值单号
     */
    public static final String BALANCENO_SEQUENCE = "balanceno_sequence:";

    /**
     * 账户流程单号
     */
    public static final String WITHDRAW_SEQUENCE = "accountwno_sequence:";

    /**
     * 组合促销商品编号
     */
    public static final String COMBINE_SPU_NO_SEQUENCE = "combine_spu_no_sequence:";

    /**
     * 入驻商appid
     */
    public static final String SUPPLIER_APPID = SYS_CONFIG_KEY + "supplier_appid";

    /**
     * 入驻商appSecret
     */
    public static final String SUPPLIER_APP_SECRET = SYS_CONFIG_KEY + "supplier_appSecret";

    /**
     * 小程序商编配置
     */
    public static final String SUPPLIER_ALT_NO = SYS_CONFIG_KEY + "app_alt_no_";

    /**
     * 入驻商支付配置
     */
    public static final String SUPPLIER_PAY_CONFIG = SYS_CONFIG_KEY + "supplier_pay_config";

    /**
     * 全局支付配置
     */
    public static final String GLOBAL_PAY_CONFIG = SYS_CONFIG_KEY + "global_pay_config";

    /**
     * 微信服务商appid
     */
    public static final String WECHAT_SERVICE_PROVIDER_ID = SYS_CONFIG_KEY + "wechat_service_provider_id";

    /**
     * 微信服务商appkey
     */
    public static final String WECHAT_SERVICE_PROVIDER_KEY = SYS_CONFIG_KEY + "wechat_service_provider_key";

    /**
     * 微信服务商aeskey, 加解密key
     */
    public static final String WECHAT_SERVICE_PROVIDER_AES = SYS_CONFIG_KEY + "wechat_service_provider_aes";

    /**
     * 微信服务商小程序授权回调url
     * 参考配置 http://test-b2b-api.zhongkeshangruan.cn/file/openWxMsg/updateAuthCode/zBVuCma2hBQh50m90mc3IuoeiM5NQI6h/{}
     * {} = 用户appid
     */
    public static final String WECHAT_SERVICE_AUTH_URL = SYS_CONFIG_KEY + "wechat_service_auth_url";

    /**
     * 微信服务商component_access_token 凭证保存中心url
     * 参考配置 http://test-b2b-api.zhongkeshangruan.cn/file/openWxMsg/getComponentAccessToken/zBVuCma2hBQh50m90mc3IuoeiM5NQI6h
     */
    public static final String WECHAT_SERVICE_PROVIDER_COMPONENT_ACCESS_TOKEN_URL = SYS_CONFIG_KEY + "wechat_service_provider_component_access_token_url";

    /**
     * 微信服务商 pre_auth_code 凭证获取中心
     * 参考配置 http://test-b2b-api.zhongkeshangruan.cn/file/openWxMsg/getPreAuthCode/zBVuCma2hBQh50m90mc3IuoeiM5NQI6h
     */
    public static final String WECHAT_SERVICE_PROVIDER_PRE_AUTH_CODE_URL = SYS_CONFIG_KEY + "wechat_service_provider_pre_auth_code_url";

    /**
     * 微信服务商 setmchprofitrate 报名手续费由白名单服务器发起
     * 参考配置 http://test-b2b-api.zhongkeshangruan.cn/file/openWxMsg/setMchProfitRate/zBVuCma2hBQh50m90mc3IuoeiM5NQI6h
     */
    public static final String WECHAT_SERVICE_PROVIDER_MCH_PROFIT_RATE_URL = SYS_CONFIG_KEY + "wechat_service_provider_mch_profit_rate_url";

    /**
     * http://test-b2b-api.zhongkeshangruan.cn/file/openWxMsg/getAuthorizerAccessToken/zBVuCma2hBQh50m90mc3IuoeiM5NQI6h?appid={}
     * {} = 用户appid
     * 微信服务商小程序 auth_code 凭证保存中心url
     */
    public static final String WECHAT_SERVICE_PROVIDER_COMPONENT_AUTH_CODE_URL = SYS_CONFIG_KEY + "wechat_service_provider_component_auth_code_url";

    /**
     * 从中心服务器获取accesstoken
     */
    public static final String WECHAT_APP_ACCESS_TOKEN_CENTER_URL = SYS_CONFIG_KEY + "wechat_app_access_token_center_url:";

    /**
     * 中心服务器对外验证token
     */
    public static final String WECHAT_SERVICE_CENTER_SERVER_TOKEN = SYS_CONFIG_KEY + "wechat_service_center_server_token";


    /** ------------------ 业务员APP ----------------------------- */

    /**
     * 业务员APP cache key
     */
    public static final String COLONEL_APP_KEY = "colenel_app:";

    /**
     * 业务员APP 首页信息Key
     */
    public static final String COLONEL_APP_PAGE_DATA = COLONEL_APP_KEY + "today_page_data";

    /**
     * 入驻商绑定的公众号appid
     */
    public static final String SUPPLIER_PUBLISH_APPID = SYS_CONFIG_KEY + "supplier_publish_appid";

    /**
     * 入驻商绑定的公众号名称
     */
    public static final String SUPPLIER_PUBLISH_APPAME = SYS_CONFIG_KEY + "supplier_publish_appname";

    /**
     * 入驻商绑定的公众号appsecret
     */
    public static final String SUPPLIER_PUBLISH_APP_SECRET = SYS_CONFIG_KEY + "supplier_publish_app_secret";

    /**
     * 业务员小程序更新配置
     */
    public static final String COLONEL_APP_VERSION = SYS_CONFIG_KEY + "colonel_app_version";


    /**
     * 产品编号
     */
    public static final String SPU_NO_SEQUENCE = "spu_no_sequence:";





    /** ------------------ get Key ----------------------------- */
    /**
     * 获取 业务员APP 首页信息Key
     * @param colonelId 业务员ID
     * @param date 日期
     * @return
     */
    public static String getColonelAppPageDataKey(Long colonelId, String date) {
        return StringUtils.format("{}_{}:{}", COLONEL_APP_PAGE_DATA, colonelId, date);
    }
}
