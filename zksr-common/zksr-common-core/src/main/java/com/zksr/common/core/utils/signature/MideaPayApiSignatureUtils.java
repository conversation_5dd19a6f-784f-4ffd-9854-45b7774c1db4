package com.zksr.common.core.utils.signature;

import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description: 美的付API签名工具类
 * 基于美的开放平台签名规范实现
 * @Date: 2025/07/16
 */
@Slf4j
public class MideaPayApiSignatureUtils {

    public static final String KEY_ALGORITHM = "RSA";
    public static final String SIGNATURE_ALGORITHM = "SHA256withRSA";

    private static Signature signature;
    private static KeyFactory keyFactory;

    private static final String REQUEST_SIGNATURE_KEY = "request-signature";
    private static final String RESPONSE_SIGNATURE_KEY = "response-signature";
    private static final String BODY_KEY = "body";
    private static final Set<String> excludeHeaders = Stream.of(
            "content-type", "content-length", "host", "user-agent",
            "accept", "accept-encoding", "connection", "cache-control"
    ).collect(Collectors.toSet());

    static {
        try {
            keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            log.error("方法参数错误, KeyFactory算法={}, Signature={}", KEY_ALGORITHM, SIGNATURE_ALGORITHM, e);
        }
    }

    /**
     * 用私钥对字符串进行数字签名
     *
     * @param signContent 待生成签名的明文
     * @param privateKey  base64编码的私钥
     * @return 签名字符串
     */
    public static String sign(String signContent, String privateKey) {
        try {
            if (StringUtils.isBlank(signContent)) {
                return null;
            }
            // 生成私钥
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));
            PrivateKey priKey = keyFactory.generatePrivate(keySpec);

            // 用私钥进行加签
            signature.initSign(priKey);
            signature.update(signContent.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64String(signature.sign());
        } catch (InvalidKeySpecException e) {
            log.error("generatePrivate方法参数错误, privateKey={}", privateKey, e);
        } catch (InvalidKeyException e) {
            log.error("私钥无效, privateKey={}", privateKey, e);
        } catch (SignatureException e) {
            log.error("待加密数据不合法, data={}", signContent, e);
        }
        return null;
    }

    /**
     * 对数据进行验签
     *
     * @param verifyContent 待验签的明文
     * @param publicKey     公钥
     * @param sign          数字签名
     * @return 验签结果
     */
    public static boolean verify(String verifyContent, String publicKey, String sign) {

        try {
            if (StringUtils.isBlank(verifyContent) || StringUtils.isBlank(sign)) {
                return false;
            }
            // 生成公钥
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKey));
            PublicKey pubKey = keyFactory.generatePublic(keySpec);

            // 用公钥进行验签
            signature.initVerify(pubKey);
            signature.update(verifyContent.getBytes(StandardCharsets.UTF_8));
            return signature.verify(Base64.decodeBase64(sign));
        } catch (InvalidKeySpecException e) {
            log.error("generatePublic参数错误, publicKey={}", publicKey, e);
        } catch (InvalidKeyException e) {
            log.error("私钥无效, publicKey={}", publicKey, e);
        } catch (SignatureException e) {
            log.error("待加密数据或签名不合法, data={} sign={}", verifyContent, sign, e);
        }
        return false;
    }

    /**
     * 生成请求签名
     * 
     * @param requestParams 请求参数（包括URL参数和Header参数）
     * @param requestBody 请求体内容（JSON字符串）
     * @param privateKey 商户私钥（Base64编码）
     * @return 签名字符串
     */
    public static String generateRequestSignature(Map<String, String> requestParams, String requestBody, String privateKey) {
        try {
            // 1. 构建待签名字符串
            String signContent = buildSignContent(requestParams, requestBody);
            
            log.debug("待签名字符串: {}", signContent);
            
            // 2. 使用私钥进行签名
            String signature = sign(signContent, privateKey);
            
            log.debug("生成的签名: {}", signature);
            
            return signature;
        } catch (Exception e) {
            log.error("生成请求签名失败", e);
            throw new RuntimeException("生成请求签名失败", e);
        }
    }

    /**
     * 验证响应签名
     * 
     * @param responseBody 响应体内容
     * @param responseSignature 响应签名
     * @param publicKey 商户公钥（Base64编码）
     * @return 验签是否通过
     */
    public static boolean verifyResponseSignature(String responseBody, String responseSignature, String publicKey) {
        try {
            if (StringUtils.isBlank(responseBody) || StringUtils.isBlank(responseSignature)) {
                log.warn("响应体或签名为空，跳过验签");
                return true;
            }
            
            log.debug("响应体: {}", responseBody);
            log.debug("响应签名: {}", responseSignature);
            
            // 使用公钥验证签名
            boolean isValid = verify(responseBody, publicKey, responseSignature);
            
            log.debug("验签结果: {}", isValid);
            
            return isValid;
        } catch (Exception e) {
            log.error("验证响应签名失败", e);
            return false;
        }
    }

    /**
     * 构建待签名字符串
     * 规则：
     * 1. 将所有参数放入TreeMap（自动按key升序排序）
     * 2. 剔除request-signature字段
     * 3. 剔除key或value为空的参数
     * 4. 如果有请求体，添加body参数
     * 5. 用&连接所有参数
     */
    public static String buildSignContent(Map<String, String> requestParams, String requestBody) {
        // 使用TreeMap自动按key升序排序
        TreeMap<String, String> sortedParams = new TreeMap<>();
        
        // 添加请求参数
        if (requestParams != null) {
            requestParams.entrySet().stream()
                    .filter(entry -> StringUtils.isNotBlank(entry.getKey()) && StringUtils.isNotBlank(entry.getValue()))
                    .filter(entry -> !REQUEST_SIGNATURE_KEY.equals(entry.getKey())) // 剔除request-signature字段
                    .forEach(entry -> sortedParams.put(entry.getKey(), entry.getValue()));
        }
        
        // 添加请求体参数（如果不为空）
        if (StringUtils.isNotBlank(requestBody)) {
            sortedParams.put(BODY_KEY, requestBody);
        }
        
        // 拼接参数
        return sortedParams.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
    }

    /**
     * 从HTTP请求中提取所有参数
     * 
     * @param urlParams URL查询参数
     * @param headerParams HTTP头参数
     * @return 合并后的参数Map
     */
    public static Map<String, String> extractRequestParams(Map<String, String> urlParams, Map<String, String> headerParams) {
        Map<String, String> allParams = new HashMap<>();
        
        // 添加URL参数
        if (urlParams != null) {
            allParams.putAll(urlParams);
        }
        
        // 添加Header参数（排除Content-Type等非业务参数）
        if (headerParams != null) {
            headerParams.entrySet().stream()
                    .filter(entry -> isBusinessHeader(entry.getKey())) // 排除标准HTTP头
                    .forEach(entry -> allParams.put(entry.getKey(), entry.getValue()));
        }
        
        return allParams;
    }

    /**
     * 判断是否为业务相关的Header参数
     */
    private static boolean isBusinessHeader(String headerName) {
        if (StringUtils.isBlank(headerName)) {
            return false;
        }
        
        String lowerHeaderName = headerName.toLowerCase();
        
        return !excludeHeaders.contains(lowerHeaderName);
    }

    /**
     * 生成请求ID
     */
    public static String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 构建完整的请求参数（用于签名）
     * 
     * @param merchantId 商户ID
     * @param appId 应用ID
     * @param version 版本号
     * @param requestId 请求ID
     * @param urlParams URL参数
     * @return 完整的请求参数
     */
    public static Map<String, String> buildFullRequestParams(String merchantId, String appId, String version, 
                                                            String requestId, Map<String, String> urlParams) {
        Map<String, String> fullParams = new HashMap<>();
        
        // 添加公共参数
        if (StringUtils.isNotBlank(merchantId)) {
            fullParams.put("merchant_id", merchantId);
        }
        if (StringUtils.isNotBlank(appId)) {
            fullParams.put("app_id", appId);
        }
        if (StringUtils.isNotBlank(version)) {
            fullParams.put("version", version);
        }
        if (StringUtils.isNotBlank(requestId)) {
            fullParams.put("request-id", requestId);
        }
        
        // 添加URL参数
        if (urlParams != null) {
            urlParams.entrySet().stream()
                    .filter(entry -> StringUtils.isNotBlank(entry.getKey()) && StringUtils.isNotBlank(entry.getValue()))
                    .forEach(entry -> fullParams.put(entry.getKey(), entry.getValue()));
        }
        
        return fullParams;
    }

    /**
     * 解析JSON请求体为字符串（用于签名）
     */
    public static String parseRequestBodyForSignature(Object requestBody) {
        if (requestBody == null) {
            return null;
        }
        
        if (requestBody instanceof String) {
            return (String) requestBody;
        }
        
        try {
            return JSON.toJSONString(requestBody);
        } catch (Exception e) {
            log.error("解析请求体失败", e);
            return null;
        }
    }

    /**
     * 验证签名参数完整性
     */
    public static void validateSignatureParams(String merchantId, String appId, String privateKey) {
        if (StringUtils.isBlank(merchantId)) {
            throw new IllegalArgumentException("商户ID不能为空");
        }
        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("应用ID不能为空");
        }
        if (StringUtils.isBlank(privateKey)) {
            throw new IllegalArgumentException("私钥不能为空");
        }
    }

    /**
     * 验证验签参数完整性
     */
    public static void validateVerifyParams(String responseBody, String responseSignature, String publicKey) {
        if (StringUtils.isBlank(responseBody)) {
            throw new IllegalArgumentException("响应体不能为空");
        }
        if (StringUtils.isBlank(responseSignature)) {
            throw new IllegalArgumentException("响应签名不能为空");
        }
        if (StringUtils.isBlank(publicKey)) {
            throw new IllegalArgumentException("公钥不能为空");
        }
    }

}
