package com.zksr.common.core.convert;

import com.zksr.common.core.enums.SysYesNoEnum;
import org.mapstruct.Named;

import java.math.BigDecimal;

public interface BaseExpressionMapper {
    @Named("getYesNoName")
    default String getYesNoName(Integer code, String defaultValue) {
        return SysYesNoEnum.getYesNoName(code, defaultValue);
    }

    @Named("simpleDecimalToStr")
    default String simpleDecimalToStr(BigDecimal decimal) {
        if (decimal == null) {
            return null;
        }
        return decimal.stripTrailingZeros().toPlainString();
    }
}
