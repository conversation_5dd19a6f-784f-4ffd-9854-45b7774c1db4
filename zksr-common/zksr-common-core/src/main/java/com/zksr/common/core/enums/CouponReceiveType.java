package com.zksr.common.core.enums;

import com.zksr.common.core.utils.ToolUtil;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum CouponReceiveType {
    NORMAL(0, "用户领取"),
    INITIATIVE(1, "主动发放"),
    PAYMENT(2, "下单返券"),
    POINT(3, "门店积分兑换"),
    REGISTER(4, "注册发券"),
    SALESMAN(5, "业务员发券"),
    BATCH(6, "批次发放")
    ;
    private final Integer type;
    private final String name;

    CouponReceiveType(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static boolean isUser(Integer receiveType) {
        if (Objects.isNull(receiveType)) {
            return false;
        }
        return Objects.equals(NORMAL.type, receiveType);
    }

    public static String getReceiveTypeName(Integer type){
        if (ToolUtil.isEmpty(type)){
            return "领取方式匹配失败";
        }

        for (CouponReceiveType value : CouponReceiveType.values()) {
            if(value.type.equals(type)){
                return value.name;
            }
        }
        return "领取方式匹配失败";
    }
}
