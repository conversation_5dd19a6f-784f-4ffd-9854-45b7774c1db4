package com.zksr.common.core.domain.vo.openapi.receive;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 新增货到付款清账能力
 *
 * <AUTHOR>
 * @date 2024-10-23
 */
@Data
@ApiModel("新增货到付款清账能力")
@NoArgsConstructor
public class OrderHdfkSettleVO {
    private static final long serialVersionUID = 1L;

    /** 订单号(B2B入驻商销售订单编号) */
    @ApiModelProperty(value = "订单号(B2B入驻商销售订单编号)")
    private String supplierOrderNo;

    @ApiModelProperty("备注")
    private String tips;

    @ApiModelProperty("凭证")
    private String voucher;
}
