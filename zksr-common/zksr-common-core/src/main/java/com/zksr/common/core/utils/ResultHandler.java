package com.zksr.common.core.utils;


import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.Serializable;
import java.nio.charset.Charset;
import java.util.Map;
import java.util.Optional;

import static org.springframework.core.codec.StringDecoder.DEFAULT_CHARSET;

public class ResultHandler implements Serializable {

    private static final Integer PRINTLENGTH = 20000;
    private byte[] content;
    private ObjectMapper objectMapper;
    private Charset charset = DEFAULT_CHARSET;
    private int statusCode = -1;
    private Request request;
    private long startTime;
    private long cost;


    public ResultHandler(byte[] content, ObjectMapper objectMapper) {
        this.content = content;
        this.objectMapper = objectMapper;
    }

    private ResultHandler setCharset(@NotNull String charsetName){
        charset = Charset.forName(charsetName);
        return this;
    }

    public <T> T toJavaObject(Class<T> classType){
        if(content == null){
            return null;
        }
        try {
            return objectMapper.readValue(content, classType);
        }catch (IOException e){
            throw new RuntimeException(e);
        }
    }

    public <T> T toJavaObject(Class<?> collectionClass, Class<?>... elementClasses){
        if(content == null){
            return null;
        }
        try {
            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);
            return objectMapper.readValue(content, javaType);
        }catch (IOException e){
            throw new RuntimeException(e);
        }
    }

    public Map getMap(){
        if(content == null){
            return null;
        }
        try {
            return objectMapper.readValue(content, Map.class);
        }catch (IOException e){
            throw new RuntimeException(e);
        }
    }

    public String getString(){
        return new String(content, charset);
    }

    public byte[] getByte(){
        return content;
    }

    public String limitString(){
        return limitString(PRINTLENGTH);
    }

    public String limitString(Integer pl){
        String rs = new String(content, charset);
        pl = Optional.ofNullable(pl).orElse(PRINTLENGTH);
        if(rs.length() > pl){
            return "{...content-length:"+ rs.length() +"}";
        }
        return rs;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public Request getRequest() {
        return request;
    }

    public void setRequest(Request request) {
        this.request = request;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getCost() {
        return cost;
    }

    public void setCost(long cost) {
        this.cost = cost;
    }
}