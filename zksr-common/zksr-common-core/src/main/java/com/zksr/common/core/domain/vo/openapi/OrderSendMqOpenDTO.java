package com.zksr.common.core.domain.vo.openapi;


import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.domain.erp.dto.AfterSheetOpenDto;
import lombok.*;
import lombok.experimental.Accessors;

/**
*
 * 发送订单 MQ入参
* <AUTHOR>
* @date 2024/6/4 15:07
*/
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class OrderSendMqOpenDTO {

    /** 订单/退单主编号 */
    private String orderNo;

    /** 业务员id */
    private Long colonelId;

    /** 订单类型 */
    private String orderType;

    /** 收款方式 HDSK/ */
    private String payWay;

    /** 门店编号 */
    private Long branchId;

    /**
     * 运营商id
     */
    @Excel(name = "运营商id")
    private Long dcId;

    /** 退货单 */
    private AfterSheetOpenDto afterSheetOpenDto;

    public OrderSendMqOpenDTO(String orderNo, Long colonelId, String orderType, String payWay, Long branchId, Long dcId) {
        this.orderNo = orderNo;
        this.colonelId = colonelId;
        this.orderType = orderType;
        this.payWay = payWay;
        this.branchId = branchId;
        this.dcId = dcId;
    }

    public OrderSendMqOpenDTO(String orderNo,String orderType){
        this.orderNo =orderNo;
        this.orderType =orderType;
    }

    public OrderSendMqOpenDTO(AfterSheetOpenDto afterSheetOpenDto, Long colonelId, String payWay, Long branchId, String orderNo, Long dcId) {
        this.afterSheetOpenDto=afterSheetOpenDto;
        this.colonelId = colonelId;
//        this.orderType = orderType;
        this.payWay = payWay;
        this.branchId = branchId;
        this.orderNo =  orderNo;
        this.dcId = dcId;
    }


}
