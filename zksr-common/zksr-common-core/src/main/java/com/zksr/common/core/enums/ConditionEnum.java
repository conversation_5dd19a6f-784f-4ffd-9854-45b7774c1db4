package com.zksr.common.core.enums;

import cn.hutool.core.util.EscapeUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

public enum ConditionEnum {
    GT(">", "大于", "one"),
    LT("<", "小于", "one"),
    GE(">=", "大于等于", "one"),
    LE("<=", "小于等于", "one"),
    BT("A<=X<=B", "介于(大于等于A, 小于等于B)", "range"),
    BTL("A<=X<B", "介于(大于等于A, 小于B)", "range"),
    BTG("A<X<=B", "介于(大于A,小于等于B)", "range"),
    ;
    private final String condition;

    @Getter
    private final String name;

    @Getter
    private final String type;

    ConditionEnum(String condition, String name, String type) {
        this.condition = condition;
        this.name = name;
        this.type = type;
    }

    @JsonValue
    public String getCondition() {
        return condition;
    }

    @JsonCreator
    public static ConditionEnum fromValue(String value) {
        for (ConditionEnum b : ConditionEnum.values()) {
            if (b.condition.equals(EscapeUtil.unescapeHtml4(value))) {
                return b;
            }
        }
        return null;
    }

    public boolean isOne() {
        return this.type.equals("one");
    }

    public boolean isRange() {
        return this.type.equals("range");
    }
}
