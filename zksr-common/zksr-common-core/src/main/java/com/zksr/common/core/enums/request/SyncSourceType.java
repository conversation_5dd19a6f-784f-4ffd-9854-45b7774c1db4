package com.zksr.common.core.enums.request;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
* 系统来源
* @date 2024/7/22 11:29
* <AUTHOR>
*/
@Getter
@AllArgsConstructor
public enum SyncSourceType {
    B2B(0, "B2B","B2B"),
    ANNTOERP(1, "安得ERP","ANNTO"),
    ERP11(2, "ERP11.0","ERP11"),
    FSTERP(3, "福商通ERP","FSTERP"),
    XXERP(4, "小象ERP","XXERP"),
    XWJERP(5, "湘无界ERP","XWJERP"),
    HBNERP(6, "好帮你ERP","HBNERP"),
    FUYUERP(7, "富屿ERP", "FUYUERP"),
    ;

    /** 系统编号 */
    private final Integer code;
    /** 系统名称 */
    private final String name;
    /** 系统类型 */
    private final String type;

    public static String matchingType(Integer code){
        if(Objects.isNull(code)){ return SyncSourceType.B2B.type;}

        for (SyncSourceType source: SyncSourceType.values()) {
            if(source.code.equals(code)){
                return source.type;
            }
        }
        return SyncSourceType.B2B.type;
    }



}
