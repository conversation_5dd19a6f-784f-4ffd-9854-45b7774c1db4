package com.zksr.common.core.utils.bean;

import cn.hutool.core.bean.BeanUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.core.web.pojo.PageResult;

import java.util.List;
import java.util.Objects;

public class HutoolBeanUtils {
    public static <T> T toBean(Object source, Class<T> targetClass) {
        if (Objects.isNull(source)) {
            return null;
        }
        return BeanUtil.toBean(source, targetClass);
    }

    public static <S, T> List<T> toBean(List<S> source, Class<T> targetType) {
        if (source == null) {
            return null;
        }
        return CollectionUtils.convertList(source, s -> toBean(s, targetType));
    }

    public static  <S, T> PageResult<T> toBean(PageResult<S> source, Class<T> targetType) {
        if (source == null) {
            return null;
        }
        return new PageResult<>(toBean(source.getList(), targetType), source.getTotal());
    }
}
