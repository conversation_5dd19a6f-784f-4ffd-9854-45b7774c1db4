package com.zksr.common.core.utils;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
public class HttpClientPool {

    private static CloseableHttpClient closeableHttpClient ;

    private static ObjectMapper objectMapper = new ObjectMapper();

    private static RequestConfig DEFAULT_REQUEST_CONFIG = RequestConfig.DEFAULT;

    private HttpClientPool() {
    }

    static {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false);
        init(new HttpPoolConfig());
    }

    private static void init(HttpPoolConfig config){
        config = config == null? new HttpPoolConfig() : config;
        try {
            DEFAULT_REQUEST_CONFIG = RequestConfig.custom()
                    .setConnectTimeout(config.getConnectTimeout())
                    .setSocketTimeout(config.getSocketTimeout())
                    .setConnectionRequestTimeout(config.getConnectionRequestTimeout())
                    .build();

            Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.INSTANCE)
                    .register("https", sslFactory())
                    .build();

            PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
            connectionManager.setDefaultMaxPerRoute(config.getMaxConnectionsPerRoute());
            connectionManager.setMaxTotal(config.getMaxTotalConnections());

            HttpClientBuilder hcb = HttpClients.custom()
                    .setConnectionManager(connectionManager)
                    .setDefaultRequestConfig(DEFAULT_REQUEST_CONFIG)
                    .setUserAgent(config.getUserAgent())
                    .evictIdleConnections(5000, TimeUnit.MILLISECONDS)
                    .disableAutomaticRetries();

            closeableHttpClient = hcb.build();
            /*IdleConnectionMonitorThread monitor = new IdleConnectionMonitorThread(connectionManager);
            monitor.start();*/
        }catch (Exception e){
            log.error("HttpClientPool init error, it will cause NullPointException in http's connecting");
        }
    }

    private static SSLConnectionSocketFactory sslFactory() throws NoSuchAlgorithmException, KeyStoreException,KeyManagementException {
        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, (chain, authType) -> true).build();
        return new SSLConnectionSocketFactory(sslContext);
    }

    public static CloseableHttpClient getHttpClient(){
        return closeableHttpClient;
    }

    public static ResultHandler delete(String url, Object params, Map<String, String> headers, ContentType contentType) {
        long startTime = System.currentTimeMillis();
        String param = params instanceof String ? (String) params : getParams(params);
        if(log.isDebugEnabled()){
            log.debug("http[DELETE] url:[{}], params:[{}], headers:[{}]", url, param, headers);
        }
        contentType = Optional.ofNullable(contentType).orElse(ContentType.APPLICATION_JSON);
        HttpDelete httpDelete = new HttpDelete(buildURI(url, param));
        if(headers != null) {
            headers.forEach(httpDelete::addHeader);
        }
        Request request = new Request(url, param, headers, contentType);
        try(CloseableHttpResponse response = getHttpClient().execute(httpDelete)) {
            ResultHandler resultHandler = dealResponse(response, request, startTime);
            if(log.isDebugEnabled()) {
                log.debug("http[DELETE] url:[{}], result:[{}], cost:[{}]", url, resultHandler.limitString(), resultHandler.getCost());
            }
            return resultHandler;
        }catch (IOException ie){
            log.error(" ResultHandlerdelete异常,", ie);
            throw new RuntimeException(ie);
        }
    }

    public static ResultHandler put(String url, Object params, Map<String, String> headers, ContentType contentType) {
        long startTime = System.currentTimeMillis();
        String param = params instanceof String ? (String) params : getParams(params);
        if(log.isDebugEnabled()) {
            log.debug("http[PUT] url:[{}], params:[{}], headers:[{}]", url, params, headers);
        }
        contentType = Optional.ofNullable(contentType).orElse(ContentType.APPLICATION_JSON);
        HttpPut httpPut = new HttpPut(buildURI(url, param));
        if(headers != null) {
            headers.forEach(httpPut::addHeader);
        }
        Request request = new Request(url, param, headers, contentType);
        try(CloseableHttpResponse response = getHttpClient().execute(httpPut)) {
            ResultHandler resultHandler = dealResponse(response, request, startTime);
            if(log.isDebugEnabled()) {
                log.debug("http[PUT] url:[{}], result:[{}], cost:[{}]", url, resultHandler.limitString(), resultHandler.getCost());
            }
            return resultHandler;
        }catch (IOException ie){
            log.error(" ResultHandlerput异常,", ie);
            throw new RuntimeException(ie);
        }
    }

    public static ResultHandler get(String url, String params, Map<String, String> headers, RequestConfig reqcfg) {
        long startTime = System.currentTimeMillis();
        if(log.isDebugEnabled()) {
            log.debug("http[GET] url:[{}], params:[{}], headers:[{}]", url, params, headers);
        }
        Request request = new Request(url, params, headers, null);
        HttpGet httpGet = new HttpGet(buildURI(url, params));
        httpGet.setConfig(Optional.ofNullable(reqcfg).orElse(DEFAULT_REQUEST_CONFIG));
        if(headers != null) headers.forEach(httpGet::addHeader);
        try(CloseableHttpResponse response = getHttpClient().execute(httpGet)) {
            ResultHandler resultHandler = dealResponse(response, request, startTime);
            if(log.isDebugEnabled()) {
                log.debug("http[GET] url:[{}], result:[{}], cost:[{}]", url, resultHandler.limitString(), resultHandler.getCost());
            }
            return resultHandler;
        }catch (IOException ie){
            log.error(" ResultHandlerget异常,", ie);
            throw new RuntimeException(ie);
        }
    }

    public static ResultHandler get(String url, String params, Map<String, String> headers) {
        return get(url, params, headers, null);
    }

    public static ResultHandler get(String url, Object params, Map<String, String> headers) {
        return get(url, getParams(params), headers);
    }

    public static ResultHandler get(String url, String params) {
        return get(url, params, null);
    }

    public static ResultHandler get(String url, Object params) {
        return get(url, params, null);
    }

    public static ResultHandler get(String url) {
        return get(url, null, null);
    }

    public static ResultHandler post(String url, String params, Map<String, String> headers, ContentType contentType, RequestConfig reqcfg) {
        long startTime = System.currentTimeMillis();
        contentType = Optional.ofNullable(contentType).orElse(ContentType.APPLICATION_JSON);
        if(log.isDebugEnabled()) {
            log.debug("http[POST] url:[{}],mimeType:[{}], params:[{}], headers:[{}]", url, contentType.getMimeType(), params, headers);
        }
        Request request = new Request(url, params, headers, contentType);
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(Optional.ofNullable(reqcfg).orElse(DEFAULT_REQUEST_CONFIG));
        if (StringUtils.isNotBlank(params)){
            httpPost.setEntity(new StringEntity(params, contentType));
        }
        if(headers != null){
            headers.forEach(httpPost::addHeader);
        }
        try(CloseableHttpResponse response = getHttpClient().execute(httpPost)) {
            ResultHandler resultHandler = dealResponse(response, request, startTime);
            if(log.isDebugEnabled()) {
                log.debug("http[POST] url:[{}], result:[{}], cost:[{}]", url, resultHandler.limitString(), resultHandler.getCost());
            }
            return resultHandler;
        }catch (IOException ie){
            throw new RuntimeException(ie);
        }
    }

    public static ResultHandler post(String url, String params, Map<String, String> headers, ContentType contentType) {
        return post(url, params, headers, contentType, null);
    }

    public static ResultHandler post(String url, Object params, Map<String, String> headers, ContentType contentType) {
        return post(url, getParams(params), headers, contentType);
    }

    public static ResultHandler post(String url, String params, Map<String, String> headers) {
        return post(url, params, headers, null);
    }

    public static ResultHandler post(String url, Object params, Map<String, String> headers) {
        return post(url, params, headers, null);
    }

    public static ResultHandler post(String url, String params) {
        return post(url, params, null, null);
    }

    public static ResultHandler post(String url, Object params) {
        return post(url, params, null, null);
    }

    public static ResultHandler post(String url) {
        return post(url, null, null, null);
    }

    public static ResultHandler put(String url, String params, Map<String, String> headers, ContentType contentType, InputStream is) {
        long startTime = new Date().getTime();
        contentType = Optional.ofNullable(contentType).orElse(ContentType.APPLICATION_JSON);
        if(log.isDebugEnabled()) {
            log.debug("http[PUT] url:[{}], mimeType:[{}], params:[{}], headers:[{}]", url, contentType.getMimeType(), params, headers);
        }
        Request request = new Request(url, params, headers, contentType);
        HttpPut httpPut = new HttpPut(url);
        if (StringUtils.isNotBlank(params)) httpPut.setEntity(new StringEntity(params, contentType));
        if(headers != null) headers.forEach(httpPut::addHeader);
        if(is != null) httpPut.setEntity(new InputStreamEntity(is, ContentType.TEXT_PLAIN));

        try(CloseableHttpResponse response = getHttpClient().execute(httpPut)) {
            ResultHandler result = dealResponse(response, request, startTime);
            if(log.isDebugEnabled()) {
                log.debug("http[PUT] url:[{}], result:[{}], cost:[{}]", url, result.limitString(), result.getCost());
            }
            return result;
        }catch (IOException ie){
            throw new RuntimeException(ie);
        }
    }

    private static URI buildURI(String url, String json) {
        try {
            URIBuilder builder = new URIBuilder(url) ;
            if (StringUtils.isNotBlank(json)) {
                Map<String, Object> map = objectMapper.readValue(json, Map.class);
                map.forEach((key, value) -> {
                    if (null != value) {
                        builder.setParameter(key, value.toString()) ;
                    }
                });
            }
            return builder.build() ;
        }catch (URISyntaxException ue){
            log.error("Get request url parse error");
            throw new RuntimeException(ue);
        }catch (IOException e){
            log.error("Get request param parse error");
            throw new RuntimeException(e);
        }
    }

    private static String getParams(Object params) {
        try {
            String str = null;
            if (null != params) {
                str = objectMapper.writeValueAsString(params);
            }
            return str;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private static ResultHandler dealResponse(CloseableHttpResponse response, Request request, long startTime){
        HttpEntity entity = response.getEntity();
        try {

            ResultHandler resultHandler = new ResultHandler(EntityUtils.toByteArray(entity), objectMapper);
            if (response.getStatusLine() != null) {
                resultHandler.setStatusCode(response.getStatusLine().getStatusCode());
            }
            resultHandler.setRequest(request);
            resultHandler.setCost(System.currentTimeMillis() - startTime);
            return resultHandler;

        }catch (IOException e){
            throw new RuntimeException(e);
        }finally {
            try {
                EntityUtils.consume(entity);
            }catch (IOException e){
                log.error("Http entity's inputStream closed error");
            }
            if(response != null){
                try {
                    response.close();
                }catch (Exception e){
                }
            }
        }
    }

    //用于监控空闲的连接池连接
    @Slf4j
    private static final class IdleConnectionMonitorThread extends Thread {
        private final HttpClientConnectionManager connMgr;
        private volatile boolean shutdown;

        private static final int MONITOR_INTERVAL_MS = 2000;
        private static final int IDLE_ALIVE_MS = 5000;

        public IdleConnectionMonitorThread(HttpClientConnectionManager connMgr) {
            super();
            this.connMgr = connMgr;
            this.shutdown = false;
        }

        @Override
        public void run() {
            if(log.isDebugEnabled()) {
                log.debug("--------------IdleConnectionMonitor running--------------");
            }
            try {
                while (!shutdown) {
                    synchronized (this) {
                        wait(MONITOR_INTERVAL_MS);
                        connMgr.closeExpiredConnections();
                        connMgr.closeIdleConnections(IDLE_ALIVE_MS, TimeUnit.MILLISECONDS);
                    }
                }
            } catch (InterruptedException e) {
                if(log.isDebugEnabled()) {
                    log.debug("--------------IdleConnectionMonitor closing--------------");
                }
                shutdown();
                Thread.currentThread().interrupt();
            }
        }

        // 关闭后台连接
        private void shutdown() {
            shutdown = true;
            synchronized (this) {
                notifyAll();
            }
        }
    }
}

