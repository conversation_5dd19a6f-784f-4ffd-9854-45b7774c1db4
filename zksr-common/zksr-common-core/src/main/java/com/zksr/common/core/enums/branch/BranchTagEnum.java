package com.zksr.common.core.enums.branch;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "门店标签定义")
public enum BranchTagEnum {
    LEVEL_A("level_a", "level", "A级"),
    LEVEL_B("level_b", "level", "B级"),
    LEVEL_C("level_c", "level", "C级"),
    LEVEL_D("level_d", "level", "D级"),
    LEVEL_E("level_e", "level", "E级"),
    CLASS_A("class_a", "class", "高占比"),
    CLASS_B("class_b", "class", "中占比"),
    CLASS_C("class_c", "class", "低占比"),
    PROFIT_A("profit_a", "profit", "高毛利"),
    PROFIT_B("profit_b", "profit", "中毛利"),
    PROFIT_C("profit_c", "profit", "低毛利"),
    CREATE_ORDER_A("create_order_a", "create_order", "高频次"),
    CREATE_ORDER_B("create_order_b", "create_order", "中频次"),
    CREATE_ORDER_C("create_order_c", "create_order", "低频次"),
    ACTIVE_CUST("active_cust", "active_cust", "活跃"),
    ;
    @ApiModelProperty("标签值")
    private final String tag;

    @Getter
    @ApiModelProperty("标签类型")
    private final String type;

    @Getter
    @ApiModelProperty("标签名称")
    private final String name;

    BranchTagEnum(String tag, String type, String name) {
        this.tag = tag;
        this.type = type;
        this.name = name;
    }

    public static List<BranchTagEnum> getLevelList() {
        ArrayList<BranchTagEnum> branchTagEnums = new ArrayList<>();
        for (BranchTagEnum anEnum : values()) {
            if ("level".equals(anEnum.getType())) {
                branchTagEnums.add(anEnum);
            }
        }
        return branchTagEnums;
    }

    @JsonValue
    public String getTag() {
        return tag;
    }

    @JsonCreator
    public static BranchTagEnum fromValue(String value) {
        for (BranchTagEnum b : BranchTagEnum.values()) {
            if (b.tag.equals(value)) {
                return b;
            }
        }
        return null;
    }
}
