package com.zksr.common.core.domain.vo.openapi;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
*  收款单支付详情 对外接口信息
* @date 2024/10/19 17:34
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ReceiptPayOpenDTO {

    @ApiModelProperty(value = "销售入驻商订单/售后入驻商订单 -- 支付流水号")
    private String sheetTradeNo;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    private String platform;

/*    *//** 支付方式;0-在线支付 1-储值支付 *//*
    @Excel(name = "支付方式;0-在线支付 1-储值支付")
    private String payWay;*/

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal payAmt;




}
