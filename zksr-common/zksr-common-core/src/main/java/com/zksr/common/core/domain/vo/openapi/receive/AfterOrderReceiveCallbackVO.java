package com.zksr.common.core.domain.vo.openapi.receive;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
*  退单接收通知实体
* @date 2024/9/18 19:02
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("退单接收通知实体")
public class AfterOrderReceiveCallbackVO {

    /** 售后订单号(入驻商售后订单编号) */
    @Excel(name = "售后订单号")
    @ApiModelProperty(value = "售后订单号")
    private String supplierAfterNo;

    /** 外部订单号 */
    @Excel(name = "外部(ERP)售后订单号")
    @ApiModelProperty(value = "外部(ERP)售后订单号")
    private String sourceOrderNo;

}
