package com.zksr.common.core.utils;

import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.TokenConstants;
import com.zksr.common.core.text.Convert;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import java.util.Map;

/**
 * Jwt工具类
 *
 * <AUTHOR>
 */
public class OpenapiJwtUtils {

    //TODO 需要做成可配置的
    public static String secret = TokenConstants.MALL_SECRET;

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    public static String createToken(Map<String, Object> claims) {
        String token = Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.HS512, secret).compact();
        return token;
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    public static Claims parseToken(String token) {
        return Jwts.parser().setSigningKey(secret).parseClaimsJws(token).getBody();
    }

    /**
     * 根据令牌获取用户标识
     *
     * @param token 令牌
     * @return 用户ID
     */
    public static String getOpensourceKey(String token) {
        Claims claims = parseToken(token);
        return getValue(claims, OpenapiSecurityConstants.OPENSOURCE_KEY);
    }

    /**
     * 根据令牌获取用户标识
     *
     * @param claims 身份信息
     * @return 用户ID
     */
    public static String getOpensourceKey(Claims claims)  {
        return getValue(claims, OpenapiSecurityConstants.OPENSOURCE_KEY);
    }

    /**
     * 根据令牌获取用户ID
     *
     * @param token 令牌
     * @return 用户ID
     */
    public static String getOpensourceId(String token) {
        Claims claims = parseToken(token);
        return getValue(claims, OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID);
    }

    /**
     * 根据身份信息获取用户ID
     *
     * @param claims 身份信息
     * @return 用户ID
     */
    public static String getOpensourceId(Claims claims) {
        return getValue(claims, OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID);
    }

    /**
     * 根据令牌获取平台商id
     *
     * @param token 令牌
     * @return 用户名
     */
    public static String getSysCode(String token) {
        Claims claims = parseToken(token);
        return getValue(claims, OpenapiSecurityConstants.SYS_CODE);
    }

    /**
     * 根据身份信息获取平台商id
     *
     * @param claims 身份信息
     * @return 用户名
     */
    public static String getSysCode(Claims claims) {
        return getValue(claims, OpenapiSecurityConstants.SYS_CODE);
    }

    /**
     * 根据身份信息获取键值
     *
     * @param claims 身份信息
     * @param key 键
     * @return 值
     */
    public static String getValue(Claims claims, String key) {
        return Convert.toStr(claims.get(key), "");
    }
}
