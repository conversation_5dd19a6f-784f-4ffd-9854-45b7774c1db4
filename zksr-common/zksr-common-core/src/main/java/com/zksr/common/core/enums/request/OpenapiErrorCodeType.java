package com.zksr.common.core.enums.request;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
*  接收日志需要重发的错误信息枚举类
* @date 2024/9/18 14:52
* <AUTHOR>
*/
@Getter
@AllArgsConstructor
public enum OpenapiErrorCodeType {
    TRD_SUPPLIER_ORDER_CHECK_DELIVERY_STATE (1_009_001_004, "当前状态不能确认收货"),

    ;
    private final Integer code;

    /** 接口信息 */
    private final String msg;

    /**
     * 根据错误编码匹配 枚举类型
     * @param code
     * @return
     */
    public static boolean checkErrorCode(Integer code){
        for (OpenapiErrorCodeType value : OpenapiErrorCodeType.values()){
            if(code.equals(value.code)){
                return true;
            }
        }
        return false;
    }
}
