package com.zksr.common.core.domain.erp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class ErpReceiptDTO {

    @ApiModelProperty(value = "往来单位名称")
    private String companyName;

    @ApiModelProperty(value = "往来单位编号")
    private String companyNo;

    @ApiModelProperty(value = "往来单位类型 门店   字符：1")
    private String companyType;

    @ApiModelProperty(value = "业务员编号")
    private String employeeNo;

    @ApiModelProperty(value = "首款类型（传 数字 0）")
    private Integer receiptType;

    @ApiModelProperty(value = "单据日期（传b2b创建时间）")
    private Date sheetDate;

    @ApiModelProperty(value = "收款类型（传 数字 0）")
    private Integer sheetType;

    @ApiModelProperty(value = "收款单还是负款单（“+” 收  “-”  负）")
    private String changeType;

    @ApiModelProperty(value = "入驻商单据号（销售入驻商订单或售后入驻商订单）")
    private String sheetNo;

    @ApiModelProperty(value = "明细数据集合")
    private List<ErpReceiptDtlDTO> subList;

    @ApiModel(value = "明细数据集合")
    @Data
    public static class ErpReceiptDtlDTO {
        @ApiModelProperty(value = "账户名称（传 字符 “06”）")
        private String accountNo;

        @ApiModelProperty(value = "金额")
        private BigDecimal sheetAmt;
    }

}
