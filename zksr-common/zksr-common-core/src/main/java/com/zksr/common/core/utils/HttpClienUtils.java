package com.zksr.common.core.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.domain.ErpRequest;
import com.zksr.common.core.domain.erp.ApiDataModel;
import com.zksr.common.core.enums.request.RequestType;
import com.zksr.common.core.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.security.interfaces.RSAPublicKey;
import java.util.HashMap;

/**
 * 封装对接外部系统的http请求方法
 */

@Slf4j
public class HttpClienUtils {

    private static String CONTENTTYPE="Content-Type";
    private static String APPLICATION="application/json";
    private static String CHARSET_UTF_8="UTF-8";

    /**
     * 重用 HttpClient 实例可以显著提高性能，因为它允许连接重用和复用 TCP 连接，而不是每次请求都重新建立连接。
     */
    private static final CloseableHttpClient httpClient;

    static {
        // 创建连接池配置
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(50000) // 从连接池获取连接的超时时间
                .setConnectTimeout(50000) // 建立连接的超时时间
                .setSocketTimeout(50000) // 数据传输的超时时间
                .build();

        // 创建连接池并设置配置
        httpClient = HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .setMaxConnTotal(100) // 设置最大连接数
                .setMaxConnPerRoute(20) // 设置每个路由的最大连接数
                .build();
    }

    /**
     * 推送订单数据到erp系统
     * @param url 目标系统地址
     * @param publicKey 密钥
     * @param data 推送的数据
     * @return 返回响应结果
     * @throws Exception
     */
    public static String doPost(String url,String publicKey,Object data) throws Exception{

        //将数据转换为json字符串
        String requestData = JSON.toJSONString(data);
        //对数据进行签名加密
        RSAPublicKey key = ZkRSAUtils.getPublicKey(publicKey);
        String sign = ZkRSAUtils.publicEncrypt(requestData, key);

        //封装请求参数
        ErpRequest request = new ErpRequest();
        request.setReqId(IdUtils.fastSimpleUUID());
        request.setReqTime(DateUtils.getSystemDate());
        request.setBizData(sign);
        String stringEntity = JSON.toJSONString(request);

        //创建HttpPost对象
        HttpPost httpPost = new HttpPost(url);
        //设置请求头信息
        httpPost.setHeader(CONTENTTYPE, APPLICATION);
        //设置请求体
        httpPost.setEntity(new StringEntity(stringEntity, CHARSET_UTF_8));
        //执行请求
        try (CloseableHttpResponse execute = httpClient.execute(httpPost)) {
            //获取响应结果
            String responseString = EntityUtils.toString(execute.getEntity(), "UTF-8");
            log.info("HTTP POST响应: {}", responseString);
            return responseString;
        }
    }


    public static String doPost(String url, JSONObject stringEntity) throws Exception{

        //将数据转换为json字符串
//        String requestData = JSON.toJSONString(data);
        //对数据进行签名加密
//        RSAPublicKey key = ZkRSAUtils.getPublicKey(publicKey);
//        String sign = ZkRSAUtils.publicEncrypt(requestData, key);

        //封装请求参数
//        ErpRequest request = new ErpRequest();
//        request.setReqId(IdUtils.fastSimpleUUID());
//        request.setReqTime(DateUtils.getSystemDate());
//        request.setBizData(sign);
//        String stringEntity = JSON.toJSONString(stringEntity);

        //创建HttpPost对象
        HttpPost httpPost = new HttpPost(url);
        //设置请求头信息
        httpPost.setHeader(CONTENTTYPE, APPLICATION);
        //设置请求体
        httpPost.setEntity(new StringEntity(String.valueOf(stringEntity), CHARSET_UTF_8));
        //执行请求
        try (CloseableHttpResponse execute = httpClient.execute(httpPost)) {
            //获取响应结果
            String responseString = EntityUtils.toString(execute.getEntity(), "UTF-8");
            log.info("HTTP POST响应: {}", responseString);
            return responseString;
        }
    }


    public static String doPost(String url, String publicKey, ApiDataModel<?> data,String strategyId,RequestType requestType) throws Exception{

        log.info("开始请求第三方系统：url:{},strategyId:{}",url,strategyId);
        //将数据转换为json字符串
        String requestData = JSON.toJSONString(data);
        //对数据进行签名加密
        RSAPublicKey key = ZkRSAUtils.getPublicKey(publicKey);
        String sign = ZkRSAUtils.publicEncrypt(requestData, key);

        //封装请求参数
//        ApiRequestBody<Object> body = new ApiRequestBody<>();
//        body.setDataModel(data);
//        body.setBizData(sign);
//        body.setRequestType(RequestType.SYNC_B2B_AFTER_SHEET);
//        body.setReqId(IdUtils.fastSimpleUUID());
//        String stringEntity = JSON.toJSONString(body);

        HashMap<String, Object> body = new HashMap<>();
        body.put("reqId",IdUtils.fastSimpleUUID());
        body.put("bizData",sign);
        body.put("requestType",requestType);
        body.put("reqTime",DateUtils.getSystemDate());
        String stringEntity = JSON.toJSONString(body);


        //创建HttpPost对象
        HttpPost httpPost = new HttpPost(url);
        //设置请求头信息
        httpPost.setHeader(CONTENTTYPE, APPLICATION);
        httpPost.setHeader("strategyId",strategyId);
        //设置请求体
        httpPost.setEntity(new StringEntity(stringEntity, CHARSET_UTF_8));
        //执行请求
        try (CloseableHttpResponse execute = httpClient.execute(httpPost)) {
            //获取响应结果
            String responseString = EntityUtils.toString(execute.getEntity(), "UTF-8");
            log.info("HTTP POST响应: {}", responseString);
            return responseString;
        }
    }
}
