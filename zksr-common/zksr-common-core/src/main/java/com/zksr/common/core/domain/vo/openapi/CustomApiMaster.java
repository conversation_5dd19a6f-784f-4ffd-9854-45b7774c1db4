package com.zksr.common.core.domain.vo.openapi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 实体类-自定义接口
 * <AUTHOR> 2024/03/07
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomApiMaster{
    private String apiNo;			    //  接口编号
    private String apiName;		        //  接口名
    private Integer apiStatus;           //  接口状态 0禁用 1启用
    private String apiAddress;          //  接口地址
    private String apiRequestProtocol;  //  请求协议 http
    private String apiRequestType;      //  请求类型 POST GET PUT DELETE
    private String apiDataType;         //  数据类型 json form-data xml
    private String apiNotes;            //  接口备注
    private String modelType;	        //  数据类型 YH要货单 DR配送退货单 DO配送出库单...
    private String isSend;              //  是否发送 0接收接口 1下发接口
    private String isAsync;             //  是否异步 0否 1是
    private String isBatch;             //  是否批量下发 0否 1是
    private String jsonExample;         //  json示例 预览json
    private String systemType;          //  对接的第三方系统类型 financial 财务,WMS,ERP
    private String systemName;          //  对接的第三方系统名称
    private String timedType;           //  定时发送类型 minute 每分钟,hour 每小时,day 每天,month 每月,year 每年
    private Date timedDate;             //  定时时间

}
