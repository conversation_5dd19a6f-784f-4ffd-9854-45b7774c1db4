package com.zksr.common.core.transaction;

import lombok.extern.slf4j.Slf4j;

import java.util.Stack;

@SuppressWarnings("rawtypes")
@Slf4j
public class BasicTransactionHandler {

    private final TransactionHandler[] handlers;

    private final Stack<TransactionHandler> stack = new Stack<>();

    public BasicTransactionHandler(TransactionHandler... handlers) {
        this.handlers = handlers;
    }

    public static BasicTransactionHandler of(TransactionHandler... handlers) {
        return new BasicTransactionHandler(handlers);
    }

    public void execute() {
        try {
            for (TransactionHandler handler : handlers) {
                if (handler == null) {
                    continue;
                }
                log.info("{}开始执行事务", handler.getClass().getSimpleName());
                handler.doBusiness();
                log.info("{}事务执行成功", handler.getClass().getSimpleName());
                stack.push(handler);
            }
        } catch (Exception e) {
            log.error("事务执行发生异常，即将回滚, error:", e);
            while (!stack.isEmpty()) {
                TransactionHandler handler = stack.pop();
                log.info("{}开始回滚事务", handler.getClass().getSimpleName());
                try {
                    handler.rollback();
                    log.info("{}事务回滚完成", handler.getClass().getSimpleName());
                } catch (Exception e1) {
                    log.error("{}事务回滚失败, error:", handler.getClass().getSimpleName(), e1);
                }
            }
            throw e;
        }
    }

}
