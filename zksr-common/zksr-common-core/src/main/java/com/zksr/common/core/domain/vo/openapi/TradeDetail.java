package com.zksr.common.core.domain.vo.openapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("b2b推送订单到erp 订单明细请求参数")
public class TradeDetail {

    @ApiModelProperty(value = "商品编号")
    private String itemNo;

    @ApiModelProperty(value = "销售数量")
    private String saleQty;

    @ApiModelProperty(value = "原销售金额")
    private String itemAmt;

    @ApiModelProperty(value = "实付销售金额")
    private String itemRealAmt;

    @ApiModelProperty(value = "是否赠品")
    private String giftFlag;

    @ApiModelProperty(value = "是否平台商品")
    private String itemFlag;

    @ApiModelProperty(value = "行号")
    private String line;
}
