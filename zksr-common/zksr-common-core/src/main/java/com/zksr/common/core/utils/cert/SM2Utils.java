package com.zksr.common.core.utils.cert;

import cn.hutool.crypto.asymmetric.SM2;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.Signature;
import java.security.cert.X509Certificate;
import java.util.Base64;
import java.util.Objects;

/**
 * 国密SM2算法工具类
 *
 * @description:
 * @since:
 */
public class SM2Utils {

    private static Logger logger = LoggerFactory.getLogger(SM2Utils.class);

    private SM2Utils() {
    }

    public static String sign(String plainText, String privateKeyStr) {
        SM2 sm2 = new SM2(privateKeyStr, null);
        byte[] data = plainText.getBytes(StandardCharsets.UTF_8);
        return Base64.getEncoder().encodeToString(sm2.sign(data, null));
    }

    public static boolean verify(String plainText, String signText, X509Certificate certificate) {
        Objects.requireNonNull(plainText, "plainText required");
        Objects.requireNonNull(signText, "signText required");
        return verify(plainText.getBytes(StandardCharsets.UTF_8), Base64.getDecoder().decode(signText), certificate);
    }

    private static boolean verify(byte[] plainData, byte[] signature, X509Certificate certificate) {
        try {
            Signature verifySign = Signature.getInstance(certificate.getSigAlgName());
            verifySign.initVerify(certificate);
            verifySign.update(plainData);
            return verifySign.verify(signature);
        } catch (Exception e) {
            logger.error("verify", e);
            return false;
        }
    }

    public static String encryptToBase64(String plainText, X509Certificate cert) {
        byte[] cipherData = encrypt(plainText.getBytes(StandardCharsets.UTF_8), cert);
        return Base64.getEncoder().encodeToString(cipherData);
    }

    private static byte[] encrypt(byte[] message, X509Certificate cert) {
        SM2 sm2 = new SM2(null, cert.getPublicKey());
        sm2.setMode(SM2Engine.Mode.C1C3C2);
        return sm2.encrypt(message);
    }

    public static String decryptBase64Message(String cipherText, String privateKeyStr) {
        byte[] base64Data = Base64.getDecoder().decode(cipherText);
        byte[] bytes = decrypt(base64Data, privateKeyStr);
        return StringUtils.toEncodedString(bytes, StandardCharsets.UTF_8);
    }

    private static byte[] decrypt(byte[] message, String privateKeyStr) {
        SM2 sm2 = new SM2(privateKeyStr, null);
        sm2.setMode(SM2Engine.Mode.C1C3C2);
        return sm2.decrypt(message);
    }

    public static void main(String[] args) {

    }
}