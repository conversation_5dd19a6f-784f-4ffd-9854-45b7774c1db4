package com.zksr.common.core.domain.vo.openapi.receive;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
*
 * OpenAPI 订单收货回传（接收拒收退货单）主表实体
* <AUTHOR>
* @date 2024/6/9 11:15
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("订单收货回传（接收拒收退货单）")
public class ReceiveOrderTakeDeliveryVO {

    /** 订单号(B2B入驻商销售订单编号)*/
    @Excel(name = "订单号(B2B入驻商销售订单编号)")
    @ApiModelProperty(value = "订单号(B2B入驻商销售订单编号)")
    @NotNull(message = "B2B入驻商销售订单编号不能为空")
    private String supplierOrderNo;

/*    *//** 外部(ERP)拒收退货单订单号 *//*
    @Excel(name = "外部(ERP)拒收退货单订单号")
    @ApiModelProperty(value = "外部(ERP)拒收退货单订单号")
    private String sourceShjOrderNo;*/

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

/*    *//** 收货是否收款 0否 1是  默认否*//*
    @Excel(name = "收货是否收款 0否 1是")
    @ApiModelProperty(value = "收货是否收款 0否 1是 默认否")
    private Integer isProceeds = 0;*/

    /** 是否校验订单详情商品条数 开启时 推送10条数据 必须返回对应的十条数据 */
    @Excel(name = "是否校验订单详情商品条数")
    @ApiModelProperty(value = "是否校验订单详情商品条数",hidden = true)
    private boolean checkDetailNumFlag = Boolean.TRUE;

    /**
     * 司机名称
     */
    @ApiModelProperty(value = "司机名称 非必填")
    private String driverName;

    /**
     * 司机电话
     */
    @ApiModelProperty(value = "司机电话 非必填")
    private String driverPhone;

    /** 订单收货回传（接收拒收退货单）详情实体 */
    @Excel(name = "订单收货回传（接收拒收退货单）详情实体")
    @ApiModelProperty(value = "订单收货回传（接收拒收退货单）详情实体")
    private List<ReceiveOrderTakeDeliveryDetailVO> detail;

}
