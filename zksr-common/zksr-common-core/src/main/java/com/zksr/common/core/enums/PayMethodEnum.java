package com.zksr.common.core.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付方式
 * @date 2024/3/8 11:35
 */
@Getter
public enum PayMethodEnum {
    WX("wx", "微信支付"),
    ALIPAY("alipay", "支付宝支付"),
    ;
    private String method;
    private String name;

    PayMethodEnum(String method, String name) {
        this.method = method;
        this.name = name;
    }


    public static boolean wx(String obj) {
        return Objects.equals(WX.method, obj);
    }

    public static boolean alipay(String obj) {
        return Objects.equals(ALIPAY.method, obj);
    }
}
