package com.zksr.common.core.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/25 16:46
 */
@Getter
public enum WeekEnum {
    MONDAY("MONDAY", "星期一"),
    TUESDAY("TUESDAY", "星期二"),
    WEDNESDAY("WEDNESD<PERSON><PERSON>", "星期三"),
    THURSDAY("THURSDAY", "星期四"),
    FRIDAY("FRIDAY", "星期五"),
    SATURDAY("SATURDAY", "星期六"),
    SUNDAY("SUNDAY", "星期日");

    private String en;
    private String zh;

    WeekEnum(String en, String zh) {
        this.en = en;
        this.zh = zh;
    }
}
