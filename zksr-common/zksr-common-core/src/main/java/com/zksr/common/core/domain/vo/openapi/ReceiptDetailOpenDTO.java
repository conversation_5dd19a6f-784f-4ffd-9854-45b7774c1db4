package com.zksr.common.core.domain.vo.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
*  收款单详情 对外接口信息
* @date 2024/9/12 16:24
* <AUTHOR>
*/
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ReceiptDetailOpenDTO {

    @ApiModelProperty(value = "订单类型 销售订单、售后订单、拒收订单")
    private String sheetType;

    @ApiModelProperty(value = "入驻商订单号(销售订单)")
    private String supplierOrderNo;

    @ApiModelProperty(value = "入驻商订单号(售后订单)")
    private String supplierAfterNo;

    @ApiModelProperty(value = "第三方原单据号 入驻商销售对应的原订单号")
    private String sourceOrderNo;

    @ApiModelProperty(value = "第三方原单据号 入驻商售后订单对应的原订单号")
    private String sourceAfterNo;

    @ApiModelProperty(value = "收款金额 存在正负数")
    private BigDecimal receiptAmt;

    @ApiModelProperty(value = "收款精确金额")
    private BigDecimal receiptExactAmt;

    @ApiModelProperty(value = "订单单据日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sheetDate;

    @ApiModelProperty(value = "订单单据日期")
    private String sheetDateString;

    /** 支付方式 0-在线支付 1-储值支付 2-货到付款 */
    @Excel(name = "支付方式", readConverterExp = "数=据字典")
    private String payWay;

    /** 是否收款 收款单使用  0否1是*/
    private Integer isProceeds = 1;

    @ApiModelProperty(value = "支付流水信息集合")
    private List<ReceiptPayOpenDTO> payOpenList;

}
