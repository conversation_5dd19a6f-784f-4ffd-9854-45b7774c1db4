package com.zksr.common.core.enums;

import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AfterHandleStateEnum {

    DCL(1L, "待处理", "保存订单"),
    WTH(2L, "未退货", "同意退货申请"),
    THZ(3L, "退货中", "门店上传退货凭证"),
    YTH(4L, "已退货", "退货收货确认"),
    DTK(5L, "待退款", "退货完成"),
    TKZ(6L, "退款中", "同意退款申请或退款失败重新发起退款"),
    TKFALL(7L, "退款失败", "账户退款失败回调"),
    TKWC(8L, "处理完成", "账户退款成功回调"),
    REFUSE(9L, "已拒绝", "售后取消"),
    CANCEL(10L, "已取消", "售后取消"),
    REVOKE(11L, "已撤销", "售后撤销操作"),
    ;
    /**
     * 编码
     */
    private final Long code;
    /**
     * 名字
     */
    private final String name;

    /**
     * 操作说明
     */
    private final String content;


    /**
     * 是否是取消售后操作
     * @param stateCode
     * @return
     */
    public static boolean isCalcel(Long stateCode) {
        if (ToolUtil.isEmpty(stateCode)) {
            return false;
        }
        return CANCEL.getCode().equals(stateCode);
    }

    /**
     * 是否是撤销售后操作
     * @param stateCode
     * @return
     */
    public static boolean isRevoke(Long stateCode) {
        if (ToolUtil.isEmpty(stateCode)) {
            return false;
        }
        return REVOKE.getCode().equals(stateCode);
    }
}
