package com.zksr.common.core.domain.vo.openapi.receive;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
*  订单接收通知实体
* @date 2024/9/18 19:02
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("订单接收通知实体")
public class OrderReceiveCallbackVO {

    /** 订单号(入驻商订单编号) */
    @Excel(name = "B2B入驻商销售订单编号")
    @ApiModelProperty(value = "B2B入驻商销售订单编号")
    @NotNull(message = "B2B入驻商销售订单编号不能为空")
    private String supplierOrderNo;

    /** 外部订单号 */
    @Excel(name = "外部(ERP)销售订单号")
    @ApiModelProperty(value = "外部(ERP)销售订单号")
    private String sourceOrderNo;

    /** 订单详情库存信息 */
    @Excel(name = "订单详情商品库存实体")
    @ApiModelProperty(value = "订单详情商品库存实体")
    private List<OrderReceiveCallbackStockVO> detail;

}
