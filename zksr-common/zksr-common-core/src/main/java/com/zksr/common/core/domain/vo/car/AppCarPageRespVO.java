package com.zksr.common.core.domain.vo.car;

import com.zksr.common.core.domain.dto.car.AppCarItemDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车分页数据
 * @date 2024/5/9 16:25
 */
@Data
@ApiModel(description = "购物车商品分页")
public class AppCarPageRespVO {
    private Long total;
    private List<AppCarItemDTO> list;
}
