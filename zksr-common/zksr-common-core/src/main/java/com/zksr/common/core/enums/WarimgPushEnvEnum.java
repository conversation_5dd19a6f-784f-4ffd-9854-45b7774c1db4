package com.zksr.common.core.enums;

import lombok.Getter;

@Getter
public enum WarimgPushEnvEnum {
    ONLY_PRD("只推送生产环境", 0),
    ALL("推送所有环境", 1);
    private final String name;
    private final Integer type;

    WarimgPushEnvEnum(String name, Integer type) {
        this.name = name;
        this.type = type;
    }

    public static Integer getType(String name) {
        for (WarimgPushEnvEnum transType : WarimgPushEnvEnum.values()) {
            if (transType.getName().equals(name)) {
                return transType.getType();
            }
        }
        return null;
    }

    public static String getName(Integer type) {
        for (WarimgPushEnvEnum transType : WarimgPushEnvEnum.values()) {
            if (transType.getType().equals(type)) {
                return transType.getName();
            }
        }
        return null;
    }
}
