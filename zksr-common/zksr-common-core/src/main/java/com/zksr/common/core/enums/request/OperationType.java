package com.zksr.common.core.enums.request;

/**
 * <AUTHOR>
 */

public enum OperationType {
    /**
     * 新增
     */
    ADD("add", "新增"),
    UPDATE("update", "修改"),
    DELETE("delete", "删除"),
    ADD_OR_UPDATE("add_or_update", "新增或修改"),
    BATCH_UPDATE("batch_update", "批量同步"),
    OTHER("other", "其他");

    private final String code;
    private final String info;

    OperationType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static OperationType findByCode(String code) {
        if (code == null) {
            return OperationType.ADD;
        }
        for (OperationType value : OperationType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
