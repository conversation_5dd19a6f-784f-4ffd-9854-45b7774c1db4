package com.zksr.common.core.domain.vo.openapi.receive;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
* 商品接收操作实体
* @date 2024/8/6 17:50
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpuReceiveVO {

    /** 入驻商id */
    private Long supplierId;

    /** 平台商id */
    @ApiModelProperty("平台商id")
    private Long sysCode;

    /** 商品信息主体  */
    private List<SpuOpenDTO> spuOpenDTO;
}
