package com.zksr.common.core.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 加密方式
 * @date 2024/3/7 18:20
 */
public enum SignatureType {
    /**
     *
     */
    MD5("MD5签名", 0),
    SM3WITHSM2("SM3WITHSM2签名", 1),
    ;


    private String desc;
    private Integer index;

    SignatureType(String desc, Integer index) {
        this.desc = desc;
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getIndex() {
        return index;
    }
}