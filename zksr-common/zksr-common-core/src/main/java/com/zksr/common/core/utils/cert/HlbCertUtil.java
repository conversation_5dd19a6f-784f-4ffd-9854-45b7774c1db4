package com.zksr.common.core.utils.cert;

import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import com.zksr.common.core.enums.SignatureType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.file.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.asn1.ASN1Encodable;
import org.bouncycastle.asn1.ASN1OctetString;
import org.bouncycastle.asn1.ASN1Primitive;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.pkcs.PKCSObjectIdentifiers;
import org.bouncycastle.crypto.DataLengthException;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.engines.SM4Engine;
import org.bouncycastle.crypto.modes.CBCBlockCipher;
import org.bouncycastle.crypto.paddings.PKCS7Padding;
import org.bouncycastle.crypto.paddings.PaddedBufferedBlockCipher;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.crypto.params.ParametersWithIV;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.Hex;

import java.io.*;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.NoSuchProviderException;
import java.security.PrivateKey;
import java.security.Security;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝支付证书解密类
 * @date 2024/3/8 9:44
 */
@Slf4j
public class HlbCertUtil {

    /**
     * 获取证书
     * @param fileContent
     * @return
     * @throws Exception
     */
    public static X509Certificate getX509Certificate(String fileContent) throws Exception {
        try {
            CertificateFactory cf = CertificateFactory.getInstance("X.509", BouncyCastleProvider.PROVIDER_NAME);
            InputStream is = new ByteArrayInputStream(fileContent.getBytes());
            return (X509Certificate) cf.generateCertificate(is);
        } catch (NoSuchProviderException | CertificateException e) {
            //log.error("getX509Certificate", e);
            throw new CertificateException(e.getMessage(), e);
        }
    }

    /**
     * 通过证书文件获取证书字符串
     * @param certFilePath
     * @return
     * @throws Exception
     */
    public static String getX509CertificateStr(String certFilePath) throws Exception {
        byte[] readAllBytes = Files.readAllBytes(Paths.get(certFilePath));
        String fileContent = new String(readAllBytes);
        if ("-----BEGIN CERTIFICATE-----".indexOf(fileContent) < 0) {
            fileContent = "-----BEGIN CERTIFICATE-----\n" + fileContent +
                    "\n-----END CERTIFICATE-----";
        }
        return fileContent;
    }


    /**
     * 获取私钥字符串
     *
     * @param sm2Content
     * @param sm2FilePwd
     * @return
     * @throws IOException
     */
    public static String getPrivateKeyStr(String sm2Content, String sm2FilePwd) throws Exception {
        Objects.requireNonNull(sm2Content, "sm2Content required");
        Objects.requireNonNull(sm2FilePwd, "sm2FilePwd required");
        //新证书是pfx格式
        /*if (sm2FilePath.endsWith(".pfx")) {
            return getPrivateKeyForPfx(sm2FilePath, sm2FilePwd);
        } else {
            return getPrivateKeyForSm2(sm2FilePath, sm2FilePwd);
        }*/
        return getPrivateKeyForSm2(sm2Content, sm2FilePwd);
    }

    /**
     * 固定参数签名 sm2
     */
    public static String getSignAndEncryptedByReq(Map<String, String> map, Set<String> needSignParams, Set<String> needEncryptParams, X509Certificate pubCert, String privateStr) {

        if (needSignParams == null || needSignParams.isEmpty()) {
            throw new ServiceException("验签参数为空.");
        }
        String sm4Key = SM4Utils.generateRandomKey();
        AtomicReference<Boolean> isSm4Encrypt = new AtomicReference<>(false);
        String signatureType = SignatureType.SM3WITHSM2.getDesc();
        if (needEncryptParams == null) {
            needEncryptParams = Sets.newHashSet();
        }
        {
            String finalSm4Key = sm4Key;
            needEncryptParams.forEach(key -> {
                boolean b = sm4Encrypt(map.get(key), key, map, finalSm4Key);
                if (b) {
                    isSm4Encrypt.set(true);
                }
            });

            //请求参数中有需要加密的字段，使用合利宝的公钥的对SM4密钥KEY进行加密处理
            if (isSm4Encrypt.get()) {
                String encryptionKey = SM2Utils.encryptToBase64(sm4Key, pubCert);
                map.put("encryptionKey", encryptionKey);
            }
        }
        StringBuffer sb = doSignStrAppend(map, needSignParams);
        String subStr = sb.toString();
        //使用商户的私钥进行签名
        String sign = SM2Utils.sign(subStr, privateStr);
        map.put("sign", sign);
        log.info("sign 原串(待签名串):{}", subStr);
        log.info(signatureType + "签名结果：{}", sign);
        return sign;
    }


    /**
     * 固定参数签名 md5
     */
    public static String getSignAndEncryptedByReqMd5(Map<String, String> map, Set<String> needSignParams, Set<String> needEncryptParams, String encryptKey, String privateStr) {
        if (needSignParams == null || needSignParams.isEmpty()) {
            throw new ServiceException("验签参数为空.");
        }
        if (Objects.nonNull(needEncryptParams)) {
            needEncryptParams.forEach(key -> {
                des3Encrypt(map.get(key), key, map, encryptKey);
            });
        }
        StringBuffer sb = doSignStrAppend(map, needSignParams);
        String subStr = sb.append(StringPool.AMPERSAND).append(privateStr).toString();
        if (!map.containsKey("firstAnd")) {
            subStr = subStr.substring(1);
        }
        //使用商户的私钥进行签名
        String sign = md5Sign(subStr);
        map.put("sign", sign);
        log.info("sign 原串(待签名串):{}", subStr);
        log.info(SignatureType.MD5 + "签名结果：{}", sign);
        return sign;
    }


    private static String des3Encrypt(String value, String key, Map<String, String> paramMap, String encryptKey) {
        if (StringUtils.isNotBlank(value)) {
            log.info("3des加密前明文:{}", value);
            try {
                value = DesUtils.encode(encryptKey, value.trim());
            } catch (Exception e) {
                log.error("3des error:", e);
            }
            log.info("3des加密后result:{}", value);
            paramMap.put(key, value);
        }
        return value;
    }

    private static String des3Decrypt(String value, String key) {
        if (StringUtils.isNotBlank(value)) {
            log.info("3des密文:{}", value);
            try {
                value = DesUtils.decode(key, value.trim());
            } catch (Exception e) {
                log.error("3des error:", e);
            }
            log.info("3des解密后result:{}", value);
        }
        return value;
    }

    /**
     * md5 签名
     * @param subStr
     * @return
     */
    private static String md5Sign(String subStr) {
        return Disguiser.disguiseMD5(subStr);
    }


    public static StringBuffer doSignStrAppend(Map<String, String> map, Set<String> needSignParams) {
        StringBuffer sb = new StringBuffer();
        for (String key : map.keySet()) {
            if (needSignParams.contains(key)) {
                // do sign append
                String value = map.get(key);
                value = (value == null ? "" : value);
                sb.append(StringPool.AMPERSAND).append(value);
            }
        }
        return sb;
    }

    public static StringBuffer doSignStrAppendSort(Map<String, String> map, Set<String> needSignParams) {
        StringBuffer sb = new StringBuffer();
        for (String key : needSignParams) {
            if (map.containsKey(key)) {
                // do sign append
                String value = map.get(key);
                value = (value == null ? "" : value);
                sb.append(StringPool.AMPERSAND).append(value);
            }
        }
        return sb;
    }

    /**
     * sm4对敏感信息加密
     *
     * @param value
     * @param key
     * @param paramMap
     * @param sm4Key
     * @return
     */
    private static boolean sm4Encrypt(String value, String key, Map<String, String> paramMap, String sm4Key) {
        if (StringUtils.isNotBlank(value)) {
            Preconditions.checkArgument(StringUtils.isNotBlank(sm4Key), "sm4Key Can't be empty");
            log.info("sm4加密前明文:{}", value);
            try {
                value = SM4Utils.encryptBase64(value.trim(), sm4Key);
            } catch (Exception e) {
                log.error("sm4 error:", e);
            }
            log.info("sm4加密后result:{}", value);
            paramMap.put(key, value);
            return true;
        }
        return false;
    }


    public static Map convertBean(Object bean, Map retMap) throws IllegalAccessException {
        Class clazz = bean.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field f : fields) {
            f.setAccessible(true);
        }
        for (Field f : fields) {
            String key = f.toString().substring(f.toString().lastIndexOf(".") + 1);
            if (StringUtils.equalsIgnoreCase("NEED_SIGN_PARAMS", key)
                    || StringUtils.equalsIgnoreCase("NEED_ENCRYPT_OR_DECRYPT_PARAMS", key)) {
                continue;
            }
            Object value = f.get(bean);
            if (value == null) {
                value = "";
            }
            retMap.put(key, value.toString());
        }
        return retMap;
    }


    private static String getPrivateKeyForSm2(String sm2Content, String sm2FilePwd) throws Exception {
        byte[] data = FileUtils.base64ToFile(sm2Content);
        try {
            PKCS12_SM2 p12 = new PKCS12_SM2(data);
            return p12.getPrivateKey(sm2FilePwd);
        } catch (Exception e) {
            //log.error("getPrivateKeyStr", e);
            throw new IllegalArgumentException(e.getMessage(), e);
        }
    }

    static{
        try{
            Security.addProvider(new BouncyCastleProvider());
        }catch(Exception e){
            log.error(" Security.addProvider失败,", e);
        }
    }


    private static class PKCS12_SM2 implements ASN1Encodable, PKCSObjectIdentifiers {

        private ASN1Sequence privateInfo = null;

        public PKCS12_SM2() {
        }


        public PKCS12_SM2(byte[] data) throws Exception {
            load(data);
        }

        public void load(byte[] data) throws Exception {
            ASN1Sequence seq = getDERSequenceFrom(data);
            parseSM2(seq);
        }

        public void parseSM2(ASN1Sequence seq) throws Exception {
            parseSM2Certs((ASN1Sequence) seq.getObjectAt(1), (ASN1Sequence) seq.getObjectAt(2));
        }

        private void parseSM2Certs(ASN1Sequence privateInfo, ASN1Sequence publicInfo) {
            this.privateInfo = privateInfo;
        }

        public static ASN1Sequence getDERSequenceFrom(byte[] encoding) throws Exception {
            if (isDERSequence(encoding)) {
                return ASN1Sequence.getInstance(encoding);
            }
            if (isBERSequence(encoding)) {
                return ASN1Sequence.getInstance(encoding);
            }
            byte[] data;
            try {
                data = Base64.decode(encoding);
            } catch (Exception e) {
                throw new Exception("encoding required base64 encoding", e);
            }
            return ASN1Sequence.getInstance(data);
        }

        public static boolean isBERSequence(byte[] encoding) throws Exception {
            if (encoding == null) {
                throw new Exception("encoding should not be null");
            }
            if (encoding.length < 4) {
                throw new Exception("encoding length less than 4");
            }
            if (encoding[0] != 48) {
                return false;
            }
            int offset = 1;
            int length = encoding[(offset++)] & 0xFF;
            if (length != 128) {
                return false;
            }
            return (encoding[(encoding.length - 1)] == 0) && (encoding[(encoding.length - 2)] == 0);
        }

        public static boolean isDERSequence(byte[] encoding) throws Exception {
            if (encoding == null) {
                throw new Exception("encoding should not be null");
            }
            if (encoding.length < 2) {
                throw new Exception("encoding length less than 4");
            }
            if (encoding[0] != 48) {
                return false;
            }
            int offset = 1;
            int length = encoding[(offset++)] & 0xFF;
            if (length == 128) {
                return false;
            }
            if (length > 127) {
                int dLength = length & 0x7F;
                if (dLength > 4) {
                    return false;
                }
                length = 0;
                int next = 0;
                for (int i = 0; i < dLength; i++) {
                    next = encoding[(offset++)] & 0xFF;
                    length = (length << 8) + next;
                }
                if (length < 0) {
                    return false;
                }
            }
            return encoding.length == offset + length;
        }

        @Deprecated
        private String getPrivateKey(String password) throws Exception {
            return decrypt(password);
        }
        @Deprecated
        private String decrypt(String password) throws Exception {
            if (password == null) {
                throw new Exception("SM2File password should not be null");
            }
            if (this.privateInfo == null) {
                throw new Exception("SM2File invalid : privateInfo=null");
            }
            ASN1OctetString priOctString = (ASN1OctetString) this.privateInfo.getObjectAt(2);
            byte[] encryptedData;
            try {
                encryptedData = priOctString.getOctets();
            } catch (Exception e) {
                throw new Exception("SM2File decoding failure", e);
            }
            byte[] dBytes = SM4DecryptDBytes(password, encryptedData);

            return Hex.toHexString(dBytes).substring(0, 64);
        }

        private byte[] SM4DecryptDBytes(String password, byte[] encryptedData) throws Exception {
            if ((password == null) || (password.length() == 0)) {
                throw new Exception("SM2File password should not be null");
            }
            byte[] passwordBytes;
            try {
                passwordBytes = password.getBytes("UTF8");
            } catch (UnsupportedEncodingException e) {
                throw new Exception("SM2File password decoding failure", e);
            }
            if ((encryptedData == null) || (encryptedData.length == 0)) {
                throw new Exception("SM2File encryptedData should not be null");
            }
            if ((encryptedData.length < 32) || (encryptedData.length > 64)) {
                throw new Exception("SM2File EncryptedData required length in [32-64] ");
            }
            byte[] encoding = null;
            if ((encryptedData.length == 32) || (encryptedData.length == 48)) {
                encoding = encryptedData;
            } else {
                try {
                    encoding = Base64.decode(encryptedData);
                } catch (Exception e) {
                    throw new Exception("SM2File EncryptedData required base64 ");
                }
            }
            byte[] iv;
            byte[] sm4;
            try {
                byte[] hash = KDF(passwordBytes);
                iv = new byte[16];
                System.arraycopy(hash, 0, iv, 0, 16);
                sm4 = new byte[16];
                System.arraycopy(hash, 16, sm4, 0, 16);
            } catch (Exception e) {
                throw new Exception("SM2File KDF failure", e);
            }
            try {
                PaddedBufferedBlockCipher cipher = new PaddedBufferedBlockCipher(new CBCBlockCipher(new SM4Engine()),
                        new PKCS7Padding());
                ParametersWithIV params = new ParametersWithIV(new KeyParameter(sm4), iv);
                cipher.init(false, params);

                int outLength = cipher.getOutputSize(encoding.length);
                byte[] out = new byte[outLength];
                int dataLength = cipher.processBytes(encoding, 0, encoding.length, out, 0);
                int lastLength = cipher.doFinal(out, dataLength);
                int realLength = dataLength + lastLength;
                byte[] dBytes = null;
                if (realLength < outLength) {
                    dBytes = new byte[realLength];
                    System.arraycopy(out, 0, dBytes, 0, realLength);
                }
                return out;
            } catch (DataLengthException e) {
                throw new Exception("SM2File SM2PrivateKey decrypt failure with IllegalDataLength", e);
            } catch (IllegalArgumentException e) {
                throw new Exception("SM2File SM2PrivateKey decrypt failure with IllegalArgument", e);
            } catch (IllegalStateException e) {
                throw new Exception("SM2File SM2PrivateKey decrypt failure with IllegalState", e);
            } catch (InvalidCipherTextException e) {
                throw new Exception("SM2File SM2PrivateKey decrypt failure with InvalidCipherText", e);
            } catch (Exception e) {
                throw new Exception("SM2File SM2PrivateKey decrypt failure", e);
            }
        }

        private byte[] KDF(byte[] z) {
            byte[] ct = {0, 0, 0, 1};
            SM3Digest sm3 = new SM3Digest();
            sm3.update(z, 0, z.length);
            sm3.update(ct, 0, ct.length);
            byte[] hash = new byte[32];
            sm3.doFinal(hash, 0);
            return hash;
        }

        @Override
        public ASN1Primitive toASN1Primitive() {
            return null;
        }

    }

    public static String verificationAndDecrypt(String encrypted, String sign, String encryptKey, String signKey) {
        String generateSign = Disguiser.disguiseMD5(encrypted + StringPool.AMPERSAND + signKey);
        if(!generateSign.equalsIgnoreCase(sign)){
            throw new ServiceException("验签失败了");
        }
        String decrypted = des3Decrypt(encrypted, encryptKey);
        log.info("解密后的数据：" + decrypted);
        if (decrypted == null) {
            log.info("加密串：" + encrypted);
            log.info("解密秘钥：" + encryptKey);
            throw new ServiceException("解密失败了");
        }
        return decrypted;
    }


    public static PrivateKey getPrivateKeyForSm2N(String sm2Content, String sm2FilePwd) throws Exception {
        byte[] data = base64ToFile(sm2Content);
        try {
            PKCS12_SM2_N p12 = new PKCS12_SM2_N(data);
            return p12.getPrivateKey(sm2FilePwd);
        } catch (Exception e) {
            //log.error("getPrivateKeyStr", e);
            throw new IllegalArgumentException(e.getMessage(), e);
        }
    }

    public static byte[] base64ToFile( String base64) {
        return org.apache.commons.codec.binary.Base64.decodeBase64(base64);
    }
}
