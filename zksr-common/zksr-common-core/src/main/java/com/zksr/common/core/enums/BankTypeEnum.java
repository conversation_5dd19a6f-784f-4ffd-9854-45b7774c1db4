package com.zksr.common.core.enums;

/**
 * @Author: chenyj8
 * @Desciption: 银行账户类型
 */
public enum BankTypeEnum {
    TOPUBLIC("TOPUBLIC", "对公"),
    TOPRIVATE("TOPRIVATE", "对私"),
    ;

    private String code;

    private String desc;


    BankTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getValue() {
        return desc;
    }

}
