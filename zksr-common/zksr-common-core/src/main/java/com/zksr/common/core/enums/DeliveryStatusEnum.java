package com.zksr.common.core.enums;

import com.zksr.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeliveryStatusEnum {
    // 配送状态
    WAIT_PH(0L, "待配货", "保存订单", 1),
    WAIT_FH(3L, "待发货", "支付回调", 2),
    WAIT_SH(4L, "待收货", "", 4),
    WAIT_ZC(40L, "待装车", "订单发货出库或取消装车",5),
    COMPLETE_ZC(41L, "已装车", "订单装车",6),
    COMPLETE_SH(5L, "已收货", "订单收货", 7),
    COMPLETE(6L, "已完成", "订单自动完成", 8),
    PREAREGOODS(7L, "备货中", "全国订单导出excel操作",3),
    CANCEL(2L, "售后取消", "售后取消", 9),
    CANCELLING(21L, "售后取消中", "售后取消中", 10),
    ;
    /**
     * 编码
     */
    private final Long code;
    /**
     * 名字
     */
    private final String name;

    /**
     * 操作说明
     */
    private final String content;

    /**
     * 排序
     */
    private final Integer sort;


    public static DeliveryStatusEnum getDeliveryStatus(Long code){
        for (DeliveryStatusEnum e : DeliveryStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new ServiceException("Invalid key: " + code);
    }
}
