package com.zksr.common.core.enums.request;

import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.enums.OrderPayWayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.zksr.common.core.constant.OpenApiConstants.OPEN_FLAG_0;
import static com.zksr.common.core.constant.OpenApiConstants.OPEN_FLAG_1;


/**
 *  可视化推送收款单类型
 * @date 2024/10/15 13:42
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VisualReceiptType {
    //组成：支付方式_单据类型_订单类型_是否付款
    /** 销售订单以1开头 */
    HDFK_ORDER_XSS_1("11","货到付款已付款-销售订单", OrderPayWayEnum.HDFK.getPayWay(), SheetTypeConstants.XSS,OPEN_FLAG_1),

    ZXZF_ORDER_XSS_1("12","在线支付已付款-销售订单", OrderPayWayEnum.ONLINE.getPayWay(), SheetTypeConstants.XSS,OPEN_FLAG_1),

    /** 售后订单以2开头 */
    HDFK_AFTER_SHS_0("21","货到付款未付款-售后订单", OrderPayWayEnum.HDFK.getPayWay(), SheetTypeConstants.SHS,OPEN_FLAG_0),

    ZXZF_AFTER_SHS_1("22","在线支付已付款-售后订单", OrderPayWayEnum.ONLINE.getPayWay(), SheetTypeConstants.SHS,OPEN_FLAG_1),

    ZXZF_AFTER_SHC_1("23","在线支付已付款-差异售后订单", OrderPayWayEnum.ONLINE.getPayWay(), SheetTypeConstants.SHC,OPEN_FLAG_1),

    ZXZF_AFTER_SHJ_1("24","在线支付已付款-拒收售后订单", OrderPayWayEnum.ONLINE.getPayWay(), SheetTypeConstants.SHJ,OPEN_FLAG_1),

    HDFK_AFTER_SHS_1("25","货到付款已付款-售后订单", OrderPayWayEnum.HDFK.getPayWay(), SheetTypeConstants.SHS,OPEN_FLAG_1),







    //订单编号改造 需兼容老数据使用
    /** 销售订单以1开头 */
    HDFK_ORDER_XSS_JR1("11","货到付款已付款-销售订单", OrderPayWayEnum.HDFK.getPayWay(), "XSS",OPEN_FLAG_1),

    ZXZF_ORDER_XSS_JR1("12","在线支付已付款-销售订单", OrderPayWayEnum.ONLINE.getPayWay(), "XSS",OPEN_FLAG_1),

    /** 售后订单以2开头 */
    HDFK_AFTER_SHS_JR0("21","货到付款未付款-售后订单", OrderPayWayEnum.HDFK.getPayWay(), "SHS",OPEN_FLAG_0),

    ZXZF_AFTER_SHS_JR1("22","在线支付已付款-售后订单", OrderPayWayEnum.ONLINE.getPayWay(), "SHS",OPEN_FLAG_1),

    ZXZF_AFTER_SHC_JR1("23","在线支付已付款-差异售后订单", OrderPayWayEnum.ONLINE.getPayWay(), "SHC",OPEN_FLAG_1),

    ZXZF_AFTER_SHJ_JR1("24","在线支付已付款-拒收售后订单", OrderPayWayEnum.ONLINE.getPayWay(), "SHJ",OPEN_FLAG_1),

    HDFK_AFTER_SHS_JR1("25","货到付款已付款-售后订单", OrderPayWayEnum.HDFK.getPayWay(), "SHS",OPEN_FLAG_1),

    ;

    /**
     * 收款类型编号
     */
    private final String type;

    /**
     * 收款类型名称
     */
    private final String name;

    /**
     * 支付方式 {@link com.zksr.common.core.enums.OrderPayWayEnum}
     */
    private final String payWay;

    /**
     * 订单类型  {@link com.zksr.common.core.constant.SheetTypeConstants}
     */
    private final String sheetType;

    /**
     * 是否收款
     */
    private final Integer isProceeds;

    public static String getVisualReceiptType(String payWay,String sheetType,Integer isProceeds){

        //匹配收款类型
        for (VisualReceiptType type:VisualReceiptType.values()) {
            if(type.payWay.equals(payWay)
                    && type.sheetType.equals(sheetType)
                    && type.isProceeds.equals(isProceeds)){
                return type.type;
            }

        }

        //没匹配上就返回空
        return null;
    }
}
