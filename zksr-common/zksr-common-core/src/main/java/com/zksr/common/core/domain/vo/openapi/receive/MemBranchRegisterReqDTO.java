package com.zksr.common.core.domain.vo.openapi.receive;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 外部系统门店注册请求实体
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemBranchRegisterReqDTO{
    private static final long serialVersionUID=1L;

    /**
     * 唯一流水号
     */
    private String reqId;


    /** 平台商id */
    @Excel(name = "平台商id")
    private Long sysCode;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @NotBlank(message = "门店名称不能为空")
    private String branchName;

    /**
     * 门店编码
     */
    @ApiModelProperty(value = "门店编码")
    private String branchNo;

    /** 联系人 */
    @Excel(name = "联系人")
    @NotBlank(message = "联系人不能为空")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @NotBlank(message = "联系电话不能为空")
    private String contactPhone;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 门店地址 */
    @Excel(name = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 业务员id */
    @Excel(name = "业务员id")
    private Long colonelId;

    /** 渠道id */
    @Excel(name = "渠道id")
    private Long channelId;

    /** 门头照 */
    @Excel(name = "门头照")
    private String branchImages;

    /** 审核人 */
    @Excel(name = "审核人")
    private Long approveMan;

    /** 审核标识 */
    @Excel(name = "审核标识")
    private Integer approveFlag;

    /** 审核时间 */
    @Excel(name = "审核时间")
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveDate;

    /** 门店自动审核标识 */
    @Excel(name = "门店自动审核标识")
    private Integer branchApproveFlag;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    /** 用户类型 0 新用户  1老用户 */
    @Excel(name = "用户类型")
    private Integer userType;

    /** 电子围栏入驻商ID信息，以,逗号间隔 */
    @Excel(name = "电子围栏入驻商ID信息")
    private String supplierIds;

    @ApiModelProperty("三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    private Long threeAreaCityId;

    @ApiModelProperty("门店id")
    private Long branchId;

    /** 省份 */
    @Excel(name = "省份")
    @ApiModelProperty(value = "省份")
    @NotBlank(message = "省份不能为空")
    private String provinceName;

    /** 城市 */
    @Excel(name = "城市")
    @ApiModelProperty(value = "城市")
    @NotBlank(message = "城市不能为空")
    private String cityName;

    /** 区县 */
    @Excel(name = "区县")
    @ApiModelProperty(value = "区县")
    @NotBlank(message = "区县不能为空")
    private String districtName;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;
}
