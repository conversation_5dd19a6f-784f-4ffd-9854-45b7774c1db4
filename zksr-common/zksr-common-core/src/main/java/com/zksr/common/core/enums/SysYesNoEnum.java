package com.zksr.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SysYesNoEnum {
    YES(1, "Y", "是"),
    NO(0, "N","否");

    final Integer code;
    final String type;
    final String name;

    public static boolean isYes(Integer yesNo) {
        return YES.getCode().equals(yesNo);
    }

    public static Integer getYesNo(String name) {
        switch (name) {
            case "是":
                return YES.getCode();
            case "否":
                return NO.getCode();
            default:
                return null;
        }
    }

    public static String getYesNoName(Integer code, String defaultValue) {
        for (SysYesNoEnum sysYesNoEnum : SysYesNoEnum.values()) {
            if (sysYesNoEnum.getCode().equals(code)) {
                return sysYesNoEnum.getName();
            }
        }
        return defaultValue;
    }
}
