package com.zksr.common.core.domain.dto.car;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车信息, 最后一次加购等
 * @date 2024/7/29 17:26
 */
@Data
@ApiModel(description = "购物车信息")
@NoArgsConstructor
public class AppCarInfoDTO {

    public static final String FIELD_LAST_TIME = "lastAddTime";
    public static final String FIELD_SHOW_WINDOW = "showWindow";

    @ApiModelProperty("最后加购时间")
    private Date lastAddTime;

    @ApiModelProperty("是否弹出业务员推荐窗口")
    private Boolean showWindow;

    @ApiModelProperty(value = "指令时间")
    private Date commendTime;

    @ApiModelProperty(value = "业务员推荐备注")
    private String commandMemo;

    public AppCarInfoDTO(Date lastAddTime) {
        this.lastAddTime = lastAddTime;
    }

    public static AppCarInfoDTO build(Map<String, Object> map) {
        if (Objects.isNull(map) || map.isEmpty()) {
            return new AppCarInfoDTO();
        }
        return BeanUtil.toBean(map, AppCarInfoDTO.class);
    }

    public Map<String, Object> toMap() {
        return BeanUtil.beanToMap(this);
    }
}
