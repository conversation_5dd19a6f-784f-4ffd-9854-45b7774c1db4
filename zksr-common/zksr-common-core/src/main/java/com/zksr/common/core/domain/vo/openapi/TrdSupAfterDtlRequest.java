package com.zksr.common.core.domain.vo.openapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("b2b推送订单到erp的退货单 请求参数")
public class TrdSupAfterDtlRequest {

    @ApiModelProperty(value = "第三方单据号")
    private String orderNo;

    @ApiModelProperty(value = "门店编号")
    private String branchNo;

    @ApiModelProperty(value = "配送中心编号")
    private String dcBranchNo;

    @ApiModelProperty(value = "第三方单据时间 格式： 2023-10-27 13:20:00")
    private String orderNoDate;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "商品明细")
    private List<TrdSupAfterDtlDetail> detailList;
}
