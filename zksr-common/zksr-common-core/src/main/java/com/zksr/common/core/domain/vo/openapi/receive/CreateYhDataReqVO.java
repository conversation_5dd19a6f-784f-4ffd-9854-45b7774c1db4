package com.zksr.common.core.domain.vo.openapi.receive;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/9 10:30
 */
@Data
@ApiModel(description = "补货单, 要货单新增")
@ToString
public class CreateYhDataReqVO {

    @ApiModelProperty(value = "门店ID", required = true)
    @NotNull(message = "门店ID")
    private Long branchId;

    @ApiModelProperty(value = "平台商Id",hidden = true)
    private Long sysCode;

    @ApiModelProperty(value = "数据接受事件", hidden = true)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date receiveTime = DateUtil.date();

    @ApiModelProperty(value = "要货单批次号(系统唯一)", required = true)
    @NotEmpty(message = "要货单批次号(系统唯一)")
    private String yhBatchNo;

    @ApiModelProperty("最迟订货时间(yyyy-MM-dd HH:mm:ss)上海时区")
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date maxLateTime;

    @ApiModelProperty(value = "要货数据集合 (1-2001个详情数量)", required = true)
    @Size(min = 1, max = 2001, message = "要货数量至少在1-2001个详情数量")
    @NotEmpty(message = "要货数量不能为空")
    private List<YhDtl> itemList;

    @Data
    @ToString
    public static class YhDtl {
        /** 商品编码 */
        @ApiModelProperty(value = "商品编码", required = true)
        @NotEmpty(message = "商品编码必须")
        private String posSourceNo;

        /** 国条码 */
        @ApiModelProperty(value = "国条码", required = true)
        @NotEmpty(message = "国条码必须")
        private String posBarcode;

        /** 商品名 */
        @ApiModelProperty("商品名")
        private String posSkuName;

        /** 建议购买数量 */
        @ApiModelProperty(value = "建议购买数量", required = true)
        @NotNull(message = "建议购买数量必填")
        @Range(min = 1, max = 9999999, message = "建议购买数量最小1, 最大9999999")
        private Integer posSuggestQty;

        /** 昨日销量 */
        @ApiModelProperty("昨日销量")
        private Integer posSalesQty;

        /** 实际库存数量 */
        @ApiModelProperty("实际库存数量")
        private Integer posStockQty;

        /** 门店库存数量 */
        @ApiModelProperty("门店库存数量")
        private Integer posBranchStockQty;

        /** 单位名称 */
        @ApiModelProperty("单位名称")
        private String posUnitName;

        /** 30天人均销量 */
        @ApiModelProperty("30天人均销量")
        private Integer pos30dayAvgSales;

        /** 安全库存天数 */
        @ApiModelProperty("安全库存天数")
        private Integer posSafetyDays;

        /** 安全库存 */
        @ApiModelProperty("安全库存")
        private Integer posSafetyStock;

    }
}
