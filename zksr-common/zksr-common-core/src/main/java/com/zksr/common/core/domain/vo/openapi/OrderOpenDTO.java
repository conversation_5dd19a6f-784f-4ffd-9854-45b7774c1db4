package com.zksr.common.core.domain.vo.openapi;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
*
 * 入驻商销售订单  主表信息
* <AUTHOR>
* @date 2024/6/4 15:34
*/
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class OrderOpenDTO {

    /** 创建时间 */
    @Excel(name = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 入驻商订单id */
    private Long supplierOrderId;

    /** 平台商id */
    private Long sysCode;

    /** 入驻商订单编号 */
    private String supplierOrderNo;

    /** 订单id */
    private Long orderId;

    /** 订单编号 */
    private String orderNo;

    /** 配送费 */
    private BigDecimal transAmt;

    /** 入驻商id */
    private Long supplierId;

    /** 入驻商名称 */
    private String supplierName;

    /** 备注 */
    private String memo;

    /** 优惠金额 **/
    private BigDecimal subDiscountAmt;

    /** 支付金额 **/
    private BigDecimal  subPayAmt;

    /** 订单金额 未减去优惠的订单金额 **/
    private BigDecimal subOrderAmt;

    /** 删除状态(0:正常，2：删除) */
    private Integer delFlag;

    /** 订单商品总数量 **/
    private BigDecimal subOrderNum;

    /** 支付公司收取的支付费率 */
    private BigDecimal payRate;

    /** 支付公司手续费金额 */
    private BigDecimal subPayFee;

    /** 发货前取消数量 */
    private Long subCancelQty;

    /** 发货前取消金额 */
    private BigDecimal subCancelAmt;

    /** 业务员id */
    private Long colonelId;

    /** 门店id */
    private Long branchId;

    @Excel(name = "门店编号")
    private String branchNo;

    private String branchName;

    /** 联系电话 */
    private String contactPhone;

    /** 省份 */
    private String provinceName;

    /** 城市 */
    private String cityName;

    /** 区县 */
    private String districtName;

    /** 门店地址 */
    private String branchAddr;

    /** 订单支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款 */
    private String payWay;

    /** 对接唯一编码 */
    @Excel(name = "对接唯一编码")
    private String sendCode;

    /** 下单用户是否本身是业务员(0-否（默认）  1-是)  */
    @Excel(name = "下单方式 0自主下单（商城下单） 1业务员下单")
    private Integer colonelFlag;

    /** 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消 3-货到付款未收款 4-货到付款已收款*/
    @Excel(name = "支付状态", readConverterExp = "数=据字典sys_pay_state")
    private Integer payState;

    /** 外部订单号 */
    @Excel(name = "外部订单号")
    private String sourceOrderNo;

    /** 推送状态 0未推送 1已推送 2已接收*/
    @Excel(name = "推送状态")
    private Integer pushStatus;

    //需要计算的订单销售参数数据======================================
    /** 订单实际销售金额 （支付金额-发货前取消金额） */
    @Excel(name = "订单实际销售金额")
    private BigDecimal realSaleAmt;

    /** 订单实际原金额  （订单金额 -发货前取消金额）*/
    @Excel(name = "订单实际原金额")
    private BigDecimal realOrderAmt;

    /** 下单单位总数量 （减去发货前取消数量）*/
    @Excel(name = "下单单位总数量")
    private BigDecimal realSumUnitQty;

    /** 门店地址 */
//    private String branchAddr;

    /** 联系人 */
    private String contactName;

    /** 联系电话 */
//    private String contactPhone;

    /** 业务员名 */
    private String colonelName;

    /** 订单详情 */
    private List<OrderDetailOpenDTO> detailList;

    @ApiModelProperty(name = "是否负库存下单 0-否 1-是")
    private Integer stockShortFlag;
}
