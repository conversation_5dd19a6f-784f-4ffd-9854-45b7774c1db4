package com.zksr.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 *  系统时间类型枚举
 */
@Getter
@AllArgsConstructor
public enum SysTimeTypeEnum {
    MINUTE(0, "分钟"),
    HOUR(1, "小时"),
    DAY(2, "天"),
    ;
    private Integer type;
    private String name;


    /**
     * 获取可售后时间转换成分钟
     * @param type 类型
     * @param afterSalesTime 可售会时间
     * @return
     */
    public static Integer getAfterSalesTimeMinute(Integer type, Integer afterSalesTime) {
        if (Objects.equals(type, MINUTE.getType())) {
            return afterSalesTime;
        } else if (Objects.equals(type, HOUR.getType())) {
            return afterSalesTime * 60;
        } else if (Objects.equals(type, DAY.getType())) {
            return afterSalesTime * 60 * 24;
        }
        return afterSalesTime;
    }
}
