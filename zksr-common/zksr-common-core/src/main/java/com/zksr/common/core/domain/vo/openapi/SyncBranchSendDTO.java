package com.zksr.common.core.domain.vo.openapi;

import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.pool.NumberPool;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* 门店信息同步 MQ入参
* @date 2024/7/12 10:25
* <AUTHOR>
*/
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SyncBranchSendDTO extends MemBranchSyncReqVO{
    /** 门店id */
    private Long branchId;


    /** 操作类型编码 */
    private String operationTypeCode = OperationType.ADD.getCode();

    /** 同步类型 1 门店信息同步   2 区域门店初始化同步到入驻商 */
    private Integer syncType = NumberPool.INT_ONE;

    public SyncBranchSendDTO(MemBranchSyncReqVO vo) {
        super(vo.getAreaId(), vo.getSupplierId());
        this.syncType = NumberPool.INT_TWO;
    }

    public SyncBranchSendDTO(Long branchId, String operationTypeCode) {
        this.branchId = branchId;
        this.operationTypeCode = operationTypeCode;
    }
}
