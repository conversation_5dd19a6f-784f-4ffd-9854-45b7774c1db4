package com.zksr.common.core.domain.vo.openapi.receive;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/30 15:12
 * @注释
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("接收订单出库回传实体")
public class OrderOutboundReturnVO {

    /** 订单号(入驻商订单编号) */
    @Excel(name = "B2B入驻商销售订单号")
    @ApiModelProperty(value = "B2B入驻商销售订单号")
    @NotNull(message = "B2B入驻商销售订单编号不能为空")
    private String supplierOrderNo;

    /** 外部订单号 */
    @Excel(name = "外部(ERP)销售订单号(非必填)")
    @ApiModelProperty(value = "外部(ERP)销售订单号(非必填)")
    private String sourceOrderNo;

//    /** 备注 */
//    @Excel(name = "备注")
//    @ApiModelProperty(value = "备注")
//    private String memo;

    /** 是否校验订单详情商品条数 开启时 推送10条数据 必须返回对应的十条数据 */
    @Excel(name = "是否校验订单详情商品条数")
    @ApiModelProperty(value = "是否校验订单详情商品条数",hidden = true)
    private boolean checkDetailNumFlag = Boolean.TRUE;

    /** 订单详情 */
    @Excel(name = "订单详情")
    @ApiModelProperty(value = "订单详情")
    private List<OrderOutboundReturnDetailVO> detailVoList;


}
