package com.zksr.common.core.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("erp系统外部接口 请求参数")
public class ErpRequest {

    @ApiModelProperty("随机生成请求的唯一id")
    private String reqId;

    @ApiModelProperty("请求时间 yyyy-MM-dd HH:mm:ss.SSS")
    private String reqTime;

    @ApiModelProperty("使用公钥加密的业务数据")
    private String bizData;

    @ApiModelProperty("异步回调通知url，无则天空")
    private String returnURL;
}
