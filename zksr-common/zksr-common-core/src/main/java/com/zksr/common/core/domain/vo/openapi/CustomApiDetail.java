package com.zksr.common.core.domain.vo.openapi;

import com.zksr.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 实体类-自定义接口详情
 * <AUTHOR> 2024/03/07
 *
 */
@Data
public class CustomApiDetail {
    private String apiNo;			    //  接口编号
    private String fieldName;		    //  字段名称
    private String fieldDataType;       //  字段数据类型 字符串 STRING , 数字 NUMBER, 对象 OBJECT, 数组 ARRAY, 布尔 BOOLEAN, 空 NULL
    private String parentFiledName;     //  父字段名
    private String modelType;           //  业务对象类型 YH要货单
    private String modelFieldName;      //  业务对象字段名
    private String modelFieldExample;   //  业务对象字段示例
    private String isRequired;          //  是否必选 0否 1必选
    private String apiNotes;            //  api备注
    private String isCustom;            //  是否自定义 0否 1是 开启后，该字段的值由程序控制，业务字段填值的方式失效
    private String isMaster;            //  是否主字段 0否 1是 详情数据渲染在该字段下
    private String defaultValue;        //  默认值 当前字段值为空时的默认值
    private String isDetail;            //  是否是详细数据 单据用
    private String notes;               //  备注
}
