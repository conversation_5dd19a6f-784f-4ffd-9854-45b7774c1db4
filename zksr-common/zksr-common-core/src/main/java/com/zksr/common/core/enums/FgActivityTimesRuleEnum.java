package com.zksr.common.core.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 活动次数限制规则枚举 （字典：fg_activity_times_rule）
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2025/3/05 9:51
 */
@Getter
public enum FgActivityTimesRuleEnum {
    RULE_LEVEL_NO(-1,"类型错误"),
    RULE_LEVEL_0(0,"每日一次"),
    RULE_LEVEL_1(1,"活动期间仅一次"),
//    RULE_LEVEL_2(2,"仅活动期间内首单"), 作废
    RULE_LEVEL_3(3,"新人首单"),
    ;

    @Getter
    private Integer type;
    @Getter
    private String name;
    FgActivityTimesRuleEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * @param timesRule 活动次数限制规则类型
     * @return 每日一次
     */
    public static boolean isRuleLevel0(Integer timesRule) {
        return Objects.equals(timesRule, RULE_LEVEL_0.getType());
    }

    /**
     * @param timesRule 活动次数限制规则类型
     * @return 仅一次
     */
    public static boolean isRuleLevel1(Integer timesRule) {
        return Objects.equals(timesRule, RULE_LEVEL_1.getType());
    }

    public static FgActivityTimesRuleEnum formValue(Integer type) {
        for (FgActivityTimesRuleEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return FgActivityTimesRuleEnum.RULE_LEVEL_NO;
    }
}
