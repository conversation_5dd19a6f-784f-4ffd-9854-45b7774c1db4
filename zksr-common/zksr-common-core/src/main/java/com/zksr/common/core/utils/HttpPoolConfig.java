package com.zksr.common.core.utils;


public class HttpPoolConfig {

    /**
     * maxTotalConnections - 连接池最大链接数量
     * 单台服务器，可以理解为单台容器，单个JVM的最大链接数
     */
    private int maxTotalConnections = 2000;

    /**
     * maxConnectionsPerRoute - 每个请求目标地址的最大并发连接数量
     * maxRoute小于maxTotal用于预留给其他目标地址
     */
    private int maxConnectionsPerRoute = 1800;

    /**
     * connectTimeout - 建立新链接的超时时间
     * 连接池创建新链接时，客户端和服务器建立链接的超时时间，单位毫秒（默认2秒）
     * 需要考虑预留网络不稳定的时间
     */
    private int connectTimeout = 2*1000 ;

    /**
     * socketTimeout - 读写数据超时时间
     * socket写入或获取数据的超时时间，单位毫秒 (默认20秒)
     * 需要综合考虑读取的数据量和服务器端响应性能，因为不同业务情况不同，过长会导致tomcat线程耗尽而雪崩
     * 根据自身业务实际情况在发起http请求时，通过在post方法指定RequestConfig的socketTimeout覆盖默认值
     */
    private int socketTimeout = 20*1000 ;

    /**
     * connectionRequestTimeout - 获取连接超时时间
     * 链接池没有可用链接时，最长等待的超时时间，单位毫秒（默认2秒）
     * 过长会导致tomcat线程耗尽而雪崩
     */
    private int connectionRequestTimeout = 2*1000 ;

    // user agent
    private String userAgent = "Mozilla/5.0";

    public HttpPoolConfig setMaxTotalConnections(int maxTotalConnections) {
        this.maxTotalConnections = maxTotalConnections;
        return this;
    }

    public HttpPoolConfig setMaxConnectionsPerRoute(int maxConnectionsPerRoute) {
        this.maxConnectionsPerRoute = maxConnectionsPerRoute;
        return this;
    }

    public HttpPoolConfig setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
        return this;
    }

    public HttpPoolConfig setSocketTimeout(int socketTimeout) {
        this.socketTimeout = socketTimeout;
        return this;
    }

    public HttpPoolConfig setConnectionRequestTimeout(int connectionRequestTimeout) {
        this.connectionRequestTimeout = connectionRequestTimeout;
        return this;
    }

    public HttpPoolConfig setUserAgent(String userAgent) {
        this.userAgent = userAgent;
        return this;
    }

    public int getMaxTotalConnections() {
        return maxTotalConnections;
    }

    public int getMaxConnectionsPerRoute() {
        return maxConnectionsPerRoute;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public int getSocketTimeout() {
        return socketTimeout;
    }

    public int getConnectionRequestTimeout() {
        return connectionRequestTimeout;
    }

    public String getUserAgent() {
        return userAgent;
    }
}

