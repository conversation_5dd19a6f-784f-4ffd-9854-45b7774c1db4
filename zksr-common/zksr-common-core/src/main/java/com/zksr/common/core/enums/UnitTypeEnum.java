package com.zksr.common.core.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * sku单位大中小字典枚举
 */
@Getter
@AllArgsConstructor
public enum UnitTypeEnum {
    NONE(-1, "无"),
    UNIT_SMALL(1, "小单位"),
    UNIT_MIDDLE(2,"中单位"),
    UNIT_LARGE(3,"大单位"),
    ;

    @JsonValue
    @Getter
    private Integer type;
    @Getter
    private String name;

    public static boolean S(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return false;
        }
        return Objects.equals(unitSize, UNIT_SMALL.type);
    }

    public static boolean M(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return false;
        }
        return Objects.equals(unitSize, UNIT_MIDDLE.type);
    }

    public static boolean L(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return false;
        }
        return Objects.equals(unitSize, UNIT_LARGE.type);
    }

    @JsonCreator
    public static UnitTypeEnum formValue(Integer unitSize) {
        switch (unitSize) {
            case 1:
                return UNIT_SMALL;
            case 2:
                return UNIT_MIDDLE;
            case 3:
                return UNIT_LARGE;
        }
        return null;
    }
}
