package com.zksr.common.core.domain.vo.openapi;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 入驻商销售订单  详情表信息
 * <AUTHOR>
 * @date 2024/6/4 15:34
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class OrderDetailOpenDTO {

    /** 入驻商订单明细id */
    private Long supplierOrderDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    private Long sysCode;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    private Long supplierOrderId;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    private String supplierOrderNo;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 入驻商上架商品id 全国*/
    @Excel(name = "入驻商上架商品id")
    private Long supplierItemId;

    /** 城市上架商品id */
    @Excel(name = "城市上架商品id")
    private Long areaItemId;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    private Long spuId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    private Long skuId;

    /** 商品spu 名称 */
    @Excel(name = "商品spu 名称")
    private String spuName;

    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    private String thumb;

    /** 封面视频（url） */
    @Excel(name = "封面视频", readConverterExp = "u=rl")
    private String thumbVideo;

    /** 详情页轮播（json） */
    @Excel(name = "详情页轮播", readConverterExp = "j=son")
    private String images;

    /** 详情信息(富文本) */
    @Excel(name = "详情信息(富文本)")
    private String details;

    /** 商品数量 */
    @Excel(name = "商品数量")
    private BigDecimal totalNum;

    /** 合计金额（price*total_num） */
    @Excel(name = "合计金额", readConverterExp = "p=rice*total_num")
    private BigDecimal totalAmt;

    /** 成交价 （最小单位）*/
    @Excel(name = "成交价")
    private BigDecimal price;

    /** 原销售价（最小单位） */
    @Excel(name = "原销售价")
    private BigDecimal salePrice;

    /** *订单状态（数据字典） */
    @Excel(name = "*订单状态", readConverterExp = "数=据字典")
    private Long deliveryState;

    /** 是否是赠品 1-是  0-否 */
    @Excel(name = "是否是赠品 1-是  0-否")
    private Integer giftFlag;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 优惠劵优惠金额(分摊的) */
    @Excel(name = "优惠劵优惠金额(分摊的)")
    private BigDecimal couponDiscountAmt;

    /** 优惠劵优惠金额(不分摊的) */
    @Excel(name = "优惠劵优惠金额(不分摊的)")
    private BigDecimal couponDiscountAmt2;

    /** 活动优惠金额(不分摊的) */
    @Excel(name = " 活动优惠金额(分摊的)")
    private BigDecimal activityDiscountAmt;

    /** 是否已经同步 1-是 0-否 */
    @Excel(name = "是否已经同步 1-是 0-否")
    private Integer syncFlag;

    /** 删除状态(0:正常，2：删除) */
    private Integer delFlag;

    /** spu规格 **/
    @Excel(name = "spu规格")
    private String specName;

    /** 主订单ID **/
    @Excel(name = "主订单ID")
    private Long orderId;

    /** 商品类型 0：全国商品 1：本地商品 **/
    @Excel(name = "商品类型 0：全国商品 1：本地商品")
    private Integer itemType;

    /** 发货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发货时间")
    private Date deliveryTime;

    /** 收货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "收货时间")
    private Date receiveTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间")
    private Date completeTime;

    /** 发货数量 （最小单位）*/
    @Excel(name = "发货数量")
    private BigDecimal sendQty;

    /** 发货前取消数量 （最小单位）*/
    @Excel(name = "发货前取消数量")
    private BigDecimal cancelQty;

    /** 发货前取消金额 */
    @Excel(name = "发货前取消金额")
    private BigDecimal cancelAmt;

    /** 精准成交价（6位小数） */
    @Excel(name = "精准成交价")
    private BigDecimal exactPrice;

    /** 精准商品金额（6位小数） */
    @Excel(name = "精准商品金额")
    private BigDecimal exactTotalAmt;

    /** 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消 */
    @Excel(name = "支付状态", readConverterExp = "数=据字典sys_pay_state")
    private Integer payState;

    /** 商品订单原总金额（salePrice*total_num） */
    @Excel(name = "商品订单原总金额")
    private BigDecimal subOrderAmt;

    /** 入驻商订单行号 */
    @Excel(name = "入驻商订单行号")
    private Long lineNum;

    //=========================下单===========================
    /** 订单购买单位;最小单位的单位，中单位的单位，大单位的单位 */
    @Excel(name = "订单购买单位")
    private String orderUnit;

    /** 单位大小 {@link com.zksr.common.core.enums.UnitTypeEnum}*/
    @Excel(name = "购买单位大小")
    private Integer orderUnitType;

    /** 订单购买单位数量;购买的是中单位，即为中单位数量。小单位、大单位同理 */
    @Excel(name = "订单购买单位数量")
    private Long orderUnitQty;

    /** 订单换算数量;小单位为1，中、大单位的换算数量  取SPU中对应单位的换算数量*/
    @Excel(name = "订单购买换算数量")
    private BigDecimal orderUnitSize;

    /** 订单购买单位价格 */    //当前单位原单位取值逻辑： subOrderAmt/orderUnitQty
    @Excel(name = "订单购买单位价格  取价逻辑:(原销售价-优惠价)/当前单位数量")
    private BigDecimal orderUnitPrice;

    //===========================发货=================================
    /** 发货单位;最小单位的单位，中单位的单位，大单位的单位 */
    @Excel(name = "发货单位")
    private String sendUnit;

    /** 发货单位大小 {@link com.zksr.common.core.enums.UnitTypeEnum}*/
    @Excel(name = "发货单位大小")
    private Integer sendUnitType;

    /** 发货单位数量;发货的是中单位，即为中单位数量。小单位、大单位同理 */
    @Excel(name = "发货单位数量")
    private BigDecimal sendUnitQty;

    /** 发货单位换算数量 */
    @Excel(name = "发货单位换算数量")
    private Long sendUnitSize;

    //=========================取消========================
    /** 发货前取消单位;最小单位的单位，中单位的单位，大单位的单位 */
    @Excel(name = "发货前取消单位")
    private String cancelUnit;

    /** 单位大小 */
    @Excel(name = "单位大小")
    private Integer cancelUnitType;

    /** 发货前取消数量;发货前取消的是中单位，即为中单位数量。小单位、大单位同理 */
    @Excel(name = "发货前取消数量")
    private BigDecimal cancelUnitQty;

    /** 发货前取消单位换算数量 */
    @Excel(name = "发货前取消单位换算数量")
    private BigDecimal cancelUnitSize;

    //=========================拒收========================
    /** 拒收数量;最小单位 */
    @Excel(name = "拒收数量")
    private BigDecimal rejectQty;

    /** 拒收单位;最小单位的单位，中单位的单位，大单位的单位 */
    @Excel(name = "拒收单位")
    private Integer rejectUnit;

    /** 单位大小 */
    @Excel(name = "单位大小")
    private Long rejectUnitType;

    /** 拒收单位数量;拒收的是中单位，即为中单位数量。小单位、大单位同理 */
    @Excel(name = "拒收单位数量")
    private BigDecimal rejectUnitQty;

    /** 拒收单位换算数量 */
    @Excel(name = "拒收单位换算数量")
    private BigDecimal rejectUnitSize;

    //=========================新增字段========================

    /** 货到付款单ID */
    @Excel(name = "货到付款单ID")
    private Long hdfkPayId;

    /** 同步库存标识 0否 1是*/
    @Excel(name = "同步库存标识 0否 1是")
    private Integer syncStock;

    /**
     * 管理分类Id
     */
    @Excel(name = "管理分类Id")
    private Long categoryId;

    /** 最旧生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最旧生产日期")
    private Date oldestDate;

    /** 最新生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最新生产日期")
    private Date latestDate;


    /**
     * 赠品优惠分摊单价（6位小数）
     */
    @Excel(name = "赠品优惠分摊单价（6位小数）")
    private BigDecimal exactGiftSharePrice;

    /**
     * 赠品分摊价小计
     */
    @Excel(name = "赠品分摊价小计")
    private BigDecimal giftShareSubtotalAmt;

    /**
     * 均摊赠品优惠后的成交价（6位小数）
     */
    @Excel(name = "均摊赠品优惠后的成交价（6位小数）")
    private BigDecimal afterGiftSharePrice;

    /**
     * 均摊赠品优惠后的购买单位成交价（6位小数）
     */
    @Excel(name = "均摊赠品优惠后的购买单位成交价（6位小数）")
    private BigDecimal afterGiftShareUnitPrice;

    /**
     * 均摊赠品优惠后的小计
     */
    @Excel(name = "均摊赠品优惠后的小计")
    private BigDecimal afterGiftShareSubtotalAmt;

    /**
     * SKU最小单位数量平均单价（6位小数）
     */
    @Excel(name = "SKU最小单位数量平均单价（6位小数）")
    private BigDecimal skuAvgPrice;

    /**
     * 赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价）
     */
    @Excel(name = "赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价）")
    private Integer giftPriceType;

    /**
     * 取价算法- 对应最终单价 （最小单位）（6位小数）
     */
    @Excel(name = "取价算法- 对应最终单价 （6位小数）")
    private BigDecimal resPrice;

    /**
     * 取价算法- 对应最终购买单位单价（最小单位） （6位小数）
     */
    @Excel(name = "取价算法- 对应最终购买单位单价 （6位小数）")
    private BigDecimal resUnitPrice;

    /**
     * 取价算法- 对应最终金额
     */
    @Excel(name = "取价算法- 对应最终金额（2位小数）")
    private BigDecimal resAmt;



    //第三方需要的定义参数数据======================================
    /** 商品的外部编码 */
    @Excel(name = "商品的外部编码")
    private String itemSourceNo;

    /** 商品条码 */
    private String itemBarcode;


    //需要计算的订单销售参数数据======================================
    /** 订单详情最小单位实际销售数量 */
    @Excel(name = "订单最小单位实际销售数量 商品数量-发货前取消数量")
    private BigDecimal realQty;

    /** 订单详情当前单位实际销售数量 */
    @Excel(name = "订单当前单位实际销售数量   （商品数量-发货前取消数量）/订单购买换算数量")
    private BigDecimal realUnitQty;

    /** 订单详情实际销售金额 （合计金额-发货前取消金额） */
    @Excel(name = "订单实际销售金额")
    private BigDecimal realSaleAmt;

    /** 订单详情实际原金额  （商品订单原总金额 -发货前取消金额）*/
    @Excel(name = "订单实际原金额")
    private BigDecimal realOrderAmt;

    /** 订单详情总优惠金额  优惠劵优惠金额(分摊的) + 优惠劵优惠金额(不分摊的) + 活动优惠金额(分摊的)  */
    @Excel(name = "订单详情总优惠金额")
    private BigDecimal realTotalCouponAmt = BigDecimal.ZERO;

    /** 订单详情当前单位原单价  订单实际原金额/订单当前单位实际销售数量 */
    @Excel(name = "订单详情当前单位原单价")
    private BigDecimal realUnitPrice;


    //第三方需要的合单参数数据======================================
    /** 促销活动备注 */
    @Excel(name = "促销活动备注")
    private String activityMemo;

    @ApiModelProperty(name = "是否负库存下单 0-否 1-是")
    private Integer stockShortFlag;
}
