package com.zksr.common.core.business;

import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

/**
 * 账户变动业务类型
 */
@ApiModel(description = "账户流水变动字段")
@Getter
public enum AccountBusiTypeField {
    NONE("", "无"),
    WITHDRAWABLE_AMT("withdrawableAmt", "可提现账户余额"),
    FROZEN_AMT("frozenAmt", "冻结金额"),
    CREDIT_AMT("creditAmt", "授信金额"),
    ;
    private String field;
    private String name;

    AccountBusiTypeField(String field, String name) {
        this.field = field;
        this.name = name;
    }

    @JsonCreator
    public static AccountBusiTypeField fromValue(String value) {
        for (AccountBusiTypeField busiTypeField : AccountBusiTypeField.values()) {
            if (busiTypeField.getField().equals(value)) {
                return busiTypeField;
            }
        }
        return NONE;
    }
}
