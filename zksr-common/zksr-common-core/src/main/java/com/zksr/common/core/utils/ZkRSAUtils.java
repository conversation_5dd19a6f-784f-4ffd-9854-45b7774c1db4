package com.zksr.common.core.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.*;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 中科公钥加密类
 */
@Slf4j
public class ZkRSAUtils {
    public static final String CHARSET = "UTF-8";
    public static final String RSA_ALGORITHM = "RSA"; // ALGORITHM ['ælgərɪð(ə)m] 算法的意思

    /**
     * 公钥加密
     * @param data
     * @param publicKey
     * @return
     */
    public static String publicEncrypt(String data, RSAPublicKey publicKey) {
        try {
            Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            return Base64.encodeBase64URLSafeString(rsaSplitCodec(cipher, Cipher.ENCRYPT_MODE, data.getBytes(CHARSET), publicKey.getModulus().bitLength()));
        } catch (Exception e) {
            log.error(" 加密字符串异常,", e);
            throw new ServiceException("加密字符串[" + data + "]时遇到异常");
        }
    }

    /**
     * 得到公钥
     * @param publicKey  密钥字符串（经过base64编码）
     * @throws Exception
     */
    public static RSAPublicKey getPublicKey(String publicKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        // 通过X509编码的Key指令获得公钥对象
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKey));
        RSAPublicKey key = (RSAPublicKey) keyFactory.generatePublic(x509KeySpec);
        return key;
    }

    //rsa切割解码  , ENCRYPT_MODE,加密数据   ,DECRYPT_MODE,解密数据
    private static byte[] rsaSplitCodec(Cipher cipher, int opmode, byte[] datas, int keySize) {
        int maxBlock = 0;  //最大块
        if (opmode == Cipher.DECRYPT_MODE) {
            maxBlock = keySize / 8;
        } else {
            maxBlock = keySize / 8 - 11;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] buff;
        int i = 0;
        try {
            while (datas.length > offSet) {
                if (datas.length - offSet > maxBlock) {
                    //可以调用以下的doFinal（）方法完成加密或解密数据：
                    buff = cipher.doFinal(datas, offSet, maxBlock);
                } else {
                    buff = cipher.doFinal(datas, offSet, datas.length - offSet);
                }
                out.write(buff, 0, buff.length);
                i++;
                offSet = i * maxBlock;
            }
        } catch (Exception e) {
            log.error(" 加解密阀值异常,", e);
            throw new RuntimeException("加解密阀值为[" + maxBlock + "]的数据时发生异常", e);
        }
        byte[] resultDatas = out.toByteArray();
        IOUtils.closeQuietly(out);
        return resultDatas;
    }

//    /**
//     * 示例方法
//     * @param args
//     */
//    public static void main(String[] args) throws Exception{
//        //公钥
//        String publicKey ="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhddYy2uRsmVzHsbStE/S2tpIJACBnb4rRU6cuwCLWb9T0M+QnqbTEcLyAG9H+0QC9CY99hDG7I85FnkZKYxBJwFrmBpSBnOBE70sOA7S/Os9+K0O+wptrLTKPIRHLLEeD/7/frrHuKRy1JvWZoDCpYqfetB+qJwihDIYHeFZPwV/ikywBUxySMyofy/RBnazGO17xk6cBEs74T4qTD9Mkx8cGjfB7VPfIIPNaKx8c9Cm6PNUXBWRD7/cwm3dny9U5Lq9vh+xdFD3+fKbWIpCCukKajeyBbNb5rzGEP5/KFOYca7DdSMramDBbsvn/q+R07JuZIo1ntdf2zOjiAGnmwIDAQAB";
//        //请求数据
//        JSONObject json = new JSONObject();
//        json.put("orderNo","新单号");
//        json.put("sourceOrderNo","中科原单号");
//        json.put("detailList","[]");
//        //公钥加密数据
//        RSAPublicKey publicKeyRsa = ZkRSAUtils.getPublicKey(publicKey);
//        String encryptData = ZkRSAUtils.publicEncrypt(json.toJSONString(), publicKeyRsa);
//        System.out.println("加密前数据：" + json.toJSONString());
//        System.out.println("加密后数据：" + encryptData);
//    }

}

