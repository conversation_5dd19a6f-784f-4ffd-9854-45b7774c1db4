package com.zksr.common.core.enums.request;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * B2B接口接收类型
 *
 * <AUTHOR>
 * @date 2024/9/23 16:02
 */
@Getter
@AllArgsConstructor
public enum B2BRequestType {

    EMPTY("null", "未匹配到对应类型", 0, null),

    //B2B接收类型

    CONFIRM_RETURN("confirmReturn", "退货确认", 1, null),

    CONFIRM_RECEIPT("confirmReceipt", "订单收货确认", 2, null),

    DELIVERY("delivery", "订单发货", 3, null),

    ORDER_LOG("orderLog", "订单状态", 4, null),

    SAVE_PRDT_DATE("savePrdtDate", "接收商品生产日期", 5, null),

    SAVE_PRDT_STOCK("savePrdtStock", "接收商品库存", 6, null),

    SAVE_PRDT("savePrdt", "新增或者修改商品信息", 7, null),

    ORDER_RECEIVE_CALLBACK("orderReceiveCallback", "订单接收成功通知接口", 8, null),

    AFTEER_ORDER_RECEIVE_CALLBACK("afterOrderReceiveCallback", "退单接收成功通知", 9, null),

    SAVE_SUPPLIER("addSupplier", "新增入驻商信息", 10, null),

    ADD_HDFK_SETTLE("addHdfkSettle", "新增货到付款清账能力", 11, null),

    ORDER_CANCEL("orderCancel", "订单发货前取消", 12, null),

    SUBMIT_BATCH_YH("submitBatchYh", "接受批量补货", 13, null),

    GET_BATCH_YH_RES("getBatchYhRes", "获取批量补货结果", 14, null),

    AFTER_CANCEL("afterCancel", "退货确认前取消", 15, null),

    AFTER_LOG("afterLog", "售后状态", 16, null),

    ORDER_CANCEL_RECEIVE_CALLBACK("orderCancelReceiveCallback","订单取消接收通知接口",17,null),

    BRANCH_REGISTER("branchRegister","注册门店",18,null),


    //B2B发送类型 从500开始
    SYNC_B2B_CONSUMER(null, "推送B2B平台门店数据", 501, 410),

    SYNC_B2B_ORDER(null, "推送B2B销售订单数据", 502, 402),

    SYNC_B2B_AFTER_SHEET(null, "推送B2B售后订单数据", 503, 406),

    SYNC_B2B_PAY(null, "推送B2B收款单数据", 504, 411),

    SYNC_B2B_BRANCH_VALUE_INFO(null, "推送B2B门店储值信息", 505, null),

    SYNC_B2B_ORDER_CANCEL(null,"B2B取消订单",506,557),

    SYNC_ERP_INCREMENT_UPDATE_STOCK("sync_erp_increase_update_stock", "同步ERP增量库存", 600, null),
    B2B_STOCK_OCCUPY_QUERY(null,"B2B查询库存预占",601,559),
    B2B_STOCK_OCCUPY_CREATE(null,"B2B创建库存预占",602,560),
    B2B_STOCK_OCCUPY_ROLLBACK(null,"B2B回滚库存预占",603,561),
    ;
    /**
     * B2B接口类型
     */
    private final String b2bKey;

    /**
     * 接口信息
     */
    private final String info;

    /**
     * B2B日志类型
     */
    private final Integer b2bType;

    /**
     * ERP接口类型
     */
    private final Integer erpType;

    /**
     * 根据B2Bkey的类型匹配日志类型
     *
     * @param type
     * @return
     */
    public static int matchingB2BType(String type) {
        for (B2BRequestType value : B2BRequestType.values()) {
            if (type.equals(value.b2bKey)) {
                return value.b2bType;
            }
        }
        return EMPTY.b2bType;
    }

    /**
     * 根据ERP的类型匹配日志类型
     *
     * @param type
     * @return
     */
    public static int matchingErpType(Integer type) {
        for (B2BRequestType value : B2BRequestType.values()) {
            if (type.equals(value.erpType)) {
                return value.b2bType;
            }
        }
        return EMPTY.b2bType;
    }

    /**
     * 根据模板类型匹配 枚举类型
     *
     * @param b2bType
     * @return
     */
    public static B2BRequestType matchingTemplateType(Integer b2bType) {
        for (B2BRequestType value : B2BRequestType.values()) {
            if (b2bType.equals(value.b2bType)) {
                return value;
            }
        }
        return EMPTY;
    }

    /**
     * 根据模板类型匹配 接口信息名称
     *
     * @param b2bType
     * @return
     */
    public static String matchingTemplateName(Integer b2bType) {
        for (B2BRequestType value : B2BRequestType.values()) {
            if (b2bType.equals(value.b2bType)) {
                return value.getInfo();
            }
        }
        return EMPTY.getInfo();
    }

    /**
     * 匹配不需要记录日志的 日志类型
     *
     * @param type
     * @return
     */
    public static boolean matchingNotLogType(Integer type) {
        for (B2BRequestType value : B2BRequestType.values()) {
            if (type.equals(SAVE_PRDT_STOCK.getB2bType())) {
                return true;
            }
            if (type.equals(SAVE_PRDT_DATE.getB2bType())) {
                return true;
            }
            if (type.equals(SAVE_PRDT.getB2bType())) {
                return true;
            }
        }
        return false;
    }

}
