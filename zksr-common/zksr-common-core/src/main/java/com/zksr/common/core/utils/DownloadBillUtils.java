package com.zksr.common.core.utils;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;

/**
 * 工具类 下载账单
 */
public class DownloadBillUtils {
    /**
     *
     * @param accessToken 微信accessToken
     * @param appKey 商户秘钥
     * @param merchantId 商户号
     * @param date 账单日期，格式:yyyymmdd,比如20231102
     */
    public static JSONObject downloadBill(String accessToken,String appKey,String merchantId,int date) throws Exception{
        HashMap<Object, Object> signData = new HashMap<>();
        signData.put("mchid", merchantId);    //  由微信支付生成并下发的商户号。示例值：1230000109
        signData.put("bill_date", "" + date);

        String signStr = JSON.toJSONString(signData);
        String paySig = calcPaySig("/retail/B2b/downloadbill", signStr, appKey);
        String url = String.format("https://api.weixin.qq.com/retail/B2b/downloadbill?access_token=%s&pay_sig=%s", accessToken, paySig);

        System.out.println("查看账单 /retail/B2b/downloadbill");
        System.out.println(signStr);
        String response = HttpUtil.post(url, signStr);
        System.out.println(response);

        // 解析 JSON 响应，提取 all_bill_url
        JSONObject resObj = JSON.parseObject(response);

        if (resObj.containsKey("profit_sharing_bill_url")) {
            System.out.println("是否存在分账账单:" + StringUtils.isNotEmpty(resObj.getString("profit_sharing_bill_url")));
        }

        return resObj;
    }



    public static String calcPaySig(String uri, String postBody, String appKey) throws Exception{
            String needSignMsg = uri + '&' + postBody;
            Mac sha256Hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(appKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256Hmac.init(secretKey);
            byte[] hash = sha256Hmac.doFinal(needSignMsg.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
    }

}
