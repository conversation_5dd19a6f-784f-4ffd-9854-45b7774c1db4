package com.zksr.common.core.utils;

import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 工作日工具类
 *
 * <AUTHOR>
 * @createtime 2025/7/14 20:25
 */
@Slf4j
public class WeekUtils {

    /**
     * 判断两个日期之间的工作日是否在N天内
     *
     * 示例：
     * Date startDate = DateUtil.parse("2025-07-14");
     * Date endDate = DateUtil.parse("2025-07-20");
     * boolean result = WeekUtils.isWithinWorkingDays(startDate, endDate, 5);
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param workingDays 工作日天数
     * @return true：在N个工作日内，false：超过N个工作日
     */
    public static boolean isWithinWorkingDays(Date startDate, Date endDate, int workingDays) {
        if (startDate == null || endDate == null) {
            throw new ServiceException("日期不能为空");
        }
        if (workingDays <= 0) {
            throw new ServiceException("工作日天数必须大于0");
        }
        if (startDate.after(endDate)) {
            throw new ServiceException("开始日期不能大于结束日期");
        }

        // 计算实际的工作日数量
        int actualWorkingDays = getWorkingDays(startDate, endDate);

        // 判断实际工作日是否在指定的工作日数量内
        return actualWorkingDays <= workingDays;
    }

    /**
     * 计算两个日期之间的工作日数量（排除周末和节假日）
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 工作日数量
     */
    public static int getWorkingDays(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            throw new ServiceException("日期不能为空");
        }
        if (startDate.after(endDate)) {
            throw new ServiceException("开始日期不能大于结束日期");
        }

        Calendar start = Calendar.getInstance();
        start.setTime(startDate);
        Calendar end = Calendar.getInstance();
        end.setTime(endDate);

        int workDays = 0;

        // 设置时分秒为0，只比较日期
        start.set(Calendar.HOUR_OF_DAY, 0);
        start.set(Calendar.MINUTE, 0);
        start.set(Calendar.SECOND, 0);
        end.set(Calendar.HOUR_OF_DAY, 0);
        end.set(Calendar.MINUTE, 0);
        end.set(Calendar.SECOND, 0);

        while (!start.after(end)) {
            // 判断是否为工作日（非周末且非节假日）
            if (isWorkingDay(start.getTime())) {
                workDays++;
            }
            // 日期加1
            start.add(Calendar.DATE, 1);
        }

        return workDays;
    }

    /**
     * 判断日期是否为工作日（非周末且非节假日）
     *
     * @param date 日期
     * @return true为工作日，false为非工作日
     */
    public static boolean isWorkingDay(Date date) {
        return !isWeekend(date) && !isHoliday(date);
    }

    /**
     * 判断日期是否为周末
     *
     * @param date 日期
     * @return true为周末，false为工作日
     */
    public static boolean isWeekend(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        // 1为周日，7为周六
        return dayOfWeek == Calendar.SUNDAY || dayOfWeek == Calendar.SATURDAY;
    }

    /**
     * 判断日期是否为节假日
     * 注意：这里需要根据实际业务情况来实现，可以：
     * 1. 通过配置文件维护节假日
     * 2. 通过数据库维护节假日
     * 3. 通过调用第三方API获取节假日
     *
     * @param date 日期
     * @return true为节假日，false为非节假日
     */
    public static boolean isHoliday(Date date) {
        // TODO: 这里需要根据实际业务情况来实现节假日的判断
        // 示例：这里简单返回false，表示没有节假日
        return false;
    }

    /**
     * 获取指定日期的下一个工作日
     *
     * @param date 指定日期
     * @return 下一个工作日
     */
    public static Date getNextWorkingDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        do {
            cal.add(Calendar.DATE, 1);
        } while (!isWorkingDay(cal.getTime()));
        return cal.getTime();
    }

    /**
     * 获取指定日期的上一个工作日
     *
     * @param date 指定日期
     * @return 上一个工作日
     */
    public static Date getPreviousWorkingDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        do {
            cal.add(Calendar.DATE, -1);
        } while (!isWorkingDay(cal.getTime()));
        return cal.getTime();
    }

    /**
     * 获取两个日期之间的所有工作日
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 工作日列表
     */
    public static List<Date> getWorkingDaysBetween(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            throw new ServiceException("日期不能为空");
        }
        if (startDate.after(endDate)) {
            throw new ServiceException("开始日期不能大于结束日期");
        }

        List<Date> workingDays = new ArrayList<>();
        Calendar start = Calendar.getInstance();
        start.setTime(startDate);
        Calendar end = Calendar.getInstance();
        end.setTime(endDate);

        while (!start.after(end)) {
            if (isWorkingDay(start.getTime())) {
                workingDays.add(start.getTime());
            }
            start.add(Calendar.DATE, 1);
        }

        return workingDays;
    }

    /**
     * 使用示例
     */
    public static void main(String[] args) {
        try {
            // 示例1：正常工作日判断（不跨周末）
            Date start1 = DateUtil.parse("2025-07-15");  // 周二
            Date end1 = DateUtil.parse("2025-07-17");    // 周四
            boolean result1 = isWithinWorkingDays(start1, end1, 3);
            System.out.println("示例1 - 是否在3个工作日内：" + result1);  // 预期输出：true

            // 示例2：跨周末判断
            Date start2 = DateUtil.parse("2025-07-18");  // 周五
            Date end2 = DateUtil.parse("2025-07-22");    // 下周二
            boolean result2 = isWithinWorkingDays(start2, end2, 3);
            System.out.println("示例2 - 是否在3个工作日内：" + result2);  // 预期输出：true

            // 示例3：超过指定工作日
            Date start3 = DateUtil.parse("2025-07-15");  // 周二
            Date end3 = DateUtil.parse("2025-07-24");    // 下周四
            boolean result3 = isWithinWorkingDays(start3, end3, 5);
            System.out.println("示例3 - 是否在5个工作日内：" + result3);  // 预期输出：false

        } catch (Exception e) {
            System.out.println("发生错误：" + e.getMessage());
        }
    }
}
