package com.zksr.common.core.context;

import com.zksr.common.core.constant.MallSecurityConstants;
import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.text.Convert;
import com.zksr.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 获取当前线程变量中的 用户id、用户名称、Token等信息
 * 注意： 必须在网关通过请求头的方法传入，同时在HeaderInterceptor拦截器设置值。 否则这里无法获取
 *
 * <AUTHOR>
 */
public class SecurityContextHolder
{
//    private static final TransmittableThreadLocal<Map<String, Object>> THREAD_LOCAL = new TransmittableThreadLocal<>();
    private static final ThreadLocal<Map<String, Object>> THREAD_LOCAL = new ThreadLocal<>();

    public static void set(String key, Object value)
    {
        Map<String, Object> map = getLocalMap();
        map.put(key, value == null ? StringUtils.EMPTY : value);
    }

    public static String get(String key)
    {
        Map<String, Object> map = getLocalMap();
        return Convert.toStr(map.getOrDefault(key, StringUtils.EMPTY));
    }

    public static Object getObject(String key) {
        Map<String, Object> map = getLocalMap();
        return map.getOrDefault(key, StringUtils.EMPTY);
    }

    public static <T> T get(String key, Class<T> clazz)
    {
        Map<String, Object> map = getLocalMap();
        return StringUtils.cast(map.getOrDefault(key, null));
    }

    public static Map<String, Object> getLocalMap()
    {
        Map<String, Object> map = THREAD_LOCAL.get();
        if (map == null)
        {
            map = new ConcurrentHashMap<String, Object>();
            THREAD_LOCAL.set(map);
        }
        return map;
    }

    public static void setLocalMap(Map<String, Object> threadLocalMap)
    {
        THREAD_LOCAL.set(threadLocalMap);
    }

    public static Long getUserId()
    {
        return Convert.toLong(get(SecurityConstants.DETAILS_USER_ID), 0L);
    }

    public static void setUserId(String account)
    {
        set(SecurityConstants.DETAILS_USER_ID, account);
    }

    public static String getUserName()
    {
        return get(SecurityConstants.DETAILS_USERNAME);
    }

    public static void setUserName(String username)
    {
        set(SecurityConstants.DETAILS_USERNAME, username);
    }

    public static String getUserKey() {
        return get(SecurityConstants.USER_KEY);
    }

    public static void setUserKey(String userKey) {
        set(SecurityConstants.USER_KEY, userKey);
    }

    public static Long getMemberId() {
        return Convert.toLong(get(MallSecurityConstants.DETAILS_MEMBER_ID), 0L);
    }

    public static void setMemberId(String account) {
        set(MallSecurityConstants.DETAILS_MEMBER_ID, account);
    }

    public static void setMemberKey(String memberKey) {
        set(MallSecurityConstants.MEMBER_KEY, memberKey);
    }

    public static String getOpensourceKey() {
        return get(OpenapiSecurityConstants.OPENSOURCE_KEY);
    }

    public static Long getOpensourceId() {
        return Convert.toLong(get(OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID), 0L);
    }

    public static void setOpensourceId(String account) {
        set(OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID, account);
    }

    public static void setOpensourceKey(String opensourceKey) {
        set(OpenapiSecurityConstants.OPENSOURCE_KEY, opensourceKey);
    }

    public static String getMemberKey() {
        return get(MallSecurityConstants.MEMBER_KEY);
    }

    public static Long getSysCode()
    {
        return Convert.toLong(get(SecurityConstants.SYS_CODE), 0L);
    }

    public static void setSysCode(String sysCode)
    {
        set(SecurityConstants.SYS_CODE, sysCode);
    }

    public static String getPermission()
    {
        return get(SecurityConstants.ROLE_PERMISSION);
    }

    public static void setPermission(String permissions)
    {
        set(SecurityConstants.ROLE_PERMISSION, permissions);
    }

    public static void remove()
    {
        THREAD_LOCAL.remove();
    }

    public static void setDcId(String dcId) {
        set(SecurityConstants.DC_ID, dcId);
    }

    public static Long getDcId()
    {
        return Convert.toLong(get(SecurityConstants.DC_ID), 0L);
    }

    public static Long hasSysCode() {
        return (Objects.nonNull(SecurityContextHolder.getSysCode()) && SecurityContextHolder.getSysCode() > 0L) ? SecurityContextHolder.getSysCode() : null;
    }

    public static List<Long> getSysCodeList() {
        Map<String, Object> map = getLocalMap();
        Object object = map.get(SecurityConstants.SYS_CODES);
        if (object instanceof String) {
            if (StringUtils.isEmpty((String) object)) {
                return null;
            }
        }
        if (Objects.nonNull(object)) {
            return (List<Long>) object;
        }
        return null;
    }

    /**
     * 获取saas系统用户编码
     */
    public static String getSaasUserCode() {
        return get(SecurityConstants.SAAS_USER_CODE);
    }

    /**
     * 设置saas系统用户编码
     */
    public static void setSaasUserCode(String saasUserCode) {
        set(SecurityConstants.SAAS_USER_CODE, saasUserCode);
    }

    /**
     * 获取saas系统租户编码
     */
    public static String getSaasTenantCode() {
        return get(SecurityConstants.SAAS_TENANT_CODE);
    }

    /**
     * 设置saas系统租户编码
     */
    public static void setSaasTenantCode(String saasTenantCode) {
        set(SecurityConstants.SAAS_TENANT_CODE, saasTenantCode);
    }

    public static void setSaasToken(String saasToken) {
        set(SecurityConstants.SAAS_ACCESS_TOKEN, saasToken);
    }
}
