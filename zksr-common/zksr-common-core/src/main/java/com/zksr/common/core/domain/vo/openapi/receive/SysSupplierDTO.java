package com.zksr.common.core.domain.vo.openapi.receive;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/10/18 14:36
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysSupplierDTO {
    /**
     * 平台商
     */
    private Long sysCode;

    /** 入驻商名称 */
    @ApiModelProperty(value = "入驻商名称")
    @NotBlank(message = "入驻商名称不能为空")
    @Size(min = 1, max = 25, message = "入驻商名称长度不能超过25个字符")
    private String supplierName;

    /** 联系人 */
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 联系地址 */
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

    /** 状态-数据字典 */
    @ApiModelProperty(value = "状态-数据字典")
    private Long status;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 是否是电子围栏入驻商 */
    @ApiModelProperty(value = "是否是电子围栏入驻商")
    private Long dzwlFlag;

    /** 营业执照图片地址*/
    @ApiModelProperty(value = "营业执照图片地址")
    private String licenceUrl;

    /**
     * 密码
     */
    @ApiModelProperty(value = "后台管理密码(不可编辑, 只可新增使用)", example = "123456")
    private String password;

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号", example = "xxxxx")
    private String userName;

    /**
     * 起送价
     */
    @ApiModelProperty("本地起送价")
    private BigDecimal minAmt;

    @ApiModelProperty("全国起送价")
    private BigDecimal globalMinAmt;

    @ApiModelProperty(value = "入驻商头像地址")
    private String avatar;

    /** 对开放资源外加密串 */
    @ApiModelProperty(value = "对开放资源外加密串")
    private String sourceSecret;

    /** 开放资源key */
    @ApiModelProperty(value = "开放资源key")
    private String sourceKey;

    /** ERP接口配置 公钥 */
    @ApiModelProperty(value = "ERP接口配置 公钥")
    private String publicKey;

    /** 对接地址 */
    @ApiModelProperty(value = "对接地址")
    private String sendUrl;

    /** 对接唯一编码 */
    @ApiModelProperty(value = "对接唯一编码")
    private String sendCode;
}
