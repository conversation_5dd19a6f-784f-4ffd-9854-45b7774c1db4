package com.zksr.common.core.domain.vo.openapi.syncCall;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 售后单明细对象
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SyncAfterOrderDetailCallDTO", description = "售后订单详情返回数据传输对象")
public class SyncAfterOrderDetailCallDTO implements Serializable {
    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "售后单明细ID", required = true)
    @TableId
    private Long supplierAfterDtlId;

    @ApiModelProperty(value = "平台商ID")
    @Excel(name = "平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "售后单编号")
    @Excel(name = "售后单编号")
    private String afterNo;

    @ApiModelProperty(value = "售后单ID")
    @Excel(name = "售后单id")
    private Long afterId;

    @ApiModelProperty(value = "售后单明细编号")
    @Excel(name = "售后单明细编号")
    private String supplierAfterDtlNo;

    @ApiModelProperty(value = "入驻商售后单ID")
    @Excel(name = "入驻商售后单id")
    private Long supplierAfterId;

    @ApiModelProperty(value = "入驻商售后单编号")
    @Excel(name = "入驻商售后单编号")
    private String supplierAfterNo;

    @ApiModelProperty(value = "入驻商订单明细ID")
    @Excel(name = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    @ApiModelProperty(value = "入驻商订单明细编号")
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    @ApiModelProperty(value = "入驻商ID")
    @Excel(name = "入驻商id")
    private Long supplierId;

    @ApiModelProperty(value = "商品类型 0-本地配送商品 1-全国一件代发商品")
    @Excel(name = "0-本地配送商品 1-全国一件代发商品")
    private Long itemType;

    @ApiModelProperty(value = "上架商品ID")
    @Excel(name = "上架商品id")
    private Long itemId;

    @ApiModelProperty(value = "ERP商品编号")
    @Excel(name = "ERP商品编号")
    private String erpItemNo;

    /** 商品SKU ID*/
    private Long skuId;

    @ApiModelProperty(value = "退货原因")
    @Excel(name = "退货原因")
    private String reason;

    @ApiModelProperty(value = "退货数量")
    @Excel(name = "退货数量")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "退货价")
    @Excel(name = "退货价")
    private BigDecimal returnPrice;

    @ApiModelProperty(value = "退货金额")
    @Excel(name = "退货金额")
    private BigDecimal returnAmt;

    @ApiModelProperty(value = "退款金额")
    @Excel(name = "退款金额")
    private BigDecimal refundAmt;

    @ApiModelProperty(value = "退货说明")
    @Excel(name = "退货说明")
    private String descr;

    @ApiModelProperty(value = "售后类型")
    @Excel(name = "售后类型")
    private Long afterType;

    @ApiModelProperty(value = "售后阶段")
    @Excel(name = "售后阶段")
    private Long afterPhase;

    @ApiModelProperty(value = "退款类型")
    @Excel(name = "退款类型")
    private Long refundType;

    @ApiModelProperty(value = "审核状态")
    @Excel(name = "审核状态")
    private Long approveState;

    @ApiModelProperty(value = "退货状态")
    @Excel(name = "退货状态")
    private Long returnState;

    @ApiModelProperty(value = "退款状态")
    @Excel(name = "退款状态")
    private Long refundState;

    @ApiModelProperty(value = "退款失败原因")
    @Excel(name = "退款失败原因")
    private String refundFailReason;

    @ApiModelProperty(value = "退款总金额")
    @Excel(name = "退款总金额")
    private BigDecimal orderdtlRefundAmt;

    @ApiModelProperty(value = "退款方式")
    @Excel(name = "退款方式")
    private String payway;

    @ApiModelProperty(value = "支付平台")
    @Excel(name = "支付平台")
    private String platform;

    @ApiModelProperty(value = "订单支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单支付时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    @ApiModelProperty(value = "订单完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date finishTime;

    @ApiModelProperty(value = "支付状态")
    @Excel(name = "支付状态")
    private Integer payState;

    @ApiModelProperty(value = "退货完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退货完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date returnTime;

    @ApiModelProperty(value = "退款完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退款完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date refundTime;

    @ApiModelProperty(value = "是否已取消")
    @Excel(name = "是否已取消")
    private Long isCancel;

    @ApiModelProperty(value = "来源")
    @Excel(name = "来源")
    private Long source;

    @ApiModelProperty(value = "是否已同步")
    @Excel(name = "是否已同步")
    private Integer syncFlag;

    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String memo;

    @ApiModelProperty(value = "删除标记")
    private Integer delFlag;

    @ApiModelProperty(value = "用于发起退款流程的退款单号")
    @Excel(name = "退款单号")
    private String refundNo;

    @ApiModelProperty(value = "精准成交价（6位小数）")
    @Excel(name = "精准退款成交价")
    private BigDecimal exactReturnPrice;

    @ApiModelProperty(value = "精准商品金额（6位小数）")
    @Excel(name = "精准退款商品金额")
    private BigDecimal exactReturnAmt;

    @ApiModelProperty(value = "优惠劵优惠金额(分摊的)")
    @Excel(name = "售后优惠劵优惠金额(分摊的)")
    private BigDecimal returnCouponDiscountAmt;

    @ApiModelProperty(value = "优惠劵优惠金额(不分摊的)")
    @Excel(name = "售后优惠劵优惠金额(不分摊的)")
    private BigDecimal returnCouponDiscountAmt2;

    @ApiModelProperty(value = "活动优惠金额(分摊的)")
    @Excel(name = "售后活动优惠金额(分摊的)")
    private BigDecimal returnActivityDiscountAmt;

    @ApiModelProperty(value = "是否是赠品 1-是  0-否")
    @Excel(name = "是否是赠品 1-是  0-否")
    private Integer giftFlag;

    @ApiModelProperty(value = "入驻商售后订单行号")
    @Excel(name = "入驻商售后订单行号")
    private Long lineNum;

    @ApiModelProperty(value = "订单购买单位;从订单，最小单位的单位，中单位的单位，大单位的单位")
    @Excel(name = "订单购买单位")
    private String orderUnit;

    @ApiModelProperty(value = "单位大小")
    @Excel(name = "单位大小")
    private Integer orderUnitType;

    @ApiModelProperty(value = "订单购买单位数量;从订单，购买的是中单位，即为中单位数量。小单位、大单位同理")
    @Excel(name = "订单购买单位数量")
    private Long orderUnitQty;

    @ApiModelProperty(value = "订单换算数量;从订单，小单位为1，中、大单位的换算数量")
    @Excel(name = "订单换算数量")
    private Long orderUnitSize;

    @ApiModelProperty(value = "售后单位;最小单位的单位，中单位的单位，大单位的单位")
    @Excel(name = "售后单位")
    private String returnUnit;

    @ApiModelProperty(value = "单位大小")
    @Excel(name = "单位大小")
    private Integer returnUnitType;

    @ApiModelProperty(value = "售后单位数量;发货的是中单位，即为中单位数量。小单位、大单位同理")
    @Excel(name = "售后单位数量")
    private BigDecimal returnUnitQty;

    @ApiModelProperty(value = "售后单位换算数量;小单位为1，中、大单位的换算数量")
    @Excel(name = "售后单位换算数量")
    private Long returnUnitSize;

    /** 商品spu 名称 */
    @ApiModelProperty(value = "商品spu 名称")
    @Excel(name = "商品spu 名称")
    private String spuName;

    /**
     * 平台商品牌id
     */
    @ApiModelProperty(value = "平台商品牌id")
    @Excel(name = "平台商品牌id")
    private Long brandId;

    /**
     * 管理分类Id
     */
    @ApiModelProperty(value = "管理分类Id")
    @Excel(name = "管理分类Id")
    private Long categoryId;
}
