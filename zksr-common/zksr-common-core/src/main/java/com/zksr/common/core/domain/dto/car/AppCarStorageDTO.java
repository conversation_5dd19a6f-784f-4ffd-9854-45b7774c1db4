package com.zksr.common.core.domain.dto.car;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>   
 * @version 1.0
 * @description: 购物车carid 定义
 * @date 2024/3/26 16:25
 */
@Data
@ApiModel(description ="购物车存储信息")
public class AppCarStorageDTO {

    @ApiModelProperty(value = "商品数量", notes = "P1")
    private Integer productNum = 0;

    @ApiModelProperty(value = "是否业务员推荐, 0 否, 1 是", notes = "P2")
    private Integer recommendFlag = 0;

    @ApiModelProperty(value = "入驻商ID", notes = "P3")
    private Long supplierId = NumberPool.LOWER_GROUND_LONG;

    @ApiModelProperty(value = "本地商品ID", notes = "P4")
    private Long areaItemId = NumberPool.LOWER_GROUND_LONG;

    @ApiModelProperty(value = "入驻商商品ID", notes = "P5")
    private Long supplierItemId = NumberPool.LOWER_GROUND_LONG;

    @ApiModelProperty(value = "skuID", notes = "P6")
    private Long skuId = NumberPool.LOWER_GROUND_LONG;

    @ApiModelProperty(value = "spuId", notes = "P7")
    private Long spuId = NumberPool.LOWER_GROUND_LONG;

    @ApiModelProperty(value = "unit, 单位", notes = "P8")
    private String unit = StringPool.ZERO;
    /**
     * 参见枚举 {@link com.zksr.common.core.enums.UnitTypeEnum}
     */
    @ApiModelProperty(value = "单位类型, 1-最小单位, 2-中单位, 3-大单位", notes = "P9")
    private Integer unitSize;

    @ApiModelProperty(value = "业务员推荐数量", notes = "P10")
    private Integer recommendNum = 0;

    @ApiModelProperty(value = "组合商品ID", notes = "P11")
    private Long spuCombineId;

    @ApiModelProperty(value = "促销活动ID", notes = "P12")
    private Long activityId;

    @ApiModelProperty(value = "指令ID", notes = "P13")
    private Long commandId;

    @ApiModelProperty(value = "加单时间,毫秒值", notes = "P14")
    private Long doubleOrder;

    @ApiModelProperty(value = "加入购物车时间,毫秒值", notes = "P15")
    private Long addTime;

    public static List<String> fields = new ArrayList<>();

    public static Map<Integer, String> fieldMap = new HashMap<>();

    static {
        fields.add("P1");
        fields.add("P2");
        fields.add("P3");
        fields.add("P4");
        fields.add("P5");
        fields.add("P6");
        fields.add("P7");
        fields.add("P8");
        fields.add("P9");
        fields.add("P10");
        fields.add("P11");
        fields.add("P12");
        fields.add("P13");
        fields.add("P14");
        fields.add("P15");

        fieldMap.put(0, "productNum");
        fieldMap.put(1, "recommendFlag");
        fieldMap.put(2, "supplierId");
        fieldMap.put(3, "areaItemId");
        fieldMap.put(4, "supplierItemId");
        fieldMap.put(5, "skuId");
        fieldMap.put(6, "spuId");
        fieldMap.put(7, "unit");
        fieldMap.put(8, "unitSize");
        fieldMap.put(9, "recommendNum");
        fieldMap.put(10, "spuCombineId");
        fieldMap.put(11, "activityId");
        fieldMap.put(12, "commandId");
        fieldMap.put(13, "doubleOrder");
        fieldMap.put(14, "addTime");
    }

    public Map<String, Object> toMap() {
        HashMap<String, Object> map = new HashMap<>();
        for (int line = 0; line < fields.size(); line++) {
            String field = fieldMap.get(line);
            Object property = BeanUtil.getProperty(this, field);
            if (Objects.nonNull(property)) {
                map.put(fields.get(line), property);
            }
        }
        return map;
    }

    public static AppCarStorageDTO build(List<String> list) {
        if (Objects.isNull(list) || list.isEmpty()) {
            return null;
        }
        AppCarStorageDTO storageDTO = new AppCarStorageDTO();
        for (int line = 0; line < list.size(); line++) {
            if (fieldMap.containsKey(line)) {
                String field = fieldMap.get(line);
                if (Objects.nonNull(list.get(line))) {
                    BeanUtil.setProperty(storageDTO, field, list.get(line));
                }
            }
        }
        return storageDTO;
    }

    public static AppCarStorageDTO build(Map<String, Object> map) {
        if (Objects.isNull(map) || map.isEmpty()) {
            return null;
        }
        AppCarStorageDTO storageDTO = new AppCarStorageDTO();
        for (String fields : map.keySet()) {
            switch (fields) {
                case "P1":
                    storageDTO.setProductNum(Integer.parseInt(String.valueOf(map.get(fields))));
                    break;
                case "P2":
                    storageDTO.setRecommendFlag(Integer.parseInt(String.valueOf(map.get(fields))));
                    break;
                case "P3":
                    storageDTO.setSupplierId(Long.parseLong(String.valueOf(map.get(fields))));
                    break;
                case "P4":
                    storageDTO.setAreaItemId(Long.parseLong(String.valueOf(map.get(fields))));
                    break;
                case "P5":
                    storageDTO.setSupplierItemId(Long.parseLong(String.valueOf(map.get(fields))));
                    break;
                case "P6":
                    storageDTO.setSkuId(Long.parseLong(String.valueOf(map.get(fields))));
                    break;
                case "P7":
                    storageDTO.setSpuId(Long.parseLong(String.valueOf(map.get(fields))));
                    break;
                case "P8":
                    storageDTO.setUnit(String.valueOf(map.get(fields)));
                    break;
                case "P9":
                    storageDTO.setUnitSize(Integer.parseInt(String.valueOf(map.get(fields))));
                    break;
                case "P10":
                    storageDTO.setRecommendNum(Integer.parseInt(String.valueOf(map.get(fields))));
                    break;
                case "P11":
                    storageDTO.setSpuCombineId(Long.parseLong(String.valueOf(map.get(fields))));
                    break;
                case "P12":
                    storageDTO.setActivityId(Long.parseLong(String.valueOf(map.get(fields))));
                    break;
                case "P13":
                    storageDTO.setCommandId(Long.parseLong(String.valueOf(map.get(fields))));
                    break;
                case "P14":
                    storageDTO.setDoubleOrder(Long.parseLong(String.valueOf(map.get(fields))));
                    break;
                case "P15":
                    storageDTO.setAddTime(Long.parseLong(String.valueOf(map.get(fields))));
                    break;
                default:
                    break;
            }
        }
        // 没有入驻商记录, 判定为脏数据
        if (Objects.isNull(storageDTO.getSupplierId())) {
            return null;
        }
        return storageDTO;
    }

    public String getUniqueKey() {
        return StringUtils.format("{}_{}", itemId(), unitSize);
    }


    public AppCarIdDTO getCarId(ProductType productType, Long branchId) {
        return new AppCarIdDTO(
                productType.getType(),
                branchId,
                supplierId,
                supplierItemId,
                areaItemId,
                skuId,
                spuId,
                unitSize
        );
    }

    /**
     * 自动返回全国还是本地上架ID
     * @return
     */
    public Long itemId() {
        if (Objects.nonNull(areaItemId) && areaItemId > 0L) {
            return areaItemId;
        }
        return supplierItemId;
    }

    @JsonIgnore
    public boolean isSpuCombine() {
        return Objects.nonNull(spuCombineId);
    }
}
