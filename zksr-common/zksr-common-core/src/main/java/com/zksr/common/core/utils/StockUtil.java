package com.zksr.common.core.utils;

import cn.hutool.core.util.NumberUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/10 16:11
 */
public class StockUtil {

    /**
     * 转换 BigDecimal 参数类型
     */
    public static BigDecimal bigDecimal(Object val) {
        if (Objects.nonNull(val)) {
            return new BigDecimal(String.valueOf(val));
        }
        return null;
    }

    /**
     * 转换 BigDecimal 参数类型
     */
    public static BigDecimal bigDecimal(Long val) {
        if (Objects.nonNull(val)) {
            return new BigDecimal(val);
        }
        return null;
    }

    /**
     * 转换 BigDecimal 参数类型
     */
    public static BigDecimal bigDecimal(Integer val) {
        if (Objects.nonNull(val)) {
            return new BigDecimal(val);
        }
        return null;
    }

    /**
     * 将库存 BigDecimal 变成整数
     * @param val   BigDecimal库存
     * @return  整数
     */
    public static BigDecimal stockLong(BigDecimal val) {
        return val.setScale(0, RoundingMode.DOWN);
    }

    /**
     * 慎用, 建议仅用于, 当前最小库存数, 可以购买多个中大单位
     */
    public static BigDecimal stockDivide(BigDecimal a, BigDecimal b) {
        // 防止空指针, 除0异常
        if (Objects.isNull(a) || Objects.isNull(b) || NumberUtil.equals(BigDecimal.ZERO, b)) {
            return BigDecimal.ZERO;
        }
        return a.divide(b, 3, RoundingMode.DOWN);
    }

    /**
     * 慎用, 建议仅用于, 当前最小库存数, 可以购买多个中大单位
     */
    public static BigDecimal stockDivide(Long a, BigDecimal b) {
        return stockDivide(new BigDecimal(a), b);
    }

    /**
     * 慎用, 建议仅用于, 当前最小库存数, 可以购买多个中大单位
     */
    public static BigDecimal stockDivide(Integer a, BigDecimal b) {
        return stockDivide(new BigDecimal(a), b);
    }

    public static BigDecimal stockMultiply(BigDecimal a, BigDecimal b) {
        return a.multiply(b);
    }

    public static BigDecimal stockMultiply(Integer a, BigDecimal b) {
        return new BigDecimal(a).multiply(b);
    }

    public static boolean isGreaterOrEqual(BigDecimal a, BigDecimal b) {
        if (Objects.isNull(a) || Objects.isNull(b)) {
            return false;
        }
        return NumberUtil.isGreaterOrEqual(a, b);
    }

    public static boolean isGreater(BigDecimal a, BigDecimal b) {
        if (Objects.isNull(a) || Objects.isNull(b)) {
            return false;
        }
        return NumberUtil.isGreater(a, b);
    }
}
