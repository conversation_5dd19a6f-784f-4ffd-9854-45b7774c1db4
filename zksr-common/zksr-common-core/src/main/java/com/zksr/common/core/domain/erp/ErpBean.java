package com.zksr.common.core.domain.erp;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.domain.erp.dto.AfterSheetOpenDto;
import com.zksr.common.core.domain.erp.dto.ErpReceiptDTO;
import com.zksr.common.core.domain.vo.openapi.AfterPushDTO;
import com.zksr.common.core.domain.vo.openapi.BranchOpenDTO;
import com.zksr.common.core.domain.vo.openapi.OrderOpenDTO;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.enums.request.RequestType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErpBean {

    /** 请求编号id */
    @Excel(name = "请求编号id")
    @ApiModelProperty(value = "请求编号id")
    private String reqId;

    /** 入驻商id */
    private Long supplierId;

    /** 入驻商id列表 */
    private List<Long> supplierIdList;

    /** 请求类型 */
    private OperationType type;

    /** 门店信息 */
    private BranchOpenDTO branchOpenDTO;

    /** 销售订单 */
    private OrderOpenDTO orderOpenDTO;

    /** 售后订单 */
    private AfterPushDTO afterOpenDTO;

    /** 订单支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款 */
    private String payWay;

    /** 退货单 */
    private AfterSheetOpenDto afterSheetOpenDto;

    private RequestType requestType;

    /** b2b推送erp收款或退款信息 */
    private ErpReceiptDTO erpReceiptDTO;

}
