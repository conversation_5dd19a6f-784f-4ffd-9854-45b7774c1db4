package com.zksr.common.core.domain.vo.openapi.receive;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 订单状态（ERP->B2B）对象 trd_express_status
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@Data
@ApiModel("订单状态（ERP->B2B） - trd_express_status分页 Request VO")
@NoArgsConstructor
public class OrderStateReturnVO {
    private static final long serialVersionUID = 1L;

    /** 订单号(B2B入驻商销售订单编号) */
    @Excel(name = "订单号(B2B入驻商销售订单编号)")
    @ApiModelProperty(value = "订单号(B2B入驻商销售订单编号)")
    @NotNull(message = "B2B入驻商销售订单编号不能为空")
    private String supplierOrderNo;

    /** 物流状态（1待出库、2待配送、3配送中、4已配送（暂弃用）、5已收货）  枚举：ReceiveLogisticsStatusEnum */
    @Excel(name = "物流状态", readConverterExp = "物流状态（1待出库、2待配送、3配送中、4已配送（暂弃用）、5已收货） ")
    @ApiModelProperty(value = "物流状态（1待出库、2待配送、3配送中、4已配送（暂弃用）、5已收货）")
    @NotNull(message = "物流状态不能为空")
    private Integer logisticsStatus;

/*    *//** 顺序标识字段 *//*
    @Excel(name = "顺序标识字段")
    @ApiModelProperty(value = "顺序标识字段")
    private Long sort;

    *//** 收货是否收款 *//*
    @Excel(name = "收货是否收款")
    @ApiModelProperty(value = "收货是否收款")
    private Integer isProceeds;*/

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注（司机+ 电话）")
    private String memo;
}
