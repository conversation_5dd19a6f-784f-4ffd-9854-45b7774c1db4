package com.zksr.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户进件状态
 * @date 2024/7/13 14:45
 */
@Getter
@AllArgsConstructor
public enum MerchantRegisterStateEnum {
    INIT("INIT", "待审核"),
    OVERRULE("OVERRULE", "审核失败/驳回"),
    REFUSE("REFUSE", "审核拒绝"),
    AUDITED("AUDITED", "审核通过"),
    AUDITING("AUDITING", "审核中"),
    ACCOUNT_NEED_VERIFY("ACCOUNT_NEED_VERIFY", "账户待验证"),
    UNSIGNED("UNSIGNED", "账户待签约"),
            ;
    /**
     * 状态
     */
    private final String state;

    /**
     * 名字
     */
    private final String desc;

    public static boolean fail(String auditStatus) {
        return OVERRULE.getState().equals(auditStatus) || REFUSE.getState().equals(auditStatus);
    }

    public static boolean success(String auditStatus) {
        return AUDITED.getState().equals(auditStatus);
    }

    public static boolean wait(String auditStatus) {
        return INIT.getState().equals(auditStatus);
    }

    public static boolean updateLink(String auditStatus) {
        return UNSIGNED.getState().equals(auditStatus) || ACCOUNT_NEED_VERIFY.getState().equals(auditStatus);
    }

    public boolean updateLink() {
        return UNSIGNED.getState().equals(this.getState()) || ACCOUNT_NEED_VERIFY.getState().equals(this.getState());
    }

    public boolean fail() {
        return OVERRULE.getState().equals(this.getState()) || REFUSE.getState().equals(this.getState());
    }

    public boolean success() {
        return AUDITED.getState().equals(this.getState());
    }

    public boolean waited() {
        return INIT.getState().equals(this.getState());
    }

    public boolean pocessing() {
        return AUDITING.getState().equals(this.getState());
    }

    public static MerchantRegisterStateEnum getState(String state) {
        for (MerchantRegisterStateEnum value : values()) {
            if (value.getState().equals(state)) {
                return value;
            }
        }
        return null;
    }
}
