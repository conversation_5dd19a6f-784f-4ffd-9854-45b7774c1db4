package com.zksr.common.core.web;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.zksr.common.core.pool.StringPool;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Objects;

public class CustomStockIntSerialize extends JsonSerializer<Object> {

    @Override
    public void serialize(Object objVal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (objVal instanceof BigDecimal) {
            jsonGenerator.writeNumber(((BigDecimal) objVal).longValue());
        } else if (Objects.isNull(objVal)) {
            jsonGenerator.writeString(StringPool.EMPTY);
        } else if (objVal instanceof Number){
            jsonGenerator.writeNumber((Long) objVal);
        }
    }
}
