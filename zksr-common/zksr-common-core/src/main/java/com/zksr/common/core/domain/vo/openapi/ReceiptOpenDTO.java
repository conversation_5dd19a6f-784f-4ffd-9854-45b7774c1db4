package com.zksr.common.core.domain.vo.openapi;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
*  收款单主表 对外接口信息
* @date 2024/9/12 16:24
* <AUTHOR>
*/
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ReceiptOpenDTO {

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店编号")
    private String branchNo;

    /** 门店id */
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 业务员id */
    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    @ApiModelProperty(value = "收款总金额")
    private BigDecimal totalReceiptAmt;

    @ApiModelProperty(value = "收款精确总金额")
    private BigDecimal totalReceiptExactAmt;

    /** 对接唯一编码 */
    @Excel(name = "对接唯一编码")
    private String sendCode;

    @ApiModelProperty(value = "明细数据集合")
    private List<ReceiptDetailOpenDTO> detailList;

}
