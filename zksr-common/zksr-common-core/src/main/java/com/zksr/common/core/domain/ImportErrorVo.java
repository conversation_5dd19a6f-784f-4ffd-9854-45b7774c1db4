package com.zksr.common.core.domain;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("导入Excel错误信息")
public class ImportErrorVo {
    @Excel(name = "错误信息", sort = Integer.MIN_VALUE)
    @ApiModelProperty("错误信息")
    private String errorMsg;
    @ApiModelProperty("是否有错")
    private boolean hasError;

    public void appendImportErrorMsg(String errorMsg) {
        if (StringUtils.isBlank(errorMsg)) {
            return;
        }
        if (StringUtils.isBlank(this.getErrorMsg())) {
            this.setErrorMsg(errorMsg);
        } else {
            this.setErrorMsg(this.getErrorMsg() + "; " + errorMsg);
        }
        hasError = true;
    }
}
