package com.zksr.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 *
 * 美信告警配置
 *
 */

@Configuration
@ConfigurationProperties(prefix = "midea.monitor")
@Data
public class MxAlertConfig {
    //美信群webhook, https://mapnew5.midea.com/mrm/v5/open/robot/send?key=54fdeb68bf47481ab83bccd31d88d35a
    private String warningurl;
    // /xops/open-api/send/address/send_meixin_group
    private String warningpath;
    //@Value("${app.envName:}")
    private String envname;
    //@Value("${app.ak:}")
    private String ak;
    //@Value("${app.sk:}")
    private String sk;
    //@Value("${app.mxNoticeUrl:}")
    private String noticeurl;

}
