package com.zksr.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 交易状态
 * @date 2024/3/11 14:16
 */
@AllArgsConstructor
@Getter
public enum TransferStatusEnum {
    WAITING(0, "未发起"),
    CREATED(1, "发起成功"),
    SUCCESS(2, "交易成功"),
    FAIL(3, "交易失败"),
    PROCESSING(4, "处理中"),

    // 需要前往第三方平台处理, 详细参阅文档
    INTERVENE(5, "人工干预"),
    ;

    private final Integer status;
    private final String name;

    public static boolean isSuccess(Integer status) {
        return Objects.equals(status, SUCCESS.getStatus()) || Objects.equals(status, CREATED.getStatus());
    }

    public static boolean isSuccess2(Integer status) {
        return Objects.equals(status, SUCCESS.getStatus());
    }

    public static boolean isFail(Integer status) {
        return Objects.equals(status, FAIL.getStatus());
    }
}
