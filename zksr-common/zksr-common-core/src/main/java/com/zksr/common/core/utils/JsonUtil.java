package com.zksr.common.core.utils;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.domain.vo.openapi.CustomApiDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

/**
 * Util - JSON
 */

@Slf4j
public class JsonUtil {
	

	
	
	
	//--------------------------以下为新增加--------------------

    /**
      * @param object
      *             任意对象
      * @return java.lang.String
      */  
    public static String objectToJson(Object object) {   
         StringBuilder json = new StringBuilder();   
        if (object == null) {   
             json.append("\"\"");   
         } else if (object instanceof String || object instanceof Integer) { 
             json.append("\"").append(object.toString()).append("\"");  
         } else {   
             json.append(beanToJson(object));   
         }   
        return json.toString();   
     }   
  
    /**
      * 功能描述:传入任意一个 javabean 对象生成一个指定规格的字符串
      *
      * @param bean
      *             bean对象
      * @return String
      */  
    public static String beanToJson(Object bean) {   
         StringBuilder json = new StringBuilder();   
         json.append("{");   
         PropertyDescriptor[] props = null;   
        try {   
             props = Introspector.getBeanInfo(bean.getClass(), Object.class)   
                     .getPropertyDescriptors();   
         } catch (IntrospectionException e) {   
         }   
        if (props != null) {   
            for (int i = 0; i < props.length; i++) {   
                try {  
                     String name = objectToJson(props[i].getName());   
                     String value = objectToJson(props[i].getReadMethod().invoke(bean));  
                     json.append(name);   
                     json.append(":");   
                     json.append(value);   
                     json.append(",");  
                 } catch (Exception e) {   
                 }   
             }   
             json.setCharAt(json.length() - 1, '}');   
         } else {   
             json.append("}");   
         }   
        return json.toString();   
     }   

	public static void modifyFieldValue(JSONObject jsonObject, String fieldName, Object newValue) {
		for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
			if (entry.getKey().equals(fieldName)) {
				jsonObject.put(entry.getKey(), newValue);
				return;
			}
			if (entry.getValue() instanceof JSONObject) {
				modifyFieldValue((JSONObject) entry.getValue(), fieldName, newValue);
			}
			if (entry.getValue() instanceof JSONArray) {
				for(Object jsonDetail:(JSONArray)entry.getValue()){
					modifyFieldValue((JSONObject) jsonDetail, fieldName, newValue);
				}
			}
		}
	}

	public static Object getFieldValue(JSONObject jsonObject, String fieldName) {
		for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
			if (entry.getKey().equals(fieldName)) {
				return jsonObject.get(entry.getKey());
			}
			if (entry.getValue() instanceof JSONObject) {
				getFieldValue((JSONObject) entry.getValue(), fieldName);
			}
			if (entry.getValue() instanceof JSONArray) {
				for(Object jsonDetail:(JSONArray)entry.getValue()){
					getFieldValue((JSONObject) jsonDetail, fieldName);
				}
			}
		}
		return null;
	}

	public static String convertJsonToFormUrlEncoded(JSONObject jsonObject) throws UnsupportedEncodingException {
		if (jsonObject == null) {
			return "";
		}

		StringBuilder formUrlEncoded = new StringBuilder();
		processJsonObject(jsonObject, "", formUrlEncoded);
		return formUrlEncoded.toString();
	}

	private static void processJsonObject(JSONObject jsonObject, String prefix, StringBuilder formUrlEncoded) throws UnsupportedEncodingException {
		Iterator<String> keys = jsonObject.keySet().iterator();
		while (keys.hasNext()) {
			String key = keys.next();
			Object value = jsonObject.get(key);

			if (value instanceof JSONObject) {
				processJsonObject((JSONObject) value, prefix + key + ".", formUrlEncoded);
			} else if (value instanceof JSONArray) {
				processJsonArray((JSONArray) value, prefix + key + "[", formUrlEncoded);
			} else {
				if (formUrlEncoded.length() > 0) {
					formUrlEncoded.append("&");
				}
				formUrlEncoded.append(URLEncoder.encode(prefix + key, StandardCharsets.UTF_8.toString()))
						.append("=")
						.append(URLEncoder.encode(String.valueOf(value), StandardCharsets.UTF_8.toString()));
			}
		}
	}

	private static void processJsonArray(JSONArray jsonArray, String prefix, StringBuilder formUrlEncoded) throws UnsupportedEncodingException {
		for (int i = 0; i < jsonArray.size(); i++) {
			Object value = jsonArray.get(i);
			if (value instanceof JSONObject) {
				processJsonObject((JSONObject) value, prefix + i + ".", formUrlEncoded);
			} else if (value instanceof JSONArray) {
				// 递归处理嵌套数组，但这通常很少见且复杂，可能需要特殊处理
				processJsonArray((JSONArray) value, prefix + i + "[", formUrlEncoded);
			} else {
				if (formUrlEncoded.length() > 0) {
					formUrlEncoded.append("&");
				}
				formUrlEncoded.append(URLEncoder.encode(prefix + i, StandardCharsets.UTF_8.toString()))
						.append("=")
						.append(URLEncoder.encode(String.valueOf(value), StandardCharsets.UTF_8.toString()));
			}
		}
	}

	public static <T> JSONObject loadJson(T t,List<Object> details,List<CustomApiDetail> apiDetails) {
		JSONObject jsonObject = loadJsonRoot(t,details,apiDetails);
		System.out.println(jsonObject);
		return jsonObject;
	}


	public static <T> JSONObject loadJsonRoot(T t,List<Object> details, List<CustomApiDetail> apiDetails) {
		JSONObject jsonObject = new JSONObject();
		// 存储根字段以及子字段
		Map<String, CustomApiDetail> rootMap = new HashMap<>();
		Map<String, List<CustomApiDetail>> nodeDetailMap = new HashMap<>();
		// 先渲染JSON根节点
		for (CustomApiDetail detail : apiDetails) {
			if (ObjectUtils.isNotEmpty(detail.getFieldName()) && ObjectUtils.isEmpty(detail.getParentFiledName())) {
				rootMap.put(detail.getFieldName(), detail);
			}else{
				List<CustomApiDetail> nodeDetails = nodeDetailMap.get(detail.getParentFiledName());
				if(ObjectUtils.isEmpty(nodeDetails)){
					nodeDetails = new ArrayList<>();
				}
				nodeDetails.add(detail);
				nodeDetailMap.put(detail.getParentFiledName(), nodeDetails);
			}
		}

		// 组装JSON子节点数据
		for(CustomApiDetail detail:rootMap.values()){
			Object children = loadChildrenTree(t,details,nodeDetailMap,detail);
			jsonObject.put(detail.getFieldName(),children);
		}

		return jsonObject;
	}

	private static <T> Object loadChildrenTree(T t,List<Object> details, Map<String,List<CustomApiDetail>> nodeDetailMap, CustomApiDetail parentDetail){
		try {
			if(ObjectUtils.isEmpty(parentDetail.getFieldName())){
				return new JSONObject();
			}
			List<CustomApiDetail> childrenDetails = nodeDetailMap.get(parentDetail.getFieldName());
			if(ObjectUtils.isEmpty(childrenDetails) || childrenDetails.isEmpty()){
				// 当前已走到最底层，返回值
				String fieldValue = ObjectUtils.isEmpty(parentDetail.getDefaultValue()) ? "" : parentDetail.getDefaultValue();
				if(ObjectUtils.isNotEmpty(parentDetail.getModelFieldName())){
					String value = getValue(t,parentDetail.getModelFieldName());
					// 如果映射字段取值为空，使用默认值
					fieldValue = ObjectUtils.isEmpty(value) ? fieldValue : value;
				}
				return fieldValue;
			}

			JSONArray childrenArray = new JSONArray();
			JSONObject childrenTree = new JSONObject();

			// 当前字段是主字段,填充明细
			if("1".equals(parentDetail.getIsMaster())){
				for (Object detail:details){
					for (CustomApiDetail children : childrenDetails) {
						if(ObjectUtils.isNotEmpty(children.getParentFiledName()) && children.getParentFiledName().equals(parentDetail.getFieldName())){
							childrenTree.put(children.getFieldName(),loadChildrenTree(detail,details,nodeDetailMap,children));
						}
					}
					childrenArray.add(childrenTree);
				}
				return childrenArray;
			}

			// 递归填充子节点
			for (CustomApiDetail children : childrenDetails) {
				if(ObjectUtils.isNotEmpty(children.getParentFiledName()) && children.getParentFiledName().equals(parentDetail.getFieldName())){
					childrenTree.put(children.getFieldName(),loadChildrenTree(t,details,nodeDetailMap,children));
				}
			}

			// 当前字段数据类型是数组，返回数组
			if("ARRAY".equals(parentDetail.getFieldDataType()) && !"1".equals(parentDetail.getIsMaster())){
				childrenArray.add(childrenTree);
				return childrenArray;
			}
			return childrenTree;
		}catch (Exception e){
			log.error(" 加载JSON子节点时报错：", e);
			return null;
		}
	}

	private static <T> String getValue(T t,String field) {
		try {
			BeanInfo beanInfo = Introspector.getBeanInfo(t.getClass());
			PropertyDescriptor[] descriptors = beanInfo.getPropertyDescriptors();
			for (PropertyDescriptor descriptor : descriptors) {
				if (descriptor.getName().equals(field)) {
					if (null == descriptor.getReadMethod().invoke(t)) return "";
					Object obj = descriptor.getReadMethod().invoke(t);
					if(obj.getClass() == Date.class){
						SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						return simpleDateFormat.format(obj);
					}
					return obj.toString();
				}
			}
		} catch (IntrospectionException | IllegalAccessException | InvocationTargetException e) {

			log.error("根据字段名获取属性值失败,", e);
		}
		return null;
	}

}