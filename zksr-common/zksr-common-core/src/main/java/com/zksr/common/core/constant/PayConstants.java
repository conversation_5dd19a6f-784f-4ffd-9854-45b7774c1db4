package com.zksr.common.core.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付常量
 * @date 2024/4/8 15:28
 */
public class PayConstants {
    /**
     * 模拟进件编号
     */
    public static String DEFAULT_MOCK_NO = "MOCK_00001";

    /**
     * 默认进件名称
     */
    public static String DEFAULT_MOCK_NAME = "模拟账户";

    /**
     * 默认进件名称
     */
    public static String DEFAULT_BANK_NAME = "中科商软股份有限制银行";

    /**
     * 收单手续费
     */
    public static BigDecimal PAY_FREE = new BigDecimal("0.003");

    /**
     * 最低一分钱手续费
     */
    public static BigDecimal MIN_FEE = new BigDecimal("0.01");

    /**
     * 对私
     */
    public static String TO_PRIVATE = "TOPRIVATE";

    /**
     * 对私
     */
    public static String TO_PUBLIC = "TOPUBLIC";

    /**
     * 入驻商订单商品列表
     */
    public static String SUPPLIER_ORDER_DTL_ID_LIST = "supplierOrderDtlIdList";
}
