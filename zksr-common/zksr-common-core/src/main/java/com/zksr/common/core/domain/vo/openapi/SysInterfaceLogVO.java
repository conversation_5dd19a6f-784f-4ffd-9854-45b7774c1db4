package com.zksr.common.core.domain.vo.openapi;

import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import static com.zksr.common.core.constant.OpenApiConstants.*;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @Date 2024/5/31 17:35
 * @注释
 */
@Data
@ApiModel("同步接口日志 - sys_interface_log  VO")
@AllArgsConstructor
@NoArgsConstructor
public class SysInterfaceLogVO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(value = "消息重发失败日志记录")
    private Long id;

    /** 请求编号id */
    @Excel(name = "请求编号id redisService.getUniqueNumber()获得唯一编号")
    @ApiModelProperty(value = "请求编号id redisService.getUniqueNumber()获得唯一编号")
    private String reqId;

    /** 平台商id */
    @Excel(name = "平台商id")
    private Long sysCode;

    /** 来源方 */
    @Excel(name = "来源方 数据字典 log_source")
    @ApiModelProperty(value = "来源方 数据字典 log_source")
    private Integer source;

    /** 接收方 */
    @Excel(name = "接收方 数据字典 log_receiver")
    @ApiModelProperty(value = "接收方 数据字典 log_receiver")
    private Integer receive;

    /** 请求数据(推送数据) */
    @Excel(name = "请求数据(推送数据)", readConverterExp = "请求数据(推送数据)")
    @ApiModelProperty(value = "请求数据(推送数据)")
    private String reqData;

    /** 请求状态  0未组装数据  1组装数据推送中/接收中 2推送/接收完成 */
    @Excel(name = "请求状态  0未组装数据  1组装数据推送中/接收中 2推送/接收完成")
    @ApiModelProperty(value = "请求状态  0未组装数据  1组装数据推送中/接收中 2推送/接收完成")
    private Integer reqStatus;

    /** 业务数据（请求数据） */
    @Excel(name = "业务数据", readConverterExp = "请求数据")
    @ApiModelProperty(value = "业务数据")
    private String bizData;

    /** 请求时间 */
    @Excel(name = "请求时间/接收时间", width = 30, dateFormat = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "请求时间/接收时间")
    private Date reqTime;

    /** 状态 -1失败 0待处理 1成功 */
    @Excel(name = "状态 -1失败 0待处理 1成功")
    @ApiModelProperty(value = "状态 -1失败 0待处理 1成功")
    private Integer status;

    /** 处理信息 */
    @Excel(name = "处理信息")
    @ApiModelProperty(value = "处理信息")
    private String message;

    /** 请求类型（接口） */
    @Excel(name = "请求类型 数据字典 log_request_type", readConverterExp = "接口")
    @ApiModelProperty(value = "请求类型 数据字典 log_request_type")
    private Integer requestType;

    /** 入驻商ID */
    @Excel(name = "入驻商ID")
    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    /** 操作类型 */
    @Excel(name = "操作类型 LogOperationType枚举")
    @ApiModelProperty(value = "操作类型 LogOperationType枚举")
    private String operationType;

    /** 重试次数 */
    @Excel(name = "重试次数")
    @ApiModelProperty(value = "重试次数")
    private Long retryCount;


    /** 是否重试 0 否 1 是 */
    @Excel(name = "是否重试 0 否 1 是")
    @ApiModelProperty(value = "是否重试 0 否 1 是")
    private Long isRetry;

    /** 消息重发失败日志记录 */
    @Excel(name = "消息重发失败日志记录")
    @ApiModelProperty(value = "消息重发失败日志记录")
    private String resendMessage;

    /** 日志类型  1、同步数据 2、接收数据 */
    @Excel(name = "日志类型  1、同步数据 2、接收数据")
    private Integer logType;

    /** 返回结果状态码 */
    @Excel(name = "返回结果状态码")
    @ApiModelProperty(value = "返回结果状态码")
    private Integer resultCode;

    /** 开放能力ID */
    @Excel(name = "开放能力ID")
    @ApiModelProperty(value = "开放能力ID")
    private Long opensourceId;

    /** 平台商开放能力ID */
    @Excel(name = "平台商开放能力ID")
    @ApiModelProperty(value = "平台商开放能力ID")
    private Long partnerId;

    /**
     * 组装接收日志信息
     * @param data
     * @param source
     * @param requestType
     * @param supplierId
     * @param operationType
     * @return
     */
    public static SysInterfaceLogVO assembleReceiveLogData(String reqId,String data, Integer source, Integer requestType, Long supplierId, String operationType){
        SysInterfaceLogVO logVO = new SysInterfaceLogVO();
        logVO.setReqId(reqId);
        logVO.setSource(source);
        logVO.setReceive(SyncSourceType.B2B.getCode());
        logVO.setBizData(data);
        logVO.setReqTime(new Date());
        logVO.setStatus(LOG_STATUS_WAIT);
        logVO.setRequestType(requestType);
        logVO.setSupplierId(supplierId);
        logVO.setOperationType(operationType);
        logVO.setLogType(LOG_TYPE_RECEIVE);
        logVO.setReqStatus(REQ_STATUS_1);
        logVO.setReqData(data);

        return logVO;
    }

    public SysInterfaceLogVO(String reqId, String message, Integer resultCode) {
        this.reqId = reqId;
        this.message = message;
        this.resultCode = resultCode;
    }

    public SysInterfaceLogVO(String reqId, String message, Integer resultCode,Integer reqStatus) {
        this.reqId = reqId;
        this.message = message;
        this.resultCode = resultCode;
        this.reqStatus = reqStatus;
    }

    public static SysInterfaceLogVO assembleUpdateData(SysInterfaceLogVO vo,String message, Integer status,Integer reqStatus) {
        vo.setMessage(message);
        vo.setStatus(status);
        vo.setReqStatus(reqStatus);
        return vo;
    }


}
