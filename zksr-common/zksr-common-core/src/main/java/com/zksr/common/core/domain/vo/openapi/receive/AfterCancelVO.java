package com.zksr.common.core.domain.vo.openapi.receive;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
* 退货确认前取消请求体
* @date 2025/1/21 14:54
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("退货确认前取消请求体")
public class AfterCancelVO {

    /** 入驻商售后单编号 */
    @Excel(name = "入驻商售后单编号")
    @ApiModelProperty(value = "入驻商售后单编号")
    @NotNull(message = "B2B入驻商售后单编号不能为空")
    private String supplierAfterNo;

    /** 外部订单号 */
    @Excel(name = "外部订单号")
    @ApiModelProperty(value = "外部订单号")
    private String sourceOrderNo;

}
