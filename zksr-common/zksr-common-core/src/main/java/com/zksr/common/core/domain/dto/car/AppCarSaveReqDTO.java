package com.zksr.common.core.domain.dto.car;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>   
 * @version 1.0
 * @description: 购物车carid 定义
 * @date 2024/3/26 16:25
 */
@Data
@ApiModel(description ="购物车商品carId定义")
public class AppCarSaveReqDTO extends AppCarIdDTO {

    @ApiModelProperty("0-普通商品, 1-组合商品")
    private Integer itemType = NumberPool.INT_ZERO;

    @ApiModelProperty(value = "组合商品ID")
    private Long spuCombineId;

    @ApiModelProperty(value = "促销活动ID")
    private Long activityId;

    @ApiModelProperty(value = "指令ID")
    private Long commandId;

    /**
     * 解析字符串获取数据
     * @param value
     * @return
     */
    public static AppCarSaveReqDTO build(String value) {
        String[] dataSegment = value.split(StringPool.UNDERSCORE);
        JSONObject entries = new JSONObject();
        for (int index = 0; index < dataSegment.length; index++) {
            entries.put(fieldSort.get(index), dataSegment[index]);
        }
        return entries.toBean(AppCarSaveReqDTO.class);
    }
}
