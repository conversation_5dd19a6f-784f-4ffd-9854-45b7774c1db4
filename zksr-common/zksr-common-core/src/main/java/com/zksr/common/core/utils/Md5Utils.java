package com.zksr.common.core.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * ©Copyright ©1968-2020 Midea Group,IT
 * FileName: MD5Utils
 * Author: zhangjh39
 * Date: 2018-12-19 11:49
 * Description: Md5加解密
 */
public class Md5Utils {

    private final static Logger log = LoggerFactory.getLogger(Md5Utils.class);

    public static String md5(String data) {
        if (StringUtils.isBlank(data)) {
            return null;
        }
        try {
            // 得到一个信息摘要器
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] result = digest.digest(data.getBytes());
            StringBuilder builder = new StringBuilder();
            for (byte b : result) {
                // 与运算
                int number = b & 0xff;
                String str = Integer.toHexString(number);
                if (str.length() == 1) {
                    builder.append("0");
                }
                builder.append(str);
            }
            // 标准的md5加密后的结果
            return builder.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error(e.getMessage(), e);
            return "";
        }
    }
}
