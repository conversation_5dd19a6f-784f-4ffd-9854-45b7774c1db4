package com.zksr.common.core.domain.vo.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BranchOpenDTO {
    /** 操作类型 */
    private String operationTypeCode;

    /** 运营商id */
    private Long dcId;

    /** 门店id */
    private Long branchId;

    /** 门店编号 */
    private String branchNo;

    /** 平台商id */
    private Long sysCode;

    /** 门店名称 */
    private String branchName;

    /** 城市id */
    private Long areaId;

    /** 城市区域 */
    private String areaName;

    /** 业务员id */
    private Long colonelId;

    /** 业务员手机号 */
    private String colonelPhone;

    /** 业务员名称 */
    private String colonelName;

    /** 门店地址 */
    private String branchAddr;

    /** 经度 */
    private BigDecimal longitude;

    /** 纬度 */
    private BigDecimal latitude;

    /** 渠道id */
    private Long channelId;

    /** 渠道名称 */
    private String channelName;

    /** 平台商城市分组id */
    private Long groupId;

    /** 联系人 */
    private String contactName;

    /** 联系电话 */
    private String contactPhone;

    /** 备注 */
    private String memo;

    /** 状态 */
    private Integer status;

    /** 删除状态(0:正常，2：删除) */
    private Integer delFlag;

    /** 审核人 */
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核状态 */
    @ApiModelProperty(value = "1已审核 0未审核")
    private Integer auditState = 0;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate; 		 // 过期时间

    @ApiModelProperty(value = "业务员级别")
    private Long colonelLevel;

    @ApiModelProperty(value = "上级业务员ID")
    private Long pColonelId;

    @ApiModelProperty(value = "上级业务员级别")
    private Long pColonelLevel;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;

    @ApiModelProperty(value = "门店Id集合")
    private List<Long> branchIds;

    /** 是否支持货到付款(0,否 1,是) */
    private Integer hdfkSupport;

    /** 是否默认 */
    @Excel(name = "是否默认数据字典")
    private String isDefault = "N";

    /** 门头照 */
    @Excel(name = "门头照")
    private String branchImages;

    /** 最后一次登陆时间 */
    @Excel(name = "最后一次登陆时间")
    @ApiModelProperty(value = "最后一次登陆时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    @Excel(name = "货到付款最大可欠款金额")
    @ApiModelProperty("货到付款最大可欠款金额")
    private BigDecimal hdfkMaxAmt;

    /** 对接唯一编码 */
    @Excel(name = "对接唯一编码")
    private String sendCode;


    /** 省份 */
    private String provinceName;

    /** 城市 */
    private String cityName;

    /** 区县 */
    private String districtName;
}
