package com.zksr.common.core.enums.request;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Objects;

/**
* 赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）
* @date 2024/12/5 9:35
* <AUTHOR>
*/
@Getter
@AllArgsConstructor
public enum GiftPriceType {

    GIFT_PRICE_LJ(0,"零价"),
    GIFT_PRICE_JJ(1,"均价"),
    GIFT_PRICE_FTJ(2,"分摊价")
    ;

    private final Integer type;

    private final String name;

    /**
     * 根据类型获取对应的价格
     * @param type  取价类型
     * @param price 原最小单位单机
     * @param avgPirce 最小单位平均价
     * @param sharePirce   最小单位分摊单价
     * @return
     */
    public static BigDecimal getAfterGiftPrice(Integer type, BigDecimal price, BigDecimal avgPirce, BigDecimal sharePirce) {
        if (Objects.equals(type, GIFT_PRICE_LJ.getType())) {
            return price;
        } else if (Objects.equals(type, GIFT_PRICE_JJ.getType())) {
            return avgPirce;
        } else if (Objects.equals(type, GIFT_PRICE_FTJ.getType())) {
            return sharePirce;
        } else {
            return price;
        }
    }
}
