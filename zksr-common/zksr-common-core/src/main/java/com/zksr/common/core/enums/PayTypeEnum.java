package com.zksr.common.core.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/4/23 14:38
 */
@Getter
public enum PayTypeEnum {

    // 支付平台
    PAY("pay", "支付平台", 1),
    // 2025年3月21日, 储值体系已经要逐渐移除流程了,
    @Deprecated
    STORE("store", "储值", 0),
            ;
    /**
     * 编码
     */
    private final String type;
    /**
     * 名字
     */
    private final String name;

    /**
     * 支付体系 0-储值 (入驻商充值), 1-收款 (订单支付平台)
     */
    private final Integer usage;

    PayTypeEnum(String type, String name, Integer usage) {
        this.type = type;
        this.name = name;
        this.usage = usage;
    }

    public static PayTypeEnum parsePayType(String type) {
        for (PayTypeEnum typeEnum : values()) {
            if (type.equals(typeEnum.getType())) {
                return typeEnum;
            }
        }
        return null;
    }
}
