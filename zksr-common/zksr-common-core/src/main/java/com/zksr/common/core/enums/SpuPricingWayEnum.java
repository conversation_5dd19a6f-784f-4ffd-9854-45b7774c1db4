package com.zksr.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description spu商品计价方式枚举
 */
@Getter
@AllArgsConstructor
public enum SpuPricingWayEnum {
    ORDINARY(1, "普通商品"),
    WEIFH(2, "称重商品"),
    ;
    private Integer type;
    private String name;


    /**
     * 是否称重商品
     * @param type
     * @return
     */
    public static boolean isWeighGoods(Integer type) {
        return Objects.equals(type, WEIFH.getType());
    }
}
