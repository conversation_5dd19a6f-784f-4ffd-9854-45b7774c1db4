package com.zksr.common.core.enums;

import lombok.Getter;

@Getter
public enum WithdrawStateEnum {
    INIT(0, "初始化"),
    PROCESSING(1, "提现处理中"),
    REJECT(2, "审核拒绝"),
    FINISH(3, "提现成功"),
    FAIL(4, "提现失败"),
    TRANSFER_PROCESSING(10, "转账中"),
    TRANSFER_SUCCESS(11, "转账成功"),
    TRANSFER_FAIL(12, "转账失败"),
    SETTLE_PROCESSING(13, "结算中"),
    SETTLE_SUCCESS(14, "结算成功"),
    SETTLE_FAIL(15, "结算失败"),
    ;
    private Integer state;
    private String name;

    WithdrawStateEnum(Integer state, String name) {
        this.state = state;
        this.name = name;
    }
}
