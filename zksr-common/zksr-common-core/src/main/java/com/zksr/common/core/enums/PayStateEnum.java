package com.zksr.common.core.enums;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.utils.ToolUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum PayStateEnum {
    // 支付状态
    PAY_NOT_ONLINE(0, "未支付"),
    PAY_ALREADY_ONLINE(1, "已支付"),
    PAY_CANCEL(2, "订单取消"),
    PAY_NOT_HDFK(3, "货到付款未收款"),
    PAY_ALREADY_HDFK(4, "货到付款已收款"),
    PAY_BEING_CANCELLED(5, "订单取消中"),
    ;
    /**
     * 编码
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String name;

    /**
     * 根据支付方式 枚举 {@link com.zksr.common.core.enums.OrderPayWayEnum} 获取对应的未支付状态
     * @param payWay
     * @return
     */
    public static Integer getNotPayState() {
//        if (payWay.equals(OrderPayWayEnum.HDFK.getPayWay())) return PAY_NOT_HDFK.getCode();
        return PAY_NOT_ONLINE.getCode();
    }

    /**
     * 根据支付方式 枚举 {@link com.zksr.common.core.enums.OrderPayWayEnum} 获取对应的已支付状态
     * @param payWay
     * @return
     */
    public static Integer getAlreadyPayState(String payWay) {
        if (payWay.equals(OrderPayWayEnum.HDFK.getPayWay())) return PAY_NOT_HDFK.getCode();
        return PAY_ALREADY_ONLINE.getCode();
    }

    /**
     * 货到付款已付款状态
     * @return
     */
    public static Integer getHdfkAlreadyPayState() {
        return PAY_ALREADY_HDFK.getCode();
    }

    public static PayStateEnum getPayState(Integer code){
        for (PayStateEnum value : PayStateEnum.values()) {
            if (code == value.getCode()){
                return value;
            }
        }
        return PAY_NOT_ONLINE;
    }

    /**
     * 判断订单是否已支付
     * @param code
     * @return
     */
    public static boolean isOrderPaid(Integer code){
        if (ToolUtil.isEmpty(code)) {
            return false;
        }
        return EnumSet.of(PAY_ALREADY_ONLINE, PAY_NOT_HDFK, PAY_ALREADY_HDFK).contains(PayStateEnum.getPayState(code));
    }


    /**
     * 判断订单是否已取消
     * @param code
     * @return
     */
    public static boolean isOrderCancel(Integer code){
        if (ToolUtil.isEmpty(code)) {
            return false;
        }
        return Objects.equals(PAY_CANCEL.getCode(), code);
    }

}
