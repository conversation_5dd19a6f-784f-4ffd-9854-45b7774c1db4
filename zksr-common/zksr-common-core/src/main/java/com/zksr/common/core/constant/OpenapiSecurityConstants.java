package com.zksr.common.core.constant;

/**
 * 权限相关通用常量
 *
 * <AUTHOR>
 */
public class OpenapiSecurityConstants
{
    /**
     * 用户ID字段
     */
    public static final String DETAILS_OPENSOURCE_ID = "opensource_id";

    /**
     * 授权信息字段
     */
    public static final String AUTHORIZATION_HEADER = "OPENAPIAuthorization";

    /**
     * 请求来源
     */
    public static final String FROM_SOURCE = "from-source";

    /**
     * 导出请求
     */
    public static final String EXPORT = "export";

    /**
     * 内部请求
     */
    public static final String INNER = "inner";

    /**
     * 用户标识
     */
    public static final String OPENSOURCE_KEY = "opensource_key";

    /**
     * 平台商ID
     */
    public static final String SYS_CODE = "sys_code";


    /**
     * 登录用户
     */
    public static final String LOGIN_OPENSOURCE = "login_opensource";


}
