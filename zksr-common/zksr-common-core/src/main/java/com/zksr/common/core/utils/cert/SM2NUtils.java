package com.zksr.common.core.utils.cert;

import cn.hutool.crypto.asymmetric.SM2;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Base64;
import java.util.Objects;

/**
 * 国密SM2算法工具类
 */
public class SM2NUtils {

    private static Logger logger = LoggerFactory.getLogger(SM2Utils.class);

    private SM2NUtils(){}

    public static String sign(PrivateKey privateKey, String data) {
        return sign(privateKey, data.getBytes(StandardCharsets.UTF_8), null);
    }

    public static String sign(PrivateKey privateKey, String data, byte[] id) {
        return sign(privateKey, data.getBytes(StandardCharsets.UTF_8), id);
    }

    public static String sign(PrivateKey privateKey, byte[] data, byte[] id) {
        SM2 sm2 = new SM2(privateKey, null);
        return Base64.getEncoder().encodeToString(sm2.sign(data, id));
    }

    public static boolean verify(PublicKey publicKey, String plainText, String signText) {
        Objects.requireNonNull(plainText, "plainText required");
        Objects.requireNonNull(signText, "signText required");
        return verify(publicKey, plainText.getBytes(StandardCharsets.UTF_8), Base64.getDecoder().decode(signText), null);
    }

    private static boolean verify(PublicKey publicKey, byte[] plainData, byte[] signature, byte[] id) {
        SM2 sm2 = new SM2(null, publicKey);
        return sm2.verify(plainData, signature, id);
    }

    public static String encryptToBase64(PublicKey publicKey, String plainText){
        byte[] cipherData = encrypt(publicKey, plainText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(cipherData);
    }

    private static byte[] encrypt(PublicKey publicKey, byte[] message) {
        SM2 sm2 = new SM2(null, publicKey);
        sm2.setMode(SM2Engine.Mode.C1C3C2);
        return sm2.encrypt(message);
    }

    public static String decryptBase64Message(PrivateKey privateKey,String cipherText){
        byte[] base64Data = Base64.getDecoder().decode(cipherText);
        byte[] bytes = decrypt(privateKey, base64Data);
        return StringUtils.toEncodedString(bytes, StandardCharsets.UTF_8);
    }

    private static byte[] decrypt(PrivateKey privateKey, byte[] message) {
        SM2 sm2 = new SM2(privateKey, null);
        sm2.setMode(SM2Engine.Mode.C1C3C2);
        return sm2.decrypt(message);
    }

    public static void main(String[] args)  {
        String plainText = "待签名串abc=123";
    }
}