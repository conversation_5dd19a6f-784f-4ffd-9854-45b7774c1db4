package com.zksr.common.core.utils;

/**
 * @Author: chenyj8
 * @Desciption: 接口调用统一日志格式
 * grok表达式：^\[%{DATA:log_date}\]\[%{DATA:log_level}\]\[%{DATA:thread_id}\]\[TID:%{DATA:tid}\]\[%{DATA:class_name}\]LINECONTENT:bizId:\[%{DATA:bizId}\],params:\[%{DATA:params}\],ErrorCode:\[%{DATA:ErrorCode}\],ErrorMsgTemp:\[%{DATA:ErrorMsgTemp}\],ErrorMsg:\[%{DATA:ErrorMsg}\],result:\[%{DATA:result}\],cost:\[%{NUMBER:cost}\]
 */

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class BizLogUtil {

    private static final Logger log = LoggerFactory.getLogger(BizLogUtil.class);
    public static final String UNDEFINED_ERR_CODE = "999999";

    /**
     * 打印业务日志——不含出入参信息
     *
     * @param bizId          业务日志编码
     * @param errorCode      异常编码
     * @param errorMsgTemp   异常信息简写
     * @param errorMsg       详细异常信息
     * @param start          接口调用开始时间
     */
    public static void printBizLogWithoutParams(String bizId, String errorCode,String errorMsgTemp, String errorMsg, Date start) {

        Date end = new Date();
        long cost = start == null ? 0 : end.getTime() - start.getTime();
        if(log.isInfoEnabled()) {
            log.info("bizId:[{}],params:[{}],ErrorCode:[{}],ErrorMsgTemp:[{}],ErrorMsg:[{}],result:[{}],cost:[{}]",
                    bizId,
                    "",
                    errorCode == null ? "" : errorCode,
                    errorMsgTemp == null ? "" : errorMsgTemp,
                    errorMsg == null ? "" : errorMsg,
                    "",
                    cost);
        }
    }

    /**
     * 打印业务日志——包含出入参信息
     *
     * @param bizId          业务日志编码
     * @param errorCode      异常编码
     * @param errorMsgTemp   异常简写描述
     * @param errorMsg       具体异常信息
     * @param start          接口调用开始时间
     */
    public static void printBizLogWithParams(String bizId, Object params, String errorCode,String errorMsgTemp,
                                             String errorMsg, Object result, Date start) {
        Date end = new Date();
        long cost = start == null ? 0 : end.getTime() - start.getTime();
        String paramsLog = null;
        String resultLog = null;
        try {
            paramsLog = params == null ? "" : ((params instanceof String) ? (String) params : JsonUtils.toJsonString(params));
            resultLog =  result == null ? "" : ((result instanceof String) ? (String) result :JsonUtils.toJsonString(result));
        }catch (Exception e){
            log.error("Grafana接口出入参格式化异常，出入参日志中暂不输出",e);
        }
        log.info("bizId:[{}],params:[{}],ErrorCode:[{}],ErrorMsgTemp:[{}],ErrorMsg:[{}],result:[{}],cost:[{}]",
                bizId,
                paramsLog,
                errorCode == null ? "" : errorCode,
                errorMsgTemp == null ? "" : errorMsgTemp,
                errorMsg == null ? "" : errorMsg,
                resultLog,
                cost);

    }

}
