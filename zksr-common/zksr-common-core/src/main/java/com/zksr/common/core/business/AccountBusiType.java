package com.zksr.common.core.business;

import lombok.Getter;

/**
 * 账户变动业务类型
 */
@Getter
public enum AccountBusiType {
    SUPPLIER_RECHARGE("supplier_recharge", "入驻商充值"),
    BRANCH_RECHARGE("branch_recharge", "门店充值"),
    BRANCH_RECHARGE_GIVE("branch_recharge_give", "门店充值赠送"),
    SUPPLIER_CREATE_ORDER("supplier_create_order", "商城创建订单冻结入驻商"),
    SUPPLIER_CREATE_ORDER_CANCEL("supplier_create_order_cancel", "商城创建订单解冻入驻商"),
    BRANCH_BALANCE_PAY("branch_balance_pay", "门店储值支付"),
    BRANCH_BALANCE_REFUND("branch_balance_refund", "门店存储支付退款"),
    ACCOUNT_TRANSFER("account_transfer", "账户转账"),
    UPDATE_CREDIT("update_credit", "修改授信金额"),
    WITHDRAW_FROZEN("withdraw_frozen", "提现冻结"),
    WITHDRAW_FAIL_FROZEN("withdraw_fail_frozen", "提现失败解除冻结"),
    WITHDRAW_SUCCESS("withdraw_success", "提现成功"),
    RECHARGE_FEE("recharge_fee", "充值手续费补贴"),
    BRANCH_HDFK_ORDER("branch_hdfk_order", "门店下单货到付款下单"),
    BRANCH_HDFK("branch_hdfk", "门店下单货到付款"),
    WALLET_DIVIDE("wallet_divide", "钱包支付订单分账")
    ;
    private String type;
    private String name;

    AccountBusiType(String type, String name) {
        this.type = type;
        this.name = name;
    }
}
