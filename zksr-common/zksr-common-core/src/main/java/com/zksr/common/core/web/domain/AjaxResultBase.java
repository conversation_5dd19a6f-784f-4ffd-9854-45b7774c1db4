package com.zksr.common.core.web.domain;

import com.zksr.common.core.constant.HttpStatus;
import com.zksr.common.core.utils.StringUtils;

import java.io.Serializable;

/**
 * 操作消息提醒
 *
 * <AUTHOR>
 */
public class AjaxResultBase<T> implements Serializable
{
    private static final long serialVersionUID = 1L;

    public static final String DATA_TAG = "data";

    /**
     * 是否成功 true or false
     */
    private boolean success;

    /**
     * 状态码
     */
    private int code;

    /**
     * 返回内容
     */
    private String msg;

    /**
     * 数据对象
     */
    private T data;

    /**
     * 状态类型
     */
    public enum Type
    {
        /** 成功 */
        SUCCESS(HttpStatus.SUCCESS),
        /** 警告 */
//        WARN(301),
        UNAUTHORIZED(HttpStatus.UNAUTHORIZED),
        FORBIDDEN(HttpStatus.FORBIDDEN),
        /** 错误 */
        ERROR(HttpStatus.ERROR);

        private final int value;

        Type(int value)
        {
            this.value = value;
        }

        public int value()
        {
            return this.value;
        }
    }

    /**
     * 初始化一个新创建的 AjaxResultBase 对象，使其表示一个空消息。
     */
    public AjaxResultBase()
    {
    }

    /**
     * 初始化一个新创建的 AjaxResultBase 对象
     *
     * @param type 状态类型
     * @param msg 返回内容
     * @param data 数据对象
     */
    public AjaxResultBase(Type type, String msg, T data) {
        this.code = type.value();
        this.msg = msg;
        if (StringUtils.isNotNull(data)) {
            this.data = data;
        }

        if (type.value == Type.SUCCESS.value) {
            this.success = Boolean.TRUE;
        } else {
            this.success = Boolean.FALSE;
        }
    }

    /**
     * 返回成功消息
     *
     * @return 成功消息
     */
    public static AjaxResultBase success()
    {
        return AjaxResultBase.success("操作成功");
    }

    /**
     * 返回成功数据
     *
     * @return 成功消息
     */
    public static <U> AjaxResultBase<U> success(U data)
    {
        return AjaxResultBase.success("操作成功", data);
    }

    /**
     * 返回成功消息
     *
     * @param msg 返回内容
     * @return 成功消息
     */
    public static AjaxResultBase success(String msg)
    {
        return AjaxResultBase.success(msg, null);
    }

    /**
     * 返回成功消息
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @return 成功消息
     */
    public static <U> AjaxResultBase<U> success(String msg, U data)
    {
        return new AjaxResultBase(Type.SUCCESS, msg, data);
    }

    public static <U> AjaxResultBase<U> unauthorized(String msg)
    {
        return new AjaxResultBase(Type.UNAUTHORIZED, msg, null);
    }

    public static <U> AjaxResultBase<U> forbidden(String msg)
    {
        return new AjaxResultBase(Type.FORBIDDEN, msg, null);
    }

//    /**
//     * 返回警告消息
//     *
//     * @param msg 返回内容
//     * @return 警告消息
//     */
//    public static AjaxResultBase warn(String msg)
//    {
//        return AjaxResultBase.warn(msg, null);
//    }

//    /**
//     * 返回警告消息
//     *
//     * @param msg 返回内容
//     * @param data 数据对象
//     * @return 警告消息
//     */
//    public static <U> AjaxResultBase<U> warn(String msg, U data)
//    {
//        return new AjaxResultBase(Type.WARN, msg, data);
//    }

    /**
     * 返回错误消息
     *
     * @return
     */
    public static AjaxResultBase error()
    {
        return AjaxResultBase.error("操作失败");
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @return 警告消息
     */
    public static AjaxResultBase error(String msg)
    {
        return AjaxResultBase.error(msg, null);
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @return 警告消息
     */
    public static <U> AjaxResultBase<U> error(String msg, U data)
    {
        return new AjaxResultBase(Type.ERROR, msg, data);
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @return 警告消息
     */
    public static <U> AjaxResultBase<U> error(int code, String msg, U data) {
        return new AjaxResultBase(Type.ERROR, msg, data);
    }

    /**
     * 方便链式调用
     *
     * @param key   键
     * @param value 值
     * @return 数据对象
     */
    @Deprecated
    public AjaxResultBase put(String key, Object value) {
        //super.put(key, value);
        return this;
    }

    /**
     * 是否为成功消息
     *
     * @return 结果
     */
    public boolean isSuccess() {
        return success;
    }

    public String getMsg() {
        return msg;
    }

    public Integer getCode() {
        return code;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
