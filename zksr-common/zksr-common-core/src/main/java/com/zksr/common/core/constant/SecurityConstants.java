package com.zksr.common.core.constant;

/**
 * 权限相关通用常量
 *
 * <AUTHOR>
 */
public class SecurityConstants
{
    /**
     * 用户ID字段
     */
    public static final String DETAILS_USER_ID = "user_id";

    /**
     * 用户名字段
     */
    public static final String DETAILS_USERNAME = "username";

    /**
     * 授权信息字段
     */
    public static final String AUTHORIZATION_HEADER = "authorization";

    /**
     * 请求来源
     */
    public static final String FROM_SOURCE = "from-source";

    /**
     * 导出请求
     */
    public static final String EXPORT = "export";

    /**
     * 内部请求
     */
    public static final String INNER = "inner";

    /**
     * 用户标识
     */
    public static final String USER_KEY = "user_key";

    /**
     * 平台商ID
     */
    public static final String SYS_CODE = "sys_code";

    /**
     * 平台商ID集合, 指定多平台商角色
     */
    public static final String SYS_CODES = "sys_codes";

    /**
     * 运营商ID
     */
    public static final String DC_ID = "dc_id";

    /**
     * 登录用户
     */
    public static final String LOGIN_USER = "login_user";

    /**
     * 角色权限
     */
    public static final String ROLE_PERMISSION = "role_permission";

    /**
     * 导出线程分页传递
     */
    public static final String EXPORT_PAGE_NUM = "export-page-num";

    /**
     * 导出线程分页传递
     */
    public static final String EXPORT_PAGE_SIZE = "export-page-size";

    /** saas系统用户编码 */
    public static final String SAAS_USER_CODE = "saasUserCode";

    /** saas系统租户编码 */
    public static final String SAAS_TENANT_CODE = "saasTenantCode";

    public static final String SAAS_HEADER_TENANT_CODE = "X-Tenant-Code";

    /**
     * saasToken自定义标识
     */
    public static final String SAAS_ACCESS_TOKEN = "X-Access-Token";

    /** 用户唯一标识 */
    public static final String TOKEN = "token";

    /** 用户信息 */
    public static final String USER = "user";

    /** 角色权限列表 */
    public static final String ROLES = "roles";

    /** 权限列表 */
    public static final String PERMISSIONS = "permissions";

    /** 平台log */
    public static final String PARTNER_LOG = "partnerLog";

    /** 平台名称 */
    public static final String PARTNER_NAME = "partnerName";

}
