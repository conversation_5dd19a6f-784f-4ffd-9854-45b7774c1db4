package com.zksr.common.core.utils;

import com.zksr.common.core.enums.MerchantTypeEnum;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 提现工具类
 * @date 2025/2/28 11:05
 */
public class WithdrawUtil {

    /**
     * 获取商户提现单号前缀
     * @param merchantType  商户类型
     * @param initNo        提现单号
     * @return
     */
    public static String getWithdrawNo(String merchantType, String initNo) {
        return MerchantTypeEnum.fromValue(merchantType).getWithdrawPrefix() + initNo;
    }

    /**
     * 获取商户提现单号前缀
     * @param merchantType  商户类型
     * @param initNo        提现单号
     * @return
     */
    public static String getWithdrawNo(MerchantTypeEnum merchantType, String initNo) {
        return merchantType.getWithdrawPrefix() + initNo;
    }
}
