package com.zksr.common.core.domain.vo.openapi.receive;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/5/30 15:21
 * @注释
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("接收配送订单物流状态回传实体")
public class PhysicalDistributionStateReturnVO {

    /** 订单号 */
    @Excel(name = "订单号")
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    /** 订单状态 */
    @Excel(name = "订单状态")
    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    /** 状态时间 */
    @Excel(name = "状态时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "状态时间")
    private String statusTime;

    /** 具体描述 */
    @Excel(name = "具体描述")
    @ApiModelProperty(value = "具体描述")
    private String memo;
}
