package com.zksr.common.core.domain.erp.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class AfterSheetOpenDto {

    @ApiModelProperty(value = "单据号")
    private String sheetNo;

    @ApiModelProperty(value = "退货单对应ERP销售出库订单号")
    private String otherSheetNo;

    @ApiModelProperty(value = "仓库编号")
    private String branchNo;

    @ApiModelProperty(value = "门店编号")
    private String consumerNo;


    @ApiModelProperty(value = "单据类型")
    private String transNo;

    @ApiModelProperty(value = "单据日期")
    private String sheetDate;


    @ApiModelProperty(value = "退款方式")
    private String refundStyle;

    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "发货人名称")
    private String senderName;

    @ApiModelProperty(value = "发货人固定电话")
    private String senderTel;

    @ApiModelProperty(value = "发货人手机号码")
    private String senderMobile;

    @ApiModelProperty(value = "发货人详细地址")
    private String senderDetailAddr;




    @ApiModelProperty(value = "明细")
    private List<AfterDetailOpenDto>  subList;




}
