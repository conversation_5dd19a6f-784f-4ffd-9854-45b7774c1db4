package com.zksr.common.core.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品排序方式
 * @date 2024/3/2 16:09
 */
@Getter
public enum ProductSortType {
    NONE("none", "无"),
    SALE("sale", "销量"),
    PRICE("price", "价格"),
    SORT_NUM("sortNum", "上架顺序"),
    SALE_PRICE("salePrice", "销售价");
    private String sortType;
    private String sortName;

    ProductSortType(String sortType, String sortName) {
        this.sortType = sortType;
        this.sortName = sortName;
    }
}
