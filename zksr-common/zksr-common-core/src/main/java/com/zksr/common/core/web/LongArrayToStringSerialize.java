package com.zksr.common.core.web;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class LongArrayToStringSerialize extends JsonSerializer<List<Long>> {

    @Override
    public void serialize(List<Long> value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        List<String> strList = value.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
        gen.writeObject(strList);
    }

}
