package com.zksr.common.core.domain.vo.openapi.receive;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
*  订单接收通知实体
* @date 2024/9/18 19:02
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("订单接收通知商品库存实体")
public class OrderReceiveCallbackStockVO {

    /** ERP商品编号 */
    @Excel(name = "ERP商品编号")
    @ApiModelProperty(value = "ERP商品编号")
    private String erpItemNo;

    /** 可用库存数量（最小单位） */
    @Excel(name = "可用库存数量（最小单位）")
    @ApiModelProperty(value = "可用库存数量（最小单位）")
    private BigDecimal stock;

    @Excel(name = "最后库存更新时间")
    @ApiModelProperty(value = "最后库存更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateTime;

}
