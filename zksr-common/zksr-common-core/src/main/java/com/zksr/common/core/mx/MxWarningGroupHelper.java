package com.zksr.common.core.mx;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.zksr.common.core.config.MxAlertConfig;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.enums.WarimgPushEnvEnum;
import com.zksr.common.core.erpUtils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.MimeTypeUtils;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;

@Slf4j
@Component
public class MxWarningGroupHelper {
//    private static final String WARNING_URL = "https://mapnew5.midea.com/mrm/v5/open/robot/send?key=54fdeb68bf47481ab83bccd31d88d35a";
//    private static final String WARNING_PATH = "/xops/open-api/send/address/send_meixin_group";
//    @Value("${app.envName:}")
//    private String envName;
//    @Value("${app.ak:}")
//    private String AK;
//    @Value("${app.sk:}")
//    private String SK;
//    @Value("${app.mxNoticeUrl:}")
//    private String mxNoticeUrl;
    @Resource
    private MxAlertConfig mxAlertConfig;

    public static ExecutorService executor =
            TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(5,
                    50,
                    60,
                    TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(500))
            );

    /**
     * 推送美信群告警
     * @param message               告警内容
     * @param warimgPushEnvEnum     推送环境
     * @param bizKey                业务键
     */
    public void sendMxMessage(String message, WarimgPushEnvEnum warimgPushEnvEnum, String bizKey) {
        CompletableFuture.runAsync(() -> {
            try {
                if (WarimgPushEnvEnum.ONLY_PRD.getType().equals(warimgPushEnvEnum.getType())) {
                    if (StringUtils.isEmpty(mxAlertConfig.getEnvname()) || !mxAlertConfig.getEnvname().equals("prod")) {
                        return;
                    }
                }
                Long ts = System.currentTimeMillis();
                String signature = getHmacSign(String.valueOf(ts), mxAlertConfig.getSk());
//                Long strategyId = SecurityContextHolder.getStrategyId();
//                String strategyIdStr = strategyId == null ? "" : strategyId.toString();
                //发送美信
                String mxMessage = String.format("{\n" + " \"环境\":\"%s\" ， \"bizKey\":\"%s\" ， \"告警内容\":\"%s\"\n" + "}", mxAlertConfig.getEnvname(), bizKey, message);
                Map<String, String> header = new HashMap<>(2);
                header.put("access", mxAlertConfig.getAk());
                header.put("ts", ts.toString());
                header.put("signature", signature);
                JSONObject requestBody = new JSONObject();
                requestBody.put("contents", mxMessage);
                JSONArray urlsArray = new JSONArray();
                urlsArray.add(mxAlertConfig.getWarningurl());
                requestBody.put("urls", urlsArray);
                log.warn("推送预警消息 req：{}", message);
                String warningUrl = mxAlertConfig.getNoticeurl() + mxAlertConfig.getWarningpath();
                String sendPost = HttpUtils.simplePost(warningUrl, header, requestBody.toString(), MimeTypeUtils.APPLICATION_JSON_VALUE);
                log.warn("推送预警消息 resp：{}", sendPost);
            } catch (Exception e) {
                log.error(" {}推送预警消息异常,", bizKey, e);
            }
        }, executor);
    }

    public static String getHmacSign(String ts, String sk) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(sk.getBytes(), "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(keySpec);
            byte[] bytes = mac.doFinal(ts.getBytes(Charset.forName("UTF-8")));
            return Hex.encodeHexString(bytes);
        } catch (NoSuchAlgorithmException var6) {
            throw new RuntimeException("NoSuchAlgorithmException", var6);
        } catch (InvalidKeyException var7) {
            throw new RuntimeException("InvalidKeyException", var7);
        } catch (IllegalStateException var8) {
            throw new RuntimeException("IllegalStateException", var8);
        }
    }
}