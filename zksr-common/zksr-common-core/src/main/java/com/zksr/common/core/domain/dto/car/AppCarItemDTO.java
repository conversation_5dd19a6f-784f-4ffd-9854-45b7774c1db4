package com.zksr.common.core.domain.dto.car;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车商品通用缓存
 * @date 2024/5/9 16:16
 */
@Data
@ApiModel(description = "购物车商品")
public class AppCarItemDTO extends AppCarIdDTO{

    @ApiModelProperty(value = "商品数量", notes = "P1")
    private Integer productNum = 0;

    @ApiModelProperty(value = "是否选中 1-是 0-否")
    private Integer selected = 0;

    @ApiModelProperty(value = "是否业务员推荐, 0 否, 1 是", notes = "P2")
    private Integer recommendFlag = 0;

    @ApiModelProperty(value = "指令ID", notes = "P13")
    private Long commandId;

    @ApiModelProperty(value = "加单时间,毫秒值", notes = "P14")
    private Long doubleOrder;

}
