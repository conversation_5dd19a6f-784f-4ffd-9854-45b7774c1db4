package com.zksr.common.core.enums;

/**
 * @Author: chenyj8
 * @Desciption: 美的付代付交易类型
 */
public enum MideaPayTradeTypeEnum {
    BALANCE("BALANCE", "代付到余额","交易类型"),
    CARD("CARD", "代付到银行账户","交易类型"),
    BANK_CARD("BANK_CARD", "银行卡","账户类型"),
    PASS_BOOK("PASS_BOOK", "存折","账户类型"),
    PUBLIC_ACCT("PUBLIC_ACCT", "对公账户","账户类型"),
    ;


    private String code;

    private String value;

    private String desc;

    MideaPayTradeTypeEnum(String code, String value, String desc) {
        this.code = code;
        this.value = value;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
