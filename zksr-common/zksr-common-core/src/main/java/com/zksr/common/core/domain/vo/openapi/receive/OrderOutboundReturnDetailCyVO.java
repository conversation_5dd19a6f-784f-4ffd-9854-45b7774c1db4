package com.zksr.common.core.domain.vo.openapi.receive;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/5/30 15:12
 * @注释
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("接收订单出库回传详情差异数量实体")
public class OrderOutboundReturnDetailCyVO {

    @ApiModelProperty(value = "入驻商订单明细ID" , required = true)
    private Long supplierOrderDtlId;

    @ApiModelProperty(value = "商品可退数量" , required = true)
    private BigDecimal totalNum;

    @ApiModelProperty(value = "退货商品单价" , required = true)
    private BigDecimal refundPrice;

    @ApiModelProperty(value = "退货商品数量", required = true)
    private BigDecimal refundQty;

    @ApiModelProperty(value = "退货商品金额", required = true)
    private BigDecimal refundAmt;

    @ApiModelProperty(value = "退货单位大小 1：小单位，2：中单位，3：大单位", required = true)
    private Integer unitType;
}
