package com.zksr.common.core.enums.request;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* Email模板类型
* @date 2025/2/13 16:40
* <AUTHOR>
*/
@Getter
@AllArgsConstructor
public enum EmailTemplateType {
    DAY_SYNC_REPORT_DATA_EMAIL(900, "日同步报告"),
    SYNC_B2B_CONSUMER_EMAIL(501, "推送B2B平台门店数据"),
    SYNC_B2B_ORDER_EMAIL(502, "推送B2B销售订单数据"),
    SYNC_B2B_AFTER_SHEET_EMAIL(503, "推送B2B售后订单数据"),
    SYNC_B2B_PAY_EMAIL(504, "推送B2B收款单数据"),
    ;
    private final Integer type;
    private final String name;
}
