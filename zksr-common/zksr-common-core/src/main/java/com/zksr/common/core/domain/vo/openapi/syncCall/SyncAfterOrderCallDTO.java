package com.zksr.common.core.domain.vo.openapi.syncCall;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 入驻商售后单对象
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SyncAfterOrderCallDTO", description = "售后订单返回数据传输对象")
public class SyncAfterOrderCallDTO implements Serializable {
    private static final long serialVersionUID=1L;

    /** 创建时间 */
    @Excel(name = "创建时间")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "入驻商订单ID", required = true)
    private Long supplierAfterId;

    @ApiModelProperty(value = "平台商ID")
    @Excel(name = "平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "入驻商售后单编号")
    @Excel(name = "入驻商售后单编号")
    private String supplierAfterNo;

    @ApiModelProperty(value = "入驻商订单编号")
    @Excel(name = "入驻商订单编号")
    private String supplierOrderNo;

    /**
     * 业务员ID
     */
    @ApiModelProperty(value = "业务员ID")
    @Excel(name = "业务员ID")
    private Long colonelId;

    /**
     * 业务员名称
     */
    @ApiModelProperty(value = "业务员名称")
    @Excel(name = "业务员名称")
    private String colonelName;

    /**
     * 业务员手机号
     */
    @ApiModelProperty(value = "业务员手机号")
    @Excel(name = "业务员手机号")
    private String colonelPhone;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    @ApiModelProperty(value = "订单ID")
    @Excel(name = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "售后单ID")
    @Excel(name = "售后单id")
    private Long afterId;

    @ApiModelProperty(value = "售后单编号")
    @Excel(name = "售后单编号")
    private String afterNo;

    @ApiModelProperty(value = "配送费")
    @Excel(name = "配送费")
    private BigDecimal transAmt;

    @ApiModelProperty(value = "入驻商ID")
    @Excel(name = "入驻商id")
    private Long supplierId;

    @ApiModelProperty(value = "优惠金额")
    @Excel(name = "优惠金额")
    private BigDecimal subDiscountAmt;

    @ApiModelProperty(value = "退款金额; 实际退款金额")
    @Excel(name = "退款金额; 实际退款金额")
    private BigDecimal subRefundAmt;

    @ApiModelProperty(value = "支付公司收取的支付费率")
    @Excel(name = "支付公司收取的支付费率; 从订单")
    private BigDecimal payRate;

    @ApiModelProperty(value = "支付平台手续费")
    @Excel(name = "支付平台手续费; (sub_refund_amt*pay_rate) 四舍五入")
    private BigDecimal subRefundFee;

    @ApiModelProperty(value = "售后收货手机号")
    @Excel(name = "售后收货手机号")
    private String returnPhone;

    @ApiModelProperty(value = "售后收货地址")
    @Excel(name = "售后收货地址")
    private String returnAddr;

    @ApiModelProperty(value = "外部订单号")
    @Excel(name = "外部订单号")
    private String sourceOrderNo;

    @ApiModelProperty(value = "订单类型")
    @Excel(name = "订单类型 SheetTypeConstants")
    private String transNo;

    @ApiModelProperty(value = "退货订单详情")
    List<SyncAfterOrderDetailCallDTO> detailList;
}
