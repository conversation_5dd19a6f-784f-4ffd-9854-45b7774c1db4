package com.zksr.common.core.domain.vo.openapi;

import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.utils.uuid.IdUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
* 第三方系统同步数据 MQ入参
* @date 2024/7/12 11:15
* <AUTHOR>
*/
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SyncDataDTO {

    /** 同步数据实体的唯一编号（门店ID、入驻商销售订单编号、入驻商售后订单编号） */
    private String dataId;

    /** 入驻商ID */
    private Long supplierId;

    /** 请求ID */
    private String reqId;

    /** 操作类型 */
    private String operationTypeCode = OperationType.ADD.getCode();

    /** 接口模板类型 */
    private Integer templateType;

    /** 平台商id */
    private Long sysCode;

    /** 延时推送时间等级 默认推送等级为1*/
    private Integer propertyDelayTimeLevel = 1;

    /** 订单类型 收款单使用  单据类型  XSS：销售订单  SHS：售后单*/
    private String sheetType;

    /** 是否收款 收款单使用  0否1是*/
    private Integer isProceeds = 1;

    /** 收款单的收款类型 */
    private String receilptType;

    /** 对接业务相关集合JSON值 */
    private String json;


    /**
     * 默认新增类型的有参构造组装数据
     * @param dataId
     * @param supplierId
     * @param templateType
     * @param sysCode
     */
    public SyncDataDTO(String dataId, Long supplierId, Integer templateType, Long sysCode) {
        this.dataId = dataId;
        this.supplierId = supplierId;
        this.templateType = templateType;
        this.sysCode = sysCode;
        this.reqId = IdUtils.fastSimpleUUID();
        this.operationTypeCode = OperationType.ADD.getCode();
    }
}
