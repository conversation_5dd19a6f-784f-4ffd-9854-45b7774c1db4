package com.zksr.common.core.domain.vo.car;

import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车分页请求数据
 * @date 2024/5/9 16:25
 */
@Data
@ApiModel(description = "购物车分页请求")
public class AppCarPageReqVO extends PageParam {

    @ApiModelProperty(value = "门店ID", required = true)
    private Long branchId;

    @ApiModelProperty(value = "商品类型 local-本地商品, global-全国商品", required = true, example = "local", notes = "区分全国和本地商品")
    private String productType = ProductType.LOCAL.getType();
}
