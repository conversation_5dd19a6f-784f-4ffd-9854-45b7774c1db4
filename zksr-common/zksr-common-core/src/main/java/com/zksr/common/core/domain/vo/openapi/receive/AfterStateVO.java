package com.zksr.common.core.domain.vo.openapi.receive;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 售后状态（ERP->B2B）对象 trd_express_status
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@Data
@ApiModel("售后状态（ERP->B2B） - trd_express_status分页 Request VO")
@NoArgsConstructor
public class AfterStateVO {
    private static final long serialVersionUID = 1L;

    /** 售后订单号(B2B入驻商售后订单编号) */
    @Excel(name = "售后订单号(B2B入驻商售后订单编号)")
    @ApiModelProperty(value = "售后订单号(B2B入驻商售后订单编号)")
    @NotNull(message = "B2B入驻商售后订单编号不能为空")
    private String supplierOrderNo;

    /** 售后物流状态（1、门店发起售后  2、商家已同意 11、待确认收货 12、已确认收货 13、已入库 21、商家同意退款 22、退款成功）  枚举：AfterLogisticsStatusEnum */
    @Excel(name = "售后物流状态", readConverterExp = "售后物流状态（1、门店发起售后  2、商家已同意 11、待确认收货 12、已确认收货 13、已入库 21、商家同意退款 22、退款成功） ")
    @ApiModelProperty(value = "售后物流状态（1、门店发起售后  2、商家已同意 11、待确认收货 12、已确认收货 13、已入库 21、商家同意退款 22、退款成功）")
    private Integer logisticsStatus;

    /** 售后物流状态 */
    @Excel(name = "售后物流状态信息")
    @ApiModelProperty(value = "售后物流状态信息")
    private String logisticsStatusInfo;

    /** 外部订单号 */
    @Excel(name = "外部(ERP)单号(非必填)")
    @ApiModelProperty(value = "外部(ERP)单号(非必填)")
    private String sourceOrderNo;

    /** 开始时间 */
    @Excel(name = "开始时间")
    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date startTime;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注（司机+ 电话）")
    private String memo;

}
