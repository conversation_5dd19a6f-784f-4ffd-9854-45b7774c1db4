package com.zksr.common.core.annotation;

import com.zksr.common.core.pool.StringPool;
import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 导出任务注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@Service
public @interface ExportHandler {
    @AliasFor(
            annotation = Service.class
    )
    String value() default "";

    /**
     * 是否需要分页支持, 如果不需要, 需要导出任务自定义分页实现, 默认开启分页
     */
    boolean defaultPage() default true;

    /**
     * 权限验证, 默认不验证, 使用权限字符例如:  system:user:export
     * @return
     */
    String permissions() default StringPool.EMPTY;
}
