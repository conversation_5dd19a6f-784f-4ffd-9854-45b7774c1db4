package com.zksr.common.core.utils;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zksr.common.core.utils.sql.SqlUtil;
import com.zksr.common.core.web.page.PageDomain;
import com.zksr.common.core.web.page.TableSupport;
import com.zksr.common.core.web.pojo.PageParam;

/**
 * 分页工具类
 * 
 * <AUTHOR>
 */
public class PageUtils extends PageHelper
{
    /**
     * 设置请求分页数据
     */
    public static void startPage()
    {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNo();
        Integer pageSize = pageDomain.getPageSize();
        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Boolean reasonable = pageDomain.getReasonable();
        PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
    }

    /**
     * 设置请求分页数据
     */
    public static <T> Page<T> startPage(PageParam pageParam)
    {
        Integer pageNum = pageParam.getPageNo();
        Integer pageSize = pageParam.getPageSize();
        return PageHelper.startPage(pageNum, pageSize);
    }

    /**
     * 设置请求分页数据
     */
    public static <T> Page<T> startPage(PageParam pageParam, boolean count)
    {
        Integer pageNum = pageParam.getPageNo();
        Integer pageSize = pageParam.getPageSize();
        return PageHelper.startPage(pageNum, pageSize, count);
    }

    /**
     * 清理分页的线程变量
     */
    public static void clearPage()
    {
        PageHelper.clearPage();
    }
}
