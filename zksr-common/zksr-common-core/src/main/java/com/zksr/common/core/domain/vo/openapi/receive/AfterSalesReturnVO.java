package com.zksr.common.core.domain.vo.openapi.receive;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/30 15:46
 * @注释
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("接收销售退货回传实体")
public class AfterSalesReturnVO {

    /** 入驻商售后单编号 */
    @Excel(name = "入驻商售后单编号")
    @ApiModelProperty(value = "入驻商售后单编号")
    private String supplierAfterNo;
    
    /** 入驻商售后单编号 */
    @Excel(name = "入驻商订单编号")
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    /** 外部订单号 */
    @Excel(name = "外部订单号")
    @ApiModelProperty(value = "外部订单号")
    private String sourceOrderNo;

    /** 退货类型 拒收退货(SHJ)、销售退后(SHS) 取枚举：SheetTypeConstants */
    @Excel(name = "退货类型 拒收退货(SHJ)、销售退后(SHS)")
    @ApiModelProperty(value = "退货类型 拒收退货(SHJ)、销售退后(SHS)")
    private String returnType;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 是否校验订单详情商品条数 开启时 推送10条数据 必须返回对应的十条数据 */
    @Excel(name = "是否校验订单详情商品条数")
    @ApiModelProperty(value = "是否校验订单详情商品条数",hidden = true)
    private boolean checkDetailNumFlag = Boolean.TRUE;

    /** 销售退货回传明细 */
    @Excel(name = "销售退货回传明细")
    @ApiModelProperty(value = "销售退货回传明细")
    private List<AfterSalesReturnDetailVO> detail;


}
