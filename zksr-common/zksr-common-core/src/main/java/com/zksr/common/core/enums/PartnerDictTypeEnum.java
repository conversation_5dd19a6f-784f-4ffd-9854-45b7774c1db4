package com.zksr.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
public enum PartnerDictTypeEnum {

    SYS_AFTER_REASON("sys_after_reason","退款原因"),
    TRD_DRIVER_RATING_REASON("trd_driver_rating_reason","评价原因"),
    TRD_DRIVER_RATING_SCORE("trd_driver_rating_score","司机评价评分"),
    TRD_DRIVER_RATING_SLOT("trd_driver_rating_slot","司机评价维度"),
    PRODUCT_DISTRIBUTION("product_distribution","商品配送标签");


    /**
     * 编码
     */
    private final String type;
    /**
     * 名字
     */
    private final String name;

    PartnerDictTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
