package com.zksr.common.core.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 通用消息推送类型
 * @date 2024/6/13 15:27
 */
@Getter
@AllArgsConstructor
public enum CommonMessagePushModeEnum {
    APPLET(0, "小程序"),
    PUBLISH(1, "公众号"),
    WX_MERCHANT(2, "微信商家助手"),
    USER_APP(3, "用户APP站内"),
    ;
    /**
     * 消息推送方式
     */
    @JsonValue
    private Integer mode;

    /**
     * 方式名称
     */
    private String modeName;


    @JsonCreator
    public static CommonMessagePushModeEnum fromValue(Integer value) {
        for (CommonMessagePushModeEnum b : CommonMessagePushModeEnum.values()) {
            if (b.mode.equals(value)) {
                return b;
            }
        }
        return null;
    }
}
