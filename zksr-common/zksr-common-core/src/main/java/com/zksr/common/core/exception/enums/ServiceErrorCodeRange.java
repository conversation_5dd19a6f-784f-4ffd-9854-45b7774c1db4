package com.zksr.common.core.exception.enums;

/**
 * 业务异常的错误码区间，解决：解决各模块错误码定义，避免重复，在此只声明不做实际使用
 *
 * 一共 10 位，分成四段
 *
 * 第一段，1 位，类型
 *      1 - 业务级别异常
 *      x - 预留
 * 第二段，3 位，系统类型
 *      000 - auth系统
 *      100 - system系统
 *      101 - product系统
 *      102 - member系统
 *      103 - supplier系统
 *      104 - promotion系统
 *      105 - trade系统
 *      106 - account系统
 *      300 - file系统
 *      400 - job系统
 *      500 - report系统
 *      ... - ...
 * 第三段，3 位，模块
 *      不限制规则。
 *      一般建议，每个系统里面，可能有多个模块，可以再去做分段。以system系统为例子：
 *          001 - partner 模块
 *          002 - area 模块
 *          003 - group 模块
 *          004 - dc模块
 * 第四段，3 位，错误码
 *       不限制规则。
 *       一般建议，每个模块自增。
 *
 */
public class ServiceErrorCodeRange {

    // 模块 auth 错误码区间        [1-000-000-000 ~ 1-001-000-000)
    // 模块 system 错误码区间      [1-100-000-000 ~ 1-101-000-000)
    // 模块 product 错误码区间     [1-101-000-000 ~ 1-102-000-000)
    // 模块 member 错误码区间      [1-102-000-000 ~ 1-103-000-000)
    // 模块 supplier 错误码区间    [1-103-000-000 ~ 1-104-000-000)
    // 模块 promotion 错误码区间   [1-104-000-000 ~ 1-105-000-000)
    // 模块 trade 错误码区间       [1-105-000-000 ~ 1-106-000-000)
    // 模块 account 错误码区间     [1-106-000-000 ~ 1-107-000-000)
    // 模块 file 错误码区间        [1-300-000-000 ~ 1-301-000-000)
    // 模块 job 错误码区间         [1-400-000-000 ~ 1-401-000-000)
    // 模块 report 错误码区间      [1-500-000-000 ~ 1-501-000-000)

}
