package com.zksr.common.core.serialize;


import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class LongToDateStringSerializer extends JsonSerializer<Long> {

    private static final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public void serialize(Long timestamp, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (timestamp != null && timestamp > 0) {
            String formattedDate = formatter.format(new Date(timestamp));
            gen.writeString(formattedDate);
        } else {
            gen.writeString("");
        }
    }
}
