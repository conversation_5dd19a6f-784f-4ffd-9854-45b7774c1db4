package com.zksr.common.core.domain.erp.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
*
 * 销售订单
* <AUTHOR>
* @date 2024/6/4 10:17
*/
@Data
@Accessors(chain = true)
public class ErpOrderDTO {

    /** 收款方式 HDSK/ */
    private String collectedWay;

    /** 门店编号 */
    private String consumerNo;

    /** 业务员编号 */
    private String employeeNo;

    /** B2B单号 */
    private String b2bSheetNo;

    /** 配送方式 0-全国物流 1-本地配送*/
    private Integer b2bDeliveryType;

    /** 预计时间 */
    private List<ErpOrderDetailDTO> subList;

}
