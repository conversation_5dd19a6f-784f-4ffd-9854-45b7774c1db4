package com.zksr.common.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * excel头部信息
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ExcelHead {

    /**
     * 表头描述
     */
    String description() default "";

    /**
     * 页名称
     */
    String sheetName() default "";
}
