<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zksr</groupId>
        <artifactId>zksr-common</artifactId>
        <version>3.6.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zksr-common-security</artifactId>

    <description>
        zksr-common-security安全模块
    </description>

    <dependencies>

        <!-- Spring Web -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <!-- Zksr-Mall Api system -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-system</artifactId>
        </dependency>

        <!-- Zksr-Mall Api file -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-system</artifactId>
        </dependency>

        <!-- Zksr-Mall Api product -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-product</artifactId>
        </dependency>

        <!-- Zksr-Mall Api member -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-member</artifactId>
        </dependency>

        <!-- Zksr-Mall Api supplier -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-supplier</artifactId>
        </dependency>

        <!-- Zksr-Mall Api promotion -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-promotion</artifactId>
        </dependency>

        <!-- Zksr-Mall Api trade -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-trade</artifactId>
        </dependency>

        <!-- Zksr-Mall Api account -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-account</artifactId>
        </dependency>

        <!-- Zksr-Mall Api demo -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-demo</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Redis-->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-all</artifactId>
            <version>1.6.1</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

</project>
