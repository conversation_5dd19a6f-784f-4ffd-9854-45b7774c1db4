package com.zksr.common.security.annotation;

import com.zksr.common.security.config.ApplicationConfig;
import com.zksr.common.security.feign.FeignAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 自定义feign注解
 * 添加basePackages路径
 *
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@EnableFeignClients
@Import({ FeignAutoConfiguration.class })
public @interface EnableRyFeignClients
{
    String[] value() default {};

    String[] basePackages() default { "com.zksr" };

    Class<?>[] basePackageClasses() default {};

    Class<?>[] defaultConfiguration() default {};

    Class<?>[] clients() default {};
}
