package com.zksr.common.security.feign;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zksr.common.core.constant.MallSecurityConstants;
import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ip.IpUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * feign 请求拦截器
 *
 * <AUTHOR>
 */
@Component
public class FeignRequestInterceptor implements RequestInterceptor
{
    @Override
    public void apply(RequestTemplate requestTemplate)
    {
        HttpServletRequest httpServletRequest = ServletUtils.getRequest();
        Map<String, String> headers = null;
        if (StringUtils.isNotNull(httpServletRequest)) {
            headers = ServletUtils.getHeaders(httpServletRequest);
        }
        if (SecurityConstants.EXPORT.equals(SecurityContextHolder.get(SecurityConstants.FROM_SOURCE))) {
            // 王珂 2024年1月16, 导出线程无 Request 上下文环境
            Map<String, Object> localMap = SecurityContextHolder.getLocalMap();
            headers = new HashMap<>();
            for (String key : localMap.keySet()) {
                headers.put(key, String.valueOf(localMap.get(key)));
            }
        }
        if (ObjectUtil.isNotEmpty(headers)) {
            // 传递用户信息请求头，防止丢失
            String userId = headers.get(SecurityConstants.DETAILS_USER_ID);
            if (StringUtils.isNotEmpty(userId)) {
                requestTemplate.header(SecurityConstants.DETAILS_USER_ID, userId);
            }
            String userKey = headers.get(SecurityConstants.USER_KEY);
            if (StringUtils.isNotEmpty(userKey)) {
                requestTemplate.header(SecurityConstants.USER_KEY, userKey);
            }
            String sysCode = headers.get(SecurityConstants.SYS_CODE);
            if (StringUtils.isNotEmpty(sysCode)) {
                requestTemplate.header(SecurityConstants.SYS_CODE, sysCode);
            }
            String dcId = headers.get(SecurityConstants.DC_ID);
            if (StringUtils.isNotEmpty(dcId)) {
                requestTemplate.header(SecurityConstants.DC_ID, sysCode);
            }
            String userName = headers.get(SecurityConstants.DETAILS_USERNAME);
            if (StringUtils.isNotEmpty(userName)) {
                requestTemplate.header(SecurityConstants.DETAILS_USERNAME, userName);
            }
            String authentication = headers.get(SecurityConstants.AUTHORIZATION_HEADER);
            if (StringUtils.isNotEmpty(authentication)) {
                requestTemplate.header(SecurityConstants.AUTHORIZATION_HEADER, authentication);
            }

            String saasToken = headers.get(SecurityConstants.SAAS_ACCESS_TOKEN);
            if (StringUtils.isNotEmpty(saasToken)) {
                requestTemplate.header(SecurityConstants.SAAS_ACCESS_TOKEN, saasToken);
            }
            String saasUserCode = headers.get(SecurityConstants.SAAS_USER_CODE);
            if (StringUtils.isNotEmpty(saasUserCode)) {
                requestTemplate.header(SecurityConstants.SAAS_USER_CODE, saasUserCode);
            }

            String saasTenantCode = headers.get(SecurityConstants.SAAS_TENANT_CODE);
            if (StringUtils.isNotEmpty(saasTenantCode)) {
                requestTemplate.header(SecurityConstants.SAAS_TENANT_CODE, saasTenantCode);
            }

            String mallAuthentication = headers.get(MallSecurityConstants.AUTHORIZATION_HEADER);
            if (StringUtils.isNotEmpty(mallAuthentication)) {
                requestTemplate.header(MallSecurityConstants.AUTHORIZATION_HEADER, mallAuthentication);
            }
            String openapiAuthentication = headers.get(OpenapiSecurityConstants.AUTHORIZATION_HEADER);
            if (StringUtils.isNotEmpty(openapiAuthentication)) {
                requestTemplate.header(OpenapiSecurityConstants.AUTHORIZATION_HEADER, openapiAuthentication);
            }


            // 导出rpc调用 传递分页信息 TODO 这可有待商榷
            if (SecurityConstants.EXPORT.equals(SecurityContextHolder.get(SecurityConstants.FROM_SOURCE))) {
                Page<Object> page = PageHelper.getLocalPage();
                if (Objects.nonNull(page)) {
                    requestTemplate.header(SecurityConstants.EXPORT_PAGE_NUM, String.valueOf(page.getPageNum()));
                    requestTemplate.header(SecurityConstants.EXPORT_PAGE_SIZE, String.valueOf(page.getPageSize()));
                    requestTemplate.header(SecurityConstants.FROM_SOURCE, SecurityConstants.EXPORT);
                };
            }
        }

        // 区别是导出
        if (SecurityConstants.EXPORT.equals(SecurityContextHolder.get(SecurityConstants.FROM_SOURCE))) {
            requestTemplate.header(SecurityConstants.FROM_SOURCE, SecurityConstants.EXPORT);
        } else {
            requestTemplate.header(SecurityConstants.FROM_SOURCE, SecurityConstants.INNER);
        }
        // 配置客户端IP
        requestTemplate.header("X-Forwarded-For", IpUtils.getIpAddr());
    }
}
