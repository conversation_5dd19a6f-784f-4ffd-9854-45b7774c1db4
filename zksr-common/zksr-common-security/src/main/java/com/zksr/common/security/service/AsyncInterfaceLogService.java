package com.zksr.common.security.service;

import com.zksr.common.core.domain.vo.openapi.SysInterfaceLogVO;
import com.zksr.system.api.log.LogApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 异步调用接口接收日志服务
 * 
 * <AUTHOR>
 */
@Service
public class AsyncInterfaceLogService
{
    @Autowired
    private LogApi logApi;

    /**
     * 保存系统日志记录
     */
    @Async
    public void saveSysLog(SysInterfaceLogVO sysInterfaceLogVO) throws Exception
    {
        logApi.insertSysInterfaceLog(sysInterfaceLogVO);
    }
}
