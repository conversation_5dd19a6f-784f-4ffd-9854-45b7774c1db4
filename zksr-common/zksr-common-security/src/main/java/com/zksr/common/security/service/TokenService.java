package com.zksr.common.security.service;

import cn.hutool.core.util.StrUtil;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.utils.JwtUtils;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ip.IpUtils;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class TokenService
{
    private static final Logger log = LoggerFactory.getLogger(TokenService.class);

    @Autowired
    private RedisService redisService;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private final static long expireTime = CacheConstants.EXPIRATION;

    private final static String ACCESS_TOKEN = CacheConstants.LOGIN_TOKEN_KEY;

    private final static String SAAS_USER_KEY_TOKEN = CacheConstants.SAAS_LOGIN_USER_TOKEN_KEY;

    private final static Long MILLIS_MINUTE_TEN = CacheConstants.REFRESH_TIME * MILLIS_MINUTE;

    /**
     * 创建令牌
     */
    public Map<String, Object> createToken(LoginUser loginUser) {
        String token = IdUtils.fastUUID();
        Long userId = loginUser.getSysUser().getUserId();
        String userName = loginUser.getSysUser().getUserName();
        Long sysCode = loginUser.getSysUser().getSysCode();
        loginUser.setToken(token);
        loginUser.setUserid(userId);
        loginUser.setUsername(userName);
        loginUser.setIpaddr(IpUtils.getIpAddr());
        loginUser.setSysCode(sysCode);
        loginUser.setDcId(loginUser.getSysUser().getDcId());
        loginUser.setColonelId(loginUser.getSysUser().getColonelId());
        refreshToken(loginUser);

        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<String, Object>();
        claimsMap.put(SecurityConstants.USER_KEY, token);
        claimsMap.put(SecurityConstants.DETAILS_USER_ID, userId);
        claimsMap.put(SecurityConstants.DETAILS_USERNAME, userName);
        claimsMap.put(SecurityConstants.SYS_CODE, sysCode);
        claimsMap.put(SecurityConstants.DC_ID, loginUser.getSysUser().getDcId());

        // 接口返回信息
        Map<String, Object> rspMap = new HashMap<String, Object>();
        rspMap.put("access_token", JwtUtils.createToken(claimsMap));
        rspMap.put("expires_in", expireTime);
        return rspMap;
    }

    /**
     * 创建和刷新令牌
     */
    public LoginUser createAndRefreshTokenBySaasLogin(LoginUser loginUser) {
        String token = null;
        String tokenKey = getLoginUserKeyTokenKey(loginUser); //redis保存登录用户的userKey, 减少频繁创建token
        token = redisService.getCacheObject(tokenKey);
        if(redisService.hasKey(tokenKey) && StrUtil.isNotBlank(token) && redisService.hasKey(getTokenKey(token))){
            LoginUser user = redisService.getCacheObject(getTokenKey(token));
            if(user!= null){
                redisService.setCacheObject(tokenKey, token, expireTime, TimeUnit.MINUTES); //刷新tokenKey缓存
                return user;
            }
        }
        token = IdUtils.fastUUID();
        Long userId = loginUser.getSysUser().getUserId();
        String userName = loginUser.getSysUser().getUserName();
        Long sysCode = loginUser.getSysUser().getSysCode();
        loginUser.setToken(token);
        loginUser.setUserid(userId);
        loginUser.setUsername(userName);
        loginUser.setIpaddr(IpUtils.getIpAddr());
        loginUser.setSysCode(sysCode);
        loginUser.setDcId(loginUser.getSysUser().getDcId());
        loginUser.setColonelId(loginUser.getSysUser().getColonelId());
        refreshToken(loginUser);
        return loginUser;
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser()
    {
        return getLoginUser(ServletUtils.getRequest());
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(HttpServletRequest request)
    {
        // 获取请求携带的令牌
        String token = SecurityUtils.getToken(request);
        return getLoginUser(token);
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(String token)
    {
        LoginUser user = null;
        try
        {
            if (StringUtils.isNotEmpty(token))
            {
                String userkey = JwtUtils.getUserKey(token);
                user = redisService.getCacheObject(getTokenKey(userkey));
                return user;
            }
        }
        catch (Exception e)
        {
            log.error("获取用户信息异常'{}'", e.getMessage());
        }
        return user;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(LoginUser loginUser)
    {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken()))
        {
            refreshToken(loginUser);
        }
    }

    /**
     * 删除用户缓存信息
     */
    public void delLoginUser(String token)
    {
        if (StringUtils.isNotEmpty(token))
        {
            String userkey = JwtUtils.getUserKey(token);
            redisService.deleteObject(getTokenKey(userkey));
        }
    }


    /**
     * 验证令牌有效期，相差不足120分钟，自动刷新缓存
     *
     * @param loginUser
     */
    public void verifyToken(LoginUser loginUser)
    {
        long expireTime = loginUser.getExpireTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN)
        {
            refreshToken(loginUser);
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(LoginUser loginUser)
    {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken());
        redisService.setCacheObject(userKey, loginUser, expireTime, TimeUnit.MINUTES);

        if (loginUser.getSysUser() != null) { //保存userKey
            redisService.setCacheObject(getLoginUserKeyTokenKey(loginUser), userKey, expireTime, TimeUnit.MINUTES);
        }
    }

    private String getTokenKey(String token)
    {
        return ACCESS_TOKEN + token;
    }


    public String getLoginUserKeyTokenKey(LoginUser loginUser){
        SysUser sysUser = loginUser.getSysUser();
        return SAAS_USER_KEY_TOKEN + sysUser.getSysCode() + ":" + sysUser.getUserId();
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUserByUserKey(String userKey)
    {
        LoginUser user = null;
        try
        {
            if (StringUtils.isNotEmpty(userKey))
            {
                user = redisService.getCacheObject(getTokenKey(userKey));
                return user;
            }
        }
        catch (Exception e)
        {
            log.error("获取用户信息异常'{}'", e.getMessage());
        }
        return user;
    }

    public void delLoginUserByUserKey(String userKey) {
        if (StringUtils.isNotEmpty(userKey))
        {
            redisService.deleteObject(getTokenKey(userKey));
        }
    }

    public void delRedisByKey(String key) {
        if (StringUtils.isNotEmpty(key) && redisService.hasKey(key))
        {
            redisService.deleteObject(key);
        }
    }
}
