package com.zksr.common.security.aspect;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ip.IpUtils;
import com.zksr.common.security.annotation.ReceiveInterfaceLog;
import com.zksr.common.core.domain.vo.openapi.SysInterfaceLogVO;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.security.service.AsyncInterfaceLogService;
import com.zksr.common.security.utils.OpenapiSecurityUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysOperLog;
import com.zksr.system.api.openapi.OpenApi;
import com.zksr.system.api.opensource.OpensourceApi;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.VisualApi;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

import static com.zksr.common.core.constant.OpenApiConstants.*;
import static com.zksr.common.core.constant.OpenApiConstants.REQ_STATUS_2;

/**
 * 接口接收日志记录处理
 * 
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class ReceiveInterfaceLogAspect
{
    @Autowired
    @Qualifier("visualSettingMasterBySupplierIdCache")
    private Cache<Long, VisualSettingMasterDto> visualSettingMasterBySupplierIdCache;

    @Resource
    private AsyncInterfaceLogService asyncInterfaceLogService;
    @Resource
    private OpensourceApi opensourceApi;

    /**
     * 构建
     */
    public ReceiveInterfaceLogAspect()
    {
    }
/*
    @Pointcut("@annotation(com.zksr.common.security.annotation.ReceiveInterfaceLog)")
    public void pointcut() {}

    @Around(value = "pointcut()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable{
        SysInterfaceLogVO sysInterfaceLogVO = new SysInterfaceLogVO();

        try{
            //得到使用注解的方法。可使用Method.getAnnotation(Class<T> annotationClass)获取指定的注解，然后可获得注解的属性
            Method method = ((MethodSignature)pjp.getSignature()).getMethod();
            //获取接口入参信息
            Object[] arguments = pjp.getArgs();
            //组装接收日志参数信息
            sysInterfaceLogVO = assembleLog(arguments, method);
            //执行方法
            Object proceed = pjp.proceed();
            //保存日志
            asyncInterfaceLogService.saveSysLog(SysInterfaceLogVO.assembleUpdateData(sysInterfaceLogVO,RECEIVE_SUCCESS ,LOG_STATUS_SUCCES,REQ_STATUS_2));
            return proceed;
        } catch (Throwable e) {
            //保存异常日志
            asyncInterfaceLogService.saveSysLog(SysInterfaceLogVO.assembleUpdateData(sysInterfaceLogVO,e.getMessage(),LOG_STATUS_FAIN,REQ_STATUS_1));
            throw new RuntimeException(e);
        }
    }*/

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(receiveLog)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, ReceiveInterfaceLog receiveLog, Object jsonResult)
    {
        handleLog(joinPoint, receiveLog, null, jsonResult);
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e 异常
     */
    @AfterThrowing(value = "@annotation(receiveLog)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, ReceiveInterfaceLog receiveLog, Exception e)
    {
        handleLog(joinPoint, receiveLog, e, null);
    }


    /**
     * 组装日志信息
     * @param reqData
     * @param receiveLog
     * @return
     */
    private SysInterfaceLogVO assembleLog(String reqData,ReceiveInterfaceLog receiveLog){
        //校验方法  获取注解参数信息
        Objects.requireNonNull(reqData);

        //设置日志信息
        String reqId = IdUtils.fastSimpleUUID();
        //获取入驻商编号
        Long supplierId = OpenapiSecurityUtils.getLoginOpensource().getOpensourceDto().getMerchantId();

        //设置系统来源 如果获取不到系统来源 默认B2B
        Integer source = SyncSourceType.B2B.getCode();

        //从缓存中获取入驻商开放配置、可视化配置
        //如果存在开发配置  获取对应的可视化配置中的系统来源
        VisualSettingMasterDto visualMaster =visualSettingMasterBySupplierIdCache.get(supplierId);
        if(Objects.isNull(visualMaster)){
            //如果则feign调用查询  并放入缓存中
            visualMaster = opensourceApi.getVisualSettingMasterByMerchantId(supplierId).getCheckedData();
            if(Objects.nonNull(visualMaster)){
                visualSettingMasterBySupplierIdCache.put(supplierId,visualMaster);
            }
        }
        //设置系统来源
        if(Objects.nonNull(visualMaster) && Objects.nonNull(visualMaster.getSourceType())){
                source = visualMaster.getSourceType();
        }

        return  SysInterfaceLogVO.assembleReceiveLogData(
                reqId,
                reqData,
                source,
                receiveLog.requestType().getB2bType(),
                supplierId,
                receiveLog.operationType().getCode());
    }


    /**
     * 处理日志信息
     * @param joinPoint
     * @param receiveLog
     * @param e
     * @param jsonResult
     */
    protected void handleLog(final JoinPoint joinPoint, ReceiveInterfaceLog receiveLog, final Exception e, Object jsonResult)
    {
        SysInterfaceLogVO sysInterfaceLogVO = new SysInterfaceLogVO();

        try{
            // 获取参数的信息，传入到数据库中。
            String reqData = setRequestValue(joinPoint, receiveLog.excludeParamNames());

            // 获取response，参数和值
            String respData = StringUtils.substring(JSON.toJSONString(jsonResult), 0, 2000);

            //组装接收日志参数信息
            sysInterfaceLogVO = assembleLog(reqData,receiveLog);
            //保存日志
            if(Objects.nonNull(e)){
                //保存异常日志
                asyncInterfaceLogService.saveSysLog(SysInterfaceLogVO.assembleUpdateData(sysInterfaceLogVO,e.getMessage(),LOG_STATUS_FAIN,REQ_STATUS_1));
            }else{
                asyncInterfaceLogService.saveSysLog(SysInterfaceLogVO.assembleUpdateData(sysInterfaceLogVO, respData,LOG_STATUS_SUCCES,REQ_STATUS_2));
            }

        } catch (Throwable tre) {
           /* //保存异常日志
            asyncInterfaceLogService.saveSysLog(SysInterfaceLogVO.assembleUpdateData(sysInterfaceLogVO,e.getMessage(),LOG_STATUS_FAIN,REQ_STATUS_1));*/
            throw new RuntimeException(tre);
        }
    }

    /**
     * 获取请求的参数
     * @param joinPoint
     * @param excludeParamNames
     * @return
     * @throws Exception
     */
    private String setRequestValue(JoinPoint joinPoint,String[] excludeParamNames) throws Exception
    {
        //获取接口请求方式
        String requestMethod = ServletUtils.getRequest().getMethod();
        Map<?, ?> paramsMap = ServletUtils.getParamMap(ServletUtils.getRequest());
        if (StringUtils.isEmpty(paramsMap)
                && (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod)))
        {
            String params = argsArrayToString(joinPoint.getArgs(), excludeParamNames);
            return StringUtils.substring(params, 0, 2000);
        }
        else
        {
            return StringUtils.substring(JSON.toJSONString(paramsMap), 0, 2000);
        }
    }


    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray, String[] excludeParamNames)
    {
        String params = "";
        if (paramsArray != null && paramsArray.length > 0)
        {
            for (Object o : paramsArray)
            {
                if (StringUtils.isNotNull(o) && !isFilterObject(o))
                {
                    try
                    {
                        String jsonObj = JSON.toJSONString(o);
                        params += jsonObj.toString() + " ";
                    }
                    catch (Exception e)
                    {
                    }
                }
            }
        }
        return params.trim();
    }


    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o)
    {
        Class<?> clazz = o.getClass();
        if (clazz.isArray())
        {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        }
        else if (Collection.class.isAssignableFrom(clazz))
        {
            Collection collection = (Collection) o;
            for (Object value : collection)
            {
                return value instanceof MultipartFile;
            }
        }
        else if (Map.class.isAssignableFrom(clazz))
        {
            Map map = (Map) o;
            for (Object value : map.entrySet())
            {
                Map.Entry entry = (Map.Entry) value;
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }

}
