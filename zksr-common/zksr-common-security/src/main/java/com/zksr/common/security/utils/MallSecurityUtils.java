package com.zksr.common.security.utils;

import com.zksr.common.core.constant.MallSecurityConstants;
import com.zksr.common.core.constant.TokenConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.member.api.LoginMember;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 权限获取工具类
 *
 * <AUTHOR>
 */
public class MallSecurityUtils {
    /**
     * 获取member ID
     */
    public static Long getMemberId() {
        return SecurityContextHolder.getMemberId();
    }

    /**
     * 获取member key
     */
    public static String getMemberKey() {
        return SecurityContextHolder.getMemberKey();
    }

    /**
     * 获取登录用户信息
     */
    public static LoginMember getLoginMember() {
        return SecurityContextHolder.get(MallSecurityConstants.LOGIN_MEMBER, LoginMember.class);
    }

    /**
     * 获取请求token
     */
    public static String getToken() {
        return getToken(ServletUtils.getRequest());
    }

    /**
     * 根据request获取请求token
     */
    public static String getToken(HttpServletRequest request){
        // 从header获取token标识
        String token = request.getHeader(TokenConstants.MALL_AUTHORIZATION);
        return replaceTokenPrefix(token);
    }

    /**
     * 裁剪token前缀
     */
    public static String replaceTokenPrefix(String token) {
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.MALL_PREFIX)) {
            token = token.replaceFirst(TokenConstants.MALL_PREFIX, "");
        }
        return token;
    }

    public static Long getBranchId() {
        LoginMember loginMember = getLoginMember();
        return Objects.isNull(loginMember) ? null : loginMember.getBranchId();
    }

    /**
     * 获取Colonel ID
     */
    public static Long getColonelId() {
        LoginMember loginMember = getLoginMember();
        return Objects.isNull(loginMember) ? null : loginMember.getMember().getRelateColonelId();
    }
}
