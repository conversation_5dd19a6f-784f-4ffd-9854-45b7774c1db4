package com.zksr.common.security.aspect;

import com.zksr.common.core.constant.MallSecurityConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.exception.InnerAuthException;
import com.zksr.common.core.exception.MallAuthException;
import com.zksr.common.core.exception.MallWithoutBranchException;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.auth.MallAuthUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 基于 Spring Aop 的注解鉴权
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class MallPreAuthorizeAspect {
    /**
     * 构建
     */
    public MallPreAuthorizeAspect()
    {
    }

    /**
     * 定义AOP签名 (切入所有使用鉴权注解的方法)
     */
    public static final String POINTCUT_SIGN = " @annotation(com.zksr.common.security.annotation.RequiresMallLogin)";

    /**
     * 声明AOP签名
     */
    @Pointcut(POINTCUT_SIGN)
    public void pointcut() {
    }

    /**
     * 环绕切入
     *
     * @param joinPoint 切面对象
     * @return 底层方法执行后的返回值
     * @throws Throwable 底层方法抛出的异常
     */
    @Around("pointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 注解鉴权
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        checkMethodAnnotation(signature.getMethod());
        try {
            // 执行原有逻辑
            Object obj = joinPoint.proceed();
            return obj;
        } catch (Throwable e) {
            throw e;
        }
    }

    /**
     * 对一个Method对象进行注解检查
     */
    public void checkMethodAnnotation(Method method) {
        // 校验 @RequiresLogin 注解
        RequiresMallLogin requiresLogin = method.getAnnotation(RequiresMallLogin.class);
        String memberId = ServletUtils.getRequest().getHeader(MallSecurityConstants.DETAILS_MEMBER_ID);
        String branchId = ServletUtils.getRequest().getHeader(MallSecurityConstants.BRANCH_ID);
        if (requiresLogin != null) {
            boolean mallLoginFlag =  requiresLogin.isMember();
            if (mallLoginFlag && (StringUtils.isEmpty(memberId))) {
                throw new MallAuthException("没有设置商城用户信息，不允许访问 ");
            }

            //检查门店状态
            if(mallLoginFlag && (StringUtils.isEmpty(branchId))){
                throw new MallWithoutBranchException("当前用户未设置默认门店，请添加或切换门店 ");
            }

            MallAuthUtil.checkLogin();

        }
    }
}
