package com.zksr.common.security.annotation;


import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;

import java.lang.annotation.*;

/**
 * 自定义操作日志记录注解
 * 
 * <AUTHOR>
 *
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface ReceiveInterfaceLog
{
    /**
     * 操作类型
     */
    public OperationType operationType() default OperationType.ADD;

    /**
     * 接口类型
     */
    public B2BRequestType requestType() default B2BRequestType.EMPTY;

    /**
     * 排除指定的请求参数
     */
    public String[] excludeParamNames() default {};

}
