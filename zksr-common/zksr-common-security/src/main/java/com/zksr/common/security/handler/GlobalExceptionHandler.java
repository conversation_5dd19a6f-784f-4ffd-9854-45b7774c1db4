package com.zksr.common.security.handler;

import com.zksr.common.core.constant.HttpStatus;
import com.zksr.common.core.exception.*;
import com.zksr.common.core.exception.auth.NotPermissionException;
import com.zksr.common.core.exception.auth.NotRoleException;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.domain.AjaxResult;
import io.seata.core.context.RootContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.sql.SQLException;
import java.sql.SQLSyntaxErrorException;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler
{
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 权限码异常
     */
    @ExceptionHandler(NotPermissionException.class)
    public AjaxResult handleNotPermissionException(NotPermissionException e, HttpServletRequest request, HttpServletResponse response) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',权限码校验失败'{}'", requestURI, e.getMessage(), e);
        setRespErrStatus(response);
        return AjaxResult.error(HttpStatus.FORBIDDEN, "没有访问权限，请联系管理员授权");
    }

    /**
     * 角色权限异常
     */
    @ExceptionHandler(NotRoleException.class)
    public AjaxResult handleNotRoleException(NotRoleException e, HttpServletRequest request, HttpServletResponse response) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',角色权限校验失败'{}'", requestURI, e.getMessage(), e);
        setRespErrStatus(response);
        return AjaxResult.error(HttpStatus.FORBIDDEN, "没有访问权限，请联系管理员授权");
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public AjaxResult handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e, HttpServletRequest request, HttpServletResponse response) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',不支持'{}'请求", requestURI, e.getMethod(), e);
        setRespErrStatus(response);
//        String tip = String.format("系统异常，请联系管理员.详细错误%s", e.getMessage());
        String tip = String.format("系统异常，请联系管理员.");
        return AjaxResult.error(tip, e.getMessage());
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public AjaxResult handleServiceException(ServiceException e, HttpServletRequest request, HttpServletResponse response) {
        log.error(" 业务异常", e);
        Integer code = e.getCode();
        setRespErrStatus(response);
        return StringUtils.isNotNull(code) ? AjaxResult.error(code, e.getMessage()) : AjaxResult.error(e.getMessage());
    }

    /**
     * 请求路径中缺少必需的路径变量
     */
    @ExceptionHandler(MissingPathVariableException.class)
    public AjaxResult handleMissingPathVariableException(MissingPathVariableException e, HttpServletRequest request, HttpServletResponse response) {
        String requestURI = request.getRequestURI();
        log.error(StringUtils.format("请求路径中缺少必需的路径变量'{}',发生系统异常.", requestURI), e);
        setRespErrStatus(response);
        return AjaxResult.error(String.format("请求路径中缺少必需的路径变量[%s]", e.getVariableName()));
    }

    /**
     * 请求参数类型不匹配
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public AjaxResult handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request, HttpServletResponse response) {
        String requestURI = request.getRequestURI();
        log.error("请求参数类型不匹配'{}',发生系统异常.", requestURI, e);
        setRespErrStatus(response);
        return AjaxResult.error(String.format("请求参数类型不匹配，参数[%s]要求类型为：'%s'，但输入值为：'%s'", e.getName(), e.getRequiredType().getName(), e.getValue()));
    }

    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public AjaxResult handleRuntimeException(RuntimeException e, HttpServletRequest request, HttpServletResponse response)  {
        String requestURI = request.getRequestURI();

        log.error(StringUtils.format(" handleRuntimeException请求地址'{}',发生未知异常.", requestURI), e);
        setRespErrStatus(response);
        //String tip = String.format("系统异常，请联系管理员.详细错误%s", e.getMessage());
        String tip = String.format("系统异常，请联系管理员.");
        return AjaxResult.error(tip, e.getMessage());
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e, HttpServletRequest request, HttpServletResponse response) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生系统异常.", requestURI, e);
        setRespErrStatus(response);
        //String tip = String.format("系统异常，请联系管理员.详细错误%s", e.getMessage());
        String tip = String.format("系统异常，请联系管理员.");
        return AjaxResult.error(tip, e.getMessage());
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(BindException.class)
    public AjaxResult handleBindException(BindException e, HttpServletResponse response)
    {
        log.error(e.getMessage(), e);
        String message = e.getAllErrors().get(0).getDefaultMessage();
        setRespErrStatus(response);
        return AjaxResult.error(message);
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletResponse response) {
        log.error(e.getMessage(), e);
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        setRespErrStatus(response);
        return AjaxResult.error(message);
    }

    /**
     * 内部认证异常
     */
    @ExceptionHandler(InnerAuthException.class)
    public AjaxResult handleInnerAuthException(InnerAuthException e, HttpServletResponse response) {
        log.error(" 内部认证异常,", e);
        setRespErrStatus(response);
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 商城认证异常
     */
    @ExceptionHandler(MallAuthException.class)
    public AjaxResult handleMallAuthException(MallAuthException e, HttpServletResponse response) {
        log.error(" 商城认证异常,", e);
        setRespErrStatus(response);
        return AjaxResult.error(HttpStatus.UNAUTHORIZED, e.getMessage());
    }

    /**
     * 商城认证异常
     */
    @ExceptionHandler(MallWithoutBranchException.class)
    public AjaxResult handleMallWithoutBranchException(MallWithoutBranchException e, HttpServletResponse response) {
        log.error(" 商城认证异常1,", e);
        setRespErrStatus(response);
        return AjaxResult.error(HttpStatus.WITHOUTBRANCH, e.getMessage());
    }

    /**
     * 演示模式异常
     */
    @ExceptionHandler(DemoModeException.class)
    public AjaxResult handleDemoModeException(DemoModeException e, HttpServletResponse response) {
        log.error(" 演示模式异常,", e);
        setRespErrStatus(response);
        return AjaxResult.error("演示模式，不允许操作");
    }

    @ExceptionHandler(SQLException.class)
    public AjaxResult handleSQLException(SQLException e, HttpServletRequest request, HttpServletResponse response) {
        log.error(" handleSQLException,请求地址{}",request.getRequestURI(), e);
        setRespErrStatus(response);
        return AjaxResult.error("系统发生SQL异常，请联系管理员.");
    }

    /**
     * 如果开启分布式事务，就设置response.status = 500，seata的tm（事务管理器）
     * 就是感知到 TmTransactionException异常，发起事务回滚
     */
    private void setRespErrStatus(HttpServletResponse response){
        //如果开启分布式事务,设置错误状态码,让事务回滚
        if (StringUtils.isNotBlank(RootContext.getXID())) {
            response.setStatus(HttpStatus.ERROR);
        }else {
            response.setStatus(HttpStatus.SUCCESS);
        }
    }
}
