package com.zksr.common.security.interceptor;

import com.github.pagehelper.PageHelper;
import com.zksr.common.core.constant.MallSecurityConstants;
import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.security.auth.AuthUtil;
import com.zksr.common.security.auth.MallAuthUtil;
import com.zksr.common.security.auth.OpenapiAuthUtil;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.common.security.utils.OpenapiSecurityUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.LoginMember;
import com.zksr.system.api.LoginOpensource;
import com.zksr.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 自定义请求头拦截器，将Header数据封装到线程变量中方便获取
 * 注意：此拦截器会同时验证当前用户有效期自动刷新有效期
 * !@拦截器 - 头部拦截器
 * <AUTHOR>
 */
@Slf4j
public class HeaderInterceptor implements AsyncHandlerInterceptor
{
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception  {
        //log.info("preHandle:getSysCode():{}", SecurityContextHolder.getSysCode());
        if(ToolUtil.isNotEmpty(SecurityContextHolder.getSysCode()) && SecurityContextHolder.getSysCode() != 0L){
            log.info("SecurityContextHolder.getSysCode() 不为空，清理线程局部变量");
            SecurityContextHolder.remove();
        }

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        SecurityContextHolder.setUserId(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USER_ID));
        SecurityContextHolder.setUserName(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USERNAME));
        SecurityContextHolder.setUserKey(ServletUtils.getHeader(request, SecurityConstants.USER_KEY));
        SecurityContextHolder.setSysCode(ServletUtils.getHeader(request, SecurityConstants.SYS_CODE));
        SecurityContextHolder.setDcId(ServletUtils.getHeader(request, SecurityConstants.DC_ID));

        String saasToken = SecurityUtils.getSaasToken();
        SecurityContextHolder.setSaasToken(saasToken);
        String token = SecurityUtils.getToken();
        if(StringUtils.isNotEmpty(saasToken)){ //saas Token 和 原 token二选一
            SecurityContextHolder.setSaasUserCode(ServletUtils.getHeader(request, SecurityConstants.SAAS_USER_CODE));
            SecurityContextHolder.setSaasTenantCode(ServletUtils.getHeader(request, SecurityConstants.SAAS_TENANT_CODE));
            String userKey = SecurityUtils.getUserKey();
            LoginUser loginUser = AuthUtil.getLoginUserByUserKey(userKey);
            if (StringUtils.isNotNull(loginUser)) {
                AuthUtil.verifyLoginUserExpire(loginUser);
                SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
                SecurityContextHolder.set(SecurityConstants.SYS_CODES, loginUser.getSysCodeList());
            }
        }else {
            if (StringUtils.isNotEmpty(token)) {
                LoginUser loginUser = AuthUtil.getLoginUser(token);
                if (StringUtils.isNotNull(loginUser)) {
                    AuthUtil.verifyLoginUserExpire(loginUser);
                    SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
                    SecurityContextHolder.set(SecurityConstants.SYS_CODES, loginUser.getSysCodeList());
                }
            }
        }

        //mall
        SecurityContextHolder.setMemberKey(ServletUtils.getHeader(request, MallSecurityConstants.MEMBER_KEY));
        SecurityContextHolder.setMemberId(ServletUtils.getHeader(request, MallSecurityConstants.DETAILS_MEMBER_ID));
        //SecurityContextHolder.setMemberName(ServletUtils.getHeader(request, MallSecurityConstants.DETAILS_MEMBER_NAME));
        String mallToken = MallSecurityUtils.getToken();
        if (StringUtils.isNotEmpty(mallToken)) {
            LoginMember loginMember = MallAuthUtil.getLoginMember(mallToken);
            if (StringUtils.isNotNull(loginMember)) {
                MallAuthUtil.verifyLoginMemberExpire(loginMember);
                SecurityContextHolder.set(MallSecurityConstants.LOGIN_MEMBER, loginMember);
            }
        }

        //openapi
        SecurityContextHolder.setOpensourceKey(ServletUtils.getHeader(request, OpenapiSecurityConstants.OPENSOURCE_KEY));
        SecurityContextHolder.setOpensourceId(ServletUtils.getHeader(request, OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID));
        //SecurityContextHolder.setMemberName(ServletUtils.getHeader(request, MallSecurityConstants.DETAILS_MEMBER_NAME));
        //luoxiang add 0906
        String sysCode =  ServletUtils.getHeader(request, SecurityConstants.SYS_CODE);
        String opensourceId = ServletUtils.getHeader(request, OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID);
            if(ToolUtil.isNotEmpty(sysCode) && ToolUtil.isNotEmpty(opensourceId)){
            LoginOpensource loginOpensource = new LoginOpensource();
            loginOpensource.setSysCode(Long.parseLong(sysCode));
            loginOpensource.setOpensourceId(Long.parseLong(opensourceId));
            SecurityContextHolder.set(OpenapiSecurityConstants.LOGIN_OPENSOURCE, loginOpensource);

        }

        String openapiToken = OpenapiSecurityUtils.getToken();
        if (StringUtils.isNotEmpty(openapiToken)) {
            LoginOpensource loginOpensource = OpenapiAuthUtil.getLoginOpensource(openapiToken);
            if (StringUtils.isNotNull(loginOpensource)) {
                OpenapiAuthUtil.verifyLoginOpensourceExpire(loginOpensource);
                SecurityContextHolder.set(OpenapiSecurityConstants.LOGIN_OPENSOURCE, loginOpensource);
            }
        }

        // 交接RPC 分页支持
        if (SecurityConstants.EXPORT.equals(ServletUtils.getHeader(request, SecurityConstants.FROM_SOURCE))) {
            String pageNum = ServletUtils.getHeader(request, SecurityConstants.EXPORT_PAGE_NUM);
            if (StringUtils.isNotEmpty(pageNum) && Integer.parseInt(pageNum) > 0) {
                String pageSize = ServletUtils.getHeader(request, SecurityConstants.EXPORT_PAGE_SIZE);
                PageHelper.startPage(Integer.parseInt(pageNum), Integer.parseInt(pageSize), false);
            }
            SecurityContextHolder.set(SecurityConstants.FROM_SOURCE, SecurityConstants.EXPORT);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception
    {
        //log.info("afterCompletion线程id:{}", Thread.currentThread().getId());
        SecurityContextHolder.remove();
    }
}
