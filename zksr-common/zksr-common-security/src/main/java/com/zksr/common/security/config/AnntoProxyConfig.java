package com.zksr.common.security.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 安得内网 nacos 代理配置
 */

@Configuration
@ConfigurationProperties(prefix = "annto.proxy")
@Data
public class AnntoProxyConfig {

    /**
     * 是否使用代理
     */
    private boolean enable;

    /**
     * 代理IP或域名
     */
    private String host;

    /**
     * 代理端口
     */
    private int port;

}
