package com.zksr.common.security.service;

import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.MallSecurityConstants;
import com.zksr.common.core.utils.MallJwtUtils;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.LoginMember;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class MallTokenService
{
    private static final Logger log = LoggerFactory.getLogger(MallTokenService.class);

    @Autowired
    private RedisService redisService;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private final static long expireTime = RedisConstants.DAY_MINUTE * 30;

    private final static String ACCESS_TOKEN = RedisConstants.MALL_TOKENS;

    private final static Long MILLIS_MINUTE_TEN = CacheConstants.REFRESH_TIME * MILLIS_MINUTE;

    /**
     * 创建令牌
     */
    public Map<String, Object> createToken(LoginMember loginMember) {
        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<String, Object>();

        String token = IdUtils.fastUUID();
        if(ToolUtil.isNotEmpty(loginMember.getMember())){
            Long memberId = loginMember.getMember().getMemberId();
            String memberName = loginMember.getMember().getMemberName();
            loginMember.setMemberId(memberId);
            claimsMap.put(MallSecurityConstants.DETAILS_MEMBER_ID, memberId);
            claimsMap.put(MallSecurityConstants.DETAILS_MEMBER_NAME, memberName);
        }

        Long sysCode = loginMember.getSysCode();
        loginMember.setToken(token);

        loginMember.setSysCode(sysCode);

        String oldTokenUuid = null;
        if(ToolUtil.isNotEmpty(loginMember.getMember())){
            oldTokenUuid = loginMember.getMember().getLoginToken();
        }
        refreshToken(loginMember, oldTokenUuid);

        claimsMap.put(MallSecurityConstants.MEMBER_KEY, token);
        claimsMap.put(MallSecurityConstants.SYS_CODE, sysCode);
        claimsMap.put(MallSecurityConstants.BRANCH_ID, loginMember.getBranchId());

        // 接口返回信息
        Map<String, Object> rspMap = new HashMap<String, Object>();
        rspMap.put("access_token", MallJwtUtils.createToken(claimsMap));
        rspMap.put("expires_in", expireTime);
        return rspMap;
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginMember getLoginMember() {
        return getLoginMember(ServletUtils.getRequest());
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginMember getLoginMember(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = MallSecurityUtils.getToken(request);
        return getLoginMember(token);
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginMember getLoginMember(String token) {
        LoginMember member = null;
        try {
            if (StringUtils.isNotEmpty(token)) {
                String memberkey = MallJwtUtils.getMemberKey(token);
                member = redisService.getCacheObject(getTokenKey(memberkey));
                return member;
            }
        }
        catch (Exception e) {
            log.error("获取用户信息异常'{}'", e.getMessage());
        }
        return member;
    }

    /**
     * 设置用户身份信息
     */
//    public void setLoginMember(LoginMember loginMember) {
//        if (StringUtils.isNotNull(loginMember) && StringUtils.isNotEmpty(loginMember.getToken())) {
//            refreshToken(loginMember);
//        }
//    }

    /**
     * 删除用户缓存信息
     */
    public void delLoginMember(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String userkey = MallJwtUtils.getMemberKey(token);
            redisService.deleteObject(getTokenKey(userkey));
        }
    }

    /**
     * 验证令牌有效期，相差不足120分钟，自动刷新缓存
     *
     * @param loginMember
     */
    public void verifyToken(LoginMember loginMember) {
        long expireTime = loginMember.getExpireTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN) {
            refreshToken(loginMember, null);
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginMember 登录信息
     */
    public void refreshToken(LoginMember loginMember, String oldTokenUuid) {
        loginMember.setLoginTime(System.currentTimeMillis());
        loginMember.setExpireTime(loginMember.getLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        String memberKey = getTokenKey(loginMember.getToken());
        redisService.setCacheObject(memberKey, loginMember, expireTime, TimeUnit.MINUTES);

        if(ToolUtil.isNotEmpty(oldTokenUuid)){
            String oldTokenKey = getTokenKey(oldTokenUuid);
            redisService.deleteObject(oldTokenKey);
        }
    }

    /**
     * 根据用户表中的login_token 清除redis，退出登录
     * @param oldTokenUuid
     */
    public void logoutByTokenUuid (String oldTokenUuid) {
        if(ToolUtil.isNotEmpty(oldTokenUuid)){
            String oldTokenKey = getTokenKey(oldTokenUuid);
            redisService.deleteObject(oldTokenKey);
        }
    }

    private String getTokenKey(String token) {
        return ACCESS_TOKEN + token;
    }
}
