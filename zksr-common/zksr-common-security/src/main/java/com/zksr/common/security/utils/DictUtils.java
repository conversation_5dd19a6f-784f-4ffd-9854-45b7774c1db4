package com.zksr.common.security.utils;

import java.util.Collection;
import java.util.List;
import com.alibaba.fastjson2.JSONArray;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.service.RedisService;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.domain.SysPartnerDictData;
import com.zksr.system.api.domain.SysPartnerDictType;

/**
 * 字典工具类
 * 
 * <AUTHOR>
 */
public class DictUtils
{
    /**
     * 设置字典缓存
     * 
     * @param key 参数键
     * @param dictDatas 字典数据列表
     */
    public static void setDictCache(String key, List<SysDictData> dictDatas)
    {
        SpringUtils.getBean(RedisService.class).setCacheObject(getCacheKey(key), dictDatas);
    }

    public static void setPartnerDictCache(String key, List<SysPartnerDictData> dictDatas,Long sysCode)
    {
        SpringUtils.getBean(RedisService.class).setCacheObject(getPartnerCacheKey(key,sysCode), dictDatas);
    }

    /**
     * 获取字典缓存
     * 
     * @param key 参数键
     * @return dictDatas 字典数据列表
     */
    public static List<SysDictData> getDictCache(String key)
    {
        JSONArray arrayCache = SpringUtils.getBean(RedisService.class).getCacheObject(getCacheKey(key));
        if (StringUtils.isNotNull(arrayCache))
        {
            return arrayCache.toList(SysDictData.class);
        }
        return null;
    }

    public static List<SysPartnerDictData> getPartnerDictCache(String key,Long sysCode)
    {
        JSONArray arrayCache = SpringUtils.getBean(RedisService.class).getCacheObject(getPartnerCacheKey(key,sysCode));
        if (StringUtils.isNotNull(arrayCache))
        {
            return arrayCache.toList(SysPartnerDictData.class);
        }
        return null;
    }

    /**
     * 删除指定字典缓存
     * 
     * @param key 字典键
     */
    public static void removeDictCache(String key)
    {
        SpringUtils.getBean(RedisService.class).deleteObject(getCacheKey(key));
    }

    public static void removePartnerDictCache(String key,Long sysCode)
    {
        SpringUtils.getBean(RedisService.class).deleteObject(getPartnerCacheKey(key,sysCode));
    }

    /**
     * 清空字典缓存
     */
    public static void clearDictCache()
    {
        Collection<String> keys = SpringUtils.getBean(RedisService.class).keys(CacheConstants.SYS_DICT_KEY + "*");
        SpringUtils.getBean(RedisService.class).deleteObject(keys);
    }

    /**
     * 设置cache key
     * 
     * @param configKey 参数键
     * @return 缓存键key
     */
    public static String getCacheKey(String configKey)
    {
        return CacheConstants.SYS_DICT_KEY + configKey;
    }

    /**
     * 设置平台商建
     * @param configKey
     * @return
     */
    public static String getPartnerCacheKey(String configKey,Long sysCode)
    {
        if (sysCode == null){
            sysCode = SecurityContextHolder.getSysCode();
        }
        return CacheConstants.SYS_PARTNER_DICT_KEY +sysCode+":"+ configKey;
    }
}
