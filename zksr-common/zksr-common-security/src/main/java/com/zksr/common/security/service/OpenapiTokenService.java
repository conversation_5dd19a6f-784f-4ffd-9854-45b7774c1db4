package com.zksr.common.security.service;

import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.utils.OpenapiJwtUtils;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.OpenapiSecurityUtils;
import com.zksr.system.api.LoginOpensource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class OpenapiTokenService
{
    private static final Logger log = LoggerFactory.getLogger(OpenapiTokenService.class);

    @Autowired
    private RedisService redisService;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private final static long expireTime = 60 * 2;

    private final static String ACCESS_TOKEN = RedisConstants.OPENAPI_TOKENS;

    //private final static Long MILLIS_MINUTE_TEN = CacheConstants.REFRESH_TIME * MILLIS_MINUTE;

    /**
     * 创建令牌
     */
    public Map<String, Object> createToken(LoginOpensource loginOpensource) {
        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<String, Object>();

        String token = IdUtils.fastUUID();
        if(ToolUtil.isNotEmpty(loginOpensource.getOpensourceDto())){
            Long opensourceId = loginOpensource.getOpensourceDto().getOpensourceId();
            loginOpensource.setOpensourceId(opensourceId);
            claimsMap.put(OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID, opensourceId);
        }

        Long sysCode = loginOpensource.getSysCode();
        loginOpensource.setToken(token);

        loginOpensource.setSysCode(sysCode);

        String oldTokenUuid = null;
        if(ToolUtil.isNotEmpty(loginOpensource.getOpensourceDto()) ){
            oldTokenUuid = loginOpensource.getOpensourceDto().getToken();
        }
        refreshToken(loginOpensource, oldTokenUuid);

        claimsMap.put(OpenapiSecurityConstants.OPENSOURCE_KEY, token);
        claimsMap.put(OpenapiSecurityConstants.SYS_CODE, sysCode);

        // 接口返回信息
        Map<String, Object> rspMap = new HashMap<String, Object>();
        rspMap.put("token", loginOpensource.getToken());
        rspMap.put("access_token", OpenapiJwtUtils.createToken(claimsMap));
        rspMap.put("expires_in", expireTime);
        return rspMap;
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginOpensource getLoginOpensource() {
        return getLoginOpensource(ServletUtils.getRequest());
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginOpensource getLoginOpensource(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = OpenapiSecurityUtils.getToken(request);
        return getLoginOpensource(token);
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginOpensource getLoginOpensource(String token) {
        LoginOpensource opensource = null;
        try {
            if (StringUtils.isNotEmpty(token)) {
                String opensourceKey = OpenapiJwtUtils.getOpensourceKey(token);
                opensource = redisService.getCacheObject(getTokenKey(opensourceKey));
                return opensource;
            }
        }
        catch (Exception e) {
            log.error("获取opensource信息异常'{}'", e.getMessage());
        }
        return opensource;
    }

    /**
     * 设置用户身份信息
     */
//    public void setLoginOpensource(LoginOpensource loginOpensource) {
//        if (StringUtils.isNotNull(loginOpensource) && StringUtils.isNotEmpty(loginOpensource.getToken())) {
//            refreshToken(loginOpensource);
//        }
//    }

    /**
     * 删除用户缓存信息
     */
    public void delLoginOpensource(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String opensourceKey = OpenapiJwtUtils.getOpensourceKey(token);
            redisService.deleteObject(getTokenKey(opensourceKey));
        }
    }

    /**
     * 验证令牌有效期，相差不足120分钟，自动刷新缓存
     *
     * @param loginOpensource
     */
    public void verifyToken(LoginOpensource loginOpensource) {
        long expireTime = loginOpensource.getExpireTime();
        long currentTime = System.currentTimeMillis();
//        if (expireTime - currentTime <= MILLIS_MINUTE_TEN) {
//            refreshToken(loginOpensource);
//        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginOpensource 登录信息
     */
    public void refreshToken(LoginOpensource loginOpensource, String oldTokenUuid) {
        loginOpensource.setLoginTime(System.currentTimeMillis());
        loginOpensource.setExpireTime(loginOpensource.getLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        String opensourceKey = getTokenKey(loginOpensource.getToken());
        redisService.setCacheObject(opensourceKey, loginOpensource, expireTime, TimeUnit.MINUTES);

        if(ToolUtil.isNotEmpty(oldTokenUuid)){
            String oldTokenKey = getTokenKey(oldTokenUuid);
//            redisService.deleteObject(oldTokenKey);
        }
    }

    private String getTokenKey(String token) {
        return ACCESS_TOKEN + token;
    }
}
