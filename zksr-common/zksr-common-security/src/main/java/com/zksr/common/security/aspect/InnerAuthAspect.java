package com.zksr.common.security.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.exception.InnerAuthException;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.security.annotation.InnerAuth;

import java.lang.reflect.Method;
import java.util.Objects;

/**
 * 内部服务调用验证处理
 * 
 * <AUTHOR>
 */
@Aspect
@Component
public class InnerAuthAspect implements Ordered
{
    @Pointcut("@within(com.zksr.common.security.annotation.InnerAuth) || @annotation(com.zksr.common.security.annotation.InnerAuth)")
    public void pointcut() {

    }
    @Around("pointcut()")
    public Object innerAround(ProceedingJoinPoint point) throws Throwable
    {
        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        Method method = methodSignature.getMethod();
        // 从方法上获取注解
        InnerAuth innerAuth = method.getAnnotation(InnerAuth.class);
        if (Objects.isNull(innerAuth)) {
            // 若方法上找不到， 从类上获取注解
            Class<?> declaringClass = method.getDeclaringClass();
            innerAuth = declaringClass.getAnnotation(InnerAuth.class);
        }

        String source = ServletUtils.getRequest().getHeader(SecurityConstants.FROM_SOURCE);
        // 内部请求验证
        if (!StringUtils.equals(SecurityConstants.INNER, source) && !StringUtils.equals(SecurityConstants.EXPORT, source))
        {
            throw new InnerAuthException("没有内部访问权限，不允许访问");
        }
        String userid = ServletUtils.getRequest().getHeader(SecurityConstants.DETAILS_USER_ID);
        String username = ServletUtils.getRequest().getHeader(SecurityConstants.DETAILS_USERNAME);
        // 用户信息验证
        if (innerAuth.isUser() && (StringUtils.isEmpty(userid) || StringUtils.isEmpty(username)))
        {
            throw new InnerAuthException("没有设置用户信息，不允许访问 ");
        }
        return point.proceed();
    }

    /**
     * 确保在权限认证aop执行前执行
     */
    @Override
    public int getOrder()
    {
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
}
