package com.zksr.common.security.rateLimit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Data
public class RateLimitConfig {

    private String abilityKey;
    private String rateLimiterKey;
    private int matchType; // 匹配类型 0 完整匹配 1 模糊匹配
    private int size; //令牌桶 大小
    private int microSecond = 1000; //生成令牌的周期
    private int consumer = 1; //一个请求消耗令牌数量
    private int waitTimer = 0; // 令牌没有时，等待最多xx ms后拿令牌
}
