package com.zksr.common.security.utils;

import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.constant.TokenConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.model.LoginUser;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 权限获取工具类
 *
 * <AUTHOR>
 */
public class SecurityUtils
{
    /**
     * 获取用户ID
     */
    public static Long getUserId()
    {
        return SecurityContextHolder.getUserId();
    }

    /**
     * 获取用户名称
     */
    public static String getUsername()
    {
        return SecurityContextHolder.getUserName();
    }

    /**
     * 获取用户key
     */
    public static String getUserKey()
    {
        return SecurityContextHolder.getUserKey();
    }

    /**
     * 获取登录用户信息
     */
    public static LoginUser getLoginUser()
    {
        return SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
    }

    /**
     * 获取请求token
     */
    public static String getToken()
    {
        return getToken(ServletUtils.getRequest());
    }

    /**
     * 获取请求token
     */
    public static String getSaasToken()
    {
        return getSaasToken(ServletUtils.getRequest());
    }

    private static String getSaasToken(HttpServletRequest request) {
        // 从header获取saas token标识
        String token = request.getHeader(TokenConstants.SAAS_ACCESS_TOKEN);
        return replaceTokenPrefix(token);
    }


    /**
     * 根据request获取请求token
     */
    public static String getToken(HttpServletRequest request){
        // 从header获取token标识
        String token = request.getHeader(TokenConstants.AUTHENTICATION);
        return replaceTokenPrefix(token);
    }

    /**
     * 裁剪token前缀
     */
    public static String replaceTokenPrefix(String token) {
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX))
        {
            token = token.replaceFirst(TokenConstants.PREFIX, "");
        }
        return token;
    }

    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId)
    {
        return userId != null && 1L == userId;
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword 真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 当前用户是否入驻商
     * @return
     */
    public static boolean isSupplier() {
        return Objects.nonNull(getSupplierId());
    }

    /**
     * 获取鉴权用户入驻商ID
     * @return
     */
    public static Long getSupplierId() {
        LoginUser loginUser = getLoginUser();
        if(loginUser==null) return null;
        SysUser sysUser = loginUser.getSysUser();
        Long supplierId = sysUser.getSupplierId();
        return supplierId;
    }

    public static Long getDcId() {
        LoginUser user = getLoginUser();
        if (Objects.isNull(user)) {
            return null;
        }
        return user.getDcId();
    }

    public static boolean hashSysCode() {
        return Objects.nonNull(getLoginUser()) && Objects.nonNull(getLoginUser().getSysCode());
    }

    /**
     * 当前用户是否业务员
     * @return
     */
    public static boolean isColonel() {
        return  Objects.nonNull(getLoginUser()) && Objects.nonNull(getLoginUser().getColonelId());
    }
}
