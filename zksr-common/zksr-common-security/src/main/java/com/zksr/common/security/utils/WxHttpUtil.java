package com.zksr.common.security.utils;

import cn.hutool.http.HttpGlobalConfig;
import cn.hutool.http.HttpRequest;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.security.config.AnntoProxyConfig;
import lombok.extern.slf4j.Slf4j;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/25 9:58
 */
@Slf4j
@SuppressWarnings("all")
public class WxHttpUtil {

    public static String post(String urlString, String body) {
        HttpRequest request = HttpRequest.post(urlString).timeout(HttpGlobalConfig.getTimeout()).body(body);
        AnntoProxyConfig proxyConfig = SpringUtils.getBean(AnntoProxyConfig.class);
        if (Objects.nonNull(proxyConfig) && StringUtils.isNotEmpty(proxyConfig.getHost())) {
            log.info("使用系统全局代理请求...");
            // 设置代理
            request.setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyConfig.getHost(), proxyConfig.getPort())));
        }
        return request.execute().body();
    }

    public static String get(String urlString) {
        HttpRequest request = HttpRequest.get(urlString).timeout(HttpGlobalConfig.getTimeout());
        AnntoProxyConfig proxyConfig = SpringUtils.getBean(AnntoProxyConfig.class);
        if (Objects.nonNull(proxyConfig) && StringUtils.isNotEmpty(proxyConfig.getHost())) {
            log.info("使用系统全局代理请求...");
            // 设置代理
            request.setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyConfig.getHost(), proxyConfig.getPort())));
        }
        return request.execute().body();
    }
}
