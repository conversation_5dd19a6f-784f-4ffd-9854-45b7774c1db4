package com.zksr.common.security.utils;

import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.TokenConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.system.api.LoginOpensource;

import javax.servlet.http.HttpServletRequest;

/**
 * 权限获取工具类
 *
 * <AUTHOR>
 */
public class OpenapiSecurityUtils {
    /**
     * 获取member ID
     */
    public static Long getOpensourceId() {
        return SecurityContextHolder.getOpensourceId();
    }

    /**
     * 获取member key
     */
    public static String getOpensourceKey() {
        return SecurityContextHolder.getOpensourceKey();
    }


    /**
     * 获取登录用户信息
     */
    public static LoginOpensource getLoginOpensource() {
        return SecurityContextHolder.get(OpenapiSecurityConstants.LOGIN_OPENSOURCE, LoginOpensource.class);
    }

    /**
     * 获取请求token
     */
    public static String getToken() {
        return getToken(ServletUtils.getRequest());
    }

    /**
     * 根据request获取请求token
     */
    public static String getToken(HttpServletRequest request){
        // 从header获取token标识
        String token = request.getHeader(TokenConstants.OPENAPI_AUTHORIZATION);
        return replaceTokenPrefix(token);
    }

    /**
     * 裁剪token前缀
     */
    public static String replaceTokenPrefix(String token) {
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.OPENAPI_PREFIX)) {
            token = token.replaceFirst(TokenConstants.OPENAPI_PREFIX, "");
        }
        return token;
    }

}
