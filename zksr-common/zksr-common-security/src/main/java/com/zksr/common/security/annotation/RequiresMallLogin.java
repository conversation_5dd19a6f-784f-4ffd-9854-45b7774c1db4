package com.zksr.common.security.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 登录认证：只有登录之后才能进入该方法
 *
 * <AUTHOR>
 *
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.METHOD, ElementType.TYPE })
public @interface RequiresMallLogin {

    /**
     * 是否校验member信息
     * 对于不对游客开放的接口，设为true
     */
    boolean isMember() default false;
}
