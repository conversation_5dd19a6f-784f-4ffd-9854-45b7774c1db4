package com.zksr.common.security.aspect;

import cn.hutool.core.util.StrUtil;
import com.zksr.common.core.constant.HttpStatus;
import com.zksr.common.core.constant.MallSecurityConstants;
import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.exception.InnerAuthException;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.annotation.RequiresOpenapiLogin;
import com.zksr.common.security.auth.MallAuthUtil;
import com.zksr.common.security.auth.OpenapiAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Method;

/**
 * 基于 Spring Aop 的注解鉴权
 *
 */
@Aspect
@Component
@Slf4j
public class OpenapiPreAuthorizeAspect {

    @Resource
    private RedissonClient redissonClient;

    /**
     * 构建
     */
    public OpenapiPreAuthorizeAspect()
    {
    }

    /**
     * 定义AOP签名 (切入所有使用鉴权注解的方法)
     */
    public static final String POINTCUT_SIGN = " @annotation(com.zksr.common.security.annotation.RequiresOpenapiLogin)";

    /**
     * 声明AOP签名
     */
    @Pointcut(POINTCUT_SIGN)
    public void pointcut() {
    }

    /**
     * 环绕切入
     *
     * @param joinPoint 切面对象
     * @return 底层方法执行后的返回值
     * @throws Throwable 底层方法抛出的异常
     */
    @Around("pointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        try {
            // 获取并校验opensourceId
            String opensourceId = validateAndExtractHeader(OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID);
            String rateLimiterKey = "";
            String abilityKey = "";

            Method method = signature.getMethod();
            RequiresOpenapiLogin requiresLogin = method.getAnnotation(RequiresOpenapiLogin.class);
            if (requiresLogin != null) {
                abilityKey = requiresLogin.abilityKey();
            }

            // 登录&权限控制优化
            if (requiresLogin != null) {
                performAuthorization(abilityKey);
            }

            // 流量控制逻辑优化
            if (!abilityKey.isEmpty() && !opensourceId.isEmpty()) {
                rateLimiterKey = formatRateLimiterKey(opensourceId, abilityKey);
                if (applyFlowControl(rateLimiterKey)) {
                    log.info("通过流量控制");
                } else {
                    return CommonResult.error(HttpStatus.FLOW_CONTROL, "The system is busy, please visit after a while");
                }
            }



            // 执行原有逻辑
            return joinPoint.proceed();
        } catch (Throwable e) {
            log.error("Error during method execution", e); // 增加日志记录异常
            throw e;
        }
    }

    private String validateAndExtractHeader(String headerName) {
        String headerValue = ServletUtils.getRequest().getHeader(headerName);
        // 这里可以添加对headerValue的校验逻辑
        return headerValue;
    }

    private String formatRateLimiterKey(String opensourceId, String abilityKey) {
        return StrUtil.format("openapilimiters:{}:{}", opensourceId, abilityKey);
    }

    private boolean applyFlowControl(String rateLimiterKey) {
        RRateLimiter rRateLimiter = redissonClient.getRateLimiter(rateLimiterKey);
        if (rRateLimiter == null) {
            log.error("Rate limiter is null for key: " + rateLimiterKey);
            return false; // 应该处理为false或者抛出异常
        }
        return rRateLimiter.tryAcquire();
    }

    private void performAuthorization(String abilityKey) {
        if (!abilityKey.isEmpty()) {
            OpenapiAuthUtil.checkLogin(abilityKey);
        } else {
            OpenapiAuthUtil.checkLogin();
        }
    }


}
