package com.zksr.common.rocketmq.dto;

import java.util.Map;

public class MqUserDto {

    private String id;
    private String name;
    private Map<String, Object> meta;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, Object> getMeta() {
        return meta;
    }

    public void setMeta(Map<String, Object> meta) {
        this.meta = meta;
    }

    @Override
    public String toString() {
        return "MqUserDto{" + "id='" + id + '\'' + ", name='" + name + '\'' + ", meta=" + meta
                + '}';
    }

}
