package com.zksr.common.rocketmq.event.promotion;

import com.zksr.common.rocketmq.event.BaseEvent;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 异步给门店发送优惠券事件消息
 * @date 2024/4/2 9:19
 */
@Data
public class CouponReceiveEvent extends BaseEvent {

    /**
     * 优惠券模版ID集合
     */
    private List<Long> couponTemplateIds;

    /**
     * 优惠券ID:状态ID
     */
    private Map<Long, Long> couponStatusMap = new HashMap<>();

    /**
     * 门店ID
     */
    private Long branchId;

    /**
     * 用户ID
     */
    private Long memberId;

    /**
     * 优惠券批次ID
     */
    private Long couponBatchId;

    /**
     * 领取模式,0-验证库存, 1-直接发放
     */
    private Integer mode = 0;

    public CouponReceiveEvent() {
    }

    public CouponReceiveEvent(List<Long> couponTemplateIds, Long branchId, Long memberId, Long couponBatchId) {
        this.couponTemplateIds = couponTemplateIds;
        this.branchId = branchId;
        this.memberId = memberId;
        this.couponBatchId = couponBatchId;
    }
}
