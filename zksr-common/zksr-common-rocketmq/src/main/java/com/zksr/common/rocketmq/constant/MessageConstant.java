package com.zksr.common.rocketmq.constant;


public class MessageConstant {

    /**
     * mytopic测试消息 - topic名
     */
    public static final String MYTOPIC_TOPIC = "mytopic";

    /**
     * mytopic2测试消息 - topic名
     */
    public static final String MYTOPIC2_TOPIC = "mytopic2";

    /**
     * mytopicConcurrency测试消息 - topic名
     */
    public static final String MYTOPICCONCURRENCY_TOPIC = "mytopicConcurrency";

    /**
     * mytopicDelay测试消息 - topic名
     */
    public static final String MYTOPICDELAY_TOPIC = "mytopicDelay";

    /**
     * mytopicOrderly测试消息 - topic名
     */
    public static final String MYTOPICORDERLY_TOPIC = "mytopicOrderly";


    /**
     * myObjectTopic测试消息 - topic名
     */
    public static final String MYOBJECTTOPIC_TOPIC = "myObjectTopic";


    /**
     * 支付回调消息
     */
    public static final String PAY_CALLBACK_MESSAGE = "pay_callback";

    /**
     * product_event 商品上架事件
     */
    public static final String PRODUCT_EVENT_TOPIC = "productEvent";

    /**
     * accountFlow
     */
    public static final String ACCOUNT_FLOW_TOPIC = "accountFlow";

    /**
     * payNotify    支付回调通知
     */
    public static final String ORDER_PAY_NOTIFY_TOPIC = "payNotify";
    /**
     * divedeNotify    分账回调通知
     */
    public static final String ORDER_DIVIDE_NOTIFY_TOPIC = "divideNotify";

    /**
     * accountTransfer  内部处理转账
     */
    public static final String ACCOUNT_TRANSFER_TOPIC = "accountTransfer";

    /**
     * refundNotify 退款回调通知
     */
    public static final String ORDER_REFUND_NOTIFY_TOPIC = "refundNotify";

    /**
     * refundDelayQuery 延迟退款查询
     */
    public static final String ORDER_REFUND_DELAY_QUERY_TOPIC = "refundDelayQuery";

    /**
     * divideDelayQuery 延迟分账查询
     */
    public static final String DIVIDE_DELAY_QUERY_TOPIC = "divideDelayQuery";

    /**
     * wxOrderDivideDelayQuery 微信整单分账状态查询
     */
    public static final String WX_ORDER_DIVIDE_DELAY_QUERY_TOPIC = "wxOrderDivideDelayQuery";

    /**
     * productCarEvent
     */
    public static final String PRODUCT_CAR_TOPIC = "productCarEvent";

    /**
     * systemServiceAddEvent
     */
    public static final String SYSTEM_SERVICE_ADD = "systemServiceAddEvent";

    /**
     * systemGeofenceAddEvent
     */
    public static final String SYSTEM_GEOFENCE_ADD_TOPIC = "systemGeofenceAddEvent";

    /**
     * systemGeofenceUpdateEvent
     */
    public static final String SYSTEM_GEOFENCE_UPDATE_TOPIC = "systemGeofenceUpdateEvent";

    /**
     * systemCheckGeofenceEvent
     */
    public static final String SYSTEM_CHECK_GEOFENCE_TOPIC = "systemCheckGeofenceEvent";

    /**
     * couponTemplateCache
     */
    public static final String COUPON_TEMPLATE_CACHE_TOPIC = "couponTemplateCache";

    /**
     * couponReceive 优惠券领取
     */
    public static final String COUPON_RECEIVE_TOPIC = "couponReceive";

    /**
     * supplierOrderExpressEvent
     */
    public static final String SUPPLIER_ORDER_EXPRESS_TOPIC = "supplierOrderExpressEvent";

    /**
     * StoreProductExpressEvent
     */
    public static final String STORE_PRODUCT_EXPRESS_TOPIC = "storeProductExpressEvent";

    /**
     * storePrintExpressEvent
     */
    public static final String STORE_PRINT_TOPIC = "storePrintExpressEvent";

    /**
     * 短信信息
     */
    public static final String SMS_TOPIC = "smsEvent";

    /**
     * 公众号,小程序订阅消息
     */
    public static final String SUBSCRIBE_MESSAGE_TOPIC = "subscribeMessageEvent";

    /**
     * b2b推送订单到erp
     */
    public static final String B2B_TO_ERP_TOPIC = "erpTradeOrderEvent";

    /**
     * 业务员APP客户信息统计
     */
    public static final String COLONEL_APP_BRANCH_TOPIC = "colonelAppBranchEvent";

    /**
     * 业务员APP首页信息统计
     */
    public static final String COLONEL_APP_PAGE_DATA_TOPIC = "colonelAppPageDataEvent";

    /**
     * 发送 货到付款订单默认成功消息
     */
    public static final String ORDER_HDFK_SUCCESS_TOPIC = "orderHdfkSuccessEvent";

    /**
     *  发送门店获取经纬度信息事件
     */
    public static final String BRANCH_GET_LONGITUDE_AND_LATITUDE_TOPIC = "branchGetLongitudeAndLatitudeEvent";

    /**
     * 发送B2B传递ERP收款单信息事件
     */
    public static final String B2B_TO_ERP_RECEIPT_TOPIC = "erpToErpReceiptEvent";

    /**
     * 发送 售后订单审核消息
     */
    public static final String AFTER_APPROVE_TOPIC = "afterApproveEvent";

    /**
     * 发送订单支付更新成功处理消息（用于处理订单支付完成后的业务逻辑）
     */
    public static final String ORDER_PAY_UPDATE_SUCCESS_TOPIC = "orderPayUpdateSuccessEvent";

    public static final String AFTER_ORDER_UPDATE_SUCCESS_TOPIC = "afterOrderUpdateSuccess";

    /**
     * 文件导入任务消息事件
     */
    public static final String FILE_IMPORT_TASK_TOPIC = "fileImportTaskEvent";

    /* =================================推送ERP接口相关========================================== */
    /* =================================推送第三方接口相关========================================== */

    /**
     * 同步门店
     */
    public static final String SYNC_DATA_BRANCH_TOPIC = "syncDataBranchEvent";

    /**
     * 同步销售订单
     */
    public static final String SYNC_DATA_ORDER_TOPIC = "syncDataOrderEvent";

    /**
     * 同步售后订单
     */
    public static final String SYNC_DATA_AFTER_TOPIC = "syncDataAfterEvent";


    /**
     * 同步货到收款订单信息 --  收款单
     */
    public static final String SYNC_DATA_RECEIPT_TOPIC = "syncDataReceiptEvent";

    /**
     * 同步门店储值充值、提现信息信息
     */
    public static final String SYNC_DATA_BRANCH_VALUE_INFO_TOPIC = "syncDataBranchValueInfoEvent";

    /**
     * 入驻商同步数据 -- 推送门店消息入口
     */
    public static final String SUPPLIER_SYNC_DATA_BRANCH_TOPIC = "supplierSyncDataBranchEvent";

    /**
     * 入驻商同步数据 -- 推送销售订单消息入口
     */
    public static final String SUPPLIER_SYNC_DATA_ORDER_TOPIC = "supplierSyncDataOrderEvent";

    /**
     * 入驻商同步数据 -- 推送售后订单消息入口
     */
    public static final String SUPPLIER_SYNC_DATA_AFTER_TOPIC = "supplierSyncDataAfterEvent";


    /**
     * 入驻商同步数据 -- 推送收款单消息入口
     */
    public static final String SUPPLIER_SYNC_DATA_RECEIPT_TOPIC = "supplierSyncDataReceiptEvent";

    /**
     * 入驻商同步数据 -- 推送门店储值充值、提现信息信息
     */
    public static final String SUPPLIER_SYNC_DATA_BRANCH_VALUE_INFO_TOPIC = "supplierSyncDataBranchValueInfoEvent";

    /**
     * 异步匹配要货单数据
     */
    public static final String YH_DATA_MATCH_TOPIC = "yhDataMatch";

/*    *//**
     * 同步数据统一推送消息入口
     *//*
    public static final String SYNC_DATA_TOPIC = "syncDataEvent";*/

    /**
     * openapi - 商品保存 消息
     */
    public static final String OPENAPI_SAVEPRDT_TOPIC = "openapi_saveprdt";

    /**
     * openapi - 商品生产日期更新 消息
     */
    public static final String OPENAPI_SAVEPRDT_DATE_TOPIC = "openapi_saveprdt_date";

    /**
     * openapi - 商品库存更新 消息
     */
    public static final String OPENAPI_SAVEPRDT_STOCK_TOPIC = "openapi_saveprdt_stock";

    /**
     * openapi - 订单收货确认 消息
     */
    public static final String OPENAPI_CONFIRM_RECEIPT_TOPIC = "openapi_confirm_receipt";

    /**
     * openapi - 售后退货确认 消息
     */
    public static final String OPENAPI_CONFIRM_RETURN_TOPIC = "openapi_confirm_return";

    /**
     * openapi - 订单发货 消息
     */
    public static final String OPENAPI_DELIVERY_TOPIC = "openapi_delivery";
    /**
     * openapi - 包裹发货 消息
     */
    public static final String OPENAPI_DELIVERY_PACKAGE_TOPIC = "openapi_delivery_package";

    /**
     * openapi - 订单状态 消息
     */
    public static final String OPENAPI_ORDERLOG_TOPIC = "openapi_orderlog";

    /**
     * openapi - 销售订单接收成功通知 消息
     */
    public static final String OPENAPI_ORDER_RECEIVE_CALLBACK_TOPIC = "openapi_order_receive_callback";

    /**
     * openapi - 销售订单取消接收通知 消息
     */
    public static final String OPENAPI_ORDER_CANCEL_RECEIVE_CALLBACK_TOPIC = "openapi_order_cancel_receive_callback";

    /**
     * openapi - 销售订单发货前取消 消息
     */
    public static final String OPENAPI_ORDER_CANCEL_TOPIC = "openapi_order_cancel";

    /**
     * openapi - 退货确认前取消(售后取消) 消息
     */
    public static final String OPENAPI_AFTER_CANCEL_TOPIC = "openapi_after_cancel";

    /**
     * openapi - 售后订单接收成功通知 消息
     */
    public static final String OPENAPI_AFTER_ORDER_RECEIVE_CALLBACK = "openapi_after_order_receive_callback";

    /**
     * openapi - 订单状态 消息
     */
    public static final String OPENAPI_AFTERLOG_TOPIC = "openapi_afterlog";

    /**
     * openapi - 入驻商保存 消息
     */
    public static final String  OPENAPI_SAVESUPPLIER_TOPIC = "openapi_savesupplier";

    /**
     * openapi - 新增货到付款清账能力 消息
     */
    public static final String OPENAPI_ADD_HDFK_SETTLE_TOPIC = "openapi_add_hdfk_settle";


    /**
     *  商品调价单审核成功 消息
     */
    public static final String PRODUCT_ADJUST_PRICES_APPROVE_TOPIC = "productAdjustPricesApproveEvent";

    /**
     * openapi - 批量要货数据
     */
    public static final String OPENAPI_BATCH_CREATE_YH = "openapi_batch_create_yh";

    /**
     * 加单指令消息
     */
    public static final String COMMAND_ADD_ORDER_TOPIC = "commandAddOrderEvent";


    /**
     * 测试事务 testTransaction 消息
     */
    public static final String TEST_TRANSACTION_TOPIC = "testTransaction";

    /**
     * openapi - 门店注册 消息
     */
    public static final String OPENAPI_REGISTER_BRANCH_TOPIC = "openapi_registerBranch";

    /* ======================================推送第三方接口相关结束====================================================*/

    /**
     * 生产者标识
     */
    public static final String DASH = "-";

    public static final String OUT = "out";

    public static final String INDEX_ZERO = "0";

    /**
     * 商品事件
     */
    public static final String PRODUCT_EVENT_TOPIC_OUT_PUT = PRODUCT_EVENT_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * mytopic消息生产者
     */
    public static final String MYTOPIC_MESSAGE_OUTPUT = MYTOPIC_TOPIC + "-" + OUT + "-" + INDEX_ZERO;

    /**
     * mytopic2消息生产者
     */
    public static final String MYTOPIC2_MESSAGE_OUTPUT = MYTOPIC2_TOPIC + "-" + OUT + "-" + INDEX_ZERO;

    /**
     * mytopicConcurrency消息生产者
     */
    public static final String MYTOPICCONCURRENCY_MESSAGE_OUTPUT = MYTOPICCONCURRENCY_TOPIC + "-" + OUT + "-" + INDEX_ZERO;

    /**
     * mytopicDelay消息生产者
     */
    public static final String MYTOPICDELAY_MESSAGE_OUTPUT = MYTOPICDELAY_TOPIC + "-" + OUT + "-" + INDEX_ZERO;

    /**
     * mytopicOrderly消息生产者
     */
    public static final String MYTOPICORDERLY_MESSAGE_OUTPUT = MYTOPICORDERLY_TOPIC + "-" + OUT + "-" + INDEX_ZERO;


    /**
     * mytopic消息生产者
     */
    public static final String MYOBJECTTOPIC_MESSAGE_OUTPUT = MYOBJECTTOPIC_TOPIC + "-" + OUT + "-" + INDEX_ZERO;

    /**
     * 账户流水
     */
    public static final String ACCOUNT_FLOW_TOPIC_OUT_PUT = ACCOUNT_FLOW_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 购物车变动事件
     */
    public static final String PRODUCT_CAR_TOPIC_OUT_PUT = PRODUCT_CAR_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 猎鹰新增服务生产者
     */
    public static final String SYSTEM_SERVICE_ADD_OUT_PUT = SYSTEM_SERVICE_ADD + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 猎鹰新增电子围栏
     */
    public static final String SYSTEM_GEOFENCE_ADD_TOPIC_OUT_PUT = SYSTEM_GEOFENCE_ADD_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 猎鹰更新电子围栏
     */
    public static final String SYSTEM_GEOFENCE_UPDATE_TOPIC_OUT_PUT = SYSTEM_GEOFENCE_UPDATE_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 猎鹰更新电子围栏
     */
    public static final String SYSTEM_CHECK_GEOFENCE_TOPIC_OUT_PUT = SYSTEM_CHECK_GEOFENCE_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 优惠券刷新缓存
     */
    public static final String COUPON_TEMPLATE_CACHE_TOPIC_OUT_PUT = COUPON_TEMPLATE_CACHE_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 优惠券领取
     */
    public static final String COUPON_RECEIVE_TOPIC_OUT_PUT = COUPON_RECEIVE_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 支付回调
     */
    public static final String ORDER_PAY_NOTIFY_TOPIC_OUT_PUT = ORDER_PAY_NOTIFY_TOPIC + DASH + OUT + DASH + INDEX_ZERO;
    /**
     * 分账回调
     */
    public static final String ORDER_DIVIDE_NOTIFY_TOPIC_OUT_PUT = ORDER_DIVIDE_NOTIFY_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 退款回调
     */
    public static final String ORDER_REFUND_NOTIFY_TOPIC_OUT_PUT = ORDER_REFUND_NOTIFY_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 延迟退款查询, 延迟(3分钟)
     */
    public static final String ORDER_REFUND_DELAY_QUERY_TOPIC_OUT_PUT = ORDER_REFUND_DELAY_QUERY_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 延迟分账查询, 延迟(8分钟)
     */
    public static final String DIVIDE_DELAY_QUERY_TOPIC_OUT_PUT = DIVIDE_DELAY_QUERY_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 微信整单分账状态查询, 延迟(2分钟)
     */
    public static final String WX_ORDER_DIVIDE_DELAY_QUERY_TOPIC_OUT_PUT = WX_ORDER_DIVIDE_DELAY_QUERY_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 处理内部账户转账
     */
    public static final String ACCOUNT_TRANSFER_TOPIC_OUT_PUT = ACCOUNT_TRANSFER_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     *  入驻商一件代发订单导入事件
     */
    public static final String SUPPLIER_ORDER_EXPRESS_TOPIC_OUT_PUT = SUPPLIER_ORDER_EXPRESS_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     *  门店下单统计Es消息
     */
    public static final String STORE_PRODUCT_EXPRESS_TOPIC_OUT_PUT = STORE_PRODUCT_EXPRESS_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     *  门店下单打印消息
     */
    public static final String STORE_PRINT_TOPIC_OUT_PUT = STORE_PRINT_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     *  短信信息
     */
    public static final String SMS_TOPIC_OUT_PUT = SMS_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     *  公众号小程序订阅消息
     */
    public static final String SUBSCRIBE_MESSAGE_TOPIC_OUT_PUT = SUBSCRIBE_MESSAGE_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     *  业务员APP门店信息统计
     */
    public static final String COLONEL_APP_BRANCH_TOPIC_OUT_PUT = COLONEL_APP_BRANCH_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     *  业务员APP首页信息统计
     */
    public static final String COLONEL_APP_PAGE_DATA_TOPIC_OUT_PUT = COLONEL_APP_PAGE_DATA_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     *  异步匹配要货单数据
     */
    public static final String YH_DATA_MATCH_TOPIC_OUT_PUT = YH_DATA_MATCH_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    public static final String B2B_TO_ERP_TOPIC_OUT_PUT = B2B_TO_ERP_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 发送 货到付款订单默认成功消息
     */
    public static final String ORDER_HDFK_SUCCESS_TOPIC_OUT_PUT = ORDER_HDFK_SUCCESS_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     *  发送门店获取经纬度信息事件
     */
    public static final String BRANCH_GET_LONGITUDE_AND_LATITUDE_TOPIC_OUT_PUT = BRANCH_GET_LONGITUDE_AND_LATITUDE_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     *  发送B2B传递ERP收款单消息
     */
    public static final String B2B_TO_ERP_RECEIPT_TOPIC_OUT_PUT = B2B_TO_ERP_RECEIPT_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 发送 售后订单审核消息
     */
    public static final String AFTER_APPROVE_TOPIC_OUT_PUT = AFTER_APPROVE_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 发送 同步门店
     */
    public static final String SYNC_DATA_BRANCH_TOPIC_OUT_PUT = SYNC_DATA_BRANCH_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 发送 同步销售订单
     */
    public static final String SYNC_DATA_ORDER_TOPIC_OUT_PUT = SYNC_DATA_ORDER_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 发送 同步售后订单
     */
    public static final String SYNC_DATA_AFTER_TOPIC_OUT_PUT = SYNC_DATA_AFTER_TOPIC + DASH + OUT + DASH + INDEX_ZERO;


    /**
     * 发送 同步货到收款订单信息 --收款单
     */
    public static final String SYNC_DATA_RECEIPT_TOPIC_OUT_PUT = SYNC_DATA_RECEIPT_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 发送 同步门店储值充值、提现信息信息
     */
    public static final String SYNC_DATA_BRANCH_VALUE_INFO_TOPIC_OUT_PUT = SYNC_DATA_BRANCH_VALUE_INFO_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

/*    *//**
     * 发送 同步数据统一推送消息入口
     *//*
    public static final String SYNC_DATA_TOPIC_OUT_PUT = SYNC_DATA_TOPIC + DASH + OUT + DASH + INDEX_ZERO;*/

    /**
     * 发送 入驻商同步数据 -- 推送门店消息入口
     */
    public static final String SUPPLIER_SYNC_DATA_BRANCH_TOPIC_OUT_PUT = SUPPLIER_SYNC_DATA_BRANCH_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 发送 入驻商同步数据 -- 推送销售订单消息入口
     */
    public static final String SUPPLIER_SYNC_DATA_ORDER_TOPIC_OUT_PUT = SUPPLIER_SYNC_DATA_ORDER_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 发送 入驻商同步数据 -- 推送售后订单消息入口
     */
    public static final String SUPPLIER_SYNC_DATA_AFTER_TOPIC_OUT_PUT = SUPPLIER_SYNC_DATA_AFTER_TOPIC + DASH + OUT + DASH + INDEX_ZERO;


    /**
     * 发送 入驻商同步数据 -- 推送收款单消息入口
     */
    public static final String SUPPLIER_SYNC_DATA_RECEIPT_TOPIC_OUT_PUT = SUPPLIER_SYNC_DATA_RECEIPT_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 发送 入驻商同步数据 -- 推送门店储值充值、提现信息消息入口
     */
    public static final String SUPPLIER_SYNC_DATA_BRANCH_VALUE_INFO_TOPIC_OUT_PUT = SUPPLIER_SYNC_DATA_BRANCH_VALUE_INFO_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 发送订单支付更新状态成功处理消息（用于处理订单支付完成后的业务逻辑）
     */
    public static final String ORDER_PAY_UPDATE_SUCCESS_TOPIC_OUT_PUT = ORDER_PAY_UPDATE_SUCCESS_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 用于处理货到付款无需付款,售后订单退款成功回调
     */
    public static final String AFTER_ORDER_UPDATE_SUCCESS_TOPIC_OUT_PUT = AFTER_ORDER_UPDATE_SUCCESS_TOPIC + DASH + OUT + DASH + INDEX_ZERO;


    /**
     * 购物车变动事件
     */
    public static final String TEST_TRANSACTION_TOPIC_OUT_PUT = TEST_TRANSACTION_TOPIC + DASH + OUT + DASH + INDEX_ZERO;



    /**
     * openapi发送 商品保存 消息
     */
    public static final String OPENAPI_SAVEPRDT_TOPIC_OUT_PUT = OPENAPI_SAVEPRDT_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 商品生产日期更新 消息
     */
    public static final String OPENAPI_SAVEPRDT_DATE_TOPIC_OUT_PUT = OPENAPI_SAVEPRDT_DATE_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 商品库存更新 消息
     */
    public static final String OPENAPI_SAVEPRDT_STOCK_TOPIC_OUT_PUT = OPENAPI_SAVEPRDT_STOCK_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 订单收货确认 消息
     */
    public static final String OPENAPI_CONFIRM_RECEIPT_TOPIC_OUT_PUT = OPENAPI_CONFIRM_RECEIPT_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 售后退货确认 消息
     */
    public static final String OPENAPI_CONFIRM_RETURN_TOPIC_OUT_PUT = OPENAPI_CONFIRM_RETURN_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 订单发货 消息
     */
    public static final String OPENAPI_DELIVERY_TOPIC_OUT_PUT = OPENAPI_DELIVERY_TOPIC + DASH + OUT + DASH + INDEX_ZERO;
    /**
     * openapi发送 包裹发货 消息
     */
    public static final String OPENAPI_DELIVERY_PACKAGE_TOPIC_OUT_PUT = OPENAPI_DELIVERY_PACKAGE_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 订单状态 消息
     */
    public static final String OPENAPI_ORDERLOG_TOPIC_OUT_PUT = OPENAPI_ORDERLOG_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 订单接收成功通知 消息
     */
    public static final String OPENAPI_ORDER_RECEIVE_CALLBACK_TOPIC_OUT_PUT = OPENAPI_ORDER_RECEIVE_CALLBACK_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 订单取消接收通知 消息
     */
    public static final String OPENAPI_ORDER_CANCEL_RECEIVE_CALLBACK_TOPIC_OUT_PUT = OPENAPI_ORDER_CANCEL_RECEIVE_CALLBACK_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 订单发货前取消 消息
     */
    public static final String OPENAPI_ORDER_CANCEL_TOPIC_OUT_PUT = OPENAPI_ORDER_CANCEL_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 退货确认前取消(售后取消) 消息
     */
    public static final String OPENAPI_AFTER_CANCEL_TOPIC_OUT_PUT = OPENAPI_AFTER_CANCEL_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 退单接收成功通知 消息
     */
    public static final String OPENAPI_AFTER_ORDER_RECEIVE_CALLBACK_TOPIC_OUT_PUT = OPENAPI_AFTER_ORDER_RECEIVE_CALLBACK + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 订单状态 消息
     */
    public static final String OPENAPI_AFTERLOG_TOPIC_OUT_PUT = OPENAPI_AFTERLOG_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 入驻商保存 消息
     */
    public static final String OPENAPI_SAVESUPPLIER_TOPIC_OUT_PUT = OPENAPI_SAVESUPPLIER_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 新增货到付款清账能力 消息
     */
    public static final String OPENAPI_ADD_HDFK_SETTLE_TOPIC_OUT_PUT = OPENAPI_ADD_HDFK_SETTLE_TOPIC + DASH + OUT + DASH + INDEX_ZERO;


    /**
     * openapi发送 商品调价单审核成功 消息 用于调整确认商品价格
     */
    public static final String PRODUCT_ADJUST_PRICES_APPROVE_TOPIC_OUT_PUT = PRODUCT_ADJUST_PRICES_APPROVE_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送 批量要货补货数据
     */
    public static final String OPENAPI_BATCH_CREATE_YH_TOPIC_OUT_PUT = OPENAPI_BATCH_CREATE_YH + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 文件导入任务消息事件
     */
    public static final String FILE_IMPORT_TASK_TOPIC_OUT_PUT = FILE_IMPORT_TASK_TOPIC+ DASH + OUT + DASH + INDEX_ZERO;

    /**
     * 加单指令消息
     */
    public static final String COMMAND_ADD_ORDER_TOPIC_OUT_PUT = COMMAND_ADD_ORDER_TOPIC + DASH + OUT + DASH + INDEX_ZERO;

    /**
     * openapi发送接收门店注册消息
     */
    public static final String OPENAPI_REGISTER_BRANCH_TOPIC_OUT_PUT = OPENAPI_REGISTER_BRANCH_TOPIC + DASH + OUT + DASH + INDEX_ZERO;


    /**
     * openapi发送 商品上下架 消息
     */
    public static final String OPENAPI_ADDAREAITEM_TOPIC_OUT_PUT = "openapi_addAreaItem" + DASH + OUT + DASH + INDEX_ZERO;

}
