<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zksr-common</artifactId>
        <groupId>com.zksr</groupId>
        <version>3.6.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zksr-common-rocketmq</artifactId>

    <description>
        zkht-common-rocketmq rocketmq消息队列
    </description>

    <dependencies>

        <!-- Zksr Common Core-->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-core</artifactId>
        </dependency>

        <!-- rocketmq version 4.9.4-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
        </dependency>

    </dependencies>

</project>
