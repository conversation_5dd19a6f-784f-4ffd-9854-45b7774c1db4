package com.zksr.common.elasticsearch.service.impl;

import com.zksr.common.core.constant.EsIndexNameConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.elasticsearch.domain.EsHomePagesSkuData;
import com.zksr.common.elasticsearch.mapper.EsHomePagesSkuDataFullMapper;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;
import com.zksr.common.elasticsearch.service.EsHomePagesSkuDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EsHomePagesSkuDataServiceImpl implements EsHomePagesSkuDataService {
    @Autowired
    private EsHomePagesSkuDataFullMapper esHomePagesSkuDataFullMapper;
    @Override
    public void initIndex() {
        try {esHomePagesSkuDataFullMapper.deleteIndex(EsIndexNameConstants.HOME_PAGES_SKU_DATA);} finally {}
        try {esHomePagesSkuDataFullMapper.createIndex();} finally {}
    }

    @Override
    public Boolean saveBatchHomePagesSkuData(List<EsHomePagesSkuData> dataList) {
        return esHomePagesSkuDataFullMapper.insertBatch(dataList) > NumberPool.INT_ZERO;
    }

    @Override
    public Boolean saveHomePagesSkuData(EsHomePagesSkuData data) {
        return esHomePagesSkuDataFullMapper.insert(data) > NumberPool.INT_ZERO;
    }

    @Override
    public EsHomePagesSkuData searchHomePagesSkuData(HomePagesSearchDTO searchDTO) {
        return esHomePagesSkuDataFullMapper.selectById(searchDTO.getId());
    }
}
