package com.zksr.common.elasticsearch.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.constant.EsIndexNameConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.math.BigDecimal;
import java.util.Date;

/**
*
 * ES 主页数据 - 当前销售、欠款、退单数据
* <AUTHOR>
* @date 2024/12/19 15:20
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@IndexName(value = EsIndexNameConstants.HOME_PAGES_DATA)
public class EsHomePagesData {

    /**
     * 唯一ID
     * 规则:  CONCAT(20241220 或 202412,'_',sys_code或dcId或supplierId)
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    /** 平台商id */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("平台商id")
    private Long sysCode;

    /** 运营商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("运营商ID")
    private Long dcId;

    /** 入驻商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("入驻商ID")
    private Long supplierId;

    /** 刷新ES时间 */
    @IndexField(fieldType = FieldType.DATE)
    @ApiModelProperty(value = "更新ES时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refreshEsTime;

    /**
     * 规则：20241220 或 202412
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("日期Id")
    private String dateId;

    //================-------订单信息-------================

    /** 当天订单实际销售额 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "当天订单实际销售额")
    private BigDecimal todayOrderTotalAmt;

    /** 咋天订单实际销售额 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "咋天订单实际销售额")
    private BigDecimal yesterdayOrderTotalAmt;

    /** 销售金额同比上升率（当天订单金额 - 咋天订单金额）/ 咋天订单金额 * 100 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "销售金额同比上升率")
    private BigDecimal orderAmtRate;

    //================-------订单欠款信息-------================
    /** 欠款总金额（待回款） */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "欠款总金额")
    private BigDecimal debtTotalAmt;

    /** 总欠款订单数量（待回款）  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "总欠款订单数量")
    private Long debtOrderTotalQty;

    /** 总欠款门店数量（待回款）  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "总欠款门店数量")
    private Long debtBranchTotalQty;

    //================-------订单退单信息-------================
    /** 总退单金额（待处理单据） */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "总退单金额")
    private BigDecimal afterTotalAmt;

    /** 总退单数量（待处理单据）  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "总退单数量")
    private Long afterTotalQty;

    /** 总退单门店数量（待处理单据）  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "总退单门店数量")
    private Long afterBranchTotalQty;

    //================-------月销售信息-------================
    /** 月销售金额 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "月销售金额")
    private BigDecimal monthOrderAmt;

    /** 上月销售金额  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "上月销售金额")
    private BigDecimal beforeMonthOrderAmt;

    /** 月销售金额金额同比上升/下降率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "月销售金额金额同比上升/下降率")
    private BigDecimal monthOrderRate;
}
