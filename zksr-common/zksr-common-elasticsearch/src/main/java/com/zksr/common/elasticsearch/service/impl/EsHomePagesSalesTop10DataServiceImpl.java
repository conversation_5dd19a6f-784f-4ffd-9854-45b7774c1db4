package com.zksr.common.elasticsearch.service.impl;

import com.zksr.common.core.constant.EsIndexNameConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.elasticsearch.domain.EsHomePagesSalesTop10Data;
import com.zksr.common.elasticsearch.mapper.EsHomePagesSalesTop10DataFullMapper;
import com.zksr.common.elasticsearch.mapper.EsHomePagesSkuDataFullMapper;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;
import com.zksr.common.elasticsearch.service.EsHomePagesSalesTop10DataService;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class EsHomePagesSalesTop10DataServiceImpl implements EsHomePagesSalesTop10DataService {
    @Autowired
    private EsHomePagesSalesTop10DataFullMapper esHomePagesSalesTop10DataFullMapper;

    @Override
    public void initIndex() {
        try {
            esHomePagesSalesTop10DataFullMapper.deleteIndex(EsIndexNameConstants.HOME_PAGES_SALES_TOP10_DATA);
        } finally {
        }
        try {
            esHomePagesSalesTop10DataFullMapper.createIndex();
        } finally {
        }
    }

    @Override
    public Boolean saveBatchHomePagesSalesTop10Data(List<EsHomePagesSalesTop10Data> dataList) {
        return esHomePagesSalesTop10DataFullMapper.insertBatch(dataList) > NumberPool.LONG_ZERO;
    }

    @Override
    public Boolean saveHomePagesSalesTop10Data(EsHomePagesSalesTop10Data data) {
        return esHomePagesSalesTop10DataFullMapper.insert(data) > NumberPool.LONG_ZERO;
    }

    @Override
    public List<EsHomePagesSalesTop10Data> searchHomePagesSalesTop10Data(HomePagesSearchDTO searchDTO) {

        LambdaEsQueryWrapper<EsHomePagesSalesTop10Data> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.eq(Objects.nonNull(searchDTO.getSysCode()), EsHomePagesSalesTop10Data::getSysCode, searchDTO.getSysCode())
                .eq(Objects.nonNull(searchDTO.getDcId()), EsHomePagesSalesTop10Data::getDcId, searchDTO.getDcId())
                .eq(Objects.nonNull(searchDTO.getSupplierId()), EsHomePagesSalesTop10Data::getSupplierId, searchDTO.getSupplierId())
                .eq(Objects.nonNull(searchDTO.getSalesType()), EsHomePagesSalesTop10Data::getSalesType, searchDTO.getSalesType())
                .eq(Objects.nonNull(searchDTO.getDateId()), EsHomePagesSalesTop10Data::getDateId, searchDTO.getDateId())
                .ge(Objects.nonNull(searchDTO.getStartDate()), EsHomePagesSalesTop10Data::getDateId, searchDTO.getStartDate())
                .le(Objects.nonNull(searchDTO.getEndDate()), EsHomePagesSalesTop10Data::getDateId, searchDTO.getEndDate())
                .orderByDesc(EsHomePagesSalesTop10Data::getOrderSalesAmt)
                .limit(10)
        ;
        if (Objects.isNull(searchDTO.getSupplierId())) {
            wrapper.eq(EsHomePagesSalesTop10Data::getSupplierId, NumberPool.LOWER_GROUND_LONG);
        }
        if (Objects.isNull(searchDTO.getDcId())) {
            wrapper.eq(EsHomePagesSalesTop10Data::getDcId, NumberPool.LOWER_GROUND_LONG);
        }
        return esHomePagesSalesTop10DataFullMapper.selectList(wrapper);
    }
}
