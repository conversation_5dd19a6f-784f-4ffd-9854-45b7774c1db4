package com.zksr.common.elasticsearch.model.dto;

import lombok.Data;

import java.util.List;

/**
* @Description: 商家门店下单商品信息请求实体类
* @Author: liuxingyu
* @Date: 2024/4/30 8:55
*/
@Data
public class StoreProductDTO {

    /**
     * 唯一ID
     * 规则: CONCAT(branch_id,'_',sku_id,'_',itemType)
     */
    private String id;

    /**
     * ID集合
     */
    private List<String> ids;

    /**
     * 平台商编号
     */
    private Long sysCode;

    /**
     * 门店ID
     */
    private Long branchId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * spuId
     */
    private Long spuId;

    /**
     * 查询类型 1,购买次数 2,最近购买 3,购买数量 4,类别编号
     */
    private Integer sortType;

    /**
     * 商品类别（管理类别编号,三级ID）
     */
    private Long catgoryId;

    /**
     * 商品父类别（管理类别编号,一级ID）
     */
    private Long catgoryFirstId;

    /**
     * 排序 默认0 降序 1升序
     */
    private Integer orderBy;
}
