package com.zksr.common.elasticsearch.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.constant.EsIndexNameConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.math.BigDecimal;
import java.util.Date;

/**
*
 * ES 主页数据 - 订单销售
* <AUTHOR>
* @date 2024/12/19 15:20
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@IndexName(value = EsIndexNameConstants.HOME_PAGES_BRANCH_DATA)
public class EsHomePagesBranchData {

    /**
     * 唯一ID
     * 规则:  CONCAT(20241220 或 202412,'_',sys_code_dcId)
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    /** 平台商id */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("平台商id")
    private Long sysCode;

    /** 运营商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("运营商ID")
    private Long dcId;

    /** 刷新ES时间 */
    @IndexField(fieldType = FieldType.DATE)
    @ApiModelProperty(value = "更新ES时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refreshEsTime;

    /**
     * 规则：20241220 或 202412
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("日期Id")
    private String dateId;

    //================-------订单售后信息-------================

    /** 新增门店数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "新增门店数量")
    private Long branchAddQty;

    /** 上次新增门店数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次新增门店数量")
    private Long beforeBranchAddQty;

    /** 新增门店同比上升/下降率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "新增门店同比上升/下降率")
    private BigDecimal branchAddRate;

    /** 动销门店数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "动销门店数量")
    private Long branchSalesQty;

    /** 上次动销门店数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次动销门店数量")
    private Long beforeBranchSalesQty;

    /** 动销门店数量同比上升/下降率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "动销门店数量同比上升/下降率")
    private BigDecimal branchSalesRate;

    /** 动销率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "动销率")
    private BigDecimal salesRate;

    /** 总计门店数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "总计门店数量")
    private Long branchTotalQty;

    /** 上次总计门店数 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次总计门店数")
    private Long beforeBranchTotalQty;

    /** 总计门店数量同比上升/下降率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "总计门店数量同比上升/下降率")
    private BigDecimal branchTotalRate;

    /** 业务员总计数量  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "业务员总计数量")
    private Long colonelTotalQty;

    /** 拜访门店数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "拜访门店数量")
    private Long visitBranchQty;

    /** 上次拜访门店数量  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次拜访门店数量")
    private Long beforeVisitBranchQty;

    /** 拜访门店数量同比上升/下降率 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "拜访门店数量同比上升/下降率")
    private BigDecimal visitBranchRate;

    /** 拜访门店率 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "拜访门店率")
    private BigDecimal visitRate;

    /** 登录门店数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "登录门店数量")
    private Long loginBranchQty;

    /** 上次登录门店数量  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次登录门店数量")
    private Long beforeLoginBranchQty;

    /** 登录门店数量同比上升/下降率 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "登录门店数量同比上升/下降率")
    private BigDecimal loginBranchRate;
}
