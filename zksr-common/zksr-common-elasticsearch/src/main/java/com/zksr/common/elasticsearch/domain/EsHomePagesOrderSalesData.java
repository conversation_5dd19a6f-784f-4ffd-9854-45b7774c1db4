package com.zksr.common.elasticsearch.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.constant.EsIndexNameConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.math.BigDecimal;
import java.util.Date;

/**
*
 * ES 主页数据 - 订单销售
* <AUTHOR>
* @date 2024/12/19 15:20
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@IndexName(value = EsIndexNameConstants.HOME_PAGES_ORDER_SALES_DATA)
public class EsHomePagesOrderSalesData {

    /**
     * 唯一ID
     * 规则:  CONCAT(20241220 或 202412,'_',sys_code_dcId_supplierId)
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    /** 平台商id */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("平台商id")
    private Long sysCode;

    /** 运营商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("运营商ID")
    private Long dcId;

    /** 入驻商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("入驻商ID")
    private Long supplierId;

    /** 刷新ES时间 */
    @IndexField(fieldType = FieldType.DATE)
    @ApiModelProperty(value = "更新ES时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refreshEsTime;

    /**
     * 规则：20241220 或 202412
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("日期Id")
    private String dateId;

    //================-------订单信息-------================

    /** 订单销售总金额 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "销售订单金额")
    private BigDecimal orderAmt;

    /** 销售订单数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "销售订单数量")
    private Long orderQty;

    /** 销售订单门店数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "销售订单门店数量")
    private Long orderBranchQty;

    /** 销售订单门店平均金额 =（销售订单总金额 / 销售订单门店数量） */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "销售订单门店平均金额")
    private BigDecimal orderBranchAvgAmt;

    /** 欠款总金额（待回款） */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "欠款金额")
    private BigDecimal debtOrderAmt;

    /** 欠款订单数量（待回款）  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "欠款订单数量")
    private Long debtOrderQty;

    /** 欠款门店数量（待回款）  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "欠款门店数量")
    private Long debtOrderBranchQty;

    /** 上次销售订单金额 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次销售订单金额")
    private BigDecimal beforeOrderAmt;

    /** 上次销售订单数量  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次销售订单数量")
    private Long beforeOrderQty;

    /** 上次销售订单门店数量  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次销售订单门店数量")
    private Long beforeOrderBranchQty;

    /** 上次销售订单门店数量  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "上次销售订单门店平均金额")
    private BigDecimal beforeOrderBranchAvgAmt;

    /** 订单金额同比上升/下降率 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "订单金额同比上升/下降率")
    private BigDecimal orderAmtRate;

    /** 订单数量同比上升/下降率 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "订单数量同比上升/下降率")
    private BigDecimal orderQtyRate;

    /** 销售订单门店数量同比上升/下降率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "销售订单门店数量同比上升/下降率")
    private BigDecimal orderBranchQtyRate;

    /** 销售订单门店平均金额同比上升/下降率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "销售订单门店平均金额同比上升/下降率")
    private BigDecimal orderBranchAmtAvgRate;

    /** 动销SKU数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "动销SKU数量")
    private Long orderSkuQty;

    /** 上次动销SKU数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次动销SKU数量")
    private Long beforeOrderSkuQty;

    /** 动销SKU数量同比上升/下降率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "动销SKU数量同比上升/下降率")
    private BigDecimal orderSkuRate;
}
