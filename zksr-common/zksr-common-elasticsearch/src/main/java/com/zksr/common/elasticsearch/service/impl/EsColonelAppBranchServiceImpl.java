package com.zksr.common.elasticsearch.service.impl;

import com.zksr.common.core.constant.EsIndexNameConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsColonelAppBranch;
import com.zksr.common.elasticsearch.mapper.EsColonelAppBranchFullMapper;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.common.elasticsearch.service.EsColonelAppBranchService;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.elasticsearch.common.geo.GeoDistance;
import org.elasticsearch.common.geo.GeoPoint;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.search.sort.GeoDistanceSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import static org.dromara.easyes.common.constants.BaseEsConstants.DEFAULT_BOOST;

/**
*
 * 业务员APP 门店统计信息 操作
* <AUTHOR>
* @date 2024/5/9 9:53
*/
@Service
public class EsColonelAppBranchServiceImpl implements EsColonelAppBranchService {

    @Resource
    private EsColonelAppBranchFullMapper colonelAppBranchFullMapper;

    @Override
    public void initIndex() {
        try {colonelAppBranchFullMapper.deleteIndex(EsIndexNameConstants.COLOENL_APP_BRANCH);} finally {}
        try {colonelAppBranchFullMapper.createIndex();} finally {}
    }

    @Override
    public Boolean saveBatchColonelAppBranch(List<EsColonelAppBranch> branchList) {
        return colonelAppBranchFullMapper.insertBatch(branchList) > BigDecimal.ZERO.intValueExact();
    }

    @Override
    public Boolean saveColonelAppBranch(EsColonelAppBranch branch) {
        return colonelAppBranchFullMapper.insert(branch) > BigDecimal.ZERO.intValueExact();
    }

    @Override
    public List<EsColonelAppBranch> seachColonelAppBranch(ColonelAppBranchDTO branchDTO) {
        LambdaEsQueryWrapper<EsColonelAppBranch> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.eq(Objects.nonNull(branchDTO.getBranchId()),EsColonelAppBranch::getBranchId,branchDTO.getBranchId())
                .in(Objects.nonNull(branchDTO.getBranchIds()),EsColonelAppBranch::getBranchId,branchDTO.getBranchIds())
                .in(Objects.nonNull(branchDTO.getIds()) && !branchDTO.getIds().isEmpty(), EsColonelAppBranch::getId, branchDTO.getIds())
                .eq(Objects.nonNull(branchDTO.getSysCode()),EsColonelAppBranch::getSysCode,branchDTO.getSysCode());
        wrapper.orderByDesc(EsColonelAppBranch::getLastBuyTime);
        return colonelAppBranchFullMapper.selectList(wrapper);
    }

    @Override
    public PageResult<EsColonelAppBranch> searchColonelAppBranchPage(ColonelAppBranchDTO search) {
        LambdaEsQueryWrapper<EsColonelAppBranch> wrapper = new LambdaEsQueryWrapper<>();
        // branchId 搜索
        wrapper.eq(Objects.nonNull(search.getBranchId()),EsColonelAppBranch::getBranchId,search.getBranchId());
        wrapper.eq(Objects.nonNull(search.getAreaId()),EsColonelAppBranch::getAreaId,search.getAreaId());
        // branchId 集合检索
        wrapper.in(Objects.nonNull(search.getBranchIds()),EsColonelAppBranch::getBranchId,search.getBranchIds());
        // id 组合唯一键 集合检索
        wrapper.in(Objects.nonNull(search.getIds()) && !search.getIds().isEmpty(), EsColonelAppBranch::getId, search.getIds());
        // sysCode 隔离
        wrapper.eq(Objects.nonNull(search.getSysCode()),EsColonelAppBranch::getSysCode,search.getSysCode());
        // 数据月份
        wrapper.eq(StringUtils.isNotEmpty(search.getCreateYearMonth()), EsColonelAppBranch::getCreateYearMonth, search.getCreateYearMonth());
        // 业务员ID
        if (Objects.nonNull(search.getColonelId())) {
            wrapper.eq(EsColonelAppBranch::getColonelId, search.getColonelId());
        } else if (Objects.nonNull(search.getColonelIds())) {
            wrapper.in(EsColonelAppBranch::getColonelId, search.getColonelIds());
        }
        if (Objects.nonNull(search.getIsOpenSeas()) && 2 == search.getIsOpenSeas()){
            wrapper.not().exists(EsColonelAppBranch::getColonelId);
        }
        // 关键字更新
        // 数据月份
        if (StringUtils.isNotEmpty(search.getKeyword())) {
            wrapper.and(and -> {
                and.like("keyword.keyword", search.getKeyword()) // 如果是分词类型
                   .or()
                   .like(EsColonelAppBranch::getKeyword, search.getKeyword()) // 如果是普通类型
                ;
            });
        }
        // 排序
        if ( Objects.nonNull(search.getSortField()) ) {
            // 最近下单时间
            if ( 1 == search.getSortField()) {
                if (StringPool.ASC.equals(search.getSort())) {
                    wrapper.orderByAsc(EsColonelAppBranch::getLastBuyTime);
                } else {
                    wrapper.orderByDesc(EsColonelAppBranch::getLastBuyTime);
                }
            }

            // 距离排序
            if ( 2 == search.getSortField() && Objects.nonNull(search.getLat())) {
                // 查询以纬度为41.0,经度为115.0为圆心,半径90公里内的所有点
                // 其中单位可以省略,默认为km
                GeoDistanceSortBuilder gro = SortBuilders.geoDistanceSort("location", new GeoPoint(search.getLat(), search.getLon()));
                // 单位， 公制/米
                gro.unit(DistanceUnit.METERS);
                // 精度， 略差， 效率高
                gro.geoDistance(GeoDistance.PLANE);
                //排序
                if (StringPool.ASC.equals(search.getSort())) {
                    gro.order(SortOrder.ASC);
                } else {
                    gro.order(SortOrder.DESC);
                }

                wrapper.sort(gro);
            }

            // 本月订单金额
            if ( 3 == search.getSortField()) {
                if (StringPool.ASC.equals(search.getSort())) {
                    wrapper.orderByAsc(EsColonelAppBranch::getNowMonthOrderAmt);
                } else {
                    wrapper.orderByDesc(EsColonelAppBranch::getNowMonthOrderAmt);
                }
            }
            // 最近下单订单金额
            if ( 4 == search.getSortField()) {
                if (StringPool.ASC.equals(search.getSort())) {
                    wrapper.orderByAsc(EsColonelAppBranch::getLastBuyAmt);
                } else {
                    wrapper.orderByDesc(EsColonelAppBranch::getLastBuyAmt);
                }
            }
        }
        // 分页获取数据
        EsPageInfo<EsColonelAppBranch> esPageInfo = colonelAppBranchFullMapper.pageQuery(wrapper, search.getPageNo(), search.getPageSize());
        return new PageResult<>(esPageInfo.getList(), esPageInfo.getTotal());
    }

    @Override
    public EsColonelAppBranch seachColonelAppBranchById(String id) {
        return colonelAppBranchFullMapper.selectById(id);
    }

    @Override
    public Integer deleteColonelAppBranch(ColonelAppBranchDTO search) {
        LambdaEsQueryWrapper<EsColonelAppBranch> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.eq(Objects.nonNull(search.getBranchId()),EsColonelAppBranch::getBranchId,search.getBranchId())
                .in(Objects.nonNull(search.getBranchIds()),EsColonelAppBranch::getBranchId,search.getBranchIds())
                .in(Objects.nonNull(search.getIds()) && !search.getIds().isEmpty(), EsColonelAppBranch::getId, search.getIds())
                .eq(Objects.nonNull(search.getSysCode()),EsColonelAppBranch::getSysCode,search.getSysCode())
                ;
        return colonelAppBranchFullMapper.delete(wrapper);
    }
}
