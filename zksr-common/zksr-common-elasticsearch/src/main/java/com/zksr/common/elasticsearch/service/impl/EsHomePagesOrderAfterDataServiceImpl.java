package com.zksr.common.elasticsearch.service.impl;

import com.zksr.common.core.constant.EsIndexNameConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.elasticsearch.domain.EsHomePagesOrderAfterData;
import com.zksr.common.elasticsearch.mapper.EsHomePagesOrderAfterDataFullMapper;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;
import com.zksr.common.elasticsearch.service.EsHomePagesOrderAfterDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class EsHomePagesOrderAfterDataServiceImpl implements EsHomePagesOrderAfterDataService {
    @Autowired
    private EsHomePagesOrderAfterDataFullMapper homePagesOrderAfterDataFullMapper;
    @Override
    public void initIndex() {
        try {homePagesOrderAfterDataFullMapper.deleteIndex(EsIndexNameConstants.HOME_PAGES_ORDER_AFTER_DATA);} finally {}
        try {homePagesOrderAfterDataFullMapper.createIndex();} finally {}
    }

    @Override
    public Boolean saveBatchHomePagesOrderAfterData(List<EsHomePagesOrderAfterData> dataList) {
        return homePagesOrderAfterDataFullMapper.insertBatch(dataList) > NumberPool.INT_ZERO;
    }

    @Override
    public Boolean saveHomePagesOrderAfterData(EsHomePagesOrderAfterData data) {
        return homePagesOrderAfterDataFullMapper.insert(data) > NumberPool.INT_ZERO;
    }

    @Override
    public EsHomePagesOrderAfterData searchHomePagesOrderAfterData(HomePagesSearchDTO searchDTO) {
        return homePagesOrderAfterDataFullMapper.selectById(searchDTO.getId());
    }
}
