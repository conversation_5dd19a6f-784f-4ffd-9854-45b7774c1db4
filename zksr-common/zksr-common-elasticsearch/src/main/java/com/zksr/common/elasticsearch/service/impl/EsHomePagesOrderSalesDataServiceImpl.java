package com.zksr.common.elasticsearch.service.impl;

import com.zksr.common.core.constant.EsIndexNameConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.elasticsearch.domain.EsHomePagesOrderSalesData;
import com.zksr.common.elasticsearch.mapper.EsHomePagesOrderSalesDataFullMapper;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;
import com.zksr.common.elasticsearch.service.EsHomePagesOrderSalesDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EsHomePagesOrderSalesDataServiceImpl implements EsHomePagesOrderSalesDataService {
    @Autowired
    private EsHomePagesOrderSalesDataFullMapper homePagesOrderSalesDataFullMapper;
    @Override
    public void initIndex() {
        try {homePagesOrderSalesDataFullMapper.deleteIndex(EsIndexNameConstants.HOME_PAGES_ORDER_SALES_DATA);} finally {}
        try {homePagesOrderSalesDataFullMapper.createIndex();} finally {}
    }

    @Override
    public Boolean saveBatchHomePagesOrderSalesData(List<EsHomePagesOrderSalesData> dataList) {
        return homePagesOrderSalesDataFullMapper.insertBatch(dataList) > NumberPool.INT_ZERO;
    }

    @Override
    public Boolean saveHomePagesOrderSalesData(EsHomePagesOrderSalesData data) {
        return homePagesOrderSalesDataFullMapper.insert(data) > NumberPool.INT_ZERO;
    }

    @Override
    public EsHomePagesOrderSalesData searchHomePagesOrderSalesData(HomePagesSearchDTO searchDTO) {
        return homePagesOrderSalesDataFullMapper.selectById(searchDTO.getId());
    }
}
