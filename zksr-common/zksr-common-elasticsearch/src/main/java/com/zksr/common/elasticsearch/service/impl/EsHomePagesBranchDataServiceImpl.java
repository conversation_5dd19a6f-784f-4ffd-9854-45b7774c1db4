package com.zksr.common.elasticsearch.service.impl;

import com.zksr.common.core.constant.EsIndexNameConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.elasticsearch.domain.EsHomePagesBranchData;
import com.zksr.common.elasticsearch.mapper.EsHomePagesBranchDataFullMapper;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;
import com.zksr.common.elasticsearch.service.EsHomePagesBranchDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EsHomePagesBranchDataServiceImpl implements EsHomePagesBranchDataService {
    @Autowired
    private EsHomePagesBranchDataFullMapper homePagesBranchDataFullMapper;
    @Override
    public void initIndex() {
        try {homePagesBranchDataFullMapper.deleteIndex(EsIndexNameConstants.HOME_PAGES_BRANCH_DATA);} finally {}
        try {homePagesBranchDataFullMapper.createIndex();} finally {}
    }

    @Override
    public Boolean saveBatchHomePagesBranchData(List<EsHomePagesBranchData> dataList) {
        return homePagesBranchDataFullMapper.insertBatch(dataList) > NumberPool.INT_ZERO;
    }

    @Override
    public Boolean saveHomePagesBranchData(EsHomePagesBranchData data) {
        return homePagesBranchDataFullMapper.insert(data) > NumberPool.INT_ZERO;
    }

    @Override
    public EsHomePagesBranchData searchHomePagesBranchData(HomePagesSearchDTO searchDTO) {
        return homePagesBranchDataFullMapper.selectById(searchDTO.getId());
    }
}
