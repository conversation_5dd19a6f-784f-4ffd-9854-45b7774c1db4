package com.zksr.common.elasticsearch.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsYhProduct;
import com.zksr.common.elasticsearch.model.dto.YhProductSearchDTO;
import com.zksr.common.elasticsearch.model.vo.YhSupplierSaleClassVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2024/12/11
 * @desc
 */
public interface EsYhProductService {

    void insertBatch(List<EsYhProduct> esYhProducts);

    Map<Long, List<Long>> getBatchSupplierSaleClass(Date batchDate, Long branchId);

    PageResult<EsYhProduct> pageList(YhProductSearchDTO yhProductSearchDTO);

    List<EsYhProduct> getYhProductByBranchItemIdList(Long branchId, Date batchDate, List<Long> itemIdList);

    List<EsYhProduct> getYhProductByBranchSkuIdList(Long branchId, Date batchDate, List<Long> skuIdList);

    void deleteById(List<Long> deleteYhIds);

    void updateBatch(List<EsYhProduct> esYhProducts);

    void createIndex();

}
