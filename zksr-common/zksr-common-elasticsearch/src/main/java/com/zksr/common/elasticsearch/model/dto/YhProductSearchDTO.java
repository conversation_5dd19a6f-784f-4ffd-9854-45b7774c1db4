package com.zksr.common.elasticsearch.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/12 14:16
 */
@Data
public class YhProductSearchDTO extends PageParam {

    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "批次日期")
    private Date batchDate;

    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    @ApiModelProperty(value = "三级展示分类ID")
    private Long threeSaleClassId;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty("是否选中, 1-选中, 2-未选中")
    private Integer checked;

    @ApiModelProperty("0-全量, 1-仅计算数据")
    private Integer searchMode = 0;
}
