package com.zksr.common.elasticsearch.model.dto;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class HomePagesSearchDTO extends PageParam {

    /**
     * id
     */
    @ApiModelProperty("id")
    private String id;

    /**
     * 平台商id
     */
    @ApiModelProperty("平台商id")
    private Long sysCode;

    /**
     * 运营商Id
     */
    @ApiModelProperty("运营商Id")
    private Long dcId;

    /**
     * 入驻商Id
     */
    @ApiModelProperty("入驻商Id")
    private Long supplierId;

    /**
     * 销售TOP10类型（区域：area，门店：branch，一级品类：category，运营商：dc，商品：itme，入驻商：supplier，业务员：colonel）
     */
    @ApiModelProperty(value = "销售TOP10类型")
    private String salesType;

    /**
     * 时间ID
     */
    @ApiModelProperty("时间ID")
    private String dateId;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;
}
