package com.zksr.common.elasticsearch.domain;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.constant.EsIndexNameConstants;
import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Score;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 本地全国商品
 * @date 2024/3/1 11:27
 */
@IndexName(value = EsIndexNameConstants.PRODUCT_GROUP)
@Data
public class EsProductGroup extends EsProduct{
    /**
     * 唯一ID
     * 规则: EsProductType_areaId_spuId
     * */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("促销活动ID")
    private Long activityId;

    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("组合商品活动开始时间, 时间毫秒值")
    private Long activityStartTime = DateUtil.parseDateTime("2001-01-01 00:00:00").getTime();

    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("组合商品活动结束时间, 时间毫秒值")
    private Long activityEndTime = DateUtil.parseDateTime("2199-01-01 00:00:00").getTime();

    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("组合促销商品ID")
    private Long spuCombineId;

    @Score
    private Float score;

    /*@IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("门店白名单列表")
    private List<Long> whiteBranchList = ListUtil.toList(NumberPool.LOWER_GROUND_LONG);

    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("门店黑名单列表")
    private List<Long> blackBranchList;

    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("渠道白名单列表")
    private List<Long> whiteChannelList = ListUtil.toList(NumberPool.LOWER_GROUND_LONG);

    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("渠道黑名单列表")
    private List<Long> blackChannelList;*/
}
