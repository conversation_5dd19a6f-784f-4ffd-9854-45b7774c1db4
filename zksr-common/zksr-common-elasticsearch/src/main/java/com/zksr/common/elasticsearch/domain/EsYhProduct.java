package com.zksr.common.elasticsearch.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.constant.EsIndexNameConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;
import org.dromara.easyes.annotation.rely.RefreshPolicy;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 要货数据
 * @date 2024/12/10 15:02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@IndexName(value = EsIndexNameConstants.YH_DATA, refreshPolicy = RefreshPolicy.IMMEDIATE)
public class EsYhProduct implements Serializable {

    @IndexId(type = IdType.CUSTOMIZE)
    @ApiModelProperty("要货ID")
    private Long yhId;

    @ApiModelProperty("本地上架商品")
    @IndexField(fieldType = FieldType.LONG)
    private Long areaItemId;

    @ApiModelProperty("门店ID")
    @IndexField(fieldType = FieldType.LONG)
    public Long branchId;

    @ApiModelProperty("入驻商ID")
    @IndexField(fieldType = FieldType.LONG)
    private Long supplierId;

    @ApiModelProperty(value = "三级展示分类ID")
    @IndexField(fieldType = FieldType.LONG)
    private Long threeSaleClassId;

    @ApiModelProperty("要货数量")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer posSuggestQty;

    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("要货单位,1-小单位,2-中单位,3-大单位")
    private Integer mallUnitType;

    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("要货年月日批次")
    private Integer batchYmd;

    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("是否选中, 1-选中, 2-未选中")
    private Integer checked;

    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("POS国条码")
    private String posBarcode;

    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("昨日销量")
    private Integer posSalesQty;

    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("POS单位名称")
    private String posUnitName;

    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("30天人均销量")
    private Integer pos30dayAvgSales;

    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("安全库存天数")
    private Integer posSafetyDays;

    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("安全库存")
    private Integer posSafetyStock;

    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("已下单, 未完成数量, 在途数量")
    private Long transitQty;

    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("skuId")
    private Long skuId;

    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("spuId")
    private Long spuId;

    @IndexField(fieldType = FieldType.TEXT)
    @ApiModelProperty("商品名称")
    private String spuName;

    /** 上次补货时间 */
    @ApiModelProperty("上次补货时间")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String lastTime;

    /** 上次补货数量 */
    @ApiModelProperty("上次补货数量")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer lastSubmitQty;

    /** 原始要货数量 */
    @ApiModelProperty("原始要货数量")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer sourceYhQty;

    /** 0-普通商品,1-组合商品 */
    @ApiModelProperty("0-普通商品,1-组合商品")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer itemType;

    @JsonFormat
    public boolean spuCombine() {
        return Objects.nonNull(itemType) && itemType == NumberPool.INT_ONE;
    }

    @JsonFormat
    public boolean notSpuCombine() {
        return Objects.nonNull(itemType) && itemType == NumberPool.INT_ZERO;
    }

    @JsonIgnore
    public String unitKey() {
        return StringUtils.format("{}:{}", this.getAreaItemId(), this.getMallUnitType());
    }

    @JsonIgnore
    public String unitSkuKey() {
        return StringUtils.format("{}:{}", this.getSkuId(), this.getMallUnitType());
    }
}
