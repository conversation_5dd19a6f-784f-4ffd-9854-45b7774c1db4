package com.zksr.common.elasticsearch.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.rely.FieldType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ColonelAppBranchDTO extends PageParam {

    /** 门店ID */
    @ApiModelProperty("门店ID")
    private Long branchId;

    /** 平台商id */
    @ApiModelProperty("平台商id")
    private Long sysCode;

    /** 门店ID列表 */
    @ApiModelProperty("门店ID列表 */")
    private List<Long> branchIds;

    /** 业务员ID */
    @ApiModelProperty("业务员ID")
    private List<Long> colonelIds;

    /** 排序字段 */
    @ApiModelProperty(value = "排序字段, 1-最近下单时间, 2-距离排序, 3-本月订单金额，4-最近下单金额")
    private Integer sortField;

    /** 排序字段 */
    @ApiModelProperty(value = "排序类型, asc-升序, des-降序")
    private String sort;

    /**
     * 刷新ES 类型 1、客户登陆，更新登陆时间 2、下单完成 更新订单信息 3、业务员签到 更新拜访时间
     */
    private Integer type;

    /** 订单金额 */
    @ApiModelProperty("本月订单金额")
    private BigDecimal orderAmt;

    /** 订单状态  0 取消 1 支付*/
    @ApiModelProperty("订单状态")
    private Integer orderStatus;

    private List<String> ids;

    @ApiModelProperty("数据月份")
    private String createYearMonth;

    @ApiModelProperty(value = "当前业务员纬度坐标, 计算相对距离必须传入")
    private Double lat;

    @ApiModelProperty(value = "当前业务员精度坐标, 计算相对距离必须传入")
    private Double lon;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    @ApiModelProperty("公海查询 0 查询所有 1查询普通 2查询公海")
    private Integer isOpenSeas;

    /** 门店名称 */
    @Excel(name = "搜索关键字 客户、联系人、地址")
    @ApiModelProperty(value = "搜索关键字 客户、联系人、地址")
    private String keyword;

    @ApiModelProperty("区域id")
    private Long areaId;

    public ColonelAppBranchDTO(Long branchId, Long sysCode) {
        this.branchId = branchId;
        this.sysCode = sysCode;
    }

    public ColonelAppBranchDTO(Long branchId, Long sysCode, BigDecimal orderAmt, Integer orderStatus) {
        this.branchId = branchId;
        this.sysCode = sysCode;
        this.orderAmt = orderAmt;
        this.orderStatus = orderStatus;
    }

    public ColonelAppBranchDTO(Long sysCode, List<Long> branchIds) {
        this.sysCode = sysCode;
        this.branchIds = branchIds;
    }

    public ColonelAppBranchDTO(List<String> ids) {
        this.ids = ids;
    }
}
