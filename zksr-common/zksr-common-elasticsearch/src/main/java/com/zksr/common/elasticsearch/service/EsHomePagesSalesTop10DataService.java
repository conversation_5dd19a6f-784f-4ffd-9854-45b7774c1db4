package com.zksr.common.elasticsearch.service;

import com.zksr.common.elasticsearch.domain.EsHomePagesSalesTop10Data;
import com.zksr.common.elasticsearch.domain.EsHomePagesSkuData;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;

import java.util.List;

/**
*
 *
* <AUTHOR>
* @date 2024/12/24 9:53
*/
public interface EsHomePagesSalesTop10DataService {

    /**
     * 初始化索引
     */
    void initIndex();

    /**
     * 批量保存信息
     * @param homePagesDataList
     * @return
     */
    Boolean saveBatchHomePagesSalesTop10Data(List<EsHomePagesSalesTop10Data> dataList);

    /**
     * 保存信息
     * @param homePagesData
     * @return
     */
    Boolean saveHomePagesSalesTop10Data(EsHomePagesSalesTop10Data data);

    /**
     * 查询ES信息
     * @param searchDTO
     * @return
     */
    List<EsHomePagesSalesTop10Data> searchHomePagesSalesTop10Data(HomePagesSearchDTO searchDTO);


}
