package com.zksr.common.elasticsearch.model.dto;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品数据搜索
 * @date 2024/3/2 16:19
 */
@Data
public class ProductSearchDTO extends PageParam {

    /**
     * 商品类型 com.zksr.common.core.enums.ProductType
     */
    @ApiModelProperty("商品类型 local-本地, global-全国, group-全部")
    private String productType;

    /**
     * 关键字条件
     */
    @ApiModelProperty("关键字条件")
    private String condition;

    /**
     * 排序类型
     */
    @ApiModelProperty("排序类型: none-无, sale-销量, price-价格")
    private String sortType = StringPool.NONE;

    /**
     * 排序方式
     */
    @ApiModelProperty("排序方式: none-无,des-降序, asc-升序")
    private String orderBy = StringPool.NONE;

    /**
     * 兼容全国和本地商品售后
     */
    @ApiModelProperty("区域ID")
    private Long areaId;

    /**
     * 本地渠道
     */
    @ApiModelProperty("渠道ID")
    private Long channelId;

    /**
     * 平台商城市分组id
     */
    @ApiModelProperty("平台城市分组ID")
    private Long groupId;

    /**
     * 平台商ID
     */
    @ApiModelProperty("平台商ID")
    private Long sysCode;

    /**
     * 入驻商ID
     */
    @ApiModelProperty("入驻商ID")
    private List<Long> supplierId;

    @ApiModelProperty("价格方案码: 0-6")
    private Integer priceCode;

    /**
     * 展示三级分类ID
     */
    @ApiModelProperty("展示三级分类ID")
    private List<Long> class3Id;

    /**
     * 展示二级分类ID
     */
    @ApiModelProperty("展示二级分类ID")
    private List<Long> class2Id;

    /**
     * 展示一级分类ID
     */
    @ApiModelProperty("展示一级分类ID")
    private List<Long> class1Id;

    /**
     * 管理分类
     */
    @ApiModelProperty("catgoryId")
    private List<Long> catgoryId;

    /**
     * 黑名单管理分类
     */
    @ApiModelProperty("黑名单管理分类")
    private List<Long> blackCatgoryId;

    /**
     * 品牌ID
     */
    @ApiModelProperty("品牌ID")
    private List<Long> brandId;

    /**
     * 黑名单品牌ID
     */
    @ApiModelProperty("黑名单品牌ID")
    private List<Long> blackBrandId;

    /**
     * skuId
     */
    @ApiModelProperty("skuId")
    private List<Long> skuId;


    /**
     * skuId
     */
    @ApiModelProperty("spuId")
    private List<Long> spuId;

    /**
     * 城市上架ID集合
     */
    @ApiModelProperty("城市上架ID集合")
    private List<Long> areaItemIds;

    /**
     * 上架ID集合
     */
    @ApiModelProperty("上架ID集合")
    private List<Long> itemIds;

    /**
     * 屏蔽的SKU集合
     */
    @ApiModelProperty("屏蔽的SKU集合")
    private List<Long> blockSkus;

    /**
     * 额外的组合促销活动ID
     */
    @ApiModelProperty("额外的组合促销活动ID")
    private List<Long> orActivityId;

    /**
     * 必须的activityId
     */
    @ApiModelProperty("必须的activityId")
    private List<Long> activityIdList;

    /**
     * 是否根据sku去重
     */
    @ApiModelProperty("是否根据sku去重")
    private boolean distinctBySkuId;

    /**
     * 是否根据spu去重
     */
    @ApiModelProperty("是否根据spu去重")
    private boolean distinctBySpuId;

    /**
     * 管理分类搜索推荐指数
     */
    @ApiModelProperty(value = "管理分类推荐指数")
    private List<SearchRecommendRate> categoryRecommendRateList;

    /**
     * 是否还有库存,1-没有库存,0-有库存的
     */
    @ApiModelProperty("是否还有库存,1-没有库存,0-有库存的")
    private Integer notStock;

    /**
     * 隐藏没库存多少天的商品
     */
    @ApiModelProperty("隐藏没库存多少天的商品")
    private Integer hideNoStockProductDay;

    /**
     * 支持负库存的入驻商ID列表
     */
    @ApiModelProperty("支持负库存的入驻商ID列表")
    private List<Long> negativeStockSupplierIds;

    @ApiModelProperty("最小价")
    private BigDecimal minPrice;

    @ApiModelProperty("最高价")
    private BigDecimal maxPrice;
}
