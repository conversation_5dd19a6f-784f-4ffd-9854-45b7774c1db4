package com.zksr.common.elasticsearch.service.impl;

import com.zksr.common.core.constant.EsIndexNameConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.elasticsearch.domain.EsHomePagesData;
import com.zksr.common.elasticsearch.mapper.EsHomePagesDataFullMapper;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;
import com.zksr.common.elasticsearch.service.EsHomePagesDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EsHomePagesDataServiceImpl implements EsHomePagesDataService {

    @Autowired
    private EsHomePagesDataFullMapper homePagesDataFullMapper;
    @Override
    public void initIndex() {
        try {homePagesDataFullMapper.deleteIndex(EsIndexNameConstants.HOME_PAGES_DATA);} finally {}
        try {homePagesDataFullMapper.createIndex();} finally {}
    }

    @Override
    public Boolean saveBatchHomePagesData(List<EsHomePagesData> homePagesDataList) {
        return homePagesDataFullMapper.insertBatch(homePagesDataList) > NumberPool.INT_ZERO;
    }

    @Override
    public Boolean saveHomePagesData(EsHomePagesData homePagesData) {
        return homePagesDataFullMapper.insert(homePagesData) > NumberPool.INT_ZERO;
    }

    @Override
    public EsHomePagesData searchHomePagesData(HomePagesSearchDTO searchDTO) {
        return homePagesDataFullMapper.selectById(searchDTO.getId());
    }


}
