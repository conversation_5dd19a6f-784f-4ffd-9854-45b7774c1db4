package com.zksr.common.elasticsearch.service;

import com.zksr.common.elasticsearch.domain.EsHomePagesOrderSalesData;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;

import java.util.List;

/**
*
 *
* <AUTHOR>
* @date 2024/5/9 9:53
*/
public interface EsHomePagesOrderSalesDataService {

    /**
     * 初始化索引
     */
    void initIndex();

    /**
     * 批量保存信息
     * @param homePagesDataList
     * @return
     */
    Boolean saveBatchHomePagesOrderSalesData(List<EsHomePagesOrderSalesData> dataList);

    /**
     * 保存信息
     * @param homePagesData
     * @return
     */
    Boolean saveHomePagesOrderSalesData(EsHomePagesOrderSalesData data);

    /**
     * 查询ES信息
     * @param searchDTO
     * @return
     */
    EsHomePagesOrderSalesData searchHomePagesOrderSalesData(HomePagesSearchDTO searchDTO);


}
