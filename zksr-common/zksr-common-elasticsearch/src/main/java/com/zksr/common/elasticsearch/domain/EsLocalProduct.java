package com.zksr.common.elasticsearch.domain;

import com.zksr.common.core.constant.EsIndexNameConstants;
import lombok.Data;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.IdType;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 本地商品索引上架商品
 * @date 2024/2/29 17:57
 */
@IndexName(value = EsIndexNameConstants.LOCAL_PRODUCT)
@Data
public class EsLocalProduct extends EsProduct{
    /**
     * 唯一ID
     * 规则: CONCAT(pai.area_item_id, '_', pai.area_id, '_', pcac.channel_id)
     * */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;
}
