package com.zksr.common.elasticsearch.service;

import com.zksr.common.core.web.pojo.PageParam;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.*;
import com.zksr.common.elasticsearch.model.dto.ProductSearchDTO;
import com.zksr.common.elasticsearch.model.dto.StoreProductDTO;
import org.dromara.easyes.core.biz.EsPageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/3/1
 * @desc
 */
public interface EsProductService {

    /**
     * 保存本地商品, 按照上架最小颗粒度
     *
     * @param list
     * @return
     */
    Boolean saveLocalFull(List<EsLocalProduct> list);

    /**
     * 保存全国商品, 按照上架最小颗粒度
     *
     * @param list
     * @return
     */
    Boolean saveGlobalFull(List<EsGlobalProduct> list);

    /**
     * 保存全国本地商品合集
     *
     * @param list
     * @return
     */
    Boolean saveProduct(List<EsProductGroup> list);

    void initIndex();

    /**
     * group SPU 唯一搜索
     *
     * @param convert
     * @return
     */
    EsPageInfo<EsProductGroup> search(ProductSearchDTO convert, boolean needMinScore);


    /**
     * 不分页查所有
     *
     * @param convert 搜索条件
     * @return List<EsProductGroup>
     */
    List<EsProductGroup> searchAll(ProductSearchDTO convert);
    
    /**
     * 不分页查所有 + 最小分数
     * @return List<EsProductGroup>
     */
    List<EsProductGroup> searchAll(ProductSearchDTO searchDto, boolean needMinScore);
    
    /**
     * 搜索本地全部商品
     */
    EsPageInfo<EsLocalProduct> searchLocalFull(ProductSearchDTO convert);

    /**
     * 搜索全国全部商品
     */
    EsPageInfo<EsGlobalProduct> searchGlobalFull(ProductSearchDTO convert);

    /**
     * 城市上架ID 搜索
     *
     * @param convert
     * @return
     */
    List<EsLocalProduct> searchAreaItemList(ProductSearchDTO convert);

    /**
     * @Description: 保存门店下单商品信息
     * @Author: liuxingyu
     * @Date: 2024/4/29 10:05
     */
    Boolean saveStoreProduct(List<EsStoreProduct> storeProductList);

    /**
     * @Description: 获取门店下单商品信息
     * @Author: liuxingyu
     * @Date: 2024/4/29 16:38
     */
    PageResult<EsStoreProduct> getEsStoreProductList(StoreProductDTO storeProductDTO, PageParam pageParam);

    /**
     * 获取全量列表数据
     *
     * @param searchDTO
     * @return
     */
    List<EsProduct> searchFullList(ProductSearchDTO searchDTO);

    /**
     * 删除商品
     *
     * @param searchDTO 匹配条件
     */
    void deleteByEvent(ProductSearchDTO searchDTO);

    /**
     * 城市上架的三级展示分类数据
     */
    List<Long> getAreaItemReleaseThreeAreaClassList(Long areaId);

    /**
     * 城市上架的三级管理分类数据
     */
    List<Long> getAreaItemReleaseThreeCategoryList(Long areaId);

    /**
     * 全国上架的三级展示分类数据
     */
    List<Long> getGloablItemReleaseThreeClassList(Long sysCode);

    /**
     * 全国上架的三级管理分类数据
     */
    List<Long> getGloablItemReleaseThreeCategoryList(Long sysCode);
    
    public EsPageInfo<EsProductGroup> testSpuDetailList(String name,Long sysCode);
}
