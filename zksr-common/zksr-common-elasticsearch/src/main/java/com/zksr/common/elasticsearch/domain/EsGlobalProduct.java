package com.zksr.common.elasticsearch.domain;

import com.zksr.common.core.constant.EsIndexNameConstants;
import lombok.Data;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.IdType;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 全国商品索引
 * @date 2024/2/29 17:57
 */
@IndexName(value = EsIndexNameConstants.GLOBAL_PRODUCT)
@Data
public class EsGlobalProduct extends EsProduct{

    /**
     * 唯一ID
     * 规则: CONCAT(pai.supplier_item_id, '_', pgsc.group_id)
     * */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;
}
