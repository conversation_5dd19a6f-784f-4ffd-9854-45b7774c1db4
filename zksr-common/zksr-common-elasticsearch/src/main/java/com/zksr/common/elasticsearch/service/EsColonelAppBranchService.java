package com.zksr.common.elasticsearch.service;


import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsColonelAppBranch;
import com.zksr.common.elasticsearch.domain.EsStoreProduct;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;

import java.util.List;

/**
*
 *
* <AUTHOR>
* @date 2024/5/9 9:53
*/
public interface EsColonelAppBranchService {

    /**
     * 初始化索引
     */
    void initIndex();

    /**
     * 批量保存门店统计信息
     * @param branchList
     * @return
     */
    Boolean saveBatchColonelAppBranch(List<EsColonelAppBranch> branchList);

    /**
     * 批量保存门店统计信息
     * @param branch
     * @return
     */
    Boolean saveColonelAppBranch(EsColonelAppBranch branch);

    /**
     * 查询门店统计信息
     * @param branchDTO
     * @return
     */
    List<EsColonelAppBranch> seachColonelAppBranch(ColonelAppBranchDTO branchDTO);

    /**
     * 分页获取ES门店数据
     * @param search
     * @return
     */
    PageResult<EsColonelAppBranch> searchColonelAppBranchPage(ColonelAppBranchDTO search);

    /**
     * 查询单个门店统计信息
     * @param id
     * @return
     */
    EsColonelAppBranch seachColonelAppBranchById(String id);

    /**
     * 删除ES门店数据
     * @param search
     * @return
     */
    Integer deleteColonelAppBranch(ColonelAppBranchDTO search);

}
