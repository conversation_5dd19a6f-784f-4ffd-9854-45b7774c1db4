package com.zksr.common.elasticsearch.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.common.Term;
import com.huaban.analysis.jieba.JiebaSegmenter;
import com.huaban.analysis.jieba.WordDictionary;
import com.zksr.common.core.enums.ProductSortType;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.ShelfStatus;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageParam;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.*;
import com.zksr.common.elasticsearch.mapper.*;
import com.zksr.common.elasticsearch.model.dto.ProductSearchDTO;
import com.zksr.common.elasticsearch.model.dto.SearchRecommendRate;
import com.zksr.common.elasticsearch.model.dto.StoreProductDTO;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.common.elasticsearch.util.EsProductConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.biz.OrderByParam;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeValuesSourceBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.ParsedComposite;
import org.elasticsearch.search.aggregations.bucket.composite.TermsValuesSourceBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.ScriptSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortMode;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.UndeclaredThrowableException;
import java.math.BigDecimal;
import java.util.*;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: es 商品操作service
 * @date 2024/3/1 8:37
 */
@Slf4j
@Service
@SuppressWarnings("all")
public class EsProductServiceImpl implements EsProductService {

    @Resource
    private EsLocalProductFullMapper localProductFullMapper;

    @Resource
    private EsGlobalProductFullMapper globalProductFullMapper;

    @Resource
    private EsProductMapper esProductMapper;

    @Resource
    private EsStoreProductFullMapper storeProductFullMapper;

    @Resource
    private EsColonelAppBranchFullMapper colonelAppBranchFullMapper;

    @Override
    public Boolean saveLocalFull(List<EsLocalProduct> list) {
        if (list.isEmpty()) {
            return false;
        }
        list.forEach(item -> {
            item.setType(ProductType.LOCAL.getType());
            EsProductConvertUtil.renderItem(item);
        });
        // 保存本地上架数据
        localProductFullMapper.insertBatch(list);
        // 转换SPU唯一
        this.saveProduct(EsProductConvertUtil.getLocalSpuUniqueList(list));
        return true;
    }


    @Override
    public Boolean saveGlobalFull(List<EsGlobalProduct> list) {
        if (list.isEmpty()) {
            return false;
        }
        list.forEach(item -> {
            item.setType(ProductType.GLOBAL.getType());
            EsProductConvertUtil.renderItem(item);
        });
        // 保存全国上架数据
        globalProductFullMapper.insertBatch(list);
        // 转换SPU唯一
        this.saveProduct(EsProductConvertUtil.getGlobalSpuUniqueList(list));
        return true;
    }

    @Override
    public Boolean saveProduct(List<EsProductGroup> list) {
        if (list.isEmpty()) {
            return false;
        }
        return esProductMapper.insertBatch(list) > BigDecimal.ZERO.intValue();
    }

    @Override
    public void initIndex() {
        try {
            localProductFullMapper.delete(new LambdaEsQueryWrapper<>());
            globalProductFullMapper.delete(new LambdaEsQueryWrapper<>());
            esProductMapper.delete(new LambdaEsQueryWrapper<>());
            //colonelAppBranchFullMapper.delete(new LambdaEsQueryWrapper<>());
            storeProductFullMapper.delete(new LambdaEsQueryWrapper<>());
        } catch (Exception e) {

            log.error(" EsProductServiceImpl.initIndex 异常1,", e);
        }
        try {
            localProductFullMapper.createIndex();
        } catch (Exception e) {
            log.error(" EsProductServiceImpl.initIndex 异常2,", e);
        }
        try {
            globalProductFullMapper.createIndex();
        } catch (Exception e) {
            log.error(" EsProductServiceImpl.initIndex 异常3,", e);
        }
        try {
            esProductMapper.createIndex();
        } catch (Exception e) {
            log.error(" EsProductServiceImpl.initIndex 异常4,", e);
        }
/*        try {
            colonelAppBranchFullMapper.createIndex();
        } catch (Exception e) {

        }*/
        try {
            storeProductFullMapper.createIndex();
        } catch (Exception e) {
            log.error(" EsProductServiceImpl.initIndex 异常5,", e);
        }
    }

    @Override
    public EsPageInfo<EsProductGroup> search(ProductSearchDTO searchDto, boolean needMinScore) {
        log.debug("EsProductServiceImpl.search searchDto:{}", searchDto);
        LambdaEsQueryWrapper<EsProductGroup> source = getEsQueryWrapper(searchDto, needMinScore);
        // 分页搜索
        return esProductMapper.pageQuery(source, searchDto.getPageNo(), searchDto.getPageSize());
    }

    @NotNull
    private LambdaEsQueryWrapper<EsProductGroup> getEsQueryWrapper(ProductSearchDTO searchDto, boolean needMinScore) {
        LambdaEsQueryWrapper<EsProductGroup> source = new LambdaEsQueryWrapper<>();
        // 上架状态
        source.eq(EsProduct::getShelfStatus, ShelfStatus.ON.getShelfStatus());
        // 关键字匹配
//        configSearch(searchDto, source);
        // 商品类型 || 全部搜索时直接屏蔽掉类型搜索
        source.eq(!ProductType.GROUP.getType().equals(searchDto.getProductType()) && StringUtils.isNotEmpty(searchDto.getProductType()),
                EsProductGroup::getType,
                searchDto.getProductType()
        );
        // 只查询指定分组或者指定区域的
        // 本地上架有城市, 全国展示分类上有分组
        source.in(EsProduct::getAreaId, Objects.nonNull(searchDto.getAreaId()) ? searchDto.getAreaId() : NumberPool.LOWER_GROUND_LONG, NumberPool.LOWER_GROUND_LONG);
        // 分类匹配
        source.in(Objects.nonNull(searchDto.getClass3Id()) && !searchDto.getClass3Id().isEmpty(), EsProductGroup::getClass3Id, searchDto.getClass3Id());
        source.in(Objects.nonNull(searchDto.getClass2Id()) && !searchDto.getClass2Id().isEmpty(), EsProductGroup::getClass2Id, searchDto.getClass2Id());
        source.in(Objects.nonNull(searchDto.getClass1Id()) && !searchDto.getClass1Id().isEmpty(), EsProductGroup::getClass1Id, searchDto.getClass1Id());
        // 管理分类匹配
        source.in(Objects.nonNull(searchDto.getCatgoryId()) && !searchDto.getCatgoryId().isEmpty(), EsProductGroup::getCatgoryId, searchDto.getCatgoryId());
        // 管理分类黑名单匹配
        source.not(ObjectUtil.isNotEmpty(searchDto.getBlackCatgoryId()), q -> q.in(EsProductGroup::getCatgoryId, searchDto.getBlackCatgoryId()));
        // 品牌匹配
        source.in(Objects.nonNull(searchDto.getBrandId()) && !searchDto.getBrandId().isEmpty(), EsProductGroup::getBrandId, searchDto.getBrandId());
        // 品牌黑名单匹配
        source.not(ObjectUtil.isNotEmpty(searchDto.getBlackBrandId()), q -> q.in(EsProductGroup::getBrandId, searchDto.getBlackBrandId()));
        // SKU匹配
        source.in(Objects.nonNull(searchDto.getSkuId()) && !searchDto.getSkuId().isEmpty(), EsProductGroup::getSkuId, searchDto.getSkuId());
        // SPU匹配
        source.in(Objects.nonNull(searchDto.getSpuId()) && !searchDto.getSpuId().isEmpty(), EsProductGroup::getSpuId, searchDto.getSpuId());
        // 入驻商匹配
        source.in(ObjectUtil.isNotEmpty(searchDto.getSupplierId()), EsProductGroup::getSupplierId, searchDto.getSupplierId());
        // 平台商匹配
        source.eq(Objects.nonNull(searchDto.getSysCode()), EsProductGroup::getSysCode, searchDto.getSysCode());
        // 经营屏蔽商品
        source.not(CollectionUtils.isNotEmpty(searchDto.getBlockSkus()), q -> q.in(EsProductGroup::getSkuId, searchDto.getBlockSkus()));
        // 根据skuId去重
        source.distinct(searchDto.isDistinctBySkuId(), EsProductGroup::getSkuId);
        source.distinct(searchDto.isDistinctBySpuId(), EsProductGroup::getSpuId);
        // 平台商匹配
        source.in(ObjectUtil.isNotEmpty(searchDto.getActivityIdList()), EsProductGroup::getActivityId, searchDto.getActivityIdList());
        // 库存过滤逻辑 - 支持负库存入驻商的特殊处理
        if (Objects.nonNull(searchDto.getNotStock())) {
            if (CollectionUtils.isNotEmpty(searchDto.getNegativeStockSupplierIds())) {
                // 如果有支持负库存的入驻商，则对这些入驻商不过滤库存，对其他入驻商过滤无库存商品
                source.and(and -> {
                    // 支持负库存的入驻商：不过滤库存（即使无库存也显示）
                    and.or().in(EsProductGroup::getSupplierId, searchDto.getNegativeStockSupplierIds());
                    // 不支持负库存的入驻商：只显示有库存的商品
                    and.or().and(notNegativeStock -> {
                        notNegativeStock.not().in(EsProductGroup::getSupplierId, searchDto.getNegativeStockSupplierIds());
                        notNegativeStock.eq(EsProductGroup::getNotStock, searchDto.getNotStock());
                    });
                });
            } else {
                // 没有支持负库存的入驻商，使用原有逻辑
                source.eq(EsProductGroup::getNotStock, searchDto.getNotStock());
            }
        }

        // 最高最低价逻辑
        source.ge(Objects.nonNull(searchDto.getMinPrice()), EsProductGroup::getMarkPrice, searchDto.getMinPrice());
        source.le(Objects.nonNull(searchDto.getMaxPrice()), EsProductGroup::getMarkPrice, searchDto.getMaxPrice());
        // 根据关键字模糊匹配商品名称
        source.match(StringUtils.isNotBlank(searchDto.getCondition()), EsProductGroup::getSpuName, searchDto.getCondition(), 5f)
                .or()
                .matchPhrase(StringUtils.isNotBlank(searchDto.getCondition()), EsProductGroup::getBrandName, searchDto.getCondition(), 3f)
                .or()
                .match(StringUtils.isNotBlank(searchDto.getCondition()), EsProductGroup::getBarcode, searchDto.getCondition(), 8f);

        // 隐藏库存天数调整
        if (Objects.nonNull(searchDto.getHideNoStockProductDay())) {
            source.and(and -> {
                // 无库存时间记录为空
                // 或者超过多少天无库存
                and.or().and(a -> a.not().exists(EsProductGroup::getNoStockTime));
                and.or().gt(EsProductGroup::getNoStockTime, System.currentTimeMillis() - (searchDto.getHideNoStockProductDay() * 86400 * 1000L));
            });
        }
        // 活动时间范围
        source.gt(EsProductGroup::getActivityEndTime, System.currentTimeMillis());
        source.lt(EsProductGroup::getActivityStartTime, System.currentTimeMillis());

        source.and(and -> {
            // 默认只查询普通商品, 组合促销商品会使用OR条件查询
            // 组合促销商品查询
            and.or().eq(EsProductGroup::getItemType, NumberPool.INT_ZERO);
            if (ObjectUtil.isNotEmpty(searchDto.getOrActivityId())) {
                and.or().in(EsProductGroup::getActivityId, searchDto.getOrActivityId());
            }
        });

        // 排序逻辑 - 考虑负库存入驻商
        if (CollectionUtils.isNotEmpty(searchDto.getNegativeStockSupplierIds())) {
            // 如果有支持负库存的入驻商，使用脚本排序：支持负库存的入驻商商品优先，然后按sortNum排序
            ScriptSortBuilder negativeStockSort = SortBuilders.scriptSort(
                    new Script(ScriptType.INLINE, "painless",
                            "if (params.negativeStockSupplierIds.contains(doc['supplierId'].value)) { " +
                            "  return 0; " +  // 支持负库存的入驻商商品 和 不支持负库存但有库存的商品 排在前面
                            "} else if (doc['notStock'].value == 0) { " +
                            "  return 0; " +
                            "} else { " +
                            "  return 1; " +  // 不支持负库存且无库存的商品排在最后
                            "}",
                            Collections.singletonMap("negativeStockSupplierIds", searchDto.getNegativeStockSupplierIds())
                    ),
                    ScriptSortBuilder.ScriptSortType.NUMBER
            );
            source.sort(negativeStockSort);
        } else {
            // 没有支持负库存的入驻商，使用原有逻辑：先排有没有库存
            source.orderByAsc(EsProductGroup::getNotStock);
        }
        // 排序 || 销量
        if (ProductSortType.SALE.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                source.orderByDesc(EsProductGroup::getSaleQty);
            } else {
                source.orderByAsc(EsProductGroup::getSaleQty);
            }
        }
        // 价格
        if (ProductSortType.PRICE.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                source.orderByDesc(EsProductGroup::getMarkPrice);
            } else {
                source.orderByAsc(EsProductGroup::getMarkPrice);
            }
        }
        // 上架顺序
        if (ProductSortType.SORT_NUM.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                source.orderByDesc(EsProductGroup::getSortNum);
            } else {
                source.orderByAsc(EsProductGroup::getSortNum);
            }
        }
        // 销售价格
        if (ProductSortType.SALE_PRICE.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                source.orderByDesc(EsProductGroup::getMarkPrice);
            } else {
                source.orderByAsc(EsProductGroup::getMarkPrice);
            }
        }

        // 如果搜索按得分排序，非搜索按指定排序号排序
        if (needMinScore) {
            source.sortByScore();
            source.minScore(12f);
        } else {
            source.orderByAsc(EsProductGroup::getSortNum);
        }

        // 如果没有指定三级分类, 因为商品可能发布到多个三级分类所以这里, 需要分组处理
        /** ================================= 排序 =========================================================*/
        // 管理分类推荐指数排序
        if (ObjectUtil.isNotEmpty(searchDto.getCategoryRecommendRateList())) {
            // 推荐概率配置
            // 构建 rateMap，根据 type 和 applyId 作为键
            Map<String, Double> rateMap = searchDto.getCategoryRecommendRateList().stream()
                    .filter(SearchRecommendRate::notCg)
                    .collect(Collectors.toMap(item -> item.getType() + "-" + item.getApplyId().toString(), item -> item.getRate().doubleValue()));

            // 构建脚本, 给每条记录计算一个随机数, 感觉这个操作比较耗时
            String searchCode =
                    "def rateKey1 = '1-' + doc['catgoryId'].value; " +
                            "def rateKey2 = '2-' + doc['spuId'].value; " +
                            "def rate = null; " +
                            "if (rateKey2 != null) { " +
                            "    rate = params.rateMap.get(rateKey2); " +
                            "} " +
                            "if (rate == null) { " +
                            "    rate = params.rateMap.get(rateKey1); " +
                            "} " +
                            "if (rate == null) { " +
                            "    rate = 0.0; " +
                            "} " +
                            "def randomNum = Math.random(); " +
                            "return randomNum * rate;";


            // 添加排序规则
            ScriptSortBuilder categorySort = SortBuilders.scriptSort(
                    new Script(ScriptType.INLINE, "painless",
                            searchCode,
                            Collections.singletonMap("rateMap", rateMap)
                    ),
                    ScriptSortBuilder.ScriptSortType.NUMBER
            );
            categorySort.order(SortOrder.DESC);
            source.sort(categorySort);
        }
        return source;
    }

    @Override
    public List<EsProductGroup> searchAll(ProductSearchDTO searchDto) {
        LambdaEsQueryWrapper<EsProductGroup> source = getEsQueryWrapper(searchDto, true);
        return esProductMapper.selectList(source);
    }

    @Override
    public EsPageInfo<EsLocalProduct> searchLocalFull(ProductSearchDTO searchDto) {
        LambdaEsQueryWrapper<EsLocalProduct> source = new LambdaEsQueryWrapper<>();
        // 上架状态
        source.eq(EsProduct::getShelfStatus, ShelfStatus.ON.getShelfStatus());
        // 是否还有库存,1-没有了,0-还有库存
        source.eq(EsProduct::getNotStock, 0);
        // 关键字匹配
        configSearch(searchDto, source);
        // 商品类型 || 全部搜索时直接屏蔽掉类型搜索
        source.eq(!ProductType.GROUP.getType().equals(searchDto.getProductType()) && StringUtils.isNotEmpty(searchDto.getProductType()),
                EsLocalProduct::getType,
                searchDto.getProductType()
        );
        // 只查询指定分组或者指定区域的
        // 本地上架有城市, 全国展示分类上有分组
        source.in(EsProduct::getAreaId, Objects.nonNull(searchDto.getAreaId()) ? searchDto.getAreaId() : NumberPool.LOWER_GROUND_LONG, NumberPool.LOWER_GROUND_LONG);
        // 分类匹配
        source.in(Objects.nonNull(searchDto.getClass3Id()) && !searchDto.getClass3Id().isEmpty(), EsLocalProduct::getClass3Id, searchDto.getClass3Id());
        source.in(Objects.nonNull(searchDto.getClass2Id()) && !searchDto.getClass2Id().isEmpty(), EsLocalProduct::getClass2Id, searchDto.getClass2Id());
        source.in(Objects.nonNull(searchDto.getClass1Id()) && !searchDto.getClass1Id().isEmpty(), EsLocalProduct::getClass1Id, searchDto.getClass1Id());
        // 管理分类匹配
        source.in(Objects.nonNull(searchDto.getCatgoryId()) && !searchDto.getCatgoryId().isEmpty(), EsLocalProduct::getCatgoryId, searchDto.getCatgoryId());
        // 管理分类黑名单匹配
        source.not(ObjectUtil.isNotEmpty(searchDto.getBlackCatgoryId()), q -> q.in(EsLocalProduct::getCatgoryId, searchDto.getBlackCatgoryId()));
        // 品牌匹配
        source.in(Objects.nonNull(searchDto.getBrandId()) && !searchDto.getBrandId().isEmpty(), EsLocalProduct::getBrandId, searchDto.getBrandId());
        // 品牌黑名单匹配
        source.not(ObjectUtil.isNotEmpty(searchDto.getBlackBrandId()), q -> q.in(EsLocalProduct::getBrandId, searchDto.getBlackBrandId()));
        // SKU匹配
        source.in(Objects.nonNull(searchDto.getSkuId()) && !searchDto.getSkuId().isEmpty(), EsLocalProduct::getSkuId, searchDto.getSkuId());
        // SPU匹配
        source.in(Objects.nonNull(searchDto.getSpuId()) && !searchDto.getSpuId().isEmpty(), EsLocalProduct::getSpuId, searchDto.getSpuId());
        // 入驻商匹配
        source.in(ObjectUtil.isNotEmpty(searchDto.getSupplierId()), EsLocalProduct::getSupplierId, searchDto.getSupplierId());
        // 平台商匹配
        source.eq(Objects.nonNull(searchDto.getSysCode()), EsLocalProduct::getSysCode, searchDto.getSysCode());
        // 经营屏蔽商品
        source.not(CollectionUtils.isNotEmpty(searchDto.getBlockSkus()), q -> q.in(EsLocalProduct::getSkuId, searchDto.getBlockSkus()));
        // 根据skuId去重
        source.distinct(searchDto.isDistinctBySkuId(), EsLocalProduct::getSkuId);
        source.distinct(searchDto.isDistinctBySpuId(), EsLocalProduct::getSpuId);
        // 如果没有指定三级分类, 因为商品可能发布到多个三级分类所以这里, 需要分组处理
        /** ================================= 排序 =========================================================*/
        // 排序 || 销量
        if (ProductSortType.SALE.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                source.orderByDesc(EsLocalProduct::getSaleQty);
            } else {
                source.orderByAsc(EsLocalProduct::getSaleQty);
            }
        }
        // 价格
        if (ProductSortType.PRICE.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                source.orderByDesc(EsLocalProduct::getMarkPrice);
            } else {
                source.orderByAsc(EsLocalProduct::getMarkPrice);
            }
        }
        // 上架顺序
        if (ProductSortType.SORT_NUM.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                source.orderByDesc(EsLocalProduct::getSortNum);
            } else {
                source.orderByAsc(EsLocalProduct::getSortNum);
            }
        }
        // 销售价格
        if (ProductSortType.SALE_PRICE.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                source.orderByDesc(EsLocalProduct::getMarkPrice);
            } else {
                source.orderByAsc(EsLocalProduct::getMarkPrice);
            }
        }
        // 默认就是排序号
        if (StringUtils.isEmpty(searchDto.getSortType()) && StringUtils.isEmpty(searchDto.getCondition())) {
            // 先排有没有库存, 然后排序号
            source.orderByAsc(
                    EsLocalProduct::getNotStock,
                    EsLocalProduct::getSortNum
            );
        }
        // 分页搜索
        try {
            return localProductFullMapper.pageQuery(source, searchDto.getPageNo(), searchDto.getPageSize());
        } catch (UndeclaredThrowableException e) {
            Throwable undeclaredThrowable = e.getUndeclaredThrowable();
            if (undeclaredThrowable instanceof InvocationTargetException) {
                Throwable targetException = ((InvocationTargetException) undeclaredThrowable).getTargetException();
                log.error("ES分页查询真实异常, error:", targetException);
            }
            throw e;
        }
    }

    @Override
    public EsPageInfo<EsGlobalProduct> searchGlobalFull(ProductSearchDTO searchDto) {
        LambdaEsQueryWrapper<EsGlobalProduct> source = new LambdaEsQueryWrapper<>();
        // 上架状态
        source.eq(EsProduct::getShelfStatus, ShelfStatus.ON.getShelfStatus());
        // 是否还有库存,1-没有了,0-还有库存
        source.eq(EsProduct::getNotStock, 0);
        // 关键字匹配
        configSearch(searchDto, source);
        // 商品类型 || 全部搜索时直接屏蔽掉类型搜索
        source.eq(!ProductType.GROUP.getType().equals(searchDto.getProductType()) && StringUtils.isNotEmpty(searchDto.getProductType()),
                EsGlobalProduct::getType,
                searchDto.getProductType()
        );
        // 只查询指定分组或者指定区域的
        // 本地上架有城市, 全国展示分类上有分组
        source.in(EsProduct::getAreaId, Objects.nonNull(searchDto.getAreaId()) ? searchDto.getAreaId() : NumberPool.LOWER_GROUND_LONG, NumberPool.LOWER_GROUND_LONG);
        // 分类匹配
        source.in(Objects.nonNull(searchDto.getClass3Id()) && !searchDto.getClass3Id().isEmpty(), EsGlobalProduct::getClass3Id, searchDto.getClass3Id());
        source.in(Objects.nonNull(searchDto.getClass2Id()) && !searchDto.getClass2Id().isEmpty(), EsGlobalProduct::getClass2Id, searchDto.getClass2Id());
        source.in(Objects.nonNull(searchDto.getClass1Id()) && !searchDto.getClass1Id().isEmpty(), EsGlobalProduct::getClass1Id, searchDto.getClass1Id());
        // 管理分类匹配
        source.in(Objects.nonNull(searchDto.getCatgoryId()) && !searchDto.getCatgoryId().isEmpty(), EsGlobalProduct::getCatgoryId, searchDto.getCatgoryId());
        // 管理分类黑名单匹配
        source.not(ObjectUtil.isNotEmpty(searchDto.getBlackCatgoryId()), q -> q.in(EsGlobalProduct::getCatgoryId, searchDto.getBlackCatgoryId()));
        // 品牌匹配
        source.in(Objects.nonNull(searchDto.getBrandId()) && !searchDto.getBrandId().isEmpty(), EsGlobalProduct::getBrandId, searchDto.getBrandId());
        // 品牌黑名单匹配
        source.not(ObjectUtil.isNotEmpty(searchDto.getBlackBrandId()), q -> q.in(EsGlobalProduct::getBrandId, searchDto.getBlackBrandId()));
        // SKU匹配
        source.in(Objects.nonNull(searchDto.getSkuId()) && !searchDto.getSkuId().isEmpty(), EsGlobalProduct::getSkuId, searchDto.getSkuId());
        // SPU匹配
        source.in(Objects.nonNull(searchDto.getSpuId()) && !searchDto.getSpuId().isEmpty(), EsGlobalProduct::getSpuId, searchDto.getSpuId());
        // 入驻商匹配
        source.in(ObjectUtil.isNotEmpty(searchDto.getSupplierId()), EsGlobalProduct::getSupplierId, searchDto.getSupplierId());
        // 平台商匹配
        source.eq(Objects.nonNull(searchDto.getSysCode()), EsGlobalProduct::getSysCode, searchDto.getSysCode());
        // 经营屏蔽商品
        source.not(CollectionUtils.isNotEmpty(searchDto.getBlockSkus()), q -> q.in(EsGlobalProduct::getSkuId, searchDto.getBlockSkus()));
        // 根据skuId去重
        source.distinct(searchDto.isDistinctBySkuId(), EsGlobalProduct::getSkuId);
        source.distinct(searchDto.isDistinctBySpuId(), EsGlobalProduct::getSpuId);
        // 如果没有指定三级分类, 因为商品可能发布到多个三级分类所以这里, 需要分组处理
        /** ================================= 排序 =========================================================*/
        // 排序 || 销量
        if (ProductSortType.SALE.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                source.orderByDesc(EsGlobalProduct::getSaleQty);
            } else {
                source.orderByAsc(EsGlobalProduct::getSaleQty);
            }
        }
        // 价格
        if (ProductSortType.PRICE.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                source.orderByDesc(EsGlobalProduct::getMarkPrice);
            } else {
                source.orderByAsc(EsGlobalProduct::getMarkPrice);
            }
        }
        // 上架顺序
        if (ProductSortType.SORT_NUM.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                source.orderByDesc(EsGlobalProduct::getSortNum);
            } else {
                source.orderByAsc(EsGlobalProduct::getSortNum);
            }
        }
        // 销售价格
        if (ProductSortType.SALE_PRICE.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                source.orderByDesc(EsGlobalProduct::getMarkPrice);
            } else {
                source.orderByAsc(EsGlobalProduct::getMarkPrice);
            }
        }
        // 默认就是排序号
        if (StringUtils.isEmpty(searchDto.getSortType()) && StringUtils.isEmpty(searchDto.getCondition())) {
            // 先排有没有库存, 然后排序号
            source.orderByAsc(
                    EsGlobalProduct::getNotStock,
                    EsGlobalProduct::getSortNum
            );
        }
        // 分页搜索
        return globalProductFullMapper.pageQuery(source, searchDto.getPageNo(), searchDto.getPageSize());
    }

    @Override
    public List<EsLocalProduct> searchAreaItemList(ProductSearchDTO searchDto) {
        LambdaEsQueryWrapper<EsLocalProduct> uw = new LambdaEsQueryWrapper<>();
        // 上架状态
        uw.eq(EsProduct::getShelfStatus, ShelfStatus.ON.getShelfStatus());
        // 平台商匹配
        uw.eq(Objects.nonNull(searchDto.getSysCode()), EsLocalProduct::getSysCode, searchDto.getSysCode());
        //城市上架ID集合
        uw.in(Objects.nonNull(searchDto.getAreaItemIds()) && !searchDto.getAreaItemIds().isEmpty(), EsProduct::getItemId, searchDto.getAreaItemIds());
        // 排序 || 销量
        if (ProductSortType.SALE.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                uw.orderByDesc(EsLocalProduct::getSaleQty);
            } else {
                uw.orderByAsc(EsLocalProduct::getSaleQty);
            }
        }
        // 价格
        if (ProductSortType.PRICE.getSortType().equals(searchDto.getSortType())) {
            if (StringPool.DES.equals(searchDto.getOrderBy())) {
                uw.orderByDesc(EsLocalProduct::getMarkPrice);
            } else {
                uw.orderByAsc(EsLocalProduct::getMarkPrice);
            }
        }
        //查询列表
        List<EsLocalProduct> esLocalProducts = localProductFullMapper.selectList(uw);

        return ToolUtil.isNotEmpty(esLocalProducts) ? esLocalProducts : null;
    }

    /**
     * @Description: 保存门店下单商品信息
     * @Author: liuxingyu
     * @Date: 2024/4/29 10:05
     */
    @Override
    public Boolean saveStoreProduct(List<EsStoreProduct> storeProductList) {
        return storeProductFullMapper.insertBatch(storeProductList) > BigDecimal.ZERO.intValue();
    }

    /**
     * @Description: 获取门店下单商品信息
     * @Author: liuxingyu
     * @Date: 2024/4/29 16:40
     */
    @Override
    public PageResult<EsStoreProduct> getEsStoreProductList(StoreProductDTO storeProductDTO, PageParam pageParam) {
        LambdaEsQueryWrapper<EsStoreProduct> qw = new LambdaEsQueryWrapper<>();
        //添加查询条件
        qw.eq(ObjectUtil.isNotNull(storeProductDTO.getId()), EsStoreProduct::getId, storeProductDTO.getId());
        qw.in(ObjectUtil.isNotEmpty(storeProductDTO.getIds()), EsStoreProduct::getId, storeProductDTO.getIds());
        qw.eq(ObjectUtil.isNotNull(storeProductDTO.getBranchId()), EsStoreProduct::getBranchId, storeProductDTO.getBranchId());
        qw.eq(ObjectUtil.isNotNull(storeProductDTO.getCatgoryId()), EsStoreProduct::getCatgoryId, storeProductDTO.getCatgoryId());
        qw.eq(ObjectUtil.isNotNull(storeProductDTO.getCatgoryFirstId()), EsStoreProduct::getCatgoryFirstId, storeProductDTO.getCatgoryFirstId());
        //排序类型
        if (ObjectUtil.equal(storeProductDTO.getSortType(), NumberPool.INT_ONE)) {
            //1 购买次数
            if (ObjectUtil.equal(NumberPool.INT_ZERO, storeProductDTO.getOrderBy())) {
                //降序
                qw.orderByDesc(EsStoreProduct::getWithinPurchasedFrequencyTotal);
            } else {
                //升序
                qw.orderByAsc(EsStoreProduct::getWithinPurchasedFrequencyTotal);
            }
        } else if (ObjectUtil.equal(storeProductDTO.getSortType(), NumberPool.INT_TWO)) {
            //2 最近购买
            Date now = DateUtil.date();
            // 默认查询三个月内更新下单的数据
            Date thirtyDaysAgo = DateUtil.offsetMonth(now, -3);
            qw.between(EsStoreProduct::getRecentlyPurchasedDate, thirtyDaysAgo.getTime(), now.getTime());
            if (ObjectUtil.equal(NumberPool.INT_ZERO, storeProductDTO.getOrderBy())) {
                //降序
                qw.orderByDesc(EsStoreProduct::getRecentlyPurchasedDate);
            } else {
                //升序
                qw.orderByAsc(EsStoreProduct::getRecentlyPurchasedDate);
            }
        } else if (ObjectUtil.equal(storeProductDTO.getSortType(), NumberPool.INT_THREE)) {
            //3 购买数量
            if (ObjectUtil.equal(NumberPool.INT_ZERO, storeProductDTO.getOrderBy())) {
                //降序
                qw.orderByDesc(EsStoreProduct::getWithinPurchasedNumberTotal);
            } else {
                //升序
                qw.orderByAsc(EsStoreProduct::getWithinPurchasedNumberTotal);
            }
        }
        EsPageInfo<EsStoreProduct> pageInfo = storeProductFullMapper.pageQuery(qw, pageParam.getPageNo(), pageParam.getPageSize());
        return new PageResult<EsStoreProduct>(pageInfo.getList(), pageInfo.getTotal());
    }

    @Override
    public List<EsProduct> searchFullList(ProductSearchDTO searchDTO) {
        List<EsProduct> result = new ArrayList<>();
        // 本地商品
        {
            LambdaEsQueryWrapper<EsLocalProduct> uw = new LambdaEsQueryWrapper<>();
            // 上架状态
            uw.eq(EsProduct::getShelfStatus, ShelfStatus.ON.getShelfStatus());
            // 分类匹配
            uw.in(Objects.nonNull(searchDTO.getClass3Id()) && !searchDTO.getClass3Id().isEmpty(), EsLocalProduct::getClass3Id, searchDTO.getClass3Id());
            // 平台商匹配
            uw.eq(Objects.nonNull(searchDTO.getSysCode()), EsLocalProduct::getSysCode, searchDTO.getSysCode());
            //城市上架ID集合
            uw.in(Objects.nonNull(searchDTO.getItemIds()) && !searchDTO.getItemIds().isEmpty(), EsProduct::getItemId, searchDTO.getItemIds());
            List<EsLocalProduct> esLocalProducts = localProductFullMapper.selectList(uw);
            //查询列表
            result.addAll(esLocalProducts);
        }
        // 全国商品
        {
            LambdaEsQueryWrapper<EsGlobalProduct> uw = new LambdaEsQueryWrapper<>();
            // 分类匹配
            uw.in(Objects.nonNull(searchDTO.getClass3Id()) && !searchDTO.getClass3Id().isEmpty(), EsGlobalProduct::getClass3Id, searchDTO.getClass3Id());
            // 上架状态
            uw.eq(EsProduct::getShelfStatus, ShelfStatus.ON.getShelfStatus());
            // 平台商匹配
            uw.eq(Objects.nonNull(searchDTO.getSysCode()), EsGlobalProduct::getSysCode, searchDTO.getSysCode());
            //城市上架ID集合
            uw.in(Objects.nonNull(searchDTO.getItemIds()) && !searchDTO.getItemIds().isEmpty(), EsProduct::getItemId, searchDTO.getItemIds());
            List<EsGlobalProduct> esLocalProducts = globalProductFullMapper.selectList(uw);
            //查询列表
            result.addAll(esLocalProducts);
        }
        return result;
    }

    @Override
    public void deleteByEvent(ProductSearchDTO searchDTO) {
        if (Objects.isNull(searchDTO.getItemIds())
                && Objects.isNull(searchDTO.getSkuId())
                && Objects.isNull(searchDTO.getSpuId())) {
            log.error("调用了删除ES商品数据, 但是没有有效参数");
            return;
        }

        LambdaEsQueryWrapper<EsGlobalProduct> wp = new LambdaEsQueryWrapper<>();
        wp.in(Objects.nonNull(searchDTO.getSpuId()), EsGlobalProduct::getSpuId, searchDTO.getSpuId());
        wp.in(Objects.nonNull(searchDTO.getSkuId()), EsGlobalProduct::getSkuId, searchDTO.getSkuId());
        wp.in(Objects.nonNull(searchDTO.getItemIds()), EsGlobalProduct::getItemId, searchDTO.getItemIds());

        globalProductFullMapper.delete(wp);

        LambdaEsQueryWrapper<EsLocalProduct> localWp = new LambdaEsQueryWrapper<>();
        localWp.in(Objects.nonNull(searchDTO.getSpuId()), EsLocalProduct::getSpuId, searchDTO.getSpuId());
        localWp.in(Objects.nonNull(searchDTO.getSkuId()), EsLocalProduct::getSkuId, searchDTO.getSkuId());
        localWp.in(Objects.nonNull(searchDTO.getItemIds()), EsLocalProduct::getItemId, searchDTO.getItemIds());
        localProductFullMapper.delete(localWp);


        LambdaEsQueryWrapper<EsProductGroup> esWp = new LambdaEsQueryWrapper<>();
        esWp.in(Objects.nonNull(searchDTO.getSpuId()), EsProductGroup::getSpuId, searchDTO.getSpuId());
        esWp.in(Objects.nonNull(searchDTO.getSkuId()), EsProductGroup::getSkuId, searchDTO.getSkuId());
        esWp.in(Objects.nonNull(searchDTO.getItemIds()), EsProductGroup::getItemId, searchDTO.getItemIds());
        esProductMapper.delete(esWp);

    }

    @Override
    public List<Long> getAreaItemReleaseThreeAreaClassList(Long areaId) {
        LambdaEsQueryWrapper<EsLocalProduct> qw = new LambdaEsQueryWrapper<>();
        // RestHighLevelClient原生语法
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 分组
        List<CompositeValuesSourceBuilder<?>> sources = new ArrayList<>();
        TermsValuesSourceBuilder supplierNo = new TermsValuesSourceBuilder("class3Id").field("class3Id");
        sources.add(supplierNo);
        //桶分组 聚合
        CompositeAggregationBuilder myAggregation = AggregationBuilders.
                composite("group_by_fields", sources).
                size(5000);
        searchSourceBuilder.aggregation(myAggregation);

        // 默认搜索条件，
        BoolQueryBuilder baseQuery = QueryBuilders.boolQuery();
        baseQuery.must(QueryBuilders.termQuery("areaId", areaId));
        baseQuery.must(QueryBuilders.termQuery("shelfStatus", ShelfStatus.ON.getShelfStatus()));
        baseQuery.must(QueryBuilders.termQuery("itemType", NumberPool.INT_ZERO));
        searchSourceBuilder.query(baseQuery);

        qw.setSearchSourceBuilder(searchSourceBuilder);
        return parseSearchResponse(localProductFullMapper.search(qw));
    }

    @Override
    public List<Long> getAreaItemReleaseThreeCategoryList(Long areaId) {
        LambdaEsQueryWrapper<EsLocalProduct> qw = new LambdaEsQueryWrapper<>();
        // RestHighLevelClient原生语法
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 分组
        List<CompositeValuesSourceBuilder<?>> sources = new ArrayList<>();
        TermsValuesSourceBuilder supplierNo = new TermsValuesSourceBuilder("class3Id").field("catgoryId");
        sources.add(supplierNo);
        //桶分组 聚合
        CompositeAggregationBuilder myAggregation = AggregationBuilders.
                composite("group_by_fields", sources).
                size(5000);
        searchSourceBuilder.aggregation(myAggregation);

        // 默认搜索条件，
        BoolQueryBuilder baseQuery = QueryBuilders.boolQuery();
        baseQuery.must(QueryBuilders.termQuery("areaId", areaId));
        baseQuery.must(QueryBuilders.termQuery("shelfStatus", ShelfStatus.ON.getShelfStatus()));
        baseQuery.must(QueryBuilders.termQuery("itemType", NumberPool.INT_ZERO));
        searchSourceBuilder.query(baseQuery);

        qw.setSearchSourceBuilder(searchSourceBuilder);
        return parseSearchResponse(localProductFullMapper.search(qw));
    }

    @Override
    public List<Long> getGloablItemReleaseThreeClassList(Long sysCode) {
        LambdaEsQueryWrapper<EsGlobalProduct> qw = new LambdaEsQueryWrapper<>();
        // RestHighLevelClient原生语法
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 分组
        List<CompositeValuesSourceBuilder<?>> sources = new ArrayList<>();
        TermsValuesSourceBuilder supplierNo = new TermsValuesSourceBuilder("class3Id").field("class3Id");
        sources.add(supplierNo);
        //桶分组 聚合
        CompositeAggregationBuilder myAggregation = AggregationBuilders.
                composite("group_by_fields", sources).
                size(5000);
        searchSourceBuilder.aggregation(myAggregation);

        // 默认搜索条件，
        BoolQueryBuilder baseQuery = QueryBuilders.boolQuery();
        baseQuery.must(QueryBuilders.termQuery("shelfStatus", ShelfStatus.ON.getShelfStatus()));
        baseQuery.must(QueryBuilders.termQuery("sysCode", sysCode));
        baseQuery.must(QueryBuilders.termQuery("itemType", NumberPool.INT_ZERO));
        searchSourceBuilder.query(baseQuery);

        qw.setSearchSourceBuilder(searchSourceBuilder);
        return parseSearchResponse(globalProductFullMapper.search(qw));
    }

    @Override
    public List<Long> getGloablItemReleaseThreeCategoryList(Long sysCode) {
        LambdaEsQueryWrapper<EsGlobalProduct> qw = new LambdaEsQueryWrapper<>();
        // RestHighLevelClient原生语法
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 分组
        List<CompositeValuesSourceBuilder<?>> sources = new ArrayList<>();
        TermsValuesSourceBuilder supplierNo = new TermsValuesSourceBuilder("class3Id").field("catgoryId");
        sources.add(supplierNo);
        //桶分组 聚合
        CompositeAggregationBuilder myAggregation = AggregationBuilders.
                composite("group_by_fields", sources).
                size(5000);
        searchSourceBuilder.aggregation(myAggregation);

        // 默认搜索条件，
        BoolQueryBuilder baseQuery = QueryBuilders.boolQuery();
        baseQuery.must(QueryBuilders.termQuery("shelfStatus", ShelfStatus.ON.getShelfStatus()));
        baseQuery.must(QueryBuilders.termQuery("sysCode", sysCode));
        baseQuery.must(QueryBuilders.termQuery("itemType", NumberPool.INT_ZERO));
        searchSourceBuilder.query(baseQuery);

        qw.setSearchSourceBuilder(searchSourceBuilder);
        return parseSearchResponse(globalProductFullMapper.search(qw));
    }

    public List<Long> parseSearchResponse(SearchResponse search) {
        List<Long> result = new ArrayList<>();
        ParsedComposite aggregation = search.getAggregations().get("group_by_fields");
        for (ParsedComposite.ParsedBucket bucket : aggregation.getBuckets()) {
            Long class3Id = Long.parseLong(String.valueOf(bucket.getKey().get("class3Id")));
            result.add(class3Id);
        }
        return result;
    }


    @PostConstruct
    public void init() {
        Path path = null;
        log.info("加载自定义词典");
        try (InputStream inputStream = EsProductServiceImpl.class.getClassLoader().getResourceAsStream("shop2021_keyword.txt")) {
            if (inputStream == null) {
                throw new ServiceException("Resource 'shop2021_keyword.txt' not found!");
            }
            File tempFile = File.createTempFile("shop2021_keyword", ".txt");
            tempFile.deleteOnExit(); // 确保临时文件在 JVM 退出时被删除
            try (OutputStream outputStream = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            path = Paths.get(tempFile.getAbsolutePath());
        } catch (Exception e) {
            log.error("加载自定义词典异常");
            throw new RuntimeException("Failed to load dictionary", e);
        }
        WordDictionary.getInstance().loadUserDict(path);
        log.info("加载自定义词典完成");
    }

    private void configSearch(ProductSearchDTO searchDto, LambdaEsQueryWrapper uw) {
        // 如果没有关键字那就不操作搜索
        if (StringUtils.isEmpty(searchDto.getCondition())) {
            return;
        }
        searchDto.setCondition(StringUtils.replaceSymbols(searchDto.getCondition(), ""));
        if (StringUtils.isEmpty(searchDto.getCondition())) {
            return;
        }
        // 创建JiebaSegmenter对象
        JiebaSegmenter segmenter = new JiebaSegmenter();
        String queryStr = StringPool.EMPTY;
        // 分词搜索关键字
        List<String> tokens = segmenter.sentenceProcess(searchDto.getCondition());
        tokens.add(searchDto.getCondition());
        // 按照读音再拆一次
        List<Term> segments = HanLP.segment(searchDto.getCondition());
        for (Term term : segments) {
            tokens.add(term.toString().split("/")[0]);
        }
        // 去重
        tokens = tokens.stream().filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        // 按照词长度降序
        tokens.sort(Comparator.comparing(String::length).reversed());
        for (int i = 0; i < tokens.size(); i++) {
            String token = tokens.get(i);
            queryStr += StringUtils.format("(condition.keyword:*{}*^{})OR", token, tokens.size() - i);
            queryStr += StringUtils.format("(condition:*{}*^{})OR", token, tokens.size() - i);
            // 限制最大匹配字词数
            if (i > 6) {
                break;
            }
        }
        // 组装query_string
        String query = StringUtils.format("({})", queryStr.substring(0, queryStr.length() - 2));
        uw.queryStringQuery(query);


        // 是否还有库存,1-没有了,0-还有库存
        // 需要实现, 如果notStock=1, 那就排在后面, 否则就按照上面的query_string相关性排序
        ScriptSortBuilder stockSort = SortBuilders.scriptSort(
                new Script(ScriptType.INLINE, "painless",
                        "if (doc['notStock'].value == 1) { return 1; } else { return 0; }",
                        Collections.emptyMap()
                ),
                ScriptSortBuilder.ScriptSortType.NUMBER
        );
        uw.sort(stockSort);
    }
}
