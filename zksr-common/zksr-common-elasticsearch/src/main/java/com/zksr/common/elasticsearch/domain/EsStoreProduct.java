package com.zksr.common.elasticsearch.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.constant.EsIndexNameConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 门店常购/最近购买商品实体类
 * @Author: liuxingyu
 * @Date: 2024/4/29 9:09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@IndexName(value = EsIndexNameConstants.STORE_PRODUCT)
public class EsStoreProduct {

    /**
     * 唯一ID
     * 规则: CONCAT(branch_id,'_',sku_id,'_',itemType)
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    /**
     * 平台商编号
     */
    private Long sysCode;

    /**
     * 门店ID
     */
    private Long branchId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * spuId
     */
    private Long spuId;

    /**
     * 最近购买数量
     */
    private Long recentlyPurchasedNumber;

    /**
     * 最近90天购买次数
     */
    private Long withinPurchasedFrequencyTotal;

    /**
     * 最近90天购买数量
     */
    private Long withinPurchasedNumberTotal;

    /**
     * 最近90天平均购买的规格数量
     */
    private BigDecimal withinPurchasedNumberAvg;

    /**
     * 最近90天购买频次
     */
    private BigDecimal withinPurchasedFrequencyAvg;

    /**
     * 首次下单时间
     */
    @IndexField(fieldType = FieldType.DATE)
    private Date firstPurchasedDate;

    /**
     * 最近下单时间
     */
    @IndexField(fieldType = FieldType.DATE)
    private Date recentlyPurchasedDate;

    /**
     * 历史累计下单次数
     */
    private Long purchasedFrequencyTotal;

    /**
     * 历史累计购买数量
     */
    private Long purchasedNumberTotal;

    /**
     * 商品类别（管理类别编号，仅第三级）
     */
    private Long catgoryId;

    /**
     * 管理类别一级ID
     */
    private Long catgoryFirstId;

    /**
     * 商品品牌
     */
    private Long brandId;

    /**
     * 点击次数
     */
    private Long clickNumberTotal;

    /**
     * 商品类型 0：全国商品 1：本地商品
     */
    private Integer itemType;

    /**
     * 入驻商ID
     */
    private Long supplierId;

    /**
     * 城市上架商品id
     */
    private Long areaItemId;

    /**
     * 入驻商上架商品id
     */
    private Long supplierItemId;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单位, 1-小单位, 2-中单位, 3-大单位
     * 参见 {@link com.zksr.common.core.enums.UnitTypeEnum}
     */
    private Integer unitSize;
}
