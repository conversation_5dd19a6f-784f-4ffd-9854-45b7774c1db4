package com.zksr.common.elasticsearch.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.constant.EsIndexNameConstants;
import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.math.BigDecimal;
import java.util.Date;

/**
*
 * ES 主页数据 - SKU信息
* <AUTHOR>
* @date 2024/12/19 15:20
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@IndexName(value = EsIndexNameConstants.HOME_PAGES_SKU_DATA)
public class EsHomePagesSkuData {

    /**
     * 唯一ID
     * 规则:  CONCAT(20241220 或 202412,'_',sys_code_dcId)
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    /** 平台商id */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("平台商id")
    private Long sysCode;

    /** 运营商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("运营商ID")
    private Long dcId;

    /** 运营商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("入驻商ID")
    private Long supplierId;

    /** 刷新ES时间 */
    @IndexField(fieldType = FieldType.DATE)
    @ApiModelProperty(value = "更新ES时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refreshEsTime;

    /**
     * 规则：20241220 或 202412
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("日期Id")
    private String dateId;

    //================-------订单售后信息-------================

    /** 上架SKU数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上架SKU数量")
    private Long skuShelfQty;

    /** 上次上架SKU数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次上架SKU数量")
    private Long beforeSkuShelfQty;

    /** 上架SKU同比上升/下降率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "上架SKU同比上升/下降率")
    private BigDecimal skuShelfRate;

    /** 动销SKU数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "动销SKU数量")
    private Long skuSalesQty;

    /** 上次动销SKU数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次动销SKU数量")
    private Long beforeSkuSalesQty;

    /** 动销SKU数量同比上升/下降率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "动销SKU数量同比上升/下降率")
    private BigDecimal skuSalesRate;

    /** SKU动销率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "SKU动销率")
    private BigDecimal skuRate;

    /** 一级品类个数 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "一级品类个数")
    private Long category1Qty;

    /** 上次一级品类个数 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次一级品类个数")
    private Long beforeCategory1Qty;

    /** 一级品类个数同比上升/下降率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "一级品类个数同比上升/下降率")
    private BigDecimal category1Rate;

    /** 一级品类动销率  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "一级品类动销率")
    private BigDecimal category1SalesRate;

    /** 新上架SKU数量  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "新上架SKU数量")
    private Long skuNewShelfQty;

    /** 上次新上架SKU数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次新上架SKU数量")
    private Long beforeSkuNewShelfQty;

    /** 新上架SKU数量同比上升/下降率 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "新上架SKU数量同比上升/下降率")
    private BigDecimal skuNewShelfRate;

    /** sku总数 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "sku总数")
    private Long skuTotalQty = NumberPool.LONG_ZERO;

    /** 一级品类动销数量 */
    @ApiModelProperty(value = "一级品类动销数量")
    private Long category1SalesQty = NumberPool.LONG_ZERO;
}
