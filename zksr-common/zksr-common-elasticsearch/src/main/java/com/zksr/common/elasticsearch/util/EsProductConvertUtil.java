package com.zksr.common.elasticsearch.util;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StockUtil;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.elasticsearch.domain.EsGlobalProduct;
import com.zksr.common.elasticsearch.domain.EsLocalProduct;
import com.zksr.common.elasticsearch.domain.EsProduct;
import com.zksr.common.elasticsearch.domain.EsProductGroup;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: ES 商品对象转换器
 * @date 2024/3/1 11:12
 */
public class EsProductConvertUtil {

    public static ArrayList<EsProductGroup> getLocalSpuUniqueList(List<EsLocalProduct> fullProducts) {
        ArrayList<EsProductGroup> esProductGroups = new ArrayList<>();
        Map<String, List<EsLocalProduct>> listMap = fullProducts.stream().collect(Collectors.groupingBy(EsProductConvertUtil::geLocalUniqueKey));
        listMap.keySet().forEach(key -> {
            List<EsLocalProduct> localFullProducts = listMap.get(key);
            // 价格最低的排前面
            localFullProducts.sort(Comparator.comparing(EsProduct::getMarkPrice));
            //默认获取上架商品SKU的排序顺序最靠前的商品
            //筛选上架商品
            List<EsLocalProduct> collect = localFullProducts.stream().filter(item -> {
                // 还没上架
                if (item.getShelfStatus() != 1) {
                    return false;
                }
                // 没有库存了
                if (StockUtil.isGreater(BigDecimal.ONE, item.getStock())) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            // 价格最低的排在前面
            Collections.sort(collect, Comparator.comparing(EsLocalProduct::getMarkPrice));
            // 获取有库存, 上架, 并且有库存的最优选择
            EsProductGroup item =
                    !collect.isEmpty() ? HutoolBeanUtils.toBean(collect.get(NumberPool.INT_ZERO), EsProductGroup.class) : HutoolBeanUtils.toBean(localFullProducts.get(NumberPool.INT_ZERO), EsProductGroup.class);
            // 设置唯一ID
            item.setId(key);
            item.setType(ProductType.LOCAL.getType());
            // 默认单规格
            item.setIsSpecs(NumberPool.LONG_ZERO);
            // 总销量
            long totalSaleQty = localFullProducts.stream().mapToLong(EsProduct::getSaleQty).sum();
            item.setSaleQty(totalSaleQty);
            // 合并条码
            List<String> barcodes = new ArrayList<>();
            localFullProducts.forEach(product -> {
                barcodes.add(product.getBarcode());
                barcodes.add(product.getMidBarcode());
                barcodes.add(product.getLargeBarcode());
            });
            item.setBarcodes(StringUtils.join(barcodes.stream().filter(Objects::nonNull).collect(Collectors.toSet()), StringPool.COMMA));
            // 上架了多个单位也是多规格
            renderItem(item);
            // 如果有多个SKU
            if (localFullProducts.size() > NumberPool.INT_ONE) {
                item.setIsSpecs(NumberPool.LONG_ONE);
            }
            esProductGroups.add(item);
        });
        return esProductGroups;
    }

    public static List<EsProductGroup> getGlobalSpuUniqueList(List<EsGlobalProduct> fullProducts) {
        ArrayList<EsProductGroup> esProductGroups = new ArrayList<>();
        Map<String, List<EsGlobalProduct>> listMap = fullProducts.stream().collect(Collectors.groupingBy(EsProductConvertUtil::getGlobalUniqueKey));
        listMap.keySet().forEach(key -> {
            List<EsGlobalProduct> localFullProducts = listMap.get(key);
            // 价格最低的排前面
            localFullProducts.sort(Comparator.comparing(EsProduct::getMarkPrice));
            //默认获取上架商品SKU的排序顺序最靠前的商品
            //筛选上架商品 && 有库存的商品
            List<EsGlobalProduct> collect = localFullProducts.stream().filter(item -> {
                // 还没上架
                if (item.getShelfStatus() != 1) {
                    return false;
                }
                // 没有库存了
                if (StockUtil.isGreater(BigDecimal.ONE, item.getStock())) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            // 价格最低的排在前面
            Collections.sort(collect, Comparator.comparing(EsGlobalProduct::getMarkPrice));
            // 获取有库存, 上架, 并且有库存的最优选择
            EsProductGroup item =
                    !collect.isEmpty() ? HutoolBeanUtils.toBean(collect.get(NumberPool.INT_ZERO), EsProductGroup.class) : HutoolBeanUtils.toBean(localFullProducts.get(NumberPool.INT_ZERO), EsProductGroup.class);
            item.setAreaId(NumberPool.LOWER_GROUND_LONG);
            item.setId(key);
            item.setType(ProductType.GLOBAL.getType());
            // 默认单规格
            item.setIsSpecs(NumberPool.LONG_ZERO);
            // 总销量
            long totalSaleQty = localFullProducts.stream().mapToLong(EsProduct::getSaleQty).sum();
            item.setSaleQty(totalSaleQty);
            // 合并条码
            List<String> barcodes = new ArrayList<>();
            localFullProducts.forEach(product -> {
                barcodes.add(product.getBarcode());
                barcodes.add(product.getMidBarcode());
                barcodes.add(product.getLargeBarcode());
            });
            item.setBarcodes(StringUtils.join(barcodes.stream().filter(Objects::nonNull).collect(Collectors.toSet()), StringPool.COMMA));
            // 上架了多个单位也是多规格
            renderItem(item);
            // 如果有多个SKU
            if (localFullProducts.size() > NumberPool.INT_ONE) {
                item.setIsSpecs(NumberPool.LONG_ONE);
            }
            esProductGroups.add(item);
        });
        return esProductGroups;
    }

    /**
     * 渲染全国, 或者本地商品, 是否多规格, 以及优先显示规格
     * @param item
     */
    public static void renderItem(EsProduct item) {
        // 判断当前规格是否多规格
        Integer shelfStatus =
                (com.zksr.common.core.utils.StringUtils.isNotEmpty(item.getUnit()) ? item.getMinShelfStatus() : 0)
                        + (com.zksr.common.core.utils.StringUtils.isNotEmpty(item.getMidUnit()) ? item.getMidShelfStatus() : 0)
                        + (com.zksr.common.core.utils.StringUtils.isNotEmpty(item.getLargeUnit()) ? item.getLargeShelfStatus() : 0);
        if (shelfStatus > NumberPool.LONG_ONE) {
            item.setIsSpecs(NumberPool.LONG_ONE);
        } else {
            item.setIsSpecs(NumberPool.LONG_ZERO);
        }
        // 设置最小单位
        if (com.zksr.common.core.utils.StringUtils.isNotEmpty(item.getUnit()) && item.getMinShelfStatus() == 1) {
            item.setUnitSize(UnitTypeEnum.UNIT_SMALL.getType());
        } else {
            if (com.zksr.common.core.utils.StringUtils.isNotEmpty(item.getLargeUnit()) && item.getLargeShelfStatus() == 1) {
                item.setUnit(item.getLargeUnit());
                item.setUnitSize(UnitTypeEnum.UNIT_LARGE.getType());
                item.setMarkPrice(item.getLargeMarkPrice());
                item.setSuggestPrice(item.getLargeSuggestPrice());
                item.setMinOq(item.getLargeMinOq());
                item.setJumpOq(item.getLargeJumpOq());
                item.setMaxOq(item.getLargeMaxOq());
            }
            if (com.zksr.common.core.utils.StringUtils.isNotEmpty(item.getMidUnit()) && item.getMidShelfStatus() == 1) {
                item.setUnit(item.getMidUnit());
                item.setUnitSize(UnitTypeEnum.UNIT_MIDDLE.getType());
                item.setMarkPrice(item.getMidMarkPrice());
                item.setSuggestPrice(item.getMidSuggestPrice());
                item.setMinOq(item.getMidMinOq());
                item.setJumpOq(item.getMidJumpOq());
                item.setMaxOq(item.getMidMaxOq());
            }
        }
        // 总库存 - 总销量, 没有库存了
        // 一份份额
        BigDecimal oneLot = StockUtil.stockDivide(item.getStock(), item.stockConvert(item.getUnitSize()));
        // 不足一份
        item.setNotStock(StockUtil.isGreater(BigDecimal.ONE, oneLot) ? 1 :  0);
        if (item.getNotStock() == NumberPool.INT_ONE) {
            // 如果是没有库存了, 记录当前单位没有库存的时间
            Date noStockTime = item.noStockTime(item.getUnitSize());
            if (Objects.nonNull(noStockTime)) {
                item.setNoStockTime(noStockTime.getTime());
            }
        } else {
            item.setNoStockTime(DateUtil.parseDate(DateUtils.MAX_DATE).getTime());
        }
    }

    public static String getGlobalUniqueKey(EsProduct globalProduct) {
        return ProductType.GLOBAL.getType()
            + StringPool.UNDERSCORE
            + globalProduct.getAreaId()
            + StringPool.UNDERSCORE
            + globalProduct.getClass3Id()
            + StringPool.UNDERSCORE
            + globalProduct.getSpuId();
    }
    public static String geLocalUniqueKey(EsProduct localProduct) {
        return ProductType.LOCAL.getType()
                + StringPool.UNDERSCORE
                + localProduct.getAreaId()
                + StringPool.UNDERSCORE
                + localProduct.getClass3Id()
                + StringPool.UNDERSCORE
                + localProduct.getSpuId();
    }
}
