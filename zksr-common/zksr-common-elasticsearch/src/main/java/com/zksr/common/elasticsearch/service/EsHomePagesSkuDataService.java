package com.zksr.common.elasticsearch.service;

import com.zksr.common.elasticsearch.domain.EsHomePagesBranchData;
import com.zksr.common.elasticsearch.domain.EsHomePagesSkuData;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;

import java.util.List;

/**
*
 *
* <AUTHOR>
* @date 2024/12/24 9:53
*/
public interface EsHomePagesSkuDataService {

    /**
     * 初始化索引
     */
    void initIndex();

    /**
     * 批量保存信息
     * @param homePagesDataList
     * @return
     */
    Boolean saveBatchHomePagesSkuData(List<EsHomePagesSkuData> dataList);

    /**
     * 保存信息
     * @param homePagesData
     * @return
     */
    Boolean saveHomePagesSkuData(EsHomePagesSkuData data);

    /**
     * 查询ES信息
     * @param searchDTO
     * @return
     */
    EsHomePagesSkuData searchHomePagesSkuData(HomePagesSearchDTO searchDTO);


}
