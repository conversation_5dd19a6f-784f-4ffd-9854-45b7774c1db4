package com.zksr.common.elasticsearch.service;


import com.zksr.common.elasticsearch.domain.EsHomePagesData;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;

import java.util.List;

/**
*
 *
* <AUTHOR>
* @date 2024/5/9 9:53
*/
public interface EsHomePagesDataService {

    /**
     * 初始化索引
     */
    void initIndex();

    /**
     * 批量保存信息
     * @param homePagesDataList
     * @return
     */
    Boolean saveBatchHomePagesData(List<EsHomePagesData> homePagesDataList);

    /**
     * 保存信息
     * @param homePagesData
     * @return
     */
    Boolean saveHomePagesData(EsHomePagesData homePagesData);

    /**
     * 查询ES信息
     * @param searchDTO
     * @return
     */
    EsHomePagesData searchHomePagesData(HomePagesSearchDTO searchDTO);


}
