package com.zksr.common.elasticsearch.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.constant.EsIndexNameConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.math.BigDecimal;
import java.util.Date;

/**
*
 * ES 主页数据 - 类型销售TOP10数据
* <AUTHOR>
* @date 2024/12/19 15:20
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@IndexName(value = EsIndexNameConstants.HOME_PAGES_SALES_TOP10_DATA)
public class EsHomePagesSalesTop10Data {

    /**
     * 唯一ID
     * 规则:  CONCAT(20241220 或 202412,'_',sys_code_dcId)
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    /** 平台商id */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("平台商id")
    private Long sysCode;

    /** 运营商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("运营商ID")
    private Long dcId;

    /** 运营商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("入驻商ID")
    private Long supplierId;

    /** 刷新ES时间 */
    @IndexField(fieldType = FieldType.DATE)
    @ApiModelProperty(value = "更新ES时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refreshEsTime;

    /**
     * 规则：20241220 或 202412
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("日期Id")
    private String dateId;

    //================--------------================

    /** 销售TOP10类型（区域：area，门店：branch，一级品类：category，运营商：dc，商品：itme，入驻商：supplier，业务员：colonel） */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty(value = "销售TOP10类型")
    private String salesType;

    /** 销售TOP10类型 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "销售TOP10类型")
    private Long salesTypeId;

    /** 销售TOP10类型名称 */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty(value = "销售TOP10类型名称")
    private String salesTypeName;

    /** 销售金额  */
    @ApiModelProperty(value = "销售金额")
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal orderSalesAmt = BigDecimal.ZERO;

    /** 上次销售金额 */
    @ApiModelProperty(value = "上次销售金额")
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal beforeOrderSalesAmt = BigDecimal.ZERO;
}
