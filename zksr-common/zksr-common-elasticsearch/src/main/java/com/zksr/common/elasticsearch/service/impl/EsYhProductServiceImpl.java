package com.zksr.common.elasticsearch.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.constant.EsIndexNameConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsLocalProduct;
import com.zksr.common.elasticsearch.domain.EsYhProduct;
import com.zksr.common.elasticsearch.mapper.EsLocalProductFullMapper;
import com.zksr.common.elasticsearch.mapper.EsYhProductMapper;
import com.zksr.common.elasticsearch.model.dto.YhProductSearchDTO;
import com.zksr.common.elasticsearch.service.EsYhProductService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeValuesSourceBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.ParsedComposite;
import org.elasticsearch.search.aggregations.bucket.composite.TermsValuesSourceBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 
 * @date 2024/12/11 9:09
 */
@Slf4j
@Service
public class EsYhProductServiceImpl implements EsYhProductService {
    
    @Resource
    private EsYhProductMapper esYhProductMapper;

    @Override
    public void insertBatch(List<EsYhProduct> esYhProducts) {
        if (ObjectUtil.isEmpty(esYhProducts)) {
            return;
        }
        esYhProductMapper.insertBatch(esYhProducts);
    }

    @Override
    public Map<Long, List<Long>> getBatchSupplierSaleClass(Date batchDate, Long branchId) {
        LambdaEsQueryWrapper<EsYhProduct> qw = new LambdaEsQueryWrapper<>();

        // RestHighLevelClient原生语法
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 门店补货批次过滤
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.matchQuery("batchYmd", Integer.parseInt(DateUtil.format(batchDate, DatePattern.PURE_DATE_PATTERN))));
        boolQuery.must(QueryBuilders.matchQuery("branchId", branchId));
        searchSourceBuilder.query(boolQuery);

        // 分组
        List<CompositeValuesSourceBuilder<?>> sources = new ArrayList<>();
        TermsValuesSourceBuilder specId = new TermsValuesSourceBuilder("supplierId").field("supplierId");
        sources.add(specId);
        TermsValuesSourceBuilder supplierNo = new TermsValuesSourceBuilder("threeSaleClassId").field("threeSaleClassId");
        sources.add(supplierNo);
        //桶分组 聚合
        CompositeAggregationBuilder myAggregation = AggregationBuilders.
                composite("group_by_fields", sources).
                size(5000);
        searchSourceBuilder.aggregation(myAggregation);
        qw.setSearchSourceBuilder(searchSourceBuilder);
        return parseSearchResponse(esYhProductMapper.search(qw));
    }

    @Override
    public PageResult<EsYhProduct> pageList(YhProductSearchDTO searchDTO) {
        LambdaEsQueryWrapper<EsYhProduct> qw = new LambdaEsQueryWrapper<>();
        qw.eq(EsYhProduct::getBatchYmd, Integer.parseInt(DateUtil.format(searchDTO.getBatchDate(), DatePattern.PURE_DATE_PATTERN)));
        qw.eq(EsYhProduct::getBranchId, searchDTO.getBranchId());
        qw.eq(Objects.nonNull(searchDTO.getSupplierId()), EsYhProduct::getSupplierId, searchDTO.getSupplierId());
        qw.eq(Objects.nonNull(searchDTO.getThreeSaleClassId()), EsYhProduct::getThreeSaleClassId, searchDTO.getThreeSaleClassId());
        qw.eq(Objects.nonNull(searchDTO.getChecked()), EsYhProduct::getChecked, searchDTO.getChecked());
        // 模糊匹配关键字
        if (StringUtils.isNotEmpty(searchDTO.getKeyword())) {
            qw.like(EsYhProduct::getSpuName, searchDTO.getKeyword());
        }
        // 仅获取计算数据, 加快网络传输
        if (searchDTO.getSearchMode() == NumberPool.INT_ONE) {
            qw.select(
                    EsYhProduct::getYhId,
                    EsYhProduct::getAreaItemId,
                    EsYhProduct::getSpuId,
                    EsYhProduct::getSkuId,
                    EsYhProduct::getMallUnitType,
                    EsYhProduct::getChecked,
                    EsYhProduct::getPosSuggestQty
            );
        }
        // 最新的要货在最前面
        if (StringUtils.isEmpty(searchDTO.getKeyword())) {
            // 在没有搜索的场景下, 默认最新要货排在前面
            qw.orderByDesc(EsYhProduct::getYhId);
        }
        EsPageInfo<EsYhProduct> pageInfo = esYhProductMapper.pageQuery(qw, searchDTO.getPageNo(), searchDTO.getPageSize());
        return new PageResult<>(pageInfo.getList(), pageInfo.getTotal());
    }

    @Override
    public List<EsYhProduct> getYhProductByBranchItemIdList(Long branchId, Date batchDate, List<Long> itemIdList) {
        LambdaEsQueryWrapper<EsYhProduct> qw = new LambdaEsQueryWrapper<>();
        qw.eq(EsYhProduct::getBatchYmd, Integer.parseInt(DateUtil.format(batchDate, DatePattern.PURE_DATE_PATTERN)));
        qw.eq(EsYhProduct::getBranchId, branchId);
        qw.in(EsYhProduct::getAreaItemId, itemIdList);
        return esYhProductMapper.selectList(qw);
    }

    @Override
    public List<EsYhProduct> getYhProductByBranchSkuIdList(Long branchId, Date batchDate, List<Long> skuIdList) {
        LambdaEsQueryWrapper<EsYhProduct> qw = new LambdaEsQueryWrapper<>();
        qw.eq(EsYhProduct::getBatchYmd, Integer.parseInt(DateUtil.format(batchDate, DatePattern.PURE_DATE_PATTERN)));
        qw.eq(EsYhProduct::getBranchId, branchId);
        qw.in(EsYhProduct::getSkuId, skuIdList);
        return esYhProductMapper.selectList(qw);
    }

    @Override
    public void deleteById(List<Long> deleteYhIds) {
        esYhProductMapper.deleteBatchIds(deleteYhIds);
    }

    @Override
    public void updateBatch(List<EsYhProduct> esYhProducts) {
        for (EsYhProduct product : esYhProducts) {
            esYhProductMapper.updateById(product);
        }
        esYhProductMapper.refresh();
    }

    @Override
    public void createIndex() {
        try {
            esYhProductMapper.deleteIndex(EsIndexNameConstants.YH_DATA);
        } catch (Exception e) {
            log.error("删除要货单索引失败", e);
        }
        try {
            esYhProductMapper.createIndex();
        } catch (Exception e) {
            log.error("创建要货单索引失败", e);
        }
    }

    public Map<Long, List<Long>> parseSearchResponse(SearchResponse search) {
        Map<Long, List<Long>> result = new HashMap<>();
        ParsedComposite aggregation = search.getAggregations().get("group_by_fields");
        for (ParsedComposite.ParsedBucket bucket : aggregation.getBuckets()) {
            Long supplierId = Long.parseLong(String.valueOf(bucket.getKey().get("supplierId")));
            Long class3Id = Long.parseLong(String.valueOf(bucket.getKey().get("threeSaleClassId")));
            result.computeIfAbsent(supplierId, k -> new ArrayList<>()).add(class3Id);
        }
        return result;
    }
}
