package com.zksr.common.elasticsearch.service;

import com.zksr.common.elasticsearch.domain.EsHomePagesBranchData;
import com.zksr.common.elasticsearch.domain.EsHomePagesOrderAfterData;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;

import java.util.List;

/**
*
 *
* <AUTHOR>
* @date 2024/5/9 9:53
*/
public interface EsHomePagesBranchDataService {

    /**
     * 初始化索引
     */
    void initIndex();

    /**
     * 批量保存信息
     * @param homePagesDataList
     * @return
     */
    Boolean saveBatchHomePagesBranchData(List<EsHomePagesBranchData> dataList);

    /**
     * 保存信息
     * @param homePagesData
     * @return
     */
    Boolean saveHomePagesBranchData(EsHomePagesBranchData data);

    /**
     * 查询ES信息
     * @param searchDTO
     * @return
     */
    EsHomePagesBranchData searchHomePagesBranchData(HomePagesSearchDTO searchDTO);


}
