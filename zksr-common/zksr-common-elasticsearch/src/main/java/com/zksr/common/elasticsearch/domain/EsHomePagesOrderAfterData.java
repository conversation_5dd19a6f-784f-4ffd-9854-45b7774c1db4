package com.zksr.common.elasticsearch.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.constant.EsIndexNameConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.math.BigDecimal;
import java.util.Date;

/**
*
 * ES 主页数据 - 订单销售
* <AUTHOR>
* @date 2024/12/19 15:20
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@IndexName(value = EsIndexNameConstants.HOME_PAGES_ORDER_AFTER_DATA)
public class EsHomePagesOrderAfterData {

    /**
     * 唯一ID
     * 规则:  CONCAT(20241220 或 202412,'_',sys_code_dcId_supplierId)
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    /** 平台商id */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("平台商id")
    private Long sysCode;

    /** 运营商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("运营商ID")
    private Long dcId;

    /** 入驻商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("入驻商ID")
    private Long supplierId;

    /** 刷新ES时间 */
    @IndexField(fieldType = FieldType.DATE)
    @ApiModelProperty(value = "更新ES时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refreshEsTime;

    /**
     * 规则：20241220 或 202412
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("日期Id")
    private String dateId;

    //================-------订单售后信息-------================

    /** 退单金额 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "退单金额")
    private BigDecimal returnAmt;

    /** 上次退单金额 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "上次退单金额")
    private BigDecimal beforeReturnAmt;

    /** 退单金额同比上升/下降率 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "退单金额同比上升/下降率")
    private BigDecimal returnAmtRate;

    /** 退单数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "退单数量")
    private Long returnQty;

    /** 上次退单数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次退单数量")
    private Long beforeReturnQty;

    /** 退单数量同比上升/下降率 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "退单数量同比上升/下降率")
    private BigDecimal returnQtyRate;

    /** 退单门店数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "退单门店数量")
    private Long returnBranchQty;

    /** 上次退单门店数量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次退单门店数量")
    private Long beforeReturnBranchQty;

    /** 退单门店数量同比上升/下降率 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "退单门店数量同比上升/下降率")
    private BigDecimal returnBranchQtyRate;

    /** 退单SKU数  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "退单SKU数")
    private Long returnSkuQty;

    /** 上次退单SKU数  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "上次退单SKU数")
    private Long beforeReturnSkuQty;

    /** 退单SKU数量同比上升/下降率 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "退单SKU数量同比上升/下降率")
    private BigDecimal returnSkuQtyRate;

    /** 待处理退单金额  */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty(value = "待处理退单金额")
    private BigDecimal pendingReturnAmt;

    /** 待处理退单数量  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "待处理退单数量")
    private Long pendingReturnQty;

    /** 待处理退单门店数量  */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "待处理退单门店数量")
    private Long pendingeturnBranchQty;
}
