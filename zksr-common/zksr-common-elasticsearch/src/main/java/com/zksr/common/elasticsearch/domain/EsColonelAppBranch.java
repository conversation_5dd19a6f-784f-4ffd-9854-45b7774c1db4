package com.zksr.common.elasticsearch.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.constant.EsIndexNameConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.math.BigDecimal;
import java.util.Date;

/**
*
 * 业务员APP门店统计信息
* <AUTHOR>
* @date 2024/5/8 20:29
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@IndexName(value = EsIndexNameConstants.COLOENL_APP_BRANCH)
public class EsColonelAppBranch {

    /**
     * 唯一ID
     * 规则: CONCAT(branch_id,'_',sys_code)
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    /** 门店ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("门店ID")
    private Long branchId;

    /** 平台商id */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("平台商id")
    private Long sysCode;

    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("数据创建年月（yyyy-MM）")
    private String createYearMonth;

//    /** 刷新ES时间 */
//    @IndexField(fieldType = FieldType.DATE)
//    @ApiModelProperty(value = "更新ES时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date refreshEsTime;

    /** -------订单信息------- */

    /** 本月订单笔数 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("本月订单笔数")
    private Long nowMonthOrderQty;

    /** 本月订单金额 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty("本月订单金额")
    private BigDecimal nowMonthOrderAmt;

    /** 上月订单笔数 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("上月订单笔数")
    private Long lastMonthOrderQty;

    /** 上月订单金额 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty("上月订单金额")
    private BigDecimal lastMonthOrderAmt;

    /** 用户最近一次订货时间 */
    @IndexField(fieldType = FieldType.DATE)
    @ApiModelProperty(value = "最近一次订货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastBuyTime;

    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty("最近一次订货金额")
    private BigDecimal lastBuyAmt;

    /**  -------客户登陆------*/

    /** 用户上次访问系统时间 */
    @IndexField(fieldType = FieldType.DATE)
    @ApiModelProperty(value = "上次访问系统时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastAccessSystemTime;

    /** 用户最近登陆时间 */
    @IndexField(fieldType = FieldType.DATE)
    @ApiModelProperty(value = "最近登陆时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    /** ------业务员签到------*/

    /** 业务员最近一次拜访时间 */
    @IndexField(fieldType = FieldType.DATE)
    @ApiModelProperty(value = "最近一次拜访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastVisitTime;

    /**
     * lat,lon
     * 40.023095, 116.377823
     * 门店经纬度
     */
    @IndexField(fieldType = FieldType.GEO_POINT)
    @ApiModelProperty(value = "门店经纬度")
    private String location;

    /**
     * 业务员ID
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    /**
     * 搜索关键字
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty(value = "搜索关键字")
    private String keyword;

    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("区域id")
    private Long areaId;
}
