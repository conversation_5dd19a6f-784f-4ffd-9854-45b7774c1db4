package com.zksr.common.elasticsearch.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.CustomStockIntSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldType;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品公共信息
 * @date 2024/3/1 10:56
 */
@Data
public class EsProduct {

    /** 上架商品ID */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("上架商品唯一ID")
    private Long itemId;

    /** 商品类型, local本地, global 全国 */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("商品类型, local本地, global 全国")
    private String type;

    /** 商品名称 */
    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_SMART)
    @ApiModelProperty("商品名称")
    private String spuName;

    /** 自定义关键字 */
    @IndexField(exist = false)
    @ApiModelProperty("自定义关键字")
    private String keywords;

    /** 关键字条件 */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("关键字条件")
    private String condition;

    /** 单位尺寸 */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("单位,1-小单位,2-中单位,3-大单位")
    private Integer unitSize;

    /** 单位 */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("单位")
    private String unit;

    /** 小单位 */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("中单位")
    private String midUnit;

    /** 单位 */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("大单位")
    private String largeUnit;

    /** 规格属性, 可能涉及搜索 */
    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_SMART)
    @ApiModelProperty("规格属性")
    private String properties;

    /** 条码 */
    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_SMART)
    @ApiModelProperty("条码")
    private String barcode;

    /** 中单位条码 */
    @IndexField(exist = false)
    @ApiModelProperty("中单位条码")
    private String midBarcode;

    /** 大单位条码 */
    @IndexField(exist = false)
    @ApiModelProperty("大单位条码")
    private String largeBarcode;

    /** 条码 */
    @IndexField(exist = false)
    @ApiModelProperty("条码")
    private String barcodes;

    /** 封面图片 */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("封面图片")
    private String thumb;

    /** 封面视频 */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("封面视频")
    private String thumbVideo;

    /** SPU ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("SPU ID")
    private Long spuId;

    /** 默认-1, 兼容全国和本地商品售后 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("区域城市默认-1, 兼容全国和本地商品售后")
    private Long areaId;

    /** 默认-1, 本地渠道 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("区域城市默认-1, 兼容全国和本地商品售后")
    private Long channelId;

    /** 默认-1, 兼容全国和本地商品 平台商城市分组id */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("区域城市默认-1, 兼容全国和本地商品售后")
    private Long groupId;

    /** SKU ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("SKU ID")
    private Long skuId;

    /** 管理分类ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("管理分类ID")
    private Long catgoryId;

    /** 平台商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("平台商ID")
    private Long sysCode;

    /** 展示三级分类ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("展示三级分类ID")
    private Long class3Id;

    /** 展示二级分类ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("展示二级分类ID")
    private Long class2Id;

    /** 展示一级分类ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("展示一级分类ID")
    private Long class1Id;

    /** 展示分类名称 */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("展示三级分类名称")
    private String className;

    /** 入驻商ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("入驻商ID")
    private Long supplierId;

    /** 入驻商名称 */
    @IndexField(fieldType = FieldType.KEYWORD)
    @ApiModelProperty("入驻商名称")
    private String supplierName;

    /** 品牌ID */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("品牌ID")
    private Long brandId;

    /** 品牌名称 */
    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_SMART)
    @ApiModelProperty("品牌名称")
    private String brandName;

    /** 标准价 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty("标准价")
    private BigDecimal markPrice;

    /** 中单位标准价 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty("中单位标准价")
    private BigDecimal midMarkPrice;

    /** 大单位标准价 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty("大单位标准价")
    private BigDecimal largeMarkPrice;

    /** 建议零售价, 原价 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty("小单位建议零售价, 原价")
    private BigDecimal suggestPrice;

    /** 建议零售价, 原价 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty("中单位建议零售价, 原价")
    private BigDecimal midSuggestPrice;

    /** 建议零售价, 原价 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty("大单位建议零售价, 原价")
    private BigDecimal largeSuggestPrice;

    /** 销售最低价 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty("销售最低价")
    private BigDecimal minPrice;

    /** 最低价规格 */
    @IndexField(fieldType = FieldType.DOUBLE)
    @ApiModelProperty("最低价规格")
    private BigDecimal minSkuName;

    /** 销量 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("销量")
    private Long saleQty;

    /** 库存 */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("是否还有库存,1-没有了,0-还有库存")
    private Integer notStock;

    /** 上架状态 */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("上架状态")
    private Integer shelfStatus;

    /** 小单位上架状态 */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("小单位上架状态")
    private Integer minShelfStatus;

    /** 中单位上架状态 */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("中单位架状态")
    private Integer midShelfStatus;

    /** 大单位上架状态 */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("大单位上架状态")
    private Integer largeShelfStatus;

    /** 价格码 */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("价格码")
    private Integer salePriceCode;


    /** 商品规格 */
    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_SMART)
    @ApiModelProperty("商品规格")
    private String specName;

    /** 产地 */
    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_SMART)
    @ApiModelProperty(value = "产地", example = "示例值")
    private String originPlace;

    /** 是否开启多规格 1-是 0-否 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "是否开启多规格 1-是 0-否",  example = "1-是 0-否")
    private Long isSpecs;


    /** 排序序号 */
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty(value = "排序序号", example = "1")
    private Integer sortNum;

    /** 起订 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "起订", example = "1")
    private Long minOq;

    /** 订货组数 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "订货组数", example = "1")
    private Long jumpOq;

    /** 限购 */
    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty("限购数量")
    private Long maxOq;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "epoch_millis")
    @ApiModelProperty("最新生产日期")
    private Date latestDate;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "epoch_millis")
    @ApiModelProperty("最后生产日期")
    private Date oldestDate;

    @IndexField(fieldType = FieldType.LONG)
    @ApiModelProperty(value = "无库存记录时间", notes = "记录毫秒值, 避免时间格式转换异常")
    private Long noStockTime;

    /** 中单位-起订 */
    @IndexField(exist = false)
    @ApiModelProperty("中单位-起订")
    private Long midMinOq;

    /** 中单位-订货组数 */
    @IndexField(exist = false)
    @ApiModelProperty("中单位-订货组数")
    private Long midJumpOq;

    /** 中单位-限购 */
    @IndexField(exist = false)
    @ApiModelProperty("中单位-限购")
    private Long midMaxOq;

    /** 大单位-起订 */
    @IndexField(exist = false)
    @ApiModelProperty("大单位-起订")
    private Long largeMinOq;

    /** 大单位-订货组数 */
    @IndexField(exist = false)
    @ApiModelProperty("大单位-订货组数")
    private Long largeJumpOq;

    /** 大单位-限购 */
    @IndexField(exist = false)
    @ApiModelProperty("大单位-限购")
    private Long largeMaxOq;

    /** 商品类型 0-普通商品, 1-组合商品*/
    @IndexField(fieldType = FieldType.INTEGER)
    @ApiModelProperty("0-普通商品, 1-组合商品")
    private Integer itemType = NumberPool.INT_ZERO;

    /** 中单位与最小单位转换比例*/
    @ApiModelProperty("中单位与最小单位转换比例")
    @IndexField(exist = false)
    private BigDecimal midSize;

    /** 大单位与小单位转换比例*/
    @ApiModelProperty("大单位与小单位转换比例")
    @IndexField(exist = false)
    private BigDecimal largeSize;

    @ApiModelProperty("小单位无库存时间")
    @IndexField(exist = false)
    private Date minNoStockTime;

    @ApiModelProperty("中单位无库存时间")
    @IndexField(exist = false)
    private Date midNoStockTime;

    @ApiModelProperty("大单位无库存时间")
    @IndexField(exist = false)
    private Date largeNoStockTime;

    /** 库存 */
    @IndexField(exist = false)
    @ApiModelProperty("库存")
    @JsonSerialize(using= CustomStockIntSerialize.class)
    private BigDecimal stock;

    public String getCondition() {
        ArrayList<String> fields = new ArrayList<>();
        fields.add(this.spuName);
        fields.add(this.brandName);
        fields.add(PropertyAndValDTO.getProperties(this.properties));
        fields.add(this.className);
        fields.add(this.barcodes);
        fields.add(this.supplierName);
        fields.add(this.keywords);
        return StringUtils.join(fields.stream().filter(Objects::nonNull).collect(Collectors.toList()), StringPool.COMMA);
    }

    public Date noStockTime(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return null;
        }
        if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType().intValue() && Objects.nonNull(midUnit)) return midNoStockTime;
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType().intValue() && Objects.nonNull(largeUnit)) return largeNoStockTime;
        return minNoStockTime;
    }

    public BigDecimal stockConvert(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return BigDecimal.ONE;
        }
        if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType().intValue() && Objects.nonNull(midUnit)) return midSize;
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType().intValue() && Objects.nonNull(largeUnit)) return largeSize;
        return BigDecimal.ONE;
    }
}
