package com.zksr.common.elasticsearch.service;

import com.zksr.common.elasticsearch.domain.EsHomePagesOrderAfterData;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;

import java.util.List;

/**
*
 *
* <AUTHOR>
* @date 2024/5/9 9:53
*/
public interface EsHomePagesOrderAfterDataService {

    /**
     * 初始化索引
     */
    void initIndex();

    /**
     * 批量保存信息
     * @param homePagesDataList
     * @return
     */
    Boolean saveBatchHomePagesOrderAfterData(List<EsHomePagesOrderAfterData> dataList);

    /**
     * 保存信息
     * @param homePagesData
     * @return
     */
    Boolean saveHomePagesOrderAfterData(EsHomePagesOrderAfterData data);

    /**
     * 查询ES信息
     * @param searchDTO
     * @return
     */
    EsHomePagesOrderAfterData searchHomePagesOrderAfterData(HomePagesSearchDTO searchDTO);


}
