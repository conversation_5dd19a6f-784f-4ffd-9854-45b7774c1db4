package com.zksr.common.elasticsearch.model.dto;

import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 三级管理分类推荐指数
 * @date 2025/2/5 10:00
 */
@Data
@ApiModel(description = "三级管理分类推荐指数")
@AllArgsConstructor
@NoArgsConstructor
public class SearchRecommendRate {

    @ApiModelProperty(value = "推荐类型,0-常购,1-管理分类,2-spuId", required = true)
    private Integer type = NumberPool.INT_ZERO;

    @ApiModelProperty(value = "三级管理分类ID 或者 spuId 根据type类型传参", required = true)
    private Long applyId;

    @ApiModelProperty(value = "推荐指数, 0.3 = 30%", required = true)
    private BigDecimal rate;

    /**
     * 获取spu实例
     * @param spuId spuId
     * @param rate  推荐比例
     * @return  spu推荐比例
     */
    public static SearchRecommendRate getInstanceSpu(Long spuId, BigDecimal rate) {
        return new SearchRecommendRate(NumberPool.INT_TWO, spuId, rate);
    }

    /**
     * 不是常购
     */
    public boolean notCg() {
        return type != 0;
    }
}
