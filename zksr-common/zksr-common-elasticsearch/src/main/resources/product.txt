DELETE product
PUT product
{
	"mappings": {
		"properties": {
			"_class": {
				"type": "text",
				"fields": {
					"keyword": {
						"type": "keyword",
						"ignore_above": 256
					}
				}
			},
			"activities": {
				"type": "long"
			},
			"brand": {
				"type": "long"
			},
			"brandName": {
				"type": "text",
				"fields": {
					"pinyin": {
						"type": "text",
						"analyzer": "pinyin"
					}
				},
				"analyzer": "standard"
			},
			"coupons": {
				"type": "long"
			},
			"createDate": {
				"type": "date",
				"format": "yyyy-MM-dd HH:mm:ss"
			},
			"groupIDs": {
				"type": "long"
			},
			"groups": {
				"type": "text",
				"fields": {
					"pinyin": {
						"type": "text",
						"analyzer": "pinyin"
					}
				},
				"analyzer": "standard"
			},
			"id": {
				"type": "keyword"
			},
			"isChild": {
				"type": "boolean"
			},
			"labels": {
				"type": "long"
			},
			"memberPrice": {
				"type": "double"
			},
			"navigations": {
				"type": "long"
			},
			"p_id": {
				"type": "long"
			},
			"priority": {
				"type": "long"
			},
			"salenum": {
				"type": "long"
			},
			"skuTitles": {
				"type": "text",
				"fields": {
					"pinyin": {
						"type": "text",
						"analyzer": "pinyin"
					}
				},
				"analyzer": "standard"
			},
			"specialPrice": {
				"type": "double"
			},
			"specials": {
				"type": "long"
			},
			"status": {
				"type": "long"
			},
			"title": {
				"type": "text",
				"fields": {
					"keyword": {
						"type": "keyword"
					},
					"pinyin": {
						"type": "text",
						"analyzer": "pinyin"
					}
				},
				"analyzer": "standard"
			},
			"touristPrice": {
				"type": "double"
			},
			"types": {
				"type": "long"
			},
			"updateDate": {
				"type": "date",
				"format": "yyyy-MM-dd HH:mm:ss"
			},
			"viewNum": {
				"type": "long"
			}
		}
	},
	"settings": {
		"analysis": {
			"filter": {
				"my_pinpin": {
					"remove_duplicated_term": "true",
					"keep_separate_first_letter": "false",
					"lowercase": "true",
					"type": "pinyin",
					"keep_full_pinyin": "true"
				},
				"my_filter": {
					"type": "pinyin",
					"keep_first_letter": "false"
				},
				"my_length": {
					"type": "length",
					"min": "2"
				}
			},
			"analyzer": {
				"cj_pinyin": {
					"tokenizer": "my_token"
				},
				"jj_pinyin": {
					"filter": [
						"my_filter",
						"my_length"
					],
					"tokenizer": "pinyin"
				},
				"leo_pinyin": {
					"filter": [
						"my_pinpin"
					],
					"type": "custom",
					"tokenizer": "pinyin"
				}
			},
			"tokenizer": {
				"my_token": {
					"type": "pinyin",
					"first_letter": "none",
					"keep_first_letter": "false"
				}
			}
		}
	}
}
