<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zksr</groupId>
        <artifactId>zksr-common</artifactId>
        <version>3.6.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zksr-common-mp</artifactId>

    <description>
        zksr-common-mp 数据相关-自动填充，id生成  TODO 拟改名 zksr-common-mp
    </description>

    <dependencies>

        <!-- Zksr-Mall Common Security -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-security</artifactId>
        </dependency>

        <!-- mybatis-plus 增强CRUD -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <!-- https://gitee.com/luoxiang723/uid-generator-mybatis-plus-spring-boot-starter.git -->
        <dependency>
            <groupId>com.chungkui</groupId>
            <artifactId>uid-generator-mybatis-plus-spring-boot-starter</artifactId>
            <version>1.1-bate</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis-spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.yulichang</groupId>
            <artifactId>mybatis-plus-join-boot-starter</artifactId> <!-- MyBatis 联表查询 -->
        </dependency>

    </dependencies>
</project>
