package com.zksr.common.database.annotation;

import com.zksr.common.core.constant.SystemConstants;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据权限过滤注解
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataScope
{
    /**
     * 部门表的别名
     */
    public String deptAlias() default "";

    /**
     * 用户表的别名
     */
    public String userAlias() default "";

    /**
     * 运营商表表别名
     * @return
     */
    public String dcAlias() default "";

    /**
     * 运营商隔离字段, dcAlias.dcFieldAlias IN ( dc_id or area_id )
     * @return
     */
    public String dcFieldAlias() default SystemConstants.AREA_ID;

    /**
     * 入驻商表别名
     * @return
     */
    public String supplierAlias() default "";

    /**
     * 入驻商字段名
     * @return
     */
    public String supplierFieldAlias() default SystemConstants.SUPPLIER_ID;

    /**
     * 权限字符（用于多个角色匹配符合要求的权限）默认根据权限注解@RequiresPermissions获取，多个权限用逗号分隔开来
     */
    public String permission() default "";
}
