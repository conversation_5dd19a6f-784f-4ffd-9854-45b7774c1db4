package com.zksr.common.database.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.zksr.common.core.constant.MallSecurityConstants;
import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.database.handler.ZksrMetaObjectHandler;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2018-08-10
 */
@Configuration
@MapperScan("com.zksr.**.mapper")
public class MybatisPlusConfig {

    private static final Logger logger = LoggerFactory.getLogger(MybatisPlusConfig.class);

    public static List<String> ignoreTables = Arrays.asList(
            //"sys_partner",
            "sys_dict_type",
            "sys_dict_data",
            "sys_menu",
            "sys_config",
            "sys_post",
            "sys_dept",
            //"sys_user",
            "sys_user_role",
            "sys_user_post",
//            "sys_role",
            "sys_role_dept",
            "sys_role_menu",
            "sys_notice",
            "sys_oper_log",
            "sys_logininfor",
            "sys_province",
//            "sys_area",
            //"sys_area",
            "sys_city",
            "sys_job",
            "sys_job_log",
            "sys_student",
            "sys_openability",
            //            "sys_supplier",
            "gen_table",
            "gen_table_column",
            "prdt_platform_sku",
            "prdt_platform_spu",
            "prdt_platform_property_val",
            "prdt_platform_property",
            "sys_bank_channel",
            "sys_area_city",
            "visual_setting_master",
            "visual_setting_detail",
            "visual_setting_template",
            "sys_print_template",
            "sys_software",
            "dim_month",
            "dim_date",
            "dim_dict_data"
    );

    /**
     * 新多租户插件配置,一缓和二缓遵循mybatis的规则,需要设置 MybatisConfiguration#useDeprecatedExecutor = false 避免缓存万一出现问题
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 平台商租户SQL处理器
        interceptor.addInnerInterceptor(new SysCodeTenantLineInnerInterceptor(new SysCodeTenantLineHandler()));

        // 如果用了分页插件注意先 add TenantLineInnerInterceptor 再 add PaginationInnerInterceptor
        // 用了分页插件必须设置 MybatisConfiguration#useDeprecatedExecutor = false
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        //乐观锁拦截器
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        return interceptor;
    }

    /**
     * 自动填充数据
     */
    @Bean
    @ConditionalOnMissingBean(ZksrMetaObjectHandler.class)
    public ZksrMetaObjectHandler zksrMetaObjectHandler() {
        return new ZksrMetaObjectHandler();
    }

    //TODO mp版本问题
//    @Bean
//    public ConfigurationCustomizer configurationCustomizer() {
//        return configuration -> configuration.setUseDeprecatedExecutor(false);
//    }

}
