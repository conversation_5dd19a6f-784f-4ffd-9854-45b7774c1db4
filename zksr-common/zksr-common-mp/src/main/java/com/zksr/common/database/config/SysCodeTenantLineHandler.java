package com.zksr.common.database.config;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.zksr.common.core.constant.MallSecurityConstants;
import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.ToolUtil;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import org.apache.dubbo.common.logger.Logger;
import org.apache.dubbo.common.logger.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 平台商租户控制器
 * @date 2024/11/18 15:46
 */
public class SysCodeTenantLineHandler implements TenantLineHandler {

    private static final Logger logger = LoggerFactory.getLogger(SysCodeTenantLineHandler.class);

    /**
     * 默认规则
     * @return
     */
    @Override
    @Deprecated
    public Expression getTenantId() {
        if (ToolUtil.isEmpty(SecurityContextHolder.get(SecurityConstants.LOGIN_USER)) &&
                ToolUtil.isEmpty(SecurityContextHolder.get(MallSecurityConstants.LOGIN_MEMBER)) &&
                ToolUtil.isEmpty(SecurityContextHolder.get(OpenapiSecurityConstants.LOGIN_OPENSOURCE)) &&
                !SecurityConstants.EXPORT.equals(SecurityContextHolder.get(SecurityConstants.FROM_SOURCE))) {
            logger.info("未获取到用户");
            //throw new RuntimeException("用户未认证，不允许访问");
            return null;
        }

        if (ToolUtil.isEmpty(SecurityContextHolder.getSysCode())) {
            return null;
        }
        long sysCode = SecurityContextHolder.getSysCode();
        return new LongValue(sysCode);
    }

    /**
     * 新规则
     * @param leftExpression
     * @return
     */
    public Expression getTenantId(Expression leftExpression) {
        if (ToolUtil.isEmpty(SecurityContextHolder.get(SecurityConstants.LOGIN_USER)) &&
                ToolUtil.isEmpty(SecurityContextHolder.get(MallSecurityConstants.LOGIN_MEMBER)) &&
                ToolUtil.isEmpty(SecurityContextHolder.get(OpenapiSecurityConstants.LOGIN_OPENSOURCE)) &&
                !SecurityConstants.EXPORT.equals(SecurityContextHolder.get(SecurityConstants.FROM_SOURCE))) {
            logger.info("未获取到用户");
            //throw new RuntimeException("用户未认证，不允许访问");
            return null;
        }

        if (ToolUtil.isEmpty(SecurityContextHolder.getSysCode()) && ToolUtil.isEmpty(SecurityContextHolder.getSysCodeList())) {
            return null;
        }
        InExpression inExpression = new InExpression();
        // 优先使用多平台商
        List<Long> sysCodeList = SecurityContextHolder.getSysCodeList();
        // IN 条件
        ExpressionList expressionList = new ExpressionList();
        // IN 条件值
        List<Expression> itemList = new ArrayList<>();
        expressionList.setExpressions(itemList);
        if (Objects.nonNull(sysCodeList) && !sysCodeList.isEmpty()) {
            for (Long sysCode : sysCodeList) {
                itemList.add(new LongValue(sysCode));
            }
        } else {
            itemList.add(new LongValue(SecurityContextHolder.getSysCode()));
        }
        inExpression.setLeftExpression(leftExpression);
        inExpression.setRightItemsList(expressionList);
        return inExpression;
    }

    @Override
    public String getTenantIdColumn() {
        return SecurityConstants.SYS_CODE;
    }

    // 这是 default 方法,默认返回 false 表示所有表都需要拼多租户条件
    @Override
    public boolean ignoreTable(String tableName) {
        if (MybatisPlusConfig.ignoreTables.stream().anyMatch(
                (t) -> t.equalsIgnoreCase(tableName))) {
            return true;
        }

        if (ToolUtil.isEmpty(SecurityContextHolder.get(SecurityConstants.LOGIN_USER)) &&
                ToolUtil.isEmpty(SecurityContextHolder.get(MallSecurityConstants.LOGIN_MEMBER)) &&
                ToolUtil.isEmpty(SecurityContextHolder.get(OpenapiSecurityConstants.LOGIN_OPENSOURCE)) &&
                !SecurityConstants.EXPORT.equals(SecurityContextHolder.get(SecurityConstants.FROM_SOURCE))) {
            return true;
        }

        List<Long> sysCodeList = SecurityContextHolder.getSysCodeList();
        if (Objects.nonNull(sysCodeList) && !sysCodeList.isEmpty()) {
            return false;
        } else {
            //用户没有sys_code，不自动添加sys_code
            if ((ToolUtil.isNotEmpty(SecurityContextHolder.get(SecurityConstants.LOGIN_USER))
                    || ToolUtil.isNotEmpty(SecurityContextHolder.get(MallSecurityConstants.LOGIN_MEMBER))
                    || ToolUtil.isNotEmpty(SecurityContextHolder.get(OpenapiSecurityConstants.LOGIN_OPENSOURCE)))
                    && (ToolUtil.isEmpty((SecurityContextHolder.getSysCode()))) || SecurityContextHolder.getSysCode() == 0L) {
                return true;
            }
        }
        //到这一步说明UserContext 是有登录信息的
        //logger.error("ignoreTable:token:{}", UserContext.getUser().getToken());
        return false;
    }
}
