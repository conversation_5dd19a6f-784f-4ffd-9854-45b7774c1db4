package com.zksr.common.database.config;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.schema.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: sysCode 租户拦截器
 * @date 2024/11/18 15:59
 */
public class SysCodeTenantLineInnerInterceptor extends TenantLineInnerInterceptor {

    public SysCodeTenantLineInnerInterceptor(TenantLineHandler tenantLineHandler) {
        super(tenantLineHandler);
    }

    @Override
    public Expression buildTableExpression(final Table table, final Expression where, final String whereSegment) {
        if (super.getTenantLineHandler().ignoreTable(table.getName())) {
            return null;
        }
        SysCodeTenantLineHandler tenantLineHandler = (SysCodeTenantLineHandler) super.getTenantLineHandler();
        return tenantLineHandler.getTenantId(getAliasColumn(table));
    }
}
