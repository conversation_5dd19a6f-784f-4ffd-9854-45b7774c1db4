package com.zksr.common.database.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.ToolUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 自动填充时间字段
 * <AUTHOR>
 */
@Component
public class ZksrMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        if(null == getFieldValByName("createTime", metaObject)){
            this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
        }

        if(null == getFieldValByName("sysCode", metaObject)){
            this.strictInsertFill(metaObject, "createBy", String.class, SecurityContextHolder.getUserName());
            if(ToolUtil.isNotEmpty(SecurityContextHolder.getSysCode())){
                setFieldValByName("sysCode", SecurityContextHolder.getSysCode(), metaObject);
            }

        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
        this.strictUpdateFill(metaObject, "updateBy", String.class, SecurityContextHolder.getUserName());
    }
}
