package com.zksr.common.database.config;

import com.baidu.fsg.uid.UidGenerator;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 自定义ID生成器
 *
 */
@Component
public class CustomIdGenerator implements IdentifierGenerator {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final AtomicLong al = new AtomicLong(1);

    private final IdentifierGenerator identifierGenerator = new DefaultIdentifierGenerator();

    private UidGenerator uidGenerator;

    //解决循环依赖问题，使用懒加载
    @Autowired
    public CustomIdGenerator(@Lazy UidGenerator uidGenerator) {
        this.uidGenerator = uidGenerator;
    }

    @Override
    public Long nextId(Object entity) {
        //final long id = al.getAndAdd(1);
        final long id = uidGenerator.getUID();
//        final long id = identifierGenerator.nextId(entity).longValue();
        logger.info("生成主键值->:{}", id);
        return id;
    }

    public Long nextId() {
        return  uidGenerator.getUID();
    }
}
