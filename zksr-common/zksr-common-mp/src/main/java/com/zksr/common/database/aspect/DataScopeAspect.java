package com.zksr.common.database.aspect;

import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.text.Convert;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.domain.BaseEntity;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.utils.DcUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.RemoteUserService;
import com.zksr.system.api.domain.SysRole;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 数据过滤处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class DataScopeAspect {
    /**
     * 全部数据权限
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 自定数据权限
     */
    public static final String DATA_SCOPE_CUSTOM = "2";

    /**
     * 部门数据权限
     */
    public static final String DATA_SCOPE_DEPT = "3";

    /**
     * 部门及以下数据权限
     */
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";

    /**
     * 仅本人数据权限
     */
    public static final String DATA_SCOPE_SELF = "5";

    /**
     * 数据权限过滤关键字
     */
    public static final String DATA_SCOPE = "dataScope";

    @Autowired
    private RedisService redisService;

    @Before("@annotation(controllerDataScope)")
    public void doBefore(JoinPoint point, DataScope controllerDataScope) throws Throwable {
        clearDataScope(point);
        handleDataScope(point, controllerDataScope);
    }

    protected void handleDataScope(final JoinPoint joinPoint, DataScope controllerDataScope) {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNotNull(loginUser)) {
            SysUser currentUser = loginUser.getSysUser();
            // 如果是超级管理员，则不过滤数据
            if (StringUtils.isNotNull(currentUser) && !currentUser.isAdmin()) {
                String permission = StringUtils.defaultIfEmpty(controllerDataScope.permission(), SecurityContextHolder.getPermission());
                dataScopeFilter(joinPoint, currentUser, controllerDataScope, permission);
            }
        }
    }

    /**
     * 数据范围过滤
     *
     * @param joinPoint          切点
     * @param user               用户
     * @param dataScopAnnotation 数据范围
     * @param permission         权限字符
     */
    public void dataScopeFilter(JoinPoint joinPoint, SysUser user, DataScope dataScopAnnotation, String permission) {
        StringBuilder sqlString = new StringBuilder();
        List<String> conditions = new ArrayList<String>();
        String deptAlias = dataScopAnnotation.deptAlias();
        String userAlias = dataScopAnnotation.userAlias();

        for (SysRole role : user.getRoles()) {
            String dataScope = role.getDataScope();
            if (!DATA_SCOPE_CUSTOM.equals(dataScope) && conditions.contains(dataScope)) {
                continue;
            }
            if (StringUtils.isNotEmpty(permission) && StringUtils.isNotEmpty(role.getPermissions())
                    && !StringUtils.containsAny(role.getPermissions(), Convert.toStrArray(permission))) {
                continue;
            }
            /*if (DATA_SCOPE_ALL.equals(dataScope)) {
                sqlString = new StringBuilder();
                conditions.add(dataScope);
                break;
            }*/
            // 只有admin才拥有所有权限
            if (role.isAdmin()) {
                sqlString = new StringBuilder();
                conditions.add(dataScope);
                break;
            }
            /*else if (DATA_SCOPE_CUSTOM.equals(dataScope)) {
                sqlString.append(StringUtils.format(
                        " OR {}.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = {} ) ", deptAlias,
                        role.getRoleId()));
            } else if (DATA_SCOPE_DEPT.equals(dataScope)) {
                if (StringUtils.isNotBlank(deptAlias)) {
                    sqlString.append(StringUtils.format(" OR {}.dept_id = {} ", deptAlias, user.getDeptId()));
                }
            } else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope)) {
                sqlString.append(StringUtils.format(
                        " OR {}.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = {} or find_in_set( {} , ancestors ) )",
                        deptAlias, user.getDeptId(), user.getDeptId()));
            } else if (DATA_SCOPE_SELF.equals(dataScope)) {
                if (StringUtils.isNotBlank(userAlias)) {
                    sqlString.append(StringUtils.format(" OR {}.user_id = {} ", userAlias, user.getUserId()));
                } else {
                    // 数据权限为仅本人且没有userAlias别名不查询任何数据
                    sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
                }
            }*/
            if (SystemConstants.FUNC_SCOPE_DC.equals(role.getFuncScop())) {
                if (StringUtils.isNotBlank(dataScopAnnotation.dcAlias())) {
                    if (SystemConstants.DC_ID.equals(dataScopAnnotation.dcFieldAlias())) {
                        sqlString.append(StringUtils.format(" AND {}.dc_id = {} ", dataScopAnnotation.dcAlias(), user.getDcId()));
                    } else if((SystemConstants.SUPPLIER_ID.equals(dataScopAnnotation.dcFieldAlias()))){
                        List<Long> supplierIdList = DcUtils.getSupplierList(user.getDcId());
                        if (Objects.nonNull(supplierIdList)) {
                            sqlString.append(StringUtils.format(" AND {}.{} IN ({}) ", dataScopAnnotation.dcAlias(), dataScopAnnotation.dcFieldAlias(), StringUtils.join(supplierIdList, StringPool.COMMA)));
                        } else {
                            sqlString.append(StringUtils.format(" AND {}.{} IN ({}) ", dataScopAnnotation.dcAlias(), dataScopAnnotation.dcFieldAlias(), -1));
                        }
                    } else {
                        List<Long> areaIdList = DcUtils.getAreaList(user.getDcId());
                        if (Objects.nonNull(areaIdList)) {
                            sqlString.append(StringUtils.format(" AND {}.{} IN ({}) ", dataScopAnnotation.dcAlias(), dataScopAnnotation.dcFieldAlias(), StringUtils.join(areaIdList, StringPool.COMMA)));
                        } else {
                            sqlString.append(StringUtils.format(" AND {}.{} IN ({}) ", dataScopAnnotation.dcAlias(), dataScopAnnotation.dcFieldAlias(), -1));
                        }
                    }
                }
            }
            if (SystemConstants.FUNC_SCOPE_SUPPLIER.equals(role.getFuncScop())) {
                if (StringUtils.isNotBlank(dataScopAnnotation.supplierAlias())) {
                    sqlString.append(StringUtils.format(" AND {}.{} = {} ", dataScopAnnotation.supplierAlias(), dataScopAnnotation.supplierFieldAlias(), user.getSupplierId()));
                }
            }
            conditions.add(dataScope);
        }

        // 多角色情况下，所有角色都不包含传递过来的权限字符，这个时候sqlString也会为空，所以要限制一下,不查询任何数据
        if (StringUtils.isEmpty(conditions)) {
            if (StringUtils.isNotEmpty(deptAlias)) {
                //sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
            }
        }

        if (StringUtils.isNotBlank(sqlString.toString())) {
            Object params = joinPoint.getArgs()[0];
            if (StringUtils.isNotNull(params) && params instanceof BaseEntity) {
                BaseEntity baseEntity = (BaseEntity) params;
                baseEntity.getParams().put(DATA_SCOPE, " AND (" + sqlString.substring(4) + ")");
            }
        }
    }

    /**
     * 拼接权限sql前先清空params.dataScope参数防止注入
     */
    private void clearDataScope(final JoinPoint joinPoint) {
        Object params = joinPoint.getArgs()[0];
        if (StringUtils.isNotNull(params) && params instanceof BaseEntity) {
            BaseEntity baseEntity = (BaseEntity) params;
            baseEntity.getParams().put(DATA_SCOPE, StringPool.EMPTY);
        }
    }
}
