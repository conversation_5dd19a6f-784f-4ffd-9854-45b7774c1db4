package com.zksr.common.third.zfb;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.zksr.common.third.zfb.vo.AlipaySystemOauthVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付宝工具
 * @date 2024/8/9 14:45
 */
public class ZfbUtils {
    public static AlipaySystemOauthVO getXcxAuthInfo(String code, String appid, String publicKey, String privateKey) {
        // 初始化SDK
        try {
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig(appid, privateKey, publicKey));
            // 构造请求参数以调用接口
            AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
            // 设置刷新令牌
            request.setRefreshToken(RandomUtil.randomNumbers(20));
            // 设置授权码
            request.setCode(code);
            // 设置授权方式
            request.setGrantType("authorization_code");
            AlipaySystemOauthTokenResponse response = alipayClient.execute(request);
            return BeanUtil.toBean(response, AlipaySystemOauthVO.class);
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }
    }

    private static AlipayConfig getAlipayConfig(String appId, String privateKey, String alipayPublicKey) {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
        alipayConfig.setAppId(appId);
        alipayConfig.setPrivateKey(privateKey);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(alipayPublicKey);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        return alipayConfig;
    }
}
