package com.zksr.common.third.wx.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 获取商户助手商家绑定信息
 * @date 2024/7/31 9:44
 */
@Data
public class RetailInfoRespVO {

    @ApiModelProperty("-1=系统繁忙，请稍后重试, 0=成功, 40001=token 无效, 48001=小程序无该api权限，反馈给对接人开通, 9404101=非法参数, 9404102=该店主不存在、未认证或者未授权给你, 9404103=请求过于频繁，请稍后重试")
    private Integer state;

    private List<Merchant> merchants = new ArrayList<>();

    @Data
    public static class Merchant {

        @ApiModelProperty("手机号")
        private String mobilePhone;

        @ApiModelProperty("门店名称")
        private String retailName;

        @ApiModelProperty("营业执照注册号")
        private String identification;

        @ApiModelProperty("门店负责人openid")
        private String openid;
    }
}
