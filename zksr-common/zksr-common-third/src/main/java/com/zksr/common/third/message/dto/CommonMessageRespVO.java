package com.zksr.common.third.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/6/13 16:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel(description = "消息通知结果记录")
public class CommonMessageRespVO {

    @ApiModelProperty("消息记录")
    private CommonMessageDTO commonMessage;

    @ApiModelProperty("消息结果")
    private boolean success = true;

    @ApiModelProperty("发送失败原因")
    private String msg;

    public CommonMessageRespVO(CommonMessageDTO commonMessage) {
        this.commonMessage = commonMessage;
    }
}
