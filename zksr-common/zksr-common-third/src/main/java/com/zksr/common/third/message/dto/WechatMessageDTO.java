package com.zksr.common.third.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/6/13 16:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel(description = "小程序消息")
public class WechatMessageDTO {

    @ApiModelProperty("消息模版ID")
    private String templateId;

    @ApiModelProperty("接受用户openid")
    private String toUser;

    @ApiModelProperty("接受消息appid")
    private String appid;

    @ApiModelProperty("跳转路径")
    private String path;

    @ApiModelProperty("跳转的appid")
    private String pathAppid;

    @ApiModelProperty("秘钥")
    private String accessToken;

    @ApiModelProperty("消息主体")
    private Map<String, String> body;
}
