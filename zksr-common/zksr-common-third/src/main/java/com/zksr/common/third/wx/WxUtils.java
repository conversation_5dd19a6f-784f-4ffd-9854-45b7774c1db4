package com.zksr.common.third.wx;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.file.FileUtils;
import com.zksr.common.third.message.dto.WechatMessageDTO;
import com.zksr.common.third.wx.dto.WxCode2SessionResponse;
import com.zksr.common.third.wx.vo.RetailInfoRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.Proxy;
import java.net.URL;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


@SuppressWarnings("all")
public class WxUtils {
	private static final Logger logger = LoggerFactory.getLogger(WxUtils.class);


	public static WxCode2SessionResponse getOpenId(String wxMiniAppId, String wxMiniAppSecret, String code, Proxy proxy) throws IOException {
		BufferedReader bd = null;
		HttpURLConnection httpURLConn = null;
		try {
			URL url = new URL("https://api.weixin.qq.com/sns/jscode2session?appid=" + wxMiniAppId + "&secret="
					+ wxMiniAppSecret + "&js_code=" + code + "&grant_type=authorization_code");
			logger.info("查询微信openId,{}", url);
			if(null != proxy){
				logger.info("url[{}],使用代理[{}]", url,proxy.toString());
				httpURLConn = (HttpURLConnection) url.openConnection(proxy);
			}else {
				httpURLConn = (HttpURLConnection) url.openConnection();
			}

			httpURLConn.setDoOutput(true);
			httpURLConn.setRequestMethod("POST");
			httpURLConn.connect();
			InputStream in = httpURLConn.getInputStream();
			bd = new BufferedReader(new InputStreamReader(in));
			String temp = bd.readLine();
			logger.error("temp:" + temp);
			bd.close();
			httpURLConn.disconnect();
			//{"session_key":"Rivr4lpOuXwjCx3Lp1r0zQ==","openid":"omxeH4kzox8uqp2ygu3WRg4PQ3A4","unionid":"oSDZnvxCcAlEY-d9HpYxeEwXjaOQ"}
			WxCode2SessionResponse response = JSONObject.parseObject(temp, WxCode2SessionResponse.class );
            return response;
		} catch (Exception e) {
			logger.error("查询微信openId失败，", e);
			throw e;
		}
	}

	public static WxCode2SessionResponse getPublishOpenId(String publishAppId, String publishAppSecret, String code) throws IOException {
		BufferedReader bd = null;
		HttpURLConnection httpURLConn = null;
		try {
			URL url = new URL("https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + publishAppId + "&secret=" + publishAppSecret + "&code=" + code + "&grant_type=authorization_code");
			System.out.println("############# url ############## " + url);
			httpURLConn = (HttpURLConnection) url.openConnection();
			httpURLConn.setDoOutput(true);
			httpURLConn.setRequestMethod("POST");
			httpURLConn.connect();
			InputStream in = httpURLConn.getInputStream();
			bd = new BufferedReader(new InputStreamReader(in));
			String temp = bd.readLine();
			logger.error("temp:" + temp);
			bd.close();
			httpURLConn.disconnect();
			//{"session_key":"Rivr4lpOuXwjCx3Lp1r0zQ==","openid":"omxeH4kzox8uqp2ygu3WRg4PQ3A4","unionid":"oSDZnvxCcAlEY-d9HpYxeEwXjaOQ"}
			WxCode2SessionResponse response = JSONObject.parseObject(temp, WxCode2SessionResponse.class );
            return response;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw e;
		}
	}

	public static String getAccessToken(String wxMiniAppId,String wxMiniAppSecret) {
		BufferedReader bd = null;
		HttpURLConnection httpURLConn = null;
		try {
			URL url = new URL("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + wxMiniAppId + "&secret="
					+ wxMiniAppSecret);
			httpURLConn = (HttpURLConnection)url.openConnection();
			httpURLConn.setDoOutput(true);
			httpURLConn.setRequestMethod("POST");
			httpURLConn.connect();
			InputStream in = httpURLConn.getInputStream();
			bd = new BufferedReader(new InputStreamReader(in));
			String temp = bd.readLine();
			bd.close();
			httpURLConn.disconnect();
			JSONObject json = JSONObject.parseObject(temp);
			logger.error("json:" + json.toString());
			System.out.println("############# access_token ############## " + json.getString("access_token"));
            if(ToolUtil.isNotEmpty(json.getString("access_token"))){
            	return json.getString("access_token");
            }
            return null;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return null;
		}
	}

	public static String uploadB2bPic(String url, String accessToken) {
		// 构建请求URL
		String requestUrl = StrUtil.format("https://api.weixin.qq.com/retail/B2b/retailuploadmchfile?access_token={}", accessToken);
		// 创建一个HttpPost请求
		Map<String, Object> paramMap = new HashMap<>();
		String fileName = FileUtils.getName(url);
		String tmpPath = "/tmp/" + fileName;
		// 设置文件名
		paramMap.put("file_name", fileName);
		// 设置文件数据
		File file = FileUtil.writeBytes(HttpRequest.get(requestUrl).execute().bodyBytes(), tmpPath);
		paramMap.put("file", FileUtils.fileToBase64(file.getAbsoluteFile()));
		try {
			String result = HttpUtil.post(requestUrl, JSON.toJSONString(paramMap));
			JSONObject resultObj = JSON.parseObject(result);
			if (resultObj.containsKey("file_id")) {
				return resultObj.getString("file_id");
			} else {
				throw new ServiceException(resultObj.getString("errmsg"));
			}
		} finally {
			file.delete();
		}
	}

	public static String uploadB2bPicByBase64(String url, String base64File, String accessToken) {
		// 构建请求URL
		String requestUrl = StrUtil.format("https://api.weixin.qq.com/retail/B2b/retailuploadmchfile?access_token={}", accessToken);
		// 创建一个HttpPost请求
		Map<String, Object> paramMap = new HashMap<>();
		String fileName = FileUtils.getName(url);
		String tmpPath = "/tmp/" + fileName;
		// 设置文件名
		paramMap.put("file_name", fileName);
		// 设置文件数据
		paramMap.put("file", base64File);

		String result = HttpUtil.post(requestUrl, JSON.toJSONString(paramMap));
		JSONObject resultObj = JSON.parseObject(result);
		if (resultObj.containsKey("file_id")) {
			return resultObj.getString("file_id");
		} else {
			throw new ServiceException(resultObj.getString("errmsg"));
		}
	}

	/**
	 * 小程序消息
	 */
	public static CommonWxAppletMsgResp sendCommonWxAppletMsg(WechatMessageDTO messageDTO) {
		Map<String, Object> requestMap = new HashMap<String, Object>();
		// 消息内容
		Map<String, Object> dataMap = new HashMap<String, Object>();
		messageDTO.getBody().forEach((name, value) -> {
			if (StringUtils.isNotEmpty(value) && value.length() > 20) {
				if (NumberUtil.isNumber(value)) {
					// number类型直接裁剪
					dataMap.put(name, MapUtil.of("value", value.substring(0, 20)));
				} else{
					// 字符类型省略
					dataMap.put(name, MapUtil.of("value", StrUtil.maxLength(value, 17)));
				}
			} else {
				dataMap.put(name, MapUtil.of("value", value));
			}
		});
		// 消息主体
		requestMap.put("touser", messageDTO.getToUser());
		requestMap.put("template_id", messageDTO.getTemplateId());
		requestMap.put("data", dataMap);
		if (StringUtils.isNotEmpty(messageDTO.getPath())) {
			requestMap.put("page", messageDTO.getPath());
		}
		String requestJson = JSON.toJSONString(requestMap);
		logger.info("========com.zksr.common.third.wx.WxUtils.sendCommonWxAppletMsg=======" + requestJson);
		String urlStr = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + messageDTO.getAccessToken();
		String returnJson = HttpUtil.post(urlStr, requestJson, 3000);
		logger.info("========returnJson=======" + returnJson);
		JSONObject res = JSON.parseObject(returnJson);
		CommonWxAppletMsgResp resp = new CommonWxAppletMsgResp();
		// {"errcode":43101,"errmsg":"user refuse to accept thee msg rid: 6751d84a-421c6f8a-76358382"}
		// {"errcode":0,"errmsg":"ok","msgid":3755156960174309376}
		if (res.getInteger("errcode") == 0) {
			resp.setMsg(res.getString("msgid"));
		} else {
			resp.setSuccess(false);
			resp.setMsg(res.getString("errmsg"));
		}
		return resp;
	}

	/**
	 * 公众号消息
	 * @param messageDTOS
	 */
	public static CommonWxAppletMsgResp sendCommonPubMsg(WechatMessageDTO messageDTO) {

		Map<String, Object> requestMap = new HashMap<String, Object>();

		// 跳转信息
		Map<String, Object> miniprogram = new HashMap<>();
		if (StringUtils.isNotEmpty(messageDTO.getPathAppid())) {
			miniprogram.put("appid", messageDTO.getPathAppid());
			if (StringUtils.isNotEmpty(messageDTO.getPath())) {
				miniprogram.put("pagepath", messageDTO.getPath());
			} else {
				miniprogram.put("pagepath", "");
			}
		}
		// 消息内容
		Map<String, Object> dataMap = new HashMap<String, Object>();
		messageDTO.getBody().forEach((name, value) -> {
			if (StringUtils.isNotEmpty(value) && value.length() > 20) {
				if (NumberUtil.isNumber(value)) {
					// number类型直接裁剪
					dataMap.put(name, MapUtil.of("value", value.substring(0, 20)));
				} else{
					// 字符类型省略
					dataMap.put(name, MapUtil.of("value", StrUtil.maxLength(value, 17)));
				}
			} else {
				dataMap.put(name, MapUtil.of("value", value));
			}
		});

		// 消息主体
		requestMap.put("touser", messageDTO.getToUser());
		requestMap.put("template_id", messageDTO.getTemplateId());
		requestMap.put("appid", messageDTO.getAppid());
		requestMap.put("data", dataMap);
		if (!miniprogram.isEmpty()) {
			requestMap.put("miniprogram", miniprogram);
		}
		String requestJson = JSON.toJSONString(requestMap);
		logger.info("========com.zksr.common.third.wx.WxUtils.sendCommonPubMsg=======" + requestJson);
		String urlStr = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + messageDTO.getAccessToken();
		String returnJson = HttpUtil.post(urlStr, requestJson, 3000);
		logger.info("========returnJson=======" + returnJson);
		JSONObject res = JSON.parseObject(returnJson);
		CommonWxAppletMsgResp resp = new CommonWxAppletMsgResp();
		// {"errcode":43101,"errmsg":"user refuse to accept thee msg rid: 6751d84a-421c6f8a-76358382"}
		// {"errcode":0,"errmsg":"ok","msgid":3755156960174309376}
		if (res.getInteger("errcode") == 0 && "ok".equals(res.getString("errmsg"))) {
			resp.setMsg(res.getString("msgid"));
		} else {
			resp.setSuccess(false);
			resp.setMsg(res.getString("errmsg"));
		}
		return resp;
	}

	/**
	 * 微信商家助手消息
	 * @param messageDTOS
	 */
	public static CommonWxAppletMsgResp sendCommonWxMerchantMsg(WechatMessageDTO messageDTO) {
		Map<String, Object> requestMap = new HashMap<String, Object>();

		// 消息内容
		Map<String, Object> dataMap = new HashMap<String, Object>();
		messageDTO.getBody().forEach((name, value) -> {
			if (StringUtils.isNotEmpty(value) && value.length() > 20) {
				if (NumberUtil.isNumber(value)) {
					// number类型直接裁剪
					dataMap.put(name, value.substring(0, 20));
				} else{
					// 字符类型省略
					dataMap.put(name, StrUtil.maxLength(value, 17));
				}
			} else {
				dataMap.put(name, value);
			}
		});
		// 消息主体
		requestMap.put("content", JSON.toJSONString(dataMap));
		requestMap.put("type", messageDTO.getTemplateId());
		requestMap.put("to_user_list", ListUtil.toList(messageDTO.getToUser()));
		String requestJson = JSON.toJSONString(requestMap);
		logger.info("========com.zksr.common.third.wx.WxUtils.sendCommonWxMerchantMsg=======" + requestJson);
		String urlStr = "https://api.weixin.qq.com/wxa/business/retailnotifybusiness?access_token=" + messageDTO.getAccessToken();
		String returnJson = HttpUtil.post(urlStr, requestJson, 3000);
		logger.info("========returnJson=======" + returnJson);
		JSONObject res = JSON.parseObject(returnJson);
		CommonWxAppletMsgResp resp = new CommonWxAppletMsgResp();
		// {"errcode":43101,"errmsg":"user refuse to accept thee msg rid: 6751d84a-421c6f8a-76358382"}
		// {"errcode":0,"errmsg":"ok","msgid":3755156960174309376}
		if (res.getInteger("errcode") == 0 && "ok".equals(res.getString("errmsg"))) {
			resp.setMsg(res.getString("msgid"));
		} else {
			resp.setSuccess(false);
			resp.setMsg(res.getString("errmsg"));
		}
		return resp;
	}

	/**
	 * 获取微信门店录入信息
	 * @param openid		微信openid
	 * @param accessToken	token
	 */
	public static RetailInfoRespVO getRetailInfo(String openid, String accessToken) {

		String url = StringUtils.format("https://api.weixin.qq.com/wxa/business/getretailinfo?access_token={}", accessToken);
		HashMap<String, String> req = new HashMap<>();
		req.put("openid", openid);
		String post = HttpUtil.post(url, JSON.toJSONString(req));
		// {"errcode":48001,"errmsg":"api unauthorized rid: 66a996a1-0c26b4fa-3a7c9796"}

		RetailInfoRespVO respVO = new RetailInfoRespVO();
		JSONObject restObj = JSON.parseObject(post);
		logger.info("/wxa/business/getretailinf 请求结果={}", post);
		if ("0".equals(restObj.getString("errcode"))) {
			JSONArray array = restObj.getJSONArray("info");
			for (int i = array.size() - 1; i >= 0; i--) {
				RetailInfoRespVO.Merchant newBean = BeanUtil.fillBeanWithMap(array.getJSONObject(i), new RetailInfoRespVO.Merchant(), true, true);
				respVO.getMerchants().add(newBean);
			}
			respVO.setState(Integer.parseInt(restObj.getString("errcode")));
		} else {
			respVO.setState(Integer.parseInt(restObj.getString("errcode")));
		}
		return respVO;
	}

	/**
	 * 门店信息预录入
	 * @param contactPhone	手机号
	 * @param branchName	门店名称
	 * @param province		省份
	 * @param city			城市
	 * @param area			区
	 * @param address		详情地址
	 * @param accessToken	token
	 */
	public static String createRetail(String contactPhone, String branchName, String province, String city, String area, String address, String accessToken) {
		String url = StringUtils.format("https://api.weixin.qq.com/wxa/business/batchcreateretail?access_token={}", accessToken);
		HashMap<String, String> req = new HashMap<>();
		req.put("mobile_phone", contactPhone);
		req.put("retail_name", branchName);
		req.put("address_province", province);
		req.put("address_city", city);
		req.put("address_region", area);
		req.put("address_street", address);
		return HttpUtil.post(url, JSON.toJSONString(MapUtil.of("retail_info_list", ListUtil.toList(req))));
	}

	public static String addProfitAccount(String nickName, String openid, String appletAccessToken) {
		String url = StringUtils.format("https://api.weixin.qq.com/retail/B2b/addprofitsharingaccount?access_token={}", appletAccessToken);
		HashMap<String, Object> req = new HashMap<>();
		req.put("env", 0);
		req.put("profit_sharing_relation_type", "RELATION_TYPE_OTHERS");
		req.put("payee_type", "PAYEE_TYPE_EXTERNAL_USER");
		req.put("payee_id", openid);
		req.put("payee_name", nickName);
		return HttpUtil.post(url, JSON.toJSONString(req));
	}

	/*public static String addprofitAccount(String appId, String nickName, String openid) {
		String url = StringUtils.format("https://api.weixin.qq.com/retail/B2b/addprofitsharingaccount?access_token=?access_token={}", W);
		HashMap<String, String> req = new HashMap<>();
		req.put("mobile_phone", contactPhone);
		req.put("retail_name", branchName);
		req.put("address_province", province);
		req.put("address_city", city);
		req.put("address_region", area);
		req.put("address_street", address);
		return HttpUtil.post(url, JSON.toJSONString(MapUtil.of("retail_info_list", ListUtil.toList(req))));
	}*/

	/**
	 * 解密微信消息推送
	 * @param encryptedMsg	加密字符串
	 * @param encodingAesKey	encodingAesKey
	 * @return	明文
	 * @throws Exception
	 */
	public static String decrypt(String encryptedMsg, String encodingAesKey) throws Exception {
		// Base64解码密钥（微信EncodingAESKey是Base64编码的）
		byte[] aesKey = Base64.decodeBase64(encodingAesKey + "=");
		byte[] original;
		try {
			// 设置解密模式为AES的CBC模式
			Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
			SecretKeySpec key_spec = new SecretKeySpec(aesKey, "AES");
			IvParameterSpec iv = new IvParameterSpec(Arrays.copyOfRange(aesKey, 0, 16));
			cipher.init(Cipher.DECRYPT_MODE, key_spec, iv);
			// 使用BASE64对密文进行解码
			byte[] encrypted = Base64.decodeBase64(encryptedMsg);
			// 解密
			original = cipher.doFinal(encrypted);
		} catch (Exception e) {
			e.printStackTrace();
			throw new AesException(AesException.DecryptAESError);
		}
		String xmlContent;
		try {
			// 去除补位字符
			byte[] bytes = PKCS7Encoder.decode(original);
			// 分离16位随机字符串,网络字节序和AppId
			byte[] networkOrder = Arrays.copyOfRange(bytes, 16, 20);
			int xmlLength = recoverNetworkBytesOrder(networkOrder);
			xmlContent = new String(Arrays.copyOfRange(bytes, 20, 20 + xmlLength), "UTF-8");
		} catch (Exception e) {
			e.printStackTrace();
			throw new AesException(AesException.IllegalBuffer);
		}
		return xmlContent;
	}

	// 还原4个字节的网络字节序
	static int recoverNetworkBytesOrder(byte[] orderBytes) {
		int sourceNumber = 0;
		for (int i = 0; i < 4; i++) {
			sourceNumber <<= 8;
			sourceNumber |= orderBytes[i] & 0xff;
		}
		return sourceNumber;
	}

	@ApiModel("微信小程序发送结果")
	@Data
	public static class CommonWxAppletMsgResp {

		@ApiModelProperty("消息是否发送成功")
		private boolean success = true;

		@ApiModelProperty("消息结果")
		private String msg;
	}
}
