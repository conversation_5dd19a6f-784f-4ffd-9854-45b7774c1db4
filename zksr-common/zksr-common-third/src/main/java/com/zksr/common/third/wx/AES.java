package com.zksr.common.third.wx;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.*;


@Slf4j
public class AES {
public static boolean initialized = false;

    /**
     * AES解密
     * @param content 密文
     * @return
     * @throws InvalidAlgorithmParameterException
     * @throws NoSuchProviderException
     */
    public static byte[] decrypt(byte[] content, byte[] keyByte, byte[] ivByte) throws InvalidAlgorithmParameterException {
        initialize();
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
            Key sKeySpec = new SecretKeySpec(keyByte, "AES");

            cipher.init(Cipher.DECRYPT_MODE, sKeySpec, generateIV(ivByte));// 初始化
            byte[] result = cipher.doFinal(content);
            return result;
        } catch (NoSuchAlgorithmException e) {
            log.error(" AES.decrypt异常1，", e);
        } catch (NoSuchPaddingException e) {
            log.error(" AES.decrypt异常2，", e);
        } catch (InvalidKeyException e) {
            log.error(" AES.decrypt异常3，", e);
        } catch (IllegalBlockSizeException e) {
            log.error(" AES.decrypt异常4，", e);
        } catch (BadPaddingException e) {
            log.error(" AES.decrypt异常5，", e);
        } catch (NoSuchProviderException e) {
            log.error(" AES.decrypt异常6，", e);
        } catch (Exception e) {
            log.error(" AES.decrypt异常7，", e);
        }
        return null;
    }

    public static void initialize(){
        if (initialized) return;
        Security.addProvider(new BouncyCastleProvider());
        initialized = true;
    }
    //生成iv
    public static AlgorithmParameters generateIV(byte[] iv) throws Exception{
        AlgorithmParameters params = AlgorithmParameters.getInstance("AES");
        params.init(new IvParameterSpec(iv));
        return params;
    }

}

