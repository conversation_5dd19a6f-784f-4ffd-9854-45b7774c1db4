package com.zksr.common.third.zfb.vo;

import com.alipay.api.internal.mapping.ApiField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付宝认证信息
 * @date 2024/8/9 14:46
 */
@Data
public class AlipaySystemOauthVO {
    private String accessToken;
    private String alipayUserId;
    private Date authStart;
    private String authTokenType;
    private String expiresIn;
    private String openId;
    private String reExpiresIn;
    private String refreshToken;
    private String unionId;
    private String userId;
}
