package com.zksr.common.third.message.dto;

import com.zksr.common.core.enums.MerchantTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/6/13 16:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel(description = "消息通知记录")
public class CommonMessageDTO {
    @ApiModelProperty(value = "消息ID", notes = "用于标记重发消息")
    private Long msgId;

    @ApiModelProperty("跳转路径")
    private String path;

    @ApiModelProperty("跳转的appid")
    private String pathAppid;

    @ApiModelProperty("消息接收者")
    private MerchantTypeEnum merchantType;

    @ApiModelProperty(value = "消息接收者", notes = "入驻商ID, 业务员ID, 等")
    private Long merchantId;

    @ApiModelProperty("消息主体")
    private Map<String, String> body;
}
