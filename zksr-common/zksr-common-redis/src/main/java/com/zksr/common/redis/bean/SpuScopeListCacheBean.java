package com.zksr.common.redis.bean;


import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.RefreshPolicy;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.zksr.common.redis.enums.RedisConstants.DAY_MINUTE;
import static com.zksr.common.redis.enums.RedisConstants.SPU_SCOPE_LIST;


@Configuration
public class SpuScopeListCacheBean {

    @Autowired
    private CacheManager cacheManager;

    private Cache<Long, List<ActivitySpuScopeDTO>> spuScopeCache;


    @PostConstruct
    public void init() {
        //自动刷新 对一些key比较少，实时性要求不高，加载开销非常大的缓存场景，适合使用自动刷新。
        RefreshPolicy policy = RefreshPolicy.newPolicy(com.zksr.common.core.utils.ToolUtil.randomCacheRefreshTime(), TimeUnit.MINUTES)//指定1小时刷新一次
                .stopRefreshAfterLastAccess(DAY_MINUTE, TimeUnit.MINUTES);//1天如果没有访问就停止刷新  指定该key多长时间没有访问就停止刷新，如果不指定会一直刷新
        QuickConfig qc = QuickConfig.newBuilder(SPU_SCOPE_LIST)
                .expire(Duration.ofMinutes(DAY_MINUTE))//过期时间为1天
                .cacheType(CacheType.REMOTE)
                //本地缓存更新后，将在所有的节点中删除缓存，以保持强一致性
                .syncLocal(true)//invalidate local cache in all jvm process after update(更新后使所有JVM进程中的本地缓存失效)
                .refreshPolicy(policy)
                .penetrationProtect(true)//当缓存访问未命中的情况下，对并发进行的加载行为进行保护。 当前版本实现的是单JVM内的保护，即同一个JVM中同一个key只有一个线程去加载，其它线程等待结果。
                .build();
        spuScopeCache = cacheManager.getOrCreateCache(qc);
    }


    @Bean
    public Cache<Long, List<ActivitySpuScopeDTO>> getSpuScopeCache() {
        return spuScopeCache;
    }


}
