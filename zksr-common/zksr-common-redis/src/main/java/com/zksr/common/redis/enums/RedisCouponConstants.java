package com.zksr.common.redis.enums;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import com.zksr.common.core.utils.StringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: redis 优惠券rediskey
 * @date 2024/3/26 17:30
 */
public class RedisCouponConstants {
    /**
     * 用户领取优惠券redis结果存储时间 60/秒
     */
    public static final Long RECEIVE_RESULT_TIME = 60L;

    /**
     * redis 优惠券主key
     */
    public static final String COUPON = "coupon";

    /**
     * redis 缓存可领取列表
     */
    public static final String COUPON_LIST = "list";

    /**
     * redis 缓存可领取列表
     */
    public static final String SUPPLIER_COUPON_LIST = "supplier_list";

    /**
     * 使用范围
     */
    public static final String SPU_SCOPE = "spu_scope";

    /**
     * 领取范围
     */
    public static final String RECEIVE_SCOPE = "receive_scope";

    /**
     * 总库存
     */
    public static final String STOCK =  "stock";

    /**
     * 已领取库存
     */
    public static final String STOCK_USED =  "stock_used";

    /**
     * 领取状态
     */
    public static final String RECEIVE_STATUS =  "receive_status";

    /**
     * 领取结果
     */
    public static final String RECEIVE_RESULT =  "receive_result";

    /**
     * 具体使用范围别称
     */
    public static final String SPU_SCOPE_AS =  "coupon:spu_scope_as:";

    /**
     * 可领取范围别称
     */
    public static final String RECEIVE_SCOPE_AS =  "coupon:receive_scope_as:";

    /**
     * 获取大区可领取优惠券key
     * @return  可领取优惠券列表key
     */
    @Deprecated
    public static String getSysCodeCouponList(Long sysCode) {
        return StringUtils.format("{}:{}:{}", COUPON, COUPON_LIST, sysCode);
    }

    /**
     * 获取大区可领取优惠券key
     * @return  可领取优惠券列表key
     */
    public static String getSupplierCouponList(Long supplierId) {
        return StringUtils.format("{}:{}:{}", COUPON, SUPPLIER_COUPON_LIST, supplierId);
    }

    /**
     * 获取具体使用范围优惠券换成
     * @param sysCode   平台ID
     * @param spuScope
     * @param applyId
     * @return
     */
    public static String getSysCodeSpuScopeCouponList(Long sysCode, Integer spuScope, Long applyId) {
        return StringUtils.format("{}:{}:{}:{}:{}", COUPON, SPU_SCOPE, sysCode, spuScope, applyId);
    }


    /**
     * 获取优惠券领取数据具体值
     * @param couponTemplateId  优惠券ID
     * @param receiveScope      0-全部可领取,1-指定渠道,2-指定城市,3-指定门店
     * @return  可领取优惠券列表key
     */
    public static String getReceiveScope(Long couponTemplateId, Integer receiveScope) {
        return StringUtils.format("{}:{}:{}:{}", COUPON, couponTemplateId, RECEIVE_SCOPE, receiveScope);
    }


    /**
     * 获取优惠券库存key
     */
    public static String getStockKey(Long couponTemplateId) {
        return StringUtils.format("{}:{}:{}", COUPON, couponTemplateId, STOCK);
    }


    /**
     * 获取优惠券已领取key
     */
    public static String getStockUsedKey(Long couponTemplateId) {
        return StringUtils.format("{}:{}:{}", COUPON, couponTemplateId, STOCK_USED);
    }


    /**
     * 门店已领取数量
     */
    public static String getCouponBranchReceiveQty(Long couponTemplateId, Long branchId) {
        return StringUtils.format("{}:{}:{}:{}", COUPON, couponTemplateId, RECEIVE_STATUS, branchId);
    }

    /**
     * 获取优惠券领取结果
     */
    public static String getCouponReceiveResult(Long statusId) {
        return StringUtils.format("{}:{}:{}", COUPON, RECEIVE_RESULT, statusId);
    }

    public static String getTempKey() {
        return "tmpKey:" + DateUtil.formatDate(new Date()) + UUID.fastUUID();
    }
}
