package com.zksr.common.redis.service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.domain.RedisCall;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.enums.RedisConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

import static com.zksr.common.core.constant.CacheConstants.SPU_NO_SEQUENCE;

/**
 * spring redis 工具类
 *
 * <AUTHOR>
 **/
@SuppressWarnings(value = { "unchecked", "rawtypes" })
@Component
@Slf4j
public class RedisService
{
    @Autowired
    public RedisTemplate redisTemplate;

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value)
    {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     * @param timeout 时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Long timeout, final TimeUnit timeUnit)
    {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * 设置有效时间
     *
     * @param key Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout)
    {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key Redis键
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit)
    {
        return redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取有效时间
     *
     * @param key Redis键
     * @return 有效时间
     */
    public long getExpire(final String key)
    {
        return redisTemplate.getExpire(key);
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key)
    {
        return redisTemplate.hasKey(key);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key)
    {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean deleteObject(final String key)
    {
        return redisTemplate.delete(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public boolean deleteObject(final Collection collection)
    {
        return redisTemplate.delete(collection) > 0;
    }

    /**
     * 缓存List数据
     *
     * @param key 缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList)
    {
        Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
        return count == null ? 0 : count;
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key)
    {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    /**
     * 缓存Set
     *
     * @param key 缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet) {
        BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
        Iterator<T> it = dataSet.iterator();
        while (it.hasNext())
        {
            setOperation.add(it.next());
        }
        return setOperation;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key) {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 随机获取缓存值
     * @param key
     * @return
     */
    public <T> List<T> getCacheSetRandom(final String key, final Long count) {
        return redisTemplate.opsForSet().randomMembers(key, count);
    }

    /**
     * 随机删除获取缓存值
     * @param key
     * @return
     */
    public <T> List<T> removeCacheSetRandom(final String key, final Long count) {
        return redisTemplate.opsForSet().pop(key, count);
    }

    public <T> Long setCacheSet(final String key, final T value) {
        return redisTemplate.opsForSet().add(key, value);
    }

    public <T> Set<T> getCacheSetMembers(final String key) {
        return redisTemplate.opsForSet().members(key);
    }

    public Boolean getCacheSetIsMember(final String key, Object value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }

    public <T> Long remCacheSet(final String key, final T value) {
        return redisTemplate.opsForSet().remove(key, value);
    }

    /**
     * 缓存Set
     *
     * @param key 缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundZSetOperations<String, T> setCacheZSet(final String key, final Set<DefaultTypedTuple<T>> dataSet) {
        BoundZSetOperations<String, T> zSetOperation = redisTemplate.boundZSetOps(key);
//        zSetOperation.add(dataSet);
        Iterator<DefaultTypedTuple<T>> it = dataSet.iterator();
        while (it.hasNext()){
            DefaultTypedTuple<T> t = it.next();
            zSetOperation.add(t.getValue(), t.getScore());
        }
        return zSetOperation;
    }

    /**
     * 获得缓存的zSet
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheZSetRange(final String key, long start, long end) {
        return redisTemplate.opsForZSet().range(key, start, end);
    }


    /**
     * 搜索缓存的zSet
     *
     * @param key
     * @return
     */
    public <T> Set<T> zSetScan(final String key, String partern) {
        Set<T> res = new HashSet<>();
        ScanOptions scanOptions = ScanOptions.scanOptions().count(100).match(partern).build();
        Cursor<ZSetOperations.TypedTuple<T>> cursor = redisTemplate.opsForZSet().scan(key, scanOptions);
        while (cursor.hasNext()){
            ZSetOperations.TypedTuple<T> typedTuple = cursor.next();
            System.out.println("通过scan(K key, ScanOptions options)方法获取匹配元素:" + typedTuple.getValue() + "--->" + typedTuple.getScore());
            res.add(typedTuple.getValue());
        }
        return res;
    }

    /**
     * 获得缓存的zSet 元素数量
     *
     * @param key
     * @return
     */
    public Long getCacheZSetCard(final String key) {
        return redisTemplate.opsForZSet().zCard(key);
    }

    /**
     * key集合与otherKey集合的并集存储到destKey中
     *
     * @param key
     * @return
     */
    public Long zSetUnionAndStore(final String key, Collection<String> otherKeys, String destKey) {
        return redisTemplate.opsForZSet().unionAndStore(key, otherKeys, destKey);
    }


    public <T> Boolean zSetAdd(final String key, final Long score, final T value){
        return redisTemplate.opsForZSet().add(key, value, score);
    }

    public <T> Long zSetRem(final String key, final T value){
        return redisTemplate.opsForZSet().remove(key, value);
    }




    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap)
    {
        if (dataMap != null) {
            redisTemplate.opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key)
    {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 往Hash中存入数据
     *
     * @param key Redis键
     * @param hKey Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value)
    {
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey)
    {
        HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
        return opsForHash.get(key, hKey);
    }

    /**
     * 给map中指定字段的整数值加上增量increment
     *
     * @param key Redis键
     * @param hKey Hash键
     * @param increment 增量值
     */
    public Long incrCacheMapValue(final String key, final String hKey, final long increment) {
        return redisTemplate.opsForHash().increment(key, hKey, increment);
    }

    /**
     * 给map中指定字段的浮点值加上增量increment
     *
     * @param key Redis键
     * @param hKey Hash键
     * @param increment 增量值
     */
    public Double incrCacheMapValue(final String key, final String hKey, final double increment) {
        return redisTemplate.opsForHash().increment(key, hKey, increment);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys)
    {
        return redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param key Redis键
     * @param hKey Hash键
     * @return 是否成功
     */
    public boolean deleteCacheMapValue(final String key, final String hKey)
    {
        return redisTemplate.opsForHash().delete(key, hKey) > 0;
    }

    /**
     * 删除多个Hash中的数据
     *
     * @param key Redis键
     * @param hKey Hash键集合
     * @return Hash对象集合
     */
    public Long delMultiCacheMapValue(final String key, final String hKey) {
        return redisTemplate.opsForHash().delete(key, hKey);
    }

    /**
     * 获得缓存的基本对象列表  keys要禁止使用
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern) {
        //return redisTemplate.keys(pattern);
        Set<String> result = (Set<String>) redisTemplate.execute(new RedisCallback<Set<String>>() {
            @Override
            public Set<String> doInRedis(RedisConnection connection) throws DataAccessException {
                Set<String> partUers = new HashSet<>();
                // 放在try中自动释放cursor
                ScanOptions scanOptions = ScanOptions.scanOptions().match(pattern).count(50000).build();
                try (Cursor<byte[]> cursor = connection.scan(scanOptions)) {
                    while (cursor.hasNext()) {
                        partUers.add(new String(cursor.next()));
                    }
                }
                return partUers;
            }
        });
        return result;
    }

    /**
     * 获得缓存的基本对象列表  keys要禁止使用 封装keys
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Set<String> scan(final String pattern) {

        Set<String> result = (Set<String>) redisTemplate.execute(new RedisCallback<Set<String>>() {
            @Override
            public Set<String> doInRedis(RedisConnection connection) throws DataAccessException {
                Set<String> partUers = new HashSet<>();
                // 放在try中自动释放cursor
                ScanOptions scanOptions = ScanOptions.scanOptions().match(pattern).count(50000).build();
                try (Cursor<byte[]> cursor = connection.scan(scanOptions)) {
                    while (cursor.hasNext()) {
                        partUers.add(new String(cursor.next()));
                    }
                }
                return partUers;
            }
        });
        return result;
        //return redisTemplate.keys(pattern);
    }

    public RedisTemplate getRedisTemplate() {
        return redisTemplate;
    }


    /**
     * 自增加1
     * @param key
     * @return
     */
    public Long incrByCacheObject(final String key){
        return redisTemplate.opsForValue().increment(key);
    }

    /**
     * 以增量方式存储long值
     * @param key
     * @param value
     * @return
     */
    public Long incrByCacheObject(final String key, final long value){
        return redisTemplate.opsForValue().increment(key, value);
    }

    /**
     * 以增量方式存储Double值
     * @param key
     * @param value
     * @return
     */
    public Double incrDoubleByCacheObject(final String key, final Double value){
        return redisTemplate.opsForValue().increment(key, value);
    }

    /**
     * 以递减方式存储long值
     * @param key
     * @param value
     * @return
     */
    public Long decrByCacheObject(final String key, final long value){
        return redisTemplate.opsForValue().decrement(key, value);
    }

    public Long getCacheListSize(final String key) {
        return redisTemplate.opsForList().size(key);
    }

    public void leftPopCacheList(final String key) {
        Object object = redisTemplate.opsForList().leftPop(key);
    }

    /**
     * 缓存List数据
     *
     * @param key 缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long rightPushCacheList(final String key, final List<T> dataList) {
        Long count = redisTemplate.opsForList().rightPush(key,dataList);
        return count == null ? 0 : count;
    }

    /**
     * 修改key名称
     * @param oldKey    旧key
     * @param newKey    新key
     */
    public void rename(final String oldKey, final String newKey) {
        redisTemplate.rename(oldKey, newKey);
    }

    /**
     * 获取zset长度
     * @param objKey
     * @return
     */
    public Long getCacheZSetSize(Object objKey) {
        return redisTemplate.opsForZSet().size(objKey);
    }













    /**
     * 从redis 获取 最大999999 不重复number
     * @return
     */
    public String getUniqueNumber() {
        return getUniqueNumber(CacheConstants.ORDERNO_SEQUENCE);
    }

    /**
     * 从redis 获取 最大999999 不重复number
     * @param keyFix 不同key 可生成不同组 999999
     * @return
     */
    public String getUniqueNumber(String keyFix) {
        int length = 6;
        String dateStr = DateUtils.parseDateToStr("yyMMdd", new Date());
        String key = keyFix + dateStr;
        StringBuilder orderNo = new StringBuilder("");
        orderNo.append(dateStr);
        String random1 = RandomStringUtils.random(2, false, true);
        orderNo.append(random1);
        //两位随机码
        try {
            /**
             * 自增加1
             */
            long sequence = this.incrByCacheObject(key);
            if (sequence == 1) {
                this.expire(key, 24 * 60 * 60);//过期时间设置为24小时
            }
            String seqStr = sequence + "";
            for (int i = 0; i < length - seqStr.length(); i++) {
                orderNo.append(0);
            }
            orderNo.append(seqStr);
        } catch (Exception e) {
            String random3 = RandomStringUtils.random(6, false, true);
            orderNo.append(random3);
        }
        String random2 = RandomStringUtils.random(4, false, true);
        orderNo.append(random2);
        return orderNo.toString();
    }


    /**
     * 从redis 获取 最大999 不重复number
     * @param keyPrefix 不同key 可生成不同组 999
     * @return 格式化的唯一编号
     */
    public String getCombineSpuUniqueNumber(String keyPrefix) {
        int seqLength = 3; // 序列号长度
        String dateStr = DateUtils.parseDateToStr("yyyyMMdd", new Date());
        String key = keyPrefix + dateStr;
        StringBuilder combineSpu = new StringBuilder(keyPrefix);
        combineSpu.append(dateStr);

        try {
            /**
             * 自增加1
             */
            long sequence = this.incrByCacheObject(key);
            if (sequence == 1) {
                this.expire(key, 24 * 60 * 60); // 过期时间设置为24小时
            }

            // 确保序列号不超过999
            if (sequence > 999) {
                throw new RuntimeException("Sequence number exceeds the maximum limit of 999");
            }

            String seqStr = String.format("%0" + seqLength + "d", sequence);
            combineSpu.append(seqStr);
        } catch (Exception e) {
            // 处理异常情况，可以选择抛出异常或返回默认值
            throw new RuntimeException("Failed to generate unique number: " + e.getMessage(), e);
        }

        return combineSpu.toString();
    }



    /**
     * 简单获取
     * @param preFix
     * @param key
     * @param func
     * @throws Exception
     */
    @SuppressWarnings("all")
    public boolean tryLock(String preFix, Object key, RedisCall func) throws Exception {
        return tryLock(preFix, key, 15, 60, func);
    }

    /**
     * 获取redis 锁
     * @param preFix 锁前缀
     * @param key    锁值
     * @param waitTime  尝试时间
     * @param timeout   超时时间
     * @param func      获得回调
     * @throws Exception
     */
    public boolean tryLock(String preFix, Object key, Integer waitTime, Integer timeout, RedisCall func) throws Exception {
        long startTime = System.currentTimeMillis();
        long end = startTime + (waitTime * 1000);
        String lockKey = RedisConstants.REDIS_DIY_LOCK + preFix + key;
        try {
            for (;;) {
                Boolean lockYes = redisTemplate.opsForValue().setIfAbsent(lockKey, 1, timeout, TimeUnit.SECONDS);
                if (Boolean.TRUE.equals(lockYes)) {
                    func.get();
                    return true;
                } else {
                    if (waitTime > 0) {
                        Thread.sleep(500);
                    }
                    if (System.currentTimeMillis() >= end) {
                        return false;
                    }
                }
            }
        } catch (Exception e) {
            log.error(" RedisService.tryLock 异常, ", e);
            throw e;
        } finally {
            redisTemplate.delete(lockKey);
        }
    }


    /**
     * 获取商品编号
     * @param keyFix  商品类别编码（三级类别）
     * @return
     */
    public String getSpuNo(String keyFix, String spuNo) {
        if (ToolUtil.isNotEmpty(spuNo)){
            return spuNo;
        }
        int length = 7;
        String key = SPU_NO_SEQUENCE + keyFix;
        StringBuilder returnSpuNo = new StringBuilder("");
        returnSpuNo.append(keyFix);
        //两位随机码
        try {
            /**
             * 自增加1
             */
            long sequence = this.incrByCacheObject(key);
            String seqStr = sequence + "";
            for (int i = 0; i < length - seqStr.length(); i++) {
                returnSpuNo.append(0);
            }
            returnSpuNo.append(seqStr);
        } catch (Exception e) {
            String random3 = RandomStringUtils.random(7, false, true);
            returnSpuNo.append(random3);
        }
        return returnSpuNo.toString();
    }

    /**
     * 获取set长度
     * @param key
     * @return
     */
    public Long getCacheSetCard(Object key) {
        return redisTemplate.opsForSet().size(key);
    }

    // String 管道查询
    public <T> List<T> executePipelinedQuery(List<String> keys) {
        // 小于等于3就不用走管道操作了
        if (keys.size() <= 5) {
            return (List<T>) keys.stream()
                    .map(cacheKey -> redisTemplate.opsForValue().get(cacheKey))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        List list = redisTemplate.executePipelined(new SessionCallback<Object>() {
            @Override
            public  Object execute(RedisOperations operations) throws DataAccessException {
                for (String key : keys) {
                    operations.opsForValue().get(key); // 添加要执行的get操作
                }
                return null;
            }
        });
        return list;
    }

    /**
     * 保存或者更新set, 请看代码以后再确定你是否要使用, 别直接就用了
     * @param cacheKey  缓存key
     * @param setList   set数据
     */
    public void saveOrUpdateSet(String cacheKey, Set<Object> setList) {
        if (ObjectUtil.isEmpty(setList)) {
            this.deleteObject(cacheKey);
            return;
        }
        this.setCacheSet(cacheKey, setList);
        // 先获取原来的set有没有数据, 然后再判断有没有需要删除的
        Set<Object> oldList = this.getCacheSet(cacheKey);
        List<Object> removeItems = oldList.stream().filter(item -> !setList.contains(item)).collect(Collectors.toList());
        if (!removeItems.isEmpty()) {
            for (Object removeItem : removeItems) {
                this.remCacheSet(cacheKey, removeItem);
            }
        }
    }
}
