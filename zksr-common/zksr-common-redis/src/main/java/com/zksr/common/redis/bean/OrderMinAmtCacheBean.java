package com.zksr.common.redis.bean;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.RefreshPolicy;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.trade.api.order.dto.OrderCutAmtDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

import static com.zksr.common.redis.enums.RedisConstants.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单截团日累计订单金额
 * @date 2024/4/26 15:36
 */
@Component
public class OrderMinAmtCacheBean {

    @Autowired
    private CacheManager cacheManager;

    private Cache<String, OrderCutAmtDTO> orderCutAmtCache;

    @PostConstruct
    public void init() {
        RefreshPolicy policy = RefreshPolicy.newPolicy(com.zksr.common.core.utils.ToolUtil.randomCacheRefreshTime(), TimeUnit.MINUTES)//指定1小时刷新一次
                .stopRefreshAfterLastAccess(DAY_MINUTE, TimeUnit.MINUTES);//1天如果没有访问就停止刷新  指定该key多长时间没有访问就停止刷新，如果不指定会一直刷新
        QuickConfig qc = QuickConfig.newBuilder(TRD_CUT_ORDER_AMT)
                .expire(Duration.ofMinutes(DAY_MINUTE))//过期时间为1天
                .cacheType(CacheType.REMOTE)
                //本地缓存更新后，将在所有的节点中删除缓存，以保持强一致性
                .syncLocal(true)//invalidate local cache in all jvm process after update(更新后使所有JVM进程中的本地缓存失效)
                .refreshPolicy(policy)
                .penetrationProtect(true)//当缓存访问未命中的情况下，对并发进行的加载行为进行保护。 当前版本实现的是单JVM内的保护，即同一个JVM中同一个key只有一个线程去加载，其它线程等待结果。
                .build();
        orderCutAmtCache = cacheManager.getOrCreateCache(qc);
    }


    @Bean
    public Cache<String, OrderCutAmtDTO> getOrderCutAmtCache() {
        return orderCutAmtCache;
    }

}
