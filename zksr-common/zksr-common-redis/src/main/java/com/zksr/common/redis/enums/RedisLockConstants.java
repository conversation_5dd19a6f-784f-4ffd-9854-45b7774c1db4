package com.zksr.common.redis.enums;

public class RedisLockConstants {
    public static final String LOCK_DEMO_PRE = "hello";
    public static final String LOCK_DEMO_POST = "haha";

    /**
     * 订单支付
     */
    public static final String LOCK_ORDER_PAY = "lock_order_pay";

    /**
     * 优惠券平台刷新
     */
    public static final String LOCK_COUPON_REFRESH = "lock_coupon_refresh";

    /**
     * 优惠券库存刷新
     */
    public static final String LOCK_COUPON_STOCK_REFRESH = "lock_coupon_stock_refresh";

    /**
     * 优惠券领取
     */
    public static final String LOCK_COUPON_RECEIVE_REFRESH = "lock_coupon_receive";

    /**
     * 内部账户转账单
     */
    public static final String LOCK_ACCOUNT_TRANSFER = "account_transfer";

    /**
     * 处理转账流水
     */
    public static final String LOCK_PROCESS_FLOW = "process_flow";

    /**
     * 商户客户端初始化
     */
    public static final String LOCK_MERCHANT_CLIENT = "merchant_client";

    /**
     * 短信消息
     */
    public static final String LOCK_MOBILE_SMS = "mobile_sms";

    /**
     * 货到付款订单
     */
    public static final String LOCK_HDFK_ORDER = "lock_hdfk_order";

    /**
     * 订单售后
     */
    public static final String LOCK_AFTER_ORDER = "lock_after_order";

    /**
     * SPU 分享
     */
    public static final String LOCK_SPU_SHARE = "lock_spu_share";

    /**
     * SPU 拷贝
     */
    public static final String LOCK_SPU_COPY = "lock_spu_copy";

    /**
     * 提现交易转账
     */
    public static final String LOCK_WITHDRAW_TRANSFER = "lock_withdraw_transfer";
    /**
     * 分账
     */
    public static final String LOCK_PROFIT_ORDER = "lock_profit_order";

    /**
     * 商户操作
     */
    public static final String LOCK_MERCHANT = "lock_merchant_action";

    /**
     * 门店导入
     */
    public static final String LOCK_BRANCH_IMPORT = "lock_branch_import";


    /* =============第三方系统同步接口======================= */
    /**
     * 同步门店信息
     */
    public static final String LOCK_SYNC_BRANCH = "lock_sync_branch";

    /**
     * 同步销售订单信息
     */
    public static final String LOCK_SYNC_ORDER = "lock_sync_order";

    /**
     * 同步售后订单信息
     */
    public static final String LOCK_SYNC_AFTER = "lock_sync_after";

    /**
     * 同步售后订单信息
     */
    public static final String LOCK_SYNC_RECEIPT = "lock_sync_receipt";

    /**
     * 同步第三方信息
     */
    public static final String LOCK_SYNC = "lock_sync";


    /**
     * OPENAPI 接收信息锁
     */
    public static final String LOCK_OPENAPI_PROCESS_TEMPLATE = "lock_openapi_process_template";

    /**
     * 获取订单信息
     */
    public static final String LOCK_GET_THEORDER = "lock_get_theorder";

    /**
     * 获取售后订单信息
     */
    public static final String LOCK_GET_AFTERORDER = "lock_get_afterorder";

    /**
     * 获取收款单信息
     */
    public static final String LOCK_GET_RECEIPT = "lock_get_receipt";

    /**
     * 同步售后订单信息
     */
    public static final String LOCK_OPENAPI_GET_TOKEN = "lock_openapi_get_token";

    /**
     * 对接第三方 -- 好帮你  鉴权token
     */
    public static final String LOCK_SYNC_HAO_BANG_NI_TOKEN = "lock_sync_hao_bang_ni_token:";

    /**
     * 批量按区域同步第三方门店信息
     */
    public static final String LOCK_SYNC_AREA_BRANCH = "lock_sync_area_branch:";

    /* =============第三方系统同步接口结束======================= */

    /**
     * 线下分账
     */
    public static final String LOCK_OFFLINE_DIVIDE = "lock_process_offline_divide";

    /**
     * 同步 业务员APP首页缓存信息
     */
    public static final String LOCK_COLONEL_APP = "lock_colonel_app";

    /**
     * 同步 订单快递信息导入
     */
    public static final String LOCK_ORDER_EXPRESS = "lock_order_express";

    /**
     * 第三方调用货到付款清账
     */
    public static final String LOCK_THIRD_PARTY_HDFK_CLEAR = "lock_third_party_hdfk_clear";

    /**
     * 后台货到付款清账
     */
    public static final String LOCK_ADMIN_HDFK_CLEAR = "lock_admin_hdfk_clear";

    /**
     * 微信B2B支付分账请求
     */
    public static final String LOCK_WXB2B_DIVIDE = "lock_wxb2b_divide";
    /**
     * 美的付微信B2B支付分账请求
     */
    public static final String LOCK_MIDEA_WXB2B_DIVIDE = "lock_midea_wxb2b_divide";
    /**
     * 美的支付分账请求
     */
    public static final String LOCK_MIDEA_PAY_DIVIDE = "lock_midea_pay_divide";

    /**
     * 业务员绑定微信支付, openid
     */
    public static final String LOCK_WXB2B_COLONEL_BIND = "lock_wxb2b_colonel_bind";

    /**
     * 保存要货单
     */
    public static final String LOCK_SAVE_YH_DATA = "lock_save_yh_data";

    /**
     * 匹配要货单
     */
    public static final String LOCK_MATCH_YH_DATA = "lock_match_yh_data";

    /**
     * 门店操作要货单
     */
    public static final String LOCK_BRANCH_YH_DATA = "lock_branch_yh_data";

    /**
     * 购物车
     */
    public static final String LOCK_BRANCH_CAR = "lock_branch_car";

    /**
     *  加单指令操作
     */
    public static final String ADD_ORDER_COMMAND = "addOrderCommand";

    /**
     * 门店储值余额
     */
    public static final String LOCK_BRANCH_WALLET = "lock_branch_wallet";

    /**
     * 发送邮件锁
     */
    public static final String LOCK_SEND_EMAIL_MESSAGE = "lock_send_email_message";

    /**
     * 下单
     */
    public static final String LOCK_SAVE_ORDER = "lock_save_order";

    /**
     * 入驻商常规
     */
    public static final String LOCK_SUPPLIER = "lock_supplier";

    /**
     * 平台商城市分组
     */
    public static final String LOCK_CITY_GROUP = "lock_city_group";

    /**
     * 渠道数据
     */
    public static final String LOCK_CHANNEL = "lock_channel";

    /**
     * 增加已售数量
     */
    public static final String LOCK_INCR_SALED_QTY = "lock_incr_saled_qty";

    /**
     * 用户注册
     */
    public static final String LOCK_MEMBER_REGISTER = "lock_member_register";

    /**
     * 入驻商订单操作锁
     */
    public static final String LOCK_SUPPLIER_ORDER = "lock_supplier_order";

}