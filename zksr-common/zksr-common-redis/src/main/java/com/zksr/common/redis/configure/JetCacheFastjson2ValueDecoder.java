package com.zksr.common.redis.configure;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.alicp.jetcache.support.Fastjson2ValueDecoder;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/4/19 10:13
 */
public class JetCacheFastjson2ValueDecoder extends Fastjson2ValueDecoder {

    public static final JetCacheFastjson2ValueDecoder INSTANCE = new JetCacheFastjson2ValueDecoder(true);

    public JetCacheFastjson2ValueDecoder(boolean useIdentityNumber) {
        super(useIdentityNumber);
    }

    @Override
    protected Object parseObject(byte[] buffer, int index, int len, Class clazz) {
        String s = new String(buffer, index, len, StandardCharsets.UTF_8);
        return JSON.parseObject(s, clazz, JSONReader.Feature.SupportAutoType);
    }
}
