package com.zksr.common.redis.service;

import cn.hutool.core.util.NumberUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.TrdOrderInfo;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.zksr.common.redis.enums.RedisConstants.*;

/**
 * <AUTHOR>
 * @date 2024年04月02日 15:43
 * @description: RedisCacheService
 */
@Component
public class RedisCacheService {
    @Autowired
    private RedisService redisService;

    /**
     * 未支付订单保存到redis中
     * @param order
     */
    public void addUnpayOrderList(TrdOrderInfo order){
        redisService.setCacheSet(TRD_NOT_UNPAID_ORDER, order);
    }

    /**
     * 订单从redis删除
     * @param order
     */
    public void delUnpayOrderList(TrdOrderInfo order){
        redisService.remCacheSet(TRD_NOT_UNPAID_ORDER, order);
    }

    /**
     * 从缓存中获取未支付的订单信息
     */
    public Set<TrdOrderInfo> getCacheOrderInfo() {
        Set<TrdOrderInfo> set = redisService.getCacheSetMembers(TRD_NOT_UNPAID_ORDER);
        return set;
    }
}
