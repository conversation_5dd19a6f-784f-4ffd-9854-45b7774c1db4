package com.zksr.common.redis.utils;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 运营商工具类
 * @date 2024/6/22 15:36
 */
public class DcUtils {

    /**
     * 获取运营商绑定的区域城市集合
     * @param dcId  运营商ID
     * @return  区域城市ID集合
     */
    public static List<Long> getAreaList(Long dcId) {
        if (Objects.isNull(dcId)) {
            return null;
        }
        DcAreaGroupDTO dcAreaGroup = SpringUtils.getBean(RedisService.class).getCacheObject(RedisConstants.DC_AREA_GROUP + dcId);
        if (Objects.nonNull(dcAreaGroup)) {
            dcAreaGroup.getAreaIds().add(NumberPool.LOWER_GROUND_LONG);
            return dcAreaGroup.getAreaIds();
        }
        return ListUtil.toList(NumberPool.LOWER_GROUND_LONG);
    }

    /**
     * 获取入驻商绑定的区域城市集合
     * @param dcId  运营商ID
     * @return  入驻商ID集合
     */
    public static List<Long> getSupplierList(Long dcId) {
        if (Objects.isNull(dcId)) {
            return null;
        }
        List<Long> supplierIdList = SpringUtils.getBean(DcApi.class).getDcSupplierList(dcId).getCheckedData();
        if (Objects.nonNull(supplierIdList)) {
            supplierIdList.add(NumberPool.LOWER_GROUND_LONG);
        }
        return supplierIdList;
    }
}
