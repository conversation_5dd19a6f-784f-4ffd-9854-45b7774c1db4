package com.zksr.common.redis.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DistributedLock {
    /**
     * 锁的名称。
     * 如果lockName可以确定，直接设置该属性。
     * 则没有动态锁值
     */
    String lockName() default "";

    /**
     * el 表达式, 锁动态值
     * @return
     */
    String condition() default "";

    /**
     * lockName前缀
     */
    String prefix() default "";
    /**
     * lockName后缀
     */
    String suffix() default "lock";

    /**
     * 获得锁名时拼接前后缀用到的分隔符
     * @return
     */
    String separator() default ".";

    /**
     * 是否使用公平锁。
     * 公平锁即先来先得。
     */
    boolean fairLock() default false;
    /**
     * 是否使用尝试锁。
     */
    boolean tryLock() default false;
    /**
     * 最长等待时间。
     * 该字段只有当tryLock()返回true才有效。
     */
    long waitTime() default 5L;
    /**
     * 锁超时时间。
     * 超时时间过后，锁自动释放。
     * 建议：
     *   尽量缩简需要加锁的逻辑。
     */
    long leaseTime() default 30L;
    /**
     * 时间单位。默认为秒。
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

}
