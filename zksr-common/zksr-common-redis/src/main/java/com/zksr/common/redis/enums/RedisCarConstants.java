package com.zksr.common.redis.enums;

import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: redis 购物车缓存key
 * @date 2024/3/26 17:30
 */
public class RedisCarConstants {
    /**
     * 购物车最大加购上限
     */
    public static final Integer CAT_MAX = 200;

    /**
     * 购物车有效存储时间60天
     */
    public static final Integer CAT_EXPIRE = 86400 * 120;

    /**
     * 购物车
     */
    public static final String CAR = "car";

    /**
     * 用户购物车商品
     */
    public static final String ALL = "all";

    /**
     * 购物车信息
     */
    public static final String INFO = "info";

    /**
     * 购物车选中
     */
    public static final String SELECTED = "selected";

    /**
     * 入驻商列表
     */
    public static final String SUPPLIER_LIST = "supplier_list";

    /**
     * 入驻商商品分组
     */
    public static final String SUPPLIER_GROUP = "supplier_group";

    /**
     * 商品字段: 商品数量
     */
    public static final String PRODUCT_FIELD_NUM = "P1";

    /**
     * 商品字段: 是否推荐
     */
    public static final String PRODUCT_FIELD_RECOMMEND = "P2";

    /**
     * 商品字段: 推荐数量
     */
    public static final String PRODUCT_FIELD_RECOMMEND_NUM = "P10";

    /**
     * 业务员加购
     */
    public static final String RECOMMEND = "recommend";

    /**
     * 业务员加单门店集合需要验证门店加单商品是否超时, 需要移除
     */
    public static final String RECOMMEND_BRANCH_LIST = "recommend_branch_list:";

    public static String getRecommendBranchList(String dateStr) {
        return RECOMMEND_BRANCH_LIST + dateStr;
    }

    /**
     * 获取购物车选中键
     * @param branchId      门店ID
     * @param productType   {@linkplain AppCarIdDTO#getType()}
     * @return
     */
    public static String getBranchSelectKey(Long branchId, String productType) {
        return StringUtils.format("{}:{}:{}:{}", CAR, branchId, productType, SELECTED);
    }

    /**
     * 获取购物车统计
     * @param branchId
     * @param productType
     * @return
     */
    public static String getBranchTotalKey(Long branchId, String productType) {
        return StringUtils.format("{}:{}:{}:{}", CAR, branchId, productType, ALL);
    }

    /**
     * 获取购物车选中键
     * @param branchId      门店ID
     * @param productType   {@linkplain AppCarIdDTO#getType()}
     * @return
     */
    public static String getSupplierListKey(Long branchId, String productType) {
        return StringUtils.format("{}:{}:{}:{}", CAR, branchId, productType, SUPPLIER_LIST);
    }

    /**
     * 获取入驻商下商品列表键
     * @param branchId      门店ID
     * @param productType   {@linkplain AppCarIdDTO#getType()}
     * @param supplierId    入驻商ID
     * @return
     */
    public static String getSupplierItemListKey(Long branchId, String productType, Long supplierId) {
        return StringUtils.format("{}:{}:{}:{}:{}", CAR, branchId, productType, SUPPLIER_GROUP, supplierId);
    }

    /**
     * 获取入驻商下商品键
     * @param branchId      门店ID
     * @param type          商品类型
     * @param unitSize      unit 单位
     * @return
     */
    public static String getSupplierItemKey(Long branchId, String type, Integer unitSize, Long itemId) {
        return StringUtils.format("{}:{}:{}:{}:{}_detail", CAR, branchId, type, unitSize, itemId);
    }

    /**
     * 获取业务员购物车推荐数据
     * @param branchId
     * @return
     */
    public static String getColonelRecommendKey(Long branchId) {
        return StringUtils.format("{}:{}:{}", CAR, branchId, RECOMMEND);
    }

    /**
     * 获取购物车信息
     * @param branchId
     * @return
     */
    public static String getInfoKey(Long branchId) {
        return StringUtils.format("{}:{}:{}", CAR, branchId, INFO);
    }
}
