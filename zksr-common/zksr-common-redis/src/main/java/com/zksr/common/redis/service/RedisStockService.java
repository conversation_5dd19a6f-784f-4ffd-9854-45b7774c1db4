package com.zksr.common.redis.service;

import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024年04月01日 17:12
 * @description: RedisStockService
 */
@Component
@Slf4j
public class RedisStockService {

    private static final String SKU_STOCK = "sku_stock:";

    //总库存
    private static final String STOCK_QTY = ":stock_qty";

    //已售库存
    private static final String SALED_STOCK_QTY = ":saled_stock_qty";

    //已同步库存
    private static final String SYNCED_STOCK_QTY = ":synced_stock_qty";


    //已占用库存
    private static final String OCCUPIED_STOCK_QTY = ":occupied_stock_qty";

    // 需要更新库存的skuId
    private static final String UPDATE_STOCK_SKU = "update_stock_sku_set";

    @Autowired
    private RedisService redisService;

    /**
     * 放置到库存更新列表
     *
     * @param skuId skuId
     */
    public void pushSkuStockUpdate(Long skuId) {
        if (Objects.isNull(skuId)) {
            return;
        }
        String area = redisService.getCacheObject(UPDATE_STOCK_SKU);
        if (StringUtils.isEmpty(area)) {
            // 默认0区位
            area = StringPool.ZERO;
        }
        redisService.zSetAdd(StringUtils.format("{}:{}", UPDATE_STOCK_SKU, area), System.currentTimeMillis(), skuId);
    }

    /**
     * 从冷交换区获取数据
     *
     * @return
     */
    public Set<Long> getSkuStockUpdateList() {
        // 取数据之前会先将热区变成冷区
        // 默认0区位
        String area = redisService.getCacheObject(UPDATE_STOCK_SKU);
        if (StringPool.ONE.equals(area)) {
            area = StringPool.ZERO;
        } else {
            // 我们要从1区位获取数据
            area = StringPool.ONE;
        }
        // 每次获取30条更新
        return redisService.getCacheZSetRange(StringUtils.format("{}:{}", UPDATE_STOCK_SKU, area), 0, 30L);
    }

    /**
     * 切换刷新交换区
     *
     * @return 切换交换区之前的数据KEY
     */
    public String changeSkuStockArea() {
        String area = redisService.getCacheObject(UPDATE_STOCK_SKU);
        if (StringUtils.isEmpty(area) || StringPool.ONE.equals(area)) {
            // 默认0区位
            redisService.setCacheObject(UPDATE_STOCK_SKU, StringPool.ZERO);
            return StringUtils.format("{}:{}", UPDATE_STOCK_SKU, StringPool.ONE);
        } else {
            redisService.setCacheObject(UPDATE_STOCK_SKU, StringPool.ONE);
            return StringUtils.format("{}:{}", UPDATE_STOCK_SKU, StringPool.ZERO);
        }
    }

    public void setSkuStock(Long skuId, BigDecimal stockQty) {
        log.info("更新总库存skuStock，skuId:{}，stockQty:{}，", skuId, stockQty);
        String skuStockKey = getSkuStockKey(skuId);
        redisService.setCacheObject(skuStockKey, stockQty.intValue());
        // 如果设置了库存, 监听
        this.pushSkuStockUpdate(skuId);
    }

    /**
     * 增加总库存
     *
     * @param skuId
     * @param stockQty
     * @return
     */
    public Double incrSkuStock(Long skuId, BigDecimal stockQty) {
        String skuStockKey = getSkuStockKey(skuId);
        Double res = redisService.incrDoubleByCacheObject(skuStockKey, stockQty.doubleValue());
        // 记录库存变动
        this.pushSkuStockUpdate(skuId);
        log.info("总库存变动-incrSkuStock- 时间：{}， SKUID：{}, 变动数量：{}， 变动结果：{}", DateUtils.getTime(), skuId, stockQty, res);
        return res;
    }

    /**
     * 设置已同步库存
     *
     * @param skuId
     * @param syncedQty
     * @return
     */
    public void setSkuSyncedQty(Long skuId, BigDecimal syncedQty) {
        String skuSyncedKey = getSkuSyncedStockKey(skuId);
        redisService.setCacheObject(skuSyncedKey, syncedQty.doubleValue());
        // 记录库存变动
        this.pushSkuStockUpdate(skuId);
        log.info("已同步库存变动-setSkuSyncedQty- 时间：{}， SKUID：{}, 变动数量：{}", DateUtils.getTime(), skuId, syncedQty);
    }

    public boolean hashSkuStock(Long skuId) {
        String skuStockKey = getSkuStockKey(skuId);
        return redisService.hasKey(skuStockKey);
    }


    /**
     * 预占redis库存
     *
     * @param skuId   skuId
     * @param saleQty 销售数量
     * @return false 库存不足预占失败
     */
    public boolean decreaseSkuStock(Long skuId, BigDecimal saleQty) {
        String skuStockKey = getSkuStockKey(skuId);
        Double result = redisService.incrDoubleByCacheObject(skuStockKey, -1 * saleQty.doubleValue());
        return result >= 0;
    }


    /**
     * 获取总库存
     *
     * @param skuId SKU ID
     * @return 总库存
     */
    public BigDecimal getSkuStock(Long skuId) {
        if (Objects.isNull(skuId)) {
            return BigDecimal.ZERO;
        }
        String skuStocKey = getSkuStockKey(skuId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object stockQty = redisService.getCacheObject(skuStocKey);
        if (stockQty == null) {
            SkuApi skuApi = SpringUtils.getBean(SkuApi.class);
            if (Objects.nonNull(skuApi)) {
                SkuDTO skuDTO = skuApi.getBySkuId(skuId).getCheckedData();
                if (Objects.nonNull(skuDTO)) {
                    setSkuStock(skuId, skuDTO.getStock());
                    return skuDTO.getStock();
                } else {
                    stockQty = NumberPool.INT_ZERO;
                    setSkuStock(skuId, new BigDecimal(stockQty.toString()));
                }
            } else {
                stockQty = NumberPool.INT_ZERO;
            }
        }
        return ToolUtil.isEmptyReturnBigDecimal(stockQty, BigDecimal.ZERO);
    }

    public Double incrSkuSaledQty(Long skuId, BigDecimal saleQty) {
        String skuSaledKey = getSkuSeledStockKey(skuId);
        Double res = redisService.incrDoubleByCacheObject(skuSaledKey, saleQty.doubleValue());
        // 变动了已售就需要操作库存变更
        pushSkuStockUpdate(skuId);
        log.info("已售库存变动-incrSkuSaledQty- 时间：{}， SKUID：{}, 变动数量：{}， 变动结果：{}", DateUtils.getTime(), skuId, saleQty, res);
        return res;
    }

    /**
     * 增加已售库存
     *
     * @param skuId
     * @param saleQty
     * @param enableNegativeStock 是否允许负库存
     * @return 允许负库存下单：true 库存充足，false 库存不足
     *         不允许负库存下单：true 占用成功，false 占用失败
     */
    @DistributedLock(prefix = RedisLockConstants.LOCK_INCR_SALED_QTY, condition = "#skuId")
    public boolean incrSkuSaledQtySafe(Long skuId, BigDecimal saleQty, boolean enableNegativeStock) {
        // 剩余可售库存
        BigDecimal surplusSaleQty = getSurplusSaleQtyBigDecimal(skuId);
        if (enableNegativeStock) {
            incrSkuSaledQty(skuId, saleQty);
            return surplusSaleQty.compareTo(saleQty) >= 0;
        }
        if (surplusSaleQty.compareTo(saleQty) >= 0) {
            incrSkuSaledQty(skuId, saleQty);
            return true;
        }
        return false;
    }

    /**
     * 占用库存
     *
     * @param skuId       SKU ID
     * @param occupiedQty
     * @return
     */
    public Double incrSkuOccupiedQty(Long skuId, BigDecimal occupiedQty) {
        String skuOccupiedKey = SKU_STOCK + skuId + OCCUPIED_STOCK_QTY;
        Double res = redisService.incrDoubleByCacheObject(skuOccupiedKey, occupiedQty.doubleValue());
        log.info("已占用库存变动-incrSkuOccupiedQty- 时间：{}， SKUID：{}, 变动数量：{}， 变动结果：{}", DateUtils.getTime(), skuId, occupiedQty, res);
        // 兜底占用数不可能是负数
        if (res == null || res < 0) {
            log.error("占用库存不能小于0,SKUID：{}", skuId);
            redisService.setCacheObject(skuOccupiedKey, 0);
        }
        return res;
    }

    /**
     * 增加已占用库存
     * @param skuId
     * @param occupiedQty
     * @param enableNegativeStock 是否允许负库存
     * @return 允许负库存下单：true 库存充足，false 库存不足
     *         不允许负库存下单：true 占用成功，false 占用失败
     */
    @DistributedLock(prefix = RedisLockConstants.LOCK_INCR_SALED_QTY, condition = "#skuId")
    public boolean incrSkuOccupiedQtySafe(Long skuId, BigDecimal occupiedQty, boolean enableNegativeStock) {
        log.info("incrSkuOccupiedQtySafe增加已占用库存，skuId：{}， occupiedQty：{}，enableNegativeStock：{}", skuId, occupiedQty, enableNegativeStock);
        // 剩余可售库存
        BigDecimal surplusSaleQty = getSurplusSaleQtyBigDecimal(skuId);
        if (enableNegativeStock) {
            incrSkuOccupiedQty(skuId, occupiedQty);
            return surplusSaleQty.compareTo(occupiedQty) >= 0;
        }
        if (surplusSaleQty.compareTo(occupiedQty) >= 0) {
            incrSkuOccupiedQty(skuId, occupiedQty);
            return true;
        }
        return false;
    }

    /**
     * 总库存调整
     *
     * @param skuId    skuId
     * @param stockQty
     * @return
     */
    public Double incrSkuStockQty(Long skuId, BigDecimal stockQty) {
        String skuSaledKey = getSkuStockKey(skuId);
        Double res = redisService.incrDoubleByCacheObject(skuSaledKey, stockQty.doubleValue());
        // 变动了已售就需要操作库存变更
        pushSkuStockUpdate(skuId);
        log.info("总库存变动-incrSkuStockQty- 时间：{}， SKUID：{}, 变动数量：{}， 变动结果：{}", DateUtils.getTime(), skuId, stockQty, res);
        return res;
    }

    public BigDecimal getSkuOccupiedQty(Long skuId) {
        String skuOccupiedKey = SKU_STOCK + skuId + OCCUPIED_STOCK_QTY;
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object occupiedQty = redisService.getCacheObject(skuOccupiedKey);
        return ToolUtil.isEmptyReturnBigDecimal(occupiedQty, BigDecimal.ZERO);
    }

    /**
     * 获取已售库存
     *
     * @param skuId SKU ID
     * @return 已售库存
     */
    public BigDecimal getSkuSaledQty(Long skuId) {
        String skuSaledKey = getSkuSeledStockKey(skuId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(skuSaledKey);
        return ToolUtil.isEmptyReturnBigDecimal(saledQty, BigDecimal.ZERO);
    }

    public BigDecimal getOccupiedQty(Long skuId) {
        String skuOccupiedKey = SKU_STOCK + skuId + OCCUPIED_STOCK_QTY;
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(skuOccupiedKey);
        return ToolUtil.isEmptyReturnBigDecimal(saledQty, BigDecimal.ZERO);
    }

    /**
     * 获取剩余库存数量 总库存 - 销售库存
     *
     * @param skuId
     * @return
     */
    public Long getSurplusSaleQty(Long skuId) {
        // 可售库存 = 总库存 - 已售
        // 2024年9月23日15:17:21
        // 参见 https://www.gitlink.org.cn/J13548784186/SaaS-B2B/issues/1472
        BigDecimal surplusSaleQty =
                getSkuStock(skuId).subtract(
                        getOccupiedQty(skuId)
                );
        log.info("获取可售库存，SKUID：{}，结果：{}", skuId, surplusSaleQty);
        //如果计算出的剩余库存小于0  则展示0
        if (surplusSaleQty.compareTo(BigDecimal.ZERO) < 0) {
            surplusSaleQty = BigDecimal.ZERO;
        }
        return surplusSaleQty.longValue();
    }

    /**
     * 获取剩余库存数量 总库存 - 占用库存
     *
     * @param skuId
     * @return
     */
    public BigDecimal getSurplusSaleQtyBigDecimal(Long skuId) {
        // 可售库存 = 总库存 - (已售 - 已同步)
        // 2024年9月23日15:17:21
        // 参见 https://www.gitlink.org.cn/J13548784186/SaaS-B2B/issues/1472
        BigDecimal surplusSaleQty =
                getSkuStock(skuId).subtract(
                        getOccupiedQty(skuId)
                );
        log.info("获取可售库存，SKUID：{}，结果：{}", skuId, surplusSaleQty);
        //如果计算出的剩余库存小于0  则展示0
        if (surplusSaleQty.compareTo(BigDecimal.ZERO) < 0) {
            surplusSaleQty = BigDecimal.ZERO;
        }
        return surplusSaleQty;
    }

    /**
     * 增加已同步库存
     *
     * @param skuId
     * @param saleQty
     * @return
     */
    public Double incrSkuSyncedQty(Long skuId, BigDecimal saleQty) {
        String skuSyncedKey = getSkuSyncedStockKey(skuId);
        Double res = redisService.incrDoubleByCacheObject(skuSyncedKey, saleQty.doubleValue());
        // 记录库存变动
        this.pushSkuStockUpdate(skuId);
        log.info("已同步库存变动-incrSkuSaledQty- 时间：{}， SKUID：{}, 变动数量：{}， 变动结果：{}", DateUtils.getTime(), skuId, saleQty, res);
        return res;
    }

    /**
     * 获取已同步库存
     *
     * @param skuId
     * @return
     */
    //@Override
    public BigDecimal getSkuSyncedQty(Long skuId) {
        String skuSyncedKey = getSkuSyncedStockKey(skuId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object syncedQty = redisService.getCacheObject(skuSyncedKey);
        if (syncedQty == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(syncedQty.toString());
    }

    public String getSkuStockKey(Long skuId) {
        return SKU_STOCK + skuId + STOCK_QTY;
    }

    private String getSkuSeledStockKey(Long skuId) {
        return SKU_STOCK + skuId + SALED_STOCK_QTY;
    }

    private String getSkuSyncedStockKey(Long skuId) {
        return SKU_STOCK + skuId + SYNCED_STOCK_QTY;
    }


    public Long getSkuAvailableStock(Long skuId) {
        Long skuStock = getSkuStock(skuId).longValue();
        Long saledStock = getSkuSaledQty(skuId).longValue();
        Long syncedStock = getSkuSyncedQty(skuId).longValue();
        Long availableStock = skuStock - (saledStock - syncedStock);
        return availableStock;
    }

    public void setSkuOccupiedQty(Long skuId, BigDecimal occupiedQty) {
        log.info("更新占用库存skuOccupiedQty，skuId:{}，occupiedQty:{}，", skuId, occupiedQty);
        String skuOccupiedKey = SKU_STOCK + skuId + OCCUPIED_STOCK_QTY;
        redisService.setCacheObject(skuOccupiedKey, occupiedQty.intValue());
    }
}
