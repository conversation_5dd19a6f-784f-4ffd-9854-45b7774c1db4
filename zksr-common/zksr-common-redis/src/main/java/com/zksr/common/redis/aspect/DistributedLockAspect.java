package com.zksr.common.redis.aspect;

import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.lock.DistributedLockCallback;
import com.zksr.common.redis.lock.DistributedLockTemplate;
import com.zksr.common.core.utils.SpELParserUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Aspect
@Slf4j
@Component
public class DistributedLockAspect {

    @Autowired
    private DistributedLockTemplate lockTemplate;

    @Pointcut("@annotation(com.zksr.common.redis.annotation.DistributedLock)")
    public void DistributedLockAspect() {}

    @Around(value = "DistributedLockAspect()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        //得到使用注解的方法。可使用Method.getAnnotation(Class<T> annotationClass)获取指定的注解，然后可获得注解的属性
        Method method = ((MethodSignature)pjp.getSignature()).getMethod();
        Object[] arguments = pjp.getArgs();
        String lockName = null;
        try {
            lockName = getLockName(method, arguments);
        } catch (Exception e) {
            log.error("锁异常, 使用方法名操作全局redis锁", e);
            lockName = method.getName();
        }
        return lock(pjp, method, lockName);
    }

    public String getLockName(Method method, Object[] args) {
        Objects.requireNonNull(method);
        DistributedLock annotation = method.getAnnotation(DistributedLock.class);
        // el 表达式
        String lockName;
        if (StringUtils.isNotEmpty(annotation.lockName())) {
            lockName = annotation.lockName();
        } else {
            String elExpression = annotation.condition();
            if (StringUtils.isEmpty(elExpression)) {
                throw new ServiceException("elExpression 不能为空");
            }
            lockName = SpELParserUtils.parse(method, args, elExpression, String.class);
        }
        String lockNamePre = annotation.prefix();
        if (StringUtils.isNotEmpty(lockNamePre)) {
            lockName = lockNamePre + annotation.separator() + lockName;
        }
        String lockNamePost = annotation.suffix();
        if (StringUtils.isNotEmpty(lockNamePost)) {
            lockName = lockName + annotation.separator() + lockNamePost;
        }
        return lockName;
    }


    public Object lock(ProceedingJoinPoint pjp, Method method, final String lockName) {

        DistributedLock annotation = method.getAnnotation(DistributedLock.class);

        boolean fairLock = annotation.fairLock();

        boolean tryLock = annotation.tryLock();

        if (tryLock) {
            return tryLock(pjp, annotation, lockName, fairLock);
        } else {
            return lock(pjp,lockName, fairLock);
        }
    }

    public Object lock(ProceedingJoinPoint pjp, final String lockName, boolean fairLock) {
        return lockTemplate.lock(new DistributedLockCallback<Object>() {
            @Override
            public Object process() {
                return proceed(pjp);
            }

            @Override
            public String getLockName() {
                return lockName;
            }
        }, fairLock);
    }

    public Object tryLock(ProceedingJoinPoint pjp, DistributedLock annotation, final String lockName, boolean fairLock) {

        long waitTime = annotation.waitTime(),
                leaseTime = annotation.leaseTime();
        TimeUnit timeUnit = annotation.timeUnit();

        return lockTemplate.tryLock(new DistributedLockCallback<Object>() {
            @Override
            public Object process() {
                return proceed(pjp);
            }

            @Override
            public String getLockName() {
                return lockName;
            }
        }, waitTime, leaseTime, timeUnit, fairLock);
    }

    public Object proceed(ProceedingJoinPoint pjp) {
        try {
            return pjp.proceed();
        } catch (ServiceException e) {
            // 捕获自定义异常, 兼容前端异常结果
            throw e;
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    private boolean isEmpty(Object str) {
        return str == null || "".equals(str);
    }

    private boolean isNotEmpty(Object str) {
        return !isEmpty(str);
    }
}
