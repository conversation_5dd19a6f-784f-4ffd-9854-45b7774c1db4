package com.zksr.common.redis.utils;

import cn.hutool.http.HttpUtil;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisSysConfigService;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 获取accesstoken
 * @date 2024/8/19 16:23
 */
public class RedisWxTokenUtil {

    /**
     * 获取微信accesstoken
     * @param appid appid
     * @return
     */
    public static String getAppletAccessToken(String appid) {
        // 如果配置中心accessToken维护, 就从中心获取
        RedisSysConfigService sysConfigService = SpringUtils.getBean(RedisSysConfigService.class);
        String requestUrl = sysConfigService.getAccessTokenUrl(appid);
        if (StringUtils.isNotEmpty(requestUrl)) {
            // 替换关键字
            String accessToken = HttpUtil.get(requestUrl.replace("{appid}", appid));
            if (StringUtils.isNotEmpty(accessToken)) {
                return accessToken;
            }
        }
        // 没有还是从缓存获取
        RedisService redisService = SpringUtils.getBean(RedisService.class);
        return redisService.getCacheObject(RedisConstants.ACCESS_TOKEN + appid);
    }
}
