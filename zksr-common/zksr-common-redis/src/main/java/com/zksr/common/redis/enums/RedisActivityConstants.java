package com.zksr.common.redis.enums;

import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.utils.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销活动缓存键
 * @date 2024/5/14 9:59
 */
public class RedisActivityConstants {

    /**
     * 促销活动主键
     */
    public static final String ACTIVITY = "activity";

    public static final String SUPPLIER = "supplier";

    /**
     * 促销活动门店销售数量KEY
     */
    private static final String ACTIVITY_BRANCH_SALE_QTY = "activity_branch_sale_qty:";
    /**
     * 促销活动门店销售数量KEY（中单位）
     */
    private static final String ACTIVITY_BRANCH_SALE_QTY_MID = "activity_branch_sale_qty_mid:";
    /**
     * 促销活动门店销售数量KEY（大单位）
     */
    private static final String ACTIVITY_BRANCH_SALE_QTY_LARGE = "activity_branch_sale_qty_large:";
    /**
     * 促销活动门店销售中单位数量KEY
     */
    private static final String ACTIVITY_BRANCH_MID_SALE_QTY = "activity_branch_mid_sale_qty:";
    /**
     * 促销活动门店销售大单位数量KEY
     */
    private static final String ACTIVITY_BRANCH_LARGE_SALE_QTY = "activity_branch_large_sale_qty:";
    /**
     * 促销活动销售数量KEY
     */
    private static final String ACTIVITY_SALE_QTY = "activity_sale_qty:";

    /**
     * 促销活动销售数量KEY（中单位）
     */
    private static final String ACTIVITY_MID_SALE_QTY = "activity_mid_sale_qty:";

    /**
     * 促销活动销售数量KEY（大单位）
     */
    private static final String ACTIVITY_LARGE_SALE_QTY = "activity_large_sale_qty:";

    /**
     * 活动次数达到上限
     */
    private static final String ACTIVITY_TIMES_RULE = "activity_times_rule:";

    /**
     * 活动次数达到上限（中单位）
     */
    private static final String ACTIVITY_TIMES_RULE_MID = "activity_times_rule_mid:";

    /**
     * 活动次数达到上限（大单位）
     */
    private static final String ACTIVITY_TIMES_RULE_LARGE = "activity_times_rule_large:";

    /**
     * 获取活动缓存, 按照入驻商分组的
     * @param supplierId    入驻商ID
     * @return
     */
    public static String getActivity(Long supplierId) {
        return StringUtils.format("{}:{}", ACTIVITY, supplierId);
    }

    /**
     * 获取门店特价已购数量KEY
     * @param branchId
     * @param activityId
     * @return
     */
    public static String getSpActivityTotalSaleNumKey(Long branchId, Long activityId) { return StringUtils.format("{}:{}:{}:{}", ACTIVITY_BRANCH_SALE_QTY, PrmNoEnum.SP.getType(), branchId, activityId); }


    /**
     * 获取门店特价已购数量KEY
     * @param branchId
     * @param activityId
     * @param activityRuleId
     * @return
     */
    public static String getSpSaleNumKey(Long branchId, Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}:{}", ACTIVITY_BRANCH_SALE_QTY, PrmNoEnum.SP.getType(), branchId, activityId, activityRuleId); }

    /**
     * 获取门店特价已购数量KEY（中单位）
     * @param branchId
     * @param activityId
     * @param activityRuleId
     * @return
     */
    public static String getSpMidSaleNumKey(Long branchId, Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}:{}", ACTIVITY_BRANCH_SALE_QTY_MID, PrmNoEnum.SP.getType(), branchId, activityId, activityRuleId); }

    /**
     * 获取门店特价已购数量KEY（大单位）
     * @param branchId
     * @param activityId
     * @param activityRuleId
     * @return
     */
    public static String getSpLargeSaleNumKey(Long branchId, Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}:{}", ACTIVITY_BRANCH_SALE_QTY_LARGE, PrmNoEnum.SP.getType(), branchId, activityId, activityRuleId); }

    /**
     * 获取特价总已购数量KEY
     * @param activityId
     * @param activityRuleId
     * @return
     */
    public static String getSpTotalSaleNumKey(Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}", ACTIVITY_SALE_QTY, PrmNoEnum.SP.getType(), activityId, activityRuleId); }

    /**
     * 获取特价中单位总已购数量KEY
     * @param activityId
     * @param activityRuleId
     * @return
     */
    public static String getSpMidTotalSaleNumKey(Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}", ACTIVITY_MID_SALE_QTY, PrmNoEnum.SP.getType(), activityId, activityRuleId); }

    /**
     * 获取特价大单位总已购数量KEY
     * @param activityId
     * @param activityRuleId
     * @return
     */
    public static String getSpLargeTotalSaleNumKey(Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}", ACTIVITY_LARGE_SALE_QTY, PrmNoEnum.SP.getType(), activityId, activityRuleId); }

    /**
     * 获取门店秒杀已购数量KEY
     * @param branchId
     * @param activityId
     * @param activityRuleId
     * @return
     */
    public static String getSkSaleNumKey(Long branchId, Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}:{}", ACTIVITY_BRANCH_SALE_QTY, PrmNoEnum.SK.getType(), branchId, activityId, activityRuleId); }

    /**
     * 获取门店秒杀已购中单位数量KEY
     * @param branchId
     * @param activityId
     * @param activityRuleId
     * @return
     */
    public static String getSkMidSaleNumKey(Long branchId, Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}:{}", ACTIVITY_BRANCH_MID_SALE_QTY, PrmNoEnum.SK.getType(), branchId, activityId, activityRuleId); }

    /**
     * 获取门店秒杀已购大单位数量KEY
     * @param branchId
     * @param activityId
     * @param activityRuleId
     * @return
     */
    public static String getSkLargeSaleNumKey(Long branchId, Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}:{}", ACTIVITY_BRANCH_LARGE_SALE_QTY, PrmNoEnum.SK.getType(), branchId, activityId, activityRuleId); }

    /**
     * 获取秒杀总已购数量KEY
     * @param activityId
     * @param activityRuleId
     * @return
     */
    public static String getSkTotalSaleNumKey(Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}", ACTIVITY_SALE_QTY, PrmNoEnum.SK.getType(), activityId, activityRuleId); }

    /**
     * 获取秒杀总已购数量KEY（中单位）
     * @param activityId
     * @param activityRuleId
     * @return
     */
    public static String getSkMidTotalSaleNumKey(Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}", ACTIVITY_MID_SALE_QTY, PrmNoEnum.SK.getType(), activityId, activityRuleId); }

    /**
     * 获取秒杀总已购数量KEY（大单位）
     * @param activityId
     * @param activityRuleId
     * @return
     */
    public static String getSkLargeTotalSaleNumKey(Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}", ACTIVITY_LARGE_SALE_QTY, PrmNoEnum.SK.getType(), activityId, activityRuleId); }

    /**
     * 获取满赠 总赠送数量KEY
     * @param activityId 活动ID
     * @param activityRuleId 活动规则ID
     * @return
     */
    public static String getFgTotalSaleNumKey(Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}", ACTIVITY_SALE_QTY, PrmNoEnum.FG.getType(), activityId, activityRuleId); }

    /**
     * 获取买赠 总赠送数量KEY
     * @param activityId 活动ID
     * @param activityRuleId 活动规则ID
     * @return
     */
    public static String getBgTotalSaleNumKey(Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}", ACTIVITY_SALE_QTY, PrmNoEnum.BG.getType(), activityId, activityRuleId); }

    /**
     * 获取组合商品 总购买数量KEY
     * @param activityId 活动ID
     * @param activityRuleId 活动规则ID
     * @return
     */
    public static String getCbTotalSaleNumKey(Long activityId, Long activityRuleId) { return StringUtils.format("{}:{}:{}:{}", ACTIVITY_SALE_QTY, PrmNoEnum.CB.getType(), activityId, activityRuleId); }

    /**
     * 促销活动达到上限
     * @param activityId 活动ID
     * @param branchId   门店ID
     * @return
     */
    public static String getTimesRuleKey(Long activityId, Long branchId) { return StringUtils.format("{}{}:{}", ACTIVITY_TIMES_RULE, activityId, branchId); }

    /**
     * 促销活动规则达到上限
     * @param activityId 活动ID
     * @param branchId   门店ID
     * @return
     */
    public static String getTimesRuleKey(Long activityId, Long activityRuleId, Long branchId) { return StringUtils.format("{}{}:{}:{}", ACTIVITY_TIMES_RULE, activityId, activityRuleId, branchId); }

    /**
     * 组合促销发布城市更新set
     */
    public static final String ACTIVITY_CB_AREA_UPDATE_SET = "activity_cb_area_update_set";

    /**
     * 组合促销发布城市入驻商
     */
    public static final String ACTIVITY_CB_LOCAL_SUPPLIER_SET = "activity_cb_local_supplier_set:";

    /**
     * 组合促销发布全国入驻商
     */
    public static final String ACTIVITY_CB_GLOBAL_SUPPLIER_SET = "activity_cb_global_supplier_set:";
}
