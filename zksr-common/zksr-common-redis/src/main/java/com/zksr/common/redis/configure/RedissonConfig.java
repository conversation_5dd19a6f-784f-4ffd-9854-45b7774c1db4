package com.zksr.common.redis.configure;

import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.lock.DistributedLockTemplate;
import com.zksr.common.redis.lock.SingleDistributedLockTemplate;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

@Slf4j
@Configuration
@ConfigurationProperties(prefix = "spring.redis")
public class RedissonConfig {

    private String host;

    private Cluster cluster;

    private String port;

    private String password;

    private Integer database;

    // subscription-connection-minimum-idle-size
    private Integer subscriptionConnectionMinimumIdleSize;

    // connection-minimum-idle-size
    private Integer connectionMinimumIdleSize;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getDatabase() {
        return database;
    }

    public void setDatabase(Integer database) {
        this.database = database;
    }

    public Integer getSubscriptionConnectionMinimumIdleSize() {
        return subscriptionConnectionMinimumIdleSize;
    }

    public void setSubscriptionConnectionMinimumIdleSize(Integer subscriptionConnectionMinimumIdleSize) {
        this.subscriptionConnectionMinimumIdleSize = subscriptionConnectionMinimumIdleSize;
    }

    public Integer getConnectionMinimumIdleSize() {
        return connectionMinimumIdleSize;
    }

    public void setConnectionMinimumIdleSize(Integer connectionMinimumIdleSize) {
        this.connectionMinimumIdleSize = connectionMinimumIdleSize;
    }

    public Cluster getCluster() {
        return cluster;
    }

    public void setCluster(Cluster cluster) {
        this.cluster = cluster;
    }

    @Bean(name = "customLockRedisson", destroyMethod="shutdown")
    RedissonClient redisson() throws IOException {
        //1、创建配置
        Config config = new Config();
        if(ToolUtil.isEmpty(database)){
            database = 0;
        }
        log.info("开始初始化 redisson");

        // 节点数量
        if (Objects.nonNull(cluster) && !cluster.nodes.isEmpty()) {
            String[] arr = new String[cluster.nodes.size()];
            for (int line = 0; line < cluster.nodes.size(); line++) {
                arr[line] = StringUtils.format("{}{}", "redis://", cluster.nodes.get(line));
            }
            config.useClusterServers()
                    .setPassword(password)
                    .addNodeAddress(arr)
                    .setIdleConnectionTimeout(10000)
                    .setConnectTimeout(10000)
                    .setTimeout(3000)
                    .setRetryAttempts(3)
                    .setRetryInterval(1500)
                    .setSubscriptionsPerConnection(5)
                    .setSubscriptionConnectionMinimumIdleSize(Objects.nonNull(subscriptionConnectionMinimumIdleSize) ? subscriptionConnectionMinimumIdleSize : 10)
                    .setSubscriptionConnectionPoolSize(200)
                    .setMasterConnectionMinimumIdleSize(Objects.nonNull(connectionMinimumIdleSize) ? connectionMinimumIdleSize : 24)
                    .setMasterConnectionPoolSize(256)
                    .setSlaveConnectionMinimumIdleSize(Objects.nonNull(connectionMinimumIdleSize) ? connectionMinimumIdleSize : 24)
                    .setSlaveConnectionPoolSize(256)
                    .setDnsMonitoringInterval(5000);
        } else {
            // 密码
            if(ToolUtil.isNotEmpty(password)){
                config.useSingleServer().setAddress("redis://" + host + ":" + port)
                        .setPassword(password)
                        .setIdleConnectionTimeout(10000)
                        .setConnectTimeout(10000)
                        .setTimeout(3000)
                        .setRetryAttempts(3)
                        .setRetryInterval(1500)
                        .setSubscriptionsPerConnection(5)
                        .setSubscriptionConnectionMinimumIdleSize(Objects.nonNull(subscriptionConnectionMinimumIdleSize) ? subscriptionConnectionMinimumIdleSize : 10)
                        .setSubscriptionConnectionPoolSize(200)
                        .setConnectionMinimumIdleSize(Objects.nonNull(connectionMinimumIdleSize) ? connectionMinimumIdleSize : 50)
                        .setConnectionPoolSize(256)
                        .setDatabase(database)
                        .setDnsMonitoringInterval(5000);
            }else{
                config.useSingleServer().setAddress("redis://" + host + ":" + port)
                        .setIdleConnectionTimeout(10000)
                        .setConnectTimeout(10000)
                        .setTimeout(3000)
                        .setRetryAttempts(3)
                        .setRetryInterval(1500)
                        .setSubscriptionsPerConnection(5)
                        .setSubscriptionConnectionMinimumIdleSize(Objects.nonNull(subscriptionConnectionMinimumIdleSize) ? subscriptionConnectionMinimumIdleSize : 10)
                        .setSubscriptionConnectionPoolSize(200)
                        .setConnectionMinimumIdleSize(Objects.nonNull(connectionMinimumIdleSize) ? connectionMinimumIdleSize : 50)
                        .setConnectionPoolSize(256)
                        .setDatabase(database)
                        .setDnsMonitoringInterval(5000);
            }
        }
        return Redisson.create(config);
    }

    @Bean
    DistributedLockTemplate distributedLockTemplate(@Qualifier("customLockRedisson") RedissonClient redissonClient) {
        return new SingleDistributedLockTemplate(redissonClient);
    }


    public static class Cluster {
        private List<String> nodes;

        public List<String> getNodes() {
            return nodes;
        }

        public void setNodes(List<String> nodes) {
            this.nodes = nodes;
        }
    }
}
