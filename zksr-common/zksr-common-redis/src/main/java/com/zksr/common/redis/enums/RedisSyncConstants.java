package com.zksr.common.redis.enums;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import com.zksr.common.core.utils.StringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: redis 优惠券rediskey
 * @date 2024/3/26 17:30
 */
public class RedisSyncConstants {

    /**
     * 同步数据 缓存
     */
    public static final String SYNC =  "sync_";

    /**
     * 可视化 缓存
     */
    public static final String VISUAL =  "visual_";


    /**
     * 对接第三方 -- 好帮你  鉴权token
     */
    public static final String SYNC_HAO_BANG_NI_TOKEN = "sync_hao_bang_ni_token:";

    /**
     * 对接第三方 -- 好帮你 token失效时间 默认3天
     */
    public static final long SYNC_HAO_BANG_NI_TOKEN_TIME = 3L;

    /**
     * 批量按区域同步第三方门店信息  -- 设置Redis锁   十分钟内不能重复调用
     */
    public static final String SYNC_AREA_BRANCH = "sync_area_branch:";


}
