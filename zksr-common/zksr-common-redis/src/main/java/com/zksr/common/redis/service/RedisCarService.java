package com.zksr.common.redis.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.domain.dto.car.*;
import com.zksr.common.core.domain.vo.car.AppCarPageReqVO;
import com.zksr.common.core.domain.vo.car.AppCarPageRespVO;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.redis.enums.RedisCarConstants;
import com.zksr.trade.api.car.dto.AppCarEventDTO;
import com.zksr.trade.api.car.vo.CarSaveReqVO;
import com.zksr.trade.api.car.vo.CarSelectedReqVO;
import com.zksr.trade.api.car.vo.CarTotalRespVO;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: redis 购物车通用数据
 * @date 2024/5/9 16:12
 */
@Component
@SuppressWarnings("all")
public class RedisCarService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedisTemplate redisTemplate;

    // 获取购物车数据, 区分全国和本地
    public AppCarPageRespVO getAppCarItemListPage(@Valid AppCarPageReqVO reqVO) {
        Integer pageNo = reqVO.getPageNo();
        Integer pageSize = reqVO.getPageSize();
        Integer offset = (pageNo - 1) * pageSize;
        AppCarPageRespVO pageRespVO = new AppCarPageRespVO();
        // 购物车 本地 | 全国 缓存key
        String productTypeKey = RedisCarConstants.getBranchTotalKey(reqVO.getBranchId(), reqVO.getProductType());
        pageRespVO.setTotal(redisService.getCacheZSetSize(productTypeKey));
        if (Objects.isNull(pageRespVO.getTotal()) || pageRespVO.getTotal() == NumberPool.LONG_ZERO) {
            return pageRespVO;
        }
        ArrayList<AppCarItemDTO> list = new ArrayList<>();
        // 获得对象列表
        Set<String> unitList = redisService.getCacheZSetRange(productTypeKey, offset, offset + pageSize - 1);
        List<AppCarUnitDTO> appCarUnitDTOList = unitList.stream().map(AppCarUnitDTO::convert).collect(Collectors.toList());
        for (AppCarUnitDTO carUnitDTO : appCarUnitDTOList) {
            String carItemKey = RedisCarConstants.getSupplierItemKey(reqVO.getBranchId(), reqVO.getProductType(), carUnitDTO.getUnitSize(), carUnitDTO.getItemId());
            // 获取数据
            AppCarStorageDTO storageDTO = AppCarStorageDTO.build(redisService.getCacheMap(carItemKey));
            if (Objects.nonNull(storageDTO)) {
                AppCarItemDTO carId = BeanUtil.copyProperties(storageDTO, AppCarItemDTO.class);
                carId.setType(reqVO.getProductType());
                carId.setBranchId(reqVO.getBranchId());
                carId.setProductNum(storageDTO.getProductNum());
                carId.setRecommendFlag(storageDTO.getRecommendFlag());
                carId.setCommandId(storageDTO.getCommandId());
                carId.setDoubleOrder(storageDTO.getDoubleOrder());
                carId.setSelected(
                        this.isSelected(reqVO.getBranchId(), reqVO.getProductType(), carUnitDTO.getItemId(), carUnitDTO.getUnitSize()) ? NumberPool.INT_ONE : NumberPool.INT_ZERO
                );
                list.add(carId);
            }
        }
        pageRespVO.setList(list);
        return pageRespVO;
    }

    /**
     * 选中以及事件通知
     * @param saveReqVO
     * @param carId
     */
    public void selected(CarSaveReqVO saveReqVO, AppCarIdDTO carId) {
        if (saveReqVO.getSelected()) {
            ArrayList<AppCarUnitDTO> sleectedList = new ArrayList<>();
            sleectedList.add(AppCarUnitDTO.convert(carId));
            // 购物车结算排序调整
            List<AppCarUnitDTO> itemList = getSupplierItemList(carId.getBranchId(), carId.getType(), carId.getSupplierItemId());
            // 把当前整个选中的驻商商品全部往前排
            itemList.forEach(carUnitDTO -> {
                if (isSelected(carId.getBranchId(), carId.getType(), carUnitDTO.getItemId(), carUnitDTO.getUnitSize())) {
                    sleectedList.add(carUnitDTO);
                }
            });
            // 翻转集合, 最后添加的scope分约大排的越靠前
            Collections.reverse(sleectedList);
            // 更新选中
            updateSelected(carId.getBranchId(), NumberPool.INT_ZERO, carId.getType(), sleectedList);
        }
    }

    /**
     * 更新购物车选中
     * @param branchId    门店ID
     * @param opType      选中类型
     * @param productType 商品类型
     * @param skuId       SKUID
     * @return
     */
    public AppCarEventDTO updateSelected(Long branchId, Integer opType, String productType, List<AppCarUnitDTO> carUnitList) {
        String enventType = AppCarEventDTO.TYPE_STANDARD;
        List<AppCarIdDTO> appCarIdList = new ArrayList<>();
        switch (opType) {
            case NumberPool.INT_THREE:
                // 全不选, 直接删除选列表中键
                redisTemplate.delete(RedisCarConstants.getBranchSelectKey(branchId, productType));
                enventType = AppCarEventDTO.TYPE_UNSELECTED_ALL;
                break;
            case NumberPool.INT_TWO:
                // 购物车全选
                selectedAll(branchId, productType);
                enventType = AppCarEventDTO.TYPE_SELECTED_ALL;
                break;
            case NumberPool.INT_ONE:
                // 取消选中
                appCarIdList = cancelSeleted(branchId, productType, carUnitList);
                break;
            default:
                // 选中
                appCarIdList = batchSelected(branchId, productType, carUnitList);
                break;
        }
        return new AppCarEventDTO(appCarIdList, enventType, branchId);
    }

    /**
     * 批量选中
     * @param branchId
     * @param productType
     * @param skuIdList SKU集合
     */
    private List<AppCarIdDTO> batchSelected(Long branchId, String productType, List<AppCarUnitDTO> carUnitList) {
        List<AppCarIdDTO> result = new ArrayList<>();
        for (AppCarUnitDTO carUnitDTO : carUnitList) {
            // 增加选中
            // car:branchId:selected = [carId, carId2]
            redisTemplate.opsForSet().add(RedisCarConstants.getBranchSelectKey(branchId, productType), carUnitDTO.toString());
            AppCarIdDTO carIdDTO = this.getAppCarIdBySkuId(branchId, productType, carUnitDTO);
            if (Objects.nonNull(carIdDTO)) {
                result.add(carIdDTO);
            }
        }
        return result;
    }
    /**
     * 批量取消选中
     * @param branchId
     * @param productType
     * @param skuIdList     SKU集合
     */
    private List<AppCarIdDTO> cancelSeleted(Long branchId, String productType, List<AppCarUnitDTO> carUnitList) {
        List<AppCarIdDTO> result = new ArrayList<>();
        for (AppCarUnitDTO carUnitDTO : carUnitList) {
            // 删除选中
            // car:branchId:selected = [carId, carId2]
            redisTemplate.opsForSet().remove(RedisCarConstants.getBranchSelectKey(branchId, productType), carUnitDTO.toString());
            AppCarIdDTO carIdDTO = this.getAppCarIdBySkuId(branchId, productType, carUnitDTO);
            if (Objects.nonNull(carIdDTO)) {
                result.add(carIdDTO);
            }
        }
        return result;
    }

    /**
     * 全部选中
     * @param selectedReqVO
     * @param resultList
     */
    private void selectedAll(Long branchId, String productType) {
        // 先获取入驻商列表
        // car:branchId:supplier_list = {supplier1 : xxx, supplier2 : xxxx}
        Set<Long> supplierList = redisTemplate.opsForZSet().range(RedisCarConstants.getSupplierListKey(branchId, productType), NumberPool.INT_ZERO, NumberPool.LOWER_GROUND_LONG);
        for (Long supplierId : supplierList) {
            // 获取入驻商下的商品
            // car:branchId:supplierId = [catId|score, catId|core]
            List<AppCarUnitDTO> skuList = getSupplierItemList(branchId, productType, supplierId);
            for (AppCarUnitDTO carUnitDTO : skuList) {
                // 构建购物车商品ID对象
                // car:branchId:selected = [carId, carId2]
                redisTemplate.opsForSet().add(RedisCarConstants.getBranchSelectKey(branchId, productType), carUnitDTO.toString());
            }
        }
    }

    /**
     * 购物车ID是否是选中
     * @param branchId      门店ID
     * @param productType   商品类型
     * @param skuId         SKUID
     * @return
     */
    public Boolean isSelected(Long branchId, String productType, Long itemId, Integer unitSize) {
        return redisTemplate.opsForSet().isMember(RedisCarConstants.getBranchSelectKey(branchId, productType), new AppCarUnitDTO(itemId, unitSize).toString());
    }

    /**
     * 增加购物车角标统计
     * @param carId
     */
    public void addTotal(AppCarIdDTO carId) {
        redisTemplate.opsForZSet().add(RedisCarConstants.getBranchTotalKey(carId.getBranchId(), carId.getType()), carId.getUniqueKey(), System.currentTimeMillis());
    }

    /**
     * 更新购物车商品在入驻商列表的排序
     * @param carId
     */
    public void updateSupplierItemList(AppCarIdDTO carId) {
        String supplierItemListKey = RedisCarConstants.getSupplierItemListKey(carId.getBranchId(), carId.getType(), carId.getSupplierId());
        if (Objects.isNull(redisTemplate.opsForZSet().rank(supplierItemListKey, carId.getUniqueKey()))) {
            redisTemplate.opsForZSet().add(
                    supplierItemListKey,
                    carId.getUniqueKey(),
                    System.currentTimeMillis()
            );
            // 保存入驻商分页列表
            // car:branchId:supplier_list = {supplier1 : xxx, supplier2 : xxxx}
            updateCarSupplierList(carId.getBranchId(), carId.getType(), carId.getSupplierId());
        }
    }

    /**
     * 更新购物车入驻商列表优先级
     * @param branchId      门店ID
     * @param productType
     * @param supplierId    入驻商ID
     */
    public void updateCarSupplierList(Long branchId, String productType, Long supplierId) {
        redisTemplate.opsForZSet().add(
                RedisCarConstants.getSupplierListKey(branchId, productType),
                supplierId,
                System.currentTimeMillis()
        );
    }


    /**
     * 购物车商品加购
     * @param carId         商品信息
     * @param saveReqVO     加购信息
     * @return  是新加入购物车的返回ture
     */
    public boolean updateCartItem(AppCarSaveReqDTO carId, CarSaveReqVO saveReqVO) {
        return updateCartItem(carId, saveReqVO, null);
    }

    /**
     * 购物车商品加购
     * @param carId         商品信息
     * @param saveReqVO     加购信息
     * @param colonelId     业务员ID
     * @return  是新加入购物车的返回ture
     */
    public boolean updateCartItem(AppCarSaveReqDTO saveReq, CarSaveReqVO saveReqVO, Long colonelId) {
        String carItemKey = RedisCarConstants.getSupplierItemKey(saveReq.getBranchId(), saveReq.getType(), saveReq.getUnitSize(), saveReq.itemId());
        Boolean isAddItem = redisTemplate.hasKey(carItemKey);
        AppCarStorageDTO appCarStorageDTO = new AppCarStorageDTO();
        appCarStorageDTO.setAreaItemId(saveReq.getAreaItemId());
        appCarStorageDTO.setSupplierId(saveReq.getSupplierId());
        appCarStorageDTO.setSupplierItemId(saveReq.getSupplierItemId());
        appCarStorageDTO.setSkuId(saveReq.getSkuId());
        appCarStorageDTO.setSpuId(saveReq.getSpuId());
        appCarStorageDTO.setUnit(saveReq.getUnit());
        appCarStorageDTO.setUnitSize(saveReq.getUnitSize());
        appCarStorageDTO.setSpuCombineId(saveReq.getSpuCombineId());
        appCarStorageDTO.setActivityId(saveReq.getActivityId());
        appCarStorageDTO.setAddTime(System.currentTimeMillis());

        boolean isColonel = Objects.nonNull(colonelId);
        appCarStorageDTO.setRecommendFlag(isColonel ? NumberPool.INT_ONE : NumberPool.INT_ZERO);
        appCarStorageDTO.setRecommendNum(isColonel ? null : NumberPool.INT_ZERO);
        // 业务员加单判断
        appCarStorageDTO.setCommandId(saveReq.getCommandId());
        if (Objects.nonNull(appCarStorageDTO.getCommandId())) {
            appCarStorageDTO.setDoubleOrder(System.currentTimeMillis());
        }
        // 如果是加单的, 那就覆盖数据
        if (!isAddItem || (Objects.nonNull(appCarStorageDTO.getCommandId()) && isColonel)) {
            redisService.setCacheMap(carItemKey, appCarStorageDTO.toMap());
        }

        // 记录业务员添加购物车
        if (isColonel) {
            redisService.setCacheSet(
                    RedisCarConstants.getColonelRecommendKey(saveReq.getBranchId()),
                    AppCarUnitDTO.build(appCarStorageDTO.itemId(), appCarStorageDTO.getUnitSize())
            );
            // 记录业务员加单门店, 每天需要维护过期加单数据
            redisService.zSetAdd(RedisCarConstants.getRecommendBranchList(DateUtil.today()), System.currentTimeMillis(), saveReq.getBranchId());
        } else {
            // 不是业务员就要移除记录
            redisService.remCacheSet(
                    RedisCarConstants.getColonelRecommendKey(saveReq.getBranchId()),
                    AppCarUnitDTO.build(appCarStorageDTO.itemId(), appCarStorageDTO.getUnitSize())
            );
            // 不是业务员直接清零
            redisService.setCacheMapValue(carItemKey, RedisCarConstants.PRODUCT_FIELD_RECOMMEND_NUM, NumberPool.INT_ZERO);
            redisService.setCacheMapValue(carItemKey, RedisCarConstants.PRODUCT_FIELD_RECOMMEND, NumberPool.INT_ZERO);
        }
        // 设置加购信息
        String infoKey = RedisCarConstants.getInfoKey(saveReq.getBranchId());
        {
            AppCarInfoDTO infoDTO = new AppCarInfoDTO(DateUtil.date());
            // 是否显示弹窗
            infoDTO.setShowWindow(isColonel ? Boolean.TRUE : Boolean.FALSE);
            if (isColonel) {
                infoDTO.setCommendTime(DateUtil.date());
                infoDTO.setCommandMemo(saveReqVO.getCommandMemo());
            }
            redisService.setCacheMap(infoKey, infoDTO.toMap());
        }

        // 记录业务员加入购物车数据
        if (NumberPool.INT_ZERO == saveReqVO.getOpType()) {
            redisService.incrCacheMapValue(carItemKey, RedisCarConstants.PRODUCT_FIELD_NUM, saveReqVO.getOpQty());
            // 增加业务员推荐
            if (isColonel)
                redisService.incrCacheMapValue(carItemKey, RedisCarConstants.PRODUCT_FIELD_RECOMMEND_NUM, saveReqVO.getOpQty());
            return false;
        } else {
            redisService.setCacheMapValue(carItemKey, RedisCarConstants.PRODUCT_FIELD_NUM, saveReqVO.getOpQty());
            // 增加业务员推荐
            if (isColonel)
                redisService.setCacheMapValue(carItemKey, RedisCarConstants.PRODUCT_FIELD_RECOMMEND_NUM, saveReqVO.getOpQty());
            return true;
        }
    }


    /**
     * 获取角标统计
     * @param branchId
     * @return
     */
    public CarTotalRespVO getTotal(Long branchId) {
        // 获取角标接口可以初始化购物车
        return new CarTotalRespVO(
                redisTemplate.opsForZSet().size(RedisCarConstants.getBranchTotalKey(branchId, ProductType.LOCAL.getType())),
                redisTemplate.opsForZSet().size(RedisCarConstants.getBranchTotalKey(branchId, ProductType.RETAIL.getType())),
                redisTemplate.opsForZSet().size(RedisCarConstants.getBranchTotalKey(branchId, ProductType.GLOBAL.getType()))
        );
    }

    /**
     * 删除购物车某些商品
     * @param removeReqVO
     * @param productTypeList
     */
    public void removeItemList(Long branchId, List<AppCarIdDTO> carIds, ArrayList<String> productTypeList) {
        for (AppCarIdDTO carId : carIds) {
            // 删除入驻商分组商品key list
            redisService.zSetRem(RedisCarConstants.getSupplierItemListKey(branchId, carId.getType(), carId.getSupplierId()), carId.getUniqueKey());
            // 删除选中商品
            redisService.remCacheSet(RedisCarConstants.getBranchSelectKey(branchId, carId.getType()), carId.getUniqueKey());
            // 删除缓存购物车商品key
            String carItemKey = RedisCarConstants.getSupplierItemKey(carId.getBranchId(), carId.getType(), carId.getUnitSize(), carId.itemId());
            redisTemplate.delete(carItemKey);
            // 移除购物车角标统计
            removeTotal(branchId, carId.getType(), carId.getUniqueKey());
            // 移除推荐
            redisService.remCacheSet(RedisCarConstants.getColonelRecommendKey(branchId), AppCarUnitDTO.convert(carId));
        }
        carIds.stream().map(AppCarIdDTO::getSupplierId).forEach(supplierId -> {
            // 删除以后判断 入驻商分组还存在否, 不存在直接再删除入驻商列表
            productTypeList.forEach(productType -> {
                if (!redisTemplate.hasKey(RedisCarConstants.getSupplierItemListKey(branchId, productType, supplierId))) {
                    redisService.zSetRem(RedisCarConstants.getSupplierListKey(branchId, productType), supplierId);
                }
            });
        });
    }

    /**
     * 移除角标统计
     * @param carId 购物车ID
     */
    private void removeTotal(Long branchId, String productType, String unitKey) {
        redisTemplate.opsForZSet().remove(RedisCarConstants.getBranchTotalKey(branchId, productType), unitKey);
    }

    /**
     * 重置购物车
     * @param branchId  门店ID
     * @param productType   商品类型
     */
    public void resetCar(Long branchId, String productType) {
        // 先获取入驻商列表
        // car:branchId:supplier_list = {supplier1 : xxx, supplier2 : xxxx}
        Set<Long> supplierList = redisTemplate.opsForZSet().range(RedisCarConstants.getSupplierListKey(branchId, productType), NumberPool.INT_ZERO, NumberPool.LOWER_GROUND_LONG);
        for (Long supplierId : supplierList) {
            // 删除商品键
            List<AppCarUnitDTO> carUnitDTOList = getSupplierItemList(branchId, productType, supplierId);
            for (AppCarUnitDTO carUnitDTO : carUnitDTOList) {
                String carItemKey = RedisCarConstants.getSupplierItemKey(branchId, productType, carUnitDTO.getUnitSize(), carUnitDTO.getItemId());
                // 删除购物车商品数据
                redisTemplate.delete(carItemKey);
                // 移除购物车角标统计
                removeTotal(branchId, productType, carUnitDTO.toString());
            }
            // 删除入驻商组
            redisTemplate.delete(RedisCarConstants.getSupplierItemListKey(branchId, productType, supplierId));
            // 删除入驻商列表
            redisTemplate.delete(RedisCarConstants.getSupplierListKey(branchId, productType));
            // 删除选中
            redisTemplate.delete(RedisCarConstants.getBranchSelectKey(branchId, productType));
        }
        // 删除全部推荐
        redisTemplate.delete(RedisCarConstants.getColonelRecommendKey(branchId));
    }

    /**
     * 获取入驻商购物车全部商品
     * @param branchId
     * @param productType
     * @param supplierId
     * @return  sku 列表
     */
    public List<AppCarUnitDTO> getSupplierItemList(Long branchId, String productType, Long supplierId) {
        String supplierGroupKey = RedisCarConstants.getSupplierItemListKey(branchId, productType, supplierId);
        Set<String> cacheSet = redisTemplate.opsForZSet().range(supplierGroupKey, NumberPool.INT_ZERO, NumberPool.LOWER_GROUND_LONG);
        if (Objects.isNull(cacheSet)) {
            return new ArrayList<>();
        }
        return cacheSet.stream().map(AppCarUnitDTO::convert).collect(Collectors.toList());
    }

    /**
     * 通过单位获取购物车ID
     * @param branchId      门店ID
     * @param productType   类型
     * @param carUnitDTO    单位
     * @return
     */
    public AppCarIdDTO getAppCarIdBySkuId(Long branchId, String productType, AppCarUnitDTO carUnitDTO) {
        String carItemKey = RedisCarConstants.getSupplierItemKey(branchId, productType, carUnitDTO.getUnitSize(), carUnitDTO.getItemId());
        // 获取数据
        AppCarStorageDTO storageDTO = AppCarStorageDTO.build(redisService.getCacheMap(carItemKey));
        if (Objects.nonNull(storageDTO)) {
            AppCarIdDTO carIdDTO = new AppCarIdDTO();
            carIdDTO.setBranchId(branchId);
            carIdDTO.setType(productType);
            carIdDTO.setSkuId(storageDTO.getSkuId());
            carIdDTO.setSupplierId(storageDTO.getSupplierId());
            carIdDTO.setAreaItemId(storageDTO.getAreaItemId());
            carIdDTO.setSupplierItemId(storageDTO.getSupplierItemId());
            carIdDTO.setUnitSize(carUnitDTO.getUnitSize());
            return carIdDTO;
        }
        return null;
    }

    /**
     * //!@小程序 - 5、购物车分页 - 4、获取购物车商品集合
     * @param branchId          门店
     * @param productType       类型
     * @param carUnitDTOList    单位集合
     * @return
     */
    public Map<String, AppCarStorageDTO> getCarStoregeList(Long branchId, String productType, List<AppCarUnitDTO> carUnitDTOList) {
        HashMap<String, AppCarStorageDTO> storageDTOHashMap = new HashMap<>();
        if (Objects.isNull(carUnitDTOList) || carUnitDTOList.isEmpty()) {
            return storageDTOHashMap;
        }
        // 小于等于3就不用走管道操作了
        if (carUnitDTOList.size() <= 3) {
            return carUnitDTOList.stream()
                    .map(carUnitDTO -> RedisCarConstants.getSupplierItemKey(branchId, productType, carUnitDTO.getUnitSize(), carUnitDTO.getItemId()))
                    .map(cacheKey -> AppCarStorageDTO.build(redisService.getCacheMap(cacheKey)))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(AppCarStorageDTO::getUniqueKey, item -> item));
        }
        List list = redisTemplate.executePipelined(new SessionCallback<Object>() {
            @Override
            public <K, V> Object execute(RedisOperations<K, V> operations) throws DataAccessException {
                HashOperations<String, Object, Object> hashOperations = (HashOperations<String, Object, Object>) operations.opsForHash();
                for (AppCarUnitDTO carUnitDTO : carUnitDTOList) {
                    String carItemKey = RedisCarConstants.getSupplierItemKey(branchId, productType, carUnitDTO.getUnitSize(), carUnitDTO.getItemId());
                    Map<Object, Object> entries = hashOperations.entries(carItemKey);
                }
                return null;
            }
        });
        if (Objects.nonNull(list) && !list.isEmpty()) {
            for (Object object : list) {
                Map<String, Object> map = (Map<String, Object>) object;
                AppCarStorageDTO storageDTO = AppCarStorageDTO.build(map);
                if (Objects.nonNull(storageDTO)) {
                    storageDTOHashMap.put(storageDTO.getUniqueKey(), storageDTO);
                }
            }
        }
        return storageDTOHashMap;
    }

    /**
     * 获取购物车商品详情数据
     * @param carUnitDTO    单位
     * @param branchId      门店
     * @param productType   类型
     * @return
     */
    public AppCarStorageDTO getAppCarStorage(AppCarUnitDTO carUnitDTO, Long branchId, ProductType productType) {
        String supplierItemKey = RedisCarConstants.getSupplierItemKey(branchId, productType.getType(), carUnitDTO.getUnitSize(), carUnitDTO.getItemId());
        return AppCarStorageDTO.build(redisService.getCacheMap(supplierItemKey));
    }

    /**
     * 获取购物车信息
     * @param branchId  门店ID
     * @return  购物车信息, 记录最后一次加购等
     */
    public AppCarInfoDTO getCarInfo(Long branchId) {
        if (Objects.isNull(branchId)) {
            return new AppCarInfoDTO();
        }
        String infoKey = RedisCarConstants.getInfoKey(branchId);
        return AppCarInfoDTO.build(
                redisService.getCacheMap(infoKey)
        );
    }
}
