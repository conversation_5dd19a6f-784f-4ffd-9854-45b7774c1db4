package com.zksr.common.redis.configure;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alicp.jetcache.support.Fastjson2ValueEncoder;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/4/19 9:43
 */
public class JetCacheFastjson2ValueEncoder extends Fastjson2ValueEncoder {

    public JetCacheFastjson2ValueEncoder(boolean useIdentityNumber) {
        super(useIdentityNumber);
    }

    @Override
    public byte[] apply(Object value) {
        return super.apply(value);
    }

    @Override
    protected byte[] encodeSingleValue(Object value) {
        return JSON.toJSONString(value, JSONWriter.Feature.WriteClassName).getBytes();
    }
}
