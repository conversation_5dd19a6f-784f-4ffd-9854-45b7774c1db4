package com.zksr.common.redis.enums;

import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.utils.StringUtils;

public class RedisConstants {

    public static final int DAY_SECONDS = 86400;

    public static final int DAY_MINUTE = 1440;

    public static final String REDIS_DIY_LOCK = "redis_diy_lock_key:";
    public static final String REDIS_DIY_ACCOUNT_FLOW = "lock_account_flow";
    public static final String REDIS_DIY_ORDER_NO = "lock_order_no";

    /**
     * 字典管理 cache key
     */
    public static final String DEMO_STU2_KEY = "demo_stu2:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 商城登陆
     */
    public final static String MALL_TOKENS = "mall_tokens:";

    /**
     * openapi登陆
     */
    public final static String OPENAPI_TOKENS = "openapi_tokens:";


    public final static String PARTNER_DTO = "partner_dto:";

    public final static String BRANCH_DTO = "branch_dto:";

    public final static String MEMBER_DTO = "member_dto:";

    public final static String AREA_DTO = "area_dto:";

    public final static String CHANNEL_DTO = "channel_dto:";

    public final static String SUPPLIER_DTO = "supplier_dto:";

    public final static String GROUP_DTO = "group_dto:";

//-----------------------------------------配置key----------------------------------------------------------
    /**
     * 软件商小程序配置key
     */
    public final static String APPLET_BASE_CONFIG_KEY = "appletBase_config_key:";

    /**
     * 软件商合利宝配置key
     */
    public final static String HELIBAOPAY_CONFIG_KEY = "heLiBaoPay_config_key:";


    /**
     * 软件商微信b2b支付配置key
     */
    public final static String WXB2B_PAY_CONFIG_KEY = "wxb2bPay_config_key:";

    /**
     * 软件商美的付配置key
     */
    public final static String MIDEAPAY_CONFIG_KEY = "mideaPay_config_key:";

    /**
     * 软件商支付配置key
     */
    public final static String PAY_CONFIG_KEY = "pay_config_key:";

    /**
     * 软件商支付账号配置key
     */
    public final static String PAY_ACCOUNT_CONFIG_KEY = "pay_account_config_key:";

    /**
     * 软件商快递查询配置key
     */
    public final static String COURIER_CONFIG_KEY = "courier_config_key:";

    /**
     * 软件商短信配置key
     */
    public final static String SMS_CONFIG_KEY = "sms_config_key:";

    /**
     * 平台商小程序设置key
     */
    public final static String APPLET_AGREEMENT_POLICY_KEY = "applet_agreement_policy_key:";

    /**
     * 运营商基础设置key
     */
    public final static String BASIC_SETTING_POLICY_KEY = "basic_setting_policy_key:";

    /**
     * 运营商订单设置key
     */
    public final static String ORDER_SETTING_POLICY_KEY = "order_setting_policy_key:";

    /**
     * 平台商提款设置key
     */
    public final static String WITHDRAWAL_SETTING_POLICY_KEY = "withdrawal_setting_policy_key:";

    /**
     * 入驻商芯烨配置
     */
    public final static String XPYUN_SETTING_POLICY_KEY = "xpyun_setting_policy_key:";

    /**
     * 入驻商售后配置
     */
    public final static String AFTER_SALE_SETTING_POLICY_KEY = "after_sale_setting_policy_key:";

    /**
     * 入驻商售后配置
     */
    public final static String FEIE_YUN_SETTING_POLICY_KEY = "feie_yun_setting_policy_key:";

    /**
     * 运营商业务员配置
     */
    public final static String COLONEL_SETTING_POLICY_KEY = "colonel_setting_policy_key:";

    /**
     * 平台商设备配置
     */
    public final static String DEVICE_SETTING_POLICY_KEY = "device_setting_policy_key:";

    /**
     * 平台商基础设置key
     */
    public final static String PARTNER_MINI_SETTING_POLICY_KEY = "partner_mini_setting_policy_key:";

    /**
     * 入驻商其他配置
     */
    public final static String SUPPLEIR_OTHER_SETTING_POLICY_KEY = "supplier_other_setting_policy_key:";

    /**
     * 运营商门店生命周期配置key
     */
    public final static String BRANCH_LIFECYCLE_SETTING_POLICY_KEY = "branch_lifecycle_setting_policy_key:";

    /**
     * 运营商其他配置
     */
    public final static String DC_OTHER_SETTING_POLICY_KEY = "dc_other_setting_policy_key:";

//-----------------------------------------配置key-end---------------------------------------------------------
    /**
     * 运营商城市关联组
     */
    public final static String DC_AREA_GROUP = "dc_area_group:";

    /**
     * 入驻商信息
     */
    public final static String SYS_SUPPLIER = "sys_supplier:";

    /**
     * 平台商管理分类
     */
    public final static String PRDT_CATGORY = "prdt_catgory:";

    /**
     * 平台商管理分类-平台商
     */
    public final static String PRDT_CATGORY_SYSCODE = "prdt_catgory_sysCode:";
    /**
     * 平台商管理分类-入驻商
     */
    public final static String PRDT_CATGORY_SUPPLIER = "prdt_catgory_supplier:";

    /**
     * 平台商屏示分类
     */
    public final static String PRDT_SALE_CLASS = "prdt_sale_class:";

    /**
     * 平台商屏示分类列表
     */
    public final static String PRDT_SALE_CLASS_LIST = "prdt_sale_class_list:";

    /**
     * 平台商品牌
     */
    public final static String PRDT_BRAND = "prdt_brand:";

    /**
     * 城市展示分类
     */
    public final static String PRDT_AREA_CLASS = "prdt_area_class:";

    /**
     * 门店绑定城市展示分类集合
     */
    public final static String BRANCH_PRDT_AREA_CLASS = "branch_prdt_area_class:";

    /**
     * 区域和渠道绑定城市展示分类
     */
    public final static String AREA_CHANNEL_PRDT_AREA_CLASS = "area_channel_prdt_area_class:";

    /**
     * 运营商信息
     */
    public final static String SYS_DC = "sys_dc:";

    /** 城市上下架 */
    public final static String PRDT_AREA_ITEM_DTO = "area_item_dto:";

    /** 全国上下架 */
    public final static String PRDT_SUPPLIER_ITEM_DTO = "supplier_item_dto:";

    /** Spu */
    public final static String PRDT_SPU_DTO = "prdt_spu_dto:";

    /** Sku */
    public final static String PRDT_SKU_DTO = "prdt_sku_dto:";

    /**
     * 购物车信息
     */
    public final static String CAR_DTO = "car_dto:";

    /**
     * 业务员信息
     */
    public final static String MEM_COLONEL = "mem_colonel:";

    /**
     * 平台管理分类分润比例
     */
    public final static String CATEGORY_RATE = "category_rate:";

    /**
     * @param categoryId   管理分类ID
     * @param areaId       城市ID
     * @return  平台管理分类分润键
     */
    public static String getCategoryRate(Long categoryId, Long areaId) {
        return StringUtils.format("{}:{}", categoryId, areaId);
    }

    /**
     * @param sysCode      平台ID
     * @param channelId    渠道ID
     * @param areaId       城市ID
     * @return  平台管理分类分润键
     */
    public static String getPageConfigKey(Long sysCode, Long channelId, Long areaId) {
        return StringUtils.format("{}:{}:{}", sysCode, channelId, areaId);
    }

    /** 城市价格方案价格码 */
    public final static String PRDT_AREA_SALE_PRICE_CODE = "prdt_area_sale_price_code:";

    /** 入驻商（全国）价格方案价格码 */
    public final static String PRDT_SUPPLIER_SALE_PRICE_CODE = "prdt_supplier_sale_price_code:";

    /** 价格方案by 城市ID、SKUID、Type */
    public final static String PRDT_SKU_PRICE_AREA_SKU_TYPE = "prdt_sku_price_area_sku_type:";

    /**
     * 优惠券模版
     */
    public final static String COUPON_TEMPLATE = "coupon_template:";
    /** 未支付订单key */
    public final static String TRD_NOT_UNPAID_ORDER = "trd_not_unpaid_order";
    /** 当日入驻商门店下单金额KEY */
    public final static String TRD_TODAY_SUPPLIER_ORDER_AMT = "trd_today_supplier_order_amt:";

    /** 当日用户的订单KEY */
    public final static String TRD_ORDER_STATUS = "trd_order_status:";

    public final static String TRD_ORDER_AMOUNT_STATISTICS = "trd_order_amount_statistics:";

    /**
     *  平台页面配置缓存
     */
    public static final String PAGES_CONFIG = "sys_pages_config:";

    /**
     *  账户缓存
     */
    public static final String ACCOUNT_KEY = "account:";

    /**
     * 获取用户缓存键
     * @param merchantId    商户ID
     * @param merchantType  商户类型    {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param platform      支付平台    {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return
     */
    public static String getAccountKey(Long merchantId, String merchantType, String platform) {
        return StringUtils.format("{}:{}:{}", merchantId, merchantType, platform);
    }


    /**
     *  短信限流
     */
    public static final String SMS_LIMIT = "sms_limit";

    /**
     *  标准验证码
     */
    public static final String SMS_CODE = "sms_code";

    /**
     *  标准验证码验证限流
     */
    public static final String SMS_CODE_LIMIT = "sms_code_limit";

    /**
     * 促销活动门店适用范围对象
     */
    public static final String BRANCH_SCOPE_LIST = "branch_scope_list:";

    /**
     * 促销活动城市适用范围缓存
     */
    public static final String CITY_SCOPE_LIST = "city_scope_list:";


    /**
     * 销活动渠道适用范围缓存
     */
    public static final String CHANNEL_SCOPE_LIST = "channel_scope_list:";

    /**
     * 促销活动spu适用范围缓存
     */
    public static final String SPU_SCOPE_LIST = "activity_spu_scope_list:";

    /**
     * 促销活动supplier适用范围缓存
     */
    public static final String SUPPLIER_SCOPE_LIST = "activity_supplier_scope_list:";

    /**
     * 登陆无需验证验证码手机号
     */
    public static final String NOT_CHECK_CODE_MOBILE = "15580493965,15243662080";

    /**
     *  入驻商促销缓存
     */
    public static final String ACTIVITY_SUPPLIER = "activity_supplier:";

    /**
     * 促销活动缓存
     */
    public static final String ACTIVITY = "activity:";

    /**
     * 促销活动规则缓存
     */
    public static final String ACTIVITY_RULE = "activity_rule";

    /**
     * 获取活动规则缓存KEY
     * @param activityType  活动类型 {@link com.zksr.common.core.enums.TrdDiscountTypeEnum}
     * @param activityId    活动ID
     * @return  活动规则列表key
     */
    public static String getActivityRuleKey(String activityType , Long activityId) {
        return StringUtils.format("{}:{}:{}", ACTIVITY_RULE, activityType, activityId);
    }

    /**
     *  促销活动特价规则缓存, 按照促销活动ID
     */
    public static final String ACTIVITY_SP_RULE = "activity_sp_rule:";

    /**
     *  促销活动秒杀规则缓存, 按照促销活动ID
     */
    public static final String ACTIVITY_SK_RULE = "activity_sk_rule:";

    /**
     * 入驻商对接系统配置
     */
    public final static String SUPPLIER_OPENAPI_SYSTEM_TYPE_KEY = "supplier_openapi_system_type_key:";

    /**
     *  促销活动满赠规则缓存, 按照促销活动ID
     */
    public static final String ACTIVITY_FG_RULE = "activity_fg_rule:";

    /**
     *  促销活动买赠规则缓存, 按照促销活动ID
     */
    public static final String ACTIVITY_BG_RULE = "activity_bg_rule:";

    /**
     *  货到付款待付款统计
     */
    public static final String HDFK_NONE_PAYMENT = "hdfk_none_payment:";

    /**
     *  spu 规格单位缓存
     */
    public static final String SPU_UNIT_GROUP = "spu_unit_group:";

    /**
     * 开放配置，根据入驻商、平台商ID
     */
    public final static String OPENSOURCE_MERCHANT_ID = "opensource_merchant_id:";

    /**
     * 可视化明细表key ： 入驻商id
     */
    public final static String VISUAL_SETTING_DETAIL_KEY = "visual_setting_detail_key:";

    /**
     * 可视化明细表key ： 入驻商id+模板类型
     */
    public final static String VISUAL_SETTING_DETAIL_BY_SUPPLIER_ID_KEY = "visual_setting_detail_by_supplier_id_key:";

    /**
     * 可视化主表key ： id+模板类型
     */
    public final static String VISUAL_SETTING_MASTER_KEY = "visual_setting_master_key:";

    /**
     * 可视化主表key ： 入驻商id+模板类型
     */
    public final static String VISUAL_SETTING_MASTER_BY_SUPPLIER_ID_KEY = "visual_setting_master_by_supplier_id_key:";

    /**
     * 可视化主模板
     */
    public final static String VISUAL_SETTING_TEMPLATE_KEY = "visual_setting_template_key:";

    /**
     * 获取商品详情 sku 列表
     * @param spuId      spu ID
     * @param areaId    城市ID
     * @param productType 商品类型
     * @return
     */
    public static String getSkuUnitKey(Long spuId, Long areaId, Long classId, String productType) {
        return StringUtils.format("{}_{}_{}_{}", spuId, productType, areaId, classId);
    }

    /**
     *  小程序微信accessToken
     *  accessToken
     */
    public static final String ACCESS_TOKEN = "access_token:";

    /**
     * 微信服务ticket
     */
    public static final String ACCESS_TICKET = "access_ticket:";

    /**
     * 微信服务商accesstoken
     */
    public static final String COMPONENT_ACCESS_TOKEN = "component_access_token:";

    /**
     * 微信服务商authorizerAccessToken
     */
    public static final String AUTHORIZER_ACCESS_TOKEN = "authorizer_access_token:";

    /**
     * 小程序授权服务商的authorizer_refresh_token
     */
    public static final String AUTHORIZER_REFRESH_TOKEN = "authorizer_refresh_token:";

    /**
     * 公众号/小程序订阅消息模版
     */
    public static final String MESSAGE_TEMPLATE = "message_template:";

    /**
     *  订单预支付成功
     */
    public static final String PRE_SUCCESS_PAY_ORDER_NO = "pre_success_pay_order_no";

    /**
     *  业务员预绑定小程序openid 凭证
     */
    public static final String COLONEL_PRE_BIND_WX_OPENID = "colonel_pre_bind_wx_openid:";

    /**
     *  业务员预绑定小程序openid 凭证
     */
    public static final String BLOCK_SCHEME_NO_CREATE_KEY = "block_scheme_no_create_key:";

    /**
     *  业务员预绑定公众号openid 凭证
     */
    public static final String COLONEL_PRE_BIND_WX_PUBLISH_OPENID = "colonel_pre_bind_wx_publish_openid:";

    /**
     * 获取预支付缓存key, 在支付回调之前验证是否已经成功支付
     * @param branchId  门店ID
     * @param orderNo   订单号
     * @return
     */
    public static String getPreSuccessPayOrderNo(Long branchId, String orderNo) {
        return StringUtils.format("{}:{}:{}", PRE_SUCCESS_PAY_ORDER_NO, branchId, orderNo);
    }

    /**
     *  城市上架商品上架状态监听
     */
    public static final String RELEASE_CITY_CHANGE_SET = "release_city_change_set";

    /**
     *  城市上架商品上架展示分类set
     */
    public static final String RELEASE_CITY_CLASS_SET = "release_city_class_set";

    /**
     *  城市上架商品上架管理分类set
     */
    public static final String RELEASE_CITY_CATEGORY_SET = "release_city_category_set";

    public static String getReleaseCityClassSet(Long areaId) {
        return StringUtils.format("{}:local:{}", RedisConstants.RELEASE_CITY_CLASS_SET, areaId);
    }

    public static String getReleaseCityCategorySet(Long areaId) {
        return StringUtils.format("{}:local:{}", RedisConstants.RELEASE_CITY_CATEGORY_SET, areaId);
    }

    public static String getGlobalReleaseClassSet(Long sysCode) {
        return StringUtils.format("{}:global:{}", RedisConstants.RELEASE_CITY_CLASS_SET, sysCode);
    }

    public static String getGlobalReleaseCategorySet(Long sysCode) {
        return StringUtils.format("{}:global:{}", RedisConstants.RELEASE_CITY_CATEGORY_SET, sysCode);
    }

    /**
     *  商品促销, 图片素材
     */
    public static final String PRDT_MATERIAL = "prdt_material:";

    /**
     * prdt_spu_combine 组合商品
     */
    public final static String PRDT_SPU_COMBINE = "prdt_spu_combine:";

    /** 搜索配置 */
    public final static String SEARCH_CONFIG = "search_config:";

    /** 要货集合 */
    public final static String YH_TEMP_LIST = "yh_tmp_list:";

    /** 订单截团日累计销售金额 */
    public final static String TRD_CUT_ORDER_AMT = "trd_cut_order_amt:";

    public final static String FUYU_TOKEN_KEY = "fuyu_token:";
}
