package com.zksr.common.redis.service;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StockUtil;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.enums.RedisCouponConstants;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import com.zksr.promotion.utils.ActivityScopeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销缓存
 * @date 2024/5/18 10:05
 */
@Component
@Slf4j
@SuppressWarnings("all")
public class RedisActivityService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedisStockService stockService;

    @Autowired
    private Cache<Long, SkuDTO> skuCache;

    @Autowired
    private Cache<Long, SpuDTO> spuCache;

    /**================================================================== 特价 ========================================================== start*/

    /** SET SET特价商品门店已购买数量
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则Id
     * @return  SET特价商品门店已购买数量
     */
    public void setSpTotalSaleNum(Long branchId, Long activityId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 门店特价已购数量
        String branchQtyKey = RedisActivityConstants.getSpActivityTotalSaleNumKey(branchId, activityId);
        redisService.incrDoubleByCacheObject(branchQtyKey, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(branchQtyKey, timeout, unit);//过期时间设置
        }
    }


    /** SET SET特价商品门店已购买数量
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则Id
     * @return  SET特价商品门店已购买数量
     */
    public void setSpSaleNum(Long branchId, Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 门店特价商品已购数量
        String branchQtyKey = RedisActivityConstants.getSpSaleNumKey(branchId, activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(branchQtyKey, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(branchQtyKey, timeout, unit);//过期时间设置
        }
    }

    /**
     * SET特价商品门店已购买数量（中单位）
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则Id
     * @return  SET特价商品门店已购买数量
     */
    public void setSpMidSaleNum(Long branchId, Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 门店特价商品已购数量
        String branchQtyKey = RedisActivityConstants.getSpMidSaleNumKey(branchId, activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(branchQtyKey, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(branchQtyKey, timeout, unit);//过期时间设置
        }
    }

    /**
     * SET特价商品门店已购买数量（大单位）
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则Id
     * @return  SET特价商品门店已购买数量
     */
    public void setSpLargeSaleNum(Long branchId, Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 门店特价商品已购数量
        String branchQtyKey = RedisActivityConstants.getSpLargeSaleNumKey(branchId, activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(branchQtyKey, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(branchQtyKey, timeout, unit);//过期时间设置
        }
    }

    /** SET 特价商品门店已购买数量
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     * @param productType   商品类型 {@link com.zksr.common.core.enums.ProductType}
     * @return  特价商品门店已购买数量
     */
    public BigDecimal getSpSaleNum(Long branchId, Long activityId, Long activityRuleId) {
        // 查询特价活动商品门店 标记活动已经达到限制
        if (redisService.hasKey(RedisActivityConstants.getTimesRuleKey(activityId, activityRuleId, branchId))) {
            return StockUtil.bigDecimal(Integer.MAX_VALUE);
        }

        String key = RedisActivityConstants.getSpSaleNumKey(branchId, activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /** SET 特价商品门店已购买数量（中单位）
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     * @param productType   商品类型 {@link com.zksr.common.core.enums.ProductType}
     * @return  特价商品门店已购买数量
     */
    public BigDecimal getSpMidSaleNum(Long branchId, Long activityId, Long activityRuleId) {
        // 查询特价活动商品门店 标记活动已经达到限制
        if (redisService.hasKey(RedisActivityConstants.getTimesRuleKey(activityId, activityRuleId, branchId))) {
            return StockUtil.bigDecimal(Integer.MAX_VALUE);
        }

        String key = RedisActivityConstants.getSpMidSaleNumKey(branchId, activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /** SET 特价商品门店已购买数量
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     * @param productType   商品类型 {@link com.zksr.common.core.enums.ProductType}
     * @return  特价商品门店已购买数量
     */
    public BigDecimal getSpLargeSaleNum(Long branchId, Long activityId, Long activityRuleId) {
        // 查询特价活动商品门店 标记活动已经达到限制
        if (redisService.hasKey(RedisActivityConstants.getTimesRuleKey(activityId, activityRuleId, branchId))) {
            return StockUtil.bigDecimal(Integer.MAX_VALUE);
        }

        String key = RedisActivityConstants.getSpLargeSaleNumKey(branchId, activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /** SET SET特价商品已购买总数量
     * @param activityId    活动ID
     * @param activityRuleId         活动规则Id
     * @return
     */
    public void setSpSaleNum(Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 特价商品已购总数量
        String key = RedisActivityConstants.getSpTotalSaleNumKey(activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(key, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(key, timeout, unit);//过期时间设置
        }
    }

    /**
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     * @param productType   商品类型 {@link com.zksr.common.core.enums.ProductType}
     * @return  特价商品总已购买数量
     */
    public BigDecimal getSpSaleNum(Long activityId, Long activityRuleId) {
        String key = RedisActivityConstants.getSpTotalSaleNumKey(activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /**================================================================== 秒杀 ========================================================== start*/

    /**
     *  SET秒杀商品门店已购数量
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     */
    public void setSkSaleNum(Long branchId, Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 门店秒杀商品已购数量
        String branchQtyKey = RedisActivityConstants.getSkSaleNumKey(branchId, activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(branchQtyKey, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(branchQtyKey, timeout, unit);//过期时间设置
        }
    }
    /**
     *  SET秒杀商品门店已购数量（中单位）
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     */
    public void setSkMidSaleNum(Long branchId, Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 门店秒杀商品已购数量
        String branchQtyKey = RedisActivityConstants.getSkMidSaleNumKey(branchId, activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(branchQtyKey, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(branchQtyKey, timeout, unit);//过期时间设置
        }
    }
    /**
     *  SET秒杀商品门店已购数量
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     */
    public void setSkLargeSaleNum(Long branchId, Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 门店秒杀商品已购数量
        String branchQtyKey = RedisActivityConstants.getSkLargeSaleNumKey(branchId, activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(branchQtyKey, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(branchQtyKey, timeout, unit);//过期时间设置
        }
    }
    /**
     *  SET秒杀商品已购买总数量
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     */
    public void setSkSaleNum(Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 秒杀商品已购总数量
        String key = RedisActivityConstants.getSkTotalSaleNumKey(activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(key, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(key, timeout, unit);//过期时间设置
        }
    }

    /**
     *  SET秒杀商品已购买总数量（中单位）
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     */
    public void setSkMidSaleNum(Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 秒杀商品已购总数量
        String key = RedisActivityConstants.getSkMidTotalSaleNumKey(activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(key, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(key, timeout, unit);//过期时间设置
        }
    }

    /**
     *  SET秒杀商品已购买总数量（大单位）
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     */
    public void setSkLargeSaleNum(Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 秒杀商品已购总数量
        String key = RedisActivityConstants.getSkLargeTotalSaleNumKey(activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(key, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(key, timeout, unit);//过期时间设置
        }
    }

    /**
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     * @param productType   商品类型 {@link com.zksr.common.core.enums.ProductType}
     * @return  秒杀商品门店已购买数量
     */
    public BigDecimal getSkSaleNum(Long branchId, Long activityId, Long activityRuleId) {
        String key = RedisActivityConstants.getSkSaleNumKey(branchId, activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /**
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     * @param productType   商品类型 {@link com.zksr.common.core.enums.ProductType}
     * @return  秒杀商品门店已购买中单位数量
     */
    public BigDecimal getSkMidSaleNum(Long branchId, Long activityId, Long activityRuleId) {
        String key = RedisActivityConstants.getSkMidSaleNumKey(branchId, activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        return Optional.ofNullable(saledQty).map(StockUtil::bigDecimal).orElse(BigDecimal.ZERO);
    }

    /**
     * @param branchId      门店ID
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     * @param productType   商品类型 {@link com.zksr.common.core.enums.ProductType}
     * @return  秒杀商品门店已购买大单位数量
     */
    public BigDecimal getSkLargeSaleNum(Long branchId, Long activityId, Long activityRuleId) {
        String key = RedisActivityConstants.getSkLargeSaleNumKey(branchId, activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        return Optional.ofNullable(saledQty).map(StockUtil::bigDecimal).orElse(BigDecimal.ZERO);
    }

    /**
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     * @param productType   商品类型 {@link com.zksr.common.core.enums.ProductType}
     * @return  秒杀商品总已购买数量
     */
    public BigDecimal getSkSaleNum(Long activityId, Long activityRuleId) {
        String key = RedisActivityConstants.getSkTotalSaleNumKey(activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /**
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     * @param productType   商品类型 {@link com.zksr.common.core.enums.ProductType}
     * @return  秒杀商品总已购买数量（中单位）
     */
    public BigDecimal getSkMidSaleNum(Long activityId, Long activityRuleId) {
        String key = RedisActivityConstants.getSkMidTotalSaleNumKey(activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /**
     * @param activityId    活动ID
     * @param activityRuleId         活动规则ID
     * @param productType   商品类型 {@link com.zksr.common.core.enums.ProductType}
     * @return  秒杀商品总已购买数量（大单位）
     */
    public BigDecimal getSkLargeSaleNum(Long activityId, Long activityRuleId) {
        String key = RedisActivityConstants.getSkLargeTotalSaleNumKey(activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /**================================================================== 满赠 ========================================================== start*/

    /**
     *  SET 满赠活动赠品已赠总数量
     * @param activityId      活动ID
     * @param activityRuleId    活动规则ID
     * @param saleQty         赠送数量
     */
    public void setFgSaleNum(Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 商品已购总数量
        String key = RedisActivityConstants.getFgTotalSaleNumKey(activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(key, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(key, timeout, unit);//过期时间设置
        }
    }

    /**
     * @param activityId    活动ID
     * @param activityRuleId    活动规则ID
     * @return  满赠活动已赠总数量
     */
    public BigDecimal getFgSaleNum(Long activityId, Long activityRuleId) {
        String key = RedisActivityConstants.getFgTotalSaleNumKey(activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /**================================================================== 买赠 ========================================================== start*/

    /**
     *  SET 买赠活动赠品已赠总数量
     * @param activityId      活动ID
     * @param activityRuleId    活动规则ID
     * @param saleQty         赠送数量
     */
    public void setBgSaleNum(Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 商品已购总数量
        String key = RedisActivityConstants.getBgTotalSaleNumKey(activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(key, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(key, timeout, unit);//过期时间设置
        }
    }

    /**
     * @param activityId    活动ID
     * @param activityRuleId      活动规则ID
     * @return  买赠活动已赠总数量
     */
    public BigDecimal getBgSaleNum(Long activityId, Long activityRuleId) {
        String key = RedisActivityConstants.getBgTotalSaleNumKey(activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /**================================================================== 买赠 ========================================================== end*/


    /**================================================================== 组合商品 ========================================================== start*/

    /**
     *  SET 组合商品活动已购总数量
     * @param activityId      活动ID
     * @param activityRuleId    活动规则ID
     * @param saleQty         赠送数量
     */
    public void setCbSaleNum(Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 商品已购总数量
        String key = RedisActivityConstants.getCbTotalSaleNumKey(activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(key, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(key, timeout, unit);//过期时间设置
        }
    }

    /**
     * @param activityId    活动ID
     * @param activityRuleId      活动规则ID
     * @return  组合商品活动已购总数量
     */
    public BigDecimal getCbSaleNum(Long activityId, Long activityRuleId) {
        String key = RedisActivityConstants.getCbTotalSaleNumKey(activityId, activityRuleId);
        //这里key如果不存在，spring-data-redis incr 会设置一个初始默认值为0 类型为Integer 导致类型转换错误 这里用Integer接收
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /**================================================================== 组合商品 ========================================================== end*/

    /**
     * 优先处理秒杀和特价等活动
     *
     * @param branchDTO
     * @param serviceSupplierActivityList
     * @param activityVerifyItemDTOS
     */
    public ActivityVerifyResultDTO calculateSkAndSpActivity(BranchDTO branch, List<SupplierActivityDTO> activityList, List<ActivityVerifyItemDTO> verifyItemList) {
        ActivityVerifyResultDTO result = new ActivityVerifyResultDTO();
        result.setVerifyItemList(verifyItemList);
        /**
         * 仅计算普通促销商品, 忽略组合促销
         */
        activityList = activityList.stream().filter(SupplierActivityDTO::notSpuCombinePrm).collect(Collectors.toList());
        // 筛选出有效活,
        List<SupplierActivityDTO> validActivityList = activityList.stream().filter(item -> validateActivity(item, branch, verifyItemList)).collect(Collectors.toList());
        // 最佳绑定类型, 商品绑定关系
        Map<String, Map<ActivityVerifyItemDTO, SupplierActivityDTO>> optimumRelation = new HashMap<>();
        // 按照促销规则类型分组
        Map<String, List<SupplierActivityDTO>> activityRoleMap = validActivityList.stream().collect(Collectors.groupingBy(ActivityDTO::getPrmNo));
        // 初始化绑定关系表
        activityRoleMap.forEach((prmNo, value) -> optimumRelation.put(prmNo, new HashMap<>()));
        // 秒杀需要按照sku分组判断, 因为需要计算秒杀限购和库存
        verifyItemList.stream().filter(ActivityVerifyItemDTO::getSelected).collect(Collectors.groupingBy(ActivityVerifyItemDTO::getSkuId)).forEach((skuId, skuItemList) -> {
            // 秒杀,
            processSkPriorityBindActivity(branch, skuItemList, activityRoleMap, optimumRelation, result);
        });
        // 特价
        processSpPriorityBindActivity(branch, verifyItemList, activityRoleMap, optimumRelation, result);
        result.setOptimumRelation(optimumRelation);
        return result;
    }

    /**
     * 计算促销
     * @param branch            门店
     * @param activityList      入驻商活动列表
     * @param verifyItemList    入驻商验证商品, 传入商品必须都得是和验证活动的入驻商是一个
     * @param allActivityVerifyItemList  满减活动多入驻商
     * @return
     */
    public void calculateActivity(BranchDTO branch, List<SupplierActivityDTO> activityList, List<ActivityVerifyItemDTO> verifyItemList, List<ActivityVerifyItemDTO> allActivityVerifyItemList, Long supplierId, ActivityVerifyResultDTO result) {
        result.setVerifyItemList(verifyItemList);
        /**
         * 仅计算普通促销商品, 忽略组合促销
         */
        activityList = activityList.stream().filter(SupplierActivityDTO::notSpuCombinePrm).collect(Collectors.toList());
        // 筛选出有效活,
        List<SupplierActivityDTO> validActivityList = activityList.stream().filter(item -> validateActivity(item, branch, verifyItemList)).collect(Collectors.toList());
        // 最佳绑定类型, 商品绑定关系
        Map<String, Map<ActivityVerifyItemDTO, SupplierActivityDTO>> optimumRelation = result.getOptimumRelation();
        // 按照促销规则类型分组
        Map<String, List<SupplierActivityDTO>> activityRoleMap = validActivityList.stream().collect(Collectors.groupingBy(ActivityDTO::getPrmNo));
        // 先计算商品与活动的优先级绑定, 则筛选出商品在哪个活动的优先级最高
        for (ActivityVerifyItemDTO varifyItem : allActivityVerifyItemList) {
            // 满减支持多入驻商
            processPriorityBindActivityBySku(varifyItem, activityRoleMap, optimumRelation, PrmNoEnum.FD.getType());
        }
        // 先计算商品与活动的优先级绑定, 则筛选出商品在哪个活动的优先级最高
        for (ActivityVerifyItemDTO varifyItem : verifyItemList) {
            // 满赠
            processPriorityBindActivityBySku(varifyItem, activityRoleMap, optimumRelation, PrmNoEnum.FG.getType());
            // 买赠
            processPriorityBindActivityBySku(varifyItem, activityRoleMap, optimumRelation, PrmNoEnum.BG.getType());
        }
        // 秒杀和特价不需要匹配具体规则
        activityRoleMap.remove(PrmNoEnum.SP.getType());
        activityRoleMap.remove(PrmNoEnum.SK.getType());
        // 按照活动分组, 开始计算促销金额
        Map<SupplierActivityDTO, List<ActivityVerifyItemDTO>> activityGroup = optimumRelation.values().stream()
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.groupingBy(
                        Map.Entry::getValue,
                        Collectors.mapping(Map.Entry::getKey, Collectors.toList())
                ));
        result.setActivityProductRelation(activityGroup);

        // 匹配具体规则
        activityGroup.forEach((activity, itemList) -> {
            // 只计算选中的商品
            itemList = itemList.stream().filter(ActivityVerifyItemDTO::getSelected).collect(Collectors.toList());
            if (itemList.isEmpty()) {
                return;
            }
            // 优惠 + 特价
            // 满减以后再判断满赠是否达到要求
            // 判断满赠
            // 促销活动以后再判断优惠券
            // 满减
            processFdActivityApplyRole(activity, itemList, result, supplierId);
            // 满赠
            processFgActivityApplyRole(activity, itemList, result);
            // 买赠
            processBgActivityApplyRole(activity, itemList, result);
        });
    }

    /**
     * 用于计算SPU参与了那些活动
     * @param branch
     * @param activityList
     * @param verifyItemList
     * @return
     */
    public Map<SupplierActivityDTO, List<ActivityVerifyItemDTO>> calculateActivityBind(BranchDTO branch, List<SupplierActivityDTO> activityList, List<ActivityVerifyItemDTO> verifyItemList) {
        ActivityVerifyResultDTO result = new ActivityVerifyResultDTO();
        result.setVerifyItemList(verifyItemList);

        // 筛选出有效活, 城市, 渠道, 门店
        List<SupplierActivityDTO> validActivityList = activityList.stream().filter(item -> validateActivity(item, branch, verifyItemList)).collect(Collectors.toList());

        // 最佳绑定类型, 商品绑定关系
        Map<String, Map<ActivityVerifyItemDTO, SupplierActivityDTO>> optimumRelation = new HashMap<>();

        // 按照促销规则类型分组
        Map<String, List<SupplierActivityDTO>> activityRoleMap = validActivityList.stream().collect(Collectors.groupingBy(ActivityDTO::getPrmNo));

        // 初始化绑定关系表
        activityRoleMap.forEach((prmNo, value) -> optimumRelation.put(prmNo, new HashMap<>()));

        // 先计算商品与活动的优先级绑定, 则筛选出商品在哪个活动的优先级最高
        for (ActivityVerifyItemDTO varifyItem : verifyItemList) {
            // 满减
            processPriorityBindActivityBySpu(varifyItem, activityRoleMap, optimumRelation, PrmNoEnum.FD.getType());
            // 满赠
            processPriorityBindActivityBySpu(varifyItem, activityRoleMap, optimumRelation, PrmNoEnum.FG.getType());
            // 买赠
            processPriorityBindActivityBySpu(varifyItem, activityRoleMap, optimumRelation, PrmNoEnum.BG.getType());
            // 秒杀,
            processSkPriorityBindActivityBySpu(branch, varifyItem, activityRoleMap, optimumRelation, result);
            // 特价
            processSpPriorityBindActivityBySpu(branch, varifyItem, activityRoleMap, optimumRelation, result);
        }
        // 按照活动分组, 开始计算促销金额
        Map<SupplierActivityDTO, List<ActivityVerifyItemDTO>> activityGroup = optimumRelation.values().stream()
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.groupingBy(
                        Map.Entry::getValue,
                        Collectors.mapping(Map.Entry::getKey, Collectors.toList())
                ));
        return activityGroup;
    }

    /**
     * 计算门店可参与那些组合促销
     */
    public List<CbRuleDTO> calculateBranchCbAcitivtyList(BranchDTO branch, ProductType productType, List<SupplierActivityDTO> activityList) {
        /**
         * 仅计算普通促销商品, 忽略组合促销
         */
        activityList = activityList.stream().filter(item -> {
            if (productType == ProductType.LOCAL && item.getFuncScope() != 2) {
                return false;
            }
            if (productType == ProductType.GLOBAL && item.getFuncScope() != 1) {
                return false;
            }
            return item.spuCombinePrm();
        }).collect(Collectors.toList());
        // 筛选出有效活动
        List<SupplierActivityDTO> validActivityList = activityList.stream().filter(item -> validateActivity(item, branch)).collect(Collectors.toList());
        return validActivityList.stream().map(SupplierActivityDTO::getCbRules).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 处理秒杀绑定, 不参与金额计算
     * @param branch
     * @param varifyItem
     * @param activityRoleMap
     * @param optimumRelation
     */
    private void processSkPriorityBindActivityBySpu(BranchDTO branch, ActivityVerifyItemDTO varifyItem, Map<String, List<SupplierActivityDTO>> activityRoleMap, Map<String, Map<ActivityVerifyItemDTO, SupplierActivityDTO>> optimumRelation, ActivityVerifyResultDTO result) {
        String prmNo = PrmNoEnum.SK.getType();
        if (!activityRoleMap.containsKey(prmNo)) {
            return;
        }
        // 商品没有其他的特价活动
        if (Objects.nonNull(varifyItem.getActivityNum()) && varifyItem.getActivityNum() > NumberPool.INT_ZERO) {
            return;
        }
        List<SupplierActivityDTO> activityDTOS = activityRoleMap.get(prmNo);
        for (SupplierActivityDTO activityDTO : activityDTOS) {
            // 验证活动是否能匹配上商品
            Map<Long, List<SkRuleDTO>> skMap = activityDTO.getSkRules().stream().collect(Collectors.groupingBy(SkRuleDTO::getSpuId));
            if (skMap.containsKey(varifyItem.getSpuId())) {
                // 验证特价限量
                List<SkRuleDTO> spuSkList = skMap.get(varifyItem.getSpuId());
                ActivityLabelInfoVO<SkRuleDTO> activityLabel = new ActivityLabelInfoVO();
                activityLabel.setActivity(activityDTO)
                        .setTargetValue(BigDecimal.ZERO)
                        .setFinishValue(BigDecimal.ZERO)
                ;
                for (SkRuleDTO skRule : spuSkList) {
                    // 验证促销活动库存
                    if (validateRuleStock(branch.getBranchId(), skRule, varifyItem.getStockConvertRate(), varifyItem.getUnitSize())) {
                        activityLabel.getAdequateRole().add(skRule);
                    }
                }
                // 有匹配到规则, 才绑定
                if (!activityLabel.getAdequateRole().isEmpty()) {
                    varifyItem.getAdequateList().add(activityLabel);
                    optimumRelation.get(prmNo).put(varifyItem, activityDTO);
                }
            }
        }
    }

    /**
     * 处理特价绑定, 不参与金额计算
     * @param branch
     * @param varifyItem
     * @param activityRoleMap
     * @param optimumRelation
     */
    private void processSpPriorityBindActivityBySpu(BranchDTO branch, ActivityVerifyItemDTO varifyItem, Map<String, List<SupplierActivityDTO>> activityRoleMap, Map<String, Map<ActivityVerifyItemDTO, SupplierActivityDTO>> optimumRelation, ActivityVerifyResultDTO result) {
        String prmNo = PrmNoEnum.SP.getType();
        if (!activityRoleMap.containsKey(prmNo)) {
            return;
        }
        // 商品没有其他的特价活动
        if (Objects.nonNull(varifyItem.getActivityNum()) && varifyItem.getActivityNum() > NumberPool.INT_ZERO) {
            return;
        }
        List<SupplierActivityDTO> activityDTOS = activityRoleMap.get(prmNo);
        for (SupplierActivityDTO activityDTO : activityDTOS) {
            // 验证活动是否能匹配上商品
            Map<Long, List<SpRuleDTO>> spMap = activityDTO.getSpRules().stream().collect(Collectors.groupingBy(SpRuleDTO::getSpuId));
            if (spMap.containsKey(varifyItem.getSpuId())) {
                // 验证特价限量
                List<SpRuleDTO> spRuleList = spMap.get(varifyItem.getSpuId());
                ActivityLabelInfoVO<SpRuleDTO> activityLabel = new ActivityLabelInfoVO();
                activityLabel.setActivity(activityDTO)
                        .setTargetValue(BigDecimal.ZERO)
                        .setFinishValue(BigDecimal.ZERO)
                ;
                for (SpRuleDTO spRule : spRuleList) {
                    // 验证促销活动库存
                    if (validateRuleStock(branch.getBranchId(), spRule, varifyItem.getStockConvertRate(), varifyItem.getUnitSize())) {
                        activityLabel.getAdequateRole().add(spRule);
                    }
                }
                // 有匹配到规则, 才绑定
                if (!activityLabel.getAdequateRole().isEmpty()) {
                    varifyItem.getAdequateList().add(activityLabel);
                    optimumRelation.get(prmNo).put(varifyItem, activityDTO);
                }
            }
        }
    }

    /**
     * 处理活动 特价 秒杀, 等价格优惠
     * @param activity
     * @param verifyItem
     * @param result
     */
    private void processSpAndSkActivityApplyRole(SupplierActivityDTO activity, ActivityVerifyItemDTO verifyItem, ActivityVerifyResultDTO result) {
        // 只对选中的数据进行总优惠计算
        if (Objects.nonNull(verifyItem.getActivityNum()) && Objects.nonNull(verifyItem.getSelected()) && verifyItem.getSelected()) {
            // 计算特价或者秒杀价格
            BigDecimal couponAmt = NumberUtil.toBigDecimal(verifyItem.getActivityNum()).multiply(verifyItem.getItemPrice().subtract(verifyItem.getActivityPrice()));
            // 计算优惠金额
            result.setTotalCouponAmt(result.getTotalCouponAmt().add(couponAmt));
        }
    }


    /**
     * 匹配买赠赠规则
     * @param activity
     * @param itemList
     * @param result
     */
    private void processBgActivityApplyRole(SupplierActivityDTO activity, List<ActivityVerifyItemDTO> itemList, ActivityVerifyResultDTO result) {
        if (!PrmNoEnum.isBg(activity.getPrmNo())) {
            return;
        }
        // 如果活动对应的商品只有一个, 哪个我们就把这个活动绑定到商品上去去, 如果有多个就绑定到最外层去
        // 满赠活动不但要计算已满足条件, 还要计算一个满足后的下一个阶梯条件
        // 满赠, 买赠活动规则都叠加满足, 买赠按数量计算
        // 总数量
        // 2025年3月17日10:59:36, 调整为买赠按照最小单位计算
        BigDecimal totalNum = itemList.stream().map(ActivityVerifyItemDTO::getUnitNum).reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer smallTotalQty = itemList.stream().map(item -> item.getNumByUnit(UnitTypeEnum.UNIT_SMALL.getType())).reduce(BigDecimal.ZERO, BigDecimal::add).intValue();
        Integer middleTotalQty = itemList.stream().map(item -> item.getNumByUnit(UnitTypeEnum.UNIT_MIDDLE.getType())).reduce(BigDecimal.ZERO, BigDecimal::add).intValue();
        Integer largeTotalQty = itemList.stream().map(item -> item.getNumByUnit(UnitTypeEnum.UNIT_LARGE.getType())).reduce(BigDecimal.ZERO, BigDecimal::add).intValue();

        // 创建活动绑定
        ActivityLabelInfoVO<BgRuleDTO> activityLabel = new ActivityLabelInfoVO();
        activityLabel.setActivity(activity);
        // 设置活动完成度
        activityLabel.setFinishValue(totalNum);
        // 先按单位升序排序，单位相同则按触发数量升序排序
        List<BgRuleDTO> rules = activity.getBgRules().stream().sorted(new Comparator<BgRuleDTO>() {
            @Override
            public int compare(BgRuleDTO rule1, BgRuleDTO rule2) {
                return rule1.getRuleUnitType().equals(rule2.getRuleUnitType()) ?
                        rule1.getRuleQty().compareTo(rule2.getRuleQty()) :
                        rule1.getRuleUnitType().compareTo(rule2.getRuleUnitType());
            }
        }).collect(Collectors.toList());

        // 买赠:(1-满赠 0-每赠)
        boolean forCondition = NumberPool.INT_ZERO == activity.getLadderFlag();

        // 遍历处理规则
        for (BgRuleDTO rule : rules) {
            Integer condition = rule.getRuleQty();
            Integer totalQty = UnitTypeEnum.UNIT_LARGE.getType().equals(rule.getRuleUnitType()) ? largeTotalQty : UnitTypeEnum.UNIT_MIDDLE.getType().equals(rule.getRuleUnitType()) ? middleTotalQty : smallTotalQty;
            // 匹配更优条件
            if (condition > totalQty) {
                // 需要赠送数量
                // 如果是每赠等于 (商品数量 / 条件) * 每次赠送
                int giftNum = forCondition ? totalQty / condition * rule.getOnceGiftQty() : rule.getOnceGiftQty();
                // 有下一个条件, 且下一个条件还满足赠品数量赠送
                if (Objects.nonNull(rule) && validateRuleStock(rule, giftNum)) {
                    activityLabel.setTargetValue(NumberUtil.toBigDecimal(rule.getRuleQty()))
                            .setRuleUnitType(rule.getRuleUnitType())
                            .setBetterRole(rule);
                    break;
                }
            }
        }
        List<BgRuleDTO> tmpList = new ArrayList<>();
        // 处理已满足规则
        for (BgRuleDTO rule : activity.getBgRules()) {
            // 满足条件
            Integer condition = rule.getRuleQty();
            Integer totalQty = UnitTypeEnum.UNIT_LARGE.getType().equals(rule.getRuleUnitType()) ? largeTotalQty : UnitTypeEnum.UNIT_MIDDLE.getType().equals(rule.getRuleUnitType()) ? middleTotalQty : smallTotalQty;
            boolean reach = totalQty >= condition;
            int giftNum = forCondition ? totalQty / condition * rule.getOnceGiftQty() : rule.getOnceGiftQty();
            // 匹配到具体规则
            if (reach && validateRuleStock(rule, giftNum)) {
                // 满赠调整并且有库存
                // 设置计算出来的最新的赠送数量, 可能因为每赠double
                rule.setOnceGiftQty(giftNum);
                activityLabel.getAdequateRole().add(rule);
            } else if (reach) {
                // 满赠条件但是没有库存了
                tmpList.add(rule);
            }
        }
        // 未命中活动, 设置第一个阶梯活动为预期活动
        if (Objects.isNull(activityLabel.getBetterRole())
                && tmpList.isEmpty()
                && activityLabel.getAdequateRole().isEmpty()
        ) {
            for (BgRuleDTO ruleDTO : rules) {
                Integer condition = ruleDTO.getRuleQty();
                Integer totalQty = UnitTypeEnum.UNIT_LARGE.getType().equals(ruleDTO.getRuleUnitType()) ? largeTotalQty : UnitTypeEnum.UNIT_MIDDLE.getType().equals(ruleDTO.getRuleUnitType()) ? middleTotalQty : smallTotalQty;
                int giftNum = forCondition ? totalQty / condition * ruleDTO.getOnceGiftQty() : ruleDTO.getOnceGiftQty();
                // 是否还有赠品
                if (validateRuleStock(ruleDTO, giftNum)) {
                    activityLabel.setBetterRole(ruleDTO);
                    activityLabel.setTargetValue(NumberUtil.toBigDecimal(ruleDTO.getRuleQty()));
                    activityLabel.setRuleUnitType(ruleDTO.getRuleUnitType());
                    break;
                }
            }
        }
        // 没有满足条件, 也没有预期条件
        if (ObjectUtil.isEmpty(activityLabel.getAdequateRole()) && ObjectUtil.isEmpty(activityLabel.getBetterRole())) {
            return;
        }
        // 如果只有一个商品则绑定到商品上
        if (itemList.size() == NumberPool.INT_ONE && activity.getSpuScope() == ActivitySpuScopeEnum.SKU.getScope()) {
            itemList.forEach(item -> item.getAdequateList().add(activityLabel));
        } else {
            result.getActivityList().add(activityLabel);
        }
        // 赠品活动不用计算金额
    }

    /**
     * 匹配满赠规则
     * @param activity
     * @param itemList
     * @param result
     */
    private void processFgActivityApplyRole(SupplierActivityDTO activity, List<ActivityVerifyItemDTO> itemList, ActivityVerifyResultDTO result) {
        if (!PrmNoEnum.isFg(activity.getPrmNo())) {
            return;
        }
        // 如果活动对应的商品只有一个, 哪个我们就把这个活动绑定到商品上去去, 如果有多个就绑定到最外层去
        // 满赠活动不但要计算已满足条件, 还要计算一个满足后的下一个阶梯条件
        // 满赠, 买赠活动规则都叠加满足
        // 总金额
        BigDecimal totalAmt = itemList.stream().map(ActivityVerifyItemDTO::getPayAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        //总购买品项数
        Integer itemSkuNum = itemList.stream().map(ActivityVerifyItemDTO::getSkuId).collect(Collectors.toSet()).size();

        // 创建活动绑定
        ActivityLabelInfoVO<FgRuleDTO> activityLabel = new ActivityLabelInfoVO();
        activityLabel.setActivity(activity);
        // 设置活动完成度
        activityLabel.setFinishValue(totalAmt);
        // 按照满足金额分组
        Map<BigDecimal, List<FgRuleDTO>> conditionMap = activity.getFgRules().stream().collect(Collectors.groupingBy(FgRuleDTO::getFullAmt));
        // 金额从大到小
        ArrayList<BigDecimal> amtList = ListUtil.toList(conditionMap.keySet());
        // 买赠:(1-满赠 0-每赠)
        boolean forCondition = Objects.nonNull(activity.getLadderFlag()) && NumberPool.INT_ZERO == activity.getLadderFlag();
        // 遍历处理规则
        amtList.sort(Comparator.comparing(BigDecimal::doubleValue));
        //设置 下一个阶梯促销规则
        firstFor:
        for (int i = 0; i < amtList.size(); i++) {
            List<FgRuleDTO> fgRuleDTOS = conditionMap.get(amtList.get(i));
            // 当前规则
            FgRuleDTO rule = fgRuleDTOS.get(0);
            // 匹配到具体规则  不满足金额
            if (NumberUtil.isGreater(rule.getFullAmt(), totalAmt)) {
                // 更优规则
                for (FgRuleDTO nextRule : fgRuleDTOS) {
                    // 如果是每赠
                    int giftNum = forCondition ? (totalAmt.divide(rule.getFullAmt(), 0, RoundingMode.DOWN).intValue()) * rule.getOnceGiftQty() : rule.getOnceGiftQty();
                    // 有更, 存在库存
                    if (Objects.nonNull(nextRule) && validateRuleStock(nextRule, giftNum)) {
                        activityLabel.setTargetValue(nextRule.getFullAmt())
                                .setBetterRole(nextRule);
                        break firstFor;
                    }
                }
            }
            // 匹配到具体规则  不达标购买品项数
            if (itemSkuNum < rule.getBuySkuNum()) {
                // 更优规则
                for (FgRuleDTO nextRule : fgRuleDTOS) {
                    // 如果是每赠
                    int giftNum = forCondition ? (totalAmt.divide(rule.getFullAmt(), 0, RoundingMode.DOWN).intValue()) * rule.getOnceGiftQty() : rule.getOnceGiftQty();
                    // 有更, 存在库存
                    if (Objects.nonNull(nextRule) && validateRuleStock(nextRule, giftNum)) {
                        //设置下一个阶梯促销目标类型 为1 满足购买品项数 并且目标值和已完成值都设置为购买品项数
                        activityLabel.setTargetValue(BigDecimal.valueOf(nextRule.getBuySkuNum()))
                                .setBetterRole(nextRule)
                                .setBetterTargetType(NumberPool.INT_ONE)
                                .setFinishValue(BigDecimal.valueOf(itemSkuNum));
                        break firstFor;
                    }
                }
            }
        }
        List<FgRuleDTO> tmpList = new ArrayList<>();
        // 处理已满足规则
        for (FgRuleDTO rule : activity.getFgRules()) {
            // 满足条件
            BigDecimal condition = rule.getFullAmt();
            // 匹配规则 满足满足金额和达标购买品项数
            boolean reach = NumberUtil.isGreaterOrEqual(totalAmt, condition) && itemSkuNum >= rule.getBuySkuNum();
            // 如果是每赠
            int giftNum = forCondition ? (totalAmt.divide(rule.getFullAmt(), 0, RoundingMode.DOWN).intValue()) * rule.getOnceGiftQty() : rule.getOnceGiftQty();
            // 匹配到具体规则
            if (reach && validateRuleStock(rule, giftNum)) {
                // 满赠调整并且有库存
                rule.setOnceGiftQty(giftNum);
                activityLabel.getAdequateRole().add(rule);
            } else if (reach) {
                // 满赠条件但是没有库存了
                tmpList.add(rule);
            }
        }
        // 未命中活动, 设置第一个阶梯活动为预期活动
        activity.getFgRules().sort(Comparator.comparing(FgRuleDTO::getFullAmt));
        if (Objects.isNull(activityLabel.getBetterRole())
                && tmpList.isEmpty()
                && activityLabel.getAdequateRole().isEmpty()
        ) {
            for (FgRuleDTO rule : activity.getFgRules()) {
                // 验证规则是否满足一次的赠送
                // 如果是每赠
                int giftNum = forCondition ? (totalAmt.divide(rule.getFullAmt(), 0, RoundingMode.DOWN).intValue()) * rule.getOnceGiftQty() : rule.getOnceGiftQty();
/*                //验证规则是否满足 购买品项数
                if(itemSkuNum < rule.getBuySkuNum()){
                    continue;
                }*/
                // 验证规则是否满足一次的赠送
                if (validateRuleStock(rule, giftNum)) {
                    activityLabel.setBetterRole(rule);
                    activityLabel.setTargetValue(rule.getFullAmt());
                    break;
                }
            }
        }
        // 没有满足条件, 也没有预期条件
        if (ObjectUtil.isEmpty(activityLabel.getAdequateRole()) && ObjectUtil.isEmpty(activityLabel.getBetterRole())) {
            return;
        }
        // 如果只有一个商品则绑定到商品上
        if (itemList.size() == NumberPool.INT_ONE && activity.getSpuScope() == ActivitySpuScopeEnum.SKU.getScope()) {
            itemList.forEach(item -> item.getAdequateList().add(activityLabel));
        } else {
            result.getActivityList().add(activityLabel);
        }
    }

    /**
     * 验证满赠赠品数量是否足够
     * @param rule          满赠规则
     * @param giftNum       本次赠送
     * @return  true-可以赠送, false-库存不足
     */
    public boolean validateRuleStock(FgRuleDTO rule, Integer giftNum) {
        // 当前已赠数量
        BigDecimal saleNum = getFgSaleNum(rule.getActivityId(), rule.getFgRuleId());
        // 本次赠送数量
        BigDecimal onceGiftQty = StockUtil.bigDecimal(giftNum);
        BigDecimal totalGiftQty = this.getTotalGiftQty(rule);
        if (Objects.isNull(rule.getTotalGiftQty())) {
            // 没有设置上限, 即判断这次增加的数量, 是否大于库存数即可
            if (NumberUtil.isGreater(onceGiftQty, totalGiftQty)) {
                return false;
            }
        } else if (NumberUtil.isGreater(onceGiftQty, totalGiftQty.subtract(saleNum))) {
            // 总数 - 已赠 < 本地需要赠送
            return false;
        }
        return true;
    }

    /**
     * 验证买赠赠品数量是否足够
     * @param rule          买赠规则
     * @param giftNum       本次赠送
     * @return  true-可以赠送, false-库存不足
     */
    public boolean validateRuleStock(BgRuleDTO rule, Integer giftNum) {
        // 当前已赠数量
        BigDecimal saleNum = getBgSaleNum(rule.getActivityId(), rule.getBgRuleId());
        // 本次赠送数量
        BigDecimal onceGiftQty = StockUtil.bigDecimal(giftNum);
        // 总数 - 已赠 < 本地需要赠送
        BigDecimal totalGiftQty = this.getTotalGiftQty(rule);
        if (Objects.isNull(rule.getTotalGiftQty())) {
            // 没有设置上限, 即判断这次增加的数量, 是否大于库存数即可
            if (NumberUtil.isGreater(onceGiftQty, totalGiftQty)) {
                return false;
            }
        } else if (NumberUtil.isGreater(onceGiftQty, totalGiftQty.subtract(saleNum))) {
            // 总数 - 已赠 < 本地需要赠送
            return false;
        }
        return true;
    }

    /**
     * 验证秒杀活动库存
     * @param branchId          门店ID
     * @param skRule            活动规则
     * @param stockConvert      当前验证商品一个销售数量 = 多少个最小库存
     * @return  ture-有效, false-无效
     */
    public boolean validateRuleStock(Long branchId, SkRuleDTO skRule, BigDecimal stockConvert, Integer unitSize) {
        BigDecimal onceLimit = Objects.nonNull(skRule.getOnceLimit()) ? StockUtil.bigDecimal(skRule.getOnceLimit()) : StockUtil.bigDecimal(Integer.MAX_VALUE);
        // 获取当前门店已经购买了多少个
        BigDecimal saleNum = getSkSaleNum(branchId, skRule.getActivityId(), skRule.getSkRuleId());
        // 中单位门店限购数量
        BigDecimal midLimit = Objects.nonNull(skRule.getMidLimit()) ? StockUtil.bigDecimal(skRule.getMidLimit()) : StockUtil.bigDecimal(Integer.MAX_VALUE);
        // 获取当前门店已经购买了多少个中单位
        BigDecimal midSaleNum = getSkMidSaleNum(branchId, skRule.getActivityId(), skRule.getSkRuleId());
        // 大单位门店限购数量
        BigDecimal largeLimit = Objects.nonNull(skRule.getLargeLimit()) ? StockUtil.bigDecimal(skRule.getLargeLimit()) : StockUtil.bigDecimal(Integer.MAX_VALUE);
        // 获取当前门店已经购买了多少个大单位
        BigDecimal largeSaleNum = getSkLargeSaleNum(branchId, skRule.getActivityId(), skRule.getSkRuleId());

        // 小单位：活动促销总库存
        BigDecimal activitySaleNum = getSkSaleNum(skRule.getActivityId(), skRule.getSkRuleId());
        // 小单位：剩余活动库存 = 设置的小单位库存 - 已售小单位数量
        BigDecimal balanceStock = Objects.nonNull(skRule.getSeckillStock()) ?
                (StockUtil.bigDecimal(skRule.getSeckillStock()).subtract(activitySaleNum)) :
                stockService.getSurplusSaleQtyBigDecimal(skRule.getSkuId()).divide(stockConvert, 0, RoundingMode.DOWN);

        // 中单位：活动促销总库存
        BigDecimal activityMidSaleNum = getSkMidSaleNum(skRule.getActivityId(), skRule.getSkRuleId());
        // 中单位：剩余活动库存 = 设置的中单位库存 - 已售中单位数量
        BigDecimal midBalanceStock = Objects.nonNull(skRule.getSeckillMidStock()) ?
                (StockUtil.bigDecimal(skRule.getSeckillMidStock()).subtract(activityMidSaleNum)) :
                stockService.getSurplusSaleQtyBigDecimal(skRule.getSkuId()).divide(stockConvert, 0, RoundingMode.DOWN);

        // 大单位：活动促销总库存
        BigDecimal activityLargeSaleNum = getSkLargeSaleNum(skRule.getActivityId(), skRule.getSkRuleId());
        // 大单位：剩余活动库存 = 设置的大单位库存 - 已售大单位数量
        BigDecimal largeBalanceStock = Objects.nonNull(skRule.getSeckillLargeStock()) ?
                (StockUtil.bigDecimal(skRule.getSeckillLargeStock()).subtract(activityLargeSaleNum)) :
                stockService.getSurplusSaleQtyBigDecimal(skRule.getSkuId()).divide(stockConvert, 0, RoundingMode.DOWN);

        if (UnitTypeEnum.UNIT_SMALL.getType().equals(unitSize)) {
            // 小单位已售大于等于购买上限
            if (NumberUtil.isGreaterOrEqual(saleNum, onceLimit)) {
                return false;
            }
            // 剩余库存是否可以购买一份商品数据
            if (NumberUtil.isGreater(BigDecimal.ONE, balanceStock)) {
                return false;
            }
        } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(unitSize)) {
            // 中单位已售大于等于购买上限
            if (NumberUtil.isGreaterOrEqual(midSaleNum, midLimit)) {
                return false;
            }
            // 剩余库存是否可以购买一份商品数据
            if (NumberUtil.isGreater(BigDecimal.ONE, midBalanceStock)) {
                return false;
            }
        } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(unitSize)) {
            // 大单位已售大于等于购买上限
            if (NumberUtil.isGreaterOrEqual(largeSaleNum, largeLimit)) {
                return false;
            }
            // 剩余库存是否可以购买一份商品数据
            if (NumberUtil.isGreater(BigDecimal.ONE, largeBalanceStock)) {
                return false;
            }
        } else {
            if (NumberUtil.isGreaterOrEqual(saleNum, onceLimit)
                    && NumberUtil.isGreaterOrEqual(midSaleNum, midLimit)
                    && NumberUtil.isGreaterOrEqual(largeSaleNum, largeLimit)) {
                return false;
            }
            if (NumberUtil.isGreater(BigDecimal.ONE, balanceStock)
                && NumberUtil.isGreater(BigDecimal.ONE, midBalanceStock)
                && NumberUtil.isGreater(BigDecimal.ONE, largeBalanceStock)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证特价活动库存
     * @param branchId          门店ID
     * @param spRule            活动规则
     * @param stockConvert      当前验证商品一个销售数量 = 多少个最小库存
     * @return  ture-有效, false-无效
     * @return
     */
    public boolean validateRuleStock(Long branchId, SpRuleDTO spRule, BigDecimal stockConvert, Integer unitSize) {
        // 小单位：活动促销总库存
        BigDecimal activitySaleNum = getSpSaleNum(spRule.getActivityId(), spRule.getSpRuleId());
        // 小单位：剩余活动库存 = 设置的小单位库存 - 已售小单位数量
        // 注意：stockService.getSurplusSaleQtyBigDecimal()返回的是小单位库存，不需要转换
        BigDecimal balanceStock = Objects.nonNull(spRule.getTotalLimitQty()) ?
                (StockUtil.bigDecimal(spRule.getTotalLimitQty()).subtract(activitySaleNum)) :
                stockService.getSurplusSaleQtyBigDecimal(spRule.getSkuId());

        // 中单位：活动促销总库存
        BigDecimal activityMidSaleNum = getSpMidSaleNum(spRule.getActivityId(), spRule.getSpRuleId());
        // 中单位：剩余活动库存 = 设置的中单位库存 - 已售中单位数量
        // 如果没有设置中单位库存，使用小单位库存转换（小单位库存 ÷ 中单位转换率）
        BigDecimal midBalanceStock = Objects.nonNull(spRule.getMidTotalLimitQty()) ?
                (StockUtil.bigDecimal(spRule.getMidTotalLimitQty()).subtract(activityMidSaleNum)) :
                stockService.getSurplusSaleQtyBigDecimal(spRule.getSkuId()).divide(stockConvert, 0, RoundingMode.DOWN);

        // 大单位：活动促销总库存
        BigDecimal activityLargeSaleNum = getSpLargeSaleNum(spRule.getActivityId(), spRule.getSpRuleId());
        // 大单位：剩余活动库存 = 设置的大单位库存 - 已售大单位数量
        // 如果没有设置大单位库存，使用小单位库存转换（小单位库存 ÷ 大单位转换率）
        BigDecimal largeBalanceStock = Objects.nonNull(spRule.getLargeTotalLimitQty()) ?
                (StockUtil.bigDecimal(spRule.getLargeTotalLimitQty()).subtract(activityLargeSaleNum)) :
                stockService.getSurplusSaleQtyBigDecimal(spRule.getSkuId()).divide(stockConvert, 0, RoundingMode.DOWN);

        // 根据单位类型选择对应的库存进行验证
        BigDecimal currentBalanceStock;
        if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(unitSize)) {
            currentBalanceStock = midBalanceStock;
        } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(unitSize)) {
            currentBalanceStock = largeBalanceStock;
        } else {
            currentBalanceStock = balanceStock;
        }

        log.info("特价活动库存校验 ActivityId[{}],branchId[{}],SpRuleId[{}],unitSize[{}],activitySaleNum[{}],activityMidSaleNum[{}],activityLargeSaleNum[{}],balanceStock[{}],midBalanceStock[{}],largeBalanceStock[{}],currentBalanceStock[{}],stockConvert[{}]",
                spRule.getActivityId(), branchId, spRule.getSpRuleId(), unitSize, activitySaleNum, activityMidSaleNum, activityLargeSaleNum,
                balanceStock, midBalanceStock, largeBalanceStock, currentBalanceStock, stockConvert);

        if (NumberUtil.isGreater(BigDecimal.ONE, currentBalanceStock)) {
            return false;
        }
        // 限购验证 - 根据单位类型使用相应的限购数量和销量
        if (UnitTypeEnum.UNIT_SMALL.getType().equals(unitSize)) {
            // 小单位限购验证
            BigDecimal onceLimit = Objects.nonNull(spRule.getOnceBuyLimit()) ?
                    StockUtil.bigDecimal(spRule.getOnceBuyLimit()) : StockUtil.bigDecimal(Integer.MAX_VALUE);
            BigDecimal saleNum = getSpSaleNum(branchId, spRule.getActivityId(), spRule.getSpRuleId());

            if (NumberUtil.isGreaterOrEqual(saleNum, onceLimit)) {
                log.info("小单位限购验证失败, saleNum: {}, onceLimit: {}", saleNum, onceLimit);
                return false;
            }
        } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(unitSize)) {
            // 中单位限购验证
            BigDecimal midLimit = Objects.nonNull(spRule.getMidLimit()) ?
                    StockUtil.bigDecimal(spRule.getMidLimit()) : StockUtil.bigDecimal(Integer.MAX_VALUE);
            BigDecimal midSaleNum = getSpMidSaleNum(branchId, spRule.getActivityId(), spRule.getSpRuleId());

            if (NumberUtil.isGreaterOrEqual(midSaleNum, midLimit)) {
                log.info("中单位限购验证失败, midSaleNum: {}, midLimit: {}", midSaleNum, midLimit);
                return false;
            }
        } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(unitSize)) {
            // 大单位限购验证
            BigDecimal largeLimit = Objects.nonNull(spRule.getLargeLimit()) ?
                    StockUtil.bigDecimal(spRule.getLargeLimit()) : StockUtil.bigDecimal(Integer.MAX_VALUE);
            BigDecimal largeSaleNum = getSpLargeSaleNum(branchId, spRule.getActivityId(), spRule.getSpRuleId());

            if (NumberUtil.isGreaterOrEqual(largeSaleNum, largeLimit)) {
                log.info("大单位限购验证失败, largeSaleNum: {}, largeLimit: {}", largeSaleNum, largeLimit);
                return false;
            }
        }
        return true;
    }

    /**
     * 匹配满减具体规则
     * @param activity
     * @param itemList
     */
    private void processFdActivityApplyRole(SupplierActivityDTO activity, List<ActivityVerifyItemDTO> itemList, ActivityVerifyResultDTO result, Long supplierId) {
        if (!PrmNoEnum.isFd(activity.getPrmNo())) {
            return;
        }
        // 当前入驻商订单的订单商品明细
        List<ActivityVerifyItemDTO> supplierItemList = itemList.stream().filter(t -> t.getSupplierId().equals(supplierId)).collect(Collectors.toList());
        // 为空则跳过
        if (CollectionUtils.isEmpty(supplierItemList)) {
            return;
        }
        // 如果活动对应的商品只有一个, 哪个我们就把这个活动绑定到商品上去去, 如果有多个就绑定到最外层去
        // 满减活动不但要计算已满足条件, 还要计算一个满足后的下一个阶梯条件
        // 总金额
        BigDecimal totalAmt;
        boolean existTotalAmtFlag = itemList.stream().allMatch(t -> Objects.nonNull(t.getCalcTotalAmt()));
        if (existTotalAmtFlag) {
            totalAmt = itemList.get(0).getCalcTotalAmt();
        } else {
            totalAmt = itemList.stream().filter(t -> CollectionUtils.isEmpty(t.getFdActivityIdList())).map(ActivityVerifyItemDTO::getPayAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        // 总数量
        BigDecimal totalNum;
        boolean existTotalNumFlag = itemList.stream().allMatch(t -> Objects.nonNull(t.getCalcTotalNum()));
        if (existTotalNumFlag) {
            totalNum = itemList.get(0).getCalcTotalNum();
        } else {
            totalNum = NumberUtil.toBigDecimal(itemList.stream().filter(t -> CollectionUtils.isEmpty(t.getFdActivityIdList())).mapToInt(ActivityVerifyItemDTO::getNum).sum());
        }
        // 创建活动绑定
        ActivityLabelInfoVO<FdRuleDTO> activityLabel = new ActivityLabelInfoVO();
        activityLabel.setActivity(activity);
        // 判断满减类型
        boolean isAmount = activity.getAmtOrQty() == NumberPool.INT_ZERO;
        // 阶梯排序, 降序排序, 从高往低进行判断
        activity.getFdRules().sort(isAmount ? Comparator.comparing(FdRuleDTO::getFullAmt).reversed() : Comparator.comparing(FdRuleDTO::getFullQty).reversed());
        // 设置活动完成度
        activityLabel.setFinishValue(isAmount ? totalAmt : totalNum);
        // 遍历处理规则, 获取更好规则
        for (int i = 0; i < activity.getFdRules().size(); i++) {
            // 当前规则
            FdRuleDTO rule = activity.getFdRules().get(i);
            // 上一个规则
            FdRuleDTO nextRule = (i - 1) >= 0 ? activity.getFdRules().get(i - 1) : null;
            // 匹配到具体规则
            if ((isAmount ? NumberUtil.isGreaterOrEqual(totalAmt, rule.getFullAmt()) : NumberUtil.isGreaterOrEqual(totalNum, new BigDecimal(rule.getFullQty())))) {
                if (Objects.nonNull(nextRule)) {
                    activityLabel.setTargetValue(isAmount ? nextRule.getFullAmt() : NumberUtil.toBigDecimal(nextRule.getFullQty()))
                            .setBetterRole(nextRule);
                }
                break;
            }
        }
        // 便利处理已满足规则
        for (FdRuleDTO rule : activity.getFdRules()) {
            // 匹配到具体规则
            if ((isAmount ? NumberUtil.isGreaterOrEqual(totalAmt, rule.getFullAmt()) : NumberUtil.isGreaterOrEqual(totalNum, new BigDecimal(rule.getFullQty())))) {
                // 满减匹配一个就行了
                activityLabel.getAdequateRole().add(rule);
                if (isAmount && !existTotalAmtFlag) {
                    itemList.forEach(t -> {
                        if (CollectionUtils.isEmpty(t.getFdActivityIdList())) {
                            t.setCalcTotalAmt(totalAmt);
                            List<Long> fdActivityIdList = new ArrayList<>();
                            fdActivityIdList.add(activity.getActivityId());
                            t.setFdActivityIdList(fdActivityIdList);
                        }
                    });
                } else if (!isAmount && !existTotalNumFlag) {
                    itemList.forEach(t -> {
                        if (CollectionUtils.isEmpty(t.getFdActivityIdList())) {
                            t.setCalcTotalNum(totalNum);
                            List<Long> fdActivityIdList = new ArrayList<>();
                            fdActivityIdList.add(activity.getActivityId());
                            t.setFdActivityIdList(fdActivityIdList);
                        }
                    });
                }
                break;
            }
        }
        // 未命中活动, 设置第一个阶梯活动为预期活动
        if (Objects.isNull(activityLabel.getBetterRole()) && activityLabel.getAdequateRole().isEmpty()) {
            FdRuleDTO ruleDTO = activity.getFdRules().get(activity.getFdRules().size() - NumberPool.INT_ONE);
            activityLabel.setBetterRole(ruleDTO);
            activityLabel.setTargetValue(isAmount ? ruleDTO.getFullAmt() : NumberUtil.toBigDecimal(ruleDTO.getFullQty()));
        }
        // 如果只有一个商品则绑定到商品上
        if (itemList.size() == NumberPool.INT_ONE && activity.getSpuScope() == ActivitySpuScopeEnum.SKU.getScope()) {
            itemList.forEach(item -> item.getAdequateList().add(activityLabel));
        } else {
            result.getActivityList().add(activityLabel);
        }
        // 计算优惠金额
        for (FdRuleDTO rule : activityLabel.getAdequateRole()) {
            // 满减需要分摊计算到商品上
            BigDecimal itemCouponAmt;
            if (activity.getLadderFlag() == NumberPool.INT_ONE) {
                // 满减
                itemCouponAmt = rule.getDiscountAmt();
            } else {
                if (isAmount) {
                    // 金额 每减
                    // (金额 / 条件金额) * 优惠券金额
                    itemCouponAmt = totalAmt.divide(rule.getFullAmt(), 0, RoundingMode.DOWN)
                            .setScale(0, RoundingMode.DOWN)
                            .multiply(rule.getDiscountAmt())
                            .setScale(2, RoundingMode.HALF_UP);
                } else {
                    // 数量 每减
                    // (总数量 / 条件金额) * 优惠券金额
                    itemCouponAmt = totalNum.divide(NumberUtil.toBigDecimal(rule.getFullQty()), 0, RoundingMode.DOWN)
                            .setScale(0, RoundingMode.DOWN)
                            .multiply(rule.getDiscountAmt()).setScale(2, RoundingMode.HALF_UP);
                }
            }
            // 计算其它已经分摊过得优惠金额
            BigDecimal dealDiscountAmt = itemList.stream().filter(t -> CollectionUtils.isNotEmpty(t.getFdActivityIdList()) &&
                    t.getFdActivityIdList().contains(activity.getActivityId())).map(ActivityVerifyItemDTO::getDiscountAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 金额降序
            supplierItemList.sort(Comparator.comparing(ActivityVerifyItemDTO::getPayAmt).reversed());
            // 用于计算优惠券分摊金额, 随计算减少
            BigDecimal tmpCouponAmt = itemCouponAmt.subtract(dealDiscountAmt);
            BigDecimal supplierDiscountAmt = BigDecimal.ZERO;
            // 计算分摊到商品上
            for (int i = 0; i < supplierItemList.size(); i++) {
                ActivityVerifyItemDTO verifyItem = supplierItemList.get(i);
                // (应付 / 总金额) * 优惠金额
                BigDecimal subCouponAmt = verifyItem.getPayAmt().divide(totalAmt, 6, RoundingMode.HALF_UP).multiply(itemCouponAmt).setScale(2, RoundingMode.UP);
                if (NumberUtil.isGreater(subCouponAmt, tmpCouponAmt)) {
                    subCouponAmt = tmpCouponAmt;
                }
                Long waitDealCount = itemList.stream().filter(t -> t.getDiscountAmt().compareTo(BigDecimal.ZERO) <= 0).count();
                if (i == supplierItemList.size() - 1 && waitDealCount == 1) {
                    subCouponAmt = tmpCouponAmt;
                }
                if (NumberUtil.isGreater(BigDecimal.ZERO, tmpCouponAmt)) {
                    subCouponAmt = BigDecimal.ZERO;
                }
                tmpCouponAmt = tmpCouponAmt.subtract(subCouponAmt);
                supplierDiscountAmt = supplierDiscountAmt.add(subCouponAmt);
                // 计算分摊金额
                BigDecimal payAmt = verifyItem.getPayAmt();
                verifyItem.setPayAmt(verifyItem.getPayAmt().subtract(subCouponAmt));
                // 记录优惠金额
                verifyItem.setDiscountAmt(verifyItem.getDiscountAmt().add(subCouponAmt));
                if (Objects.nonNull(verifyItem.getActivityNum()) && verifyItem.getActivityNum() > NumberPool.INT_ZERO) {
                    // 秒杀价在这个商品里面的占比, 然后乘以优惠券金额
                    BigDecimal activityPayAmt = verifyItem.getActivityPayAmt().subtract(verifyItem.getActivityPayAmt().divide(payAmt, 6, RoundingMode.HALF_UP).multiply(subCouponAmt).setScale(2, RoundingMode.HALF_UP));
                    verifyItem.setActivityPayAmt(activityPayAmt);
                }
            }
            // 设置活动总优惠券金额
            activityLabel.setTotalCouponAmt(activityLabel.getTotalCouponAmt().add(supplierDiscountAmt));
            // 合计优惠金额
            result.setTotalCouponAmt(result.getTotalCouponAmt().add(supplierDiscountAmt));
        }
    }

    /**
     * 处理秒杀
     * @param branch
     * @param varifyItem
     * @param activityRoleMap
     * @param optimumRelation
     */
    private void processSkPriorityBindActivity(BranchDTO branch, List<ActivityVerifyItemDTO> varifyItems, Map<String, List<SupplierActivityDTO>> activityRoleMap, Map<String, Map<ActivityVerifyItemDTO, SupplierActivityDTO>> optimumRelation, ActivityVerifyResultDTO result) {
        String prmNo = PrmNoEnum.SK.getType();
        if (!activityRoleMap.containsKey(prmNo)) {
            return;
        }
        Long skuId = varifyItems.get(0).getSkuId();
        // 只验证没有参加促销的
        varifyItems = varifyItems.stream().filter(item -> Objects.isNull(item.getActivityNum()) || item.getActivityNum() == 0).collect(Collectors.toList());
        if (varifyItems.isEmpty()) {
            return;
        }
        List<SupplierActivityDTO> activityDTOS = activityRoleMap.get(prmNo);
        HashSet<ActivityVerifyItemDTO> dictSet = new HashSet<>();
        // 活动和商品绑定列表
        HashMap<SupplierActivityDTO, List<ActivityVerifyItemDTO>> supplierActivityDTOListHashMap = new HashMap<>();
        for (SupplierActivityDTO activityDTO : activityDTOS) {
            List<ActivityVerifyItemDTO> bindList = new ArrayList<>();
            supplierActivityDTOListHashMap.put(activityDTO, bindList);
            // 验证活动是否能匹配上商品
            Map<Long, SkRuleDTO> skMap = activityDTO.getSkRules().stream().collect(Collectors.toMap(SkRuleDTO::getSkuId, item -> item));
            for (ActivityVerifyItemDTO varifyItem : varifyItems) {
                if (skMap.containsKey(varifyItem.getSkuId()) && !dictSet.contains(varifyItem)) {
                    // 命中了一个就跳过
                    bindList.add(varifyItem);
                    // 一个商品最多参加一个秒杀 / 特价活动
                    dictSet.add(varifyItem);
                }
            }
        }
        supplierActivityDTOListHashMap.forEach((activityDTO, itemList) -> {
            // 获取具体规则
            Map<Long, SkRuleDTO> skMap = activityDTO.getSkRules().stream().collect(Collectors.toMap(SkRuleDTO::getSkuId, item -> item));
            if (!skMap.containsKey(skuId)) {
                return;
            }
            SkRuleDTO skRule = skMap.get(skuId);
            // 过滤没包含的活动单位
            itemList = itemList.stream().filter(skRule::containsUnit).collect(Collectors.toList());
            // 没命中到商品的活动直接就跳过
            if (itemList.isEmpty()) {
                return;
            }
            // 小单位限购
            BigDecimal onceLimit = Objects.nonNull(skRule.getOnceLimit()) ? StockUtil.bigDecimal(skRule.getOnceLimit()) : StockUtil.bigDecimal(Integer.MAX_VALUE);
            // 获取当前门店已经购买了多少个小单位
            BigDecimal saleNum = getSkSaleNum(branch.getBranchId(), activityDTO.getActivityId(), skRule.getSkRuleId());
            // 限购 - 已购 = 门店可购小单位
            BigDecimal validNum = onceLimit.subtract(saleNum);
            // 中单位限购
            BigDecimal midLimit = Optional.ofNullable(StockUtil.bigDecimal(skRule.getMidLimit())).orElse(StockUtil.bigDecimal(Integer.MAX_VALUE));
            // 获取当前门店已经购买了多少个中单位
            BigDecimal midSaleNum = getSkMidSaleNum(branch.getBranchId(), activityDTO.getActivityId(), skRule.getSkRuleId());
            // 限购 - 已购 = 门店可购中单位
            BigDecimal midValidNum = midLimit.subtract(midSaleNum);
            // 大单位限购
            BigDecimal largeLimit = Optional.ofNullable(StockUtil.bigDecimal(skRule.getLargeLimit())).orElse(StockUtil.bigDecimal(Integer.MAX_VALUE));
            // 获取当前门店已经购买了多少个大单位
            BigDecimal largeSaleNum = getSkLargeSaleNum(branch.getBranchId(), activityDTO.getActivityId(), skRule.getSkRuleId());
            // 限购 - 已购 = 门店可购大单位
            BigDecimal largeValidNum = largeLimit.subtract(largeSaleNum);
            // 活动促销总库存（小单位）
            BigDecimal activitySaleNum = getSkSaleNum(activityDTO.getActivityId(), skRule.getSkRuleId());
            // 活动促销总库存（中单位）
            BigDecimal midActivitySaleNum = getSkMidSaleNum(activityDTO.getActivityId(), skRule.getSkRuleId());
            // 活动促销总库存（大单位）
            BigDecimal largeActivitySaleNum = getSkLargeSaleNum(activityDTO.getActivityId(), skRule.getSkRuleId());
            /**
             * ========================================================== start  门店限量验证 start ====================================================
             */
            // 优先销售大单位
            itemList.sort(Comparator.comparing(ActivityVerifyItemDTO::getUnitSize).reversed());
            for (ActivityVerifyItemDTO verifyItemDTO : itemList) {
                Integer currentUnitSize = verifyItemDTO.getUnitSize();
                BigDecimal currentValidNum = UnitTypeEnum.UNIT_LARGE.getType().equals(currentUnitSize) ? largeValidNum : UnitTypeEnum.UNIT_MIDDLE.getType().equals(currentUnitSize) ? midValidNum : validNum;
                // 如果可购买数量 < 0 就跳出
                if (NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, currentValidNum)) {
                    verifyItemDTO.setActivityNum(NumberPool.INT_ZERO);
                    continue;
                }
                BigDecimal buyNum = Optional.ofNullable(verifyItemDTO.getNum()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO);
                if (NumberUtil.isGreaterOrEqual(buyNum, currentValidNum)) {
                    // 活动数量 = 可购买数量
                    verifyItemDTO.setActivityNum(currentValidNum.intValue());
                } else {
                    // 如果当前数量 还没超过限购数量, 则直接使用当前数量
                    verifyItemDTO.setActivityNum(verifyItemDTO.getNum());
                }
                // 更新可购买数量
                if (UnitTypeEnum.UNIT_LARGE.getType().equals(currentUnitSize)) {
                    largeValidNum = largeValidNum.subtract(BigDecimal.valueOf(verifyItemDTO.getActivityNum()));
                } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(currentUnitSize)) {
                    midValidNum = midValidNum.subtract(BigDecimal.valueOf(verifyItemDTO.getActivityNum()));
                } else {
                    validNum = validNum.subtract(BigDecimal.valueOf(verifyItemDTO.getActivityNum()));
                }
            }
            /**
             * ========================================================== start  活动总限购验证 start ====================================================
             */
            /*int totalUnitNum = itemList.stream().mapToInt(ActivityVerifyItemDTO::getUnitNum).sum();
            if ((totalUnitNum > (spRule.getTotalLimitQty() - activitySaleNum)) || Objects.isNull(spRule.getTotalLimitQty())) {
                // 总数量 > 活动总库存 - 活动已销售库存 || 活动库存没限制
                return;
            }*/
            // 判断活动库存还够不够 - 需要分别验证各单位类型的库存
            // 由于itemList中的商品单位类型可能不同，需要分别处理

            // 按单位类型分组处理库存验证
            Map<Integer, List<ActivityVerifyItemDTO>> unitGroupMap = itemList.stream()
                    .collect(Collectors.groupingBy(ActivityVerifyItemDTO::getUnitSize));

            // 分别验证各单位类型的库存
            for (Map.Entry<Integer, List<ActivityVerifyItemDTO>> entry : unitGroupMap.entrySet()) {
                Integer unitSize = entry.getKey();
                List<ActivityVerifyItemDTO> unitItems = entry.getValue();

                // 计算该单位类型的总需求量
                BigDecimal totalUnitNum = BigDecimal.valueOf(unitItems.stream()
                        .map(ActivityVerifyItemDTO::getActivityNum)
                        .reduce(0, Integer::sum));

                // 根据单位类型获取对应的剩余库存
                BigDecimal balanceActivityStock;
                if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(unitSize)) {
                    // 中单位库存验证
                    balanceActivityStock = Objects.nonNull(skRule.getSeckillMidStock()) ?
                            (StockUtil.bigDecimal(skRule.getSeckillMidStock()).subtract(midActivitySaleNum)) :
                            // 如果没有设置中单位库存，使用小单位库存转换（需要除以中单位转换率）
                            stockService.getSurplusSaleQtyBigDecimal(skuId).divide(
                                    unitItems.get(0).getStockConvertRate(), 0, RoundingMode.DOWN);
                } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(unitSize)) {
                    // 大单位库存验证
                    balanceActivityStock = Objects.nonNull(skRule.getSeckillLargeStock()) ?
                            (StockUtil.bigDecimal(skRule.getSeckillLargeStock()).subtract(largeActivitySaleNum)) :
                            // 如果没有设置大单位库存，使用小单位库存转换（需要除以大单位转换率）
                            stockService.getSurplusSaleQtyBigDecimal(skuId).divide(
                                    unitItems.get(0).getStockConvertRate(), 0, RoundingMode.DOWN);
                } else {
                    // 小单位库存验证
                    balanceActivityStock = Objects.nonNull(skRule.getSeckillStock()) ?
                            (StockUtil.bigDecimal(skRule.getSeckillStock()).subtract(activitySaleNum)) :
                            // 小单位直接使用库存，不需要转换
                            stockService.getSurplusSaleQtyBigDecimal(skuId);
                }

                // 验证该单位类型的库存是否足够
                if (NumberUtil.isGreater(totalUnitNum, balanceActivityStock)) {
                    // 如果库存不够，调整该单位类型商品的活动数量
                    adjustActivityQuantityForUnit(unitItems, balanceActivityStock);
                }
            }
            /**
             * ========================================================== end  活动总限购验证 end ====================================================
             */
            // 收纳数据统计
            itemList.stream().filter(varifyItem -> Objects.nonNull(varifyItem.getActivityNum()) && varifyItem.getActivityNum() > NumberPool.INT_ZERO).forEach(varifyItem -> {
                // 根据单位计算适用规则
                getRulePrice(varifyItem, skRule);
                // 重新计算商品的应付金额
                varifyItem.setPayAmt(varifyItem.getAmt());
                // 计算活动促销价格
                varifyItem.setActivityPayAmt(NumberUtil.toBigDecimal(varifyItem.getActivityNum()).multiply(varifyItem.getActivityPrice()));

                ActivityLabelInfoVO<SkRuleDTO> activityLabel = new ActivityLabelInfoVO();
                activityLabel.setActivity(activityDTO)
                        .setTargetValue(BigDecimal.ZERO)
                        .setFinishValue(BigDecimal.ZERO)
                        .getAdequateRole().add(skRule)
                ;
                varifyItem.getAdequateList().add(activityLabel);
                optimumRelation.get(prmNo).put(varifyItem, activityDTO);
                // 特价 / 秒杀 / 金额优惠计算
                processSpAndSkActivityApplyRole(activityDTO, varifyItem, result);
            });
        });
    }

    private static void getRulePrice(ActivityVerifyItemDTO varifyItem, SkRuleDTO ruleDTO) {
        if (UnitTypeEnum.S(varifyItem.getUnitSize())) {
            varifyItem.setActivityPrice(ruleDTO.getSeckillPrice());
        } else if (UnitTypeEnum.M(varifyItem.getUnitSize())) {
            varifyItem.setActivityPrice(ruleDTO.getMidSeckillPrice());
        } else if (UnitTypeEnum.L(varifyItem.getUnitSize())) {
            varifyItem.setActivityPrice(ruleDTO.getLargeSeckillPrice());
        } else {
            varifyItem.setActivityPrice(varifyItem.getItemPrice());
        }
    }

    private static void getRulePrice(ActivityVerifyItemDTO varifyItem, SpRuleDTO ruleDTO) {
        if (UnitTypeEnum.S(varifyItem.getUnitSize())) {
            varifyItem.setActivityPrice(ruleDTO.getSpPrice());
        } else if (UnitTypeEnum.M(varifyItem.getUnitSize())) {
            varifyItem.setActivityPrice(ruleDTO.getMidSpPrice());
        } else if (UnitTypeEnum.L(varifyItem.getUnitSize())) {
            varifyItem.setActivityPrice(ruleDTO.getLargeSpPrice());
        } else {
            varifyItem.setActivityPrice(varifyItem.getItemPrice());
        }
    }

    /**
     * 处理特价
     * @param branch
     * @param varifyItem
     * @param activityRoleMap
     * @param optimumRelation
     */
    private void processSpPriorityBindActivity(BranchDTO branch, List<ActivityVerifyItemDTO> varifyItems, Map<String, List<SupplierActivityDTO>> activityRoleMap, Map<String, Map<ActivityVerifyItemDTO, SupplierActivityDTO>> optimumRelation, ActivityVerifyResultDTO result) {
        String prmNo = PrmNoEnum.SP.getType();
        if (!activityRoleMap.containsKey(prmNo)) {
            return;
        }
        List<SupplierActivityDTO> activityDTOS = activityRoleMap.get(prmNo);
        for (SupplierActivityDTO activityDTO : activityDTOS) {
            // 只验证没有参加促销的
            varifyItems = varifyItems.stream().filter(item -> Objects.isNull(item.getActivityNum()) && item.getSelected()).collect(Collectors.toList());
            if (varifyItems.isEmpty()) {
                continue;
            }
            // 获取具体规则
            Map<Long, SpRuleDTO> spMap = activityDTO.getSpRules().stream().collect(Collectors.toMap(SpRuleDTO::getSkuId, item -> item));
            // 同一条活动规则, 可能命中购物车多个商品记录, 他们共享一条活动规则的限制
            Map<Long, BigDecimal> validNumMap = new HashMap<>();
            Map<Long, BigDecimal> midValidNumMap = new HashMap<>();
            Map<Long, BigDecimal> largeValidNumMap = new HashMap<>();
            Map<String, BigDecimal> balanceActivityStockMap = new HashMap<>();
            /**
             * 2025年3月10日, 由于需要满足先加入购物车的商品, 先匹配特价, 导致同一个sku, 优先售卖特价大单位无法满足
             */
            Integer limitSkus = activityDTO.getLimitSkus();
            SpActivityTimesRuleEnum timesRuleEnum = SpActivityTimesRuleEnum.formValue(activityDTO.getTimesRule());
            if (Objects.isNull(limitSkus)) {
                limitSkus = Integer.MAX_VALUE;
            }
            // 只有指定类型才允许设置限定SKU
            if (!timesRuleEnum.validateSkuLimit()) {
                limitSkus = Integer.MAX_VALUE;
            }

            // 按照加入购物车时间优先匹配
            varifyItems.forEach(itemDTO -> {
                if (Objects.isNull(itemDTO.getCarTime())) {
                    itemDTO.setCarTime(Long.MAX_VALUE);
                }
            });
            varifyItems.sort(Comparator.comparing(ActivityVerifyItemDTO::getCarTime));
            for (ActivityVerifyItemDTO varifyItem : varifyItems) {
                Long skuId = varifyItem.getSkuId();
                // 促销包含当前活动
                if (!spMap.containsKey(skuId)) {
                    continue;
                }
                SpRuleDTO spRule = spMap.get(skuId);
                if (!spRule.containsUnit(varifyItem)) {
                    continue;
                }
                BigDecimal validNum = validNumMap.get(spRule.getSpRuleId());
                if (Objects.isNull(validNum)) {
                    // 小单位限购
                    BigDecimal onceLimit = Objects.nonNull(spRule.getOnceBuyLimit()) ? StockUtil.bigDecimal(spRule.getOnceBuyLimit()) : StockUtil.bigDecimal(Integer.MAX_VALUE);
                    // 获取当前门店已经购买了多少个小单位
                    BigDecimal saleNum = getSpSaleNum(branch.getBranchId(), activityDTO.getActivityId(), spRule.getSpRuleId());
                    // 限购 - 已购 = 门店可购小单位
                    validNum = onceLimit.subtract(saleNum);
                    validNumMap.put(spRule.getSpRuleId(), validNum);
                }

                BigDecimal midValidNum = midValidNumMap.get(spRule.getSpRuleId());
                if (Objects.isNull(midValidNum)) {
                    // 中单位限购
                    BigDecimal midLimit = Optional.ofNullable(StockUtil.bigDecimal(spRule.getMidLimit())).orElse(StockUtil.bigDecimal(Integer.MAX_VALUE));
                    // 获取当前门店已经购买了多少个中单位
                    BigDecimal midSaleNum = getSpMidSaleNum(branch.getBranchId(), activityDTO.getActivityId(), spRule.getSpRuleId());
                    // 限购 - 已购 = 门店可购中单位
                    midValidNum = midLimit.subtract(midSaleNum);
                    midValidNumMap.put(spRule.getSpRuleId(), midValidNum);
                }

                BigDecimal largeValidNum = largeValidNumMap.get(spRule.getSpRuleId());
                if (Objects.isNull(largeValidNum)) {
                    // 大单位限购
                    BigDecimal largeLimit = Optional.ofNullable(StockUtil.bigDecimal(spRule.getLargeLimit())).orElse(StockUtil.bigDecimal(Integer.MAX_VALUE));
                    // 获取当前门店已经购买了多少个大单位
                    BigDecimal largeSaleNum = getSpLargeSaleNum(branch.getBranchId(), activityDTO.getActivityId(), spRule.getSpRuleId());
                    // 限购 - 已购 = 门店可购大单位
                    largeValidNum = largeLimit.subtract(largeSaleNum);
                    largeValidNumMap.put(spRule.getSpRuleId(), largeValidNum);
                }
                // 当前活动规则已售
                BigDecimal activitySaleNum = getSpSaleNum(activityDTO.getActivityId(), spRule.getSpRuleId());
                // 当前商品的单位
                Integer currentUnitSize = varifyItem.getUnitSize();
                // 当前商品单位的可购买数量
                BigDecimal currentValidNum = UnitTypeEnum.UNIT_LARGE.getType().equals(currentUnitSize) ? largeValidNum : UnitTypeEnum.UNIT_MIDDLE.getType().equals(currentUnitSize) ? midValidNum : validNum;
                /**
                 * ========================================================== start  门店限量验证 start ====================================================
                 */
                // 如果可购买数量 < 0 就跳出
                if (NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, currentValidNum)) {
                    varifyItem.setActivityNum(NumberPool.INT_ZERO);
                    continue;
                }
                BigDecimal activityNum;
                BigDecimal buyNum = Optional.ofNullable(varifyItem.getNum()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO);
                if (NumberUtil.isGreaterOrEqual(buyNum, currentValidNum)) {
                    // 活动数量 = 可购买数量
                    activityNum = currentValidNum;
                } else {
                    // 如果当前数量 还没超过限购数量, 则直接使用当前数量
                    activityNum = BigDecimal.valueOf(varifyItem.getNum());
                }
                // 库存不够购买1份了, 活动门店限购
                if (NumberUtil.isGreater(BigDecimal.ONE, activityNum)) {
                    continue;
                }
                /**
                 * ========================================================== start  活动总限购验证 start ====================================================
                 */
                // 判断活动库存还够不够 - 根据单位类型使用对应的库存
                // 如果没有设置活动库存上限, 那就直接取SKU库存
                String stockMapKey = spRule.getSpRuleId() + "_" + currentUnitSize;
                BigDecimal balanceActivityStock = balanceActivityStockMap.get(stockMapKey);
                if (Objects.isNull(balanceActivityStock)) {
                    // 根据单位类型获取对应的库存和销量
                    if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(currentUnitSize)) {
                        // 中单位库存验证
                        BigDecimal activityMidSaleNum = getSpMidSaleNum(activityDTO.getActivityId(), spRule.getSpRuleId());
                        balanceActivityStock = Objects.nonNull(spRule.getMidTotalLimitQty()) ?
                                (StockUtil.bigDecimal(spRule.getMidTotalLimitQty()).subtract(activityMidSaleNum)) :
                                // 使用小单位库存转换（需要除以中单位转换率）
                                stockService.getSurplusSaleQtyBigDecimal(skuId).divide(
                                        varifyItem.getStockConvertRate(), 0, RoundingMode.DOWN);
                    } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(currentUnitSize)) {
                        // 大单位库存验证
                        BigDecimal activityLargeSaleNum = getSpLargeSaleNum(activityDTO.getActivityId(), spRule.getSpRuleId());
                        balanceActivityStock = Objects.nonNull(spRule.getLargeTotalLimitQty()) ?
                                (StockUtil.bigDecimal(spRule.getLargeTotalLimitQty()).subtract(activityLargeSaleNum)) :
                                // 使用小单位库存转换（需要除以大单位转换率）
                                stockService.getSurplusSaleQtyBigDecimal(skuId).divide(
                                        varifyItem.getStockConvertRate(), 0, RoundingMode.DOWN);
                    } else {
                        // 小单位库存验证
                        balanceActivityStock = Objects.nonNull(spRule.getTotalLimitQty()) ?
                                (StockUtil.bigDecimal(spRule.getTotalLimitQty()).subtract(activitySaleNum)) :
                                // 小单位直接使用库存，不需要转换
                                stockService.getSurplusSaleQtyBigDecimal(skuId);
                    }
                    balanceActivityStockMap.put(stockMapKey, balanceActivityStock);
                }
                if (NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, balanceActivityStock)) {
                    activityNum = BigDecimal.ZERO;
                }
                if (NumberUtil.isGreater(activityNum, balanceActivityStock)) {
                    // 库存已经不够当前商品挥霍了, 用剩余库存得出当前单位还可以购买几个
                    activityNum = balanceActivityStock;
                }
                // 活动总限购
                if (NumberUtil.isGreater(BigDecimal.ONE, activityNum)) {
                    continue;
                }
                // 如果确实可以购买, 但是已经达到了活动限制, 那就绑定一个活动, 但是无有效参与
                if (limitSkus <= NumberPool.INT_ZERO) {
                    activityNum = BigDecimal.ZERO;
                }
                // 更新可购买数量
                if (UnitTypeEnum.UNIT_LARGE.getType().equals(currentUnitSize)) {
                    largeValidNum = largeValidNum.subtract(activityNum);
                    largeValidNumMap.put(spRule.getSpRuleId(), largeValidNum);
                } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(currentUnitSize)) {
                    midValidNum = midValidNum.subtract(activityNum);
                    midValidNumMap.put(spRule.getSpRuleId(), midValidNum);
                } else {
                    validNum = validNum.subtract(activityNum);
                    validNumMap.put(spRule.getSpRuleId(), validNum);
                }
                varifyItem.setActivityNum(activityNum.intValue());
                // 更新对应单位类型的剩余库存
                balanceActivityStock = balanceActivityStock.subtract(BigDecimal.valueOf(varifyItem.getActivityNum()));
                balanceActivityStockMap.put(stockMapKey, balanceActivityStock);
                // 收纳数据统计
                {
                    // 根据单位计算适用规则
                    getRulePrice(varifyItem, spRule);
                    // 重新计算商品的应付金额
                    varifyItem.setPayAmt(varifyItem.getAmt());
                    // 计算活动总价
                    varifyItem.setActivityPayAmt(NumberUtil.toBigDecimal(varifyItem.getActivityNum()).multiply(varifyItem.getActivityPrice()));
                    ActivityLabelInfoVO<SpRuleDTO> activityLabel = new ActivityLabelInfoVO();
                    activityLabel.setActivity(activityDTO)
                            .setTargetValue(BigDecimal.ZERO)
                            .setFinishValue(BigDecimal.ZERO)
                            .getAdequateRole().add(spRule)
                    ;
                    varifyItem.getAdequateList().add(activityLabel);
                    optimumRelation.get(prmNo).put(varifyItem, activityDTO);
                    // 特价 / 秒杀 / 金额优惠计算
                    processSpAndSkActivityApplyRole(activityDTO, varifyItem, result);
                }
                limitSkus--;
            }
        }
    }

    /**
     * 规则
     *      全场  >  类别   >   品牌  >   商品
     * 处理满减,满赠,买赠活动, 绑定商品最选种
     * @param itemDTO           商品
     * @param activityRoleMap   活动分组
     * @param optimumRelation   商品与活动绑定关系
     */
    private static void processPriorityBindActivityBySku(
            ActivityVerifyItemDTO varifyItem,
            Map<String, List<SupplierActivityDTO>> activityRoleMap,
            Map<String, Map<ActivityVerifyItemDTO, SupplierActivityDTO>> optimumRelation,
            String prmNo
    ) {
        if (!activityRoleMap.containsKey(prmNo)) {
            return;
        }

        // 同一时间类, 只能一个全场, 同一类目, 同一品牌, 同一商品 只能存在一种促销活动
        List<SupplierActivityDTO> activityDTOS = activityRoleMap.get(prmNo);
        if (PrmNoEnum.isBg(prmNo)) {
            // 买赠没有单位限制, 或者商品是买赠指定单位
            activityDTOS = activityDTOS.stream().filter(item -> Objects.isNull(item.getActivityUnitType()) || (varifyItem.getUnitSize().equals(item.getActivityUnitType()))).collect(Collectors.toList());
        }

        // 按照使用范围
        Map<Integer, List<SupplierActivityDTO>> scopeMap = activityDTOS.stream().collect(Collectors.groupingBy(SupplierActivityDTO::getSpuScope));
        // 全场券优先级第一
        {
            // 如果全场,
            if (scopeMap.containsKey(ActivitySpuScopeEnum.ALL.getScope())) {
                List<SupplierActivityDTO> allActivity = scopeMap.get(ActivitySpuScopeEnum.ALL.getScope());
                // 使用最先创建的
                allActivity.sort(Comparator.comparing(ActivityDTO::getActivityId));
                SupplierActivityDTO bindActivity = allActivity.get(NumberPool.INT_ZERO);
                List<Long> supplierIdList = bindActivity.getSupplierIdList();
                // 验证商品参与范围
                if (supplierIdList.contains(varifyItem.getSupplierId()) && ActivityScopeUtil.validateSpuScope(ActivityScopeUtil.ActivitySpuScopeValidVO.build(varifyItem), bindActivity.getActivitySpuScopeList())) {
                    optimumRelation.get(prmNo).put(varifyItem, bindActivity);
                }
                return;
            }
        }
        // 品类优先级第二, 说是管理分类
        if (validateScope(varifyItem, scopeMap.get(ActivitySpuScopeEnum.CLASS.getScope()), optimumRelation.get(prmNo))) {
            return;
        }
        // 品牌优先级第三
        if (validateScope(varifyItem, scopeMap.get(ActivitySpuScopeEnum.BRAND.getScope()), optimumRelation.get(prmNo))) {
            return;
        }
        // 商品优先级第四
        if (validateScope(varifyItem, scopeMap.get(ActivitySpuScopeEnum.SKU.getScope()), optimumRelation.get(prmNo))) {
            return;
        }
    }

    /**
     * spu 通常用于计算单个SPU品参与了那些活动
     * @param varifyItem
     * @param activityRoleMap
     * @param optimumRelation
     * @param type
     */
    private void processPriorityBindActivityBySpu(
            ActivityVerifyItemDTO varifyItem,
            Map<String, List<SupplierActivityDTO>> activityRoleMap,
            Map<String, Map<ActivityVerifyItemDTO,
                    SupplierActivityDTO>> optimumRelation,
            String prmNo) {
        if (!activityRoleMap.containsKey(prmNo)) {
            return;
        }
        // 同一时间类, 只能一个全场, 同一类目, 同一品牌, 同一商品 只能存在一种促销活动
        List<SupplierActivityDTO> activityDTOS = activityRoleMap.get(prmNo);
        if (PrmNoEnum.isBg(prmNo)) {
            // 买赠没有单位限制, 或者商品是买赠指定单位
            activityDTOS = activityDTOS.stream().filter(item -> Objects.isNull(item.getActivityUnitType()) || (varifyItem.getUnitSize().equals(item.getActivityUnitType()))).collect(Collectors.toList());
        }
        // 按照使用范围
        Map<Integer, List<SupplierActivityDTO>> scopeMap = activityDTOS.stream().collect(Collectors.groupingBy(SupplierActivityDTO::getSpuScope));
        // 全场券优先级第一
        {
            // 如果全场,
            if (scopeMap.containsKey(ActivitySpuScopeEnum.ALL.getScope())) {
                List<SupplierActivityDTO> allActivity = scopeMap.get(ActivitySpuScopeEnum.ALL.getScope());
                // 使用最先创建的
                allActivity.sort(Comparator.comparing(ActivityDTO::getActivityId));
                SupplierActivityDTO bindActivity = allActivity.get(NumberPool.INT_ZERO);
                List<Long> supplierIdList = bindActivity.getSupplierIdList();
                // 验证商品参与范围
                if (supplierIdList.contains(varifyItem.getSupplierId()) && ActivityScopeUtil.validateSpuScope(ActivityScopeUtil.ActivitySpuScopeValidVO.build(varifyItem), bindActivity.getActivitySpuScopeList())) {
                    optimumRelation.get(prmNo).put(varifyItem, bindActivity);
                }
                return;
            }
        }
        // 品类优先级第二, 说是管理分类
        if (validateScope(varifyItem, scopeMap.get(ActivitySpuScopeEnum.CLASS.getScope()), optimumRelation.get(prmNo))) {
            return;
        }
        // 品牌优先级第三
        if (validateScope(varifyItem, scopeMap.get(ActivitySpuScopeEnum.BRAND.getScope()), optimumRelation.get(prmNo))) {
            return;
        }
        // 商品优先级第四
        if (validateScope(varifyItem, scopeMap.get(ActivitySpuScopeEnum.SKU.getScope()), optimumRelation.get(prmNo))) {
            return;
        }
    }

    private static boolean validateScope(
            ActivityVerifyItemDTO itemDTO,
            List<SupplierActivityDTO> scopeActivityList,
            Map<ActivityVerifyItemDTO, SupplierActivityDTO> roleMap
    ) {
        if (ObjectUtil.isNotEmpty(scopeActivityList)) {
            for (SupplierActivityDTO activityDTO : scopeActivityList) {
                List<Long> supplierIdList = activityDTO.getSupplierIdList();
                // 验证商品参与范围
                if (supplierIdList.contains(itemDTO.getSupplierId()) && ActivityScopeUtil.validateSpuScope(ActivityScopeUtil.ActivitySpuScopeValidVO.build(itemDTO), activityDTO.getActivitySpuScopeList())) {
                    roleMap.put(itemDTO, activityDTO);
                    return true;
                }
            }
        }
        return false;
    }

    @Deprecated
    private static boolean validateScopeSpu(
            ActivityVerifyItemDTO itemDTO,
            List<SupplierActivityDTO> scopeActivityList,
            Long applyId,
            Map<ActivityVerifyItemDTO, SupplierActivityDTO> roleMap
    ) {
        if (Objects.nonNull(scopeActivityList)) {
            // 查看有没有当前商品的品类
            List<SupplierActivityDTO> validScopeActivityList = scopeActivityList.stream().filter(activity ->
                    Objects.nonNull(activity.getActivitySpuScopeList()) && activity.getActivitySpuScopeList().stream().map(ActivitySpuScopeDTO::getSpuId).collect(Collectors.toSet()).contains(applyId)
            ).collect(Collectors.toList());
            if (!validScopeActivityList.isEmpty()) {
                SupplierActivityDTO bindActivity = validScopeActivityList.get(NumberPool.INT_ZERO);
                roleMap.put(itemDTO, bindActivity);
                return true;
            }
        }
        return false;
    }

    public boolean validateActivity(SupplierActivityDTO activity, BranchDTO branch, List<ActivityVerifyItemDTO> verifyItemList) {
        if (verifyItemList.isEmpty()) {
            return false;
        }
        // 商品是全国但是促销不是全国的
        if (ProductType.isGlobal(verifyItemList.get(0).getType()) && activity.getFuncScope() != NumberPool.INT_ONE) {
            return false;
        }
        // 商品是本地但是促销不是本地的
        if (!ProductType.isGlobal(verifyItemList.get(0).getType()) && activity.getFuncScope() != NumberPool.INT_TWO) {
            return false;
        }
        // 活动规则判断
        {
            if (PrmNoEnum.isBg(activity.getPrmNo()) && (Objects.isNull(activity.getBgRules()) || activity.getBgRules().isEmpty())) {
                return false;
            }
            if (PrmNoEnum.isFg(activity.getPrmNo()) && (Objects.isNull(activity.getFgRules()) || activity.getFgRules().isEmpty())) {
                return false;
            }
            if (PrmNoEnum.isFd(activity.getPrmNo()) && (Objects.isNull(activity.getFdRules()) || activity.getFdRules().isEmpty())) {
                return false;
            }
        }
        return validateActivity(activity, branch);
    }

    public boolean validateActivity(SupplierActivityDTO activity, BranchDTO branch) {
        // 是否是系统首单
        if (Objects.nonNull(activity.getTimesRule()) && activity.getTimesRule() == NumberPool.INT_THREE && branch.fistOrderFlagTrue()) {
            // 已经不是新客户了
            return false;
        }
        // 是否达到活动参与频次
        if (redisService.hasKey(RedisActivityConstants.getTimesRuleKey(activity.getActivityId(), branch.getBranchId()))) {
            // 已经达到上限
            return false;
        }
        // 城市验证
        if (ObjectUtil.isNotEmpty(activity.getCityScopeList())) {
            ActivityCityScopeDTO cityScopeDTO = activity.getCityScopeList().stream().findFirst().get();
            Set<Long> applyIds = activity.getCityScopeList().stream().map(ActivityCityScopeDTO::getAreaId).collect(Collectors.toSet());
            // 1-白名单 0-黑名单
            if (cityScopeDTO.getWhiteOrBlack() == NumberPool.INT_ONE) {
                // 白名单不包含
                if (!applyIds.contains(branch.getAreaId())) {
                    return false;
                }
            } else {
                // 黑名单包含
                if (applyIds.contains(branch.getAreaId())) {
                    return false;
                }
            }
        }

        // 渠道验证
        if (ObjectUtil.isNotEmpty(activity.getChannelScopeList())) {
            ActivityChannelScopeDTO channelScopeDTO = activity.getChannelScopeList().stream().findFirst().get();
            Set<Long> applyIds = activity.getChannelScopeList().stream().map(ActivityChannelScopeDTO::getChannelId).collect(Collectors.toSet());
            // 1-白名单 0-黑名单
            if (channelScopeDTO.getWhiteOrBlack() == NumberPool.INT_ONE) {
                // 白名单不包含
                if (!applyIds.contains(branch.getChannelId())) {
                    return false;
                }
            } else {
                // 黑名单包含
                if (applyIds.contains(branch.getChannelId())) {
                    return false;
                }
            }
        }

        // 门店验证
        if (ObjectUtil.isNotEmpty(activity.getBranchScopeList())) {
            ActivityBranchScopeDTO branchScopeDTO = activity.getBranchScopeList().stream().findFirst().get();
            Set<Long> applyIds = activity.getBranchScopeList().stream().map(ActivityBranchScopeDTO::getBranchId).collect(Collectors.toSet());
            // 1-白名单 0-黑名单
            if (branchScopeDTO.getWhiteOrBlack() == NumberPool.INT_ONE) {
                // 白名单不包含
                if (!applyIds.contains(branch.getBranchId())) {
                    return false;
                }
            } else {
                // 黑名单包含
                if (applyIds.contains(branch.getBranchId())) {
                    return false;
                }
            }
        }

        // 没到活动开始时间
        if (activity.getStartTime().getTime() > System.currentTimeMillis()) {
            return false;
        }
        // 没到活动已结束
        if (activity.getEndTime().getTime() < System.currentTimeMillis()) {
            return false;
        }
        // 没启用
        if (activity.getPrmStatus() != NumberPool.INT_ONE) {
            return false;
        }
        return true;
    }

    public BigDecimal getTotalGiftQty(GiveRuleDTO rule) {
        // 总数 - 已赠 < 本地需要赠送
        BigDecimal totalGiftQty = Objects.nonNull(rule.getTotalGiftQty()) ? StockUtil.bigDecimal(rule.getTotalGiftQty()) : null;
        // 如果没有总限量则取库存数量
        // 并且赠品是商品
        if (Objects.isNull(totalGiftQty) && (Objects.nonNull(rule.getGiftType()) && rule.getGiftType() == NumberPool.INT_ZERO)) {
            totalGiftQty = this.getSkuInventory(rule.getSkuId(), rule.getGiftSkuUnitType());
        }
        // 优惠券库存
        if (Objects.isNull(totalGiftQty) && (Objects.nonNull(rule.getGiftType()) && rule.getGiftType() == NumberPool.INT_ONE)) {
            totalGiftQty = StockUtil.bigDecimal(this.getCouponInventory(rule.getCouponTemplateId()));
        }
        return totalGiftQty;
    }

    /**
     * 获取sku单位剩余数量
     * @param skuId     skuID
     * @param unitType  单位大小
     * @return  单位换算比例实际有效库存
     */
    public BigDecimal getSkuInventory(Long skuId, Integer unitType) {
        SkuDTO skuDTO = skuCache.get(skuId);
        if (Objects.isNull(skuDTO)) {
            return BigDecimal.ZERO;
        }
        SpuDTO spuDTO = spuCache.get(skuDTO.getSpuId());
        // 根据单位换算的剩余库存
//        Long balanceStock = stockService.getSurplusSaleQty(skuId) / spuDTO.stockConvert(unitType);
        return stockService.getSurplusSaleQtyBigDecimal(skuId).divide(spuDTO.stockConvert(unitType), 3, RoundingMode.HALF_UP);
    }

    /**
     * 获取优惠券剩余库存
     * @param couponTemplateId  优惠券模版ID
     * @return  优惠券有效库存
     */
    public Long getCouponInventory(Long couponTemplateId) {
        if (Objects.isNull(couponTemplateId)) {
            return NumberPool.LONG_ZERO;
        }
        String stockUsedKey = RedisCouponConstants.getStockUsedKey(couponTemplateId);
        String stockKey = RedisCouponConstants.getStockKey(couponTemplateId);
        // 已领取数量
        Integer stockUsed = redisService.getCacheObject(stockUsedKey);
        if (Objects.isNull(stockUsed)) {
            stockUsed = NumberPool.INT_ZERO;
        }
        // 总可领取数量
        Integer stock = redisService.getCacheObject(stockKey);
        if (Objects.isNull(stock)) {
            stock = NumberPool.INT_ZERO;
        }
        return (long) (stock - stockUsed);
    }

    public static void main(String[] args) {
        BigDecimal saleNum = BigDecimal.valueOf(0);
        BigDecimal onceLimit = BigDecimal.valueOf(3);
        BigDecimal stockConvert = BigDecimal.valueOf(8);
        BigDecimal validNum = BigDecimal.valueOf(3);
        BigDecimal balanceStock = BigDecimal.valueOf(1400.0);
        // 已售大于购买上限, 或者购买上限甚至都小于一次购买
        if (NumberUtil.isGreaterOrEqual(saleNum, onceLimit) || NumberUtil.isGreater(stockConvert, onceLimit)) {
            System.out.println("1");
        }
        // 如果可购买数量 < 0 就跳出
        // 或者可购买数小于一次购买
        if (NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, validNum) || NumberUtil.isGreater(stockConvert, validNum)) {
            System.out.println("2");
        }

        if (NumberUtil.isGreater(stockConvert, balanceStock)) {
            System.out.println("3");
        }

        System.out.println("4");
    }

    /**
     * 按单位类型调整活动数量
     * @param unitItems 同一单位类型的商品列表
     * @param balanceActivityStock 该单位类型的剩余库存
     */
    private void adjustActivityQuantityForUnit(List<ActivityVerifyItemDTO> unitItems, BigDecimal balanceActivityStock) {

        BigDecimal remainingStock = balanceActivityStock;

        for (ActivityVerifyItemDTO verifyItemDTO : unitItems) {
            if (NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, remainingStock)) {
                // 库存已经用完，设置为0
                verifyItemDTO.setActivityNum(NumberPool.INT_ZERO);
                continue;
            }
            BigDecimal activityNum = BigDecimal.valueOf(verifyItemDTO.getActivityNum());
            if (NumberUtil.isGreaterOrEqual(remainingStock, activityNum)) {
                // 当前商品需求量 <= 剩余库存，可以全部满足
                remainingStock = remainingStock.subtract(activityNum);
                continue;
            }

            // 库存不够，需要调整数量
            int availableNum = remainingStock.intValue();
            verifyItemDTO.setActivityNum(availableNum);

            // 更新剩余库存
            remainingStock = BigDecimal.ZERO;
        }
    }

    // ========== 特价活动中单位销量管理方法 ==========

    /**
     * 获取特价活动中单位总已购买数量
     */
    public BigDecimal getSpMidSaleNum(Long activityId, Long activityRuleId) {
        String key = RedisActivityConstants.getSpMidTotalSaleNumKey(activityId, activityRuleId);
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /** SET特价商品已购买总数量（中单位）
     * @param activityId    活动ID
     * @param activityRuleId         活动规则Id
     * @return
     */
    public void setSpMidSaleNum(Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 特价商品已购总数量
        String key = RedisActivityConstants.getSpMidTotalSaleNumKey(activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(key, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(key, timeout, unit);//过期时间设置
        }
    }

    // ========== 特价活动大单位销量管理方法 ==========

    /**
     * 获取特价活动大单位总已购买数量
     */
    public BigDecimal getSpLargeSaleNum(Long activityId, Long activityRuleId) {
        String key = RedisActivityConstants.getSpLargeTotalSaleNumKey(activityId, activityRuleId);
        Object saledQty = redisService.getCacheObject(key);
        if(saledQty == null){ return BigDecimal.ZERO; }
        return StockUtil.bigDecimal(saledQty);
    }

    /** SET特价商品已购买总数量（大单位）
     * @param activityId    活动ID
     * @param activityRuleId         活动规则Id
     * @return
     */
    public void setSpLargeSaleNum(Long activityId, Long activityRuleId, BigDecimal saleQty, Long timeout, TimeUnit unit) {
        // 特价商品已购总数量
        String key = RedisActivityConstants.getSpLargeTotalSaleNumKey(activityId, activityRuleId);
        redisService.incrDoubleByCacheObject(key, saleQty.doubleValue());
        if (ToolUtil.isNotEmpty(timeout)) { // 数量返还时不需要再次设置过期时间， timeout 设置为NUll
            redisService.expire(key, timeout, unit);//过期时间设置
        }
    }
}
