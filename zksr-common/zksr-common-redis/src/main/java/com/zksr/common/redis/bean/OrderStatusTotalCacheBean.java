package com.zksr.common.redis.bean;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.RefreshPolicy;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.trade.api.order.vo.OrderStatusVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

import static com.zksr.common.redis.enums.RedisConstants.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单角标数据统计
 * @date 2024/6/22 8:38
 */
@Configuration
public class OrderStatusTotalCacheBean {

    @Autowired
    private CacheManager cacheManager;

    private Cache<Long, OrderStatusVO> orderStatusTotalCache;

    @PostConstruct
    public void init() {
        // 每13分钟刷新一次, 30分钟后不自动刷新
        RefreshPolicy policy = RefreshPolicy.newPolicy(13, TimeUnit.MINUTES)
                .stopRefreshAfterLastAccess(30, TimeUnit.MINUTES);

        // 过期时间为30分钟
        QuickConfig qc = QuickConfig.newBuilder(TRD_ORDER_STATUS)
                .expire(Duration.ofMinutes(30))
                .cacheType(CacheType.REMOTE)
                //本地缓存更新后，将在所有的节点中删除缓存，以保持强一致性
                .syncLocal(true)//invalidate local cache in all jvm process after update(更新后使所有JVM进程中的本地缓存失效)
                .refreshPolicy(policy)
                .penetrationProtect(true)//当缓存访问未命中的情况下，对并发进行的加载行为进行保护。 当前版本实现的是单JVM内的保护，即同一个JVM中同一个key只有一个线程去加载，其它线程等待结果。
                .build();
        orderStatusTotalCache = cacheManager.getOrCreateCache(qc);
    }


    @Bean
    public Cache<Long, OrderStatusVO> getOrderStatusTotalCache() {
        return orderStatusTotalCache;
    }
}
