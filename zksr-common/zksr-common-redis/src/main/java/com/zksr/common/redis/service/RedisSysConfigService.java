package com.zksr.common.redis.service;

import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.system.api.sysconfig.GlobalPayConfigDTO;
import com.zksr.system.api.sysconfig.SupplierPayConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: sys_config 配置信息
 * @date 2024/4/20 16:43
 */
@Component
public class RedisSysConfigService {

    @Autowired
    private RedisService redisService;

    public SupplierPayConfigDTO getSupplierConfig() {
        String json = redisService.getCacheObject(CacheConstants.SUPPLIER_PAY_CONFIG);
        return JSON.parseObject(json, SupplierPayConfigDTO.class);
    }

    public <T> T querySupplierConfig(Class<T> clazz) {
        String json = redisService.getCacheObject(CacheConstants.SUPPLIER_PAY_CONFIG);
        return JSON.parseObject(json, clazz);
    }

    public String getSupplierAppid() {
        return redisService.getCacheObject(CacheConstants.SUPPLIER_APPID);
    }

    public String getSupplierKey() {
        return redisService.getCacheObject(CacheConstants.SUPPLIER_APP_SECRET);
    }

    /**
     * 获取全局支付参数
     * @param platform  {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return  全局支付参数配置
     */
    public GlobalPayConfigDTO getGlobalPayConfig(String platform) {
        String json = redisService.getCacheObject(StringUtils.format("{}:{}", CacheConstants.GLOBAL_PAY_CONFIG, platform));
        return JSON.parseObject(json, GlobalPayConfigDTO.class);
    }

    /**
     * 获取入驻商绑定的公众号appid
     * @return appid
     */
    public String getSupplierPublishAppid() {
        return redisService.getCacheObject(CacheConstants.SUPPLIER_PUBLISH_APPID);
    }

    /**
     * 获取入驻商绑定的公众号appid
     * @return appid
     */
    public String getSupplierPublishAppName() {
        return redisService.getCacheObject(CacheConstants.SUPPLIER_PUBLISH_APPAME);
    }

    /**
     * 获取入驻商绑定的公众号 app_secret
     * @return app_secret
     */
    public String getSupplierPublishAppSecret() {
        return redisService.getCacheObject(CacheConstants.SUPPLIER_PUBLISH_APP_SECRET);
    }

    /**
     * 获取参数
     * @param key
     * @return
     */
    public String get(String key) {
        return redisService.getCacheObject(key);
    }

    public String getAccessTokenUrl(String appid) {
        return redisService.getCacheObject(CacheConstants.WECHAT_APP_ACCESS_TOKEN_CENTER_URL + appid);
    }
}
