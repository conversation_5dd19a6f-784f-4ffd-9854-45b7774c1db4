<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zksr</groupId>
        <artifactId>zksr-common</artifactId>
        <version>3.6.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zksr-common-redis</artifactId>

    <description>
        zksr-common-redis缓存服务
    </description>

    <dependencies>

        <!-- SpringBoot Boot Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Core-->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-member</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-product</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-trade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-account</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.13.6</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-promotion</artifactId>
        </dependency>

    </dependencies>
</project>
