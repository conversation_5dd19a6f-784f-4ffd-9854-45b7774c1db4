-- 2025.1.24 入驻商订单优惠表新增字段
ALTER TABLE `zksr_trade`.`trd_order_discount_dtl`
    ADD COLUMN `discount_name` VARCHAR(64) DEFAULT NULL COMMENT '订单优惠名称' AFTER `discount_id`;
;

-- 2025.1.24 更新订单明细优惠数据字段
ALTER TABLE `zksr_trade`.`trd_order_discount_dtl`
    MODIFY COLUMN `activity_discount_amt` DECIMAL(12,2) DEFAULT 0 COMMENT '活动优惠金额(分摊的)(new)',
    MODIFY COLUMN `coupon_discount_amt` DECIMAL(12,2) DEFAULT 0 COMMENT '优惠金额(分摊的)(new)',
    MODIFY COLUMN `coupon_discount_amt2` DECIMAL(12,2) DEFAULT 0 COMMENT '优惠金额(不分摊的)(new)'
;
-- 修复订单明细优惠数据
UPDATE  `zksr_trade`.`trd_order_discount_dtl` SET  activity_discount_amt = 0 WHERE activity_discount_amt IS NULL;
UPDATE  `zksr_trade`.`trd_order_discount_dtl` SET  coupon_discount_amt = 0 WHERE coupon_discount_amt IS NULL;
UPDATE  `zksr_trade`.`trd_order_discount_dtl` SET  coupon_discount_amt2 = 0 WHERE coupon_discount_amt2 IS NULL;

-- 修复订单明细优惠数据 赠品优惠金额问题
UPDATE `zksr_trade`.`trd_order_discount_dtl` todd
    JOIN (
    SELECT
    todd.supplier_order_dtl_id,
    tsod.activity_discount_amt
    FROM
    `zksr_trade`.`trd_order_discount_dtl` todd
    LEFT JOIN `zksr_trade`.`trd_supplier_order_dtl` tsod ON todd.supplier_order_dtl_id = tsod.supplier_order_dtl_id
    WHERE
    todd.discount_type in ('BG', 'FG')
    AND (todd.activity_discount_amt IS NULL OR todd.activity_discount_amt <= 0)
    AND todd.gift_type = 0
    AND todd.supplier_order_dtl_id is NOT null
    ) updateTable ON todd.supplier_order_dtl_id = updateTable.supplier_order_dtl_id
    SET
        todd.activity_discount_amt = updateTable.activity_discount_amt
WHERE
    todd.discount_type in ('BG', 'FG')
  AND (todd.activity_discount_amt IS NULL OR todd.activity_discount_amt <= 0)
  AND todd.gift_type = 0
  AND todd.supplier_order_dtl_id is NOT null
;

-- 修复订单明细优惠数据 优惠名称（促销活动名称）
UPDATE `zksr_trade`.trd_order_discount_dtl todd
    JOIN (
    SELECT activity_id, activity_name FROM `zksr_promotion`.prm_activity
    ) updateTable  ON todd.discount_id = updateTable.activity_id
    SET
        todd.discount_name = updateTable.activity_name
WHERE
    todd.discount_type in ('BG', 'FG', 'SK', 'SP', 'FD', 'CB')
  AND updateTable.activity_name is not null
;

-- 修复订单明细优惠数据 优惠名称（优惠券）
UPDATE `zksr_trade`.trd_order_discount_dtl todd
    JOIN (
    SELECT pc.coupon_id, pct.coupon_name FROM `zksr_promotion`.prm_coupon pc
    LEFT JOIN `zksr_promotion`.prm_coupon_template pct ON pc.coupon_template_id = pct.coupon_template_id
    ) updateTable  ON todd.discount_id = updateTable.coupon_id
    SET
        todd.discount_name = updateTable.coupon_name
WHERE
    todd.discount_type in ('COUPON') AND updateTable.coupon_name is not null
;
-- 新人专区新增字典
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '新人首单', '3', 'activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2025-02-08 16:04:03', '', NULL, NULL);

