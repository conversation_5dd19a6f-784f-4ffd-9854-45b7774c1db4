
-- 门店充值方案
CREATE TABLE `zksr_account`.`acc_recharge_scheme` (
                                                      `recharge_scheme_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '充值方案id',
                                                      `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                                      `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                      `area_ids` text COMMENT '适用区域城市',
                                                      `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
                                                      `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
                                                      `rule_json` text COMMENT '充值金额，赠送金额',
                                                      `status` tinyint(4) DEFAULT '0' COMMENT '0-正常, 1-停用',
                                                      `scheme_name` varchar(32) DEFAULT NULL COMMENT '方案名称',
                                                      PRIMARY KEY (`recharge_scheme_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='储值套餐';
CREATE TABLE `zksr_account`.`acc_recharge_scheme_area` (
                                                           `recharge_scheme_area_id` bigint(20) NOT NULL COMMENT '主键',
                                                           `recharge_scheme_id` bigint(20) NOT NULL COMMENT '充值方案id',
                                                           `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                                           `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                           `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                           `area_id` bigint(20) DEFAULT NULL COMMENT '发布城市ID',
                                                           PRIMARY KEY (`recharge_scheme_area_id`),
                                                           KEY `idx_area_id` (`area_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='储值套餐上架发布城市';
ALTER TABLE `zksr_account`.`acc_account`
    MODIFY COLUMN `account_type` int(1) NULL DEFAULT NULL COMMENT '账户类型;0-储值账户 1-赠送余额' AFTER `update_time`;
ALTER TABLE `zksr_account`.`acc_account_flow` ADD COLUMN `account_type` int(1) NULL DEFAULT 0 COMMENT '账户类型;0-储值账户 1-赠送余额' AFTER `platform`;
ALTER TABLE `zksr_account`.`acc_account` MODIFY COLUMN `account_type` int(1) NULL DEFAULT 0 COMMENT '账户类型;0-储值账户 1-赠送余额' AFTER `update_time`;

-- 门店充值方案菜单权限
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('编辑', 2786, 2, 'cJsedlZpXOXBVKRwju', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:edit', '#', 'zksr', '2025-02-11 10:34:44', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('商户可结算', 2263, 6, 'DfqdkZirKOEKCzgasC', '2263', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:account:query-dc-settle-list', '#', 'zksr', '2025-02-27 18:59:25', '', NULL, '', 'partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('审核拒绝', 2822, 2, 'eTqAoZxhPgAuCDspjv', 'kTKWwmwemviGPWDxJT', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-branch:disable', '#', 'zksr', '2025-02-24 16:04:53', 'zksr', '2025-02-24 16:30:31', '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('提现单详情', 2822, 3, 'FPkhwXVIrToHZrJKbZ', 'kTKWwmwemviGPWDxJT', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-branch:query', '#', 'zksr', '2025-02-24 16:05:10', 'zksr', '2025-02-24 16:30:37', '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('新增提现', 2265, 0, 'JxnhcejVYeNgckDfHR', '2265', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-branch:add', '#', 'zksr', '2025-03-10 09:03:13', '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店提现申请', 2031, 11, 'kTKWwmwemviGPWDxJT', '2031', 'storeValuewithdrawal', 'operation/storeValuewithdrawal/index', NULL, 1, 0, 'C', '0', '0', 'account:withdraw-branch:list', '#', 'zksr', '2025-02-24 15:09:56', 'zksr', '2025-02-24 15:20:51', '', 'partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('客户储值流水', 2658, 10, 'NQSyXkUqsslVwXRGPV', 'ZfGDdvXtdyqxghIocd', 'storeValueCustomer', 'operation/storeValueCustomer/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2025-02-22 17:45:47', '', NULL, '', 'partner,dc,supplier');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('启用', 2786, 6, 'nssPcEiXBYfjDDftGg', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:enable', '#', 'zksr', '2025-02-11 10:35:50', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店充值', 2031, 10, 'qtnsnFVRVVDaPWZFkB', '2031', 'storeValueRecharge', 'operation/storeValueRecharge/index', NULL, 1, 0, 'C', '0', '0', 'account:recharge:list', '#', 'zksr', '2025-02-22 17:40:49', 'zksr', '2025-02-24 14:33:55', '', 'partner,dc,software');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('详情', 2786, 4, 'raHYbETKiqEuLsRxtz', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:query', '#', 'zksr', '2025-02-11 10:35:23', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('审核通过', 2822, 1, 'SdfRwuuSkeVDpxRGUE', 'kTKWwmwemviGPWDxJT', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-branch:enable', '#', 'zksr', '2025-02-24 16:04:33', 'zksr', '2025-02-24 16:30:25', '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店储值套餐', 2017, 6, 'sKoiWoAYxhxAcvhbZs', '2017', 'storedValue', 'operation/storedValue/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2025-02-11 10:34:04', 'zksr', '2025-02-12 16:03:53', '', 'partner,software,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('运营商其他配置', 2082, 7, 'smKWOoWMUTXsSlRmwv', '2082', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerPolicy:query-dc', '#', 'zksr', '2025-02-12 14:44:07', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('列表', 2786, 3, 'sTKsVOWVgAcHZhvxRJ', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:list', '#', 'zksr', '2025-02-11 10:34:59', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('直接结算', 2263, 7, 'TeOtTMcStsynbZvMhN', '2263', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-dc:direct-settle', '#', 'zksr', '2025-02-27 19:00:01', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('添加', 2786, 1, 'TgCGZTAaSstSAGmgFG', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:add', '#', 'zksr', '2025-02-11 10:34:26', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('停用', 2786, 5, 'uSypgjHQlRbMyhxYOP', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:disable', '#', 'zksr', '2025-02-11 10:35:39', '', NULL, '', 'software,partner,dc');
UPDATE `zksr_cloud`.`sys_menu` SET `perms` = 'member:branch:accountList' WHERE `menu_id` = 2265;


-- 账户充值记录增加赠送方案
ALTER TABLE `zksr_account`.`acc_recharge`
    ADD COLUMN `recharge_scheme_id` bigint(20) NULL COMMENT '充值赠送方案ID' AFTER `fee`,
    ADD COLUMN `scheme_rule_json` varchar(1024) NULL COMMENT '充值赠送方案JSON' AFTER `recharge_scheme_id`,
    ADD COLUMN `give_amt` decimal(10, 2) NULL COMMENT '赠送金额' AFTER `scheme_rule_json`;

-- 账户流水增加业务单号冗余
ALTER TABLE `zksr_account`.`acc_account_flow`
    ADD COLUMN `account_type` int(1) NULL COMMENT '账户类型;0-储值账户 1-赠送余额' AFTER `platform`;
ALTER TABLE `zksr_account`.`acc_account_flow`
    ADD COLUMN `memo` varchar(64) NULL COMMENT '流水变动备注' AFTER `account_type`,
    ADD COLUMN `busi_no` varchar(64) NULL COMMENT '流水变动业务单号' AFTER `memo`;

-- 账户流水索引优化
ALTER TABLE `zksr_account`.`acc_account_flow`
    ADD INDEX `idx_merchant_id`(`merchant_id`);

-- 字典维护
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('账户流水变动类型', 'account_flow_busi_type', '0', 'zksr', '2025-02-15 09:04:28', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('钱包充值支付平台', 'wallet_pay_platform', '0', 'zksr', '2025-02-11 15:29:49', '', NULL, '钱包充值支付平台');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店下单货到付款', 'branch_hdfk', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店下单货到付款下单', 'branch_hdfk_order', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '充值手续费补贴', 'recharge_fee', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '提现成功', 'withdraw_success', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '提现失败解除冻结', 'withdraw_fail_frozen', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '提现冻结', 'withdraw_frozen', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '修改授信金额', 'update_credit', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '账户转账', 'account_transfer', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店储值支付退款', 'branch_balance_refund', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店储值支付', 'branch_balance_pay', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商城创建订单解冻入驻商', 'supplier_create_order_cancel', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商城创建订单冻结入驻商', 'supplier_create_order', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店充值赠送', 'branch_recharge_give', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店充值', 'branch_recharge', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '入驻商充值', 'supplier_recharge', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '合利宝', 'hlb', 'wallet_pay_platform', NULL, 'default', 'N', '1', 'zksr', '2025-02-11 15:30:00', '', NULL, '合利宝支付有商户问题需要处理');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '微信B2B', 'wxb2b', 'wallet_pay_platform', NULL, 'default', 'N', '0', 'zksr', '2025-02-11 15:30:00', '', NULL, NULL);

-- 提现凭证
ALTER TABLE `zksr_account`.`acc_withdraw`
    ADD COLUMN `voucher` varchar(255) NULL COMMENT '提现凭证' AFTER `transfer_amt`;
ALTER TABLE `zksr_account`.`acc_withdraw`
    ADD COLUMN `apply_tip` varchar(64) NULL COMMENT '申请备注' AFTER `voucher`;

ALTER TABLE `zksr_account`.`acc_account_flow`
    ADD INDEX `idx_busi_id`(`busi_id`);

-- 支付请求信息
ALTER TABLE `zksr_account`.`acc_pay_flow` ADD COLUMN `req_info` varchar(512) NULL;



-- 对接第三方 新增推送门店储值充值、提现信息
-- 数据字典
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '推送B2B门店储值数据', '505', 'log_request_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-10 17:06:32', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '推送B2B门店储值数据', '505', 'send_model_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-10 17:06:54', '', NULL, NULL);


-- 新增ERP11.0 门店储值模板
INSERT INTO `zksr_cloud`.`visual_setting_template`(`visual_template_id`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `template_name`, `api_template`, `template_type`, `source_type`) VALUES (591774711751278592, 'zksr', '2025-03-10 17:08:07.030', 'zksr', '2025-03-11 18:21:55.062', 1, 'ERP11.0门店储值充值/提现信息同步', '{\n    \"dcBranchNo\": \"${sendCode}\",\n    \"branchNo\": \"${branchNo}\", \n    ## 提现\n     #if($busiType == \"withdraw_success\")\n     \"type\": \"1\",\n     ## 充值\n     #elseif($busiType == \"branch_recharge\")\n     \"type\": \"0\",\n      #else\n      \"type\": \"\",\n      #end\n    ## 金额\n    \"amt\": \"${totaltAmt}\",\n    ## 赠送金额\n    \"credits\":\"${giveAmt}\"\n}', 505, 2);
INSERT INTO `zksr_cloud`.`visual_setting_detail`(`visual_detail_id`, `sys_code`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `visual_master_id`, `visual_template_id`, `template_type`, `req_type`, `api_url`, `content_type`, `resp_name`, `resp_code`, `debug_result_status`, `debug_result_message`, `memo`, `resp_data`, `resp_msg`, `visual_receipt_type`) VALUES (591782915139010560, NULL, 'zksr', '2025-03-10 17:39:57.154', NULL, NULL, 1, 511521817490620416, 591774711751278592, 505, 'POST', 'syncStoredPayInformation.action', 'application/json', 'responseCode', '200', NULL, NULL, NULL, 'responseMessage', 'responseMessage', '');

-- 修改ERP 订单、售后单推送模板
UPDATE `zksr_cloud`.`visual_setting_template` SET api_template = '{\n    \"orderNo\": \"${supplierOrderNo}\",\n    \"branchNo\": \"${branchNo}\",  \n    \"dcBranchNo\": \"${sendCode}\",   \n    \"totalAmt\": \"${subOrderAmt}\",   \n    \"realAmt\": \"${subPayAmt}\",   \n    \"discountAmt\":\"${subDiscountAmt}\",  \n     #if(!\"${memo}\")\n    \"memo\": \"${memo}\",   \n    #else\n     \"memo\": \"\",\n      #end\n    \"distributionType\":\"1\",  \n    #if($payWay == 0)\n    \"payWay\": \"1\",\n    #elseif($payWay == 2)\n    \"payWay\": \"0\",\n    #elseif($payWay == 1)\n    \"payWay\": \"2\",  \n    #else\n    \"payWay\": \"\",\n    #end\n    \"detailList\": [\n        #foreach( $sub in $detailList)\n        {\n            #set($qty = $sub.orderUnitQty)\n            #set($size = $sub.orderUnitSize)\n            #set($minDetailQty = $qty * $size)\n            \"itemNo\": \"${sub.itemSourceNo}\",\n            \"saleQty\": $minDetailQty,\n            \"itemAmt\": \"${sub.subOrderAmt}\",\n            \"itemRealAmt\": \"${sub.totalAmt}\",\n            \"discountAmount\": \"${sub.realTotalCouponAmt}\",  \n            \"giftFlag\": \"${sub.giftFlag}\",\n            \"itemFlag\": \"0\",   \n            \"line\": \"${sub.lineNum}\",\n             \"outCountPriority\":${sub.orderUnitType},\n              \"orderUnitSize\":\"${sub.orderUnitSize}\"\n        }#if($foreach.hasNext),#end\n        #end\n    ]\n}' WHERE template_name = 'ERP11.0销售订单同步';
UPDATE `zksr_cloud`.`visual_setting_template` SET api_template = '{\n    \"orderNo\": \"${supplierAfterNo}\",\n    \"branchNo\": \"${branchNo}\",\n    \"dcBranchNo\": \"${sendCode}\",\n    \"orderNoDate\": \"${createTimeString}\", \n     #if(!\"${memo}\")\n    \"memo\": \"${memo}\",   \n    #else\n     \"memo\": \"\",\n      #end     \n    ## 支付类型 1是储值  5线上支付/货到付款\n     #if($payWay == \"1\")\n     \"refundType\": \"1\",\n      #else\n      \"refundType\": \"5\",\n      #end \n    \"detailList\": [\n        #foreach( $sub in $detailList)\n        {\n            #set($price = $sub.exactReturnPrice)\n            \"itemNo\": \"${sub.itemSourceNo}\",\n            \"itemQty\":  ${sub.returnQty},\n            \"itemPrice\": ${price},\n            \"itemAmt\": ${sub.exactReturnAmt},\n            \"line\": ${sub.lineNum}\n        }#if($foreach.hasNext),#end\n        #end\n    ]\n}' WHERE template_name = 'ERP11.0售后订单同步';


-- 订单表增加字段
ALTER TABLE `zksr_trade`.`trd_order`
    ADD COLUMN `cz_principal_rate` DECIMAL(12,6) DEFAULT 0 COMMENT '储值本金比率' AFTER `order_amt`,
    ADD COLUMN `cz_principal_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值本金支付金额' AFTER `cz_principal_rate`,
    ADD COLUMN `cz_give_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值赠金支付金额' AFTER `cz_principal_pay_amt`
;
-- 订单明细结算表增加字段
ALTER TABLE `zksr_trade`.`trd_supplier_order_settle`
    ADD COLUMN `cz_principal_rate` DECIMAL(12,6) DEFAULT 0 COMMENT '储值本金比率' AFTER `item_amt`,
    ADD COLUMN `cz_principal_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值本金支付金额' AFTER `cz_principal_rate`,
    ADD COLUMN `cz_give_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值赠金支付金额' AFTER `cz_principal_pay_amt`,
    ADD COLUMN `return_cz_principal_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值本金已退款金额' AFTER `cz_give_pay_amt`,
    ADD COLUMN `return_cz_give_pay_amt` DECIMAL(12,6) DEFAULT 0 COMMENT '储值赠金已退款金额' AFTER `return_cz_principal_pay_amt`
;

-- 售后表增加字段
ALTER TABLE `zksr_trade`.`trd_after`
    ADD COLUMN `return_cz_principal_rate` DECIMAL(12,6) DEFAULT 0 COMMENT '储值本金比率' AFTER `return_discount_amt`,
    ADD COLUMN `return_cz_principal_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值本金支付金额' AFTER `return_cz_principal_rate`,
    ADD COLUMN `return_cz_give_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值赠金支付金额' AFTER `return_cz_principal_pay_amt`
;

-- 售后明细结算表增加字段
ALTER TABLE `zksr_trade`.`trd_supplier_after_settle`
    ADD COLUMN `return_cz_principal_rate` DECIMAL(12,6) DEFAULT 0 COMMENT '储值本金比率' AFTER `return_amt`,
    ADD COLUMN `return_cz_principal_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值本金支付金额' AFTER `return_cz_principal_rate`,
    ADD COLUMN `return_cz_give_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值赠金支付金额' AFTER `return_cz_principal_pay_amt`
;
-- 新增订单支付方式【储值支付】字典
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '储值支付', '1', 'sys_order_pay_way', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '储值支付');
