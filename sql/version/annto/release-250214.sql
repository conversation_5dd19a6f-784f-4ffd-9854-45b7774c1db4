-- 搜索推荐
ALTER TABLE `zksr_cloud`.`sys_partner_policy` MODIFY COLUMN policy_key varchar(255);
ALTER TABLE `zksr_product`.`prdt_keywords`
    ADD COLUMN `del_flag` char(1) CHARACTER SET utf8mb4 DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）';

DELETE FROM  `zksr_cloud`.`sys_menu` where menu_name ='搜索配置';
DELETE FROM  `zksr_cloud`.`sys_menu` where menu_name ='搜索日志';
DELETE FROM  `zksr_cloud`.`sys_menu` where menu_name ='关键词库';
DELETE FROM  `zksr_cloud`.`sys_menu` where menu_name ='推荐管理';
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '一键加入词库', 2783, 1, 'FvZHcgBBTtcvXucoGe', 'vcIkWQGVegjEdtvaRt', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:searchHis:add', '#', 'zksr', '2025-02-12 16:45:05', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '编辑', 2781, 1, 'SIpWHEQwcFaevOHjOY', 'ZvSyZWIESCrIoyZpyr', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerPolicy:edit', '#', 'zksr', '2025-02-08 11:30:50', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '搜索配置', 2730, 3, 'ZvSyZWIESCrIoyZpyr', 'ZcZcfROKeQBpLJYidO', 'searchConfiguration', 'platform/searchConfiguration/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2025-01-18 15:07:36', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '搜索日志', 2784, 2, 'lbqefuasEpRaYOPZji', 'ZcZcfROKeQBpLJYidO', 'searchLogings', 'platform/searchLogings/index', NULL, 1, 0, 'C', '0', '0', 'member:searchHis:list', '#', 'zksr', '2025-01-18 15:06:40', 'zksr', '2025-02-12 16:39:58', '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '关键词库', 2784, 1, 'vcIkWQGVegjEdtvaRt', 'ZcZcfROKeQBpLJYidO', 'keywordLibrary', 'platform/keywordLibrary/index', NULL, 1, 0, 'C', '0', '0', 'product:keywords:list', '#', 'zksr', '2025-01-18 15:05:10', 'zksr', '2025-02-13 15:30:52', '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '推荐管理', 0, 22, 'ZcZcfROKeQBpLJYidO', '0', 'recommend', NULL, NULL, 1, 0, 'M', '0', '0', NULL, '#', 'zksr', '2025-01-18 15:03:51', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '删除', 2781, 2, 'CuSNlDoOFjZPTEywnh', 'ZvSyZWIESCrIoyZpyr', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerPolicy:remove', '#', 'zksr', '2025-02-13 16:07:24', '', NULL, '', 'partner');
-- 预设时间展示模版
ALTER TABLE `zksr_cloud`.`sys_pages_config`
    ADD COLUMN `type` tinyint(2) NULL DEFAULT 0 COMMENT '0-固定模版, 1-时效模版' AFTER `url_dtl`,
    ADD COLUMN `start_time` datetime DEFAULT '2024-01-01 00:00:00' COMMENT '开始展示时间' AFTER `type`,
    ADD COLUMN `end_time` datetime DEFAULT '2199-01-01 00:00:00' COMMENT '结束展示时间' AFTER `start_time`;