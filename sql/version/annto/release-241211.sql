-- 优惠券增加黑白名单
ALTER TABLE `zksr_promotion`.`prm_coupon_scope_apply` ADD COLUMN `white_or_black` tinyint(1) NULL DEFAULT 1 COMMENT '1-白名单 0-黑名单';
ALTER TABLE `zksr_promotion`.`prm_activity_branch_scope` ADD COLUMN `white_or_black` tinyint(1) NULL DEFAULT 1 COMMENT '1-白名单 0-黑名单';
ALTER TABLE `zksr_promotion`.`prm_activity_city_scope` ADD COLUMN `white_or_black` tinyint(1) NULL DEFAULT 1 COMMENT '1-白名单 0-黑名单';
ALTER TABLE `zksr_promotion`.`prm_activity_channel_scope` ADD COLUMN `white_or_black` tinyint(1) NULL DEFAULT 1 COMMENT '1-白名单 0-黑名单';
ALTER TABLE `zksr_promotion`.`prm_activity_spu_scope` ADD COLUMN `white_or_black` tinyint(1) NULL DEFAULT 1 COMMENT '1-白名单 0-黑名单';

-- 开放接口 新增 订单发货前取消 接口  增加相应配置
-- MQ： application-rocketmq-system-dev.yml  新增配置
-- 新增 definition:
openapi_order_cancel

-- 新增 bindings:
#对外接口:订单发货前取消
        openapi_order_cancel-out-0:
          destination: openapi_order_cancel
        openapi_order_cancel-in-0:
          destination: openapi_order_cancel
          group: openapi_order_cancel_group

-- 增加开放配置SQL
INSERT INTO `zksr_cloud`.`sys_openability`(`openability_id`, `create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES (487846016408055555, 'zksr', '2024-06-03 15:31:42.308', 'zksr', '2024-06-09 09:13:42.850', 0, 'supplier', '订单发货前取消', 'orderCancel', '0', 10);

-- 对外开放能力新增配置 赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）
ALTER TABLE `zksr_cloud`.`sys_opensource`
    ADD COLUMN `gift_price_type` tinyint(1) DEFAULT 0 COMMENT '赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）';

ALTER TABLE `zksr_cloud`.`sys_opensource`
    ADD COLUMN `order_merge_flag` tinyint(1) DEFAULT 0 COMMENT '订单同步是否合单（0否，1是）';

-- 赠品取价算法 新增数据字典配置
INSERT INTO `zksr_cloud`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (102, '赠品取价算法', 'gift_price_type', '0', 'zksr', '2024-12-09 10:53:30', '', NULL, '对接第三方使用');

INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (563, 0, '零价', '0', 'gift_price_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-09 11:18:18', '', NULL, '默认零价');
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (564, 0, '均价', '1', 'gift_price_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-09 11:18:25', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (565, 0, '分摊价', '2', 'gift_price_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-09 11:18:33', '', NULL, NULL);



-- 平台商首页状态权限调整
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2661, '获得平台页面配置模版详情', 2613, 5, 'DQAwGMFsQftBxgBrJO', '2613', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:pageConfigTemplate:query', '#', 'zksr', '2024-12-05 09:29:10', '', NULL, '', 'partner');

-- 优惠券领取方式字典 新增业务员发券、批次发券
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '业务员发券', '5', 'coupon_receive_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-05 14:42:44', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '批次发券', '6', 'coupon_receive_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-05 14:43:09', '', NULL, NULL);

-- 新增优惠券批量发送表和批量发送详情表
CREATE TABLE `zksr_promotion`.`prm_coupon_batch` (
`coupon_batch_id` bigint NOT NULL COMMENT '优惠券批量发送id',
`sys_code` bigint DEFAULT NULL COMMENT '平台商id',
`create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
`create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
`update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
`func_scope` tinyint NOT NULL COMMENT '全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定）',
`update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
`coupon_template_qty` int DEFAULT NULL COMMENT '优惠券模板数量',
`branch_qty` int DEFAULT NULL COMMENT '门店数量',
`total_qty` int DEFAULT NULL COMMENT '总计发券数量',
`valid_type` tinyint(1) DEFAULT NULL COMMENT '生效类型：0-定时生效，1-立即生效',
`valid_time` datetime DEFAULT NULL COMMENT '生效时间',
`real_send_qty` int DEFAULT NULL COMMENT '实际发券成功数量',
`task_execute_status` tinyint(1) DEFAULT NULL COMMENT '执行状态 0-未执行，1-已执行',
`audit_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态 0-未审核，1-已审核',
`del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
`remark` varchar(255) DEFAULT NULL COMMENT '备注',
PRIMARY KEY (`coupon_batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券批量发送';

CREATE TABLE `zksr_promotion`.`prm_coupon_batch_dtl` (
 `prm_coupon_batch_dtl_id` bigint NOT NULL COMMENT '优惠券批量发送详情id',
 `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
 `batch_coupon_id` bigint NOT NULL COMMENT '优惠券批量发送id',
 `branch_id` bigint DEFAULT NULL COMMENT '门店id',
 `coupon_template_id` bigint DEFAULT NULL COMMENT '优惠券模板',
 `scope_type` tinyint(1) DEFAULT NULL COMMENT '0-优惠券模板 1-门店',
 PRIMARY KEY (`prm_coupon_batch_dtl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券批量发送详情';


-- 平台商门店修改权限
UPDATE `zksr_cloud`.`sys_menu` SET `menu_name` = '门店信息修改', `parent_id` = 2193, `order_num` = 3, `menu_code` = '2213', `menu_pcode` = '2193', `path` = '#', `component` = '', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'member:branch:edit', `icon` = '#', `create_by` = 'admin', `create_time` = '2024-03-21 11:16:30', `update_by` = 'zksr', `update_time` = '2024-12-05 10:05:53', `remark` = '', `func_scop` = 'dc,partner' WHERE `menu_code` = '2213';

-- 消息中心调整
ALTER TABLE `zksr_cloud`.`sys_message_template` ADD COLUMN `push_mode` tinyint(1) NULL COMMENT '0-小程序,1-公众号,2-微信门店助手,3-用户APP站内' AFTER `receive_merchant`;

-- 增加业务员公众号openid配置
ALTER TABLE `zksr_member`.`mem_colonel` ADD COLUMN `publish_openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公众号openid' AFTER `three_area_city_id`;

-- 订单查询新增本地订单发货收货按钮权限
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`)
VALUES ('入驻商订单发货出库', 2262, 1, 'JFLNLnZmDORWuhUDOV', '2262', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierOrder:outbound', '#', 'zksr', '2024-12-05 09:42:32', '', NULL, '', 'supplier');
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`)
VALUES ('入驻商订单收货', 2262, 1, 'SROMUHRczOJapLwzFo', '2262', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierOrder:takeDelivery', '#', 'zksr', '2024-12-05 09:43:04', '', NULL, '', 'supplier');

-- 订单明细增加赠品分摊价字段
ALTER TABLE `zksr_trade`.`trd_supplier_order_dtl`
ADD COLUMN `exact_gift_share_price` DECIMAL(18,6) DEFAULT 0 COMMENT '赠品优惠分摊单价（6位小数）',
ADD COLUMN `gift_share_subtotal_amt` DECIMAL(18,6) DEFAULT 0 COMMENT '赠品分摊价小计',
ADD COLUMN `after_gift_share_price` DECIMAL(18,6) DEFAULT 0 COMMENT '均摊赠品优惠后的成交价（6位小数）',
ADD COLUMN `after_gift_share_unit_price` DECIMAL(18,6) DEFAULT 0 COMMENT '均摊赠品优惠后的购买单位成交价（6位小数）',
ADD COLUMN `after_gift_share_subtotal_amt` DECIMAL(18,6) DEFAULT 0 COMMENT '均摊赠品优惠后的小计',
ADD COLUMN `sku_avg_price` DECIMAL(18,6) DEFAULT 0 COMMENT 'SKU最小单位数量平均单价（6位小数）',
ADD COLUMN `res_price` DECIMAL(18,6) DEFAULT 0 COMMENT '赠品取价算法- 对应最终单价 （6位小数）',
ADD COLUMN `res_unit_price` DECIMAL(18,6) DEFAULT NULL COMMENT '赠品取价算法- 对应最终购买单位单价 （6位小数）',
ADD COLUMN `res_amt` DECIMAL(12,2) DEFAULT 0 COMMENT '赠品取价算法- 对应最终金额',
ADD COLUMN `gift_price_type` TINYINT(1) DEFAULT 0 COMMENT '赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）'
;

-- 订单优惠明细增加赠品参与商品行号字段
ALTER TABLE `zksr_trade`.`trd_order_discount_dtl`
ADD COLUMN `order_dtl_num_str` text DEFAULT NULL COMMENT '赠品优惠时使用，存放参与此赠品优惠的订单明细编号对应的行号，多个以;分号分隔'
;



-- 消息日志
CREATE TABLE `zksr_cloud`.`sys_message_log` (
`msg_id` bigint(20) NOT NULL,
`sys_code` bigint(20) DEFAULT NULL COMMENT '平台商ID',
`create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
`create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
`update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
`update_time` datetime(3) DEFAULT NULL COMMENT '最后修改时间',
`message_template_id` bigint(20) DEFAULT NULL COMMENT '消息模版ID',
`merchant_id` bigint(20) DEFAULT NULL COMMENT '接受商户ID',
`merchant_type` varchar(32) DEFAULT NULL COMMENT '接受商户类型',
`state` tinyint(1) DEFAULT NULL COMMENT '0-未发送, 1-成功, 2-失败',
`content` varchar(1024) DEFAULT NULL COMMENT '消息内容',
`path` varchar(255) DEFAULT NULL COMMENT '跳转路径',
`tips` varchar(255) DEFAULT NULL COMMENT '消息备注',
PRIMARY KEY (`msg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息发送记录';

-- 消息模版时间异常调整
ALTER TABLE `zksr_cloud`.`sys_message_template` MODIFY COLUMN `create_time` datetime NOT NULL COMMENT '创建时间' AFTER `create_by`;

-- 消息模版设置
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2673, '消息设置', 2021, 6, 'fNuhYtHNGmDJnlQshP', '2021', 'messageSetting', 'platform/messageSetting/index', NULL, 1, 0, 'C', '0', '0', 'system:subscribe-template:list', '#', 'zksr', '2024-12-06 15:35:36', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2672, '消息模版详情', 2673, 6, 'syhNhMntgkLJhTCstL', 'fNuhYtHNGmDJnlQshP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:subscribe-template:query', '#', 'zksr', '2024-12-06 15:30:06', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2671, '删除消息模版', 2673, 5, 'SgJaFHlFwyxaqLgiZO', 'fNuhYtHNGmDJnlQshP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:subscribe-template:remove', '#', 'zksr', '2024-12-06 15:29:54', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2670, '启用模版', 2673, 4, 'AalEQtEzjEOgmcbqZd', 'fNuhYtHNGmDJnlQshP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:subscribe-template:enable', '#', 'zksr', '2024-12-06 15:29:33', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2669, '停用模版', 2673, 3, 'VUTrJtqyPJRfIqKqhI', 'fNuhYtHNGmDJnlQshP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:subscribe-template:disable', '#', 'zksr', '2024-12-06 15:29:18', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2668, '修改消息模版', 2673, 2, 'xQHnTqwreAzrbxmOph', 'fNuhYtHNGmDJnlQshP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:subscribe-template:edit', '#', 'zksr', '2024-12-06 15:29:05', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2667, '新增消息模版', 2673, 1, 'qVKObGOLycboSNmVcs', 'fNuhYtHNGmDJnlQshP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:subscribe-template:add', '#', 'zksr', '2024-12-06 15:28:51', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2666, '消息模版分页列表', 2673, 6, 'WQaIjTffTKUkKNQzjT', 'fNuhYtHNGmDJnlQshP', '#', NULL, NULL, 1, 0, 'C', '0', '0', 'system:subscribe-template:list', '#', 'zksr', '2024-12-06 15:28:24', '', NULL, '', 'partner');


-- 消息模版字典
DELETE FROM `zksr_cloud`.sys_dict_type WHERE dict_type like 'message_scene_%';
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('用户下单通知', 'message_scene_0', '0', 'zksr', '2024-06-12 19:06:01', 'zksr', '2024-12-06 17:07:03', '公众号消息_下单通知参数项');
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('订单配送通知', 'message_scene_1', '0', 'zksr', '2024-06-13 08:59:59', 'zksr', '2024-12-06 17:05:31', '公众号消息_配送通知参数项,仅本地配送商品');
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('退货完成通知', 'message_scene_11', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '退货退款, 或者完成通知');
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('门店注册成功', 'message_scene_12', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '门店注册成功同志');
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('订货提醒', 'message_scene_13', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '门店客户长期未下单通知');
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('促销活动提醒', 'message_scene_14', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '促销活动开始前有指定范围通知');
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('门店订单发货(本地)', 'message_scene_2', '0', 'zksr', '2024-07-31 11:29:02', 'zksr', '2024-12-06 17:05:06', '门店订单发货通知,由微信商家助手发送');
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('待付款提醒', 'message_scene_3', '0', 'zksr', '2024-12-06 17:07:59', '', NULL, '订单待付款提醒通知');
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('订单取消通知', 'message_scene_4', '0', 'zksr', '2024-12-07 14:30:24', '', NULL, '订单未付款取消通知');
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('订单发货通知(全国)', 'message_scene_6', '0', 'zksr', '2024-12-07 14:30:43', '', NULL, '订单发货通知');
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('订单收货通知', 'message_scene_7', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '订单收货通知');
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('退货申请', 'message_scene_8', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '售后退款申请成功');
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('退货申请审核', 'message_scene_9', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '退货审核通过, 或者失败结果');

DELETE FROM `zksr_cloud`.sys_dict_data WHERE dict_type like 'message_scene_%';
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '用户下单通知', '0', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:04:22', 'zksr', '2024-12-06 16:51:15', '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '订单配送通知', '1', 'message_scene', NULL, 'default', 'N', '1', 'zksr', '2024-06-12 19:04:33', 'zksr', '2024-12-06 17:30:43', '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '订单号', 'orderNo', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:06:13', 'zksr', '2024-06-14 16:30:44', 'XS2403319300001299511');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '下单用户', 'memberName', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:07:00', 'zksr', '2024-06-14 16:30:52', '张三');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '下单用户手机号', 'memberPhone', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:07:21', 'zksr', '2024-06-14 16:31:00', '15580493955');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '门店名称', 'branchName', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:07:43', 'zksr', '2024-06-14 16:31:12', '张三的小店');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '门店地址', 'branchAddr', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:03', 'zksr', '2024-06-14 16:31:27', '长沙市雨花区黄土岭101');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (99, '自定义1', 'diy1', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:17', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (99, '自定义2', 'diy2', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:24', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (99, '自定义3', 'diy3', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:31', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '商品名称', 'spuName', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:09:13', 'zksr', '2024-06-14 16:31:37', '西瓜*1,茄子*2');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '订单金额', 'payAmt', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:10:58', 'zksr', '2024-06-14 16:31:42', '99.00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '订单金额', 'payAmt', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:10:58', 'zksr', '2024-06-14 16:33:57', '99.00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '商品名称', 'spuName', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:09:13', 'zksr', '2024-06-14 16:33:47', '西瓜*1,茄子*2');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (99, '自定义3', 'diy3', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:31', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (99, '自定义2', 'diy2', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:24', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (99, '自定义1', 'diy1', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:17', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '门店地址', 'branchAddr', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:03', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '门店名称', 'branchName', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:07:43', '', NULL, '张三的小店');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '下单用户手机号', 'memberPhone', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:07:21', '', NULL, '15580493955');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '下单用户', 'memberName', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:07:00', '', NULL, '张三');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '订单号', 'orderNo', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:06:13', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '下单时间', 'orderCreateTime', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-13 15:02:45', 'zksr', '2024-06-14 16:31:53', '2024-06-14 16:31:42');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, '入驻商名称', 'supplierName', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-13 15:03:48', 'zksr', '2024-06-14 16:34:19', '华农商行XX有限公司');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '入驻商电话', 'supplierContactPhone', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-13 15:04:02', 'zksr', '2024-06-14 16:34:02', '15580493955');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (10, '订单创建时间', 'orderCreateTime', 'message_scene_1', '', 'default', 'N', '0', 'zksr', '2024-06-13 15:04:40', 'zksr', '2024-06-14 16:34:26', '2024-06-14 16:31:42');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '门店订单发货(本地)', '2', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-07-31 11:27:26', 'zksr', '2024-12-06 16:52:26', '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品名称X数量', 'spuName', 'message_scene_2', NULL, 'default', 'N', '0', 'zksr', '2024-07-31 11:30:14', 'zksr', '2024-07-31 14:04:11', '西瓜*1,茄子*2');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单金额', 'payAmt', 'message_scene_2', NULL, 'default', 'N', '0', 'zksr', '2024-07-31 11:30:29', '', NULL, '99.00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商家名称', 'supplierName', 'message_scene_2', NULL, 'default', 'N', '0', 'zksr', '2024-07-31 11:31:03', '', NULL, '华农商行XX有限公司');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '待付款提醒', '3', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:51:57', 'zksr', '2024-12-06 16:52:02', '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '订单取消通知', '4', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:52:43', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '订单发货通知(全国)', '6', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:53:04', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '订单收货通知', '7', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:53:16', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '退货申请', '8', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:53:30', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, '退货申请审核', '9', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:53:59', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (11, '退货完成通知', '11', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:54:25', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (12, '门店注册成功', '12', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:54:48', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"1\": \"colonel\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (13, '订货提醒', '13', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:55:17', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (14, '活动通知', '14', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:55:57', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '订单编号', 'orderNo', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '商品名称', 'spuName', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '订单金额', 'payAmt', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:02', '', NULL, '99.00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '订单备注', 'orderMemo', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '帮我送上楼');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '下单时间', 'orderCreateTime', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, '2024-06-14 16:31:42');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '门店名称', 'branchName', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '自定义1', 'diy1', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '自定义2', 'diy2', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '订单编号', 'orderNo', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '商品名称', 'spuName', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '订单金额', 'payAmt', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:02', '', NULL, '99.00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '订单备注', 'orderMemo', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '帮我送上楼');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '下单时间', 'orderCreateTime', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, '2024-06-14 16:31:42');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '门店名称', 'branchName', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '自定义1', 'diy1', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '自定义2', 'diy2', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, '取消时间', 'orderCancelTime', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '订单取消时间');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (10, '门店地址', 'branchAddr', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单编号', 'orderNo', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品名称', 'spuName', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单金额', 'payAmt', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:02', '', NULL, '99.00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单备注', 'orderMemo', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '帮我送上楼');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '下单时间', 'orderCreateTime', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, '2024-06-14 16:31:42');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店名称', 'branchName', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店地址', 'branchAddr', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单发货时间', 'orderDeliveryTime', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单快递公司', 'orderExpressName', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单快递单号', 'orderExpressNo', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '156415646546');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单编号', 'orderNo', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品名称', 'spuName', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单金额', 'payAmt', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:02', '', NULL, '99.00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单备注', 'orderMemo', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '帮我送上楼');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '下单时间', 'orderCreateTime', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, '2024-06-14 16:31:42');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店名称', 'branchName', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店地址', 'branchAddr', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单收货时间', 'orderReceiveTime', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单编号', 'orderNo', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品名称', 'spuName', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货备注', 'afterMemo', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '我不想要了');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货申请单号', 'afterNo', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, 'SH21123131321');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店名称', 'branchName', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货申请时间', 'afterCreateTime', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店地址', 'branchAddr', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单编号', 'orderNo', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品名称', 'spuName', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货备注', 'afterMemo', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '我不想要了');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货申请单号', 'afterNo', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, 'SH21123131321');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店名称', 'branchName', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货申请时间', 'afterCreateTime', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店地址', 'branchAddr', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货审核状态', 'afterAuditStatus', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '审核成功');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货审核备注', 'afterAuditMemo', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '超过7天不予退货');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单编号', 'orderNo', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品名称', 'spuName', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货备注', 'afterMemo', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '我不想要了');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货申请单号', 'afterNo', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, 'SH21123131321');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店名称', 'branchName', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货完成时间', 'afterFinishTime', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店地址', 'branchAddr', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退款金额', 'afterRefundAmt', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '199.00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店名称', 'branchName', 'message_scene_12', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_12', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_12', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店注册时间', 'branchCreateTime', 'message_scene_12', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店地址', 'branchAddr', 'message_scene_12', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店手机号', 'branchContractPhone', 'message_scene_12', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '15580493955');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_13', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_13', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义3', 'diy3', 'message_scene_13', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义4', 'diy4', 'message_scene_13', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义3', 'diy3', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义4', 'diy4', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '促销名称', 'activityName', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '梨子秒杀');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '促销开始时间', 'activityStartTime', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '促销结束时间', 'activityEndTime', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');

-- 业务员决策数据
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('业务员决策数据', 2331, 9, 'kTUvwTnxFrQWwgvrrq', '2331', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:baseReport', '#', 'zksr', '2024-12-10 09:08:34', '', NULL, '', 'colonel');


-- 业务员日结、月结 表 平台商字段长度调整
ALTER TABLE `zksr_member`.`mem_colonel_day_settle` MODIFY COLUMN `sys_code` BIGINT(20) NOT NULL COMMENT '平台商id';
ALTER TABLE `zksr_member`.`mem_colonel_month_settle` MODIFY COLUMN `sys_code` BIGINT(20) NOT NULL COMMENT '平台商id';

-- 新增业务员发券额度表
CREATE TABLE `zksr_promotion`.`prm_coupon_colonel_quota` (
`coupon_colonel_quota_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '业务员发券额度ID',
`sys_code` bigint(20) DEFAULT NULL COMMENT '平台商ID',
`create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime DEFAULT NULL COMMENT '更新时间',
`colonel_id` bigint(20) DEFAULT NULL COMMENT '业务员ID',
`quota_type` int(1) DEFAULT NULL COMMENT '0-模板额度 1-实例额度',
`quota` decimal(12,2) DEFAULT NULL COMMENT '额度',
`finish_quota` decimal(12,2) DEFAULT NULL COMMENT '已使用额度，仅1-实例额度',
`month_id` int(6) DEFAULT NULL COMMENT '月份ID，仅1-实例额度',
PRIMARY KEY (`coupon_colonel_quota_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='业务员发券额度表';

-- 业务员发券额度 门店月销售汇总菜单
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店月销售汇总', 2658, 7, 'HlWbcGAdUMetAHaiYS', 'ZfGDdvXtdyqxghIocd', 'BranchSaleSummary', 'finance/branchSaleSummary/index', NULL, 1, 0, 'C', '0', '0', '', 'education', 'zksr', NOW(), 'zksr', NOW(), '', 'partner,supplier,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('查看历史发券额度', 2674, 2, 'lbGhiWQNFmJQdEgxha', 'BXULUOBHzMcpRiROUI', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:quota:history', '#', 'zksr', NOW(), '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('业务员发券调整额度', 2674, 1, 'EhmMGADBATqVJzGkZc', 'BXULUOBHzMcpRiROUI', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:quota:adjust', '#', 'zksr', NOW(), '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('业务员发券额度管理', 2027, 8, 'BXULUOBHzMcpRiROUI', '2027', 'CouponQuotaManage', 'operation/promotion/couponQuotaManage/index', NULL, 1, 0, 'C', '0', '0', 'promotion:quota:list', 'button', 'zksr', NOW(), '', NULL, '', 'partner');

-- 业务员APP 发券权限和查看优惠券权限
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('业务员主动发券', 2331, 11, 'SsPpIVApYLnGulMAQo', '2331', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:salesmanIssueCoupons', '#', 'zksr', '2024-12-11 15:47:42', '', NULL, '', 'colonel');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('业务员查看优惠券，查看业务员发券额度，查看业务员发券管理', 2331, 10, 'yqQLKaxZHHIfCJTvDn', '2331', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:viewCoupons', '#', 'zksr', NOW(), '', NULL, '', 'colonel');


-- 发券菜单
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('删除优惠券批量发送', 2685, 4, 'EMPNZlYTHlNHrNfiVt', 'XMXoTrNcVajRaraFFb', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:remove', '#', 'zksr', '2024-12-11 17:46:56', '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('获得优惠券批量发送详情', 2685, 5, 'dNixHYtyqYWPojGvdk', 'XMXoTrNcVajRaraFFb', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:query', '#', 'zksr', '2024-12-11 11:08:20', '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('批量审核优惠券批量发送', 2685, 3, 'hLAGtwocGeBkRxeFia', 'XMXoTrNcVajRaraFFb', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:audit', '#', 'zksr', '2024-12-11 11:07:40', '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('修改优惠券批量发送', 2685, 2, 'KcEmQoFuDntcACrlEW', 'XMXoTrNcVajRaraFFb', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:edit', '#', 'zksr', '2024-12-11 11:06:36', '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('新增优惠券批量发送', 2685, 1, 'iRkpVJxgPnSDBOnftp', 'XMXoTrNcVajRaraFFb', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:add', '#', 'zksr', '2024-12-11 11:06:21', '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('批次发券管理', 2368, 10, 'XMXoTrNcVajRaraFFb', '2368', 'BatchCouponManage', 'operation/promotion/batchCouponManage/index', '{\"source\": 2}', 1, 0, 'C', '0', '0', 'couponBatch:couponBatch:list', 'button', 'zksr', '2024-12-11 11:05:37', '', NULL, '', 'dc');

INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('批次发券管理', 2027, 9, 'GBRtouCgJcuWuNBHxd', '2027', 'BatchCouponManage', 'operation/promotion/batchCouponManage/index', '{\"source\": 1}', 1, 0, 'C', '0', '0', 'couponBatch:couponBatch:list', 'button', 'zksr', '2024-12-07 11:18:24', 'zksr', '2024-12-11 11:01:27', '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('获得优惠券批量发送详情', 2675, 5, 'fCRtpROuicLNKPtmfq', 'GBRtouCgJcuWuNBHxd', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:query', '#', 'zksr', '2024-12-08 19:03:54', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('删除优惠券批量发送', 2675, 4, 'fNmncTXuFoRSsnUWzC', 'GBRtouCgJcuWuNBHxd', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:remove', '#', 'zksr', '2024-12-08 19:03:40', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('批量审核优惠券批量发送', 2675, 3, 'TbyigsUrdFQiDZmFWF', 'GBRtouCgJcuWuNBHxd', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:audit', '#', 'zksr', '2024-12-08 19:03:21', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('修改优惠券批量发送', 2675, 2, 'eOsdApKyICRwcEvvSC', 'GBRtouCgJcuWuNBHxd', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:edit', '#', 'zksr', '2024-12-08 19:03:05', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('新增优惠券批量发送', 2675, 1, 'jyQJMvdnGHtzmCmYBI', 'GBRtouCgJcuWuNBHxd', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:add', '#', 'zksr', '2024-12-08 19:02:52', '', NULL, '', 'partner');

-- 更新订单表历史数据赠品分摊计算字段值 (注，不要重复执行)
-- update trd_supplier_order_dtl
-- set res_price = exact_price, res_unit_price = order_sales_unit_price, res_amt = total_amt
-- where gift_price_type = 0 AND res_unit_price is null and create_time < '2024-12-12 20:00:00';
