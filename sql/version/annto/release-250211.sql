-- 开放接口 新增 退货确认前取消(售后取消) 接口  增加相应配置
-- MQ： application-rocketmq-system-dev.yml  新增配置
-- 新增 definition:
openapi_after_cancel

-- 新增 bindings:
#对外接口:退货确认前取消(售后取消)
        openapi_after_cancel-out-0:
          destination: openapi_after_cancel
        openapi_after_cancel-in-0:
          destination: openapi_after_cancel
          group: openapi_after_cancel_group

-- 增加开放配置SQL
INSERT INTO `zksr_cloud`.`sys_openability`(`openability_id`, `create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES (487846016408066666, 'zksr', '2024-06-03 15:31:42.308', 'zksr', '2024-06-09 09:13:42.850', 0, 'supplier', '退货确认前取消(售后取消)', 'afterCancel', '0', 10);

-- 增加数据字典
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '接受批量补货', '13', 'log_request_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-10 16:15:36', 'zksr', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '获取批量补货结果', '14', 'log_request_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-10 16:15:52', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货确认前取消', '15', 'log_request_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-10 16:16:16', '', NULL, NULL);

-- 全国上架商品表添加spu索引
CREATE INDEX idx_spu_id ON `zksr_product`.prdt_supplier_item(spu_id);