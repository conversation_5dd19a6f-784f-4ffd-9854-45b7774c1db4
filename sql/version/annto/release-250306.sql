-- 门店表增加省市区
ALTER TABLE `zksr_member`.mem_branch
add `province_name` varchar(64) DEFAULT null COMMENT '省份';
ALTER TABLE `zksr_member`.mem_branch
add `city_name` varchar(64) DEFAULT null COMMENT '城市';
ALTER TABLE `zksr_member`.mem_branch
add `district_name` varchar(64) DEFAULT null COMMENT '区县';


-- 新增索引 2025.02.28
ALTER TABLE `zksr_trade`.`trd_supplier_order_settle`
    ADD INDEX `idx_supplier_order_dtl_id`(`supplier_order_dtl_id`);

ALTER TABLE `zksr_trade`.`trd_supplier_order_dtl`
    ADD INDEX `idx_supplier_order_dtl_id`(`supplier_order_dtl_id`);

ALTER TABLE `zksr_trade`.`trd_supplier_after_dtl`
    ADD INDEX `idx_supplier_after_dtl_id`(`supplier_after_dtl_id`);

ALTER TABLE `zksr_trade`.`trd_supplier_after_settle`
    ADD INDEX `idx_supplier_after_dtl_id`(`supplier_after_dtl_id`);

-- 用户门店注册表新增渠道字段
ALTER TABLE `zksr_member`.`mem_member_register`
    ADD COLUMN `channel_id`  bigint(20) DEFAULT NULL COMMENT '渠道id'
;



-- 【开发接口优化】针对推送失败的单据 通过发邮件的形式，告知给客户和实施人员 新增SQL
ALTER TABLE `zksr_cloud`.`sys_opensource`
    ADD COLUMN alarm_email VARCHAR(64) DEFAULT null COMMENT '告警邮箱  多个邮箱用逗号隔开',
ADD COLUMN subscribe_send_email VARCHAR(64) DEFAULT NULL COMMENT '接口发送邮件订阅 选择需要发送的接口 用逗号隔开';

ALTER TABLE `zksr_cloud`.`sys_interface_log`
    ADD COLUMN partner_id bigint(20) DEFAULT NULL COMMENT '平台商开放能力ID';

-- 【开发接口优化】针对推送失败的单据 通过发邮件的形式，告知给客户和实施人员 日同步报告定时任务 新增数据字典SQL
INSERT INTO `zksr_cloud`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('Email模板类型', 'email_template_type', '0', 'zksr', '2025-02-13 16:54:24', '', NULL, 'Email模板类型');
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '日同步报告', '900', 'email_template_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-13 16:54:53', 'zksr', '2025-03-03 11:39:16', NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '推送B2B平台门店数据', '501', 'email_template_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-13 16:54:59', 'zksr', '2025-03-03 11:39:30', NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '推送B2B销售订单数据', '502', 'email_template_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-03 11:39:40', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '推送B2B售后订单数据', '503', 'email_template_type', '', 'default', 'N', '0', 'zksr', '2025-03-03 11:39:48', 'zksr', '2025-03-03 11:40:10', NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '推送B2B收款单数据', '504', 'email_template_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-03 11:39:57', '', NULL, NULL);


-- 【开发接口优化】针对推送失败的单据 通过发邮件的形式，告知给客户和实施人员 日同步报告定时任务 新增SQL
--  未开启 请手动开始该定时任务
-- INSERT INTO `xxl_job`.`xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (2, '每天同步第三方数据情况汇总统计(日同步报告) - 按对接第三方入驻商发送邮件', '2025-03-01 16:14:27', '2025-03-01 16:14:27', '蒋剑超', '', 'CRON', '0 00 3 * * ?', 'DO_NOTHING', 'FIRST', 'openApiDaySyncReportDataEmailJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-03-01 16:14:27', '', 0, 0, 0);

-- 【小象】系统首单的活动，门店参与资格逻辑优化 门店新增 首单单号
ALTER TABLE `zksr_member`.`mem_branch`
    ADD COLUMN `first_order_no` VARCHAR(25) DEFAULT  NULL COMMENT '首单单号' AFTER `first_order_flag`
;

-- 添加修改密码权限
ALTER TABLE `zksr_product`.`prdt_sku_price` ADD COLUMN `deleted` int NULL DEFAULT 0 COMMENT '逻辑删除 0未删除 1已删除' AFTER `large_sale_price6`;
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '修改入驻商密码', 2090, 1, 'TdVeNqzPPiXqEOrKdb', '2090', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:supplier:edit-password', '#', 'zksr', '2025-03-05 16:11:31', 'zksr', '2025-03-06 09:27:11', '', 'software,partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('修改品牌商子账户子账户密码', 2538, 1, 'IZpygKQDUeOjuGEKVx', '2538', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:brand-member:edit-password', '#', 'zksr', '2025-03-05 17:06:48', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '修改门店用户密码信息', 2193, 1, 'gMcRALmoDJPLBbLMfW', '2193', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:member:edit-password', '#', 'zksr', '2025-03-05 17:08:51', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '修改业务员密码', 2057, 1, 'uzfCtpQNgmnxNRnzxm', '2057', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonel:edit-password', '#', 'zksr', '2025-03-05 17:09:32', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '修改运营商密码', 2082, 1, 'DJMIEYRKzCnCrBhYpM', '2082', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dc:edit-password', '#', 'zksr', '2025-03-05 17:10:04', 'zksr', '2025-03-06 09:32:42', '', 'software,partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '修改平台商密码', 2240, 1, 'TRUVynRWeBveLbmgUc', '2240', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partner:edit-password', '#', 'zksr', '2025-03-06 09:30:34', '', NULL, '', 'software,partner,dc');

-- 异步导入
CREATE TABLE `zksr_cloud`.`sys_file_import_dtl` (
                                                    `file_import_dtl_id` bigint NOT NULL COMMENT '导入明细id',
                                                    `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
                                                    `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                    `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                                    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                    `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                                    `status` tinyint(1) DEFAULT NULL COMMENT '状态0成功 1失败',
                                                    `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '失败原因',
                                                    `file_import_id` bigint NOT NULL COMMENT '快递导入记录id',
                                                    `dtl_json` text COMMENT '详情json',
                                                    `batch_num` int DEFAULT NULL COMMENT '批次',
                                                    PRIMARY KEY (`file_import_dtl_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='导入明细';


CREATE TABLE `zksr_cloud`.`sys_file_import` (
                                                `file_import_id` bigint NOT NULL COMMENT '导入记录id',
                                                `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
                                                `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                                `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                                `file_name` varchar(255) DEFAULT NULL COMMENT '导入文件名',
                                                `file_url` varchar(255) DEFAULT NULL COMMENT '导入文件下载地址',
                                                `user_id` bigint DEFAULT NULL COMMENT '用户id',
                                                `total_num` int DEFAULT NULL COMMENT '导入总数',
                                                `mq_send_num` int DEFAULT NULL COMMENT 'mq发送数量',
                                                `mq_receive_num` int DEFAULT NULL COMMENT 'mq接收数量',
                                                `success_num` int DEFAULT NULL COMMENT '成功条数',
                                                `fail_num` int DEFAULT NULL COMMENT '失败条数',
                                                `update_support` tinyint(1) DEFAULT NULL COMMENT '是否更新已存在的数据;是否更新已存在的数据',
                                                `import_type` varchar(32) DEFAULT NULL COMMENT '导入类型',
                                                `import_status` int DEFAULT NULL COMMENT '导入状态 0成功 1失败 2进行中',
                                                `remark` text COMMENT '备注',
                                                PRIMARY KEY (`file_import_id`) USING BTREE,
                                                KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='导入记录';



INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '导入任务列表', 2829, 1, 'GiqqVZlHphKsIBQcDJ', 'JsOUYcsrddWpkDsVwz', 'importTaskList', 'importReportManage/importTaskList/index', NULL, 1, 0, 'C', '0', '0', '', 'education', 'zksr', '2025-02-27 14:55:54', 'zksr', '2025-02-27 15:10:27', '', 'software,partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('导入管理', 0, 11, 'JsOUYcsrddWpkDsVwz', '0', 'importReportManage', NULL, NULL, 1, 0, 'M', '0', '0', '', 'education', 'zksr', '2025-02-27 14:53:49', 'zksr', '2025-02-27 15:10:16', '', 'software,partner,dc');

ALTER TABLE zksr_cloud.sys_area_city ADD del_flag TINYINT DEFAULT 0 NULL COMMENT '删除状态(0:正常，2：删除)';
ALTER TABLE zksr_cloud.sys_area_city ADD saas_enable TINYINT NULL COMMENT 'SaaS是否可用:0是启用/1是停用';
ALTER TABLE zksr_cloud.sys_area_city ADD remark varchar(255) NULL COMMENT '备注';