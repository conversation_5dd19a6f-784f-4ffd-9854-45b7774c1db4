-- 新增、修改  同步日志功能权限信息
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '同步日志查询', 2454, 0, 'cmdjsrcTOjSRSFyPvi', '2454', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:log:query', '#', 'zksr', '2025-02-24 16:20:39', '', NULL, '', 'partner');
UPDATE `zksr_cloud`.`sys_menu` SET `perms` = 'system:log:list',`func_scop` = 'partner' WHERE `menu_code` = '2454';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner' WHERE `menu_code` = '2';


-- 新增特价限购类型字典
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('特价限购类型', 'sp_activity_times_rule', '0', 'zksr', '2024-11-25 09:49:14', 'zksr', '2024-11-25 09:50:09', '特价限购类型');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '每日一次', '0', 'sp_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:40', '', NULL, '每天重置门店活动限制');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '活动期间内仅一次', '1', 'sp_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '活动期间内只能参与一次');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '新人首单', '3', 'sp_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '只要下过单就不算了, 取消也不重置机会, 和系统首单同义');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品级别每日限购', '4', 'sp_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '即商品A每个门店每日购买上限为5件，商品A全平台每日购买上限为100件；商品B同理');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品级别仅一次', '5', 'sp_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '即商品A活动期间内只能下单一次，门店单次购买上限为5件，商品A全平台活动期间内限量100件；商品B同理');

CREATE TABLE `wx_qr_data` (
                              `qr_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                              `qr_key` varchar(32) DEFAULT NULL COMMENT '二维码key',
                              `qr_value` text COMMENT '二维码value',
                              `sys_code` bigint DEFAULT NULL COMMENT '平台商id;平台商id',
                              `create_by` varchar(64) DEFAULT NULL COMMENT '创建人;创建人',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间;创建时间',
                              `update_by` varchar(64) DEFAULT NULL COMMENT '更新人;修改人',
                              `update_time` datetime DEFAULT NULL COMMENT '更新时间;修改时间',
                              PRIMARY KEY (`qr_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='微信小程序二维码参数';

INSERT INTO `zksr_cloud`.`sys_area_city` (`area_city_id`,`pid`,`deep`,`ext_id`,`name`,`create_by`,`create_time`,`update_by`,`update_time`) VALUES (NULL,4101,2,CONCAT(area_city_id,REPEAT ('0',12-LENGTH(area_city_id))),'经济开发区',NULL,NULL,NULL,NULL);
UPDATE `zksr_cloud`.`sys_area_city` SET `ext_id`=CONCAT(area_city_id,REPEAT ('0',12-LENGTH(area_city_id))) WHERE pid=4101 AND deep=2 AND NAME='经济开发区';
INSERT INTO `zksr_cloud`.`sys_area_city` (`area_city_id`,`pid`,`deep`,`ext_id`,`name`,`create_by`,`create_time`,`update_by`,`update_time`) VALUES (NULL,4101,2,CONCAT(area_city_id,REPEAT ('0',12-LENGTH(area_city_id))),'郑东新区',NULL,NULL,NULL,NULL);
UPDATE `zksr_cloud`.`sys_area_city` SET `ext_id`=CONCAT(area_city_id,REPEAT ('0',12-LENGTH(area_city_id))) WHERE pid=4101 AND deep=2 AND NAME='郑东新区';
ALTER TABLE `zksr_cloud`.`sys_area` ADD COLUMN `deleted` int NULL DEFAULT 0 COMMENT '逻辑删除字段（0 未删除 1 已删除）' AFTER `sort_num`;
ALTER TABLE `zksr_cloud`.`sys_channel` ADD COLUMN `deleted` int NULL DEFAULT 0 COMMENT '逻辑删除字段（0 未删除 1 已删除）' AFTER `hdfk_support`;
ALTER TABLE `zksr_cloud`.`sys_channel` ADD COLUMN `status` int NULL DEFAULT 1 COMMENT '启用状态（1启用 0 停用）' AFTER `deleted`;
-- 批次发券门店导入权限
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '批次发券门店导入', 2675, 6, 'zIgKsvUhHQIbRaJQKP', 'GBRtouCgJcuWuNBHxd', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:importBranch', '#', 'zksr', '2025-02-27 15:54:53', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '批次发券门店导入', 2685, 6, 'SgMBVpAMiyydCavQEK', 'XMXoTrNcVajRaraFFb', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:importBranch', '#', 'zksr', '2025-02-26 16:42:42', '', NULL, '', 'dc');

-- 新增活动备注说明
ALTER TABLE `zksr_promotion`.`prm_activity`
    ADD COLUMN `memo` TEXT CHARACTER SET utf8mb4  DEFAULT NULL COMMENT '活动说明';

ALTER TABLE `zksr_promotion`.`prm_coupon_template`
    ADD COLUMN `memo` TEXT CHARACTER SET utf8mb4  DEFAULT NULL COMMENT '优惠券模板说明';