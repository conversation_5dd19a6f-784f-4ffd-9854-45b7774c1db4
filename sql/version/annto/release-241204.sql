ALTER TABLE `zksr_member`.`mem_member`
    ADD COLUMN `device_id` varchar(128) DEFAULT NULL COMMENT '设备唯一编码';

ALTER TABLE `zksr_member`.`mem_colonel_visit_log`
    ADD INDEX `idx_branch_id`(`branch_id`),
    ADD INDEX `idx_colonel_id`(`colonel_id`);

-- 报表管理, 一级菜单调整
UPDATE `zksr_cloud`.`sys_menu` SET `menu_name` = '区域销售汇总', `parent_id` = 2658, `order_num` = 6, `menu_code` = '2641', `menu_pcode` = 'ZfGDdvXtdyqxghIocd', `path` = 'AreaSaleSummary', `component` = 'finance/areaSaleSummary/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'report:month:list', `icon` = 'education', `create_by` = 'zksr', `create_time` = '2024-11-25 15:15:16', `update_by` = 'zksr', `update_time` = '2024-12-02 15:20:03', `remark` = '', `func_scop` = 'partner,dc,supplier' WHERE `menu_id` = 2641;
UPDATE `zksr_cloud`.`sys_menu` SET `menu_name` = '入驻商销售汇总', `parent_id` = 2658, `order_num` = 5, `menu_code` = '2640', `menu_pcode` = 'ZfGDdvXtdyqxghIocd', `path` = 'SupplierSaleSummary', `component` = 'finance/supplierSaleSummary/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'report:month:list', `icon` = 'education', `create_by` = 'zksr', `create_time` = '2024-11-25 14:16:57', `update_by` = 'zksr', `update_time` = '2024-12-02 15:19:47', `remark` = '', `func_scop` = 'supplier,dc,partner' WHERE `menu_id` = 2640;
UPDATE `zksr_cloud`.`sys_menu` SET `menu_name` = '提现对账单', `parent_id` = 2658, `order_num` = 4, `menu_code` = '2627', `menu_pcode` = 'ZfGDdvXtdyqxghIocd', `path` = 'withdrawalAccount', `component` = 'finance/withdrawalAccount/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'account:withdraw:getAccWithdrawBillList', `icon` = '#', `create_by` = 'zksr', `create_time` = '2024-11-06 23:04:28', `update_by` = 'zksr', `update_time` = '2024-12-02 15:19:41', `remark` = '', `func_scop` = 'partner,dc,supplier' WHERE `menu_id` = 2627;
UPDATE `zksr_cloud`.`sys_menu` SET `menu_name` = '交易对账单明细', `parent_id` = 2658, `order_num` = 3, `menu_code` = '2626', `menu_pcode` = 'ZfGDdvXtdyqxghIocd', `path` = 'tradeAccountDetails', `component` = 'finance/tradeAccount/details', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'account:transfer:getAccTransferBillOrderList', `icon` = '#', `create_by` = 'zksr', `create_time` = '2024-11-06 23:03:33', `update_by` = 'zksr', `update_time` = '2024-12-02 15:19:32', `remark` = '', `func_scop` = 'partner,dc,supplier' WHERE `menu_id` = 2626;
UPDATE `zksr_cloud`.`sys_menu` SET `menu_name` = '交易对账单', `parent_id` = 2658, `order_num` = 2, `menu_code` = '2625', `menu_pcode` = 'ZfGDdvXtdyqxghIocd', `path` = 'tradeAccount', `component` = 'finance/tradeAccount/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'account:transfer:getAccTranList', `icon` = '#', `create_by` = 'zksr', `create_time` = '2024-11-06 23:02:42', `update_by` = 'zksr', `update_time` = '2024-12-02 15:19:23', `remark` = '', `func_scop` = 'partner,dc,supplier' WHERE `menu_id` = 2625;
UPDATE `zksr_cloud`.`sys_menu` SET `menu_name` = '订单分佣对账单', `parent_id` = 2658, `order_num` = 1, `menu_code` = '2624', `menu_pcode` = 'ZfGDdvXtdyqxghIocd', `path` = 'orderKickbackAccount', `component` = 'finance/orderKickbackAccount/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'trade:settle:orderStatementOfAccountList', `icon` = '#', `create_by` = 'zksr', `create_time` = '2024-11-06 23:00:48', `update_by` = 'zksr', `update_time` = '2024-12-02 15:19:18', `remark` = '', `func_scop` = 'partner,dc,supplier,software' WHERE `menu_id` = 2624;
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2658, '报表管理', 0, 21, 'ZfGDdvXtdyqxghIocd', '0', 'forms', NULL, NULL, 1, 0, 'M', '0', '0', '', 'build', 'zksr', '2024-12-02 15:17:12', 'zksr', '2024-12-02 15:17:41', '', 'software,dc,supplier,partner');

-- 软件商进件菜单
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2659, '软件商进件', 2017, 1, 'ySKtMSPBqyjJcxCKuF', '2017', 'SoftwareAuth', 'softwareManage/softwareAuth/index', '', 1, 1, 'C', '1', '0', 'account:platform-software-partner:list', 'button', 'zksr', '2024-12-02 17:56:43', 'zksr', '2024-12-02 17:57:34', '', 'software');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2660, '平台商配置保存', 2000, 6, 'IbWoJwExwNCRVdrbvl', '2000', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerConfig:savePartnerConfig', '#', 'zksr', '2024-12-03 17:07:13', '', NULL, '', 'software');

-- 导出任务标识长度调整
ALTER TABLE `zksr_cloud`.`sys_export_job` MODIFY COLUMN `export_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '导出类型,匹配执行器' AFTER `operate`;

--优惠券类型字典 添加注册发券
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (453, 0, '注册发券', '4', 'coupon_receive_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-28 10:51:24', 'zksr', '2024-12-02 16:05:54', NULL);

