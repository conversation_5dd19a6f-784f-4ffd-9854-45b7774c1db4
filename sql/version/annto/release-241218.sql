
-- 订单优惠明细增加字段
ALTER TABLE `zksr-trade`.`trd_order_discount_dtl`
    ADD COLUMN `discount_condition` VARCHAR(20) DEFAULT NULL COMMENT '活动满足条件;满足多少金额或数量参与促销 （目前只有满赠、买赠使用）'
;

-- 新增司机信息表和司机评价表
CREATE TABLE `zksr-trade`.`trd_driver_rating` (
                                                  `driver_rating_id` bigint NOT NULL COMMENT '司机评分',
                                                  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
                                                  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                  `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                                  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                  `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                                  `member_id` bigint DEFAULT NULL COMMENT '用户id',
                                                  `supplier_order_id` bigint NOT NULL COMMENT '入驻商订单id',
                                                  `slot1_code` varchar(16) DEFAULT NULL COMMENT '评价维度1字典code;例：0',
                                                  `slot1_val` varchar(64) DEFAULT NULL COMMENT '评价维度1字典值;例：司机态度',
                                                  `slot1_score_code` tinyint(1) DEFAULT NULL COMMENT '评价维度1-得分字典;例：4',
                                                  `slot1_score_code_val` varchar(16) DEFAULT NULL COMMENT '评价维度1-得分字典值;例：满意',
                                                  `slot2_code` varchar(16) DEFAULT NULL COMMENT '评价维度2字典code;例：1',
                                                  `slot2_val` varchar(32) DEFAULT NULL COMMENT '评价维度2字典值;例：配送时效',
                                                  `slot2_score` tinyint(1) DEFAULT NULL COMMENT '评价维度2-得分字典;例：5',
                                                  `slot2_score_code_val` varchar(16) DEFAULT NULL COMMENT '评价维度2-得分字典值;例：非常满意',
                                                  `slot3_code` varchar(16) DEFAULT NULL COMMENT '评价维度3字典code;例：2',
                                                  `slot3_val` varchar(32) DEFAULT NULL COMMENT '评价维度3字典值;例：商品完好',
                                                  `slot3_score` tinyint(1) DEFAULT NULL COMMENT '评价维度3-得分;例：3',
                                                  `score_code_val` varchar(16) DEFAULT NULL COMMENT '评价维度1-得分字典值;例：一般满意',
                                                  `reason_code` varchar(32) DEFAULT NULL COMMENT '原因code;例：0,1',
                                                  `reason_val` varchar(32) DEFAULT NULL COMMENT '低分原因code字典值;例：态度不好,送货不及时',
                                                  `fedback_msg` varchar(128) DEFAULT NULL COMMENT '反馈信息;例：加油！努力！',
                                                  `fedback_pics` text COMMENT '反馈图片;例：',
                                                  PRIMARY KEY (`driver_rating_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='司机评分表';


CREATE TABLE `zksr-trade`.`trd_driver` (
                                           `driver_id` bigint NOT NULL COMMENT '司机id',
                                           `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
                                           `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                           `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                           `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                           `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                           `supplier_id` bigint DEFAULT NULL COMMENT '入驻商id',
                                           `driver_name` varchar(16) DEFAULT NULL COMMENT '司机名',
                                           `driver_phone` varchar(16) DEFAULT NULL COMMENT '司机手机号',
                                           PRIMARY KEY (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='司机档案表';


ALTER TABLE `zksr-trade`.`trd_supplier_order`
    ADD COLUMN `driver_id` bigint(20) DEFAULT NULL COMMENT '司机ID',
    ADD COLUMN `driver_rating_flag` tinyint(1) DEFAULT 0 COMMENT '司机评价状态 (0-未评价, 1-已评价)',
    ADD COLUMN `driver_rating_id` bigint(20) DEFAULT NULL COMMENT '司机评级ID';

-- 商品月销售报表和业务员月销售报表 菜单
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('商品月销售汇总', 2658, 9, 'ZDeMUkMOwxMoopuTZd', 'ZfGDdvXtdyqxghIocd', 'goodSaleSummary', 'finance/goodSaleSummary/index', NULL, 1, 0, 'C', '0', '0', NULL, 'education', 'zksr', NOW(), '', NULL, '', 'partner,dc,supplier');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('业务员月销售汇总', 2658, 8, 'vAsmSkrnQFGVfzzOyr', 'ZfGDdvXtdyqxghIocd', 'ColonelSaleSummary', 'finance/colonelSaleSummary/index', NULL, 1, 0, 'C', '0', '0', '', 'education', 'zksr', NOW(), 'zksr', NOW(), '', 'partner,dc,supplier');
