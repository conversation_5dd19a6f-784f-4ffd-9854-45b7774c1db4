--增加saas用户编码、saas租户编码字段
ALTER TABLE zksr_cloud.sys_user ADD saas_user_code varchar(64) NULL COMMENT 'saas系统用户编码';
ALTER TABLE zksr_cloud.sys_partner ADD saas_tenant_code varchar(64) NULL COMMENT 'saas系统租户编码';

--初始化已存在的用户账号
update sys_partner s set s.saas_tenant_code = 'ceshi1' where s.sys_code = 12;
update sys_partner s set s.saas_tenant_code = 'ceshi2' where s.sys_code = 13;
update sys_partner s set s.saas_tenant_code = 'fenghaoyunchuang' where s.sys_code = 500737635620585472;
update sys_partner s set s.saas_tenant_code = 'henanzanmei' where s.sys_code = 566813173439823872;
update sys_user s set s.phonenumber ='18140014102' where s.user_id =94;
update sys_user s set s.phonenumber ='13526420730' where s.user_id =11247;
update sys_user s set s.saas_user_code = '859f78d3' where s.phonenumber = '13500000001';
update sys_user s set s.saas_user_code = 'f663df5a' where s.phonenumber = '13500000002';
update sys_user s set s.saas_user_code = 'eba29d5f' where s.phonenumber = '13500000003';
update sys_user s set s.saas_user_code = '596b622d' where s.phonenumber = '13500000004';
update sys_user s set s.saas_user_code = '0b97a280' where s.phonenumber = '13526420730';
update sys_user s set s.saas_user_code = '184436cc' where s.phonenumber = '13526420737';
update sys_user s set s.saas_user_code = '52839966' where s.phonenumber = '13526420738';
update sys_user s set s.saas_user_code = '35fab24f' where s.phonenumber = '13526420739';
update sys_user s set s.saas_user_code = 'c20cb0de' where s.phonenumber = '13537149836';
update sys_user s set s.saas_user_code = '940b6589' where s.phonenumber = '13595011440';
update sys_user s set s.saas_user_code = '976cf4ad' where s.phonenumber = '13595011441';
update sys_user s set s.saas_user_code = '82857cf7' where s.phonenumber = '13655555555';
update sys_user s set s.saas_user_code = '811b78bf' where s.phonenumber = '13765009656';
update sys_user s set s.saas_user_code = '93e7fb91' where s.phonenumber = '15085953513';
update sys_user s set s.saas_user_code = 'c9ee0505' where s.phonenumber = '15185120581';
update sys_user s set s.saas_user_code = 'd7b55caf' where s.phonenumber = '15185120583';
update sys_user s set s.saas_user_code = '69ed588d' where s.phonenumber = '15500000001';
update sys_user s set s.saas_user_code = '80f7b263' where s.phonenumber = '15500000002';
update sys_user s set s.saas_user_code = '0d71acc5' where s.phonenumber = '15500000003';
update sys_user s set s.saas_user_code = '2a9997e9' where s.phonenumber = '15625298526';
update sys_user s set s.saas_user_code = 'd819de01' where s.phonenumber = '15625298527';
update sys_user s set s.saas_user_code = '5c365a70' where s.phonenumber = '15625298622';
update sys_user s set s.saas_user_code = '5e130068' where s.phonenumber = '15626142055';
update sys_user s set s.saas_user_code = 'a22f83f3' where s.phonenumber = '15670000001';
update sys_user s set s.saas_user_code = 'b749458d' where s.phonenumber = '15773096666';
update sys_user s set s.saas_user_code = '96c1c1cf' where s.phonenumber = '15773097777';
update sys_user s set s.saas_user_code = 'd1969a9e' where s.phonenumber = '15773098888';
update sys_user s set s.saas_user_code = 'bd661914' where s.phonenumber = '15774363636';
update sys_user s set s.saas_user_code = 'bffa7fac' where s.phonenumber = '15800000000';
update sys_user s set s.saas_user_code = '66be9125' where s.phonenumber = '15888888888';
update sys_user s set s.saas_user_code = '4da6d455' where s.phonenumber = '15902698205';
update sys_user s set s.saas_user_code = 'bcde475d' where s.phonenumber = '15908505260';
update sys_user s set s.saas_user_code = '4e91f0c7' where s.phonenumber = '16688888888';
update sys_user s set s.saas_user_code = '63fbf534' where s.phonenumber = '16688888889';
update sys_user s set s.saas_user_code = 'bb8b44a0' where s.phonenumber = '17700000001';
update sys_user s set s.saas_user_code = '16232b24' where s.phonenumber = '17700000002';
update sys_user s set s.saas_user_code = '0ef76a24' where s.phonenumber = '18085184252';
update sys_user s set s.saas_user_code = '52fd161a' where s.phonenumber = '18140014102';
update sys_user s set s.saas_user_code = '3f24ae11' where s.phonenumber = '18602506296';
update sys_user s set s.saas_user_code = '930f1bb3' where s.phonenumber = '18620241219';
update sys_user s set s.saas_user_code = 'e0037e47' where s.phonenumber = '18885255247';
update sys_user s set s.saas_user_code = 'e70f111d' where s.phonenumber = '18886004095';
update sys_user s set s.saas_user_code = '39003e2c' where s.phonenumber = '19974654041';
update sys_user s set s.saas_user_code = 'f6390d8e' where s.phonenumber = '19999999999';



 CREATE TABLE `zksr_promotion`.prm_live_order (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID主键',
  `order_no` varchar(20) DEFAULT NULL COMMENT '订单编号',
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  activity_id varchar(512) NOT NULL COMMENT '直播ID',
  `name` varchar(150) NOT NULL DEFAULT '' COMMENT '直播间名称',
  live_product_id bigint NOT NULL COMMENT '直播商品id',
  `sku_id` bigint NOT NULL COMMENT '商品sku_id',
  `spu_id` bigint NOT NULL COMMENT '商品SPU_id',
  `spu_no` varchar(32) NOT NULL DEFAULT '' COMMENT '商品SPU编号',
  `spu_name` varchar(128) NOT NULL DEFAULT ''COMMENT '商品SPU名称',
  `live_price` decimal(12,2)  NOT NULL COMMENT '直播价',
  `unit` varchar(16)  NOT NULL COMMENT '商品品单位 数据字典（sys_prdt_unit）',
  `unit_name` varchar(16)  NOT NULL COMMENT '商品品单位名称',
   order_qty decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '订单数量',
   `branch_name` varchar(55) NOT NULL DEFAULT '' COMMENT '门店名称',
   `contact_name` varchar(32) NOT NULL DEFAULT '' COMMENT '联系人',
  `contact_phone` varchar(16) NOT NULL DEFAULT '' COMMENT '联系电话',
  `branch_addr` varchar(512) NOT NULL DEFAULT '' COMMENT '门店地址',
  `remark` varchar(256) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` varchar(100) DEFAULT '' COMMENT '创建人',
  `create_time` datetime(3) NOT NULL DEFAULT current_timestamp(3) COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT '' COMMENT '更新人',
  `update_time` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3) COMMENT '更新时间',
  `del_flag` int NOT null DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `version` int(11) NOT null DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_prm_live_order_ai` (`activity_id`),
  KEY `idx_prm_live_order_on` (`order_no`)
) COMMENT='直播订单表';





 CREATE TABLE `zksr_promotion`.prm_live_product (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID主键',
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  activity_id varchar(512) NOT NULL COMMENT '直播ID',
  `name` varchar(150) NOT NULL DEFAULT '' COMMENT '直播间名称',
  `sku_id` bigint NOT NULL COMMENT '商品sku_id',
  `spu_id` bigint NOT NULL COMMENT '商品SPU_id',
  `spu_no` varchar(32) NOT NULL DEFAULT '' COMMENT '商品SPU编号',
  `spu_name` varchar(128) NOT NULL DEFAULT ''COMMENT '商品SPU名称',
  `mark_price` decimal(12,2) NOT NULL COMMENT '标准价(原价)',
  `live_price` decimal(12,2)  NOT NULL COMMENT '直播价',
  `unit` varchar(16)  NOT NULL COMMENT '商品品单位 数据字典（sys_prdt_unit）',
  `unit_name` varchar(16)  NOT NULL COMMENT '商品品单位名称',
  `image_url1` varchar(2000)  NOT NULL DEFAULT '' COMMENT '图片1',
  `image_url2` varchar(2000)  NOT NULL DEFAULT ''  COMMENT '图片2',
  `image_url3` varchar(2000)  NOT NULL DEFAULT ''  COMMENT '图片3',
  `supplier_id` bigint NOT NULL COMMENT '入驻商id',
  `supplier_name` varchar(128) NOT NULL DEFAULT '' COMMENT '入驻商名称',
  `remark` varchar(256) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` varchar(100) DEFAULT '' COMMENT '创建人',
  `create_time` datetime(3) NOT NULL DEFAULT current_timestamp(3) COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT '' COMMENT '更新人',
  `update_time` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3) COMMENT '更新时间',
  `del_flag` int NOT null DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `version` int(11) NOT null DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_prm_live_product_ai` (`activity_id`),
  KEY `idx_prm_live_product_ski` (`sku_id`),
  KEY `idx_prm_live_product_spi` (`spu_id`),
  KEY `idx_prm_live_product_spn` (`spu_no`)
) COMMENT='直播商品表';

alter TABLE `zksr_promotion`.prm_live_product add broadcast_flag int DEFAULT 0 COMMENT '弹框商品标记，0：不是，1：是';




 CREATE TABLE `zksr_promotion`.prm_live_room (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID主键',
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  activity_id bigint(20) NOT NULL COMMENT '直播ID',
  `name` varchar(150) NOT NULL DEFAULT '' COMMENT '直播间名称',
  live_time datetime(3) NOT NULL DEFAULT current_timestamp(3) COMMENT '直播开始时间',
  end_time datetime(3)  COMMENT '直播结束时间',
  activity_type int DEFAULT 0 COMMENT '直播模式,0：视频直播;1：伪直播',
  live_mode int DEFAULT 1 COMMENT '直播延时类型,1:超低延时(延时小于1秒) 2:普通延时(延时5-10秒)',
  live_layout int DEFAULT 2 COMMENT '视频布局,1:三分屏 2:纯视频',
  view_url varchar(512) NOT NULL DEFAULT '' COMMENT '观看地址',
  view_url_path varchar(20) NOT NULL DEFAULT '' COMMENT '观看地址后缀',
  is_replay_auto_online_enable int DEFAULT 0 COMMENT '是否开启回放自动上架,0:关闭 1:开启',
  cover_image varchar(512) NOT NULL DEFAULT '' COMMENT '横屏直播的封面图 URL',
  vertical_cover_image varchar(512) NOT NULL DEFAULT '' COMMENT '竖屏直播的封面图 URL',
  live_companion_url varchar(512) NOT NULL DEFAULT '' COMMENT '直播伴侣地址',
  `remark` varchar(256) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(3) NOT NULL DEFAULT current_timestamp(3) COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3) COMMENT '更新时间',
  `del_flag` int DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `version` int(11) DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_prm_live_room_ai` (`activity_id`),
  KEY `idx_prm_live_room_n` (`name`),
  KEY `idx_prm_live_room_lt` (`live_time`)
) COMMENT='直播间表';