-- application-rocketmq-system-dev.yml 新增配置
-- definition新增
openapi_afterlog;
-- bindings 新增
#对外接口:售后状态
        openapi_afterlog-out-0:
          destination: openapi_afterlog
        openapi_afterlog-in-0:
          destination: openapi_afterlog
          group: openapi_afterlog_group


-- -----------------------------------  xxl-job 增加配置 start ---------------------------------
** 更新完之后要刷新数据执行任务homePagesDayDataJobHandler
-- -----------------------------------  xxl-job 增加配置 end ---------------------------------



-- -----------------------------------  xxl-job 增加配置 start ---------------------------------
任务描述     :  	移除购物车过期加单指令
JobHandler  :   removeRecommend
CRON        :   0 0 1 * * ?
------------------------------------------------

-- -----------------------------------  xxl-job 增加配置 end ---------------------------------