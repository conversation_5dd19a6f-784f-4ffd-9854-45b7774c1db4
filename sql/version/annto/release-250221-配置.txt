-- -----------------------------------  application-rocketmq-trade-dev.yml 增加配置  start ---------------------------------
-- 业务员加单消息通知处理
-- bindings 新增
# 订单加单发送消息指令
commandAddOrderEvent-out-0:
    destination: commandAddOrderEvent
-- -----------------------------------  application-rocketmq-trade-dev.yml 增加配置 end ---------------------------------



-- -----------------------------------  application-rocketmq-member-dev.yml 增加配置 start ---------------------------------
-- 业务员加单消息通知处理
-- definition 新增
commandAddOrderEvent;

-- bindings 新增
# 收到订单加单消息指令
commandAddOrderEvent-in-0:
    destination: commandAddOrderEvent
    group: commandAddOrderEventGroup
-- -----------------------------------  application-rocketmq-member-dev.yml 增加配置 end ---------------------------------



-- 2024.10.29 郑森冰  Nacos增加配置 货到付款无需付款 售后订单退款成功回调
# application-rocketmq-trade-dev.yml
spring:
  cloud:
    stream:
      function:
        definition: "afterOrderUpdateSuccess"
      bindings:
        #售后订单退款成功回调
        afterOrderUpdateSuccess-out-0:
          destination: afterOrderUpdateSuccess
        afterOrderUpdateSuccess-in-0:
          destination: afterOrderUpdateSuccess
          group: afterOrderUpdateSuccessGroup