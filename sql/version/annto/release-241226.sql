-- 促销活动黑白名单新增导入商品信息和导入门店信息
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台特价门店导入', 2400, 5, 'HOQnodbijJecxrgfdd', '2400', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sp-rule:importBranch', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台秒杀门店导入', 2396, 5, 'yyDtrdmVSCyNCEKnjs', '2396', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:importBranch', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台满减门店导入', 2420, 6, 'HwEbjlYwlpbEWBBVgY', '2420', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:importBranch', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台满减商品导入', 2420, 5, 'JZrNLnHDGDaEvhVFZT', '2420', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:importItem', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台满赠门店导入', 2418, 6, 'OHpSzVRGsLPqtWVcxR', '2418', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:importBranch', '#', 'zksr', NOW() , 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台满赠商品导入', 2418, 5, 'ZMBGEFkwTPMdTZSoWe', '2418', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:importItem', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('特价黑白名单导入门店 ', 2401, 5, 'mojyCickfiGCbXVlsQ', '2401', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sp-rule:importBranch', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台买赠门店导入', 2398, 6, 'NTUMYivtoENIOOcMTR', '2398', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:importBranch', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('秒杀黑白名单导入门店', 2399, 5, 'NYkKgOhadoknSgZSoq', '2399', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:importBranch', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台买赠商品导入', 2398, 5, 'GUkKIchHbWZJstNUuu', '2398', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:importItem', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('满减黑白名单导入商品 ', 2419, 6, 'tcsOVikaNzwytvLaYI', '2419', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:importItem', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('满减黑白名单导入门店 ', 2419, 5, 'iXLjrPsdlKWdndVYKh', '2419', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:importBranch', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('买赠黑白名单导入商品', 2397, 6, 'hRkxBUOSBSVJnFxAYl', '2397', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:importItem', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台优惠券门店导入', 2256, 8, 'QsMIeCPjrCgxAhbEVU', '2256', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:importBranch', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('买赠黑白名单导入门店 ', 2397, 5, 'cnGRVLgSHRlDavQddn', '2397', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:importBranch', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('满赠黑白名单导入商品', 2417, 6, 'qlXgWqDcPpfQFgfEmB', '2417', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:importItem', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台优惠券商品导入', 2256, 7, 'PvXbtpYlKFmgYQFGSM', '2256', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:importItem', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('满赠黑白名单导入门店', 2417, 5, 'yAMwCUOKyCgDOYTEwZ', '2417', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:importBranch', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('优惠券黑白名单导入商品 ', 2371, 9, 'XZQjjwkxfknvgySCcX', '2371', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:importItem', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('优惠券黑白名单导入门店', 2371, 8, 'XEsODZEdbzTHfQAoNA', '2371', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:importBranch', '#', 'zksr', NOW(), '', NULL, '', 'dc');
-- 账号登录日志
CREATE TABLE `zksr_member`.`mem_login_his` (
  `login_his_id` bigint NOT NULL COMMENT '登录历史id',
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `date_id` int DEFAULT NULL COMMENT '日期;yyyyMMdd',
  `wx_openid` varchar(100) DEFAULT NULL COMMENT '微信openid;后台登录信息获取',
  `member_phone` varchar(16) DEFAULT NULL COMMENT '用户手机号;后台登录信息获取',
  `member_username` varchar(255) DEFAULT NULL COMMENT '用户名;后台登录信息获取',
  `member_id` bigint DEFAULT NULL COMMENT '用户id;后台登录信息获取',
  `branch_id` bigint DEFAULT NULL COMMENT '门店id;后台登录信息获取',
  `ip` varchar(32) DEFAULT NULL COMMENT 'ip地址;http_request',
  `district` varchar(32) DEFAULT NULL COMMENT 'ip地址归属地;http_request',
  `tp` varchar(8) DEFAULT NULL COMMENT '类型（数据字典）;0-登陆  1-访问',
  `device_id` varchar(128) DEFAULT NULL COMMENT '设备id;前端传（HttpHeader）',
  `device_type` varchar(32) DEFAULT NULL COMMENT '设备类型;前端传（HttpHeader）',
  `device_brand` varchar(32) DEFAULT NULL COMMENT '设备品牌;前端传（HttpHeader）',
  `device_model` varchar(32) DEFAULT NULL COMMENT '设备型号;前端传（HttpHeader）',
  `os_name` varchar(32) DEFAULT NULL COMMENT '系统名称;前端传（HttpHeader）',
  `os_version` varchar(32) DEFAULT NULL COMMENT '操作系统版本;前端传（HttpHeader）',
  `port` varchar(32) DEFAULT NULL COMMENT 'pc app xcx',
  `spu_id` bigint DEFAULT NULL COMMENT 'spu_id',
  PRIMARY KEY (`login_his_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='登录历史表';

--登录日志类型
INSERT INTO `zksr_cloud`.`sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '登录日志类型', 'mem_login_his_tp', '0', 'zksr', '2024-12-19 09:46:40', '', NULL, '登录日志类型');
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '登陆', '0', 'mem_login_his_tp', NULL, 'default', 'N', '0', 'zksr', '2024-12-19 09:47:09', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '访问', '1', 'mem_login_his_tp', NULL, 'default', 'N', '0', 'zksr', '2024-12-19 09:47:15', '', NULL, NULL);

ALTER TABLE `zksr_trade`.`trd_driver_rating`
ADD COLUMN `driver_id` bigint DEFAULT NULL COMMENT '司机ID';

INSERT INTO `zksr_cloud`.`sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '评价原因', 'trd_driver_rating_reason', '0', 'zksr', '2024-12-11 10:00:07', '', NULL, '评价原因');
INSERT INTO `zksr_cloud`.`sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '司机评价评分', 'trd_driver_rating_score', '0', 'zksr', '2024-12-11 09:57:56', 'zksr', '2024-12-11 09:58:08', '司机评价评分');
INSERT INTO `zksr_cloud`.`sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '司机评价维度', 'trd_driver_rating_slot', '0', 'zksr', '2024-12-11 09:56:16', '', NULL, '司机评价维度');
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '其他', '7', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:01:52', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '没礼貌', '6', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:01:44', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '语言粗暴', '5', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:01:32', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '商品缺损', '4', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:01:21', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '送达不通知', '3', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:01:01', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '送错地址', '2', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:00:51', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '送货不及时', '1', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:00:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '态度不好', '0', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:00:29', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '非常不满意', '1', 'trd_driver_rating_score', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:59:13', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '不满意', '2', 'trd_driver_rating_score', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:59:03', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '一般满意', '3', 'trd_driver_rating_score', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:58:53', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '满意', '4', 'trd_driver_rating_score', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:58:46', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '非常满意', '5', 'trd_driver_rating_score', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:58:33', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '商品完好', '2', 'trd_driver_rating_slot', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:57:20', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '配送时效', '1', 'trd_driver_rating_slot', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:57:10', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '司机态度', '0', 'trd_driver_rating_slot', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:56:58', '', NULL, NULL);

-- 消息推送方式字典调整
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('消息推送方式', 'message_push_mode', '0', 'zksr', '2024-12-12 14:51:39', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '用户APP站内', '3', 'message_push_mode', NULL, 'default', 'N', '1', 'zksr', '2024-12-12 14:54:24', 'zksr', '2024-12-12 14:55:24', NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '微信门店助手', '2', 'message_push_mode', NULL, 'default', 'N', '0', 'zksr', '2024-12-12 14:53:18', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '微信公众号', '1', 'message_push_mode', NULL, 'default', 'N', '0', 'zksr', '2024-12-12 14:52:57', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '微信小程序', '0', 'message_push_mode', NULL, 'default', 'N', '0', 'zksr', '2024-12-12 14:52:25', '', NULL, NULL);

-- 开放能力增加补货单数据
update `zksr_cloud_ver`.`sys_openability` set openability_id = 99 where openability_id = 1;

-- 然后重新添加自增长属性，并设置自增长值从100开始
ALTER TABLE zksr_cloud_ver.sys_openability MODIFY COLUMN openability_id bigint auto_increment NOT NULL COMMENT '开放能力id';
-- 设置自增长值从100开始
ALTER TABLE zksr_cloud_ver.sys_openability AUTO_INCREMENT = 100;

-- 开放能力增加补货单数据
INSERT INTO `zksr_cloud`.`sys_openability` (`create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES ('zksr', '2024-12-09 10:02:32.496', NULL, NULL, NULL, 'partner', '获取批量补货结果', 'getBatchYhRes', '0', 20);
INSERT INTO `zksr_cloud`.`sys_openability` (`create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES ('zksr', '2024-12-09 10:01:55.541', NULL, NULL, NULL, 'partner', '接受批量补货', 'submitBatchYh', '0', 1);

-- 要货单数据
CREATE TABLE `zksr_product`.`prdt_branch_yhdata` (
                                      `yh_id` bigint(20) NOT NULL,
                                      `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                      `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `pos_yh_batch_no` varchar(32) DEFAULT NULL COMMENT '要货批次号',
                                      `area_id` bigint(20) DEFAULT NULL COMMENT '区域城市id',
                                      `branch_id` bigint(20) DEFAULT NULL COMMENT '门店id',
                                      `pos_sku_name` varchar(64) DEFAULT NULL COMMENT '商品名',
                                      `pos_source_no` varchar(32) DEFAULT NULL COMMENT '商品编码',
                                      `pos_barcode` varchar(32) DEFAULT NULL COMMENT '国条码',
                                      `pos_sales_qty` int(8) DEFAULT NULL COMMENT '昨日销量',
                                      `pos_stock_qty` int(8) DEFAULT NULL COMMENT '实际库存数量',
                                      `pos_branch_stock_qty` int(8) DEFAULT NULL COMMENT '门店库存数量',
                                      `pos_suggest_qty` int(8) DEFAULT NULL COMMENT '建议购买数量',
                                      `pos_unit_name` varchar(16) DEFAULT NULL COMMENT '单位名称',
                                      `pos_max_late_time` datetime DEFAULT NULL COMMENT '最迟订货时间',
                                      `pos30day_avg_sales` int(8) DEFAULT NULL COMMENT '30天人均销量',
                                      `pos_safety_days` int(8) DEFAULT NULL COMMENT '安全库存天数',
                                      `pos_safety_stock` int(8) DEFAULT NULL COMMENT '安全库存',
                                      `mall_area_item_id` bigint(20) DEFAULT NULL COMMENT '区域城市上架商品id',
                                      `mall_match_sku_id` bigint(20) DEFAULT NULL COMMENT '匹配的sku id',
                                      `mall_unit_type` int(2) DEFAULT NULL COMMENT '单位大小,1-小单位, 2-中单位, 3-大单位',
                                      `last_time` datetime DEFAULT NULL COMMENT '上次补货时间',
                                      `last_submit_qty` int(8) DEFAULT NULL COMMENT '上次补货数量',
                                      `transit_qty` int(8) DEFAULT NULL COMMENT '已下单, 未完成数量, 在途数量',
                                      `checked` tinyint(2) DEFAULT '1' COMMENT '是否选中, 1-选中, 2-未选中',
                                      `match_state` int(2) DEFAULT NULL COMMENT '匹配状态（数据字典）0-未匹配, 1-匹配成功, 2-匹配失败',
                                      `fail_reason` int(2) DEFAULT NULL COMMENT '匹配失败原因（数据字典）0-库存不足, 1-已下架, 2-未匹配到商品',
                                      `line_num` int(10) DEFAULT NULL COMMENT '行号, 从1开始',
                                      `batch_ymd` int(10) DEFAULT NULL COMMENT '要货批次年月日yyyyMMdd',
                                      `del_flag` tinyint(2) DEFAULT '0' COMMENT '0-正常,1-已删除',
                                      PRIMARY KEY (`yh_id`),
                                      KEY `idx_branch_id` (`branch_id`),
                                      KEY `idx_pos_yh_batch_no` (`pos_yh_batch_no`),
                                      KEY `idx_batch_ymd` (`batch_ymd`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店批量要货';