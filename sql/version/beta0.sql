INSERT INTO `sys_user` VALUES (1, NULL, 103, 'zksr', '中科商软', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$hpr4ZZXQKhhSK2AKkmCit.GEw0uILwHEZxGsuz8KvJm4JdbbWzD2u', '0', '0', '127.0.0.1', '2024-01-10 18:27:15', 'zksr', '2024-01-10 18:27:15', 'zksr', NULL, '管理员');

INSERT INTO `sys_role` VALUES (1, NULL,NULL, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'zksr', '2024-01-10 18:27:15', 'zksr', NULL, '超级管理员', 'software');
INSERT INTO `sys_role` VALUES (2, NULL,NULL, '平台管理员', 'software-admin', 2, '2', 1, 1, '0', '0', 'zksr', '2024-01-10 18:27:15', 'zksr', NULL, '平台管理员角色', 'software');
INSERT INTO `sys_role` VALUES (3, NULL,NULL, '平台商管理员', 'partner-admin', 3, '3', 1, 1, '0', '0', 'zksr', '2024-01-20 14:19:23', 'zksr', '2024-01-20 14:19:31', '平台商管理员角色', 'partner');
INSERT INTO `sys_role` VALUES (4, NULL,NULL, '运营商管理员', 'dc-admin', 4, '3', 1, 1, '0', '0', 'zksr', '2021-04-08 14:45:50', 'zksr', '2024-01-20 16:39:54', '运营商管理员角色', 'dc');
INSERT INTO `sys_role` VALUES (5, NULL,NULL, '供应商管理员', 'supplier-admin', 5, '3', 1, 1, '0', '0', 'zksr', '2024-01-20 18:28:56', 'zksr', '2024-01-20 18:29:01', '供应商管理员角色', 'supplier');
INSERT INTO `sys_role` VALUES (6, NULL,NULL, '业务员管理员', 'colonel-admin', 6, '3', 1, 1, '0', '0', 'zksr', '2024-01-20 18:28:56', 'zksr', '2024-01-20 18:29:01', '业务员管理员角色', 'colonel');


-- sys_menu , sys_dict_type , sys_dict_data 从测试服数据库拷贝

-- 2024.5.16 入驻商售后订单明细表新增字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
	ADD COLUMN `exact_return_price` DECIMAL(18,6) NULL COMMENT '精准退货成交价',
	ADD COLUMN `exact_return_amt` DECIMAL(18,6) NULL COMMENT '精准退货商品金额'
	;


ALTER TABLE `zksr-promotion`.`prm_coupon_template` ADD COLUMN `supplier_id` bigint(20) NULL COMMENT '入驻商ID';
ALTER TABLE `zksr-promotion`.`prm_coupon` ADD COLUMN `supplier_id` bigint(20) NULL COMMENT '入驻商ID';
-- 2024.5.17 入驻商订单优惠表新增字段
ALTER TABLE `zksr-trade`.`trd_order_discount_dtl`
	ADD COLUMN `order_discount_dtl_id` BIGINT(20) NOT NULL PRIMARY KEY  COMMENT '订单优惠明细id'
	;

-- 2024.5.17 入驻商售后订单明细表新增字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
	ADD COLUMN `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)(new)' ,
	ADD COLUMN `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)(new)' ,
	ADD COLUMN `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)(new)' ,
	ADD COLUMN `gift_flag` VARCHAR(255)   COMMENT '是否是赠品 1-是  0-否'
	;

-- 2024.5.17 入驻商售后订单优惠表新增
-- DROP TABLE IF EXISTS trd_after_discount_dtl;
CREATE TABLE trd_after_discount_dtl(
    `after_discount_dtl_id` BIGINT(20) NOT NULL  COMMENT '售后优惠明细' ,
    `sys_code` INT(20)   COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` VARCHAR(64)   COMMENT '更新人' ,
    `update_time` DATETIME(3)   COMMENT '更新时间' ,
    `order_id` BIGINT(20) NOT NULL  COMMENT '订单id' ,
    `branch_id` BIGINT(20)   COMMENT '门店id' ,
    `sku_id` BIGINT(20) NOT NULL  COMMENT '商品sku id' ,
    `after_id` BIGINT(20) NOT NULL  COMMENT '售后单id;' ,
    `after_no` VARCHAR(25) NOT NULL  COMMENT '售后编号;' ,
    `supplier_order_dtl_id` BIGINT(20) NOT NULL  COMMENT '入驻商订单明细id' ,
    `supplier_order_dtl_no` VARCHAR(25) NOT NULL  COMMENT '入驻商订单明细编号' ,
    `supplier_after_dtl_id` BIGINT(20) NOT NULL  COMMENT '售后单明细id' ,
    `supplier_after_dtl_no` VARCHAR(25) NOT NULL  COMMENT '售后单明细编号' ,
    `discount_type` VARCHAR(16)   COMMENT '优惠类型（数据字典）' ,
    `discount_id` BIGINT(20)   COMMENT '优惠id' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)(new)' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)(new)' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)(new)' ,
    `gift_type` INT(2)   COMMENT '赠品类型;0-商品 1-优惠券' ,
    `gift_sku_id` INT(20)   COMMENT '赠品sku;gift_type=0 则记录;gift_type=1 则记录' ,
    `gift_coupon_template_id` INT(20)   COMMENT '赠品sku优惠券模板' ,
    `gift_qty` INT(8)   COMMENT '赠品数量' ,
    PRIMARY KEY (after_discount_dtl_id)
)  COMMENT = '售后优惠明细';


CREATE TABLE `prm_bg_rule` (
                               `bg_rule_id` bigint(20) NOT NULL COMMENT '买赠条件规则id',
                               `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                               `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                               `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                               `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                               `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                               `activity_id` bigint(20) NOT NULL COMMENT '买赠活动id',
                               `rule_qty` int(8) DEFAULT NULL COMMENT '触发赠送的数量',
                               `gift_type` int(2) DEFAULT NULL COMMENT '赠品类型;0-商品 1-优惠券',
                               `sku_id` bigint(20) DEFAULT NULL COMMENT 'sku_id',
                               `coupon_template_id` bigint(20) DEFAULT NULL COMMENT '返券模板id',
                               `once_gift_qty` int(8) DEFAULT NULL COMMENT '赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段',
                               `total_gift_qty` int(8) DEFAULT NULL COMMENT '总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段',
                               `status` int(1) DEFAULT NULL COMMENT '状态 1-启用 0-停用',
                               PRIMARY KEY (`bg_rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='买赠条件规则';



ALTER TABLE `zksr-promotion`.`prm_sp_rule` MODIFY COLUMN `status` int(4) NOT NULL DEFAULT 1 COMMENT '状态 1-启用 0-停用';
ALTER TABLE `zksr-promotion`.`prm_fg_rule` MODIFY COLUMN `status` int(4) NULL DEFAULT 1 COMMENT '状态 1-启用 0-停用';
ALTER TABLE `zksr-promotion`.`prm_fd_rule` MODIFY COLUMN `status` int(1) NOT NULL DEFAULT 1 COMMENT '状态 1-启用 0-停用';
ALTER TABLE `zksr-promotion`.`prm_bg_rule` MODIFY COLUMN `status` int(1) NOT NULL DEFAULT 1 COMMENT '状态 1-启用 0-停用';


/*默认值调整*/
ALTER TABLE `zksr-promotion`.`prm_activity`
    MODIFY COLUMN `chanel_scope_all_flag` tinyint(4) NULL DEFAULT 0 COMMENT '是否指定渠道参与;0-所有渠道参与 1-指定渠道参与  （指定渠道和指定门店二选一）' AFTER `spu_scope`,
    MODIFY COLUMN `branch_scope_all_flag` tinyint(4) NULL DEFAULT 0 COMMENT '是否指定门店参与;0-所有门店参与 1-指定门店参与   （指定渠道和指定门店二选一）' AFTER `chanel_scope_all_flag`;




-- 新增订单优惠明细表
-- DROP TABLE IF EXISTS trd_order_discount_dtl;
CREATE TABLE trd_order_discount_dtl(
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` VARCHAR(64)   COMMENT '更新人' ,
    `update_time` DATETIME(3)   COMMENT '更新时间' ,
    `order_id` BIGINT(20)   COMMENT '订单id' ,
    `branch_id` BIGINT(20)   COMMENT '门店id' ,
    `sku_id` BIGINT(20)   COMMENT '商品sku id' ,
    `supplier_order_dtl_id` BIGINT(20)   COMMENT '入驻商订单明细id' ,
    `supplier_order_dtl_no` VARCHAR(30)   COMMENT '入驻商订单明细编号' ,
    `discount_type` VARCHAR(16)   COMMENT '优惠类型（数据字典）' ,
    `discount_id` BIGINT(20)   COMMENT '优惠id' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)(new)' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)(new)' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)(new)' ,
    `gift_type` INT(2)   COMMENT '赠品类型;0-商品 1-优惠券' ,
    `gift_sku_id` BIGINT(20)   COMMENT '赠品sku;gift_type=0 则记录;gift_type=1 则记录' ,
    `gift_coupon_template_id` BIGINT(20)   COMMENT '赠品sku优惠券模板' ,
    `gift_qty` INT(8)   COMMENT '赠品数量'
)  COMMENT = '订单优惠明细';

-- 订单明细表添加字段
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD COLUMN `activity_discount_amt` DECIMAL(12,2) NULL COMMENT '活动优惠金额(分摊的)',
    ADD COLUMN `coupon_discount_amt` DECIMAL(12,2) NULL COMMENT '优惠金额(分摊的)',
	ADD COLUMN `coupon_discount_amt2` DECIMAL(12,2) NULL COMMENT '优惠金额(不分摊的)',
	ADD COLUMN `exact_price` DECIMAL(18,6) NULL COMMENT '精准成交价',
	ADD COLUMN `exact_total_amt` DECIMAL(18,6) NULL COMMENT '精准商品金额'
	;
-- 优惠券日志表添加字段
ALTER TABLE `zksr-promotion`.`prm_coupon_log`
	ADD COLUMN `create_by` VARCHAR(64)   COMMENT '创建人' ,
  ADD COLUMN  `update_by` VARCHAR(64)   COMMENT '更新人' ,
  ADD COLUMN  `update_time` DATETIME(3)   COMMENT '更新时间' ;

-- 增加优惠券拓展数据
CREATE TABLE `prm_coupon_template_extend` (
                                              `coupon_template_id` bigint(20) NOT NULL COMMENT '优惠券模版ID',
                                              `sys_code` bigint(20) DEFAULT NULL COMMENT '大区',
                                              `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                              `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                              `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                              `used_count` int(10) DEFAULT NULL COMMENT '已使用优惠券张数',
                                              `record_count` int(10) DEFAULT NULL COMMENT '实际领取优惠券张数',
                                              `total_sale_amt` decimal(16,2) DEFAULT NULL COMMENT '优惠券关联的订单合计销售金额',
                                              `total_coupon_amt` decimal(16,2) DEFAULT NULL COMMENT '优惠券在订单上合计优惠了多少',
                                              PRIMARY KEY (`coupon_template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券模版拓展统计表';
ALTER TABLE `zksr-trade`.`trd_order_discount_dtl` ADD COLUMN `coupon_template_id` bigint(20) NULL COMMENT '优惠券模版ID' AFTER `discount_id`;
ALTER TABLE `zksr-trade`.`trd_after_discount_dtl` ADD COLUMN `coupon_template_id` bigint(20) NULL COMMENT '优惠券模版ID' AFTER `discount_id`;
ALTER TABLE `zksr-trade`.`trd_order_discount_dtl` ADD INDEX `idx_coupon_template_id`(`coupon_template_id`);
ALTER TABLE `zksr-trade`.`trd_after_discount_dtl` ADD INDEX `idx_coupon_template_id`(`coupon_template_id`);
ALTER TABLE `zksr-trade`.`trd_order_discount_dtl`
    MODIFY COLUMN `coupon_discount_amt` decimal(12, 2) NULL DEFAULT 0 COMMENT '优惠金额(分摊的)(new)' AFTER `activity_discount_amt`,
    MODIFY COLUMN `coupon_discount_amt2` decimal(12, 2) NULL DEFAULT 0 COMMENT '优惠金额(不分摊的)(new)' AFTER `coupon_discount_amt`;
ALTER TABLE `zksr-trade`.`trd_after_discount_dtl`
    MODIFY COLUMN `coupon_discount_amt` decimal(12, 2) NULL DEFAULT 0 COMMENT '优惠金额(分摊的)(new)' AFTER `activity_discount_amt`,
    MODIFY COLUMN `coupon_discount_amt2` decimal(12, 2) NULL DEFAULT 0 COMMENT '优惠金额(不分摊的)(new)' AFTER `coupon_discount_amt`;
ALTER TABLE `zksr-promotion`.`prm_coupon`
    ADD INDEX `idx_coupon_template_id`(`coupon_template_id`),
    ADD INDEX `idx_receive_member_id`(`receive_member_id`),
    ADD INDEX `idx_branch_id`(`branch_id`);


-- 增加货到付款需求
CREATE TABLE `trd_hdfk_settle` (
                                   `hdfk_settle_id` bigint(20) NOT NULL COMMENT '订单结算id',
                                   `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                   `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                   `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                   `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                   `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                   `supplier_id` bigint(20) NOT NULL COMMENT '入驻商id',
                                   `branch_id` bigint(20) DEFAULT NULL COMMENT '门店id',
                                   `order_no` varchar(20) DEFAULT NULL COMMENT '订单编号',
                                   `order_id` bigint(20) DEFAULT NULL COMMENT '订单id',
                                   `supplier_order_no` varchar(25) DEFAULT NULL COMMENT '全国商品订单编号',
                                   `supplier_order_id` bigint(20) DEFAULT NULL COMMENT '入驻商订单id',
                                   `supplier_order_dtl_no` varchar(25) DEFAULT NULL COMMENT '入驻商订单明细编号',
                                   `supplier_order_dtl_id` bigint(20) DEFAULT NULL COMMENT '入驻商订单明细id',
                                   `supplier_after_dtl_no` varchar(25) DEFAULT NULL COMMENT '售后编号;仅售后写',
                                   `supplier_after_dtl_id` bigint(20) DEFAULT NULL COMMENT '售后单id;仅售后写',
                                   `settle_type` int(2) NOT NULL COMMENT '0-订单  1-售后',
                                   `settle_time` datetime(3) DEFAULT NULL COMMENT '结算时间',
                                   `state` int(1) NOT NULL DEFAULT '0' COMMENT '结算状态 0=未结算，1=已结算',
                                   `settle_amt` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '应结算金额',
                                   `hdfk_pay_no` varchar(25) DEFAULT NULL COMMENT '货到付款付款单号;生成付款单后反写',
                                   `fdfk_pay_dtl_id` bigint(20) DEFAULT NULL COMMENT '货到付款付款单明细;生成付款单后反写',
                                   PRIMARY KEY (`hdfk_settle_id`),
                                   KEY `idx_supplier_order_id` (`supplier_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='货到付款结算表';
CREATE TABLE `trd_hdfk_pay_dtl` (
                                    `hdfk_pay_dtl_id` bigint(20) NOT NULL COMMENT '货到付款付款单id',
                                    `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                    `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                    `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                    `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                    `pay_no` varchar(25) DEFAULT NULL COMMENT '付款单号',
                                    `hdfk_pay_id` bigint(20) DEFAULT NULL COMMENT '付款单id',
                                    `branch_id` bigint(20) NOT NULL COMMENT '门店id',
                                    `supplier_id` bigint(20) NOT NULL COMMENT '入驻商id',
                                    `pay_amt` decimal(12,2) DEFAULT NULL COMMENT '支付金额；实际支付金额',
                                    `settle_amt` decimal(12,2) DEFAULT NULL COMMENT '结算金额',
                                    `pay_rate` decimal(5,4) DEFAULT NULL COMMENT '支付公司收取的支付费率',
                                    `pay_fee` decimal(12,2) DEFAULT NULL COMMENT '支付平台手续费；(pay_amt*pay_rate) 四舍五入',
                                    `divide_amt` decimal(12,2) DEFAULT NULL COMMENT '分账金额',
                                    PRIMARY KEY (`hdfk_pay_dtl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='货到付款付款单明细';
CREATE TABLE `trd_hdfk_pay` (
                                `hdfk_pay_id` bigint(20) NOT NULL COMMENT '货到付款付款单id',
                                `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                `hdfk_pay_no` varchar(25) DEFAULT NULL COMMENT '付款单号',
                                `branch_id` varchar(255) DEFAULT NULL COMMENT '门店id',
                                `pay_state` int(1) DEFAULT NULL COMMENT '支付状态（数据字典sys_pay_state）；0-未支付 1-已支付',
                                `pay_way` varchar(32) DEFAULT NULL COMMENT '支付方式(数据字典sys_hdfk_pay_way));0-在线支付',
                                `pay_time` datetime(3) DEFAULT NULL COMMENT '支付时间',
                                `pay_amt` decimal(12,2) DEFAULT NULL COMMENT '支付金额；实际支付金额',
                                `order_amt` decimal(12,2) DEFAULT NULL COMMENT '订单金额；未减去优惠的订单金额',
                                `pay_rate` decimal(5,4) DEFAULT NULL COMMENT '支付公司收取的支付费率',
                                `pay_fee` decimal(12,2) DEFAULT NULL COMMENT '支付平台手续费；(pay_amt*pay_rate) 四舍五入',
                                `platform` varchar(16) DEFAULT NULL COMMENT '支付平台(数据字典);从sys_partner表字段jy_platform获取',
                                `pay_source` int(2) DEFAULT NULL COMMENT '付款单来源(数据字典)',
                                PRIMARY KEY (`hdfk_pay_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='货到付款付款单';

-- 交易订单表结构调整
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    MODIFY COLUMN `cancel_qty` int(11) NULL DEFAULT 0 COMMENT '取消数量' AFTER `send_qty`,
    MODIFY COLUMN `cancel_amt` decimal(12, 2) NULL COMMENT '发货前取消金额' AFTER `cancel_qty`;

-- 订单优惠表添加 新增字段
ALTER TABLE `zksr-trade`.`trd_order_discount_dtl`
	ADD COLUMN `supplier_order_id` BIGINT(20) NULL COMMENT '入驻商订单ID';

-- 货到付款索引调整
ALTER TABLE `zksr-trade`.`trd_hdfk_settle`
    ADD INDEX `idx_supplier_order_dtl_id`(`supplier_order_dtl_id`);
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD INDEX `idx_supplier_order_id`(`supplier_order_id`);

-- 入驻商订单明细表添加支付状态字段
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD COLUMN `pay_state` TINYINT ( 1 ) DEFAULT NULL COMMENT '支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消  3-货到付款未收款 4-货到付款已收款'
    ;

-- 增加支付状态
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl` ADD COLUMN `pay_state` tinyint(1) NULL DEFAULT NULL COMMENT '支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消';
ALTER TABLE `zksr-trade`.`trd_hdfk_pay_dtl` ADD COLUMN `hdfk_settle_id` bigint(20) NULL COMMENT '货到付款结算ID' AFTER `divide_amt`;
ALTER TABLE `zksr-trade`.`trd_hdfk_pay_dtl`
    MODIFY COLUMN `branch_id` bigint(20) NULL COMMENT '门店id' AFTER `hdfk_pay_id`,
    MODIFY COLUMN `supplier_id` bigint(20) NULL COMMENT '入驻商id' AFTER `branch_id`;
ALTER TABLE `zksr-trade`.`trd_hdfk_pay`
    MODIFY COLUMN `branch_id` bigint NULL COMMENT '门店id' AFTER `hdfk_pay_no`;
ALTER TABLE `zksr-trade`.`trd_hdfk_pay`
    MODIFY COLUMN `pay_state` int(1) NULL DEFAULT 0 COMMENT '支付状态（数据字典sys_pay_state）；0-未支付 1-已支付' AFTER `branch_id`;
	ADD COLUMN `supplier_order_id` BIGINT(20) NULL COMMENT '入驻商订单ID'
	;
--2024 5.25 商品改造字段
ALTER TABLE `zksr-product`.`prdt_spu`
ADD COLUMN `min_unit` VARCHAR(16) COMMENT '最小单位-单品单位 数据字典（sys_prdt_unit）' ,
ADD COLUMN  `mid_unit` VARCHAR(16) COMMENT '中单位 数据字典（sys_prdt_unit）' ,
ADD COLUMN  `mid_size` INT(8) COMMENT '中单位换算数量（换算成最小单位）' ,
ADD COLUMN  `large_unit` VARCHAR(16) COMMENT '大单位 数据字典（sys_prdt_unit）' ,
ADD COLUMN  `large_size` INT(8) COMMENT '大单位换算数量（换算成最小单位）' ,
ADD COLUMN  `is_linkage` TINYINT(1) COMMENT '是否开启联动换算 1-是 0-否',
ADD COLUMN  `expiration_date` INT(8) COMMENT '保质期';

ALTER TABLE `zksr-product`.`prdt_sku`
ADD COLUMN `mid_barcode` VARCHAR(64)   COMMENT '中单位-国际条码' ,
ADD COLUMN `mid_mark_price` DECIMAL(12,2)   COMMENT '中单位-标准价' ,
ADD COLUMN `mid_cost_price` DECIMAL(12,2)   COMMENT '中单位-成本价(供货价)' ,
ADD COLUMN `mid_suggest_price` DECIMAL(12,2)   COMMENT '中单位-建议零售价' ,
ADD COLUMN `mid_min_oq` INT(8)   COMMENT '中单位-起订' ,
ADD COLUMN `mid_jump_oq` INT(8)   COMMENT '中单位-订货组数' ,
ADD COLUMN `mid_max_oq` INT(8)   COMMENT '中单位-限购' ,
ADD COLUMN `large_barcode` VARCHAR(64)   COMMENT '大单位-国际条码' ,
ADD COLUMN `large_mark_price` DECIMAL(12,2)   COMMENT '大单位-标准价' ,
ADD COLUMN `large_cost_price` DECIMAL(12,2)   COMMENT '大单位-成本价(供货价)' ,
ADD COLUMN `large_suggest_price` DECIMAL(12,2)   COMMENT '大单位-建议零售价' ,
ADD COLUMN `large_min_oq` INT(8)   COMMENT '大单位-起订' ,
ADD COLUMN `large_jump_oq` INT(8)   COMMENT '大单位-订货组数' ,
ADD COLUMN `large_max_oq` INT(8)   COMMENT '大单位-限购' ;

--2024 5.31 商品对接REP字段
ALTER TABLE `zksr-product`.`prdt_sku`
DROP COLUMN `sourcer_no`,
DROP COLUMN `source`,
ADD COLUMN `source_no` varchar(32) NULL COMMENT '外部编码' ,
ADD COLUMN `source` varchar(8) NULL COMMENT '来源(B2B、ERP)' ;

ALTER TABLE `zksr-product`.`prdt_spu`
ADD COLUMN `source_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部编码' ,
ADD COLUMN `source` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源(B2B、ERP)' ;

ALTER TABLE `zksr-product`.`prdt_area_item`
ADD COLUMN  `min_shelf_status` TINYINT(1) COMMENT '小单位-上下架状态',
ADD COLUMN  `mid_shelf_status` TINYINT(1) COMMENT '中单位-上下架状态',
ADD COLUMN  `large_shelf_status` TINYINT(1) COMMENT '大单位-上下架状态';


ALTER TABLE `zksr-product`.`prdt_supplier_item`
ADD COLUMN  `min_shelf_status` TINYINT(1) COMMENT '小单位-上下架状态',
ADD COLUMN  `mid_shelf_status` TINYINT(1) COMMENT '中单位-上下架状态',
ADD COLUMN  `large_shelf_status` TINYINT(1) COMMENT '大单位-上下架状态';

ALTER TABLE `zksr-product`.`prdt_sku_price`
ADD COLUMN `mid_sale_price1` DECIMAL(10,2)   COMMENT '中单位-销售价1' ,
ADD COLUMN `mid_sale_price2` DECIMAL(10,2)   COMMENT '中单位-销售价2' ,
ADD COLUMN `mid_sale_price3` DECIMAL(10,2)   COMMENT '中单位-销售价3' ,
ADD COLUMN `mid_sale_price4` DECIMAL(10,2)   COMMENT '中单位-销售价4' ,
ADD COLUMN `mid_sale_price5` DECIMAL(10,2)   COMMENT '中单位-销售价5' ,
ADD COLUMN `mid_sale_price6` DECIMAL(10,2)   COMMENT '中单位-销售价6' ,
ADD COLUMN `large_sale_price1` DECIMAL(10,2)   COMMENT '大单位-销售价1' ,
ADD COLUMN `large_sale_price2` DECIMAL(10,2)   COMMENT '大单位-销售价2' ,
ADD COLUMN `large_sale_price3` DECIMAL(10,2)   COMMENT '大单位-销售价3' ,
ADD COLUMN `large_sale_price4` DECIMAL(10,2)   COMMENT '大单位-销售价4' ,
ADD COLUMN `large_sale_price5` DECIMAL(10,2)   COMMENT '大单位-销售价5' ,
ADD COLUMN `large_sale_price6` DECIMAL(10,2)   COMMENT '大单位-销售价6';



-- 入驻商订单明细表添加字段
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
	ADD COLUMN `sub_order_amt` DECIMAL(12,2)  DEFAULT 0 COMMENT '商品订单原总金额'
	;


-----------------------          安得环境已执行脚本  2024-05-30 15:33         -----------------------
ALTER TABLE `zksr-member`.`mem_branch`
    ADD COLUMN `hdfk_max_amt` decimal(12, 2) NULL COMMENT '货到付款最大可欠款金额' AFTER `last_login_time`;

-- 入驻商配置增加ERP配置
ALTER TABLE `zksr-cloud`.`sys_opensource`
 ADD COLUMN `strategy_id` varchar(255)  COMMENT 'erp租户',
 ADD COLUMN `public_key` varchar(500) COMMENT '公钥',
 ADD COLUMN `private_key` varchar(500) COMMENT '私钥',
 ADD COLUMN `erp_url` varchar(255) COMMENT 'erp请求地址';

ALTER TABLE `zksr-cloud`.`sys_opensource`
    ADD COLUMN `token` varchar(64) NULL COMMENT 'token' AFTER `ip_white_list`;

-- 促销活动增加单位
ALTER TABLE `zksr-promotion`.`prm_activity`
    ADD COLUMN `activity_unit_type` TINYINT(1) COMMENT '促销活动单位' ;

ALTER TABLE `zksr-promotion`.`prm_bg_rule`
    ADD COLUMN `gift_sku_unit_type` TINYINT(1) COMMENT '赠品商品单位大小' ;

-- 入驻商订单明细表添加字段
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
	ADD COLUMN `line_num` INT(10)  DEFAULT null COMMENT '入驻商订单行号'
	;
-- 入驻商售后订单明细表添加字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
	ADD COLUMN `line_num` INT(10)  DEFAULT null COMMENT '入驻商售后订单行号'
	;

ALTER TABLE `acc_pay_flow`
    ADD INDEX `idx_trade_no`(`trade_no`) USING BTREE;


ALTER TABLE `zksr-product`.`prdt_sku`
    MODIFY COLUMN `stock` int(11) NULL DEFAULT 0 COMMENT '库存数量' AFTER `thumb`;
-- 入驻商订单明细表添加字段
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
	-- 下单
    ADD COLUMN `order_unit` VARCHAR(16)   COMMENT '订单购买单位;最小单位的单位，中单位的单位，大单位的单位' ,
	ADD COLUMN `order_unit_type` TINYINT (1)   COMMENT '购买单位大小' ,
	ADD COLUMN `order_unit_qty` INT(8)   COMMENT '订单购买单位数量;购买的是中单位，即为中单位数量。小单位、大单位同理' ,
	ADD COLUMN `order_unit_size` INT(8)   COMMENT '订单换算数量;小单位为1，中、大单位的换算数量' ,
	ADD COLUMN `order_unit_price` DECIMAL(18,6)   COMMENT '订单购买单位价格' ,
	-- 发货
	ADD COLUMN `send_unit` VARCHAR(16)   COMMENT '发货单位;最小单位的单位，中单位的单位，大单位的单位' ,
    ADD COLUMN `send_unit_type` TINYINT (1)   COMMENT '单位大小' ,
    ADD COLUMN `send_unit_qty` INT(8)   COMMENT '发货单位数量;发货的是中单位，即为中单位数量。小单位、大单位同理' ,
    ADD COLUMN `send_unit_size` INT(8)   COMMENT '发货单位换算数量' ,
	-- 发货前取消
	ADD COLUMN `cancel_unit` VARCHAR(16)   COMMENT '发货前取消单位;最小单位的单位，中单位的单位，大单位的单位' ,
    ADD COLUMN `cancel_unit_type` TINYINT (1)   COMMENT '单位大小' ,
    ADD COLUMN `cancel_unit_qty` INT(8)   COMMENT '发货前取消数量;发货前取消的是中单位，即为中单位数量。小单位、大单位同理' ,
    ADD COLUMN `cancel_unit_size` INT(8)   COMMENT '发货前取消单位换算数量' ,
	-- 拒收
	ADD COLUMN `reject_qty` INT(8)   COMMENT '拒收数量;最小单位' ,
    ADD COLUMN `reject_unit` VARCHAR(16)   COMMENT '拒收单位;最小单位的单位，中单位的单位，大单位的单位' ,
    ADD COLUMN `reject_unit_type` TINYINT (1)   COMMENT '单位大小' ,
    ADD COLUMN `reject_unit_qty` INT(8)   COMMENT '拒收单位数量;拒收的是中单位，即为中单位数量。小单位、大单位同理' ,
    ADD COLUMN `reject_unit_size` INT(8)   COMMENT '拒收单位换算数量'
		;

-- 入驻商订单表添加字段
ALTER TABLE `zksr-trade`.`trd_supplier_order`
	ADD COLUMN `source_order_no` VARCHAR(32)  DEFAULT null COMMENT '外部订单号';

		-- 入驻商售后订单明细表添加字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
	-- 下单
    ADD COLUMN `order_unit` VARCHAR(16)   COMMENT '订单购买单位;从订单，最小单位的单位，中单位的单位，大单位的单位' ,
	ADD COLUMN `order_unit_type` TINYINT(1)   COMMENT '单位大小' ,
	ADD COLUMN `order_unit_qty` INT(8)   COMMENT '订单购买单位数量;从订单，购买的是中单位，即为中单位数量。小单位、大单位同理' ,
	ADD COLUMN `order_unit_size` INT(8)   COMMENT '订单换算数量;从订单，小单位为1，中、大单位的换算数量' ,

	-- 退货
	ADD COLUMN `return_unit` VARCHAR(16)   COMMENT '售后单位;最小单位的单位，中单位的单位，大单位的单位' ,
    ADD COLUMN `return_unit_type` TINYINT(2)   COMMENT '单位大小' ,
    ADD COLUMN `return_unit_qty` INT(8)   COMMENT '售后单位数量;发货的是中单位，即为中单位数量。小单位、大单位同理' ,
    ADD COLUMN `return_unit_size` INT(8)   COMMENT '售后单位换算数量;小单位为1，中、大单位的换算数量'
	;
-- 索引优化
ALTER TABLE `zksr-product`.`prdt_sku_price`
    ADD INDEX `idx_sku_id`(`sku_id`);

-- 入驻商订单优惠表表添加字段
ALTER TABLE `zksr-trade`.`trd_order_discount_dtl`
	ADD COLUMN `gift_unit_type` TINYINT(1)   COMMENT '赠品单位大小' ,
	ADD COLUMN `gift_unit` VARCHAR(16)   COMMENT '赠品单位' ,
	ADD COLUMN `gift_unit_size` INT(8)   COMMENT '赠品单位换算数量'
	;

-- 入驻商订单优惠表表添加字段
ALTER TABLE `zksr-trade`.`trd_order_discount_dtl`
	ADD COLUMN `discount_rule_id` bigint(20)   COMMENT '活动规则ID'
	;

-- 物流状态表（ERP->B2B）
CREATE TABLE `trd_express_status` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `supplier_order_no` varchar(255) DEFAULT NULL COMMENT '订单号',
                                      `logistics_status` TINYINT(1) COMMENT '物流状态（1待出库、2待配送、3配送中、4已配送）',
                                      `sort` int(11) DEFAULT NULL COMMENT '顺序标识字段',
                                      `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                      `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                      `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                      `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4 COMMENT='物流状态表（ERP->B2B）';

-- 入驻商订单明细表添加是否收款
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD COLUMN `is_proceeds` TINYINT (1) DEFAULT NULL COMMENT '收货是否收款'
    ;

-- 入驻商售后订单表添加字段
ALTER TABLE `zksr-trade`.`trd_supplier_after`
	ADD COLUMN `source_order_no` VARCHAR(32)  DEFAULT null COMMENT '外部订单号';

-- 接口日志表添加字段
ALTER TABLE `zksr-cloud`.`sys_interface_log`
  ADD COLUMN  `create_time` DATETIME(3)   COMMENT '创建时间' ,
  ADD COLUMN  `update_time` DATETIME(3)   COMMENT '更新时间' ;


-- sku 维护已售
ALTER TABLE `zksr-product`.`prdt_sku`
    ADD COLUMN `sale_qty` int(11) NULL DEFAULT 0 COMMENT '已售数量, 库存 - 已售 = 剩余' AFTER `source`;

-- 商品索引优化
ALTER TABLE `zksr-product`.`prdt_area_item`
    ADD INDEX `idx_sku_id`(`sku_id`);
ALTER TABLE `zksr-product`.`prdt_supplier_item`
    ADD INDEX `idx_sku_id`(`sku_id`);


-- ---------------------          安得环境已执行脚本  2024-06-13 10:00         -----------------------

-- 公众号消息通知
CREATE TABLE `sys_message_template` (
                                        `message_template_id` bigint(20) NOT NULL COMMENT '消息模版id',
                                        `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商ID',
                                        `create_by` varchar(64) NOT NULL COMMENT '创建者',
                                        `create_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
                                        `update_time` datetime DEFAULT NULL COMMENT '最后修改时间',
                                        `status` int(10) DEFAULT NULL COMMENT '0-停用,1-启用',
                                        `template_id` varchar(128) DEFAULT NULL COMMENT '模版ID',
                                        `config` varchar(1024) DEFAULT NULL COMMENT '模版配置',
                                        `scene` int(10) DEFAULT NULL COMMENT '消息场景 0-用户下单,1-订单开始配送',
                                        `receive_merchant` varchar(64) DEFAULT NULL COMMENT '接收对象 branch-门店,colonel-业务员',
                                        PRIMARY KEY (`message_template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公众号, 小程序订阅消息模版';

-- 用户增加公众号openid绑定
ALTER TABLE `zksr-member`.`mem_member`
    ADD COLUMN `publish_openid` varchar(64) NULL COMMENT '公众号openid' AFTER `pid`;

-- 入驻商增加公众号openid绑定
ALTER TABLE `zksr-cloud`.`sys_supplier`
    ADD COLUMN `publish_openid` varchar(64) NULL COMMENT '公众号openid' AFTER `min_settle_amt`;


-- 接口日志表添加字段
ALTER TABLE `zksr-cloud`.`sys_interface_log`
  ADD COLUMN  `sys_code` bigint(20) NOT NULL COMMENT '平台商id';

-- 用户注册申请表新增字段
ALTER TABLE `zksr-member`.`mem_member_register`
    ADD COLUMN  `supplier_ids` varchar(255) NULL COMMENT '需绑定的电子围栏入驻商ID信息';

-- sku条码索引
ALTER TABLE `zksr-product`.`prdt_sku`
    ADD INDEX `idx_barcode`(`barcode`),
    ADD INDEX `idx_mid_barcode`(`mid_barcode`),
    ADD INDEX `idx_large_barcode`(`large_barcode`);

-- 新增门店申请表新增字段
ALTER TABLE `zksr-member`.`mem_branch_register`
    ADD COLUMN  `supplier_ids` varchar(255) NULL COMMENT '需绑定的电子围栏入驻商ID信息';

-- 入驻商表新增字段
ALTER TABLE `zksr-cloud`.`sys_supplier`
    ADD COLUMN  `avatar` varchar(255) NULL COMMENT '入驻商头像地址';



-- 入驻商售后订单表添加字段
ALTER TABLE `zksr-trade`.`trd_supplier_after`
	ADD COLUMN `trans_no` VARCHAR(8)  DEFAULT null COMMENT '订单类型';

------------------------          生产环境已执行脚本  2024-06-17 20:30         -----------------------
-- ---------------------          安得环境已执行脚本  2024-06-18 17:30         -----------------------

-- 对接日志表修改
ALTER TABLE `zksr-cloud`.`sys_interface_log`
	MODIFY COLUMN `request_type` int(8)  DEFAULT null COMMENT '请求类型（接口）',
	DROP COLUMN `request_primary_key` ;


CREATE TABLE `prdt_platform_spu`  (
                                      `platform_spu_id` bigint(20) NOT NULL COMMENT '商品SPU id',
                                      `sys_code` bigint(20) NULL DEFAULT NULL COMMENT '平台商ID',
                                      `supplier_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入驻商id',
                                      `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
                                      `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
                                      `update_time` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
                                      `catgory_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名;一级分类>二级分类>三级分类',
                                      `brand_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌名',
                                      `supplier_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入驻商名称',
                                      `spu_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品SPU编号',
                                      `spu_name` varchar(104) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品SPU名称',
                                      `thumb` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面图（url）',
                                      `thumb_video` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面视频（url）',
                                      `images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详情页轮播（json）',
                                      `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详情信息(富文本)',
                                      `is_delete` int(1) NULL DEFAULT NULL COMMENT '是否删除 1-是 0-否',
                                      `status` int(1) NULL DEFAULT NULL COMMENT '状态 1-启用 0-停用',
                                      `spec_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品规格',
                                      `is_specs` tinyint(1) NULL DEFAULT NULL COMMENT '是否开启多规格 1-是 0-否',
                                      `min_unit` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最小单位-单品单位 数据字典（sys_prdt_unit）',
                                      `mid_unit` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中单位 数据字典（sys_prdt_unit）',
                                      `mid_size` int(8) NULL DEFAULT NULL COMMENT '中单位换算数量（换算成最小单位）',
                                      `large_unit` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '大单位 数据字典（sys_prdt_unit）',
                                      `large_size` int(8) NULL DEFAULT NULL COMMENT '大单位换算数量（换算成最小单位）',
                                      `spu_id` bigint(20) NULL DEFAULT NULL COMMENT '平台商spu_id',
                                      `copy_times` int(8) NULL DEFAULT 0 COMMENT '被复制次数',
                                      PRIMARY KEY (`platform_spu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '平台商品库-商品SPU' ROW_FORMAT = Dynamic;

CREATE TABLE `prdt_platform_sku`  (
                                      `platform_sku_id` bigint(20) NOT NULL COMMENT '商品库sku id',
                                      `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
                                      `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
                                      `update_time` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
                                      `platform_spu_id` bigint(20) NULL DEFAULT NULL COMMENT '商品库SPU id',
                                      `unit` int(3) NULL DEFAULT NULL COMMENT '单位-数据字典（sys_prdt_unit）',
                                      `barcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '国际条码',
                                      `properties` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性数组，JSON 格式',
                                      `thumb` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面图',
                                      `status` int(1) NULL DEFAULT NULL COMMENT '状态 1-启用 0-停用',
                                      `is_delete` int(1) NULL DEFAULT NULL COMMENT '是否删除 1-是 0-否',
                                      `mid_barcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中单位-国际条码',
                                      `large_barcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '大单位-国际条码',
                                      PRIMARY KEY (`platform_sku_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '平台商品库-商品SKU' ROW_FORMAT = Dynamic;
CREATE TABLE `prdt_platform_property_val`  (
                                               `platform_property_val_id` bigint(20) NOT NULL COMMENT '规格值id',
                                               `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                               `create_time` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
                                               `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
                                               `update_time` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
                                               `spu_id` bigint(20) NULL DEFAULT NULL COMMENT '商品SPU id',
                                               `supplier_id` bigint(20) NULL DEFAULT NULL COMMENT '入驻商id',
                                               `property_id` bigint(20) NULL DEFAULT NULL COMMENT '规格名称id',
                                               `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格值名称',
                                               `is_delete` int(1) NULL DEFAULT NULL COMMENT '是否删除 1-是 0-否',
                                               `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                               `status` int(1) NULL DEFAULT NULL COMMENT '状态(数据字典 sys_common_status)',
                                               PRIMARY KEY (`platform_property_val_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格值' ROW_FORMAT = Dynamic;

CREATE TABLE `prdt_platform_property`  (
                                           `platform_property_id` bigint(20) NOT NULL COMMENT '规格名称id',
                                           `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                           `create_time` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
                                           `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
                                           `update_time` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
                                           `spu_id` bigint(20) NULL DEFAULT NULL COMMENT '商品SPU id',
                                           `supplier_id` bigint(20) NULL DEFAULT NULL COMMENT '入驻商id',
                                           `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格名称',
                                           `is_delete` int(1) NULL DEFAULT NULL COMMENT '是否删除 1-是 0-否',
                                           `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                           `status` int(1) NULL DEFAULT NULL COMMENT '状态(数据字典 sys_common_status)'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格名称' ROW_FORMAT = Dynamic;

-- spu 增加共享状态
ALTER TABLE `zksr-product`.`prdt_spu`
    ADD COLUMN `share_flag` tinyint(2) NULL DEFAULT 0 COMMENT '分享状态,0-未分享, 1-共享' AFTER `source`,
    ADD COLUMN `p_spu_id` bigint(20) NULL COMMENT '共享主SPU_ID' AFTER `share_flag`;
ALTER TABLE `zksr-product`.`prdt_spu`
    ADD INDEX `idx_p_spu_id`(`p_spu_id`),
    ADD INDEX `idx_spu_no`(`spu_no`);

-- 订单表索引调整
ALTER TABLE `zksr-trade`.`trd_order`
    ADD INDEX `idx_member_id`(`member_id`);
ALTER TABLE `zksr-trade`.`trd_after`
    ADD INDEX `idx_member_id`(`member_id`);

-- 商品上架索引调整
ALTER TABLE `zksr-product`.`prdt_area_item`
    ADD INDEX `idx_area_id`(`area_id`);

-- 增加促销名称长度
ALTER TABLE `zksr-promotion`.`prm_activity`
    MODIFY COLUMN `activity_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动名称' AFTER `prm_sheet_no`;
ALTER TABLE `zksr-cloud`.`sys_user`
    MODIFY COLUMN `nick_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户昵称' AFTER `user_name`;

-- 城市展示分类增加删除标识
ALTER TABLE `zksr-product`.`prdt_area_class`
    ADD COLUMN `del_flag` char(2) NULL DEFAULT '0' COMMENT '0-未删除,1-已删除' AFTER `level`;


-- nacos gateWay配置 新增白名单
- /portal/mall/index/getPartnerMiniSettingPolicy
- /portal/mall/login/getDzwlSupplierInfoByAreaId

-- 入驻商订单表增加入驻商索引
ALTER TABLE `zksr-trade`.`trd_supplier_order`
    ADD INDEX `idx_supplier_id`(`supplier_id`);

-- 入驻商订单表增加订单编号索引
ALTER TABLE `zksr-trade`.`trd_supplier_order`
    ADD INDEX `idx_order_no`(`order_no`);
-- 订单表增加订单编号索引
ALTER TABLE `zksr-trade`.`trd_order`
    ADD INDEX `idx_order_no`(`order_no`);
-- 订单明细表增加订单id索引
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD INDEX `idx_order_id`(`order_id`);
-- 入驻商订单结算表增加订单编号索引
ALTER TABLE `zksr-trade`.`trd_supplier_order_settle`
    ADD INDEX `idx_order_no`(`order_no`);

-- 商品库需求调整
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2471, '查看详情', 2463, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:query', '#', 'zksr', '2024-06-26 15:05:45', '', NULL, '', 'software,partner');
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2470, '关闭共享', 2463, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:disable', '#', 'zksr', '2024-06-26 15:01:56', '', NULL, '', 'software,partner');
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2469, '开启共享', 2463, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:enable', '#', 'zksr', '2024-06-26 15:01:41', '', NULL, '', 'software,partner');
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2468, '商品列表', 2463, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:list', '#', 'zksr', '2024-06-26 15:01:13', '', NULL, '', 'software,partner');
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2467, '快速编辑', 2131, 11, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:edit-base', '#', 'zksr', '2024-06-26 14:46:30', '', NULL, '', 'software,partner,dc,supplier');
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2466, '关闭商品共享', 2131, 9, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:share-disable', '#', 'zksr', '2024-06-26 14:41:33', '', NULL, '', 'software,partner,dc,supplier');
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2465, '开启商品共享', 2131, 8, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:share-enable', '#', 'zksr', '2024-06-26 14:41:16', '', NULL, '', 'software,partner,dc,supplier');
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2464, '商品列表', 2131, 7, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:spuList', '#', 'zksr', '2024-06-26 14:40:52', '', NULL, '', 'software,partner,dc,supplier');
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2463, '商品库管理', 2017, 1, 'commodityStocks', 'operation/commodityStocks/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2024-06-21 13:05:20', 'zksr', '2024-06-26 15:02:19', '', 'software,partner');


-- 用户增加运营商绑定
ALTER TABLE `zksr-member`.`mem_member`
    ADD COLUMN `dc_id` bigint(20) NULL COMMENT '运营商id' AFTER `sys_code`;


-- 购物车调整
ALTER TABLE `zksr-trade`.`trd_car`
    ADD COLUMN `unit_size` tinyint(2) NULL COMMENT '1-小单位,2-中单位,3-大单位' AFTER `supplier_id`,
    ADD COLUMN `unit` int(10) NULL COMMENT '单位' AFTER `unit_size`;

-- sku 默认值调整
ALTER TABLE `zksr-product`.`prdt_sku`
    MODIFY COLUMN `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除 1-是 0-否' AFTER `cost_price`;
UPDATE prdt_sku set is_delete = 0 WHERE is_delete is null;

-- 订单结算表增加订单id索引
ALTER TABLE `zksr-trade`.`trd_settle`
    ADD INDEX `idx_order_id`(`order_id`);

-- 订单增加订单类型
ALTER TABLE `zksr-trade`.`trd_order`
    ADD COLUMN `order_type` tinyint(2) NULL COMMENT '订单类型' AFTER `order_no`;
-- 售后订单增加订单类型
ALTER TABLE `zksr-trade`.`trd_after`
    ADD COLUMN `order_type` tinyint(2) NULL COMMENT '订单类型' AFTER `order_id`;


-- 新增数据字典 【订单类型】
INSERT INTO `zksr-cloud`.`sys_dict_type`( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES ( '订单类型', 'order_type', '0', 'zksr', '2024-06-28 14:51:08', '', NULL, '订单类型');
INSERT INTO `zksr-cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES ( 0, '全国订单', '0', 'order_type', NULL, 'default', 'N', '0', 'zksr', '2024-06-28 14:51:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data`( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES ( 0, '本地订单', '1', 'order_type', NULL, 'default', 'N', '0', 'zksr', '2024-06-28 14:51:53', '', NULL, NULL);

-- INSERT INTO `zksr-cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '商品库信息导入', 2463, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:import', '#', 'zksr', '2024-06-29 17:49:50', '', NULL, '', 'software');
------------------------          生产环境已执行脚本  2024-06-29 18:00         -----------------------

/**
  添加人:郑森冰
  添加时间:2024-06-28
  添加内容:平台管理类别导入功能
 */
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台管理分类导入', 2110, 8, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:catgory:import', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');


------------------------          安得环境已执行脚本  2024-07-01 15:00         -----------------------

-- 入驻商增加 对接系统配置
ALTER TABLE `zksr-cloud`.`sys_supplier`
    ADD COLUMN `open_system_dock` tinyint(1) NULL COMMENT '是否开启第三方系统对接 0 未开启 1 开启' AFTER `avatar`;

-- 入驻商订单表添加支付状态字段
ALTER TABLE `zksr-trade`.`trd_supplier_order`
    ADD COLUMN `pay_state` TINYINT ( 1 ) DEFAULT NULL COMMENT '支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消  3-货到付款未收款 4-货到付款已收款'
;

-- nacos 添加配置   member MQ新增消息
branchGetLongitudeAndLatitudeEvent

branchGetLongitudeAndLatitudeEvent-out-0:
           destination: branchGetLongitudeAndLatitudeEvent
branchGetLongitudeAndLatitudeEvent-in-0:
          destination: branchGetLongitudeAndLatitudeEvent
          group: branchGetLongitudeAndLatitudeEventGroup

------------------------          安得环境已执行脚本  2024-07-04 20:00         -----------------------

-- zksr-gateway-dev.yml 添加简易注册白名单
- /portal/mall/login/registerSimple

-- 2024.7.8 门店信息表新增字段
ALTER TABLE `zksr-member`.`mem_branch`
    ADD COLUMN `branch_no` varchar(30) NULL COMMENT '门店编号' AFTER `branch_id`;

------------------------          生产环境已执行脚本  2024-07-09 09:05         -----------------------
------------------------          安得环境已执行脚本  2024-07-09 09:30         -----------------------

-- nacos 添加配置   trade MQ新增消息
erpToErpReceiptEvent

erpToErpReceiptEvent-out-0:
           destination: erpToErpReceiptEvent
erpToErpReceiptEvent-in-0:
          destination: erpToErpReceiptEvent
          group: erpToErpReceiptEventGroup


-- 门店备注字段修改长度大小
ALTER TABLE `zksr-member`.`mem_branch`
    MODIFY COLUMN `memo` varchar(550) NULL COMMENT '备注';

------------------------          安得环境已执行脚本  2024-07-09 16:20         -----------------------

-- 城市展示类别 调整 电子围栏字段默认值 0
ALTER TABLE `zksr-product`.`prdt_area_class` MODIFY COLUMN dzwl_flag INT DEFAULT 0 COMMENT '是否是电子围栏入驻商';


-- 门店信息表 调整 门店名称字段值长度
ALTER TABLE `zksr-member`.`mem_branch` MODIFY COLUMN branch_name varchar(128)  DEFAULT NULL COMMENT '门店名称';


ALTER TABLE `zksr-cloud`.`sys_supplier`
    MODIFY COLUMN `dzwl_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL COMMENT '电子围栏' AFTER `dzwl_flag`;

-- ----------------------          安得环境已执行脚本  2024-07-11 11:20         -----------------------


-- nacos 修改application-rocketmq-system-dev.yml 配置  删除如下配置
-- systemGeofenceAddEvent
-- systemGeofenceUpdateEvent
-- systemCheckGeofenceEvent
-- systemServiceAddEvent
--
--
-- systemServiceAddEvent-out-0:
--   destination: systemServiceAddEvent
-- systemServiceAddEvent-in-0:
--   destination: systemServiceAddEvent
--   group: systemServiceAddEventGroup
-- systemGeofenceAddEvent-out-0:
--   destination: systemGeofenceAddEvent
-- systemGeofenceAddEvent-in-0:
--   destination: systemGeofenceAddEvent
--   group: systemGeofenceAddEventGroup
-- systemGeofenceUpdateEvent-out-0:
--   destination: systemGeofenceUpdateEvent
-- systemGeofenceUpdateEvent-in-0:
--   destination: systemGeofenceUpdateEvent
--   group: systemGeofenceUpdateEventGroup
-- systemCheckGeofenceEvent-in-0:
--   destination: systemCheckGeofenceEvent
--   group: systemCheckGeofenceEventGroup

------------------------          生产环境已执行脚本  2024-07-15 09:45         -----------------------
   -- 平台商户表添加字段
ALTER TABLE `zksr-account`.`acc_platform_merchant`
  ADD COLUMN  `account_name` VARCHAR(64)   COMMENT '银行户名' ;

-- 支付商户信息增加审核状态
ALTER TABLE `zksr-account`.`acc_platform_merchant`
    ADD COLUMN `audit_status` varchar(12) NULL COMMENT '审核状态: INIT-待审核, OVERRULE-驳回, AUDITED-审核通过' AFTER `account_name`;
ALTER TABLE `zksr-account`.`acc_platform_merchant`
    ADD COLUMN `id_card` varchar(64) NULL COMMENT '身份证' AFTER `audit_status`;
ALTER TABLE `zksr-account`.`acc_platform_merchant`
    MODIFY COLUMN `account_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行卡号' AFTER `bank_branch`,
    ADD COLUMN `bank_channel_no` varchar(64) NULL COMMENT '银联行号' AFTER `account_no`,
    MODIFY COLUMN `account_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行户名' AFTER `account_no`;
ALTER TABLE `zksr-account`.`acc_platform_merchant`
    ADD COLUMN `contract_name` varchar(12) NULL COMMENT '联系人' AFTER `id_card`,
    ADD COLUMN `contract_phone` varchar(32) NULL COMMENT '联系人电话' AFTER `contract_name`;
ALTER TABLE `zksr-account`.`acc_platform_merchant`
    MODIFY COLUMN `alt_mch_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分账方商户编号' AFTER `merchant_id`;

--  nacos 添加配置   trade MQ新增【订单售后自动审核】消息
afterApproveEvent

afterApproveEvent-out-0:
    destination: afterApproveEvent
afterApproveEvent-in-0:
    destination: afterApproveEvent
    group: afterApproveEventGroup

/**
    添加内容：业务员管理表增加新字段 用于业务员审核
    添加人：zhengsenbing
    添加时间：2024-07-16
 */
ALTER TABLE `zksr-member`.`mem_colonel`
    ADD COLUMN `source` varchar(8) NULL COMMENT '来源(PC、APP)',
  ADD COLUMN `audit_state` tinyint(1) DEFAULT '1' COMMENT '审核状态 （0待审核 1审核通过 2审核不通过）',
  ADD COLUMN `audit_by` varchar(64) DEFAULT NULL COMMENT '审核人',
  ADD COLUMN `audit_time` datetime(3) DEFAULT NULL COMMENT '审核时间',
  ADD COLUMN `audit_memo` varchar(255) DEFAULT NULL COMMENT '审核备注';


--sys_opensource表商城显示物流信息
ALTER TABLE `zksr-cloud`.`sys_opensource`
ADD COLUMN `logistics_info` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商城显示物流信息（0是第三方，1否本地）' AFTER `erp_url`;
-- 新增订单支付方式数据字典
INSERT INTO `zksr-cloud`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES ('订单支付方式', 'sys_order_pay_way', '0', 'zksr', '2024-07-13 11:29:17', 'zksr', '2024-07-13 11:29:27', '订单支付方式');
INSERT INTO `zksr-cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES (0, '在线支付', '0', 'sys_order_pay_way', NULL, 'default', 'N', '0', 'zksr', '2024-07-13 11:30:41', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data`( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES (0, '货到付款', '2', 'sys_order_pay_way', NULL, 'default', 'N', '0', 'zksr', '2024-07-13 11:30:54', '', NULL, NULL);

------------------------          测试环境已执行脚本  2024-07-16 15:37         -----------------------


-- 增加认证信息
ALTER TABLE `zksr-account`.`acc_platform_merchant`
    ADD COLUMN `auth_msg` varchar(64) NULL COMMENT '认证信息' AFTER `contract_phone`;
ALTER TABLE `zksr-account`.`acc_platform_merchant`
    ADD COLUMN `pic_status` varchar(16) NULL COMMENT '图片上传状态' AFTER `auth_msg`,
    ADD COLUMN `pic_msg` varchar(255) NULL COMMENT '图片上传信息' AFTER `pic_status`,
    ADD COLUMN `card_positive_url` varchar(255) NULL COMMENT '身份证正面' AFTER `pic_msg`,
    ADD COLUMN `card_negative_url` varchar(255) NULL COMMENT '身份证反面' AFTER `card_positive_url`,
    ADD COLUMN `trade_licence_url` varchar(255) NULL COMMENT '三证合一营业执照' AFTER `card_negative_url`,
    ADD COLUMN `open_account_licence_url` varchar(255) NULL COMMENT '开户许可证' AFTER `trade_licence_url`;


/**
    添加内容：业务员管理表增加新字段
    添加人：zhengsenbing
    添加时间：2024-07-17
 */
ALTER TABLE `zksr-member`.`mem_colonel`
    ADD COLUMN `develop_people_id` bigint(20) DEFAULT NULL COMMENT '发展人id';


ALTER TABLE `zksr-account`.`acc_platform_merchant`
    ADD COLUMN `busi_merchant_type` varchar(12) NULL COMMENT '子商户类型,0-个人, 1-个人工商, 2-企业' AFTER `bank_type`,
    ADD COLUMN `legal_person` varchar(12) NULL COMMENT '法人' AFTER `id_card`;

ALTER TABLE `zksr-account`.`acc_platform_merchant`
    ADD COLUMN `license_no` varchar(48) NULL COMMENT '营业执照编号' AFTER `contract_phone`;

ALTER TABLE `zksr-member`.`mem_colonel`
    ADD COLUMN `avatar_images` varchar(255) DEFAULT NULL COMMENT '业务员头像',
	ADD COLUMN `avatar_update_time` DATETIME(3) DEFAULT NULL COMMENT '业务员头像修改时间';
/**
  增加学历字典
 */
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '小学', '0', 'type_of_education', NULL, 'default', 'N', '0', '', '2024-07-16 16:18:06', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '初中', '1', 'type_of_education', NULL, 'default', 'N', '0', '', '2024-07-16 16:18:17', '', NOW(), NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '高中', '2', 'type_of_education', NULL, 'default', 'N', '0', '', '2024-07-16 16:18:35', '', NOW(), NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '大专', '3', 'type_of_education', NULL, 'default', 'N', '0', '', '2024-07-16 16:18:59', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '本科', '4', 'type_of_education', NULL, 'default', 'N', '0', '', '2024-07-16 16:19:08', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '研究生', '5', 'type_of_education', NULL, 'default', 'N', '0', '', '2024-07-16 16:19:25', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '其他', '6', 'type_of_education', NULL, 'default', 'N', '0', '', '2024-07-16 16:19:33', '', NULL, NULL);



ALTER TABLE `zksr-account`.`acc_platform_merchant`
    ADD COLUMN `edit_order_no` varchar(64) NULL COMMENT '修改信息单号' AFTER `open_account_licence_url`,
    ADD COLUMN `edit_status` varchar(16) NULL COMMENT '审核状态: INIT-待审核, OVERRULE-驳回, AUDITED-审核通过' AFTER `edit_order_no`;
ALTER TABLE `zksr-account`.`acc_platform_merchant`
    ADD COLUMN `edit_msg` varchar(255) NULL COMMENT '图片审核状态' AFTER `edit_status`;

ALTER TABLE `zksr-trade`.`trd_supplier_order`
    ADD COLUMN `push_status` int(4) DEFAULT NULL COMMENT '推送标志（1 已推送）';


-- nacos 添加配置   trade MQ新增消息 发送订单支付更新成功处理消息（用于处理订单支付完成后的业务逻辑） topic
orderPayUpdateSuccessEvent

orderPayUpdateSuccessEvent-out-0:
    destination: orderPayUpdateSuccessEvent
orderPayUpdateSuccessEvent-in-0:
    destination: orderPayUpdateSuccessEvent
    group: orderPayUpdateSuccessEventGroup
storeProductExpressEvent-in-0:
    destination: orderPayUpdateSuccessEvent
    group: storeProductExpressEventGroup
storePrintExpressEvent-in-0:
    destination: orderPayUpdateSuccessEvent
    group: storePrintExpressEventGroup


-- nacos 删除配置   trade MQ删除或注释以下消息配置

-- storeProductExpressEvent-out-0:
    -- destination: storeProductExpressEvent
-- storeProductExpressEvent-in-0:
    -- destination: storeProductExpressEvent
    -- group: storeProductExpressEventGroup

-- storePrintExpressEvent-out-0:
    -- destination: storePrintExpressEvent
-- storePrintExpressEvent-in-0:
    -- destination: storePrintExpressEvent
    -- group: storePrintExpressEventGroup

-- 添加内容：售后订单明细表新增 商品名称字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
    ADD COLUMN `spu_name` varchar(100) DEFAULT NULL COMMENT '商品名称' AFTER `spu_id`

ALTER TABLE `zksr-account`.`acc_platform_merchant` MODIFY COLUMN `auth_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '认证信息';

------------------------          生产环境已执行脚本  2024-07-25 19:35         -----------------------

-- 可视化增加MQ配置
application-rocketmq-member-dev.yml:
        #门店信息同步
        syncDataBranchEvent-out-0:
          destination: syncDataBranchEvent

application-rocketmq-trade-dev.yml:
        #销售订单信息同步
        syncDataOrderEvent-out-0:
            destination: syncDataOrderEvent

application-rocketmq-system-dev.yml:
        definition: "smsEvent;syncDataEvent;syncDataBranchEvent;syncDataOrderEvent;syncDataAfterEvent;"

        #同步数据统一推送消息入口
        syncDataEvent-out-0:
          destination: syncDataEvent
        syncDataEvent-in-0:
          destination: syncDataEvent
          group: syncDataEventGroup

        #门店信息同步
        syncDataBranchEvent-out-0:
          destination: syncDataBranchEvent
        syncDataBranchEvent-in-0:
          destination: syncDataBranchEvent
          group: syncDataBranchEventGroup

        #销售订单信息同步
        syncDataOrderEvent-out-0:
            destination: syncDataOrderEvent
        syncDataOrderEvent-in-0:
          destination: syncDataOrderEvent
          group: syncDataOrderEventGroup

        #售后订单信息同步
        syncDataAfterEvent-out-0:
            destination: syncDataAfterEvent
        syncDataAfterEvent-in-0:
          destination: syncDataAfterEvent
          group: syncDataAfterEventGroup


application-rocketmq-trade-dev.yml:
        #售后订单信息同步
        syncDataAfterEvent-out-0:
            destination: syncDataAfterEvent



-- 添加内容：业务员表新增 签名密钥名称字段
ALTER TABLE `zksr-member`.`mem_colonel`
    ADD COLUMN `sign_secret` varchar(512) DEFAULT NULL COMMENT '签名密钥(公钥)',
    ADD COLUMN `sign_secret_private` varchar(1000) DEFAULT NULL COMMENT '签名密钥(私钥解密)';

-- 添加内容：商城用户表新增 是否业务员、关联业务员ID字段
ALTER TABLE `zksr-member`.`mem_member`
    ADD COLUMN `is_colonel` tinyint(1) DEFAULT 0 COMMENT '是否业务员 1:是 0:否 (默认：否)',
    ADD COLUMN `relate_colonel_id` bigint(20) DEFAULT NULL COMMENT '关联业务员ID';

-- 添加内容：主订单表新增 是否业务员下单、订单来源字段
ALTER TABLE `zksr-trade`.`trd_order`
    ADD COLUMN `colonel_flag` tinyint(1) DEFAULT 0 COMMENT '下单用户是否本身是业务员(0-否（默认）  1-是)',
    ADD COLUMN `source` varchar(32) DEFAULT NULL COMMENT '订单来源(mini-小程序(默认) ywyApp-业务员APP)';

-- 添加内容：订单来源字典
INSERT INTO `zksr-cloud`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES ('订单来源', 'sys_order_source', '0', 'zksr', '2024-07-26 10:27:28', '', NULL, '订单来源');
INSERT INTO `zksr-cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES (0, '商城小程序', 'mini', 'sys_order_source', NULL, 'default', 'N', '0', 'zksr', '2024-07-26 10:27:57', '', NULL, '商城小程序');
INSERT INTO `zksr-cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES (0, '业务员App', 'ywyApp', 'sys_order_source', NULL, 'default', 'N', '0', 'zksr', '2024-07-26 10:28:38', 'zksr', '2024-07-26 10:28:49', '业务员App');
INSERT INTO `zksr-cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES (0, '安卓商城App', 'androidMallApp', 'sys_order_source', NULL, 'default', 'N', '0', 'zksr', '2024-07-26 10:28:38', 'zksr', '2024-07-26 10:28:49', '安卓商城App');


-- 增加门店注册省市区
ALTER TABLE `zksr-member`.`mem_branch_register`
    ADD COLUMN `three_area_city_id` bigint(20) NULL COMMENT '三级区域城市ID, 省市区关联' AFTER `supplier_ids`;
ALTER TABLE `zksr-member`.`mem_branch`
    ADD COLUMN `three_area_city_id` bigint(20) NULL DEFAULT NULL COMMENT '三级区域城市ID, 省市区关联' AFTER `hdfk_max_amt`;
ALTER TABLE `zksr-member`.`mem_member_register`
    ADD COLUMN `three_area_city_id` bigint(20) NULL DEFAULT NULL COMMENT '三级区域城市ID, 省市区关联' AFTER `supplier_ids`;
ALTER TABLE `zksr-member`.`mem_branch`
    ADD COLUMN `wechat_merchant_auth_openid` varchar(32) NULL COMMENT '微信商户认证openid' AFTER `three_area_city_id`;

-- ----------------------          安得环境已执行脚本  2024-07-31 10:00         -----------------------

-- 添加内容：对接日志新增字段
ALTER TABLE `zksr-cloud`.`sys_interface_log`
    ADD COLUMN `req_status` tinyint(1) NULL COMMENT '请求状态(0未组装数据、1组装数据推送中/接收中、2推送/接收完成)',
    ADD COLUMN `req_data` text NULL COMMENT '请求数据(推送数据)',
    ADD COLUMN `log_type` tinyint(1) NULL COMMENT '日志类型  1、同步数据 2、接收数据',
    MODIFY COLUMN retry_count tinyint(2) DEFAULT 0 COMMENT '重试次数',
DROP COLUMN `receive_time`;


-- 添加内容：订单日志表新增 索引【订单明细ID】
ALTER TABLE `zksr-trade`.`trd_order_log`
    ADD INDEX `idx_supplier_order_dtl_id`(`supplier_order_dtl_id`);

ALTER TABLE `zksr-member`.`mem_branch`
    MODIFY COLUMN `three_area_city_id` bigint(20) NULL DEFAULT NULL COMMENT '三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)' AFTER `hdfk_max_amt`;
ALTER TABLE `zksr-member`.`mem_colonel`
    ADD COLUMN `three_area_city_id` bigint(20) NULL DEFAULT NULL COMMENT '三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)' AFTER `sign_secret_private`;
ALTER TABLE `zksr-cloud`.`sys_area`
    ADD COLUMN `three_area_city_id` bigint(20) NULL DEFAULT NULL COMMENT '三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)' AFTER `level`;


ALTER TABLE `zksr-promotion`.`prm_activity_spu_scope`
    ADD INDEX `idx_apply_id`(`apply_id`);
ALTER TABLE `zksr-promotion`.`prm_sk_rule`
    ADD INDEX `idx_sku_id`(`sku_id`);
ALTER TABLE `zksr-promotion`.`prm_sp_rule`
    ADD INDEX `idx_sku_id`(`sku_id`);

/**
  新增品牌商管理员角色
 */
INSERT INTO `zksr-cloud`.`sys_role` (`sys_code`, `dc_id`, `role_name`, `role_key`, `role_sort`, `data_scope`, `menu_check_strictly`, `dept_check_strictly`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (NULL, NULL, '品牌商管理员', 'brand-admin', 6, '3', 1, 1, '0', '0', 'zksr', '2024-01-20 18:28:56', 'zksr', '2024-07-25 19:46:51', '品牌商管理员角色', 'partner');

-- 添加内容：trade MQ　新增配置
orderToEsColonelAppBranchEvent;orderToColoenlAppPageDataEvent;orderToSyncDataOrderEvent
-- 以下这些消费者要放到 这个生产者下【orderPayUpdateSuccessEvent-out-0:】
orderToEsColonelAppBranchEvent-in-0:
          destination: orderPayUpdateSuccessEvent
          group: orderToEsColonelAppBranchEventGroup
        orderToColoenlAppPageDataEvent-in-0:
          destination: orderPayUpdateSuccessEvent
          group: orderToColoenlAppPageDataEventGroup
        orderToSyncDataOrderEvent-in-0:
          destination: orderPayUpdateSuccessEvent
          group: orderToSyncDataOrderEventGroup



-- 品牌商商户资料维护
CREATE TABLE `sys_brand_merchant` (
                                      `brand_merchant_id` bigint(20) NOT NULL,
                                      `sys_code` bigint(20) NOT NULL COMMENT '运营商编号',
                                      `create_by` varchar(64) NOT NULL COMMENT '创建者',
                                      `create_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
                                      `update_time` datetime DEFAULT NULL COMMENT '最后修改时间',
                                      `contact_name` varchar(32) DEFAULT NULL COMMENT '联系人',
                                      `contact_phone` varchar(32) DEFAULT NULL COMMENT '联系人手机号',
                                      `name` varchar(128) DEFAULT NULL COMMENT '品牌商全称',
                                      `simple_name` varchar(32) DEFAULT NULL COMMENT '品牌商简称',
                                      `contact_address` varchar(128) DEFAULT NULL COMMENT '联系地址',
                                      `status` tinyint(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
                                      `memo` varchar(255) DEFAULT NULL COMMENT '备注',
                                      `brand_ids` text COMMENT '关联品牌集合',
                                      `sys_user_id` bigint(20) DEFAULT NULL COMMENT '关联管理员id',
                                      PRIMARY KEY (`brand_merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='品牌商资料';

ALTER TABLE `zksr-product`.`prdt_brand`
    ADD COLUMN `brand_merchant_id` bigint(20) NULL COMMENT '品牌商id' AFTER `status`;

CREATE TABLE `zksr-cloud`.`visual_setting_detail` (
`visual_detail_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
`sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
`create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci DEFAULT NULL COMMENT '创建人',
`create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
`update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci DEFAULT NULL COMMENT '更新人',
`update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
`status` tinyint(1) DEFAULT NULL COMMENT '状态（0 停用  1启用）',
`supplier_id` bigint(20) DEFAULT NULL COMMENT '入驻商ID',
`visual_master_id` bigint(20) NOT NULL COMMENT '主表ID',
`visual_template_id` bigint(20) DEFAULT NULL COMMENT '接口模板类型ID',
`template_type` bigint(20) DEFAULT NULL COMMENT '接口模板类型',
`req_type` varchar(8) DEFAULT NULL COMMENT '请求类型（visual_req_type）',
`api_url` varchar(64) DEFAULT NULL COMMENT '接口地址',
`content_type` varchar(64) DEFAULT NULL COMMENT '数据类型（visual_content_type）',
`resp_name` varchar(64) DEFAULT NULL COMMENT '响应参数名称例如：code',
`resp_code` varchar(16) DEFAULT NULL COMMENT '响应参数值例如：200/success',
`api_template` text COMMENT '接口模板JSON',
`debug_result_status` tinyint(1) DEFAULT NULL COMMENT '调试状态（0 失败  1 成功  2其他）',
`debug_result_message` text COMMENT '调试结果',
`memo` varchar(255) DEFAULT NULL COMMENT '备注',
`api_name` varchar(255) DEFAULT NULL COMMENT '接口名称',
`resp_data` varchar(255) DEFAULT NULL COMMENT '响应数据',
`resp_msg` varchar(255) DEFAULT NULL COMMENT '响应消息',
PRIMARY KEY (`visual_detail_id`) USING BTREE
) COMMENT='可视化配置详情表';






CREATE TABLE `zksr-cloud`.`visual_setting_master` (
`visual_master_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
`sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
`create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci DEFAULT NULL COMMENT '创建人',
`create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
`update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci DEFAULT NULL COMMENT '更新人',
`update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
`platform_name` varchar(64) DEFAULT NULL COMMENT '平台名称',
`supplier_id` bigint(20) DEFAULT NULL COMMENT '入驻商ID',
`encrypt_type` int(11) DEFAULT NULL COMMENT '加密类型（数据字典send_encrypt_type）',
`send_url` varchar(255) DEFAULT NULL COMMENT '对接地址',
`status` tinyint(1) DEFAULT NULL COMMENT '状态（0 停用  1启用）',
`public_key` varchar(255) DEFAULT NULL COMMENT '公钥',
`private_key` varchar(255) DEFAULT NULL COMMENT '私钥',
`send_code` varchar(255) DEFAULT NULL COMMENT '对接唯一编码',
`memo` varchar(255) DEFAULT NULL COMMENT '备注',
`send_type` varchar(16) DEFAULT NULL COMMENT '对接类型',
`source_type` int(4) DEFAULT NULL COMMENT '系统类型',
PRIMARY KEY (`visual_master_id`) USING BTREE
)  COMMENT='可视化配置主表';



CREATE TABLE `zksr-cloud`.`visual_setting_template` (
`visual_template_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
`create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci DEFAULT NULL COMMENT '创建人',
`create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
`update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci DEFAULT NULL COMMENT '更新人',
`update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
`status` tinyint(1) DEFAULT NULL COMMENT '状态（0 停用  1启用）',
`template_name` varchar(64) DEFAULT NULL COMMENT '接口模板名称',
`api_template` text COMMENT '接口模板JSON',
`template_type` int(11) DEFAULT NULL COMMENT '接口模板类型',
PRIMARY KEY (`visual_template_id`) USING BTREE
)  COMMENT='可视化接口模板表';


/**
  业务员APP权限菜单 发展业务员 查我发展的、查我管理的、新增业务员
 */
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('发展业务员', 2331, 7, '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:developmentSalesman', '#', 'zksr', NOW(), '', NOW(), '', 'colonel');


------------------------          生产环境已执行脚本  2024-08-05 19:35         -----------------------

/**
  记录平台展示分类删除
 */
ALTER TABLE `zksr-product`.`prdt_sale_class`
    ADD COLUMN `del_flag` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '0' COMMENT '0-未删除,1-已删除' AFTER `level`;

/**
  平台商子商户
 */
CREATE TABLE `sys_brand_member` (
                                    `brand_member_id` bigint(20) NOT NULL,
                                    `sys_code` bigint(20) NOT NULL COMMENT '运营商编号',
                                    `create_by` varchar(64) NOT NULL COMMENT '创建者',
                                    `create_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
                                    `update_time` datetime DEFAULT NULL COMMENT '最后修改时间',
                                    `brand_merchant_id` bigint(20) DEFAULT NULL COMMENT '品牌商ID',
                                    `contact_phone` varchar(32) DEFAULT NULL COMMENT '联系人手机号',
                                    `contact_name` varchar(32) DEFAULT NULL COMMENT '联系人',
                                    `contact_address` varchar(128) DEFAULT NULL COMMENT '联系地址',
                                    `memo` varchar(255) DEFAULT NULL COMMENT '备注',
                                    `sys_user_id` bigint(20) DEFAULT NULL COMMENT '关联管理员id',
                                    `brand_ids` text COMMENT '关联品牌集合',
                                    `area_ids` text COMMENT '关联城市集合',
                                    `status` tinyint(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
                                    PRIMARY KEY (`brand_member_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='品牌商子账户';

ALTER TABLE `zksr-cloud`.`sys_user`
    ADD COLUMN `area_id` text NULL COMMENT '城市ID' AFTER `colonel_id`,
    ADD COLUMN `brand_id` text NULL COMMENT '品牌ID' AFTER `area_id`;


------------------------          生产环境已执行脚本  2024-08-08 10:15         -----------------------



-- 新增业务员 业务日结表
CREATE TABLE mem_colonel_day_settle(
                                       `colonel_day_settle_id` BIGINT(20) NOT NULL  COMMENT '主键ID' ,
                                       `sys_code` INT(20)  NOT NULL COMMENT '平台商id' ,
                                       `create_by` VARCHAR(64)   COMMENT '创建人' ,
                                       `create_time` DATETIME(3)   COMMENT '创建时间' ,
                                       `update_by` VARCHAR(64)   COMMENT '更新人' ,
                                       `update_time` DATETIME(3)   COMMENT '更新时间' ,
                                       `settle_create_date` DATE   COMMENT '结算创建日期' ,
                                       `colonel_id` BIGINT(20) NOT NULL  COMMENT '业务员ID' ,
                                       `order_qty` BIGINT(8)   COMMENT '下单数量' ,
                                       `percentage_amt` DECIMAL(12,2)  COMMENT '业务提成金额' ,
                                       `branch_order_amt` DECIMAL(12,2)  COMMENT '门店下单金额' ,
                                       `branch_refund_amt` DECIMAL(12,2)  COMMENT '门店退货金额' ,
                                       `visit_qty` BIGINT(8)   COMMENT '拜访门店数量' ,
                                       `add_branch_qty` BIGINT(8)   COMMENT '拓店数量' ,
                                       `sale_branch_qty` BIGINT(8)   COMMENT '动销门店数量' ,
                                       `business_order_qty` BIGINT(8)   COMMENT '业务下单数量(业务员代客下单)' ,
                                       `business_order_amt` DECIMAL(12,2)    COMMENT '业务下单金额(业务员代客下单)' ,
                                       PRIMARY KEY (colonel_day_settle_id)
)  COMMENT = '业务员-业务日结表';
-- 新增业务员 业务月结表
CREATE TABLE mem_colonel_month_settle(
                                         `colonel_month_settle_id` BIGINT(20) NOT NULL  COMMENT '主键ID' ,
                                         `sys_code` INT(20)  NOT NULL COMMENT '平台商id' ,
                                         `create_by` VARCHAR(64)   COMMENT '创建人' ,
                                         `create_time` DATETIME(3)   COMMENT '创建时间' ,
                                         `update_by` VARCHAR(64)   COMMENT '更新人' ,
                                         `update_time` DATETIME(3)   COMMENT '更新时间' ,
                                         `settle_month_date` varchar(20)   COMMENT '结算月份' ,
                                         `colonel_id` BIGINT(20) NOT NULL  COMMENT '业务员ID' ,
                                         `order_qty` BIGINT(8)   COMMENT '下单数量' ,
                                         `percentage_amt` DECIMAL(12,2)  COMMENT '业务提成金额' ,
                                         `branch_order_amt` DECIMAL(12,2)  COMMENT '门店下单金额' ,
                                         `branch_refund_amt` DECIMAL(12,2)  COMMENT '门店退货金额' ,
                                         `visit_qty` BIGINT(8)   COMMENT '拜访门店数量' ,
                                         `add_branch_qty` BIGINT(8)   COMMENT '拓店数量' ,
                                         `sale_branch_qty` BIGINT(8)   COMMENT '动销门店数量' ,
                                         `business_order_qty` BIGINT(8)   COMMENT '业务下单数量(业务员代客下单)' ,
                                         `business_order_amt` DECIMAL(12,2)    COMMENT '业务下单金额(业务员代客下单)' ,
                                         `visit_month_avg_qty` DOUBLE(12,2)   COMMENT '拜访门店月平均数量' ,
                                         `visit_day30_avg_qty` DOUBLE(12,2)   COMMENT '前30天拜访门店平均数量' ,
                                         `add_branch_month_avg_qty` DOUBLE(12,2)   COMMENT '拓店月平均数量' ,
                                         `add_branch_day30_avg_qty` DOUBLE(12,2)   COMMENT '前30天拓店数量' ,
                                         `sale_branch_month_avg_qty` DOUBLE(12,2)   COMMENT '动销门店月平均数量' ,
                                         `sale_branch_day30_avg_qty` DOUBLE(12,2)   COMMENT '前30天动销门店平均数量' ,
                                         PRIMARY KEY (colonel_month_settle_id)
)  COMMENT = '业务员-业务月结表';



-- 销售订单明细表添加品牌ID
ALTER TABLE `zksr_trade`.`trd_supplier_order_dtl`
    ADD COLUMN `brand_id` BIGINT(20) NULL COMMENT '品牌ID' AFTER `sku_id`;
-- 售后订单明细表添加品牌ID
ALTER TABLE `zksr_trade`.`trd_supplier_after_dtl`
    ADD COLUMN `brand_id` BIGINT(20) NULL COMMENT '品牌ID' AFTER `sku_id`;

--可视化接口菜单新增
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('可视化接口', 0, 1, 'software', NULL, NULL, 1, 1, 'M', '0', '0', '', 'edit', 'zksr', '2024-06-28 10:46:15', 'zksr', '2024-07-01 14:20:29', '', 'software');
-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('可视化接口', @parentId, 1, 'visualization', 'software/visualization/index', NULL, 1, 0, 'C', '0', '0', '', 'edit', 'zksr', '2024-06-28 15:46:33', 'zksr', '2024-06-28 17:36:26', '', 'software');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('可视化接口配置详情', @parentId, 2, 'visualization/detail', 'software/visualization/detail', NULL, 1, 1, 'C', '1', '0', '', 'edit', 'zksr', '2024-07-01 13:24:52', 'zksr', '2024-07-01 13:26:00', '', 'software');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('可视化接口模板', @parentId, 3, 'visualization/template', 'software/visualization/template', NULL, 1, 0, 'C', '0', '0', '', 'education', 'zksr', '2024-07-23 10:28:16', 'zksr', '2024-07-23 10:29:03', '', 'software');

------------------------          生产环境已执行脚本  2024-08-14 16:15         -----------------------
ALTER TABLE `zksr-account`.`acc_pay_flow`
    ADD COLUMN `split_type` tinyint(2) NULL DEFAULT 0 COMMENT '0-订单号支付单号合一, 1-支付流水唯一' AFTER `appid`;



/**
  增加微信与门店认证授权一对一对小程序关系
 */
CREATE TABLE `mem_member_open_auth` (
                                        `b2b_auth_open_id` bigint(20) NOT NULL,
                                        `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                        `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                        `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                        `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                        `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                        `openid` varchar(32) DEFAULT NULL COMMENT '微信小程序openid',
                                        `branch_id` bigint(20) DEFAULT NULL COMMENT '门店ID',
                                        `auth_state` tinyint(4) DEFAULT NULL COMMENT '0-未授权, 1-已认证授权',
                                        `appid` varchar(64) DEFAULT NULL COMMENT '小程序appid',
                                        PRIMARY KEY (`b2b_auth_open_id`),
                                        KEY `idx_branch_id` (`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='b2b openid 认证表';
------------------------          生产环境已执行脚本  2024-08-15 20:15         -----------------------

ALTER TABLE `zksr-account`.`acc_pay_flow`
    ADD COLUMN `settles` text NULL COMMENT '分账信息' AFTER `split_type`;


/**
  离线分账支持
 */
CREATE TABLE `acc_divide_dtl` (
                                  `divide_dtl_id` bigint(20) NOT NULL COMMENT '支付分账详情id',
                                  `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                  `merchant_type` varchar(16) NOT NULL COMMENT '商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店',
                                  `merchant_id` bigint(20) NOT NULL COMMENT '商户id',
                                  `alt_mch_no` varchar(32) NOT NULL COMMENT '分账方商户编号',
                                  `alt_mch_name` varchar(64) DEFAULT NULL COMMENT '分账方名称',
                                  `order_amt` decimal(10,2) DEFAULT NULL COMMENT '订单金额',
                                  `alt_amt` decimal(10,2) DEFAULT NULL COMMENT '分账金额',
                                  `fee_rate` decimal(6,2) DEFAULT NULL COMMENT '支付手续费率',
                                  `fee` decimal(10,2) DEFAULT NULL COMMENT '支付手续费',
                                  `platform` varchar(16) NOT NULL COMMENT '支付平台(数据字典)',
                                  `pay_flow_id` bigint(20) NOT NULL COMMENT '支付流水id',
                                  `online_or_offline` tinyint(1) DEFAULT NULL COMMENT '0-线上分账  1-线下分账',
                                  `online_divide_state` tinyint(2) DEFAULT '0' COMMENT '线上分账状态(数据字典);0-未分账 1-已分账 2-分账中 3-分账失败 4-不分账',
                                  `offline_pro_state` tinyint(2) DEFAULT '0' COMMENT '线下处理状态;0-未处理 1-已处理 2-无需处理 针对分账失败或者不分账的订单的补偿措施',
                                  `offline_pro_no` varchar(32) DEFAULT NULL COMMENT '线下处理单号',
                                  `divide_time` datetime DEFAULT NULL COMMENT '分账完成时间',
                                  `trade_no` varchar(32) DEFAULT NULL COMMENT '支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号',
                                  `sub_trade_no` varchar(32) DEFAULT NULL COMMENT '子订单号, 适用于一主多详情',
                                  `out_trade_no` varchar(32) DEFAULT NULL COMMENT '支付平台商户订单号',
                                  `refund_no` varchar(32) DEFAULT NULL COMMENT '退款单号;商城退款即是售后单号',
                                  `sub_refund_no` varchar(32) DEFAULT NULL COMMENT '子退款单, 适用于一主多详情',
                                  `out_refund_no` varchar(32) DEFAULT NULL COMMENT '支付平台商户退款单号',
                                  `pay_type` tinyint(1) DEFAULT NULL COMMENT '支付类型 0-支付 1-退款',
                                  `platform_divide_flow_no` varchar(32) DEFAULT NULL COMMENT '第三方支付平台分账流水号',
                                  `order_type` tinyint(2) DEFAULT '0' COMMENT '订单类型 0-商城订单  1-入驻商充值  2-门店充值',
                                  PRIMARY KEY (`divide_dtl_id`,`pay_flow_id`),
                                  KEY `idx_merchant_id` (`merchant_id`),
                                  KEY `idx_trade_no` (`trade_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付分账详情表';

CREATE TABLE `acc_offline_divide` (
                                      `offline_divide_id` bigint(20) NOT NULL,
                                      `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                      `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                      `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                      `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                      `offline_pro_no` varchar(32) DEFAULT NULL COMMENT '线下处理单号',
                                      `merchant_type` varchar(16) NOT NULL COMMENT '商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店',
                                      `merchant_id` bigint(20) NOT NULL COMMENT '商户id',
                                      `alt_amt` decimal(10,2) DEFAULT NULL COMMENT '分账金额',
                                      `offline_no` varchar(64) DEFAULT NULL COMMENT '线下转账单号',
                                      `total_alt_amt` decimal(10,2) DEFAULT NULL COMMENT '总分账金额',
                                      `total_order_amt` decimal(10,2) DEFAULT NULL COMMENT '总订单金额',
                                      `voucher_pic` varchar(255) DEFAULT NULL COMMENT '凭证图片',
                                      PRIMARY KEY (`offline_divide_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线下分账处理';

-- 订单明细表添加 属性【支付平台】
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD COLUMN `platform` VARCHAR(20) NULL  COMMENT '支付平台' AFTER `pay_state`;
-- 售后订单明细表添加 属性【支付状态】
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
    ADD COLUMN `pay_state` tinyint(1) DEFAULT NULL  COMMENT '支付状态（同订单明细表支付状态）' AFTER `platform`;


ALTER TABLE `zksr-account`.`acc_offline_divide`
    ADD INDEX `idx_offline_pro_no`(`offline_pro_no`);

------------------------          生产环境已执行脚本  2024-08-17 18:00         -----------------------
/**
  添加人:郑森冰
  添加时间:2024-08-19 10:19
  添加内容:用户表头列配置表
 */
CREATE TABLE sys_user_column(
                                `user_column_id` BIGINT(20) NOT NULL  COMMENT '用户表头列配置id' ,
                                `sys_code` BIGINT(20)   COMMENT '平台商id' ,
                                `create_by` VARCHAR(64)   COMMENT '创建人' ,
                                `create_time` DATETIME(3)   COMMENT '创建时间' ,
                                `update_by` VARCHAR(64)   COMMENT '更新人' ,
                                `update_time` DATETIME(3)   COMMENT '更新时间' ,
                                `table_code` VARCHAR(32) NOT NULL  COMMENT '表code' ,
                                `filed_key` VARCHAR(128)   COMMENT '列key' ,
                                `filed_name` VARCHAR(128)   COMMENT '列名' ,
                                `visible_flag` TINYINT(1)   COMMENT '是否显示列' ,
                                `export_visibel_flag` TINYINT(1)   COMMENT '是否导出显示列' ,
                                `fixed_flag` TINYINT(1)   COMMENT '是否固定列' ,
                                `sort` INT(10)   COMMENT '排序' ,
                                PRIMARY KEY (user_column_id)
)  COMMENT = '用户表头列配置';

ALTER TABLE `zksr-cloud`.`sys_area`
    ADD COLUMN `sort_num` int(10) NULL COMMENT '城市排序号' AFTER `three_area_city_id`;

-- 业务员表添加 更新 签名密钥字段长度
ALTER TABLE `zksr-member`.`mem_colonel`
    MODIFY COLUMN `sign_secret` varchar(1000) DEFAULT NULL COMMENT '签名密钥(公钥)',
    MODIFY COLUMN `sign_secret_private` text DEFAULT NULL COMMENT '签名密钥(私钥解密)';

-- 用户表头列配置表 新增字段
ALTER TABLE `zksr-cloud`.`sys_user_column`
    ADD COLUMN `align` varchar(20) DEFAULT NULL  COMMENT '对齐方式 （left，center，right）' AFTER `fixed_flag`,
    ADD COLUMN `width` INT(10) DEFAULT NULL  COMMENT '列宽度' AFTER `align`,
    ADD COLUMN `min_width` INT(10) DEFAULT NULL  COMMENT '列最小宽度' AFTER `width`,
    ADD COLUMN `use_slot` varchar(20) DEFAULT NULL  COMMENT '是否使用插槽' AFTER `min_width`,
    ADD COLUMN `overflow` varchar(20) DEFAULT NULL  COMMENT '是否隐藏多行' AFTER `use_slot`
    ;

------------------------          生产环境已执行脚本  2024-08-19 19:20         -----------------------
-- 用户表添加 唯一索引
CREATE UNIQUE INDEX idx_unique_userName ON `zksr-cloud`.`sys_user` (user_name);
CREATE UNIQUE INDEX idx_unique_phonenumber ON `zksr-cloud`.`sys_user` (phonenumber);
------------------------          安得环境已执行脚本  2024-08-20 11:20         -----------------------



ALTER TABLE `zksr-account`.`acc_offline_divide`
    MODIFY COLUMN `voucher_pic` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '凭证图片' AFTER `total_order_amt`;
-- 开放能力增加 订单是否自动推送第三方 0手动推送  1 自动推送 、 订单延时推送时间
ALTER TABLE `zksr-cloud`.`sys_opensource`
    ADD COLUMN `order_auto_push` tinyint(1) NULL DEFAULT 1 COMMENT '订单是否自动推送第三方 0-手动推送 1-自动推送（默认自动推送）' AFTER `logistics_info`,
    ADD COLUMN `order_delay_push_time` tinyint(1) NULL COMMENT '订单延时推送时间类型' AFTER `logistics_info`;

-- 新增手动推送入驻商订单MQ
--application-rocketmq-trade-dev.yml
        #手动推送入驻商订单
        syncDataEvent-out-0:
          destination: syncDataEvent

-- 后台订单模块新增权限 订单手动推送
-- INSERT INTO `zksr-cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '订单手动推送至第三方', 2262, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierOrder:sync', '#', 'zksr', '2024-08-21 17:49:50', '', NULL, '', 'supplier');

-- 订单表添加 已退款支付金额和已退款手续费金额
ALTER TABLE `zksr-trade`.`trd_order`
    ADD COLUMN `refund_pay_amt` decimal(12,2) DEFAULT 0 COMMENT '退款支付金额' AFTER `pay_fee`,
    ADD COLUMN `refund_pay_fee` decimal(12,2) DEFAULT 0 COMMENT '退款手续费金额' AFTER `refund_pay_amt`
    ;

-- 售后订单明细表添加 退款失败原因
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
    ADD COLUMN `refund_fail_reason` varchar(255) DEFAULT NULL COMMENT '退款支付金额' AFTER `refund_state`
;

-- zksr-gateway-dev.yml 添加 接口白名单
# 发送短信验证码
- /auth/sendSmsCode
# 校验短信验证码
- /auth/checkSmsCode
# 更新用户密码
- /auth/updatePwd

-------------------------          生产环境已执行脚本  2024-08-23 11:20         -----------------------
-- 开放能力字段调整
ALTER TABLE `zksr-cloud`.`sys_opensource`
    CHANGE  COLUMN `erp_url` `send_url` varchar(255),
    CHANGE  COLUMN `strategy_id` `send_code` varchar(255),
    ADD COLUMN `visual_master_id` bigint(20) NULL COMMENT '可视化接口配置主表ID';


-- 可视化接口主表字段调整
ALTER TABLE `zksr-cloud`.`visual_setting_master`
    DROP COLUMN `send_code`,
    DROP COLUMN `supplier_id`,
    DROP COLUMN `sys_code`,
    ADD COLUMN `common_setting_type` tinyint(1) NULL COMMENT '获取公共配置方式(0可视化接口配置 1入驻商配置 2优先入驻商配置)';

-- 可视化接口模板增加字段
ALTER TABLE `zksr-cloud`.`visual_setting_template`
    ADD COLUMN `source_type` int(4) NULL COMMENT '系统类型(数据字典sync_source_type 枚举：syncSourceType) ';

-- 可视化接口模板需要更新字段
update `zksr-cloud`.`visual_setting_template` template
    JOIN `zksr-cloud`.`visual_setting_detail` detail on detail. visual_template_id = template.visual_template_id
    JOIN `zksr-cloud`.`visual_setting_master` master on master.visual_master_id = detail.visual_master_id
    set template.source_type = master.source_type , template.api_template = detail.api_template
where master.send_type = 'visual'

-- 可视化接口详情删除字段
ALTER TABLE `zksr-cloud`.`visual_setting_detail`
DROP COLUMN `api_name`,
	DROP COLUMN `supplier_id`,
    DROP COLUMN `api_template`;

--  新增 业务员门店目标设置表
CREATE TABLE `mem_colonel_branch_target` (
                                             `colonel_branch_target_id` bigint(20) NOT NULL COMMENT '主键ID',
                                             `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                             `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                             `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                             `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                             `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                             `colonel_id` bigint(20) DEFAULT NULL COMMENT '业务员Id',
                                             `branch_id` bigint(20) DEFAULT NULL COMMENT '门店Id',
                                             `branch_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
                                             `branch_contact_name` varchar(50) DEFAULT NULL COMMENT '门店联系人',
                                             `branch_contact_phone` varchar(20) DEFAULT NULL COMMENT '门店联系电话',
                                             `target_year` varchar(4) DEFAULT NULL COMMENT '目标年份',
                                             `target_month` varchar(2) DEFAULT NULL COMMENT '目标月份',
                                             `target_sales_money` decimal(19,6) DEFAULT NULL COMMENT '目标销售额',
                                             `memo` varchar(255) DEFAULT NULL COMMENT '备注',
                                             `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除状态(0:正常，2：删除)',
                                             PRIMARY KEY (`colonel_branch_target_id`)
) COMMENT='业务员门店目标设置';

-- 本地展示分类默认排序号
ALTER TABLE `zksr_product`.`prdt_area_class`
    MODIFY COLUMN `sort` int(11) NULL DEFAULT 0 COMMENT '排序' AFTER `icon`;

-------------------------          生产环境已执行脚本  2024-08-29 19:50         -----------------------

-- 首页状态支持多渠道
ALTER TABLE `zksr-cloud`.`sys_pages_config` MODIFY COLUMN `channel_id` text NULL COMMENT '渠道ID' AFTER `page_config`;


ALTER TABLE `zksr-trade`.`trd_hdfk_pay` MODIFY COLUMN `pay_source` int(2) NULL DEFAULT NULL COMMENT '付款单来源,0-app, 1-后台' AFTER `platform`;

-- 打印模版支持
CREATE TABLE `sys_print_settings` (
                                      `print_setter_id` bigint(20) NOT NULL COMMENT '主键',
                                      `module_name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模块名称',
                                      `module_type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模块类型',
                                      `template_width` decimal(19,6) DEFAULT NULL COMMENT '模板宽度',
                                      `template_height` decimal(19,6) DEFAULT NULL COMMENT '模板高度',
                                      `paper_width` decimal(19,6) DEFAULT NULL COMMENT '纸张宽度',
                                      `paper_height` decimal(19,6) DEFAULT NULL COMMENT '纸张高度',
                                      `print_content` json DEFAULT NULL COMMENT '打印内容',
                                      `sys_code` bigint(20) DEFAULT NULL COMMENT '平台ID',
                                      `remark` varchar(2000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
                                      `del_flag` tinyint(4) DEFAULT '0' COMMENT '删除标志(0正常;1删除)',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `create_by` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                                      `update_by` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
                                      PRIMARY KEY (`print_setter_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='打印设置';

ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD COLUMN `hdfk_pay_id` bigint(20) NULL COMMENT '货到付款单id' AFTER `brand_id`;


ALTER TABLE `zksr-trade`.`trd_hdfk_pay`
    ADD COLUMN `tips` varchar(255) NULL COMMENT '备注' AFTER `pay_source`,
    ADD COLUMN `voucher` varchar(255) NULL COMMENT '凭证' AFTER `tips`;

ALTER TABLE `zksr-trade`.`trd_hdfk_pay`
    MODIFY COLUMN `pay_way` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式0-在线支付, 6-线下支付' AFTER `pay_state`;

-------------------------          生产环境已执行脚本  2024-09-03 20:15         -----------------------


-- 第三方物流新增 备注字段
ALTER TABLE `zksr_trade`.`trd_express_status`
    add COLUMN `memo` varchar(255) NULL COMMENT '备注';

-- B2B接收接口调整
update `zksr-cloud`.`sys_openability` set ability_key = 'confirmReturn',ability_name = '退货确认' where ability_key = 'receiveSalesReturn';
update `zksr-cloud`.`sys_openability` set ability_key = 'confirmReceipt',ability_name = '订单收货确认' where ability_key = 'receiveRejectableReturnSave';
update `zksr-cloud`.`sys_openability` set ability_key = 'delivery',ability_name = '订单发货' where ability_key = 'receiveOrderOutboundReturn';
update `zksr-cloud`.`sys_openability` set ability_key = 'orderLog',ability_name = '订单状态' where ability_key = 'receiveStateReturn';
update `zksr-cloud`.`sys_openability` set ability_key = 'savePrdtDate',ability_name = '接收商品生产日期' where ability_key = 'ProductionDate';
update `zksr-cloud`.`sys_openability` set ability_key = 'savePrdtStock',ability_name = '接收商品库存' where ability_key = 'receivePrdInventory';
update `zksr-cloud`.`sys_openability` set ability_key = 'savePrdt',ability_name = '新增或者修改商品信息' where ability_key = 'addOrUpdateSpu';
update `zksr-cloud`.`sys_dict_data` set dict_label = '订单收货确认'  where dict_label = '接收拒收退货单';
update `zksr-cloud`.`sys_dict_data` set dict_label = '退货确认'  where dict_label = '售后接口-销售/拒收退货回传';
update `zksr-cloud`.`sys_dict_data` set dict_label = '订单发货'  where dict_label = '订单出库回传';
update `zksr-cloud`.`sys_dict_data` set dict_label = '订单状态'  where dict_label = '配送订单状态回传';

-- 货到付款上传那么多凭证干啥???   不知道啊 毕竟是欠钱嘛严谨亿点点咯
ALTER TABLE `trd_hdfk_pay` MODIFY COLUMN `voucher` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '凭证' AFTER `tips`;

-- 打印模版, 纸张类型调整
ALTER TABLE `zksr-cloud`.`sys_print_settings` ADD COLUMN `paper_type` varchar(32) NULL COMMENT '纸张类型' AFTER `update_by`;

-- 用户门店注册表新增推广业务员ID
ALTER TABLE `zksr-member`.`mem_member_register`
    ADD COLUMN `colonel_id`BIGINT(20) NULL COMMENT '推广业务员ID'
;
# application-rocketmq-system-dev.yml
spring:
  cloud:
    stream:
      function:
        definition: "openapi_saveprdt;openapi_saveprdt_date;openapi_saveprdt_stock;openapi_confirm_receipt;openapi_confirm_return;openapi_delivery;openapi_orderlog"
      bindings:
        #对外接口:商品保存
        openapi_saveprdt-out-0:
          destination: openapi_saveprdt
        openapi_saveprdt-in-0:
          destination: openapi_saveprdt
          group: openapi_saveprdt_group

        #对外接口:商品生产日期更新
        openapi_saveprdt_date-out-0:
          destination: openapi_saveprdt_date
        openapi_saveprdt_date-in-0:
          destination: openapi_saveprdt_date
          group: openapi_saveprdt_date_group

        #对外接口:商品库存更新
        openapi_saveprdt_stock-out-0:
          destination: openapi_saveprdt_stock
        openapi_saveprdt_stock-in-0:
          destination: openapi_saveprdt_stock
          group: openapi_saveprdt_stock_group

        #对外接口:订单收货确认
        openapi_confirm_receipt-out-0:
          destination: openapi_confirm_receipt
        openapi_confirm_receipt-in-0:
          destination: openapi_confirm_receipt
          group: openapi_confirm_receipt_group

        #对外接口:售后退货确认
        openapi_confirm_return-out-0:
          destination: openapi_confirm_return
        openapi_confirm_return-in-0:
          destination: openapi_confirm_return
          group: openapi_confirm_return_group

        #对外接口:订单发货
        openapi_delivery-out-0:
          destination: openapi_delivery
        openapi_delivery-in-0:
          destination: openapi_delivery
          group: openapi_delivery_group

        #对外接口:订单状态
        openapi_orderlog-out-0:
          destination: openapi_orderlog
        openapi_orderlog-in-0:
          destination: openapi_orderlog
          group: openapi_orderlog_group


# zksr-gateway-dev.yml
security:
  ignore:
    whites
    # 开放接口
      - /system/openapi/token/getToken


/**
    时间:2024-09-03
    zhengsenbing
    sku表新增最后库存更新时间
 */
ALTER TABLE `prdt_sku`
    ADD COLUMN `last_update_time` datetime(3) DEFAULT NULL COMMENT '最后库存更新时间';


-- 对外同步日志表 增加 sys_opensource的 opensourceId
ALTER TABLE `zksr-cloud`.`sys_interface_log`
    ADD COLUMN `opensource_id` bigint(20) DEFAULT NULL COMMENT '开放能力Id';

-- 入驻商添加全国起送价
ALTER TABLE `zksr-cloud`.`sys_supplier`
    ADD COLUMN `global_min_amt` decimal(12,2) DEFAULT '0.00' COMMENT '全国起送价' after min_amt
;

---------------------          生产环境已执行脚本  2024-09-10 19:40         -----------------------

CREATE TABLE `sys_print_template` (
                                      `print_template_id` bigint(20) NOT NULL COMMENT '主键',
                                      `module_name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模块名称',
                                      `module_type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模块类型',
                                      `template_width` decimal(19,6) DEFAULT NULL COMMENT '模板宽度',
                                      `template_height` decimal(19,6) DEFAULT NULL COMMENT '模板高度',
                                      `paper_width` decimal(19,6) DEFAULT NULL COMMENT '纸张宽度',
                                      `paper_height` decimal(19,6) DEFAULT NULL COMMENT '纸张高度',
                                      `print_content` json DEFAULT NULL COMMENT '打印内容',
                                      `remark` varchar(2000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
                                      `del_flag` tinyint(4) DEFAULT '0' COMMENT '删除标志(0正常;1删除)',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `create_by` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                                      `update_by` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
                                      `paper_type` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '纸张类型',
                                      PRIMARY KEY (`print_template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='打印模版';

/**
  一级管理分类, 销售占比利润比例
 */
ALTER TABLE `prdt_catgory` ADD COLUMN `sale_total_rate` decimal(10, 4) NULL COMMENT '销售占比利润, 百分比,最高29% = 0.29, 只有一级可以设置' AFTER `catgory_no`;

/**
  入驻商与一级管理分类绑定的分润占比数据
 */
CREATE TABLE `prdt_supplier_class_rate` (
                                            `catgory_id` bigint(20) NOT NULL COMMENT '平台商管理分类id;平台商管理分类id',
                                            `supplier_id` bigint(20) NOT NULL COMMENT '入驻商id;入驻商id',
                                            `sys_code` bigint(20) NOT NULL COMMENT '平台商id;平台商id',
                                            `sale_total_rate` decimal(10,4) DEFAULT NULL COMMENT '销售分润占比, 最大0.29 = 29%'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_croatian_ci COMMENT='入驻商-平台商管理分类 关联关系, 一级管理分类销售分润占比';
------------生产环境已执行脚本  2024-09-13 19:54---------

-- 入驻商订单结算信息 添加 字段
ALTER TABLE `zksr-trade`.`trd_supplier_order_settle`
    ADD COLUMN `sale_total_rate` decimal(10,4) DEFAULT 0 COMMENT '消费分润占比, 最大0.29 = 29%' after cost_price,
    ADD COLUMN `profit_model` varchar(2) DEFAULT null  COMMENT '利润模式: 0=(售价*比例=利润), 1=(售价-进货价=利润)' after sale_total_rate

-- 入驻商售后订单结算信息 添加 字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_settle`
    ADD COLUMN `sale_total_rate` decimal(10,4) DEFAULT 0 COMMENT '消费分润占比, 最大0.29 = 29%；从订单表来' after cost_price,
    ADD COLUMN `profit_model` varchar(2) DEFAULT null  COMMENT '利润模式: 0=(售价*比例=利润), 1=(售价-进货价=利润)；从订单表来' after sale_total_rate


ALTER TABLE `prdt_sku` ADD COLUMN `sale_total_rate` decimal(10, 4) NULL COMMENT '销售分润占比, 最大0.29 = 29%';

ALTER TABLE `prdt_supplier_class_rate` ADD INDEX `idx_supplier_id`(`supplier_id`);


-- 新增可视化收款单  MQ配置
--application-rocketmq-trade-dev.yml

#收款单信息同步
syncDataReceiptEvent-out-0:
          destination: syncDataReceiptEvent


--application-rocketmq-system-dev.yml
definition:"syncDataReceiptEvent"
        #收款单信息同步
        syncDataReceiptEvent-out-0:
          destination: syncDataReceiptEvent
        syncDataReceiptEvent-in-0:
          destination: syncDataReceiptEvent
          group: syncDataReceiptEventGroup


------------------------          安得环境已执行脚本  2024-09-18 15:40         -----------------------


--openAPI新增 订单接收成功通知 接口
-- application-rocketmq-system-dev.yml
spring:
  cloud:
    stream:
      function:
        definition: "openapi_order_receive_callback"
      bindings:
        #对外接口:订单接收成功通知
        openapi_order_receive_callback-out-0:
          destination: openapi_order_receive_callback
        openapi_order_receive_callback-in-0:
          destination: openapi_order_receive_callback
          group: openapi_order_receive_callback_group

--openAPI新增 退单接收成功通知 接口
-- application-rocketmq-system-dev.yml
spring:
  cloud:
    stream:
      function:
        definition: "openapi_after_order_receive_callback"
      bindings:
        #对外接口:退单接收成功通知
        openapi_after_order_receive_callback-out-0:
          destination: openapi_after_order_receive_callback
        openapi_after_order_receive_callback-in-0:
          destination: openapi_after_order_receive_callback
          group: openapi_after_order_receive_callback_group


--入驻商订单详情增加库存同步标识字段
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD COLUMN `sync_stock` tinyint(1) DEFAULT 0 COMMENT '同步库存标识 0否 1是';

--入驻商订单  推送状态 设置默认值
ALTER TABLE `zksr-trade`.`trd_supplier_order`
    MODIFY COLUMN `push_status` tinyint(1) DEFAULT 0 COMMENT '推送状态 0未推送 1已推送 2已接收';
/**
 * 微信b2b进件调整
 */
ALTER TABLE `acc_platform_merchant`
    ADD COLUMN `out_link` varchar(255) NULL COMMENT '外部认证链接' AFTER `edit_msg`;

/**
  企业地址
 */
ALTER TABLE `zksr-account`.`acc_platform_merchant`
    ADD COLUMN `id_card_address` varchar(64) NULL COMMENT '身份证地址' AFTER `out_link`,
    ADD COLUMN `licence_address` varchar(64) NULL COMMENT '企业地址' AFTER `id_card_address`;


/**
  记录微信B2B支付分账请求记录
 */
CREATE TABLE `acc_divide_flow` (
                                   `divide_flow_id` bigint(20) NOT NULL COMMENT '分账流水id',
                                   `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                   `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                   `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                   `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                   `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                   `platform` varchar(16) DEFAULT NULL COMMENT '支付平台(数据字典)',
                                   `type` tinyint(2) DEFAULT NULL COMMENT '1-请求分账  2-请求分账回退 3-分账完成',
                                   `trade_no` varchar(32) DEFAULT NULL COMMENT '订单号',
                                   `out_trade_no` varchar(32) DEFAULT NULL COMMENT '支付平台商户订单号',
                                   `out_refund_no` varchar(32) DEFAULT NULL COMMENT '支付平台商户退款单号',
                                   `source_platform_merchant_id` bigint(20) DEFAULT NULL COMMENT '支付平台分账发起方商户id',
                                   `target_platform_merchant_id` bigint(20) NOT NULL COMMENT '支付平台分账接收方商户id',
                                   `mchid` varchar(32) DEFAULT NULL COMMENT '子商户id',
                                   `payee_type` varchar(32) DEFAULT NULL COMMENT '分账接收方类型',
                                   `payee_no` varchar(32) DEFAULT NULL COMMENT '分账接收方账号',
                                   `divide_amt` decimal(12,2) DEFAULT NULL COMMENT '分账金额',
                                   `divide_status` tinyint(2) DEFAULT NULL COMMENT '仅type=1, 1: 分账中 2: 分账完成 3：分账失败',
                                   `divide_reverse_status` tinyint(2) DEFAULT NULL COMMENT '仅type=2, 1: 分账退回中 2: 分账退回完成 3: 分账退回失败',
                                   `err_msg` varchar(255) DEFAULT NULL COMMENT '错误信息(仅失败记录)',
                                   PRIMARY KEY (`divide_flow_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)';


-- 增加入驻商配置是否同步价格
ALTER TABLE `sys_opensource`
    ADD COLUMN `sync_price` tinyint(1) DEFAULT '1' COMMENT '是否同步价格 0-不同步 1-同步（默认同步）';


-- 入驻商订单明细表  管理分类id
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD COLUMN `category_id` bigint(20) DEFAULT NULL COMMENT '管理分类id';
-- 入驻商售后订单明细表  管理分类id
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
    ADD COLUMN `category_id` bigint(20) DEFAULT NULL COMMENT '管理分类id；从订单表来';

-- 第三方系统抓取数据接口配置
INSERT INTO `zksr-cloud`.`sys_openability` (`create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES ('zksr', NOW(), 'zksr', NOW(), NULL, 'supplier', '获取门店数据', 'getBranchData', '0', 10);
INSERT INTO `zksr-cloud`.`sys_openability` (`create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES ('zksr', NOW(), 'zksr', NOW(), NULL, 'supplier', '获取订单信息', 'getTheOrder', '0', 10);
INSERT INTO `zksr-cloud`.`sys_openability` (`create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES ('zksr', NOW(), 'zksr', NOW(), NULL, 'supplier', '获取退货订单', 'getAfterOrder', '0', 10);
INSERT INTO `zksr-cloud`.`sys_openability` (`create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES ('zksr', NOW(), 'zksr', NOW(), NULL, 'supplier', '退单接收成功通知', 'afterOrderReceiveCallback', '0', 10);


ALTER TABLE `trd_supplier_after`
    ADD COLUMN `push_status` tinyint(1) DEFAULT '0' COMMENT '推送状态 0未推送 1已推送 2已接收';

-- sku增加已同步库存
ALTER TABLE `prdt_sku`
    ADD COLUMN `synced_qty` int(11) NULL DEFAULT 0 COMMENT '已同步库存';

-- 分账发起状态
ALTER TABLE `acc_divide_flow`
    ADD COLUMN `divide_req_status` tinyint(2) NULL COMMENT '分账请求发起状态, 0-成功, 1-失败';

-- 新增数据字典值 【售后订单处理状态】
INSERT INTO `zksr-cloud`.`sys_dict_data`( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES ( 0, '已取消', '10', 'sys_after_handle_state', NULL, 'default', 'N', '0', 'zksr', '2024-06-28 14:51:53', '', NULL, NULL);

-- 微信B2B支付, 商户信息
CREATE TABLE `acc_platform_merchant_wxb2b` (
                                               `wx_b2b_platform_merchant_id` bigint(20) NOT NULL,
                                               `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                               `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                               `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                               `min_withdraw_amt` decimal(12,2) DEFAULT NULL COMMENT '最小账户保留金',
                                               `auto_withdraw` tinyint(2) DEFAULT '0' COMMENT '0-未开启,1-已开启',
                                               `app_key` varchar(255) DEFAULT NULL COMMENT '支付秘钥',
                                               `merchant_type` varchar(16) NOT NULL COMMENT '商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店',
                                               `merchant_id` bigint(20) NOT NULL COMMENT '商户id',
                                               PRIMARY KEY (`wx_b2b_platform_merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付平台商户表, 微信B2B平台商户扩展信息';
ALTER TABLE `acc_platform_merchant` ADD COLUMN `alt_mch_key` varchar(64) NULL COMMENT '商户key' AFTER `alt_mch_name`;
ALTER TABLE `acc_platform_merchant`ADD INDEX `idx_merchant_id`(`merchant_id`);
ALTER TABLE `acc_platform_merchant_wxb2b` ADD COLUMN `profile` varchar(64) NULL COMMENT '微信头像';
ALTER TABLE `acc_platform_merchant_wxb2b` ADD COLUMN `nick_name` varchar(64) NULL COMMENT '微信昵称';


ALTER TABLE `acc_divide_flow` ADD COLUMN `acc_pay_flow_id` bigint(20) NULL COMMENT '支付acc_pay_flow_id' AFTER `err_msg`;

-- 分账处理时间, 分账数据关联分账流水
ALTER TABLE `acc_divide_dtl`
    ADD COLUMN `divide_flow_id` bigint(20) NULL COMMENT 'B2B支付分账流水ID' AFTER `order_type`,
    ADD COLUMN `process_time` datetime NULL COMMENT '分账处理时间' AFTER `divide_flow_id`;

/**
  2024年9月25日
  商城首页装修支持二级页面
 */
ALTER TABLE `sys_pages_config`
    ADD COLUMN `pid` bigint(20) NULL COMMENT '父级页面' AFTER `json_url`,
    ADD COLUMN `level` tinyint(2) NULL COMMENT '1-一级页面, 2-二级页面' AFTER `pid`;
ALTER TABLE `sys_pages_config` MODIFY COLUMN `level` tinyint(2) NULL DEFAULT 1 COMMENT '1-一级页面, 2-二级页面' AFTER `pid`;
ALTER TABLE `sys_pages_config` ADD COLUMN `has_child` tinyint(2) NULL DEFAULT 0 COMMENT '0-没有子页面,1-有子页面' AFTER `level`;


-- 调整 售后优惠记录表结构
ALTER TABLE `zksr_trade`.`trd_after_discount_dtl`
    MODIFY COLUMN `sku_id` BIGINT(20) DEFAULT NULL COMMENT '商品sku id' ,
    MODIFY COLUMN `gift_coupon_template_id` bigint(20) DEFAULT NULL COMMENT '赠品sku优惠券模板',
    MODIFY COLUMN `gift_sku_id` bigint(20) DEFAULT NULL COMMENT '赠品sku;gift_type=0 则记录;gift_type=1 则记录';

------------生产环境已执行脚本  2024-09-26 17:54---------

ALTER TABLE `zksr_trade`.`trd_supplier_order_settle`
    ADD COLUMN `refund_pay_amt` decimal(12,2) DEFAULT 0 COMMENT '退款支付金额' AFTER `pay_fee`,
    ADD COLUMN `refund_pay_fee` decimal(12,2) DEFAULT 0 COMMENT '退款手续费金额' AFTER `refund_pay_amt`;

/**
  业务员提成统计
 */
ALTER TABLE `trd_settle` ADD INDEX `idx_merchant_id`(`merchant_id`);

/**
  结算金额默认值调整
 */
ALTER TABLE `acc_transfer` MODIFY COLUMN `settle_amt` decimal(12, 2) NULL DEFAULT 0 COMMENT '转账发起方解除金额' AFTER `transfer_amt`;
UPDATE acc_transfer set settle_amt = 0 WHERE settle_amt is null;


-- 入驻商售后订单详情增加库存同步标识字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
    ADD COLUMN `sync_stock` tinyint(1) DEFAULT 0 COMMENT '同步库存标识 0否 1是';

-- 微信商户信息头像长度调整
ALTER TABLE `acc_platform_merchant_wxb2b`
    MODIFY COLUMN `profile` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信头像' AFTER `merchant_id`;

-- 分账单号可以非必填
ALTER TABLE `acc_divide_dtl`
    MODIFY COLUMN `alt_mch_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分账方商户编号' AFTER `merchant_id`;


-- 调整 可视化配置主表 字段长度
ALTER TABLE `zksr_cloud`.`visual_setting_master`
    MODIFY COLUMN `send_url` varchar(255)  DEFAULT NULL COMMENT '对接地址';

------------生产环境已执行脚本  2024-10-11 20:54---------


-- 售后订单详情 增加 关联销售订单明细id 索引
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
    ADD INDEX `idx_supplier_order_dtl_id`(`supplier_order_dtl_id`);


-- 中单位、大单位状态
ALTER TABLE `zksr-product`.`prdt_sku`
ADD COLUMN `mid_status` tinyint(1) DEFAULT 1 COMMENT '中单位状态 1-启用 0-停用',
ADD COLUMN `large_status` tinyint(1) DEFAULT 1 COMMENT '大单位状态 1-启用 0-停用';

-- 售后订单明细优惠详情 更新 字段类型
ALTER TABLE `zksr_trade`.`trd_after_discount_dtl`
    MODIFY COLUMN `sys_code` bigint(20)  DEFAULT NULL COMMENT '平台商id';

-- 促销表字段长度兼容
ALTER TABLE `prm_sp_rule` MODIFY COLUMN `sys_code` bigint(20) NOT NULL COMMENT '平台商id' AFTER `sp_rule_id`;
ALTER TABLE `prm_sk_rule` MODIFY COLUMN `sys_code` bigint(20) NOT NULL COMMENT '平台商id' AFTER `sk_rule_id`;


-- 2024年10月14日09:12:56 王珂 商户解绑菜单
-- INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2591, '解除入驻商商户绑定', 2492, 36, '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:remove', '#', 'zksr', '2024-10-14 09:05:39', '', NULL, '', 'dc,partner,software');
-- INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2590, '解除平台商商户绑定', 2492, 34, '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-partner:remove', '#', 'zksr', '2024-10-14 09:05:11', '', NULL, '', 'partner,software');
-- INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2589, '解除运营商商户绑定', 2492, 33, '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-dc:remove', '#', 'zksr', '2024-10-14 09:04:51', '', NULL, '', 'software,partner,dc');
-- INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2588, '解除业务员商户绑定', 2492, 32, '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-colonel:remove', '#', 'zksr', '2024-10-14 09:04:00', 'zksr', '2024-10-14 09:04:27', '', 'dc,partner,software');

-- 2024年10月14日14:15:10 支付手续费费率, 微信商户
ALTER TABLE `acc_platform_merchant_wxb2b` ADD COLUMN `profit_rate` decimal(10, 4) NULL COMMENT '支付手续费费率' AFTER `nick_name`;

-- 入驻商售后订单详情增加 退货审核时间 字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
    ADD COLUMN `return_approve_time` datetime NULL COMMENT '退货审核时间' AFTER `finish_time`;

------------生产环境已执行脚本  2024-10-14 19:34---------

-- 独立退款单号支持, 支持验证业务方是否多次发起退款
ALTER TABLE `acc_pay_flow` ADD COLUMN `refund_status` TINYINT (4) NULL DEFAULT NULL COMMENT '退款状态,0-未退款,1-发起退款成功,2-退款成功,3-退款失败,4-退款中' AFTER `callback_time`;
-- 第三方系统抓取收款单数据接口配置
INSERT INTO `zksr-cloud`.`sys_openability` (`create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES ('zksr', NOW(), 'zksr', NOW(), NULL, 'supplier', '获取收款单', 'getReceipt', '0', 10);
------------生产环境已执行脚本  2024-10-15 22:18---------

-- 可视化接口详情增加 可视化收款单类型 字段
ALTER TABLE `zksr-cloud`.`visual_setting_detail`
    ADD COLUMN `visual_receipt_type` varchar(64) NULL COMMENT '可视化收款单类型（多个按逗号隔开）' AFTER `resp_msg`;

-- nacos增加配置：
--application-rocketmq-trade-dev.yml
--definition增加消费者：
orderToSyncDataReceiptEvent
-- bindings通道增加消费者：   放入[orderPayUpdateSuccessEvent]生产者下
orderToSyncDataReceiptEvent-in-0:
          destination: orderPayUpdateSuccessEvent
          group: orderToSyncDataReceiptEventGroup

-- 新增数据字典配置 可视化收款单类型
INSERT INTO `zksr-cloud`.`sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('可视化推送收款单类型', 'visual_receipt_type', '0', 'zksr', '2024-10-17 15:35:28', 'zksr', '2024-10-17 15:36:07', '用于推送给第三方的收款单类型');

INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '货到付款已付款-销售订单', '11', 'visual_receipt_type', NULL, 'default', 'N', '0', 'zksr', '2024-10-17 15:36:41', '', NULL, '货到付款已付款-销售订单');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '在线支付已付款-销售订单', '12', 'visual_receipt_type', NULL, 'default', 'N', '0', 'zksr', '2024-10-17 15:36:54', '', NULL, '在线支付已付款-销售订单');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '货到付款未付款-售后订单', '21', 'visual_receipt_type', NULL, 'default', 'N', '0', 'zksr', '2024-10-17 15:37:12', 'zksr', '2024-10-17 15:37:25', '货到付款未付款-售后订单');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '在线支付已付款-售后订单', '22', 'visual_receipt_type', NULL, 'default', 'N', '0', 'zksr', '2024-10-17 15:37:33', '', NULL, '在线支付已付款-售后订单');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '在线支付已付款-差异售后订单', '23', 'visual_receipt_type', NULL, 'default', 'N', '0', 'zksr', '2024-10-17 15:37:50', '', NULL, '在线支付已付款-差异售后订单');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '在线支付已付款-拒收售后订单', '24', 'visual_receipt_type', NULL, 'default', 'N', '0', 'zksr', '2024-10-17 15:38:06', '', NULL, '在线支付已付款-拒收售后订单');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '货到付款已付款-售后订单', '25', 'visual_receipt_type', NULL, 'default', 'N', '0', 'zksr', '2024-10-17 15:38:06', '', NULL, '货到付款已付款-售后订单');


-- 开放能力对象增加 是否开启统配入驻商 字段
ALTER TABLE `zksr-cloud`.`sys_opensource`
    ADD COLUMN `super_supplier` tinyint(1) DEFAULT 0 COMMENT '是否开启统配入驻商  0否 1是';
------------生产环境已执行脚本  2024-10-19 21:18---------


-- Nacos增加配置 openAPI新增 新增入驻商接口
# application-rocketmq-system-dev.yml
spring:
  cloud:
    stream:
      function:
        definition: "openapi_savesupplier"
      bindings:
        #对外接口:新增入驻商
        openapi_savesupplier-out-0:
          destination: openapi_savesupplier
        openapi_savesupplier-in-0:
          destination: openapi_savesupplier
          group: openapi_savesupplier_group

    -- 第三方系统新增入驻商信息接口配置
INSERT INTO `zksr-cloud`.`sys_openability` (`create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES ('zksr', NOW(), 'zksr', NOW(), NULL, 'supplier', '新增入驻商信息', 'addSupplier', '0', 10);



-- nacos 添加配置   account MQ新增消息, 店小盟环境已经新增, 无需新增, 建议安得等其他环境, 复制account-mq整个文件配置, 记得修改name-server ip
wxOrderDivideDelayQuery

# 处理微信异步退款之前需要等待分账完成调整
wxOrderDivideDelayQuery-out-0:
  destination: wxOrderDivideDelayQuery
wxOrderDivideDelayQuery-in-0:
  destination: wxOrderDivideDelayQuery
  consumer:
    concurrency: 3
  group: wxOrderDivideDelayQueryGroup


-- 入驻商订单详情增加 订单购买单位实际销售单价 字段
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD COLUMN `order_sales_unit_price` DECIMAL(18,6) NULL COMMENT '订单购买单位实际销售单价' AFTER `order_unit_price`;

-- 入驻商售后订单详情增加 订单购买单位实际销售单价 字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
    ADD COLUMN `return_sales_unit_price` DECIMAL(18,6) NULL COMMENT '售后购买单位实际销售单价' AFTER `return_unit_size`;

------------生产环境已执行脚本  2024-10-24 20:48---------

-- 2024年10月28日10:33:03, 进件商户号字典值补充
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (430, 0, '账户待签约', 'UNSIGNED', 'platform_merchant_audit_status', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:30:33', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (431, 0, '待账户验证', 'ACCOUNT_NEED_VERIFY', 'platform_merchant_audit_status', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:30:25', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (432, 0, '审核中', 'AUDITING', 'platform_merchant_audit_status', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:30:12', '', NULL, NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (82, '提现转账状态', 'account_withdraw_transfer_state', '0', 'zksr', '2024-10-28 10:35:15', '', NULL, '提现单转账状态');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (83, '提现结算状态', 'account_withdraw_settle_state', '0', 'zksr', '2024-10-28 10:35:15', '', NULL, '提现单结算状态');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (86, '提现状态', 'account_withdraw_state', '0', 'zksr', '2024-10-28 10:35:15', '', NULL, '提现单检索状态');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (433, 0, '提现失败', '4', 'account_withdraw_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:41:45', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (434, 0, '提现成功', '3', 'account_withdraw_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:41:39', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (435, 0, '审核拒绝', '2', 'account_withdraw_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:41:34', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (436, 0, '提现处理中', '1', 'account_withdraw_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:41:27', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (437, 0, '初始化', '0', 'account_withdraw_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:41:21', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (438, 0, '结算失败', '15', 'account_withdraw_settle_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:39:54', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (443, 0, '结算成功', '14', 'account_withdraw_settle_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:39:47', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (439, 0, '结算中', '13', 'account_withdraw_settle_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:39:41', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (440, 0, '转账失败', '12', 'account_withdraw_transfer_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:37:49', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (441, 0, '转账成功', '11', 'account_withdraw_transfer_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:37:44', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (442, 0, '转账中', '10', 'account_withdraw_transfer_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:37:34', '', NULL, NULL);

------------生产环境已执行脚本  2024-10-29 09:14---------
-- 货到付款付款单 状态增加 无需付款
ALTER TABLE `zksr-trade`.`trd_hdfk_pay`
    MODIFY COLUMN `pay_state` int(1) DEFAULT NULL COMMENT '支付状态（数据字典sys_pay_state）；0-未支付 1-已支付 2-无需付款';
-- 入驻商信息配置信息增加 是否开启货到付款清账功能
ALTER TABLE `zksr-cloud`.`sys_opensource`
    ADD COLUMN `is_hdfk_settle` tinyint(1) DEFAULT '0' COMMENT '是否开启货到付款清账功能  0否 1是';

-- Nacos增加配置 openAPI新增 新增货到付款清账能力
# application-rocketmq-system-dev.yml
spring:
  cloud:
    stream:
      function:
        definition: "openapi_add_hdfk_settle"
      bindings:
        #对外接口:新增货到付款清账能力
        openapi_add_hdfk_settle-out-0:
          destination: openapi_add_hdfk_settle
        openapi_add_hdfk_settle-in-0:
          destination: openapi_add_hdfk_settle
          group: openapi_add_hdfk_settle_group

-- 第三方系统新增货到付款清账能力接口配置
INSERT INTO `zksr-cloud`.`sys_openability` (`create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES ('zksr', NOW(), 'zksr', NOW(), NULL, 'supplierHdfkPay', '新增货到付款清账能力', 'addHdfkSettle', '0', 10);

-- 开放能力表 资源类型字段大小从 16增加到32
ALTER TABLE `zksr-cloud`.`sys_openability`
    MODIFY COLUMN `merchant_type` varchar(32) DEFAULT NULL COMMENT '资源类型（数据字典 supplier-入驻商等）';

------------生产环境已执行脚本  2024-10-24 20:48---------
-- 2024年10月28日10:33:03, 进件商户号字典值补充
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (430, 0, '账户待签约', 'UNSIGNED', 'platform_merchant_audit_status', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:30:33', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (431, 0, '待账户验证', 'ACCOUNT_NEED_VERIFY', 'platform_merchant_audit_status', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:30:25', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (432, 0, '审核中', 'AUDITING', 'platform_merchant_audit_status', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:30:12', '', NULL, NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (82, '提现转账状态', 'account_withdraw_transfer_state', '0', 'zksr', '2024-10-28 10:35:15', '', NULL, '提现单转账状态');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (83, '提现结算状态', 'account_withdraw_settle_state', '0', 'zksr', '2024-10-28 10:35:15', '', NULL, '提现单结算状态');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (86, '提现状态', 'account_withdraw_state', '0', 'zksr', '2024-10-28 10:35:15', '', NULL, '提现单检索状态');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (433, 0, '提现失败', '4', 'account_withdraw_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:41:45', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (434, 0, '提现成功', '3', 'account_withdraw_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:41:39', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (435, 0, '审核拒绝', '2', 'account_withdraw_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:41:34', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (436, 0, '提现处理中', '1', 'account_withdraw_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:41:27', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (437, 0, '初始化', '0', 'account_withdraw_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:41:21', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (438, 0, '结算失败', '15', 'account_withdraw_settle_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:39:54', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (443, 0, '结算成功', '14', 'account_withdraw_settle_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:39:47', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (439, 0, '结算中', '13', 'account_withdraw_settle_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:39:41', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (440, 0, '转账失败', '12', 'account_withdraw_transfer_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:37:49', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (441, 0, '转账成功', '11', 'account_withdraw_transfer_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:37:44', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (442, 0, '转账中', '10', 'account_withdraw_transfer_state', NULL, 'default', 'N', '0', 'zksr', '2024-10-28 10:37:34', '', NULL, NULL);

------------生产环境已执行脚本  2024-10-29 09:14---------

------------安得环境已执行脚本  2024-10-29 15:29---------

-- 第三方系统新增货到付款清账能力接口配置
INSERT INTO `zksr-cloud`.`sys_openability` (`create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES ('zksr', NOW(), 'zksr', NOW(), NULL, 'supplierHdfkPay', '新增货到付款清账能力', 'addHdfkSettle', '0', 10);

-- 开放能力表 资源类型字段大小从 16增加到32
ALTER TABLE `zksr-cloud`.`sys_openability`
    MODIFY COLUMN `merchant_type` varchar(32) DEFAULT NULL COMMENT '资源类型（数据字典 supplier-入驻商等）';


-- 2024.10.29  Nacos增加配置 货到付款无需付款 售后订单退款成功回调
# application-rocketmq-trade-dev.yml
spring:
  cloud:
    stream:
      function:
        definition: "afterOrderUpdateSuccess"
      bindings:
        #售后订单退款成功回调
        afterOrderUpdateSuccess-out-0:
          destination: afterOrderUpdateSuccess
        afterOrderUpdateSuccess-in-0:
          destination: afterOrderUpdateSuccess
          group: afterOrderUpdateSuccessGroup

-- 交易对账和提现对账需求
-- 2024-11-05 提现对账单表
CREATE TABLE `acc_withdraw_bill` (
                                     `withdraw_bill_id` bigint(20) NOT NULL COMMENT '提现账单ID',
                                     `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                     `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `platform_withdraw_amt` decimal(10,2) DEFAULT NULL COMMENT '平台提现金额',
                                     `merchant_withdraw_amt` decimal(10,2) DEFAULT NULL COMMENT '商户提现金额',
                                     `platform_free` decimal(10,2) DEFAULT NULL COMMENT '平台提现手续费',
                                     `merchant_free` decimal(10,2) DEFAULT NULL COMMENT '商户提现手续费',
                                     `platform_trade_no` varchar(64) DEFAULT NULL COMMENT '平台交易单号(外部)',
                                     `merchant_trade_no` varchar(64) DEFAULT NULL COMMENT '商户交易单号(内部)',
                                     `bank_account_no` varchar(32) DEFAULT NULL COMMENT '提现到账银行卡号',
                                     `bank_account_name` varchar(32) DEFAULT NULL COMMENT '提现到账银行账户名称',
                                     `request_time` datetime DEFAULT NULL COMMENT '提现发起时间',
                                     `finish_time` datetime DEFAULT NULL COMMENT '提现完成时间',
                                     `alt_no` varchar(32) DEFAULT NULL COMMENT '提现商户号',
                                     `state` tinyint(2) NOT NULL DEFAULT '0' COMMENT '0-正常,1-异常',
                                     `remark` varchar(32) DEFAULT NULL COMMENT '备注, 金额不对, 支付, 退款状态不对',
                                     `platform` varchar(16) DEFAULT NULL COMMENT '支付平台, hlb-合利宝,wxb2b-微信b2b',
                                     PRIMARY KEY (`withdraw_bill_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='提现对账单';

-- 2024-11-05 交易对账单明细单表
CREATE TABLE `acc_transfer_bill_order` (
                                           `bill_order_id` bigint(20) NOT NULL COMMENT '账单ID',
                                           `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                           `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           `transfer_bill_id` bigint(20) DEFAULT NULL COMMENT '交易账单id',
                                           `transfer_time` datetime DEFAULT NULL COMMENT '交易时间',
                                           `platform_pay_amt` decimal(10,2) DEFAULT NULL COMMENT '平台支付金额',
                                           `merchant_pay_amt` decimal(10,2) DEFAULT NULL COMMENT '商户支付金额',
                                           `platform_refund_amt` decimal(10,2) DEFAULT NULL COMMENT '平台退款金额',
                                           `merchant_refund_amt` decimal(10,2) DEFAULT NULL COMMENT '商户退款金额',
                                           `platform_pay_free` decimal(10,2) DEFAULT NULL COMMENT '平台支付手续费',
                                           `merchant_pay_free` decimal(10,2) DEFAULT NULL COMMENT '商户支付手续费',
                                           `platform_refund_free` decimal(10,2) DEFAULT NULL COMMENT '平台退款手续费',
                                           `merchant_refund_free` decimal(10,2) DEFAULT NULL COMMENT '商户退款手续费',
                                           `platform_trade_no` varchar(64) DEFAULT NULL COMMENT '平台交易单号(外部)',
                                           `merchant_trade_no` varchar(64) DEFAULT NULL COMMENT '商户交易单号(内部)',
                                           `alt_no` varchar(32) DEFAULT NULL COMMENT '商户号',
                                           `busi_trade_no` varchar(64) DEFAULT NULL COMMENT '业务单号(订单号, 退款单号...)',
                                           `state` tinyint(2) NOT NULL DEFAULT '0' COMMENT '0-正常,1-异常',
                                           `remark` varchar(32) DEFAULT NULL COMMENT '备注, 金额不对, 支付, 退款状态不对',
                                           `platform` varchar(16) DEFAULT NULL COMMENT '支付平台, hlb-合利宝,wxb2b-微信b2b',
                                           `order_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '0-支付, 1-退款',
                                           PRIMARY KEY (`bill_order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='交易对账单明细单';

-- 2024-11-05 交易对账单表
CREATE TABLE `acc_transfer_bill` (
                                     `transfer_bill_id` bigint(20) NOT NULL COMMENT '交易账单ID',
                                     `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                     `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `bill_date` varchar(10) DEFAULT NULL COMMENT '账单日期',
                                     `transfer_num` int(10) DEFAULT NULL COMMENT '交易笔数',
                                     `merchant_total_pay_amt` decimal(16,2) DEFAULT NULL COMMENT '商户总支付金额',
                                     `platform_total_pay_amt` decimal(16,2) DEFAULT NULL COMMENT '平台总支付金额',
                                     `merchant_total_refund_amt` decimal(16,2) DEFAULT NULL COMMENT '商户总退款金额',
                                     `platform_total_refund_amt` decimal(16,2) DEFAULT NULL COMMENT '平台总退款金额',
                                     `merchant_total_pay_free` decimal(10,2) DEFAULT NULL COMMENT '商户总支付手续费',
                                     `platform_total_pay_free` decimal(10,2) DEFAULT NULL COMMENT '平台总支付手续费',
                                     `merchant_total_refund_free` decimal(10,2) DEFAULT NULL COMMENT '商户总退款手续费',
                                     `platform_total_refund_free` decimal(10,2) DEFAULT NULL COMMENT '平台总退款手续费',
                                     `refund_count` int(10) DEFAULT NULL COMMENT '退款笔数',
                                     `platform` varchar(16) DEFAULT NULL COMMENT '支付平台, hlb-合利宝,wxb2b-微信b2b',
                                     PRIMARY KEY (`transfer_bill_id`) USING BTREE,
                                     KEY `idx_bill_date` (`bill_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='交易对账单';

-- 2024-11-05 商户账单文件备份表
CREATE TABLE `acc_bill_file` (
                                 `bill_file_id` bigint(20) NOT NULL,
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `date` date DEFAULT NULL COMMENT '账单日期',
                                 `file` text COMMENT '文件',
                                 `alt_no` varchar(32) DEFAULT NULL COMMENT '商户号',
                                 `type` varchar(32) DEFAULT NULL COMMENT '文件类型, 自定义名称, 中文',
                                 PRIMARY KEY (`bill_file_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户账单文件备份';



-- 订单分享控制
CREATE TABLE `trd_order_share` (
                                   `share_order_id` bigint(20) NOT NULL,
                                   `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                   `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                   `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                   `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                   `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                   `supplier_order_id` bigint(20) DEFAULT NULL COMMENT '入驻商订单ID',
                                   `order_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
                                   `remote_ip` varchar(32) DEFAULT NULL COMMENT '发起分享IP',
                                   `expiration_time` datetime DEFAULT NULL COMMENT '有效时间',
                                   `share_key` varchar(64) DEFAULT NULL COMMENT '分享key',
                                   `branch_id` bigint(20) DEFAULT NULL COMMENT '门店ID',
                                   PRIMARY KEY (`share_order_id`),
                                   UNIQUE KEY `idx_share_key` (`share_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单分享';

-- application-gateway.yml 增加通用白名单
    # 商城通用白名单
    - /portal/mall/public/**


      */
-- 可视化对接接口代码重构 调整配置
--application-rocketmq-trade-dev.yml
-- 移除开始：
definition:"orderToSyncDataOrderEvent"

#销售订单信息同步
        syncDataOrderEvent-out-0:
          destination: syncDataOrderEvent

        orderToSyncDataOrderEvent-in-0:
          destination: orderPayUpdateSuccessEvent
          group: orderToSyncDataOrderEventGroup
 --------------------------------------------------------移除结束



--application-rocketmq-system-dev.yml
          definition:
          移除：syncDataEvent
          新增：  supplierSyncDataBranchEvent;supplierSyncDataOrderEvent;supplierSyncDataAfterEvent;supplierSyncDataReceiptEvent;



-- 调整：
        #销售订单信息同步
        orderPayUpdateSuccessEvent-out-0:
          destination: orderPayUpdateSuccessEvent
        syncDataOrderEvent-in-0:
          destination: orderPayUpdateSuccessEvent
          group: syncDataOrderEventGroup

-- 移除：
        #同步数据统一推送消息入口
        syncDataEvent-out-0:
          destination: syncDataEvent
        syncDataEvent-in-0:
          destination: syncDataEvent
          group: syncDataEventGroup

--新增：
        #入驻商同步数据 -- 推送门店消息入口
        supplierSyncDataBranchEvent-out-0:
          destination: supplierSyncDataBranchEvent
        supplierSyncDataBranchEvent-in-0:
          destination: supplierSyncDataBranchEvent
          group: supplierSyncDataBranchEventGroup

        #入驻商同步数据 -- 推送销售订单消息入口
        supplierSyncDataOrderEvent-out-0:
          destination: supplierSyncDataOrderEvent
        supplierSyncDataOrderEvent-in-0:
          destination: supplierSyncDataOrderEvent
          group: supplierSyncDataOrderEventGroup

        #入驻商同步数据 -- 推送售后订单消息入口
        supplierSyncDataAfterEvent-out-0:
          destination: supplierSyncDataAfterEvent
        supplierSyncDataAfterEvent-in-0:
          destination: supplierSyncDataAfterEvent
          group: supplierSyncDataAfterEventGroup

        #入驻商同步数据 -- 推送收款单消息入口
        supplierSyncDataReceiptEvent-out-0:
          destination: supplierSyncDataReceiptEvent
        supplierSyncDataReceiptEvent-in-0:
          destination: supplierSyncDataReceiptEvent
          group: supplierSyncDataReceiptEventGroup


--修改对接相关字段类型
ALTER TABLE `zksr-cloud`.`sys_interface_log`
    MODIFY COLUMN `status`     int(4)  NULL DEFAULT 0 COMMENT '状态 -2异常-1失败 0待处理 1成功',
    MODIFY COLUMN `req_status` int(4)  NULL DEFAULT 0 COMMENT '请求状态  0未组装数据  1组装数据推送中/接收中 2推送/接收完成',
    MODIFY COLUMN `source`     int(4)  NULL DEFAULT 0 COMMENT '来源方 数据字典 log_source',
    MODIFY COLUMN `receive`    int(4)  NULL DEFAULT 0 COMMENT '接收方 数据字典 log_receiver';


-- 可视化对接接口代码重构 调整配置
--application-rocketmq-trade-dev.yml
        --去除已废弃MQ配置
        definition:erpTradeOrderEvent;erpToErpReceiptEvent;

        bindings:

        erpTradeOrderEvent-out-0:
           destination: erpTradeOrderEvent
        erpTradeOrderEvent-in-0:
          destination: erpTradeOrderEvent
          group: erpTradeOrderEventGroup

        erpToErpReceiptEvent-out-0:
           destination: erpToErpReceiptEvent
        erpToErpReceiptEvent-in-0:
          destination: erpToErpReceiptEvent
          group: erpToErpReceiptEventGroup


/* 2024年10月31日10:53:29 索引优化 */
ALTER TABLE `trd_order_express` ADD INDEX `idx_supplier_order_dtl_id`(`supplier_order_dtl_id`);
ALTER TABLE `sys_partner_policy` ADD INDEX `idx_supplier_id`(`supplier_id`);
ALTER TABLE `trd_supplier_after`
    ADD INDEX `idx_after_id`(`after_id`),
    ADD INDEX `idx_supplier_id`(`supplier_id`);
ALTER TABLE `trd_supplier_after_dtl` ADD INDEX `idx_after_id`(`after_id`);


-- 入驻商订单详情增加 生产日期 字段
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD COLUMN `latest_date` datetime DEFAULT NULL COMMENT '最新生产日期' AFTER `category_id`,
    ADD COLUMN `oldest_date` datetime DEFAULT NULL COMMENT '最旧生产日期' AFTER `latest_date` ;

-- 入驻商售后订单详情增加 生产日期 字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
    ADD COLUMN `latest_date` datetime DEFAULT NULL COMMENT '最新生产日期' AFTER `category_id`,
    ADD COLUMN `oldest_date` datetime DEFAULT NULL COMMENT '最旧生产日期' AFTER `latest_date` ;

-- 业务员拜访日志表 更改平台商 字段长度
ALTER TABLE `mem_colonel_visit_log` MODIFY COLUMN `sys_code` bigint(20) NOT NULL COMMENT '平台商id'


-- 新增字典 调价单价格类型
INSERT INTO `zksr-cloud`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('调价单价格类型', 'prdt_adjust_price_type', '0', 'zksr', '2024-11-05 10:16:41', '', NULL, '调价单价格类型');
INSERT INTO `zksr-cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '标准价', 'markPrice', 'prdt_adjust_price_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-05 10:17:31', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '供货价', 'costPrice', 'prdt_adjust_price_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-05 10:18:05', '', NULL, NULL);


-- 新增调价单权限
-- INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2598, '调价管理', 0, 20, 'priceManage', NULL, NULL, 1, 0, 'M', '0', '0', '', 'monitor', 'zksr', '2024-11-04 15:12:45', 'zksr', '2024-11-04 15:13:47', '', 'software,partner,dc,supplier');
-- INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2599, '调价单', 2598, 1, 'priceOrder', 'priceManage/priceOrder/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2024-11-04 15:14:54', '', NULL, '', 'software,partner,dc,supplier');
-- INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2600, '新增调价单', 2599, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:add', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'supplier,dc');
-- INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2604, '修改调价单', 2599, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:edit', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'supplier,dc');
-- INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2605, '删除调价单', 2599, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:remove', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'supplier,dc');
-- INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2606, '调价单列表', 2599, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:list', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'software,partner,dc,supplier');
-- INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2607, '查询调价单', 2599, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:query', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'software,partner,dc,supplier');
-- INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2608, '审核调价单', 2599, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:approve', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'partner,dc');
-- INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2609, '导入调价单', 2599, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:import', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'supplier,dc');
-- INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2610, '导出调价单', 2599, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:export', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'software,partner,dc,supplier');

-- 商品调价单新增MQ配置
-- application-rocketmq-product-dev.yml
-- definition 新增
productAdjustPricesApproveEvent;
-- bindings 新增
       #商品调价单
       productAdjustPricesApproveEvent-out-0:
          destination: productAdjustPricesApproveEvent
        productAdjustPricesApproveEvent-in-0:
          destination: productAdjustPricesApproveEvent
          group: productAdjustPricesApproveEventGroup

-- 默认值调整
ALTER TABLE `acc_pay_flow` MODIFY COLUMN `pay_way` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 0 COMMENT '支付方式;0-在线支付 1-储值支付 2-模拟支付' AFTER `out_flow_no`;


------------生产环境已执行脚本  2024年11月4日10:46:40---------

ALTER TABLE `trd_supplier_after_dtl`
    MODIFY COLUMN `is_cancel` int(11) NULL DEFAULT 0 COMMENT '是否已取消 0未取消 1已取消' AFTER `refund_time`;

-- 门店支持逻辑删除调整
ALTER TABLE `mem_branch` MODIFY COLUMN `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除状态(0:正常，2：删除)' AFTER `audit_time`;
UPDATE mem_branch SET del_flag = 0 WHERE del_flag IS NULL;

-- 业务员逻辑删除
ALTER TABLE `mem_colonel`
    MODIFY COLUMN `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除状态(0:正常，2：删除)' AFTER `order_auto_approve`;
UPDATE mem_colonel SET del_flag = 0 WHERE del_flag IS NULL;

-- 移除用户标账户名称, 手机号唯一性验证调整, 因为user表涉及逻辑删除
ALTER TABLE `sys_user`
    DROP INDEX `idx_unique_userName`,
    DROP INDEX `idx_unique_phonenumber`;

-- 索引优化
ALTER TABLE `trd_supplier_order`
    ADD INDEX `idx_order_id`(`order_id`);
ALTER TABLE `trd_supplier_after_settle`
    ADD INDEX `idx_supplier_order_dtl_id`(`supplier_order_dtl_id`);

-- 软件商模版库
CREATE TABLE `sys_pages_config_template` (
                                             `page_template_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自定义页面ID',
                                             `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商code',
                                             `create_by` varchar(64) DEFAULT NULL COMMENT '创建人;创建人',
                                             `create_time` datetime DEFAULT NULL COMMENT '创建时间;创建时间',
                                             `update_by` varchar(64) DEFAULT NULL COMMENT '更新人;修改人',
                                             `update_time` datetime DEFAULT NULL COMMENT '更新时间;修改时间',
                                             `enable_time` datetime DEFAULT NULL COMMENT '启用时间',
                                             `page_name` varchar(64) DEFAULT NULL COMMENT '页面名称',
                                             `page_type` varchar(32) DEFAULT NULL COMMENT '页面类型;页面类型,index-首页',
                                             `page_config` longtext COMMENT '页面配置JSON',
                                             `status` char(2) DEFAULT '0' COMMENT '状态 (0正常 1停用)',
                                             `del_flag` char(2) DEFAULT '0' COMMENT '删除状态 (0正常 2已删除)',
                                             `pid` bigint(20) DEFAULT NULL COMMENT '父级页面',
                                             `level` tinyint(2) DEFAULT '1' COMMENT '1-一级页面, 2-二级页面',
                                             `has_child` tinyint(2) DEFAULT '0' COMMENT '0-没有子页面,1-有子页面',
                                             PRIMARY KEY (`page_template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=452267525218893876 DEFAULT CHARSET=utf8mb4 COMMENT='平台页面配置模版';

-- 门店用户表关联优化调整
ALTER TABLE `mem_branch_user` ADD INDEX `idx_member_id`(`member_id`);

-- 默认值调整
ALTER TABLE `prm_activity`
    MODIFY COLUMN `chanel_scope_all_flag` tinyint(4) NULL COMMENT '是否指定渠道参与;0-所有渠道参与 1-指定渠道参与  （指定渠道和指定门店二选一）' AFTER `spu_scope`,
    MODIFY COLUMN `branch_scope_all_flag` tinyint(4) NULL COMMENT '是否指定门店参与;0-所有门店参与 1-指定门店参与   （指定渠道和指定门店二选一）' AFTER `chanel_scope_all_flag`;


CREATE TABLE `zksr-product`.`prdt_adjust_prices` (
                                      `adjust_prices_id` bigint(20) NOT NULL COMMENT '单据ID',
                                      `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                      `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `dc_id` bigint(20) DEFAULT NULL COMMENT '运营商ID',
                                      `supplier_id` bigint(20) NOT NULL COMMENT '入驻商ID',
                                      `adjust_prices_no` varchar(30) DEFAULT NULL COMMENT '单据编号',
                                      `approve_state` tinyint(2) DEFAULT '0' COMMENT '审核状态：0-待审核,1-已审核,2-已作废',
                                      `approve_user_id` bigint(20) DEFAULT NULL COMMENT '审核人Id',
                                      `approve_by` varchar(64) DEFAULT NULL COMMENT '审核人',
                                      `approve_time` datetime DEFAULT NULL COMMENT '审核时间',
                                      `valid_type` tinyint(2) DEFAULT '1' COMMENT '生效类型：0-定时生效，1-立即生效',
                                      `valid_time` date DEFAULT NULL COMMENT '生效时间',
                                      `sku_num` int(11) DEFAULT '0' COMMENT 'SKU数',
                                      `price_type_str` varchar(255) DEFAULT NULL COMMENT '价格类型选项：标准价：markPrice, 成本价：costPrice，已，号分隔',
                                      `memo` varchar(255) DEFAULT NULL COMMENT '备注',
                                      PRIMARY KEY (`adjust_prices_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品调价单主表';


CREATE TABLE `zksr-product`.`prdt_adjust_prices_dtl` (
                                          `adjust_prices_dtl_id` bigint(20) NOT NULL COMMENT '单据明细ID',
                                          `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                          `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                          `adjust_prices_id` bigint(20) NOT NULL COMMENT '单据ID',
                                          `spu_id` bigint(20) DEFAULT NULL COMMENT 'spuId',
                                          `sku_id` bigint(20) DEFAULT NULL COMMENT 'skuId',
                                          `old_large_mark_price` decimal(12,2) DEFAULT NULL COMMENT '大单位-原标准价',
                                          `old_large_cost_price` decimal(12,2) DEFAULT NULL COMMENT '大单位-原成本价(供货价)',
                                          `old_mid_mark_price` decimal(12,2) DEFAULT NULL COMMENT '中单位-原标准价',
                                          `old_mid_cost_price` decimal(12,2) DEFAULT NULL COMMENT '中单位-原成本价(供货价)',
                                          `old_min_mark_price` decimal(12,2) DEFAULT NULL COMMENT '小单位-原标准价',
                                          `old_min_cost_price` decimal(12,2) DEFAULT NULL COMMENT '小单位-原成本价(供货价)',
                                          `new_large_mark_price` decimal(12,2) DEFAULT NULL COMMENT '大单位-新标准价',
                                          `new_large_cost_price` decimal(12,2) DEFAULT NULL COMMENT '大单位-新新成本价(供货价)',
                                          `new_mid_mark_price` decimal(12,2) DEFAULT NULL COMMENT '中单位-标准价',
                                          `new_mid_cost_price` decimal(12,2) DEFAULT NULL COMMENT '中单位-新成本价(供货价)',
                                          `new_min_mark_price` decimal(12,2) DEFAULT NULL COMMENT '小单位-新标准价',
                                          `new_min_cost_price` decimal(12,2) DEFAULT NULL COMMENT '小单位-新成本价(供货价)',
                                          `valid_state` tinyint(2) DEFAULT '0' COMMENT '生效状态：0-未生效，1-生效中，2-已生效',
                                          `valid_time` datetime DEFAULT NULL COMMENT '生效时间',
                                          PRIMARY KEY (`adjust_prices_dtl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品调价单明细表';


INSERT INTO `sys_config` (`config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('中心服务器对外验证token', 'wechat_service_center_server_token', 'zBVuCma2hBQh50m90mc3IuoeiM5NQI6h', 'Y', 'zksr', '2024-08-15 14:04:22', '', NULL, '中心服务器外部授权调用路径token');

ALTER TABLE `prdt_adjust_prices` ADD INDEX `idx_supplier_id`(`supplier_id`);
ALTER TABLE `prdt_adjust_prices_dtl` ADD INDEX `idx_adjust_prices_id`(`adjust_prices_id`);
ALTER TABLE `prdt_adjust_prices_dtl`
    ADD INDEX `idx_spu_id`(`spu_id`),
    ADD INDEX `idx_sku_id`(`sku_id`);

-- 促销库索引优化
ALTER TABLE `prm_activity` ADD INDEX `idx_supplier_id`(`supplier_id`);
ALTER TABLE `prm_activity` ADD INDEX `idx_sys_code`(`sys_code`);
ALTER TABLE `prm_activity_branch_scope` ADD INDEX `idx_activity_id`(`activity_id`);
ALTER TABLE `prm_activity_channel_scope` ADD INDEX `idx_activity_id`(`activity_id`);
ALTER TABLE `prm_activity_city_scope` ADD INDEX `idx_activity_id`(`activity_id`);
ALTER TABLE `prm_activity_spu_scope` ADD INDEX `idx_activity_id`(`activity_id`);
ALTER TABLE `prm_bg_rule`
    ADD INDEX `idx_activity_id`(`activity_id`),
    ADD INDEX `idx_sku_id`(`sku_id`);
ALTER TABLE `prm_coupon` ADD INDEX `idx_relate_order_no`(`relate_order_no`);
ALTER TABLE `prm_coupon_log` ADD INDEX `idx_coupon_id`(`coupon_id`);
ALTER TABLE `prm_coupon_scope_apply` ADD INDEX `idx_coupon_template_id`(`coupon_template_id`);
ALTER TABLE `prm_coupon_scope_apply` ADD INDEX `idx_apply_id`(`apply_id`);
ALTER TABLE `prm_coupon_template` ADD INDEX `idx_sys_code`(`sys_code`);
ALTER TABLE `prm_coupon_template` ADD INDEX `idx_supplier_id`(`supplier_id`);
ALTER TABLE `prm_coupon_template_repeat_rule` ADD INDEX `idx_coupon_template_id`(`coupon_template_id`);
ALTER TABLE `prm_fd_rule` ADD INDEX `idx_activity_id`(`activity_id`);
ALTER TABLE `prm_fg_rule`
    ADD INDEX `idx_activity_id`(`activity_id`),
    ADD INDEX `idx_sku_id`(`sku_id`);
ALTER TABLE `prm_sk_rule`
    ADD INDEX `idx_activity_id`(`activity_id`),
    ADD INDEX `idx_spu_id`(`spu_id`);
ALTER TABLE `prm_sp_rule`
    ADD INDEX `idx_activity_id`(`activity_id`),
    ADD INDEX `idx_spu_id`(`spu_id`);

-- 用户库索引优化
ALTER TABLE `mem_branch`
    ADD INDEX `idx_colonel_id`(`colonel_id`),
    ADD INDEX `idx_area_id`(`area_id`),
    ADD INDEX `idx_channel_id`(`channel_id`),
    ADD INDEX `idx_group_id`(`group_id`),
    ADD INDEX `idx_three_area_city_id`(`three_area_city_id`);
ALTER TABLE `mem_branch_register`
    ADD INDEX `idx_colonel_id`(`colonel_id`),
    ADD INDEX `idx_channel_id`(`channel_id`);
ALTER TABLE `mem_branch_supplier`
    ADD INDEX `idx_supplier_id`(`supplier_id`),
    ADD INDEX `idx_branch_id`(`branch_id`);
ALTER TABLE `mem_colonel`
    ADD INDEX `idx_area_id`(`area_id`),
    ADD INDEX `idx_pcolonel_id`(`pcolonel_id`),
    ADD INDEX `idx_develop_people_id`(`develop_people_id`);
ALTER TABLE `mem_colonel_branch_target`
    ADD INDEX `idx_colonel_id`(`colonel_id`),
    ADD INDEX `idx_branch_id`(`branch_id`);
ALTER TABLE `mem_colonel_day_settle`
    ADD INDEX `idx_colonel_id`(`colonel_id`);
ALTER TABLE `mem_colonel_day_settle`
    ADD INDEX `idx_settle_create_date`(`settle_create_date`);
ALTER TABLE `mem_colonel_month_settle`
    ADD INDEX `idx_settle_month_date`(`settle_month_date`),
    ADD INDEX `idx_colonel_id`(`colonel_id`);
ALTER TABLE `mem_colonel_relation`
    ADD INDEX `idx_admin_colonel_id`(`admin_colonel_id`),
    ADD INDEX `idx_colonel_id`(`colonel_id`);
ALTER TABLE `mem_colonel_target`
    ADD INDEX `idx_colonel_id`(`colonel_id`),
    ADD INDEX `idx_target_month`(`target_month`),
    ADD INDEX `idx_target_year`(`target_year`);
ALTER TABLE `mem_colonel_tidy`
    ADD INDEX `idx_colonel_id`(`colonel_id`),
    ADD INDEX `idx_branch_id`(`branch_id`);
ALTER TABLE `mem_complain`
    ADD INDEX `idx_member_id`(`member_id`),
    ADD INDEX `idx_business_id`(`business_id`);
ALTER TABLE `mem_member`
    ADD INDEX `idx_dc_id`(`dc_id`),
    ADD INDEX `idx_xcx_openid`(`xcx_openid`),
    ADD INDEX `idx_register_colonel_id`(`register_colonel_id`),
    ADD INDEX `idx_relate_colonel_id`(`relate_colonel_id`);
ALTER TABLE `mem_member_register`
    ADD INDEX `idx_sys_code`(`sys_code`),
    ADD INDEX `idx_area_id`(`area_id`),
    ADD INDEX `idx_colonel_id`(`colonel_id`);

-- 交易库索引优化
ALTER TABLE `trd_after`
    ADD INDEX `idx_colonel_id`(`colonel_id`) USING BTREE,
    ADD INDEX `idx_pcolonel_id`(`pcolonel_id`) USING BTREE,
    ADD INDEX `idx_branch_id`(`branch_id`) USING BTREE,
    ADD INDEX `idx_dc_id`(`dc_id`) USING BTREE,
    ADD INDEX `idx_area_id`(`area_id`) USING BTREE;
ALTER TABLE `trd_after`
    ADD INDEX `idx_order_id`(`order_id`);
ALTER TABLE `trd_after_discount_dtl`
    ADD INDEX `idx_order_id`(`order_id`),
    ADD INDEX `idx_branch_id`(`branch_id`),
    ADD INDEX `idx_sku_id`(`sku_id`),
    ADD INDEX `idx_after_id`(`after_id`),
    ADD INDEX `idx_discount_id`(`discount_id`);
ALTER TABLE `trd_after_log`
    ADD INDEX `idx_supplier_after_dtl_id`(`supplier_after_dtl_id`);
ALTER TABLE `trd_express_import`
    ADD INDEX `idx_supplier_id`(`supplier_id`);
ALTER TABLE `trd_express_import_dtl`
    ADD INDEX `idx_supplier_order_dtl_id`(`supplier_order_dtl_id`),
    ADD INDEX `idx_express_import_id`(`express_import_id`),
    ADD INDEX `idx_order_id`(`order_id`);
ALTER TABLE `trd_express_status`
    ADD INDEX `idx_supplier_order_no`(`supplier_order_no`);
ALTER TABLE `trd_hdfk_pay`
    ADD INDEX `idx_branch_id`(`branch_id`);
ALTER TABLE `trd_hdfk_pay_dtl`
    ADD INDEX `idx_hdfk_pay_id`(`hdfk_pay_id`),
    ADD INDEX `idx_branch_id`(`branch_id`),
    ADD INDEX `idx_supplier_id`(`supplier_id`),
    ADD INDEX `idx_hdfk_settle_id`(`hdfk_settle_id`);
ALTER TABLE `trd_hdfk_settle`
    ADD INDEX `idx_fdfk_pay_dtl_id`(`fdfk_pay_dtl_id`),
    ADD INDEX `idx_branch_id`(`branch_id`);
ALTER TABLE `trd_order`
    ADD INDEX `idx_area_id`(`area_id`),
    ADD INDEX `idx_colonel_id`(`colonel_id`),
    ADD INDEX `idx_branch_id`(`branch_id`);
ALTER TABLE `trd_order_discount_dtl`
    ADD INDEX `idx_order_id`(`order_id`),
    ADD INDEX `idx_branch_id`(`branch_id`),
    ADD INDEX `idx_supplier_order_id`(`supplier_order_id`);
ALTER TABLE `trd_order_express`
    ADD INDEX `idx_order_id`(`order_id`);
ALTER TABLE `trd_order_express`
    ADD INDEX `idx_express_import_dtl_id`(`express_import_dtl_id`);
ALTER TABLE `trd_supplier_after`
    ADD INDEX `idx_order_id`(`order_id`);
-- ----------生产环境已执行脚本  2024年11月7日09:36:59 ---------


-- 商品调价单增加 定时调价状态标识
alter table `zksr-product`.`prdt_adjust_prices`
   add column `task_execute_status` tinyint(1) DEFAULT NULL COMMENT '定时调价执行状态 0-未执行，1-已执行';

-- 新增商品定时调价定时任务
INSERT INTO `xxl_job`.`xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (22, 2, '每天两点执行-商品调价单定时调价', '2024-11-08 11:01:44', '2024-11-08 11:02:26', '蒋剑超', '', 'CRON', '0 00 2 * * ?', 'DO_NOTHING', 'FIRST', 'adjustPricesTaskExecuteStatusJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-11-08 11:01:44', '', 0, 0, 0);

-- 入驻商售后订单详情增加  字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
    ADD COLUMN `return_original_price` DECIMAL(18,6) DEFAULT 0 COMMENT '售后最小单位原销售单价（不包括优惠）' AFTER `return_sales_unit_price`,
    ADD COLUMN `return_original_unit_price` DECIMAL(18,6) DEFAULT 0 COMMENT '售后单位原销售单价（不包括优惠）' AFTER `return_original_price`;

-- 入驻商售后订单增加  字段
ALTER TABLE `zksr-trade`.`trd_supplier_after`
    ADD COLUMN `return_sub_order_amt` DECIMAL(12,2) DEFAULT 0 COMMENT '入驻商订单本次售后退款金额（不包括优惠）' AFTER `sub_refund_amt`;

-- 售后订单增加  字段
ALTER TABLE `zksr-trade`.`trd_after`
    ADD COLUMN `return_order_amt` DECIMAL(12,2) DEFAULT 0 COMMENT '订单本次售后退款金额（不包括优惠）' AFTER `refund_amt`,
    ADD COLUMN `return_discount_amt` decimal(12,2) DEFAULT 0 COMMENT '本次售后订单优惠金额' AFTER `return_order_amt`;

CREATE TABLE `zksr-product`.`prdt_product_share` (
                                      `share_product_id` bigint(20) NOT NULL,
                                      `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                      `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                      `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                      `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                      `item_id` bigint(20) DEFAULT NULL COMMENT '商品上架ID',
                                      `unit_size` bigint(20) DEFAULT NULL COMMENT '商品单位',
                                      `remote_ip` varchar(32) DEFAULT NULL COMMENT '发起分享IP',
                                      `expiration_time` datetime DEFAULT NULL COMMENT '有效时间',
                                      `share_key` varchar(64) DEFAULT NULL COMMENT '分享key',
                                      PRIMARY KEY (`share_product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分享';

-- ----------生产环境已执行脚本  2024年11月13日18:47:59 ---------

-- 修改是否同步标准价格字段名称和注释
ALTER TABLE `zksr-cloud`.`sys_opensource`
    CHANGE COLUMN `sync_price` `sync_mark_price` tinyint(1) DEFAULT '1' COMMENT '是否同步标准价格 0-不同步 1-同步（默认同步）';

-- 增加是否同步供货价新字段
ALTER TABLE `zksr-cloud`.`sys_opensource`
    ADD COLUMN `sync_cost_price` tinyint(1) DEFAULT '1' COMMENT '是否同步供货价格 0-不同步 1-同步（默认同步）';
-- 订单结算表 结算状态 备注说明调整
ALTER TABLE `trd_settle`
    MODIFY COLUMN  `state` tinyint(1) NOT NULL COMMENT '结算状态 0=未结算，1=已结算 , 2=结算中，3= 待执行结算';

-- 促销特价允许字段为null调整, 如果未null则是不参与活动, 或者不限制
ALTER TABLE `zksr-promotion`.`prm_sp_rule`
    MODIFY COLUMN `sp_price` decimal(10, 2) NULL COMMENT '促销价' AFTER `sku_id`,
    MODIFY COLUMN `once_buy_limit` int(11) NULL COMMENT '单次限购数量' AFTER `sp_price`,
    MODIFY COLUMN `total_limit_qty` int(11) NULL COMMENT '总限量' AFTER `once_buy_limit`;

ALTER TABLE `zksr-promotion`.`prm_sk_rule`
MODIFY COLUMN `seckill_stock` int(11) NULL COMMENT '秒杀库存' AFTER `once_limit`;

-- ----------生产环境已执行脚本  2024年11月20日18:04:59 ---------

-- 门店表索引
ALTER TABLE `zksr-report`.`dim_branch` ADD INDEX `idx_colonel_id`(`colonel_id`);
-- spu 索引调整
ALTER TABLE `zksr-product`.`prdt_spu` ADD INDEX `idx_supplier_id`(`supplier_id`);

-- 菜单code编码优化, 使用自定义code作为子父级依据 新增字段
ALTER TABLE `zksr-cloud`.`sys_menu`
    ADD COLUMN `menu_code` varchar(32) NOT NULL COMMENT '菜单编号' AFTER `order_num`,
 ADD COLUMN `menu_pcode` varchar(32) NOT NULL COMMENT '菜单父级编号' AFTER `menu_code`;

-- 菜单code编码优化, 使用自定义code作为子父级依据 更新原数据SQL
-- update `zksr-cloud`.`sys_menu` set menu_code = menu_id, menu_pcode = parent_id

-- 软件商信息表
CREATE TABLE `zksr-cloud`.`sys_software`(
    `software_id` BIGINT(20) NOT NULL  COMMENT '软件商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` VARCHAR(64)   COMMENT '更新人' ,
    `update_time` DATETIME(3)   COMMENT '更新时间' ,
    `software_name` VARCHAR(32)   COMMENT '软件商名' ,
    `contact_name` VARCHAR(32)   COMMENT '联系人' ,
    `contact_phone` VARCHAR(16)   COMMENT '联系电话' ,
    `contact_address` VARCHAR(255)   COMMENT '联系地址' ,
    `software_user_id` BIGINT(20)   COMMENT '关联软件商管理员的账号id' ,
    `software_rate` DECIMAL(7,6)   COMMENT '软件商分润比例' ,
    PRIMARY KEY (software_id)
)  COMMENT = '软件商信息';

--平台商新增软件商ID和软件商分润比例
ALTER TABLE `zksr-cloud`.`sys_partner`
ADD COLUMN `software_id` bigint(20) NOT NULL COMMENT '软件商id' AFTER `sid`;

ALTER TABLE `zksr-cloud`.`sys_partner`
ADD COLUMN `software_rate` decimal(7, 6) NOT NULL default 0 COMMENT '软件商分润比例' AFTER `sid`;

--管理类别新增软件商分润比例
ALTER TABLE `zksr-product`.`prdt_catgory`
ADD COLUMN `software_rate` decimal(7,6) DEFAULT NULL default 0 COMMENT '软件商分润比例 百分比的小数表现形式，1%表示为0.01';


ALTER TABLE `mem_branch`
ADD COLUMN `first_order_flag` tinyint(1) NULL COMMENT '首单标识 1-是 0-否' ;
-- 增加拉链表  zhengsenbing 2024-11-22
DROP TABLE IF EXISTS mem_colonel_branch_zip;
CREATE TABLE `zksr-member`.`mem_colonel_branch_zip`(
    `colonel_branch_zip_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '门店业务员关系拉链表ID',
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
    `branch_id` BIGINT(20) NOT NULL  COMMENT '门店id' ,
    `colonel_id` BIGINT(20) NOT NULL  COMMENT '业务员id' ,
    `start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
    `end_date` DATETIME NOT NULL  COMMENT '结束日期',
    PRIMARY KEY (`colonel_branch_zip_id`)
)  COMMENT = '门店业务员关系拉链表';


DROP TABLE IF EXISTS mem_colonel_hierarchy_zip;
CREATE TABLE `zksr-member`.`mem_colonel_hierarchy_zip`(
    `colonel_hierarchy_zip_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '业务员上下级关系拉链表ID',
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
    `pcolonel_id` BIGINT(20) NOT NULL  COMMENT '上级业务员id' ,
    `colonel_id` BIGINT(20) NOT NULL  COMMENT '业务员id' ,
    `start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
    `end_date` DATETIME NOT NULL  COMMENT '结束日期',
    PRIMARY KEY (`colonel_hierarchy_zip_id`)
)  COMMENT = '业务员上下级关系拉链表';


DROP TABLE IF EXISTS sys_area_supplier_zip;
CREATE TABLE `zksr-cloud`.`sys_area_supplier_zip`(
     `area_supplier_zip_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '区域城市入驻商关系拉链表ID',
     `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
     `create_by` VARCHAR(64)   COMMENT '创建人' ,
     `create_time` DATETIME(3)   COMMENT '创建时间' ,
     `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
     `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
     `area_id` VARCHAR(255) NOT NULL  COMMENT '区域城市id' ,
     `supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
     `start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
     `end_date` DATETIME NOT NULL  COMMENT '结束日期',
     PRIMARY KEY (`area_supplier_zip_id`)
)  COMMENT = '区域城市入驻商关系拉链表';



DROP TABLE IF EXISTS sys_dc_area_zip;
CREATE TABLE `zksr-cloud`.`sys_dc_area_zip`(
    `dc_area_zip_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '运营商区域城市拉链表ID',
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
    `dc_id` BIGINT(20) NOT NULL  COMMENT '运营商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域城市id' ,
    `start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
    `end_date` DATETIME NOT NULL  COMMENT '结束日期',
    PRIMARY KEY (`dc_area_zip_id`)
)  COMMENT = '运营商区域城市拉链表';



DROP TABLE IF EXISTS prdt_area_item_zip;
CREATE TABLE `zksr-product`.`prdt_area_item_zip`(
    `area_item_zip_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '城市上架商品拉链表ID',
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
    `supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
    `spu_id` BIGINT(20) NOT NULL  COMMENT '商品SPU id' ,
    `sku_id` BIGINT(20) NOT NULL  COMMENT '商品sku id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '城市id' ,
    `start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
    `end_date` DATETIME NOT NULL  COMMENT '结束日期',
    PRIMARY KEY (`area_item_zip_id`)
)  COMMENT = '城市上架商品拉链表';


DROP TABLE IF EXISTS prdt_supplier_item_zip;
CREATE TABLE `zksr-product`.`prdt_supplier_item_zip`(
    `supplier_item_zip_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '全国上架商品拉链表ID',
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
    `supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
    `spu_id` BIGINT(20) NOT NULL  COMMENT '商品SPU id' ,
    `sku_id` BIGINT(20) NOT NULL  COMMENT '商品sku id' ,
    `start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
    `end_date` DATETIME NOT NULL  COMMENT '结束日期',
    PRIMARY KEY (`supplier_item_zip_id`)
)  COMMENT = '全国上架商品拉链表';


-- 门店标签计算表增加区域ID
ALTER TABLE `zksr-report`.`ads_branch_tag_month`
ADD COLUMN `area_id` bigint(20) NULL COMMENT '区域ID' AFTER `branch_id`,
ADD INDEX `idx_area_id`(`area_id`);

INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('满赠限购类型', 'fg_activity_times_rule', '0', 'zksr', '2024-11-25 09:49:14', 'zksr', '2024-11-25 09:50:09', '满赠限购类型');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '系统首单', '3', 'fg_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:40', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '仅一次', '1', 'fg_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, NULL);



-- trd_supplier_order_settle 订单结算表新增字段
ALTER TABLE `zksr-trade`.`trd_supplier_order_settle`
    ADD COLUMN `software_amt`  decimal(12,2) NULL COMMENT '软件商结算金额', -- 该字段可能已存在
    ADD COLUMN `software_rate` decimal(7,6) NULL COMMENT '软件商分润比例' AFTER `software_amt`;

-- trd_supplier_after_settle 售后订单结算表新增字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_settle`
    ADD COLUMN `software_amt`  decimal(12,2) NULL COMMENT '软件商分润金额', -- 该字段可能已存在
    ADD COLUMN `software_rate` decimal(7,6) NULL COMMENT '软件商分润比例 从订单 平台商分润比例，百分比的小数表现形式，1%表示为0.01' AFTER `software_amt`;

-- 新增菜单配置项
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`)
    VALUES ('入驻商销售汇总', 2031, 12, 'oxioKWXhYJULhihcUG', '2031', 'SupplierSaleSummary', 'finance/supplierSaleSummary/index', NULL, 1, 0, 'C', '0', '0', 'report:month:list', 'education', 'zksr', '2024-11-25 14:16:57', '', NULL, '', 'supplier,dc,software,partner');
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`)
    VALUES ('区域销售汇总', 2031, 13, 'ThUmSyQJgFOuJgVkyl', '2031', 'AreaSaleSummary', 'finance/areaSaleSummary/index', NULL, 1, 0, 'C', '0', '0', 'report:month:list', 'education', 'zksr', '2024-11-25 15:15:16', '', NULL, '', 'software,partner,dc,supplier');

-- 新增菜单配置项 秒杀商品导入 特价商品导入
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('特价商品导入', 2401, 4, 'IiubIWFeoNoKOvORAz', '2401', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sp-rule:import', '#', 'zksr', NOW(), '', NULL, '', 'dc');
-- INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('秒杀商品导入', 2399, 4, 'ejPNSJpUpBxDNGznxw', '2399', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:import', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'dc');

-- 提现流水调整
ALTER TABLE `acc_transfer_flow` MODIFY COLUMN `source_merchant_id` bigint(20) NULL DEFAULT NULL COMMENT '转出方商户id' AFTER `source_merchant_type`;

INSERT INTO `zksr_cloud`.`sys_software` (`software_id`, `create_by`, `create_time`, `update_by`, `update_time`, `software_name`, `contact_name`, `contact_phone`, `contact_address`, `software_user_id`, `software_rate`) VALUES (553204835870867456, 'zksr', '2024-11-26 18:37:18.271', 'zksr', '2024-11-26 18:37:18.273', '中科商软-软件商管理员', '中科商软', '19999999999', '暂无地址', 11231, 0.000000);


INSERT INTO `zksr_cloud`.`sys_user` (`user_id`, `sys_code`, `dept_id`, `user_name`, `nick_name`, `user_type`, `email`, `phonenumber`, `sex`, `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `dc_id`, `supplier_id`, `colonel_id`, `area_id`, `brand_id`) VALUES (11231, NULL, NULL, '19999999999', '【中科商软-软件商管理员】中科商软', '00', '', '19999999999', '0', '', '$2a$10$M9Wxs2dxLTkINXG17BJoceYrxC9MtMNNBh4dLfdFf2eFZP6SCHcZq', '0', '0', '', NULL, 'zksr', '2024-11-26 18:37:18', '', NULL, '中科商软-软件商管理员默认软件商管理员用户', NULL, NULL, NULL, NULL, NULL);


INSERT INTO `zksr_cloud`.`sys_user_role` (`user_id`, `role_id`, `sys_code`) VALUES (11231, 2, NULL);


update `zksr_cloud`.`sys_partner` set software_id = 553204835870867456;

-- 业务员用户信息 新增设备ID字段
ALTER TABLE `zksr-member`.`mem_member`
    ADD COLUMN `device_id` varchar(128) DEFAULT NULL COMMENT '设备唯一编码';

-- ----------生产环境已执行脚本  2024年11月27日19:37:59 ---------


ALTER TABLE `zksr-report`.`dwd_trd_order_dtl_inc` ADD INDEX `idx_branch_id`(`branch_id`);
ALTER TABLE `zksr-member`.`mem_colonel_visit_log`
    ADD INDEX `idx_branch_id`(`branch_id`),
    ADD INDEX `idx_colonel_id`(`colonel_id`);

-- 报表管理, 一级菜单调整
UPDATE `zksr-cloud`.`sys_menu` SET `menu_name` = '区域销售汇总', `parent_id` = 2658, `order_num` = 6, `menu_code` = '2641', `menu_pcode` = 'ZfGDdvXtdyqxghIocd', `path` = 'AreaSaleSummary', `component` = 'finance/areaSaleSummary/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'report:month:list', `icon` = 'education', `create_by` = 'zksr', `create_time` = '2024-11-25 15:15:16', `update_by` = 'zksr', `update_time` = '2024-12-02 15:20:03', `remark` = '', `func_scop` = 'partner,dc,supplier' WHERE `menu_id` = 2641;
UPDATE `zksr-cloud`.`sys_menu` SET `menu_name` = '入驻商销售汇总', `parent_id` = 2658, `order_num` = 5, `menu_code` = '2640', `menu_pcode` = 'ZfGDdvXtdyqxghIocd', `path` = 'SupplierSaleSummary', `component` = 'finance/supplierSaleSummary/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'report:month:list', `icon` = 'education', `create_by` = 'zksr', `create_time` = '2024-11-25 14:16:57', `update_by` = 'zksr', `update_time` = '2024-12-02 15:19:47', `remark` = '', `func_scop` = 'supplier,dc,partner' WHERE `menu_id` = 2640;
UPDATE `zksr-cloud`.`sys_menu` SET `menu_name` = '提现对账单', `parent_id` = 2658, `order_num` = 4, `menu_code` = '2627', `menu_pcode` = 'ZfGDdvXtdyqxghIocd', `path` = 'withdrawalAccount', `component` = 'finance/withdrawalAccount/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'account:withdraw:getAccWithdrawBillList', `icon` = '#', `create_by` = 'zksr', `create_time` = '2024-11-06 23:04:28', `update_by` = 'zksr', `update_time` = '2024-12-02 15:19:41', `remark` = '', `func_scop` = 'partner,dc,supplier' WHERE `menu_id` = 2627;
UPDATE `zksr-cloud`.`sys_menu` SET `menu_name` = '交易对账单明细', `parent_id` = 2658, `order_num` = 3, `menu_code` = '2626', `menu_pcode` = 'ZfGDdvXtdyqxghIocd', `path` = 'tradeAccountDetails', `component` = 'finance/tradeAccount/details', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'account:transfer:getAccTransferBillOrderList', `icon` = '#', `create_by` = 'zksr', `create_time` = '2024-11-06 23:03:33', `update_by` = 'zksr', `update_time` = '2024-12-02 15:19:32', `remark` = '', `func_scop` = 'partner,dc,supplier' WHERE `menu_id` = 2626;
UPDATE `zksr-cloud`.`sys_menu` SET `menu_name` = '交易对账单', `parent_id` = 2658, `order_num` = 2, `menu_code` = '2625', `menu_pcode` = 'ZfGDdvXtdyqxghIocd', `path` = 'tradeAccount', `component` = 'finance/tradeAccount/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'account:transfer:getAccTranList', `icon` = '#', `create_by` = 'zksr', `create_time` = '2024-11-06 23:02:42', `update_by` = 'zksr', `update_time` = '2024-12-02 15:19:23', `remark` = '', `func_scop` = 'partner,dc,supplier' WHERE `menu_id` = 2625;
UPDATE `zksr-cloud`.`sys_menu` SET `menu_name` = '订单分佣对账单', `parent_id` = 2658, `order_num` = 1, `menu_code` = '2624', `menu_pcode` = 'ZfGDdvXtdyqxghIocd', `path` = 'orderKickbackAccount', `component` = 'finance/orderKickbackAccount/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'trade:settle:orderStatementOfAccountList', `icon` = '#', `create_by` = 'zksr', `create_time` = '2024-11-06 23:00:48', `update_by` = 'zksr', `update_time` = '2024-12-02 15:19:18', `remark` = '', `func_scop` = 'partner,dc,supplier,software' WHERE `menu_id` = 2624;
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2658, '报表管理', 0, 21, 'ZfGDdvXtdyqxghIocd', '0', 'forms', NULL, NULL, 1, 0, 'M', '0', '0', '', 'build', 'zksr', '2024-12-02 15:17:12', 'zksr', '2024-12-02 15:17:41', '', 'software,dc,supplier,partner');

-- 软件商进件菜单
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2659, '软件商进件', 2017, 1, 'ySKtMSPBqyjJcxCKuF', '2017', 'SoftwareAuth', 'softwareManage/softwareAuth/index', '', 1, 1, 'C', '1', '0', 'account:platform-software-partner:list', 'button', 'zksr', '2024-12-02 17:56:43', 'zksr', '2024-12-02 17:57:34', '', 'software');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2660, '平台商配置保存', 2000, 6, 'IbWoJwExwNCRVdrbvl', '2000', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerConfig:savePartnerConfig', '#', 'zksr', '2024-12-03 17:07:13', '', NULL, '', 'software');

-- 导出任务标识长度调整
ALTER TABLE `zksr-cloud`.`sys_export_job` MODIFY COLUMN `export_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '导出类型,匹配执行器' AFTER `operate`;

--优惠券类型字典 添加注册发券
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (453, 0, '注册发券', '4', 'coupon_receive_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-28 10:51:24', 'zksr', '2024-12-02 16:05:54', NULL);



-- ----------生产环境已执行脚本  2024年12月04日19:39:59 ---------


-- 优惠券增加黑白名单
ALTER TABLE `zksr-promotion`.`prm_coupon_scope_apply` ADD COLUMN `white_or_black` tinyint(1) NULL DEFAULT 1 COMMENT '1-白名单 0-黑名单';
ALTER TABLE `zksr-promotion`.`prm_activity_branch_scope` ADD COLUMN `white_or_black` tinyint(1) NULL DEFAULT 1 COMMENT '1-白名单 0-黑名单';
ALTER TABLE `zksr-promotion`.`prm_activity_city_scope` ADD COLUMN `white_or_black` tinyint(1) NULL DEFAULT 1 COMMENT '1-白名单 0-黑名单';
ALTER TABLE `zksr-promotion`.`prm_activity_channel_scope` ADD COLUMN `white_or_black` tinyint(1) NULL DEFAULT 1 COMMENT '1-白名单 0-黑名单';
ALTER TABLE `zksr-promotion`.`prm_activity_spu_scope` ADD COLUMN `white_or_black` tinyint(1) NULL DEFAULT 1 COMMENT '1-白名单 0-黑名单';

-- 开放接口 新增 订单发货前取消 接口  增加相应配置
-- MQ： application-rocketmq-system-dev.yml  新增配置
-- 新增 definition:
openapi_order_cancel

-- 新增 bindings:
#对外接口:订单发货前取消
        openapi_order_cancel-out-0:
          destination: openapi_order_cancel
        openapi_order_cancel-in-0:
          destination: openapi_order_cancel
          group: openapi_order_cancel_group

-- 增加开放配置SQL
INSERT INTO `zksr-cloud`.`sys_openability`(`openability_id`, `create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES (487846016408055555, 'zksr', '2024-06-03 15:31:42.308', 'zksr', '2024-06-09 09:13:42.850', 0, 'supplier', '订单发货前取消', 'orderCancel', '0', 10);

-- 对外开放能力新增配置 赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）
ALTER TABLE `zksr-cloud`.`sys_opensource`
    ADD COLUMN `gift_price_type` tinyint(1) DEFAULT 0 COMMENT '赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）';

ALTER TABLE `zksr-cloud`.`sys_opensource`
    ADD COLUMN `order_merge_flag` tinyint(1) DEFAULT 0 COMMENT '订单同步是否合单（0否，1是）';

-- 赠品取价算法 新增数据字典配置
INSERT INTO `zksr-cloud`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (102, '赠品取价算法', 'gift_price_type', '0', 'zksr', '2024-12-09 10:53:30', '', NULL, '对接第三方使用');

INSERT INTO `zksr-cloud`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (563, 0, '零价', '0', 'gift_price_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-09 11:18:18', '', NULL, '默认零价');
INSERT INTO `zksr-cloud`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (564, 0, '均价', '1', 'gift_price_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-09 11:18:25', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (565, 0, '分摊价', '2', 'gift_price_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-09 11:18:33', '', NULL, NULL);



-- 平台商首页状态权限调整
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2661, '获得平台页面配置模版详情', 2613, 5, 'DQAwGMFsQftBxgBrJO', '2613', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:pageConfigTemplate:query', '#', 'zksr', '2024-12-05 09:29:10', '', NULL, '', 'partner');

-- 优惠券领取方式字典 新增业务员发券、批次发券
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (454, 0, '业务员发券', '5', 'coupon_receive_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-05 14:42:44', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (455, 0, '批次发券', '6', 'coupon_receive_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-05 14:43:09', '', NULL, NULL);

-- 新增优惠券批量发送表和批量发送详情表
DROP TABLE IF EXISTS prm_coupon_batch;
CREATE TABLE `zksr-promotion`.`prm_coupon_batch` (
  `coupon_batch_id` bigint NOT NULL COMMENT '优惠券批量发送id',
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `func_scope` tinyint NOT NULL COMMENT '全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定）',
  `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `coupon_template_qty` int DEFAULT NULL COMMENT '优惠券模板数量',
  `branch_qty` int DEFAULT NULL COMMENT '门店数量',
  `total_qty` int DEFAULT NULL COMMENT '总计发券数量',
  `valid_type` tinyint(1) DEFAULT NULL COMMENT '生效类型：0-定时生效，1-立即生效',
  `valid_time` datetime DEFAULT NULL COMMENT '生效时间',
  `real_send_qty` int DEFAULT NULL COMMENT '实际发券成功数量',
  `task_execute_status` tinyint(1) DEFAULT NULL COMMENT '执行状态 0-未执行，1-已执行',
  `audit_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态 0-未审核，1-已审核',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`coupon_batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='优惠券批量发送';

DROP TABLE IF EXISTS prm_coupon_batch_dtl;
CREATE TABLE `zksr-promotion`.`prm_coupon_batch_dtl` (
  `prm_coupon_batch_dtl_id` bigint NOT NULL COMMENT '优惠券批量发送详情id',
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  `batch_coupon_id` bigint NOT NULL COMMENT '优惠券批量发送id',
  `branch_id` bigint DEFAULT NULL COMMENT '门店id',
  `coupon_template_id` bigint DEFAULT NULL COMMENT '优惠券模板',
  `scope_type` tinyint(1) DEFAULT NULL COMMENT '0-优惠券模板 1-门店',
  PRIMARY KEY (`prm_coupon_batch_dtl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='优惠券批量发送详情';


-- 平台商门店修改权限
UPDATE `zksr-cloud`.`sys_menu` SET `menu_name` = '门店信息修改', `parent_id` = 2193, `order_num` = 3, `menu_code` = '2213', `menu_pcode` = '2193', `path` = '#', `component` = '', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'member:branch:edit', `icon` = '#', `create_by` = 'admin', `create_time` = '2024-03-21 11:16:30', `update_by` = 'zksr', `update_time` = '2024-12-05 10:05:53', `remark` = '', `func_scop` = 'dc,partner' WHERE `menu_code` = '2213';

-- 消息中心调整
ALTER TABLE `zksr-cloud`.`sys_message_template` ADD COLUMN `push_mode` tinyint(1) NULL COMMENT '0-小程序,1-公众号,2-微信门店助手,3-用户APP站内' AFTER `receive_merchant`;
CREATE TABLE `sys_message_log` (
                                   `msg_id` bigint(20) NOT NULL,
                                   `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商ID',
                                   `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
                                   `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                   `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
                                   `update_time` datetime(3) DEFAULT NULL COMMENT '最后修改时间',
                                   `message_template_id` bigint(20) DEFAULT NULL COMMENT '消息模版ID',
                                   `merchant_id` bigint(20) DEFAULT NULL COMMENT '接受商户ID',
                                   `merchant_type` varchar(32) DEFAULT NULL COMMENT '接受商户类型',
                                   `state` tinyint(1) DEFAULT NULL COMMENT '0-未发送, 1-成功, 2-失败',
                                   `content` varchar(1024) DEFAULT NULL COMMENT '消息内容',
                                   PRIMARY KEY (`msg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- 增加业务员公众号openid配置
ALTER TABLE `zksr-member`.`mem_colonel` ADD COLUMN `publish_openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公众号openid' AFTER `three_area_city_id`;

-- 订单查询新增本地订单发货收货按钮权限
INSERT INTO `zksr-cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`)
    VALUES ('入驻商订单发货出库', 2262, 1, 'JFLNLnZmDORWuhUDOV', '2262', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierOrder:outbound', '#', 'zksr', '2024-12-05 09:42:32', '', NULL, '', 'supplier');
INSERT INTO `zksr-cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`)
    VALUES ('入驻商订单收货', 2262, 1, 'SROMUHRczOJapLwzFo', '2262', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierOrder:takeDelivery', '#', 'zksr', '2024-12-05 09:43:04', '', NULL, '', 'supplier');

-- 订单明细增加赠品分摊价字段
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD COLUMN `exact_gift_share_price` DECIMAL(18,6) DEFAULT 0 COMMENT '赠品优惠分摊单价（6位小数）',
		ADD COLUMN `gift_share_subtotal_amt` DECIMAL(18,6) DEFAULT 0 COMMENT '赠品分摊价小计',
		ADD COLUMN `after_gift_share_price` DECIMAL(18,6) DEFAULT 0 COMMENT '均摊赠品优惠后的成交价（6位小数）',
        ADD COLUMN `after_gift_share_unit_price` DECIMAL(18,6) DEFAULT 0 COMMENT '均摊赠品优惠后的购买单位成交价（6位小数）',
		ADD COLUMN `after_gift_share_subtotal_amt` DECIMAL(18,6) DEFAULT 0 COMMENT '均摊赠品优惠后的小计',
		ADD COLUMN `sku_avg_price` DECIMAL(18,6) DEFAULT 0 COMMENT 'SKU最小单位数量平均单价（6位小数）',
		ADD COLUMN `res_price` DECIMAL(18,6) DEFAULT 0 COMMENT '赠品取价算法- 对应最终单价 （6位小数）',
        ADD COLUMN `res_unit_price` DECIMAL(18,6) DEFAULT NULL COMMENT '赠品取价算法- 对应最终购买单位单价 （6位小数）',
        ADD COLUMN `res_amt` DECIMAL(12,2) DEFAULT 0 COMMENT '赠品取价算法- 对应最终金额',
		ADD COLUMN `gift_price_type` TINYINT(1) DEFAULT 0 COMMENT '赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）'
;

-- 订单优惠明细增加赠品参与商品行号字段
ALTER TABLE `zksr-trade`.`trd_order_discount_dtl`
    ADD COLUMN `order_dtl_num_str` text DEFAULT NULL COMMENT '赠品优惠时使用，存放参与此赠品优惠的订单明细编号对应的行号，多个以;分号分隔'
    ;



-- 消息日志
CREATE TABLE `sys_message_log` (
                                   `msg_id` bigint(20) NOT NULL,
                                   `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商ID',
                                   `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
                                   `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                   `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
                                   `update_time` datetime(3) DEFAULT NULL COMMENT '最后修改时间',
                                   `message_template_id` bigint(20) DEFAULT NULL COMMENT '消息模版ID',
                                   `merchant_id` bigint(20) DEFAULT NULL COMMENT '接受商户ID',
                                   `merchant_type` varchar(32) DEFAULT NULL COMMENT '接受商户类型',
                                   `state` tinyint(1) DEFAULT NULL COMMENT '0-未发送, 1-成功, 2-失败',
                                   `content` varchar(1024) DEFAULT NULL COMMENT '消息内容',
                                   `path` varchar(255) DEFAULT NULL COMMENT '跳转路径',
                                   `tips` varchar(255) DEFAULT NULL COMMENT '消息备注',
                                   PRIMARY KEY (`msg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息发送记录';

-- 消息模版时间异常调整
ALTER TABLE `zksr-cloud`.`sys_message_template` MODIFY COLUMN `create_time` datetime NOT NULL COMMENT '创建时间' AFTER `create_by`;

-- 消息模版设置
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2673, '消息设置', 2021, 6, 'fNuhYtHNGmDJnlQshP', '2021', 'messageSetting', 'platform/messageSetting/index', NULL, 1, 0, 'C', '0', '0', 'system:subscribe-template:list', '#', 'zksr', '2024-12-06 15:35:36', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2672, '消息模版详情', 2673, 6, 'syhNhMntgkLJhTCstL', 'fNuhYtHNGmDJnlQshP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:subscribe-template:query', '#', 'zksr', '2024-12-06 15:30:06', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2671, '删除消息模版', 2673, 5, 'SgJaFHlFwyxaqLgiZO', 'fNuhYtHNGmDJnlQshP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:subscribe-template:remove', '#', 'zksr', '2024-12-06 15:29:54', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2670, '启用模版', 2673, 4, 'AalEQtEzjEOgmcbqZd', 'fNuhYtHNGmDJnlQshP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:subscribe-template:enable', '#', 'zksr', '2024-12-06 15:29:33', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2669, '停用模版', 2673, 3, 'VUTrJtqyPJRfIqKqhI', 'fNuhYtHNGmDJnlQshP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:subscribe-template:disable', '#', 'zksr', '2024-12-06 15:29:18', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2668, '修改消息模版', 2673, 2, 'xQHnTqwreAzrbxmOph', 'fNuhYtHNGmDJnlQshP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:subscribe-template:edit', '#', 'zksr', '2024-12-06 15:29:05', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2667, '新增消息模版', 2673, 1, 'qVKObGOLycboSNmVcs', 'fNuhYtHNGmDJnlQshP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:subscribe-template:add', '#', 'zksr', '2024-12-06 15:28:51', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2666, '消息模版分页列表', 2673, 6, 'WQaIjTffTKUkKNQzjT', 'fNuhYtHNGmDJnlQshP', '#', NULL, NULL, 1, 0, 'C', '0', '0', 'system:subscribe-template:list', '#', 'zksr', '2024-12-06 15:28:24', '', NULL, '', 'partner');


-- 消息模版字典
DELETE FROM `zksr-cloud`.sys_dict_type WHERE dict_type like 'message_scene_%';
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('用户下单通知', 'message_scene_0', '0', 'zksr', '2024-06-12 19:06:01', 'zksr', '2024-12-06 17:07:03', '公众号消息_下单通知参数项');
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('订单配送通知', 'message_scene_1', '0', 'zksr', '2024-06-13 08:59:59', 'zksr', '2024-12-06 17:05:31', '公众号消息_配送通知参数项,仅本地配送商品');
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('退货完成通知', 'message_scene_11', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '退货退款, 或者完成通知');
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('门店注册成功', 'message_scene_12', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '门店注册成功同志');
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('订货提醒', 'message_scene_13', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '门店客户长期未下单通知');
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('促销活动提醒', 'message_scene_14', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '促销活动开始前有指定范围通知');
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('门店订单发货(本地)', 'message_scene_2', '0', 'zksr', '2024-07-31 11:29:02', 'zksr', '2024-12-06 17:05:06', '门店订单发货通知,由微信商家助手发送');
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('待付款提醒', 'message_scene_3', '0', 'zksr', '2024-12-06 17:07:59', '', NULL, '订单待付款提醒通知');
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('订单取消通知', 'message_scene_4', '0', 'zksr', '2024-12-07 14:30:24', '', NULL, '订单未付款取消通知');
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('订单发货通知(全国)', 'message_scene_6', '0', 'zksr', '2024-12-07 14:30:43', '', NULL, '订单发货通知');
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('订单收货通知', 'message_scene_7', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '订单收货通知');
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('退货申请', 'message_scene_8', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '售后退款申请成功');
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('退货申请审核', 'message_scene_9', '0', 'zksr', '2024-12-07 14:30:59', '', NULL, '退货审核通过, 或者失败结果');

DELETE FROM `zksr-cloud`.sys_dict_data WHERE dict_type like 'message_scene_%';
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '用户下单通知', '0', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:04:22', 'zksr', '2024-12-06 16:51:15', '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '订单配送通知', '1', 'message_scene', NULL, 'default', 'N', '1', 'zksr', '2024-06-12 19:04:33', 'zksr', '2024-12-06 17:30:43', '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '订单号', 'orderNo', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:06:13', 'zksr', '2024-06-14 16:30:44', 'XS2403319300001299511');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '下单用户', 'memberName', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:07:00', 'zksr', '2024-06-14 16:30:52', '张三');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '下单用户手机号', 'memberPhone', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:07:21', 'zksr', '2024-06-14 16:31:00', '15580493955');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '门店名称', 'branchName', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:07:43', 'zksr', '2024-06-14 16:31:12', '张三的小店');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '门店地址', 'branchAddr', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:03', 'zksr', '2024-06-14 16:31:27', '长沙市雨花区黄土岭101');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (99, '自定义1', 'diy1', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:17', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (99, '自定义2', 'diy2', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:24', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (99, '自定义3', 'diy3', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:31', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '商品名称', 'spuName', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:09:13', 'zksr', '2024-06-14 16:31:37', '西瓜*1,茄子*2');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '订单金额', 'payAmt', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:10:58', 'zksr', '2024-06-14 16:31:42', '99.00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '订单金额', 'payAmt', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:10:58', 'zksr', '2024-06-14 16:33:57', '99.00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '商品名称', 'spuName', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:09:13', 'zksr', '2024-06-14 16:33:47', '西瓜*1,茄子*2');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (99, '自定义3', 'diy3', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:31', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (99, '自定义2', 'diy2', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:24', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (99, '自定义1', 'diy1', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:17', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '门店地址', 'branchAddr', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:08:03', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '门店名称', 'branchName', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:07:43', '', NULL, '张三的小店');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '下单用户手机号', 'memberPhone', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:07:21', '', NULL, '15580493955');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '下单用户', 'memberName', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:07:00', '', NULL, '张三');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '订单号', 'orderNo', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-12 19:06:13', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '下单时间', 'orderCreateTime', 'message_scene_0', NULL, 'default', 'N', '0', 'zksr', '2024-06-13 15:02:45', 'zksr', '2024-06-14 16:31:53', '2024-06-14 16:31:42');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, '入驻商名称', 'supplierName', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-13 15:03:48', 'zksr', '2024-06-14 16:34:19', '华农商行XX有限公司');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '入驻商电话', 'supplierContactPhone', 'message_scene_1', NULL, 'default', 'N', '0', 'zksr', '2024-06-13 15:04:02', 'zksr', '2024-06-14 16:34:02', '15580493955');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (10, '订单创建时间', 'orderCreateTime', 'message_scene_1', '', 'default', 'N', '0', 'zksr', '2024-06-13 15:04:40', 'zksr', '2024-06-14 16:34:26', '2024-06-14 16:31:42');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '门店订单发货(本地)', '2', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-07-31 11:27:26', 'zksr', '2024-12-06 16:52:26', '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品名称X数量', 'spuName', 'message_scene_2', NULL, 'default', 'N', '0', 'zksr', '2024-07-31 11:30:14', 'zksr', '2024-07-31 14:04:11', '西瓜*1,茄子*2');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单金额', 'payAmt', 'message_scene_2', NULL, 'default', 'N', '0', 'zksr', '2024-07-31 11:30:29', '', NULL, '99.00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商家名称', 'supplierName', 'message_scene_2', NULL, 'default', 'N', '0', 'zksr', '2024-07-31 11:31:03', '', NULL, '华农商行XX有限公司');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '待付款提醒', '3', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:51:57', 'zksr', '2024-12-06 16:52:02', '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '订单取消通知', '4', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:52:43', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '订单发货通知(全国)', '6', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:53:04', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '订单收货通知', '7', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:53:16', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '退货申请', '8', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:53:30', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, '退货申请审核', '9', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:53:59', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (11, '退货完成通知', '11', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:54:25', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (12, '门店注册成功', '12', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:54:48', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"1\": \"colonel\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (13, '订货提醒', '13', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:55:17', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (14, '活动通知', '14', 'message_scene', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 16:55:57', '', NULL, '{\r\n  \"supportMode\": {\r\n    \"0\": \"member\",\r\n    \"1\": \"member,colonel,supplier\",\r\n    \"2\": \"member\"\r\n  }\r\n}');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '订单编号', 'orderNo', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '商品名称', 'spuName', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '订单金额', 'payAmt', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:02', '', NULL, '99.00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '订单备注', 'orderMemo', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '帮我送上楼');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '下单时间', 'orderCreateTime', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, '2024-06-14 16:31:42');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '门店名称', 'branchName', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '自定义1', 'diy1', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '自定义2', 'diy2', 'message_scene_3', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '订单编号', 'orderNo', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '商品名称', 'spuName', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '订单金额', 'payAmt', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:02', '', NULL, '99.00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '订单备注', 'orderMemo', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '帮我送上楼');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '下单时间', 'orderCreateTime', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, '2024-06-14 16:31:42');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '门店名称', 'branchName', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '自定义1', 'diy1', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '自定义2', 'diy2', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, '取消时间', 'orderCancelTime', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '订单取消时间');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (10, '门店地址', 'branchAddr', 'message_scene_4', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单编号', 'orderNo', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品名称', 'spuName', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单金额', 'payAmt', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:02', '', NULL, '99.00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单备注', 'orderMemo', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '帮我送上楼');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '下单时间', 'orderCreateTime', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, '2024-06-14 16:31:42');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店名称', 'branchName', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店地址', 'branchAddr', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单发货时间', 'orderDeliveryTime', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单快递公司', 'orderExpressName', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单快递单号', 'orderExpressNo', 'message_scene_6', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '156415646546');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单编号', 'orderNo', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品名称', 'spuName', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单金额', 'payAmt', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:02', '', NULL, '99.00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单备注', 'orderMemo', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '帮我送上楼');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '下单时间', 'orderCreateTime', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, '2024-06-14 16:31:42');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店名称', 'branchName', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店地址', 'branchAddr', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单收货时间', 'orderReceiveTime', 'message_scene_7', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单编号', 'orderNo', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品名称', 'spuName', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货备注', 'afterMemo', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '我不想要了');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货申请单号', 'afterNo', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, 'SH21123131321');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店名称', 'branchName', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货申请时间', 'afterCreateTime', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店地址', 'branchAddr', 'message_scene_8', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单编号', 'orderNo', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品名称', 'spuName', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货备注', 'afterMemo', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '我不想要了');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货申请单号', 'afterNo', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, 'SH21123131321');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店名称', 'branchName', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货申请时间', 'afterCreateTime', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店地址', 'branchAddr', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货审核状态', 'afterAuditStatus', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '审核成功');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货审核备注', 'afterAuditMemo', 'message_scene_9', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '超过7天不予退货');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '订单编号', 'orderNo', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:39', '', NULL, 'XS2403319300001299511');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品名称', 'spuName', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:14:49', '', NULL, '西瓜*1,茄子*2');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货备注', 'afterMemo', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:28', '', NULL, '我不想要了');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货申请单号', 'afterNo', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:36', '', NULL, 'SH21123131321');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店名称', 'branchName', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货完成时间', 'afterFinishTime', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店地址', 'branchAddr', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退款金额', 'afterRefundAmt', 'message_scene_11', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '199.00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店名称', 'branchName', 'message_scene_12', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:15:43', '', NULL, '张三的小店');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_12', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_12', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店注册时间', 'branchCreateTime', 'message_scene_12', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店地址', 'branchAddr', 'message_scene_12', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '长沙市雨花区黄土岭101');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店手机号', 'branchContractPhone', 'message_scene_12', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '15580493955');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_13', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_13', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义3', 'diy3', 'message_scene_13', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义4', 'diy4', 'message_scene_13', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义1', 'diy1', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:05', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义2', 'diy2', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义3', 'diy3', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '自定义4', 'diy4', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '自定义字段');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '促销名称', 'activityName', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '梨子秒杀');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '促销开始时间', 'activityStartTime', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '促销结束时间', 'activityEndTime', 'message_scene_14', NULL, 'default', 'N', '0', 'zksr', '2024-12-06 17:17:46', '', NULL, '2024-12-12 22:00:00');

-- 业务员决策数据
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('业务员决策数据', 2331, 9, 'kTUvwTnxFrQWwgvrrq', '2331', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:baseReport', '#', 'zksr', '2024-12-10 09:08:34', '', NULL, '', 'colonel');


-- 业务员日结、月结 表 平台商字段长度调整
ALTER TABLE `zksr-member`.`mem_colonel_day_settle` MODIFY COLUMN `sys_code` BIGINT(20) NOT NULL COMMENT '平台商id';
ALTER TABLE `zksr-member`.`mem_colonel_month_settle` MODIFY COLUMN `sys_code` BIGINT(20) NOT NULL COMMENT '平台商id';

-- 新增业务员发券额度表
CREATE TABLE `zksr_promotion`.`prm_coupon_colonel_quota` (
    `coupon_colonel_quota_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '业务员发券额度ID',
    `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商ID',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `colonel_id` bigint(20) DEFAULT NULL COMMENT '业务员ID',
    `quota_type` int(1) DEFAULT NULL COMMENT '0-模板额度 1-实例额度',
    `quota` decimal(12,2) DEFAULT NULL COMMENT '额度',
    `finish_quota` decimal(12,2) DEFAULT NULL COMMENT '已使用额度，仅1-实例额度',
    `month_id` int(6) DEFAULT NULL COMMENT '月份ID，仅1-实例额度',
    PRIMARY KEY (`coupon_colonel_quota_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='业务员发券额度表';

-- 业务员发券额度 门店月销售汇总菜单
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店月销售汇总', 2658, 7, 'HlWbcGAdUMetAHaiYS', 'ZfGDdvXtdyqxghIocd', 'BranchSaleSummary', 'finance/branchSaleSummary/index', NULL, 1, 0, 'C', '0', '0', '', 'education', 'zksr', NOW(), 'zksr', NOW(), '', 'partner,supplier,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('查看历史发券额度', 2674, 2, 'lbGhiWQNFmJQdEgxha', 'BXULUOBHzMcpRiROUI', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:quota:history', '#', 'zksr', NOW(), '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('业务员发券调整额度', 2674, 1, 'EhmMGADBATqVJzGkZc', 'BXULUOBHzMcpRiROUI', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:quota:adjust', '#', 'zksr', NOW(), '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('业务员发券额度管理', 2027, 8, 'BXULUOBHzMcpRiROUI', '2027', 'CouponQuotaManage', 'operation/promotion/couponQuotaManage/index', NULL, 1, 0, 'C', '0', '0', 'promotion:quota:list', 'button', 'zksr', NOW(), '', NULL, '', 'partner');

-- 业务员APP 发券权限和查看优惠券权限
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('业务员主动发券', 2331, 11, 'SsPpIVApYLnGulMAQo', '2331', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:salesmanIssueCoupons', '#', 'zksr', '2024-12-11 15:47:42', '', NULL, '', 'colonel');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('业务员查看优惠券，查看业务员发券额度，查看业务员发券管理', 2331, 10, 'yqQLKaxZHHIfCJTvDn', '2331', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:viewCoupons', '#', 'zksr', NOW(), '', NULL, '', 'colonel');


-- 发券菜单
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('删除优惠券批量发送', 2685, 4, 'EMPNZlYTHlNHrNfiVt', 'XMXoTrNcVajRaraFFb', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:remove', '#', 'zksr', '2024-12-11 17:46:56', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('获得优惠券批量发送详情', 2685, 5, 'dNixHYtyqYWPojGvdk', 'XMXoTrNcVajRaraFFb', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:query', '#', 'zksr', '2024-12-11 11:08:20', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('批量审核优惠券批量发送', 2685, 3, 'hLAGtwocGeBkRxeFia', 'XMXoTrNcVajRaraFFb', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:audit', '#', 'zksr', '2024-12-11 11:07:40', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('修改优惠券批量发送', 2685, 2, 'KcEmQoFuDntcACrlEW', 'XMXoTrNcVajRaraFFb', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:edit', '#', 'zksr', '2024-12-11 11:06:36', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('新增优惠券批量发送', 2685, 1, 'iRkpVJxgPnSDBOnftp', 'XMXoTrNcVajRaraFFb', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:add', '#', 'zksr', '2024-12-11 11:06:21', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('批次发券管理', 2368, 10, 'XMXoTrNcVajRaraFFb', '2368', 'BatchCouponManage', 'operation/promotion/batchCouponManage/index', '{\"source\": 2}', 1, 0, 'C', '0', '0', 'couponBatch:couponBatch:list', 'button', 'zksr', '2024-12-11 11:05:37', '', NULL, '', 'dc');

INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('批次发券管理', 2027, 9, 'GBRtouCgJcuWuNBHxd', '2027', 'BatchCouponManage', 'operation/promotion/batchCouponManage/index', '{\"source\": 1}', 1, 0, 'C', '0', '0', 'couponBatch:couponBatch:list', 'button', 'zksr', '2024-12-07 11:18:24', 'zksr', '2024-12-11 11:01:27', '', 'dc,partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('获得优惠券批量发送详情', 2675, 5, 'fCRtpROuicLNKPtmfq', 'GBRtouCgJcuWuNBHxd', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:query', '#', 'zksr', '2024-12-08 19:03:54', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('删除优惠券批量发送', 2675, 4, 'fNmncTXuFoRSsnUWzC', 'GBRtouCgJcuWuNBHxd', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:remove', '#', 'zksr', '2024-12-08 19:03:40', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('批量审核优惠券批量发送', 2675, 3, 'TbyigsUrdFQiDZmFWF', 'GBRtouCgJcuWuNBHxd', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:audit', '#', 'zksr', '2024-12-08 19:03:21', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('修改优惠券批量发送', 2675, 2, 'eOsdApKyICRwcEvvSC', 'GBRtouCgJcuWuNBHxd', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:edit', '#', 'zksr', '2024-12-08 19:03:05', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('新增优惠券批量发送', 2675, 1, 'jyQJMvdnGHtzmCmYBI', 'GBRtouCgJcuWuNBHxd', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:add', '#', 'zksr', '2024-12-08 19:02:52', '', NULL, '', 'partner');

-- 更新订单表历史数据赠品分摊计算字段值 (注，不要重复执行)
-- update trd_supplier_order_dtl
-- set res_price = exact_price, res_unit_price = order_sales_unit_price, res_amt = total_amt
-- where gift_price_type = 0 AND res_unit_price is null and create_time < '2024-12-12 20:00:00';

-- -------------------------------------------- 生产环境已执行脚本  2024年12月12日19:39:59 ------------------------------------------------------------

-- 订单优惠明细增加字段
ALTER TABLE `zksr-trade`.`trd_order_discount_dtl`
    ADD COLUMN `discount_condition` VARCHAR(20) DEFAULT NULL COMMENT '活动满足条件;满足多少金额或数量参与促销 （目前只有满赠、买赠使用）'
;

-- 新增司机信息表和司机评价表
CREATE TABLE `zksr-trade`.`trd_driver_rating` (
  `driver_rating_id` bigint NOT NULL COMMENT '司机评分',
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `member_id` bigint DEFAULT NULL COMMENT '用户id',
  `supplier_order_id` bigint NOT NULL COMMENT '入驻商订单id',
  `slot1_code` varchar(16) DEFAULT NULL COMMENT '评价维度1字典code;例：0',
  `slot1_val` varchar(64) DEFAULT NULL COMMENT '评价维度1字典值;例：司机态度',
  `slot1_score_code` tinyint(1) DEFAULT NULL COMMENT '评价维度1-得分字典;例：4',
  `slot1_score_code_val` varchar(16) DEFAULT NULL COMMENT '评价维度1-得分字典值;例：满意',
  `slot2_code` varchar(16) DEFAULT NULL COMMENT '评价维度2字典code;例：1',
  `slot2_val` varchar(32) DEFAULT NULL COMMENT '评价维度2字典值;例：配送时效',
  `slot2_score` tinyint(1) DEFAULT NULL COMMENT '评价维度2-得分字典;例：5',
  `slot2_score_code_val` varchar(16) DEFAULT NULL COMMENT '评价维度2-得分字典值;例：非常满意',
  `slot3_code` varchar(16) DEFAULT NULL COMMENT '评价维度3字典code;例：2',
  `slot3_val` varchar(32) DEFAULT NULL COMMENT '评价维度3字典值;例：商品完好',
  `slot3_score` tinyint(1) DEFAULT NULL COMMENT '评价维度3-得分;例：3',
  `score_code_val` varchar(16) DEFAULT NULL COMMENT '评价维度1-得分字典值;例：一般满意',
  `reason_code` varchar(32) DEFAULT NULL COMMENT '原因code;例：0,1',
  `reason_val` varchar(32) DEFAULT NULL COMMENT '低分原因code字典值;例：态度不好,送货不及时',
  `fedback_msg` varchar(128) DEFAULT NULL COMMENT '反馈信息;例：加油！努力！',
  `fedback_pics` text COMMENT '反馈图片;例：',
  PRIMARY KEY (`driver_rating_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='司机评分表';


CREATE TABLE `zksr-trade`.`trd_driver` (
  `driver_id` bigint NOT NULL COMMENT '司机id',
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `supplier_id` bigint DEFAULT NULL COMMENT '入驻商id',
  `driver_name` varchar(16) DEFAULT NULL COMMENT '司机名',
  `driver_phone` varchar(16) DEFAULT NULL COMMENT '司机手机号',
  PRIMARY KEY (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='司机档案表';


ALTER TABLE `zksr-trade`.`trd_supplier_order`
ADD COLUMN `driver_id` bigint(20) DEFAULT NULL COMMENT '司机ID',
ADD COLUMN `driver_rating_flag` tinyint(1) DEFAULT 0 COMMENT '司机评价状态 (0-未评价, 1-已评价)',
ADD COLUMN `driver_rating_id` bigint(20) DEFAULT NULL COMMENT '司机评级ID';

-- 商品月销售报表和业务员月销售报表 菜单
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('商品月销售汇总', 2658, 9, 'ZDeMUkMOwxMoopuTZd', 'ZfGDdvXtdyqxghIocd', 'goodSaleSummary', 'finance/goodSaleSummary/index', NULL, 1, 0, 'C', '0', '0', NULL, 'education', 'zksr', NOW(), '', NULL, '', 'partner,dc,supplier');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('业务员月销售汇总', 2658, 8, 'vAsmSkrnQFGVfzzOyr', 'ZfGDdvXtdyqxghIocd', 'ColonelSaleSummary', 'finance/colonelSaleSummary/index', NULL, 1, 0, 'C', '0', '0', '', 'education', 'zksr', NOW(), 'zksr', NOW(), '', 'partner,dc,supplier');

-- -------------------------------------------- 生产环境已执行脚本  2024年12月18日16:47:19 ------------------------------------------------------------

ALTER TABLE `zksr-trade`.`trd_driver_rating`
ADD COLUMN `driver_id` bigint DEFAULT NULL COMMENT '司机ID';

INSERT INTO `zksr-cloud`.`sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '评价原因', 'trd_driver_rating_reason', '0', 'zksr', '2024-12-11 10:00:07', '', NULL, '评价原因');
INSERT INTO `zksr-cloud`.`sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '司机评价评分', 'trd_driver_rating_score', '0', 'zksr', '2024-12-11 09:57:56', 'zksr', '2024-12-11 09:58:08', '司机评价评分');
INSERT INTO `zksr-cloud`.`sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '司机评价维度', 'trd_driver_rating_slot', '0', 'zksr', '2024-12-11 09:56:16', '', NULL, '司机评价维度');
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '其他', '7', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:01:52', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '没礼貌', '6', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:01:44', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '语言粗暴', '5', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:01:32', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '商品缺损', '4', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:01:21', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '送达不通知', '3', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:01:01', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '送错地址', '2', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:00:51', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '送货不及时', '1', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:00:38', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '态度不好', '0', 'trd_driver_rating_reason', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 10:00:29', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '非常不满意', '1', 'trd_driver_rating_score', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:59:13', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '不满意', '2', 'trd_driver_rating_score', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:59:03', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '一般满意', '3', 'trd_driver_rating_score', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:58:53', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '满意', '4', 'trd_driver_rating_score', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:58:46', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '非常满意', '5', 'trd_driver_rating_score', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:58:33', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '商品完好', '2', 'trd_driver_rating_slot', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:57:20', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '配送时效', '1', 'trd_driver_rating_slot', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:57:10', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '司机态度', '0', 'trd_driver_rating_slot', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 09:56:58', '', NULL, NULL);
-- 新增平台商配置 -- 入驻商其他配置  数据字典
INSERT INTO `zksr-cloud`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (566, 18, '入驻商其他配置', '18', 'sys_partner_policy_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-11 18:44:08', 'zksr', '2024-12-11 18:44:21', NULL);

-- 要货单数据
CREATE TABLE `prdt_branch_yhdata` (
                                      `yh_id` bigint(20) NOT NULL,
                                      `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                      `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `pos_yh_batch_no` varchar(32) DEFAULT NULL COMMENT '要货批次号',
                                      `area_id` bigint(20) DEFAULT NULL COMMENT '区域城市id',
                                      `branch_id` bigint(20) DEFAULT NULL COMMENT '门店id',
                                      `pos_sku_name` varchar(64) DEFAULT NULL COMMENT '商品名',
                                      `pos_source_no` varchar(32) DEFAULT NULL COMMENT '商品编码',
                                      `pos_barcode` varchar(32) DEFAULT NULL COMMENT '国条码',
                                      `pos_sales_qty` int(8) DEFAULT NULL COMMENT '昨日销量',
                                      `pos_stock_qty` int(8) DEFAULT NULL COMMENT '实际库存数量',
                                      `pos_branch_stock_qty` int(8) DEFAULT NULL COMMENT '门店库存数量',
                                      `pos_suggest_qty` int(8) DEFAULT NULL COMMENT '建议购买数量',
                                      `pos_unit_name` varchar(16) DEFAULT NULL COMMENT '单位名称',
                                      `pos_max_late_time` datetime DEFAULT NULL COMMENT '最迟订货时间',
                                      `pos30day_avg_sales` int(8) DEFAULT NULL COMMENT '30天人均销量',
                                      `pos_safety_days` int(8) DEFAULT NULL COMMENT '安全库存天数',
                                      `pos_safety_stock` int(8) DEFAULT NULL COMMENT '安全库存',
                                      `mall_area_item_id` bigint(20) DEFAULT NULL COMMENT '区域城市上架商品id',
                                      `mall_match_sku_id` bigint(20) DEFAULT NULL COMMENT '匹配的sku id',
                                      `mall_unit_type` int(2) DEFAULT NULL COMMENT '单位大小,1-小单位, 2-中单位, 3-大单位',
                                      `last_time` datetime DEFAULT NULL COMMENT '上次补货时间',
                                      `last_submit_qty` int(8) DEFAULT NULL COMMENT '上次补货数量',
                                      `transit_qty` int(8) DEFAULT NULL COMMENT '已下单, 未完成数量, 在途数量',
                                      `checked` tinyint(2) DEFAULT '1' COMMENT '是否选中, 1-选中, 2-未选中',
                                      `match_state` int(2) DEFAULT NULL COMMENT '匹配状态（数据字典）0-未匹配, 1-匹配成功, 2-匹配失败',
                                      `fail_reason` int(2) DEFAULT NULL COMMENT '匹配失败原因（数据字典）0-库存不足, 1-已下架, 2-未匹配到商品',
                                      `line_num` int(10) DEFAULT NULL COMMENT '行号, 从1开始',
                                      `batch_ymd` int(10) DEFAULT NULL COMMENT '要货批次年月日yyyyMMdd',
                                      `del_flag` tinyint(2) DEFAULT '0' COMMENT '0-正常,1-已删除',
                                      PRIMARY KEY (`yh_id`),
                                      KEY `idx_branch_id` (`branch_id`),
                                      KEY `idx_pos_yh_batch_no` (`pos_yh_batch_no`),
                                      KEY `idx_batch_ymd` (`batch_ymd`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店批量要货';

-- 开放能力增加补货单数据
INSERT INTO `zksr-cloud`.`sys_openability` (`create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES ('zksr', '2024-12-09 10:02:32.496', NULL, NULL, NULL, 'partner', '获取批量补货结果', 'getBatchYhRes', '0', 20);
INSERT INTO `zksr-cloud`.`sys_openability` (`create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES ('zksr', '2024-12-09 10:01:55.541', NULL, NULL, NULL, 'partner', '接受批量补货', 'submitBatchYh', '0', 1);

-- 促销报表调整
UPDATE `zksr-cloud`.`sys_menu` SET `menu_name` = '促销报表', `parent_id` = 2368, `order_num` = 9, `menu_code` = '2452', `menu_pcode` = '2368', `path` = 'promotion/reportBuilder', `component` = 'operation/promotion/reportBuilder/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'promotion:activityReport:list', `icon` = '#', `create_by` = 'zksr', `create_time` = '2024-05-25 15:08:53', `update_by` = 'zksr', `update_time` = '2024-12-13 09:06:17', `remark` = '', `func_scop` = 'dc' WHERE menu_code = '2452';

-- 消息推送方式字典调整
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('消息推送方式', 'message_push_mode', '0', 'zksr', '2024-12-12 14:51:39', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '用户APP站内', '3', 'message_push_mode', NULL, 'default', 'N', '1', 'zksr', '2024-12-12 14:54:24', 'zksr', '2024-12-12 14:55:24', NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '微信门店助手', '2', 'message_push_mode', NULL, 'default', 'N', '0', 'zksr', '2024-12-12 14:53:18', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '微信公众号', '1', 'message_push_mode', NULL, 'default', 'N', '0', 'zksr', '2024-12-12 14:52:57', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '微信小程序', '0', 'message_push_mode', NULL, 'default', 'N', '0', 'zksr', '2024-12-12 14:52:25', '', NULL, NULL);

-- -----------------------------------  system mq 增加配置 start ---------------------------------
# Spring
spring:
  cloud:
    #spring-cloud-stream 配置
    stream:
      function:
        # spring-cloud-stream规范 函数式编程定义的BEAN name（这里只定义了消费者入口）
        definition: "openapi_batch_create_yh;"
      bindings:
        # 门店要货单补货数据
        openapi_batch_create_yh-out-0:
          destination: openapi_batch_create_yh
        openapi_batch_create_yh-in-0:
          destination: openapi_batch_create_yh
          group: openapi_batch_create_yh_group
-- -----------------------------------  system mq 增加配置 end ---------------------------------
-- -----------------------------------  product mq 增加配置 start ---------------------------------
# Spring
spring:
  cloud:
    #spring-cloud-stream 配置
    stream:
      function:
        # spring-cloud-stream规范 函数式编程定义的BEAN name（这里只定义了消费者入口）
        definition: "yhDataMatch"
      bindings:
        # 门店要货单数据匹配
        yhDataMatch-out-0:
          destination: yhDataMatch
        yhDataMatch-in-0:
          destination: yhDataMatch
          group: yhDataMatchGroup
-- -----------------------------------  product mq 增加配置 end ---------------------------------

-- 账号登录日志
CREATE TABLE `zksr-member`.`mem_login_his` (
  `login_his_id` bigint NOT NULL COMMENT '登录历史id',
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `date_id` int DEFAULT NULL COMMENT '日期;yyyyMMdd',
  `wx_openid` varchar(100) DEFAULT NULL COMMENT '微信openid;后台登录信息获取',
  `member_phone` varchar(16) DEFAULT NULL COMMENT '用户手机号;后台登录信息获取',
  `member_username` varchar(255) DEFAULT NULL COMMENT '用户名;后台登录信息获取',
  `member_id` bigint DEFAULT NULL COMMENT '用户id;后台登录信息获取',
  `branch_id` bigint DEFAULT NULL COMMENT '门店id;后台登录信息获取',
  `ip` varchar(32) DEFAULT NULL COMMENT 'ip地址;http_request',
  `district` varchar(32) DEFAULT NULL COMMENT 'ip地址归属地;http_request',
  `tp` varchar(8) DEFAULT NULL COMMENT '类型（数据字典）;0-登陆  1-访问',
  `device_id` varchar(128) DEFAULT NULL COMMENT '设备id;前端传（HttpHeader）',
  `device_type` varchar(32) DEFAULT NULL COMMENT '设备类型;前端传（HttpHeader）',
  `device_brand` varchar(32) DEFAULT NULL COMMENT '设备品牌;前端传（HttpHeader）',
  `device_model` varchar(32) DEFAULT NULL COMMENT '设备型号;前端传（HttpHeader）',
  `os_name` varchar(32) DEFAULT NULL COMMENT '系统名称;前端传（HttpHeader）',
  `os_version` varchar(32) DEFAULT NULL COMMENT '操作系统版本;前端传（HttpHeader）',
  `port` varchar(32) DEFAULT NULL COMMENT 'pc app xcx',
  `spu_id` bigint DEFAULT NULL COMMENT 'spu_id',
  PRIMARY KEY (`login_his_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='登录历史表';

--登录日志类型
INSERT INTO `zksr-cloud`.`sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '登录日志类型', 'mem_login_his_tp', '0', 'zksr', '2024-12-19 09:46:40', '', NULL, '登录日志类型');
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '登陆', '0', 'mem_login_his_tp', NULL, 'default', 'N', '0', 'zksr', '2024-12-19 09:47:09', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '访问', '1', 'mem_login_his_tp', NULL, 'default', 'N', '0', 'zksr', '2024-12-19 09:47:15', '', NULL, NULL);


-- 促销活动黑白名单新增导入商品信息和导入门店信息
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台特价门店导入', 2400, 5, 'HOQnodbijJecxrgfdd', '2400', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sp-rule:importBranch', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台秒杀门店导入', 2396, 5, 'yyDtrdmVSCyNCEKnjs', '2396', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:importBranch', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台满减门店导入', 2420, 6, 'HwEbjlYwlpbEWBBVgY', '2420', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:importBranch', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台满减商品导入', 2420, 5, 'JZrNLnHDGDaEvhVFZT', '2420', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:importItem', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台满赠门店导入', 2418, 6, 'OHpSzVRGsLPqtWVcxR', '2418', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:importBranch', '#', 'zksr', NOW() , 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台满赠商品导入', 2418, 5, 'ZMBGEFkwTPMdTZSoWe', '2418', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:importItem', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('特价黑白名单导入门店 ', 2401, 5, 'mojyCickfiGCbXVlsQ', '2401', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sp-rule:importBranch', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台买赠门店导入', 2398, 6, 'NTUMYivtoENIOOcMTR', '2398', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:importBranch', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('秒杀黑白名单导入门店', 2399, 5, 'NYkKgOhadoknSgZSoq', '2399', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:importBranch', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台买赠商品导入', 2398, 5, 'GUkKIchHbWZJstNUuu', '2398', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:importItem', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('满减黑白名单导入商品 ', 2419, 6, 'tcsOVikaNzwytvLaYI', '2419', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:importItem', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('满减黑白名单导入门店 ', 2419, 5, 'iXLjrPsdlKWdndVYKh', '2419', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:importBranch', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('买赠黑白名单导入商品', 2397, 6, 'hRkxBUOSBSVJnFxAYl', '2397', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:importItem', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台优惠券门店导入', 2256, 8, 'QsMIeCPjrCgxAhbEVU', '2256', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:importBranch', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('买赠黑白名单导入门店 ', 2397, 5, 'cnGRVLgSHRlDavQddn', '2397', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:importBranch', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('满赠黑白名单导入商品', 2417, 6, 'qlXgWqDcPpfQFgfEmB', '2417', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:importItem', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台优惠券商品导入', 2256, 7, 'PvXbtpYlKFmgYQFGSM', '2256', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:importItem', '#', 'zksr', NOW(), 'zksr', NOW(), '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('满赠黑白名单导入门店', 2417, 5, 'yAMwCUOKyCgDOYTEwZ', '2417', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:importBranch', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('优惠券黑白名单导入商品 ', 2371, 9, 'XZQjjwkxfknvgySCcX', '2371', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:importItem', '#', 'zksr', NOW(), '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('优惠券黑白名单导入门店', 2371, 8, 'XEsODZEdbzTHfQAoNA', '2371', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:importBranch', '#', 'zksr', NOW(), '', NULL, '', 'dc');

-- 入驻商订单表 新增字段  打印次数字段
ALTER TABLE `zksr-trade`.`trd_supplier_order`
    ADD COLUMN `print_qty` INT(11) DEFAULT 0 COMMENT '打印次数',
    ADD COLUMN `print_state` TINYINT(1) DEFAULT 0 COMMENT '打印状态 0：未打印，1：已打印'
;

-- 批次发券新增门店发放数量字段
ALTER TABLE `zksr-promotion`.`prm_coupon_batch`
    ADD COLUMN `branch_send_qty` int DEFAULT NULL COMMENT '门店发放数量';

-- 组合商品和组合商品详情和组合商品规则
CREATE TABLE `zksr-product`.`prdt_spu_combine`(
    `spu_combine_id` BIGINT(20) NOT NULL  COMMENT '组合商品id' ,
    `sys_code` BIGINT(20)   COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` VARCHAR(64)   COMMENT '更新人' ,
    `update_time` DATETIME(3)   COMMENT '更新时间' ,
    `func_scope` INT(2) NOT NULL  COMMENT '全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定）' ,
    `supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '城市id' ,
    `spu_combine_no` VARCHAR(32)   COMMENT '组合商品编号' ,
    `spu_combine_name` VARCHAR(32)   COMMENT '组合商品名' ,
    `thumb` VARCHAR(255)   COMMENT '封面图（url）' ,
    `thumb_video` VARCHAR(255)   COMMENT '封面视频（url）' ,
    `images` TEXT   COMMENT '详情页轮播（json）' ,
    `details` TEXT   COMMENT '详情信息(富文本)' ,
    `memo` VARCHAR(128)   COMMENT '备注' ,
    `is_delete` INT(1)   COMMENT '是否删除 1-是 0-否' ,
    `status` INT(1)   COMMENT '状态 1-启用 0-停用' ,
    `spec_name` VARCHAR(32)   COMMENT '商品规格' ,
    `total_limit` INT(10)   COMMENT '总限量' ,
    `min_oq` INT(8)   COMMENT '起订' ,
    `jump_oq` INT(8)   COMMENT '订货组数' ,
    `max_oq` INT(8)   COMMENT '限购' ,
    `unit` INT(3)   COMMENT '单位-数据字典（sys_prdt_unit）' ,
    `sale_price1` DECIMAL(10,2)   COMMENT '销售价1' ,
    `sale_price2` DECIMAL(10,2)   COMMENT '销售价2' ,
    `sale_price3` DECIMAL(10,2)   COMMENT '销售价3' ,
    `sale_price4` DECIMAL(10,2)   COMMENT '销售价4' ,
    `sale_price5` DECIMAL(10,2)   COMMENT '销售价5' ,
    `sale_price6` DECIMAL(10,2)   COMMENT '销售价6' ,
    PRIMARY KEY (spu_combine_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT = '组合商品';


CREATE TABLE `zksr-product`.`prdt_spu_combine_dtl` (
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `spu_combine_id` bigint DEFAULT NULL,
  `sku_id` bigint DEFAULT NULL,
  `sku_unit_type` int DEFAULT NULL COMMENT '商品单位大小',
  `qty` int DEFAULT NULL COMMENT '数量',
  `gift_flag` tinyint(1) DEFAULT NULL COMMENT '是否为赠品'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='组合商品详情';


CREATE TABLE `zksr_promotion`.`prm_cb_rule` (
  `cb_rule_id` bigint NOT NULL COMMENT '组合商品规则id',
  `sys_code` bigint NOT NULL COMMENT '平台商id',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `activity_id` bigint NOT NULL COMMENT '活动id',
  `spu_combine_id` bigint NOT NULL COMMENT '组合商品id',
  `shelf_class_id` bigint NOT NULL COMMENT '上架展示分类ID'
  PRIMARY KEY (`cb_rule_id`) USING BTREE,
  KEY `idx_spu_combine_id` (`spu_combine_id`) USING BTREE,
  KEY `idx_activity_id` (`activity_id`) USING BTREE,
  KEY `idx_shelf_class_id` (`shelf_class_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  ROW_FORMAT=DYNAMIC COMMENT='组合商品规则';

ALTER TABLE `zksr-product`.`prdt_area_item`
ADD COLUMN `item_type` tinyint(1) DEFAULT '0' COMMENT '0-普通商品, 1-组合商品',
ADD COLUMN `activity_start_time` datetime DEFAULT NULL COMMENT '活动开始时间, 活动商品',
ADD COLUMN `activity_end_time` datetime DEFAULT NULL COMMENT '活动结束时间, 活动商品',
ADD COLUMN `spu_combine_id` bigint DEFAULT NULL COMMENT '组合商品ID',
ADD COLUMN `activity_id` bigint DEFAULT NULL COMMENT '活动ID',
ADD KEY `idx_activity_id` (`activity_id`) USING BTREE,
ADD KEY `idx_spu_combine_id` (`spu_combine_id`) USING BTREE;

ALTER TABLE `zksr-product`.`prdt_supplier_item`
ADD COLUMN `item_type` tinyint(1) DEFAULT '0' COMMENT '0-普通商品, 1-组合商品',
ADD COLUMN `activity_start_time` datetime DEFAULT NULL COMMENT '活动开始时间, 活动商品',
ADD COLUMN `activity_end_time` datetime DEFAULT NULL COMMENT '活动结束时间, 活动商品',
ADD COLUMN `spu_combine_id` bigint DEFAULT NULL COMMENT '组合商品ID',
ADD COLUMN `activity_id` bigint DEFAULT NULL COMMENT '活动ID',
ADD KEY `idx_activity_id` (`activity_id`) USING BTREE,
ADD KEY `idx_spu_combine_id` (`spu_combine_id`) USING BTREE;

ADD COLUMN `branch_send_qty` int DEFAULT NULL COMMENT '门店发放数量';
-- 报表库 交易域订单事务事实表 新增字段
ALTER TABLE `zksr-report`.`dwd_trd_order_dtl_inc`
    ADD COLUMN `pay_state` TINYINT(1)  DEFAULT NULL COMMENT '支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消, 3-货到付款未支付, 4-货到付款已支付',
	ADD COLUMN `pay_way` VARCHAR(32) DEFAULT NULL COMMENT '支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款',
    ADD COLUMN `category_id` BIGINT(20) DEFAULT NULL COMMENT '管理分类id'
;

-- 报表库 交易域售后订单事务事实表 新增字段
ALTER TABLE `zksr-report`.`dwd_trd_return_inc`
    ADD COLUMN `approve_state` INT(11) DEFAULT NULL COMMENT '审核状态(数据字典);0待审核 1同意 2拒绝',
	ADD COLUMN `refund_state` INT(11) DEFAULT NULL COMMENT '退款状态(数据字典);0-未退款 1-退款中 2-退款完成  3-退款失败'
;


DROP TABLE IF EXISTS dwd_colonel_visit_inc;
CREATE TABLE dwd_colonel_visit_inc(
                                      `colonel_visit_log_id` BIGINT(20) NOT NULL  COMMENT '业务员拜访日志id' ,
                                      `create_by` VARCHAR(64)   COMMENT '创建人' ,
                                      `create_time` DATETIME(3)   COMMENT '创建时间' ,
                                      `update_by` VARCHAR(64)   COMMENT '更新人' ,
                                      `update_time` DATETIME(3)   COMMENT '更新时间' ,
                                      `sys_code` BIGINT(20)   COMMENT '平台商id' ,
                                      `area_id` BIGINT(20)   COMMENT '区域城市id' ,
                                      `colonel_id` BIGINT(20)   COMMENT '业务员ID' ,
                                      `branch_id` BIGINT(20)   COMMENT '门店ID' ,
                                      `sign_in_longitude` VARCHAR(30)   COMMENT '签到经度' ,
                                      `sign_in_latitude` VARCHAR(30)   COMMENT '签到纬度' ,
                                      `sign_in_address` VARCHAR(255)   COMMENT '签到地址' ,
                                      `sign_in_distance` VARCHAR(50)   COMMENT '签到距离' ,
                                      `sign_in_img_urls` TEXT(255)   COMMENT '签到图片链接：多个以英文，隔开' ,
                                      `sign_in_date` DATETIME(3)   COMMENT '签到时间' ,
                                      `sign_out_longitude` VARCHAR(30)   COMMENT '签退经度' ,
                                      `sign_out_latitude` VARCHAR(30)   COMMENT '签退纬度' ,
                                      `sign_out_address` VARCHAR(255)   COMMENT '签退地址' ,
                                      `sign_out_distance` VARCHAR(50)   COMMENT '签退距离' ,
                                      `sign_out_date` DATETIME(3)   COMMENT '签退时间' ,
                                      `visit_interval_time` VARCHAR(20)   COMMENT '拜访间隔时间' ,
                                      `visit_flag` int(11) DEFAULT NULL COMMENT '拜访状态 0-签到 1-签退 2-作废',
                                      `visit_flag_val` VARCHAR(20) DEFAULT NULL COMMENT '拜访状态 对应值',
                                      `memo` varchar(255) DEFAULT NULL COMMENT '备注',
                                      `date_id`BIGINT(20)   COMMENT '日期ID' ,
                                      `insert_date_id`BIGINT(20)   COMMENT '插入时间id' ,
                                      PRIMARY KEY (colonel_visit_log_id)
)  COMMENT = '业务员域拜访事务事实表';

DROP TABLE IF EXISTS dwd_login_his_inc;
CREATE TABLE dwd_login_his_inc(
                                  `login_his_id` BIGINT(20) NOT NULL  COMMENT '登录历史Id' ,
                                  `create_time` DATETIME(3)   COMMENT '创建时间' ,
                                  `sys_code` BIGINT(20)   COMMENT '平台商id' ,
                                  `date_id` INT(11) DEFAULT NULL COMMENT '日期;yyyyMMdd',
                                  `wx_openid` VARCHAR(100) DEFAULT NULL COMMENT '微信openid;后台登录信息获取',
                                  `member_phone` VARCHAR(16) DEFAULT NULL COMMENT '用户手机号;后台登录信息获取',
                                  `member_username` VARCHAR(255) DEFAULT NULL COMMENT '用户名;后台登录信息获取',
                                  `member_id` BIGINT(20) DEFAULT NULL COMMENT '用户id;后台登录信息获取',
                                  `branch_id` BIGINT(20) DEFAULT NULL COMMENT '门店id;后台登录信息获取',
                                  `area_id` BIGINT(20)   COMMENT '区域城市id' ,
                                  `ip` VARCHAR(32) DEFAULT NULL COMMENT 'ip地址;http_request',
                                  `district` VARCHAR(32) DEFAULT NULL COMMENT 'ip地址归属地;http_request',
                                  `tp` VARCHAR(8) DEFAULT NULL COMMENT '类型（数据字典）;0-登陆  1-访问',
                                  `device_id` VARCHAR(128) DEFAULT NULL COMMENT '设备id;前端传（HttpHeader）',
                                  `device_type` VARCHAR(32) DEFAULT NULL COMMENT '设备类型;前端传（HttpHeader）',
                                  `device_brand` VARCHAR(32) DEFAULT NULL COMMENT '设备品牌;前端传（HttpHeader）',
                                  `device_model` VARCHAR(32) DEFAULT NULL COMMENT '设备型号;前端传（HttpHeader）',
                                  `os_name` VARCHAR(32) DEFAULT NULL COMMENT '系统名称;前端传（HttpHeader）',
                                  `os_version` VARCHAR(32) DEFAULT NULL COMMENT '操作系统版本;前端传（HttpHeader）',
                                  `port` VARCHAR(32) DEFAULT NULL COMMENT 'pc app xcx',
                                  `insert_date_id`BIGINT(20)   COMMENT '插入时间id' ,
                                  PRIMARY KEY (login_his_id)
)  COMMENT = '门店登录域事务事实表';

-- 商品基本信息新增字段 辅助商品编号
ALTER TABLE `zksr_product`.`prdt_spu`
    ADD COLUMN `auxiliary_spu_no` VARCHAR(32) DEFAULT NULL COMMENT 'SPU辅助的商品编号';




-- 促销满赠改造  新增字段
ALTER TABLE `zksr_promotion`.`prm_fg_rule`
    ADD COLUMN `buy_sku_num` int(8) DEFAULT 1 COMMENT '购买品项数(sku种类数)默认为1',
ADD COLUMN `gift_group_type` tinyint(1) DEFAULT 2 COMMENT '赠送方式(默认为全赠 0仅一种，1任选，2全赠)',
ADD COLUMN `gift_sku_unit_qty` int(8) DEFAULT NULL COMMENT '赠送单位数量';

-- 满赠改造  新增数据字典
INSERT INTO `zksr_cloud`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '满赠-赠送方式', 'gift_group_type', '0', 'zksr', '2024-12-31 11:02:19', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '仅一种', '0', 'gift_group_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-31 11:02:39', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '任选', '1', 'gift_group_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-31 11:02:45', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '全赠', '2', 'gift_group_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-31 11:02:52', '', NULL, NULL);


-- 对接好帮你 新增数据字典系统类型
INSERT INTO `zksr_cloud`.`sys_dict_data`( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '好帮你ERP', '6', 'source_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-02 16:12:31', 'zksr', '2024-12-02 16:12:38', NULL);

-- 区域城市导入权限
INSERT INTO `zksr_cloud`.sys_menu (menu_name, parent_id, order_num, menu_code, menu_pcode, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, func_scop) VALUES ('区域城市导入', 2074, 6, 'pkJdfczhmffmhwIwFr', '2074', '', null, null, 1, 0, 'F', '0', '0', 'system:area:import', '#', 'zksr', NOW(), '', null, '', 'software,partner,dc');

    ADD COLUMN `branch_send_qty` int DEFAULT NULL COMMENT '门店发放数量';

-- 组合商品和组合商品详情和组合商品规则
CREATE TABLE `zksr-product`.`prdt_spu_combine`(
        `spu_combine_id` bigint(20) NOT NULL,
        `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商编号',
        `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
        `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
        `supplier_id` bigint(20) DEFAULT NULL COMMENT '供应商ID',
        `area_id` bigint(20) DEFAULT NULL COMMENT '区域ID',
        `category_id` bigint(20) DEFAULT NULL COMMENT '管理分类ID',
        `spu_combine_no` varchar(32) DEFAULT NULL COMMENT '组合商品编号',
        `spu_combine_name` varchar(32) DEFAULT NULL COMMENT '组合商品名称',
        `thumb` varchar(255) DEFAULT NULL COMMENT '封面图片',
        `thumb_video` varchar(255) DEFAULT NULL COMMENT '封面视频',
        `images` text COMMENT '详情页轮播图',
        `details` text COMMENT '组合商品描述,富文本',
        `memo` varchar(128) DEFAULT NULL COMMENT '备注',
        `is_delete` int(11) DEFAULT NULL COMMENT '1-删除, 0-正常',
        `status` int(11) DEFAULT NULL COMMENT '1-启用,0-停用',
        `spec_name` varchar(32) DEFAULT NULL COMMENT '规格名称',
        `total_limit` int(11) DEFAULT NULL COMMENT '总限量',
        `min_oq` int(11) DEFAULT NULL COMMENT '最新起订',
        `jump_oq` INT(11) DEFAULT 1 COMMENT '订货组数',
        `max_oq` int(11) DEFAULT NULL COMMENT '最大限购',
        `mark_price` decimal(12,2) DEFAULT NULL COMMENT '标准价',
        `suggest_price` decimal(12,2) DEFAULT NULL COMMENT '建议零售价',
        `sale_price1` decimal(12,2) DEFAULT NULL COMMENT '销售价1',
        `sale_price2` decimal(12,2) DEFAULT NULL COMMENT '销售价2',
        `sale_price3` decimal(12,2) DEFAULT NULL COMMENT '销售价3',
        `sale_price4` decimal(12,2) DEFAULT NULL COMMENT '销售价4',
        `sale_price5` decimal(12,2) DEFAULT NULL COMMENT '销售价5',
        `sale_price6` decimal(12,2) DEFAULT NULL COMMENT '销售价6',
        `unit` int(10) DEFAULT NULL COMMENT '单位/字典值',
    PRIMARY KEY (spu_combine_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT = '组合商品';


CREATE TABLE `zksr-product`.`prdt_spu_combine_dtl` (
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `spu_combine_id` bigint(20) DEFAULT NULL,
  `area_item_id` bigint(20) DEFAULT NULL,
  `supplier_item_id` bigint(20) DEFAULT NULL,
  `sku_id` bigint DEFAULT NULL,
  `sku_unit_type` int DEFAULT NULL COMMENT '商品单位大小',
  `qty` int DEFAULT NULL COMMENT '数量',
  `gift_flag` tinyint(1) DEFAULT NULL COMMENT '是否为赠品'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='组合商品详情';


CREATE TABLE `zksr_promotion`.`prm_cb_rule` (
  `cb_rule_id` bigint NOT NULL COMMENT '组合商品规则id',
  `sys_code` bigint NOT NULL COMMENT '平台商id',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `activity_id` bigint NOT NULL COMMENT '活动id',
  `spu_combine_id` bigint NOT NULL COMMENT '组合商品id',
  `shelf_class_id` bigint NOT NULL COMMENT '上架展示分类ID'
  PRIMARY KEY (`cb_rule_id`) USING BTREE,
  KEY `idx_spu_combine_id` (`spu_combine_id`) USING BTREE,
  KEY `idx_activity_id` (`activity_id`) USING BTREE,
  KEY `idx_shelf_class_id` (`shelf_class_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  ROW_FORMAT=DYNAMIC COMMENT='组合商品规则';

ALTER TABLE `zksr-product`.`prdt_area_item`
ADD COLUMN `item_type` tinyint(1) DEFAULT '0' COMMENT '0-普通商品, 1-组合商品',
ADD COLUMN `activity_start_time` datetime DEFAULT NULL COMMENT '活动开始时间, 活动商品',
ADD COLUMN `activity_end_time` datetime DEFAULT NULL COMMENT '活动结束时间, 活动商品',
ADD COLUMN `spu_combine_id` bigint DEFAULT NULL COMMENT '组合商品ID',
ADD COLUMN `activity_id` bigint DEFAULT NULL COMMENT '活动ID',
ADD KEY `idx_activity_id` (`activity_id`) USING BTREE,
ADD KEY `idx_spu_combine_id` (`spu_combine_id`) USING BTREE;

ALTER TABLE `zksr-product`.`prdt_supplier_item`
ADD COLUMN `item_type` tinyint(1) DEFAULT '0' COMMENT '0-普通商品, 1-组合商品',
ADD COLUMN `activity_start_time` datetime DEFAULT NULL COMMENT '活动开始时间, 活动商品',
ADD COLUMN `activity_end_time` datetime DEFAULT NULL COMMENT '活动结束时间, 活动商品',
ADD COLUMN `spu_combine_id` bigint DEFAULT NULL COMMENT '组合商品ID',
ADD COLUMN `activity_id` bigint DEFAULT NULL COMMENT '活动ID',
ADD KEY `idx_activity_id` (`activity_id`) USING BTREE,
ADD KEY `idx_spu_combine_id` (`spu_combine_id`) USING BTREE;

INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销商品详情', 2723, 6, 'ulXCDHHadsocIcmvmF', 'qgpijUvAmWiWljwYkf', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:cb-rule:query', '#', 'zksr', '2025-01-08 11:01:08', 'zksr', '2025-01-08 19:22:54', '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销启用', 2723, 5, 'DXpObNjmIyZdFCkLHQ', 'qgpijUvAmWiWljwYkf', '', NULL, NULL, 1, 0, 'F', '0', '0', ' promotion:cb-rule:enable', '#', 'zksr', '2025-01-08 10:57:43', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销停用', 2723, 4, 'OEqvmfoiGiWytOyNly', 'qgpijUvAmWiWljwYkf', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:cb-rule:disable', '#', 'zksr', '2025-01-08 10:57:17', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销新增', 2723, 3, 'MtNwhhOLUhYdqoGGaD', 'qgpijUvAmWiWljwYkf', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:cb-rule:add', '#', 'zksr', '2025-01-08 10:56:38', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销编辑', 2723, 2, 'AtcsnWWYvBEyAyvowg', 'qgpijUvAmWiWljwYkf', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:cb-rule:edit', '#', 'zksr', '2025-01-08 10:55:10', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销列表', 2723, 1, 'vOhYRdjncQpQpmipyM', 'qgpijUvAmWiWljwYkf', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:combine:list', '#', 'zksr', '2025-01-08 10:54:07', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销', 2368, 11, 'qgpijUvAmWiWljwYkf', '2368', '/combine', 'operation/promotion/combine/index', '{\"source\": 2}', 1, 0, 'C', '0', '0', '', '#', 'zksr', '2025-01-08 10:49:57', 'zksr', '2025-01-08 11:14:57', '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销商品详情', 2716, 6, 'lrShaNZIGeAjiApOXK', 'wsFwBucvaBkgmUXvzX', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:cb-rule:query', '#', 'zksr', '2025-01-07 15:27:01', 'zksr', '2025-01-08 19:22:29', '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销启用', 2716, 5, 'EOQjKhJIlejIQVesLo', 'wsFwBucvaBkgmUXvzX', '', NULL, NULL, 1, 0, 'F', '0', '0', ' promotion:cb-rule:enable', '#', 'zksr', '2025-01-07 15:26:07', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销停用', 2716, 4, 'YPfckXhJfKjxGSZrie', 'wsFwBucvaBkgmUXvzX', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:cb-rule:disable', '#', 'zksr', '2025-01-07 15:25:38', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销新增', 2716, 3, 'ZkcsCinCYcDvsfYGXN', 'wsFwBucvaBkgmUXvzX', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:cb-rule:add', '#', 'zksr', '2025-01-07 15:24:38', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销编辑', 2716, 2, 'sEeQlXBpAWDANuMdPq', 'wsFwBucvaBkgmUXvzX', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:cb-rule:edit', '#', 'zksr', '2025-01-07 15:23:53', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销列表', 2716, 1, 'hNxeEvrbBAMBsUllEr', 'wsFwBucvaBkgmUXvzX', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:combine:list', '#', 'zksr', '2025-01-07 15:02:56', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '组合促销', 2027, 10, 'wsFwBucvaBkgmUXvzX', '2027', '/combine', 'operation/promotion/combine/index', '{\"source\": 1}', 1, 0, 'C', '0', '0', '', '#', 'zksr', '2024-12-30 09:56:47', 'zksr', '2025-01-08 11:14:26', '', 'dc,partner');


-- 订单编号改造  涉及对接第三方对接收款单模板更新
-- 安得ERP收款单同步
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n    \"companyName\": \"${branchName}\",\n    \"companyNo\": \"${branchNo}\",\n    \"companyType\": \"1\",\n    \"receiptType\": 0,\n    \"sheetDate\": \"${sheetDateString}\",\n    \"sheetType\": 0,\n    #if($sheetType == \"XR\")\n    \"changeType\": \"+\",\n    #elseif($sheetType == \"SR\")\n    \"changeType\": \"-\",\n    #else\n    \"changeType\": \"\",\n    #end\n    \"sheetNo\": \"${supplierOrderNo}\",\n     \"subList\": [\n      {\n        \"accountNo\": \"06\",\n        \"sheetAmt\": ${totalReceiptAmt}\n      }\n     ]\n}' WHERE `template_name` = '安得ERP收款单同步';


-- 福商通ERP收款单同步
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n  \"sheetAmt\":${totalReceiptAmt},       \n  \"actualAmtMB\":${totalReceiptExactAmt},   \n  \"consumerNo\":\"${branchNo}\",\n    \n  \"payMastersB2B\": [\n    #foreach( $sub in $detailList)\n    {\n  #if($sub.sheetType == \"XR\")\n  \"transNo\": \"XS\",  \n  \"sheetNo\":\"${sub.supplierOrderNo}\",      \n  #elseif($sub.sheetType == \"SJ\")\n  \"transNo\": \"JS\",\n  \"refundWay\":\"2\",\n  \"sheetNo\":\"${sub.supplierOrderNo}\",      \n  #elseif($sub.sheetType == \"SC\")\n  \"transNo\": \"XY\",\n  \"refundWay\":\"1\",    \n  \"sheetNo\":\"${sub.supplierOrderNo}\",      \n  #elseif($sub.sheetType == \"SR\" && $sub.isProceeds == 1)\n  \"transNo\": \"XT\",\n  \"refundWay\":\"3\",   \n  \"sheetNo\":\"${sub.supplierAfterNo}\",      \n  #elseif($sub.sheetType == \"SR\" && $sub.isProceeds == 0)\n  \"transNo\": \"QKSH\",\n  \"refundWay\":\"3\",     \n  \"sheetNo\":\"${sub.supplierOrderNo}\",      \n  #else  \n  \"transNo\": \"\",    \n  #end\n  #if($sub.payWay == \"0\")\n  \"payWay\": \"1\",\n  #elseif($sub.payWay == \"2\")\n  \"payWay\": \"2\",\n  #else  \n  \"payWay\": \"\" ,\n  #end        \n  \"sheetAmt\": \"${sub.receiptAmt}\",\n  \"actualAmtMB\": \"${sub.receiptExactAmt}\"      \n      }#if($foreach.hasNext),#end\n    #end\n    ]\n    }' WHERE `template_name` = '福商通ERP收款单同步';

-- 湘无界ERP收款单同步
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n    #set($firstItem = $detailList.get(0))\n    #set($payOpenList = $firstItem.payOpenList)\n  \"store_id\":\"${branchNo}\",\n  #if($firstItem.sheetType == \'XR\')\n    \"ori_order_id\": \"${firstItem.supplierOrderNo}\", \n    \"type\":\"1\",\n    #else\n    \"ori_order_id\": \"${firstItem.supplierAfterNo}\", \n    \"yao_ori_order_id\": \"${firstItem.supplierOrderNo}\", \n    \"type\":\"2\",\n    #end  \n    \"pay_list\": [\n        #foreach( $payOpenDTO in $payOpenList)\n        { \n        \"ori_trade_no\":\"${payOpenDTO.sheetTradeNo}\", \n       \"channel\":\"${payOpenDTO.platform}\", \n       \"amount\":${payOpenDTO.payAmt}, \n       \"money\":${payOpenDTO.payAmt},  \n       \"coupon\": 0   \n        }#if($foreach.hasNext),#end\n        #end\n    ]		\n}' WHERE `template_name` = '湘无界ERP收款单同步';

-- 好帮你ERP收款单同步
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n#set($firstItem = $detailList.get(0))  \n#set($payOpenList = $firstItem.payOpenList)  \n#set($firstPay = $payOpenList.get(0))    \n\"customerCode\": \"${branchNo}\",\n\"businessDate\": \"${firstItem.sheetDateString}\",\n  #if($firstItem.sheetType == \'XR\')\n    \"customerBillNo\": \"${firstItem.supplierOrderNo}\", \n    \"remark\": \"B2B入驻商订单号：${firstItem.supplierOrderNo},ERP订单号：${firstItem.sourceOrderNo}\", \n    #else\n    \"customerBillNo\": \"${firstItem.supplierAfterNo}\",  \n    \"remark\": \"B2B入驻商售后单号：${firstItem.supplierAfterNo},ERP退单号：${firstItem.sourceAfterNo}\",       \n    #end    \n #if(\"$!{$firstPay}\" && \"$!{$firstPay.platform}\")\n     \"bankCode\": \"${firstPay.platform}\",\n  #end      \n\"collectionAmount\": ${totalReceiptAmt}\n}' WHERE `template_name` = '好帮你ERP收款单同步';

-- ERP11.0收款单同步
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n#set($firstItem = $detailList.get(0))  \n  \n## 1.在线支付订单支付 onlineYhPay \n#if($firstItem.sheetType == \"XR\"  && $firstItem.payWay == 0)   \n\"sheetNo\": \"${firstItem.sourceOrderNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierOrderNo}\",\n\"type\": \"onlineYhPay\",   \n  \n## 2.货到付款收款 deliveryYhPay   \n#elseif($firstItem.sheetType == \"XR\"  && $firstItem.payWay == 2)   \n\"sheetNo\": \"${firstItem.sourceOrderNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierOrderNo}\",\n\"type\": \"deliveryYhPay\",   \n  \n## 3.差异出库退款（线上支付、货到付款）  differenceDoPay\n#elseif($firstItem.sheetType == \"SC\")\n\"sheetNo\": \"${firstItem.sourceOrderNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierOrderNo}\",\n\"type\": \"differenceDoPay\",      \n    \n## 4.拒收退货退款（线上支付、货到付款） rejectDrPay   \n#elseif($firstItem.sheetType == \"SJ\")\n\"sheetNo\": \"${firstItem.sourceOrderNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierOrderNo}\",\n\"type\": \"rejectDrPay\",   \n    \n## 5.售后退货退款（线上支付、货到付款）  afterSales \n#elseif($firstItem.sheetType == \"SR\")\n\"sheetNo\": \"${firstItem.sourceAfterNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierAfterNo}\",\n\"type\": \"afterSales\",   \n#else  \n\"sheetNo\": \"\",\n\"sourceSheetNo\": \"\",\n\"type\": \"\",    \n#end\n\"amt\": ${totalReceiptExactAmt}\n}' WHERE `template_name` = 'ERP11.0收款单同步';


DROP TABLE IF EXISTS `zksr_product`.`prdt_material`;
CREATE TABLE `zksr_product`.`prdt_material`(
                                               `material_id` BIGINT(20) NOT NULL  COMMENT '素材id' ,
                                               `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
                                               `create_by` VARCHAR(64)   COMMENT '创建人' ,
                                               `create_time` DATETIME(3)   COMMENT '创建时间' ,
                                               `update_by` VARCHAR(64)   COMMENT '更新人' ,
                                               `update_time` DATETIME(3)   COMMENT '更新时间' ,
                                               `name` VARCHAR(32) NOT NULL  COMMENT '素材名称' ,
                                               `img` VARCHAR(255) NOT NULL  COMMENT '素材图片地址' ,
                                               `img_size` VARCHAR(8) NOT NULL  COMMENT '素材图片大小' ,
                                               `status` TINYINT(1)   COMMENT '状态 1-启用 0-停用' ,
                                               PRIMARY KEY (material_id)
)  COMMENT = '素材表';

DROP TABLE IF EXISTS `zksr_product`.`prdt_material_apply`;
CREATE TABLE `zksr_product`.`prdt_material_apply`(
                                                     `material_apply_id` BIGINT(20) NOT NULL  COMMENT '素材应用id' ,
                                                     `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
                                                     `create_by` VARCHAR(64)   COMMENT '创建人' ,
                                                     `create_time` DATETIME(3)   COMMENT '创建时间' ,
                                                     `update_by` VARCHAR(64)   COMMENT '更新人' ,
                                                     `update_time` DATETIME(3)   COMMENT '更新时间' ,
                                                     `material_id` BIGINT(20) NOT NULL  COMMENT '素材id' ,
                                                     `apply_type` TINYINT(1) NOT NULL  COMMENT '素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品' ,
                                                     `apply_id` BIGINT(20)   COMMENT '素材应用类型id' ,
                                                     `start_time` DATETIME   COMMENT '生效时间' ,
                                                     `end_time` DATETIME   COMMENT '失效时间' ,
                                                     `apply_user_id` BIGINT(20)   COMMENT '操作人' ,
                                                     PRIMARY KEY (material_apply_id)
)  COMMENT = '素材应用表';

-- 素材功能相关菜单信息
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('商品素材管理', 2731, 88, 'wLRhLHHlMOCzfYpyRP', 'ZMbSRJAhKvXZFWpIXo', 'materialManagement', 'platform/materialManagement/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2025-01-08 15:50:35', 'zksr', '2025-01-10 09:57:30', '', 'partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材管理', 0, 10, 'ZMbSRJAhKvXZFWpIXo', '0', '/material', NULL, NULL, 1, 0, 'M', '0', '0', '', 'tool', 'zksr', '2025-01-10 09:56:45', 'zksr', '2025-01-10 10:01:04', '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材查询', 2730, 0, 'VuNgBsyBqtZADQYdjv', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:query', '#', 'zksr', '2025-01-10 09:59:51', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材新增', 2730, 0, 'FfIGFtQJzLyYvwVDdA', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:add', '#', 'zksr', '2025-01-10 10:00:19', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材修改', 2730, 0, 'dGaUnzoSTZASeGdgCN', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:edit', '#', 'zksr', '2025-01-10 10:00:41', 'zksr', '2025-01-10 10:01:01', '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材删除', 2730, 0, 'zpntxFSenwSTKtdSUd', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:remove', '#', 'zksr', '2025-01-10 10:01:33', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材导出', 2730, 0, 'jFSDxhgGeErlBaZAJF', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:export', '#', 'zksr', '2025-01-10 10:01:53', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材打标管理', 2731, 1, 'EjarFEReoGVNppgfqv', 'ZMbSRJAhKvXZFWpIXo', 'materialMarking', 'platform/materialMarking/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2025-01-10 10:03:04', 'zksr', '2025-01-11 09:05:09', '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材应用新增', 2737, 0, 'qQXYbvAjKccDshesvZ', 'EjarFEReoGVNppgfqv', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:materialApply:add', '#', 'zksr', '2025-01-10 10:03:34', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材应用修改', 2737, 0, 'DjzkvVgRJeRqhazebX', 'EjarFEReoGVNppgfqv', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:materialApply:edit', '#', 'zksr', '2025-01-10 10:03:58', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材应用删除', 2737, 0, 'qktGRphwoUjjcKiOCT', 'EjarFEReoGVNppgfqv', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:materialApply:remove', '#', 'zksr', '2025-01-10 10:04:17', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材应用导出', 2737, 0, 'uPlIytpKBUvanilvle', 'EjarFEReoGVNppgfqv', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:materialApply:export', '#', 'zksr', '2025-01-10 10:04:37', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材列表', 2730, 0, 'xIBWotmyTrAmApAAja', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:list', '#', 'zksr', '2025-01-10 10:13:57', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材应用列表', 2737, 0, 'HGonFLQDnazfVovcnQ', 'EjarFEReoGVNppgfqv', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:materialApply:list', '#', 'zksr', '2025-01-10 10:14:32', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材停用', 2730, 0, 'BWEKiOGckEJXVGPVTM', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:disable', '#', 'zksr', '2025-01-10 14:47:05', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材启用', 2730, 0, 'EMkuqyReusicNQEvwV', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:enable', '#', 'zksr', '2025-01-10 14:47:23', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材应用查询', 2737, 0, 'llABIAOLqIavuIfRJb', 'EjarFEReoGVNppgfqv', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:materialApply:query', '#', 'zksr', '2025-01-13 18:03:11', '', NULL, '', 'dc,partner');


-- 平台商字典新增表
CREATE TABLE `sys_partner_dict_type` (
                                         `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
                                         `sys_code` bigint NOT NULL COMMENT '平台商编号',
                                         `dict_name` varchar(100) DEFAULT '' COMMENT '字典名称',
                                         `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
                                         `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                         `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                         `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                         `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                         PRIMARY KEY (`dict_id`) USING BTREE,
                                         KEY `dict_type` (`dict_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=110 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='平台商字典类型表';


CREATE TABLE `sys_partner_dict_data` (
                                         `dict_code` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
                                         `sys_code` bigint(20) NOT NULL COMMENT '平台商编号',
                                         `dict_sort` int(4) DEFAULT '0' COMMENT '字典排序',
                                         `dict_label` varchar(100) DEFAULT '' COMMENT '字典标签',
                                         `dict_value` varchar(100) DEFAULT '' COMMENT '字典键值',
                                         `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
                                         `css_class` varchar(100) DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
                                         `list_class` varchar(100) DEFAULT NULL COMMENT '表格回显样式',
                                         `is_default` char(1) DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
                                         `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                         `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                         `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                         `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                         PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=596 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='平台商字典数据表';

-- 平台商字典菜单
INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2778, '字典删除', 2729, 4, 'VNooudsoWFuomJhcPS', 'jCtwbnehGKJAHKJAtm', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerDict:remove', '#', 'zksr', '2025-01-14 17:04:07', 'zksr', '2025-01-14 17:04:33', '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2777, '字典修改', 2729, 3, 'CxwRJcFHAifuEcarWT', 'jCtwbnehGKJAHKJAtm', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerDict:edit', '#', 'zksr', '2025-01-14 17:03:30', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2776, '字典新增', 2729, 2, 'KTKXzitWeLTQlrNxFT', 'jCtwbnehGKJAHKJAtm', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerDict:add', '#', 'zksr', '2025-01-14 17:03:05', 'zksr', '2025-01-14 17:03:37', '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2775, '查看详情', 2729, 1, 'kPqxKifdwXMvuZtkKP', 'jCtwbnehGKJAHKJAtm', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerDict:query', '#', 'zksr', '2025-01-14 17:02:38', '', NULL, '', 'partner');

INSERT INTO `zksr-cloud`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2729, '平台商数据字典', 1, 11, 'jCtwbnehGKJAHKJAtm', '1', 'aftersaleDictionary', 'system/aftersaleDictionary/index', NULL, 1, 0, 'C', '0', '0', 'system:partnerDict:list', '#', 'zksr', '2025-01-03 14:28:36', 'zksr', '2025-01-14 17:06:42', '', 'partner,software');

-- 商品配送标签
ALTER TABLE `zksr-cloud`.`sys_supplier`
    ADD COLUMN `product_distribution_label` varchar(100) COMMENT '商品配送标签' AFTER `avatar`;

ALTER TABLE `zksr-cloud`.`sys_partner_policy`
    ADD COLUMN `product_distribution_label` varchar(100) COMMENT '商品配送标签' AFTER `supplier_id`;


-- =======================订单编号改造  涉及对接第三方对接收款单模板还原=========
-- 安得ERP收款单同步模板还原
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n    \"companyName\": \"${branchName}\",\n    \"companyNo\": \"${branchNo}\",\n    \"companyType\": \"1\",\n    \"receiptType\": 0,\n    \"sheetDate\": \"${sheetDateString}\",\n    \"sheetType\": 0,\n    #if($sheetType == \"XSS\")\n    \"changeType\": \"+\",\n    #elseif($sheetType == \"SHS\")\n    \"changeType\": \"-\",\n    #else\n    \"changeType\": \"\",\n    #end\n    \"sheetNo\": \"${supplierOrderNo}\",\n     \"subList\": [\n      {\n        \"accountNo\": \"06\",\n        \"sheetAmt\": ${totalReceiptAmt}\n      }\n     ]\n}' WHERE `template_name` = '安得ERP收款单同步';
-- 福商通ERP收款单同步模板还原
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n  \"sheetAmt\":${totalReceiptAmt},       \n  \"actualAmtMB\":${totalReceiptExactAmt},   \n  \"consumerNo\":\"${branchNo}\",\n    \n  \"payMastersB2B\": [\n    #foreach( $sub in $detailList)\n    {\n  #if($sub.sheetType == \"XSS\")\n  \"transNo\": \"XS\",  \n  \"sheetNo\":\"${sub.supplierOrderNo}\",      \n  #elseif($sub.sheetType == \"SHJ\")\n  \"transNo\": \"JS\",\n  \"refundWay\":\"2\",\n  \"sheetNo\":\"${sub.supplierOrderNo}\",      \n  #elseif($sub.sheetType == \"SHC\")\n  \"transNo\": \"XY\",\n  \"refundWay\":\"1\",    \n  \"sheetNo\":\"${sub.supplierOrderNo}\",      \n  #elseif($sub.sheetType == \"SHS\" && $sub.isProceeds == 1)\n  \"transNo\": \"XT\",\n  \"refundWay\":\"3\",   \n  \"sheetNo\":\"${sub.supplierAfterNo}\",      \n  #elseif($sub.sheetType == \"SHS\" && $sub.isProceeds == 0)\n  \"transNo\": \"QKSH\",\n  \"refundWay\":\"3\",     \n  \"sheetNo\":\"${sub.supplierOrderNo}\",      \n  #else  \n  \"transNo\": \"\",    \n  #end\n  #if($sub.payWay == \"0\")\n  \"payWay\": \"1\",\n  #elseif($sub.payWay == \"2\")\n  \"payWay\": \"2\",\n  #else  \n  \"payWay\": \"\" ,\n  #end        \n  \"sheetAmt\": \"${sub.receiptAmt}\",\n  \"actualAmtMB\": \"${sub.receiptExactAmt}\"      \n      }#if($foreach.hasNext),#end\n    #end\n    ]\n    }' WHERE `template_name` = '福商通ERP收款单同步';

-- 湘无界ERP收款单同步模板还原
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n    #set($firstItem = $detailList.get(0))\n    #set($payOpenList = $firstItem.payOpenList)\n  \"store_id\":\"${branchNo}\",\n  #if($firstItem.sheetType == \'XSS\')\n    \"ori_order_id\": \"${firstItem.supplierOrderNo}\", \n    \"type\":\"1\",\n    #else\n    \"ori_order_id\": \"${firstItem.supplierAfterNo}\", \n    \"yao_ori_order_id\": \"${firstItem.supplierOrderNo}\", \n    \"type\":\"2\",\n    #end  \n    \"pay_list\": [\n        #foreach( $payOpenDTO in $payOpenList)\n        { \n        \"ori_trade_no\":\"${payOpenDTO.sheetTradeNo}\", \n       \"channel\":\"${payOpenDTO.platform}\", \n       \"amount\":${payOpenDTO.payAmt}, \n       \"money\":${payOpenDTO.payAmt},  \n       \"coupon\": 0   \n        }#if($foreach.hasNext),#end\n        #end\n    ]		\n}' WHERE `template_name` = '湘无界ERP收款单同步';

-- 好帮你ERP收款单同步模板还原
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n#set($firstItem = $detailList.get(0))  \n#set($payOpenList = $firstItem.payOpenList)  \n#set($firstPay = $payOpenList.get(0))    \n\"customerCode\": \"${branchNo}\",\n\"businessDate\": \"${firstItem.sheetDateString}\",\n  #if($firstItem.sheetType == \'XSS\')\n    \"customerBillNo\": \"${firstItem.supplierOrderNo}\", \n    \"remark\": \"B2B入驻商订单号：${firstItem.supplierOrderNo},ERP订单号：${firstItem.sourceOrderNo}\", \n    #else\n    \"customerBillNo\": \"${firstItem.supplierAfterNo}\",  \n    \"remark\": \"B2B入驻商售后单号：${firstItem.supplierAfterNo},ERP退单号：${firstItem.sourceAfterNo}\",       \n    #end    \n #if(\"$!{$firstPay}\" && \"$!{$firstPay.platform}\")\n     \"bankCode\": \"${firstPay.platform}\",\n  #end      \n\"collectionAmount\": ${totalReceiptAmt}\n}' WHERE `template_name` = '好帮你ERP收款单同步';

-- ERP11.0收款单同步模板还原
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n#set($firstItem = $detailList.get(0))  \n  \n## 1.在线支付订单支付 onlineYhPay \n#if($firstItem.sheetType == \"XSS\"  && $firstItem.payWay == 0)   \n\"sheetNo\": \"${firstItem.sourceOrderNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierOrderNo}\",\n\"type\": \"onlineYhPay\",   \n  \n## 2.货到付款收款 deliveryYhPay   \n#elseif($firstItem.sheetType == \"XSS\"  && $firstItem.payWay == 2)   \n\"sheetNo\": \"${firstItem.sourceOrderNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierOrderNo}\",\n\"type\": \"deliveryYhPay\",   \n  \n## 3.差异出库退款（线上支付、货到付款）  differenceDoPay\n#elseif($firstItem.sheetType == \"SHC\")\n\"sheetNo\": \"${firstItem.sourceOrderNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierOrderNo}\",\n\"type\": \"differenceDoPay\",      \n    \n## 4.拒收退货退款（线上支付、货到付款） rejectDrPay   \n#elseif($firstItem.sheetType == \"SHJ\")\n\"sheetNo\": \"${firstItem.sourceOrderNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierOrderNo}\",\n\"type\": \"rejectDrPay\",   \n    \n## 5.售后退货退款（线上支付、货到付款）  afterSales \n#elseif($firstItem.sheetType == \"SHS\")\n\"sheetNo\": \"${firstItem.sourceAfterNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierAfterNo}\",\n\"type\": \"afterSales\",   \n#else  \n\"sheetNo\": \"\",\n\"sourceSheetNo\": \"\",\n\"type\": \"\",    \n#end\n\"amt\": ${totalReceiptExactAmt}\n}' WHERE `template_name` = 'ERP11.0收款单同步';


-- 门店信息新增按钮菜单  区域门店信息初始化同步到入驻商第三方系统
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店信息同步', 2193, 1, 'khnvkxIkiuLhwTMaCz', '2193', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:branch:sync', '#', 'zksr', '2025-01-20 15:21:37', '', NULL, '', 'partner,dc');

-- 搜索推荐
CREATE TABLE `zksr-member`.`mem_search_his` (
                                  `search_his_id` bigint NOT NULL COMMENT '搜索历史id',
                                  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
                                  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                  `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                  `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                  `member_id` bigint DEFAULT NULL COMMENT '门店用户id',
                                  `branch_id` bigint DEFAULT NULL COMMENT '门店id',
                                  `words` varchar(64) DEFAULT NULL COMMENT '搜索词',
                                  PRIMARY KEY (`search_his_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='搜索历史表';

CREATE TABLE `zksr-product`.`prdt_keywords` (
                                 `keywords_id` bigint NOT NULL COMMENT '关键词id',
                                 `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
                                 `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                 `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                 `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                 `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                 `keyword` varchar(64) NOT NULL COMMENT '关键词',
                                 `status` int DEFAULT NULL COMMENT '状态',
                                 PRIMARY KEY (`keywords_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='搜索关键词词库';

ALTER TABLE `zksr-product`.`prdt_spu`
    ADD COLUMN `keywords` varchar(1024) NULL COMMENT '关联关键词' ;

ALTER TABLE `zksr-cloud`.`sys_partner_policy`
    ADD COLUMN `area_id` bigint(20) NULL COMMENT '区域城市ID' ;

INSERT INTO `zksr-cloud`.`sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '推荐类型', 'recommend_type', '0', 'zksr', '2025-01-22 17:15:44', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '订单明细', 'orderDetails', 'recommend_type', NULL, 'default', 'N', '0', 'zksr', '2025-01-22 17:17:54', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '我的', 'my', 'recommend_type', NULL, 'default', 'N', '0', 'zksr', '2025-01-22 17:17:19', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 1, '购物车', 'car', 'recommend_type', NULL, 'default', 'N', '0', 'zksr', '2025-01-22 17:17:04', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '商品详情', 'goodDetails', 'recommend_type', NULL, 'default', 'N', '0', 'zksr', '2025-01-22 17:16:41', '', NULL, NULL);

-- 开放接口 新增 退货确认前取消(售后取消) 接口  增加相应配置
-- MQ： application-rocketmq-system-dev.yml  新增配置
-- 新增 definition:
openapi_after_cancel

-- 新增 bindings:
#对外接口:退货确认前取消(售后取消)
        openapi_after_cancel-out-0:
          destination: openapi_after_cancel
        openapi_after_cancel-in-0:
          destination: openapi_after_cancel
          group: openapi_after_cancel_group

-- 增加开放配置SQL
INSERT INTO `zksr_cloud`.`sys_openability`(`openability_id`, `create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES (487846016408066666, 'zksr', '2024-06-03 15:31:42.308', 'zksr', '2024-06-09 09:13:42.850', 0, 'supplier', '退货确认前取消(售后取消)', 'afterCancel', '0', 10);

-- 增加数据字典
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '接受批量补货', '13', 'log_request_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-10 16:15:36', 'zksr', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '获取批量补货结果', '14', 'log_request_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-10 16:15:52', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '退货确认前取消', '15', 'log_request_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-10 16:16:16', '', NULL, NULL);
-- 2025.1.24 入驻商订单优惠表新增字段
ALTER TABLE `zksr_trade`.`trd_order_discount_dtl`
    ADD COLUMN `discount_name` VARCHAR(64) DEFAULT NULL COMMENT '订单优惠名称' AFTER `discount_id`;
;
-- 2025.1.24 更新订单明细优惠数据字段
ALTER TABLE `zksr_trade`.`trd_order_discount_dtl`
    MODIFY COLUMN `activity_discount_amt` DECIMAL(12,2) DEFAULT 0 COMMENT '活动优惠金额(分摊的)(new)',
    MODIFY COLUMN `coupon_discount_amt` DECIMAL(12,2) DEFAULT 0 COMMENT '优惠金额(分摊的)(new)',
    MODIFY COLUMN `coupon_discount_amt2` DECIMAL(12,2) DEFAULT 0 COMMENT '优惠金额(不分摊的)(new)'
;
-- 修复订单明细优惠数据
UPDATE  `zksr_trade`.`trd_order_discount_dtl` SET  activity_discount_amt = 0 WHERE activity_discount_amt IS NULL;
UPDATE  `zksr_trade`.`trd_order_discount_dtl` SET  coupon_discount_amt = 0 WHERE coupon_discount_amt IS NULL;
UPDATE  `zksr_trade`.`trd_order_discount_dtl` SET  coupon_discount_amt2 = 0 WHERE coupon_discount_amt2 IS NULL;
-- 新人专区新增字典
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '新人首单', '3', 'activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2025-02-08 16:04:03', '', NULL, NULL);
ALTER TABLE `zksr-cloud`.`sys_partner_policy` MODIFY COLUMN policy_key varchar(255);
-- 2024.5.16 入驻商售后订单明细表新增字段
ALTER TABLE `zksr_trade`.`trd_supplier_order_dtl`
    ADD COLUMN `command_id` BIGINT(20) NULL COMMENT '加单指令ID'
;

-- 业务员加单消息通知处理 application-rocketmq-trade-dev.yml
-- bindings 新增
# 订单加单发送消息指令
commandAddOrderEvent-out-0:
    destination: commandAddOrderEvent

-- 业务员加单消息通知处理  application-rocketmq-member-dev.yml
-- definition 新增
commandAddOrderEvent;

-- bindings 新增
# 收到订单加单消息指令
commandAddOrderEvent-in-0:
    destination: commandAddOrderEvent
    group: commandAddOrderEventGroup

-- 新增表
CREATE TABLE `zksr_member`.`mem_command` (
                                             `command_id` bigint(20) NOT NULL COMMENT '指令ID',
                                             `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                             `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                             `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                             `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                             `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                             `command_level` tinyint(1) DEFAULT NULL COMMENT '0-指令锚点，1-普通指令，2-无需执行指令',
                                             `pid` bigint(20) DEFAULT NULL COMMENT '指令锚点id，仅普通指令',
                                             `command_date` datetime(3) DEFAULT NULL COMMENT '指令有效期',
                                             `command_type` tinyint(2) DEFAULT NULL COMMENT '指令类型（数据字典）(加单、拜访等)',
                                             `status` tinyint(1) DEFAULT NULL COMMENT '0-作废 1-进行中  2-完成 ',
                                             `pub_merchant_type` varchar(16) DEFAULT NULL COMMENT '指令发布角色 colonel-业务员',
                                             `pub_id` bigint(20) DEFAULT NULL COMMENT '指令发布人id',
                                             `exec_merchant_type` varchar(16) DEFAULT NULL COMMENT '指令执行角色 branch-门店',
                                             `exec_id` bigint(20) DEFAULT NULL COMMENT '指令执行人id',
                                             `exec_res` text COMMENT '执行结果',
                                             `memo` varchar(255) DEFAULT NULL COMMENT '备注',
                                             PRIMARY KEY (`command_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '指令表';


-- 修复订单明细优惠数据 赠品优惠金额问题
UPDATE trd_order_discount_dtl todd
    JOIN (
        SELECT
            todd.supplier_order_dtl_id,
            tsod.activity_discount_amt
        FROM
            trd_order_discount_dtl todd
            LEFT JOIN trd_supplier_order_dtl tsod ON todd.supplier_order_dtl_id = tsod.supplier_order_dtl_id
        WHERE
            todd.discount_type in ('BG', 'FG')
            AND (todd.activity_discount_amt IS NULL OR todd.activity_discount_amt <= 0)
            AND todd.gift_type = 0
            AND todd.supplier_order_dtl_id is NOT null
    ) updateTable ON todd.supplier_order_dtl_id = updateTable.supplier_order_dtl_id
SET
    todd.activity_discount_amt = updateTable.activity_discount_amt
WHERE
    todd.discount_type in ('BG', 'FG')
    AND (todd.activity_discount_amt IS NULL OR todd.activity_discount_amt <= 0)
    AND todd.gift_type = 0
    AND todd.supplier_order_dtl_id is NOT null

-- 修复订单明细优惠数据 优惠名称（促销活动名称）
UPDATE `zksr_trade`.trd_order_discount_dtl todd
    JOIN (
        SELECT activity_id, activity_name FROM `zksr_promotion`.prm_activity
    ) updateTable  ON todd.discount_id = updateTable.activity_id
SET
    todd.discount_name = updateTable.activity_name
WHERE
    todd.discount_type in ('BG', 'FG', 'SK', 'SP', 'FD', 'CB')
    AND updateTable.activity_name is not null

-- 修复订单明细优惠数据 优惠名称（优惠券）
UPDATE `zksr-trade`.trd_order_discount_dtl todd
    JOIN (
        SELECT pc.coupon_id, pct.coupon_name FROM `zksr-promotion`.prm_coupon pc
    LEFT JOIN `zksr-promotion`.prm_coupon_template pct ON pc.coupon_template_id = pct.coupon_template_id
    ) updateTable  ON todd.discount_id = updateTable.coupon_id
SET
    todd.discount_name = updateTable.coupon_name
WHERE
    todd.discount_type in ('COUPON') AND updateTable.coupon_name is not null

ALTER TABLE `zksr-cloud`.`sys_partner_policy` MODIFY COLUMN policy_key varchar(255);
ALTER TABLE `zksr-product`.`prdt_keywords`
    ADD COLUMN `del_flag` char(1) CHARACTER SET utf8mb4 DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）';

DELETE FROM  `zksr-cloud`.`sys_menu` where menu_name ='搜索配置';
DELETE FROM  `zksr-cloud`.`sys_menu` where menu_name ='搜索日志';
DELETE FROM  `zksr-cloud`.`sys_menu` where menu_name ='关键词库';
DELETE FROM  `zksr-cloud`.`sys_menu` where menu_name ='推荐管理';
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '一键加入词库', 2783, 1, 'FvZHcgBBTtcvXucoGe', 'vcIkWQGVegjEdtvaRt', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:searchHis:add', '#', 'zksr', '2025-02-12 16:45:05', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '编辑', 2781, 1, 'SIpWHEQwcFaevOHjOY', 'ZvSyZWIESCrIoyZpyr', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerPolicy:edit', '#', 'zksr', '2025-02-08 11:30:50', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '搜索配置', 2730, 3, 'ZvSyZWIESCrIoyZpyr', 'ZcZcfROKeQBpLJYidO', 'searchConfiguration', 'platform/searchConfiguration/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2025-01-18 15:07:36', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '搜索日志', 2784, 2, 'lbqefuasEpRaYOPZji', 'ZcZcfROKeQBpLJYidO', 'searchLogings', 'platform/searchLogings/index', NULL, 1, 0, 'C', '0', '0', 'member:searchHis:list', '#', 'zksr', '2025-01-18 15:06:40', 'zksr', '2025-02-12 16:39:58', '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '关键词库', 2784, 1, 'vcIkWQGVegjEdtvaRt', 'ZcZcfROKeQBpLJYidO', 'keywordLibrary', 'platform/keywordLibrary/index', NULL, 1, 0, 'C', '0', '0', 'product:keywords:list', '#', 'zksr', '2025-01-18 15:05:10', 'zksr', '2025-02-13 15:30:52', '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '推荐管理', 0, 22, 'ZcZcfROKeQBpLJYidO', '0', 'recommend', NULL, NULL, 1, 0, 'M', '0', '0', NULL, '#', 'zksr', '2025-01-18 15:03:51', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '删除', 2781, 2, 'CuSNlDoOFjZPTEywnh', 'ZvSyZWIESCrIoyZpyr', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerPolicy:remove', '#', 'zksr', '2025-02-13 16:07:24', '', NULL, '', 'partner');


-- 新增OPENAPI 售后状态接口 相关配置
ALTER TABLE `zksr_trade`.`trd_express_status`
    MODIFY COLUMN `logistics_status` int(8)  DEFAULT null COMMENT '订单物流状态（1待出库、2待配送、3配送中、4已配送（暂弃用）、5已收货） 售后物流状态（1、门店发起售后  2、商家已同意 11、待确认收货 12、已确认收货 13、已入库 21、商家同意退款 22、退款成功））',
    ADD COLUMN `logistics_status_info` VARCHAR(64) NULL COMMENT '物流状态信息',
    ADD COLUMN `source_order_no` VARCHAR(64) NULL COMMENT '外部（ERP）单号',
    ADD COLUMN `start_time` datetime(3) DEFAULT NULL COMMENT '开始时间';

INSERT INTO `zksr_cloud`.`sys_openability`(`openability_id`, `create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES (585073686581477376, 'zksr', '2025-02-20 15:44:43.970', NULL, NULL, NULL, 'supplier', '售后状态', 'afterLog', '0', 10);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '售后状态', '16', 'log_request_type', '', 'default', 'N', '0', 'zksr', '2025-02-20 15:46:26', 'zksr', '2025-02-20 15:47:11', NULL);

-- application-rocketmq-system-dev.yml 新增配置
-- definition新增
openapi_afterlog;
-- bindings 新增
#对外接口:售后状态
        openapi_afterlog-out-0:
          destination: openapi_afterlog
        openapi_afterlog-in-0:
          destination: openapi_afterlog
          group: openapi_afterlog_group

-- 入驻商订单明细添加字段
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
    ADD COLUMN `original_return_qty` INT(8) NULL COMMENT '原申请退货最小单位数量' AFTER `return_qty`,
    ADD COLUMN `original_return_amt` DECIMAL(12,2) NULL COMMENT '原申请退货金额' AFTER `original_return_qty`
;
-- 初始化原申请退货最小单位数量 & 金额
UPDATE `zksr-trade`.`trd_supplier_after_dtl` SET original_return_qty = return_qty;
UPDATE `zksr-trade`.`trd_supplier_after_dtl` SET original_return_amt = return_amt;

-- 满赠，满减活动新增每天一次类型（限定全场类型）
INSERT INTO `zksr-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '每天一次', '0', 'fg_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2025-02-20 10:44:28', '', NULL, NULL);

-- 秒杀活动新增状态和排序
ALTER TABLE `zksr-promotion`.`prm_sk_rule`
ADD COLUMN sk_status tinyint DEFAULT 1 COMMENT '状态：0-停用，1-启用',
ADD COLUMN sort_order int DEFAULT NULL COMMENT '排序字段';



-- 新增特价限购类型字典
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('特价限购类型', 'sp_activity_times_rule', '0', 'zksr', '2024-11-25 09:49:14', 'zksr', '2024-11-25 09:50:09', '特价限购类型');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '每日一次', '0', 'sp_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:40', '', NULL, '每天重置门店活动限制');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '活动期间内仅一次', '1', 'sp_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '活动期间内只能参与一次');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '新人首单', '3', 'sp_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '只要下过单就不算了, 取消也不重置机会, 和系统首单同义');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品级别每日限购', '4', 'sp_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '即商品A每个门店每日购买上限为5件，商品A全平台每日购买上限为100件；商品B同理');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商品级别仅一次', '5', 'sp_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '即商品A活动期间内只能下单一次，门店单次购买上限为5件，商品A全平台活动期间内限量100件；商品B同理');

-- 微信小程序参数
CREATE TABLE `wx_qr_data` (
                              `qr_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                              `qr_key` varchar(32) DEFAULT NULL COMMENT '二维码key',
                              `qr_value` text COMMENT '二维码value',
                              `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id;平台商id',
                              `create_by` varchar(64) DEFAULT NULL COMMENT '创建人;创建人',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间;创建时间',
                              `update_by` varchar(64) DEFAULT NULL COMMENT '更新人;修改人',
                              `update_time` datetime DEFAULT NULL COMMENT '更新时间;修改时间',
                              PRIMARY KEY (`qr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信小程序二维码参数';
-- 新增、修改  同步日志功能权限信息
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '同步日志查询', 2454, 0, 'cmdjsrcTOjSRSFyPvi', '2454', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:log:query', '#', 'zksr', '2025-02-24 16:20:39', '', NULL, '', 'partner');
UPDATE `zksr_cloud`.`sys_menu` SET `perms` = 'system:log:list',`func_scop` = 'partner' WHERE `menu_code` = '2454';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner' WHERE `menu_code` = '2';

-- 批次发券门店导入权限
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '批次发券门店导入', 2675, 6, 'zIgKsvUhHQIbRaJQKP', 'GBRtouCgJcuWuNBHxd', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:importBranch', '#', 'zksr', '2025-02-27 15:54:53', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '批次发券门店导入', 2685, 6, 'SgMBVpAMiyydCavQEK', 'XMXoTrNcVajRaraFFb', '', NULL, NULL, 1, 0, 'F', '0', '0', 'couponBatch:couponBatch:importBranch', '#', 'zksr', '2025-02-26 16:42:42', '', NULL, '', 'dc');





-- 新增活动备注说明
ALTER TABLE `zksr-promotion`.`prm_activity`
    ADD COLUMN `memo` TEXT CHARACTER SET utf8mb4  DEFAULT NULL COMMENT '活动说明';

ALTER TABLE `zksr-promotion`.`prm_coupon_template`
    ADD COLUMN `memo` TEXT CHARACTER SET utf8mb4  DEFAULT NULL COMMENT '优惠券模板说明';



-- 门店充值方案
CREATE TABLE `zksr-account`.`acc_recharge_scheme` (
                                                      `recharge_scheme_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '充值方案id',
                                                      `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                                      `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                      `area_ids` text COMMENT '适用区域城市',
                                                      `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
                                                      `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
                                                      `rule_json` text COMMENT '充值金额，赠送金额',
                                                      `status` tinyint(4) DEFAULT '0' COMMENT '0-正常, 1-停用',
                                                      `scheme_name` varchar(32) DEFAULT NULL COMMENT '方案名称',
                                                      PRIMARY KEY (`recharge_scheme_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='储值套餐';
CREATE TABLE `zksr-account`.`acc_recharge_scheme_area` (
                                                           `recharge_scheme_area_id` bigint(20) NOT NULL COMMENT '主键',
                                                           `recharge_scheme_id` bigint(20) NOT NULL COMMENT '充值方案id',
                                                           `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                                           `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                           `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                           `area_id` bigint(20) DEFAULT NULL COMMENT '发布城市ID',
                                                           PRIMARY KEY (`recharge_scheme_area_id`),
                                                           KEY `idx_area_id` (`area_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='储值套餐上架发布城市';
ALTER TABLE `zksr-account`.`acc_account`
    MODIFY COLUMN `account_type` int(1) NULL DEFAULT NULL COMMENT '账户类型;0-储值账户 1-赠送余额' AFTER `update_time`;
ALTER TABLE `zksr-account`.`acc_account_flow` ADD COLUMN `account_type` int(1) NULL DEFAULT 0 COMMENT '账户类型;0-储值账户 1-赠送余额' AFTER `platform`;
ALTER TABLE `zksr-account`.`acc_account` MODIFY COLUMN `account_type` int(1) NULL DEFAULT 0 COMMENT '账户类型;0-储值账户 1-赠送余额' AFTER `update_time`;

-- 门店充值方案菜单权限
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('运营商其他配置', 2082, 7, 'smKWOoWMUTXsSlRmwv', '2082', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerPolicy:query-dc', '#', 'zksr', '2025-02-12 14:44:07', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('启用', 2786, 6, 'nssPcEiXBYfjDDftGg', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:enable', '#', 'zksr', '2025-02-11 10:35:50', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('停用', 2786, 5, 'uSypgjHQlRbMyhxYOP', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:disable', '#', 'zksr', '2025-02-11 10:35:39', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('详情', 2786, 4, 'raHYbETKiqEuLsRxtz', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:query', '#', 'zksr', '2025-02-11 10:35:23', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('列表', 2786, 3, 'sTKsVOWVgAcHZhvxRJ', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:list', '#', 'zksr', '2025-02-11 10:34:59', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('编辑', 2786, 2, 'cJsedlZpXOXBVKRwju', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:edit', '#', 'zksr', '2025-02-11 10:34:44', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('添加', 2786, 1, 'TgCGZTAaSstSAGmgFG', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:add', '#', 'zksr', '2025-02-11 10:34:26', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店储值套餐', 2017, 6, 'sKoiWoAYxhxAcvhbZs', '2017', 'storedValue', 'operation/storedValue/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2025-02-11 10:34:04', 'zksr', '2025-02-12 16:03:53', '', 'partner,software,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('客户储值流水', 2658, 10, 'NQSyXkUqsslVwXRGPV', 'ZfGDdvXtdyqxghIocd', 'storeValueCustomer', 'operation/storeValueCustomer/index', NULL, 1, 0, 'C', '0', '0', 'account:flow:list', '#', 'zksr', '2025-02-22 17:45:47', '', NULL, '', 'partner,dc,supplier');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店充值', 2031, 10, 'qtnsnFVRVVDaPWZFkB', '2031', 'storeValueRecharge', 'operation/storeValueRecharge/index', NULL, 1, 0, 'C', '0', '0', 'account:recharge:list', '#', 'zksr', '2025-02-22 17:40:49', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('提现单详情', 2822, 3, 'FPkhwXVIrToHZrJKbZ', 'kTKWwmwemviGPWDxJT', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-branch:query', '#', 'zksr', '2025-02-24 16:05:10', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('审核拒绝', 2822, 2, 'eTqAoZxhPgAuCDspjv', 'kTKWwmwemviGPWDxJT', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-branch:disable', '#', 'zksr', '2025-02-24 16:04:53', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('审核通过', 2822, 1, 'SdfRwuuSkeVDpxRGUE', 'kTKWwmwemviGPWDxJT', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-branch:enable', '#', 'zksr', '2025-02-24 16:04:33', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店提现申请', 2031, 11, 'kTKWwmwemviGPWDxJT', '2031', 'storeValuewithdrawal', 'operation/storeValuewithdrawal/index', NULL, 1, 0, 'C', '0', '0', 'account:withdraw-branch:list', '#', 'zksr', '2025-02-24 15:09:56', 'zksr', '2025-02-24 15:20:51', '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('直接结算', 2263, 7, 'TeOtTMcStsynbZvMhN', '2263', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-dc:direct-settle', '#', 'zksr', '2025-02-27 19:00:01', '', NULL, '', 'dc,partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('商户可结算', 2263, 6, 'DfqdkZirKOEKCzgasC', '2263', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:account:query-dc-settle-list', '#', 'zksr', '2025-02-27 18:59:25', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('新增提现', 2265, 0, 'JxnhcejVYeNgckDfHR', '2265', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-branch:add', '#', 'zksr', '2025-03-10 09:03:13', '', NULL, '', 'dc');


-- 账户充值记录增加赠送方案
ALTER TABLE `zksr-account`.`acc_recharge`
    ADD COLUMN `recharge_scheme_id` bigint(20) NULL COMMENT '充值赠送方案ID' AFTER `fee`,
    ADD COLUMN `scheme_rule_json` varchar(1024) NULL COMMENT '充值赠送方案JSON' AFTER `recharge_scheme_id`,
    ADD COLUMN `give_amt` decimal(10, 2) NULL COMMENT '赠送金额' AFTER `scheme_rule_json`;

-- 账户流水增加业务单号冗余
ALTER TABLE `zksr_account`.`acc_account_flow`
    ADD COLUMN `account_type` int(1) NULL COMMENT '账户类型;0-储值账户 1-赠送余额' AFTER `platform`;
ALTER TABLE `zksr-account`.`acc_account_flow`
    ADD COLUMN `memo` varchar(64) NULL COMMENT '流水变动备注' AFTER `account_type`,
    ADD COLUMN `busi_no` varchar(64) NULL COMMENT '流水变动业务单号' AFTER `memo`;

-- 账户流水索引优化
ALTER TABLE `zksr-account`.`acc_account_flow`
    ADD INDEX `idx_merchant_id`(`merchant_id`);

-- 字典维护
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('账户流水变动类型', 'account_flow_busi_type', '0', 'zksr', '2025-02-15 09:04:28', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('钱包充值支付平台', 'wallet_pay_platform', '0', 'zksr', '2025-02-11 15:29:49', '', NULL, '钱包充值支付平台');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店下单货到付款', 'branch_hdfk', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店下单货到付款下单', 'branch_hdfk_order', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '充值手续费补贴', 'recharge_fee', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '提现成功', 'withdraw_success', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '提现失败解除冻结', 'withdraw_fail_frozen', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '提现冻结', 'withdraw_frozen', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '修改授信金额', 'update_credit', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '账户转账', 'account_transfer', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店储值支付退款', 'branch_balance_refund', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店储值支付', 'branch_balance_pay', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商城创建订单解冻入驻商', 'supplier_create_order_cancel', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商城创建订单冻结入驻商', 'supplier_create_order', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店充值赠送', 'branch_recharge_give', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店充值', 'branch_recharge', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '入驻商充值', 'supplier_recharge', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '合利宝', 'hlb', 'wallet_pay_platform', NULL, 'default', 'N', '0', 'zksr', '2025-02-11 15:30:00', '', NULL, NULL);

-- 提现凭证
ALTER TABLE `zksr-account`.`acc_withdraw`
    ADD COLUMN `voucher` varchar(255) NULL COMMENT '提现凭证' AFTER `transfer_amt`;
ALTER TABLE `zksr-account`.`acc_withdraw`
    ADD COLUMN `apply_tip` varchar(64) NULL COMMENT '申请备注' AFTER `voucher`;

ALTER TABLE `zksr-account`.`acc_account_flow`
    ADD INDEX `idx_busi_id`(`busi_id`);

-- 支付请求信息
ALTER TABLE `zksr-account`.`acc_pay_flow` ADD COLUMN `req_info` varchar(512) NULL;



-- 展示类目配置生产日期格式
ALTER TABLE `zksr-product`.`prdt_area_class`
    ADD COLUMN `show_produce_date` tinyint(2) NULL DEFAULT 1 COMMENT '是否展示生产日期 0-关闭, 1-开启' AFTER `del_flag`,
    ADD COLUMN `produce_date_format` varchar(12) NULL DEFAULT 'yy/MM/dd' COMMENT '生产日期格式 yy/MM/dd 年月日 yy/MM 年月' AFTER `show_produce_date`;

ALTER TABLE `zksr-product`.`prdt_sale_class`
    ADD COLUMN `show_produce_date` tinyint(2) NULL DEFAULT 1 COMMENT '是否展示生产日期 0-关闭, 1-开启' AFTER `del_flag`,
    ADD COLUMN `produce_date_format` varchar(12) NULL DEFAULT 'yy/MM/dd' COMMENT '生产日期格式 yy/MM/dd 年月日 yy/MM 年月' AFTER `show_produce_date`;


-- 新增索引 2025.02.28
ALTER TABLE `zksr_trade`.`trd_supplier_order_settle`
    ADD INDEX `idx_supplier_order_dtl_id`(`supplier_order_dtl_id`);

ALTER TABLE `zksr_trade`.`trd_supplier_order_dtl`
    ADD INDEX `idx_supplier_order_dtl_id`(`supplier_order_dtl_id`);

ALTER TABLE `zksr_trade`.`trd_supplier_after_dtl`
    ADD INDEX `idx_supplier_after_dtl_id`(`supplier_after_dtl_id`);

ALTER TABLE `zksr_trade`.`trd_supplier_after_settle`
    ADD INDEX `idx_supplier_after_dtl_id`(`supplier_after_dtl_id`);

-- 门店表增加省市区
ALTER TABLE `zksr_member`.mem_branch
add `province_name` varchar(64) DEFAULT null COMMENT '省份';
ALTER TABLE `zksr_member`.mem_branch
add `city_name` varchar(64) DEFAULT null COMMENT '城市';
ALTER TABLE `zksr_member`.mem_branch
add `district_name` varchar(64) DEFAULT null COMMENT '区县';

-- 用户门店注册表新增渠道字段
ALTER TABLE `zksr-member`.`mem_member_register`
    ADD COLUMN `channel_id`  bigint(20) DEFAULT NULL COMMENT '渠道id'
;

-- nacos 配置
-- zksr-gateway-dev.yml 增加白名单接口
- /portal/mall/index/getChannelInfo

-- 【开发接口优化】针对推送失败的单据 通过发邮件的形式，告知给客户和实施人员 新增SQL
ALTER TABLE `zksr_cloud`.`sys_opensource`
ADD COLUMN alarm_email VARCHAR(64) DEFAULT null COMMENT '告警邮箱  多个邮箱用逗号隔开',
ADD COLUMN subscribe_send_email VARCHAR(64) DEFAULT NULL COMMENT '接口发送邮件订阅 选择需要发送的接口 用逗号隔开';

ALTER TABLE `zksr_cloud`.`sys_interface_log`
ADD COLUMN partner_id bigint(20) DEFAULT NULL COMMENT '平台商开放能力ID';

-- 【开发接口优化】针对推送失败的单据 通过发邮件的形式，告知给客户和实施人员 日同步报告定时任务 新增数据字典SQL
INSERT INTO `zksr_cloud`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('Email模板类型', 'email_template_type', '0', 'zksr', '2025-02-13 16:54:24', '', NULL, 'Email模板类型');
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '日同步报告', '900', 'email_template_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-13 16:54:53', 'zksr', '2025-03-03 11:39:16', NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '推送B2B平台门店数据', '501', 'email_template_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-13 16:54:59', 'zksr', '2025-03-03 11:39:30', NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '推送B2B销售订单数据', '502', 'email_template_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-03 11:39:40', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '推送B2B售后订单数据', '503', 'email_template_type', '', 'default', 'N', '0', 'zksr', '2025-03-03 11:39:48', 'zksr', '2025-03-03 11:40:10', NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '推送B2B收款单数据', '504', 'email_template_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-03 11:39:57', '', NULL, NULL);


-- 【开发接口优化】针对推送失败的单据 通过发邮件的形式，告知给客户和实施人员 日同步报告定时任务 新增SQL
--  未开启 请手动开始该定时任务
INSERT INTO `xxl_job`.`xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (2, '每天同步第三方数据情况汇总统计(日同步报告) - 按对接第三方入驻商发送邮件', '2025-03-01 16:14:27', '2025-03-01 16:14:27', '蒋剑超', '', 'CRON', '0 00 3 * * ?', 'DO_NOTHING', 'FIRST', 'openApiDaySyncReportDataEmailJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-03-01 16:14:27', '', 0, 0, 0);

-- 【开发接口优化】针对推送失败的单据 通过发邮件的形式，告知给客户和实施人员 新增Nacos配置
-- zksr-system-dev.yml
-- 配置在 spring: 下面
# 设置邮箱通知
  mail:
    # 设置邮箱主机 网易: smtp.163.com  QQ邮箱:smtp.qq.com
    host: smtp.qq.com
    # 设置用户名
    username: <EMAIL>
    # 设置密码 注这个不是真实的密码，是在邮箱设置里面生成的授权码
    password: kokeoojocahybfja
    properties:
      mail:
        smtp:
          # 设置是否需要认证，如果为true,那么用户名和密码就必须的
          #如果设置false，可以不设置用户名和密码，当然也得看你的对接的平台是否支持无密码进行访问的。
          auth: true
          starttls:
            # STARTTLS[1]  是对纯文本通信协议的扩展。它提供一种方式将纯文本连接升级为加密连接（TLS或SSL），而不是另外使用一个端口作加密通信。
            enable: true
            required: true

-- 【小象】系统首单的活动，门店参与资格逻辑优化 门店新增 首单单号
ALTER TABLE `zksr_member`.`mem_branch`
    ADD COLUMN `first_order_no` VARCHAR(25) DEFAULT  NULL COMMENT '首单单号' AFTER `first_order_flag`
;


ALTER TABLE `zksr-product`.`prdt_spu`
    ADD COLUMN `pricing_way` TINYINT(1) DEFAULT 1 COMMENT '商品计价方式类型：1-普通商品，2-称重商品';

-- 新增商品计价方式类型字典
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('商品计价方式类型', 'spu_pricing_way', '0', 'zksr', '2025-03-01 09:49:14', 'zksr', '2025-03-01 09:50:09', '商品计价方式类型');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '普通商品', '1', 'spu_pricing_way', NULL, 'default', 'N', '0', 'zksr', '2025-03-01 09:49:40', '', NULL, '商品新增默认普通商品');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '称重商品', '2', 'spu_pricing_way', NULL, 'default', 'N', '0', 'zksr', '2025-03-01 09:49:33', '', NULL, '称重商品默认单位为（千克）且只会有一个最小单位');

-- 新增商品单位类型字典 - KG 用于称重商品
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '千克（KG）', '999', 'sys_prdt_unit', NULL, 'default', 'N', '0', 'zksr', '2025-03-01 09:49:33', '', NULL, '称重商品单独使用且只能是称重商品使用（不要私自调整，会导致商品异常）');

-- 更新入驻商售后订单明细表字段 类型为小数
ALTER TABLE `zksr-trade`.`trd_supplier_after_dtl`
    MODIFY COLUMN original_return_qty DECIMAL(12,4) DEFAULT 0 COMMENT '原申请退货最小单位数量',
    MODIFY COLUMN return_qty DECIMAL(12,4) DEFAULT 0 COMMENT '退货数量',
    MODIFY COLUMN return_unit_qty DECIMAL(12,4) DEFAULT 0 COMMENT '售后单位数量',
    MODIFY COLUMN return_unit_size DECIMAL(12,4) DEFAULT 0 COMMENT '售后单位换算数量',
    MODIFY COLUMN order_unit_qty DECIMAL(12,4) DEFAULT 0 COMMENT '订单购买单位数量'
    ;
-- 更新入驻商售后订单明细结算表字段 类型为小数
ALTER TABLE `zksr-trade`.`trd_supplier_after_settle`
    MODIFY COLUMN return_qty DECIMAL(12,4) DEFAULT 0 COMMENT '退货数量'
;
-- 更新入驻商售后订单明细优惠表字段 类型为小数
ALTER TABLE `zksr-trade`.`trd_after_discount_dtl`
    MODIFY COLUMN gift_qty DECIMAL(12,4) DEFAULT 0 COMMENT '赠品退货数量'
;
-- 更新入驻商订单明细表字段 类型为小数
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    MODIFY COLUMN cancel_qty DECIMAL(12,4) DEFAULT 0 COMMENT '发货前取消数量',
    MODIFY COLUMN send_unit_qty DECIMAL(12,4) DEFAULT 0 COMMENT '发货单位数量',
    MODIFY COLUMN send_qty DECIMAL(12,4) DEFAULT 0 COMMENT '发货数量',
    MODIFY COLUMN reject_unit_qty DECIMAL(12,4) DEFAULT 0 COMMENT '拒收单位数量',
    MODIFY COLUMN reject_qty DECIMAL(12,4) DEFAULT 0 COMMENT '拒收数量',
    MODIFY COLUMN total_num DECIMAL(12,4) DEFAULT 0 COMMENT '商品最小单位数量',
    MODIFY COLUMN order_unit_size DECIMAL(12,4) DEFAULT 0 COMMENT '订单购买单位换算数量',
    MODIFY COLUMN cancel_unit_size DECIMAL(12,4) DEFAULT 0 COMMENT '发货前取消单位换算数量',
    MODIFY COLUMN reject_unit_size DECIMAL(12,4) DEFAULT 0 COMMENT '拒收单位换算数量'
;
-- 更新入驻商订单表字段 类型为小数
ALTER TABLE `zksr-trade`.`trd_supplier_order`
    MODIFY COLUMN sub_cancel_qty DECIMAL(12,4) DEFAULT 0 COMMENT '发货前订单取消数量',
;

-- 更新商品SKU表字段 类型为小数
ALTER TABLE `zksr-product`.`prdt_sku`
    MODIFY COLUMN synced_qty DECIMAL(12,4) DEFAULT 0 COMMENT '已同步库存',
    MODIFY COLUMN sale_qty DECIMAL(12,4) DEFAULT 0 COMMENT '已售数量, 库存 - 已售 = 剩余',
    MODIFY COLUMN stock DECIMAL(12,4) DEFAULT 0 COMMENT '库存数量'
;

ALTER TABLE `zksr-product`.`prdt_spu`
    MODIFY COLUMN mid_size DECIMAL(12,4) DEFAULT 0 COMMENT '中单位换算数量（换算成最小单位）',
    MODIFY COLUMN large_size DECIMAL(12,4) DEFAULT 0 COMMENT '大单位换算数量（换算成最小单位）'
;

ALTER TABLE `zksr-trade`.`trd_supplier_order_settle`
    MODIFY COLUMN item_qty DECIMAL(12,4) DEFAULT 0 COMMENT '商品最小单位数量'
;

-- 城市价格码唯一值调整
ALTER TABLE `zksr-product`.`prdt_area_channel_price`
    ADD UNIQUE INDEX `unique_key`(`area_id`, `channel_id`, `sale_price_code`) USING BTREE;

ALTER TABLE `zksr-product`.`prdt_supplier_group_price`
    ADD UNIQUE INDEX `unique_key`(`group_id`, `area_id`, `sale_price_code`) USING BTREE;


-- 新增系统时间类型字典
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('系统时间类型', 'sys_time_type', '0', 'zksr', '2024-11-25 09:49:14', 'zksr', '2024-11-25 09:50:09', '系统时间类型');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (0, '分钟', '0', 'sys_time_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:40', '', NULL, '分钟');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (0, '小时', '1', 'sys_time_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '小时');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (0, '天', '2', 'sys_time_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '天');

-- 新增系统状态类型字典
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('系统状态类型', 'sys_status_type', '0', 'zksr', '2024-11-25 09:49:14', 'zksr', '2024-11-25 09:50:09', '系统状态类型');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (0, '否', '0', 'sys_status_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:40', '', NULL, '否');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (0, '是', '1', 'sys_status_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '是');

-- 入驻商管理分类关系新增可售后时间等字段
ALTER TABLE `zksr-product`.`prdt_supplier_class`
    ADD COLUMN is_after_sales TINYINT(2) DEFAULT 1 COMMENT '是否可售后，默认为是（1：是，0：否）',
	ADD COLUMN after_sales_time_type TINYINT(2) DEFAULT NULL COMMENT '售后时间类型：字典：sys_time_type',
	ADD COLUMN after_sales_time INT(4) DEFAULT NULL COMMENT '可售后时间'
;

-- 入驻商管理分类关系新增可售后时间等字段
ALTER TABLE `zksr-trade`.`trd_supplier_order_dtl`
    ADD COLUMN is_after_sales TINYINT(2) DEFAULT 1 COMMENT '是否可售后，默认为是（1：是，0：否）',
	ADD COLUMN after_sales_time INT(8) DEFAULT 10080 COMMENT '可售后时间（存储类型为分钟）默认为为7天（10080分钟）'
;

-- 新增菜单【入驻商管理分类】
INSERT INTO `zksr-cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`)
    VALUES ('入驻商管理分类', 2017, 25, 'xfsMmrOQeUOLHBjehA', '2017', 'suppliedCategory', 'operation/suppliedCategory/index', NULL, 1, 0, 'C', '0', '0', 'product:supplierClass:list', '#', 'zksr', '2025-03-12 08:49:02', 'zksr', '2025-03-12 10:27:52', '', 'supplier');
INSERT INTO `zksr-cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`)
    VALUES ('详情', 2851, 1, 'CHNmlvlzAWPoaVbIAG', 'xfsMmrOQeUOLHBjehA', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:supplierClass:query', '#', 'zksr', '2025-03-12 10:39:55', '', NULL, '', 'supplier');
INSERT INTO `zksr-cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`)
    VALUES ('修改', 2851, 2, 'KTccNLIhvIwFGLZHcF', 'xfsMmrOQeUOLHBjehA', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:supplierClass:edit', '#', 'zksr', '2025-03-12 10:40:29', '', NULL, '', 'supplier');

-- 修复订单售后时间
update `zksr-trade`.`trd_supplier_order_dtl` set after_sales_time = 10080 where after_sales_time = 0



-- 后台/业务员APP：新增门店生命周期配置 新增表
-- 门店表新增生命周期字段
ALTER TABLE `zksr_member`.`mem_branch`
    ADD COLUMN `lifecycle_stage` tinyint(2) NULL COMMENT '生命周期阶段' AFTER `district_name`;

-- 门店生命周期拉链表
CREATE TABLE `zksr_member`.`mem_branch_lifecycle_zip` (
                                            `branch_lifecycle_zip_id` bigint(20) NOT NULL COMMENT '门店生命周期拉链表ID',
                                            `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                            `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                            `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                            `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                            `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                            `branch_id` bigint(20) NOT NULL COMMENT '门店id',
                                            `last_lifecycle_stage` tinyint(2) DEFAULT NULL COMMENT '上一次生命周期code',
                                            `lifecycle_stage` tinyint(2) NOT NULL COMMENT '生命周期code',
                                            `start_date` datetime NOT NULL COMMENT '开始日期',
                                            `end_date` datetime NOT NULL COMMENT '结束日期',
                                            `start_memo` varchar(255) DEFAULT NULL COMMENT '触发事件备注',
                                            PRIMARY KEY (`branch_lifecycle_zip_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店生命周期拉链表';


-- 后台/业务员APP：新增门店生命周期配置 新增数据字典信息
INSERT INTO `zksr_cloud`.`sys_dict_type`( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('门店生命周期', 'branch_lifecycle_stage_type', '0', 'zksr', '2025-03-14 15:04:28', 'zksr', '2025-03-14 15:06:17', '门店生命周期');

INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '新店', '1', 'branch_lifecycle_stage_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-14 15:06:49', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '活跃', '2', 'branch_lifecycle_stage_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-14 15:06:56', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '沉默', '3', 'branch_lifecycle_stage_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-14 15:07:02', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '流失', '4', 'branch_lifecycle_stage_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-14 15:07:10', '', NULL, NULL);

-- 后台/业务员APP：新增门店生命周期配置 新增菜单权限
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店生命周期配置', 2021, 12, 'UexvrBvxFwqgXMhBpt', '2021', 'storeCycle', 'operation/storeCycle/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2025-03-14 09:57:23', 'zksr', '2025-03-18 15:01:50', '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('客户状态记录', 2192, 5, 'XEArFqwejzulrJZEqT', '2192', 'shopInformation/customerStatusRecord', 'operation/shopInformation/customerStatusRecord/index', NULL, 1, 0, 'C', '0', '0', 'member:branchLifeCycleZip:list', '#', 'zksr', '2025-03-14 14:19:06', 'zksr', '2025-03-14 14:54:29', '', 'dc');
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,dc' WHERE `menu_code` = '2021';

-- 后台/业务员APP：新增门店生命周期配置 新增定时任务  需手动开启
INSERT INTO `xxl_job`.`xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES ( 2, '刷新门店生命周期', '2025-03-14 09:40:26', '2025-03-14 09:40:26', '蒋剑超', '', 'CRON', '0 00 4 * * ?', 'DO_NOTHING', 'FIRST', 'refreshBranchLifecycleZipHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-03-14 09:40:26', '', 0, 0, 0);








-- 门店充值方案
CREATE TABLE `zksr-account`.`acc_recharge_scheme` (
                                                      `recharge_scheme_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '充值方案id',
                                                      `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                                      `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                      `area_ids` text COMMENT '适用区域城市',
                                                      `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
                                                      `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
                                                      `rule_json` text COMMENT '充值金额，赠送金额',
                                                      `status` tinyint(4) DEFAULT '0' COMMENT '0-正常, 1-停用',
                                                      `scheme_name` varchar(32) DEFAULT NULL COMMENT '方案名称',
                                                      PRIMARY KEY (`recharge_scheme_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='储值套餐';
CREATE TABLE `zksr-account`.`acc_recharge_scheme_area` (
                                                           `recharge_scheme_area_id` bigint(20) NOT NULL COMMENT '主键',
                                                           `recharge_scheme_id` bigint(20) NOT NULL COMMENT '充值方案id',
                                                           `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                                           `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                           `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                           `area_id` bigint(20) DEFAULT NULL COMMENT '发布城市ID',
                                                           PRIMARY KEY (`recharge_scheme_area_id`),
                                                           KEY `idx_area_id` (`area_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='储值套餐上架发布城市';
ALTER TABLE `zksr-account`.`acc_account`
    MODIFY COLUMN `account_type` int(1) NULL DEFAULT NULL COMMENT '账户类型;0-储值账户 1-赠送余额' AFTER `update_time`;
ALTER TABLE `zksr-account`.`acc_account_flow` ADD COLUMN `account_type` int(1) NULL DEFAULT 0 COMMENT '账户类型;0-储值账户 1-赠送余额' AFTER `platform`;
ALTER TABLE `zksr-account`.`acc_account` MODIFY COLUMN `account_type` int(1) NULL DEFAULT 0 COMMENT '账户类型;0-储值账户 1-赠送余额' AFTER `update_time`;

-- 门店充值方案菜单权限
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('运营商其他配置', 2082, 7, 'smKWOoWMUTXsSlRmwv', '2082', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerPolicy:query-dc', '#', 'zksr', '2025-02-12 14:44:07', '', NULL, '', 'partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('启用', 2786, 6, 'nssPcEiXBYfjDDftGg', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:enable', '#', 'zksr', '2025-02-11 10:35:50', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('停用', 2786, 5, 'uSypgjHQlRbMyhxYOP', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:disable', '#', 'zksr', '2025-02-11 10:35:39', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('详情', 2786, 4, 'raHYbETKiqEuLsRxtz', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:query', '#', 'zksr', '2025-02-11 10:35:23', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('列表', 2786, 3, 'sTKsVOWVgAcHZhvxRJ', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:list', '#', 'zksr', '2025-02-11 10:34:59', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('编辑', 2786, 2, 'cJsedlZpXOXBVKRwju', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:edit', '#', 'zksr', '2025-02-11 10:34:44', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('添加', 2786, 1, 'TgCGZTAaSstSAGmgFG', 'sKoiWoAYxhxAcvhbZs', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:rechargeScheme:add', '#', 'zksr', '2025-02-11 10:34:26', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店储值套餐', 2017, 6, 'sKoiWoAYxhxAcvhbZs', '2017', 'storedValue', 'operation/storedValue/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2025-02-11 10:34:04', 'zksr', '2025-02-12 16:03:53', '', 'partner,software,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('客户储值流水', 2658, 10, 'NQSyXkUqsslVwXRGPV', 'ZfGDdvXtdyqxghIocd', 'storeValueCustomer', 'operation/storeValueCustomer/index', NULL, 1, 0, 'C', '0', '0', 'account:flow:list', '#', 'zksr', '2025-02-22 17:45:47', '', NULL, '', 'partner,dc,supplier');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店充值', 2031, 10, 'qtnsnFVRVVDaPWZFkB', '2031', 'storeValueRecharge', 'operation/storeValueRecharge/index', NULL, 1, 0, 'C', '0', '0', 'account:recharge:list', '#', 'zksr', '2025-02-22 17:40:49', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('提现单详情', 2822, 3, 'FPkhwXVIrToHZrJKbZ', 'kTKWwmwemviGPWDxJT', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-branch:query', '#', 'zksr', '2025-02-24 16:05:10', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('审核拒绝', 2822, 2, 'eTqAoZxhPgAuCDspjv', 'kTKWwmwemviGPWDxJT', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-branch:disable', '#', 'zksr', '2025-02-24 16:04:53', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('审核通过', 2822, 1, 'SdfRwuuSkeVDpxRGUE', 'kTKWwmwemviGPWDxJT', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-branch:enable', '#', 'zksr', '2025-02-24 16:04:33', '', NULL, '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店提现申请', 2031, 11, 'kTKWwmwemviGPWDxJT', '2031', 'storeValuewithdrawal', 'operation/storeValuewithdrawal/index', NULL, 1, 0, 'C', '0', '0', 'account:withdraw-branch:list', '#', 'zksr', '2025-02-24 15:09:56', 'zksr', '2025-02-24 15:20:51', '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('直接结算', 2263, 7, 'TeOtTMcStsynbZvMhN', '2263', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-dc:direct-settle', '#', 'zksr', '2025-02-27 19:00:01', '', NULL, '', 'dc,partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('商户可结算', 2263, 6, 'DfqdkZirKOEKCzgasC', '2263', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:account:query-dc-settle-list', '#', 'zksr', '2025-02-27 18:59:25', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('新增提现', 2265, 0, 'JxnhcejVYeNgckDfHR', '2265', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-branch:add', '#', 'zksr', '2025-03-10 09:03:13', '', NULL, '', 'dc');


-- 账户充值记录增加赠送方案
ALTER TABLE `zksr-account`.`acc_recharge`
    ADD COLUMN `recharge_scheme_id` bigint(20) NULL COMMENT '充值赠送方案ID' AFTER `fee`,
    ADD COLUMN `scheme_rule_json` varchar(1024) NULL COMMENT '充值赠送方案JSON' AFTER `recharge_scheme_id`,
    ADD COLUMN `give_amt` decimal(10, 2) NULL COMMENT '赠送金额' AFTER `scheme_rule_json`;

-- 账户流水增加业务单号冗余
ALTER TABLE `zksr_account`.`acc_account_flow`
    ADD COLUMN `account_type` int(1) NULL COMMENT '账户类型;0-储值账户 1-赠送余额' AFTER `platform`;
ALTER TABLE `zksr-account`.`acc_account_flow`
    ADD COLUMN `memo` varchar(64) NULL COMMENT '流水变动备注' AFTER `account_type`,
    ADD COLUMN `busi_no` varchar(64) NULL COMMENT '流水变动业务单号' AFTER `memo`;

-- 账户流水索引优化
ALTER TABLE `zksr-account`.`acc_account_flow`
    ADD INDEX `idx_merchant_id`(`merchant_id`);

-- 字典维护
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('账户流水变动类型', 'account_flow_busi_type', '0', 'zksr', '2025-02-15 09:04:28', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('钱包充值支付平台', 'wallet_pay_platform', '0', 'zksr', '2025-02-11 15:29:49', '', NULL, '钱包充值支付平台');
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店下单货到付款', 'branch_hdfk', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店下单货到付款下单', 'branch_hdfk_order', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '充值手续费补贴', 'recharge_fee', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '提现成功', 'withdraw_success', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '提现失败解除冻结', 'withdraw_fail_frozen', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '提现冻结', 'withdraw_frozen', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '修改授信金额', 'update_credit', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '账户转账', 'account_transfer', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店储值支付退款', 'branch_balance_refund', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店储值支付', 'branch_balance_pay', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商城创建订单解冻入驻商', 'supplier_create_order_cancel', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '商城创建订单冻结入驻商', 'supplier_create_order', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店充值赠送', 'branch_recharge_give', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '门店充值', 'branch_recharge', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '入驻商充值', 'supplier_recharge', 'account_flow_busi_type', NULL, 'default', 'N', '0', 'zksr', '2025-02-15 09:04:42', '', NULL, NULL);
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '合利宝', 'hlb', 'wallet_pay_platform', NULL, 'default', 'N', '0', 'zksr', '2025-02-11 15:30:00', '', NULL, NULL);

-- 提现凭证
ALTER TABLE `zksr-account`.`acc_withdraw`
    ADD COLUMN `voucher` varchar(255) NULL COMMENT '提现凭证' AFTER `transfer_amt`;
ALTER TABLE `zksr-account`.`acc_withdraw`
    ADD COLUMN `apply_tip` varchar(64) NULL COMMENT '申请备注' AFTER `voucher`;

ALTER TABLE `zksr-account`.`acc_account_flow`
    ADD INDEX `idx_busi_id`(`busi_id`);

-- 支付请求信息
ALTER TABLE `zksr-account`.`acc_pay_flow` ADD COLUMN `req_info` varchar(512) NULL;

-- 门店注册索引
ALTER TABLE `zksr_member`.`mem_branch_register`
    ADD INDEX `idx_branch_id`(`branch_id`) USING BTREE;

-- 订单表增加字段
ALTER TABLE `zksr_trade`.`trd_order`
    ADD COLUMN `cz_principal_rate` DECIMAL(12,6) DEFAULT 0 COMMENT '储值本金比率' AFTER `order_amt`,
    ADD COLUMN `cz_principal_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值本金支付金额' AFTER `cz_principal_rate`,
    ADD COLUMN `cz_give_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值赠金支付金额' AFTER `cz_principal_pay_amt`
;
-- 订单明细结算表增加字段
ALTER TABLE `zksr_trade`.`trd_supplier_order_settle`
    ADD COLUMN `cz_principal_rate` DECIMAL(12,6) DEFAULT 0 COMMENT '储值本金比率' AFTER `item_amt`,
    ADD COLUMN `cz_principal_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值本金支付金额' AFTER `cz_principal_rate`,
    ADD COLUMN `cz_give_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值赠金支付金额' AFTER `cz_principal_pay_amt`,
    ADD COLUMN `return_cz_principal_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值本金已退款金额' AFTER `cz_give_pay_amt`,
    ADD COLUMN `return_cz_give_pay_amt` DECIMAL(12,6) DEFAULT 0 COMMENT '储值赠金已退款金额' AFTER `return_cz_principal_pay_amt`
;

-- 售后表增加字段
ALTER TABLE `zksr_trade`.`trd_after`
    ADD COLUMN `return_cz_principal_rate` DECIMAL(12,6) DEFAULT 0 COMMENT '储值本金比率' AFTER `return_discount_amt`,
    ADD COLUMN `return_cz_principal_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值本金支付金额' AFTER `return_cz_principal_rate`,
    ADD COLUMN `return_cz_give_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值赠金支付金额' AFTER `return_cz_principal_pay_amt`
;

-- 售后明细结算表增加字段
ALTER TABLE `zksr_trade`.`trd_supplier_after_settle`
    ADD COLUMN `return_cz_principal_rate` DECIMAL(12,6) DEFAULT 0 COMMENT '储值本金比率' AFTER `return_amt`,
    ADD COLUMN `return_cz_principal_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值本金支付金额' AFTER `return_cz_principal_rate`,
    ADD COLUMN `return_cz_give_pay_amt` decimal(12,6) DEFAULT 0 COMMENT '储值赠金支付金额' AFTER `return_cz_principal_pay_amt`
;
-- 新增订单支付方式【储值支付】字典
INSERT INTO `zksr-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '储值支付', '1', 'sys_order_pay_way', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '储值支付');

-- 订单货到付款默认支付成功消息通知处理  application-rocketmq-portal-dev.yml
-- bindings 新增
#货到付款订单发送支付成功消息
orderHdfkSuccessEvent-out-0:
          destination: orderHdfkSuccessEvent



-- 对接第三方 新增推送门店储值充值、提现信息
-- 数据字典
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '推送B2B门店储值数据', '505', 'log_request_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-10 17:06:32', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '推送B2B门店储值数据', '505', 'send_model_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-10 17:06:54', '', NULL, NULL);


-- 新增ERP11.0 门店储值模板
INSERT INTO `zksr_cloud`.`visual_setting_template`(`visual_template_id`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `template_name`, `api_template`, `template_type`, `source_type`) VALUES (591774711751278592, 'zksr', '2025-03-10 17:08:07.030', 'zksr', '2025-03-11 18:21:55.062', 1, 'ERP11.0门店储值充值/提现信息同步', '{\n    \"dcBranchNo\": \"${sendCode}\",\n    \"branchNo\": \"${branchNo}\", \n    ## 提现\n     #if($busiType == \"withdraw_success\")\n     \"type\": \"1\",\n     ## 充值\n     #elseif($busiType == \"branch_recharge\")\n     \"type\": \"0\",\n      #else\n      \"type\": \"\",\n      #end\n    ## 金额\n    \"amt\": \"${totaltAmt}\",\n    ## 赠送金额\n    \"credits\":\"${giveAmt}\"\n}', 505, 2);
INSERT INTO `zksr_cloud`.`visual_setting_detail`(`visual_detail_id`, `sys_code`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `visual_master_id`, `visual_template_id`, `template_type`, `req_type`, `api_url`, `content_type`, `resp_name`, `resp_code`, `debug_result_status`, `debug_result_message`, `memo`, `resp_data`, `resp_msg`, `visual_receipt_type`) VALUES (591782915139010560, NULL, 'zksr', '2025-03-10 17:39:57.154', NULL, NULL, 1, 511521817490620416, 591774711751278592, 505, 'POST', 'syncStoredPayInformation.action', 'application/json', 'responseCode', '200', NULL, NULL, NULL, 'responseMessage', 'responseMessage', '');

-- 修改ERP 订单、售后单推送模板
UPDATE `zksr_cloud`.`visual_setting_template` SET api_template = '{\n    \"orderNo\": \"${supplierOrderNo}\",\n    \"branchNo\": \"${branchNo}\",  \n    \"dcBranchNo\": \"${sendCode}\",   \n    \"totalAmt\": \"${subOrderAmt}\",   \n    \"realAmt\": \"${subPayAmt}\",   \n    \"discountAmt\":\"${subDiscountAmt}\",  \n     #if(!\"${memo}\")\n    \"memo\": \"${memo}\",   \n    #else\n     \"memo\": \"\",\n      #end\n    \"distributionType\":\"1\",  \n    #if($payWay == 0)\n    \"payWay\": \"1\",\n    #elseif($payWay == 2)\n    \"payWay\": \"0\",\n    #elseif($payWay == 1)\n    \"payWay\": \"2\",  \n    #else\n    \"payWay\": \"\",\n    #end\n    \"detailList\": [\n        #foreach( $sub in $detailList)\n        {\n            #set($qty = $sub.orderUnitQty)\n            #set($size = $sub.orderUnitSize)\n            #set($minDetailQty = $qty * $size)\n            \"itemNo\": \"${sub.itemSourceNo}\",\n            \"saleQty\": $minDetailQty,\n            \"itemAmt\": \"${sub.subOrderAmt}\",\n            \"itemRealAmt\": \"${sub.totalAmt}\",\n            \"discountAmount\": \"${sub.realTotalCouponAmt}\",  \n            \"giftFlag\": \"${sub.giftFlag}\",\n            \"itemFlag\": \"0\",   \n            \"line\": \"${sub.lineNum}\",\n             \"outCountPriority\":${sub.orderUnitType},\n              \"orderUnitSize\":\"${sub.orderUnitSize}\"\n        }#if($foreach.hasNext),#end\n        #end\n    ]\n}' WHERE template_name = 'ERP11.0销售订单同步';
UPDATE `zksr_cloud`.`visual_setting_template` SET api_template = '{\n    \"orderNo\": \"${supplierAfterNo}\",\n    \"branchNo\": \"${branchNo}\",\n    \"dcBranchNo\": \"${sendCode}\",\n    \"orderNoDate\": \"${createTimeString}\", \n     #if(!\"${memo}\")\n    \"memo\": \"${memo}\",   \n    #else\n     \"memo\": \"\",\n      #end     \n    ## 支付类型 1是储值  5线上支付/货到付款\n     #if($payWay == \"1\")\n     \"refundType\": \"1\",\n      #else\n      \"refundType\": \"5\",\n      #end \n    \"detailList\": [\n        #foreach( $sub in $detailList)\n        {\n            #set($price = $sub.exactReturnPrice)\n            \"itemNo\": \"${sub.itemSourceNo}\",\n            \"itemQty\":  ${sub.returnQty},\n            \"itemPrice\": ${price},\n            \"itemAmt\": ${sub.exactReturnAmt},\n            \"line\": ${sub.lineNum}\n        }#if($foreach.hasNext),#end\n        #end\n    ]\n}' WHERE template_name = 'ERP11.0售后订单同步';


--nacos配置
--application-rocketmq-system-dev.yml 新增配置：
definition:
syncDataBranchValueInfoEvent;supplierSyncDataBranchValueInfoEvent;

bindings:
        #门店储值充值/提现信息
        syncDataBranchValueInfoEvent-out-0:
          destination: syncDataBranchValueInfoEvent
        syncDataBranchValueInfoEvent-in-0:
          destination: syncDataBranchValueInfoEvent
          group: syncDataBranchValueInfoEventGroup

        #入驻商同步数据 -- 推送门店储值充值/提现信息
        supplierSyncDataBranchValueInfoEvent-out-0:
          destination: supplierSyncDataBranchValueInfoEvent
        supplierSyncDataBranchValueInfoEvent-in-0:
          destination: supplierSyncDataBranchValueInfoEvent
          group: supplierSyncDataBranchValueInfoEventtGroup


-- application-rocketmq-account-dev.yml 新增配置
bindings:
        # 推送第三方门店储值充值/提现信息
        syncDataBranchValueInfoEvent-out-0:
          destination: syncDataBranchValueInfoEvent
-- sku无库存时间
ALTER TABLE `zksr-product`.`prdt_sku`
    ADD COLUMN `min_no_stock_time` datetime(0) NULL DEFAULT NULL COMMENT '小单位无库存时间',
    ADD COLUMN `mid_no_stock_time` datetime(0) NULL DEFAULT NULL COMMENT '中单位无库存时间',
    ADD COLUMN `large_no_stock_time` datetime(0) NULL DEFAULT NULL COMMENT '大单位无库存时间';
-- 秒杀/特价限定sku数
ALTER TABLE `zksr-promotion`.`prm_activity`
    ADD COLUMN `limit_skus` int(10) NULL COMMENT '秒杀/特价, 限定参与SKU数' AFTER `memo`;




-- 【福商通】对接第三方入驻商，将开发接口配置下放到平台商级别  修改权限配置
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner' WHERE `menu_code` = '2408';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner' WHERE `menu_code` = '2409';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner' WHERE `menu_code` = '2410';

-- PC 后台储值导入
CREATE TABLE `zksr-account`.`acc_recharge_import` (
                                       `recharge_import_id` bigint(20) NOT NULL,
                                       `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                       `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                       `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                       `deleted` tinyint(1) DEFAULT '0' COMMENT '0-未删除,1-已删除',
                                       `audit_by` varchar(64) DEFAULT NULL COMMENT '审核人',
                                       `recharge_import_state` tinyint(2) DEFAULT NULL COMMENT '0-初始化, 1-审核成功, 2-作废',
                                       `remark` varchar(255) DEFAULT NULL COMMENT '后台充值备注',
                                       `voucher` varchar(255) DEFAULT NULL COMMENT '凭证',
                                       `counter` bigint(20) DEFAULT NULL COMMENT '计数',
                                       `recharge_amt` decimal(16,2) DEFAULT NULL COMMENT '总金额',
                                       PRIMARY KEY (`recharge_import_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='后台导入充值表';

CREATE TABLE `zksr-account`.`acc_recharge_import_dtl` (
                                           `recharge_import_dtl_id` bigint(20) NOT NULL,
                                           `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                           `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           `recharge_import_id` bigint(20) DEFAULT NULL,
                                           `branch_id` bigint(20) NOT NULL COMMENT '门店ID',
                                           `recharge_amt` decimal(16,2) NOT NULL COMMENT '充值金额',
                                           `recharge_id` bigint(20) DEFAULT NULL COMMENT '关联的充值单ID',
                                           PRIMARY KEY (`recharge_import_dtl_id`),
                                           UNIQUE KEY `unique_key` (`recharge_import_id`,`branch_id`),
                                           KEY `idx_recharge_import_id` (`recharge_import_id`),
                                           KEY `idx_branch_id` (`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PC后台导入充值详情';


INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('导入解析', 2860, 7, 'edVZaFbOyEKBNQCxCC', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:import', '#', 'zksr', '2025-03-25 14:23:47', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('审核批次', 2860, 6, 'DiAHcuSHVDHKANXXIE', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:enable', '#', 'zksr', '2025-03-25 14:23:26', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('作废批次', 2860, 5, 'hZELNOTsaazeXPfhUw', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:disable', '#', 'zksr', '2025-03-25 14:23:12', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('获取审核验证码', 2860, 4, 'NdNeqJJKItcOIpcOUn', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:sms', '#', 'zksr', '2025-03-25 14:22:51', '', NULL, '', 'dc,partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('批次详情', 2860, 3, 'nGsizFRJFBJmynZeGs', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:query', '#', 'zksr', '2025-03-25 14:22:31', '', NULL, '', 'dc,partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('编辑充值批次', 2860, 2, 'SHhJUJtmnXoWzELcEU', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:edit', '#', 'zksr', '2025-03-25 14:22:18', '', NULL, '', 'partner,dc');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('新增充值批次', 2860, 1, 'vHXpfrtBNKujIwMcjP', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:add', '#', 'zksr', '2025-03-25 14:21:34', '', NULL, '', 'dc,partner');
INSERT INTO `zksr-cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('PC门店储值', 2031, 12, 'UyWNOaXJckpimNhayc', '2031', 'menberStored', 'operation/menberStored/index', NULL, 1, 0, 'C', '0', '0', 'recharge:rechargeImport:list', '#', 'zksr', '2025-03-25 08:56:04', 'zksr', '2025-03-25 14:21:05', '', 'dc,partner');

-- 增加充值来源
ALTER TABLE `zksr-account`.`acc_recharge`
    ADD COLUMN `source` varchar(6) NULL DEFAULT 'app' COMMENT 'app-用户端, pc-后台' AFTER `give_amt`

-- 入驻商订单表增加字段
ALTER TABLE `zksr_trade`.`trd_supplier_order`
    ADD COLUMN `sub_refund_amt` DECIMAL(12,2) DEFAULT 0 COMMENT '入驻商已退款金额（不包括优惠）' AFTER `sub_pay_fee`,
    ADD COLUMN `sub_refund_fee` decimal(12,2) DEFAULT 0 COMMENT '入驻商已退款手续费金额' AFTER `sub_refund_amt`
;

-- 修复历史入驻商订单 已退款金额和手续费数据 SQL
UPDATE trd_supplier_order tso
    JOIN (
        SELECT
            tsa.supplier_order_no,
            SUM(tsa.sub_refund_amt) AS sub_refund_amt,
            SUM(tsa.sub_refund_fee) AS sub_refund_fee
        FROM
            trd_supplier_after tsa
            LEFT JOIN trd_supplier_after_dtl tsad ON tsa.supplier_after_id = tsad.supplier_after_id
        WHERE
            tsad.is_cancel != 1
            AND approve_state = 1
        GROUP BY
            tsa.supplier_order_no
    ) updateTable ON tso.supplier_order_no = updateTable.supplier_order_no
    SET
        tso.sub_refund_amt = updateTable.sub_refund_amt,
        tso.sub_refund_fee = updateTable.sub_refund_fee

-- 同步日志 日志重发功能配置权限至平台商
INSERT INTO `zksr-cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('同步日志重发', 2454, 1, 'IlSPfUlFhmwdyXHmgR', '2454', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:log:magRetry', '#', 'zksr', '2025-03-25 15:11:33', '', NULL, '', 'partner');

-- 入驻商 - 开放接口设置  接口配置显示异常 权限配置
INSERT INTO `zksr-cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('可视化主表列表查询', 2532, 0, 'pFuIQxcWgXySLHMEJR', '2532', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:visualSettingMaster:list', '#', 'zksr', '2025-03-25 15:21:45', '', NULL, '', 'partner');


---业务员目标设置新增月下单量目标 客单价目标 首次动销数量
ALTER TABLE `zksr-member`.`mem_colonel_target`
ADD COLUMN `month_order_count` INT DEFAULT NULL COMMENT '月下单量',
ADD COLUMN `month_avg_order_value` DECIMAL(19,6) DEFAULT NULL COMMENT '月客单价',
ADD COLUMN `month_first_sale_count` INT DEFAULT NULL COMMENT '月首次动销数量';
