-- 入驻商订单表增加字段
ALTER TABLE `zksr_trade`.trd_supplier_order
add `delivery_state` int DEFAULT null COMMENT '订单状态';

-- 更新历史数据
UPDATE `zksr_trade`.trd_supplier_order AS tso
JOIN (
    SELECT supplier_order_id, delivery_state
    FROM `zksr_trade`.trd_supplier_order_dtl
    WHERE supplier_order_dtl_id IN (
        SELECT MIN(supplier_order_dtl_id)
        FROM `zksr_trade`.trd_supplier_order_dtl
        GROUP BY supplier_order_id
    )
) AS first_row
ON tso.supplier_order_id = first_row.supplier_order_id
SET tso.delivery_state = first_row.delivery_state;

