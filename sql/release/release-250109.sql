-- 增加导出列配置
ALTER TABLE `zksr_cloud`.`sys_export_job`
    ADD COLUMN `col_config` text NULL COMMENT '导出列配置' AFTER `user_key`;

-- 公海字段
ALTER TABLE `zksr_member`.`mem_branch`
    ADD COLUMN `seas_time` datetime NULL COMMENT '进入公海时间'


-- 商品基本信息新增字段 辅助商品编号
ALTER TABLE `zksr_product`.`prdt_spu`
    ADD COLUMN `auxiliary_spu_no` VARCHAR(32) DEFAULT NULL COMMENT 'SPU辅助的商品编号';

-- 促销满赠改造  新增字段
ALTER TABLE `zksr_promotion`.`prm_fg_rule`
    ADD COLUMN `buy_sku_num` int(8) DEFAULT 1 COMMENT '购买品项数(sku种类数)默认为1',
ADD COLUMN `gift_group_type` tinyint(1) DEFAULT 2 COMMENT '赠送方式(默认为全赠 0仅一种，1任选，2全赠)',
ADD COLUMN `gift_sku_unit_qty` int(8) DEFAULT NULL COMMENT '赠送单位数量';

-- 满赠改造  新增数据字典
INSERT INTO `zksr_cloud`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '满赠-赠送方式', 'gift_group_type', '0', 'zksr', '2024-12-31 11:02:19', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '仅一种', '0', 'gift_group_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-31 11:02:39', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '任选', '1', 'gift_group_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-31 11:02:45', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '全赠', '2', 'gift_group_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-31 11:02:52', '', NULL, NULL);


-- 对接好帮你 新增数据字典系统类型
INSERT INTO `zksr_cloud`.`sys_dict_data`( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '好帮你ERP', '6', 'source_type', NULL, 'default', 'N', '0', 'zksr', '2024-12-02 16:12:31', 'zksr', '2024-12-02 16:12:38', NULL);
