USE zksr_product;
CREATE TABLE `prdt_block_scheme`  (
    `block_scheme_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `sys_code` bigint(20) NOT NULL DEFAULT '0' COMMENT '平台商id',
    `scheme_no` varchar(32) NOT NULL DEFAULT '' COMMENT '方案编码',
    `scheme_name` varchar(50) NOT NULL DEFAULT '' COMMENT '方案名称',
    `area_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '城市id',
    `supplier_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '入驻商id',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1正常 0禁用',
    `memo` varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
	`del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志 0 未删除 1 已删除',
	`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	`create_by` varchar(30) NOT NULL DEFAULT '' COMMENT '创建人',
	`update_by` varchar(30) NOT NULL DEFAULT '' COMMENT '修改人',
PRIMARY KEY (`block_scheme_id`) USING BTREE,
UNIQUE KEY `uk_scheme_no` (`scheme_no`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '经营屏蔽方案表';

CREATE TABLE `prdt_block_branch` (
    `block_branch_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `sys_code` bigint(20) NOT NULL DEFAULT '0' COMMENT '平台商id',
    `scheme_no` varchar(32) NOT NULL DEFAULT '' COMMENT '方案编码',
    `branch_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '门店id',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `create_by` varchar(30) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_by` varchar(30) NOT NULL DEFAULT '' COMMENT '修改人',
PRIMARY KEY (`block_branch_id`) USING BTREE,
UNIQUE KEY `uk_scheme_no_branch_id` (`scheme_no`, `branch_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '经营屏蔽客户表';

CREATE TABLE `prdt_block_sku` (
    `block_sku_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `sys_code` bigint(20) NOT NULL DEFAULT '0' COMMENT '平台商id',
    `scheme_no` varchar(32) NOT NULL DEFAULT '' COMMENT '方案编码',
    `sku_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '商品sku_id',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `create_by` varchar(30) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_by` varchar(30) NOT NULL DEFAULT '' COMMENT '修改人',
PRIMARY KEY (`block_sku_id`) USING BTREE,
UNIQUE KEY `uk_scheme_no_sku_id` (`scheme_no`, `sku_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '经营屏蔽sku表';