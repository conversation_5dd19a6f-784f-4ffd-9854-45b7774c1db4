
-- 展示类目配置生产日期格式
ALTER TABLE `zksr_product`.`prdt_area_class`
    ADD COLUMN `show_produce_date` tinyint(2) NULL DEFAULT 1 COMMENT '是否展示生产日期 0-关闭, 1-开启' AFTER `del_flag`,
    ADD COLUMN `produce_date_format` varchar(12) NULL DEFAULT 'yyyy-MM-dd' COMMENT '生产日期格式 yyyy-MM-dd 年月日 yy/MM/dd 年月日 yy/MM 年月' AFTER `show_produce_date`;

ALTER TABLE `zksr_product`.`prdt_sale_class`
    ADD COLUMN `show_produce_date` tinyint(2) NULL DEFAULT 1 COMMENT '是否展示生产日期 0-关闭, 1-开启' AFTER `del_flag`,
    ADD COLUMN `produce_date_format` varchar(12) NULL DEFAULT 'yyyy-MM-dd' COMMENT '生产日期格式 yyyy-MM-dd 年月日 yy/MM/dd 年月日 yy/MM 年月' AFTER `show_produce_date`;

-- 添加门店id字段
ALTER TABLE `zksr_member`.`mem_branch_register` ADD COLUMN `branch_id` BIGINT NULL COMMENT '门店id' AFTER `three_area_city_id`;
ALTER TABLE `zksr_member`.`mem_member_register` ADD COLUMN `branch_id` BIGINT NULL COMMENT '门店id' AFTER `colonel_id`;

-- 处理门店注册表和门店表关联数据
UPDATE `zksr_member`.mem_member_register mmr LEFT JOIN `zksr_member`.mem_branch branch ON (mmr.user_name=branch.contact_phone COLLATE utf8mb4_general_ci AND mmr.branch_name=branch.branch_name COLLATE utf8mb4_general_ci)
    SET mmr.branch_id=branch.branch_id WHERE mmr.approve_flag=1;
UPDATE `zksr_member`.mem_branch_register mbr LEFT JOIN `zksr_member`.mem_branch branch ON (mbr.contact_phone=branch.contact_phone COLLATE utf8mb4_general_ci AND mbr.branch_name=branch.branch_name COLLATE utf8mb4_general_ci)
    SET mbr.branch_id=branch.branch_id WHERE mbr.approve_flag=1;


-- 城市价格码唯一值调整
ALTER TABLE `zksr_product`.`prdt_area_channel_price`
    ADD UNIQUE INDEX `unique_key`(`area_id`, `channel_id`, `sale_price_code`) USING BTREE;

ALTER TABLE `zksr_product`.`prdt_supplier_group_price`
    ADD UNIQUE INDEX `unique_key`(`group_id`, `area_id`, `sale_price_code`) USING BTREE;


-- 新增系统时间类型字典
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('系统时间类型', 'sys_time_type', '0', 'zksr', '2024-11-25 09:49:14', 'zksr', '2024-11-25 09:50:09', '系统时间类型');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (0, '分钟', '0', 'sys_time_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:40', '', NULL, '分钟');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (0, '小时', '1', 'sys_time_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '小时');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (0, '天', '2', 'sys_time_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '天');

-- 新增系统状态类型字典
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('系统状态类型', 'sys_status_type', '0', 'zksr', '2024-11-25 09:49:14', 'zksr', '2024-11-25 09:50:09', '系统状态类型');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (0, '否', '0', 'sys_status_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:40', '', NULL, '否');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (0, '是', '1', 'sys_status_type', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, '是');

-- 入驻商管理分类关系新增可售后时间等字段
ALTER TABLE `zksr_product`.`prdt_supplier_class`
    ADD COLUMN is_after_sales TINYINT(2) DEFAULT 1 COMMENT '是否可售后，默认为是（1：是，0：否）',
	ADD COLUMN after_sales_time_type TINYINT(2) DEFAULT NULL COMMENT '售后时间类型：字典：sys_time_type',
	ADD COLUMN after_sales_time INT(4) DEFAULT NULL COMMENT '可售后时间'
;

-- 入驻商管理分类关系新增可售后时间等字段
ALTER TABLE `zksr_trade`.`trd_supplier_order_dtl`
    ADD COLUMN is_after_sales TINYINT(2) DEFAULT 1 COMMENT '是否可售后，默认为是（1：是，0：否）',
	ADD COLUMN after_sales_time INT(8) DEFAULT 10080 COMMENT '可售后时间（存储类型为分钟）默认为为7天（10080分钟）'
;

-- 新增菜单【入驻商管理分类】
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`)
VALUES ('入驻商管理分类', 2017, 25, 'xfsMmrOQeUOLHBjehA', '2017', 'suppliedCategory', 'operation/suppliedCategory/index', NULL, 1, 0, 'C', '0', '0', 'product:supplierClass:list', '#', 'zksr', '2025-03-12 08:49:02', 'zksr', '2025-03-12 10:27:52', '', 'supplier');
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`)
VALUES ('详情', 2851, 1, 'CHNmlvlzAWPoaVbIAG', 'xfsMmrOQeUOLHBjehA', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:supplierClass:query', '#', 'zksr', '2025-03-12 10:39:55', '', NULL, '', 'supplier');
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`)
VALUES ('修改', 2851, 2, 'KTccNLIhvIwFGLZHcF', 'xfsMmrOQeUOLHBjehA', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:supplierClass:edit', '#', 'zksr', '2025-03-12 10:40:29', '', NULL, '', 'supplier');

-- 修复订单售后时间
update `zksr_trade`.`trd_supplier_order_dtl` set after_sales_time = 10080 where after_sales_time = 0;