-- 微信开户行对照码表
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('微信开户行对照表', 'wx_open_bank_name', '0', 'zksr', '2025-01-21 15:58:05', '', NULL, '微信开户行对照表');

INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '宁波银行', '宁波银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '中国银行', '中国银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '邮政储蓄银行', '邮政储蓄银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '建设银行', '建设银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '农业银行', '农业银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '华夏银行', '华夏银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '北京银行', '北京银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '平安银行', '平安银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '广发银行', '广发银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '光大银行', '光大银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '兴业银行', '兴业银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '浦发银行', '浦发银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '中信银行', '中信银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '民生银行', '民生银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '招商银行', '招商银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '交通银行', '交通银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '工商银行', '工商银行', 'wx_open_bank_name', NULL, 'default', 'N', '0', 'zksr', '2025-01-21 15:58:38', '', NULL, NULL);

-- 城市上架商品表添加spu索引
CREATE INDEX idx_spu_id ON `zksr_product`.`prdt_area_item`(spu_id);
-- 搜索推荐
CREATE TABLE `zksr_member`.`mem_search_his` (
                                                `search_his_id` bigint NOT NULL COMMENT '搜索历史id',
                                                `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
                                                `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                                `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                                `member_id` bigint DEFAULT NULL COMMENT '门店用户id',
                                                `branch_id` bigint DEFAULT NULL COMMENT '门店id',
                                                `words` varchar(64) DEFAULT NULL COMMENT '搜索词',
                                                PRIMARY KEY (`search_his_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='搜索历史表';

CREATE TABLE `zksr_product`.`prdt_keywords` (
                                                `keywords_id` bigint NOT NULL COMMENT '关键词id',
                                                `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
                                                `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                                `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                                `keyword` varchar(64) NOT NULL COMMENT '关键词',
                                                `status` int DEFAULT NULL COMMENT '状态',
                                                PRIMARY KEY (`keywords_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='搜索关键词词库';

ALTER TABLE `zksr_product`.`prdt_spu`
    ADD COLUMN `keywords` varchar(1024) NULL COMMENT '关联关键词' ;

ALTER TABLE `zksr_cloud`.`sys_partner_policy`
    ADD COLUMN `area_id` bigint(20) NULL COMMENT '区域城市ID' ;

INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '搜索配置', 2730, 3, 'ZvSyZWIESCrIoyZpyr', 'ZcZcfROKeQBpLJYidO', 'searchConfiguration', 'platform/searchConfiguration/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2025-01-18 15:07:36', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '搜索日志', 2730, 2, 'lbqefuasEpRaYOPZji', 'ZcZcfROKeQBpLJYidO', 'searchLogings', 'platform/searchLogings/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2025-01-18 15:06:40', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '关键词库', 2730, 1, 'vcIkWQGVegjEdtvaRt', 'ZcZcfROKeQBpLJYidO', 'keywordLibrary', 'platform/keywordLibrary/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2025-01-18 15:05:10', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` ( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '推荐管理', 0, 22, 'ZcZcfROKeQBpLJYidO', '0', 'recommend', NULL, NULL, 1, 0, 'M', '0', '0', NULL, '#', 'zksr', '2025-01-18 15:03:51', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '推荐类型', 'recommend_type', '0', 'zksr', '2025-01-22 17:15:44', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '订单明细', 'orderDetails', 'recommend_type', NULL, 'default', 'N', '0', 'zksr', '2025-01-22 17:17:54', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '我的', 'my', 'recommend_type', NULL, 'default', 'N', '0', 'zksr', '2025-01-22 17:17:19', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 1, '购物车', 'car', 'recommend_type', NULL, 'default', 'N', '0', 'zksr', '2025-01-22 17:17:04', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '商品详情', 'goodDetails', 'recommend_type', NULL, 'default', 'N', '0', 'zksr', '2025-01-22 17:16:41', '', NULL, NULL);

