
-- 订单增加分销模式
ALTER TABLE `zksr_trade`.trd_order
add `distribution_mode` varchar(64) DEFAULT null COMMENT '分销模式';

-- 收货地址
ALTER TABLE `zksr_trade`.trd_order
add `delivery_address` varchar(512) DEFAULT null COMMENT '收货地址';
ALTER TABLE `zksr_trade`.trd_order
add `contact_phone` varchar(16) DEFAULT null COMMENT '联系电话';
ALTER TABLE `zksr_trade`.trd_order
add `contact_name` varchar(32) DEFAULT null COMMENT '联系人';
ALTER TABLE `zksr_trade`.trd_order
add `member_address_id` bigint DEFAULT null COMMENT '用户地址ID';
ALTER TABLE `zksr_trade`.trd_order
add `member_invoice_id` bigint DEFAULT null COMMENT '用户发票ID';
-- 新建用户地址表
 CREATE TABLE `zksr_member`.mem_member_address (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID主键',
  `member_id` bigint NOT NULL COMMENT '用户id',
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  `three_area_city_id` bigint DEFAULT NULL COMMENT '三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)',
  `province_name` varchar(64) DEFAULT NULL COMMENT '省份',
  `city_name` varchar(64) DEFAULT NULL COMMENT '城市',
  `district_name` varchar(64) DEFAULT NULL COMMENT '区县',
  `contact_name` varchar(32) NOT NULL DEFAULT '' COMMENT '联系人',
  `contact_phone` varchar(16) NOT NULL DEFAULT '' COMMENT '联系电话',
  `delivery_address` varchar(512) NOT NULL DEFAULT '' COMMENT '详细收货地址',
  `remark` varchar(256) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` varchar(100) DEFAULT '' COMMENT '创建人',
  `create_time` datetime(3) NOT NULL DEFAULT current_timestamp(3) COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT '' COMMENT '更新人',
  `update_time` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3) COMMENT '更新时间',
  `del_flag` int NOT null DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `version` int(11) NOT null DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_mem_member_address_mi` (`member_id`),
  KEY `idx_mem_member_address_cp` (`contact_phone`)
) COMMENT='用户地址表';

-- 增加唯一校验
ALTER TABLE zksr_member.mem_member_address
ADD CONSTRAINT unique_mem_member_address_msd UNIQUE (sys_code, member_id, delivery_address);


-- 新建发票设置表
 CREATE TABLE `zksr_member`.mem_member_invoice (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID主键',
  `member_id` bigint NOT NULL COMMENT '用户id',
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  `invoice_type` int NOT NULL  COMMENT '发票类型,10电子普通发票20专用发票',
  `title_type` int NOT NULL  COMMENT '发票抬头类型,10个人20单位',
  `invoice_title` varchar(512) DEFAULT '' COMMENT '发票抬头',
  `taxpayer_code` varchar(128) DEFAULT '' COMMENT '纳税人识别码',
  `company_address` varchar(512) DEFAULT '' COMMENT '单位地址',
  `company_phone` varchar(16) NOT NULL DEFAULT '' COMMENT '单位电话',
  `contact_name` varchar(32) NOT NULL DEFAULT '' COMMENT '联系人',
  `contact_phone` varchar(16) NOT NULL DEFAULT '' COMMENT '联系电话',
  `bank_name` varchar(512) NOT NULL DEFAULT '' COMMENT '开户银行',
  `bank_account` varchar(64) NOT NULL DEFAULT '' COMMENT '银行账户',
  `remark` varchar(256) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` varchar(100) DEFAULT '' COMMENT '创建人',
  `create_time` datetime(3) NOT NULL DEFAULT current_timestamp(3) COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT '' COMMENT '更新人',
  `update_time` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3) COMMENT '更新时间',
  `del_flag` int NOT null DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `version` int(11) NOT null DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_mem_member_address_mi` (`member_id`),
  KEY `idx_mem_member_address_cp` (`contact_phone`)
) COMMENT='用户发票表';
-- 增加唯一校验
ALTER TABLE zksr_member.mem_member_invoice
ADD CONSTRAINT unique_mem_member_invoice_smit UNIQUE (sys_code, member_id, invoice_title, taxpayer_code);




-- 新建入驻商订单发票表
 CREATE TABLE `zksr_trade`.trd_supplier_order_invoice (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID主键',
  `member_invoice_id` bigint(20) NOT NULL COMMENT '用户发票id',
  `member_id` bigint NOT NULL COMMENT '用户id',
  `supplier_order_id` bigint NOT NULL COMMENT '入驻商订单ID',
  `supplier_order_no` varchar(32) NOT NULL DEFAULT '' COMMENT '入驻商订单号',
  `sys_code` bigint DEFAULT NULL COMMENT '平台商id',
  `invoice_type` int NOT NULL  COMMENT '发票类型,10电子普通发票20专用发票',
  `title_type` int NOT NULL  COMMENT '发票抬头类型,10个人20单位',
  `invoice_title` varchar(512) DEFAULT '' COMMENT '发票抬头',
  `taxpayer_code` varchar(128) DEFAULT '' COMMENT '纳税人识别码',
  `company_address` varchar(512) DEFAULT '' COMMENT '单位地址',
  `company_phone` varchar(16) NOT NULL DEFAULT '' COMMENT '单位电话',
  `contact_name` varchar(32) NOT NULL DEFAULT '' COMMENT '联系人',
  `contact_phone` varchar(16) NOT NULL DEFAULT '' COMMENT '联系电话',
  `bank_name` varchar(512) NOT NULL DEFAULT '' COMMENT '开户银行',
  `bank_account` varchar(64) NOT NULL DEFAULT '' COMMENT '银行账户',
  `pdf_url` VARCHAR(512) COMMENT '发票PDF文件下载/访问URL ',
  `remark` varchar(256) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` varchar(100) DEFAULT '' COMMENT '创建人',
  `create_time` datetime(3) NOT NULL DEFAULT current_timestamp(3) COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT '' COMMENT '更新人',
  `update_time` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3) COMMENT '更新时间',
  `del_flag` int NOT null DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `version` int(11) NOT null DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_trd_supplier_order_invoice_son` (`supplier_order_no`)
) COMMENT='订单发票表';
-- 增加唯一校验
ALTER TABLE zksr_trade.trd_supplier_order_invoice
ADD CONSTRAINT unique_trd_supplier_order_invoice_smit UNIQUE (sys_code, member_invoice_id, supplier_order_no);
