-- 满赠，满减活动新增每天一次类型（限定全场类型）
INSERT INTO `zksr_cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '每天一次', '0', 'fg_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2025-02-20 10:44:28', '', NULL, NULL);

-- 新增表
CREATE TABLE `zksr_member`.`mem_command` (
                                             `command_id` bigint(20) NOT NULL COMMENT '指令ID',
                                             `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                             `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                             `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                             `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                             `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                             `command_level` tinyint(1) DEFAULT NULL COMMENT '0-指令锚点，1-普通指令，2-无需执行指令',
                                             `pid` bigint(20) DEFAULT NULL COMMENT '指令锚点id，仅普通指令',
                                             `command_date` datetime(3) DEFAULT NULL COMMENT '指令有效期',
                                             `command_type` tinyint(2) DEFAULT NULL COMMENT '指令类型（数据字典）(加单、拜访等)',
                                             `status` tinyint(1) DEFAULT NULL COMMENT '0-作废 1-进行中  2-完成 ',
                                             `pub_merchant_type` varchar(16) DEFAULT NULL COMMENT '指令发布角色 colonel-业务员',
                                             `pub_id` bigint(20) DEFAULT NULL COMMENT '指令发布人id',
                                             `exec_merchant_type` varchar(16) DEFAULT NULL COMMENT '指令执行角色 branch-门店',
                                             `exec_id` bigint(20) DEFAULT NULL COMMENT '指令执行人id',
                                             `exec_res` text COMMENT '执行结果',
                                             `memo` varchar(255) DEFAULT NULL COMMENT '备注',
                                             PRIMARY KEY (`command_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '指令表';

-- 2024.5.16 入驻商售后订单明细表新增字段
ALTER TABLE `zksr_trade`.`trd_supplier_order_dtl`
    ADD COLUMN `command_id` BIGINT(20) NULL COMMENT '加单指令ID'
;

-- 入驻商订单明细添加字段
ALTER TABLE `zksr_trade`.`trd_supplier_after_dtl`
    ADD COLUMN `original_return_qty` INT(8) NULL COMMENT '原申请退货最小单位数量' AFTER `return_qty`,
    ADD COLUMN `original_return_amt` DECIMAL(12,2) NULL COMMENT '原申请退货金额' AFTER `original_return_qty`
;
-- 初始化原申请退货最小单位数量 & 金额
UPDATE `zksr_trade`.`trd_supplier_after_dtl` SET original_return_qty = return_qty;
UPDATE `zksr_trade`.`trd_supplier_after_dtl` SET original_return_amt = return_amt;
-- 秒杀活动新增状态和排序
ALTER TABLE `zksr_promotion`.`prm_sk_rule`
ADD COLUMN sk_status tinyint DEFAULT 1 COMMENT '状态：0-停用，1-启用',
ADD COLUMN sort_order int DEFAULT NULL COMMENT '排序字段';
