-- sku无库存时间
ALTER TABLE `zksr_product`.`prdt_sku`
    ADD COLUMN `min_no_stock_time` datetime(0) NULL DEFAULT NULL COMMENT '小单位无库存时间',
    ADD COLUMN `mid_no_stock_time` datetime(0) NULL DEFAULT NULL COMMENT '中单位无库存时间',
    ADD COLUMN `large_no_stock_time` datetime(0) NULL DEFAULT NULL COMMENT '大单位无库存时间';

-- 权限按钮
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('平台管理类别导出', 2110, 8, 'DQuHqzmOyoSSMbohre', '2110', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:catgory:export', '#', 'zksr', '2025-03-19 09:05:13', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('投诉管理导出', 2361, 2, 'wwubMtraAHIksLoAEt', '2361', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:complain:export', '#', 'zksr', '2025-03-19 09:02:18', 'zksr', '2025-03-19 09:03:06', '', 'partner,dc');

-- 门店 是否支持货到付款 null值置零
UPDATE `zksr_member`.`mem_branch` SET hdfk_support = 0 WHERE hdfk_support is null;

-- 秒杀/特价限定sku数
ALTER TABLE `zksr_promotion`.`prm_activity`
    ADD COLUMN `limit_skus` int(10) NULL COMMENT '秒杀/特价, 限定参与SKU数';

ALTER TABLE `zksr_promotion`.`prm_sk_rule`
    ADD UNIQUE INDEX `unique_key`(`activity_id`, `sku_id`) USING BTREE;

ALTER TABLE `zksr_promotion`.`prm_sp_rule`
    ADD UNIQUE INDEX `unique_key`(`activity_id`, `sku_id`) USING BTREE;


-- 【福商通】对接第三方入驻商，将开发接口配置下放到平台商级别  修改权限配置
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner' WHERE `menu_code` = '2408';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner' WHERE `menu_code` = '2409';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner' WHERE `menu_code` = '2410';