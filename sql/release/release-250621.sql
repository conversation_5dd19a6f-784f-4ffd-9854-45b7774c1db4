-- 负库存标识
ALTER TABLE zksr_cloud.sys_supplier ADD is_negative_stock TINYINT DEFAULT 0 NULL COMMENT '是否支持负库存下单 0：否，1：是';
ALTER TABLE zksr_trade.trd_supplier_order ADD stock_short_flag TINYINT DEFAULT 0 NULL COMMENT '是否负库存下单 0-否 1-是';
ALTER TABLE zksr_trade.trd_supplier_order_dtl ADD stock_short_flag TINYINT DEFAULT 0 NULL COMMENT '是否负库存下单 0-否 1-是';

-- 入驻商订单表增加字段
ALTER TABLE `zksr_trade`.trd_supplier_order
add `delivery_state` int DEFAULT null COMMENT '订单状态';

-- 更新历史数据
UPDATE `zksr_trade`.trd_supplier_order AS tso
JOIN (
    SELECT supplier_order_id, delivery_state
    FROM `zksr_trade`.trd_supplier_order_dtl
    WHERE supplier_order_dtl_id IN (
        SELECT MIN(supplier_order_dtl_id)
        FROM `zksr_trade`.trd_supplier_order_dtl
        GROUP BY supplier_order_id
    )
) AS first_row
ON tso.supplier_order_id = first_row.supplier_order_id
SET tso.delivery_state = CASE
    WHEN first_row.delivery_state = 1 THEN 0
    WHEN first_row.delivery_state = 2 THEN 10
    WHEN first_row.delivery_state = 4 THEN 20
    WHEN first_row.delivery_state = 7 THEN 21
    ELSE tso.delivery_state -- 保持原值不变
END;

