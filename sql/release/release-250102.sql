-- 增加原始要货数量
ALTER TABLE `zksr_product`.`prdt_branch_yhdata`
    ADD COLUMN `source_yh_qty` int(10) NULL DEFAULT 0 COMMENT '原始要货数量' AFTER `pos_suggest_qty`;
-- 要货单后台菜单
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('补货单', 2028, 9, 'kDzFsqQquTPPQFANXv', '2028', 'replenishment', 'orderManage/replenishment/index', NULL, 1, 0, 'C', '0', '0', 'product:yhdata:list', '#', 'zksr', '2024-12-26 14:44:06', 'zksr', '2024-12-26 18:41:08', '', 'partner,dc');


INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('补货单匹配状态', 'yh_match_state', '0', 'zksr', '2024-12-27 14:11:10', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '匹配失败', '2', 'yh_match_state', NULL, 'default', 'N', '0', 'zksr', '2024-12-27 14:13:38', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '匹配成功', '1', 'yh_match_state', NULL, 'default', 'N', '0', 'zksr', '2024-12-27 14:12:58', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '未匹配', '0', 'yh_match_state', NULL, 'default', 'N', '0', 'zksr', '2024-12-27 14:12:32', '', NULL, NULL);


--批次发券新增门店发放数量字段
ALTER TABLE `zksr_promotion`.`prm_coupon_batch`
ADD COLUMN `branch_send_qty` int DEFAULT NULL COMMENT '门店发放数量';

-- 入驻商订单表 新增字段  打印次数字段
ALTER TABLE `zksr_trade`.`trd_supplier_order`
    ADD COLUMN `print_qty` INT(11) DEFAULT 0 COMMENT '打印次数',
    ADD COLUMN `print_state` TINYINT(1) DEFAULT 0 COMMENT '打印状态 0：未打印，1：已打印'
;

-- 报表库 交易域订单事务事实表 新增字段
ALTER TABLE `zksr_report`.`dwd_trd_order_dtl_inc`
    ADD COLUMN `pay_state` TINYINT(1)  DEFAULT NULL COMMENT '支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消, 3-货到付款未支付, 4-货到付款已支付',
	ADD COLUMN `pay_way` VARCHAR(32) DEFAULT NULL COMMENT '支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款',
    ADD COLUMN `category_id` BIGINT(20) DEFAULT NULL COMMENT '管理分类id'
;

-- 报表库 交易域售后订单事务事实表 新增字段
ALTER TABLE `zksr_report`.`dwd_trd_return_inc`
    ADD COLUMN `approve_state` INT(11) DEFAULT NULL COMMENT '审核状态(数据字典);0待审核 1同意 2拒绝',
	ADD COLUMN `refund_state` INT(11) DEFAULT NULL COMMENT '退款状态(数据字典);0-未退款 1-退款中 2-退款完成  3-退款失败'
;


DROP TABLE IF EXISTS `zksr_report`.dwd_colonel_visit_inc;
CREATE TABLE `zksr_report`.dwd_colonel_visit_inc(
                                      `colonel_visit_log_id` BIGINT(20) NOT NULL  COMMENT '业务员拜访日志id' ,
                                      `create_by` VARCHAR(64)   COMMENT '创建人' ,
                                      `create_time` DATETIME(3)   COMMENT '创建时间' ,
                                      `update_by` VARCHAR(64)   COMMENT '更新人' ,
                                      `update_time` DATETIME(3)   COMMENT '更新时间' ,
                                      `sys_code` BIGINT(20)   COMMENT '平台商id' ,
                                      `area_id` BIGINT(20)   COMMENT '区域城市id' ,
                                      `colonel_id` BIGINT(20)   COMMENT '业务员ID' ,
                                      `branch_id` BIGINT(20)   COMMENT '门店ID' ,
                                      `sign_in_longitude` VARCHAR(30)   COMMENT '签到经度' ,
                                      `sign_in_latitude` VARCHAR(30)   COMMENT '签到纬度' ,
                                      `sign_in_address` VARCHAR(255)   COMMENT '签到地址' ,
                                      `sign_in_distance` VARCHAR(50)   COMMENT '签到距离' ,
                                      `sign_in_img_urls` TEXT(255)   COMMENT '签到图片链接：多个以英文，隔开' ,
                                      `sign_in_date` DATETIME(3)   COMMENT '签到时间' ,
                                      `sign_out_longitude` VARCHAR(30)   COMMENT '签退经度' ,
                                      `sign_out_latitude` VARCHAR(30)   COMMENT '签退纬度' ,
                                      `sign_out_address` VARCHAR(255)   COMMENT '签退地址' ,
                                      `sign_out_distance` VARCHAR(50)   COMMENT '签退距离' ,
                                      `sign_out_date` DATETIME(3)   COMMENT '签退时间' ,
                                      `visit_interval_time` VARCHAR(20)   COMMENT '拜访间隔时间' ,
                                      `visit_flag` int(11) DEFAULT NULL COMMENT '拜访状态 0-签到 1-签退 2-作废',
                                      `visit_flag_val` VARCHAR(20) DEFAULT NULL COMMENT '拜访状态 对应值',
                                      `memo` varchar(255) DEFAULT NULL COMMENT '备注',
                                      `date_id`BIGINT(20)   COMMENT '日期ID' ,
                                      `insert_date_id`BIGINT(20)   COMMENT '插入时间id' ,
                                      PRIMARY KEY (colonel_visit_log_id)
)  COMMENT = '业务员域拜访事务事实表';

DROP TABLE IF EXISTS `zksr_report`.dwd_login_his_inc;
CREATE TABLE `zksr_report`.dwd_login_his_inc(
                                  `login_his_id` BIGINT(20) NOT NULL  COMMENT '登录历史Id' ,
                                  `create_time` DATETIME(3)   COMMENT '创建时间' ,
                                  `sys_code` BIGINT(20)   COMMENT '平台商id' ,
                                  `date_id` INT(11) DEFAULT NULL COMMENT '日期;yyyyMMdd',
                                  `wx_openid` VARCHAR(100) DEFAULT NULL COMMENT '微信openid;后台登录信息获取',
                                  `member_phone` VARCHAR(16) DEFAULT NULL COMMENT '用户手机号;后台登录信息获取',
                                  `member_username` VARCHAR(255) DEFAULT NULL COMMENT '用户名;后台登录信息获取',
                                  `member_id` BIGINT(20) DEFAULT NULL COMMENT '用户id;后台登录信息获取',
                                  `branch_id` BIGINT(20) DEFAULT NULL COMMENT '门店id;后台登录信息获取',
                                  `area_id` BIGINT(20)   COMMENT '区域城市id' ,
                                  `ip` VARCHAR(32) DEFAULT NULL COMMENT 'ip地址;http_request',
                                  `district` VARCHAR(32) DEFAULT NULL COMMENT 'ip地址归属地;http_request',
                                  `tp` VARCHAR(8) DEFAULT NULL COMMENT '类型（数据字典）;0-登陆  1-访问',
                                  `device_id` VARCHAR(128) DEFAULT NULL COMMENT '设备id;前端传（HttpHeader）',
                                  `device_type` VARCHAR(32) DEFAULT NULL COMMENT '设备类型;前端传（HttpHeader）',
                                  `device_brand` VARCHAR(32) DEFAULT NULL COMMENT '设备品牌;前端传（HttpHeader）',
                                  `device_model` VARCHAR(32) DEFAULT NULL COMMENT '设备型号;前端传（HttpHeader）',
                                  `os_name` VARCHAR(32) DEFAULT NULL COMMENT '系统名称;前端传（HttpHeader）',
                                  `os_version` VARCHAR(32) DEFAULT NULL COMMENT '操作系统版本;前端传（HttpHeader）',
                                  `port` VARCHAR(32) DEFAULT NULL COMMENT 'pc app xcx',
                                  `insert_date_id`BIGINT(20)   COMMENT '插入时间id' ,
                                  PRIMARY KEY (login_his_id)
)  COMMENT = '门店登录域事务事实表';