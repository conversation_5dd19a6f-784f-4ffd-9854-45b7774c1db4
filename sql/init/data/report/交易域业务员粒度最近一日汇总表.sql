-- 注意不能有行注释，报表平台会报错
-- 注意，配置大数据报表时不能有行尾注释
WITH OrderSummary AS (
    select
    o.sys_code,
        o.colonel_id,
        DATE(o.create_time) AS order_date,
        SUM(d.total_amt) AS total_order_amount,
        COUNT(DISTINCT so.supplier_order_id) AS order_count,
        COUNT(DISTINCT o.branch_id) AS store_count,
        COUNT(DISTINCT d.spu_id) AS sku_count,
        COUNT(DISTINCT p.catgory_id) AS category_count
    FROM
        zksr_trade.trd_order o
    JOIN
        zksr_trade.trd_supplier_order so ON o.order_id = so.order_id
    JOIN
        zksr_trade.trd_supplier_order_dtl d ON o.order_id = d.order_id
    JOIN
        zksr_product.prdt_spu p ON d.spu_id = p.spu_id
    WHERE
        so.pay_state != 2
        <if test="createTime !=null and createTime !=''">
		   AND DATE(o.create_time) = #{createTime}
		</if>
    GROUP BY
        o.sys_code, o.colonel_id, DATE(o.create_time)
),
AfterSalesSummary AS (
    select
    a.sys_code,
        a.colonel_id,
        DATE(a.create_time) AS after_sales_date,
        SUM(a.refund_amt) AS total_refund_amount,
        COUNT(DISTINCT a.after_id) AS after_sales_count
    FROM
        zksr_trade.trd_after a
    where 1=1
    	<if test="createTime !=null and createTime !=''">
		   AND DATE(a.create_time) = #{createTime}
		</if>

    GROUP BY
        a.sys_code, a.colonel_id, DATE(a.create_time)
),
CancelSummary AS (
    select
    o.sys_code,
        o.colonel_id,
        DATE(o.create_time) AS cancel_date,
        SUM(o.pay_amt) AS total_cancel_amount,
        COUNT(DISTINCT so.supplier_order_id) AS cancel_count
    FROM
        zksr_trade.trd_order o
    JOIN
        zksr_trade.trd_supplier_order so ON o.order_id = so.order_id
    WHERE
        so.pay_state = 2
        <if test="createTime !=null and createTime !=''">
		   AND DATE(o.create_time) = #{createTime}
		</if>

    GROUP BY
        o.sys_code, o.colonel_id, DATE(o.create_time)
)
select
os.sys_code,
    os.colonel_id ,
    os.total_order_amount,
    ROUND(os.total_order_amount / SUM(os.total_order_amount) OVER (), 2) AS contribution,
    os.order_count,
    COALESCE(aft.total_refund_amount, 0) AS total_refund_amount ,
    COALESCE(aft.after_sales_count, 0) AS after_sales_count,
    COALESCE(cs.total_cancel_amount, 0) AS total_cancel_amount,
    COALESCE(cs.cancel_count, 0) AS cancel_count,
    os.category_count AS category_count,
    os.sku_count AS sku_count,
    os.store_count AS store_count
FROM
    OrderSummary os
LEFT JOIN
    AfterSalesSummary aft ON os.colonel_id = aft.colonel_id  AND os.order_date = aft.after_sales_date
LEFT JOIN
    CancelSummary cs ON os.colonel_id = cs.colonel_id AND os.order_date = cs.cancel_date
where 1=1
<if test="sysCode !=null and sysCode !=''">
   and os.sys_code = #{sysCode}
</if>
<if test="colonelId !=null and colonelId !=''">
   and os.colonel_id = #{colonelId}
</if>
ORDER BY
 os.total_order_amount DESC
