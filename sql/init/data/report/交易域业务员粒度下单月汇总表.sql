-- 注意不能有行注释，报表平台会报错
SELECT
    o.sys_code,
    o.colonel_id,
    SUM(so.sub_pay_amt) AS y_amt,
    SUM(CASE WHEN MONTH(o.create_time) = 1 THEN so.sub_pay_amt ELSE 0 END) AS m_1,
    SUM(CASE WHEN MONTH(o.create_time) = 2 THEN so.sub_pay_amt ELSE 0 END) AS m_2,
    SUM(CASE WHEN MONTH(o.create_time) = 3 THEN so.sub_pay_amt ELSE 0 END) AS m_3,
    SUM(CASE WHEN MONTH(o.create_time) = 4 THEN so.sub_pay_amt ELSE 0 END) AS m_4,
    SUM(CASE WHEN MONTH(o.create_time) = 5 THEN so.sub_pay_amt ELSE 0 END) AS m_5,
    SUM(CASE WHEN MONTH(o.create_time) = 6 THEN so.sub_pay_amt ELSE 0 END) AS m_6,
    SUM(CASE WHEN MONTH(o.create_time) = 7 THEN so.sub_pay_amt ELSE 0 END) AS m_7,
    SUM(CASE WHEN MONTH(o.create_time) = 8 THEN so.sub_pay_amt ELSE 0 END) AS m_8,
    SUM(CASE WHEN MONTH(o.create_time) = 9 THEN so.sub_pay_amt ELSE 0 END) AS m_9,
    SUM(CASE WHEN MONTH(o.create_time) = 10 THEN so.sub_pay_amt ELSE 0 END) AS m_10,
    SUM(CASE WHEN MONTH(o.create_time) = 11 THEN so.sub_pay_amt ELSE 0 END) AS m_11,
    SUM(CASE WHEN MONTH(o.create_time) = 12 THEN so.sub_pay_amt ELSE 0 END) AS m_12
FROM
    trd_order o
JOIN
    trd_supplier_order so ON o.order_id = so.order_id
WHERE
    so.pay_state != 2
    <if test="createTime !=null and createTime !=''">
       AND YEAR(o.create_time) = #{createTime}
    </if>
    <if test="sysCode !=null and sysCode !=''">
       and o.sys_code = #{sysCode}
    </if>
    <if test="colonelId !=null and colonelId !=''">
       and o.colonel_id = #{colonelId}
    </if>
GROUP BY
    o.sys_code, o.colonel_id