-- 注意不能有行注释，报表平台会报错
SELECT
    tor.colonel_id,
    SUM(tso.sub_pay_amt) AS m_amt,
    SUM(CASE WHEN DAY(tor.create_time) = 1 THEN tso.sub_pay_amt ELSE 0 END) AS d_1,
    SUM(CASE WHEN DAY(tor.create_time) = 2 THEN tso.sub_pay_amt ELSE 0 END) AS d_2,
    SUM(CASE WHEN DAY(tor.create_time) = 3 THEN tso.sub_pay_amt ELSE 0 END) AS d_3,
    SUM(CASE WHEN DAY(tor.create_time) = 4 THEN tso.sub_pay_amt ELSE 0 END) AS d_4,
    SUM(CASE WHEN DAY(tor.create_time) = 5 THEN tso.sub_pay_amt ELSE 0 END) AS d_5,
    SUM(CASE WHEN DAY(tor.create_time) = 6 THEN tso.sub_pay_amt ELSE 0 END) AS d_6,
    SUM(CASE WHEN DAY(tor.create_time) = 7 THEN tso.sub_pay_amt ELSE 0 END) AS d_7,
    SUM(CASE WHEN DAY(tor.create_time) = 8 THEN tso.sub_pay_amt ELSE 0 END) AS d_8,
    SUM(CASE WHEN DAY(tor.create_time) = 9 THEN tso.sub_pay_amt ELSE 0 END) AS d_9,
    SUM(CASE WHEN DAY(tor.create_time) = 10 THEN tso.sub_pay_amt ELSE 0 END) AS d_10,
    SUM(CASE WHEN DAY(tor.create_time) = 11 THEN tso.sub_pay_amt ELSE 0 END) AS d_11,
    SUM(CASE WHEN DAY(tor.create_time) = 12 THEN tso.sub_pay_amt ELSE 0 END) AS d_12,
    SUM(CASE WHEN DAY(tor.create_time) = 13 THEN tso.sub_pay_amt ELSE 0 END) AS d_13,
    SUM(CASE WHEN DAY(tor.create_time) = 14 THEN tso.sub_pay_amt ELSE 0 END) AS d_14,
    SUM(CASE WHEN DAY(tor.create_time) = 15 THEN tso.sub_pay_amt ELSE 0 END) AS d_15,
    SUM(CASE WHEN DAY(tor.create_time) = 16 THEN tso.sub_pay_amt ELSE 0 END) AS d_16,
    SUM(CASE WHEN DAY(tor.create_time) = 17 THEN tso.sub_pay_amt ELSE 0 END) AS d_17,
    SUM(CASE WHEN DAY(tor.create_time) = 18 THEN tso.sub_pay_amt ELSE 0 END) AS d_18,
    SUM(CASE WHEN DAY(tor.create_time) = 19 THEN tso.sub_pay_amt ELSE 0 END) AS d_19,
    SUM(CASE WHEN DAY(tor.create_time) = 20 THEN tso.sub_pay_amt ELSE 0 END) AS d_20,
    SUM(CASE WHEN DAY(tor.create_time) = 21 THEN tso.sub_pay_amt ELSE 0 END) AS d_21,
    SUM(CASE WHEN DAY(tor.create_time) = 22 THEN tso.sub_pay_amt ELSE 0 END) AS d_22,
    SUM(CASE WHEN DAY(tor.create_time) = 23 THEN tso.sub_pay_amt ELSE 0 END) AS d_23,
    SUM(CASE WHEN DAY(tor.create_time) = 24 THEN tso.sub_pay_amt ELSE 0 END) AS d_24,
    SUM(CASE WHEN DAY(tor.create_time) = 25 THEN tso.sub_pay_amt ELSE 0 END) AS d_25,
    SUM(CASE WHEN DAY(tor.create_time) = 26 THEN tso.sub_pay_amt ELSE 0 END) AS d_26,
    SUM(CASE WHEN DAY(tor.create_time) = 27 THEN tso.sub_pay_amt ELSE 0 END) AS d_27,
    SUM(CASE WHEN DAY(tor.create_time) = 28 THEN tso.sub_pay_amt ELSE 0 END) AS d_28,
    SUM(CASE WHEN DAY(tor.create_time) = 29 THEN tso.sub_pay_amt ELSE 0 END) AS d_29,
    SUM(CASE WHEN DAY(tor.create_time) = 30 THEN tso.sub_pay_amt ELSE 0 END) AS d_30,
    SUM(CASE WHEN DAY(tor.create_time) = 31 THEN tso.sub_pay_amt ELSE 0 END) AS d_31
FROM
    trd_order tor
    INNER JOIN trd_supplier_order tso ON tor.order_id = tso.order_id
WHERE
    tso.pay_state != 2
    <if test="createTime !=null and createTime !=''">
       AND DATE_FORMAT(tor.create_time, '%Y-%m') = #{createTime}
    </if>
    <if test="colonelId !=null and colonelId !=''">
       AND tor.colonel_id = #{colonelId}
    </if>
    <if test="sysCode !=null and sysCode !=''">
       and tor.sys_code = #{sysCode}
    </if>
GROUP BY
    tor.sys_code, tor.colonel_id
ORDER BY
    m_amt DESC