{
    "sheetNo": "${supplierAfterNo}",
    "otherSheetNo": "${supplierSourceNo}",
    "consumerNo": "${branchNo}",
    "transNo": "XT",
    "sheetDate": "${createTimeString}",
    "refundStyle": "${payWay}",
    "senderName": "${branchName}",
    "senderTel": "${contactPhone}",
    "senderMobile": "${contactPhone}",
    "senderDetailAddr": "${branchAddr}",
    "subList": [
        #foreach( $sub in $detailList)
        {
            #set($qty = $sub.orderUnitQty)
            #set($size = $sub.orderUnitSize)
            #set($price = $sub.exactReturnPrice)
            #set($itemPrice = $size * $price)
            #set($minDetailQty = $qty * $size)
            "itemNo": "${sub.itemSourceNo}",
            "detailQty": ${sub.returnQty},
            "remake": "${sub.reason}",
            "externalLineNo": ${sub.lineNum},
            "itemPrice": ${itemPrice},
            #if($sub.orderUnitType == 1)
            "packageType": "0",
            #elseif($sub.orderUnitType == 2)
            "packageType": "1",
            #else
            "packageType": "2",
            #end
            "realQty": ${qty},
            "minDetailQty": ${minDetailQty},
            "detailPrice": ${sub.returnAmt},
            "itemType": "${sub.giftFlag}",
            "itemBarcode": "${sub.itemBarcode}",
            #if("$!{sub.expressNo}")
            "expressNo": "${sub.expressNo}",
            #else
            "expressNo": "",
            #end
            #if("$!{sub.expressCom}")
            "expressCom": "${sub.expressCom}"
            #else
            "expressCom": ""
            #end
        }#if($foreach.hasNext),#end
        #end
    ]
}