{
  #set($firstItem = $detailList.get(0))
    "companyName": "${branchName}",
    "companyNo": "${branchNo}",
    "companyType": "1",
    "receiptType": 0,
    "sheetDate": "${firstItem.sheetDateString}",
    "sheetType": 0,
  #if($firstItem.sheetType == "XSS")
    "sourceNo": "${firstItem.sourceOrderNo}",
    "changeType": "+",
  #elseif($firstItem.sheetType == "SHJ")
    "sourceNo": "${firstItem.sourceOrderNo}",
    "changeType": "-",
  #elseif($firstItem.sheetType == "SHC")
    "sourceNo": "${firstItem.sourceOrderNo}",
    "changeType": "-",
  #elseif($firstItem.sheetType == "SHS")
    "sourceNo": "${firstItem.sourceAfterNo}",
    "changeType": "-",
    #else
    "sourceNo": "",
    "changeType": "",
    #end
     "subList": [
      {
        "accountNo": "06",
        "sheetAmt": ${totalReceiptAmt}
      }
     ]
}