
update prdt_spu set is_delete = 1 where spu_no = 'SPY000001' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00548' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00549' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00550' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00551' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00552' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00553' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00554' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00555' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00556' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00557' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00558' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00559' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00560' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00561' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00562' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00563' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00564' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00565' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00566' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00567' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00568' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00569' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00570' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00571' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00572' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00573' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00574' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00575' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00576' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00577' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00578' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00579' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00580' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00581' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00582' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00583' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00584' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00585' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00586' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00587' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00588' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00589' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00590' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00591' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00592' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00593' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00594' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00595' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00596' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00597' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00598' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00599' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00600' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00601' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00602' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00603' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00604' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00605' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00606' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00607' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00608' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00609' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00610' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00611' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00612' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00613' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00614' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00615' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00616' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00617' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00618' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00619' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00620' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00621' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00622' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00623' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00624' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00625' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00626' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00627' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00628' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00629' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00630' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00631' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00632' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00633' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00634' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00635' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00636' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00637' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00638' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00639' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00640' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00641' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00642' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00643' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00644' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00645' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00646' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00647' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00648' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00649' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00650' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00651' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00652' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00653' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00654' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00655' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00656' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00657' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00658' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00659' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00660' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00661' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00662' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00663' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00664' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00665' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00666' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00667' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00668' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00669' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00670' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00671' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00672' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00673' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00674' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00675' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00676' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00677' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00678' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00679' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00680' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00681' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00682' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00683' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00684' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00685' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00686' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00687' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00688' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00689' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00690' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00691' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00692' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00693' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00694' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00695' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00696' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00697' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00698' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00094' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00093' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00092' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00091' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00090' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00089' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00088' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00087' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00086' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00085' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00084' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00083' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00082' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00081' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00080' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00079' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00078' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00077' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00076' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00075' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00074' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00073' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00072' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00071' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00070' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00069' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00068' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00067' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00066' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00065' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00064' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00063' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00062' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00061' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00060' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00059' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00058' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00057' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00056' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00055' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00054' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00053' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00052' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00051' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00050' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00049' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00048' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00047' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00046' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00045' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00044' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00043' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00042' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00041' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00040' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00039' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00038' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00037' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00036' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00035' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00034' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00033' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00032' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00031' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00030' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00029' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00028' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00027' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00026' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00025' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00024' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00023' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00022' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00021' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00020' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00019' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00018' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00017' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00016' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00015' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00014' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00013' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00012' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00011' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00010' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00009' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00008' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00007' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00006' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00005' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00004' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00003' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00002' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00001' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00525' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00524' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00523' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00511' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00510' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00509' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00508' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00507' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00506' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00505' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00504' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00547' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00546' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00545' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00544' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00543' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00542' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00541' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00540' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00539' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00538' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00537' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00536' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00535' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00534' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00533' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00532' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00531' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00530' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00529' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00528' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00527' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00526' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00522' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00521' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00520' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00519' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00518' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00517' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00516' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00515' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00514' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00513' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00512' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00503' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00502' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00501' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00500' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00499' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00498' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00497' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00496' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00495' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00494' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00493' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00492' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00491' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00490' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00489' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00488' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00487' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00486' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00485' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00484' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00483' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00482' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00481' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00480' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00479' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00478' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00477' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00476' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00475' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00474' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00473' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00472' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00471' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00470' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00469' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00468' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00467' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00466' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00465' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00464' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00463' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00462' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00461' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00460' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00459' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00458' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00457' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00456' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00455' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00454' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00453' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00452' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00451' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00450' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00449' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00448' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00447' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00446' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00445' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00444' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00443' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00442' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00441' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00440' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00439' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00438' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00437' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00436' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00435' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00434' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00433' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00432' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00431' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00430' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00429' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00428' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00427' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00426' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00425' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00424' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00423' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00422' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00421' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00420' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00419' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00418' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00417' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00416' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00415' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00414' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00413' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00412' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00411' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00410' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00409' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00408' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00407' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00406' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00405' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00404' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00403' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00402' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00401' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00400' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00399' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00398' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00397' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00396' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00395' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00394' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00393' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00392' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00391' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00390' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00389' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00388' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00387' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00386' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00385' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00384' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00383' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00382' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00381' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00380' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00379' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00378' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00377' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00376' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00375' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00374' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00373' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00372' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00371' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00370' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00369' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00368' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00367' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00366' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00365' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00364' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00363' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00362' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00361' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00360' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00359' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00358' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00357' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00356' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00355' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00354' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00353' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00352' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00351' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00350' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00349' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00348' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00347' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00346' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00345' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00344' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00343' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00342' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00341' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00340' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00339' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00338' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00337' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00336' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00335' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00334' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00333' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00332' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00331' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00330' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00329' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00328' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00327' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00326' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00325' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00324' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00323' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00322' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00321' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00320' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00319' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00318' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00317' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00316' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00315' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00314' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00313' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00312' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00311' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00310' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00309' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00308' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00307' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00306' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00305' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00304' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00303' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00302' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00301' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00300' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00299' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00298' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00297' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00296' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00295' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00294' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00293' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00292' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00291' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00290' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00289' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00288' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00287' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00286' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00285' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00284' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00283' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00282' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00281' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00280' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00279' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00278' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00277' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00276' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00275' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00274' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00273' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00272' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00271' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00270' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00269' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00268' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00267' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00266' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00265' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00264' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00263' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00262' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00261' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00260' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00259' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00258' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00257' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00256' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00255' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00254' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00253' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00252' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00251' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00250' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00249' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00248' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00247' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00246' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00245' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00244' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00243' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00242' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00241' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00240' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00239' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00238' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00237' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00236' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00235' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00234' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00233' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00232' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00231' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00230' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00229' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00228' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00227' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00226' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00225' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00224' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00223' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00222' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00221' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00220' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00219' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00218' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00217' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00216' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00215' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00214' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00213' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00212' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00211' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00210' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00209' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00208' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00207' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00206' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00205' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00204' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00203' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00202' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00201' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00200' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00199' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00198' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00197' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00196' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00195' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00194' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00193' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00192' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00191' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00190' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00189' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00188' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00187' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00186' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00185' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00184' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00183' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00182' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00181' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00180' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00179' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00178' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00177' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00176' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00175' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00174' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00173' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00172' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00171' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00170' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00169' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00168' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00167' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00166' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00165' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00164' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00163' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00162' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00161' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00160' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00159' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00158' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00157' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00156' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00155' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00154' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00153' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00152' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00151' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00150' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00149' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00148' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00147' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00146' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00145' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00144' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00143' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00142' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00141' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00140' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00139' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00138' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00137' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00136' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00135' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00134' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00133' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00132' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00131' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00130' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00129' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00128' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00127' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00126' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00125' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00124' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00123' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00122' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00121' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00120' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00119' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00118' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00117' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00116' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00115' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00114' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00113' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00112' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00111' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00110' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00109' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00108' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00107' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00106' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00105' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00104' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00103' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00102' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00101' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00100' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00099' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00098' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00097' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00096' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPW00095' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000001' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000002' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000003' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000004' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000005' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000006' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000007' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000008' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000009' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000010' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000011' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000012' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000013' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000014' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000015' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000016' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000017' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000018' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000019' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000020' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000021' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000022' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000023' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000024' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000025' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000026' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000027' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000028' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000029' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000030' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000031' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000032' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000033' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000034' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000035' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000036' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000037' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000038' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000039' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000040' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000041' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000042' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000043' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000044' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000045' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000046' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000047' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000048' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000049' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000050' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000051' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000052' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000053' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000054' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000055' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000056' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000057' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000058' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000059' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000060' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000061' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000062' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000063' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000064' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000065' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000066' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000067' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000068' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000069' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000070' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000071' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000072' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000073' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000074' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000075' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000076' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000077' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000078' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000079' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000080' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000081' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000082' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000083' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000084' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000085' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000086' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000087' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000088' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000089' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000090' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000091' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000092' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000093' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000094' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000095' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000096' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000097' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000098' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000099' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000100' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000101' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000102' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000103' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000104' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000105' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000106' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000107' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000108' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000109' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000110' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000111' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000112' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000113' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000114' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000115' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000116' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000117' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000118' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000119' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000120' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000121' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000122' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000123' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000124' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000125' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000126' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000127' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000128' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000129' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000130' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000131' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000132' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000133' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000134' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000135' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000136' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000137' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000138' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000139' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000140' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000141' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000142' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000143' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000144' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000145' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000146' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000147' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000148' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000149' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000150' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000151' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000152' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000153' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000154' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000155' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000156' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000157' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000158' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000159' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000160' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000161' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000162' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000163' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000164' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000165' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000166' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000167' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000168' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000169' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000170' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000171' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000172' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000173' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000174' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000175' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000176' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000177' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000178' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000179' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000180' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000181' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000182' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000183' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000184' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000185' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000186' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000187' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000188' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000189' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000190' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000191' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000192' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000193' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000194' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000195' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000196' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000197' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000198' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000199' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000200' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000201' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000202' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000203' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000204' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000205' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000206' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000207' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000208' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000209' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000210' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000211' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000212' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000213' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000214' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000215' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000216' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000217' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000218' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000219' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000220' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000221' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000222' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000223' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000224' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000225' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000226' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000227' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000228' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000229' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000230' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000231' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000232' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000233' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000234' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000235' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000236' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000237' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000238' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000239' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000240' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000241' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000242' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000243' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000244' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000245' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000246' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000247' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000248' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000249' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000250' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000251' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000252' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000253' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000254' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000255' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000256' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000257' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000258' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000259' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000260' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000261' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000262' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000263' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000264' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000265' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000266' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000267' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000268' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000269' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000270' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000271' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000272' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000273' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000274' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000275' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000276' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000277' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000278' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000279' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000280' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000281' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000282' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000283' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000284' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000285' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000286' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000287' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000288' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000289' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000290' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000291' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000292' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000293' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000294' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000295' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000296' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000297' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000298' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000299' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000300' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000301' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000302' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000303' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000304' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000305' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000306' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000307' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000308' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000309' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000310' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000311' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000312' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000313' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000314' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000315' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000316' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000317' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000318' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000319' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000320' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000321' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000322' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000323' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000324' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000325' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000326' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000327' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000328' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000329' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000330' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000331' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000332' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000333' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000334' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000335' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000336' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000337' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000338' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000339' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000340' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000341' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000342' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000343' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000344' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000345' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000346' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000347' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000348' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000349' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000350' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000351' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000352' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000353' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000354' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000355' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000356' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000357' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000358' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000359' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000360' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000361' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000362' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000363' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000364' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000365' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000366' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000367' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000368' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000369' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000370' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000371' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000372' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000373' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000374' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000375' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000376' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000377' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000378' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000379' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000380' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000381' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000382' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000383' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000384' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000385' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000386' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000387' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000388' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000389' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000390' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000391' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000392' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000393' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000394' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000395' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000396' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000397' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000398' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000399' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000400' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000401' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000402' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000403' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000404' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000405' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000406' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000407' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000408' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000409' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000410' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000411' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000412' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000413' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000414' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000415' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000416' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000417' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000418' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000419' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000420' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000421' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000422' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000423' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000424' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000425' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000426' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000427' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000428' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000429' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000430' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000431' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000432' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000433' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000434' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000435' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000436' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000437' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000438' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000439' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000440' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000441' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000442' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000443' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000444' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000445' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000446' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000447' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000448' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000449' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000450' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000451' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000452' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000453' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000454' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000455' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000456' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000457' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000458' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000459' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000460' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000461' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000462' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000463' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000464' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000465' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000466' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000467' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000468' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000469' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000470' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000471' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000472' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000473' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000474' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000475' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000476' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000477' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000478' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000479' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000480' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000481' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000482' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000483' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000484' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000485' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000486' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000487' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000488' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000489' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000490' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000491' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000492' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000493' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000494' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000495' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000496' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000497' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000498' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000499' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000500' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000501' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000502' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000503' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000504' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000505' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000506' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000507' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000508' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000509' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000510' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000511' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000512' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000513' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000514' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000515' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000516' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000517' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000518' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000519' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000520' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000521' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000522' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000523' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000524' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000525' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000526' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000527' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000528' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000529' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000530' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000531' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000532' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000533' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000534' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000535' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000536' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000537' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000538' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000539' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000540' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000541' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000542' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000543' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000544' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000545' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000546' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000547' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000548' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000549' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000550' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000551' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000552' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000553' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000554' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000555' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000556' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000557' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000558' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000559' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000560' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000561' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000562' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000563' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000564' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000565' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000566' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000567' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000568' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000569' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000570' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000571' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000572' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000573' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000574' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000575' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000576' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000577' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000578' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000579' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000580' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000581' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000582' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000583' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000584' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000585' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000586' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000587' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000588' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000589' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000590' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000591' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000592' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000593' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000594' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000595' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000596' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000597' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000598' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000599' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000600' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000601' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000602' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000603' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000604' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000605' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000606' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000607' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000608' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000609' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000610' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000611' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000612' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000613' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000614' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000615' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000616' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000617' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000618' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000619' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000620' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000621' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000622' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000623' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000624' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000625' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000626' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000627' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000628' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000629' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000630' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000631' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000632' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000633' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000634' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000635' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000636' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000637' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000638' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000639' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000640' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000641' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000642' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000643' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000644' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000645' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000646' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000647' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000648' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000649' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000650' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000651' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000652' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000653' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000654' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000655' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000656' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000657' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000658' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000659' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000660' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000661' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000662' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000663' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000664' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000665' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000666' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000667' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000668' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000669' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000670' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000671' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000672' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000673' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000674' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000675' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000676' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000677' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000678' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000679' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000680' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000681' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000682' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000683' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000684' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000685' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000686' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000687' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000688' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000689' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000690' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000691' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000692' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000693' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000694' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000695' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000696' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000697' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000698' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000699' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000700' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000701' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000702' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000703' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000704' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000705' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000706' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000707' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000708' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000709' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000710' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000711' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000712' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000713' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000714' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000715' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000716' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000717' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000718' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000719' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000720' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000721' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000722' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000723' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000724' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000725' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000726' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000727' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000728' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000729' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000730' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000731' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000732' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000733' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000734' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000735' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000736' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000737' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000738' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000739' and sys_code = 610330619602468864 and supplier_id <> 未找到;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000740' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000741' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000742' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000743' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000744' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000745' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000746' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000747' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000748' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000749' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000750' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000751' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000752' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000753' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000754' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000755' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000756' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000757' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPS000758' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000682' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000683' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000684' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000685' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000686' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000687' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000688' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000689' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000690' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000691' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000692' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000693' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000694' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000695' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000696' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000697' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000698' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000699' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000700' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000701' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000702' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000703' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000704' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000705' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000706' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000707' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000708' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000709' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000710' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000711' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000712' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000713' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000714' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000715' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000716' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000717' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000718' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000719' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000720' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000721' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000722' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000723' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000724' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000725' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000726' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000727' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000728' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000729' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000730' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000731' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000732' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000733' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000734' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000735' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000736' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000737' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000738' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000739' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000740' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000741' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000742' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000743' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000744' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000745' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000746' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000747' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000748' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000749' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000750' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000751' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000752' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000753' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000754' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000755' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000756' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000757' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000758' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000759' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000760' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000761' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000762' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000763' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000764' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000765' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000766' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000767' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000768' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000769' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000770' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000771' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000772' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000773' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000774' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000775' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000776' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000777' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000778' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000779' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000780' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000781' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000782' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000783' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000784' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000785' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000786' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000787' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000788' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000789' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000790' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000791' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000792' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000793' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000794' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000795' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000796' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000797' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000798' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000799' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000800' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000801' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000802' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000803' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000804' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000805' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000806' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000807' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000808' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000809' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000810' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000811' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000812' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000813' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000814' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000815' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000816' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000817' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000818' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000819' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000820' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000821' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000822' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000823' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000824' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000825' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000826' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000827' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000828' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000829' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000830' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000831' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000832' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000833' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000834' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000835' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000836' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000837' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000838' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000839' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000840' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000841' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000842' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000843' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000844' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000845' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000846' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000847' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000848' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000849' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000850' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000851' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000852' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000853' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000854' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000855' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000856' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000857' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000858' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000859' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000860' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000861' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000862' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000863' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000864' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000865' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000866' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000867' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000868' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000869' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000870' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000871' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000872' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000873' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000874' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000875' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000876' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000877' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000878' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000879' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000880' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000881' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000882' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000883' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000884' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000885' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000886' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000887' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000888' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000889' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000890' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000891' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000892' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000893' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000894' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000895' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000896' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000897' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000898' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000899' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000900' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000901' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000902' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000903' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000904' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000905' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000906' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000907' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000908' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000909' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000910' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000911' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000912' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000913' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000914' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000915' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000916' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000917' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000918' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000919' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000920' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000921' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000922' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000923' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000924' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000925' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000926' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000927' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000928' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000929' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000930' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000931' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000932' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000933' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000934' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000935' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000936' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000937' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000938' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000939' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000940' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000941' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000942' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000943' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000944' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000945' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000946' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000947' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000948' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000949' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000950' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000951' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000952' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000953' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000954' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000955' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000956' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000957' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000958' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000959' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000960' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000961' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000962' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000963' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000964' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000965' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000966' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000967' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000968' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000969' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000970' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000971' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000972' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000973' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000974' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000975' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000976' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000977' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000978' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000979' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000980' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000981' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000982' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000983' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000984' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000985' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000986' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000987' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000988' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000989' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000990' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000991' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000992' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000993' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000994' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000995' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000996' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000997' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000998' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000999' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001000' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001001' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001002' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001003' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001004' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001005' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001006' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001007' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001008' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001009' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001010' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001011' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001012' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001013' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001014' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001015' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001016' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001017' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001018' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001019' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001020' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001021' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001022' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001023' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001024' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001025' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001026' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001027' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001028' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001029' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001030' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001031' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001032' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001033' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001034' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001035' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001036' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001037' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001038' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001039' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001040' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001041' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001042' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001043' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001044' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001045' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001046' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001047' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001048' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001049' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001050' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001051' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001052' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001053' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001054' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001055' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001056' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001057' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001058' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001059' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001060' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001061' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001062' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001063' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001064' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001065' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001066' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001067' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001068' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001069' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001070' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001071' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001072' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001073' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001074' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001075' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001076' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001077' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001078' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001079' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001080' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001081' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001082' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001083' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001084' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001085' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001086' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001087' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001088' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001089' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001090' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001091' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001092' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001093' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001094' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001095' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001096' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001097' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001098' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001099' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001100' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001101' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001102' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001103' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001104' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001105' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001106' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001107' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001108' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001109' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001110' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001111' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001112' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001113' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001114' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001115' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001116' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001117' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001118' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001119' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001120' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001121' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001122' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001123' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001124' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001125' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001126' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001127' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001128' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001129' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001130' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001131' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001132' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001133' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001134' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001135' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001136' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001137' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001138' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001139' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001140' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001141' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001142' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001143' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001144' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001145' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001146' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001147' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001148' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001149' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001150' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001151' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001152' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001153' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001154' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001155' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001156' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001157' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001158' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001159' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001160' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001161' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001162' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001163' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001164' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001165' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001166' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001167' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001168' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001169' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001170' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001171' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001172' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001173' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001174' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001175' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001176' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001177' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001178' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001179' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001180' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001181' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001182' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001183' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001184' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001185' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001186' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001187' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001188' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001189' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001190' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001191' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001192' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001193' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001194' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001195' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001196' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001197' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001198' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001199' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001200' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001201' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001202' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001203' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001204' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001205' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001206' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001207' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001208' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001209' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001210' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001211' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001212' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001213' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001214' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001215' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001216' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001217' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001218' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001219' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001220' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001221' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001222' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001223' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001224' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001225' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001226' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001227' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001228' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001229' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001230' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001231' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001232' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001233' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001234' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001235' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001236' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001237' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001238' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001239' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001240' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001241' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001242' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001243' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001244' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001245' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001246' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001247' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001248' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001249' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001250' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001251' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001252' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001253' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001254' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001255' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001256' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001257' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001258' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001259' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001260' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001261' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001262' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001263' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001264' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001265' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001266' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001267' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001268' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001269' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001270' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001271' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001272' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001273' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001274' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001275' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001276' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001277' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001278' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001279' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001280' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001281' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001282' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001283' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001284' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001285' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001286' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001287' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001288' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001289' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001290' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001291' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001292' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001293' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001294' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001295' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001296' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001297' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001298' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001299' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001300' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001301' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001302' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001303' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001304' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001305' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001306' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001307' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001308' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001309' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001310' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001311' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001312' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001313' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001314' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001315' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001316' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001317' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001318' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001319' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001320' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001321' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001322' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001323' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001324' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001325' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001326' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001327' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001328' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001329' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001330' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001331' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001332' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001333' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001334' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001335' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001336' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001337' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001338' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001339' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001340' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001341' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001342' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL001343' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000001' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000002' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000003' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000004' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000005' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000006' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000007' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000008' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000009' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000010' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000011' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000012' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000013' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000014' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000015' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000016' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000017' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000018' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000019' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000020' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000021' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000022' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000023' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000024' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000025' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000026' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000027' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000028' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000029' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000030' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000031' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000032' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000033' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000034' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000035' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000036' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000037' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000038' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000039' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000040' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000041' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000042' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000043' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000044' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000045' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000046' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000047' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000048' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000049' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000050' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000051' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000052' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000053' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000054' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000055' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000056' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000057' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000058' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000059' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000060' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000061' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000062' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000063' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000064' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000065' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000066' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000067' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000068' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000069' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000070' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000071' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000072' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000073' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000074' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000075' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000076' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000077' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000078' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000079' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000080' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000081' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000082' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000083' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000084' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000085' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000086' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000087' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000088' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000089' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000090' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000091' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000092' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000093' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000094' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000095' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000096' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000097' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000098' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000099' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000100' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000101' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000102' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000103' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000104' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000105' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000106' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000107' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000108' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000109' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000110' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000111' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000112' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000113' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000114' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000115' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000116' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000117' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000118' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000119' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000120' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000121' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000122' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000123' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000124' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000125' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000126' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000127' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000128' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000129' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000130' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000131' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000132' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000133' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000134' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000135' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000136' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000137' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000138' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000139' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000140' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000141' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000142' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000143' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000144' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000145' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000146' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000147' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000148' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000149' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000150' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000151' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000152' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000153' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000154' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000155' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000156' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000157' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000158' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000159' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000160' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000161' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000162' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000163' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000164' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000165' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000166' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000167' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000168' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000169' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000170' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000171' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000172' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000173' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000174' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000175' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000176' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000177' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000178' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000179' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000180' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000181' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000182' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000183' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000184' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000185' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000186' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000187' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000188' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000189' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000190' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000191' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000192' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000193' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000194' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000195' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000196' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000197' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000198' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000199' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000200' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000201' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000202' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000203' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000204' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000205' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000206' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000207' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000208' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000209' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000210' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000211' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000212' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000213' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000214' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000215' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000216' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000217' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000218' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000219' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000220' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000221' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000222' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000223' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000224' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000225' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000226' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000227' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000228' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000229' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000230' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000231' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000232' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000233' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000234' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000235' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000236' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000237' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000238' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000239' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000240' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000241' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000242' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000243' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000244' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000245' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000246' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000247' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000248' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000249' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000250' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000251' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000252' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000253' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000254' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000255' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000256' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000257' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000258' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000259' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000260' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000261' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000262' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000263' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000264' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000265' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000266' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000267' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000268' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000269' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000270' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000271' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000272' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000273' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000274' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000275' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000276' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000277' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000278' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000279' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000280' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000281' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000282' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000283' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000284' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000285' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000286' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000287' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000288' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000289' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000290' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000291' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000292' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000293' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000294' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000295' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000296' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000297' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000298' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000299' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000300' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000301' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000302' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000303' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000304' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000305' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000306' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000307' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000308' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000309' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000310' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000311' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000312' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000313' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000314' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000315' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000316' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000317' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000318' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000319' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000320' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000321' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000322' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000323' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000324' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000325' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000326' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000327' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000328' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000329' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000330' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000331' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000332' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000333' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000334' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000335' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000336' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000337' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000338' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000339' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000340' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000341' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000342' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000343' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000344' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000345' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000346' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000347' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000348' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000349' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000350' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000351' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000352' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000353' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000354' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000355' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000356' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000357' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000358' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000359' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000360' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000361' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000362' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000363' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000364' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000365' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000366' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000367' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000368' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000369' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000370' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000371' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000372' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000373' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000374' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000375' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000376' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000377' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000378' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000379' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000380' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000381' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000382' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000383' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000384' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000385' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000386' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000387' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000388' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000389' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000390' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000391' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000392' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000393' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000394' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000395' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000396' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000397' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000398' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000399' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000400' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000401' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000402' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000403' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000404' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000405' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000406' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000407' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000408' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000409' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000410' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000411' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000412' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000413' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000414' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000415' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000416' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000417' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000418' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000419' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000420' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000421' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000422' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000423' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000424' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000425' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000426' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000427' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000428' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000429' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000430' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000431' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000432' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000433' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000434' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000435' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000436' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000437' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000438' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000439' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000440' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000441' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000442' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000443' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000444' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000445' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000446' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000447' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000448' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000449' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000450' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000451' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000452' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000453' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000454' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000455' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000456' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000457' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000458' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000459' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000460' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000461' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000462' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000463' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000464' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000465' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000466' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000467' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000468' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000469' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000470' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000471' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000472' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000473' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000474' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000475' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000476' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000477' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000478' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000479' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000480' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000481' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000482' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000483' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000484' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000485' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000486' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000487' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000488' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000489' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000490' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000491' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000492' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000493' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000494' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000495' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000496' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000497' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000498' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000499' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000500' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000501' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000502' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000503' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000504' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000505' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000506' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000507' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000508' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000509' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000510' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000511' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000512' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000513' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000514' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000515' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000516' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000517' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000518' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000519' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000520' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000521' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000522' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000523' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000524' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000525' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000526' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000527' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000528' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000529' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000530' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000531' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000532' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000533' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000534' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000535' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000536' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000537' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000538' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000539' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000540' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000541' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000542' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000543' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000544' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000545' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000546' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000547' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000548' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000549' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000550' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000551' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000552' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000553' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000554' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000555' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000556' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000557' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000558' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000559' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000560' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000561' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000562' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000563' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000564' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000565' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000566' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000567' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000568' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000569' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000570' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000571' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000572' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000573' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000574' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000575' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000576' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000577' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000578' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000579' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000580' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000581' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000582' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000583' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000584' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000585' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000586' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000587' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000588' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000589' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000590' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000591' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000592' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000593' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000594' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000595' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000596' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000597' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000598' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000599' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000600' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000601' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000602' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000603' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000604' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000605' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000606' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000607' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000608' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000609' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000610' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000611' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000612' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000613' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000614' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000615' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000616' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000617' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000618' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000619' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000620' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000621' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000622' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000623' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000624' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000625' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000626' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000627' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000628' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000629' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000630' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000631' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000632' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000633' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000634' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000635' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000636' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000637' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000638' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000639' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000640' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000641' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000642' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000643' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000644' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000645' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000646' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000647' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000648' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000649' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000650' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000651' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000652' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000653' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000654' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000655' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000656' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000657' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000658' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000659' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000660' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000661' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000662' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000663' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000664' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000665' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000666' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000667' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000668' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000669' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000670' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000671' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000672' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000673' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000674' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000675' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000676' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000677' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000678' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000679' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000680' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SPL000681' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SP00013' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SP00014' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SP00015' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SP00016' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SP00017' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SP00018' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SP00019' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SP00020' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SP00021' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SP00022' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SP00012' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SP00011' and sys_code = 610330619602468864 and supplier_id <> 612919801982156800;
update prdt_spu set is_delete = 1 where spu_no = 'SP00010' and sys_code = 610330619602468864 and supplier_id <> 612935482907754496;
update prdt_spu set is_delete = 1 where spu_no = 'SP00009' and sys_code = 610330619602468864 and supplier_id <> 612917809117331456;
update prdt_spu set is_delete = 1 where spu_no = 'SP00008' and sys_code = 610330619602468864 and supplier_id <> 612918741125234688;
update prdt_spu set is_delete = 1 where spu_no = 'SP00007' and sys_code = 610330619602468864 and supplier_id <> 612920270133592064;
update prdt_spu set is_delete = 1 where spu_no = 'SP00006' and sys_code = 610330619602468864 and supplier_id <> 612920270133592064;
update prdt_spu set is_delete = 1 where spu_no = 'SP00005' and sys_code = 610330619602468864 and supplier_id <> 612920270133592064;
