-- 删除环境持有菜单
DROP TABLE `sys_menu`;
CREATE TABLE `sys_menu` (
                            `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
                            `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
                            `parent_id` bigint(20) DEFAULT '0' COMMENT '父菜单ID',
                            `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
                            `menu_code` varchar(32) NOT NULL COMMENT '菜单编号',
                            `menu_pcode` varchar(32) NOT NULL COMMENT '菜单父级编号',
                            `path` varchar(200) DEFAULT '' COMMENT '路由地址',
                            `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
                            `query` varchar(255) DEFAULT NULL COMMENT '路由参数',
                            `is_frame` int(1) DEFAULT '1' COMMENT '是否为外链（0是 1否）',
                            `is_cache` int(1) DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
                            `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
                            `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
                            `status` char(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
                            `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
                            `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
                            `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) DEFAULT '' COMMENT '备注',
                            `func_scop` varchar(64) NOT NULL COMMENT '用来区分 是软件商(software)，平台商(platform)，运营商(partner)，入驻商(supplier)) ，新增和修改的时候做判断func_scop是否相等，不相等则允许做分配',
                            PRIMARY KEY (`menu_id`) USING BTREE,
                            UNIQUE KEY `idx_menu_code` (`menu_code`)
) ENGINE=InnoDB AUTO_INCREMENT=2656 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='菜单权限表';

-- 删除角色菜单关联关系
DELETE FROM `sys_role_menu`;

-- 插入生产环境菜单
-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2657, '平台特价商品导入', 2400, 4, 'iXLvBgBDqOJrkkPjUw', '2400', '', NULL, NULL, 1, 0, 'F', '0', '0', '	 promotion:activity:sp-rule:import', '#', 'zksr', '2024-11-27 16:05:12', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2656, '平台秒杀商品导入', 2396, 4, 'ySmqSoCkCaMlFBOJAK', '2396', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:import', '#', 'zksr', '2024-11-27 16:04:35', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2655, '分页查询账户订单结算流水', 2644, 4, 'ZrbwNLshWmQSvzzmzA', '2644', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:settle:list', '#', 'zksr', '2024-11-27 15:39:15', '', NULL, '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2654, '获得软件账户提现单分页列表', 2644, 3, 'ZzwOTpdhNSEdFrCROF', '2644', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-software:list', '#', 'zksr', '2024-11-27 15:38:14', '', NULL, '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2653, '注册支付软件商户', 2648, 5, 'fbistFRRCKnHseXXXZ', 'KGINOgYcXdLRpGqhPx', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-software-partner:register', '#', 'zksr', '2024-11-27 15:32:55', '', NULL, '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2652, '获取进件商户列表', 2648, 4, 'GygVgZjHAFQWNeECpu', 'KGINOgYcXdLRpGqhPx', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-software-partner:list', '#', 'zksr', '2024-11-27 15:32:20', '', NULL, '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2651, '修改软件商信息', 2648, 3, 'cPvLIqCipZukSkQcPQ', 'KGINOgYcXdLRpGqhPx', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:software:edit', '#', 'zksr', '2024-11-27 15:29:24', '', NULL, '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2650, '新增软件商信息', 2648, 2, 'hppBFHZGxlOjNhphLe', 'KGINOgYcXdLRpGqhPx', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:software:add', '#', 'zksr', '2024-11-27 15:29:03', '', NULL, '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2649, '获得软件商信息详情', 2648, 1, 'RCpyFKKKzKhWvYrsma', 'KGINOgYcXdLRpGqhPx', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:software:query', '#', 'zksr', '2024-11-27 15:28:26', 'zksr', '2024-11-27 16:08:53', '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2648, '软件商管理', 2017, 1, 'KGINOgYcXdLRpGqhPx', '2017', 'SoftwareInfo', 'softwareManage/softwareInfo/index', NULL, 1, 0, 'C', '0', '0', 'system:software:list', 'button', 'zksr', '2024-11-27 15:26:09', 'zksr', '2024-11-27 16:11:04', '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2647, '新增软件账户提现单', 2644, 2, 'vrrwbDyYbZOrJuMqxr', '2644', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-software:add', '#', 'zksr', '2024-11-27 15:05:10', '', NULL, '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2646, '获取软件商账户', 2644, 1, 'MEegIlkTtzOYMwIsHu', '2644', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:account:query-software', '#', 'zksr', '2024-11-27 15:04:28', '', NULL, '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2645, '秒杀商品导入', 2399, 4, 'ejPNSJpUpBxDNGznxw', '2399', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:import', '#', 'zksr', '2024-11-27 14:39:30', 'zksr', '2024-11-27 14:39:30', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2644, '软件商账户', 2031, 1, '2644', '2031', 'SoftwareAccount', 'finance/softwareAccount/index', NULL, 1, 0, 'C', '0', '0', 'system:software:list', 'documentation', 'zksr', '2024-11-26 16:15:42', 'zksr', '2024-11-27 11:23:08', '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2643, '特价商品导入', 2401, 4, '2643', '2401', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sp-rule:import', '#', 'zksr', '2024-11-26 09:21:29', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2641, '区域销售汇总', 2031, 13, '2641', '2031', 'AreaSaleSummary', 'finance/areaSaleSummary/index', NULL, 1, 0, 'C', '0', '0', 'report:month:list', 'education', 'zksr', '2024-11-25 15:15:16', '', NULL, '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2640, '入驻商销售汇总', 2031, 12, '2640', '2031', 'SupplierSaleSummary', 'finance/supplierSaleSummary/index', NULL, 1, 0, 'C', '0', '0', 'report:month:list', 'education', 'zksr', '2024-11-25 14:16:57', '', NULL, '', 'supplier,dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2639, '修改标签定义', 2638, 1, '2639', '2638', '', NULL, NULL, 1, 0, 'F', '0', '0', 'report:tagDefinit:edit', '#', 'zksr', '2024-11-20 18:41:21', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2638, '标签配置', 2021, 5, '2638', '2021', 'LabelConfig', 'platform/labelConfig/index', NULL, 1, 0, 'C', '0', '0', 'report:tagDefinit:query', 'language', 'zksr', '2024-11-20 18:40:22', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2637, '删除平台页面配置模版', 2613, 4, '2637', '2613', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:pageConfigTemplate:remove', '#', 'zksr', '2024-11-19 16:52:24', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2636, '修改页面启用状态', 2613, 3, '2636', '2613', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:pageConfigTemplate:edit', '#', 'zksr', '2024-11-19 16:51:58', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2635, '修改平台页面配置模版', 2613, 2, '2635', '2613', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:pageConfigTemplate:edit', '#', 'zksr', '2024-11-19 16:51:25', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2634, '新增平台页面配置模版', 2613, 1, '2634', '2613', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:pageConfigTemplate:add', '#', 'zksr', '2024-11-19 16:50:56', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2633, '城市展示分类导入', 2096, 6, '2633', '2096', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:areaClass:import', '#', 'zksr', '2024-11-13 19:49:38', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2632, '异步导出全部商品', 2131, 20, '2632', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'shopcot:spu_export', '#', 'zksr', '2024-11-13 19:45:13', 'zksr', '2024-11-27 11:14:04', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2631, '平台商展示分类导入', 2122, 7, '2631', '2122', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:saleClass:import', '#', 'zksr', '2024-11-13 19:45:10', 'zksr', '2024-11-13 19:48:39', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2630, '异步导出商品价格', 2131, 19, '2630', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'shopcot:sku_prices_export', '#', 'zksr', '2024-11-13 19:39:54', 'zksr', '2024-11-13 19:41:58', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2629, '任务列表', 2628, 1, '2629', '2628', 'taskList', 'reportManage/taskList/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2024-11-06 23:18:12', '', NULL, '', 'partner,supplier,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2628, '导出管理', 0, 10, '2628', '0', 'reportManage', NULL, NULL, 1, 0, 'M', '0', '0', '', 'education', 'zksr', '2024-11-06 23:17:24', 'zksr', '2024-11-27 11:46:48', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2627, '提现对账单', 2031, 13, '2627', '2031', 'withdrawalAccount', 'finance/withdrawalAccount/index', NULL, 1, 0, 'C', '0', '0', 'account:withdraw:getAccWithdrawBillList', '#', 'zksr', '2024-11-06 23:04:28', '', NULL, '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2626, '交易对账单明细', 2031, 12, '2626', '2031', 'tradeAccountDetails', 'finance/tradeAccount/details', NULL, 1, 0, 'C', '0', '0', 'account:transfer:getAccTransferBillOrderList', '#', 'zksr', '2024-11-06 23:03:33', '', NULL, '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2625, '交易对账单', 2031, 11, '2625', '2031', 'tradeAccount', 'finance/tradeAccount/index', NULL, 1, 0, 'C', '0', '0', 'account:transfer:getAccTranList', '#', 'zksr', '2024-11-06 23:02:42', '', NULL, '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2624, '订单分佣对账单', 2031, 10, '2624', '2031', 'orderKickbackAccount', 'finance/orderKickbackAccount/index', NULL, 1, 0, 'C', '0', '0', 'trade:settle:orderStatementOfAccountList', '#', 'zksr', '2024-11-06 23:00:48', 'zksr', '2024-11-27 11:30:01', '', 'partner,dc,supplier,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2623, '商城装修', 0, 1, '2623', '0', 'customize', NULL, NULL, 1, 0, 'M', '0', '0', '', 'theme', 'zksr', '2024-11-06 22:53:08', 'zksr', '2024-11-27 10:50:40', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2622, '分享配置', 2623, 6, '2622', '2623', 'shareConfig', 'customize/shareConfig/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2024-11-06 22:50:01', 'zksr', '2024-11-06 22:53:45', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2620, '自定义页面详情', 2623, 4, '2620', '2623', 'PageConfigDetails', 'customize/pageConfig/details', NULL, 1, 1, 'C', '1', '0', 'system:pageConfig:list', '#', 'zksr', '2024-11-06 22:48:29', 'zksr', '2024-11-19 16:46:56', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2619, '获得平台页面配置详情', 2612, 6, '2619', '2612', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:pageConfig:query', '#', 'zksr', '2024-11-06 22:47:13', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2618, '删除平台页面配置', 2612, 5, '2618', '2612', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:pageConfig:remove', '#', 'zksr', '2024-11-06 22:46:45', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2617, '修改平台页面配置启用状态', 2612, 4, '2617', '2612', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:pageConfig:changeStatus', '#', 'zksr', '2024-11-06 22:46:20', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2616, '修改平台页面配置是否默认', 2612, 3, '2616', '2612', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:pageConfig:changeDefFlag', '#', 'zksr', '2024-11-06 22:45:51', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2615, '修改平台页面配置', 2612, 2, '2615', '2612', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:pageConfig:edit', '#', 'zksr', '2024-11-06 22:45:20', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2614, '新增平台页面配置', 2612, 1, '2614', '2612', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:pageConfig:add', '#', 'zksr', '2024-11-06 22:44:51', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2613, '平台模版', 2623, 2, '2613', '2623', 'pageConfigTemplate', 'customize/pageConfigTemplate/index', NULL, 1, 0, 'C', '0', '0', 'system:pageConfigTemplate:list', '#', 'zksr', '2024-11-06 22:44:17', 'zksr', '2024-11-19 16:50:18', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2612, '首页配置', 2623, 1, '2612', '2623', 'pageConfig', 'customize/pageConfig/index', NULL, 1, 0, 'C', '0', '0', 'system:pageConfig:list', '#', 'zksr', '2024-11-06 22:43:36', 'zksr', '2024-11-06 22:56:15', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2611, '导出调价单', 2603, 1, '2611', '2603', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:export', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2610, '导入调价单', 2603, 1, '2610', '2603', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:import', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'supplier,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2609, '审核调价单', 2603, 1, '2609', '2603', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:approve', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2608, '查询调价单', 2603, 1, '2608', '2603', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:query', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2607, '调价单列表', 2603, 1, '2607', '2603', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:list', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2606, '删除调价单', 2603, 1, '2606', '2603', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:remove', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'supplier,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2605, '修改调价单', 2603, 1, '2605', '2603', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:edit', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'supplier,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2604, '新增调价单', 2603, 1, '2604', '2603', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:prices:add', '#', 'zksr', '2024-11-05 10:29:16', 'zksr', '2024-11-05 15:30:54', '', 'supplier,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2603, '调价单', 2602, 1, '2603', '2602', 'priceOrder', 'priceManage/priceOrder/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2024-11-04 15:14:54', '', NULL, '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2602, '调价管理', 0, 20, '2602', '0', 'priceManage', NULL, NULL, 1, 0, 'M', '0', '0', '', 'monitor', 'zksr', '2024-11-04 15:12:45', 'zksr', '2024-11-04 15:13:47', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2601, '列表', 2490, 2, '2601', '2490', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonel:list', '#', 'zksr', '2024-11-05 16:08:43', '', NULL, '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2600, '审核', 2490, 0, '2600', '2490', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonel:audit', '#', 'zksr', '2024-11-05 16:08:24', 'zksr', '2024-11-27 11:38:16', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2599, '获得平台品牌分页列表', 2131, 10, '2599', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:brand:list', '#', 'zksr', '2024-11-01 15:45:14', 'zksr', '2024-11-01 15:47:16', '', 'dc,partner,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2598, '品牌导入', 2102, 6, '2598', '2102', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:brand:import', '#', 'zksr', '2024-10-29 09:32:44', 'zksr', '2024-11-27 11:11:34', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2597, '导入商品图片', 2131, 16, '2597', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:batchImport', '#', 'zksr', '2024-10-25 16:26:21', 'zksr', '2024-11-27 11:13:53', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2596, '联行号管理', 1, 10, '2596', '1', 'bankChannel', 'system/bankChannel/index', NULL, 1, 0, 'C', '0', '0', 'system:bank-channel:list', 'edit', 'zksr', '2024-10-22 18:55:24', '', NULL, '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2591, '解除入驻商商户绑定', 2492, 36, '2591', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:remove', '#', 'zksr', '2024-10-14 09:05:39', 'zksr', '2024-10-15 15:53:00', '', 'dc,partner,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2590, '解除平台商商户绑定', 2492, 34, '2590', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-partner:remove', '#', 'zksr', '2024-10-14 09:05:11', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2589, '解除运营商商户绑定', 2492, 33, '2589', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-dc:remove', '#', 'zksr', '2024-10-14 09:04:51', '', NULL, '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2588, '解除业务员商户绑定', 2492, 32, '2588', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-colonel:remove', '#', 'zksr', '2024-10-14 09:04:00', 'zksr', '2024-10-14 09:04:27', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2572, '入驻商提现配置', 2492, 27, '2572', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-partner:edit', '#', 'zksr', '2024-09-25 08:55:29', '', NULL, '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2571, '平台商提现配置', 2492, 26, '2571', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-partner:edit', '#', 'zksr', '2024-09-25 08:54:37', 'zksr', '2024-11-27 11:50:54', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2570, '运营商微信B2B支付提现配置', 2492, 25, '2570', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-partner:edit', '#', 'zksr', '2024-09-25 08:53:30', 'zksr', '2024-11-27 11:51:06', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2569, '取消售后单', 2290, 3, '2569', '2290', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:after:approve', '#', 'zksr', '2024-09-24 11:35:14', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2568, '商品拷贝', 2246, 16, '2568', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:copy', '#', 'zksr', '2024-09-14 14:46:56', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2567, '打印设置详情', 2562, 5, '2567', '2562', '', NULL, NULL, 1, 0, 'F', '0', '0', 'print:printSettings:query', '#', 'zksr', '2024-09-14 08:55:05', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2566, '删除打印设置', 2562, 4, '2566', '2562', '', NULL, NULL, 1, 0, 'F', '0', '0', 'print:printSettings:remove', '#', 'zksr', '2024-09-14 08:54:33', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2565, '打印列表', 2562, 3, '2565', '2562', '', NULL, NULL, 1, 0, 'F', '0', '0', 'print:printSettings:list', '#', 'zksr', '2024-09-14 08:53:55', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2564, '修改打印设置', 2562, 2, '2564', '2562', '', NULL, NULL, 1, 0, 'F', '0', '0', 'print:printSettings:edit', '#', 'zksr', '2024-09-14 08:53:23', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2563, '新增打印设置', 2562, 1, '2563', '2562', '', NULL, NULL, 1, 0, 'F', '0', '0', 'print:printSettings:add', '#', 'zksr', '2024-09-14 08:52:53', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2562, '打印设置', 2021, 4, '2562', '2021', 'printSet', 'platform/printSet/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2024-09-14 08:52:16', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2561, 'pc打印', 2262, 7, '2561', '2262', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:order:getOperatorOrderPageList', '#', 'zksr', '2024-09-14 08:50:03', 'zksr', '2024-11-27 11:44:29', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2560, '修改入驻商备注', 2262, 6, '2560', '2262', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierOrder:memoEdit', '#', 'zksr', '2024-09-11 09:59:06', '', NULL, '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2559, '详情', 2555, 4, '2559', '2555', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:hdfk-pay:query', '#', 'zksr', '2024-09-11 08:52:24', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2558, '新增货到付款付款单', 2555, 3, '2558', '2555', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:hdfk-pay:add', '#', 'zksr', '2024-09-11 08:51:46', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2557, '历史清账列表', 2555, 2, '2557', '2555', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:hdfk-pay:list', '#', 'zksr', '2024-09-11 08:51:22', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2556, '获得清账结算分页列表', 2555, 1, '2556', '2555', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:hdfk-settle:list', '#', 'zksr', '2024-09-11 08:50:53', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2555, '订单清账', 2028, 6, '2555', '2028', 'orderSettlement', 'orderManage/orderSettlement/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2024-09-11 08:50:18', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2554, '批量完成业务员目标设置', 2058, 9, '2554', '2058', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonelTarget:completed', '#', 'zksr', '2024-09-11 08:47:42', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2553, '新增批量业务员目标设置', 2058, 8, '2553', '2058', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonelTarget:add', '#', 'zksr', '2024-09-11 08:47:10', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2552, '共享商品详情', 2246, 15, '2552', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:query', '#', 'zksr', '2024-09-10 19:08:37', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2551, '共享商品搜索', 2246, 14, '2551', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:list', '#', 'zksr', '2024-09-10 19:08:18', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2550, '订单手动推送至第三方', 2262, 1, '2550', '2262', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierOrder:sync', '#', 'zksr', '2024-08-21 17:49:50', 'zksr', '2024-10-18 11:29:23', '', 'supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2549, '详情', 2538, 6, '2549', '2538', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:brand-member:query', '#', 'zksr', '2024-08-20 14:41:17', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2548, '获得品牌商子账户分页列表', 2538, 5, '2548', '2538', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:brand-member:list', '#', 'zksr', '2024-08-20 14:40:49', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2547, '启用', 2538, 4, '2547', '2538', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:brand-member:enable', '#', 'zksr', '2024-08-20 14:40:11', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2546, '停用', 2538, 3, '2546', '2538', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:brand-member:disable', '#', 'zksr', '2024-08-20 14:39:24', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2545, '修改账号', 2538, 2, '2545', '2538', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:brand-member:edit', '#', 'zksr', '2024-08-20 14:38:58', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2544, '新增', 2538, 1, '2544', '2538', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:brand-member:add', '#', 'zksr', '2024-08-20 14:38:28', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2543, '启用品牌商', 2537, 5, '2543', '2537', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:brand-merchant:enable', '#', 'zksr', '2024-08-20 14:37:51', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2542, '停用品牌商', 2537, 4, '2542', '2537', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:brand-merchant:disable', '#', 'zksr', '2024-08-20 14:37:24', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2541, '品牌商资料详情', 2537, 3, '2541', '2537', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:brand-merchant:query', '#', 'zksr', '2024-08-20 14:36:54', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2540, '修改品牌商', 2537, 2, '2540', '2537', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:brand-merchant:edit', '#', 'zksr', '2024-08-20 14:36:23', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2539, '新增', 2537, 1, '2539', '2537', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:brand-merchant:add', '#', 'zksr', '2024-08-20 14:35:52', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2538, '品牌商账户管理', 2536, 2, '2538', '2536', 'branduserManagement', 'platform/branduserManagement/index', NULL, 1, 0, 'C', '0', '0', 'system:brand-member:list', '#', 'zksr', '2024-08-20 14:34:26', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2537, '品牌商基础信息', 2536, 1, '2537', '2536', 'brandManagement', 'platform/brandManagement/index', NULL, 1, 0, 'C', '0', '0', 'system:brand-merchant:list', '#', 'zksr', '2024-08-20 14:33:23', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2536, '品牌商管理', 0, 20, '2536', '0', 'basis', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'star', 'zksr', '2024-08-20 14:32:07', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2535, '订单打印', 2262, 4, '2535', '2262', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:order:orderPrint', '#', 'zksr', '2024-08-15 20:09:26', '', NULL, '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2534, '可视化接口模板', 2531, 3, '2534', '2531', 'visualization/template', 'software/visualization/template', NULL, 1, 0, 'C', '0', '0', '', 'education', 'zksr', '2024-07-23 10:28:16', 'zksr', '2024-07-23 10:29:03', '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2533, '可视化接口配置详情', 2531, 2, '2533', '2531', 'visualization/detail', 'software/visualization/detail', NULL, 1, 1, 'C', '1', '0', '', 'edit', 'zksr', '2024-07-01 13:24:52', 'zksr', '2024-07-01 13:26:00', '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2532, '可视化接口', 2531, 1, '2532', '2531', 'visualization', 'software/visualization/index', NULL, 1, 0, 'C', '0', '0', '', 'edit', 'zksr', '2024-06-28 15:46:33', 'zksr', '2024-06-28 17:36:26', '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2531, '可视化接口', 0, 1, '2531', '0', 'software', NULL, NULL, 1, 1, 'M', '0', '0', '', 'edit', 'zksr', '2024-06-28 10:46:15', 'zksr', '2024-07-01 14:20:29', '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2530, '获取入驻商小程序订单详情', 2246, 3, '2530', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierAppOrder:getSupplierOrderDtl', '#', 'zksr', '2024-08-07 16:56:40', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2529, '获取入驻商小程序订单分页列表', 2246, 2, '2529', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierAppOrder:getMerchantMiniProgramOrderPageList', '#', 'zksr', '2024-08-07 16:56:14', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2528, '发展业务员', 2331, 7, '2528', '2331', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:developmentSalesman', '#', 'zksr', '2024-08-05 21:03:51', '', '2024-08-05 21:03:51', '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2527, '获取首页数据', 2246, 12, '2527', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierAppOrder:getHomePage', '#', 'zksr', '2024-07-26 10:23:15', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2526, '联行号查询', 2518, 0, '2526', '2518', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:bank-channel:list', '#', 'zksr', '2024-07-24 09:47:14', '', NULL, '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2525, '联行号查询', 2246, 13, '2525', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:bank-channel:list', '#', 'zksr', '2024-07-23 16:43:25', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2524, '修改商户信息', 2520, 16, '2524', '2520', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:edit', '#', 'zksr', '2024-07-23 16:27:01', 'zksr', '2024-07-23 16:27:10', '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2523, '查询商户', 2520, 15, '2523', '2520', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:query', '#', 'zksr', '2024-07-23 16:26:16', 'zksr', '2024-07-23 16:27:16', '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2522, '获取进件商户信息', 2520, 14, '2522', '2520', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:query-supplier-merchant', '#', 'zksr', '2024-07-23 16:25:54', 'zksr', '2024-07-23 16:26:24', '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2521, '进件资质上传', 2520, 13, '2521', '2520', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:uploadPic', '#', 'zksr', '2024-07-23 16:25:24', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2520, '商户进件', 2246, 12, '2520', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:register', '#', 'zksr', '2024-07-23 16:24:57', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2518, '支付平台进件', 2331, 6, '2518', '2331', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:merchant', '#', 'zksr', '2024-07-23 09:41:36', '', NULL, '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2517, '业务员详情', 2492, 24, '2517', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-colonel:query', '#', 'zksr', '2024-07-22 10:28:44', 'zksr', '2024-07-25 19:58:49', '', 'partner,dc,colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2516, '业务员上传', 2492, 23, '2516', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-colonel:uploadPic', '#', 'zksr', '2024-07-22 10:28:09', 'zksr', '2024-09-29 08:38:21', '', 'partner,dc,colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2515, '业务员更新进件信息', 2492, 22, '2515', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-colonel:edit', '#', 'zksr', '2024-07-22 10:27:36', 'zksr', '2024-11-27 11:49:54', '', 'partner,dc,colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2514, '业务员同步', 2492, 21, '2514', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-colonel:sync', '#', 'zksr', '2024-07-22 10:27:04', '', NULL, '', 'partner,dc,colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2513, '业务员注册', 2492, 20, '2513', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-colonel:register', '#', 'zksr', '2024-07-22 10:26:34', 'zksr', '2024-09-29 08:38:18', '', 'partner,dc,colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2512, '业务员列表', 2492, 19, '2512', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-colonel:list', '#', 'zksr', '2024-07-22 10:26:09', 'zksr', '2024-09-29 08:38:09', '', 'partner,dc,colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2511, '入驻商详情', 2492, 18, '2511', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:query', '#', 'zksr', '2024-07-22 10:25:37', 'zksr', '2024-08-16 13:15:18', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2510, '入驻商上传', 2492, 17, '2510', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:uploadPic', '#', 'zksr', '2024-07-22 10:25:01', 'zksr', '2024-08-16 13:19:44', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2509, '入驻商更新支付', 2492, 16, '2509', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:edit', '#', 'zksr', '2024-07-22 10:24:12', 'zksr', '2024-08-16 13:19:48', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2508, '入驻商同步商户信息', 2492, 15, '2508', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:sync', '#', 'zksr', '2024-07-22 10:23:31', 'zksr', '2024-07-25 19:58:37', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2507, '注册入驻商', 2492, 14, '2507', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:register', '#', 'zksr', '2024-07-22 10:22:38', 'zksr', '2024-08-16 13:19:53', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2506, '入驻商列表', 2492, 13, '2506', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-supplier:list', '#', 'zksr', '2024-07-22 10:21:55', 'zksr', '2024-10-15 15:53:19', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2505, 'APP配置', 2246, 10, '2505', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:supplier:app-config', '#', 'zksr', '2024-07-18 14:09:12', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2504, '运营商上传商户资质', 2492, 12, '2504', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-dc:uploadPic', '#', 'zksr', '2024-07-18 11:05:41', '', NULL, '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2503, '平台商上传', 2492, 11, '2503', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-partner:uploadPic', '#', 'zksr', '2024-07-18 11:05:02', '', NULL, '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2502, '完善平台商支付平台商户', 2492, 10, '2502', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-partner:edit', '#', 'zksr', '2024-07-18 11:03:58', '', NULL, '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2501, '完善运营商支付平台商户', 2492, 9, '2501', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-dc:edit', '#', 'zksr', '2024-07-18 11:03:11', '', NULL, '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2500, '平台商支详情', 2492, 8, '2500', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-partner:query', '#', 'zksr', '2024-07-17 18:25:44', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2499, '平台商同步', 2492, 7, '2499', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-partner:sync', '#', 'zksr', '2024-07-17 18:24:57', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2498, '平台商进件商户列表', 2492, 6, '2498', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-partner:list', '#', 'zksr', '2024-07-17 18:23:34', 'zksr', '2024-07-18 09:23:50', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2497, '平台商注册支付', 2492, 5, '2497', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-partner:register', '#', 'zksr', '2024-07-17 18:22:43', 'zksr', '2024-07-17 18:22:54', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2496, '运营商注册支付', 2492, 4, '2496', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-dc:register', '#', 'zksr', '2024-07-17 18:21:57', 'zksr', '2024-07-18 09:04:27', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2495, '运营商同步', 2492, 3, '2495', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-dc:sync', '#', 'zksr', '2024-07-17 17:27:33', 'zksr', '2024-07-18 09:04:22', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2494, '运营商支付平台商户详情', 2492, 2, '2494', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-dc:query', 'backpack', 'zksr', '2024-07-17 17:27:00', 'zksr', '2024-07-18 09:04:16', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2493, '运营商进件商户列表', 2492, 1, '2493', '2492', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:platform-merchant-dc:list', '#', 'zksr', '2024-07-17 16:24:30', 'zksr', '2024-10-15 15:53:12', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2492, '自主进件', 2491, 1, '2492', '2491', 'trademark', 'operation/trademark/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2024-07-16 09:39:08', 'zksr', '2024-09-29 08:38:02', '', 'dc,partner,supplier,colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2491, '商户管理', 0, 15, '2491', '0', 'ney', NULL, NULL, 1, 0, 'M', '0', '0', '', 'zip', 'zksr', '2024-07-15 16:16:54', 'zksr', '2024-08-16 13:19:18', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2490, '业务员注册信息', 2025, 7, '2490', '2025', 'salesmanrRegister', 'operation/salesmanrRegister/index', NULL, 1, 0, 'C', '0', '0', '', 'backpack', 'zksr', '2024-07-15 14:27:29', 'zksr', '2024-11-05 16:00:23', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2489, '审核通过', 2274, 0, '2489', '2274', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-dc:enable', '#', 'zksr', '2024-07-13 18:27:11', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2488, '结算重试', 2274, 1, '2488', '2274', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-dc:settle', '#', 'zksr', '2024-07-13 18:25:36', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2487, '解绑公众号', 2246, 9, '2487', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:supplier:unbind-publish-openid', '#', 'zksr', '2024-07-12 18:00:35', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2486, '绑定公众号', 2246, 8, '2486', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:supplier:bind-publish-openid', '#', 'zksr', '2024-07-12 18:00:23', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2485, '入驻商详情', 2246, 7, '2485', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:supplier:query', '#', 'zksr', '2024-07-12 18:00:08', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2484, '消费金额列表', 2246, 6, '2484', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierAppOrder:getSupplierOrderSettle', '#', 'zksr', '2024-07-11 18:31:27', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2483, '批量修改商品品牌', 2131, 15, '2483', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:edit-brand', '#', 'zksr', '2024-07-11 08:53:16', 'zksr', '2024-08-08 09:53:05', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2482, '批量修改商品类别', 2131, 14, '2482', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:edit-category', '#', 'zksr', '2024-07-11 08:52:15', 'zksr', '2024-08-08 09:53:03', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2481, '业务员导入', 2057, 8, '2481', '2057', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonel:import', '#', 'zksr', '2024-07-05 11:31:01', 'zksr', '2024-11-27 11:39:00', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2480, '门店信息导入', 2193, 9, '2480', '2193', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:branch:import', '#', 'zksr', '2024-07-04 15:34:33', 'zksr', '2024-11-27 11:35:22', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2475, '平台管理分类导入', 2110, 8, '2475', '2110', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:catgory:import', '#', 'zksr', '2024-07-09 09:03:58', 'zksr', '2024-07-09 09:03:58', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2474, '商品库信息导入', 2463, 1, '2474', '2463', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:import', '#', 'zksr', '2024-06-29 17:49:50', 'zksr', '2024-11-27 11:04:37', '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2473, '共享商品列表', 2131, 13, '2473', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:list', '#', 'zksr', '2024-06-28 19:24:09', 'zksr', '2024-08-08 09:48:19', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2472, '共享商品详情', 2131, 12, '2472', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:query', '#', 'zksr', '2024-06-28 19:23:42', 'zksr', '2024-08-08 09:47:59', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2471, '查看详情', 2463, 4, '2471', '2463', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:query', '#', 'zksr', '2024-06-26 15:05:45', 'zksr', '2024-11-27 11:05:49', '', 'partner,dc,supplier,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2470, '关闭共享', 2463, 3, '2470', '2463', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:disable', '#', 'zksr', '2024-06-26 15:01:56', 'zksr', '2024-11-27 11:05:32', '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2469, '开启共享', 2463, 2, '2469', '2463', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:enable', '#', 'zksr', '2024-06-26 15:01:41', 'zksr', '2024-11-27 11:05:25', '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2468, '商品列表', 2463, 1, '2468', '2463', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:platform-spu:list', '#', 'zksr', '2024-06-26 15:01:13', 'zksr', '2024-11-27 14:02:22', '', 'partner,dc,supplier,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2467, '快速编辑', 2131, 11, '2467', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:edit-base', '#', 'zksr', '2024-06-26 14:46:30', 'zksr', '2024-08-08 09:48:22', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2466, '关闭商品共享', 2131, 9, '2466', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:share-disable', '#', 'zksr', '2024-06-26 14:41:33', 'zksr', '2024-08-08 09:47:51', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2465, '开启商品共享', 2131, 8, '2465', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:share-enable', '#', 'zksr', '2024-06-26 14:41:16', 'zksr', '2024-08-08 09:47:46', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2464, '商品列表', 2131, 7, '2464', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:spuList', '#', 'zksr', '2024-06-26 14:40:52', 'zksr', '2024-06-28 19:16:55', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2463, '商品库管理', 2017, 1, '2463', '2017', 'commodityStocks', 'operation/commodityStocks/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2024-06-21 13:05:20', 'zksr', '2024-11-27 14:02:17', '', 'partner,supplier,dc,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2454, '同步日志', 2, 3, '2454', '2', 'synchronization', 'monitor/synchronization/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2024-05-31 16:17:10', '', NULL, '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2453, '可视化接口', 2021, 3, '2453', '2021', 'visualization', 'platform/visualization/index', NULL, 1, 0, 'C', '0', '1', '', '#', 'zksr', '2024-05-29 08:41:08', 'zksr', '2024-11-27 11:47:44', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2452, '促销报表', 2368, 9, '2452', '2368', 'promotion/reportBuilder', 'operation/promotion/reportBuilder/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'zksr', '2024-05-25 15:08:53', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2451, '优惠劵报表', 2368, 8, '2451', '2368', 'promotion/couponForm', 'operation/promotion/couponForm/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2024-05-24 19:25:23', 'zksr', '2024-05-24 19:25:51', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2450, '获取特价活动详细信息', 2400, 3, '2450', '2400', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:query', '#', 'zksr', '2024-05-20 15:51:44', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2449, '编辑特价促销活动', 2400, 2, '2449', '2400', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sp-rule:edit', '#', 'zksr', '2024-05-20 15:51:18', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2448, '新增特价活动规则', 2400, 1, '2448', '2400', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sp-rule:add', '#', 'zksr', '2024-05-20 15:50:42', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2447, '获取秒杀活动详细信息', 2396, 3, '2447', '2396', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:query', '#', 'zksr', '2024-05-20 15:50:10', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2446, '编辑秒杀促销活动', 2396, 2, '2446', '2396', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:edit', '#', 'zksr', '2024-05-20 15:49:34', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2445, '新增秒杀规则', 2396, 1, '2445', '2396', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:add', '#', 'zksr', '2024-05-20 15:49:01', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2444, '启用停用', 2398, 4, '2444', '2398', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:changeBgStatus', '#', 'zksr', '2024-05-20 15:32:45', 'zksr', '2024-05-21 08:48:42', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2443, '启用停用', 2397, 4, '2443', '2397', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:changeBgStatus', '#', 'zksr', '2024-05-20 15:32:24', 'zksr', '2024-05-21 08:48:27', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2442, '启用-停用满减活动', 2419, 4, '2442', '2419', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:edit', '#', 'zksr', '2024-05-20 15:36:51', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2441, '启用-停用满减活动', 2420, 4, '2441', '2420', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:edit', '#', 'zksr', '2024-05-20 15:35:53', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2440, '启用停用', 2418, 4, '2440', '2418', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:changeFgStatus', '#', 'zksr', '2024-05-20 15:32:45', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2439, '启用停用', 2417, 4, '2439', '2417', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:changeFgStatus', '#', 'zksr', '2024-05-20 15:32:24', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2438, '获取详情', 2398, 3, '2438', '2398', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:query', '#', 'zksr', '2024-05-18 15:48:21', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2437, '修改', 2398, 2, '2437', '2398', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:edit', '#', 'zksr', '2024-05-18 15:48:43', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2436, '新增', 2398, 1, '2436', '2398', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:add', '#', 'zksr', '2024-05-18 15:49:00', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2435, '获取详情', 2397, 3, '2435', '2397', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:query', '#', 'zksr', '2024-05-18 15:47:48', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2434, '修改', 2397, 2, '2434', '2397', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:edit', '#', 'zksr', '2024-05-18 15:47:23', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2433, '新增', 2397, 1, '2433', '2397', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:bg-rule:add', '#', 'zksr', '2024-05-18 15:46:52', 'zksr', '2024-05-18 15:47:01', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2432, '获取详情', 2420, 3, '2432', '2420', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:query', '#', 'zksr', '2024-05-18 15:51:36', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2431, '获取详情', 2419, 3, '2431', '2419', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:query', '#', 'zksr', '2024-05-18 15:51:17', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2430, '修改', 2419, 2, '2430', '2419', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:edit', '#', 'zksr', '2024-05-18 15:50:56', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2429, '修改', 2420, 2, '2429', '2420', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:edit', '#', 'zksr', '2024-05-18 15:50:38', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2428, '新增', 2420, 1, '2428', '2420', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:add', '#', 'zksr', '2024-05-18 15:50:11', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2427, '新增', 2419, 1, '2427', '2419', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fd-rule:add', '#', 'zksr', '2024-05-18 15:49:43', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2426, '新增', 2418, 1, '2426', '2418', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:add', '#', 'zksr', '2024-05-18 15:49:00', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2425, '修改', 2418, 2, '2425', '2418', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:edit', '#', 'zksr', '2024-05-18 15:48:43', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2424, '获取详情', 2418, 3, '2424', '2418', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:query', '#', 'zksr', '2024-05-18 15:48:21', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2423, '获取详情', 2417, 3, '2423', '2417', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:query', '#', 'zksr', '2024-05-18 15:47:48', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2422, '修改', 2417, 2, '2422', '2417', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:edit', '#', 'zksr', '2024-05-18 15:47:23', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2421, '新增', 2417, 1, '2421', '2417', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:fg-rule:add', '#', 'zksr', '2024-05-18 15:46:52', 'zksr', '2024-05-18 15:47:01', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2420, '满减', 2027, 5, '2420', '2027', 'promotion/fullReduction', 'operation/promotion/fullReduction/index', NULL, 1, 0, 'C', '0', '0', 'promotion:activity:fd-rule:list', '#', 'zksr', '2024-05-17 18:29:36', 'zksr', '2024-05-17 18:37:27', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2419, '满减', 2368, 5, '2419', '2368', 'promotion/fullReduction', 'operation/promotion/fullReduction/index', NULL, 1, 0, 'C', '0', '0', 'promotion:activity:fd-rule:list', '#', 'zksr', '2024-05-17 18:29:01', 'zksr', '2024-05-17 18:37:14', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2418, '满赠', 2027, 4, '2418', '2027', 'promotion/fullGift', 'operation/promotion/fullGift/index', '{\"source\": 1}', 1, 0, 'C', '0', '0', ' promotion:activity:fg-rule:list', '#', 'zksr', '2024-05-17 18:25:24', 'zksr', '2024-06-05 10:10:27', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2417, '满赠', 2368, 4, '2417', '2368', 'promotion/fullGift', 'operation/promotion/fullGift/index', '{\"source\": 2}', 1, 0, 'C', '0', '0', ' promotion:activity:fg-rule:list', '#', 'zksr', '2024-05-17 17:57:01', 'zksr', '2024-06-05 10:10:08', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2416, '特价详细信息', 2401, 3, '2416', '2401', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:query', '#', 'zksr', '2024-05-17 14:28:19', 'zksr', '2024-05-17 14:42:47', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2415, '编辑特价促销活动', 2401, 2, '2415', '2401', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sp-rule:edit', '#', 'zksr', '2024-05-17 14:26:56', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2414, '新增特价活动规则', 2401, 1, '2414', '2401', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sp-rule:add', '#', 'zksr', '2024-05-17 14:26:10', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2413, '获取秒杀活动详细信息', 2399, 3, '2413', '2399', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:query', '#', 'zksr', '2024-05-16 10:08:37', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2412, ' 编辑秒杀促销活动', 2399, 2, '2412', '2399', '', NULL, NULL, 1, 0, 'F', '0', '0', ' promotion:activity:sk-rule:edit', '#', 'zksr', '2024-05-16 10:08:07', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2411, '新增秒杀', 2399, 1, '2411', '2399', '', NULL, NULL, 1, 0, 'F', '0', '0', ' promotion:activity:sk-rule:add', '#', 'zksr', '2024-05-16 10:07:41', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2410, '开放资源查询', 2402, 7, '2410', '2402', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:opensource:query', '#', 'zksr', '2024-05-15 17:40:43', '', NULL, '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2409, '开放资源修改', 2402, 6, '2409', '2402', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:opensource:edit', '#', 'zksr', '2024-05-15 17:40:09', '', NULL, '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2408, '开放资源新增', 2402, 5, '2408', '2402', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:opensource:add', '#', 'zksr', '2024-05-15 17:33:49', '', NULL, '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2407, '获得开放能力详情', 2403, 4, '2407', '2403', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:openability:query', '#', 'zksr', '2024-05-15 16:21:45', 'zksr', '2024-05-15 17:32:03', '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2406, '删除开放能力', 2403, 3, '2406', '2403', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:openability:remove', '#', 'zksr', '2024-05-15 16:21:08', 'zksr', '2024-05-15 17:32:12', '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2405, '修改开放能力', 2403, 2, '2405', '2403', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:openability:edit', '#', 'zksr', '2024-05-15 16:20:46', 'zksr', '2024-05-15 17:31:31', '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2404, '新增开放能力', 2403, 1, '2404', '2403', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:openability:add', '#', 'zksr', '2024-05-15 16:20:17', 'zksr', '2024-05-15 17:31:24', '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2403, '开放能力', 2402, 1, '2403', '2402', 'openAbility', 'openAbility/index', NULL, 1, 0, 'C', '0', '0', '', 'documentation', 'zksr', '2024-05-15 09:27:09', 'zksr', '2024-05-15 17:31:16', '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2402, '开放能力', 0, 1, '2402', '0', 'openAbility', NULL, NULL, 1, 0, 'M', '0', '0', '', 'documentation', 'zksr', '2024-05-15 09:26:33', 'zksr', '2024-05-27 11:20:14', '', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2401, '特价限购', 2368, 7, '2401', '2368', 'special', 'operation/special/index', NULL, 1, 0, 'C', '0', '0', 'promotion:activity:list', '#', 'zksr', '2024-05-14 16:21:33', 'zksr', '2024-05-17 18:05:35', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2400, '特价限购', 2027, 7, '2400', '2027', 'special', 'operation/special/index', NULL, 1, 0, 'C', '0', '0', 'promotion:activity:list', '#', 'zksr', '2024-05-14 16:20:34', 'zksr', '2024-05-20 15:48:06', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2399, '秒杀', 2368, 6, '2399', '2368', 'seckill', 'operation/seckill/index', NULL, 1, 0, 'C', '0', '0', 'promotion:activity:list', '#', 'zksr', '2024-05-13 19:42:07', 'zksr', '2024-11-27 11:42:08', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2398, '买赠', 2027, 3, '2398', '2027', 'promotion/buyGift', 'operation/promotion/buyGift/index', '{\"source\": 1}', 1, 0, 'C', '0', '0', 'promotion:activity:bg-rule:list', '#', 'zksr', '2024-05-13 19:39:36', 'zksr', '2024-06-05 10:10:23', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2397, '买赠', 2368, 3, '2397', '2368', 'promotion/buyGift', 'operation/promotion/buyGift/index', '{\"source\": 2}', 1, 0, 'C', '0', '0', 'promotion:activity:bg-rule:list', '#', 'zksr', '2024-05-13 19:38:38', 'zksr', '2024-06-05 10:10:05', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2396, '秒杀', 2027, 6, '2396', '2027', 'seckill', 'operation/seckill/index', NULL, 1, 0, 'C', '0', '0', 'promotion:activity:list', '#', 'zksr', '2024-05-13 19:32:03', 'zksr', '2024-05-20 15:47:06', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2395, '业务员拜访记录图片下载', 2390, 5, '2395', '2390', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:visitLog:download', '#', 'admin', '2024-05-13 17:03:48', 'zksr', '2024-08-20 12:53:19', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2394, '业务员拜访记录删除', 2390, 4, '2394', '2390', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:visitLog:remove', '#', 'admin', '2024-05-13 17:03:48', 'zksr', '2024-11-27 11:41:05', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2393, '业务员拜访记录修改', 2390, 3, '2393', '2390', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:visitLog:edit', '#', 'admin', '2024-05-13 17:03:48', 'zksr', '2024-11-27 11:41:00', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2392, '业务员拜访记录新增', 2390, 2, '2392', '2390', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:visitLog:add', '#', 'admin', '2024-05-13 17:03:48', 'zksr', '2024-11-27 11:40:55', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2391, '业务员拜访记录查询', 2390, 1, '2391', '2390', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:visitLog:query', '#', 'admin', '2024-05-13 17:03:47', 'zksr', '2024-08-20 12:53:27', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2390, '业务员拜访汇总', 2025, 41, '2390', '2025', 'salesmanVisitAll', 'operation/salesmanVisitAll/index', NULL, 1, 0, 'C', '0', '0', 'member:visitLog:list', '#', 'admin', '2024-05-13 17:03:47', 'zksr', '2024-08-20 12:53:29', '业务员拜访记录菜单', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2389, '业务员理货记录图片下载', 2384, 5, '2389', '2384', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:ColonelTidy:download', '#', 'admin', '2024-05-13 17:03:47', 'zksr', '2024-08-20 12:53:04', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2388, '业务员理货记录删除', 2384, 4, '2388', '2384', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:ColonelTidy:remove', '#', 'admin', '2024-05-13 17:03:47', 'zksr', '2024-11-27 11:40:39', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2387, '业务员理货记录修改', 2384, 3, '2387', '2384', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:ColonelTidy:edit', '#', 'admin', '2024-05-13 17:03:47', 'zksr', '2024-11-27 11:40:34', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2386, '业务员理货记录新增', 2384, 2, '2386', '2384', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:ColonelTidy:add', '#', 'admin', '2024-05-13 17:03:47', 'zksr', '2024-11-27 11:40:30', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2385, '业务员理货记录查询', 2384, 1, '2385', '2384', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:ColonelTidy:query', '#', 'admin', '2024-05-13 17:03:47', 'zksr', '2024-08-20 12:52:57', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2384, '理货记录', 2025, 40, '2384', '2025', 'salesTallyLog', 'operation/salesTallyLog/index', NULL, 1, 0, 'C', '0', '0', 'member:ColonelTidy:list', '#', 'admin', '2024-05-13 17:03:47', 'zksr', '2024-08-20 12:53:38', '业务员理货记录菜单', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2383, '门店注册信息详情', 2317, 1, '2383', '2317', '', NULL, NULL, 1, 0, 'F', '0', '0', ' member:branchRegister:query', '#', 'zksr', '2024-05-13 15:59:00', 'zksr', '2024-08-08 10:01:50', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2382, '获得业务员信息分页列表', 2363, 4, '2382', '2363', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonel:list', '#', 'zksr', '2024-05-11 15:11:08', 'zksr', '2024-11-27 11:29:01', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2381, '业务员提现结算重试', 2363, 3, '2381', '2363', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-colonel:settle', '#', 'zksr', '2024-05-11 14:37:23', 'zksr', '2024-11-27 11:28:41', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2380, '编辑优惠券', 2371, 7, '2380', '2371', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:edit', '#', 'zksr', '2024-05-11 13:53:07', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2379, '下架优惠券', 2371, 6, '2379', '2371', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:disable', '#', 'zksr', '2024-05-11 13:52:45', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2378, '发布优惠券', 2371, 5, '2378', '2371', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:enable', '#', 'zksr', '2024-05-11 13:52:25', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2377, '渠道下拉', 2371, 4, '2377', '2371', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:channel:list', '#', 'zksr', '2024-05-11 13:51:52', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2376, '门店下拉', 2371, 3, '2376', '2371', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:branch:list', '#', 'zksr', '2024-05-11 13:51:28', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2375, '获取优惠券模板详情', 2371, 2, '2375', '2371', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:query', '#', 'zksr', '2024-05-11 13:50:55', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2374, '获得平台品牌分页列表', 2372, 2, '2374', '2372', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:brand:list', '#', 'zksr', '2024-05-11 13:50:10', 'zksr', '2024-05-11 13:50:18', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2373, '类别券查询', 2372, 7, '2373', '2372', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:areaClass:list', '#', 'zksr', '2024-05-11 13:49:17', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2372, '新增优惠券模板', 2371, 1, '2372', '2371', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:add', '#', 'zksr', '2024-05-11 13:47:27', 'zksr', '2024-05-11 13:47:44', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2371, '优惠券列表', 2368, 2, '2371', '2368', 'preferential', 'operation/preferential/index', '{\"source\": 2}', 1, 0, 'C', '0', '0', 'promotion:template:list', 'tree-table', 'zksr', '2024-05-11 13:46:28', 'zksr', '2024-05-27 14:05:33', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2370, '停用优惠券记录', 2369, 1, '2370', '2369', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:coupon:disable', '#', 'zksr', '2024-05-11 13:45:37', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2369, '优惠券领取记录', 2368, 1, '2369', '2368', 'preferentialRecord', 'operation/preferentialRecord/index', NULL, 1, 0, 'C', '0', '0', 'promotion:coupon:list', 'tree-table', 'zksr', '2024-05-11 13:43:55', 'zksr', '2024-05-11 13:44:30', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2368, '运营促销管理', 0, 6, '2368', '0', 'promotion', NULL, NULL, 1, 0, 'M', '0', '0', '', 'documentation', 'zksr', '2024-05-11 13:41:46', 'zksr', '2024-10-15 20:54:09', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2367, '类别券查询', 2339, 7, '2367', '2339', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:areaClass:list', '#', 'zksr', '2024-05-11 10:08:36', 'zksr', '2024-05-11 11:36:13', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2366, '业务员审核通过', 2363, 2, '2366', '2363', '', NULL, NULL, 1, 0, 'F', '0', '0', ' account:withdraw-colonel:enable', '#', 'zksr', '2024-05-11 09:12:54', 'zksr', '2024-11-27 11:28:37', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2364, '业务员审核拒绝', 2363, 1, '2364', '2363', '', NULL, NULL, 1, 0, 'F', '0', '0', ' account:withdraw-colonel:disable', '#', 'zksr', '2024-05-11 09:11:05', 'zksr', '2024-11-27 11:28:32', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2363, '业务员申请审核', 2031, 8, '2363', '2031', 'salesmanAudit', 'finance/salesmanAudit/index', NULL, 1, 0, 'C', '0', '0', 'account:withdraw-colonel:list', '#', 'zksr', '2024-05-11 09:10:00', 'zksr', '2024-11-27 11:29:06', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2362, '查询投诉详情', 2361, 1, '2362', '2361', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:member:query', '#', 'zksr', '2024-05-10 17:40:04', 'zksr', '2024-11-27 11:45:35', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2361, '投诉管理', 2028, 5, '2361', '2028', 'complaints', 'orderManage/complaints/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2024-05-09 17:11:34', 'zksr', '2024-11-27 11:45:29', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2360, '门店下拉', 2256, 3, '2360', '2256', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:branch:list', '#', 'zksr', '2024-05-09 14:57:11', 'zksr', '2024-05-11 11:36:42', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2359, '优惠券列表', 2256, 0, '2359', '2256', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:list', '#', 'zksr', '2024-05-09 14:33:22', 'zksr', '2024-05-11 11:36:28', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2358, '业务员行程', 2025, 42, '2358', '2025', 'salesmanTrip', 'operation/salesmanTrip/index', NULL, 1, 0, 'C', '0', '0', '', 'documentation', 'zksr', '2024-05-09 14:31:05', 'zksr', '2024-08-20 12:53:14', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2357, '获得短信模版配置详情', 2351, 6, '2357', '2351', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sms-template:query', '#', 'zksr', '2024-05-09 11:40:17', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2356, '启用短信模版', 2351, 5, '2356', '2351', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sms-template:enable', '#', 'zksr', '2024-05-09 11:39:39', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2355, '停用短信模版', 2351, 4, '2355', '2351', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sms-template:disable', '#', 'zksr', '2024-05-09 11:38:56', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2354, '获得短信模版配置分页列表', 2351, 3, '2354', '2351', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sms-template:list', '#', 'zksr', '2024-05-09 11:38:24', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2353, '修改短信模版配置', 2351, 2, '2353', '2351', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sms-template:edit', '#', 'zksr', '2024-05-09 11:37:45', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2352, '新增短信模版配置', 2351, 1, '2352', '2351', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sms-template:add', '#', 'zksr', '2024-05-09 11:37:16', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2351, '短信模板', 2021, 2, '2351', '2021', 'smsTemplate', 'platform/smsTemplate/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2024-05-09 11:35:34', 'zksr', '2024-05-24 11:10:05', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2349, '充值记录', 2246, 5, '2349', '2246', 'account:recharge:list', NULL, NULL, 1, 0, 'F', '0', '0', 'account:recharge:list', '#', 'zksr', '2024-05-09 08:59:24', 'zksr', '2024-11-27 13:57:08', '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2348, '编辑优惠券', 2256, 6, '2348', '2256', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:edit', '#', 'zksr', '2024-05-08 17:13:35', 'zksr', '2024-05-11 11:37:21', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2347, '下架优惠券', 2256, 5, '2347', '2256', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:disable', '#', 'zksr', '2024-05-08 17:13:17', 'zksr', '2024-05-11 11:37:15', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2346, '发布优惠券', 2256, 4, '2346', '2256', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:enable', '#', 'zksr', '2024-05-08 17:13:01', 'zksr', '2024-05-11 11:37:08', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2345, '业务员提成权限', 2331, 5, '2345', '2331', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:orderSettle', '#', 'admin', '2024-05-08 09:40:53', '', NULL, '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2344, '订单权限', 2331, 4, '2344', '2331', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:order', '#', 'admin', '2024-05-08 09:40:53', '', NULL, '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2343, '客户权限', 2331, 3, '2343', '2331', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:member', '#', 'admin', '2024-05-08 09:40:53', '', NULL, '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2342, '账户权限', 2331, 2, '2342', '2331', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:account', '#', 'admin', '2024-05-08 09:40:53', '', NULL, '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2341, '扩店权限', 2331, 1, '2341', '2331', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:addBranch', '#', 'admin', '2024-05-08 09:40:53', '', NULL, '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2340, '获得平台品牌分页列表', 2339, 3, '2340', '2339', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:brand:list', '#', 'zksr', '2024-05-07 17:25:28', 'zksr', '2024-05-11 13:49:38', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2339, '新增优惠券模板', 2256, 1, '2339', '2256', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:add', '#', 'zksr', '2024-05-07 17:14:21', 'zksr', '2024-11-27 11:43:20', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2338, '获得优惠券模板详情', 2256, 2, '2338', '2256', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template:query', '#', 'zksr', '2024-05-07 16:56:58', 'zksr', '2024-05-11 11:36:18', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2337, '停用优惠券记录', 2255, 2, '2337', '2255', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:coupon:disable', '#', 'zksr', '2024-05-07 16:33:03', 'zksr', '2024-11-27 11:42:58', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2336, '业务员提成查询权限', 2331, 4, '2336', '2331', '', NULL, NULL, 1, 0, 'F', '0', '0', 'member:colonelApp:orderSettleQuery', '#', 'zksr', '2024-05-07 15:36:15', 'zksr', '2024-08-26 16:34:15', '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2335, '获得优惠券记录分页列表', 2255, 1, '2335', '2255', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:coupon:list', '#', 'zksr', '2024-05-07 15:21:02', 'zksr', '2024-11-27 11:43:03', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2334, '提现记录', 2331, 3, '2334', '2331', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-colonel:list', '#', 'zksr', '2024-05-06 17:25:24', 'zksr', '2024-08-26 16:34:11', '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2333, '新增提现单', 2331, 2, '2333', '2331', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-colonel:add', '#', 'zksr', '2024-05-06 17:24:55', 'zksr', '2024-08-26 16:34:08', '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2332, '业务员余额查询', 2331, 1, '2332', '2331', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:account:query-colonel', '#', 'zksr', '2024-05-06 17:24:23', 'zksr', '2024-08-26 16:34:06', '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2331, '业务员APP权限', 0, 1, '2331', '0', '#', NULL, NULL, 1, 0, 'C', '1', '0', '', '#', 'zksr', '2024-05-06 17:23:49', 'zksr', '2024-11-25 15:13:05', '', 'colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2330, '平台商展示分类状态变更', 2122, 6, '2330', '2122', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:saleClass:changeStatus', '#', 'zksr', '2024-05-06 16:28:05', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2329, '获取平台商账户余额', 2261, 3, '2329', '2261', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:account:query-partner', '#', 'zksr', '2024-04-30 17:29:11', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2328, '导入商品', 2131, 6, '2328', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:import', '#', 'zksr', '2024-04-29 14:56:35', 'zksr', '2024-11-27 11:13:13', '', 'supplier,partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2327, '修改入驻商授信金额', 2264, 4, '2327', '2264', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:account:update-supplier-credit', '#', 'zksr', '2024-04-29 10:07:31', 'zksr', '2024-11-27 11:26:44', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2326, '获得运营商账户分页列表', 2263, 5, '2326', '2263', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dc:accountList', '#', 'zksr', '2024-04-28 15:08:23', 'zksr', '2024-10-14 14:56:11', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2325, '获取运营商账户', 2263, 4, '2325', '2263', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:account:query-dc', '#', 'zksr', '2024-04-28 14:57:53', 'zksr', '2024-10-14 14:56:19', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2324, '用户注册信息审核', 2311, 6, '2324', '2311', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:memberRegister:audit', '#', 'admin', '2024-04-26 20:46:51', 'zksr', '2024-11-27 11:37:52', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2323, '门店注册信息审核', 2317, 6, '2323', '2317', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branchRegister:audit', '#', 'admin', '2024-04-26 20:46:49', 'zksr', '2024-11-27 11:36:23', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2322, '门店注册信息导出', 2317, 5, '2322', '2317', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branchRegister:export', '#', 'admin', '2024-04-26 19:57:54', 'zksr', '2024-08-08 10:03:12', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2321, '门店注册信息删除', 2317, 4, '2321', '2317', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branchRegister:remove', '#', 'admin', '2024-04-26 19:57:54', 'zksr', '2024-11-27 11:36:14', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2320, '门店注册信息修改', 2317, 3, '2320', '2317', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branchRegister:edit', '#', 'admin', '2024-04-26 19:57:54', 'zksr', '2024-11-27 11:36:08', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2319, '门店注册信息新增', 2317, 2, '2319', '2317', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branchRegister:add', '#', 'admin', '2024-04-26 19:57:54', 'zksr', '2024-11-27 11:36:02', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2318, '门店注册信息查询', 2317, 1, '2318', '2317', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branchRegister:query', '#', 'admin', '2024-04-26 19:57:54', 'zksr', '2024-08-08 10:01:47', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2317, '门店注册信息', 2192, 2, '2317', '2192', 'storeAudit', 'operation/storeAudit/index', NULL, 1, 0, 'C', '0', '0', 'member:branchRegister:list', '#', 'admin', '2024-04-26 19:57:54', 'zksr', '2024-08-08 10:01:45', '门店注册信息菜单', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2316, '用户注册信息导出', 2311, 5, '2316', '2311', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:memberRegister:export', '#', 'admin', '2024-04-26 19:57:46', 'zksr', '2024-08-08 10:02:40', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2315, '用户注册信息删除', 2311, 4, '2315', '2311', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:memberRegister:remove', '#', 'admin', '2024-04-26 19:57:46', 'zksr', '2024-11-27 11:37:44', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2314, '用户注册信息修改', 2311, 3, '2314', '2311', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:memberRegister:edit', '#', 'admin', '2024-04-26 19:57:46', 'zksr', '2024-11-27 11:37:39', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2313, '用户注册信息新增', 2311, 2, '2313', '2311', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:memberRegister:add', '#', 'admin', '2024-04-26 19:57:46', 'zksr', '2024-11-27 11:37:32', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2312, '用户注册信息查询', 2311, 1, '2312', '2311', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:memberRegister:query', '#', 'admin', '2024-04-26 19:57:46', 'zksr', '2024-08-08 10:02:31', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2311, '用户注册信息', 2192, 4, '2311', '2192', 'storeRegistration', 'operation/storeRegistration/index', NULL, 1, 0, 'C', '0', '0', 'member:memberRegister:list', '#', 'admin', '2024-04-26 19:57:46', 'zksr', '2024-08-08 10:02:27', '用户注册信息菜单', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2310, '新增入入驻商提现单', 2246, 4, '2310', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-supplier:add', '#', 'zksr', '2024-04-26 12:25:01', 'zksr', '2024-11-27 13:57:02', '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2309, '提现列表', 2246, 3, '2309', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-supplier:list', '#', 'zksr', '2024-04-26 11:14:27', 'zksr', '2024-11-27 13:56:58', '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2308, '入入驻商账户提现单分页列表', 2246, 1, '2308', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-supplier:list', '#', 'zksr', '2024-04-26 11:12:39', 'zksr', '2024-11-27 13:56:41', '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2307, '查询入驻商平台余额', 2246, 3, '2307', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:account:query-supplier-settle', '#', 'zksr', '2024-04-26 10:02:55', 'zksr', '2024-11-27 13:56:53', '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2306, '售后单审核', 2290, 2, '2306', '2290', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:after:approve', '#', 'zksr', '2024-04-25 21:19:07', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2305, '售后详情查询', 2290, 1, '2305', '2290', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:after:query', '#', 'zksr', '2024-04-25 21:04:24', 'zksr', '2024-10-30 10:54:10', '', 'supplier,partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2304, '平台商提现单结算重试', 2303, 1, '2304', '2303', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-partner:settle', '#', 'zksr', '2024-04-25 19:38:13', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2303, '平台商申请审核', 2031, 2, '2303', '2031', 'platformBusiness', 'finance/platformBusiness/index', NULL, 1, 0, 'C', '0', '0', ' account:withdraw-partner:list', '#', 'zksr', '2024-04-25 19:35:59', 'zksr', '2024-10-14 14:51:06', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2302, '门店注册信息', 2317, 1, '2302', '2317', 'storeRegistration', 'operation/storeRegistration/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2024-04-25 15:29:46', 'zksr', '2024-08-08 10:01:54', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2301, '品牌列表', 2155, 6, '2301', '2155', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:brand:list', '#', 'zksr', '2024-04-24 16:55:10', 'zksr', '2024-11-27 12:05:33', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2300, '售后详情', 2028, 1, '2300', '2028', 'salesAdministration/detail', 'orderManage/salesAdministration/detail', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2024-04-20 16:56:24', 'zksr', '2024-10-30 10:54:18', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2299, '售后详情', 2262, 5, '2299', '2262', 'detail', NULL, NULL, 1, 1, 'C', '1', '0', '', '#', 'zksr', '2024-04-20 16:54:21', 'zksr', '2024-11-27 11:44:13', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2297, '获取详情', 2288, 2, '2297', '2288', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:import:query', '#', 'zksr', '2024-04-18 10:13:42', '', NULL, '', 'supplier,dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2296, '获取列表数据', 2288, 1, '2296', '2288', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:import:list', '#', 'zksr', '2024-04-18 10:13:04', '', NULL, '', 'supplier,dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2295, '导入物流订单', 2262, 3, '2295', '2262', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:orderExpress:orderExportImport', '#', 'zksr', '2024-04-17 20:00:57', 'zksr', '2024-10-18 11:29:29', '', 'supplier,dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2294, '获取供应商下全国订单未发货的单据日期', 2262, 2, '2294', '2262', '', NULL, NULL, 1, 0, 'F', '0', '0', ' trade:order:list', '#', 'zksr', '2024-04-17 16:26:43', 'zksr', '2024-10-18 11:29:26', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2293, '导出', 2262, 1, '2293', '2262', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:orderExpress:orderExportExport', '#', 'zksr', '2024-04-17 16:16:26', 'zksr', '2024-10-18 11:29:20', '', 'dc,partner,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2292, '门店用户管理启用', 2194, 7, '2292', '2194', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:member:enable', '#', 'admin', '2024-04-17 09:55:35', 'zksr', '2024-11-27 11:37:18', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2291, '门店用户管理停用', 2194, 6, '2291', '2194', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:member:disable', '#', 'admin', '2024-04-17 09:55:35', 'zksr', '2024-11-27 11:37:12', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2290, '售后管理', 2028, 4, '2290', '2028', 'salesAdministration', 'orderManage/salesAdministration/index', NULL, 1, 0, 'C', '0', '0', ' trade:after:list', 'checkbox', 'zksr', '2024-04-16 17:53:06', 'zksr', '2024-10-30 10:54:02', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2288, '批量导入发货日志信息', 2028, 2, '2288', '2028', 'ImportLogs', 'orderManage/ImportLogs/index', NULL, 1, 0, 'C', '0', '0', 'trade:order:getOperatorOrderPageList', 'build', 'zksr', '2024-04-16 15:29:17', 'zksr', '2024-04-18 09:52:36', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2287, '获取查询入驻商消费流水', 2277, 1, '2287', '2277', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierAppOrder:getSupplierOrderSettle', '#', 'zksr', '2024-04-16 12:43:36', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2286, '审核驳回', 2263, 4, '2286', '2263', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-dc:disable', '#', 'zksr', '2024-04-15 21:04:07', 'zksr', '2024-11-27 11:25:38', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2285, '审核通过', 2263, 3, '2285', '2263', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-dc:enable', '#', 'zksr', '2024-04-15 21:03:33', 'zksr', '2024-11-27 11:24:37', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2284, '提现', 2263, 2, '2284', '2263', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-dc:add', '#', 'zksr', '2024-04-15 20:55:32', 'zksr', '2024-10-14 14:56:00', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2283, '提现', 2261, 2, '2283', '2261', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-partner:add', '#', 'zksr', '2024-04-15 20:54:57', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2282, '提现历史', 2263, 1, '2282', '2263', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-dc:list', '#', 'zksr', '2024-04-15 20:51:01', 'zksr', '2024-10-14 14:55:57', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2281, '提现历史', 2261, 1, '2281', '2261', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:withdraw-partner:list', '#', 'zksr', '2024-04-15 20:50:34', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2280, '获取入驻商小程序订单详情', 2277, 3, '2280', '2277', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierAppOrder:getSupplierOrderDtl', '#', 'zksr', '2024-04-13 17:55:55', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2279, '获取入驻商小程序订单分页列表', 2277, 2, '2279', '2277', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierAppOrder:getMerchantMiniProgramOrderPageList', '#', 'zksr', '2024-04-13 17:55:40', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2278, '获取首页数据', 2277, 1, '2278', '2277', '', NULL, NULL, 1, 0, 'F', '0', '0', 'trade:supplierAppOrder:getHomePage', '#', 'zksr', '2024-04-13 17:55:02', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2277, '订单管理', 2276, 1, '2277', '2276', '/', NULL, NULL, 1, 0, 'C', '1', '0', NULL, '#', 'zksr', '2024-04-13 17:54:08', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2276, '入驻商小程序', 0, 99, '2276', '0', '/', NULL, NULL, 1, 0, 'M', '1', '0', NULL, '#', 'zksr', '2024-04-13 17:53:45', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2275, '分润提现审核	', 2031, 9, '2275', '2031', 'profitAuditing', 'finance/profitAuditing/index', NULL, 1, 0, 'C', '1', '1', '', 'client', 'zksr', '2024-04-13 17:35:55', 'zksr', '2024-11-27 13:59:07', '', 'partner,dc,supplier,colonel');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2274, '运营商申请审核', 2031, 4, '2274', '2031', 'operatorsAuditing', 'finance/operatorsAuditing/index', NULL, 1, 0, 'C', '0', '0', '', 'chart', 'zksr', '2024-04-13 17:11:13', 'zksr', '2024-10-14 14:55:02', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2273, '业务员账户', 2031, 7, '2273', '2031', 'salespersonAccount', 'finance/salespersonAccount/index', NULL, 1, 0, 'C', '0', '0', 'system:dc:accountList', 'date-range', 'zksr', '2024-04-13 16:09:45', 'zksr', '2024-10-14 14:52:35', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2265, '客户账户', 2031, 6, '2265', '2031', 'customerAccount', 'finance/customerAccount/index', NULL, 1, 0, 'C', '0', '0', ' member:branch:accountList', 'cascader', 'zksr', '2024-04-12 09:34:23', 'zksr', '2024-10-14 14:51:47', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2264, '入驻商账户', 2031, 5, '2264', '2031', 'entrantsAccount', 'finance/entrantsAccount/index', NULL, 1, 0, 'C', '0', '0', 'system:supplier:accountList', 'color', 'zksr', '2024-04-12 09:17:43', 'zksr', '2024-11-27 11:27:13', '', 'supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2263, '运营商账户', 2031, 3, '2263', '2031', 'operatorAccount', 'finance/operatorAccount/index', NULL, 1, 0, 'C', '0', '0', ' account:withdraw-dc:list', 'date-range', 'zksr', '2024-04-11 18:35:37', 'zksr', '2024-10-14 14:55:18', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2262, '订单管理', 2028, 1, '2262', '2028', 'orderManage', 'orderManage/operationOrder/index', NULL, 1, 0, 'C', '0', '0', 'trade:order:getOperatorOrderPageList', 'date-range', 'zksr', '2024-04-11 16:42:59', 'zksr', '2024-10-18 11:29:15', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2261, '平台商账户', 2031, 1, '2261', '2031', 'finance', 'finance/platformAccount/index', NULL, 1, 0, 'C', '0', '0', ' system:partner:accountList', 'documentation', 'zksr', '2024-04-11 14:46:29', 'zksr', '2024-10-14 14:48:35', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2259, '渠道下拉', 2256, 3, '2259', '2256', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:channel:list', '#', 'zksr', '2024-04-09 09:44:42', 'zksr', '2024-05-11 11:37:00', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2256, '优惠劵列表', 2027, 2, '2256', '2027', 'preferential', 'operation/preferential/index', '{\"source\": 1}', 1, 0, 'C', '0', '0', '', 'cascader', 'zksr', '2024-04-08 10:38:45', 'zksr', '2024-05-27 14:05:40', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2255, '优惠劵领取记录', 2027, 1, '2255', '2027', 'preferentialRecord', 'operation/preferentialRecord/index', NULL, 1, 0, 'C', '0', '0', '', 'cascader', 'zksr', '2024-04-08 10:37:20', 'zksr', '2024-11-27 11:43:09', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2250, '平台管理分类状态变更', 2110, 7, '2250', '2110', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:catgory:changeStatus', '#', 'zksr', '2024-04-01 15:34:06', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2249, '平台商配置获取', 2000, 5, '2249', '2000', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerConfig:getPartnerConfig', '#', 'zksr', '2024-03-28 17:43:05', 'zksr', '2024-11-27 11:01:34', '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2248, '查看账户余额', 2246, 2, '2248', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:account:queryBySupplier', '#', 'zksr', '2024-03-25 19:08:46', '', NULL, '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2247, '入驻商充值', 2246, 1, '2247', '2246', '', NULL, NULL, 1, 0, 'F', '0', '0', 'account:recharge:addBySupplier', '#', 'zksr', '2024-03-25 19:00:00', 'zksr', '2024-11-27 13:56:47', '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2246, '入驻商小程序权限', 0, 1, '2246', '0', 'supplierAppletRole', NULL, NULL, 1, 0, 'C', '1', '0', '', '#', 'zksr', '2024-03-25 18:59:15', 'zksr', '2024-11-27 10:52:59', '', 'supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2245, '获取运营商配置信息', 2243, 2, '2245', '2243', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerPolicy:query', '#', 'zksr', '2024-03-23 18:28:38', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2244, '新增运行商配置', 2243, 1, '2244', '2243', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerPolicy:add', '#', 'zksr', '2024-03-23 18:27:59', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2243, '运营商基础设置', 2017, 0, '2243', '2017', 'settingsBasis', 'operation/settingsBasis/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2024-03-23 18:27:26', 'zksr', '2024-05-15 17:20:37', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2242, '获取平台基本信息', 2240, 2, '2242', '2240', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerPolicy:query', '#', 'zksr', '2024-03-23 18:26:45', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2241, '新增平台配置信息', 2240, 1, '2241', '2240', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerPolicy:add', '#', 'zksr', '2024-03-23 18:26:13', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2240, '平台基础设置', 2017, 0, '2240', '2017', 'settings', 'platform/settings/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2024-03-23 18:25:29', 'zksr', '2024-11-27 10:54:44', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2238, '运营商获取商品管理分类详情', 2236, 2, '2238', '2236', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:catgoryRate:getRateInfo', '#', 'zksr', '2024-03-22 19:33:30', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2237, '设置扣点', 2236, 1, '2237', '2236', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:catgoryRate:installRate', '#', 'zksr', '2024-03-22 19:33:07', '', NULL, '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2236, '商品类别', 2017, 1, '2236', '2017', 'classifier', 'operation/classifier/index', NULL, 1, 0, 'C', '0', '1', 'product:catgoryRate:getCatgoryRateList', '#', 'zksr', '2024-03-22 19:32:51', 'zksr', '2024-11-27 11:09:12', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2228, '门店用户管理导出', 2194, 5, '2228', '2194', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:member:export', '#', 'admin', '2024-03-21 11:22:45', 'zksr', '2024-08-08 10:02:05', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2227, '门店用户管理删除', 2194, 4, '2227', '2194', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:member:remove', '#', 'admin', '2024-03-21 11:22:45', 'zksr', '2024-11-27 11:37:01', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2226, '门店用户管理修改', 2194, 3, '2226', '2194', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:member:edit', '#', 'admin', '2024-03-21 11:22:45', 'zksr', '2024-11-27 11:36:55', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2225, '门店用户管理新增', 2194, 2, '2225', '2194', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:member:add', '#', 'admin', '2024-03-21 11:22:45', 'zksr', '2024-11-27 11:36:48', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2224, '门店用户管理查询', 2194, 1, '2224', '2194', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:member:query', '#', 'admin', '2024-03-21 11:22:45', 'zksr', '2024-08-08 10:00:50', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2223, '业务员关系导出', 2059, 5, '2223', '2059', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:relation:export', '#', 'admin', '2024-03-21 11:18:08', 'zksr', '2024-11-27 11:40:14', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2222, '业务员关系删除', 2059, 4, '2222', '2059', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:relation:remove', '#', 'admin', '2024-03-21 11:18:08', 'zksr', '2024-11-27 11:40:08', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2221, '业务员关系修改', 2059, 3, '2221', '2059', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:relation:edit', '#', 'admin', '2024-03-21 11:18:08', 'zksr', '2024-11-27 11:40:03', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2220, '业务员关系新增', 2059, 2, '2220', '2059', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:relation:add', '#', 'admin', '2024-03-21 11:18:08', 'zksr', '2024-11-27 11:39:58', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2219, '业务员关系查询', 2059, 1, '2219', '2059', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:relation:query', '#', 'admin', '2024-03-21 11:18:08', 'zksr', '2024-08-20 12:49:28', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2218, '门店信息审核', 2193, 8, '2218', '2193', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branch:audit', '#', 'admin', '2024-03-21 11:16:30', 'zksr', '2024-11-27 11:35:16', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2217, '门店信息启用', 2193, 7, '2217', '2193', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branch:enable', '#', 'admin', '2024-03-21 11:16:30', 'zksr', '2024-11-27 11:34:53', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2216, '门店信息停用', 2193, 6, '2216', '2193', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branch:disable', '#', 'admin', '2024-03-21 11:16:30', 'zksr', '2024-11-27 11:34:46', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2215, '门店信息导出', 2193, 5, '2215', '2193', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branch:export', '#', 'admin', '2024-03-21 11:16:30', 'zksr', '2024-08-08 09:59:35', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2214, '门店信息删除', 2193, 4, '2214', '2193', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branch:remove', '#', 'admin', '2024-03-21 11:16:30', 'zksr', '2024-11-27 11:34:38', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2213, '门店信息修改', 2193, 3, '2213', '2193', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branch:edit', '#', 'admin', '2024-03-21 11:16:30', 'zksr', '2024-11-27 11:34:32', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2212, '门店信息新增', 2193, 2, '2212', '2193', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branch:add', '#', 'admin', '2024-03-21 11:16:30', 'zksr', '2024-11-27 11:34:23', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2211, '门店信息查询', 2193, 1, '2211', '2193', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:branch:query', '#', 'admin', '2024-03-21 11:16:30', 'zksr', '2024-08-08 09:49:37', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2210, '业务员目标设置导启用', 2058, 7, '2210', '2058', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonelTarget:enable', '#', 'admin', '2024-03-21 11:15:39', 'zksr', '2024-11-27 11:39:45', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2209, '业务员目标设置停用', 2058, 6, '2209', '2058', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonelTarget:disable', '#', 'admin', '2024-03-21 11:15:39', 'zksr', '2024-11-27 11:39:31', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2208, '业务员目标设置导出', 2058, 5, '2208', '2058', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonelTarget:export', '#', 'admin', '2024-03-21 11:15:39', 'zksr', '2024-08-20 12:47:35', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2207, '业务员目标设置删除', 2058, 4, '2207', '2058', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonelTarget:remove', '#', 'admin', '2024-03-21 11:15:39', 'zksr', '2024-11-27 11:39:22', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2206, '业务员目标设置修改', 2058, 3, '2206', '2058', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonelTarget:edit', '#', 'admin', '2024-03-21 11:15:39', 'zksr', '2024-11-27 11:39:18', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2205, '业务员目标设置新增', 2058, 2, '2205', '2058', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonelTarget:add', '#', 'admin', '2024-03-21 11:15:39', 'zksr', '2024-11-27 11:39:13', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2204, '业务员目标设置查询', 2058, 1, '2204', '2058', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonelTarget:query', '#', 'admin', '2024-03-21 11:15:39', 'zksr', '2024-08-20 12:52:16', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2203, '业务员信息启用', 2057, 7, '2203', '2057', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonel:enable', '#', 'admin', '2024-03-21 11:06:57', 'zksr', '2024-11-27 11:38:55', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2202, '业务员信息停用', 2057, 6, '2202', '2057', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonel:disable', '#', 'admin', '2024-03-21 11:06:57', 'zksr', '2024-11-27 11:38:49', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2201, '业务员信息导出', 2057, 5, '2201', '2057', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonel:export', '#', 'admin', '2024-03-21 11:06:57', 'zksr', '2024-08-20 12:46:55', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2200, '业务员信息删除', 2057, 4, '2200', '2057', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonel:remove', '#', 'admin', '2024-03-21 11:06:57', 'zksr', '2024-11-27 11:38:41', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2199, '业务员信息修改', 2057, 3, '2199', '2057', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonel:edit', '#', 'admin', '2024-03-21 11:06:57', 'zksr', '2024-11-27 11:38:36', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2198, '业务员信息新增', 2057, 2, '2198', '2057', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonel:add', '#', 'admin', '2024-03-21 11:06:57', 'zksr', '2024-11-27 11:38:29', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2197, '业务员信息查询', 2057, 1, '2197', '2057', '#', '', NULL, 1, 0, 'F', '0', '0', 'member:colonel:query', '#', 'admin', '2024-03-21 11:06:57', 'zksr', '2024-10-18 09:33:16', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2194, '门店用户管理', 2192, 3, '2194', '2192', 'shopUserMgmt', 'operation/shopUserMgmt/index', NULL, 1, 0, 'C', '0', '0', 'member:member:list', 'documentation', 'zksr', '2024-03-12 10:43:48', 'zksr', '2024-08-08 10:00:25', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2193, '门店信息', 2192, 1, '2193', '2192', 'shopInformation', 'operation/shopInformation/index', NULL, 1, 0, 'C', '0', '0', 'member:branch:list', 'documentation', 'zksr', '2024-03-12 10:43:48', 'zksr', '2024-08-08 09:53:54', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2192, '门店管理', 0, 4, '2192', '0', 'branch', NULL, NULL, 1, 0, 'M', '0', '0', '', 'documentation', 'zksr', '2024-03-12 09:38:57', 'zksr', '2024-08-08 09:53:29', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2191, '平台商配置(由软件商设置)新增', 2000, 2, '2191', '2000', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:partnerConfig:add', '#', 'admin', '2024-03-14 17:59:33', 'zksr', '2024-11-27 11:00:41', '', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2190, '全国价格方案导出', 2185, 5, '2190', '2185', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:export', '#', 'admin', '2024-03-14 17:21:46', 'zksr', '2024-03-14 17:37:54', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2189, '全国价格方案删除', 2185, 4, '2189', '2185', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:remove', '#', 'admin', '2024-03-14 17:21:46', 'zksr', '2024-03-14 17:37:44', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2188, '全国价格方案修改', 2185, 3, '2188', '2185', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:edit', '#', 'admin', '2024-03-14 17:21:46', 'zksr', '2024-03-14 17:37:35', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2187, '全国价格方案新增', 2185, 2, '2187', '2185', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:add', '#', 'admin', '2024-03-14 17:21:46', 'zksr', '2024-03-14 17:37:29', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2186, '全国价格方案查询', 2185, 1, '2186', '2185', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:query', '#', 'admin', '2024-03-14 17:21:46', 'zksr', '2024-03-14 17:37:24', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2185, '全国价格方案', 2017, 7, '2185', '2017', 'priceProgramme', 'platform/priceProgramme/index', '', 1, 0, 'C', '0', '0', 'product:price:list', '#', 'admin', '2024-03-14 17:21:46', 'zksr', '2024-05-15 17:18:28', '全国价格方案菜单', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2184, '平台商城市分组价格导出', 2179, 5, '2184', '2179', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:export', '#', 'admin', '2024-03-14 17:15:41', 'zksr', '2024-03-14 17:37:17', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2183, '平台商城市分组价格删除', 2179, 4, '2183', '2179', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:remove', '#', 'admin', '2024-03-14 17:15:41', 'zksr', '2024-03-14 17:37:12', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2182, '平台商城市分组价格修改', 2179, 3, '2182', '2179', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:edit', '#', 'admin', '2024-03-14 17:15:41', 'zksr', '2024-03-14 17:37:05', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2181, '平台商城市分组价格新增', 2179, 2, '2181', '2179', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:add', '#', 'admin', '2024-03-14 17:15:41', 'zksr', '2024-03-14 17:36:59', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2180, '平台商城市分组价格查询', 2179, 1, '2180', '2179', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:query', '#', 'admin', '2024-03-14 17:15:41', 'zksr', '2024-03-14 17:36:53', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2179, '全国价格分配设置', 2017, 8, '2179', '2017', 'priceAllocation', 'platform/priceAllocation/index', NULL, 1, 0, 'C', '0', '0', 'product:price:list', '#', 'admin', '2024-03-14 17:15:41', 'zksr', '2024-05-15 17:18:21', '平台商城市分组价格菜单', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2178, '城市渠道价格导出', 2173, 5, '2178', '2173', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:export', '#', 'admin', '2024-03-14 17:15:37', 'zksr', '2024-03-14 17:36:42', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2177, '城市渠道价格删除', 2173, 4, '2177', '2173', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:remove', '#', 'admin', '2024-03-14 17:15:37', 'zksr', '2024-03-14 17:47:45', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2176, '城市渠道价格修改', 2173, 3, '2176', '2173', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:edit', '#', 'admin', '2024-03-14 17:15:37', 'zksr', '2024-03-14 17:36:33', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2175, '城市渠道价格新增', 2173, 2, '2175', '2173', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:add', '#', 'admin', '2024-03-14 17:15:37', 'zksr', '2024-03-14 17:36:27', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2174, '城市渠道价格查询', 2173, 1, '2174', '2173', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:query', '#', 'admin', '2024-03-14 17:15:37', 'zksr', '2024-03-14 17:36:05', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2173, '城市渠道价格设置', 2017, 6, '2173', '2017', 'cityPrice', 'platform/cityPrice/index', NULL, 1, 0, 'C', '0', '0', 'product:price:list', '#', 'admin', '2024-03-14 17:15:37', 'zksr', '2024-05-15 17:18:08', '城市渠道价格菜单', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2172, 'sku销售价导出', 2167, 5, '2172', '2167', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:export', '#', 'admin', '2024-03-14 17:15:33', 'zksr', '2024-03-14 17:35:56', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2171, 'sku销售价删除', 2167, 4, '2171', '2167', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:remove', '#', 'admin', '2024-03-14 17:15:33', 'zksr', '2024-03-14 17:35:51', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2170, 'sku销售价修改', 2167, 3, '2170', '2167', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:edit', '#', 'admin', '2024-03-14 17:15:33', 'zksr', '2024-03-14 17:35:45', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2169, 'sku销售价新增', 2167, 2, '2169', '2167', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:add', '#', 'admin', '2024-03-14 17:15:33', 'zksr', '2024-03-14 17:35:37', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2168, 'sku销售价查询', 2167, 1, '2168', '2167', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:price:query', '#', 'admin', '2024-03-14 17:15:33', 'zksr', '2024-03-14 17:35:30', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2167, '城市价格方案', 2017, 5, '2167', '2017', 'priceCityProgramme', 'platform/priceCityProgramme/index', NULL, 1, 0, 'C', '0', '0', 'product:price:list', '#', 'admin', '2024-03-14 17:15:33', 'zksr', '2024-05-15 17:17:57', 'sku销售价菜单', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2166, '入驻商上架商品导出', 2161, 5, '2166', '2161', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:item:export', '#', 'admin', '2024-03-14 17:15:30', 'zksr', '2024-03-14 17:39:52', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2165, '入驻商上架商品删除', 2161, 4, '2165', '2161', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:item:remove', '#', 'admin', '2024-03-14 17:15:30', 'zksr', '2024-03-14 17:39:44', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2164, '入驻商上架商品修改', 2161, 3, '2164', '2161', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:item:edit', '#', 'admin', '2024-03-14 17:15:30', 'zksr', '2024-03-14 17:39:35', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2163, '入驻商上架商品新增', 2161, 2, '2163', '2161', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:item:add', '#', 'admin', '2024-03-14 17:15:30', 'zksr', '2024-03-14 17:39:25', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2162, '入驻商上架商品查询', 2161, 1, '2162', '2161', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:item:query', '#', 'admin', '2024-03-14 17:15:30', 'zksr', '2024-03-14 17:39:17', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2161, '全国上下架管理', 2024, 1, '2161', '2024', 'item', 'platform/nationalManagement/index', NULL, 1, 0, 'C', '0', '0', 'product:item:list', '#', 'admin', '2024-03-14 17:15:30', 'zksr', '2024-11-13 19:35:35', '入驻商上架商品菜单', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2160, '城市上架商品导出', 2155, 5, '2160', '2155', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:item:export', '#', 'admin', '2024-03-14 17:15:27', 'zksr', '2024-03-14 17:39:06', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2159, '城市上架商品删除', 2155, 4, '2159', '2155', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:item:remove', '#', 'admin', '2024-03-14 17:15:27', 'zksr', '2024-03-14 17:39:01', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2158, '城市上架商品修改', 2155, 3, '2158', '2155', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:item:edit', '#', 'admin', '2024-03-14 17:15:27', 'zksr', '2024-03-14 17:38:56', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2157, '城市上架商品新增', 2155, 2, '2157', '2155', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:item:add', '#', 'admin', '2024-03-14 17:15:27', 'zksr', '2024-03-14 17:38:51', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2156, '城市上架商品查询', 2155, 1, '2156', '2155', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:item:query', '#', 'admin', '2024-03-14 17:15:27', 'zksr', '2024-03-14 17:38:45', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2155, '城市上架商品', 2024, 1, '2155', '2024', 'cityShelves', 'platform/cityShelves/index', NULL, 1, 0, 'C', '0', '0', 'product:item:list', '#', 'admin', '2024-03-14 17:15:27', 'zksr', '2024-03-23 18:30:44', '城市上架商品菜单', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2154, '规格值导出', 2149, 5, '2154', '2149', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:val:export', '#', 'admin', '2024-03-14 17:15:11', 'zksr', '2024-03-14 17:35:21', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2153, '规格值删除', 2149, 4, '2153', '2149', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:val:remove', '#', 'admin', '2024-03-14 17:15:11', 'zksr', '2024-03-14 17:35:14', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2152, '规格值修改', 2149, 3, '2152', '2149', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:val:edit', '#', 'admin', '2024-03-14 17:15:11', 'zksr', '2024-03-14 17:35:05', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2151, '规格值新增', 2149, 2, '2151', '2149', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:val:add', '#', 'admin', '2024-03-14 17:15:11', 'zksr', '2024-03-14 17:34:59', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2150, '规格值查询', 2149, 1, '2150', '2149', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:val:query', '#', 'admin', '2024-03-14 17:15:11', 'zksr', '2024-03-14 17:34:53', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2149, '规格值', 2017, 1, '2149', '2017', 'val', 'product/val/index', NULL, 1, 0, 'C', '1', '1', 'product:val:list', '#', 'admin', '2024-03-14 17:15:11', 'zksr', '2024-03-22 17:51:38', '规格值菜单', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2148, '规格名称导出', 2143, 5, '2148', '2143', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:property:export', '#', 'admin', '2024-03-14 17:15:07', 'zksr', '2024-03-14 17:34:45', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2147, '规格名称删除', 2143, 4, '2147', '2143', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:property:remove', '#', 'admin', '2024-03-14 17:15:07', 'zksr', '2024-03-14 17:34:36', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2146, '规格名称修改', 2143, 3, '2146', '2143', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:property:edit', '#', 'admin', '2024-03-14 17:15:07', 'zksr', '2024-03-14 17:34:30', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2145, '规格名称新增', 2143, 2, '2145', '2143', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:property:add', '#', 'admin', '2024-03-14 17:15:07', 'zksr', '2024-03-14 17:34:24', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2144, '规格名称查询', 2143, 1, '2144', '2143', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:property:query', '#', 'admin', '2024-03-14 17:15:07', 'zksr', '2024-03-14 17:34:18', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2143, '规格名称', 2017, 1, '2143', '2017', 'classifier', 'operation/classifier/index', NULL, 1, 0, 'C', '1', '1', '', '#', 'admin', '2024-03-14 17:15:07', 'zksr', '2024-03-22 17:51:30', '规格名称菜单', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2142, '商品SKU导出', 2137, 5, '2142', '2137', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:sku:export', '#', 'admin', '2024-03-14 17:14:59', 'zksr', '2024-03-14 17:34:10', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2141, '商品SKU删除', 2137, 4, '2141', '2137', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:sku:remove', '#', 'admin', '2024-03-14 17:14:59', 'zksr', '2024-03-14 17:34:04', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2140, '商品SKU修改', 2137, 3, '2140', '2137', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:sku:edit', '#', 'admin', '2024-03-14 17:14:59', 'zksr', '2024-03-14 17:33:58', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2139, '商品SKU新增', 2137, 2, '2139', '2137', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:sku:add', '#', 'admin', '2024-03-14 17:14:59', 'zksr', '2024-03-14 17:33:52', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2138, '商品SKU查询', 2137, 1, '2138', '2137', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:sku:query', '#', 'admin', '2024-03-14 17:14:59', 'zksr', '2024-03-14 17:33:45', '', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2137, '商品SKU', 2017, 1, '2137', '2017', 'sku', 'product/sku/index', NULL, 1, 0, 'C', '1', '1', 'product:sku:list', '#', 'admin', '2024-03-14 17:14:59', 'zksr', '2024-03-22 17:51:12', '商品SKU菜单', 'dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2136, '商品SPU导出', 2131, 5, '2136', '2131', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:spu:export', '#', 'admin', '2024-03-14 17:14:02', 'zksr', '2024-08-08 09:47:33', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2135, '商品SPU删除', 2131, 4, '2135', '2131', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:spu:remove', '#', 'admin', '2024-03-14 17:14:02', 'zksr', '2024-08-08 09:47:42', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2134, '商品SPU修改', 2131, 3, '2134', '2131', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:spu:edit', '#', 'admin', '2024-03-14 17:14:02', 'zksr', '2024-08-08 09:52:32', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2133, '商品SPU新增', 2131, 2, '2133', '2131', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:spu:add', '#', 'admin', '2024-03-14 17:14:02', 'zksr', '2024-08-08 09:47:37', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2132, '商品SPU查询', 2131, 1, '2132', '2131', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:spu:query', '#', 'admin', '2024-03-14 17:14:02', 'zksr', '2024-08-08 09:46:55', '', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2131, '商品基本信息', 2017, 3, '2131', '2017', 'shopcot', 'operation/shopcot/index', NULL, 1, 0, 'C', '0', '0', 'product:spu:list', '#', 'admin', '2024-03-14 17:14:02', 'zksr', '2024-08-08 09:59:19', '商品SPU菜单', 'dc,supplier,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2127, '平台商展示分类导出', 2122, 5, '2127', '2122', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:saleClass:export', '#', 'admin', '2024-03-12 11:08:58', 'zksr', '2024-03-12 15:28:10', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2126, '平台商展示分类删除', 2122, 4, '2126', '2122', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:saleClass:remove', '#', 'admin', '2024-03-12 11:08:58', 'zksr', '2024-03-12 15:28:04', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2125, '平台商展示分类修改', 2122, 3, '2125', '2122', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:saleClass:edit', '#', 'admin', '2024-03-12 11:08:58', 'zksr', '2024-03-12 15:27:58', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2124, '平台商展示分类新增', 2122, 2, '2124', '2122', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:saleClass:add', '#', 'admin', '2024-03-12 11:08:58', 'zksr', '2024-03-12 15:27:53', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2123, '平台商展示分类查询', 2122, 1, '2123', '2122', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:saleClass:query', '#', 'admin', '2024-03-12 11:08:58', 'zksr', '2024-03-14 16:08:46', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2122, '平台商展示分类', 2024, 17, '2122', '2024', 'showCategory', 'platform/showCategory/index', NULL, 1, 0, 'C', '0', '0', 'product:saleClass:list', '#', 'admin', '2024-03-12 11:08:58', 'zksr', '2024-11-13 19:39:20', '平台商展示分类菜单', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2121, '城市级管理分类扣点设置导出', 2116, 5, '2121', '2116', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:catgoryRate:export', '#', 'admin', '2024-03-12 11:07:24', 'zksr', '2024-11-27 11:21:33', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2120, '城市级管理分类扣点设置删除', 2116, 4, '2120', '2116', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:catgoryRate:remove', '#', 'admin', '2024-03-12 11:07:24', 'zksr', '2024-11-27 11:21:02', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2119, '城市级管理分类扣点设置修改', 2116, 3, '2119', '2116', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:catgoryRate:edit', '#', 'admin', '2024-03-12 11:07:24', 'zksr', '2024-11-27 11:19:38', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2118, '城市级管理分类扣点设置新增', 2116, 2, '2118', '2116', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:catgoryRate:installRate', '#', 'admin', '2024-03-12 11:07:24', 'zksr', '2024-11-27 11:19:01', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2117, '城市级管理分类扣点设置查询', 2116, 1, '2117', '2116', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:catgoryRate:getRateInfo', '#', 'admin', '2024-03-12 11:07:24', 'zksr', '2024-11-27 14:06:45', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2116, '设置扣点', 2017, 24, '2116', '2017', 'classifier', 'operation/classifier/index', NULL, 1, 0, 'C', '0', '0', ' product:catgoryRate:getCatgoryRateList', '#', 'admin', '2024-03-12 11:07:24', 'zksr', '2024-11-27 14:08:03', '城市级管理分类扣点设置菜单', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2115, '平台商管理分类导出', 2110, 5, '2115', '2110', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:catgory:export', '#', 'admin', '2024-03-12 11:04:12', 'zksr', '2024-03-12 15:18:54', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2114, '平台商管理分类删除', 2110, 4, '2114', '2110', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:catgory:remove', '#', 'admin', '2024-03-12 11:04:12', 'zksr', '2024-03-12 15:18:38', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2113, '平台商管理分类修改', 2110, 3, '2113', '2110', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:catgory:edit', '#', 'admin', '2024-03-12 11:04:12', 'zksr', '2024-11-27 11:15:47', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2112, '平台商管理分类新增', 2110, 2, '2112', '2110', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:catgory:add', '#', 'admin', '2024-03-12 11:04:12', 'zksr', '2024-03-12 15:18:13', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2111, '平台商管理分类查询', 2110, 6, '2111', '2110', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:catgory:query', '#', 'admin', '2024-03-12 11:04:12', 'zksr', '2024-04-24 16:20:48', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2110, '平台管理类别', 2017, 14, '2110', '2017', 'catgory', 'platform/category/index', NULL, 1, 0, 'C', '0', '0', 'product:catgory:list', '#', 'admin', '2024-03-12 11:04:12', 'zksr', '2024-08-22 16:27:15', '平台商管理分类菜单', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2109, '品牌启用', 2102, 5, '2109', '2102', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:brand:enable', '#', 'admin', '2024-03-12 11:03:19', 'zksr', '2024-11-27 11:11:29', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2108, '品牌停用', 2102, 4, '2108', '2102', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:brand:disable', '#', 'admin', '2024-03-12 11:03:19', 'zksr', '2024-11-27 11:11:13', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2107, '品牌导出', 2102, 5, '2107', '2102', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:brand:export', '#', 'admin', '2024-03-12 11:03:19', 'zksr', '2024-11-27 11:11:21', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2106, '品牌删除', 2102, 4, '2106', '2102', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:brand:remove', '#', 'admin', '2024-03-12 11:03:19', 'zksr', '2024-11-27 11:11:07', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2105, '品牌修改', 2102, 3, '2105', '2102', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:brand:edit', '#', 'admin', '2024-03-12 11:03:19', 'zksr', '2024-11-27 11:11:00', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2104, '品牌新增', 2102, 2, '2104', '2102', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:brand:add', '#', 'admin', '2024-03-12 11:03:19', 'zksr', '2024-11-27 11:10:50', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2103, '品牌查询', 2102, 1, '2103', '2102', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:brand:query', '#', 'admin', '2024-03-12 11:03:19', 'zksr', '2024-11-27 11:10:38', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2102, '品牌管理', 2017, 2, '2102', '2017', 'branding', 'platform/branding/index', NULL, 1, 0, 'C', '0', '0', 'product:brand:list', '#', 'admin', '2024-03-12 11:03:19', 'zksr', '2024-05-15 17:18:46', '平台品牌菜单', 'partner,supplier,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2101, '城市展示分类导出', 2096, 5, '2101', '2096', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:areaClass:export', '#', 'admin', '2024-03-12 11:02:25', 'zksr', '2024-03-12 15:33:14', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2100, '城市展示分类删除', 2096, 4, '2100', '2096', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:areaClass:remove', '#', 'admin', '2024-03-12 11:02:25', 'zksr', '2024-03-12 15:33:09', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2099, '城市展示分类修改', 2096, 3, '2099', '2096', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:areaClass:edit', '#', 'admin', '2024-03-12 11:02:25', 'zksr', '2024-03-12 15:33:04', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2098, '城市展示分类新增', 2096, 2, '2098', '2096', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:areaClass:add', '#', 'admin', '2024-03-12 11:02:25', 'zksr', '2024-03-12 15:32:56', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2097, '城市展示分类查询', 2096, 1, '2097', '2096', '#', '', NULL, 1, 0, 'F', '0', '0', 'product:areaClass:query', '#', 'admin', '2024-03-12 11:02:25', 'zksr', '2024-03-12 15:32:44', '', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2096, '运营商展示分类', 2024, 34, '2096', '2024', 'xinshop', 'operation/xinshop/index', NULL, 1, 0, 'C', '0', '0', 'product:areaClass:list', '#', 'admin', '2024-03-12 11:02:25', 'zksr', '2024-11-27 11:32:16', '城市展示分类菜单', 'dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2095, '入驻商信息导出', 2090, 5, '2095', '2090', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:supplier:export', '#', 'admin', '2024-03-12 10:59:42', 'zksr', '2024-03-12 15:27:15', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2094, '入驻商信息删除', 2090, 4, '2094', '2090', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:supplier:remove', '#', 'admin', '2024-03-12 10:59:42', 'zksr', '2024-03-12 15:27:10', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2093, '入驻商信息修改', 2090, 3, '2093', '2090', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:supplier:edit', '#', 'admin', '2024-03-12 10:59:42', 'zksr', '2024-03-12 15:26:43', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2092, '入驻商信息新增', 2090, 2, '2092', '2090', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:supplier:add', '#', 'admin', '2024-03-12 10:59:42', 'zksr', '2024-03-12 15:26:37', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2091, '入驻商信息查询', 2090, 1, '2091', '2090', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:supplier:query', '#', 'admin', '2024-03-12 10:59:42', 'zksr', '2024-03-12 15:26:31', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2090, '入驻商信息', 2017, 16, '2090', '2017', 'supplier', 'platform/supplier/index', NULL, 1, 0, 'C', '0', '0', 'system:supplier:list', '#', 'admin', '2024-03-12 10:59:42', 'zksr', '2024-03-12 15:26:12', '入驻商信息菜单', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2089, '运营商启用', 2082, 6, '2089', '2082', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:dc:enable', '#', 'admin', '2024-03-12 10:59:08', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2088, '运营商停用', 2082, 5, '2088', '2082', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:dc:disable', '#', 'admin', '2024-03-12 10:59:08', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2087, '运营商导出', 2082, 5, '2087', '2082', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:dc:export', '#', 'admin', '2024-03-12 10:59:08', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2086, '运营商删除', 2082, 4, '2086', '2082', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:dc:remove', '#', 'admin', '2024-03-12 10:59:08', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2085, '运营商修改', 2082, 3, '2085', '2082', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:dc:edit', '#', 'admin', '2024-03-12 10:59:08', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2084, '运营商新增', 2082, 2, '2084', '2082', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:dc:add', '#', 'admin', '2024-03-12 10:59:08', '', NULL, '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2083, '运营商查询', 2082, 1, '2083', '2082', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:dc:query', '#', 'admin', '2024-03-12 10:59:08', 'zksr', '2024-10-12 11:15:12', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2082, '运营商管理', 2017, 12, '2082', '2017', 'platform', 'platform/operator/index', NULL, 1, 0, 'C', '0', '0', 'system:dc:list', '#', 'admin', '2024-03-12 10:59:08', 'zksr', '2024-10-12 11:15:17', '运营商菜单', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2081, '业务城市启用', 2074, 6, '2081', '2074', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:area:enable', '#', 'admin', '2024-03-12 10:58:09', 'zksr', '2024-11-27 11:15:25', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2080, '业务城市停用', 2074, 5, '2080', '2074', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:area:disable', '#', 'admin', '2024-03-12 10:58:09', 'zksr', '2024-11-27 11:15:20', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2079, '业务城市导出', 2074, 5, '2079', '2074', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:area:export', '#', 'admin', '2024-03-12 10:58:09', 'zksr', '2024-05-07 11:24:55', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2078, '业务城市删除', 2074, 4, '2078', '2074', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:area:remove', '#', 'admin', '2024-03-12 10:58:09', 'zksr', '2024-11-27 11:15:12', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2077, '业务城市修改', 2074, 3, '2077', '2074', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:area:edit', '#', 'admin', '2024-03-12 10:58:09', 'zksr', '2024-11-27 11:15:06', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2076, '业务城市新增', 2074, 2, '2076', '2074', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:area:add', '#', 'admin', '2024-03-12 10:58:09', 'zksr', '2024-11-27 11:15:01', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2075, '业务城市查询', 2074, 1, '2075', '2074', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:area:query', '#', 'admin', '2024-03-12 10:58:09', 'zksr', '2024-03-12 16:33:05', '', 'partner,dc,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2074, '区域城市', 2017, 13, '2074', '2017', 'area', 'platform/area/index', NULL, 1, 0, 'C', '0', '0', 'system:area:list', '#', 'admin', '2024-03-12 10:58:09', 'zksr', '2024-05-07 11:19:21', '业务城市菜单', 'partner,supplier,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2073, '渠道信息导出', 2068, 5, '2073', '2068', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:channel:export', '#', 'admin', '2024-03-12 10:57:14', 'zksr', '2024-03-12 15:29:44', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2072, '渠道信息删除', 2068, 4, '2072', '2068', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:channel:remove', '#', 'admin', '2024-03-12 10:57:14', 'zksr', '2024-03-12 15:29:39', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2071, '渠道信息修改', 2068, 3, '2071', '2068', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:channel:edit', '#', 'admin', '2024-03-12 10:57:14', 'zksr', '2024-03-12 15:29:35', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2070, '渠道信息新增', 2068, 2, '2070', '2068', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:channel:add', '#', 'admin', '2024-03-12 10:57:14', 'zksr', '2024-03-12 15:29:26', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2069, '渠道信息查询', 2068, 1, '2069', '2068', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:channel:query', '#', 'admin', '2024-03-12 10:57:14', 'zksr', '2024-03-12 16:33:49', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2068, '渠道管理', 2017, 19, '2068', '2017', 'channel', 'platform/channel/index', NULL, 1, 0, 'C', '0', '0', 'system:channel:list', 'button', 'admin', '2024-03-12 10:57:14', 'zksr', '2024-03-12 16:33:43', '渠道信息菜单', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2067, '平台商城市分组导出', 2062, 5, '2067', '2062', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:group:export', '#', 'admin', '2024-03-12 10:55:47', 'zksr', '2024-11-27 11:17:12', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2066, '平台商城市分组删除', 2062, 4, '2066', '2062', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:group:remove', '#', 'admin', '2024-03-12 10:55:47', 'zksr', '2024-03-12 15:29:03', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2065, '平台商城市分组修改', 2062, 3, '2065', '2062', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:group:edit', '#', 'admin', '2024-03-12 10:55:47', 'zksr', '2024-03-12 15:29:00', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2064, '平台商城市分组新增', 2062, 2, '2064', '2062', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:group:add', '#', 'admin', '2024-03-12 10:55:47', 'zksr', '2024-03-12 15:28:57', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2063, '平台商城市分组查询', 2062, 1, '2063', '2062', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:group:query', '#', 'admin', '2024-03-12 10:55:47', 'zksr', '2024-11-27 11:17:00', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2062, '平台商城市分组', 2017, 18, '2062', '2017', 'cityGrouping', 'platform/cityGrouping/index', NULL, 1, 0, 'C', '0', '0', 'system:group:list', '#', 'admin', '2024-03-12 10:55:47', 'zksr', '2024-11-27 11:16:55', '平台商城市分组菜单', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2061, '入驻商信息', 2023, 30, '2061', '2023', 'supplier', 'platform/supplier/index', NULL, 1, 0, 'C', '0', '0', '', 'documentation', 'zksr', '2024-03-12 10:50:33', 'zksr', '2024-11-27 14:00:07', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2060, '业务员拜访', 2025, 39, '2060', '2025', 'salesmanVisit', 'operation/salesmanVisit/index', NULL, 1, 0, 'C', '0', '0', '', 'documentation', 'zksr', '2024-03-12 10:46:06', 'zksr', '2024-08-20 12:53:40', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2059, '业务员关系管理', 2025, 38, '2059', '2025', 'relationship', 'operation/relationship/index', NULL, 1, 0, 'C', '0', '0', 'member:relation:list', 'documentation', 'zksr', '2024-03-12 10:45:30', 'zksr', '2024-08-20 12:52:50', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2058, '业务员目标设置', 2025, 37, '2058', '2025', 'salesmanTarget', 'operation/salesmanTarget/index', NULL, 1, 0, 'C', '0', '0', 'member:colonelTarget:list', 'documentation', 'zksr', '2024-03-12 10:44:49', 'zksr', '2024-08-20 12:52:14', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2057, '业务员信息', 2025, 36, '2057', '2025', 'salesmanMgmt', 'operation/salesmanMgmt/index', NULL, 1, 0, 'C', '0', '0', 'member:colonel:list', 'documentation', 'zksr', '2024-03-12 10:43:48', 'zksr', '2024-10-18 09:33:12', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2053, '入驻商结算', 2023, 32, '2053', '2023', '#', NULL, NULL, 1, 0, 'C', '1', '1', '', 'documentation', 'zksr', '2024-03-12 10:38:40', 'zksr', '2024-11-27 14:00:19', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2052, '入驻商账户', 2023, 31, '2052', '2023', '#', NULL, NULL, 1, 0, 'C', '1', '1', '', '#', 'zksr', '2024-03-12 10:37:57', 'zksr', '2024-11-27 14:00:15', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2044, '基础设置', 2021, 24, '2044', '2021', 'settings', 'platform/settings/index', NULL, 1, 0, 'C', '0', '1', '', 'documentation', 'zksr', '2024-03-12 10:29:29', 'zksr', '2024-11-27 11:48:36', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2031, '财务管理', 0, 1, '2031', '0', 'finance', NULL, NULL, 1, 0, 'M', '0', '0', '', 'money', 'zksr', '2024-03-12 09:45:20', 'zksr', '2024-11-27 11:22:28', '', 'supplier,dc,partner,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2028, '订单管理', 0, 7, '2028', '0', 'operation', NULL, NULL, 1, 0, 'M', '0', '0', '', 'documentation', 'zksr', '2024-03-12 09:40:40', 'zksr', '2024-04-17 16:35:20', '', 'dc,partner,supplier');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2027, '平台促销管理', 0, 6, '2027', '0', 'platformPromotion', NULL, NULL, 1, 0, 'M', '0', '0', '', 'documentation', 'zksr', '2024-03-12 09:40:14', 'zksr', '2024-05-13 19:22:42', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2025, '业务员管理', 0, 4, '2025', '0', 'salesman', NULL, NULL, 1, 0, 'M', '0', '0', '', 'documentation', 'zksr', '2024-03-12 09:38:57', 'zksr', '2024-10-18 09:33:06', '', 'dc,partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2024, '商城管理', 0, 3, '2024', '0', 'management', NULL, NULL, 1, 0, 'M', '0', '0', '', 'backpack', 'zksr', '2024-03-12 09:38:18', 'zksr', '2024-08-16 13:18:30', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2023, '入驻商管理', 0, 2, '2023', '0', 'supplier', NULL, NULL, 1, 0, 'M', '1', '0', '', '#', 'zksr', '2024-03-12 09:37:14', 'zksr', '2024-05-06 16:43:42', '', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2021, '系统设置', 0, 11, '2021', '0', 'platform', NULL, NULL, 1, 0, 'M', '0', '0', '', 'documentation', 'zksr', '2024-03-11 18:48:44', 'zksr', '2024-05-24 11:15:06', '', 'partner');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2017, '基础管理', 0, 1, '2017', '0', 'info', NULL, NULL, 1, 0, 'M', '0', '0', '', 'tree', 'zksr', '2024-03-11 18:31:25', 'zksr', '2024-11-27 10:59:35', '', 'supplier,partner,dc,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2005, '平台商导出', 2000, 4, '2005', '2000', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:partner:export', '#', 'zksr', '2024-01-11 21:33:54', 'zksr', '2024-11-27 11:01:23', '平台商导出', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2003, '平台商修改', 2000, 3, '2003', '2000', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:partner:edit', '#', 'zksr', '2024-01-11 21:33:54', 'zksr', '2024-11-27 11:00:55', '平台商修改', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2002, '平台商新增', 2000, 2, '2002', '2000', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:partner:add', '#', 'zksr', '2024-01-11 21:33:54', 'zksr', '2024-11-27 11:00:48', '平台商新增', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2001, '平台商查询', 2000, 1, '2001', '2000', '#', '', NULL, 1, 0, 'F', '0', '0', 'system:partner:query', '#', 'zksr', '2024-01-11 21:33:54', 'zksr', '2024-11-27 11:00:35', '平台商查询', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2000, '平台商', 2017, 0, '2000', '2017', 'software', 'software/platform/list/index', NULL, 1, 0, 'C', '0', '0', 'system:partner:list', '#', 'zksr', '2024-01-11 21:33:54', 'zksr', '2024-11-27 16:09:23', '平台商菜单', 'software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1060, '生成代码', 115, 5, '1060', '115', '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '生成代码', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1059, '预览代码', 115, 4, '1059', '115', '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '预览代码', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1058, '导入代码', 115, 2, '1058', '115', '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '导入代码', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1057, '生成删除', 115, 3, '1057', '115', '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '生成删除', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1056, '生成修改', 115, 2, '1056', '115', '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '生成修改', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1055, '生成查询', 115, 1, '1055', '115', '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '生成查询', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1054, '任务导出', 110, 6, '1054', '110', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '任务导出', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1053, '状态修改', 110, 5, '1053', '110', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '状态修改', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1052, '任务删除', 110, 4, '1052', '110', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '任务删除', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1051, '任务修改', 110, 3, '1051', '110', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '任务修改', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1050, '任务新增', 110, 2, '1050', '110', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '任务新增', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1049, '任务查询', 110, 1, '1049', '110', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '任务查询', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1048, '单条强退', 109, 3, '1048', '109', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '单条强退', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1047, '批量强退', 109, 2, '1047', '109', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '批量强退', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1046, '在线查询', 109, 1, '1046', '109', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '在线查询', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1045, '账户解锁', 501, 4, '1045', '501', '#', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:unlock', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '账户解锁', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1044, '日志导出', 501, 3, '1044', '501', '#', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:export', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '日志导出', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1043, '登录删除', 501, 2, '1043', '501', '#', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:remove', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '登录删除', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1042, '登录查询', 501, 1, '1042', '501', '#', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:query', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '登录查询', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1041, '日志导出', 500, 3, '1041', '500', '#', '', '', 1, 0, 'F', '0', '0', 'system:operlog:export', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '日志导出', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1040, '操作删除', 500, 2, '1040', '500', '#', '', '', 1, 0, 'F', '0', '0', 'system:operlog:remove', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '操作删除', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1039, '操作查询', 500, 1, '1039', '500', '#', '', '', 1, 0, 'F', '0', '0', 'system:operlog:query', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '操作查询', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1038, '公告删除', 107, 4, '1038', '107', '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '公告删除', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1037, '公告修改', 107, 3, '1037', '107', '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '公告修改', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1036, '公告新增', 107, 2, '1036', '107', '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '公告新增', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1035, '公告查询', 107, 1, '1035', '107', '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '公告查询', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1034, '参数导出', 106, 5, '1034', '106', '#', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '参数导出', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1033, '参数删除', 106, 4, '1033', '106', '#', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '参数删除', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1032, '参数修改', 106, 3, '1032', '106', '#', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '参数修改', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1031, '参数新增', 106, 2, '1031', '106', '#', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '参数新增', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1030, '参数查询', 106, 1, '1030', '106', '#', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '参数查询', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1029, '字典导出', 105, 5, '1029', '105', '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '字典导出', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1028, '字典删除', 105, 4, '1028', '105', '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '字典删除', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1027, '字典修改', 105, 3, '1027', '105', '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '字典修改', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1026, '字典新增', 105, 2, '1026', '105', '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '字典新增', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1025, '字典查询', 105, 1, '1025', '105', '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '字典查询', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1024, '岗位导出', 104, 5, '1024', '104', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '岗位导出', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1023, '岗位删除', 104, 4, '1023', '104', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '岗位删除', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1022, '岗位修改', 104, 3, '1022', '104', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '岗位修改', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1021, '岗位新增', 104, 2, '1021', '104', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '岗位新增', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1020, '岗位查询', 104, 1, '1020', '104', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '岗位查询', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1019, '部门删除', 103, 4, '1019', '103', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '部门删除', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1018, '部门修改', 103, 3, '1018', '103', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '部门修改', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1017, '部门新增', 103, 2, '1017', '103', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '部门新增', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1016, '部门查询', 103, 1, '1016', '103', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '部门查询', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1015, '菜单删除', 102, 4, '1015', '102', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '菜单删除', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1014, '菜单修改', 102, 3, '1014', '102', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '菜单修改', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1013, '菜单新增', 102, 2, '1013', '102', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'zksr', '2024-01-10 18:27:15', '', NULL, '菜单新增', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1012, '菜单查询', 102, 1, '1012', '102', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 20:05:31', '菜单查询', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1011, '角色导出', 101, 5, '1011', '101', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-03-14 11:14:52', '角色导出', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1010, '角色删除', 101, 4, '1010', '101', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-03-14 11:14:47', '角色删除', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1009, '角色修改', 101, 3, '1009', '101', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-03-14 11:14:42', '角色修改', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1008, '角色新增', 101, 2, '1008', '101', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-03-14 11:14:38', '角色新增', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1007, '角色查询', 101, 1, '1007', '101', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-03-14 11:14:33', '角色查询', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1006, '重置密码', 100, 7, '1006', '100', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:55:58', '重置密码', 'partner,dc,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1005, '用户导入', 100, 6, '1005', '100', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:55:52', '用户导入', 'partner,dc,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1004, '用户导出', 100, 5, '1004', '100', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:55:47', '用户导出', 'partner,dc,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1003, '用户删除', 100, 4, '1003', '100', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:55:41', '用户删除', 'partner,dc,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1002, '用户修改', 100, 3, '1002', '100', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:54:45', '用户修改', 'partner,dc,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1001, '用户新增', 100, 2, '1001', '100', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:54:40', '用户新增', 'partner,dc,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1000, '用户查询', 100, 1, '1000', '100', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:54:33', '用户查询', 'partner,dc,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (501, '登录日志', 108, 2, '501', '108', 'logininfor', 'system/logininfor/index', '', 1, 0, 'C', '0', '0', 'system:logininfor:list', 'logininfor', 'zksr', '2024-01-10 18:27:15', '', NULL, '登录日志菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (500, '操作日志', 108, 1, '500', '108', 'operlog', 'system/operlog/index', '', 1, 0, 'C', '0', '0', 'system:operlog:list', 'form', 'zksr', '2024-01-10 18:27:15', '', NULL, '操作日志菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (116, '系统接口', 3, 3, '116', '3', 'http://localhost:8080/swagger-ui/index.html', '', '', 0, 0, 'C', '1', '0', 'tool:swagger:list', 'swagger', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-05-27 15:37:08', '系统接口菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (115, '代码生成', 3, 2, '115', '3', 'gen', 'tool/gen/index', '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-05-27 15:37:11', '代码生成菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (114, '表单构建', 3, 1, '114', '3', 'build', 'tool/build/index', '', 1, 0, 'C', '1', '0', 'tool:build:list', 'build', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-05-27 15:37:04', '表单构建菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (113, 'Admin控制台', 2, 5, '113', '2', 'http://localhost:9100/login', '', '', 0, 0, 'C', '1', '1', 'monitor:server:list', 'server', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-03-14 10:44:22', '服务监控菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (112, 'Nacos控制台', 2, 4, '112', '2', 'http://localhost:8848/nacos', '', '', 0, 0, 'C', '1', '1', 'monitor:nacos:list', 'nacos', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-03-14 10:43:38', '服务治理菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (111, 'Sentinel控制台', 2, 3, '111', '2', 'http://localhost:8718', '', '', 0, 0, 'C', '1', '1', 'monitor:sentinel:list', 'sentinel', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-03-14 10:44:27', '流量控制菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (110, '定时任务', 2, 2, '110', '2', 'job', 'monitor/job/index', '', 1, 0, 'C', '0', '1', 'monitor:job:list', 'job', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:59:27', '定时任务菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (109, '在线用户', 2, 1, '109', '2', 'online', 'monitor/online/index', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'zksr', '2024-01-10 18:27:15', '', NULL, '在线用户菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (108, '日志管理', 1, 9, '108', '1', 'log', '', '', 1, 0, 'M', '0', '0', '', 'log', 'zksr', '2024-01-10 18:27:15', '', NULL, '日志管理菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (107, '通知公告', 1, 8, '107', '1', 'notice', 'system/notice/index', '', 1, 0, 'C', '0', '1', 'system:notice:list', 'message', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:57:34', '通知公告菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (106, '参数设置', 1, 7, '106', '1', 'config', 'system/config/index', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'zksr', '2024-01-10 18:27:15', '', NULL, '参数设置菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (105, '字典管理', 1, 6, '105', '1', 'dict', 'system/dict/index', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'zksr', '2024-01-10 18:27:15', '', NULL, '字典管理菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (104, '岗位管理', 1, 5, '104', '1', 'post', 'system/post/index', '', 1, 0, 'C', '0', '1', 'system:post:list', 'post', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:57:11', '岗位管理菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (103, '部门管理', 1, 4, '103', '1', 'dept', 'system/dept/index', '', 1, 0, 'C', '0', '1', 'system:dept:list', 'tree', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:56:57', '部门管理菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (102, '菜单管理', 1, 3, '102', '1', 'menu', 'system/menu/index', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 20:05:28', '菜单管理菜单', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (101, '角色管理', 1, 2, '101', '1', 'role', 'system/role/index', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-03-14 11:07:08', '角色管理菜单', 'partner,dc');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (100, '用户管理', 1, 1, '100', '1', 'user', 'system/user/index', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:54:26', '用户管理菜单', 'partner,dc,software');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (4, '中科商软官网', 0, 99999, '4', '0', 'http://zksr.vip', NULL, '', 0, 0, 'M', '1', '1', '', 'guide', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 16:18:01', '中科商软官网地址', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (3, '系统工具', 0, 99997, '3', '0', 'tool', NULL, '', 1, 0, 'M', '0', '1', '', 'tool', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:58:54', '系统工具目录', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (2, '系统监控', 0, 99998, '2', '0', 'monitor', NULL, '', 1, 0, 'M', '0', '0', '', 'monitor', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-03-11 18:43:35', '系统监控目录', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES (1, '系统管理', 0, 99996, '1', '0', 'system', NULL, '', 1, 0, 'M', '0', '0', '', 'system', 'zksr', '2024-01-10 18:27:15', 'zksr', '2024-11-27 11:54:19', '系统管理目录', 'partner,dc,software');


-- 初始化 menu_code, menu_pcode
-- update`sys_menu` set menu_code = menu_id, menu_pcode = parent_id;


-- 移除软件商func_scope
-- UPDATE sys_menu SET func_scop = REPLACE(REPLACE(REPLACE(func_scop, 'software,', ''), ',software', ''), 'software', '');
