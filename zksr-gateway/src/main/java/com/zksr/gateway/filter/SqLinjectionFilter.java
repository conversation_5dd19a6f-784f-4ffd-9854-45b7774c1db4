package com.zksr.gateway.filter;

import com.zksr.common.core.utils.SqLinjectionRuleUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.gateway.config.properties.SqlInjectProperties;
import com.zksr.gateway.util.WebfluxResponseUtil;
import io.netty.buffer.ByteBufAllocator;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.core.io.buffer.NettyDataBufferFactory;
import org.springframework.http.*;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.servlet.http.Cookie;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

@Component
@ConditionalOnProperty(value = "security.sqlinject.enabled", havingValue = "true")
@Slf4j
public class SqLinjectionFilter implements GlobalFilter, Ordered {

    @Autowired
    private SqlInjectProperties sqlInjectProperties;

    @SneakyThrows
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain){
        if (!sqlInjectProperties.getEnabled()) {
            return chain.filter(exchange);
        }

        ServerHttpRequest serverHttpRequest = exchange.getRequest();
        String path = serverHttpRequest.getURI().getRawPath();

        // excludeUrls 不过滤
        if (StringUtils.matches(path, sqlInjectProperties.getExcludeUrls())) {
            return chain.filter(exchange);
        }


        // grab configuration from Config object
        log.debug("----自定义防sql注入网关全局过滤器生效----");

        HttpMethod method = serverHttpRequest.getMethod();
        String contentType = serverHttpRequest.getHeaders().getFirst(HttpHeaders.CONTENT_TYPE);
        URI uri = exchange.getRequest().getURI();

        Boolean postFlag = (method == HttpMethod.POST || method == HttpMethod.PUT) &&
                (MediaType.APPLICATION_FORM_URLENCODED_VALUE.equalsIgnoreCase(contentType) || MediaType.APPLICATION_JSON_VALUE.equals(contentType));

        //过滤get请求
        if (method == HttpMethod.GET) {

            String rawQuery = uri.getRawQuery();
            if (StringUtils.isBlank(rawQuery)){
                //return setCookieFileter(exchange, chain);
                return chain.filter(exchange);
            }

            log.debug("请求参数为：{}", rawQuery);
            // 执行sql注入校验清理
            boolean chkRet = SqLinjectionRuleUtils.getRequestSqlKeyWordsCheck(rawQuery);

            //    如果存在sql注入,直接拦截请求
            if (chkRet) {
                log.error("请求【" + uri.getRawPath() + uri.getRawQuery() + "】参数中包含不允许sql的关键词, 请求拒绝");
                return setUnauthorizedResponse(exchange);
            }
            //透传参数，不对参数做任何处理
            return chain.filter(exchange);
        } else if (postFlag) {
            //post请求时，如果是文件上传之类的请求，不修改请求消息体
            return DataBufferUtils.join(serverHttpRequest.getBody()).flatMap(d -> Mono.just(Optional.of(d))).defaultIfEmpty(
                            Optional.empty())
                    .flatMap(optional -> {
                        // 取出body中的参数
                        String bodyString = "";
                        if (optional.isPresent()) {
                            byte[] oldBytes = new byte[optional.get().readableByteCount()];
                            optional.get().read(oldBytes);
                            bodyString = new String(oldBytes, StandardCharsets.UTF_8);
                        }
                        HttpHeaders httpHeaders = serverHttpRequest.getHeaders();
                        // 执行XSS清理
                        log.debug("{} - [{}] 请求参数：{}", method, uri.getPath(), bodyString);
                        boolean chkRet = false;
                        if (MediaType.APPLICATION_JSON_VALUE.equals(contentType)) {
                            //如果MediaType是json才执行json方式验证
                            chkRet = SqLinjectionRuleUtils.postRequestSqlKeyWordsCheck(bodyString);
                        } else {
                            //form表单方式，需要走get请求
                            try {
                                chkRet = SqLinjectionRuleUtils.getRequestSqlKeyWordsCheck(bodyString);
                            } catch (UnsupportedEncodingException e) {
                                log.error(" SqLinjectionRuleUtils.getRequestSqlKeyWordsCheck异常,", e);
                                throw new RuntimeException(e);
                            }
                        }

                        //  如果存在sql注入,直接拦截请求
                        if (chkRet) {
                            log.error("{} - [{}] 参数：{}, 包含不允许sql的关键词，请求拒绝", method, uri.getPath(), bodyString);
                            return setUnauthorizedResponse(exchange);
                        }

                        ServerHttpRequest newRequest = serverHttpRequest.mutate().uri(uri).build();

                        // 重新构造body
                        byte[] newBytes = bodyString.getBytes(StandardCharsets.UTF_8);
                        DataBuffer bodyDataBuffer = toDataBuffer(newBytes);
                        Flux<DataBuffer> bodyFlux = Flux.just(bodyDataBuffer);

                        // 重新构造header
                        HttpHeaders headers = new HttpHeaders();
                        headers.putAll(httpHeaders);
                        // 由于修改了传递参数，需要重新设置CONTENT_LENGTH，长度是字节长度，不是字符串长度
                        int length = newBytes.length;
                        headers.remove(HttpHeaders.CONTENT_LENGTH);
                        headers.setContentLength(length);
                        headers.set(HttpHeaders.CONTENT_TYPE, contentType);
                        // 重写ServerHttpRequestDecorator，修改了body和header，重写getBody和getHeaders方法
                        newRequest = new ServerHttpRequestDecorator(newRequest) {
                            @Override
                            public Flux<DataBuffer> getBody() {
                                return bodyFlux;
                            }

                            @Override
                            public HttpHeaders getHeaders() {
                                return headers;
                            }
                        };

                        return chain.filter(exchange.mutate().request(newRequest).build());
                    });
        } else {
            return chain.filter(exchange);
        }

    }

//    public Mono<Void> setCookieFileter(ServerWebExchange exchange, GatewayFilterChain chain) {
//        return chain.filter(exchange).then(Mono.defer(() -> {
//            HttpHeaders headers = exchange.getResponse().getHeaders();
////            // 假设你要设置的Cookie名为MY_COOKIE
//            String cookieValue = headers.getFirst("Admin-Token");
//            if (cookieValue != null) {
//                // 修改Cookie的属性，添加HttpOnly
//                headers.set("Admin-Token", cookieValue.replace("Secure", "Secure; HttpOnly"));
//            }
//
//            ServerHttpRequest serverHttpRequest = exchange.getRequest();
//            serverHttpRequest.getCookies().forEach((name, values) -> {
//                if (name.equals("Admin-Token")) {
//                    values.forEach(cookie -> {
//                        // 修改Cookie的属性，添加HttpOnly
//                        log.info("luoxiang:{}", cookie.getName());
////                        cookie.setHttpOnly(true);
////                        cookie.setSecure(true);
//                    });
//                }
//            });
//
//            return Mono.empty();
//        }));
////        return chain.filter(exchange);
//    }

    // 自定义过滤器执行的顺序，数值越大越靠后执行，越小就越先执行
    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }

    /**
     * 设置403拦截状态
     */
    private Mono<Void> setUnauthorizedResponse(ServerWebExchange exchange) {
        return WebfluxResponseUtil.responseFailed(exchange, HttpStatus.FORBIDDEN.value(),
                "request is forbidden, SQL keywords are not allowed in the parameters.");
    }

    /**
     * 字节数组转DataBuffer
     *
     * @param bytes 字节数组
     * @return DataBuffer
     */
    private DataBuffer toDataBuffer(byte[] bytes) {
        NettyDataBufferFactory nettyDataBufferFactory = new NettyDataBufferFactory(ByteBufAllocator.DEFAULT);
        DataBuffer buffer = nettyDataBufferFactory.allocateBuffer(bytes.length);
        buffer.write(bytes);
        return buffer;
    }
}
