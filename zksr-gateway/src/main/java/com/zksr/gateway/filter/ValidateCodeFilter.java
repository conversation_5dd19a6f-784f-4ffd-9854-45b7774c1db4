package com.zksr.gateway.filter;

import java.nio.CharBuffer;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.redis.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.gateway.config.properties.CaptchaProperties;
import com.zksr.gateway.service.ValidateCodeService;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Flux;

/**
 * 验证码过滤器
 *
 * <AUTHOR>
 */
@Component
public class ValidateCodeFilter extends AbstractGatewayFilterFactory<Object>
{
    private final static String[] VALIDATE_URL = new String[] { "/auth/login", "/auth/register" };

    @Autowired
    private ValidateCodeService validateCodeService;

    @Autowired
    private CaptchaProperties captchaProperties;

    @Autowired
    private RedisService redisService;

    private static final String CODE = "code";

    private static final String UUID = "uuid";

    @Override
    public GatewayFilter apply(Object config)
    {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();

            String userAgent = request.getHeaders().getFirst("User-Agent");
            // 非PC端不校验验证码
            if (ObjectUtil.isNotEmpty(userAgent) && (userAgent.contains("Android") || userAgent.contains("iPhone"))) {
                return chain.filter(exchange);
            }

            // 非登录/注册请求或验证码关闭，不处理
            if (!StringUtils.equalsAnyIgnoreCase(request.getURI().getPath(), VALIDATE_URL) || !captchaProperties.getEnabled())
            {
                return chain.filter(exchange);
            }

            try
            {
                String rspStr = resolveBodyFromRequest(request);
                JSONObject obj = JSON.parseObject(rspStr);
                Object username = obj.get("username");
                if (ObjectUtil.isNotEmpty(username)){
                    Integer retryCount = redisService.getCacheObject(CacheConstants.PWD_ERR_CNT_KEY + username.toString());
                    if (ObjectUtil.isEmpty(retryCount) || retryCount < CacheConstants.PASSWORD_MIN_RETRY_COUNT) {
                        return chain.filter(exchange);
                    }
                }
                validateCodeService.checkCaptcha(obj.getString(CODE), obj.getString(UUID));
            }
            catch (Exception e)
            {
                return ServletUtils.webFluxResponseWriter(exchange.getResponse(), e.getMessage());
            }
            return chain.filter(exchange);
        };
    }

    private String resolveBodyFromRequest(ServerHttpRequest serverHttpRequest)
    {
        // 获取请求体
        Flux<DataBuffer> body = serverHttpRequest.getBody();
        AtomicReference<String> bodyRef = new AtomicReference<>();
        body.subscribe(buffer -> {
            CharBuffer charBuffer = StandardCharsets.UTF_8.decode(buffer.asByteBuffer());
            DataBufferUtils.release(buffer);
            bodyRef.set(charBuffer.toString());
        });
        return bodyRef.get();
    }
}
