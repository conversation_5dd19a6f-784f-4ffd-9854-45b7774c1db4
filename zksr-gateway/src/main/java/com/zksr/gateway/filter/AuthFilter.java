package com.zksr.gateway.filter;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.zksr.common.core.constant.*;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.erpUtils.HttpUtils;
import com.zksr.common.core.utils.*;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.gateway.config.properties.IgnoreWhiteProperties;
import com.zksr.gateway.user.dto.UserDto;
import com.zksr.system.api.model.LoginUser;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.loadbalancer.reactive.ReactorLoadBalancerExchangeFilterFunction;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 网关鉴权
 *
 * <AUTHOR>
 */
@Component
public class AuthFilter implements GlobalFilter, Ordered, InitializingBean
{
    private static final Logger log = LoggerFactory.getLogger(AuthFilter.class);

    // 排除过滤的 uri 地址，nacos自行添加
    @Autowired
    private IgnoreWhiteProperties ignoreWhite;

    @Autowired
    private RedisService redisService;

    /**
     * 小于29天过期, 调用了接口, 刷新key过期时间
     */
    protected static final long REFRESH_USER_KEY_SECOND = 2505600;
    //B2B走SAAS权限开关，false走原有逻辑，true走SAAS权限

    @Resource
    private ReactorLoadBalancerExchangeFilterFunction loadBalancerExchangeFilterFunction;

    private final static String ERP_USER_INFO_URL = "//zksr-system/user/inner/userInfoForGateway?userCode=%s&tenantCode=%s&b2bUserCacheKey=%s";
    @Value("${b2b.saas.auth.loginVerificationHost:https://icloudapi-sit.annto.com}")
    private String b2bSaasAuthLoginVerificationHost;
    @Value("${b2b.saas.auth.loginVerificationUri:/api-saas-auth/core/loginVerification}")
    private String  b2bSaasAuthLoginVerificationUri;
    @Value("${b2b.gateway.userCache.enabled:true}")
    private Boolean  b2bGatewayUserCacheEnable;
    /**
     * token过期时间默认为10分钟,单位为分钟
     * **/
    @Value("${b2b.gateway.expireTime.token:10}")
    private long tokenExpireTime;
    @Value("${b2b.saas.auth.switch:false}")
    private Boolean saasAuthSwitch;

    private WebClient innerWebClient;

    // B2B 用户缓存
    private Cache<String, R<LoginUser>> b2bUserCache;


    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String url = request.getURI().getPath();
        // 跳过不需要验证的路径
        if (StringUtils.matches(url, ignoreWhite.getWhites())) {
            return chain.filter(exchange);
        }

        String type = getFileterType(request);
        if(ToolUtil.isNotEmpty(type) && "ADMIN".equals(type)){
            //后台
            return adminFilter(exchange, chain);
        }else if(ToolUtil.isNotEmpty(type) && "MALL".equals(type)){
            //商城端
            return mallFileter(exchange, chain);
        }else if(ToolUtil.isNotEmpty(type) && "OPENAPI".equals(type)){
            //商城端
            return openapiFileter(exchange, chain);
        }else{
            return unauthorizedResponse(exchange, "令牌不能为空");
        }
        //OpenApi
    }

    private Mono<Void> openapiFileter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpRequest.Builder mutate = request.mutate();

        String token = getOpenapiToken(request);
        if (StringUtils.isEmpty(token)) {
            return unauthorizedResponse(exchange, "令牌不能为空");
        }

        Claims claims = null;
        try{
            claims = OpenapiJwtUtils.parseToken(token);
        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
        if (claims == null) {
            return unauthorizedResponse(exchange, "令牌已过期或验证不正确！");
        }

        String opensourceKey = OpenapiJwtUtils.getOpensourceKey(claims);
        boolean islogin = redisService.hasKey(getOpenapiTokenKey(opensourceKey));

        log.info("校验OPENAPI -- token ： token信息:{},请求地址:{}",token,request.getPath());
        if (!islogin) {
            log.info("校验OPENAPI -- token ： 登陆状态过期的token信息:{}, 请求地址:{}",token,request.getPath());
            return unauthorizedResponse(exchange, "登录状态已过期");
        }

        String sysCode = OpenapiJwtUtils.getSysCode(claims);
        //log.info("AuthFilter:filter:sysCode:{}", sysCode);
        if (StringUtils.isEmpty(sysCode)) {
            return unauthorizedResponse(exchange, "令牌验证失败");
        }

        String opensourceId = OpenapiJwtUtils.getOpensourceId(claims);

        // 设置用户信息到请求
        addHeader(mutate, OpenapiSecurityConstants.OPENSOURCE_KEY, opensourceKey);
        addHeader(mutate, OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID, opensourceId);
        addHeader(mutate, OpenapiSecurityConstants.SYS_CODE, sysCode);
        // 内部请求来源参数清除
        removeHeader(mutate, SecurityConstants.FROM_SOURCE);
        return chain.filter(exchange.mutate().request(mutate.build()).build());
        //return unauthorizedResponse(exchange, "令牌不能为空");
    }

    private Mono<Void> mallFileter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpRequest.Builder mutate = request.mutate();

        String token = getMallToken(request);
        if (StringUtils.isEmpty(token)) {
            return unauthorizedResponse(exchange, "令牌不能为空");
        }
        Claims claims = MallJwtUtils.parseToken(token);
        if (claims == null) {
            return unauthorizedResponse(exchange, "令牌已过期或验证不正确！");
        }
        String memberKey = MallJwtUtils.getMemberKey(claims);

        /**
         * 用户key默认是30天, 调整为, 只要三十天内, 超过24小时后再次进入过小程序就延后三十天, 避免大量执行expire命令
         * ttl
         * > -1   剩余秒
         * = -1   永久key
         * < -1   不存在
         */
        String userCacheKey = getMallTokenKey(memberKey);
        long expireSecond = redisService.getExpire(userCacheKey);
        if (expireSecond < -1) {
            return unauthorizedResponse(exchange, "登录状态已过期");
        }
        if (expireSecond < REFRESH_USER_KEY_SECOND) {
            // 设置为30天, 保证只要30天内经常使用就无需重新授权
            redisService.expire(userCacheKey, REFRESH_USER_KEY_SECOND + 86400);
        }

        String sysCode = MallJwtUtils.getSysCode(claims);
        //log.info("AuthFilter:filter:sysCode:{}", sysCode);
        if (StringUtils.isEmpty(sysCode)) {
            return unauthorizedResponse(exchange, "令牌验证失败");
        }

        String memberId = MallJwtUtils.getMemberId(claims);
        String memberName = MallJwtUtils.getMemberName(claims);

        // 设置用户信息到请求
        addHeader(mutate, MallSecurityConstants.MEMBER_KEY, memberKey);
        addHeader(mutate, MallSecurityConstants.DETAILS_MEMBER_ID, memberId);
        addHeader(mutate, MallSecurityConstants.DETAILS_MEMBER_NAME, memberName);
        addHeader(mutate, MallSecurityConstants.SYS_CODE, sysCode);
        addHeader(mutate, MallSecurityConstants.BRANCH_ID, MallJwtUtils.getBranchId(claims));
        // 内部请求来源参数清除
        removeHeader(mutate, SecurityConstants.FROM_SOURCE);
        return chain.filter(exchange.mutate().request(mutate.build()).build());
        //return unauthorizedResponse(exchange, "令牌不能为空");
    }

    private Mono<Void> adminFilter(ServerWebExchange exchange, GatewayFilterChain chain) {
        if(saasAuthSwitch){ //开启saas权限开关，则走saas权限校验
            return saasAuthFilter(exchange, chain);
        }else {
            //默认走原有逻辑
            ServerHttpRequest request = exchange.getRequest();
            ServerHttpRequest.Builder mutate = request.mutate();

            String token = getToken(request);
            if (StringUtils.isEmpty(token)) {
                return unauthorizedResponse(exchange, "令牌不能为空");
            }
            Claims claims = JwtUtils.parseToken(token);
            if (claims == null) {
                return unauthorizedResponse(exchange, "令牌已过期或验证不正确！");
            }
            String userkey = JwtUtils.getUserKey(claims);
            boolean islogin = redisService.hasKey(getTokenKey(userkey));
            if (!islogin) {
                return unauthorizedResponse(exchange, "登录状态已过期");
            }
            String userid = JwtUtils.getUserId(claims);
            String username = JwtUtils.getUserName(claims);
            String sysCode = JwtUtils.getSysCode(claims);
            log.info("AuthFilter:filter:sysCode:{}", sysCode);
            if (StringUtils.isEmpty(userid) || StringUtils.isEmpty(username)) {
                return unauthorizedResponse(exchange, "令牌验证失败");
            }

            // 设置用户信息到请求
            addHeader(mutate, SecurityConstants.USER_KEY, userkey);
            addHeader(mutate, SecurityConstants.DETAILS_USER_ID, userid);
            addHeader(mutate, SecurityConstants.DETAILS_USERNAME, username);
            addHeader(mutate, SecurityConstants.SYS_CODE, sysCode);
            addHeader(mutate, SecurityConstants.DC_ID, JwtUtils.getDcId(claims));
            // 内部请求来源参数清除
            removeHeader(mutate, SecurityConstants.FROM_SOURCE);
            return chain.filter(exchange.mutate().request(mutate.build()).build());
        }
    }

    private Mono<Void> saasAuthFilter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpRequest.Builder mutate = request.mutate();
        String token = getSaasToken(request);
        if (StringUtils.isEmpty(token)) {
            return unauthorizedResponse(exchange, "令牌不能为空");
        }
        Mono<Void> login = checkSaasLogin(exchange, chain, token);
        if (login != null) {
            return login;
        }
        return chain.filter(exchange.mutate().request(mutate.build()).build());
    }

    private Mono<Void> checkSaasLogin(ServerWebExchange exchange, GatewayFilterChain chain, String token) {
            if (StringUtils.hasText(token)) {
                // 第一步：处理外部用户信息逻辑
                return getSaasUserInfoV2(token).flatMap(userInfoRsp -> {
                    if (userInfoRsp == null) {
                        log.error("请求saas的接口/loginVerification,获取saas用户信息失败");
                        return unauthorizedResponse(exchange, "token解析用户信息失败");
                    }
                    if (!Objects.equals(userInfoRsp.getCode(), 0)) {
                        log.error("请求saas的接口/loginVerification,获取saas用户信息失败，失败原因：{}", JsonUtils.toJsonString(userInfoRsp));
                        return unauthorizedResponse(exchange, "token解析用户信息失败,失败原因：" + userInfoRsp.getMsg());
                    }
                    UserDto userDto = userInfoRsp.getData();

                    // 第二步：处理 B2B 用户信息逻辑
                    return getB2bUserInfo(token,userDto).flatMap(b2bUserInfoRsp -> {
                        if (b2bUserInfoRsp == null) {
                            log.error("b2b获取用户信息失败,saas用户编码：{}，saas租户编码：{}",userDto.getUserCode(),userDto.getCurrentTenant().getTenantCode());
                            return unauthorizedResponse(exchange, String.format("b2b获取用户信息失败,saas用户编码：%s，saas租户编码：%s",userDto.getUserCode(),userDto.getCurrentTenant().getTenantCode()));
                        }
                        if (!b2bUserInfoRsp.isSuccess()) {
                            log.error("b2b获取用户信息失败,saas用户编码：{}，saas租户编码：{}，失败原因：{}",userDto.getUserCode(),userDto.getCurrentTenant().getTenantCode(), JsonUtils.toJsonString(b2bUserInfoRsp));
                            return unauthorizedResponse(exchange, String.format("b2b获取用户信息失败,saas用户编码：%s，saas租户编码：%s，失败原因：%s",userDto.getUserCode(),userDto.getCurrentTenant().getTenantCode(),b2bUserInfoRsp.getMsg()));
                        }
                        LoginUser b2bUserInfo = b2bUserInfoRsp.getData();
                        String userId =  b2bUserInfo.getUserid() == null ? null : b2bUserInfo.getUserid().toString() ;
                        String userName = b2bUserInfo.getUsername();
                        String sysCode = b2bUserInfo.getSysCode() == null ? null : b2bUserInfo.getSysCode().toString();
                        String userKey = b2bUserInfo.getToken();
                        String dcId = b2bUserInfo.getDcId() == null ? "" : b2bUserInfo.getDcId().toString();
                        if (StrUtil.hasBlank(userId, userName, userKey)) {
                            log.error("【鉴权异常处理】b2b用户信息不足，验证失败,userId：{}, userName：{},  userKey：{}",userId, userName, userKey);
                            return unauthorizedResponse(exchange, "b2b用户信息不足，验证失败");
                        }
//                        // 添加用户信息到请求头
                        ServerHttpRequest modifiedRequest = exchange.getRequest().mutate()
                                .header(SecurityConstants.USER_KEY, userKey)
                                .header(SecurityConstants.DETAILS_USER_ID, userId)
                                .header(SecurityConstants.DETAILS_USERNAME, userName)
                                .header(SecurityConstants.SYS_CODE, sysCode)
                                .header(SecurityConstants.DC_ID, dcId)
                                .header(SecurityConstants.SAAS_USER_CODE, userDto.getUserCode())
                                .header(SecurityConstants.SAAS_TENANT_CODE,userDto.getCurrentTenant().getTenantCode())
                                .header(SecurityConstants.SAAS_HEADER_TENANT_CODE, token)
                                .build();
                        removeHeader(exchange.getRequest().mutate(), SecurityConstants.FROM_SOURCE);
                        return chain.filter(exchange.mutate().request(modifiedRequest).build());
                    });
                });
            }
        // 没有 Token，返回 401
        exchange.getResponse().setStatusCode(org.springframework.http.HttpStatus.UNAUTHORIZED);
        return exchange.getResponse().setComplete();
    }

//    // 获取SAAS用户信息
//    private Mono<R<UserDto>> getSaasUserInfo(String token){
//        UserDto userDto = new UserDto();
//        userDto.setAccessToken(token);
//        String md5TokenKey = Md5Utils.md5(token);
//        return Mono.justOrEmpty(!b2bGatewayUserCacheEnable?null:saasUserCache.getIfPresent(md5TokenKey))
//                .switchIfEmpty(saasWebClient.post().uri(b2bSaasAuthLoginVerificationUri)
//                        .header("X-Access-Token", token)
//                        .bodyValue(userDto)
//                        .retrieve()
//                        .bodyToMono(new ParameterizedTypeReference<R<UserDto>>() {})
//                        .doOnNext(userDtoRsp -> {
//                            if (Objects.equals(userDtoRsp.getCode(), 0) && b2bGatewayUserCacheEnable) {
//                                saasUserCache.put(md5TokenKey, userDtoRsp);
//                            }})
//                        .onErrorResume(e -> {
//                            log.error("【gateway请求报错】请求saas权限服务接口{}，错误原因：",b2bSaasAuthLoginVerificationUri,e);
//                            R<UserDto> userDtoR = R.fail(e.getMessage());
//                            return Mono.just(userDtoR);
//                        }));
//    }

    // 获取SAAS用户信息
    private Mono<R<UserDto>> getSaasUserInfoV2(String token){
        String url = String.format("%s%s", b2bSaasAuthLoginVerificationHost, b2bSaasAuthLoginVerificationUri);
        try {
            String md5TokenKey = Md5Utils.md5(token);
            String key = String.format("%s:%s", "b2b:saas:user", md5TokenKey);
            String jsonData = redisService.getCacheObject(key);
            if(StrUtil.isBlank(jsonData)){
                // header信息
                Map<String, String> header = new HashMap<>();
                header.put(TokenConstants.SAAS_ACCESS_TOKEN, token);
                // body信息
                Map<String, Object> body = new HashMap<>();
                body.put("accessToken",token);
                log.info("调用saas权限平台token鉴权接口请求入参:{},请求头信息:{},请求地址:{}", JSONUtil.toJsonStr(body), JSONUtil.toJsonStr(header), url);
                String responseStr = HttpUtils.simplePost(url, header, JSONUtil.toJsonStr(body), HttpUtils.TIME_OUT_TEN);
                log.info("调用saas权限平台token鉴权接口响应数据:{}", responseStr);
                R<UserDto> saasUserResponse = JSONUtil.toBean(responseStr, new TypeReference<R<UserDto>>(){}, false);
                if (!Objects.equals(saasUserResponse.getCode(),0 )) {
                    throw new RuntimeException(saasUserResponse.getMsg());
                }
                redisService.setCacheObject(key, com.alibaba.fastjson.JSON.toJSONString(saasUserResponse), 60L, TimeUnit.MINUTES);
                return Mono.just(saasUserResponse);
            }else {
                R<UserDto> saasUserResponse = JSONUtil.toBean(jsonData, new TypeReference<R<UserDto>>() {}, false);
                return Mono.just(saasUserResponse);
            }
        } catch (RuntimeException e) {
            log.error("【gateway请求报错】请求saas权限服务接口{}，错误原因：",url,e);
            R<UserDto> userDtoR = R.fail(e.getMessage());
            return Mono.just(userDtoR);
        }
    }

    // 获取 B2B 用户信息
    private Mono<R<LoginUser>> getB2bUserInfo(String token , UserDto userDto){
        // 增加token标识，可重新登录刷新用户信息。
        String b2bUserCacheKey = String.format("%s%s", CacheConstants.LOGIN_TOKEN_KEY,Md5Utils.md5(token));
        // redis缓存kep:erpUserCacheKey为空，就重新设置redis的key。
        return Mono.justOrEmpty(!redisService.hasKey(b2bUserCacheKey) || redisService.getExpire(b2bUserCacheKey) <= 300 ? null : b2bUserCache.getIfPresent(b2bUserCacheKey))
                .switchIfEmpty(innerWebClient.get()
                        .uri(String.format(ERP_USER_INFO_URL, userDto.getUserCode(), userDto.getCurrentTenant().getTenantCode(), b2bUserCacheKey))
                        .header(SecurityConstants.FROM_SOURCE, SecurityConstants.INNER)
                        .retrieve()
                        .bodyToMono(new ParameterizedTypeReference<R<LoginUser>>() {})
                        .doOnNext(b2bUserInfoRsp -> {
                            // 还要加上租户。
                            if (b2bUserInfoRsp.isSuccess()) {
                                // 本地缓存
                                b2bUserCache.put(b2bUserCacheKey, b2bUserInfoRsp);
                                // redis保存用户
                                LoginUser loginUser = b2bUserInfoRsp.getData();
                                Map<String, Object> loginMap = new HashMap<>();
                                loginMap.put(SecurityConstants.USER_KEY, loginUser.getToken());
                                loginMap.put(SecurityConstants.DETAILS_USER_ID, loginUser.getUserid());
                                loginMap.put(SecurityConstants.DETAILS_USERNAME, loginUser.getUsername());
                                loginMap.put(SecurityConstants.SYS_CODE, loginUser.getSysCode());
                                loginMap.put(SecurityConstants.DC_ID, loginUser.getDcId());
                                loginMap.put(SecurityConstants.USER, loginUser.getSysUser());
                                loginMap.put(SecurityConstants.ROLES, loginUser.getRoles());
                                loginMap.put(SecurityConstants.PERMISSIONS, loginUser.getPermissions());
                                loginMap.put(SecurityConstants.LOGIN_USER, loginUser);
                                loginMap.put(SecurityConstants.PARTNER_LOG, loginUser.getPartnerLog());
                                loginMap.put(SecurityConstants.PARTNER_NAME, loginUser.getPartnerName());
                                redisService.setCacheMap(b2bUserCacheKey, loginMap);
                                redisService.expire(b2bUserCacheKey, tokenExpireTime, TimeUnit.MINUTES);
                            }})
                        .onErrorResume(e -> {
                            log.error("【gateway请求报错】请求b2b的zksr-system服务接口/user/inner/userInfoForGateway，错误原因：",e);
                            R<LoginUser> loginUserR = R.fail(e.getMessage());
                            return Mono.just(loginUserR);
                        }));
    }

    private String getFileterType(ServerHttpRequest request) {
        String token = "";
        token = request.getHeaders().getFirst(TokenConstants.SAAS_ACCESS_TOKEN);
        if(ToolUtil.isNotEmpty(token)){
            return "ADMIN";
        }
        token = request.getHeaders().getFirst(TokenConstants.AUTHENTICATION);
        if(ToolUtil.isNotEmpty(token)){
            return "ADMIN";
        }

        token = request.getHeaders().getFirst(TokenConstants.MALL_AUTHORIZATION);
        if(ToolUtil.isNotEmpty(token)){
            return "MALL";
        }

        token = request.getHeaders().getFirst(TokenConstants.OPENAPI_AUTHORIZATION);
        if(ToolUtil.isNotEmpty(token)){
            return "OPENAPI";
        }
        return token;
    }

    private void addHeader(ServerHttpRequest.Builder mutate, String name, Object value)
    {
        if (value == null)
        {
            return;
        }
        String valueStr = value.toString();
        String valueEncode = ServletUtils.urlEncode(valueStr);
        mutate.header(name, valueEncode);
    }

    private void removeHeader(ServerHttpRequest.Builder mutate, String name)
    {
        mutate.headers(httpHeaders -> httpHeaders.remove(name)).build();
    }

    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange, String msg)
    {
        log.error("[鉴权异常处理]请求路径:{}", exchange.getRequest().getPath());
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), msg, HttpStatus.UNAUTHORIZED);
    }

    /**
     * 获取缓存key
     */
    private String getTokenKey(String token) {
        return CacheConstants.LOGIN_TOKEN_KEY + token;
    }

    /**
     * 获取缓存key
     */
    private String getMallTokenKey(String token) {
        return RedisConstants.MALL_TOKENS + token;
    }

    /**
     * 获取缓存key
     */
    private String getOpenapiTokenKey(String token) {
        return RedisConstants.OPENAPI_TOKENS + token;
    }

    /**
     * 获取SAAS请求token
     */
    private String getSaasToken(ServerHttpRequest request) {
        String token = request.getHeaders().getFirst(TokenConstants.SAAS_ACCESS_TOKEN);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX)) {
            token = token.replaceFirst(TokenConstants.PREFIX, StringUtils.EMPTY);
        }
        return token;
    }

    /**
     * 获取请求token
     */
    private String getToken(ServerHttpRequest request) {
        String token = request.getHeaders().getFirst(TokenConstants.AUTHENTICATION);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX)) {
            token = token.replaceFirst(TokenConstants.PREFIX, StringUtils.EMPTY);
        }
        return token;
    }

    private String getMallToken(ServerHttpRequest request) {
        String token = request.getHeaders().getFirst(TokenConstants.MALL_AUTHORIZATION);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.MALL_PREFIX)) {
            token = token.replaceFirst(TokenConstants.MALL_PREFIX, StringUtils.EMPTY);
        }
        return token;
    }

    private String getOpenapiToken(ServerHttpRequest request) {
        String token = request.getHeaders().getFirst(TokenConstants.OPENAPI_AUTHORIZATION);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.OPENAPI_PREFIX)) {
            token = token.replaceFirst(TokenConstants.OPENAPI_PREFIX, StringUtils.EMPTY);
        }
        return token;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        innerWebClient = WebClient.builder().filter(loadBalancerExchangeFilterFunction).build();
       // saasWebClient = WebClient.create(b2bSaasAuthLoginVerificationHost);
       // saasUserCache = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES).maximumSize(8_000L).build();
        b2bUserCache = CacheBuilder.newBuilder().expireAfterWrite(5, TimeUnit.MINUTES).maximumSize(8_000L).build();
    }

    @Override
    public int getOrder()
    {
        return -200;
    }
}
