package com.zksr.gateway.handler;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.exception.CaptchaException;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.redis.service.RedisService;
import com.zksr.gateway.service.ValidateCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.HandlerFunction;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

import java.io.IOException;

/**
 * 验证码获取
 *
 * <AUTHOR>
 */
@Component
public class ValidateCodeHandler implements HandlerFunction<ServerResponse>
{

    @Autowired
    private RedisService redisService;
    @Autowired
    private ValidateCodeService validateCodeService;

    @Override
    public Mono<ServerResponse> handle(ServerRequest serverRequest)
    {
        AjaxResult ajax = null;
        ServerHttpRequest request = serverRequest.exchange().getRequest();
        String userAgent = request.getHeaders().getFirst("User-Agent");
        // 非PC端不校验验证码
        if (ObjectUtil.isNotEmpty(userAgent) && (userAgent.contains("Android") || userAgent.contains("iPhone"))) {
            ajax = AjaxResult.success().put("captchaEnabled", false);
        }
        MultiValueMap<String, String> queryParams = request.getQueryParams();
        String username = queryParams.getFirst("username");
        if (ObjectUtil.isNotEmpty(username)) {
            Integer retryCount = redisService.getCacheObject(CacheConstants.PWD_ERR_CNT_KEY + username.toString());
            if (ObjectUtil.isEmpty(retryCount) || retryCount < CacheConstants.PASSWORD_MIN_RETRY_COUNT) {
                ajax = AjaxResult.success().put("captchaEnabled", false);
            }
        } else {
            ajax = AjaxResult.success().put("captchaEnabled", false);
        }
        if (ObjectUtil.isNotEmpty(ajax)) {
            return ServerResponse.status(HttpStatus.OK).body(BodyInserters.fromValue(ajax));
        }
        try
        {
            ajax = validateCodeService.createCaptcha();
        }
        catch (CaptchaException | IOException e)
        {
            return Mono.error(e);
        }
        return ServerResponse.status(HttpStatus.OK).body(BodyInserters.fromValue(ajax));
    }

}
