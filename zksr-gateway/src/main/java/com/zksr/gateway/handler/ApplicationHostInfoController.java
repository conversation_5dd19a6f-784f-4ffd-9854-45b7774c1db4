package com.zksr.gateway.handler;

import com.alibaba.fastjson2.JSON;
import com.zksr.gateway.config.properties.ApplicationHostProperties;
import com.zksr.gateway.config.properties.CaptchaProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

@RestController
@Slf4j
public class ApplicationHostInfoController {

    @Autowired
    private ApplicationHostProperties applicationHostProperties;

    @Autowired
    private CaptchaProperties captchaProperties;

    @GetMapping("/host-info")
    public Mono<ApplicationHostProperties.HostProperties> getHostProperty(ServerHttpRequest request) {
        String hostHeader = request.getHeaders().getFirst("Host");
        if (hostHeader == null || hostHeader.isEmpty()) {
            return Mono.error(new IllegalArgumentException("Missing or empty Host header"));
        }

        log.info(hostHeader);

        List<ApplicationHostProperties.HostProperties> hostList = applicationHostProperties.getHostsList();
        ApplicationHostProperties.HostProperties res = null;
        for (ApplicationHostProperties.HostProperties host : hostList) {
//            log.info("value:{}", JSON.toJSON(host));
            if (host.getHostName().equals(hostHeader)) {
                return Mono.just(host);
            }
        }
        return Mono.error(new IllegalArgumentException("未匹配到host"));
    }
}
