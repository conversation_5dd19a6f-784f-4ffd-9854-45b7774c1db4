package com.zksr.gateway.util;

import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.web.domain.AjaxResultBase;
import com.zksr.common.core.web.pojo.CommonResult;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.Charset;

public class WebfluxResponseUtil {

//    /**
//     * webflux的response返回json对象
//     */
//    public static Mono<Void> responseWriter(Boolean success, ServerWebExchange exchange, int httpStatus, String msg) {
//        Result result = Result.of(success, null, httpStatus, msg, null);
//        return responseWrite(exchange, httpStatus, result);
//    }
//
//    public static Mono<Void> responseFailed(ServerWebExchange exchange, String msg) {
//        //Result result = Result.failed(msg);
//        AjaxResultBase result = AjaxResultBase.error(msg,);
//        return responseWrite(exchange, HttpStatus.INTERNAL_SERVER_ERROR.value(), result);
//    }
//
//    public static Mono<Void> responseFailed(ServerWebExchange exchange, int code, int httpStatus, String msg) {
//        //Result result = Result.failed(code, msg, null);
//        AjaxResultBase result = AjaxResultBase.error(code, msg, null);
//        return responseWrite(exchange, httpStatus, result);
//    }

    public static Mono<Void> responseFailed(ServerWebExchange exchange, int httpStatus, String msg) {
       // Result result = Result.failed(httpStatus, msg, null);
        AjaxResultBase result = AjaxResultBase.error(httpStatus, msg, null);
        return responseWrite(exchange, httpStatus, result);
    }

    public static Mono<Void> responseWrite(ServerWebExchange exchange, int httpStatus, AjaxResultBase result) {
        if (httpStatus == 0) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR.value();
        }
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().setAccessControlAllowCredentials(true);
        response.getHeaders().setAccessControlAllowOrigin("*");
        response.setStatusCode(HttpStatus.valueOf(httpStatus));
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON_UTF8);
        DataBufferFactory dataBufferFactory = response.bufferFactory();
        DataBuffer buffer = dataBufferFactory.wrap(JSONObject.toJSONString(result).getBytes(Charset.defaultCharset()));
        return response.writeWith(Mono.just(buffer)).doOnError((error) -> {
            DataBufferUtils.release(buffer);
        });
    }
}
