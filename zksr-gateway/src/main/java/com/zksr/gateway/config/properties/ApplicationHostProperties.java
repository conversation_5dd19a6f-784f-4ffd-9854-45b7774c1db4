package com.zksr.gateway.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
@ConfigurationProperties(prefix = "appconf")
@RefreshScope
public class ApplicationHostProperties {
    private List<HostProperties> hostsList = new ArrayList<>();

    public List<HostProperties> getHostsList() {
        return hostsList;
    }

    public void setHostsList(List<HostProperties> hostsList) {
        this.hostsList = hostsList;
    }

    @Data
    public static class HostProperties {
        private String hostName;
        private String platformName;
        private String comName;
        private String logo;
        private String bkg;
    }
}
