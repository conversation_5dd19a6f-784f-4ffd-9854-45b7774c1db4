package com.zksr.gateway.user.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/03/19 14:35
 */
@Data
public class UserDto {
    private Long id;

    private String accessToken;

    private String account;

    /**
     * 账号平台类型	1.集团用户 2.微信小程序用户 3. app用户 4.租户 5.第三方授权appkey用户
     */
    private int accountType;

    private Integer enableFlag;

    private Integer deleteFlag;

    private String mobile;

    private String orgCode;

    private String tenantCode;

    private String userCode;

    private String userName;

    private String createUserCode;

    private String updateUserCode;

    private Integer version;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date createTime;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date updateTime;

    /**
     * gateway传递的Host值
     */
    private String host;

    /**
     * 当前租户
     */
    private TenantCore currentTenant;
}
