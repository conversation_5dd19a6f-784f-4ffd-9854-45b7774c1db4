# Tomcat
server:
  port: 8080

# Spring
spring:
  application:
    # 应用名称
    name: zksr-gateway
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 10.27.12.137:8848
        username: nacos
        password: 50vGMVGRtg
        namespace: b2b-dev
      config:
        # 配置中心地址
        server-addr: 10.27.12.137:8848
        username: nacos
        password: 50vGMVGRtg
        namespace: b2b-dev
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-redis-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: 127.0.0.1:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: 127.0.0.1:8848
            dataId: sentinel-zksr-gateway
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: gw-flow
