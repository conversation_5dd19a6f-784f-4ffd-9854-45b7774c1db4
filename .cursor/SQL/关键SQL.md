-- 平台展示分类
select * from zksr_product.prdt_sale_class order by 1 desc limit 100 ;


-- 城市分类
select * from zksr_cloud.sys_area
select * from zksr_product.prdt_area_class	 order by 1 desc limit 100 ;
select * from zksr_cloud.sys_channel order by 1 desc limit 100 ;
select * from zksr_product.prdt_channel_area_class order by 1 desc limit 100 ;
select channel_id from zksr_member.mem_branch order by 1 desc limit 100 ;



select * from zksr_product.prdt_catgory_rate where catgory_id = 1635

-- 商品组合
select * from zksr_product.prdt_spu order by 1 desc limit 1200;
select * from zksr_product.prdt_sku where retail_price is null  order by 1 desc limit 1200
select * from zksr_product.prdt_spu_combine where spu_combine_id =  '584972398334836736';
select * from zksr_product.prdt_spu_combine_dtl where spu_combine_id =  '584972398334836736';

-- 城市商品
select * from zksr_product.prdt_area_item  where area_item_id = '635102182877429761';
select * from zksr_product.prdt_material_apply ;

-- 全国商品
select * from zksr_product.prdt_supplier_item ;
select * from zksr_product.prdt_spu_combine group by spu_combine_id having count(spu_combine_id) >1

-- 转账
select * from zksr_account.acc_transfer_flow where catgory_id = 1635
select * from zksr_account.acc_transfer;
select * from zksr_account.acc_pay_flow where trade_no = 'XSS250704510000113325';
select * from zksr_account.acc_pay_flow where trade_no = 'XSS250704510000113325';
select * from zksr_account.acc_pay_flow where trade_no = 'XSS250703230001603233';
select * from zksr_account.acc_pay_flow where pay_flow_id = '634793928771731456';
select * from zksr_trade.trd_settle where supplier_order_no = 'XSS250704510000113325'
select * from zksr_trade.trd_settle order by settle_id desc  limit 10
select * from zksr_account.acc_divide_flow order by divide_flow_id desc  limit 1000;
select * from zksr_account.acc_divide_flow where type = 2
select * from zksr_account.acc_platform_merchant where trade_no = 'XSS250702810000535959';
select * from zksr_account.acc_pay_flow order by pay_flow_id desc limit 10
select * from zksr_account.acc_platform_merchant where platform_merchant_id  in (82,83)
select * from zksr_account.acc_transfer_bill
select * from zksr_trade.trd_settle  limit 10

select * from zksr_trade.trd_order o  where order_no = ''
XSS250708440000140999

-- 账单：	******************** - XSS250704510000113325

-- 订单
select * from zksr_trade.trd_order where order_no = '********************'  limit 10;
select * from zksr_trade.trd_supplier_order where order_no = 'XSS250712820133550351'  limit 10;
select * from zksr_trade.trd_supplier_order where supplier_order_no = 'XSS250715460143807164'  limit 10;
select * from zksr_trade.trd_supplier_order_dtl d where supplier_order_no = 'XSS250715200129771428'  limit 10
select * from zksr_trade.trd_supplier_order_settle where supplier_order_no = 'XSS240929780001344512'  limit 10
select * from zksr_trade.trd_order_discount_dtl where order_id = ''  limit 10

[{"accountNo":"**********","amt":0.02,"merchantId":7,"merchantType":"dc","subAmt":0.02,"subOrderNo":"********************"}]
[{"accountNo":"**********","amt":0.05,"merchantId":***********,"merchantType":"supplier","subAmt":0.06,"subOrderNo":"XSS250712950206352991"}]

-- 支付前
-- [{"accountNo":"**********","amt":0.02,"merchantId":***********,"merchantType":"supplier","subAmt":0.02,"subOrderNo":"XSS250703230001603233"}]
-- [{"accountNo":"**********","amt":0.02,"merchantId":7,"merchantType":"dc","subAmt":0.02,"subOrderNo":"XSS250713520259367260"},{"accountNo":"**********","amt":0.06,"merchantId":***********,"merchantType":"supplier","subAmt":0.06,"subOrderNo":"XSS250713520259367260"}]
select * from zksr_account.acc_pay_flow where trade_no = 'XSS250715200129771428';
select * from zksr_account.acc_pay_flow where combine_flow_id = '202507121541578534';

-- 支付后
select * from zksr_account.acc_divide_dtl where trade_no = 'XSS240929780001344512';
select * from zksr_trade.trd_settle where supplier_order_no = 'XSS250713520259367260'

-- [{"accountNo":"**********","amt":0.02,"merchantId":7,"merchantType":"dc","subAmt":0.02,"subOrderNo":"********************"}]
select * from zksr_account.acc_pay_flow where trade_no = '********************';



-- 支付商户 (AltMchNo) 分账
ByfwJJkAS8PDcbI2ZusWjEpBn5lKp3eb

select * from zksr_account.acc_platform_merchant where merchant_id = '7' and platform = 'mideaPay';
select * from zksr_member.mem_branch where branch_id = '637429526771138560'

