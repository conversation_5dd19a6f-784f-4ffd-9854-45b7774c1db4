1. 消息发送流程：
```java
// OpenapiProducer.java
public void sendDeliveryMsg(SysInterfaceLog sysInterfaceLog) {
    boolean result = streamBridge.send(
            MessageConstant.OPENAPI_DELIVERY_TOPIC_OUT_PUT,
            MessageBuilder.withPayload(sysInterfaceLog).build());
}
```

2. 消息消费者定位：
- 消费者通过 Spring Cloud Stream 的函数式编程模型来定义，在 `OpenapiConsumer` 类中：
```java
@Bean
public Consumer<SysInterfaceLog> openapi_delivery() {
    return (data -> {
        log.info("收到订单发货推送信息:{}", data);
        //将需要同步的数据发送给指挥者
        delivery.invoke(data);
    });
}
```

3. 消息绑定配置：
在 nacos 配置文件中定义了生产者和消费者的绑定关系：
```yaml
#对外接口:订单发货
openapi_delivery-out-0:
  destination: openapi_delivery
openapi_delivery-in-0:
  destination: openapi_delivery
  group: openapi_delivery_group
```

让我来解释这个流程：

1. 当调用 `sendDeliveryMsg` 时，消息会被发送到 `MessageConstant.OPENAPI_DELIVERY_TOPIC_OUT_PUT` 这个目的地
2. Spring Cloud Stream 根据配置文件中的绑定关系，将消息路由到 `openapi_delivery` 这个目的地
3. 消费者通过 `@Bean` 注解定义的 `openapi_delivery()` 方法会自动订阅这个目的地
4. 消费者组 `openapi_delivery_group` 确保消息只被一个消费者实例处理

如果你想新增一个 `MessageConstant.OPENAPI_XXXX`，需要：

1. 在 `MessageConstant` 类中添加新的常量：
```java
public static final String OPENAPI_XXXX_TOPIC_OUT_PUT = "openapi_xxxx-out-0";
```

2. 在 nacos 配置文件中添加对应的绑定配置：
```yaml
#对外接口:新功能描述
openapi_xxxx-out-0:
  destination: openapi_xxxx
openapi_xxxx-in-0:
  destination: openapi_xxxx
  group: openapi_xxxx_group
```

3. 在 `OpenapiProducer` 中添加发送方法：
```java
public void sendXxxxMsg(SysInterfaceLog sysInterfaceLog) {
    boolean result = streamBridge.send(
            MessageConstant.OPENAPI_XXXX_TOPIC_OUT_PUT,
            MessageBuilder.withPayload(sysInterfaceLog).build());
}
```

4. 在 `OpenapiConsumer` 中添加对应的消费者方法：
```java
@Bean
public Consumer<SysInterfaceLog> openapi_xxxx() {
    return (data -> {
        log.info("收到xxx推送信息:{}", data);
        xxxx.invoke(data);  // 需要注入对应的处理模板
    });
}
```

5. 在 Spring Cloud Stream 的 function definition 中添加新的函数定义：
```yaml
spring:
  cloud:
    stream:
      function:
        definition: "openapi_saveprdt;...;openapi_xxxx"  # 添加新的函数名
```

这样就完成了一个新的消息类型的配置。消息会通过 Spring Cloud Stream 的绑定关系正确路由到对应的消费者。