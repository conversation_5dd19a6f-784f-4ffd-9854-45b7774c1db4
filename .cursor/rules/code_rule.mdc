---
alwaysApply: true
---
# 1. Entity写法规范
- 命名：数据库表名驼峰写法，且要继承 BaseEntity。
- 注解：@TableName、@Dat、@EqualsAndHashCode(callSuper = true)、@ToString(callSuper = true)、@Builder、@NoArgsConstructor、@AllArgsConstructor
- 结构：分层清晰，方法注释齐全，导出用 @Excel  。
- 示例：
```java
@TableName(value = "prm_activity")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmActivity extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 活动id */
    @ApiModelProperty("活动id")
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 平台商id */
    @ApiModelProperty("平台商id")
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定） */
    @ApiModelProperty("全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定）")
    @Excel(name = "全国或者本地(数据字典);1-全国商品可用", readConverterExp = "平=台商设定")
    private Integer funcScope;

    /** 促销单号 */
    @Excel(name = "促销单号")
    @ApiModelProperty("促销单号")
    private String prmSheetNo;

    /** 启用时间 */
    @ApiModelProperty("启用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "启用时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectTime;

    /** 是否指定渠道参与;0-所有渠道参与 1-指定渠道参与  （指定渠道和指定门店二选一） */
    @ApiModelProperty("是否指定渠道参与;0-所有渠道参与 1-指定渠道参与  （指定渠道和指定门店二选一）")
    @Excel(name = "是否指定渠道参与;0-所有渠道参与 1-指定渠道参与  ", readConverterExp = "指=定渠道和指定门店二选一")
    private Integer chanelScopeAllFlag;

    /** 是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减) */
    @ApiModelProperty("是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减)")
    @Excel(name = "是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减)")
    private Integer ladderFlag;


}
```

# 2. Controller写法规范
- 命名：以业务+Controller结尾，如 XxxController。
- 注解：@RestController、@RequestMapping、@Api、@ApiOperation、@Validated、@Log、@RequiresPermissions。
- 结构：分层清晰，方法注释齐全，参数校验用@Valid。
- 示例：
```java
@Api(tags = "管理后台 - 订单日志接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/log")
public class TrdOrderLogController {
    @Autowired
    private ITrdOrderLogService trdOrderLogService;

    /**
     * 新增订单日志
     */
    @ApiOperation(value = "新增订单日志", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "订单日志", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdOrderLogSaveReqVO createReqVO) {
        return success(trdOrderLogService.insertTrdOrderLog(createReqVO));
    }

    /**
     * 修改订单日志
     */
    @ApiOperation(value = "修改订单日志", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "订单日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdOrderLogSaveReqVO updateReqVO) {
            trdOrderLogService.updateTrdOrderLog(updateReqVO);
        return success(true);
    }

    /**
     * 删除订单日志
     */
    @ApiOperation(value = "删除订单日志", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "订单日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sysCodes}")
    public CommonResult<Boolean> remove(@PathVariable Long[] sysCodes) {
        trdOrderLogService.deleteTrdOrderLogBySysCodes(sysCodes);
        return success(true);
    }

    /**
     * 获取订单日志详细信息
     */
    @ApiOperation(value = "获得订单日志详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{sysCode}")
    public CommonResult<TrdOrderLogRespVO> getInfo(@PathVariable("sysCode") Long sysCode) {
        TrdOrderLog trdOrderLog = trdOrderLogService.getTrdOrderLog(sysCode);
        return success(HutoolBeanUtils.toBean(trdOrderLog, TrdOrderLogRespVO.class));
    }

    /**
     * 分页查询订单日志
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得订单日志分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdOrderLogRespVO>> getPage(@Valid TrdOrderLogPageReqVO pageReqVO) {
        PageResult<TrdOrderLog> pageResult = trdOrderLogService.getTrdOrderLogPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, TrdOrderLogRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:log:add";
        /** 编辑 */
        public static final String EDIT = "trade:log:edit";
        /** 删除 */
        public static final String DELETE = "trade:log:remove";
        /** 列表 */
        public static final String LIST = "trade:log:list";
        /** 查询 */
        public static final String GET = "trade:log:query";
        /** 停用 */
        public static final String DISABLE = "trade:log:disable";
        /** 启用 */
        public static final String ENABLE = "trade:log:enable";
    }
}

```


# 3. Service写法规范
- 命名：I+业务+ServiceImpl结尾，如 IXXXService。
- 注解：
- 结构：分层清晰，方法注释齐全。
- 示例：
```java
/**
 * 订单日志Service接口
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
public interface ITrdOrderLogService {

    /**
     * 新增订单日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdOrderLog(@Valid TrdOrderLogSaveReqVO createReqVO);

    /**
     * 修改订单日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdOrderLog(@Valid TrdOrderLogSaveReqVO updateReqVO);

    /**
     * 删除订单日志
     *
     * @param sysCode 平台商id
     */
    public void deleteTrdOrderLog(Long sysCode);

    /**
     * 批量删除订单日志
     *
     * @param sysCodes 需要删除的订单日志主键集合
     * @return 结果
     */
    public void deleteTrdOrderLogBySysCodes(Long[] sysCodes);

    /**
     * 获得订单日志
     *
     * @param sysCode 平台商id
     * @return 订单日志
     */
    public TrdOrderLog getTrdOrderLog(Long sysCode);

    /**
     * 获得订单日志分页
     *
     * @param pageReqVO 分页查询
     * @return 订单日志分页
     */
    PageResult<TrdOrderLog> getTrdOrderLogPage(TrdOrderLogPageReqVO pageReqVO);

}
```

# 4. ServiceImpl写法规范
- 命名：以业务+ServiceImpl结尾，如 XxxServiceImpl。
- 注解：@Service
- 结构：分层清晰，方法注释齐全。
- 示例：
```java
@Service
public class TrdOrderLogServiceImpl implements ITrdOrderLogService {
    @Autowired
    private TrdOrderLogMapper trdOrderLogMapper;

    /**
     * 新增订单日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdOrderLog(TrdOrderLogSaveReqVO createReqVO) {
        // 插入
        TrdOrderLog trdOrderLog = HutoolBeanUtils.toBean(createReqVO, TrdOrderLog.class);
        trdOrderLogMapper.insert(trdOrderLog);
        // 返回
        return trdOrderLog.getSysCode();
    }

    /**
     * 修改订单日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdOrderLog(TrdOrderLogSaveReqVO updateReqVO) {
        trdOrderLogMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, TrdOrderLog.class));
    }

    /**
     * 删除订单日志
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deleteTrdOrderLog(Long sysCode) {
        // 删除
        trdOrderLogMapper.deleteById(sysCode);
    }

    /**
     * 批量删除订单日志
     *
     * @param sysCodes 需要删除的订单日志主键
     * @return 结果
     */
    @Override
    public void deleteTrdOrderLogBySysCodes(Long[] sysCodes) {
        for(Long sysCode : sysCodes){
            this.deleteTrdOrderLog(sysCode);
        }
    }

    /**
     * 获得订单日志
     *
     * @param sysCode 平台商id
     * @return 订单日志
     */
    @Override
    public TrdOrderLog getTrdOrderLog(Long sysCode) {
        return trdOrderLogMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdOrderLog> getTrdOrderLogPage(TrdOrderLogPageReqVO pageReqVO) {
        return trdOrderLogMapper.selectPage(pageReqVO);
    }

    private void validateTrdOrderLogExists(Long sysCode) {
        if (trdOrderLogMapper.selectById(sysCode) == null) {
            throw exception(TRD_ORDER_LOG_NOT_EXISTS);
        }
    }
}
```

# 5. Feign远程调用写法
- 接口用@FeignClient注解，value为服务名，contextId区分接口。
- 方法用@RequestMapping/@PostMapping等，参数用@RequestParam/@RequestBody。
- 示例：
```java
@FeignClient(contextId = "remoteAccountApi", value = ApiConstants.NAME)
public interface AccountApi {
    @PostMapping("/rpc-api/account/getSupplierAccount")
    CommonResult<AccAccountDTO> getSupplierAccount(@RequestParam("supplierId") Long supplierId);
}
```

# 6. DTO写法规范
- 命名：XxxDTO、XxxReqVO、XxxRespVO等。
- 注解：@Data、@ApiModel、@ApiModelProperty、@Excel、@JsonFormat等。
- 结构：字段注释齐全，必要时加校验注解。
- 示例：
```java
@ApiModel("订单日志 - trd_order_log分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdOrderLogPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    @ApiModelProperty(value = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    @ApiModelProperty(value = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 操作前状态 */
    @Excel(name = "操作前状态")
    @ApiModelProperty(value = "操作前状态")
    private Long beforeState;

    /** 操作后状态 */
    @Excel(name = "操作后状态")
    @ApiModelProperty(value = "操作后状态")
    private Long afterState;

    /** 操作类型(数据字典) */
    @Excel(name = "操作类型(数据字典)")
    @ApiModelProperty(value = "操作类型(数据字典)")
    private Long operateType;

    /** 订单日志信息 */
    @Excel(name = "订单日志信息")
    @ApiModelProperty(value = "订单日志信息")
    private String content;


}
```

# 7. Mapper接口写法
- 继承BaseMapper<Xxx>，加@Mapper注解。
- 命名：XxxMapper。
- 注意：使用 default 可以重新重写对应的的方法
- 示例：
```java
/**
 * 订单日志Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@Mapper
public interface TrdOrderLogMapper extends BaseMapperX<TrdOrderLog> {
    default PageResult<TrdOrderLog> selectPage(TrdOrderLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdOrderLog>()
                    .eqIfPresent(TrdOrderLog::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdOrderLog::getSupplierOrderDtlId, reqVO.getSupplierOrderDtlId())
                    .eqIfPresent(TrdOrderLog::getSupplierOrderDtlNo, reqVO.getSupplierOrderDtlNo())
                    .eqIfPresent(TrdOrderLog::getBeforeState, reqVO.getBeforeState())
                    .eqIfPresent(TrdOrderLog::getAfterState, reqVO.getAfterState())
                    .eqIfPresent(TrdOrderLog::getOperateType, reqVO.getOperateType())
                    .eqIfPresent(TrdOrderLog::getContent, reqVO.getContent())
                .orderByDesc(TrdOrderLog::getSysCode));
    }

    /**
     * 根据入驻商订单明细ID 获取入驻商订单明细操作记录
     * @param supplierOrderDtlId 入驻商订单明细ID
     * @return
     */
//    default List<TrdOrderLog> selectListBySupplierOrderDtlId(Long supplierOrderDtlId) {
//        return selectList(new LambdaQueryWrapperX<TrdOrderLog>()
//                .eqIfPresent(TrdOrderLog::getSupplierOrderDtlId, supplierOrderDtlId));
//    }

    List<TrdOrderLog> selectListBySupplierOrderDtlId(@Param("supplierOrderDtlId") Long supplierOrderDtlId);

}
```

# 7. Mapper.xml接口写法
- 命名：XXXMapper.xml。
- 示例：
```java
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdOrderLogMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- 根据供应商订单明细编号，查询订单日志 -->
    <select id="selectListBySupplierOrderDtlId" resultType="com.zksr.trade.domain.TrdOrderLog">
        SELECT
            MIN(create_time) AS createTime
            ,after_state
            ,supplier_order_dtl_id
        FROM
            trd_order_log
        WHERE
            supplier_order_dtl_id = #{supplierOrderDtlId}
        GROUP BY
            supplier_order_dtl_id
            ,after_state
    </select>
</mapper>

也可以类似

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdOrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- 用户小程序查询订单start -->
    <!-- 订单列表 -->
    <resultMap id="queryOrderAll" type="com.zksr.trade.api.order.dto.TrdOrderRespDTO">
        <id property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="orderType" column="orderType"/>
        <collection property="supplierOrderList" select="querySupplierOrderList"
                    column="{orderId=order_id,deliveryState=deliveryState,keyWords = keyWords,supplierOrderId = supplierOrderId}"/>
    </resultMap>

    <!-- 入驻商订单列表 -->
    <resultMap id="supplierOrderList" type="com.zksr.trade.api.order.dto.TrdSupplierOrderDTO">
        <id property="supplierOrderId" column="supplier_order_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="subAmt" column="subAmt"/>
        <result property="subNum" column="subNum"/>
        <result property="deliveryState" column="sDeliveryState"/>
        <collection property="supplierOrderDtlDTOList"
                    select="querySupplierOrderDtlList"
                    column="{supplierOrderId=supplier_order_id ,deliveryState=sDeliveryState ,keyWords = keyWords}"/>
    </resultMap>

    <resultMap id="orderList" type="com.zksr.trade.controller.order.vo.DcOrderPageRespVO">
        <id property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="deliveryState" column="delivery_state"/>
        <result property="payWay" column="pay_way"/>
        <result property="branchId" column="branch_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="spuId" column="spu_id"/>
        <result property="totalNum" column="total_num"/>
        <result property="price" column="price"/>
        <result property="totalAmt" column="total_amt"/>
        <result property="totalNum" column="send_qty"/>
        <result property="shortageNum" column="cancel_qty"/>
        <result property="discrepancyAmt" column="cancel_amt"/>
        <result property="itemType" column="item_type"/>
        <result property="supplierOrderId" column="supplier_order_id"/>
        <collection property="expressesList" select="getExpressesList" column="supplier_order_dtl_id">
            <id property="orderExpressId" column="order_express_id"/>
            <result property="expressNo" column="express_no"/>
            <result property="expressCom" column="express_com"/>
            <result property="expressComNo" column="express_com_no"/>
            <result property="receivePhone" column="receive_phone"/>
        </collection>
    </resultMap>

    <resultMap id="printOrder" type="com.zksr.trade.print.vo.PrintSupplierOrderVO">
        <id property="orderId" column="order_id"/>
        <result property="createTime" column="create_time"/>
        <result property="colonelId" column="colonel_id"/>
        <result property="branchId" column="branch_id"/>
        <result property="payWay" column="pay_way"/>
        <result property="orderNo" column="order_no"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierOrderId" column="supplier_order_id"/>
        <result property="supplierOrderNo" column="supplier_order_no"/>
        <result property="subDiscountAmt" column="sub_discount_amt"/>
        <result property="subPayAmt" column="sub_pay_amt"/>
        <result property="subOrderAmt" column="sub_order_amt"/>
        <result property="subOrderNum" column="sub_order_num"/>
        <collection property="supplierOrderDtlVOList" ofType="com.zksr.trade.print.vo.PrintSupplierOrderDtlVO">
            <id property="supplierOrderDtlId" column="supplier_order_dtl_id"/>
            <result property="spuId" column="spu_id"/>
            <result property="skuId" column="sku_id"/>
            <result property="totalAmt" column="total_amt"/>
            <result property="salePrice" column="sale_price"/>
            <result property="totalNum" column="total_num"/>
            <result property="price" column="price"/>
            <result property="memo" column="memo"/>
            <result property="orderUnit" column="order_unit"/>
            <result property="orderUnitQty" column="order_unit_qty"/>
            <result property="orderUnitSize" column="order_unit_size"/>
            <result property="orderUnitPrice" column="order_unit_price"/>
            <result property="cancelAmt" column="cancel_amt"/>
            <result property="cancelQty" column="cancel_qty"/>
        </collection>
    </resultMap>

    <select id="countSupplierOrderAll" resultType="com.zksr.trade.api.order.vo.SupplierOrderCountAllRespVO">
        select
        sys_code,
        create_date,
        order_type,
        sum(qty) qty,
        sum(sub_order_amt) sub_order_amt
        from
        (
        select
        sys_code,
        DATE_FORMAT(o.create_time, '%Y-%m-%d') as create_date,
        count(1) qty,
        sum(sub_order_amt) sub_order_amt,
        8 order_type
        from
        trd_supplier_order o
        <where>
            1 = 1
            <if test="null != param.sysCode">
                and sys_code = #{param.sysCode}
            </if>
            <if test="null != param.startDate and null != param.endDate ">
                and create_time BETWEEN #{param.startDate} AND #{param.endDate}
            </if>

        </where>
        group by
        o.sys_code,
        DATE_FORMAT(o.create_time, '%Y-%m-%d'),
        order_type
        union all
        select
        a.sys_code,
        DATE_FORMAT(a.create_time, '%Y-%m-%d') as create_date,
        count(1) qty,
        sum(sub_refund_amt) sub_order_amt,
        9 order_type
        from
        trd_supplier_after a

        <where>
            1 = 1
            <if test="null != param.sysCode">
                and sys_code = #{param.sysCode}
            </if>
            <if test="null != param.startDate and null != param.endDate ">
                and create_time BETWEEN #{param.startDate} AND #{param.endDate}
            </if>

        </where>
        group by
        a.sys_code,
        DATE_FORMAT(a.create_time, '%Y-%m-%d'),
        order_type

        ) t
        <where>
            1 = 1
            <if test="null != param.sysCode">
                and sys_code = #{param.sysCode}
            </if>
            <if test="null != param.startDate and null != param.endDate ">
                and create_date BETWEEN #{param.startDate} AND #{param.endDate}
            </if>

        </where>
        group by
        sys_code,
        create_date,
        order_type
        order by
        create_date desc,
        order_type asc
    </select>
</mapper>

```

# 5. Api接口写法
- 以XxxApi命名，@FeignClient声明，方法参数注解齐全。
- 示例：
```java
@FeignClient(contextId = "remoteProductApi", value = ApiConstants.NAME)
public interface ProductApi {
    @GetMapping("/rpc-api/product/getProduct")
    CommonResult<ProductDTO> getProduct(@RequestParam("productId") Long productId);
}
```

# 6. Enum枚举写法
- 命名：XxxEnum。
- 结构：有code和desc，提供get方法和静态valueOf。
- 示例：
```java
/**
 * 分润模式
 */
@Getter
public enum ProfitModeEnum {
	DICT_CODE("profit_mode","数据字典"),
	ALL("ALL", "全部"),
	RATE("RATE", "按比例"),
	RULE("RULE", "平台规则");
	
	private final String code;
	private final String name;
	
	ProfitModeEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}
	
	public static String getValue(String key){
		if (StringUtils.isEmpty(key)){
			return  null;
		}
		ProfitModeEnum[] values = ProfitModeEnum.values();
		for(ProfitModeEnum value : values){
			if(value.getCode().equals(key)){
				return value.getName();
			}
		}
		return null;
	}
	
	public static ProfitModeEnum getByName(String name) {
		if (StringUtils.isBlank(name)) {
			return null;
		}
		for (ProfitModeEnum enums : ProfitModeEnum.values()) {
			
			if (StringUtils.equals(enums.getName(), name)) {
				return enums;
			}
		}
		return null;
	}
	
	public static String getCodeByName(String name) {
		if (StringUtils.isBlank(name)) {
			return null;
		}
		for (ProfitModeEnum enums : ProfitModeEnum.values()) {
			
			if (StringUtils.equals(enums.getName(), name)) {
				return enums.getCode();
			}
		}
		return null;
	}

} 
```

# 7. @Api/@ApiOperation等注解用法
- @Api：类上，描述接口分组。
- @ApiOperation：方法上，描述接口功能。
- @ApiModel/@ApiModelProperty：DTO/VO上，描述字段。
- 示例：
```java
@Api(tags = "订单管理")
public class OrderController {
    @ApiOperation(value = "新增订单", httpMethod = "POST")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody OrderDTO order) { ... }
}
```

# 8. 关键注解写法
- @RequiresPermissions：权限控制，参数为权限标识。
- @DistributedLock：分布式锁，常用于幂等、并发控制。
- @Log：操作日志，title+businessType。
- 示例：
```java
@RequiresPermissions("product:add")
@DistributedLock(prefix = RedisLockConstants.LOCK_PRODUCT_ADD, condition = "#productId")
@Log(title = "新增商品", businessType = BusinessType.INSERT)
```

# 9. Mapper.xml写法
- 命名空间与Mapper接口一致。
- 只在复杂SQL、多表关联时使用，常规CRUD用注解/自动生成。
- 示例：
```xml
<mapper namespace="com.zksr.product.mapper.ProductMapper">
    <select id="selectProductList" resultType="com.zksr.product.domain.Product">
        SELECT * FROM product WHERE name = #{name}
    </select>
</mapper>
```


# 10.数据库Mysql建表规范
- 涉及含有 “占比” “比例”等 字眼，采用以下类型和COMMENT, COMMENT 要重点标注：百分比的小数表现形式，1%表示为0.01 ：
ALTER TABLE `zksr_member`.`mem_branch` ADD COLUMN `profit_rate` decimal(7,6) NULL COMMENT '分润比例 百分比的小数表现形式，1%表示为0.01';

- 涉及含有 “模式” “规格”等 字眼，采用以下类型和COMMENT ，COMMENT 要重点标注：(1-x、2-x、3-x、4-x、5-x)：
ALTER TABLE `zksr_member`.`mem_branch` ADD COLUMN wholesale_profit_mode TINYINT(1) DEFAULT 4  COMMENT 'B2b分润模式(1-不分润、2-按比例、3-按金额、4-按平台规则、5-按供货价差额)';

- 涉及含有 “是否” “开启”等 字眼，采用以下类型和COMMENT ，字段用 enable_开头 ， COMMENT 要重点标注：（0 否 1 是)：
ALTER TABLE zksr_cloud.sys_partner ADD COLUMN enable_o2o TINYINT(1) DEFAULT 0 COMMENT '开启O2O（0 否 1 是)';

- 涉及含有 “价格” “价钱”等 字眼：
ALTER TABLE zksr_cloud.sys_partner ADD COLUMN retail_price DECIMAL(12,2) DEFAULT 0 COMMENT '零售价',;


# 11.系统工具类
- SecurityContextHolder
- SecurityUtils

# 12、字典
String maxDictValue = String.valueOf(dictDataApi.getMaxdictValue(dictType));

# 11. 其他规范
- 统一使用lombok简化实体/DTO/VO代码。
- 代码注释齐全，方法、参数、返回值均需说明。
- 目录结构分层清晰：controller、service、domain、mapper、api、dto、vo、enums、convert等。
- 统一异常处理、统一返回结构（如CommonResult、AjaxResult等）。
- 代码风格遵循阿里Java开发手册。


