---
description: 
globs: 
alwaysApply: false
---
-名词解析
  - 商户
    - SOFTWARE("software", "ST","软件商"),
    - PARTNER("partner", "PT","平台商"),
    - PARTNER_FREE("partner-fee","PF", "平台商手续费补贴账户"),
    - PARTNER_COLONEL("partner-colonel", "PC", "平台商统一业务员分润提成账户"),
    - DC("dc", "DC","运营商"),
    - SUPPLIER("supplier", "SP","入驻商"),
    - COLONEL("colonel", "CL","业务员"),
    - BRANCH("branch", "BH","门店储值"),
    - MEMBER("member", "BR","用户"),
    - BRANCH_DEBT("branch-debt", "BT","门店欠款账户"),

- 支付方式
  - 支持多种支付渠道，统一由 `PayChannelEnum` 枚举定义：
    - MOCK（模拟支付）
    - HLB（合利宝）
    - MIDEA_PAY（美的付）
    - WX_B2B_PAY（微信B2B支付）
    - WALLET（储值支付）
    - 细分如 HLB_WX_LITE、MIDEA_PAY_WX_LITE 等


 - 合利宝和美的付支付配置字段：
    
    ### 共有字段
    
    | 字段名 | 说明 |
    |--------|------|
    | `notifyUrl` | 回调域名前缀 |
    | `merchantNo` | 商户号 |
    
    ### 美的付特殊字段
    
    | 字段名 | 说明 |
    |--------|------|
    | `subMerchantNo` | 美的付入驻商充值商户号 |
    | `signUrl` | 签名地址 |
    | `checkSignUrl` | 验签地址 |
    | `payUrl` | 美的付地址 |
    | `partnerKey` | 合作伙伴密钥 |
    | `merchantNo` | 美的付平台商户号 |
    
    ### 合利宝特殊字段
    
    | 字段名 | 说明 |
    |--------|------|
    | `platformMerchantNo` | 合利宝平台商户号 |
    | `publicMerchantMd5SignKey` | 公共产品MD5签名秘钥 |
    | `publicEncryptKey` | 公共产品Des3加密key |
    | `pubCert` | 公证书 |
    | `merchantPrivateKey` | sm2证书 |
    | `pass` | sm2证书密码 |
    
    注意：`merchantNo`在两个系统中都存在，但美的付的注释中特别说明了是"美的付平台商户号"，而合利宝的注释是"商户号"，因此虽然字段名相同，但实际业务含义可能有差异。


- 支付体系由 `PayTypeEnum` 区分：
  - PAY（支付平台）
  - STORE（储值，已逐步废弃）


- 代付交易（普通代付） - 提现
  定义：指由第三方（如平台、企业或机构）代替用户向指定收款方支付款项。
  场景：常见于电商平台退款、佣金发放、分账结算等。例如：平台将货款代付给商家，或公司将工资代付给员工。
  特点：资金流向由第三方发起，收款方通常是他人或特定账户，而非用户本人

# 2. 订单与支付方式绑定
- 订单与支付方式通过 `AccPay`（账户付款单）表绑定：
  - 字段 `orderNo` 关联商城订单
  - 字段 `payType` 标识支付类型（0-支付 1-退款）
  - 字段 `platform` 标识支付平台（如mideaPay、hlb等）
  - 字段 `payWay` 标识具体支付方式（如在线/储值）
- 订单支付流水通过 `AccPayFlow` 记录，包含订单号、支付平台、支付方式、回调状态等

# 3. 支付流程与入口
- 支付主流程入口为 `PayOrderServiceImpl.submitOrder(PayOrderSubmitReqVO)`
  - 1）参数校验、订单类型校验
  - 2）获取订单处理器，业务前置校验
  - 3）获取支付配置与PayClient（如美的付、合利宝等）
  - 4）生成支付流水 `AccPayFlow`
  - 5）调用PayClient发起支付（如美的付统一下单）
  - 6）更新支付流水，处理支付结果
  - 7）如支付成功，发送MQ通知业务模块
- 退款流程入口为 `PayOrderServiceImpl.createPayRefund(PayRefundOrderSubmitReqVO)`

# 4. 支付完成后的流程
- 支付回调由各支付平台回调接口触发（如美的付回调）
- 回调处理入口：`PayOrderServiceImpl.notifyOrder(PayOrderRespDTO, Integer orderType)`
  - 1）查找支付流水，校验回调
  - 2）更新支付状态
  - 3）通知业务模块（如订单、分账等）
- 退款回调同理，处理退款状态与业务通知

# 5. 支付与平台商家关系
- 每笔支付均关联 `sysCode`（平台商ID）、`merchantId`（商户ID）、`merchantType`（商户类型）
- 支付配置（如美的付、合利宝）由平台商级别配置，支持多平台多商户
- 分账模式、手续费等可按平台商配置

# 6. 涉及的表与实体
- acc_pay：账户付款单（主表，订单与支付方式绑定）
- acc_pay_flow：支付流水（每次支付/退款的详细流水）
- acc_account：账户信息
- acc_divide_dtl：分账明细
- 相关实体：AccPay、AccPayFlow、AccAccount、AccDivideDtl 等

# 7. 鉴权与加密方法
- 支付接口调用、回调均需验签（如美的付用 sign() 方法）
- 敏感信息（如银行卡号）用AES加密（如MideaPaySdkClient.aesEncode）
- 回调验签：MideaPaySdkClient.checkSign
- 分布式锁保证幂等（@DistributedLock）
- 业务参数加密传输，接口层防重放

# 8. 美的付交易流程
- 1）统一下单：MideaPayAppletPayClient.unifiedOrder -> MideaPaySdkClient.mideaPay
  - 构造支付参数、子单、签名，发起httpPost到美的付支付网关
  - 回调地址配置为/pay/notify/order/...
- 2）支付回调：MideaPayAppletPayClient.parseOrderNotify
  - 验签，解析回调参数，更新支付状态
- 3）退款：MideaPayAppletPayClient.unifiedRefund -> MideaPaySdkClient.mideaRefund
  - 构造退款参数、签名，发起httpPost到美的付退款网关
  - 回调地址配置为/account/pay/notify/refund/...
- 4）退款回调：MideaPayAppletPayClient.parseRefundNotify
  - 验签，解析回调参数，更新退款状态
- 5）提现/转账：MideaPaySdkClient.mideaWithdraw/mideaTransfer
  - 构造参数、加密敏感信息、签名，发起httpPost
- 6）所有请求均需签名，回调需验签，敏感信息AES加密

# 9. 其他说明
- 支付相关流程均有详细日志与异常处理，便于追踪
- 业务流程与支付解耦，支付结果通过MQ异步通知业务
- 统一支付入口，便于扩展新支付渠道
- 详细流程、参数、回调格式见各PayClient实现与MideaPaySdkClient


