### 1、入口：

    com.zksr.account.service.profitOrder.impl.ProfitOrderServiceImpl.profitOrder

2、根据对应的sysCode获取配置:

    com.zksr.account.service.profitOrder.impl.DivideChannelServiceImpl#initClient

   （1）天威证书服务器（如何申请：https://open.mideaepay.com/#/unionPage/apiInterface?id=1051）：
        
        核心：一个证书+商户号，搭建服务后，启动服务，得到验签url：【 http://10.27.11.170:28084 】

   （2）测试环境配置

    select * from zksr_cloud.sys_partner_config where sys_code = '4' and config_type = 15

    修改前：
        美的付支付	mideaPayConfig.signUrl	http://192.168.11.200:28084/rsa/sign.htm
        美的付支付	mideaPayConfig.checkSignUrl	http://192.168.11.200:28084/rsa/checkSign.htm
        美的付支付	mideaPayConfig.notifyUrl	http://test-b2b-api.zhongkeshangruan.cn/account
        美的付支付	mideaPayConfig.merchantNo	**********
        美的付支付	mideaPayConfig.subMerchantNo	**********
        美的付支付	mideaPayConfig.payUrl	https://in.mideaepayuat.com/gateway.htm

    修改后：
        美的付支付	mideaPayConfig.signUrl	http://10.27.11.170:28084/rsa/sign.htm
        美的付支付	mideaPayConfig.checkSignUrl	http://10.27.11.170:28084/rsa/checkSign.htm
        美的付支付	mideaPayConfig.notifyUrl	https://saasopen-ver.annto.com/bop/annto/saasB2BPay/
        美的付支付	mideaPayConfig.merchantNo	**********
        美的付支付	mideaPayConfig.subMerchantNo	**********
        美的付支付	mideaPayConfig.payUrl	https://in.mideaepayuat.com/gateway.htm

 3、分账请求：

    com.zksr.account.client.mideapay.MideaPaySdkClient#mideaProfitOrder       


######## 商户号配置 ###########

1、平台 - 分账配置

2、入驻商（看商品属于哪个入驻商）   - 分账配置

3、运营商（入驻商属于哪个运营商） - 分账配置

4、门店 - 自主进件（O2O新增）
