### 订单

    订单管理 > 订单管理 trd_order 、 trd_supplier_order 、trd_supplier_order_dtl
        基础设置：订单编号  下单时间 入驻商订单编号 外部订单号  单据类型  状态  异常说明  产品名称 单位  单价 要货数量 单价  要货数量  金额  合计金额  优惠金额  实际金额  订单状态  打印状态  付款方式  支付状态  业务员经理  门店名称 门店地址 同步标识
        trd_order ：         
                1、【订单id(主键)】 
                2、【运营/用户/业务员/门店/平台商ID】 
                3、【支付状态/平台/方式/时间/金额/手续费/手续费率/退款】
                4、【订单金额/编号/类型/来源】
        trd_supplier_order ：
                1、【入驻商订单id(主键)】 
                2、【入驻商/平台商ID/司机ID】 
                3、【入驻商订单编号/订单号（trd_order.id）/外部订单号】 
                4、【订单金额/优惠金额/订单数量】 
                5、【支付状态/金额/手续费】 
                6、【推送状态】 
                7、【打印次数/父库存下单】
        trd_supplier_order_dtl :
                【主键标识】
                    supplier_order_dtl_id (主键)
                【系统/平台信息】
                    sys_code (平台商id)
                    supplier_id (入驻商id)
                    platform (支付平台)
                【订单关联信息】
                    supplier_order_id (入驻商订单id)
                    supplier_order_no (入驻商订单编号)
                    order_id (订单ID)
                    line_num (入驻商订单行号)
                    command_id (加单指令ID)
                【商品信息】
                    area_item_id (城市上架商品id)
                    supplier_item_id (入驻商上架商品id)
                    spu_id (商品SPU id)
                    sku_id (商品sku id)
                    spu_name (spu名称)
                    spec_name (spu规格)
                    item_type (商品类型)
                    brand_id (品牌id)
                    category_id (管理分类id)
                【商品展示信息】
                    thumb (封面图)
                    thumb_video (封面视频)
                    images (详情页轮播)
                    details (详情信息)
                【价格信息】
                    price (成交价)
                    sale_price (原销售价)
                    exact_price (精准成交价)
                    exact_total_amt (精准商品金额)
                    order_unit_price (订单购买单位价格)
                    order_sales_unit_price (订单购买单位实际销售单价)
                    sku_avg_price (SKU最小单位数量平均单价)
                    res_price (赠品取价算法最终单价)
                    after_gift_share_price (均摊赠品优惠后的成交价)
                【金额计算】
                    total_amt (合计金额)
                    discount_amt (优惠金额-分摊)
                    discount_amt2 (优惠金额-不分摊)
                    activity_discount_amt (活动优惠金额)
                    coupon_discount_amt (优惠券优惠金额-分摊)
                    coupon_discount_amt2 (优惠券优惠金额-不分摊)
                    sub_order_amt (商品订单原总金额)
                    cancel_amt (发货前取消金额)
                    res_amt (赠品取价算法最终金额)
                【数量信息】
                    total_num (商品最小单位数量)
                    send_qty (发货数量)
                    cancel_qty (发货前取消数量)
                    reject_qty (拒收数量)
                【单位信息】
                    order_unit (订单购买单位)
                    order_unit_type (购买单位大小)
                    order_unit_qty (订单购买单位数量)
                    order_unit_size (订单购买单位换算数量)
                    send_unit (发货单位)
                    send_unit_type (单位大小)
                    send_unit_qty (发货单位数量)
                    send_unit_size (发货单位换算数量)
                    cancel_unit (发货前取消单位)
                    cancel_unit_type (单位大小)
                    cancel_unit_qty (发货前取消数量)
                    cancel_unit_size (发货前取消单位换算数量)
                    reject_unit (拒收单位)
                    reject_unit_type (单位大小)
                    reject_unit_qty (拒收单位数量)
                    reject_unit_size (拒收单位换算数量)
                【时间信息】
                    delivery_time (发货时间)
                    receive_time (收货时间)
                    complete_time (完成时间)
                    latest_date (最新生产日期)
                    oldest_date (最旧生产日期)
                【状态信息】
                    delivery_state (订单状态)
                    pay_state (支付状态)
                    del_flag (删除状态)
                    sync_flag (同步状态)
                    sync_stock (同步库存标识)
                    stock_short_flag (是否负库存下单)
                【赠品信息】
                    gift_flag (是否是赠品)
                    exact_gift_share_price (赠品优惠分摊单价)
                    gift_share_subtotal_amt (赠品分摊价小计)
                    after_gift_share_subtotal_amt (均摊赠品优惠后的小计)
                    gift_price_type (赠品取价算法)
                    after_gift_share_unit_price (均摊赠品优惠后的购买单位成交价)
                    res_unit_price (赠品取价算法最终购买单位单价)
                【售后信息】
                    is_after_sales (是否可售后)
                    after_sales_time (可售后时间)
                    is_proceeds (收货是否收款)
                【其他信息】
                    memo (备注)
                    hdfk_pay_id (货到付款单id)
                    version (版本号)
                    create_by (创建人)
                    create_time (创建时间)
                    update_by (更新人)
                    update_time (更新时间)
        
    订单管理 > 订单清账
    订单管理 > 批量导入发货日志信息
    订单管理 > 售后管理
    订单管理 > 投诉管理
    订单管理 > 司机管理
    报表管理 > 订单分佣对账单



### 商品

    基础管理 > 商品基本信息 prdt_spu 、 prdt_sku（单位） prdt_property（属性头表） prdt_property_val（属性值 - 新增一个值  = 新增一条商品）
        基础设置：商品编（平台商品库：PrdtPlatformSpu）、外部来源商品编号、SPU辅助的商品编号、商品名称、入驻商 、 商品类别 、商品品牌、状态 、 保质期 、生产日期  、计算方式（普通商品、称重）[小单位 瓶、 件、箱 、 桶 、 份]
        单位列表：*标准价0   +供货价   建议零售价    条码     *库存  履   起订倍数  限购  状态
                1、标准价 ， 建议零售价 ， 供货价有什么不一样？ 一级经销商供货价￥50（成本），二级经销商￥70（差价），终端建议零售价￥100（消费者看到的价格），形成层级利润分配
                2、添加单位：添加后，如果有属性值，会成倍添加
        本地上架 和 全国上架关系： 
                1、本地：（如生鲜、本地生活服务、区域限定的优惠券）仅适合本地用户购买，商家可能只希望在所属城市或区域内展示，避免跨区域配送或服务不可达的问题
                2、全国：标准化商品（如电子产品、服装）或无地域限制的虚拟商品（如在线课程），适合全国统一销售，通过“全国上架”一键覆盖所有市场。
        管理类别和展示类别的关系:
                1、管理类别：面向内部运营团队（如采购、库存、数据分析） - 商品属性、供应链需求 - 低频（避免影响系统稳定性） - 通常唯一（避免数据混乱） - 【一级分类设置扣点、分润比例】
                2、展示类别：面向消费者（如分类浏览、搜索结果、商城导航） - 用户行为、营销场景 - 高频（随活动/季节调整） - 可多归属（增强曝光）
                3、一个后台管理类目（如“智能手机”）可能对应多个前端展示类目（如“旗舰机”“学生党千元机”“拍照神器”）。
        表关系 
                SPU
                    SKU1    小    中   大
                    SKU2    小    中   大
                    SKU3    小    中   大


                 [属性名 × 属性值]  比如属性名有【A,B】，  属性值有【1,2,3】 ，使用笛卡尔积处理成 2*3 = 6条

                SPU
                    A1 SKU1    小    中   大
                    A2 SKU2    小    中   大
                    A3 SKU3    小    中   大
                    B1 SKU4    小    中   大
                    B2 SKU5    小    中   大
                    B3 SKU6    小    中   大

    商城管理 > 城市上架商品 （prdt_area_item） PrdtAreaItem   - 关联城市 、关联商品 、 关联活动 、 关联素材
        基础设置：上架状态 序号 入驻商 产品编号 商品 主图 产品名称 品牌名称 管理类别 上架城市 展示类别 小单位 中单位
        关联表：
                prdt_area_item item
                LEFT JOIN prdt_sku sku ON sku.sku_id = item.sku_id
                LEFT JOIN prdt_spu spu ON spu.spu_id = sku.spu_id
                LEFT JOIN prdt_brand brand ON brand.brand_id = spu.brand_id
                LEFT JOIN prdt_catgory catgory ON catgory.catgory_id = spu.catgory_id
                LEFT JOIN prdt_catgory catgory2 ON catgory2.catgory_id = catgory.pid
                LEFT JOIN prdt_catgory catgory1 ON catgory1.catgory_id = catgory2.pid
                LEFT JOIN prdt_area_class pac3 ON item.area_class_id = pac3.area_class_id
                LEFT JOIN prdt_area_class pac2 ON pac3.pid = pac2.area_class_id
                LEFT JOIN prdt_area_class pac1 ON pac2.pid = pac1.area_class_id
                LEFT JOIN prdt_material_apply pma ON pma.apply_id = item.area_item_id
                and pma.apply_type = 3
                and pma.start_time &lt;= now()
                and pma.end_time &gt; now()
                LEFT JOIN prdt_material pm ON pm.material_id = pma.material_id

    基础管理 > 商品库管理
    
    素材管理 > 商品素材管理
    报表管理 > 商品销售汇总
    直播中心 > 直播商品


### 管理类别
    基础管理 > 平台管理类别 PrdtCatgory  - prdt_catgory（树形结构）  
        基础设置：分类名称、分类编码、分类描述、分类图片、分类状态、分类排序、分类层级、父级分类、扣点、分润比例
        概念：
            1、管理类别：面向内部运营团队（如采购、库存、数据分析） - 商品属性、供应链需求 - 低频（避免影响系统稳定性） - 通常唯一（避免数据混乱） - 【一级分类设置扣点、分润比例】
            2、展示类别：面向消费者（如分类浏览、搜索结果、商城导航） - 用户行为、营销场景 - 高频（随活动/季节调整） - 可多归属（增强曝光）
            3、一个后台管理类目（如“智能手机”）可能对应多个前端展示类目（如“旗舰机”“学生党千元机”“拍照神器”）。

### 分类

    入驻商管理分类、平台商展示分类 、 运营商展示分类的区别：
        1、入驻商管理分类（针对商家）
            （1）帮助商家高效管理商品（如上架、库存、定价）。
            （2）适配商家自身的供应链体系（如按品牌、型号、季节分类）。
        2、平台商展示分类 （针对用户）
            （1）提升用户购物体验，降低搜索门槛。
            （2）强化平台品牌调性（如“京东自营”“天猫国际”风格）。
        3、运营商展示分类（针对运营）
            （1）适配区域市场差异（如北方展示“暖气设备”，南方展示“除湿机”）。
            （2）支持渠道定制化（如医院周边商城展示“医疗用品”专题）。

    基础管理 > 入驻商管理分类 PrdtSupplierClass prdt_supplier_class  （树形结构）  

    商城管理 > 平台商展示分类 PrdtSaleClass prdt_sale_class （树形结构）  

    商城管理 > 运营商展示分类 PrdtAreaClass prdt_area_class （树形结构）
      基础设置：城市、类别编号、类别名称、上级类别、电子围栏分类、渠道类型（第三级才展示）、展示生产日期、生产日期格式、状态
      分类与渠道的关系？
            展示分类：指运营商对产品/服务在界面或宣传物料中的呈现方式（如按功能、套餐档次、用户需求等分类）。例如：5G套餐、家庭宽带、国际漫游等。
            渠道类型：指用户接触产品的路径（如线上App、线下营业厅、代理商、社交媒体广告等）。例如：自有渠道（官网、营业厅）、合作渠道（电商平台、第三方代理商）、社交媒体（抖音、微信）。
            运营商需根据渠道特性（用户习惯、触达场景）动态设计展示分类，同时利用渠道数据持续迭代分类逻辑，最终实现资源精准投放和用户体验提升。两者关系可概括为：渠道是骨架，展示分类是血肉，共同构成完整的运营体系
      关联表：
            select * from zksr_cloud.sys_area -- 地址
            select * from zksr_product.prdt_area_class	 order by 1 desc limit 100 ;  -- 关联地址 、 渠道 
            select * from zksr_cloud.sys_channel order by 1 desc limit 100 ; -- 渠道 
            select * from zksr_product.prdt_channel_area_class order by 1 desc limit 100 ;   -- 运营商分类与渠道 1:1 关系
            select channel_id from zksr_member.mem_branch order by 1 desc limit 100 ;  -- 门店（渠道）



      