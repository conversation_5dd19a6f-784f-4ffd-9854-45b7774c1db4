### 创建订单

    ### 1. 核心订单表
    
        `trd_order` (主订单表)
        `trd_supplier_order` (入驻商订单表)
        `trd_supplier_order_dtl` (入驻商订单明细表)
    
    ### 2. 结算相关表
    
        `trd_supplier_order_settle` (入驻商订单结算表) <1:1> trd_supplier_order_dtl
    
    ### 3. 优惠相关表
    
        `trd_order_discount_dtl` (订单优惠明细表)
    
    ### 4. 日志相关表
         `trd_order_log` (订单日志表)
    
    ## 5、表之间的关系
    
        ```
        trd_order (主订单)
            ↓ 1:N
        trd_supplier_order (入驻商订单)
            ↓ 1:N  
        trd_supplier_order_dtl (订单明细)
            ↓ 1:1
        trd_supplier_order_settle (结算信息)
            ↓ 1:N
        trd_order_discount_dtl (优惠明细)
            ↓ 1:N
        trd_order_log (操作日志)
        ```
### 提交支付


    ### 涉及的主要表
    
        1. `acc_pay_flow` (AccPayFlow) - 支付流水表
            - 包含：支付金额、支付方式、支付平台、订单号、分账信息等
            - 关键字段：`pay_flow_id`、`trade_no`、`pay_amt`、`platform`、`settles`(分账信息JSON)
    
       2. `acc_divide_dtl` (AccDivideDtl) - 支付分账详情表
            - 包含：商户ID、分账金额、分账状态、支付流水ID等
            - 关键字段：pay_flow_id、merchant_id、alt_amt、online_divide_state
    
       3. `acc_divide_flow` (AccDivideFlow) - 分账流水表
            - 包含：分账类型、分账金额、分账状态等
            - 关键字段：`acc_pay_flow_id`、`type`、`divide_status`

    ### 表之间的关系
    
        ```
        acc_pay_flow (支付流水表) - 每个子单创建一条记录
            ↓ (1对多关系)
        acc_divide_dtl (分账详情表) - 每个分账方创建一条记录
            ↓ (通过 pay_flow_id 关联)
        
        acc_divide_flow (分账流水表)  - B2B分账
       
            ↓ (通过 acc_pay_flow_id 关联)
        ```

### 支付、退回、分账

    微信：

    （合单支付）:

            注意：美的付合单支付是一次提交（多子单），【多】次回调（一个子单）

            【请求支付 - 交易单号关系】
                  头
                    out_trade_no - （废弃，微信无需要）
        
                    子1
                        out_trade_no - 改成 acc_pay_flow 的 flow_id
        
                    子2
                        out_trade_no - 改成 acc_pay_flow 的 flow_id  

            【回调支付 - 交易单号关系】
                
                 头
                    out_trade_no  -》 combine_flow_id
                
                    子1
                        out_trade_no -> flow_id
        
                    子2
                        out_trade_no -> flow_id    

            【例子】
                                商户1（4元） acc_pay_flow(请求支付生成) - acc_divide_dtl（支付回调生成 - 根据acc_pay_flow.settles生成多个）
                   消费者(8元)  ->  
                                商户2（4元） acc_pay_flow(请求支付生成) - acc_divide_dtl（支付回调生成 - 根据acc_pay_flow.settles生成多个）

    合利宝：


    美的付

        （合单支付） - 【分润】 :

            注意：美的付合单支付是一次提交（多子单），一次回调（多个子单）

            【请求支付 - 交易单号关系】
                  头
                    out_trade_no - 改成 acc_pay_flow 的 combine_flow_id - 没啥用
        
                    子1 （分润）
                        out_trade_no（1） - 改成 acc_pay_flow 的 flow_id - 供应商1流水
                        out_trade_no（2） - 改成 acc_pay_flow 的 flow_id - 运营商流水
                        out_trade_no（3） - 改成 acc_pay_flow 的 flow_id - 平台商流水
                        out_trade_no（4） - 改成 acc_pay_flow 的 flow_id - 业务员流水
        
                    子2
                        out_trade_no - 改成 acc_pay_flow 的 flow_id      - 供应商2流水

        （合单支付） - 【不分润】 :

            注意：美的付合单支付是一次提交（多子单），一次回调（多个子单）

            【请求支付 - 交易单号关系】
                  头
                    out_trade_no - 改成 acc_pay_flow 的 combine_flow_id
        
                    子1 （不分润）
                        out_trade_no - 改成 acc_pay_flow 的 flow_id - 供应商1流水
        
                    子2
                        out_trade_no - 改成 acc_pay_flow 的 flow_id - 供应商2流水

         【回调支付 - 交易单号关系】
                
                 头
                    out_trade_no  -》 combine_flow_id
                
                    子1
                        out_trade_no -> flow_id
        
                    子2
                        out_trade_no -> flow_id     


                1. TrdSettle 主要在以下两个场景生成：
                
                   a. 订单支付成功后：
                    - 在 `TrdNormalOrderHandlerServiceImpl.afterOrderPay()` 方法中
                    - 调用 `trdSettleService.createBatchSettle(order)` 生成结算记录
                    - 生成后通过 `trdSettleMapper.insertBatch(settles)` 批量保存
                
                   b. 售后退款时：
                    - 通过 `trdSettleService.createBatchAfterSettle()` 生成售后结算记录
                
                2. 具体生成逻辑（在 `TrdSettleServiceImpl.createBatchSettle()` 中）：
                
                    - 首先获取入驻商订单结算信息（`TrdSupplierOrderSettle`）
                    - 获取入驻商订单信息（`TrdSupplierOrder`）
                    - 根据分销模式（O2O或非O2O）生成不同的结算记录
                    - 结算记录包含以下角色的分账：
                        - 软件商（software）
                        - 平台商（partner）
                        - 运营商（dc）
                        - 业务员（colonel）
                        - 业务员负责人（pcolonel）
                        - 门店分润（branch_profit，仅O2O模式）

                3. 结算状态：
                    - 初始状态为 `SETTLE_STATE_0`（未结算）
                    - 后续通过定时任务处理分账（`orderCreateSettleTransfer`方法）
                
                4、所以总结来说：
                TrdSettle 不是在支付时生成，而是在支付成功后的回调中生成
                生成的结算记录包含了各个角色的分账信息
                结算记录生成后，通过定时任务进行实际的分账操作

         【例子】
                                商户1（4元） acc_pay_flow(请求支付生成) - acc_divide_dtl（支付回调生成 - 根据acc_pay_flow.settles生成多个）
                   消费者(8元)  ->  
                                商户2（4元） acc_pay_flow(请求支付生成) - acc_divide_dtl（支付回调生成 - 根据acc_pay_flow.settles生成多个）

                                             -> 商户1（分账号）（4元） 
                   消费者(8元)  ->  主账号(原) 
                                             -> 商户2（分账号）（4元） 

            【订单与流水表结算表关系】
                   主单 (trd_order)
                      【供应商1】
                          子单1 （trd_supplier_order <1:1> acc_pay_flow）  20元
                             商品1 (trd_supplier_order_dtl) - （trd_supplier_order_settle）
                               结算（trd_settle）：（平台 （1元）、软件商（1元）、运营商（1元）、业务员（1元））
                             商品2 (trd_supplier_order_dtl)- （trd_supplier_order_settle）
                               结算（trd_settle）：（平台 （1元）、软件商（1元）、运营商（1元）、业务员（1元））
        
                      【供应商2】
                          子单2 （trd_supplier_order <1:1> acc_pay_flow）   30元
                            商品3 (trd_supplier_order_dtl) - （trd_supplier_order_settle）
                               结算（trd_settle）：（平台 （1元）、软件商（1元）、运营商（1元）、业务员（1元））
                            商品4 (trd_supplier_order_dtl)- （trd_supplier_order_settle）
                               结算（trd_settle）：（平台 （1元）、软件商（1元）、运营商（1元）、业务员（1元））


        （退款）：

           售后主单(trd_after)
              【供应商1】
                 子单1 （trd_supplier_after <1:1> acc_pay_flow）  20元  【全额退款】
                     商品1 (trd_supplier_after_dtl) - （trd_supplier_after_settle）
                       结算（trd_settle[金额负数]）：（平台 （-1元）、软件商（-1元）、运营商（-1元）、业务员（-1元））
                     商品2 (trd_supplier_after_dtl) - （trd_supplier_after_settle）
                       结算（trd_settle[金额负数]）：（平台 （-1元）、软件商（-1元）、运营商（-1元）、业务员（-1元））    

              【供应商2】
                 子单1 （trd_supplier_after <1:1> acc_pay_flow）  15元  【50%退款】
                     商品3 (trd_supplier_after_dtl) - （trd_supplier_after_settle）
                       结算（trd_settle[金额负数]）：（平台 （-0.5元）、软件商（-0.5元）、运营商（-0.5元）、业务员（-0.5元））   
                     商品4 (trd_supplier_after_dtl) - （trd_supplier_after_settle）
                       结算（trd_settle[金额负数]）：（平台 （-0.5元）、软件商（-0.5元）、运营商（-0.5元）、业务员（-0.5元））        


        (分账)：
             子单1 - 支付流水1（acc_pay_flow） - 总（20元） 
                    1、（平台合计：2元）（trd_settle ） 
                    2、（软件合计：2元）（trd_settle ） 
                    3、（运营合计：2元）（trd_settle ） 
                    4、（业务合计：2元）（trd_settle ） 
                    = 剩余就是入驻商的钱（12元）
             子单2 - 支付流水2（acc_pay_flow） - 总（30元） 
                    1、（平台合计：2元）（trd_settle ） 
                    2、（软件合计：2元）（trd_settle ） 
                    3、（运营合计：2元）（trd_settle ） 
                    4、（业务合计：2元）（trd_settle ） 
                    = 剩余就是入驻商的钱（12元）
    


### 分润逻辑
     
    ---------------------注意---------------------------
    现在sysCode = 4 的 支付配置是 按比例，所以想要有profit ， 建议将初始化初始扣点设置为29%！
    商品供货价0.01 ，销售价0.02  ，当买了4个，总支出 0.08
    此时商品的profit = 0.08（总价格） - （0.02-0.01[入驻商利润]） * 29%（初始扣点设置为） = 0.02

    1、[平台 -支付] 利润算法 = 按销售价 - 比例 

                

    2、[平台 -支付] 利润算法 = 按毛利 - 价格 
            
                    待补充。。。。


    ### 1. 获取分润比例
    - 通过 `portalCacheService.getCatgoryByIdAndAreaId` 获取商品分类和区域对应的分润比例（如平台商、软件商、运营商、业务员等）。
      - 软件商分润比例还会通过 `portalCacheService.getPartnerDto` 获取。
    
    ### 2. 组装订单明细结算对象
    - 设置订单号、供应商订单号、明细号、供应商ID、应付金额等基本信息。
      - 获取SKU信息，设置商品数量、单价、总金额、成本价、入驻商金额（成本价×数量）、运费（默认0）、利润（商品金额-入驻商金额-运费）。
    
    ### 3. 利润模式处理
    - 如果 `profitModel` 为0，按利润模式比例计算利润（如按商品销售分润比例）。
         商品的利润：
                    for skuId in skuIdList:
                        if sku 不存在:
                            rate = 0
                        elif sku 有自定义占比:
                            rate = sku.自定义占比--> 【SKU商品.saleTotalRate】
                        elif 供应商+一级分类有占比:
                            rate = 供应商+一级分类占比 ->【prdt_supplier_class_rate.saleTotalRate】
                        elif 一级分类有占比:
                            rate = 一级分类占比  --> 【平台分类一级分类的扣点】
                        elif 平台有默认占比:
                            rate = 平台默认占比  --> 【平台支付配置的初始扣点】
                        else:
                            rate = 0
      - 利润不能为负，若为负则利润为0。
    
    ### 4. 分润金额计算

    软件商分润规则说明 （向下取整）：
     利润是100元，
     1、软件商10%，100*10%=10元；
     2、平台商30%，100*30%=30元
     3.1、运营商60%,100*（100%-40%）*60%=36元
     3.3、业务经理30%,100*（100%-40%）*30%=18元
     3.3、业务员10%，100*（100%-40%）*10%=6元
    
     * 特别说明：
     * 1.如果运营商没有设置二级分润比例，则剩余分润金额100 * （100%-30%）= 70元  全部归到运营商；
     * 2.如果订单没有业务员，则本该分给业务员的分润全部归到运营商下面；
     * 3.如果业务员有设置提成系数，假如是 提成系数为80%，按照上面的例子，实际业务员管理员分润为：100 * （100%-30%）* 30%  * 80% = 16.8元，剩余20%（21 * 20%=4.2元）业务员分润金额归到运营商下面， 如果业务员没有设置提成系数，按100% 处理；
     * 4.如果是业务员管理员产生的订单，按照上面的例子，该业务员管理员所得的分润金额为 21 + 35 = 56元
     */




                


微信合单支付（即“合单下单”）和分账是两个不同的业务流程，虽然它们都和支付有关，但目的和实现机制不同。下面用通俗的方式解释为什么合单支付后还需要单独进行分账：

---

### 1. 合单支付的作用

- 合单支付：是指用户在微信支付时，可以将多个订单合并成一个支付请求，一次性付款，提升用户体验和支付效率。
- 本质：只是把多个订单的金额合并，生成一个“合单号”，用户只需支付一次，后台再拆分到各个订单。

---

### 2. 分账的作用

- 分账：是指一笔支付成功后，需要把这笔钱按照一定的规则分配给多个收款方（如平台、商家、门店、业务员等）。
- 本质：是对一笔已完成支付的资金进行“二次分配”，确保每个参与方都能拿到属于自己的那部分钱。

---

### 3. 为什么合单支付后还要分账？

- 合单支付 只是解决了“多订单合并收款”的问题，但钱是先到主收款方（如平台）账户。
- 分账 是解决“多方收益分配”的问题，需要把钱再分给各个参与方。
- 微信支付的合单接口和分账接口是完全独立的，合单支付完成后，微信不会自动帮你分账，必须再调用微信的分账API，指定分账明细，微信才会把钱分到各个子商户。

---

### 4. 业务举例

假设用户在平台下单，买了A商家和B商家的商品，平台用合单支付收款，钱先到平台账户。  
但A、B商家、平台、业务员都要分钱，这时就要用分账，把钱按规则分到各自账户。

---

### 5. 代码和系统实现

- 合单支付完成后，系统会监听支付成功回调，然后发起分账请求，生成 `AccDivideFlow` 和 `AccDivideDtl` 记录，调用微信分账API。
- 分账完成后，系统再更新各方的分账状态。

---

### 6、美的付分账功能

https://open.mideaepay.com/#/unionPage/productCenter?id=1050&name=%E5%A4%9A%E7%BA%A7%E5%88%86%E8%B4%A6

![img.png](../图片/img.png)

多级分账
美的支付推出多层级分账体系，满足电商、售后网点、销售链销售等场景。目前美的付分账体系，支持分账到美的付企业账户或者个人账户。

1.能力简介
多级分账产品是美的支付专为订单收款多层级资金分发场景打造的支付、结算、分账解决方案。多级分账支持将多个商户的订单进行合单支付如付款订单中包含多个不同的收款对象），合单支付款项分别进入到收款商户各自的账户（资金为冻结状态，可用于后续的多层级分账）；在结算完成后，业务系统可将分账指令告知美的支付，美的支付执行业务系统的分账，剩余的资金可解冻至收款商户的现金户。商户可根据自行业务特点自由组合，在不同场景下实现高效分账。

总结：  
合单支付解决“多订单合并收款”，分账解决“多方收益分配”。两者是不同的业务场景，合单支付后还要分账，是为了让每个参与方都能拿到属于自己的钱。

如需更详细的微信官方文档或代码流程，可以进一步说明。


