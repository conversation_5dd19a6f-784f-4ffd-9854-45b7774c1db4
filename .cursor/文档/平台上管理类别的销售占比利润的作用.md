Ran tool
Read file: zksr-modules/zksr-product/src/main/java/com/zksr/product/service/impl/PrdtSkuServiceImpl.java
平台商管理分类里的 `private BigDecimal saleTotalRate` 字段（销售占比利润）主要作用和用途如下：

---

### 1. 字段含义

- **saleTotalRate**：表示平台商对某一级分类商品设置的“销售占比利润”比例（如0.29代表29%），只有一级分类可以设置。

---

### 2. 用途与流转

#### ① 分润比例的来源之一

- 在订单分润计算时（如`TradePriceServiceImpl#calculateRateAmt`），会通过一系列优先级（SKU、入驻商分类、一级分类、平台默认）获取商品的分润比例。
- 如果SKU和入驻商分类都没有设置，则会取一级分类（即平台商管理分类）上的`saleTotalRate`作为该商品的分润比例。

#### ② 参与利润分配

- 该比例会被封装到`PrdtSkuSaleTotalRateVO`对象中，最终在订单结算时用于计算平台商分润金额。
- 具体分润公式（举例）：
  - 平台商分润 = 商品利润 × saleTotalRate
  - 剩余利润再分配给运营商、业务员等

#### ③ 相关代码流转

- 订单创建时会调用`skuApi.getSkuSaleTotalRate`，内部会优先查找SKU、入驻商分类、再查一级分类（即本字段）。
- 相关代码（`PrdtSkuServiceImpl#getSkuSaleTotalRate`）：
  - 如果SKU和入驻商分类都没有设置分润比例，则查找一级分类的`saleTotalRate`，并作为该商品的分润比例。

---

### 3. 典型业务场景

- 平台商希望对某一级分类下的所有商品统一设置分润比例时，只需在该一级分类设置`saleTotalRate`即可，后续所有订单分润会自动引用。
- 该比例直接影响平台商在每笔订单中的分润金额。

---

### 4. 相关表/对象

- 该字段通常存储在商品分类表（如`prdt_catgory`）中。
- 通过缓存、API等方式在订单分润计算时被读取。

---

## 总结

**saleTotalRate 字段是平台商在一级分类上设置的销售利润分润比例，主要用于订单分润时作为平台商分润比例的来源之一，直接影响平台商的分润金额。**

如需追踪具体某个订单/商品的分润比例是如何选取的，可以进一步查看`getSkuSaleTotalRate`方法的调用链和优先级逻辑。

好的，下面详细说明**商品分润比例（saleTotalRate）**在系统中是如何通过一系列优先级（SKU、入驻商分类、一级分类、平台默认）获取的，以及每一步依赖了哪些表的信息。

---

## 1. 优先级获取逻辑

在`PrdtSkuServiceImpl#getSkuSaleTotalRate`方法中，分润比例的获取顺序如下：

### ① SKU级别（商品级别）
- **优先查找 SKU 表（prdt_sku）** 的 `saleTotalRate` 字段。
- 如果该 SKU 设置了分润比例，则直接使用。

### ② 入驻商+一级分类级别
- 如果 SKU 没有设置，则查找**入驻商+一级分类**的分润比例。
- 依赖表：**prdt_supplier_class_rate**
  - 该表记录了“入驻商ID+一级分类ID”维度的分润比例（saleTotalRate）。
- 逻辑：通过商品的 SPU 找到其分类，再找到一级分类ID，然后用入驻商ID+一级分类ID查表。

### ③ 一级分类级别（平台商管理分类）
- 如果入驻商+一级分类没有设置，则查找**一级分类表（prdt_catgory）**的 `saleTotalRate` 字段。
- 逻辑：通过商品的分类ID找到其一级分类，再查一级分类的分润比例。

### ④ 平台默认
- 如果以上都没有设置，则使用**平台配置表**（如 pay_config）中的默认分润比例（defaultSaleTotalCategoryRate）。

---

## 2. 依赖的表结构与字段

| 优先级 | 依赖表名                    | 关键字段（分润比例） | 说明                                 |
|--------|-----------------------------|---------------------|--------------------------------------|
| 1      | prdt_sku                    | saleTotalRate       | SKU级别分润比例                      |
| 2      | prdt_supplier_class_rate    | saleTotalRate       | 入驻商+一级分类分润比例              |
| 3      | prdt_catgory                | saleTotalRate       | 一级分类分润比例（平台商管理分类）    |
| 4      | pay_config（或相关配置表）  | defaultSaleTotalCategoryRate | 平台默认分润比例             |

---

## 3. 代码流程简述

- 订单创建时，系统会调用`skuApi.getSkuSaleTotalRate`，传入skuId、sysCode等参数。
- 该API内部会依次查找上述表，优先级高的有值就直接返回，否则继续往下查。
- 最终返回的分润比例会用于订单分润金额的计算。

---

## 4. 业务举例

- 某商品A（skuId=1001）下单时，系统先查`prdt_sku`表的saleTotalRate字段；
- 如果没有，再查该商品所属入驻商+一级分类在`prdt_supplier_class_rate`表的saleTotalRate；
- 如果还没有，再查一级分类（如“饮料”）在`prdt_catgory`表的saleTotalRate；
- 如果都没有，最后用平台默认配置。

---

## 5. 结论

**商品分润比例的获取依赖于：**
- 商品SKU表（prdt_sku）
- 入驻商+一级分类分润表（prdt_supplier_class_rate）
- 一级分类表（prdt_catgory）
- 平台配置表（pay_config等）

**优先级为：SKU > 入驻商+一级分类 > 一级分类 > 平台默认。**

如需查看具体表结构或SQL，可进一步说明！