根据项目模块文档中的数据库表结构，我将为您列出所有表的查询语句和重点查询条件：

## 系统/组织/权限（zksr-cloud/zksr-system）

```sql
-- sys_partner（SysPartner）：平台商表（平台商户、合作方信息）
select * from zksr_cloud.sys_partner where partner_code = 'PLATFORM001';

-- sys_user（SysUser）：用户表（系统用户信息）
select * from zksr_cloud.sys_user where user_name = 'admin';

-- sys_role（SysRole）：角色表（系统角色信息）
select * from zksr_cloud.sys_role where role_name = '管理员';

-- sys_dept（SysDept）：部门表（组织结构、部门信息）
select * from zksr_cloud.sys_dept where dept_name = '技术部';

-- sys_menu（SysMenu）：菜单权限表（系统菜单、权限点）
select * from zksr_cloud.sys_menu where menu_name = '用户管理';

-- sys_post（SysPost）：岗位表（系统岗位信息）
select * from zksr_cloud.sys_post where post_name = '开发工程师';

-- sys_dict_type（SysDictType）：字典类型表（字典项类型）
select * from zksr_cloud.sys_dict_type where dict_name = '用户性别';

-- sys_dict_data（SysDictData）：数据字典表（字典项数据）
select * from zksr_cloud.sys_dict_data where dict_type = 'sys_user_sex';

-- sys_config（SysConfig）：参数配置表（系统参数配置）
select * from zksr_cloud.sys_config where config_key = 'sys.account.captchaEnabled';

-- sys_logininfor（SysLogininfor）：登录日志表（用户登录日志）
select * from zksr_cloud.sys_logininfor where user_name = 'admin' order by login_time desc;

-- sys_oper_log（SysOperLog）：操作日志表（系统操作日志）
select * from zksr_cloud.sys_oper_log where oper_user = 'admin' order by oper_time desc;

-- sys_job（SysJob）：定时任务表（定时任务主表）
select * from zksr_cloud.sys_job where job_name = '数据清理任务';

-- sys_job_log（SysJobLog）：定时任务日志表（定时任务执行日志）
select * from zksr_cloud.sys_job_log where job_id = 1 order by create_time desc;

-- sys_notice（SysNotice）：通知公告表（系统公告、通知）
select * from zksr_cloud.sys_notice where notice_type = '1' and status = '0';

-- sys_user_role（SysUserRole）：用户-角色关联表（用户与角色多对多关系）
select * from zksr_cloud.sys_user_role where user_id = 1;

-- sys_role_menu（SysRoleMenu）：角色-菜单关联表（角色与菜单多对多关系）
select * from zksr_cloud.sys_role_menu where role_id = 1;

-- sys_user_post（SysUserPost）：用户-岗位关联表（用户与岗位多对多关系）
select * from zksr_cloud.sys_user_post where user_id = 1;

-- sys_role_dept（SysRoleDept）：角色-部门关联表（角色与部门多对多关系）
select * from zksr_cloud.sys_role_dept where role_id = 1;

-- sys_supplier（SysSupplier）：供应商表（供应商基础信息）
select * from zksr_cloud.sys_supplier where supplier_name = '美的集团';

-- sys_software（SysSoftware）：软件商信息表（收银机等软件商信息）
select * from zksr_cloud.sys_software where software_name = '收银系统A';

-- sys_area（SysArea）：区域表（记录行政区域信息）
select * from zksr_cloud.sys_area where area_name = '北京市';

-- sys_dc（SysDc）：配送中心表（配送中心基础信息）
select * from zksr_cloud.sys_dc where dc_name = '北京配送中心';

-- sys_channel（SysChannel）：渠道表（业务渠道信息）
select * from zksr_cloud.sys_channel where channel_name = '线上商城';

-- sys_brand_merchant（SysBrandMerchant）：品牌商表（品牌公司信息）
select * from zksr_cloud.sys_brand_merchant where brand_name = '可口可乐';

-- sys_brand_member（SysBrandMember）：品牌商子账户表（品牌商员工账号）
select * from zksr_cloud.sys_brand_member where brand_id = 1;

-- sys_partner_config（SysPartnerConfig）：平台商配置表（平台商户参数配置）
select * from zksr_cloud.sys_partner_config where partner_id = 1;

-- sys_partner_policy（SysPartnerPolicy）：平台商策略表（平台商户业务策略）
select * from zksr_cloud.sys_partner_policy where partner_id = 1;

-- sys_partner_dict_type（SysPartnerDictType）：平台商字典类型表（平台商自定义字典类型）
select * from zksr_cloud.sys_partner_dict_type where partner_id = 1;

-- sys_partner_dict_data（SysPartnerDictData）：平台商字典数据表（平台商自定义字典项）
select * from zksr_cloud.sys_partner_dict_data where partner_id = 1;

-- sys_supplier_area（SysSupplierArea）：供应商区域表（供应商与区域的对应关系）
select * from zksr_cloud.sys_supplier_area where supplier_id = 1;

-- sys_dc_area（SysDcArea）：配送中心区域表（配送中心与区域的对应关系）
select * from zksr_cloud.sys_dc_area where dc_id = 1;

-- sys_area_city（SysAreaCity）：城市区域表（城市与区域的对应关系）
select * from zksr_cloud.sys_area_city where area_id = 1;

-- sys_group（SysGroup）：分组表（系统分组、组织分组等）
select * from zksr_cloud.sys_group where group_name = 'VIP客户组';

-- sys_export_job（SysExportJob）：导出任务表（数据导出任务记录）
select * from zksr_cloud.sys_export_job where job_name = '订单导出';

-- sys_file_import（SysFileImport）：文件导入表（文件导入任务主表）
select * from zksr_cloud.sys_file_import where import_name = '商品导入';

-- sys_file_import_dtl（SysFileImportDtl）：文件导入明细表（文件导入的明细记录）
select * from zksr_cloud.sys_file_import_dtl where import_id = 1;

-- sys_interface_log（SysInterfaceLog）：接口日志表（外部接口调用日志）
select * from zksr_cloud.sys_interface_log where interface_name = '订单推送' order by create_time desc;

-- sys_message_log（SysMessageLog）：消息日志表（系统消息发送日志）
select * from zksr_cloud.sys_message_log where message_type = 'SMS' order by send_time desc;

-- sys_message_template（SysMessageTemplate）：消息模板表（系统消息模板）
select * from zksr_cloud.sys_message_template where template_name = '订单通知';

-- sys_sms（SysSms）：短信发送表（短信发送记录）
select * from zksr_cloud.sys_sms where phone = '13800138000' order by send_time desc;

-- sys_sms_template（SysSmsTemplate）：短信模板表（短信模板信息）
select * from zksr_cloud.sys_sms_template where template_name = '验证码模板';

-- sys_openability（SysOpenability）：开放能力表（系统开放能力、API能力）
select * from zksr_cloud.sys_openability where ability_name = '订单查询';

-- sys_opensource（SysOpensource）：开源组件表（系统集成的开源组件信息）
select * from zksr_cloud.sys_opensource where component_name = 'Redis';

-- sys_pages_config（SysPagesConfig）：页面配置表（系统页面配置）
select * from zksr_cloud.sys_pages_config where page_name = '首页';

-- sys_pages_config_template（SysPagesConfigTemplate）：页面配置模板表（页面配置模板信息）
select * from zksr_cloud.sys_pages_config_template where template_name = '默认模板';

-- sys_print_settings（SysPrintSettings）：打印设置表（系统打印参数配置）
select * from zksr_cloud.sys_print_settings where printer_name = '小票打印机';

-- sys_print_template（SysPrintTemplate）：打印模板表（系统打印模板）
select * from zksr_cloud.sys_print_template where template_name = '订单小票';

-- sys_bank_channel（SysBankChannel）：联行号表（银行渠道信息）
select * from zksr_cloud.sys_bank_channel where bank_name = '中国银行';

-- sys_area_supplier_zip（SysAreaSupplierZip）：区域供应商分组表（区域与供应商分组关系）
select * from zksr_cloud.sys_area_supplier_zip where area_id = 1;

-- sys_dc_area_zip（SysDcAreaZip）：配送中心区域分组表（配送中心与区域分组关系）
select * from zksr_cloud.sys_dc_area_zip where dc_id = 1;

-- sys_dlq（SysDlq）：死信队列表（消息队列死信管理）
select * from zksr_cloud.sys_dlq where topic = 'order_topic' order by create_time desc;

-- visual_setting_master（VisualSettingMaster）：可视化配置主表（可视化配置主档）
select * from zksr_cloud.visual_setting_master where setting_name = '首页配置';

-- visual_setting_detail（VisualSettingDetail）：可视化配置明细表（可视化配置的明细项）
select * from zksr_cloud.visual_setting_detail where master_id = 1;

-- visual_setting_template（VisualSettingTemplate）：可视化配置模板表（可视化配置模板信息）
select * from zksr_cloud.visual_setting_template where template_name = '默认模板';

-- worker_node（WorkerNode）：分布式ID节点表（分布式ID生成节点信息）
select * from zksr_cloud.worker_node where host_name = 'server01';

-- wx_qr_data（WxQrData）：微信二维码数据表（微信二维码相关数据）
select * from zksr_cloud.wx_qr_data where qr_type = 'PAYMENT';

-- gen_table（GenTable）：代码生成表（代码生成器主表，记录表结构元数据）
select * from zksr_cloud.gen_table where table_name = 'sys_user';

-- gen_table_column（GenTableColumn）：代码生成字段表（代码生成器字段元数据）
select * from zksr_cloud.gen_table_column where table_id = 1;
```

## 会员/门店/业务员（zksr-member）

```sql
-- mem_member（MemMember）：会员表（存储会员的基础信息，如手机号、姓名、等级、注册时间等）
select * from zksr_member.mem_member where phone = '13800138000';

-- mem_branch（MemBranch）：门店表（记录会员下属门店的信息，支持多门店管理）
select * from zksr_member.mem_branch where branch_name = '北京朝阳店';

-- mem_colonel（MemColonel）：业务员表（记录业务员信息，支持门店与业务员的关联）
select * from zksr_member.mem_colonel where colonel_name = '张三';

-- mem_member_register（MemMemberRegister）：会员注册表（会员注册申请、审核等流程记录）
select * from zksr_member.mem_member_register where phone = '13800138000' and status = 'PENDING';

-- mem_branch_register（MemBranchRegister）：门店注册表（门店注册申请、审核等流程记录）
select * from zksr_member.mem_branch_register where branch_name = '新门店' and status = 'PENDING';

-- mem_branch_supplier（MemBranchSupplier）：门店供应商表（门店与供应商的关联信息）
select * from zksr_member.mem_branch_supplier where branch_id = 1;

-- mem_branch_user（MemBranchUser）：门店用户表（门店与用户的关联信息）
select * from zksr_member.mem_branch_user where branch_id = 1;

-- mem_colonel_branch_target（MemColonelBranchTarget）：业务员门店目标表（业务员在门店的业绩目标）
select * from zksr_member.mem_colonel_branch_target where colonel_id = 1;

-- mem_colonel_day_settle（MemColonelDaySettle）：业务员日结算表（记录业务员每日的业绩结算）
select * from zksr_member.mem_colonel_day_settle where colonel_id = 1 and settle_date = '2024-01-01';

-- mem_colonel_month_settle（MemColonelMonthSettle）：业务员月结算表（记录业务员每月的业绩结算）
select * from zksr_member.mem_colonel_month_settle where colonel_id = 1 and settle_month = '2024-01';

-- mem_colonel_target（MemColonelTarget）：业务员目标表（设定业务员的业绩目标）
select * from zksr_member.mem_colonel_target where colonel_id = 1;

-- mem_colonel_visit_log（MemColonelVisitLog）：业务员拜访日志表（记录业务员拜访门店、客户的情况）
select * from zksr_member.mem_colonel_visit_log where colonel_id = 1 order by visit_time desc;

-- mem_colonel_relation（MemColonelRelation）：业务员关系表（记录业务员与门店、会员的关系）
select * from zksr_member.mem_colonel_relation where colonel_id = 1;

-- mem_branch_lifecycle_zip（MemBranchLifecycleZip）：门店生命周期分组表（门店生命周期分组与归类）
select * from zksr_member.mem_branch_lifecycle_zip where branch_id = 1;

-- mem_colonel_branch_zip（MemColonelBranchZip）：业务员门店分组表（业务员负责门店的分组管理）
select * from zksr_member.mem_colonel_branch_zip where colonel_id = 1;

-- mem_colonel_hierarchy_zip（MemColonelHierarchyZip）：业务员层级分组表（业务员的层级、上下级关系管理）
select * from zksr_member.mem_colonel_hierarchy_zip where colonel_id = 1;

-- mem_colonel_tidy（MemColonelTidy）：业务员整理表（业务员数据整理、归档等）
select * from zksr_member.mem_colonel_tidy where colonel_id = 1;

-- mem_command（MemCommand）：指令表（系统下发给会员或门店的指令记录）
select * from zksr_member.mem_command where command_type = 'NOTICE';

-- mem_complain（MemComplain）：投诉表（会员投诉建议的记录与处理）
select * from zksr_member.mem_complain where member_id = 1 order by create_time desc;

-- mem_login_his（MemLoginHis）：登录历史表（记录会员的登录历史、设备、IP等信息）
select * from zksr_member.mem_login_his where member_id = 1 order by login_time desc;

-- mem_search_his（MemSearchHis）：会员搜索历史表（记录会员的搜索行为）
select * from zksr_member.mem_search_his where member_id = 1 order by search_time desc;

-- mem_member_open_auth（MemMemberOpenAuth）：会员第三方授权表（记录会员与第三方平台的授权信息）
select * from zksr_member.mem_member_open_auth where member_id = 1;
```

## 交易/订单/结算/售后（zksr-trade）

```sql
-- order_tbl（OrderTbl）：订单主表（平台所有订单的基础信息，包括买家、卖家、金额、状态等）
select * from zksr_trade.order_tbl where order_no = 'ORD202401010001';

-- trd_order（TrdOrder）：订单表（订单主档，记录订单基础信息）
select * from zksr_trade.trd_order where order_no = 'ORD202401010001';

-- trd_order_express（TrdOrderExpress）：订单物流表（订单物流信息、快递单号等）
select * from zksr_trade.trd_order_express where order_id = 1;

-- trd_order_share（TrdOrderShare）：订单分摊表（订单分摊、分账等信息）
select * from zksr_trade.trd_order_share where order_id = 1;

-- trd_order_log（TrdOrderLog）：订单日志表（记录订单的操作、状态变更等日志信息）
select * from zksr_trade.trd_order_log where order_id = 1 order by create_time desc;

-- trd_order_discount_dtl（TrdOrderDiscountDtl）：订单优惠明细表（订单享受的优惠明细）
select * from zksr_trade.trd_order_discount_dtl where order_id = 1;

-- trd_after（TrdAfter）：售后主表（售后单据主档，记录售后类型、状态等）
select * from zksr_trade.trd_after where order_id = 1;

-- trd_after_log（TrdAfterLog）：售后日志表（记录售后单的操作、状态变更等日志信息）
select * from zksr_trade.trd_after_log where after_id = 1 order by create_time desc;

-- trd_after_discount_dtl（TrdAfterDiscountDtl）：售后优惠明细表（售后订单的优惠明细）
select * from zksr_trade.trd_after_discount_dtl where after_id = 1;

-- trd_settle（TrdSettle）：结算表（平台与商家、供应商的结算信息）
select * from zksr_trade.trd_settle where order_id = 1;

-- trd_supplier_order（TrdSupplierOrder）：供应商订单主表（供应商订单基础信息）
select * from zksr_trade.trd_supplier_order where order_id = 1;

-- trd_supplier_order_settle（TrdSupplierOrderSettle）：供应商订单结算表（记录供应商订单的结算信息）
select * from zksr_trade.trd_supplier_order_settle where supplier_order_id = 1;

-- trd_supplier_order_dtl（TrdSupplierOrderDtl）：供应商订单明细表（供应商订单的商品明细）
select * from zksr_trade.trd_supplier_order_dtl where order_settle_id = 1;

-- trd_supplier_after（TrdSupplierAfter）：供应商售后主表（供应商售后单据主档）
select * from zksr_trade.trd_supplier_after where order_dtl_id = 1;

-- trd_supplier_after_settle（TrdSupplierAfterSettle）：供应商售后结算表（供应商售后订单的结算信息）
select * from zksr_trade.trd_supplier_after_settle where after_id = 1;

-- trd_supplier_after_dtl（TrdSupplierAfterDtl）：供应商售后明细表（供应商售后订单的商品明细）
select * from zksr_trade.trd_supplier_after_dtl where after_settle_id = 1;

-- trd_car（TrdCar）：车辆表（配送车辆信息管理）
select * from zksr_trade.trd_car where car_no = '京A12345';

-- trd_driver（TrdDriver）：司机表（配送司机信息管理）
select * from zksr_trade.trd_driver where driver_name = '李师傅';

-- trd_driver_rating（TrdDriverRating）：司机评分表（司机服务评分记录）
select * from zksr_trade.trd_driver_rating where driver_id = 1 order by rating_time desc;

-- trd_express_import（TrdExpressImport）：物流导入表（批量导入物流信息）
select * from zksr_trade.trd_express_import where import_name = '物流信息导入';

-- trd_express_import_dtl（TrdExpressImportDtl）：物流导入明细表（物流导入的明细记录）
select * from zksr_trade.trd_express_import_dtl where import_id = 1;

-- trd_express_status（TrdExpressStatus）：物流状态表（订单物流状态跟踪）
select * from zksr_trade.trd_express_status where order_id = 1 order by status_time desc;

-- trd_hdfk_pay（TrdHdfkPay）：货到付款支付表（货到付款的支付记录）
select * from zksr_trade.trd_hdfk_pay where order_id = 1;

-- trd_hdfk_pay_dtl（TrdHdfkPayDtl）：货到付款支付明细表（货到付款的明细记录）
select * from zksr_trade.trd_hdfk_pay_dtl where pay_id = 1;

-- trd_hdfk_settle（TrdHdfkSettle）：货到付款结算表（货到付款相关的结算信息）
select * from zksr_trade.trd_hdfk_settle where order_id = 1;
```

## 账户/支付/结算（zksr-account）

```sql
-- acc_account（AccAccount）：账户表（记录平台、商家、会员等各类账户的基础信息）
select * from zksr_account.acc_account where account_no = 'ACC001';

-- acc_account_flow（AccAccountFlow）：账户流水表（账户的资金变动明细）
select * from zksr_account.acc_account_flow where account_id = 1 order by flow_time desc;

-- acc_pay（AccPay）：支付表（支付订单的主表，记录支付请求、状态等信息）
select * from zksr_account.acc_pay where pay_no = 'PAY202401010001';

-- acc_pay_flow（AccPayFlow）：支付流水表（支付订单的资金流转明细）
select * from zksr_account.acc_pay_flow where pay_id = 1 order by flow_time desc;

-- acc_recharge（AccRecharge）：充值表（账户充值的主表）
select * from zksr_account.acc_recharge where recharge_no = 'RECH202401010001';

-- acc_recharge_import（AccRechargeImport）：充值导入表（批量导入充值数据）
select * from zksr_account.acc_recharge_import where import_name = '充值数据导入';

-- acc_recharge_import_dtl（AccRechargeImportDtl）：充值导入明细表（充值导入的明细记录）
select * from zksr_account.acc_recharge_import_dtl where import_id = 1;

-- acc_recharge_scheme（AccRechargeScheme）：充值方案表（定义不同的充值优惠、规则等）
select * from zksr_account.acc_recharge_scheme where scheme_name = '充值送券';

-- acc_recharge_scheme_area（AccRechargeSchemeArea）：充值方案区域表（定义充值方案适用的区域范围）
select * from zksr_account.acc_recharge_scheme_area where scheme_id = 1;

-- acc_withdraw（AccWithdraw）：提现表（账户提现的主表）
select * from zksr_account.acc_withdraw where withdraw_no = 'WD202401010001';

-- acc_withdraw_bill（AccWithdrawBill）：提现单表（提现的单据记录）
select * from zksr_account.acc_withdraw_bill where withdraw_id = 1;

-- acc_withdraw_flow（AccWithdrawFlow）：提现流水表（提现的资金流转明细）
select * from zksr_account.acc_withdraw_flow where withdraw_bill_id = 1 order by flow_time desc;

-- acc_divide_dtl（AccDivideDtl）：分账明细表（分账业务的明细记录，记录每笔分账的对象、金额等）
select * from zksr_account.acc_divide_dtl where divide_no = 'DIV202401010001';

-- acc_divide_flow（AccDivideFlow）：分账流水表（分账资金流转明细，记录分账资金的实际流转过程）
select * from zksr_account.acc_divide_flow where divide_dtl_id = 1 order by flow_time desc;

-- acc_offline_divide（AccOfflineDivide）：线下分账表（线下分账业务记录，支持线下结算场景）
select * from zksr_account.acc_offline_divide where divide_no = 'OFFDIV202401010001';

-- acc_transfer（AccTransfer）：转账表（账户间转账的主表）
select * from zksr_account.acc_transfer where transfer_no = 'TRF202401010001';

-- acc_transfer_bill（AccTransferBill）：转账单表（转账的单据记录）
select * from zksr_account.acc_transfer_bill where transfer_id = 1;

-- acc_transfer_bill_order（AccTransferBillOrder）：转账单订单表（转账单与订单的关联）
select * from zksr_account.acc_transfer_bill_order where transfer_bill_id = 1;

-- acc_transfer_flow（AccTransferFlow）：转账流水表（转账资金流转明细）
select * from zksr_account.acc_transfer_flow where transfer_bill_id = 1 order by flow_time desc;

-- acc_platform_merchant（AccPlatformMerchant）：平台商户表（平台商户的基础信息）
select * from zksr_account.acc_platform_merchant where merchant_name = '平台商户A';

-- acc_platform_merchant_wxb2b（AccPlatformMerchantWxb2b）：平台商户微信B2B表（微信B2B商户信息）
select * from zksr_account.acc_platform_merchant_wxb2b where merchant_id = 1;

-- acc_bill_file（AccBillFile）：账单文件表（记录账单相关的文件信息，如对账单、结算单等）
select * from zksr_account.acc_bill_file where bill_type = 'SETTLEMENT';
```

## 促销/活动/优惠券（zksr-promotion）

```sql
-- prm_activity（PrmActivity）：活动表（促销活动的主表，记录活动信息）
select * from zksr_promotion.prm_activity where activity_name = '双11大促销';

-- prm_coupon（PrmCoupon）：优惠券表（记录平台发放的优惠券信息）
select * from zksr_promotion.prm_coupon where coupon_code = 'COUPON001';

-- prm_coupon_batch（PrmCouponBatch）：优惠券批次表（优惠券的批量生成、发放管理）
select * from zksr_promotion.prm_coupon_batch where batch_name = '新用户专享券';

-- prm_coupon_batch_dtl（PrmCouponBatchDtl）：优惠券批次明细表（批次下的具体优惠券明细）
select * from zksr_promotion.prm_coupon_batch_dtl where batch_id = 1;

-- prm_coupon_log（PrmCouponLog）：优惠券日志表（记录优惠券的发放、使用、作废等操作日志）
select * from zksr_promotion.prm_coupon_log where coupon_id = 1 order by operate_time desc;

-- prm_coupon_template（PrmCouponTemplate）：优惠券模板表（优惠券的模板定义）
select * from zksr_promotion.prm_coupon_template where template_name = '满减券模板';

-- prm_coupon_template_extend（PrmCouponTemplateExtend）：优惠券模板扩展表（模板的扩展属性）
select * from zksr_promotion.prm_coupon_template_extend where template_id = 1;

-- prm_coupon_template_repeat_rule（PrmCouponTemplateRepeatRule）：优惠券模板重复规则表（优惠券可重复领取、使用规则）
select * from zksr_promotion.prm_coupon_template_repeat_rule where template_id = 1;

-- prm_coupon_scope_apply（PrmCouponScopeApply）：优惠券适用范围申请表（优惠券适用范围的申请、审批记录）
select * from zksr_promotion.prm_coupon_scope_apply where coupon_id = 1;

-- prm_coupon_colonel_quota（PrmCouponColonelQuota）：业务员优惠券额度表（记录业务员可发放的优惠券额度）
select * from zksr_promotion.prm_coupon_colonel_quota where colonel_id = 1;

-- prm_activity_branch_scope（PrmActivityBranchScope）：活动门店范围表（活动适用的门店范围）
select * from zksr_promotion.prm_activity_branch_scope where activity_id = 1;

-- prm_activity_channel_scope（PrmActivityChannelScope）：活动渠道范围表（活动适用的渠道范围）
select * from zksr_promotion.prm_activity_channel_scope where activity_id = 1;

-- prm_activity_city_scope（PrmActivityCityScope）：活动城市范围表（活动适用的城市范围）
select * from zksr_promotion.prm_activity_city_scope where activity_id = 1;

-- prm_activity_spu_scope（PrmActivitySpuScope）：活动SPU范围表（活动适用的商品SPU范围）
select * from zksr_promotion.prm_activity_spu_scope where activity_id = 1;

-- prm_activity_supplier_scope（PrmActivitySupplierScope）：活动供应商范围表（活动适用的供应商范围）
select * from zksr_promotion.prm_activity_supplier_scope where activity_id = 1;

-- prm_bg_rule（PrmBgRule）：买赠规则表（促销活动买赠规则）
select * from zksr_promotion.prm_bg_rule where activity_id = 1;

-- prm_cb_rule（PrmCbRule）：组合购规则表（促销活动组合购规则）
select * from zksr_promotion.prm_cb_rule where activity_id = 1;

-- prm_fd_rule（PrmFdRule）：满减规则表（促销活动满减规则）
select * from zksr_promotion.prm_fd_rule where activity_id = 1;

-- prm_fg_rule（PrmFgRule）：满赠规则表（促销活动满赠规则）
select * from zksr_promotion.prm_fg_rule where activity_id = 1;

-- prm_sp_rule（PrmSpRule）：特价规则表（促销活动特价规则）
select * from zksr_promotion.prm_sp_rule where activity_id = 1;

-- prm_sk_rule（PrmSkRule）：秒杀规则表（促销活动秒杀规则）
select * from zksr_promotion.prm_sk_rule where activity_id = 1;

-- prm_live_room（PrmLiveRoom）：直播间表（直播活动的直播间信息）
select * from zksr_promotion.prm_live_room where room_name = '新品发布会';

-- prm_live_product（PrmLiveProduct）：直播商品表（直播活动的商品信息）
select * from zksr_promotion.prm_live_product where room_id = 1;

-- prm_live_order（PrmLiveOrder）：直播订单表（直播活动的订单信息）
select * from zksr_promotion.prm_live_order where room_id = 1;
```

## 报表（zksr-report）

```sql
-- dws_trd_supplier_sales_month（DwsTrdSupplierSalesMonth）：供应商销售月报表（按月统计供应商销售数据）
select * from zksr_report.dws_trd_supplier_sales_month where supplier_id = 1 and month_id = '2024-01';

-- dws_trd_supplier_sales_day（DwsTrdSupplierSalesDay）：供应商销售日报表（按天统计供应商销售数据）
select * from zksr_report.dws_trd_supplier_sales_day where supplier_id = 1 and date_id = '2024-01-01';

-- dws_trd_branch_sales_month（DwsTrdBranchSalesMonth）：门店销售月报表（按月统计门店销售数据）
select * from zksr_report.dws_trd_branch_sales_month where branch_id = 1 and month_id = '2024-01';

-- dws_trd_branch_sales_day（DwsTrdBranchSalesDay）：门店销售日报表（按天统计门店销售数据）
select * from zksr_report.dws_trd_branch_sales_day where branch_id = 1 and date_id = '2024-01-01';

-- dws_trd_area_sales_month（DwsTrdAreaSalesMonth）：区域销售月报表（区域月度销售统计）
select * from zksr_report.dws_trd_area_sales_month where area_id = 1 and month_id = '2024-01';

-- dws_trd_area_sales_day（DwsTrdAreaSalesDay）：区域销售日报表（区域每日销售统计）
select * from zksr_report.dws_trd_area_sales_day where area_id = 1 and date_id = '2024-01-01';

-- dws_trd_colonel_sales_month（DwsTrdColonelSalesMonth）：业务员销售月报表（按月统计业务员销售数据）
select * from zksr_report.dws_trd_colonel_sales_month where colonel_id = 1 and month_id = '2024-01';

-- dws_trd_colonel_sales_day（DwsTrdColonelSalesDay）：业务员销售日报表（按天统计业务员销售数据）
select * from zksr_report.dws_trd_colonel_sales_day where colonel_id = 1 and date_id = '2024-01-01';

-- dws_trd_sku_sales_month（DwsTrdSkuSalesMonth）：SKU销售月报表（按月统计SKU销售数据）
select * from zksr_report.dws_trd_sku_sales_month where sku_id = 1 and month_id = '2024-01';

-- dws_trd_sku_sales_day（DwsTrdSkuSalesDay）：SKU销售日报表（按天统计SKU销售数据）
select * from zksr_report.dws_trd_sku_sales_day where sku_id = 1 and date_id = '2024-01-01';

-- dws_trd_area_category_sales_month（DwsTrdAreaCategorySalesMonth）：区域品类月销售表（区域品类销售月度统计）
select * from zksr_report.dws_trd_area_category_sales_month where area_id = 1 and category_id = 1 and month_id = '2024-01';

-- dws_trd_branch_category_sales_month（DwsTrdBranchCategorySalesMonth）：门店品类月销售表（门店品类销售月度统计）
select * from zksr_report.dws_trd_branch_category_sales_month where branch_id = 1 and category_id = 1 and month_id = '2024-01';

-- dws_trd_area_hours_sales_month（DwsTrdAreaHoursSalesMonth）：区域小时销售月度表（区域小时维度销售统计）
select * from zksr_report.dws_trd_area_hours_sales_month where area_id = 1 and hour = 10 and month_id = '2024-01';

-- dws_trd_branch_hours_sales_month（DwsTrdBranchHoursSalesMonth）：门店小时销售月度表（门店小时维度销售统计）
select * from zksr_report.dws_trd_branch_hours_sales_month where branch_id = 1 and hour = 10 and month_id = '2024-01';

-- ads_branch_trade_weekday_distribution（AdsBranchTradeWeekdayDistribution）：门店周分布表（统计门店订单在一周内的分布情况）
select * from zksr_report.ads_branch_trade_weekday_distribution where branch_id = 1;

-- ads_branch_tag_month（AdsBranchTagMonth）：门店标签月报表（门店标签相关的月度统计）
select * from zksr_report.ads_branch_tag_month where branch_id = 1 and tag_id = 1 and month_id = '2024-01';

-- ads_branch_cat1_orderamt_stats_month（AdsBranchCat1OrderamtStatsMonth）：门店一级品类订单金额月报表（门店一级品类订单金额的月度统计）
select * from zksr_report.ads_branch_cat1_orderamt_stats_month where branch_id = 1 and category_id = 1 and month_id = '2024-01';

-- ads_area_branch_stats_stats（AdsAreaBranchStatsStats）：区域门店统计表（按区域统计门店相关数据）
select * from zksr_report.ads_area_branch_stats_stats where area_id = 1;

-- ads_area_cat1_stats_month（AdsAreaCat1StatsMonth）：区域一级品类月度统计表（区域一级品类销售等月度统计）
select * from zksr_report.ads_area_cat1_stats_month where area_id = 1 and category_id = 1 and month_id = '2024-01';

-- ads_area_trade_weekday_distribution（AdsAreaTradeWeekdayDistribution）：区域交易周分布表（区域订单在一周内的分布情况）
select * from zksr_report.ads_area_trade_weekday_distribution where area_id = 1;

-- ads_branch_stats_month（AdsBranchStatsMonth）：门店月度统计表（门店各项业务月度统计）
select * from zksr_report.ads_branch_stats_month where branch_id = 1 and month_id = '2024-01';

-- dim_area（DimArea）：区域维度表（区域基础信息）
select * from zksr_report.dim_area where area_name = '北京市';

-- dim_branch（DimBranch）：门店维度表（门店基础信息）
select * from zksr_report.dim_branch where branch_name = '北京朝阳店';

-- dim_category（DimCategory）：品类维度表（商品品类基础信息）
select * from zksr_report.dim_category where category_name = '饮料';

-- dim_colonel（DimColonel）：业务员维度表（业务员基础信息）
select * from zksr_report.dim_colonel where colonel_name = '张三';

-- dim_sku（DimSku）：SKU维度表（商品SKU基础信息）
select * from zksr_report.dim_sku where sku_name = '可口可乐330ml';

-- dim_supplier（DimSupplier）：供应商维度表（供应商基础信息）
select * from zksr_report.dim_supplier where supplier_name = '美的集团';

-- dim_date（DimDate）：日期维度表（日期基础信息）
select * from zksr_report.dim_date where date_id = '2024-01-01';

-- dim_month（DimMonth）：月份维度表（月度基础信息）
select * from zksr_report.dim_month where month_id = '2024-01';

-- dim_partner（DimPartner）：合作方维度表（合作方基础信息）
select * from zksr_report.dim_partner where partner_name = '平台商A';

-- dim_dc（DimDc）：配送中心维度表（配送中心基础信息）
select * from zksr_report.dim_dc where dc_name = '北京配送中心';

-- dim_dict_data（DimDictData）：字典数据维度表（字典项基础信息）
select * from zksr_report.dim_dict_data where dict_label = '男';

-- dim_member（DimMember）：会员维度表（会员基础信息）
select * from zksr_report.dim_member where member_name = '张三';

-- dim_coupon_template_full（DimCouponTemplateFull）：优惠券模板全量维度表（优惠券模板基础信息）
select * from zksr_report.dim_coupon_template_full where template_name = '满减券模板';

-- dwd_colonel_visit_inc（DwdColonelVisitInc）：业务员拜访明细表（业务员拜访行为明细）
select * from zksr_report.dwd_colonel_visit_inc where colonel_id = 1 order by visit_time desc;

-- dwd_login_his_inc（DwdLoginHisInc）：会员登录明细表（会员登录行为明细）
select * from zksr_report.dwd_login_his_inc where member_id = 1 order by login_time desc;

-- dwd_trd_cancel_inc（DwdTrdCancelInc）：订单取消明细表（订单取消行为明细）
select * from zksr_report.dwd_trd_cancel_inc where order_id = 1 order by cancel_time desc;

-- dwd_trd_order_dtl_inc（DwdTrdOrderDtlInc）：订单明细表（订单商品明细）
select * from zksr_report.dwd_trd_order_dtl_inc where order_id = 1;

-- dwd_trd_return_inc（DwdTrdReturnInc）：订单退货明细表（订单退货行为明细）
select * from zksr_report.dwd_trd_return_inc where order_id = 1 order by return_time desc;

-- rpt_tag_def（RptTagDef）：标签定义表（报表标签的定义和管理）
select * from zksr_report.rpt_tag_def where tag_name = 'VIP客户';
```

这些查询语句涵盖了项目中所有主要数据库表，每个查询都包含了该表的重要查询条件，便于快速定位和查询相关数据。