1、品牌商新增
curl 'https://b2bmart-uat.annto.com/prod-api/system/brandMerchant' \
-H 'Connection: keep-alive' \
-H 'sec-ch-ua: "Chromium";v="21", " Not;A Brand";v="99"' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Content-Type: application/json;charset=UTF-8' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'X-Access-Token: C04A4E51E35DA545CF68BB33BE2666F5EC7CDA1292350B00C4BDA30F67AF893AC996AF1EE7C571BA4C9B0BAEDC4113C72CF8D4BF1ADF4013ECE64D0563F4D7219DED1F8DBB58E138E86211C77308D534AE0679BB4EBE77E2D594606788321BBF1E3521ED388C61FB298EAC02064DCF5566DFF4BDEDFD8CBE4037EE7DF41E09CD3480135C5563698948749CEA67794EC8014879376846367970FB2EDA375B73A2F4190D79D22443370C86A8BB29899964D891765D628F2A9CA6309454E376C6CC2E735195D211358E37ACFD8EADCE51AA5976F466AA1C8D532EEA906CB0ED1B70DAC3E14503B8DA3CD154AE682C889B8CC9AF81758FBCC900CA188B59C2C6E723' \
-H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36' \
-H 'sec-ch-ua-platform: "Windows"' \
-H 'Origin: https://b2bmart-uat.annto.com' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Referer: https://b2bmart-uat.annto.com/basis/brandManagement' \
-H 'Accept-Language: zh-CN,zh;q=0.9' \
-H 'Cookie: midea_auth_uat=CC84B8796D024B6F74F752A4A2B6FB427A8B3364F86A14050DD1FB34F434746A386926120BB298312369A09548974C665A503ADF9B633B5722BBD0FEAADF3422EC98F6DBA93ADF2D2BAEAFFD30355048A74B75BC0208F84549FE0C632DFB72D15566BE76B5EBCE6F7970406019E71660A6EACA441828CF83FD9238F831B46B786A54C7F860D52F41B65156DD691395303FEE040D36E179F2F53A30D4D27A1F6BB7C7755D78DFFBDF66BD1AB599546DBCA1BCD17A86F1A8FE47A99C6B774E3E362EE52FA65CD1367C2D53CF1FC41CD018848B9172FD82AA9F37F97059D05CBFFD97BA23FE61C72A0B373762FCA12B876573823D9F4432B0B2C946D223EC8EAFAF; midea_auth=4542A0B30E539FDE7A702CE2CB056BBDC32F989F5443C46F5C40DAA7E93A8F6675537AC50859D8763B986FCA7EC1B08567E61394708B135E5564AB7A43644EFF6EC7708525FF44C6C3157F2518FD318FFB5EF69A369223D59E9D77244EA2F3B521B2482836E1B35E79DB1D392361F0ED8A4D3AC966D1E4C768A0784101C4AA0911D5ACEE4A01E12949B6520D9B69DD12F3156BBE4EF7C8E0E32ADEC6D5A21F60B8C09407DD22C3BB242D3F44460364757CABAEAC61CAB8663CE8ABE230881DA0377CA88084D6B98C7930125C8CBF4A0EA6C5FCBFDC31945D8837AAA6EA9F4B4F50C7E990C67DB2828F451032D3D74FA5676E6401B320E49CD6D70F009A25896F; lang=zh-CN; timezone=GMT+08:00; SAAS_TOKEN_UAT=C04A4E51E35DA545CF68BB33BE2666F5EC7CDA1292350B00C4BDA30F67AF893AC996AF1EE7C571BA4C9B0BAEDC4113C72CF8D4BF1ADF4013ECE64D0563F4D7219DED1F8DBB58E138E86211C77308D534AE0679BB4EBE77E2D594606788321BBF1E3521ED388C61FB298EAC02064DCF5566DFF4BDEDFD8CBE4037EE7DF41E09CD3480135C5563698948749CEA67794EC8014879376846367970FB2EDA375B73A2F4190D79D22443370C86A8BB29899964D891765D628F2A9CA6309454E376C6CC2E735195D211358E37ACFD8EADCE51AA5976F466AA1C8D532EEA906CB0ED1B70DAC3E14503B8DA3CD154AE682C889B8CC9AF81758FBCC900CA188B59C2C6E723' \
--data-raw '{"status":0,"brandIds":"943,944,945","name":"vinp品牌","simpleName":"12","contactName":"12","contactPhone":"13113112333","username":"13113112333","password":"1234"}' \
--compressed

{msg: "系统异常，请联系管理员.", code: 500,…}

code: 500
data: "\n### Error updating database.  Cause: java.sql.SQLException: Field 'sys_code' doesn't have a default value\n### The error may exist in com/zksr/system/mapper/SysBrandMerchantMapper.java (best guess)\n### The error may involve com.zksr.system.mapper.SysBrandMerchantMapper.insert-Inline\n### The error occurred while setting parameters\n### SQL: INSERT INTO sys_brand_merchant (brand_merchant_id, contact_name, contact_phone, name, simple_name, status, brand_ids, create_by, create_time, update_by, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n### Cause: java.sql.SQLException: Field 'sys_code' doesn't have a default value\n; Field 'sys_code' doesn't have a default value; nested exception is java.sql.SQLException: Field 'sys_code' doesn't have a default value"
msg: "系统异常，请联系管理员."