apiVersion: apps/v1
kind: Deployment
metadata:
  name: zksr-promotion
  namespace: haixin
  labels:
    app: zksr-promotion
spec:
  replicas: 2
  selector:
    matchLabels:
      app: zksr-promotion
  template:
    metadata:
      labels:
        app: zksr-promotion
    spec:
      containers:
        - name: zksr-promotion
          image: haixin.tencentcloudcr.com/hisense/u-biz/prod/b2b/promotion:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 6104
          env:
            - name: TZ
              value: "Asia/Shanghai"
            - name: SPRING_PROFILES_ACTIVE
              value: "dev"
          resources:
            requests:
              memory: "6Gi"
              cpu: "1000m"
            limits:
              memory: "8Gi"
              cpu: "2000m"
      imagePullSecrets:
        - name: hisense