package com.zksr.promotion.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCouponTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmCouponScopeApplyMapper;
import com.zksr.promotion.domain.PrmCouponScopeApply;
import com.zksr.promotion.controller.apply.vo.PrmCouponScopeApplyPageReqVO;
import com.zksr.promotion.controller.apply.vo.PrmCouponScopeApplySaveReqVO;
import com.zksr.promotion.service.IPrmCouponScopeApplyService;

import java.util.ArrayList;
import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 优惠券关联的使用范围, 领取范围中间Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Service
public class PrmCouponScopeApplyServiceImpl implements IPrmCouponScopeApplyService {
    @Autowired
    private PrmCouponScopeApplyMapper prmCouponScopeApplyMapper;

    /**
     * 新增优惠券关联的使用范围, 领取范围中间
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrmCouponScopeApply(PrmCouponScopeApplySaveReqVO createReqVO) {
        // 插入
        PrmCouponScopeApply prmCouponScopeApply = HutoolBeanUtils.toBean(createReqVO, PrmCouponScopeApply.class);
        prmCouponScopeApplyMapper.insert(prmCouponScopeApply);
        // 返回
        return prmCouponScopeApply.getScopeApplyId();
    }

    /**
     * 修改优惠券关联的使用范围, 领取范围中间
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmCouponScopeApply(PrmCouponScopeApplySaveReqVO updateReqVO) {
        prmCouponScopeApplyMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrmCouponScopeApply.class));
    }

    /**
     * 删除优惠券关联的使用范围, 领取范围中间
     *
     * @param scopeApplyId ${pkColumn.columnComment}
     */
    @Override
    public void deletePrmCouponScopeApply(Long scopeApplyId) {
        // 删除
        prmCouponScopeApplyMapper.deleteById(scopeApplyId);
    }

    /**
     * 批量删除优惠券关联的使用范围, 领取范围中间
     *
     * @param scopeApplyIds 需要删除的优惠券关联的使用范围, 领取范围中间主键
     * @return 结果
     */
    @Override
    public void deletePrmCouponScopeApplyByScopeApplyIds(Long[] scopeApplyIds) {
        for(Long scopeApplyId : scopeApplyIds){
            this.deletePrmCouponScopeApply(scopeApplyId);
        }
    }

    /**
     * 获得优惠券关联的使用范围, 领取范围中间
     *
     * @param scopeApplyId ${pkColumn.columnComment}
     * @return 优惠券关联的使用范围, 领取范围中间
     */
    @Override
    public PrmCouponScopeApply getPrmCouponScopeApply(Long scopeApplyId) {
        return prmCouponScopeApplyMapper.selectById(scopeApplyId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmCouponScopeApply> getPrmCouponScopeApplyPage(PrmCouponScopeApplyPageReqVO pageReqVO) {
        return prmCouponScopeApplyMapper.selectPage(pageReqVO);
    }

    /**
     * 保存优惠券使用领取具体范围
     * @param spuScopeApplyIds
     * @param couponTemplateId
     * @param spuScope
     * @param receiveScope
     */
    @Override
    public void saveApplyId(String spuScopeApplyIds, Long couponTemplateId, Integer spuScope, Integer receiveScope,Integer whiteOrBlack) {
        ArrayList<String> applyIds = ListUtil.toList(spuScopeApplyIds.split(StringPool.COMMA));
        ArrayList<PrmCouponScopeApply> scopeApplyArrayList = new ArrayList<>();
        for (String applyId : applyIds) {
            PrmCouponScopeApply scopeApply = new PrmCouponScopeApply();
            // 优惠券ID
            scopeApply.setCouponTemplateId(couponTemplateId);
            // 关联ID
            scopeApply.setApplyId(Long.valueOf(applyId));
            // 类型
            scopeApply.setSpuScope(spuScope);
            scopeApply.setReceiveScope(receiveScope);
            scopeApply.setWhiteOrBlack(whiteOrBlack);
            scopeApplyArrayList.add(scopeApply);
        }
        this.prmCouponScopeApplyMapper.insertBatch(scopeApplyArrayList);
    }

    @Override
    public void deleteByCouponTemplateId(Long couponTemplateId) {
        this.prmCouponScopeApplyMapper.deleteByCouponTemplateId(couponTemplateId);
    }

    @Override
    public List<PrmCouponTemplate> getPrmCouponScopeApplyValidBySpuScope(Long applyId, Integer spuScope) {
        return this.prmCouponScopeApplyMapper.selectPrmCouponScopeApplyValidBySpuScope(applyId, spuScope);
    }

    @Override
    public List<PrmCouponScopeApply> getPrmCouponScopeApplyByCouponTemplateIdAndSpuScope(Long couponTemplateId, Integer spuScope) {
        return this.prmCouponScopeApplyMapper.selectPrmCouponScopeApplyByCouponTemplateIdAndSpuScope(couponTemplateId, spuScope);
    }

    @Override
    public List<PrmCouponScopeApply> getCouponSpuScopeApplyByCouponTemplateId(Long couponTemplateId) {
        return this.prmCouponScopeApplyMapper.selectCouponSpuScopeApplyByCouponTemplateId(couponTemplateId);
    }

    @Override
    public List<PrmCouponScopeApply> getCouponReceiveScopeApplyByCouponTemplateId(Long couponTemplateId) {
        return this.prmCouponScopeApplyMapper.selectCouponReceiveScopeApplyByCouponTemplateId(couponTemplateId);
    }

}
