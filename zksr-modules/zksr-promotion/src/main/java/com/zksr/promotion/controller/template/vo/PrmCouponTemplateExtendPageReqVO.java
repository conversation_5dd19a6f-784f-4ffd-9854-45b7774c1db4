package com.zksr.promotion.controller.template.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.alibaba.druid.util.FnvHash.Constants.DATE_FORMAT;
import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 优惠券模版拓展统计对象 prm_coupon_template_extend
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@ApiModel("优惠券模版拓展统计 - prm_coupon_template_extend分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmCouponTemplateExtendPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 优惠券模版ID */
    @ApiModelProperty(value = "优惠券在订单上合计优惠了多少")
    private Long couponTemplateId;

    /** 大区 */
    @Excel(name = "大区")
    @ApiModelProperty(value = "大区")
    private Long sysCode;

    /** 已使用优惠券张数 */
    @Excel(name = "已使用优惠券张数")
    @ApiModelProperty(value = "已使用优惠券张数")
    private Integer usedCount;

    /** 实际领取优惠券张数 */
    @Excel(name = "实际领取优惠券张数")
    @ApiModelProperty(value = "实际领取优惠券张数")
    private Integer recordCount;

    /** 优惠券关联的订单合计销售金额 */
    @Excel(name = "优惠券关联的订单合计销售金额")
    @ApiModelProperty(value = "优惠券关联的订单合计销售金额")
    private Integer totalSaleAmt;

    /** 优惠券在订单上合计优惠了多少 */
    @Excel(name = "优惠券在订单上合计优惠了多少")
    @ApiModelProperty(value = "优惠券在订单上合计优惠了多少")
    private Integer totalCouponAmt;

    @ApiModelProperty(value = "1-全国商品可用（平台商设定）2-本地商品可用（运营商设定）")
    private Integer funcScope;

    /** 优惠券名称 */
    @Excel(name = "优惠券名称")
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /** 优惠券类型 */
    @Excel(name = "优惠券类型")
    @ApiModelProperty(value = "优惠券类型")
    private Integer spuScope;

    /** 优惠券领取方式 */
    @Excel(name = "优惠券领取方式")
    @ApiModelProperty(value = "优惠券领取方式")
    private Integer receiveType;

    /** 优惠券状态 */
    @Excel(name = "优惠券状态")
    @ApiModelProperty(value = "优惠券状态")
    private Integer activityStatus;

    /** 优惠券有效时间开始时间 */
    @Excel(name = "优惠券有效时间开始时间")
    @ApiModelProperty(value = "优惠券有效时间开始时间")
    private String templateStartDate;

    /** 优惠券有效时间结束时间 */
    @Excel(name = "优惠券有效时间结束时间")
    @ApiModelProperty(value = "优惠券有效时间结束时间")
    private String templateEndDate;



}
