package com.zksr.promotion.convert.template;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.coupon.dto.CouponTemplateExtendDTO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateExtendRespVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateExtendSaveReqVO;
import com.zksr.promotion.domain.PrmCouponTemplateExtend;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
* 优惠券模版拓展统计 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-27
*/
@Mapper
public interface PrmCouponTemplateExtendConvert {

    PrmCouponTemplateExtendConvert INSTANCE = Mappers.getMapper(PrmCouponTemplateExtendConvert.class);

    PrmCouponTemplateExtendRespVO convert(PrmCouponTemplateExtend prmCouponTemplateExtend);

    PrmCouponTemplateExtend convert(PrmCouponTemplateExtendSaveReqVO prmCouponTemplateExtendSaveReq);

    PageResult<PrmCouponTemplateExtendRespVO> convertPage(PageResult<PrmCouponTemplateExtend> prmCouponTemplateExtendPage);

    @Mappings({
            @Mapping(source = "extendDTO.totalSaleAmt", target = "totalSaleAmt"),
            @Mapping(source = "extendDTO.totalCouponAmt", target = "totalCouponAmt"),
    })
    @BeanMapping(ignoreByDefault = true)
    void buildSet(@MappingTarget PrmCouponTemplateExtend templateExtend, CouponTemplateExtendDTO extendDTO);

    @Mappings({
            @Mapping(source = "prmCouponTemplateExtend.usedCount", target = "usedCount"),
            @Mapping(source = "prmCouponTemplateExtend.recordCount", target = "recordCount"),
    })
    @BeanMapping(ignoreByDefault = true)
    void buildSet(@MappingTarget PrmCouponTemplateExtend templateExtend, PrmCouponTemplateExtend prmCouponTemplateExtend);

}