package com.zksr.promotion.domain.excel;

import com.zksr.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PrmSpRuleImportExcel {
    @Excel(name = "产品编号")
    private String itemId; // 产品编号

    @Excel(name = "SKUid")
    private Long skuId; // SKUid

    @Excel(name = "产品名称")
    private String spuName; // 产品名称

    @Excel(name = "条码")
    private String barcode; // 条码

    @Excel(name = "入驻商Id")
    private Long supplierId; // 入驻商Id

    @Excel(name = "小单位标准价")
    private BigDecimal markPrice; // 小单位标准价

    @Excel(name = "中单位标准价")
    private BigDecimal midMarkPrice; // 中单位标准价

    @Excel(name = "大单位标准价")
    private BigDecimal largeMarkPrice; // 大单位标准价

    @Excel(name = "库存")
    private Integer stock; // 库存

    @Excel(name = "小单位促销价")
    private BigDecimal spPrice; // 小单位促销价

    @Excel(name = "中单位促销价")
    private BigDecimal midSpPrice; // 中单位促销价

    @Excel(name = "大单位促销价")
    private BigDecimal largeSpPrice; // 大单位促销价

    @Excel(name = "门店限量")
    private Integer onceBuyLimit; // 门店限量

    @Excel(name = "总限量")
    private Integer totalLimitQty; // 总限量
}