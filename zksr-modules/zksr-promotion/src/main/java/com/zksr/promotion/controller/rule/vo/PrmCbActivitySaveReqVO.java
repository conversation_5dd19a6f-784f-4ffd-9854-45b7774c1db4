package com.zksr.promotion.controller.rule.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.product.api.combine.vo.SpuCombineSaveReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/1/2 14:33
 * @组合促销活动活动新增对象
 */
@Data
@ApiModel("组合促销活动活动新增对象")
public class PrmCbActivitySaveReqVO extends PrmActivitySaveReqVO {

    /** 促销类型（数据字典）;SK-秒杀 FG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价  CB-组合促销*/
    @Excel(name = "促销类型", readConverterExp = "数据字典")
    @ApiModelProperty(value = "促销类型", required = true)
    private String prmNo = PrmNoEnum.CB.getType();

    @ApiModelProperty("组合促销规则")
    @Excel(name = "组合促销规则", readConverterExp = "组合促销规则")
    private PrmCbRuleSaveReqVO cbRuleSaveReqVO;

    @ApiModelProperty("门店列表")
    @Excel(name = "门店列表", readConverterExp = "门店列表")
    private List<PrmActivityWhiteOrBlackVO> branchIdList;

    @ApiModelProperty("渠道列表")
    @Excel(name = "渠道列表", readConverterExp = "渠道列表")
    private List<PrmActivityWhiteOrBlackVO> channelIdList;

    @ApiModelProperty("上架类别")
    @Excel(name = "上架类别", readConverterExp = "上架类别")
    private Long classId;

    @ApiModelProperty("上架城市")
    @Excel(name = "上架城市", readConverterExp = "上架城市")
    private Long areaId;

    @ApiModelProperty("组合促销商品信息")
    @Excel(name = "组合促销商品信息", readConverterExp = "组合促销商品信息")
    private SpuCombineSaveReqVO spuCombineSaveReqVO;





}
