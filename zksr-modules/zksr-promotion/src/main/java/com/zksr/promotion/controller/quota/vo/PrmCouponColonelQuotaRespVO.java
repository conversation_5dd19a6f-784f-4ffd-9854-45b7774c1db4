package com.zksr.promotion.controller.quota.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 业务员发券额度对象 prm_coupon_colonel_quota
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
@ApiModel("业务员发券额度 - prm_coupon_colonel_quota Response VO")
public class PrmCouponColonelQuotaRespVO {
    private static final long serialVersionUID = 1L;

    /** 业务员发券额度ID */
    @ApiModelProperty(value = "业务员发券额度ID")
    private Integer couponColonelQuotaId;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 业务员名称 */
    @Excel(name = "业务员名称")
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    /** 业务员手机号 */
    @Excel(name = "业务员手机号")
    @ApiModelProperty(value = "业务员手机号")
    private String colonelPhone;

    /** 管理门店数*/
    @Excel(name = "管理门店数")
    @ApiModelProperty(value = "管理门店数")
    private Long branchCount;

    /** 当前月销售额目标 */
    @Excel(name = "当前月销售额目标")
    @ApiModelProperty(value = "当前月销售额目标")
    private BigDecimal monthlySalesTarget;

    /** 职务 */
    @Excel(name = "职务")
    @ApiModelProperty(value = "职务")
    private Long position;

    /** 本月发券额度 */
    @Excel(name = "本月发券额度")
    @ApiModelProperty(value = "本月发券额度")
    private BigDecimal quota;

    /** 已发额度 */
    @Excel(name = "已发额度")
    @ApiModelProperty(value = "已发额度")
    private BigDecimal finishQuota;

    /** 剩余额度 */
    @Excel(name = "剩余额度")
    @ApiModelProperty(value = "剩余额度")
    private BigDecimal remainingQuota;

    /** 状态*/
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Integer status;
}
