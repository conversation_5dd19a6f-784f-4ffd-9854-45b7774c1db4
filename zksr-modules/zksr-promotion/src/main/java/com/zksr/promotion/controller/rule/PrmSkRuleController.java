package com.zksr.promotion.controller.rule;

import com.alicp.jetcache.Cache;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.exception.enums.GlobalErrorCodeConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.api.material.vo.MaterialCacheVO;
import com.zksr.promotion.controller.activity.dto.SkOrSpRuleCheckDTO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import com.zksr.promotion.controller.rule.dto.PrmSkRuleDTO;
import com.zksr.promotion.controller.rule.vo.PrmSkRulePageReqVO;
import com.zksr.promotion.controller.rule.vo.PrmSkRuleRespVO;
import com.zksr.promotion.convert.rule.PrmSkRuleConvert;
import com.zksr.promotion.domain.PrmSkRule;
import com.zksr.promotion.domain.excel.PrmBranchImportExcel;
import com.zksr.promotion.domain.excel.PrmSkRuleImportExcel;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPrmSkRuleService;
import com.zksr.promotion.service.IPromotionCacheService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 秒杀规则Controller
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Api(tags = "管理后台 - 秒杀规则接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/skRule")
public class PrmSkRuleController {
    @Autowired
    private IPrmSkRuleService prmSkRuleService;

    @Autowired
    private IPrmActivityService activityService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IPrmActivityCommonService prmActivityCommonService;

    @Autowired
    private Cache<String, MaterialCacheVO> materialCache;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    /**
     * 新增秒杀规则
     */
    @ApiOperation(value = "新增秒杀规则", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "秒杀规则", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmSkRuleDTO createReqVO) {
        Long activityId = prmSkRuleService.insertPrmSkRule(createReqVO);
        materialCache.remove(MaterialCacheVO.getActivityCacheKey(activityId));
        return success(activityId);
    }

    /**
     * 删除秒杀规则
     */
    @ApiOperation(value = "删除秒杀规则", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "秒杀规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{skRuleIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] skRuleIds) {
        prmSkRuleService.deletePrmSkRuleBySkRuleIds(skRuleIds);
        return success(true);
    }

    /**
     * 获取秒杀规则详细信息
     */
    @ApiOperation(value = "获得秒杀规则详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{skRuleId}")
    public CommonResult<PrmSkRuleRespVO> getInfo(@PathVariable("skRuleId") Long skRuleId) {
        PrmSkRule prmSkRule = prmSkRuleService.getPrmSkRule(skRuleId);
        return success(PrmSkRuleConvert.INSTANCE.convert(prmSkRule));
    }

    /**
     * 分页查询秒杀规则
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得秒杀规则分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmSkRuleRespVO>> getPage(@Valid PrmSkRulePageReqVO pageReqVO) {
        PageResult<PrmSkRule> pageResult = prmSkRuleService.getPrmSkRulePage(pageReqVO);
        return success(PrmSkRuleConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 获取秒杀活动详细信息
     */
    @ApiOperation(value = "获取秒杀活动详细信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "getSkRuleInfo")
    public CommonResult<PrmActivityRespVO> getSkRuleInfo(@RequestParam("activityId") Long activityId) {
        return success(prmSkRuleService.getSkRuleInfo(activityId));
    }

    /**
     * 编辑秒杀促销活动
     */
    @ApiOperation(value = "编辑秒杀促销活动", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "编辑秒杀促销活动", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> editSkRuleInfo(@Valid @RequestBody PrmSkRuleDTO updateReqVO) {
        prmSkRuleService.editSkRuleInfo(updateReqVO);
        // 刷新缓存
        if (Objects.nonNull(updateReqVO.getPrmStatus())) {
            activityService.reloadSupplierActivity(updateReqVO.getActivityId());

            promotionCacheService.removeSkRule(updateReqVO.getActivityId());

            materialCache.remove(MaterialCacheVO.getActivityCacheKey(updateReqVO.getActivityId()));

            String ruleKey = RedisConstants.getActivityRuleKey(updateReqVO.getPrmNo(), updateReqVO.getActivityId());

            redisService.deleteObject(ruleKey);

        }
        return success(true);
    }

    @ApiOperation(value = "导入秒杀商品", httpMethod = HttpMethod.POST,notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORT)
    @Log(title = "导入秒杀商品", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/importData")
    public CommonResult<Map<String, Object>> importData(MultipartFile file) throws Exception
    {
        ExcelUtil<PrmSkRuleImportExcel> util = new ExcelUtil<>(PrmSkRuleImportExcel.class);
        List<PrmSkRuleImportExcel> prmSkRuleList = util.importExcel(file.getInputStream(),0);
        // 调用服务层方法
        Map<String, Object> importResult = prmSkRuleService.importPrmSkRuleData(prmSkRuleList);
        CommonResult<Map<String, Object>> result = new CommonResult<>();
        result.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        result.setData(importResult);
        result.setMsg((String) importResult.get("message"));
        return result;
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载秒杀商品信息模板", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<PrmSkRuleImportExcel> util = new ExcelUtil<>(PrmSkRuleImportExcel.class);
        util.importTemplateExcel(response, "秒杀商品信息导入", StringUtils.EMPTY, null);
    }
    @ApiOperation(value = "秒杀黑白名单导入门店", httpMethod = HttpMethod.POST,notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORTBRANCH)
    @Log(title = "秒杀黑白名单导入门店", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORTBRANCH)
    @PostMapping("/importBranchData")
    public CommonResult<Map<String, Object>> importBranchData(MultipartFile file) throws Exception
    {
        ExcelUtil<PrmBranchImportExcel> util = new ExcelUtil<>(PrmBranchImportExcel.class);
        List<PrmBranchImportExcel> prmBranchList = util.importExcel(file.getInputStream(),0);
        // 调用服务层方法
        Map<String, Object> importResult = prmActivityCommonService.importPrmBranchsData(prmBranchList);
        CommonResult<Map<String, Object>> result = new CommonResult<>();
        result.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        result.setData(importResult);
        result.setMsg((String) importResult.get("message"));
        return result;
    }

    @PostMapping("/importBranchTemplate")
    @ApiOperation(value = "下载导入门店信息模板", httpMethod = HttpMethod.POST)
    public void importBranchTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<PrmBranchImportExcel> util = new ExcelUtil<>(PrmBranchImportExcel.class);
        util.importTemplateExcel(response, "导入门店信息导入", StringUtils.EMPTY, null);
    }

    /**
     * 校验秒杀活动返回结果
     */
    @PostMapping("/checkActivity")
    @ApiOperation(value = "校验秒杀活动返回结果", httpMethod = HttpMethod.POST)
    public CommonResult<SkOrSpRuleCheckDTO> checkActivity(@RequestBody PrmActivitySaveReqVO activity) {
        return success(activityService.selectRepeatActivity(activity));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:activity:sk-rule:add";
        /** 编辑 */
        public static final String EDIT = "promotion:activity:sk-rule:edit";
        /** 删除 */
        public static final String DELETE = "promotion:activity:sk-rule:remove";
        /** 列表 */
        public static final String LIST = "promotion:activity:sk-rule:list";
        /** 查询 */
        public static final String GET = "promotion:activity:sk-rule:query";
        /** 停用 */
        public static final String DISABLE = "promotion:activity:sk-rule:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:activity:sk-rule:enable";
         /** 导入 */
        public static final String IMPORT = "promotion:activity:sk-rule:import";
        /** 导入门店 */
        public static final String IMPORTBRANCH = "promotion:activity:sk-rule:importBranch";
    }
}
