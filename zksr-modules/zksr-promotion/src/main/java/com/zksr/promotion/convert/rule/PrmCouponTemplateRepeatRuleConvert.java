package com.zksr.promotion.convert.rule;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCouponTemplateRepeatRule;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateRepeatRuleRespVO;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateRepeatRuleSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 优惠券模板-重复规则 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-07
*/
@Mapper
public interface PrmCouponTemplateRepeatRuleConvert {

    PrmCouponTemplateRepeatRuleConvert INSTANCE = Mappers.getMapper(PrmCouponTemplateRepeatRuleConvert.class);

    PrmCouponTemplateRepeatRuleRespVO convert(PrmCouponTemplateRepeatRule prmCouponTemplateRepeatRule);

    PrmCouponTemplateRepeatRule convert(PrmCouponTemplateRepeatRuleSaveReqVO prmCouponTemplateRepeatRuleSaveReq);

    PageResult<PrmCouponTemplateRepeatRuleRespVO> convertPage(PageResult<PrmCouponTemplateRepeatRule> prmCouponTemplateRepeatRulePage);
}