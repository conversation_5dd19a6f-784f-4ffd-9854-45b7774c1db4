package com.zksr.promotion.controller.couponBatch.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 优惠券批量发送对象 prm_coupon_batch
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@ApiModel("优惠券批量发送 - prm_coupon_batch分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmCouponBatchPageReqVO extends PageParam {
    private static final long serialVersionUID = 1L;

    /**
     * 优惠券批量发送id
     */
    @ApiModelProperty(value = "执行状态 0-未执行，1-已执行")
    private Long couponBatchId;

    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /**
     * 全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定）
     */
    @Excel(name = "全国或者本地(数据字典);1-全国商品可用", readConverterExp = "平=台商设定")
    @ApiModelProperty(value = "全国或者本地(数据字典);1-全国商品可用", required = true)
    private Long funcScope;

    /**
     * 优惠券模板数量
     */
    @Excel(name = "优惠券模板数量")
    @ApiModelProperty(value = "优惠券模板数量")
    private Long couponTemplateQty;

    /**
     * 门店数量
     */
    @Excel(name = "门店数量")
    @ApiModelProperty(value = "门店数量")
    private Long branchQty;

    /**
     * 门店发放数量
     */
    @Excel(name = "门店发放数量")
    @ApiModelProperty(value = "门店发放数量")
    private Long branchSendQty;

    /**
     * 总计发券数量
     */
    @Excel(name = "总计发券数量")
    @ApiModelProperty(value = "总计发券数量")
    private Long totalQty;

    /**
     * 生效类型：0-定时生效，1-立即生效
     */
    @Excel(name = "生效类型：0-定时生效，1-立即生效")
    @ApiModelProperty(value = "生效类型：0-定时生效，1-立即生效")
    private Integer validType;

    /**
     * 生效时间
     */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "生效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "生效时间")
    private Date validTime;

    /**
     * 实际发券成功数量
     */
    @Excel(name = "实际发券成功数量")
    @ApiModelProperty(value = "实际发券成功数量")
    private Long realSendQty;

    /**
     * 执行状态 0-未执行，1-已执行
     */
    @Excel(name = "执行状态 0-未执行，1-已执行")
    @ApiModelProperty(value = "执行状态 0-未执行，1-已执行")
    private Integer taskExecuteStatus;

    /**
     * 审核状态
     */
    @Excel(name = "审核状态")
    @ApiModelProperty(value = "审核状态")
    private Integer auditStatus;

    /**
     * 发券人
     */
    @Excel(name = "发券人")
    @ApiModelProperty(value = "发券人")
    private String createBy;

    /**
     * 制单时间开始时间
     */
    @Excel(name = "制单时间开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "制单时间开始时间")
    private String createTimeStart;

    /**
     * 制单时间结束时间
     */
    @Excel(name = "制单时间结束时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "制单时间结束时间")
    private String createTimeEnd;

    /**
     * 生效开始时间
     */
    @Excel(name = "生效开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "生效开始时间")
    private String validTimeStart;

    /**
     * 生效结束时间
     */
    @Excel(name = "生效结束时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "生效结束时间")
    private String validTimeEnd;


}
