package com.zksr.promotion.service;

import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.api.coupon.dto.CouponReceiveScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/3/30
 * @desc 促销模块缓存
 */
public interface IPromotionCacheService {

    CouponTemplateDTO getCouponTemplate(Long couponTemplateId);

    BrandDTO getBrand(Long branchId);

    AreaClassDTO getAreaClass(Long classId);

    SaleClassDTO getSaleClass(Long classId);

    SpuDTO getSpuDTO(Long spuId);

    SupplierDTO getSupplierDTO(Long supplierId);

    BranchDTO getBranchDTO(Long branchId);

    SkuDTO getSkuDTO(Long skuId);

    /**
    * @Description: 删除促销绑定门店缓存
    * @Author: liuxingyu
    * @Date: 2024/5/16 11:47
    */
    Boolean removeBranchScope(Long activityId);

    /**
    * @Description: 删除促销绑定城市缓存
    * @Author: liuxingyu
    * @Date: 2024/5/16 11:47
    */
    Boolean removeCityScope(Long activityId);

    /**
    * @Description: 删除促销绑定渠道缓存
    * @Author: liuxingyu
    * @Date: 2024/5/16 11:48
    */
    Boolean removeChannelScope(Long activityId);

    /**
    * @Description: 删除促销绑定Spu缓存
    * @Author: liuxingyu
    * @Date: 2024/5/16 11:48
    */
    Boolean removeSpuScopeScope(Long activityId);

    /**
    * @Description: 获取促销绑定门店列表
    * @Author: liuxingyu
    * @Date: 2024/5/16 11:53
    */
    List<ActivityBranchScopeDTO> getBranchScope(Long activityId);

    /**
    * @Description: 获取促销绑定渠道列表
    * @Author: liuxingyu
    * @Date: 2024/5/16 11:53
    */
    List<ActivityChannelScopeDTO> getChannelScope(Long activityId);

    /**
    * @Description: 获取促销绑定城市列表
    * @Author: liuxingyu
    * @Date: 2024/5/16 11:53
    */
    List<ActivityCityScopeDTO> getCityScope(Long activityId);

    /**
    * @Description: 获取促销绑定spu列表
    * @Author: liuxingyu
    * @Date: 2024/5/16 11:54
    */
    List<ActivitySpuScopeDTO> getSpuScope(Long activityId);

    /**
     * 获取管理分类数据
     * @param categoryId    管理分类ID
     * @return
     */
    CatgoryDTO getCatgoryDTO(Long categoryId);

    /**
     * @Description: 获取业务员信息
     * @Author: chenmingqing
     * @Date: 2024/3/28 10:12
     */
    ColonelDTO getColonel(Long colonelId);
    /**
     * @param channelId 渠道ID
     * @return  渠道
     */
    ChannelDTO getChannelDTO(Long channelId);

    /**
     * @param areaId    区域ID
     * @return  区域
     */
    AreaDTO getAreaDTO(Long areaId);

    /**
     * @Description: 根据活动ID删除秒杀规则缓存
     * @Author: liuxingyu
     * @Date: 2024/5/16 11:54
     */
    Boolean removeSkRule(Long activityId);

    /**
     * 删除活动供应商缓存
     *
     * @param activityId
     */
    Boolean removeSupplierScope(Long activityId);

    /**
     * 通过活动缓存id获取入驻商列表
     *
     * @param activityId
     * @return
     */
    List<ActivitySupplierScopeDTO> getSupplierScope(Long activityId);

    /**
     * 获取优惠券模板的黑白名单配置
     */
    List<CouponReceiveScopeDTO> getCouponReceiveScopeList(Long couponTemplateId);
}
