package com.zksr.promotion.controller.rule.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 满赠规则对象 prm_fg_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@ApiModel("满赠规则 - prm_fg_rule分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmFgRulePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 满赠规则 */
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Long fgRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 满赠活动id */
    @Excel(name = "满赠活动id")
    @ApiModelProperty(value = "满赠活动id", required = true)
    private Long activityId;

    /** 满赠金额 */
    @Excel(name = "满赠金额")
    @ApiModelProperty(value = "满赠金额")
    private BigDecimal fullAmt;

    /** 赠品类型;0-商品 1-优惠券 */
    @Excel(name = "赠品类型;0-商品 1-优惠券")
    @ApiModelProperty(value = "赠品类型;0-商品 1-优惠券")
    private Integer giftType;

    /** sku_id */
    @Excel(name = "sku_id")
    @ApiModelProperty(value = "sku_id")
    private Long skuId;

    /** 返券模板id */
    @Excel(name = "返券模板id")
    @ApiModelProperty(value = "返券模板id")
    private Long couponTemplateId;

    /** 赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段 */
    @Excel(name = "赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    @ApiModelProperty(value = "赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    private Integer onceGiftQty;

    /** 总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段 */
    @Excel(name = "总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    @ApiModelProperty(value = "总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    private Integer totalGiftQty;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Integer status;


}
