package com.zksr.promotion.controller.activity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.promotion.controller.rule.vo.PrmActivityWhiteOrBlackVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * @Description: 促销活动校验商品适用范围实体类
 * @Author: liuxingyu
 * @Date: 2024/5/18 17:21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PrmActivityCheckScopeVO {
    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 入驻商id
     */
    @NotNull(message = "入驻商ID不能为空")
    private Long supplierId;

    /**
     * 促销类型（数据字典）;SK-秒杀 FG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价
     */
    @NotNull(message = "促销类型不能为空")
    private String prmNo;

    /**
     * 活动开始时间
     */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @NotNull(message = "活动开始时间不能为空")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @NotNull(message = "活动结束时间不能为空")
    private Date endTime;

    /**
     * 商品适用范围(数据字典);0-所有商品可用（全场） 2-指定品类可用（品类），3-指定品牌可用（品牌），4-指定商品可用（商品）
     */
    @NotNull(message = "商品适用范围不能为空")
    private Integer spuScope;

    @ApiModelProperty("spu列表")
    private List<PrmActivityWhiteOrBlackVO> spuIdList;

    /**
     * 促销状态;0-未启用 1-启用 2-停用 3-已失效（待定，过期或者无商品可参与促销）
     */
    @NotNull(message = "促销状态不能为空")
    private Integer prmStatus;

    /** 参与活动次数限制规则（数据字典）;仅特价限购，0-每日一次 1-仅一次  2-仅活动期间内首单 3-系统首单 */
    @ApiModelProperty("参与活动次数限制规则（数据字典）;仅特价限购，0-每日一次 1-仅一次  2-仅活动期间内首单 3-系统首单")
    private Integer timesRule;

    /**入驻商列表*/
    @ApiModelProperty(value = "入驻商id列表")
    private List<Long> supplierIds;
}
