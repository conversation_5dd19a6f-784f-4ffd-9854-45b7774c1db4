package com.zksr.promotion.mapper;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.promotion.api.activity.dto.CbRuleDTO;
import com.zksr.promotion.api.activity.dto.CheckActivityItemDTO;
import com.zksr.promotion.controller.activity.dto.PrmActivityReportRespDTO;
import com.zksr.promotion.controller.activity.vo.PrmActivityPageReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityReportPageVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.rule.vo.PrmFdRuleRespVO;
import com.zksr.promotion.controller.rule.vo.PrmFgRuleRespVO;
import com.zksr.promotion.domain.PrmActivity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

import static com.zksr.common.core.constant.PrmConstants.*;


/**
 * 促销活动Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Mapper
public interface PrmActivityMapper extends BaseMapperX<PrmActivity> {

    List<PrmActivity> selectListPage(@Param("reqVo") PrmActivityPageReqVO reqVO);
    default PageResult<PrmActivity> selectPage(PrmActivityPageReqVO reqVO) {
        LambdaQueryWrapperX<PrmActivity> query = new LambdaQueryWrapperX<>();
        query.eqIfPresent(PrmActivity::getActivityId, reqVO.getActivityId())
                .eqIfPresent(PrmActivity::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrmActivity::getFuncScope, reqVO.getFuncScope())
                .eqIfPresent(PrmActivity::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(PrmActivity::getPrmNo, reqVO.getPrmNo())
                .eqIfPresent(PrmActivity::getPrmSheetNo, reqVO.getPrmSheetNo())
                .likeIfPresent(PrmActivity::getActivityName, reqVO.getActivityName())
                .eqIfPresent(PrmActivity::getEffectTime, reqVO.getEffectTime())
                .eqIfPresent(PrmActivity::getEffectMan, reqVO.getEffectMan())
                .eqIfPresent(PrmActivity::getStartTime, reqVO.getStartTime())
                .eqIfPresent(PrmActivity::getEndTime, reqVO.getEndTime())
                .eqIfPresent(PrmActivity::getSpuScope, reqVO.getSpuScope())
                .eqIfPresent(PrmActivity::getChanelScopeAllFlag, reqVO.getChanelScopeAllFlag())
                .eqIfPresent(PrmActivity::getBranchScopeAllFlag, reqVO.getBranchScopeAllFlag())
                .eqIfPresent(PrmActivity::getLadderFlag, reqVO.getLadderFlag())
                .eqIfPresent(PrmActivity::getTimesRule, reqVO.getTimesRule())
                .eqIfPresent(PrmActivity::getAmtOrQty, reqVO.getAmtOrQty());
                //.eqIfPresent(PrmActivity::getPrmStatus, reqVO.getPrmStatus());

        //校验是查询启用的促销列表还是已失效的促销列表
        //已失效的促销列表：条件： 已启用并且活动结束时间小于或等于当前时间
        //启用的促销列表： 条件： 已启用并且活动结束时间大于当前时间
        if(PRM_STATUS_3.equals(reqVO.getPrmStatus())){
            query.eqIfPresent(PrmActivity::getPrmStatus, PRM_STATUS_1);
            query.le(PrmActivity::getEndTime, DateUtils.getNowDate());
        }else if(PRM_STATUS_1.equals(reqVO.getPrmStatus())){
            query.eqIfPresent(PrmActivity::getPrmStatus, reqVO.getPrmStatus());
            query.gt(PrmActivity::getEndTime,DateUtils.getNowDate());
        }else{
            query.eqIfPresent(PrmActivity::getPrmStatus, reqVO.getPrmStatus());
        }

        //设置排序
        query.applyScope(reqVO.getParams());
        query.orderByDesc(PrmActivity::getActivityId);
        return selectPage(reqVO, query);
    }

    default List<PrmActivity> selectList(PrmActivityPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrmActivity>()
                .eqIfPresent(PrmActivity::getActivityId, reqVO.getActivityId())
                .eqIfPresent(PrmActivity::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrmActivity::getFuncScope, reqVO.getFuncScope())
                .eqIfPresent(PrmActivity::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(PrmActivity::getPrmNo, reqVO.getPrmNo())
                .eqIfPresent(PrmActivity::getPrmSheetNo, reqVO.getPrmSheetNo())
                .likeIfPresent(PrmActivity::getActivityName, reqVO.getActivityName())
                .eqIfPresent(PrmActivity::getEffectTime, reqVO.getEffectTime())
                .eqIfPresent(PrmActivity::getEffectMan, reqVO.getEffectMan())
                .eqIfPresent(PrmActivity::getStartTime, reqVO.getStartTime())
                .eqIfPresent(PrmActivity::getEndTime, reqVO.getEndTime())
                .eqIfPresent(PrmActivity::getSpuScope, reqVO.getSpuScope())
                .eqIfPresent(PrmActivity::getChanelScopeAllFlag, reqVO.getChanelScopeAllFlag())
                .eqIfPresent(PrmActivity::getBranchScopeAllFlag, reqVO.getBranchScopeAllFlag())
                .eqIfPresent(PrmActivity::getLadderFlag, reqVO.getLadderFlag())
                .eqIfPresent(PrmActivity::getTimesRule, reqVO.getTimesRule())
                .eqIfPresent(PrmActivity::getAmtOrQty, reqVO.getAmtOrQty())
                .eqIfPresent(PrmActivity::getPrmStatus, reqVO.getPrmStatus())
                .inIfPresent(PrmActivity::getActivityId, reqVO.getActivityIds())
                .inIfPresent(PrmActivity::getPrmStatus, reqVO.getPrmStatusList())
                .orderByDesc(PrmActivity::getActivityId));
    }

    /**
     * @param supplierId 入驻商ID
     * @return 入驻商下的活动列表
     */
    default List<PrmActivity> selectBySupplierId(Long supplierId, Object... prNos) {
        return selectList(new LambdaQueryWrapperX<PrmActivity>()
                .eqIfPresent(PrmActivity::getSupplierId, supplierId)
                .ge(PrmActivity::getPrmStatus, NumberPool.INT_ONE)
                .in(PrmActivity::getPrmNo, prNos)
                .ge(PrmActivity::getEndTime, DateUtil.date())
        );
    }

    default List<PrmActivity> selectByActivityIds(List<Long> activityIds, Object... prNos) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapperX<PrmActivity>()
                .in(PrmActivity::getActivityId, activityIds)
                .ge(PrmActivity::getPrmStatus, NumberPool.INT_ONE)
                .in(PrmActivity::getPrmNo, prNos)
                .ge(PrmActivity::getEndTime, DateUtil.date())
        );
    }

    /**
     * @Description: 获取满赠数据
     * @Author: liuxingyu
     * @Date: 2024/5/16 10:22
     */
    PrmFgRuleRespVO getPrmFgRule(Long activityId);

    /**
     * 获取满减数据
     *
     * @param activityId
     * @return
     */
    PrmFdRuleRespVO getPrmFdRule(Long activityId);

    /**
     * 根据ActivityId 和促销类型 获取促销信息
     *
     * @return
     */
    default PrmActivity getActivityByPrmNo(PrmActivityRespVO respVO) {
        return selectOne(new LambdaQueryWrapperX<PrmActivity>()
                .eqIfPresent(PrmActivity::getActivityId, respVO.getActivityId())
                .eqIfPresent(PrmActivity::getPrmNo, respVO.getPrmNo())
                .eqIfPresent(PrmActivity::getSysCode, respVO.getSysCode()));
    }

    /**
     * @Description: 根据条件获取促销活动ID集合
     * @Author: liuxingyu
     * @Date: 2024/5/18 17:27
     */
    List<Long> getIdListByScope(@Param("activity") PrmActivity activity);

    /**
     * 根据ActivityId 获取促销信息
     * @return
     */
    default PrmActivity getActivityByActivityId(Long activityId){
        return selectOne(new LambdaQueryWrapperX<PrmActivity>()
                .eqIfPresent(PrmActivity::getActivityId,activityId));
    }


    /**
     *  查询促销活动使用报表
     * @param pageVO
     * @return
     */
   List<PrmActivityReportRespDTO> getActivityReport(PrmActivityReportPageVO pageVO);

    List<Long> selectSeckillSpuIds(@Param("prmNo") String prmNo, @Param("funcScope") Integer funcScope);

    List<Long> selectSeckillSkuIds(@Param("prmNo") String prmNo, @Param("funcScope") Integer funcScope);

    /**
     * 获取 sku 绑定的进行中或者未开始的活动
     * @param skuId skuId
     * @return  活动role总数
     */
    Long selectCountBySkuId(@Param("skuId") Long skuId);

    List<CheckActivityItemDTO> selectChcekActivityBySkuIds(@Param("skuIds") Long [] skuIds,@Param("funcScope") Integer funcScope);

    /**
     * 获取组合促销DTO
     */
    List<CbRuleDTO> selectCbRuleList(@Param("activityId") Long activityId);

    List<Long> selectAreaCbSupplierIds(@Param("areaId") Long areaId);

    List<Long> getGlobalCbSupplierIds(@Param("sysCode") Long sysCode);

    List<Long> getExpiredCbActivityIds();

    Page<PrmActivityRespVO> selectActivityPage(@Param("page") Page<PrmActivityPageReqVO> page, @Param("reqVO") PrmActivityPageReqVO reqVO);

    default PageResult<PrmActivityRespVO> selectPageAndByAreaId(PrmActivityPageReqVO reqVO) {
        Page<PrmActivityPageReqVO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<PrmActivityRespVO> pageResult = this.selectActivityPage(page, reqVO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }
}
