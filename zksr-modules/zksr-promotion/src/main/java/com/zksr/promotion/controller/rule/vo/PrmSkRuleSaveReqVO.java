package com.zksr.promotion.controller.rule.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 秒杀规则对象 prm_sk_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ApiModel("秒杀规则 - prm_sk_rule分页 Request VO")
public class PrmSkRuleSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 秒杀规则id */
    @ApiModelProperty(value = "秒杀价")
    private Long skRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @ApiModelProperty(value = "活动id", required = true)
    private Long activityId;

    /** SPU id */
    @Excel(name = "SPU id")
    @ApiModelProperty(value = "SPU id", required = true)
    private Long spuId;

    /** SKU id */
    @Excel(name = "SKU id")
    @ApiModelProperty(value = "SKU id", required = true)
    private Long skuId;

    /** 单次限量 */
    @Excel(name = "单次限量")
    @ApiModelProperty(value = "单次限量")
    private Integer onceLimit;

    /** 秒杀库存 */
    @Excel(name = "秒杀库存")
    @ApiModelProperty(value = "秒杀库存", required = true)
    private Integer seckillStock;

    /** 秒杀库存（中单位） */
    @Excel(name = "秒杀库存中单位）")
    @ApiModelProperty(value = "秒杀库存中单位）", required = true)
    private Integer seckillMidStock;

    /** 秒杀库存（大单位） */
    @Excel(name = "秒杀库存（大单位）")
    @ApiModelProperty(value = "秒杀库存（大单位）", required = true)
    private Integer seckillLargeStock;

    /** 秒杀价 */
    @Excel(name = "小单位秒杀价")
    @ApiModelProperty(value = "小单位秒杀价", required = true)
    private BigDecimal seckillPrice;

    /** 秒杀价 */
    @Excel(name = "中单位秒杀价")
    @ApiModelProperty(value = "中单位秒杀价")
    private BigDecimal midSeckillPrice;

    /** 秒杀价 */
    @Excel(name = "大单位秒杀价")
    @ApiModelProperty(value = "大单位秒杀价")
    private BigDecimal largeSeckillPrice;

    /** 状态：0-停用，1-启用 */
    @Excel(name = "状态：0-停用，1-启用")
    @ApiModelProperty(value = "状态：0-停用，1-启用")
    private Integer skStatus;

    /** 排序 */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    /** 中单位限购数量 */
    @Excel(name = "中单位限购数量")
    @ApiModelProperty(value = "中单位限购数量")
    private Integer midLimit;

    /** 大单位限购数量 */
    @Excel(name = "大单位限购数量")
    @ApiModelProperty(value = "大单位限购数量")
    private Integer largeLimit;
}
