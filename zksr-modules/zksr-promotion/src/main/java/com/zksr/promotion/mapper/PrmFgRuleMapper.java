package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.controller.rule.vo.PrmFgRulePageReqVO;
import com.zksr.promotion.domain.PrmFgRule;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 满赠规则Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Mapper
public interface PrmFgRuleMapper extends BaseMapperX<PrmFgRule> {
    default PageResult<PrmFgRule> selectPage(PrmFgRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmFgRule>()
                    .eqIfPresent(PrmFgRule::getFgRuleId, reqVO.getFgRuleId())
                    .eqIfPresent(PrmFgRule::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmFgRule::getActivityId, reqVO.getActivityId())
                    .eqIfPresent(PrmFgRule::getFullAmt, reqVO.getFullAmt())
                    .eqIfPresent(PrmFgRule::getGiftType, reqVO.getGiftType())
                    .eqIfPresent(PrmFgRule::getSkuId, reqVO.getSkuId())
                    .eqIfPresent(PrmFgRule::getCouponTemplateId, reqVO.getCouponTemplateId())
                    .eqIfPresent(PrmFgRule::getOnceGiftQty, reqVO.getOnceGiftQty())
                    .eqIfPresent(PrmFgRule::getTotalGiftQty, reqVO.getTotalGiftQty())
                    .eqIfPresent(PrmFgRule::getStatus, reqVO.getStatus())
                .orderByDesc(PrmFgRule::getFgRuleId));
    }

    default Integer deleteByActivityId(Long activityId){
        return delete(new LambdaUpdateWrapper<PrmFgRule>()
                .eq(PrmFgRule::getActivityId,activityId));
    }

    default List<PrmFgRule> selectByActivityId(Long activityId) {
        return selectList(new LambdaQueryWrapperX<PrmFgRule>()
                .eqIfPresent(PrmFgRule::getActivityId, activityId)
                .eqIfPresent(PrmFgRule::getStatus, NumberPool.INT_ONE)
                .orderByDesc(PrmFgRule::getFgRuleId));
    }
}
