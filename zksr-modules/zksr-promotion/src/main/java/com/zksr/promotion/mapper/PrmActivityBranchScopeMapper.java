package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.controller.scope.vo.PrmActivityBranchScopePageReqVO;
import com.zksr.promotion.domain.PrmActivityBranchScope;
import com.zksr.promotion.controller.scope.vo.PrmActivityBranchScopePageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 促销活动门店适用范围Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Mapper
public interface PrmActivityBranchScopeMapper extends BaseMapperX<PrmActivityBranchScope> {
    default PageResult<PrmActivityBranchScope> selectPage(PrmActivityBranchScopePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmActivityBranchScope>()
                .eqIfPresent(PrmActivityBranchScope::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrmActivityBranchScope::getActivityId, reqVO.getActivityId())
                .eqIfPresent(PrmActivityBranchScope::getBranchId, reqVO.getBranchId())
                .orderByDesc(PrmActivityBranchScope::getSysCode));
    }

    default List<PrmActivityBranchScope> getBranchIdsByActivityId(Long activityId) {
        List<PrmActivityBranchScope> list = selectList(
                new LambdaQueryWrapperX<PrmActivityBranchScope>()
                        .select(PrmActivityBranchScope::getBranchId, PrmActivityBranchScope::getWhiteOrBlack)
                        .eq(PrmActivityBranchScope::getActivityId, activityId)
        );


        return list;
    }

    void deleteBranchByActivityId(@Param("activityId") Long activityId);

    /**
     * @Description: 通过促销活动ID删除绑定关系
     * @Author: liuxingyu
     * @Date: 2024/5/16 9:42
     */
    default Integer deleteByActivityId(Long activityId) {
        return delete(new LambdaUpdateWrapper<PrmActivityBranchScope>()
                .eq(PrmActivityBranchScope::getActivityId, activityId));
    }

    default List<PrmActivityBranchScope> selectByActivityId(Long activityId) {
        return selectList(new LambdaQueryWrapper<PrmActivityBranchScope>().eq(PrmActivityBranchScope::getActivityId, activityId));
    }
}
