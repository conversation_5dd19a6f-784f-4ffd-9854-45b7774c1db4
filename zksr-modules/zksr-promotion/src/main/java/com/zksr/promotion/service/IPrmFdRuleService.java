package com.zksr.promotion.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.controller.activity.vo.PrmActivityPageReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.rule.dto.PrmFdRuleDTO;
import com.zksr.promotion.controller.rule.vo.PrmFdRuleRespVO;
import com.zksr.promotion.domain.PrmActivity;

import javax.validation.Valid;

/**
 * 满减活动规则Service接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
public interface IPrmFdRuleService {

    /**
     * 新增满减活动规则
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmFdRule(@Valid PrmFdRuleDTO createReqVO);

    /**
     * 修改满减活动规则
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmFdRule(@Valid PrmFdRuleDTO updateReqVO);

    /**
     * 删除满减活动规则
     *
     * @param fdRuleId 满减活动规则id
     */
    public void deletePrmFdRule(Long fdRuleId);

    /**
     * 批量删除满减活动规则
     *
     * @param fdRuleIds 需要删除的满减活动规则主键集合
     * @return 结果
     */
    public void deletePrmFdRuleByFdRuleIds(Long[] fdRuleIds);

    /**
     * 获得满减活动规则
     *
     * @param activityId 满减活动规则id
     * @return 满减活动规则
     */
    public PrmFdRuleRespVO getPrmFdRule(Long activityId);

    /**
     * 获得满减活动规则分页
     *
     * @param pageReqVO 分页查询
     * @return 满减活动规则分页
     */
    PageResult<PrmActivityRespVO> getPrmFdRulePage(PrmActivityPageReqVO pageReqVO);

    /**
     * 启用活动
     * @param isEnable
     */
    Boolean isEnableActivity(PrmFdRuleDTO isEnable);
}
