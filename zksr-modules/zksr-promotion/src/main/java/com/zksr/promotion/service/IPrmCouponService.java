package com.zksr.promotion.service;

import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.api.coupon.dto.*;
import com.zksr.promotion.api.coupon.vo.CouponPageReqVO;
import com.zksr.promotion.api.coupon.vo.NormalCouponReceiveSingleAsyncReqVo;
import com.zksr.promotion.controller.coupon.vo.PrmCouponRespVO;
import com.zksr.promotion.domain.PrmCoupon;
import com.zksr.promotion.controller.coupon.vo.PrmCouponPageReqVO;

import java.util.ArrayList;
import java.util.List;

/**
 * 优惠券记录Service接口
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
public interface IPrmCouponService {

    /**
     * 获得优惠券记录
     *
     * @param couponId 优惠券记录id
     * @return 优惠券记录
     */
    public PrmCoupon getPrmCoupon(Long couponId);

    CouponDTO getPrmCouponDto(Long couponId);

    /**
     * 获得优惠券记录分页
     *
     * @param pageReqVO 分页查询
     * @return 优惠券记录分页
     */
    PageResult<PrmCouponRespVO> getPrmCouponPage(PrmCouponPageReqVO pageReqVO);

    /**
     * 用户领取优惠券
     * @param receiveData   领取请求
     */
    void saveNormalCouponReceive(CouponReceiveDTO receiveData);

    /**
     * 领取单个优惠券
     * @param branchId  门店ID
     * @param memberId  用户ID
     * @param couponTemplateId  优惠券ID
     * @return
     */
    CouponReceiveResultDTO saveNormalCouponReceiveSingle(Long branchId, Long memberId, Long couponTemplateId, boolean checkStock, Long couponBatchId);


    /**
     * 获取门店优惠券分页列表
     * @param receiveReq
     * @return
     */
    PageResult<CouponDTO> getBranchCouponListPage(CouponPageReqVO receiveReq);

    /**
     * 获取门店状态优惠券数据
     * @param branchId
     * @param couponState
     * @return
     */
    List<CouponDTO> getCouponListByBranchIdAndState(Long branchId, Integer couponState);


    /**
     * 获取门店状态优惠券数据
     *
     * @param branchId
     * @return
     */
    List<CouponDTO> getCouponListByBranchId(Long branchId);

    /**
     * 获取集合
     * @param branchId      门店ID
     * @param couponIdList  优惠券ID集合
     * @return
     */
    List<CouponDTO> getCouponBatch(Long branchId, List<Long> couponIdList);

    /**
     * 使用优惠券
     * @param couponList
     * @return
     */
    void applyCoupon(List<CouponApplyDTO> couponList);

    /**
     * 返回优惠券
     * @param couponReturn
     */
    void returnCoupon(CouponReturnDTO couponReturn);

    /**
     * 停用优惠券
     * @param couponIdList
     */
    void disableBatch(List<Long> couponIdList);
    /**
     * 根据模板ID获取领取优惠券的门店ID
     * @param couponTemplateId
     * @return
     */
    List<Long> getCouponReceivedBranchs(Long couponTemplateId);

     /**
     * 根据门店ID和批量发券ID获得优惠券记录
     *
     * @param branchId 门店ID couponBatchId，批量发券ID
     * @return 优惠券记录
     */
     List<CouponDTO> getCouponListByBranchIdAndCouponBatchId( Long branchId, Long couponBatchId);
}
