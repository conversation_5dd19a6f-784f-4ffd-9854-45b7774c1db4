package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.domain.PrmActivitySupplierScope;
import com.zksr.promotion.controller.scope.vo.PrmActivitySupplierScopePageReqVO;
import com.zksr.promotion.controller.scope.vo.PrmActivitySupplierScopeSaveReqVO;

/**
 * 促销活动入驻商适用范围Service接口
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface IPrmActivitySupplierScopeService {

    /**
     * 新增促销活动入驻商适用范围
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmActivitySupplierScope(@Valid PrmActivitySupplierScopeSaveReqVO createReqVO);

    /**
     * 修改促销活动入驻商适用范围
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmActivitySupplierScope(@Valid PrmActivitySupplierScopeSaveReqVO updateReqVO);

    /**
     * 删除促销活动入驻商适用范围
     *
     * @param sysCode 平台商id
     */
    public void deletePrmActivitySupplierScope(Long sysCode);

    /**
     * 批量删除促销活动入驻商适用范围
     *
     * @param sysCodes 需要删除的促销活动入驻商适用范围主键集合
     * @return 结果
     */
    public void deletePrmActivitySupplierScopeBySysCodes(Long[] sysCodes);

    /**
     * 获得促销活动入驻商适用范围
     *
     * @param sysCode 平台商id
     * @return 促销活动入驻商适用范围
     */
    public PrmActivitySupplierScope getPrmActivitySupplierScope(Long sysCode);

    /**
     * 获得促销活动入驻商适用范围分页
     *
     * @param pageReqVO 分页查询
     * @return 促销活动入驻商适用范围分页
     */
    PageResult<PrmActivitySupplierScope> getPrmActivitySupplierScopePage(PrmActivitySupplierScopePageReqVO pageReqVO);

}
