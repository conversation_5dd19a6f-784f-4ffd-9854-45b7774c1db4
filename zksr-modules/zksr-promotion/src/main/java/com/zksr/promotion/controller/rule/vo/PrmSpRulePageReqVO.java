package com.zksr.promotion.controller.rule.vo;

import java.math.BigDecimal;
import java.util.List;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 特价活动规则对象 prm_sp_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@ApiModel("特价活动规则 - prm_sp_rule分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmSpRulePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 特价活动规则id */
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Long spRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 特价活动id */
    @Excel(name = "特价活动id")
    @ApiModelProperty(value = "特价活动id", required = true)
    private Long activityId;

    /** SPU id */
    @Excel(name = "SPU id")
    @ApiModelProperty(value = "SPU id", required = true)
    private Long spuId;

    /** sku_id */
    @Excel(name = "sku_id")
    @ApiModelProperty(value = "sku_id", required = true)
    private Long skuId;

    /** 促销价 */
    @Excel(name = "促销价")
    @ApiModelProperty(value = "促销价", required = true)
    private BigDecimal spPrice;

    /** 单次限购数量 */
    @Excel(name = "单次限购数量")
    @ApiModelProperty(value = "单次限购数量", required = true)
    private Integer onceBuyLimit;

    /** 总限量 */
    @Excel(name = "总限量")
    @ApiModelProperty(value = "总限量", required = true)
    private Integer totalLimitQty;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用", required = true)
    private Integer status;

    /** skuId集合 */
    @Excel(name = "skuId集合")
    @ApiModelProperty(value = "skuId集合", required = true)
    private List<Long> skuIds;


}
