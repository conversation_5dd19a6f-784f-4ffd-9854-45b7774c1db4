package com.zksr.promotion.controller.quota.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 查看历史发券额度 prm_coupon_colonel_quota
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
@ApiModel("查看历史发券额度 - prm_coupon_colonel_quota Response VO")
public class QuotaHistoryResponse {
    private static final long serialVersionUID = 1L;

    /** 时间 */
    @Excel(name = "时间")
    @ApiModelProperty(value = "时间")
    private Integer monthId;

    /** 发券额度 */
    @Excel(name = "发券额度")
    @ApiModelProperty(value = "发券额度")
    private BigDecimal quota;

    /** 已发额度 */
    @Excel(name = "已发额度")
    @ApiModelProperty(value = "已发额度")
    private BigDecimal remainingQuota;

}
