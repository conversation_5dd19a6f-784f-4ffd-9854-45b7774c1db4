package com.zksr.promotion.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.controller.rule.vo.PrmFgActivitySaveReqVO;
import com.zksr.promotion.controller.rule.vo.PrmFgRulePageReqVO;
import com.zksr.promotion.controller.rule.vo.PrmFgRuleRespVO;
import com.zksr.promotion.domain.PrmFgRule;

import javax.validation.Valid;

/**
 * 满赠规则Service接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
public interface IPrmFgRuleService {

    /**
     * 新增满赠规则
     *
     * @param saveReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmFgRule(@Valid PrmFgActivitySaveReqVO saveReqVO);

    /**
     * 修改满赠规则
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmFgRule(@Valid PrmFgActivitySaveReqVO updateReqVO);

    /**
     * 删除满赠规则
     *
     * @param fgRuleId 满赠规则
     */
    public void deletePrmFgRule(Long fgRuleId);

    /**
     * 批量删除满赠规则
     *
     * @param fgRuleIds 需要删除的满赠规则主键集合
     * @return 结果
     */
    public void deletePrmFgRuleByFgRuleIds(Long[] fgRuleIds);

    /**
     * 获得满赠规则
     *
     * @param activityId 满赠规则
     * @return 满赠规则
     */
    public PrmFgRuleRespVO getPrmFgRule(Long activityId);

    /**
     * 获得满赠规则分页
     *
     * @param pageReqVO 分页查询
     * @return 满赠规则分页
     */
    PageResult<PrmFgRule> getPrmFgRulePage(PrmFgRulePageReqVO pageReqVO);

    /**
    * @Description: 变更满赠状态
    * @Author: liuxingyu
    * @Date: 2024/5/21 15:36
    */
    Long changeStatus(Long activityId, Integer prmStatus);
}
