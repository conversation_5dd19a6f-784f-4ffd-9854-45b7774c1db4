package com.zksr.promotion.controller.scope.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 优惠券模板入驻商
 */
@ApiModel("优惠券模板入驻商 - prm_coupon_template_supplier分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmCouponTemplateSupplierPageReqVO extends PageParam {

    private static final long serialVersionUID = -7281962883739271392L;

    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /**
     * 优惠券模板id
     */
    @Excel(name = "优惠券模板id")
    @ApiModelProperty(value = "优惠券模板id", required = true)
    private Long couponTemplateId;

    /**
     * 入驻商id
     */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

}
