package com.zksr.promotion.controller.rule.vo;

import java.math.BigDecimal;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 优惠券模板-下单返券规则（待讨论）对象 prm_coupon_template_return_rule
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@ApiModel("优惠券模板-下单返券规则（待讨论） - prm_coupon_template_return_rule分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmCouponTemplateReturnRulePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 优惠券模板重复规则 */
    @ApiModelProperty(value = "条件之适用ids")
    private Long couponTemplateReturnRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 优惠券模板id */
    @Excel(name = "优惠券模板id")
    @ApiModelProperty(value = "优惠券模板id")
    private Long couponTemplateId;

    /** 下单返优惠券条件（数据字典）;0-新会员下单  1-全场满返  2-购买指定入驻商商品满返  3-购买指定管理分类商品满返  4-购买指定品牌商品满返  5-购买指定商品满返 */
    @Excel(name = "下单返优惠券条件", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "下单返优惠券条件")
    private Integer conditionType;

    /** 满多少钱返券 */
    @Excel(name = "满多少钱返券")
    @ApiModelProperty(value = "满多少钱返券")
    private BigDecimal conditionAmt;

    /** 条件之适用ids */
    @Excel(name = "条件之适用ids")
    @ApiModelProperty(value = "条件之适用ids")
    private String conditionApplyIds;


}
