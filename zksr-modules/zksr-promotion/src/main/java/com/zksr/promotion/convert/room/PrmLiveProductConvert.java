package com.zksr.promotion.convert.room;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmLiveProduct;
import com.zksr.promotion.controller.product.vo.PrmLiveProductRespVO;
import com.zksr.promotion.controller.product.vo.PrmLiveProductSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 直播商品 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-03-28
*/
@Mapper
public interface PrmLiveProductConvert {

    PrmLiveProductConvert INSTANCE = Mappers.getMapper(PrmLiveProductConvert.class);

    PrmLiveProductRespVO convert(PrmLiveProduct prmLiveProduct);
    List<PrmLiveProduct> convert2List(List<PrmLiveProductSaveReqVO> prmLiveProduct);

    PrmLiveProduct convert(PrmLiveProductSaveReqVO prmLiveProductSaveReq);

    PageResult<PrmLiveProductRespVO> convertPage(PageResult<PrmLiveProduct> prmLiveProductPage);
}