package com.zksr.promotion.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.api.coupon.dto.CouponReceiveDTO;
import com.zksr.promotion.controller.couponBatch.vo.CouponBatchBranchRespVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchPageReqVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchRespVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchSaveReqVO;
import com.zksr.promotion.convert.couponBatch.PrmCouponBatchConvert;
import com.zksr.promotion.domain.PrmCouponBatch;
import com.zksr.promotion.domain.PrmCouponBatchDtl;
import com.zksr.promotion.domain.PrmCouponTemplate;
import com.zksr.promotion.mapper.PrmCouponBatchDtlMapper;
import com.zksr.promotion.mapper.PrmCouponBatchMapper;
import com.zksr.promotion.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.BATCH_COUPON_DELETE_ERROR;
import static com.zksr.promotion.enums.ErrorCodeConstants.BATCH_COUPON_SAVE_ERROR;

/**
 * 优惠券批量发送Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class PrmCouponBatchServiceImpl implements IPrmCouponBatchService {

    @Autowired
    private PrmCouponBatchMapper prmCouponBatchMapper;

    @Autowired
    private PrmCouponBatchDtlMapper prmCouponBatchDtlMapper;

    @Autowired
    private IPrmCouponTemplateService prmCouponTemplateService;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Autowired
    private IPrmCouponScopeApplyService couponScopeApplyService;

    @Autowired
    private IPrmCouponService prmCouponService;

    @Resource
    private BranchApi branchApi;


    /**
     * 新增优惠券批量发送
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    /**
     * 新增优惠券批量发送
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    public Long insertPrmCouponBatch(PrmCouponBatchSaveReqVO createReqVO) {
        try {
            // 插入主表记录
            PrmCouponBatch prmCouponBatch = PrmCouponBatchConvert.INSTANCE.convert(createReqVO);
            //审核状态默认为未审核
            prmCouponBatch.setAuditStatus(0);
            //执行状态为未执行
            prmCouponBatch.setTaskExecuteStatus(0);
            //填充总计发券数量
            prmCouponBatch.setCouponTemplateQty(Long.parseLong(createReqVO.getCouponTemplateIds().size() + ""));
            prmCouponBatch.setBranchQty(Long.parseLong(createReqVO.getBranchIds().size() + ""));
            prmCouponBatch.setTotalQty(prmCouponBatch.getCouponTemplateQty() * prmCouponBatch.getBranchQty());
            //填充全国或者本地
            prmCouponBatch.setFuncScope(ToolUtil.isNotEmpty(SecurityUtils.getLoginUser().getSysCode()) ? 1L : 2L);
            prmCouponBatchMapper.insert(prmCouponBatch);

            // 创建明细记录
            List<PrmCouponBatchDtl> prmCouponBatchDtls = new ArrayList<>();

            // 处理优惠券明细
            createReqVO.getCouponTemplateIds().forEach(couponTemplateId -> {
                PrmCouponBatchDtl prmCouponBatchDtl = createPrmCouponBatchDtl(prmCouponBatch.getCouponBatchId(), couponTemplateId, 0);
                prmCouponBatchDtls.add(prmCouponBatchDtl);
            });

            // 处理门店明细
            createReqVO.getBranchIds().forEach(branchId -> {
                PrmCouponBatchDtl prmCouponBatchDtl = createPrmCouponBatchDtl(prmCouponBatch.getCouponBatchId(), branchId, 1);
                prmCouponBatchDtls.add(prmCouponBatchDtl);
            });

            // 插入新的明细记录
            if (!prmCouponBatchDtls.isEmpty()) {
                prmCouponBatchDtlMapper.insertBatch(prmCouponBatchDtls);
            }

            // 返回主表记录的 ID
            return prmCouponBatch.getCouponBatchId();
        } catch (Exception e) {
            throw exception(BATCH_COUPON_SAVE_ERROR);
        }
    }

    @Override
    @Transactional
    public void updatePrmCouponBatch(PrmCouponBatchSaveReqVO updateReqVO) {
        try {
            // 更新主表记录
            PrmCouponBatch prmCouponBatch = PrmCouponBatchConvert.INSTANCE.convert(updateReqVO);
            ;
            //填充总计发券数量
            prmCouponBatch.setCouponTemplateQty(Long.parseLong(updateReqVO.getCouponTemplateIds().size() + ""));
            prmCouponBatch.setBranchQty(Long.parseLong(updateReqVO.getBranchIds().size() + ""));
            prmCouponBatch.setTotalQty(prmCouponBatch.getCouponTemplateQty() * prmCouponBatch.getBranchQty());
            prmCouponBatchMapper.updateById(prmCouponBatch);

            if (ToolUtil.isNotEmpty(updateReqVO.getCouponTemplateIds()) || ToolUtil.isNotEmpty(updateReqVO.getBranchIds())) {
                // 删除旧的明细记录
                prmCouponBatchDtlMapper.deleteByBatchCouponId(updateReqVO.getCouponBatchId());

                // 创建新的明细记录
                List<PrmCouponBatchDtl> prmCouponBatchDtls = new ArrayList<>();

                // 处理优惠券明细
                if (ToolUtil.isNotEmpty(updateReqVO.getCouponTemplateIds())) {
                    updateReqVO.getCouponTemplateIds().forEach(couponTemplateId -> {
                        PrmCouponBatchDtl prmCouponBatchDtl = createPrmCouponBatchDtl(updateReqVO.getCouponBatchId(), couponTemplateId, 0);
                        prmCouponBatchDtls.add(prmCouponBatchDtl);
                    });
                }

                // 处理门店明细
                if (ToolUtil.isNotEmpty(updateReqVO.getBranchIds())) {
                    updateReqVO.getBranchIds().forEach(branchId -> {
                        PrmCouponBatchDtl prmCouponBatchDtl = createPrmCouponBatchDtl(updateReqVO.getCouponBatchId(), branchId, 1);
                        prmCouponBatchDtls.add(prmCouponBatchDtl);
                    });
                }

                // 插入新的明细记录
                if (!prmCouponBatchDtls.isEmpty()) {
                    prmCouponBatchDtlMapper.insertBatch(prmCouponBatchDtls);
                }
            }
        } catch (Exception e) {
            throw exception(BATCH_COUPON_SAVE_ERROR);
        }
    }


    private PrmCouponBatchDtl createPrmCouponBatchDtl(Long batchCouponId, String id, int scopeType) {
        PrmCouponBatchDtl prmCouponBatchDtl = new PrmCouponBatchDtl();
        prmCouponBatchDtl.setBatchCouponId(batchCouponId);
        if (scopeType == 0) {
            prmCouponBatchDtl.setCouponTemplateId(Long.parseLong(id));
        } else if (scopeType == 1) {
            prmCouponBatchDtl.setBranchId(Long.parseLong(id));
        }
        prmCouponBatchDtl.setScopeType(scopeType);
        return prmCouponBatchDtl;
    }


    /**
     * 删除优惠券批量发送
     *
     * @param couponBatchId 优惠券批量发送id
     */
    @Override
    public void deletePrmCouponBatch(Long couponBatchId) {
        // 删除
        prmCouponBatchMapper.deleteById(couponBatchId);
    }

    /**
     * 批量删除优惠券批量发送
     *
     * @param couponBatchIds 需要删除的优惠券批量发送主键
     * @return 结果
     */
    @Override
    public void deletePrmCouponBatchByCouponBatchIds(Long[] couponBatchIds) {
        for (Long couponBatchId : couponBatchIds) {
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            PrmCouponBatch prmCouponBatch = prmCouponBatchMapper.selectById(couponBatchId);
            if (ToolUtil.isNotEmpty(prmCouponBatch)) {
                //判断审核状态  如果是已经审核则不允许删除
                if (prmCouponBatch.getAuditStatus() == 1) {
                    throw exception(BATCH_COUPON_DELETE_ERROR);
                }
                prmCouponBatch.setDelFlag("2");
                prmCouponBatchMapper.updateById(prmCouponBatch);
            }
        }
    }

    /**
     * 获得优惠券批量发送
     *
     * @param couponBatchId 优惠券批量发送id
     * @return 优惠券批量发送
     */
    @Override
    public PrmCouponBatchRespVO getPrmCouponBatch(Long couponBatchId) {
        // 查询主表信息
        PrmCouponBatch prmCouponBatch = prmCouponBatchMapper.selectById(couponBatchId);
        PrmCouponBatchRespVO prmCouponBatchRespVO = PrmCouponBatchConvert.INSTANCE.convert(prmCouponBatch);

        // 查询明细信息
        List<PrmCouponBatchDtl> couponTemplateDetails = prmCouponBatchDtlMapper.selectByBatchCouponIdAndScopeType(couponBatchId, null);

        // 组装优惠券和门店相关信息
        List<PrmCouponBatchRespVO.CouponTemplateDetail> couponTemplateDetail = new ArrayList<>();
        List<PrmCouponBatchRespVO.BranchDetail> branchDetails = new ArrayList<>();

        if (ToolUtil.isNotEmpty(couponTemplateDetails)) {
            for (PrmCouponBatchDtl detail : couponTemplateDetails) {
                if (detail.getScopeType() == 0) {
                    // 优惠券相关明细
                    PrmCouponBatchRespVO.CouponTemplateDetail couponTemplateDetailItem = convertToCouponTemplateDetail(detail);
                    couponTemplateDetail.add(couponTemplateDetailItem);
                } else {
                    // 门店相关
                    PrmCouponBatchRespVO.BranchDetail branchDetail = convertToBranchDetail(detail);
                    branchDetails.add(branchDetail);
                }
            }
        }

        prmCouponBatchRespVO.setBranchDetails(branchDetails);
        prmCouponBatchRespVO.setCouponTemplateDetails(couponTemplateDetail);

        return prmCouponBatchRespVO;
    }

    private PrmCouponBatchRespVO.CouponTemplateDetail convertToCouponTemplateDetail(PrmCouponBatchDtl detail) {
        PrmCouponBatchRespVO.CouponTemplateDetail couponTemplateDetailItem = HutoolBeanUtils.toBean(
                prmCouponTemplateService.getPrmCouponTemplate(detail.getCouponTemplateId()),
                PrmCouponBatchRespVO.CouponTemplateDetail.class
        );
        couponTemplateDetailItem.setReceiveCount(couponTemplateDetailItem.getReceiveCount() == null ? 0 : couponTemplateDetailItem.getReceiveCount());
        couponTemplateDetailItem.setCouponLeftQty(couponTemplateDetailItem.getCouponQty() - (couponTemplateDetailItem.getReceiveCount() == null ? 0 : couponTemplateDetailItem.getReceiveCount()));
        return couponTemplateDetailItem;
    }

    private PrmCouponBatchRespVO.BranchDetail convertToBranchDetail(PrmCouponBatchDtl detail) {
        PrmCouponBatchRespVO.BranchDetail branchDetail = HutoolBeanUtils.toBean(
                promotionCacheService.getBranchDTO(detail.getBranchId()),
                PrmCouponBatchRespVO.BranchDetail.class
        );
        branchDetail.setColonelName(getColonelName(branchDetail));
        branchDetail.setAreaName(getAreaName(branchDetail));
        branchDetail.setChannelName(getChannelName(branchDetail));
        return branchDetail;
    }

    private String getColonelName(PrmCouponBatchRespVO.BranchDetail branchDetail) {
        return branchDetail.getColonelId() != null ? promotionCacheService.getColonel(branchDetail.getColonelId()) == null ? "" : promotionCacheService.getColonel(branchDetail.getColonelId()).getColonelName() : null;
    }

    private String getAreaName(PrmCouponBatchRespVO.BranchDetail branchDetail) {
        return branchDetail.getAreaId() != null ? promotionCacheService.getAreaDTO(branchDetail.getAreaId()).getAreaName() : null;
    }

    private String getChannelName(PrmCouponBatchRespVO.BranchDetail branchDetail) {
        return branchDetail.getChannelId() != null ? promotionCacheService.getChannelDTO(branchDetail.getChannelId()).getChannelName() : null;
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrmCouponBatch> getPrmCouponBatchPage(PrmCouponBatchPageReqVO pageReqVO) {
        // 平台商
        boolean sysCodeFlag = ToolUtil.isNotEmpty(SecurityUtils.getLoginUser().getSysCode());
        pageReqVO.setFuncScope(sysCodeFlag ? 1L : 2L);
        boolean dcFlag = ToolUtil.isNotEmpty(SecurityUtils.getLoginUser().getDcId());
        // 不是平台商，按照运营商权限隔离
        if (dcFlag) {
            pageReqVO.setCreateBy(SecurityUtils.getUsername());
        }
        PageResult<PrmCouponBatch> pageResult = prmCouponBatchMapper.selectPage(pageReqVO);
        pageResult.getList().forEach(x -> {
            //填充实际发券数量
            List<CouponDTO> prmCoupons = prmCouponService.getCouponListByBranchIdAndCouponBatchId(null, x.getCouponBatchId());
            x.setRealSendQty(Long.parseLong(prmCoupons.isEmpty() ? "0" : prmCoupons.size() + ""));
        });
        return pageResult;

    }

    @Override
    public PageResult<CouponBatchBranchRespVO> getPrmCouponBatchBranchList(PrmCouponBatchPageReqVO pageReqVO) {
        Page<PrmCouponBatchPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<CouponBatchBranchRespVO> pageResult = prmCouponBatchMapper.getPrmCouponBatchBranchList(page, pageReqVO);
        if (ToolUtil.isEmpty(pageResult)) {
            return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
        }
        List<CouponBatchBranchRespVO> updatedRecords = pageResult.getRecords().stream()
                .map(couponBatchBranchRespVO -> {
                    CouponBatchBranchRespVO updatedCouponBatchBranchRespVO = HutoolBeanUtils.toBean(
                            promotionCacheService.getBranchDTO(couponBatchBranchRespVO.getBranchId()),
                            CouponBatchBranchRespVO.class
                    );
                    //updatedCouponBatchBranchRespVO.setColonelName(getColonelName(couponBatchBranchRespVO.getColonelId()));
                    //updatedCouponBatchBranchRespVO.setAreaName(getAreaName(couponBatchBranchRespVO.getAreaId()));
                    //updatedCouponBatchBranchRespVO.setChannelName(getChannelName(couponBatchBranchRespVO.getChannelId()));
                    return updatedCouponBatchBranchRespVO;
                })
                .collect(Collectors.toList());

        pageResult.setRecords(updatedRecords);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }


    public static List<Long> parseCouponBatchIds(String couponBatchId) {
        if (couponBatchId == null || couponBatchId.trim().isEmpty()) {
            return new ArrayList<>();
        }

        return Arrays.stream(couponBatchId.split(","))
                .map(String::trim)
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void auditPrmCouponBatch(String couponBatchIds) {
        List<Long> couponBatchIdList = parseCouponBatchIds(couponBatchIds);
        for (Long couponBatchId : couponBatchIdList) {
            PrmCouponBatch prmCouponBatch = prmCouponBatchMapper.selectById(couponBatchId);
            if (ToolUtil.isNotEmpty(prmCouponBatch)) {

                // 判断生效类型 如果为立即生效 则直接执行优惠券发送
                if (prmCouponBatch.getValidType() == 1) {
                    prmCouponBatch.setValidTime(new Date());

                    // 获取优惠券模板信息
                    List<PrmCouponBatchDtl> prmCouponBatchDtls = prmCouponBatchDtlMapper.selectByBatchCouponIdAndScopeType(couponBatchId, 0);
                    if (ToolUtil.isNotEmpty(prmCouponBatchDtls)) {
                        for (PrmCouponBatchDtl prmCouponBatchDtl : prmCouponBatchDtls) {
                            PrmCouponTemplate couponTemplate = prmCouponTemplateService.getPrmCouponTemplate(prmCouponBatchDtl.getCouponTemplateId());
                            List<PrmCouponBatchDtl> prmCouponBatchBranchIds = prmCouponBatchDtlMapper.selectByBatchCouponIdAndScopeType(couponBatchId, 1);
                            // 获取批量发券详情门店ID集合
                            List<Long> branchIds = prmCouponBatchBranchIds.stream().map(PrmCouponBatchDtl::getBranchId).collect(Collectors.toList());

                            // 校验优惠券的状态是否正常
                            if (couponTemplate.getStatus() != 0) {
                                continue; // 跳过当前循环，进行下个模板的发券任务

                            }

                            // 校验优惠券有效期类型为固定日期时 发放时间是否在当前时间之中
                            if (couponTemplate.getExpirationType() == 0) {
                                if (DateUtil.compare(new Date(), couponTemplate.getExpirationDateStart()) < 0 || DateUtil.compare(new Date(), couponTemplate.getExpirationDateEnd()) > 0) {
                                    continue;  // 跳过当前循环，进行下个模板的发券任务
                                }
                            }
                            for (Long branchId : branchIds) {
                                //根据门店发放数量给门店发放指定数量优惠券
                                for (int i = 0; i < prmCouponBatch.getBranchSendQty(); i++) {
                                    CouponReceiveDTO couponReceiveDTO = new CouponReceiveDTO();
                                    Map<Long, Long> couponStatusMap = new HashMap<>();
                                    couponStatusMap.put(couponTemplate.getCouponTemplateId(), Long.parseLong(couponTemplate.getStatus() + ""));
                                    couponReceiveDTO.setCouponStatusMap(couponStatusMap);
                                    couponReceiveDTO.setCouponBatchId(couponBatchId);
                                    couponReceiveDTO.setCouponTemplateIds(Arrays.asList(couponTemplate.getCouponTemplateId()));
                                    couponReceiveDTO.setCouponStatusMap(couponStatusMap);
                                    couponReceiveDTO.setBranchId(branchId);
                                    //发送优惠券
                                    prmCouponService.saveNormalCouponReceive(couponReceiveDTO);
                                }
                            }
                        }
                        prmCouponBatch.setTaskExecuteStatus(1);
                    }
                }
            }
            prmCouponBatch.setAuditStatus(1);
            prmCouponBatchMapper.updateById(prmCouponBatch);
        }
    }

    @Override
    public List<PrmCouponBatch> getValidCouponBatchList() {
        return prmCouponBatchMapper.getValidCouponBatchList();
    }


/*    private void validatePrmCouponBatchExists(Long couponBatchId) {
        if (prmCouponBatchMapper.selectById(couponBatchId) == null) {
            throw exception(PRM_COUPON_BATCH_NOT_EXISTS);
        }
    }*/

    // TODO 待办：请将下面的错误码复制到 com.zksr.promotion.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 优惠券批量发送 TODO 补充编号 ==========
    // ErrorCode PRM_COUPON_BATCH_NOT_EXISTS = new ErrorCode(TODO 补充编号, "优惠券批量发送不存在");


}
