package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.domain.PrmLiveOrder;
import com.zksr.promotion.controller.order.vo.PrmLiveOrderPageReqVO;
import com.zksr.promotion.controller.order.vo.PrmLiveOrderSaveReqVO;

import java.util.List;

/**
 * 直播订单Service接口
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
public interface IPrmLiveOrderService {

    /**
     * 新增直播订单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public String insertPrmLiveOrder(@Valid List<PrmLiveOrderSaveReqVO> createReqVO);

    /**
     * 修改直播订单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmLiveOrder(@Valid PrmLiveOrderSaveReqVO updateReqVO);

    /**
     * 删除直播订单
     *
     * @param id ID主键
     */
    public void deletePrmLiveOrder(Long id);

    /**
     * 批量删除直播订单
     *
     * @param ids 需要删除的直播订单主键集合
     * @return 结果
     */
    public void deletePrmLiveOrderByIds(Long[] ids);

    /**
     * 获得直播订单
     *
     * @param id ID主键
     * @return 直播订单
     */
    public PrmLiveOrder getPrmLiveOrder(Long id);

    /**
     * 获得直播订单分页
     *
     * @param pageReqVO 分页查询
     * @return 直播订单分页
     */
    PageResult<PrmLiveOrder> getPrmLiveOrderPage(PrmLiveOrderPageReqVO pageReqVO);

}
