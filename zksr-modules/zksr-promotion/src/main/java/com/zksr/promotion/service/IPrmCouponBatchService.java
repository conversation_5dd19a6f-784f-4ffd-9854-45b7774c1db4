package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.controller.couponBatch.vo.CouponBatchBranchRespVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchRespVO;
import com.zksr.promotion.domain.PrmCouponBatch;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchPageReqVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchSaveReqVO;

import java.util.List;

/**
 * 优惠券批量发送Service接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface IPrmCouponBatchService {

    /**
     * 新增优惠券批量发送
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmCouponBatch(@Valid PrmCouponBatchSaveReqVO createReqVO);

    /**
     * 修改优惠券批量发送
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmCouponBatch(@Valid PrmCouponBatchSaveReqVO updateReqVO);

    /**
     * 删除优惠券批量发送
     *
     * @param couponBatchId 优惠券批量发送id
     */
    public void deletePrmCouponBatch(Long couponBatchId);

    /**
     * 批量删除优惠券批量发送
     *
     * @param couponBatchIds 需要删除的优惠券批量发送主键集合
     * @return 结果
     */
    public void deletePrmCouponBatchByCouponBatchIds(Long[] couponBatchIds);

    /**
     * 获得优惠券批量发送
     *
     * @param couponBatchId 优惠券批量发送id
     * @return 优惠券批量发送
     */
    public PrmCouponBatchRespVO getPrmCouponBatch(Long couponBatchId);

    /**
     * 获得优惠券批量发送分页
     *
     * @param pageReqVO 分页查询
     * @return 优惠券批量发送分页
     */
    PageResult<PrmCouponBatch> getPrmCouponBatchPage(PrmCouponBatchPageReqVO pageReqVO);


    /**
     * 获取优惠券批量发送门店信息列表分页
     *
     * @param pageReqVO 分页查询
     * @return 获取优惠券批量发送门店信息列表分页
     */
    PageResult<CouponBatchBranchRespVO> getPrmCouponBatchBranchList(PrmCouponBatchPageReqVO pageReqVO);


    /**
     * 审核优惠券批量发送
     * @param couponBatchId 批量发券ID
     * @return 结果
     */
     void auditPrmCouponBatch(String couponBatchId);

     /**
     * 获取已审核的生效类型为定时生效的批次发券集合
     */
     List<PrmCouponBatch> getValidCouponBatchList();


}
