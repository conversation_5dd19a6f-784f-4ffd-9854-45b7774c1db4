package com.zksr.promotion.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmCouponTemplateReturnRuleMapper;
import com.zksr.promotion.convert.rule.PrmCouponTemplateReturnRuleConvert;
import com.zksr.promotion.domain.PrmCouponTemplateReturnRule;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateReturnRulePageReqVO;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateReturnRuleSaveReqVO;
import com.zksr.promotion.service.IPrmCouponTemplateReturnRuleService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 优惠券模板-下单返券规则（待讨论）Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@Service
public class PrmCouponTemplateReturnRuleServiceImpl implements IPrmCouponTemplateReturnRuleService {
    @Autowired
    private PrmCouponTemplateReturnRuleMapper prmCouponTemplateReturnRuleMapper;

    /**
     * 新增优惠券模板-下单返券规则（待讨论）
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrmCouponTemplateReturnRule(PrmCouponTemplateReturnRuleSaveReqVO createReqVO) {
        // 插入
        PrmCouponTemplateReturnRule prmCouponTemplateReturnRule = PrmCouponTemplateReturnRuleConvert.INSTANCE.convert(createReqVO);
        prmCouponTemplateReturnRuleMapper.insert(prmCouponTemplateReturnRule);
        // 返回
        return prmCouponTemplateReturnRule.getCouponTemplateReturnRuleId();
    }

    /**
     * 修改优惠券模板-下单返券规则（待讨论）
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmCouponTemplateReturnRule(PrmCouponTemplateReturnRuleSaveReqVO updateReqVO) {
        prmCouponTemplateReturnRuleMapper.updateById(PrmCouponTemplateReturnRuleConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除优惠券模板-下单返券规则（待讨论）
     *
     * @param couponTemplateReturnRuleId 优惠券模板重复规则
     */
    @Override
    public void deletePrmCouponTemplateReturnRule(Long couponTemplateReturnRuleId) {
        // 删除
        prmCouponTemplateReturnRuleMapper.deleteById(couponTemplateReturnRuleId);
    }

    /**
     * 批量删除优惠券模板-下单返券规则（待讨论）
     *
     * @param couponTemplateReturnRuleIds 需要删除的优惠券模板-下单返券规则（待讨论）主键
     * @return 结果
     */
    @Override
    public void deletePrmCouponTemplateReturnRuleByCouponTemplateReturnRuleIds(Long[] couponTemplateReturnRuleIds) {
        for(Long couponTemplateReturnRuleId : couponTemplateReturnRuleIds){
            this.deletePrmCouponTemplateReturnRule(couponTemplateReturnRuleId);
        }
    }

    /**
     * 获得优惠券模板-下单返券规则（待讨论）
     *
     * @param couponTemplateReturnRuleId 优惠券模板重复规则
     * @return 优惠券模板-下单返券规则（待讨论）
     */
    @Override
    public PrmCouponTemplateReturnRule getPrmCouponTemplateReturnRule(Long couponTemplateReturnRuleId) {
        return prmCouponTemplateReturnRuleMapper.selectById(couponTemplateReturnRuleId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmCouponTemplateReturnRule> getPrmCouponTemplateReturnRulePage(PrmCouponTemplateReturnRulePageReqVO pageReqVO) {
        return prmCouponTemplateReturnRuleMapper.selectPage(pageReqVO);
    }

}
