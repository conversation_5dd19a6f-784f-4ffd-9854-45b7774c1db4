package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.domain.PrmLiveProduct;
import com.zksr.promotion.controller.product.vo.PrmLiveProductPageReqVO;
import com.zksr.promotion.controller.product.vo.PrmLiveProductSaveReqVO;

import java.util.List;

/**
 * 直播商品Service接口
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
public interface IPrmLiveProductService {

    /**
     * 新增直播商品
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public String insertPrmLiveProduct(@Valid List<PrmLiveProductSaveReqVO> createReqVO);

    /**
     * 修改直播商品
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmLiveProduct(@Valid PrmLiveProductSaveReqVO updateReqVO);

    /**
     * 删除直播商品
     *
     * @param id ID主键
     */
    public void deletePrmLiveProduct(Long id);

    /**
     * 批量删除直播商品
     *
     * @param ids 需要删除的直播商品主键集合
     * @return 结果
     */
    public void deletePrmLiveProductByIds(Long[] ids);

    /**
     * 获得直播商品
     *
     * @param id ID主键
     * @return 直播商品
     */
    public PrmLiveProduct getPrmLiveProduct(Long id);

    /**
     * 获得直播商品分页
     *
     * @param pageReqVO 分页查询
     * @return 直播商品分页
     */
    PageResult<PrmLiveProduct> getPrmLiveProductPage(PrmLiveProductPageReqVO pageReqVO);

}
