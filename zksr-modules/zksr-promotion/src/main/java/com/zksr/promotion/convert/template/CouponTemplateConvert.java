package com.zksr.promotion.convert.template;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.coupon.dto.CouponNormalCacheDTO;
import com.zksr.promotion.api.coupon.dto.CouponReceiveScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.controller.template.dto.SpuScopeOrReceiveScopeVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateRespVO;
import com.zksr.promotion.domain.PrmCouponTemplate;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 优惠券模版转换器
 * @date 2024/5/7 17:37
 */
@Mapper
public interface CouponTemplateConvert {

    CouponTemplateConvert INSTANCE = Mappers.getMapper(CouponTemplateConvert.class);

    PrmCouponTemplateRespVO convertResp(PrmCouponTemplate prmCouponTemplate);

    CouponTemplateDTO convertDTO(PrmCouponTemplate prmCouponTemplate);

    CouponNormalCacheDTO convertNormal(PrmCouponTemplate template);

    PageResult<PrmCouponTemplateRespVO> convertPage(PageResult<PrmCouponTemplate> pageResult);
}
