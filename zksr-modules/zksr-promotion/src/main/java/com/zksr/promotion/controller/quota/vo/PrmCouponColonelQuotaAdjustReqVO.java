package com.zksr.promotion.controller.quota.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 业务员调整额度
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@ApiModel("业务员调整额度")
@Data
@ToString(callSuper = true)
public class PrmCouponColonelQuotaAdjustReqVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 平台商ID */
    private Long sysCode;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    private String colonelId;

    /** 调整发券额度 */
    @Excel(name = "调整发券额度")
    @ApiModelProperty(value = "调整发券额度")
    private BigDecimal quota;
}
