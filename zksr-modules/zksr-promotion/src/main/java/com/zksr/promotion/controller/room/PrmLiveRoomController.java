package com.zksr.promotion.controller.room;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.promotion.convert.room.PrmLiveRoomConvert;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.domain.PrmLiveRoom;
import com.zksr.promotion.service.IPrmLiveRoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.promotion.controller.room.vo.PrmLiveRoomPageReqVO;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomSaveReqVO;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomRespVO;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 直播间Controller
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Api(tags = "管理后台 - 直播间接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/room")
public class PrmLiveRoomController {
    @Autowired
    private IPrmLiveRoomService prmLiveRoomService;

    /**
     * 新增直播间
     */
    @ApiOperation(value = "新增直播间", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "直播间", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmLiveRoomSaveReqVO createReqVO) {
        return success(prmLiveRoomService.createLiveRoom(createReqVO));
    }

    /**
     * 修改直播间
     */
    @ApiOperation(value = "修改直播间", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "直播间", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmLiveRoomSaveReqVO updateReqVO) {
            prmLiveRoomService.updatePrmLiveRoom(updateReqVO);
        return success(true);
    }

    /**
     * 删除直播间
     */
    @ApiOperation(value = "删除直播间", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "直播间", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public CommonResult<Boolean> remove(@PathVariable Long[] ids) {
        prmLiveRoomService.deletePrmLiveRoomByIds(ids);
        return success(true);
    }

    /**
     * 获取直播间详细信息
     */
    @ApiOperation(value = "获得直播间详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{id}")
    public CommonResult<PrmLiveRoomRespVO> getInfo(@PathVariable("id") Long id) {
        PrmLiveRoom prmLiveRoom = prmLiveRoomService.getPrmLiveRoom(id);

        return success(PrmLiveRoomConvert.INSTANCE.convert(prmLiveRoom));
    }

    /**
     * 分页查询直播间
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得直播间分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmLiveRoomRespVO>> getPage(@Valid PrmLiveRoomPageReqVO pageReqVO) {
        PageResult<PrmLiveRoom> pageResult = prmLiveRoomService.getPrmLiveRoomPage(pageReqVO);
        return success(PrmLiveRoomConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:room:add";
        /** 编辑 */
        public static final String EDIT = "promotion:room:edit";
        /** 删除 */
        public static final String DELETE = "promotion:room:remove";
        /** 列表 */
        public static final String LIST = "promotion:room:list";
        /** 查询 */
        public static final String GET = "promotion:room:query";
        /** 停用 */
        public static final String DISABLE = "promotion:room:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:room:enable";
    }
}
