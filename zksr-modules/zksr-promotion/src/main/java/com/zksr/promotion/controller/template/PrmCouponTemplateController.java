package com.zksr.promotion.controller.template;

import com.google.common.collect.Lists;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.exception.enums.GlobalErrorCodeConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.controller.rule.dto.UploadFileForm;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplatePageReqVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateRespVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateSaveReqVO;
import com.zksr.promotion.convert.template.CouponTemplateConvert;
import com.zksr.promotion.domain.PrmCouponTemplate;
import com.zksr.promotion.domain.excel.PrmBranchImportExcel;
import com.zksr.promotion.domain.excel.PrmItemImportExcel;
import com.zksr.promotion.mq.PromotionMqProducer;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmCouponTemplateRepeatRuleService;
import com.zksr.promotion.service.IPrmCouponTemplateService;
import com.zksr.promotion.service.IPromotionCacheService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 优惠券模板Controller
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Api(tags = "管理后台 - 优惠券模板接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/template")
public class PrmCouponTemplateController {

    @Autowired
    private IPrmCouponTemplateService prmCouponTemplateService;

    @Autowired
    private IPrmCouponTemplateRepeatRuleService repeatRuleService;

    @Autowired
    private PromotionMqProducer promotionMqProducer;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Autowired
    private IPrmActivityCommonService prmActivityCommonService;

    /**
     * 新增优惠券模板
     */
    @ApiOperation(value = "新增优惠券模板", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "优惠券模板", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmCouponTemplateSaveReqVO createReqVO) {
        Long couponTemplateId = prmCouponTemplateService.insertPrmCouponTemplate(createReqVO);
        return success(couponTemplateId);
    }

    /**
     * 修改优惠券模板
     */
    @ApiOperation(value = "修改优惠券模板", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "优惠券模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmCouponTemplateSaveReqVO updateReqVO) {
        prmCouponTemplateService.updatePrmCouponTemplate(updateReqVO);
        //promotionMqProducer.sendCouponRefreshEvent(prmCouponTemplateService.getPrmCouponTemplate(updateReqVO.getCouponTemplateId()));
        return success(true);
    }

    /**
     * 删除优惠券模板
     */
    @ApiOperation(value = "删除优惠券模板", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "优惠券模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{couponTemplateIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] couponTemplateIds) {
        prmCouponTemplateService.deletePrmCouponTemplateByCouponTemplateIds(couponTemplateIds);
        return success(true);
    }

    /**
     * 获取优惠券模板详细信息
     */
    @ApiOperation(value = "获得优惠券模板详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{couponTemplateId}")
    public CommonResult<PrmCouponTemplateRespVO> getInfo(@PathVariable("couponTemplateId") Long couponTemplateId) {
        PrmCouponTemplate prmCouponTemplate = prmCouponTemplateService.getPrmCouponTemplate(couponTemplateId);
        PrmCouponTemplateRespVO templateRespVO = CouponTemplateConvert.INSTANCE.convertResp(prmCouponTemplate);
        templateRespVO.setRepeatRule(repeatRuleService.getPrmCouponTemplateRepeatRuleByTemplateId(prmCouponTemplate.getCouponTemplateId()));
        templateRespVO.setSpuScopeList(prmCouponTemplateService.getSpuScopeOrReceiveScope( couponTemplateId, false));
        templateRespVO.setReceiveScopeList(prmCouponTemplateService.getSpuScopeOrReceiveScope( couponTemplateId, true));
        List<Long> supplierIds = prmCouponTemplateService.getSupplierIds(couponTemplateId);
        if (CollectionUtils.isEmpty(supplierIds)) {
            // 兼容旧数据
            supplierIds = Lists.newArrayList(prmCouponTemplate.getSupplierId());
        }
        templateRespVO.setSupplierIds(supplierIds);
        return success(templateRespVO);
    }

    /**
     * 分页查询优惠券模板
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得优惠券模板分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(dcAlias = "`pct`", dcFieldAlias = SystemConstants.DC_ID, supplierAlias = "`pct`")
    public CommonResult<PageResult<PrmCouponTemplateRespVO>> getPage(@Valid PrmCouponTemplatePageReqVO pageReqVO) {
        PageResult<PrmCouponTemplateRespVO> voPageResult = prmCouponTemplateService.getPrmCouponTemplatePage(pageReqVO);
        return success(voPageResult);
    }


    /**
     * 下架优惠券模版
     */
    @ApiOperation(value = "下架优惠券模版", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "优惠券模板", businessType = BusinessType.UPDATE)
    @PutMapping("/disable")
    public CommonResult<Boolean> disable(@ApiParam(name = "couponTemplateId", value = "优惠券模版ID", required = true) @RequestParam("couponTemplateId") Long couponTemplateId) {
        prmCouponTemplateService.disable(couponTemplateId);
        promotionMqProducer.sendCouponRefreshEvent(prmCouponTemplateService.getPrmCouponTemplate(couponTemplateId));
        return success(true);
    }

    /**
     * 发布优惠券模板
     */
    @ApiOperation(value = "发布优惠券模版", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "优惠券模板", businessType = BusinessType.UPDATE)
    @PutMapping("/enable")
    public CommonResult<Boolean> enable(@ApiParam(name = "couponTemplateId", value = "优惠券模版ID", required = true) @RequestParam("couponTemplateId") Long couponTemplateId) {
        prmCouponTemplateService.enable(couponTemplateId);
        promotionMqProducer.sendCouponRefreshEvent(prmCouponTemplateService.getPrmCouponTemplate(couponTemplateId));
        return success(true);
    }

    @ApiOperation(value = "优惠券黑白名单导入商品", httpMethod = HttpMethod.POST,notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORTITEM)
    @Log(title = "优惠券黑白名单导入商品", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORTITEM)
    @PostMapping("/importData")
    public CommonResult<Map<String, Object>> importData(@ModelAttribute UploadFileForm uploadFileForm) throws Exception
    {
        MultipartFile file = uploadFileForm.getFile();
        List<String> supplierIds = uploadFileForm.getSupplierIds();
        ExcelUtil<PrmItemImportExcel> util = new ExcelUtil<>(PrmItemImportExcel.class);
        List<PrmItemImportExcel> prmItemList = util.importExcel(file.getInputStream(),0);
        // 调用服务层方法
        Map<String, Object> importResult = prmActivityCommonService.importPrmItemsData(prmItemList, supplierIds);
        CommonResult<Map<String, Object>> result = new CommonResult<>();
        result.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        result.setData(importResult);
        result.setMsg((String) importResult.get("message"));
        return result;
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载商品信息模板", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<PrmItemImportExcel> util = new ExcelUtil<>(PrmItemImportExcel.class);
        util.importTemplateExcel(response, "商品信息导入", StringUtils.EMPTY, null);
    }

    @ApiOperation(value = "优惠券黑白名单导入门店", httpMethod = HttpMethod.POST,notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORTBRANCH)
    @Log(title = "优惠券黑白名单导入门店", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORTBRANCH)
    @PostMapping("/importBranchData")
    public CommonResult<Map<String, Object>> importBranchData(MultipartFile file) throws Exception
    {
        ExcelUtil<PrmBranchImportExcel> util = new ExcelUtil<>(PrmBranchImportExcel.class);
        List<PrmBranchImportExcel> prmBranchList = util.importExcel(file.getInputStream(),0);
        // 调用服务层方法
        Map<String, Object> importResult = prmActivityCommonService.importPrmBranchsData(prmBranchList);
        CommonResult<Map<String, Object>> result = new CommonResult<>();
        result.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        result.setData(importResult);
        result.setMsg((String) importResult.get("message"));
        return result;
    }

    @PostMapping("/importBranchTemplate")
    @ApiOperation(value = "下载导入门店信息模板", httpMethod = HttpMethod.POST)
    public void importBranchTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<PrmBranchImportExcel> util = new ExcelUtil<>(PrmBranchImportExcel.class);
        util.importTemplateExcel(response, "导入门店信息导入", StringUtils.EMPTY, null);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:template:add";
        /** 编辑 */
        public static final String EDIT = "promotion:template:edit";
        /** 删除 */
        public static final String DELETE = "promotion:template:remove";
        /** 列表 */
        public static final String LIST = "promotion:template:list";
        /** 查询 */
        public static final String GET = "promotion:template:query";
        /** 停用 */
        public static final String DISABLE = "promotion:template:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:template:enable";
        /** 导入门店 */
        public static final String IMPORTBRANCH = "promotion:template:importBranch";
        /**
         * 导入商品
         */
        public static final String IMPORTITEM = "promotion:template:importItem";

    }
}
