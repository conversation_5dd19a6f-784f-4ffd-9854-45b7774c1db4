package com.zksr.promotion.controller.rule;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.google.common.collect.Lists;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.exception.enums.GlobalErrorCodeConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.material.vo.MaterialCacheVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityPageReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.rule.vo.PrmBgActivityRespVo;
import com.zksr.promotion.controller.rule.vo.PrmBgActivitySaveReqVO;
import com.zksr.promotion.domain.excel.PrmBranchImportExcel;
import com.zksr.promotion.domain.excel.PrmItemImportExcel;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPrmBgRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_1;
import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_2;
import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 买赠条件规则Controller
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Api(tags = "管理后台 - 买赠条件规则接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/bgRule")
public class PrmBgRuleController {
    @Autowired
    private IPrmBgRuleService prmBgRuleService;

    @Autowired
    private IPrmActivityService prmActivityService;

    @Autowired
    private IPrmActivityCommonService prmActivityCommonService;

    @Autowired
    private Cache<String, MaterialCacheVO> materialCache;

    /**
     * 新增买赠条件规则
     */
    @ApiOperation(value = "新增买赠条件规则", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "买赠条件规则", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmBgActivitySaveReqVO createReqVO) {
        Long activityId = prmBgRuleService.insertPrmBgRule(createReqVO);
        materialCache.remove(MaterialCacheVO.getActivityCacheKey(activityId));
        return success(activityId);
    }

    /**
     * 修改买赠条件规则
     */
    @ApiOperation(value = "修改买赠条件规则", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "买赠条件规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmBgActivitySaveReqVO updateReqVO) {
        prmBgRuleService.updatePrmBgRule(updateReqVO);

        // 刷新缓存
        if (Objects.nonNull(updateReqVO.getPrmStatus())) {
            prmActivityService.reloadSupplierActivity(updateReqVO.getActivityId());

            materialCache.remove(MaterialCacheVO.getActivityCacheKey(updateReqVO.getActivityId()));
        }
        return success(true);
    }

    /**
     * 删除买赠条件规则
     */
    @ApiOperation(value = "删除买赠条件规则", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "买赠条件规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bgRuleIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] bgRuleIds) {
        prmBgRuleService.deletePrmBgRuleByBgRuleIds(bgRuleIds);
        return success(true);
    }

    /**
     * 获取买赠条件规则详细信息
     */
    @ApiOperation(value = "获得买赠条件规则详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{activityId}")
    public CommonResult<PrmBgActivityRespVo> getInfo(@PathVariable("activityId") Long activityId) {
        return success(prmBgRuleService.getPrmBgRule(activityId));
    }

    /**
     * 分页查询买赠条件规则
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得买赠条件规则分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(supplierAlias = "`pa`", dcAlias = "`pa`", dcFieldAlias = SystemConstants.SUPPLIER_ID)
    public CommonResult<PageResult<PrmActivityRespVO>> getPage(@Valid PrmActivityPageReqVO pageReqVO) {
        pageReqVO.setFuncScope(ObjectUtil.isNull(SecurityUtils.getLoginUser().getDcId()) ? PRM_FUNC_SCOPE_1 : PRM_FUNC_SCOPE_2);
        pageReqVO.setPrmNo(PrmNoEnum.BG.getType());
        return success(prmActivityService.getPrmActivityPage(pageReqVO));
    }

    /**
     * @Description: 变更买赠状态
     * @Param: saveReqVO
     * @return: Long activityId
     * @Author: liuxingyu
     * @Date: 2024/5/20 9:28
     */
    @ApiOperation(value = "变更买赠状态", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.CHANGE_BG_STATIS)
    @RequiresPermissions(Permissions.CHANGE_BG_STATIS)
    @Log(title = "买赠规则", businessType = BusinessType.UPDATE)
    @PutMapping("/changeBgStatus")
    public CommonResult<Long> changeBgStatus(@RequestBody PrmBgActivitySaveReqVO saveReqVO){
        return success(prmBgRuleService.changeBgStatus(saveReqVO));
    }

    @ApiOperation(value = "买赠黑白名单导入商品", httpMethod = HttpMethod.POST,notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORTITEM)
    @Log(title = "买赠黑白名单导入商品", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORTITEM)
    @PostMapping("/importData")
    public CommonResult<Map<String, Object>> importData(MultipartFile file,String supplierId) throws Exception
    {
        ExcelUtil<PrmItemImportExcel> util = new ExcelUtil<>(PrmItemImportExcel.class);
        List<PrmItemImportExcel> prmItemList = util.importExcel(file.getInputStream(),0);
        // 调用服务层方法
        Map<String, Object> importResult = prmActivityCommonService.importPrmItemsData(prmItemList, Lists.newArrayList(supplierId));
        CommonResult<Map<String, Object>> result = new CommonResult<>();
        result.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        result.setData(importResult);
        result.setMsg((String) importResult.get("message"));
        return result;
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载商品信息模板", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<PrmItemImportExcel> util = new ExcelUtil<>(PrmItemImportExcel.class);
        util.importTemplateExcel(response, "商品信息导入", StringUtils.EMPTY, null);
    }

    @ApiOperation(value = "买赠黑白名单导入门店", httpMethod = HttpMethod.POST,notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORTBRANCH)
    @Log(title = "买赠黑白名单导入门店", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORTBRANCH)
    @PostMapping("/importBranchData")
    public CommonResult<Map<String, Object>> importBranchData(MultipartFile file) throws Exception
    {
        ExcelUtil<PrmBranchImportExcel> util = new ExcelUtil<>(PrmBranchImportExcel.class);
        List<PrmBranchImportExcel> prmBranchList = util.importExcel(file.getInputStream(),0);
        // 调用服务层方法
        Map<String, Object> importResult = prmActivityCommonService.importPrmBranchsData(prmBranchList);
        CommonResult<Map<String, Object>> result = new CommonResult<>();
        result.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        result.setData(importResult);
        result.setMsg((String) importResult.get("message"));
        return result;
    }

    @PostMapping("/importBranchTemplate")
    @ApiOperation(value = "下载导入门店信息模板", httpMethod = HttpMethod.POST)
    public void importBranchTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<PrmBranchImportExcel> util = new ExcelUtil<>(PrmBranchImportExcel.class);
        util.importTemplateExcel(response, "导入门店信息导入", StringUtils.EMPTY, null);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:activity:bg-rule:add";
        /** 编辑 */
        public static final String EDIT = "promotion:activity:bg-rule:edit";
        /** 删除 */
        public static final String DELETE = "promotion:activity:bg-rule:remove";
        /** 列表 */
        public static final String LIST = "promotion:activity:bg-rule:list";
        /** 查询 */
        public static final String GET = "promotion:activity:bg-rule:query";
        /** 停用 */
        public static final String DISABLE = "promotion:activity:bg-rule:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:activity:bg-rule:enable";

        /** 启用停用 */
        public static final String CHANGE_BG_STATIS = "promotion:activity:bg-rule:changeBgStatus";
        /**
         * 导入商品
         */
        public static final String IMPORTITEM = "promotion:activity:bg-rule:importItem";
        /** 导入门店 */
        public static final String IMPORTBRANCH = "promotion:activity:bg-rule:importBranch";
    }
}
