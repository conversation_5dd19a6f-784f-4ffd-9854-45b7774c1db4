package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.domain.PrmLiveRoom;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomPageReqVO;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomSaveReqVO;

/**
 * 直播间Service接口
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
public interface IPrmLiveRoomService {

    /**
     * 新增直播间
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long createLiveRoom(@Valid PrmLiveRoomSaveReqVO createReqVO);

    /**
     * 修改直播间
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmLiveRoom(@Valid PrmLiveRoomSaveReqVO updateReqVO);

    /**
     * 删除直播间
     *
     * @param id ID主键
     */
    public void deletePrmLiveRoom(Long id);

    /**
     * 批量删除直播间
     *
     * @param ids 需要删除的直播间主键集合
     * @return 结果
     */
    public void deletePrmLiveRoomByIds(Long[] ids);

    /**
     * 获得直播间
     *
     * @param id ID主键
     * @return 直播间
     */
    public PrmLiveRoom getPrmLiveRoom(Long id);

    /**
     * 获得直播间分页
     *
     * @param pageReqVO 分页查询
     * @return 直播间分页
     */
    PageResult<PrmLiveRoom> getPrmLiveRoomPage(PrmLiveRoomPageReqVO pageReqVO);

}
