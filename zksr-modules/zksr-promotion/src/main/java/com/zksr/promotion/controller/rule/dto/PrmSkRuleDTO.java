package com.zksr.promotion.controller.rule.dto;

import com.zksr.common.core.annotation.Excel;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import com.zksr.promotion.controller.rule.vo.PrmActivityWhiteOrBlackVO;
import com.zksr.promotion.controller.rule.vo.PrmFdRuleSaveReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024/5/15 15:26
 */
@Data
@ApiModel("促销活动 - prm_activity保存 Request DTO")
public class PrmSkRuleDTO extends PrmActivitySaveReqVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门店")
    private List<PrmActivityWhiteOrBlackVO> branchIds;

    @ApiModelProperty(value = "区域")
    private List<PrmActivityWhiteOrBlackVO> areaIds;

    @ApiModelProperty(value = "渠道")
    private List<PrmActivityWhiteOrBlackVO> channelIds;

}
