package com.zksr.promotion.mapper;

import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.controller.activity.dto.SkOrSpRuleCheckDTO;
import com.zksr.promotion.controller.rule.dto.PrmSkRuleDTO;
import com.zksr.promotion.domain.PrmActivity;
import com.zksr.promotion.domain.PrmSpRule;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmSkRule;
import com.zksr.promotion.controller.rule.vo.PrmSkRulePageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 秒杀规则Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Mapper
public interface PrmSkRuleMapper extends BaseMapperX<PrmSkRule> {
    default PageResult<PrmSkRule> selectPage(PrmSkRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmSkRule>()
                .eqIfPresent(PrmSkRule::getSkRuleId, reqVO.getSkRuleId())
                .eqIfPresent(PrmSkRule::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrmSkRule::getActivityId, reqVO.getActivityId())
                .eqIfPresent(PrmSkRule::getSpuId, reqVO.getSpuId())
                .eqIfPresent(PrmSkRule::getSkuId, reqVO.getSkuId())
                .eqIfPresent(PrmSkRule::getOnceLimit, reqVO.getOnceLimit())
                .eqIfPresent(PrmSkRule::getSeckillStock, reqVO.getSeckillStock())
                .eqIfPresent(PrmSkRule::getSeckillPrice, reqVO.getSeckillPrice())
                .orderByDesc(PrmSkRule::getSkRuleId));
    }

    default List<PrmSkRule> selectList(PrmSkRulePageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrmSkRule>()
                .eqIfPresent(PrmSkRule::getSkRuleId, reqVO.getSkRuleId())
                .eqIfPresent(PrmSkRule::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrmSkRule::getActivityId, reqVO.getActivityId())
                .eqIfPresent(PrmSkRule::getSpuId, reqVO.getSpuId())
                .eqIfPresent(PrmSkRule::getSkuId, reqVO.getSkuId())
                .eqIfPresent(PrmSkRule::getOnceLimit, reqVO.getOnceLimit())
                .eqIfPresent(PrmSkRule::getSeckillStock, reqVO.getSeckillStock())
                .eqIfPresent(PrmSkRule::getSeckillPrice, reqVO.getSeckillPrice())
                .inIfPresent(PrmSkRule::getSkuId, reqVO.getSkuIds())
                .orderByAsc(PrmSkRule::getSortOrder));
    }

    void deleteSkRuleByActivityId(@Param("activityId") Long activityId);

    default List<PrmSkRule> selectByActivityId(Long activityId) {
        return selectList(new LambdaQueryWrapperX<PrmSkRule>()
                .eqIfPresent(PrmSkRule::getActivityId, activityId)
                .eq(PrmSkRule::getSkStatus, 1)
                .orderByAsc(PrmSkRule::getSortOrder));
    }

    default List<PrmSkRule> selectList(Long activityId) {
        return selectList(new LambdaQueryWrapperX<PrmSkRule>()
                .eqIfPresent(PrmSkRule::getActivityId, activityId)
                .orderByAsc(PrmSkRule::getSortOrder));
    }

    List<SkOrSpRuleCheckDTO> selectRepeatActivity(@Param("activity") PrmActivity activity, @Param("skuIds") List<Long> skuIds);
}
