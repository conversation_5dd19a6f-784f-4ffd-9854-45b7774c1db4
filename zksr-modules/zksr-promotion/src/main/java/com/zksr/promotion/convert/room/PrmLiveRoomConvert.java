package com.zksr.promotion.convert.room;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmLiveRoom;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomRespVO;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 直播间 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-03-25
*/
@Mapper
public interface PrmLiveRoomConvert {

    PrmLiveRoomConvert INSTANCE = Mappers.getMapper(PrmLiveRoomConvert.class);

    PrmLiveRoomRespVO convert(PrmLiveRoom prmLiveRoom);

    PrmLiveRoom convert(PrmLiveRoomSaveReqVO prmLiveRoomSaveReq);

    PageResult<PrmLiveRoomRespVO> convertPage(PageResult<PrmLiveRoom> prmLiveRoomPage);
}