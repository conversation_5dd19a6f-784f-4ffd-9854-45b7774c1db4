package com.zksr.promotion.service.impl;

import com.alibaba.fastjson2.JSON;
import com.volcengine.model.livesaas.request.CommonRequest;
import com.volcengine.model.livesaas.request.CreateActivityAPIRequest;
import com.volcengine.model.livesaas.response.CommonResponse;
import com.volcengine.model.livesaas.response.GetActivityAPIResponse;
import com.volcengine.service.livesaas.LivesaasService;
import com.volcengine.service.livesaas.impl.LivesaasServiceImpl;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.volc.IVolcEngineRpc;
import com.zksr.promotion.convert.room.PrmLiveRoomConvert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmLiveRoomMapper;
import com.zksr.promotion.domain.PrmLiveRoom;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomPageReqVO;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomSaveReqVO;
import com.zksr.promotion.service.IPrmLiveRoomService;

import javax.annotation.Resource;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 直播间Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Service
public class PrmLiveRoomServiceImpl implements IPrmLiveRoomService {
    @Resource
    private PrmLiveRoomMapper prmLiveRoomMapper;

    @Resource
    private IVolcEngineRpc iVolcEngineRpc;

    /**
     * 新增直播间
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long createLiveRoom(PrmLiveRoomSaveReqVO createReqVO) {
        // 插入
        PrmLiveRoom prmLiveRoom = PrmLiveRoomConvert.INSTANCE.convert(createReqVO);

//        iVolcEngineRpc.createActivityAPIV2();

        prmLiveRoomMapper.insert(prmLiveRoom);
        // 返回
        return prmLiveRoom.getId();
    }

    /**
     * 修改直播间
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmLiveRoom(PrmLiveRoomSaveReqVO updateReqVO) {
        prmLiveRoomMapper.updateById(PrmLiveRoomConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除直播间
     *
     * @param id ID主键
     */
    @Override
    public void deletePrmLiveRoom(Long id) {
        // 删除
        prmLiveRoomMapper.deleteById(id);
    }

    /**
     * 批量删除直播间
     *
     * @param ids 需要删除的直播间主键
     * @return 结果
     */
    @Override
    public void deletePrmLiveRoomByIds(Long[] ids) {
        for(Long id : ids){
            // 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
             this.deletePrmLiveRoom(id);
        }
    }

    /**
     * 获得直播间
     *
     * @param id ID主键
     * @return 直播间
     */
    @Override
    public PrmLiveRoom getPrmLiveRoom(Long id) {
        return prmLiveRoomMapper.selectById(id);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmLiveRoom> getPrmLiveRoomPage(PrmLiveRoomPageReqVO pageReqVO) {
        return prmLiveRoomMapper.selectPage(pageReqVO);
    }

    private void validatePrmLiveRoomExists(Long id) {
        if (prmLiveRoomMapper.selectById(id) == null) {
//            throw exception(PRM_LIVE_ROOM_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.promotion.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 直播间 TODO 补充编号 ==========
    // ErrorCode PRM_LIVE_ROOM_NOT_EXISTS = new ErrorCode(TODO 补充编号, "直播间不存在");

    public static void main(String[] args) {
        LivesaasService livesaasService = LivesaasServiceImpl.getInstance();
        // call below method if you dont set ak and sk in ～/.volc/config

        livesaasService.setAccessKey("AKLTMzI0M2UxZDhlZGI3NDM0N2I5OWNiYjYyMTA4ZGRjY2E");
        livesaasService.setSecretKey("TVRsaE0yWXlNemt3TURFMU5EZGxPV0k0TmpRM05EYzJORE5pWmpBM01ETQ==");
        // list users
        try {
            CommonRequest commonAPIRequest = new CommonRequest();
            commonAPIRequest.setActivityId(1827535897919626L);

            CreateActivityAPIRequest createReq = new CreateActivityAPIRequest();
            createReq.setName("陈宇佳测试创建直播1");//id:1827535897919626
            createReq.setLiveTime(System.currentTimeMillis());
            //调用创建直播
            CommonResponse createRs = livesaasService.createActivityAPIV2(createReq);

            //查询直播信息
            GetActivityAPIResponse getActivityAPIResponse = livesaasService.getActivityAPI(commonAPIRequest);
            System.out.println(JSON.toJSONString(getActivityAPIResponse));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
