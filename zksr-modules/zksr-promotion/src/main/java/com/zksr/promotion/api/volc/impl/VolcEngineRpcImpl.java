package com.zksr.promotion.api.volc.impl;

import com.alibaba.fastjson2.JSON;
import com.volcengine.model.livesaas.request.CommonRequest;
import com.volcengine.model.livesaas.request.CreateActivityAPIRequest;
import com.volcengine.model.livesaas.response.CommonResponse;
import com.volcengine.model.livesaas.response.GetActivityAPIResponse;
import com.volcengine.model.livesaas.response.GetDownloadLiveClientAPIResponse;
import com.volcengine.service.livesaas.LivesaasService;
import com.volcengine.service.livesaas.impl.LivesaasServiceImpl;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.volc.IVolcEngineRpc;
import com.zksr.promotion.api.volc.convert.VolcConvert;
import com.zksr.promotion.api.volc.vo.*;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomPageReqVO;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomSaveReqVO;
import com.zksr.promotion.convert.room.PrmLiveRoomConvert;
import com.zksr.promotion.domain.PrmLiveRoom;
import com.zksr.promotion.mapper.PrmLiveRoomMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static java.time.temporal.ChronoField.HOUR_OF_DAY;

/**
 * 火山SDK调用
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Service
@Slf4j
public class VolcEngineRpcImpl implements IVolcEngineRpc {


    @Override
    public CreateActivityRespVO createActivityAPIV2(CreateActivityReqVO reqVO) {
        CreateActivityAPIRequest req = VolcConvert.INSTANCE.convert2CreateActivityAPIRequest(reqVO);
        CommonResponse createRs = null;
        //调用创建直播
        try {
            createRs = getLivesaasService().createActivityAPIV2(req);
        } catch (Exception e) {
            log.error(" 调用火山创建直播信息异常，", e);
            throw new RuntimeException(e);
        } finally {
            log.info(" 调用火山创建直播信息.入参:{},出参:{}", JsonUtils.toJsonString(req), JsonUtils.toJsonString(createRs));
        }

        return VolcConvert.INSTANCE.convert2CreateActivityRespVO(createRs);
    }

    @Override
    public GetActivityRespVO getActivityAPI(GetActivityReqVO req) {
        CommonRequest commonAPIRequest = VolcConvert.INSTANCE.convert2CommonRequest(req);
//        commonAPIRequest.setActivityId(1827535897919626L);

        GetActivityAPIResponse getActivityAPIResponse = null;
        //查询直播信息
        try {
            getActivityAPIResponse = getLivesaasService().getActivityAPI(commonAPIRequest);
        } catch (Exception e) {
            log.error(" 调用火山查询直播信息异常，", e);
            throw new RuntimeException(e);
        }finally {
            log.info(" 调用火山查询直播信息.入参:{},出参:{}", JsonUtils.toJsonString(commonAPIRequest), JsonUtils.toJsonString(getActivityAPIResponse));
        }

        return VolcConvert.INSTANCE.conver2GetActivityRespVO(getActivityAPIResponse);
    }

    @Override
    public GetDownloadLiveClientRespVO getDownloadLiveClientAPI(GetActivityReqVO req) {
        CommonRequest commonAPIRequest = VolcConvert.INSTANCE.convert2CommonRequest(req);
        GetDownloadLiveClientAPIResponse liveClientAPIResponse = null;
        try {
            liveClientAPIResponse = getLivesaasService().getDownloadLiveClientAPI(commonAPIRequest);
        } catch (Exception e) {
            log.error(" 调用获取直播伴侣一键开播地址或嘉宾连麦登录地址异常，", e);
            throw new RuntimeException(e);
        }finally {
            log.info(" 调用获取直播伴侣一键开播地址或嘉宾连麦登录地址信息.入参:{},出参:{}", JsonUtils.toJsonString(commonAPIRequest), JsonUtils.toJsonString(liveClientAPIResponse));
        }
        return VolcConvert.INSTANCE.convert2GetDownloadLiveClientRespVO(liveClientAPIResponse);
    }

    private static LivesaasService getLivesaasService(){
        LivesaasService livesaasService = LivesaasServiceImpl.getInstance();
        // call below method if you dont set ak and sk in ～/.volc/config

        livesaasService.setAccessKey("AKLTMzI0M2UxZDhlZGI3NDM0N2I5OWNiYjYyMTA4ZGRjY2E");
        livesaasService.setSecretKey("TVRsaE0yWXlNemt3TURFMU5EZGxPV0k0TmpRM05EYzJORE5pWmpBM01ETQ==");
        return livesaasService;
    }

    public static void main(String[] args) {
        CreateActivityReqVO createReq = new CreateActivityReqVO();
        createReq.setName("陈宇佳测试创建直播2");//id:1827535897919626
        createReq.setLiveTime(LocalDateTime.now().getLong(HOUR_OF_DAY));
        new VolcEngineRpcImpl().createActivityAPIV2(createReq);
    }


}
