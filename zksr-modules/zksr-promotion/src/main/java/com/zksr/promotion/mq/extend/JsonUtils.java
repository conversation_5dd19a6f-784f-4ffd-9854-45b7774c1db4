package com.zksr.promotion.mq.extend;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
//import com.midea.mbf.mq.exception.MQConsumerException;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
public class JsonUtils {

    private static final ObjectMapper MAPPER;

    static {
        MAPPER = new ObjectMapper();
        JavaTimeModule timeModule = new JavaTimeModule();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        timeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));
        timeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        MAPPER.registerModule(timeModule);
    }


    private JsonUtils() {
    }

    public static String toJsonString(Object o) {
        if (o == null) {
            return null;
        }
        try {
            return MAPPER.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("对象转化JSON格式错误", e);
        }
    }


    public static <T> T toJavaClass(String s, Class<T> valueType) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        try {
            return MAPPER.readValue(s, valueType);
        } catch (IOException e) {
            throw new RuntimeException("对象转化JSON格式错误", e);
        }
    }

    public static <T> T toJavaClass(String s, TypeReference<T> type) {
        if (s == null) {
            return null;
        }
        try {
            return MAPPER.readValue(s, type);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
