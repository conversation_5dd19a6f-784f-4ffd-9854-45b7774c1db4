package com.zksr.promotion.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.config.CustomIdGenerator;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.areaItem.AreaItemApi;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.combine.CombineApi;
import com.zksr.product.api.combine.dto.SpuCombineDTO;
import com.zksr.product.api.combine.dto.SpuCombineDtlRespDTO;
import com.zksr.product.api.combine.vo.SkuItemVO;
import com.zksr.product.api.combine.vo.SpuCombineSaveReqVO;
import com.zksr.product.api.content.ProductContentApi;
import com.zksr.product.api.supplierItem.SupplierItemApi;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.activity.dto.ActivityBranchScopeDTO;
import com.zksr.promotion.api.activity.dto.ActivityChannelScopeDTO;
import com.zksr.promotion.controller.activity.vo.PrmActivityCheckScopeVO;
import com.zksr.promotion.controller.rule.vo.*;
import com.zksr.promotion.domain.*;
import com.zksr.promotion.mapper.*;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPromotionCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.convert.rule.PrmCbRuleConvert;
import com.zksr.promotion.service.IPrmCbRuleService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_1;
import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_2;
import static com.zksr.common.core.constant.StatusConstants.STATE_ENABLE;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.PRDT_SHELF_STATUS_0;
import static com.zksr.product.constant.ProductConstant.PRDT_SHELF_STATUS_1;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 组合商品规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Service
public class PrmCbRuleServiceImpl implements IPrmCbRuleService {

    @Autowired
    private PrmCbRuleMapper prmCbRuleMapper;

    @Autowired
    private CustomIdGenerator customIdGenerator;

    @Autowired
    private PrmActivityMapper prmActivityMapper;

    @Autowired
    private PrmActivityBranchScopeMapper branchScopeMapper;

    @Autowired
    private PrmActivityChannelScopeMapper channelScopeMapper;

    @Autowired
    private PrmActivityCityScopeMapper cityScopeMapper;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Autowired
    private IPrmActivityService prmActivityService;

    @Resource
    private CombineApi combineApi;

    @Resource
    private SupplierItemApi supplierItemApi;

    @Resource
    private AreaItemApi areaItemApi;


    /**
     * 新增组合商品规则
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertPrmCbRule(PrmCbActivitySaveReqVO createReqVO) {
        createReqVO.setActivityId(null);
        // 校验活动有效时间
        validateActivityTime(createReqVO);

        checkCbRule(createReqVO.getSpuCombineSaveReqVO().getSkuItems());

        PrmActivity prmActivity = HutoolBeanUtils.toBean(createReqVO, PrmActivity.class);

        // 保存并启用
        if (ObjectUtil.equal(NumberPool.INT_ONE, prmActivity.getPrmStatus())) {
            prmActivity.setEffectTime(DateUtil.date());
            prmActivity.setEffectMan(SecurityContextHolder.getUserName());
            prmActivity.setPrmStatus(NumberPool.INT_ONE);
        }

        // 校验商品类型（平台商：1全国 运营商：2本地）
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        prmActivity.setFuncScope(ObjectUtil.isNull(dcId) ? PRM_FUNC_SCOPE_1 : PRM_FUNC_SCOPE_2);
        prmActivity.setSpuScope(0);
        createReqVO.getSpuCombineSaveReqVO().setFuncScope(prmActivity.getFuncScope().longValue());

        // 生成促销单号
        prmActivity.setPrmSheetNo(PrmNoEnum.CB.getType() + customIdGenerator.nextId());

        // 插入主表数据
        prmActivityMapper.insert(prmActivity);
        createReqVO.setActivityId(prmActivity.getActivityId());
        createReqVO.setFuncScope(prmActivity.getFuncScope());
        createReqVO.getSpuCombineSaveReqVO().setActivityId(prmActivity.getActivityId());
        createReqVO.getSpuCombineSaveReqVO().setClassId(createReqVO.getClassId());
        createReqVO.getSpuCombineSaveReqVO().setAreaId(createReqVO.getAreaId());

        // 插入详情数据
        insertDetail(createReqVO, NumberPool.INT_ONE);

        // 刷新缓存
        prmActivityService.reloadSupplierActivity(prmActivity.getActivityId());

        return prmActivity.getActivityId();
    }


    /**
     * 修改组合商品规则
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrmCbRule(PrmCbActivitySaveReqVO updateReqVO) {
        PrmActivity prmActivity = prmActivityMapper.selectById(updateReqVO.getActivityId());
        if (ObjectUtil.isNotNull(prmActivity) && ObjectUtil.notEqual(prmActivity.getPrmStatus(), NumberPool.INT_ZERO)) {
            throw exception(STATUS_NOT_UPDATE);
        }

        // 校验活动有效时间
        validateActivityTime(updateReqVO);

        checkCbRule(updateReqVO.getSpuCombineSaveReqVO().getSkuItems());

        // 保存并启用
        if (ObjectUtil.equal(NumberPool.INT_ONE, updateReqVO.getPrmStatus())) {
            updateReqVO.setEffectTime(DateUtil.date());
            updateReqVO.setEffectMan(SecurityContextHolder.getUserName());
            updateReqVO.setPrmStatus(NumberPool.INT_ONE);
        }
        updateReqVO.getSpuCombineSaveReqVO().setClassId(updateReqVO.getClassId());
        updateReqVO.getSpuCombineSaveReqVO().setAreaId(updateReqVO.getAreaId());
        // 修改促销主表
        prmActivityMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrmActivity.class));

        // 修改详情数据
        insertDetail(updateReqVO, NumberPool.INT_TWO);

        // 刷新缓存
        prmActivityService.reloadSupplierActivity(updateReqVO.getActivityId());
    }

    private void checkCbRule(List<SkuItemVO> skuItems) {
        boolean repetitionSku = skuItems.stream()
                .collect(Collectors.groupingBy(
                        SkuItemVO::getSkuId,
                        Collectors.groupingBy(
                                SkuItemVO::getUnit,
                                Collectors.counting()
                        )
                ))
                .entrySet()
                .stream()
                .anyMatch(x -> x.getValue().values().stream().anyMatch(count -> count > 1));
        if (repetitionSku) {
            throw exception(COMBINATION_SKU_REPETITION);
        }
    }


    /**
     * 删除组合商品规则
     *
     * @param cbRuleId 组合商品规则id
     */
    @Override
    public void deletePrmCbRule(Long cbRuleId) {
        // 删除
        prmCbRuleMapper.deleteById(cbRuleId);
    }

    /**
     * 批量删除组合商品规则
     *
     * @param cbRuleIds 需要删除的组合商品规则主键
     * @return 结果
     */
    @Override
    public void deletePrmCbRuleByCbRuleIds(Long[] cbRuleIds) {
        for (Long cbRuleId : cbRuleIds) {
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deletePrmCbRule(cbRuleId);
        }
    }

    /**
     * 获得组合促销详情
     *
     * @param activityId 活动ID
     * @return 组合促销详情
     */
    @Override
    public PrmCbActivityRespVo getPrmCbRule(Long activityId) {

        PrmActivity activity = prmActivityMapper.selectById(activityId);
        if (ObjectUtil.isNull(activity)) {
            throw exception(ACTIVITY_IS_NULL);
        }
        PrmCbActivityRespVo respVo = HutoolBeanUtils.toBean(activity, PrmCbActivityRespVo.class);

        //填充门店列表
        List<ActivityBranchScopeDTO> branchScope = promotionCacheService.getBranchScope(activityId);
        //填充门店列表
        if (ObjectUtil.isNotEmpty(branchScope) && ObjectUtil.notEqual(NumberPool.INT_ZERO, respVo.getBranchScopeAllFlag())) {
            //组装门店数据
            respVo.setBranchIdList(branchScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getBranchId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        //填充渠道列表
        List<ActivityChannelScopeDTO> channelScope = promotionCacheService.getChannelScope(activityId);
        if (ObjectUtil.isNotEmpty(channelScope) && ObjectUtil.notEqual(NumberPool.INT_ZERO, respVo.getChanelScopeAllFlag())) {
            //组装渠道数据
            respVo.setChannelIdList(channelScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getChannelId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        Long spuCombineId = 0L;
        SupplierItemDTO supplierItem = supplierItemApi.getSupplierItemByActivityId(activityId).getCheckedData();
        if (ToolUtil.isNotEmpty(supplierItem)) {
            respVo.setClassId(supplierItem.getSaleClassId());
            spuCombineId = supplierItem.getSpuCombineId();
        } else {
            AreaItemDTO areaItem = areaItemApi.getAreaItemByActivityId(activityId).getCheckedData();
            respVo.setAreaId(areaItem.getAreaId());
            respVo.setClassId(areaItem.getAreaClassId());
            spuCombineId = areaItem.getSpuCombineId();
        }
        SpuCombineDTO spuCombine = combineApi.getSpuCombine(spuCombineId).getCheckedData();
        if (ToolUtil.isNotEmpty(spuCombine)) {
            List<SpuCombineDtlRespDTO> spuCombineDtlList = spuCombine.getPrdtSpuCombineDtlList();
            SpuCombineSaveReqVO spuCombineSaveReqVO = HutoolBeanUtils.toBean(spuCombine, SpuCombineSaveReqVO.class);

            // 自定义转换逻辑，确保 skuUnitType 赋值给 unit
            List<SkuItemVO> skuItems = spuCombineDtlList.stream().map(dtl -> {
                SkuItemVO skuItemVO = HutoolBeanUtils.toBean(dtl, SkuItemVO.class);
                skuItemVO.setUnit(dtl.getSkuUnitType()); // 将 skuUnitType 赋值给 unit
                return skuItemVO;
            }).collect(Collectors.toList());

            spuCombineSaveReqVO.setSkuItems(skuItems);
            respVo.setSpuCombineSaveReqVO(spuCombineSaveReqVO);
        }
        return respVo;
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrmCbRule> getPrmCbRulePage(PrmCbRulePageReqVO pageReqVO) {
        return prmCbRuleMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long changePrmCbRuleStatus(PrmCbActivitySaveReqVO createReqVO, Integer type) {
        PrmActivity prmActivity = prmActivityMapper.selectById(createReqVO.getActivityId());

        //活动启用时校验活动时间
        if(type != 2 ) {
            createReqVO.setStartTime(prmActivity.getStartTime());
            createReqVO.setEndTime(prmActivity.getEndTime());
            validateActivityTime(createReqVO);
        }

        PrmCbRule prmCbRule = prmCbRuleMapper.selectByActivityId(createReqVO.getActivityId());

        // 更新活动状态和相关时间、人员信息
        updateActivityStatus(prmActivity, type);

        // 更新商品上下架状态
        updateShelfStatus(prmActivity, prmCbRule.getSpuCombineId(), type);

        return prmCbRule.getSpuCombineId();
    }

    private void updateActivityStatus(PrmActivity activity, Integer status) {
        if (ObjectUtil.equal(NumberPool.INT_ONE, status)) {
            activity.setPrmStatus(NumberPool.INT_ONE);
            activity.setEffectTime(DateUtil.date());
            activity.setEffectMan(SecurityContextHolder.getUserName());
        } else {
            activity.setPrmStatus(NumberPool.INT_TWO);
        }
        prmActivityMapper.updateById(activity);
    }

    private void updateShelfStatus(PrmActivity activity, Long spuCombineId, Integer status) {
        int shelfStatus = ObjectUtil.equal(NumberPool.INT_ONE, status) ? PRDT_SHELF_STATUS_1 : PRDT_SHELF_STATUS_0;

        if (activity.getFuncScope() == 1) {
            SupplierItemDTO supplierItemDTO = supplierItemApi.getSupplierItemBySpuCombineId(spuCombineId).getCheckedData();
            if (ToolUtil.isNotEmpty(supplierItemDTO)) {
                supplierItemDTO.setShelfStatus(shelfStatus);
                supplierItemDTO.setMinShelfStatus(shelfStatus);
                supplierItemApi.updateSupplierItem(supplierItemDTO);
            }
        } else {
            AreaItemDTO areaItemDTO = areaItemApi.getAreaItemBySpuCombineId(spuCombineId).getCheckedData();
            if (ToolUtil.isNotEmpty(areaItemDTO)) {
                areaItemDTO.setShelfStatus(shelfStatus);
                areaItemDTO.setMinShelfStatus(shelfStatus);
                areaItemApi.updateAreaItem(areaItemDTO);
            }
        }

        combineApi.editSpuCombineStatus(spuCombineId, status == 1 ? NumberPool.INT_ONE : NumberPool.INT_ZERO);
    }


    /**
     * 新增/修改详情
     *
     * @param createReqVO 促销信息
     * @param type        1 新增  2 修改
     */
    private void insertDetail(PrmCbActivitySaveReqVO createReqVO, int type) {
        Long activityId = createReqVO.getActivityId();
        Long spuCombineId = 0L;
        //修改时 需要先将数据原有详情数据删除后 再去新增
        if (NumberPool.INT_TWO == type) {
            deleteDetail(activityId);
            spuCombineId = combineApi.editSpuCombine(createReqVO.getSpuCombineSaveReqVO()).getCheckedData();
        } else {
            spuCombineId = combineApi.addSpuCombine(createReqVO.getSpuCombineSaveReqVO()).getCheckedData();
        }

        //新增详情
        //插入组合促销规则详情

        PrmCbRule cbRule = new PrmCbRule();
        cbRule.setActivityId(activityId);
        cbRule.setSpuCombineId(spuCombineId);
        cbRule.setShelfClassId(createReqVO.getClassId());
        prmCbRuleMapper.insert(cbRule);

        if (ObjectUtil.isNotEmpty(createReqVO.getAreaId())) {
            //绑定活动与城市关系
                PrmActivityCityScope cityScope = new PrmActivityCityScope();
                cityScope.setActivityId(activityId);
                cityScope.setAreaId(createReqVO.getAreaId());
                cityScope.setWhiteOrBlack(1);
                cityScopeMapper.insert(cityScope);
        }

        //插入促销活动门店适用范围
        if (ObjectUtil.isNotEmpty(createReqVO.getBranchIdList())) {
            //绑定活动与门店关系
            List<PrmActivityBranchScope> branchScopes = createReqVO.getBranchIdList().stream().map(x -> {
                PrmActivityBranchScope branchScope = new PrmActivityBranchScope();
                branchScope.setActivityId(activityId);
                branchScope.setBranchId(Long.parseLong(x.getId()));
                branchScope.setWhiteOrBlack(x.getType());
                return branchScope;
            }).collect(Collectors.toList());
            branchScopeMapper.insertBatch(branchScopes);
        }
        //插入促销活动渠道适用范围
        if (ObjectUtil.isNotEmpty(createReqVO.getChannelIdList())) {
            //绑定活动与渠道关系
            List<PrmActivityChannelScope> channelScope = createReqVO.getChannelIdList().stream().map(x -> {
                PrmActivityChannelScope prmActivityChannelScope = new PrmActivityChannelScope();
                prmActivityChannelScope.setActivityId(activityId);
                prmActivityChannelScope.setChannelId(Long.parseLong(x.getId()));
                prmActivityChannelScope.setWhiteOrBlack(x.getType());
                return prmActivityChannelScope;
            }).collect(Collectors.toList());
            channelScopeMapper.insertBatch(channelScope);
        }
    }

    /**
     * 删除组合促销详情
     *
     * @param activityId
     */
    private void deleteDetail(Long activityId) {
        //删除买赠规则详情
        prmCbRuleMapper.deleteByActivityId(activityId);
        //删除促销活动门店适用范围
        branchScopeMapper.deleteByActivityId(activityId);
        //删除销活动渠道适用范围
        channelScopeMapper.deleteByActivityId(activityId);
        //删除促销活动城市适用范围
        cityScopeMapper.deleteByActivityId(activityId);

        //清除缓存信息
        //删除促销活动门店适用范围缓存
        promotionCacheService.removeBranchScope(activityId);
        //删除销活动渠道适用范围缓存
        promotionCacheService.removeChannelScope(activityId);
        //删除促销活动城市适用范围缓存
        promotionCacheService.removeCityScope(activityId);
    }

    private void validateActivityTime(PrmCbActivitySaveReqVO reqVO) {
        if (ToolUtil.isEmpty(reqVO.getEndTime()) || ToolUtil.isEmpty(reqVO.getStartTime())) {
            throw exception(START_TIME_IS_NULL);
        }

        if (reqVO.getEndTime().getTime() < reqVO.getStartTime().getTime()) {
            throw exception(START_TIME_GREATER_THAN_END_TIME);
        }
        if (reqVO.getEndTime().getTime() < DateUtil.date().getTime()) {
            throw exception(END_TIME_GREATER_THAN_NOW_TIME);
        }
        if (reqVO.getStartTime().getTime() < DateUtil.date().getTime()) {
            throw exception(START_TIME_GREATER_THAN_NOW_TIME);
        }
    }


/*    private void validatePrmCbRuleExists(Long cbRuleId) {
        if (prmCbRuleMapper.selectById(cbRuleId) == null) {
            throw exception(PRM_CB_RULE_NOT_EXISTS);
        }
    }*/

    // TODO 待办：请将下面的错误码复制到 com.zksr.promotion.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 组合商品规则 TODO 补充编号 ==========
    // ErrorCode PRM_CB_RULE_NOT_EXISTS = new ErrorCode(TODO 补充编号, "组合商品规则不存在");


}
