package com.zksr.promotion.api.couponBatch;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.promotion.api.couponBatch.dto.CouponBatchDTO;
import com.zksr.promotion.api.couponBatch.dto.CouponBatchDtlDTO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchSaveReqVO;
import com.zksr.promotion.domain.PrmCouponBatch;
import com.zksr.promotion.domain.PrmCouponBatchDtl;
import com.zksr.promotion.service.IPrmCouponBatchDtlService;
import com.zksr.promotion.service.IPrmCouponBatchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/7 9:18
 */
@RestController
@InnerAuth
public class couponBatchApiImpl implements CouponBatchApi {

    @Autowired
    private IPrmCouponBatchService couponBatchService;

    @Autowired
    private IPrmCouponBatchDtlService couponBatchDtlService;

    @Override
    public CommonResult<List<CouponBatchDTO>> getValidCouponBatchList() {
        List<PrmCouponBatch> couponBatchDTOList = couponBatchService.getValidCouponBatchList();
        if(ToolUtil.isEmpty(couponBatchDTOList)){
            return CommonResult.success(new ArrayList<>());
        }
        return CommonResult.success(HutoolBeanUtils.toBean(couponBatchDTOList, CouponBatchDTO.class));
    }

    @Override
    public CommonResult<List<CouponBatchDtlDTO>> getCouponBatchDtlList(Long couponBatchId, Integer scopeType) {
        List<PrmCouponBatchDtl> prmCouponBatchList = couponBatchDtlService.selectByBatchCouponIdAndScopeType(couponBatchId, scopeType);
        if(ToolUtil.isEmpty(prmCouponBatchList)){
            return CommonResult.success(new ArrayList<>());
        }
        return CommonResult.success(HutoolBeanUtils.toBean(prmCouponBatchList, CouponBatchDtlDTO.class));
    }

    @Override
    public void deleteByBatchCouponIdAndBranchId(Long couponBatchId, Long branchId) {
        couponBatchDtlService.deleteByBatchCouponIdAndBranchId(couponBatchId, branchId);
    }

    @Override
    public void updateCouponBatch(CouponBatchDTO couponBatch) {
        couponBatchService.updatePrmCouponBatch(HutoolBeanUtils.toBean(couponBatch, PrmCouponBatchSaveReqVO.class));
    }
}
