package com.zksr.promotion.mq;

import com.midea.mbf.txmq.rocketmq.producer.RocketMQTxProducerImpl;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.SymbolConstants;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.common.rocketmq.constant.promotion.PromotionMqTagConstants;
import com.zksr.common.rocketmq.constant.promotion.PromotionMqTopicConstants;
import com.zksr.common.rocketmq.event.promotion.CouponReceiveEvent;
import com.zksr.promotion.api.coupon.dto.CouponReceiveDTO;
import com.zksr.promotion.convert.couponBatch.PrmCouponBatchConvert;
import com.zksr.promotion.domain.PrmCouponTemplate;
import com.zksr.trade.api.car.dto.AppCarEventDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;


/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/4/1 10:26
 */
@Configuration
@Slf4j
public class PromotionMqProducer {

    @Autowired
    private StreamBridge streamBridge;

    @Autowired
    private RocketMQTxProducerImpl rocketMQTxProducerImpl;

    /**
     * 优惠券刷新事件
     * 消费者 {@link PromotionMqConsumer#couponTemplateCache()}
     * @param couponTemplate
     */
    public void sendCouponRefreshEvent(PrmCouponTemplate couponTemplate){
        log.info("发送优惠券刷新事件：{} ", couponTemplate);
        streamBridge.send(
                MessageConstant.COUPON_TEMPLATE_CACHE_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(couponTemplate).build());
    }

    /**
     * 优惠券领取事件
     * 消费者 {@link PromotionMqConsumer#couponReceive()}
     * @param couponReceive
     */
    public void sendCouponReceiveEvent(CouponReceiveDTO couponReceive){
        log.info("发送优惠券领取事件：{} ", couponReceive);
        streamBridge.send(
                MessageConstant.COUPON_RECEIVE_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(couponReceive).build());
    }


    /**
     * 优惠券领取事件
     * 消费者 {@link PromotionMqConsumer#couponReceive()}
     * @param couponReceive
     */
    public void sendCouponReceiveEvent(CouponReceiveDTO couponReceive, boolean isNew){
        log.info("发送优惠券领取事件：{} ", couponReceive);
        if(isNew){
            CouponReceiveEvent event = PrmCouponBatchConvert.INSTANCE.convert2CouponReceiveEvent(couponReceive);
            String transactionId = new StringBuilder()
                    .append("Receive")
                    .append("-")
                    .append(couponReceive.getCouponBatchId())
                    .toString();
            event.setTransactionId(transactionId);

            Message<CouponReceiveEvent> msg = MessageBuilder.withPayload(event).setHeader(
                    RocketMQHeaders.KEYS, couponReceive.getCouponBatchId()).build();
            StringBuilder destination = new StringBuilder();
            destination.append(PromotionMqTopicConstants.TOPIC_B2B_PRM_COUPON_RECEIVED).append(SymbolConstants.COLON).append(
                    PromotionMqTagConstants.TAG_B2B_PRM_COUPON_RECEIVED);
            if (log.isInfoEnabled()) {
                log.info(" 发送优惠券领取成功事件, {}", JsonUtils.toJsonString(msg));
            }

            rocketMQTxProducerImpl.syncSend(destination.toString(), msg);
        }else {
            streamBridge.send(
                    MessageConstant.COUPON_RECEIVE_TOPIC_OUT_PUT,
                    MessageBuilder.withPayload(couponReceive).build());
        }

    }

}
