package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.domain.PrmActivitySpuScope;
import com.zksr.promotion.controller.scope.vo.PrmActivitySpuScopePageReqVO;
import com.zksr.promotion.controller.scope.vo.PrmActivitySpuScopeSaveReqVO;

/**
 * 促销活动spu适用范围Service接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
public interface IPrmActivitySpuScopeService {

    /**
     * 新增促销活动spu适用范围
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmActivitySpuScope(@Valid PrmActivitySpuScopeSaveReqVO createReqVO);

    /**
     * 修改促销活动spu适用范围
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmActivitySpuScope(@Valid PrmActivitySpuScopeSaveReqVO updateReqVO);

    /**
     * 删除促销活动spu适用范围
     *
     * @param sysCode 平台商id
     */
    public void deletePrmActivitySpuScope(Long sysCode);

    /**
     * 批量删除促销活动spu适用范围
     *
     * @param sysCodes 需要删除的促销活动spu适用范围主键集合
     * @return 结果
     */
    public void deletePrmActivitySpuScopeBySysCodes(Long[] sysCodes);

    /**
     * 获得促销活动spu适用范围
     *
     * @param sysCode 平台商id
     * @return 促销活动spu适用范围
     */
    public PrmActivitySpuScope getPrmActivitySpuScope(Long sysCode);

    /**
     * 获得促销活动spu适用范围分页
     *
     * @param pageReqVO 分页查询
     * @return 促销活动spu适用范围分页
     */
    PageResult<PrmActivitySpuScope> getPrmActivitySpuScopePage(PrmActivitySpuScopePageReqVO pageReqVO);

}
