package com.zksr.promotion.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 直播间对象 prm_live_room
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@TableName(value = "prm_live_room")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmLiveRoom extends BaseEntity {
    private static final long serialVersionUID=1L;

    /** ID主键 */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long id;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 直播ID */
    @Excel(name = "直播ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 直播间名称 */
    @Excel(name = "直播间名称")
    private String name;

    /** 直播开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "直播开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date liveTime;

    /** 直播结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "直播结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 直播模式,0：视频直播;1：伪直播 */
    @Excel(name = "直播模式,0：视频直播;1：伪直播")
    private Long activityType;

    /** 直播延时类型,1:超低延时(延时小于1秒) 2:普通延时(延时5-10秒) */
    @Excel(name = "直播延时类型,1:超低延时(延时小于1秒) 2:普通延时(延时5-10秒)")
    private Long liveMode;

    /** 视频布局,1:三分屏 2:纯视频 */
    @Excel(name = "视频布局,1:三分屏 2:纯视频")
    private Long liveLayout;

    /** 观看地址 */
    @Excel(name = "观看地址")
    private String viewUrl;

    /** 观看地址后缀 */
    @Excel(name = "观看地址后缀")
    private String viewUrlPath;

    /** 是否开启回放自动上架,0:关闭 1:开启 */
    @Excel(name = "是否开启回放自动上架,0:关闭 1:开启")
    private Long isReplayAutoOnlineEnable;

    /** 横屏直播的封面图 URL */
    @Excel(name = "横屏直播的封面图 URL")
    private String coverImage;

    /** 竖屏直播的封面图 URL */
    @Excel(name = "竖屏直播的封面图 URL")
    private String verticalCoverImage;

    /** 直播伴侣地址 */
    @Excel(name = "直播伴侣地址")
    private String liveCompanionUrl;

    /** 是否删除：0-否，1-是 */
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

}
