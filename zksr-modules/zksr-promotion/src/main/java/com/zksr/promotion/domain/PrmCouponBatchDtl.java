package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 优惠券批量发送详情对象 prm_coupon_batch_dtl
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@TableName(value = "prm_coupon_batch_dtl")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmCouponBatchDtl{
    private static final long serialVersionUID=1L;

    /** 优惠券批量发送详情id */
    @TableId
    private Long prmCouponBatchDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 优惠券批量发送id */
    @Excel(name = "优惠券批量发送id")
    private Long batchCouponId;

    /** 门店id */
    @Excel(name = "门店id")
    private Long branchId;

    /** 优惠券模板 */
    @Excel(name = "优惠券模板")
    private Long couponTemplateId;

    /** 0-优惠券模板 1-门店 */
    @Excel(name = "0-优惠券模板 1-门店")
    private Integer scopeType;


}
