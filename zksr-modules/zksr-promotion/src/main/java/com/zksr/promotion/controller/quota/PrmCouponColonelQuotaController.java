package com.zksr.promotion.controller.quota;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.controller.quota.vo.*;
import com.zksr.promotion.service.IPrmCouponColonelQuotaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 业务员发券额度Controller
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Api(tags = "管理后台 - 业务员发券额度接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/quota")
public class PrmCouponColonelQuotaController {
    @Autowired
    private IPrmCouponColonelQuotaService prmCouponColonelQuotaService;
    /**
     * 分页查询业务员发券额度
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得业务员发券额度分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmCouponColonelQuotaRespVO>> getPage(@Valid PrmCouponColonelQuotaPageReqVO pageReqVO) {
        PageResult<PrmCouponColonelQuotaRespVO> pageResult = prmCouponColonelQuotaService.getPrmCouponColonelQuotaPage(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/adjust")
    @ApiOperation(value = "业务员发券调整额度", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADJUST)
    @RequiresPermissions(Permissions.ADJUST)
    public CommonResult<Long> adjustQuota(@Valid @RequestBody PrmCouponColonelQuotaAdjustReqVO pageReqVO) {
        return success(prmCouponColonelQuotaService.adjustQuota(pageReqVO));
    }

    @GetMapping("/getQuotaDetails")
    @ApiOperation(value = "查看当前发券额度详情", httpMethod = HttpMethod.GET)
    public CommonResult<QuotaDetailResponse> getQuotaDetails(@RequestParam String colonelId) {
        return success(prmCouponColonelQuotaService.selectQuotaDetails(colonelId));
    }

    @GetMapping("/history")
    @ApiOperation(value = "查看历史发券额度", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.HISTORY)
    @RequiresPermissions(Permissions.HISTORY)
    public CommonResult<List<QuotaHistoryResponse>> getQuotaHistory(@RequestParam String colonelId) {
        return success(prmCouponColonelQuotaService.selectQuotaHistory(colonelId));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 列表 */
        public static final String LIST = "promotion:quota:list";
        /** 调整 */
        public static final String ADJUST = "promotion:quota:adjust";
        /** 查看历史发券额度 */
        public static final String HISTORY = "promotion:quota:history";
    }
}
