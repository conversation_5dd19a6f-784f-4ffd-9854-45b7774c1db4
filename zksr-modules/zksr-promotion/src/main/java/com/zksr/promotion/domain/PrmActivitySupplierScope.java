package com.zksr.promotion.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 促销活动入驻商适用范围对象 prm_activity_supplier_scope
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@TableName(value = "prm_activity_supplier_scope")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmActivitySupplierScope {
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 1-白名单 0-黑名单 */
    @Excel(name = "1-白名单 0-黑名单")
    private Integer whiteOrBlack;

}
