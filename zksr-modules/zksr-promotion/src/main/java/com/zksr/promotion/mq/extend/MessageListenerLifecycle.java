package com.zksr.promotion.mq.extend;

import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.common.message.MessageExt;

/**
 * 消息监听器生命周期类，可覆盖消费者启动，消息处理前，消息处理后几个阶段。
 *
 * <AUTHOR>
 */
public interface MessageListenerLifecycle {

    /**
     * 消费者启动前执行
     *
     * @param consumer 消费者
     */
    void prepareConsumerStart(DefaultMQPushConsumer consumer);

    /**
     * 消息出来前前执行
     *
     * @param messageExt 消息
     * @param consumer 消费者
     */
    void beforeOnMessage(MessageExt messageExt, DefaultMQPushConsumer consumer);

    /**
     * 消息处理后执行
     *
     * @param messageExt 消息
     * @param consumer 消费者
     * @param cause 消息消费过程中抛出的异常
     */
    void afterOnMessage(MessageExt messageExt, DefaultMQPushConsumer consumer, Throwable cause);

}
