package com.zksr.promotion.controller.template.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 优惠券模版拓展统计对象 prm_coupon_template_extend
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@ApiModel("优惠券模版拓展统计 - prm_coupon_template_extend分页 Request VO")
public class PrmCouponTemplateExtendSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 优惠券模版ID */
    @ApiModelProperty(value = "优惠券在订单上合计优惠了多少")
    private Long couponTemplateId;

    /** 大区 */
    @Excel(name = "大区")
    @ApiModelProperty(value = "大区")
    private Long sysCode;

    /** 已使用优惠券张数 */
    @Excel(name = "已使用优惠券张数")
    @ApiModelProperty(value = "已使用优惠券张数")
    private Integer usedCount;

    /** 实际领取优惠券张数 */
    @Excel(name = "实际领取优惠券张数")
    @ApiModelProperty(value = "实际领取优惠券张数")
    private Integer recordCount;

    /** 优惠券关联的订单合计销售金额 */
    @Excel(name = "优惠券关联的订单合计销售金额")
    @ApiModelProperty(value = "优惠券关联的订单合计销售金额")
    private Integer totalSaleAmt;

    /** 优惠券在订单上合计优惠了多少 */
    @Excel(name = "优惠券在订单上合计优惠了多少")
    @ApiModelProperty(value = "优惠券在订单上合计优惠了多少")
    private Integer totalCouponAmt;

}
