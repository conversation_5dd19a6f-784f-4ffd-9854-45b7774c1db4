package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.domain.PrmFgRule;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmBgRule;
import com.zksr.promotion.controller.rule.vo.PrmBgRulePageReqVO;

import java.util.List;


/**
 * 买赠条件规则Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Mapper
public interface PrmBgRuleMapper extends BaseMapperX<PrmBgRule> {
    default PageResult<PrmBgRule> selectPage(PrmBgRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmBgRule>()
                    .eqIfPresent(PrmBgRule::getBgRuleId, reqVO.getBgRuleId())
                    .eqIfPresent(PrmBgRule::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmBgRule::getActivityId, reqVO.getActivityId())
                    .eqIfPresent(PrmBgRule::getRuleQty, reqVO.getRuleQty())
                    .eqIfPresent(PrmBgRule::getGiftType, reqVO.getGiftType())
                    .eqIfPresent(PrmBgRule::getSkuId, reqVO.getSkuId())
                    .eqIfPresent(PrmBgRule::getCouponTemplateId, reqVO.getCouponTemplateId())
                    .eqIfPresent(PrmBgRule::getOnceGiftQty, reqVO.getOnceGiftQty())
                    .eqIfPresent(PrmBgRule::getTotalGiftQty, reqVO.getTotalGiftQty())
                    .eqIfPresent(PrmBgRule::getStatus, reqVO.getStatus())
                .orderByDesc(PrmBgRule::getBgRuleId));
    }

    default void deleteByActivityId(Long activityId) {
        delete(new LambdaUpdateWrapper<PrmBgRule>()
                .eq(PrmBgRule::getActivityId,activityId));
    }

    default List<PrmBgRule> getListByActivityId(Long activityId) {
       return selectList(new LambdaQueryWrapperX<PrmBgRule>()
                .eq(PrmBgRule::getActivityId,activityId));
    }
}
