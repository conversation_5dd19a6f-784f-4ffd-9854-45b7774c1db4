package com.zksr.promotion.controller.order.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 直播订单对象 prm_live_order
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Data
@ApiModel("直播订单 - prm_live_order分页 Request VO")
public class PrmLiveOrderSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** ID主键 */
    @ApiModelProperty(value = "版本号")
    private Long id;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 直播ID */
    @Excel(name = "直播ID")
    @NotEmpty(message = "直播ID不能为空")
    @ApiModelProperty(value = "直播ID", required = true)
    private String activityId;

    /** 直播间名称 */
    @Excel(name = "直播间名称")
    @ApiModelProperty(value = "直播间名称", required = true)
    private String name;

    /** 直播商品id */
    @Excel(name = "直播商品id")
    @NotNull(message = "直播商品id不能为空")
    @ApiModelProperty(value = "直播商品id", required = true)
    private Long liveProductId;

    /** 商品sku_id */
    @Excel(name = "商品sku_id")
    @NotNull(message = "商品sku_id不能为空")
    @ApiModelProperty(value = "商品sku_id", required = true)
    private Long skuId;

    /** 商品SPU_id */
    @Excel(name = "商品SPU_id")
    @NotNull(message = "商品SPU_id不能为空")
    @ApiModelProperty(value = "商品SPU_id", required = true)
    private Long spuId;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    @ApiModelProperty(value = "商品SPU编号", required = true)
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称", required = true)
    private String spuName;

    /** 直播价 */
    @Excel(name = "直播价")
    @ApiModelProperty(value = "直播价", required = true)
    private BigDecimal livePrice;

    /** 商品品单位 数据字典（sys_prdt_unit） */
    @Excel(name = "商品品单位 数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "商品品单位 数据字典", required = true)
    private String unit;

    /** 商品品单位名称 */
    @Excel(name = "商品品单位名称")
    @ApiModelProperty(value = "商品品单位名称", required = true)
    private String unitName;

    /** 订单数量 */
    @Excel(name = "订单数量")
    @ApiModelProperty(value = "订单数量", required = true)
    private BigDecimal orderQty;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称", required = true)
    private String branchName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人", required = true)
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话", required = true)
    private String contactPhone;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址", required = true)
    private String branchAddr;

    /** 是否删除：0-否，1-是 */
    @ApiModelProperty(value = "门店地址", required = true)
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号", required = true)
    private Long version;

}
