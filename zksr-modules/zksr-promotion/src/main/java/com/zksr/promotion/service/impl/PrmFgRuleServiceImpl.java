package com.zksr.promotion.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.enums.FgGiftGroupType;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.config.CustomIdGenerator;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.materialApply.dto.MaterialApplyDTO;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.controller.activity.vo.PrmActivityCheckScopeVO;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityScopeVO;
import com.zksr.promotion.controller.rule.vo.*;
import com.zksr.promotion.domain.PrmActivity;
import com.zksr.promotion.domain.PrmFgRule;
import com.zksr.promotion.mapper.*;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPrmFgRuleService;
import com.zksr.promotion.service.IPromotionCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_1;
import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_2;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.PRDT_MATERIAL_APPLY_TYPE_1;
import static com.zksr.promotion.enums.ErrorCodeConstants.FULL_AMT_REPETITION;
import static com.zksr.promotion.enums.ErrorCodeConstants.FULL_AMT_SKU_REPETITION;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 满赠规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Service
public class PrmFgRuleServiceImpl implements IPrmFgRuleService {

    private static final Logger log = LoggerFactory.getLogger(PrmFgRuleServiceImpl.class);

    @Autowired
    private PrmFgRuleMapper prmFgRuleMapper;

    @Autowired
    private PrmActivityMapper prmActivityMapper;

    @Autowired
    private PrmActivitySpuScopeMapper spuScopeMapper;

    @Autowired
    private PrmActivityBranchScopeMapper branchScopeMapper;

    @Autowired
    private PrmActivityCityScopeMapper cityScopeMapper;

    @Autowired
    private PrmActivityChannelScopeMapper channelScopeMapper;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Autowired
    private CustomIdGenerator customIdGenerator;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private IPrmActivityCommonService prmActivityCommonService;

    @Autowired
    private IPrmActivityService activityService;

    /**
     * 新增满赠规则
     *
     * @param saveReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertPrmFgRule(PrmFgActivitySaveReqVO saveReqVO) {
        saveReqVO.setActivityId(null);
        //校验
        PrmActivity prmActivity = prmActivityCommonService.activityChangeCheck(HutoolBeanUtils.toBean(saveReqVO, PrmActivityCheckScopeVO.class));
        saveReqVO.setEffectTime(prmActivity.getEffectTime());
        saveReqVO.setEffectMan(prmActivity.getEffectMan());
        //校验满赠规则
        checkFgRule(saveReqVO.getPrmFgRuleSaveReqVOList());
        // 插入促销主表
        PrmActivity activity = HutoolBeanUtils.toBean(saveReqVO, PrmActivity.class);
        //校验商品类型(平台商：1全国  运营商：2本地)
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        activity.setFuncScope(ObjectUtil.isNull(dcId) ? PRM_FUNC_SCOPE_1 : PRM_FUNC_SCOPE_2);
        //生成促销单号
        activity.setPrmSheetNo(PrmNoEnum.FG.getType() + customIdGenerator.nextId());
        prmActivityMapper.insert(activity);
        saveReqVO.setActivityId(activity.getActivityId());
        insertScope(saveReqVO,true);

        //新增素材信息
        if(ToolUtil.isNotEmpty(saveReqVO.getMaterialId())){
            prmActivityCommonService.addMaterialApply(MaterialApplyDTO.builder()
                    .materialId(saveReqVO.getMaterialId())
                    .applyId(saveReqVO.getActivityId())
                    .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                    .startTime(saveReqVO.getStartTime())
                    .endTime(saveReqVO.getEndTime()).build());

        }

        activityService.reloadSupplierActivity(activity.getActivityId());
        return 0L;
    }

    /**
     * 修改满赠规则
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrmFgRule(PrmFgActivitySaveReqVO updateReqVO) {
        //校验
        PrmActivity checkPrmActivity = prmActivityCommonService.checkUpdate(updateReqVO.getActivityId());
        PrmActivity prmActivity = prmActivityCommonService.activityChangeCheck(HutoolBeanUtils.toBean(updateReqVO, PrmActivityCheckScopeVO.class));
        updateReqVO.setEffectTime(prmActivity.getEffectTime());
        updateReqVO.setEffectMan(prmActivity.getEffectMan());
        //校验满赠规则
        checkFgRule(updateReqVO.getPrmFgRuleSaveReqVOList());
        //更新主表
        PrmActivity activity = HutoolBeanUtils.toBean(updateReqVO, PrmActivity.class);
        prmActivityMapper.updateById(activity);
        insertScope(updateReqVO,false);

        //修改素材信息
        if(ToolUtil.isNotEmpty(updateReqVO.getMaterialId())){
            prmActivityCommonService.editMaterialApply(MaterialApplyDTO.builder()
                    .materialId(updateReqVO.getMaterialId())
                    .applyId(updateReqVO.getActivityId())
                    .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                    .startTime(checkPrmActivity.getStartTime())
                    .endTime(checkPrmActivity.getEndTime())
                    .newStartTime(updateReqVO.getStartTime())
                    .newEndTime(updateReqVO.getEndTime())
                    .build());

        }

/*        activityService.reloadSupplierActivity(activity.getActivityId());*/
    }

    /**
     * 删除满赠规则
     *
     * @param fgRuleId 满赠规则
     */
    @Override
    public void deletePrmFgRule(Long fgRuleId) {
        // 删除
        prmFgRuleMapper.deleteById(fgRuleId);
    }

    /**
     * 批量删除满赠规则
     *
     * @param fgRuleIds 需要删除的满赠规则主键
     * @return 结果
     */
    @Override
    public void deletePrmFgRuleByFgRuleIds(Long[] fgRuleIds) {
        for (Long fgRuleId : fgRuleIds) {
            this.deletePrmFgRule(fgRuleId);
        }
    }

    /**
     * 获得满赠规则
     *
     * @param activityId 满赠规则
     * @return 满赠规则
     */
    @Override
    public PrmFgRuleRespVO getPrmFgRule(Long activityId) {
        PrmFgRuleRespVO prmFgRule = prmActivityMapper.getPrmFgRule(activityId);
        //填充赠品名称
        List<PrmFgRuleSaveReqVO> prmFgRuleSaveReqVOList = prmFgRule.getPrmFgRuleSaveReqVOList();
        if (ObjectUtil.isNotEmpty(prmFgRuleSaveReqVOList)) {
            List<PrmFgRuleSaveReqVO> prmFgRuleSaveReqVOS = prmFgRuleSaveReqVOList.stream().peek(x -> {
                //商品
                if (ObjectUtil.equal(NumberPool.INT_ZERO, x.getGiftType())) {
                    SkuDTO skuDTO = promotionCacheService.getSkuDTO(x.getSkuId());
                    if (ObjectUtil.isNotNull(skuDTO)) {
                        SpuDTO spuDTO = promotionCacheService.getSpuDTO(skuDTO.getSpuId());
                        x.setGiftName(ObjectUtil.isNull(spuDTO) ? "" : spuDTO.getSpuName());
                        //设置库存
                        x.setStock(redisStockService.getSurplusSaleQty(skuDTO.getSkuId()));
                    }
                } else if (ObjectUtil.equal(NumberPool.INT_ONE, x.getGiftType())) {
                    //优惠券
                    CouponTemplateDTO couponTemplate = promotionCacheService.getCouponTemplate(x.getCouponTemplateId());
                    x.setGiftName(ObjectUtil.isNull(couponTemplate) ? "" : couponTemplate.getCouponName());
                }
            }).collect(Collectors.toList());
            prmFgRule.setPrmFgRuleSaveReqVOList(prmFgRuleSaveReqVOS);
        }
        PrmActivityScopeVO scope = prmActivityCommonService.getScope(HutoolBeanUtils.toBean(prmFgRule, PrmActivitySaveReqVO.class));
        //填充门店列表
        prmFgRule.setBranchIdList(scope.getBranchIdList());
        //填充渠道列表
        prmFgRule.setChannelIdList(scope.getChannelIdList());
        //填充城市列表
        prmFgRule.setAreaIdList(scope.getAreaIdList());
        //填充spu列表
        prmFgRule.setSpuIdList(scope.getSpuIdList());

        //填充素材信息
        prmFgRule.setMaterialApplyVO(prmActivityCommonService.getByMaterialApplyByMaterial(
                MaterialApplyVO.builder()
                        .applyId(prmFgRule.getActivityId())
                        .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                        .startTime(prmFgRule.getStartTime())
                        .endTime(prmFgRule.getEndTime()).build()));

        return prmFgRule;
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrmFgRule> getPrmFgRulePage(PrmFgRulePageReqVO pageReqVO) {
        return prmFgRuleMapper.selectPage(pageReqVO);
    }

    /**
     * @Description: 变更满赠状态
     * @Author: liuxingyu
     * @Date: 2024/5/21 15:36
     */
    @Override
    public Long changeStatus(Long activityId, Integer prmStatus) {
        return prmActivityCommonService.changeStatus(activityId, prmStatus);
    }


    /**
     * @Description: 校验满赠规则
     * @Author: liuxingyu
     * @Date: 2024/5/22 15:08
     */
    private void checkFgRule(List<PrmFgRuleSaveReqVO> fgRuleList) {
        //满赠改造  新增购买品项数、赠送方式、赠送单位数量
        //校验
        //品项数不能为空 必须大于等于1
        //赠送方式不能为空(0仅一种，1任选，2全赠)
        //赠送单位数量 需要是 赠送方式是1任选 时才能填写 并且大于等于1
        boolean checkFg = fgRuleList.stream().anyMatch(x ->
                !(ToolUtil.isNotEmpty(x.getBuySkuNum()) && x.getBuySkuNum().compareTo(NumberPool.INT_ONE) >= NumberPool.INT_ZERO) ||
                        !(ToolUtil.isNotEmpty(x.getGiftGroupType())) ||
                        !((x.getGiftGroupType() == FgGiftGroupType.MORE.getType() && x.getGiftSkuUnitQty() >= NumberPool.INT_ONE)
                                || (x.getGiftGroupType() != FgGiftGroupType.MORE.getType() && ToolUtil.isEmpty(x.getGiftSkuUnitQty())))
        );

        if(checkFg){
            throw exception(FULL_CONDITIONS_CHECK);
        }

        for (PrmFgRuleSaveReqVO ruleSaveReqVO : fgRuleList) {
            if (ruleSaveReqVO.getCouponTemplateId()!=null&&ruleSaveReqVO.getGiftType()==1){
                CouponTemplateDTO couponTemplate = promotionCacheService.getCouponTemplate(ruleSaveReqVO.getCouponTemplateId());
                if (!(couponTemplate.getStatus() == 0
                        && couponTemplate.getTemplateStartDate().getTime() < System.currentTimeMillis()
                        && couponTemplate.getTemplateEndDate().getTime() > System.currentTimeMillis())) {
                    throw new SecurityException("请选择进行中的优惠卷");
                }

            }
        }

/*        log.info("满赠规则 List<PrmFgRuleSaveReqVO> fgRuleList", fgRuleList);
        //活动不允许相同满赠金额中, 存在多个skuID和商品单位大小giftSkuUnitType相同的数据  同一个skuID和giftSkuUnitType只能存在一条数据
        boolean repetitionSku = fgRuleList.stream()
                .filter(req -> req.getCouponTemplateId() == null) // 过滤掉 getGiftSkuUnitType 为 null 的记录
                .collect(Collectors.groupingBy(
                        PrmFgRuleSaveReqVO::getFullAmt,
                        Collectors.groupingBy(
                                PrmFgRuleSaveReqVO::getSkuId,
                                Collectors.groupingBy(
                                        PrmFgRuleSaveReqVO::getGiftSkuUnitType,
                                        Collectors.counting()
                                )
                        )
                ))
                .entrySet()
                .stream()
                .anyMatch(x -> x.getValue().entrySet().stream().anyMatch(y -> y.getValue().values().stream().anyMatch(count -> count > 1)));
        if (repetitionSku) {
            throw exception(FULL_AMT_SKU_REPETITION);
        }*/

    }

    /**
     * @Description: 插入关联表
     * @Author: liuxingyu
     * @Date: 2024/5/15 17:35
     */
    private void insertScope(PrmFgActivitySaveReqVO saveReqVO,Boolean f) {
        //删除满赠表关系
        prmFgRuleMapper.deleteByActivityId(saveReqVO.getActivityId());
        //插入满赠表
        List<PrmFgRuleSaveReqVO> prmFgRuleSaveReqVOList = saveReqVO.getPrmFgRuleSaveReqVOList();
        if (ObjectUtil.isNotEmpty(prmFgRuleSaveReqVOList)) {
            List<PrmFgRule> fgRules = HutoolBeanUtils.toBean(prmFgRuleSaveReqVOList, PrmFgRule.class);
            List<PrmFgRule> insertFgRules = fgRules.stream().peek(x -> {
                //字段填充
                x.setActivityId(saveReqVO.getActivityId());
                x.setStatus(1);
                if (f){
                    x.setFgRuleId(null);
                }
            }).collect(Collectors.toList());
            prmFgRuleMapper.insertBatch(insertFgRules);
        }
        prmActivityCommonService.bindScope(HutoolBeanUtils.toBean(saveReqVO, PrmActivityScopeVO.class));
    }
}
