package com.zksr.promotion.api.volc.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.volcengine.model.livesaas.response.GetActivityAPIResponse;
import com.volcengine.model.response.ResponseMetadata;
import lombok.Data;

/**
 * 查询直播信息返回实体类
 * @Author: chenyj8
 */
@Data
public class GetActivityRespVO {
    //火山API字段没有按驼峰

    ResponseMetadataVO responseMetadata;
    GetActivityAPIBody result;

    @Data
    public static class GetActivityAPIBody {
        String Name;
        Long LiveTime;
        String ViewUrl;
        String OwnerSubAccount;
        Integer Status;
        String CoverImage;
        Integer IsLockPreview;
        Long Id;
        Integer LiveReviewStatus;
        Integer LiveMode;
        String VerticalCoverImage;
    }
}
