package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.domain.PrmCouponTemplateReturnRule;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateReturnRulePageReqVO;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateReturnRuleSaveReqVO;

/**
 * 优惠券模板-下单返券规则（待讨论）Service接口
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
public interface IPrmCouponTemplateReturnRuleService {

    /**
     * 新增优惠券模板-下单返券规则（待讨论）
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmCouponTemplateReturnRule(@Valid PrmCouponTemplateReturnRuleSaveReqVO createReqVO);

    /**
     * 修改优惠券模板-下单返券规则（待讨论）
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmCouponTemplateReturnRule(@Valid PrmCouponTemplateReturnRuleSaveReqVO updateReqVO);

    /**
     * 删除优惠券模板-下单返券规则（待讨论）
     *
     * @param couponTemplateReturnRuleId 优惠券模板重复规则
     */
    public void deletePrmCouponTemplateReturnRule(Long couponTemplateReturnRuleId);

    /**
     * 批量删除优惠券模板-下单返券规则（待讨论）
     *
     * @param couponTemplateReturnRuleIds 需要删除的优惠券模板-下单返券规则（待讨论）主键集合
     * @return 结果
     */
    public void deletePrmCouponTemplateReturnRuleByCouponTemplateReturnRuleIds(Long[] couponTemplateReturnRuleIds);

    /**
     * 获得优惠券模板-下单返券规则（待讨论）
     *
     * @param couponTemplateReturnRuleId 优惠券模板重复规则
     * @return 优惠券模板-下单返券规则（待讨论）
     */
    public PrmCouponTemplateReturnRule getPrmCouponTemplateReturnRule(Long couponTemplateReturnRuleId);

    /**
     * 获得优惠券模板-下单返券规则（待讨论）分页
     *
     * @param pageReqVO 分页查询
     * @return 优惠券模板-下单返券规则（待讨论）分页
     */
    PageResult<PrmCouponTemplateReturnRule> getPrmCouponTemplateReturnRulePage(PrmCouponTemplateReturnRulePageReqVO pageReqVO);

}
