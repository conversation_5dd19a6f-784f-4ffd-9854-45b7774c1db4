package com.zksr.promotion.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmLiveRoom;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomPageReqVO;


/**
 * 直播间Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Mapper
public interface PrmLiveRoomMapper extends BaseMapperX<PrmLiveRoom> {
    default PageResult<PrmLiveRoom> selectPage(PrmLiveRoomPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmLiveRoom>()
                    .eqIfPresent(PrmLiveRoom::getId, reqVO.getId())
                    .eqIfPresent(PrmLiveRoom::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmLiveRoom::getActivityId, reqVO.getActivityId())
                    .likeIfPresent(PrmLiveRoom::getName, reqVO.getName())
                    .eqIfPresent(PrmLiveRoom::getLiveTime, reqVO.getLiveTime())
                    .eqIfPresent(PrmLiveRoom::getEndTime, reqVO.getEndTime())
                    .eqIfPresent(PrmLiveRoom::getActivityType, reqVO.getActivityType())
                    .eqIfPresent(PrmLiveRoom::getLiveMode, reqVO.getLiveMode())
                    .eqIfPresent(PrmLiveRoom::getLiveLayout, reqVO.getLiveLayout())
                    .eqIfPresent(PrmLiveRoom::getViewUrl, reqVO.getViewUrl())
                    .eqIfPresent(PrmLiveRoom::getViewUrlPath, reqVO.getViewUrlPath())
                    .eqIfPresent(PrmLiveRoom::getIsReplayAutoOnlineEnable, reqVO.getIsReplayAutoOnlineEnable())
                    .eqIfPresent(PrmLiveRoom::getCoverImage, reqVO.getCoverImage())
                    .eqIfPresent(PrmLiveRoom::getVerticalCoverImage, reqVO.getVerticalCoverImage())
                    .eqIfPresent(PrmLiveRoom::getLiveCompanionUrl, reqVO.getLiveCompanionUrl())
                    .eqIfPresent(PrmLiveRoom::getRemark, reqVO.getRemark())
                    .eqIfPresent(PrmLiveRoom::getDelFlag, reqVO.getDelFlag())
                    .eqIfPresent(PrmLiveRoom::getVersion, reqVO.getVersion())
                .orderByDesc(PrmLiveRoom::getId));
    }
}
