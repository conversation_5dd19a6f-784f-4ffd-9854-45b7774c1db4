package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.controller.couponBatch.vo.CouponBatchBranchRespVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchPageReqVO;
import com.zksr.promotion.domain.PrmCouponBatch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 优惠券批量发送Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Mapper
public interface PrmCouponBatchMapper extends BaseMapperX<PrmCouponBatch> {
    default PageResult<PrmCouponBatch> selectPage(PrmCouponBatchPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmCouponBatch>()
                .eqIfPresent(PrmCouponBatch::getCouponBatchId, reqVO.getCouponBatchId())
                .eqIfPresent(PrmCouponBatch::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrmCouponBatch::getFuncScope, reqVO.getFuncScope())
                .eqIfPresent(PrmCouponBatch::getCouponTemplateQty, reqVO.getCouponTemplateQty())
                .eqIfPresent(PrmCouponBatch::getBranchQty, reqVO.getBranchQty())
                .eqIfPresent(PrmCouponBatch::getTotalQty, reqVO.getTotalQty())
                .eqIfPresent(PrmCouponBatch::getValidType, reqVO.getValidType())
                .eqIfPresent(PrmCouponBatch::getValidTime, reqVO.getValidTime())
                .eqIfPresent(PrmCouponBatch::getRealSendQty, reqVO.getRealSendQty())
                .eqIfPresent(PrmCouponBatch::getTaskExecuteStatus, reqVO.getTaskExecuteStatus())
                .eqIfPresent(PrmCouponBatch::getAuditStatus, reqVO.getAuditStatus())
                .eqIfPresent(PrmCouponBatch::getCreateBy, reqVO.getCreateBy())
                .betweenIfPresent(PrmCouponBatch::getCreateTime, reqVO.getCreateTimeStart(), reqVO.getCreateTimeEnd())
                .betweenIfPresent(PrmCouponBatch::getValidTime, reqVO.getValidTimeStart(), reqVO.getValidTimeEnd())
                .eq(PrmCouponBatch::getDelFlag, "0")
                .orderByDesc(PrmCouponBatch::getCouponBatchId));
    }

    /**
     * 获取已审核的生效类型为定时生效的批次发券集合
     */
    default List<PrmCouponBatch> getValidCouponBatchList() {
        return selectList(new LambdaQueryWrapperX<PrmCouponBatch>()
                .eq(PrmCouponBatch::getAuditStatus, 1)
                .eq(PrmCouponBatch::getValidType, 0)
                .eq(PrmCouponBatch::getDelFlag, "0")
                .orderByDesc(PrmCouponBatch::getCouponBatchId));
    }

    Page<CouponBatchBranchRespVO> getPrmCouponBatchBranchList(@Param("page") Page<PrmCouponBatchPageReqVO> page, @Param("reqVO") PrmCouponBatchPageReqVO reqVO);


}
