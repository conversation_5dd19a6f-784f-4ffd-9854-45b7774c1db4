package com.zksr.promotion.controller.room;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.domain.PrmLiveProduct;
import com.zksr.promotion.service.IPrmLiveProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.promotion.controller.product.vo.PrmLiveProductPageReqVO;
import com.zksr.promotion.controller.product.vo.PrmLiveProductSaveReqVO;
import com.zksr.promotion.controller.product.vo.PrmLiveProductRespVO;
import com.zksr.promotion.convert.room.PrmLiveProductConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 直播商品Controller
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Api(tags = "管理后台 - 直播商品接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/product")
public class PrmLiveProductController {
    @Autowired
    private IPrmLiveProductService prmLiveProductService;

    /**
     * 新增直播商品
     */
    @ApiOperation(value = "新增直播商品", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
//    @RequiresPermissions(Permissions.ADD)
    @Log(title = "直播商品", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public CommonResult<String> add(@Valid @RequestBody List<PrmLiveProductSaveReqVO> createReqVO) {
        return success(prmLiveProductService.insertPrmLiveProduct(createReqVO));
    }



    /**
     * 修改直播商品
     */
    @ApiOperation(value = "修改直播商品", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
//    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "直播商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmLiveProductSaveReqVO updateReqVO) {
            prmLiveProductService.updatePrmLiveProduct(updateReqVO);
        return success(true);
    }

    /**
     * 删除直播商品
     */
    @ApiOperation(value = "删除直播商品", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
//    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "直播商品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public CommonResult<Boolean> remove(@PathVariable Long[] ids) {
        prmLiveProductService.deletePrmLiveProductByIds(ids);
        return success(true);
    }

    /**
     * 获取直播商品详细信息
     */
    @ApiOperation(value = "获得直播商品详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
//    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{id}")
    public CommonResult<PrmLiveProductRespVO> getInfo(@PathVariable("id") Long id) {
        PrmLiveProduct prmLiveProduct = prmLiveProductService.getPrmLiveProduct(id);
        return success(PrmLiveProductConvert.INSTANCE.convert(prmLiveProduct));
    }

    /**
     * 分页查询直播商品
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得直播商品分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
//    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmLiveProductRespVO>> getPage(@Valid PrmLiveProductPageReqVO pageReqVO) {
        PageResult<PrmLiveProduct> pageResult = prmLiveProductService.getPrmLiveProductPage(pageReqVO);
        return success(PrmLiveProductConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:product:add";
        /** 编辑 */
        public static final String EDIT = "promotion:product:edit";
        /** 删除 */
        public static final String DELETE = "promotion:product:remove";
        /** 列表 */
        public static final String LIST = "promotion:product:list";
        /** 查询 */
        public static final String GET = "promotion:product:query";
        /** 停用 */
        public static final String DISABLE = "promotion:product:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:product:enable";
    }
}
