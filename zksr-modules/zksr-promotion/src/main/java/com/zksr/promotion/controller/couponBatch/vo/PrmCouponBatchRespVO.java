package com.zksr.promotion.controller.couponBatch.vo;

import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 优惠券批量发送对象 prm_coupon_batch
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@ApiModel("优惠券批量发送 - prm_coupon_batch Response VO")
public class PrmCouponBatchRespVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 优惠券批量发送id
     */
    @ApiModelProperty(value = "优惠券批量发送id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponBatchId;

    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /**
     * 全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定）
     */
    @Excel(name = "全国或者本地(数据字典);1-全国商品可用", readConverterExp = "平=台商设定")
    @ApiModelProperty(value = "全国或者本地(数据字典);1-全国商品可用")
    private Long funcScope;

    /**
     * 优惠券模板数量
     */
    @Excel(name = "优惠券模板数量")
    @ApiModelProperty(value = "优惠券模板数量")
    private Long couponTemplateQty;

    /**
     * 门店数量
     */
    @Excel(name = "门店数量")
    @ApiModelProperty(value = "门店数量")
    private Long branchQty;

    /**
     * 门店发放数量
     */
    @Excel(name = "门店发放数量")
    @ApiModelProperty(value = "门店发放数量")
    private Long branchSendQty;

    /**
     * 总计发券数量
     */
    @Excel(name = "总计发券数量")
    @ApiModelProperty(value = "总计发券数量")
    private Long totalQty;

    /**
     * 生效类型：0-定时生效，1-立即生效
     */
    @Excel(name = "生效类型：0-定时生效，1-立即生效")
    @ApiModelProperty(value = "生效类型：0-定时生效，1-立即生效")
    private Integer validType;

    /**
     * 生效时间
     */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "生效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "生效时间")
    private Date validTime;

    /**
     * 实际发券成功数量
     */
    @Excel(name = "实际发券成功数量")
    @ApiModelProperty(value = "实际发券成功数量")
    private Long realSendQty;

    /**
     * 执行状态 0-未执行，1-已执行
     */
    @Excel(name = "执行状态 0-未执行，1-已执行")
    @ApiModelProperty(value = "执行状态 0-未执行，1-已执行")
    private Integer taskExecuteStatus;

    /**
     * 审核状态 0-未审核，1-已审核
     */
    @Excel(name = "审核状态 0-未审核，1-已审核")
    @ApiModelProperty(value = "审核状态 0-未审核，1-已审核")
    private Integer auditStatus;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 优惠券明细集合
     */
    @Excel(name = "优惠券明细集合")
    @ApiModelProperty(value = "优惠券明细集合")
    private List<CouponTemplateDetail> couponTemplateDetails;

    /**
     * 门店明细集合
     */
    @Excel(name = "门店明细集合")
    @ApiModelProperty(value = "门店明细集合")
    private List<BranchDetail> branchDetails;


    /**
     * 优惠券模板相关明细
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CouponTemplateDetail {

        @ApiModelProperty(value = "优惠券模版ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long couponTemplateId;

        @Excel(name = "优惠券模板名称")
        @ApiModelProperty(value = "优惠券模板名称", required = true)
        private String couponName;

        @Excel(name = "优惠券数量 ")
        @ApiModelProperty(value = "优惠券数量 ")
        private Integer couponQty;

        @Excel(name = "领取优惠券的数量;统计信息")
        @ApiModelProperty(value = "领取优惠券的数量;统计信息")
        private Integer receiveCount;

        @Excel(name = "优惠券剩余数量")
        @ApiModelProperty(value = "优惠券剩余数量")
        private Integer couponLeftQty;
    }

    /**
     * 门店相关明细
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BranchDetail {

        @ApiModelProperty(value = "门店ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long branchId;

        @ApiModelProperty(value = "门店编号")
        private String branchNo;

        @Excel(name = "门店名称")
        @ApiModelProperty(value = "门店名称", required = true)
        private String branchName;

        @Excel(name = "门店地址")
        @ApiModelProperty(value = "门店地址", required = true)
        private String branchAddress;

        @Excel(name = "门店领取优惠券的数量;统计信息")
        @ApiModelProperty(value = "门店领取优惠券的数量;统计信息")
        private Integer receiveCount;

        /**
         * 城市id
         */
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long areaId;

        /**
         * 业务员id
         */
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long colonelId;

        /**
         * 渠道id
         */
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long channelId;

        @ApiModelProperty(value = "业务员名称")
        private String colonelName;

        @Excel(name = "渠道名称")
        @ApiModelProperty(value = "渠道名称")
        private String channelName;

        @Excel(name = "城市名称")
        @ApiModelProperty(value = "城市名称")
        private String areaName;

        @Excel(name = "联系人")
        @ApiModelProperty(value = "联系人")
        private String contactName;

        @Excel(name = "联系电话")
        @ApiModelProperty(value = "联系电话")
        private String contactPhone;

    }


}
