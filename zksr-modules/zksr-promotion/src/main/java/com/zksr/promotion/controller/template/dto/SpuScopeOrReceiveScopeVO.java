package com.zksr.promotion.controller.template.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/12/8 14:18
 * @注释
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SpuScopeOrReceiveScopeVO {

        @ApiModelProperty(value = "商品适用范围或者领取范围的Type类型")
        private Integer type;

        @ApiModelProperty(value = "商品适用范围或者领取范围的值")
        private String applyIds;

        @ApiModelProperty(value = "1-白名单 0-黑名单")
        private Integer whiteOrBlack;

}
