package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.controller.scope.vo.PrmActivityChannelScopePageReqVO;
import com.zksr.promotion.domain.PrmActivityCityScope;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmActivityChannelScope;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import com.zksr.promotion.controller.scope.vo.PrmActivityChannelScopePageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 促销活动渠道适用范围Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Mapper
public interface PrmActivityChannelScopeMapper extends BaseMapperX<PrmActivityChannelScope> {
    default PageResult<PrmActivityChannelScope> selectPage(PrmActivityChannelScopePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmActivityChannelScope>()
                .eqIfPresent(PrmActivityChannelScope::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrmActivityChannelScope::getActivityId, reqVO.getActivityId())
                .eqIfPresent(PrmActivityChannelScope::getChannelId, reqVO.getChannelId())
                .orderByDesc(PrmActivityChannelScope::getSysCode));
    }

    default List<PrmActivityChannelScope> getChannelByActivityId(Long activityId) {
        List<PrmActivityChannelScope> list = selectList(
                new LambdaQueryWrapperX<PrmActivityChannelScope>()
                        .select(PrmActivityChannelScope::getChannelId, PrmActivityChannelScope::getWhiteOrBlack)
                        .eq(PrmActivityChannelScope::getActivityId, activityId)
        );

        return list;
    }

    void deleteChannelByActivityId(@Param("activityId") Long activityId);

    /**
     * @Description: 通过促销ID删除绑定关系
     * @Author: liuxingyu
     * @Date: 2024/5/16 9:44
     */
    default Integer deleteByActivityId(Long activityId) {
        return delete(new LambdaUpdateWrapper<PrmActivityChannelScope>()
                .eq(PrmActivityChannelScope::getActivityId, activityId));
    }

    /**
     * @Description: 通过促销ID获取绑定关系
     * @Author: liuxingyu
     * @Date: 2024/5/16 12:16
     */
    default List<PrmActivityChannelScope> selectByActivityId(Long activityId) {
        return selectList(new LambdaQueryWrapper<PrmActivityChannelScope>().eq(PrmActivityChannelScope::getActivityId, activityId));
    }
}
