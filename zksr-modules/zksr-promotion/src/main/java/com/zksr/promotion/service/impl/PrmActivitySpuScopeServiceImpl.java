package com.zksr.promotion.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmActivitySpuScopeMapper;
import com.zksr.promotion.convert.scope.PrmActivitySpuScopeConvert;
import com.zksr.promotion.domain.PrmActivitySpuScope;
import com.zksr.promotion.controller.scope.vo.PrmActivitySpuScopePageReqVO;
import com.zksr.promotion.controller.scope.vo.PrmActivitySpuScopeSaveReqVO;
import com.zksr.promotion.service.IPrmActivitySpuScopeService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 促销活动spu适用范围Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Service
public class PrmActivitySpuScopeServiceImpl implements IPrmActivitySpuScopeService {
    @Autowired
    private PrmActivitySpuScopeMapper prmActivitySpuScopeMapper;

    /**
     * 新增促销活动spu适用范围
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrmActivitySpuScope(PrmActivitySpuScopeSaveReqVO createReqVO) {
        // 插入
        PrmActivitySpuScope prmActivitySpuScope = PrmActivitySpuScopeConvert.INSTANCE.convert(createReqVO);
        prmActivitySpuScopeMapper.insert(prmActivitySpuScope);
        // 返回
        return prmActivitySpuScope.getSysCode();
    }

    /**
     * 修改促销活动spu适用范围
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmActivitySpuScope(PrmActivitySpuScopeSaveReqVO updateReqVO) {
        prmActivitySpuScopeMapper.updateById(PrmActivitySpuScopeConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除促销活动spu适用范围
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deletePrmActivitySpuScope(Long sysCode) {
        // 删除
        prmActivitySpuScopeMapper.deleteById(sysCode);
    }

    /**
     * 批量删除促销活动spu适用范围
     *
     * @param sysCodes 需要删除的促销活动spu适用范围主键
     * @return 结果
     */
    @Override
    public void deletePrmActivitySpuScopeBySysCodes(Long[] sysCodes) {
        for(Long sysCode : sysCodes){
            this.deletePrmActivitySpuScope(sysCode);
        }
    }

    /**
     * 获得促销活动spu适用范围
     *
     * @param sysCode 平台商id
     * @return 促销活动spu适用范围
     */
    @Override
    public PrmActivitySpuScope getPrmActivitySpuScope(Long sysCode) {
        return prmActivitySpuScopeMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmActivitySpuScope> getPrmActivitySpuScopePage(PrmActivitySpuScopePageReqVO pageReqVO) {
        return prmActivitySpuScopeMapper.selectPage(pageReqVO);
    }
}
