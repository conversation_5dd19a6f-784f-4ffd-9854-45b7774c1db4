package com.zksr.promotion.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.coupon.dto.CouponReceiveScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.controller.template.dto.SpuScopeOrReceiveScopeVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplatePageReqVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateRespVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateSaveReqVO;
import com.zksr.promotion.domain.PrmCouponTemplate;

import javax.validation.Valid;
import java.util.List;

/**
 * 优惠券模板Service接口
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
public interface IPrmCouponTemplateService {

    /**
     * 新增优惠券模板
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmCouponTemplate(@Valid PrmCouponTemplateSaveReqVO createReqVO);

    /**
     * 修改优惠券模板
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmCouponTemplate(@Valid PrmCouponTemplateSaveReqVO updateReqVO);

    /**
     * 删除优惠券模板
     *
     * @param couponTemplateId 优惠券模板id
     */
    public void deletePrmCouponTemplate(Long couponTemplateId);

    /**
     * 批量删除优惠券模板
     *
     * @param couponTemplateIds 需要删除的优惠券模板主键集合
     * @return 结果
     */
    public void deletePrmCouponTemplateByCouponTemplateIds(Long[] couponTemplateIds);

    /**
     * 获得优惠券模板
     *
     * @param couponTemplateId 优惠券模板id
     * @return 优惠券模板
     */
    public PrmCouponTemplate getPrmCouponTemplate(Long couponTemplateId);

    /**
     * 获得优惠券模板分页
     *
     * @param pageReqVO 分页查询
     * @return 优惠券模板分页
     */
    PageResult<PrmCouponTemplateRespVO> getPrmCouponTemplatePage(PrmCouponTemplatePageReqVO pageReqVO);


    /**
     * 刷新优惠券缓存
     * @param couponTemplate
     */
    public void refreshCache(PrmCouponTemplate couponTemplate);

    /**
     * 筛选优惠券库存
     * @param couponTemplateId  优惠券ID
     */
    public void refreshStock(Long couponTemplateId);

    /**
     * 获取优惠券具体使用范围
     */
    List<CouponSpuScopeDTO> getApplySpuScopeList(PrmCouponTemplate couponTemplate);

    /**
     * 获取优惠券具体领取范围
     */
    List<CouponReceiveScopeDTO> getApplyReceiveScopeList(PrmCouponTemplate couponTemplate);

    /**
     * 获取优惠券1:1缓存对象
     * @param couponTemplateId  优惠券ID
     * @return
     */
    CouponTemplateDTO getPrmCouponTemplateCacheDTO(Long couponTemplateId);

    /**
     * 停用优惠券模版
     * @param couponTemplateId  优惠券模版ID
     */
    void disable(Long couponTemplateId);

    /**
     * 启用优惠券模版
     * @param couponTemplateId  优惠券模版ID
     */
    void enable(Long couponTemplateId);

    /**
     * 获取统计优惠券已领取数据
     * @param minId 最小键ID
     * @return  每批次返回50条数据
     */
    List<CouponTemplateDTO> getTotalStockCouponTemplateList(Long minId);

    /**
     * 更新统计数量
     * @param receiveCount      已领取优惠券数量
     * @param couponTemplateId  优惠券模版ID
     */
    void updateCouponTemplateTotal(Integer receiveCount, Long couponTemplateId);

    /**
     * 获取指定领取类型有效优惠券
     * @param sysCode, couponReceiveType
     */
    List<PrmCouponTemplate> selectValidListBySysCodeAndReceiveType(Long sysCode, Integer couponReceiveType);


     /**
     * 获取优惠券具体使用范围和领取范围
     */
    List<SpuScopeOrReceiveScopeVO> getSpuScopeOrReceiveScope(Long couponTemplateId,boolean isReceive);

    /**
     * 获取优惠券模板入驻商列表
     */
    List<Long> getSupplierIds(Long couponTemplateId);

}
