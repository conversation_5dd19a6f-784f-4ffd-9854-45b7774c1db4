package com.zksr.promotion.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.exception.ErrorCode;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.product.api.materialApply.MaterialApplyApi;
import com.zksr.product.api.materialApply.dto.MaterialApplyDTO;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.promotion.api.activity.dto.ActivityBranchScopeDTO;
import com.zksr.promotion.api.activity.dto.ActivityChannelScopeDTO;
import com.zksr.promotion.api.activity.dto.ActivityCityScopeDTO;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import com.zksr.promotion.controller.activity.vo.*;
import com.zksr.promotion.controller.rule.vo.PrmActivityWhiteOrBlackVO;
import com.zksr.promotion.domain.*;
import com.zksr.promotion.domain.excel.PrmBranchImportExcel;
import com.zksr.promotion.domain.excel.PrmItemImportExcel;
import com.zksr.promotion.mapper.*;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPromotionCacheService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_1;
import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_2;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.PRDT_SHELF_STATUS_1;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

@Service
public class PrmActivityCommonServiceImpl implements IPrmActivityCommonService {
    @Autowired
    private PrmActivityMapper prmActivityMapper;

    @Autowired
    private PrmActivitySpuScopeMapper spuScopeMapper;

    @Autowired
    private PrmActivityBranchScopeMapper branchScopeMapper;

    @Autowired
    private PrmActivityCityScopeMapper cityScopeMapper;

    @Autowired
    private PrmActivityChannelScopeMapper channelScopeMapper;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Autowired
    private IPrmActivityService activityService;

    @Autowired
    private IPrmActivityService prmActivityService;

    @Resource
    private BranchApi branchApi;

    @Resource
    private SkuApi skuApi;

    @Autowired
    private MaterialApplyApi materialApplyApi;

    /**
     * @Description: 变更促销活动状态
     * @Author: liuxingyu
     * @Date: 2024/5/18 15:54
     */
    @Override
    public Long changeStatus(Long activityId, Integer status) {
        PrmActivity prmActivity = new PrmActivity();
        prmActivity.setActivityId(activityId);
        prmActivity.setPrmStatus(status);
        //启用
        if (ObjectUtil.equal(NumberPool.INT_ONE, status)) {
            PrmActivity activity = checkUpdate(activityId);
            if (ObjectUtil.isNull(activity)) {
                throw exception(ACTIVITY_IS_NULL);
            }
            //如果非全场则需要获取spu适用范围列表
           /* List<Long> spuIdList = new ArrayList<>();
            if (ObjectUtil.notEqual(NumberPool.INT_ZERO, activity.getSpuScope())) {
                List<PrmActivitySpuScope> spuScopes = spuScopeMapper.selectByActivityId(activityId);
                spuIdList = spuScopes.stream().map(PrmActivitySpuScope::getApplyId).collect(Collectors.toList());
            }*/
            //checkScope(activity, spuIdList);
            prmActivity.setEffectTime(DateUtil.date());
            prmActivity.setEffectMan(SecurityContextHolder.getUserName());
        }
        prmActivityMapper.updateById(prmActivity);
        // 刷新缓存
        activityService.reloadSupplierActivity(activityId);
        return activityId;
    }

    /**
     * @Description: 校验是否可以更改
     * @Author: liuxingyu
     * @Date: 2024/5/18 16:31
     */
    @Override
    public PrmActivity checkUpdate(Long activityId) {
        PrmActivity prmActivity = new PrmActivity();
        prmActivity.setActivityId(activityId);
        prmActivity = prmActivityMapper.selectById(prmActivity);
        if (ObjectUtil.isNotNull(prmActivity) && ObjectUtil.notEqual(prmActivity.getPrmStatus(), NumberPool.INT_ZERO)) {
            throw exception(STATUS_NOT_UPDATE);
        }
        return prmActivity;
    }

    /**
     * @Description: 新增促销活动校验保存状态（prmStatus保存:0 保存并启用:1）
     * @Author: liuxingyu
     * @Date: 2024/5/18 16:55
     */
    @Override
    public PrmActivity checkStatusByInsert(PrmActivity prmActivity) {
        if (ObjectUtil.isNull(prmActivity)) {
            return null;
        }
        //保存并启用
        if (ObjectUtil.equal(NumberPool.INT_ONE, prmActivity.getPrmStatus())) {
            prmActivity.setEffectTime(DateUtil.date());
            prmActivity.setEffectMan(SecurityContextHolder.getUserName());
        }
        return prmActivity;
    }

    /**
     * @Description: 校验商品适用范围
     * @Author: liuxingyu
     * @Date: 2024/5/18 17:17
     */
    @Override
    public void checkScope(PrmActivity activity, List<PrmActivityWhiteOrBlackVO> applyIdList) {
        //校验商品类型(平台商：1全国  运营商：2本地)
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        activity.setFuncScope(ObjectUtil.isNull(dcId) ? PRM_FUNC_SCOPE_1 : PRM_FUNC_SCOPE_2);
        //通过条件获取已经存在的促销活动
        List<Long> prmActivityIdList = prmActivityMapper.getIdListByScope(activity);
        if (ObjectUtil.isEmpty(prmActivityIdList)) {
            return;
        }
        if(ObjectUtil.isEmpty(applyIdList)){
            return;
        }
        //全场
        if (ObjectUtil.equal(NumberPool.INT_ZERO, activity.getSpuScope())) {
            // 获取冲突活动
            throw exception(new ErrorCode(ACTIVITY_COLLIDE.getCode(), StringUtils.format("以下活动和当前活动冲突: {}", conflictMessage(prmActivityIdList))));
        }
        //不是全场
        List<PrmActivitySpuScope> applyList = spuScopeMapper.getListByActivityIdList(activity.getSpuScope(), prmActivityIdList);
        //取交集,若不为空则活动冲突
        Set<PrmActivityWhiteOrBlackVO> activityApplyIdSet = applyIdList.stream().collect(Collectors.toSet());
        List<Long> applyIdIntersection = applyList.stream().filter(item -> activityApplyIdSet.contains(item.getApplyId())).map(PrmActivitySpuScope::getActivityId).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(applyIdIntersection)) {
            throw exception(new ErrorCode(ACTIVITY_COLLIDE.getCode(), StringUtils.format("以下活动和当前活动冲突: {}", conflictMessage(applyIdIntersection))));
        }
    }

    /**
     * @Description: 新增促销活动校验
     * @Author: liuxingyu
     * @Date: 2024/5/18 17:54
     */
    @Override
    public PrmActivity activityChangeCheck(@Valid PrmActivityCheckScopeVO prmActivityCheckScopeVO) {
        PrmActivity activity = HutoolBeanUtils.toBean(prmActivityCheckScopeVO, PrmActivity.class);
        //校验活动有效时间
        //活动时间不能为空
        if (ToolUtil.isEmpty(activity.getEndTime()) || ToolUtil.isEmpty(activity.getStartTime())) {
            throw exception(START_TIME_IS_NULL);
        }

        if (activity.getEndTime().getTime() < activity.getStartTime().getTime()) {
            throw exception(START_TIME_GREATER_THAN_END_TIME);
        }
        if (activity.getEndTime().getTime() < DateUtil.date().getTime()) {
            throw exception(END_TIME_GREATER_THAN_NOW_TIME);
        }
        if (activity.getStartTime().getTime() < DateUtil.date().getTime()) {
            throw exception(START_TIME_GREATER_THAN_NOW_TIME);
        }
        //校验指定类型是否冲突
        checkScope(activity, prmActivityCheckScopeVO.getSpuIdList());
        //校验促销活动校验保存状态
        return checkStatusByInsert(activity);
    }

    /**
     * @Description: 促销活动绑定关联数据
     * @Author: liuxingyu
     * @Date: 2024/5/21 15:28
     */
    @Override
    public void bindScope(PrmActivityScopeVO prmActivityScopeVO) {
        Long activityId = prmActivityScopeVO.getActivityId();
        if (ObjectUtil.isNull(activityId)) {
            throw exception(ACTIVITY_ID_IS_NULL);
        }
        //删除促销活动门店适用范围
        branchScopeMapper.deleteByActivityId(prmActivityScopeVO.getActivityId());
        //删除促销活动城市适用范围
        cityScopeMapper.deleteByActivityId(prmActivityScopeVO.getActivityId());
        //删除销活动渠道适用范围
        channelScopeMapper.deleteByActivityId(prmActivityScopeVO.getActivityId());
        //删除促销活动spu适用范围
        spuScopeMapper.deleteByActivityId(prmActivityScopeVO.getActivityId());

        //插入促销活动门店适用范围
        if (ObjectUtil.isNotEmpty(prmActivityScopeVO.getBranchIdList())) {
            //绑定活动与门店关系
            List<PrmActivityBranchScope> branchScopes = prmActivityScopeVO.getBranchIdList().stream().map(x -> {
                PrmActivityBranchScope branchScope = new PrmActivityBranchScope();
                branchScope.setActivityId(prmActivityScopeVO.getActivityId());
                branchScope.setBranchId(Long.parseLong(x.getId()));
                branchScope.setWhiteOrBlack(x.getType());
                return branchScope;
            }).collect(Collectors.toList());
            branchScopeMapper.insertBatch(branchScopes);
        }
        //插入促销活动城市适用范围
        if (ObjectUtil.isNotEmpty(prmActivityScopeVO.getAreaIdList())) {
            //绑定活动与区域城市关系
            List<PrmActivityCityScope> cityScope = prmActivityScopeVO.getAreaIdList().stream().map(x -> {
                PrmActivityCityScope prmActivityCityScope = new PrmActivityCityScope();
                prmActivityCityScope.setActivityId(prmActivityScopeVO.getActivityId());
                prmActivityCityScope.setAreaId(Long.parseLong(x.getId()));
                prmActivityCityScope.setWhiteOrBlack(x.getType());
                return prmActivityCityScope;
            }).collect(Collectors.toList());
            cityScopeMapper.insertBatch(cityScope);
        }
        //插入促销活动渠道适用范围
        if (ObjectUtil.isNotEmpty(prmActivityScopeVO.getChannelIdList())) {
            //绑定活动与渠道关系
            List<PrmActivityChannelScope> channelScope = prmActivityScopeVO.getChannelIdList().stream().map(x -> {
                PrmActivityChannelScope prmActivityChannelScope = new PrmActivityChannelScope();
                prmActivityChannelScope.setActivityId(prmActivityScopeVO.getActivityId());
                prmActivityChannelScope.setChannelId(Long.parseLong(x.getId()));
                prmActivityChannelScope.setWhiteOrBlack(x.getType());
                return prmActivityChannelScope;
            }).collect(Collectors.toList());
            channelScopeMapper.insertBatch(channelScope);
        }
        //插入促销活动spu适用范围
        if (ObjectUtil.isNotEmpty(prmActivityScopeVO.getSpuIdList())) {
            //绑定活动与渠道关系
            List<PrmActivitySpuScope> spuScope = prmActivityScopeVO.getSpuIdList().stream().map(x -> {
                PrmActivitySpuScope prmActivitySpuScope = new PrmActivitySpuScope();
                prmActivitySpuScope.setActivityId(prmActivityScopeVO.getActivityId());
                prmActivitySpuScope.setApplyId(Long.parseLong(x.getId()));
                prmActivitySpuScope.setWhiteOrBlack(x.getType());
                prmActivitySpuScope.setApplyType(x.getApplyType());
                return prmActivitySpuScope;
            }).collect(Collectors.toList());
            spuScopeMapper.insertBatch(spuScope);
        }
        //删除促销活动门店适用范围缓存
        promotionCacheService.removeBranchScope(prmActivityScopeVO.getActivityId());
        //删除促销活动城市适用范围缓存
        promotionCacheService.removeCityScope(prmActivityScopeVO.getActivityId());
        //删除销活动渠道适用范围缓存
        promotionCacheService.removeChannelScope(prmActivityScopeVO.getActivityId());
        //删除促销活动spu适用范围缓存
        promotionCacheService.removeSpuScopeScope(prmActivityScopeVO.getActivityId());
    }

    /**
     * @Description: 根据促销活动ID获取关联数据
     * @Author: liuxingyu
     * @Date: 2024/5/21 15:43
     */
    @Override
    public PrmActivityScopeVO getScope(PrmActivitySaveReqVO activity) {
        if (ObjectUtil.isNull(activity) || ObjectUtil.isNull(activity.getActivityId())) {
            throw exception(ACTIVITY_ID_IS_NULL);
        }
        PrmActivityScopeVO prmActivityScopeVO = new PrmActivityScopeVO();
        Long activityId = activity.getActivityId();
        //填充门店列表
        List<ActivityBranchScopeDTO> branchScope = promotionCacheService.getBranchScope(activityId);
        if (ObjectUtil.isNotEmpty(branchScope) && ObjectUtil.notEqual(NumberPool.INT_ZERO, activity.getBranchScopeAllFlag())) {

            //组装门店数据
            prmActivityScopeVO.setBranchIdList(branchScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getBranchId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        //填充渠道列表
        List<ActivityChannelScopeDTO> channelScope = promotionCacheService.getChannelScope(activityId);
        if (ObjectUtil.isNotEmpty(channelScope) && ObjectUtil.notEqual(NumberPool.INT_ZERO, activity.getChanelScopeAllFlag())) {
            //组装渠道数据
            prmActivityScopeVO.setChannelIdList(channelScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getChannelId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        //填充城市列表
        List<ActivityCityScopeDTO> cityScope = promotionCacheService.getCityScope(activityId);
        if (ObjectUtil.isNotEmpty(cityScope)) {
            //组装城市数据
            prmActivityScopeVO.setAreaIdList(cityScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getAreaId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        //填充spu列表
        List<ActivitySpuScopeDTO> spuScope = promotionCacheService.getSpuScope(activityId);
        if (ObjectUtil.isNotEmpty(spuScope)) {
            //组装Spu数据
            prmActivityScopeVO.setSpuIdList(spuScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getApplyId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                prmActivityWhiteOrBlackVO.setApplyType(x.getApplyType().intValue());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        return prmActivityScopeVO;
    }

    /**
     * 封装冲突活动信息
     * @param prmActivityIdList    获取ID集合
     * @return
     */
    private String conflictMessage(List<Long> prmActivityIdList) {
        String activityName = StringUtils.join(
                prmActivityMapper.selectBatchIds(prmActivityIdList).stream().filter(Objects::nonNull).map(item -> StringUtils.format("{}(id={})", item.getActivityName(), item.getActivityId())).collect(Collectors.toList())
                , StringPool.COMMA);
        return activityName;
    }

    /**
     * 促销活动导入门店信息 公用导入门店方法
     * @param prmBranchList
     * @return
     */
    @Override
    public Map<String, Object> importPrmBranchsData(List<PrmBranchImportExcel> prmBranchList) {
        if (prmBranchList.isEmpty()) {
            exception(BRANCH_IMPORT_ERROR);
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder failureMsg = new StringBuilder();
        List<MemBranchRespVO> branchList = new ArrayList<>(); // 成功的数据列表

        // 遍历导入数据
        for (int line = 0; line < prmBranchList.size(); line++) {
            if (failureMsg.length() > 2000) { // 限制错误信息长度
                break;
            }
            int cellNumber = line + 3; // 当前行号
            PrmBranchImportExcel itemData = prmBranchList.get(line);

            try {
                // 校验数据
                validatePrmBranchData(itemData, cellNumber);
                BranchDTO branchDTO = promotionCacheService.getBranchDTO(Long.valueOf(itemData.getBranchId()));
                if(ToolUtil.isEmpty(branchDTO)){
                    BranchDTO checkedData = branchApi.getByBranchId(Long.valueOf(itemData.getBranchId())).getCheckedData();
                    if(ToolUtil.isEmpty(checkedData)){
                        throw new ServiceException(String.format("未查询到该门店数据，请检查门店ID是否正确!"));
                    }
                }
                MemBranchRespVO memBranchRespVO = HutoolBeanUtils.toBean(branchDTO, MemBranchRespVO.class);
                branchList.add(memBranchRespVO);
                successNum++;
            } catch (Exception e) {
                // 捕获异常并记录失败信息
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据导入失败，错误信息：{}", cellNumber-1, e.getMessage()));
            }
        }

        // 构造返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("branchList", branchList); // 成功导入的数据列表

        if (failureNum > 0) {
            // 如果存在失败的数据，拼接失败信息
            String resultMessage = String.format(
                    "共导入 %d 条，成功 %d 条，失败 %d 条。失败原因如下：%s",
                    prmBranchList.size(), successNum, failureNum, failureMsg
            );
            resultMap.put("message", resultMessage);
        } else {
            // 如果全部成功
            String successMessage = String.format("恭喜您，数据已全部导入成功！共 %d 条", successNum);
            resultMap.put("message", successMessage);
        }

        return resultMap;
    }

    /**
     * 校验单行导入数据的规则
     *
     * @param itemData   导入数据对象
     * @param cellNumber 当前行号
     */
    private void validatePrmBranchData(PrmBranchImportExcel itemData, int cellNumber) throws Exception {
        if (StringUtils.isEmpty(itemData.getBranchId())) {
            throw new Exception(StringUtils.format("第{}行数据门店ID必填且支持字母加数字的组合", cellNumber-1));
        }
        if (StringUtils.isEmpty(itemData.getBranchName()) || itemData.getBranchName().length() > 20) {
            throw new Exception(StringUtils.format("第{}行数据门店名称必填且长度不能超过20个字符", cellNumber-1));
        }
        if (StringUtils.isEmpty(itemData.getContactAddress())) {
            throw new Exception(StringUtils.format("第{}行数据联系地址必填", cellNumber-1));
        }
    }

    @Override
    public Map<String, Object> importPrmItemsData(List<PrmItemImportExcel> prmItemList, List<String> supplierIds) {
        if (prmItemList.isEmpty()) {
            exception(ITEM_IMPORT_ERROR);
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder failureMsg = new StringBuilder();
        List<PrmActivityItemPageVo> prmBranchList = new ArrayList<>(); // 成功的数据列表

        // 遍历导入数据
        for (int line = 0; line < prmItemList.size(); line++) {
            if (failureMsg.length() > 2000) { // 限制错误信息长度
                break;
            }
            int cellNumber = line + 1; // 当前行号
            PrmItemImportExcel itemData = prmItemList.get(line);

            try {
                // 校验数据
                validatePrmItemData(itemData, cellNumber);
                if(!supplierIds.contains(String.valueOf(itemData.getSupplierId()))){
                    throw new ServiceException(String.format("传入的商品必须在选择的入驻商下面!"));
                }
                // 获取 PrmActivityItemPageVo 对象
                PrmActivityItemPageVo pageReqVO = new PrmActivityItemPageVo();
                SkuDTO skuDTO = promotionCacheService.getSkuDTO(itemData.getSkuId());
                if(ToolUtil.isEmpty(skuDTO)){
                    skuDTO = skuApi.getBySkuId(itemData.getSkuId()).getCheckedData();
                    if(ToolUtil.isEmpty(skuDTO)){
                        throw new ServiceException(String.format("SKUID查询不存在，请检查参数!"));
                    }
                }
                pageReqVO.setSpuId(skuDTO.getSpuId());
                pageReqVO.setSkuId(itemData.getSkuId());
                pageReqVO.setShelfStatus(PRDT_SHELF_STATUS_1);
                pageReqVO.setSysCode(SecurityUtils.getLoginUser().getSysCode());
                pageReqVO.setSupplierId(itemData.getSupplierId());
                PrmActivityItemPageVo prmActivityItemPageVo = null;
                try {
                    prmActivityItemPageVo = prmActivityService.getItemPage(pageReqVO).getList().get(0);
                } catch (Exception e) {
                    throw new ServiceException(String.format("未查询到商品上架数据,请确认是否为上架商品!"));
                }
                prmBranchList.add(prmActivityItemPageVo);
                successNum++;
            } catch (Exception e) {
                // 捕获异常并记录失败信息
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据导入失败，错误信息：{}", cellNumber, e.getMessage()));
            }
        }

        // 构造返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("prmBranchList", prmBranchList); // 成功导入的数据列表

        if (failureNum > 0) {
            // 如果存在失败的数据，拼接失败信息
            String resultMessage = String.format(
                    "共导入 %d 条，成功 %d 条，失败 %d 条。失败原因如下：%s",
                    prmItemList.size(), successNum, failureNum, failureMsg
            );
            resultMap.put("message", resultMessage);
        } else {
            // 如果全部成功
            String successMessage = String.format("恭喜您，数据已全部导入成功！共 %d 条", successNum);
            resultMap.put("message", successMessage);
        }

        return resultMap;
    }

    @Override
    public void addMaterialApply(MaterialApplyDTO dto) {
        materialApplyApi.addMaterialApply(dto);
    }

    @Override
    public void editMaterialApply(MaterialApplyDTO dto) {
        //本次修改时的素材信息
        Long materialId = dto.getMaterialId();

        //校验该促销是否存在素材打标信息
        //将素材信息清空 查询是否存在打标信息
        dto.setMaterialId(null);
        MaterialApplyDTO materialApplyDTO = materialApplyApi.getMaterialApplyByEchoReq(dto).getCheckedData();

        //校验
        if(ToolUtil.isNotEmpty(materialApplyDTO)){
            if(!Objects.equals(materialId,materialApplyDTO.getMaterialId())
                    || (materialApplyDTO.getStartTime().compareTo(dto.getNewStartTime()) != NumberPool.INT_ZERO
                    || materialApplyDTO.getEndTime().compareTo(dto.getNewEndTime()) != NumberPool.INT_ZERO)){
                //存在并且当前打标信息与已存在的打标信息不一致时 或者 时间不一致时 则修改
                dto.setMaterialId(materialId);
                dto.setMaterialApplyId(materialApplyDTO.getMaterialApplyId());
                dto.setStartTime(dto.getNewStartTime());
                dto.setEndTime(dto.getNewEndTime());
                materialApplyApi.editMaterialApply(dto);
            }
            //如果存在打标信息 并且与当前设置的打标信息一致 不做修改
        }else{
            dto.setMaterialId(materialId);
            materialApplyApi.addMaterialApply(dto);
        }
    }

    @Override
    public List<MaterialApplyDTO> getByMaterialApplyByApplyIds(List<Long> applyIds) {
        return materialApplyApi.getByMaterialApplyByApplyIds(applyIds).getCheckedData();
    }

    @Override
    public MaterialApplyVO getByMaterialApplyByMaterial(MaterialApplyVO vo) {
        return materialApplyApi.getByMaterialApplyByMaterial(vo).getCheckedData();
    }

    /**
     * 校验单行导入数据的规则
     *
     * @param itemData   导入数据对象
     * @param cellNumber 当前行号
     */
    private void validatePrmItemData(PrmItemImportExcel itemData, int cellNumber) throws Exception{
        // 数据校验逻辑
        if (ToolUtil.isEmpty(itemData.getSkuId())) {
            throw new Exception(StringUtils.format("第{}行数据SKUId不能为空", cellNumber));
        }
        if (ToolUtil.isEmpty(itemData.getSupplierId())) {
            throw new Exception(StringUtils.format("第{}行数据入驻商Id不能为空", cellNumber));
        }
        if(!NumberUtils.isCreatable(Convert.toStr(itemData.getStock()))){
            throw new Exception(StringUtils.format("第{}行库存数据，不是数字类型", cellNumber));
        }
    }
}
