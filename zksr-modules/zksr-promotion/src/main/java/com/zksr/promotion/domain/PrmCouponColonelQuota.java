package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 业务员发券额度对象 prm_coupon_colonel_quota
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@TableName(value = "prm_coupon_colonel_quota")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmCouponColonelQuota extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 业务员发券额度ID */
    @TableId
    private Integer couponColonelQuotaId;

    /** 平台商ID */
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 0-模板额度 1-实例额度 */
    private Integer quotaType;

    /** 本月发券额度 */
    @Excel(name = "本月发券额度")
    private BigDecimal quota;

    /** 已发额度 仅1-实例额度 */
    @Excel(name = "已发额度")
    private BigDecimal finishQuota;

    /** 月份ID，仅1-实例额度 */
    private Integer monthId;

}
