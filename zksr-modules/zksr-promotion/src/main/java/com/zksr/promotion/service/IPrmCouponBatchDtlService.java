package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.domain.PrmCouponBatchDtl;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchDtlPageReqVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchDtlSaveReqVO;

import java.util.List;

/**
 * 优惠券批量发送详情Service接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface IPrmCouponBatchDtlService {

    /**
     * 新增优惠券批量发送详情
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmCouponBatchDtl(@Valid PrmCouponBatchDtlSaveReqVO createReqVO);

    /**
     * 修改优惠券批量发送详情
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmCouponBatchDtl(@Valid PrmCouponBatchDtlSaveReqVO updateReqVO);

    /**
     * 删除优惠券批量发送详情
     *
     * @param prmCouponBatchDtlId 优惠券批量发送详情id
     */
    public void deletePrmCouponBatchDtl(Long prmCouponBatchDtlId);

    /**
     * 批量删除优惠券批量发送详情
     *
     * @param prmCouponBatchDtlIds 需要删除的优惠券批量发送详情主键集合
     * @return 结果
     */
    public void deletePrmCouponBatchDtlByPrmCouponBatchDtlIds(Long[] prmCouponBatchDtlIds);

    /**
     * 获得优惠券批量发送详情
     *
     * @param prmCouponBatchDtlId 优惠券批量发送详情id
     * @return 优惠券批量发送详情
     */
    public PrmCouponBatchDtl getPrmCouponBatchDtl(Long prmCouponBatchDtlId);

    /**
     * 获得优惠券批量发送详情分页
     *
     * @param pageReqVO 分页查询
     * @return 优惠券批量发送详情分页
     */
    PageResult<PrmCouponBatchDtl> getPrmCouponBatchDtlPage(PrmCouponBatchDtlPageReqVO pageReqVO);

    /**
     * 根据批次发券ID和发券类型获取优惠券批量发送详情集合
     */
    List<PrmCouponBatchDtl> selectByBatchCouponIdAndScopeType(Long batchCouponId, Integer scopeType);

    /**
     * 根据批量发券ID和门店ID删除优惠券批量发送详情数据
     */
    void deleteByBatchCouponIdAndBranchId(Long batchCouponId, Long branchId);

}
