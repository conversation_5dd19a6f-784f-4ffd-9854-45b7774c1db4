package com.zksr.promotion.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmActivityBranchScopeMapper;
import com.zksr.promotion.convert.scope.PrmActivityBranchScopeConvert;
import com.zksr.promotion.domain.PrmActivityBranchScope;
import com.zksr.promotion.controller.scope.vo.PrmActivityBranchScopePageReqVO;
import com.zksr.promotion.controller.scope.vo.PrmActivityBranchScopeSaveReqVO;
import com.zksr.promotion.service.IPrmActivityBranchScopeService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 促销活动门店适用范围Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Service
public class PrmActivityBranchScopeServiceImpl implements IPrmActivityBranchScopeService {
    @Autowired
    private PrmActivityBranchScopeMapper prmActivityBranchScopeMapper;

    /**
     * 新增促销活动门店适用范围
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrmActivityBranchScope(PrmActivityBranchScopeSaveReqVO createReqVO) {
        // 插入
        PrmActivityBranchScope prmActivityBranchScope = PrmActivityBranchScopeConvert.INSTANCE.convert(createReqVO);
        prmActivityBranchScopeMapper.insert(prmActivityBranchScope);
        // 返回
        return prmActivityBranchScope.getSysCode();
    }

    /**
     * 修改促销活动门店适用范围
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmActivityBranchScope(PrmActivityBranchScopeSaveReqVO updateReqVO) {
        prmActivityBranchScopeMapper.updateById(PrmActivityBranchScopeConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除促销活动门店适用范围
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deletePrmActivityBranchScope(Long sysCode) {
        // 删除
        prmActivityBranchScopeMapper.deleteById(sysCode);
    }

    /**
     * 批量删除促销活动门店适用范围
     *
     * @param sysCodes 需要删除的促销活动门店适用范围主键
     * @return 结果
     */
    @Override
    public void deletePrmActivityBranchScopeBySysCodes(Long[] sysCodes) {
        for(Long sysCode : sysCodes){
            this.deletePrmActivityBranchScope(sysCode);
        }
    }

    /**
     * 获得促销活动门店适用范围
     *
     * @param sysCode 平台商id
     * @return 促销活动门店适用范围
     */
    @Override
    public PrmActivityBranchScope getPrmActivityBranchScope(Long sysCode) {
        return prmActivityBranchScopeMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmActivityBranchScope> getPrmActivityBranchScopePage(PrmActivityBranchScopePageReqVO pageReqVO) {
        return prmActivityBranchScopeMapper.selectPage(pageReqVO);
    }

   /* private void validatePrmActivityBranchScopeExists(Long sysCode) {
        if (prmActivityBranchScopeMapper.selectById(sysCode) == null) {
            throw exception(PRM_ACTIVITY_BRANCH_SCOPE_NOT_EXISTS);
        }
    }*/

}
