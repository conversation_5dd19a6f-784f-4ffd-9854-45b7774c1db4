package com.zksr.promotion.service;

import com.zksr.promotion.domain.PrmCouponTemplateSupplier;

import java.util.List;

/**
 * 优惠券模板入驻商service
 */
public interface IPrmCouponTemplateSupplierService {

    // 新增优惠券模板入驻商数据
    Boolean insertBatch(List<PrmCouponTemplateSupplier> templateSupplierList);

    // 跟据优惠券模板id删除数据
    int deleteByCouponTemplateId(Long couponTemplateId);

    // 跟据优惠券模板id查询入驻商列表
    List<PrmCouponTemplateSupplier> selectByCouponTemplateId(Long couponTemplateId);
}
