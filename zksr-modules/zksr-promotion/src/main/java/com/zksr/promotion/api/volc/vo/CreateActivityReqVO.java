package com.zksr.promotion.api.volc.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.volcengine.model.beans.livesaas.SiteActivityTag;
import com.volcengine.model.livesaas.response.CommonResponse;
import lombok.Data;

import java.util.List;

/**
 * 创建直播请求实体
 * @Author: chenyj8
 */
@Data
public class CreateActivityReqVO {
    //火山API字段没有按驼峰
    Long LiveTime;
    Long TemplateId;
    List<SiteActivityTag> SiteTags;
    List<SiteActivityTag> TextSiteTags;
    String Name;
    String ViewUrlPath;
    String CoverImage;
    Long OldId;
    Boolean CopyStream;
    Integer LiveMode;
    String VerticalCoverImage;
}
