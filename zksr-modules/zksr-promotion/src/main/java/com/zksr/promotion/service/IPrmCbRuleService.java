package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.controller.rule.vo.PrmCbActivityRespVo;
import com.zksr.promotion.controller.rule.vo.PrmCbActivitySaveReqVO;
import com.zksr.promotion.domain.PrmCbRule;
import com.zksr.promotion.controller.rule.vo.PrmCbRulePageReqVO;
import com.zksr.promotion.controller.rule.vo.PrmCbRuleSaveReqVO;

/**
 * 组合商品规则Service接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface IPrmCbRuleService {

    /**
     * 新增组合商品规则
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmCbRule(@Valid PrmCbActivitySaveReqVO createReqVO);

    /**
     * 修改组合商品规则
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmCbRule(@Valid PrmCbActivitySaveReqVO updateReqVO);

    /**
     * 删除组合商品规则
     *
     * @param cbRuleId 组合商品规则id
     */
    public void deletePrmCbRule(Long cbRuleId);

    /**
     * 批量删除组合商品规则
     *
     * @param cbRuleIds 需要删除的组合商品规则主键集合
     * @return 结果
     */
    public void deletePrmCbRuleByCbRuleIds(Long[] cbRuleIds);

    /**
     * 获得组合商品规则
     *
     * @param activityId 组合商品规则id
     * @return 组合商品规则
     */
    public PrmCbActivityRespVo getPrmCbRule(Long activityId);

    /**
     * 获得组合商品规则分页
     *
     * @param pageReqVO 分页查询
     * @return 组合商品规则分页
     */
    PageResult<PrmCbRule> getPrmCbRulePage(PrmCbRulePageReqVO pageReqVO);

    /**
     * 修改组合促销活动状态
     *
     * @param createReqVO 修改信息 type 1 启用 2 停用
     * @return 结果
     */
    Long changePrmCbRuleStatus(@Valid PrmCbActivitySaveReqVO createReqVO, Integer type);

}
