package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 优惠券关联的使用范围, 领取范围中间对象 prm_coupon_scope_apply
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@TableName(value = "prm_coupon_scope_apply")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmCouponScopeApply extends BaseEntity{
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long scopeApplyId;

    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    @Excel(name = "优惠券模版ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    @ApiModelProperty("关联ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long applyId;

    @ApiModelProperty(value = "数据字典coupon_spu_scope;0-所有商品可用（全场券） 1-指定入驻商可用（入驻商券），2-指定品类可用（品类券），3-指定品牌可用（品牌券），4-指定商品可用（商品券）")
    private Integer spuScope;

    @ApiModelProperty(value = "数据字典coupon_receive_scope领取范围; 0-全部可领取,1-指定渠道,2-指定城市,3-指定门店")
    private Integer receiveScope;

    @ApiModelProperty("1-白名单 0-黑名单")
    private Integer whiteOrBlack;
}
