package com.zksr.promotion.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmLiveProduct;
import com.zksr.promotion.controller.product.vo.PrmLiveProductPageReqVO;


/**
 * 直播商品Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Mapper
public interface PrmLiveProductMapper extends BaseMapperX<PrmLiveProduct> {
    default PageResult<PrmLiveProduct> selectPage(PrmLiveProductPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmLiveProduct>()
                    .eqIfPresent(PrmLiveProduct::getId, reqVO.getId())
                    .eqIfPresent(PrmLiveProduct::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmLiveProduct::getActivityId, reqVO.getActivityId())
                    .likeIfPresent(PrmLiveProduct::getName, reqVO.getName())
                    .eqIfPresent(PrmLiveProduct::getSkuId, reqVO.getSkuId())
                    .eqIfPresent(PrmLiveProduct::getSpuId, reqVO.getSpuId())
                    .eqIfPresent(PrmLiveProduct::getSpuNo, reqVO.getSpuNo())
                    .likeIfPresent(PrmLiveProduct::getSpuName, reqVO.getSpuName())
                    .eqIfPresent(PrmLiveProduct::getMarkPrice, reqVO.getMarkPrice())
                    .eqIfPresent(PrmLiveProduct::getLivePrice, reqVO.getLivePrice())
                    .eqIfPresent(PrmLiveProduct::getUnit, reqVO.getUnit())
                    .likeIfPresent(PrmLiveProduct::getUnitName, reqVO.getUnitName())
                    .eqIfPresent(PrmLiveProduct::getImageUrl1, reqVO.getImageUrl1())
                    .eqIfPresent(PrmLiveProduct::getImageUrl2, reqVO.getImageUrl2())
                    .eqIfPresent(PrmLiveProduct::getImageUrl3, reqVO.getImageUrl3())
                    .eqIfPresent(PrmLiveProduct::getSupplierId, reqVO.getSupplierId())
                    .likeIfPresent(PrmLiveProduct::getSupplierName, reqVO.getSupplierName())
                    .eqIfPresent(PrmLiveProduct::getRemark, reqVO.getRemark())
                    .eqIfPresent(PrmLiveProduct::getDelFlag, reqVO.getDelFlag())
                    .eqIfPresent(PrmLiveProduct::getVersion, reqVO.getVersion())
                    .eqIfPresent(PrmLiveProduct::getBroadcastFlag, reqVO.getBroadcastFlag())
                .orderByDesc(PrmLiveProduct::getId));
    }
}
