package com.zksr.promotion.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmCouponTemplateRepeatRuleMapper;
import com.zksr.promotion.convert.rule.PrmCouponTemplateRepeatRuleConvert;
import com.zksr.promotion.domain.PrmCouponTemplateRepeatRule;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateRepeatRulePageReqVO;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateRepeatRuleSaveReqVO;
import com.zksr.promotion.service.IPrmCouponTemplateRepeatRuleService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 优惠券模板-重复规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Service
public class PrmCouponTemplateRepeatRuleServiceImpl implements IPrmCouponTemplateRepeatRuleService {
    @Autowired
    private PrmCouponTemplateRepeatRuleMapper prmCouponTemplateRepeatRuleMapper;

    /**
     * 新增优惠券模板-重复规则
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrmCouponTemplateRepeatRule(PrmCouponTemplateRepeatRuleSaveReqVO createReqVO) {
        // 删除优惠券绑定重复规则
        prmCouponTemplateRepeatRuleMapper.deleteByTemplateId(createReqVO.getCouponTemplateId());
        // 插入
        PrmCouponTemplateRepeatRule prmCouponTemplateRepeatRule = PrmCouponTemplateRepeatRuleConvert.INSTANCE.convert(createReqVO);
        prmCouponTemplateRepeatRuleMapper.insert(prmCouponTemplateRepeatRule);
        // 返回
        return prmCouponTemplateRepeatRule.getCouponTemplateRepeatRuleId();
    }

    /**
     * 修改优惠券模板-重复规则
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmCouponTemplateRepeatRule(PrmCouponTemplateRepeatRuleSaveReqVO updateReqVO) {
        prmCouponTemplateRepeatRuleMapper.updateById(PrmCouponTemplateRepeatRuleConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除优惠券模板-重复规则
     *
     * @param couponTemplateRepeatRuleId 优惠券模板重复规则
     */
    @Override
    public void deletePrmCouponTemplateRepeatRule(Long couponTemplateRepeatRuleId) {
        // 删除
        prmCouponTemplateRepeatRuleMapper.deleteById(couponTemplateRepeatRuleId);
    }

    /**
     * 批量删除优惠券模板-重复规则
     *
     * @param couponTemplateRepeatRuleIds 需要删除的优惠券模板-重复规则主键
     * @return 结果
     */
    @Override
    public void deletePrmCouponTemplateRepeatRuleByCouponTemplateRepeatRuleIds(Long[] couponTemplateRepeatRuleIds) {
        for(Long couponTemplateRepeatRuleId : couponTemplateRepeatRuleIds){
            this.deletePrmCouponTemplateRepeatRule(couponTemplateRepeatRuleId);
        }
    }

    /**
     * 获得优惠券模板-重复规则
     *
     * @param couponTemplateRepeatRuleId 优惠券模板重复规则
     * @return 优惠券模板-重复规则
     */
    @Override
    public PrmCouponTemplateRepeatRule getPrmCouponTemplateRepeatRule(Long couponTemplateRepeatRuleId) {
        return prmCouponTemplateRepeatRuleMapper.selectById(couponTemplateRepeatRuleId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmCouponTemplateRepeatRule> getPrmCouponTemplateRepeatRulePage(PrmCouponTemplateRepeatRulePageReqVO pageReqVO) {
        return prmCouponTemplateRepeatRuleMapper.selectPage(pageReqVO);
    }

    /**
     * 获取优惠券模版
     * @param couponTemplateId  优惠券模版ID
     * @return
     */
    @Override
    public PrmCouponTemplateRepeatRule getPrmCouponTemplateRepeatRuleByTemplateId(Long couponTemplateId) {
        return prmCouponTemplateRepeatRuleMapper.selectPrmCouponTemplateRepeatRuleByTemplateId(couponTemplateId);
    }

}
