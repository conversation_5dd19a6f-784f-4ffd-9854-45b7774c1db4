package com.zksr.promotion.service.impl;

import com.zksr.common.core.enums.WithdrawStateEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmLiveProductMapper;
import com.zksr.promotion.convert.room.PrmLiveProductConvert;
import com.zksr.promotion.domain.PrmLiveProduct;
import com.zksr.promotion.controller.product.vo.PrmLiveProductPageReqVO;
import com.zksr.promotion.controller.product.vo.PrmLiveProductSaveReqVO;
import com.zksr.promotion.service.IPrmLiveProductService;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 直播商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Service
public class PrmLiveProductServiceImpl implements IPrmLiveProductService {
    @Autowired
    private PrmLiveProductMapper prmLiveProductMapper;

    /**
     * 新增直播商品
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public String insertPrmLiveProduct(List<PrmLiveProductSaveReqVO> createReqVO) {
        if(CollectionUtils.isEmpty(createReqVO)){
            return null;
        }
        String activityId = createReqVO.get(0).getActivityId();
        // 插入
        List<PrmLiveProduct> prmLiveProduct = PrmLiveProductConvert.INSTANCE.convert2List(createReqVO);
        prmLiveProductMapper.insertBatch(prmLiveProduct);
        // 返回
        return activityId;
    }

    /**
     * 修改直播商品
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmLiveProduct(PrmLiveProductSaveReqVO updateReqVO) {
        prmLiveProductMapper.updateById(PrmLiveProductConvert.INSTANCE.convert(updateReqVO));
        //弹框商品设置
        if(null != updateReqVO.getActivityId() && null != updateReqVO.getBroadcastFlag() && updateReqVO.getBroadcastFlag().intValue() == NumberPool.INT_ONE){
            // 创建更新对象并设置Broadcast_Flag字段的值
            PrmLiveProduct update = new PrmLiveProduct();
            update.setBroadcastFlag(Long.valueOf(NumberPool.INT_ZERO));

            // 构建查询条件
            LambdaQueryWrapperX<PrmLiveProduct> queryWrapper = new LambdaQueryWrapperX<>();
            queryWrapper.eq(PrmLiveProduct::getActivityId, updateReqVO.getActivityId())
                    .ne(PrmLiveProduct::getId, updateReqVO.getId());

            // 执行更新操作
            prmLiveProductMapper.update(update, queryWrapper);
        }

    }

    /**
     * 删除直播商品
     *
     * @param id ID主键
     */
    @Override
    public void deletePrmLiveProduct(Long id) {
        // 删除
        prmLiveProductMapper.deleteById(id);
    }

    /**
     * 批量删除直播商品
     *
     * @param ids 需要删除的直播商品主键
     * @return 结果
     */
    @Override
    public void deletePrmLiveProductByIds(Long[] ids) {
        for(Long id : ids){
             this.deletePrmLiveProduct(id);
        }
    }

    /**
     * 获得直播商品
     *
     * @param id ID主键
     * @return 直播商品
     */
    @Override
    public PrmLiveProduct getPrmLiveProduct(Long id) {
        return prmLiveProductMapper.selectById(id);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmLiveProduct> getPrmLiveProductPage(PrmLiveProductPageReqVO pageReqVO) {
        return prmLiveProductMapper.selectPage(pageReqVO);
    }

    private void validatePrmLiveProductExists(Long id) {
        if (prmLiveProductMapper.selectById(id) == null) {
//            throw exception(PRM_LIVE_PRODUCT_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.promotion.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 直播商品 TODO 补充编号 ==========
    // ErrorCode PRM_LIVE_PRODUCT_NOT_EXISTS = new ErrorCode(TODO 补充编号, "直播商品不存在");


}
