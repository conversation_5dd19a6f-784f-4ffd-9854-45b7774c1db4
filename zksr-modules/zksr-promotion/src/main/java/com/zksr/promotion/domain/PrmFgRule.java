package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 满赠规则对象 prm_fg_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@TableName(value = "prm_fg_rule")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmFgRule extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 满赠规则
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long fgRuleId;

    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /**
     * 满赠活动id
     */
    @Excel(name = "满赠活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /**
     * 满赠金额
     */
    @Excel(name = "满赠金额")
    private BigDecimal fullAmt;

    /**
     * 赠品类型;0-商品 1-优惠券
     */
    @Excel(name = "赠品类型;0-商品 1-优惠券")
    private Integer giftType;

    /**
     * sku_id
     */
    @Excel(name = "sku_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /**
     * 返券模板id
     */
    @Excel(name = "返券模板id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    /**
     * 赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段
     */
    @Excel(name = "赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    private Integer onceGiftQty;

    /**
     * 总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段
     */
    @Excel(name = "总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    private Integer totalGiftQty;

    /**
     * 状态 1-启用 0-停用
     */
    @Excel(name = "状态 1-启用 0-停用")
    private Integer status;

    /**
     * 赠品商品单位大小
     */
    @Excel(name = "赠品商品单位大小")
    private Integer giftSkuUnitType;

    /**
     * 购买品项数(sku种类数)
     */
    @Excel(name = "购买品项数(sku种类数)")
    private Integer buySkuNum;

    /**
     * 赠送方式(默认为全赠 0仅一种，1任选，2全赠)
     */
    @Excel(name = "赠送方式(默认为全赠 0仅一种，1任选，2全赠)")
    private Integer giftGroupType;

    /**
     * 赠送单位数量
     */
    @Excel(name = "赠送单位数量")
    private Integer giftSkuUnitQty;

}
