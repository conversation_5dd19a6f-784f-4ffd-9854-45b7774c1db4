package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.domain.PrmBgRule;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCbRule;
import com.zksr.promotion.controller.rule.vo.PrmCbRulePageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import java.util.List;


/**
 * 组合商品规则Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Mapper
public interface PrmCbRuleMapper extends BaseMapperX<PrmCbRule> {
    default PageResult<PrmCbRule> selectPage(PrmCbRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmCbRule>()
                    .eqIfPresent(PrmCbRule::getCbRuleId, reqVO.getCbRuleId())
                    .eqIfPresent(PrmCbRule::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmCbRule::getActivityId, reqVO.getActivityId())
                    .eqIfPresent(PrmCbRule::getSpuCombineId, reqVO.getSpuCombineId())
                    .eqIfPresent(PrmCbRule::getShelfClassId, reqVO.getShelfClassId())
                .orderByDesc(PrmCbRule::getCbRuleId));
    }

    default void deleteByActivityId(Long activityId) {
        delete(new LambdaUpdateWrapper<PrmCbRule>()
                .eq(PrmCbRule::getActivityId,activityId));
    }

    default PrmCbRule selectByActivityId(Long activityId) {
        return selectOne(new LambdaQueryWrapperX<PrmCbRule>()
                .eq(PrmCbRule::getActivityId,activityId));
    }

    List<Long> getActivityCbByStatus(@Param("sysCode") Long sysCode,@Param("funcScope")Integer funcScope,@Param("status") Integer status);


    default List<PrmCbRule> getListByActivityId(Long activityId) {
        return selectList(new LambdaQueryWrapperX<PrmCbRule>()
                .eq(PrmCbRule::getActivityId, activityId));
    }
}
