package com.zksr.promotion.controller.template.vo;

import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel("优惠券模版拓展统计 - prm_coupon_template_receive分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmCouponTemplateReceivePageVO extends PageParam {

    /** 优惠券模版ID */
    @ApiModelProperty(value = "优惠券模版ID")
    @NotNull(message = "优惠券模版ID不能为空")
    private Long couponTemplateId;

    /** 优惠券模版ID */
    @ApiModelProperty(value = "客户编号")
    private Long customerId;

    /** 优惠券模版ID */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

}
