package com.zksr.promotion.controller.rule.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.List;

@ApiModel("促销活动-文件导入DTO")
@Data
public class UploadFileForm implements Serializable {

    private static final long serialVersionUID = 3724028365686606887L;

    @ApiModelProperty(value = "上传文件")
    private MultipartFile file;

    @ApiModelProperty(value = "活动入驻商id列表")
    private List<String> supplierIds;
}
