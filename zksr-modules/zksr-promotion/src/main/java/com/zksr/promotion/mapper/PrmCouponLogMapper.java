package com.zksr.promotion.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCouponLog;
import com.zksr.promotion.controller.coupon.vo.PrmCouponLogPageReqVO;


/**
 * 优惠券日志Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
@Mapper
public interface PrmCouponLogMapper extends BaseMapperX<PrmCouponLog> {
    default PageResult<PrmCouponLog> selectPage(PrmCouponLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmCouponLog>()
                    .eqIfPresent(PrmCouponLog::getCouponLogId, reqVO.getCouponLogId())
                    .eqIfPresent(PrmCouponLog::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmCouponLog::getCouponId, reqVO.getCouponId())
                    .eqIfPresent(PrmCouponLog::getBeforeState, reqVO.getBeforeState())
                    .eqIfPresent(PrmCouponLog::getAfterState, reqVO.getAfterState())
                    .eqIfPresent(PrmCouponLog::getOperateType, reqVO.getOperateType())
                    .eqIfPresent(PrmCouponLog::getContent, reqVO.getContent())
                .orderByDesc(PrmCouponLog::getCouponLogId));
    }
}
