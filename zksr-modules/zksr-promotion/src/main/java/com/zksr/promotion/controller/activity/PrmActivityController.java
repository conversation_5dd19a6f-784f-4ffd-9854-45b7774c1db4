package com.zksr.promotion.controller.activity;

import javax.validation.Valid;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.promotion.controller.activity.vo.PrmActivityItemPageVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.domain.PrmActivity;
import com.zksr.promotion.service.IPrmActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.promotion.controller.activity.vo.PrmActivityPageReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.convert.activity.PrmActivityConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_1;
import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_2;
import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 促销活动Controller
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Api(tags = "管理后台 - 促销活动接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/activity")
public class PrmActivityController {
    @Autowired
    private IPrmActivityService prmActivityService;

    /**
     * 新增促销活动
     */
    @ApiOperation(value = "新增促销活动", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "促销活动", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmActivitySaveReqVO createReqVO) {
        return success(prmActivityService.insertPrmActivity(createReqVO));
    }

    /**
     * 修改促销活动
     */
    @ApiOperation(value = "修改促销活动", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "促销活动", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmActivitySaveReqVO updateReqVO) {
            prmActivityService.updatePrmActivity(updateReqVO);
        return success(true);
    }

    /**
     * 删除促销活动
     */
    @ApiOperation(value = "删除促销活动", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "促销活动", businessType = BusinessType.DELETE)
    @DeleteMapping("/{activityIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] activityIds) {
        prmActivityService.deletePrmActivityByActivityIds(activityIds);
        return success(true);
    }

    /**
     * 获取促销活动详细信息
     */
    @ApiOperation(value = "获得促销活动详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{activityId}")
    public CommonResult<PrmActivityRespVO> getInfo(@PathVariable("activityId") Long activityId) {
        PrmActivity prmActivity = prmActivityService.getPrmActivity(activityId);
        return success(PrmActivityConvert.INSTANCE.convert(prmActivity));
    }

    /**
     * 分页查询促销活动
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得促销活动分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(supplierAlias = "`pa`", dcAlias = "`pa`", dcFieldAlias = SystemConstants.SUPPLIER_ID)
    public CommonResult<PageResult<PrmActivityRespVO>> getPage(@Valid PrmActivityPageReqVO pageReqVO) {
        pageReqVO.setFuncScope(ObjectUtil.isNull(SecurityUtils.getLoginUser().getDcId()) ? PRM_FUNC_SCOPE_1 : PRM_FUNC_SCOPE_2);
        return success(prmActivityService.getPrmActivityPage(pageReqVO));
    }

    /**
     * 分页查询新增促销活动的商品信息
     */
    @GetMapping("/listItem")
    @ApiOperation(value = "获得促销活动商品分页列表", httpMethod = HttpMethod.GET)
    @DataScope(supplierAlias = "`spu`")
    public CommonResult<PageResult<PrmActivityItemPageVo>> getItemPage(@Valid PrmActivityItemPageVo pageReqVO) {
        return success(prmActivityService.getItemPage(pageReqVO));
    }

    /**
     * 分页查询新增促销活动的商品信息
     */
    @PostMapping("/listItems")
    @ApiOperation(value = "获得促销活动商品分页列表", httpMethod = HttpMethod.POST)
    @DataScope(supplierAlias = "`spu`")
    public CommonResult<PageResult<PrmActivityItemPageVo>> listItems(@Valid @RequestBody PrmActivityItemPageVo pageReqVO) {
        return success(prmActivityService.getItemPage(pageReqVO));
    }

    /**
     * 分页查询新增促销活动的商品信息
     */
    @PostMapping("/checkActivityByItem/{skuIds}")
    @ApiOperation(value = "校验全国/城市 商品下架商品校验是否参与促销", httpMethod = HttpMethod.POST)
    public CommonResult<String> checkActivityByItem(@PathVariable("skuIds") Long[] skuIds) {
        return success(prmActivityService.checkActivityByItem(skuIds));
    }

    /**
     * 分页查询促销活动
     */
    @GetMapping("/listActivity")
    @ApiOperation(value = "获得促销活动分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmActivityRespVO>> listActivity(PrmActivityPageReqVO pageReqVO) {
        return success(prmActivityService.listActivity(pageReqVO));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:activity:add";
        /** 编辑 */
        public static final String EDIT = "promotion:activity:edit";
        /** 删除 */
        public static final String DELETE = "promotion:activity:remove";
        /** 列表 */
        public static final String LIST = "promotion:activity:list";
        /** 查询 */
        public static final String GET = "promotion:activity:query";
        /** 停用 */
        public static final String DISABLE = "promotion:activity:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:activity:enable";
    }
}
