package com.zksr.promotion.convert.rule;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.BgRuleDTO;
import com.zksr.promotion.api.activity.dto.CbRuleDTO;
import com.zksr.promotion.domain.PrmBgRule;
import com.zksr.promotion.api.activity.dto.CbRuleDTO;
import com.zksr.promotion.domain.PrmCbRule;
import com.zksr.promotion.controller.rule.vo.PrmCbRuleRespVO;
import com.zksr.promotion.controller.rule.vo.PrmCbRuleSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 组合商品规则 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-12-31
*/
@Mapper
public interface PrmCbRuleConvert {

    PrmCbRuleConvert INSTANCE = Mappers.getMapper(PrmCbRuleConvert.class);

    PrmCbRuleRespVO convert(PrmCbRule prmCbRule);

    PrmCbRule convert(PrmCbRuleSaveReqVO prmCbRuleSaveReq);

    PageResult<PrmCbRuleRespVO> convertPage(PageResult<PrmCbRule> prmCbRulePage);

    List<CbRuleDTO> convertListDTO(List<PrmCbRule> cbRules);
}