package com.zksr.promotion.convert.couponBatch;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCouponBatchDtl;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchDtlRespVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchDtlSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 优惠券批量发送详情 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-12-05
*/
@Mapper
public interface PrmCouponBatchDtlConvert {

    PrmCouponBatchDtlConvert INSTANCE = Mappers.getMapper(PrmCouponBatchDtlConvert.class);

    PrmCouponBatchDtlRespVO convert(PrmCouponBatchDtl prmCouponBatchDtl);

    PrmCouponBatchDtl convert(PrmCouponBatchDtlSaveReqVO prmCouponBatchDtlSaveReq);

    PageResult<PrmCouponBatchDtlRespVO> convertPage(PageResult<PrmCouponBatchDtl> prmCouponBatchDtlPage);
}