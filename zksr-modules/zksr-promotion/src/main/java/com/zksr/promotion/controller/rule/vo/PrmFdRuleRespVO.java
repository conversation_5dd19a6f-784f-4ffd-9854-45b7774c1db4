package com.zksr.promotion.controller.rule.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.promotion.domain.PrmFdRule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;


/**
 * 满减活动规则对象 prm_fd_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ApiModel("满减活动规则 - prm_fd_rule Response VO")
public class PrmFdRuleRespVO {
    private static final long serialVersionUID = 1L;


    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @ApiModelProperty(value = "活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;


    /** 全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定） */
    @Excel(name = "全国或者本地(数据字典);1-全国商品可用", readConverterExp = "平=台商设定")
    @ApiModelProperty(value = "全国或者本地(数据字典);1-全国商品可用", required = true)
    private Integer funcScope;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 促销类型（数据字典）;SK-秒杀 FG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价 */
    @Excel(name = "促销类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "促销类型", required = true)
    private String prmNo;

    /** 促销单号 */
    @Excel(name = "促销单号")
    @ApiModelProperty(value = "促销单号", required = true)
    private String prmSheetNo;

    /** 活动名称 */
    @Excel(name = "活动名称")
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /** 启用时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "启用时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "启用时间")
    private Date effectTime;

    /** 启用人 */
    @Excel(name = "启用人")
    @ApiModelProperty(value = "启用人")
    private String effectMan;

    /** 活动开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "活动开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "活动开始时间")
    private Date startTime;

    /** 活动结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "活动结束时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "活动结束时间")
    private Date endTime;

    /** 商品适用范围(数据字典);0-所有商品可用（全场） 2-指定品类可用（品类），3-指定品牌可用（品牌），4-指定商品可用（商品） */
    @Excel(name = "商品适用范围(数据字典);0-所有商品可用", readConverterExp = "全=场")
    @ApiModelProperty(value = "商品适用范围(数据字典);0-所有商品可用", required = true)
    private Integer spuScope;

    /** 是否指定渠道参与;0-所有渠道参与 1-指定渠道参与  （指定渠道和指定门店二选一） */
    @Excel(name = "是否指定渠道参与;0-所有渠道参与 1-指定渠道参与  ", readConverterExp = "指=定渠道和指定门店二选一")
    @ApiModelProperty(value = "是否指定渠道参与;0-所有渠道参与 1-指定渠道参与  ")
    private Integer chanelScopeAllFlag;

    /** 是否指定门店参与;0-所有门店参与 1-指定门店参与   （指定渠道和指定门店二选一） */
    @Excel(name = "是否指定门店参与;0-所有门店参与 1-指定门店参与   ", readConverterExp = "指=定渠道和指定门店二选一")
    @ApiModelProperty(value = "是否指定门店参与;0-所有门店参与 1-指定门店参与   ")
    private Integer branchScopeAllFlag;

    /** 是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减) */
    @Excel(name = "是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减)")
    @ApiModelProperty(value = "是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减)")
    private Integer ladderFlag;

    /** 参与活动次数限制规则（数据字典）;仅特价限购，0-每日一次 1-仅一次  2-仅活动期间内首单 */
    @Excel(name = "参与活动次数限制规则", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "参与活动次数限制规则")
    private Integer timesRule;

    /** 活动优惠类型;仅满减，0-金额  1-数量 */
    @Excel(name = "活动优惠类型;仅满减，0-金额  1-数量")
    @ApiModelProperty(value = "活动优惠类型;仅满减，0-金额  1-数量")
    private Integer amtOrQty;

    /** 促销状态;0-未启用 1-启用 2-停用 3-已失效（待定，过期或者无商品可参与促销） */
    @Excel(name = "促销状态;0-未启用 1-启用 2-停用 3-已失效", readConverterExp = "待=定，过期或者无商品可参与促销")
    @ApiModelProperty(value = "促销状态;0-未启用 1-启用 2-停用 3-已失效")
    private Integer prmStatus;

    /** 素材信息 */
    @Excel(name = "素材信息")
    @ApiModelProperty(value = "素材信息")
    private MaterialApplyVO materialApplyVO;

    /** 活动说明 */
    @Excel(name = "活动说明")
    @ApiModelProperty(value = "活动说明")
    private String memo;

    @ApiModelProperty("门店列表")
    private List<PrmActivityWhiteOrBlackVO> branchIds;

    @ApiModelProperty("城市列表")
    private List<PrmActivityWhiteOrBlackVO> areaIds;

    @ApiModelProperty("渠道列表")
    private List<PrmActivityWhiteOrBlackVO> channelIds;

    @ApiModelProperty("促销范围列表")
    private List<PrmActivityWhiteOrBlackVO> spuIdList;

    @ApiModelProperty(value = "满减规则列表")
    private List<PrmFdRule> prmFdRuleList;

    @ApiModelProperty(value = "入驻商Id集合")
    private List<String> supplierIds;

}
