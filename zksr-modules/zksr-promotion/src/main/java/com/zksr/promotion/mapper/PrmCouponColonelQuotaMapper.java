package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.api.coupon.dto.ColonelQuotaDTO;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.controller.quota.vo.QuotaHistoryResponse;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCouponColonelQuota;
import com.zksr.promotion.controller.quota.vo.PrmCouponColonelQuotaPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * 业务员发券额度Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Mapper
public interface PrmCouponColonelQuotaMapper extends BaseMapperX<PrmCouponColonelQuota> {
    List<PrmCouponColonelQuota> selectPage(PrmCouponColonelQuotaPageReqVO reqVO);

    Long countPrmCouponColonelQuota(PrmCouponColonelQuotaPageReqVO pageReqVO);

    // 查询模板额度
    default PrmCouponColonelQuota selectTemplateQuota(String colonelId){
        return selectOne(
                Wrappers.lambdaQuery(PrmCouponColonelQuota.class)
                        .eq(PrmCouponColonelQuota::getColonelId, colonelId)
                        .eq(PrmCouponColonelQuota::getQuotaType, NumberPool.INT_ZERO)
        );
    }

    // 查询实例额度
    default PrmCouponColonelQuota selectInstanceQuota(String colonelId, Integer monthId){
        return selectOne(
                Wrappers.lambdaQuery(PrmCouponColonelQuota.class)
                        .eq(PrmCouponColonelQuota::getColonelId, colonelId)
                        .eq(PrmCouponColonelQuota::getMonthId, monthId)
                        .eq(PrmCouponColonelQuota::getQuotaType, NumberPool.INT_ONE)
        );
    }

    // 插入额度（模板或实例）
    Long insertQuota(PrmCouponColonelQuotaPageReqVO templateQuotaReqVO);

    // 更新模板额度
    Long updateTemplateQuota(@Param("colonelId") String colonelId, @Param("newQuota") BigDecimal newQuota);

    // 更新实例额度
    Long updateInstanceQuota(@Param("colonelId") String colonelId, @Param("monthId") Integer monthId, @Param("newQuota") BigDecimal newQuota, @Param("finishQuota") BigDecimal finishQuota);

    List<QuotaHistoryResponse> selectQuotaHistory(@Param("colonelId") String colonelId);

    ColonelQuotaDTO getColonelQuota(@Param("colonelId") Long colonelId,@Param("monthId") int monthId);

    List<CouponDTO> getAvailableCouponsForSalesperson();
}
