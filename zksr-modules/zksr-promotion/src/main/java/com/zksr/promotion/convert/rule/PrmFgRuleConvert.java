package com.zksr.promotion.convert.rule;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.FgRuleDTO;
import com.zksr.promotion.domain.PrmFgRule;
import com.zksr.promotion.controller.rule.vo.PrmFgRuleRespVO;
import com.zksr.promotion.controller.rule.vo.PrmFgRuleSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 满赠规则 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-13
*/
@Mapper
public interface PrmFgRuleConvert {

    PrmFgRuleConvert INSTANCE = Mappers.getMapper(PrmFgRuleConvert.class);

    PrmFgRuleRespVO convert(PrmFgRule prmFgRule);

    PrmFgRule convert(PrmFgRuleSaveReqVO prmFgRuleSaveReq);

    PageResult<PrmFgRuleRespVO> convertPage(PageResult<PrmFgRule> prmFgRulePage);

    List<FgRuleDTO> convertListDTO(List<PrmFgRule> prmFgRules);
}