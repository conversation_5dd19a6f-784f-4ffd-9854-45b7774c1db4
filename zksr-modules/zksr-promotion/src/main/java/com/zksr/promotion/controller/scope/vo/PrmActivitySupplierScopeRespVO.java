package com.zksr.promotion.controller.scope.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 促销活动入驻商适用范围对象 prm_activity_supplier_scope
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@ApiModel("促销活动入驻商适用范围 - prm_activity_supplier_scope Response VO")
public class PrmActivitySupplierScopeRespVO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @ApiModelProperty(value = "活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 1-白名单 0-黑名单 */
    @Excel(name = "1-白名单 0-黑名单")
    @ApiModelProperty(value = "1-白名单 0-黑名单")
    private Integer whiteOrBlack;

}
