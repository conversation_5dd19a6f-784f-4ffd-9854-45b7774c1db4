package com.zksr.promotion.controller.rule.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 买赠条件规则对象 prm_bg_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ApiModel("买赠条件规则 - prm_bg_rule分页 Request VO")
public class PrmBgRuleSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 买赠条件规则id */
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Long bgRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 买赠活动id */
    @Excel(name = "买赠活动id")
    @ApiModelProperty(value = "买赠活动id", required = true)
    private Long activityId;

    /** 触发赠送的数量 */
    @Excel(name = "触发赠送的数量")
    @ApiModelProperty(value = "触发赠送的数量")
    private Integer ruleQty;

    /** 赠品类型;0-商品 1-优惠券 */
    @Excel(name = "赠品类型;0-商品 1-优惠券")
    @ApiModelProperty(value = "赠品类型;0-商品 1-优惠券")
    private Integer giftType;

    /** sku_id */
    @Excel(name = "sku_id")
    @ApiModelProperty(value = "sku_id")
    private Long skuId;

    /** 返券模板id */
    @Excel(name = "返券模板id")
    @ApiModelProperty(value = "返券模板id")
    private Long couponTemplateId;

    /** 赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段 */
    @Excel(name = "赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    @ApiModelProperty(value = "赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    private Integer onceGiftQty;

    /** 总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段 */
    @Excel(name = "总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    @ApiModelProperty(value = "总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    private Integer totalGiftQty;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Integer status;

    /** 促销活动单位 */
    @Excel(name = "赠品商品单位大小", readConverterExp = "大中小")
    @ApiModelProperty(value = "赠品商品单位大小")
    private Integer giftSkuUnitType;

    /** 触发条件的单位 */
    @ApiModelProperty(value = "触发条件的单位", example = "1：小单位, 2：中单位, 3：大单位")
    private Integer ruleUnitType;
}
