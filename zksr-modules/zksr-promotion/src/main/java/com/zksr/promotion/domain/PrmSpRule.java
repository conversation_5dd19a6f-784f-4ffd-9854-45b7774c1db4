package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 特价活动规则对象 prm_sp_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@TableName(value = "prm_sp_rule")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmSpRule extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 特价活动规则id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 特价活动id */
    @Excel(name = "特价活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** SPU id */
    @Excel(name = "SPU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** sku_id */
    @Excel(name = "sku_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 促销价 */
    @Excel(name = "小单位促销价")
    private BigDecimal spPrice;

    /** 秒杀价 */
    @Excel(name = "中单位促销价")
    private BigDecimal midSpPrice;

    /** 秒杀价 */
    @Excel(name = "大单位促销价")
    private BigDecimal largeSpPrice;

    /** 单次限购数量 */
    @Excel(name = "单次限购数量")
    private Integer onceBuyLimit;

    /** 总限量 */
    @Excel(name = "总限量")
    private Integer totalLimitQty;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    private Integer status;

    /** 中单位限购数量 */
    @Excel(name = "中单位限购数量")
    @ApiModelProperty(value = "中单位限购数量")
    private Integer midLimit;

    /** 大单位限购数量 */
    @Excel(name = "大单位限购数量")
    @ApiModelProperty(value = "大单位限购数量")
    private Integer largeLimit;

    /** 中单位总限量 */
    @Excel(name = "中单位总限量")
    private Integer midTotalLimitQty;

    /** 大单位总限量 */
    @Excel(name = "大单位总限量")
    private Integer largeTotalLimitQty;
}
