package com.zksr.promotion.controller.apply.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 优惠券关联的使用范围, 领取范围中间对象 prm_coupon_scope_apply
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@ApiModel("优惠券关联的使用范围, 领取范围中间 - prm_coupon_scope_apply分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmCouponScopeApplyPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "优惠券模版ID")
    private Long scopeApplyId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty(value = "${comment}")
    private Long sysCode;

    /** 优惠券模版ID */
    @Excel(name = "优惠券模版ID")
    @ApiModelProperty(value = "优惠券模版ID")
    private Long couponTemplateId;


}
