package com.zksr.promotion.controller.room.vo;

import lombok.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 直播间对象 prm_live_room
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("直播间 - prm_live_room Response VO")
public class PrmLiveRoomRespVO {
    private static final long serialVersionUID = 1L;

    /** ID主键 */
    @ApiModelProperty(value = "版本号")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long id;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 直播ID */
    @Excel(name = "直播ID")
    @ApiModelProperty(value = "直播ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 直播间名称 */
    @Excel(name = "直播间名称")
    @ApiModelProperty(value = "直播间名称")
    private String name;

    /** 直播开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "直播开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "直播开始时间")
    private Date liveTime;

    /** 直播结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "直播结束时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "直播结束时间")
    private Date endTime;

    /** 直播模式,0：视频直播;1：伪直播 */
    @Excel(name = "直播模式,0：视频直播;1：伪直播")
    @ApiModelProperty(value = "直播模式,0：视频直播;1：伪直播")
    private Long activityType;

    /** 直播延时类型,1:超低延时(延时小于1秒) 2:普通延时(延时5-10秒) */
    @Excel(name = "直播延时类型,1:超低延时(延时小于1秒) 2:普通延时(延时5-10秒)")
    @ApiModelProperty(value = "直播延时类型,1:超低延时(延时小于1秒) 2:普通延时(延时5-10秒)")
    private Long liveMode;

    /** 视频布局,1:三分屏 2:纯视频 */
    @Excel(name = "视频布局,1:三分屏 2:纯视频")
    @ApiModelProperty(value = "视频布局,1:三分屏 2:纯视频")
    private Long liveLayout;

    /** 观看地址 */
    @Excel(name = "观看地址")
    @ApiModelProperty(value = "观看地址")
    private String viewUrl;

    /** 观看地址后缀 */
    @Excel(name = "观看地址后缀")
    @ApiModelProperty(value = "观看地址后缀")
    private String viewUrlPath;

    /** 是否开启回放自动上架,0:关闭 1:开启 */
    @Excel(name = "是否开启回放自动上架,0:关闭 1:开启")
    @ApiModelProperty(value = "是否开启回放自动上架,0:关闭 1:开启")
    private Long isReplayAutoOnlineEnable;

    /** 横屏直播的封面图 URL */
    @Excel(name = "横屏直播的封面图 URL")
    @ApiModelProperty(value = "横屏直播的封面图 URL")
    private String coverImage;

    /** 竖屏直播的封面图 URL */
    @Excel(name = "竖屏直播的封面图 URL")
    @ApiModelProperty(value = "竖屏直播的封面图 URL")
    private String verticalCoverImage;

    /** 直播伴侣地址 */
    @Excel(name = "直播伴侣地址")
    @ApiModelProperty(value = "直播伴侣地址")
    private String liveCompanionUrl;

    /** 是否删除：0-否，1-是 */
    @ApiModelProperty(value = "直播伴侣地址")
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号")
    private Long version;

}
