package com.zksr.promotion.controller.rule.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 组合商品规则对象 prm_cb_rule
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
@ApiModel("组合商品规则 - prm_cb_rule分页 Request VO")
public class PrmCbRuleSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 组合商品规则id */
    @ApiModelProperty(value = "上架展示分类ID")
    private Long cbRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @ApiModelProperty(value = "活动id", required = true)
    private Long activityId;

    /** 组合商品id */
    @Excel(name = "组合商品id")
    @ApiModelProperty(value = "组合商品id", required = true)
    private Long spuCombineId;

    /** 上架展示分类ID */
    @Excel(name = "上架展示分类ID")
    @ApiModelProperty(value = "上架展示分类ID", required = true)
    private Long shelfClassId;

}
