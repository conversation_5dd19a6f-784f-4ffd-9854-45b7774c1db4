package com.zksr.promotion.service;

import javax.validation.*;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import com.zksr.promotion.controller.rule.dto.PrmSkRuleDTO;
import com.zksr.promotion.domain.PrmSpRule;
import com.zksr.promotion.controller.rule.vo.PrmSpRulePageReqVO;
import com.zksr.promotion.controller.rule.vo.PrmSpRuleSaveReqVO;
import com.zksr.promotion.domain.excel.PrmSpRuleImportExcel;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 特价活动规则Service接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
public interface IPrmSpRuleService {

    /**
     * 新增特价活动规则
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    Long insertPrmSpRule(@Valid PrmSkRuleDTO createReqVO);

    /**
     * 编辑特价促销活动
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    Boolean editSpRuleInfo(PrmSkRuleDTO updateReqVO);

    /**
     * 删除特价活动规则
     *
     * @param spRuleId 特价活动规则id
     */
    public void deletePrmSpRule(Long spRuleId);

    /**
     * 批量删除特价活动规则
     *
     * @param spRuleIds 需要删除的特价活动规则主键集合
     * @return 结果
     */
    public void deletePrmSpRuleBySpRuleIds(Long[] spRuleIds);

    /**
     * 获得特价活动规则
     *
     * @param spRuleId 特价活动规则id
     * @return 特价活动规则
     */
    public PrmSpRule getPrmSpRule(Long spRuleId);

    /**
     * 获得特价活动规则分页
     *
     * @param pageReqVO 分页查询
     * @return 特价活动规则分页
     */
    PageResult<PrmSpRule> getPrmSpRulePage(PrmSpRulePageReqVO pageReqVO);


    /**
     * 获得特价活动规则
     *
     * @param activityId
     * @return 特价活动规则
     */
    PrmActivityRespVO getSpRuleInfo(@RequestParam("activityId") Long activityId);

    Map<String, Object> importPrmSpRuleData(List<PrmSpRuleImportExcel> prmSpRuleList);
}
