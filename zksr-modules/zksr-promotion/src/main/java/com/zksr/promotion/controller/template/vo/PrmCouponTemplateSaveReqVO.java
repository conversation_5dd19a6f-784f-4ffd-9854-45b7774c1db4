package com.zksr.promotion.controller.template.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateRepeatRuleSaveReqVO;
import com.zksr.promotion.controller.template.dto.SpuScopeOrReceiveScopeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 优惠券模板对象 prm_coupon_template
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Data
@ApiModel("优惠券模板 - prm_coupon_template分页 Request VO")
public class PrmCouponTemplateSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 优惠券模板id */
    @ApiModelProperty(value = "优惠券模版ID")
    private Long couponTemplateId;

    /** 优惠券模板名称 */
    @NotNull(message = "优惠券名称必填")
    @NotEmpty(message = "优惠券名称必填")
    @Excel(name = "优惠券模板名称")
    @ApiModelProperty(value = "优惠券模板名称", required = true)
    private String couponName;

    /** 入驻商ID */
    @Excel(name = "入驻商ID")
    @NotNull(message = "入驻商必填")
    private Long supplierId;

    @NotNull
    @Excel(name = "全国或本地", readConverterExp = "0-全部商品可用,1-本地商品, 2-全国商品")
    @ApiModelProperty(value = "0-全部商品可用,1-全国商品, 2-本地商品", required = true)
    private Integer funcScope;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=未发布,2-下架")
    @ApiModelProperty(value = "0=正常,1未发布,2-下架")
    private Integer status;

    @NotNull(message = "优惠券显示领取开始时间不能为空")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "优惠券可领取开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "优惠券可领取开始时间", required = true)
    private Date templateStartDate;

    @NotNull(message = "优惠券显示领取结束时间不能为空")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "优惠券可领取结束时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "优惠券可领取结束时间", required = true)
    private Date templateEndDate;

    /** 商品适用范围(数据字典);0-所有商品可用（全场券） 1-指定入驻商可用（入驻商券），2-指定品类可用（品类券），3-指定品牌可用（品牌券），4-指定商品可用（商品券） 多个范围用逗号分隔*/
    @NotNull(message = "请选择商品使用范围")
    @Excel(name = "商品适用范围(数据字典);0-所有商品可用 多个范围用逗号分隔")
    @ApiModelProperty(value = "数据字典coupon_spu_scope;0-所有商品可用（全场券） 1-指定入驻商可用（入驻商券），2-指定品类可用（品类券），3-指定品牌可用（品牌券），4-指定商品可用（商品券） 多个范围用逗号分隔", required = true)
    private Integer spuScope;

    @NotNull(message = "请选择领取范围")
    @Excel(name = "领取范围", readConverterExp = "0-全部可领取,1-指定渠道,2-指定城市,3-指定门店 多个范围用逗号分隔")
    @ApiModelProperty(value = "数据字典coupon_receive_scope领取范围; 0-全部可领取,1-指定渠道,2-指定城市,3-指定门店 多个范围用逗号分隔", required = true)
    private Integer receiveScope;

    @NotNull(message = "[discountType] 优惠券类型不能未空")
    @Excel(name = "优惠类型(数据字典);0-满减券  1-折扣券")
    @ApiModelProperty(value = "优惠类型(数据字典);0-满减券  1-折扣券", required = true)
    private Integer discountType;

    @NotNull(message = "优惠券启动金额不能未空")
    @Excel(name = "优惠券启用金额")
    @DecimalMin(value = "0")
    @ApiModelProperty(value = "优惠券启用金额")
    private BigDecimal triggerAmt;

    @DecimalMin(value = "0.01")
    @Excel(name = "优惠金额")
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmt;

    @Excel(name = "优惠折扣;折，如99折记9.9折(折扣券设置)")
    @ApiModelProperty(value = "优惠折扣;折，如99折记9.9折(折扣券设置)")
    private BigDecimal discountPercent;

    @Excel(name = "最高折扣多少元")
    @ApiModelProperty(value = "最多优惠;折扣券设置，例如，折扣上限为 20 元，当使用 8 折优惠券，订单金额为 1000 元时，最高只可折扣 20 元，而非 80  元。")
    private BigDecimal discountLimitAmt;

    @NotNull
    @Excel(name = "有效期类型(数据字典);0-固定日期1-领取之后")
    @ApiModelProperty(value = "有效期类型(数据字典);0-固定日期1-领取之后", required = true)
    private Integer expirationType;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "有效期开始时间;当expiration_type=0设定", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "有效期开始时间;当expiration_type=0设定", required = true)
    private Date expirationDateStart;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "有效期结束时间;当expiration_type=0设定", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "有效期结束时间;当expiration_type=0设定")
    private Date expirationDateEnd;

    /** 领取优惠券后禁用天数（默认值为0）;当expiration_type=1设定 ，第x天至第y天有效，本值为x */
    @Excel(name = "领取优惠券后禁用天数", readConverterExp = "默=认值为0")
    @ApiModelProperty(value = "领取优惠券后禁用天数")
    private Integer disableDays;

    /** 领取优惠券后有效期天数;当expiration_type=1设定 ，第x天至第y天有效，本值为y */
    @Excel(name = "领取优惠券后有效期天数;当expiration_type=1设定 ，第x天至第y天有效，本值为y")
    @ApiModelProperty(value = "领取优惠券后有效期天数;当expiration_type=1设定 ，第x天至第y天有效，本值为y")
    private Integer expireDays;

    @NotNull
    @Excel(name = "领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券")
    @ApiModelProperty(value = "领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券", required = true)
    private Integer receiveType;

    /** 是否分摊;用来计算订单表的discount_amt1,discount_amt2, 需要传输给ERP */
    @Excel(name = "是否分摊;用来计算订单表的discount_amt1,discount_amt2, 需要传输给ERP")
    @ApiModelProperty(value = "是否分摊;用来计算订单表的discount_amt1,discount_amt2, 需要传输给ERP", required = true, example = "0")
    private Integer costFlag;

    /** 优惠券数量 （0-不限制数量） */
    @NotNull
    @Min(value = 0)
    @Excel(name = "优惠券数量 ", readConverterExp = "0=-不限制数量")
    @ApiModelProperty(value = "优惠券数量 ")
    private Integer couponQty;

    /** 每人限领数量（0-不限制数量） */
    @NotNull
    @Min(value = 0)
    @Excel(name = "每人限领数量", readConverterExp = "0=-不限制数量")
    @ApiModelProperty(value = "每人限领数量")
    private Integer limit;

    @Excel(name = "不同类型优惠券是否排它", readConverterExp = "0=否,1=是")
    @ApiModelProperty(value = "不同类型优惠券是否排它 0-否,1-是", required = true, example = "1")
    private String excludable;

    /** 是否重复;0-不重复（默认） 1-重复（需要设定重复规则(扩展表)，生成后面的重复模板，生成的重复模板repeat_flag为不重复，根据设定的重复次数生成对应条数的模板） */
    @Excel(name = "是否重复;0-不重复", readConverterExp = "0-不重复,1-重复")
    @ApiModelProperty(value = "是否重复;0-不重复,1-重复", required = true, example = "0")
    private String repeatFlag;

    /** 父重复模板id;由重复模板生成的，记录重复模板的id */
    @Excel(name = "父重复模板id;由重复模板生成的，记录重复模板的id")
    @ApiModelProperty(value = "父重复模板id;由重复模板生成的，记录重复模板的id")
    private Long repeatPid;

    /** 重复的第几个 */
    @Excel(name = "重复的第几个")
    @ApiModelProperty(value = "重复的第几个")
    private Integer repeatSeq;

    /** 优惠券模板说明 */
    @Excel(name = "优惠券模板说明")
    @ApiModelProperty(value = "优惠券模板说明")
    private String memo;

    /** 重复规则 */
    @ApiModelProperty(value = "重复规则")
    private PrmCouponTemplateRepeatRuleSaveReqVO repeatRule;


    /**商品适用范围集合 */
    @ApiModelProperty(value = "商品适用范围集合")
    private List<SpuScopeOrReceiveScopeVO> spuScopeList;

    /**商品领取范围集合 */
    @ApiModelProperty(value = "商品领取范围集合")
    private List<SpuScopeOrReceiveScopeVO> receiveScopeList;

    @Excel(name = "同类型优惠券是否排它", readConverterExp = "0=否,1=是")
    @ApiModelProperty(value = "同类型优惠券是否排它 0-否,1-是", required = true, example = "1")
    private Integer sameTypeExcludable;

//    @NotEmpty(message = "入驻商id列表不能为空")
    @ApiModelProperty(value = "入驻商id列表")
    private List<Long> supplierIds;

}
