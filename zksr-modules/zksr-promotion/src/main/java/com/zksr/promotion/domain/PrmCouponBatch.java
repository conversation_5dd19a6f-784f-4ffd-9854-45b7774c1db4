package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 优惠券批量发送对象 prm_coupon_batch
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@TableName(value = "prm_coupon_batch")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmCouponBatch extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 优惠券批量发送id */
    @TableId
    private Long couponBatchId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定） */
    @Excel(name = "全国或者本地(数据字典);1-全国商品可用", readConverterExp = "平=台商设定")
    private Long funcScope;

    /** 优惠券模板数量 */
    @Excel(name = "优惠券模板数量")
    private Long couponTemplateQty;

    /** 门店数量 */
    @Excel(name = "门店数量")
    private Long branchQty;

    /** 门店发放数量 */
    @Excel(name = "门店发放数量")
    private Long branchSendQty;

    /** 总计发券数量 */
    @Excel(name = "总计发券数量")
    private Long totalQty;

    /** 生效类型：0-定时生效，1-立即生效 */
    @Excel(name = "生效类型：0-定时生效，1-立即生效")
    private Integer validType;

    /** 生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生效时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validTime;

    /** 实际发券成功数量 */
    @Excel(name = "实际发券成功数量")
    private Long realSendQty;

    /** 执行状态 0-未执行，1-已执行 */
    @Excel(name = "执行状态 0-未执行，1-已执行")
    private Integer taskExecuteStatus;

    /** 审核状态 0-未审核，1-已审核 */
    @Excel(name = "审核状态 0-未审核，1-已审核")
    private Integer auditStatus;

    /** 删除标志（0代表存在 2代表删除） */
    @Excel(name = "删除标志（0代表存在 2代表删除）")
    private String delFlag;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;


}
