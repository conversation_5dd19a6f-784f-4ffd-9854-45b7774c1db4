package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 秒杀规则对象 prm_sk_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@TableName(value = "prm_sk_rule")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmSkRule extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 秒杀规则id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long skRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** SPU id */
    @Excel(name = "SPU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** SKU id */
    @Excel(name = "SKU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 单次限量 */
    @Excel(name = "单次限量")
    private Integer onceLimit;

    /** 秒杀库存 */
    @Excel(name = "秒杀库存")
    private Integer seckillStock;

    /** 秒杀价 */
    @Excel(name = "小单位秒杀价")
    private BigDecimal seckillPrice;

    /** 秒杀价 */
    @Excel(name = "中单位秒杀价")
    private BigDecimal midSeckillPrice;

    /** 秒杀价 */
    @Excel(name = "大单位秒杀价")
    private BigDecimal largeSeckillPrice;

    /** 状态：0-停用，1-启用 */
    @Excel(name = "状态：0-停用，1-启用")
    private Integer skStatus;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 中单位限购数量 */
    @Excel(name = "中单位限购数量")
    @ApiModelProperty(value = "中单位限购数量")
    private Integer midLimit;

    /** 大单位限购数量 */
    @Excel(name = "大单位限购数量")
    @ApiModelProperty(value = "大单位限购数量")
    private Integer largeLimit;

    /** 中单位秒杀库存 */
    @Excel(name = "中单位秒杀库存")
    private Integer seckillMidStock;

    /** 大单位秒杀库存 */
    @Excel(name = "大单位秒杀库存")
    private Integer seckillLargeStock;
}
