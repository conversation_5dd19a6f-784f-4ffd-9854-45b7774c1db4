package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 买赠条件规则对象 prm_bg_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@TableName(value = "prm_bg_rule")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmBgRule extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 买赠条件规则id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long bgRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 买赠活动id */
    @Excel(name = "买赠活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 触发赠送的数量 */
    @Excel(name = "触发赠送的数量")
    private Integer ruleQty;

    /** 赠品类型;0-商品 1-优惠券 */
    @Excel(name = "赠品类型;0-商品 1-优惠券")
    private Integer giftType;

    /** sku_id */
    @Excel(name = "sku_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 返券模板id */
    @Excel(name = "返券模板id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    /** 赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段 */
    @Excel(name = "赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    private Integer onceGiftQty;

    /** 总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段 */
    @Excel(name = "总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    private Integer totalGiftQty;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    private Integer status;

    /** 促销活动单位 */
    @Excel(name = "赠品商品单位大小", readConverterExp = "大中小")
    private Integer giftSkuUnitType;

    /** 触发条件的单位 */
    @ApiModelProperty(value = "触发条件的单位", example = "1：小单位, 2：中单位, 3：大单位")
    @Excel(name = "触发条件的单位 1：小单位, 2：中单位, 3：大单位")
    private Integer ruleUnitType;
}
