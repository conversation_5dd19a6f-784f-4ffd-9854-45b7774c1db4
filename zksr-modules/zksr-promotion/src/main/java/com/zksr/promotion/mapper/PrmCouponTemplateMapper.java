package com.zksr.promotion.mapper;

import cn.hutool.core.date.CalendarUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.enums.CouponReceiveType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCouponTemplate;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplatePageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.Calendar;
import java.util.Collection;
import java.util.List;
import java.util.Objects;


/**
 * 优惠券模板Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Mapper
public interface PrmCouponTemplateMapper extends BaseMapperX<PrmCouponTemplate> {
    default PageResult<PrmCouponTemplate> selectPage(PrmCouponTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmCouponTemplate>()
                    .eq(PrmCouponTemplate::getDelFlag, StringPool.ZERO)
                    .eqIfPresent(PrmCouponTemplate::getCouponTemplateId, reqVO.getCouponTemplateId())
                    .eqIfPresent(PrmCouponTemplate::getSysCode, reqVO.getSysCode())
                    .likeIfPresent(PrmCouponTemplate::getCouponName, reqVO.getCouponName())
                    .eqIfPresent(PrmCouponTemplate::getFuncScope, reqVO.getFuncScope())
                    .eqIfPresent(PrmCouponTemplate::getStatus, reqVO.getStatus())
                    .eqIfPresent(PrmCouponTemplate::getTemplateStartDate, reqVO.getTemplateStartDate())
                    .eqIfPresent(PrmCouponTemplate::getTemplateEndDate, reqVO.getTemplateEndDate())
                    .eqIfPresent(PrmCouponTemplate::getSpuScope, reqVO.getSpuScope())
                    .eqIfPresent(PrmCouponTemplate::getReceiveScope, reqVO.getReceiveScope())
                    .eqIfPresent(PrmCouponTemplate::getDiscountType, reqVO.getDiscountType())
                    .eqIfPresent(PrmCouponTemplate::getExpirationType, reqVO.getExpirationType())
                    .eqIfPresent(PrmCouponTemplate::getReceiveType, reqVO.getReceiveType())
                    .eqIfPresent(PrmCouponTemplate::getCostFlag, reqVO.getCostFlag())
                    .eqIfPresent(PrmCouponTemplate::getExcludable, reqVO.getExcludable())
                    .eqIfPresent(PrmCouponTemplate::getRepeatFlag, reqVO.getRepeatFlag())
                    .eqIfPresent(PrmCouponTemplate::getRepeatPid, reqVO.getRepeatPid())
                    .eqIfPresent(PrmCouponTemplate::getRepeatSeq, reqVO.getRepeatSeq())
                    .applyScope(reqVO.getParams())
                .orderByDesc(PrmCouponTemplate::getCouponTemplateId));
    }

    /**
     * 获取指定领取类型有效优惠券
     * @param sysCode           平台编号
     * @param couponReceiveType 领取类型
     * @return
     */
    default List<PrmCouponTemplate> selectValidListBySysCodeAndReceiveType(Long sysCode, Object... couponReceiveType) {
        return selectList(
                Wrappers.lambdaQuery(PrmCouponTemplate.class)
                        .eq(PrmCouponTemplate::getSysCode, sysCode)
                        .in(Objects.nonNull(couponReceiveType), PrmCouponTemplate::getReceiveType, couponReceiveType)
                        .ge(PrmCouponTemplate::getTemplateEndDate, DateUtil.date())
                        .eq(PrmCouponTemplate::getStatus, NumberPool.INT_ZERO)
        );
    }

    default List<PrmCouponTemplate> selectValidListByFuncScopeAndReceiveType(Long sysCode,Integer funcScope, Object... couponReceiveType) {
        return selectList(
                Wrappers.lambdaQuery(PrmCouponTemplate.class)
                        .eq(PrmCouponTemplate::getSysCode, sysCode)
                        .eq(PrmCouponTemplate::getFuncScope, funcScope)
                        .in(Objects.nonNull(couponReceiveType), PrmCouponTemplate::getReceiveType, couponReceiveType)
                        .ge(PrmCouponTemplate::getTemplateEndDate, DateUtil.date())
                        .eq(PrmCouponTemplate::getStatus, NumberPool.INT_ZERO)
        );
    }

    /**
     * 获取指定领取类型有效优惠券
     * @param supplierId        入驻商ID
     * @param couponReceiveType 领取类型
     * @return
     */
    default List<PrmCouponTemplate> selectValidListBySupplierAndReceiveType(Long supplierId, Object... couponReceiveType) {
        return selectList(
                Wrappers.lambdaQuery(PrmCouponTemplate.class)
                        .eq(PrmCouponTemplate::getSupplierId, supplierId)
                        .in(Objects.nonNull(couponReceiveType), PrmCouponTemplate::getReceiveType, couponReceiveType)
                        .ge(PrmCouponTemplate::getTemplateEndDate, DateUtil.date())
                        .eq(PrmCouponTemplate::getStatus, NumberPool.INT_ZERO)
        );
    }

    /**
     * 获取3天内过期有效
     * @param minId
     * @return
     */
    default List<PrmCouponTemplate> selectTotalStockCouponTemplateList(Long minId) {
        return selectList(
                Wrappers.lambdaQuery(PrmCouponTemplate.class)
                        // 15天以前过期都纳入统计
                        .ge(PrmCouponTemplate::getExpirationDateEnd, DateUtil.offsetDay(DateUtil.date(), -15))
                        .gt(PrmCouponTemplate::getCouponTemplateId, minId)
                        .in(PrmCouponTemplate::getReceiveType,
                                CouponReceiveType.NORMAL.getType(),
                                CouponReceiveType.PAYMENT.getType(),
                                CouponReceiveType.POINT.getType(),
                                CouponReceiveType.REGISTER.getType(),
                                CouponReceiveType.SALESMAN.getType(),
                                CouponReceiveType.BATCH.getType()
                        )
                        .orderByAsc(PrmCouponTemplate::getCouponTemplateId)
                        .last("LIMIT 50")
        );
    }

    List<PrmCouponTemplateRespVO> selectPageExt(PrmCouponTemplatePageReqVO req);
}
