package com.zksr.promotion.controller.rule.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 特价活动规则对象 prm_sp_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ApiModel("特价活动规则 - prm_sp_rule Response VO")
public class PrmSpRuleRespVO {
    private static final long serialVersionUID = 1L;

    /** 特价活动规则id */
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 特价活动id */
    @Excel(name = "特价活动id")
    @ApiModelProperty(value = "特价活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** SPU id */
    @Excel(name = "SPU id")
    @ApiModelProperty(value = "SPU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** sku_id */
    @Excel(name = "sku_id")
    @ApiModelProperty(value = "sku_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 小单位促销价 */
    @Excel(name = "小单位促销价")
    @ApiModelProperty(value = "小单位促销价")
    private BigDecimal spPrice;

    /** 促销价 */
    @Excel(name = "中单位促销价")
    @ApiModelProperty(value = "中单位促销价")
    private BigDecimal midSpPrice;

    /** 促销价 */
    @Excel(name = "大单位促销价")
    @ApiModelProperty(value = "大单位促销价")
    private BigDecimal largeSpPrice;

    /** 单次限购数量 */
    @Excel(name = "单次限购数量")
    @ApiModelProperty(value = "单次限购数量")
    private Integer onceBuyLimit;

    /** 总限量 */
    @Excel(name = "总限量")
    @ApiModelProperty(value = "总限量")
    private Integer totalLimitQty;

    /** 总限量（中单位） */
    @Excel(name = "总限量（中单位）")
    @ApiModelProperty(value = "总限量（中单位）")
    private Integer midTotalLimitQty;

    /** 总限量（大单位） */
    @Excel(name = "总限量（大单位）")
    @ApiModelProperty(value = "总限量（大单位）")
    private Integer largeTotalLimitQty;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Integer status;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String spuName;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    private String supplierName;

    /** spu编号 */
    @Excel(name = "spu编号")
    private String spuNo;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典（sys_prdt_unit）")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long unit;


    /** 国际条码 */
    @Excel(name = "国际条码")
    private String barcode;

    /** 小单位销售价 */
    @Excel(name = "小单位销售价")
    private BigDecimal markPrice;

    /** 中单位销售价 */
    @Excel(name = "中单位销售价")
    private BigDecimal midMarkPrice;

    /** 大单位销售价 */
    @Excel(name = "大单位销售价")
    private BigDecimal largeMarkPrice;

    /** 库存数量 */
    @Excel(name = "库存数量")
    private Integer stock;

    /** 中单位限购数量 */
    @Excel(name = "中单位限购数量")
    @ApiModelProperty(value = "中单位限购数量")
    private Integer midLimit;

    /** 大单位限购数量 */
    @Excel(name = "大单位限购数量")
    @ApiModelProperty(value = "大单位限购数量")
    private Integer largeLimit;

    @ApiModelProperty(value = "中单位换算数量（换算成最小单位）")
    private BigDecimal midSize;

    @ApiModelProperty(value = "大单位换算数量（换算成最小单位）")
    private BigDecimal largeSize;

    @ApiModelProperty(value = "最小单位-数据字典（sys_prdt_unit）")
    private Long minUnit;

    @ApiModelProperty(value = "中单位-数据字典（sys_prdt_unit）")
    private Long midUnit;

    @ApiModelProperty(value = "大单位-数据字典（sys_prdt_unit）")
    private Long largeUnit;

    /** 总已售数量（小单位） */
    @Excel(name = "总已售数量（小单位）")
    @ApiModelProperty(value = "总已售数量（小单位）")
    private Integer totalSaleNum;

    /** 总已售数量（中单位） */
    @Excel(name = "总已售数量（中单位）")
    @ApiModelProperty(value = "总已售数量（中单位）")
    private Integer midTotalSaleNum;

    /** 总已售数量（大单位） */
    @Excel(name = "总已售数量（大单位）")
    @ApiModelProperty(value = "总已售数量（大单位）")
    private Integer largeTotalSaleNum;
}
