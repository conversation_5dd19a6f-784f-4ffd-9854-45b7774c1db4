package com.zksr.promotion.controller.coupon;

import javax.validation.Valid;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.promotion.api.coupon.dto.CouponNormalCacheDTO;
import com.zksr.promotion.api.coupon.dto.CouponReceiveDTO;
import com.zksr.promotion.api.coupon.dto.CouponReceiveStatusDTO;
import com.zksr.promotion.mq.PromotionMqProducer;
import com.zksr.promotion.service.IPromotionCacheService;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.domain.PrmCoupon;
import com.zksr.promotion.service.IPrmCouponService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.promotion.controller.coupon.vo.PrmCouponPageReqVO;
import com.zksr.promotion.controller.coupon.vo.PrmCouponSaveReqVO;
import com.zksr.promotion.controller.coupon.vo.PrmCouponRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.promotion.enums.ErrorCodeConstants.RECEIVE_MQ_CHANNEL_ERR;

/**
 * 优惠券记录Controller
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Api(tags = "管理后台 - 优惠券记录接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/coupon")
public class PrmCouponController {

    @Autowired
    private PromotionMqProducer promotionMqProducer;

    @Autowired
    private IPrmCouponService prmCouponService;

    /**
     * 获取优惠券记录详细信息
     */
    @ApiOperation(value = "获得优惠券记录详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{couponId}")
    public CommonResult<PrmCouponRespVO> getInfo(@PathVariable("couponId") Long couponId) {
        PrmCoupon prmCoupon = prmCouponService.getPrmCoupon(couponId);
        return success(HutoolBeanUtils.toBean(prmCoupon, PrmCouponRespVO.class));
    }

    /**
     * 分页查询优惠券记录
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得优惠券记录分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(dcAlias = "pct", dcFieldAlias = SystemConstants.DC_ID, supplierAlias = "`pct`")
    public CommonResult<PageResult<PrmCouponRespVO>> getPage(@Valid PrmCouponPageReqVO pageReqVO) {
        PageResult<PrmCouponRespVO> pageResult = prmCouponService.getPrmCouponPage(pageReqVO);
        return success(pageResult);
    }

    /**
     * 作废优惠券
     */
    @ApiOperation(value = "停用优惠券记录", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @Log(title = "优惠券记录", businessType = BusinessType.DELETE)
    @RequiresPermissions(Permissions.DISABLE)
    @PutMapping(value = "/disable")
    public CommonResult<Boolean> disable(@RequestParam("couponId") @ApiParam(name = "优惠券ID", value = "couponId") Long couponId) {
        prmCouponService.disableBatch(ListUtil.toList(couponId));
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 用户主动领取优惠券
     */
    @PostMapping("/receiveCoupon2")
    CommonResult<List<CouponReceiveStatusDTO>> receiveCoupon2(@RequestBody CouponReceiveDTO couponReceive){
        promotionMqProducer.sendCouponReceiveEvent(couponReceive,true);
        return CommonResult.success(new ArrayList<CouponReceiveStatusDTO>());
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:coupon:add";
        /** 编辑 */
        public static final String EDIT = "promotion:coupon:edit";
        /** 删除 */
        public static final String DELETE = "promotion:coupon:remove";
        /** 列表 */
        public static final String LIST = "promotion:coupon:list";
        /** 查询 */
        public static final String GET = "promotion:coupon:query";
        /** 停用 */
        public static final String DISABLE = "promotion:coupon:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:coupon:enable";
    }
}
