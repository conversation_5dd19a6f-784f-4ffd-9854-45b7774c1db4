package com.zksr.promotion.controller.activity.vo;

import com.zksr.promotion.controller.rule.vo.PrmActivityWhiteOrBlackVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 促销活动关联数据VO
 * @Author: liuxingyu
 * @Date: 2024/5/21 15:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrmActivityScopeVO {
    @ApiModelProperty("促销ID")
    private Long activityId;

    @ApiModelProperty(value = "商品适用范围(数据字典);0-所有商品可用（全场） 2-指定品类可用（品类），3-指定品牌可用（品牌），4-指定商品可用（商品）")
    private Integer spuScope;

    @ApiModelProperty("区域城市列表")
    private List<PrmActivityWhiteOrBlackVO> areaIdList;

    @ApiModelProperty("门店列表")
    private List<PrmActivityWhiteOrBlackVO> branchIdList;

    @ApiModelProperty("渠道列表")
    private List<PrmActivityWhiteOrBlackVO> channelIdList;

    @ApiModelProperty("spu列表")
    private List<PrmActivityWhiteOrBlackVO> spuIdList;
}
