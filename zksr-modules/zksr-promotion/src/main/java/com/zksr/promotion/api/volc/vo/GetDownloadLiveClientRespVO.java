package com.zksr.promotion.api.volc.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.volcengine.model.livesaas.response.GetDownloadLiveClientAPIResponse;
import com.volcengine.model.response.ResponseMetadata;
import lombok.Data;

/**
 * 获取直播伴侣一键开播地址返回实体类
 * @Author: chenyj8
 */
@Data
public class GetDownloadLiveClientRespVO {

    ResponseMetadataVO responseMetadata;
    GetDownloadLiveClientAPIResponseBody result;

    @Data
    public static class GetDownloadLiveClientAPIResponseBody {
        //火山API字段没有按驼峰
        String Url;
    }

}
