package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 优惠券模板-下单返券规则（待讨论）对象 prm_coupon_template_return_rule
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@TableName(value = "prm_coupon_template_return_rule")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmCouponTemplateReturnRule extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 优惠券模板重复规则 */
    @TableId
    private Long couponTemplateReturnRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 优惠券模板id */
    @Excel(name = "优惠券模板id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    /** 下单返优惠券条件（数据字典）;0-新会员下单  1-全场满返  2-购买指定入驻商商品满返  3-购买指定管理分类商品满返  4-购买指定品牌商品满返  5-购买指定商品满返 */
    @Excel(name = "下单返优惠券条件", readConverterExp = "数=据字典")
    private Integer conditionType;

    /** 满多少钱返券 */
    @Excel(name = "满多少钱返券")
    private BigDecimal conditionAmt;

    /** 条件之适用ids */
    @Excel(name = "条件之适用ids")
    private String conditionApplyIds;

}
