package com.zksr.promotion.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LFUCache;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zksr.common.core.enums.CouponStateEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisCouponConstants;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.*;
import com.zksr.promotion.api.coupon.vo.CouponPageReqVO;
import com.zksr.promotion.controller.coupon.vo.PrmCouponPageReqVO;
import com.zksr.promotion.controller.coupon.vo.PrmCouponRespStatisticsVO;
import com.zksr.promotion.controller.coupon.vo.PrmCouponRespVO;
import com.zksr.promotion.domain.PrmCoupon;
import com.zksr.promotion.domain.PrmCouponBatch;
import com.zksr.promotion.domain.PrmCouponLog;
import com.zksr.promotion.mapper.PrmCouponBatchMapper;
import com.zksr.promotion.mapper.PrmCouponLogMapper;
import com.zksr.promotion.mapper.PrmCouponMapper;
import com.zksr.promotion.service.IPrmCouponService;
import com.zksr.promotion.service.IPromotionCacheService;
import com.zksr.promotion.utils.CouponScopeUtil;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderDtlVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 优惠券记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Slf4j
@Service
public class PrmCouponServiceImpl implements IPrmCouponService {

    @Autowired
    private PrmCouponMapper prmCouponMapper;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PrmCouponLogMapper couponLogMapper;

    @Autowired
    private PrmCouponBatchMapper prmCouponBatchMapper;

    @Autowired
    private CouponApi couponApi;
    @Resource
    private OrderApi orderApi;

    /**
     * 获得优惠券记录
     *
     * @param couponId 优惠券记录id
     * @return 优惠券记录
     */
    @Override
    public PrmCoupon getPrmCoupon(Long couponId) {
        return prmCouponMapper.selectById(couponId);
    }

    public CouponDTO getPrmCouponDto(Long couponId) {
        PrmCoupon prmCoupon = prmCouponMapper.selectById(couponId);
        CouponDTO couponDTO = new CouponDTO();
        BeanUtils.copyProperties(prmCoupon,couponDTO);
        return couponDTO;
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrmCouponRespVO> getPrmCouponPage(PrmCouponPageReqVO pageReqVO) {
        Page<PrmCouponRespVO> page = PageHelper.startPage(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        List<PrmCouponRespVO> couponRespVOS = prmCouponMapper.selectList(pageReqVO);

        LFUCache<Long, BranchDTO> branchCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, PrmCouponBatch> batchCache = CacheUtil.newLFUCache(0);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        PrmCouponRespStatisticsVO respStatisticsVO = new PrmCouponRespStatisticsVO();
        respStatisticsVO.setTotalCoupon(couponRespVOS.size());
//        log.info("优惠券记录,{}", JsonUtils.toJsonString(couponRespVOS));
        // 1. 收集所有已使用优惠券的订单号，去重
        Set<String> usedOrderNos = couponRespVOS.stream()
                .filter(vo -> CouponStateEnum.USED.getState().equals(vo.getState()))
                .map(PrmCouponRespVO::getRelateOrderNo)
                .filter(StringUtils::isNotEmpty)
                .collect(java.util.stream.Collectors.toSet());
        // 2. 批量查询订单明细
        if (!usedOrderNos.isEmpty()) {
            CommonResult<List<TrdSupplierOrderDtlVO>> allOrderDetailsResult = orderApi.getSupplierOrderDtlByOrderNos(new java.util.ArrayList<>(usedOrderNos));
            if (allOrderDetailsResult.isSuccess() && allOrderDetailsResult.getData() != null) {
                List<TrdSupplierOrderDtlVO> countList = allOrderDetailsResult.getData();
                if(CollectionUtils.isNotEmpty(countList)){
                    java.math.BigDecimal couponAmt = countList.stream().map(t -> t.getCouponAmt()).reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);
                    java.math.BigDecimal useCouponOrderMoney = countList.stream().map(t -> t.getUseCouponOrderMoney()).reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);
                    //合计优惠金额计算修改，产品逻辑：合计金额优惠=当前页面所有已使用优惠券扣减金额合计
                    respStatisticsVO.setTotalCouponMoney(respStatisticsVO.getTotalCouponMoney().add(couponAmt));
                    respStatisticsVO.setUseCouponOrderMoney(respStatisticsVO.getUseCouponOrderMoney().add(useCouponOrderMoney));

                }
            }
        }

        for (PrmCouponRespVO couponRespVO : couponRespVOS) {
            // 优惠券状态正常, 并且过期时间小于当前, 则标记未已过期
            if (CouponStateEnum.NORMAL.getState().equals(couponRespVO.getState()) && couponRespVO.getExpirationDateEnd().getTime() < System.currentTimeMillis()) {
                couponRespVO.setState(CouponStateEnum.PAST_DUE.getState());

            }
            couponRespVO.setCouponState(couponRespVO.getState());
            if(couponRespVO.getDiscountType() == 0){
                couponRespVO.setCouponRule(dateFormat.format(couponRespVO.getExpirationDateStart())+"至"+dateFormat.format(couponRespVO.getExpirationDateEnd())+"内可用,满"+couponRespVO.getTriggerAmt()+"元减"+couponRespVO.getDiscountAmt()+"元");
            }
            if(couponRespVO.getDiscountType() == 1){
                couponRespVO.setCouponRule(dateFormat.format(couponRespVO.getExpirationDateStart()) + "至" + dateFormat.format(couponRespVO.getExpirationDateEnd()) + "内可用,满" + couponRespVO.getTriggerAmt() + "元享受" + couponRespVO.getDiscountPercent() + "折");
            }
            // 使用缓存获取 branch 信息
            Long branchId =couponRespVO.getBranchId();
            if (branchId != null) {
                BranchDTO branch = branchCache.get(branchId,
                        () -> promotionCacheService.getBranchDTO(branchId));
                if (Objects.nonNull(branch)) {
                    couponRespVO.setBranchName(branch.getBranchName());
                }
            }

            if(ToolUtil.isNotEmpty(couponRespVO.getCouponBatchId())){
                //填充发券人
                PrmCouponBatch batch = batchCache.get(couponRespVO.getCouponBatchId(),
                        () -> prmCouponBatchMapper.selectById(couponRespVO.getCouponBatchId()));
                couponRespVO.setCreatorName(batch.getCreateBy());
            }
            //优惠卷记录统计
            if (CouponStateEnum.NORMAL.getState().equals(couponRespVO.getState())){
                respStatisticsVO.setUnusedCoupon(respStatisticsVO.getUnusedCoupon()+1);
            }else if (CouponStateEnum.USED.getState().equals(couponRespVO.getState())){
                respStatisticsVO.setUsedCoupon(respStatisticsVO.getUsedCoupon()+1);
            }else if (CouponStateEnum.PAST_DUE.getState().equals(couponRespVO.getState())){
                respStatisticsVO.setDueCoupon(respStatisticsVO.getDueCoupon()+1);
            }

        }
//        log.info("优惠券记录统计,{}", JsonUtils.toJsonString(respStatisticsVO));
        return new PageResult<>(couponRespVOS, page.getTotal(),respStatisticsVO);
    }

    /**
     * 下发用户领取优惠券
     *
     * @param receiveData 领取信息
     */
    @Override
    @Transactional
    public void saveNormalCouponReceive(CouponReceiveDTO receiveData) {
        receiveData.getCouponStatusMap().forEach((couponTemplateId, statusId) -> {
            // 单个优惠券操作领取
            CouponReceiveResultDTO resultDTO = shelf().saveNormalCouponReceiveSingle(receiveData.getBranchId(), receiveData.getMemberId() != null ? receiveData.getMemberId() : null, couponTemplateId, receiveData.getMode() == NumberPool.INT_ZERO,receiveData.getCouponBatchId());
            // 保存领取结果
            redisService.setCacheObject(RedisCouponConstants.getCouponReceiveResult(statusId), resultDTO, RedisCouponConstants.RECEIVE_RESULT_TIME, TimeUnit.SECONDS);
        });
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_COUPON_RECEIVE_REFRESH, condition = "#couponTemplateId")
    public CouponReceiveResultDTO saveNormalCouponReceiveSingle(Long branchId, Long memberId, Long couponTemplateId, boolean checkStock, Long couponBatchId) {
        CouponReceiveResultDTO receiveResult = new CouponReceiveResultDTO(couponTemplateId);
        BranchDTO branchDTO = promotionCacheService.getBranchDTO(branchId);
        // 优惠券不存在
        CouponTemplateDTO couponTemplate = promotionCacheService.getCouponTemplate(couponTemplateId);
        if (Objects.isNull(couponTemplate)) {
            receiveResult.setErr(COUPON_NOT_EXITS);
            return receiveResult;
        }
        // 再次校验是否过期, 或者下架, 防止积压消息消费
        if (Objects.isNull(couponTemplate.getTemplateStartDate())
                || Objects.isNull(couponTemplate.getTemplateEndDate())
                || couponTemplate.getStatus() == NumberPool.INT_ONE
                || couponTemplate.getTemplateEndDate().getTime() < System.currentTimeMillis()) {
            log.error("优惠券还没生效 branchId={},couponTemplateId={}", branchId, couponTemplateId);
            receiveResult.setErr(COUPON_IS_DISABLE);
            return receiveResult;
        }
        // 如果领取的时候已经验证过库存了, 这里验证基本信息以后就直接发放
        if (checkStock) {
            String stockUsedKey = RedisCouponConstants.getStockUsedKey(couponTemplateId);
            // 验证库存
            Integer stockUsed = redisService.getCacheObject(stockUsedKey);
            if (Objects.isNull(couponTemplate.getCouponQty()) || couponTemplate.getCouponQty() == NumberPool.INT_ZERO) {
                // 没有库存限制
            } else {
                if (Objects.isNull(stockUsed) || stockUsed.intValue() + 1 > couponTemplate.getCouponQty().intValue()) {
                    log.error("优惠券已经领取完了 branchId={},couponTemplateId={}", branchId, couponTemplateId);
                    // 已领数量大于 库存数量
                    receiveResult.setErr(COUPON_STOCK_ERR);
                    return receiveResult;
                }
            }
            // 是否已经领取过 || 超过领取限制
            String branchReceiveQty = RedisCouponConstants.getCouponBranchReceiveQty(couponTemplateId, branchId);
            if (Objects.nonNull(couponTemplate.getLimit()) && !couponTemplate.getLimit().equals(NumberPool.INT_ZERO)) {
                // 存在领取限制
                // 已领取数量
                Integer receiveNum = redisService.getCacheObject(branchReceiveQty);
                if (Objects.nonNull(receiveNum) && receiveNum + 1 > couponTemplate.getLimit()) {
                    log.error("超过领取限制 branchId={},couponTemplateId={}", branchId, couponTemplateId);
                    // 超过限制
                    receiveResult.setErr(COUPON_RECEIVE_LIMIT);
                    return receiveResult;
                }
            }
            //校验过滤黑白名单
            List<CouponReceiveScopeDTO> couponSpuScopeList = promotionCacheService.getCouponReceiveScopeList(couponTemplateId);
            if (CouponScopeUtil.conditionReceiveCopeApplyId(HutoolBeanUtils.toBean(branchDTO, CouponScopeUtil.ReceiveScopeValidVO.class), couponSpuScopeList)) {
                // 发放优惠券
                receiveResult.setCouponId(sendCoupon(couponTemplate, branchId, memberId, couponBatchId));
                // 增加优惠券已领取
                redisService.incrByCacheObject(branchReceiveQty, NumberPool.INT_ONE);
                // 增加用户已领取
                redisService.incrByCacheObject(stockUsedKey, NumberPool.INT_ONE);
                // 适配重复领取规则
                repeatRole(couponTemplate, branchId);
            }
        } else {
            // 校验过滤黑白名单
            List<CouponReceiveScopeDTO> couponSpuScopeList = promotionCacheService.getCouponReceiveScopeList(couponTemplateId);
            if (CouponScopeUtil.conditionReceiveCopeApplyId(HutoolBeanUtils.toBean(branchDTO, CouponScopeUtil.ReceiveScopeValidVO.class), couponSpuScopeList)) {
                receiveResult.setCouponId(sendCoupon(couponTemplate, branchId, memberId, null));
            }
        }
        return receiveResult;
    }

    @Override
    public PageResult<CouponDTO> getBranchCouponListPage(CouponPageReqVO receiveReq) {
        Page<Object> page = PageHelper.startPage(receiveReq.getPageNo(), receiveReq.getPageSize());
        return new PageResult<>(HutoolBeanUtils.toBean(prmCouponMapper.selectBranchCouponListPage(receiveReq), CouponDTO.class), page.getTotal());
    }

    @Override
    public List<CouponDTO> getCouponListByBranchIdAndState(Long branchId, Integer couponState) {
        CouponPageReqVO req = new CouponPageReqVO();
        req.setBranchId(branchId);
        req.setState(couponState);
        return prmCouponMapper.selectBranchCouponListPage(req);
    }

    @Override
    public List<CouponDTO> getCouponListByBranchId(Long branchId) {
        CouponPageReqVO req = new CouponPageReqVO();
        req.setBranchId(branchId);
        return prmCouponMapper.selectBranchCouponListPage(req);
    }
    /**
     * 获取门店指定优惠券列表
     *
     * @param branchId     门店ID
     * @param couponIdList 优惠券ID集合
     * @return
     */
    @Override
    public List<CouponDTO> getCouponBatch(Long branchId, List<Long> couponIdList) {
        return prmCouponMapper.selectCouponBatchIds(branchId, couponIdList);
    }

    /**
     * 优惠券核销使用
     *
     * @param couponList 优惠券核销列表
     * @return
     */
    @Override
    @Transactional
    public void applyCoupon(List<CouponApplyDTO> couponList) {
        log.info("核销优惠券 couponList={}", couponList);
        for (CouponApplyDTO couponApply : couponList) {
            int line = prmCouponMapper.updateApply(couponApply);
            if (line == 0) {
                throw exception(COUPON_CHANGE_STATE_ERR);
            }
            PrmCouponLog prmCouponLog = new PrmCouponLog();
            prmCouponLog.setBeforeState(CouponStateEnum.NORMAL.getState());
            prmCouponLog.setAfterState(CouponStateEnum.USED.getState());
            prmCouponLog.setOperateType(0);
            prmCouponLog.setContent(StringUtils.format("{} 下单优惠券核销", couponApply.getRelateOrderNo()));
            couponLogMapper.insert(prmCouponLog);
        }
    }

    @Override
    @Transactional
    public void returnCoupon(CouponReturnDTO couponReturn) {
        log.info("优惠券返还 {}", couponReturn);
        for (CouponApplyDTO couponApply : couponReturn.getCouponList()) {
            int line = prmCouponMapper.updateReturn(couponApply);
            if (line == 0) {
                throw exception(COUPON_CHANGE_STATE_ERR);
            }
            PrmCouponLog prmCouponLog = new PrmCouponLog();
            prmCouponLog.setCouponId(String.valueOf(couponApply.getCouponId()));
            prmCouponLog.setBeforeState(CouponStateEnum.USED.getState());
            prmCouponLog.setAfterState(CouponStateEnum.NORMAL.getState());
            prmCouponLog.setOperateType(1);
            prmCouponLog.setContent(StringUtils.format("{} {}", couponApply.getRelateOrderNo(), couponReturn.getMemo()));
            couponLogMapper.insert(prmCouponLog);
        }
    }

    @Override
    public void disableBatch(List<Long> list) {
        for (Long couponId : list) {
            PrmCoupon update = new PrmCoupon();
            update.setCouponId(couponId);
            update.setUpdateBy(SecurityUtils.getUsername());
            update.setState(CouponStateEnum.DISABLE.getState());
            prmCouponMapper.updateById(update);
        }
    }

    @Override
    public List<CouponDTO> getCouponListByBranchIdAndCouponBatchId(Long branchId, Long couponBatchId) {
        CouponPageReqVO req = new CouponPageReqVO();
        req.setBranchId(branchId);
        req.setCouponBatchId(couponBatchId);
        return prmCouponMapper.selectBranchCouponListPage(req);
    }

    @Override
    public List<Long> getCouponReceivedBranchs(Long couponTemplateId) {
        return prmCouponMapper.getCouponReceivedBranchs(couponTemplateId);
    }

    /**
     * 发送优惠券
     *
     * @param couponTemplate 优惠券模版
     * @param branchId       门店ID
     * @param memberId       用户ID
     * @return 优惠券ID
     */
    public Long sendCoupon(CouponTemplateDTO couponTemplate, Long branchId, Long memberId, Long couponBatchId) {
        PrmCoupon coupon = new PrmCoupon();
        coupon.setSysCode(couponTemplate.getSysCode())
                .setCouponTemplateId(couponTemplate.getCouponTemplateId())
                .setBranchId(branchId)
                .setReceiveMemberId(memberId != null ? memberId : null)
                .setCouponBatchId(couponBatchId != null ? couponBatchId : null)
                .setState(CouponStateEnum.NORMAL.getState());
        // 有效期
        if (couponTemplate.getExpirationType() == NumberPool.INT_ZERO) {
            // 固定日期
            coupon.setExpirationDateStart(couponTemplate.getExpirationDateStart())
                    .setExpirationDateEnd(couponTemplate.getExpirationDateEnd());
        } else {
            // 领取之后
            DateTime now = DateUtil.date();
            coupon.setExpirationDateStart(DateUtil.offsetDay(now, couponTemplate.getDisableDays()));
            coupon.setExpirationDateEnd(DateUtil.offsetDay(now, couponTemplate.getExpireDays()));
        }
        coupon.setSpuScope(couponTemplate.getSpuScope())
                .setSpuScopeApplysIds(couponTemplate.getSpuScopeApplyIds())
                .setDiscountType(couponTemplate.getDiscountType())
                .setTriggerAmt(couponTemplate.getTriggerAmt())
                .setDiscountAmt(couponTemplate.getDiscountAmt())
                .setDiscountLimitAmt(couponTemplate.getDiscountLimitAmt())
                .setDiscountPercent(couponTemplate.getDiscountPercent())
                .setReceiveType(couponTemplate.getReceiveType())
                .setCostFlag(couponTemplate.getCostFlag())
                .setSupplierId(couponTemplate.getSupplierId())
                .setExcludable(StringUtils.isNotEmpty(couponTemplate.getExcludable()) ? Integer.parseInt(couponTemplate.getExcludable()) : null)
                .setSameTypeExcludable(couponTemplate.getSameTypeExcludable());
        prmCouponMapper.insert(coupon);
        // 操作数据库
        return coupon.getCouponId();
    }

    /**
     * 适配重复领取规则
     *
     * @param couponTemplate
     * @param branchId
     */
    private void repeatRole(CouponTemplateDTO couponTemplate, Long branchId) {
        String couponBranchReceiveQty = RedisCouponConstants.getCouponBranchReceiveQty(couponTemplate.getCouponTemplateId(), branchId);
        if (!StringPool.ONE.equals(couponTemplate.getRepeatFlag())) {
            // 设置用户领取key过期时间
            redisService.expire(couponBranchReceiveQty, couponTemplate.expireTimes());
        }
        Long expreTime = NumberPool.LOWER_GROUND_LONG;
        PrmCouponTemplateRepeatRuleDTO repeatRule = couponTemplate.getRepeatRule();
        if (StringPool.ONE.equals(couponTemplate.getRepeatFlag()) && Objects.nonNull(repeatRule)) {
            // 需要计算重复规则
            switch (repeatRule.getRepeatType()) {
                case NumberPool.INT_ONE:
                    // 每天, 推迟到明天早上
                    expreTime = DateUtil.tomorrow().getTime() - System.currentTimeMillis();
                    break;
                case NumberPool.INT_TWO:
                    // 每周, 推迟到下周一
                    expreTime = DateUtil.nextWeek().getTime() - System.currentTimeMillis();
                    break;
                case NumberPool.INT_THREE:
                    // 每月, 推迟到下个月一号
                    expreTime = DateUtil.nextMonth().getTime() - System.currentTimeMillis();
                    break;
                default:
                    // 自定义
                    expreTime = repeatRoleDiy(repeatRule);
                    break;
            }
        }
        if (expreTime > NumberPool.LONG_ZERO) {
            // 设置用户领取key过期时间
            redisService.expire(couponBranchReceiveQty, expreTime / NumberPool.THOUSAND);
        }
    }

    /**
     * 自定义重复模版
     *
     * @param repeatRule
     * @return
     */
    private Long repeatRoleDiy(PrmCouponTemplateRepeatRuleDTO repeatRule) {
        Long expreTime = NumberPool.LOWER_GROUND_LONG;
        if (Objects.isNull(repeatRule.getRepeatFrequencyUnit()) || StringUtils.isEmpty(repeatRule.getRepeatFrequencyPeriod())) {
            return expreTime;
        }
        switch (repeatRule.getRepeatFrequencyUnit()) {
            case NumberPool.INT_ZERO:
                // 天, 多少天后重置领取数量
                expreTime = DateUtil.offsetDay(DateUtil.date(), Integer.parseInt(repeatRule.getRepeatFrequencyPeriod())).getTime() - System.currentTimeMillis();
                break;
            case NumberPool.INT_ONE:
                // 周, 周几充值领取数量
                // 下一周, 第几天
                expreTime = DateUtil.offsetDay(
                        DateUtil.offsetWeek(DateUtil.date(), NumberPool.INT_ONE),
                        Integer.parseInt(repeatRule.getRepeatFrequencyPeriod())
                ).getTime() - System.currentTimeMillis();
                break;
            case NumberPool.INT_TWO:
                // 月, 月的第几天重置
                // 下个月, 第几天
                expreTime = DateUtil.offsetDay(
                        DateUtil.offsetMonth(DateUtil.date(), NumberPool.INT_ONE),
                        Integer.parseInt(repeatRule.getRepeatFrequencyPeriod())
                ).getTime() - System.currentTimeMillis();
                break;
            case NumberPool.INT_THREE:
                // 年, 每年的第几个月
                // 明年, 第几个月
                expreTime = DateUtil.offsetMonth(
                        DateUtil.offset(DateUtil.date(), DateField.YEAR, NumberPool.INT_ONE),
                        Integer.parseInt(repeatRule.getRepeatFrequencyPeriod())
                ).getTime() - System.currentTimeMillis();
                break;
        }
        return expreTime;
    }

    // 获取自己的代理对象, 调用方法时需要在接口上进行定义, 基于接口的AOP, 直接调用内部方法会报错
    PrmCouponServiceImpl shelf() {
        return SpringUtils.getAopProxy(this);
    }
}
