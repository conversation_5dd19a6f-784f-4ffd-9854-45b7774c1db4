package com.zksr.promotion.convert.quota;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.coupon.dto.ColonelQuotaDTO;
import com.zksr.promotion.controller.quota.vo.QuotaDetailResponse;
import com.zksr.promotion.domain.PrmCouponColonelQuota;
import com.zksr.promotion.controller.quota.vo.PrmCouponColonelQuotaRespVO;
import com.zksr.promotion.controller.quota.vo.PrmCouponColonelQuotaSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 业务员发券额度 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-12-03
*/
@Mapper
public interface PrmCouponColonelQuotaConvert {

    PrmCouponColonelQuotaConvert INSTANCE = Mappers.getMapper(PrmCouponColonelQuotaConvert.class);

    PrmCouponColonelQuotaRespVO convert(PrmCouponColonelQuota prmCouponColonelQuota);

    PrmCouponColonelQuota convert(PrmCouponColonelQuotaSaveReqVO prmCouponColonelQuotaSaveReq);

    PageResult<PrmCouponColonelQuotaRespVO> convertPage(PageResult<PrmCouponColonelQuota> prmCouponColonelQuotaPage);

    QuotaDetailResponse convertDetail(ColonelQuotaDTO colonelQuotaDTO);
}