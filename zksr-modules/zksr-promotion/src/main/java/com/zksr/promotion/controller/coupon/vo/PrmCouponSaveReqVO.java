package com.zksr.promotion.controller.coupon.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 优惠券记录对象 prm_coupon
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Data
@ApiModel("优惠券记录 - prm_coupon分页 Request VO")
public class PrmCouponSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 优惠券记录id */
    @ApiModelProperty(value = "是否异种排他")
    private Long couponId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 优惠券模板id */
    @Excel(name = "优惠券模板id")
    @ApiModelProperty(value = "优惠券模板id")
    private Long couponTemplateId;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 领取的用户id */
    @Excel(name = "领取的用户id")
    @ApiModelProperty(value = "领取的用户id")
    private Long receiveMemberId;

    /** 状态（数据字典）;1-正常 0-作废 2-已使用 */
    @Excel(name = "状态", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "状态")
    private Integer state;

    /** 有效期开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "有效期开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "有效期开始时间")
    private Date expirationDateStart;

    /** 有效期结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "有效期结束时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "有效期结束时间")
    private Date expirationDateEnd;

    /** 使用时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "使用时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "使用时间")
    private Date useTime;

    /** 对应订单号 */
    @Excel(name = "对应订单号")
    @ApiModelProperty(value = "对应订单号")
    private String relateOrderNo;

    /** 商品适用范围(数据字典);0-所有商品可用（全场券） 1-指定入驻商可用（入驻商券），2-指定品类可用（品类券），3-指定品牌可用（品牌券），4-指定商品可用（商品券） */
    @Excel(name = "商品适用范围(数据字典);0-所有商品可用", readConverterExp = "全=场券")
    @ApiModelProperty(value = "商品适用范围(数据字典);0-所有商品可用", required = true)
    private Integer spuScope;

    /** 商品适用范围之适用ids;spu_scope=入驻商券时设定，入驻商id逗号分隔;spu_scope=品类券时设定，管理分类id逗号分隔;spu_scope=品牌券时设定，品牌id逗号分隔;spu_scope=商品券券时设定，SPU id逗号分隔 */
    @Excel(name = "商品适用范围之适用ids;spu_scope=入驻商券时设定，入驻商id逗号分隔;spu_scope=品类券时设定，管理分类id逗号分隔;spu_scope=品牌券时设定，品牌id逗号分隔;spu_scope=商品券券时设定，SPU id逗号分隔")
    @ApiModelProperty(value = "商品适用范围之适用ids;spu_scope=入驻商券时设定，入驻商id逗号分隔;spu_scope=品类券时设定，管理分类id逗号分隔;spu_scope=品牌券时设定，品牌id逗号分隔;spu_scope=商品券券时设定，SPU id逗号分隔")
    private String spuScopeApplysIds;

    /** 优惠类型(数据字典);0-满减券  1-折扣券 */
    @Excel(name = "优惠类型(数据字典);0-满减券  1-折扣券")
    @ApiModelProperty(value = "优惠类型(数据字典);0-满减券  1-折扣券", required = true)
    private Integer discountType;

    /** 优惠券启用金额(使用门槛);满多少元(商品适用范围内商品订单金额)可以使用 */
    @Excel(name = "优惠券启用金额(使用门槛);满多少元(商品适用范围内商品订单金额)可以使用")
    @ApiModelProperty(value = "优惠券启用金额(使用门槛);满多少元(商品适用范围内商品订单金额)可以使用", required = true)
    private BigDecimal triggerAmt;

    /** 优惠券面额;满多少元可以减多少元(满减券设置) */
    @Excel(name = "优惠券面额;满多少元可以减多少元(满减券设置)")
    @ApiModelProperty(value = "优惠券面额;满多少元可以减多少元(满减券设置)")
    private BigDecimal discountAmt;

    /** 优惠折扣;折，如99折记9.9折(折扣券设置) */
    @Excel(name = "优惠折扣;折，如99折记9.9折(折扣券设置)")
    @ApiModelProperty(value = "优惠折扣;折，如99折记9.9折(折扣券设置)")
    private BigDecimal discountPercent;

    /** 最多优惠;折扣券设置，例如，折扣上限为 20 元，当使用 8 折优惠券，订单金额为 1000 元时，最高只可折扣 20 元，而非 80  元。 */
    @Excel(name = "最多优惠;折扣券设置，例如，折扣上限为 20 元，当使用 8 折优惠券，订单金额为 1000 元时，最高只可折扣 20 元，而非 80  元。")
    @ApiModelProperty(value = "最多优惠;折扣券设置，例如，折扣上限为 20 元，当使用 8 折优惠券，订单金额为 1000 元时，最高只可折扣 20 元，而非 80  元。")
    private BigDecimal discountLimitAmt;

    /** 领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券（需要讨论返券规则，返券规则用扩展表存 如购买指定商品返券，支付满多少返券等等） */
    @Excel(name = "领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券", readConverterExp = "需=要讨论返券规则，返券规则用扩展表存,如=购买指定商品返券，支付满多少返券等等")
    @ApiModelProperty(value = "领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券", required = true)
    private Integer receiveType;

    /** 是否分摊;用来计算订单表的discount_amt1,discount_amt2, 需要传输给ERP */
    @Excel(name = "是否分摊;用来计算订单表的discount_amt1,discount_amt2, 需要传输给ERP")
    @ApiModelProperty(value = "是否分摊;用来计算订单表的discount_amt1,discount_amt2, 需要传输给ERP", required = true)
    private Integer costFlag;

    /** 是否异种排他 */
    @Excel(name = "是否异种排他")
    @ApiModelProperty(value = "是否异种排他", required = true)
    private Integer excludable;

}
