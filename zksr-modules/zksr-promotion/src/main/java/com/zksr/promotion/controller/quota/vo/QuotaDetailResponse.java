package com.zksr.promotion.controller.quota.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 业务员发券额度对象 prm_coupon_colonel_quota
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
@ApiModel("业务员发券额度 - prm_coupon_colonel_quota Response VO")
public class QuotaDetailResponse {
    private static final long serialVersionUID = 1L;

    /** 业务员发券额度ID */
    @ApiModelProperty(value = "业务员发券额度ID")
    private Integer couponColonelQuotaId;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 本月发券额度 */
    @Excel(name = "本月发券额度")
    @ApiModelProperty(value = "本月发券额度")
    private BigDecimal quota;

    /** 已发额度 */
    @Excel(name = "已发额度")
    @ApiModelProperty(value = "已发额度")
    private BigDecimal finishQuota;

    /** 剩余额度 */
    @Excel(name = "剩余额度")
    @ApiModelProperty(value = "剩余额度")
    private BigDecimal remainingQuota;

}
