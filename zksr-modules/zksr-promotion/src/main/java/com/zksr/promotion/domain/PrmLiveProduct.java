package com.zksr.promotion.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 直播商品对象 prm_live_product
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@TableName(value = "prm_live_product")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmLiveProduct extends BaseEntity {
    private static final long serialVersionUID=1L;

    /** ID主键 */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long id;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 直播ID */
    @Excel(name = "直播ID")
    private String activityId;

    /** 直播间名称 */
    @Excel(name = "直播间名称")
    private String name;

    /** 商品sku_id */
    @Excel(name = "商品sku_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 商品SPU_id */
    @Excel(name = "商品SPU_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    private String spuName;

    /** 标准价(原价) */
    @Excel(name = "标准价(原价)")
    private BigDecimal markPrice;

    /** 直播价 */
    @Excel(name = "直播价")
    private BigDecimal livePrice;

    /** 商品品单位 数据字典（sys_prdt_unit） */
    @Excel(name = "商品品单位 数据字典", readConverterExp = "s=ys_prdt_unit")
    private String unit;

    /** 商品品单位名称 */
    @Excel(name = "商品品单位名称")
    private String unitName;

    /** 图片1 */
    @Excel(name = "图片1")
    private String imageUrl1;

    /** 图片2 */
    @Excel(name = "图片2")
    private String imageUrl2;

    /** 图片3 */
    @Excel(name = "图片3")
    private String imageUrl3;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    private String supplierName;

    /** 是否删除：0-否，1-是 */
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 弹框商品标记，0：不是，1：是 */
    @Excel(name = "弹框商品标记，0：不是，1：是")
    private Long broadcastFlag;
}
