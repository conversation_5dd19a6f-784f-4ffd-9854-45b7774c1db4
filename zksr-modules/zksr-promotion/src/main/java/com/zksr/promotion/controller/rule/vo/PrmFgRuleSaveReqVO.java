package com.zksr.promotion.controller.rule.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 满赠规则对象 prm_fg_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ApiModel("满赠规则 - prm_fg_rule分页 Request VO")
public class PrmFgRuleSaveReqVO {
    private static final long serialVersionUID = 1L;

    /**
     * 满赠规则
     */
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long fgRuleId;

    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sysCode;

    /**
     * 满赠活动id
     */
    @Excel(name = "满赠活动id")
    @ApiModelProperty(value = "满赠活动id", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 满赠金额
     */
    @Excel(name = "满赠金额")
    @ApiModelProperty(value = "满赠金额")
    private BigDecimal fullAmt;

    /**
     * 赠品名称
     */
    @Excel(name = "赠品名称")
    @ApiModelProperty(value = "赠品名称")
    private String giftName;

    /**
     * 赠品类型;0-商品 1-优惠券
     */
    @Excel(name = "赠品类型;0-商品 1-优惠券")
    @ApiModelProperty(value = "赠品类型;0-商品 1-优惠券")
    private Integer giftType;

    /**
     * sku_id
     */
    @Excel(name = "sku_id")
    @ApiModelProperty(value = "sku_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;

    /**
     * 返券模板id
     */
    @Excel(name = "返券模板id")
    @ApiModelProperty(value = "返券模板id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long couponTemplateId;

    /**
     * 赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段
     */
    @Excel(name = "赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    @ApiModelProperty(value = "赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    private Integer onceGiftQty;

    /**
     * 总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段
     */
    @Excel(name = "总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    @ApiModelProperty(value = "总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    private Integer totalGiftQty;

    /**
     * 状态 1-启用 0-停用
     */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Integer status;

    /**
     * 总库存
     */
    @Excel(name = "总库存")
    @ApiModelProperty(value = "总库存")
    private Long stock;

    /**
     * 赠品商品单位大小
     */
    @Excel(name = "赠品商品单位大小")
    @ApiModelProperty(value = "赠品商品单位大小")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer giftSkuUnitType;

    /**
     * 购买品项数(sku种类数)
     */
    @Excel(name = "购买品项数(sku种类数)")
    @ApiModelProperty(value = "购买品项数(sku种类数)")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer buySkuNum;

    /**
     * 赠送方式(默认为全赠 0仅一种，1任选，2全赠)
     */
    @Excel(name = "赠送方式(默认为全赠 0仅一种，1任选，2全赠)")
    @ApiModelProperty(value = "赠送方式(默认为全赠 0仅一种，1任选，2全赠)")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer giftGroupType;

    /**
     * 赠品商品单位大小
     */
    @Excel(name = "赠送单位数量")
    @ApiModelProperty(value = "赠送单位数量")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer giftSkuUnitQty;
}
