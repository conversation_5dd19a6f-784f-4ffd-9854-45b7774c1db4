package com.zksr.promotion.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.controller.rule.vo.PrmBgActivityRespVo;
import com.zksr.promotion.controller.rule.vo.PrmBgActivitySaveReqVO;
import com.zksr.promotion.controller.rule.vo.PrmBgRulePageReqVO;
import com.zksr.promotion.domain.PrmBgRule;

import javax.validation.Valid;

/**
 * 买赠条件规则Service接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
public interface IPrmBgRuleService {

    /**
     * 新增买赠条件规则
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmBgRule(@Valid PrmBgActivitySaveReqVO createReqVO);

    /**
     * 修改买赠条件规则
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmBgRule(@Valid PrmBgActivitySaveReqVO updateReqVO);

    /**
     * 删除买赠条件规则
     *
     * @param bgRuleId 买赠条件规则id
     */
    public void deletePrmBgRule(Long bgRuleId);

    /**
     * 批量删除买赠条件规则
     *
     * @param bgRuleIds 需要删除的买赠条件规则主键集合
     * @return 结果
     */
    public void deletePrmBgRuleByBgRuleIds(Long[] bgRuleIds);

    /**
     * 获得买赠条件规则
     *
     * @param activityId 促销id
     * @return 买赠条件规则
     */
    public PrmBgActivityRespVo getPrmBgRule(Long activityId);

    /**
     * 获得买赠条件规则分页
     *
     * @param pageReqVO 分页查询
     * @return 买赠条件规则分页
     */
    PageResult<PrmBgRule> getPrmBgRulePage(PrmBgRulePageReqVO pageReqVO);

    /**
    * @Description: 变更买赠状态
    * @Author: liuxingyu
    * @Date: 2024/5/24 16:53
    */
    Long changeBgStatus(PrmBgActivitySaveReqVO saveReqVO);
}
