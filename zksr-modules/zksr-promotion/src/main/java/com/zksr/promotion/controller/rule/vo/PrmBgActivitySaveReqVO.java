package com.zksr.promotion.controller.rule.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
*
 *
* <AUTHOR>
* @date 2024/5/16 16:56
*/
@Data
@ApiModel("买赠活动新增对象")
public class PrmBgActivitySaveReqVO extends PrmActivitySaveReqVO {

    private static final long serialVersionUID = 1L;

    /** 促销类型（数据字典）;SK-秒杀 FG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价 */
    @Excel(name = "促销类型", readConverterExp = "数据字典")
    @ApiModelProperty(value = "促销类型", required = true)
    private String prmNo = PrmNoEnum.BG.getType();

    @ApiModelProperty("买赠规则列表")
    private List<PrmBgRuleSaveReqVO> bgRuleSaveReqVOList;

    @ApiModelProperty("区域城市列表")
    private List<PrmActivityWhiteOrBlackVO> areaIdList;

    @ApiModelProperty("门店列表")
    private List<PrmActivityWhiteOrBlackVO> branchIdList;

    @ApiModelProperty("渠道列表")
    private List<PrmActivityWhiteOrBlackVO> channelIdList;

    @ApiModelProperty("spu列表")
    private List<PrmActivityWhiteOrBlackVO> spuIdList;

}
