package com.zksr.promotion.api.activity;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.content.ProductContentApi;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import com.zksr.promotion.api.activity.dto.SkRuleDTO;
import com.zksr.promotion.api.activity.dto.SpRuleDTO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.controller.rule.vo.PrmCbActivitySaveReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityPageReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.domain.PrmActivity;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPrmCbRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销活动实现类
 * @date 2024/5/18 8:53
 */
@ApiIgnore
@RestController
public class ActivityApiImpl implements ActivityApi {

    @Autowired
    private IPrmActivityService activityService;

    @Autowired
    private IPrmCbRuleService cbRuleService;

    @Resource
    private ProductContentApi productContentApi;

    @Override
    public CommonResult<List<SupplierActivityDTO>> getSupplierActivity(Long supplierId) {
        return CommonResult.success(activityService.getSupplierActivity(supplierId));
    }

    @Override
    public CommonResult<List<SpRuleDTO>> getActivitySpRule(Long activityId) {
        return CommonResult.success(activityService.getActivitySpRule(activityId));
    }

    @Override
    public CommonResult<List<SkRuleDTO>> getActivitySkRule(Long activityId) {
        return CommonResult.success(activityService.getActivitySkRule(activityId));
    }

    @Override
    public CommonResult<List<ActivitySpuScopeDTO>> getActivitySpuScopeList(Long activityId) {
        return CommonResult.success(activityService.getActivitySpuScopeList(activityId));
    }

    @Override
    public CommonResult<List<ActivityBranchScopeDTO>> getActivityBranchScopeList(Long activityId) {
        return CommonResult.success(activityService.getActivityBranchScopeList(activityId));
    }

    @Override
    public CommonResult<List<ActivityCityScopeDTO>> getActivityCityScopeList(Long activityId) {
        return CommonResult.success(activityService.getActivityCityScopeList(activityId));
    }

    @Override
    public CommonResult<List<ActivityChannelScopeDTO>> getActivityChannelScopeList(Long activityId) {
        return CommonResult.success(activityService.getActivityChannelScopeList(activityId));
    }

    @Override
    public CommonResult<List<ActivitySupplierScopeDTO>> getActivitySupplierScopeList(Long activityId) {
        return CommonResult.success(activityService.getActivitySupplierScopeList(activityId));
    }

    @Override
    public CommonResult<PrmActivityDTO> getActivityDto(Long activityId) {
        return CommonResult.success(activityService.getActivityDto(activityId));
    }

    @Override
    public CommonResult<List<FdRuleDTO>> getActivityFdRule(Long activityId) {
        return CommonResult.success(activityService.getActivityFdRule(activityId));
    }

    @Override
    public CommonResult<List<FgRuleDTO>> getActivityFgRule(Long activityId) {
        return CommonResult.success(activityService.getActivityFgRule(activityId));
    }

    @Override
    public CommonResult<List<BgRuleDTO>> getActivityBgRule(Long activityId) {
        return CommonResult.success(activityService.getActivityBgRule(activityId));
    }

    @Override
    public CommonResult<List<CbRuleDTO>> getActivityCbRule(Long activityId) {
        return CommonResult.success(activityService.getActivityCbRule(activityId));
    }

    @Override
    public CommonResult<List<Long>> getSeckillSpuIds(String prmNo, Integer funcScope) {
        return CommonResult.success(activityService.getSeckillSpuIds(prmNo, funcScope));
    }

    @Override
    public CommonResult<List<Long>> getSeckillSkuIds(String prmNo, Integer funcScope) {
        return CommonResult.success(activityService.getSeckillSkuIds(prmNo, funcScope));
    }

    @Override
    public CommonResult<Long> countBySkuId(Long skuId) {
        return CommonResult.success(activityService.countBySkuId(skuId));
    }

    @Override
    public CommonResult<List<Long>> getAreaCbSupplierIds(Long areaId) {
        return CommonResult.success(activityService.getAreaCbSupplierIds(areaId));
    }

    @Override
    public CommonResult<List<Long>> getGlobalCbSupplierIds(Long sysCode) {
        return CommonResult.success(activityService.getGlobalCbSupplierIds(sysCode));
    }

    @Override
    public CommonResult<List<Long>> getActivityCbByStatus(Long sysCode, Integer funcScope, Integer status) {
        return CommonResult.success(activityService.getActivityCbByStatus(sysCode, funcScope, status));
    }

    @Override
    public CommonResult<List<Long>> getExpiredCbActivityIds() {
        return CommonResult.success(activityService.getExpiredCbActivityIds());
    }

    @Override
    public CommonResult<Boolean> stopCbActivity(Long activityId) {
        PrmCbActivitySaveReqVO updateReqVO = new PrmCbActivitySaveReqVO();
        updateReqVO.setActivityId(activityId);
        Long spuCombineId = cbRuleService.changePrmCbRuleStatus(updateReqVO, 2);
        productContentApi.sendSpuCombineEvent(Arrays.asList(spuCombineId));
        return CommonResult.success(true);
    }


    @Override
    public CommonResult<ActivityDTO> getActivityById(Long activityId) {
        return CommonResult.success(HutoolBeanUtils.toBean(activityService.getPrmActivity(activityId),ActivityDTO.class));
    }

    @Override
    public CommonResult<List<ActivityDTO>> getActivityList(ActivityDTO reqVO) {
        PrmActivityPageReqVO pageReqVO = HutoolBeanUtils.toBean(reqVO, PrmActivityPageReqVO.class);
        PageResult<PrmActivityRespVO> prmActivityPage = activityService.getPrmActivityPage(pageReqVO);
        return CommonResult.success(HutoolBeanUtils.toBean(prmActivityPage.getList(),ActivityDTO.class));
    }


    @Override
    public CommonResult<String> checkActivityByItem(Long[] skuIdArray) {
        return CommonResult.success(activityService.checkActivityByItem(skuIdArray));
    }
}
