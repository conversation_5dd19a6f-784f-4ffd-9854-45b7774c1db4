package com.zksr.promotion.convert.rule;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.SkRuleDTO;
import com.zksr.promotion.controller.activity.vo.PrmActivityItemPageVo;
import com.zksr.promotion.controller.rule.vo.PrmSkRuleItemExcelRespVO;
import com.zksr.promotion.domain.PrmSkRule;
import com.zksr.promotion.controller.rule.vo.PrmSkRuleRespVO;
import com.zksr.promotion.controller.rule.vo.PrmSkRuleSaveReqVO;
import com.zksr.promotion.domain.excel.PrmSkRuleImportExcel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 秒杀规则 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-13
*/
@Mapper
public interface PrmSkRuleConvert {

    PrmSkRuleConvert INSTANCE = Mappers.getMapper(PrmSkRuleConvert.class);

    PrmSkRuleRespVO convert(PrmSkRule prmSkRule);

    PrmSkRule convert(PrmSkRuleSaveReqVO prmSkRuleSaveReq);

    PageResult<PrmSkRuleRespVO> convertPage(PageResult<PrmSkRule> prmSkRulePage);

    List<SkRuleDTO> convertListDTO(List<PrmSkRule> prmSkRules);

    // 定义字段手动映射规则
    PrmSkRuleItemExcelRespVO convert(PrmActivityItemPageVo prmActivityItemPageVo);

    default PrmSkRuleItemExcelRespVO updateFromItemData(PrmSkRuleImportExcel itemData, PrmSkRuleItemExcelRespVO respVO){
        if (itemData == null || respVO == null) {
            return respVO;
        }
        // 覆盖或补充属性
        respVO.setOnceLimit(itemData.getOnceLimit());
        respVO.setSeckillStock(itemData.getSeckillStock());
        respVO.setSeckillPrice(itemData.getSeckillPrice());
        respVO.setMidSeckillPrice(itemData.getMidSeckillPrice());
        respVO.setLargeSeckillPrice(itemData.getLargeSeckillPrice());
        // 根据实际字段补充逻辑
        return respVO;
    }
}