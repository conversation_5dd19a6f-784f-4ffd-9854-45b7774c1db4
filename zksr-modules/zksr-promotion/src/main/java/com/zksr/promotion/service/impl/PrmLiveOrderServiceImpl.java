package com.zksr.promotion.service.impl;

import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmLiveOrderMapper;
import com.zksr.promotion.convert.room.PrmLiveOrderConvert;
import com.zksr.promotion.domain.PrmLiveOrder;
import com.zksr.promotion.controller.order.vo.PrmLiveOrderPageReqVO;
import com.zksr.promotion.controller.order.vo.PrmLiveOrderSaveReqVO;
import com.zksr.promotion.service.IPrmLiveOrderService;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 直播订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Service
public class PrmLiveOrderServiceImpl implements IPrmLiveOrderService {
    @Autowired
    private PrmLiveOrderMapper prmLiveOrderMapper;

    @Autowired
    private RedisService redisService;

    /**
     * 新增直播订单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public String insertPrmLiveOrder(List<PrmLiveOrderSaveReqVO> createReqVO) {
        if(CollectionUtils.isEmpty(createReqVO)){
            return null;
        }

        String orderNo = SheetTypeConstants.XS + redisService.getUniqueNumber();
        createReqVO.forEach(i->{
            i.setOrderNo(orderNo);
        });

        // 插入
        List<PrmLiveOrder> prmLiveOrder = PrmLiveOrderConvert.INSTANCE.convert2List(createReqVO);
        prmLiveOrderMapper.insertBatch(prmLiveOrder);
        // 返回
        return orderNo;
    }

    /**
     * 修改直播订单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmLiveOrder(PrmLiveOrderSaveReqVO updateReqVO) {
        prmLiveOrderMapper.updateById(PrmLiveOrderConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除直播订单
     *
     * @param id ID主键
     */
    @Override
    public void deletePrmLiveOrder(Long id) {
        // 删除
        prmLiveOrderMapper.deleteById(id);
    }

    /**
     * 批量删除直播订单
     *
     * @param ids 需要删除的直播订单主键
     * @return 结果
     */
    @Override
    public void deletePrmLiveOrderByIds(Long[] ids) {
        for(Long id : ids){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deletePrmLiveOrder(id);
        }
    }

    /**
     * 获得直播订单
     *
     * @param id ID主键
     * @return 直播订单
     */
    @Override
    public PrmLiveOrder getPrmLiveOrder(Long id) {
        return prmLiveOrderMapper.selectById(id);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmLiveOrder> getPrmLiveOrderPage(PrmLiveOrderPageReqVO pageReqVO) {
        return prmLiveOrderMapper.selectPage(pageReqVO);
    }

    private void validatePrmLiveOrderExists(Long id) {
        if (prmLiveOrderMapper.selectById(id) == null) {
//            throw exception(PRM_LIVE_ORDER_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.promotion.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 直播订单 TODO 补充编号 ==========
    // ErrorCode PRM_LIVE_ORDER_NOT_EXISTS = new ErrorCode(TODO 补充编号, "直播订单不存在");


}
