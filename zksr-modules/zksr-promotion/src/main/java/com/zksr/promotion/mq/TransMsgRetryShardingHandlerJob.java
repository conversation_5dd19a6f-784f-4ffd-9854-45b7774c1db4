package com.zksr.promotion.mq;

import com.midea.mbf.txmq.core.handler.TransMsgRetryShardingHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: chenyj8
 * @Desciption: 类功能描述
 */
@Slf4j
@Component
public class TransMsgRetryShardingHandlerJob extends TransMsgRetryShardingHandler {

//    @XxlJob("transactionMsgRetryHandler")
    public void transactionMsgRetryHandlerTask(String queryCondition) {
        this.transactionMsgRetryHandler(queryCondition);
//        return ReturnT.SUCCESS;
    }

    /*
本地数据库增加表；


    CREATE TABLE `tc_transaction_msg_send` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `batch_no` bigint(20) DEFAULT NULL COMMENT '批次号',
  `busi_class` varchar(100) DEFAULT NULL COMMENT '类',
  `busi_method` varchar(100) DEFAULT NULL COMMENT '方法',
  `mq_topic` varchar(60) DEFAULT NULL COMMENT 'mq->topic',
  `mq_tag` varchar(60) DEFAULT NULL COMMENT 'mq->tag',
  `message` blob DEFAULT NULL COMMENT '消息',
  `message_body` varchar(2000)  DEFAULT NULL COMMENT '消息体',
  `ext_info` varchar(2000) DEFAULT NULL COMMENT '消息头信息',
  `message_key` varchar(60) DEFAULT NULL COMMENT 'msgKey',
  `status` tinyint(2) DEFAULT NULL COMMENT '状态：0 待处理 1：处理成功 2：处理失败',
  `send_type` varchar(20) DEFAULT NULL COMMENT 'asyn,synSend',
  `op_type` tinyint(2) DEFAULT 0 COMMENT '状态：0：生产 1：消费',
  `retry_times` tinyint(3) DEFAULT 0 COMMENT '重试次数',
  `msg_id` varchar(100) DEFAULT NULL COMMENT '消息id',
  `first_send_time` datetime DEFAULT NULL COMMENT '第一次发送时间',
  `last_send_time` datetime DEFAULT NULL COMMENT '最后一次发送时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `timeout` varchar(20)   DEFAULT NULL COMMENT '超时时间',
  `sharding_key` varchar(60)   DEFAULT NULL COMMENT '分库键',
  `hash_key` varchar(60)  DEFAULT NULL COMMENT '订单号或仓库编码，实现同一个订单号或仓库编码分发到同一个分区队列里面，保证顺序性',
  `delay_level` tinyint(2) DEFAULT NULL COMMENT '延迟等级',
 `data_source_name` varchar(32)  DEFAULT NULL COMMENT '所在分库名',
  PRIMARY KEY (`id`),
  KEY `ix_ttm_mi` (`msg_id`)
) COMMENT='事务消息表';


nacos配置：

##默认false-表示消息和本地事务一起持久化  true-表示不持久化消息，只有消息发送失败时才会持久化消息，提高组件性能；
mbf:
  txmq:
    close-persist: false # 全局是否消息落库


指定事务消息默认库配置：

mbf:
  txmq:
    enabled: true
    id-type: id4j # auto-increment 消息落库使用id自增  id4j 使用雪花算法id
    sharding:
      defaultDb: salesmart-amc-r,salesmart-amc ## 逻辑库，实际库  （分库场景需加此配置）
    close-persist: false ##本配置可不添加，不配置则默认false (false-持久化  true-不持久化)

mbf:
  txmq:
    enabled: true
    id-type: auto-increment # auto-increment 消息落库使用id自增  id4j 使用雪花算法id
    close-persist: false #本配置可不添加，不配置则默认false (false-持久化  true-不持久化)
    persist-table-name: tc_transaction_msg_send #默认表名是tc_transaction_msg_send





    方法上添加注解，@TxMsgPersistSwitcher注解，true-落库 false-不落库

 @Target({ElementType.METHOD, ElementType.TYPE})
 @Retention(RetentionPolicy.RUNTIME)
 @Documented
 public @interface TxMsgPersistSwitcher {
 /**
 * 事务消息是否库,默认 false
 *
 * @return true-落库 false-不落库

    boolean value() default false;

}



     */
}
