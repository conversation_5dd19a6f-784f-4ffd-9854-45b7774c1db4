package com.zksr.promotion.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmActivityChannelScopeMapper;
import com.zksr.promotion.convert.scope.PrmActivityChannelScopeConvert;
import com.zksr.promotion.domain.PrmActivityChannelScope;
import com.zksr.promotion.controller.scope.vo.PrmActivityChannelScopePageReqVO;
import com.zksr.promotion.controller.scope.vo.PrmActivityChannelScopeSaveReqVO;
import com.zksr.promotion.service.IPrmActivityChannelScopeService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 促销活动渠道适用范围Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Service
public class PrmActivityChannelScopeServiceImpl implements IPrmActivityChannelScopeService {
    @Autowired
    private PrmActivityChannelScopeMapper prmActivityChannelScopeMapper;

    /**
     * 新增促销活动渠道适用范围
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrmActivityChannelScope(PrmActivityChannelScopeSaveReqVO createReqVO) {
        // 插入
        PrmActivityChannelScope prmActivityChannelScope = PrmActivityChannelScopeConvert.INSTANCE.convert(createReqVO);
        prmActivityChannelScopeMapper.insert(prmActivityChannelScope);
        // 返回
        return prmActivityChannelScope.getSysCode();
    }

    /**
     * 修改促销活动渠道适用范围
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmActivityChannelScope(PrmActivityChannelScopeSaveReqVO updateReqVO) {
        prmActivityChannelScopeMapper.updateById(PrmActivityChannelScopeConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除促销活动渠道适用范围
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deletePrmActivityChannelScope(Long sysCode) {
        // 删除
        prmActivityChannelScopeMapper.deleteById(sysCode);
    }

    /**
     * 批量删除促销活动渠道适用范围
     *
     * @param sysCodes 需要删除的促销活动渠道适用范围主键
     * @return 结果
     */
    @Override
    public void deletePrmActivityChannelScopeBySysCodes(Long[] sysCodes) {
        for(Long sysCode : sysCodes){
            this.deletePrmActivityChannelScope(sysCode);
        }
    }

    /**
     * 获得促销活动渠道适用范围
     *
     * @param sysCode 平台商id
     * @return 促销活动渠道适用范围
     */
    @Override
    public PrmActivityChannelScope getPrmActivityChannelScope(Long sysCode) {
        return prmActivityChannelScopeMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmActivityChannelScope> getPrmActivityChannelScopePage(PrmActivityChannelScopePageReqVO pageReqVO) {
        return prmActivityChannelScopeMapper.selectPage(pageReqVO);
    }

}
