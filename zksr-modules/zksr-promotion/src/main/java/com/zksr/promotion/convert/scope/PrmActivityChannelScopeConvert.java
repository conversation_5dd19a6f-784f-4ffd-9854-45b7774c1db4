package com.zksr.promotion.convert.scope;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.ActivityChannelScopeDTO;
import com.zksr.promotion.domain.PrmActivityChannelScope;
import com.zksr.promotion.controller.scope.vo.PrmActivityChannelScopeRespVO;
import com.zksr.promotion.controller.scope.vo.PrmActivityChannelScopeSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 促销活动渠道适用范围 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-13
*/
@Mapper
public interface PrmActivityChannelScopeConvert {

    PrmActivityChannelScopeConvert INSTANCE = Mappers.getMapper(PrmActivityChannelScopeConvert.class);

    PrmActivityChannelScopeRespVO convert(PrmActivityChannelScope prmActivityChannelScope);

    PrmActivityChannelScope convert(PrmActivityChannelScopeSaveReqVO prmActivityChannelScopeSaveReq);

    PageResult<PrmActivityChannelScopeRespVO> convertPage(PageResult<PrmActivityChannelScope> prmActivityChannelScopePage);

    List<ActivityChannelScopeDTO> convert(List<PrmActivityChannelScope> activityChannelScopes);
}