package com.zksr.promotion.service;

import com.zksr.product.api.materialApply.dto.MaterialApplyDTO;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityCheckScopeVO;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityScopeVO;
import com.zksr.promotion.controller.rule.vo.PrmActivityWhiteOrBlackVO;
import com.zksr.promotion.domain.PrmActivity;
import com.zksr.promotion.domain.excel.PrmBranchImportExcel;
import com.zksr.promotion.domain.excel.PrmItemImportExcel;

import java.util.List;
import java.util.Map;

/**
 * @Description: 促销公共service
 * @Author: liuxingyu
 * @Date: 2024/5/21 15:10
 */
public interface IPrmActivityCommonService {
    /**
     * @Description: 变更促销活动状态
     * @Author: liuxingyu
     * @Date: 2024/5/18 15:54
     */
    Long changeStatus(Long activityId, Integer status);

    /**
     * @Description: 校验是否能够编辑
     * @Author: liuxingyu
     * @Date: 2024/5/18 16:30
     */
    PrmActivity checkUpdate(Long activityId);

    /**
     * @Description: ps:直接使用注意字段不能为空,新增促销活动校验保存状态（prmStatus保存:0 保存并启用:1）
     * @Author: liuxingyu
     * @Date: 2024/5/18 16:55
     */
    PrmActivity checkStatusByInsert(PrmActivity prmActivity);

    /**
     * @Description: ps:直接使用注意字段不能为空,校验类型(同入驻商时间段内同类型 ( spuScope)不可冲突, 若是全场:0 则直接提示错误,若非全场:0 则校验品牌/类别/商品是否重复,重复则提示错误)
     * @Author: liuxingyu
     * @Date: 2024/5/18 17:00
     */
    void checkScope(PrmActivity activity, List<PrmActivityWhiteOrBlackVO> applyIdList);

    /**
     * @Description: 新增促销活动校验
     * @Author: liuxingyu
     * @Date: 2024/5/18 17:53
     */
    PrmActivity activityChangeCheck(PrmActivityCheckScopeVO prmActivityCheckScopeVO);

    /**
    * @Description: 促销活动绑定关联数据
    * @Author: liuxingyu
    * @Date: 2024/5/21 15:27
    */
    void bindScope(PrmActivityScopeVO prmActivityScopeVO);

    /**
    * @Description: 根据促销活动ID获取关联数据（所需字段 促销ID:activityId 是否指定门店参与:branchScopeAllFlag 是否指定渠道参与:chanelScopeAllFlag）
    * @Author: liuxingyu
    * @Date: 2024/5/21 15:43
    */
    PrmActivityScopeVO getScope(PrmActivitySaveReqVO activity);

    /**
     * 促销活动导入门店信息 公用导入门店方法
     * @return
     */
    Map<String, Object> importPrmBranchsData(List<PrmBranchImportExcel> prmBranchImportExcel);

    /**
     * 促销活动导入上架商品信息 公用导入商品信息方法
     * @return
     */
    Map<String, Object> importPrmItemsData(List<PrmItemImportExcel> prmItemList, List<String> supplierIds);

    /**
     * 促销公用 新增素材应用信息
     * @param dto
     */
    void addMaterialApply(MaterialApplyDTO dto);

    /**
     * 促销公用 修改素材应用信息
     * @param dto
     */
    void editMaterialApply(MaterialApplyDTO dto);

    /**
     * 促销公用 获取生效中的素材应用信息
     * @param applyIds
     */
    List<MaterialApplyDTO> getByMaterialApplyByApplyIds(List<Long> applyIds);

    /**
     * 促销公用 获取促销活动的素材应用信息(带素材信息)
     * @param vo
     */
    MaterialApplyVO getByMaterialApplyByMaterial(MaterialApplyVO vo);
}
