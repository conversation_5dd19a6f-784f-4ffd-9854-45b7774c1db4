package com.zksr.promotion.controller.rule.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2024/5/16 16:56
 */
@Data
@ApiModel("买赠活动详情对象")
public class PrmBgActivityRespVo extends PrmActivityRespVO {

    @ApiModelProperty("满赠规则列表")
    private List<PrmBgRuleRespVO> prmBgRuleRespVOList;

    @ApiModelProperty("区域城市列表")
    private List<PrmActivityWhiteOrBlackVO> areaIdList;

    @ApiModelProperty("门店列表")
    private List<PrmActivityWhiteOrBlackVO> branchIdList;

    @ApiModelProperty("渠道列表")
    private List<PrmActivityWhiteOrBlackVO> channelIdList;

    @ApiModelProperty("spu列表")
    private List<PrmActivityWhiteOrBlackVO> spuIdList;
}
