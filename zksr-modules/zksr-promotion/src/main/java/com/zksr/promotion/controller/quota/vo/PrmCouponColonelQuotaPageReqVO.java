package com.zksr.promotion.controller.quota.vo;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 业务员发券额度对象 prm_coupon_colonel_quota
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@ApiModel("业务员发券额度 - prm_coupon_colonel_quota分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmCouponColonelQuotaPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商ID */
    private Long sysCode;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    private String colonelId;

    /** 月份ID，仅1-实例额度 */
    private Integer monthId;

    @ApiModelProperty(value = "业务员手机号")
    private String colonelPhone;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "状态 1-正常 0-停用")
    private Integer status;

    @ApiModelProperty(value = "当前月份")
    private Integer currentMonthId;

    /** 本月发券额度 */
    @Excel(name = "本月发券额度")
    private BigDecimal quota;

    /** 0-模板额度 1-实例额度 */
    private Integer quotaType;
}
