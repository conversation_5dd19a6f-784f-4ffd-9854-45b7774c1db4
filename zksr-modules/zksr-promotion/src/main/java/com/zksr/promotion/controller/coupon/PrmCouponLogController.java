package com.zksr.promotion.controller.coupon;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.domain.PrmCouponLog;
import com.zksr.promotion.service.IPrmCouponLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.promotion.controller.coupon.vo.PrmCouponLogPageReqVO;
import com.zksr.promotion.controller.coupon.vo.PrmCouponLogSaveReqVO;
import com.zksr.promotion.controller.coupon.vo.PrmCouponLogRespVO;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 优惠券日志Controller
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
@Api(tags = "管理后台 - 优惠券日志接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/coupon-log")
public class PrmCouponLogController {
    @Autowired
    private IPrmCouponLogService prmCouponLogService;

    /**
     * 新增优惠券日志
     */
    @ApiOperation(value = "新增优惠券日志", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "优惠券日志", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmCouponLogSaveReqVO createReqVO) {
        return success(prmCouponLogService.insertPrmCouponLog(createReqVO));
    }

    /**
     * 修改优惠券日志
     */
    @ApiOperation(value = "修改优惠券日志", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "优惠券日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmCouponLogSaveReqVO updateReqVO) {
            prmCouponLogService.updatePrmCouponLog(updateReqVO);
        return success(true);
    }

    /**
     * 删除优惠券日志
     */
    @ApiOperation(value = "删除优惠券日志", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "优惠券日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{couponLogIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] couponLogIds) {
        prmCouponLogService.deletePrmCouponLogByCouponLogIds(couponLogIds);
        return success(true);
    }

    /**
     * 获取优惠券日志详细信息
     */
    @ApiOperation(value = "获得优惠券日志详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{couponLogId}")
    public CommonResult<PrmCouponLogRespVO> getInfo(@PathVariable("couponLogId") Long couponLogId) {
        PrmCouponLog prmCouponLog = prmCouponLogService.getPrmCouponLog(couponLogId);
        return success(HutoolBeanUtils.toBean(prmCouponLog, PrmCouponLogRespVO.class));
    }

    /**
     * 分页查询优惠券日志
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得优惠券日志分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmCouponLogRespVO>> getPage(@Valid PrmCouponLogPageReqVO pageReqVO) {
        PageResult<PrmCouponLog> pageResult = prmCouponLogService.getPrmCouponLogPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, PrmCouponLogRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:coupon-log:add";
        /** 编辑 */
        public static final String EDIT = "promotion:coupon-log:edit";
        /** 删除 */
        public static final String DELETE = "promotion:coupon-log:remove";
        /** 列表 */
        public static final String LIST = "promotion:coupon-log:list";
        /** 查询 */
        public static final String GET = "promotion:coupon-log:query";
        /** 停用 */
        public static final String DISABLE = "promotion:coupon-log:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:coupon-log:enable";
    }
}
