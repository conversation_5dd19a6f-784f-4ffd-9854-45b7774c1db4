package com.zksr.promotion.controller.rule;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.domain.PrmCouponTemplateReturnRule;
import com.zksr.promotion.service.IPrmCouponTemplateReturnRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateReturnRulePageReqVO;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateReturnRuleSaveReqVO;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateReturnRuleRespVO;
import com.zksr.promotion.convert.rule.PrmCouponTemplateReturnRuleConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 优惠券模板-下单返券规则（待讨论）Controller
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@Api(tags = "管理后台 - 优惠券模板-下单返券规则（待讨论）接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/returnRule")
public class PrmCouponTemplateReturnRuleController {
    @Autowired
    private IPrmCouponTemplateReturnRuleService prmCouponTemplateReturnRuleService;

    /**
     * 新增优惠券模板-下单返券规则（待讨论）
     */
    @ApiOperation(value = "新增优惠券模板-下单返券规则（待讨论）", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "优惠券模板-下单返券规则（待讨论）", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmCouponTemplateReturnRuleSaveReqVO createReqVO) {
        return success(prmCouponTemplateReturnRuleService.insertPrmCouponTemplateReturnRule(createReqVO));
    }

    /**
     * 修改优惠券模板-下单返券规则（待讨论）
     */
    @ApiOperation(value = "修改优惠券模板-下单返券规则（待讨论）", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "优惠券模板-下单返券规则（待讨论）", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmCouponTemplateReturnRuleSaveReqVO updateReqVO) {
            prmCouponTemplateReturnRuleService.updatePrmCouponTemplateReturnRule(updateReqVO);
        return success(true);
    }

    /**
     * 删除优惠券模板-下单返券规则（待讨论）
     */
    @ApiOperation(value = "删除优惠券模板-下单返券规则（待讨论）", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "优惠券模板-下单返券规则（待讨论）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{couponTemplateReturnRuleIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] couponTemplateReturnRuleIds) {
        prmCouponTemplateReturnRuleService.deletePrmCouponTemplateReturnRuleByCouponTemplateReturnRuleIds(couponTemplateReturnRuleIds);
        return success(true);
    }

    /**
     * 获取优惠券模板-下单返券规则（待讨论）详细信息
     */
    @ApiOperation(value = "获得优惠券模板-下单返券规则（待讨论）详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{couponTemplateReturnRuleId}")
    public CommonResult<PrmCouponTemplateReturnRuleRespVO> getInfo(@PathVariable("couponTemplateReturnRuleId") Long couponTemplateReturnRuleId) {
        PrmCouponTemplateReturnRule prmCouponTemplateReturnRule = prmCouponTemplateReturnRuleService.getPrmCouponTemplateReturnRule(couponTemplateReturnRuleId);
        return success(PrmCouponTemplateReturnRuleConvert.INSTANCE.convert(prmCouponTemplateReturnRule));
    }

    /**
     * 分页查询优惠券模板-下单返券规则（待讨论）
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得优惠券模板-下单返券规则（待讨论）分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmCouponTemplateReturnRuleRespVO>> getPage(@Valid PrmCouponTemplateReturnRulePageReqVO pageReqVO) {
        PageResult<PrmCouponTemplateReturnRule> pageResult = prmCouponTemplateReturnRuleService.getPrmCouponTemplateReturnRulePage(pageReqVO);
        return success(PrmCouponTemplateReturnRuleConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:rule:add";
        /** 编辑 */
        public static final String EDIT = "promotion:rule:edit";
        /** 删除 */
        public static final String DELETE = "promotion:rule:remove";
        /** 列表 */
        public static final String LIST = "promotion:rule:list";
        /** 查询 */
        public static final String GET = "promotion:rule:query";
        /** 停用 */
        public static final String DISABLE = "promotion:rule:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:rule:enable";
    }
}
