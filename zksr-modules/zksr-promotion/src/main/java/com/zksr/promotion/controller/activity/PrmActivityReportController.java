package com.zksr.promotion.controller.activity;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.controller.activity.dto.PrmActivityReportRespDTO;
import com.zksr.promotion.controller.activity.vo.PrmActivityReportPageVO;
import com.zksr.promotion.service.IPrmActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 促销活动统计接口
 */
@Api(tags = "管理后台 - 促销活动统计接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/activityReport")
public class PrmActivityReportController {
    @Autowired
    private IPrmActivityService prmActivityService;

    @GetMapping("/getActivityReportPage")
    @ApiOperation(value = "分页查询促销活动使用报表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(supplierAlias = "`pa`", dcAlias = "`pa`", dcFieldAlias = SystemConstants.SUPPLIER_ID)
    public CommonResult<PageResult<PrmActivityReportRespDTO>> getActivityReportPage(@Valid PrmActivityReportPageVO pageReqVO) {
        return success(prmActivityService.getActivityReportPage(pageReqVO));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 列表 */
        public static final String LIST = "promotion:activityReport:list";
        /** 查询 */
        public static final String GET = "promotion:activityReport:query";
    }
}
