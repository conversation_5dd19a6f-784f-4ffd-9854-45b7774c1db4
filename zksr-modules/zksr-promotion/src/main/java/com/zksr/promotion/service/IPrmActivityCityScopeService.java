package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.domain.PrmActivityCityScope;
import com.zksr.promotion.controller.scope.vo.PrmActivityCityScopePageReqVO;
import com.zksr.promotion.controller.scope.vo.PrmActivityCityScopeSaveReqVO;

/**
 * 促销活动城市适用范围Service接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
public interface IPrmActivityCityScopeService {

    /**
     * 新增促销活动城市适用范围
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmActivityCityScope(@Valid PrmActivityCityScopeSaveReqVO createReqVO);

    /**
     * 修改促销活动城市适用范围
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmActivityCityScope(@Valid PrmActivityCityScopeSaveReqVO updateReqVO);

    /**
     * 删除促销活动城市适用范围
     *
     * @param sysCode 平台商id
     */
    public void deletePrmActivityCityScope(Long sysCode);

    /**
     * 批量删除促销活动城市适用范围
     *
     * @param sysCodes 需要删除的促销活动城市适用范围主键集合
     * @return 结果
     */
    public void deletePrmActivityCityScopeBySysCodes(Long[] sysCodes);

    /**
     * 获得促销活动城市适用范围
     *
     * @param sysCode 平台商id
     * @return 促销活动城市适用范围
     */
    public PrmActivityCityScope getPrmActivityCityScope(Long sysCode);

    /**
     * 获得促销活动城市适用范围分页
     *
     * @param pageReqVO 分页查询
     * @return 促销活动城市适用范围分页
     */
    PageResult<PrmActivityCityScope> getPrmActivityCityScopePage(PrmActivityCityScopePageReqVO pageReqVO);

}
