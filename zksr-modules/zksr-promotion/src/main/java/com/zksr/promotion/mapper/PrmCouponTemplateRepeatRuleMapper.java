package com.zksr.promotion.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCouponTemplateRepeatRule;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateRepeatRulePageReqVO;


/**
 * 优惠券模板-重复规则Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Mapper
public interface PrmCouponTemplateRepeatRuleMapper extends BaseMapperX<PrmCouponTemplateRepeatRule> {
    default PageResult<PrmCouponTemplateRepeatRule> selectPage(PrmCouponTemplateRepeatRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmCouponTemplateRepeatRule>()
                    .eqIfPresent(PrmCouponTemplateRepeatRule::getCouponTemplateRepeatRuleId, reqVO.getCouponTemplateRepeatRuleId())
                    .eqIfPresent(PrmCouponTemplateRepeatRule::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmCouponTemplateRepeatRule::getCouponTemplateId, reqVO.getCouponTemplateId())
                    .eqIfPresent(PrmCouponTemplateRepeatRule::getRepeatType, reqVO.getRepeatType())
                    .eqIfPresent(PrmCouponTemplateRepeatRule::getRepeatFrequencyUnit, reqVO.getRepeatFrequencyUnit())
                    .eqIfPresent(PrmCouponTemplateRepeatRule::getRepeatFrequencyPeriod, reqVO.getRepeatFrequencyPeriod())
                    .eqIfPresent(PrmCouponTemplateRepeatRule::getRepaeatTimes, reqVO.getRepaeatTimes())
                .orderByDesc(PrmCouponTemplateRepeatRule::getCouponTemplateRepeatRuleId));
    }

    default PrmCouponTemplateRepeatRule selectPrmCouponTemplateRepeatRuleByTemplateId(Long couponTemplateId) {
        return selectOne( new LambdaQueryWrapperX<PrmCouponTemplateRepeatRule>()
                .eqIfPresent(PrmCouponTemplateRepeatRule::getCouponTemplateId, couponTemplateId));
    }

    default void deleteByTemplateId(Long couponTemplateId) {
        delete(new LambdaQueryWrapperX<PrmCouponTemplateRepeatRule>()
                .eqIfPresent(PrmCouponTemplateRepeatRule::getCouponTemplateId, couponTemplateId) );
    }
}
