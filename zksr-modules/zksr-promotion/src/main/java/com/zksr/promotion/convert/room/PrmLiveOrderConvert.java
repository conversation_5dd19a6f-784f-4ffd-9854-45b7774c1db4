package com.zksr.promotion.convert.room;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmLiveOrder;
import com.zksr.promotion.controller.order.vo.PrmLiveOrderRespVO;
import com.zksr.promotion.controller.order.vo.PrmLiveOrderSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 直播订单 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-03-28
*/
@Mapper
public interface PrmLiveOrderConvert {

    PrmLiveOrderConvert INSTANCE = Mappers.getMapper(PrmLiveOrderConvert.class);

    PrmLiveOrderRespVO convert(PrmLiveOrder prmLiveOrder);

    PrmLiveOrder convert(PrmLiveOrderSaveReqVO prmLiveOrderSaveReq);
    List<PrmLiveOrder> convert2List(List<PrmLiveOrderSaveReqVO> prmLiveOrderSaveReq);

    PageResult<PrmLiveOrderRespVO> convertPage(PageResult<PrmLiveOrder> prmLiveOrderPage);
}