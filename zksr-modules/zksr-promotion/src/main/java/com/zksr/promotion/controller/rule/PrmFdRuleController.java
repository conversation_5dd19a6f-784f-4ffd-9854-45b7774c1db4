package com.zksr.promotion.controller.rule;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.exception.enums.GlobalErrorCodeConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.material.vo.MaterialCacheVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityPageReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.rule.dto.PrmFdRuleDTO;
import com.zksr.promotion.controller.rule.dto.UploadFileForm;
import com.zksr.promotion.controller.rule.vo.PrmFdRuleRespVO;
import com.zksr.promotion.domain.excel.PrmBranchImportExcel;
import com.zksr.promotion.domain.excel.PrmItemImportExcel;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPrmFdRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_1;
import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_2;
import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 满减活动规则Controller
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Api(tags = "管理后台 - 满减活动规则接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/fdRule")
public class PrmFdRuleController {
    @Autowired
    private IPrmFdRuleService prmFdRuleService;

    @Autowired
    private IPrmActivityService prmActivityService;

    @Autowired
    private IPrmActivityCommonService prmActivityCommonService;

    @Autowired
    private Cache<String, MaterialCacheVO> materialCache;
    /**
     * 新增满减活动规则
     */
    @ApiOperation(value = "新增满减活动规则", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "满减活动规则", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmFdRuleDTO prmFdRuleDTO) {
        Long activityId = prmFdRuleService.insertPrmFdRule(prmFdRuleDTO);
        materialCache.remove(MaterialCacheVO.getActivityCacheKey(activityId));
        return success(activityId);
    }

    /**
     * 修改满减活动规则
     */
    @ApiOperation(value = "修改满减活动规则", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "满减活动规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmFdRuleDTO prmFdRuleDTO) {
        prmFdRuleService.updatePrmFdRule(prmFdRuleDTO);

        // 刷新缓存
        if (Objects.nonNull(prmFdRuleDTO.getPrmStatus())) {
            prmActivityService.reloadSupplierActivity(prmFdRuleDTO.getActivityId());

            materialCache.remove(MaterialCacheVO.getActivityCacheKey(prmFdRuleDTO.getActivityId()));
        }
        return success(true);
    }



    @ApiOperation(value = "启用/停用满减活动", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "启用满减活动", businessType = BusinessType.UPDATE)
    @PutMapping("/isEnableActivity")
    public CommonResult<Boolean> isEnableActivity(@Valid @RequestBody PrmFdRuleDTO isEnable) {
        prmFdRuleService.isEnableActivity(isEnable);
        // 刷新缓存
        prmActivityService.reloadSupplierActivity(isEnable.getActivityId());
        return success(true);
    }

    /**
     * 删除满减活动规则
     */
    @ApiOperation(value = "删除满减活动规则", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "满减活动规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{fdRuleIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] fdRuleIds) {
        prmFdRuleService.deletePrmFdRuleByFdRuleIds(fdRuleIds);
        return success(true);
    }

    /**
     * 获取满减活动规则详细信息
     */
    @ApiOperation(value = "获得满减活动规则详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{activityId}")
    public CommonResult<PrmFdRuleRespVO> getInfo(@PathVariable("activityId") Long activityId) {
        return success(prmFdRuleService.getPrmFdRule(activityId));
    }

    /**
     * 分页查询满减活动规则
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得满减活动规则分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(supplierAlias = "`prm_activity`", dcAlias = "`prm_activity`", dcFieldAlias = SystemConstants.SUPPLIER_ID)
    public CommonResult<PageResult<PrmActivityRespVO>> getPage(@Valid PrmActivityPageReqVO pageReqVO) {
        pageReqVO.setFuncScope(ObjectUtil.isNull(SecurityUtils.getLoginUser().getDcId()) ? PRM_FUNC_SCOPE_1 : PRM_FUNC_SCOPE_2);
        pageReqVO.setPrmNo(PrmNoEnum.FD.getType());
        return success(prmFdRuleService.getPrmFdRulePage(pageReqVO));

    }

    @ApiOperation(value = "满减黑白名单导入商品", httpMethod = HttpMethod.POST,notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORTITEM)
    @Log(title = "满减黑白名单导入商品", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORTITEM)
    @PostMapping("/importData")
    public CommonResult<Map<String, Object>> importData(@ModelAttribute UploadFileForm uploadFileForm) throws Exception
    {
        MultipartFile file = uploadFileForm.getFile();
        List<String> supplierIds = uploadFileForm.getSupplierIds();
        ExcelUtil<PrmItemImportExcel> util = new ExcelUtil<>(PrmItemImportExcel.class);
        List<PrmItemImportExcel> prmItemList = util.importExcel(file.getInputStream(),0);
        // 调用服务层方法
        Map<String, Object> importResult = prmActivityCommonService.importPrmItemsData(prmItemList, supplierIds);
        CommonResult<Map<String, Object>> result = new CommonResult<>();
        result.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        result.setData(importResult);
        result.setMsg((String) importResult.get("message"));
        return result;
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载商品信息模板", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<PrmItemImportExcel> util = new ExcelUtil<>(PrmItemImportExcel.class);
        util.importTemplateExcel(response, "商品信息导入", StringUtils.EMPTY, null);
    }

    @ApiOperation(value = "满减黑白名单导入门店", httpMethod = HttpMethod.POST,notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORTBRANCH)
    @Log(title = "满减黑白名单导入门店", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORTBRANCH)
    @PostMapping("/importBranchData")
    public CommonResult<Map<String, Object>> importBranchData(MultipartFile file) throws Exception
    {
        ExcelUtil<PrmBranchImportExcel> util = new ExcelUtil<>(PrmBranchImportExcel.class);
        List<PrmBranchImportExcel> prmBranchList = util.importExcel(file.getInputStream(),0);
        // 调用服务层方法
        Map<String, Object> importResult = prmActivityCommonService.importPrmBranchsData(prmBranchList);
        CommonResult<Map<String, Object>> result = new CommonResult<>();
        result.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        result.setData(importResult);
        result.setMsg((String) importResult.get("message"));
        return result;
    }

    @PostMapping("/importBranchTemplate")
    @ApiOperation(value = "下载导入门店信息模板", httpMethod = HttpMethod.POST)
    public void importBranchTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<PrmBranchImportExcel> util = new ExcelUtil<>(PrmBranchImportExcel.class);
        util.importTemplateExcel(response, "导入门店信息导入", StringUtils.EMPTY, null);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:activity:fd-rule:add";
        /** 编辑 */
        public static final String EDIT = "promotion:activity:fd-rule:edit";
        /** 删除 */
        public static final String DELETE = "promotion:activity:fd-rule:remove";
        /** 列表 */
        public static final String LIST = "promotion:activity:fd-rule:list";
        /** 查询 */
        public static final String GET = "promotion:activity:fd-rule:query";
        /** 停用 */
        public static final String DISABLE = "promotion:activity:fd-rule:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:activity:fd-rule:enable";
        /** 导入商品 */
        public static final String IMPORTITEM = "promotion:activity:fd-rule:importItem";
        /** 导入门店 */
        public static final String IMPORTBRANCH = "promotion:activity:fd-rule:importBranch";
    }
}
