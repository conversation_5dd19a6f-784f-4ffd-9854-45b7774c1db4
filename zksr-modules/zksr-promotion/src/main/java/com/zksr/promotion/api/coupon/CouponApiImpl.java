package com.zksr.promotion.api.coupon;

import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.config.CustomIdGenerator;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.promotion.api.coupon.dto.*;
import com.zksr.promotion.api.coupon.vo.*;
import com.zksr.promotion.controller.coupon.vo.PrmCouponPageReqVO;
import com.zksr.promotion.controller.coupon.vo.PrmCouponRespVO;
import com.zksr.promotion.domain.*;
import com.zksr.promotion.api.coupon.vo.NormalCouponReceiveSingleAsyncReqVo;
import com.zksr.promotion.domain.PrmCoupon;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplatePageReqVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateRespVO;
import com.zksr.promotion.api.coupon.vo.NormalCouponReceiveSingleAsyncReqVo;
import com.zksr.promotion.domain.PrmCoupon;
import com.zksr.promotion.mapper.PrmCouponColonelQuotaMapper;
import com.zksr.promotion.mapper.PrmCouponMapper;
import com.zksr.promotion.mq.PromotionMqProducer;
import com.zksr.promotion.service.IPrmCouponColonelQuotaService;
import com.zksr.promotion.service.IPrmCouponService;
import com.zksr.promotion.service.IPrmCouponTemplateExtendService;
import com.zksr.promotion.service.IPrmCouponTemplateService;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/4/1 10:42
 */
@RestController
@InnerAuth
public class CouponApiImpl implements CouponApi{

    @Autowired
    private IPrmCouponTemplateService couponTemplateService;

    @Autowired
    private PromotionMqProducer promotionMqProducer;

    @Autowired
    private CustomIdGenerator generator;

    @Autowired
    private IPrmCouponService couponService;

    @Autowired
    private IPrmCouponTemplateExtendService couponTemplateExtendService;

    @Autowired
    private PrmCouponColonelQuotaMapper prmCouponColonelQuotaMapper;

    @Autowired
    private IPrmCouponTemplateService prmCouponTemplateService;

    @Autowired
    private PrmCouponMapper prmCouponMapper;

    @Override
    public CommonResult<CouponTemplateDTO> getCouponTemplate(Long couponTemplateId) {
        return CommonResult.success(couponTemplateService.getPrmCouponTemplateCacheDTO(couponTemplateId));
    }

    @Override
    public CommonResult<List<CouponTemplateDTO>> getTotalStockCouponTemplateList(Long minId) {
        return CommonResult.success(couponTemplateService.getTotalStockCouponTemplateList(minId));
    }

    @Override
    public void updateCouponTemplateTotal(Integer receiveCount, Long couponTemplateId) {
        couponTemplateService.updateCouponTemplateTotal(receiveCount, couponTemplateId);
    }

    @Override
    public CommonResult<List<CouponReceiveStatusDTO>> receiveCoupon(CouponReceiveDTO couponReceive) {
        // 增加绑定状态ID
        ArrayList<CouponReceiveStatusDTO> receiveStatusDTOS = new ArrayList<>();
        for (Long couponTemplateId : couponReceive.getCouponTemplateIds()) {
            Long statusId = generator.nextId();
            couponReceive.getCouponStatusMap().put(couponTemplateId, statusId);
            // 返回数据
            receiveStatusDTOS.add( new CouponReceiveStatusDTO( couponTemplateId, statusId ));
        }
        promotionMqProducer.sendCouponReceiveEvent(couponReceive);
        return CommonResult.success(receiveStatusDTOS);
    }

    @Override
    public CommonResult<List<CouponReceiveStatusDTO>> receiveCoupon2(CouponReceiveDTO couponReceive) {
        promotionMqProducer.sendCouponReceiveEvent(couponReceive,true);
        return CommonResult.success(new ArrayList<CouponReceiveStatusDTO>());
    }

    @Override
    public CommonResult<List<CouponReceiveStatusDTO>> receiveCouponNotCheck(CouponReceiveDTO couponReceive) {
        couponReceive.setMode(NumberPool.INT_ONE);
        return receiveCoupon(couponReceive);
    }

    @Override
    public PageResult<CouponDTO> getCouponList(CouponPageReqVO receiveReq) {
        return couponService.getBranchCouponListPage(receiveReq);
    }

    @Override
    public CommonResult<List<CouponSpuScopeDTO>> getCouponSpuScopeList(Long couponTemplateId) {
        PrmCouponTemplate prmCouponTemplate = couponTemplateService.getPrmCouponTemplate(couponTemplateId);
        return CommonResult.success(couponTemplateService.getApplySpuScopeList(prmCouponTemplate));
    }

    @Override
    public CommonResult<List<CouponReceiveScopeDTO>> getCouponReceiveScopeList(Long couponTemplateId) {
        PrmCouponTemplate prmCouponTemplate = couponTemplateService.getPrmCouponTemplate(couponTemplateId);
        return CommonResult.success(couponTemplateService.getApplyReceiveScopeList(prmCouponTemplate));
    }

    @Override
    public CommonResult<List<CouponDTO>> getCouponListByBranchIdAndState(Long branchId, Integer couponState) {
        return CommonResult.success(couponService.getCouponListByBranchIdAndState(branchId, couponState));
    }

    @Override
    public CommonResult<List<CouponDTO>> getCouponListByCouponIds(CouponBatchListVO couponBatchListVO) {
        return CommonResult.success(couponService.getCouponBatch(couponBatchListVO.getBranchId(), couponBatchListVO.getCouponIdList()));
    }

    @Override
    public CommonResult<Boolean> applyCoupon(List<CouponApplyDTO> couponList) {
        couponService.applyCoupon(couponList);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> returnCoupon(CouponReturnDTO couponReturn) {
        couponService.returnCoupon(couponReturn);
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 更新优惠券统计数据( 订单数据 )
     * @param extendTotalVOList
     */
    @Override
    public void updateCouponTemplateOrderExtend(List<CouponTemplateExtendDTO> extendTotalVOList) {
        couponTemplateExtendService.updateCouponTemplateOrderExtend(extendTotalVOList);
    }

    /**
     * 更新优惠券统计数据( 领取数据 )
     * @param couponTemplateIdList
     */
    @Override
    public void updateCouponTemplateCouponExtend(List<Long> couponTemplateIdList) {
        couponTemplateExtendService.updateCouponTemplateCouponExtend(couponTemplateIdList);
    }

    @Override
    public List<CouponTemplateDTO> selectValidListBySysCodeAndReceiveType(Long sysCode, Integer couponReceiveType) {
        return HutoolBeanUtils.toBean(couponTemplateService.selectValidListBySysCodeAndReceiveType(sysCode, couponReceiveType), CouponTemplateDTO.class);
    }

    @Override
    public CouponReceiveResultDTO saveNormalCouponReceiveSingle(NormalCouponReceiveSingleAsyncReqVo reqVo) {
        CouponReceiveResultDTO couponReceiveResultDTO = couponService.saveNormalCouponReceiveSingle(reqVo.getBranchId(), reqVo.getMemberId(), reqVo.getCouponTemplateId(), reqVo.getCheckStock(), null);
        if (couponReceiveResultDTO.getCode() != 0) {
            throw new ServiceException(couponReceiveResultDTO.getMsg());
        }
        return couponReceiveResultDTO;
    }

    @Override
    public void saveNormalCouponReceiveSingleAsync(NormalCouponReceiveSingleAsyncReqVo reqVo) {
        CouponReceiveDTO couponReceiveDTO = new CouponReceiveDTO();
        couponReceiveDTO.setCouponBatchId(reqVo.getCouponBatchId());
        couponReceiveDTO.setCouponTemplateIds(Arrays.asList(reqVo.getCouponTemplateId()));
        couponReceiveDTO.setCouponStatusMap(reqVo.getCouponStatusMap());
        couponReceiveDTO.setBranchId(reqVo.getBranchId());
        couponReceiveDTO.setMemberId(reqVo.getMemberId());
        couponService.saveNormalCouponReceive(couponReceiveDTO);
    }

    @Override
    public CommonResult<ColonelQuotaDTO> getColonelQuota(Long colonelId) {
        String currentMonthId = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        int monthId = Integer.parseInt(currentMonthId);
        return CommonResult.success(prmCouponColonelQuotaMapper.getColonelQuota(colonelId, monthId));
    }

    @Override
    public CommonResult<PageResult<ColonelAppPrmCouponTemplateRespVO>> getColonelAppPrmCouponTemplatePage(ColonelAppPrmCouponTemplatePageReqVO pageReqVO) {
        PrmCouponTemplatePageReqVO prmCouponTemplatePageReqVO = new PrmCouponTemplatePageReqVO();
        BeanUtils.copyProperties(pageReqVO, prmCouponTemplatePageReqVO);  // 或使用构造函数进行转换

        PageResult<PrmCouponTemplateRespVO> prmCouponTemplatePage = prmCouponTemplateService.getPrmCouponTemplatePage(prmCouponTemplatePageReqVO);

        List<ColonelAppPrmCouponTemplateRespVO> colonelAppPrmCouponTemplateRespVOList =
                prmCouponTemplatePage.getList().stream()
                        .map(prmCouponTemplateRespVO -> {
                            ColonelAppPrmCouponTemplateRespVO colonelAppRespVO = new ColonelAppPrmCouponTemplateRespVO();
                            BeanUtils.copyProperties(prmCouponTemplateRespVO, colonelAppRespVO);  // 或者进行字段映射
                            return colonelAppRespVO;
                        })
                        .collect(Collectors.toList());

        PageResult<ColonelAppPrmCouponTemplateRespVO> result = new PageResult<>();
        result.setList(colonelAppPrmCouponTemplateRespVOList);
        result.setTotal(prmCouponTemplatePage.getTotal());

        return CommonResult.success(result);
    }

    @Override
    public CommonResult<List<Long>> getCouponReceivedBranchs(Long couponTemplateId) {
        return CommonResult.success(couponService.getCouponReceivedBranchs(couponTemplateId));
    }

    @Override
    public Long updateColonelQuota(Long colonelId, BigDecimal discountAmt) {
        String currentMonthId = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        int monthId = Integer.parseInt(currentMonthId);
        ColonelQuotaDTO colonelQuota = prmCouponColonelQuotaMapper.getColonelQuota(colonelId, monthId);
        BigDecimal newFinishQuota = colonelQuota.getFinishQuota().add(discountAmt);
        return prmCouponColonelQuotaMapper.updateInstanceQuota(String.valueOf(colonelId), monthId, null,newFinishQuota);
    }

    @Override
    public Integer getBranchReceiveQty(Long couponTemplateId, Long branchId) {
        return prmCouponMapper.getBranchReceiveQty(couponTemplateId, branchId);
    }

    @Override
    public CommonResult<List<CouponDTO>> getCouponListByBranchIdAndCouponBatchId(Long branchId, Long couponBatchId) {
        List<CouponDTO> prmCoupons = couponService.getCouponListByBranchIdAndCouponBatchId(branchId, couponBatchId);
        if (ToolUtil.isEmpty(prmCoupons)) return CommonResult.success(new ArrayList<>());
        return CommonResult.success(HutoolBeanUtils.toBean(prmCoupons, CouponDTO.class));
    }

    public CommonResult<CouponDTO> getPrmCoupon(Long couponId){
        return CommonResult.success(couponService.getPrmCouponDto(couponId));
    }

    @Override
    public CommonResult<List<CouponExportRespDTO>> getPrmCouponPage(CouponExportVO pageReqVO) {
        List<PrmCouponRespVO> list = couponService.getPrmCouponPage(HutoolBeanUtils.toBean(pageReqVO, PrmCouponPageReqVO.class)).getList();
        if (ToolUtil.isEmpty(list)) return  CommonResult.success(new ArrayList<>());
        return CommonResult.success(HutoolBeanUtils.toBean(list, CouponExportRespDTO.class));
    }

    @Override
    public CommonResult<CouponDTO> getCouponByOrderNoAndCouponTemplateId(String relateOrderNo, Long couponTemplateId) {
        PrmCoupon prmCoupon = prmCouponMapper.getCouponByOrderNoAndCouponTemplateId(relateOrderNo, couponTemplateId);
        if (ToolUtil.isEmpty(prmCoupon)){
            CommonResult.success(null);
        }
        return CommonResult.success(HutoolBeanUtils.toBean(prmCoupon, CouponDTO.class));
    }
}
