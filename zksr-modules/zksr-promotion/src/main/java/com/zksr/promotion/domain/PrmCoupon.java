package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠券记录对象 prm_coupon
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@TableName(value = "prm_coupon")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PrmCoupon extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 优惠券记录id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 优惠券模板id */
    @Excel(name = "优惠券模板id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    /** 门店id */
    @Excel(name = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 入驻商ID */
    @Excel(name = "入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 领取的用户id */
    @Excel(name = "领取的用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long receiveMemberId;

    /** 状态（数据字典）;1-正常 0-作废 2-已使用 */
    @Excel(name = "状态", readConverterExp = "数=据字典")
    private Integer state;

    /** 有效期开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "有效期开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expirationDateStart;

    /** 有效期结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "有效期结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expirationDateEnd;

    /** 使用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "使用时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date useTime;

    /** 对应订单号 */
    @Excel(name = "对应订单号")
    private String relateOrderNo;

    /** 商品适用范围(数据字典);0-所有商品可用（全场券） 1-指定入驻商可用（入驻商券），2-指定品类可用（品类券），3-指定品牌可用（品牌券），4-指定商品可用（商品券） */
    @Excel(name = "商品适用范围(数据字典);0-所有商品可用", readConverterExp = "全=场券")
    private Integer spuScope;

    /** 商品适用范围之适用ids;spu_scope=入驻商券时设定，入驻商id逗号分隔;spu_scope=品类券时设定，管理分类id逗号分隔;spu_scope=品牌券时设定，品牌id逗号分隔;spu_scope=商品券券时设定，SPU id逗号分隔 */
    @Excel(name = "商品适用范围之适用ids;spu_scope=入驻商券时设定，入驻商id逗号分隔;spu_scope=品类券时设定，管理分类id逗号分隔;spu_scope=品牌券时设定，品牌id逗号分隔;spu_scope=商品券券时设定，SPU id逗号分隔")
    private String spuScopeApplysIds;

    /** 优惠类型(数据字典);0-满减券  1-折扣券 */
    @Excel(name = "优惠类型(数据字典);0-满减券  1-折扣券")
    private Integer discountType;

    /** 优惠券启用金额(使用门槛);满多少元(商品适用范围内商品订单金额)可以使用 */
    @Excel(name = "优惠券启用金额(使用门槛);满多少元(商品适用范围内商品订单金额)可以使用")
    private BigDecimal triggerAmt;

    /** 优惠券面额;满多少元可以减多少元(满减券设置) */
    @Excel(name = "优惠券面额;满多少元可以减多少元(满减券设置)")
    private BigDecimal discountAmt;

    /** 优惠折扣;折，如99折记9.9折(折扣券设置) */
    @Excel(name = "优惠折扣;折，如99折记9.9折(折扣券设置)")
    private BigDecimal discountPercent;

    /** 最多优惠;折扣券设置，例如，折扣上限为 20 元，当使用 8 折优惠券，订单金额为 1000 元时，最高只可折扣 20 元，而非 80  元。 */
    @Excel(name = "最多优惠;折扣券设置，例如，折扣上限为 20 元，当使用 8 折优惠券，订单金额为 1000 元时，最高只可折扣 20 元，而非 80  元。")
    private BigDecimal discountLimitAmt;

    /** 领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券（需要讨论返券规则，返券规则用扩展表存 如购买指定商品返券，支付满多少返券等等） */
    @Excel(name = "领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券", readConverterExp = "需=要讨论返券规则，返券规则用扩展表存,如=购买指定商品返券，支付满多少返券等等")
    private Integer receiveType;

    /** 是否分摊;用来计算订单表的discount_amt1,discount_amt2, 需要传输给ERP */
    @Excel(name = "是否分摊;用来计算订单表的discount_amt1,discount_amt2, 需要传输给ERP")
    private Integer costFlag;

    /** 是否异种排他 */
    @Excel(name = "是否异种排他")
    private Integer excludable;

     /** 批次发券ID */
     @Excel(name = "批次发券ID")
     @JsonSerialize(using = CustomLongSerialize.class)
     private Long couponBatchId;

    @Excel(name = "同类型优惠券是否排它", readConverterExp = "0=否,1=是")
    @ApiModelProperty(value = "同类型优惠券是否排它 0-否,1-是", required = true, example = "1")
    private Integer sameTypeExcludable;

}
