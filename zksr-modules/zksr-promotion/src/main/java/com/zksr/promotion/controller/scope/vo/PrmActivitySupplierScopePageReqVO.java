package com.zksr.promotion.controller.scope.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 促销活动入驻商适用范围对象 prm_activity_supplier_scope
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@ApiModel("促销活动入驻商适用范围 - prm_activity_supplier_scope分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmActivitySupplierScopePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @ApiModelProperty(value = "活动id", required = true)
    private Long activityId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 1-白名单 0-黑名单 */
    @Excel(name = "1-白名单 0-黑名单")
    @ApiModelProperty(value = "1-白名单 0-黑名单")
    private Integer whiteOrBlack;


}
