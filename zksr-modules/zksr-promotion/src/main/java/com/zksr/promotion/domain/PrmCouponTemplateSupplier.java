package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;

import java.io.Serializable;

/**
 * 优惠券模板入驻商
 */
@TableName(value = "prm_coupon_template_supplier")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmCouponTemplateSupplier implements Serializable {

    private static final long serialVersionUID = -585744034620000711L;

    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /**
     * 优惠券模板id
     */
    @Excel(name = "优惠券模板id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    /**
     * 入驻商id
     */
    @Excel(name = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

}
