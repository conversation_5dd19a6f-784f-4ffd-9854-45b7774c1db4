package com.zksr.promotion.controller.scope.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 促销活动渠道适用范围对象 prm_activity_channel_scope
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ApiModel("促销活动渠道适用范围 - prm_activity_channel_scope分页 Request VO")
public class PrmActivityChannelScopeSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @ApiModelProperty(value = "活动id", required = true)
    private Long activityId;

    /** 渠道id */
    @Excel(name = "渠道id")
    @ApiModelProperty(value = "渠道id")
    private Long channelId;

}
