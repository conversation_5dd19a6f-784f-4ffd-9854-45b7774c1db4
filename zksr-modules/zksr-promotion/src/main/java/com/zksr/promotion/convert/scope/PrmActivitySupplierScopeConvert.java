package com.zksr.promotion.convert.scope;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.ActivityChannelScopeDTO;
import com.zksr.promotion.api.activity.dto.ActivitySupplierScopeDTO;
import com.zksr.promotion.domain.PrmActivityChannelScope;
import com.zksr.promotion.domain.PrmActivitySupplierScope;
import com.zksr.promotion.controller.scope.vo.PrmActivitySupplierScopeRespVO;
import com.zksr.promotion.controller.scope.vo.PrmActivitySupplierScopeSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 促销活动入驻商适用范围 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-06-16
*/
@Mapper
public interface PrmActivitySupplierScopeConvert {

    PrmActivitySupplierScopeConvert INSTANCE = Mappers.getMapper(PrmActivitySupplierScopeConvert.class);

    PrmActivitySupplierScopeRespVO convert(PrmActivitySupplierScope prmActivitySupplierScope);

    PrmActivitySupplierScope convert(PrmActivitySupplierScopeSaveReqVO prmActivitySupplierScopeSaveReq);

    PageResult<PrmActivitySupplierScopeRespVO> convertPage(PageResult<PrmActivitySupplierScope> prmActivitySupplierScopePage);

    List<ActivitySupplierScopeDTO> convert(List<PrmActivitySupplierScope> activitySupplierScopes);
}