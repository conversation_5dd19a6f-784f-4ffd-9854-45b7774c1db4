package com.zksr.promotion.controller.rule;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.product.api.content.ProductContentApi;
import com.zksr.promotion.controller.rule.vo.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.domain.PrmCbRule;
import com.zksr.promotion.service.IPrmCbRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.promotion.convert.rule.PrmCbRuleConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.Arrays;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 组合商品规则Controller
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Api(tags = "管理后台 - 组合商品规则接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/rule")
public class PrmCbRuleController {
    @Autowired
    private IPrmCbRuleService prmCbRuleService;

    @Resource
    private ProductContentApi productContentApi;

    /**
     * 新增组合商品规则
     */
    @ApiOperation(value = "新增组合商品规则", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "组合商品规则", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmCbActivitySaveReqVO createReqVO) {
        return success(prmCbRuleService.insertPrmCbRule(createReqVO));
    }

    /**
     * 修改组合商品规则
     */
    @ApiOperation(value = "修改组合商品规则", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "组合商品规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmCbActivitySaveReqVO updateReqVO) {
        prmCbRuleService.updatePrmCbRule(updateReqVO);
        return success(true);
    }

    /**
     * 停用组合商品规则
     */
    @ApiOperation(value = "停用组合商品规则", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "停用组合商品规则", businessType = BusinessType.UPDATE)
    @PutMapping("/disable")
    public CommonResult<Boolean> disable(@Valid @RequestBody PrmCbActivitySaveReqVO updateReqVO) {
        Long spuCombineId = prmCbRuleService.changePrmCbRuleStatus(updateReqVO, 2);
        productContentApi.sendSpuCombineEvent(Arrays.asList(spuCombineId));
        return success(true);
    }

    /**
     * 启用组合商品规则
     */
    @ApiOperation(value = "启用组合商品规则", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "启用组合商品规则", businessType = BusinessType.UPDATE)
    @PutMapping("/enable")
    public CommonResult<Boolean> enable(@Valid @RequestBody PrmCbActivitySaveReqVO updateReqVO) {
        Long spuCombineId = prmCbRuleService.changePrmCbRuleStatus(updateReqVO, 1);
        productContentApi.sendSpuCombineEvent(Arrays.asList(spuCombineId));
        return success(true);
    }

    /**
     * 删除组合商品规则
     */
    @ApiOperation(value = "删除组合商品规则", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "组合商品规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{cbRuleIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] cbRuleIds) {
        prmCbRuleService.deletePrmCbRuleByCbRuleIds(cbRuleIds);
        return success(true);
    }

    /**
     * 获取组合商品规则详细信息
     */
    @ApiOperation(value = "获得组合商品规则详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/getPrmCbInfo/{activityId}")
    public CommonResult<PrmCbActivityRespVo> getPrmCbInfo(@PathVariable("activityId") Long activityId) {
        PrmCbActivityRespVo prmCbRule = prmCbRuleService.getPrmCbRule(activityId);
        return success(prmCbRule);
    }

    /**
     * 分页查询组合商品规则
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得组合商品规则分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmCbRuleRespVO>> getPage(@Valid PrmCbRulePageReqVO pageReqVO) {
        PageResult<PrmCbRule> pageResult = prmCbRuleService.getPrmCbRulePage(pageReqVO);
        return success(PrmCbRuleConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "promotion:cb-rule:add";
        /**
         * 编辑
         */
        public static final String EDIT = "promotion:cb-rule:edit";
        /**
         * 删除
         */
        public static final String DELETE = "promotion:cb-rule:remove";
        /**
         * 列表
         */
        public static final String LIST = "promotion:cb-rule:list";
        /**
         * 查询
         */
        public static final String GET = "promotion:cb-rule:query";
        /**
         * 停用
         */
        public static final String DISABLE = "promotion:cb-rule:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "promotion:cb-rule:enable";

    }
}
