package com.zksr.promotion.controller.rule.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 秒杀商品信息返回给前端
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Data
@ApiModel("秒杀商品信息返回给前端使用 -- PrmSkRuleItemExcelRespVO")
public class PrmSkRuleItemExcelRespVO {
    private static final long serialVersionUID = 1L;

    /** 上架商品id */
    @ApiModelProperty(value = "上架商品id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long itemId;

    /** 展示分类id */
    @Excel(name = "展示分类id")
    @ApiModelProperty(value = "展示分类id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long classId;

    /** 展示分类名称 */
    @Excel(name = "展示分类名称")
    @ApiModelProperty(value = "展示分类名称")
    private String className;

    /** 城市上架商品id */
    @ApiModelProperty(value = "城市上架商品id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaItemId;

    /** 入驻商上架商品id */
    @ApiModelProperty(value = "入驻商上架商品id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierItemId;

    /** 城市展示分类id */
    @Excel(name = "城市展示分类id")
    @ApiModelProperty(value = "城市展示分类id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaClassId;

    /** 城市展示分类名称 */
    @Excel(name = "城市展示分类名称")
    @ApiModelProperty(value = "城市展示分类名称")
    private String areaClassName;

    /** 平台商展示分类id */
    @Excel(name = "平台商展示分类id")
    @ApiModelProperty(value = "平台商展示分类id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long saleClassId;

    /** 平台商展示分类名称 */
    @Excel(name = "平台商展示分类名称")
    @ApiModelProperty(value = "平台商展示分类名称")
    private String saleClassName;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 上下架状态 */
    @Excel(name = "上下架状态")
    @ApiModelProperty(value = "上下架状态")
    private Integer shelfStatus;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    @ApiModelProperty(value = "商品SPU id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    @ApiModelProperty(value = "商品sku id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 平台商管理分类id */
    @Excel(name = "平台商管理分类id")
    @ApiModelProperty(value = "平台商管理分类id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long catgoryId;

    /** 平台商品牌id */
    @Excel(name = "平台商品牌id")
    @ApiModelProperty(value = "平台商品牌id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long brandId;

    /** 成本价 */
    @Excel(name = "成本价")
    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    @ApiModelProperty(value = "商品SPU编号")
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    /** 库存数量 */
    @Excel(name = "库存数量")
    @ApiModelProperty(value = "库存数量")
    private Long stock;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否")
    private Long isDelete;

    /** keyword */
    @Excel(name = "搜索关键字")
    @ApiModelProperty(value = "搜索关键字", example = "示例值")
    private String keyword;

    /** 国际条码 */
    @Excel(name = "国际条码")
    @ApiModelProperty(value = "国际条码", example = "示例值")
    private String barcode;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "单位-数据字典", example = "示例值")
    private Long unit;

    /** 属性数组，JSON 格式 */
    @Excel(name = "属性数组，JSON 格式")
    @ApiModelProperty(value = "属性数组，JSON 格式", example = "示例值")
    private String properties;

    /** 标准价 */
    @Excel(name = "标准价")
    @ApiModelProperty(value = "标准价", example = "示例值")
    private BigDecimal markPrice;

    /** 建议零售价 */
    @Excel(name = "建议零售价")
    @ApiModelProperty(value = "建议零售价", example = "示例值")
    private BigDecimal suggestPrice;

    /** 保质期 */
    @Excel(name = "保质期")
    @ApiModelProperty(value = "保质期")
    private Integer expirationDate;		 // 保质期

    /** 参考进价 */
    @Excel(name = "参考进价")
    @ApiModelProperty(value = "参考进价")
    private BigDecimal referencePrice;

    /** 参考售价 */
    @Excel(name = "参考售价")
    @ApiModelProperty(value = "参考售价")
    private BigDecimal referenceSalePrice;

    /** 平台商管理分类名称 */
    @Excel(name = "平台商管理分类名称")
    @ApiModelProperty(value = "平台商管理分类名称")
    private String catgoryName;

    /** 平台商品牌名称 */
    @Excel(name = "平台商品牌名称")
    @ApiModelProperty(value = "平台商品牌名称")
    private String brandName;

    /** 排序序号 */
    @Excel(name = "排序序号")
    @ApiModelProperty(value = "排序序号", example = "示例值")
    private Integer sortNum;

    /** 上架时间 */
    @Excel(name = "上架时间")
    @ApiModelProperty(value = "上架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelfDate; 		 // 上架时间

    /** 列表排序方式 */
    @Excel(name = "列表排序方式")
    @ApiModelProperty(value = "列表排序方式", example = "示例值")
    private String sortord;

    /** 列表排序顺序 */
    @Excel(name = "列表排序顺序")
    @ApiModelProperty(value = "列表排序顺序 0 正序 1 倒叙", example = "示例值")
    private Long sortOrder;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)", example = "示例值")
    private Long status;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;


    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面图")
    private String thumb;

    /** 商品规格 */
    @Excel(name = "商品规格")
    @ApiModelProperty(value = "商品规格")
    private String specName;

    /**
     * 商品类型 0：全国商品 1：本地商品
     */
    @ApiModelProperty("商品类型 0：全国商品 1：本地商品")
    private Integer itemType;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @Excel(name = "最小单位-数据字典（sys_prdt_unit）", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "最小单位-数据字典（sys_prdt_unit）")
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    @Excel(name = "中单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "中单位-数据字典（sys_prdt_unit）")
    private Long midUnit;

    /** 大单位-数据字典（sys_prdt_unit） */
    @Excel(name = "大单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "大单位-数据字典（sys_prdt_unit）")
    private Long largeUnit;

    /** 大单位-标准价 */
    @Excel(name = "大单位-标准价")
    private BigDecimal largeMarkPrice;

    /** 大单位-成本价(供货价) */
    @Excel(name = "大单位-成本价(供货价)")
    private BigDecimal largeCostPrice;

    /** 大单位-建议零售价 */
    @Excel(name = "大单位-建议零售价")
    private BigDecimal largeSuggestPrice;

    /** 中单位-标准价 */
    @Excel(name = "中单位-标准价")
    private BigDecimal midMarkPrice;

    /** 中单位-成本价(供货价) */
    @Excel(name = "中单位-成本价(供货价)")
    private BigDecimal midCostPrice;

    /** 中单位-建议零售价 */
    @Excel(name = "中单位-建议零售价")
    private BigDecimal midSuggestPrice;

    /** 单次限量 */
    @Excel(name = "单次限量")
    @ApiModelProperty(value = "单次限量")
    private Integer onceLimit;

    /** 秒杀库存 */
    @Excel(name = "秒杀库存")
    @ApiModelProperty(value = "秒杀库存")
    private Integer seckillStock;

    /** 秒杀价 */
    @Excel(name = "小单位秒杀价")
    @ApiModelProperty(value = "小单位秒杀价")
    private BigDecimal seckillPrice;

    /** 秒杀价 */
    @Excel(name = "中单位秒杀价")
    @ApiModelProperty(value = "中单位秒杀价")
    private BigDecimal midSeckillPrice;

    /** 秒杀价 */
    @Excel(name = "大单位秒杀价")
    @ApiModelProperty(value = "大单位秒杀价")
    private BigDecimal largeSeckillPrice;
}
