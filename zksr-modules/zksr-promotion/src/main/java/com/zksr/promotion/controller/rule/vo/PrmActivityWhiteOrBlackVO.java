package com.zksr.promotion.controller.rule.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/12/9 16:30
 * @注释
 */
@Data
@ApiModel("买赠活动新增对象")
public class PrmActivityWhiteOrBlackVO {

    @ApiModelProperty("区域、门店、渠道、SPU")
    private String id;

    @ApiModelProperty("适用类型;2-品类，3-品牌，4-商品")
    private Integer applyType;

    @ApiModelProperty("1-白名单 0-黑名单")
    private Integer type;

}
