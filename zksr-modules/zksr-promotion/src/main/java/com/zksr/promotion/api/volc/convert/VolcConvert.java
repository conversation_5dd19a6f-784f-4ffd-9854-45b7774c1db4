package com.zksr.promotion.api.volc.convert;

import com.volcengine.model.livesaas.request.CommonRequest;
import com.volcengine.model.livesaas.request.CreateActivityAPIRequest;
import com.volcengine.model.livesaas.response.CommonResponse;
import com.volcengine.model.livesaas.response.GetActivityAPIResponse;
import com.volcengine.model.livesaas.response.GetDownloadLiveClientAPIResponse;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.volc.vo.*;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomRespVO;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomSaveReqVO;
import com.zksr.promotion.domain.PrmLiveRoom;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 直播间 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-03-25
*/
@Mapper
public interface VolcConvert {

    VolcConvert INSTANCE = Mappers.getMapper(VolcConvert.class);

    CreateActivityRespVO convert2CreateActivityRespVO(CommonResponse commonResponse);
    CreateActivityAPIRequest convert2CreateActivityAPIRequest(CreateActivityReqVO reqVO);
    GetActivityRespVO conver2GetActivityRespVO(GetActivityAPIResponse response);
    GetActivityReqVO convert2GetActivityReqVO(CommonRequest commonRequest);
    CommonRequest convert2CommonRequest(GetActivityReqVO req);

    GetDownloadLiveClientRespVO convert2GetDownloadLiveClientRespVO(GetDownloadLiveClientAPIResponse response);
}