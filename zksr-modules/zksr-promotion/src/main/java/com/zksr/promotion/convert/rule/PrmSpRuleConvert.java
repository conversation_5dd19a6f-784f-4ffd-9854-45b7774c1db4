package com.zksr.promotion.convert.rule;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.SpRuleDTO;
import com.zksr.promotion.controller.activity.vo.PrmActivityItemPageVo;
import com.zksr.promotion.controller.rule.vo.PrmSpRuleItemExcelRespVO;
import com.zksr.promotion.domain.PrmSpRule;
import com.zksr.promotion.controller.rule.vo.PrmSpRuleRespVO;
import com.zksr.promotion.controller.rule.vo.PrmSpRuleSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.promotion.domain.excel.PrmSpRuleImportExcel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 特价活动规则 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-13
*/
@Mapper
public interface PrmSpRuleConvert {

    PrmSpRuleConvert INSTANCE = Mappers.getMapper(PrmSpRuleConvert.class);

    PrmSpRuleRespVO convert(PrmSpRule prmSpRule);

    PrmSpRule convert(PrmSpRuleSaveReqVO prmSpRuleSaveReq);

    PageResult<PrmSpRuleRespVO> convertPage(PageResult<PrmSpRule> prmSpRulePage);

    List<SpRuleDTO> convertListDTO(List<PrmSpRule> prmSpRules);

    PrmSpRuleItemExcelRespVO convert(PrmActivityItemPageVo prmActivityItemPageVo);

    default PrmSpRuleItemExcelRespVO updateFromItemData(PrmSpRuleImportExcel itemData, PrmSpRuleItemExcelRespVO respVO){
        if (itemData == null || respVO == null) {
            return respVO;
        }
        // 覆盖或补充属性
        respVO.setSpPrice(itemData.getSpPrice());
        respVO.setMidSpPrice(itemData.getMidSpPrice());
        respVO.setLargeSpPrice(itemData.getLargeSpPrice());
        respVO.setOnceBuyLimit(itemData.getOnceBuyLimit());
        respVO.setTotalLimitQty(itemData.getTotalLimitQty());
        // 根据实际字段补充逻辑
        return respVO;
    }
}