package com.zksr.promotion.convert.scope;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.ActivityBranchScopeDTO;
import com.zksr.promotion.domain.PrmActivityBranchScope;
import com.zksr.promotion.controller.scope.vo.PrmActivityBranchScopeRespVO;
import com.zksr.promotion.controller.scope.vo.PrmActivityBranchScopeSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 促销活动门店适用范围 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-13
*/
@Mapper
public interface PrmActivityBranchScopeConvert {

    PrmActivityBranchScopeConvert INSTANCE = Mappers.getMapper(PrmActivityBranchScopeConvert.class);

    PrmActivityBranchScopeRespVO convert(PrmActivityBranchScope prmActivityBranchScope);

    PrmActivityBranchScope convert(PrmActivityBranchScopeSaveReqVO prmActivityBranchScopeSaveReq);

    PageResult<PrmActivityBranchScopeRespVO> convertPage(PageResult<PrmActivityBranchScope> prmActivityBranchScopePage);

    List<ActivityBranchScopeDTO> convert(List<PrmActivityBranchScope> activityBranchScopes);
}