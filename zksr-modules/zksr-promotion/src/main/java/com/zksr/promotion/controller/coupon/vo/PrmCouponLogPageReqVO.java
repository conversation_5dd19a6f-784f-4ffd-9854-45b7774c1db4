package com.zksr.promotion.controller.coupon.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 优惠券日志对象 prm_coupon_log
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
@ApiModel("优惠券日志 - prm_coupon_log分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmCouponLogPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 优惠券日志 */
    @ApiModelProperty(value = "日志信息")
    private Long couponLogId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 优惠券id */
    @Excel(name = "优惠券id")
    @ApiModelProperty(value = "优惠券id")
    private String couponId;

    /** 操作前状态 */
    @Excel(name = "操作前状态")
    @ApiModelProperty(value = "操作前状态")
    private Integer beforeState;

    /** 操作后状态 */
    @Excel(name = "操作后状态")
    @ApiModelProperty(value = "操作后状态")
    private Integer afterState;

    /** 操作类型(数据字典) */
    @Excel(name = "操作类型(数据字典)")
    @ApiModelProperty(value = "操作类型(数据字典)")
    private Integer operateType;

    /** 日志信息 */
    @Excel(name = "日志信息")
    @ApiModelProperty(value = "日志信息")
    private String content;


}
