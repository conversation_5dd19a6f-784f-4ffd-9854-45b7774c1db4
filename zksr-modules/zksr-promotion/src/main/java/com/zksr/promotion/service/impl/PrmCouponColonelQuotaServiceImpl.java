package com.zksr.promotion.service.impl;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.colonel.Colonel<PERSON>pi;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.promotion.api.coupon.dto.ColonelQuotaDTO;
import com.zksr.promotion.controller.quota.vo.*;
import com.zksr.promotion.service.IPromotionCacheService;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.model.LoginUser;
import com.zksr.trade.api.order.dto.TrdOrderRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmCouponColonelQuotaMapper;
import com.zksr.promotion.convert.quota.PrmCouponColonelQuotaConvert;
import com.zksr.promotion.domain.PrmCouponColonelQuota;
import com.zksr.promotion.service.IPrmCouponColonelQuotaService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.COUPON_CHANGE_STATE_ERR;
import static com.zksr.promotion.enums.ErrorCodeConstants.SALESPERSON_QUOTA_NOT_FOUND;

/**
 * 业务员发券额度Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Service
public class PrmCouponColonelQuotaServiceImpl implements IPrmCouponColonelQuotaService {
    @Autowired
    private PrmCouponColonelQuotaMapper prmCouponColonelQuotaMapper;
    @Resource
    private ColonelApi colonelApi;
    @Resource
    private BranchApi branchApi;
    @Autowired
    private IPromotionCacheService productCacheService;

    @Resource
    private DcApi dcApi;
    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrmCouponColonelQuotaRespVO> getPrmCouponColonelQuotaPage(PrmCouponColonelQuotaPageReqVO pageReqVO) {
        PageResult<PrmCouponColonelQuotaRespVO> result = new PageResult<>();
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        Long dcId =SecurityUtils.getLoginUser().getDcId();
        String colonelName = null;
        String colonelPhone = null;
        Integer status = null;
        List<Long> dcAreaList =null;
        if(ToolUtil.isNotEmpty(pageReqVO.getColonelName())){
            colonelName = pageReqVO.getColonelName();
        }
        if(ToolUtil.isNotEmpty(pageReqVO.getColonelPhone())){
            colonelPhone = pageReqVO.getColonelPhone();
        }
        if(ToolUtil.isEmpty(pageReqVO.getStatus())){
            status = NumberPool.INT_ONE;
        }else{
            status = Integer.valueOf(pageReqVO.getStatus());
        }

        if(ToolUtil.isNotEmpty(dcId)){
            if(ToolUtil.isNotEmpty(dcApi.getDcAreaList(dcId).getCheckedData())){
                dcAreaList = dcApi.getDcAreaList(dcId).getCheckedData();
            }
        }
        // 获取当前月份
        String currentMonthId = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        pageReqVO.setCurrentMonthId(Integer.valueOf(currentMonthId));
        // 计算分页偏移量
        int offset = (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize();
        pageReqVO.setPageNo(offset);


        List<Long> colonelIdList = colonelApi.getColonelIdList(sysCode,status ,colonelName,colonelPhone,dcAreaList).getCheckedData();
        // 没有业务员信息
        if (colonelIdList.isEmpty()) { // 没有查询到数据，直接返回
            result.setList(new ArrayList<>());
            result.setTotal(0L);
            return result;
        }

        List<PrmCouponColonelQuota> quotaPage = prmCouponColonelQuotaMapper.selectPage(pageReqVO);

        // 统计总数（如需要分页插件可省略）
        Long total = prmCouponColonelQuotaMapper.countPrmCouponColonelQuota(pageReqVO);

        // 创建一个集合来存储 quotaPage 中存在的 colonelId
        Set<Long> existingColonelIds = new HashSet<>();
        if (quotaPage != null) {
            existingColonelIds = quotaPage.stream()
                    .map(PrmCouponColonelQuota::getColonelId)
                    .collect(Collectors.toSet());
        }

        List<PrmCouponColonelQuotaRespVO> responseList = new ArrayList<>();

        for (Long colonelId : colonelIdList) {
            if (existingColonelIds.contains(colonelId)) {
                // 业务员在 quotaPage 中存在，按照现有逻辑处理
                quotaPage.stream()
                        .filter(quota -> quota.getColonelId().equals(colonelId))
                        .findFirst()
                        .ifPresent(quota -> {
                            PrmCouponColonelQuotaRespVO responseVO = PrmCouponColonelQuotaConvert.INSTANCE.convert(quota);
                            // 查询业务员信息
                            ColonelDTO colonel = productCacheService.getColonel(quota.getColonelId());
                            if(ToolUtil.isEmpty(colonel)){
                                colonel = colonelApi.getByColonelId(colonelId).getCheckedData();
                                if(ToolUtil.isEmpty(colonel)){
                                    return;
                                }
                            }
                            responseVO.setColonelName(colonel.getColonelName());
                            responseVO.setColonelPhone(colonel.getColonelPhone());
                            responseVO.setStatus(colonel.getStatus());
                            responseVO.setPosition(colonel.getColonelLevel());
                            Long branchCount = branchApi.selectBranchCountByColonel(colonel.getColonelId()).getCheckedData();
                            if (ToolUtil.isNotEmpty(branchCount)) {
                                responseVO.setBranchCount(branchCount); // 管理门店数
                            }
                            BigDecimal salesMoney = colonelApi.getColonelMonthlySalesTarget(colonel.getColonelId()).getCheckedData();
                            if (ToolUtil.isNotEmpty(salesMoney)) {
                                responseVO.setMonthlySalesTarget(salesMoney); // 当前月销售额目标
                            }
                            responseVO.setRemainingQuota(quota.getQuota().subtract(quota.getFinishQuota())); // 剩余额度
                            responseList.add(responseVO);
                        });
            } else {
                // 业务员不在 quotaPage 中，只展示编号和名称
                ColonelDTO colonel = productCacheService.getColonel(colonelId);
                if(ToolUtil.isEmpty(colonel)){
                    colonel = colonelApi.getByColonelId(colonelId).getCheckedData();
                    if(ToolUtil.isEmpty(colonel)){
                        continue;
                    }
                }
                PrmCouponColonelQuotaRespVO responseVO = new PrmCouponColonelQuotaRespVO();
                responseVO.setColonelId(colonelId);
                responseVO.setColonelName(colonel.getColonelName());
                responseVO.setColonelPhone(colonel.getColonelPhone());
                responseVO.setStatus(colonel.getStatus());
                responseVO.setPosition(colonel.getColonelLevel());
                Long branchCount = branchApi.selectBranchCountByColonel(colonel.getColonelId()).getCheckedData();
                if (ToolUtil.isNotEmpty(branchCount)) {
                    responseVO.setBranchCount(branchCount); // 管理门店数
                }
                BigDecimal salesMoney = colonelApi.getColonelMonthlySalesTarget(colonel.getColonelId()).getCheckedData();
                if (ToolUtil.isNotEmpty(salesMoney)) {
                    responseVO.setMonthlySalesTarget(salesMoney); // 当前月销售额目标
                }
                responseVO.setQuota(BigDecimal.ZERO);
                responseVO.setFinishQuota(BigDecimal.ZERO);
                responseVO.setRemainingQuota(BigDecimal.ZERO);
                responseList.add(responseVO);
            }
        }

        // 封装分页结果
        result.setList(responseList);
        result.setTotal((long)colonelIdList.size()); // 设置总记录数为业务员总数
        return result;
    }


    /**
     * 调整业务员额度
     * @param pageReqVO
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long adjustQuota(PrmCouponColonelQuotaAdjustReqVO pageReqVO) {
        if(ToolUtil.isEmpty(pageReqVO.getColonelId())){
            throw exception(SALESPERSON_QUOTA_NOT_FOUND);
        }
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        String colonelId = pageReqVO.getColonelId();
        BigDecimal newQuota = pageReqVO.getQuota();
        Long count = null;
        // 检查是否存在模板额度
        PrmCouponColonelQuota templateQuota = prmCouponColonelQuotaMapper.selectTemplateQuota(pageReqVO.getColonelId());

        if (ToolUtil.isEmpty(templateQuota)) {
            // 如果不存在模板额度，新增模板额度
            PrmCouponColonelQuotaPageReqVO templateQuotaReqVO = new PrmCouponColonelQuotaPageReqVO();
            templateQuotaReqVO.setSysCode(sysCode);
            templateQuotaReqVO.setColonelId(colonelId);
            templateQuotaReqVO.setQuotaType(NumberPool.INT_ZERO);
            templateQuotaReqVO.setQuota(newQuota);
            templateQuotaReqVO.setMonthId(null);
            count = prmCouponColonelQuotaMapper.insertQuota(templateQuotaReqVO); // 模板额度
        } else {
            // 如果模板额度存在，更新额度
            count = prmCouponColonelQuotaMapper.updateTemplateQuota(colonelId, newQuota);
        }

        // 检查当前月份的实例额度是否存在
        String currentMonthId = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        int monthId = Integer.parseInt(currentMonthId);
        PrmCouponColonelQuota instanceQuota = prmCouponColonelQuotaMapper.selectInstanceQuota(pageReqVO.getColonelId(), monthId);

        if (ToolUtil.isEmpty(instanceQuota)) {
            // 如果实例额度不存在，新增实例额度
            PrmCouponColonelQuotaPageReqVO templateQuotaReqVO = new PrmCouponColonelQuotaPageReqVO();
            templateQuotaReqVO.setSysCode(sysCode);
            templateQuotaReqVO.setColonelId(colonelId);
            templateQuotaReqVO.setQuotaType(NumberPool.INT_ONE);
            templateQuotaReqVO.setQuota(newQuota);
            templateQuotaReqVO.setMonthId(monthId);
            count = prmCouponColonelQuotaMapper.insertQuota(templateQuotaReqVO); // 实例额度
        } else {
            // 如果实例额度存在，更新额度
            count = prmCouponColonelQuotaMapper.updateInstanceQuota(colonelId, monthId, newQuota,null);
        }
        return count;
    }

    @Override
    public QuotaDetailResponse selectQuotaDetails(String colonelId) {
        String currentMonthId = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        int monthId = Integer.parseInt(currentMonthId);
        ColonelQuotaDTO quota = prmCouponColonelQuotaMapper.getColonelQuota(Long.valueOf(colonelId),monthId);
        QuotaDetailResponse quotaDetails = new QuotaDetailResponse();
        if(ToolUtil.isNotEmpty(quota)){
            quotaDetails = PrmCouponColonelQuotaConvert.INSTANCE.convertDetail(quota);
            quotaDetails.setRemainingQuota(quota.getQuota().subtract(quota.getFinishQuota()));
        }else{
            quotaDetails.setColonelId(Long.valueOf(colonelId));
            quotaDetails.setQuota(BigDecimal.ZERO);
            quotaDetails.setRemainingQuota(BigDecimal.ZERO);
            quotaDetails.setFinishQuota(BigDecimal.ZERO);
        }
        return quotaDetails;
    }

    @Override
    public List<QuotaHistoryResponse> selectQuotaHistory(String colonelId) {
        return  prmCouponColonelQuotaMapper.selectQuotaHistory(colonelId);
    }


    // TODO 待办：请将下面的错误码复制到 com.zksr.promotion.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 业务员发券额度 TODO 补充编号 ==========
    // ErrorCode PRM_COUPON_COLONEL_QUOTA_NOT_EXISTS = new ErrorCode(TODO 补充编号, "业务员发券额度不存在");


}
