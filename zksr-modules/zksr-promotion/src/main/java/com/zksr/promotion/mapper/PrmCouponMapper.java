package com.zksr.promotion.mapper;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.enums.CouponStateEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.api.coupon.dto.CouponApplyDTO;
import com.zksr.promotion.api.coupon.vo.CouponPageReqVO;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.controller.coupon.vo.PrmCouponRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCoupon;
import com.zksr.promotion.controller.coupon.vo.PrmCouponPageReqVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 优惠券记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Mapper
public interface PrmCouponMapper extends BaseMapperX<PrmCoupon> {
    List<PrmCouponRespVO> selectList(PrmCouponPageReqVO reqVO);

    List<CouponDTO> selectBranchCouponListPage(CouponPageReqVO receiveReq);

    List<CouponDTO> selectCouponBatchIds(@Param("branchId") Long branchId, @Param("couponIdList") List<Long> couponIdList);

    default List<PrmCoupon> getCouponListByBranchIdAndCouponBatchId(Long branchId, Long couponBatchId) {
        LambdaQueryWrapper<PrmCoupon> queryWrapper = Wrappers.lambdaQuery(PrmCoupon.class)
                // 状态为1-正常和2-已使用
                .in(PrmCoupon::getState, CouponStateEnum.NORMAL.getState(), CouponStateEnum.USED.getState())
                // 优惠券批次ID
                .eq(PrmCoupon::getCouponBatchId, couponBatchId);

        // 如果门店ID不为null，则添加筛选条件
        if (branchId != null) {
            queryWrapper.eq(PrmCoupon::getBranchId, branchId);
        }

        return selectList(queryWrapper);
    }
    /**
     * 核销使用优惠券
     * @param couponApply
     * @return
     */
    default int updateApply(CouponApplyDTO couponApply) {
        PrmCoupon coupon = new PrmCoupon();
        // 优惠券ID
        coupon.setCouponId(couponApply.getCouponId());
        // 使用状态
        coupon.setState(CouponStateEnum.USED.getState());
        // 使用时间
        coupon.setUseTime(DateUtil.date());
        // 使用订单
        coupon.setRelateOrderNo(couponApply.getRelateOrderNo());
        return update(
                coupon,
                Wrappers.lambdaUpdate(PrmCoupon.class)
                        // 未使用的
                        .eq(PrmCoupon::getState, CouponStateEnum.NORMAL.getState())
                        // ID
                        .eq(PrmCoupon::getCouponId, coupon.getCouponId())
        );
    }

    default int updateReturn(CouponApplyDTO couponApply) {
        PrmCoupon coupon = new PrmCoupon();
        // 优惠券ID
        coupon.setCouponId(couponApply.getCouponId());
        // 使用状态
        coupon.setState(CouponStateEnum.NORMAL.getState());
        // 使用订单
        coupon.setRelateOrderNo(StringPool.EMPTY);
        return update(
                coupon,
                Wrappers.lambdaUpdate(PrmCoupon.class)
                        // 未使用的
                        .eq(PrmCoupon::getState, CouponStateEnum.USED.getState())
                        // 必须是使用优惠券的订单才可以返还
                        .eq(PrmCoupon::getRelateOrderNo, couponApply.getRelateOrderNo())
                        // ID
                        .eq(PrmCoupon::getCouponId, coupon.getCouponId())
                        // 重置使用时间
                        .set(PrmCoupon::getUseTime, null)
        );
    }

    List<Long> getCouponReceivedBranchs(@Param("couponTemplateId")Long couponTemplateId);

    Integer getBranchReceiveQty(@Param("couponTemplateId")Long couponTemplateId,@Param("branchId") Long branchId);

    PrmCoupon getCouponByOrderNoAndCouponTemplateId(@Param("relateOrderNo")String relateOrderNo, @Param("couponTemplateId")Long couponTemplateId);
}
