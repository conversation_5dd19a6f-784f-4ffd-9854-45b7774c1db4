package com.zksr.promotion.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmActivityCityScopeMapper;
import com.zksr.promotion.convert.scope.PrmActivityCityScopeConvert;
import com.zksr.promotion.domain.PrmActivityCityScope;
import com.zksr.promotion.controller.scope.vo.PrmActivityCityScopePageReqVO;
import com.zksr.promotion.controller.scope.vo.PrmActivityCityScopeSaveReqVO;
import com.zksr.promotion.service.IPrmActivityCityScopeService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 促销活动城市适用范围Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Service
public class PrmActivityCityScopeServiceImpl implements IPrmActivityCityScopeService {
    @Autowired
    private PrmActivityCityScopeMapper prmActivityCityScopeMapper;

    /**
     * 新增促销活动城市适用范围
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrmActivityCityScope(PrmActivityCityScopeSaveReqVO createReqVO) {
        // 插入
        PrmActivityCityScope prmActivityCityScope = PrmActivityCityScopeConvert.INSTANCE.convert(createReqVO);
        prmActivityCityScopeMapper.insert(prmActivityCityScope);
        // 返回
        return prmActivityCityScope.getSysCode();
    }

    /**
     * 修改促销活动城市适用范围
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmActivityCityScope(PrmActivityCityScopeSaveReqVO updateReqVO) {
        prmActivityCityScopeMapper.updateById(PrmActivityCityScopeConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除促销活动城市适用范围
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deletePrmActivityCityScope(Long sysCode) {
        // 删除
        prmActivityCityScopeMapper.deleteById(sysCode);
    }

    /**
     * 批量删除促销活动城市适用范围
     *
     * @param sysCodes 需要删除的促销活动城市适用范围主键
     * @return 结果
     */
    @Override
    public void deletePrmActivityCityScopeBySysCodes(Long[] sysCodes) {
        for(Long sysCode : sysCodes){
            this.deletePrmActivityCityScope(sysCode);
        }
    }

    /**
     * 获得促销活动城市适用范围
     *
     * @param sysCode 平台商id
     * @return 促销活动城市适用范围
     */
    @Override
    public PrmActivityCityScope getPrmActivityCityScope(Long sysCode) {
        return prmActivityCityScopeMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmActivityCityScope> getPrmActivityCityScopePage(PrmActivityCityScopePageReqVO pageReqVO) {
        return prmActivityCityScopeMapper.selectPage(pageReqVO);
    }

}
