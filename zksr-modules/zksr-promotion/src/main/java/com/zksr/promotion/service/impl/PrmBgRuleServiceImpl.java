package com.zksr.promotion.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.config.CustomIdGenerator;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.materialApply.dto.MaterialApplyDTO;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.activity.dto.ActivityBranchScopeDTO;
import com.zksr.promotion.api.activity.dto.ActivityChannelScopeDTO;
import com.zksr.promotion.api.activity.dto.ActivityCityScopeDTO;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.controller.activity.vo.PrmActivityCheckScopeVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.rule.vo.*;
import com.zksr.promotion.domain.*;
import com.zksr.promotion.mapper.*;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPrmBgRuleService;
import com.zksr.promotion.service.IPromotionCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.PrmConstants.*;
import static com.zksr.common.core.constant.StatusConstants.STATE_ENABLE;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.PRDT_MATERIAL_APPLY_TYPE_1;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 买赠条件规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Service
public class PrmBgRuleServiceImpl implements IPrmBgRuleService {
    @Autowired
    private PrmBgRuleMapper prmBgRuleMapper;

    @Autowired
    private PrmActivityMapper prmActivityMapper;

    @Autowired
    private PrmActivitySpuScopeMapper spuScopeMapper;

    @Autowired
    private PrmActivityBranchScopeMapper branchScopeMapper;

    @Autowired
    private PrmActivityCityScopeMapper cityScopeMapper;

    @Autowired
    private PrmActivityChannelScopeMapper channelScopeMapper;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Autowired
    private CustomIdGenerator customIdGenerator;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private IPrmActivityCommonService prmActivityCommonService;

    @Autowired
    private IPrmActivityService prmActivityService;

    /**
     * 新增买赠条件规则
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertPrmBgRule(@Valid PrmBgActivitySaveReqVO createReqVO) {
        createReqVO.setActivityId(null);
        //校验是否阶梯
        if (createReqVO.getLadderFlag() == PRM_LADDER_FLAG_0) {
            Set<String> checkLadder = createReqVO.getBgRuleSaveReqVOList().stream().map(t -> StringUtils.format("{}_{}", t.getRuleUnitType(), t.getRuleQty())).collect(Collectors.toSet());
            if (checkLadder.size() > NumberPool.INT_ONE) {
                throw exception(PRO_CHECK_LADDER_FLAG);
            }
        }
        PrmActivity prmActivity = prmActivityCommonService.activityChangeCheck(HutoolBeanUtils.toBean(createReqVO, PrmActivityCheckScopeVO.class));
        createReqVO.setEffectTime(prmActivity.getEffectTime());
        createReqVO.setEffectMan(prmActivity.getEffectMan());
        //校验买赠规则
        checkFgRule(createReqVO.getBgRuleSaveReqVOList());
        // 插入促销主表
        PrmActivity activity = HutoolBeanUtils.toBean(createReqVO, PrmActivity.class);
        //校验商品类型(平台商：1全国  运营商：2本地)
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        activity.setFuncScope(ObjectUtil.isNull(dcId) ? PRM_FUNC_SCOPE_1 : PRM_FUNC_SCOPE_2);
        //生成促销单号
        activity.setPrmSheetNo(PrmNoEnum.BG.getType() + customIdGenerator.nextId());
        prmActivityMapper.insert(activity);
        createReqVO.setActivityId(activity.getActivityId());

        //插入详情数据
        insertDetail(createReqVO, NumberPool.INT_ONE,true);

        //新增素材信息
        if(ToolUtil.isNotEmpty(createReqVO.getMaterialId())){
            prmActivityCommonService.addMaterialApply(MaterialApplyDTO.builder()
                    .materialId(createReqVO.getMaterialId())
                    .applyId(createReqVO.getActivityId())
                    .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                    .startTime(createReqVO.getStartTime())
                    .endTime(createReqVO.getEndTime()).build());

        }

        // 刷新缓存
        prmActivityService.reloadSupplierActivity(activity.getActivityId());
        // 返回
        return activity.getActivityId();
    }

    /**
     * 修改买赠条件规则
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrmBgRule(@Valid PrmBgActivitySaveReqVO updateReqVO) {
        PrmActivity checkPrmActivity = prmActivityCommonService.checkUpdate(updateReqVO.getActivityId());
        PrmActivity prmActivity = prmActivityCommonService.activityChangeCheck(HutoolBeanUtils.toBean(updateReqVO, PrmActivityCheckScopeVO.class));
        updateReqVO.setEffectTime(prmActivity.getEffectTime());
        updateReqVO.setEffectMan(prmActivity.getEffectMan());
        //校验买赠规则
        checkFgRule(updateReqVO.getBgRuleSaveReqVOList());
        // 修改促销主表
        prmActivityMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrmActivity.class));

        //修改详情数据
        insertDetail(updateReqVO, NumberPool.INT_TWO,false);

        //修改素材信息
        if(ToolUtil.isNotEmpty(updateReqVO.getMaterialId())){
            prmActivityCommonService.editMaterialApply(MaterialApplyDTO.builder()
                    .materialId(updateReqVO.getMaterialId())
                    .applyId(updateReqVO.getActivityId())
                    .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                    .startTime(checkPrmActivity.getStartTime())
                    .endTime(checkPrmActivity.getEndTime())
                    .newStartTime(updateReqVO.getStartTime())
                    .newEndTime(updateReqVO.getEndTime())
                    .build());

        }
/*
        // 刷新缓存
        prmActivityService.reloadSupplierActivity(updateReqVO.getActivityId());*/
    }

    /**
     * 删除买赠条件规则
     *
     * @param bgRuleId 买赠条件规则id
     */
    @Override
    public void deletePrmBgRule(Long bgRuleId) {
        // 删除
        prmBgRuleMapper.deleteById(bgRuleId);
    }

    /**
     * 批量删除买赠条件规则
     *
     * @param bgRuleIds 需要删除的买赠条件规则主键
     * @return 结果
     */
    @Override
    public void deletePrmBgRuleByBgRuleIds(Long[] bgRuleIds) {
        for (Long bgRuleId : bgRuleIds) {
            this.deletePrmBgRule(bgRuleId);
        }
    }

    /**
     * 获得买赠条件规则
     *
     * @param activityId 买赠条件规则id
     * @return 买赠条件规则
     */
    @Override
    public PrmBgActivityRespVo getPrmBgRule(Long activityId) {
        PrmBgActivityRespVo respVo = new PrmBgActivityRespVo();

        Long sysCode = SecurityUtils.getLoginUser().getSysCode();

        //获取促销主表信息
        PrmActivity prmActivity = prmActivityMapper.getActivityByPrmNo(new PrmActivityRespVO(activityId, sysCode, PrmNoEnum.BG.getType()));
        if (ObjectUtil.isEmpty(prmActivity)) return respVo;

        //填充促销主表信息
        respVo = HutoolBeanUtils.toBean(prmActivity, PrmBgActivityRespVo.class);

        //填充买赠详情数据
        List<PrmBgRule> bgRuleList = prmBgRuleMapper.getListByActivityId(activityId);
        if (ObjectUtil.isNotEmpty(bgRuleList)) {
            List<PrmBgRuleRespVO> bgRuleReqVOList = HutoolBeanUtils.toBean(bgRuleList, PrmBgRuleRespVO.class);
            List<PrmBgRuleRespVO> resultVoList = bgRuleReqVOList.stream().peek(x -> {
                //商品
                if (ObjectUtil.equal(NumberPool.INT_ZERO, x.getGiftType())) {
                    SkuDTO skuDTO = promotionCacheService.getSkuDTO(x.getSkuId());
                    if (ObjectUtil.isNotNull(skuDTO)) {
                        SpuDTO spuDTO = promotionCacheService.getSpuDTO(skuDTO.getSpuId());
                        x.setGiftName(ObjectUtil.isNull(spuDTO) ? "" : spuDTO.getSpuName());
                        //设置库存
                        x.setStock(redisStockService.getSurplusSaleQty(skuDTO.getSkuId()));
                    }
                } else if (ObjectUtil.equal(NumberPool.INT_ONE, x.getGiftType())) {
                    //优惠券
                    CouponTemplateDTO couponTemplate = promotionCacheService.getCouponTemplate(x.getCouponTemplateId());
                    x.setGiftName(ObjectUtil.isNull(couponTemplate) ? "" : couponTemplate.getCouponName());
                }
            }).collect(Collectors.toList());
            respVo.setPrmBgRuleRespVOList(resultVoList);

        }

        //填充门店列表
        List<ActivityBranchScopeDTO> branchScope = promotionCacheService.getBranchScope(activityId);
        //填充门店列表
        if (ObjectUtil.isNotEmpty(branchScope) && ObjectUtil.notEqual(NumberPool.INT_ZERO, respVo.getBranchScopeAllFlag())) {
            //组装门店数据
            respVo.setBranchIdList(branchScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getBranchId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        //填充渠道列表
        List<ActivityChannelScopeDTO> channelScope = promotionCacheService.getChannelScope(activityId);
        if (ObjectUtil.isNotEmpty(channelScope) && ObjectUtil.notEqual(NumberPool.INT_ZERO, respVo.getChanelScopeAllFlag())) {
            //组装渠道数据
            respVo.setChannelIdList(channelScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getChannelId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        //填充城市列表
        List<ActivityCityScopeDTO> cityScope = promotionCacheService.getCityScope(activityId);
        if (ObjectUtil.isNotEmpty(cityScope)) {
            //组装城市数据
            respVo.setAreaIdList(cityScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getAreaId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        //填充spu列表
        List<ActivitySpuScopeDTO> spuScope = promotionCacheService.getSpuScope(activityId);
        if (ObjectUtil.isNotEmpty(spuScope)) {
            //组装Spu数据
            respVo.setSpuIdList(spuScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getApplyId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                prmActivityWhiteOrBlackVO.setApplyType(x.getApplyType().intValue());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }

        //填充素材信息
        respVo.setMaterialApplyVO(prmActivityCommonService.getByMaterialApplyByMaterial(
                MaterialApplyVO.builder()
                        .applyId(prmActivity.getActivityId())
                        .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                        .startTime(prmActivity.getStartTime())
                        .endTime(prmActivity.getEndTime()).build()));

        return respVo;
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrmBgRule> getPrmBgRulePage(PrmBgRulePageReqVO pageReqVO) {
        return prmBgRuleMapper.selectPage(pageReqVO);
    }

    /**
     * @Description: 变更买赠状态
     * @Author: liuxingyu
     * @Date: 2024/5/24 16:53
     */
    @Override
    public Long changeBgStatus(PrmBgActivitySaveReqVO saveReqVO) {
        return prmActivityCommonService.changeStatus(saveReqVO.getActivityId(), saveReqVO.getPrmStatus());
    }


    /**
     * 新增/修改详情
     *
     * @param createReqVO 促销信息
     * @param type        1 新增  2 修改
     */
    private void insertDetail(PrmBgActivitySaveReqVO createReqVO, int type,Boolean f) {
        Long activityId = createReqVO.getActivityId();

        //修改时 需要先将数据原有详情数据删除后 再去新增
        if (NumberPool.INT_TWO == type) {
            deleteDetail(activityId);

        }

        //新增详情
        //插入买赠规则详情
        List<PrmBgRuleSaveReqVO> bgRuleSaveReqVOList = createReqVO.getBgRuleSaveReqVOList();
        if (ObjectUtil.isNotEmpty(bgRuleSaveReqVOList)) {
            List<PrmBgRule> bgRules = HutoolBeanUtils.toBean(bgRuleSaveReqVOList, PrmBgRule.class);
            List<PrmBgRule> insertBgRules = bgRules.stream().peek(x -> {
                //字段填充
                x.setActivityId(activityId);
                x.setStatus(STATE_ENABLE);
                if (f){
                    x.setBgRuleId(null);
                }
            }).collect(Collectors.toList());
            prmBgRuleMapper.insertBatch(insertBgRules);
        }

        //插入促销活动门店适用范围
        if (ObjectUtil.isNotEmpty(createReqVO.getBranchIdList())) {
            //绑定活动与门店关系
            List<PrmActivityBranchScope> branchScopes = createReqVO.getBranchIdList().stream().map(x -> {
                PrmActivityBranchScope branchScope = new PrmActivityBranchScope();
                branchScope.setActivityId(activityId);
                branchScope.setBranchId(Long.parseLong(x.getId()));
                branchScope.setWhiteOrBlack(x.getType());
                return branchScope;
            }).collect(Collectors.toList());
            branchScopeMapper.insertBatch(branchScopes);
        }
        //插入促销活动城市适用范围
        if (ObjectUtil.isNotEmpty(createReqVO.getAreaIdList())) {
            //绑定活动与区域城市关系
            List<PrmActivityCityScope> cityScope = createReqVO.getAreaIdList().stream().map(x -> {
                PrmActivityCityScope prmActivityCityScope = new PrmActivityCityScope();
                prmActivityCityScope.setActivityId(activityId);
                prmActivityCityScope.setAreaId(Long.parseLong(x.getId()));
                prmActivityCityScope.setWhiteOrBlack(x.getType());
                return prmActivityCityScope;
            }).collect(Collectors.toList());
            cityScopeMapper.insertBatch(cityScope);
        }
        //插入促销活动渠道适用范围
        if (ObjectUtil.isNotEmpty(createReqVO.getChannelIdList())) {
            //绑定活动与渠道关系
            List<PrmActivityChannelScope> channelScope = createReqVO.getChannelIdList().stream().map(x -> {
                PrmActivityChannelScope prmActivityChannelScope = new PrmActivityChannelScope();
                prmActivityChannelScope.setActivityId(activityId);
                prmActivityChannelScope.setChannelId(Long.parseLong(x.getId()));
                prmActivityChannelScope.setWhiteOrBlack(x.getType());
                return prmActivityChannelScope;
            }).collect(Collectors.toList());
            channelScopeMapper.insertBatch(channelScope);
        }
        //插入促销活动spu适用范围
        if (ObjectUtil.isNotEmpty(createReqVO.getSpuIdList())) {
            //绑定活动与渠道关系
            List<PrmActivitySpuScope> spuScope = createReqVO.getSpuIdList().stream().map(x -> {
                PrmActivitySpuScope prmActivitySpuScope = new PrmActivitySpuScope();
                prmActivitySpuScope.setActivityId(activityId);
                prmActivitySpuScope.setApplyId(Long.parseLong(x.getId()));
                prmActivitySpuScope.setWhiteOrBlack(x.getType());
                prmActivitySpuScope.setApplyType(x.getApplyType());
                return prmActivitySpuScope;
            }).collect(Collectors.toList());
            spuScopeMapper.insertBatch(spuScope);
        }


    }

    /**
     * @Description: 校验买赠规则
     * @Author: liuxingyu
     * @Date: 2024/5/24 16:56
     */
    private void checkFgRule(List<PrmBgRuleSaveReqVO> bgRuleList) {
        //活动不允许相同满赠金额
      /*  boolean repetition = bgRuleList.stream().collect(Collectors.groupingBy(PrmBgRuleSaveReqVO::getRuleQty, Collectors.counting()))
                .entrySet()
                .stream()
                .anyMatch(x -> x.getValue() > 1);
        if (repetition) {
            //throw exception(RULE_QTY_REPETITION);
        }*/
        //活动不允许相同满赠金额中, 存在多个skuID和商品单位大小giftSkuUnitType相同的数据  同一个skuID和giftSkuUnitType只能存在一条数据

        //活动不允许相同满赠金额中, 存在多个skuID和商品单位大小giftSkuUnitType相同的数据  同一个skuID和giftSkuUnitType只能存在一条数据
        boolean repetitionSku = bgRuleList.stream()
                .filter(req -> req.getCouponTemplateId() == null)
                .collect(Collectors.groupingBy(
                        t -> StringUtils.format("{}_{}", t.getRuleUnitType(), t.getRuleQty()),
                        Collectors.groupingBy(
                                PrmBgRuleSaveReqVO::getSkuId,
                                Collectors.groupingBy(
                                        PrmBgRuleSaveReqVO::getGiftSkuUnitType,
                                        Collectors.counting()
                                )
                        )
                ))
                .entrySet()
                .stream()
                .anyMatch(x -> x.getValue().entrySet().stream().anyMatch(y -> y.getValue().values().stream().anyMatch(count -> count > 1)));
        if (repetitionSku) {
            throw exception(FULL_AMT_SKU_REPETITION);
        }

        for (PrmBgRuleSaveReqVO prmBgRuleSaveReqVO : bgRuleList) {
            if (prmBgRuleSaveReqVO.getCouponTemplateId()!=null&&prmBgRuleSaveReqVO.getGiftType()==1){
                CouponTemplateDTO couponTemplate = promotionCacheService.getCouponTemplate(prmBgRuleSaveReqVO.getCouponTemplateId());
                if (!(couponTemplate.getStatus() == 0
                        && couponTemplate.getTemplateStartDate().getTime() < System.currentTimeMillis()
                        && couponTemplate.getTemplateEndDate().getTime() > System.currentTimeMillis())) {
                    throw new SecurityException("请选择进行中的优惠卷");
                }

            }
        }

    }

    /**
     * 删除买赠详情
     *
     * @param activityId
     */
    private void deleteDetail(Long activityId) {
        //删除买赠规则详情
        prmBgRuleMapper.deleteByActivityId(activityId);
        //删除促销活动门店适用范围
        branchScopeMapper.deleteByActivityId(activityId);
        //删除促销活动城市适用范围
        cityScopeMapper.deleteByActivityId(activityId);
        //删除销活动渠道适用范围
        channelScopeMapper.deleteByActivityId(activityId);
        //删除促销活动spu适用范围
        spuScopeMapper.deleteByActivityId(activityId);

        //清除缓存信息
        //删除促销活动门店适用范围缓存
        promotionCacheService.removeBranchScope(activityId);
        //删除促销活动城市适用范围缓存
        promotionCacheService.removeCityScope(activityId);
        //删除销活动渠道适用范围缓存
        promotionCacheService.removeChannelScope(activityId);
        //删除促销活动spu适用范围缓存
        promotionCacheService.removeSpuScopeScope(activityId);
    }

}
