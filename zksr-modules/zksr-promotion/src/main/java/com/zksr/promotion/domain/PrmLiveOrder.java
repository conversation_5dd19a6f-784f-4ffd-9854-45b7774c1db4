package com.zksr.promotion.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 直播订单对象 prm_live_order
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@TableName(value = "prm_live_order")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmLiveOrder extends BaseEntity {
    private static final long serialVersionUID=1L;

    /** ID主键 */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long id;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 直播ID */
    @Excel(name = "直播ID")
    private String activityId;

    /** 直播间名称 */
    @Excel(name = "直播间名称")
    private String name;

    /** 直播商品id */
    @Excel(name = "直播商品id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long liveProductId;

    /** 商品sku_id */
    @Excel(name = "商品sku_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 商品SPU_id */
    @Excel(name = "商品SPU_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    private String spuName;

    /** 直播价 */
    @Excel(name = "直播价")
    private BigDecimal livePrice;

    /** 商品品单位 数据字典（sys_prdt_unit） */
    @Excel(name = "商品品单位 数据字典", readConverterExp = "s=ys_prdt_unit")
    private String unit;

    /** 商品品单位名称 */
    @Excel(name = "商品品单位名称")
    private String unitName;

    /** 订单数量 */
    @Excel(name = "订单数量")
    private BigDecimal orderQty;

    /** 门店名称 */
    @Excel(name = "门店名称")
    private String branchName;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 门店地址 */
    @Excel(name = "门店地址")
    private String branchAddr;

    /** 是否删除：0-否，1-是 */
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

}
