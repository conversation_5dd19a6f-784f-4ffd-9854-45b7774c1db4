package com.zksr.promotion.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.coupon.dto.CouponTemplateExtendDTO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateExtendPageReqVO;
import com.zksr.promotion.controller.template.dto.PrmCouponTemplateReceiveRespVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateExtendRespVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateReceivePageVO;

import java.util.List;

/**
 * 优惠券模版拓展统计Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface IPrmCouponTemplateExtendService {

    /**
     * 获得优惠券模版拓展统计分页
     *
     * @param pageReqVO 分页查询
     * @return 优惠券模版拓展统计分页
     */
    PageResult<PrmCouponTemplateExtendRespVO> getPrmCouponTemplateExtendPage(PrmCouponTemplateExtendPageReqVO pageReqVO);

    void updateCouponTemplateOrderExtend(List<CouponTemplateExtendDTO> extendTotalVOList);

    void updateCouponTemplateCouponExtend(List<Long> couponTemplateIdList);

    /**
     * 分页查询优惠券模版门店领取列表
     * @param pageReqVO
     */
    PageResult<PrmCouponTemplateReceiveRespVO> getCounponTemplateReceiveListPage(PrmCouponTemplateReceivePageVO pageReqVO);
}
