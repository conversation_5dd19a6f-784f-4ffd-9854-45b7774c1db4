package com.zksr.promotion.controller.coupon.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("优惠券记录统计")
public class PrmCouponRespStatisticsVO {

    @ApiModelProperty("合计优惠卷")
    private Integer totalCoupon;

    @ApiModelProperty("未使用优惠卷")
    private Integer unusedCoupon;

    @ApiModelProperty("已使用优惠卷")
    private Integer usedCoupon;

    @ApiModelProperty("过期优惠卷")
    private Integer dueCoupon;

    @ApiModelProperty("合计优惠卷金额")
    private BigDecimal totalCouponMoney;

    @ApiModelProperty("用卷下单金额")
    private BigDecimal useCouponOrderMoney;

    public PrmCouponRespStatisticsVO(){
        this.totalCoupon = 0;
        this.unusedCoupon = 0;
        this.usedCoupon = 0;
        this.dueCoupon = 0;
        this.totalCouponMoney = BigDecimal.ZERO;
        this.useCouponOrderMoney = BigDecimal.ZERO;
    }

}
