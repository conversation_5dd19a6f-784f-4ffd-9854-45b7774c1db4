package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.domain.PrmCouponLog;
import com.zksr.promotion.controller.coupon.vo.PrmCouponLogPageReqVO;
import com.zksr.promotion.controller.coupon.vo.PrmCouponLogSaveReqVO;

/**
 * 优惠券日志Service接口
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
public interface IPrmCouponLogService {

    /**
     * 新增优惠券日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmCouponLog(@Valid PrmCouponLogSaveReqVO createReqVO);

    /**
     * 修改优惠券日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmCouponLog(@Valid PrmCouponLogSaveReqVO updateReqVO);

    /**
     * 删除优惠券日志
     *
     * @param couponLogId 优惠券日志
     */
    public void deletePrmCouponLog(Long couponLogId);

    /**
     * 批量删除优惠券日志
     *
     * @param couponLogIds 需要删除的优惠券日志主键集合
     * @return 结果
     */
    public void deletePrmCouponLogByCouponLogIds(Long[] couponLogIds);

    /**
     * 获得优惠券日志
     *
     * @param couponLogId 优惠券日志
     * @return 优惠券日志
     */
    public PrmCouponLog getPrmCouponLog(Long couponLogId);

    /**
     * 获得优惠券日志分页
     *
     * @param pageReqVO 分页查询
     * @return 优惠券日志分页
     */
    PageResult<PrmCouponLog> getPrmCouponLogPage(PrmCouponLogPageReqVO pageReqVO);

}
