package com.zksr.promotion.controller.activity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

@Data
@ApiModel("促销活动 - 促销报表分页返回实体 Request VO")
public class PrmActivityReportRespDTO {

    @ApiModelProperty(value = "活动ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long activityId;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动状态")
    private Integer activityStatus;

    @ApiModelProperty(value = "活动规则信息")
    private String activityRuleInfo;

    @ApiModelProperty(value = "活动类型")
    private String activityType;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date activityStartTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date activityEndTime;

    @ApiModelProperty(value = "参与活动客户数")
    private Long activityCustomerCount;

    @ApiModelProperty(value = "参与活动订单金额")
    private BigDecimal activityOrderAmtSum;
}
