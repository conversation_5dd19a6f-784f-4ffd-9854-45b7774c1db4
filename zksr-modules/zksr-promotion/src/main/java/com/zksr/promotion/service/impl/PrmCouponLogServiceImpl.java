package com.zksr.promotion.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmCouponLogMapper;
import com.zksr.promotion.domain.PrmCouponLog;
import com.zksr.promotion.controller.coupon.vo.PrmCouponLogPageReqVO;
import com.zksr.promotion.controller.coupon.vo.PrmCouponLogSaveReqVO;
import com.zksr.promotion.service.IPrmCouponLogService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 优惠券日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
@Service
public class PrmCouponLogServiceImpl implements IPrmCouponLogService {
    @Autowired
    private PrmCouponLogMapper prmCouponLogMapper;

    /**
     * 新增优惠券日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrmCouponLog(PrmCouponLogSaveReqVO createReqVO) {
        // 插入
        PrmCouponLog prmCouponLog = HutoolBeanUtils.toBean(createReqVO, PrmCouponLog.class);
        prmCouponLogMapper.insert(prmCouponLog);
        // 返回
        return prmCouponLog.getCouponLogId();
    }

    /**
     * 修改优惠券日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmCouponLog(PrmCouponLogSaveReqVO updateReqVO) {
        prmCouponLogMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrmCouponLog.class));
    }

    /**
     * 删除优惠券日志
     *
     * @param couponLogId 优惠券日志
     */
    @Override
    public void deletePrmCouponLog(Long couponLogId) {
        // 删除
        prmCouponLogMapper.deleteById(couponLogId);
    }

    /**
     * 批量删除优惠券日志
     *
     * @param couponLogIds 需要删除的优惠券日志主键
     * @return 结果
     */
    @Override
    public void deletePrmCouponLogByCouponLogIds(Long[] couponLogIds) {
        for(Long couponLogId : couponLogIds){
            this.deletePrmCouponLog(couponLogId);
        }
    }

    /**
     * 获得优惠券日志
     *
     * @param couponLogId 优惠券日志
     * @return 优惠券日志
     */
    @Override
    public PrmCouponLog getPrmCouponLog(Long couponLogId) {
        return prmCouponLogMapper.selectById(couponLogId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmCouponLog> getPrmCouponLogPage(PrmCouponLogPageReqVO pageReqVO) {
        return prmCouponLogMapper.selectPage(pageReqVO);
    }

}
