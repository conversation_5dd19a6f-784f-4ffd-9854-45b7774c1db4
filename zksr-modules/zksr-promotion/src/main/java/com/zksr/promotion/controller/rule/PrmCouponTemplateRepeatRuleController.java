package com.zksr.promotion.controller.rule;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateRepeatRulePageReqVO;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateRepeatRuleRespVO;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateRepeatRuleSaveReqVO;
import com.zksr.promotion.convert.rule.PrmCouponTemplateRepeatRuleConvert;
import com.zksr.promotion.domain.PrmCouponTemplateRepeatRule;
import com.zksr.promotion.service.IPrmCouponTemplateRepeatRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 优惠券模板-重复规则Controller
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Api(tags = "管理后台 - 优惠券模板-重复规则接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/repeatRule")
public class PrmCouponTemplateRepeatRuleController {

    @Autowired
    private IPrmCouponTemplateRepeatRuleService prmCouponTemplateRepeatRuleService;

    /**
     * 新增优惠券模板-重复规则
     */
    @ApiOperation(value = "新增优惠券模板-重复规则", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "优惠券模板-重复规则", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmCouponTemplateRepeatRuleSaveReqVO createReqVO) {
        return success(prmCouponTemplateRepeatRuleService.insertPrmCouponTemplateRepeatRule(createReqVO));
    }

    /**
     * 修改优惠券模板-重复规则
     */
    @ApiOperation(value = "修改优惠券模板-重复规则", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "优惠券模板-重复规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmCouponTemplateRepeatRuleSaveReqVO updateReqVO) {
        prmCouponTemplateRepeatRuleService.updatePrmCouponTemplateRepeatRule(updateReqVO);
        return success(true);
    }

    /**
     * 删除优惠券模板-重复规则
     */
    @ApiOperation(value = "删除优惠券模板-重复规则", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "优惠券模板-重复规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{couponTemplateRepeatRuleIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] couponTemplateRepeatRuleIds) {
        prmCouponTemplateRepeatRuleService.deletePrmCouponTemplateRepeatRuleByCouponTemplateRepeatRuleIds(couponTemplateRepeatRuleIds);
        return success(true);
    }

    /**
     * 获取优惠券模板-重复规则详细信息
     */
    @ApiOperation(value = "获得优惠券模板-重复规则详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{couponTemplateRepeatRuleId}")
    public CommonResult<PrmCouponTemplateRepeatRuleRespVO> getInfo(@PathVariable("couponTemplateRepeatRuleId") Long couponTemplateRepeatRuleId) {
        PrmCouponTemplateRepeatRule prmCouponTemplateRepeatRule = prmCouponTemplateRepeatRuleService.getPrmCouponTemplateRepeatRule(couponTemplateRepeatRuleId);
        return success(PrmCouponTemplateRepeatRuleConvert.INSTANCE.convert(prmCouponTemplateRepeatRule));
    }

    /**
     * 分页查询优惠券模板-重复规则
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得优惠券模板-重复规则分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmCouponTemplateRepeatRuleRespVO>> getPage(@Valid PrmCouponTemplateRepeatRulePageReqVO pageReqVO) {
        PageResult<PrmCouponTemplateRepeatRule> pageResult = prmCouponTemplateRepeatRuleService.getPrmCouponTemplateRepeatRulePage(pageReqVO);
        return success(PrmCouponTemplateRepeatRuleConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:repeat-rule:add";
        /** 编辑 */
        public static final String EDIT = "promotion:repeat-rule:edit";
        /** 删除 */
        public static final String DELETE = "promotion:repeat-rule:remove";
        /** 列表 */
        public static final String LIST = "promotion:repeat-rule:list";
        /** 查询 */
        public static final String GET = "promotion:repeat-rule:query";
        /** 停用 */
        public static final String DISABLE = "promotion:repeat-rule:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:repeat-rule:enable";
    }
}
