package com.zksr.promotion.controller.couponBatch;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.exception.enums.GlobalErrorCodeConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.controller.couponBatch.vo.CouponBatchBranchRespVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchPageReqVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchRespVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchSaveReqVO;
import com.zksr.promotion.convert.couponBatch.PrmCouponBatchConvert;
import com.zksr.promotion.domain.PrmCouponBatch;
import com.zksr.promotion.domain.excel.PrmBranchImportExcel;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmCouponBatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 优惠券批量发送Controller
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Api(tags = "管理后台 - 优惠券批量发送接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/couponBatch")
public class PrmCouponBatchController {

    @Autowired
    private IPrmCouponBatchService prmCouponBatchService;

    @Autowired
    private IPrmActivityCommonService prmActivityCommonService;

    /**
     * 新增优惠券批量发送
     */
    @ApiOperation(value = "新增优惠券批量发送", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "优惠券批量发送", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmCouponBatchSaveReqVO createReqVO) {
        return success(prmCouponBatchService.insertPrmCouponBatch(createReqVO));
    }

    /**
     * 修改优惠券批量发送
     */
    @ApiOperation(value = "修改优惠券批量发送", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "优惠券批量发送", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmCouponBatchSaveReqVO updateReqVO) {
        prmCouponBatchService.updatePrmCouponBatch(updateReqVO);
        return success(true);
    }

    /**
     * 删除优惠券批量发送
     */
    @ApiOperation(value = "删除优惠券批量发送", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "优惠券批量发送", businessType = BusinessType.DELETE)
    @DeleteMapping("/{couponBatchIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] couponBatchIds) {
        prmCouponBatchService.deletePrmCouponBatchByCouponBatchIds(couponBatchIds);
        return success(true);
    }

    /**
     * 获取优惠券批量发送详细信息
     */
    @ApiOperation(value = "获得优惠券批量发送详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{couponBatchId}")
    public CommonResult<PrmCouponBatchRespVO> getInfo(@PathVariable("couponBatchId") Long couponBatchId) {
        return success(prmCouponBatchService.getPrmCouponBatch(couponBatchId));
    }

    /**
     * 获取优惠券批量发送门店信息列表分页
     */
    @ApiOperation(value = "获取优惠券批量发送门店信息列表分页", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @GetMapping("/couponBatchBranchList")
    public CommonResult<PageResult<CouponBatchBranchRespVO>> getPrmCouponBatchBranchList(@Valid PrmCouponBatchPageReqVO pageReqVO) {
        return success(prmCouponBatchService.getPrmCouponBatchBranchList(pageReqVO));
    }

    /**
     * 分页查询优惠券批量发送
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得优惠券批量发送分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmCouponBatchRespVO>> getPage(@Valid PrmCouponBatchPageReqVO pageReqVO) {
        PageResult<PrmCouponBatch> pageResult = prmCouponBatchService.getPrmCouponBatchPage(pageReqVO);
        return success(PrmCouponBatchConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 审核优惠券批量发送
     */
    @ApiOperation(value = "批量审核优惠券批量发送", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.AUDIT)
    @RequiresPermissions(Permissions.AUDIT)
    @Log(title = "优惠券批量发送", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/{couponBatchId}")
    public CommonResult<Boolean> audit(@PathVariable("couponBatchId") String couponBatchId) {
        prmCouponBatchService.auditPrmCouponBatch(couponBatchId);
        return success(true);
    }

    @ApiOperation(value = "导入门店", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORTBRANCH)
    @Log(title = "导入门店", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORTBRANCH)
    @PostMapping("/importBranchData")
    public CommonResult<Map<String, Object>> importBranchData(MultipartFile file) throws Exception {
        ExcelUtil<PrmBranchImportExcel> util = new ExcelUtil<>(PrmBranchImportExcel.class);
        List<PrmBranchImportExcel> prmBranchList = util.importExcel(file.getInputStream(), 0);
        // 调用服务层方法
        Map<String, Object> importResult = prmActivityCommonService.importPrmBranchsData(prmBranchList);
        CommonResult<Map<String, Object>> result = new CommonResult<>();
        result.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        result.setData(importResult);
        result.setMsg((String) importResult.get("message"));
        return result;
    }

    @PostMapping("/importBranchTemplate")
    @ApiOperation(value = "下载导入门店信息模板", httpMethod = HttpMethod.POST)
    public void importBranchTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<PrmBranchImportExcel> util = new ExcelUtil<>(PrmBranchImportExcel.class);
        util.importTemplateExcel(response, "导入门店信息导入", StringUtils.EMPTY, null);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "couponBatch:couponBatch:add";
        /**
         * 编辑
         */
        public static final String EDIT = "couponBatch:couponBatch:edit";
        /**
         * 删除
         */
        public static final String DELETE = "couponBatch:couponBatch:remove";
        /**
         * 列表
         */
        public static final String LIST = "couponBatch:couponBatch:list";
        /**
         * 查询
         */
        public static final String GET = "couponBatch:couponBatch:query";
        /**
         * 停用
         */
        public static final String DISABLE = "couponBatch:couponBatch:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "couponBatch:couponBatch:enable";
        /**
         * 审核
         */
        public static final String AUDIT = "couponBatch:couponBatch:audit";
        /**
         * 导入门店
         */
        public static final String IMPORTBRANCH = "couponBatch:couponBatch:importBranch";
    }
}
