package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 组合商品规则对象 prm_cb_rule
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@TableName(value = "prm_cb_rule")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmCbRule extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 组合商品规则id */
    @TableId
    private Long cbRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    private Long activityId;

    /** 组合商品id */
    @Excel(name = "组合商品id")
    private Long spuCombineId;

    /** 上架展示分类ID */
    @Excel(name = "上架展示分类ID")
    private Long shelfClassId;

}
