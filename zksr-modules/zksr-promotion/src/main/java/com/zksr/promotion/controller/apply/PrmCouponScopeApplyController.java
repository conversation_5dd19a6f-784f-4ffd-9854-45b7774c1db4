package com.zksr.promotion.controller.apply;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.domain.PrmCouponScopeApply;
import com.zksr.promotion.service.IPrmCouponScopeApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.promotion.controller.apply.vo.PrmCouponScopeApplyPageReqVO;
import com.zksr.promotion.controller.apply.vo.PrmCouponScopeApplySaveReqVO;
import com.zksr.promotion.controller.apply.vo.PrmCouponScopeApplyRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 优惠券关联的使用范围, 领取范围中间Controller
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Api(tags = "管理后台 - 优惠券关联的使用范围, 领取范围中间接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/apply")
public class PrmCouponScopeApplyController {
    @Autowired
    private IPrmCouponScopeApplyService prmCouponScopeApplyService;

    /**
     * 新增优惠券关联的使用范围, 领取范围中间
     */
    @ApiOperation(value = "新增优惠券关联的使用范围, 领取范围中间", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "优惠券关联的使用范围, 领取范围中间", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmCouponScopeApplySaveReqVO createReqVO) {
        return success(prmCouponScopeApplyService.insertPrmCouponScopeApply(createReqVO));
    }

    /**
     * 修改优惠券关联的使用范围, 领取范围中间
     */
    @ApiOperation(value = "修改优惠券关联的使用范围, 领取范围中间", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "优惠券关联的使用范围, 领取范围中间", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmCouponScopeApplySaveReqVO updateReqVO) {
            prmCouponScopeApplyService.updatePrmCouponScopeApply(updateReqVO);
        return success(true);
    }

    /**
     * 删除优惠券关联的使用范围, 领取范围中间
     */
    @ApiOperation(value = "删除优惠券关联的使用范围, 领取范围中间", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "优惠券关联的使用范围, 领取范围中间", businessType = BusinessType.DELETE)
    @DeleteMapping("/{scopeApplyIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] scopeApplyIds) {
        prmCouponScopeApplyService.deletePrmCouponScopeApplyByScopeApplyIds(scopeApplyIds);
        return success(true);
    }

    /**
     * 获取优惠券关联的使用范围, 领取范围中间详细信息
     */
    @ApiOperation(value = "获得优惠券关联的使用范围, 领取范围中间详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{scopeApplyId}")
    public CommonResult<PrmCouponScopeApplyRespVO> getInfo(@PathVariable("scopeApplyId") Long scopeApplyId) {
        PrmCouponScopeApply prmCouponScopeApply = prmCouponScopeApplyService.getPrmCouponScopeApply(scopeApplyId);
        return success(HutoolBeanUtils.toBean(prmCouponScopeApply, PrmCouponScopeApplyRespVO.class));
    }

    /**
     * 分页查询优惠券关联的使用范围, 领取范围中间
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得优惠券关联的使用范围, 领取范围中间分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmCouponScopeApplyRespVO>> getPage(@Valid PrmCouponScopeApplyPageReqVO pageReqVO) {
        PageResult<PrmCouponScopeApply> pageResult = prmCouponScopeApplyService.getPrmCouponScopeApplyPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, PrmCouponScopeApplyRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:apply:add";
        /** 编辑 */
        public static final String EDIT = "promotion:apply:edit";
        /** 删除 */
        public static final String DELETE = "promotion:apply:remove";
        /** 列表 */
        public static final String LIST = "promotion:apply:list";
        /** 查询 */
        public static final String GET = "promotion:apply:query";
        /** 停用 */
        public static final String DISABLE = "promotion:apply:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:apply:enable";
    }
}
