package com.zksr.promotion.controller.product.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 直播商品对象 prm_live_product
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Data
@ApiModel("直播商品 - prm_live_product Response VO")
public class PrmLiveProductRespVO {
    private static final long serialVersionUID = 1L;

    /** ID主键 */
    @ApiModelProperty(value = "版本号")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long id;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 直播ID */
    @Excel(name = "直播ID")
    @ApiModelProperty(value = "直播ID")
    private String activityId;

    /** 直播间名称 */
    @Excel(name = "直播间名称")
    @ApiModelProperty(value = "直播间名称")
    private String name;

    /** 商品sku_id */
    @Excel(name = "商品sku_id")
    @ApiModelProperty(value = "商品sku_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 商品SPU_id */
    @Excel(name = "商品SPU_id")
    @ApiModelProperty(value = "商品SPU_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    @ApiModelProperty(value = "商品SPU编号")
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    /** 标准价(原价) */
    @Excel(name = "标准价(原价)")
    @ApiModelProperty(value = "标准价(原价)")
    private BigDecimal markPrice;

    /** 直播价 */
    @Excel(name = "直播价")
    @ApiModelProperty(value = "直播价")
    private BigDecimal livePrice;

    /** 商品品单位 数据字典（sys_prdt_unit） */
    @Excel(name = "商品品单位 数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "商品品单位 数据字典")
    private String unit;

    /** 商品品单位名称 */
    @Excel(name = "商品品单位名称")
    @ApiModelProperty(value = "商品品单位名称")
    private String unitName;

    /** 图片1 */
    @Excel(name = "图片1")
    @ApiModelProperty(value = "图片1")
    private String imageUrl1;

    /** 图片2 */
    @Excel(name = "图片2")
    @ApiModelProperty(value = "图片2")
    private String imageUrl2;

    /** 图片3 */
    @Excel(name = "图片3")
    @ApiModelProperty(value = "图片3")
    private String imageUrl3;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    /** 是否删除：0-否，1-是 */
    @ApiModelProperty(value = "入驻商名称")
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号")
    private Long version;

    /** 弹框商品标记，0：不是，1：是 */
    @Excel(name = "弹框商品标记，0：不是，1：是")
    @ApiModelProperty(value = "弹框商品标记，0：不是，1：是")
    private Long broadcastFlag;

}
