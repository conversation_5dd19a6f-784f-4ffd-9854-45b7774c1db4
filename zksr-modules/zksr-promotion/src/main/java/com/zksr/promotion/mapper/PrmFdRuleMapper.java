package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.controller.rule.vo.PrmFdRulePageReqVO;
import com.zksr.promotion.domain.PrmFdRule;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 满减活动规则Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Mapper
public interface PrmFdRuleMapper extends BaseMapperX<PrmFdRule> {
    default PageResult<PrmFdRule> selectPage(PrmFdRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmFdRule>()
                    .eqIfPresent(PrmFdRule::getFdRuleId, reqVO.getFdRuleId())
                    .eqIfPresent(PrmFdRule::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmFdRule::getActivityId, reqVO.getActivityId())
                    .eqIfPresent(PrmFdRule::getFullAmt, reqVO.getFullAmt())
                    .eqIfPresent(PrmFdRule::getFullQty, reqVO.getFullQty())
                    .eqIfPresent(PrmFdRule::getDiscountAmt, reqVO.getDiscountAmt())
                    .eqIfPresent(PrmFdRule::getStatus, reqVO.getStatus())
                .orderByDesc(PrmFdRule::getFdRuleId));
    }

    default Integer deleteByActivityId(Long activityId){
        return delete(new LambdaUpdateWrapper<PrmFdRule>()
                .eq(PrmFdRule::getActivityId,activityId));
    }

    default List<PrmFdRule> selectByActivityId(Long activityId) {
        return selectList( new LambdaQueryWrapperX<PrmFdRule>()
                .eqIfPresent(PrmFdRule::getActivityId, activityId)
                .eqIfPresent(PrmFdRule::getStatus, NumberPool.INT_ONE)
                .orderByDesc(PrmFdRule::getFdRuleId));
    }


    default List<PrmFdRule> getprmFdRulesByActivityId(Long activityId){
        return selectList( new LambdaQueryWrapperX<PrmFdRule>()
                .eqIfPresent(PrmFdRule::getActivityId, activityId));
    }
}
