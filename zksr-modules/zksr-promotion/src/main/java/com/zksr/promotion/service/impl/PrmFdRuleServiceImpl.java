package com.zksr.promotion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.config.CustomIdGenerator;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.materialApply.dto.MaterialApplyDTO;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.controller.activity.vo.PrmActivityCheckScopeVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityPageReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.rule.dto.PrmFdRuleDTO;
import com.zksr.promotion.controller.rule.vo.PrmActivityWhiteOrBlackVO;
import com.zksr.promotion.controller.rule.vo.PrmFdRuleRespVO;
import com.zksr.promotion.controller.rule.vo.PrmFdRuleSaveReqVO;
import com.zksr.promotion.convert.activity.PrmActivityConvert;
import com.zksr.promotion.domain.*;
import com.zksr.promotion.mapper.*;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPrmFdRuleService;
import com.zksr.promotion.service.IPromotionCacheService;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.PrmConstants.PRM_STATUS_1;
import static com.zksr.common.core.constant.PrmConstants.PRM_STATUS_3;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.PRDT_MATERIAL_APPLY_TYPE_1;
import static com.zksr.promotion.enums.ErrorCodeConstants.CHANEL_BRANCH_ONLY_ONE;
import static com.zksr.promotion.enums.ErrorCodeConstants.START_TIME_GREATER_THAN_END_TIME;

/**
 * 满减活动规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Service
public class PrmFdRuleServiceImpl implements IPrmFdRuleService {
    @Autowired
    private PrmFdRuleMapper prmFdRuleMapper;

    @Autowired
    private PrmActivityMapper prmActivityMapper;

    @Autowired
    private PrmActivityCityScopeMapper prmActivityCityScopeMapper;

    @Autowired
    private PrmActivityBranchScopeMapper prmActivityBranchScopeMapper;

    @Autowired
    private PrmActivityChannelScopeMapper prmActivityChannelScopeMapper;

    @Autowired
    private PrmActivitySpuScopeMapper prmActivitySpuScopeMapper;

    @Autowired
    private PrmActivitySupplierScopeMapper prmActivitySupplierScopeMapper;

    @Autowired
    private CustomIdGenerator generator;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Autowired
    private IPrmActivityCommonService prmActivityCommonService;

    @Autowired
    private IPrmActivityService prmActivityService;

    /**
     * 新增满减活动规则
     *
     * @param prmFdRuleDTO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertPrmFdRule(PrmFdRuleDTO prmFdRuleDTO) {
        prmFdRuleDTO.setActivityId(null);
        PrmActivity activity = prmActivityCommonService.activityChangeCheck(HutoolBeanUtils.toBean(prmFdRuleDTO, PrmActivityCheckScopeVO.class));
        prmFdRuleDTO.setEffectTime(activity.getEffectTime());
        prmFdRuleDTO.setEffectMan(activity.getEffectMan());
        // 插入
        PrmActivity prmActivity = HutoolBeanUtils.toBean(prmFdRuleDTO, PrmActivity.class);
        prmActivity.setPrmNo(PrmNoEnum.FD.getType());
        prmActivity.setPrmSheetNo(PrmNoEnum.FD.getType() + generator.nextId());
        //校验商品类型(平台商：1全国  运营商：2本地)
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        prmActivity.setFuncScope(ObjectUtil.isNull(dcId) ? NumberPool.INT_ONE : NumberPool.INT_TWO);
        if (BeanUtil.isEmpty(prmActivity.getPrmStatus())) {
            prmActivity.setPrmStatus(NumberPool.INT_ZERO);
        }
        prmActivityMapper.insert(prmActivity);
        insertAssociation(prmFdRuleDTO, prmActivity,true);

        //新增素材信息
        if(ToolUtil.isNotEmpty(prmFdRuleDTO.getMaterialId())){
            prmActivityCommonService.addMaterialApply(MaterialApplyDTO.builder()
                    .materialId(prmFdRuleDTO.getMaterialId())
                    .applyId(prmActivity.getActivityId())
                    .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                    .startTime(prmFdRuleDTO.getStartTime())
                    .endTime(prmFdRuleDTO.getEndTime()).build());

        }
        // 刷新缓存
        prmActivityService.reloadSupplierActivity(prmActivity.getActivityId());
        return prmActivity.getActivityId();
    }

    private void insertAssociation(PrmFdRuleDTO prmFdRuleDTO, PrmActivity prmActivity,Boolean f) {
        //删除满减表规则
        prmFdRuleMapper.deleteByActivityId(prmActivity.getActivityId());
        //插入满减规则
        List<PrmFdRuleSaveReqVO> ruleSaveReqVOList = prmFdRuleDTO.getPrmFdRuleSaveReqVOList();
        ruleSaveReqVOList.forEach(ruleSaveReqVO -> {
            PrmFdRule prmFdRule = HutoolBeanUtils.toBean(ruleSaveReqVO, PrmFdRule.class);
            prmFdRule.setActivityId(prmActivity.getActivityId());
            if (ObjectUtil.isEmpty(prmFdRuleDTO.getPrmStatus())) {
                prmFdRule.setStatus(NumberPool.INT_ZERO);
            } else if (ObjectUtil.isNotEmpty(prmFdRuleDTO.getPrmStatus()) && prmFdRuleDTO.getPrmStatus().equals(NumberPool.INT_ONE)) {
                prmFdRule.setStatus(NumberPool.INT_ONE);
            }
            if (prmActivity.getAmtOrQty().equals(NumberPool.INT_ZERO)) {
                prmFdRule.setFullAmt(ruleSaveReqVO.getFullAmt());

            } else {
                prmFdRule.setFullQty(ruleSaveReqVO.getFullAmt().intValue());
            }
            if (f){
                prmFdRule.setFdRuleId(null);
            }
            prmFdRuleMapper.insert(prmFdRule);
        });
        //删除促销活动门店适用范围
        prmActivityBranchScopeMapper.deleteByActivityId(prmActivity.getActivityId());
        //删除促销活动城市适用范围
        prmActivityCityScopeMapper.deleteByActivityId(prmActivity.getActivityId());
        //删除销活动渠道适用范围
        prmActivityChannelScopeMapper.deleteByActivityId(prmActivity.getActivityId());
        //删除促销活动spu适用范围
        prmActivitySpuScopeMapper.deleteByActivityId(prmActivity.getActivityId());
        //删除促销活动入驻商适用范围
        prmActivitySupplierScopeMapper.deleteSupplierByActivityId(prmActivity.getActivityId());
        List<Long> supplierIds = prmFdRuleDTO.getSupplierIds();
        // 保存活动入驻商信息
        if (ToolUtil.isNotEmpty(supplierIds)) {
            List<PrmActivitySupplierScope> activitySupplierScopeList = supplierIds.stream().distinct().map(t -> {
                        PrmActivitySupplierScope prmActivitySupplierScope = new PrmActivitySupplierScope();
                        prmActivitySupplierScope.setActivityId(prmActivity.getActivityId());
                        prmActivitySupplierScope.setSupplierId(t);
                        // 默认为白名单
                        prmActivitySupplierScope.setWhiteOrBlack(NumberPool.INT_ONE);
                        prmActivitySupplierScope.setSysCode(SecurityUtils.getLoginUser().getSysCode());
                        return prmActivitySupplierScope;
                    }
            ).collect(Collectors.toList());
            prmActivitySupplierScopeMapper.insertBatch(activitySupplierScopeList);
        }

        //插入门店或者渠道
        if (prmFdRuleDTO.getChannelIds() != null) {
            //指定渠道
            List<PrmActivityWhiteOrBlackVO> channelIds = prmFdRuleDTO.getChannelIds();
            channelIds.forEach(channelId -> {
                PrmActivityChannelScope channelScope = new PrmActivityChannelScope();
                channelScope.setActivityId(prmActivity.getActivityId());
                channelScope.setChannelId(Long.parseLong(channelId.getId()));
                channelScope.setWhiteOrBlack(channelId.getType());
                channelScope.setSysCode(SecurityUtils.getLoginUser().getSysCode());
                prmActivityChannelScopeMapper.insert(channelScope);
            });

        }
        if (prmFdRuleDTO.getBranchScopeAllFlag() != null) {
            //指定门店
            List<PrmActivityWhiteOrBlackVO> branchIds = new ArrayList<>();
            branchIds = prmFdRuleDTO.getBranchIds();
            if (ToolUtil.isNotEmpty(branchIds)) {
                branchIds.forEach(branchId -> {
                    PrmActivityBranchScope brandScope = new PrmActivityBranchScope();
                    brandScope.setActivityId(prmActivity.getActivityId());
                    brandScope.setBranchId(Long.parseLong(branchId.getId()));
                    brandScope.setWhiteOrBlack(branchId.getType());
                    brandScope.setSysCode(SecurityUtils.getLoginUser().getSysCode());
                    prmActivityBranchScopeMapper.insert(brandScope);
                });
            }
        }

        //插入适用范围
        //不等于0，说明是商品，或者类别，或者品牌，需要插入以上类型的多个数据
        List<PrmActivityWhiteOrBlackVO> spuScopeIds = prmFdRuleDTO.getSpuIdList();
        if (ToolUtil.isNotEmpty(spuScopeIds)) {
            spuScopeIds.forEach(spuScopeId -> {
                PrmActivitySpuScope spuScope = new PrmActivitySpuScope();
                spuScope.setActivityId(prmActivity.getActivityId());
                spuScope.setApplyId(Long.parseLong(spuScopeId.getId()));
                spuScope.setWhiteOrBlack(spuScopeId.getType());
                spuScope.setApplyType(spuScopeId.getApplyType());
                prmActivitySpuScopeMapper.insert(spuScope);
            });
        }
        //插入城市
        List<PrmActivityWhiteOrBlackVO> cityIds = prmFdRuleDTO.getAreaIds();
        if (ToolUtil.isNotEmpty(cityIds)) {
            cityIds.forEach(cityId -> {
                PrmActivityCityScope cityScope = new PrmActivityCityScope();
                cityScope.setActivityId(prmActivity.getActivityId());
                cityScope.setAreaId(Long.parseLong(cityId.getId()));
                cityScope.setWhiteOrBlack(cityId.getType());
                cityScope.setSysCode(SecurityUtils.getLoginUser().getSysCode());
                prmActivityCityScopeMapper.insert(cityScope);
            });
        }


        //删除促销活动门店适用范围缓存
        promotionCacheService.removeBranchScope(prmActivity.getActivityId());
        //删除促销活动城市适用范围缓存
        promotionCacheService.removeCityScope(prmActivity.getActivityId());
        //删除销活动渠道适用范围缓存
        promotionCacheService.removeChannelScope(prmActivity.getActivityId());
        //删除促销活动spu适用范围缓存
        promotionCacheService.removeSpuScopeScope(prmActivity.getActivityId());
        //删除促销活动入驻商使用范围缓存
        promotionCacheService.removeSupplierScope(prmActivity.getActivityId());
    }

    /**
     * 修改满减活动规则
     *
     * @param rpmFdRule 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmFdRule(PrmFdRuleDTO rpmFdRule) {

        PrmActivity checkPrmActivity = prmActivityCommonService.checkUpdate(rpmFdRule.getActivityId());
        PrmActivity prmActivity = prmActivityCommonService.activityChangeCheck(HutoolBeanUtils.toBean(rpmFdRule, PrmActivityCheckScopeVO.class));
        rpmFdRule.setEffectTime(prmActivity.getEffectTime());
        rpmFdRule.setEffectMan(prmActivity.getEffectMan());
        PrmActivity activity = HutoolBeanUtils.toBean(rpmFdRule, PrmActivity.class);
        if (rpmFdRule.getPrmStatus().equals(NumberPool.INT_ONE)) {
            activity.setEffectTime(new Date());
            activity.setEffectMan(SecurityUtils.getLoginUser().getUsername());
        }
        prmActivityMapper.updateById(activity);

        insertAssociation(rpmFdRule, activity,false);

        //修改素材信息
        if(ToolUtil.isNotEmpty(rpmFdRule.getMaterialId())){
            prmActivityCommonService.editMaterialApply(MaterialApplyDTO.builder()
                    .materialId(rpmFdRule.getMaterialId())
                    .applyId(rpmFdRule.getActivityId())
                    .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                    .startTime(checkPrmActivity.getStartTime())
                    .endTime(checkPrmActivity.getEndTime())
                    .newStartTime(rpmFdRule.getStartTime())
                    .newEndTime(rpmFdRule.getEndTime())
                    .build());

        }
/*        // 刷新缓存
        prmActivityService.reloadSupplierActivity(prmActivity.getActivityId());*/
    }

    /**
     * 删除满减活动规则
     *
     * @param fdRuleId 满减活动规则id
     */
    @Override
    public void deletePrmFdRule(Long fdRuleId) {
        // 删除
        prmFdRuleMapper.deleteById(fdRuleId);
    }

    /**
     * 批量删除满减活动规则
     *
     * @param fdRuleIds 需要删除的满减活动规则主键
     * @return 结果
     */
    @Override
    public void deletePrmFdRuleByFdRuleIds(Long[] fdRuleIds) {
        for (Long fdRuleId : fdRuleIds) {
            this.deletePrmFdRule(fdRuleId);
        }
    }

    /**
     * 获得满减活动规则
     *
     * @param activityId 满减活动规则id
     * @return 满减活动规则
     */
    @Override
    public PrmFdRuleRespVO getPrmFdRule(Long activityId) {

        PrmFdRuleRespVO prmFdRule = prmActivityMapper.getPrmFdRule(activityId);
        List<ActivityBranchScopeDTO> branchScope = promotionCacheService.getBranchScope(activityId);
        //填充门店列表
        if (ObjectUtil.isNotEmpty(branchScope)) {
            //组装门店数据
            prmFdRule.setBranchIds(branchScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getBranchId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        //填充渠道列表
        List<ActivityChannelScopeDTO> channelScope = promotionCacheService.getChannelScope(activityId);
        if (ObjectUtil.isNotEmpty(channelScope)) {
            prmFdRule.setChannelIds(channelScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getChannelId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        //填充城市列表
        List<ActivityCityScopeDTO> cityScope = promotionCacheService.getCityScope(activityId);
        if (ObjectUtil.isNotEmpty(cityScope)) {
            prmFdRule.setAreaIds(cityScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getAreaId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        //填充spu列表
        List<ActivitySpuScopeDTO> spuScope = promotionCacheService.getSpuScope(activityId);
        if (ObjectUtil.isNotEmpty(spuScope)) {
            prmFdRule.setSpuIdList(spuScope.stream().map(x -> {
                PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                prmActivityWhiteOrBlackVO.setId(x.getApplyId().toString());
                prmActivityWhiteOrBlackVO.setType(x.getWhiteOrBlack());
                prmActivityWhiteOrBlackVO.setApplyType(x.getApplyType().intValue());
                return prmActivityWhiteOrBlackVO;
            }).collect(Collectors.toList()));
        }
        // 查询促销活动供应商列表
        List<ActivitySupplierScopeDTO> supplierScopeDTOList = promotionCacheService.getSupplierScope(activityId);
        if (CollectionUtils.isNotEmpty(supplierScopeDTOList)) {
            prmFdRule.setSupplierIds(supplierScopeDTOList.stream().filter(t -> Objects.nonNull(t) && Objects.nonNull(t.getSupplierId()))
                    .map(t -> String.valueOf(t.getSupplierId())).collect(Collectors.toList()));
        } else {
            // 兼容旧数据
            prmFdRule.setSupplierIds(Lists.newArrayList());
        }

        //填充素材信息
        prmFdRule.setMaterialApplyVO(prmActivityCommonService.getByMaterialApplyByMaterial(
                MaterialApplyVO.builder()
                .applyId(prmFdRule.getActivityId())
                .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                .startTime(prmFdRule.getStartTime())
                .endTime(prmFdRule.getEndTime()).build()));

        return prmFdRule;
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrmActivityRespVO> getPrmFdRulePage(PrmActivityPageReqVO pageReqVO) {
        PageResult<PrmActivityRespVO> pageResult = PrmActivityConvert.INSTANCE.convertPage(prmActivityMapper.selectPage(pageReqVO));

        //获取素材信息
        List<Long> activityIdList = pageResult.getList().stream().map(PrmActivityRespVO::getActivityId).collect(Collectors.toList());

        //获取生效中的素材应用信息
        List<MaterialApplyDTO> applyList = new ArrayList<>();
        if(!activityIdList.isEmpty()){
            applyList = prmActivityCommonService.getByMaterialApplyByApplyIds(activityIdList);
        }

        Map<Long, MaterialApplyDTO> applyMap = applyList.stream().collect(Collectors.toMap(MaterialApplyDTO::getApplyId, x -> x));

        //循环处理列表信息
        pageResult.getList().forEach(x -> {
            //已失效的促销列表：条件： 已启用并且活动结束时间小于或等于当前时间
            if(PRM_STATUS_1.equals(x.getPrmStatus()) && x.getEndTime().before(DateUtils.getNowDate())){
                x.setPrmStatus(PRM_STATUS_3);
            }

            // 设置入驻商名称
            SupplierDTO supplierDTO = promotionCacheService.getSupplierDTO(x.getSupplierId());
            if (Objects.nonNull(supplierDTO)) {
                x.setSupplierName(supplierDTO.getSupplierName());
            }

            //设置素材信息
            MaterialApplyDTO materialApplyDTO = applyMap.get(x.getActivityId());
            if(ToolUtil.isNotEmpty(materialApplyDTO)){
                x.setMaterialApplyId(materialApplyDTO.getMaterialApplyId());
                x.setMaterialId(materialApplyDTO.getMaterialId());
                x.setImg(materialApplyDTO.getImg());
            }

        });


        return pageResult;
    }

    /**
     * 启用活动
     *
     * @param isEnable
     */
    @Override
    public Boolean isEnableActivity(PrmFdRuleDTO isEnable) {
        // 更新活动表
        PrmActivity activity = HutoolBeanUtils.toBean(isEnable, PrmActivity.class);
        if (Objects.equals(isEnable.getPrmStatus(), NumberPool.INT_ONE)) { // 启用时，才填充操作人
            activity.setEffectMan(SecurityUtils.getLoginUser().getUsername());
            activity.setEffectTime(new Date());
        }
        prmActivityMapper.updateById(activity);
        // 更新规则表
        List<PrmFdRule> prmFdRules = prmFdRuleMapper.getprmFdRulesByActivityId(isEnable.getActivityId());
        //把状态改根据prmStatus改成一致
        if (ObjectUtil.isNotEmpty(prmFdRules)) {
            for (PrmFdRule prmFdRule : prmFdRules) {
                if (isEnable.getPrmStatus().equals(NumberPool.INT_TWO)) {
                    prmFdRule.setStatus(NumberPool.INT_ZERO);
                    prmFdRuleMapper.updateById(prmFdRule);
                } else {
                    prmFdRule.setStatus(isEnable.getPrmStatus());
                    prmFdRuleMapper.updateById(prmFdRule);
                }
            }
        }
        return Boolean.TRUE;
    }


    private void verify(PrmFdRuleDTO prmFdRuleDTO) {
        //校验门店与渠道
        if (ObjectUtil.isNotNull(prmFdRuleDTO.getChanelScopeAllFlag()) && ObjectUtil.isNotNull(prmFdRuleDTO.getBranchScopeAllFlag())) {
            throw exception(CHANEL_BRANCH_ONLY_ONE);
        }
        //校验活动有效时间
        if (prmFdRuleDTO.getEndTime().getTime() < prmFdRuleDTO.getStartTime().getTime()) {
            throw exception(START_TIME_GREATER_THAN_END_TIME);
        }
    }
}
