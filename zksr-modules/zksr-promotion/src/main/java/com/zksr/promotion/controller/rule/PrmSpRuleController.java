package com.zksr.promotion.controller.rule;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.alicp.jetcache.Cache;
import com.zksr.common.core.exception.enums.GlobalErrorCodeConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.product.api.material.vo.MaterialCacheVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import com.zksr.promotion.controller.rule.dto.PrmSkRuleDTO;
import com.zksr.promotion.domain.excel.PrmBranchImportExcel;
import com.zksr.promotion.domain.excel.PrmSpRuleImportExcel;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.common.core.utils.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.domain.PrmSpRule;
import com.zksr.promotion.service.IPrmSpRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.promotion.controller.rule.vo.PrmSpRulePageReqVO;
import com.zksr.promotion.controller.rule.vo.PrmSpRuleSaveReqVO;
import com.zksr.promotion.controller.rule.vo.PrmSpRuleRespVO;
import com.zksr.promotion.convert.rule.PrmSpRuleConvert;
import com.zksr.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 特价活动规则Controller
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Api(tags = "管理后台 - 特价活动规则接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/spRule")
public class PrmSpRuleController {

    @Autowired
    private IPrmSpRuleService prmSpRuleService;

    @Autowired
    private IPrmActivityService activityService;

    @Autowired
    private IPrmActivityCommonService prmActivityCommonService;

    @Autowired
    private Cache<String, MaterialCacheVO> materialCache;

    /**
     * 新增特价活动规则
     */
    @ApiOperation(value = "新增特价活动规则", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "特价活动规则", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmSkRuleDTO createReqVO) {
        Long activityId = prmSpRuleService.insertPrmSpRule(createReqVO);
        materialCache.remove(MaterialCacheVO.getActivityCacheKey(activityId));
        return success(activityId);
    }

    /**
     * 编辑特价促销活动
     */
    @ApiOperation(value = "编辑特价促销活动", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "编辑特价促销活动", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> editSkRuleInfo(@Valid @RequestBody PrmSkRuleDTO updateReqVO) {
        prmSpRuleService.editSpRuleInfo(updateReqVO);
        // 刷新缓存
        if (Objects.nonNull(updateReqVO.getPrmStatus())) {
            activityService.reloadSupplierActivity(updateReqVO.getActivityId());

            materialCache.remove(MaterialCacheVO.getActivityCacheKey(updateReqVO.getActivityId()));
        }
        return success(true);
    }

    /**
     * 删除特价活动规则
     */
    @ApiOperation(value = "删除特价活动规则", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "特价活动规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{spRuleIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] spRuleIds) {
        prmSpRuleService.deletePrmSpRuleBySpRuleIds(spRuleIds);
        return success(true);
    }

    /**
     * 获取特价活动详细信息
     */
    @ApiOperation(value = "获取特价活动详细信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + PrmSkRuleController.Permissions.GET)
    @RequiresPermissions(PrmSkRuleController.Permissions.GET)
    @GetMapping(value = "getSpRuleInfo")
    public CommonResult<PrmActivityRespVO> getSpRuleInfo(@RequestParam("activityId") Long activityId) {
        return success(prmSpRuleService.getSpRuleInfo(activityId));
    }

    /**
     * 分页查询特价活动规则
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得特价活动规则分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmSpRuleRespVO>> getPage(@Valid PrmSpRulePageReqVO pageReqVO) {
        PageResult<PrmSpRule> pageResult = prmSpRuleService.getPrmSpRulePage(pageReqVO);
        return success(PrmSpRuleConvert.INSTANCE.convertPage(pageResult));
    }

    @ApiOperation(value = "导入特价商品", httpMethod = HttpMethod.POST,notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORT)
    @Log(title = "导入特价商品", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/importData")
    public CommonResult<Map<String, Object>> importData(MultipartFile file) throws Exception
    {
        ExcelUtil<PrmSpRuleImportExcel> util = new ExcelUtil<>(PrmSpRuleImportExcel.class);
        List<PrmSpRuleImportExcel> prmSpRuleList = util.importExcel(file.getInputStream(),0);
        // 调用服务层方法
        Map<String, Object> importResult = prmSpRuleService.importPrmSpRuleData(prmSpRuleList);
        CommonResult<Map<String, Object>> result = new CommonResult<>();
        result.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        result.setData(importResult);
        result.setMsg((String) importResult.get("message"));
        return result;
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载特价商品信息模板", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<PrmSpRuleImportExcel> util = new ExcelUtil<>(PrmSpRuleImportExcel.class);
        util.importTemplateExcel(response, "特价商品信息导入", StringUtils.EMPTY, null);
    }

    @ApiOperation(value = "特价黑白名单导入门店", httpMethod = HttpMethod.POST,notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORTBRANCH)
    @Log(title = "特价黑白名单导入门店", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORTBRANCH)
    @PostMapping("/importBranchData")
    public CommonResult<Map<String, Object>> importBranchData(MultipartFile file) throws Exception
    {
        ExcelUtil<PrmBranchImportExcel> util = new ExcelUtil<>(PrmBranchImportExcel.class);
        List<PrmBranchImportExcel> prmBranchList = util.importExcel(file.getInputStream(),0);
        // 调用服务层方法
        Map<String, Object> importResult = prmActivityCommonService.importPrmBranchsData(prmBranchList);
        CommonResult<Map<String, Object>> result = new CommonResult<>();
        result.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        result.setData(importResult);
        result.setMsg((String) importResult.get("message"));
        return result;
    }

    @PostMapping("/importBranchTemplate")
    @ApiOperation(value = "下载导入门店信息模板", httpMethod = HttpMethod.POST)
    public void importBranchTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<PrmBranchImportExcel> util = new ExcelUtil<>(PrmBranchImportExcel.class);
        util.importTemplateExcel(response, "导入门店信息导入", StringUtils.EMPTY, null);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:activity:sp-rule:add";
        /** 编辑 */
        public static final String EDIT = "promotion:activity:sp-rule:edit";
        /** 删除 */
        public static final String DELETE = "promotion:activity:sp-rule:remove";
        /** 列表 */
        public static final String LIST = "promotion:activity:sp-rule:list";
        /** 查询 */
        public static final String GET = "promotion:activity:sp-rule:query";
        /** 停用 */
        public static final String DISABLE = "promotion:activity:sp-rule:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:activity:sp-rule:enable";
         /** 导入 */
        public static final String IMPORT = "promotion:activity:sp-rule:import";
        /** 导入门店 */
        public static final String IMPORTBRANCH = "promotion:activity:sp-rule:importBranch";
    }
}
