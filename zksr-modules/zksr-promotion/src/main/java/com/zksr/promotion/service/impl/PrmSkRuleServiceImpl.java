package com.zksr.promotion.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.exception.ErrorCode;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.materialApply.dto.MaterialApplyDTO;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.activity.dto.SkRuleDTO;
import com.zksr.promotion.controller.activity.dto.SkOrSpRuleCheckDTO;
import com.zksr.promotion.controller.activity.vo.PrmActivityItemPageVo;
import com.zksr.promotion.controller.activity.vo.PrmActivityPageReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import com.zksr.promotion.controller.rule.dto.PrmSkRuleDTO;
import com.zksr.promotion.controller.rule.vo.*;
import com.zksr.promotion.convert.activity.PrmActivityConvert;
import com.zksr.promotion.domain.*;
import com.zksr.promotion.domain.excel.PrmSkRuleImportExcel;
import com.zksr.promotion.mapper.*;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPromotionCacheService;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.convert.rule.PrmSkRuleConvert;
import com.zksr.promotion.service.IPrmSkRuleService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.PRDT_MATERIAL_APPLY_TYPE_1;
import static com.zksr.product.constant.ProductConstant.PRDT_SHELF_STATUS_1;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 秒杀规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Service
public class PrmSkRuleServiceImpl implements IPrmSkRuleService {

    @Autowired
    private PrmSkRuleMapper prmSkRuleMapper;

    @Autowired
    private PrmSpRuleMapper prmSpRuleMapper;

    @Autowired
    private PrmActivityMapper prmActivityMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PrmActivityBranchScopeMapper prmActivityBranchScopeMapper;

    @Autowired
    private PrmActivityCityScopeMapper prmActivityCityScopeMapper;

    @Autowired
    private PrmActivityChannelScopeMapper prmActivityChannelScopeMapper;

    @Autowired
    private PrmActivitySupplierScopeMapper prmActivitySupplierScopeMapper;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private IPrmActivityService prmActivityService;

    @Autowired
    private IPrmActivityCommonService prmActivityCommonService;

    @Autowired
    private RedisActivityService redisActivityService;

    /**
     * 新增秒杀规则
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertPrmSkRule(PrmSkRuleDTO createReqVO) {
        createReqVO.setActivityId(null);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 设置全国还是本地活动
        createReqVO.setFuncScope(Objects.isNull(loginUser.getDcId()) ? NumberPool.INT_ONE : NumberPool.INT_TWO);
        //校验秒杀规则信息
        this.checkSkRuleInfo(PrmActivityConvert.INSTANCE.convert(createReqVO), createReqVO.getSkRuleList().stream().map(PrmSkRuleSaveReqVO::getSkuId).collect(Collectors.toList()));
        // 校验秒杀活动的限购参数
        this.checkSkRuleParam(createReqVO);
        if (createReqVO.getPrmStatus() == 1L) {
            createReqVO.setEffectMan(loginUser.getSysUser().getUserName());
            createReqVO.setEffectTime(new Date());
        }
        createReqVO.setSpuScope(0);
        createReqVO.setPrmNo(PrmNoEnum.SK.getType());
        createReqVO.setPrmSheetNo(PrmNoEnum.SK.getType() + redisService.getUniqueNumber());

        PrmActivity prmActivity = PrmActivityConvert.INSTANCE.convert(createReqVO);
        prmActivity.setCreateBy(loginUser.getSysUser().getUserName());
        prmActivityMapper.insert(prmActivity);

        //如果是指定渠道 则保存活动渠道信息
        if (ToolUtil.isNotEmpty(createReqVO.getChanelScopeAllFlag()) && createReqVO.getChanelScopeAllFlag() == 1) {
            createReqVO.getChannelIds().forEach(channelId -> {
                PrmActivityChannelScope prmActivityChannelScope = new PrmActivityChannelScope();
                prmActivityChannelScope.setActivityId(prmActivity.getActivityId());
                prmActivityChannelScope.setChannelId(Long.parseLong(channelId.getId()));
                prmActivityChannelScope.setWhiteOrBlack(channelId.getType());
                prmActivityChannelScopeMapper.insert(prmActivityChannelScope);
            });
        }

        //如果是指定门店 则保存活动门店信息
        if (ToolUtil.isNotEmpty(createReqVO.getBranchScopeAllFlag()) && createReqVO.getBranchScopeAllFlag() == 1) {
            createReqVO.getBranchIds().forEach(branchId -> {
                PrmActivityBranchScope prmActivityBranchScope = new PrmActivityBranchScope();
                prmActivityBranchScope.setActivityId(prmActivity.getActivityId());
                prmActivityBranchScope.setBranchId(Long.parseLong(branchId.getId()));
                prmActivityBranchScope.setWhiteOrBlack(branchId.getType());
                prmActivityBranchScopeMapper.insert(prmActivityBranchScope);
            });
        }

        //如果是指定城市 则保存活动城市信息
        if (ToolUtil.isNotEmpty(createReqVO.getAreaIds())) {
            createReqVO.getAreaIds().forEach(cityId -> {
                PrmActivityCityScope prmActivityCityScope = new PrmActivityCityScope();
                prmActivityCityScope.setActivityId(prmActivity.getActivityId());
                prmActivityCityScope.setAreaId(Long.parseLong(cityId.getId()));
                prmActivityCityScope.setWhiteOrBlack(cityId.getType());
                prmActivityCityScopeMapper.insert(prmActivityCityScope);
            });
        }

        // 保存活动入驻商信息
        if (ToolUtil.isNotEmpty(createReqVO.getSupplierIds())) {
            createReqVO.getSupplierIds().forEach(supplierId -> {
                PrmActivitySupplierScope prmActivitySupplierScope = new PrmActivitySupplierScope();
                prmActivitySupplierScope.setActivityId(prmActivity.getActivityId());
                prmActivitySupplierScope.setSupplierId(supplierId);
                prmActivitySupplierScopeMapper.insert(prmActivitySupplierScope);
            });
        }

        createReqVO.getSkRuleList().forEach(skRule -> {
            skRule.setSysCode(prmActivity.getSysCode());
            skRule.setActivityId(prmActivity.getActivityId());
            // 插入
            PrmSkRule prmSkRule = PrmSkRuleConvert.INSTANCE.convert(skRule);
            prmSkRule.setSkRuleId(null);
            prmSkRuleMapper.insert(prmSkRule);
        });

        //新增素材信息
        if (ToolUtil.isNotEmpty(createReqVO.getMaterialId())) {
            prmActivityCommonService.addMaterialApply(MaterialApplyDTO.builder()
                    .materialId(createReqVO.getMaterialId())
                    .applyId(prmActivity.getActivityId())
                    .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                    .startTime(createReqVO.getStartTime())
                    .endTime(createReqVO.getEndTime()).build());

        }

        // 刷新缓存
        prmActivityService.reloadSupplierActivity(prmActivity.getActivityId());
        // 返回
        return prmActivity.getActivityId();
    }

    @Override
    @Transactional
    public Boolean editSkRuleInfo(PrmSkRuleDTO updateReqVO) {
        //停用不需要校验  直接停用即可
        if (ToolUtil.isEmpty(updateReqVO.getAreaIds()) && updateReqVO.getPrmStatus() == 2) {
            PrmActivity prmActivityReq = PrmActivityConvert.INSTANCE.convert(updateReqVO);
            prmActivityMapper.updateById(prmActivityReq);
        } else {
            // 校验秒杀活动的限购参数
            this.checkSkRuleParam(updateReqVO);
            PrmActivity prmActivity = prmActivityMapper.selectById(updateReqVO.getActivityId());
            updateReqVO.setFuncScope(prmActivity.getFuncScope());

            if (updateReqVO.getPrmStatus() == 1L && ToolUtil.isEmpty(prmActivity.getEffectTime())) {
                updateReqVO.setEffectMan(SecurityUtils.getLoginUser().getSysUser().getUserName());
                updateReqVO.setEffectTime(new Date());
            }
            PrmActivity prmActivityReq = PrmActivityConvert.INSTANCE.convert(updateReqVO);
            prmActivityMapper.updateById(prmActivityReq);

            //如果是指定渠道 则保存活动渠道信息
            if (ToolUtil.isNotEmpty(updateReqVO.getChannelIds())) {
                if (updateReqVO.getChanelScopeAllFlag() == 1) {
                    prmActivityChannelScopeMapper.deleteChannelByActivityId(updateReqVO.getActivityId());
                    updateReqVO.getChannelIds().forEach(channelId -> {
                        PrmActivityChannelScope prmActivityChannelScope = new PrmActivityChannelScope();
                        prmActivityChannelScope.setActivityId(prmActivity.getActivityId());
                        prmActivityChannelScope.setChannelId(Long.parseLong(channelId.getId()));
                        prmActivityChannelScope.setWhiteOrBlack(channelId.getType());
                        prmActivityChannelScopeMapper.insert(prmActivityChannelScope);
                    });
                }
            }


            if (ToolUtil.isNotEmpty(updateReqVO.getBranchIds())) {
                //如果是指定门店 则保存活动门店信息
                if (updateReqVO.getBranchScopeAllFlag() == 1) {
                    prmActivityBranchScopeMapper.deleteBranchByActivityId(updateReqVO.getActivityId());
                    updateReqVO.getBranchIds().forEach(branchId -> {
                        PrmActivityBranchScope prmActivityBranchScope = new PrmActivityBranchScope();
                        prmActivityBranchScope.setActivityId(prmActivity.getActivityId());
                        prmActivityBranchScope.setBranchId(Long.parseLong(branchId.getId()));
                        prmActivityBranchScope.setWhiteOrBlack(branchId.getType());
                        prmActivityBranchScopeMapper.insert(prmActivityBranchScope);
                    });
                }
            }

            if (ToolUtil.isNotEmpty(updateReqVO.getAreaIds())) {
                prmActivityCityScopeMapper.deleteCityByActivityId(updateReqVO.getActivityId());
                //如果是指定城市 则保存活动城市信息
                updateReqVO.getAreaIds().forEach(cityId -> {
                    PrmActivityCityScope prmActivityCityScope = new PrmActivityCityScope();
                    prmActivityCityScope.setActivityId(prmActivity.getActivityId());
                    prmActivityCityScope.setAreaId(Long.parseLong(cityId.getId()));
                    prmActivityCityScope.setWhiteOrBlack(cityId.getType());
                    prmActivityCityScopeMapper.insert(prmActivityCityScope);
                });
            }

            if (ToolUtil.isNotEmpty(updateReqVO.getSupplierIds())) {
                prmActivitySupplierScopeMapper.deleteSupplierByActivityId(updateReqVO.getActivityId());
                //保存活动入驻商信息
                updateReqVO.getSupplierIds().forEach(supplierId -> {
                    PrmActivitySupplierScope prmActivitySupplierScope = new PrmActivitySupplierScope();
                    prmActivitySupplierScope.setActivityId(prmActivity.getActivityId());
                    prmActivitySupplierScope.setSupplierId(supplierId);
                    prmActivitySupplierScopeMapper.insert(prmActivitySupplierScope);
                });
            }

            List<PrmSkRule> prmSkRuleList =prmSkRuleMapper.selectList(prmActivity.getActivityId());

            if (ToolUtil.isNotEmpty(updateReqVO.getSkRuleList())) {

                List<Long> skRuleIdsToKeep = updateReqVO.getSkRuleList().stream()
                        .map(PrmSkRuleSaveReqVO::getSkRuleId)
                        .collect(Collectors.toList());

                List<PrmSkRule> skRulesToDelete = prmSkRuleList.stream()
                        .filter(prmSkRule -> !skRuleIdsToKeep.contains(prmSkRule.getSkRuleId()))
                        .collect(Collectors.toList());

                skRulesToDelete.forEach(prmSkRule -> prmSkRuleMapper.deleteById(prmSkRule.getSkRuleId()));


                updateReqVO.getSkRuleList().forEach(skRule -> {
                    skRule.setActivityId(prmActivity.getActivityId());
                    saveOrUpdateSkRule(skRule);
                });
            }

            //修改素材信息
            if(ToolUtil.isNotEmpty(updateReqVO.getMaterialId())){
                prmActivityCommonService.editMaterialApply(MaterialApplyDTO.builder()
                        .materialId(updateReqVO.getMaterialId())
                        .applyId(updateReqVO.getActivityId())
                        .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                        .startTime(prmActivity.getStartTime())
                        .endTime(prmActivity.getEndTime())
                        .newStartTime(updateReqVO.getStartTime())
                        .newEndTime(updateReqVO.getEndTime())
                        .build());

            }
        }

        return true;
    }

    private void saveOrUpdateSkRule(PrmSkRuleSaveReqVO skRule) {
        PrmSkRule prmSkRule = PrmSkRuleConvert.INSTANCE.convert(skRule);
        if (ToolUtil.isNotEmpty(skRule.getSkRuleId())) {
            prmSkRuleMapper.updateById(prmSkRule);
        } else {
            prmSkRuleMapper.insert(prmSkRule);
        }
    }

    /**
     * 删除秒杀规则
     *
     * @param skRuleId 秒杀规则id
     */
    @Override
    public void deletePrmSkRule(Long skRuleId) {
        // 删除
        prmSkRuleMapper.deleteById(skRuleId);
    }

    /**
     * 批量删除秒杀规则
     *
     * @param skRuleIds 需要删除的秒杀规则主键
     * @return 结果
     */
    @Override
    public void deletePrmSkRuleBySkRuleIds(Long[] skRuleIds) {
        for (Long skRuleId : skRuleIds) {
            this.deletePrmSkRule(skRuleId);
        }
    }

    /**
     * 获得秒杀规则
     *
     * @param skRuleId 秒杀规则id
     * @return 秒杀规则
     */
    @Override
    public PrmSkRule getPrmSkRule(Long skRuleId) {


        return prmSkRuleMapper.selectById(skRuleId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrmSkRule> getPrmSkRulePage(PrmSkRulePageReqVO pageReqVO) {
        return prmSkRuleMapper.selectPage(pageReqVO);
    }

    @Override
    public PrmActivityRespVO getSkRuleInfo(Long activityId) {
        PrmActivity prmActivity = prmActivityMapper.selectById(activityId);
        PrmActivityRespVO respVO = HutoolBeanUtils.toBean(prmActivity, PrmActivityRespVO.class);
        if (respVO != null) {
            PrmSkRulePageReqVO reqVO = new PrmSkRulePageReqVO();
            reqVO.setActivityId(activityId);
            List<PrmSkRule> list = prmSkRuleMapper.selectList(reqVO);
            List<PrmSkRuleRespVO> skRuleList = HutoolBeanUtils.toBean(list, PrmSkRuleRespVO.class);
            skRuleList.forEach(skRule -> {
                SpuDTO spuDTO = promotionCacheService.getSpuDTO(skRule.getSpuId());
                SkuDTO skuDTO = promotionCacheService.getSkuDTO(skRule.getSkuId());
                SupplierDTO supplierDTO = promotionCacheService.getSupplierDTO(spuDTO.getSupplierId());
                skRule.setSpuName(spuDTO.getSpuName());
                skRule.setUnit(skuDTO.getUnit());
                skRule.setSupplierName(supplierDTO.getSupplierName());
                skRule.setBarcode(skuDTO.getBarcode());
                skRule.setCostPrice(skuDTO.getCostPrice());
                skRule.setMidCostPrice(skuDTO.getMidCostPrice());
                skRule.setLargeCostPrice(skuDTO.getLargeCostPrice());
                skRule.setMarkPrice(skuDTO.getMarkPrice());
                skRule.setMidMarkPrice(skuDTO.getMidMarkPrice());
                skRule.setLargeMarkPrice(skuDTO.getLargeMarkPrice());
                skRule.setStock(redisStockService.getSurplusSaleQty(skuDTO.getSkuId()).intValue());
                skRule.setSoldNum(redisStockService.getSkuSaledQty(skuDTO.getSkuId()).intValue());
                skRule.setMidSize(spuDTO.getMidSize());
                skRule.setLargeSize(spuDTO.getLargeSize());
                skRule.setMinUnit(spuDTO.getMinUnit());
                skRule.setMidUnit(spuDTO.getMidUnit());
                skRule.setLargeUnit(spuDTO.getLargeUnit());
                skRule.setTotalSaleNum(redisActivityService.getSkSaleNum(activityId, skRule.getSkRuleId()).intValue());
                skRule.setMidTotalSaleNum(redisActivityService.getSkMidSaleNum(activityId, skRule.getSkRuleId()).intValue());
                skRule.setLargeTotalSaleNum(redisActivityService.getSkLargeSaleNum(activityId, skRule.getSkRuleId()).intValue());
            });
            respVO.setSkRuleList(skRuleList);
            List<PrmActivityBranchScope> branchIds = prmActivityBranchScopeMapper.getBranchIdsByActivityId(activityId);
            if (ToolUtil.isNotEmpty(branchIds)) {
                //组装门店数据
                respVO.setBranchIds(
                        branchIds.stream().map(branch -> {
                            PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                            prmActivityWhiteOrBlackVO.setId(branch.getBranchId().toString());
                            prmActivityWhiteOrBlackVO.setType(branch.getWhiteOrBlack());
                            return prmActivityWhiteOrBlackVO;
                        }).collect(Collectors.toList())
                );
            }
            List<PrmActivityChannelScope> channelIds = prmActivityChannelScopeMapper.getChannelByActivityId(activityId);
            if (ToolUtil.isNotEmpty(channelIds)) {
                //组装渠道数据
                respVO.setChannelIds(
                        channelIds.stream().map(channel -> {
                            PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                            prmActivityWhiteOrBlackVO.setId(channel.getChannelId().toString());
                            prmActivityWhiteOrBlackVO.setType(channel.getWhiteOrBlack());
                            return prmActivityWhiteOrBlackVO;
                        }).collect(Collectors.toList()
                        ));
            }
            List<PrmActivityCityScope> cityIds = prmActivityCityScopeMapper.getAreaIdsByActivityId(activityId);
            if (ToolUtil.isNotEmpty(cityIds)) {
                respVO.setAreaIds(
                        cityIds.stream().map(city -> {
                            PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                            prmActivityWhiteOrBlackVO.setId(city.getAreaId().toString());
                            prmActivityWhiteOrBlackVO.setType(city.getWhiteOrBlack());
                            return prmActivityWhiteOrBlackVO;
                        }).collect(Collectors.toList())
                );
            }
            // 查询入驻商数据
            List<Long> supplierIds = prmActivitySupplierScopeMapper.getSupplierByActivityId(activityId);
            if(CollectionUtils.isNotEmpty(supplierIds)){
                List<String> stringSupplierIds = supplierIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());
                respVO.setSupplierIds(stringSupplierIds);
            }



        }

        //填充素材信息
        respVO.setMaterialApplyVO(prmActivityCommonService.getByMaterialApplyByMaterial(
                MaterialApplyVO.builder()
                        .applyId(prmActivity.getActivityId())
                        .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                        .startTime(prmActivity.getStartTime())
                        .endTime(prmActivity.getEndTime()).build()));

        return respVO;
    }


    public void checkSkRuleInfo(PrmActivity prmActivity, List<Long> skuIds) {
        // 重复冲突的活动
        List<SkOrSpRuleCheckDTO> repeatActivity = prmSkRuleMapper.selectRepeatActivity(prmActivity, skuIds);
        if (!repeatActivity.isEmpty()) {
            throw exception(new ErrorCode(1_016_005_002, "温馨提示:同一个商品同时间只能参与一个秒杀/特价活动,该秒杀活动与" + StringUtils.join(repeatActivity.get(0).getActivityName(), StringPool.COMMA) + "特价活动存在相同商品"));
        }
    }

    /**
     * 校验秒杀活动的限购参数
     * @param prmSkRuleDTO
     */
    private void checkSkRuleParam(PrmSkRuleDTO prmSkRuleDTO) {
        List<PrmSkRuleSaveReqVO> skRuleList = prmSkRuleDTO.getSkRuleList();
        skRuleList.forEach(skRule -> {
            if (skRule.getSeckillPrice() == null && skRule.getOnceLimit() != null) {
                throw new ServiceException("小单位促销价格为空，不能设置小单位门店限量");
            }
            if (skRule.getMidSeckillPrice() == null && skRule.getMidLimit() != null) {
                throw new ServiceException("中单位促销价格为空，不能设置中单位门店限量");
            }
            if (skRule.getLargeSeckillPrice() == null && skRule.getLargeLimit() != null) {
                throw new ServiceException("大单位促销价格为空，不能设置大单位门店限量");
            }
        });
    }

    /**
     * 秒杀商品导入
     *
     * @param prmSkRuleList
     * @return
     */
    @Override
    public Map<String, Object> importPrmSkRuleData(List<PrmSkRuleImportExcel> prmSkRuleList) {
        if (prmSkRuleList.isEmpty()) {
            exception(SECKILL_IMPORT_ERROR);
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder failureMsg = new StringBuilder();
        List<PrmSkRuleItemExcelRespVO> skRuleList = new ArrayList<>(); // 成功的数据列表

        // 遍历导入数据
        for (int line = 0; line < prmSkRuleList.size(); line++) {
            if (failureMsg.length() > 2000) { // 限制错误信息长度
                break;
            }
            int cellNumber = line + 1; // 当前行号
            PrmSkRuleImportExcel itemData = prmSkRuleList.get(line);

            try {
                // 校验数据
                validatePrmSkRuleData(itemData, cellNumber);
                // 获取 PrmActivityItemPageVo 对象
                PrmActivityItemPageVo pageReqVO = new PrmActivityItemPageVo();
                SkuDTO skuDTO = promotionCacheService.getSkuDTO(itemData.getSkuId());
                pageReqVO.setSpuId(skuDTO.getSpuId());
                pageReqVO.setSkuId(itemData.getSkuId());
                pageReqVO.setShelfStatus(PRDT_SHELF_STATUS_1);
                pageReqVO.setSysCode(SecurityUtils.getLoginUser().getSysCode());
                pageReqVO.setSupplierId(itemData.getSupplierId());
                PrmActivityItemPageVo prmActivityItemPageVo = null;
                try {
                    prmActivityItemPageVo = prmActivityService.getItemPage(pageReqVO).getList().get(0);
                } catch (Exception e) {
                    throw new ServiceException(String.format("未查询到商品上架数据,请确认是否为上架商品!"));
                }
                // 数据转换并加入成功列表
                PrmSkRuleItemExcelRespVO skRuleItemExcelRespVO = PrmSkRuleConvert.INSTANCE.convert(prmActivityItemPageVo);
                PrmSkRuleItemExcelRespVO updatedSkRuleItemExcelRespVO = PrmSkRuleConvert.INSTANCE.updateFromItemData(itemData, skRuleItemExcelRespVO);
                skRuleList.add(updatedSkRuleItemExcelRespVO);
                successNum++;
            } catch (Exception e) {
                // 捕获异常并记录失败信息
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据导入失败，错误信息：{}", cellNumber, e.getMessage()));
            }
        }

        // 构造返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("skRuleList", skRuleList); // 成功导入的数据列表

        if (failureNum > 0) {
            // 如果存在失败的数据，拼接失败信息
            String resultMessage = String.format(
                    "共导入 %d 条，成功 %d 条，失败 %d 条。失败原因如下：%s",
                    prmSkRuleList.size(), successNum, failureNum, failureMsg
            );
            resultMap.put("message", resultMessage);
        } else {
            // 如果全部成功
            String successMessage = String.format("恭喜您，数据已全部导入成功！共 %d 条", successNum);
            resultMap.put("message", successMessage);
        }

        return resultMap;
    }

    /**
     * 校验单行导入数据的规则
     *
     * @param itemData   导入数据对象
     * @param cellNumber 当前行号
     */
    private void validatePrmSkRuleData(PrmSkRuleImportExcel itemData, int cellNumber) {
        // 数据校验逻辑
        if (itemData.getSeckillStock() != null && itemData.getSeckillStock() < 0) {
            throw new ServiceException(String.format("第%d行：秒杀库存不能为负数", cellNumber));
        }
        if (itemData.getSeckillStock() != null && itemData.getStock() != null
                && itemData.getSeckillStock() > itemData.getStock()) {
            throw new ServiceException(String.format("第%d行：秒杀库存不能大于库存", cellNumber));
        }
        if (itemData.getOnceLimit() != null && itemData.getSeckillStock() != null
                && itemData.getOnceLimit() > itemData.getSeckillStock()) {
            throw new ServiceException(String.format("第%d行：门店限量不能大于秒杀库存", cellNumber));
        }
        if (itemData.getOnceLimit() != null && itemData.getOnceLimit() == 0) {
            throw new ServiceException(String.format("第%d行：门店限量不能为0", cellNumber));
        }
        if (itemData.getSeckillStock() != null && itemData.getSeckillStock() == 0) {
            throw new ServiceException(String.format("第%d行：秒杀库存不能为0", cellNumber));
        }

        // 校验价格
        if (itemData.getSeckillPrice() != null) {
            if (itemData.getSeckillPrice().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException(String.format("第%d行：秒杀价格小单位不能小于或等于0", cellNumber));
            }
            if (itemData.getMarkPrice() != null
                    && itemData.getSeckillPrice().compareTo(itemData.getMarkPrice()) > 0) {
                throw new ServiceException(String.format("第%d行：秒杀价格小单位不能大于销售价", cellNumber));
            }
        }
        if (itemData.getMidSeckillPrice() != null) {
            if (itemData.getMidSeckillPrice().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException(String.format("第%d行：秒杀价格中单位不能小于或等于0", cellNumber));
            }
            if (itemData.getMidMarkPrice() != null
                    && itemData.getMidSeckillPrice().compareTo(itemData.getMidMarkPrice()) > 0) {
                throw new ServiceException(String.format("第%d行：秒杀价格中单位不能大于销售价", cellNumber));
            }
        }
        if (itemData.getLargeSeckillPrice() != null) {
            if (itemData.getLargeSeckillPrice().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException(String.format("第%d行：秒杀价格大单位不能小于或等于0", cellNumber));
            }
            if (itemData.getLargeMarkPrice() != null
                    && itemData.getLargeSeckillPrice().compareTo(itemData.getLargeMarkPrice()) > 0) {
                throw new ServiceException(String.format("第%d行：秒杀价格大单位不能大于销售价", cellNumber));
            }
        }
    }

}
