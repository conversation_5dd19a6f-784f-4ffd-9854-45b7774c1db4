package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.controller.scope.vo.PrmCouponTemplateSupplierPageReqVO;
import com.zksr.promotion.domain.PrmCouponTemplateSupplier;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 优惠券模板入驻商
 */
@Mapper
public interface PrmCouponTemplateSupplierMapper extends BaseMapperX<PrmCouponTemplateSupplier> {

    default PageResult<PrmCouponTemplateSupplier> selectPage(PrmCouponTemplateSupplierPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmCouponTemplateSupplier>()
                .eqIfPresent(PrmCouponTemplateSupplier::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrmCouponTemplateSupplier::getCouponTemplateId, reqVO.getCouponTemplateId())
                .eqIfPresent(PrmCouponTemplateSupplier::getSupplierId, reqVO.getSupplierId())
                .orderByDesc(PrmCouponTemplateSupplier::getSysCode));
    }

    int deleteByCouponTemplateId(Long couponTemplateId);

    default List<Long> getSupplierByCouponTemplateId(Long couponTemplateId) {
        return selectList(new LambdaQueryWrapperX<PrmCouponTemplateSupplier>()
                .select(PrmCouponTemplateSupplier::getSupplierId)
                .eq(PrmCouponTemplateSupplier::getCouponTemplateId, couponTemplateId)
        ).stream().map(PrmCouponTemplateSupplier::getSupplierId).collect(Collectors.toList());

    }

    default List<PrmCouponTemplateSupplier> selectByCouponTemplateId(Long couponTemplateId) {
        return selectList(new LambdaQueryWrapper<PrmCouponTemplateSupplier>().eq(PrmCouponTemplateSupplier::getCouponTemplateId, couponTemplateId));
    }
}
