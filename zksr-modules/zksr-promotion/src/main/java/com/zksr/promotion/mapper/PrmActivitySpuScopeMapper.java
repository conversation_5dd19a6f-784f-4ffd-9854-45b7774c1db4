package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.controller.scope.vo.PrmActivitySpuScopePageReqVO;
import com.zksr.promotion.domain.PrmActivitySpuScope;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 促销活动spu适用范围Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Mapper
public interface PrmActivitySpuScopeMapper extends BaseMapperX<PrmActivitySpuScope> {
    default PageResult<PrmActivitySpuScope> selectPage(PrmActivitySpuScopePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmActivitySpuScope>()
                .eqIfPresent(PrmActivitySpuScope::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrmActivitySpuScope::getActivityId, reqVO.getActivityId())
                .eqIfPresent(PrmActivitySpuScope::getApplyId, reqVO.getApplyId())
                .eqIfPresent(PrmActivitySpuScope::getApplyType, reqVO.getApplyType())
                .orderByDesc(PrmActivitySpuScope::getSysCode));
    }

    /**
     * @Description: 通过促销ID删除绑定关系
     * @Author: liuxingyu
     * @Date: 2024/5/15 17:51
     */
    default Integer deleteByActivityId(Long activityId) {
        return delete(new LambdaUpdateWrapper<PrmActivitySpuScope>()
                .eq(PrmActivitySpuScope::getActivityId, activityId));
    }

    /**
     * @Description: 通过促销ID获取绑定关系
     * @Author: liuxingyu
     * @Date: 2024/5/16 11:11
     */
    default List<PrmActivitySpuScope> selectByActivityId(Long activityId) {
        return selectList(new LambdaQueryWrapperX<PrmActivitySpuScope>().eq(PrmActivitySpuScope::getActivityId, activityId));
    }

    /**
     * @Description: 通过出校ID集合获取绑定关系
     * @Author: liuxingyu
     * @Date: 2024/5/18 17:47
     */
    default List<PrmActivitySpuScope> getListByActivityIdList(Integer spuScope, List<Long> prmActivityIdList) {
        return selectList(new LambdaQueryWrapper<PrmActivitySpuScope>()
                .eq(PrmActivitySpuScope::getApplyType, spuScope)
                .in(PrmActivitySpuScope::getActivityId, prmActivityIdList));
    }
}
