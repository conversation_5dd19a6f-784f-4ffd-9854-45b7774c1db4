package com.zksr.promotion.mq;
import com.midea.mbf.mq.consumer.listener.AbstractRocketMQListener;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.rocketmq.constant.promotion.PromotionMqGroupConstants;
import com.zksr.common.rocketmq.constant.promotion.PromotionMqTagConstants;
import com.zksr.common.rocketmq.constant.promotion.PromotionMqTopicConstants;
import com.zksr.common.rocketmq.event.promotion.CouponReceiveEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * @Description: RFO创建驱动RFO提交
 * <AUTHOR>
 *
 */
@Slf4j
@Component
@RocketMQMessageListener(
        consumeThreadMax = 4,
        topic = PromotionMqTopicConstants.TOPIC_B2B_PRM_COUPON_RECEIVED,
        selectorExpression = PromotionMqTagConstants.TAG_B2B_PRM_COUPON_RECEIVED,
        consumerGroup = PromotionMqGroupConstants.GROUP_B2B_PRM_COUPON_RECEIVED
)
//@ConditionalOnProperty(name = "rocketmq.consumer.enable", havingValue = "true", matchIfMissing = true)
public class CouponReceive2SaveConsumer extends AbstractRocketMQListener<CouponReceiveEvent> {

    public void processMsg(CouponReceiveEvent event, MessageExt msg) {
        log.info(" CouponReceive2SaveConsumer消费, {},{}", event.getTransactionId(), JsonUtils.toJsonString(event));

    }
}
