package com.zksr.promotion.service.impl;

import com.midea.mbf.txmq.rocketmq.producer.RocketMQTxProducerImpl;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.SymbolConstants;
import com.zksr.promotion.service.MqCustomService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * @Author: chenyj8
 * @Desciption: 类功能描述
 */
@Service
@Slf4j
public class MqCustomServiceImpl implements MqCustomService {

//    @Autowired
//    private TxProducer txProducer;


    @Autowired
    private RocketMQTxProducerImpl rocketMQTxProducerImpl;

    //业务代码
    @Transactional
    public void executeLocalTransaction(String message) {
        //执行本地事务逻辑
        // ...
        //发送事务消息
//        Message<String> msg = MessageBuilder.withPayload(message)
//                .setHeader(RocketMQHeaders.TRANSACTION_ID, "transactionId1111")
//                .build();
//        String destination = "TOPIC-B2B-TEST";
//        txProducer.syncSend(destination, msg);

        // 执行本地事务逻辑 ...


        Message<String> msg1 = MessageBuilder.withPayload(message).setHeader(
                RocketMQHeaders.KEYS, "keytest-1").build();
        StringBuilder destination1 = new StringBuilder();
        destination1.append("TOPIC-B2B-TEST").append(SymbolConstants.COLON).append(
                "TAG-B2B-TEST");
        if (log.isInfoEnabled()) {
            log.info(" 发送成功事件, {}", JsonUtils.toJsonString(msg1));
        }

        rocketMQTxProducerImpl.syncSend(destination1.toString(), msg1);
    }
}