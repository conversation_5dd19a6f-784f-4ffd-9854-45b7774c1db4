package com.zksr.promotion.mapper;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmSpRule;
import com.zksr.promotion.controller.rule.vo.PrmSpRulePageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 特价活动规则Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Mapper
public interface PrmSpRuleMapper extends BaseMapperX<PrmSpRule> {
    default PageResult<PrmSpRule> selectPage(PrmSpRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmSpRule>()
                    .eqIfPresent(PrmSpRule::getSpRuleId, reqVO.getSpRuleId())
                    .eqIfPresent(PrmSpRule::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmSpRule::getActivityId, reqVO.getActivityId())
                    .eqIfPresent(PrmSpRule::getSpuId, reqVO.getSpuId())
                    .eqIfPresent(PrmSpRule::getSkuId, reqVO.getSkuId())
                    .eqIfPresent(PrmSpRule::getSpPrice, reqVO.getSpPrice())
                    .eqIfPresent(PrmSpRule::getOnceBuyLimit, reqVO.getOnceBuyLimit())
                    .eqIfPresent(PrmSpRule::getTotalLimitQty, reqVO.getTotalLimitQty())
                    .eqIfPresent(PrmSpRule::getStatus, reqVO.getStatus())
                .orderByDesc(PrmSpRule::getSpRuleId));
    }

    default List<PrmSpRule> selectList(PrmSpRulePageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrmSpRule>()
                .eqIfPresent(PrmSpRule::getSpRuleId, reqVO.getSpRuleId())
                .eqIfPresent(PrmSpRule::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrmSpRule::getActivityId, reqVO.getActivityId())
                .eqIfPresent(PrmSpRule::getSpuId, reqVO.getSpuId())
                .eqIfPresent(PrmSpRule::getSkuId, reqVO.getSkuId())
                .eqIfPresent(PrmSpRule::getSpPrice, reqVO.getSpPrice())
                .eqIfPresent(PrmSpRule::getOnceBuyLimit, reqVO.getOnceBuyLimit())
                .eqIfPresent(PrmSpRule::getTotalLimitQty, reqVO.getTotalLimitQty())
                .eqIfPresent(PrmSpRule::getStatus, reqVO.getStatus())
                .inIfPresent(PrmSpRule::getSkuId, reqVO.getSkuIds())
                .orderByDesc(PrmSpRule::getSpRuleId));
    }

    void deleteSpRuleByActivityId(@Param("activityId") Long activityId);


    default List<PrmSpRule> selectByActivityId(Long activityId) {
        return selectList(new LambdaQueryWrapperX<PrmSpRule>()
                .eqIfPresent(PrmSpRule::getActivityId, activityId)
                .eqIfPresent(PrmSpRule::getStatus, NumberPool.INT_ONE)
                .orderByDesc(PrmSpRule::getSpRuleId));
    }
}
