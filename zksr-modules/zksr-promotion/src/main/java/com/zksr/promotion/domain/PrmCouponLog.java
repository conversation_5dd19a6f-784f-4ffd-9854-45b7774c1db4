package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 优惠券日志对象 prm_coupon_log
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
@TableName(value = "prm_coupon_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmCouponLog extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 优惠券日志 */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponLogId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 优惠券id */
    @Excel(name = "优惠券id")
    private String couponId;

    /** 操作前状态 */
    @Excel(name = "操作前状态")
    private Integer beforeState;

    /** 操作后状态 */
    @Excel(name = "操作后状态")
    private Integer afterState;

    /** 操作类型(数据字典) */
    @Excel(name = "操作类型(数据字典)")
    @ApiModelProperty(value = "操作类型", notes = "0-使用,1-退回")
    private Integer operateType;

    /** 日志信息 */
    @Excel(name = "日志信息")
    private String content;

}
