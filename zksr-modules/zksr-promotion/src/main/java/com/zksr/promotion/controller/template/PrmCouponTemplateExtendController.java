package com.zksr.promotion.controller.template;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateExtendPageReqVO;
import com.zksr.promotion.controller.template.dto.PrmCouponTemplateReceiveRespVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateExtendRespVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateReceivePageVO;
import com.zksr.promotion.service.IPrmCouponTemplateExtendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 优惠券模版拓展统计Controller
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Api(tags = "管理后台 - 优惠券模版拓展统计接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/templateExtend")
public class PrmCouponTemplateExtendController {
    @Autowired
    private IPrmCouponTemplateExtendService prmCouponTemplateExtendService;

    /**
     * 分页查询优惠券模版拓展统计
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得优惠券模版拓展统计分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(supplierAlias = "`pct`", dcAlias = "`pct`", dcFieldAlias = SystemConstants.SUPPLIER_ID)
    public CommonResult<PageResult<PrmCouponTemplateExtendRespVO>> getPage(@Valid PrmCouponTemplateExtendPageReqVO pageReqVO) {
        if(ToolUtil.isNotEmpty(SecurityUtils.getLoginUser().getDcId())){
            pageReqVO.setFuncScope(2);
        }
        PageResult<PrmCouponTemplateExtendRespVO> pageResult = prmCouponTemplateExtendService.getPrmCouponTemplateExtendPage(pageReqVO);
        return success(pageResult);
    }


    /**
     * 分页查询优惠券模版门店领取列表
     */
    @GetMapping("/getCounponTemplateReceiveList")
    @ApiOperation(value = "分页查询优惠券模版门店领取列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
//    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmCouponTemplateReceiveRespVO>> getCounponTemplateReceiveListPage(@Valid PrmCouponTemplateReceivePageVO pageReqVO) {
        return success(prmCouponTemplateExtendService.getCounponTemplateReceiveListPage(pageReqVO));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:template-extend:add";
        /** 编辑 */
        public static final String EDIT = "promotion:template-extend:edit";
        /** 删除 */
        public static final String DELETE = "promotion:template-extend:remove";
        /** 列表 */
        public static final String LIST = "promotion:template-extend:list";
        /** 查询 */
        public static final String GET = "promotion:template-extend:query";
        /** 停用 */
        public static final String DISABLE = "promotion:template-extend:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:template-extend:enable";
    }
}
