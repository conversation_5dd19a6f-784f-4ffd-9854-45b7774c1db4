package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.IdType;
import com.zksr.common.core.enums.ActivitySpuScopeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;

import java.util.Date;

/**
 * 促销活动对象 prm_activity
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@TableName(value = "prm_activity")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmActivity extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 活动id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定） */
    @Excel(name = "全国或者本地(数据字典);1-全国商品可用", readConverterExp = "平=台商设定")
    private Integer funcScope;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 促销类型（数据字典）;SK-秒杀 FG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价 */
    @Excel(name = "促销类型", readConverterExp = "数=据字典")
    private String prmNo;

    /** 促销单号 */
    @Excel(name = "促销单号")
    private String prmSheetNo;

    /** 活动名称 */
    @Excel(name = "活动名称")
    private String activityName;

    /** 启用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "启用时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectTime;

    /** 启用人 */
    @Excel(name = "启用人")
    private String effectMan;

    /** 活动开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "活动开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 活动结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "活动结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 参见 {@link ActivitySpuScopeEnum}
     */
    /** 商品适用范围(数据字典);0-所有商品可用（全场） 2-指定品类可用（品类），3-指定品牌可用（品牌），4-指定商品可用（商品） */
    @Excel(name = "商品适用范围(数据字典);0-所有商品可用", readConverterExp = "全=场")
    private Integer spuScope;

    /** 是否指定渠道参与;0-所有渠道参与 1-指定渠道参与  （指定渠道和指定门店二选一） */
    @Excel(name = "是否指定渠道参与;0-所有渠道参与 1-指定渠道参与  ", readConverterExp = "指=定渠道和指定门店二选一")
    private Integer chanelScopeAllFlag;

    /** 是否指定门店参与;0-所有门店参与 1-指定门店参与   （指定渠道和指定门店二选一） */
    @Excel(name = "是否指定门店参与;0-所有门店参与 1-指定门店参与   ", readConverterExp = "指=定渠道和指定门店二选一")
    private Integer branchScopeAllFlag;

    /** 是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减) */
    @Excel(name = "是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减)")
    private Integer ladderFlag;

    /** 参与活动次数限制规则（数据字典）;仅特价限购，0-每日一次 1-仅一次  2-仅活动期间内首单 3-系统首单 */
    @Excel(name = "参与活动次数限制规则", readConverterExp = "数=据字典")
    private Integer timesRule;

    /** 活动优惠类型;仅满减，0-金额  1-数量 */
    @Excel(name = "活动优惠类型;仅满减，0-金额  1-数量")
    private Integer amtOrQty;

    /** 促销状态;0-未启用 1-启用 2-停用 3-已失效（待定，过期或者无商品可参与促销） */
    @Excel(name = "促销状态;0-未启用 1-启用 2-停用 3-已失效", readConverterExp = "待=定，过期或者无商品可参与促销")
    private Integer prmStatus;

    /** 促销活动单位 */
    @Excel(name = "促销活动单位", readConverterExp = "大中小")
    private Integer activityUnitType;

    /** 活动说明 */
    @Excel(name = "活动说明")
    private String memo;

    /** 秒杀/特价, 限定参与SKU数 */
    @Excel(name = "秒杀/特价, 限定参与SKU数")
    private Integer limitSkus;

}
