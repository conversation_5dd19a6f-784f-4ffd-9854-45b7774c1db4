package com.zksr.promotion.convert.rule;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.BgRuleDTO;
import com.zksr.promotion.api.activity.dto.FgRuleDTO;
import com.zksr.promotion.domain.PrmBgRule;
import com.zksr.promotion.controller.rule.vo.PrmBgRuleRespVO;
import com.zksr.promotion.controller.rule.vo.PrmBgRuleSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 买赠条件规则 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-13
*/
@Mapper
public interface PrmBgRuleConvert {

    PrmBgRuleConvert INSTANCE = Mappers.getMapper(PrmBgRuleConvert.class);

    PrmBgRuleRespVO convert(PrmBgRule prmBgRule);

    PrmBgRule convert(PrmBgRuleSaveReqVO prmBgRuleSaveReq);

    PageResult<PrmBgRuleRespVO> convertPage(PageResult<PrmBgRule> prmBgRulePage);

    List<BgRuleDTO> convertListDTO(List<PrmBgRule> bgRules);

}