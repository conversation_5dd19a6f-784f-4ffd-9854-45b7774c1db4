package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.domain.PrmCouponTemplateRepeatRule;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateRepeatRulePageReqVO;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateRepeatRuleSaveReqVO;

/**
 * 优惠券模板-重复规则Service接口
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
public interface IPrmCouponTemplateRepeatRuleService {

    /**
     * 新增优惠券模板-重复规则
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmCouponTemplateRepeatRule(@Valid PrmCouponTemplateRepeatRuleSaveReqVO createReqVO);

    /**
     * 修改优惠券模板-重复规则
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmCouponTemplateRepeatRule(@Valid PrmCouponTemplateRepeatRuleSaveReqVO updateReqVO);

    /**
     * 删除优惠券模板-重复规则
     *
     * @param couponTemplateRepeatRuleId 优惠券模板重复规则
     */
    public void deletePrmCouponTemplateRepeatRule(Long couponTemplateRepeatRuleId);

    /**
     * 批量删除优惠券模板-重复规则
     *
     * @param couponTemplateRepeatRuleIds 需要删除的优惠券模板-重复规则主键集合
     * @return 结果
     */
    public void deletePrmCouponTemplateRepeatRuleByCouponTemplateRepeatRuleIds(Long[] couponTemplateRepeatRuleIds);

    /**
     * 获得优惠券模板-重复规则
     *
     * @param couponTemplateRepeatRuleId 优惠券模板重复规则
     * @return 优惠券模板-重复规则
     */
    public PrmCouponTemplateRepeatRule getPrmCouponTemplateRepeatRule(Long couponTemplateRepeatRuleId);

    /**
     * 获得优惠券模板-重复规则分页
     *
     * @param pageReqVO 分页查询
     * @return 优惠券模板-重复规则分页
     */
    PageResult<PrmCouponTemplateRepeatRule> getPrmCouponTemplateRepeatRulePage(PrmCouponTemplateRepeatRulePageReqVO pageReqVO);

    /**
     * 获取优惠券重复模版
     * @param couponTemplateId  优惠券模版ID
     * @return
     */
    PrmCouponTemplateRepeatRule getPrmCouponTemplateRepeatRuleByTemplateId(Long couponTemplateId);

}
