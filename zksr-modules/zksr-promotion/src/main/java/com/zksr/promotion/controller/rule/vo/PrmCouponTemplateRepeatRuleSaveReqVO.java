package com.zksr.promotion.controller.rule.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 优惠券模板-重复规则对象 prm_coupon_template_repeat_rule
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Data
@ApiModel("优惠券模板-重复规则 - prm_coupon_template_repeat_rule分页 Request VO")
public class PrmCouponTemplateRepeatRuleSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 优惠券模板重复规则 */
    @ApiModelProperty(value = "总重复次数ID", hidden = true)
    private Long couponTemplateRepeatRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", hidden = true)
    private Long sysCode;

    /** 优惠券模板id */
    @Excel(name = "优惠券模板id")
    @ApiModelProperty(value = "优惠券模板id", required = true, hidden = true)
    private Long couponTemplateId;

    /** 重复类型 */
    @Excel(name = "重复类型")
    @ApiModelProperty(value = "重复类型(数据字典);repeat_flag=重复时设定 1-每天(每天可再次领取),2-每周(每周一可再次领取),3-每月(每月1号可再次领取),4-自定义")
    private Integer repeatType;

    /** 重复频率单位 */
    @Excel(name = "重复频率单位")
    @ApiModelProperty(value = "重复频率单位(数据字典);repeat_type=自定义时设定 0-天(每过几天可以再次领取)，1-周(每周的周几可以再次领取)，2-月(每个月的第几天可以再次领取)，3-年 (每年的几月可以重复领取)")
    private Integer repeatFrequencyUnit;

    /** 重复周期*/
    @Excel(name = "重复周期")
    @ApiModelProperty(value = "重复周期(数据字典);repeat_type=自定义时设定 0：重复频率单位为天时，可选范围为0~99，即表示每几天重复；1：重复频率单位为周时，可选范围为1-7；2：重复频率单位为月时，可选范围为1-31, 3: 每年的第几个月可以领取, 可选范围为1-12")
    private String repeatFrequencyPeriod;

    /** 总重复次数 */
    @Excel(name = "总重复次数")
    @ApiModelProperty(value = "总重复次数", hidden = true)
    private Integer repaeatTimes;

}
