package com.zksr.promotion.controller.apply.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 优惠券关联的使用范围, 领取范围中间对象 prm_coupon_scope_apply
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Data
@ApiModel("优惠券关联的使用范围, 领取范围中间 - prm_coupon_scope_apply Response VO")
public class PrmCouponScopeApplyRespVO {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "优惠券模版ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long scopeApplyId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty(value = "${comment}")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 优惠券模版ID */
    @Excel(name = "优惠券模版ID")
    @ApiModelProperty(value = "优惠券模版ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

}
