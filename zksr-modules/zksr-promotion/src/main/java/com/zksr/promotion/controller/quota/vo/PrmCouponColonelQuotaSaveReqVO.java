package com.zksr.promotion.controller.quota.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 业务员发券额度对象 prm_coupon_colonel_quota
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
@ApiModel("业务员发券额度 - prm_coupon_colonel_quota分页 Request VO")
public class PrmCouponColonelQuotaSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 业务员发券额度ID */
    @ApiModelProperty(value = "已发额度")
    private Integer couponColonelQuotaId;

    /** 平台商ID */
    @ApiModelProperty(value = "已发额度")
    private Long sysCode;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    /** 0-模板额度 1-实例额度 */
    @ApiModelProperty(value = "业务员ID")
    private Integer quotaType;

    /** 本月发券额度 */
    @Excel(name = "本月发券额度")
    @ApiModelProperty(value = "本月发券额度")
    private BigDecimal quota;

    /** 已发额度 */
    @Excel(name = "已发额度")
    @ApiModelProperty(value = "已发额度")
    private BigDecimal finishQuota;

    /** 月份ID，仅1-实例额度 */
    @ApiModelProperty(value = "已发额度")
    private Integer monthId;

}
