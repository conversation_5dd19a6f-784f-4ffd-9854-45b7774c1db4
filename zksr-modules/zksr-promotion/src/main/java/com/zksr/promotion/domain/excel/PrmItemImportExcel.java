package com.zksr.promotion.domain.excel;

import com.zksr.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PrmItemImportExcel {
    @Excel(name = "产品编号")
    private String spuNo; // 产品编号

    @Excel(name = "SKUid")
    private Long skuId; // SKUid

    @Excel(name = "产品名称")
    private String spuName; // 产品名称

    @Excel(name = "条码")
    private String barcode; // 条码

    @Excel(name = "入驻商Id")
    private Long supplierId; // 入驻商Id

    @Excel(name = "小单位标准价")
    private BigDecimal markPrice; // 小单位标准价

    @Excel(name = "中单位标准价")
    private BigDecimal midMarkPrice; // 中单位标准价

    @Excel(name = "大单位标准价")
    private BigDecimal largeMarkPrice; // 大单位标准价

    @Excel(name = "库存")
    private Integer stock; // 库存
}
