package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.domain.PrmActivityBranchScope;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.controller.scope.vo.PrmActivityCityScopePageReqVO;
import com.zksr.promotion.domain.PrmActivityCityScope;
import com.zksr.promotion.controller.scope.vo.PrmActivityCityScopePageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 促销活动城市适用范围Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Mapper
public interface PrmActivityCityScopeMapper extends BaseMapperX<PrmActivityCityScope> {
    default PageResult<PrmActivityCityScope> selectPage(PrmActivityCityScopePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmActivityCityScope>()
                .eqIfPresent(PrmActivityCityScope::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrmActivityCityScope::getActivityId, reqVO.getActivityId())
                .eqIfPresent(PrmActivityCityScope::getAreaId, reqVO.getAreaId())
                .orderByDesc(PrmActivityCityScope::getSysCode));
    }

    /**
     * @Description: 通过促销ID删除绑定关系
     * @Author: liuxingyu
     * @Date: 2024/5/16 9:43
     */
    default Integer deleteByActivityId(Long activityId) {
        return delete(new LambdaUpdateWrapper<PrmActivityCityScope>()
                .eq(PrmActivityCityScope::getActivityId, activityId));
    }

    /**
     * @Description: 通过促销ID获取绑定关系
     * @Author: liuxingyu
     * @Date: 2024/5/16 12:15
     */
    default List<PrmActivityCityScope> selectByActivityId(Long activityId) {
        return selectList(new LambdaQueryWrapper<PrmActivityCityScope>().eq(PrmActivityCityScope::getActivityId, activityId));
    }

    default List<PrmActivityCityScope> getAreaIdsByActivityId(Long activityId) {
        List<PrmActivityCityScope> list = selectList(
                new LambdaQueryWrapperX<PrmActivityCityScope>()
                        .select(PrmActivityCityScope::getAreaId, PrmActivityCityScope::getWhiteOrBlack)
                        .eq(PrmActivityCityScope::getActivityId, activityId)
        );

        return list;
    }

    void deleteCityByActivityId(@Param("activityId") Long activityId);
}
