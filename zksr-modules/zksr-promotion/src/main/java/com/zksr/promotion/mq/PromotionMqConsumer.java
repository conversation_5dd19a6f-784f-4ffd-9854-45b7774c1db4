package com.zksr.promotion.mq;

import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.promotion.api.coupon.CouponApiImpl;
import com.zksr.promotion.api.coupon.dto.CouponReceiveDTO;
import com.zksr.promotion.domain.PrmCouponTemplate;
import com.zksr.promotion.service.IPrmCouponService;
import com.zksr.promotion.service.IPrmCouponTemplateService;
import com.zksr.trade.api.car.dto.AppCarEventDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.support.MessageBuilder;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/4/1 10:26
 */
@Configuration
@Slf4j
public class PromotionMqConsumer {

    @Autowired
    private IPrmCouponTemplateService couponTemplateService;

    @Autowired
    private IPrmCouponService couponService;

    /**
     * 消息来源 {@link PromotionMqProducer#sendCouponRefreshEvent(PrmCouponTemplate)}
     * @return
     */
    @Bean
    public Consumer<PrmCouponTemplate> couponTemplateCache() {
        return (data) -> {
            log.info("收到优惠券更新缓存事件：{} ", data);
            couponTemplateService.refreshCache(data);
        };
    }


    /**
     * 消息来源 {@link CouponApiImpl#receiveCoupon(CouponReceiveDTO)}
     *  couponReceive-in-0:
     *           destination: couponReceive
     *           consumer:
     *             concurrency: 5
     *             使用了5个线程处理高流量
     * @return
     */
    @Bean
    public Consumer<CouponReceiveDTO> couponReceive() {
        return (data) -> {
            log.info("收到领取优惠券：{} ", data);
            couponService.saveNormalCouponReceive(data);
        };
    }
}
