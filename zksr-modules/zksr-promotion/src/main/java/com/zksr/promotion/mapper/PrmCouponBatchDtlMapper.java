package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCouponBatchDtl;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchDtlPageReqVO;

import java.util.List;


/**
 * 优惠券批量发送详情Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Mapper
public interface PrmCouponBatchDtlMapper extends BaseMapperX<PrmCouponBatchDtl> {
    default PageResult<PrmCouponBatchDtl> selectPage(PrmCouponBatchDtlPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmCouponBatchDtl>()
                    .eqIfPresent(PrmCouponBatchDtl::getPrmCouponBatchDtlId, reqVO.getPrmCouponBatchDtlId())
                    .eqIfPresent(PrmCouponBatchDtl::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmCouponBatchDtl::getBatchCouponId, reqVO.getBatchCouponId())
                    .eqIfPresent(PrmCouponBatchDtl::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(PrmCouponBatchDtl::getCouponTemplateId, reqVO.getCouponTemplateId())
                    .eqIfPresent(PrmCouponBatchDtl::getScopeType, reqVO.getScopeType())
                .orderByDesc(PrmCouponBatchDtl::getPrmCouponBatchDtlId));
    }


    /**
     * 根据batchCouponId和scopType获取优惠券批量发送详情数据集合
     *
     * @param batchCouponId 优惠券批量发送id scopeType 类型
     * @return 结果
     */
    default List<PrmCouponBatchDtl> selectByBatchCouponIdAndScopeType(Long batchCouponId, Integer scopeType) {
        return selectList(new LambdaQueryWrapperX<PrmCouponBatchDtl>()
                .eq(PrmCouponBatchDtl::getBatchCouponId, batchCouponId)
                .eqIfPresent(PrmCouponBatchDtl::getScopeType, scopeType)
        );
    }

     /**
     * 根据批量发券ID删除优惠券批量发送详情数据
     *
     */
     default int deleteByBatchCouponId(Long batchCouponId) {
         return delete(new LambdaQueryWrapperX<PrmCouponBatchDtl>()
                 .eq(PrmCouponBatchDtl::getBatchCouponId, batchCouponId)
         );
     }


    /**
     * 根据批量发券ID和门店ID删除优惠券批量发送详情数据
     */
    default int deleteByBatchCouponIdAndBranchId(Long batchCouponId, Long branchId) {
        LambdaQueryWrapper<PrmCouponBatchDtl> queryWrapper = new LambdaQueryWrapper<PrmCouponBatchDtl>()
                .eq(PrmCouponBatchDtl::getBatchCouponId, batchCouponId)
                .eq(PrmCouponBatchDtl::getScopeType, 1);

        // 如果门店ID不为null，则添加筛选条件
        if (branchId != null) {
            queryWrapper.eq(PrmCouponBatchDtl::getBranchId, branchId);
        }

        return delete(queryWrapper);
    }

}
