package com.zksr.promotion.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.controller.template.dto.PrmCouponTemplateReceiveRespVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateExtendRespVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateReceivePageVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCouponTemplateExtend;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateExtendPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 优惠券模版拓展统计Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Mapper
public interface PrmCouponTemplateExtendMapper extends BaseMapperX<PrmCouponTemplateExtend> {
    default PageResult<PrmCouponTemplateExtend> selectPage(PrmCouponTemplateExtendPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmCouponTemplateExtend>()
                    .eqIfPresent(PrmCouponTemplateExtend::getCouponTemplateId, reqVO.getCouponTemplateId())
                    .eqIfPresent(PrmCouponTemplateExtend::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmCouponTemplateExtend::getUsedCount, reqVO.getUsedCount())
                    .eqIfPresent(PrmCouponTemplateExtend::getRecordCount, reqVO.getRecordCount())
                    .eqIfPresent(PrmCouponTemplateExtend::getTotalSaleAmt, reqVO.getTotalSaleAmt())
                    .eqIfPresent(PrmCouponTemplateExtend::getTotalCouponAmt, reqVO.getTotalCouponAmt())
                .orderByDesc(PrmCouponTemplateExtend::getCouponTemplateId));
    }

    List<PrmCouponTemplateExtendRespVO> selectPageExt(PrmCouponTemplateExtendPageReqVO pageReqVO);

    List<PrmCouponTemplateExtend> selectCouponExtendTotal(@Param("couponTemplateIdList") List<Long> couponTemplateIdList);

    /**
     * 优惠券模版客户领取列表 分页查询
     * @param pageReqVO
     * @return
     */
    List<PrmCouponTemplateReceiveRespVO> selectCouponExtendReceivePage(PrmCouponTemplateReceivePageVO pageReqVO);

}
