package com.zksr.promotion.controller.couponBatch;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.domain.PrmCouponBatchDtl;
import com.zksr.promotion.service.IPrmCouponBatchDtlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchDtlPageReqVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchDtlSaveReqVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchDtlRespVO;
import com.zksr.promotion.convert.couponBatch.PrmCouponBatchDtlConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 优惠券批量发送详情Controller
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Api(tags = "管理后台 - 优惠券批量发送详情接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/couponBatchDtl")
public class PrmCouponBatchDtlController {
    @Autowired
    private IPrmCouponBatchDtlService prmCouponBatchDtlService;

    /**
     * 新增优惠券批量发送详情
     */
    @ApiOperation(value = "新增优惠券批量发送详情", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "优惠券批量发送详情", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmCouponBatchDtlSaveReqVO createReqVO) {
        return success(prmCouponBatchDtlService.insertPrmCouponBatchDtl(createReqVO));
    }

    /**
     * 修改优惠券批量发送详情
     */
    @ApiOperation(value = "修改优惠券批量发送详情", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "优惠券批量发送详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmCouponBatchDtlSaveReqVO updateReqVO) {
            prmCouponBatchDtlService.updatePrmCouponBatchDtl(updateReqVO);
        return success(true);
    }

    /**
     * 删除优惠券批量发送详情
     */
    @ApiOperation(value = "删除优惠券批量发送详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "优惠券批量发送详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{prmCouponBatchDtlIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] prmCouponBatchDtlIds) {
        prmCouponBatchDtlService.deletePrmCouponBatchDtlByPrmCouponBatchDtlIds(prmCouponBatchDtlIds);
        return success(true);
    }

    /**
     * 获取优惠券批量发送详情详细信息
     */
    @ApiOperation(value = "获得优惠券批量发送详情详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{prmCouponBatchDtlId}")
    public CommonResult<PrmCouponBatchDtlRespVO> getInfo(@PathVariable("prmCouponBatchDtlId") Long prmCouponBatchDtlId) {
        PrmCouponBatchDtl prmCouponBatchDtl = prmCouponBatchDtlService.getPrmCouponBatchDtl(prmCouponBatchDtlId);
        return success(PrmCouponBatchDtlConvert.INSTANCE.convert(prmCouponBatchDtl));
    }

    /**
     * 分页查询优惠券批量发送详情
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得优惠券批量发送详情分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmCouponBatchDtlRespVO>> getPage(@Valid PrmCouponBatchDtlPageReqVO pageReqVO) {
        PageResult<PrmCouponBatchDtl> pageResult = prmCouponBatchDtlService.getPrmCouponBatchDtlPage(pageReqVO);
        return success(PrmCouponBatchDtlConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "couponBatch:couponBatch:add";
        /** 编辑 */
        public static final String EDIT = "couponBatch:couponBatch:edit";
        /** 删除 */
        public static final String DELETE = "couponBatch:couponBatch:remove";
        /** 列表 */
        public static final String LIST = "couponBatch:couponBatch:list";
        /** 查询 */
        public static final String GET = "couponBatch:couponBatch:query";
        /** 停用 */
        public static final String DISABLE = "couponBatch:couponBatch:disable";
        /** 启用 */
        public static final String ENABLE = "couponBatch:couponBatch:enable";
    }
}
