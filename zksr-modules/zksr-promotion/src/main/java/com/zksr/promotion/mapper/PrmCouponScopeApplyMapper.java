package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.domain.PrmCouponTemplate;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCouponScopeApply;
import com.zksr.promotion.controller.apply.vo.PrmCouponScopeApplyPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 优惠券关联的使用范围, 领取范围中间Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Mapper
public interface PrmCouponScopeApplyMapper extends BaseMapperX<PrmCouponScopeApply> {
    default PageResult<PrmCouponScopeApply> selectPage(PrmCouponScopeApplyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmCouponScopeApply>()
                    .eqIfPresent(PrmCouponScopeApply::getScopeApplyId, reqVO.getScopeApplyId())
                    .eqIfPresent(PrmCouponScopeApply::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmCouponScopeApply::getCouponTemplateId, reqVO.getCouponTemplateId())
                .orderByDesc(PrmCouponScopeApply::getScopeApplyId));
    }

    /**
     * 获取优惠券具体领取范围
     * @param couponTemplateId
     * @return
     */
    default List<PrmCouponScopeApply> selectByCouponTemplateIdByReceiveScope(Long couponTemplateId) {
        return selectList(
                Wrappers.lambdaQuery(PrmCouponScopeApply.class)
                        .eq(PrmCouponScopeApply::getCouponTemplateId, couponTemplateId)
                        .isNotNull(PrmCouponScopeApply::getReceiveScope)
        );
    }

    /**
     * 删除优惠券绑定的领取使用范围
     * @param couponTemplateId
     */
    default void deleteByCouponTemplateId(Long couponTemplateId) {
        delete(
                Wrappers.lambdaQuery(PrmCouponScopeApply.class)
                        .eq(PrmCouponScopeApply::getCouponTemplateId, couponTemplateId)
        );
    }

    List<PrmCouponTemplate> selectPrmCouponScopeApplyValidBySpuScope(@Param("applyId") Long applyId, @Param("spuScope") Integer spuScope);

    default List<PrmCouponScopeApply> selectPrmCouponScopeApplyByCouponTemplateIdAndSpuScope(Long couponTemplateId, Integer spuScope) {
        return selectList(
                Wrappers.lambdaQuery(PrmCouponScopeApply.class)
                        .eq(PrmCouponScopeApply::getCouponTemplateId, couponTemplateId)
                        .eq(PrmCouponScopeApply::getSpuScope, spuScope)
        );
    }

    default List<PrmCouponScopeApply> selectCouponSpuScopeApplyByCouponTemplateId(Long couponTemplateId) {
        return selectList(
                Wrappers.lambdaQuery(PrmCouponScopeApply.class)
                        .eq(PrmCouponScopeApply::getCouponTemplateId, couponTemplateId)
                        .isNotNull(PrmCouponScopeApply::getSpuScope)
        );
    }

    default List<PrmCouponScopeApply> selectCouponReceiveScopeApplyByCouponTemplateId(Long couponTemplateId) {
        return selectList(
                Wrappers.lambdaQuery(PrmCouponScopeApply.class)
                        .eq(PrmCouponScopeApply::getCouponTemplateId, couponTemplateId)
                        .isNotNull(PrmCouponScopeApply::getReceiveScope)
        );
    }

}
