package com.zksr.promotion.convert.scope;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import com.zksr.promotion.domain.PrmActivitySpuScope;
import com.zksr.promotion.controller.scope.vo.PrmActivitySpuScopeRespVO;
import com.zksr.promotion.controller.scope.vo.PrmActivitySpuScopeSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 促销活动spu适用范围 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-13
*/
@Mapper
public interface PrmActivitySpuScopeConvert {

    PrmActivitySpuScopeConvert INSTANCE = Mappers.getMapper(PrmActivitySpuScopeConvert.class);

    PrmActivitySpuScopeRespVO convert(PrmActivitySpuScope prmActivitySpuScope);

    PrmActivitySpuScope convert(PrmActivitySpuScopeSaveReqVO prmActivitySpuScopeSaveReq);

    PageResult<PrmActivitySpuScopeRespVO> convertPage(PageResult<PrmActivitySpuScope> prmActivitySpuScopePage);

    List<ActivitySpuScopeDTO> convertListDTO(List<PrmActivitySpuScope> prmActivitySpuScopes);
}