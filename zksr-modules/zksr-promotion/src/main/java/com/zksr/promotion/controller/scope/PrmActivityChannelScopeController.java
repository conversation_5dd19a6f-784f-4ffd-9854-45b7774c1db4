package com.zksr.promotion.controller.scope;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.domain.PrmActivityChannelScope;
import com.zksr.promotion.service.IPrmActivityChannelScopeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.promotion.controller.scope.vo.PrmActivityChannelScopePageReqVO;
import com.zksr.promotion.controller.scope.vo.PrmActivityChannelScopeSaveReqVO;
import com.zksr.promotion.controller.scope.vo.PrmActivityChannelScopeRespVO;
import com.zksr.promotion.convert.scope.PrmActivityChannelScopeConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 促销活动渠道适用范围Controller
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Api(tags = "管理后台 - 促销活动渠道适用范围接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/channelScope")
public class PrmActivityChannelScopeController {
    @Autowired
    private IPrmActivityChannelScopeService prmActivityChannelScopeService;

    /**
     * 新增促销活动渠道适用范围
     */
    @ApiOperation(value = "新增促销活动渠道适用范围", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "促销活动渠道适用范围", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrmActivityChannelScopeSaveReqVO createReqVO) {
        return success(prmActivityChannelScopeService.insertPrmActivityChannelScope(createReqVO));
    }

    /**
     * 修改促销活动渠道适用范围
     */
    @ApiOperation(value = "修改促销活动渠道适用范围", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "促销活动渠道适用范围", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmActivityChannelScopeSaveReqVO updateReqVO) {
            prmActivityChannelScopeService.updatePrmActivityChannelScope(updateReqVO);
        return success(true);
    }

    /**
     * 删除促销活动渠道适用范围
     */
    @ApiOperation(value = "删除促销活动渠道适用范围", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "促销活动渠道适用范围", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sysCodes}")
    public CommonResult<Boolean> remove(@PathVariable Long[] sysCodes) {
        prmActivityChannelScopeService.deletePrmActivityChannelScopeBySysCodes(sysCodes);
        return success(true);
    }

    /**
     * 获取促销活动渠道适用范围详细信息
     */
    @ApiOperation(value = "获得促销活动渠道适用范围详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{sysCode}")
    public CommonResult<PrmActivityChannelScopeRespVO> getInfo(@PathVariable("sysCode") Long sysCode) {
        PrmActivityChannelScope prmActivityChannelScope = prmActivityChannelScopeService.getPrmActivityChannelScope(sysCode);
        return success(PrmActivityChannelScopeConvert.INSTANCE.convert(prmActivityChannelScope));
    }

    /**
     * 分页查询促销活动渠道适用范围
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得促销活动渠道适用范围分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmActivityChannelScopeRespVO>> getPage(@Valid PrmActivityChannelScopePageReqVO pageReqVO) {
        PageResult<PrmActivityChannelScope> pageResult = prmActivityChannelScopeService.getPrmActivityChannelScopePage(pageReqVO);
        return success(PrmActivityChannelScopeConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:activity:channel-scope:add";
        /** 编辑 */
        public static final String EDIT = "promotion:activity:channel-scope:edit";
        /** 删除 */
        public static final String DELETE = "promotion:activity:channel-scope:remove";
        /** 列表 */
        public static final String LIST = "promotion:activity:channel-scope:list";
        /** 查询 */
        public static final String GET = "promotion:activity:channel-scope:query";
        /** 停用 */
        public static final String DISABLE = "promotion:activity:channel-scope:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:activity:channel-scope:enable";
    }
}
