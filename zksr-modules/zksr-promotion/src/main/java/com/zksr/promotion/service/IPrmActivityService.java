package com.zksr.promotion.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import com.zksr.promotion.api.activity.dto.SkRuleDTO;
import com.zksr.promotion.api.activity.dto.SpRuleDTO;
import com.zksr.promotion.api.activity.dto.SupplierActivityDTO;
import com.zksr.promotion.controller.activity.dto.PrmActivityReportRespDTO;
import com.zksr.promotion.controller.activity.dto.SkOrSpRuleCheckDTO;
import com.zksr.promotion.controller.activity.vo.*;
import com.zksr.promotion.controller.rule.dto.PrmFdRuleDTO;
import com.zksr.promotion.domain.PrmActivity;
import org.apache.ibatis.annotations.Param;

import javax.validation.Valid;
import java.util.List;

/**
 * 促销活动Service接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
public interface IPrmActivityService {

    /**
     * 新增促销活动
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmActivity(@Valid PrmActivitySaveReqVO createReqVO);

    /**
     * 修改促销活动
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmActivity(@Valid PrmActivitySaveReqVO updateReqVO);

    /**
     * 删除促销活动
     *
     * @param activityId 活动id
     */
    public void deletePrmActivity(Long activityId);

    /**
     * 批量删除促销活动
     *
     * @param activityIds 需要删除的促销活动主键集合
     * @return 结果
     */
    public void deletePrmActivityByActivityIds(Long[] activityIds);

    /**
     * 获得促销活动
     *
     * @param activityId 活动id
     * @return 促销活动
     */
    public PrmActivity getPrmActivity(Long activityId);

    /**
     * 获得促销活动分页
     *
     * @param pageReqVO 分页查询
     * @return 促销活动分页
     */
    PageResult<PrmActivityRespVO> getPrmActivityPage(PrmActivityPageReqVO pageReqVO);

    /**
     * 获取入驻商活动
     *
     * @param supplierId
     * @return
     */
    List<SupplierActivityDTO> getSupplierActivity(Long supplierId);

    /**
     * 分页查询新增促销活动的商品信息
     * @param pageReqVO
     * @return
     */
    PageResult<PrmActivityItemPageVo> getItemPage(PrmActivityItemPageVo pageReqVO);

    /**
     * 获取特价活动规则
     * @param activityId
     * @return
     */
    List<SpRuleDTO> getActivitySpRule(Long activityId);

    /**
     * 获取秒杀活动规则
     * @param activityId
     * @return
     */
    List<SkRuleDTO> getActivitySkRule(Long activityId);

    /**
     *
     * @param activityId
     * @return
     */
    List<ActivitySpuScopeDTO> getActivitySpuScopeList(Long activityId);

    /**
     *
     * @param activityId
     * @return
     */
    List<ActivityBranchScopeDTO> getActivityBranchScopeList(Long activityId);

    /**
     *
     * @param activityId
     * @return
     */
    List<ActivityCityScopeDTO> getActivityCityScopeList(Long activityId);

    /**
     *
     * @param activityId
     * @return
     */
    List<ActivityChannelScopeDTO> getActivityChannelScopeList(Long activityId);

    /**
     * 校验活动是否可以停用
     * @param isEnable
     */
    void checkEnable(PrmFdRuleDTO isEnable);

    /**
     * 获取活动信息
     * @param activityId 活动ID
     * @return
     */
    PrmActivityDTO getActivityDto(Long activityId);

    /**
     * 获取满减促销规则
     * @param activityId 活动ID
     * @return
     */
    List<FdRuleDTO> getActivityFdRule(Long activityId);

    /**
     * 获取满赠促销规则
     * @param activityId 活动ID
     * @return
     */
    List<FgRuleDTO> getActivityFgRule(Long activityId);

    /**
     * 获取买赠促销规则
     * @param activityId 活动ID
     * @return
     */
    List<BgRuleDTO> getActivityBgRule(Long activityId);

    /**
     * 获取组合商品促销规则
     * @param activityId 活动ID
     * @return
     */
    List<CbRuleDTO> getActivityCbRule(Long activityId);

    /**
     * 刷新入驻商促销缓存
     * @param activityId    促销活动ID
     */
    void reloadSupplierActivity(Long activityId);

    /**
     *分页查询促销活动使用报表
     * @param pageVO
     * @return
     */
    PageResult<PrmActivityReportRespDTO> getActivityReportPage(PrmActivityReportPageVO pageVO);

    /**
     * 根据促销活动方案查询商品信息
     * @param prmNo
     * @return
     */
    List<Long> getSeckillSpuIds(String prmNo, Integer funcScope);

    /**
     * 根据促销活动方案查询 SKUID
     * @param prmNo
     * @return
     */
    List<Long> getSeckillSkuIds(String prmNo, Integer funcScope);


    /**
     * 获取 sku 绑定的进行中或者未开始的活动
     * @param skuId skuId
     * @return  活动role总数
     */
    Long countBySkuId(Long skuId);

    /**
     * 校验全国/城市 商品下架商品校验是否参与促销
     *
     * @param skuIds skuId
     * @return 校验信息
     */
    String checkActivityByItem(Long[] skuIds);

    /**
     * 获取城市, 有在搞组合促销的入驻商集合
     * @param areaId    城市ID
     * @return  入驻商ID集合
     */
    List<Long> getAreaCbSupplierIds(Long areaId);

    /**
     * 获取城市, 有在搞组合促销的入驻商集合
     * @param sysCode    平台ID
     * @return  入驻商ID集合
     */
    List<Long> getGlobalCbSupplierIds(Long sysCode);


    /**
     * 根据平台商和活动状态获取组合促销活动集合
     * @param sysCode    平台ID
     * @return  活动ID集合
     */
    List<Long> getActivityCbByStatus(Long sysCode,Integer funcScope, Integer status);


    /**
     * 获取活动时间已过期的组合促销ID集合
     * @return  活动ID集合
     */
    List<Long> getExpiredCbActivityIds();


    PageResult<PrmActivityRespVO> listActivity(PrmActivityPageReqVO pageReqVO);

    /**
     * 根据获取的活动校验是否和当前启用的特价或者秒杀活动冲突
     * @return  校验信息
     */
    SkOrSpRuleCheckDTO selectRepeatActivity(PrmActivitySaveReqVO activity);

    List<ActivitySupplierScopeDTO> getActivitySupplierScopeList(Long activityId);
}
