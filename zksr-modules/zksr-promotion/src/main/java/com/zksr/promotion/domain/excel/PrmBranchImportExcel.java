package com.zksr.promotion.domain.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

@Data
public class PrmBranchImportExcel {
    /** 门店ID */
    @Excel(name = "门店ID", headerColor = IndexedColors.RED)
    @ApiModelProperty(value = "门店ID")
    private String branchId;

    @Excel(name = "门店名称", headerColor = IndexedColors.RED)
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @Excel(name = "联系地址", headerColor = IndexedColors.RED)
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;
}
