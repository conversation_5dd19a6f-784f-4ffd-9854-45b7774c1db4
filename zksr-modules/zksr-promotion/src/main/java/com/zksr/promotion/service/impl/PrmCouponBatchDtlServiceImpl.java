package com.zksr.promotion.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmCouponBatchDtlMapper;
import com.zksr.promotion.convert.couponBatch.PrmCouponBatchDtlConvert;
import com.zksr.promotion.domain.PrmCouponBatchDtl;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchDtlPageReqVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchDtlSaveReqVO;
import com.zksr.promotion.service.IPrmCouponBatchDtlService;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 优惠券批量发送详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class PrmCouponBatchDtlServiceImpl implements IPrmCouponBatchDtlService {
    @Autowired
    private PrmCouponBatchDtlMapper prmCouponBatchDtlMapper;

    /**
     * 新增优惠券批量发送详情
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrmCouponBatchDtl(PrmCouponBatchDtlSaveReqVO createReqVO) {
        // 插入
        PrmCouponBatchDtl prmCouponBatchDtl = PrmCouponBatchDtlConvert.INSTANCE.convert(createReqVO);
        prmCouponBatchDtlMapper.insert(prmCouponBatchDtl);
        // 返回
        return prmCouponBatchDtl.getPrmCouponBatchDtlId();
    }

    /**
     * 修改优惠券批量发送详情
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmCouponBatchDtl(PrmCouponBatchDtlSaveReqVO updateReqVO) {
        prmCouponBatchDtlMapper.updateById(PrmCouponBatchDtlConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除优惠券批量发送详情
     *
     * @param prmCouponBatchDtlId 优惠券批量发送详情id
     */
    @Override
    public void deletePrmCouponBatchDtl(Long prmCouponBatchDtlId) {
        // 删除
        prmCouponBatchDtlMapper.deleteById(prmCouponBatchDtlId);
    }

    /**
     * 批量删除优惠券批量发送详情
     *
     * @param prmCouponBatchDtlIds 需要删除的优惠券批量发送详情主键
     * @return 结果
     */
    @Override
    public void deletePrmCouponBatchDtlByPrmCouponBatchDtlIds(Long[] prmCouponBatchDtlIds) {
        for(Long prmCouponBatchDtlId : prmCouponBatchDtlIds){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deletePrmCouponBatchDtl(prmCouponBatchDtlId);
        }
    }

    /**
     * 获得优惠券批量发送详情
     *
     * @param prmCouponBatchDtlId 优惠券批量发送详情id
     * @return 优惠券批量发送详情
     */
    @Override
    public PrmCouponBatchDtl getPrmCouponBatchDtl(Long prmCouponBatchDtlId) {
        return prmCouponBatchDtlMapper.selectById(prmCouponBatchDtlId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmCouponBatchDtl> getPrmCouponBatchDtlPage(PrmCouponBatchDtlPageReqVO pageReqVO) {
        return prmCouponBatchDtlMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PrmCouponBatchDtl> selectByBatchCouponIdAndScopeType(Long batchCouponId, Integer scopeType) {
        return prmCouponBatchDtlMapper.selectByBatchCouponIdAndScopeType(batchCouponId, scopeType);
    }

    @Override
    public void deleteByBatchCouponIdAndBranchId(Long batchCouponId, Long branchId) {
        prmCouponBatchDtlMapper.deleteByBatchCouponIdAndBranchId(batchCouponId, branchId);
    }

/*    private void validatePrmCouponBatchDtlExists(Long prmCouponBatchDtlId) {
        if (prmCouponBatchDtlMapper.selectById(prmCouponBatchDtlId) == null) {
            throw exception(PRM_COUPON_BATCH_DTL_NOT_EXISTS);
        }
    }*/

    // TODO 待办：请将下面的错误码复制到 com.zksr.promotion.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 优惠券批量发送详情 TODO 补充编号 ==========
    // ErrorCode PRM_COUPON_BATCH_DTL_NOT_EXISTS = new ErrorCode(TODO 补充编号, "优惠券批量发送详情不存在");


}
