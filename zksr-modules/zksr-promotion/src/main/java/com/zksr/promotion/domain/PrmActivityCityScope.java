package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;

/**
 * 促销活动城市适用范围对象 prm_activity_city_scope
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@TableName(value = "prm_activity_city_scope")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmActivityCityScope{
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 门店id */
    @Excel(name = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    @Excel(name = "1-白名单 0-黑名单")
    private Integer whiteOrBlack;

}
