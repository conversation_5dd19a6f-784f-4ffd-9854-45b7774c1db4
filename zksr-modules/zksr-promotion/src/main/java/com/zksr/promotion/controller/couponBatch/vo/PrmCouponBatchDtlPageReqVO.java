package com.zksr.promotion.controller.couponBatch.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 优惠券批量发送详情对象 prm_coupon_batch_dtl
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@ApiModel("优惠券批量发送详情 - prm_coupon_batch_dtl分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmCouponBatchDtlPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 优惠券批量发送详情id */
    @ApiModelProperty(value = "0-优惠券模板 1-门店")
    private Long prmCouponBatchDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 优惠券批量发送id */
    @Excel(name = "优惠券批量发送id")
    @ApiModelProperty(value = "优惠券批量发送id", required = true)
    private Long batchCouponId;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 优惠券模板 */
    @Excel(name = "优惠券模板")
    @ApiModelProperty(value = "优惠券模板")
    private Long couponTemplateId;

    /** 0-优惠券模板 1-门店 */
    @Excel(name = "0-优惠券模板 1-门店")
    @ApiModelProperty(value = "0-优惠券模板 1-门店")
    private Integer scopeType;


}
