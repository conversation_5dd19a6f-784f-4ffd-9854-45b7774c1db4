package com.zksr.promotion.controller.couponBatch.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/1/21 14:48
 * @注释
 */
@Data
@ApiModel("优惠券批量发送门店信息 - prm_coupon_batch Response VO")
@AllArgsConstructor
@NoArgsConstructor
public class CouponBatchBranchRespVO {

    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "门店编号")
    private String branchNo;

    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称", required = true)
    private String branchName;

    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址", required = true)
    private String branchAddress;

    @Excel(name = "门店领取优惠券的数量;统计信息")
    @ApiModelProperty(value = "门店领取优惠券的数量;统计信息")
    private Integer receiveCount;

    /**
     * 城市id
     */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /**
     * 业务员id
     */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /**
     * 渠道id
     */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long channelId;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @Excel(name = "渠道名称")
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @Excel(name = "城市名称")
    @ApiModelProperty(value = "城市名称")
    private String areaName;

    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;
}
