package com.zksr.promotion.service.impl;

import com.zksr.promotion.domain.PrmCouponTemplateSupplier;
import com.zksr.promotion.mapper.PrmCouponTemplateSupplierMapper;
import com.zksr.promotion.service.IPrmCouponTemplateSupplierService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class PrmCouponTemplateSupplierServiceImpl implements IPrmCouponTemplateSupplierService {

    @Autowired
    private PrmCouponTemplateSupplierMapper mapper;

    @Override
    public Boolean insertBatch(List<PrmCouponTemplateSupplier> templateSupplierList) {
        return this.mapper.insertBatch(templateSupplierList);
    }

    @Override
    public int deleteByCouponTemplateId(Long couponTemplateId) {
        return this.mapper.deleteByCouponTemplateId(couponTemplateId);
    }

    @Override
    public List<PrmCouponTemplateSupplier> selectByCouponTemplateId(Long couponTemplateId) {
        return this.mapper.selectByCouponTemplateId(couponTemplateId);
    }

}
