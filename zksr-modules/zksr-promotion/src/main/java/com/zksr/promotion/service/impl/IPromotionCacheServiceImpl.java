package com.zksr.promotion.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.brand.BrandApi;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.CatgoryApi;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.saleClass.SaleClassApi;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.api.coupon.dto.CouponReceiveScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.domain.*;
import com.zksr.promotion.mapper.*;
import com.zksr.promotion.service.IPrmCouponTemplateService;
import com.zksr.promotion.service.IPromotionCacheService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.ChannelApi;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销缓存模块
 * @date 2024/4/2 10:44
 */
@Service
public class IPromotionCacheServiceImpl implements IPromotionCacheService {

    @Autowired
    private Cache<Long, CouponTemplateDTO> couponTemplateCache;

    @Autowired
    private Cache<Long, BrandDTO> brandDtoCache;

    @Autowired
    @Lazy
    private IPrmCouponTemplateService prmCouponTemplateService;

    @Autowired
    private Cache<Long, AreaClassDTO> areaClassDtoCache;

    @Autowired
    private Cache<Long, SaleClassDTO> saleClassDtoCache;

    @Autowired
    private Cache<Long, SpuDTO> spuDTOCache;

    @Autowired
    private Cache<Long, SkuDTO> skuDTOCache;

    @Autowired
    private Cache<Long, SupplierDTO> supplierDTOCache;

    @Autowired
    private Cache<Long, BranchDTO> branchDTOCache;

    @Autowired
    private Cache<Long, List<ActivityBranchScopeDTO>> branchScopeDTOCache;

    @Autowired
    private Cache<Long, List<ActivityCityScopeDTO>> cityScopeDTOCache;

    @Autowired
    private Cache<Long, List<ActivityChannelScopeDTO>> channelScopeDTOCache;

    @Autowired
    private Cache<Long, List<ActivitySpuScopeDTO>> spuScopeDTOCache;

    @Autowired
    private Cache<Long, List<ActivitySupplierScopeDTO>> supplierScopeDTOCache;

    @Autowired
    private Cache<Long, CatgoryDTO> catgoryDTOCache;

    @Autowired
    private Cache<Long, ChannelDTO> channelDTOCache;

    @Autowired
    private Cache<Long, AreaDTO> areaDtoCache;

    @Autowired
    private Cache<Long, List<SkRuleDTO>> activitySkRuleCache;

    @Resource
    private BrandApi brandApi;

    @Autowired
    private ColonelApi colonelApi;

    @Resource
    private AreaClassApi areaClassApi;

    @Resource
    private SaleClassApi saleClassApi;

    @Resource
    private SpuApi spuApi;

    @Resource
    private SkuApi skuApi;

    @Resource
    private SupplierApi supplierApi;

    @Resource
    private BranchApi branchApi;

    @Resource
    private CatgoryApi catgoryApi;


    @Autowired
    private PrmActivityBranchScopeMapper branchScopeMapper;

    @Autowired
    private PrmActivityChannelScopeMapper channelScopeMapper;

    @Autowired
    private PrmActivityCityScopeMapper cityScopeMapper;

    @Autowired
    private PrmActivitySpuScopeMapper spuScopeMapper;

    @Autowired
    private PrmActivitySupplierScopeMapper prmActivitySupplierScopeMapper;

    @Autowired
    private PrmSkRuleMapper skRuleMapper;

    @Autowired
    private Cache<Long, ColonelDTO> colonelDTOCache;

    @Resource
    private ChannelApi channelApi;

    @Resource
    private AreaApi areaApi;

    @Autowired
    private Cache<Long, List<CouponReceiveScopeDTO>> couponReceiveScopeCache;

    @PostConstruct
    public void init() {
        //自动load（read through）
        couponTemplateCache.config().setLoader(prmCouponTemplateService::getPrmCouponTemplateCacheDTO);
        brandDtoCache.config().setLoader(this::loadBrandDtoFromApi);
        areaClassDtoCache.config().setLoader(this::loadAreaClassDtoFromApi);
        saleClassDtoCache.config().setLoader(this::loadSaleClassDtoFromApi);
        spuDTOCache.config().setLoader(this::loadSpuDtoFromApi);
        supplierDTOCache.config().setLoader(this::loadSupplierDTOFromApi);
        branchDTOCache.config().setLoader(this::loadBranchDTOFromApi);
        skuDTOCache.config().setLoader(this::loadSkuDtoFromApi);
        channelScopeDTOCache.config().setLoader(this::loadChannelScopeDTOFromMapper);
        cityScopeDTOCache.config().setLoader(this::loadCityScopeDTOFromMapper);
        branchScopeDTOCache.config().setLoader(this::loadBranchScopeDTOFromMapper);
        spuScopeDTOCache.config().setLoader(this::loadSpuScopeDTOFromMapper);
        catgoryDTOCache.config().setLoader(this::loadCategoryDTOFromApi);
        channelDTOCache.config().setLoader(this::loadChannelDTOFromApi);
        areaDtoCache.config().setLoader(this::loadAreaDtoFromApi);
        activitySkRuleCache.config().setLoader(this::loadSkRuleDTOFromMapper);
        supplierScopeDTOCache.config().setLoader(this::loadSupplierScopeDTOFromMapper);
        couponReceiveScopeCache.config().setLoader(this::loadCouponReceiveScopeCache);
    }

    /**
     * 加载优惠券黑白名单校验
     */
    private List<CouponReceiveScopeDTO> loadCouponReceiveScopeCache(Long couponTemplateId) {
        IPrmCouponTemplateService couponTemplateService = SpringUtils.getBean(PrmCouponTemplateServiceImpl.class);
        PrmCouponTemplate prmCouponTemplate = couponTemplateService.getPrmCouponTemplate(couponTemplateId);
        return couponTemplateService.getApplyReceiveScopeList(prmCouponTemplate);
    }

    /**
     * 加载当前活动入驻商列表
     */
    private List<ActivitySupplierScopeDTO> loadSupplierScopeDTOFromMapper(Long activityId) {
        List<PrmActivitySupplierScope> dataList = prmActivitySupplierScopeMapper.selectByActivityId(activityId);
        return ObjectUtil.isEmpty(dataList) ? null : HutoolBeanUtils.toBean(dataList, ActivitySupplierScopeDTO.class);
    }

    private List<SkRuleDTO> loadSkRuleDTOFromMapper(Long activityId) {
        List<PrmSkRule> skRuleDTOS = skRuleMapper.selectByActivityId(activityId);
        return ObjectUtil.isEmpty(skRuleDTOS) ? null : HutoolBeanUtils.toBean(skRuleDTOS, SkRuleDTO.class);
    }

    private AreaDTO loadAreaDtoFromApi(Long areaId) {
        return areaApi.getAreaByAreaId(areaId).getCheckedData();
    }



    private ChannelDTO loadChannelDTOFromApi(Long channelId) {
        return channelApi.getByChannelId(channelId).getCheckedData();
    }

    private CatgoryDTO loadCategoryDTOFromApi(Long categoryId) {
        return catgoryApi.getCatgoryByCatgoryId(categoryId).getCheckedData();
    }

    /**
     * @Description: 获取促销绑定spu列表
     * @Author: liuxingyu
     * @Date: 2024/5/16 12:10
     */
    private List<ActivitySpuScopeDTO> loadSpuScopeDTOFromMapper(Long activityId) {
        List<PrmActivitySpuScope> prmActivitySpuScopes = spuScopeMapper.selectByActivityId(activityId);
        return ObjectUtil.isEmpty(prmActivitySpuScopes) ? null : HutoolBeanUtils.toBean(prmActivitySpuScopes, ActivitySpuScopeDTO.class);
    }

    /**
     * @Description: 获取促销绑定门店列表
     * @Author: liuxingyu
     * @Date: 2024/5/16 12:11
     */
    private List<ActivityBranchScopeDTO> loadBranchScopeDTOFromMapper(Long activityId) {
        List<PrmActivityBranchScope> prmActivityBranchScopes = branchScopeMapper.selectByActivityId(activityId);
        return ObjectUtil.isEmpty(prmActivityBranchScopes) ? null : HutoolBeanUtils.toBean(prmActivityBranchScopes, ActivityBranchScopeDTO.class);
    }

    /**
     * @Description: 获取促销绑定城市列表
     * @Author: liuxingyu
     * @Date: 2024/5/16 12:11
     */
    private List<ActivityCityScopeDTO> loadCityScopeDTOFromMapper(Long activityId) {
        List<PrmActivityCityScope> prmActivityCityScopes = cityScopeMapper.selectByActivityId(activityId);
        return ObjectUtil.isEmpty(prmActivityCityScopes) ? null : HutoolBeanUtils.toBean(prmActivityCityScopes, ActivityCityScopeDTO.class);
    }

    /**
     * @Description: 获取促销绑定渠道列表
     * @Author: liuxingyu
     * @Date: 2024/5/16 12:11
     */
    private List<ActivityChannelScopeDTO> loadChannelScopeDTOFromMapper(Long activityId) {
        List<PrmActivityChannelScope> prmActivityChannelScopes = channelScopeMapper.selectByActivityId(activityId);
        return ObjectUtil.isEmpty(prmActivityChannelScopes) ? null : HutoolBeanUtils.toBean(prmActivityChannelScopes, ActivityChannelScopeDTO.class);
    }

    private BranchDTO loadBranchDTOFromApi(Long branchId) {
        return branchApi.getByBranchId(branchId).getCheckedData();
    }

    @Override
    public CouponTemplateDTO getCouponTemplate(Long couponTemplateId) {
        return couponTemplateCache.get(couponTemplateId);
    }

    @Override
    public BrandDTO getBrand(Long branchId) {
        return brandDtoCache.get(branchId);
    }

    @Override
    public AreaClassDTO getAreaClass(Long classId) {
        return areaClassDtoCache.get(classId);
    }

    @Override
    public SaleClassDTO getSaleClass(Long classId) {
        return saleClassDtoCache.get(classId);
    }

    @Override
    public SpuDTO getSpuDTO(Long spuId) {
        return spuDTOCache.get(spuId);
    }


    @Override
    public SupplierDTO getSupplierDTO(Long supplierId) {
        if (Objects.isNull(supplierId)) {
            return null;
        }
        return supplierDTOCache.get(supplierId);
    }

    @Override
    public BranchDTO getBranchDTO(Long branchId) {
        return branchDTOCache.get(branchId);
    }

    @Override
    public SkuDTO getSkuDTO(Long skuId) {
        return skuDTOCache.get(skuId);
    }

    /**
     * @Description: 删除促销绑定门店缓存
     * @Author: liuxingyu
     * @Date: 2024/5/16 11:49
     */
    @Override
    public Boolean removeBranchScope(Long activityId) {
        return branchScopeDTOCache.remove(activityId);
    }

    /**
     * @Description: 删除促销绑定城市缓存
     * @Author: liuxingyu
     * @Date: 2024/5/16 11:50
     */
    @Override
    public Boolean removeCityScope(Long activityId) {
        return cityScopeDTOCache.remove(activityId);
    }

    /**
     * @Description: 删除促销绑定渠道缓存
     * @Author: liuxingyu
     * @Date: 2024/5/16 11:50
     */
    @Override
    public Boolean removeChannelScope(Long activityId) {
        return channelScopeDTOCache.remove(activityId);
    }

    /**
     * @Description: 删除促销绑定spu缓存
     * @Author: liuxingyu
     * @Date: 2024/5/16 11:50
     */
    @Override
    public Boolean removeSpuScopeScope(Long activityId) {
        return spuScopeDTOCache.remove(activityId);
    }

    /**
     * @Description: 获取促销绑定门店列表
     * @Author: liuxingyu
     * @Date: 2024/5/16 11:54
     */
    @Override
    public List<ActivityBranchScopeDTO> getBranchScope(Long activityId) {
        return branchScopeDTOCache.get(activityId);
    }

    /**
     * @Description: 获取促销绑定渠道列表
     * @Author: liuxingyu
     * @Date: 2024/5/16 11:54
     */
    @Override
    public List<ActivityChannelScopeDTO> getChannelScope(Long activityId) {
        return channelScopeDTOCache.get(activityId);
    }

    /**
     * @Description: 获取促销绑定城市列表
     * @Author: liuxingyu
     * @Date: 2024/5/16 11:54
     */
    @Override
    public List<ActivityCityScopeDTO> getCityScope(Long activityId) {
        return cityScopeDTOCache.get(activityId);
    }

    /**
     * @Description: 获取促销绑定spu列表
     * @Author: liuxingyu
     * @Date: 2024/5/16 11:54
     */
    @Override
    public List<ActivitySpuScopeDTO> getSpuScope(Long activityId) {
        return spuScopeDTOCache.get(activityId);
    }

    @Override
    public CatgoryDTO getCatgoryDTO(Long categoryId) {
        return catgoryDTOCache.get(categoryId);
    }

    @Override
    public ChannelDTO getChannelDTO(Long channelId) {
        return channelDTOCache.get(channelId);
    }

    @Override
    public AreaDTO getAreaDTO(Long areaId) {
        return areaDtoCache.get(areaId);
    }

    @Override
    public Boolean removeSkRule(Long activityId) {
       return activitySkRuleCache.remove(activityId);
    }

    @Override
    public Boolean removeSupplierScope(Long activityId) {
        return supplierScopeDTOCache.remove(activityId);
    }

    @Override
    public List<ActivitySupplierScopeDTO> getSupplierScope(Long activityId) {
        return supplierScopeDTOCache.get(activityId);
    }

    @Override
    public ColonelDTO getColonel(Long colonelId) {
        return colonelDTOCache.get(colonelId);
    }

    private BrandDTO loadBrandDtoFromApi(Long brandId) {
        return brandApi.getBrandByBrandId(brandId).getCheckedData();
    }

    private AreaClassDTO loadAreaClassDtoFromApi(Long areaClassId) {
        return areaClassApi.getAreaClassByAreaClassId(areaClassId).getCheckedData();
    }

    private ColonelDTO loadColonelDtoFromApi(Long colonelId){return colonelApi.getByColonelId(colonelId).getCheckedData();}

    private SaleClassDTO loadSaleClassDtoFromApi(Long saleClassId) {
        return saleClassApi.getSaleClassBySaleClassId(saleClassId).getCheckedData();
    }

    private SpuDTO loadSpuDtoFromApi(Long spuId) {
        return spuApi.getBySpuId(spuId).getCheckedData();
    }


    private SkuDTO loadSkuDtoFromApi(Long spuId) {
        return skuApi.getBySkuId(spuId).getCheckedData();
    }

    private SupplierDTO loadSupplierDTOFromApi(Long supplierId) {
        return supplierApi.getBySupplierId(supplierId).getCheckedData();
    }

    @Override
    public List<CouponReceiveScopeDTO> getCouponReceiveScopeList(Long couponTemplateId) {
        return couponReceiveScopeCache.get(couponTemplateId);
    }
}
