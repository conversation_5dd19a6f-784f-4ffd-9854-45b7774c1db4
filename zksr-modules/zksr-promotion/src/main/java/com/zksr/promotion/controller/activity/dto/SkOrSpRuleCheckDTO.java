package com.zksr.promotion.controller.activity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @Date 2025/3/3 16:26
 * @注释
 */
@Data
@ApiModel(value = "秒杀和特价规则校验")
public class SkOrSpRuleCheckDTO {

    @Excel(name = "活动名称")
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @Excel(name = "spuId")
    @ApiModelProperty(value = "spuId")
    private Long spuId;

    @Excel(name = "spu名称")
    @ApiModelProperty(value = "spu名称")
    private String spuName;

    /** 活动开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "活动开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "活动开始时间")
    private Date startTime;

    /** 活动结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "活动结束时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "活动结束时间")
    private Date endTime;


}
