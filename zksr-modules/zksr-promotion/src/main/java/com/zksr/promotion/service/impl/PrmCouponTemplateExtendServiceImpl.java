package com.zksr.promotion.service.impl;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateExtendDTO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateExtendPageReqVO;
import com.zksr.promotion.controller.template.dto.PrmCouponTemplateReceiveRespVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateExtendRespVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateReceivePageVO;
import com.zksr.promotion.convert.template.PrmCouponTemplateExtendConvert;
import com.zksr.promotion.domain.PrmCouponTemplate;
import com.zksr.promotion.domain.PrmCouponTemplateExtend;
import com.zksr.promotion.mapper.PrmCouponTemplateExtendMapper;
import com.zksr.promotion.mapper.PrmCouponTemplateMapper;
import com.zksr.promotion.service.IPrmCouponTemplateExtendService;
import com.zksr.promotion.service.IPromotionCacheService;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.CouponCustomerOrderUseTotalDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 优惠券模版拓展统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
public class PrmCouponTemplateExtendServiceImpl implements IPrmCouponTemplateExtendService {

    @Autowired
    private PrmCouponTemplateExtendMapper prmCouponTemplateExtendMapper;

    @Autowired
    private PrmCouponTemplateMapper prmCouponTemplateMapper;

    @Autowired
    private OrderApi orderApi;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmCouponTemplateExtendRespVO> getPrmCouponTemplateExtendPage(PrmCouponTemplateExtendPageReqVO pageReqVO) {
        // 手动分页
        Page<PrmCouponTemplateExtendRespVO> page = PageUtils.startPage(pageReqVO);
        List<PrmCouponTemplateExtendRespVO> list = prmCouponTemplateExtendMapper.selectPageExt(pageReqVO);

        list.stream().forEach(item -> {
            // 活动状态
            // 0=进行中,1=已结束,2-未开始,3-已下架
            if (item.getStatus() == 0
                    && item.getTemplateStartDate().getTime() < System.currentTimeMillis()
                    && item.getTemplateEndDate().getTime() > System.currentTimeMillis()) {
                item.setActivityStatus(0);
            } else if ((item.getStatus() == 0 || item.getStatus() == 1) && item.getTemplateEndDate().getTime() < System.currentTimeMillis()) {
                item.setActivityStatus(1);
            } else if ((item.getStatus() == 0 || item.getStatus() == 1) && item.getTemplateStartDate().getTime() > System.currentTimeMillis()) {
                item.setActivityStatus(2);
            } else if (item.getStatus() == 2) {
                item.setActivityStatus(3);
            }
        });
        return PageResult.result(page, list);
    }

    /**
     * 更新来自订单表的数据统计
     * @param extendTotalVOList
     */
    @Override
    public void updateCouponTemplateOrderExtend(List<CouponTemplateExtendDTO> extendTotalVOList) {
        if (Objects.isNull(extendTotalVOList)) {
            return;
        }
        extendTotalVOList = extendTotalVOList.stream().filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getCouponTemplateId())).collect(Collectors.toList());
        for (CouponTemplateExtendDTO extendDTO : extendTotalVOList) {
            PrmCouponTemplateExtend templateExtend = getPrmCouponTemplateExtend(extendDTO.getCouponTemplateId());
            // 设置数据
            PrmCouponTemplateExtendConvert.INSTANCE.buildSet(templateExtend, extendDTO);
            templateExtend.setUpdateTime(DateUtil.date());
            // 更新数据
            prmCouponTemplateExtendMapper.updateById(templateExtend);
        }
    }

    /**
     * 更新优惠券, 领取, 使用数据, 自采集
     * @param couponTemplateIdList
     */
    @Override
    public void updateCouponTemplateCouponExtend(List<Long> couponTemplateIdList) {
        if (Objects.isNull(couponTemplateIdList) || couponTemplateIdList.isEmpty()) {
            return;
        }
        List<PrmCouponTemplateExtend> list = prmCouponTemplateExtendMapper.selectCouponExtendTotal(couponTemplateIdList);
        for (PrmCouponTemplateExtend templateDTO : list) {
            Long couponTemplateId = templateDTO.getCouponTemplateId();

            PrmCouponTemplateExtend templateExtend = getPrmCouponTemplateExtend(couponTemplateId);
            // 设置数据
            PrmCouponTemplateExtendConvert.INSTANCE.buildSet(templateExtend, templateDTO);
            templateExtend.setUpdateTime(DateUtil.date());
            // 更新数据
            prmCouponTemplateExtendMapper.updateById(templateExtend);
        }
    }

    @Override
    public PageResult<PrmCouponTemplateReceiveRespVO> getCounponTemplateReceiveListPage(PrmCouponTemplateReceivePageVO pageReqVO) {
        // 手动分页
        Page<PrmCouponTemplateReceiveRespVO> page = PageUtils.startPage(pageReqVO);
        List<PrmCouponTemplateReceiveRespVO> list = prmCouponTemplateExtendMapper.selectCouponExtendReceivePage(pageReqVO);
        if (ToolUtil.isEmpty(list)) {
            return PageResult.result(page, list);
        }
        List<CouponCustomerOrderUseTotalDTO> couponCustomerUseTotalDTOList =
                orderApi.getCouponCustomerUseTotal(pageReqVO.getCouponTemplateId(), list.stream().map(PrmCouponTemplateReceiveRespVO::getCustomerId).collect(Collectors.toList())).getCheckedData();
        Map<Long, CouponCustomerOrderUseTotalDTO> couponCustomerUseTotalDTOMap = CollectionUtils.convertMap(couponCustomerUseTotalDTOList, CouponCustomerOrderUseTotalDTO::getCustomerId);
        list.stream().forEach(coupon -> {
                    CouponCustomerOrderUseTotalDTO couponCustomerUseTotalDTO = couponCustomerUseTotalDTOMap.get(coupon.getCustomerId());
                    BranchDTO branchDTO = promotionCacheService.getBranchDTO(coupon.getCustomerId());
                    coupon.setCouponTemplateId(pageReqVO.getCouponTemplateId())
                            .setCustomerName(branchDTO.getBranchName())
                            .setOrderNumSum(couponCustomerUseTotalDTO==null? 0L :couponCustomerUseTotalDTO.getOrderNumSum())
                            .setOrderAmtSum(couponCustomerUseTotalDTO==null? BigDecimal.ZERO :couponCustomerUseTotalDTO.getOrderAmtSum())
                    ;
                });
        return PageResult.result(page, list);
    }

    /**
     * 获取订单数据扩展统计
     * @param couponTemplateId
     * @return
     */
    public PrmCouponTemplateExtend getPrmCouponTemplateExtend(Long couponTemplateId) {
        PrmCouponTemplateExtend couponTemplateExtend = prmCouponTemplateExtendMapper.selectById(couponTemplateId);
        if (Objects.isNull(couponTemplateExtend)) {
            couponTemplateExtend = PrmCouponTemplateExtend.builder()
                    .couponTemplateId(couponTemplateId)
                    .totalCouponAmt(BigDecimal.ZERO)
                    .totalSaleAmt(BigDecimal.ZERO)
                    .usedCount(NumberPool.INT_ZERO)
                    .recordCount(NumberPool.INT_ZERO)
                    .build();
            Long sysCode = SecurityContextHolder.hasSysCode();
            if (Objects.isNull(sysCode)) {
                PrmCouponTemplate couponTemplate = prmCouponTemplateMapper.selectById(couponTemplateId);
                couponTemplateExtend.setSysCode(couponTemplate.getSysCode());
            }
            prmCouponTemplateExtendMapper.insert(couponTemplateExtend);
        }
        return couponTemplateExtend;
    }
}
