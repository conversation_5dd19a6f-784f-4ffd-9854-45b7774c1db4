package com.zksr.promotion.convert.couponBatch;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.rocketmq.event.promotion.CouponReceiveEvent;
import com.zksr.promotion.api.coupon.dto.CouponReceiveDTO;
import com.zksr.promotion.domain.PrmCouponBatch;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchRespVO;
import com.zksr.promotion.controller.couponBatch.vo.PrmCouponBatchSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 优惠券批量发送 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-12-05
*/
@Mapper
public interface PrmCouponBatchConvert {

    PrmCouponBatchConvert INSTANCE = Mappers.getMapper(PrmCouponBatchConvert.class);

    PrmCouponBatchRespVO convert(PrmCouponBatch prmCouponBatch);

    PrmCouponBatch convert(PrmCouponBatchSaveReqVO prmCouponBatchSaveReq);

    PageResult<PrmCouponBatchRespVO> convertPage(PageResult<PrmCouponBatch> prmCouponBatchPage);

    CouponReceiveEvent convert2CouponReceiveEvent(CouponReceiveDTO dto);
}