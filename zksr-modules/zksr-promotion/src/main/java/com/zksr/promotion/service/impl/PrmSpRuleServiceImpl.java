package com.zksr.promotion.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.exception.ErrorCode;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.materialApply.dto.MaterialApplyDTO;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.controller.activity.dto.SkOrSpRuleCheckDTO;
import com.zksr.promotion.controller.activity.vo.PrmActivityItemPageVo;
import com.zksr.promotion.controller.activity.vo.PrmActivityPageReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import com.zksr.promotion.controller.rule.dto.PrmSkRuleDTO;
import com.zksr.promotion.controller.rule.vo.*;
import com.zksr.promotion.convert.activity.PrmActivityConvert;
import com.zksr.promotion.convert.rule.PrmSkRuleConvert;
import com.zksr.promotion.convert.rule.PrmSpRuleConvert;
import com.zksr.promotion.domain.*;
import com.zksr.promotion.domain.excel.PrmSpRuleImportExcel;
import com.zksr.promotion.mapper.*;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPrmSpRuleService;
import com.zksr.promotion.service.IPromotionCacheService;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.PRDT_MATERIAL_APPLY_TYPE_1;
import static com.zksr.product.constant.ProductConstant.PRDT_SHELF_STATUS_1;
import static com.zksr.promotion.enums.ErrorCodeConstants.ACTIVITY_STATUS_EDIT_ERROR;
import static com.zksr.promotion.enums.ErrorCodeConstants.SPECIAL_OFFER_IMPORT_ERROR;

/**
 * 特价活动规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Service
public class PrmSpRuleServiceImpl implements IPrmSpRuleService {
    @Autowired
    private PrmSpRuleMapper prmSpRuleMapper;

    @Autowired
    private PrmSkRuleMapper prmSkRuleMapper;

    @Autowired
    private PrmActivityMapper prmActivityMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PrmActivityBranchScopeMapper prmActivityBranchScopeMapper;

    @Autowired
    private PrmActivityCityScopeMapper prmActivityCityScopeMapper;

    @Autowired
    private PrmActivityChannelScopeMapper prmActivityChannelScopeMapper;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private IPrmActivityService prmActivityService;

    @Autowired
    private IPrmActivityCommonService prmActivityCommonService;

    @Autowired
    private PrmActivitySupplierScopeMapper prmActivitySupplierScopeMapper;

    /**
     * 新增特价活动规则
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertPrmSpRule(PrmSkRuleDTO createReqVO) {
        createReqVO.setActivityId(null);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 设置全国还是本地活动
        createReqVO.setFuncScope(Objects.isNull(loginUser.getDcId()) ? NumberPool.INT_ONE : NumberPool.INT_TWO);
        //校验特价规则信息
        this.checkSpRuleInfo(PrmActivityConvert.INSTANCE.convert(createReqVO), createReqVO.getSpRuleList().stream().map(PrmSpRuleSaveReqVO::getSkuId).collect(Collectors.toList()));
        if (createReqVO.getPrmStatus() == 1) {
            createReqVO.setEffectMan(loginUser.getSysUser().getUserName());
            createReqVO.setEffectTime(new Date());
        }
        createReqVO.setFuncScope(Objects.isNull(loginUser.getDcId()) ? NumberPool.INT_ONE : NumberPool.INT_TWO);
        createReqVO.setSpuScope(4);
        createReqVO.setPrmNo(PrmNoEnum.SP.getType());
        createReqVO.setPrmSheetNo(PrmNoEnum.SP.getType() + redisService.getUniqueNumber());

        PrmActivity prmActivity = PrmActivityConvert.INSTANCE.convert(createReqVO);
        prmActivity.setCreateBy(loginUser.getSysUser().getUserName());
        prmActivityMapper.insert(prmActivity);

        //如果是指定渠道 则保存活动渠道信息

        if (ToolUtil.isNotEmpty(createReqVO.getChanelScopeAllFlag()) && createReqVO.getChanelScopeAllFlag() == 1) {
            createReqVO.getChannelIds().forEach(channelId -> {
                PrmActivityChannelScope prmActivityChannelScope = new PrmActivityChannelScope();
                prmActivityChannelScope.setActivityId(prmActivity.getActivityId());
                prmActivityChannelScope.setChannelId(Long.parseLong(channelId.getId()));
                prmActivityChannelScope.setWhiteOrBlack(channelId.getType());
                prmActivityChannelScopeMapper.insert(prmActivityChannelScope);
            });
        }

        //如果是指定门店 则保存活动门店信息
        if (ToolUtil.isNotEmpty(createReqVO.getBranchScopeAllFlag()) && createReqVO.getBranchScopeAllFlag() == 1) {
            createReqVO.getBranchIds().forEach(branchId -> {
                PrmActivityBranchScope prmActivityBranchScope = new PrmActivityBranchScope();
                prmActivityBranchScope.setActivityId(prmActivity.getActivityId());
                prmActivityBranchScope.setBranchId(Long.parseLong(branchId.getId()));
                prmActivityBranchScope.setWhiteOrBlack(branchId.getType());
                prmActivityBranchScopeMapper.insert(prmActivityBranchScope);
            });
        }

        //如果是指定城市 则保存活动城市信息
        if (ToolUtil.isNotEmpty(createReqVO.getAreaIds())) {
            createReqVO.getAreaIds().forEach(cityId -> {
                PrmActivityCityScope prmActivityCityScope = new PrmActivityCityScope();
                prmActivityCityScope.setActivityId(prmActivity.getActivityId());
                prmActivityCityScope.setAreaId(Long.parseLong(cityId.getId()));
                prmActivityCityScope.setWhiteOrBlack(cityId.getType());
                prmActivityCityScopeMapper.insert(prmActivityCityScope);
            });
        }

        // 保存活动入驻商信息
        if (ToolUtil.isNotEmpty(createReqVO.getSupplierIds())) {
            createReqVO.getSupplierIds().forEach(supplierId -> {
                PrmActivitySupplierScope prmActivitySupplierScope = new PrmActivitySupplierScope();
                prmActivitySupplierScope.setActivityId(prmActivity.getActivityId());
                prmActivitySupplierScope.setSupplierId(supplierId);
                prmActivitySupplierScopeMapper.insert(prmActivitySupplierScope);
            });
        }

        createReqVO.getSpRuleList().forEach(spRule -> {
            spRule.setActivityId(prmActivity.getActivityId());
            // 默认启用
            spRule.setStatus(NumberPool.INT_ONE);
            // 插入
            PrmSpRule prmSpRule = PrmSpRuleConvert.INSTANCE.convert(spRule);
            prmSpRule.setSpRuleId(null);
            prmSpRuleMapper.insert(prmSpRule);
        });

        //新增素材信息
        if(ToolUtil.isNotEmpty(createReqVO.getMaterialId())){
            prmActivityCommonService.addMaterialApply(MaterialApplyDTO.builder()
                    .materialId(createReqVO.getMaterialId())
                    .applyId(prmActivity.getActivityId())
                    .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                    .startTime(createReqVO.getStartTime())
                    .endTime(createReqVO.getEndTime()).build());

        }

        // 刷新缓存
        prmActivityService.reloadSupplierActivity(prmActivity.getActivityId());
        // 返回
        return prmActivity.getActivityId();
    }

    @Override
    public Boolean editSpRuleInfo(PrmSkRuleDTO updateReqVO) {

        //停用不需要校验  直接停用即可
        if (ToolUtil.isEmpty(updateReqVO.getAreaIds()) && updateReqVO.getPrmStatus() == 2) {
            PrmActivity prmActivityReq = PrmActivityConvert.INSTANCE.convert(updateReqVO);
            prmActivityMapper.updateById(prmActivityReq);
        } else {

            PrmActivity prmActivity = prmActivityMapper.selectById(updateReqVO.getActivityId());

            if (ToolUtil.isNotEmpty(prmActivity.getEffectMan()) && ToolUtil.isNotEmpty(updateReqVO.getAreaIds())) {
                throw exception(ACTIVITY_STATUS_EDIT_ERROR);
            }
            updateReqVO.setFuncScope(prmActivity.getFuncScope());
            // 验证数据
            this.checkSpRuleInfo(
                    prmActivityMapper.selectById(updateReqVO.getActivityId()),
                    prmSpRuleMapper.selectByActivityId(updateReqVO.getActivityId()).stream().map(PrmSpRule::getSkuId).collect(Collectors.toList())
            );

            if (updateReqVO.getPrmStatus() == 1L) {
                updateReqVO.setEffectMan(SecurityUtils.getLoginUser().getSysUser().getUserName());
                updateReqVO.setEffectTime(new Date());
            }

            PrmActivity prmActivityReq = PrmActivityConvert.INSTANCE.convert(updateReqVO);
            prmActivityMapper.updateById(prmActivityReq);

            //如果是指定渠道 则保存活动渠道信息
            if (ToolUtil.isNotEmpty(updateReqVO.getChannelIds())) {
                if (updateReqVO.getChanelScopeAllFlag() == 1) {
                    prmActivityChannelScopeMapper.deleteChannelByActivityId(updateReqVO.getActivityId());
                    updateReqVO.getChannelIds().forEach(channelId -> {
                        PrmActivityChannelScope prmActivityChannelScope = new PrmActivityChannelScope();
                        prmActivityChannelScope.setActivityId(prmActivity.getActivityId());
                        prmActivityChannelScope.setChannelId(Long.parseLong(channelId.getId()));
                        prmActivityChannelScope.setWhiteOrBlack(channelId.getType());
                        prmActivityChannelScopeMapper.insert(prmActivityChannelScope);
                    });
                }
            }


            if (ToolUtil.isNotEmpty(updateReqVO.getBranchIds())) {
                //如果是指定门店 则保存活动门店信息
                if (updateReqVO.getBranchScopeAllFlag() == 1) {
                    prmActivityBranchScopeMapper.deleteBranchByActivityId(updateReqVO.getActivityId());
                    updateReqVO.getBranchIds().forEach(branchId -> {
                        PrmActivityBranchScope prmActivityBranchScope = new PrmActivityBranchScope();
                        prmActivityBranchScope.setActivityId(prmActivity.getActivityId());
                        prmActivityBranchScope.setBranchId(Long.parseLong(branchId.getId()));
                        prmActivityBranchScope.setWhiteOrBlack(branchId.getType());
                        prmActivityBranchScopeMapper.insert(prmActivityBranchScope);
                    });
                }
            }

            if (ToolUtil.isNotEmpty(updateReqVO.getAreaIds())) {
                prmActivityCityScopeMapper.deleteCityByActivityId(updateReqVO.getActivityId());
                //如果是指定城市 则保存活动城市信息
                updateReqVO.getAreaIds().forEach(cityId -> {
                    PrmActivityCityScope prmActivityCityScope = new PrmActivityCityScope();
                    prmActivityCityScope.setActivityId(prmActivity.getActivityId());
                    prmActivityCityScope.setAreaId(Long.parseLong(cityId.getId()));
                    prmActivityCityScope.setWhiteOrBlack(cityId.getType());
                    prmActivityCityScopeMapper.insert(prmActivityCityScope);
                });
            }

            if (ToolUtil.isNotEmpty(updateReqVO.getSupplierIds())) {
                prmActivitySupplierScopeMapper.deleteSupplierByActivityId(updateReqVO.getActivityId());
                //保存活动入驻商信息
                updateReqVO.getSupplierIds().forEach(supplierId -> {
                    PrmActivitySupplierScope prmActivitySupplierScope = new PrmActivitySupplierScope();
                    prmActivitySupplierScope.setActivityId(prmActivity.getActivityId());
                    prmActivitySupplierScope.setSupplierId(supplierId);
                    prmActivitySupplierScopeMapper.insert(prmActivitySupplierScope);
                });
            }

            if (ToolUtil.isNotEmpty(updateReqVO.getSpRuleList())) {
                prmSpRuleMapper.deleteSpRuleByActivityId(updateReqVO.getActivityId());
                updateReqVO.getSpRuleList().forEach(spRule -> {
                    spRule.setActivityId(prmActivity.getActivityId());
                    // 默认启用
                    spRule.setStatus(NumberPool.INT_ONE);
                    // 插入
                    PrmSpRule prmSpRule = PrmSpRuleConvert.INSTANCE.convert(spRule);
                    prmSpRuleMapper.insert(prmSpRule);
                });
            }

            //修改素材信息
            if(ToolUtil.isNotEmpty(updateReqVO.getMaterialId())){
                prmActivityCommonService.editMaterialApply(MaterialApplyDTO.builder()
                        .materialId(updateReqVO.getMaterialId())
                        .applyId(updateReqVO.getActivityId())
                        .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                        .startTime(prmActivity.getStartTime())
                        .endTime(prmActivity.getEndTime())
                        .newStartTime(updateReqVO.getStartTime())
                        .newEndTime(updateReqVO.getEndTime())
                        .build());

            }
        }

        return true;
    }


    /**
     * 删除特价活动规则
     *
     * @param spRuleId 特价活动规则id
     */
    @Override
    public void deletePrmSpRule(Long spRuleId) {
        // 删除
        prmSpRuleMapper.deleteById(spRuleId);
    }

    /**
     * 批量删除特价活动规则
     *
     * @param spRuleIds 需要删除的特价活动规则主键
     * @return 结果
     */
    @Override
    public void deletePrmSpRuleBySpRuleIds(Long[] spRuleIds) {
        for (Long spRuleId : spRuleIds) {
            this.deletePrmSpRule(spRuleId);
        }
    }

    /**
     * 获得特价活动规则
     *
     * @param spRuleId 特价活动规则id
     * @return 特价活动规则
     */
    @Override
    public PrmSpRule getPrmSpRule(Long spRuleId) {
        return prmSpRuleMapper.selectById(spRuleId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrmSpRule> getPrmSpRulePage(PrmSpRulePageReqVO pageReqVO) {
        return prmSpRuleMapper.selectPage(pageReqVO);
    }

    @Override
    public PrmActivityRespVO getSpRuleInfo(Long activityId) {
        PrmActivity prmActivity = prmActivityMapper.selectById(activityId);
        PrmActivityRespVO respVO = HutoolBeanUtils.toBean(prmActivity, PrmActivityRespVO.class);
        if (respVO != null) {
            PrmSpRulePageReqVO reqVO = new PrmSpRulePageReqVO();
            reqVO.setActivityId(activityId);
            List<PrmSpRule> list = prmSpRuleMapper.selectList(reqVO);
            List<PrmSpRuleRespVO> spRuleList = HutoolBeanUtils.toBean(list, PrmSpRuleRespVO.class);
            spRuleList.forEach(spRule -> {
                SpuDTO spuDTO = promotionCacheService.getSpuDTO(spRule.getSpuId());
                SkuDTO skuDTO = promotionCacheService.getSkuDTO(spRule.getSkuId());
                SupplierDTO supplierDTO = promotionCacheService.getSupplierDTO(spuDTO.getSupplierId());
                spRule.setSpuName(spuDTO.getSpuName());
                spRule.setSupplierName(supplierDTO.getSupplierName());
                spRule.setUnit(skuDTO.getUnit());
                spRule.setBarcode(skuDTO.getBarcode());
                spRule.setMarkPrice(skuDTO.getMarkPrice());
                spRule.setMidMarkPrice(skuDTO.getMidMarkPrice());
                spRule.setLargeMarkPrice(skuDTO.getLargeMarkPrice());
                spRule.setSpuNo(spuDTO.getSpuNo());
                spRule.setStock(redisStockService.getSurplusSaleQty(skuDTO.getSkuId()).intValue());
            });
            respVO.setSpRuleList(spRuleList);
            List<PrmActivityBranchScope> branchIds = prmActivityBranchScopeMapper.getBranchIdsByActivityId(activityId);
            if (ToolUtil.isNotEmpty(branchIds)) {
                //组装门店数据
                respVO.setBranchIds(
                        branchIds.stream().map(branch -> {
                            PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                            prmActivityWhiteOrBlackVO.setId(branch.getBranchId().toString());
                            prmActivityWhiteOrBlackVO.setType(branch.getWhiteOrBlack());
                            return prmActivityWhiteOrBlackVO;
                        }).collect(Collectors.toList())
                );
            }
            List<PrmActivityChannelScope> channelIds = prmActivityChannelScopeMapper.getChannelByActivityId(activityId);
            if (ToolUtil.isNotEmpty(channelIds)) {
                //组装渠道数据
                respVO.setChannelIds(
                        channelIds.stream().map(channel -> {
                            PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                            prmActivityWhiteOrBlackVO.setId(channel.getChannelId().toString());
                            prmActivityWhiteOrBlackVO.setType(channel.getWhiteOrBlack());
                            return prmActivityWhiteOrBlackVO;
                        }).collect(Collectors.toList()
                        ));
            }
            List<PrmActivityCityScope> cityIds = prmActivityCityScopeMapper.getAreaIdsByActivityId(activityId);
            if (ToolUtil.isNotEmpty(cityIds)) {
                respVO.setAreaIds(
                        cityIds.stream().map(city -> {
                            PrmActivityWhiteOrBlackVO prmActivityWhiteOrBlackVO = new PrmActivityWhiteOrBlackVO();
                            prmActivityWhiteOrBlackVO.setId(city.getAreaId().toString());
                            prmActivityWhiteOrBlackVO.setType(city.getWhiteOrBlack());
                            return prmActivityWhiteOrBlackVO;
                        }).collect(Collectors.toList())
                );
            }
            // 查询入驻商数据
            List<Long> supplierIds = prmActivitySupplierScopeMapper.getSupplierByActivityId(activityId);
            if(CollectionUtils.isNotEmpty(supplierIds)){
                List<String> stringSupplierIds = supplierIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());
                respVO.setSupplierIds(stringSupplierIds);
            }
        }

        //填充素材信息
        respVO.setMaterialApplyVO(prmActivityCommonService.getByMaterialApplyByMaterial(
                MaterialApplyVO.builder()
                        .applyId(prmActivity.getActivityId())
                        .applyType(PRDT_MATERIAL_APPLY_TYPE_1)
                        .startTime(prmActivity.getStartTime())
                        .endTime(prmActivity.getEndTime()).build()));

        return respVO;
    }

    public void checkSpRuleInfo(PrmActivity activity, List<Long> skuList) {
        // 重复冲突的活动
        List<SkOrSpRuleCheckDTO> repeatActivity = prmSkRuleMapper.selectRepeatActivity(activity, skuList);
        if (!repeatActivity.isEmpty()) {
            throw exception(new ErrorCode(1_016_005_002, "温馨提示:同一个商品同时间只能参与一个秒杀/特价活动,该秒杀活动与" + StringUtils.join(repeatActivity.get(0).getActivityName(), StringPool.COMMA) + "特价活动存在相同商品"));
        }
    }

    /**
     * 导入特价商品
     *
     * @param prmSpRuleList
     * @return
     */
    @Override
    public Map<String, Object> importPrmSpRuleData(List<PrmSpRuleImportExcel> prmSpRuleList) {
        if (prmSpRuleList.isEmpty()) {
            exception(SPECIAL_OFFER_IMPORT_ERROR);
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder failureMsg = new StringBuilder();
        List<PrmSpRuleItemExcelRespVO> spRuleList = new ArrayList<>(); // 存储成功的规则列表

        // 遍历导入数据
        for (int line = 0; line < prmSpRuleList.size(); line++) {
            if (failureMsg.length() > 2000) { // 限制错误信息长度
                break;
            }
            int cellNumber = line + 1; // 当前行号
            PrmSpRuleImportExcel itemData = prmSpRuleList.get(line);

            try {
                //校验数据
                validatePrmSpRuleData(itemData, cellNumber);
                // 获取 PrmActivityItemPageVo 对象
                PrmActivityItemPageVo pageReqVO = new PrmActivityItemPageVo();
                SkuDTO skuDTO = promotionCacheService.getSkuDTO(itemData.getSkuId());
                pageReqVO.setSpuId(skuDTO.getSpuId());
                pageReqVO.setSkuId(itemData.getSkuId());
                pageReqVO.setShelfStatus(PRDT_SHELF_STATUS_1);
                pageReqVO.setSysCode(SecurityUtils.getLoginUser().getSysCode());
                pageReqVO.setSupplierId(itemData.getSupplierId());
                PrmActivityItemPageVo prmActivityItemPageVo = null;
                try {
                    prmActivityItemPageVo = prmActivityService.getItemPage(pageReqVO).getList().get(0);
                } catch (Exception e) {
                    throw new ServiceException(String.format("未查询到商品上架数据,请确认是否为上架商品!"));
                }
                PrmSpRuleItemExcelRespVO spRuleItemExcelRespVO = PrmSpRuleConvert.INSTANCE.convert(prmActivityItemPageVo);
                PrmSpRuleItemExcelRespVO updatedSpRuleItemExcelRespVO = PrmSpRuleConvert.INSTANCE.updateFromItemData(itemData, spRuleItemExcelRespVO);
                spRuleList.add(updatedSpRuleItemExcelRespVO);
                successNum++;
            } catch (Exception e) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据导入失败，错误信息：{}", cellNumber, e.getMessage()));
            }
        }

        // 构造返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("spRuleList", spRuleList); // 成功导入的数据列表

        if (failureNum > 0) {
            // 如果存在失败的数据，拼接失败信息
            String resultMessage = String.format(
                    "共导入 %d 条，成功 %d 条，失败 %d 条。失败原因如下：%s",
                    prmSpRuleList.size(), successNum, failureNum, failureMsg
            );
            resultMap.put("message", resultMessage);
        } else {
            // 如果全部成功
            String successMessage = String.format("恭喜您，数据已全部导入成功！共 %d 条", successNum);
            resultMap.put("message", successMessage);
        }

        return resultMap;
    }

    /**
     * 校验单行导入数据的规则
     *
     * @param itemData   导入数据对象
     * @param cellNumber 当前行号
     */
    private void validatePrmSpRuleData(PrmSpRuleImportExcel itemData, int cellNumber) {
        // 校验门店限量
        if (itemData.getOnceBuyLimit() != null) {
            if (itemData.getOnceBuyLimit() < 0) {
                throw new ServiceException(String.format("第%d行：门店限量不能为负数", cellNumber));
            }
            if (itemData.getStock() != null && itemData.getOnceBuyLimit() > itemData.getStock()) {
                throw new ServiceException(String.format("第%d行：门店限量不能大于库存", cellNumber));
            }
            if (itemData.getTotalLimitQty() != null && itemData.getOnceBuyLimit() > itemData.getTotalLimitQty()) {
                throw new ServiceException(String.format("第%d行：门店限量不能大于总限量", cellNumber));
            }
        }

        // 校验总限量
        if (itemData.getTotalLimitQty() != null && itemData.getTotalLimitQty() < 0) {
            throw new ServiceException(String.format("第%d行：总限量不能为负数", cellNumber));
        }

        // 校验促销价格（小单位）
        validateSpPrice(itemData.getSpPrice(), itemData.getMarkPrice(), "小单位", cellNumber);

        // 校验促销价格（中单位）
        validateSpPrice(itemData.getMidSpPrice(), itemData.getMidMarkPrice(), "中单位", cellNumber);

        // 校验促销价格（大单位）
        validateSpPrice(itemData.getLargeSpPrice(), itemData.getLargeMarkPrice(), "大单位", cellNumber);
    }

    /**
     * 校验促销价格通用方法
     */
    private void validateSpPrice(BigDecimal spPrice, BigDecimal markPrice, String unitType, int cellNumber) {
        if (spPrice != null) {
            if (spPrice.compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException(String.format("第%d行：促销价格%s不能为负数", cellNumber, unitType));
            }
            if (spPrice.compareTo(BigDecimal.ZERO) == 0) {
                throw new ServiceException(String.format("第%d行：促销价格%s不能为0", cellNumber, unitType));
            }
            if (markPrice != null && spPrice.compareTo(markPrice) > 0) {
                throw new ServiceException(String.format("第%d行：促销价格%s不能大于销售价", cellNumber, unitType));
            }
        }
    }
}
