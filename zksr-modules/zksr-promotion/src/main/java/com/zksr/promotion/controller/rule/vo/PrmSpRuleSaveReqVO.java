package com.zksr.promotion.controller.rule.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 特价活动规则对象 prm_sp_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ApiModel("特价活动规则 - prm_sp_rule分页 Request VO")
public class PrmSpRuleSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 特价活动规则id */
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Long spRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 特价活动id */
    @Excel(name = "特价活动id")
    @ApiModelProperty(value = "特价活动id", required = true)
    private Long activityId;

    /** SPU id */
    @Excel(name = "SPU id")
    @ApiModelProperty(value = "SPU id", required = true)
    private Long spuId;

    /** sku_id */
    @Excel(name = "sku_id")
    @ApiModelProperty(value = "sku_id", required = true)
    private Long skuId;

    /** 促销价 */
    @Excel(name = "小单位促销价")
    @ApiModelProperty(value = "小单位促销价", required = true)
    private BigDecimal spPrice;

    /** 秒杀价 */
    @Excel(name = "中单位促销价")
    @ApiModelProperty(value = "中单位促销价", required = true)
    private BigDecimal midSpPrice;

    /** 秒杀价 */
    @Excel(name = "大单位促销价")
    @ApiModelProperty(value = "大单位促销价", required = true)
    private BigDecimal largeSpPrice;

    /** 单次限购数量 */
    @Excel(name = "单次限购数量")
    @ApiModelProperty(value = "单次限购数量", required = true)
    private Integer onceBuyLimit;

    /** 总限量 */
    @Excel(name = "总限量")
    @ApiModelProperty(value = "总限量", required = true)
    private Integer totalLimitQty;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用", required = true)
    private Integer status;

    /** 中单位限购数量 */
    @Excel(name = "中单位限购数量")
    @ApiModelProperty(value = "中单位限购数量")
    private Integer midLimit;

    /** 大单位限购数量 */
    @Excel(name = "大单位限购数量")
    @ApiModelProperty(value = "大单位限购数量")
    private Integer largeLimit;

    /** 总限量（中单位） */
    @Excel(name = "总限量（中单位）")
    @ApiModelProperty(value = "总限量（中单位）")
    private Integer midTotalLimitQty;

    /** 总限量（大单位） */
    @Excel(name = "总限量（大单位）")
    @ApiModelProperty(value = "总限量（大单位）")
    private Integer largeTotalLimitQty;
}
