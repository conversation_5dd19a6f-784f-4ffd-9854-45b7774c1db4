package com.zksr.promotion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alicp.jetcache.Cache;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.enums.CouponReceiveScope;
import com.zksr.common.core.enums.CouponReceiveType;
import com.zksr.common.core.enums.CouponSpuScopeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.bean.CouponTemplateCacheBean;
import com.zksr.common.redis.enums.RedisCouponConstants;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.coupon.dto.CouponNormalCacheDTO;
import com.zksr.promotion.api.coupon.dto.CouponReceiveScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.controller.template.dto.SpuScopeOrReceiveScopeVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplatePageReqVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateRespVO;
import com.zksr.promotion.controller.template.vo.PrmCouponTemplateSaveReqVO;
import com.zksr.promotion.convert.rule.PrmCouponTemplateReturnRuleConvert;
import com.zksr.promotion.convert.template.CouponTemplateConvert;
import com.zksr.promotion.domain.PrmCouponScopeApply;
import com.zksr.promotion.domain.PrmCouponTemplate;
import com.zksr.promotion.domain.PrmCouponTemplateSupplier;
import com.zksr.promotion.mapper.PrmCouponMapper;
import com.zksr.promotion.mapper.PrmCouponTemplateMapper;
import com.zksr.promotion.service.*;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * 优惠券模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@Service
public class PrmCouponTemplateServiceImpl implements IPrmCouponTemplateService {

    @Autowired
    private PrmCouponTemplateMapper prmCouponTemplateMapper;

    @Autowired
    private PrmCouponMapper prmCouponMapper;

    @Autowired
    private IPrmCouponScopeApplyService couponScopeApplyService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CouponTemplateCacheBean couponTemplateCacheBean;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Autowired
    private Cache<Long, List<CouponSpuScopeDTO>> couponSpuScopeCache;

    @Autowired
    private Cache<Long, List<CouponReceiveScopeDTO>> couponReceiveScopeCache;

    @Autowired
    private IPrmCouponTemplateRepeatRuleService repeatRuleService;

    @Autowired
    private IPrmCouponTemplateSupplierService templateSupplierService;

    /**
     * 新增优惠券模板
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    public Long insertPrmCouponTemplate(PrmCouponTemplateSaveReqVO createReqVO) {
        // 数据验证
        validate(createReqVO);
        // 插入
        PrmCouponTemplate prmCouponTemplate = HutoolBeanUtils.toBean(createReqVO, PrmCouponTemplate.class);
        // 新增的默认是停用
        prmCouponTemplate.setStatus(NumberPool.INT_ONE);
        // 设置运营商ID
        prmCouponTemplate.setDcId(SecurityUtils.getLoginUser().getDcId());
        prmCouponTemplateMapper.insert(prmCouponTemplate);
        // 保存优惠券模板和多入驻商关系
        List<Long> supplierIds = createReqVO.getSupplierIds();
        if (ToolUtil.isNotEmpty(supplierIds)) {
            this.saveOrUpdateTemplateSupplier(supplierIds, prmCouponTemplate.getCouponTemplateId(), true);
        }
        if (ToolUtil.isNotEmpty(createReqVO.getSpuScopeList())) {
            saveApplyIds(createReqVO.getSpuScopeList(), prmCouponTemplate.getCouponTemplateId(), true);
        }
        if (ToolUtil.isNotEmpty(createReqVO.getReceiveScopeList())) {
            saveApplyIds(createReqVO.getReceiveScopeList(), prmCouponTemplate.getCouponTemplateId(), false);
        }
        if (Objects.nonNull(createReqVO.getRepeatRule())) {
            createReqVO.getRepeatRule().setCouponTemplateId(prmCouponTemplate.getCouponTemplateId());
            // 保存重复规则
            repeatRuleService.insertPrmCouponTemplateRepeatRule(createReqVO.getRepeatRule());
        }
        // 返回
        return prmCouponTemplate.getCouponTemplateId();
    }

    /**
     * 新增或更新优惠券模板入驻商信息
     */
    private void saveOrUpdateTemplateSupplier(List<Long> supplierIds, Long couponTemplateId, boolean saveOrUpdate) {
        // 新增为true,更新为false
        if (!saveOrUpdate) {
            // 更新时先跟据优惠券模板id删除原有数据
            templateSupplierService.deleteByCouponTemplateId(couponTemplateId);
        }
        List<PrmCouponTemplateSupplier> templateSupplierList = supplierIds.stream().filter(Objects::nonNull).map(t -> {
                    PrmCouponTemplateSupplier templateSupplier = new PrmCouponTemplateSupplier();
                    templateSupplier.setSupplierId(t);
                    templateSupplier.setCouponTemplateId(couponTemplateId);
                    templateSupplier.setSysCode(SecurityUtils.getLoginUser().getSysCode());
                    return templateSupplier;
                }
        ).collect(Collectors.toList());
        templateSupplierService.insertBatch(templateSupplierList);
    }

    /**
     * 修改优惠券模板
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional
    public void updatePrmCouponTemplate(PrmCouponTemplateSaveReqVO updateReqVO) {
        // 数据验证
        validate(updateReqVO);
        // 保存数据关系
        couponScopeApplyService.deleteByCouponTemplateId(updateReqVO.getCouponTemplateId());
        if (ToolUtil.isNotEmpty(updateReqVO.getSpuScopeList())) {
            saveApplyIds(updateReqVO.getSpuScopeList(), updateReqVO.getCouponTemplateId(), true);
        }
        if (ToolUtil.isNotEmpty(updateReqVO.getReceiveScopeList())) {
            saveApplyIds(updateReqVO.getReceiveScopeList(), updateReqVO.getCouponTemplateId(), false);
        }
        prmCouponTemplateMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrmCouponTemplate.class));
        // 更新优惠券模板和多入驻商关系
        List<Long> supplierIds = updateReqVO.getSupplierIds();
        if (ToolUtil.isNotEmpty(supplierIds)) {
            this.saveOrUpdateTemplateSupplier(supplierIds, updateReqVO.getCouponTemplateId(), false);
        }
        // 保存重复规则
        if (Objects.nonNull(updateReqVO.getRepeatRule())) {
            updateReqVO.getRepeatRule().setCouponTemplateId(updateReqVO.getCouponTemplateId());
            // 保存重复规则
            repeatRuleService.insertPrmCouponTemplateRepeatRule(updateReqVO.getRepeatRule());
        }
    }

    /**
     * 删除优惠券模板
     *
     * @param couponTemplateId 优惠券模板id
     */
    @Override
    public void deletePrmCouponTemplate(Long couponTemplateId) {
        // 删除
        PrmCouponTemplate update = new PrmCouponTemplate();
        update.setCouponTemplateId(couponTemplateId);
        update.setDelFlag(StringPool.TWO);
        prmCouponTemplateMapper.updateById(update);
    }

    /**
     * 批量删除优惠券模板
     *
     * @param couponTemplateIds 需要删除的优惠券模板主键
     * @return 结果
     */
    @Override
    public void deletePrmCouponTemplateByCouponTemplateIds(Long[] couponTemplateIds) {
        for (Long couponTemplateId : couponTemplateIds) {
            deletePrmCouponTemplate(couponTemplateId);
        }
    }

    /**
     * 获得优惠券模板
     *
     * @param couponTemplateId 优惠券模板id
     * @return 优惠券模板
     */
    @Override
    public PrmCouponTemplate getPrmCouponTemplate(Long couponTemplateId) {
        return prmCouponTemplateMapper.selectById(couponTemplateId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrmCouponTemplateRespVO> getPrmCouponTemplatePage(PrmCouponTemplatePageReqVO pageReqVO) {
        Page<PrmCouponTemplateRespVO> page = PageHelper.startPage(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        List<PrmCouponTemplateRespVO> couponTemplateRespVOS = prmCouponTemplateMapper.selectPageExt(pageReqVO);
        for (PrmCouponTemplateRespVO templateRespVO : couponTemplateRespVOS) {
            SupplierDTO supplierDTO = promotionCacheService.getSupplierDTO(templateRespVO.getSupplierId());
            // 组装入驻商名称
            if (Objects.nonNull(supplierDTO)) {
                templateRespVO.setSupplierName(supplierDTO.getSupplierName());
            }
            // 活动状态
            // 0=进行中,1=已结束,2-未开始,3-已下架
            if (templateRespVO.getStatus() == 0
                    && templateRespVO.getTemplateStartDate().getTime() < System.currentTimeMillis()
                    && templateRespVO.getTemplateEndDate().getTime() > System.currentTimeMillis()) {
                templateRespVO.setActivityStatus(0);
            } else if ((templateRespVO.getStatus() == 0 || templateRespVO.getStatus() == 1) && templateRespVO.getTemplateEndDate().getTime() < System.currentTimeMillis()) {
                templateRespVO.setActivityStatus(1);
            } else if ((templateRespVO.getStatus() == 0 || templateRespVO.getStatus() == 1) && templateRespVO.getTemplateStartDate().getTime() > System.currentTimeMillis()) {
                templateRespVO.setActivityStatus(2);
            } else if (templateRespVO.getStatus() == 2) {
                templateRespVO.setActivityStatus(3);
            }
            // 判断过期状态, 状态不是启用, 并且优惠券活动结束时间小于当前时间, 则是已过期
            if (templateRespVO.getTemplateEndDate().getTime() < System.currentTimeMillis() && templateRespVO.getStatus() != NumberPool.INT_TWO) {
                templateRespVO.setStatus(NumberPool.INT_THREE);
            }
        }
        return new PageResult<>(couponTemplateRespVOS, page.getTotal());
    }

    /**
     * 刷新优惠券缓存
     * 按照平台对刷新进行上锁, 防止缓存覆盖, 复写
     *
     * @param couponTemplate
     */
    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_COUPON_REFRESH, condition = "#couponTemplate.supplierId", leaseTime = 30L)
    public void refreshCache(PrmCouponTemplate couponTemplate) {
        // 刷新优惠券库存
        shelf().refreshStock(couponTemplate.getCouponTemplateId());
        /**
         * 目前只针对用户主动领取优惠券做了缓存优化
         */
        refreshUserCouponCache(couponTemplate);
    }

    /**
     * 加载具体使用范围 别称
     *
     * @param couponTemplate
     */
    @Override
    public List<CouponSpuScopeDTO> getApplySpuScopeList(PrmCouponTemplate couponTemplate) {
        // 全部不需要处理
        List<CouponSpuScopeDTO> alias = new ArrayList<>();
        // 多具体使用范围
        // 获取使用范围限定列表
        List<PrmCouponScopeApply> scopeApplyList = couponScopeApplyService.getCouponSpuScopeApplyByCouponTemplateId(couponTemplate.getCouponTemplateId());
        // 商品具体使用范围
        scopeApplyList.stream().collect(Collectors.groupingBy(PrmCouponScopeApply::getSpuScope)).forEach((spuCope, spuScopeApplyList) -> {
            ArrayList<CouponSpuScopeDTO> tempList = new ArrayList<>();
            List<Long> applyIds = spuScopeApplyList.stream().map(PrmCouponScopeApply::getApplyId).distinct().collect(Collectors.toList());
            // 加载具体使用范围
            if (CouponSpuScopeEnum.SUPPLIER.getScope().equals(spuCope)) {
                // 入驻商券
                tempList.addAll(applyIds.stream().map(promotionCacheService::getSupplierDTO).filter(Objects::nonNull).map(CouponSpuScopeDTO::build).collect(Collectors.toList()));
            } else if (CouponSpuScopeEnum.CLASS.getScope().equals(spuCope)) {
                // 类目券 (管理类别)
                tempList.addAll(applyIds.stream().map(promotionCacheService::getCatgoryDTO).filter(Objects::nonNull).map(CouponSpuScopeDTO::build).collect(Collectors.toList()));
            } else if (CouponSpuScopeEnum.BRAND.getScope().equals(spuCope)) {
                // 品牌券
                tempList.addAll(applyIds.stream().map(promotionCacheService::getBrand).filter(Objects::nonNull).map(CouponSpuScopeDTO::build).collect(Collectors.toList()));
            } else if (CouponSpuScopeEnum.SKU.getScope().equals(spuCope)) {
                // 商品券
                tempList.addAll(applyIds.stream().map(promotionCacheService::getSkuDTO).filter(Objects::nonNull).map(item -> {
                    SpuDTO spuDTO = promotionCacheService.getSpuDTO(item.getSpuId());
                    String properties = PropertyAndValDTO.getProperties(item.getProperties());
                    return new CouponSpuScopeDTO(
                            item.getSkuId(),
                            StringUtils.format("{}{}{}",
                                    spuDTO.getSpuName(),
                                    StringUtils.isNotEmpty(properties) ? StringPool.DASH : StringPool.EMPTY,
                                    properties
                            ),
                            spuCope
                    );
                }).collect(Collectors.toList()));
            }
            // 黑名单或者白名单
            Integer whiteOrBlack = spuScopeApplyList.get(0).getWhiteOrBlack();
            tempList.forEach(item -> {
                item.setWhiteOrBlack(whiteOrBlack);
            });
            alias.addAll(tempList);
        });
        return alias;
    }

    @Override
    public List<CouponReceiveScopeDTO> getApplyReceiveScopeList(PrmCouponTemplate couponTemplate) {
        // 全部不需要处理
        List<CouponReceiveScopeDTO> alias = new ArrayList<>();
        // 多具体使用范围
        // 获取使用范围限定列表
        List<PrmCouponScopeApply> scopeApplyList = couponScopeApplyService.getCouponReceiveScopeApplyByCouponTemplateId(couponTemplate.getCouponTemplateId());
        // 商品具体使用范围
        scopeApplyList.stream().collect(Collectors.groupingBy(PrmCouponScopeApply::getReceiveScope)).forEach((receiveScope, spuScopeApplyList) -> {
            ArrayList<CouponReceiveScopeDTO> tempList = new ArrayList<>();
            List<Long> applyIds = spuScopeApplyList.stream().map(PrmCouponScopeApply::getApplyId).distinct().collect(Collectors.toList());
            // 加载具体使用范围
            if (CouponReceiveScope.CHANNEL.getScope().equals(receiveScope)) {
                // 渠道
                tempList.addAll(applyIds.stream().map(promotionCacheService::getChannelDTO).filter(Objects::nonNull).map(CouponReceiveScopeDTO::build).collect(Collectors.toList()));
            } else if (CouponReceiveScope.AREA.getScope().equals(receiveScope)) {
                // 区域
                tempList.addAll(applyIds.stream().map(promotionCacheService::getAreaDTO).filter(Objects::nonNull).map(CouponReceiveScopeDTO::build).collect(Collectors.toList()));
            } else if (CouponReceiveScope.BRANCH.getScope().equals(receiveScope)) {
                // 门店
                tempList.addAll(applyIds.stream().map(promotionCacheService::getBranchDTO).filter(Objects::nonNull).map(item -> new CouponReceiveScopeDTO(item.getBranchId(), item.getBranchName(), CouponReceiveScope.BRANCH.getScope())).collect(Collectors.toList()));
            }
            // 黑名单或者白名单
            Integer whiteOrBlack = spuScopeApplyList.get(0).getWhiteOrBlack();
            tempList.forEach(item -> {
                item.setWhiteOrBlack(whiteOrBlack);
            });
            alias.addAll(tempList);
        });
        return alias;
    }

    /**
     * 刷新优惠券缓存
     *
     * @param couponTemplateId 优惠券ID
     */
    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_COUPON_STOCK_REFRESH, condition = "#couponTemplateId")
    public void refreshStock(Long couponTemplateId) {
        PrmCouponTemplate prmCouponTemplate = prmCouponTemplateMapper.selectById(couponTemplateId);
        // 如果键不存在并且有数量,并且缓存里面没有库存键 则去初始化库存
        if (Objects.nonNull(prmCouponTemplate.getCouponQty()) && prmCouponTemplate.getCouponQty() > NumberPool.INT_ZERO && !redisService.hasKey(RedisCouponConstants.getStockKey(prmCouponTemplate.getCouponTemplateId()))) {
            String stockUsedKey = RedisCouponConstants.getStockUsedKey(couponTemplateId);
            String stockKey = RedisCouponConstants.getStockKey(couponTemplateId);
            if (!redisService.hasKey(stockKey)) {
                // 如果没有基础key则需要初始化, 总库存, 和已经领取库存
                // 一次发布是没有库存 和 已发布优惠券过期以后是没有库存的
                redisService.setCacheObject(stockKey, prmCouponTemplate.getCouponQty(), prmCouponTemplate.expireTimes(), TimeUnit.SECONDS);
                // 更新已领取库存
                redisService.setCacheObject(stockUsedKey, Objects.nonNull(prmCouponTemplate.getReceiveCount()) ? prmCouponTemplate.getReceiveCount() : NumberPool.INT_ZERO, prmCouponTemplate.expireTimes(), TimeUnit.SECONDS);
            } else {
                // 如果已经存在了那么就无需更新已领数据了, 只需要更新库存数
                redisService.setCacheObject(stockKey, prmCouponTemplate.getCouponQty(), prmCouponTemplate.expireTimes(), TimeUnit.SECONDS);
                // 设置key过期时间,防止优惠券时间延长
                redisService.expire(stockUsedKey, prmCouponTemplate.expireTimes(), TimeUnit.SECONDS);
            }
        }
    }

    @Override
    public CouponTemplateDTO getPrmCouponTemplateCacheDTO(Long couponTemplateId) {
        CouponTemplateDTO couponTemplate = CouponTemplateConvert.INSTANCE.convertDTO(prmCouponTemplateMapper.selectById(couponTemplateId));
        couponTemplate.setRepeatRule(PrmCouponTemplateReturnRuleConvert.INSTANCE.convertDTO(repeatRuleService.getPrmCouponTemplateRepeatRuleByTemplateId(couponTemplateId)));
        // 优惠券模板多入驻商
        List<PrmCouponTemplateSupplier> templateSupplierList = templateSupplierService.selectByCouponTemplateId(couponTemplateId);
        if (CollectionUtils.isNotEmpty(templateSupplierList)) {
            couponTemplate.setSupplierIdList(templateSupplierList.stream().filter(Objects::nonNull).map(PrmCouponTemplateSupplier::getSupplierId).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        return couponTemplate;
    }

    /**
     * 下架
     *
     * @param couponTemplateId 优惠券模版ID
     */
    @Override
    public void disable(Long couponTemplateId) {
        PrmCouponTemplate update = new PrmCouponTemplate();
        update.setCouponTemplateId(couponTemplateId);
        update.setStatus(NumberPool.INT_TWO);
        prmCouponTemplateMapper.updateById(update);
    }

    /**
     * 发布
     *
     * @param couponTemplateId 优惠券模版ID
     */
    @Override
    public void enable(Long couponTemplateId) {
        PrmCouponTemplate couponTemplate = prmCouponTemplateMapper.selectById(couponTemplateId);
        //判断优惠券活动是否是注册发券
        if (CouponReceiveType.REGISTER.getType().equals(couponTemplate.getReceiveType())) {
            // 判断当前是否有有效的注册优惠券活动
            if (ToolUtil.isNotEmpty(prmCouponTemplateMapper.selectValidListBySysCodeAndReceiveType(couponTemplate.getSysCode(), couponTemplate.getFuncScope(), CouponReceiveType.REGISTER.getType()))) {
                throw exception(COUPON_ACTIVITY_EXIST);
            }
        }
        PrmCouponTemplate update = new PrmCouponTemplate();
        update.setCouponTemplateId(couponTemplateId);
        update.setStatus(NumberPool.INT_ZERO);
        if (Objects.nonNull(couponTemplate.getExpirationDateEnd())) {
            update.setExpirationDateEnd(couponTemplate.getExpirationDateEnd());
        } else {
            // 使用优惠券活动有效期 + 可用天数, 设置为过期时间
            update.setExpirationDateEnd(DateUtil.offsetDay(couponTemplate.getTemplateEndDate(), couponTemplate.getExpireDays()));
        }
        prmCouponTemplateMapper.updateById(update);
    }

    /**
     * 获取应统计优惠券列表
     *
     * @param minId 最小键ID
     * @return
     */
    @Override
    public List<CouponTemplateDTO> getTotalStockCouponTemplateList(Long minId) {
        return BeanUtil.copyToList(prmCouponTemplateMapper.selectTotalStockCouponTemplateList(minId), CouponTemplateDTO.class);
    }

    /**
     * 更新优惠券统计数据
     *
     * @param receiveCount     已领取优惠券数量
     * @param couponTemplateId 优惠券模版ID
     */
    @Override
    public void updateCouponTemplateTotal(Integer receiveCount, Long couponTemplateId) {
        PrmCouponTemplate update = new PrmCouponTemplate();
        update.setReceiveCount(receiveCount);
        update.setCouponTemplateId(couponTemplateId);
        prmCouponTemplateMapper.updateById(update);
    }

    @Override
    public List<PrmCouponTemplate> selectValidListBySysCodeAndReceiveType(Long sysCode, Integer couponReceiveType) {
        return prmCouponTemplateMapper.selectValidListBySysCodeAndReceiveType(sysCode, couponReceiveType);
    }

    @Override
    public List<SpuScopeOrReceiveScopeVO> getSpuScopeOrReceiveScope(Long couponTemplateId, boolean isReceive) {
        List<SpuScopeOrReceiveScopeVO> spuScopeOrReceiveScopeVOList = new ArrayList<>();
        List<PrmCouponScopeApply> scopeApplyList = new ArrayList<>();
        Map<Integer, List<PrmCouponScopeApply>> groupedBySpuScope = new HashMap<>();
        if (isReceive) {
            scopeApplyList = couponScopeApplyService.getCouponReceiveScopeApplyByCouponTemplateId(couponTemplateId);
            groupedBySpuScope = scopeApplyList.stream()
                    .collect(Collectors.groupingBy(PrmCouponScopeApply::getReceiveScope));
        } else {
            scopeApplyList = couponScopeApplyService.getCouponSpuScopeApplyByCouponTemplateId(couponTemplateId);
            groupedBySpuScope = scopeApplyList.stream()
                    .collect(Collectors.groupingBy(PrmCouponScopeApply::getSpuScope));
        }

        // 按 spuScope 字段分组

        // 转换为所需的格式
        spuScopeOrReceiveScopeVOList = groupedBySpuScope.entrySet().stream()
                .map(entry -> {
                    List<PrmCouponScopeApply> group = entry.getValue();
                    return SpuScopeOrReceiveScopeVO.builder()
                            .applyIds(group.stream().map(PrmCouponScopeApply::getApplyId).map(Object::toString).collect(Collectors.joining(",")))
                            .type(entry.getKey())
                            .whiteOrBlack(group.get(0).getWhiteOrBlack())
                            .build();
                })
                .collect(Collectors.toList());

        return spuScopeOrReceiveScopeVOList;
    }

    @Override
    public List<Long> getSupplierIds(Long couponTemplateId) {
        List<PrmCouponTemplateSupplier> templateSupplierList = templateSupplierService.selectByCouponTemplateId(couponTemplateId);
        if (CollectionUtils.isEmpty(templateSupplierList)) {
            return new ArrayList<>();
        }
        return templateSupplierList.stream().filter(Objects::nonNull).map(PrmCouponTemplateSupplier::getSupplierId).distinct().collect(Collectors.toList());
    }

    /**
     * 刷新针对用户领取的优惠券缓存
     *
     * @param couponTemplate
     */
    private void refreshUserCouponCache(PrmCouponTemplate couponTemplate) {
        // 只加载用户领取优惠券
        if (!CouponReceiveType.isUser(couponTemplate.getReceiveType())) {
            return;
        }
        // 筛选目前整个平台可领取优惠券, 在有效期内的
        // 平台用户领取优惠券缓存是永久性的
        List<PrmCouponTemplate> prmCouponTemplates = prmCouponTemplateMapper.selectValidListBySupplierAndReceiveType(couponTemplate.getSupplierId(),
                CouponReceiveType.NORMAL.getType()
        );
        ArrayList<CouponNormalCacheDTO> normalCacheDTOS = new ArrayList<>();
        for (PrmCouponTemplate template : prmCouponTemplates) {
            normalCacheDTOS.add(CouponTemplateConvert.INSTANCE.convertNormal(template));
            // 如果键不存在并且有数量, 则去初始化库存
            if (Objects.nonNull(template.getCouponQty()) && template.getCouponQty() > NumberPool.INT_ZERO && !redisService.hasKey(RedisCouponConstants.getStockKey(template.getCouponTemplateId()))) {
                // 写入优惠券库存
                shelf().refreshStock(template.getCouponTemplateId());
            }
        }
        // 重新写入优惠券 1:1 缓存
        couponTemplateCacheBean.getCouponTemplateCache().put(couponTemplate.getCouponTemplateId(), getPrmCouponTemplateCacheDTO(couponTemplate.getCouponTemplateId()));
        // 写入优惠券库存
        shelf().refreshStock(couponTemplate.getCouponTemplateId());
        // 设置具体使用范围
        couponSpuScopeCache.put(couponTemplate.getCouponTemplateId(), getApplySpuScopeList(couponTemplate));
        // 设置具体领取范围
        couponReceiveScopeCache.put(couponTemplate.getCouponTemplateId(), getApplyReceiveScopeList(couponTemplate));
        // 如果没有用户可领取优惠券, 那就移除缓存
        String cacheKey = RedisCouponConstants.getSupplierCouponList(couponTemplate.getSupplierId());
        redisService.deleteObject(cacheKey);
        if (!normalCacheDTOS.isEmpty()) {
            redisService.setCacheList(cacheKey, normalCacheDTOS);
        }
    }

    /**
     * 数据验证
     *
     * @param createReqVO
     */
    private void validate(PrmCouponTemplateSaveReqVO createReqVO) {
        // 验证模版有效期
        if (createReqVO.getTemplateStartDate().getTime() > createReqVO.getTemplateEndDate().getTime()) {
            // 有效时间大于结束时间
            throw exception(COUPON_TIME01);
        }
        // 优惠券类型
        // 0-满减券  1-折扣券
        if (createReqVO.getDiscountType() == NumberPool.INT_ONE) {
            // 折扣券
            if (Objects.isNull(createReqVO.getDiscountPercent())) {
                throw exception(PARAMS_DEFICIENCY01);
            }
            // 折扣力度必须大于0 小于 10
            if (NumberUtil.isGreater(BigDecimal.ZERO, createReqVO.getDiscountPercent())) {
                throw exception(PARAMS_DEFICIENCY01);
            }
            if (NumberUtil.isGreater(createReqVO.getDiscountPercent(), new BigDecimal(10))) {
                throw exception(PARAMS_DEFICIENCY01);
            }
            // 最高折扣金额小于零
            if (Objects.nonNull(createReqVO.getDiscountLimitAmt()) && NumberUtil.isGreater(BigDecimal.ZERO, createReqVO.getDiscountLimitAmt())) {
                throw exception(PARAMS_DEFICIENCY02);
            }
            // 同类型优惠券是否排它--只能为是（1）
            if (!NumberUtil.equals(createReqVO.getSameTypeExcludable(), NumberPool.INT_ONE)) {
                throw exception(PARAMS_DEFICIENCY03);
            }
        }
    }

    public PrmCouponTemplateServiceImpl shelf() {
        return SpringUtils.getAopProxy(this);
    }

    private void saveApplyIds(List<SpuScopeOrReceiveScopeVO> scopeList, Long couponTemplateId, boolean isSpuScope) {
        for (SpuScopeOrReceiveScopeVO scope : scopeList) {
            Integer spuType = isSpuScope ? scope.getType() : null;
            Integer receiveType = isSpuScope ? null : scope.getType();
            couponScopeApplyService.saveApplyId(scope.getApplyIds(), couponTemplateId, spuType, receiveType, scope.getWhiteOrBlack());
        }
    }
}
