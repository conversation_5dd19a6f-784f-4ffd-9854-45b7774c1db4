package com.zksr.promotion.convert.rule;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.FdRuleDTO;
import com.zksr.promotion.controller.rule.dto.PrmFdRuleDTO;
import com.zksr.promotion.domain.PrmFdRule;
import com.zksr.promotion.controller.rule.vo.PrmFdRuleRespVO;
import com.zksr.promotion.controller.rule.vo.PrmFdRuleSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 满减活动规则 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-13
*/
@Mapper
public interface PrmFdRuleConvert {

    PrmFdRuleConvert INSTANCE = Mappers.getMapper(PrmFdRuleConvert.class);

    PrmFdRuleRespVO convert(PrmFdRule prmFdRule);

    PrmFdRule convert(PrmFdRuleSaveReqVO prmFdRuleSaveReq);

    PageResult<PrmFdRuleRespVO> convertPage(PageResult<PrmFdRule> prmFdRulePage);

    List<FdRuleDTO> convertListDTO(List<PrmFdRule> prmFdRules);

}