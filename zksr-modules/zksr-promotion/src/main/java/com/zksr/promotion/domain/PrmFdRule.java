package com.zksr.promotion.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 满减活动规则对象 prm_fd_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@TableName(value = "prm_fd_rule")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmFdRule extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 满减活动规则id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long fdRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 满减触发金额 */
    @Excel(name = "满减触发金额")
    private BigDecimal fullAmt;

    /** 满减触发数量 */
    @Excel(name = "满减触发数量")
    private Integer fullQty;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    private BigDecimal discountAmt;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    private Integer status;

}
