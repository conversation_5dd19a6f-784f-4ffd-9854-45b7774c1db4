package com.zksr.promotion.controller.rule.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.product.api.combine.vo.SpuCombineSaveReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/1/4 10:54
 * @注释
 */
@Data
@ApiModel("组合促销活动详情对象")
public class PrmCbActivityRespVo extends PrmActivityRespVO {

    @ApiModelProperty("门店列表")
    private List<PrmActivityWhiteOrBlackVO> branchIdList;

    @ApiModelProperty("渠道列表")
    private List<PrmActivityWhiteOrBlackVO> channelIdList;

    @ApiModelProperty("上架类别")
    private Long classId;

    @ApiModelProperty("上架城市")
    private Long areaId;

    @ApiModelProperty("组合促销商品信息")
    private SpuCombineSaveReqVO spuCombineSaveReqVO;
}
