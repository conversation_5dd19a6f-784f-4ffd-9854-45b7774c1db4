package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.domain.PrmCouponScopeApply;
import com.zksr.promotion.controller.apply.vo.PrmCouponScopeApplyPageReqVO;
import com.zksr.promotion.controller.apply.vo.PrmCouponScopeApplySaveReqVO;
import com.zksr.promotion.domain.PrmCouponTemplate;

import java.util.List;

/**
 * 优惠券关联的使用范围, 领取范围中间Service接口
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
public interface IPrmCouponScopeApplyService {

    /**
     * 新增优惠券关联的使用范围, 领取范围中间
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmCouponScopeApply(@Valid PrmCouponScopeApplySaveReqVO createReqVO);

    /**
     * 修改优惠券关联的使用范围, 领取范围中间
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmCouponScopeApply(@Valid PrmCouponScopeApplySaveReqVO updateReqVO);

    /**
     * 删除优惠券关联的使用范围, 领取范围中间
     *
     * @param scopeApplyId ${pkColumn.columnComment}
     */
    public void deletePrmCouponScopeApply(Long scopeApplyId);

    /**
     * 批量删除优惠券关联的使用范围, 领取范围中间
     *
     * @param scopeApplyIds 需要删除的优惠券关联的使用范围, 领取范围中间主键集合
     * @return 结果
     */
    public void deletePrmCouponScopeApplyByScopeApplyIds(Long[] scopeApplyIds);

    /**
     * 获得优惠券关联的使用范围, 领取范围中间
     *
     * @param scopeApplyId ${pkColumn.columnComment}
     * @return 优惠券关联的使用范围, 领取范围中间
     */
    public PrmCouponScopeApply getPrmCouponScopeApply(Long scopeApplyId);

    /**
     * 获得优惠券关联的使用范围, 领取范围中间分页
     *
     * @param pageReqVO 分页查询
     * @return 优惠券关联的使用范围, 领取范围中间分页
     */
    PageResult<PrmCouponScopeApply> getPrmCouponScopeApplyPage(PrmCouponScopeApplyPageReqVO pageReqVO);

    /**
     * 保存优惠券使用领取范围具体值
     * @param spuScopeApplyIds
     * @param couponTemplateId
     * @param spuScope
     * @param receiveScope
     * @param whiteOrBlack
     */
    void saveApplyId(String spuScopeApplyIds, Long couponTemplateId, Integer spuScope, Integer receiveScope,Integer whiteOrBlack);

    /**
     * 删除优惠券绑定使用领取具体范围
     * @param couponTemplateId
     */
    void deleteByCouponTemplateId(Long couponTemplateId);

    /**
     * 在具体使用范围内有效的优惠券
     * @param applyId   具体范围ID
     * @param spuScope  范围类型
     * @return
     */
    List<PrmCouponTemplate> getPrmCouponScopeApplyValidBySpuScope(Long applyId, Integer spuScope);

    /**
     * 获取优惠券绑定的使用范围
     * @param couponTemplateId
     * @return
     */
    List<PrmCouponScopeApply> getPrmCouponScopeApplyByCouponTemplateIdAndSpuScope(Long couponTemplateId, Integer spuScope);

    /**
     * 获取优惠券具体, 使用范围
     */
    List<PrmCouponScopeApply> getCouponSpuScopeApplyByCouponTemplateId(Long couponTemplateId);

    /**
     * 获取优惠券具体, 领取范围
     */
    List<PrmCouponScopeApply> getCouponReceiveScopeApplyByCouponTemplateId(Long couponTemplateId);
}
