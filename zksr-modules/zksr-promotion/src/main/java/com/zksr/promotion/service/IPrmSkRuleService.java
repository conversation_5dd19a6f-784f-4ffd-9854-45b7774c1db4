package com.zksr.promotion.service;

import javax.validation.*;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import com.zksr.promotion.controller.rule.dto.PrmSkRuleDTO;
import com.zksr.promotion.domain.PrmSkRule;
import com.zksr.promotion.controller.rule.vo.PrmSkRulePageReqVO;
import com.zksr.promotion.controller.rule.vo.PrmSkRuleSaveReqVO;
import com.zksr.promotion.domain.excel.PrmSkRuleImportExcel;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 秒杀规则Service接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
public interface IPrmSkRuleService {

    /**
     * 新增秒杀规则
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmSkRule(@Valid PrmSkRuleDTO createReqVO);

    /**
     * 编辑秒杀促销活动
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    Boolean editSkRuleInfo(PrmSkRuleDTO updateReqVO);

    /**
     * 删除秒杀规则
     *
     * @param skRuleId 秒杀规则id
     */
    public void deletePrmSkRule(Long skRuleId);

    /**
     * 批量删除秒杀规则
     *
     * @param skRuleIds 需要删除的秒杀规则主键集合
     * @return 结果
     */
    public void deletePrmSkRuleBySkRuleIds(Long[] skRuleIds);

    /**
     * 获得秒杀规则
     *
     * @param skRuleId 秒杀规则id
     * @return 秒杀规则
     */
    public PrmSkRule getPrmSkRule(Long skRuleId);

    /**
     * 获得秒杀规则分页
     *
     * @param pageReqVO 分页查询
     * @return 秒杀规则分页
     */
    PageResult<PrmSkRule> getPrmSkRulePage(PrmSkRulePageReqVO pageReqVO);

    /**
     * 获取秒杀活动详细信息
     *
     * @param activityId 活动Id
     * @return 获取秒杀活动详细信息
     */
    PrmActivityRespVO getSkRuleInfo(@RequestParam("activityId") Long activityId);

    /**
     * 导入秒杀商品
     * @param prmActivityList
     * @return
     */
    Map<String, Object> importPrmSkRuleData(List<PrmSkRuleImportExcel> prmActivityList);

}
