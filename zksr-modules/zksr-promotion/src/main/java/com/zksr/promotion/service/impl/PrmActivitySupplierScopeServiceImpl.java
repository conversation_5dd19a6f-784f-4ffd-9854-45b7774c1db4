package com.zksr.promotion.service.impl;

import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.promotion.mapper.PrmActivitySupplierScopeMapper;
import com.zksr.promotion.convert.scope.PrmActivitySupplierScopeConvert;
import com.zksr.promotion.domain.PrmActivitySupplierScope;
import com.zksr.promotion.controller.scope.vo.PrmActivitySupplierScopePageReqVO;
import com.zksr.promotion.controller.scope.vo.PrmActivitySupplierScopeSaveReqVO;
import com.zksr.promotion.service.IPrmActivitySupplierScopeService;

/**
 * 促销活动入驻商适用范围Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class PrmActivitySupplierScopeServiceImpl implements IPrmActivitySupplierScopeService {
    @Autowired
    private PrmActivitySupplierScopeMapper prmActivitySupplierScopeMapper;

    /**
     * 新增促销活动入驻商适用范围
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrmActivitySupplierScope(PrmActivitySupplierScopeSaveReqVO createReqVO) {
        // 插入
        PrmActivitySupplierScope prmActivitySupplierScope = PrmActivitySupplierScopeConvert.INSTANCE.convert(createReqVO);
        prmActivitySupplierScopeMapper.insert(prmActivitySupplierScope);
        // 返回
        return prmActivitySupplierScope.getSysCode();
    }

    /**
     * 修改促销活动入驻商适用范围
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmActivitySupplierScope(PrmActivitySupplierScopeSaveReqVO updateReqVO) {
        prmActivitySupplierScopeMapper.updateById(PrmActivitySupplierScopeConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除促销活动入驻商适用范围
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deletePrmActivitySupplierScope(Long sysCode) {
        // 删除
        prmActivitySupplierScopeMapper.deleteById(sysCode);
    }

    /**
     * 批量删除促销活动入驻商适用范围
     *
     * @param sysCodes 需要删除的促销活动入驻商适用范围主键
     * @return 结果
     */
    @Override
    public void deletePrmActivitySupplierScopeBySysCodes(Long[] sysCodes) {
        for(Long sysCode : sysCodes){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deletePrmActivitySupplierScope(sysCode);
        }
    }

    /**
     * 获得促销活动入驻商适用范围
     *
     * @param sysCode 平台商id
     * @return 促销活动入驻商适用范围
     */
    @Override
    public PrmActivitySupplierScope getPrmActivitySupplierScope(Long sysCode) {
        return prmActivitySupplierScopeMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrmActivitySupplierScope> getPrmActivitySupplierScopePage(PrmActivitySupplierScopePageReqVO pageReqVO) {
        return prmActivitySupplierScopeMapper.selectPage(pageReqVO);
    }


    // TODO 待办：请将下面的错误码复制到 com.zksr.promotion.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 促销活动入驻商适用范围 TODO 补充编号 ==========
    // ErrorCode PRM_ACTIVITY_SUPPLIER_SCOPE_NOT_EXISTS = new ErrorCode(TODO 补充编号, "促销活动入驻商适用范围不存在");


}
