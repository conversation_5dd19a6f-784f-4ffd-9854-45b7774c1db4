package com.zksr.promotion.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.promotion.domain.PrmActivityChannelScope;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmActivitySupplierScope;
import com.zksr.promotion.controller.scope.vo.PrmActivitySupplierScopePageReqVO;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 促销活动入驻商适用范围Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Mapper
public interface PrmActivitySupplierScopeMapper extends BaseMapperX<PrmActivitySupplierScope> {
    default PageResult<PrmActivitySupplierScope> selectPage(PrmActivitySupplierScopePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmActivitySupplierScope>()
                    .eqIfPresent(PrmActivitySupplierScope::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmActivitySupplierScope::getActivityId, reqVO.getActivityId())
                    .eqIfPresent(PrmActivitySupplierScope::getSupplierId, reqVO.getSupplierId())
                    .eqIfPresent(PrmActivitySupplierScope::getWhiteOrBlack, reqVO.getWhiteOrBlack())
                .orderByDesc(PrmActivitySupplierScope::getSysCode));
    }

    void deleteSupplierByActivityId(Long activityId);

    default List<Long> getSupplierByActivityId(Long activityId) {
        return selectList(
                new LambdaQueryWrapperX<PrmActivitySupplierScope>()
                        .select(PrmActivitySupplierScope::getSupplierId)
                        .eq(PrmActivitySupplierScope::getActivityId, activityId)
        ).stream().map(PrmActivitySupplierScope::getSupplierId).collect(Collectors.toList());

    }

    default List<Long> selectActivitiesBySupplierId(Long supplierId) {
        return selectList(
                new LambdaQueryWrapperX<PrmActivitySupplierScope>()
                        .select(PrmActivitySupplierScope::getActivityId)
                        .eq(PrmActivitySupplierScope::getSupplierId, supplierId)
        ).stream().map(PrmActivitySupplierScope::getActivityId).collect(Collectors.toList());
    }

    default List<PrmActivitySupplierScope> selectByActivityId(Long activityId) {
        return selectList(new LambdaQueryWrapper<PrmActivitySupplierScope>().eq(PrmActivitySupplierScope::getActivityId, activityId));
    }
}
