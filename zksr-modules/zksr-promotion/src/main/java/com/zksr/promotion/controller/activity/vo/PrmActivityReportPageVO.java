package com.zksr.promotion.controller.activity.vo;

import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("促销活动 - 促销报表查询分页实体 Request VO")
public class PrmActivityReportPageVO extends PageParam {

    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动类型")
    private String activityType;

    @ApiModelProperty(value = "开始时间(年-月-日)")
    private String startDate;

    @ApiModelProperty(value = "结束时间(年-月-日)")
    private String endDate;

    @ApiModelProperty(value = "活动状态")
    private Integer activityStatus;

    @ApiModelProperty(value = "1-全国商品可用（平台商设定）2-本地商品可用（运营商设定）")
    private Integer funcScope;
}
