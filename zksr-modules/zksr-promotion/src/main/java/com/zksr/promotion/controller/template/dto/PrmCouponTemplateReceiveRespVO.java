package com.zksr.promotion.controller.template.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 优惠劵模板 - 门店领取列表
 */
@Data
@Accessors(chain = true)
@ApiModel("优惠券模版拓展门店领取统计 - PrmCouponTemplateExtendReceiveRespVO")
public class PrmCouponTemplateReceiveRespVO {

    @ApiModelProperty(value = "优惠券模版ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long couponTemplateId;

    @ApiModelProperty(value = "客户ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "领取数量")
    private Long receiveCount;

    @ApiModelProperty(value = "使用数量")
    private Long useCount;

    @ApiModelProperty(value = "剩余数量")
    private Long surplusCount;

    @ApiModelProperty(value = "过期数量")
    private Long obsoleteCount;

    @ApiModelProperty(value = "使用优惠劵下单总商品数量")
    private Long orderNumSum;

    @ApiModelProperty(value = "使用优惠劵下单总金额")
    private BigDecimal orderAmtSum;
}
