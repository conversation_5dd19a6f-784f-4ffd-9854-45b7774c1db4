package com.zksr.promotion.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmLiveOrder;
import com.zksr.promotion.controller.order.vo.PrmLiveOrderPageReqVO;


/**
 * 直播订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Mapper
public interface PrmLiveOrderMapper extends BaseMapperX<PrmLiveOrder> {
    default PageResult<PrmLiveOrder> selectPage(PrmLiveOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmLiveOrder>()
                    .eqIfPresent(PrmLiveOrder::getId, reqVO.getId())
                    .eqIfPresent(PrmLiveOrder::getOrderNo, reqVO.getOrderNo())
                    .eqIfPresent(PrmLiveOrder::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmLiveOrder::getActivityId, reqVO.getActivityId())
                    .likeIfPresent(PrmLiveOrder::getName, reqVO.getName())
                    .eqIfPresent(PrmLiveOrder::getLiveProductId, reqVO.getLiveProductId())
                    .eqIfPresent(PrmLiveOrder::getSkuId, reqVO.getSkuId())
                    .eqIfPresent(PrmLiveOrder::getSpuId, reqVO.getSpuId())
                    .eqIfPresent(PrmLiveOrder::getSpuNo, reqVO.getSpuNo())
                    .likeIfPresent(PrmLiveOrder::getSpuName, reqVO.getSpuName())
                    .eqIfPresent(PrmLiveOrder::getLivePrice, reqVO.getLivePrice())
                    .eqIfPresent(PrmLiveOrder::getUnit, reqVO.getUnit())
                    .likeIfPresent(PrmLiveOrder::getUnitName, reqVO.getUnitName())
                    .eqIfPresent(PrmLiveOrder::getOrderQty, reqVO.getOrderQty())
                    .likeIfPresent(PrmLiveOrder::getBranchName, reqVO.getBranchName())
                    .likeIfPresent(PrmLiveOrder::getContactName, reqVO.getContactName())
                    .eqIfPresent(PrmLiveOrder::getContactPhone, reqVO.getContactPhone())
                    .eqIfPresent(PrmLiveOrder::getBranchAddr, reqVO.getBranchAddr())
                    .eqIfPresent(PrmLiveOrder::getRemark, reqVO.getRemark())
                    .eqIfPresent(PrmLiveOrder::getDelFlag, reqVO.getDelFlag())
                    .eqIfPresent(PrmLiveOrder::getVersion, reqVO.getVersion())
                .orderByDesc(PrmLiveOrder::getId));
    }
}
