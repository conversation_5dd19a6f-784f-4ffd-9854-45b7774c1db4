package com.zksr.promotion.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.promotion.domain.PrmActivityBranchScope;
import com.zksr.promotion.controller.scope.vo.PrmActivityBranchScopePageReqVO;
import com.zksr.promotion.controller.scope.vo.PrmActivityBranchScopeSaveReqVO;

/**
 * 促销活动门店适用范围Service接口
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
public interface IPrmActivityBranchScopeService {

    /**
     * 新增促销活动门店适用范围
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrmActivityBranchScope(@Valid PrmActivityBranchScopeSaveReqVO createReqVO);

    /**
     * 修改促销活动门店适用范围
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrmActivityBranchScope(@Valid PrmActivityBranchScopeSaveReqVO updateReqVO);

    /**
     * 删除促销活动门店适用范围
     *
     * @param sysCode 平台商id
     */
    public void deletePrmActivityBranchScope(Long sysCode);

    /**
     * 批量删除促销活动门店适用范围
     *
     * @param sysCodes 需要删除的促销活动门店适用范围主键集合
     * @return 结果
     */
    public void deletePrmActivityBranchScopeBySysCodes(Long[] sysCodes);

    /**
     * 获得促销活动门店适用范围
     *
     * @param sysCode 平台商id
     * @return 促销活动门店适用范围
     */
    public PrmActivityBranchScope getPrmActivityBranchScope(Long sysCode);

    /**
     * 获得促销活动门店适用范围分页
     *
     * @param pageReqVO 分页查询
     * @return 促销活动门店适用范围分页
     */
    PageResult<PrmActivityBranchScope> getPrmActivityBranchScopePage(PrmActivityBranchScopePageReqVO pageReqVO);

}
