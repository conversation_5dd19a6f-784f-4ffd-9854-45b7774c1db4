package com.zksr.promotion.controller.rule.vo;

import java.math.BigDecimal;
import java.util.List;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 秒杀规则对象 prm_sk_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@ApiModel("秒杀规则 - prm_sk_rule分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmSkRulePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 秒杀规则id */
    @ApiModelProperty(value = "秒杀价")
    private Long skRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @ApiModelProperty(value = "活动id", required = true)
    private Long activityId;

    /** SPU id */
    @Excel(name = "SPU id")
    @ApiModelProperty(value = "SPU id", required = true)
    private Long spuId;

    /** SKU id */
    @Excel(name = "SKU id")
    @ApiModelProperty(value = "SKU id", required = true)
    private Long skuId;

    /** 单次限量 */
    @Excel(name = "单次限量")
    @ApiModelProperty(value = "单次限量")
    private Integer onceLimit;

    /** 秒杀库存 */
    @Excel(name = "秒杀库存")
    @ApiModelProperty(value = "秒杀库存", required = true)
    private Integer seckillStock;

    /** 秒杀价 */
    @Excel(name = "秒杀价")
    @ApiModelProperty(value = "秒杀价", required = true)
    private BigDecimal seckillPrice;

    /** skuId集合 */
    @Excel(name = "skuId集合")
    @ApiModelProperty(value = "skuId集合", required = true)
    private List<Long> skuIds;


}
