package com.zksr.promotion.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.github.pagehelper.Page;
import com.google.common.collect.Maps;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.*;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.areaItem.AreaItemApi;
import com.zksr.product.api.areaItem.vo.ApiAreaItemPageReqVO;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.api.content.ProductContentApi;
import com.zksr.product.api.content.dto.ProductContentDTO;
import com.zksr.product.api.content.dto.ProductContentPageReqDTO;
import com.zksr.product.api.materialApply.dto.MaterialApplyDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.spu.vo.PrdtSpuNotItemPageReqVo;
import com.zksr.product.api.supplierItem.SupplierItemApi;
import com.zksr.product.api.supplierItem.vo.PrdtSupplierItemPageRespVO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.controller.activity.dto.PrmActivityReportRespDTO;
import com.zksr.promotion.controller.activity.dto.SkOrSpRuleCheckDTO;
import com.zksr.promotion.controller.activity.vo.*;
import com.zksr.promotion.controller.rule.dto.PrmFdRuleDTO;
import com.zksr.promotion.controller.rule.vo.PrmSkRuleSaveReqVO;
import com.zksr.promotion.controller.rule.vo.PrmSpRuleSaveReqVO;
import com.zksr.promotion.convert.activity.PrmActivityConvert;
import com.zksr.promotion.convert.rule.*;
import com.zksr.promotion.convert.scope.*;
import com.zksr.promotion.domain.PrmActivity;
import com.zksr.promotion.domain.PrmActivitySpuScope;
import com.zksr.promotion.domain.PrmActivitySupplierScope;
import com.zksr.promotion.mapper.*;
import com.zksr.promotion.service.IPrmActivityCommonService;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPromotionCacheService;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.ActivityOrderTotalDTO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.PrmConstants.*;
import static com.zksr.common.core.constant.SystemConstants.FUNC_SCOPE_DC;
import static com.zksr.common.core.constant.SystemConstants.FUNC_SCOPE_PARTNER;
import static com.zksr.product.constant.ProductConstant.*;

/**
 * 促销活动Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Service
@SuppressWarnings("all")
public class PrmActivityServiceImpl implements IPrmActivityService {
    @Autowired
    private PrmActivityMapper prmActivityMapper;

    @Autowired
    private PrmActivityCityScopeMapper cityScopeMapper;

    @Autowired
    private PrmActivityChannelScopeMapper channelScopeMapper;

    @Autowired
    private PrmActivitySupplierScopeMapper supplierScopeMapper;

    @Autowired
    private PrmActivityBranchScopeMapper activityBranchScopeMapper;

    @Autowired
    private PrmFdRuleMapper prmFdRuleMapper;

    @Autowired
    private PrmFgRuleMapper fgRuleMapper;

    @Autowired
    private PrmBgRuleMapper bgRuleMapper;

    @Autowired
    private PrmSkRuleMapper skRuleMapper;

    @Autowired
    private PrmSpRuleMapper spRuleMapper;

    @Autowired
    private PrmCbRuleMapper cbRuleMapper;

    @Autowired
    private AreaItemApi remoteAreaItemApi;

    @Autowired
    private SupplierItemApi remoteSupplierItemApi;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private PrmActivitySpuScopeMapper spuScopeMapper;

    @Autowired
    private Cache<Long, List<SupplierActivityDTO>> supplierActivityCache;

    @Resource
    private SpuApi remoteSpuApi;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Autowired
    private OrderApi orderApi;

    @Autowired
    private ProductContentApi productContentApi;

    @Autowired
    private PrmActivitySupplierScopeMapper prmActivitySupplierScopeMapper;

    /**
     * 新增促销活动
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrmActivity(PrmActivitySaveReqVO createReqVO) {
        createReqVO.setActivityId(null);
        // 插入
        PrmActivity prmActivity = PrmActivityConvert.INSTANCE.convert(createReqVO);
        prmActivityMapper.insert(prmActivity);
        // 返回
        return prmActivity.getActivityId();
    }

    /**
     * 修改促销活动
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrmActivity(PrmActivitySaveReqVO updateReqVO) {
        prmActivityMapper.updateById(PrmActivityConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除促销活动
     *
     * @param activityId 活动id
     */
    @Override
    public void deletePrmActivity(Long activityId) {
        // 删除
        prmActivityMapper.deleteById(activityId);
    }

    /**
     * 批量删除促销活动
     *
     * @param activityIds 需要删除的促销活动主键
     * @return 结果
     */
    @Override
    public void deletePrmActivityByActivityIds(Long[] activityIds) {
        for (Long activityId : activityIds) {
            this.deletePrmActivity(activityId);
        }
    }

    /**
     * 获得促销活动
     *
     * @param activityId 活动id
     * @return 促销活动
     */
    @Override
    public PrmActivity getPrmActivity(Long activityId) {
        return prmActivityMapper.selectById(activityId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrmActivityRespVO> getPrmActivityPage(PrmActivityPageReqVO pageReqVO) {
        Page<PrmActivityPageReqVO> page = PageUtils.startPage(pageReqVO);
        PageResult<PrmActivityRespVO> pageResult = PrmActivityConvert.INSTANCE.convertPage(new PageResult<>(prmActivityMapper.selectListPage(pageReqVO), page.getTotal()));

        //获取素材信息
        List<Long> activityIdList = pageResult.getList().stream().map(PrmActivityRespVO::getActivityId).collect(Collectors.toList());

        //获取生效中的素材应用信息
        List<MaterialApplyDTO> applyList = new ArrayList<>();
        if (!activityIdList.isEmpty()) {
            applyList = prmActivityCommonService().getByMaterialApplyByApplyIds(activityIdList);
        }

        Map<Long, MaterialApplyDTO> applyMap = applyList.stream().collect(Collectors.toMap(MaterialApplyDTO::getApplyId, x -> x));

        //循环处理列表信息
        pageResult.getList().forEach(x -> {
            //已失效的促销列表：条件： 已启用并且活动结束时间小于或等于当前时间
            if (PRM_STATUS_1.equals(x.getPrmStatus()) && x.getEndTime().before(DateUtils.getNowDate())) {
                x.setPrmStatus(PRM_STATUS_3);
            }

            // 设置入驻商名称
            SupplierDTO supplierDTO = promotionCacheService.getSupplierDTO(x.getSupplierId());
            if (Objects.nonNull(supplierDTO)) {
                x.setSupplierName(supplierDTO.getSupplierName());
            }

            //设置素材信息
            MaterialApplyDTO materialApplyDTO = applyMap.get(x.getActivityId());
            if (ToolUtil.isNotEmpty(materialApplyDTO)) {
                x.setMaterialApplyId(materialApplyDTO.getMaterialApplyId());
                x.setMaterialId(materialApplyDTO.getMaterialId());
                x.setImg(materialApplyDTO.getImg());
            }

        });


        return pageResult;
    }

    @Override
    public List<SupplierActivityDTO> getSupplierActivity(Long supplierId) {
        // 查询当前活动入驻商下的所有活动
        List<Long> activityIds = prmActivitySupplierScopeMapper.selectActivitiesBySupplierId(supplierId);
        List<PrmActivity> activities = prmActivityMapper.selectByActivityIds(
                activityIds,
                PrmNoEnum.SK.getType(),
                PrmNoEnum.BG.getType(),
                PrmNoEnum.FG.getType(),
                PrmNoEnum.FD.getType(),
                PrmNoEnum.SP.getType(),
                PrmNoEnum.CB.getType()
        );
        // 兼容历史数据
        activities.addAll(prmActivityMapper.selectBySupplierId(
                supplierId,
                PrmNoEnum.SK.getType(),
                PrmNoEnum.BG.getType(),
                PrmNoEnum.FG.getType(),
                PrmNoEnum.FD.getType(),
                PrmNoEnum.SP.getType(),
                PrmNoEnum.CB.getType()
        ));
        // 去重
        activities = activities.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PrmActivity::getActivityId))), ArrayList::new));
        // 转换入驻商活动
        List<SupplierActivityDTO> supplierActivityList = PrmActivityConvert.INSTANCE.convertSupplierActivity(activities);
        supplierActivityList.forEach(supplierActivity -> {
            // 促销ID
            Long activityId = supplierActivity.getActivityId();

            // 促销类型
            String prmNo = supplierActivity.getPrmNo();

            // 城市范围
            List<ActivityCityScopeDTO> cityScopeList = PrmActivityCityScopeConvert.INSTANCE.convertListDTO(cityScopeMapper.selectByActivityId(activityId));

            // 渠道范围
            List<ActivityChannelScopeDTO> channelScopeList =
                    Objects.nonNull(supplierActivity.getChanelScopeAllFlag()) && supplierActivity.getChanelScopeAllFlag() == NumberPool.INT_ONE
                            ? PrmActivityChannelScopeConvert.INSTANCE.convert(channelScopeMapper.selectByActivityId(activityId)) : ListUtil.empty();

            // 门店范围
            List<ActivityBranchScopeDTO> branchScopeList =
                    Objects.nonNull(supplierActivity.getBranchScopeAllFlag()) && supplierActivity.getBranchScopeAllFlag() == NumberPool.INT_ONE
                            ? PrmActivityBranchScopeConvert.INSTANCE.convert(activityBranchScopeMapper.selectByActivityId(activityId)) : ListUtil.empty();

            // 满减规则
            List<FdRuleDTO> fdRuleList = PrmNoEnum.isFd(prmNo) ? PrmFdRuleConvert.INSTANCE.convertListDTO(prmFdRuleMapper.selectByActivityId(activityId)) : ListUtil.empty();

            // 满赠规则
            List<FgRuleDTO> fgRuleList = null;//PrmNoEnum.isFg(prmNo) ? PrmFgRuleConvert.INSTANCE.convertListDTO(fgRuleMapper.selectByActivityId(activityId)) : ListUtil.empty();

            // 买赠规则
            List<BgRuleDTO> bgRuleList = null;//PrmNoEnum.isBg(prmNo) ? PrmBgRuleConvert.INSTANCE.convertListDTO(bgRuleMapper.getListByActivityId(activityId)) : ListUtil.empty();

            // 秒杀规则
            List<SkRuleDTO> skRuleList = null;//PrmNoEnum.isSk(prmNo) ? PrmSkRuleConvert.INSTANCE.convertListDTO(skRuleMapper.selectByActivityId(activityId)) : ListUtil.empty();

            // 特价规则
            List<SpRuleDTO> spRuleList = null;//PrmNoEnum.isSp(prmNo) ? PrmSpRuleConvert.INSTANCE.convertListDTO(spRuleMapper.selectByActivityId(activityId)) : ListUtil.empty();

            // 因为组合促销下面只有一个规则, 所以这里直接添加进去
            List<CbRuleDTO> cbRuleList = prmActivityMapper.selectCbRuleList(activityId);

            // 设置数据
            PrmActivityConvert.INSTANCE.buildSetDTO(
                    supplierActivity,
                    cityScopeList,
                    channelScopeList,
                    branchScopeList,
                    fdRuleList,
                    fgRuleList,
                    bgRuleList,
                    skRuleList,
                    spRuleList,
                    cbRuleList
            );
        });
        return supplierActivityList;
    }

    @Override
    public PageResult<PrmActivityItemPageVo> getItemPage(PrmActivityItemPageVo pageReqVO) {
        PageResult<PrmActivityItemPageVo> pageResult = new PageResult<PrmActivityItemPageVo>();
        //校验登陆账号是平台商还是运营商
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        Integer itemType = ObjectUtil.isNull(dcId) ? PRDT_ITEM_TYPE_0 : PRDT_ITEM_TYPE_1;

        //查询分页列表
        if (PRDT_SHELF_STATUS_0.equals(pageReqVO.getShelfStatus())) {
            pageResult = getNotShelfItem(pageReqVO, itemType, pageResult);
        } else {
            pageResult = getShelfItem(pageReqVO, itemType, pageResult);
        }
        return pageResult;
    }

    /**
     * 获取上架商品信息
     *
     * @param pageReqVO
     * @return
     */
    public PageResult<PrmActivityItemPageVo> getShelfItem(PrmActivityItemPageVo pageReqVO, Integer itemType, PageResult<PrmActivityItemPageVo> pageResult) {
        if (PRDT_ITEM_TYPE_1.equals(itemType)) {
            PageResult<PrdtAreaItemPageRespVO> areaItemDTOPage = remoteAreaItemApi.getAreaItemPageByApi(PrmActivityConvert.INSTANCE.convertAreaItemPageReqVO(pageReqVO)).getCheckedData();
            if (areaItemDTOPage.getTotal() > 0) {
                //组装本地商品列表
                List<PrmActivityItemPageVo> itemList = HutoolBeanUtils.toBean(areaItemDTOPage.getList(), PrmActivityItemPageVo.class);
                List<PrmActivityItemPageVo> pageList = itemList.stream().map(x -> {
                    x.setItemId(x.getAreaItemId())
                            .setClassId(x.getAreaClassId())
                            .setClassName(x.getAreaClassName())
                            .setStock(redisStockService.getSurplusSaleQty(x.getSkuId()));
                    return x;
                }).collect(Collectors.toList());
                pageResult.setList(pageList);
            }
            pageResult.setTotal(areaItemDTOPage.getTotal());
        } else {
            PageResult<PrdtSupplierItemPageRespVO> supplierItemDTOPage = remoteSupplierItemApi.getSupplierItemPageByApi(PrmActivityConvert.INSTANCE.convertSupplierItemPageReqVO(pageReqVO)).getCheckedData();
            if (supplierItemDTOPage.getTotal() > 0) {
                //组装全国商品列表
                List<PrmActivityItemPageVo> itemList = HutoolBeanUtils.toBean(supplierItemDTOPage.getList(), PrmActivityItemPageVo.class);
                List<PrmActivityItemPageVo> pageList = itemList.stream().map(x -> {
                    x.setItemId(x.getSupplierItemId())
                            .setClassId(x.getSaleClassId())
                            .setClassName(x.getSaleClassName())
                            .setStock(redisStockService.getSurplusSaleQty(x.getSkuId()));
                    return x;
                }).collect(Collectors.toList());
                pageResult.setList(pageList);
            }
            pageResult.setTotal(supplierItemDTOPage.getTotal());
        }

        return pageResult;
    }

    /**
     * 获取未上架商品信息
     *
     * @param pageReqVO
     * @return
     */
    public PageResult<PrmActivityItemPageVo> getNotShelfItem(PrmActivityItemPageVo pageReqVO, Integer itemType, PageResult<PrmActivityItemPageVo> pageResult) {
        //设置商品类型
        pageReqVO.setItemType(itemType);
        PageResult<PrdtSpuNotItemPageReqVo> itemPage = remoteSpuApi.getSpuPageNotItemByApi(HutoolBeanUtils.toBean(pageReqVO, PrdtSpuNotItemPageReqVo.class)).getCheckedData();
        if (itemPage.getTotal() > 0) {
            List<PrmActivityItemPageVo> pageList = HutoolBeanUtils.toBean(itemPage.getList(), PrmActivityItemPageVo.class);
            pageResult.setList(pageList);
        }
        pageResult.setTotal(itemPage.getTotal());
        return pageResult;
    }

    @Override
    public List<SpRuleDTO> getActivitySpRule(Long activityId) {
        return PrmSpRuleConvert.INSTANCE.convertListDTO(spRuleMapper.selectByActivityId(activityId));
    }

    @Override
    public List<SkRuleDTO> getActivitySkRule(Long activityId) {
        return PrmSkRuleConvert.INSTANCE.convertListDTO(skRuleMapper.selectByActivityId(activityId));
    }

    @Override
    public List<ActivitySpuScopeDTO> getActivitySpuScopeList(Long activityId) {
        List<ActivitySpuScopeDTO> spuScopeDTOS = PrmActivitySpuScopeConvert.INSTANCE.convertListDTO(spuScopeMapper.selectByActivityId(activityId));
        // 为商品使用范围扩展spuId
        List<ActivitySpuScopeDTO> skuScopeList = spuScopeDTOS.stream().filter(item -> item.getApplyType() == 4L).collect(Collectors.toList());
        for (ActivitySpuScopeDTO scopeDTO : skuScopeList) {
            SkuDTO skuDTO = promotionCacheService.getSkuDTO(scopeDTO.getApplyId());
            if (Objects.nonNull(skuDTO)) {
                scopeDTO.setSpuId(skuDTO.getSpuId());
            }
        }
        return spuScopeDTOS;
    }

    @Override
    public List<ActivityBranchScopeDTO> getActivityBranchScopeList(Long activityId) {
        return PrmActivityBranchScopeConvert.INSTANCE.convert(activityBranchScopeMapper.selectByActivityId(activityId));
    }

    @Override
    public List<ActivityCityScopeDTO> getActivityCityScopeList(Long activityId) {
        return PrmActivityCityScopeConvert.INSTANCE.convertListDTO(cityScopeMapper.selectByActivityId(activityId));
    }

    @Override
    public List<ActivityChannelScopeDTO> getActivityChannelScopeList(Long activityId) {
        return PrmActivityChannelScopeConvert.INSTANCE.convert(channelScopeMapper.selectByActivityId(activityId));
    }

    @Override
    public List<ActivitySupplierScopeDTO> getActivitySupplierScopeList(Long activityId) {
        return PrmActivitySupplierScopeConvert.INSTANCE.convert(supplierScopeMapper.selectByActivityId(activityId));
    }

    /**
     * 校验活动是否可以停用
     *
     * @param isEnable
     */
    @Override
    public void checkEnable(PrmFdRuleDTO isEnable) {
        //如果活动正在进行中，则不能停用
        if (ObjectUtil.equal(NumberPool.INT_ONE, isEnable.getPrmStatus()) && DateUtil.compare(DateUtil.date(), isEnable.getEndTime()) < 0) {
            throw new ServiceException("活动已经开始了，禁止停用！");
        }
    }

    @Override
    public PrmActivityDTO getActivityDto(Long activityId) {

        PrmActivity prmActivity = prmActivityMapper.getActivityByActivityId(activityId);


        // 城市范围
        List<ActivityCityScopeDTO> cityScopeList = PrmActivityCityScopeConvert.INSTANCE.convertListDTO(cityScopeMapper.selectByActivityId(activityId));

        // 渠道范围
        List<ActivityChannelScopeDTO> channelScopeList =
                Objects.nonNull(prmActivity.getChanelScopeAllFlag()) && prmActivity.getChanelScopeAllFlag() == NumberPool.INT_ONE
                        ? PrmActivityChannelScopeConvert.INSTANCE.convert(channelScopeMapper.selectByActivityId(activityId)) : ListUtil.empty();

        // 门店范围
        List<ActivityBranchScopeDTO> branchScopeList =
                Objects.nonNull(prmActivity.getBranchScopeAllFlag()) && prmActivity.getBranchScopeAllFlag() == NumberPool.INT_ONE
                        ? PrmActivityBranchScopeConvert.INSTANCE.convert(activityBranchScopeMapper.selectByActivityId(activityId)) : ListUtil.empty();

        List<Long> spuScopeList = prmActivity.getSpuScope() != NumberPool.INT_ONE ? spuScopeMapper.selectByActivityId(activityId).stream().map(PrmActivitySpuScope::getApplyId).collect(Collectors.toList()) : ListUtil.empty();

        return PrmActivityConvert.INSTANCE.convert0(prmActivity, cityScopeList, channelScopeList, branchScopeList, spuScopeList);
    }

    @Override
    public List<FdRuleDTO> getActivityFdRule(Long activityId) {
        return PrmFdRuleConvert.INSTANCE.convertListDTO(prmFdRuleMapper.selectByActivityId(activityId));
    }

    @Override
    public List<FgRuleDTO> getActivityFgRule(Long activityId) {
        return PrmFgRuleConvert.INSTANCE.convertListDTO(fgRuleMapper.selectByActivityId(activityId));
    }

    @Override
    public List<BgRuleDTO> getActivityBgRule(Long activityId) {
        return PrmBgRuleConvert.INSTANCE.convertListDTO(bgRuleMapper.getListByActivityId(activityId));
    }

    @Override
    public List<CbRuleDTO> getActivityCbRule(Long activityId) {
        return PrmCbRuleConvert.INSTANCE.convertListDTO(cbRuleMapper.getListByActivityId(activityId));
    }

    @Override
    public synchronized void reloadSupplierActivity(Long activityId) {
        // 促销活动
        PrmActivity activity = prmActivityMapper.selectById(activityId);
        if (Objects.nonNull(activity) && (activity.getPrmStatus() == NumberPool.INT_ONE || activity.getPrmStatus() == NumberPool.INT_TWO)) {
            List<Long> supplierIds = prmActivitySupplierScopeMapper.getSupplierByActivityId(activityId);
            supplierIds.add(activity.getSupplierId());
            supplierIds.stream().filter(Objects::nonNull).distinct().forEach(supplierId -> {
                // 获取入驻商活动
                List<SupplierActivityDTO> supplierActivity = getSupplierActivity(supplierId);
                // 重新推送缓存
                supplierActivityCache.put(supplierId, supplierActivity);
            });
        }
    }

    @Override
    public PageResult<PrmActivityReportRespDTO> getActivityReportPage(PrmActivityReportPageVO pageVO) {
        Page<PrmActivityReportRespDTO> page = PageUtils.startPage(pageVO);
        List<PrmActivityReportRespDTO> list = prmActivityMapper.getActivityReport(pageVO);
        if (ToolUtil.isEmpty(list)) {
            return PageResult.result(page, list);
        }
        List<ActivityOrderTotalDTO> activityOrderTotalList = orderApi.getPrmActivityByActivityIds(list.stream().map(PrmActivityReportRespDTO::getActivityId).collect(Collectors.toList())).getCheckedData();
        Map<Long, ActivityOrderTotalDTO> activityOrderTotalMap = CollectionUtils.convertMap(activityOrderTotalList, ActivityOrderTotalDTO::getActivityId);
        list.forEach(item -> {
            ActivityOrderTotalDTO activityOrderTotalDTO = activityOrderTotalMap.get(item.getActivityId());
            if (Objects.nonNull(activityOrderTotalDTO)) {
                item.setActivityCustomerCount(activityOrderTotalDTO.getActivityCustomerCount());
                item.setActivityOrderAmtSum(activityOrderTotalDTO.getActivityOrderAmtSum());
            }
        });
        return PageResult.result(page, list);
    }

    @Override
    public List<Long> getSeckillSpuIds(String prmNo, Integer funcScope) {
        return prmActivityMapper.selectSeckillSpuIds(prmNo, funcScope);
    }

    @Override
    public List<Long> getSeckillSkuIds(String prmNo, Integer funcScope) {
        return prmActivityMapper.selectSeckillSkuIds(prmNo, funcScope);
    }

    @Override
    public Long countBySkuId(Long skuId) {
        return prmActivityMapper.selectCountBySkuId(skuId);
    }

    @Override
    public String checkActivityByItem(Long[] skuIds) {
        //促销范围
        Integer funcScope = null;

        //获取登录账号是运营商还是平台商
        String funcScop = SecurityUtils.getLoginUser().getFuncScop();
        if (FUNC_SCOPE_PARTNER.equals(funcScop)) {
            funcScope = PRM_FUNC_SCOPE_1;
        } else if (FUNC_SCOPE_DC.equals(funcScop)) {
            funcScope = PRM_FUNC_SCOPE_2;
        } else {
            throw new ServiceException("该下架商品的账号不是平台商或运营商，无权限下架商品！");
        }

        StringBuilder sb = new StringBuilder();

        //查询sku是否存在参与促销 如果参与了 组装校验信息
        for (CheckActivityItemDTO dto : prmActivityMapper.selectChcekActivityBySkuIds(skuIds, funcScope)) {
            SkuDTO skuDTO = promotionCacheService.getSkuDTO(dto.getSkuId());
            SpuDTO spuDTO = promotionCacheService.getSpuDTO(skuDTO.getSpuId());
            sb.append(StringUtils.format("商品{}已参与促销活动：{};", spuDTO.getSpuName(), dto.getActivityName()));
        }

        if (sb.length() > 0) {
            sb.append("请确认是否仍要下架商品！！！");
        }


        return sb.toString();
    }

    @Override
    public List<Long> getAreaCbSupplierIds(Long areaId) {
        return prmActivityMapper.selectAreaCbSupplierIds(areaId);
    }

    @Override
    public List<Long> getGlobalCbSupplierIds(Long sysCode) {
        return prmActivityMapper.getGlobalCbSupplierIds(sysCode);
    }

    @Override
    public List<Long> getActivityCbByStatus(Long sysCode, Integer funcScope, Integer status) {
        return cbRuleMapper.getActivityCbByStatus(sysCode, funcScope, status);
    }

    @Override
    public List<Long> getExpiredCbActivityIds() {
        return prmActivityMapper.getExpiredCbActivityIds();
    }


    /**
     * 防止依赖循环
     *
     * @return
     */
    IPrmActivityCommonService prmActivityCommonService() {
        return SpringUtils.getBean(IPrmActivityCommonService.class);
    }

    @Override
    public PageResult<PrmActivityRespVO> listActivity(PrmActivityPageReqVO pageReqVO) {
        PageResult<PrmActivityRespVO> pageResult = prmActivityMapper.selectPageAndByAreaId(pageReqVO);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(pageResult.getList())) {
            //循环处理列表信息
            pageResult.getList().stream().forEach(x -> {
                //已失效的促销列表：条件： 已启用并且活动结束时间小于或等于当前时间
                if (PRM_STATUS_1.equals(x.getPrmStatus()) && x.getEndTime().before(DateUtils.getNowDate())) {
                    x.setPrmStatus(PRM_STATUS_3);
                }
                // 参与入驻商
                Optional.ofNullable(promotionCacheService.getSupplierDTO(x.getSupplierId())).ifPresent(supplierDTO -> {
                    x.setSupplierName(supplierDTO.getSupplierName());
                });

                // 城市名称
                Optional.ofNullable(x.getAreaId()).map(promotionCacheService::getAreaDTO).ifPresent(areaDTO -> {
                    x.setAreaName(areaDTO.getAreaName());
                });

                List<ActivitySpuScopeDTO> spuScopes = Optional.ofNullable(promotionCacheService.getSpuScope(x.getActivityId())).orElse(Lists.newArrayList());
                // 活动下任意3个商品
                List<SkuPageRespVO> items = getItemsByActivityId(x, spuScopes);
                x.setItems(items);
            });
        }
        return pageResult;
    }

    @Override
    public SkOrSpRuleCheckDTO selectRepeatActivity(PrmActivitySaveReqVO activity) {
        SkOrSpRuleCheckDTO result = new SkOrSpRuleCheckDTO();

        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 设置全国还是本地活动
        activity.setFuncScope(Objects.isNull(loginUser.getDcId()) ? NumberPool.INT_ONE : NumberPool.INT_TWO);

        List<SkOrSpRuleCheckDTO> check = new ArrayList<>();

        if (ToolUtil.isNotEmpty(activity.getSkRuleList())) {
            //校验秒杀规则信息
            check = skRuleMapper.selectRepeatActivity(PrmActivityConvert.INSTANCE.convert(activity), activity.getSkRuleList().stream().map(PrmSkRuleSaveReqVO::getSkuId).collect(Collectors.toList()));
        }
        if (ToolUtil.isNotEmpty(activity.getSpRuleList())) {
            //校验特价规则信息
            check = skRuleMapper.selectRepeatActivity(PrmActivityConvert.INSTANCE.convert(activity), activity.getSpRuleList().stream().map(PrmSpRuleSaveReqVO::getSkuId).collect(Collectors.toList()));

        }
        if (ToolUtil.isEmpty(check)) {
            return new SkOrSpRuleCheckDTO();
        }
        String spuName = "";
        for (SkOrSpRuleCheckDTO item : check) {
            //取出spuId 并查询 获取spuName在用逗号进行拼接
            spuName += promotionCacheService.getSpuDTO(item.getSpuId()).getSpuName() + ",";
        }
        result.setSpuName(spuName.substring(0, spuName.length() - 1));

        //因为只有一个重复的促销活动，所以直接拿第一个就可以了
        result.setActivityName(check.get(0).getActivityName());
        result.setStartTime(check.get(0).getStartTime());
        result.setEndTime(check.get(0).getEndTime());
        return result;
    }

    private List<SkuPageRespVO> getItemsByActivityId(PrmActivityRespVO activityRespVO, List<ActivitySpuScopeDTO> spuScopes) {
        ProductContentPageReqDTO pageReqDTO = new ProductContentPageReqDTO();
        pageReqDTO.setSysCode(Optional.ofNullable(SecurityUtils.getLoginUser()).map(LoginUser::getSysCode).orElse(null));
        pageReqDTO.setPageNo(1);
        pageReqDTO.setPageSize(3);
        if (activityRespVO.getSpuScope() == 0) { // 全场商品
            List<Long> supplierIds = Lists.newArrayList();
            supplierIds.add(activityRespVO.getSupplierId());
            pageReqDTO.setSupplierId(supplierIds);
            List<Long> blockSkus = spuScopes.stream().filter(spuScope -> spuScope.getWhiteOrBlack() != null && NumberPool.INT_ZERO == spuScope.getWhiteOrBlack())
                    .map(ActivitySpuScopeDTO::getApplyId)
                    .filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());
            pageReqDTO.setBlockSkus(blockSkus);
        } else {
            List<Long> skuIds = spuScopes.stream().filter(spuScope -> spuScope.getWhiteOrBlack() != null && NumberPool.INT_ONE == spuScope.getWhiteOrBlack())
                    .map(ActivitySpuScopeDTO::getApplyId)
                    .filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());
            pageReqDTO.setSkuId(skuIds);
        }
        //获取商品数据
        PageResult<ProductContentDTO> pageResult = productContentApi.getPageElasticSearchListByApi(pageReqDTO).getCheckedData();
        List<SkuPageRespVO> searchList = HutoolBeanUtils.toBean(pageResult.getList(), SkuPageRespVO.class);
        //获取销售价和库存
        if (ToolUtil.isNotEmpty(searchList)) {
            searchList.forEach(product -> {
                BigDecimal markPrice = Optional.ofNullable(promotionCacheService.getSkuDTO(product.getSkuId())).map(SkuDTO::getMarkPrice).orElse(null);
                product.setMarkPrice(markPrice);
            });
        }
        return searchList;
    }

    private Map<Long, PrdtAreaItemPageRespVO> getAreaItemsBySkuIds(List<Long> skuIds) {
        Map<Long, PrdtAreaItemPageRespVO> skuAreaItemMap = Maps.newHashMap();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(skuIds)) {
            return skuAreaItemMap;
        }
        ApiAreaItemPageReqVO apiAreaItemPageReqVO = new ApiAreaItemPageReqVO();
        apiAreaItemPageReqVO.setSkuIdList(skuIds);
        CommonResult<PageResult<PrdtAreaItemPageRespVO>> result = remoteAreaItemApi.getAreaItemPageByApi(apiAreaItemPageReqVO);
        if (result != null && result.isSuccess() && result.getData() != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(result.getData().getList())) {
            skuAreaItemMap = result.getData().getList().stream().collect(Collectors.toMap(PrdtAreaItemPageRespVO::getSkuId, Function.identity(), (v1, v2) -> v1));
        }
        return skuAreaItemMap;
    }
}
