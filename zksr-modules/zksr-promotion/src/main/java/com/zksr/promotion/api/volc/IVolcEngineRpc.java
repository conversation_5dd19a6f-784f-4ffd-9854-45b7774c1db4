package com.zksr.promotion.api.volc;

import com.volcengine.model.livesaas.request.CommonRequest;
import com.volcengine.model.livesaas.request.CreateActivityAPIRequest;
import com.volcengine.model.livesaas.response.CommonResponse;
import com.volcengine.model.livesaas.response.GetActivityAPIResponse;
import com.volcengine.model.livesaas.response.GetDownloadLiveClientAPIResponse;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.volc.vo.*;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomPageReqVO;
import com.zksr.promotion.controller.room.vo.PrmLiveRoomSaveReqVO;
import com.zksr.promotion.domain.PrmLiveRoom;

import javax.validation.Valid;

/**
 * 火山引擎接口
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
public interface IVolcEngineRpc {

    /**
     * 创建直播间
     * @param reqVO
     * @return
     */
    CreateActivityRespVO createActivityAPIV2(CreateActivityReqVO reqVO);

    /**
     * 查询直播信息
     * @param req
     * @return
     */
    GetActivityRespVO getActivityAPI(GetActivityReqVO req);

    /**
     * 获取直播伴侣一键开播地址或嘉宾连麦登录地址
     * @param req
     * @return
     */
    GetDownloadLiveClientRespVO getDownloadLiveClientAPI(GetActivityReqVO req);
}
