package com.zksr.promotion.convert.scope;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.activity.dto.ActivityCityScopeDTO;
import com.zksr.promotion.domain.PrmActivityCityScope;
import com.zksr.promotion.controller.scope.vo.PrmActivityCityScopeRespVO;
import com.zksr.promotion.controller.scope.vo.PrmActivityCityScopeSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 促销活动城市适用范围 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-13
*/
@Mapper
public interface PrmActivityCityScopeConvert {

    PrmActivityCityScopeConvert INSTANCE = Mappers.getMapper(PrmActivityCityScopeConvert.class);

    PrmActivityCityScopeRespVO convert(PrmActivityCityScope prmActivityCityScope);

    PrmActivityCityScope convert(PrmActivityCityScopeSaveReqVO prmActivityCityScopeSaveReq);

    PageResult<PrmActivityCityScopeRespVO> convertPage(PageResult<PrmActivityCityScope> prmActivityCityScopePage);

    ActivityCityScopeDTO convertDTO(PrmActivityCityScope prmActivityCityScope);

    List<ActivityCityScopeDTO> convertListDTO(List<PrmActivityCityScope> prmActivityCityScopes);

}