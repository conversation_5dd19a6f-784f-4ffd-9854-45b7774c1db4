package com.zksr.promotion.convert.activity;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.areaItem.vo.ApiAreaItemPageReqVO;
import com.zksr.product.api.supplierItem.vo.ApiSupplierItemPageReqVO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.controller.activity.vo.PrmActivityItemPageVo;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.controller.activity.vo.PrmActivitySaveReqVO;
import com.zksr.promotion.domain.PrmActivity;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 促销活动 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-13
*/
@Mapper
public interface PrmActivityConvert {

    PrmActivityConvert INSTANCE = Mappers.getMapper(PrmActivityConvert.class);

    PrmActivityRespVO convert(PrmActivity prmActivity);

    PrmActivity convert(PrmActivitySaveReqVO prmActivitySaveReq);

    PageResult<PrmActivityRespVO> convertPage(PageResult<PrmActivity> prmActivityPage);

    List<SupplierActivityDTO> convertSupplierActivity(List<PrmActivity> activities);

    @Mappings({
            @Mapping(source = "cityScopeList", target = "supplierActivity.cityScopeList"),
            @Mapping(source = "channelScopeList", target = "supplierActivity.channelScopeList"),
            @Mapping(source = "branchScopeList", target = "supplierActivity.branchScopeList"),
            @Mapping(source = "fdRules", target = "supplierActivity.fdRules"),
            @Mapping(source = "fgRules", target = "supplierActivity.fgRules"),
            @Mapping(source = "bgRules", target = "supplierActivity.bgRules"),
            @Mapping(source = "skRules", target = "supplierActivity.skRules"),
            @Mapping(source = "spRules", target = "supplierActivity.spRules"),
            @Mapping(source = "cbRules", target = "supplierActivity.cbRules"),
    })
    @BeanMapping(ignoreByDefault = true)
    void buildSetDTO(@MappingTarget SupplierActivityDTO supplierActivity,
                     List<ActivityCityScopeDTO> cityScopeList,
                     List<ActivityChannelScopeDTO> channelScopeList,
                     List<ActivityBranchScopeDTO> branchScopeList,
                     List<FdRuleDTO> fdRules,
                     List<FgRuleDTO> fgRules,
                     List<BgRuleDTO> bgRules,
                     List<SkRuleDTO> skRules,
                     List<SpRuleDTO> spRules,
                     List<CbRuleDTO> cbRules
    );

    @Mappings({
            @Mapping(source = "cityScopeList", target = "cityScopes"),
            @Mapping(source = "channelScopeList", target = "channelScopes"),
            @Mapping(source = "branchScopeList", target = "branchScopes"),
            @Mapping(source = "spuScopeList", target = "spuScopes")
    })
    PrmActivityDTO convert0(PrmActivity prmActivity, List<ActivityCityScopeDTO> cityScopeList, List<ActivityChannelScopeDTO> channelScopeList, List<ActivityBranchScopeDTO> branchScopeList, List<Long> spuScopeList);

    ApiAreaItemPageReqVO convertAreaItemPageReqVO(PrmActivityItemPageVo pageReqVO);

    ApiSupplierItemPageReqVO convertSupplierItemPageReqVO(PrmActivityItemPageVo pageReqVO);
}