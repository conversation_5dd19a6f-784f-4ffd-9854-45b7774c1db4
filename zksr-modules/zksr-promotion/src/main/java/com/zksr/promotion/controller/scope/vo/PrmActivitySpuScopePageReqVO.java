package com.zksr.promotion.controller.scope.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 促销活动spu适用范围对象 prm_activity_spu_scope
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@ApiModel("促销活动spu适用范围 - prm_activity_spu_scope分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmActivitySpuScopePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @ApiModelProperty(value = "活动id", required = true)
    private Long activityId;

    /** 适用id */
    @Excel(name = "适用id")
    @ApiModelProperty(value = "适用id")
    private Long applyId;

    /** 适用类型;2-品类，3-品牌，4-商品 */
    @Excel(name = "适用类型;2-品类，3-品牌，4-商品")
    @ApiModelProperty(value = "适用类型;2-品类，3-品牌，4-商品")
    private Long applyType;


}
