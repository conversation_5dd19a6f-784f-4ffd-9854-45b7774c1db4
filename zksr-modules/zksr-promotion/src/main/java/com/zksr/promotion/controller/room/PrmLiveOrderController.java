package com.zksr.promotion.controller.room;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.promotion.domain.PrmLiveOrder;
import com.zksr.promotion.service.IPrmLiveOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.promotion.controller.order.vo.PrmLiveOrderPageReqVO;
import com.zksr.promotion.controller.order.vo.PrmLiveOrderSaveReqVO;
import com.zksr.promotion.controller.order.vo.PrmLiveOrderRespVO;
import com.zksr.promotion.convert.room.PrmLiveOrderConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 直播订单Controller
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Api(tags = "管理后台 - 直播订单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/order")
public class PrmLiveOrderController {
    @Autowired
    private IPrmLiveOrderService prmLiveOrderService;

    /**
     * 新增直播订单
     */
    @ApiOperation(value = "新增直播订单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
//    @RequiresPermissions(Permissions.ADD)
    @Log(title = "直播订单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public CommonResult<String> add(@Valid @RequestBody List<PrmLiveOrderSaveReqVO> createReqVO) {
        return success(prmLiveOrderService.insertPrmLiveOrder(createReqVO));
    }

    /**
     * 修改直播订单
     */
    @ApiOperation(value = "修改直播订单", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "直播订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrmLiveOrderSaveReqVO updateReqVO) {
            prmLiveOrderService.updatePrmLiveOrder(updateReqVO);
        return success(true);
    }

    /**
     * 删除直播订单
     */
    @ApiOperation(value = "删除直播订单", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "直播订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public CommonResult<Boolean> remove(@PathVariable Long[] ids) {
        prmLiveOrderService.deletePrmLiveOrderByIds(ids);
        return success(true);
    }

    /**
     * 获取直播订单详细信息
     */
    @ApiOperation(value = "获得直播订单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{id}")
    public CommonResult<PrmLiveOrderRespVO> getInfo(@PathVariable("id") Long id) {
        PrmLiveOrder prmLiveOrder = prmLiveOrderService.getPrmLiveOrder(id);
        return success(PrmLiveOrderConvert.INSTANCE.convert(prmLiveOrder));
    }

    /**
     * 分页查询直播订单
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得直播订单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
//    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrmLiveOrderRespVO>> getPage(@Valid PrmLiveOrderPageReqVO pageReqVO) {
        PageResult<PrmLiveOrder> pageResult = prmLiveOrderService.getPrmLiveOrderPage(pageReqVO);
        return success(PrmLiveOrderConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "promotion:order:add";
        /** 编辑 */
        public static final String EDIT = "promotion:order:edit";
        /** 删除 */
        public static final String DELETE = "promotion:order:remove";
        /** 列表 */
        public static final String LIST = "promotion:order:list";
        /** 查询 */
        public static final String GET = "promotion:order:query";
        /** 停用 */
        public static final String DISABLE = "promotion:order:disable";
        /** 启用 */
        public static final String ENABLE = "promotion:order:enable";
    }
}
