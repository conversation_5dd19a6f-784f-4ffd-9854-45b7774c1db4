package com.zksr.promotion.controller.rule.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 秒杀规则对象 prm_sk_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ApiModel("秒杀规则 - prm_sk_rule Response VO")
public class PrmSkRuleRespVO {
    private static final long serialVersionUID = 1L;

    /** 秒杀规则id */
    @ApiModelProperty(value = "秒杀价")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @ApiModelProperty(value = "活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** SPU id */
    @Excel(name = "SPU id")
    @ApiModelProperty(value = "SPU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** SKU id */
    @Excel(name = "SKU id")
    @ApiModelProperty(value = "SKU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 单次限量 */
    @Excel(name = "单次限量")
    @ApiModelProperty(value = "单次限量")
    private Integer onceLimit;

    /** 秒杀库存 */
    @Excel(name = "秒杀库存")
    @ApiModelProperty(value = "秒杀库存")
    private Integer seckillStock;

    /** 秒杀库存（中单位） */
    @Excel(name = "秒杀库存中单位）")
    @ApiModelProperty(value = "秒杀库存中单位）")
    private Integer seckillMidStock;

    /** 秒杀库存（大单位） */
    @Excel(name = "秒杀库存（大单位）")
    @ApiModelProperty(value = "秒杀库存（大单位）")
    private Integer seckillLargeStock;

    /** 秒杀价 */
    @Excel(name = "小单位秒杀价")
    @ApiModelProperty(value = "小单位秒杀价")
    private BigDecimal seckillPrice;

    /** 秒杀价 */
    @Excel(name = "中单位秒杀价")
    @ApiModelProperty(value = "中单位秒杀价")
    private BigDecimal midSeckillPrice;

    /** 秒杀价 */
    @Excel(name = "大单位秒杀价")
    @ApiModelProperty(value = "大单位秒杀价")
    private BigDecimal largeSeckillPrice;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String spuName;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典（sys_prdt_unit）")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long unit;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    private String supplierName;

    /** 国际条码 */
    @Excel(name = "国际条码")
    private String barcode;

    /** 小单位销售价 */
    @Excel(name = "小单位销售价")
    private BigDecimal markPrice;

    /** 中单位销售价 */
    @Excel(name = "中单位销售价")
    private BigDecimal midMarkPrice;

    /** 大单位销售价 */
    @Excel(name = "大单位销售价")
    private BigDecimal largeMarkPrice;

    /** 小单位成本价 */
    @Excel(name = "小单位成本价")
    private BigDecimal costPrice;

    /** 中单位成本价 */
    @Excel(name = "中单位成本价")
    private BigDecimal midCostPrice;

    /** 大单位成本价 */
    @Excel(name = "大单位成本价")
    private BigDecimal largeCostPrice;

    /** 库存数量 */
    @Excel(name = "库存数量")
    private Integer stock;

    /** 已售数量 */
    @Excel(name = "已售数量")
    private Integer soldNum;

    /** 状态：0-停用，1-启用 */
    @Excel(name = "状态：0-停用，1-启用")
    private Integer skStatus;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    @ApiModelProperty(value = "中单位换算数量（换算成最小单位）")
    @Excel(name = "中单位换算数量（换算成最小单位）")
    private BigDecimal midSize;

    @ApiModelProperty(value = "大单位换算数量（换算成最小单位）")
    @Excel(name = "大单位换算数量（换算成最小单位）")
    private BigDecimal largeSize;

    @ApiModelProperty(value = "最小单位-数据字典（sys_prdt_unit）")
    private Long minUnit;

    @ApiModelProperty(value = "中单位-数据字典（sys_prdt_unit）")
    private Long midUnit;

    @ApiModelProperty(value = "大单位-数据字典（sys_prdt_unit）")
    private Long largeUnit;

    /** 中单位限购数量 */
    @Excel(name = "中单位限购数量")
    @ApiModelProperty(value = "中单位限购数量")
    private Integer midLimit;

    /** 大单位限购数量 */
    @Excel(name = "大单位限购数量")
    @ApiModelProperty(value = "大单位限购数量")
    private Integer largeLimit;

    /** 总已售数量（小单位） */
    @Excel(name = "总已售数量（小单位）")
    @ApiModelProperty(value = "总已售数量（小单位）")
    private Integer totalSaleNum;

    /** 总已售数量（中单位） */
    @Excel(name = "总已售数量（中单位）")
    @ApiModelProperty(value = "总已售数量（中单位）")
    private Integer midTotalSaleNum;

    /** 总已售数量（大单位） */
    @Excel(name = "总已售数量（大单位）")
    @ApiModelProperty(value = "总已售数量（大单位）")
    private Integer largeTotalSaleNum;
}
