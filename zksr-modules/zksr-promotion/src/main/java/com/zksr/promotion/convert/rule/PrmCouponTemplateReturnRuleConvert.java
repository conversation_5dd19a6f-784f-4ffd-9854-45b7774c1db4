package com.zksr.promotion.convert.rule;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.coupon.dto.PrmCouponTemplateRepeatRuleDTO;
import com.zksr.promotion.domain.PrmCouponTemplateRepeatRule;
import com.zksr.promotion.domain.PrmCouponTemplateReturnRule;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateReturnRuleRespVO;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateReturnRuleSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 优惠券模板-下单返券规则（待讨论） 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-09
*/
@Mapper
public interface PrmCouponTemplateReturnRuleConvert {

    PrmCouponTemplateReturnRuleConvert INSTANCE = Mappers.getMapper(PrmCouponTemplateReturnRuleConvert.class);

    PrmCouponTemplateReturnRuleRespVO convert(PrmCouponTemplateReturnRule prmCouponTemplateReturnRule);

    PrmCouponTemplateReturnRule convert(PrmCouponTemplateReturnRuleSaveReqVO prmCouponTemplateReturnRuleSaveReq);

    PageResult<PrmCouponTemplateReturnRuleRespVO> convertPage(PageResult<PrmCouponTemplateReturnRule> prmCouponTemplateReturnRulePage);

    PrmCouponTemplateRepeatRuleDTO convertDTO(PrmCouponTemplateRepeatRule prmCouponTemplateRepeatRuleByTemplateId);

}