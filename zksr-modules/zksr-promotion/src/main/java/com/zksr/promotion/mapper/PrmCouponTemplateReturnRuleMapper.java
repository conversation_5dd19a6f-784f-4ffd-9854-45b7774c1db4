package com.zksr.promotion.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.domain.PrmCouponTemplateReturnRule;
import com.zksr.promotion.controller.rule.vo.PrmCouponTemplateReturnRulePageReqVO;


/**
 * 优惠券模板-下单返券规则（待讨论）Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@Mapper
public interface PrmCouponTemplateReturnRuleMapper extends BaseMapperX<PrmCouponTemplateReturnRule> {
    default PageResult<PrmCouponTemplateReturnRule> selectPage(PrmCouponTemplateReturnRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrmCouponTemplateReturnRule>()
                    .eqIfPresent(PrmCouponTemplateReturnRule::getCouponTemplateReturnRuleId, reqVO.getCouponTemplateReturnRuleId())
                    .eqIfPresent(PrmCouponTemplateReturnRule::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrmCouponTemplateReturnRule::getCouponTemplateId, reqVO.getCouponTemplateId())
                    .eqIfPresent(PrmCouponTemplateReturnRule::getConditionType, reqVO.getConditionType())
                    .eqIfPresent(PrmCouponTemplateReturnRule::getConditionAmt, reqVO.getConditionAmt())
                    .eqIfPresent(PrmCouponTemplateReturnRule::getConditionApplyIds, reqVO.getConditionApplyIds())
                .orderByDesc(PrmCouponTemplateReturnRule::getCouponTemplateReturnRuleId));
    }
}
