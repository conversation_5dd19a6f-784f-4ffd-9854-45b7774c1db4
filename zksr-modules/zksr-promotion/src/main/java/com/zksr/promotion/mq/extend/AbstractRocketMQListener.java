package com.zksr.promotion.mq.extend;


import com.fasterxml.jackson.core.type.TypeReference;
//import com.midea.mbf.mq.cfg.ConsumerCfg;
//import com.midea.mbf.mq.consumer.listener.MessageListenerLifecycle;
//import com.midea.mbf.mq.exception.MQConsumerException;
//import com.midea.mbf.mq.utils.JsonUtils;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ResolvableType;
import org.springframework.stereotype.Component;

/**
 * 消息消费抽象类. (补丁类)
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public abstract class AbstractRocketMQListener<T> implements RocketMQListener<MessageExt>,
        RocketMQPushConsumerLifecycleListener {

//    @Autowired
//    private ConsumerCfg cfg;

    @Autowired
    private List<MessageListenerLifecycle> listenerLifecycles;

    private final Type messageType;

    private DefaultMQPushConsumer consumer;

    protected AbstractRocketMQListener() {
        this.messageType = resolveMessageType();
        if (!this.getClass().isAnnotationPresent(RocketMQMessageListener.class)) {
            throw new RuntimeException(getClass() + " is not annotated @RocketMQMessageListener");
        }
        RocketMQMessageListener annotation = this.getClass().getAnnotation(RocketMQMessageListener.class);
        if (StringUtils.isEmpty(annotation.topic())) {
            throw new RuntimeException(
                    getClass().getSimpleName() + " annotated with " + RocketMQMessageListener.class
                            + ", but topic is empty");
        }
        if (StringUtils.isEmpty(annotation.consumerGroup())) {
            throw new RuntimeException(
                    getClass().getSimpleName() + " annotated with " + RocketMQMessageListener.class
                            + ", but consumerGroup is empty");
        }
    }


    @Override
    public void prepareStart(DefaultMQPushConsumer consumer) {
        this.consumer = consumer;
        if (listenerLifecycles != null) {
            listenerLifecycles.forEach(each -> each.prepareConsumerStart(consumer));
        }
    }

    @Override
    public void onMessage(MessageExt msg) {
        String payload = beforeOnMessage(msg);
        Throwable cause = null;
        try {
            T body = this.doConvertMessage(payload);
            AbstractRocketMQListener<T> listener = (AbstractRocketMQListener<T>) determineListenerInstance();
            listener.processMsg(body, msg);
        } catch (Throwable e) { // NOSONAR
            log.error("Consume message error, topic: {}, consumerGroup: {}", msg.getTopic(),
                    consumer.getConsumerGroup());
            cause = e;
//            if (msg.getReconsumeTimes() < cfg.getMaxFailRetryTimes(consumer.getConsumerGroup())) {
//                throw e;
//            }
        } finally {
            afterOnMessage(msg, cause);
        }
    }

    private AbstractRocketMQListener<?> determineListenerInstance() {
        AbstractRocketMQListener<?> listener;
        try {
            listener = (AbstractRocketMQListener<?>) AopContext.currentProxy();
        } catch (IllegalStateException e) {
            listener = this;
        }
        return listener;
    }

    @NotNull
    private String beforeOnMessage(MessageExt msg) {
        String str = new String(msg.getBody(), StandardCharsets.UTF_8);
        if (log.isDebugEnabled()) {
            log.debug("Receive rocketmq message, messageId:[{}],topic:[{}],groupName:[{}],keys:[{}],messageBody:[{}]",
                    msg.getMsgId(), msg.getTopic(), consumer.getConsumerGroup(), msg.getKeys(), str);
        }
        if (listenerLifecycles != null) {
            listenerLifecycles.forEach(each -> each.beforeOnMessage(msg, consumer));
        }
        return str;
    }

    /**
     * 将消息体转成Java数据对象
     *
     * @param payload 消息体
     * @return Java数据对象
     */
    protected T doConvertMessage(String payload) {
        if (String.class.equals(messageType)) {
            return (T) payload;
        }
        return (T) JsonUtils.toJavaClass(payload, new TypeReference<Type>() {
            @Override
            public Type getType() {
                return messageType;
            }
        });
    }

    /**
     * 处理消息
     *
     * @param payload 消息体
     * @param msg 消息
     */
    public abstract void processMsg(T payload, MessageExt msg);

    private void afterOnMessage(MessageExt msg, Throwable ex) {
        if (listenerLifecycles != null) {
            listenerLifecycles.forEach(each -> each.afterOnMessage(msg, consumer, ex));
        }
    }

    private Type resolveMessageType() {
        ResolvableType resolvableType = this.getResolvableType();

        if (!resolvableType.hasGenerics()) {
            throw new RuntimeException("cannot reach here");
        }

        if (resolvableType.getGenerics().length > 1) {
            throw new RuntimeException("Expected 1 type argument on generic interface [" + resolvableType +
                    "] but found " + resolvableType.getGenerics().length);
        }

        return resolvableType.getGeneric().getType();
    }

    /**
     * 获取父类泛型类型.
     *
     * @return ResolvableType
     */
    private ResolvableType getResolvableType() {
        ResolvableType resolvableType = null;
        Class<?> currentCls = this.getClass();
        do {
            if (currentCls.getGenericSuperclass() instanceof ParameterizedType) {
                resolvableType = ResolvableType.forClass(currentCls).as(currentCls.getSuperclass());
                break;
            }
            currentCls = currentCls.getSuperclass();
        } while (null != currentCls && !currentCls.equals(AbstractRocketMQListener.class));
        return resolvableType;
    }
}

