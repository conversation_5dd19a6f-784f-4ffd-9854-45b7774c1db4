package com.zksr.promotion.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.controller.quota.vo.*;
import com.zksr.promotion.domain.PrmCouponColonelQuota;

import java.util.List;

/**
 * 业务员发券额度Service接口
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
public interface IPrmCouponColonelQuotaService {
    /**
     * 获得业务员发券额度分页
     *
     * @param pageReqVO 分页查询
     * @return 业务员发券额度分页
     */
    PageResult<PrmCouponColonelQuotaRespVO> getPrmCouponColonelQuotaPage(PrmCouponColonelQuotaPageReqVO pageReqVO);

    /**
     * 调整额度
     * @param pageReqVO
     */
    Long adjustQuota(PrmCouponColonelQuotaAdjustReqVO pageReqVO);

    /**
     * 查看当前业务员发券额度详情
     * @param colonelId
     * @return
     */
    QuotaDetailResponse selectQuotaDetails(String colonelId);

    /**
     * 查看历史发券额度
     * @param colonelId
     * @return
     */
    List<QuotaHistoryResponse> selectQuotaHistory(String colonelId);
}
