package com.zksr.promotion.controller.rule.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 满减活动规则对象 prm_fd_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ApiModel("满减活动规则 - prm_fd_rule分页 Request VO")
public class PrmFdRuleSaveReqVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 满减活动规则id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long fdRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @ApiModelProperty(value = "活动id")
    private Long activityId;

    /** 满减触发金额 */
    @Excel(name = "满减触发金额")
    @ApiModelProperty(value = "满减触发金额")
    private BigDecimal fullAmt;

    /** 满减触发数量 */
    @Excel(name = "满减触发数量")
    @ApiModelProperty(value = "满减触发数量")
    private Integer fullQty;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmt;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Integer status;

}
