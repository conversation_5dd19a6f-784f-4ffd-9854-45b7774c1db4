package com.zksr.promotion.controller.product.vo;

import java.math.BigDecimal;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 直播商品对象 prm_live_product
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@ApiModel("直播商品 - prm_live_product分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrmLiveProductPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** ID主键 */
    @ApiModelProperty(value = "版本号")
    private Long id;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 直播ID */
    @Excel(name = "直播ID")
    @ApiModelProperty(value = "直播ID", required = true)
    private String activityId;

    /** 直播间名称 */
    @Excel(name = "直播间名称")
    @ApiModelProperty(value = "直播间名称", required = true)
    private String name;

    /** 商品sku_id */
    @Excel(name = "商品sku_id")
    @ApiModelProperty(value = "商品sku_id", required = true)
    private Long skuId;

    /** 商品SPU_id */
    @Excel(name = "商品SPU_id")
    @ApiModelProperty(value = "商品SPU_id", required = true)
    private Long spuId;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    @ApiModelProperty(value = "商品SPU编号", required = true)
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称", required = true)
    private String spuName;

    /** 标准价(原价) */
    @Excel(name = "标准价(原价)")
    @ApiModelProperty(value = "标准价(原价)", required = true)
    private BigDecimal markPrice;

    /** 直播价 */
    @Excel(name = "直播价")
    @ApiModelProperty(value = "直播价", required = true)
    private BigDecimal livePrice;

    /** 商品品单位 数据字典（sys_prdt_unit） */
    @Excel(name = "商品品单位 数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "商品品单位 数据字典", required = true)
    private String unit;

    /** 商品品单位名称 */
    @Excel(name = "商品品单位名称")
    @ApiModelProperty(value = "商品品单位名称", required = true)
    private String unitName;

    /** 图片1 */
    @Excel(name = "图片1")
    @ApiModelProperty(value = "图片1", required = true)
    private String imageUrl1;

    /** 图片2 */
    @Excel(name = "图片2")
    @ApiModelProperty(value = "图片2", required = true)
    private String imageUrl2;

    /** 图片3 */
    @Excel(name = "图片3")
    @ApiModelProperty(value = "图片3", required = true)
    private String imageUrl3;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id", required = true)
    private Long supplierId;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称", required = true)
    private String supplierName;

    /** 是否删除：0-否，1-是 */
    @ApiModelProperty(value = "入驻商名称", required = true)
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号", required = true)
    private Long version;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 弹框商品标记，0：不是，1：是 */
    @Excel(name = "弹框商品标记，0：不是，1：是")
    @ApiModelProperty(value = "弹框商品标记，0：不是，1：是")
    private Long broadcastFlag;
}
