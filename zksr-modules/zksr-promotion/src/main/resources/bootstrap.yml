# Tomcat
server:
  port: 6104

# Spring
spring:
  application:
    # 应用名称
    name: zksr-promotion
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 10.27.12.137:8848
        namespace: b2b-dev
        username: nacos
        password: 50vGMVGRtg
      config:
        # 配置中心地址
        server-addr: 10.27.12.137:8848
        namespace: b2b-dev
        username: nacos
        password: 50vGMVGRtg
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-redis-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-rocketmq-promotion-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

# knife4j公共配置
swagger:
  enabled: false
knife4j:
  enabled: false

mbf:
  txmq:
    enabled: true
    id-type: auto-increment # auto-increment 消息落库使用id自增  id4j 使用雪花算法id
    close-persist: false #本配置可不添加，不配置则默认false (false-持久化  true-不持久化)
    persist-table-name: tc_transaction_msg_send #默认表名是tc_transaction_msg_send
  mq:
    consumer:
      persist-type: jdbc #选配，指定持久化策略
      id-type: auto-increment ##选配 默认auto-increment:id自增  id4j-使用雪花算法
      core-pool-size: 20 #选配 核心线程数想，默认20
      consume-thread-max: 64 #选配 最大线程数，默认64
      max-fail-retry-times: 16 #选配，允许rocketmq自动重试的次数，到达该次数后，消息落库
      groups: #选配 针对消费组配置，可不配置
        GROUP-BASEMQ-TEST-01:
          core-pool-size: 20 # 核心线程数
          consume-thread-max: 10 # 最大线程数
        GROUP-BASEMQ-TEST-02:
          core-pool-size: 20 # 核心线程数
          consume-thread-max: 64 # 最大线程数
        GROUP-BASEMQ-TEST-03:
          core-pool-size: 20 # 核心线程数
          consume-thread-max: 64 # 最大线程数

rocketmq:
  consumer:
    enable: true
  name-server: 10.27.12.136:9876
  producer:
    compressMessageBodyThreshold: 4096
    enableMsgTrace: false
    group: b2b
    maxMessageSize: 1048576
    retryNextServer: true
    retryTimesWhenSendAsyncFailed: 2
    retryTimesWhenSendFailed: 2
    sendMessageTimeout: 10000
