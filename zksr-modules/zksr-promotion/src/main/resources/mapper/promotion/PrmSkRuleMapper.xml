<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.promotion.mapper.PrmSkRuleMapper">

    <delete id="deleteSkRuleByActivityId">
        DELETE FROM prm_sk_rule
        WHERE activity_id = #{activityId}
    </delete>
    <select id="selectRepeatActivity" resultType="com.zksr.promotion.controller.activity.dto.SkOrSpRuleCheckDTO">
        SELECT
        pa.activity_name AS activityName,sk.spu_id AS spuId,pa.start_time AS startTime,pa.end_time AS endTime
        FROM
        prm_sk_rule sk
        LEFT JOIN prm_activity pa ON sk.activity_id = pa.activity_id
        WHERE
        sk.sku_id IN
        <foreach item="item" index="index" collection="skuIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="activity.activityId != null">
            AND pa.activity_id != #{activity.activityId}
        </if>
        AND pa.prm_status IN ( 0, 1 )
        AND pa.prm_no IN ('SK', 'SP')
        AND pa.func_scope = #{activity.funcScope} <!-- 校验活动商品互斥区分 全国或者本地 -->
        AND pa.end_time &gt;= now() -- 校验促销 必须是未失效的促销活动
        AND (( pa.start_time &lt;= #{activity.endTime} AND pa.end_time &gt;= #{activity.startTime} ) OR ( pa.start_time
        &lt;= #{activity.startTime} AND pa.end_time &gt;= #{activity.endTime} ) )
        GROUP BY
        pa.activity_id,sk.spu_id
        UNION
        SELECT
        pa.activity_name AS activityName,sp.spu_id AS spuId,pa.start_time AS startTime,pa.end_time AS endTime
        FROM
        prm_sp_rule sp
        LEFT JOIN prm_activity pa ON sp.activity_id = pa.activity_id
        WHERE
        sp.sku_id IN
        <foreach item="item" index="index" collection="skuIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="activity.activityId != null">
            AND pa.activity_id != #{activity.activityId}
        </if>
        AND pa.prm_status IN ( 0, 1 )
        AND pa.prm_no IN ('SK', 'SP')
        AND pa.func_scope = #{activity.funcScope} <!-- 校验活动商品互斥区分 全国或者本地 -->
        AND pa.end_time &gt;= now() -- 校验促销 必须是未失效的促销活动
        AND (( pa.start_time &lt;= #{activity.endTime} AND pa.end_time &gt;= #{activity.startTime} ) OR ( pa.start_time
        &lt;= #{activity.startTime} AND pa.end_time &gt;= #{activity.endTime} ) )
        GROUP BY
        pa.activity_id,sp.spu_id
    </select>

</mapper>