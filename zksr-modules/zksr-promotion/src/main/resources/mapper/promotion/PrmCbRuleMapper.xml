<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.promotion.mapper.PrmCbRuleMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getActivityCbByStatus" resultType="Long">
        SELECT
        activity_id
        FROM
        prm_activity
        WHERE
        prm_no = 'CB'
        AND sys_code = #{sysCode}
        <if test="funcScope != null">
            AND func_scope = #{funcScope}
        </if>
        <choose>
            <when test="status != null and status == 3">
                AND NOW() > end_time
            </when>
            <when test="status != null and status != 3">
                AND prm_status = #{status}
            </when>
        </choose>
    </select>


</mapper>