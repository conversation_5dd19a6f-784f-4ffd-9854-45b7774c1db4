<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.promotion.mapper.PrmActivityMapper">
    <!-- 促销表与满赠表数据 -->
    <resultMap id="getFgActivity" type="com.zksr.promotion.controller.rule.vo.PrmFgRuleRespVO">
        <id property="activityId" column="activity_id"/>
        <result property="activityName" column="activity_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="prmNo" column="prm_no"/>
        <result property="spuScope" column="spu_scope"/>
        <result property="chanelScopeAllFlag" column="chanel_scope_all_flag"/>
        <result property="branchScopeAllFlag" column="branch_scope_all_flag"/>
        <result property="funcScope" column="func_scope"/>
        <result property="prmStatus" column="prm_status"/>
        <result property="ladderFlag" column="ladder_flag"/>
        <result property="timesRule" column="times_rule"/>
        <result property="memo" column="memo"/>
        <collection property="prmFgRuleSaveReqVOList" ofType="com.zksr.promotion.controller.rule.vo.PrmFgRuleSaveReqVO">
            <id property="fgRuleId" column="fg_rule_id"/>
            <result property="fullAmt" column="full_amt"/>
            <result property="giftType" column="gift_type"/>
            <result property="skuId" column="sku_id"/>
            <result property="couponTemplateId" column="coupon_template_id"/>
            <result property="onceGiftQty" column="once_gift_qty"/>
            <result property="totalGiftQty" column="total_gift_qty"/>
            <result property="status" column="status"/>
            <result property="giftSkuUnitType" column="gift_sku_unit_type"/>
            <result property="buySkuNum" column="buy_sku_num"/>
            <result property="giftGroupType" column="gift_group_type"/>
            <result property="giftSkuUnitQty" column="gift_sku_unit_qty"/>
        </collection>
    </resultMap>

    <select id="getPrmFgRule" resultMap="getFgActivity">
        SELECT
            pa.activity_id,
            pa.activity_name,
            pa.start_time,
            pa.end_time,
            pa.supplier_id,
            pa.prm_no,
            pa.spu_scope,
            pa.chanel_scope_all_flag,
            pa.branch_scope_all_flag,
            pa.func_scope,
            pa.prm_status,
            pa.ladder_flag,
            pa.times_rule,
            pa.memo,
            pfr.fg_rule_id,
            pfr.full_amt,
            pfr.gift_type,
            pfr.sku_id,
            pfr.coupon_template_id,
            pfr.once_gift_qty,
            pfr.total_gift_qty,
            pfr.status,
            pfr.gift_sku_unit_type,
            pfr.buy_sku_num,    --  购买品项数(sku种类数)默认为1
            pfr.gift_group_type, -- 赠送方式(默认为全赠 0仅一种，1任选，2全赠)
            pfr.gift_sku_unit_qty -- 赠送单位数量
        FROM
            prm_activity pa
            LEFT JOIN prm_fg_rule pfr ON pa.activity_id = pfr.activity_id and pfr.status = 1
        WHERE
            pa.activity_id = #{activityId}
    </select>

    <!-- 促销表与满减表数据 -->
    <resultMap id="getFdActivity" type="com.zksr.promotion.controller.rule.vo.PrmFdRuleRespVO">
        <id property="activityId" column="activity_id"/>
        <result property="activityName" column="activity_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="prmNo" column="prm_no"/>
        <result property="prmSheetNo" column="prm_sheet_no"/>
        <result property="spuScope" column="spu_scope"/>
        <result property="chanelScopeAllFlag" column="chanel_scope_all_flag"/>
        <result property="branchScopeAllFlag" column="branch_scope_all_flag"/>
        <result property="funcScope" column="func_scope"/>
        <result property="prmStatus" column="prm_status"/>
        <result property="ladderFlag" column="ladder_flag"/>
        <result property="timesRule" column="times_rule"/>
        <result property="amtOrQty" column="amt_or_qty"/>
        <result property="memo" column="memo"/>
        <collection property="prmFdRuleList" ofType="com.zksr.promotion.domain.PrmFdRule">
            <id property="fdRuleId" column="fd_rule_id"/>
            <result property="fullAmt" column="full_amt"/>
            <result property="activityId" column="activity_id"/>
            <result property="fullQty" column="full_qty"/>
            <result property="discountAmt" column="discount_amt"/>
            <result property="status" column="status"/>
        </collection>
    </resultMap>


    <select id="getPrmFdRule" resultMap="getFdActivity">
        SELECT
               pa.activity_id,
               pa.activity_name,
               pa.start_time,
               pa.end_time,
               pa.supplier_id,
               pa.prm_no,
               pa.prm_sheet_no,
               pa.spu_scope,
               pa.chanel_scope_all_flag,
               pa.branch_scope_all_flag,
               pa.func_scope,
               pa.prm_status,
               pa.ladder_flag,
               pa.times_rule,
               pa.amt_or_qty,
               pa.memo,
               fd.fd_rule_id,
               fd.full_amt,
               fd.activity_id,
               fd.full_qty,
               fd.discount_amt,
               fd.status
        FROM
            prm_activity pa
            LEFT JOIN prm_fd_rule fd ON pa.activity_id = fd.activity_id
        WHERE
            pa.activity_id = #{activityId}
    </select>

    <select id="getIdListByScope" resultType="java.lang.Long">
        SELECT
            activity_id
        FROM
            prm_activity
        WHERE
            supplier_id = #{activity.supplierId}
          AND func_scope = #{activity.funcScope}
          AND prm_status = 1
          AND prm_no = #{activity.prmNo}
          AND spu_scope = #{activity.spuScope}
        <if test="null != activity.activityId">
            AND activity_id != #{activity.activityId}
        </if>
          AND (#{activity.startTime} BETWEEN start_time AND end_time OR #{activity.endTime} BETWEEN start_time AND end_time )
    </select>

    <!-- 查询促销活动使用报表 -->
    <select id="getActivityReport"
            parameterType="com.zksr.promotion.controller.activity.vo.PrmActivityReportPageVO"
            resultType="com.zksr.promotion.controller.activity.dto.PrmActivityReportRespDTO">
        SELECT
            pa.activity_id
            ,pa.activity_name
            ,pa.prm_status AS activityStatus
            ,pa.prm_no AS activityType
            ,pa.start_time AS activityStartTime
            ,pa.end_time AS activityEndTime
        FROM
            prm_activity pa
        <where>
            <if test="null != funcScope">
                AND pa.func_scope = #{funcScope}
            </if>
            <if test="null != activityId">
                AND pa.activity_id LIKE concat('%', #{activityId}, '%')
            </if>
            <if test="null != activityName">
                AND pa.activity_name LIKE concat('%', #{activityName}, '%')
            </if>
            <if test="null != activityType">
                AND pa.prm_no = #{activityType}
            </if>
            <if test="null != activityStatus">
                AND pa.prm_status = #{activityStatus}
            </if>
            <if test="null != startDate and startDate !=''">
                AND pa.start_time &gt;= concat(#{startDate}, ' 00:00:00.000')
            </if>
            <if test="null != endDate and endDate !=''">
                AND pa.end_time &lt;= concat(#{endDate}, ' 23:59:59.999')
            </if>
            ${params.dataScope}
        </where>
    </select>
    <select id="selectSeckillSpuIds" resultType="java.lang.Long">
        SELECT
            DISTINCT spu_id
        FROM
            <choose>
                <when test="prmNo.startsWith('SK')">
                    prm_sk_rule
                </when>
                <otherwise>
                    prm_sp_rule
                </otherwise>
            </choose>
        WHERE
            activity_id IN (
                SELECT
                    activity_id
                FROM
                    prm_activity
                WHERE
                    func_scope = #{funcScope}
                    AND prm_status = 1
                    <if test="prmNo != null">
                        AND prm_no = #{prmNo}
                    </if>
                    AND start_time &lt;= NOW()
                    AND end_time &gt;= NOW()
            )
            <if test="prmNo == 'SP'">
                AND status = 1
            </if>
    </select>
    <select id="selectSeckillSkuIds" resultType="java.lang.Long">
        SELECT
            DISTINCT pas.apply_id
        FROM
            prm_activity_spu_scope pas
            JOIN
                <choose>
                    <when test="prmNo.startsWith('FG')">
                        prm_fg_rule pfr
                    </when>
                    <when test="prmNo.startsWith('FD')">
                        prm_fd_rule pfr
                    </when>
                    <otherwise>
                        prm_bg_rule pfr
                    </otherwise>
                </choose>
            ON pas.activity_id = pfr.activity_id
            JOIN prm_activity pa ON pfr.activity_id = pa.activity_id
        WHERE
            pa.func_scope = #{funcScope}
            AND pa.prm_status = 1
            AND pfr.status = 1
            AND pa.start_time &lt;= NOW()
            AND pa.end_time &gt;= NOW()
            <if test="prmNo != null">
                AND pa.prm_no = #{prmNo}
            </if>
    </select>
    <select id="selectCountBySkuId" resultType="java.lang.Long">
        SELECT
            (
            SELECT
                IFNULL(COUNT(1), 0)
            FROM
                prm_activity pa
                INNER JOIN prm_sk_rule re ON pa.activity_id = re.activity_id
            WHERE
                re.sku_id = #{skuId}
                AND pa.end_time > now()
                AND pa.prm_status IN (0, 1)
            ) +
            (
            SELECT
                IFNULL(COUNT(1), 0)
            FROM
                prm_activity pa
                INNER JOIN prm_sp_rule re ON pa.activity_id = re.activity_id
            WHERE
                re.sku_id = #{skuId}
                AND pa.end_time > now()
                AND pa.prm_status IN (0, 1)
            )+
            (
            SELECT
                IFNULL(COUNT(1), 0)
            FROM
                prm_activity pa
                INNER JOIN prm_activity_spu_scope sp ON pa.activity_id = sp.activity_id
            WHERE
                pa.prm_no = 'FD'
                AND sp.apply_id = #{skuId}
                AND pa.end_time > now()
                AND pa.prm_status IN (0, 1)
            )
    </select>

    <select id="selectChcekActivityBySkuIds" resultType="com.zksr.promotion.api.activity.dto.CheckActivityItemDTO">
         SELECT * from (
             -- 查询是否参与秒杀
             SELECT pa.activity_id,pa.activity_name,pa.prm_no,pa.prm_sheet_no,psk.sku_id
             FROM
                 prm_activity pa
             LEFT JOIN prm_sk_rule psk ON psk.activity_id = pa.activity_id AND pa.prm_no = 'SK'
             WHERE
                 pa.func_scope = #{funcScope}
               AND pa.end_time > now()
               AND pa.prm_status IN (0, 1)
               AND psk.sku_id in
                  <foreach collection="skuIds" item="id" open="(" separator="," close=")">
                     #{id}
                  </foreach>
         UNION ALL

        -- 查询是否参与特价
         SELECT pa.activity_id,pa.activity_name,pa.prm_no,pa.prm_sheet_no,psp.sku_id
         FROM
         prm_activity pa
        LEFT JOIN prm_sp_rule psp ON psp.activity_id = pa.activity_id AND pa.prm_no = 'SP'
         WHERE
         pa.func_scope = #{funcScope}
         AND pa.end_time > now()
         AND pa.prm_status IN (0, 1)
         AND psp.sku_id in
         <foreach collection="skuIds" item="id" open="(" separator="," close=")">
             #{id}
         </foreach>

        UNION ALL

        -- 查询是否参与买赠
        SELECT pa.activity_id,pa.activity_name,pa.prm_no,pa.prm_sheet_no,pbg.sku_id
        FROM
        prm_activity pa
        LEFT JOIN prm_bg_rule pbg ON pbg.activity_id = pa.activity_id AND pa.prm_no = 'BG'
        WHERE
        pa.func_scope = #{funcScope}
        AND pa.end_time > now()
        AND pa.prm_status IN (0, 1)
        AND pbg.sku_id in
        <foreach collection="skuIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

<!--        UNION ALL

        &#45;&#45; 查询是否参与满减
        SELECT pa.activity_id,pa.activity_name,pa.prm_no,pa.prm_sheet_no,pfd.sku_id
        FROM
        prm_activity pa
        LEFT JOIN prm_fd_rule pfd ON pfd.activity_id = pa.activity_id AND pa.prm_no = 'FD'
        WHERE
        pa.func_scope = #{funcScope}
        AND pa.end_time > now()
        AND pfd.sku_id in
        <foreach collection="skuIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>-->

        UNION ALL

        -- 查询是否参与满赠
        SELECT pa.activity_id,pa.activity_name,pa.prm_no,pa.prm_sheet_no,pfg.sku_id
        FROM
        prm_activity pa
        LEFT JOIN prm_fg_rule pfg ON pfg.activity_id = pa.activity_id AND pa.prm_no = 'FG'
        WHERE
        pa.func_scope = #{funcScope}
        AND pa.end_time > now()
        AND pa.prm_status IN (0, 1)
        AND pfg.sku_id in
        <foreach collection="skuIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

        UNION ALL

        -- 查询是否参与促销活动spu适用范围
        SELECT pa.activity_id,pa.activity_name,pa.prm_no,pa.prm_sheet_no,pass.apply_id as sku_id
        FROM
        prm_activity pa
        LEFT JOIN prm_activity_spu_scope pass ON pass.activity_id = pa.activity_id
        WHERE
        pa.func_scope = #{funcScope}
        AND pa.end_time > now()
        AND pa.prm_status IN (0, 1)
        AND pass.apply_id in
        <foreach collection="skuIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
           ) a
                  order by sku_id

    </select>
    <select id="selectActivityPage" resultType="com.zksr.promotion.controller.activity.vo.PrmActivityRespVO">
        SELECT
            pa.*
        FROM
            prm_activity pa
            left join prm_activity_city_scope pc on pa.activity_id = pc.activity_id
        WHERE
            pa.prm_no != 'CB'
            <if test="reqVO.activityName != null and reqVO.activityName != ''">
                AND pa.activity_name LIKE CONCAT('%', #{reqVO.activityName}, '%')
            </if>
            <if test="reqVO.prmStatus != null">
                AND pa.prm_status = #{reqVO.prmStatus}
            </if>
            <if test="reqVO.funcScope != null">
                AND pa.func_scope = #{reqVO.funcScope}
            </if>
            <if test="reqVO.prmNo != null and reqVO.prmNo != ''">
                AND pa.prm_no = #{reqVO.prmNo}
            </if>
            <if test="reqVO.supplierId != null">
                AND pa.supplier_id = #{reqVO.supplierId}
            </if>
            <if test="reqVO.createTimeStart != null">
                AND pa.create_time &gt;= #{reqVO.createTimeStart}
            </if>
            <if test="reqVO.createTimeEnd != null">
                AND pa.create_time &lt;= #{reqVO.createTimeEnd}
            </if>
            <if test="reqVO.timesRule != null and reqVO.timesRule != ''">
                AND pa.times_rule = #{reqVO.timesRule}
            </if>
            <if test="reqVO.areaId != null and reqVO.areaId != ''">
                AND (
                    pc.activity_id IS NULL -- prm_activity_city_scope 表中没有数据
                OR (
                        pc.activity_id IS NOT NULL -- prm_activity_city_scope 表中有数据
                        AND (
                            (pc.area_id = #{reqVO.areaId} AND pc.white_or_black != 0) -- 有匹配的 area_id 且 white_or_black 不为 0
                            OR pc.area_id IS NULL -- 或者 area_id 为空
                        )
                    )
                )
            </if>
            <if test="reqVO.prmStatus != null">
                <choose>
                    <when test="reqVO.prmStatus == 3">
                        AND pa.prm_status = 1
                        AND pa.end_time &lt;= NOW()
                    </when>
                    <when test="reqVO.prmStatus == 1">
                        AND pa.prm_status = #{reqVO.prmStatus}
                        AND pa.end_time &gt; NOW()
                    </when>
                    <otherwise>
                        AND pa.prm_status = #{reqVO.prmStatus}
                    </otherwise>
                </choose>
            </if>
        ORDER BY
            pa.activity_id DESC
    </select>
    <select id="selectCbRuleList" resultType="com.zksr.promotion.api.activity.dto.CbRuleDTO">
        SELECT * FROM prm_cb_rule WHERE activity_id = #{activityId}
    </select>
    <select id="selectAreaCbSupplierIds" resultType="java.lang.Long">
        SELECT
            pay.supplier_id
        FROM
            prm_activity pay
            INNER JOIN prm_activity_city_scope pacs ON pacs.activity_id = pay.activity_id
        WHERE
            pacs.area_id = #{areaId}
            AND pay.end_time &gt; NOW()
            AND pay.prm_no = 'CB'
            AND pay.func_scope = 2
        GROUP BY
            pay.supplier_id
    </select>
    <select id="getGlobalCbSupplierIds" resultType="java.lang.Long">
        SELECT
            pay.supplier_id
        FROM
            prm_activity pay
        WHERE
            pay.sys_code = #{sysCode}
            AND pay.prm_no = 'CB'
            AND pay.end_time &gt; NOW()
            AND pay.func_scope = 1
        GROUP BY
            pay.supplier_id
    </select>
    <select id="getExpiredCbActivityIds" resultType="java.lang.Long">
        SELECT
            pay.activity_id
        FROM
            prm_activity pay
        WHERE
            pay.prm_no = 'CB'
            AND pay.end_time &lt; NOW()
            AND pay.prm_status = 1
    </select>
    <select id="selectListPage" resultType="com.zksr.promotion.domain.PrmActivity">
        SELECT
            pa.*
        FROM
            prm_activity pa
        <where>
            <if test="null != reqVo.sysCode"> AND pa.sys_code = #{reqVo.sysCode} </if>
            <if test="null != reqVo.funcScope"> AND pa.func_scope = #{reqVo.funcScope} </if>
            <if test="null != reqVo.activityId"> AND pa.activity_id = #{reqVo.activityId} </if>
            <if test="null != reqVo.supplierId"> AND pa.supplierId = #{reqVo.supplierId} </if>
            <if test="null != reqVo.prmNo"> AND pa.prm_no = #{reqVo.prmNo} </if>
            <if test="null != reqVo.activityName"> AND pa.activity_Name = #{reqVo.activityName} </if>
            <if test="null != reqVo.effectTime"> AND pa.effect_time = #{reqVo.effectTime} </if>
            <if test="null != reqVo.effectMan"> AND pa.effect_man = #{reqVo.effectMan} </if>
            <if test="null != reqVo.startTime"> AND pa.start_time = #{reqVo.startTime} </if>
            <if test="null != reqVo.endTime"> AND pa.end_time = #{reqVo.endTime} </if>
            <if test="null != reqVo.spuScope"> AND pa.spu_scope = #{reqVo.spuScope} </if>
            <if test="null != reqVo.chanelScopeAllFlag"> AND pa.chanel_scope_all_flag = #{reqVo.chanelScopeAllFlag} </if>
            <if test="null != reqVo.branchScopeAllFlag"> AND pa.branch_scope_all_flag = #{reqVo.branchScopeAllFlag} </if>
            <if test="null != reqVo.ladderFlag"> AND pa.ladder_flag = #{reqVo.ladderFlag} </if>
            <if test="null != reqVo.timesRule"> AND pa.times_rule = #{reqVo.timesRule} </if>
            <if test="null != reqVo.amtOrQty"> AND pa.amt_or_qty = #{reqVo.amtOrQty} </if>
            <if test="null != reqVo.prmStatus">
                <choose>
                    <when test="reqVo.prmStatus == 3">
                        AND pa.prm_status = 1
                        AND pa.end_time &lt;= NOW()
                    </when>
                    <when test="reqVo.prmStatus == 1">
                        AND pa.prm_status = #{reqVo.prmStatus}
                        AND pa.end_time &gt; NOW()
                    </when>
                    <otherwise>
                        AND pa.prm_status = #{reqVo.prmStatus}
                    </otherwise>
                </choose>
            </if>
            ${reqVo.params.dataScope}
        </where>

        ORDER BY
            pa.activity_id DESC
    </select>


</mapper>
