<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.promotion.mapper.PrmCouponTemplateExtendMapper">

    <select id="selectPageExt"
            resultType="com.zksr.promotion.controller.template.vo.PrmCouponTemplateExtendRespVO"
            parameterType="com.zksr.promotion.controller.template.vo.PrmCouponTemplateExtendPageReqVO">
        SELECT
            pct.coupon_template_id,
            pct.sys_code,
            pct.dc_id,
            pct.create_by,
            pct.create_time,
            pct.update_by,
            pct.update_time,
            pct.coupon_name,
            pct.func_scope,
            pct.status,
            pct.template_start_date,
            pct.template_end_date,
            pct.spu_scope,
            pct.spu_scope_apply_ids,
            pct.receive_scope,
            pct.receive_scope_apply_ids,
            pct.discount_type,
            pct.trigger_amt,
            pct.discount_amt,
            pct.discount_percent,
            pct.discount_limit_amt,
            pct.expiration_type,
            pct.expiration_date_start,
            pct.expiration_date_end,
            pct.disable_days,
            pct.expire_days,
            pct.receive_type,
            pct.cost_flag,
            pct.coupon_qty,
            pct.`limit`,
            pct.receive_count,
            pct.use_count,
            pct.excludable,
            pct.repeat_flag,
            pct.repeat_pid,
            pct.repeat_seq,
            pct.del_flag,
            pct.supplier_id,
            pcte.update_time extendUpdateTime,
            IFNULL(pcte.used_count, 0) AS usedCount,    --  已经使用数量
            IFNULL(pcte.record_count, 0) AS recordCount,  --  实际已领取数据
            IFNULL(pcte.total_sale_amt, 0) AS totalSaleAmt,    --  优惠券绑定的订单销量
            IFNULL(pcte.total_coupon_amt, 0) AS totalCouponAmt   --  优惠券实际优惠金额
        FROM
            prm_coupon_template pct
            LEFT JOIN prm_coupon_template_extend pcte ON pct.coupon_template_id = pcte.coupon_template_id
        WHERE 1 = 1
            <if test="null != funcScope">
                AND pct.func_scope = #{funcScope}
            </if>
            <if test="null != spuScope">
                AND pct.spu_scope = #{spuScope}
            </if>
            <if test="null != receiveType">
                AND pct.receive_type = #{receiveType}
            </if>
            <if test="null != couponName">
                AND pct.coupon_name LIKE CONCAT('%', #{couponName}, '%')
            </if>
            <if test="null != couponTemplateId">
                AND pct.coupon_template_id = #{couponTemplateId}
            </if>
            <if test="null != templateStartDate and templateStartDate !=''">
                AND pct.expiration_date_start &gt;= concat(#{templateStartDate}, ' 00:00:00.000')
            </if>
            <if test="null != templateEndDate and templateEndDate !=''">
                AND pct.expiration_date_end &lt;= concat(#{templateEndDate}, ' 23:59:59.999')
            </if>
            <!-- 0=进行中,1=已结束,2-未开始,3-已下架  -->
            <if test='activityStatus != null'>
                <if test="activityStatus == 0">
                    AND pct.status = '0'
                    AND pct.template_start_date &lt; NOW()
                    AND pct.template_end_date &gt; NOW()
                </if>
                <if test="activityStatus == 1">
                    AND pct.status IN ('0', '1')
                    AND pct.template_end_date &lt; NOW()
                </if>
                <if test="activityStatus == 2">
                    AND pct.status IN ('0', '1')
                    AND pct.template_start_date &gt; NOW()
                </if>
                <if test="activityStatus == 3">
                    AND pct.status IN ('2')
                </if>
            </if>
            ${params.dataScope}
        ORDER BY
            pct.coupon_template_id DESC
    </select>
    <select id="selectCouponExtendTotal" resultType="com.zksr.promotion.domain.PrmCouponTemplateExtend">
        SELECT
            SUM( CASE WHEN pc.state = 2 THEN 1 ELSE 0 END ) used_count,
            COUNT( 1 ) record_count,
            pc.coupon_template_id
        FROM
            prm_coupon pc
        WHERE
            pc.coupon_template_id IN
            <foreach collection="couponTemplateIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        GROUP BY
            pc.coupon_template_id
    </select>

    <!-- 优惠券模版客户领取列表 分页查询-->
    <select id="selectCouponExtendReceivePage"
            resultType="com.zksr.promotion.controller.template.dto.PrmCouponTemplateReceiveRespVO"
            parameterType="com.zksr.promotion.controller.template.vo.PrmCouponTemplateReceivePageVO">
        SELECT
            branch_id AS customerId
            ,COUNT(branch_id) AS receiveCount
            ,SUM(CASE WHEN state = 2 THEN 1 ELSE 0 END) AS useCount
            ,SUM(CASE WHEN state = 1 THEN 1 ELSE 0 END) AS surplusCount
            ,SUM(CASE WHEN state = 0 THEN 1 ELSE 0 END) AS obsoleteCount
        FROM prm_coupon
        WHERE
            coupon_template_id = #{couponTemplateId}
            <if test="null != customerId">
                AND branch_id = #{customerId}
            </if>
        GROUP BY
            branch_id
    </select>
</mapper>