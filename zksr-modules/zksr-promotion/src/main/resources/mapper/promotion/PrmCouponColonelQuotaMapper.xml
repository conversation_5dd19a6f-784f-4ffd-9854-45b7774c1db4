<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.promotion.mapper.PrmCouponColonelQuotaMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPage" resultType="com.zksr.promotion.domain.PrmCouponColonelQuota">
        <!-- 查询逻辑 -->
        SELECT
            guota1.coupon_colonel_quota_id,
            guota1.colonel_id,
            IFNULL(guota1.quota, guota0.quota) AS quota,
            guota1.month_id,
            guota1.sys_code,
            guota1.finish_quota
        FROM
            prm_coupon_colonel_quota guota1
            LEFT JOIN prm_coupon_colonel_quota guota0
            ON guota1.colonel_id = guota0.colonel_id
            AND guota0.quota_type = 0
        WHERE
            guota1.quota_type = 1
            AND guota1.month_id = #{currentMonthId}
        ORDER BY
            guota1.coupon_colonel_quota_id DESC
            LIMIT #{pageNo}, #{pageSize}
    </select>

    <select id="countPrmCouponColonelQuota" resultType="java.lang.Long">
        SELECT
           COUNT(*)
        FROM
            prm_coupon_colonel_quota guota1
                LEFT JOIN prm_coupon_colonel_quota guota0
                          ON guota1.colonel_id = guota0.colonel_id
                              AND guota0.quota_type = 0
        WHERE
            guota1.quota_type = 1
          AND guota1.month_id = #{currentMonthId}
    </select>

    <select id="selectQuotaHistory" resultType="com.zksr.promotion.controller.quota.vo.QuotaHistoryResponse">
        SELECT
            IFNULL(quota,0) AS quota,
            IFNULL(IFNULL(quota,0) - IFNULL(finish_quota,0),0) AS remainingQuota,
            month_id AS monthId
        FROM
            prm_coupon_colonel_quota
        WHERE
            colonel_id = #{colonelId}
          AND quota_type = 1
        ORDER BY
            month_id DESC
    </select>

    <select id="getColonelQuota" resultType="com.zksr.promotion.api.coupon.dto.ColonelQuotaDTO">
        SELECT
            colonel_id AS colonelId,
            IFNULL(quota,0) AS quota,
            IFNULL(finish_quota,0) AS finishQuota,
            IFNULL(quota,0) - IFNULL(finish_quota,0) AS remainingQuota
        FROM
            prm_coupon_colonel_quota
        WHERE
            colonel_id = #{colonelId}
          AND quota_type = 1
          AND month_id = #{monthId}
    </select>

    <select id="getAvailableCouponsForSalesperson" resultType="com.zksr.promotion.api.coupon.dto.CouponDTO">
        SELECT
            pc.coupon_id,
            pc.sys_code,
            pc.create_by,
            pc.create_time,
            pc.update_by,
            pc.update_time,
            pc.coupon_template_id,
            pc.branch_id,
            pc.receive_member_id,
            pc.state,
            pc.expiration_date_start,
            pc.expiration_date_end,
            pc.use_time,
            pc.relate_order_no,
            pc.spu_scope,
            pc.discount_type,
            pc.trigger_amt,
            pc.discount_amt,
            pc.discount_percent,
            pc.discount_limit_amt,
            pc.receive_type,
            pc.cost_flag,
            pc.excludable,
            pct.func_scope,
            pct.supplier_id
        FROM
            prm_coupon_template pct
            LEFT JOIN prm_coupon pc ON pct.coupon_template_id = pc.coupon_template_id
        WHERE
            template_end_date > NOW()
          AND del_flag = '0'
          AND receive_type = 5
          AND expiration_date_end > NOW()
          AND discount_type = 0;
    </select>

    <insert id="insertQuota">
        INSERT INTO prm_coupon_colonel_quota (sys_code, colonel_id, quota_type, quota, month_id, finish_quota)
        VALUES (#{sysCode}, #{colonelId}, #{quotaType}, #{quota}, #{monthId}, 0);
    </insert>

    <update id="updateTemplateQuota">
        UPDATE prm_coupon_colonel_quota
        SET quota = #{newQuota}, update_time = NOW()
        WHERE colonel_id = #{colonelId} AND quota_type = 0;
    </update>

    <update id="updateInstanceQuota">
        UPDATE prm_coupon_colonel_quota
        <set>
            <if test="newQuota != null">quota = #{newQuota},</if>
            <if test="finishQuota != null">finish_quota = #{finishQuota},</if>
            update_time = NOW()
        </set>
        WHERE colonel_id = #{colonelId} AND month_id = #{monthId} AND quota_type = 1;
    </update>
</mapper>