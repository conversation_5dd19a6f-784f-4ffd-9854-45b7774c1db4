<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.promotion.mapper.PrmCouponTemplateMapper">

    <select id="selectPageExt" resultType="com.zksr.promotion.controller.template.vo.PrmCouponTemplateRespVO">
        SELECT
            pct.coupon_template_id,
            pct.sys_code,
            pct.create_by,
            pct.create_time,
            pct.update_by,
            pct.update_time,
            pct.coupon_name,
            pct.func_scope,
            pct.status,
            pct.template_start_date,
            pct.template_end_date,
            pct.spu_scope,
            pct.spu_scope_apply_ids,
            pct.receive_scope,
            pct.receive_scope_apply_ids,
            pct.discount_type,
            pct.trigger_amt,
            pct.discount_amt,
            pct.discount_percent,
            pct.discount_limit_amt,
            pct.expiration_type,
            pct.expiration_date_start,
            pct.expiration_date_end,
            pct.disable_days,
            pct.expire_days,
            pct.receive_type,
            pct.cost_flag,
            pct.coupon_qty,
            pct.`limit`,
            pct.receive_count,
            pct.use_count,
            pct.excludable,
            pct.repeat_flag,
            pct.repeat_pid,
            pct.repeat_seq,
            pct.same_type_excludable
        FROM
            prm_coupon_template pct
        WHERE 1 = 1
            <if test='couponName != null and couponName != ""'>
                AND pct.coupon_name LIKE CONCAT('%', #{couponName}, '%')
            </if>
            <if test='couponTemplateId != null'>
                AND pct.coupon_template_id = #{couponTemplateId}
            </if>
            <if test='templateStartDate != null'>
                AND pct.template_start_date BETWEEN #{templateStartDate} AND #{templateEndDate}
            </if>
            <if test='discountType != null'>
                AND pct.discount_type = #{discountType}
            </if>
            <if test='spuScope != null'>
                AND pct.spu_scope = #{spuScope}
            </if>
            <if test='receiveScope != null'>
                AND pct.spu_scope = #{receiveScope}
            </if>
            <if test='funcScope != null'>
                AND pct.func_scope = #{funcScope}
            </if>
            <if test='supplierId != null'>
                AND pct.supplier_id = #{supplierId}
            </if>
            <if test='receiveType != null'>
                AND pct.receive_type = #{receiveType}
            </if>
            <!-- 0=进行中,1=已结束,2-未开始,3-已下架  -->
            <if test='activityStatus != null'>
                <if test="activityStatus == 0">
                    AND pct.status = '0'
                    AND pct.template_start_date &lt; NOW()
                    AND pct.template_end_date &gt; NOW()
                </if>
                <if test="activityStatus == 1">
                    AND pct.status IN ('0', '1')
                    AND pct.template_end_date &lt; NOW()
                </if>
                <if test="activityStatus == 2">
                    AND pct.status IN ('0', '1')
                    AND pct.template_start_date &gt; NOW()
                </if>
                <if test="activityStatus == 3">
                    AND pct.status IN ('2')
                </if>
            </if>
            ${params.dataScope}
        ORDER BY
            pct.coupon_template_id DESC
    </select>
</mapper>