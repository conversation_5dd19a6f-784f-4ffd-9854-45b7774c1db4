<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.promotion.mapper.PrmCouponMapper">

    <select id="selectBranchCouponListPage" resultType="com.zksr.promotion.api.coupon.dto.CouponDTO">
        SELECT
            pc.coupon_id,
            pc.sys_code,
            pc.create_by,
            pc.create_time,
            pc.update_by,
            pc.update_time,
            pc.coupon_template_id,
            pc.branch_id,
            pc.receive_member_id,
            pc.state,
            pc.expiration_date_start,
            pc.expiration_date_end,
            pc.use_time,
            pc.relate_order_no,
            pc.spu_scope,
            pc.discount_type,
            pc.trigger_amt,
            pc.discount_amt,
            pc.discount_percent,
            pc.discount_limit_amt,
            pc.receive_type,
            pc.cost_flag,
            pc.excludable,
            pct.coupon_name,
            pct.func_scope,
            pc.supplier_id,
            pc.same_type_excludable
        FROM
            prm_coupon pc
            LEFT JOIN prm_coupon_template pct ON pc.coupon_template_id = pct.coupon_template_id
        WHERE
            pc.state IN (1, 2)
            <if test="branchId != null">
                AND pc.branch_id = #{branchId}
            </if>
            <if test="couponBatchId!= null">
                AND pc.coupon_batch_id = #{couponBatchId}
            </if>
            <if test="state != null and state == 1">
                AND pc.state = 1
                AND pc.expiration_date_end > now()
            </if>
            <if test="state != null and state == 2">
                AND pc.state = 2
            </if>
            <if test="state != null and state == 3">
                AND pc.state = 1 AND pc.expiration_date_end &lt; now()
            </if>
        ORDER BY pc.coupon_id DESC
    </select>
    <select id="selectCouponBatchIds" resultType="com.zksr.promotion.api.coupon.dto.CouponDTO">
        SELECT
            pc.coupon_id,
            pc.sys_code,
            pc.create_by,
            pc.create_time,
            pc.update_by,
            pc.update_time,
            pc.coupon_template_id,
            pc.branch_id,
            pc.receive_member_id,
            pc.state,
            pc.expiration_date_start,
            pc.expiration_date_end,
            pc.use_time,
            pc.relate_order_no,
            pc.spu_scope,
            pc.discount_type,
            pc.trigger_amt,
            pc.discount_amt,
            pc.discount_percent,
            pc.discount_limit_amt,
            pc.receive_type,
            pc.cost_flag,
            pc.excludable,
            pct.func_scope,
            pct.supplier_id,
            pct.coupon_name
        FROM
            prm_coupon pc
            LEFT JOIN prm_coupon_template pct ON pc.coupon_template_id = pct.coupon_template_id
        WHERE
            pc.branch_id = #{branchId}
            AND pc.coupon_id IN
            <foreach collection="couponIdList" item="couponId" open="(" separator="," close=")">
                #{couponId}
            </foreach>
    </select>
    <select id="selectList" resultType="com.zksr.promotion.controller.coupon.vo.PrmCouponRespVO">
        SELECT
            pc.coupon_id,
            pc.sys_code,
            pc.create_by,
            pc.create_time,
            pc.update_by,
            pc.update_time,
            pc.coupon_template_id,
            pc.branch_id,
            pc.receive_member_id,
            pc.state,
            pc.expiration_date_start,
            pc.expiration_date_end,
            pc.use_time,
            pc.relate_order_no,
            pc.spu_scope,
            pc.discount_type,
            pc.trigger_amt,
            pc.discount_amt,
            pc.discount_percent,
            pc.discount_limit_amt,
            pc.receive_type,
            pc.cost_flag,
            pc.excludable,
            pct.coupon_name,
            pc.coupon_batch_id
        FROM
            prm_coupon pc
            INNER JOIN prm_coupon_template pct ON pc.coupon_template_id = pct.coupon_template_id
        <where>
            <if test="branchId != null">
                AND pc.branch_id = #{branchId}
            </if>
            <if test="couponTemplateId != null">
                AND pc.coupon_template_id = #{couponTemplateId}
            </if>
            <if test="sysCode != null">
                AND pc.sys_code = #{sysCode}
            </if>
            <if test="state != null and state == 1">
                AND pc.state = 1
                AND pc.expiration_date_end > now()
            </if>
            <if test="state != null and state == 2">
                AND pc.state = 2
            </if>
            <if test="state != null and state == 3">
                AND pc.state = 1 AND pc.expiration_date_end &lt; now()
            </if>
            <if test="state != null and state == 4">
                AND pc.state = 0
            </if>
            <if test="createTime != null">
                AND pc.create_time BETWEEN #{createTime} AND #{createTimeEnd}
            </if>
            <if test='couponName != null and couponName != ""'>
                AND pct.coupon_name LIKE CONCAT(#{couponName}, '%')
            </if>
            <if test='funcScope != null'>
                AND pct.func_scope = #{funcScope}
            </if>
            <if test='receiveType != null'>
                AND pct.receive_type = #{receiveType}
            </if>
            <if test='discountType != null'>
                AND pct.discount_type = #{discountType}
            </if>
            <if test='dcId != null'>
                AND pct.dc_id = #{dcId}
            </if>
            ${params.dataScope}
        </where>
        ORDER BY
            pc.coupon_id DESC
    </select>
    <select id="getCouponReceivedBranchs" resultType="java.lang.Long">
        SELECT
            branch_id
        FROM
            prm_coupon
        WHERE
            coupon_template_id = #{couponTemplateId}
          AND state != 0
            AND expiration_date_end > now()
    </select>
    <select id="getBranchReceiveQty" resultType="java.lang.Integer">
        SELECt
            COUNT(*)
        FROM
            prm_coupon
        WHERE
            coupon_template_id = #{couponTemplateId}
            AND branch_id = #{branchId}
            AND state != 0
            AND expiration_date_end > now()
    </select>

    <select id="getCouponByOrderNoAndCouponTemplateId" resultType="com.zksr.promotion.domain.PrmCoupon">
        SELECT
            pc.coupon_id,
            pc.sys_code,
            pc.create_by,
            pc.create_time,
            pc.update_by,
            pc.update_time,
            pc.coupon_template_id,
            pc.branch_id,
            pc.receive_member_id,
            pc.state,
            pc.expiration_date_start,
            pc.expiration_date_end,
            pc.use_time,
            pc.relate_order_no,
            pc.spu_scope,
            pc.discount_type,
            pc.trigger_amt,
            pc.discount_amt,
            pc.discount_percent,
            pc.discount_limit_amt,
            pc.receive_type,
            pc.cost_flag,
            pc.excludable,
            pc.coupon_batch_id
        FROM
            prm_coupon pc
        WHERE
            pc.relate_order_no = #{relateOrderNo}
            AND pc.coupon_template_id = #{couponTemplateId}
    </select>

</mapper>