<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.promotion.mapper.PrmCouponScopeApplyMapper">


    <select id="selectPrmCouponScopeApplyValidBySpuScope"
            resultType="com.zksr.promotion.domain.PrmCouponTemplate">
        SELECT
            pct.*
        FROM
            prm_coupon_scope_apply pcsa
            INNER JOIN prm_coupon_template pct ON pcsa.coupon_template_id = pct.coupon_template_id
        WHERE
            pcsa.apply_id = #{applyId}
            AND pcsa.spu_scope = #{spuScope}
            <!-- 0=正常,1未发布,2-下架 -->
            AND pct.status = 0
            AND pct.template_end_date &gt; NOW()
    </select>
</mapper>