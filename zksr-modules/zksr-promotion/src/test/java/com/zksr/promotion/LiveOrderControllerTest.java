package com.zksr.promotion;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.zksr.promotion.controller.room.PrmLiveOrderController;
import com.zksr.promotion.controller.room.PrmLiveProductController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @Author: chenyj8
 * @Desciption: 测试类
 */
@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallPromotionApplication.class)
public class LiveOrderControllerTest {
//    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private PrmLiveOrderController prmLiveOrderController;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(prmLiveOrderController)
                .setValidator(new LocalValidatorFactoryBean())
                .build();
    }

    @Test
//    @WithMockUser(username = "zksr", roles = {"member:branch:add"}) // 模拟一个有权限的用户
    public void testAdd() throws Exception {
        List<com.zksr.promotion.controller.order.vo.PrmLiveOrderSaveReqVO> createReqVOList = new ArrayList<>();
        com.zksr.promotion.controller.order.vo.PrmLiveOrderSaveReqVO reqVO = new com.zksr.promotion.controller.order.vo.PrmLiveOrderSaveReqVO();
        reqVO.setActivityId("ActivityId");
        reqVO.setSkuId(1111L);
        reqVO.setSpuId(1111L);
        reqVO.setLivePrice(BigDecimal.valueOf(1.0));
        reqVO.setUnit("a");
        reqVO.setUnitName("b");
        reqVO.setLiveProductId(1111L);

        createReqVOList.add(reqVO);
        mockMvc.perform(post("/order/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createReqVOList)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0)) // 假设CommonResult的code为0表示成功
//                .andExpect(jsonPath("$.data").isNumber())
                .andDo(MockMvcResultHandlers.print());
    }


}