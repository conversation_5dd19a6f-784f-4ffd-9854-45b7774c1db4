package com.zksr.promotion;

import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.base.BaseTest;
import com.zksr.promotion.controller.activity.vo.PrmActivityPageReqVO;
import com.zksr.promotion.controller.activity.vo.PrmActivityRespVO;
import com.zksr.promotion.service.impl.IPromotionCacheServiceImpl;
import com.zksr.promotion.service.impl.PrmActivityServiceImpl;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import javax.annotation.Resource;

@Slf4j
public class PrmActivityServiceTest extends BaseTest {
    @InjectMocks
    @Resource
    private PrmActivityServiceImpl prmActivityService;

    @InjectMocks
    @Resource
    private IPromotionCacheServiceImpl promotionCacheService;

    @Mock
    private SupplierApi supplierApi;

    @Mock
    private AreaApi areaApi;

    @Test
    public void testListActivity() {
        SupplierDTO supplierDTO = new SupplierDTO();
        supplierDTO.setSupplierName("测试入驻商");
        Mockito.doReturn(CommonResult.success(supplierDTO)).when(supplierApi).getBySupplierId(Mockito.any());

        AreaDTO areaDTO = new AreaDTO();
        areaDTO.setAreaName("测试地区");
        Mockito.doReturn(CommonResult.success(areaDTO)).when(areaApi).getAreaByAreaId(Mockito.any());

        PrmActivityPageReqVO pageReqVO = new PrmActivityPageReqVO();
        PageResult<PrmActivityRespVO> pageResult = prmActivityService.listActivity(pageReqVO);
        log.info("listActivity result: {}", JsonUtils.toJsonString(pageResult));

        Assertions.assertNotNull(pageResult, "result is null");
    }
}
