package com.zksr.promotion.mq;

//import com.midea.mbf.txmq.core.producer.TxProducer;
import com.zksr.promotion.ZksrMallPromotionApplication;
import com.zksr.promotion.service.MqCustomService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallPromotionApplication.class)
class CustomServiceTest {
    @Autowired
    private ApplicationContext applicationContext;

//    @Mock
//    private TxProducer txProducer;

    //@MockBean
    @Autowired
    private MqCustomService mqCustomService;

//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//    }

    @Test
    void testExecuteLocalTransaction() {
        // 准备测试数据
        String testMessage = "test message";
        String destination = "TOPIC-B2B-TEST";
        
        // 模拟消息构建
        Message<String> expectedMessage = MessageBuilder.withPayload(testMessage)
                .setHeader(RocketMQHeaders.TRANSACTION_ID, "transactionId")
                .build();

        // 执行测试
        mqCustomService.executeLocalTransaction(testMessage);

        // 验证
//        verify(txProducer).syncSend(eq(destination), eq(expectedMessage));
    }


    @Test
    public void testBeans() {
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        Arrays.stream(beanNames).forEach(System.out::println);
    }
} 