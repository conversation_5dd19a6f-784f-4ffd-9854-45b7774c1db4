package com.zksr.promotion;

import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import com.zksr.promotion.api.activity.dto.SupplierActivityDTO;
import com.zksr.promotion.service.IPrmActivityService;
import com.zksr.promotion.service.IPromotionCacheService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallPromotionApplication.class)
@Slf4j
public class ActivityPromotionApplicationTests {


    @Autowired
    private IPrmActivityService activityService;

    @Autowired
    private RedisActivityService redisActivityService;

    @Autowired
    private IPromotionCacheService promotionCacheService;

    @Resource
    private BranchApi branchApi;

    /*@Test
    public void testRedisService() throws InterruptedException {

        List<SupplierActivityDTO> supplierActivity = activityService.getSupplierActivity(15580493951L);
        for (SupplierActivityDTO activityDTO : supplierActivity) {
            List<ActivitySpuScopeDTO> spuScope = promotionCacheService.getSpuScope(activityDTO.getActivityId());
            if (Objects.nonNull(spuScope)) {
                List<Long> longs = spuScope.stream().map(ActivitySpuScopeDTO::getApplyId).collect(Collectors.toList());
                activityDTO.setSpuScopeList(new HashSet<>(longs));
            }
        }

        System.out.println(supplierActivity);
        List<ActivityVerifyItemDTO> itemList = new ArrayList<>();

        ActivityVerifyItemDTO item1 = new ActivityVerifyItemDTO();
        item1.setSpuId(474009968234397696L);
        item1.setSkuId(474009968234397697L);
        item1.setSupplierId(15580493951L);
        item1.setAreaItemId(474010041248841728L);
        item1.setBrandId(4L);
        item1.setCategoryId(13L);
        item1.setNum(12);
        item1.setItemPrice(new BigDecimal("0.03"));
        itemList.add(item1);

        redisActivityService.calculateActivity(
                branchApi.getByBranchId(466979106766028801L).getCheckedData(),
                supplierActivity,
                itemList
        );
    }*/


}
