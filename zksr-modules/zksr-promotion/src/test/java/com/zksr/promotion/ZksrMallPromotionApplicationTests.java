package com.zksr.promotion;

import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.api.model.event.EsSpuUpdateProductEvent;
import com.zksr.promotion.api.coupon.dto.CouponReceiveDTO;
import com.zksr.promotion.mq.PromotionMqProducer;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallPromotionApplication.class)
@Slf4j
public class ZksrMallPromotionApplicationTests {


    /*@Autowired
    private PromotionMqProducer promotionMqProducer;

    @Test
    public void testRedisService() throws InterruptedException {
        for (int i = 0; i < 10; i++) {
            CouponReceiveDTO item = new CouponReceiveDTO();
            item.setBranchId(System.currentTimeMillis());
            promotionMqProducer.sendCouponReceiveEvent(item);
        }
        Thread.sleep(10000L);
    }*/


}
