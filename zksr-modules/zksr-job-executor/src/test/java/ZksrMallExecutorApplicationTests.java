import com.zksr.jobexecutor.ZksrJobExecutorApplication;
import com.zksr.jobexecutor.task.*;
import com.zksr.member.api.branch.BranchApi;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrJobExecutorApplication.class)
@Slf4j
public class ZksrMallExecutorApplicationTests {

    /*@Autowired
    private CouponXxlJob xxlJob;

    @Test
    public void testRedisService() throws Exception {
        xxlJob.couponTotalExtendJobHandler();
    }*/

//    @Autowired
//    private ColonelXxlJob ColonelXxlJob;
//    @Test
//    public void  setColonelXxlJobtest1(){
//        ColonelXxlJob.colonelSeasHandler();
//    }


    @Autowired
    private OpenApiXxlJob xxlJob;

    @Autowired
    private MemberXxlJob memberXxlJob;

    @Autowired
    private BranchApi remoteBranchApi;

    @Test
    public void testOpenApiXxlJob() throws Exception {
        xxlJob.openApiDaySyncReportDataEmailJobHandler();
    }

    @Test
    public void testMemberXxlJob() throws Exception {
        //remoteBranchApi.refreshBranchLifecycleZipHandler(18L);
        memberXxlJob.refreshBranchLifecycleZipHandler();
    }
}
