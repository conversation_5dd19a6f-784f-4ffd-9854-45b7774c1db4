package com.zksr.jobexecutor.task;

import cn.hutool.core.util.ObjectUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.enums.RedisCouponConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.promotion.api.activity.ActivityApi;
import com.zksr.promotion.api.activity.dto.ActivityCbAreaUpdateDTO;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/1/4 11:06
 */
@Component
public class ActivityXxlJob {

    @Autowired
    private RedisService redisService;

    @Resource
    private ActivityApi activityApi;

    @Resource
    private PartnerApi partnerApi;

    @Resource
    private AreaApi areaApi;

    /**
     * 刷新有效的组合促销
     */
    @XxlJob("refreshCbActivity")
    public void refreshCbActivity() {
        synchronized (ActivityXxlJob.class) {
            int counter = 0;
            for (;;) {
                List<ActivityCbAreaUpdateDTO> areaList = redisService.removeCacheSetRandom(RedisActivityConstants.ACTIVITY_CB_AREA_UPDATE_SET, 10L);
                // 没有数据, 或者达到最大100次循环, 防止死循环
                if (ObjectUtil.isEmpty(areaList) || counter > 100) {
                    break;
                }
                counter++;
                // 轮询处理本地组合促销
                for (ActivityCbAreaUpdateDTO updateDTO : areaList.stream().filter(ActivityCbAreaUpdateDTO::local).collect(Collectors.toList())) {
                    XxlJobHelper.log("动态刷新平台商有效发布组合促销入驻商, areaId={}", updateDTO.getSysCode());
                    processCbActivity(RedisActivityConstants.ACTIVITY_CB_LOCAL_SUPPLIER_SET, updateDTO.getAreaId(), activityApi.getAreaCbSupplierIds(updateDTO.getAreaId()));
                }
                // 轮询处理全国组合促销
                for (ActivityCbAreaUpdateDTO updateDTO : areaList.stream().filter(ActivityCbAreaUpdateDTO::global).collect(Collectors.toList())) {
                    XxlJobHelper.log("动态刷新平台商有效发布组合促销入驻商, areaId={}", updateDTO.getSysCode());
                    processCbActivity(RedisActivityConstants.ACTIVITY_CB_GLOBAL_SUPPLIER_SET, updateDTO.getSysCode(), activityApi.getGlobalCbSupplierIds(updateDTO.getSysCode()));
                }
            }
        }
    }

    /**
     * 全量刷新有效的组合促销
     */
    @XxlJob("refreshAllCbActivity")
    public void refreshAllCbActivity() {
        synchronized (ActivityXxlJob.class) {
            List<PartnerDto> partnerDtos = partnerApi.getPartnerInfo().getCheckedData();
            for (PartnerDto partnerDto : partnerDtos) {
                List<AreaDTO> areaDTOList = areaApi.getListBySysCode(partnerDto.getSysCode()).getCheckedData();
                for (AreaDTO areaDTO : areaDTOList) {
                    XxlJobHelper.log("全量刷新城市有效发布组合促销入驻商, areaId={}", areaDTO.getAreaId());
                    processCbActivity(RedisActivityConstants.ACTIVITY_CB_LOCAL_SUPPLIER_SET, areaDTO.getAreaId(), activityApi.getAreaCbSupplierIds(areaDTO.getAreaId()));
                }
                XxlJobHelper.log("全量刷新平台商有效发布组合促销入驻商, areaId={}", partnerDto.getSysCode());
                processCbActivity(RedisActivityConstants.ACTIVITY_CB_GLOBAL_SUPPLIER_SET, partnerDto.getSysCode(), activityApi.getGlobalCbSupplierIds(partnerDto.getSysCode()));
            }
        }
    }


    private void processCbActivity(String prefixKey, Long applyId, CommonResult<List<Long>> activityApi) {
        String key = prefixKey + applyId;
        redisService.saveOrUpdateSet(key, new HashSet<>(activityApi.getCheckedData()));
    }

    /**
     * 刷新过期的组合促销 并下架关联商品
     */
    @XxlJob("refreshExpiredCbActivity")
    public void refreshExpiredCbActivity() {
        synchronized (ActivityXxlJob.class) {
            List<Long> expiredCbActivityIds = activityApi.getExpiredCbActivityIds().getCheckedData();
            if (ObjectUtil.isNotEmpty(expiredCbActivityIds)) {
                for (Long activityId : expiredCbActivityIds) {
                    XxlJobHelper.log("刷新过期的组合促销, activityId={}", activityId);
                    activityApi.stopCbActivity(activityId);
                }
            }
        }
    }
}
