package com.zksr.jobexecutor.task;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.common.core.domain.CustomMultipartFile;
import com.zksr.common.core.enums.request.EmailTemplateType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.file.api.file.FileApi;
import com.zksr.system.api.EmailMessage.EmailMessageApi;
import com.zksr.system.api.EmailMessage.dto.SyncEmailReportData;
import com.zksr.system.api.EmailMessage.dto.SyncEmailReportExcel;
import com.zksr.system.api.log.LogApi;
import com.zksr.system.api.opensource.OpensourceApi;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;

/**
* 对接第三方、OPENAPI开放接口 功能相关定时任务
* @date 2025/2/27 19:51
* <AUTHOR>
*/
@Component
@Slf4j
public class OpenApiXxlJob {

    @Autowired
    private PartnerApi partnerApi;

    @Autowired
    private OpensourceApi opensourceApi;

    @Autowired
    private EmailMessageApi emailMessageApi;

    @Autowired
    private LogApi logApi;

    @Autowired
    private FileApi fileApi;


    /**
     *  !@定时器 - 每天同步第三方数据情况汇总统计(日同步报告) - 按对接第三方入驻商发送邮件
     */
    @XxlJob("openApiDaySyncReportDataEmailJobHandler")
    public void openApiDaySyncReportDataEmailJobHandler() throws Exception {
        log.info(" ===============每天同步第三方数据情况汇总统计(日同步报告) - 按对接第三方入驻商发送邮件定时任务开始=================");
        //某个日期的参数 默认为昨天
        String param = XxlJobHelper.getJobParam();

        String date = null;
        //设置需要统计日同步报告日期
        if (ToolUtil.isNotEmpty(param)){
            //校验日期格式
            if(!DateUtils.isTimeString(param, YYYY_MM_DD)){
                log.info("每天同步第三方数据情况汇总统计(日同步报告) 异常：设置的报告日期格式不正确，请重新设置，当前格式为：{}",param);
                return;
            }
            date = param;
        }else{
            date = DateUtils.getDataByDayCount(NumberPool.LOWER_GROUND);
        }

        //文件名
        String fileName = date +"同步第三方数据情况汇总统计(日同步报告).xlsx";

        //文件路径
        String filePath = null;

        //获取平台商信息
        List<Long> sysCodeList = partnerApi.getPartnerInfo().getCheckedData().stream().map(PartnerDto::getSysCode).collect(Collectors.toList());

        //获取所有的开放能力信息 根据sysCode分组
        List<OpensourceDto> opensourceDtoList = opensourceApi.getOpenSourceBySysCode(null).getCheckedData();

        if(opensourceDtoList == null || opensourceDtoList.isEmpty()){
            log.info(" ===============未获取到开放能力配置信息，每天同步第三方数据情况汇总统计(日同步报告) - 按对接第三方入驻商发送邮件定时任务结束=================");
            return;
        }

        //过滤掉不存在平台商ID 、没配置推送日报告信息的 开放能力信息
        Map<Long, List<OpensourceDto>> opensourceMap = opensourceDtoList
                .stream().filter(x -> ToolUtil.isNotEmpty(x.getSysCode())
                        && ToolUtil.isNotEmpty(x.getAlarmEmail())
                        && ToolUtil.isNotEmpty(x.getSubscribeSendEmail())
                        && x.getSubscribeSendEmail().contains(String.valueOf(EmailTemplateType.DAY_SYNC_REPORT_DATA_EMAIL.getType()))).collect(Collectors.groupingBy(OpensourceDto::getSysCode));
        for (Long sysCode : sysCodeList) {
            //校验 该平台商下是否有对接第三方信息
            List<OpensourceDto> opensourceDtos = opensourceMap.get(sysCode);
            if(opensourceDtos == null || opensourceDtos.isEmpty()){
                log.info(" ===============该平台商下未配置统计日报告信息模板类型，跳过该平台商，平台商ID：{}=================",sysCode);
                continue;
            }

            //统计 日同步报告数据
            List<SyncEmailReportExcel> reportExcelList = logApi.getLogByDaySyncReport(sysCode, date).getCheckedData();
            if(reportExcelList == null || reportExcelList.isEmpty()){
                log.info(" ===============该平台商未生产同步日志，跳过该平台商，平台商ID：{}=================",sysCode);
                continue;
            }

            //生成文件、 上传云端
            // 使用 Excel 工具类导出数据
            ExcelUtil<SyncEmailReportExcel> excelUtil = new ExcelUtil<>(SyncEmailReportExcel.class);
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                excelUtil.exportExcel(baos, reportExcelList, fileName);

                // 将 ByteArrayOutputStream 转换为 MultipartFile
                MultipartFile file = new CustomMultipartFile(fileName, baos.toByteArray());
                // 上传文件到服务器
                filePath = fileApi.uploadFileByMail(file).getCheckedData();

                //校验文件路径是否有值
                if (ToolUtil.isEmpty(filePath)) {
                    log.info("发送同步日志日同步报告-发送邮件通知,文件路径为空，不推送邮件。平台商ID：{}",sysCode);
                    continue;
                }

            } catch (Exception e) {
                log.error("文件生成或上传失败，平台商ID：{}", sysCode, e);
                continue;
            }

            //发送邮件
            //设置邮件参数
            SyncEmailReportData reportData = new SyncEmailReportData();
            reportData.setFileName(fileName);
            reportData.setSendCount(reportExcelList.stream().map(SyncEmailReportExcel::getSendCount).reduce(0, Integer::sum));
            reportData.setFailCount(reportExcelList.stream().map(SyncEmailReportExcel::getFailCount).reduce(0, Integer::sum));
            reportData.setFileUrl(filePath);
            //给改平台商下对接第三方的所有入驻商发送邮件
            //设置对接了第三方的入驻商的开放能力信息
            reportData.setOpensourceDtoList(opensourceDtos);
            emailMessageApi.sendDaySyncReportDataEmail(reportData);
        }
        log.info(" ===============完成每天同步第三方数据情况汇总统计(日同步报告) - 按对接第三方入驻商发送邮件定时任务结束=================");
    }

}
