package com.zksr.jobexecutor.task;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.domain.dto.car.AppCarInfoDTO;
import com.zksr.common.core.domain.dto.car.AppCarItemDTO;
import com.zksr.common.core.domain.vo.car.AppCarPageReqVO;
import com.zksr.common.core.domain.vo.car.AppCarPageRespVO;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.redis.enums.RedisCarConstants;
import com.zksr.common.redis.service.RedisCarService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.trade.api.car.CarApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车定时处理缓存数据
 * @date 2024/6/27 14:36
 */
@Component
@Slf4j
public class CarXxlJob {

    @Resource
    private BranchApi branchApi;

    @Resource
    private CarApi carApi;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedisCarService redisCarService;

    /**
     * 从redis中保存购物车到数据库
     */
    @XxlJob("saveRedisCar")
    public void saveRedisCar() {
        Long minBranchId = -1L;
        for (;;) {
            // 循环获取ID
            List<Long> branchIdList = branchApi.getAllBranchIdList(minBranchId).getCheckedData();
            if (branchIdList.isEmpty()) {
                return;
            }
            for (Long branchId : branchIdList) {
                // 循环保存购物车数据
                carApi.saveRedisCar(branchId).checkError();
            }
            // 重载最小ID
            minBranchId = branchIdList.get(branchIdList.size() - 1);
        }

    }

    /**
     * 移除购物车加单指令
     */
    @XxlJob("removeRecommend")
    public void removeRecommend() {
        DateTime today = DateUtil.parseDate(DateUtil.today());
        DateTime yesterday = DateUtil.yesterday();
        String yesterdayKey = RedisCarConstants.getRecommendBranchList(DateUtil.formatDate(yesterday));
        for (;;) {
            // 获取业务员加单门店集合
            // 获取昨天的加单请求, 需要移除
            Set<Long> branchList = redisService.getCacheZSetRange(yesterdayKey, 0, 100L);
            if (ObjectUtil.isEmpty(branchList)) {
                return;
            }
            for (Long branchId : branchList) {
                // 购物车统计信息
                AppCarInfoDTO carInfo = redisCarService.getCarInfo(branchId);
                if (Objects.nonNull(carInfo) && Objects.nonNull(carInfo.getCommendTime()) && carInfo.getCommendTime().getTime() < today.getTime()) {
                    carInfo.setShowWindow(false);
                    carInfo.setCommendTime(null);
                    carInfo.setCommandMemo(null);

                    // 移除购物车业务员加单指令
                    String infoKey = RedisCarConstants.getInfoKey(branchId);
                    redisService.deleteObject(infoKey);
                    redisService.setCacheMap(infoKey, carInfo.toMap());
                }

                AppCarPageReqVO req = new AppCarPageReqVO();
                req.setBranchId(branchId);
                req.setProductType(ProductType.LOCAL.getType());
                req.setPageNo(1);
                req.setPageSize(300);
                AppCarPageRespVO appCarPageRespVO = redisCarService.getAppCarItemListPage(req);
                List<AppCarIdDTO> carIds = new ArrayList<>();
                for (AppCarItemDTO appCarItemDTO : appCarPageRespVO.getList()) {
                    // 是否是加单商品, 判断是否过期
                    if (Objects.nonNull(appCarItemDTO.getDoubleOrder()) && appCarItemDTO.getDoubleOrder() < today.getTime()) {
                        carIds.add(appCarItemDTO);
                    }
                }
                if (!carIds.isEmpty()) {
                    redisCarService.removeItemList(branchId, carIds, ListUtil.toList(ProductType.LOCAL.getType()));
                }
                // 移除加单清单
                redisService.zSetRem(yesterdayKey, branchId);
            }
        }
    }
}
