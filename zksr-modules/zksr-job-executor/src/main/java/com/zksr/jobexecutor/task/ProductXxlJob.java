package com.zksr.jobexecutor.task;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.jobexecutor.service.IJobCacheService;
import com.zksr.product.api.adjustPrice.AdjustPriceApi;
import com.zksr.product.api.areaItem.AreaItemApi;
import com.zksr.product.api.content.ProductContentApi;
import com.zksr.product.api.content.dto.ReleaseItemChangeEventDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.supplierItem.SupplierItemApi;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品调度任务
 * @date 2024/6/12 9:13
 */
@Component
@Slf4j
public class ProductXxlJob {

    @Resource
    private AreaItemApi areaItemApi;

    @Resource
    private SkuApi skuApi;

    @Resource
    private SupplierItemApi supplierItemApi;

    @Resource
    private ProductContentApi productContentApi;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IJobCacheService jobCacheService;

    @Resource
    private PartnerApi partnerApi;

    @Resource
    private AdjustPriceApi adjustPriceApi;

    @Resource
    private AreaApi areaApi;

    /**
     * 统计SKU 库存已售
     */
    @XxlJob("skuSaleQtyJobHandler")
    public void skuSaleQtyJobHandler(){
        // 只会刷新上架的商品已售数量
        // 查询更新时间在15天内的上架SKUID集合
        {
            Long minId = NumberPool.LOWER_GROUND_LONG;
            XxlJobHelper.log("开始统计本地商品上架SKU");
            for (;;) {
                minId = areaItemApi.updateSaleQtyTotal(minId).getCheckedData();
                // 如果返回的最小ID 等于 -1, 则判断为已结束
                if (minId.equals(NumberPool.LOWER_GROUND_LONG)) {
                    break;
                }
            }
        }
        {
            Long minId = NumberPool.LOWER_GROUND_LONG;
            XxlJobHelper.log("开始统计全国商品上架SKU");
            for (;;) {
                minId = supplierItemApi.updateSaleQtyTotal(minId).getCheckedData();
                // 如果返回的最小ID 等于 -1, 则判断为已结束
                if (minId.equals(NumberPool.LOWER_GROUND_LONG)) {
                    break;
                }
            }
        }
    }


    /**
     * 刷新商品列表已售
     */
    @XxlJob("refreshEsSaleQty")
    public void refreshEsSaleQty() throws InterruptedException {
        // 获取数据之前先切换交换区
        XxlJobHelper.log("开始刷新es销量数据");
        String cacheKey = redisStockService.changeSkuStockArea();
        XxlJobHelper.log("已成功切换SKU交换区");
        XxlJobHelper.log("休息8秒等待缓存刷新");
        Thread.sleep(8000);
        XxlJobHelper.log("准备处理数据变更");
        for (;;) {
            Set<Long> stockUpdateList = redisStockService.getSkuStockUpdateList();
            if (Objects.isNull(stockUpdateList) || stockUpdateList.isEmpty()) {
                XxlJobHelper.log("未获取到有效数据");
                return;
            }
            // 更新已售
            Boolean result = skuApi.updateSaleQtyEvent(ListUtil.toList(stockUpdateList)).getCheckedData();
            if (!result) {
                // 直接return掉, 商品服务故障了
                return;
            }
            // 计算SKU关联的组合促销
            List<Long> spuCombineList = skuApi.getRelationSpuCombineList(ListUtil.toList(stockUpdateList)).getCheckedData();
            if (ObjectUtil.isNotEmpty(spuCombineList)) {
                productContentApi.sendSpuCombineEvent(spuCombineList).checkError();
            }
            // 转换成SPU_ID, 因为ES需要将SPU下所有SKU有库存的展示在前面
            Set<Long> spuIdList = stockUpdateList
                    .stream()
                    .map(jobCacheService::getSkuDTO)
                    .filter(Objects::nonNull)
                    .map(SkuDTO::getSpuId)
                    .collect(Collectors.toSet());
            XxlJobHelper.log("开始处理数据批次数量 : {}", stockUpdateList.size());
            Boolean success = productContentApi.sendSpuEvent(ListUtil.toList(spuIdList)).getCheckedData();
            if (success) {
                // 删除数据
                stockUpdateList.forEach(skuId -> redisService.zSetRem(cacheKey, skuId));
            }
        }
    }

    /**
     * 商品调价单定时调价 - 定时任务
     */
    @XxlJob("adjustPricesTaskExecuteStatusJobHandler")
    public void adjustPricesTaskExecuteStatusJobHandler() {
        log.info(" ===============商品调价单定时调价 - 定时任务=================");
        String param = XxlJobHelper.getJobParam();

        List<Long> sysCodeList = new ArrayList<>();
        if (ToolUtil.isNotEmpty(param)) {
            sysCodeList.add(Long.parseLong(param));
        } else {
            // 获取所有的平台商信息
            sysCodeList = partnerApi.getPartnerInfo().getCheckedData().stream().map(PartnerDto::getSysCode).collect(Collectors.toList());
        }

        Date nowDate = DateUtils.getNowDate();

        sysCodeList.forEach(sysCode -> {
            // 执行 商品调价单定时调价任务代码
            adjustPriceApi.operateValidJob(sysCode,DateUtils.getNowDate());
        });
        log.info(" ===============完成商品调价单定时调价 - 定时任务=================");
    }

    /**
     * 定时处理刷新城市下没有上架商品的展示分类
     */
    @XxlJob("refreshNoReleaseClassCls")
    public void refreshNoReleaseClassCls(){
        // 上架城市不会太多太多, 撑死几百条?
        // 如果数据数据太多, 考虑对redis key 拆分平台商处理
        synchronized (ProductXxlJob.class) {
            Set<ReleaseItemChangeEventDTO> changeEventDTOSet = redisService.getCacheSetMembers(RedisConstants.RELEASE_CITY_CHANGE_SET);
            if (Objects.nonNull(changeEventDTOSet)) {
                // 遍历处理城市数据
                for (ReleaseItemChangeEventDTO changeEventDTO : changeEventDTOSet) {
                    try {
                        if(null == changeEventDTO.getAreaId()){
                            continue;
                        }
                        productContentApi.updateAreaReleaseData(changeEventDTO.getAreaId()).checkError();
                        redisService.remCacheSet(RedisConstants.RELEASE_CITY_CHANGE_SET, changeEventDTO);
                    } catch (Exception e) {
                        log.error("{}定时处理刷新城市下没有上架商品的展示分类失败,", changeEventDTO.getAreaId(), e);
                    }
                }
                Map<Long, List<ReleaseItemChangeEventDTO>> areaMap = changeEventDTOSet.stream().collect(Collectors.groupingBy(ReleaseItemChangeEventDTO::getAreaId));
                if (areaMap.containsKey(Long.parseLong(StringPool.DEFAULT))) {
                    for (ReleaseItemChangeEventDTO changeEventDTO : areaMap.get(Long.parseLong(StringPool.DEFAULT))) {
                        try {
                            // 如果有-1, 表示有全国商品需要处理
                            productContentApi.updateGlobalReleaseData(changeEventDTO.getSysCode()).checkError();
                        } catch (ServiceException e) {
                            log.error("{}定时处理刷新城市下没有上架商品的展示分类失败1,", changeEventDTO.getSysCode(), e);
                        }
                    }
                }
            }
        }
    }

    /**
     * 全量刷新城市下没有上架商品的展示分类
     */
    @XxlJob("refreshAllNoReleaseClassCls")
    public void refreshAllNoReleaseClassCls(){
        synchronized (ProductXxlJob.class) {
            List<PartnerDto> partnerDtos = partnerApi.getPartnerInfo().getCheckedData();
            for (PartnerDto partnerDto : partnerDtos) {
                List<AreaDTO> areaDTOList = areaApi.getListBySysCode(partnerDto.getSysCode()).getCheckedData();
                for (AreaDTO areaDTO : areaDTOList) {
                    // 本地商品处理
                    productContentApi.updateAreaReleaseData(areaDTO.getAreaId()).checkError();
                }
                // 全国商品处理
                productContentApi.updateGlobalReleaseData(partnerDto.getSysCode()).checkError();
            }
        }
    }
}
