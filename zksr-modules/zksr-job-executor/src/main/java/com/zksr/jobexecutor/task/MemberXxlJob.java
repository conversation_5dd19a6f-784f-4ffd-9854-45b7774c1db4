package com.zksr.jobexecutor.task;

import cn.hutool.core.collection.ListUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.record.StyleRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.STATE_DISABLE;
import static com.zksr.common.core.constant.StatusConstants.STATE_ENABLE;

/**
 *
 * 用户模块定时任务
 * <AUTHOR>
 * @date 2024/4/17 19:25
 */
@Component
@Slf4j
public class MemberXxlJob {
    @Autowired
    private PartnerApi remotePartnerApi;

    @Autowired
    private MemberApi remoteMemberApi;

    @Autowired
    private BranchApi remoteBranchApi;

    @Autowired
    private OrderApi orderApi;

    /**
     * 定时停用已过期的用户、门店信息
     */
    @XxlJob("memberAndBranchExpirationDateJobHandler")
    public void memberAndBranchExpirationDateJobHandler(){
        log.info("开始执行：停用已过期的用户、门店信息");
        try{
            //查询所有平台商
            List<PartnerDto> partnerDtos = new ArrayList<>();
            partnerDtos   = remotePartnerApi.getPartnerInfo().getCheckedData();
            //根据平台商查询过期门店、用户
            partnerDtos.forEach(partner ->{
                Long sysCode = partner.getSysCode();
                //查询所有已启用、有过期时间的门店信息
                CommonResult<List<MemberDTO>> expirationMemberResult = remoteMemberApi.getAllExpirationDateMemberList(sysCode);
                if(expirationMemberResult.isSuccess()){
                    List<MemberDTO> expirationMemberList = expirationMemberResult.getData();
                    if(ToolUtil.isNotEmpty(expirationMemberList)){
                        // 根据用户编号查询订单信息是否下过单 ， 将下过单的用户 状态设置为 启用 过期时间设置 为 null
                        List<Long> enableMemberIds = orderApi.checkMemberOrBranchExistsOrderSaleInfo("member", sysCode, expirationMemberList.stream().map(MemberDTO::getMemberId).collect(Collectors.toList())).getCheckedData();
                        if (ToolUtil.isNotEmpty(enableMemberIds) && !enableMemberIds.isEmpty()) {
                            remoteMemberApi.updateStatusByMemberIds(new MemberDTO(sysCode,STATE_ENABLE,enableMemberIds)).getCheckedData();
                        }

                        // 将没有下过单的用户 状态设置为 停用 且 过期时间为 null
                        List<Long> disableMemberIds = expirationMemberList.stream()
                                .map(MemberDTO::getMemberId)
                                .filter(memberId -> !enableMemberIds.contains(memberId)).collect(Collectors.toList());
                        if (ToolUtil.isNotEmpty(disableMemberIds) && !disableMemberIds.isEmpty()) {
                            remoteMemberApi.updateStatusByMemberIds(new MemberDTO(sysCode,STATE_DISABLE,disableMemberIds)).getCheckedData();
                        }

                    }
                }
                //查询所有已启用、有过期时间的门店信息
                CommonResult<List<BranchDTO>> expirationBranchResult = remoteBranchApi.getAllExpirationDateBranchList(sysCode);
                if(expirationBranchResult.isSuccess()){
                    List<BranchDTO> expirationBranchList = expirationBranchResult.getData();
                    if(ToolUtil.isNotEmpty(expirationBranchList)){
                        // 根据门店Id查询订单信息是否下过单 ， 将下过单的门店 状态设置为 启用 过期时间设置 为 null
                        List<Long> enableBranchIds = orderApi.checkMemberOrBranchExistsOrderSaleInfo("member", sysCode, expirationBranchList.stream().map(BranchDTO::getBranchId).collect(Collectors.toList())).getCheckedData();
                        if (ToolUtil.isNotEmpty(enableBranchIds) && !enableBranchIds.isEmpty()) {
                            remoteBranchApi.updateStatusByBranchIds(new BranchDTO(sysCode,STATE_ENABLE,enableBranchIds)).getCheckedData();
                        }

                        // 将没有下过单的门店 状态设置为 停用 且 过期时间为 null
                        List<Long> disableBranchIds = expirationBranchList.stream()
                                .map(BranchDTO::getBranchId)
                                .filter(branchId -> !enableBranchIds.contains(branchId)).collect(Collectors.toList());
                        if (ToolUtil.isNotEmpty(disableBranchIds) && !disableBranchIds.isEmpty()) {
                            remoteBranchApi.updateStatusByBranchIds(new BranchDTO(sysCode,STATE_DISABLE,disableBranchIds)).getCheckedData();
                        }
                    }
                }

            });
        }catch (Exception e){
            log.error("执行停用已过期的用户、门店信息功能失败,", e);

        }

        log.info("执行成功：停用已过期的用户、门店信息");
    }

    /**
     * 每月月初1号 凌晨12：01分 刷新ES门店下单数据信息 将上月门店的销售信息添加到本月的ES中
     */
    @XxlJob("refreshEsBranchSalesInfoHandler")
    public void refreshEsBranchSalesInfoHandler(){
        log.info("开始执行：刷新ES门店下单数据信息");
        try{
            //查询所有平台商
            List<PartnerDto> partnerDtos = remotePartnerApi.getPartnerInfo().getCheckedData();
            //根据平台商查询过期门店、用户
            partnerDtos.forEach(partner ->{
                try {
                    remoteBranchApi.refreshEsBranchSalesInfoJobHandler(partner.getSysCode());
                } catch (Exception e) {
                    log.error("执行【刷新ES门店下单数据信息】功能失败：" + e.getMessage());
                }

            });
        }catch (Exception e){
            log.error("执行【刷新ES门店下单数据信息】功能失败：", e);

        }

        log.info("执行成功：刷新ES门店下单数据信息");
    }


    /**
     * 	根据日期区间初始化 ES门店下单数据信息
     */
    @XxlJob("initEsBranchSalesInfoHandler")
    public void initEsBranchSalesInfoHandler(){

        log.info(" ===============进入初始化 ES门店下单数据信息=================");
        String param = XxlJobHelper.getJobParam();
        System.out.println("-----------------" + param);
        String [] data = param.split(";");

        if (ToolUtil.isEmpty(data) || !DateUtils.isTimeString(data[1], DateUtils.YYYY_MM) || !DateUtils.isTimeString(data[2], DateUtils.YYYY_MM)) {
            log.error(" ===============初始化 ES门店下单数据信息 参数错误:{} =================", param);
            return;
        }

        // 1、获取所有的平台商信息
        List<Long> sysCodeList = new ArrayList<>();
        if (ToolUtil.isNotEmpty(data[0])) {
            sysCodeList.add(Long.parseLong(data[0]));
        } else {
            sysCodeList = remotePartnerApi.getPartnerInfo().getCheckedData().stream().map(PartnerDto::getSysCode).collect(Collectors.toList());
        }
        sysCodeList.forEach(sysCode -> {
            remoteBranchApi.initEsBranchSalesInfoHandler(sysCode, data[1], data[2]);
        });

        log.info(" ===============完成 初始化 ES门店下单数据信息=================");
    }

    /**
     * 	初始化门店GEO数据
     */
    @XxlJob("initEsBranchGeoInfoHandler")
    public void initEsBranchGeoInfoHandler(){
        log.info(" ===============进入初始化 ES门店下单数据信息=================");
        Long minBranchId = -1L;
        for (;;) {
            // 循环获取ID
            List<Long> branchIdList = remoteBranchApi.getAllBranchIdList(minBranchId).getCheckedData();
            if (branchIdList.isEmpty()) {
                break;
            }
            for (Long branchId : branchIdList) {
                // 循环保存购物车数据
                remoteBranchApi.refreshEsBranchBase(branchId);
            }
            // 重载最小ID
            minBranchId = branchIdList.get(branchIdList.size() - 1);
        }
        log.info(" ===============完成 初始化 ES门店下单数据信息=================");
    }

    /**
     * 	刷新门店生命周期
     */
    @XxlJob("refreshBranchLifecycleZipHandler")
    public void refreshBranchLifecycleZipHandler(){
        log.info(" ===============进入 刷新门店生命周期 数据信息=================");
        String param = XxlJobHelper.getJobParam();

        // 1、获取所有的平台商信息
        List<Long> sysCodeList;
        if (ToolUtil.isNotEmpty(param)) {
            sysCodeList = Arrays.stream(param.split(";")).map(Long::parseLong).collect(Collectors.toList());
        } else {
            sysCodeList = remotePartnerApi.getPartnerInfo().getCheckedData().stream().map(PartnerDto::getSysCode).collect(Collectors.toList());
        }

        //刷新各个平台商下的门店生命周期
        sysCodeList.forEach(sysCode -> {
            remoteBranchApi.refreshBranchLifecycleZipHandler(sysCode);
        });

        log.info(" ===============完成 刷新门店生命周期 数据信息=================");
    }

}
