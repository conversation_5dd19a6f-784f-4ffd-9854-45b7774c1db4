package com.zksr.jobexecutor.service;

import com.zksr.common.redis.bean.OrderSettingPolicyCacheBean;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.partnerPolicy.dto.OrderSettingPolicyDTO;

/**
 * <AUTHOR>
 * @date 2024年04月09日 17:14
 * @description: IJobCacheService
 */
public interface IJobCacheService {
    /**
     * 根据DcId查询出运营商订单参数配置
     * @param dcId
     * @return
     */
    public OrderSettingPolicyDTO getOrderSettingPolicyInfo(Long dcId);

    /**
     * 获取SPU
     * @param spuId
     * @return
     */
    public SpuDTO getSpuDTO(Long spuId);

    /**
     * 获取SKU
     * @param skuId
     * @return
     */
    public SkuDTO getSkuDTO(Long skuId);
}
