package com.zksr.jobexecutor.task;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.common.core.pool.NumberPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付商户调度器
 * @date 2024/7/24 19:30
 */
@Component
@Slf4j
public class PlatformMerchantXxlJob {

    @Resource
    private PlatformMerchantApi platformMerchantApi;

    /**
     * 更新支付平台商户进件状态信息
     */
    @XxlJob("syncPlatformMerchantProcessingJobHandler")
    public void syncPlatformMerchantProcessingJobHandler() {
        Long minId = NumberPool.LOWER_GROUND_LONG;
        XxlJobHelper.log("开始处理支付商户进件状态查询");
        for (;;) {
            // 从最小的开始处理
            Long batchMaxId = platformMerchantApi.processMerchantStatus(minId).getCheckedData();
            if (Objects.isNull(batchMaxId)) {
                break;
            }
            minId = batchMaxId;
            XxlJobHelper.log("批次处理节点");
        }
    }

}
