package com.zksr.jobexecutor.task;

import com.alibaba.fastjson2.JSON;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.account.api.pay.PayFlowApi;
import com.zksr.account.model.pay.vo.PayRefundQueryVO;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.vo.openapi.OrderOpenDTO;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.service.RedisCacheService;
import com.zksr.trade.api.order.vo.O2OSettleTaskParamVO;
import com.zksr.jobexecutor.service.IJobCacheService;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerPolicy.dto.OrderSettingPolicyDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.TrdOrderTakeDeliveryVO;
import com.zksr.trade.api.order.vo.TrdOrderInfo;
import com.zksr.trade.api.supplierOrder.SupplierOrderApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import com.zksr.common.core.exception.ServiceException;

/**
 * <AUTHOR>
 * @date 2024年04月09日 16:53
 * @description: OrderXxlJob
 */
@Component
@Slf4j
public class OrderXxlJob {

    @Autowired
    private RedisCacheService redisCacheService;

    @Autowired
    private IJobCacheService jobCacheService;

    @Resource
    private OrderApi orderApi;

    @Resource
    private PartnerApi partnerApi;

    @Resource
    private DcApi dcApi;

    @Resource
    private PayFlowApi payFlowApi;

    @Resource
    private SupplierOrderApi supplierOrderApi;


    /**
     * !@定时器 - 0、订单取消定时任务
     */
    @XxlJob("orderCancelJobHandler")
    public void orderCancelJobHandler() {
        log.info(" ===============进入订单取消定时任务=================");
        Set<TrdOrderInfo> set = redisCacheService.getCacheOrderInfo();
        Map<Long, List<TrdOrderInfo>> orderInfoMap = set.stream().collect(Collectors.groupingBy(TrdOrderInfo::getDcId));  //根据运营商ID进行分组
        orderInfoMap.forEach((key, value) ->{
            OrderSettingPolicyDTO orderSetting = jobCacheService.getOrderSettingPolicyInfo(key);
            double orderValidity = 30 * 60;//默认30分钟

            if (ToolUtil.isNotEmpty(orderSetting) && ToolUtil.isNotEmpty(orderSetting.getOrderExpiryDate()) && ToolUtil.isNotEmpty(orderSetting.getOrderExpiryDateType())) {
                double orderDate = Double.parseDouble(orderSetting.getOrderExpiryDate());
                int orderDateType = Integer.parseInt(orderSetting.getOrderExpiryDateType());
                orderValidity = orderDate * (orderDateType == StatusConstants.TIME_HOUR ? 60 : 1) * 60;
            }
            for (TrdOrderInfo order : value) {
                long now = System.currentTimeMillis();
                long createTime = order.getCreateTime();
                if (now - createTime > orderValidity * 1000) {
                    try {
                        //获取入驻商订单信息
                        List<OrderOpenDTO> supplierOrderList = supplierOrderApi.getSupplierOrderListByOrderNo(order.getOrderNo()).getCheckedData();
                        if(CollectionUtils.isNotEmpty(supplierOrderList)){
                            for (OrderOpenDTO supplierOrder : supplierOrderList){
                                try {
                                    orderApi.orderCancel(supplierOrder.getSupplierOrderNo()).getCheckedData();
                                    log.info("入驻商订单号：{}，订单取消成功", supplierOrder.getSupplierOrderNo());
                                } catch (Exception e) {
                                    log.info("入驻商订单号：{}，订单取消失败，", supplierOrder.getSupplierOrderNo(), e);
                                }

                            }
                        }
//                        orderApi.orderCancel(order.getOrderNo()).getCheckedData();
//                        log.info("订单号：{}，订单取消成功", order.getOrderNo());
                    } catch (Exception e){
                        log.info("订单号：{}，订单取消失败，原因{}", order.getOrderNo(), e.getMessage());
                    }

                }
            }
        });
        log.info(" ===============完成订单取消定时任务=================");
    }

    /**
     * !@定时器 - 2、单据自动确认收货定时任务
     */
    @XxlJob("orderTakeDeliveryJobHandler")
    public void orderTakeDeliveryJobHandler(){
        log.info(" ===============进入单据自动确认收货定时任务=================");
        TrdOrderTakeDeliveryVO takeDeliveryDTO = new TrdOrderTakeDeliveryVO();
        takeDeliveryDTO.setDeliveryState(DeliveryStatusEnum.WAIT_SH.getCode());
        takeDeliveryDTO.setNowDate(DateUtils.getNowDate());
        takeDeliveryDTO.setItemType(StatusConstants.ITEM_TYPE_0);
        // 1、获取所有的平台商信息
       List<PartnerDto> partnerDtos = partnerApi.getPartnerInfo().getCheckedData();
       partnerDtos.stream().forEach(partner -> {
           takeDeliveryDTO.setSysCode(partner.getSysCode());
           // 2、根据平台商编号查询该平台下所有运营商信息
           List<DcDTO> dcList = dcApi.getDcBySysCode(partner.getSysCode()).getCheckedData();
           dcList.stream().forEach(dc -> {
               // 3、根据平台商查询出 定时收货时间配置
               //!@定时器 - 2、单据自动确认收货定时任务 - 1、根据平台下所有的运营商，找出所有的运营商订单配置
               OrderSettingPolicyDTO orderSetting = jobCacheService.getOrderSettingPolicyInfo(dc.getDcId());
               Integer orderDate = 72;  // 默认3天
               String orderDateType = "0";

               if (ToolUtil.isNotEmpty(orderSetting) && ToolUtil.isNotEmpty(orderSetting.getOrderVerifyTime()))
                   orderDate = Integer.parseInt(orderSetting.getOrderVerifyTime()); // 时间

               if (ToolUtil.isNotEmpty(orderSetting) && ToolUtil.isNotEmpty(orderSetting.getOrderVerifyTimeType()))
                   orderDateType = orderSetting.getOrderVerifyTimeType(); // 时间类型  0：小时 1：分钟


               takeDeliveryDTO.setDcId(dc.getDcId());
               takeDeliveryDTO.setOperDate(orderDate);
               takeDeliveryDTO.setOperDateType(orderDateType);

               orderApi.orderTakeDelivery(takeDeliveryDTO).getCheckedData();
           });
       });
        log.info(" ===============完成单据自动确认收货定时任务=================");
    }

    /**
     * !@定时器 - 3、单据自动确认完成定时任务
     */
    @XxlJob("orderCompleteJobHandler")
    public void orderCompleteJobHandler(){
        log.info(" ===============进入单据自动确认完成定时任务=================");
        TrdOrderTakeDeliveryVO takeDeliveryDTO = new TrdOrderTakeDeliveryVO();
        takeDeliveryDTO.setDeliveryState(DeliveryStatusEnum.COMPLETE_SH.getCode());
        takeDeliveryDTO.setNowDate(DateUtils.getNowDate());
        // 1、获取所有的平台商信息
        List<PartnerDto> partnerDtos = partnerApi.getPartnerInfo().getCheckedData();
        partnerDtos.stream().forEach(partner -> {
            takeDeliveryDTO.setSysCode(partner.getSysCode());
            // 2、根据平台商编号查询该平台下所有运营商信息
            List<DcDTO> dcList = dcApi.getDcBySysCode(partner.getSysCode()).getCheckedData();
            dcList.stream().forEach(dc -> {
                // 3、根据平台商查询出 定时收货时间配置
                //!@定时器 - 3、单据自动确认完成定时任务 - 1、根据平台商查询所有运营商收货时间配置
                OrderSettingPolicyDTO orderSetting = jobCacheService.getOrderSettingPolicyInfo(dc.getDcId());
                Integer orderDate  = 120; // 默认5天
                String orderDateType  = "0";

                if (ToolUtil.isNotEmpty(orderSetting) && ToolUtil.isNotEmpty(orderSetting.getOrderFinishDate()))
                    orderDate = Integer.parseInt(orderSetting.getOrderFinishDate()); // 时间
                if (ToolUtil.isNotEmpty(orderSetting) && ToolUtil.isNotEmpty(orderSetting.getOrderFinishDateType()))
                    orderDateType = orderSetting.getOrderFinishDateType(); // 时间类型  0：小时 1：分钟

                takeDeliveryDTO.setDcId(dc.getDcId());
                takeDeliveryDTO.setOperDate(orderDate);
                takeDeliveryDTO.setOperDateType(orderDateType);

                orderApi.orderComplete(takeDeliveryDTO).getCheckedData();
            });
        });
        log.info(" ===============完成单据自动确认完成定时任务=================");
    }

    /**
     * !@定时器 - 4、订单分账 -  1、总入口
     */
    @XxlJob("orderSettleTransferJobHandler")
    public void orderSettleTransferJobHandler(){
        log.info(" ===============进入 订单结算 生成转账单或提交分账请求 定时任务=================");
        String param = XxlJobHelper.getJobParam();
        List<Long> sysCodeList = new ArrayList<>();
        String [] data = param.split(";");
        Long orderId;
        if (ToolUtil.isNotEmpty(param)) {
            sysCodeList.add(Long.parseLong(data[NumberPool.INT_ZERO]));
            orderId = Long.parseLong(data[NumberPool.INT_ONE]);
        } else {
            orderId = null;
            // 获取所有的平台商信息
            sysCodeList = partnerApi.getPartnerInfo().getCheckedData().stream().map(PartnerDto::getSysCode).collect(Collectors.toList());
        }
        sysCodeList.forEach(sysCode -> {
            try {
                orderApi.orderCreateSettleTransfer(sysCode, orderId).getCheckedData();
            } catch (Exception e) {
                log.error("订单结算 生成转账单或提交分账请求 定时任务执行失败，平台商{}，订单号：{}", sysCode, orderId, e);
            }
        });
        log.info(" ===============完成 订单结算 生成转账单或提交分账请求 定时任务=================");
    }
    
    /**
     * !@定时器 - 5、O2O分账定时器(入驻商) -  1、总入口
     */
    @XxlJob("orderO2OSettleSupplierJobHandler")
    public void orderO2OSettleSupplierJobHandler(){
        log.info(" ===============进入 O2O分账（入驻商）结算 定时任务=================");
        String param = XxlJobHelper.getJobParam();
        log.info(" 参数："+param);
        
        O2OSettleTaskParamVO settleTaskParam = getO2OSettleTaskParamVO(param);
        try {
            orderApi.orderO2OSignAfterSettle(settleTaskParam).getCheckedData();
        } catch (Exception e) {
            log.error("O2O分账结算任务执行失败，平台商{}，订单列表：{}", settleTaskParam.getSysCode(), settleTaskParam.getOrderNos(), e);
            throw new ServiceException("O2O分账结算任务执行失败: " + e.getMessage());
        }
        
        log.info(" ===============完成 O2O分账结算（入驻商） 定时任务=================");
    }
    /**
     * !@定时器 - 6、O2O分账定时器(门店) -  1、总入口
     */
    @XxlJob("orderO2OSettleBrandJobHandler")
    public void orderO2OSettleBrandJobHandler(){
        log.info(" ===============进入 O2O分账结算（门店） 定时任务=================");
        String param = XxlJobHelper.getJobParam();
        log.info(" 参数："+param);
        O2OSettleTaskParamVO settleTaskParam = getO2OSettleTaskParamVO(param);
        settleTaskParam.setMerchantType(MerchantTypeEnum.BRANCH_PROFIT.getType());
        try {
            orderApi.orderO2OSignAfterSettle(settleTaskParam).getCheckedData();
        } catch (Exception e) {
            log.error("O2O分账结算任务执行失败，平台商{}，订单列表：{}", settleTaskParam.getSysCode(), settleTaskParam.getOrderNos(), e);
            throw new ServiceException("O2O分账结算任务执行失败: " + e.getMessage());
        }
        log.info(" ===============完成 O2O分账结算（门店） 定时任务=================");
    }
    
    private static O2OSettleTaskParamVO getO2OSettleTaskParamVO(String param) {
        // 将参数转换为O2OTaskParam对象
        O2OSettleTaskParamVO settleTaskParam;
        try {
            settleTaskParam = JSON.parseObject(param, O2OSettleTaskParamVO.class);
        } catch (Exception e) {
            String errorMsg = "O2O分账结算任务参数解析失败: " + e.getMessage();
            log.error(errorMsg, e);
            throw new ServiceException(errorMsg);
        }
        
        // 校验sysCode是否为空
        if (settleTaskParam == null || settleTaskParam.getSysCode() == null) {
            String errorMsg = "O2O分账结算任务参数错误: sysCode不能为空";
            log.error(errorMsg);
            throw new ServiceException(errorMsg);
        }
        return settleTaskParam;
    }
    
    /**
     * !@定时器 - 查询微信B2B支付退款进度
     */
    @XxlJob("wxB2bRefundQueryProcess")
    public void wxB2bRefundQueryProcess(){
        Long minId = -1L;
        for (;;) {
            List<PayRefundQueryVO> refundQueryVOS = payFlowApi.getWxB2bRefundProcessAgainList(minId).getCheckedData();
            if (refundQueryVOS.isEmpty()) {
                return;
            }
            for (PayRefundQueryVO queryVO : refundQueryVOS) {
                payFlowApi.tryAgainB2bRefundProcess(queryVO).checkError();
            }
            minId = refundQueryVOS.get(refundQueryVOS.size() - 1).getRefundFlowId();;
        }
    }


}
