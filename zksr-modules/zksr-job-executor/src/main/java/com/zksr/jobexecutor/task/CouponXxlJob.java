package com.zksr.jobexecutor.task;

import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.enums.RedisCouponConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.jobexecutor.convert.ExecuteCouponConvert;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.api.coupon.dto.CouponReceiveResultDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.api.coupon.vo.NormalCouponReceiveSingleAsyncReqVo;
import com.zksr.promotion.api.couponBatch.CouponBatchApi;
import com.zksr.promotion.api.couponBatch.dto.CouponBatchDTO;
import com.zksr.promotion.api.couponBatch.dto.CouponBatchDtlDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.CouponExtendTotalVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 优惠券定时调度任务
 * @date 2024/4/8 10:46
 */
@Component
@Slf4j
public class CouponXxlJob {

    @Resource
    private CouponApi couponApi;

    @Resource
    private OrderApi orderApi;

    @Resource
    private CouponBatchApi couponBatchApi;

    @Autowired
    private RedisService redisService;

    /**
     * 执行优惠券定时已领取统计
     *
     * @throws Exception
     */
    @XxlJob("couponTotalStockJobHandler")
    public void couponTotalStockJobHandler() throws Exception {
        // 获取3天内未过期的优惠券数据列表
        Long minId = -1L;
        for (; ; ) {
            List<CouponTemplateDTO> templateDTOList = couponApi.getTotalStockCouponTemplateList(minId).getCheckedData();
            for (CouponTemplateDTO template : templateDTOList) {
                // 从缓存中去更新数据
                String stockUsedKey = RedisCouponConstants.getStockUsedKey(template.getCouponTemplateId());
                Integer cacheNumber = redisService.getCacheObject(stockUsedKey);
                log.info("获取优惠券缓存已领取数量 优惠券ID: {}, 缓存数量 : {}", template.getCouponTemplateId(), cacheNumber);
                XxlJobHelper.log("获取优惠券缓存已领取数量 优惠券名称: {}, 缓存数量 : {}", template.getCouponName(), cacheNumber);
                if (Objects.nonNull(cacheNumber)) {
                    // 存在已领取数据, 则同步数据
                    couponApi.updateCouponTemplateTotal(cacheNumber, template.getCouponTemplateId());
                }
            }
            if (templateDTOList.isEmpty()) {
                break;
            }
            minId = templateDTOList.get(templateDTOList.size() - 1).getCouponTemplateId();
        }

    }

    /**
     * 执行优惠券定时统计拓展表数据
     *
     * @throws Exception
     */
    @XxlJob("couponTotalExtendJobHandler")
    public void couponTotalExtendJobHandler() throws Exception {
        // 获取3天内未过期的优惠券数据列表
        Long minId = -1L;
        for (; ; ) {
            List<CouponTemplateDTO> templateDTOList = couponApi.getTotalStockCouponTemplateList(minId).getCheckedData();
            if (templateDTOList.isEmpty()) {
                return;
            }
            List<Long> couponTemplateIdList = templateDTOList.stream().map(CouponTemplateDTO::getCouponTemplateId).collect(Collectors.toList());
            // 获取优惠券统计数据
            List<CouponExtendTotalVO> extendTotalVOList = orderApi.getCouponExtendTotal(couponTemplateIdList).getCheckedData();
            // 更新来自订单的统计数据
            couponApi.updateCouponTemplateOrderExtend(ExecuteCouponConvert.INSTANCE.convertExtendDTOList(extendTotalVOList));
            // 更新优惠券自己的领取, 使用情况数据
            couponApi.updateCouponTemplateCouponExtend(couponTemplateIdList);
            if (templateDTOList.isEmpty()) {
                break;
            }
            minId = templateDTOList.get(templateDTOList.size() - 1).getCouponTemplateId();
        }

    }

    /**
     * 执行批量发券优惠券定时发券
     *
     * @throws Exception
     */
    @XxlJob("couponBatchSendJobHandler")
    public void couponBatchSendJobHandler() throws Exception {

        //获取已审核的生效类型为定时生效的批次发券集合
        List<CouponBatchDTO> validCouponBatchList = couponBatchApi.getValidCouponBatchList().getCheckedData();

        if (ToolUtil.isNotEmpty(validCouponBatchList)) {
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startOfCurrentHour = now.withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endOfCurrentHour = startOfCurrentHour.plusHours(1).minusNanos(1);

            // 过滤出生效时间在当前小时中的数据
            List<CouponBatchDTO> filteredList = validCouponBatchList.stream()
                    .filter(batch -> {
                        Date validTime = batch.getValidTime();
                        LocalDateTime validTimeLocal = validTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        return validTimeLocal.isAfter(startOfCurrentHour) && validTimeLocal.isBefore(endOfCurrentHour);
                    })
                    .collect(Collectors.toList());

            // 处理过滤后的数据
            for (CouponBatchDTO batch : filteredList) {

                //获取批量发券详情
                List<CouponBatchDtlDTO> prmCouponBatchDtlList = couponBatchApi.getCouponBatchDtlList(batch.getCouponBatchId(), 0).getCheckedData();
                if (ToolUtil.isEmpty(prmCouponBatchDtlList)) continue;
                for (CouponBatchDtlDTO prmCouponBatchDtl : prmCouponBatchDtlList) {
                    CouponTemplateDTO couponTemplate = couponApi.getCouponTemplate(prmCouponBatchDtl.getCouponTemplateId()).getCheckedData();
                    List<CouponBatchDtlDTO> prmCouponBatchBranchIds = couponBatchApi.getCouponBatchDtlList(batch.getCouponBatchId(), 1).getCheckedData();
                    // 获取批量发券详情门店ID集合
                    List<Long> branchIds = prmCouponBatchBranchIds.stream().map(CouponBatchDtlDTO::getBranchId).collect(Collectors.toList());
                    // 校验优惠券的状态是否正常
                    if (couponTemplate.getStatus() != 0) {
                        continue; // 跳过当前循环，进行下个模板的发券任务
                    }
                    // 校验优惠券有效期类型为固定日期时 发放时间是否在当前时间之中
                    if (couponTemplate.getExpirationType() == 0) {
                        if (DateUtil.compare(new Date(), couponTemplate.getExpirationDateStart()) < 0 || DateUtil.compare(new Date(), couponTemplate.getExpirationDateEnd()) > 0) {
                            continue;  // 跳过当前循环，进行下个模板的发券任务
                        }
                    }
                    for (Long branchId : branchIds) {
                        //根据门店发放数量给门店发放指定数量优惠券
                        for (int i = 0; i < batch.getBranchSendQty(); i++) {
                            Map<Long, Long> couponStatusMap = new HashMap<>();
                            couponStatusMap.put(prmCouponBatchDtl.getCouponTemplateId(), Long.parseLong(couponTemplate.getStatus() + ""));
                            //发送优惠券
                            couponApi.saveNormalCouponReceiveSingleAsync(new NormalCouponReceiveSingleAsyncReqVo(branchId, null, prmCouponBatchDtl.getCouponTemplateId(), true, prmCouponBatchDtl.getBatchCouponId(), couponStatusMap));
                        }
                    }

                }
                batch.setTaskExecuteStatus(1);
                couponBatchApi.updateCouponBatch(batch);
            }

        }

    }
}
