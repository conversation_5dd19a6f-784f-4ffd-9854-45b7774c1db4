package com.zksr.jobexecutor.task;

import com.alicp.jetcache.Cache;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.redis.bean.BasicSettingPolicyCacheBean;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.branchRegister.BranchRegisterApi;
import com.zksr.member.api.colonel.Colonel<PERSON>pi;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import com.zksr.member.api.colonelSettle.ColonelSettleApi;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.BasicSettingPolicyDTO;
import com.zksr.system.api.partnerPolicy.enums.PartnerPolicyEnum;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.SupplierOrderDtlInfoExportVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年08月05日 16:53
 * @description: ColonelXxlJob 业务员相关定时任务
 */
@Component
@Slf4j
public class ColonelXxlJob {

    @Autowired
    private PartnerApi partnerApi;
    @Autowired
    private ColonelSettleApi colonelSettleApi;
    @Autowired
    private ColonelApi colonelApi;
    @Autowired
    private PartnerPolicyApi partnerPolicyApi;
    @Autowired
    private BranchApi branchApi;
    @Autowired
    private SupplierApi supplierApi;
    @Autowired
    private OrderApi orderApi;
    @Autowired
    private DcApi dcApi;
    @Autowired
    private AreaApi areaApi;
    @Autowired
    private BranchApi remoteBranchApi;



    /**
     * 业务员 - 业务日结定时任务
     */
    @XxlJob("colonelDaySettleJobHandler")
    public void colonelDaySettleJobHandler() {
        log.info(" ===============进入业务员 - 业务日结定时任务=================");
        String param = XxlJobHelper.getJobParam();

        List<Long> sysCodeList = new ArrayList<>();
        if (ToolUtil.isNotEmpty(param)) {
            sysCodeList.add(Long.parseLong(param));
        } else {
            // 获取所有的平台商信息
            sysCodeList = partnerApi.getPartnerInfo().getCheckedData().stream().map(PartnerDto::getSysCode).collect(Collectors.toList());
        }
        sysCodeList.forEach(sysCode -> {
            // 执行 业务员日结任务代码
            colonelSettleApi.colonelDaySettlementJob(sysCode, DateUtils.getDateAdd(-1));
        });
        log.info(" ===============完成业务员 - 业务日结定时任务=================");
    }

    /**
     * 业务员 - 业务月结定时任务
     */
//    @XxlJob("colonelMonthSettleJobHandler")
//    public void colonelMonthSettleJobHandler() {
//        log.info(" ===============进入业务员 - 业务月结定时任务=================");
//        // 1、获取所有的平台商信息
//        List<PartnerDto> partnerDtos = partnerApi.getPartnerInfo().getCheckedData();
//        partnerDtos.forEach(partner -> {
//            // 执行 业务员日结任务代码 (前一天的数据)
//            colonelSettleApi.colonelMonthSettlementJob(partner.getSysCode(), DateUtils.getDateAdd(-1));
//        });
//        log.info(" ===============完成业务员 - 业务月结定时任务=================");
//    }

    /**
     * 业务员 - 业务结算定时任务 （用于调整错误数据）
     */
    @XxlJob("colonelSettleJobHandler")
    public void colonelSettleJobHandler() {
        log.info(" ===============进入业务员 - 业务定时任务用于(调整错误数据）=================");
        String param = XxlJobHelper.getJobParam();
        String [] data = param.split(";");

        if (ToolUtil.isEmpty(data) || !DateUtils.isTimeString(data[1], DateUtils.YYYY_MM_DD) || !DateUtils.isTimeString(data[2], DateUtils.YYYY_MM_DD)) {
            log.error(" ===============业务员执行结算定时任务(调整错误数据） 参数错误:{} =================", param);
            return;
        }

        // 1、获取所有的平台商信息
        List<Long> sysCodeList = new ArrayList<>();
        if (ToolUtil.isNotEmpty(data[0])) {
            sysCodeList.add(Long.parseLong(data[0]));
        } else {
            sysCodeList = partnerApi.getPartnerInfo().getCheckedData().stream().map(PartnerDto::getSysCode).collect(Collectors.toList());
        }
        sysCodeList.forEach(sysCode -> {
            colonelSettleApi.colonelSettlementJob(sysCode, data[1], data[2]);
        });
        log.info(" ===============完成业务员 - 业务定时任务(调整错误数据）=================");
    }

    /**
     * 业务员 - 公海客户定时任务
     */
    @XxlJob("colonelSeasHandler")
    public void colonelSeasHandler() {
        List<PartnerDto> partnerDtos = partnerApi.getPartnerInfo().getCheckedData();
        ArrayList<DcDTO> dcList = new ArrayList<>();
        partnerDtos.forEach(partner -> {
            // 2、根据平台商编号查询该平台下所有运营商信息
            dcList.addAll(dcApi.getDcBySysCode(partner.getSysCode()).getCheckedData());
        });
        log.info(" ===============开始公海扫描 - 业务定时任务=================");
        for (DcDTO dcDTO : dcList) {
            if (ToolUtil.isNotEmpty(dcDTO.getDcId()) && ToolUtil.isNotEmpty(dcDTO.getSysCode())) {
                BasicSettingPolicyDTO basicSettingPolicyDTO = partnerPolicyApi.getBasicSettingPolicy(dcDTO.getDcId()).getData();
                if (ToolUtil.isEmpty(basicSettingPolicyDTO) || ToolUtil.isEmpty(basicSettingPolicyDTO.getIsSeas()) || ToolUtil.isEmpty(basicSettingPolicyDTO.getSeasCondition()) || ToolUtil.isEmpty(basicSettingPolicyDTO.getSeasDay()))continue;
                Integer isSeas = Integer.valueOf(basicSettingPolicyDTO.getIsSeas());
                Integer seasCondition = Integer.valueOf(basicSettingPolicyDTO.getSeasCondition());
                Integer seasDay = Integer.valueOf(basicSettingPolicyDTO.getSeasDay());
                Date currentTime = new Date();

                // 获取运营商所有门店
                ArrayList<Long> branchList = new ArrayList<>();
                ArrayList<Long> initBranchList = new ArrayList<>(); // 没有进入公海时间，需要初始化的门店
                List<AreaDTO> areaDTOList = areaApi.getAreaListByDcId(dcDTO.getDcId()).getData(); // 获取运营商下所有的城市id
                areaDTOList.forEach(areaDTO -> {
                    if (Objects.nonNull(areaDTO.getThreeAreaCityId())) {
                        List<BranchDTO> branchDTOList = branchApi.getBranchListByArea(areaDTO.getAreaId()).getData();
                        branchDTOList.forEach(branchDTO -> {
                            if (Objects.isNull(branchDTO.getSeasTime())) {
                                initBranchList.add(branchDTO.getBranchId());
                            }
                            branchList.add(branchDTO.getBranchId());
                        });
                    }
                });

                // 初始化门店
                if (isSeas == 1 && ToolUtil.isNotEmpty(seasDay) && seasDay > 0 && !initBranchList.isEmpty()) { // 开启公海门店
                    seasInit(initBranchList, seasCondition, seasDay);
                }
                // 判断门店是否进入公海
                if (isSeas == 1 && ToolUtil.isNotEmpty(seasDay) && seasDay > 0){
                    for (Long branchId : branchList) {
                        BranchDTO branch = branchApi.getByBranchId(branchId).getData();
                        if (Objects.nonNull(branch) && Objects.nonNull(branch.getSeasTime()) && currentTime.after(branch.getSeasTime())) { // 当前时间大于进入公海时间
                            MemBranchSaveReqVO memBranchSaveReqVO = new MemBranchSaveReqVO();
                            memBranchSaveReqVO.setBranchId(branchId);
                            memBranchSaveReqVO.setColonelId(branch.getColonelId());
                            memBranchSaveReqVO.setSysCode(dcDTO.getSysCode());
                            branchApi.editSeasTime(memBranchSaveReqVO);
                            // 将改门店与业务员刷入es中
                            remoteBranchApi.refreshEsBranchBase(branchId);
                        }
                    }
                }
            }
        }
        log.info(" ===============开始公海扫描 - 业务定时任务完成=================");
    }


    /**
     * 初始化门店进入公海时间
     *
     * @param initBranchList 需要初始的门店
     * @param seasCondition 进入公海的条件判断
     * @param seasDay 进入公海时间
     */
    public void seasInit(ArrayList<Long> initBranchList, Integer seasCondition, Integer seasDay) {
        List<SupplierOrderDtlInfoExportVO> lastOrderDateListMap = orderApi.getLastOrderTime(initBranchList).getData();
        List<MemColonelRespVO> lastVisitDateListMap = colonelApi.getLastVisitTime(initBranchList).getData();
        Map<Long, Date> lastOrderDateMap = null;
        Map<Long, Date> lastVisitDateMap = null;
        if (ToolUtil.isNotEmpty(lastOrderDateListMap)){
            lastOrderDateMap = lastOrderDateListMap.stream().collect(Collectors.toMap(SupplierOrderDtlInfoExportVO::getBranchId, SupplierOrderDtlInfoExportVO::getLasterOrderTime));
        }
        if (ToolUtil.isNotEmpty(lastVisitDateListMap)){
            lastVisitDateMap = lastVisitDateListMap.stream().collect(Collectors.toMap(MemColonelRespVO::getBranchId, MemColonelRespVO::getLastVisitDate));
        }

        for (Long branchId : initBranchList) {
            Date baseDate = getBaseDate(branchId, seasCondition,lastOrderDateMap,lastVisitDateMap);
            if (ToolUtil.isEmpty(baseDate)){
                BranchDTO branchDTO = branchApi.getByBranchId(branchId).getData();
                if (Objects.nonNull(branchDTO) && Objects.nonNull(branchDTO.getCreateTime())) {
                    baseDate = branchDTO.getCreateTime();
                }
            }

            if (ToolUtil.isNotEmpty(baseDate)) {
                // 根据基础日期计算进入公海的时间
                LocalDateTime newDateTime = baseDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()
                        .plus(seasDay, ChronoUnit.DAYS);
                Date seasTime = Date.from(newDateTime.atZone(ZoneId.systemDefault()).toInstant());

                // 修改进入公海时间
                MemBranchSaveReqVO memBranchSaveReqVO = new MemBranchSaveReqVO();
                memBranchSaveReqVO.setBranchId(branchId);
                memBranchSaveReqVO.setSeasTime(seasTime);
                branchApi.edit(memBranchSaveReqVO);
            } else {
                log.info(branchId + "门店不存在,或门店未审核时间");
            }
        }
    }
    private Date getBaseDate(Long branchId, Integer seasCondition, Map<Long, Date> lastOrderDateMap, Map<Long, Date> lastVisitDateMap) {
        if (lastOrderDateMap ==null){
            lastOrderDateMap = new HashMap<>();
        }
        if (lastVisitDateMap == null){
            lastVisitDateMap = new HashMap<>();
        }
        switch (seasCondition) {
            case 0: // 根据下单时间
                return lastOrderDateMap.get(branchId);
            case 1: // 根据下单时间和拜访时间,谁大取谁
                Date lastOrderTime = lastOrderDateMap.get(branchId); // 上次下单时间
                Date lastVisitTime = lastVisitDateMap.get(branchId); // 获取上次拜访时间
                if (lastOrderTime != null && lastVisitTime != null) {
                    return lastOrderTime.compareTo(lastVisitTime) > 0 ? lastOrderTime : lastVisitTime;
                } else {
                    return lastOrderTime != null ? lastOrderTime : lastVisitTime;
                }
            default:
                 return null;
        }
    }

}
