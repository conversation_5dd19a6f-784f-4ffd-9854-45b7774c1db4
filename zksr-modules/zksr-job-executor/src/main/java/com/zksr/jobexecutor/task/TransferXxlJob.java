package com.zksr.jobexecutor.task;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.account.api.transfer.TransferApi;
import com.zksr.account.api.transfer.dto.TransferRetryDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 转账单xxljob
 * @date 2024/4/11 11:38
 */
@Component
@Slf4j
public class TransferXxlJob {

    @Autowired
    private TransferApi transferApi;

    /**
     * 重试内部转账单
     * @throws Exception
     */
    @XxlJob("transferRetryJobHandler")
    public void transferRetryJobHandler() throws Exception {
        // 获取3天内未过期的优惠券数据列表
        Long minId = -1L;
        for (;;) {
            CommonResult<List<TransferRetryDTO>> apiRetryAccTransfer = transferApi.getRetryAccTransfer(minId);
            if (!apiRetryAccTransfer.isSuccess()) {
                XxlJobHelper.log("获取转账单重试列表失败");
            }
            List<TransferRetryDTO> transferRetryList = apiRetryAccTransfer.getCheckedData();
            transferRetryList.forEach(item -> {
                log.info("重试转账单 transferNo={}", item.getTransferNo());
                XxlJobHelper.log("重试转账单 transferNo={}", item.getTransferNo());
            });
            transferApi.retryAccTransfer(transferRetryList);
            if (transferRetryList.isEmpty()) {
                break;
            }
            minId = transferRetryList.get(transferRetryList.size() - 1).getTransferId();
        }

    }
}
