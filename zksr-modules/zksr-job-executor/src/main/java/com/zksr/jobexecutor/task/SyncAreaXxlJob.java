package com.zksr.jobexecutor.task;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.common.core.erpUtils.HttpUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.jobexecutor.domain.SaaSDcEbplRequestDTO;
import com.zksr.jobexecutor.domain.SaasDcApiResponse;
import com.zksr.jobexecutor.domain.SaasDcApiResponse.DataList;
import com.zksr.jobexecutor.domain.SaasDcEbPlaceDTO;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.vo.SysAreaCityPageReqVO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.area.vo.SysAreaCitySaveReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RefreshScope
@Slf4j
public class SyncAreaXxlJob {

    private static final Map<PlaceType, Integer> deepMap = Maps.newHashMap();
    @Value("${saasDcApi.host:null}")
    private String saasDcApiHost;
    @Value("${saasDcApi.urlPrefix:null}")
    private String saasDcApiUrlPrefix;
    @Value("${saasDcApi.api.selectByNameList:null}")
    private String selectByNameListApi;
    @Value("${saasDcApi.api.pageSearch:null}")
    private String pageSearchApi;
    @Value("${saasDcApi.queryPageSize:200}")
    private Integer queryPageSize;
    @Autowired
    private AreaCityApi areaCityApi;

    private final String COUNTRY_CODE = "100000";

    private enum PlaceType {
        PLACE_PROVINCE,
        PLACE_CITY,
        PLACE_DISTRICT
        ;

        public static PlaceType getType(String type) {
            for (PlaceType placeType : PlaceType.values()) {
                if (placeType.name().equals(type)) {
                    return placeType;
                }
            }
            return null;
        }
    }

    static {
        // 省
        deepMap.put(PlaceType.PLACE_PROVINCE, 0);
        // 市
        deepMap.put(PlaceType.PLACE_CITY, 1);
        // 区
        deepMap.put(PlaceType.PLACE_DISTRICT, 2);
    }

    /**
     * 同步行政区域
     */
    @XxlJob("syncAreaJobHandler")
    public void syncAreaJobHandler() throws Exception {
        log.info("同步行政区域---start");
        List<SysAreaCityRespVO> areaCityList = queryAllSysAreaCity();
        List<SaasDcEbPlaceDTO> dcAreaList = pageSearchDcArea();
        handle(areaCityList, dcAreaList);
        log.info("同步行政区域---end");
    }

    private String getRequestUrl(String api) {
        return saasDcApiHost + saasDcApiUrlPrefix + api;
    }

    private PageResult<SysAreaCityRespVO> getAreaCityPage(Integer pageNo) {
        SysAreaCityPageReqVO reqVO = new SysAreaCityPageReqVO();
        reqVO.setPageNo(pageNo);
        reqVO.setPageSize(queryPageSize);
        CommonResult<PageResult<SysAreaCityRespVO>> result = areaCityApi.getPage(reqVO);
        if (result == null || result.isError()) {
            return null;
        }
        return result.getData();
    }

    private List<SysAreaCityRespVO> queryAllSysAreaCity() {
        List<SysAreaCityRespVO> areaCityList = Lists.newArrayList();
        int pageNo = 1;
        PageResult<SysAreaCityRespVO> pageResult;
        do {
            pageResult = getAreaCityPage(pageNo++);
            if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
                break;
            }
            areaCityList.addAll(pageResult.getList());
        } while (CollectionUtils.isNotEmpty(pageResult.getList()));
        return areaCityList;
    }


    private void handle(List<SysAreaCityRespVO> areaCityList, List<SaasDcEbPlaceDTO> dcAreaList) {
        Map<Long, SysAreaCityRespVO> idAreaMap = areaCityList.stream().collect(Collectors.toMap(SysAreaCityRespVO::getAreaCityId, Function.identity()));
        Map<String, List<SysAreaCityRespVO>> nameAreaMap = areaCityList.stream().collect(Collectors.groupingBy(SysAreaCityRespVO::getName));
        Map<String, SaasDcEbPlaceDTO> dcCodeAreaMap = dcAreaList.stream().collect(Collectors.toMap(SaasDcEbPlaceDTO::getEbplCode, Function.identity()));

        List<SysAreaCitySaveReqVO> updateList = Lists.newArrayList();
        // 1.只有name和parentName都一致，才进行更新；其他情况都进行插入
        // 2.插入：通过parentName找b2b的，如果有，直接取其id为pid；如果没有，就先插入parent，再插入当前
        for (SaasDcEbPlaceDTO dcArea : dcAreaList) {
            SysAreaCitySaveReqVO updateVO = needUpdate(dcArea, idAreaMap, nameAreaMap, dcCodeAreaMap);
            if (updateVO != null) {
                updateList.add(updateVO);
                idAreaMap.get(updateVO.getAreaCityId()).setExtId(updateVO.getExtId());
                continue;
            }
            // 插入
            handleInsert(dcArea, idAreaMap, nameAreaMap, dcCodeAreaMap);
        }

        // 每次更新100个
        Lists.partition(updateList, 100).forEach(areaCityApi::updateBatch);
    }

    private List<SaasDcEbPlaceDTO> getDcAreaByNameList(List<String> nameList) throws Exception {
        List<SaasDcEbPlaceDTO> result = Lists.newArrayListWithCapacity(nameList.size());
        if (CollectionUtils.isEmpty(nameList)) {
            return result;
        }
        String requestUrl = getRequestUrl(selectByNameListApi);
        for (List<String> names : Lists.partition(nameList, 200)) {
            String response = HttpUtils.sendPost(requestUrl, names, null, 3000);
            SaasDcApiResponse<List<SaasDcEbPlaceDTO>> apiResponse = JSON.parseObject(response, new TypeReference<SaasDcApiResponse<List<SaasDcEbPlaceDTO>>>() {});
            if (apiResponse == null || apiResponse.isError() || CollectionUtils.isEmpty(apiResponse.getData())) {
                continue;
            }
            result.addAll(apiResponse.getData());
        }
        return result;
    }

    private Long handleInsert(SaasDcEbPlaceDTO dcArea,
                              Map<Long, SysAreaCityRespVO> idAreaMap,
                              Map<String, List<SysAreaCityRespVO>> nameAreaMap,
                              Map<String, SaasDcEbPlaceDTO> dcCodeAreaMap) {
        String parentCode = dcArea.getEbplParentPmCode();
        Long pid = null;
        if (PlaceType.getType(dcArea.getEbplType()) == PlaceType.PLACE_PROVINCE || COUNTRY_CODE.equals(parentCode)) {
            pid = 0L;
        } else {
            SaasDcEbPlaceDTO parentDcArea = dcCodeAreaMap.get(parentCode);
            String parentName = parentDcArea.getEbplNameCn();
            if (nameAreaMap.containsKey(parentName)) {
                // 找父级地点，pid不为null即找到
                pid = nameAreaMap.get(parentName).stream().filter(sysAreaCity -> isSame(parentDcArea, sysAreaCity, idAreaMap, dcCodeAreaMap))
                        .findFirst().map(SysAreaCityRespVO::getAreaCityId).orElse(null);
            }
            if (pid == null) {
                // 递归插入父级地点
                pid = handleInsert(parentDcArea, idAreaMap, nameAreaMap, dcCodeAreaMap);
            }
        }
        SysAreaCitySaveReqVO insertVO = buildSysAreaCityInsertVO(pid, deepMap.get(PlaceType.getType(dcArea.getEbplType())), dcArea.getEbplNameCn(), dcArea.getEbplCode(), dcArea.getEbplIsAble());
        CommonResult<Long> insertResult = areaCityApi.insert(insertVO);
        if (insertResult != null && insertResult.isSuccess() && insertResult.getData() != null) {
            Long insertId = insertResult.getData();
            SysAreaCityRespVO sysAreaCityRespVO = convert(insertVO);
            sysAreaCityRespVO.setAreaCityId(insertId);
            // 把插入后的地区添加到Map中
            nameAreaMap.computeIfAbsent(sysAreaCityRespVO.getName(), k -> Lists.newArrayList()).add(sysAreaCityRespVO);
            idAreaMap.put(insertId, sysAreaCityRespVO);
            return insertId;
        } else {
            throw new RuntimeException(StringUtils.format("行政区域插入失败, {}", JSON.toJSONString(insertVO)));
        }
    }

    private SysAreaCityRespVO convert(SysAreaCitySaveReqVO insertVO) {
        SysAreaCityRespVO sysAreaCityRespVO = new SysAreaCityRespVO();
        sysAreaCityRespVO.setAreaCityId(insertVO.getAreaCityId());
        sysAreaCityRespVO.setDeep(insertVO.getDeep());
        sysAreaCityRespVO.setName(insertVO.getName());
        sysAreaCityRespVO.setPid(insertVO.getPid());
        sysAreaCityRespVO.setExtId(insertVO.getExtId());
        return sysAreaCityRespVO;
    }

    private List<SaasDcEbPlaceDTO> pageSearchDcArea() throws Exception {
        List<SaasDcEbPlaceDTO> result = Lists.newArrayList();
        String requestUrl = getRequestUrl(pageSearchApi);
        for (PlaceType placeType : deepMap.keySet()) {
            SaaSDcEbplRequestDTO saaSDcEbplRequestDTO = new SaaSDcEbplRequestDTO().setPageNo(1).setPageSize(queryPageSize).setEbplType(placeType.name());
            for (;;) {
                String response = HttpUtils.sendPost(requestUrl, saaSDcEbplRequestDTO, null, 3000);
                SaasDcApiResponse<DataList<SaasDcEbPlaceDTO>> apiResponse = JSON.parseObject(response, new TypeReference<SaasDcApiResponse<DataList<SaasDcEbPlaceDTO>>>() {});
                if (apiResponse == null || apiResponse.isError() || apiResponse.getData() == null || CollectionUtils.isEmpty(apiResponse.getData().getList())) {
                    break;
                }
                result.addAll(apiResponse.getData().getList());
                saaSDcEbplRequestDTO.setPageNo(saaSDcEbplRequestDTO.getPageNo() + 1);
            }
        }
        return result;
    }

    private SysAreaCitySaveReqVO buildSysAreaCityUpdateVO(Long areaCityId, String ebplCode, Integer ebplIsAble) {
        SysAreaCitySaveReqVO updateVO = new SysAreaCitySaveReqVO();
        updateVO.setAreaCityId(areaCityId);
        updateVO.setUpdateTime(new Date());
        updateVO.setExtId(Long.valueOf(ebplCode));
        updateVO.setSaasEnable(ebplIsAble);
        return updateVO;
    }

    private SysAreaCitySaveReqVO buildSysAreaCityInsertVO(Long parentId, Integer deep, String name, String ebplCode, Integer ebplIsAble) {
        SysAreaCitySaveReqVO insertVO = new SysAreaCitySaveReqVO();
        insertVO.setPid(parentId);
        insertVO.setDeep(deep);
        insertVO.setExtId(Long.valueOf(ebplCode));
        insertVO.setName(name);
        insertVO.setSaasEnable(ebplIsAble);
        insertVO.setCreateTime(new Date());
        return insertVO;
    }

    private SysAreaCitySaveReqVO needUpdate(SaasDcEbPlaceDTO dcArea,
                               Map<Long, SysAreaCityRespVO> idAreaMap,
                               Map<String, List<SysAreaCityRespVO>> nameAreaMap,
                               Map<String, SaasDcEbPlaceDTO> dcCodeAreaMap) {
        String name = dcArea.getEbplNameCn();
        String parentCode = dcArea.getEbplParentPmCode();
        // b2b中没有该名称，直接插入
        if (!nameAreaMap.containsKey(name)) {
            return null;
        }
        List<SysAreaCityRespVO> sysAreaCityList = nameAreaMap.get(name);
        // 当前是省级别，直接更新
        if (PlaceType.getType(dcArea.getEbplType()) == PlaceType.PLACE_PROVINCE
                || COUNTRY_CODE.equals(dcArea.getEbplParentPmCode())) {
            Long areaCityId = sysAreaCityList.get(0).getAreaCityId();
            return buildSysAreaCityUpdateVO(areaCityId, dcArea.getEbplCode(), dcArea.getEbplIsAble());
        }
        // 比较父级是否一致
        Long areaCityId = sysAreaCityList.stream().filter(vo -> isSame(dcCodeAreaMap.get(parentCode), idAreaMap.get(vo.getPid()), idAreaMap, dcCodeAreaMap))
                .findFirst().map(SysAreaCityRespVO::getAreaCityId).orElse(null);
        if (areaCityId == null) {
            return null;
        }
        return buildSysAreaCityUpdateVO(areaCityId, dcArea.getEbplCode(), dcArea.getEbplIsAble());
    }

    /**
     * 判断给定的saas区域和b2b中的区域，是否相同
     * @param dcArea
     * @param sysAreaCity
     * @param idAreaMap
     * @param dcCodeAreaMap
     * @return
     */
    private boolean isSame(SaasDcEbPlaceDTO dcArea,
                           SysAreaCityRespVO sysAreaCity,
                           Map<Long, SysAreaCityRespVO> idAreaMap,
                           Map<String, SaasDcEbPlaceDTO> dcCodeAreaMap) {
        if (dcArea == null && sysAreaCity == null) {
            return true;
        }
        if (dcArea == null || sysAreaCity == null) {
            return false;
        }
        // 名称不一样，则不相同
        if (!StringUtils.equals(dcArea.getEbplNameCn(), sysAreaCity.getName())) {
            return false;
        }
        // 地点级别不一样，则不相同
        if (!deepMap.get(PlaceType.getType(dcArea.getEbplType())).equals(sysAreaCity.getDeep())) {
            return false;
        }
        // 递归比较父级
        return isSame(dcCodeAreaMap.get(dcArea.getEbplParentPmCode()), idAreaMap.get(sysAreaCity.getPid()), idAreaMap, dcCodeAreaMap);
    }

}
