package com.zksr.jobexecutor.task;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.report.api.homePages.RptHomePagesApi;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月19日 16:53
 * @description: HomePagesDataXxlJob PC首页相关定时任务
 */
@Component
@Slf4j
public class HomePagesDataXxlJob {

    @Autowired
    private PartnerApi partnerApi;
    @Autowired
    private RptHomePagesApi rptHomePagesApi;

    /**
     * PC首页数据 - 定时刷新ES首页ToDay数据任务
     */
    @XxlJob("homePagesDayDataJobHandler")
    public void homePagesDayDataJobHandler() {
        String param = XxlJobHelper.getJobParam();
        List<Long> sysCodeList = new ArrayList<>();
        if (ToolUtil.isNotEmpty(param)) {
            sysCodeList.add(Long.parseLong(param));
        } else {
            // 获取所有的平台商信息
            sysCodeList = partnerApi.getPartnerInfo().getCheckedData().stream().map(PartnerDto::getSysCode).collect(Collectors.toList());
        }
        sysCodeList.forEach(sysCode -> {
            try {
                rptHomePagesApi.refreshEsHomePagesDataJob(sysCode);
            } catch (Exception e){
                log.info("平台：{}，刷新ES首页数据任务，原因{}", sysCode, e.getMessage());
            }
        });
    }

    /**
     * PC首页数据 - 定时刷新ES首页Month数据任务
     */
    @XxlJob("homePagesMonthDataJobHandler")
    public void homePagesMonthDataJobHandler() {
        String param = XxlJobHelper.getJobParam();

        List<Long> sysCodeList = new ArrayList<>();
        String startDate;
        String endDate;
        if (ToolUtil.isNotEmpty(param)) {
            sysCodeList.add(Long.parseLong(param.split(";")[0]));
            startDate = param.split(";")[1];
            endDate = param.split(";")[2];
        } else {
            // 获取所有的平台商信息
            sysCodeList = partnerApi.getPartnerInfo().getCheckedData().stream().map(PartnerDto::getSysCode).collect(Collectors.toList());
            startDate = DateUtils.getMonthFirstDay(DateUtils.getDate());
            endDate = DateUtils.getMonthLastDay(DateUtils.getDate());
        }
        sysCodeList.forEach(sysCode -> {
            try {
                rptHomePagesApi.refreshEsHomePagesMonthDataJob(sysCode, startDate, endDate);
            } catch (Exception e){
                log.info("平台：{}，刷新ES首页数据任务，原因{}", sysCode, e.getMessage());
            }
        });
    }
}
