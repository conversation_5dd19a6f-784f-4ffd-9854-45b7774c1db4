package com.zksr.jobexecutor.task;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.AccountFlowApi;
import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.divide.DivideFlowApi;
import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.account.api.pay.PayApi;
import com.zksr.account.api.pay.PayFlowApi;
import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantNameAndKeyDTO;
import com.zksr.account.api.transfer.TransferApi;
import com.zksr.account.api.transfer.dto.*;
import com.zksr.account.api.withdraw.WithdrawApi;
import com.zksr.account.api.withdraw.dto.AccWithdrawBillDTO;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.vo.CreateDivideReqVO;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.common.core.enums.ExceptionType;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DownloadBillUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.utils.RedisWxTokenUtil;
import com.zksr.system.api.RemoteFileService;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 账户XXL
 * @date 2024/4/8 10:46
 */
@Component
@Slf4j
public class AccountXxlJob {

    @Resource
    private AccountFlowApi accountFlowApi;

    @Resource
    private AccountApi accountApi;

    @Resource
    private DivideFlowApi divideFlowApi;

    @Resource
    private PartnerApi partnerApi;

    @Resource
    private PlatformMerchantApi platformMerchantApi;

    @Resource
    private PartnerConfigApi partnerConfigApi;

    @Resource
    private TransferApi transferApi;

    @Resource
    private PayFlowApi payFlowApi;

    @Resource
    private PayApi payApi;

    @Resource
    private RemoteFileService fileApi;

    @Resource
    private WithdrawApi withdrawApi;

    /**
     * !@定时器 - 1、处理微信B2B支付充值流水自动分账
     */
    @XxlJob("wxB2bRechargeDivide")
    public void wxB2bRechargeDivide() {
        Long minPayFlowId = -1L;
        for (;;) {
            List<PayFlowDTO> payFlowDTOS = payFlowApi.getWxB2bRechargeDivideFlow(minPayFlowId).getCheckedData();
            if (payFlowDTOS.isEmpty()) {
                XxlJobHelper.log("处理完成微信充值分润订单");
                return;
            }
            for (PayFlowDTO flowDTO : payFlowDTOS) {
                List<OrderSettlementDTO> settlementDTOS = flowDTO.buildSettle();
                for (OrderSettlementDTO settlementDTO : settlementDTOS) {
                    CreateDivideReqVO divideReqVO = CreateDivideReqVO.builder()
                            .merchantType(settlementDTO.getMerchantType())
                            .merchantId(settlementDTO.getMerchantId())
                            .tradeNo(flowDTO.getTradeNo())
                            .build();
                    log.info("发起微信B2B充值订单分润, {}", JSON.toJSONString(divideReqVO));
                    CreateDivideRespVO respVO = payApi.divide(divideReqVO).getCheckedData();
                    log.info("处理微信B2B充值订单分润结果, {}", JSON.toJSONString(respVO));
                }
            }
            minPayFlowId = payFlowDTOS.get(payFlowDTOS.size() - 0).getPayFlowId();
        }
    }

    /**
     * 定时重试未结算流水
     *
     * @throws Exception
     */
    @XxlJob("accountFlowTrySettleJobHandler")
    public void accountFlowTrySettleJobHandler() throws Exception {
        // 获取3天内未过期的优惠券数据列表
        Long minId = -1L;
        for (; ; ) {
            List<AccAccountFlowDTO> tryList = accountFlowApi.getTrySettleFlow(minId).getCheckedData();
            Lists.partition(tryList, 20).forEach(
                    item -> accountApi.processFlow(item.stream().map(AccAccountFlowDTO::getAccountFlowId).collect(Collectors.toList()))
            );
            if (tryList.isEmpty()) {
                break;
            }
            minId = tryList.get(tryList.size() - 1).getAccountFlowId();
        }

    }

    /**
     * 异步处理更新分账流水状态, 处理B2B支付分账流水, 是否完成
     */
    @XxlJob("wxB2bDivideTryAgain")
    public void wxB2bDivideTryAgain() {

        // 轮询最小ID
        Long minId = NumberPool.LOWER_GROUND_LONG;
        for (; ; ) {
            List<DivideFlowDTO> divideFlowDTOList = divideFlowApi.getTryDivideFlow(minId).getCheckedData();
            if (divideFlowDTOList.isEmpty()) {
                return;
            }
            // 拆分批次, 20条一批次处理, 涉及到API请求, 可能会导致请求缓慢
            Lists.partition(divideFlowDTOList, 20).forEach(itemList -> {
                divideFlowApi.updateDivideStatus(itemList);
            });

            // 重置最小键ID
            minId = divideFlowDTOList.get(divideFlowDTOList.size() - 1).getDivideFlowId();
        }
    }
    /**
     * 定时任务生成提现对账单
     */
    @XxlJob("wxB2bAccWithdrawBill")
    public void wxB2bAccWithdrawBill(String data) throws Exception{
        XxlJobHelper.log("定时任务开始生成提现对账单");
        // 判断data是否为null或者为空，如果为空，则获取当前日期
        if (ToolUtil.isEmpty(data)) {
            // 获取昨天的日期
            LocalDate yesterday = LocalDate.now().minusDays(1);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            data = yesterday.format(formatter);
        }
        // 格式化输入的日期，只保留年月日
        String dataFormatted = String.format("%s-%s-%s",
                data.substring(0, 4),
                data.substring(4, 6),
                data.substring(6, 8));
        //获取所有的平台商信息
        List<Long> sysCodeList = partnerApi.getPartnerInfo().getCheckedData().stream().map(PartnerDto::getSysCode).collect(Collectors.toList());
        for (Long sysCode : sysCodeList) {
            //获取appid
            AppletBaseConfigDTO appletBaseConfigDTO = partnerConfigApi.getAppletBaseConfig(sysCode).getCheckedData();
            if (ToolUtil.isEmpty(appletBaseConfigDTO.getAppId())) {
                XxlJobHelper.log("获取平台商{}appid为空",sysCode);
                continue;
            }
            //获取微信accessToken
            String accessToken = RedisWxTokenUtil.getAppletAccessToken(appletBaseConfigDTO.getAppId());
            if(ToolUtil.isEmpty(accessToken)){
                XxlJobHelper.log("AppId:{}获取微信accessToken不存在",appletBaseConfigDTO.getAppId());
                continue;
            }
            //获取微信支付的商户和秘钥
            List<PlatformMerchantNameAndKeyDTO> platformMerchantList = platformMerchantApi.getPlatformMerchantNameAndKey(MerchantTypeEnum.SUPPLIER.getType(), sysCode, PayChannelEnum.WX_B2B_PAY.getCode()).getCheckedData();
            if (ToolUtil.isEmpty(platformMerchantList)) {
                XxlJobHelper.log("获取微信支付的商户和秘钥为空");
                continue;
            }
            for (PlatformMerchantNameAndKeyDTO platformMerchant : platformMerchantList) {
                if(ToolUtil.isEmpty(platformMerchant.getAltMchNo())){
                    XxlJobHelper.log("平台商:{},日期为:{},商户号为空",sysCode,new Date());
                    continue;
                }
                if(ToolUtil.isEmpty(platformMerchant.getAltMchKey())){
                    XxlJobHelper.log("平台商:{},日期为:{},秘钥为空",sysCode,new Date());
                    continue;
                }
                String altNo = platformMerchant.getAltMchNo();
                if(ToolUtil.isEmpty(altNo)){
                    XxlJobHelper.log("平台商:{},日期为:{},商户号为空",sysCode,new Date());
                    continue;
                }
                JSONObject jsonObject = null;
                try{
                    jsonObject = DownloadBillUtils.downloadBill(accessToken, platformMerchant.getAltMchKey(), altNo, Integer.parseInt(data));
                }catch (Exception e){
                    continue;
                }
                if(ToolUtil.isEmpty(jsonObject)){
                    XxlJobHelper.log("下载提现账单失败：{}", jsonObject);
                    continue;
                }
                String fundBillUrl = jsonObject.getString("fund_bill_url");
                if (ToolUtil.isEmpty(fundBillUrl)) {
                    XxlJobHelper.log("平台商{},appid是{},获取微信accessToken是{}",sysCode,appletBaseConfigDTO.getAppId(),accessToken);
                    XxlJobHelper.log("商户号:{},秘钥:{}---获取提现账单信息fundBillUrl不存在日期为:{},错误信息:{}",altNo,platformMerchant.getAltMchKey(),dataFormatted,jsonObject.getString("errmsg"));
                    continue;
                }
                // 生成文件路径
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                String formattedDate = dateFormat.format(new Date());
                String fundBillFilePath = "/path/to/downloaded/" + formattedDate + "_fund_bill.csv";  // 提现账单路径
                // 检查数据库中是否已经存在对应日期和商户号的数据
                int count = withdrawApi.countWithdrawBillsByDateAndAltNo(dataFormatted, altNo);

                // 如果数据存在，删除当天该商户号的所有记录
                if (count > 0) {
                    //删除提现对账单数据
                    withdrawApi.deleteWithdrawBillsByDateAndAltNo(dataFormatted, altNo);
                }
                // 下载提现账单文件
                HttpUtil.downloadFile(fundBillUrl,fundBillFilePath);
                uploadToAliyunOSS(fundBillFilePath);
                insertBillFileRecord(fundBillUrl, dataFormatted, altNo, "提现账单");
                // 解析 提现对账单 CSV 文件
                List<WithdrawalTransactionDTO> withdrawalTransactionDTOS = parseWithdrawalCSVFile(fundBillUrl);
                //保存提现账单
                saveWithdrawalTransactions(withdrawalTransactionDTOS,altNo,sysCode);
                // 删除本地文件
                Files.delete(Paths.get(fundBillFilePath));
                XxlJobHelper.log("定时任务结束生成提现对账单");
            }
        }
    }

    /**
     * 定时任务生成交易对账单并对对账单进行汇总
     */
    @XxlJob("wxB2bAccTransferBill")
    public void wxB2bAccTransferBill(String data) throws Exception{
        log.info("定时任务开始生成交易对账单并对对账单进行汇总操作");
        // 判断data是否为null或者为空，如果为空，则获取当前日期
        if (ToolUtil.isEmpty(data)) {
            // 获取昨天的日期
            LocalDate yesterday = LocalDate.now().minusDays(1);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            data = yesterday.format(formatter);
        }
        // 格式化输入的日期，只保留年月日
        String dataFormatted = String.format("%s-%s-%s",
                data.substring(0, 4),
                data.substring(4, 6),
                data.substring(6, 8));
        //获取所有的平台商信息
        List<Long> sysCodeList = partnerApi.getPartnerInfo().getCheckedData().stream().map(PartnerDto::getSysCode).collect(Collectors.toList());
        for (Long sysCode : sysCodeList) {
            //获取appid
            AppletBaseConfigDTO appletBaseConfigDTO = partnerConfigApi.getAppletBaseConfig(sysCode).getCheckedData();
            if (ToolUtil.isEmpty(appletBaseConfigDTO.getAppId())) {
                log.error("获取平台商{}appid为空",sysCode);
                continue;
            }
            //获取微信accessToken
            String accessToken = RedisWxTokenUtil.getAppletAccessToken(appletBaseConfigDTO.getAppId());
            if(ToolUtil.isEmpty(accessToken)){
                log.error("AppId:{}获取微信accessToken不存在",appletBaseConfigDTO.getAppId());
                continue;
            }
            //获取微信支付的商户和秘钥
            List<PlatformMerchantNameAndKeyDTO> platformMerchantList = platformMerchantApi.getPlatformMerchantNameAndKey(MerchantTypeEnum.SUPPLIER.getType(), sysCode, PayChannelEnum.WX_B2B_PAY.getCode()).getCheckedData();
            if (ToolUtil.isEmpty(platformMerchantList)) {
                log.error("获取微信支付的商户和秘钥为空");
                continue;
            }
            for (PlatformMerchantNameAndKeyDTO platformMerchant : platformMerchantList) {
                if(ToolUtil.isEmpty(platformMerchant.getAltMchNo())){
                    log.error("平台商:{},日期为:{},商户号为空",sysCode,new Date());
                    continue;
                }
                if(ToolUtil.isEmpty(platformMerchant.getAltMchKey())){
                    log.error("平台商:{},日期为:{},秘钥为空",sysCode,new Date());
                    continue;
                }
                String altNo = platformMerchant.getAltMchNo();
                JSONObject jsonObject = null;
                try{
                    jsonObject = DownloadBillUtils.downloadBill(accessToken, platformMerchant.getAltMchKey() , altNo, Integer.parseInt(data));
                }catch (Exception e){
                    continue;
                }
                if(ToolUtil.isEmpty(jsonObject)){
                    log.error("下载交易账单失败：{}", jsonObject);
                    continue;
                }
                String allBillUrl = jsonObject.getString("all_bill_url");
                if (ToolUtil.isEmpty(allBillUrl)) {
                    log.error("平台商{},appid是{},获取微信accessToken是{}",sysCode,appletBaseConfigDTO.getAppId(),accessToken);
                    log.error("商户号:{},秘钥:{}---获取账单信息allBillUrl不存在日期为:{},错误信息:{}",altNo,platformMerchant.getAltMchKey(),dataFormatted,jsonObject.getString("errmsg"));
                    continue;
                }
                // 生成文件路径
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                String formattedDate = dateFormat.format(new Date());
                String allBillFilePath = "/path/to/downloaded/" + formattedDate + "_all_bill.csv";  // 交易账单路径
                // 检查数据库中是否已经存在对应日期和商户号的数据
                List<String> transferBillIds = transferApi.countTransferBillsByDateAndAltNo(dataFormatted, altNo);
                // 如果数据存在，删除当天该商户号的所有记录
                if (ToolUtil.isNotEmpty(transferBillIds)) {
                    //删除交易对账详情表数据
                    transferApi.deleteTransferBillsByDateAndAltNo(dataFormatted, altNo);
                    for (String transferBillId : transferBillIds) {
                        //删除交易对账主表数据
                        transferApi.deleteTransferBill(transferBillId);
                    }
                }
                // 下载交易账单文件
                HttpUtil.downloadFile(allBillUrl,allBillFilePath);
                uploadToAliyunOSS(allBillFilePath);
                insertBillFileRecord(allBillUrl, dataFormatted, altNo, "交易账单");
                // 解析 交易对账单 CSV 文件
                List<WeChatTransactionDTO> weChatTransactionDTOS = parseCSVFile(allBillUrl);
                //对账 生成对账单明细
                List<AccTransferBillOrderDTO> billOrderList = reconcileStatementDetails(weChatTransactionDTOS);
                //汇总统计对账单主表
                processTransferBillData(billOrderList);
                // 插入对账记录
                transferApi.insertTransferBillOrder(billOrderList);
                // 删除本地文件
                Files.delete(Paths.get(allBillFilePath));
                log.info("定时任务结束生成交易对账单并对对账单进行汇总操作");
            }
        }
    }

    /**
     * 保存提现账单
     * @param withdrawalTransactionDTOS
     */
    public void saveWithdrawalTransactions(List<WithdrawalTransactionDTO> withdrawalTransactionDTOS,String altNo,Long sysCode) {
        List<AccWithdrawBillDTO> accWithdrawBills = withdrawalTransactionDTOS.stream()
                .map(dto -> {
                    AccWithdrawBillDTO bill = new AccWithdrawBillDTO();
                    bill.setSysCode(sysCode); // 设置平台商id
                    bill.setPlatformWithdrawAmt(dto.getAmount());
                    bill.setPlatformFree(BigDecimal.ZERO); // 设置平台提现手续费
                    bill.setPlatformTradeNo(dto.getFundFlowOrderNo());
                    bill.setBankAccountNo(dto.getFundFlowOrderNo()); // 设置银行卡号
                    bill.setRequestTime(dto.getTransactionTime());
                    bill.setFinishTime(dto.getTransactionTime());
                    bill.setAltNo(altNo); // 设置提现商户号
                    bill.setState(0); // 设置状态为正常
                    bill.setRemark("提现记录");
                    bill.setPlatform("wxb2b"); // 设置支付平台
                    return bill;
                })
                .collect(Collectors.toList());
        // 批量保存到数据库
        withdrawApi.insertAccWithdrawBillBatch(accWithdrawBills);
    }

    /**
     * 插入账单文件记录
     * @param file
     * @param data
     * @param altNo
     * @param type
     */
    private void insertBillFileRecord(String file, String data, String altNo, String type) {
        AccBillFileDTO accBillFileDTO = new AccBillFileDTO();
        accBillFileDTO.setFile(file);
        accBillFileDTO.setDate(data); // 确保将日期格式化为正确的字符串
        accBillFileDTO.setAltNo(altNo);
        accBillFileDTO.setType(type);

        // 检查记录是否存在
        List<String> billFileIds = transferApi.checkBillFileRecordExists(altNo,data);

        if (ToolUtil.isNotEmpty(billFileIds)) {
            for (String billFileId : billFileIds) {
                // 删除已存在的记录
                transferApi.deleteBillFileRecord(billFileId);
            }
        }

        // 插入新记录
        transferApi.insertBillFileRecord(accBillFileDTO);
    }
    /**
     * 汇总统计对账单主表
     * @param billOrderList
     */
    private void processTransferBillData(List<AccTransferBillOrderDTO> billOrderList) {
        if (ToolUtil.isEmpty(billOrderList)) {
            return; // 如果列表为空，直接返回
        }

        // 格式化日期为 yyyyMMdd 格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date transferTime = billOrderList.get(0).getTransferTime();
        String formattedTransferTime = dateFormat.format(transferTime);

        // 创建用于汇总的 AccTransferBillDTO，只赋值必要的基础信息
        AccTransferBillDTO transferBill = new AccTransferBillDTO();
        transferBill.setBillDate(formattedTransferTime);
        transferBill.setSysCode(billOrderList.get(0).getSysCode());
        transferBill.setCreateTime(new Date());
        transferBill.setPlatform(billOrderList.get(0).getPlatform());

        // 初始化汇总字段
        int transferNum = 0;
        BigDecimal totalMerchantPayAmt = BigDecimal.ZERO;
        BigDecimal totalPlatformPayAmt = BigDecimal.ZERO;
        BigDecimal totalMerchantRefundAmt = BigDecimal.ZERO;
        BigDecimal totalPlatformRefundAmt = BigDecimal.ZERO;
        BigDecimal totalMerchantPayFree = BigDecimal.ZERO;
        BigDecimal totalPlatformPayFree = BigDecimal.ZERO;
        BigDecimal totalMerchantRefundFree = BigDecimal.ZERO;
        BigDecimal totalPlatformRefundFree = BigDecimal.ZERO;
        int refundCount = 0;

        // 遍历订单列表，统计金额和数量
        for (AccTransferBillOrderDTO billOrder : billOrderList) {
            // 根据 orderType 判断是付款还是退款
            if (billOrder.getOrderType() == 0) { // 付款
                transferNum++;
                totalMerchantPayAmt = totalMerchantPayAmt.add(
                        billOrder.getMerchantPayAmt() != null ? billOrder.getMerchantPayAmt() : BigDecimal.ZERO
                );
                totalPlatformPayAmt = totalPlatformPayAmt.add(
                        billOrder.getPlatformPayAmt() != null ? billOrder.getPlatformPayAmt() : BigDecimal.ZERO
                );
                totalMerchantPayFree = totalMerchantPayFree.add(
                        billOrder.getMerchantPayFree() != null ? billOrder.getMerchantPayFree() : BigDecimal.ZERO
                );
                totalPlatformPayFree = totalPlatformPayFree.add(
                        billOrder.getPlatformPayFree() != null ? billOrder.getPlatformPayFree() : BigDecimal.ZERO
                );
            } else if (billOrder.getOrderType() == 1) { // 退款
                refundCount++;
                totalMerchantRefundAmt = totalMerchantRefundAmt.add(
                        billOrder.getMerchantRefundAmt() != null ? billOrder.getMerchantRefundAmt() : BigDecimal.ZERO
                );
                totalPlatformRefundAmt = totalPlatformRefundAmt.add(
                        billOrder.getPlatformRefundAmt() != null ? billOrder.getPlatformRefundAmt() : BigDecimal.ZERO
                );
                totalMerchantRefundFree = totalMerchantRefundFree.add(
                        billOrder.getMerchantRefundFree() != null ? billOrder.getMerchantRefundFree() : BigDecimal.ZERO
                );
                totalPlatformRefundFree = totalPlatformRefundFree.add(
                        billOrder.getPlatformRefundFree() != null ? billOrder.getPlatformRefundFree() : BigDecimal.ZERO
                );
            }
        }

        // 将汇总的金额和数量赋值到 transferBill
        transferBill.setTransferNum(transferNum);
        transferBill.setMerchantTotalPayAmt(totalMerchantPayAmt);
        transferBill.setPlatformTotalPayAmt(totalPlatformPayAmt);
        transferBill.setMerchantTotalRefundAmt(totalMerchantRefundAmt);
        transferBill.setPlatformTotalRefundAmt(totalPlatformRefundAmt);
        transferBill.setMerchantTotalPayFree(totalMerchantPayFree);
        transferBill.setPlatformTotalPayFree(totalPlatformPayFree);
        transferBill.setMerchantTotalRefundFree(totalMerchantRefundFree);
        transferBill.setPlatformTotalRefundFree(totalPlatformRefundFree);
        transferBill.setRefundCount(refundCount);

        // 生成 transferBillId，具体实现取决于您使用的 ID 生成策略
        long transferBillId = System.currentTimeMillis(); // 自定义生成 ID 的方法
        transferBill.setTransferBillId(transferBillId);

        // 插入汇总数据到数据库中
        transferApi.insertTransferBill(transferBill);

        // 更新 billOrderList 中的 transferBillId
        for (AccTransferBillOrderDTO billOrder : billOrderList) {
            billOrder.setTransferBillId(transferBillId);
        }
    }

    /**
     * 生成对账单明细---对账
     * @param weChatTransactionDTOS
     */
    private List<AccTransferBillOrderDTO> reconcileStatementDetails(List<WeChatTransactionDTO> weChatTransactionDTOS){
        List<AccTransferBillOrderDTO> billOrderList = new ArrayList<>();
        for (WeChatTransactionDTO transaction : weChatTransactionDTOS) {
            // 判断是订单还是退单
            boolean isRefund = "REFUND".equals(transaction.getTransactionStatus());

            // 根据订单或退单类型查询支付流水记录
            PayFlowDTO payFlow;
            if (isRefund) {
                // 退单，使用 merchantRefundNo 查询
                payFlow = payFlowApi.getAccPayFlowOrder(Long.valueOf(transaction.getMerchantRefundNo())).getCheckedData();
            } else {
                // 订单，使用 merchantOrderNo 查询
                payFlow = payFlowApi.getAccPayFlowOrder(Long.valueOf(transaction.getMerchantOrderNo())).getCheckedData();
            }


            if (ToolUtil.isNotEmpty(payFlow)) {
                AccTransferBillOrderDTO billOrder = new AccTransferBillOrderDTO();
                billOrder.setSysCode(payFlow.getSysCode());
                billOrder.setCreateTime(new Date());
                billOrder.setPlatform("wxb2b");
                billOrder.setPlatformTradeNo(payFlow.getTradeNo());
                billOrder.setMerchantTradeNo(isRefund ? transaction.getMerchantRefundNo() : transaction.getMerchantOrderNo());
                billOrder.setOrderType(isRefund ? 1 : 0); // 1:退款, 0:付款
                billOrder.setAltNo(transaction.getMerchantId());
                billOrder.setTransferTime(transaction.getTransactionTime());
                billOrder.setPlatformPayAmt(payFlow.getPayAmt());
                billOrder.setMerchantPayAmt(transaction.getOrderAmount());

                // 异常类型字段
                List<ExceptionType> exceptions = new ArrayList<>();

                if (isRefund) {
                    // 设置退款相关的手续费
                    billOrder.setPlatformRefundAmt(payFlow.getRefundAmt());
                    billOrder.setMerchantRefundAmt(transaction.getRefundAmount());
                    billOrder.setPlatformRefundFree(payFlow.getFee()); // 设置平台退款手续费
                    billOrder.setMerchantRefundFree(transaction.getTechnicalServiceFee()); // 设置商户退款手续费

                    // 检查退款金额是否一致
                    if (payFlow.getRefundAmt().compareTo(transaction.getRefundAmount()) != 0) {
                        billOrder.setState(1); // 异常
                        billOrder.setRemark("退款金额不一致");
                        exceptions.add(ExceptionType.REFUND_AMOUNT_MISMATCH);
                    }

                    // 检查退款手续费是否一致
                    if (payFlow.getFee().compareTo(transaction.getTechnicalServiceFee()) != 0) {
                        billOrder.setState(1);
                        billOrder.setRemark("退款手续费不一致");
                        exceptions.add(ExceptionType.FEE_MISMATCH);
                    }
                } else {
                    // 设置付款相关的手续费
                    billOrder.setPlatformPayFree(payFlow.getFee());
                    billOrder.setMerchantPayFree(transaction.getTechnicalServiceFee());

                    // 检查付款金额是否一致
                    if (payFlow.getPayAmt().compareTo(transaction.getOrderAmount()) != 0) {
                        billOrder.setState(1); // 异常
                        billOrder.setRemark("支付金额不一致");
                        exceptions.add(ExceptionType.PAY_AMOUNT_MISMATCH);
                    }

                    // 检查付款手续费是否一致
                    if (payFlow.getFee().compareTo(transaction.getTechnicalServiceFee()) != 0) {
                        billOrder.setState(1);
                        billOrder.setRemark("付款手续费不一致");
                        exceptions.add(ExceptionType.FEE_MISMATCH);
                    }
                }

                if (exceptions.isEmpty()) {
                    billOrder.setState(0); // 正常
                    billOrder.setRemark("对账成功");
                } else {
                    billOrder.setExceptionTypes(exceptions); // 设置异常场景
                }

                billOrderList.add(billOrder);
            } else {
                log.error("订单号：" + transaction.getMerchantOrderNo() + "退款订单号:" + transaction.getMerchantRefundNo() + "未找到对应的支付流水记录，请检查数据是否正确");
            }
        }
        return billOrderList;
    }

    // 上传文件到阿里云 OSS
    private void uploadToAliyunOSS(String filePath){
        try {
            // 读取本地文件并转换为 MultipartFile
            File file = new File(filePath);
            FileInputStream inputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(
                    file.getName(),    // 文件名
                    file.getName(),    // 原始文件名
                    "text/csv",        // 内容类型，根据实际情况调整
                    inputStream        // 文件流
            );

            // 调用 fileApi.upload() 方法上传
            fileApi.upload(multipartFile);
        } catch (Exception e) {
            log.error(" uploadToAliyunOSS异常,", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 解析日期
     */
    private Date parseDate(String dateStr, SimpleDateFormat dateFormat){
        try {
            return dateStr != null && !dateStr.isEmpty() ? dateFormat.parse(dateStr) : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将字符串转换为 BigDecimal
     */
    private BigDecimal parseBigDecimal(String str){
        try {
            return str != null && !str.isEmpty() ? new BigDecimal(str) : BigDecimal.ZERO;
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 解析提现相关的CSV文件
     * @param fundBillUrl CSV文件URL
     * @return 提现交易列表
     */
    private List<WithdrawalTransactionDTO> parseWithdrawalCSVFile(String fundBillUrl) {
        List<WithdrawalTransactionDTO> transactionList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> errorLines = new ArrayList<>(); // 用于存储出错的行信息

        // 下载并读取CSV数据
        // 下载并读取 CSV 数据
        byte[] csvData = HttpUtil.downloadBytes(fundBillUrl);
        CsvReader reader = CsvUtil.getReader();
        CsvData read = reader.read(IoUtil.getReader(new ByteArrayInputStream(csvData), CharsetUtil.CHARSET_UTF_8));

        // 按行处理 CSV 内容
        for (int i = 0; i < read.getRows().size(); i++) {
            CsvRow fields = read.getRow(i);
            if (i == 0) {
                continue;
            }
            // 替换无效字符
            for (int i1 = 0; i1 < fields.size(); i1++) {
                String temp = fields.get(i1);
                if (StringUtils.isNotEmpty(temp)) {
                    fields.set(i1, temp.replace("`", ""));
                }
            }
            // 仅当业务类型为"提现"时处理该记录
            if (!fields.get(4).contains("提现")) continue;
            try {
                WithdrawalTransactionDTO transaction = WithdrawalTransactionDTO.builder()
                        .transactionTime(parseDate(fields.get(0), dateFormat))
                        .b2bPaymentOrderNo(fields.get(1))
                        .fundFlowOrderNo(fields.get(2))
                        .businessName(fields.get(3))
                        .businessType(fields.get(4))
                        .transactionType(fields.get(5))
                        .amount(parseBigDecimal(fields.get(6)))
                        .accountBalance(parseBigDecimal(fields.get(7)))
                        .build();

                transactionList.add(transaction);
            } catch (Exception e) {
                errorLines.add("行 " + i + " 数据解析错误：" + e.getMessage());
            }
        }

        // 打印错误行信息
        if (!errorLines.isEmpty()) {
            errorLines.forEach(System.err::println);
        }

        return transactionList;
    }

    /**
     * 解析 CSV 文件
     * @param allBillUrl
     * @return
     */
    private List<WeChatTransactionDTO> parseCSVFile(String allBillUrl) {
        List<WeChatTransactionDTO> transactionList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
        List<String> errorLines = new ArrayList<>(); // 存储出错的行信息

        // 下载并读取 CSV 数据
        byte[] csvData = HttpUtil.downloadBytes(allBillUrl);
        CsvReader reader = CsvUtil.getReader();
        CsvData read = reader.read(IoUtil.getReader(new ByteArrayInputStream(csvData), CharsetUtil.CHARSET_UTF_8));

        // 按行处理 CSV 内容
        for (int i = 0; i < read.getRows().size(); i++) {
            CsvRow fields = read.getRow(i);
            if (i == 0) {
                continue;
            }
            // 替换无效字符
            for (int i1 = 0; i1 < fields.size(); i1++) {
                String temp = fields.get(i1);
                if (StringUtils.isNotEmpty(temp)) {
                    fields.set(i1, temp.replace("`", ""));
                }
            }
            // 构建 WeChatTransactionDTO 对象
            try {
                WeChatTransactionDTO transaction = WeChatTransactionDTO.builder()
                        .transactionTime(parseDate(fields.get(0), dateFormat))
                        .publicAccountId(fields.get(1))
                        .merchantId(fields.get(2))
                        .weChatOrderNo(fields.get(3))
                        .merchantOrderNo(fields.get(4))
                        .userIdentifier(fields.get(5))
                        .transactionStatus(fields.get(6))
                        .weChatRefundNo(fields.get(7))
                        .merchantRefundNo(fields.get(8))
                        .refundAmount(parseBigDecimal(fields.get(9)))
                        .refundStatus(fields.get(10))
                        .productName(fields.get(11))
                        .merchantData(fields.get(12))
                        .technicalServiceFee(parseBigDecimal(fields.get(13)))
                        .serviceFeeRate(fields.get(14))
                        .orderAmount(parseBigDecimal(fields.get(15)))
                        .refundRequestAmount(parseBigDecimal(fields.get(16)))
                        .settlementStatus(fields.get(17))
                        .settlementTime(parseDate(fields.get(18), dateFormat))
                        .build();

                transactionList.add(transaction);
            } catch (Exception e) {
                errorLines.add("行 " + i + " 数据解析错误：" + e.getMessage());
            }
        }
        // 统一打印错误行信息
        if (!errorLines.isEmpty()) {
            errorLines.forEach(System.err::println);
        }
        return transactionList;
    }
}

