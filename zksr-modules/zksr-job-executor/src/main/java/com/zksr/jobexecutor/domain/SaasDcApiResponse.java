package com.zksr.jobexecutor.domain;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("Saas-dc api 响应体")
public class SaasDcApiResponse<T> {
    private String code;
    private String msg;
    private String errMsg;
    private String timestamp;
    private T data;

    @Data
    public static class DataList<U> {
        private List<U> list;
    }

    public boolean isSuccess() {
        return "0".equals(code);
    }

    public boolean isError() {
        return !"0".equals(code);
    }
}
