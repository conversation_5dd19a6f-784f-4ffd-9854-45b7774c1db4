package com.zksr.jobexecutor.service.impl;

import com.alicp.jetcache.Cache;
import com.zksr.common.redis.service.RedisService;
import com.zksr.jobexecutor.service.IJobCacheService;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.OrderSettingPolicyDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024年04月09日 17:14
 * @description: IJobCacheServiceImpl
 */
@Service
@Slf4j
public class JobCacheServiceImpl implements IJobCacheService {
    @Autowired
    private RedisService redisService;

    @Autowired
    private Cache<Long, OrderSettingPolicyDTO> orderSettingPolicyCache;

    @Autowired
    private Cache<Long, SpuDTO> spuDTOCache;

    @Autowired
    private Cache<Long, SkuDTO> skuDTOCache;

    @Resource
    private PartnerPolicyApi partnerPolicyApi;

    @Resource
    private SpuApi spuApi;

    @Resource
    private SkuApi skuApi;

    @PostConstruct
    public void init() {
        orderSettingPolicyCache.config().setLoader(this::loadOrderSettingPolicyFromApi);
        spuDTOCache.config().setLoader(this::loadSpuDTOFromApi);
        skuDTOCache.config().setLoader(this::loadSkuDTOFromApi);
    }

    private SkuDTO loadSkuDTOFromApi(Long skuId) {
        return skuApi.getBySkuId(skuId).getCheckedData();
    }

    private SpuDTO loadSpuDTOFromApi(Long spuId) {
        return spuApi.getBySpuId(spuId).getCheckedData();
    }

    private OrderSettingPolicyDTO loadOrderSettingPolicyFromApi(Long dcId){
        return partnerPolicyApi.getOrderSettingPolicy(dcId).getCheckedData();
    }

    @Override
    public OrderSettingPolicyDTO getOrderSettingPolicyInfo(Long dcId) {
        return orderSettingPolicyCache.get(dcId);
    }

    @Override
    public SpuDTO getSpuDTO(Long spuId) {
        return spuDTOCache.get(spuId);
    }

    @Override
    public SkuDTO getSkuDTO(Long skuId) {
        return skuDTOCache.get(skuId);
    }
}
