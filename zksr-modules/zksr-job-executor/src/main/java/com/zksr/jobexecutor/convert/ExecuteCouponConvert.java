package com.zksr.jobexecutor.convert;

import com.zksr.promotion.api.coupon.dto.CouponTemplateExtendDTO;
import com.zksr.trade.api.order.vo.CouponExtendTotalVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/5/28
 * @desc
 */
@Mapper
public interface ExecuteCouponConvert {

    ExecuteCouponConvert INSTANCE = Mappers.getMapper(ExecuteCouponConvert.class);

    List<CouponTemplateExtendDTO> convertExtendDTOList(List<CouponExtendTotalVO> extendTotalVOList);

}
