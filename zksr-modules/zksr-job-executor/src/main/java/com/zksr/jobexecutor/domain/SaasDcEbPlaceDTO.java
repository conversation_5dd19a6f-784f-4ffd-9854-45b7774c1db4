package com.zksr.jobexecutor.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

@Data
@ApiModel("Saas dc 行政区域实体")
public class SaasDcEbPlaceDTO {
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("创建用户")
    private String createUserCode;
    @ApiModelProperty("创建用户名称")
    private String createUserName;
    @ApiModelProperty("修改用户")
    private String updateUserCode;
    @ApiModelProperty("修改用户名称")
    private String updateUserName;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("逻辑删除:0-正常,1-删除")
    private Integer deleteFlag;
    @ApiModelProperty("软删除key")
    private Long deleteKey;
    @ApiModelProperty("软删除时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date deleteTime;
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date updateTime;
    @ApiModelProperty("数据版本")
    private Integer version;
    @ApiModelProperty("租户编码")
    private String tenantCode;
    @ApiModelProperty("地点级别")
    private String ebplType;
    @ApiModelProperty("代码")
    private String ebplCode;
    @ApiModelProperty("中文名称")
    private String ebplNameCn;
    @ApiModelProperty("是否可用:0是启用/1是停用")
    private Integer ebplIsAble;
    @ApiModelProperty("上级地点code")
    private String ebplParentPmCode;
    @ApiModelProperty("地区区号")
    private String ebplAreaNumber;
    @ApiModelProperty("国家编码")
    private String countryCode;
    @ApiModelProperty("经度")
    private String longitude;
    @ApiModelProperty("纬度")
    private String latitude;

}
