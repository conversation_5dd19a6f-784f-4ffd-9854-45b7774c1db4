package com.zksr.jobexecutor.task;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.common.security.utils.WxHttpUtil;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/6/14 14:19
 */
@Component
@Slf4j
public class AccessTokenXxlJob {

    @Resource
    private PartnerConfigApi partnerConfigApi;

    @Resource
    private PartnerApi partnerApi;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private RedisService redisService;

    /**
     * 获取accessToken
     */
    @XxlJob("getAccessTokenJobHandler")
    public void getAccessTokenJobHandler() {
        XxlJobHelper.log("准备开始获取公众号accessToken");
        HashMap<String, String> appidList = new HashMap<>();
        List<PartnerDto> partnerDtos = partnerApi.getPartnerInfo().getCheckedData();
        for (PartnerDto partnerDto : partnerDtos) {
            AppletBaseConfigDTO appletBaseConfigDTO = partnerConfigApi.getAppletBaseConfig(partnerDto.getSysCode()).getCheckedData();
            if (Objects.nonNull(appletBaseConfigDTO)) {
                if (StringUtils.isNotEmpty(appletBaseConfigDTO.getPublishAppId())
                        && StringUtils.isNotEmpty(appletBaseConfigDTO.getPublishSecret())) {
                    appidList.put(appletBaseConfigDTO.getPublishAppId(), appletBaseConfigDTO.getPublishSecret());
                }
                if (StringUtils.isNotEmpty(appletBaseConfigDTO.getAppId())
                        && StringUtils.isNotEmpty(appletBaseConfigDTO.getAppSecret())) {
                    appidList.put(appletBaseConfigDTO.getAppId(), appletBaseConfigDTO.getAppSecret());
                }
            }
        }
        String supplierPublishAppid = redisSysConfigService.getSupplierPublishAppid();
        if (StringUtils.isNotEmpty(supplierPublishAppid)) {
            appidList.put(supplierPublishAppid, redisSysConfigService.getSupplierPublishAppSecret());
        }
        appidList.forEach((appid,appSecret) -> {

            // 如果配置了从远程地址获取accesstoken的话, 本机暂不维护最新token
            if (StringUtils.isNotEmpty(redisSysConfigService.getAccessTokenUrl(appid))) {
                XxlJobHelper.log("如果配置了从远程地址获取accesstoken的话, 本机暂不维护最新token, {}", appid);
               log.info("如果配置了从远程地址获取accesstoken的话, 本机暂不维护最新token, {}", appid);
               return;
            }

            String returnJson = WxHttpUtil.get(StringUtils.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={}&secret={}", appid, appSecret));
            XxlJobHelper.log("appid={},结果={}", appid, returnJson);
            JSONObject object = JSON.parseObject(returnJson);
            if (object.containsKey("access_token")) {
                // 设置accessToken
               redisService.setCacheObject(StringUtils.format("{}{}", RedisConstants.ACCESS_TOKEN, appid), object.getString("access_token"));
            }
        });
    }
}
