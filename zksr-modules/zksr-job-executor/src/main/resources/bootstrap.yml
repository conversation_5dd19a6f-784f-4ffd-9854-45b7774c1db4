# Tomcat
server:
  port: 6400

# Spring
spring:
  application:
    # 应用名称
    name: zksr-job-executor
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 10.27.12.137:8848
        namespace: b2b-dev
        username: nacos
        password: 50vGMVGRtg
      config:
        # 配置中心地址
        server-addr: 10.27.12.137:8848
        namespace: b2b-dev
        username: nacos
        password: 50vGMVGRtg
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-redis-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
