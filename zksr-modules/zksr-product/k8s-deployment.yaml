apiVersion: apps/v1
kind: Deployment
metadata:
  name: zksr-product
  namespace: haixin
  labels:
    app: zksr-product
spec:
  replicas: 4
  selector:
    matchLabels:
      app: zksr-product
  template:
    metadata:
      labels:
        app: zksr-product
    spec:
      containers:
        - name: zksr-product
          image: haixin.tencentcloudcr.com/hisense/u-biz/prod/b2b/product:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 6101
          env:
            - name: TZ
              value: "Asia/Shanghai"
            - name: SPRING_PROFILES_ACTIVE
              value: "dev"
          resources:
            requests:
              memory: "6Gi"
              cpu: "1000m"
            limits:
              memory: "8Gi"
              cpu: "2000m"
      imagePullSecrets:
        - name: hisense