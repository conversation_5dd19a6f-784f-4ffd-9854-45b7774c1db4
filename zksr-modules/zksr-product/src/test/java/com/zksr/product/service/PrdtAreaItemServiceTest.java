package com.zksr.product.service;

import com.zksr.common.core.domain.vo.openapi.receive.AreaItemOpenDTO;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.ZksrMallProductApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: chenyj8
 * @Desciption: 类功能描述
 */
@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallProductApplication.class)
public class PrdtAreaItemServiceTest {


    @Resource
    private IPrdtAreaItemService areaItemService;

    @BeforeEach
    void setUp() {

    }


    @Test
    public void testAddAreaItem(){
        AreaItemOpenDTO openDTO = new AreaItemOpenDTO();
        openDTO.setSpuNo("0003434");
        openDTO.setSupplierId(623735917012647936L);
        openDTO.setMinShelfStatus(1);
        openDTO.setItemType(0);
        openDTO.setSysCode(4L);

        areaItemService.addAreaItem(openDTO);
    }

}
