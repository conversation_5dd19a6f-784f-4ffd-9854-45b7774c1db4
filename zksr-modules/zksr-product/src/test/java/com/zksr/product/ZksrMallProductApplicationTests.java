package com.zksr.product;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.elasticsearch.domain.EsLocalProduct;
import com.zksr.common.elasticsearch.domain.EsProductGroup;
import com.zksr.common.elasticsearch.mapper.EsLocalProductFullMapper;
import com.zksr.common.elasticsearch.mapper.EsProductMapper;
import com.zksr.common.redis.service.RedisService;
import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.api.model.event.EsSpuUpdateProductEvent;
import com.zksr.product.api.spu.dto.SpuductOpenDTO;
import com.zksr.product.api.spu.vo.PrdtSpuPageReqVO;
import com.zksr.product.cache.handler.impl.SpuUpdateProductHandler;
import com.zksr.product.controller.spu.vo.ExcelImportVO;
import com.zksr.product.domain.PrdtProperty;
import com.zksr.product.domain.PrdtPropertyVal;
import com.zksr.product.domain.PrdtSku;
import com.zksr.product.domain.PrdtSpu;
import com.zksr.product.mapper.PrdtPropertyMapper;
import com.zksr.product.mapper.PrdtPropertyValMapper;
import com.zksr.product.mapper.PrdtSkuMapper;
import com.zksr.product.mapper.PrdtSpuMapper;
import com.zksr.product.service.IPrdtBranchYhdataService;
import com.zksr.product.service.IPrdtSkuService;
import com.zksr.product.service.IPrdtSpuService;
import com.zksr.product.service.IProductContentService;
import com.zksr.system.api.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.conditions.update.LambdaEsUpdateWrapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.*;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallProductApplication.class)
@Slf4j
public class ZksrMallProductApplicationTests {

    /*@Autowired
    private IPrdtSpuService prdtSpuService;

    @Test
    public void testElasticProductService() {

        SpuductOpenDTO vo = new SpuductOpenDTO();
        vo.setSpuNo("by01134Test");
        vo.setSupplierId(569046565128339456L);
        vo.setOldestDate(new Date());
        vo.setLatestDate(new Date());
        prdtSpuService.updatePrdData(vo);
    }*/

   /* @Autowired
    private SpuUpdateProductHandler productHandler;

    @Test
    public void testRedisService() {
        EsProductEvent<EsSpuUpdateProductEvent> item = new EsProductEvent();
        item.setData(new EsSpuUpdateProductEvent(new ArrayList<>()));
        productHandler.execEvent(item);
    }*/

    /*@Autowired
    private PrdtSpuMapper spuMapper;

    @Autowired
    private PrdtSkuMapper skuMapper;

    @Autowired
    private PrdtPropertyMapper prdtPropertyMapper;

    @Autowired
    private PrdtPropertyValMapper prdtPropertyValMapper;

    @Test
    public void loadExcel() throws Exception {

        ExcelUtil<ExcelImportVO> util = new ExcelUtil<ExcelImportVO>(ExcelImportVO.class);
        List<ExcelImportVO> userList = util.importExcel(new FileInputStream("E:\\tmp\\2024年6月5日.xlsx"));
        //List<ExcelImportVO> userList = util.importExcel(new FileInputStream("E:\\tmp\\test.xlsx"));

        Long unit = 5L;
        Long sysCode = -178L;

        Map<PrdtSpu, List<PrdtSku>> spus = new HashMap();
        Map<Integer, List<ExcelImportVO>> goodsMap = userList.stream().collect(Collectors.groupingBy(ExcelImportVO::getId));
        goodsMap.forEach((goodsId, itemList) -> {
            ExcelImportVO spuItem = itemList.get(0);

            PrdtSpu prdtSpu = PrdtSpu.builder()
                    .sysCode(sysCode)
                    .costPrice(spuItem.getCostPrice())
                    .spuNo(StrUtil.maxLength(spuItem.getSpuNo(), 28))
                    .spuName(spuItem.getSpuName())
                    .thumb(StringUtils.isNotEmpty(spuItem.getImage()) ? ("https://mfxp.dianxiaomeng.cn/" + spuItem.getImage()) : null)
                    .images(StringUtils.isNotEmpty(spuItem.getImage()) ? ("https://mfxp.dianxiaomeng.cn/" + spuItem.getImage()) : null)
                    .stock(spuItem.getStock())
                    .status(1L)
                    .isSpecs(spuItem.getSpecs())
                    .minUnit(unit)
                    .isDelete(0L)
                    .build();
            spuMapper.insert(prdtSpu);

            List<PrdtSku> skuList = new ArrayList<>();
            // 多规格
            if (spuItem.getSpecs() == 1 && StringUtils.isNotEmpty(spuItem.getPropertyNames())) {
                System.out.println("LOG1" + spuItem);
                ArrayList<String> propertyNames = ListUtil.toList(spuItem.getPropertyNames().split(StringPool.COMMA));
                Collections.reverse(propertyNames);

                ArrayList<PrdtProperty> prdtProperties = new ArrayList<>();
                for (int i = 0; i < propertyNames.size(); i++) {
                    String[] specData = propertyNames.get(i).split("__");
                    String name = specData[0];
                    PrdtProperty property = PrdtProperty.builder()
                            .spuId(prdtSpu.getSpuId())
                            .sysCode(sysCode)
                            .name(name)
                            .isDelete(0L)
                            .status(1L)
                            .build();
                    prdtPropertyMapper.insert(property);
                    prdtProperties.add(property);
                }

                Map<String, PrdtPropertyVal> cacheMap = new HashMap<>();

                for (ExcelImportVO importVO : itemList) {
                    System.out.println(importVO);
                    ArrayList<String> propertyVals = ListUtil.toList(importVO.getPropertyNames().split(StringPool.COMMA));
                    List<PropertyAndValDTO> propertyList = new ArrayList<>();
                    for (int i = 0; i < propertyVals.size(); i++) {
                        String propertyKey = propertyVals.get(i);
                        String[] specData = propertyKey.split("__");
                        String value = specData[1];
                        PrdtProperty prdtProperty = prdtProperties.get(i);
                        PrdtPropertyVal property = null;
                        if (!cacheMap.containsKey(propertyKey)) {
                            property = PrdtPropertyVal.builder()
                                    .spuId(prdtSpu.getSpuId())
                                    .sysCode(sysCode)
                                    .name(value)
                                    .isDelete(0L)
                                    .status(1L)
                                    .propertyId(prdtProperty.getPropertyId())
                                    .build();
                            prdtPropertyValMapper.insert(property);
                            cacheMap.put(propertyKey, property);
                        } else {
                            property = cacheMap.get(propertyKey);
                        }
                        propertyList.add(
                                PropertyAndValDTO
                                        .builder()
                                        .propertyId(prdtProperty.getPropertyId())
                                        .propertyName(prdtProperty.getName())
                                        .propertyValId(property.getPropertyValId())
                                        .valName(property.getName())
                                        .build()
                        );
                    }
                    PrdtSku prdtSku = PrdtSku.builder()
                            .unit(unit)
                            .sysCode(sysCode)
                            .spuId(prdtSpu.getSpuId())
                            .barcode(StrUtil.maxLength(importVO.getSkuBarcode(), 60))
                            .stock(importVO.getSkuStock())
                            .markPrice(importVO.getSkuSalePrice())
                            .suggestPrice(importVO.getSkuPrePrice())
                            .costPrice(importVO.getSkuCostPrice())
                            .properties(JSON.toJSONString(propertyList))
                            .status(1L)
                            .build();
                    skuList.add(prdtSku);
                }
            } else {
                PrdtSku prdtSku = PrdtSku.builder()
                        .unit(unit)
                        .sysCode(sysCode)
                        .spuId(prdtSpu.getSpuId())
                        .barcode(StrUtil.maxLength(spuItem.getSpuNo(), 60))
                        .stock(spuItem.getStock())
                        .markPrice(spuItem.getSalePrice())
                        .suggestPrice(spuItem.getItemPrePrice())
                        .costPrice(spuItem.getCostPrice())
                        .status(1L)
                        .build();
                skuList.add(prdtSku);
            }
            skuMapper.insertBatch(skuList);
            spus.put(prdtSpu, skuList);
        });


    }*/


    /*@Autowired
    private IPrdtBranchYhdataService prdtBranchYhdataService;

    @Test
    public void test05() {
        prdtBranchYhdataService.initData();
    }*/

    /*@Autowired
    private IPrdtSpuService prdtSpuServiceImpl;

    @Test
    public void test05() {
        PrdtSpuPageReqVO vo = new PrdtSpuPageReqVO();
        vo.setSupplierId(554977566507499520L);
        vo.setPageNo(1);
        vo.setPageSize(50);
        vo.setSourceNo("ZS001");
        prdtSpuServiceImpl.getSpuExportList(vo);
    }*/

    /*@Autowired
    private EsProductMapper productMapper;

    @Test
    public void test02() {
        productMapper.deleteById("wangketest");
        EsProductGroup entity = new EsProductGroup();
        entity.setId("wangketest");
        entity.setSpuName("王珂珂呜呜呜");
        entity.setWhiteBranchList(ListUtil.toList(-1L));
        entity.setBlackBranchList(ListUtil.toList(5L, 6L));
        productMapper.insert(entity);
        LambdaEsQueryWrapper<EsProductGroup> qw = new LambdaEsQueryWrapper<>();
        qw.and(and ->
                and.and(
                                and2 -> and2.in(EsProductGroup::getWhiteBranchList, 5L).or().in(EsProductGroup::getWhiteBranchList, -1L)
                        )
                        .and(
                                and1 -> and1.not().in(EsProductGroup::getBlackBranchList, 5L)
                        )
        );
        List<EsProductGroup> wangketest = productMapper.selectList(qw);
        System.out.println(wangketest);
    }*/

    /*@Autowired
    private IProductContentService productContentService;

    @Test
    public void test04() {
        productContentService.refreshProduct();
    }*/

    /*@Autowired
    private IPrdtSpuService prdtSpuServiceImpl;

    @Test
    public void test05() {
        PrdtSpuPageReqVO vo = new PrdtSpuPageReqVO();
        vo.setSupplierId(554977566507499520L);
        vo.setPageNo(1);
        vo.setPageSize(50);
        vo.setSourceNo("ZS001");
        prdtSpuServiceImpl.getSpuExportList(vo);
    }*/
}
