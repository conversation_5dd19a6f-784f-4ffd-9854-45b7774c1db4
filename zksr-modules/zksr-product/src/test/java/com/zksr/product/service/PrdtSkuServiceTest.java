package com.zksr.product.service;

import com.alibaba.fastjson.JSON;
import com.zksr.common.core.domain.vo.openapi.IncreaseUpdateStockDTO;
import com.zksr.product.ZksrMallProductApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallProductApplication.class)
@Slf4j
//@ActiveProfiles({"dev"})
public class PrdtSkuServiceTest {
    @Autowired
    private IPrdtSkuService prdtSkuService;

    @Test
    public void testIncreaseUpdateStock() {
        String s = "{\"details\":[{\"erpItemNo\":\"11000027\",\"source\":\"ANNTO\",\"updateStock\":3},{\"erpItemNo\":\"11000027\",\"source\":\"ANNTO\",\"updateStock\":40}],\"requestId\":\"3d9b8d3039264d7f80e926d2a20c8351\"}";
        IncreaseUpdateStockDTO dto = JSON.parseObject(s, IncreaseUpdateStockDTO.class);
        dto.setSupplierId(15580493955L);
        prdtSkuService.increaseUpdateStock(dto);
    }
}
