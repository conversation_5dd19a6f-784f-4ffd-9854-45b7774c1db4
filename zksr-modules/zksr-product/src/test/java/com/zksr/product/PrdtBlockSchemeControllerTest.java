package com.zksr.product;

import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.base.BaseTest;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSchemePageReqVO;
import com.zksr.product.domain.PrdtBlockScheme;
import com.zksr.product.service.IPrdtBlockSchemeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

@Slf4j
public class PrdtBlockSchemeControllerTest extends BaseTest {
    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private IPrdtBlockSchemeService prdtBlockSchemeService;
    @Test
    public void testList() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/blockScheme/list")
                                .accept(MediaType.APPLICATION_JSON) // 设置响应的文本类型
                        // .param(name, value)  // ?name=value&key1=value1
                ).andExpect(MockMvcResultMatchers.status().isOk()) // 断言状态码为200
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(403))// 期望JSON数据中的code属性为403:没有访问权限，请联系管理员授权
                .andDo(MockMvcResultHandlers.print()); // 打印
    }

    @Test
    public void testGetPrdtBlockSchemePage() {
        PageResult<PrdtBlockScheme> pageResult = prdtBlockSchemeService.getPrdtBlockSchemePage(new PrdtBlockSchemePageReqVO());
        log.info("prdtBlockSchemeService.getPrdtBlockSchemePage result: {}", JsonUtils.toJsonString(pageResult));
    }
}
