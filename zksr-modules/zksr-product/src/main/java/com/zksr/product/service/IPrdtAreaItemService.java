package com.zksr.product.service;

import javax.validation.*;

import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.areaItem.excel.PrdtAreaItemImportExcel;
import com.zksr.product.api.areaItem.excel.PrdtAreaItemSortImportExcel;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemExcelVO;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.api.spu.vo.PrdtSpuNotItemPageReqExportVo;
import com.zksr.product.controller.areaItem.vo.*;
import com.zksr.product.domain.PrdtAreaItem;
import com.zksr.report.api.homePages.dto.HomePagesSkuDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;

import java.util.List;


/**
 * 城市上架商品Service接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
public interface IPrdtAreaItemService {

    /**
     * 新增城市上架商品
     *
     * @param createReqVO 创建信息
     */
    public List<PrdtAreaItem> insertPrdtAreaItem(@Valid List<PrdtAreaItemSaveReqVO> createReqVO);

    /**
     * 修改城市上架商品
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public List<PrdtAreaItem> updatePrdtAreaItem(@Valid PrdtAreaItemSaveReqVO updateReqVO);

    /**
     * 批量上下架城市上商品
     *
     * @param areaItemIds 修改信息Id
     * @param minShelfStatus
     * @param midShelfStatus
     * @param largeShelfStatus
     * @return 结果
     */
    public List<PrdtAreaItem> updatePrdtAreaItemShelfStatus(Long[] areaItemIds, Integer minShelfStatus, Integer midShelfStatus, Integer largeShelfStatus);

    /**
     * 删除城市上架商品
     *
     * @param areaItemId 城市上架商品id
     */
    public void deletePrdtAreaItem(Long areaItemId);

    /**
     * 批量删除城市上架商品
     *
     * @param areaItemIds 需要删除的城市上架商品主键集合
     * @return 结果
     */
    public void deletePrdtAreaItemByAreaItemIds(Long[] areaItemIds);

    /**
     * 获得城市上架商品
     *
     * @param areaItemId 城市上架商品id
     * @return 城市上架商品
     */
    public PrdtAreaItemRespVO getPrdtAreaItem(Long areaItemId);

    /**
     * 获得城市上架商品分页
     *
     * @param pageReqVO 分页查询
     * @return 城市上架商品分页
     */
    PageResult<PrdtAreaItemPageRespVO> getPrdtAreaItemPage(PrdtAreaItemPageReqVO pageReqVO);

    List<PrdtSpuNotItemPageReqExportVo> getPrdtAreaItemExport(PrdtAreaItemPageReqVO pageReqVO);


    /**
     * 获取城市上下架商品
     * @param areaItemId
     * @return
     */
    public PrdtAreaItem getAreaItemId(Long areaItemId);

    public List<AreaItemDTO> getAreaItemListByApi(AreaItemDTO itemDTO);

    /**
     * 处理事务之外的缓存刷新
     * @param areaItems
     */
    void cacheEvent(List<PrdtAreaItem> areaItems);

    List<PrdtAreaItem> getSaleQtyTotalList(Long minAreaItemId);

    /**
     * 批量修改城市上架商品展示分类
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public List<PrdtAreaItem> batchEditItemAreaClassId(@Valid PrdtAreaItemSaveReqVO updateReqVO);

    /**
     * 一键复制城市上架商品信息
     *
     * @param vo 复制信息
     * @return 结果
     */
    public List<PrdtAreaItem> copyAreaItem(PrdtAreaItemCopyReqVO vo);


    /**
     * 根据组合商品ID查询城市上架商品
     *
     * @param spuCombineId 组合商品ID
     * @return 结果
     */
    PrdtAreaItem getAreaItemBySpuCombineId(Long spuCombineId);

    /**
     * 修改城市上架组合商品信息
     *
     * @param itemDTO
     */
    void updateAreaCombineItem(AreaItemDTO itemDTO);

    /**
     * 根据活动ID查询城市上架商品
     *
     * @param activityId 活动ID
     * @return 结果
     */
    PrdtAreaItem getAreaItemByActivityId(Long activityId);

    /**
     * 获取PC首页SKU数据
     * @param reqVO
     * @return
     */
    List<HomePagesSkuDataRespDTO> getHomePagesSkuData(HomePagesReqVO reqVO);


    String impordDataDc(List<PrdtAreaItemImportExcel> prdtAreaList);

    String impordDataDcEvent(List<PrdtAreaItemImportExcel> prdtAreaList,Long sysCode,Long fileImportId,Long dcId);

    List<PrdtAreaItemExcelVO> getAreaItemExportList(PrdtAreaItemPageReqVO pageReqVO);

    String importSortData(List<PrdtAreaItemSortImportExcel> prdtAreaItemSortList);

}
