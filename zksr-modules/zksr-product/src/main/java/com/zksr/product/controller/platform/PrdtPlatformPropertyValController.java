package com.zksr.product.controller.platform;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyValPageReqVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyValRespVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyValSaveReqVO;
import com.zksr.product.convert.platform.PrdtPlatformPropertyValConvert;
import com.zksr.product.domain.PrdtPlatformPropertyVal;
import com.zksr.product.service.IPrdtPlatformPropertyValService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 规格值Controller
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Api(tags = "管理后台 - 规格值接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/platform/propertyVal")
public class PrdtPlatformPropertyValController {
    @Autowired
    private IPrdtPlatformPropertyValService prdtPlatformPropertyValService;

    /**
     * 新增规格值
     */
    @ApiOperation(value = "新增规格值", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "规格值", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtPlatformPropertyValSaveReqVO createReqVO) {
        return success(prdtPlatformPropertyValService.insertPrdtPlatformPropertyVal(createReqVO));
    }

    /**
     * 修改规格值
     */
    @ApiOperation(value = "修改规格值", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "规格值", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtPlatformPropertyValSaveReqVO updateReqVO) {
            prdtPlatformPropertyValService.updatePrdtPlatformPropertyVal(updateReqVO);
        return success(true);
    }

    /**
     * 删除规格值
     */
    @ApiOperation(value = "删除规格值", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "规格值", businessType = BusinessType.DELETE)
    @DeleteMapping("/{platformPropertyValIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] platformPropertyValIds) {
        prdtPlatformPropertyValService.deletePrdtPlatformPropertyValByPlatformPropertyValIds(platformPropertyValIds);
        return success(true);
    }

    /**
     * 获取规格值详细信息
     */
    @ApiOperation(value = "获得规格值详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{platformPropertyValId}")
    public CommonResult<PrdtPlatformPropertyValRespVO> getInfo(@PathVariable("platformPropertyValId") Long platformPropertyValId) {
        PrdtPlatformPropertyVal prdtPlatformPropertyVal = prdtPlatformPropertyValService.getPrdtPlatformPropertyVal(platformPropertyValId);
        return success(PrdtPlatformPropertyValConvert.INSTANCE.convert(prdtPlatformPropertyVal));
    }

    /**
     * 分页查询规格值
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得规格值分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtPlatformPropertyValRespVO>> getPage(@Valid PrdtPlatformPropertyValPageReqVO pageReqVO) {
        PageResult<PrdtPlatformPropertyVal> pageResult = prdtPlatformPropertyValService.getPrdtPlatformPropertyValPage(pageReqVO);
        return success(PrdtPlatformPropertyValConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:platform-propertyVal:add";
        /** 编辑 */
        public static final String EDIT = "product:platform-propertyVal:edit";
        /** 删除 */
        public static final String DELETE = "product:platform-propertyVal:remove";
        /** 列表 */
        public static final String LIST = "product:platform-propertyVal:list";
        /** 查询 */
        public static final String GET = "product:platform-propertyVal:query";
        /** 停用 */
        public static final String DISABLE = "product:platform-propertyVal:disable";
        /** 启用 */
        public static final String ENABLE = "product:platform-propertyVal:enable";
    }
}
