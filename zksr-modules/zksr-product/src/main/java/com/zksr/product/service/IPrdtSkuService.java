package com.zksr.product.service;

import com.zksr.common.core.domain.vo.openapi.IncreaseUpdateStockDTO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.sku.dto.SkuPricesRespDTO;
import com.zksr.common.core.domain.vo.openapi.receive.PrdInventoryVO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateReqVO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateVO;
import com.zksr.product.api.sku.vo.SkuPricesPageReqVO;
import com.zksr.product.controller.sku.vo.PrdtSkuPageReqVO;
import com.zksr.product.controller.sku.vo.PrdtSkuRespVO;
import com.zksr.product.controller.sku.vo.PrdtSkuSaveReqVO;
import com.zksr.product.controller.sku.vo.PrdtSkuSelectedRespVO;
import com.zksr.product.domain.PrdtSku;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 商品SKUService接口
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
public interface IPrdtSkuService {

    /**
     * 新增商品SKU
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtSku(@Valid PrdtSkuSaveReqVO createReqVO);

    /**
     * 修改商品SKU
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtSku(@Valid PrdtSkuSaveReqVO updateReqVO);

    /**
     * 删除商品SKU
     *
     * @param skuId 商品sku_id
     * @param status
     */
    public void deletePrdtSku(Long skuId,Long status);

    /**
     * 批量删除商品SKU
     *
     * @param skuIds 需要删除的商品SKU主键集合
     * @param status
     * @return 结果
     */
    public void deletePrdtSkuBySkuIds(Long[] skuIds,Long status);

    /**
     * 获得商品SKU
     *
     * @param skuId 商品sku_id
     * @return 商品SKU
     */
    public PrdtSku getPrdtSku(Long skuId);

    /**
     * 获得商品SKU分页
     *
     * @param pageReqVO 分页查询
     * @return 商品SKU分页
     */
    PageResult<PrdtSku> getPrdtSkuPage(PrdtSkuPageReqVO pageReqVO);


    /**
     * 获得商品SKU
     *
     * @param skuId 商品sku_id
     * @return 商品SKU
     */
    public PrdtSku getBySkuId(Long skuId);


    /**
     * 根据skuID 获取库存数量
     * @param skuId
     * @return
     */
    public Long getSkuStockBySkuId(Long skuId);

    /**
     *   根据skuID 修改库存并更新缓存
     * @param dto SkuDTO
     * @return
     */
    Boolean editInventory(PrdInventoryVO prdInventoryVO);

    /**
     * 批量获取SPU信息 (展示用于多选数据回显)
     * @param skuIds    skuIds 集合
     * @return  集合
     */
    List<PrdtSkuSelectedRespVO> getSelectedPrdtSku(List<Long> skuIds);

    void updateSaleQty(List<Long> skuList);

    /**
     * 根据外部编码和入驻商Id查询最后库存更新时间
     * @param sourceNo
     * @param supplierId
     * @return
     */
    Date getLastUpdateTime(String sourceNo, Long supplierId);

    /**
     * 获取sku销售分润占比
     * @param reqVO
     * @return
     */
    PrdtSkuSaleTotalRateVO getSkuSaleTotalRate(PrdtSkuSaleTotalRateReqVO reqVO);

    /**
     * 获取sku价格信息列表（目前用于导出功能）
     * @param reqVO
     * @return
     */
    List<SkuPricesRespDTO> getSkuPricesList(SkuPricesPageReqVO reqVO);

    /**
     * 分页查询商品SKU
     *
     * @param pageReqVO 分页查询
     * @return 商品SKU分页
     */
    PageResult<PrdtSkuRespVO> selectSkuPage(PrdtSkuPageReqVO pageReqVO);

    Map<Long, SkuDTO> listBySkuIds(List<Long> skuIds);

    /**
     * 获取SKU关联的有效的, 组合商品ID结婚
     */
    List<Long> getRelationSpuCombineList(List<Long> skuIds);

    Boolean getAreaIdExistSpu(Long areaId, Long sysCode);

    Boolean getExistShelfSku(Long spuId);
    PrdtSku getByBarcode(String barcode, String spuName, Long supplierNo);

    /**
     * 计算无库存调整
     */
    void calculateNoStock(PrdtSku sku, BigDecimal stock);

    /**
     * 计算无库存调整, 从redis查询stock计算
     */
    void calculateNoStockOnline(PrdtSku sku);

    /**
     * 计算无库存调整, 基于PrdtSku 里面的stock计算
     */
    void calculateNoStockShelf(PrdtSku sku);

    Boolean increaseUpdateStock(IncreaseUpdateStockDTO dto);

    void updateBatchTransactional(List<PrdtSku> prdtSkus);

    void syncSkuStock(List<Long> skuIds);

}
