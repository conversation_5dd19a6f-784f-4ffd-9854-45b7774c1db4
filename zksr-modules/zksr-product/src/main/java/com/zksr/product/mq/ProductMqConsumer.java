package com.zksr.product.mq;

import com.zksr.common.core.domain.vo.openapi.receive.CreateYhDataReqVO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.product.api.yhdata.vo.CreateYhDataRespVO;
import com.zksr.product.service.IPrdtAdjustPricesDtlService;
import com.zksr.product.service.IPrdtBranchYhdataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.support.MessageBuilder;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2024-11-04
 * @description:  产品模块消息队列消费者
 */
@Slf4j
@Configuration
public class ProductMqConsumer {

    @Autowired
    private IPrdtAdjustPricesDtlService prdtAdjustPricesDtlService;

    @Autowired
    private IPrdtBranchYhdataService branchYhdataService;

    /**
     * 消息消费者
     * 来源 {@linkplain ProductMqProducer#sendAdjustPricesDtlApprove (adjustPricesDtlId)}
     */
    @Bean
    public Consumer<Long> productAdjustPricesApproveEvent() {
        return (data) -> {
            log.info("收到 商品调价单明细审核成功消息：{} ", data);
            prdtAdjustPricesDtlService.adjustPricesDtlComplete(data);
        };
    }

    /**
     * 异步匹配要货单商品
     * 来源 {@link  ProductMqProducer#sendYhDataMatch(CreateYhDataReqVO)}
     */
    @Bean
    public Consumer<CreateYhDataReqVO> yhDataMatch() {
        return (reqVO) -> {
            log.info("收到异步匹配要货单商品, 批次号：{} ", reqVO.getYhBatchNo());
            // 保存要货数据
            CommonResult<CreateYhDataRespVO> result = branchYhdataService.saveYhData(reqVO);
            if (result.isSuccess()) {
                // 异步发送处理匹配商品数据
                branchYhdataService.matchYhBatch(result.getData());
            }
        };
    }
}
