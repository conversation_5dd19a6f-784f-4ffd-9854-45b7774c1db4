package com.zksr.product.controller.event;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.service.IProductEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品服务 - ES商品刷新事件
 * @date 2024/3/1 10:21
 */
@Api(tags = "商品服务 - ES商品刷新事件", produces = HttpMethod.APPLICATION_JSON)
@Validated
@RestController
@RequestMapping("/esProductEvent")
public class EsProductEventController {

    @Autowired
    IProductEventService productEventService;

    /**
     * 接受事件
     */
    @ApiOperation(value = "ES刷新事件通知", httpMethod = HttpMethod.POST)
    @Log(title = "商品ES缓存", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Boolean> putProductEvent(@RequestBody EsProductEvent<?> event) {
        productEventService.acceptEvent(event);
        return CommonResult.success(Boolean.TRUE);
    }


    @ApiOperation(value = "初始化ES_INDEX", httpMethod = HttpMethod.GET)
    @GetMapping("/createIndex")
    public CommonResult<Boolean> createIndex() {
        productEventService.createIndex();
        return CommonResult.success(Boolean.TRUE);
    }

    @ApiOperation(value = "测试Demo1, 本地商品上架", httpMethod = HttpMethod.GET, notes = "商品上下架||销量变动||改价等")
    @GetMapping("/testDemo1/{itemIds}")
    public CommonResult<Boolean> testDemo1(@PathVariable @ApiParam(name = "itemIds", value = "本地上架商品ID集合") Long[] itemIds) {
        productEventService.testDemo1(Arrays.asList(itemIds));
        return CommonResult.success(Boolean.TRUE);
    }

    @ApiOperation(value = "测试Demo2, 全国商品上架", httpMethod = HttpMethod.GET, notes = "商品上下架||销量变动||改价等")
    @GetMapping("/testDemo2/{itemIds}")
    public CommonResult<Boolean> testDemo2(@PathVariable @ApiParam(name = "itemIds", value = "全国上架商品ID集合") Long[] itemIds) {
        productEventService.testDemo2(Arrays.asList(itemIds));
        return CommonResult.success(Boolean.TRUE);
    }

    @ApiOperation(value = "测试Demo3, SPU变动", httpMethod = HttpMethod.GET)
    @GetMapping("/testDemo3")
    public CommonResult<Boolean> testDemo3(@RequestParam @ApiParam(name = "spuId", value = "SPU_ID") Long spuId) {
        productEventService.testDemo3(spuId);
        return CommonResult.success(Boolean.TRUE);
    }

    @ApiOperation(value = "测试Demo4, SKU变动", httpMethod = HttpMethod.GET)
    @GetMapping("/testDemo4")
    public CommonResult<Boolean> testDemo4(@RequestParam @ApiParam(name = "skuId", value = "SKU_ID") Long skuId) {
        productEventService.testDemo4(skuId);
        return CommonResult.success(Boolean.TRUE);
    }
}
