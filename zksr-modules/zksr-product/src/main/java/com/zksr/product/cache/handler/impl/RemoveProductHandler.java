package com.zksr.product.cache.handler.impl;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.elasticsearch.domain.EsProduct;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.api.model.EsProductEventType;
import com.zksr.product.api.model.event.EsRemoveProductEvent;
import com.zksr.product.cache.handler.IAbstractProductEventHandler;
import com.zksr.product.convert.cache.EsCacheConvert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: SPU数据更新
 * @date 2024/2/29 19:45
 */
@Service(EsProductEventType.REMOVE)
public class RemoveProductHandler extends IAbstractProductEventHandler<EsRemoveProductEvent> {

    @Autowired
    private EsProductService esProductService;

    @Override
    public List<EsProduct> execEvent(EsProductEvent<EsRemoveProductEvent> event) {
        esProductService.deleteByEvent(EsCacheConvert.INSTANCE.convertSearchDTO(event.getData()));
        return ListUtil.empty();
    }
}
