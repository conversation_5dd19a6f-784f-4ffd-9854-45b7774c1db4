package com.zksr.product.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.controller.platform.vo.PrdtPlatformSkuPageReqVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSkuSaveReqVO;
import com.zksr.product.domain.PrdtPlatformSku;

import javax.validation.Valid;

/**
 * 平台商品库-商品SKUService接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
public interface IPrdtPlatformSkuService {

    /**
     * 新增平台商品库-商品SKU
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtPlatformSku(@Valid PrdtPlatformSkuSaveReqVO createReqVO);

    /**
     * 修改平台商品库-商品SKU
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtPlatformSku(@Valid PrdtPlatformSkuSaveReqVO updateReqVO);

    /**
     * 删除平台商品库-商品SKU
     *
     * @param platformSkuId 商品库sku id
     */
    public void deletePrdtPlatformSku(Long platformSkuId);

    /**
     * 批量删除平台商品库-商品SKU
     *
     * @param platformSkuIds 需要删除的平台商品库-商品SKU主键集合
     * @return 结果
     */
    public void deletePrdtPlatformSkuByPlatformSkuIds(Long[] platformSkuIds);

    /**
     * 获得平台商品库-商品SKU
     *
     * @param platformSkuId 商品库sku id
     * @return 平台商品库-商品SKU
     */
    public PrdtPlatformSku getPrdtPlatformSku(Long platformSkuId);

    /**
     * 获得平台商品库-商品SKU分页
     *
     * @param pageReqVO 分页查询
     * @return 平台商品库-商品SKU分页
     */
    PageResult<PrdtPlatformSku> getPrdtPlatformSkuPage(PrdtPlatformSkuPageReqVO pageReqVO);

}
