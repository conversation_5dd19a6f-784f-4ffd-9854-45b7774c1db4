package com.zksr.product.service.impl;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineDtlRespVO;
import com.zksr.product.domain.PrdtSpuCombine;
import com.zksr.product.mapper.PrdtSpuCombineMapper;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtSpuCombineDtlMapper;
import com.zksr.product.convert.combine.PrdtSpuCombineDtlConvert;
import com.zksr.product.domain.PrdtSpuCombineDtl;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineDtlPageReqVO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineDtlSaveReqVO;
import com.zksr.product.service.IPrdtSpuCombineDtlService;

import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 组合商品详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Service
public class PrdtSpuCombineDtlServiceImpl implements IPrdtSpuCombineDtlService {

    @Autowired
    private PrdtSpuCombineDtlMapper prdtSpuCombineDtlMapper;

    @Autowired
    private PrdtSpuCombineMapper prdtSpuCombineMapper;

    @Autowired
    private IProductCacheService productCacheService;


    /**
     * 新增组合商品详情
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtSpuCombineDtl(PrdtSpuCombineDtlSaveReqVO createReqVO) {
        // 插入
        PrdtSpuCombineDtl prdtSpuCombineDtl = PrdtSpuCombineDtlConvert.INSTANCE.convert(createReqVO);
        prdtSpuCombineDtlMapper.insert(prdtSpuCombineDtl);
        // 返回
        return prdtSpuCombineDtl.getSysCode();
    }

    /**
     * 修改组合商品详情
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtSpuCombineDtl(PrdtSpuCombineDtlSaveReqVO updateReqVO) {
        prdtSpuCombineDtlMapper.updateById(PrdtSpuCombineDtlConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除组合商品详情
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deletePrdtSpuCombineDtl(Long sysCode) {
        // 删除
        prdtSpuCombineDtlMapper.deleteById(sysCode);
    }

    /**
     * 批量删除组合商品详情
     *
     * @param sysCodes 需要删除的组合商品详情主键
     * @return 结果
     */
    @Override
    public void deletePrdtSpuCombineDtlBySysCodes(Long[] sysCodes) {
        for (Long sysCode : sysCodes) {
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deletePrdtSpuCombineDtl(sysCode);
        }
    }

    /**
     * 获得组合商品详情
     *
     * @param spuCombineId 组合商品ID
     * @return 组合商品详情
     */
    @Override
    public List<PrdtSpuCombineDtlRespVO> getPrdtSpuCombineDtl(Long spuCombineId) {
        List<PrdtSpuCombineDtl> prdtSpuCombineDtls = prdtSpuCombineDtlMapper.getPrdtSpuCombineDtlBySpuCombineId(spuCombineId);
        if (CollectionUtils.isEmpty(prdtSpuCombineDtls)) {
            return Collections.emptyList();
        }

        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, String> unitMap = Optional.ofNullable(dictCache)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        PrdtSpuCombine prdtSpuCombine = prdtSpuCombineMapper.selectById(spuCombineId);

        return prdtSpuCombineDtls.stream()
                .map(prdtSpuCombineDtl -> {
                    PrdtSpuCombineDtlRespVO respVO = HutoolBeanUtils.toBean(prdtSpuCombineDtl, PrdtSpuCombineDtlRespVO.class);

                    SkuDTO skuDTO = productCacheService.getSkuDTO(prdtSpuCombineDtl.getSkuId());
                    if(ToolUtil.isNotEmpty(skuDTO)) {
                        SpuDTO spuDTO = productCacheService.getSpuDTO(skuDTO.getSpuId());
                        if (ToolUtil.isNotEmpty(spuDTO.getBrandId())) {
                            BrandDTO brandDTO = productCacheService.getBrandDTO(spuDTO.getBrandId());
                            respVO.setBrandName(brandDTO.getBrandName());
                        }
                        CatgoryDTO catgoryDTO = productCacheService.getCatgoryDTO(spuDTO.getCatgoryId());
                        respVO.setUnitName(getUnitName(unitMap, spuDTO, prdtSpuCombineDtl.getSkuUnitType()));
                        respVO.setMinUnit(spuDTO.getMinUnit());
                        respVO.setMidUnit(spuDTO.getMidUnit());
                        respVO.setLargeUnit(spuDTO.getLargeUnit());
                        respVO.setSpuNo(spuDTO.getSpuNo());
                        respVO.setSpuName(spuDTO.getSpuName());
                        respVO.setBrandId(spuDTO.getBrandId());
                        respVO.setClassName(catgoryDTO.getCatgoryName());
                        respVO.setThumb(spuDTO.getThumb());
                        respVO.setBarcode(skuDTO.getBarcode());
                        respVO.setMarkPrice(skuDTO.getMarkPrice());
                        respVO.setCostPrice(skuDTO.getCostPrice());
                    }
                    SupplierDTO supplierDTO = productCacheService.getSupplierDTO(prdtSpuCombine.getSupplierId());
                    respVO.setSupplierName(supplierDTO.getSupplierName());
                    return respVO;
                })
                .collect(Collectors.toList());
    }

    private String getUnitName(Map<String, String> unitMap, SpuDTO spuDTO, Long skuUnitType) {
        switch (skuUnitType.intValue()) {
            case 1:
                return getUnit(unitMap, spuDTO.getMinUnit());
            case 2:
                return getUnit(unitMap, spuDTO.getMidUnit());
            case 3:
                return getUnit(unitMap, spuDTO.getLargeUnit());
            default:
                return null;
        }
    }

    private String getUnit(Map<String, String> unitMap, Long unit) {
        return Optional.ofNullable(unit)
                .map(String::valueOf)
                .map(unitMap::get)
                .orElse(null);
    }


    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrdtSpuCombineDtl> getPrdtSpuCombineDtlPage(PrdtSpuCombineDtlPageReqVO pageReqVO) {
        return prdtSpuCombineDtlMapper.selectPage(pageReqVO);
    }

//    private void validatePrdtSpuCombineDtlExists(Long sysCode) {
//        if (prdtSpuCombineDtlMapper.selectById(sysCode) == null) {
//            throw exception(PRDT_SPU_COMBINE_DTL_NOT_EXISTS);
//        }
//    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.product.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 组合商品详情 TODO 补充编号 ==========
    // ErrorCode PRDT_SPU_COMBINE_DTL_NOT_EXISTS = new ErrorCode(TODO 补充编号, "组合商品详情不存在");


}
