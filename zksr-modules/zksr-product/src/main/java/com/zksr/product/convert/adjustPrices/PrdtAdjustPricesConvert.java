package com.zksr.product.convert.adjustPrices;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtAdjustPrices;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesRespVO;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 商品调价单主 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-01
*/
@Mapper
public interface PrdtAdjustPricesConvert {

    PrdtAdjustPricesConvert INSTANCE = Mappers.getMapper(PrdtAdjustPricesConvert.class);

    PrdtAdjustPricesRespVO convert(PrdtAdjustPrices prdtAdjustPrices);

    PrdtAdjustPrices convert(PrdtAdjustPricesSaveReqVO prdtAdjustPricesSaveReq);

    PageResult<PrdtAdjustPricesRespVO> convertPage(PageResult<PrdtAdjustPrices> prdtAdjustPricesPage);
}