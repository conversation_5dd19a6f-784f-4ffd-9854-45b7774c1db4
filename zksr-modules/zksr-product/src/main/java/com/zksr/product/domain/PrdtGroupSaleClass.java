package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.annotation.Excel;
import lombok.*;

/**
 * 平台商城市分组-展示分类关联对象 prdt_group_sale_class
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@TableName(value = "prdt_group_sale_class")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtGroupSaleClass{
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    private Long groupId;

    /** 平台商展示分类id;平台商展示分类id */
    @Excel(name = "平台商展示分类id;平台商展示分类id")
    private Long saleClassId;

}
