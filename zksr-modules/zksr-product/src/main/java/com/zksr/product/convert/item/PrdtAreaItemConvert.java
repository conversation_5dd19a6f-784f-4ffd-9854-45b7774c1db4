package com.zksr.product.convert.item;

import com.zksr.common.core.convert.BaseExpressionMapper;
import com.zksr.product.api.areaItem.vo.ApiAreaItemPageReqVO;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemExcelVO;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.controller.areaItem.vo.PrdtAreaItemPageReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/6/9
 * @desc
 */
@Mapper
public interface PrdtAreaItemConvert extends BaseExpressionMapper {

    PrdtAreaItemConvert INSTANCE = Mappers.getMapper(PrdtAreaItemConvert.class);

    PrdtAreaItemPageReqVO convertPageReq(ApiAreaItemPageReqVO areaItemPageReqVO);

    @Mappings({
            @Mapping(target = "shelfStatus", expression = "java(getYesNoName(vo.getShelfStatus(), null))"),
            @Mapping(target = "className", expression = "java(java.lang.String.join(com.zksr.common.core.pool.StringPool.RIGHT_CHEV, vo.getFirstAreaClassName(), vo.getSecondAreaClassName(), vo.getAreaClassName()))"),
            @Mapping(target = "minShelfStatus", expression = "java(getYesNoName(vo.getMinShelfStatus(), null))"),
            @Mapping(target = "midShelfStatus", expression = "java(getYesNoName(vo.getMidShelfStatus(), null))"),
            @Mapping(target = "largeShelfStatus", expression = "java(getYesNoName(vo.getLargeShelfStatus(), null))"),
            @Mapping(target = "markPrice", expression = "java(simpleDecimalToStr(vo.getMarkPrice()))"),
            @Mapping(target = "midMarkPrice", expression = "java(simpleDecimalToStr(vo.getMidMarkPrice()))"),
            @Mapping(target = "largeMarkPrice", expression = "java(simpleDecimalToStr(vo.getLargeMarkPrice()))"),
            @Mapping(target = "suggestPrice", expression = "java(simpleDecimalToStr(vo.getSuggestPrice()))"),
            @Mapping(target = "midSuggestPrice", expression = "java(simpleDecimalToStr(vo.getMidSuggestPrice()))"),
            @Mapping(target = "largeSuggestPrice", expression = "java(simpleDecimalToStr(vo.getLargeSuggestPrice()))"),
    })
    PrdtAreaItemExcelVO convertExcelVO(PrdtAreaItemPageRespVO vo);
}
