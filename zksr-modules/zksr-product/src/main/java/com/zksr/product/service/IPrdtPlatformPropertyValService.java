package com.zksr.product.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyValPageReqVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyValSaveReqVO;
import com.zksr.product.domain.PrdtPlatformPropertyVal;

import javax.validation.Valid;

/**
 * 规格值Service接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
public interface IPrdtPlatformPropertyValService {

    /**
     * 新增规格值
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtPlatformPropertyVal(@Valid PrdtPlatformPropertyValSaveReqVO createReqVO);

    /**
     * 修改规格值
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtPlatformPropertyVal(@Valid PrdtPlatformPropertyValSaveReqVO updateReqVO);

    /**
     * 删除规格值
     *
     * @param platformPropertyValId 规格值id
     */
    public void deletePrdtPlatformPropertyVal(Long platformPropertyValId);

    /**
     * 批量删除规格值
     *
     * @param platformPropertyValIds 需要删除的规格值主键集合
     * @return 结果
     */
    public void deletePrdtPlatformPropertyValByPlatformPropertyValIds(Long[] platformPropertyValIds);

    /**
     * 获得规格值
     *
     * @param platformPropertyValId 规格值id
     * @return 规格值
     */
    public PrdtPlatformPropertyVal getPrdtPlatformPropertyVal(Long platformPropertyValId);

    /**
     * 获得规格值分页
     *
     * @param pageReqVO 分页查询
     * @return 规格值分页
     */
    PageResult<PrdtPlatformPropertyVal> getPrdtPlatformPropertyValPage(PrdtPlatformPropertyValPageReqVO pageReqVO);

}
