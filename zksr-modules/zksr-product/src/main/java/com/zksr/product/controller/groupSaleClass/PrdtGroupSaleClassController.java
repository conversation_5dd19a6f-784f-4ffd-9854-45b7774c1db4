package com.zksr.product.controller.groupSaleClass;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtGroupSaleClass;
import com.zksr.product.service.IPrdtGroupSaleClassService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.groupSaleClass.vo.PrdtGroupSaleClassPageReqVO;
import com.zksr.product.controller.groupSaleClass.vo.PrdtGroupSaleClassSaveReqVO;
import com.zksr.product.controller.groupSaleClass.vo.PrdtGroupSaleClassRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台商城市分组-展示分类关联Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Api(tags = "管理后台 - 平台商城市分组-展示分类关联接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/groupSaleClass")
public class PrdtGroupSaleClassController {
    @Autowired
    private IPrdtGroupSaleClassService prdtGroupSaleClassService;

    /**
     * 新增平台商城市分组-展示分类关联
     */
    @ApiOperation(value = "新增平台商城市分组-展示分类关联", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "平台商城市分组-展示分类关联", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtGroupSaleClassSaveReqVO createReqVO) {
        return success(prdtGroupSaleClassService.insertPrdtGroupSaleClass(createReqVO));
    }

    /**
     * 修改平台商城市分组-展示分类关联
     */
    @ApiOperation(value = "修改平台商城市分组-展示分类关联", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "平台商城市分组-展示分类关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtGroupSaleClassSaveReqVO updateReqVO) {
            prdtGroupSaleClassService.updatePrdtGroupSaleClass(updateReqVO);
        return success(true);
    }

    /**
     * 删除平台商城市分组-展示分类关联
     */
    @ApiOperation(value = "删除平台商城市分组-展示分类关联", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "平台商城市分组-展示分类关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sysCodes}")
    public CommonResult<Boolean> remove(@PathVariable Long[] sysCodes) {
        prdtGroupSaleClassService.deletePrdtGroupSaleClassBySysCodes(sysCodes);
        return success(true);
    }

    /**
     * 获取平台商城市分组-展示分类关联详细信息
     */
    @ApiOperation(value = "获得平台商城市分组-展示分类关联详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{sysCode}")
    public CommonResult<PrdtGroupSaleClassRespVO> getInfo(@PathVariable("sysCode") Long sysCode) {
        PrdtGroupSaleClass prdtGroupSaleClass = prdtGroupSaleClassService.getPrdtGroupSaleClass(sysCode);
        return success(HutoolBeanUtils.toBean(prdtGroupSaleClass, PrdtGroupSaleClassRespVO.class));
    }

    /**
     * 分页查询平台商城市分组-展示分类关联
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台商城市分组-展示分类关联分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtGroupSaleClassRespVO>> getPage(@Valid PrdtGroupSaleClassPageReqVO pageReqVO) {
        PageResult<PrdtGroupSaleClass> pageResult = prdtGroupSaleClassService.getPrdtGroupSaleClassPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, PrdtGroupSaleClassRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:groupSaleClass:add";
        /** 编辑 */
        public static final String EDIT = "product:groupSaleClass:edit";
        /** 删除 */
        public static final String DELETE = "product:groupSaleClass:remove";
        /** 列表 */
        public static final String LIST = "product:groupSaleClass:list";
        /** 查询 */
        public static final String GET = "product:groupSaleClass:query";
        /** 停用 */
        public static final String DISABLE = "product:groupSaleClass:disable";
        /** 启用 */
        public static final String ENABLE = "product:groupSaleClass:enable";
    }
}
