package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 素材对象 prdt_material
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@TableName(value = "prdt_material")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtMaterial extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 素材id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long materialId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 素材名称 */
    @Excel(name = "素材名称")
    private String name;

    /** 素材图片地址 */
    @Excel(name = "素材图片地址")
    private String img;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    private Integer status;

    /** 素材图片大小 */
    @Excel(name = "素材图片大小")
    private String imgSize;

}
