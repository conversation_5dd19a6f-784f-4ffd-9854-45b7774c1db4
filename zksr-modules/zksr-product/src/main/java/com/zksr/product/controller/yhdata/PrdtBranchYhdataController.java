package com.zksr.product.controller.yhdata;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataGroupRespVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtBranchYhdata;
import com.zksr.product.service.IPrdtBranchYhdataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataPageReqVO;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataSaveReqVO;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataRespVO;
import com.zksr.product.convert.yhdata.PrdtBranchYhdataConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 门店批量要货Controller
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@Api(tags = "管理后台 - 门店批量要货接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/yhdata")
public class PrdtBranchYhdataController {

    @Autowired
    private IPrdtBranchYhdataService prdtBranchYhdataService;

    /**
     * 获取门店批量要货详细信息
     */
    /*@ApiOperation(value = "获得门店批量要货详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{yhId}")
    public CommonResult<PrdtBranchYhdataRespVO> getInfo(@PathVariable("yhId") Long yhId) {
        PrdtBranchYhdata prdtBranchYhdata = prdtBranchYhdataService.getPrdtBranchYhdata(yhId);
        return success(PrdtBranchYhdataConvert.INSTANCE.convert(prdtBranchYhdata));
    }*/

    /**
     * 分页查询门店批量要货
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得门店批量要货分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtBranchYhdataRespVO>> getPage(@Valid PrdtBranchYhdataPageReqVO pageReqVO) {
        return success(prdtBranchYhdataService.getPrdtBranchYhdataPage(pageReqVO));
    }

    /**
     * 要货单批次统计列表
     */
    @GetMapping("/batchTotalList")
    @ApiOperation(value = "要货单批次统计列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtBranchYhdataGroupRespVO>> batchTotalList(@Valid PrdtBranchYhdataPageReqVO pageReqVO) {
        return success(prdtBranchYhdataService.batchTotalPageList(pageReqVO));
    }

    @GetMapping("/initIndex")
    @ApiOperation(value = "要货单批次统计列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ROOT)
    @RequiresPermissions(Permissions.ROOT)
    public CommonResult<Boolean> initIndex() {
        prdtBranchYhdataService.initData();
        return success(Boolean.TRUE);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:yhdata:add";
        /** 编辑 */
        public static final String EDIT = "product:yhdata:edit";
        /** 删除 */
        public static final String DELETE = "product:yhdata:remove";
        /** 列表 */
        public static final String LIST = "product:yhdata:list";
        /** 查询 */
        public static final String GET = "product:yhdata:query";
        /** 停用 */
        public static final String DISABLE = "product:yhdata:disable";
        /** 启用 */
        public static final String ENABLE = "product:yhdata:enable";
        /** 初始化使用 */
        public static final String ROOT = "product:yhdata:root";
    }
}
