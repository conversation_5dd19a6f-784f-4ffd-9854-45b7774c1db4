package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.controller.blockScheme.excel.BlockBranchExcelToListVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockBranchRespVO;
import com.zksr.product.domain.PrdtBlockBranch;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockBranchPageReqVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockBranchSaveReqVO;

import java.util.List;

/**
 * 经营屏蔽客户Service接口
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
public interface IPrdtBlockBranchService {

    /**
     * 新增经营屏蔽客户
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtBlockBranch(@Valid PrdtBlockBranchSaveReqVO createReqVO);

    /**
     * 修改经营屏蔽客户
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtBlockBranch(@Valid PrdtBlockBranchSaveReqVO updateReqVO);

    /**
     * 删除经营屏蔽客户
     *
     * @param blockBranchId 主键
     */
    public void deletePrdtBlockBranch(Long blockBranchId);

    /**
     * 批量删除经营屏蔽客户
     *
     * @param blockBranchIds 需要删除的经营屏蔽客户主键集合
     * @return 结果
     */
    public void deletePrdtBlockBranchByBlockBranchIds(Long[] blockBranchIds);

    /**
     * 获得经营屏蔽客户
     *
     * @param blockBranchId 主键
     * @return 经营屏蔽客户
     */
    public PrdtBlockBranch getPrdtBlockBranch(Long blockBranchId);

    /**
     * 获得经营屏蔽客户分页
     *
     * @param pageReqVO 分页查询
     * @return 经营屏蔽客户分页
     */
    PageResult<PrdtBlockBranch> getPrdtBlockBranchPage(PrdtBlockBranchPageReqVO pageReqVO);

    List<PrdtBlockBranchRespVO> excelToList(List<BlockBranchExcelToListVO> branchList, String schemeNo, Long areaId);
}
