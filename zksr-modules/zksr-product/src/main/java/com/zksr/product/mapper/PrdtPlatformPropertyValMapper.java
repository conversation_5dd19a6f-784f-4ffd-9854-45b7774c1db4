package com.zksr.product.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.domain.PrdtPlatformProperty;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtPlatformPropertyVal;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyValPageReqVO;

import java.util.List;

import static com.zksr.product.constant.ProductConstant.PRDT_IS_DELETE_0;
import static com.zksr.product.constant.ProductConstant.PRDT_IS_DELETE_1;


/**
 * 规格值Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Mapper
public interface PrdtPlatformPropertyValMapper extends BaseMapperX<PrdtPlatformPropertyVal> {
    default PageResult<PrdtPlatformPropertyVal> selectPage(PrdtPlatformPropertyValPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtPlatformPropertyVal>()
                    .eqIfPresent(PrdtPlatformPropertyVal::getPlatformPropertyValId, reqVO.getPlatformPropertyValId())
                    .eqIfPresent(PrdtPlatformPropertyVal::getSpuId, reqVO.getSpuId())
                    .eqIfPresent(PrdtPlatformPropertyVal::getSupplierId, reqVO.getSupplierId())
                    .eqIfPresent(PrdtPlatformPropertyVal::getPropertyId, reqVO.getPropertyId())
                    .likeIfPresent(PrdtPlatformPropertyVal::getName, reqVO.getName())
                    .eqIfPresent(PrdtPlatformPropertyVal::getIsDelete, reqVO.getIsDelete())
                    .eqIfPresent(PrdtPlatformPropertyVal::getMemo, reqVO.getMemo())
                    .eqIfPresent(PrdtPlatformPropertyVal::getStatus, reqVO.getStatus())
                .orderByDesc(PrdtPlatformPropertyVal::getPlatformPropertyValId));
    }

    default void deleteByPlatformSpuId(Long platformSpuId) {
        update(
                PrdtPlatformPropertyVal.builder().isDelete((int) PRDT_IS_DELETE_1).build(),
                new LambdaQueryWrapperX<PrdtPlatformPropertyVal>()
                        .eq(PrdtPlatformPropertyVal::getSpuId, platformSpuId)
        );
    }

    default List<PrdtPlatformPropertyVal> selectBySpuId(Long platformSpuId) {
        return selectList(new LambdaQueryWrapperX<PrdtPlatformPropertyVal>()
                .eqIfPresent(PrdtPlatformPropertyVal::getSpuId, platformSpuId)
                .eqIfPresent(PrdtPlatformPropertyVal::getIsDelete, PRDT_IS_DELETE_0)
        );
    }

    default List<PrdtPlatformPropertyVal> selectByPropertyValId(Long propertyId) {
        return selectList(new LambdaQueryWrapperX<PrdtPlatformPropertyVal>()
                .eqIfPresent(PrdtPlatformPropertyVal::getPropertyId, propertyId)
                .eqIfPresent(PrdtPlatformPropertyVal::getIsDelete, PRDT_IS_DELETE_0)
        );
    }
}
