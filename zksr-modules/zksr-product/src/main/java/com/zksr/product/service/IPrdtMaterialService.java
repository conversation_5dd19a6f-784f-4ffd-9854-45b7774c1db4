package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.domain.PrdtMaterial;
import com.zksr.product.controller.material.vo.PrdtMaterialPageReqVO;
import com.zksr.product.controller.material.vo.PrdtMaterialSaveReqVO;

/**
 * 素材Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IPrdtMaterialService {

    /**
     * 新增素材
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtMaterial(@Valid PrdtMaterialSaveReqVO createReqVO);

    /**
     * 修改素材
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtMaterial(@Valid PrdtMaterialSaveReqVO updateReqVO);

    /**
     * 删除素材
     *
     * @param materialId 素材id
     */
    public void deletePrdtMaterial(Long materialId);

    /**
     * 批量删除素材
     *
     * @param materialIds 需要删除的素材主键集合
     * @return 结果
     */
    public void deletePrdtMaterialByMaterialIds(Long[] materialIds);

    /**
     * 获得素材
     *
     * @param materialId 素材id
     * @return 素材
     */
    public PrdtMaterial getPrdtMaterial(Long materialId);

    /**
     * 获得素材分页
     *
     * @param pageReqVO 分页查询
     * @return 素材分页
     */
    PageResult<PrdtMaterial> getPrdtMaterialPage(PrdtMaterialPageReqVO pageReqVO);

    /**
     * 停用启用 修改状态
     *
     * @param materialId ID
     * @param status
     * @return 结果
     */
    public void updateStatus(Long materialId,Integer status);

}
