package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.domain.PrdtPropertyVal;
import com.zksr.product.controller.propertyVal.vo.PrdtPropertyValPageReqVO;
import com.zksr.product.controller.propertyVal.vo.PrdtPropertyValSaveReqVO;

/**
 * 规格值Service接口
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
public interface IPrdtPropertyValService {

    /**
     * 新增规格值
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtPropertyVal(@Valid PrdtPropertyValSaveReqVO createReqVO);

    /**
     * 修改规格值
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtPropertyVal(@Valid PrdtPropertyValSaveReqVO updateReqVO);

    /**
     * 删除规格值
     *
     * @param propertyValId 规格值id
     */
    public void deletePrdtPropertyVal(Long propertyValId);

    /**
     * 批量删除规格值
     *
     * @param propertyValIds 需要删除的规格值主键集合
     * @return 结果
     */
    public void deletePrdtPropertyValByPropertyValIds(Long[] propertyValIds);

    /**
     * 获得规格值
     *
     * @param propertyValId 规格值id
     * @return 规格值
     */
    public PrdtPropertyVal getPrdtPropertyVal(Long propertyValId);

    /**
     * 获得规格值分页
     *
     * @param pageReqVO 分页查询
     * @return 规格值分页
     */
    PageResult<PrdtPropertyVal> getPrdtPropertyValPage(PrdtPropertyValPageReqVO pageReqVO);

}
