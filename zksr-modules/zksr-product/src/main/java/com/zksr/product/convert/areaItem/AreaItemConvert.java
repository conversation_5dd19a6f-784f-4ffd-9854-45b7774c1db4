package com.zksr.product.convert.areaItem;

import com.zksr.common.core.domain.vo.openapi.receive.AreaItemOpenDTO;
import com.zksr.product.api.spu.excel.ProductImportExcel;
import com.zksr.product.api.spu.vo.PrdtSpuSaveReqVO;
import com.zksr.product.controller.areaItem.vo.PrdtAreaItemSaveReqVO;
import com.zksr.product.domain.PrdtSpu;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AreaItemConvert {

    AreaItemConvert INSTANCE = Mappers.getMapper(AreaItemConvert.class);

    PrdtAreaItemSaveReqVO convert2PrdtAreaItemSaveReqVO(AreaItemOpenDTO areaItemOpenDTO);
}
