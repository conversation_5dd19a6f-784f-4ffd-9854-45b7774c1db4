package com.zksr.product.convert.product;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.content.dto.ProductContentDTO;
import com.zksr.product.controller.product.vo.ProductContentRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/28 9:01
 */
@Mapper
public interface ProductContentConvert {

    ProductContentConvert INSTANCE = Mappers.getMapper(ProductContentConvert.class);

    PageResult<ProductContentDTO> convertContentDTOPage(PageResult<ProductContentRespVO> pageResult);

}
