package com.zksr.product.api.adjustPrice;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.areaChannelPrice.AreaChannelPriceApi;
import com.zksr.product.api.areaChannelPrice.dto.AreaChannelPriceDTO;
import com.zksr.product.domain.PrdtAreaChannelPrice;
import com.zksr.product.service.IPrdtAdjustPricesService;
import com.zksr.product.service.IPrdtAreaChannelPriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;

/**
*  商品调价单 feign实现
* @date 2024/11/8 10:48
* <AUTHOR>
*/
@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class AdjustPriceApiImpl implements AdjustPriceApi {

    @Autowired
    private IPrdtAdjustPricesService prdtAdjustPricesService;

    @Override
    public CommonResult<Boolean> operateValidJob(Long sysCode, Date validTime) {
        prdtAdjustPricesService.operateValidJob(sysCode,validTime);
        return CommonResult.success(true);
    }
}
