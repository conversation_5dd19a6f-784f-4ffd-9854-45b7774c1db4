package com.zksr.product.convert.saleClass;

import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRateDTO;
import com.zksr.product.domain.PrdtSupplierClassRate;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 入驻商一级管理分润销售占比
 * @date 2024/9/13 15:26
 */
@Mapper
public interface SupplierClassRateConvert {

    public static SupplierClassRateConvert INSTANCE = Mappers.getMapper(SupplierClassRateConvert.class);

    List<PrdtSupplierClassRate> convertPOList(List<PrdtSupplierClassRateDTO> supplierClassRateDTOS);

    PrdtSupplierClassRate convertPO(PrdtSupplierClassRateDTO item);

    List<PrdtSupplierClassRateDTO> convertDTOList(List<PrdtSupplierClassRate> prdtSupplierClassRates);

}
