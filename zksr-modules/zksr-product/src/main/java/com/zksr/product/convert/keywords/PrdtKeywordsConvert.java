package com.zksr.product.convert.keywords;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtKeywords;
import com.zksr.product.controller.keywords.vo.PrdtKeywordsRespVO;
import com.zksr.product.controller.keywords.vo.PrdtKeywordsSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 搜索关键词词库 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-01-15
*/
@Mapper
public interface PrdtKeywordsConvert {

    PrdtKeywordsConvert INSTANCE = Mappers.getMapper(PrdtKeywordsConvert.class);

    PrdtKeywordsRespVO convert(PrdtKeywords prdtKeywords);

    PrdtKeywords convert(PrdtKeywordsSaveReqVO prdtKeywordsSaveReq);

    PageResult<PrdtKeywordsRespVO> convertPage(PageResult<PrdtKeywords> prdtKeywordsPage);
}