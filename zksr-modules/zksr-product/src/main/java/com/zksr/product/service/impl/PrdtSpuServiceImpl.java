package com.zksr.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.domain.vo.openapi.receive.SpuOpenDTO;
import com.zksr.common.core.domain.vo.openapi.receive.SpuReceiveVO;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.exception.ErrorCode;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.*;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.collection.ArrayUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.config.CustomIdGenerator;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.redis.utils.DcUtils;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.model.event.EsProductEventBuild;
import com.zksr.product.api.property.vo.PrdtPropertySpuGroupReqVO;
import com.zksr.product.api.property.vo.PrdtPropertySpuGroupSaveInVO;
import com.zksr.product.api.propertyVal.vo.PrdtPropertyAndValVO;
import com.zksr.product.api.propertyVal.vo.PrdtPropertyValSpuGroupReqVO;
import com.zksr.product.api.propertyVal.vo.PrdtPropertyValSpuGroupSaveInVO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.sku.vo.PrdtSkuSpuGroupReqVO;
import com.zksr.product.api.sku.vo.PrdtSkuSpuGroupSaveInVO;
import com.zksr.product.api.sku.vo.PrdtUpdateSkuStockReqVO;
import com.zksr.product.api.spu.dto.*;
import com.zksr.product.api.spu.excel.ProductImportExcel;
import com.zksr.product.api.spu.vo.*;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.product.controller.areaItem.vo.PrdtAreaItemRespVO;
import com.zksr.product.controller.property.vo.PrdtPropertyRespVO;
import com.zksr.product.controller.propertyVal.vo.PrdtPropertyValRespVO;
import com.zksr.product.controller.sku.vo.PrdtSkuRespVO;
import com.zksr.product.controller.sku.vo.PrdtSkuSelectedRespVO;
import com.zksr.product.controller.spu.dto.PrdtSpuUpdateRespDTO;
import com.zksr.product.controller.spu.vo.*;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemRespVO;
import com.zksr.product.convert.sku.SkuConvert;
import com.zksr.product.convert.spu.SpuConvert;
import com.zksr.product.domain.*;
import com.zksr.product.domain.excel.ProductBrandImportUpdateExcel;
import com.zksr.product.domain.excel.ProductCatgoryImportUpdateExcel;
import com.zksr.product.domain.excel.ProductExport;
import com.zksr.product.mapper.*;
import com.zksr.product.service.*;
import com.zksr.promotion.api.activity.ActivityApi;
import com.zksr.system.api.RemoteFileService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.dictData.DictDataApi;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.domain.SysFile;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.trade.api.supplierOrder.SupplierOrderApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.xmlbeans.impl.xb.ltgfmt.Code;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static com.zksr.common.core.constant.DictTypeConstants.SYS_PRDT_UNIT;
import static com.zksr.common.core.constant.StatusConstants.STATUS_FAIL;
import static com.zksr.common.core.constant.StatusConstants.STATUS_SUCCESS;
import static com.zksr.common.core.constant.StatusConstants.PRICE_RESERVE_3;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.pool.NumberPool.INT_THREE;
import static com.zksr.product.constant.ProductConstant.*;
import static com.zksr.product.enums.ErrorCodeConstants.*;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 商品SPUService业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Service
@Slf4j
public class PrdtSpuServiceImpl implements IPrdtSpuService {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PrdtSpuMapper prdtSpuMapper;

    @Autowired
    private PrdtSkuMapper prdtSkuMapper;

    @Autowired
    private PrdtPropertyMapper prdtPropertyMapper;

    @Autowired
    private PrdtPropertyValMapper prdtPropertyValMapper;

    @Autowired
    private PrdtAreaItemMapper prdtAreaItemMapper;

    @Autowired
    private PrdtSupplierItemMapper supplierItemMapper;

    @Autowired
    private PrdtSupplierItemMapper prdtSupplierItemMapper;

    // 入驻商信息调用服务
    @Resource
    private SupplierApi remoteSupplierApi;

    @Autowired
    private Cache<Long, SpuDTO> spuDTOCache;

    @Autowired
    private Cache<Long, SkuDTO> skuDTOCache;

    @Autowired
    private IProductEventService productEventService;

    @Autowired
    private Cache<Long, AreaItemDTO> areaItemDTOCache;

    @Autowired
    private Cache<Long, SupplierItemDTO> supplierItemDTOCache;

    @Autowired
    private IProductCacheService productCacheService;

    @Autowired
    private PrdtCatgoryMapper prdtCatgoryMapper;

    @Autowired
    private IProductBrandService productBrandService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private IPrdtPlatformSpuService prdtPlatformSpuService;

    @Autowired
    private CustomIdGenerator generator;

    @Resource
    private ActivityApi activityApi;
    @Autowired
    private DictDataApi dictDataApi;

    @Autowired
    private PrdtSupplierClassMapper prdtSupplierClassMapper;

    @Resource
    private RemoteFileService remoteFileService;

    @Resource
    private AreaApi areaApi;

    @Resource
    private SupplierOrderApi supplierOrderApi;

    @Resource
    private SkuApi skuApi;

    @Autowired
    private PrdtAreaClassMapper prdtAreaClassMapper;
    @Autowired
    private PrdtSaleClassMapper prdtSaleClassMapper;
    @Autowired
    private IPrdtSupplierItemZipService prdtSupplierItemZipService;
    @Resource
    private AreaApi remoteAreaApi;
    @Autowired
    private PrdtSupplierItemServiceImpl prdtSupplierItemService;
    @Autowired
    private PrdtAreaItemServiceImpl prdtAreaItemService;
    @Autowired
    private IPrdtAreaItemZipService prdtAreaItemZipService;
    @Autowired
    private IPrdtSkuService prdtSkuService;


    /**
     * 新增商品SPU
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertPrdtSpu(@Valid PrdtSpuGroupSaveInVO createReqVO) {
        //Spu信息
        PrdtSpuSaveReqVO spuVo = createReqVO.getSpu();
        // 如果没有填写编号, 系统生成
        if (StringUtils.isEmpty(spuVo.getSpuNo())) {
            spuVo.setSpuNo(String.valueOf(generator.nextId()));
        }

        //Sku信息
        List<PrdtSkuSpuGroupSaveInVO> skuVoList = createReqVO.getSkuList();

        //规格信息
        List<PrdtPropertySpuGroupSaveInVO> propertyVoList = createReqVO.getPropertyList();

        //校验商品信息
        checkSpu(spuVo);
        
        //校验零售和批发分润
        checkRetailWholeProfit(spuVo,skuVoList);
        
        //后台新增 不能新增外部来源编号
        if(ToolUtil.isNotEmpty(spuVo.getSourceNo())){
            throw exception(PRDT_SPU_CHECK_SOURCE_NO_PC);
        }

        //校验该商品的入驻商是否对接了第三方  如果对接了第三方不允许后台新增
        if(remoteSupplierApi.checkSyncConfig(spuVo.getSupplierId()).getCheckedData()){
            throw exception(PRDT_SPU_CHECK_SUPPLIER_SYNC);
        }

        // 验证规格信息
        checkSku(skuVoList, spuVo);

        // 插入商品信息
        PrdtSpu prdtSpu = HutoolBeanUtils.toBean(spuVo, PrdtSpu.class);
        //获取商品id
        prdtSpu.setPSpuId(createReqVO.getPlatformSpuId());
        Long spuId = prdtSpuMapper.insertPrdtSpu(prdtSpu);
        logger.info("insertSpu id {}", spuId);

        //获取入驻商ID
        Long supplierId = spuVo.getSupplierId();

        //商品规格
        List<PrdtSku> prdtSkuList = new ArrayList<>();

        //组装多规格数据
        Map<String, PrdtPropertyAndValVO> skuPropertiesMap = new HashMap<>();

        //是否是规格商品
        boolean isProperty = propertyVoList != null && !propertyVoList.isEmpty();

        //多规格数据组装
        if(isProperty){
            propertyVoList.forEach(propertyVo -> {
                PrdtProperty prdtProperty = HutoolBeanUtils.toBean(propertyVo, PrdtProperty.class);
                //设置规格名称基本属性
                prdtProperty.setSpuId(spuId);
                prdtProperty.setSupplierId(supplierId);
                Long propertyId = prdtPropertyMapper.insertPrdtProperty(prdtProperty);

                //保存规格值
                List<PrdtPropertyValSpuGroupSaveInVO> valVoList = propertyVo.getValList();
                valVoList.forEach(valVo -> {
                    PrdtPropertyVal val = HutoolBeanUtils.toBean(valVo, PrdtPropertyVal.class);
                    //设置规格值基本属性
                    val.setPropertyId(propertyId);
                    val.setSpuId(spuId);
                    val.setSupplierId(supplierId);

                    //插入规格值
                    Long valId = prdtPropertyValMapper.insertPrdtPropertyVal(val);

                    //组装多规格数据
                    PrdtPropertyAndValVO propertyAndValVO = new PrdtPropertyAndValVO();
                    propertyAndValVO.setPropertyId(propertyId);
                    propertyAndValVO.setPropertyName(prdtProperty.getName());
                    propertyAndValVO.setPropertyValId(valId);
                    propertyAndValVO.setValName(val.getName());
                    skuPropertiesMap.put(propertyAndValVO.getPropertyName() + "-" + propertyAndValVO.getValName(), propertyAndValVO);
                });
            });
        }

        //保存sku表
        skuVoList.forEach(skuVo ->{
            PrdtSku sku = HutoolBeanUtils.toBean(skuVo, PrdtSku.class);
            //校验标准价 不能低于成本价
            if (Objects.isNull(sku.getCostPrice())) {
                sku.setCostPrice(BigDecimal.ZERO);
            }
            if(sku.getMarkPrice().compareTo(sku.getCostPrice()) < 0) {
                throw exception(PRDT_SKU_CHECK_MARK_PRICE);
            }
            //设置sku属性
            sku.setSpuId(spuId);
            sku.setIsDelete(PRDT_IS_DELETE_0);
            sku.setStatus(PRDT_STATUS_1);

            if(isProperty){
                List<PrdtPropertyAndValVO> propertyAndValList = skuVo.getPropertyAndValList();
                //获取多规格组装数据
                propertyAndValList.forEach(propertyAndVal ->{
                    PrdtPropertyAndValVO propertyAndValVO = skuPropertiesMap.get(propertyAndVal.getPropertyName() + "-" + propertyAndVal.getValName());
                    if (ToolUtil.isEmpty(propertyAndValVO)) throw exception(PRDT_SKU_PROPERTY_NOT_EXISTS) ;
                    propertyAndVal.setPropertyId(propertyAndValVO.getPropertyId());
                    propertyAndVal.setPropertyValId(propertyAndValVO.getPropertyValId());
                });
                sku.setProperties(JSON.toJSONString(propertyAndValList));
            }
            //加入保存集合
            prdtSkuList.add(sku);
        });
        // 验证SKU数据
        checkSkuData(prdtSkuList);
        prdtSkuMapper.insertBatch(prdtSkuList);
        // 更新共享引用
        prdtPlatformSpuService.updateReferenced(createReqVO.getPlatformSpuId());
        // 返回
        return prdtSpu.getSpuId();
    }

    /**
     * 新增商品SPUOpen
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtSpuOpen(PrdtSpuGroupSaveInVO createReqVO) {
        //Spu信息
        PrdtSpuSaveReqVO spuVo = createReqVO.getSpu();

        //Sku信息
        List<PrdtSkuSpuGroupSaveInVO> skuVoList = createReqVO.getSkuList();

        //规格信息
        List<PrdtPropertySpuGroupSaveInVO> propertyVoList = createReqVO.getPropertyList();

        //校验商品信息
        checkSpuOpen(spuVo);

        // 插入商品信息
        PrdtSpu prdtSpu = HutoolBeanUtils.toBean(spuVo, PrdtSpu.class);
        //获取商品id
        Long spuId = prdtSpuMapper.insertPrdtSpuOpen(prdtSpu);
        logger.info("insertSpu id {}", spuId);

        //获取入驻商ID
        Long supplierId = spuVo.getSupplierId();

        //商品规格
        List<PrdtSku> prdtSkuList = new ArrayList<>();

        //组装多规格数据
        Map<String, PrdtPropertyAndValVO> skuPropertiesMap = new HashMap<>();

        //是否是规格商品
        boolean isProperty = propertyVoList != null && !propertyVoList.isEmpty();

        //多规格数据组装
        if(isProperty){
        propertyVoList.forEach(propertyVo -> {
            PrdtProperty prdtProperty = HutoolBeanUtils.toBean(propertyVo, PrdtProperty.class);
            //设置规格名称基本属性
            prdtProperty.setSpuId(spuId);
            prdtProperty.setSupplierId(supplierId);
            Long propertyId = prdtPropertyMapper.insertPrdtProperty(prdtProperty);

            //保存规格值
            List<PrdtPropertyValSpuGroupSaveInVO> valVoList = propertyVo.getValList();
            valVoList.forEach(valVo -> {
                PrdtPropertyVal val = HutoolBeanUtils.toBean(valVo, PrdtPropertyVal.class);
                //设置规格值基本属性
                val.setPropertyId(propertyId);
                val.setSpuId(spuId);
                val.setSupplierId(supplierId);

                //插入规格值
                Long valId = prdtPropertyValMapper.insertPrdtPropertyVal(val);

                //组装多规格数据
                PrdtPropertyAndValVO propertyAndValVO = new PrdtPropertyAndValVO();
                propertyAndValVO.setPropertyId(propertyId);
                propertyAndValVO.setPropertyName(prdtProperty.getName());
                propertyAndValVO.setPropertyValId(valId);
                propertyAndValVO.setValName(val.getName());
                skuPropertiesMap.put(propertyAndValVO.getPropertyName() + "-" + propertyAndValVO.getValName(), propertyAndValVO);
            });
        });
        }
        //保存sku表
        skuVoList.forEach(skuVo ->{
            PrdtSku sku = HutoolBeanUtils.toBean(skuVo, PrdtSku.class);
            //校验标准价 不能低于成本价
            //if(sku.getMarkPrice().compareTo(sku.getCostPrice()) < 0) throw exception(PRDT_SKU_CHECK_MARK_PRICE);

            //设置sku属性
            sku.setSpuId(spuId);
            sku.setIsDelete(PRDT_IS_DELETE_0);
            sku.setStatus(PRDT_STATUS_1);
            List<PrdtPropertyAndValVO> propertyAndValList = skuVo.getPropertyAndValList();
            //获取多规格组装数据
            if (isProperty) {
                propertyAndValList.forEach(propertyAndVal -> {
                    PrdtPropertyAndValVO propertyAndValVO = skuPropertiesMap.get(propertyAndVal.getPropertyName() + "-" + propertyAndVal.getValName());
                    if (ToolUtil.isEmpty(propertyAndValVO)) throw exception(PRDT_SKU_PROPERTY_NOT_EXISTS);
                    propertyAndVal.setPropertyId(propertyAndValVO.getPropertyId());
                    propertyAndVal.setPropertyValId(propertyAndValVO.getPropertyValId());
                });
                sku.setProperties(JSON.toJSONString(propertyAndValList));
            }
            //加入保存集合
            prdtSkuList.add(sku);
        });


        prdtSkuMapper.insertBatch(prdtSkuList);
        // 返回
        return prdtSpu.getSpuId();

    }


    /**
     * 修改商品SPU
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public PrdtSpuUpdateRespDTO updatePrdtSpuOpen(@Valid PrdtSpuGroupSaveInVO updateReqVO) {

        //Spu信息
        PrdtSpuSaveReqVO spuVo = updateReqVO.getSpu();
        //根据sourceNo查询spuId
        PrdtSpu prdtSpu = prdtSpuMapper.selectOne(
                new LambdaQueryWrapper<PrdtSpu>()
                        .eq(PrdtSpu::getSourceNo, spuVo.getSourceNo())
                        .eq(PrdtSpu::getSource, spuVo.getSource())
                        .eq(PrdtSpu::getSupplierId, spuVo.getSupplierId())
                        .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
        );
        if (ToolUtil.isEmpty(prdtSpu)) throw exception(PRDT_SPU_NOT_EXISTS);
        spuVo.setSpuId(prdtSpu.getSpuId());

        //把updateReqVO的propertyId 和skuId查出来
        //SpuID
        Long spuId = prdtSpu.getSpuId();

        //Sku缓存
        Set<Long> skuCacheSet = new HashSet<>();

        //获取入驻商ID
        Long supplierId = spuVo.getSupplierId();

        //Sku信息
        List<PrdtSkuSpuGroupSaveInVO> skuVoList = updateReqVO.getSkuList();

        //规格信息
        List<PrdtPropertySpuGroupSaveInVO> propertyVoList = updateReqVO.getPropertyList();

        //Spu校验 商品明细长度
        if (ToolUtil.isNotEmpty(spuVo.getDetails()) && spuVo.getDetails().length() > 10240) throw exception(PRDT_SPU_DETAILS_SIZE);

        //Spu校验
        if (ToolUtil.isEmpty(spuId)) throw exception(PRDT_SPU_SPU_ID_EXISTS);

        //校验商品管理类别 是否是第三级
//        checkCatgoryLevel(spuVo.getCatgoryId());

        //校验生产日期
        checkLatestDate(spuVo.getLatestDate(),spuVo.getOldestDate());

        //更新Spu信息
        prdtSpuMapper.updateById(HutoolBeanUtils.toBean(spuVo, PrdtSpu.class));

        //是否是规格商品
        boolean isProperty = propertyVoList != null && !propertyVoList.isEmpty();

        //根据spuID查询sku集合信息
        List<PrdtSku> prdtSkuList = prdtSkuMapper.selectPrdtSkuByList(new PrdtSkuRespVO(spuId));

        //获取sku的id集  用于修改后已删除的sku信息
        List<Long> skuIds = prdtSkuList.stream().map(PrdtSku::getSkuId).collect(Collectors.toList());


        // 将skuIds设置到skuVoList中
        for (int i = 0; i < skuIds.size() && i < skuVoList.size(); i++) {
            skuVoList.get(i).setSkuId(skuIds.get(i)); // 假设 setId 是设置ID的方法
        }

        //根据spuID查询多规格名称信息
        List<PrdtProperty> prdtPropertieList = prdtPropertyMapper.selectPropertyByList(new PrdtPropertyRespVO(spuId));
        //获取多规格名称的id集   用于修改后已删除的多规格信息
        List<Long> propertieIds = prdtPropertieList.stream().map(PrdtProperty::getPropertyId).collect(Collectors.toList());


        // 将propertieIds设置到propertyVoList中
        for (int i = 0; i < propertieIds.size() && i < propertyVoList.size(); i++) {
            propertyVoList.get(i).setPropertyId(propertieIds.get(i)); // 假设 setId 是设置ID的方法
        }
        //根据spuID查询多规格值信息
        List<PrdtPropertyVal> prdtPropertyValList = prdtPropertyValMapper.selectPropertyValByList(new PrdtPropertyValRespVO(spuId));
        //获取多规格值信息的id集  用于修改后已删除的多规格值信息
        List<Long> valIds = prdtPropertyValList.stream().map(PrdtPropertyVal::getPropertyValId).collect(Collectors.toList());

        //如果是新增的多规格 则需要组装新增多规格数据
        Map<String, PrdtPropertyAndValVO> skuPropertiesMap = new HashMap<>();
        //更新多规格信息
        propertyVoList.forEach(propertyVo -> {
            //如果规格名称不存在规格id 则做新增处理
            if (ToolUtil.isEmpty(propertyVo.getPropertyId())) {
                PrdtProperty prdtProperty = HutoolBeanUtils.toBean(propertyVo, PrdtProperty.class);
                //设置规格名称基本属性
                prdtProperty.setSpuId(spuId);
                prdtProperty.setSupplierId(supplierId);
                Long propertyId = prdtPropertyMapper.insertPrdtProperty(prdtProperty);

                //保存规格值
                List<PrdtPropertyValSpuGroupSaveInVO> valVoList = propertyVo.getValList();
                valVoList.forEach(valVo -> {
                    PrdtPropertyVal val = HutoolBeanUtils.toBean(valVo, PrdtPropertyVal.class);
                    //设置规格值基本属性
                    val.setPropertyId(propertyId);
                    val.setSpuId(spuId);
                    val.setSupplierId(supplierId);

                    //插入规格值
                    Long valId = prdtPropertyValMapper.insertPrdtPropertyVal(val);

                    //组装多规格数据
                    PrdtPropertyAndValVO propertyAndValVO = new PrdtPropertyAndValVO();
                    propertyAndValVO.setPropertyId(propertyId);
                    propertyAndValVO.setPropertyName(prdtProperty.getName());
                    propertyAndValVO.setPropertyValId(valId);
                    propertyAndValVO.setValName(val.getName());
                    skuPropertiesMap.put(propertyAndValVO.getPropertyName() + "-" + propertyAndValVO.getValName(), propertyAndValVO);
                });

            } else {
                //规格名称ID存在
                Long propertyId = propertyVo.getPropertyId();
                //保存规格值
                List<PrdtPropertyValSpuGroupSaveInVO> valVoList = propertyVo.getValList();
                valVoList.forEach(valVo -> {
                    //如果规格值不存在
                    if (ToolUtil.isEmpty(valVo.getPropertyValId())){
                        PrdtPropertyVal val = HutoolBeanUtils.toBean(valVo, PrdtPropertyVal.class);
                        //设置规格值基本属性
                        val.setPropertyId(propertyId);
                        val.setSpuId(spuId);
                        val.setSupplierId(supplierId);

                        //插入规格值
                        Long valId = prdtPropertyValMapper.insertPrdtPropertyVal(val);

                        //组装新多规格数据
                        PrdtPropertyAndValVO propertyAndValVO = new PrdtPropertyAndValVO();
                        propertyAndValVO.setPropertyId(propertyId);
                        propertyAndValVO.setPropertyName(propertyVo.getName());
                        propertyAndValVO.setPropertyValId(valId);
                        propertyAndValVO.setValName(val.getName());
                        skuPropertiesMap.put(propertyAndValVO.getPropertyName() + "-" + propertyAndValVO.getValName(), propertyAndValVO);

                    }else{
                        //规格值存在 则需要从 删除ID集合中去除掉
                        valIds.remove(valVo.getPropertyValId());
                    }

                });
                //规格存在 则需要从 删除ID集合中去除掉
                propertieIds.remove(propertyId);
            }
        });

        //删除更新后不存在的多规格信息和多规格值
        if(ToolUtil.isNotEmpty(propertieIds)){
            prdtPropertyMapper.deleteByIds(propertieIds);
        }
        if(ToolUtil.isNotEmpty(valIds)){
            prdtPropertyValMapper.deleteByIds(valIds);
        }

        //商品规格
        List<PrdtSku> skuUpdateOrInsertList = new ArrayList<>();

        //更新多规格的sku信息
        skuVoList.forEach(skuVo ->{
            PrdtSku sku = HutoolBeanUtils.toBean(skuVo, PrdtSku.class);
            //不存在sku信息 则新增
            if(ToolUtil.isEmpty(skuVo.getSkuId())) {
                sku.setSpuId(spuId);
                sku.setIsDelete(PRDT_IS_DELETE_0);
                sku.setStatus(PRDT_STATUS_1);
            }else{
                //skuId存在 则需要进行更新 并且从 删除ID集合中去除掉
                skuIds.remove(skuVo.getSkuId());
                skuCacheSet.add(skuVo.getSkuId());
            }

            //组装规格信息
            if(isProperty){
                //组装规格信息
                List<PrdtPropertyAndValVO> propertyAndValList = skuVo.getPropertyAndValList();
                //获取多规格组装数据
                propertyAndValList.forEach(propertyAndVal -> {
                    //将新增的规格信息赋值ID
                    if(ToolUtil.isEmpty(propertyAndVal.getPropertyId() ) || ToolUtil.isEmpty(propertyAndVal.getPropertyValId())){
                        PrdtPropertyAndValVO propertyAndValVO = skuPropertiesMap.get(propertyAndVal.getPropertyName() + "-" + propertyAndVal.getValName());
                        if (ToolUtil.isEmpty(propertyAndValVO)) throw exception(PRDT_SKU_PROPERTY_NOT_EXISTS);
                        propertyAndVal.setPropertyId(propertyAndValVO.getPropertyId());
                        propertyAndVal.setPropertyValId(propertyAndValVO.getPropertyValId());
                    }
                });
                sku.setProperties(JSON.toJSONString(propertyAndValList));
            }

            //将sku加入新增或更新sku的集合中
            skuUpdateOrInsertList.add(sku);
        });
        //新增或更新sku集合
        prdtSkuMapper.insertOrUpdateBatch(skuUpdateOrInsertList);
        //删除更新后不存在的sku信息
        if(ToolUtil.isNotEmpty(skuIds)){
            prdtSkuMapper.deleteByIds(skuIds);
        }

//        //刷新ES
//        productEventService.acceptEvent(EsProductEventBuild.spuEvent(spuId));
//
//        //清除Spu、Sku缓存
//        spuDTOCache.remove(spuId);
//        if(ToolUtil.isNotEmpty(skuCacheSet)){
//            skuDTOCache.removeAll(skuCacheSet);
//        }

        //更新SPU后续操作 返回 在事务处理后统一处理
        logger.info("修改商品后 清除缓存的SpuID：{}", spuId);
        logger.info("修改商品后 清除缓存的SkuID：{}", skuCacheSet);
        return new PrdtSpuUpdateRespDTO(spuId,skuIds);
    }


    /**
     * 修改商品SPU
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PrdtSpuUpdateRespDTO updatePrdtSpu(@Valid PrdtSpuGroupSaveInVO updateReqVO) {
        //Sku缓存
        Set<Long> skuCacheSet = new HashSet<>();

        //Spu信息
        PrdtSpuSaveReqVO spuVo = updateReqVO.getSpu();
        //SpuID
        Long spuId = spuVo.getSpuId();

        //获取入驻商ID
        Long supplierId = spuVo.getSupplierId();

        //Sku信息
        List<PrdtSkuSpuGroupSaveInVO> skuVoList = updateReqVO.getSkuList();

        // 验证规格信息
        checkSku(skuVoList, spuVo);
        
        //校验零售和批发分润
        checkRetailWholeProfit(spuVo,skuVoList);

        // 校验sku是否允许停用
        checkSkuStatus(skuVoList);

        //规格信息
        List<PrdtPropertySpuGroupSaveInVO> propertyVoList = updateReqVO.getPropertyList();

        //Spu校验 商品明细长度
        if (ToolUtil.isNotEmpty(spuVo.getDetails()) && spuVo.getDetails().length() > 10240) throw exception(PRDT_SPU_DETAILS_SIZE);

        //Spu校验
        if (ToolUtil.isEmpty(spuId)) throw exception(PRDT_SPU_SPU_ID_EXISTS);

        //校验商品管理类别 是否是第三级
        checkCatgoryLevel(spuVo.getCatgoryId());

        //校验生产日期
        checkLatestDate(spuVo.getLatestDate(),spuVo.getOldestDate());

        //后台修改 不能修改外部来源编号
        PrdtSpu prdtSpu = prdtSpuMapper.selectById(spuId);
        if(!Objects.equals(prdtSpu.getSourceNo(),spuVo.getSourceNo())){
            throw exception(PRDT_SPU_CHECK_SOURCE_NO_PC);
        }

        //更新Spu信息
        prdtSpuMapper.updateById(HutoolBeanUtils.toBean(spuVo, PrdtSpu.class));

        //是否是规格商品
        boolean isProperty = propertyVoList != null && !propertyVoList.isEmpty();

        //根据spuID查询sku集合信息
        List<PrdtSku> prdtSkuList = prdtSkuMapper.selectPrdtSkuByList(new PrdtSkuRespVO(spuId));
        Map<Long, PrdtSku> databaseSkuMap = prdtSkuList.stream().collect(Collectors.toMap(PrdtSku::getSkuId, item -> item));

        //校验 如果是外部推送的商品信息 不允许修改库存
        checkSourceStock(spuVo.getSpuId(),prdtSkuList,skuVoList);

        //获取sku的id集  用于修改后已删除的sku信息
        List<Long> skuIds = prdtSkuList.stream().map(PrdtSku::getSkuId).collect(Collectors.toList());

        //根据spuID查询多规格名称信息
        List<PrdtProperty> prdtPropertieList = prdtPropertyMapper.selectPropertyByList(new PrdtPropertyRespVO(spuId));
        //获取多规格名称的id集   用于修改后已删除的多规格信息
        List<Long> propertieIds = prdtPropertieList.stream().map(PrdtProperty::getPropertyId).collect(Collectors.toList());

        //根据spuID查询多规格值信息
        List<PrdtPropertyVal> prdtPropertyValList = prdtPropertyValMapper.selectPropertyValByList(new PrdtPropertyValRespVO(spuId));
        //获取多规格值信息的id集  用于修改后已删除的多规格值信息
        List<Long> valIds = prdtPropertyValList.stream().map(PrdtPropertyVal::getPropertyValId).collect(Collectors.toList());

        //如果是新增的多规格 则需要组装新增多规格数据
        Map<String, PrdtPropertyAndValVO> skuPropertiesMap = new HashMap<>();
        //更新多规格信息
        propertyVoList.forEach(propertyVo -> {
            //如果规格名称不存在规格id 则做新增处理
            if (ToolUtil.isEmpty(propertyVo.getPropertyId())) {
                PrdtProperty prdtProperty = HutoolBeanUtils.toBean(propertyVo, PrdtProperty.class);
                //设置规格名称基本属性
                prdtProperty.setSpuId(spuId);
                prdtProperty.setSupplierId(supplierId);
                Long propertyId = prdtPropertyMapper.insertPrdtProperty(prdtProperty);

                //保存规格值
                List<PrdtPropertyValSpuGroupSaveInVO> valVoList = propertyVo.getValList();
                valVoList.forEach(valVo -> {
                    PrdtPropertyVal val = HutoolBeanUtils.toBean(valVo, PrdtPropertyVal.class);
                    //设置规格值基本属性
                    val.setPropertyId(propertyId);
                    val.setSpuId(spuId);
                    val.setSupplierId(supplierId);

                    //插入规格值
                    Long valId = prdtPropertyValMapper.insertPrdtPropertyVal(val);

                    //组装多规格数据
                    PrdtPropertyAndValVO propertyAndValVO = new PrdtPropertyAndValVO();
                    propertyAndValVO.setPropertyId(propertyId);
                    propertyAndValVO.setPropertyName(prdtProperty.getName());
                    propertyAndValVO.setPropertyValId(valId);
                    propertyAndValVO.setValName(val.getName());
                    skuPropertiesMap.put(propertyAndValVO.getPropertyName() + "-" + propertyAndValVO.getValName(), propertyAndValVO);
                });

            } else {
                //规格名称ID存在
                Long propertyId = propertyVo.getPropertyId();
                //保存规格值
                List<PrdtPropertyValSpuGroupSaveInVO> valVoList = propertyVo.getValList();
                valVoList.forEach(valVo -> {
                    //如果规格值不存在
                    if (ToolUtil.isEmpty(valVo.getPropertyValId())){
                        PrdtPropertyVal val = HutoolBeanUtils.toBean(valVo, PrdtPropertyVal.class);
                        //设置规格值基本属性
                        val.setPropertyId(propertyId);
                        val.setSpuId(spuId);
                        val.setSupplierId(supplierId);

                        //插入规格值
                        Long valId = prdtPropertyValMapper.insertPrdtPropertyVal(val);

                        //组装新多规格数据
                        PrdtPropertyAndValVO propertyAndValVO = new PrdtPropertyAndValVO();
                        propertyAndValVO.setPropertyId(propertyId);
                        propertyAndValVO.setPropertyName(propertyVo.getName());
                        propertyAndValVO.setPropertyValId(valId);
                        propertyAndValVO.setValName(val.getName());
                        skuPropertiesMap.put(propertyAndValVO.getPropertyName() + "-" + propertyAndValVO.getValName(), propertyAndValVO);

                    }else{
                        //规格值存在 则需要从 删除ID集合中去除掉
                        valIds.remove(valVo.getPropertyValId());
                    }

                });
                //规格存在 则需要从 删除ID集合中去除掉
                propertieIds.remove(propertyId);
            }
        });

        //删除更新后不存在的多规格信息和多规格值
        if(ToolUtil.isNotEmpty(propertieIds)){
            prdtPropertyMapper.deleteByIds(propertieIds);
        }
        if(ToolUtil.isNotEmpty(valIds)){
            prdtPropertyValMapper.deleteByIds(valIds);
        }

        //商品规格
        List<PrdtSku> skuUpdateOrInsertList = new ArrayList<>();

        //更新多规格的sku信息
        skuVoList.forEach(skuVo ->{
            PrdtSku sku = HutoolBeanUtils.toBean(skuVo, PrdtSku.class);
            //不存在sku信息 则新增
            if(ToolUtil.isEmpty(skuVo.getSkuId())) {
                sku.setSpuId(spuId);
                sku.setIsDelete(PRDT_IS_DELETE_0);
                sku.setStatus(PRDT_STATUS_1);

            }else{
                //skuId存在 则需要进行更新 并且从 删除ID集合中去除掉
                skuIds.remove(skuVo.getSkuId());
                skuCacheSet.add(skuVo.getSkuId());
                // 判断是否修改价格 , 需要验证是否参与了促销活动, 参与促销活动的商品, 不允许修改
                validatePriceUpdate(sku, databaseSkuMap.get(skuVo.getSkuId()));
            }

            //组装规格信息
            if(isProperty){
                //组装规格信息
                List<PrdtPropertyAndValVO> propertyAndValList = skuVo.getPropertyAndValList();
                //获取多规格组装数据
                propertyAndValList.forEach(propertyAndVal -> {
                    //将新增的规格信息赋值ID
                    if(ToolUtil.isEmpty(propertyAndVal.getPropertyId() ) || ToolUtil.isEmpty(propertyAndVal.getPropertyValId())){
                        PrdtPropertyAndValVO propertyAndValVO = skuPropertiesMap.get(propertyAndVal.getPropertyName() + "-" + propertyAndVal.getValName());
                        if (ToolUtil.isEmpty(propertyAndValVO)) throw exception(PRDT_SKU_PROPERTY_NOT_EXISTS);
                        propertyAndVal.setPropertyId(propertyAndValVO.getPropertyId());
                        propertyAndVal.setPropertyValId(propertyAndValVO.getPropertyValId());
                    }
                });
                sku.setProperties(JSON.toJSONString(propertyAndValList));
            }

            // 如果是停用的需要下架商品
            if (PRDT_STATUS_0 == sku.getStatus() && Objects.nonNull(sku.getSkuId())) {
                // 标记下架状态
                prdtSupplierItemMapper.updateSupplierItemBatchShelfStatus(null, sku.getSkuId(), null);
                prdtAreaItemMapper.updateAreaItemBatchShelfStatus(null, sku.getSkuId(), null);
            }
            //将sku加入新增或更新sku的集合中
            skuUpdateOrInsertList.add(sku);
        });
        //新增或更新sku集合
        prdtSkuMapper.saveOrUpdateBatch(skuUpdateOrInsertList);
        // 刷新库存信息
        for (PrdtSku prdtSku : skuUpdateOrInsertList) {
            if (Objects.nonNull(prdtSku.getStock()) && prdtSku.getStock().compareTo(BigDecimal.ZERO) >= 0) {
                // 直接重载库存数
                redisStockService.setSkuStock(prdtSku.getSkuId(), prdtSku.getStock());
            }
        }
        //删除更新后不存在的sku信息
        if(ToolUtil.isNotEmpty(skuIds)){
            skuIds.forEach(skuId -> validateUpdateSku(skuId, false, true));
            prdtSkuMapper.deleteByIds(skuIds);
        }
        return new PrdtSpuUpdateRespDTO(spuId, ListUtil.toList(skuCacheSet));
    }

    /**
     * 删除商品SPU
     *  @param spuId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrdtSpu(Long spuId) {

    }

    /**
     * 批量删除商品SPU
     *
     * @param spuIds 需要删除的商品SPU主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrdtSpuBySpuIds(Long[] spuIds) {
        //规则：如果该SPU没有产生过订单数据  按照SPU纬度做以下操作：
        //1、修改SPU的删除标识为删除 、 状态改为停用
        //2、修改所有SKU的删除标识为删除、状态改为停用
        //3、所有跟该SPU相关的本地上架商品全部改为下架状态
        //4、所有跟该SPU相关的全国上架商品全部改为下架状态

        //校验该SPU是否产生过订单
        List<Long> checkSpuIdList = supplierOrderApi.deleteSpuCheckSupplierOrderDtl(spuIds).getCheckedData();

        //如果存在已经下单的SPU  则不进行删除
        if (!checkSpuIdList.isEmpty()){
            StringBuilder errMsg = new StringBuilder();
            for (PrdtSpu spu : prdtSpuMapper.selectBatchIds(checkSpuIdList)) {
                errMsg.append(StringUtils.format("商品编号：{}，商品名称：{}；",spu.getSpuNo(),spu.getSpuName()));
            }
            throw exception(PRDT_SPU_DELETE_CHECK_ORDER_SPU,errMsg);
        }

        //停用及下架
        updateStatus(spuIds,PRDT_STATUS_0);

        //获取需要刷新缓存的sku集合
        Set<Long> spuSet = new HashSet<>();
        Collections.addAll(spuSet,spuIds);
        List<PrdtSku> skuList = prdtSkuMapper.selectBySpuIdList(spuSet);

        //修改删除标识
        prdtSpuMapper.deleteSpuList(spuIds);
        prdtSkuMapper.deleteSkuListBySpuIds(spuIds);

        //刷新缓存
        skuDTOCache.removeAll(skuList.stream().map(PrdtSku::getSkuId).collect(Collectors.toSet()));
        spuDTOCache.removeAll(spuSet);
        // 联动停用共享
        this.shareDisable(new PrdtSpuShareReqVO(Arrays.asList(spuIds)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Long[] spuIds, Long status) {

        //循环修改各个SPU状态及相关信息
        for(Long spuId : spuIds){
            // 停用共享
            this.shareDisable(new PrdtSpuShareReqVO(spuId));
            // 如果商品有上架上架就不能停用
            if (skuApi.getExistShelfSku(spuId).getCheckedData()){
                throw exception(new ErrorCode(500, "商品有上架请先解绑"));
            }

            PrdtSpu prdtSpu = prdtSpuMapper.selectById(spuId);
            prdtSpu.setStatus(status);
            // 删除  停用商品信息
            prdtSpuMapper.updateById(prdtSpu);
            //如果是停用SPU  则需要停用他对应的所有SKU及上下架
            if(status == PRDT_STATUS_0){
                //查询需要停用的SKU信息
                List<PrdtSku> prdtSkus = prdtSkuMapper.selectPrdtSkuByList(new PrdtSkuRespVO(spuId, PRDT_STATUS_1));
                if(ToolUtil.isNotEmpty(prdtSkus)) {
                    List<Long> skuIds = prdtSkus.stream().map(PrdtSku::getSkuId).collect(Collectors.toList());
                    //停用sku
                    prdtSkuMapper.updateBatchPrdtSkuStatus(spuId,skuIds);

                    //刷新缓存
                    skuDTOCache.removeAll(new HashSet<>(skuIds));

                }
                //查询需要停用的城市已上下架信息
                List<PrdtAreaItem> prdtAreaItems = prdtAreaItemMapper.selectAreaItemByList(new PrdtAreaItemRespVO(spuId, PRDT_SHELF_STATUS_1));

                if(ToolUtil.isNotEmpty(prdtAreaItems)) {
                    List<Long> areaItemIds = prdtAreaItems.stream().map(PrdtAreaItem::getAreaItemId).collect(Collectors.toList());
                    //停用城市上下架
                    prdtAreaItemMapper.updateAreaItemBatchShelfStatus(spuId, null, areaItemIds);

                    //刷新缓存
                    areaItemDTOCache.removeAll(new HashSet<>(areaItemIds));
                }
                //查询需要停用的全国上下架信息
                List<PrdtSupplierItem> prdtSupplierItems = prdtSupplierItemMapper.selectSupplierItemByList(new PrdtSupplierItemRespVO(spuId, PRDT_SHELF_STATUS_1));
                if(ToolUtil.isNotEmpty(prdtSupplierItems)) {
                    List<Long> supplierItemIds = prdtSupplierItems.stream().map(PrdtSupplierItem::getSupplierItemId).collect(Collectors.toList());
                    //停用全国上下架
                    prdtSupplierItemMapper.updateSupplierItemBatchShelfStatus(spuId, null, supplierItemIds);
                    //刷新缓存
                    supplierItemDTOCache.removeAll(new HashSet<>(supplierItemIds));
                }
            } else {
                // 修改sku状态为启用
                prdtSkuMapper.updateStatusEnable(spuId);
                prdtSkuMapper.updateMidStatusEnable(spuId);
                prdtSkuMapper.updateLargeStatusEnable(spuId);
            }
            //刷新缓存
            spuDTOCache.remove(spuId);
        }
    }

    /**
     * 获得商品SPU
     *
     * @param spuId 商品SPU_id
     * @return 商品SPU
     */
    @Override
    public PrdtSpuGroupInVO getPrdtSpu(Long spuId) {
        PrdtSpuGroupInVO spuGroupInVO = new PrdtSpuGroupInVO();
        //获取Spu信息
        PrdtSpu spu = prdtSpuMapper.selectSpuBySpu(new PrdtSpuRespVO(spuId, null));
        // 转换VO实体信息
        PrdtSpuSaveReqVO saveReqVO = SpuConvert.INSTANCE.convertSaveReqVO(spu);
        spuGroupInVO.setSpu(saveReqVO);
        //获取Sku信息
        List<PrdtSku> skus = prdtSkuMapper.selectPrdtSkuByList(PrdtSkuRespVO.builder().spuId(spuId).isDelete(PRDT_IS_DELETE_0).build());
        // 转换数据
        List<PrdtSkuSpuGroupReqVO> skuVoList = SkuConvert.INSTANCE.convertSpuGroupReqVO(skus);
        //是否是规格商品
        AtomicBoolean isProperty = new AtomicBoolean(false);
        //组装规格数据
        skuVoList.stream().forEach(skuVo -> {
            if(Objects.nonNull(skuVo.getProperties())){
                skuVo.setPropertyAndValList(JSONArray.parseArray(skuVo.getProperties(),PrdtPropertyAndValVO.class));
                isProperty.set(true);
            }
            // 从缓存里面更新最新的数据
            // 已售库存
            BigDecimal saledQty = redisStockService.getSkuSaledQty(skuVo.getSkuId());
            // 总库存
            BigDecimal stock = redisStockService.getSkuStock(skuVo.getSkuId());
            // 已占用库存
            BigDecimal occupiedQty = redisStockService.getSkuOccupiedQty(skuVo.getSkuId());
            // 设置库存
            skuVo.setSaleQty(saledQty);
            skuVo.setStock(stock);
            skuVo.setOccupiedQty(occupiedQty);
        });
        spuGroupInVO.setSkuList(skuVoList);
        //多规格数据组装
        if(isProperty.get()){
            List<PrdtPropertySpuGroupReqVO> propertyVoList = new ArrayList<PrdtPropertySpuGroupReqVO>();
            //获取规格信息
            List<PrdtProperty> properties = prdtPropertyMapper.selectPropertyByList(
                    PrdtPropertyRespVO.builder().spuId(spuId).isDelete(PRDT_IS_DELETE_0).build()
            );
            propertyVoList.addAll(HutoolBeanUtils.toBean(properties,PrdtPropertySpuGroupReqVO.class));
            propertyVoList.forEach(propertyVo -> {
                //获取规格对应的规格值
                List<PrdtPropertyVal> propertyVals = prdtPropertyValMapper.selectPropertyValByList(
                        PrdtPropertyValRespVO.builder()
                                .spuId(propertyVo.getSpuId())
                                .propertyId(propertyVo.getPropertyId())
                                .isDelete(PRDT_IS_DELETE_0)
                                .build()
                );
                propertyVo.setValList(HutoolBeanUtils.toBean(propertyVals, PrdtPropertyValSpuGroupReqVO.class));
            });
            spuGroupInVO.setPropertyList(propertyVoList);
        }
        if (Objects.nonNull(spu.getBrandId())) {
            BrandDTO brandDTO = productCacheService.getBrandDTO(spu.getBrandId());
            if (Objects.nonNull(brandDTO)) {
                saveReqVO.setBrandName(brandDTO.getBrandName());
            }
        }
        spuGroupInVO.getSpu().setIsSyncThirdParty(false);
        if(Objects.nonNull(spu.getSource()) && !SyncSourceType.B2B.getName().equals(spu.getSource())){
            spuGroupInVO.getSpu().setIsSyncThirdParty(true);
        }
        OpensourceDto opensourceDto = productCacheService.getOpensourceByMerchantId(spu.getSupplierId());
        if(ToolUtil.isNotEmpty(opensourceDto.getSyncMarkPrice())){
            spuGroupInVO.getSpu().setSyncMarkPrice(opensourceDto.getSyncMarkPrice());
        }
        if(ToolUtil.isNotEmpty(opensourceDto.getSyncCostPrice())){
            spuGroupInVO.getSpu().setSyncCostPrice(opensourceDto.getSyncCostPrice());
        }
        return spuGroupInVO;
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtSpuPageReqVO> getPrdtSpuPage(PrdtSpuPageReqVO pageReqVO) {
        Page<PrdtSpuPageReqVO> page = new Page<>(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        if (Objects.nonNull(dcId)) {
            pageReqVO.setSupplierIdList(DcUtils.getSupplierList(dcId));
        }
        Long supplierId = SecurityUtils.getSupplierId();
        if (Objects.nonNull(supplierId)) {
            pageReqVO.setSupplierId(supplierId);
        }
        //page 转换
        Page<PrdtSpuPageReqVO> spuPage = prdtSpuMapper.selectPageSpu(pageReqVO, page);
        List<PrdtSpuPageReqVO> records = spuPage.getRecords();

        //获取入驻商信息
        logger.error("商品列表获取的用户信息的供应商ID" +SecurityUtils.getLoginUser().getDcId());
        if(ToolUtil.isNotEmpty(records)) {
            //组装商品名称、设置入驻商名称
            records.forEach(record -> {
                SupplierDTO supplierDTO = productCacheService.getSupplierDTO(record.getSupplierId());
                if(ToolUtil.isNotEmpty(supplierDTO)){
                    record.setSupplierName(supplierDTO.getSupplierName());
                }

                // 属性格式转换
                record.setProperties(PropertyAndValDTO.getProperties(record.getProperties()));
            });
        }
        return new PageResult<>(spuPage.getRecords(),spuPage.getTotal());
    }

    /**
     * 获得上下架管理分页查询未上架商品SPU列表
     *
     * @param pageReqVO 分页查询
     * @return 商品SPU分页
     */
    @Override
    public PageResult<PrdtSpuNotItemPageReqVo> getPrdtSpuPageByNotItem(PrdtSpuNotItemPageReqVo pageReqVO) {
        Page<PrdtSpuNotItemPageReqVo> page = new Page<PrdtSpuNotItemPageReqVo>(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        //校验查询的上下架类型 如果为空 则根据登陆账号匹配类型
        if(ToolUtil.isEmpty(pageReqVO.getItemType())){
            Long dcId = SecurityUtils.getLoginUser().getDcId();
            pageReqVO.setItemType(ObjectUtil.isNull(dcId) ? PRDT_ITEM_TYPE_0 : PRDT_ITEM_TYPE_1);
        }
        // 设置运营商入驻商隔离
        pageReqVO.setSupplierIdList(DcUtils.getSupplierList(SecurityUtils.getDcId()));
        //page 转换
        Page<PrdtSpuNotItemPageReqVo> spuPage = prdtSpuMapper.selectPageSpuByNotItem(pageReqVO, page);
        List<PrdtSpuNotItemPageReqVo> records = spuPage.getRecords();
        if(ToolUtil.isNotEmpty(records)) {
            //组装商品名称、设置入驻商名称
            records.forEach(record -> {
                //商品名称
                record.setSpuName(PropertyAndValDTO.getPropertiesSpuName(record.getProperties(),record.getSpuName()));

                if(ToolUtil.isNotEmpty(record.getSupplierId())) {
                    //设置入驻商名称
                    SupplierDTO supplierDTO = productCacheService.getSupplierDTO(record.getSupplierId());
                    if (ToolUtil.isNotEmpty(supplierDTO)) {
                        record.setSupplierName(supplierDTO.getSupplierName());
                    }
                }

            });
        }
        return new PageResult<>(spuPage.getRecords(),spuPage.getTotal());
    }

    /**
     * 上架商品导出
     * @param pageReqVO
     */
    public List<PrdtSpuNotItemPageReqExportVo> getPrdtSpuPageByNotItemExport(PrdtSpuNotItemPageReqVo pageReqVO) {
        //校验查询的上下架类型 如果为空 则根据登陆账号匹配类型
        if(ToolUtil.isEmpty(pageReqVO.getItemType())){
            Long dcId = SecurityUtils.getLoginUser().getDcId();
            pageReqVO.setItemType(ObjectUtil.isNull(dcId) ? PRDT_ITEM_TYPE_0 : PRDT_ITEM_TYPE_1);
        }
        // 设置运营商入驻商隔离
        pageReqVO.setSupplierIdList(DcUtils.getSupplierList(SecurityUtils.getDcId()));
        List<PrdtSpuNotItemPageReqVo> list = prdtSpuMapper.selectPageSpuByNotItem(pageReqVO);
        List<PrdtSpuNotItemPageReqExportVo> exportVos = new ArrayList<>();
        for (PrdtSpuNotItemPageReqVo reqVo : list) {
            PrdtSpuNotItemPageReqExportVo exportVo = new PrdtSpuNotItemPageReqExportVo();
            exportVo.setAreaId(pageReqVO.getAreaId());
            exportVo.setClassId(reqVo.getClassId());
            exportVo.setSkuId(reqVo.getSkuId());
            exportVos.add(exportVo);
        }
        return exportVos;
    }

    @Override
    public PrdtSpu getBySpuId(Long spuId) {
        return prdtSpuMapper.selectById(spuId);
    }


    /**
     * 批量获取SPU信息 (展示用于多选数据回显)
     * @param spuIds    spuId 集合
     * @return  集合
     */
    @Override
    public List<PrdtSpuSelectedRespVO> getSelectedPrdtSpu(List<Long> spuIds) {
        return BeanUtil.copyToList(prdtSpuMapper.selectSelectedPrdtSpu(spuIds), PrdtSpuSelectedRespVO.class);
    }

    /**
    * @Description: 获取商品信息
    * @Author: liuxingyu
    * @Date: 2024/4/11 16:49
    */
    @Override
    public ProductDTO getByProduct(String param) {
        List<PrdtSpu> spuList = prdtSpuMapper.getByProduct(param);
        List<PrdtSku> skuList = prdtSkuMapper.getByProduct(param);
        //获取集合ID
        List<Long> spuIds = spuList.stream().map(PrdtSpu::getSpuId).collect(Collectors.toList());
        List<Long> skuIds = skuList.stream().map(PrdtSku::getSkuId).collect(Collectors.toList());
        return new ProductDTO(spuIds,skuIds);
    }

    /**
    * @Description: 获取产品下拉
    * @Author: liuxingyu
    * @Date: 2024/4/12 9:10
    */
    @Override
    public PageResult<PrdtProductRespVO> getProductDropdown(PrdtProductPageReqVO pageReqVO) {
        PageResult<PrdtProductRespVO> pageResult = new PageResult<>();
        List<Long> supplierIds = new ArrayList<>();
        if (ObjectUtil.isNotNull(SecurityUtils.getLoginUser().getDcId())){
            supplierIds = remoteSupplierApi.getByOrder(SecurityUtils.getLoginUser().getDcId()).getCheckedData();
            if (ObjectUtil.isEmpty(supplierIds)){
                return pageResult;
            }
        }
        if (ObjectUtil.isNotNull(SecurityUtils.getSupplierId())){
            SupplierDTO supplierDTO = remoteSupplierApi.getBySupplierId(SecurityUtils.getSupplierId()).getCheckedData();
            if (ObjectUtil.isNull(supplierDTO)){
                return pageResult;
            }
            supplierIds.add(supplierDTO.getSupplierId());
        }
        pageReqVO.setSupplierIds(supplierIds);
        Page<PrdtProductPageReqVO> page = new Page<>(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        Page<PrdtProductRespVO> spuPage = prdtSpuMapper.getProductDropdown(pageReqVO,page);
        pageResult.setList(spuPage.getRecords());
        pageResult.setTotal(spuPage.getTotal());
        return pageResult;
    }

    public String importBaseProduct(List<ProductImportExcel> productList) {
        return importBaseProductEvent(productList,SecurityUtils.getSupplierId(),SecurityUtils.getLoginUser().getSysCode(),null,0).getMsg();
    }

    @Override
    public FileImportHandlerVo importBaseProductEvent(List<ProductImportExcel> productList,Long supplierId,Long sysCode,Long fileImportId,Integer seq) {
        FileImportHandlerVo fileImportHandlerVo = new FileImportHandlerVo();
        List<SysFileImportDtl> sysFileImportDtls = new ArrayList<>();
        int successNum = productList.size();
        int failureNum = 0;
        int totalNum = productList.size();
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        fileImportHandlerVo.setSuccessNum(successNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setTotalNum(totalNum);

        if (productList.isEmpty()) {
            // 如果导入数据为空，则不进行数据导入
            fileImportHandlerVo.setFailureNum(productList.size());
            fileImportHandlerVo.setTotalNum(productList.size());
            fileImportHandlerVo.setMsg("导入数据为空");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
//            throw exception(PRDT_SPU_IMPORT_EMPTY);
        }
//        Long supplierId = SecurityUtils.getSupplierId();
        if (Objects.isNull(supplierId)) {
            fileImportHandlerVo.setFailureNum(productList.size());
            fileImportHandlerVo.setTotalNum(productList.size());
            fileImportHandlerVo.setMsg("非入驻商角色");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
//            throw new ServiceException("非入驻商角色");
        }
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, SysDictData> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictLabel, item -> item));
        Map<PrdtSpu, List<PrdtSku>> productCache = new LinkedHashMap<>();
//        int successNum = productList.size();
//        int failureNum = 0;
//        StringBuilder successMsg = new StringBuilder();
//        StringBuilder failureMsg = new StringBuilder();
        for (int line = 0; line < productList.size(); line++) {
            //导入明细
            SysFileImportDtl sysFileImportDtl = new SysFileImportDtl();
            sysFileImportDtl.setSysCode(sysCode);
            sysFileImportDtl.setCreateBy(sysFileImportDtl.getCreateBy());
            sysFileImportDtl.setCreateTime(new Date());
            sysFileImportDtl.setFileImportId(fileImportId);

            if (failureMsg.length() > 2000) {
                break;
            }
            int cellNumber = line + 3+seq;
            ProductImportExcel itemData = productList.get(line);
            sysFileImportDtl.setDtlJson(JsonUtils.toJsonString(itemData));
//            if (StringUtils.isEmpty(itemData.getSpuNo())) {
//                failureNum++;
//                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>商品</span>不能为空。", cellNumber));
//                continue;
//            }
            if (StringUtils.isEmpty(itemData.getSpuName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>商品名称</span>不能为空。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据商品名称不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (ToolUtil.isNotEmpty(itemData.getLatestDate()) && itemData.getLatestDate().after(DateUtils.getNowDate())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>最新生产日期</span>超出当前操作日。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据最新生产日期超出当前操作日。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (ToolUtil.isNotEmpty(itemData.getExpirationDate()) && itemData.getExpirationDate() <= 0) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>保质期</span>需大于等于0。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据保质期需大于等于0。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            /*if (StringUtils.isEmpty(itemData.getOriginPlace())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>产地</span>不能为空。", cellNumber));
                continue;
            }*/
            if (StringUtils.isEmpty(itemData.getCategoryId())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>管理类别</span>不能为空。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据管理类别不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
           /* if (StringUtils.isEmpty(itemData.getSkuName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>规格</span>不能为空。", cellNumber));
                continue;
            }*/
            if (StringUtils.isEmpty(itemData.getBarcode())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>小单位条码</span>不能为空。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据小单位条码不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (itemData.getBarcode().length() > 13) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>小单位条码</span>字符长度超出。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据小单位条码字符长度超出。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (Objects.isNull(itemData.getStock()) || itemData.getStock() < NumberPool.LONG_ZERO) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>小单位库存数量不能为空并且必须大于0</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据小单位库存数量不能为空并且必须大于0。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (Objects.isNull(itemData.getCostPrice())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>供货价不能为空并</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据供货价不能为空并。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (Objects.isNull(itemData.getMarkPrice()) || NumberUtil.isGreater(BigDecimal.ZERO, itemData.getMarkPrice())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>标准价不能为空并且必须大于0</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据标准价不能为空并且必须大于0。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (NumberUtil.isGreater(itemData.getCostPrice(), itemData.getMarkPrice())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>供货价不能大于标准价！</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据供货价不能大于标准价！", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (ToolUtil.isNotEmpty(itemData.getSuggestPrice()) && NumberUtil.isGreater(BigDecimal.ZERO, itemData.getSuggestPrice())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>建议零售价必须大于0！</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据建议零售价必须大于0！", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            // 验证品牌,
            ProductBrand productBrand = null;
            if (StringUtils.isNotEmpty(itemData.getBrandName())) {
                productBrand = productBrandService.getBrandByBrandName(itemData.getBrandName());
                if (Objects.isNull(productBrand)) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>品牌不存在</span>。", cellNumber));
                    sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                    sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据品牌不存在。", cellNumber));
                    sysFileImportDtls.add(sysFileImportDtl);
                    continue;
                }
            }
            /*if (Objects.isNull(productBrand)) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据品牌<span style='color:red'>{}</span>不存在。", cellNumber, itemData.getBrandName()));
                continue;
            }*/
            // 验证管理分类
            PrdtCatgory category = prdtCatgoryMapper.selectCategoryBySupplierAndCategoryIdAndLevel(supplierId, itemData.getCategoryId(), INT_THREE);
            if (Objects.isNull(category)) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据当前登录入驻商三级管理分类<span style='color:red'>{}</span>不存在。", cellNumber, itemData.getCategoryId()));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据当前登录入驻商三级管理分类{}不存在。", cellNumber, itemData.getCategoryId()));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            // 验证规格单位是否存在
            if (!unitMap.containsKey(itemData.getUnit())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据小单位<span style='color:red'>{}</span>不存在。", cellNumber, itemData.getUnit()));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据小单位{}不存在。", cellNumber, itemData.getUnit()));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            // 转换导入实体
            PrdtSpu prdtSpu = new PrdtSpu();
            {
                SpuConvert.INSTANCE.convertImport(prdtSpu, itemData);
                prdtSpu.setCatgoryId(category.getCatgoryId())
                        .setBrandId(Objects.nonNull(productBrand) ? productBrand.getBrandId() : null)
                        .setSupplierId(supplierId)
                        .setIsDelete(PRDT_IS_DELETE_0)
                        .setStatus(PRDT_STATUS_1)
                        .setIsSpecs(PRDT_IS_SPECS_0)
                        .setSpuNo(redisService.getSpuNo(prdtSpu.getCatgoryId() + "", null))
                ;
                if (unitMap.containsKey(itemData.getUnit())) {
                    prdtSpu.setMinUnit(Long.parseLong(unitMap.get(itemData.getUnit()).getDictValue()));
                }
                // 查找数据库原来有没有数据
                /*PrdtSpu sourceSpu = prdtSpuMapper.selectSpuBySpu(new PrdtSpuRespVO(prdtSpu.getSpuNo()));
                if (Objects.nonNull(sourceSpu)) {
                    prdtSpu.setSpuId(sourceSpu.getSpuId());
                }*/
            }
            PrdtSku prdtSku = new PrdtSku();
            {
                SkuConvert.INSTANCE.convertImport(prdtSku, itemData);
                prdtSku.setSpuId(prdtSpu.getSpuId())
                        .setIsDelete(PRDT_IS_DELETE_0)
                        .setStatus(PRDT_STATUS_1)
                        .setMaxOq(ToolUtil.isEmpty(prdtSku.getMaxOq()) ? prdtSku.getStock().longValue() : prdtSku.getMaxOq())
                        .setJumpOq(ToolUtil.isEmpty(prdtSku.getJumpOq()) ? NumberPool.LONG_ONE : prdtSku.getJumpOq())
                        .setMinOq(ToolUtil.isEmpty(prdtSku.getMinOq()) ? NumberPool.LONG_ONE : prdtSku.getMinOq())
                        ;
                /*if (StringUtils.isNotEmpty(itemData.getSkuName())) {
                    prdtSku.setProperties(JSON.toJSONString(ListUtil.toList(new PropertyAndValDTO(itemData.getSkuName()))));
                }*/
                // 查找数据库原来有没有数据
                /*PrdtSku sourceSku = prdtSkuMapper.selectBySpuIdAndBarcode(prdtSku.getSpuId(), itemData.getBarcode());
                if (Objects.nonNull(sourceSku)) {
                    prdtSku.setSkuId(sourceSku.getSkuId());
                }*/
                // 截取条码长度
                if (prdtSku.getBarcode().length() > 64) {
                    prdtSku.setBarcode(prdtSku.getBarcode().substring(0, 63));
                }
            }
            productCache.put(prdtSpu, ListUtil.toList(prdtSku));

            sysFileImportDtl.setStatus(STATUS_SUCCESS);
            sysFileImportDtls.add(sysFileImportDtl);
        }

        fileImportHandlerVo.setTotalNum(totalNum);
        fileImportHandlerVo.setSuccessNum(totalNum-failureNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setList(sysFileImportDtls);
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            fileImportHandlerVo.setMsg(failureMsg.toString());
            return fileImportHandlerVo;
//            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            // 保存数据
            for (PrdtSpu spu : productCache.keySet()) {
                List<PrdtSku> skuList = productCache.get(spu);
                prdtSpuMapper.insertOrUpdate(spu);
                skuList.forEach(item -> item.setSpuId(spu.getSpuId()));
                prdtSkuMapper.insertOrUpdateBatch(skuList);
            }
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
            fileImportHandlerVo.setStatus(STATUS_SUCCESS);
            fileImportHandlerVo.setMsg(successMsg.toString());
        }
        return fileImportHandlerVo;
    }

    @Override
    public String importUpdateBrandData(List<ProductBrandImportUpdateExcel> productList) {
        if (productList.isEmpty()) {
            throw new ServiceException("导入数据为空");
        }

        Long supplierId = SecurityUtils.getSupplierId();
        Long dcId = SecurityUtils.getDcId();
        if (Objects.isNull(supplierId) && Objects.isNull(dcId)) {
            throw new ServiceException("只有入驻商和运营商才能操作");
        }

        Set<PrdtSpu> productCache = new HashSet<>();
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (int line = 0; line < productList.size(); line++) {
            if (failureMsg.length() > 2000) {
                break;
            }
            int cellNumber = line + 3;
            ProductBrandImportUpdateExcel itemData = productList.get(line);

            // 检查入驻商编号
            SupplierDTO supplierDTO = null;
            if (ObjectUtil.isNotEmpty(itemData.getSupplierCode())) {
                 supplierDTO = remoteSupplierApi.getBySupplierId(itemData.getSupplierCode()).getCheckedData();
                if (Objects.isNull(supplierDTO)) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>入驻商编号不存在</span>", cellNumber));
                    continue;
                }
            } else {
                failureNum++;
                failureMsg.append(String.format("<br/>第%d行数据<span style='color:red'>入驻商编号</span>不能为空", cellNumber));
                continue;
            }

            if (StringUtils.isEmpty(itemData.getSpuNo()) /*|| !itemData.getSpuNo().matches("\\d+")*/) {
                failureNum++;
                failureMsg.append(String.format("<br/>第{}行数据<span style='color:red'>商品编号</span>不能为空", cellNumber));
//                failureMsg.append(String.format("<br/>第{}行数据<span style='color:red'>商品编号</span>不能为空且必须为数字", cellNumber));
                continue;
            }

            // 验证品牌
            ProductBrand productBrand = null;
            if (StringUtils.isNotEmpty(itemData.getBrandName())) {
                productBrand = productBrandService.getBrandByBrandName(itemData.getBrandName());
                if (Objects.isNull(productBrand)) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>品牌不存在</span>", cellNumber));
                    continue;
                }
            } else {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>商品品牌</span>不能为空并且要是数字", cellNumber));
                continue;
            }

            // 获取商品SPU 商品编号 + 入驻商ID
            PrdtSpu prdtSpu = prdtSpuMapper.selectBySpuNo(itemData.getSpuNo(), supplierDTO.getSupplierId());
            if (prdtSpu == null) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>商品编号</span>不存在", cellNumber));
                continue;
            }
            //判断一下商品是否在入驻商下面
            if (!prdtSpu.getSupplierId().equals(supplierDTO.getSupplierId())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>商品编号</span>不属于该入驻商", cellNumber));
                continue;
            }

            // 更新品牌信息
            prdtSpu.setBrandId(productBrand.getBrandId());

            productCache.add(prdtSpu);
            successNum++;
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            // 更新数据
            prdtSpuMapper.updateBatch(productCache);
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }

        return successMsg.toString();
    }

    @Override
    public String importUpdateCategoryData(List<ProductCatgoryImportUpdateExcel> productList) {
        if (productList.isEmpty()) {
            throw new ServiceException("导入数据为空");
        }

        Long supplierId = SecurityUtils.getSupplierId();
        Long dcId = SecurityUtils.getDcId();
        if (Objects.isNull(supplierId) && Objects.isNull(dcId)) {
            throw new ServiceException("只有入驻商和运营商才能操作");
        }
        // 检查是否有重复的skuId
        List<String> duplicateSkuIds = productList.stream().map(ProductCatgoryImportUpdateExcel::getSkuId).filter(StringUtils::isNotBlank).map(String::trim)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet().stream().filter(e -> e.getValue() > 1L)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(duplicateSkuIds)) {
            String duplicate = String.join(",", duplicateSkuIds);
            throw new ServiceException(StringUtils.format("<br/>skuId为<span style='color:red'>{}</span>的数据存在多行", duplicate));
        }

        Set<PrdtSpu> productCache = new HashSet<>();
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (int line = 0; line < productList.size(); line++) {
            if (failureMsg.length() > 2000) {
                break;
            }
            List<String> errorList = Lists.newArrayList();
            int cellNumber = line + 3;
            ProductCatgoryImportUpdateExcel itemData = productList.get(line);
            // 检查入驻商编号
            Long supplierCode = null;
            SupplierDTO supplierDTO = null;
            if (StringUtils.isBlank(itemData.getSupplierCode())) {
                errorList.add("<span style='color:red'>入驻商编号</span>不能为空");
            } else if (!NumberUtil.isLong(itemData.getSupplierCode().trim())) {
                errorList.add("<span style='color:red'>入驻商编号</span>有误");
            } else {
                supplierCode = Long.parseLong(itemData.getSupplierCode().trim());
                supplierDTO = remoteSupplierApi.getBySupplierId(supplierCode).getCheckedData();
                if (Objects.isNull(supplierDTO)) {
                    errorList.add("<span style='color:red'>入驻商编号不存在</span>");
                }
            }

            PrdtSpu prdtSpu = null;
            // 检查商品编号
            if (StringUtils.isBlank(itemData.getSkuId())) {
                errorList.add("<span style='color:red'>skuId</span>不能为空");
            } else if (!NumberUtil.isLong(itemData.getSkuId().trim())) {
                errorList.add("<span style='color:red'>skuId</span>有误");
            } else {
                // 获取商品SPU
                Long skuId = Long.parseLong(itemData.getSkuId().trim());
                SkuDTO skuDTO = productCacheService.getSkuDTO(skuId);
                if (skuDTO == null) {
                    errorList.add("<span style='color:red'>skuId</span>不存在");
                } else {
                    Long spuId = skuDTO.getSpuId();
                    prdtSpu = prdtSpuMapper.selectById(spuId);
                    if (prdtSpu == null) {
                        errorList.add("<span style='color:red'>skuId对应的spu</span>不存在");
                    }
                }
            }
            // 检查商品类别
            Long catgoryId = null;
            if (StringUtils.isBlank(itemData.getCatgoryId())) {
                errorList.add("<span style='color:red'>商品类别</span>不能为空");
            } else if (!NumberUtil.isLong(itemData.getCatgoryId().trim())) {
                errorList.add("<span style='color:red'>商品类别</span>有误");
            } else {
                catgoryId = Long.parseLong(itemData.getCatgoryId().trim());
                CatgoryDTO catgoryDTO = productCacheService.getCatgoryDTO(catgoryId);
                if (catgoryDTO == null) {
                    errorList.add("<span style='color:red'>商品类别</span>不存在");
                }
            }

            //判断一下商品是否在入驻商下面
            if (prdtSpu != null && supplierDTO != null && !prdtSpu.getSupplierId().equals(supplierDTO.getSupplierId())) {
                errorList.add("<span style='color:red'>skuId</span>不属于该入驻商");
            }

            // 验证管理分类
            PrdtCatgory category = null;
            if (supplierDTO != null && catgoryId != null) {
                category = prdtCatgoryMapper.selectCategoryBySupplierAneNameAndLevel(supplierCode, catgoryId, INT_THREE);
                if (Objects.isNull(category)) {
                    errorList.add(StringUtils.format("入驻商没有这个三级管理分类<span style='color:red'>{}</span>", itemData.getCatgoryId()));
                }
            }

            if (CollectionUtils.isNotEmpty(errorList)) {
                failureNum++;
                String errorMsg = String.join(", ", errorList);
                failureMsg.append(StringUtils.format("<br/>第{}行数据{}", cellNumber, errorMsg));
            } else {
                // 更新商品SPU
                prdtSpu.setCatgoryId(category.getCatgoryId());
                productCache.add(prdtSpu);

                successNum++;
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            // 更新数据
            prdtSpuMapper.updateBatch(productCache);
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }

        return successMsg.toString();
    }

    /**
     * 根据商品编号查询商品信息
     *
     * @param spuNo
     * @return
     */
    @Override
    public PrdtSpu getBySpuNo(Long spuNo) {
        return prdtSpuMapper.selectOne(PrdtSpu::getSpuNo, spuNo);
    }

    /**
     * 根据商品编号查询商品信息
     *
     * @param spuductOpenDTO
     * @return
     */
    @Override
    public Boolean updatePrdData(SpuductOpenDTO spuductOpenDTO) {
        log.info("更新生产日期,{}", JsonUtils.toJsonString(spuductOpenDTO));
        PrdtSpu prdtSpu = new PrdtSpu();
        prdtSpu.setSpuNo(spuductOpenDTO.getSpuNo());
        prdtSpu.setLatestDate(spuductOpenDTO.getLatestDate());
        prdtSpu.setOldestDate(spuductOpenDTO.getOldestDate());
        LambdaQueryWrapper<PrdtSpu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PrdtSpu::getSpuNo, spuductOpenDTO.getSpuNo())
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
                .eq(PrdtSpu::getSupplierId, spuductOpenDTO.getSupplierId());
        int update = prdtSpuMapper.update(prdtSpu, wrapper);
        if (update > 0) {
            List<PrdtSpu> spuList = prdtSpuMapper.selectListBySpuNoAndSupplierId(spuductOpenDTO.getSpuNo(), spuductOpenDTO.getSupplierId());
            List<Long> spuIdList = spuList.stream().map(PrdtSpu::getSpuId).collect(Collectors.toList());
            if (!spuIdList.isEmpty()) {
                // 发送更新ES缓存信息
                productEventService.acceptEvent(EsProductEventBuild.spuEvent(spuIdList));
                spuIdList.forEach(spuDTOCache::remove);
            }
            return true;
        }
        return false;
    }

    @Override
    public List<SkuUnitGroupDTO> getSkuUnitGroupList(Long spuId, Long areaId, Long classId, String productType) {
        List<SpuSkuReleaseListVO> sourceReleaseListVOS;
        if (!ProductType.isGlobal(productType)) {
            sourceReleaseListVOS = prdtAreaItemMapper.selectByReleaseSpuId(spuId, areaId, classId);
        } else {
            sourceReleaseListVOS = supplierItemMapper.selectByReleaseSpuId(spuId, classId);
        }
        List<SpuSkuReleaseListVO> releaseListVOS = new ArrayList<>();
        // 总是因为上架展示类目重复导致数据异常, 这里兼容去重调整
        sourceReleaseListVOS.stream().collect(Collectors.groupingBy(SpuSkuReleaseListVO::getSkuId)).forEach((skuId, list) -> {
            // 同一个展示类目只需要一个上架SKU信息就行了
            releaseListVOS.add(list.get(0));
        });

        // 渲染规格参数
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, SysDictData> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictValue, item -> item));

        List<SkuUnitGroupDTO> result = new ArrayList<>();
        for (SpuSkuReleaseListVO releaseListVO : releaseListVOS) {
            List<SkuUnitDTO> unitList = new ArrayList<>();
            SkuUnitGroupDTO unitGroupDTO = SkuUnitGroupDTO.builder()
                    .skuId(releaseListVO.getSkuId())
                    .itemId(releaseListVO.getItemId())
                    .thumb(releaseListVO.getThumb())
                    .stock(releaseListVO.getStock())
                    .properties(releaseListVO.getProperties())
                    .expirationDate(releaseListVO.getExpirationDate())
                    .unitList(unitList)
                    .build();
            if (Objects.nonNull(releaseListVO.getMinShelfStatus())
                    && releaseListVO.getMinShelfStatus() == NumberPool.INT_ONE) {
                unitList.add(
                        SkuUnitDTO.builder()
                                .markPrice(releaseListVO.getMarkPrice())
                                .suggestPrice(releaseListVO.getSuggestPrice())
                                .barcode(releaseListVO.getBarcode())
                                .unit(releaseListVO.getMinUnit())
                                .unitSize(UnitTypeEnum.UNIT_SMALL.getType())
                                .minOq(releaseListVO.getMinOq())
                                .jumpOq(releaseListVO.getJumpOq())
                                .maxOq(releaseListVO.getMaxOq())
                                .build()
                );
            }
            if (Objects.nonNull(releaseListVO.getMidShelfStatus())
                    && releaseListVO.getMidShelfStatus() == NumberPool.INT_ONE
                    && StringUtils.isNotEmpty(releaseListVO.getMidUnit())
            ) {
                unitList.add(
                        SkuUnitDTO.builder()
                                .markPrice(releaseListVO.getMidMarkPrice())
                                .suggestPrice(releaseListVO.getMidSuggestPrice())
                                .barcode(releaseListVO.getMidBarcode())
                                .unit(releaseListVO.getMidUnit())
                                .unitSize(UnitTypeEnum.UNIT_MIDDLE.getType())
                                .minOq(releaseListVO.getMidMinOq())
                                .jumpOq(releaseListVO.getMidJumpOq())
                                .maxOq(releaseListVO.getMidMaxOq())
                                .build()
                );
            }
            if (Objects.nonNull(releaseListVO.getLargeShelfStatus())
                    && releaseListVO.getLargeShelfStatus() == NumberPool.INT_ONE
                    && StringUtils.isNotEmpty(releaseListVO.getLargeUnit())
            ) {
                unitList.add(
                        SkuUnitDTO.builder()
                                .markPrice(releaseListVO.getLargeMarkPrice())
                                .suggestPrice(releaseListVO.getLargeSuggestPrice())
                                .barcode(releaseListVO.getLargeBarcode())
                                .unit(releaseListVO.getLargeUnit())
                                .unitSize(UnitTypeEnum.UNIT_LARGE.getType())
                                .minOq(releaseListVO.getLargeMinOq())
                                .jumpOq(releaseListVO.getLargeJumpOq())
                                .maxOq(releaseListVO.getLargeMaxOq())
                                .build()
                );
            }
            unitList.forEach(item -> {
                if (unitMap.containsKey(item.getUnit())) {
                    item.setUnitName(unitMap.get(item.getUnit()).getDictLabel());
                }
            });
            // 从小到大单位排序
            unitList.sort(Comparator.comparing(SkuUnitDTO::getUnitSize));
            unitGroupDTO.setUnitList(unitList);
            result.add(unitGroupDTO);
        }
        return result;
    }

    @Override
    public PageResult<PrdtSpuUniquePageRespVO> getPrdtSpuListPage(PrdtSpuPageReqVO pageReqVO) {
        com.github.pagehelper.Page<PrdtSpuUniquePageRespVO> startPage = PageUtils.startPage(pageReqVO);
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        if (Objects.nonNull(dcId)) {
            pageReqVO.setSupplierIdList(DcUtils.getSupplierList(dcId));
        }
        Long supplierId = SecurityUtils.getSupplierId();
        if (Objects.nonNull(supplierId)) {
            pageReqVO.setSupplierId(supplierId);
        }
        List<PrdtSpuUniquePageRespVO> list = prdtSpuMapper.selectPrdtSpuUniquePage(pageReqVO);
        if (!list.isEmpty()) {
            List<SkuDTO> skuDTOList = new ArrayList<>();
            // 查询上架状态
            skuDTOList = prdtSkuMapper.selectBySpuIdAreaShelfList(list.stream().map(PrdtSpuUniquePageRespVO::getSpuId).collect(Collectors.toSet()),pageReqVO.getAreaShelfStatus(),pageReqVO.getSupplierShelfStatus());
           // 如果这个商品即是城市商品，又是平台商品
//            if ( Objects.isNull(pageReqVO.getAreaShelfStatus())){
//                List<SkuDTO> skuDTOS = prdtSkuMapper.selectBySpuIdShelfList2(list.stream().map(PrdtSpuUniquePageRespVO::getSpuId).collect(Collectors.toSet()), pageReqVO.getShelfStatus());
//                skuDTOList.addAll(skuDTOS);
//            }

            skuDTOList = skuDTOList.stream().map((item) -> {
                SkuDTO skuDTO = productCacheService.getSkuDTO(item.getSkuId());
                if (Objects.nonNull(skuDTO)) {
                    BeanUtil.copyProperties(skuDTO, item,"areaMinShelfStatus","areaMidShelfStatus","areaLargeShelfStatus","supplierMinShelfStatus","supplierMidShelfStatus","supplierLargeShelfStatus");
                }
                return item;
            }).collect(Collectors.toList());

            // 有相同的skuId只保留一个上架信息 例如 skuA上架长沙 skuA下架益阳 则显示skuA上架长沙
            Map<Long, SkuDTO> tempSkuMap = new HashMap<>();
            for (SkuDTO skuDTO : skuDTOList) {
                Long skuId = skuDTO.getSkuId();
                if (tempSkuMap.containsKey(skuId)) {
                    SkuDTO existSkuDTO = tempSkuMap.get(skuId);
                    if (Objects.nonNull(existSkuDTO)){
                        existSkuDTO.setAreaMinShelfStatus(Math.max(ToolUtil.isEmpty(existSkuDTO.getAreaMinShelfStatus())?0:existSkuDTO.getAreaMinShelfStatus(), ToolUtil.isEmpty(skuDTO.getAreaMinShelfStatus())?0:skuDTO.getAreaMinShelfStatus()));
                        existSkuDTO.setAreaMidShelfStatus(Math.max(ToolUtil.isEmpty(existSkuDTO.getAreaMidShelfStatus())?0:existSkuDTO.getAreaMidShelfStatus(), ToolUtil.isEmpty(skuDTO.getAreaMidShelfStatus())?0:skuDTO.getAreaMidShelfStatus()));
                        existSkuDTO.setAreaLargeShelfStatus(Math.max(ToolUtil.isEmpty(existSkuDTO.getAreaLargeShelfStatus())?0:existSkuDTO.getAreaLargeShelfStatus(), ToolUtil.isEmpty(skuDTO.getAreaLargeShelfStatus())?0:skuDTO.getAreaLargeShelfStatus()));
                        existSkuDTO.setSupplierLargeShelfStatus(Math.max(ToolUtil.isEmpty(existSkuDTO.getSupplierLargeShelfStatus())?0:existSkuDTO.getSupplierLargeShelfStatus(), ToolUtil.isEmpty(skuDTO.getSupplierLargeShelfStatus())?0:skuDTO.getSupplierLargeShelfStatus()));
                        existSkuDTO.setSupplierMidShelfStatus(Math.max(ToolUtil.isEmpty(existSkuDTO.getSupplierMidShelfStatus())?0:existSkuDTO.getSupplierMidShelfStatus(), ToolUtil.isEmpty(skuDTO.getSupplierMidShelfStatus())?0:skuDTO.getSupplierMidShelfStatus()));
                        existSkuDTO.setSupplierLargeShelfStatus(Math.max(ToolUtil.isEmpty(existSkuDTO.getSupplierLargeShelfStatus())?0:existSkuDTO.getSupplierLargeShelfStatus(), ToolUtil.isEmpty(skuDTO.getSupplierLargeShelfStatus())?0:skuDTO.getSupplierLargeShelfStatus()));
                    }
                } else {
                    tempSkuMap.put(skuId, skuDTO);
                }
            }
            skuDTOList= new ArrayList<>(tempSkuMap.values());

            // SKU 指定条件
            Map<Long, List<SkuDTO>> skuMap = skuDTOList.stream().filter(sku -> {
                // 获取总库存
                BigDecimal stock = redisStockService.getSkuStock(sku.getSkuId());
                sku.setStock(stock);

                // 过滤库存条件
                if (Objects.nonNull(pageReqVO.getValidStockQty())) {
                    return sku.getStock().subtract(sku.getSaleQty()).compareTo(BigDecimal.valueOf(pageReqVO.getValidStockQty())) < 0;
//                            sku.getStock() - sku.getSaleQty() - sku.getSyncedQty() < pageReqVO.getValidStockQty();
                }
                // 从缓存里面更新最新的数据
                // 已售库存
                BigDecimal saledQty = redisStockService.getSkuSaledQty(sku.getSkuId());
                // 已同步库存
//                BigDecimal skuSyncedQty = redisStockService.getSkuSyncedQty(sku.getSkuId());

                // 设置缓存
                sku.setSaleQty(saledQty);
//                sku.setSyncedQty(skuSyncedQty);

                // 已占用库存
                BigDecimal occupiedQty = redisStockService.getSkuOccupiedQty(sku.getSkuId());
                sku.setOccupiedQty(occupiedQty);
                return true;
            }).collect(Collectors.groupingBy(SkuDTO::getSpuId, Collectors.toList()));

            for (PrdtSpuUniquePageRespVO prdtSpuUniquePageRespVO : list) {
                StringBuilder spuAreaName = new StringBuilder();
                // 渲染其他数据库的数据
                SupplierDTO supplierDTO = productCacheService.getSupplierDTO(prdtSpuUniquePageRespVO.getSupplierId());
                if (Objects.nonNull(supplierDTO)) {
                    prdtSpuUniquePageRespVO.setSupplierName(supplierDTO.getSupplierName());
                }
                prdtSpuUniquePageRespVO.setIsSyncThirdParty(false);
                if(Objects.nonNull(prdtSpuUniquePageRespVO.getSource()) && !SyncSourceType.B2B.getName().equals(prdtSpuUniquePageRespVO.getSource())){
                    prdtSpuUniquePageRespVO.setIsSyncThirdParty(true);
                }
                List<SkuDTO> prdtSkus = skuMap.get(prdtSpuUniquePageRespVO.getSpuId());
                if (ToolUtil.isNotEmpty(prdtSkus)){

                    // 拼接上架城市
                    for (SkuDTO skus : prdtSkus) {
                        StringBuilder areaName = new StringBuilder();
                        List<PrdtAreaItem> prdtAreaItems  = prdtAreaItemMapper.getAreaItemShelfBySkuId(skus.getSkuId(),pageReqVO.getAreaShelfStatus()); // 城市商品上架
                        prdtAreaItems.forEach(item->{
                            // 如果有一个规格上架就要展示上架城市
                            if (skus.getAreaMinShelfStatus() == 1){
                                AreaDTO areaDto = productCacheService.getAreaDto(item.getAreaId());
                                if (ToolUtil.isNotEmpty(areaDto) && !areaName.toString().contains(areaDto.getAreaName()) && item.getMinShelfStatus() == 1){
                                    AreaDTO pidArea = productCacheService.getAreaDto(areaDto.getPid());
                                    if (ToolUtil.isNotEmpty(pidArea)){
                                        areaName.append(pidArea.getAreaName()).append(">").append(areaDto.getAreaName()).append(";<br/>");
                                        if (!spuAreaName.toString().contains(areaDto.getAreaName())){
                                            spuAreaName.append(pidArea.getAreaName()).append(">").append(areaDto.getAreaName()).append(";<br/>");
                                        }
                                    }else {
                                        areaName.append(areaDto.getAreaName()).append(";<br/>");
                                    }
                                }
                                skus.setMinAreaName(areaName.toString());
                            }
                            if (skus.getAreaMidShelfStatus() == 1){
                                AreaDTO areaDto = productCacheService.getAreaDto(item.getAreaId());
                                if (ToolUtil.isNotEmpty(areaDto) && !areaName.toString().contains(areaDto.getAreaName()) && item.getMidShelfStatus() == 1){
                                    AreaDTO pidArea = productCacheService.getAreaDto(areaDto.getPid());
                                    if (ToolUtil.isNotEmpty(pidArea)){
                                        areaName.append(pidArea.getAreaName()).append(">").append(areaDto.getAreaName()).append(";<br/>");
                                        if (!spuAreaName.toString().contains(areaDto.getAreaName())){
                                            spuAreaName.append(pidArea.getAreaName()).append(">").append(areaDto.getAreaName()).append(";<br/>");
                                        }
                                    }else {
                                        areaName.append(areaDto.getAreaName()).append(";<br/>");
                                    }
                                }
                                skus.setMidAreaName(areaName.toString());
                            }
                            if (skus.getAreaLargeShelfStatus() == 1 ){
                                AreaDTO areaDto = productCacheService.getAreaDto(item.getAreaId());
                                if (ToolUtil.isNotEmpty(areaDto) && !areaName.toString().contains(areaDto.getAreaName()) && item.getLargeShelfStatus() == 1){
                                    AreaDTO pidArea = productCacheService.getAreaDto(areaDto.getPid());
                                    if (ToolUtil.isNotEmpty(pidArea)){
                                        areaName.append(pidArea.getAreaName()).append(">").append(areaDto.getAreaName()).append(";<br/>");
                                        if (!spuAreaName.toString().contains(areaDto.getAreaName())){
                                            spuAreaName.append(pidArea.getAreaName()).append(">").append(areaDto.getAreaName()).append(";<br/>");
                                        }
                                    }else {
                                        areaName.append(areaDto.getAreaName()).append(";<br/>");
                                    }
                                }
                                skus.setLargeAreaName(areaName.toString());
                            }
                        });
//                        List<PrdtSupplierItem> prdtSupplierItems = prdtSupplierItemMapper.getSupplierItemShelfBySkuId(skus.getSkuId(),pageReqVO.getSupplierShelfStatus());
//                        prdtSupplierItems.forEach((item -> {
//                            skus.setAreaName("平台商品");
//                        }));
                        // 设置启用状态
                        PrdtSku prdtSku = prdtSkuMapper.getBySkuId(skus.getSkuId());
                        if (Objects.nonNull(prdtSku)){
                            skus.setStatus(prdtSku.getStatus());
                            skus.setMidStatus(prdtSku.getMidStatus());
                            skus.setLargeStatus(prdtSku.getLargeStatus());
                        }
                    }
                }
                prdtSpuUniquePageRespVO.setSpuAreaName(spuAreaName.toString());
                prdtSpuUniquePageRespVO.setSkuList(SkuConvert.INSTANCE.convertSkuDTOGroupReqVO(skuMap.get(prdtSpuUniquePageRespVO.getSpuId())));
                if(ToolUtil.isNotEmpty(pageReqVO.getKeywords())){
                    prdtSpuUniquePageRespVO.setIsRelation(!ToolUtil.isEmpty(prdtSpuUniquePageRespVO.getKeywords()) && prdtSpuUniquePageRespVO.getKeywords().contains(pageReqVO.getKeywords()));
                }
                if (Objects.nonNull(prdtSpuUniquePageRespVO.getSkuList())) {
                    prdtSpuUniquePageRespVO.getSkuList().forEach(item -> {
                        // 属性格式转换
                        item.setProperties(PropertyAndValDTO.getProperties(item.getProperties()));
                    });
                }
            }
        }
        return PageResult.result(startPage, list);
    }

    @Override
    public void shareEnable(PrdtSpuShareReqVO updateReqVO) {
        prdtPlatformSpuService.shareEnable(updateReqVO);
    }

    @Override
    public void shareDisable(PrdtSpuShareReqVO updateReqVO) {
        prdtPlatformSpuService.shareDisable(updateReqVO);
    }

    @Override
    @Transactional
    public PrdtSpuUpdateRespDTO updateSpuBase(PrdtSpuSaveReqVO prdtSpuSaveReqVO) {
        // 修改
        PrdtSpu update = PrdtSpu.builder()
                .spuId(prdtSpuSaveReqVO.getSpuId())
                .spuName(prdtSpuSaveReqVO.getSpuName())
                .thumb(prdtSpuSaveReqVO.getThumb())
                .keywords(prdtSpuSaveReqVO.getKeywords())
                .build();
        prdtSpuMapper.updateById(update);
        // 同步共享产品库
        prdtPlatformSpuService.updatePlatformSpu(prdtSpuSaveReqVO.getSpuId());
        return PrdtSpuUpdateRespDTO.builder().spuId(prdtSpuSaveReqVO.getSpuId()).build();
    }

    @Override
    @Transactional
    @DistributedLock(prefix = RedisLockConstants.LOCK_SPU_COPY, condition = "#prdtSpuSaveReqVO.spuId")
    public Long copySpu(PrdtSpuSaveReqVO prdtSpuSaveReqVO) {
        PrdtSpu prdtSpu = prdtSpuMapper.selectById(prdtSpuSaveReqVO.getSpuId());
        if (Objects.isNull(prdtSpu)) {
            throw new ServiceException("spuId 关联的商品不存在");
        }
        List<PrdtSku> skuList = prdtSkuMapper.selectBySpuId(prdtSpu.getSpuId());

        // 重新设置新的商品编号
        prdtSpu.setSpuId(null);
        prdtSpu.setSpuNo(String.valueOf(generator.nextId()));
        prdtSpuMapper.insert(prdtSpu);

        List<PrdtProperty> properties = prdtPropertyMapper.selectBySpuId(prdtSpuSaveReqVO.getSpuId());
        List<PrdtPropertyVal> propertyVals = prdtPropertyValMapper.selectBySpuId(prdtSpuSaveReqVO.getSpuId());

        // 新增新的键值对
        Map<Long, Long> propertieIdMap = new HashMap<>();
        Map<Long, Long> propertyValIdMap = new HashMap<>();
        for (PrdtProperty property : properties) {
            Long propertyId = property.getPropertyId();
            property.setPropertyId(null);
            property.setSpuId(prdtSpu.getSpuId());
            prdtPropertyMapper.insert(property);
            propertieIdMap.put(propertyId, property.getPropertyId());
        }
        for (PrdtPropertyVal propertyVal : propertyVals) {
            Long propertyValId = propertyVal.getPropertyValId();
            propertyVal.setPropertyValId(null);
            propertyVal.setPropertyId(propertieIdMap.get(propertyVal.getPropertyId()));
            propertyVal.setSpuId(prdtSpu.getSpuId());
            prdtPropertyValMapper.insert(propertyVal);
            propertyValIdMap.put(propertyValId, propertyVal.getPropertyValId());
        }

        // 设置SKU
        skuList.forEach(item -> {
            // 设置新的关联spuId
            item.setSkuId(null);
            item.setSpuId(prdtSpu.getSpuId());
            // 设置新的键值对
            if (StringUtils.isNotEmpty(item.getProperties())) {
                String builder = item.getProperties();
                for (Long oldId : propertieIdMap.keySet()) {
                    builder = builder.replace(String.valueOf(oldId), String.valueOf(propertieIdMap.get(oldId)));
                }
                for (Long oldId : propertyValIdMap.keySet()) {
                    builder = builder.replace(String.valueOf(oldId), String.valueOf(propertyValIdMap.get(oldId)));
                }
                item.setProperties(builder);
            }
            prdtSkuMapper.insert(item);
        });
        return prdtSpu.getSpuId();
    }
    /**
     * 处理单位转换逻辑
     */
    private SysDictData processUnit(String unitName, String dictType) {
        SysDictData sysDictData = new SysDictData();
        if (ToolUtil.isNotEmpty(unitName)) {
            sysDictData = dictDataApi.getUnitByName(dictType, unitName);
            sysDictData = getSysDictData(unitName, dictType, sysDictData);
        }
        return sysDictData;
    }
    /**
     * 停用单位并下架商城中的该单位
     */
    private void disableUnit(Long spuId,String unitType) {
        //启用中大单位
        List<PrdtSku> skuList = prdtSkuMapper.selectBySpuId(spuId);
        if (ToolUtil.isNotEmpty(skuList)) {
            List<Long> skuIds = skuList.stream().map(PrdtSku::getSkuId).collect(Collectors.toList());
            // 根据不同单位类型停用
            disableSku(spuId, skuIds, unitType);
            skuDTOCache.removeAll(new HashSet<>(skuIds));
        }
        //查询需要停用的城市已上下架信息
        List<PrdtAreaItem> prdtAreaItems = prdtAreaItemMapper.selectAreaItemByList(new PrdtAreaItemRespVO(spuId, PRDT_SHELF_STATUS_1));
        if (ToolUtil.isNotEmpty(prdtAreaItems)) {
            List<Long> areaItemIds = prdtAreaItems.stream().map(PrdtAreaItem::getAreaItemId).collect(Collectors.toList());
            disableAreaItems(spuId, areaItemIds, unitType);
            //刷新缓存
            areaItemDTOCache.removeAll(new HashSet<>(areaItemIds));
        }
        //查询需要停用的全国上下架信息
        List<PrdtSupplierItem> prdtSupplierItems = prdtSupplierItemMapper.selectSupplierItemByList(new PrdtSupplierItemRespVO(spuId, PRDT_SHELF_STATUS_1));
        if (ToolUtil.isNotEmpty(prdtSupplierItems)) {
            List<Long> supplierItemIds = prdtSupplierItems.stream().map(PrdtSupplierItem::getSupplierItemId).collect(Collectors.toList());
            disableSupplierItems(spuId, supplierItemIds, unitType);
            //刷新缓存
            supplierItemDTOCache.removeAll(new HashSet<>(supplierItemIds));
        }
        spuDTOCache.remove(spuId);
    }

    // 停用SKU单位
    private void disableSku(Long spuId, List<Long> skuIds, String unitType) {
        switch (unitType) {
            case "2":
                prdtSkuMapper.updateBatchPrdtSkuMidStatus(spuId, skuIds); // 停用中单位
                break;
            case "3":
                prdtSkuMapper.updateBatchPrdtSkuLargeStatus(spuId, skuIds); // 停用大单位
                break;
            default:
                throw new ServiceException("Unknown unit type: " + unitType);
        }
    }

    // 停用城市下架
    private void disableAreaItems(Long spuId, List<Long> areaItemIds, String unitType) {
        switch (unitType) {
            case "2":
                prdtAreaItemMapper.updateAreaItemBatchMidShelfStatus(spuId, null, areaItemIds); // 停用中单位城市下架
                break;
            case "3":
                prdtAreaItemMapper.updateAreaItemBatchLargeShelfStatus(spuId, null, areaItemIds); // 停用大单位城市下架
                break;
            default:
                throw new ServiceException("Unknown unit type: " + unitType);
        }
    }

    // 停用全国下架
    private void disableSupplierItems(Long spuId, List<Long> supplierItemIds, String unitType) {
        switch (unitType) {
            case "2":
                prdtSupplierItemMapper.updateSupplierItemBatchMidShelfStatus(spuId, null, supplierItemIds); // 停用中单位全国下架
                break;
            case "3":
                prdtSupplierItemMapper.updateSupplierItemBatchLargeShelfStatus(spuId, null, supplierItemIds); // 停用大单位全国下架
                break;
            default:
                throw new ServiceException("Unknown unit type: " + unitType);
        }
    }

    /**
     * 新增或更新商品规格
     * !@商品 - 上游同步 -  2、新增或更新商品规格 （匹配小种大单位、匹配品牌/类别 、校验价格）
     * @param spuReceiveVO@return
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PrdtSpuUpdateRespDTO> addOrUpdateSpu(SpuReceiveVO spuReceiveVO) {
        List<PrdtSpuUpdateRespDTO> result = new ArrayList<>();

        //商品信息主体
        List<SpuOpenDTO> spuOpenDTO = spuReceiveVO.getSpuOpenDTO();
        //入驻商编号
        Long supplierId = spuReceiveVO.getSupplierId();
        //平台商ID
        Long sysCode = spuReceiveVO.getSysCode();

        // 构建分类信息为 key： 一级分类名称[^]二级分类名称[^]三级分类名称   value： 三级分类ID
        Map<String, Long> catgoryResultMap = new HashMap<>();

        //获取当前入驻商的管理分类信息（最末级）
        List<Long> catgoryList3 = prdtSupplierClassMapper.getCatgoryIdListBySupplierId(supplierId);

        //组装当前入驻商管理分类信息数据
        if(!catgoryList3.isEmpty()){
            assembleSyncCatgory(catgoryList3,catgoryResultMap);
        }

        //获取系统来源
        String source = SyncSourceType.B2B.getType();
        VisualSettingMasterDto visualMaster = productCacheService.getVisualMasterBySupplierId(supplierId);
        if(Objects.nonNull(visualMaster) && Objects.nonNull(visualMaster.getSourceType())){
            source = SyncSourceType.matchingType(visualMaster.getSourceType());
        }
        
        //检查
        for (SpuOpenDTO spuOpendto : spuOpenDTO) { 
            checkRetail(spuOpendto);
        }
        
        for (SpuOpenDTO spuOpendto : spuOpenDTO) {
            PrdtSpu prdtSpu = prdtSpuMapper.getBySourceNoOrSource(spuOpendto.getSpuNo(),supplierId,source);
            //单位转换
            SysDictData SmallSysDictData = processUnit(spuOpendto.getMinUnit(), SYS_PRDT_UNIT);
            // 处理中单位
            SysDictData MidSysDictData = processUnit(spuOpendto.getMidUnit(), SYS_PRDT_UNIT);
            // 处理大单位
            SysDictData LargeSysDictData = processUnit(spuOpendto.getLargeUnit(), SYS_PRDT_UNIT);
            
            //判断是否为修改操作
            if(spuOpendto.getType() == 1 && ToolUtil.isNotEmpty(prdtSpu)){
                //如果是修改操作 商品商品计价方式类型 不能调整
                if(!Objects.equals(prdtSpu.getPricingWay(),spuOpendto.getPricingWay())){
                    throw new ServiceException(500,"商品计价方式类型不能调整");
                }

                // 如果中单位传空值 需要停用中单位
                if (ToolUtil.isEmpty(spuOpendto.getMidUnit())) {
                    disableUnit(prdtSpu.getSpuId(), UnitTypeEnum.UNIT_MIDDLE.getType().toString());
                }else{
                    prdtSkuMapper.updateMidStatusEnable(prdtSpu.getSpuId());
                }
                // 如果大单位传空值 需要停用大单位
                if(ToolUtil.isEmpty(spuOpendto.getLargeUnit())){
                    disableUnit(prdtSpu.getSpuId(),UnitTypeEnum.UNIT_LARGE.getType().toString());
                }else{
                    prdtSkuMapper.updateLargeStatusEnable(prdtSpu.getSpuId());
                }
            }


            //--------------------------------------------------------
            PrdtSpuGroupSaveInVO saveInVO = new PrdtSpuGroupSaveInVO();
            //清空spu的单位，以防报错
            spuOpendto.setMinUnit(null);
            spuOpendto.setMidUnit(null);
            spuOpendto.setLargeUnit(null);
            //封装PrdtSpuSaveReqVO
            PrdtSpuSaveReqVO prdtSpuSaveReqVO = new PrdtSpuSaveReqVO();
            prdtSpuSaveReqVO = HutoolBeanUtils.toBean(spuOpendto, PrdtSpuSaveReqVO.class);

            if (spuOpendto.getType() != 1) {
                if (SmallSysDictData.getDictValue() == null) {
                    throw exception(SMALL_UNITS_MUST_BE_PASSED);
                }
            }
            if (ToolUtil.isNotEmpty(SmallSysDictData.getDictValue())) {
                prdtSpuSaveReqVO.setMinUnit(Long.valueOf(SmallSysDictData.getDictValue()));
            }
            //商品商品计价方式类型 如果是称重商品 则小单位必须是  系统默认为KG
            // 这里校验称重商品的SKU 单位是否是千克且 SKU只要一个最小单位
            if (SpuPricingWayEnum.isWeighGoods(spuOpendto.getPricingWay())) {
                prdtSpuSaveReqVO.setMinUnit(999L);
            }

            if (ToolUtil.isNotEmpty(MidSysDictData.getDictValue())) {
                prdtSpuSaveReqVO.setMidUnit(Long.valueOf(MidSysDictData.getDictValue()));

                // 校验推送的商品信息 换算单位数量
                if (spuOpendto.getMidSize() == null || spuOpendto.getMidSize().compareTo(BigDecimal.ZERO) == NumberPool.INT_ZERO) {
                    throw new ServiceException("中单位换算数量不能为空或0");
                }

            }
            if (ToolUtil.isNotEmpty(LargeSysDictData.getDictValue())) {
                prdtSpuSaveReqVO.setLargeUnit(Long.valueOf(LargeSysDictData.getDictValue()));
//                if (spuOpendto.getLargeMarkPrice() == null) {
//                    throw new RuntimeException("大单位必须传大单位标准价");
//                }
                // 校验推送的商品信息 换算单位数量
                if (spuOpendto.getLargeSize() == null || spuOpendto.getLargeSize().compareTo(BigDecimal.ZERO) == NumberPool.INT_ZERO) {
                    throw new ServiceException("大单位换算数量不能为空或0");
                }
            }


            prdtSpuSaveReqVO.setSupplierId(supplierId);
            prdtSpuSaveReqVO.setSource(source);
            prdtSpuSaveReqVO.setSourceNo(spuOpendto.getSpuNo());
            //设置平台商ID
            prdtSpuSaveReqVO.setSysCode(sysCode);


            //ANNTOERP 1是禁用 ，0是启用
            if (ToolUtil.isNotEmpty(spuOpendto) && ToolUtil.isNotEmpty(spuOpendto.getStatus())) {
                String status = spuOpendto.getStatus();
                if (status.equals(StringPool.ONE)) {
                    prdtSpuSaveReqVO.setStatus(NumberPool.LONG_ZERO);
                } else {
                    prdtSpuSaveReqVO.setStatus(NumberPool.LONG_ONE);
                }
            }

            //校验平台商品牌  ERP11.0需求 品牌：传文字，如果B2B没有此品牌 如果没有则为空
            if(Objects.nonNull(spuOpendto.getBrandName())){
                ProductBrand productBrand = productBrandService.getBrandByBrandName(spuOpendto.getBrandName());
                if(Objects.nonNull(productBrand)){
                    prdtSpuSaveReqVO.setBrandId(productBrand.getBrandId());
                }
            }

            //处理商品类别
            if(Objects.nonNull(spuOpendto.getCatgoryName())){
                //匹配商品类型
                prdtSpuSaveReqVO.setCatgoryId(catgoryResultMap.get(spuOpendto.getCatgoryName()));

/*                if (spuOpendto.getCatgoryName().contains("[^]")) {
                    //如果存在 | 则传的类别为一级|二级|三级 找到准确的三级类别处理
                    String[] categoryLevels = spuOpendto.getCatgoryName().split("\\[\\^\\]");
                    validateAndSetCategory(categoryLevels, catgoryMap, prdtSpuSaveReqVO);
                } else {
                    //校验入驻商管理分类  最末级类别:传文字，匹配B2B入驻商第三级管理类别名称，如果没有则为空
                    Long catgoryId = catgoryMap.get(spuOpendto.getCatgoryName());
                    if(Objects.nonNull(catgoryId)){
                        prdtSpuSaveReqVO.setCatgoryId(catgoryId);
                    }
                }*/
            }
            //封面图片
            if(ToolUtil.isNotEmpty(spuOpendto.getPicUrl())){
                prdtSpuSaveReqVO.setThumb(spuOpendto.getPicUrl());
            }
            //图片详情
            if(ToolUtil.isNotEmpty(spuOpendto.getImages())){
                prdtSpuSaveReqVO.setImages(spuOpendto.getImages());
            }
            //联动换算 第三方同步 联动换算为禁用 0
            prdtSpuSaveReqVO.setIsLinkage(NumberPool.INT_ZERO);


            List<PrdtSkuSpuGroupSaveInVO> skuList = new ArrayList<>();
            PrdtSkuSpuGroupSaveInVO prdtSkuSpuGroupSaveInVO = HutoolBeanUtils.toBean(spuOpendto, PrdtSkuSpuGroupSaveInVO.class);

            prdtSkuSpuGroupSaveInVO.setBarcode(spuOpendto.getBarcode());
            if (BeanUtil.isNotEmpty(SmallSysDictData.getDictValue())) {
                prdtSkuSpuGroupSaveInVO.setUnit(Long.valueOf(SmallSysDictData.getDictValue()));  //TODO 小单位
            }
            prdtSkuSpuGroupSaveInVO.setStatus(NumberPool.LONG_ONE);
            prdtSkuSpuGroupSaveInVO.setSource(source);
            prdtSkuSpuGroupSaveInVO.setSourceNo(spuOpendto.getSpuNo());
            //设置平台商ID
            prdtSkuSpuGroupSaveInVO.setSysCode(sysCode);
            // 供应价如果大于标准价 修改成 标准价
            if(ToolUtil.isNotEmpty(prdtSkuSpuGroupSaveInVO.getMarkPrice()) && ToolUtil.isNotEmpty(prdtSkuSpuGroupSaveInVO.getCostPrice())){
                if(prdtSkuSpuGroupSaveInVO.getMarkPrice().compareTo(prdtSkuSpuGroupSaveInVO.getCostPrice()) < 0){
                    prdtSkuSpuGroupSaveInVO.setCostPrice(prdtSkuSpuGroupSaveInVO.getMarkPrice());
                }
            }
            if(ToolUtil.isNotEmpty(prdtSkuSpuGroupSaveInVO.getMidMarkPrice()) && ToolUtil.isNotEmpty(prdtSkuSpuGroupSaveInVO.getMidCostPrice())){
                if(prdtSkuSpuGroupSaveInVO.getMidMarkPrice().compareTo(prdtSkuSpuGroupSaveInVO.getMidCostPrice()) < 0){
                    prdtSkuSpuGroupSaveInVO.setMidCostPrice(prdtSkuSpuGroupSaveInVO.getMidMarkPrice());
                }
            }
            if(ToolUtil.isNotEmpty(prdtSkuSpuGroupSaveInVO.getLargeMarkPrice()) && ToolUtil.isNotEmpty(prdtSkuSpuGroupSaveInVO.getLargeCostPrice())){
                if(prdtSkuSpuGroupSaveInVO.getLargeMarkPrice().compareTo(prdtSkuSpuGroupSaveInVO.getLargeCostPrice()) < 0){
                    prdtSkuSpuGroupSaveInVO.setLargeCostPrice(prdtSkuSpuGroupSaveInVO.getLargeMarkPrice());
                }
            }

            //如果供货价等于0或者等于空  则取标准价
            BigDecimal costPrice = prdtSkuSpuGroupSaveInVO.getCostPrice();
            BigDecimal midCostPrice = prdtSkuSpuGroupSaveInVO.getMidCostPrice();
            BigDecimal largeCostPrice = prdtSkuSpuGroupSaveInVO.getLargeCostPrice();

            prdtSkuSpuGroupSaveInVO.setCostPrice(ToolUtil.isEmpty(costPrice) || costPrice.compareTo(BigDecimal.ZERO) == NumberPool.INT_ZERO ? prdtSkuSpuGroupSaveInVO.getMarkPrice() : costPrice);
            prdtSkuSpuGroupSaveInVO.setMidCostPrice(ToolUtil.isEmpty(midCostPrice) || midCostPrice.compareTo(BigDecimal.ZERO) == NumberPool.INT_ZERO ? prdtSkuSpuGroupSaveInVO.getMidMarkPrice() : midCostPrice);
            prdtSkuSpuGroupSaveInVO.setLargeCostPrice(ToolUtil.isEmpty(largeCostPrice) || largeCostPrice.compareTo(BigDecimal.ZERO) == NumberPool.INT_ZERO ? prdtSkuSpuGroupSaveInVO.getLargeMarkPrice() : largeCostPrice);

            OpensourceDto opensourceDto = productCacheService.getOpensourceByMerchantId(supplierId);
            if(spuOpendto.getType().equals(NumberPool.INT_ONE)){
                //ERP11.0需求 传起订 订货组数 限购 新增操作时 使用传值 修改操作时 不覆盖
                prdtSkuSpuGroupSaveInVO.setMinOq(null);
                prdtSkuSpuGroupSaveInVO.setJumpOq(null);
                prdtSkuSpuGroupSaveInVO.setMaxOq(null);
                //如果入驻商不同步标准价格 页面价格允许修改 不覆盖
                if(opensourceDto.getSyncMarkPrice() == 0){
                    prdtSkuSpuGroupSaveInVO.setMarkPrice(null);
                    prdtSkuSpuGroupSaveInVO.setMidMarkPrice(null);
                    prdtSkuSpuGroupSaveInVO.setLargeMarkPrice(null);
                }
                //如果入驻商不同步供货价格 页面价格允许修改 不覆盖
                if(opensourceDto.getSyncCostPrice() == 0){
                    prdtSkuSpuGroupSaveInVO.setCostPrice(null);
                    prdtSkuSpuGroupSaveInVO.setMidCostPrice(null);
                    prdtSkuSpuGroupSaveInVO.setLargeCostPrice(null);
                }
            }
            List<PrdtPropertyAndValVO> propertyAndValList = new ArrayList<>();
            PrdtPropertyAndValVO prdtPropertyAndValVO = new PrdtPropertyAndValVO();

            propertyAndValList.add(prdtPropertyAndValVO);
            prdtSkuSpuGroupSaveInVO.setPropertyAndValList(propertyAndValList);

            skuList.add(prdtSkuSpuGroupSaveInVO);

            //-----------------------------------------------------
            /*prdtPropertyAndValVO.setPropertyName(spuOpendto.getSpecName());  //TODO sku属性名称 不能没有
            prdtPropertyAndValVO.setValName(spuOpendto.getSpuName());   //TODO 规格名称 spuOpendto.getSize()  24/6/3 10:39*/
            //-----------------------------------------------------


            //设置PropertyList数据
           /* List<PrdtPropertySpuGroupSaveInVO> propertyList = new ArrayList<>();
            PrdtPropertySpuGroupSaveInVO prdtPropertySpuGroupSaveInVO = new PrdtPropertySpuGroupSaveInVO();

            List<PrdtPropertyValSpuGroupSaveInVO> valList = new ArrayList<>();
            PrdtPropertyValSpuGroupSaveInVO prdtPropertyValSpuGroupSaveInVO = new PrdtPropertyValSpuGroupSaveInVO();
            //prdtPropertyValSpuGroupSaveInVO.setName(spuOpendto.getSpuName()); //TODO 规格名称 spuOpendto.getSize()  24/6/3 10:39
            valList.add(prdtPropertyValSpuGroupSaveInVO);

            //prdtPropertySpuGroupSaveInVO.setName(spuOpendto.getSpecName()); //TODO 规格名称 spuOpendto.getSize()  24/6/3 10:39
            prdtPropertySpuGroupSaveInVO.setValList(valList);


            propertyList.add(prdtPropertySpuGroupSaveInVO);*/

            //-----------------------------------------------------

            saveInVO.setSpu(prdtSpuSaveReqVO);
            saveInVO.setSkuList(skuList);
            //返回一个空集合
            saveInVO.setPropertyList(new ArrayList<>());
            // 执行保存或更新操作
            if(spuOpendto.getType().equals(NumberPool.INT_ZERO)){
                boolean exists = prdtSpuMapper.existsBySourceNoOrSource(prdtSpuSaveReqVO.getSourceNo(), prdtSpuSaveReqVO.getSupplierId(), prdtSpuSaveReqVO.getSource());
                if(exists){
                    logger.info("商品已经存在,商品编号为"+prdtSpuSaveReqVO.getSourceNo());
                }else{
                    insertPrdtSpuOpen(saveInVO);
                }
            }else{
                boolean exists = prdtSpuMapper.existsBySourceNoOrSource(prdtSpuSaveReqVO.getSourceNo(), prdtSpuSaveReqVO.getSupplierId(), prdtSpuSaveReqVO.getSource());
                if(exists){
                    //修改  修改后存入结果集合中  用于处理 更新SPU后续操作
                    result.add(updatePrdtSpuOpen(saveInVO));
                }else{
                    insertPrdtSpuOpen(saveInVO);
                }
            }
        }

        return result;
    }
    //处理商品分类
    /*private void validateAndSetCategory(String[] categoryLevels, Map<String, Long> catgoryMap, PrdtSpuSaveReqVO prdtSpuSaveReqVO) {
        // 校验是否为三层结构
        if (categoryLevels.length > 3) {
            throw exception(CATEGORY_LEVELS_EXCEED_LIMIT);
        }

        // 获取第三级类别的ID列表（可能存在多个）
        List<Long> thirdLevelCategoryIds = catgoryMap.entrySet().stream()
                .filter(entry -> entry.getKey().equals(categoryLevels[categoryLevels.length - 1]))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        if (!thirdLevelCategoryIds.isEmpty()) {
            boolean validCategoryFound = false;

            // 遍历每个第三级类别ID并进行验证
            for (Long thirdLevelCategoryId : thirdLevelCategoryIds) {
                boolean isValid = true;
                Long currentCategoryId = thirdLevelCategoryId;

                for (int i = categoryLevels.length - 2; i >= 0; i--) {
                    String currentCategory = categoryLevels[i];
                    Long parentId = catgoryMap.get(currentCategory);

                    // 验证当前层级是否存在并且符合预期的父子关系
                    if (parentId == null || !catgoryMap.containsValue(currentCategoryId)) {
                        isValid = false;
                        break; // 当前层级不符合，校验失败
                    }

                    // 更新当前ID为其上级ID，继续向上验证
                    currentCategoryId = parentId;
                }

                if (isValid) {
                    prdtSpuSaveReqVO.setCatgoryId(thirdLevelCategoryId);
                    validCategoryFound = true;
                    break; // 找到一个有效的分类即可结束验证
                }
            }

            if (!validCategoryFound) {
                throw exception(CATEGORY_VALIDATION_FAILED);
            }
        }
    }*/
    
    
    //!@开放API - 商品同步  - 3、更新缓存 + 刷新Es
    @Override
    public void updatePrdtSpuAfter(PrdtSpuUpdateRespDTO prdtSpuUpdateRespDTO) {
        //清除Spu、Sku缓存
        spuDTOCache.remove(prdtSpuUpdateRespDTO.getSpuId());
        if(ToolUtil.isNotEmpty(prdtSpuUpdateRespDTO.getSkuIdList())){
            prdtSpuUpdateRespDTO.getSkuIdList().forEach(skuDTOCache::remove);
        }

        // 刷新SPU - SKU组缓存
        // (Long spuId, Long areaId, Long classId, ProductType productType)
        for (PrdtAreaItem areaItem : prdtAreaItemMapper.selectByReleaseRefreshSpuId(prdtSpuUpdateRespDTO.getSpuId())) {
            productCacheService.removeSkuUnitGroup(
                    prdtSpuUpdateRespDTO.getSpuId(),
                    areaItem.getAreaId(),
                    areaItem.getAreaClassId(),
                    ProductType.LOCAL
            );
        }
        for (PrdtSupplierItem supplierItem : prdtSupplierItemMapper.selectByReleaseRefreshSpuId(prdtSpuUpdateRespDTO.getSpuId())) {
            productCacheService.removeSkuUnitGroup(
                    prdtSpuUpdateRespDTO.getSpuId(),
                    NumberPool.LOWER_GROUND_LONG,
                    supplierItem.getSaleClassId(),
                    ProductType.GLOBAL
            );
        }

        //刷新ES
        productEventService.acceptEvent(EsProductEventBuild.spuEvent(prdtSpuUpdateRespDTO.getSpuId()));
    }

    @Override
    public List<ProductExport> getExportProducts(PrdtExportSpuVO prdtExportSpuVO) {
        List<PrdtSpuGroupInVO> prdtSpuGroupInVOList  = new ArrayList<>();
        if(ToolUtil.isEmpty(prdtExportSpuVO.getDates())){
            throw new ServiceException("请选择要导出的数据!");
        }
        List<Long> dates = prdtExportSpuVO.getDates();
        for (Long spuId : dates) {
            prdtSpuGroupInVOList .add(getPrdtSpu(spuId));
        }
        // 将获取的数据解析为 ProductExport 对象
        List<ProductExport> productExports = parseProducts(prdtSpuGroupInVOList);
        // 返回解析后的产品信息
        return productExports;
    }

    @Override
    @Transactional
    public BigDecimal editStock(PrdtUpdateSkuStockReqVO skuStockReqVO) {
        PrdtSku prdtSku = prdtSkuMapper.selectById(skuStockReqVO.getSkuId());
        logger.info("变更库存前数据库库存skuId={},stock={}", skuStockReqVO.getSkuId(), prdtSku.getStock());
        PrdtSku update = new PrdtSku();
        update.setSkuId(skuStockReqVO.getSkuId());
        if (skuStockReqVO.isIncry()) {
            update.setStock(prdtSku.getStock().add(skuStockReqVO.getStock()));
        } else {
            update.setStock(prdtSku.getStock().subtract(skuStockReqVO.getStock()));
        }
        // 计算无库存时间
        prdtSkuService.calculateNoStockShelf(update);
        // 更新数据
        prdtSkuMapper.updateById(update);
        logger.info("变更库存前Redis库库存skuId={},stock={}", skuStockReqVO.getSkuId(), redisStockService.getSkuStock(skuStockReqVO.getSkuId()));
        redisStockService.setSkuStock(skuStockReqVO.getSkuId(), update.getStock());
        logger.info("变更库存后数据库库存skuId={},stock={}", skuStockReqVO.getSkuId(), update.getStock());
        return update.getStock();
    }

    @Override
    public String importProductImages(MultipartFile file) {
        try {
            // 检查文件是否为压缩包
            if (!file.getContentType().equals("application/x-zip-compressed")) {
                throw new ServiceException("请上传ZIP格式的压缩包");
            }

            // 创建临时目录来解压文件
            Path tempDir = Files.createTempDirectory("productImages");
            File tempFile = new File(tempDir.toFile(), file.getOriginalFilename());
            file.transferTo(tempFile);

            // 解压文件
            try (InputStream is = new FileInputStream(tempFile)) {
                unzip(is, tempDir);
            }

            // 处理解压后的文件
            List<String> missingSpus = handleExtractedFiles(tempDir);

            // 删除临时文件
            //FileUtils.deleteDirectory(tempDir.toFile());

            if (missingSpus.isEmpty()) {


                return "图片导入成功";
            } else {
                StringBuilder errorMessage = new StringBuilder("以下产品编号不存在: ");
                for (String spuNo : missingSpus) {
                    errorMessage.append(spuNo).append(", ");
                }
                errorMessage.deleteCharAt(errorMessage.length() - 2); // 去掉最后一个逗号和空格
                return "部分图片保存成功,以下spu不存在" + errorMessage;
            }
        } catch (IOException e) {
            log.error("处理文件时发生错误: {}", e.getMessage(), e);
            return "处理文件时发生错误: " + e.getMessage();
        }
    }

    @Override
    public List<SpuExportDTO> getSpuExportList(PrdtSpuPageReqVO vo) {
        //获取导出列表信息
        PageUtils.startPage(vo, false);
        List<SpuExportDTO> spuExportList = prdtSpuMapper.selectSpuExportList(vo);

        //获取单位信息
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, SysDictData> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictValue, item -> item));
        SysDictData defaultUnit = new SysDictData();

        //组装商品信息
        spuExportList.forEach(spu -> {
                //设置入驻商名称
                SupplierDTO supplierDTO = productCacheService.getSupplierDTO(spu.getSupplierId());
                if(ToolUtil.isNotEmpty(supplierDTO)){
                    spu.setSupplierName(supplierDTO.getSupplierName());
                }

                // 属性格式转换
                spu.setProperties(PropertyAndValDTO.getProperties(spu.getProperties()));

                // 组装单位信息
                BigDecimal largeSize = ToolUtil.isEmptyReturn(spu.getLargeSize(), BigDecimal.ZERO);
                BigDecimal midSize = ToolUtil.isEmptyReturn(spu.getMidSize(), BigDecimal.ZERO);

                // 处理单位关系
                StringBuilder unitRelation = new StringBuilder();

                if (ToolUtil.isNotEmpty(spu.getLargeUnit())) {
                    spu.setLargeUnitName(unitMap.getOrDefault(spu.getLargeUnit() + "", defaultUnit).getDictLabel());
                    if (largeSize.compareTo(BigDecimal.ZERO) != 0) {
                        unitRelation.append(StringUtils.format("1{}", spu.getLargeUnitName())); // 大单位固定为 1
                    }
                }

                if (ToolUtil.isNotEmpty(spu.getMidUnit())) {
                    spu.setMidUnitName(unitMap.getOrDefault(spu.getMidUnit() + "", defaultUnit).getDictLabel());
                    if (midSize.compareTo(BigDecimal.ZERO) != 0) {
                        if (largeSize.compareTo(BigDecimal.ZERO) != 0 && ToolUtil.isNotEmpty(spu.getLargeUnit())) {
                            BigDecimal midRatio = largeSize.divide(midSize, 2, RoundingMode.HALF_UP); // 保留 2 位小数，四舍五入
                            unitRelation.append(StringUtils.format(" = {}{}", midRatio, spu.getMidUnitName()));
                        } else {
                            // 如果大单位为 0，只显示中单位和小单位的关系
                            unitRelation.append(StringUtils.format("1{}", spu.getMidUnitName()));
                        }
                    }
                }

                if (ToolUtil.isNotEmpty(spu.getMinUnit())) {
                    spu.setMinUnitName(unitMap.getOrDefault(spu.getMinUnit() + "", defaultUnit).getDictLabel());
                    if (largeSize.compareTo(BigDecimal.ZERO) != 0 && ToolUtil.isNotEmpty(spu.getLargeUnit())) {
                        // 如果大单位不为 0，显示大单位和小单位的关系
                        unitRelation.append(StringUtils.format(" = {}{}", largeSize.setScale(2, BigDecimal.ROUND_HALF_UP), spu.getMinUnitName()));
                    } else if (midSize.compareTo(BigDecimal.ZERO) != 0 && ToolUtil.isNotEmpty(spu.getMidUnit())) {
                        // 如果大单位为 0，但中单位不为 0，显示中单位和小单位的关系
                        unitRelation.append(StringUtils.format(" = {}{}", midSize.setScale(2, BigDecimal.ROUND_HALF_UP), spu.getMinUnitName()));
                    } else {
                        // 如果大单位和中单位都为 0，只显示小单位
                        unitRelation.append(StringUtils.format("1{}", spu.getMinUnitName()));
                    }
                }


            spu.setUnitRelation(unitRelation.toString());
                //中单位库存
                if (ObjectUtil.isNotNull(spu.getMidSize())){
                    spu.setMidStock(StockUtil.stockDivide(spu.getStock(), spu.getMidSize()));
                }
                //大单位库存
                if (ObjectUtil.isNotNull(spu.getLargeSize())){
                    spu.setLargeStock(StockUtil.stockDivide(spu.getStock(), spu.getLargeSize()));
                }
        });
        return spuExportList;
    }

    @Override
    public Map<Long, SpuDTO> listBySpuIds(List<Long> spuIds) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return Maps.newHashMap();
        }
        List<PrdtSpu> prdtSpus = prdtSpuMapper.selectList(PrdtSpu::getSpuId, spuIds.stream().distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(prdtSpus)) {
            return Maps.newHashMap();
        }
        return prdtSpus.stream()
                .collect(Collectors.toMap(PrdtSpu::getSpuId, prdtSpu -> Objects.requireNonNull(HutoolBeanUtils.toBean(prdtSpu, SpuDTO.class))));
    }

    private void unzip(InputStream is, Path tempDir) throws IOException {
        try (ZipInputStream zis = new ZipInputStream(is)) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    Path filePath = tempDir.resolve(entry.getName());
                    Files.copy(zis, filePath);
                }
            }
        }
    }

    private List<String> handleExtractedFiles(Path tempDir) throws IOException {
        List<String> missingSpus = new ArrayList<>();
        List<File> files = FileUtils.listFiles(tempDir.toFile(), new String[]{"jpg", "jpeg", "png"}, true)
                .stream()
                .collect(Collectors.toList());

        // 使用Map来存储每个SPU的轮播图URL列表
        Map<String, List<String>> spuImagesMap = new HashMap<>();

        // 提取一次供应商ID
        String firstImageName = files.get(0).getName();
        Long supplierId = extractSupplierId(firstImageName);
        if (supplierId == null) {
            throw new ServiceException("无法提取供应商ID，请检查图片命名规则");
        }

        for (File file : files) {
            String imageName = file.getName();
            boolean isCarousel = isCarouselImage(imageName);
            String spuNo = extractSpuNo(imageName);
            // 上传图片到服务器或云存储
            String imageUrl = uploadImage(file, imageName);

            // 更新SPU的主图或轮播图字段
            if (!updateProductImage(spuNo,supplierId, imageUrl, isCarousel, spuImagesMap)) {
                if(!isCarousel){
                    missingSpus.add(spuNo);
                }
            }
        }

        // 批量更新轮播图字段
        spuImagesMap.forEach((spuNo, imageUrls) -> updateCarouselImages(spuNo,supplierId, imageUrls));

        return missingSpus;
    }

    private Long extractSupplierId(String imageName) {
        int lastDotIndex = imageName.lastIndexOf('.');
        String nameWithoutExtension = imageName.substring(0, lastDotIndex);
        String[] parts = nameWithoutExtension.split("_");
        if (parts.length >= 2) {
            return Long.parseLong(parts[0]); // 返回供应商ID
        }
        return 0L;
    }

    private String extractSpuNo(String imageName) {
        int lastDotIndex = imageName.lastIndexOf('.');
        String nameWithoutExtension = imageName.substring(0, lastDotIndex);
        String[] parts = nameWithoutExtension.split("_");
        if (parts.length >= 2) {
            return parts[1]; // 返回 SPU 编号
        }
        return nameWithoutExtension;
    }

    private boolean isCarouselImage(String imageName) {
        int lastDotIndex = imageName.lastIndexOf('.');
        String nameWithoutExtension = imageName.substring(0, lastDotIndex);
        String[] parts = nameWithoutExtension.split("_");
        return parts.length >= 3; // 判断是否有轮播编号
    }

    private String uploadImage(File file, String imageName) throws IOException {
        Path path = Paths.get(file.getAbsolutePath());
        String contentType = Files.probeContentType(path);

        // 将 File 转换为 MultipartFile
        try (FileInputStream input = new FileInputStream(file)) {
            MultipartFile multipartFile = new MockMultipartFile(imageName, file.getName(), contentType, IOUtils.toByteArray(input));

            // 调用远程文件服务上传
            R<SysFile> fileResult = remoteFileService.upload(multipartFile);
            return fileResult.getData().getUrl();
        }
    }

    private boolean updateProductImage(String spuNo,Long supplierId, String imageUrl, boolean isCarousel, Map<String, List<String>> spuImagesMap) {
        if (isCarousel) {
            // 将轮播图URL添加到Map中
            spuImagesMap.computeIfAbsent(spuNo, k -> new ArrayList<>()).add(imageUrl);
        } else {
            // 根据SPU ID查询商品
            PrdtSpu prdtSpu = prdtSpuMapper.selectBySpuNoAndSupplierId(spuNo,supplierId);
            if (prdtSpu == null) {
                return false; // SPU ID 不存在
            }

            // 更新主图字段
            prdtSpu.setThumb(imageUrl);
            prdtSpuMapper.updateById(prdtSpu);

            //刷新spu缓存
            this.updatePrdtSpuAfter(new PrdtSpuUpdateRespDTO(prdtSpu.getSpuId(), null));

             this.updatePrdtSpuAfter(new PrdtSpuUpdateRespDTO(prdtSpu.getSpuId(), null));
        }

        return true;
    }

    private void updateCarouselImages(String spuNo,Long supplierId, List<String> imageUrls) {
        // 根据SPU ID查询商品
        PrdtSpu prdtSpu = prdtSpuMapper.selectBySpuNoAndSupplierId(spuNo,supplierId);
        if (prdtSpu == null) {
            return; // SPU ID 不存在
        }

        // 更新轮播图字段
        prdtSpu.setImages(String.join(",", imageUrls));

        // 保存更新后的商品信息
        prdtSpuMapper.updateById(prdtSpu);

        //刷新spu缓存
        this.updatePrdtSpuAfter(new PrdtSpuUpdateRespDTO(prdtSpu.getSpuId(), null));

        this.updatePrdtSpuAfter(new PrdtSpuUpdateRespDTO(prdtSpu.getSpuId(), null));
    }




    private List<ProductExport> parseProducts(List<PrdtSpuGroupInVO> prdtSpuGroupInVOList) {
        List<ProductExport> productList = new ArrayList<>();
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        // 防止空指针异常
        Map<String, SysDictData> unitMap = (dictCache != null ? dictCache : new ArrayList<SysDictData>())
                .stream()
                .collect(Collectors.toMap(SysDictData::getDictValue, item -> item));

        for (PrdtSpuGroupInVO prdtSpuGroupInVO : prdtSpuGroupInVOList) {
            PrdtSpuSaveReqVO spu = prdtSpuGroupInVO.getSpu();
            if (spu == null) continue; // 防止 spu 为空时发生空指针异常

            String spuNo = spu.getSpuNo();
            String spuName = spu.getSpuName();
            SupplierDTO supplierDTO = productCacheService.getSupplierDTO(spu.getSupplierId());
            String supplierName = supplierDTO != null ? supplierDTO.getSupplierName() : "Unknown Supplier"; // 处理供应商为空的情况

            // 类别名称处理
            String tertiaryCategoryName = null;
            String secondaryCategoryName = null;
            String primaryCategoryName = null;

            if (ToolUtil.isNotEmpty(spu.getCatgoryId())) {
                CatgoryDTO catgoryDTO3 = productCacheService.getCatgoryDTO(spu.getCatgoryId());
                if (catgoryDTO3 != null) {
                    tertiaryCategoryName = catgoryDTO3.getCatgoryName();
                    if (ToolUtil.isNotEmpty(catgoryDTO3.getPid())) {
                        CatgoryDTO catgoryDTO2 = productCacheService.getCatgoryDTO(catgoryDTO3.getPid());
                        if (catgoryDTO2 != null) {
                            secondaryCategoryName = catgoryDTO2.getCatgoryName();
                            if (ToolUtil.isNotEmpty(catgoryDTO2.getPid())) {
                                CatgoryDTO catgoryDTO1 = productCacheService.getCatgoryDTO(catgoryDTO2.getPid());
                                if (catgoryDTO1 != null) {
                                    primaryCategoryName = catgoryDTO1.getCatgoryName();
                                }
                            }
                        }
                    }
                }
            }

            String brandName = spu.getBrandName();
            String minUnit = spu.getMinUnit() != null ? spu.getMinUnit().toString() : null;
            String midUnit = spu.getMidUnit() != null ? spu.getMidUnit().toString() : null;
            String largeUnit = spu.getLargeUnit() != null ? spu.getLargeUnit().toString() : null;

            List<PrdtSkuSpuGroupReqVO> skuList = prdtSpuGroupInVO.getSkuList();
            if (skuList == null || skuList.isEmpty()) continue; // 防止 skuList 为空时发生空指针异常

            for (PrdtSkuSpuGroupReqVO sku : skuList) {
                if (sku == null) continue; // 防止 sku 为 null
                String propertiesJson = sku.getProperties() != null ? sku.getProperties() : null;
                String properties = PropertyAndValDTO.getProperties(propertiesJson);
                BigDecimal minStock = ToolUtil.isEmptyReturn(sku.getStock(), BigDecimal.ZERO); // 防止 stock 为空
                String minUnitValue = unitMap.getOrDefault(minUnit, new SysDictData()).getDictLabel();

                // 创建最小单位的产品信息
                ProductExport product = createProduct(
                        spuNo, spuName, supplierName, primaryCategoryName, secondaryCategoryName, tertiaryCategoryName,
                        brandName, minUnitValue, properties, sku.getMarkPrice(), minStock, sku.getBarcode(),
                        spu.getStatus() != null ? spu.getStatus().toString() : "Unknown Status" // 防止状态为空
                        ,spu.getAuxiliarySpuNo(),spu.getSourceNo()
                );
                productList.add(product);

                // 如果存在中单位，创建中单位的产品信息
                if (midUnit != null) {
                    BigDecimal midStock = calculateStock(minStock, spu.getMidSize());
                    String midUnitValue = unitMap.getOrDefault(midUnit, new SysDictData()).getDictLabel();
                    product = createProduct(
                            spuNo, spuName, supplierName, primaryCategoryName, secondaryCategoryName, tertiaryCategoryName,
                            brandName, midUnitValue, properties, sku.getMidMarkPrice(), midStock, sku.getMidBarcode(),
                            spu.getStatus() != null ? spu.getStatus().toString() : "Unknown Status"
                            ,spu.getAuxiliarySpuNo(),spu.getSourceNo()
                    );
                    productList.add(product);
                }

                // 如果存在大单位，创建大单位的产品信息
                if (largeUnit != null) {
                    BigDecimal largeStock = calculateStock(minStock, spu.getLargeSize());
                    String largeUnitValue = unitMap.getOrDefault(largeUnit, new SysDictData()).getDictLabel();
                    product = createProduct(
                            spuNo, spuName, supplierName, primaryCategoryName, secondaryCategoryName, tertiaryCategoryName,
                            brandName, largeUnitValue, properties, sku.getLargeMarkPrice(), largeStock, sku.getLargeBarcode(),
                            spu.getStatus() != null ? spu.getStatus().toString() : "Unknown Status"
                            ,spu.getAuxiliarySpuNo(),spu.getSourceNo()
                    );
                    productList.add(product);
                }
            }
        }
        return productList;
    }

    /**
     * 根据最小单位的库存和倍数计算中、大单位的库存
     */
    private BigDecimal calculateStock(BigDecimal minStock, BigDecimal size) {
        return Optional.ofNullable(size)
                .filter(s -> s.compareTo(BigDecimal.ZERO) > 0)
                .map(s -> minStock.divide(s, PRICE_RESERVE_3, RoundingMode.HALF_UP))
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 创建 ProductExport 对象
     */
    private ProductExport createProduct(String spuNo, String spuName, String supplierName, String primaryCategoryName,
                                        String secondaryCategoryName, String tertiaryCategoryName, String brandName,
                                        String unit, String properties, BigDecimal price, BigDecimal stock,
                                        String barcode, String status,String auxiliarySpuNo,String sourceNo) {
        ProductExport product = new ProductExport();
        product.setSpuNo(spuNo);
        product.setSpuName(spuName);
        product.setSupplierName(supplierName);
        product.setPrimaryCategoryName(primaryCategoryName);
        product.setSecondaryCategoryName(secondaryCategoryName);
        product.setTertiaryCategoryName(tertiaryCategoryName);
        product.setBrandName(brandName);
        product.setProperties(properties);
        product.setUnit(unit);
        product.setPrice(price);
        product.setStock(stock);
        product.setBarcode(barcode);
        product.setStatus(status);
        product.setAuxiliarySpuNo(auxiliarySpuNo);
        product.setSourceNo(sourceNo);
        return product;
    }


    private SysDictData getSysDictData(String unit, String dictType, SysDictData sysDictData) {
        if (sysDictData == null) {
            //sysDictDataService
            //sys_prdt_unit 瓶，件，箱 sys_prdt_unit为空 ，则新增一个字典
            //sysDictDataService.
            sysDictData = new SysDictData();
            String maxDictValue = String.valueOf(dictDataApi.getMaxdictValue(dictType));
            sysDictData.setDictType(dictType);
            sysDictData.setDictLabel(unit);
            sysDictData.setDictValue(String.valueOf(maxDictValue));
            sysDictData.setStatus(StringPool.ZERO);
            sysDictData.setDictSort(NumberPool.LONG_ZERO);
            sysDictData.setListClass(DictTypeConstants.DEFAULT);
            sysDictData.setIsDefault(DictTypeConstants.NO);
            dictDataApi.insertDictData(sysDictData);
        }
        return sysDictData;
    }
    private void validatePrdtSpuExists(Long spuId) {
        if (prdtSpuMapper.selectById(spuId) == null) {
            throw exception(PRDT_SPU_NOT_EXISTS);
        }
    }

    /**
     * 校验管理分类是否是三级分类
     * @param catgoryId
     */
    public void checkCatgoryLevel(Long catgoryId){
        if(catgoryId == null){
            throw exception(PRDT_CATGORY_IS_NULL);
        }
        PrdtCatgory catgory = prdtCatgoryMapper.selectById(catgoryId);
        if(ToolUtil.isEmpty(catgory)){
            throw exception(PRDT_CATGORY_NOT_EXISTS);
        }
        if(catgory.getLevel() != INT_THREE){
            throw exception(PRDT_CATGORY_LEVEL_NOT_MAKE);
        }
    }




    /**
     * 校验生产日期
     * @param latestDate 最新生产日期
     * @param oldestDate 最旧生产日期
     */
    public void checkLatestDate(Date latestDate,Date oldestDate){
        //如果两个生产日期都不为空时  需要校验 最新生产日期 >= 最旧生产日期
        if(ToolUtil.isNotEmpty(latestDate) && ToolUtil.isNotEmpty(oldestDate)){
            if (latestDate.before(oldestDate)){
                throw exception(PRDT_SPU_PRODUCTION_DATE_NOT_NORM);
            }
        }

    }

    public void checkSpu(PrdtSpuSaveReqVO spuVo){
        //spu信息是否存在
        if(ToolUtil.isEmpty(spuVo))throw exception(PRDT_SPU_NOT_EXISTS);
        //校验商品管理类别 是否是第三级
        checkCatgoryLevel(spuVo.getCatgoryId());
        //商品明细长度
        if (ToolUtil.isNotEmpty(spuVo.getDetails()) && spuVo.getDetails().length() > 10240) throw exception(PRDT_SPU_DETAILS_SIZE);
        //入驻商信息是否存在
        if(ToolUtil.isEmpty(spuVo.getSupplierId()))throw exception(PRDT_CHECK_SUPPLIER);
        //商品编码
        Long count = prdtSpuMapper.selectCountBySpuNo(spuVo.getSpuNo(),spuVo.getSupplierId());
        if(ToolUtil.isNotEmpty(count) && count > 0) {
            throw exception(PRDT_SPU_SPUNO_EXISTS);
        }
        //校验生产日期
        checkLatestDate(spuVo.getLatestDate(),spuVo.getOldestDate());
    }
    
    /**
     * 校验零售和批发分润
     * https://cf.annto.com/pages/viewpage.action?pageId=82721812
     * @param spuVo
     * @param skuList
     */
    private void checkRetailWholeProfit(PrdtSpuSaveReqVO spuVo,List<PrdtSkuSpuGroupSaveInVO> skuList) {
        Integer retailProfitMode = spuVo.getRetailProfitMode();
        Integer wholesaleProfitMode = spuVo.getWholesaleProfitMode();

        if (skuList == null) return;

        //都不选，那只能清空了
        if (null == retailProfitMode && null == wholesaleProfitMode){
            for (PrdtSkuSpuGroupSaveInVO sku : skuList) {
                //sku.setProfitRate(null);
                //sku.setProfitAmount(null);
            }
            return;
        }

        for (PrdtSkuSpuGroupSaveInVO sku : skuList) {
            Integer saleType = sku.getSaleType();

            // 1. 销售类型为零售
            if (saleType != null && saleType == SkuSaleTypeEnum.RETAIL.getCode()) {
                if ((sku.getRetailPrice() == null || BigDecimal.ZERO.compareTo(sku.getRetailPrice()) >= 0)
                    && (sku.getMidRetailPrice() == null || BigDecimal.ZERO.compareTo(sku.getMidRetailPrice()) >= 0)
                    && (sku.getLargeRetailPrice() == null || BigDecimal.ZERO.compareTo(sku.getLargeRetailPrice()) >= 0)) {
                    throw exception(PRDT_SPU_RETAIL_NOT_NULL);
                }
                if (retailProfitMode != null) {
                    if (retailProfitMode == RetailProfitModeEnum.BY_RATE.getCode()) {
                        if ((sku.getProfitRate() == null || sku.getProfitRate().compareTo(BigDecimal.ZERO) <= 0)
                            && (sku.getMidProfitRate() == null || sku.getMidProfitRate().compareTo(BigDecimal.ZERO) <= 0)
                            && (sku.getLargeProfitRate() == null || sku.getLargeProfitRate().compareTo(BigDecimal.ZERO) <= 0))
                            throw exception(PRDT_SPU_RETAIL_PROFIT_RATE_REQUIRED);
                        //sku.setProfitAmount(null);
                        //sku.setMidProfitAmount(null);
                        //sku.setLargeProfitAmount(null);
                    } else if (retailProfitMode == RetailProfitModeEnum.BY_AMOUNT.getCode()) {
                        if ((sku.getProfitAmount() == null || sku.getProfitAmount().compareTo(BigDecimal.ZERO) <= 0)
                            && (sku.getMidProfitAmount() == null || sku.getMidProfitAmount().compareTo(BigDecimal.ZERO) <= 0)
                            && (sku.getLargeProfitAmount() == null || sku.getLargeProfitAmount().compareTo(BigDecimal.ZERO) <= 0))
                            throw exception(PRDT_SPU_RETAIL_PROFIT_AMOUNT_REQUIRED);
                        //sku.setProfitRate(null);
                        //sku.setMidProfitRate(null);
                        //sku.setLargeProfitRate(null);
                    } else {
                        //sku.setProfitRate(null);
                        //sku.setProfitAmount(null);
                        //sku.setMidProfitRate(null);
                        //sku.setMidProfitAmount(null);
                        //sku.setLargeProfitRate(null);
                        //sku.setLargeProfitAmount(null);
                    }
                }
            }

            // 2. 销售类型为批发
            else if (saleType != null && saleType == SkuSaleTypeEnum.WHOLESALE.getCode()) {
                if (wholesaleProfitMode != null) {
                    if (wholesaleProfitMode == WholesaleProfitModeEnum.BY_RATE.getCode()) {
                        if (sku.getProfitRate() == null || sku.getProfitRate().compareTo(BigDecimal.ZERO) <= 0) throw exception(PRDT_SPU_WHOLESALE_PROFIT_RATE_REQUIRED);
                        //sku.setProfitAmount(null);
                    } else if (wholesaleProfitMode == WholesaleProfitModeEnum.BY_AMOUNT.getCode()) {
                        if (sku.getProfitAmount() == null || sku.getProfitAmount().compareTo(BigDecimal.ZERO) <= 0) throw exception(PRDT_SPU_WHOLESALE_PROFIT_AMOUNT_REQUIRED);
                        //sku.setProfitRate(null);
                    } else {
                        //sku.setProfitRate(null);
                        //sku.setProfitAmount(null);
                    }
                }
            }

            // 3. 销售类型为空
            else {
                //sku.setProfitRate(null);
                //sku.setProfitAmount(null);
            }
        }
    }
    
    public void checkSpuOpen(PrdtSpuSaveReqVO spuVo){
        //spu信息是否存在
        if(ToolUtil.isEmpty(spuVo))throw exception(PRDT_SPU_NOT_EXISTS);
        //校验商品管理类别 是否是第三级
       /* checkCatgoryLevel(spuVo.getCatgoryId());*/
        //商品明细长度
        if (ToolUtil.isNotEmpty(spuVo.getDetails()) && spuVo.getDetails().length() > 10240) throw exception(PRDT_SPU_DETAILS_SIZE);
        //商品编码
        //if(ToolUtil.isNotEmpty(prdtSpuMapper.selectSpuBySpu(new PrdtSpuRespVO(null,spuVo.getSpuNo()))))throw exception(PRDT_SPU_SPUNO_EXISTS);
        //入驻商信息是否存在
        if(ToolUtil.isEmpty(spuVo.getSupplierId()))throw exception(PRDT_CHECK_SUPPLIER);
        /*//校验生产日期
        checkLatestDate(spuVo.getLatestDate(),spuVo.getOldestDate());*/
    }


    private void checkSkuData(List<PrdtSku> prdtSkuList) {
        /**
         * 2024年6月21日15:47:11 关闭条码唯一验证
         */
        /*Map<String, List<PrdtSku>> barcodeMap = prdtSkuList.stream()
                .map(sku ->
                        Stream.of(new PrdtSku(sku.getSkuId(), sku.getBarcode()), new PrdtSku(sku.getSkuId(), sku.getMidBarcode()), new PrdtSku(sku.getSkuId(), sku.getLargeBarcode()))
                                .filter(item -> Objects.nonNull(item.getBarcode()))
                                .collect(Collectors.toList())
                ).flatMap(Collection::stream)
                .collect(Collectors.groupingBy(PrdtSku::getBarcode));

        barcodeMap.forEach((barcode, barcodeList) -> {
            if (barcodeList.size() > NumberPool.INT_ONE) {
                throw exception(new ErrorCode(PRDT_SPU_BARCODE_REPEAT.getCode(), StringUtils.format("{} 规格条码重复", barcode)));
            }
            List<Long> skuIdList = barcodeList.stream().map(PrdtSku::getSkuId).collect(Collectors.toList());
            Long barcodeNumber = prdtSkuMapper.selectCountByBarcode(barcode,skuIdList);
            if (barcodeNumber > NumberPool.INT_ONE) {
                throw exception(new ErrorCode(PRDT_SPU_BARCODE_REPEAT.getCode(), StringUtils.format("{} 规格条码重复", barcode)));
            }
        });*/
    }

    /**
     * 校验 如果是外部推送的商品信息 不允许修改库存
     * @param spuId
     * @param prdtSkuList  原sku数据
     * @param skuVoList   修改sku数据
     */
    private void checkSourceStock(Long spuId,List<PrdtSku> prdtSkuList,List<PrdtSkuSpuGroupSaveInVO> skuVoList){
        PrdtSpu prdtSpu = prdtSpuMapper.selectById(spuId);
        if(Objects.nonNull(prdtSpu.getSource()) && !SyncSourceType.B2B.getName().equals(prdtSpu.getSource())){
            //组装sku的库存信息
            Map<Long, BigDecimal> checkStockMap = prdtSkuList.stream().collect(Collectors.toMap(PrdtSku::getSkuId, PrdtSku::getStock));
            skuVoList.forEach(sku ->{
                //如果是新增的sku信息  不允许设置库存
                if(Objects.isNull(sku.getSkuId()) && Objects.nonNull(sku.getStock())){
                    throw exception(PRDT_SPU_CHECK_SOURCE_STOCK);
                }
                //修改的sku信息  不允许修改库存信息
                if(Objects.nonNull(sku.getSkuId())){
                    //获取sku的库存信息
//                    BigDecimal stock = checkStockMap.get(sku.getSkuId());
                    BigDecimal stock = redisStockService.getSkuStock(sku.getSkuId());
                    if(Objects.isNull(stock) && Objects.nonNull(sku.getStock())){
                        throw exception(PRDT_SPU_CHECK_SOURCE_STOCK);
                    }

                    if(stock.compareTo(sku.getStock()) != NumberPool.INT_ZERO){
                        throw exception(PRDT_SPU_CHECK_SOURCE_STOCK);
                    }
                }

            });

        }
    }



    private void validatePriceUpdate(PrdtSku newObj, PrdtSku oldObj) {

        List<BigDecimal> prdtSku1 = new ArrayList<>();
        prdtSku1.add(newObj.getMarkPrice());
        prdtSku1.add(newObj.getCostPrice());
        prdtSku1.add(newObj.getMidMarkPrice());
        prdtSku1.add(newObj.getMidCostPrice());
        prdtSku1.add(newObj.getLargeMarkPrice());
        prdtSku1.add(newObj.getLargeCostPrice());

        List<BigDecimal> prdtSku2 = new ArrayList<>();
        prdtSku2.add(oldObj.getMarkPrice());
        prdtSku2.add(oldObj.getCostPrice());
        prdtSku2.add(oldObj.getMidMarkPrice());
        prdtSku2.add(oldObj.getMidCostPrice());
        prdtSku2.add(oldObj.getLargeMarkPrice());
        prdtSku2.add(oldObj.getLargeCostPrice());

        boolean changePrice = false;
        for (int i = 0; i < prdtSku1.size(); i++) {
            BigDecimal price1 = prdtSku1.get(i);
            BigDecimal price2 = prdtSku2.get(i);
            if (Objects.nonNull(price1) && Objects.isNull(price2)) {
                changePrice = true;
                break;
            }
            if (Objects.isNull(price1) && Objects.nonNull(price2)) {
                changePrice = true;
                break;
            }
            if (Objects.nonNull(price1) && !NumberUtil.equals(price1, price2)) {
                changePrice = true;
                break;
            }
        }
        validateUpdateSku(newObj.getSkuId(), changePrice, false);
    }

    /**
     * 验证sku是否能修改
     * @param skuId
     */
    private void validateUpdateSku(Long skuId, boolean changePrice, boolean delete) {
        PrdtSku prdtSku = prdtSkuMapper.selectById(skuId);

        // 上架验证
        if (delete) {
            // 验证本地上架
            {
                Long count = prdtAreaItemMapper.selectBySkuIdReleaseCount(skuId);
                if (count > NumberPool.LONG_ZERO) {
                    throw exception(PRDT_SPU_RELEASE_VALIDATE, PropertyAndValDTO.getProperties(prdtSku.getProperties()));
                }
            }
            // 验证全国上架
            {
                Long count = prdtSupplierItemMapper.selectBySkuIdReleaseCount(skuId);
                if (count > NumberPool.LONG_ZERO) {
                    throw exception(PRDT_SPU_RELEASE_VALIDATE, PropertyAndValDTO.getProperties(prdtSku.getProperties()));
                }
            }
        }

        // 活动验证
        if (changePrice || delete) {
            Long promotionCount = activityApi.countBySkuId(skuId).getCheckedData();
            if (promotionCount > NumberPool.LONG_ZERO) {
                throw exception(PRDT_SPU_ACTIVITY_VALIDATE, PropertyAndValDTO.getProperties(prdtSku.getProperties()));
            }
        }
    }

    // 修改 / 新增 前验证SKU
    private void checkSku(List<PrdtSkuSpuGroupSaveInVO> skuSpuGroupSaveInVOS, PrdtSpuSaveReqVO spuVo) {
        for (PrdtSkuSpuGroupSaveInVO saveInVO : skuSpuGroupSaveInVOS) {
            if (Objects.nonNull(saveInVO.getSaleTotalRate()) && NumberUtil.isGreater(saveInVO.getSaleTotalRate(), new BigDecimal("0.29"))) {
                throw exception(PRDT_SPU_MAX_PROFIT_RATE);
            }
            // 这里校验称重商品的SKU 单位是否是千克且 SKU只要一个最小单位
            if (SpuPricingWayEnum.isWeighGoods(spuVo.getPricingWay())) {
                try {
                    Assert.isTrue(Objects.equals(spuVo.getMinUnit(), 999L),
                            "商品【{}】计价方式类型为称重商品，小单位必须为千克",
                            spuVo.getSpuName()
                    );
                } catch (IllegalArgumentException e) {
                    throw new ServiceException(e.getMessage());
                }
            }
        }
    }

    /**
     * !@商品 - 上游同步 - 2.1、组装与第三方同步商品相关的该入驻商所使用的管理分类信息
     * @param catgoryList3
     * @param catgoryResultMap
     */
    private void assembleSyncCatgory(List<Long> catgoryList3,Map<String, Long> catgoryResultMap){
        //获取到了二级分类
        List<Long> catgoryList2 = prdtCatgoryMapper.getPidCatgoryIds(catgoryList3);
        //获取到了一级分类
        List<Long> catgoryList1 = prdtCatgoryMapper.getPidCatgoryIds(catgoryList2);
        // 合并所有级别的分类ID并自动去重
        Set<Long> allCategoryIds = new HashSet<>();
        allCategoryIds.addAll(catgoryList3); // 三级分类
        allCategoryIds.addAll(catgoryList2);  // 二级分类
        allCategoryIds.addAll(catgoryList1);  // 一级分类
        List<PrdtCatgory> catgoryList = prdtCatgoryMapper.getCatgoryByIds(new ArrayList<>(allCategoryIds));

        // 构建分类ID:分类信息 Map
        Map<Long, PrdtCatgory> catgoryMap = catgoryList.stream().collect(Collectors.toMap(PrdtCatgory::getCatgoryId, x-> x));

        for (Long categoryId : catgoryList3) {
            PrdtCatgory thirdLevelCategory = catgoryMap.get(categoryId);
            if (thirdLevelCategory != null) {
                Long secondLevelCategoryId = thirdLevelCategory.getPid();
                PrdtCatgory secondLevelCategory = catgoryMap.get(secondLevelCategoryId);
                if (secondLevelCategory != null) {
                    Long firstLevelCategoryId = secondLevelCategory.getPid();
                    PrdtCatgory firstLevelCategory = catgoryMap.get(firstLevelCategoryId);
                    if (firstLevelCategory != null) {
                        //组装Key
                        String key = firstLevelCategory.getCatgoryName() + "[^]" + secondLevelCategory.getCatgoryName() + "[^]" + thirdLevelCategory.getCatgoryName();
                        catgoryResultMap.put(key, categoryId);
                    }
                }
            }
        }
    }


    private void checkSkuStatus(List<PrdtSkuSpuGroupSaveInVO> skuList) {
        for (PrdtSkuSpuGroupSaveInVO sku : skuList) {
            if (ToolUtil.isNotEmpty(sku.getStatus()) && sku.getStatus() == 0){
                List<SkuDTO> skuDTOS = prdtSkuMapper.selectExistShelfSkuId(sku.getSkuId());
                if (ToolUtil.isNotEmpty(skuDTOS)){
                    throw new ServiceException("商品已上架无法停用");
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchShelf(PrdtBatchShelfVO batchShelfVO) {
        StringBuilder failureMsg = new StringBuilder();
        int failureNum = 0;
        try {
            List<Long> shelfSpuList = batchShelfVO.getShelfSpuList();
            if (ToolUtil.isEmpty(shelfSpuList)) {
                throw new ServiceException("请选择上架的商品");
            }
            // 数据校验
            if (ToolUtil.isEmpty(batchShelfVO.getMinShelfStatus())) {
                throw new ServiceException("请选择上架单位");
            }

            batchShelfVO.getShelfAreaIdCatgoryIdList().forEach(areaCatogry -> {
                Long areaId = areaCatogry.getAreaId();
                Long catgoryId = areaCatogry.getCatgoryId();
                // 校验区域是否存在
                AreaDTO areaDTO = productCacheService.getAreaDto(areaId);
                if (ToolUtil.isEmpty(areaDTO)) {
                    throw new ServiceException("areaId上架区域编号不存在！请先修改数据");
                }
                //校验商品区域城市 是否是二级
                remoteAreaApi.checkAreaByAreaId(areaId);
                // 校验类别是否存在
                PrdtAreaClass areaClass = prdtAreaClassMapper.selectById(catgoryId);
                if (ToolUtil.isEmpty(areaClass)) {
                    throw new ServiceException("catgoryId上架分类编号不存在！请先修改数据");
                }
                //校验商品城市展示类别 是否是第三级
                prdtAreaItemService.checkClassLevel(catgoryId);

                // 根据区域编号和展示分类编号获取分类信息
                PrdtAreaClass prdtAreaClass = prdtAreaClassMapper.selectClassByAreaIdAndAreaClassId(areaId, catgoryId);
                if (ToolUtil.isEmpty(prdtAreaClass)) {
                    throw new ServiceException(
                            StringUtils.format("上架三级展示分类【{}】不在上架区域编号【{}】中！请先修改数据再导入", catgoryId, areaId)
                    );
                }
            });
        } catch (Exception e) {
            return "校验未通过：" + e.getMessage();
        }
        // 重新组装数据
        List<PrdtAreaItem> areaItemList = batchShelfVOToPrdtAreaItem(batchShelfVO);

        areaItemList.stream().forEach(item -> {
            // 根据sku找到spu再找到入驻商
            SkuDTO skuDTO = productCacheService.getSkuDTO(item.getSkuId());
            SpuDTO spuDTO = productCacheService.getSpuDTO(skuDTO.getSpuId());
            if (ToolUtil.isNotEmpty(spuDTO)) {
                item.setSpuId(spuDTO.getSpuId());
                item.setSupplierId(spuDTO.getSupplierId());
            }
        });

        // 上架前进行校验
        prdtAreaItemService.releaseBeforeValidate(areaItemList);

        if (ToolUtil.isEmpty(areaItemList)){
            throw new ServiceException("该商品没有上架记录，请先手动上架");
        }

        for (PrdtAreaItem item : areaItemList) {
            try {
                prdtAreaItemZipService.insertPrdtAreaItemZip(item);
                // 校验
                PrdtAreaItem checkItem = prdtAreaItemMapper.selectAreaItemByCheck(item);
                if (Objects.isNull(checkItem)) {
                    checkItem = item;
                    //获取当前城市商品类型的最大排序号
                    Integer sortNum = prdtAreaItemMapper.selectMaxSort(item);
                    checkItem.setSortNum(sortNum + Code.GREATER_THAN);
                }
                PrdtSpu prdtSpu = prdtSpuMapper.selectById(item.getSpuId());
                // 调整上架状态
                checkItem.setShelfDate(DateUtils.getNowDate());
                checkItem.setAreaClassId(item.getAreaClassId());
                // 小单位一定有

                checkItem.setMinShelfStatus(item.getMinShelfStatus());
                // 中单位和大单位不一定有
                if (ToolUtil.isNotEmpty(item.getMidShelfStatus())) {
                    // 查询商品是否有对应的单位
                    if (ToolUtil.isEmpty(prdtSpu.getMidUnit()) && Objects.equals(item.getMidShelfStatus(), PRDT_SHELF_STATUS_1)) {
                        failureNum ++;
                        failureMsg.append(StringUtils.format("<br/>{}商品没有中单位无法上下架到{}", prdtSpu.getSpuName(),productCacheService.getAreaDto(checkItem.getAreaId()).getAreaName()));
                    }else {
                        checkItem.setMidShelfStatus(item.getMidShelfStatus());
                    }
                }
                if (ToolUtil.isNotEmpty(item.getLargeShelfStatus())) {
                    // 查询商品是否有对应的单位
                    if (ToolUtil.isEmpty(prdtSpu.getLargeUnit()) && Objects.equals(item.getLargeShelfStatus(), PRDT_SHELF_STATUS_1)) {
                        failureNum ++;
                        failureMsg.append(StringUtils.format("<br/>{}商品没有大单位无法上下架到{}", prdtSpu.getSpuName(),productCacheService.getAreaDto(checkItem.getAreaId()).getAreaName()));
                    }else {
                        checkItem.setLargeShelfStatus(item.getLargeShelfStatus());
                    }
                }

                if (Objects.equals(item.getMinShelfStatus(), PRDT_SHELF_STATUS_0) && Objects.equals(item.getMidShelfStatus(), PRDT_SHELF_STATUS_0) && Objects.equals(item.getLargeShelfStatus(), PRDT_SHELF_STATUS_0)) {
                    checkItem.setShelfStatus(0);
                } else {
                    checkItem.setShelfStatus(1);
                }
                prdtAreaItemMapper.insertOrUpdate(checkItem);
                // 在这里，无论更新还是插入成功，都把item添加到新的列表
            } catch (Exception e) {
                failureNum ++;
                failureMsg.append(StringUtils.format("上架失败：", e.getMessage()));
            }
        }
        if (failureNum>0){
            return failureMsg.toString();
        }else {
            return "";
        }
    }

    /**
     * 重新组装数据
     *
     * @param batchShelfVO
     * @return
     */
    private List<PrdtAreaItem> batchShelfVOToPrdtAreaItem(PrdtBatchShelfVO batchShelfVO) {
        List<Long> spu = batchShelfVO.getShelfSpuList(); // 勾选上架的spu
        ArrayList<PrdtAreaItem> prdtAreaItems = new ArrayList<>();
        for (Long spuId : spu) {
            List<PrdtSku> prdtSkus = prdtSkuMapper.selectBySpuId(spuId); // spu对应的sku集合
            if (ToolUtil.isNotEmpty(prdtSkus)) {
                for (PrdtSku skus : prdtSkus) {
                    List<PrdtAreaIdCatgoryId> shelfAreaIdCatgoryIdList = batchShelfVO.getShelfAreaIdCatgoryIdList();
                    for (PrdtAreaIdCatgoryId prdtAreaIdCatgoryId : shelfAreaIdCatgoryIdList) {
                        PrdtAreaItem prdtAreaItem = new PrdtAreaItem();
                        prdtAreaItem.setSkuId(skus.getSkuId());
                        prdtAreaItem.setAreaId(prdtAreaIdCatgoryId.getAreaId());
                        prdtAreaItem.setAreaClassId(prdtAreaIdCatgoryId.getCatgoryId());
                        if (batchShelfVO.getMinShelfStatus() == 1) {
                            prdtAreaItem.setMinShelfStatus(1);
                        }
                        if (batchShelfVO.getMidShelfStatus() == 1) {
                            prdtAreaItem.setMidShelfStatus(1);
                        }
                        if (batchShelfVO.getLargeShelfStatus() == 1) {
                            prdtAreaItem.setLargeShelfStatus(1);
                        }
                        prdtAreaItems.add(prdtAreaItem);
                    }
                }
            }
        }

        return prdtAreaItems;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PrdtAreaItem>  batchDownShelf(PrdtBatchShelfVO batchShelfVO) {
        //校验入参
        List<Long> shelfSpuList = batchShelfVO.getShelfSpuList();
        List<PrdtAreaItem> areaItemList = prdtAreaItemMapper.getAreaItemSpuIdList(shelfSpuList);
        List<Long> areaItemIds = areaItemList.stream().map(item -> item.getAreaItemId()).collect(Collectors.toList());

        //获取上下架商品信息
        List<PrdtAreaItem> itemList = prdtAreaItemMapper.selectAreaItemListById(ArrayUtils.toArray(areaItemIds));
        //上架时校验：如果存在已停用的商品 不允许批量上架
        //校验该次修改上架状态是否是改为上架
        // 循环判断上架
        itemList.stream().forEach(item -> {
            // 判断是否存在单位再操作
            SpuDTO spuDTO = productCacheService.getSpuDTO(item.getSpuId());
            Integer updateMinShelfStatus = ToolUtil.isNotEmpty(spuDTO.getMinUnit())? 0 : null;
            Integer updateMidShelfStatus = ToolUtil.isNotEmpty(spuDTO.getMidUnit())? 0 : null;
            Integer updateLargeShelfStatus = ToolUtil.isNotEmpty(spuDTO.getLargeUnit())? 0 : null;
            Integer updateShelfStatus = 0;
            prdtAreaItemMapper.updatePrdtAreaItemShelfStatus(new Long[]{item.getAreaItemId()}, updateMinShelfStatus, updateMidShelfStatus, updateLargeShelfStatus, updateShelfStatus);
        });
        return itemList;
    }

    /**
     * 修改上下架状态
     */
    @Override
    public List<PrdtAreaItem> updateShelfStatus(Long skuId, Integer minShelfStatus, Integer midShelfStatus, Integer largeShelfStatus) {
        List<PrdtAreaItem> areaItemList = prdtAreaItemMapper.getBySkuId(skuId);
        List<Long> areaItemIds = areaItemList.stream().map(item -> item.getAreaItemId()).collect(Collectors.toList());
        if (ToolUtil.isEmpty(areaItemIds)){
            throw new ServiceException("该商品没有上架记录，请先手动上架");
        }
        List<PrdtAreaItem> prdtAreaItems = prdtAreaItemService.updatePrdtAreaItemShelfStatus(ArrayUtils.toArray(areaItemIds), minShelfStatus, midShelfStatus, largeShelfStatus);
        return prdtAreaItems;
    }

    /**
     * 批量上架平台商品
     * @param batchShelfVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchSupplierShelf(PrdtBatchShelfVO batchShelfVO) {
        StringBuilder failureMsg = new StringBuilder();
        int failureNum = 0;
        try {
            List<Long> shelfSpuList = batchShelfVO.getShelfSpuList();
            if (ToolUtil.isEmpty(shelfSpuList)) {
                throw new ServiceException("请选择上架的商品");
            }
            // 数据校验
            if (ToolUtil.isEmpty(batchShelfVO.getMinShelfStatus())) {
                throw new ServiceException("请选择上架单位");
            }
            batchShelfVO.getShelfAreaIdCatgoryIdList().forEach(areaCatogry -> {
                Long catgoryId = areaCatogry.getCatgoryId();
                // 校验类别是否存在
                if(catgoryId == null){
                    throw exception(PRDT_SALE_CLASS_IS_NULL);
                }
                PrdtSaleClass prdtSaleClass = prdtSaleClassMapper.selectById(catgoryId);
                if(ToolUtil.isEmpty(prdtSaleClass)){
                    throw exception(PRDT_SALE_CLASS_NOT_EXISTS);
                }
                if(prdtSaleClass.getLevel() != INT_THREE){
                    throw exception(PRDT_SALE_CLASS_LEVEL_NOT_MAKE);
                }
            });
        } catch (Exception e) {
            return "校验未通过：" + e.getMessage();
        }
        // 重新组装数据
        List<PrdtSupplierItem> supplierItemList = batchShelfVOToPrdSupplierItem(batchShelfVO);

        supplierItemList.stream().forEach(item -> {
            // 根据sku找到spu再找到入驻商
            SkuDTO skuDTO = productCacheService.getSkuDTO(item.getSkuId());
            SpuDTO spuDTO = productCacheService.getSpuDTO(skuDTO.getSpuId());
            if (ToolUtil.isNotEmpty(spuDTO)) {
                item.setSpuId(spuDTO.getSpuId());
                item.setSupplierId(spuDTO.getSupplierId());
            }
        });
        prdtSupplierItemService.releaseBeforeValidate(supplierItemList);
        // 上架前进行校验

            for (PrdtSupplierItem item : supplierItemList) {
                try {
                    //校验
                    PrdtSupplierItem checkItem = prdtSupplierItemMapper.selectSupplierItemByCheck(item);
                    PrdtSpu prdtSpu = prdtSpuMapper.selectById(item.getSpuId());
                    if (Objects.isNull(checkItem)) {
                        checkItem = item;
                        //获取当前城市商品类型的最大排序号
                        Integer sortNum = prdtSupplierItemMapper.selectMaxSort(item);
                        checkItem.setSortNum(sortNum + Code.GREATER_THAN);
                    }
                    // 调整上架状态
                    checkItem.setShelfDate(DateUtils.getNowDate());
                    checkItem.setSaleClassId(item.getSaleClassId());
                    // 小单位一定有
                    checkItem.setMinShelfStatus(item.getMinShelfStatus());
                    // 中单位和大单位不一定有
                    if (ToolUtil.isNotEmpty(item.getMidShelfStatus())) {
                        // 查询商品是否有对应的单位
                        if (ToolUtil.isEmpty(prdtSpu.getMidUnit()) && Objects.equals(item.getMidShelfStatus(), PRDT_SHELF_STATUS_1)) {
                            failureNum ++;
                            failureMsg.append(StringUtils.format("<br/>{}商品没有中单位无法上下架", prdtSpu.getSpuName()));
                        }else {
                            checkItem.setMidShelfStatus(item.getMidShelfStatus());
                        }
                    }
                    if (ToolUtil.isNotEmpty(item.getLargeShelfStatus())) {
                        // 查询商品是否有对应的单位
                        if (ToolUtil.isEmpty(prdtSpu.getLargeUnit()) && Objects.equals(item.getLargeShelfStatus(), PRDT_SHELF_STATUS_1)) {
                            failureNum ++;
                            failureMsg.append(StringUtils.format("<br/>{}商品没有大单位无法上下架", prdtSpu.getSpuName()));
                        }else {
                            checkItem.setLargeShelfStatus(item.getLargeShelfStatus());
                        }
                    }
                    if (Objects.equals(item.getMinShelfStatus(), PRDT_SHELF_STATUS_0) && Objects.equals(item.getMidShelfStatus(), PRDT_SHELF_STATUS_0) && Objects.equals(item.getLargeShelfStatus(), PRDT_SHELF_STATUS_0)) {
                        checkItem.setShelfStatus(0);
                    } else {
                        checkItem.setShelfStatus(1);
                    }
                    prdtSupplierItemMapper.insertOrUpdate(checkItem);
                    prdtSupplierItemZipService.insertPrdtSupplierItemZip(item);

                } catch (Exception e) {
                    failureNum ++;
                    failureMsg.append(StringUtils.format("上架失败：", e.getMessage()));
                }
            }
        if (failureNum>0){
            return failureMsg.toString();
        }else {
            return "";
        }
    }

    private List<PrdtSupplierItem> batchShelfVOToPrdSupplierItem(PrdtBatchShelfVO batchShelfVO) {
        List<Long> spu = batchShelfVO.getShelfSpuList(); // 勾选上架的spu
        ArrayList<PrdtSupplierItem> prdtSupplierItems = new ArrayList<>();
        for (Long spuId : spu) {
            List<PrdtSku> prdtSkus = prdtSkuMapper.selectBySpuId(spuId); // spu对应的sku集合
            if (ToolUtil.isNotEmpty(prdtSkus)) {
                for (PrdtSku skus : prdtSkus) {
                    List<PrdtAreaIdCatgoryId> shelfAreaIdCatgoryIdList = batchShelfVO.getShelfAreaIdCatgoryIdList();
                    for (PrdtAreaIdCatgoryId prdtAreaIdCatgoryId : shelfAreaIdCatgoryIdList) {
                        PrdtSupplierItem prdtSupplierItem = new PrdtSupplierItem();
                        prdtSupplierItem.setSkuId(skus.getSkuId());
                        prdtSupplierItem.setSaleClassId(prdtAreaIdCatgoryId.getCatgoryId());
                        if (batchShelfVO.getMinShelfStatus() == 1) {
                            prdtSupplierItem.setMinShelfStatus(1);
                        }
                        if (batchShelfVO.getMidShelfStatus() == 1) {
                            prdtSupplierItem.setMidShelfStatus(1);
                        }
                        if (batchShelfVO.getLargeShelfStatus() == 1) {
                            prdtSupplierItem.setLargeShelfStatus(1);
                        }
                        prdtSupplierItems.add(prdtSupplierItem);
                    }
                }
            }
        }
        return prdtSupplierItems;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PrdtSupplierItem> batchSupplierDownShelf(PrdtBatchShelfVO batchShelfVO) {
        //校验入参
        List<Long> shelfSpuList = batchShelfVO.getShelfSpuList();
        List<PrdtSupplierItem> prdtSupplierItems = prdtSupplierItemMapper.getSupplierItemBySpuList(shelfSpuList);
        List<Long> supplierItemIds = prdtSupplierItems.stream().map(item -> item.getSupplierItemId()).collect(Collectors.toList());

        //获取上下架商品信息
        List<PrdtSupplierItem> prdtSupplierItemList = prdtSupplierItemMapper.selectSupplierItemListById(ArrayUtils.toArray(supplierItemIds));
        //上架时校验：如果存在已停用的商品 不允许批量上架
        //校验该次修改上架状态是否是改为上架
        // 循环判断上架
        prdtSupplierItemList.stream().forEach(item -> {
            // 判断是否存在单位再操作
            SpuDTO spuDTO = productCacheService.getSpuDTO(item.getSpuId());
            Integer updateMinShelfStatus = ToolUtil.isNotEmpty(spuDTO.getMinUnit())? 0 : null;
            Integer updateMidShelfStatus = ToolUtil.isNotEmpty(spuDTO.getMidUnit())? 0 : null;
            Integer updateLargeShelfStatus = ToolUtil.isNotEmpty(spuDTO.getLargeUnit())? 0 : null;
            Integer updateShelfStatus = 0;
            prdtSupplierItemMapper.updatePrdtSupplierItemShelfStatus(new Long[]{item.getSupplierItemId()}, updateMinShelfStatus, updateMidShelfStatus, updateLargeShelfStatus, updateShelfStatus);
        });
        return prdtSupplierItemList;
    }

    @Override
    public List<PrdtSupplierItem> updateSupplierShelfStatus(Long skuId, Integer minShelfStatus, Integer midShelfStatus, Integer largeShelfStatus) {
        List<PrdtSupplierItem> prdtSupplierItems = prdtSupplierItemMapper.getBySkuId(skuId);
        prdtSupplierItemService.releaseBeforeValidate(prdtSupplierItems);
        if (ToolUtil.isEmpty(prdtSupplierItems)){
            throw new ServiceException("该商品没有上架记录，请先手动上架");
        }

        prdtSupplierItems.forEach(item ->{
            //校验
            PrdtSupplierItem checkItem = prdtSupplierItemMapper.selectSupplierItemByCheck(item);
            PrdtSpu prdtSpu = prdtSpuMapper.selectById(item.getSpuId());
            if (Objects.isNull(checkItem)) {
                checkItem = item;
                //获取当前城市商品类型的最大排序号
                Integer sortNum = prdtSupplierItemMapper.selectMaxSort(item);
                checkItem.setSortNum(sortNum + Code.GREATER_THAN);
            }
            // 调整上架状态
            checkItem.setShelfDate(DateUtils.getNowDate());
            checkItem.setSaleClassId(item.getSaleClassId());
            // 小单位一定有
            if (minShelfStatus !=-1){
                checkItem.setMinShelfStatus(minShelfStatus);
            }
            // 中单位和大单位不一定有
            if (midShelfStatus!=-1) {
                // 查询商品是否有对应的单位
                if (ToolUtil.isEmpty(prdtSpu.getMidUnit()) && Objects.equals(item.getMidShelfStatus(), PRDT_SHELF_STATUS_1)) {
                    throw new ServiceException(StringUtils.format("<br/>{}商品没有中单位无法上下架", prdtSpu.getSpuName()));
                }else {
                    checkItem.setMidShelfStatus(midShelfStatus);
                }
            }
            if (largeShelfStatus !=-1) {
                // 查询商品是否有对应的单位
                if (ToolUtil.isEmpty(prdtSpu.getLargeUnit()) && Objects.equals(item.getLargeShelfStatus(), PRDT_SHELF_STATUS_1)) {
                    throw new ServiceException(StringUtils.format("<br/>{}商品没有大单位无法上下架", prdtSpu.getSpuName()));
                }else {
                    checkItem.setLargeShelfStatus(largeShelfStatus);
                }
            }
            if (Objects.equals(checkItem.getMinShelfStatus(), PRDT_SHELF_STATUS_0) && Objects.equals(checkItem.getMidShelfStatus(), PRDT_SHELF_STATUS_0) && Objects.equals(checkItem.getLargeShelfStatus(), PRDT_SHELF_STATUS_0)) {
                checkItem.setShelfStatus(0);
            } else {
                checkItem.setShelfStatus(1);
            }

            prdtSupplierItemMapper.insertOrUpdate(checkItem);
            prdtSupplierItemZipService.insertPrdtSupplierItemZip(item);
        });
        return prdtSupplierItems;
    }

    @Override
    public String chefkBatchDownShelf(List<Long> shelfSpuList) {
        List<PrdtSku> prdtSkus = prdtSkuMapper.getskuListBySpuIdList(shelfSpuList);
        List<Long> skuIds = prdtSkus.stream().map(prdtSku -> prdtSku.getSkuId()).collect(Collectors.toList());
        StringBuilder stringBuilder = new StringBuilder();
        for (Long skuId : skuIds) {
            stringBuilder.append(activityApi.checkActivityByItem( new Long[]{skuId}).getCheckedData());

        }
        return stringBuilder.toString();
    }

    private void checkRetail(SpuOpenDTO spuOpendto) {
        // 检查零售价
        if (spuOpendto != null) {
            BigDecimal retailPrice = spuOpendto.getRetailPrice();
            BigDecimal midRetailPrice = spuOpendto.getMidRetailPrice();
            BigDecimal largeRetailPrice = spuOpendto.getLargeRetailPrice();
            if ((retailPrice != null && retailPrice.compareTo(BigDecimal.ZERO) > 0) ||
                (midRetailPrice != null && midRetailPrice.compareTo(BigDecimal.ZERO) > 0) ||
                (largeRetailPrice != null && largeRetailPrice.compareTo(BigDecimal.ZERO) > 0)) {
                spuOpendto.setSaleType(0);
            }
        }
        
        if (Objects.nonNull(spuOpendto.getSaleType()) && spuOpendto.getSaleType() == 0) {
            // 检查 markPrice
            if (Objects.nonNull(spuOpendto.getMarkPrice())) {
                if (Objects.isNull(spuOpendto.getRetailPrice()) || spuOpendto.getRetailPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException(500, "设置了标价时，零售价不能为空或小于等于0");
                }
                if (Objects.equals(spuOpendto.getRetailProfitMode(), 3) && (Objects.isNull(spuOpendto.getProfitAmount()) || spuOpendto.getProfitAmount().compareTo(BigDecimal.ZERO) <= 0)) {
                    throw new ServiceException(500, "设置了标价且利润模式为按金额时，利润分润不能为空或小于等于0");
                }
            }
            // 检查 midMarkPrice
            if (Objects.nonNull(spuOpendto.getMidMarkPrice())) {
                if (Objects.isNull(spuOpendto.getMidRetailPrice()) || spuOpendto.getMidRetailPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException(500, "设置了中单位标价时，中单位零售价不能为空或小于等于0");
                }
                if (Objects.equals(spuOpendto.getRetailProfitMode(), 3) && (Objects.isNull(spuOpendto.getMidProfitAmount()) || spuOpendto.getMidProfitAmount().compareTo(BigDecimal.ZERO) <= 0)) {
                    throw new ServiceException(500, "设置了中单位标价且利润模式为按金额时，中单位分润金额不能为空或小于等于0");
                }
            }
            // 检查 largeMarkPrice
            if (Objects.nonNull(spuOpendto.getLargeMarkPrice())) {
                if (Objects.isNull(spuOpendto.getLargeRetailPrice()) || spuOpendto.getLargeRetailPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException(500, "设置了大单位标价时，大单位零售价不能为空或小于等于0");
                }
                if (Objects.equals(spuOpendto.getRetailProfitMode(), 3) && (Objects.isNull(spuOpendto.getLargeProfitAmount()) || spuOpendto.getLargeProfitAmount().compareTo(BigDecimal.ZERO) <= 0)) {
                    throw new ServiceException(500, "设置了大单位标价且利润模式为按金额时，大单位分润金额不能为空或小于等于0");
                }
            }
        }
    }
}
