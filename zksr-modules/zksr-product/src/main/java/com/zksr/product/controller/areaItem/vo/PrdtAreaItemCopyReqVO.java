package com.zksr.product.controller.areaItem.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 城市上架商品对象 prdt_area_item
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Data
@ApiModel("城市上架商品 - prdt_area_item分页 Request VO")
@NoArgsConstructor
@AllArgsConstructor
public class PrdtAreaItemCopyReqVO{
    private static final long serialVersionUID = 1L;

    /** 复制类型 */
    @ApiModelProperty(value = "复制类型 0 上架商品多选复制  1 一键复制上架商品")
    private Integer copyType;

    /** 复制前 -- 城市id */
    @ApiModelProperty(value = "复制前(一键复制上架商品) -- 城市id")
    private Long copyAreaId;

    /** 复制前 -- 城市展示分类id */
    @ApiModelProperty(value = "复制前(一键复制上架商品) -- 城市展示分类id")
    private Long copyAreaClassId;

    /** 复制前 -- 城市上架商品id集合 */
    @ApiModelProperty(value = "复制前(上架商品多选复制) -- 城市上架商品id集合")
    private List<Long> copyAreaItemIds;

    /** 复制后 -- 城市id */
    @Excel(name = "复制后 -- 目标城市id")
    @ApiModelProperty(value = "复制后 -- 目标城市id")
    private Long targetAreaId;

    /** 复制后 -- 目标城市展示分类id */
    @Excel(name = "复制后 -- 目标城市展示分类id")
    @ApiModelProperty(value = "复制后 -- 目标城市展示分类id")
    private Long targetAreaClassId;

}
