package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 城市上架商品对象 prdt_area_item
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@TableName(value = "prdt_area_item")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtAreaItem extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 城市上架商品id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long areaItemId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 城市展示分类id */
    @Excel(name = "城市展示分类id")
    private Long areaClassId;

    /** 上下架状态 */
    @Excel(name = "上下架状态")
    private Integer shelfStatus;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    private Long spuId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    private Long skuId;

    /** 排序序号 */
    @Excel(name = "排序序号")
    private Integer sortNum;

    /** 上架时间 */
    @Excel(name = "上架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelfDate; 		 // 上架时间

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 小单位-上下架状态 */
    @Excel(name = "小单位-上下架状态")
    private Integer minShelfStatus;

    /** 中单位-上下架状态 */
    @Excel(name = "中单位-上下架状态")
    private Integer midShelfStatus;

    /** 大单位-上下架状态 */
    @Excel(name = "大单位-上下架状态")
    private Integer largeShelfStatus;

    /** 0-普通商品, 1-组合商品 */
    @Excel(name = "0-普通商品, 1-组合商品")
    private Integer itemType = NumberPool.INT_ZERO;

    /** 活动开始时间, 活动商品 */
    @Excel(name = "活动开始时间, 活动商品")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityStartTime;

    /** 活动结束时间, 活动商品 */
    @Excel(name = "活动结束时间, 活动商品")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityEndTime;

    /** 组合商品ID */
    @Excel(name = "组合商品ID")
    private Long spuCombineId;

    /** 活动ID */
    @Excel(name = "活动ID")
    private Long activityId;

}
