package com.zksr.product.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.product.api.brand.BrandApi;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.controller.blockScheme.vo.*;
import com.zksr.product.convert.blockScheme.PrdtBlockBranchConvert;
import com.zksr.product.convert.blockScheme.PrdtBlockSkuConvert;
import com.zksr.product.domain.PrdtBlockBranch;
import com.zksr.product.domain.PrdtBlockSku;
import com.zksr.product.mapper.PrdtBlockBranchMapper;
import com.zksr.product.mapper.PrdtBlockSkuMapper;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtBlockSchemeMapper;
import com.zksr.product.convert.blockScheme.PrdtBlockSchemeConvert;
import com.zksr.product.domain.PrdtBlockScheme;
import com.zksr.product.service.IPrdtBlockSchemeService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 经营屏蔽方案Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Service
public class PrdtBlockSchemeServiceImpl implements IPrdtBlockSchemeService {
    @Autowired
    private PrdtBlockSchemeMapper prdtBlockSchemeMapper;
    @Autowired
    private PrdtBlockBranchMapper prdtBlockBranchMapper;
    @Autowired
    private PrdtBlockSkuMapper prdtBlockSkuMapper;
    @Autowired
    private BranchApi branchApi;
    @Autowired
    private SkuApi skuApi;
    @Autowired
    private SpuApi spuApi;
    @Autowired
    private BrandApi brandApi;
    @Autowired
    private RedisService redisService;
    @Autowired
    private SupplierApi supplierApi;

    @Resource
    private DcApi dcApi;

    /**
     * 新增经营屏蔽方案
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertPrdtBlockScheme(PrdtBlockSchemeSaveReqVO createReqVO) {
        // 校验入驻商
        this.validateSupplier(createReqVO.getSupplierId());
        // 校验门店
        this.validateBranch(createReqVO.getAreaId(), createReqVO.getBranchIds());
        // 校验sku
        this.validateSku(createReqVO.getSupplierId(), createReqVO.getSkuIds());
        // 方案名称重复校验
        this.validateSchemeNameDuplicate(createReqVO.getSchemeName(), null);

        String schemeNo = this.createSchemeNo();

        // 插入方案
        PrdtBlockScheme prdtBlockScheme = PrdtBlockSchemeConvert.INSTANCE.convertForInsert(createReqVO);
        prdtBlockScheme.setSchemeNo(schemeNo);
        prdtBlockSchemeMapper.insert(prdtBlockScheme);

        // 插入方案指定客户
        List<PrdtBlockBranch> prdtBlockBranches = createReqVO.getBranchIds().stream().distinct()
                .map(branchId -> PrdtBlockBranchConvert.INSTANCE.convert(schemeNo, branchId))
                .collect(Collectors.toList());
        prdtBlockBranchMapper.insertBatch(prdtBlockBranches);

        // 插入方案指定商品
        List<PrdtBlockSku> prdtBlockSkus = createReqVO.getSkuIds().stream().distinct()
                .map(skuId -> PrdtBlockSkuConvert.INSTANCE.convert(schemeNo, skuId))
                .collect(Collectors.toList());
        prdtBlockSkuMapper.insertBatch(prdtBlockSkus);
        return prdtBlockScheme.getBlockSchemeId();
    }

    /**
     * 修改经营屏蔽方案
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrdtBlockScheme(PrdtBlockSchemeSaveReqVO updateReqVO) {
        // 校验入驻商
        this.validateSupplier(updateReqVO.getSupplierId());
        // 校验门店
        this.validateBranch(updateReqVO.getAreaId(), updateReqVO.getBranchIds());
        // 校验sku
        this.validateSku(updateReqVO.getSupplierId(), updateReqVO.getSkuIds());
        // 方案名称重复校验
        this.validateSchemeNameDuplicate(updateReqVO.getSchemeName(), updateReqVO.getSchemeNo());

        PrdtBlockScheme prdtBlockScheme = prdtBlockSchemeMapper.selectById(updateReqVO.getBlockSchemeId());
        if (StatusConstants.STATE_ENABLE.equals(prdtBlockScheme.getStatus())) {
            throw new ServiceException("只能在关闭状态下进行编辑");
        }
        String schemeNo = prdtBlockScheme.getSchemeNo();

        // 更新方案信息
        prdtBlockSchemeMapper.updateById(PrdtBlockSchemeConvert.INSTANCE.convertForUpdate(updateReqVO));

        // 更新方案指定的客户
        this.updateBlockBranch(schemeNo, updateReqVO.getBranchIds());

        // 更新方案指定的商品
        this.updateBlockSku(schemeNo, updateReqVO.getSkuIds());
    }

    /**
     * 删除经营屏蔽方案
     *
     * @param blockSchemeId 主键
     */
    @Override
    public void deletePrdtBlockScheme(Long blockSchemeId) {
        // 删除
        prdtBlockSchemeMapper.deleteById(blockSchemeId);
    }

    /**
     * 批量删除经营屏蔽方案
     *
     * @param blockSchemeIds 需要删除的经营屏蔽方案主键
     * @return 结果
     */
    @Override
    public void deletePrdtBlockSchemeByBlockSchemeIds(List<Long> blockSchemeIds) {
        if (CollectionUtils.isEmpty(blockSchemeIds)) {
            return;
        }
        boolean anyEnable = prdtBlockSchemeMapper.exists(Wrappers.lambdaQuery(PrdtBlockScheme.class)
                .in(PrdtBlockScheme::getBlockSchemeId, blockSchemeIds)
                .eq(PrdtBlockScheme::getStatus, StatusConstants.STATE_ENABLE));
        if (anyEnable) {
            throw new ServiceException("开启的方案不能删除");
        }
        prdtBlockSchemeMapper.update(null, Wrappers.lambdaUpdate(PrdtBlockScheme.class)
                .set(PrdtBlockScheme::getDelFlag, StatusConstants.FLAG_TRUE)
                .in(PrdtBlockScheme::getBlockSchemeId, blockSchemeIds));
    }

    /**
     * 获得经营屏蔽方案
     *
     * @param blockSchemeId 主键
     * @return 经营屏蔽方案
     */
    @Override
    public PrdtBlockSchemeRespVO getPrdtBlockScheme(Long blockSchemeId) {
        PrdtBlockScheme prdtBlockScheme = prdtBlockSchemeMapper.selectById(blockSchemeId);
        if (prdtBlockScheme == null) {
            return null;
        }
        PrdtBlockSchemeRespVO respVO = PrdtBlockSchemeConvert.INSTANCE.convert(prdtBlockScheme);

        // 门店信息
        List<Long> branchIds = prdtBlockBranchMapper.selectList(new LambdaQueryWrapperX<PrdtBlockBranch>()
                    .eq(PrdtBlockBranch::getSchemeNo, prdtBlockScheme.getSchemeNo()))
                .stream().map(PrdtBlockBranch::getBranchId).collect(Collectors.toList());

        Map<Long, BranchDTO> branchMap = branchApi.listByBranchIds(branchIds).getCheckedData();
        List<PrdtBlockBranchRespVO> branchList = branchIds.stream()
                .map(branchMap::get)
                .filter(Objects::nonNull)
                .map(PrdtBlockBranchConvert.INSTANCE::convert)
                .collect(Collectors.toList());
        respVO.setBranchList(branchList);

        // 商品信息
        List<Long> skuIds = prdtBlockSkuMapper.selectSkuIdsBySchemeNo(prdtBlockScheme.getSchemeNo());
        Map<Long, SkuDTO> skuMap = skuApi.listBySkuIds(skuIds).getCheckedData();
        List<Long> spuIds = skuMap.values().stream().map(SkuDTO::getSpuId).collect(Collectors.toList());
        Map<Long, SpuDTO> spuMap = spuApi.listBySpuIds(spuIds).getCheckedData();
        List<Long> brandIds = spuMap.values().stream().map(SpuDTO::getBrandId).collect(Collectors.toList());
        Map<Long, BrandDTO> brandMap = brandApi.listByBrandIds(brandIds).getCheckedData();

        List<PrdtBlockSkuRespVO> skuList = skuIds.stream().map(skuId -> {
            SkuDTO skuDTO = skuMap.get(skuId);
            SpuDTO spuDTO = skuDTO == null ? null : spuMap.get(skuDTO.getSpuId());
            BrandDTO brandDTO = spuDTO == null ? null : brandMap.get(spuDTO.getBrandId());
            return PrdtBlockSkuConvert.INSTANCE.convert(skuDTO, spuDTO, brandDTO);
        }).filter(Objects::nonNull).collect(Collectors.toList());
        respVO.setSkuList(skuList);
        return respVO;
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtBlockScheme> getPrdtBlockSchemePage(PrdtBlockSchemePageReqVO pageReqVO) {
        Long dcId = SecurityUtils.getLoginUser() == null ? null : SecurityUtils.getLoginUser().getDcId();
        List<Long> areaId = new ArrayList<>();
        if(null != dcId){
            areaId = dcApi.getDcAreaList(dcId).getCheckedData();
        }
        // 将areaId列表赋值到pageReqVO
        pageReqVO.setAreaId(areaId);
        Page<PrdtBlockSchemePageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<PrdtBlockScheme> pageResult = prdtBlockSchemeMapper.selectPage(page, pageReqVO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public void batchEnable(List<Long> blockSchemeIds) {
        if (CollectionUtils.isEmpty(blockSchemeIds)) {
            return;
        }
        boolean anyEnable = prdtBlockSchemeMapper.exists(Wrappers.lambdaQuery(PrdtBlockScheme.class)
                .in(PrdtBlockScheme::getBlockSchemeId, blockSchemeIds)
                .eq(PrdtBlockScheme::getStatus, StatusConstants.STATE_ENABLE));
        if (anyEnable) {
            throw new ServiceException("开启中的方案不能启用");
        }
        prdtBlockSchemeMapper.update(null, Wrappers.lambdaUpdate(PrdtBlockScheme.class)
                .set(PrdtBlockScheme::getStatus, StatusConstants.STATE_ENABLE)
                .in(PrdtBlockScheme::getBlockSchemeId, blockSchemeIds));
    }

    @Override
    public void batchDisable(List<Long> blockSchemeIds) {
        if (CollectionUtils.isEmpty(blockSchemeIds)) {
            return;
        }
        boolean anyDisable = prdtBlockSchemeMapper.exists(Wrappers.lambdaQuery(PrdtBlockScheme.class)
                .in(PrdtBlockScheme::getBlockSchemeId, blockSchemeIds)
                .eq(PrdtBlockScheme::getStatus, StatusConstants.STATE_DISABLE));
        if (anyDisable) {
            throw new ServiceException("关闭中的方案不能关闭");
        }
        prdtBlockSchemeMapper.update(null, Wrappers.lambdaUpdate(PrdtBlockScheme.class)
                .set(PrdtBlockScheme::getStatus, StatusConstants.STATE_DISABLE)
                .in(PrdtBlockScheme::getBlockSchemeId, blockSchemeIds));
    }

    @Override
    public List<Long> getBlockSkusByBranchId(Long branchId) {
        if (branchId == null) {
            return Lists.newArrayList();
        }
        List<PrdtBlockScheme> prdtBlockSchemes = prdtBlockBranchMapper.selectBlockSchemesByBranchId(branchId);
        // 当一个客户存在多个方案中的时候，只生效最近创建时间且开启的方案
        String schemeNo = prdtBlockSchemes.stream()
                .filter(prdtBlockScheme -> StatusConstants.STATE_ENABLE.equals(prdtBlockScheme.getStatus()))
                .max(Comparator.comparing(PrdtBlockScheme::getCreateTime))
                .map(PrdtBlockScheme::getSchemeNo)
                .orElse(null);
        return prdtBlockSkuMapper.selectSkuIdsBySchemeNo(schemeNo);
    }

    private String createSchemeNo() {
        String yyyyMM = DateUtils.dateTimeNow(DateUtils.YYYYMM);
        String redisKey = RedisConstants.BLOCK_SCHEME_NO_CREATE_KEY + yyyyMM;
        long seqNo = redisService.incrByCacheObject(redisKey);
        if (seqNo == 1L) {
            redisService.expire(redisKey, DateUtils.getRemainSecondThisMonth() + 3600L, TimeUnit.SECONDS);
        }
        return String.format("ZD%s%03d", yyyyMM, seqNo);
    }

    private void updateBlockBranch(String schemeNo, List<Long> branchIds) {
        // 方案已存在的客户
        List<PrdtBlockBranch> existedBlockBranches = prdtBlockBranchMapper.selectList(new LambdaQueryWrapperX<PrdtBlockBranch>()
                .eq(PrdtBlockBranch::getSchemeNo, schemeNo));

        // 需要删除的客户
        List<Long> deleteIds = existedBlockBranches.stream()
                .filter(existedBlockBranch -> branchIds.stream().noneMatch(branchId -> branchId.equals(existedBlockBranch.getBranchId())))
                .map(PrdtBlockBranch::getBlockBranchId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteIds)) {
            prdtBlockBranchMapper.deleteBatchIds(deleteIds);
        }

        // 需要新增的客户
        List<PrdtBlockBranch> insertList = branchIds.stream().distinct().filter(branchId -> existedBlockBranches.stream().noneMatch(existedBlockBranch -> branchId.equals(existedBlockBranch.getBranchId())))
                .map(branchId -> PrdtBlockBranchConvert.INSTANCE.convert(schemeNo, branchId))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList)) {
            prdtBlockBranchMapper.insertBatch(insertList);
        }
    }

    private void updateBlockSku(String schemeNo, List<Long> skuIds) {
        // 方案已存在的商品
        List<PrdtBlockSku> existedBlockSkus = prdtBlockSkuMapper.selectList(new LambdaQueryWrapperX<PrdtBlockSku>()
                .eq(PrdtBlockSku::getSchemeNo, schemeNo));

        // 需要删除的商品
        List<Long> deleteIds = existedBlockSkus.stream()
                .filter(existedBlockSku -> skuIds.stream().noneMatch(skuId -> skuId.equals(existedBlockSku.getSkuId())))
                .map(PrdtBlockSku::getBlockSkuId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteIds)) {
            prdtBlockSkuMapper.deleteBatchIds(deleteIds);
        }

        // 需要新增的商品
        List<PrdtBlockSku> insertList = skuIds.stream().distinct().filter(skuId -> existedBlockSkus.stream().noneMatch(existedBlockSku -> skuId.equals(existedBlockSku.getSkuId())))
                .map(skuId -> PrdtBlockSkuConvert.INSTANCE.convert(schemeNo, skuId))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList)) {
            prdtBlockSkuMapper.insertBatch(insertList);
        }
    }

    private void validateSupplier(Long supplierId) {
        SupplierDTO supplierDTO = supplierApi.getBySupplierId(supplierId).getCheckedData();
        if (supplierDTO == null) {
            throw new ServiceException("入驻商不存在");
        }
        if (supplierDTO.getStatus() != null && supplierDTO.getStatus().intValue() != StatusConstants.STATE_ENABLE) {
            throw new ServiceException("入驻商不在启用状态");
        }
    }

    private void validateBranch(Long areaId, List<Long> branchIds) {
        Map<Long, BranchDTO> branchMap = branchApi.listByBranchIds(branchIds).getCheckedData();
        branchIds.forEach(branchId -> {
            BranchDTO branchDTO = branchMap.get(branchId);
            if (branchDTO == null || !StatusConstants.DEL_FLAG_0.equals(branchDTO.getDelFlag())) {
                throw new ServiceException(String.format("客户%s不存在", branchId));
            }
            if (!StatusConstants.STATE_ENABLE.equals(branchDTO.getStatus())) {
                throw new ServiceException(String.format("客户%s不在启用状态", branchDTO.getBranchName()));
            }
            if (!areaId.equals(branchDTO.getAreaId())) {
                throw new ServiceException(String.format("客户%s不属于选中的城市，请重试", branchDTO.getBranchName()));
            }
        });
    }

    private void validateSku(Long supplierId, List<Long> skuIds) {
        Map<Long, SkuDTO> skuMap = skuApi.listBySkuIds(skuIds).getCheckedData();
        List<Long> spuIds = skuMap.values().stream().map(SkuDTO::getSpuId).collect(Collectors.toList());
        Map<Long, SpuDTO> spuMap = spuApi.listBySpuIds(spuIds).getCheckedData();
        skuIds.forEach(skuId -> {
            SkuDTO skuDTO = skuMap.get(skuId);
            SpuDTO spuDTO;
            if (skuDTO == null || (spuDTO = spuMap.get(skuDTO.getSpuId())) == null ||
                    (skuDTO.getIsDelete() != null && skuDTO.getIsDelete().intValue() != StatusConstants.DEL_FLAG_0) ||
                    (spuDTO.getIsDelete() != null && spuDTO.getIsDelete().intValue() != StatusConstants.DEL_FLAG_0)) {
                throw new ServiceException(String.format("商品%s不存在", skuId));
            }
            if ((skuDTO.getStatus() != null && skuDTO.getStatus().intValue() != StatusConstants.STATE_ENABLE) ||
                    (spuDTO.getStatus() != null && spuDTO.getStatus().intValue() != StatusConstants.STATE_ENABLE)) {
                throw new ServiceException(String.format("商品%s:%s不在启用状态", skuId, spuDTO.getSpuName()));
            }
            if (!supplierId.equals(spuDTO.getSupplierId())) {
                throw new ServiceException(String.format("商品%s:%s不属于选中的入驻商，请重试", skuId, spuDTO.getSpuName()));
            }
        });
    }

    private void validateSchemeNameDuplicate(String schemeName, String schemeNo) {
        List<PrdtBlockScheme> schemes = prdtBlockSchemeMapper.selectList(Wrappers.lambdaQuery(PrdtBlockScheme.class)
                .eq(PrdtBlockScheme::getSchemeName, schemeName)
                .eq(PrdtBlockScheme::getDelFlag, DelFlagConstants.NORMAL));
        if (StringUtils.isBlank(schemeNo)) { // 新增
            if (CollectionUtils.isNotEmpty(schemes)) {
                throw new ServiceException(String.format("方案名称%s已存在", schemeName));
            }
        } else { // 修改
            if (CollectionUtils.isNotEmpty(schemes)) {
                if (schemes.stream().noneMatch(scheme -> scheme.getSchemeNo().equals(schemeNo))) {
                    throw new ServiceException(String.format("方案名称%s已存在", schemeName));
                }
            }
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.product.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 经营屏蔽方案 TODO 补充编号 ==========
    // ErrorCode PRDT_BLOCK_SCHEME_NOT_EXISTS = new ErrorCode(TODO 补充编号, "经营屏蔽方案不存在");

}
