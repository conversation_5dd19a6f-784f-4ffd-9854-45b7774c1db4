package com.zksr.product.controller.areaItem;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.areaItem.excel.PrdtAreaItemImportExcel;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemExcelVO;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.api.spu.vo.PrdtSpuNotItemPageReqExportVo;
import com.zksr.product.controller.areaItem.vo.PrdtAreaItemCopyReqVO;
import com.zksr.product.controller.areaItem.vo.PrdtAreaItemPageReqVO;
import com.zksr.product.controller.areaItem.vo.PrdtAreaItemRespVO;
import com.zksr.product.controller.areaItem.vo.PrdtAreaItemSaveReqVO;
import com.zksr.product.domain.PrdtAreaItem;
import com.zksr.product.service.IPrdtAreaItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_SUPPLIER_ITEM_ID_NOT_NULL;

/**
 * 城市上架商品Controller
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Api(tags = "管理后台 - 城市上架商品接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/areaItem")
public class PrdtAreaItemController {
    @Autowired
    private IPrdtAreaItemService prdtAreaItemService;

    /**
     * 新增城市上架商品
     */
    @ApiOperation(value = "新增城市上架商品", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "城市上架商品", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Boolean> add(@Valid @RequestBody List<PrdtAreaItemSaveReqVO> createReqVO) {
        List<PrdtAreaItem> areaItems = prdtAreaItemService.insertPrdtAreaItem(createReqVO);
        // 事务之外的刷新缓存事件
        prdtAreaItemService.cacheEvent(areaItems);
        return success(true);
    }

    /**
     * 修改城市上架商品
     */
    @ApiOperation(value = "修改城市上架商品", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "城市上架商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtAreaItemSaveReqVO updateReqVO) {
        List<PrdtAreaItem> eventAreaItem = prdtAreaItemService.updatePrdtAreaItem(updateReqVO);
        // 事务之外的刷新缓存事件
        prdtAreaItemService.cacheEvent(eventAreaItem);
        return success(true);
    }

    /**
     * 批量上下架城市商品
     */
    @ApiOperation(value = "批量上下架城市商品", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + PrdtAreaItemController.Permissions.EDIT)
    @RequiresPermissions(PrdtAreaItemController.Permissions.EDIT)
    @Log(title = "批量上下架城市商品", businessType = BusinessType.UPDATE)
    @PutMapping("/updateBatchItemShelfStatus")
    public CommonResult<Boolean> updateBatchItemShelfStatus(@RequestParam("areaItemIds") Long[] areaItemIds,
                                                            @RequestParam(name = "minShelfStatus", required = false,defaultValue = "-1") Integer minShelfStatus,
                                                            @RequestParam(name = "midShelfStatus", required = false,defaultValue = "-1") Integer midShelfStatus,
                                                            @RequestParam(name = "largeShelfStatus", required = false,defaultValue = "-1") Integer largeShelfStatus) {
        List<PrdtAreaItem> areaItems = prdtAreaItemService.updatePrdtAreaItemShelfStatus(areaItemIds, minShelfStatus, midShelfStatus, largeShelfStatus);
        // 事务之外的刷新缓存事件
        prdtAreaItemService.cacheEvent(areaItems);
        return success(true);
    }

    /**
     * 删除城市上架商品
     */
    @ApiOperation(value = "删除城市上架商品", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "城市上架商品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{areaItemIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] areaItemIds) {
        //prdtAreaItemService.deletePrdtAreaItemByAreaItemIds(areaItemIds);
        return success(true);
    }

    /**
     * 获取城市上架商品详细信息
     */
    @ApiOperation(value = "获得城市上架商品详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{areaItemId}")
    public CommonResult<PrdtAreaItemRespVO> getInfo(@PathVariable("areaItemId") Long areaItemId) {
        return success(prdtAreaItemService.getPrdtAreaItem(areaItemId));
    }

    /**
     * 分页查询城市上架商品
     * !@商品 - !@城市上架商品 - 查询列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得城市上架商品分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(supplierAlias = "`spu`")
    public CommonResult<PageResult<PrdtAreaItemPageRespVO>> getPage(@Valid PrdtAreaItemPageReqVO pageReqVO) {
        pageReqVO.setItemType(0);//只查询普通商品
        return success(prdtAreaItemService.getPrdtAreaItemPage(pageReqVO));
    }

    /**
     * 分页查询城市上架商品
     */
    @PostMapping("/lists")
    @ApiOperation(value = "获得城市上架商品分页列表(多入驻商)", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(supplierAlias = "`spu`")
    public CommonResult<PageResult<PrdtAreaItemPageRespVO>> getPageList(@Valid @RequestBody PrdtAreaItemPageReqVO pageReqVO) {
        pageReqVO.setItemType(0);//只查询普通商品
        pageReqVO.setShelfStatus(NumberPool.INT_ONE);//查询上架商品
        return success(prdtAreaItemService.getPrdtAreaItemPage(pageReqVO));
    }

    @PostMapping("/getPrdtAreaItemExport")
    @ApiOperation(value = "上架商品导出", httpMethod = HttpMethod.POST)
    public void getPrdtAreaItemExport(HttpServletResponse response, PrdtAreaItemPageReqVO pageReqVO) throws IOException {
        List<PrdtSpuNotItemPageReqExportVo> prdtAreaItemExport = prdtAreaItemService.getPrdtAreaItemExport(pageReqVO);

        String path = "template" + File.separator+ "商品上架导入-模板.xlsx";
        // 直接获取流
        ClassPathResource resource = new ClassPathResource(path);
        InputStream inputStream = resource.getInputStream();
        Workbook sheets = new XSSFWorkbook(inputStream);

        ExcelUtil<PrdtSpuNotItemPageReqExportVo> util = new ExcelUtil<>(PrdtSpuNotItemPageReqExportVo.class);
        util.exportExcel(response, prdtAreaItemExport, "商品上架导入-模板",sheets,1);
    }

    @PostMapping("/exportAreaItem")
    @ApiOperation(value = "导出上架商品", httpMethod = HttpMethod.POST)
    public void exportAreaItem(@RequestBody PrdtAreaItemPageReqVO pageReqVO, HttpServletResponse response) {
        pageReqVO.setItemType(0);//只查询普通商品
        List<PrdtAreaItemExcelVO> list = prdtAreaItemService.getAreaItemExportList(pageReqVO);
        ExcelUtil<PrdtAreaItemExcelVO> util = new ExcelUtil<>(PrdtAreaItemExcelVO.class);
        util.exportExcel(response, list, "城市上架商品");
    }


    @ApiOperation(value = "批量修改城市上架商品展示分类", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "批量修改城市上架商品展示分类", businessType = BusinessType.UPDATE)
    @PutMapping("/batchEditItemAreaClassId")
    public CommonResult<Boolean> batchEditItemAreaClassId(@Valid @RequestBody PrdtAreaItemSaveReqVO updateReqVO) {
        // 验证必填参数
        if(ToolUtil.isEmpty(updateReqVO.getAreaItemIds())) {
            throw exception(PRDT_SUPPLIER_ITEM_ID_NOT_NULL);
        }
        List<PrdtAreaItem> eventAreaItem = prdtAreaItemService.batchEditItemAreaClassId(updateReqVO);
        // 事务之外的刷新缓存事件
        prdtAreaItemService.cacheEvent(eventAreaItem);
        return success(true);
    }

    @ApiOperation(value = "一键复制城市上架商品信息", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "一键复制城市上架商品信息", businessType = BusinessType.INSERT)
    @PostMapping("/copyAreaItem")
    public CommonResult<Boolean> copyAreaItem(@Valid @RequestBody PrdtAreaItemCopyReqVO vo) {
        List<PrdtAreaItem> eventAreaItem = prdtAreaItemService.copyAreaItem(vo);
        // 事务之外的刷新缓存事件
        prdtAreaItemService.cacheEvent(eventAreaItem);
        return success(true);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "获取上架导入模版", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException {
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        if (ToolUtil.isNotEmpty(dcId)){
            ExcelUtil<PrdtAreaItemImportExcel> util = new ExcelUtil<>(PrdtAreaItemImportExcel.class);
            String instructions = "填表说明：\n" +
                    "1、上架区域编号：必填项，运营商导入上架商品需要指定上架到哪个城市，城市需是当前运营商运营的区域，需与系统中区域的编号值保持一致，否则导入不成功；\n" +
                    "2、上架三级展示分类编号：必填项，需与A列的城市中的三级分类匹配，且值必须和系统中三级展示分类的编号保持一致，否在导入不成功；\n" +
                    "注意：运营商只能导自己运营区域的商品到自己运营的本地商品，且如果产品有多个单位，导入都会上架，如需下架需去后台手动操作；\n" +
                    "3、大中小单位上下架：必填项目，且只能填上架、下架";
            util.importTemplateExcel(response, "上架信息导入", StringUtils.EMPTY, instructions);
        }
    }

    @ApiOperation(value = "城市商品上架导入", httpMethod = HttpMethod.POST)
    @Log(title = "城市商品上架导入", businessType = BusinessType.IMPORT)
//    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/importData")
    public CommonResult<String> importData(MultipartFile file) throws Exception
    {
        ExcelUtil<PrdtAreaItemImportExcel> util = new ExcelUtil<>(PrdtAreaItemImportExcel.class);
        List<PrdtAreaItemImportExcel> prdtAreaList = util.importExcel(file.getInputStream(), 1);
        prdtAreaList.forEach(item->{
            if (Objects.equals(item.getMinShelfStatusName(), "上架")){
                item.setMinShelfStatus(1);
            }else {
                item.setMinShelfStatus(0);
            }
            if (Objects.equals(item.getMidShelfStatusName(), "上架")){
                item.setMidShelfStatus(1);
            }else {
                item.setMidShelfStatus(0);
            }
            if (Objects.equals(item.getLargeShelfStatusName(), "上架")){
                item.setLargeShelfStatus(1);
            }else {
                item.setLargeShelfStatus(0);
            }
        });
        String message = prdtAreaItemService.impordDataDc(prdtAreaList);
        return success(message);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:item:add";
        /** 编辑 */
        public static final String EDIT = "product:item:edit";
        /** 删除 */
        public static final String DELETE = "product:item:remove";
        /** 列表 */
        public static final String LIST = "product:item:list";
        /** 查询 */
        public static final String GET = "product:item:query";
        /** 停用 */
        public static final String DISABLE = "product:item:disable";
        /** 启用 */
        public static final String ENABLE = "product:item:enable";
        public static final String IMPORT = "product:item:import";
    }
}
