package com.zksr.product.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtKeywords;
import com.zksr.product.controller.keywords.vo.PrdtKeywordsPageReqVO;


/**
 * 搜索关键词词库Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Mapper
public interface PrdtKeywordsMapper extends BaseMapperX<PrdtKeywords> {
    default PageResult<PrdtKeywords> selectPage(PrdtKeywordsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtKeywords>()
                    .eqIfPresent(PrdtKeywords::getKeywordsId, reqVO.getKeywordsId())
                    .eqIfPresent(PrdtKeywords::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtKeywords::getKeyword, reqVO.getKeyword())
                    .eqIfPresent(PrdtKeywords::getStatus, reqVO.getStatus())
                .eqIfPresent(PrdtKeywords::getDelFlag, 0)
                .eqIfPresent(PrdtKeywords::getCreateBy, reqVO.getCreateBy())
                .orderByDesc(PrdtKeywords::getKeywordsId));
    }
}
