package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品SKU对象 prdt_sku
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@TableName(value = "prdt_sku")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PrdtSku extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 商品sku_id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long skuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 商品SPU_id */
    @Excel(name = "商品SPU_id")
    private Long spuId;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    private Long unit;

    /** 国际条码 */
    @Excel(name = "国际条码")
    private String barcode;

    /** 属性数组，JSON 格式 */
    @Excel(name = "属性数组，JSON 格式")
    private String properties;

    /** 图片地址 */
    @Excel(name = "图片地址")
    private String thumb;

    /** 库存数量 */
    @Excel(name = "库存数量")
    private BigDecimal stock;

    /** 标准价 */
    @Excel(name = "标准价")
    private BigDecimal markPrice;

    /** 建议零售价 */
    @Excel(name = "建议零售价")
    private BigDecimal suggestPrice;

    /** 成本价 */
    @Excel(name = "成本价")
    private BigDecimal costPrice;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    private Long isDelete;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    private Long status;

    /** 参考进价 */
    @Excel(name = "参考进价")
    private BigDecimal referencePrice;

    /** 参考售价 */
    @Excel(name = "参考售价")
    private BigDecimal referenceSalePrice;



    /** 起订 */
    @Excel(name = "起订")
    private Long minOq;


    /** 订货组数 */
    @Excel(name = "订货组数")
    private Long jumpOq;


    /** 限购 */
    @Excel(name = "限购")
    private Long maxOq;

    /** 中单位-国际条码 */
    @Excel(name = "中单位-国际条码")
    private String midBarcode;

    /** 中单位-标准价 */
    @Excel(name = "中单位-标准价")
    private BigDecimal midMarkPrice;

    /** 中单位-成本价(供货价) */
    @Excel(name = "中单位-成本价(供货价)")
    private BigDecimal midCostPrice;

    /** 中单位-建议零售价 */
    @Excel(name = "中单位-建议零售价")
    private BigDecimal midSuggestPrice;

    /** 中单位-起订 */
    @Excel(name = "中单位-起订")
    private Long midMinOq;

    /** 中单位-订货组数 */
    @Excel(name = "中单位-订货组数")
    private Long midJumpOq;

    /** 中单位-限购 */
    @Excel(name = "中单位-限购")
    private Long midMaxOq;

    /** 大单位-国际条码 */
    @Excel(name = "大单位-国际条码")
    private String largeBarcode;

    /** 大单位-标准价 */
    @Excel(name = "大单位-标准价")
    private BigDecimal largeMarkPrice;

    /** 大单位-成本价(供货价) */
    @Excel(name = "大单位-成本价(供货价)")
    private BigDecimal largeCostPrice;

    /** 大单位-建议零售价 */
    @Excel(name = "大单位-建议零售价")
    private BigDecimal largeSuggestPrice;

    /** 大单位-起订 */
    @Excel(name = "大单位-起订")
    private Long largeMinOq;

    /** 大单位-订货组数 */
    @Excel(name = "大单位-订货组数")
    private Long largeJumpOq;

    /** 大单位-限购 */
    @Excel(name = "大单位-限购")
    private Long largeMaxOq;

    /** 外部来源商品编号 */
    @Excel(name = "外部来源商品编号")
    private String sourceNo;

    /** 外部来源商品编号 */
    @Excel(name = "来源（B2B、ERP）")
    private String source;

    /** 已售数量, 库存 - 已售 = 剩余 */
    @Excel(name = "已售数量, 库存 - 已售 = 剩余")
    private BigDecimal saleQty;

    /** 最后库存更新时间 */
    @Excel(name = "最后库存更新时间")
    private Date lastUpdateTime;


    /**
     * 销售分润占比, 最大0.29 = 29%
     */
    @Excel(name = "销售分润占比, 最大0.29 = 29%")
    @ApiModelProperty("销售分润占比, 最大0.29 = 29%")
    private BigDecimal saleTotalRate;

    /** 已同步库存 */
    @Excel(name = "已同步库存")
    @ApiModelProperty(value = "已同步库存", notes = "可售库存 = 总库存 - (已售 - 已同步)")
    private BigDecimal syncedQty;

    /** 中单位状态 */
    @Excel(name = "中单位状态")
    @ApiModelProperty(value = "中单位状态", notes = "中单位状态 1-启用 0-停用")
    private Long midStatus;

    /** 大单位状态 */
    @Excel(name = "大单位状态")
    @ApiModelProperty(value = "大单位状态", notes = "大单位状态 1-启用 0-停用")
    private Long largeStatus;


    @TableField(exist = false)
    @ApiModelProperty("上架城市id")
    private Long areaId;

    @TableField(exist = false)
    @ApiModelProperty("上架城市名称")
    private String areaName;

    @TableField(exist = false)
    @ApiModelProperty("小单位上架状态,0-未上架,1-已上架")
    private Integer minShelfStatus;

    @TableField(exist = false)
    @ApiModelProperty("中单位上架状态,0-未上架,1-已上架")
    private Integer midShelfStatus;

    @TableField(exist = false)
    @ApiModelProperty("大单位上架状态,0-未上架,1-已上架")
    private Integer largeShelfStatus;

    @ApiModelProperty("小单位无库存时间")
    private Date minNoStockTime;

    @ApiModelProperty("中单位无库存时间")
    private Date midNoStockTime;

    @ApiModelProperty("大单位无库存时间")
    private Date largeNoStockTime;

    public PrdtSku(Long skuId, String barcode) {
        this.skuId = skuId;
        this.barcode = barcode;
    }

    public void setNoStockTime(Date time, UnitTypeEnum unitType) {
        if (unitType == UnitTypeEnum.UNIT_SMALL) {
            this.minNoStockTime = time;
        }
        if (unitType == UnitTypeEnum.UNIT_MIDDLE) {
            this.midNoStockTime = time;
        }
        if (unitType == UnitTypeEnum.UNIT_LARGE) {
            this.largeNoStockTime = time;
        }
    }

    public Date getNoStockTime(UnitTypeEnum unitType) {
        if (unitType == UnitTypeEnum.UNIT_SMALL) {
            return this.minNoStockTime;
        }
        if (unitType == UnitTypeEnum.UNIT_MIDDLE) {
            return this.midNoStockTime;
        }
        if (unitType == UnitTypeEnum.UNIT_LARGE) {
            return this.largeNoStockTime;
        }
        return null;
    }
}
