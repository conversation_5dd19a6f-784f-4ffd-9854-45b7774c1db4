package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtMaterial;
import com.zksr.product.controller.material.vo.PrdtMaterialPageReqVO;
import org.apache.ibatis.annotations.Param;
import com.zksr.product.api.material.vo.MaterialInfoVO;

import java.util.List;


/**
 * 素材Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Mapper
public interface PrdtMaterialMapper extends BaseMapperX<PrdtMaterial> {
    default PageResult<PrdtMaterial> selectPage(PrdtMaterialPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtMaterial>()
                    .eqIfPresent(PrdtMaterial::getMaterialId, reqVO.getMaterialId())
                    .eqIfPresent(PrdtMaterial::getSysCode, reqVO.getSysCode())
                    .likeIfPresent(PrdtMaterial::getName, reqVO.getName())
                    .eqIfPresent(PrdtMaterial::getImg, reqVO.getImg())
                    .eqIfPresent(PrdtMaterial::getStatus, reqVO.getStatus())
                .orderByDesc(PrdtMaterial::getMaterialId));
    }


    /**
     * 修改素材状态
     * @param materialIds 素材ID集合
     * @param status 状态
     */
    default void updateStatus(List<Long> materialIds,Integer status){
        update(null, new LambdaUpdateWrapper<PrdtMaterial>()
                .set(PrdtMaterial::getStatus,status)
                .in(PrdtMaterial::getMaterialId,materialIds));
    }


    /**
     * 获取素材类型集合
     */
    List<MaterialInfoVO> selectByApplyType(@Param("applyType") Integer applyType, @Param("applyId") Long applyId);
}
