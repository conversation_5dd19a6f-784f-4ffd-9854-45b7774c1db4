package com.zksr.product.cache.handler.impl;

import com.zksr.common.elasticsearch.domain.EsGlobalProduct;
import com.zksr.common.elasticsearch.domain.EsLocalProduct;
import com.zksr.common.elasticsearch.domain.EsProduct;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.common.elasticsearch.util.EsProductConvertUtil;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.api.model.EsProductEventType;
import com.zksr.product.api.model.event.EsSpuUpdateProductEvent;
import com.zksr.product.cache.handler.IAbstractProductEventHandler;
import com.zksr.product.domain.dto.EsProductLoadReqDTO;
import com.zksr.product.mapper.ProductDataMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: SPU数据更新
 * @date 2024/2/29 19:45
 */
@Service(EsProductEventType.SPU_UPDATE)
public class SpuUpdateProductHandler extends IAbstractProductEventHandler<EsSpuUpdateProductEvent> {

    @Autowired
    private ProductDataMapper productDataMapper;

    @Autowired
    private EsProductService esProductService;

    @Override
    public List<EsProduct> execEvent(EsProductEvent<EsSpuUpdateProductEvent> event) {
        List<EsProduct> result = new ArrayList<>();
        {
            List<EsLocalProduct> fullProducts = productDataMapper.selectByLocal(EsProductLoadReqDTO.build(event.getData()));
            // 渲染数据
            this.render(fullProducts);
            esProductService.saveLocalFull(fullProducts);
            result.addAll(fullProducts);
        }
        {
            List<EsGlobalProduct> esGlobalProducts = productDataMapper.selectByGlobal(EsProductLoadReqDTO.build(event.getData()));
            // 渲染数据
            this.render(esGlobalProducts);
            esProductService.saveGlobalFull(esGlobalProducts);
            result.addAll(esGlobalProducts);
        }
        return result;
    }
}
