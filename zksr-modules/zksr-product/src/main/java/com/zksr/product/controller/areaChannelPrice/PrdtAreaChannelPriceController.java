package com.zksr.product.controller.areaChannelPrice;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtAreaChannelPrice;
import com.zksr.product.service.IPrdtAreaChannelPriceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.areaChannelPrice.vo.PrdtAreaChannelPricePageReqVO;
import com.zksr.product.controller.areaChannelPrice.vo.PrdtAreaChannelPriceSaveReqVO;
import com.zksr.product.controller.areaChannelPrice.vo.PrdtAreaChannelPriceRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 城市渠道价格Controller
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Api(tags = "管理后台 - 城市渠道价格接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/areaChannelPrice")
public class PrdtAreaChannelPriceController {
    @Autowired
    private IPrdtAreaChannelPriceService prdtAreaChannelPriceService;

    /**
     * 新增城市渠道价格
     */
    @ApiOperation(value = "新增城市渠道价格", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "城市渠道价格", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtAreaChannelPriceSaveReqVO createReqVO) {
        return success(prdtAreaChannelPriceService.insertPrdtAreaChannelPrice(createReqVO));
    }

    /**
     * 修改城市渠道价格
     */
    @ApiOperation(value = "修改城市渠道价格", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "城市渠道价格", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtAreaChannelPriceSaveReqVO updateReqVO) {
            prdtAreaChannelPriceService.updatePrdtAreaChannelPrice(updateReqVO);
        return success(true);
    }

    /**
     * 删除城市渠道价格
     */
    @ApiOperation(value = "删除城市渠道价格", httpMethod = "DELETE", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "城市渠道价格", businessType = BusinessType.DELETE)
    @DeleteMapping("/{channelId}/{areaId}")
    public CommonResult<Boolean> remove(@PathVariable("channelId") Long channelId,@PathVariable("areaId") Long areaId) {
        prdtAreaChannelPriceService.deletePrdtAreaChannelPrice(channelId,areaId);
        return success(true);
    }

    /**
     * 获取城市渠道价格详细信息
     */
    @ApiOperation(value = "获得城市渠道价格详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{sysCode}")
    public CommonResult<PrdtAreaChannelPriceRespVO> getInfo(@PathVariable("sysCode") Long sysCode) {
        PrdtAreaChannelPrice prdtAreaChannelPrice = prdtAreaChannelPriceService.getPrdtAreaChannelPrice(sysCode);
        return success(HutoolBeanUtils.toBean(prdtAreaChannelPrice, PrdtAreaChannelPriceRespVO.class));
    }

    /**
     * 分页查询城市渠道价格
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得城市渠道价格分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtAreaChannelPricePageReqVO>> getPage(@Valid PrdtAreaChannelPricePageReqVO pageReqVO) {
        return success(prdtAreaChannelPriceService.getPrdtAreaChannelPricePage(pageReqVO));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:price:add";
        /** 编辑 */
        public static final String EDIT = "product:price:edit";
        /** 删除 */
        public static final String DELETE = "product:price:remove";
        /** 列表 */
        public static final String LIST = "product:price:list";
        /** 查询 */
        public static final String GET = "product:price:query";
        /** 停用 */
        public static final String DISABLE = "product:price:disable";
        /** 启用 */
        public static final String ENABLE = "product:price:enable";
    }
}
