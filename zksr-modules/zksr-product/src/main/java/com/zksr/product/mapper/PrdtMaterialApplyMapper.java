package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.product.controller.materialApply.vo.PrdtMaterialApplyEchoReqVO;
import com.zksr.product.controller.materialApply.vo.PrdtMaterialApplyRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtMaterialApply;
import com.zksr.product.controller.materialApply.vo.PrdtMaterialApplyPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.zksr.product.constant.ProductConstant.*;


/**
 * 素材应用Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Mapper
public interface PrdtMaterialApplyMapper extends BaseMapperX<PrdtMaterialApply> {
    default PageResult<PrdtMaterialApply> selectPage(PrdtMaterialApplyPageReqVO reqVO) {
        LambdaQueryWrapperX<PrdtMaterialApply> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(PrdtMaterialApply::getMaterialApplyId, reqVO.getMaterialApplyId())
                .eqIfPresent(PrdtMaterialApply::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrdtMaterialApply::getMaterialId, reqVO.getMaterialId())
                .eqIfPresent(PrdtMaterialApply::getApplyType, reqVO.getApplyType())
                .eqIfPresent(PrdtMaterialApply::getApplyId, reqVO.getApplyId())
                .eqIfPresent(PrdtMaterialApply::getStartTime, reqVO.getStartTime())
                .eqIfPresent(PrdtMaterialApply::getEndTime, reqVO.getEndTime())
                .eqIfPresent(PrdtMaterialApply::getApplyUserId,reqVO.getApplyUserId());

        //设置查询状态
        Integer status = reqVO.getMaterialApplyStatus();
        if(Objects.equals(status,PRDT_MATERIAL_APPLY_STATUS_0)){
            //未生效：素材生效时间大于当前时间
            wrapper.gt(PrdtMaterialApply::getStartTime, DateUtils.getNowDate());

        }else if(Objects.equals(status,PRDT_MATERIAL_APPLY_STATUS_1)){
            //生效中：素材生效时间小于或等于当前时间，素材失效时间大于当前时间
            wrapper.le(PrdtMaterialApply::getStartTime, DateUtils.getNowDate()).gt(PrdtMaterialApply::getEndTime, DateUtils.getNowDate());

        }else if((Objects.equals(status,PRDT_MATERIAL_APPLY_STATUS_2))){
            //已失效：素材失效时间小于或等于当前时间
            wrapper.le(PrdtMaterialApply::getEndTime, DateUtils.getNowDate());
        }


        //设置分组
        //wrapper.groupBy(PrdtMaterialApply::getMaterialId).groupBy(PrdtMaterialApply::getApplyType).groupBy(PrdtMaterialApply::getStartTime).groupBy(PrdtMaterialApply::getEndTime);

        //设置排序
        wrapper.orderByDesc(PrdtMaterialApply::getStartTime);

        return selectPage(reqVO, wrapper);
    }

    /**
     *
     * 校验 查询该时间区间内 是否已经存在素材打标信息
     * @param applyId
     * @param applyType
     * @param startTime
     * @param endTime
     * @return
     */
    default PrdtMaterialApply selectApplyByCheck(Long applyId, Integer applyType, Date startTime, Date endTime) {
        return selectOne(new LambdaQueryWrapperX<PrdtMaterialApply>()
                .eq(PrdtMaterialApply::getApplyId, applyId)
                .eq(PrdtMaterialApply::getApplyType, applyType)
                //已生效或未生效的素材打标信息
                .gt(PrdtMaterialApply::getEndTime, DateUtils.getNowDate())
                .and(and ->
                 //情况一： 开始时间在某个打标时间段内
                 and.le(PrdtMaterialApply::getStartTime, startTime).ge(PrdtMaterialApply::getEndTime, startTime)
                //情况二： 结束时间在某个打标时间段内
                .or()
                .le(PrdtMaterialApply::getStartTime, endTime).ge(PrdtMaterialApply::getEndTime, endTime)
                //情况三： 开始时间和结束时间都在某个打标时间段内
                .or()
                .le(PrdtMaterialApply::getStartTime, startTime).ge(PrdtMaterialApply::getEndTime, endTime)
                //情况四： 开始时间和结束时间内包含某个打标时间
                .or()
                .ge(PrdtMaterialApply::getStartTime, startTime).le(PrdtMaterialApply::getEndTime, endTime)
        ));
    }


    /**
     * 查询分页列表
     * @param reqVO
     * @param page
     * @return
     */
    public Page<PrdtMaterialApplyRespVO> selectPageMaterialApply(@Param("reqVO") PrdtMaterialApplyPageReqVO reqVO, @Param("page") Page<PrdtMaterialApplyPageReqVO> page);

    /**
     * 获取后台素材打标详情
     *
     * @param reqVO@return
     */
    default List<PrdtMaterialApply> getMaterialApplyList(PrdtMaterialApplyEchoReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PrdtMaterialApply>()
                .eqIfPresent(PrdtMaterialApply::getMaterialId, reqVO.getMaterialId())
                .eqIfPresent(PrdtMaterialApply::getApplyType, reqVO.getApplyType())
                .eqIfPresent(PrdtMaterialApply::getStartTime, reqVO.getStartTime())
                .eqIfPresent(PrdtMaterialApply::getEndTime, reqVO.getEndTime())
                .eqIfPresent(PrdtMaterialApply::getApplyId,reqVO.getApplyId()));

    }

    /**
     * 获取后台素材打标详情
     *
     * @param reqVO@return
     */
    default PrdtMaterialApply getMaterialApplyByEchoReq(PrdtMaterialApplyEchoReqVO reqVO) {
        return selectOne(new LambdaQueryWrapperX<PrdtMaterialApply>()
                .eqIfPresent(PrdtMaterialApply::getMaterialId, reqVO.getMaterialId())
                .eqIfPresent(PrdtMaterialApply::getApplyType, reqVO.getApplyType())
                .eqIfPresent(PrdtMaterialApply::getStartTime, reqVO.getStartTime())
                .eqIfPresent(PrdtMaterialApply::getEndTime, reqVO.getEndTime())
                .eqIfPresent(PrdtMaterialApply::getApplyId,reqVO.getApplyId()));

    }


    /**
     * 下架素材应用 实际为修改失效时间  将失效时间改为当前时间  如果生效时间大于当前时间 则将生效时间也改为当前时间
     * @param reqVO
     */
    default void removeMaterialApply(PrdtMaterialApplyEchoReqVO reqVO){

        //获取当前时间 减一秒  由于下架素材后 即会刷新素材列表 导致列表显示还是未失效 所以下架时间设置为当前时间 减一秒
        Date now = DateUtils.getDateAddSecondByNow(NumberPool.LOWER_GROUND);

        update(null,new LambdaUpdateWrapper<PrdtMaterialApply>().set(PrdtMaterialApply::getEndTime, now)
                .setSql("start_time = CASE WHEN start_time > {0} THEN {0} ELSE start_time END", now)
                .eq(PrdtMaterialApply::getMaterialId,reqVO.getMaterialId())
                .eq(PrdtMaterialApply::getApplyType,reqVO.getApplyType())
                .eq(PrdtMaterialApply::getStartTime,reqVO.getStartTime())
                .eq(PrdtMaterialApply::getEndTime,reqVO.getEndTime()));
    }


    /**
     * 根据应用ID集合 获得生效中的素材应用
     * @param applyIds
     * @return
     */
    List<PrdtMaterialApplyRespVO> getByMaterialApplyByApplyIds(@Param("applyIds") List<Long> applyIds);

    /**
     * 根据素材应用ID集合下架素材应用 实际为修改失效时间  将失效时间改为当前时间  如果生效时间大于当前时间 则将生效时间也改为当前时间
     * @param materialApplyIds
     */
    default void removeByMaterialApplyIds(List<Long> materialApplyIds){
        //获取当前时间 减一秒  由于下架素材后 即会刷新列表 导致列表显示还是未失效 所以下架时间设置为当前时间 减一秒
        Date now = DateUtils.getDateAddSecondByNow(NumberPool.LOWER_GROUND);
        update(null,new LambdaUpdateWrapper<PrdtMaterialApply>().set(PrdtMaterialApply::getEndTime, now)
                .setSql("start_time = CASE WHEN start_time > {0} THEN {0} ELSE start_time END", now)
                .in(PrdtMaterialApply::getMaterialApplyId,materialApplyIds));
    }

    /**
     * 获取促销活动的素材应用信息(带素材信息)
     * @param vo
     * @return
     */
    MaterialApplyVO getByMaterialApplyByMaterial(@Param("reqVO") MaterialApplyVO vo);



}
