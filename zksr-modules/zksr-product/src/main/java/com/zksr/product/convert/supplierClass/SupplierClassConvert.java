package com.zksr.product.convert.supplierClass;

import com.zksr.product.api.spu.excel.ProductImportExcel;
import com.zksr.product.api.spu.vo.PrdtSpuSaveReqVO;
import com.zksr.product.api.supplierClass.dto.SupplierClassRespDTO;
import com.zksr.product.domain.PrdtSpu;
import com.zksr.product.domain.PrdtSupplierClass;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2025-03-11
 * @desc
 */
@Mapper
public interface SupplierClassConvert {

    SupplierClassConvert INSTANCE = Mappers.getMapper(SupplierClassConvert.class);

//    @Mappings({
//            @Mapping(source = "supplierClass.catgoryId", target = "catgoryId"),
//            @Mapping(source = "supplierClass.supplierId", target = "supplierId"),
//            @Mapping(source = "supplierClass.isAfterSales", target = "isAfterSales"),
//            @Mapping(source = "supplierClass.afterSalesTimeType", target = "afterSalesTimeType"),
//            @Mapping(source = "supplierClass.afterSalesTime", target = "afterSalesTime"),
//    })
//    @BeanMapping(ignoreByDefault = true)
//    void convertRespDTO(@MappingTarget SupplierClassRespDTO target, PrdtSupplierClass supplierClass);

    List<SupplierClassRespDTO> convertListRespDTO(List<PrdtSupplierClass> supplierClass);
}
