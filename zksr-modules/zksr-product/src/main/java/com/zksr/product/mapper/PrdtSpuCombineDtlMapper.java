package com.zksr.product.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtSpuCombineDtl;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineDtlPageReqVO;

import java.util.List;


/**
 * 组合商品详情Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Mapper
public interface PrdtSpuCombineDtlMapper extends BaseMapperX<PrdtSpuCombineDtl> {
    default PageResult<PrdtSpuCombineDtl> selectPage(PrdtSpuCombineDtlPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtSpuCombineDtl>()
                    .eqIfPresent(PrdtSpuCombineDtl::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtSpuCombineDtl::getSpuCombineId, reqVO.getSpuCombineId())
                    .eqIfPresent(PrdtSpuCombineDtl::getSkuId, reqVO.getSkuId())
                    .eqIfPresent(PrdtSpuCombineDtl::getSkuUnitType, reqVO.getSkuUnitType())
                    .eqIfPresent(PrdtSpuCombineDtl::getQty, reqVO.getQty())
                    .eqIfPresent(PrdtSpuCombineDtl::getGiftFlag, reqVO.getGiftFlag())
                .orderByDesc(PrdtSpuCombineDtl::getSysCode));
    }

    default List<PrdtSpuCombineDtl> getPrdtSpuCombineDtlBySpuCombineId(Long spuCombineId){
        return selectList(new LambdaQueryWrapperX<PrdtSpuCombineDtl>()
                .eq(PrdtSpuCombineDtl::getSpuCombineId, spuCombineId));
    }

    default void deleteBySpuCombineId(Long spuCombineId){
        delete(new LambdaQueryWrapperX<PrdtSpuCombineDtl>()
                .eq(PrdtSpuCombineDtl::getSpuCombineId, spuCombineId));
    }
}
