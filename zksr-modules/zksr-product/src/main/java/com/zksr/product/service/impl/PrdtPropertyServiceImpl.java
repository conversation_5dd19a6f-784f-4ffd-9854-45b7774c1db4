package com.zksr.product.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtPropertyMapper;
import com.zksr.product.domain.PrdtProperty;
import com.zksr.product.controller.property.vo.PrdtPropertyPageReqVO;
import com.zksr.product.controller.property.vo.PrdtPropertySaveReqVO;
import com.zksr.product.service.IPrdtPropertyService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 规格名称Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Service
public class PrdtPropertyServiceImpl implements IPrdtPropertyService {
    @Autowired
    private PrdtPropertyMapper prdtPropertyMapper;

    /**
     * 新增规格名称
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtProperty(PrdtPropertySaveReqVO createReqVO) {

        PrdtProperty prdtProperty = HutoolBeanUtils.toBean(createReqVO, PrdtProperty.class);
        return prdtPropertyMapper.insertPrdtProperty(prdtProperty);
    }

    /**
     * 修改规格名称
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtProperty(PrdtPropertySaveReqVO updateReqVO) {
        prdtPropertyMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrdtProperty.class));
    }

    /**
     * 删除规格名称
     *
     * @param propertyId 规格名称id
     */
    @Override
    public void deletePrdtProperty(Long propertyId) {
        // 删除
        prdtPropertyMapper.deleteById(propertyId);
    }

    /**
     * 批量删除规格名称
     *
     * @param propertyIds 需要删除的规格名称主键
     * @return 结果
     */
    @Override
    public void deletePrdtPropertyByPropertyIds(Long[] propertyIds) {
        for(Long propertyId : propertyIds){
            this.deletePrdtProperty(propertyId);
        }
    }

    /**
     * 获得规格名称
     *
     * @param propertyId 规格名称id
     * @return 规格名称
     */
    @Override
    public PrdtProperty getPrdtProperty(Long propertyId) {
        return prdtPropertyMapper.selectById(propertyId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtProperty> getPrdtPropertyPage(PrdtPropertyPageReqVO pageReqVO) {
        return prdtPropertyMapper.selectPage(pageReqVO);
    }

    private void validatePrdtPropertyExists(Long propertyId) {
        if (prdtPropertyMapper.selectById(propertyId) == null) {
            throw exception(PRDT_PROPERTY_NOT_EXISTS);
        }
    }



}
