package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.product.controller.materialApply.vo.*;
import com.zksr.product.domain.PrdtMaterialApply;

import java.util.List;

/**
 * 素材应用Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IPrdtMaterialApplyService {

    /**
     * 新增素材应用
     *
     * @param createReqVO 创建信息
     */
    public void insertPrdtMaterialApply(@Valid List<PrdtMaterialApplySaveReqVO> createReqVO);

    /**
     * 修改素材应用
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtMaterialApply(@Valid List<PrdtMaterialApplySaveReqVO> updateReqVO);

    /**
     * 删除素材应用
     *
     * @param materialApplyId 素材应用id
     */
    public void deletePrdtMaterialApply(Long materialApplyId);

    /**
     * 批量删除素材应用
     *
     * @param materialApplyIds 需要删除的素材应用主键集合
     * @return 结果
     */
    public void deletePrdtMaterialApplyByMaterialApplyIds(List<Long> materialApplyIds);

    /**
     * 获得素材应用
     *
     * @param materialApplyId 素材应用id
     * @return 素材应用
     */
    public PrdtMaterialApply getPrdtMaterialApply(Long materialApplyId);

    /**
     * 获得素材应用分页
     *
     * @param pageReqVO 分页查询
     * @return 素材应用分页
     */
    PageResult<PrdtMaterialApplyRespVO> getPrdtMaterialApplyPage(PrdtMaterialApplyPageReqVO pageReqVO);

    /**
     * 获得后台素材应用详情
     *
     * @param reqVO@return 素材应用
     */
    public PrdtMaterialApplyEchoRespVO getMaterialApplyList(PrdtMaterialApplyEchoReqVO reqVO);

    /**
     * 下架素材应用
     *
     * @param reqVO@return 素材应用
     */
    public void removeMaterialApply(PrdtMaterialApplyEchoReqVO reqVO);

    /**
     * 获得素材应用详情
     *
     * @param reqVO@return 素材应用
     */
    public PrdtMaterialApply getMaterialApplyByEchoReq(PrdtMaterialApplyEchoReqVO reqVO);

    /**
     * 根据应用ID集合 获得生效中的素材应用
     *
     * @param applyIds 应用id集合
     * @return 素材应用
     */
    public List<PrdtMaterialApplyRespVO> getByMaterialApplyByApplyIds(List<Long> applyIds);

    /**
     * 获取促销活动的素材应用信息(带素材信息)
     * @param vo
     */
    MaterialApplyVO getByMaterialApplyByMaterial(MaterialApplyVO vo);

    /**
     * 清除缓存信息
     * @param updateReqVO
     */
    void removeCache(List<PrdtMaterialApplySaveReqVO> updateReqVO);

}
