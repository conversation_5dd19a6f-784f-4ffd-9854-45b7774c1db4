package com.zksr.product.controller.areaClass.vo;

import com.zksr.product.domain.PrdtAreaClass;
import com.zksr.product.domain.PrdtChannelAreaClass;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 修改本地展示分类事件参数
 * @date 2024/10/11 11:00
 */
@Data
@ApiModel(description = "修改本地展示分类事件参数")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateAreaClassRespEvent {

    @ApiModelProperty("节点家族, 子节点")
    public List<PrdtAreaClass> family;

    @ApiModelProperty("本地更新数据")
    private PrdtAreaClassSaveReqVO saveReqVO;

    @ApiModelProperty("更新之前的数据")
    private PrdtAreaClass source;

    @ApiModelProperty("更新之前绑定的渠道信息")
    private List<PrdtChannelAreaClass> sourceChannelList;
}
