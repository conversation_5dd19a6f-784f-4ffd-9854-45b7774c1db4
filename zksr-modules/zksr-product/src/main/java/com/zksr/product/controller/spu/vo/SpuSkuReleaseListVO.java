package com.zksr.product.controller.spu.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: spu 下 sku 上架的集合
 * @date 2024/5/31 15:05
 */
@Data
public class SpuSkuReleaseListVO {
    private Long itemId;
    private Integer minShelfStatus;
    private Integer midShelfStatus;
    private Integer largeShelfStatus;
    private Long skuId;
    private String thumb;
    private String properties;
    private Integer expirationDate;
    private Long stock;

    private BigDecimal markPrice;
    private BigDecimal suggestPrice;
    private String barcode;
    private String minUnit;
    private Long minOq;
    private Long jumpOq;
    private Long maxOq;

    private BigDecimal midMarkPrice;
    private BigDecimal midSuggestPrice;
    private String midBarcode;
    private String midUnit;
    private Long midMinOq;
    private Long midJumpOq;
    private Long midMaxOq;

    private BigDecimal largeMarkPrice;
    private BigDecimal largeSuggestPrice;
    private String largeBarcode;
    private String largeUnit;
    private Long largeMinOq;
    private Long largeJumpOq;
    private Long largeMaxOq;
}
