package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.domain.PrdtAreaChannelPrice;
import com.zksr.product.controller.areaChannelPrice.vo.PrdtAreaChannelPricePageReqVO;
import com.zksr.product.controller.areaChannelPrice.vo.PrdtAreaChannelPriceSaveReqVO;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 城市渠道价格Service接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface IPrdtAreaChannelPriceService {

    /**
     * 新增城市渠道价格
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtAreaChannelPrice(@Valid PrdtAreaChannelPriceSaveReqVO createReqVO);

    /**
     * 修改城市渠道价格
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtAreaChannelPrice(@Valid PrdtAreaChannelPriceSaveReqVO updateReqVO);

    /**
     * 删除城市渠道价格
     *
     * @param channelId 平台商id
     * @param areaId
     */
    public void deletePrdtAreaChannelPrice(Long channelId,Long areaId);

    /**
     * 批量删除城市渠道价格
     *
     * @param sysCodes 需要删除的城市渠道价格主键集合
     * @return 结果
     */
    public void deletePrdtAreaChannelPriceBySysCodes(Long[] sysCodes);

    /**
     * 获得城市渠道价格
     *
     * @param sysCode 平台商id
     * @return 城市渠道价格
     */
    public PrdtAreaChannelPrice getPrdtAreaChannelPrice(Long sysCode);

    /**
     * 获得城市渠道价格分页
     *
     * @param pageReqVO 分页查询
     * @return 城市渠道价格分页
     */
    PageResult<PrdtAreaChannelPricePageReqVO> getPrdtAreaChannelPricePage(PrdtAreaChannelPricePageReqVO pageReqVO);


    /**
     * 根据城市ID、渠道ID获得城市渠道价格
     *
     * @param areaId 平台商id
     * @return 城市渠道价格
     */
    public PrdtAreaChannelPrice getPriceByAreaIdAndChannelId(Long areaId,Long channelId);


}
