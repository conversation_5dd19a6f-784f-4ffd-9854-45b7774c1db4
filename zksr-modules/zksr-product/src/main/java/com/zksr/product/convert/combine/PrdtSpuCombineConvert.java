package com.zksr.product.convert.combine;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtSpuCombine;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineRespVO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 组合商品 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-12-31
*/
@Mapper
public interface PrdtSpuCombineConvert {

    PrdtSpuCombineConvert INSTANCE = Mappers.getMapper(PrdtSpuCombineConvert.class);

    PrdtSpuCombineRespVO convert(PrdtSpuCombine prdtSpuCombine);

    PrdtSpuCombine convert(PrdtSpuCombineSaveReqVO prdtSpuCombineSaveReq);

    PageResult<PrdtSpuCombineRespVO> convertPage(PageResult<PrdtSpuCombine> prdtSpuCombinePage);
}