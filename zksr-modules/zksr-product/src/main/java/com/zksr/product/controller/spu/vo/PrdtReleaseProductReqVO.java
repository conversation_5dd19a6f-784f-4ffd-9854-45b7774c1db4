package com.zksr.product.controller.spu.vo;

import com.zksr.product.controller.areaItem.vo.ActivityItemPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 上架发布商品 - request vo
 * @date 2025/3/4 8:29
 */
@Data
public class PrdtReleaseProductReqVO extends ActivityItemPage {

    @ApiModelProperty("上架ID")
    private Long itemId;

    @ApiModelProperty("发布城市ID")
    private Long areaId;

    @ApiModelProperty(value = "local-本地, global-全国", required = true)
    private String productType = "local";

    @ApiModelProperty(value = "0-普通商品,1-组合促销", required = true)
    private Integer itemType = 0;

    @ApiModelProperty("展示分类ID")
    private String saleClassId;

    @ApiModelProperty("管理分类ID")
    private String categoryId;

    @ApiModelProperty("skuId")
    private Long skuId;

    @ApiModelProperty("spuId")
    private Long spuId;

    @ApiModelProperty("品牌ID")
    private Long brandId;

    @ApiModelProperty("关键信息, 商品名称/条码/编码")
    private String keyword;

    @ApiModelProperty("spu编码")
    private String spuNo;

    @ApiModelProperty("上架城市集合")
    private List<Long> areaIdList;

    @ApiModelProperty("上架商品ID集合")
    private List<Long> itemIdList;

    @ApiModelProperty("spu集合")
    private List<Long> spuIdList;

    @ApiModelProperty("入驻商集合")
    private List<Long> supplierList;
}
