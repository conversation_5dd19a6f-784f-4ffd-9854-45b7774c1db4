package com.zksr.product.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.controller.groupSaleClass.vo.PrdtGroupSaleClassPageReqVO;
import com.zksr.product.controller.groupSaleClass.vo.PrdtGroupSaleClassSaveReqVO;
import com.zksr.product.domain.PrdtGroupSaleClass;

import javax.validation.Valid;

/**
 * 平台商城市分组-展示分类关联Service接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
public interface IPrdtGroupSaleClassService {

    /**
     * 新增平台商城市分组-展示分类关联
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtGroupSaleClass(@Valid PrdtGroupSaleClassSaveReqVO createReqVO);

    /**
     * 修改平台商城市分组-展示分类关联
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtGroupSaleClass(@Valid PrdtGroupSaleClassSaveReqVO updateReqVO);

    /**
     * 删除平台商城市分组-展示分类关联
     *
     * @param sysCode 平台商id
     */
    public void deletePrdtGroupSaleClass(Long sysCode);

    /**
     * 批量删除平台商城市分组-展示分类关联
     *
     * @param sysCodes 需要删除的平台商城市分组-展示分类关联主键集合
     * @return 结果
     */
    public void deletePrdtGroupSaleClassBySysCodes(Long[] sysCodes);

    /**
     * 获得平台商城市分组-展示分类关联
     *
     * @param sysCode 平台商id
     * @return 平台商城市分组-展示分类关联
     */
    public PrdtGroupSaleClass getPrdtGroupSaleClass(Long sysCode);

    /**
     * 获得平台商城市分组-展示分类关联分页
     *
     * @param pageReqVO 分页查询
     * @return 平台商城市分组-展示分类关联分页
     */
    PageResult<PrdtGroupSaleClass> getPrdtGroupSaleClassPage(PrdtGroupSaleClassPageReqVO pageReqVO);

}
