package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 城市级管理分类扣点设置对象 prdt_catgory_rate
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@TableName(value = "prdt_catgory_rate")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtCatgoryRate extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 城市级管理分类扣点设置id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long catgoryRateId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 平台商二级管理分类id */
    @Excel(name = "平台商二级管理分类id")
    private Long catgoryId;

    /** 运营商分润比例 */
    @Excel(name = "运营商分润比例")
    private BigDecimal dcRate;

    /** 业务员负责人分润比例 */
    @Excel(name = "业务员负责人分润比例")
    private BigDecimal colonel1Rate;

    /** 业务员分润比例 */
    @Excel(name = "业务员分润比例")
    private BigDecimal colonel2Rate;

}
