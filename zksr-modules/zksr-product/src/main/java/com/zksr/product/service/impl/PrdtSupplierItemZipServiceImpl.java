package com.zksr.product.service.impl;

import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.product.domain.PrdtSupplierItem;
import com.zksr.product.domain.PrdtSupplierItemZip;
import com.zksr.product.mapper.PrdtSupplierItemMapper;
import com.zksr.product.mapper.PrdtSupplierItemZipMapper;
import com.zksr.product.service.IPrdtSupplierItemZipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
/**
 * 全国上架商品拉链表 Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Service
public class PrdtSupplierItemZipServiceImpl implements IPrdtSupplierItemZipService {

    @Autowired
    private PrdtSupplierItemZipMapper prdtSupplierItemZipMapper;

    @Autowired
    private PrdtSupplierItemMapper prdtSupplierItemMapper;
    @Override
    @Transactional
    public void insertPrdtSupplierItemZip(PrdtSupplierItem prdtSupplierItem) {
        //获取时间
        Date nowDate = DateUtils.getNowDate();
        Date endOfYearDate = DateUtils.toDate(LocalDateTime.of(2099, 12, 30, 23, 59, 59));
        PrdtSupplierItemZip currentRelation = prdtSupplierItemZipMapper.selectPrdtSupplierItemZip(prdtSupplierItem);
        if(ToolUtil.isEmpty(currentRelation)){
            PrdtSupplierItemZip newRelation = new PrdtSupplierItemZip();
            newRelation.setSysCode(prdtSupplierItem.getSysCode());
            newRelation.setSupplierId(prdtSupplierItem.getSupplierId());
            newRelation.setSpuId(prdtSupplierItem.getSpuId());
            newRelation.setSkuId(prdtSupplierItem.getSkuId());
            newRelation.setStartDate(nowDate);
            newRelation.setEndDate(endOfYearDate);
            prdtSupplierItemZipMapper.insert(newRelation);
        }else{
            //判断一下 这个sku 是否上架了 如果上架了的话 就不需要插入了
            Long aLong = prdtSupplierItemMapper.selectSpuSkuIsOnShelf(prdtSupplierItem);
            //如果没上架 就可以把上一条结束时间改成当前时间 并且新增一条
            if(aLong == 0){
                currentRelation.setEndDate(nowDate);
                prdtSupplierItemZipMapper.updatePrdtSupplierItemZip(currentRelation);

                PrdtSupplierItemZip newPrdtSupplierItemZip = HutoolBeanUtils.toBean(currentRelation, PrdtSupplierItemZip.class);
                newPrdtSupplierItemZip.setSupplierItemZipId(null);
                newPrdtSupplierItemZip.setCreateTime(nowDate);
                newPrdtSupplierItemZip.setStartDate(nowDate);
                newPrdtSupplierItemZip.setEndDate(endOfYearDate);
                prdtSupplierItemZipMapper.insert(newPrdtSupplierItemZip);
            }
        }
    }
}
