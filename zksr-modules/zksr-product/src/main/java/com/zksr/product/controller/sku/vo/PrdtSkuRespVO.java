package com.zksr.product.controller.sku.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 商品SKU对象 prdt_sku
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Data
@ApiModel("商品SKU - prdt_sku Response VO")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PrdtSkuRespVO {
    private static final long serialVersionUID = 1L;

    /** 商品sku_id */
    @ApiModelProperty(value = "商品sku_id", example = "示例值")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", example = "示例值")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 商品SPU_id */
    @Excel(name = "商品SPU_id")
    @ApiModelProperty(value = "商品SPU_id", example = "示例值")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "单位-数据字典", example = "示例值")
    private Long unit;

    /** 国际条码 */
    @Excel(name = "国际条码")
    @ApiModelProperty(value = "国际条码", example = "示例值")
    private String barcode;

    /** 属性数组，JSON 格式 */
    @Excel(name = "属性数组，JSON 格式")
    @ApiModelProperty(value = "属性数组，JSON 格式", example = "示例值")
    private String properties;

    /** 图片地址 */
    @Excel(name = "图片地址")
    @ApiModelProperty(value = "图片地址", example = "示例值")
    private String thumb;

    /** 库存数量 */
    @Excel(name = "库存数量")
    @ApiModelProperty(value = "库存数量", example = "示例值")
    private Long stock;

    /** 标准价 */
    @Excel(name = "标准价")
    @ApiModelProperty(value = "标准价", example = "示例值")
    private BigDecimal markPrice;

    /** 建议零售价 */
    @Excel(name = "建议零售价")
    @ApiModelProperty(value = "建议零售价", example = "示例值")
    private BigDecimal suggestPrice;

    /** 成本价 */
    @Excel(name = "成本价")
    @ApiModelProperty(value = "成本价", example = "示例值")
    private BigDecimal costPrice;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否", example = "示例值")
    private Long isDelete = 0L;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)", example = "示例值")
    private Long status;

    /** 参考进价 */
    @Excel(name = "参考进价")
    @ApiModelProperty(value = "参考进价")
    private BigDecimal referencePrice;

    /** 参考售价 */
    @Excel(name = "参考售价")
    @ApiModelProperty(value = "参考售价")
    private BigDecimal referenceSalePrice;

    /** 起订 */
    @Excel(name = "起订")
    @ApiModelProperty(value = "起订")
    private Long minOq;


    /** 订货组数 */
    @Excel(name = "订货组数")
    @ApiModelProperty(value = "订货组数")
    private Long jumpOq;


    /** 限购 */
    @Excel(name = "限购")
    @ApiModelProperty(value = "限购")
    private Long maxOq;

    /** 中单位-国际条码 */
    @Excel(name = "中单位-国际条码")
    @ApiModelProperty(value = "中单位-国际条码")
    private String midBarcode;

    /** 中单位-标准价 */
    @Excel(name = "中单位-标准价")
    @ApiModelProperty(value = "中单位-标准价")
    private BigDecimal midMarkPrice;

    /** 中单位-成本价(供货价) */
    @Excel(name = "中单位-成本价(供货价)")
    @ApiModelProperty(value = "中单位-成本价(供货价)")
    private BigDecimal midCostPrice;

    /** 中单位-建议零售价 */
    @Excel(name = "中单位-建议零售价")
    @ApiModelProperty(value = "中单位-建议零售价")
    private BigDecimal midSuggestPrice;

    /** 中单位-起订 */
    @Excel(name = "中单位-起订")
    @ApiModelProperty(value = "中单位-起订")
    private Long midMinOq;

    /** 中单位-订货组数 */
    @Excel(name = "中单位-订货组数")
    @ApiModelProperty(value = "中单位-订货组数")
    private Long midJumpOq;

    /** 中单位-限购 */
    @Excel(name = "中单位-限购")
    @ApiModelProperty(value = "中单位-限购")
    private Long midMaxOq;

    /** 中单位零售价 */
    @ApiModelProperty(value = "中单位零售价")
    private java.math.BigDecimal midRetailPrice;

    /** 中单位分润比例 */
    @ApiModelProperty(value = "中单位分润比例 百分比的小数表现形式，1%表示为0.01")
    private java.math.BigDecimal midProfitRate;

    /** 中单位分润金额 */
    @ApiModelProperty(value = "中单位分润金额")
    private java.math.BigDecimal midProfitAmount;

    /** 大单位-国际条码 */
    @Excel(name = "大单位-国际条码")
    @ApiModelProperty(value = "大单位-国际条码")
    private String largeBarcode;

    /** 大单位-标准价 */
    @Excel(name = "大单位-标准价")
    @ApiModelProperty(value = "大单位-标准价")
    private BigDecimal largeMarkPrice;

    /** 大单位-成本价(供货价) */
    @Excel(name = "大单位-成本价(供货价)")
    @ApiModelProperty(value = "大单位-成本价(供货价)")
    private BigDecimal largeCostPrice;

    /** 大单位-建议零售价 */
    @Excel(name = "大单位-建议零售价")
    @ApiModelProperty(value = "大单位-建议零售价")
    private BigDecimal largeSuggestPrice;

    /** 大单位-起订 */
    @Excel(name = "大单位-起订")
    @ApiModelProperty(value = "大单位-起订")
    private Long largeMinOq;

    /** 大单位-订货组数 */
    @Excel(name = "大单位-订货组数")
    @ApiModelProperty(value = "大单位-订货组数")
    private Long largeJumpOq;

    /** 大单位-限购 */
    @Excel(name = "大单位-限购")
    @ApiModelProperty(value = "大单位-限购")
    private Long largeMaxOq;

    /** 大单位零售价 */
    @ApiModelProperty(value = "大单位零售价")
    private java.math.BigDecimal largeRetailPrice;

    /** 大单位分润比例 */
    @ApiModelProperty(value = "大单位分润比例 百分比的小数表现形式，1%表示为0.01")
    private java.math.BigDecimal largeProfitRate;

    /** 大单位分润金额 */
    @ApiModelProperty(value = "大单位分润金额")
    private java.math.BigDecimal largeProfitAmount;

    /** SKU列表 */
    @ApiModelProperty(value = "SKU列表")
    private List<Long> skuList;

    /** 商品名称 */
    @Excel(name = "商品名称")
    @ApiModelProperty(value = "商品名称")
    private String spuName;

    /** 品牌名称 */
    @Excel(name = "品牌名称")
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    /** 规格 */
    @Excel(name = "规格")
    @ApiModelProperty(value = "规格")
    private String specName;

    public PrdtSkuRespVO(Long spuId, Long status) {
        this.spuId = spuId;
        this.status = status;
    }

    public PrdtSkuRespVO(Long spuId) {
        this.spuId = spuId;
    }

    public PrdtSkuRespVO(Long spuId, Long status,Long skuId) {
        this.skuId = skuId;
        this.spuId = spuId;
        this.status = status;
    }

    public PrdtSkuRespVO(Long status, List<Long> skuList) {
        this.status = status;
        this.skuList = skuList;
    }
}
