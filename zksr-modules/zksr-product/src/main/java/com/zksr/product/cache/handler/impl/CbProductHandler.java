package com.zksr.product.cache.handler.impl;

import com.alibaba.fastjson.JSON;
import com.zksr.common.elasticsearch.domain.EsProduct;
import com.zksr.common.elasticsearch.domain.EsProductGroup;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.api.model.EsProductEventType;
import com.zksr.product.api.model.event.EsCbProductEvent;
import com.zksr.product.cache.handler.IAbstractProductEventHandler;
import com.zksr.product.domain.dto.EsCbProductLoadReqDTO;
import com.zksr.product.service.IProductContentService;
import com.zksr.promotion.api.activity.dto.ActivityCbAreaUpdateDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 组合商品刷新ES
 * @date 2024/12/27 14:57
 */
@Slf4j
@Service(EsProductEventType.CB_PRODUCT_UPDATE)
public class CbProductHandler extends IAbstractProductEventHandler<EsCbProductEvent> {

    @Autowired
    private IProductContentService productContentService;

    @Override
    public List<EsProduct> execEvent(EsProductEvent<EsCbProductEvent> event) {
        log.info("开始处理组合促销写入ES{}", JSON.toJSONString(event));
        List<EsProductGroup> cbProductList = new ArrayList<>();
        // 查询全国组合商品
        cbProductList.addAll(productDataMapper.selectCbProductByLocal(EsCbProductLoadReqDTO.build(event.getData())));
        // 查询本地组合商品
        cbProductList.addAll(productDataMapper.selectCbProductByGlobal(EsCbProductLoadReqDTO.build(event.getData())));
        // 渲染组合促销商品数据
        productContentService.renderSpuCombineList(cbProductList);
        // 写入商品到ES
        esProductService.saveProduct(cbProductList);
        // 准备刷新组合促销专用缓存
        Set<ActivityCbAreaUpdateDTO> areaUpdateDTOS = cbProductList.stream().map(item -> new ActivityCbAreaUpdateDTO(item.getAreaId(), item.getSysCode())).collect(Collectors.toSet());
        redisService.setCacheSet(RedisActivityConstants.ACTIVITY_CB_AREA_UPDATE_SET, areaUpdateDTOS);
        return new ArrayList<>(cbProductList);
    }
}
