package com.zksr.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Maps;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.brand.excel.BrandImportExcel;
import com.zksr.product.api.brand.vo.BindBrandMerchantReqVO;
import com.zksr.product.controller.brand.vo.ProductBrandPageReqVO;
import com.zksr.product.controller.brand.vo.ProductBrandRespVO;
import com.zksr.product.controller.brand.vo.ProductBrandSaveReqVO;
import com.zksr.product.domain.ProductBrand;
import com.zksr.product.mapper.ProductBrandMapper;
import com.zksr.product.service.IProductBrandService;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.FileImportApi;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.STATUS_FAIL;
import static com.zksr.common.core.constant.StatusConstants.STATUS_SUCCESS;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 平台品牌Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
@Service
public class ProductBrandServiceImpl implements IProductBrandService {
    @Autowired
    private ProductBrandMapper productBrandMapper;

    @Autowired
    private Cache<Long, BrandDTO> brandDtoCache;
    @Resource
    private FileImportApi fileImportApi;

    /**
     * 新增平台品牌
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertProductBrand(ProductBrandSaveReqVO createReqVO) {
        // 插入
        ProductBrand productBrand = HutoolBeanUtils.toBean(createReqVO, ProductBrand.class);
        productBrand.setCreateBy(SecurityUtils.getUsername());
        productBrand.setCreateTime(DateUtil.date());
        productBrand.setUpdateBy(SecurityUtils.getUsername());
        productBrand.setUpdateTime(DateUtil.date());
        validate(productBrand);
        productBrandMapper.insert(productBrand);
        // 返回
        return productBrand.getBrandId();
    }

    /**
     * 修改平台品牌
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateProductBrand(ProductBrandSaveReqVO updateReqVO) {
        ProductBrand productBrand = HutoolBeanUtils.toBean(updateReqVO, ProductBrand.class);
        productBrand.setUpdateBy(SecurityUtils.getUsername());
        productBrand.setUpdateTime(DateUtil.date());
        validate(productBrand);
        productBrandMapper.updateById(productBrand);
        //删除缓存
        brandDtoCache.remove(updateReqVO.getBrandId());
    }

    /**
     * 删除平台品牌
     *
     * @param brandId 平台商品牌id
     */
    @Override
    public void deleteProductBrand(Long brandId) {
        // 删除
        productBrandMapper.deleteById(brandId);
    }

    /**
     * 批量删除平台品牌
     *
     * @param brandIds 需要删除的平台品牌主键
     * @return 结果
     */
    @Override
    public void deleteProductBrandByBrandIds(Long[] brandIds) {
        for(Long brandId : brandIds){
            this.deleteProductBrand(brandId);
        }
    }

    /**
     * 获得平台品牌
     *
     * @param brandId 平台商品牌id
     * @return 平台品牌
     */
    @Override
    public ProductBrand getProductBrand(Long brandId) {
        return productBrandMapper.selectById(brandId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<ProductBrand> getProductBrandPage(ProductBrandPageReqVO pageReqVO) {
        return productBrandMapper.selectPage(pageReqVO);
    }

    @Override
    public void disable(Long brandId) {
        ProductBrand productBrand = productBrandMapper.selectById(brandId);
        productBrand.setUpdateBy(SecurityUtils.getUsername());
        productBrand.setUpdateTime(DateUtil.date());
        productBrand.setStatus(StringPool.ONE);
        productBrandMapper.updateById(productBrand);
    }

    @Override
    public void enable(Long brandId) {
        ProductBrand productBrand = productBrandMapper.selectById(brandId);
        productBrand.setUpdateBy(SecurityUtils.getUsername());
        productBrand.setUpdateTime(DateUtil.date());
        productBrand.setStatus(StringPool.ZERO);
        productBrandMapper.updateById(productBrand);
    }

    /**
    * @Description: 获取所有品牌
    * @Author: liuxingyu
    * @Date: 2024/3/22 13:21
    */
    @Override
    public List<ProductBrand> getBrand() {
        return productBrandMapper.selectList();
    }

    /**
    * @Description: 根据平台商品牌id获取平台商品牌
    * @Author: liuxingyu
    * @Date: 2024/3/25 15:00
    */
    @Override
    public ProductBrand getBrandByBrandId(Long brandId) {
        return productBrandMapper.selectById(brandId);
    }

    @Override
    public List<ProductBrandRespVO> getSelectedBrand(List<Long> brandIds) {
        return BeanUtil.copyToList( productBrandMapper.selectBatchIds(brandIds), ProductBrandRespVO.class);
    }

    @Override
    public ProductBrand getBrandByBrandName(String brandName) {
        return productBrandMapper.selectBrandByBrandName(brandName);
    }

    /**
    * @Description: 通过ID集合获取品牌
    * @Author: liuxingyu
    * @Date: 2024/5/18 15:16
    */
    @Override
    public List<ProductBrand> getBrandByIds(List<Long> brandIds) {
        return productBrandMapper.getBrandByIds(brandIds);
    }

    @Override
    @Transactional
    public void bindBrandMerchant(BindBrandMerchantReqVO bindBrandMerchantReqVO) {
        // 先置空
        productBrandMapper.setNullMerchantId(bindBrandMerchantReqVO.getBrandMerchantId());
        if (Objects.nonNull(bindBrandMerchantReqVO.getBrandList())) {
            for (Long brandId : bindBrandMerchantReqVO.getBrandList()) {
                ProductBrand productBrand = productBrandMapper.selectById(brandId);
                productBrand.setBrandMerchantId(bindBrandMerchantReqVO.getBrandMerchantId());
                productBrandMapper.updateById(productBrand);
            }
        }
    }

    public String impordBrandData(List<BrandImportExcel> brandList) {
        FileImportHandlerVo fileImportHandlerVo = impordBrandDataEvent(brandList, SecurityUtils.getLoginUser().getSysCode(),null,0);
        return fileImportHandlerVo.getMsg();
    }

    public FileImportHandlerVo impordBrandDataEvent(List<BrandImportExcel> brandList, Long sysCode,Long fileImportId,Integer seq) {
        FileImportHandlerVo fileImportHandlerVo = new FileImportHandlerVo();
        List<SysFileImportDtl> sysFileImportDtls = new ArrayList<>();
        int successNum = 0;
        int failureNum = 0;
        int totalNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        fileImportHandlerVo.setSuccessNum(successNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setTotalNum(totalNum);

        if (brandList.isEmpty()) {
            fileImportHandlerVo.setFailureNum(brandList.size());
            fileImportHandlerVo.setTotalNum(brandList.size());
            fileImportHandlerVo.setMsg("导入数据为空");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
        }

//        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        if (Objects.isNull(sysCode)) {
            fileImportHandlerVo.setFailureNum(brandList.size());
            fileImportHandlerVo.setTotalNum(brandList.size());
            fileImportHandlerVo.setMsg("非运营商角色");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
        }


        // 遍历导入的品牌数据
        for (int line = 0; line < brandList.size(); line++) {
            //导入明细
            SysFileImportDtl sysFileImportDtl = new SysFileImportDtl();
            sysFileImportDtl.setSysCode(sysCode);
            sysFileImportDtl.setCreateBy(sysFileImportDtl.getCreateBy());
            sysFileImportDtl.setCreateTime(new Date());
            sysFileImportDtl.setFileImportId(fileImportId);

            if (failureMsg.length() > 2000) { // 限制错误信息长度
                break;
            }
            int cellNumber = line + 3+seq; // Excel行号

            BrandImportExcel itemData = brandList.get(line);
            sysFileImportDtl.setDtlJson(JsonUtils.toJsonString(itemData));

            // 校验品牌名称
            if (StringUtils.isEmpty(itemData.getBrandName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>品牌名称</span>不能为空。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据品牌名称不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            // 校验品牌状态
            if (!"0".equals(itemData.getStatus()) && !"1".equals(itemData.getStatus())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>品牌状态</span>只能为0或1。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据品牌状态只能为0或1。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            // 创建品牌对象，只设置导入所需的字段
            ProductBrand productBrand = new ProductBrand();
            productBrand.setBrandName(itemData.getBrandName()); // 设置品牌名称
            productBrand.setStatus(itemData.getStatus());       // 设置品牌状态
            productBrand.setMemo(itemData.getMemo());           // 设置备注
            productBrand.setSysCode(sysCode);

            // 判断是否存在重复品牌，基于品牌名称和 dcId
            ProductBrand existingBrand = productBrandMapper.selectByDcIdAndBrandName(sysCode, productBrand.getBrandName());
            if (existingBrand == null) {
                productBrandMapper.insert(productBrand); // 插入新品牌
                successNum++;
            } else {
                // 如果品牌已存在，则更新相关信息
                productBrand.setBrandId(existingBrand.getBrandId());
                productBrandMapper.updateById(productBrand); // 更新已有品牌
                successNum++;
            }

            sysFileImportDtl.setStatus(STATUS_SUCCESS);
            sysFileImportDtls.add(sysFileImportDtl);
        }

        fileImportHandlerVo.setTotalNum(successNum+failureNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setSuccessNum(successNum);
        fileImportHandlerVo.setList(sysFileImportDtls);
        // 检查是否有导入失败项
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            fileImportHandlerVo.setMsg(failureMsg.toString());
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
        } else {
            successMsg.append("恭喜您，数据已全部导入成功！共 " + successNum + " 条");
            fileImportHandlerVo.setMsg(successMsg.toString());
            fileImportHandlerVo.setStatus(STATUS_SUCCESS);
        }

        return fileImportHandlerVo;
    }

    @Override
    public Map<Long, BrandDTO> listByBrandIds(List<Long> brandIds) {
        if (CollectionUtils.isEmpty(brandIds)) {
            return Maps.newHashMap();
        }
        List<ProductBrand> productBrands = productBrandMapper.selectList(ProductBrand::getBrandId, brandIds.stream().distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(productBrands)) {
            return Maps.newHashMap();
        }
        return productBrands.stream()
                .collect(Collectors.toMap(ProductBrand::getBrandId, productBrand -> Objects.requireNonNull(HutoolBeanUtils.toBean(productBrand, BrandDTO.class))));
    }

    public void validate(ProductBrand productBrand) {
        {

            Long count = productBrandMapper.selectCountByBrandIdAndBrandNo( productBrand.getBrandNo(), productBrand.getBrandId() );
            if (count > 0) {
                throw exception(PRDT_BRAND_NO_REPT);
            }
        }
        {
            Long count = productBrandMapper.selectCountByBrandIdAndBrandName( productBrand.getBrandName(), productBrand.getBrandId() );
            if (count > 0) {
                throw exception(PRDT_BRAND_NAME_REPT);
            }
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.product.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 平台品牌 TODO 补充编号 ==========
    // ErrorCode PRDT_BRAND_NOT_EXISTS = new ErrorCode(TODO 补充编号, "平台品牌不存在");


}
