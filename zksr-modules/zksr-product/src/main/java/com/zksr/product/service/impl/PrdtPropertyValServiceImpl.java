package com.zksr.product.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtPropertyValMapper;
import com.zksr.product.domain.PrdtPropertyVal;
import com.zksr.product.controller.propertyVal.vo.PrdtPropertyValPageReqVO;
import com.zksr.product.controller.propertyVal.vo.PrdtPropertyValSaveReqVO;
import com.zksr.product.service.IPrdtPropertyValService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 规格值Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Service
public class PrdtPropertyValServiceImpl implements IPrdtPropertyValService {
    @Autowired
    private PrdtPropertyValMapper prdtPropertyValMapper;

    /**
     * 新增规格值
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtPropertyVal(PrdtPropertyValSaveReqVO createReqVO) {
        PrdtPropertyVal prdtPropertyVal = HutoolBeanUtils.toBean(createReqVO, PrdtPropertyVal.class);
        return prdtPropertyValMapper.insertPrdtPropertyVal(prdtPropertyVal);
    }

    /**
     * 修改规格值
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtPropertyVal(PrdtPropertyValSaveReqVO updateReqVO) {
        prdtPropertyValMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrdtPropertyVal.class));
    }

    /**
     * 删除规格值
     *
     * @param propertyValId 规格值id
     */
    @Override
    public void deletePrdtPropertyVal(Long propertyValId) {
        // 删除
        prdtPropertyValMapper.deleteById(propertyValId);
    }

    /**
     * 批量删除规格值
     *
     * @param propertyValIds 需要删除的规格值主键
     * @return 结果
     */
    @Override
    public void deletePrdtPropertyValByPropertyValIds(Long[] propertyValIds) {
        for(Long propertyValId : propertyValIds){
            this.deletePrdtPropertyVal(propertyValId);
        }
    }

    /**
     * 获得规格值
     *
     * @param propertyValId 规格值id
     * @return 规格值
     */
    @Override
    public PrdtPropertyVal getPrdtPropertyVal(Long propertyValId) {
        return prdtPropertyValMapper.selectById(propertyValId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtPropertyVal> getPrdtPropertyValPage(PrdtPropertyValPageReqVO pageReqVO) {
        return prdtPropertyValMapper.selectPage(pageReqVO);
    }

    private void validatePrdtPropertyValExists(Long propertyValId) {
        if (prdtPropertyValMapper.selectById(propertyValId) == null) {
            throw exception(PRDT_PROPERTY_VAL_NOT_EXISTS);
        }
    }

}
