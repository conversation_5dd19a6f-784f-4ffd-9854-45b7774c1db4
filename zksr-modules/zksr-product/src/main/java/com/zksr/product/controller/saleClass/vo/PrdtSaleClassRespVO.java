package com.zksr.product.controller.saleClass.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 平台商展示分类对象 prdt_sale_class
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Data
@ApiModel("平台商展示分类 - prdt_sale_class Response VO")
public class PrdtSaleClassRespVO {
    private static final long serialVersionUID = 1L;

    /** 平台商展示分类id */
    @ApiModelProperty(value = "平台商城市分组id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long saleClassId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 分类名称 */
    @Excel(name = "分类名称")
    @ApiModelProperty(value = "分类名称", example = "示例值")
    private String name;

    /** 父id */
    @Excel(name = "父id")
    @ApiModelProperty(value = "父id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pid;

    /** 分类图标 */
    @Excel(name = "分类图标")
    @ApiModelProperty(value = "分类图标", example = "示例值")
    private String icon;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "示例值")
    private String memo;

    /** 排序 */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sort;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态", example = "示例值")
    private Long status;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long groupId;

    /** 创建时间 */
    @Excel(name = "创建时间")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 级别 */
    @Excel(name = "级别")
    @ApiModelProperty(value = "级别")
    private Integer level;

    /** 是否展示生产日期 0-关闭, 1-开启 */
    @Excel(name = "是否展示生产日期 0-关闭, 1-开启")
    @ApiModelProperty(value = "是否展示生产日期 0-关闭, 1-开启")
    private Integer showProduceDate;

    /** 生产日期格式 yy/MM/dd 年月日 yy/MM 年月 */
    @Excel(name = "生产日期格式 yy/MM/dd 年月日 yy/MM 年月")
    @ApiModelProperty(value = "生产日期格式 yy/MM/dd 年月日 yy/MM 年月")
    private String produceDateFormat;
}
