package com.zksr.product.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.combine.vo.SkuItemVO;
import com.zksr.product.api.content.ProductContentApi;
import com.zksr.product.api.model.event.EsProductEventBuild;
import com.zksr.product.controller.combine.vo.PrdtSpuCombinePageRespVO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineRespVO;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemPageReqVO;
import com.zksr.product.domain.*;
import com.zksr.product.mapper.*;
import com.zksr.product.mq.ProductEventMqChannel;
import com.zksr.product.service.IPrdtSpuCombineDtlService;
import com.zksr.promotion.api.activity.ActivityApi;
import com.zksr.promotion.api.activity.dto.PrmActivityDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.convert.combine.PrdtSpuCombineConvert;
import com.zksr.product.controller.combine.vo.PrdtSpuCombinePageReqVO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineSaveReqVO;
import com.zksr.product.service.IPrdtSpuCombineService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_1;
import static com.zksr.common.core.constant.PrmConstants.PRM_FUNC_SCOPE_2;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.pool.NumberPool.INT_ONE;
import static com.zksr.product.constant.ProductConstant.PRDT_SHELF_STATUS_0;
import static com.zksr.product.constant.ProductConstant.PRDT_SHELF_STATUS_1;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 组合商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Service
public class PrdtSpuCombineServiceImpl implements IPrdtSpuCombineService {

    @Autowired
    private PrdtSpuCombineMapper prdtSpuCombineMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PrdtSpuCombineDtlMapper prdtSpuCombineDtlMapper;

    @Autowired
    private PrdtSupplierItemMapper prdtSupplierItemMapper;

    @Autowired
    private PrdtAreaItemMapper prdtAreaItemMapper;

    @Autowired
    private IPrdtSpuCombineDtlService prdtSpuCombineDtlService;

    @Resource
    private ActivityApi activityApi;

    @Autowired
    private ProductEventMqChannel productEventProducer;


    /**
     * 新增组合商品
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertPrdtSpuCombine(PrdtSpuCombineSaveReqVO createReqVO) {
        createReqVO.setSpuCombineId(null);
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        createReqVO.setFuncScope(ObjectUtil.isNull(dcId) ? PRM_FUNC_SCOPE_1 : PRM_FUNC_SCOPE_2);

        // 校验请求参数
        validateRequest(createReqVO);

        // 插入组合商品主表
        PrdtSpuCombine prdtSpuCombine = PrdtSpuCombineConvert.INSTANCE.convert(createReqVO);
        prdtSpuCombine.setSpuCombineNo(redisService.getCombineSpuUniqueNumber("ZH"));

        // 计算 suggestPrice
        BigDecimal suggestPrice = BigDecimal.ZERO;
        List<SkuItemVO> skuItems = createReqVO.getSkuItems();
        if (skuItems != null && !skuItems.isEmpty()) {
            suggestPrice = skuItems.stream()
                    .map(skuItem -> skuItem.getMarkPrice().multiply(new BigDecimal(skuItem.getQty())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 设置 suggestPrice
        prdtSpuCombine.setSuggestPrice(suggestPrice);
        if (createReqVO.getSaveStatus() == 1) {
            prdtSpuCombine.setStatus(1L);
        }
        prdtSpuCombineMapper.insert(prdtSpuCombine);

        Long spuCombineId = prdtSpuCombine.getSpuCombineId();

        // 插入组合商品明细表
        List<PrdtSpuCombineDtl> prdtSpuCombineDtls = skuItems.stream()
                .map(skuItem -> {
                    PrdtSpuCombineDtl prdtSpuCombineDtl = new PrdtSpuCombineDtl();
                    prdtSpuCombineDtl.setSpuCombineId(spuCombineId);
                    prdtSpuCombineDtl.setSkuId(skuItem.getSkuId());
                    prdtSpuCombineDtl.setSkuUnitType(skuItem.getUnit());
                    prdtSpuCombineDtl.setQty(skuItem.getQty());
                    prdtSpuCombineDtl.setAreaItemId(skuItem.getAreaItemId());
                    prdtSpuCombineDtl.setSupplierItemId(skuItem.getSupplierItemId());
                    return prdtSpuCombineDtl;
                })
                .collect(Collectors.toList());
        prdtSpuCombineDtlMapper.insertBatch(prdtSpuCombineDtls);

        // 插入上架商品记录
        insertShelfItem(createReqVO, spuCombineId);

        return spuCombineId;
    }


    /**
     * 修改组合商品
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrdtSpuCombine(PrdtSpuCombineSaveReqVO updateReqVO) {
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        updateReqVO.setFuncScope(ObjectUtil.isNull(dcId) ? PRM_FUNC_SCOPE_1 : PRM_FUNC_SCOPE_2);

        // 校验请求参数
        validateRequest(updateReqVO);

        // 计算 suggestPrice
        BigDecimal suggestPrice = BigDecimal.ZERO;
        List<SkuItemVO> skuItems = updateReqVO.getSkuItems();
        if (skuItems != null && !skuItems.isEmpty()) {
            suggestPrice = skuItems.stream()
                    .map(skuItem -> skuItem.getMarkPrice().multiply(new BigDecimal(skuItem.getQty())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        // 设置 suggestPrice
        updateReqVO.setSuggestPrice(suggestPrice);
        if (updateReqVO.getSaveStatus() == 1) {
            updateReqVO.setStatus(1L);
        }

        // 更新组合商品主表
        prdtSpuCombineMapper.updateById(PrdtSpuCombineConvert.INSTANCE.convert(updateReqVO));

        // 删除旧的组合商品明细表记录
        prdtSpuCombineDtlMapper.deleteBySpuCombineId(updateReqVO.getSpuCombineId());

        // 插入组合商品明细表
        List<PrdtSpuCombineDtl> prdtSpuCombineDtls = skuItems.stream()
                .map(skuItem -> {
                    PrdtSpuCombineDtl prdtSpuCombineDtl = new PrdtSpuCombineDtl();
                    prdtSpuCombineDtl.setSpuCombineId(updateReqVO.getSpuCombineId());
                    prdtSpuCombineDtl.setSkuId(skuItem.getSkuId());
                    prdtSpuCombineDtl.setSkuUnitType(skuItem.getUnit());
                    prdtSpuCombineDtl.setQty(skuItem.getQty());
                    prdtSpuCombineDtl.setAreaItemId(skuItem.getAreaItemId());
                    prdtSpuCombineDtl.setSupplierItemId(skuItem.getSupplierItemId());
                    return prdtSpuCombineDtl;
                })
                .collect(Collectors.toList());
        prdtSpuCombineDtlMapper.insertBatch(prdtSpuCombineDtls);

        // 删除旧的上架商品记录
        deleteShelfItem(updateReqVO.getSpuCombineId());

        // 插入新的上架商品记录
        insertShelfItem(updateReqVO, updateReqVO.getSpuCombineId());
    }


    /**
     * 删除组合商品
     *
     * @param spuCombineId 组合商品id
     */
    @Override
    public void deletePrdtSpuCombine(Long spuCombineId) {
        // 删除
        prdtSpuCombineMapper.deleteById(spuCombineId);
    }

    /**
     * 批量删除组合商品
     *
     * @param spuCombineIds 需要删除的组合商品主键
     * @return 结果
     */
    @Override
    public void deletePrdtSpuCombineBySpuCombineIds(Long[] spuCombineIds) {
        for (Long spuCombineId : spuCombineIds) {
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deletePrdtSpuCombine(spuCombineId);
        }
    }

    /**
     * 获得组合商品
     *
     * @param spuCombineId 组合商品id
     * @return 组合商品
     */
    @Override
    public PrdtSpuCombineRespVO getPrdtSpuCombine(Long spuCombineId) {
        PrdtSpuCombineRespVO prdtSpuCombine = HutoolBeanUtils.toBean(prdtSpuCombineMapper.selectById(spuCombineId), PrdtSpuCombineRespVO.class);
        prdtSpuCombine.setPrdtSpuCombineDtlList(prdtSpuCombineDtlService.getPrdtSpuCombineDtl(prdtSpuCombine.getSpuCombineId()));
        return prdtSpuCombine;
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrdtSpuCombinePageRespVO> getPrdtSpuCombinePage(PrdtSpuCombinePageReqVO pageReqVO) {
        Page<PrdtSpuCombinePageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        pageReqVO.setFuncScope(ObjectUtil.isNull(dcId) ? PRM_FUNC_SCOPE_1 : PRM_FUNC_SCOPE_2);
        List<Long> activityIds = activityApi.getActivityCbByStatus(SecurityUtils.getLoginUser().getSysCode(), pageReqVO.getFuncScope(), pageReqVO.getActivityStatus()).getData();
        if (ToolUtil.isEmpty(activityIds)) {
            //设置为-1
            pageReqVO.setActivityIds(Arrays.asList(-1L));
        } else {
            pageReqVO.setActivityIds(activityIds);
        }

        Page<PrdtSpuCombinePageRespVO> pageResult = prdtSpuCombineMapper.selectPageSpuCombine(pageReqVO, page);

        pageResult.getRecords().forEach(item -> {
            PrmActivityDTO activityDTO = activityApi.getActivityDto(item.getActivityId()).getCheckedData();
            item.setTimesRule(activityDTO.getTimesRule());
            item.setActivityStatus(activityDTO.getPrmStatus());
        });
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public void editSpuCombineStatus(Long spuCombineId, Integer status) {
        PrdtSpuCombine prdtSpuCombine = prdtSpuCombineMapper.selectById(spuCombineId);
        prdtSpuCombine.setStatus(status.longValue());
        prdtSpuCombineMapper.updateById(prdtSpuCombine);

    }

    private void validateRequest(PrdtSpuCombineSaveReqVO reqVO) {
        // 商品明细长度
        if (ToolUtil.isNotEmpty(reqVO.getDetails()) && reqVO.getDetails().length() > 10240)
            throw exception(PRDT_SPU_DETAILS_SIZE);

        // 入驻商信息是否存在
        if (ToolUtil.isEmpty(reqVO.getSupplierId()))
            throw exception(PRDT_CHECK_SUPPLIER);
    }

    private void insertShelfItem(PrdtSpuCombineSaveReqVO reqVO, Long spuCombineId) {
        if (reqVO.getFuncScope() == 1) {
            PrdtSupplierItem prdtSupplierItem = new PrdtSupplierItem();
            prdtSupplierItem.setSupplierId(reqVO.getSupplierId());
            prdtSupplierItem.setShelfStatus(reqVO.getSaveStatus() == 0 ? PRDT_SHELF_STATUS_0 : PRDT_SHELF_STATUS_1);
            prdtSupplierItem.setMinShelfStatus(reqVO.getSaveStatus() == 0 ? PRDT_SHELF_STATUS_0 : PRDT_SHELF_STATUS_1);
            prdtSupplierItem.setShelfDate(new Date());
            prdtSupplierItem.setItemType(INT_ONE);
            prdtSupplierItem.setSpuCombineId(spuCombineId);
            prdtSupplierItem.setActivityStartTime(reqVO.getActivityStartTime());
            prdtSupplierItem.setActivityEndTime(reqVO.getActivityEndTime());
            prdtSupplierItem.setActivityId(reqVO.getActivityId());
            prdtSupplierItem.setSaleClassId(reqVO.getClassId());
            prdtSupplierItemMapper.insert(prdtSupplierItem);
        } else {
            PrdtAreaItem prdtAreaItem = new PrdtAreaItem();
            prdtAreaItem.setSupplierId(reqVO.getSupplierId());
            prdtAreaItem.setShelfStatus(reqVO.getSaveStatus() == 0 ? PRDT_SHELF_STATUS_0 : PRDT_SHELF_STATUS_1);
            prdtAreaItem.setMinShelfStatus(reqVO.getSaveStatus() == 0 ? PRDT_SHELF_STATUS_0 : PRDT_SHELF_STATUS_1);
            prdtAreaItem.setShelfDate(new Date());
            prdtAreaItem.setItemType(INT_ONE);
            prdtAreaItem.setSpuCombineId(spuCombineId);
            prdtAreaItem.setActivityStartTime(reqVO.getActivityStartTime());
            prdtAreaItem.setActivityEndTime(reqVO.getActivityEndTime());
            prdtAreaItem.setActivityId(reqVO.getActivityId());
            prdtAreaItem.setAreaId(reqVO.getAreaId());
            prdtAreaItem.setAreaClassId(reqVO.getClassId());
            prdtAreaItemMapper.insert(prdtAreaItem);
        }
    }

    private void deleteShelfItem(Long spuCombineId) {
        prdtSupplierItemMapper.deleteBySpuCombineId(spuCombineId);
        prdtAreaItemMapper.deleteBySpuCombineId(spuCombineId);
    }


//    private void validatePrdtSpuCombineExists(Long spuCombineId) {
//        if (prdtSpuCombineMapper.selectById(spuCombineId) == null) {
//            throw exception(PRDT_SPU_COMBINE_NOT_EXISTS);
//        }
//    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.product.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 组合商品 TODO 补充编号 ==========
    // ErrorCode PRDT_SPU_COMBINE_NOT_EXISTS = new ErrorCode(TODO 补充编号, "组合商品不存在");


}
