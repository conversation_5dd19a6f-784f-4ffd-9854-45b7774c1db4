package com.zksr.product.controller.yhdata.vo;

import com.zksr.product.domain.po.YhMatchAreaItemPO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/1 10:50
 */
@Data
public class YhSkuUnitVO {

    @ApiModelProperty("上架ID")
    private Long areaItemId;

    @ApiModelProperty("spuId")
    private Long spuId;

    @ApiModelProperty("skuId")
    private Long skuId;

    @ApiModelProperty("条码")
    private String barcode;

    /**
     * 参见枚举 {@link com.zksr.common.core.enums.UnitTypeEnum}
     */
    @ApiModelProperty("单位类型, 1-最小单位, 2-中单位, 3-大单位")
    private Integer unitSize;

    @ApiModelProperty("单位起订")
    private Long minOq;

    @ApiModelProperty("单位订货组")
    private Long jumpOq;

    @ApiModelProperty("单位最大限购")
    private Long maxOq;

    @ApiModelProperty("当前单位库存")
    private Long stock;

    @ApiModelProperty("库存转换比例")
    private BigDecimal stockConvert;

    @ApiModelProperty("入驻商要货优先级,0最高, 1次之")
    private Long supplierYhSort = Long.MAX_VALUE;

    @ApiModelProperty(value = "上下架状态", notes = "0-未上架, 1-已上架")
    private Integer shelfStatus;

    private YhMatchAreaItemPO areaItemPO;
}
