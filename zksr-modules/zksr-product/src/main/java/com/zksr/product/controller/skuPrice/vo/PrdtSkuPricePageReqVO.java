package com.zksr.product.controller.skuPrice.vo;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * sku销售价对象 prdt_sku_price
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@ApiModel("sku销售价 - prdt_sku_price分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PrdtSkuPricePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** sku销售价id */
    @Excel(name = "sku销售价id")
    @ApiModelProperty(value = "sku销售价id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuPriceId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 城市id列表 */
    @Excel(name = "城市id列表")
    @ApiModelProperty(value = "城市id列表", required = true)
    private List<Long> areaIdList;

    /** 商品sku id;商品sku id */
    @Excel(name = "商品sku id;商品sku id")
    @ApiModelProperty(value = "商品sku id;商品sku id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 销售价1 */
    @Excel(name = "销售价1")
    @ApiModelProperty(value = "销售价1")
    private BigDecimal salePrice1;

    /** 销售价2 */
    @Excel(name = "销售价2")
    @ApiModelProperty(value = "销售价2")
    private BigDecimal salePrice2;

    /** 销售价3 */
    @Excel(name = "销售价3")
    @ApiModelProperty(value = "销售价3")
    private BigDecimal salePrice3;

    /** 销售价4 */
    @Excel(name = "销售价4")
    @ApiModelProperty(value = "销售价4")
    private BigDecimal salePrice4;

    /** 销售价5 */
    @Excel(name = "销售价5")
    @ApiModelProperty(value = "销售价5")
    private BigDecimal salePrice5;

    /** 销售价6 */
    @Excel(name = "销售价6")
    @ApiModelProperty(value = "销售价6")
    private BigDecimal salePrice6;

    /** 类型(用来区分是全国商品，还是本地配送商品，数据字典);0-全国商品 1-本地配送商品 */
    @NotNull(message = "上架类型不能为空")
    @Excel(name = "类型(用来区分是全国商品，还是本地配送商品，数据字典);0-全国商品 1-本地配送商品")
    @ApiModelProperty(value = "类型0-全国商品 1-本地配送商品")
    private Integer type;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;


    /** 平台商管理分类名称 */
    @Excel(name = "平台商管理分类名称")
    @ApiModelProperty(value = "平台商管理分类名称")
    private String catgoryName;

    /** 平台商品牌名称 */
    @Excel(name = "平台商品牌名称")
    @ApiModelProperty(value = "平台商品牌名称")
    private String brandName;


    /** 成本价 */
    @Excel(name = "成本价")
    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;



    /** 国际条码 */
    @Excel(name = "国际条码")
    @ApiModelProperty(value = "国际条码", example = "示例值")
    private String barcode;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "单位-数据字典", example = "示例值")
    private Long unit;

    /** 属性数组，JSON 格式 */
    @Excel(name = "属性数组，JSON 格式")
    @ApiModelProperty(value = "属性数组，JSON 格式", example = "示例值")
    private String properties;

    /** 标准价 */
    @Excel(name = "标准价")
    @ApiModelProperty(value = "标准价", example = "示例值")
    private BigDecimal markPrice;



    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    @ApiModelProperty(value = "商品SPU编号")
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    /** keyword */
    @Excel(name = "搜索关键字")
    @ApiModelProperty(value = "搜索关键字", example = "示例值")
    private String keyword;


    /** 平台商管理分类id */
    @Excel(name = "平台商管理分类id")
    @ApiModelProperty(value = "平台商管理分类id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long catgoryId;

    /** 平台商品牌id */
    @Excel(name = "平台商品牌id")
    @ApiModelProperty(value = "平台商品牌id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long brandId;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    /** 中单位-销售价1 */
    @Excel(name = "中单位-销售价1")
    @ApiModelProperty(value = "中单位-销售价1")
    private BigDecimal midSalePrice1;

    /** 中单位-销售价2 */
    @Excel(name = "中单位-销售价2")
    @ApiModelProperty(value = "中单位-销售价2")
    private BigDecimal midSalePrice2;

    /** 中单位-销售价3 */
    @Excel(name = "中单位-销售价3")
    @ApiModelProperty(value = "中单位-销售价3")
    private BigDecimal midSalePrice3;

    /** 中单位-销售价4 */
    @Excel(name = "中单位-销售价4")
    @ApiModelProperty(value = "中单位-销售价4")
    private BigDecimal midSalePrice4;

    /** 中单位-销售价5 */
    @Excel(name = "中单位-销售价5")
    @ApiModelProperty(value = "中单位-销售价5")
    private BigDecimal midSalePrice5;

    /** 中单位-销售价6 */
    @Excel(name = "中单位-销售价6")
    @ApiModelProperty(value = "中单位-销售价6")
    private BigDecimal midSalePrice6;

    /** 大单位-销售价1 */
    @Excel(name = "大单位-销售价1")
    @ApiModelProperty(value = "大单位-销售价1")
    private BigDecimal largeSalePrice1;

    /** 大单位-销售价2 */
    @Excel(name = "大单位-销售价2")
    @ApiModelProperty(value = "大单位-销售价2")
    private BigDecimal largeSalePrice2;

    /** 大单位-销售价3 */
    @Excel(name = "大单位-销售价3")
    @ApiModelProperty(value = "大单位-销售价3")
    private BigDecimal largeSalePrice3;

    /** 大单位-销售价4 */
    @Excel(name = "大单位-销售价4")
    @ApiModelProperty(value = "大单位-销售价4")
    private BigDecimal largeSalePrice4;

    /** 大单位-销售价5 */
    @Excel(name = "大单位-销售价5")
    @ApiModelProperty(value = "大单位-销售价5")
    private BigDecimal largeSalePrice5;

    /** 大单位-销售价6 */
    @Excel(name = "大单位-销售价6")
    @ApiModelProperty(value = "大单位-销售价6")
    private BigDecimal largeSalePrice6;

    /** 中单位-标准价 */
    @Excel(name = "中单位-标准价")
    @ApiModelProperty(value = "中单位-标准价")
    private BigDecimal midMarkPrice;

    /** 中单位-成本价(供货价) */
    @Excel(name = "中单位-成本价(供货价)")
    @ApiModelProperty(value = "中单位-成本价(供货价)")
    private BigDecimal midCostPrice;

    /** 大单位-标准价 */
    @Excel(name = "大单位-标准价")
    @ApiModelProperty(value = "大单位-标准价")
    private BigDecimal largeMarkPrice;

    /** 大单位-成本价(供货价) */
    @Excel(name = "大单位-成本价(供货价)")
    @ApiModelProperty(value = "大单位-成本价(供货价)")
    private BigDecimal largeCostPrice;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @Excel(name = "最小单位-数据字典（sys_prdt_unit）", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "最小单位-数据字典（sys_prdt_unit）")
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    @Excel(name = "中单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "中单位-数据字典（sys_prdt_unit）")
    private Long midUnit;

    /** 大单位-数据字典（sys_prdt_unit） */
    @Excel(name = "大单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "大单位-数据字典（sys_prdt_unit）")
    private Long largeUnit;

    /** 单位类型 */
    @Excel(name = "单位类型")
    @ApiModelProperty(value = "单位类型 数据字典sku_price_unit_type： minUnit、midUnit、largeUnit", example = "示例值")
    private String unitType;

    public PrdtSkuPricePageReqVO(Long sysCode, Long areaId, Integer type, Long supplierId, String keyword, Long catgoryId, Long brandId) {
        this.sysCode = sysCode;
        this.areaId = areaId;
        this.type = type;
        this.supplierId = supplierId;
        this.keyword = keyword;
        this.catgoryId = catgoryId;
        this.brandId = brandId;
    }
}
