package com.zksr.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.model.event.EsProductEventBuild;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateReqVO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateVO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.product.controller.areaItem.excel.PrdtSupplierItemImportExcel;
import com.zksr.product.controller.sku.vo.PrdtSkuRespVO;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemPageReqVO;
import com.zksr.product.api.supplierItem.vo.PrdtSupplierItemPageRespVO;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemRespVO;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemSaveReqVO;
import com.zksr.product.domain.*;
import com.zksr.product.mapper.*;
import com.zksr.product.service.*;
import com.zksr.promotion.api.activity.ActivityApi;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.apache.xmlbeans.impl.xb.ltgfmt.Code;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.pool.NumberPool.INT_THREE;
import static com.zksr.product.constant.ProductConstant.*;
import static com.zksr.product.enums.ErrorCodeConstants.*;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_PARTNER_CONFIG_GLOBAL_DISABLE;

/**
 * 入驻商上架商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Service
public class PrdtSupplierItemServiceImpl implements IPrdtSupplierItemService {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PrdtSupplierItemMapper prdtSupplierItemMapper;

    // 入驻商信息调用服务
    @Resource
    private SupplierApi remoteSupplierApi;

    @Autowired
    private IProductEventService productEventService;

    @Autowired
    private Cache<Long, SupplierItemDTO> supplierItemDTOCache;

    @Autowired
    private IProductCacheService productCacheService;

    @Autowired
    private PrdtSkuMapper prdtSkuMapper;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private PrdtSaleClassMapper prdtSaleClassMapper;

    @Autowired
    private PrdtSpuMapper prdtSpuMapper;

    @Resource
    private ActivityApi activityApi;

    @Autowired
    private IPrdtSupplierItemZipService prdtSupplierItemZipService;

    @Autowired
    private IPrdtSkuService skuService;

    @Autowired
    private IPrdtActivityService activityService;

    /**
     * 新增入驻商上架商品
     *
     * @param createReqVO 创建信息
     */
    @Override
    @Transactional
    public List<PrdtSupplierItem> insertPrdtSupplierItem(@Valid List<PrdtSupplierItemSaveReqVO> createReqVO) {
        if(ToolUtil.isEmpty(createReqVO)) throw exception(PRDT_SUPPLIER_ITEM_NOT_EXISTS);
        List<PrdtSupplierItem> supplierItemList = HutoolBeanUtils.toBean(createReqVO, PrdtSupplierItem.class);
        //校验
        PrdtSupplierItem supplierItem = supplierItemList.get(0);
        //校验商品城市展示类别 是否是第三级
        checkClassLevel(supplierItem.getSaleClassId());
        /**
         * ================================================ 前置验证    ==========================================
         */
        releaseBeforeValidate(supplierItemList);
        /**
         * ================================================ 前置验证    ==========================================
         */
        supplierItemList.forEach(item ->{
            //校验
            PrdtSupplierItem checkItem = prdtSupplierItemMapper.selectSupplierItemByCheck(item);
            PrdtSpu prdtSpu = prdtSpuMapper.selectById(item.getSpuId());
            if (Objects.isNull(checkItem)) {
                checkItem = item;
                //获取当前城市商品类型的最大排序号
                Integer sortNum = prdtSupplierItemMapper.selectMaxSort(item);
                checkItem.setSortNum(sortNum + Code.GREATER_THAN);
            }
            // 调整上架状态
            checkItem.setShelfDate(DateUtils.getNowDate());
            checkItem.setSaleClassId(item.getSaleClassId());
            // 小单位一定有
            checkItem.setShelfStatus(PRDT_SHELF_STATUS_1);
            checkItem.setMinShelfStatus(PRDT_SHELF_STATUS_1);
            // 中单位和大单位不一定有
            checkItem.setMidShelfStatus(Objects.nonNull(prdtSpu.getMidUnit()) ? PRDT_SHELF_STATUS_1 : PRDT_SHELF_STATUS_0);
            checkItem.setLargeShelfStatus(Objects.nonNull(prdtSpu.getLargeUnit()) ? PRDT_SHELF_STATUS_1 : PRDT_SHELF_STATUS_0);
            prdtSupplierItemMapper.insertOrUpdate(checkItem);
            prdtSupplierItemZipService.insertPrdtSupplierItemZip(item);
        });
        return supplierItemList;
    }

    /**
     * 修改入驻商上架商品
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
	@Override
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("all")
    public List<PrdtSupplierItem> updatePrdtSupplierItem(PrdtSupplierItemSaveReqVO updateReqVO) {
        ArrayList<PrdtSupplierItem> eventItems = new ArrayList<>();

        PrdtSupplierItem supplierItem = prdtSupplierItemMapper.selectById(updateReqVO.getSupplierItemId());
        eventItems.add(supplierItem);
        //校验
        if(ToolUtil.isEmpty(updateReqVO.getUpdateFlag())) throw exception(PRDT_SUPPLIER_ITEM_UPDATE_FALG_NOT_EXISTS);
        //修改顺序/类别
        //顺序
        if(PRDT_ITEM_UPDATE_FLAG_0 == updateReqVO.getUpdateFlag()){
            supplierItem.setSortNum(updateReqVO.getSortNum());
            prdtSupplierItemMapper.updateById(supplierItem);
        }else{
            PrdtSupplierItem newSupplierItem = prdtSupplierItemMapper.selectBySkuAndSaleId(supplierItem.getSkuId(), updateReqVO.getSaleClassId());
            if (Objects.nonNull(newSupplierItem)) {
                // 在新的展示类目存在
                // 1. 同步新展示分类的上架状态
                newSupplierItem.setShelfDate(DateUtils.getNowDate());
                newSupplierItem.setShelfStatus(supplierItem.getShelfStatus());
                newSupplierItem.setMinShelfStatus(supplierItem.getMinShelfStatus());
                newSupplierItem.setMidShelfStatus(supplierItem.getMidShelfStatus());
                newSupplierItem.setLargeShelfStatus(supplierItem.getLargeShelfStatus());
                prdtSupplierItemMapper.updateById(newSupplierItem);

                // 2. 下架当前的商品数据
                supplierItem.setShelfStatus(NumberPool.INT_ZERO);
                supplierItem.setMinShelfStatus(NumberPool.INT_ZERO);
                supplierItem.setMidShelfStatus(NumberPool.INT_ZERO);
                supplierItem.setLargeShelfStatus(NumberPool.INT_ZERO);
                prdtSupplierItemMapper.updateById(supplierItem);
            } else {
                // 新的上架展示分类没有冲突的, 直接修改展示分类
                supplierItem.setSaleClassId(updateReqVO.getSaleClassId());
                prdtSupplierItemMapper.updateById(supplierItem);
            }
            // 修改展示分类, 必须需要删除上架信息
            // 然后触发重新刷新上架信息
            productEventService.executeEvent(EsProductEventBuild.removeItem(ListUtil.toList(updateReqVO.getSupplierItemId())));
        }
        return eventItems;
    }


    /**
     * 批量上下架入驻商商品
     * @param supplierItemIds 修改信息Id
     * @param minShelfStatus
     * @param midShelfStatus
     * @param largeShelfStatus
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PrdtSupplierItem> updatePrdtSupplierItemShelfStatus(Long[] supplierItemIds, Integer minShelfStatus, Integer midShelfStatus, Integer largeShelfStatus) {
        //校验入参
        if(Objects.isNull(minShelfStatus) && Objects.isNull(midShelfStatus) && Objects.isNull(largeShelfStatus)){
            throw exception(PRDT_SUPPLIER_ITEM_SHELF_STATUS_NOT_EXISTS);
        }
        //获取上下架商品信息
        List<PrdtSupplierItem> itemList = prdtSupplierItemMapper.selectSupplierItemListById(supplierItemIds);
        //上架时校验：如果存在已停用的 不允许批量上架
        boolean isShelfStatus = (Objects.nonNull(minShelfStatus) && PRDT_SHELF_STATUS_1.equals(minShelfStatus))
                || (Objects.nonNull(midShelfStatus) && PRDT_SHELF_STATUS_1.equals(midShelfStatus))
                || (Objects.nonNull(largeShelfStatus) && PRDT_SHELF_STATUS_1.equals(largeShelfStatus));
        if(isShelfStatus){
            //校验需要上架的商品是否存在SKU已停用的
            List<Long> skuList = itemList.stream().map(PrdtSupplierItem::getSkuId).collect(Collectors.toList());
            Long skuStatusCount = prdtSkuMapper.selectSkuCount(new PrdtSkuRespVO(PRDT_STATUS_0, skuList));
            if(skuStatusCount > NumberPool.INT_ZERO){
                throw exception(PRDT_AREA_ITEM_SHELF_STATUS_SKU_STATIS);
            }
        }
        // 循环判断上架
        itemList.stream().forEach(item -> {
            // 可能因为其他的单位已经下架了, 这次下架导致整个SKU 是下架状态
            Integer tempShelfStatus =
                    (minShelfStatus > NumberPool.LOWER_GROUND   ? minShelfStatus : item.getMinShelfStatus())
                            + (midShelfStatus > NumberPool.LOWER_GROUND ? midShelfStatus : item.getMidShelfStatus())
                            + (largeShelfStatus > NumberPool.LOWER_GROUND ? largeShelfStatus : item.getLargeShelfStatus());
            Integer updateShelfStatus = tempShelfStatus > NumberPool.INT_ZERO ? NumberPool.INT_ONE : NumberPool.INT_ZERO;
            // 判断是否存在单位再操作
            SpuDTO spuDTO = productCacheService.getSpuDTO(item.getSpuId());
            Integer updateMinShelfStatus = ToolUtil.isNotEmpty(spuDTO.getMinUnit()) || minShelfStatus == NumberPool.INT_ZERO ? minShelfStatus : NumberPool.INT_ZERO;
            Integer updateMidShelfStatus = ToolUtil.isNotEmpty(spuDTO.getMidUnit()) || midShelfStatus == NumberPool.INT_ZERO ? midShelfStatus : NumberPool.INT_ZERO;
            Integer updateLargeShelfStatus = ToolUtil.isNotEmpty(spuDTO.getLargeUnit()) || largeShelfStatus == NumberPool.INT_ZERO ? largeShelfStatus : NumberPool.INT_ZERO;
            prdtSupplierItemMapper.updatePrdtSupplierItemShelfStatus(new Long[]{item.getSupplierItemId()}, updateMinShelfStatus, updateMidShelfStatus, updateLargeShelfStatus, updateShelfStatus);
        });
        return itemList;
    }

    /**
     * 删除入驻商上架商品
     *
     * @param supplierItemId 入驻商上架商品id
     */
    @Override
    public void deletePrdtSupplierItem(Long supplierItemId) {
        // 删除
        prdtSupplierItemMapper.deleteById(supplierItemId);
    }

    /**
     * 批量删除入驻商上架商品
     *
     * @param supplierItemIds 需要删除的入驻商上架商品主键
     * @return 结果
     */
    @Override
    public void deletePrdtSupplierItemBySupplierItemIds(Long[] supplierItemIds) {
        for(Long supplierItemId : supplierItemIds){
            this.deletePrdtSupplierItem(supplierItemId);
        }
    }

    /**
     * 获得入驻商上架商品
     *
     * @param supplierItemId 入驻商上架商品id
     * @return 入驻商上架商品
     */
    @Override
    public PrdtSupplierItemRespVO getPrdtSupplierItem(Long supplierItemId) {
        PrdtSupplierItem supplierItem = prdtSupplierItemMapper.selectById(supplierItemId);
        PrdtSupplierItemRespVO respVO = HutoolBeanUtils.toBean(supplierItem, PrdtSupplierItemRespVO.class);
        //获取分类信息
        respVO.setSaleClassLevelName(getClassLevelName(respVO.getSaleClassId()));
        return respVO;
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtSupplierItemPageRespVO> getPrdtSupplierItemPage(PrdtSupplierItemPageReqVO pageReqVO) {
        Page<PrdtSupplierItemPageReqVO> page = new Page<>(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        if(ToolUtil.isNotEmpty(pageReqVO.getSpuId())){
            pageReqVO.setSpuIdList(ListUtil.toList(pageReqVO.getSpuId()));
        }
        // 加载促销范围限制
        activityService.loadActivityScopeByItemPage(pageReqVO);
        Page<PrdtSupplierItemPageRespVO> supplierItemPage = prdtSupplierItemMapper.selectPageSupplierItem(pageReqVO, page);
        //组装商品名称、设置入驻商名称
        supplierItemPage.getRecords().forEach(record -> {
            //商品名称
            record.setSpuName(PropertyAndValDTO.getPropertiesSpuName(record.getProperties(),record.getSpuName()));
            //设置入驻商名称
            SupplierDTO supplierDTO = productCacheService.getSupplierDTO(record.getSupplierId());
            if (ToolUtil.isNotEmpty(supplierDTO)) {
                record.setSupplierName(supplierDTO.getSupplierName());
            }
        });
        return new PageResult<>(supplierItemPage.getRecords(),supplierItemPage.getTotal());
    }


    @Override
    public PrdtSupplierItem getBySupplierItemId(Long supplierItemId) {
        return prdtSupplierItemMapper.selectById(supplierItemId);
    }

    @Override
    public List<SupplierItemDTO> getSupplierItemListByApi(SupplierItemDTO itemDTO) {
        return prdtSupplierItemMapper.selectSupplierItemListByApi(itemDTO);
    }

    @Override
    public void cacheEvent(List<PrdtSupplierItem> supplierItems) {
        //刷新ES
        productEventService.acceptEvent(EsProductEventBuild.spuEvent(new ArrayList<>(supplierItems.stream().map(PrdtSupplierItem::getSpuId).collect(Collectors.toSet()))));
        //清除缓存
        supplierItemDTOCache.removeAll(supplierItems.stream().map(PrdtSupplierItem::getSupplierItemId).collect(Collectors.toSet()));
        // 重新加载商品详情缓存
        Map<Long, List<PrdtSupplierItem>> spuMap = supplierItems.stream().collect(Collectors.groupingBy(PrdtSupplierItem::getSpuId));
        // 清理上架商品sku组
        spuMap.forEach((spuId, spuList) -> spuList.forEach(supplierItem -> productCacheService.setSkuUnitGroup(spuId, NumberPool.LOWER_GROUND_LONG, supplierItem.getSaleClassId(), ProductType.GLOBAL)));
        // 初始化库存
        for (PrdtSupplierItem item : supplierItems) {
            // 没有库存需要初始化库存
            if (!redisStockService.hashSkuStock(item.getSkuId())) {
                redisStockService.setSkuStock(item.getSkuId(), prdtSkuMapper.selectPrdtSkuByStock(item.getSkuId()));
            }
        }
    }

    @Override
    public List<PrdtSupplierItem> getSaleQtyTotalList(Long minSupplierItemId) {
        return prdtSupplierItemMapper.selectSaleQtyTotalList(minSupplierItemId);
    }

	@Override
    @Transactional
    @SuppressWarnings("all")
    public List<PrdtSupplierItem> batchEditItemSaleClassId(PrdtSupplierItemSaveReqVO updateReqVO) {
        ArrayList<PrdtSupplierItem> eventItems = new ArrayList<>();
        if(ToolUtil.isEmpty(updateReqVO.getSupplierItemIds()))throw exception(PRDT_SUPPLIER_ITEM_ID_NOT_NULL);
        for (Long supplierItemId : updateReqVO.getSupplierItemIds()){
            PrdtSupplierItem supplierItem = prdtSupplierItemMapper.selectById(supplierItemId);
            eventItems.add(supplierItem);
            // 查询新的展示类目有没有已经存在的商品
            PrdtSupplierItem newSupplierItem = prdtSupplierItemMapper.selectBySkuAndSaleId(supplierItem.getSkuId(), updateReqVO.getSaleClassId());
            if (Objects.nonNull(newSupplierItem)) {
                // 在新的展示类目存在
                // 1. 同步新展示分类的上架状态
                newSupplierItem.setShelfDate(DateUtils.getNowDate());
                newSupplierItem.setShelfStatus(supplierItem.getShelfStatus());
                newSupplierItem.setMinShelfStatus(supplierItem.getMinShelfStatus());
                newSupplierItem.setMidShelfStatus(supplierItem.getMidShelfStatus());
                newSupplierItem.setLargeShelfStatus(supplierItem.getLargeShelfStatus());
                prdtSupplierItemMapper.updateById(newSupplierItem);

                // 2. 下架当前的商品数据
                supplierItem.setShelfStatus(NumberPool.INT_ZERO);
                supplierItem.setMinShelfStatus(NumberPool.INT_ZERO);
                supplierItem.setMidShelfStatus(NumberPool.INT_ZERO);
                supplierItem.setLargeShelfStatus(NumberPool.INT_ZERO);
                prdtSupplierItemMapper.updateById(supplierItem);
            } else {
                // 新的上架展示分类没有冲突的, 直接修改展示分类
                supplierItem.setSaleClassId(updateReqVO.getSaleClassId());
                prdtSupplierItemMapper.updateById(supplierItem);
            }
        }
        // 修改展示分类, 必须需要删除上架信息
        // 然后触发重新刷新上架信息
        productEventService.executeEvent(EsProductEventBuild.removeItem(updateReqVO.getSupplierItemIds()));
        return eventItems;
    }

    @Override
    public PrdtSupplierItem getSupplierItemBySpuCombineId(Long spuCombineId) {
        if(ToolUtil.isNotEmpty(spuCombineId)){
            PrdtSupplierItem supplierItem = prdtSupplierItemMapper.selectBySpuCombineId(spuCombineId);
            if(ToolUtil.isNotEmpty(supplierItem)){
                return supplierItem;
            }
        }
        return new PrdtSupplierItem();
    }

    @Override
    public void updatePrdtSupplierCombineItem(SupplierItemDTO supplierItemDTO) {
        prdtSupplierItemMapper.updateById(HutoolBeanUtils.toBean(supplierItemDTO, PrdtSupplierItem.class));
    }

    @Override
    public PrdtSupplierItem getSupplierItemByActivityId(Long activityId) {
        if(ToolUtil.isNotEmpty(activityId)){
            PrdtSupplierItem supplierItem = prdtSupplierItemMapper.getSupplierItemByActivityId(activityId);
            if(ToolUtil.isNotEmpty(supplierItem)){
                return supplierItem;
            }
        }
        return null;
    }

    private void validatePrdtSupplierItemExists(Long supplierItemId) {
        if (prdtSupplierItemMapper.selectById(supplierItemId) == null) {
            throw exception(PRDT_SUPPLIER_ITEM_NOT_EXISTS);
        }
    }

    /**
     * 校验平台展示分类是否是三级分类
     * @param saleClassId
     */
    public void checkClassLevel(Long saleClassId){
        if(saleClassId == null){
            throw exception(PRDT_SALE_CLASS_IS_NULL);
        }
        PrdtSaleClass prdtSaleClass = prdtSaleClassMapper.selectById(saleClassId);
        if(ToolUtil.isEmpty(prdtSaleClass)){
            throw exception(PRDT_SALE_CLASS_NOT_EXISTS);
        }
        if(prdtSaleClass.getLevel() != INT_THREE){
            throw exception(PRDT_SALE_CLASS_LEVEL_NOT_MAKE);
        }
    }

    /**
     * 获取分类各级信息
     * @param saleClassId
     * @return
     */
    public String getClassLevelName(Long saleClassId){
        PrdtSaleClass prdtSaleClass = prdtSaleClassMapper.selectById(saleClassId);
        //校验当前商品是否是三级展示分类
        if(prdtSaleClass.getLevel() != INT_THREE){
            throw exception(PRDT_SALE_CLASS_LEVEL_NOT_MAKE);
        }

        //获取当前分类的一级、二级分类信息
        //获取二级
        PrdtSaleClass secondSaleClass = prdtSaleClassMapper.selectById(prdtSaleClass.getPid());
        if(ToolUtil.isEmpty(secondSaleClass)){
            throw exception(PRDT_SALE_CLASS_NOT_SECOND_LEVEL_EXISTS);
        }

        //获取一级
        PrdtSaleClass firstSaleClass = prdtSaleClassMapper.selectById(secondSaleClass.getPid());
        if(ToolUtil.isEmpty(secondSaleClass)){
            throw exception(PRDT_SALE_CLASS_NOT_FIRST_LEVEL_EXISTS);
        }

        //组装分类
        StringJoiner level = new StringJoiner(">>");
        level.add(firstSaleClass.getName());
        level.add(secondSaleClass.getName());
        level.add(prdtSaleClass.getName());

        return level.toString();
    }


    /**
     * 上架发布前置校验
     * @param supplierItemList
     */
    public void releaseBeforeValidate(List<PrdtSupplierItem> supplierItemList) {
        // 验证允不允许使用本地商品
        AppletBaseConfigDTO appletBaseConfigDTO = productCacheService.getAppletBaseConfigDTO(SecurityUtils.getLoginUser().getSysCode());
        if (Objects.nonNull(appletBaseConfigDTO) && StringPool.ONE.equals(appletBaseConfigDTO.getSaleClassSwitch())) {
            throw exception(SYS_PARTNER_CONFIG_GLOBAL_DISABLE);
        }
        // 验证上架利润比例
        PrdtSkuSaleTotalRateVO skuSaleTotalRate = skuService.getSkuSaleTotalRate(
                PrdtSkuSaleTotalRateReqVO
                        .builder()
                        .skuIdList(supplierItemList.stream().map(PrdtSupplierItem::getSkuId).distinct().collect(Collectors.toList()))
                        .sysCode(SecurityUtils.getLoginUser().getSysCode())
                        .build()
        );
        // 查询平台商默认配置的比例
        PayConfigDTO payConfigDTO = productCacheService.getPayConfigDTO(SecurityUtils.getLoginUser().getSysCode());

        supplierItemList.forEach(item -> {
            SkuDTO skuDTO = productCacheService.getSkuDTO(item.getSkuId());
            SpuDTO prdtSpu = productCacheService.getSpuDTO(skuDTO.getSpuId());
            if (Objects.isNull(prdtSpu)) {
                throw exception(PRDT_SPU_SPU_ID_EXISTS);
            }
            if (Objects.isNull(prdtSpu.getCatgoryId())) {
                throw exception(PRDT_SPU_CATEGORY_NOT_EXISTS, prdtSpu.getSpuName());
            }

            // B2B支付, 利润比例最大29%
            if (Objects.nonNull(payConfigDTO) && PayChannelEnum.isB2b(payConfigDTO.getStoreOrderPayPlatform())) {
                if (StringPool.ONE.equals(payConfigDTO.getProfitModel())) {
                    // 销售 - 利润
                    if (Objects.nonNull(prdtSpu.getMinUnit())) {
                        // 小单位
                        if (NumberUtil.isGreater(skuDTO.getMarkPrice().subtract(skuDTO.getCostPrice()).divide(skuDTO.getMarkPrice(), 4, RoundingMode.HALF_UP), NumberPool.B2B_PAY_NAX_RATE)) {
                            throw exception(PRDT_SPU_MAX_PROFIT_RATE);
                        }
                    }
                    if (Objects.nonNull(prdtSpu.getMidUnit())) {
                        // 中单位
                        if (NumberUtil.isGreater(skuDTO.getMidMarkPrice().subtract(skuDTO.getMidCostPrice()).divide(skuDTO.getMidMarkPrice(), 4, RoundingMode.HALF_UP), NumberPool.B2B_PAY_NAX_RATE)) {
                            throw exception(PRDT_SPU_MAX_PROFIT_RATE);
                        }
                    }
                    if (Objects.nonNull(prdtSpu.getLargeUnit())) {
                        // 大单位
                        if (NumberUtil.isGreater(skuDTO.getLargeMarkPrice().subtract(skuDTO.getLargeCostPrice()).divide(skuDTO.getLargeMarkPrice(), 4, RoundingMode.HALF_UP), NumberPool.B2B_PAY_NAX_RATE)) {
                            throw exception(PRDT_SPU_MAX_PROFIT_RATE);
                        }
                    }
                } else {
                    // 销售 * 比例
                    PrdtSkuSaleTotalRateVO.RateConfig skuRate = skuSaleTotalRate.getSkuRate(item.getSkuId());
                    if (Objects.nonNull(skuRate) && NumberUtil.isGreater(skuRate.getRate(), NumberPool.B2B_PAY_NAX_RATE)) {
                        throw exception(PRDT_SPU_MAX_PROFIT_RATE);
                    }
                }
            }
        });
    }

    @Override
    public String impordData(List<PrdtSupplierItemImportExcel> prdtSupplierList) {
        String resultMessage = "";
            int successNum = 0;
            int failureNum = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();
            List<PrdtAreaItem> result = new ArrayList<>();
            try {
                if (prdtSupplierList.isEmpty()) {
                    // 如果导入数据为空，则不进行数据导入
                    throw exception(THE_PLATFORM_MANAGEMENT_CATEGORY_IS_EMPTY);
                }
                Map<String, Long> prdtSupplierMap = new HashMap<>(); // 存储已插入的类别，避免重复插入
                // 数据校验
                for (int line = 0; line < prdtSupplierList.size(); line++) {
                    if (failureMsg.length() > 2000) {
                        break;
                    }
                    int cellNumber = line + 3;
                    PrdtSupplierItemImportExcel itemData = prdtSupplierList.get(line);
                    prdtSupplierImportExcel(itemData, cellNumber);
                }
                List<PrdtSupplierItem> supplierItemList = HutoolBeanUtils.toBean(prdtSupplierList, PrdtSupplierItem.class);
                //校验
                PrdtSupplierItem supplierItem = supplierItemList.get(0);
                //校验商品城市展示类别 是否是第三级
                checkClassLevel(supplierItem.getSaleClassId());
                releaseBeforeValidate(supplierItemList);
                supplierItemList.stream().forEach(item -> {
                    // 根据sku找到spu再找到入驻商
                    SkuDTO skuDTO = productCacheService.getSkuDTO(item.getSkuId());
                    SpuDTO spuDTO = productCacheService.getSpuDTO(skuDTO.getSpuId());
                    if (ToolUtil.isNotEmpty(spuDTO)) {
                        item.setSpuId(spuDTO.getSpuId());
                        item.setSupplierId(spuDTO.getSupplierId());
                    }
                });
                for (PrdtSupplierItem item : supplierItemList) {
                    try {
                        //校验
                        PrdtSupplierItem checkItem = prdtSupplierItemMapper.selectSupplierItemByCheck(item);
                        PrdtSpu prdtSpu = prdtSpuMapper.selectById(item.getSpuId());
                        if (Objects.isNull(checkItem)) {
                            checkItem = item;
                            //获取当前城市商品类型的最大排序号
                            Integer sortNum = prdtSupplierItemMapper.selectMaxSort(item);
                            checkItem.setSortNum(sortNum + Code.GREATER_THAN);
                        }
                        // 调整上架状态
                        checkItem.setShelfDate(DateUtils.getNowDate());
                        checkItem.setSaleClassId(item.getSaleClassId());
                        // 小单位一定有
                        checkItem.setMinShelfStatus(item.getMinShelfStatus());
                        // 中单位和大单位不一定有
                        if (ToolUtil.isNotEmpty(item.getMidShelfStatus())){
                            // 查询商品是否有对应的单位
                            if (ToolUtil.isEmpty(prdtSpu.getMidUnit()) && Objects.equals(item.getMidShelfStatus(), PRDT_SHELF_STATUS_1)){
                                failureNum++;
                                throw new Exception(StringUtils.format("{}商品没有中单位无法上下架",prdtSpu.getSpuName() ));
                            }
                            checkItem.setMidShelfStatus(item.getMidShelfStatus());
                        }
                        if (ToolUtil.isNotEmpty(item.getLargeShelfStatus())){
                            // 查询商品是否有对应的单位
                            if (ToolUtil.isEmpty(prdtSpu.getLargeUnit()) && Objects.equals(item.getLargeShelfStatus(), PRDT_SHELF_STATUS_1)){
                                failureNum++;
                                throw new Exception(StringUtils.format("{}商品没有大单位无法上下架",prdtSpu.getSpuName() ));
                            }
                            checkItem.setLargeShelfStatus(item.getLargeShelfStatus());
                        }
                        if (Objects.equals(item.getMinShelfStatus(), PRDT_SHELF_STATUS_0) && Objects.equals(item.getMidShelfStatus(), PRDT_SHELF_STATUS_0) && Objects.equals(item.getLargeShelfStatus(), PRDT_SHELF_STATUS_0)){
                            checkItem.setShelfStatus(PRDT_SHELF_STATUS_0);
                        }else {
                            checkItem.setShelfStatus(PRDT_SHELF_STATUS_1);
                        }
                        prdtSupplierItemMapper.insertOrUpdate(checkItem);
                        prdtSupplierItemZipService.insertPrdtSupplierItemZip(item);
                        successNum++;
                    }catch (Exception e){
                        failureMsg.append(StringUtils.format("<br/>skuId数据导入失败，错误信息：{}。", e.getMessage()));
                    }
                }
                this.cacheEvent(supplierItemList); // 刷新缓存es
            } catch (Exception e) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>skuId数据导入失败，错误信息：{}。", e.getMessage()));
            }
            if (failureNum > 0) {
                resultMessage = String.format("共导入%d条，成功%d条，失败%d条。失败原因如下：", prdtSupplierList.size(), successNum, failureNum)
                        + failureMsg.toString();
                throw new ServiceException(resultMessage);
            } else {
                resultMessage = String.format("恭喜您，数据已全部导入成功！共 %d 条", successNum);
            }
        return resultMessage;
    }

    private void prdtSupplierImportExcel(PrdtSupplierItemImportExcel itemData, int cellNumber) throws Exception {
        if (ToolUtil.isEmpty(itemData.getSaleClassId())){
            throw new Exception(StringUtils.format("第{}行数据上架三级展示分类编号不能为空,请先修改数据再导入", cellNumber));
        }
        if (ToolUtil.isEmpty(itemData.getSkuId())){
            throw new Exception(StringUtils.format("第{}行数据sku不能为空,请先修改数据再导入", cellNumber));
        }
        if (ToolUtil.isEmpty(itemData.getMinShelfStatus())){
            throw new Exception(StringUtils.format("第{}行数据小单位上架状态不能为空,请先修改数据再导入", cellNumber));
        }
        if (ToolUtil.isEmpty(itemData.getMidShelfStatus())){
            throw new Exception(StringUtils.format("第{}行数据中单位上架状态不能为空,请先修改数据再导入", cellNumber));
        }
        if (ToolUtil.isEmpty(itemData.getLargeShelfStatus())){
            throw new Exception(StringUtils.format("第{}行数据大单位上架状态不能为空,请先修改数据再导入", cellNumber));
        }
        // 校验展示分类是否存在
        PrdtSaleClass prdtSaleClass = prdtSaleClassMapper.selectById(itemData.getSaleClassId());
        if (ToolUtil.isEmpty(prdtSaleClass)) {
            throw new Exception(StringUtils.format("第{}行数据上架三级展示分类编号不存在！请先修改数据再导入", cellNumber));
        }
        SkuDTO skuDTO = productCacheService.getSkuDTO(itemData.getSkuId());
        if (ToolUtil.isEmpty(skuDTO)){
            throw new Exception(StringUtils.format("第{}行数据skuId不存在！请先修改数据再导入", cellNumber));
        }


    }
}
