package com.zksr.product.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.constant.EsIndexNameConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StockUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsGlobalProduct;
import com.zksr.common.elasticsearch.domain.EsLocalProduct;
import com.zksr.common.elasticsearch.domain.EsProduct;
import com.zksr.common.elasticsearch.domain.EsProductGroup;
import com.zksr.common.elasticsearch.mapper.EsGlobalProductFullMapper;
import com.zksr.common.elasticsearch.mapper.EsLocalProductFullMapper;
import com.zksr.common.elasticsearch.mapper.EsProductMapper;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.common.elasticsearch.util.EsProductConvertUtil;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.combine.SpuCombineDtlDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.controller.product.vo.ProductContentPageReqVO;
import com.zksr.product.controller.product.vo.ProductContentRespVO;
import com.zksr.product.domain.dto.EsCbProductLoadReqDTO;
import com.zksr.product.domain.dto.EsProductLoadReqDTO;
import com.zksr.product.mapper.ProductDataMapper;
import com.zksr.product.service.IProductCacheService;
import com.zksr.product.service.IProductContentService;
import com.zksr.promotion.api.activity.dto.ActivityBranchScopeDTO;
import com.zksr.promotion.api.activity.dto.ActivityChannelScopeDTO;
import com.zksr.promotion.api.activity.dto.PrmActivityDTO;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品内容提供 service
 * @date 2024/3/2 16:29
 */
@Slf4j
@Service
public class ProductContentServiceImpl implements IProductContentService {

    @Autowired
    private EsProductService esProductService;

    @Autowired
    protected ProductDataMapper productDataMapper;

    @Autowired
    private RedisStockService redisStockService;

    @Resource
    private EsLocalProductFullMapper localProductFullMapper;

    @Resource
    private EsGlobalProductFullMapper globalProductFullMapper;

    @Resource
    private EsProductMapper esProductMapper;

    @Autowired
    protected IProductCacheService productCacheService;

    @Override
    public List<ProductContentRespVO> getElasticSearchList(ProductContentPageReqVO pageReqVo) {
        EsPageInfo<EsProductGroup> search = esProductService.search(pageReqVo.convert(), false);
        return HutoolBeanUtils.toBean(search.getList(), ProductContentRespVO.class);
    }

    @Override
    public List<ProductContentRespVO> getElasticSearchAreaItemList(ProductContentPageReqVO pageReqVo) {
        List<EsLocalProduct> esLocalProducts = esProductService.searchAreaItemList(pageReqVo.convert());
        ArrayList<ProductContentRespVO> resultList = new ArrayList<>();
        if (Objects.nonNull(esLocalProducts)) {
            for (EsLocalProduct productGroup : esLocalProducts) {
                ProductContentRespVO bean = HutoolBeanUtils.toBean(productGroup, ProductContentRespVO.class);
                bean.setClassId(productGroup.getClass3Id());
                resultList.add(bean);
            }
        }
        return resultList;
    }

    @Override
    public PageResult<ProductContentRespVO> getPageElasticSearchList(ProductContentPageReqVO pageReqVo) {
        EsPageInfo<EsProductGroup> search = esProductService.search(pageReqVo.convert(), false);
        List<EsProductGroup> list = search.getList();
        ArrayList<ProductContentRespVO> resultList = new ArrayList<>();
        if (Objects.nonNull(list)) {
            for (EsProductGroup productGroup : list) {
                ProductContentRespVO bean = HutoolBeanUtils.toBean(productGroup, ProductContentRespVO.class);
                bean.setClassId(productGroup.getClass3Id());
                resultList.add(bean);
            }
        }
        return new PageResult<>(resultList, search.getTotal());
    }

    @Override
    public List<ProductContentRespVO> getElasticSearchFullItemList(ProductContentPageReqVO reqVO) {
        List<EsProduct> list = esProductService.searchFullList(reqVO.convert());
        ArrayList<ProductContentRespVO> resultList = new ArrayList<>();
        if (Objects.nonNull(list)) {
            for (EsProduct productGroup : list) {
                ProductContentRespVO bean = HutoolBeanUtils.toBean(productGroup, ProductContentRespVO.class);
                bean.setClassId(productGroup.getClass3Id());
                resultList.add(bean);
            }
        }
        return resultList;
    }

    @Override
    public List<Long> getAreaItemReleaseThreeAreaClassList(Long areaId) {
        return esProductService.getAreaItemReleaseThreeAreaClassList(areaId);
    }

    @Override
    public List<Long> getAreaItemReleaseThreeCategoryList(Long areaId) {
        return esProductService.getAreaItemReleaseThreeCategoryList(areaId);
    }

    @Override
    public List<Long> getGloablItemReleaseThreeClassList(Long sysCode) {
        return esProductService.getGloablItemReleaseThreeClassList(sysCode);
    }

    @Override
    public List<Long> getGloablItemReleaseThreeCategoryList(Long sysCode) {
        return esProductService.getGloablItemReleaseThreeCategoryList(sysCode);

    }

    @Override
    public void refreshProduct(Integer initIndex) {
        // 是否需要重建索引
        if (Objects.nonNull(initIndex) && initIndex == 1) {
            // 删除全部索引数据
            try {
                localProductFullMapper.deleteIndex(EsIndexNameConstants.LOCAL_PRODUCT);
            } catch (Exception e) {
                log.error("删除本地商品索引失败");
            }
            try {
                globalProductFullMapper.deleteIndex(EsIndexNameConstants.GLOBAL_PRODUCT);
            } catch (Exception e) {
                log.error("删除全国商品索引失败");
            }
            try {
                esProductMapper.deleteIndex(EsIndexNameConstants.PRODUCT_GROUP);
            } catch (Exception e) {
                log.error("删除全国本地商品索引失败");
            }
            // 重建索引
            try {
                localProductFullMapper.createIndex();
            } catch (Exception e) {
                log.error("删除本地商品索引失败");
            }
            try {
                globalProductFullMapper.createIndex();
            } catch (Exception e) {
                log.error("删除全国商品索引失败");
                ;
            }
            try {
                esProductMapper.createIndex();
            } catch (Exception e) {
                log.error("删除全国本地商品索引失败");
            }
        }
        // 本地商品
        EsProductLoadReqDTO reqDTO = new EsProductLoadReqDTO();
        reqDTO.setMinId(-1L);
        for (; ; ) {
            List<EsLocalProduct> fullProducts = productDataMapper.selectByLocal(reqDTO);
            if (fullProducts.isEmpty()) {
                break;
            }
            for (EsLocalProduct product : fullProducts) {
                product.setSaleQty(redisStockService.getSkuSaledQty(product.getSkuId()).longValue());
                product.setStock(redisStockService.getSurplusSaleQtyBigDecimal(product.getSkuId()));
            }
            esProductService.saveLocalFull(fullProducts);
            // 转换areaId_SPU唯一
            esProductService.saveProduct(EsProductConvertUtil.getLocalSpuUniqueList(fullProducts));
            reqDTO.setMinId(fullProducts.get(fullProducts.size() - 1).getItemId());
        }

        // 全国商品
        reqDTO.setMinId(-1L);
        for (; ; ) {
            List<EsGlobalProduct> fullProducts = productDataMapper.selectByGlobal(reqDTO);
            if (fullProducts.isEmpty()) {
                break;
            }
            for (EsGlobalProduct product : fullProducts) {
                product.setSaleQty(redisStockService.getSkuSaledQty(product.getSkuId()).longValue());
                product.setStock(redisStockService.getSurplusSaleQtyBigDecimal(product.getSkuId()));
            }
            esProductService.saveGlobalFull(fullProducts);
            // 转换SPU唯一
            esProductService.saveProduct(EsProductConvertUtil.getGlobalSpuUniqueList(fullProducts));
            reqDTO.setMinId(fullProducts.get(fullProducts.size() - 1).getItemId());
        }

        // 组合商品 (本地)
        EsCbProductLoadReqDTO combineReq = new EsCbProductLoadReqDTO();
        combineReq.setMinId(-1L);
        for (; ; ) {
            List<EsProductGroup> fullProducts = productDataMapper.selectCbProductByLocal(combineReq);
            if (fullProducts.isEmpty()) {
                break;
            }
            this.renderSpuCombineList(fullProducts);
            // 写入商品到ES
            esProductService.saveProduct(fullProducts);
            combineReq.setMinId(fullProducts.get(fullProducts.size() - 1).getItemId());
        }

        // 组合商品 (全国)
        combineReq.setMinId(-1L);
        for (; ; ) {
            List<EsProductGroup> fullProducts = productDataMapper.selectCbProductByGlobal(combineReq);
            if (fullProducts.isEmpty()) {
                break;
            }
            this.renderSpuCombineList(fullProducts);
            // 写入商品到ES
            esProductService.saveProduct(fullProducts);
            combineReq.setMinId(fullProducts.get(fullProducts.size() - 1).getItemId());
        }
    }

    @Override
    public void renderSpuCombineList(List<EsProductGroup> cbProductList) {

        // 查询组合促销库存
        cbProductList.stream().collect(Collectors.groupingBy(EsProductGroup::getSpuCombineId)).forEach((spuCombineId, itemList) -> {
            SpuCombineDTO spuCombineDTO = productCacheService.getSpuCombineDTO(spuCombineId);
            BigDecimal minStock = this.getSpuCombineMinStock(spuCombineDTO);
            itemList.forEach(item -> item.setStock(minStock));
        });

        // 计算默认信息, 和是否还有库存
        for (EsProductGroup productGroup : cbProductList) {
            // 是否还有库存,1-没有了,0-还有库存
            productGroup.setNotStock(NumberPool.INT_ZERO);
            // 没有库存, 或者小于1份库存
            if (Objects.isNull(productGroup.getStock()) || StockUtil.isGreater(BigDecimal.ONE, productGroup.getStock())) {
                productGroup.setNotStock(NumberPool.INT_ONE);
            }
            if (Objects.nonNull(productGroup.getAreaId())) {
                productGroup.setAreaId(NumberPool.LOWER_GROUND_LONG);
            }
            // 中单位上架状态
            productGroup.setMidShelfStatus(NumberPool.INT_ZERO);
            // 大单位上架状态
            productGroup.setLargeShelfStatus(NumberPool.INT_ZERO);
            productGroup.setSpuId(NumberPool.LOWER_GROUND_LONG);
            productGroup.setSkuId(NumberPool.LOWER_GROUND_LONG);
            //productGroup.setCatgoryId(NumberPool.LOWER_GROUND_LONG);
            productGroup.setChannelId(NumberPool.LOWER_GROUND_LONG);
            productGroup.setGroupId(NumberPool.LOWER_GROUND_LONG);
            // 单位,1-小单位,2-中单位,3-大单位
            productGroup.setUnitSize(NumberPool.INT_ONE);
            // 是否多单位
            productGroup.setIsSpecs(NumberPool.LONG_ZERO);
            // 商品类型 0-普通商品, 1-组合商品
            productGroup.setItemType(NumberPool.INT_ONE);
        }

        // 查询组合商品, 参与门店, 渠道
        /*final Integer WHITE = NumberPool.INT_ONE;
        final Integer BLACK = NumberPool.INT_ZERO;
        // 加载黑白名单数据
        for (EsProductGroup productGroup : cbProductList) {
            // 一个组合促销商品, 在上架数据里面只会对应一个活动
            PrmActivityDTO activityDTO = productCacheService.getActivityDTO(productGroup.getActivityId());
            if (Objects.nonNull(activityDTO)) {
                // 门店黑白名单
                if (ObjectUtil.isNotEmpty(activityDTO.getBranchScopes())) {
                    // 1-白名单 0-黑名单
                    Map<Integer, List<ActivityBranchScopeDTO>> blackWhiteMap = activityDTO.getBranchScopes().stream().collect(Collectors.groupingBy(ActivityBranchScopeDTO::getWhiteOrBlack));
                    // 白名单
                    if (blackWhiteMap.containsKey(WHITE)) {
                        productGroup.setWhiteBranchList(blackWhiteMap.get(WHITE).stream().map(ActivityBranchScopeDTO::getBranchId).collect(Collectors.toList()));
                        //productGroup.getWhiteBranchList().add(NumberPool.LOWER_GROUND_LONG);
                    }
                    // 黑名单
                    if (blackWhiteMap.containsKey(BLACK)) {
                        productGroup.setBlackBranchList(blackWhiteMap.get(BLACK).stream().map(ActivityBranchScopeDTO::getBranchId).collect(Collectors.toList()));
                    }
                }
                // 渠道黑白名单
                if (ObjectUtil.isNotEmpty(activityDTO.getChannelScopes())) {
                    // 1-白名单 0-黑名单
                    Map<Integer, List<ActivityChannelScopeDTO>> blackWhiteMap = activityDTO.getChannelScopes().stream().collect(Collectors.groupingBy(ActivityChannelScopeDTO::getWhiteOrBlack));
                    // 白名单
                    if (blackWhiteMap.containsKey(WHITE)) {
                        productGroup.setWhiteChannelList(blackWhiteMap.get(WHITE).stream().map(ActivityChannelScopeDTO::getChannelId).collect(Collectors.toList()));
                        //productGroup.getWhiteChannelList().add(NumberPool.LOWER_GROUND_LONG);
                    }
                    // 黑名单
                    if (blackWhiteMap.containsKey(BLACK)) {
                        productGroup.setBlackChannelList(blackWhiteMap.get(BLACK).stream().map(ActivityChannelScopeDTO::getChannelId).collect(Collectors.toList()));
                    }
                }
            }
        }*/
    }


    /**
     * 获取组合商品最小库存数
     *
     * @param spuCombineDTO
     * @return 组合商品最小库存
     */
    @Override
    public BigDecimal getSpuCombineMinStock(SpuCombineDTO spuCombineDTO) {
        List<BigDecimal> stockList = new ArrayList<>();
        for (SpuCombineDtlDTO combineDtl : spuCombineDTO.getCombineDtls()) {
            SkuDTO skuDTO = productCacheService.getSkuDTO(combineDtl.getSkuId());
            SpuDTO spuDTO = productCacheService.getSpuDTO(skuDTO.getSpuId());
            BigDecimal skuStock = redisStockService.getSurplusSaleQtyBigDecimal(combineDtl.getSkuId());
            // 在当前组合条件下,能支持组合商品下单的次数
            BigDecimal unitSizeQty = spuDTO.getUnitSizeQty(combineDtl.getSkuUnitType());
            if (Objects.isNull(unitSizeQty)) {
                log.warn("获取组合商品最小有效库存异常,单位转换比例不存在 {}", combineDtl.getSpuCombineId());
                unitSizeQty = BigDecimal.ONE;
            }
            // 最小库存 / (条件数量 * 转换比例)
            BigDecimal maxQty = StockUtil.stockDivide(
                    skuStock,
                    StockUtil.stockMultiply(combineDtl.getQty(), unitSizeQty)
            );
            stockList.add(maxQty);
        }
        if (stockList.isEmpty()) {
            return BigDecimal.ZERO;
        }
        // 获取最小的库存
        stockList.sort(Comparator.comparing(BigDecimal::doubleValue));
        return stockList.get(0);
    }
}
