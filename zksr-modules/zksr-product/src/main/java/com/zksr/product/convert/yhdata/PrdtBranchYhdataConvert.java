package com.zksr.product.convert.yhdata;

import com.zksr.common.core.domain.vo.openapi.receive.CreateYhDataReqVO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsYhProduct;
import com.zksr.common.elasticsearch.model.dto.YhProductSearchDTO;
import com.zksr.product.api.yhdata.vo.YhBatchItemVO;
import com.zksr.product.api.yhdata.vo.YhBatchListReqVO;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataRespVO;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataSaveReqVO;
import com.zksr.product.domain.PrdtBranchYhdata;
import com.zksr.product.domain.po.YhMatchAreaItemPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 门店批量要货 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-12-09
*/
@Mapper
public interface PrdtBranchYhdataConvert {

    PrdtBranchYhdataConvert INSTANCE = Mappers.getMapper(PrdtBranchYhdataConvert.class);

    PrdtBranchYhdataRespVO convert(PrdtBranchYhdata prdtBranchYhdata);

    PrdtBranchYhdata convert(PrdtBranchYhdataSaveReqVO prdtBranchYhdataSaveReq);

    PageResult<PrdtBranchYhdataRespVO> convertPage(PageResult<PrdtBranchYhdata> prdtBranchYhdataPage);

    PrdtBranchYhdata convertPO(CreateYhDataReqVO.YhDtl yhDtl);

    EsYhProduct convertESProductVO(PrdtBranchYhdata yhdata, YhMatchAreaItemPO areaItemPO);

    YhProductSearchDTO convertESSearchDTO(YhBatchListReqVO reqVO);

    PageResult<YhBatchItemVO> convertESPage(PageResult<EsYhProduct> pageResult);

    List<EsYhProduct> convertESPO(List<PrdtBranchYhdata> updateList);

    EsYhProduct convertESPO(PrdtBranchYhdata item);
}