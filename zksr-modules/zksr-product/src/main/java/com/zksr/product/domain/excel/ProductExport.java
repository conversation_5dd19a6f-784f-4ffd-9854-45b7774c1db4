package com.zksr.product.domain.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品基本信息导出
 * @date 2024/9/6
 */
@Data
@ApiModel(description = "商品基本信息导出")
public class ProductExport {
    @Excel(name = "产品编号")
    private String spuNo;

    @Excel(name = "产品名称")
    private String spuName;

    @Excel(name = "入驻商名称")
    private String supplierName;

    @Excel(name = "一级类别名称")
    private String primaryCategoryName;

    @Excel(name = "二级类别名称")
    private String secondaryCategoryName;

    @Excel(name = "三级类别名称")
    private String tertiaryCategoryName;

    @Excel(name = "品牌名称")
    private String brandName;

    @Excel(name = "单位")
    private String unit;

    @Excel(name = "属性")
    private String properties;

    @Excel(name = "标准价")
    private BigDecimal price;

    @Excel(name = "库存")
    private BigDecimal stock;

    @Excel(name = "条码")
    private String barcode;

    @Excel(name = "状态", readConverterExp = "0=禁用,1=启用")
    private String status;

    /** 外部来源商品编号 */
    @Excel(name = "外部来源商品编号")
    private String sourceNo;

    /** SPU辅助的商品编号 */
    @Excel(name = "SPU辅助的商品编号")
    private String auxiliarySpuNo;
}
