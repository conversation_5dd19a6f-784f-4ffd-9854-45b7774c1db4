package com.zksr.product.service.impl;

import com.github.pagehelper.Page;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.enums.ApproveStateEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesRespVO;
import com.zksr.product.convert.adjustPrices.PrdtAdjustPricesDtlConvert;
import com.zksr.product.domain.PrdtAdjustPricesDtl;
import com.zksr.product.mq.ProductMqProducer;
import com.zksr.product.service.IPrdtAdjustPricesDtlService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtAdjustPricesMapper;
import com.zksr.product.convert.adjustPrices.PrdtAdjustPricesConvert;
import com.zksr.product.domain.PrdtAdjustPrices;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesPageReqVO;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesSaveReqVO;
import com.zksr.product.service.IPrdtAdjustPricesService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.constant.SystemConstants.FUNC_SCOPE_PARTNER;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.*;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 商品调价单主Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Service
@Slf4j
public class PrdtAdjustPricesServiceImpl implements IPrdtAdjustPricesService {
    @Autowired
    private PrdtAdjustPricesMapper prdtAdjustPricesMapper;


    @Autowired
    private IPrdtAdjustPricesDtlService prdtAdjustPricesDtlService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IProductCacheService productCacheService;

    @Autowired
    private ProductMqProducer productMqProducer;

    // 入驻商信息调用服务
    @Resource
    private SupplierApi remoteSupplierApi;


    /**
     * 新增商品调价单主
     *
     * @param createReqVO 创建信息
     * @param addType
     * @return 结果
     */
    @Override
    @Transactional
    public Long insertPrdtAdjustPrices(PrdtAdjustPricesSaveReqVO createReqVO, Integer addType) {
        //校验该调价商品的入驻商是否对接了第三方
        checkSupplierSync(createReqVO.getSupplierId());

        // 插入
        PrdtAdjustPrices prdtAdjustPrices = PrdtAdjustPricesConvert.INSTANCE.convert(createReqVO);
        prdtAdjustPrices.setAdjustPricesNo(SheetTypeConstants.TJ + redisService.getUniqueNumber());
        prdtAdjustPrices.setSkuNum((long) createReqVO.getDtlList().size());

        SupplierDTO supplierDTO = productCacheService.getSupplierDTO(prdtAdjustPrices.getSupplierId());
        if (ToolUtil.isNotEmpty(supplierDTO)) {
            prdtAdjustPrices.setDcId(supplierDTO.getDcId());
        }


        // 主单新增
        prdtAdjustPricesMapper.insert(prdtAdjustPrices);
        // 明细新增
        prdtAdjustPricesDtlService.insertPrdtAdjustPricesDtlBatch(prdtAdjustPrices, createReqVO.getDtlList(),addType);
        // 返回
        return prdtAdjustPrices.getAdjustPricesId();
    }

    /**
     * 修改商品调价单主
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional
    public void updatePrdtAdjustPrices(PrdtAdjustPricesSaveReqVO updateReqVO) {

        //校验该调价商品的入驻商是否对接了第三方
        checkSupplierSync(updateReqVO.getSupplierId());

        // 更新主表
        PrdtAdjustPrices prdtAdjustPrices = PrdtAdjustPricesConvert.INSTANCE.convert(updateReqVO);
        prdtAdjustPrices.setSkuNum((long) updateReqVO.getDtlList().size());

        prdtAdjustPricesMapper.updateById(prdtAdjustPrices);

        //校验
        prdtAdjustPricesDtlService.checkAddOrUpdateDtlList(updateReqVO.getDtlList(),prdtAdjustPrices.getSupplierId());

        // 更新明细
        prdtAdjustPricesDtlService.updatePrdtAdjustPricesDtlBatch(updateReqVO);
    }

    /**
     * 删除商品调价单主
     *
     * @param adjustPricesId 单据ID
     */
    @Override
    @Transactional
    public void deletePrdtAdjustPrices(Long adjustPricesId) {
        // 删除
        prdtAdjustPricesMapper.deleteById(adjustPricesId);
        // 删除明细
        prdtAdjustPricesDtlService.deletePrdtAdjustPricesDtlByAdjustPricesDtlId(adjustPricesId);
    }

    /**
     * 获得商品调价单主
     *
     * @param adjustPricesId 单据ID
     * @return 商品调价单主
     */
    @Override
    public PrdtAdjustPricesRespVO getPrdtAdjustPrices(Long adjustPricesId) {
        PrdtAdjustPrices prdtAdjustPrices = prdtAdjustPricesMapper.selectById(adjustPricesId);

        validatePrdtAdjustPricesExists(prdtAdjustPrices);

        List<PrdtAdjustPricesDtl> dtlList = prdtAdjustPricesDtlService.getPrdtAdjustPricesDtlList(adjustPricesId);


        if (ToolUtil.isEmpty(dtlList)) {
            throw exception(PRDT_ADJUST_PRICES_DTL_NOT_EXISTS);
        }

        //获取入驻商信息
        SupplierDTO supplierDTO = productCacheService.getSupplierDTO(prdtAdjustPrices.getSupplierId());

        PrdtAdjustPricesRespVO prdtAdjustPricesRespVO = PrdtAdjustPricesConvert.INSTANCE.convert(prdtAdjustPrices);
        prdtAdjustPricesRespVO.setDtlList(PrdtAdjustPricesDtlConvert.INSTANCE.convertRespVoList(dtlList));
        prdtAdjustPricesRespVO.getDtlList().forEach(dtl -> {
            SkuDTO skuDTO = productCacheService.getSkuDTO(dtl.getSkuId());
            //设置规格信息
            if (ToolUtil.isNotEmpty(skuDTO)) {
                dtl.setProperties(PropertyAndValDTO.getProperties(skuDTO.getProperties()));
            }

            SpuDTO spuDTO = productCacheService.getSpuDTO(dtl.getSpuId());
            if (ToolUtil.isNotEmpty(spuDTO)) {
                //设置商品编号
                dtl.setSpuNo(spuDTO.getSpuNo());

                //设置商品名称
                dtl.setSpuName(spuDTO.getSpuName());
            }

            if(ToolUtil.isNotEmpty(supplierDTO)){
                dtl.setSupplierName(supplierDTO.getSupplierName());
            }

        });
        return prdtAdjustPricesRespVO;
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtAdjustPricesRespVO> getPrdtAdjustPricesPage(PrdtAdjustPricesPageReqVO pageReqVO) {
        Page<PrdtAdjustPrices> page = PageUtils.startPage(pageReqVO);

        PageResult<PrdtAdjustPricesRespVO> pageResult =
                PrdtAdjustPricesConvert.INSTANCE.convertPage(new PageResult<>(prdtAdjustPricesMapper.selectListPage(pageReqVO), page.getTotal()));
        pageResult.getList().forEach(adjustPrices -> {

            SupplierDTO supplierDTO = productCacheService.getSupplierDTO(adjustPrices.getSupplierId());
            if (ToolUtil.isNotEmpty(supplierDTO)) {
                adjustPrices.setSupplierName(supplierDTO.getSupplierName());
            }
            DcDTO dcDTO = productCacheService.getDcDTO(adjustPrices.getDcId());
            if (ToolUtil.isNotEmpty(dcDTO)) {
                adjustPrices.setDcName(dcDTO.getDcName());
            }

        });

        return pageResult;
    }

    @Override
    @Transactional
    public void approve (Long adjustPricesId) {
        PrdtAdjustPrices prdtAdjustPrices = prdtAdjustPricesMapper.selectById(adjustPricesId);

        List<PrdtAdjustPricesDtl> dtlList = prdtAdjustPricesDtlService.getPrdtAdjustPricesDtlList(adjustPricesId);

        //校验
        validateApprove(prdtAdjustPrices,dtlList);

        //如果已经审核过的单据 则直接跳过
        if(prdtAdjustPrices.getApproveState().compareTo(Long.valueOf(StatusConstants.AUDIT_STATE_1)) == 0){
            log.info("已审核的调价单直接过滤掉,单号为：{}",prdtAdjustPrices.getAdjustPricesNo());
            return;
        }

        prdtAdjustPrices.setApproveBy(SecurityUtils.getLoginUser().getUsername());
        prdtAdjustPrices.setApproveUserId(SecurityUtils.getLoginUser().getUserid());
        prdtAdjustPrices.setApproveState(Long.valueOf(StatusConstants.AUDIT_STATE_1));
        prdtAdjustPrices.setApproveTime(new Date());

        //如果是定时生效 需要设置定时执行状态
        if(PRDT_ADJUST_VALID_TYPE_0.equals(prdtAdjustPrices.getValidType())){
            prdtAdjustPrices.setTaskExecuteStatus(PRDT_ADJUST_TASK_EXECUTE_STATUS_0);
        }
        prdtAdjustPricesMapper.updateById(prdtAdjustPrices);

        //如果是直接生效
        if(PRDT_ADJUST_VALID_TYPE_1.equals(prdtAdjustPrices.getValidType())){
            // 调整 调价单明细状态为 生效中
            prdtAdjustPricesDtlService.updatePrdtAdjustPricesDtlValidState(dtlList, NumberPool.LONG_ONE);

            dtlList.forEach(dtl -> {
                // 发送MQ 进行商品价格调整 处理
                productMqProducer.sendAdjustPricesDtlApprove(dtl.getAdjustPricesDtlId());
            });
        }
    }

    @Override
    public void operateValidJob(Long sysCode, Date validTime) {
        //查询该平台商下所有未执行的定时生效已审核的调价单
        List<PrdtAdjustPrices> pricesList = prdtAdjustPricesMapper.selectListByOperateValid(sysCode, PRDT_ADJUST_VALID_TYPE_0, validTime, PRDT_ADJUST_TASK_EXECUTE_STATUS_0, ApproveStateEnum.COMPLETE_APPROVE.getState());

        //循环处理调价单
        pricesList.forEach(x -> {
            //获取调价单详情
            List<PrdtAdjustPricesDtl> dtlList = prdtAdjustPricesDtlService.getPrdtAdjustPricesDtlList(x.getAdjustPricesId());

            // 调整 调价单明细状态为 生效中
            prdtAdjustPricesDtlService.updatePrdtAdjustPricesDtlValidState(dtlList, NumberPool.LONG_ONE);

            dtlList.forEach(dtl -> {
                // 发送MQ 进行商品价格调整 处理
                productMqProducer.sendAdjustPricesDtlApprove(dtl.getAdjustPricesDtlId());
            });
            //修改主表任务执行状态
            x.setTaskExecuteStatus(PRDT_ADJUST_TASK_EXECUTE_STATUS_1);
            prdtAdjustPricesMapper.updateById(x);

        });

    }

    /**
     * 校验商品调价单主是否已经存在
     * @param prdtAdjustPrices
     */
    private void validatePrdtAdjustPricesExists(PrdtAdjustPrices prdtAdjustPrices) {
        if (ToolUtil.isEmpty(prdtAdjustPrices)) {
            throw exception(PRDT_ADJUST_PRICES_NOT_EXISTS);
        }
    }

    /**
     * 审核信息校验
     * @param prdtAdjustPrices
     */
    private void validateApprove(PrdtAdjustPrices prdtAdjustPrices,List<PrdtAdjustPricesDtl> dtlList) {
        //校验主表是否存在
        validatePrdtAdjustPricesExists(prdtAdjustPrices);

        //校验详情列表是否存在
        if (ToolUtil.isEmpty(dtlList)) {
            throw exception(PRDT_ADJUST_PRICES_DTL_NOT_EXISTS);
        }

        //校验该调价商品的入驻商是否对接了第三方
        checkSupplierSync(prdtAdjustPrices.getSupplierId());

        //校验如果是平台商审核  只能审核该平台商管理的入驻商（没有主运营商的入驻商）生成的调价单
        //获取登录角色信息
        String funcScop = SecurityUtils.getLoginUser().getFuncScop();
        //如果是平台商角色 并且该调价单存在运营商ID 说明是有主运营商的入驻商调价单
        if(ToolUtil.isNotEmpty(funcScop)
                && FUNC_SCOPE_PARTNER.equals(funcScop)
                && ToolUtil.isNotEmpty(prdtAdjustPrices.getDcId())){
            throw exception(PRDT_ADJUST_PRICES_CHECK_PARTNER_ROLE);
        }

    }

    /**
     * 校验 商品调价单商品明细匹配入驻商与上传入驻商是否一致
     * @param createReqVO
     */
    private void validateAdjustPriceSupplierNotMacth(PrdtAdjustPricesSaveReqVO createReqVO) {
        createReqVO.getDtlList().forEach(dtl -> {
            SkuDTO skuDTO = productCacheService.getSkuDTO(dtl.getSkuId());
            SpuDTO spuDTO = productCacheService.getSpuDTO(skuDTO.getSpuId());
            if (ToolUtil.isNotEmpty(spuDTO) && !Objects.equals(spuDTO.getSupplierId(), createReqVO.getSupplierId())) {
                throw exception(PRDT_ADJUST_PRICES_SUPPLIER_NOT_MATCH);
            }

            //校验 调增的价格不能为0或空
            boolean checkPriceZero = false;
            if(ToolUtil.isNotEmpty(skuDTO.getMarkPrice()) && ToolUtil.isNotEmpty(dtl.getOldMinMarkPrice())
                    && (ToolUtil.isEmpty(dtl.getNewMinMarkPrice()) || dtl.getNewMinMarkPrice().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO )){
                checkPriceZero = true;
            }
            if(ToolUtil.isNotEmpty(skuDTO.getMidMarkPrice()) && ToolUtil.isNotEmpty(dtl.getOldMidMarkPrice())
                    && (ToolUtil.isEmpty(dtl.getNewMidMarkPrice()) || dtl.getNewMidMarkPrice().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO )){
                checkPriceZero = true;
            }
            if(ToolUtil.isNotEmpty(skuDTO.getLargeMarkPrice()) && ToolUtil.isNotEmpty(dtl.getOldLargeMarkPrice())
                    && (ToolUtil.isEmpty(dtl.getNewLargeMarkPrice()) || dtl.getNewLargeMarkPrice().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO )){
                checkPriceZero = true;
            }
            if(ToolUtil.isNotEmpty(skuDTO.getCostPrice()) && ToolUtil.isNotEmpty(dtl.getOldMinCostPrice())
                    && (ToolUtil.isEmpty(dtl.getNewMinCostPrice()) || dtl.getNewMinCostPrice().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO )){
                checkPriceZero = true;
            }
            if(ToolUtil.isNotEmpty(skuDTO.getMidCostPrice()) && ToolUtil.isNotEmpty(dtl.getOldMidCostPrice())
                    && (ToolUtil.isEmpty(dtl.getNewMidCostPrice()) || dtl.getNewMidCostPrice().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO )){
                checkPriceZero = true;
            }
            if(ToolUtil.isNotEmpty(skuDTO.getLargeCostPrice()) && ToolUtil.isNotEmpty(dtl.getOldLargeCostPrice())
                    && (ToolUtil.isEmpty(dtl.getNewLargeCostPrice()) || dtl.getNewLargeCostPrice().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO )){
                checkPriceZero = true;
            }
            if(checkPriceZero){
                throw exception(PRDT_ADJUST_PRICES_CHECK_PRICE_ZERO,spuDTO.getSpuNo());
            }
        });
    }

    /**
     * 校验该调价商品的入驻商是否对接了第三方
     * @param supplierId
     */
    private void checkSupplierSync(Long supplierId){
        //校验该调价商品的入驻商是否对接了第三方  如果对接了第三方不允许后台新增或修改调价单
        if(remoteSupplierApi.checkSyncConfig(supplierId).getCheckedData()){
            throw exception(PRDT_ADJUST_PRICES_CHECK_SUPPLIER_SYNC);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.product.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 商品调价单主 TODO 补充编号 ==========
    // ErrorCode PRDT_ADJUST_PRICES_NOT_EXISTS = new ErrorCode(TODO 补充编号, "商品调价单主不存在");


}
