package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 组合商品对象 prdt_spu_combine
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@TableName(value = "prdt_spu_combine")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSpuCombine extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 组合商品id */
    @TableId
    private Long spuCombineId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 商品管理分类ID */
    @Excel(name = "商品管理分类ID")
    private Long categoryId;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 组合商品编号 */
    @Excel(name = "组合商品编号")
    private String spuCombineNo;

    /** 组合商品名 */
    @Excel(name = "组合商品名")
    private String spuCombineName;

    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    private String thumb;

    /** 封面视频（url） */
    @Excel(name = "封面视频", readConverterExp = "u=rl")
    private String thumbVideo;

    /** 详情页轮播（json） */
    @Excel(name = "详情页轮播", readConverterExp = "j=son")
    private String images;

    /** 详情信息(富文本) */
    @Excel(name = "详情信息(富文本)")
    private String details;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    private Long isDelete;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    private Long status;

    /** 商品规格 */
    @Excel(name = "商品规格")
    private String specName;

    /** 总限量 */
    @Excel(name = "总限量")
    private Long totalLimit;

    /** 起订 */
    @Excel(name = "起订")
    private Long minOq;

    /** 订货组数 */
    @Excel(name = "订货组数")
    private Long jumpOq;

    /** 限购 */
    @Excel(name = "限购")
    private Long maxOq;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    private Long unit;

    /** 标准价 */
    @Excel(name = "标准价")
    private BigDecimal markPrice;

    /** 建议零售价 */
    @Excel(name = "建议零售价")
    private BigDecimal suggestPrice;

    /** 销售价1 */
    @Excel(name = "销售价1")
    private BigDecimal salePrice1;

    /** 销售价2 */
    @Excel(name = "销售价2")
    private BigDecimal salePrice2;

    /** 销售价3 */
    @Excel(name = "销售价3")
    private BigDecimal salePrice3;

    /** 销售价4 */
    @Excel(name = "销售价4")
    private BigDecimal salePrice4;

    /** 销售价5 */
    @Excel(name = "销售价5")
    private BigDecimal salePrice5;

    /** 销售价6 */
    @Excel(name = "销售价6")
    private BigDecimal salePrice6;

}
