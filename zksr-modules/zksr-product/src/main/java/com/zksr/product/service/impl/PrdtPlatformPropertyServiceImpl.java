package com.zksr.product.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtPlatformPropertyMapper;
import com.zksr.product.convert.platform.PrdtPlatformPropertyConvert;
import com.zksr.product.domain.PrdtPlatformProperty;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyPageReqVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertySaveReqVO;
import com.zksr.product.service.IPrdtPlatformPropertyService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 规格名称Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Service
public class PrdtPlatformPropertyServiceImpl implements IPrdtPlatformPropertyService {
    @Autowired
    private PrdtPlatformPropertyMapper prdtPlatformPropertyMapper;

    /**
     * 新增规格名称
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtPlatformProperty(PrdtPlatformPropertySaveReqVO createReqVO) {
        // 插入
        PrdtPlatformProperty prdtPlatformProperty = PrdtPlatformPropertyConvert.INSTANCE.convert(createReqVO);
        prdtPlatformPropertyMapper.insert(prdtPlatformProperty);
        // 返回
        return prdtPlatformProperty.getPlatformPropertyId();
    }

    /**
     * 修改规格名称
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtPlatformProperty(PrdtPlatformPropertySaveReqVO updateReqVO) {
        prdtPlatformPropertyMapper.updateById(PrdtPlatformPropertyConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除规格名称
     *
     * @param platformPropertyId 规格名称id
     */
    @Override
    public void deletePrdtPlatformProperty(Long platformPropertyId) {
        // 删除
        prdtPlatformPropertyMapper.deleteById(platformPropertyId);
    }

    /**
     * 批量删除规格名称
     *
     * @param platformPropertyIds 需要删除的规格名称主键
     * @return 结果
     */
    @Override
    public void deletePrdtPlatformPropertyByPlatformPropertyIds(Long[] platformPropertyIds) {
        for(Long platformPropertyId : platformPropertyIds){
            this.deletePrdtPlatformProperty(platformPropertyId);
        }
    }

    /**
     * 获得规格名称
     *
     * @param platformPropertyId 规格名称id
     * @return 规格名称
     */
    @Override
    public PrdtPlatformProperty getPrdtPlatformProperty(Long platformPropertyId) {
        return prdtPlatformPropertyMapper.selectById(platformPropertyId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtPlatformProperty> getPrdtPlatformPropertyPage(PrdtPlatformPropertyPageReqVO pageReqVO) {
        return prdtPlatformPropertyMapper.selectPage(pageReqVO);
    }

}
