package com.zksr.product.controller.catgoryRate.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 城市级管理分类扣点设置对象 prdt_catgory_rate
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Data
@ApiModel("城市级管理分类扣点设置 - prdt_catgory_rate分页 Request VO")
public class PrdtCatgoryRateSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 城市级管理分类扣点设置id */
    @ApiModelProperty(value = "城市级管理分类扣点设置id")
    private Long catgoryRateId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /** 平台商二级管理分类id */
    @Excel(name = "平台商二级管理分类id")
    @ApiModelProperty(value = "平台商二级管理分类id")
    private Long catgoryId;

    /** 运营商分润比例 */
    @Excel(name = "运营商分润比例")
    @ApiModelProperty(value = "运营商分润比例")
    private BigDecimal dcRate;

    /** 业务员负责人分润比例 */
    @Excel(name = "业务员负责人分润比例")
    @ApiModelProperty(value = "业务员负责人分润比例")
    private BigDecimal colonel1Rate;

    /** 业务员分润比例 */
    @Excel(name = "业务员分润比例")
    @ApiModelProperty(value = "业务员分润比例")
    private BigDecimal colonel2Rate;

    /** 父id */
    @Excel(name = "父id")
    @ApiModelProperty(value = "父id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pid;

    /** 分类名 */
    @Excel(name = "分类名")
    @ApiModelProperty(value = "分类名", example = "示例值")
    private String catgoryName;

    /** 状态 0-停用 1-启用 */
    @Excel(name = "状态 0-停用 1-启用")
    @ApiModelProperty(value = "状态 0-停用 1-启用", example = "示例值")
    private String status;

    /** 平台商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "平台商分润比例", readConverterExp = "只=一级分类设定")
    @ApiModelProperty(value = "平台商分润比例", example = "示例值")
    private BigDecimal partnerRate;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "示例值")
    private String memo;
}
