package com.zksr.product.controller.keywords.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 搜索关键词词库对象 prdt_keywords
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel("搜索关键词词库 - prdt_keywords Response VO")
public class PrdtKeywordsRespVO {
    private static final long serialVersionUID = 1L;

    /** 关键词id */
    @ApiModelProperty(value = "状态")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long keywordsId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 关键词 */
    @Excel(name = "关键词")
    @ApiModelProperty(value = "关键词")
    private String keyword;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Long status;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 关联商品数量 */
    @Excel(name = "关联商品数量")
    @ApiModelProperty(value = "关联商品数量")
    private Long spuCount;

}
