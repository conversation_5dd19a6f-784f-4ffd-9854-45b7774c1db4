package com.zksr.product.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.product.controller.areaItem.vo.ActivityItemPage;
import com.zksr.product.service.IPrdtActivityService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import com.zksr.promotion.api.activity.dto.PrmActivityDTO;
import com.zksr.promotion.api.activity.dto.SkRuleDTO;
import com.zksr.promotion.api.activity.dto.SpRuleDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品促销服务
 * @date 2025/1/22 14:59
 */
@Service
public class PrdtActivityServiceImpl implements IPrdtActivityService {

    @Autowired
    private IProductCacheService productCacheService;

    @Override
    public void loadActivityScopeByItemPage(ActivityItemPage pageReqVO) {
        if (ObjectUtil.isEmpty(pageReqVO.getActivityId())) {
            return;
        }
        PrmActivityDTO activityDTO = productCacheService.getActivityDTO(pageReqVO.getActivityId());
        if (Objects.nonNull(activityDTO)) {
            // 后台查询活动不需要判断活动是否有效
            // 限制入驻商
            pageReqVO.setSupplierId(activityDTO.getSupplierId());
            PrmNoEnum noEnum = PrmNoEnum.formValue(activityDTO.getPrmNo());
            // 查询
            List<ActivitySpuScopeDTO> spuScopeList = productCacheService.getActivitySpuScopeList(pageReqVO.getActivityId());
            if (ObjectUtil.isNotEmpty(spuScopeList)) {
                Map<Long, List<ActivitySpuScopeDTO>> typeMap = spuScopeList.stream().collect(Collectors.groupingBy(ActivitySpuScopeDTO::getApplyType));
                typeMap.forEach((applyType, applyTypeList) -> {
                    // 1-白名单 0-黑名单
                    Integer whiteOrBlack = applyTypeList.get(0).getWhiteOrBlack();
                    List<Long> applyIdList = applyTypeList.stream().map(ActivitySpuScopeDTO::getApplyId).collect(Collectors.toList());
                    if (whiteOrBlack == NumberPool.INT_ONE) {
                        // 适用类型;2-管理分类，3-品牌，4-商品
                        if (applyType == 2) {
                            pageReqVO.setCategoryIdList(applyIdList);
                        }
                        if (applyType == 3) {
                            pageReqVO.setBrandIdList(applyIdList);
                        }
                        if (applyType == 4) {
                            pageReqVO.setSkuIdList(applyIdList);
                        }
                    } else {
                        // 适用类型;2-管理分类，3-品牌，4-商品
                        if (applyType == 2) {
                            pageReqVO.setBlackCategoryIdList(applyIdList);
                        }
                        if (applyType == 3) {
                            pageReqVO.setBlackBrandIdList(applyIdList);
                        }
                        if (applyType == 4) {
                            pageReqVO.setBlackSkuIdList(applyIdList);
                        }
                    }
                });
            }
            // 秒杀或者特价
            if (noEnum == PrmNoEnum.SK) {
                List<SkRuleDTO> activitySkRuleList = productCacheService.getActivitySkRuleList(activityDTO.getActivityId());
                if (ObjectUtil.isNotEmpty(activitySkRuleList)) {
                    pageReqVO.setSkuIdList(activitySkRuleList.stream().map(SkRuleDTO::getSkuId).collect(Collectors.toList()));
                }
            }
            if (noEnum == PrmNoEnum.SP) {
                List<SpRuleDTO> activitySpRuleList = productCacheService.getActivitySpRuleList(activityDTO.getActivityId());
                if (ObjectUtil.isNotEmpty(activitySpRuleList)) {
                    pageReqVO.setSkuIdList(activitySpRuleList.stream().map(SpRuleDTO::getSkuId).collect(Collectors.toList()));
                }
            }
        }
    }
}
