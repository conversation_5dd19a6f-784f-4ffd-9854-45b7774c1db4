package com.zksr.product.convert.item;

import com.zksr.product.api.areaItem.vo.ApiAreaItemPageReqVO;
import com.zksr.product.api.supplierItem.vo.ApiSupplierItemPageReqVO;
import com.zksr.product.controller.areaItem.vo.PrdtAreaItemPageReqVO;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemPageReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/6/9
 * @desc
 */
@Mapper
public interface PrdtSupplierItemConvert {

    PrdtSupplierItemConvert INSTANCE = Mappers.getMapper(PrdtSupplierItemConvert.class);

    PrdtSupplierItemPageReqVO convertPageReq(ApiSupplierItemPageReqVO supplierItemPageReqVO);

}
