package com.zksr.product.convert.sku;

import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.sku.vo.PrdtSkuSpuGroupReqVO;
import com.zksr.product.api.spu.excel.ProductImportExcel;
import com.zksr.product.controller.sku.vo.PrdtSkuSaveReqVO;
import com.zksr.product.domain.PrdtSku;
import com.zksr.product.domain.excel.ProductBrandImportUpdateExcel;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: sku 转换器
 * @date 2024/4/25 16:50
 */
@Mapper
public interface SkuConvert {

    SkuConvert INSTANCE = Mappers.getMapper(SkuConvert.class);

    @Mappings({
            @Mapping(source = "itemData.barcode", target = "barcode"),
            @Mapping(source = "itemData.stock", target = "stock"),
            @Mapping(source = "itemData.maxOq", target = "maxOq"),
            @Mapping(source = "itemData.jumpOq", target = "jumpOq"),
            @Mapping(source = "itemData.minOq", target = "minOq"),
            @Mapping(source = "itemData.costPrice", target = "costPrice"),
            @Mapping(source = "itemData.markPrice", target = "markPrice"),
            @Mapping(source = "itemData.suggestPrice", target = "suggestPrice"),
//            @Mapping(source = "itemData.referencePrice", target = "referencePrice"),
//            @Mapping(source = "itemData.referenceSalePrice", target = "referenceSalePrice"),
            @Mapping(target = "unit", ignore = true),
    })
    @BeanMapping(ignoreByDefault = true)
    void convertImport(@MappingTarget PrdtSku prdtSku, ProductImportExcel itemData);

    void convertImport(@MappingTarget PrdtSku prdtSku, ProductBrandImportUpdateExcel itemData);

    List<PrdtSkuSpuGroupReqVO> convertSpuGroupReqVO(List<PrdtSku> skus);
    List<PrdtSkuSpuGroupReqVO> convertSkuDTOGroupReqVO(List<SkuDTO> skus);
    PrdtSkuSaveReqVO convertPrdtSkuSaveReqVO(PrdtSku sku);
}
