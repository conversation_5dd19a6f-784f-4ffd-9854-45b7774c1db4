package com.zksr.product.controller.supplierItem;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.api.supplierItem.vo.PrdtSupplierItemPageRespVO;
import com.zksr.product.controller.areaItem.excel.PrdtSupplierItemImportExcel;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemPageReqVO;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemRespVO;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemSaveReqVO;
import com.zksr.product.domain.PrdtSupplierItem;
import com.zksr.product.service.IPrdtSupplierItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 入驻商上架商品Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Api(tags = "管理后台 - 入驻商上架商品接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/supplierItem")
public class PrdtSupplierItemController {
    @Autowired
    private IPrdtSupplierItemService prdtSupplierItemService;

    /**
     * 新增入驻商上架商品
     */
    @ApiOperation(value = "新增入驻商上架商品", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "入驻商上架商品", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Boolean> add(@Valid @RequestBody List<PrdtSupplierItemSaveReqVO> createReqVO) {
        List<PrdtSupplierItem> supplierItems = prdtSupplierItemService.insertPrdtSupplierItem(createReqVO);
        // 集中处理后续缓存刷新
        prdtSupplierItemService.cacheEvent(supplierItems);
        return success(true);
    }

    /**
     * 修改入驻商上架商品
     */
    @ApiOperation(value = "修改入驻商上架商品", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "入驻商上架商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtSupplierItemSaveReqVO updateReqVO) {
        List<PrdtSupplierItem> eventSupplierItems = prdtSupplierItemService.updatePrdtSupplierItem(updateReqVO);
        // 集中处理后续缓存刷新
        prdtSupplierItemService.cacheEvent(ListUtil.toList(eventSupplierItems));
        return success(true);
    }

    /**
     * 批量上下架入驻商商品
     */
    @ApiOperation(value = "批量上下架入驻商商品", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "批量上下架入驻商商品", businessType = BusinessType.UPDATE)
    @PutMapping("/updateBatchItemShelfStatus")
    public CommonResult<Boolean> updateBatchItemShelfStatus(@RequestParam("supplierItemIds") Long[] supplierItemIds,
                                                            @RequestParam(name = "minShelfStatus", required = false,defaultValue = "-1") Integer minShelfStatus,
                                                            @RequestParam(name = "midShelfStatus", required = false,defaultValue = "-1") Integer midShelfStatus,
                                                            @RequestParam(name = "largeShelfStatus", required = false,defaultValue = "-1") Integer largeShelfStatus) {
        List<PrdtSupplierItem> supplierItems = prdtSupplierItemService.updatePrdtSupplierItemShelfStatus(supplierItemIds, minShelfStatus, midShelfStatus, largeShelfStatus);
        // 集中处理后续缓存刷新
        prdtSupplierItemService.cacheEvent(supplierItems);
        return success(true);
    }

    /**
     * 删除入驻商上架商品
     */
    @ApiOperation(value = "删除入驻商上架商品", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "入驻商上架商品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{supplierItemIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] supplierItemIds) {
        //prdtSupplierItemService.deletePrdtSupplierItemBySupplierItemIds(supplierItemIds);
        return success(true);
    }

    /**
     * 获取入驻商上架商品详细信息
     */
    @ApiOperation(value = "获得入驻商上架商品详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{supplierItemId}")
    public CommonResult<PrdtSupplierItemRespVO> getInfo(@PathVariable("supplierItemId") Long supplierItemId) {
        return success(prdtSupplierItemService.getPrdtSupplierItem(supplierItemId));
    }

    /**
     * 分页查询入驻商上架商品
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得入驻商上架商品分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtSupplierItemPageRespVO>> getPage(@Valid PrdtSupplierItemPageReqVO pageReqVO) {
        pageReqVO.setItemType(0);//只查询普通商品
        return success(prdtSupplierItemService.getPrdtSupplierItemPage(pageReqVO));
    }

    /**
     * 分页查询入驻商上架商品
     */
    @PostMapping("/lists")
    @ApiOperation(value = "获得多个入驻商上架商品分页列表", httpMethod = "PSOT", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtSupplierItemPageRespVO>> getPageLists(@Valid @RequestBody PrdtSupplierItemPageReqVO pageReqVO) {
        pageReqVO.setItemType(0);//只查询普通商品
        pageReqVO.setShelfStatus(NumberPool.INT_ONE);//查询上架商品
        return success(prdtSupplierItemService.getPrdtSupplierItemPage(pageReqVO));
    }

    @ApiOperation(value = "批量修改入驻商上架商品分类", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "批量修改入驻商上架商品分类", businessType = BusinessType.UPDATE)
    @PutMapping("/batchEditItemSaleClassId")
    public CommonResult<Boolean> batchEditItemSaleClassId(@Valid @RequestBody PrdtSupplierItemSaveReqVO updateReqVO) {
        List<PrdtSupplierItem> eventSupplierItems = prdtSupplierItemService.batchEditItemSaleClassId(updateReqVO);
        // 集中处理后续缓存刷新
        prdtSupplierItemService.cacheEvent(eventSupplierItems);
        return success(true);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "获取上架导入模版", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException {
            ExcelUtil<PrdtSupplierItemImportExcel> util = new ExcelUtil<>(PrdtSupplierItemImportExcel.class);
            String instructions = "填表说明：\n" +
                    "1、上架区域编号：必填项，运营商导入上架商品需要指定上架到哪个城市，城市需是当前运营商运营的区域，需与系统中区域的编号值保持一致，否则导入不成功；\n" +
                    "2、上架三级展示分类编号：必填项，需与A列的城市中的三级分类匹配，且值必须和系统中三级展示分类的编号保持一致，否在导入不成功；\n" +
                    "注意：运营商只能导自己运营区域的商品到自己运营的本地商品，且如果产品有多个单位，导入都会上架，如需下架需去后台手动操作；\n" +
                    "3、大中小单位上下架：必填项目，且只能填上架、下架";
            util.importTemplateExcel(response, "上架信息导入", StringUtils.EMPTY, instructions);
    }

    @ApiOperation(value = "平台商品上架导入", httpMethod = HttpMethod.POST)
    @Log(title = "平台商品上架导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public CommonResult<String> importData(MultipartFile file) throws Exception
    {
        ExcelUtil<PrdtSupplierItemImportExcel> util = new ExcelUtil<>(PrdtSupplierItemImportExcel.class);
        List<PrdtSupplierItemImportExcel> prdtSupplierList = util.importExcel(file.getInputStream(), 1);
        prdtSupplierList.forEach(item->{
            if (Objects.equals(item.getMinShelfStatusName(), "上架")){
                item.setMinShelfStatus(1);
            }else {
                item.setMinShelfStatus(0);
            }
            if (Objects.equals(item.getMidShelfStatusName(), "上架")){
                item.setMidShelfStatus(1);
            }else {
                item.setMidShelfStatus(0);
            }
            if (Objects.equals(item.getLargeShelfStatusName(), "上架")){
                item.setLargeShelfStatus(1);
            }else {
                item.setLargeShelfStatus(0);
            }
        });
        String message = prdtSupplierItemService.impordData(prdtSupplierList);
        return success(message);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:item:add";
        /** 编辑 */
        public static final String EDIT = "product:item:edit";
        /** 删除 */
        public static final String DELETE = "product:item:remove";
        /** 列表 */
        public static final String LIST = "product:item:list";
        /** 查询 */
        public static final String GET = "product:item:query";
        /** 停用 */
        public static final String DISABLE = "product:item:disable";
        /** 启用 */
        public static final String ENABLE = "product:item:enable";
    }
}
