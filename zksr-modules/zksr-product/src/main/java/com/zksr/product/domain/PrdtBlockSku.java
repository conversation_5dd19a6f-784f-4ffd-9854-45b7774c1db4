package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 经营屏蔽sku对象 prdt_block_sku
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@TableName(value = "prdt_block_sku")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class PrdtBlockSku extends BaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键 */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long blockSkuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 方案编码 */
    @Excel(name = "方案编码")
    private String schemeNo;

    /** 商品sku_id */
    @Excel(name = "商品sku_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

}
