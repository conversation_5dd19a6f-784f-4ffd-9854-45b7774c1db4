package com.zksr.product.service.impl;

import com.alicp.jetcache.Cache;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.material.vo.MaterialCacheVO;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.product.controller.areaItem.vo.PrdtAreaItemPageReqVO;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.controller.materialApply.vo.*;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemPageReqVO;
import com.zksr.product.api.supplierItem.vo.PrdtSupplierItemPageRespVO;
import com.zksr.product.convert.material.PrdtMaterialConvert;
import com.zksr.product.convert.materialApply.PrdtMaterialApplyConvert;
import com.zksr.product.domain.PrdtAreaItem;
import com.zksr.product.domain.PrdtSupplierItem;
import com.zksr.product.mapper.PrdtAreaItemMapper;
import com.zksr.product.mapper.PrdtMaterialMapper;
import com.zksr.product.mapper.PrdtSupplierItemMapper;
import com.zksr.product.service.IPrdtAreaItemService;
import com.zksr.product.service.IPrdtSupplierItemService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.promotion.api.activity.ActivityApi;
import com.zksr.promotion.api.activity.dto.ActivityDTO;
import com.zksr.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtMaterialApplyMapper;
import com.zksr.product.domain.PrdtMaterialApply;
import com.zksr.product.service.IPrdtMaterialApplyService;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.*;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 素材应用Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class PrdtMaterialApplyServiceImpl implements IPrdtMaterialApplyService {
    @Autowired
    private PrdtMaterialApplyMapper prdtMaterialApplyMapper;

    @Autowired
    private IProductCacheService productCacheService;

    @Autowired
    private ActivityApi activityApi;

    @Autowired
    private PrdtAreaItemMapper prdtAreaItemMapper;

    @Autowired
    private PrdtSupplierItemMapper prdtSupplierItemMapper;

    @Autowired
    private PrdtMaterialMapper prdtMaterialMapper;

    @Autowired
    private Cache<String, MaterialCacheVO> materialCache;


    /**
     * 新增素材应用
     *
     * @param createReqVO 创建信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertPrdtMaterialApply(List<PrdtMaterialApplySaveReqVO> createReqVO) {

        //校验
        checkMaterialApplyAdd(createReqVO);

        // 插入
        prdtMaterialApplyMapper.insertBatch(HutoolBeanUtils.toBean(createReqVO,PrdtMaterialApply.class));
    }

    /**
     * 修改素材应用
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrdtMaterialApply(@Valid List<PrdtMaterialApplySaveReqVO> updateReqVO) {
        //校验
        checkMaterialApplyAdd(updateReqVO);

        prdtMaterialApplyMapper.insertOrUpdateBatch(HutoolBeanUtils.toBean(updateReqVO,PrdtMaterialApply.class));
    }

    /**
     * 删除素材应用
     *
     * @param materialApplyId 素材应用id
     */
    @Override
    public void deletePrdtMaterialApply(Long materialApplyId) {
        // 删除
        prdtMaterialApplyMapper.deleteById(materialApplyId);
    }

    /**
     * 批量删除素材应用
     *
     * @param materialApplyIds 需要删除的素材应用主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrdtMaterialApplyByMaterialApplyIds(List<Long> materialApplyIds) {
        prdtMaterialApplyMapper.removeByMaterialApplyIds(materialApplyIds);
    }

    /**
     * 获得素材应用
     *
     * @param materialApplyId 素材应用id
     * @return 素材应用
     */
    @Override
    public PrdtMaterialApply getPrdtMaterialApply(Long materialApplyId) {
        return prdtMaterialApplyMapper.selectById(materialApplyId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrdtMaterialApplyRespVO> getPrdtMaterialApplyPage(PrdtMaterialApplyPageReqVO pageReqVO) {
        //获取当前登录用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        pageReqVO.setApplyUserId(ToolUtil.isNotEmpty(loginUser.getDcId()) ? loginUser.getDcId() : loginUser.getSysCode());

        Page<PrdtMaterialApplyPageReqVO> page = new Page<>(pageReqVO.getPageNo(),pageReqVO.getPageSize());

        //page 转换
        Page<PrdtMaterialApplyRespVO> applyRespPage = prdtMaterialApplyMapper.selectPageMaterialApply(pageReqVO, page);

        applyRespPage.getRecords().forEach(x ->{
            //设置素材应用信息的状态
            // 未生效：素材生效时间大于当前时间
            if(x.getStartTime().after(DateUtils.getNowDate())){
                x.setMaterialApplyStatus(PRDT_MATERIAL_APPLY_STATUS_0);
            }else if(x.getStartTime().before(DateUtils.getNowDate()) && x.getEndTime().after(DateUtils.getNowDate())){
                //生效中：素材生效时间小于或等于当前时间，素材失效时间大于当前时间
                x.setMaterialApplyStatus(PRDT_MATERIAL_APPLY_STATUS_1);

            }else if(x.getEndTime().before(DateUtils.getNowDate())){
                //已失效：素材失效时间小于或等于当前时间
                x.setMaterialApplyStatus(PRDT_MATERIAL_APPLY_STATUS_2);
            }

        });

        return new PageResult<>(applyRespPage.getRecords(),applyRespPage.getTotal());
    }

    @Override
    public PrdtMaterialApplyEchoRespVO getMaterialApplyList(PrdtMaterialApplyEchoReqVO reqVO) {
        PrdtMaterialApplyEchoRespVO respVO = new PrdtMaterialApplyEchoRespVO();

        //获取素材打标集合信息
        List<PrdtMaterialApply> materialApplyList = prdtMaterialApplyMapper.getMaterialApplyList(reqVO);

        if(!materialApplyList.isEmpty()){
            PrdtMaterialApply materialApply = materialApplyList.stream().findFirst().get();
            //设置回显信息
            respVO.setApplyType(materialApply.getApplyType());
            //设置生效时间类型 如果集合内所有的生效时间、结束时间都一致  则为按指定时间类型  不一致则为按促销类型
            boolean checkTimeType = materialApplyList.stream()
                    .collect(Collectors.groupingBy(
                            x -> Arrays.asList(x.getStartTime(), x.getEndTime()),
                            Collectors.counting()
                    ))
                    .values()
                    .stream()
                    .anyMatch(count -> count == 1);
            respVO.setTimeType(checkTimeType ? NumberPool.INT_TWO : NumberPool.INT_ONE);

            //如果是按指定时间  则设置生效时间
            if(checkTimeType){
                respVO.setStartTime(materialApply.getStartTime());
                respVO.setEndTime(materialApply.getEndTime());
            }

            //设置素材信息
            respVO.setMaterialRespVO(PrdtMaterialConvert.INSTANCE.convert(prdtMaterialMapper.selectById(materialApply.getMaterialId())));

            //ID集合
            List<Long> applyIdList = new ArrayList<>();

            //素材应用类型id与素材应用id Map集合
            Map<Long,Long> materialApplyIdMap = new HashMap<>();

            materialApplyList.forEach(x ->{
                applyIdList.add(x.getApplyId());
                materialApplyIdMap.put(x.getApplyId(),x.getMaterialApplyId());
            });

            // 根据 applyType 设置不同的列表
            setApplyTypeSpecificData(respVO, reqVO.getApplyType(), applyIdList,materialApplyIdMap);
        }

        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeMaterialApply(PrdtMaterialApplyEchoReqVO reqVO) {
        //规则：下架素材应用 实际为修改失效时间  将失效时间改为当前时间  如果生效时间大于当前时间 则将生效时间也改为当前时间
        prdtMaterialApplyMapper.removeMaterialApply(reqVO);
    }

    @Override
    public PrdtMaterialApply getMaterialApplyByEchoReq(PrdtMaterialApplyEchoReqVO reqVO) {
        return prdtMaterialApplyMapper.getMaterialApplyByEchoReq(reqVO);
    }

    @Override
    public List<PrdtMaterialApplyRespVO> getByMaterialApplyByApplyIds(List<Long> applyIds) {
        return prdtMaterialApplyMapper.getByMaterialApplyByApplyIds(applyIds);
    }

    @Override
    public MaterialApplyVO getByMaterialApplyByMaterial(MaterialApplyVO vo) {
        return prdtMaterialApplyMapper.getByMaterialApplyByMaterial(vo);
    }

    /**
     * 根据 applyType 设置不同的列表
     *
     * @param respVO
     * @param applyType
     * @param applyIdList
     * @param materialApplyIdMap
     */
    private void setApplyTypeSpecificData(PrdtMaterialApplyEchoRespVO respVO, Integer applyType, List<Long> applyIdList,Map<Long,Long> materialApplyIdMap) {
        switch (applyType) {
            case PRDT_MATERIAL_APPLY_TYPE_1:
                // 设置促销
                List<ActivityDTO> activityList = activityApi.getActivityList(new ActivityDTO(applyIdList)).getCheckedData();
                //设置该素材列表的素材应用信息
                activityList.forEach(x -> x.setMaterialApplyId(materialApplyIdMap.get(x.getActivityId())));
                respVO.setDetailList(new ArrayList<>(activityList));
                break;
            case PRDT_MATERIAL_APPLY_TYPE_2:
                // 设置全国商品
                List<PrdtSupplierItemPageRespVO> supplierItemList = getSupplierItemService().getPrdtSupplierItemPage(new PrdtSupplierItemPageReqVO(applyIdList)).getList();
                //设置该素材列表的素材应用信息
                supplierItemList.forEach(x -> x.setMaterialApplyId(materialApplyIdMap.get(x.getSupplierItemId())));
                respVO.setDetailList(new ArrayList<>(supplierItemList));

                break;
            case PRDT_MATERIAL_APPLY_TYPE_3:
                // 设置本地商品
                List<PrdtAreaItemPageRespVO> areaItemList = getAreaItemService().getPrdtAreaItemPage(new PrdtAreaItemPageReqVO(applyIdList)).getList();
                //设置该素材列表的素材应用信息
                areaItemList.forEach(x -> x.setMaterialApplyId(materialApplyIdMap.get(x.getAreaItemId())));
                respVO.setDetailList(new ArrayList<>(areaItemList));
                break;
            default:
                // 默认情况下不做任何处理
                break;
        }
    }

    private void validatePrdtMaterialApplyExists(Long materialApplyId) {
        if (prdtMaterialApplyMapper.selectById(materialApplyId) == null) {
            throw exception(PRDT_MATERIAL_APPLY_NOT_EXISTS);
        }
    }

    /**
     * 新增/修改 素材应用校验
     * @param applyList
     */
    private void checkMaterialApplyAdd(List<PrdtMaterialApplySaveReqVO> applyList){
        //设置操作人
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ToolUtil.isEmpty(loginUser.getDcId()) && ToolUtil.isEmpty(loginUser.getSysCode())){
            throw exception(PRDT_MATERIAL_APPLY_CHECK_USER);
        }
        //操作人ID
        Long applyUserId = ToolUtil.isNotEmpty(loginUser.getDcId()) ? loginUser.getDcId() : loginUser.getSysCode();

        //校验
        //1、普通商品的上架商品信息只能选择一个素材标签
        //2、同一个促销活动只能选择一个素材标签

        //校验前端类型传值是否异常
        List<Integer> typeList = ListUtil.toList(PRDT_MATERIAL_APPLY_TYPE_1, PRDT_MATERIAL_APPLY_TYPE_2, PRDT_MATERIAL_APPLY_TYPE_3);
        if(!applyList.stream().allMatch(x -> typeList.contains(x.getApplyType()))){
            throw exception(PRDT_MATERIAL_APPLY_CHECK_TYPE);
        }

        //素材应用类型
        Integer applyType = applyList.stream().findFirst().get().getApplyType();

        //筛选出需要新增的素材信息 进行校验
        for (PrdtMaterialApplySaveReqVO checkApply : applyList.stream().filter(x -> ToolUtil.isEmpty(x.getMaterialApplyId())).collect(Collectors.toList())) {
            //促销或者商品的唯一ID
            Long applyId = checkApply.getApplyId();

            //校验
            if(ToolUtil.isNotEmpty(prdtMaterialApplyMapper.selectApplyByCheck(applyId,applyType,checkApply.getStartTime(),checkApply.getEndTime()))){
                if(Objects.equals(applyType,PRDT_MATERIAL_APPLY_TYPE_1)){
                    //校验促销
                    ActivityDTO activityDTO = activityApi.getActivityById(applyId).getCheckedData();
                    throw exception(PRDT_MATERIAL_APPLY_CHECK_ACTIVITY_ONEC,activityDTO.getActivityName());
                }else if(Objects.equals(applyType,PRDT_MATERIAL_APPLY_TYPE_2)){
                    //校验商品
                    PrdtSupplierItem supplierItem = prdtSupplierItemMapper.selectById(applyId);
                    throw exception(PRDT_MATERIAL_APPLY_CHECK_SUPPLIER_ITEM_ONEC,productCacheService.getSpuDTO(supplierItem.getSpuId()).getSpuName());
                }else if(Objects.equals(applyType,PRDT_MATERIAL_APPLY_TYPE_3)){
                    //校验商品
                    PrdtAreaItem areaItem = prdtAreaItemMapper.selectById(applyId);
                    throw exception(PRDT_MATERIAL_APPLY_CHECK_AREA_ITEM_ONEC,productCacheService.getSpuDTO(areaItem.getSpuId()).getSpuName());
                }

            }

            //设置操作人
            checkApply.setApplyUserId(applyUserId);

        }
    }

    /**
     * 清除缓存信息
     * @param updateReqVO
     */
    @Override
    public void removeCache(List<PrdtMaterialApplySaveReqVO> updateReqVO){
        //组装缓存key
        Set<String> removeKeys = updateReqVO.stream().map(x -> MaterialCacheVO.getCacheKey(x.getApplyType(), x.getApplyId())).collect(Collectors.toSet());

        //清除缓存信息
        materialCache.removeAll(removeKeys);

    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.product.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 素材应用 TODO 补充编号 ==========
    // ErrorCode PRDT_MATERIAL_APPLY_NOT_EXISTS = new ErrorCode(TODO 补充编号, "素材应用不存在");

    /**
     * 防止依赖循环
     * @return
     */
    IPrdtSupplierItemService getSupplierItemService() {
        return SpringUtils.getBean(IPrdtSupplierItemService.class);
    }

    /**
     * 防止依赖循环
     * @return
     */
    IPrdtAreaItemService getAreaItemService() {
        return SpringUtils.getBean(IPrdtAreaItemService.class);
    }

}
