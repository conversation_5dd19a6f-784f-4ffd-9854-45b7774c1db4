package com.zksr.product.controller.keywords.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 搜索关键词词库对象 prdt_keywords
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@ApiModel("搜索关键词词库 - prdt_keywords分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtKeywordsPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 关键词id */
    @ApiModelProperty(value = "状态")
    private Long keywordsId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 关键词 */
    @Excel(name = "关键词")
    @ApiModelProperty(value = "关键词", required = true)
    private String keyword;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Long status;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createBy;



}
