package com.zksr.product.controller.blockScheme;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.product.controller.blockScheme.excel.BlockBranchExcelToListVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtBlockBranch;
import com.zksr.product.service.IPrdtBlockBranchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.blockScheme.vo.PrdtBlockBranchPageReqVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockBranchSaveReqVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockBranchRespVO;
import com.zksr.product.convert.blockScheme.PrdtBlockBranchConvert;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 经营屏蔽客户Controller
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Api(tags = "管理后台 - 经营屏蔽客户接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/blockBranch")
public class PrdtBlockBranchController {
    @Autowired
    private IPrdtBlockBranchService prdtBlockBranchService;

    /**
     * 新增经营屏蔽客户
     */
    @ApiOperation(value = "新增经营屏蔽客户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "经营屏蔽客户", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtBlockBranchSaveReqVO createReqVO) {
        return success(prdtBlockBranchService.insertPrdtBlockBranch(createReqVO));
    }

    /**
     * 修改经营屏蔽客户
     */
    @ApiOperation(value = "修改经营屏蔽客户", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "经营屏蔽客户", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtBlockBranchSaveReqVO updateReqVO) {
            prdtBlockBranchService.updatePrdtBlockBranch(updateReqVO);
        return success(true);
    }

    /**
     * 删除经营屏蔽客户
     */
    @ApiOperation(value = "删除经营屏蔽客户", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "经营屏蔽客户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{blockBranchIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] blockBranchIds) {
        prdtBlockBranchService.deletePrdtBlockBranchByBlockBranchIds(blockBranchIds);
        return success(true);
    }

    /**
     * 获取经营屏蔽客户详细信息
     */
    @ApiOperation(value = "获得经营屏蔽客户详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{blockBranchId}")
    public CommonResult<PrdtBlockBranchRespVO> getInfo(@PathVariable("blockBranchId") Long blockBranchId) {
        PrdtBlockBranch prdtBlockBranch = prdtBlockBranchService.getPrdtBlockBranch(blockBranchId);
        return success(PrdtBlockBranchConvert.INSTANCE.convert(prdtBlockBranch));
    }

    /**
     * 分页查询经营屏蔽客户
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得经营屏蔽客户分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtBlockBranchRespVO>> getPage(@Valid PrdtBlockBranchPageReqVO pageReqVO) {
        PageResult<PrdtBlockBranch> pageResult = prdtBlockBranchService.getPrdtBlockBranchPage(pageReqVO);
        return success(PrdtBlockBranchConvert.INSTANCE.convertPage(pageResult));
    }

    @ApiOperation(value = "根据Excel获取客户信息", httpMethod = HttpMethod.POST)
    @PostMapping("/excelToList")
    public CommonResult<List<PrdtBlockBranchRespVO>> excelToList(@RequestParam("file") MultipartFile file,
                                                                 @RequestParam(value = "schemeNo", required = false) String schemeNo,
                                                                 @RequestParam(value = "areaId", required = false) Long areaId) throws Exception {
        ExcelUtil<BlockBranchExcelToListVO> util = new ExcelUtil<>(BlockBranchExcelToListVO.class);
        List<BlockBranchExcelToListVO> branchList = util.importExcel(file.getInputStream());
        return CommonResult.success(prdtBlockBranchService.excelToList(branchList, schemeNo, areaId));
    }

    @PostMapping("/excelToListTemplate")
    @ApiOperation(value = "根据Excel获取客户信息导入模版", httpMethod = HttpMethod.POST)
    public void excelToListTemplate(HttpServletResponse response) {
        ExcelUtil<BlockBranchExcelToListVO> util = new ExcelUtil<>(BlockBranchExcelToListVO.class);
        util.importTemplateExcel(response, "根据Excel获取客户信息", StringUtils.EMPTY, null);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:blockBranch:add";
        /** 编辑 */
        public static final String EDIT = "product:blockBranch:edit";
        /** 删除 */
        public static final String DELETE = "product:blockBranch:remove";
        /** 列表 */
        public static final String LIST = "product:blockBranch:list";
        /** 查询 */
        public static final String GET = "product:blockBranch:query";
        /** 停用 */
        public static final String DISABLE = "product:blockBranch:disable";
        /** 启用 */
        public static final String ENABLE = "product:blockBranch:enable";
    }
}
