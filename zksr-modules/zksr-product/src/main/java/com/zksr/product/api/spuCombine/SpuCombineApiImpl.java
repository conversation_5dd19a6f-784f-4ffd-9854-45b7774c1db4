package com.zksr.product.api.spuCombine;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.mapper.PrdtSpuCombineMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/31 15:17
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
@InnerAuth
public class SpuCombineApiImpl implements SpuCombineApi{

    @Autowired
    private PrdtSpuCombineMapper prdtSpuCombineMapper;

    @Override
    public CommonResult<SpuCombineDTO> getSpuCombine(Long spuCombineId) {
        SpuCombineDTO spuCombine = prdtSpuCombineMapper.selectSpuCombineDTO(spuCombineId);
        if (Objects.nonNull(spuCombine)) {
            spuCombine.setCombineDtls(prdtSpuCombineMapper.selectSpuCombineDtlList(spuCombineId));
        }
        return success(spuCombine);
    }
}
