package com.zksr.product.controller.supplierGroupPrice.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 平台商城市分组价格对象 prdt_supplier_group_price
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Data
@ApiModel("平台商城市分组价格 - prdt_supplier_group_price分页 Request VO")
public class PrdtSupplierGroupPriceSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id", required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long groupId;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id", required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @Excel(name = "价格码-数据字典", readConverterExp = "1=，2，3，4，5，6")
    @ApiModelProperty(value = "价格码-数据字典", required = true)
    private Long salePriceCode;

}
