package com.zksr.product.controller.catgory.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 平台商管理分类对象 prdt_catgory
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Data
@ApiModel("平台商管理分类 - prdt_catgory分页 Request VO")
public class PrdtCatgorySaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 平台商管理分类id */
    @ApiModelProperty(value = "平台商管理分类id")
    private Long catgoryId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 父id */
    @Excel(name = "父id")
    @ApiModelProperty(value = "父id")
    private Long pid;

    /** 级别 */
    @Excel(name = "级别")
    @ApiModelProperty(value = "级别")
    private Integer level;

    /** 分类名 */
    @Excel(name = "分类名")
    @ApiModelProperty(value = "分类名")
    private String catgoryName;

    /** 分类图标 */
    @Excel(name = "分类图标")
    @ApiModelProperty(value = "分类图标")
    private String icon;

    /** 排序 */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Long sort;

    /** 状态 0-停用 1-启用 */
    @Excel(name = "状态 0-停用 1-启用")
    @ApiModelProperty(value = "状态 0-停用 1-启用")
    private String status;

    /** 平台商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "平台商分润比例", readConverterExp = "只=一级分类设定")
    @ApiModelProperty(value = "平台商分润比例")
    private BigDecimal partnerRate;

    /** 运营商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "运营商分润比例", readConverterExp = "只=一级分类设定")
    @ApiModelProperty(value = "运营商分润比例")
    private BigDecimal dcRate;

    /** 业务员负责人分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "业务员负责人分润比例", readConverterExp = "只=一级分类设定")
    @ApiModelProperty(value = "业务员负责人分润比例")
    private BigDecimal colonel1Rate;

    /** 业务员分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "业务员分润比例", readConverterExp = "只=一级分类设定")
    @ApiModelProperty(value = "业务员分润比例")
    private BigDecimal colonel2Rate;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /**
     * 销售占比利润, 百分比,最高29% = 0.29, 只有一级可以设置
     */
    @ApiModelProperty(value = "销售占比利润, 百分比,最高29% = 0.29, 只有一级可以设置")
    private BigDecimal saleTotalRate;

    /** 软件商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "软件商分润比例", readConverterExp = "只=一级分类设定")
    @ApiModelProperty(value = "软件商分润比例")
    private BigDecimal softwareRate;


}
