package com.zksr.product.controller.material;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtMaterial;
import com.zksr.product.service.IPrdtMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.material.vo.PrdtMaterialPageReqVO;
import com.zksr.product.controller.material.vo.PrdtMaterialSaveReqVO;
import com.zksr.product.controller.material.vo.PrdtMaterialRespVO;
import com.zksr.product.convert.material.PrdtMaterialConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.product.constant.ProductConstant.*;

/**
 * 素材Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Api(tags = "管理后台 - 素材接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/material")
public class PrdtMaterialController {
    @Autowired
    private IPrdtMaterialService prdtMaterialService;

    /**
     * 新增素材
     */
    @ApiOperation(value = "新增素材", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "素材", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtMaterialSaveReqVO createReqVO) {
        return success(prdtMaterialService.insertPrdtMaterial(createReqVO));
    }

    /**
     * 修改素材
     */
    @ApiOperation(value = "修改素材", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "素材", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtMaterialSaveReqVO updateReqVO) {
            prdtMaterialService.updatePrdtMaterial(updateReqVO);
        return success(true);
    }

    /**
     * 删除素材
     */
    @ApiOperation(value = "删除素材", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "素材", businessType = BusinessType.DELETE)
    @DeleteMapping("/{materialIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] materialIds) {
        prdtMaterialService.deletePrdtMaterialByMaterialIds(materialIds);
        return success(true);
    }

    /**
     * 获取素材详细信息
     */
    @ApiOperation(value = "获得素材详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{materialId}")
    public CommonResult<PrdtMaterialRespVO> getInfo(@PathVariable("materialId") Long materialId) {
        PrdtMaterial prdtMaterial = prdtMaterialService.getPrdtMaterial(materialId);
        return success(PrdtMaterialConvert.INSTANCE.convert(prdtMaterial));
    }

    /**
     * 分页查询素材
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得素材分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtMaterialRespVO>> getPage(@Valid PrdtMaterialPageReqVO pageReqVO) {
        PageResult<PrdtMaterial> pageResult = prdtMaterialService.getPrdtMaterialPage(pageReqVO);
        return success(PrdtMaterialConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 停用素材
     */
    @ApiOperation(value = "停用素材", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "素材", businessType = BusinessType.UPDATE)
    @PutMapping("/disable/{materialId}")
    public CommonResult<Boolean> disable(@PathVariable Long materialId) {
        prdtMaterialService.updateStatus(materialId,PRDT_STATUS_INT_0);
        return success(true);
    }

    /**
     * 启用素材
     */
    @ApiOperation(value = "启用素材", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "素材", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{materialId}")
    public CommonResult<Boolean> enable(@PathVariable Long materialId) {
        prdtMaterialService.updateStatus(materialId,PRDT_STATUS_INT_1);
        return success(true);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:material:add";
        /** 编辑 */
        public static final String EDIT = "product:material:edit";
        /** 删除 */
        public static final String DELETE = "product:material:remove";
        /** 列表 */
        public static final String LIST = "product:material:list";
        /** 查询 */
        public static final String GET = "product:material:query";
        /** 停用 */
        public static final String DISABLE = "product:material:disable";
        /** 启用 */
        public static final String ENABLE = "product:material:enable";
    }
}
