package com.zksr.product.controller.areaItem.excel;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(description = "用于运营商导入上架")
public class PrdtAreaItemImportExcel {

    @Excel(name = "上架区域编号")
    @ApiModelProperty(value = "上架区域编号")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    @Excel(name = "上架三级展示分类编号")
    @ApiModelProperty(value = "上架三级展示分类编号")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaClassId;

    /** 商品sku id */
    @Excel(name = "skuId")
    @ApiModelProperty(value = "skuId")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;



}