package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 入驻商-平台商管理分类 关联关系对象 prdt_supplier_class
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@TableName(value = "prdt_supplier_class")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSupplierClass {
    private static final long serialVersionUID = 1L;

    /**
     * 平台商管理分类id;平台商管理分类id
     */
    @Excel(name = "平台商管理分类id;平台商管理分类id")
    private Long catgoryId;

    /**
     * 入驻商id;入驻商id
     */
    @Excel(name = "入驻商id;入驻商id")
    private Long supplierId;

    /**
     * 平台商id;平台商id
     */
    @Excel(name = "平台商id;平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;


    /**
     * 是否可售后 1：是，0：否 数据字典 sys_status_type）
     */
    @Excel(name = "是否可售后")
    private Integer isAfterSales;

    /**
     * 售后时间类型- 分钟：1，小时：2，天：3（数据字典 sys_time_type）
     */
    @Excel(name = "售后时间类型")
    private Integer afterSalesTimeType;

    /**
     * 可售后时间
     */
    @Excel(name = "可售后时间")
    private Long afterSalesTime;

}
