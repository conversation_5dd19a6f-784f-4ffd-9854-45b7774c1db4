package com.zksr.product.api.combine;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.combine.dto.SpuCombineDTO;
import com.zksr.product.api.combine.dto.SpuCombineDtlRespDTO;
import com.zksr.product.api.combine.vo.SpuCombineSaveReqVO;
import com.zksr.product.api.content.ProductContentApi;
import com.zksr.product.api.model.event.EsProductEventBuild;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineRespVO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineSaveReqVO;
import com.zksr.product.mq.ProductEventMqChannel;
import com.zksr.product.service.IPrdtSpuCombineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2025/1/2 18:21
 * @注释
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
@InnerAuth
public class CombineApiImpl implements CombineApi {

    @Autowired
    private IPrdtSpuCombineService prdtSpuCombineService;

    @Autowired
    private ProductEventMqChannel productEventProducer;

    @Override
    public CommonResult<Long> addSpuCombine(SpuCombineSaveReqVO spuCombineSaveReqVO) {
        Long spuCombineId =prdtSpuCombineService.insertPrdtSpuCombine(HutoolBeanUtils.toBean(spuCombineSaveReqVO, PrdtSpuCombineSaveReqVO.class));
        if(spuCombineSaveReqVO.getSaveStatus() == 1){
            productEventProducer.sendEvent(EsProductEventBuild.spuCombineEvent(Arrays.asList(spuCombineId)));
        }
        return CommonResult.success(spuCombineId);
    }

    @Override
    public CommonResult<Long> editSpuCombine(SpuCombineSaveReqVO spuCombineSaveReqVO) {
        prdtSpuCombineService.updatePrdtSpuCombine(HutoolBeanUtils.toBean(spuCombineSaveReqVO, PrdtSpuCombineSaveReqVO.class));
        if(spuCombineSaveReqVO.getSaveStatus() == 1){
            productEventProducer.sendEvent(EsProductEventBuild.spuCombineEvent(Arrays.asList(spuCombineSaveReqVO.getSpuCombineId())));
        }
        return CommonResult.success(spuCombineSaveReqVO.getSpuCombineId());
    }

    @Override
    public CommonResult<SpuCombineDTO> getSpuCombine(Long spuCombineId) {
        PrdtSpuCombineRespVO prdtSpuCombineRespVO =prdtSpuCombineService.getPrdtSpuCombine(spuCombineId);
        SpuCombineDTO resp =HutoolBeanUtils.toBean(prdtSpuCombineRespVO, SpuCombineDTO.class);
        resp.setPrdtSpuCombineDtlList(HutoolBeanUtils.toBean(prdtSpuCombineRespVO.getPrdtSpuCombineDtlList(), SpuCombineDtlRespDTO.class));
        return CommonResult.success(resp);
    }

    @Override
    public CommonResult<Boolean> editSpuCombineStatus(Long spuCombineId, Integer status) {
        prdtSpuCombineService.editSpuCombineStatus(spuCombineId,status);
        return CommonResult.success(true);
    }
}
