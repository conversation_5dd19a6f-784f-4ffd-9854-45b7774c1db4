package com.zksr.product.controller.propertyVal;

import javax.validation.Valid;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtPropertyVal;
import com.zksr.product.service.IPrdtPropertyValService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.propertyVal.vo.PrdtPropertyValPageReqVO;
import com.zksr.product.controller.propertyVal.vo.PrdtPropertyValSaveReqVO;
import com.zksr.product.controller.propertyVal.vo.PrdtPropertyValRespVO;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 规格值Controller
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Api(tags = "管理后台 - 规格值接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/val")
public class PrdtPropertyValController {
    @Autowired
    private IPrdtPropertyValService prdtPropertyValService;

    /**
     * 新增规格值
     */
    @ApiOperation(value = "新增规格值", httpMethod = "POST")
    @RequiresPermissions("product:val:add")
    @Log(title = "规格值", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtPropertyValSaveReqVO createReqVO) {
        return success(prdtPropertyValService.insertPrdtPropertyVal(createReqVO));
    }

    /**
     * 修改规格值
     */
    @ApiOperation(value = "修改规格值", httpMethod = "PUT")
    @RequiresPermissions("product:val:edit")
    @Log(title = "规格值", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtPropertyValSaveReqVO updateReqVO) {
            prdtPropertyValService.updatePrdtPropertyVal(updateReqVO);
        return success(true);
    }

    /**
     * 删除规格值
     */
    @ApiOperation(value = "删除规格值", httpMethod = "GET")
    @RequiresPermissions("product:val:remove")
    @Log(title = "规格值", businessType = BusinessType.DELETE)
    @DeleteMapping("/{propertyValIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] propertyValIds) {
        prdtPropertyValService.deletePrdtPropertyValByPropertyValIds(propertyValIds);
        return success(true);
    }

    /**
     * 获取规格值详细信息
     */
    @ApiOperation(value = "获得规格值详情", httpMethod = "GET")
    @RequiresPermissions("product:val:query")
    @GetMapping(value = "/{propertyValId}")
    public CommonResult<PrdtPropertyValRespVO> getInfo(@PathVariable("propertyValId") Long propertyValId) {
        PrdtPropertyVal prdtPropertyVal = prdtPropertyValService.getPrdtPropertyVal(propertyValId);
        return success(HutoolBeanUtils.toBean(prdtPropertyVal, PrdtPropertyValRespVO.class));
    }

    /**
     * 分页查询规格值
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得规格值分页列表", httpMethod = "GET")
    @RequiresPermissions("product:val:list")
    public CommonResult<PageResult<PrdtPropertyValRespVO>> getPage(@Valid PrdtPropertyValPageReqVO pageReqVO) {
        PageResult<PrdtPropertyVal> pageResult = prdtPropertyValService.getPrdtPropertyValPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, PrdtPropertyValRespVO.class));
    }

}
