package com.zksr.product.api.sku;

import com.zksr.common.core.domain.vo.openapi.IncreaseUpdateStockDTO;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.sku.dto.SkuPricesRespDTO;
import com.zksr.common.core.domain.vo.openapi.receive.PrdInventoryVO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateReqVO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateVO;
import com.zksr.product.api.sku.vo.SkuPricesPageReqVO;
import com.zksr.product.controller.sku.vo.PrdtSkuSaveReqVO;
import com.zksr.product.domain.PrdtSku;
import com.zksr.product.service.IPrdtSkuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
@InnerAuth
public class SkuApiImpl implements SkuApi {

    @Autowired
    private IPrdtSkuService prdtSkuService;

    @Override
    public CommonResult<SkuDTO> getBySkuId(Long skuId) {
        PrdtSku sku = prdtSkuService.getBySkuId(skuId);
        return success(HutoolBeanUtils.toBean(sku, SkuDTO.class));
    }

    @Override
    public void updateBySku(SkuDTO skuDTO) {
        prdtSkuService.updatePrdtSku(HutoolBeanUtils.toBean(skuDTO, PrdtSkuSaveReqVO.class));
    }


    @Override
    public CommonResult<Boolean> editInventory(Long sysCode, Long opensourceId, PrdInventoryVO prdInventoryVO) {
        return success(prdtSkuService.editInventory(prdInventoryVO));
    }

    @Override
    public CommonResult<Boolean> updateSaleQtyEvent(List<Long> skuIds) {
        prdtSkuService.updateSaleQty(skuIds);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Date> getLastUpdateTime(String sourceNo, Long supplierId) {
        return success(prdtSkuService.getLastUpdateTime(sourceNo, supplierId));
    }

    @Override
    public CommonResult<PrdtSkuSaleTotalRateVO> getSkuSaleTotalRate(PrdtSkuSaleTotalRateReqVO reqVO) {
        return success(prdtSkuService.getSkuSaleTotalRate(reqVO));
    }

    @Override
    public CommonResult<List<SkuPricesRespDTO>> getSkuPricesList(SkuPricesPageReqVO reqVO) {
        return success(prdtSkuService.getSkuPricesList(reqVO));
    }

    @Override
    public CommonResult<Map<Long, SkuDTO>> listBySkuIds(List<Long> skuIds) {
        return success(prdtSkuService.listBySkuIds(skuIds));
    }

    @Override
    public CommonResult<List<Long>> getRelationSpuCombineList(List<Long> skuIds) {
        return success(prdtSkuService.getRelationSpuCombineList(skuIds));
    }

    @Override
    public CommonResult<Boolean> getAreaIdExistSku(Long areaId, Long sysCode) {
        return success(prdtSkuService.getAreaIdExistSpu(areaId, sysCode));
    }

    @Override
    public CommonResult<Boolean> getExistShelfSku(Long spuId) {
        return success(prdtSkuService.getExistShelfSku(spuId));
    }

    @Override
    public CommonResult<Boolean> increaseUpdateStock(IncreaseUpdateStockDTO dto) {
        return success(prdtSkuService.increaseUpdateStock(dto));
    }
}
