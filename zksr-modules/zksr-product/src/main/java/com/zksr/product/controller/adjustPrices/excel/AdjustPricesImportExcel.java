package com.zksr.product.controller.adjustPrices.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用于商品基本价格调价单导入
 */
@Data
@ApiModel(description = "用于商品基本价格调价单导入")
public class AdjustPricesImportExcel {

    @Excel(name = "SKUID", cellType = Excel.ColumnType.STRING)
    private String skuId;


    @Excel(name = "小单位原标准价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal oldMinMarkPrice;

    @Excel(name = "小单位新标准价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal newMinMarkPrice;

    @Excel(name = "小单位原供货价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal oldMinCostPrice;

    @Excel(name = "小单位新供货价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal newMinCostPrice;


    @Excel(name = "中单位原标准价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal oldMidMarkPrice;

    @Excel(name = "中单位新标准价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal newMidMarkPrice;

    @Excel(name = "中单位原供货价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal oldMidCostPrice;

    @Excel(name = "中单位新供货价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal newMidCostPrice;


    @Excel(name = "大单位原标准价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal oldLargeMarkPrice;

    @Excel(name = "大单位新标准价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal newLargeMarkPrice;

    @Excel(name = "大单位原供货价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal oldLargeCostPrice;

    @Excel(name = "大单位新供货价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal newLargeCostPrice;
}
