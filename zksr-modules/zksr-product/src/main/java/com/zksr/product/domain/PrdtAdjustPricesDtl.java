package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 商品调价单明细对象 prdt_adjust_prices_dtl
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@TableName(value = "prdt_adjust_prices_dtl")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtAdjustPricesDtl extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 单据明细ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long adjustPricesDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 主单据ID */
    @Excel(name = "主单据ID")
    private Long adjustPricesId;

    /** spuId */
    @Excel(name = "spuId")
    private Long spuId;

    /** skuId */
    @Excel(name = "skuId")
    private Long skuId;

    /** 大单位-原标准价 */
    @Excel(name = "大单位-原标准价")
    private BigDecimal oldLargeMarkPrice;

    /** 大单位-原成本价(供货价) */
    @Excel(name = "大单位-原成本价(供货价)")
    private BigDecimal oldLargeCostPrice;

    /** 中单位-原标准价 */
    @Excel(name = "中单位-原标准价")
    private BigDecimal oldMidMarkPrice;

    /** 中单位-原成本价(供货价) */
    @Excel(name = "中单位-原成本价(供货价)")
    private BigDecimal oldMidCostPrice;

    /** 小单位-原标准价 */
    @Excel(name = "小单位-原标准价")
    private BigDecimal oldMinMarkPrice;

    /** 小单位-原成本价(供货价) */
    @Excel(name = "小单位-原成本价(供货价)")
    private BigDecimal oldMinCostPrice;

    /** 大单位-新标准价 */
    @Excel(name = "大单位-新标准价")
    private BigDecimal newLargeMarkPrice;

    /** 大单位-新新成本价(供货价) */
    @Excel(name = "大单位-新新成本价(供货价)")
    private BigDecimal newLargeCostPrice;

    /** 中单位-标准价 */
    @Excel(name = "中单位-标准价")
    private BigDecimal newMidMarkPrice;

    /** 中单位-新成本价(供货价) */
    @Excel(name = "中单位-新成本价(供货价)")
    private BigDecimal newMidCostPrice;

    /** 小单位-新标准价 */
    @Excel(name = "小单位-新标准价")
    private BigDecimal newMinMarkPrice;

    /** 小单位-新成本价(供货价) */
    @Excel(name = "小单位-新成本价(供货价)")
    private BigDecimal newMinCostPrice;

    /** 生效状态：0-未生效，1-生效中，2-已生效 */
    @Excel(name = "生效状态：0-未生效，1-生效中，2-已生效")
    private Long validState;

    /** 生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生效时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validTime;

}
