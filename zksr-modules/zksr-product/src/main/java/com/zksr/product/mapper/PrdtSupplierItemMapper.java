package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.product.controller.spu.vo.SpuSkuReleaseListVO;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemPageReqVO;
import com.zksr.product.api.supplierItem.vo.PrdtSupplierItemPageRespVO;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemRespVO;
import com.zksr.product.domain.PrdtAreaItem;
import com.zksr.product.domain.PrdtSupplierItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import static com.zksr.product.constant.ProductConstant.PRDT_SHELF_STATUS_1;


/**
 * 入驻商上架商品Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Mapper
@SuppressWarnings("all")
public interface PrdtSupplierItemMapper extends BaseMapperX<PrdtSupplierItem> {

    default PageResult<PrdtSupplierItem> selectPage(PrdtSupplierItemPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtSupplierItem>()
                .eqIfPresent(PrdtSupplierItem::getSupplierItemId, reqVO.getSupplierItemId())
                .eqIfPresent(PrdtSupplierItem::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrdtSupplierItem::getSaleClassId, reqVO.getSaleClassId())
                .eqIfPresent(PrdtSupplierItem::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(PrdtSupplierItem::getSpuId, reqVO.getSpuId())
                .eqIfPresent(PrdtSupplierItem::getSkuId, reqVO.getSkuId())
                .eqIfPresent(PrdtSupplierItem::getShelfStatus, reqVO.getShelfStatus())
                .orderByDesc(PrdtSupplierItem::getSupplierItemId));
    }

    //入驻商上架商品分页查询
    public Page<PrdtSupplierItemPageRespVO> selectPageSupplierItem(@Param("reqVO") PrdtSupplierItemPageReqVO reqVO, @Param("page") Page<PrdtSupplierItemPageReqVO> page);

    //根据查询条件查询入驻商上架商品
    default PrdtSupplierItem selectSupplierItemByItem(PrdtSupplierItemRespVO vo) {
        LambdaQueryWrapperX<PrdtSupplierItem> wrapper = new LambdaQueryWrapperX<PrdtSupplierItem>()
                .eqIfPresent(PrdtSupplierItem::getSupplierItemId, vo.getSupplierItemId())
                .eqIfPresent(PrdtSupplierItem::getSkuId, vo.getSkuId())
                .eqIfPresent(PrdtSupplierItem::getSpuId, vo.getSpuId())
                .eqIfPresent(PrdtSupplierItem::getSortNum, vo.getSortNum())
                .eqIfPresent(PrdtSupplierItem::getSupplierId, vo.getSupplierId())
                .eqIfPresent(PrdtSupplierItem::getSaleClassId, vo.getSaleClassId());
        return selectOne(wrapper);
    }

    //校验入驻商上架商品是否存在
    default PrdtSupplierItem selectSupplierItemByCheck(PrdtSupplierItem item) {
        LambdaQueryWrapperX<PrdtSupplierItem> wrapper = new LambdaQueryWrapperX<PrdtSupplierItem>()
                .eqIfPresent(PrdtSupplierItem::getSkuId, item.getSkuId())
                .eqIfPresent(PrdtSupplierItem::getSpuId, item.getSpuId())
                .eqIfPresent(PrdtSupplierItem::getSupplierId, item.getSupplierId())
                .eqIfPresent(PrdtSupplierItem::getSaleClassId, item.getSaleClassId());
        return selectOne(wrapper);
    }

    //新增上架商品
    default Long insertPrdtSupplierItem(PrdtSupplierItem item) {
        //设置默认值
        item.setShelfStatus(PRDT_SHELF_STATUS_1);
        item.setMinShelfStatus(PRDT_SHELF_STATUS_1);
        item.setMidShelfStatus(PRDT_SHELF_STATUS_1);
        item.setLargeShelfStatus(PRDT_SHELF_STATUS_1);
        //获取当前入驻商的最大排序号
        Integer sortNum = selectMaxSort(item);
        item.setSortNum(sortNum + 1);
        // 插入
        insert(item);
        // 返回
        return item.getSupplierItemId();
    }

    //获取该入驻商最大的商品排序序号
    public Integer selectMaxSort(@Param("item") PrdtSupplierItem item);

    //批量上下架入驻商商品
    public void updatePrdtSupplierItemShelfStatus(@Param("supplierItemIds") Long[] supplierItemIds,
                                                  @Param("minShelfStatus") Integer minShelfStatus,
                                                  @Param("midShelfStatus") Integer midShelfStatus,
                                                  @Param("largeShelfStatus") Integer largeShelfStatus,
                                                  @Param("shelfStatus") Integer shelfStatus);

    //根据ID查询城市上架商品列表
    default List<PrdtSupplierItem> selectSupplierItemListById(Long[] supplierItemIds) {
        LambdaQueryWrapper<PrdtSupplierItem> wrapper = new LambdaQueryWrapperX<PrdtSupplierItem>()
                .in(PrdtSupplierItem::getSupplierItemId, supplierItemIds);
        return selectList(wrapper);
    }


    //查询上架商品列表
    public List<PrdtSupplierItemPageReqVO> selectSupplierItemList(@Param("reqVO") PrdtSupplierItemPageReqVO reqVO);

    public void updateSupplierItemBatchShelfStatus(@Param("spuId") Long spuId, @Param("skuId") Long skuId, @Param("supplierItemIds") List<Long> supplierItemIds);
    //停用商品全国中单位下架
    public void updateSupplierItemBatchMidShelfStatus(@Param("spuId") Long spuId, @Param("skuId") Long skuId, @Param("supplierItemIds") List<Long> supplierItemIds);
    //停用商品全国大单位下架
    public void updateSupplierItemBatchLargeShelfStatus(@Param("spuId") Long spuId, @Param("skuId") Long skuId, @Param("supplierItemIds") List<Long> supplierItemIds);

    //查询全国已上架商品列表
    default List<PrdtSupplierItem> selectSupplierItemByList(PrdtSupplierItemRespVO vo) {
        LambdaQueryWrapper<PrdtSupplierItem> wrapper = new LambdaQueryWrapperX<PrdtSupplierItem>()
                .eqIfPresent(PrdtSupplierItem::getSkuId, vo.getSkuId())
                .eqIfPresent(PrdtSupplierItem::getSpuId, vo.getSpuId())
                .eqIfPresent(PrdtSupplierItem::getSortNum, vo.getSortNum())
                .eqIfPresent(PrdtSupplierItem::getSupplierId, vo.getSupplierId())
                .eqIfPresent(PrdtSupplierItem::getSaleClassId, vo.getSaleClassId())
                .eqIfPresent(PrdtSupplierItem::getShelfStatus, vo.getShelfStatus());
        return selectList(wrapper);
    }

    //查询小程序商品上架商品列表
    public List<SupplierItemDTO> selectSupplierItemListByApi(@Param("reqVO") SupplierItemDTO reqVO);

    List<SpuSkuReleaseListVO> selectByReleaseSpuId(@Param("spuId") Long spuId, @Param("classId") Long classId);

    /**
     * @Description: 根据平台展示分类获取入驻商上级商品集合
     * @Author: liuxingyu
     * @Date: 2024/6/7 9:08
     */
    default List<PrdtSupplierItem> selectBySaleClassId(Long saleClassId) {
        return selectList(new LambdaQueryWrapper<PrdtSupplierItem>().eq(PrdtSupplierItem::getSaleClassId, saleClassId));
    }

    default List<PrdtSupplierItem> selectSaleQtyTotalList(Long minSupplierItemId) {
        return selectList(
                new LambdaQueryWrapper<PrdtSupplierItem>()
                        .gt(PrdtSupplierItem::getSupplierItemId, minSupplierItemId)
                        .eq(PrdtSupplierItem::getShelfStatus, NumberPool.INT_ONE)
                        .select(PrdtSupplierItem::getSkuId, PrdtSupplierItem::getSupplierItemId)
                        .orderByAsc(PrdtSupplierItem::getSupplierItemId)
                        .last("LIMIT 1000")
        );
    }

    default Long selectBySkuIdReleaseCount(Long skuId) {
        return selectCount(
                new LambdaQueryWrapper<PrdtSupplierItem>()
                        .eq(PrdtSupplierItem::getSkuId, skuId)
                        .eq(PrdtSupplierItem::getShelfStatus, NumberPool.INT_ONE)
        );
    }

    default Long selectCountReleaseBySaleClassId(Long saleClassId) {
        return selectCount(
                new LambdaQueryWrapper<PrdtSupplierItem>()
                        .eq(PrdtSupplierItem::getSaleClassId, saleClassId)
                        .eq(PrdtSupplierItem::getShelfStatus, NumberPool.INT_ONE)
        );
    }

    default PrdtSupplierItem selectBySkuAndSaleId(Long skuId, Long saleClassId) {
        return selectOne(
                new LambdaQueryWrapper<PrdtSupplierItem>()
                        .eq(PrdtSupplierItem::getSkuId, skuId)
                        .eq(PrdtSupplierItem::getSaleClassId, saleClassId)
                        .last(StringPool.LIMIT_ONE)
        );
    }

    default List<PrdtSupplierItem> selectByReleaseRefreshSpuId(Long spuId) {
        return selectList(
                new LambdaQueryWrapper<PrdtSupplierItem>()
                        .select(
                                PrdtSupplierItem::getSupplierItemId,
                                PrdtSupplierItem::getSkuId,
                                PrdtSupplierItem::getSaleClassId
                        )
                        .eq(PrdtSupplierItem::getSpuId, spuId)
                        .eq(PrdtSupplierItem::getShelfStatus, PRDT_SHELF_STATUS_1)
        );
    }

    /**
     * 根据全国展示分类ID集合 获取全国上架商品集合
     * @param saleClassIds
     * @return
     */
    default List<PrdtSupplierItem> selectBySaleClassIdList(List<Long> saleClassIds){
        return selectList(new LambdaQueryWrapper<PrdtSupplierItem>().in(PrdtSupplierItem::getSaleClassId,saleClassIds));
    }

    default Long selectSpuSkuIsOnShelf(PrdtSupplierItem prdtSupplierItem){
        return selectCount(
                new LambdaQueryWrapper<PrdtSupplierItem>()
                        .eq(PrdtSupplierItem::getSupplierId, prdtSupplierItem.getSupplierId())
                        .eq(PrdtSupplierItem::getSpuId, prdtSupplierItem.getSpuId())
                        .eq(PrdtSupplierItem::getSkuId, prdtSupplierItem.getSkuId())
                        .eq(PrdtSupplierItem::getShelfStatus, PRDT_SHELF_STATUS_1)
        );
    }

    default PrdtSupplierItem selectBySpuCombineId(Long spuCombineId){
        return selectOne(
                new LambdaQueryWrapper<PrdtSupplierItem>()
                        .eq(PrdtSupplierItem::getSpuCombineId, spuCombineId)
                        .last(StringPool.LIMIT_ONE)
        );
    }

    default PrdtSupplierItem getSupplierItemByActivityId (Long activityId){
        return selectOne(
                new LambdaQueryWrapper<PrdtSupplierItem>()
                        .eq(PrdtSupplierItem::getActivityId, activityId)
                        .last(StringPool.LIMIT_ONE)
        );
    }

    default void deleteBySpuCombineId(Long spuCombineId){
        delete(new LambdaQueryWrapper<PrdtSupplierItem>().eq(PrdtSupplierItem::getSpuCombineId, spuCombineId));
    }

    default List<PrdtSupplierItem> getSupplierItemShelfBySkuId(Long skuId, Integer shelfStatus){
        return selectList(
                new LambdaQueryWrapper<PrdtSupplierItem>()
                        .eq(PrdtSupplierItem::getSkuId, skuId)
                        .eq(shelfStatus != null, PrdtSupplierItem::getShelfStatus, shelfStatus)
        );
    }

    default List<PrdtSupplierItem> getSupplierItemBySpuList(List<Long> shelfSpuList){
        return selectList(
                new LambdaQueryWrapper<PrdtSupplierItem>()
                        .in(PrdtSupplierItem::getSpuId, shelfSpuList)
        );
    }

    default List<PrdtSupplierItem> getBySkuId(Long skuId){
        return selectList(
                new LambdaQueryWrapper<PrdtSupplierItem>()
                        .eq(PrdtSupplierItem::getSkuId, skuId)
        );
    }
}
