package com.zksr.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaClass.excel.ProductAreaClassImportExcel;
import com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRespDTO;
import com.zksr.product.controller.areaClass.vo.*;
import com.zksr.product.domain.PrdtAreaClass;
import com.zksr.product.domain.PrdtAreaItem;
import com.zksr.product.domain.PrdtChannelAreaClass;
import com.zksr.product.mapper.PrdtAreaClassMapper;
import com.zksr.product.mapper.PrdtAreaItemMapper;
import com.zksr.product.mapper.PrdtChannelAreaClassMapper;
import com.zksr.product.service.IPrdtAreaClassService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.ChannelApi;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.FileImportApi;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.STATUS_FAIL;
import static com.zksr.common.core.constant.StatusConstants.STATUS_SUCCESS;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.PRDT_CLASS_COPY_TYPE_0;
import static com.zksr.product.constant.ProductConstant.PRDT_CLASS_COPY_TYPE_1;
import static com.zksr.product.enums.ErrorCodeConstants.*;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_AREA_LEVEL_NOT_TWO;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_PARTNER_CONFIG_LOCAL_DISABLE;
import static org.apache.commons.lang3.StringUtils.isNumeric;

/**
 * 城市展示分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Service
public class PrdtAreaClassServiceImpl implements IPrdtAreaClassService {
    @Autowired
    private PrdtAreaClassMapper prdtAreaClassMapper;

    @Autowired
    private Cache<Long, AreaClassDTO> areaClassDtoCache;

    @Autowired
    @Qualifier("areaClassBranchListDtoCache")
    private Cache<Long, List<AreaClassDTO>> areaClassBranchListDtoCache;

    @Autowired
    @Qualifier("areaClassAreaChannelListDtoCache")
    private Cache<String, List<AreaClassDTO>> areaClassAreaChannelListDtoCache;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IProductCacheService productCacheService;

    @Autowired
    private PrdtChannelAreaClassMapper channelAreaClassMapper;

    @Resource
    private BranchApi branchApi;

    @Resource
    private ChannelApi channelApi;

    @Autowired
    private PrdtAreaItemMapper areaItemMapper;

    @Resource
    private AreaApi areaApi;
    @Resource
    private FileImportApi fileImportApi;

    /**
     * 新增城市展示分类
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertPrdtAreaClass(PrdtAreaClassSaveReqVO createReqVO,Long sysCode) {
        validate(createReqVO,sysCode);
        //校验级别
        checkAreaClassLevel(createReqVO);
        // 插入
        PrdtAreaClass prdtAreaClass = HutoolBeanUtils.toBean(createReqVO, PrdtAreaClass.class);
        prdtAreaClass.setSysCode(sysCode);
        prdtAreaClassMapper.insert(prdtAreaClass);
        List<Long> channelDTOList = createReqVO.getChannelIds();
        if (ObjectUtil.isNotEmpty(channelDTOList)) {
            //插入渠道关联表
            List<PrdtChannelAreaClass> channelAreaClassList = channelDTOList
                    .stream()
                    .map(x -> new PrdtChannelAreaClass(SecurityUtils.getLoginUser().getSysCode(), prdtAreaClass.getAreaClassId(), x))
                    .collect(Collectors.toList());
            channelAreaClassMapper.insertBatch(channelAreaClassList);
        }

        //---------------------------删除缓存---------------------------
        //删除区域城市或渠道绑定的城市展示分类
        areaClassAreaChannelListDtoCache.remove(createReqVO.getAreaId() + "-0");
        if (ObjectUtil.isNotEmpty(createReqVO.getChannelIds())) {
            createReqVO.getChannelIds().forEach(x -> {
                areaClassAreaChannelListDtoCache.remove(createReqVO.getAreaId() + "-" + x);
            });
        }
        if (ObjectUtil.isNotNull(prdtAreaClass.getSupplierId())) {
            //删除门店绑定的城市分类
            List<Long> branchId = branchApi.getBySupplier(prdtAreaClass.getSupplierId()).getCheckedData();
            branchId.forEach(x -> areaClassBranchListDtoCache.remove(x));
        }

        // 返回
        return prdtAreaClass.getAreaClassId();
    }

    /**
     * 修改城市展示分类
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UpdateAreaClassRespEvent updatePrdtAreaClass(PrdtAreaClassSaveReqVO updateReqVO) {
        //获取原数据
        PrdtAreaClass prdtAreaClass = prdtAreaClassMapper.selectById(updateReqVO.getAreaClassId());
        if (ObjectUtil.isNull(prdtAreaClass)) {
            throw exception(PRDT_AREA_CLASS_NOT_EXISTS);
        }

        // 验证名称唯一性
        validate(updateReqVO);

        // 校验级别
        checkAreaClassLevel(updateReqVO);

        // 同步子级
        List<PrdtAreaClass> family = synSublevel(updateReqVO);
        prdtAreaClassMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrdtAreaClass.class));

        // 获取原渠道绑定分类信息
        List<PrdtChannelAreaClass> prdtChannelAreaClassList = channelAreaClassMapper.getListByAreaClassId(updateReqVO.getAreaClassId());

        // 重新绑定渠道关系
        channelAreaClassMapper.bindChannel(prdtAreaClass.getAreaClassId(), updateReqVO.getChannelIds());

        // 返回事件, 用于处理缓存刷新
        return UpdateAreaClassRespEvent.builder()
                .family(family)
                .saveReqVO(updateReqVO)
                .source(prdtAreaClass)
                .sourceChannelList(prdtChannelAreaClassList)
                .build();
    }

    @Override
    public void reloadCache(UpdateAreaClassRespEvent event) {
        PrdtAreaClassSaveReqVO updateReqVO = event.getSaveReqVO();
        PrdtAreaClass prdtAreaClass = event.getSource();
        List<PrdtChannelAreaClass> prdtChannelAreaClassList = event.getSourceChannelList();
        List<PrdtAreaClass> family = event.getFamily();

        //============================删除缓存====================
        // 删除城市展示分类基础缓存
        areaClassDtoCache.remove(updateReqVO.getAreaClassId());

        // 删除区域城市或渠道绑定的城市展示分类(老数据)
        areaClassAreaChannelListDtoCache.remove(prdtAreaClass.getAreaId() + "-0");

        // 删除原来绑定的渠道数据(老数据)
        prdtChannelAreaClassList.forEach(item -> areaClassAreaChannelListDtoCache.remove(prdtAreaClass.getAreaId() + "-" + item.getChannelId()));

        // 因为一级分类无渠道信息, 所以这里更新所有的区域
        if (updateReqVO.getLevel() == NumberPool.INT_ONE) {
            List<ChannelDTO> channelDTOList = channelApi.getChannelList().getCheckedData();
            if (!channelDTOList.isEmpty()) {
                channelDTOList.forEach(channelDTO -> areaClassAreaChannelListDtoCache.remove(prdtAreaClass.getAreaId() + "-" + channelDTO.getChannelId()));
            }
        }

        // 删除区域城市或渠道绑定的城市展示分类(新数据)
        areaClassAreaChannelListDtoCache.remove(updateReqVO.getAreaId() + "-0");

        // 新区域渠道数据
        updateReqVO.getChannelIds().forEach(x -> areaClassAreaChannelListDtoCache.remove(updateReqVO.getAreaId() + "-" + x));

        //删除门店新绑定的城市分类
        if (ObjectUtil.isNotNull(prdtAreaClass.getSupplierId())) {
            List<Long> branchId = branchApi.getBySupplier(prdtAreaClass.getSupplierId()).getCheckedData();
            branchId.forEach(x -> areaClassBranchListDtoCache.remove(x));
        }

        //删除门店旧绑定的城市分类
        if (ObjectUtil.isNotNull(updateReqVO.getSupplierId())) {
            List<Long> branchId = branchApi.getBySupplier(updateReqVO.getSupplierId()).getCheckedData();
            branchId.forEach(x -> areaClassBranchListDtoCache.remove(x));
        }
    }

    @Override
    public void removeMultilevelClasses(Long[] areaClassIds) {
        //一键删除父类及子类。 注意，必须对父类即子类做上架商品判定，存在上架商品需要跳过当前分类和上一级父类。

        //转换为集合
        List<Long> areaClassIdList = ListUtil.toList(areaClassIds);

        //存在上架商品需要过滤的三级展示ID集合
        Set<Long> areaItemClassIdSet = new HashSet<>();

        //展示分类Map集合
        Map<Long,PrdtAreaClass> prdtAreaClassMap = new HashMap<>();


        //根据城市展示分类ID集合 获取城市上架商品集合
        List<PrdtAreaItem> prdtAreaItemList = areaItemMapper.selectByAreaClassIdList(areaClassIdList);

        //如果上架商品集合存在 则查询展示分类集合 进行组装数据
        if(!prdtAreaItemList.isEmpty()){
            prdtAreaClassMap = prdtAreaClassMapper.selectBatchIds(areaClassIdList).stream().collect(Collectors.toMap(PrdtAreaClass::getAreaClassId, x-> x));

            //组装成set集合 去除重复数据
            areaItemClassIdSet = prdtAreaItemList.stream().map(PrdtAreaItem::getAreaClassId).collect(Collectors.toSet());
        }

        //过滤 已存在上架数据的三级展示分类及父类
        for (Long x : areaItemClassIdSet) {
            //三级展示分类
            PrdtAreaClass thirdClass = prdtAreaClassMap.get(x);

            //二级展示分类
            PrdtAreaClass secondClass = prdtAreaClassMap.get(thirdClass.getPid());

            //一级展示分类
            PrdtAreaClass firstClass = prdtAreaClassMap.get(secondClass.getPid());

            //过滤
            areaClassIdList.remove(x);
            areaClassIdList.remove(secondClass.getAreaClassId());
            areaClassIdList.remove(firstClass.getAreaClassId());
        }

        //删除展示分类集合
        if(!areaClassIdList.isEmpty()){
            areaClassIdList.forEach(this::deleteClassAndRelevance);
        }else{
            throw exception(PRDT_AREA_CLASS_REMOVE_MULTILEVE);
        }
    }

    public String importAreaClassData(List<ProductAreaClassImportExcel> areaClassList) {
        return importAreaClassDataEvent(areaClassList,SecurityUtils.getLoginUser().getSysCode(),null,SecurityUtils.getLoginUser().getDcId(),0).getMsg();
    }

    /**
     * 导入城市展示分类数据
     * @param areaClassList
     * @return
     */
    @Override
    public FileImportHandlerVo importAreaClassDataEvent(List<ProductAreaClassImportExcel> areaClassList,Long sysCode,Long fileImportId,Long dcId,Integer seq) {
        FileImportHandlerVo fileImportHandlerVo = new FileImportHandlerVo();
        List<SysFileImportDtl> sysFileImportDtls = new ArrayList<>();
        int successNum = 0;
        int failureNum = 0;
        int totalNum = areaClassList.size();
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        fileImportHandlerVo.setSuccessNum(successNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setTotalNum(totalNum);


        if (areaClassList.isEmpty()) {
            // 如果导入数据为空，则不进行数据导入
            fileImportHandlerVo.setFailureNum(areaClassList.size());
            fileImportHandlerVo.setTotalNum(areaClassList.size());
            fileImportHandlerVo.setMsg("导入数据为空");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
        }
//        Long dcId = SecurityUtils.getLoginUser().getDcId();
//        Long sysCode  = SecurityUtils.getLoginUser().getSysCode();

        Map<String, Long> insertedCategoriesMap  = new HashMap<>(); // 存储已插入的类别，避免重复插入
        Map<String, Long> levelSortMap = new HashMap<>(); // 存储每一级的排序号
        for (int line = 0; line < areaClassList.size(); line++) {
            //导入明细
            SysFileImportDtl sysFileImportDtl = new SysFileImportDtl();
            sysFileImportDtl.setSysCode(sysCode);
            sysFileImportDtl.setCreateBy(sysFileImportDtl.getCreateBy());
            sysFileImportDtl.setCreateTime(new Date());
            sysFileImportDtl.setFileImportId(fileImportId);

            if (failureMsg.length() > 2000) {
                break; // 避免错误信息过长
            }

            int cellNumber = line + 3+seq; // Excel中的行号
            ProductAreaClassImportExcel itemData = areaClassList.get(line);
            sysFileImportDtl.setDtlJson(JsonUtils.toJsonString(itemData));

            try {
                // 校验导入数据的有效性
                validateAreaClassImportExcel(itemData, cellNumber,dcId,sysCode);

                Long primaryCategoryId = processCategory(itemData.getPrimaryCategoryName(), 1, insertedCategoriesMap, itemData.getRegionCode(), null,levelSortMap,sysCode);
                Long secondaryCategoryId = processCategory(itemData.getSecondaryCategoryName(), 2, insertedCategoriesMap, itemData.getRegionCode(), primaryCategoryId,levelSortMap,sysCode);

                // 验证三级分类在当前二级分类下是否唯一
                  PrdtAreaClass existingTertiaryCategory = prdtAreaClassMapper.checkUniqueTertiaryCategoryInSecondaryCategory(itemData.getTertiaryCategoryName(), secondaryCategoryId,itemData.getRegionCode());
                if (existingTertiaryCategory != null) {
                    throw new Exception(StringUtils.format("三级类别 '{}' 已存在于二级类别 '{}' 下", itemData.getTertiaryCategoryName(), itemData.getSecondaryCategoryName()));
                }

                processCategory(itemData.getTertiaryCategoryName(), 3, insertedCategoriesMap, itemData.getRegionCode(), secondaryCategoryId,levelSortMap,sysCode);
                successNum++;
                sysFileImportDtl.setStatus(STATUS_SUCCESS);
                sysFileImportDtls.add(sysFileImportDtl);
            } catch (Exception e) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据导入失败，错误信息：{}。", cellNumber, e.getMessage()));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据导入失败，错误信息：{}。", cellNumber, e.getMessage()));
                sysFileImportDtls.add(sysFileImportDtl);
            }
        }
        String resultMessage;
        // 返回导入结果
        fileImportHandlerVo.setTotalNum(totalNum);
        fileImportHandlerVo.setSuccessNum(successNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setList(sysFileImportDtls);
        if (failureNum > 0) {
            resultMessage = String.format("共导入%d条，成功%d条，失败%d条。失败原因如下：", areaClassList.size(), successNum, failureNum)
                    + failureMsg.toString();
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            fileImportHandlerVo.setMsg(failureMsg.toString());
            return fileImportHandlerVo;
        } else {
            resultMessage =  String.format("恭喜您，数据已全部导入成功！共 %d 条", successNum);
            fileImportHandlerVo.setStatus(STATUS_SUCCESS);
            fileImportHandlerVo.setMsg(failureMsg.toString());
        }
        return fileImportHandlerVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyAreaClass(PrdtAreaClassCopyReqVO vo) {
        //校验复制类型
        Integer copyType = vo.getCopyType();
        if(ToolUtil.isEmpty(copyType)){
            throw exception(PRDT_AREA_CLASS_COPY_TYPE_NULL);
        }
        // 验证允不允许使用本地商品
        AppletBaseConfigDTO appletBaseConfigDTO = productCacheService.getAppletBaseConfigDTO(SecurityUtils.getLoginUser().getSysCode());
        if (Objects.nonNull(appletBaseConfigDTO) && StringPool.TWO.equals(appletBaseConfigDTO.getSaleClassSwitch())) {
            throw exception(SYS_PARTNER_CONFIG_LOCAL_DISABLE);
        }

        //开始复制
        if(PRDT_CLASS_COPY_TYPE_0.equals(copyType)){
            //单个复制上架分类信息
            copyClassByClassId(vo);

        }else if(PRDT_CLASS_COPY_TYPE_1.equals(copyType)){
            //根据城市ID一键复制上架分类信息
            copyClassByAreaId(vo);
        }
    }

    private void validateAreaClassImportExcel(ProductAreaClassImportExcel itemData, int cellNumber,Long dcId,Long sysCode) throws Exception {
        // 检查区域编号是否为空并且是有效的Long类型
        if (ToolUtil.isEmpty(itemData.getRegionCode()) || !isNumeric(String.valueOf(itemData.getRegionCode()))) {
            throw new Exception(StringUtils.format("第{}行数据区域编号必填且必须为数字", cellNumber));
        }

        List<AreaDTO> areaBySyscodeAndDcId = areaApi.getAreaBySyscodeAndDcId(sysCode, dcId).getCheckedData();
        List<Long> areaIds = areaBySyscodeAndDcId.stream()
                .map(AreaDTO::getAreaId)
                .collect(Collectors.toList());
        if (!areaIds.contains(itemData.getRegionCode())) {
            throw new Exception(StringUtils.format("第{}行数据区域编号 '{}' 不存在于当前运营商中", cellNumber, itemData.getRegionCode()));
        }
        // 检查区域编号是否在系统中存在
        AreaDTO areaDto = productCacheService.getAreaDto(itemData.getRegionCode());
        if(ToolUtil.isEmpty(areaDto)){
            throw new Exception(StringUtils.format("第{}行数据区域编号 '{}' 不存在或与系统不一致", cellNumber, itemData.getRegionCode()));
        }
        if (StringUtils.isEmpty(itemData.getPrimaryCategoryName()) || itemData.getPrimaryCategoryName().length() > 20) {
            throw new Exception(StringUtils.format("第{}行数据一级类别名称必填且长度不能超过20个字符", cellNumber));
        }
        if (StringUtils.isEmpty(itemData.getSecondaryCategoryName()) || itemData.getSecondaryCategoryName().length() > 20) {
            throw new Exception(StringUtils.format("第{}行数据二级类别名称必填且长度不能超过20个字符", cellNumber));
        }
        if (StringUtils.isEmpty(itemData.getTertiaryCategoryName()) || itemData.getTertiaryCategoryName().length() > 20) {
            throw new Exception(StringUtils.format("第{}行数据三级类别名称必填且长度不能超过20个字符", cellNumber));
        }
    }
    private Long processCategory(String categoryName, int level, Map<String, Long> categoryMap, Long regionCode, Long pid,Map<String, Long> levelSortMap,Long sysCode) throws Exception {
        PrdtAreaClass existingCategory;

        //检查一级类别是否已经存在
        if(level == 1){
            Long aLong = categoryMap.get(categoryName);
            if(aLong != null){
                return aLong;
            }
        }

        // 检查类别是否存在
        existingCategory = prdtAreaClassMapper.checkCategoryExistenceByNameAndLevel(categoryName, level, pid, regionCode);
        if (existingCategory != null) {
            return existingCategory.getAreaClassId();  // 返回已存在的类别ID
        }

        // 构建用于排序的键，以确保排序号在相同父类别和区域下是唯一的
        String sortKey = level + "_" + regionCode + "_" + (pid != null ? pid : "root");
        Long sortOrder = levelSortMap.getOrDefault(sortKey, 0L) + 1L;
        levelSortMap.put(sortKey, sortOrder);  // 更新当前级别和父级别的排序号

        // 如果类别不存在，则插入新类别
        if (categoryMap.containsKey(categoryName)) {
            return categoryMap.get(categoryName);  // 返回已插入类别的ID
        }
        PrdtAreaClassSaveReqVO createReqVO = new PrdtAreaClassSaveReqVO();
        if(level == 1){
            createReqVO.setPid(0L);
        }else{
            createReqVO.setPid(pid);
        }
        createReqVO.setAreaClassName(categoryName);
        createReqVO.setStatus("1");
        createReqVO.setLevel(level);
        createReqVO.setAreaId(regionCode);  // 区域代码
        createReqVO.setSort(sortOrder); // 设置排序号
        Long aLong = insertPrdtAreaClass(createReqVO,sysCode);
        if(level == 1){
            categoryMap.put(categoryName, aLong);  // 更新映射
        }
        return aLong;  // 返回新插入的类别ID
    }

    /**
     * 删除城市展示分类
     *
     * @param areaClassId 城市展示分类id
     */
    @Override
    public void deletePrdtAreaClass(Long areaClassId) {

            //删除展示分类校验
            // 查询本地商品还有多少在上架
            Integer count = prdtAreaClassMapper.selectCountByAreaItem(areaClassId);
            if (Objects.nonNull(count) && count > 0) {
                throw exception(PRDT_AREA_CLASS_AREA_RELEASE);
            }
            // 是否还有有效的下级
            Long haveChild = prdtAreaClassMapper.selectCountValidateChild(areaClassId);
            if (Objects.nonNull(haveChild) && haveChild > 0L) {
                throw exception(PRDT_AREA_CLASS_AREA_HAVE_CHILD);
            }

            //逻辑删除分类信息及清除展示分类相关联的缓存等信息
            deleteClassAndRelevance(areaClassId);


    }

    /**
     * 逻辑删除分类信息及清除展示分类相关联的缓存等信息
     * @param areaClassId
     */
    private void deleteClassAndRelevance(Long areaClassId){
        PrdtAreaClass areaClass = prdtAreaClassMapper.selectById(areaClassId);
        // 删除
        areaClass.setDelFlag(StringPool.ONE);
        prdtAreaClassMapper.updateById(areaClass);

        //删除城市展示分类基础缓存
        areaClassDtoCache.remove(areaClassId);
        //获取原渠道绑定分类信息
        if (areaClass.getLevel() != NumberPool.INT_THREE) {
            // 因为一级分类无渠道信息, 所以这里更新所有的区域
            List<ChannelDTO> channelDTOList = channelApi.getChannelList().getCheckedData();
            if (!channelDTOList.isEmpty()) {
                channelDTOList.forEach(channelDTO -> {
                    areaClassAreaChannelListDtoCache.remove(areaClass.getAreaId() + "-" + channelDTO.getChannelId());
                });
            }
        } else {
            // 三级分类清除绑定的指定缓存
            //获取原渠道绑定分类信息
            List<PrdtChannelAreaClass> prdtChannelAreaClassList = channelAreaClassMapper.getListByAreaClassId(areaClass.getAreaClassId());
            //删除区域城市或渠道绑定的城市展示分类(老数据)
            areaClassAreaChannelListDtoCache.remove(areaClass.getAreaId() + "-0");
            if (ObjectUtil.isNotEmpty(prdtChannelAreaClassList)) {
                prdtChannelAreaClassList.forEach(x -> {
                    areaClassAreaChannelListDtoCache.remove(areaClass.getAreaId() + "-" + x.getChannelId());
                });
            }
        }
        List<PrdtChannelAreaClass> channelAreaClasses = channelAreaClassMapper.selectByAreaClassId(areaClassId);
        List<Long> channelList = channelAreaClasses.stream().map(PrdtChannelAreaClass::getChannelId).collect(Collectors.toList());

        //删除区域城市或渠道绑定的城市展示分类(新数据)
        areaClassAreaChannelListDtoCache.remove(areaClass.getAreaId() + "-0");
        if (ObjectUtil.isNotEmpty(channelList)) {
            channelList.forEach(x -> {
                areaClassAreaChannelListDtoCache.remove(areaClass.getAreaId() + "-" + x);
            });
        }
        if (ObjectUtil.isNotNull(areaClass.getSupplierId())) {
            //删除门店新绑定的城市分类
            List<Long> branchId = branchApi.getBySupplier(areaClass.getSupplierId()).getCheckedData();
            branchId.forEach(x -> areaClassBranchListDtoCache.remove(x));
        }
        if (ObjectUtil.isNotNull(areaClass.getSupplierId())) {
            //删除门店旧绑定的城市分类
            List<Long> branchId = branchApi.getBySupplier(areaClass.getSupplierId()).getCheckedData();
            branchId.forEach(x -> areaClassBranchListDtoCache.remove(x));
        }
    }

    /**
     * 批量删除城市展示分类
     *
     * @param areaClassIds 需要删除的城市展示分类主键
     * @return 结果
     */
    @Override
    public void deletePrdtAreaClassByAreaClassIds(Long[] areaClassIds) {
        for (Long areaClassId : areaClassIds) {
            this.deletePrdtAreaClass(areaClassId);
        }
    }

    /**
     * 获得城市展示分类
     *
     * @param areaClassId 城市展示分类id
     * @return 城市展示分类
     */
    @Override
    public PrdtAreaClassRespVO getPrdtAreaClass(Long areaClassId) {
        PrdtAreaClass prdtAreaClass = prdtAreaClassMapper.selectById(areaClassId);
        PrdtAreaClassRespVO prdtAreaClassRespVO = HutoolBeanUtils.toBean(prdtAreaClass, PrdtAreaClassRespVO.class);
        List<Long> channelIds = channelAreaClassMapper.getListByAreaClassId(areaClassId)
                .stream()
                .map(PrdtChannelAreaClass::getChannelId)
                .collect(Collectors.toList());
        if (ToolUtil.isNotEmpty(prdtAreaClassRespVO)){
            prdtAreaClassRespVO.setChannelIds(channelIds);
        }
        return prdtAreaClassRespVO;
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrdtAreaClassRespVO> getPrdtAreaClassPage(PrdtAreaClassPageReqVO pageReqVO) {
        PageResult<PrdtAreaClassRespVO> prdtAreaClassPageResult =
                HutoolBeanUtils.toBean(prdtAreaClassMapper.selectPage(pageReqVO), PrdtAreaClassRespVO.class);
        List<PrdtAreaClassRespVO> resultList = prdtAreaClassPageResult.getList();
        //填充区域名称
        resultList = resultList.stream().peek(x -> {
            if (ObjectUtil.isNotNull(x.getAreaId())) {
                AreaDTO areaDto = productCacheService.getAreaDto(x.getAreaId());
                if (ObjectUtil.isNotNull(areaDto)) x.setAreaName(areaDto.getAreaName());
            }
        }).collect(Collectors.toList());
        prdtAreaClassPageResult.setList(resultList);
        return prdtAreaClassPageResult;
    }

    /**
     * @Description: 获取所有城市展示分类信息
     * @Author: liuxingyu
     * @Date: 2024/3/23 9:07
     */
    @Override
    public List<PrdtAreaClass> getAreaClassList(Long areaClassId, Long areaId, String status, Integer level) {
        List<Long> areaIds = new ArrayList<>();
        if (ObjectUtil.isNotNull(areaId)) {
            areaIds.add(areaId);
            return prdtAreaClassMapper.getAreaClassList(areaClassId, areaIds, status, level);
        }
        if (ObjectUtil.isNotNull(SecurityUtils.getLoginUser().getDcId())) {
            DcAreaGroupDTO dcAreaGroup = redisService.getCacheObject(RedisConstants.DC_AREA_GROUP + SecurityUtils.getLoginUser().getDcId());
            if (ObjectUtil.isNotNull(dcAreaGroup)) {
                areaIds = dcAreaGroup.getAreaIds();
            }
        }
        return prdtAreaClassMapper.getAreaClassList(areaClassId, areaIds, status, level);
    }

    /**
     * @Description: 根据Id获取城市展示分类
     * @Author: liuxingyu
     * @Date: 2024/3/25 14:57
     */
    @Override
    public PrdtAreaClass getAreaClassByAreaClassId(Long areaClassId) {
        return prdtAreaClassMapper.selectById(areaClassId);
    }

    /**
     * @Description: 获取门店绑定的城市展示分类(根据入驻商ID集合获取)
     * @Author: liuxingyu
     * @Date: 2024/3/28 17:46
     */
    @Override
    public List<PrdtAreaClass> getAreaClassBranchList(List<Long> supplierIds) {
        return prdtAreaClassMapper.getAreaClassBranchList(supplierIds);
    }

    /**
     * 批量获取展示分类信息   (暂时用于支持数据回显)
     *
     * @param saleClassIds 展示分类ID集合
     * @return 展示分类集合
     */
    @Override
    public List<PrdtAreaClassRespVO> getSelectedAreaClass(List<Long> saleClassIds) {
        return BeanUtil.copyToList(prdtAreaClassMapper.selectSelectedAreaClass(saleClassIds), PrdtAreaClassRespVO.class);
    }

    /**
     * @Description: 获取城市展示分类详情
     * @Author: liuxingyu
     * @Date: 2024/4/13 9:25
     */
    @Override
    public PageResult<PrdtAreaClassRespVO> getAreaClassDetail(PrdtAreaClassPageReqVO reqVO) {
        PageResult<PrdtAreaClassRespVO> prdtAreaClassPageResult =
                HutoolBeanUtils.toBean(prdtAreaClassMapper.selectPage(reqVO), PrdtAreaClassRespVO.class);
        List<PrdtAreaClassRespVO> resultList = prdtAreaClassPageResult.getList();
        //填充区域名称
        resultList = resultList.stream().peek(x -> {
            if (ObjectUtil.isNotNull(x.getAreaId())) {
                AreaDTO areaDto = productCacheService.getAreaDto(x.getAreaId());
                if (ObjectUtil.isNotNull(areaDto)) x.setAreaName(areaDto.getAreaName());
            }
        }).collect(Collectors.toList());
        prdtAreaClassPageResult.setList(resultList);
        return prdtAreaClassPageResult;
    }

    /**
     * @Description: 根据门店区域和门店渠道获取城市展示分类
     * @Author: liuxingyu
     * @Date: 2024/4/24 10:50
     */
    @Override
    public List<PrdtAreaClass> getAreaClassAreaChannelList(String key) {
        String[] split = key.split("-");
        Long areaId = Long.valueOf(split[0]);
        Long channelId = Long.valueOf(split[1]);
        return prdtAreaClassMapper.getAreaClassAreaChannelList(areaId, channelId);
    }

    /**
     * @Description: 同步子级数据
     * @Author: liuxingyu
     * @Date: 2024/4/16 17:37
     */
    private List<PrdtAreaClass> synSublevel(PrdtAreaClassSaveReqVO updateReqVO) {
        // 二级
        List<PrdtAreaClass> second = prdtAreaClassMapper.selectByPid(updateReqVO.getAreaClassId());
        List<PrdtAreaClass> family = new ArrayList<>(second);

        // 三级
        List<PrdtAreaClass> three = second.stream().map(PrdtAreaClass::getAreaClassId).map(prdtAreaClassMapper::selectByPid).flatMap(Collection::stream).collect(Collectors.toList());
        family.addAll(three);

        // 更新子节点状态, 目前主要考虑电子围栏开关
        ArrayList<PrdtAreaClass> updateList = new ArrayList<>();
        for (PrdtAreaClass areaClass : family) {

            PrdtAreaClass update = new PrdtAreaClass();
            update.setAreaClassId(areaClass.getAreaClassId());
            update.setDzwlFlag(updateReqVO.getDzwlFlag());
            // 如果不是启用, 子节点同步状态属性
            if (StringUtils.isNotEmpty(updateReqVO.getStatus()) && !StringPool.ONE.equals(updateReqVO.getStatus())) {
                update.setStatus(updateReqVO.getStatus());
            }
            updateList.add(update);
        }
        if (!updateList.isEmpty()) {
            prdtAreaClassMapper.updateBatch(updateList);
        }
        return family;
    }

    /**
     * @Description: 校验级别
     * @Author: liuxingyu
     * @Date: 2024/4/1 15:56
     */
    private void checkAreaClassLevel(PrdtAreaClassSaveReqVO createReqVO) {
        //ID不为空则修改
        if (ObjectUtil.isNotNull(createReqVO.getAreaClassId())) {
            //如果当前分类非末级则不允许改变层级(存在子级)
            PrdtAreaClass prdtAreaClass = prdtAreaClassMapper.selectById(createReqVO.getAreaClassId());
            if (ObjectUtil.notEqual(nullToZero(createReqVO.getPid()), nullToZero(prdtAreaClass.getPid()))) {
                List<PrdtAreaClass> areaClasses = prdtAreaClassMapper.selectByPid(createReqVO.getAreaClassId());
                if (ObjectUtil.isNotEmpty(areaClasses)) {
                    throw exception(PRDT_AREA_CLASS_IS_SUB_EXISTS);
                }
                //校验是否绑定了商品
                List<PrdtAreaItem> prdtAreaItems = areaItemMapper.selectByAreaClassId(createReqVO.getAreaClassId());
                if (ObjectUtil.isNotEmpty(prdtAreaItems)) {
                    throw exception(PRDT_AREA_CLASS_ITEM_IS_NOT_NULL);
                }
            }
        }
        //获取父级类别
        if (ObjectUtil.isNotNull(createReqVO.getPid()) && ObjectUtil.notEqual(createReqVO.getPid(), NumberPool.LONG_ZERO)) {
            PrdtAreaClass prdtAreaClass = prdtAreaClassMapper.selectById(createReqVO.getPid());
            if (ObjectUtil.isNull(prdtAreaClass) || ObjectUtil.isNull(prdtAreaClass.getLevel())) {
                throw exception(PRDT_AREA_CLASS_PID_ERROR);
            }
            //校验级别
            if (ObjectUtil.equal(prdtAreaClass.getLevel(), NumberPool.INT_THREE)) {
                throw exception(PRDT_AREA_CLASS_LEVEL_EXCEED_MAX);
            }
            createReqVO.setLevel(prdtAreaClass.getLevel() + NumberPool.INT_ONE);
            createReqVO.setSupplierId(prdtAreaClass.getSupplierId());
            createReqVO.setAreaId(prdtAreaClass.getAreaId());
            createReqVO.setDzwlFlag(prdtAreaClass.getDzwlFlag());
            //如果父级为二级 校验电子围栏和渠道不能同时开启
            if (ObjectUtil.equal(prdtAreaClass.getLevel(), NumberPool.INT_TWO)
                    && ObjectUtil.isNotEmpty(createReqVO.getChannelIds())
                    && ObjectUtil.equal(prdtAreaClass.getDzwlFlag(), NumberPool.LONG_ONE)) {
                throw exception(PRDT_AREA_CLASS_DZWL_CHANNEL_NPT_BIND);
            }
        } else {
            createReqVO.setLevel(NumberPool.INT_ONE);
        }
    }

    /**
     * @Description: 校验分类名是否唯一
     * @Param: String name 名称, Integer pid 父Id
     * @Author: liuxingyu
     * @Date: 2024/2/27 16:12
     */
    private void validate(PrdtAreaClassSaveReqVO saveReqVO) {
        validate(saveReqVO,SecurityUtils.getLoginUser().getSysCode());
    }

    private void validate(PrdtAreaClassSaveReqVO saveReqVO,Long sysCode){
        // 验证名称唯一性
        Long count = prdtAreaClassMapper.checkNameUnique(
                saveReqVO.getAreaClassName(),
                saveReqVO.getPid(),
                saveReqVO.getAreaId(),
                saveReqVO.getAreaClassId());
        if (ObjectUtil.notEqual(NumberPool.LONG_ZERO, count)) {
            throw exception(PRDT_AREA_CLASS_NAME_REUSE);
        }

        // 验证允不允许使用本地商品
        AppletBaseConfigDTO appletBaseConfigDTO = productCacheService.getAppletBaseConfigDTO(sysCode);
        if (Objects.nonNull(appletBaseConfigDTO) && StringPool.TWO.equals(appletBaseConfigDTO.getSaleClassSwitch())) {
            throw exception(SYS_PARTNER_CONFIG_LOCAL_DISABLE);
        }
    }

    private void validatePrdtAreaClassExists(Long areaClassId) {
        if (prdtAreaClassMapper.selectById(areaClassId) == null) {
            throw exception(PRDT_AREA_CLASS_NOT_EXISTS);
        }
    }


    /**
     * 复制分类 - 组装新分类数据
     * @param copyClass 复制前的分类信息
     * @param targetPidClass  目标父级分类信息
     * @param areaId 城市ID
     * @param targetAreaClassPid 目标父级分类ID
     * @return
     */
    private PrdtAreaClass assembleCopyClass(PrdtAreaClass copyClass,PrdtAreaClass targetPidClass,Long areaId,Long targetAreaClassPid){
        //设置新增参数
        PrdtAreaClass insertClass = new PrdtAreaClass();
        insertClass.setAreaId(areaId);
        insertClass.setPid(targetAreaClassPid);
        insertClass.setAreaClassName(copyClass.getAreaClassName());
        insertClass.setIcon(copyClass.getIcon());
        insertClass.setSort(copyClass.getSort());
        insertClass.setStatus(copyClass.getStatus());
        //设置等级 如果pid是0 则是最高级 新增的分类为一级分类
        if(NumberPool.LONG_ZERO == targetAreaClassPid){
            insertClass.setLevel(NumberPool.INT_ONE);
        }else{
            insertClass.setLevel(targetPidClass.getLevel() + NumberPool.INT_ONE);
        }

        //设置默认配置
        //电子围栏配置默认 否
        insertClass.setDzwlFlag(NumberPool.LONG_ZERO);
        //删除标识 默认存在
        insertClass.setDelFlag(StringPool.ZERO);
        //入驻商ID 默认为空 电子围栏相关配置

        //校验级别
        if (insertClass.getLevel().compareTo(NumberPool.INT_THREE) > NumberPool.INT_ZERO) {
            throw exception(PRDT_AREA_CLASS_LEVEL_EXCEED_MAX);
        }

        return insertClass;
    }

    /**
     * 单个复制上架分类信息
     */
    private void copyClassByClassId(PrdtAreaClassCopyReqVO vo){
        //复制前的城市展示分类id
        Long copyAreaClassId = vo.getCopyAreaClassId();

        //复制后的目标城市展示分类上级id
        Long targetAreaClassPid = vo.getTargetAreaClassPid();

        //城市ID
        Long areaId = vo.getAreaId();

        //校验目标城市ID 必须是二级城市
        checkAreaIdBySecond(areaId);

        //单个复制
        //校验 目标父级分类ID、复制分类ID是否存在
        if(ToolUtil.isEmpty(copyAreaClassId) || ToolUtil.isEmpty(targetAreaClassPid) || ToolUtil.isEmpty(areaId)){
            throw exception(PRDT_AREA_CLASS_COPY_0_DATA_NULL);
        }
        //获取 目标父级分类ID、复制分类ID对应的分类信息
        //复制分类
        PrdtAreaClass copyClass = prdtAreaClassMapper.selectById(copyAreaClassId);
        //目标父级分类
        PrdtAreaClass targetPidClass = prdtAreaClassMapper.selectById(targetAreaClassPid);


        //校验该目标父级分类下是否已存在该复制分类
        // 验证名称唯一性
        Long count = prdtAreaClassMapper.checkNameUnique(
                copyClass.getAreaClassName(),
                targetAreaClassPid,
                areaId,
                null);
        if (ObjectUtil.notEqual(NumberPool.LONG_ZERO, count)) {
            throw exception(PRDT_AREA_CLASS_NAME_REUSE);
        }

        //组装新分类数据
        PrdtAreaClass insertClass = assembleCopyClass(copyClass, targetPidClass, areaId, targetAreaClassPid);

        //新增
        prdtAreaClassMapper.insert(insertClass);

        //删除区域城市或渠道绑定的城市展示分类
        areaClassAreaChannelListDtoCache.remove(insertClass.getAreaId() + "-0");
    }

    /**
     * 根据城市ID一键复制上架分类信息
     */
    private void copyClassByAreaId(PrdtAreaClassCopyReqVO vo){
//一键城市分类复制
        //校验 目标城市ID、复制城市ID是否存在
        if(ToolUtil.isEmpty(vo.getCopyAreaId()) || ToolUtil.isEmpty(vo.getTargetAreaId())){
            throw exception(PRDT_AREA_CLASS_COPY_1_DATA_NULL);
        }

        //复制前的城市id
        Long copyAreaId = vo.getCopyAreaId();

        //复制后的目标城市id
        Long targetAreaId = vo.getTargetAreaId();

        //校验目标城市ID 必须是二级城市
        checkAreaIdBySecond(targetAreaId);

        //需要新增的三级分类集合
        List<PrdtAreaClass> insertThirdClassList = new ArrayList<>();


        //查询复制城市下所有的一级展示分类
        for (PrdtAreaClass firstClass : prdtAreaClassMapper.selectClassList(NumberPool.LONG_ZERO, copyAreaId, null, NumberPool.INT_ONE)) {
            //校验该一级分类是否已经在目标城市存在
            PrdtAreaClass checkFirstClass = prdtAreaClassMapper.selectClassByCopy(
                    firstClass.getAreaClassName(),
                    firstClass.getPid(),
                    targetAreaId,
                    null);

            //如果一级分类名称不在该城市下存在 则该一级分类下的所有二级、三级分类都直接新增
            if (ToolUtil.isEmpty(checkFirstClass)) {

                //组装新一级分类数据
                PrdtAreaClass insertFirstClass = assembleCopyClass(firstClass, null, targetAreaId, NumberPool.LONG_ZERO);

                //新增一级分类
                prdtAreaClassMapper.insert(insertFirstClass);

                //获取一级分类下的所有二级分类
                for (PrdtAreaClass secondAreaClass : prdtAreaClassMapper.selectClassList(firstClass.getAreaClassId(), copyAreaId, null, NumberPool.INT_TWO)) {
                    //组装新二级分类数据
                    PrdtAreaClass insertSecondClass = assembleCopyClass(secondAreaClass, insertFirstClass, targetAreaId, insertFirstClass.getAreaClassId() );

                    //新增二级分类
                    prdtAreaClassMapper.insert(insertSecondClass);

                    //获取二级分类下的所有三级分类
                    for (PrdtAreaClass thirdAreaClass : prdtAreaClassMapper.selectClassList(secondAreaClass.getAreaClassId(), copyAreaId, null, NumberPool.INT_THREE)) {
                        //组装新三级分类数据
                        PrdtAreaClass insertThirdClass = assembleCopyClass(thirdAreaClass, insertSecondClass, targetAreaId, insertSecondClass.getAreaClassId() );

                        //新增三级分类
                        //prdtAreaClassMapper.insert(insertThirdClass);
                        insertThirdClassList.add(insertThirdClass);
                    }


                }
            }else{
                //如果一级分类名称已经在该城市下存在  则不需要新增新的一级分类 直接在该一级分类下做复制
                //获取原一级分类下的所有二级分类
                for (PrdtAreaClass secondAreaClass : prdtAreaClassMapper.selectClassList(firstClass.getAreaClassId(), copyAreaId, null, NumberPool.INT_TWO)) {

                    //校验该二级分类名称是否已经在目标城市存在
                    PrdtAreaClass checkSecondClass = prdtAreaClassMapper.selectClassByCopy(
                            secondAreaClass.getAreaClassName(),
                            checkFirstClass.getAreaClassId(),
                            targetAreaId,
                            null);

                    //目标二级分类数据
                    PrdtAreaClass insertSecondClass = new PrdtAreaClass();

                    //目标是否存在该复制二级分类的标识
                    boolean checkSecondFlag = false;

                    //如果该二级分类不存在 则在已存在的目标一级分类下增加该二级分类
                    if(ToolUtil.isEmpty(checkSecondClass)){
                        //组装新二级分类数据
                        insertSecondClass = assembleCopyClass(secondAreaClass, checkFirstClass, targetAreaId, checkFirstClass.getAreaClassId() );

                        //新增二级分类
                        prdtAreaClassMapper.insert(insertSecondClass);
                    }else{
                        insertSecondClass = checkSecondClass;
                        checkSecondFlag = true;
                    }


                    //获取二级分类下的所有三级分类
                    for (PrdtAreaClass thirdAreaClass : prdtAreaClassMapper.selectClassList(secondAreaClass.getAreaClassId(), copyAreaId, null, NumberPool.INT_THREE)) {

                        //校验该三级分类名称是否已经在目标城市存在 如果该二级分类不在目标分类下 或者 该二级分类在目标分类下 但是该三级分类不在目标分类下
                        if(!checkSecondFlag || ToolUtil.isEmpty(prdtAreaClassMapper.selectClassByCopy(
                                thirdAreaClass.getAreaClassName(),
                                insertSecondClass.getAreaClassId(),
                                targetAreaId,
                                null))){
                            //组装新三级分类数据
                            PrdtAreaClass insertThirdClass = assembleCopyClass(thirdAreaClass, insertSecondClass, targetAreaId, insertSecondClass.getAreaClassId());

                            //新增三级分类
                            //prdtAreaClassMapper.insert(insertThirdClass);
                            insertThirdClassList.add(insertThirdClass);
                        }

                    }
                }
            }
        }

        //新增需要复制到目标分类的三级分类
        if(!insertThirdClassList.isEmpty()){
            prdtAreaClassMapper.insertBatch(insertThirdClassList);
        }

        //删除区域城市或渠道绑定的城市展示分类
        areaClassAreaChannelListDtoCache.remove( targetAreaId+ "-0");
    }

    /**
     * @Description: 如果为null 转换成0
     * @Author: liuxingyu
     * @Date: 2024/6/7 11:19
     */
    private Long nullToZero(Long pid) {
        return ObjectUtil.isNull(pid) ? 0L : pid;
    }

    /**
     *  校验目标城市ID 必须是二级城市
     * @param areaId
     */
    private void checkAreaIdBySecond(Long areaId){
        AreaDTO areaDto = productCacheService.getAreaDto(areaId);
        if(!Objects.equals(areaDto.getLevel(),NumberPool.INT_TWO)){
            throw exception(SYS_AREA_LEVEL_NOT_TWO);
        }
    }

    /**
     * 城市展示分类导出
     * @param pageVo
     * @return
     */
    @Override
    public List<PrdtAreaClassExportVo> getPrdtAreaClassExportList(PrdtAreaClassExportVo pageVo) {
        List<PrdtAreaClassExportVo> prdtAreaClassExportVoList = prdtAreaClassMapper.selectPrdtAreaClassExportList(pageVo);
        for (PrdtAreaClassExportVo prdtAreaClassExportVo : prdtAreaClassExportVoList) {
            AreaDTO areaDto = productCacheService.getAreaDto(prdtAreaClassExportVo.getAreaId());
            if (ToolUtil.isNotEmpty(areaDto)){ // 城市名称
                prdtAreaClassExportVo.setAreaName(areaDto.getAreaName());
            }
            PrdtAreaClassRespVO pidAreaClass = this.getPrdtAreaClass(prdtAreaClassExportVo.getPid());
            StringBuilder channelName = new StringBuilder();
            if (ToolUtil.isNotEmpty(pidAreaClass)){ // 上级类别
                prdtAreaClassExportVo.setPidName(pidAreaClass.getAreaClassName());
            }
            PrdtAreaClassRespVO prdtAreaClass = getPrdtAreaClass(prdtAreaClassExportVo.getAreaClassId());
            if (ToolUtil.isNotEmpty(prdtAreaClass) && ToolUtil.isNotEmpty(prdtAreaClass.getChannelIds())){
                for (Long channelId : prdtAreaClass.getChannelIds()) { // 渠道名称
                    ChannelDTO channelDto = productCacheService.getChannelDto(channelId);
                    if (ToolUtil.isNotEmpty(channelDto)){
                        channelName.append(channelDto.getChannelName()).append(";");
                    }
                }
                prdtAreaClassExportVo.setChannelIdName(channelName.toString());
            }
        }
        return prdtAreaClassExportVoList;
    }
}
