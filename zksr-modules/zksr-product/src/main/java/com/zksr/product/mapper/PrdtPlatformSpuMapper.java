package com.zksr.product.mapper;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.controller.platform.vo.PrdtPlatformSpuPageReqVO;
import com.zksr.product.domain.PrdtPlatformSpu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 平台商品库-商品SPUMapper接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Mapper
public interface PrdtPlatformSpuMapper extends BaseMapperX<PrdtPlatformSpu> {
    default PageResult<PrdtPlatformSpu> selectPage(PrdtPlatformSpuPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtPlatformSpu>()
                    .eqIfPresent(PrdtPlatformSpu::getPlatformSpuId, reqVO.getPlatformSpuId())
                    .eqIfPresent(PrdtPlatformSpu::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtPlatformSpu::getSupplierId, reqVO.getSupplierId())
                    .likeIfPresent(PrdtPlatformSpu::getCatgoryName, reqVO.getCatgoryName())
                    .likeIfPresent(PrdtPlatformSpu::getBrandName, reqVO.getBrandName())
                    .likeIfPresent(PrdtPlatformSpu::getSpuNo, reqVO.getSpuNo())
                    .likeIfPresent(PrdtPlatformSpu::getSpuName, reqVO.getSpuName())
                    .eqIfPresent(PrdtPlatformSpu::getThumb, reqVO.getThumb())
                    .eqIfPresent(PrdtPlatformSpu::getThumbVideo, reqVO.getThumbVideo())
                    .eqIfPresent(PrdtPlatformSpu::getImages, reqVO.getImages())
                    .eqIfPresent(PrdtPlatformSpu::getDetails, reqVO.getDetails())
                    .eqIfPresent(PrdtPlatformSpu::getIsDelete, reqVO.getIsDelete())
                    .eqIfPresent(PrdtPlatformSpu::getStatus, reqVO.getStatus())
                    .likeIfPresent(PrdtPlatformSpu::getSpecName, reqVO.getSpecName())
                    .eqIfPresent(PrdtPlatformSpu::getIsSpecs, reqVO.getIsSpecs())
                    .eqIfPresent(PrdtPlatformSpu::getMinUnit, reqVO.getMinUnit())
                    .eqIfPresent(PrdtPlatformSpu::getMidUnit, reqVO.getMidUnit())
                    .eqIfPresent(PrdtPlatformSpu::getMidSize, reqVO.getMidSize())
                    .eqIfPresent(PrdtPlatformSpu::getLargeUnit, reqVO.getLargeUnit())
                    .eqIfPresent(PrdtPlatformSpu::getLargeSize, reqVO.getLargeSize())
                    .eqIfPresent(PrdtPlatformSpu::getSpuId, reqVO.getSpuId())
                    .eqIfPresent(PrdtPlatformSpu::getCopyTimes, reqVO.getCopyTimes())
                .orderByDesc(PrdtPlatformSpu::getPlatformSpuId));
    }

    default PrdtPlatformSpu selectBySpuId(Long spuId) {
        return selectOne( new LambdaQueryWrapperX<PrdtPlatformSpu>()
                .eqIfPresent(PrdtPlatformSpu::getSpuId, spuId));
    }

    /**
     * 根据商品编号查询商品库商品信息
     * @param spuNo
     * @return
     */
    default List<PrdtPlatformSpu> selectBySpuNo(String spuNo) {
        return selectList( new LambdaQueryWrapperX<PrdtPlatformSpu>()
                .eqIfPresent(PrdtPlatformSpu::getSpuNo, spuNo));
    }

    List<PrdtPlatformSpu> selectPageExt(@Param("reqVO") PrdtPlatformSpuPageReqVO pageReqVO);
}
