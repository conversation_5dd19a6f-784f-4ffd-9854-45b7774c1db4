package com.zksr.product.controller.blockScheme;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtBlockScheme;
import com.zksr.product.service.IPrdtBlockSchemeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.blockScheme.vo.PrdtBlockSchemePageReqVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSchemeSaveReqVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSchemeRespVO;
import com.zksr.product.convert.blockScheme.PrdtBlockSchemeConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 经营屏蔽方案Controller
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Api(tags = "管理后台 - 经营屏蔽方案接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/blockScheme")
public class PrdtBlockSchemeController {
    @Autowired
    private IPrdtBlockSchemeService prdtBlockSchemeService;

    /**
     * 新增经营屏蔽方案
     */
    @ApiOperation(value = "新增经营屏蔽方案", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "经营屏蔽方案", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public CommonResult<Long> add(@Valid @RequestBody PrdtBlockSchemeSaveReqVO createReqVO) {
        return success(prdtBlockSchemeService.insertPrdtBlockScheme(createReqVO));
    }

    /**
     * 修改经营屏蔽方案
     */
    @ApiOperation(value = "修改经营屏蔽方案", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "经营屏蔽方案", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtBlockSchemeSaveReqVO updateReqVO) {
            prdtBlockSchemeService.updatePrdtBlockScheme(updateReqVO);
        return success(true);
    }

    /**
     * 经营屏蔽方案-批量启用
     */
    @ApiOperation(value = "经营屏蔽方案-批量启用", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "经营屏蔽方案", businessType = BusinessType.UPDATE)
    @PostMapping("/batchEnable/{blockSchemeIds}")
    public CommonResult<Boolean> batchEnable(@PathVariable List<Long> blockSchemeIds) {
        prdtBlockSchemeService.batchEnable(blockSchemeIds);
        return success(true);
    }

    /**
     * 经营屏蔽方案-批量禁用
     */
    @ApiOperation(value = "经营屏蔽方案-批量禁用", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "经营屏蔽方案", businessType = BusinessType.UPDATE)
    @PostMapping("/batchDisable/{blockSchemeIds}")
    public CommonResult<Boolean> batchDisable(@PathVariable List<Long> blockSchemeIds) {
        prdtBlockSchemeService.batchDisable(blockSchemeIds);
        return success(true);
    }

    /**
     * 删除经营屏蔽方案
     */
    @ApiOperation(value = "删除经营屏蔽方案", httpMethod = HttpMethod.DEL, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
//    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "经营屏蔽方案", businessType = BusinessType.DELETE)
    @DeleteMapping("/batchRemove/{blockSchemeIds}")
    public CommonResult<Boolean> batchRemove(@PathVariable List<Long> blockSchemeIds) {
        prdtBlockSchemeService.deletePrdtBlockSchemeByBlockSchemeIds(blockSchemeIds);
        return success(true);
    }

    /**
     * 获取经营屏蔽方案详细信息
     */
    @ApiOperation(value = "获得经营屏蔽方案详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{blockSchemeId}")
    public CommonResult<PrdtBlockSchemeRespVO> getInfo(@PathVariable("blockSchemeId") Long blockSchemeId) {
        return success(prdtBlockSchemeService.getPrdtBlockScheme(blockSchemeId));
    }

    /**
     * 分页查询经营屏蔽方案
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得经营屏蔽方案分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtBlockSchemeRespVO>> getPage(@Valid PrdtBlockSchemePageReqVO pageReqVO) {
        PageResult<PrdtBlockScheme> pageResult = prdtBlockSchemeService.getPrdtBlockSchemePage(pageReqVO);
        return success(PrdtBlockSchemeConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:blockScheme:add";
        /** 编辑 */
        public static final String EDIT = "product:blockScheme:edit";
        /** 删除 */
        public static final String DELETE = "product:blockScheme:remove";
        /** 列表 */
        public static final String LIST = "product:blockScheme:list";
        /** 查询 */
        public static final String GET = "product:blockScheme:query";
        /** 停用 */
        public static final String DISABLE = "product:blockScheme:disable";
        /** 启用 */
        public static final String ENABLE = "product:blockScheme:enable";
    }
}
