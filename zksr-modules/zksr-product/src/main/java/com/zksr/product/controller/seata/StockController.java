/*
 *  Copyright 1999-2021 Seata.io Group.
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.zksr.product.controller.seata;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.service.impl.StockService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * Program Name: springcloud-nacos-seata
 * <p>
 * Description:
 * <p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/8/28 4:05 PM
 */
@RestController
@RequestMapping("stock")
public class StockController {

    @Resource
    private StockService stockService;

    /**
     * 减库存
     *
     * @param commodityCode 商品代码
     * @param count         数量
     * @return
     */
    @RequestMapping(path = "/deduct")
    public CommonResult<Boolean> deduct(String commodityCode, Integer count) {
        stockService.deduct(commodityCode, count);
        return success(true);
    }

//    /**
//     * 减库存
//     *
//     * @return
//     */
//    @RequestMapping(path = "/deduct")
//    public CommonResult<Boolean> deduct() {
//        stockService.deduct("1", 1);
//        return success(true);
//    }

}
