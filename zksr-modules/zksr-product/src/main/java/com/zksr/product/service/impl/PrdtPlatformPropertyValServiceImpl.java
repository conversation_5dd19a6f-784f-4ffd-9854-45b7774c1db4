package com.zksr.product.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtPlatformPropertyValMapper;
import com.zksr.product.convert.platform.PrdtPlatformPropertyValConvert;
import com.zksr.product.domain.PrdtPlatformPropertyVal;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyValPageReqVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyValSaveReqVO;
import com.zksr.product.service.IPrdtPlatformPropertyValService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 规格值Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Service
public class PrdtPlatformPropertyValServiceImpl implements IPrdtPlatformPropertyValService {
    @Autowired
    private PrdtPlatformPropertyValMapper prdtPlatformPropertyValMapper;

    /**
     * 新增规格值
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtPlatformPropertyVal(PrdtPlatformPropertyValSaveReqVO createReqVO) {
        // 插入
        PrdtPlatformPropertyVal prdtPlatformPropertyVal = PrdtPlatformPropertyValConvert.INSTANCE.convert(createReqVO);
        prdtPlatformPropertyValMapper.insert(prdtPlatformPropertyVal);
        // 返回
        return prdtPlatformPropertyVal.getPlatformPropertyValId();
    }

    /**
     * 修改规格值
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtPlatformPropertyVal(PrdtPlatformPropertyValSaveReqVO updateReqVO) {
        prdtPlatformPropertyValMapper.updateById(PrdtPlatformPropertyValConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除规格值
     *
     * @param platformPropertyValId 规格值id
     */
    @Override
    public void deletePrdtPlatformPropertyVal(Long platformPropertyValId) {
        // 删除
        prdtPlatformPropertyValMapper.deleteById(platformPropertyValId);
    }

    /**
     * 批量删除规格值
     *
     * @param platformPropertyValIds 需要删除的规格值主键
     * @return 结果
     */
    @Override
    public void deletePrdtPlatformPropertyValByPlatformPropertyValIds(Long[] platformPropertyValIds) {
        for(Long platformPropertyValId : platformPropertyValIds){
            this.deletePrdtPlatformPropertyVal(platformPropertyValId);
        }
    }

    /**
     * 获得规格值
     *
     * @param platformPropertyValId 规格值id
     * @return 规格值
     */
    @Override
    public PrdtPlatformPropertyVal getPrdtPlatformPropertyVal(Long platformPropertyValId) {
        return prdtPlatformPropertyValMapper.selectById(platformPropertyValId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtPlatformPropertyVal> getPrdtPlatformPropertyValPage(PrdtPlatformPropertyValPageReqVO pageReqVO) {
        return prdtPlatformPropertyValMapper.selectPage(pageReqVO);
    }


}
