package com.zksr.product.domain.dto;

import com.zksr.product.api.model.event.EsReleaseProductEvent;
import com.zksr.product.api.model.event.EsSkuUpdateProductEvent;
import com.zksr.product.api.model.event.EsSpuUpdateProductEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 加载数据库数据搜索实体
 * @date 2024/3/1 9:41
 */
@Data
@NoArgsConstructor
public class EsProductLoadReqDTO {

    @ApiModelProperty("上架发布商品ID, 非商品ID")
    private List<Long> itemIds;

    @ApiModelProperty("spuIds")
    private List<Long> spuIds;

    @ApiModelProperty("skuIds")
    private List<Long> skuIds;

    @ApiModelProperty(value = "批次最大ID", notes = "用于批量刷新")
    private Long minId;

    public EsProductLoadReqDTO(EsReleaseProductEvent data) {
        this.itemIds = data.getItemIds();
    }

    public EsProductLoadReqDTO(EsSpuUpdateProductEvent data) {
        this.spuIds = data.getSpuIds();
    }

    public EsProductLoadReqDTO(EsSkuUpdateProductEvent data) {
        this.skuIds = data.getSkuId();
    }

    public static EsProductLoadReqDTO build(EsReleaseProductEvent data) {
        return new EsProductLoadReqDTO(data);
    }

    public static EsProductLoadReqDTO build(EsSpuUpdateProductEvent data) {
        return new EsProductLoadReqDTO(data);
    }

    public static EsProductLoadReqDTO build(EsSkuUpdateProductEvent data) {
        return new EsProductLoadReqDTO(data);
    }
}
