package com.zksr.product.mq;

import com.alibaba.fastjson.JSON;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.cache.handler.IProductEventHandler;
import com.zksr.product.service.IProductEventService;
import com.zksr.product.service.impl.StockService;
import com.zksr.trade.api.order.dto.TestOrderDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import java.util.Objects;
import java.util.function.Consumer;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品上架发布事件
 * @date 2024/3/2 9:57
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class ProductEventMqChannel {

    @Autowired
    private StreamBridge streamBridge;

    @Autowired
    private StockService stockService;

    @Autowired
    private IProductEventService productEventService;

    public void sendEvent(EsProductEvent<?> event){
        log.info("com.zksr.product.mq.ProductEventProducer.sendEvent发送消息：" + JSON.toJSONString(event));
        boolean flag = streamBridge.send(
                MessageConstant.PRODUCT_EVENT_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(event)
                        //.setHeader("for", "这是一个请求头～")
                        .build());
        // @com.zksr.product.mq.ProductEventProducer.productEvent 事件处理
        log.info("com.zksr.product.mq.ProductEventProducer.sendEvent：mgs status :{}", flag);
    }

    @Bean
    public Consumer<Message<EsProductEvent<?>>> productEvent() {
        return (data) -> {
            EsProductEvent<?> event = data.getPayload();
            log.info("com.zksr.product.mq.ProductEventProducer.productEvent收到消息 : {}", JSON.toJSONString(event));
            if (StringUtils.isEmpty(event.getType())) {
                log.error("事件类型不存在");
                return;
            }
            productEventService.executeEvent(event);
        };
    }

    /**
     * 测试事务消息接收
     * @return
     */
    @Bean
    public Consumer<Message<TestOrderDto>> testTransaction() {
        return (data) -> {
            TestOrderDto order = data.getPayload();
            log.info("测试事务消息 收到消息 : {}", JSON.toJSONString(order));
            stockService.deduct(order.getCommodityCode(), order.getCount());
        };
    }
}
