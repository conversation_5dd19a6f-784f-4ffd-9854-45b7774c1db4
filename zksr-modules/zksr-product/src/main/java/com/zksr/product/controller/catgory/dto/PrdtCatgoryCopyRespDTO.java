package com.zksr.product.controller.catgory.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.product.domain.PrdtCatgory;
import lombok.Data;

@Data
public class PrdtCatgoryCopyRespDTO extends PrdtCatgory {

    /** 父类分类名 */
    @Excel(name = "父类分类名(二级)")
    private String pcatgoryName;

    /** 父类级别 */
    @Excel(name = "父类级别(二级)")
    private Integer plevel;

    @Excel(name = "父类分类名(一级)")
    private String pcatgoryName2;

    /** 父类级别 */
    @Excel(name = "父类级别(一级)")
    private Integer plevel2;

    @JsonIgnore
    public String getCatgoryKey() {
        return StringUtils.format("{}_{}_{}_{}", getCatgoryName(), getLevel(), ToolUtil.isEmptyReturn(pcatgoryName,"2"), ToolUtil.isEmptyReturn(pcatgoryName2,"1"));
    }

    @JsonIgnore
    public String getPcatgoryKey() {
        return StringUtils.format("{}_{}_{}_{}", ToolUtil.isEmptyReturn(pcatgoryName,"2"), plevel, ToolUtil.isEmptyReturn(pcatgoryName2,"2"), "1");
    }
}
