package com.zksr.product.controller.catgoryRate.vo;

import java.math.BigDecimal;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 城市级管理分类扣点设置对象 prdt_catgory_rate
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@ApiModel("城市级管理分类扣点设置 - prdt_catgory_rate分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtCatgoryRatePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 城市级管理分类扣点设置id */
    @ApiModelProperty(value = "业务员分润比例")
    private Long catgoryRateId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /** 平台商二级管理分类id */
    @Excel(name = "平台商二级管理分类id")
    @ApiModelProperty(value = "平台商二级管理分类id")
    private Long catgoryId;

    /** 运营商分润比例 */
    @Excel(name = "运营商分润比例")
    @ApiModelProperty(value = "运营商分润比例")
    private BigDecimal dcRate;

    /** 业务员负责人分润比例 */
    @Excel(name = "业务员负责人分润比例")
    @ApiModelProperty(value = "业务员负责人分润比例")
    private BigDecimal colonel1Rate;

    /** 业务员分润比例 */
    @Excel(name = "业务员分润比例")
    @ApiModelProperty(value = "业务员分润比例")
    private BigDecimal colonel2Rate;


}
