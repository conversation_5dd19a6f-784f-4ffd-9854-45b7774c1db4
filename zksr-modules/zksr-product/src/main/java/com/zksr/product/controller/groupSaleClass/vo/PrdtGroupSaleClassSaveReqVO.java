package com.zksr.product.controller.groupSaleClass.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 平台商城市分组-展示分类关联对象 prdt_group_sale_class
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Data
@ApiModel("平台商城市分组-展示分类关联 - prdt_group_sale_class分页 Request VO")
public class PrdtGroupSaleClassSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id", required = true)
    private Long groupId;

    /** 平台商展示分类id;平台商展示分类id */
    @Excel(name = "平台商展示分类id;平台商展示分类id")
    @ApiModelProperty(value = "平台商展示分类id;平台商展示分类id", required = true)
    private Long saleClassId;

}
