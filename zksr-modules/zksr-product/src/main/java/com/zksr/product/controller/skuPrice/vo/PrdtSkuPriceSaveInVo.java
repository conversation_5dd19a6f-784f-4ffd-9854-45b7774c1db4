package com.zksr.product.controller.skuPrice.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
*
 *
* <AUTHOR>
* @date 2024/3/23 10:56
*/
@Data
@ApiModel("sku销售价 - prdt_sku_price新增VO")
public class PrdtSkuPriceSaveInVo {

    /** 入驻商id;入驻商id */
    @Excel(name = "入驻商id;入驻商id")
    @ApiModelProperty(value = "入驻商id;入驻商id", example = "示例值")
    private Long supplierId;

    /** 平台商管理分类id */
    @Excel(name = "平台商管理分类id")
    @ApiModelProperty(value = "平台商管理分类id")
    private Long catgoryId;

    /** 平台商品牌id */
    @Excel(name = "平台商品牌id")
    @ApiModelProperty(value = "平台商品牌id")
    private Long brandId;

    /** 搜索关键字 */
    @Excel(name = "搜索关键字")
    @ApiModelProperty(value = "搜索关键字", example = "示例值")
    private String keyword;

    /** 价格选项 */
    @Excel(name = "价格选项")
    @ApiModelProperty(value = "价格选项 数据字典sku_price_option： salePrice1、salePrice2、salePrice3、salePrice4、salePrice5、salePrice6", example = "示例值")
    private String priceOption;

    /** 价格类型 */
    @Excel(name = "价格类型")
    @ApiModelProperty(value = "价格类型 数据字典sku_price_type： costPrice、markPrice", example = "示例值")
    private String priceType;

    /** 单位类型 */
    @Excel(name = "单位类型")
    @ApiModelProperty(value = "单位类型 数据字典sku_price_unit_type： minUnit、midUnit、largeUnit", example = "示例值")
    private String unitType;

    /** 调价比率 */
    @Excel(name = "调价比率")
    @ApiModelProperty(value = "调价比率")
    private BigDecimal priceRatio;

    /** 新增数据列表 */
    @Excel(name = "新增数据列表")
    @ApiModelProperty(value = "新增数据列表", example = "示例值")
    private List<PrdtSkuPriceSaveReqVO> saveList;


    /** 类型(用来区分是全国商品，还是本地配送商品，数据字典);0-全国商品 1-本地配送商品 */
    @Excel(name = "类型(用来区分是全国商品，还是本地配送商品，数据字典);0-全国商品 1-本地配送商品")
    @ApiModelProperty(value = "类型(用来区分是全国商品，还是本地配送商品，数据字典);0-全国商品 1-本地配送商品")
    private Integer type;


    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;


    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;


    /** 定价标识*/
    @Excel(name = "定价标识")
    @ApiModelProperty(value = "定价标识 0 新增 1批量定价")
    private Integer saveFlag;

}
