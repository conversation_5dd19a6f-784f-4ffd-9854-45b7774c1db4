package com.zksr.product.api.areaClass;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaClass.form.AreaClassImportForm;
import com.zksr.product.api.spu.dto.SpuExportDTO;
import com.zksr.product.api.spu.vo.PrdtSpuPageReqVO;
import com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRespDTO;
import com.zksr.product.domain.PrdtAreaClass;
import com.zksr.product.service.IPrdtAreaClassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@ApiIgnore
public class AreaClassApiImpl implements AreaClassApi {

    @Autowired
    private IPrdtAreaClassService areaClassService;

    /**
     * @Description: 根据Id获取城市展示分类
     * @Param: Long areaClassId
     * @return: CommonResult<AreaClassDto>
     * @Author: liuxingyu
     * @Date: 2024/3/25 14:55
     */
    @Override
    @InnerAuth
    public CommonResult<AreaClassDTO> getAreaClassByAreaClassId(Long areaClassId) {
        return CommonResult.success(HutoolBeanUtils.toBean(areaClassService.getAreaClassByAreaClassId(areaClassId), AreaClassDTO.class));
    }

    /**
    * @Description: 获取门店绑定的城市展示分类(根据入驻商ID集合获取)
    * @Param: List<Long> supplierIds
    * @return: List<AreaClassDTO>
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:44
    */
    @Override
    @InnerAuth
    public CommonResult<List<AreaClassDTO>> getAreaClassBranchList(List<Long> supplierIds) {
        List<PrdtAreaClass> areaClassBranchList = areaClassService.getAreaClassBranchList(supplierIds);
        if (ObjectUtil.isEmpty(areaClassBranchList)){
            return CommonResult.success(null);
        }
        return CommonResult.success(HutoolBeanUtils.toBean(areaClassBranchList, AreaClassDTO.class));
    }

    /**
    * @Description: 根据门店区域和门店渠道获取城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/4/24 10:48
    */
    @Override
    public CommonResult<List<AreaClassDTO>> getAreaClassAreaChannelList(String key) {
        List<PrdtAreaClass> areaClassAreaChannelList = areaClassService.getAreaClassAreaChannelList(key);
        if (ObjectUtil.isEmpty(areaClassAreaChannelList)){
            return CommonResult.success(null);
        }
        return CommonResult.success(HutoolBeanUtils.toBean(areaClassAreaChannelList, AreaClassDTO.class));
    }

    @Override
    public CommonResult<List<PrdtAreaClassExportVo>> getPrdtAreaClassExportList(PrdtAreaClassExportVo pageVo) {
        return success(areaClassService.getPrdtAreaClassExportList(pageVo));
    }

    public CommonResult<String> importAreaClassDataEvent(AreaClassImportForm form){
        return success(JsonUtils.toJsonString(areaClassService.importAreaClassDataEvent(form.getList(), form.getSysCode(), form.getFileImportId(), form.getDcId(),form.getSeq())));
}
}
