package com.zksr.product.convert.platform;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtPlatformPropertyVal;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyValRespVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyValSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.product.domain.PrdtPropertyVal;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 规格值 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-06-21
*/
@Mapper
public interface PrdtPlatformPropertyValConvert {

    PrdtPlatformPropertyValConvert INSTANCE = Mappers.getMapper(PrdtPlatformPropertyValConvert.class);

    PrdtPlatformPropertyValRespVO convert(PrdtPlatformPropertyVal prdtPlatformPropertyVal);

    PrdtPlatformPropertyVal convert(PrdtPlatformPropertyValSaveReqVO prdtPlatformPropertyValSaveReq);

    PageResult<PrdtPlatformPropertyValRespVO> convertPage(PageResult<PrdtPlatformPropertyVal> prdtPlatformPropertyValPage);

    PrdtPlatformPropertyVal convertPartnerPropertyVal(PrdtPropertyVal propertyVal);
}