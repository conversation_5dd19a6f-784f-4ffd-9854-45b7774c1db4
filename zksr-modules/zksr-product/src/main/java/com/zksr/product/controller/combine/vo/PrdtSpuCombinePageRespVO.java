package com.zksr.product.controller.combine.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageParam;
import lombok.*;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 组合商品对象 prdt_spu_combine
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("组合商品 - prdt_spu_combine Response VO")
public class PrdtSpuCombinePageRespVO extends PageParam {
    private static final long serialVersionUID = 1L;

    /** 组合商品id */
    @ApiModelProperty(value = "组合商品id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuCombineId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 管理类别 */
    @Excel(name = "管理类别")
    @ApiModelProperty(value = "管理类别")
    private Long categoryId;

    /** 组合商品编号 */
    @Excel(name = "组合商品编号")
    @ApiModelProperty(value = "组合商品编号")
    private String spuCombineNo;

    /** 组合商品名 */
    @Excel(name = "组合商品名")
    @ApiModelProperty(value = "组合商品名")
    private String spuCombineName;

    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面图")
    private String thumb;

    /** 封面视频（url） */
    @Excel(name = "封面视频", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面视频")
    private String thumbVideo;

    /** 详情页轮播（json） */
    @Excel(name = "详情页轮播", readConverterExp = "j=son")
    @ApiModelProperty(value = "详情页轮播")
    private String images;

    /** 详情信息(富文本) */
    @Excel(name = "详情信息(富文本)")
    @ApiModelProperty(value = "详情信息(富文本)")
    private String details;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否")
    private Long isDelete;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Long status;

    /** 商品规格 */
    @Excel(name = "商品规格")
    @ApiModelProperty(value = "商品规格")
    private String specName;

    /** 总限量 */
    @Excel(name = "总限量")
    @ApiModelProperty(value = "总限量")
    private Long totalLimit;

    /** 起订 */
    @Excel(name = "起订")
    @ApiModelProperty(value = "起订")
    private Long minOq;

    /** 订货组数 */
    @Excel(name = "订货组数")
    @ApiModelProperty(value = "订货组数")
    private Long jumpOq;

    /** 限购 */
    @Excel(name = "限购")
    @ApiModelProperty(value = "限购")
    private Long maxOq;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "单位-数据字典")
    private Long unit;

    /** 标准价 */
    @Excel(name = "标准价")
    @ApiModelProperty(value = "标准价")
    private BigDecimal markPrice;

    /** 建议零售价 */
    @Excel(name = "建议零售价")
    @ApiModelProperty(value = "建议零售价")
    private BigDecimal suggestPrice;

    /** 销售价1 */
    @Excel(name = "销售价1")
    @ApiModelProperty(value = "销售价1")
    private BigDecimal salePrice1;

    /** 销售价2 */
    @Excel(name = "销售价2")
    @ApiModelProperty(value = "销售价2")
    private BigDecimal salePrice2;

    /** 销售价3 */
    @Excel(name = "销售价3")
    @ApiModelProperty(value = "销售价3")
    private BigDecimal salePrice3;

    /** 销售价4 */
    @Excel(name = "销售价4")
    @ApiModelProperty(value = "销售价4")
    private BigDecimal salePrice4;

    /** 销售价5 */
    @Excel(name = "销售价5")
    @ApiModelProperty(value = "销售价5")
    private BigDecimal salePrice5;

    /** 销售价6 */
    @Excel(name = "销售价6")
    @ApiModelProperty(value = "销售价6")
    private BigDecimal salePrice6;

    /** 活动开始时间, 活动商品 */
    @Excel(name = "活动开始时间, 活动商品")
    @ApiModelProperty(value = "活动开始时间, 活动商品")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityStartTime;

    /** 活动结束时间, 活动商品 */
    @Excel(name = "活动结束时间, 活动商品")
    @ApiModelProperty(value = "活动结束时间, 活动商品")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityEndTime;

    /** 限购类型 */
    @Excel(name = "限购类型")
    @ApiModelProperty(value = "限购类型")
    private Integer timesRule;

    /** 活动ID */
    @Excel(name = "活动ID")
    @ApiModelProperty(value = "活动ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long activityId;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    /** 活动状态  0-未启用 1-启用 2-停用 3-已失效*/
    @Excel(name = "活动状态 0-未启用 1-启用 2-停用 3-已失效")
    @ApiModelProperty(value = "活动状态 0-未启用 1-启用 2-停用 3-已失效")
    private Integer activityStatus;

    @ApiModelProperty(value = "上架发布ID")
    private Long itemId;
}
