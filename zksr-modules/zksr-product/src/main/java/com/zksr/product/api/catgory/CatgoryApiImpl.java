package com.zksr.product.api.catgory;

import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.catgory.dto.CatgoryIdDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.api.catgory.excel.PrdtCatgoryExcel;
import com.zksr.product.api.catgory.form.CategoryImportForm;
import com.zksr.product.api.catgory.vo.PrdtCatgoryPageVO;
import com.zksr.product.controller.catgory.vo.PrdtCatgoryPageReqVO;
import com.zksr.product.controller.catgory.vo.PrdtCatgoryRespVO;
import com.zksr.product.controller.catgory.vo.PrdtCatgorySaveReqVO;
import com.zksr.product.service.IPrdtCatgoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@RestController
@ApiIgnore
public class CatgoryApiImpl implements CatgoryApi {

    @Autowired
    private IPrdtCatgoryService prdtCatgoryService;

    /**
     * @Description: 根据管理分类ID获取管理分类信息
     * @Param: Long catgoryId
     * @return: CommonResult<CatgoryDto>
     * @Author: liuxingyu
     * @Date: 2024/3/25 10:46
     */
    @Override
    @InnerAuth
    public CommonResult<CatgoryDTO> getCatgoryByCatgoryId(Long catgoryId) {
        return CommonResult.success(HutoolBeanUtils.toBean(prdtCatgoryService.getCatgoryByCatgoryId(catgoryId), CatgoryDTO.class));
    }

    /**
     * 获取三级管理类目分润比例
     * @param catgoryId 管理分类ID
     * @param areaId    区域城市ID
     * @return
     */
    @Override
    public CommonResult<CatgoryRateDTO> getCatgoryByIdAndAreaId(Long catgoryId, Long areaId) {
        return CommonResult.success(prdtCatgoryService.getThreeCatgoryByIdAndAreaId(catgoryId, areaId));
    }

    /**
    * @Description: 根据平台code获取平台管理分类
    * @Author: liuxingyu
    * @Date: 2024/5/7 11:12
    */
    @Override
    @InnerAuth
    public CommonResult<List<CatgoryDTO>> getListBySysCode(Long sysCode) {
        return CommonResult.success(HutoolBeanUtils.toBean(prdtCatgoryService.getListBySysCode(sysCode), CatgoryDTO.class));
    }

    /**
     * 新增平台商管理分类
     *
     * @param catgoryDTO
     */
    @Override
    public CommonResult<Long> add(CatgoryDTO catgoryDTO) {
        return CommonResult.success(prdtCatgoryService.insertPrdtCatgory(HutoolBeanUtils.toBean(catgoryDTO, PrdtCatgorySaveReqVO.class)));
    }

    /**
     * 修改平台商管理分类
     *
     * @param catgoryDTO
     */
    @Override
    public CommonResult<Boolean> edit(CatgoryDTO catgoryDTO) {
        prdtCatgoryService.updatePrdtCatgory(HutoolBeanUtils.toBean(catgoryDTO, PrdtCatgorySaveReqVO.class));
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 删除平台商管理分类
     *
     * @param classIds
     */
    @Override
    public CommonResult<Boolean> remove(Long[] classIds) {
        prdtCatgoryService.deletePrdtCatgoryByClassIds(classIds);
        return CommonResult.success(Boolean.TRUE);
    }

    /**
    * @Description: 获取平台管理类别一级Id
    * @Author: liuxingyu
    * @Date: 2024/5/16 17:01
    */
    @Override
    @InnerAuth
    public CommonResult<List<CatgoryIdDTO>> getCatgoryFirstId() {
        return CommonResult.success(prdtCatgoryService.getCatgoryFirstId());
    }

    /**
     * 获取平台商管理分类列表
     *
     * @return
     */
    @Override
    public CommonResult<List<CatgoryDTO>> getCatgoryList(Long catgoryId, Long supplierId) {
        List<PrdtCatgoryRespVO> catgoryList = prdtCatgoryService.getCatgoryList(catgoryId, supplierId, null);
        List<CatgoryDTO> catgoryDTOList = HutoolBeanUtils.toBean(catgoryList, CatgoryDTO.class);
        return CommonResult.success(catgoryDTOList);
    }

    /**
     *   根据入驻商ID获取绑定的管理类别
     * @param supplierId 入驻商ID
     * @return CatgoryDTO
     */
    @Override
    @InnerAuth
    public CommonResult<List<CatgoryDTO>> getCatgoryListBySupplierId(Long supplierId) {
        return CommonResult.success(HutoolBeanUtils.toBean(prdtCatgoryService.getCatgoryBySupplierId(supplierId), CatgoryDTO.class));
    }

    public CommonResult<String> importBaseCategoryEvent(CategoryImportForm categoryImportForm){
        return CommonResult.success(JsonUtils.toJsonString(prdtCatgoryService.importBaseCategoryEvent(categoryImportForm.getList(),categoryImportForm.getSysCode(),categoryImportForm.getFileImportId(), categoryImportForm.getSeq())));
    }

    public CommonResult<List<PrdtCatgoryExcel>> getPrdtCatgoryExcel(PrdtCatgoryPageVO pageVO){
        return CommonResult.success(HutoolBeanUtils.toBean(prdtCatgoryService.getPrdtCatgoryPage(HutoolBeanUtils.toBean(pageVO,PrdtCatgoryPageReqVO.class)).getList(),PrdtCatgoryExcel.class));
    }

}
