package com.zksr.product.service.impl;

import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.api.model.event.EsProductEventBuild;
import com.zksr.product.cache.handler.IProductEventHandler;
import com.zksr.product.mq.ProductEventMqChannel;
import com.zksr.product.service.IProductEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品刷新事件service
 * @date 2024/3/1 14:01
 */
@Service
@SuppressWarnings("all")
public class ProductEventServiceImpl implements IProductEventService {

    @Override
    public void acceptEvent(EsProductEvent<?> event) {
        SpringUtils.getBean(ProductEventMqChannel.class).sendEvent(event);
    }

    @Override
    public void executeEvent(EsProductEvent<?> event) {
        IProductEventHandler eventHandler = SpringUtils.getBean(event.getType());
        if (Objects.nonNull(eventHandler)) {
            eventHandler.processData(event);
        }
    }

    @Autowired
    private EsProductService esProductService;

    @Override
    public void createIndex() {
        esProductService.initIndex();
    }

    @Override
    public void testDemo1(List<Long> list) {
        acceptEvent(EsProductEventBuild.localReleaseEvent(list));
    }

    @Override
    public void testDemo2(List<Long> list) {
        acceptEvent(EsProductEventBuild.globalReleaseEvent(list));
    }

    @Override
    public void testDemo3(Long spuId) {
        acceptEvent(EsProductEventBuild.spuEvent(spuId));
    }

    @Override
    public void testDemo4(Long skuId) {
        acceptEvent(EsProductEventBuild.skuEvent(skuId));
    }

}
