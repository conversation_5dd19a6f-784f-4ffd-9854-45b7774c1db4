package com.zksr.product.controller.yhdata.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 门店批量要货对象 prdt_branch_yhdata
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@ApiModel("门店批量要货 - prdt_branch_yhdata分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtBranchYhdataPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "匹配失败原因")
    private Long yhId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 要货批次号 */
    @Excel(name = "要货批次号")
    @ApiModelProperty(value = "要货批次号")
    private String posYhBatchNo;

    /** 区域城市id */
    @Excel(name = "区域城市id")
    @ApiModelProperty(value = "区域城市id")
    private Long areaId;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 商品名 */
    @Excel(name = "商品名")
    @ApiModelProperty(value = "商品名")
    private String posSkuName;

    /** 商品名 */
    @Excel(name = "spuName")
    @ApiModelProperty(value = "spuName")
    private String spuName;

    /** 商品编码 */
    @Excel(name = "商品编码")
    @ApiModelProperty(value = "商品编码")
    private String posSourceNo;

    /** 国条码 */
    @Excel(name = "国条码")
    @ApiModelProperty(value = "国条码")
    private String posBarcode;

    /** 匹配状态（数据字典）0-未匹配, 1-匹配成功, 2-匹配失败 */
    @Excel(name = "匹配状态", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "匹配状态")
    private Integer matchState;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "补货申请开始时间")
    private Date startTime;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "补货申请结束时间")
    private Date endTime;


}
