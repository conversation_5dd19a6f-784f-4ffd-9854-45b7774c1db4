package com.zksr.product.api.productContent;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.content.ProductContentApi;
import com.zksr.product.api.content.dto.ProductContentDTO;
import com.zksr.product.api.content.dto.ProductContentPageReqDTO;
import com.zksr.product.api.model.event.EsProductEventBuild;
import com.zksr.product.controller.product.vo.ProductContentPageReqVO;
import com.zksr.product.controller.product.vo.ProductContentRespVO;
import com.zksr.product.convert.product.ProductContentConvert;
import com.zksr.product.mq.ProductEventMqChannel;
import com.zksr.product.service.IProductContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashSet;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@ApiIgnore
@InnerAuth
public class ProductContentApiImpl implements ProductContentApi {

    @Autowired
    private IProductContentService productContentService;

    @Autowired
    private ProductEventMqChannel productEventProducer;

    @Autowired
    private RedisService redisService;

    @Override
    public CommonResult<List<ProductContentDTO>> getElasticSearchListByApi(@RequestBody ProductContentPageReqDTO pageReqVo) {
        List<ProductContentRespVO> elasticSearchList = productContentService.getElasticSearchList(HutoolBeanUtils.toBean(pageReqVo, ProductContentPageReqVO.class));
        return success(HutoolBeanUtils.toBean(elasticSearchList,ProductContentDTO.class));
    }

    @Override
    public CommonResult<List<ProductContentDTO>> getElasticSearchAreaItemList(@RequestBody ProductContentPageReqDTO pageReqVo) {
        List<ProductContentRespVO> elasticSearchList = productContentService.getElasticSearchAreaItemList(HutoolBeanUtils.toBean(pageReqVo, ProductContentPageReqVO.class));
        return success(HutoolBeanUtils.toBean(elasticSearchList,ProductContentDTO.class));
    }

    @Override
    public CommonResult<List<ProductContentDTO>> getElasticSearchFullItemList(ProductContentPageReqDTO pageReqVo) {
        List<ProductContentRespVO> elasticSearchList = productContentService.getElasticSearchFullItemList(HutoolBeanUtils.toBean(pageReqVo, ProductContentPageReqVO.class));
        return success(HutoolBeanUtils.toBean(elasticSearchList,ProductContentDTO.class));
    }

    @Override
    public CommonResult<PageResult<ProductContentDTO>> getPageElasticSearchListByApi(ProductContentPageReqDTO pageReqVo) {
        PageResult<ProductContentRespVO> pageResult = productContentService.getPageElasticSearchList(HutoolBeanUtils.toBean(pageReqVo, ProductContentPageReqVO.class));
        return success(ProductContentConvert.INSTANCE.convertContentDTOPage(pageResult));
    }

    /**
     * 搜索ES中的商品信息
     *
     * @param pageReqVo
     * @return
     */
    @Override
    public CommonResult<PageResult<ProductContentDTO>> getElasticSearchItemList(ProductContentPageReqDTO pageReqVo) {
        PageResult<ProductContentRespVO> pageList = productContentService.getPageElasticSearchList(HutoolBeanUtils.toBean(pageReqVo, ProductContentPageReqVO.class));
        return success(
                new PageResult(HutoolBeanUtils.toBean(pageList.getList(),ProductContentDTO.class), pageList.getTotal())
        );

    }

    @Override
    public CommonResult<Boolean> sendSkuEvent(List<Long> skuIdList) {
        if (skuIdList.isEmpty()) {
            return success(Boolean.TRUE);
        }
        productEventProducer.sendEvent(EsProductEventBuild.skuEvent(skuIdList));
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> sendSpuEvent(List<Long> spuIdList) {
        if (spuIdList.isEmpty()) {
            return success(Boolean.TRUE);
        }
        productEventProducer.sendEvent(EsProductEventBuild.spuEvent(spuIdList));
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> sendSpuCombineEvent(List<Long> spuCombineList) {
        productEventProducer.sendEvent(EsProductEventBuild.spuCombineEvent(spuCombineList));
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> updateAreaReleaseData(Long areaId) {
        // 城市上架的三级展示分类数据
        {
            List<Long> classIdList = productContentService.getAreaItemReleaseThreeAreaClassList(areaId);
            redisService.saveOrUpdateSet(RedisConstants.getReleaseCityClassSet(areaId), new HashSet<>(classIdList));
        }

        // 城市上架的三级管理分类数据
        {
            List<Long> categoryIdList = productContentService.getAreaItemReleaseThreeCategoryList(areaId);
            redisService.saveOrUpdateSet(RedisConstants.getReleaseCityCategorySet(areaId), new HashSet<>(categoryIdList));
        }
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> updateGlobalReleaseData(Long sysCode) {
        // 全国上架的三级展示分类数据
        {
            List<Long> classIdList = productContentService.getGloablItemReleaseThreeClassList(sysCode);
            redisService.saveOrUpdateSet(RedisConstants.getGlobalReleaseClassSet(sysCode), new HashSet<>(classIdList));
        }
        // 全国上架的三级管理分类数据
        {
            List<Long> categoryIdList = productContentService.getGloablItemReleaseThreeCategoryList(sysCode);
            redisService.saveOrUpdateSet(RedisConstants.getGlobalReleaseCategorySet(sysCode), new HashSet<>(categoryIdList));
        }
        return success(Boolean.TRUE);
    }
}
