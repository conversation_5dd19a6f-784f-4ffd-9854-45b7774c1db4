package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 商品调价单主对象 prdt_adjust_prices
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@TableName(value = "prdt_adjust_prices")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtAdjustPrices extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 单据ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long adjustPricesId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 运营商ID */
    @Excel(name = "运营商ID")
    private Long dcId;

    /** 入驻商ID */
    @Excel(name = "入驻商ID")
    private Long supplierId;

    /** 单据编号 */
    @Excel(name = "单据编号")
    private String adjustPricesNo;

    /** 审核状态：0-待审核,1-已审核,2-已作废 */
    @Excel(name = "审核状态：0-待审核,1-已审核,2-已作废")
    private Long approveState;

    /** 审核人Id */
    @Excel(name = "审核人Id")
    private Long approveUserId;

    /** 审核人 */
    @Excel(name = "审核人")
    private String approveBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date approveTime;

    /** 生效类型：0-定时生效，1-立即生效 */
    @Excel(name = "生效类型：0-定时生效，1-立即生效")
    private Long validType;

    /** 生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validTime;

    /** SKU数 */
    @Excel(name = "SKU数")
    private Long skuNum;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 价格类型选项：标准价：markPrice, 成本价：costPrice，已，号分隔 */
    @Excel(name = "价格类型选项：标准价：markPrice, 成本价：costPrice，已，号分隔")
    private String priceTypeStr;

    @ApiModelProperty(value = "定时调价执行状态 0-未执行，1-已执行")
    private Integer taskExecuteStatus;

}
