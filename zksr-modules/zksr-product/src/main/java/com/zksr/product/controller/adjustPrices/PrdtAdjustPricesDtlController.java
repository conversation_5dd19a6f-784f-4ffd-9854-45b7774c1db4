package com.zksr.product.controller.adjustPrices;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtAdjustPricesDtl;
import com.zksr.product.service.IPrdtAdjustPricesDtlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesDtlPageReqVO;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesDtlSaveReqVO;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesDtlRespVO;
import com.zksr.product.convert.adjustPrices.PrdtAdjustPricesDtlConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 商品调价单明细Controller
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Api(tags = "管理后台 - 商品调价单明细接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/dtl")
public class PrdtAdjustPricesDtlController {
    @Autowired
    private IPrdtAdjustPricesDtlService prdtAdjustPricesDtlService;

//    /**
//     * 新增商品调价单明细
//     */
//    @ApiOperation(value = "新增商品调价单明细", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
//    @RequiresPermissions(Permissions.ADD)
//    @Log(title = "商品调价单明细", businessType = BusinessType.INSERT)
//    @PostMapping
//    public CommonResult<Long> add(@Valid @RequestBody PrdtAdjustPricesDtlSaveReqVO createReqVO) {
//        return success(prdtAdjustPricesDtlService.insertPrdtAdjustPricesDtl(createReqVO));
//    }
//
//    /**
//     * 修改商品调价单明细
//     */
//    @ApiOperation(value = "修改商品调价单明细", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
//    @RequiresPermissions(Permissions.EDIT)
//    @Log(title = "商品调价单明细", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtAdjustPricesDtlSaveReqVO updateReqVO) {
//            prdtAdjustPricesDtlService.updatePrdtAdjustPricesDtl(updateReqVO);
//        return success(true);
//    }
//
//    /**
//     * 删除商品调价单明细
//     */
//    @ApiOperation(value = "删除商品调价单明细", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
//    @RequiresPermissions(Permissions.DELETE)
//    @Log(title = "商品调价单明细", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{adjustPricesDtlIds}")
//    public CommonResult<Boolean> remove(@PathVariable Long[] adjustPricesDtlIds) {
//        prdtAdjustPricesDtlService.deletePrdtAdjustPricesDtlByAdjustPricesDtlIds(adjustPricesDtlIds);
//        return success(true);
//    }
//
//    /**
//     * 获取商品调价单明细详细信息
//     */
//    @ApiOperation(value = "获得商品调价单明细详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
//    @RequiresPermissions(Permissions.GET)
//    @GetMapping(value = "/{adjustPricesDtlId}")
//    public CommonResult<PrdtAdjustPricesDtlRespVO> getInfo(@PathVariable("adjustPricesDtlId") Long adjustPricesDtlId) {
//        PrdtAdjustPricesDtl prdtAdjustPricesDtl = prdtAdjustPricesDtlService.getPrdtAdjustPricesDtl(adjustPricesDtlId);
//        return success(PrdtAdjustPricesDtlConvert.INSTANCE.convert(prdtAdjustPricesDtl));
//    }
//
//    /**
//     * 分页查询商品调价单明细
//     */
//    @GetMapping("/list")
//    @ApiOperation(value = "获得商品调价单明细分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
//    @RequiresPermissions(Permissions.LIST)
//    public CommonResult<PageResult<PrdtAdjustPricesDtlRespVO>> getPage(@Valid PrdtAdjustPricesDtlPageReqVO pageReqVO) {
//        PageResult<PrdtAdjustPricesDtl> pageResult = prdtAdjustPricesDtlService.getPrdtAdjustPricesDtlPage(pageReqVO);
//        return success(PrdtAdjustPricesDtlConvert.INSTANCE.convertPage(pageResult));
//    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:dtl:add";
        /** 编辑 */
        public static final String EDIT = "product:dtl:edit";
        /** 删除 */
        public static final String DELETE = "product:dtl:remove";
        /** 列表 */
        public static final String LIST = "product:dtl:list";
        /** 查询 */
        public static final String GET = "product:dtl:query";
        /** 停用 */
        public static final String DISABLE = "product:dtl:disable";
        /** 启用 */
        public static final String ENABLE = "product:dtl:enable";
    }
}
