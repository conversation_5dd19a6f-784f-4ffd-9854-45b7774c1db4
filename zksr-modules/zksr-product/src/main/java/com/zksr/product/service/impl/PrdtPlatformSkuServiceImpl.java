package com.zksr.product.service.impl;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.controller.platform.vo.PrdtPlatformSkuPageReqVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSkuSaveReqVO;
import com.zksr.product.convert.platform.PrdtPlatformSkuConvert;
import com.zksr.product.domain.PrdtPlatformSku;
import com.zksr.product.mapper.PrdtPlatformSkuMapper;
import com.zksr.product.service.IPrdtPlatformSkuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 平台商品库-商品SKUService业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Service
public class PrdtPlatformSkuServiceImpl implements IPrdtPlatformSkuService {
    @Autowired
    private PrdtPlatformSkuMapper prdtPlatformSkuMapper;

    /**
     * 新增平台商品库-商品SKU
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtPlatformSku(PrdtPlatformSkuSaveReqVO createReqVO) {
        // 插入
        PrdtPlatformSku prdtPlatformSku = PrdtPlatformSkuConvert.INSTANCE.convert(createReqVO);
        prdtPlatformSkuMapper.insert(prdtPlatformSku);
        // 返回
        return prdtPlatformSku.getPlatformSkuId();
    }

    /**
     * 修改平台商品库-商品SKU
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtPlatformSku(PrdtPlatformSkuSaveReqVO updateReqVO) {
        prdtPlatformSkuMapper.updateById(PrdtPlatformSkuConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除平台商品库-商品SKU
     *
     * @param platformSkuId 商品库sku id
     */
    @Override
    public void deletePrdtPlatformSku(Long platformSkuId) {
        // 删除
        prdtPlatformSkuMapper.deleteById(platformSkuId);
    }

    /**
     * 批量删除平台商品库-商品SKU
     *
     * @param platformSkuIds 需要删除的平台商品库-商品SKU主键
     * @return 结果
     */
    @Override
    public void deletePrdtPlatformSkuByPlatformSkuIds(Long[] platformSkuIds) {
        for(Long platformSkuId : platformSkuIds){
            this.deletePrdtPlatformSku(platformSkuId);
        }
    }

    /**
     * 获得平台商品库-商品SKU
     *
     * @param platformSkuId 商品库sku id
     * @return 平台商品库-商品SKU
     */
    @Override
    public PrdtPlatformSku getPrdtPlatformSku(Long platformSkuId) {
        return prdtPlatformSkuMapper.selectById(platformSkuId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtPlatformSku> getPrdtPlatformSkuPage(PrdtPlatformSkuPageReqVO pageReqVO) {
        return prdtPlatformSkuMapper.selectPage(pageReqVO);
    }

}
