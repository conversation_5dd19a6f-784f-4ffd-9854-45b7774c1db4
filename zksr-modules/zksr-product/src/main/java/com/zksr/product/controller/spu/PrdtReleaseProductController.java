package com.zksr.product.controller.spu;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.controller.areaItem.vo.PrdtAreaItemPageReqVO;
import com.zksr.product.controller.spu.vo.PrdtReleaseProductReqVO;
import com.zksr.product.controller.spu.vo.PrdtReleaseProductRespVO;
import com.zksr.product.service.IPrdtReleaseProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/4 8:27
 */
@Api(tags = "管理后台 - 上架商品接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/releaseProduct")
public class PrdtReleaseProductController {

    @Autowired
    private IPrdtReleaseProductService releaseProductService;

    /**
     * 分页查询城市上架商品
     */
    @PostMapping("/list")
    @ApiOperation(value = "获得城市上架商品分页列表", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtReleaseProductRespVO>> getPage(@RequestBody PrdtReleaseProductReqVO reqVO) {
        return success(releaseProductService.getProductList(reqVO));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 列表 */
        public static final String LIST = "product:releaseProduct:list";
    }
}
