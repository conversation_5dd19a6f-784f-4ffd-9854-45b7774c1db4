package com.zksr.product.domain.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用于平台展示类别信息导入
 * @date 2024/11/08 09:18
 */
@Data
@ApiModel(description = "用于平台展示类别信息导入")
public class ProductSaleClassImportExcel {
    @Excel(name = "一级类别名称", headerColor = IndexedColors.RED)
    private String primaryCategoryName;

    @Excel(name = "二级类别名称", headerColor = IndexedColors.RED)
    private String secondaryCategoryName;

    @Excel(name = "三级类别名称", headerColor = IndexedColors.RED)
    private String tertiaryCategoryName;
}
