package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.product.enums.YhMatchReason;
import com.zksr.product.enums.YhMatchState;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 门店批量要货对象 prdt_branch_yhdata
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@TableName(value = "prdt_branch_yhdata")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtBranchYhdata extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long yhId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 要货批次号 */
    @Excel(name = "要货批次号")
    private String posYhBatchNo;

    /** 区域城市id */
    @Excel(name = "区域城市id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 门店id */
    @Excel(name = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 商品名 */
    @Excel(name = "商品名")
    private String posSkuName;

    /** 商品编码 */
    @Excel(name = "商品编码")
    private String posSourceNo;

    /** 国条码 */
    @Excel(name = "国条码")
    private String posBarcode;

    /** 昨日销量 */
    @Excel(name = "昨日销量")
    private Integer posSalesQty;

    /** 实际库存数量 */
    @Excel(name = "实际库存数量")
    private Integer posStockQty;

    /** 门店库存数量 */
    @Excel(name = "门店库存数量")
    private Integer posBranchStockQty;

    /** 建议购买数量 */
    @Excel(name = "建议购买数量")
    private Integer posSuggestQty;

    /** 原始要货数量 */
    private Integer sourceYhQty;

    /** 单位名称 */
    @Excel(name = "单位名称")
    private String posUnitName;

    /** 最迟订货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最迟订货时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date posMaxLateTime;

    /** 30天人均销量 */
    @ApiModelProperty("30天人均销量")
    private Integer pos30dayAvgSales;

    /** 安全库存天数 */
    @ApiModelProperty("安全库存天数")
    private Integer posSafetyDays;

    /** 安全库存 */
    @ApiModelProperty("安全库存")
    private Integer posSafetyStock;

    /** 区域城市上架商品id */
    @Excel(name = "区域城市上架商品id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long mallAreaItemId;

    /** 匹配的sku id */
    @Excel(name = "匹配的sku id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long mallMatchSkuId;

    /** 单位大小 */
    @Excel(name = "单位大小")
    private Integer mallUnitType;

    /** 上次补货时间 */
    @Excel(name = "上次补货时间")
    private Date lastTime;

    /** 上次补货数量 */
    @Excel(name = "上次补货数量")
    private Integer lastSubmitQty;

    /** 已下单, 未完成数量, 在途数量 */
    @Excel(name = "已下单, 未完成数量, 在途数量")
    private Long transitQty;

    /** 是否选中, 1-选中, 2-未选中 */
    @Excel(name = "是否选中, 1-选中, 2-未选中")
    private Integer checked;

    /** 匹配状态（数据字典）0-未匹配, 1-匹配成功, 2-匹配失败 */
    @Excel(name = "匹配状态", readConverterExp = "数=据字典")
    private Integer matchState;

    /** 匹配失败原因（数据字典）0-库存不足, 1-已下架, 2-未匹配到商品 */
    @Excel(name = "匹配失败原因", readConverterExp = "数=据字典")
    private Integer failReason;

    /** 行号 */
    @Excel(name = "行号")
    private Integer lineNum;

    /** 要货批次年月日yyyyMMdd */
    @Excel(name = "要货批次年月日yyyyMMdd")
    private Integer batchYmd;

    /** 0-正常,1-已删除 */
    @Excel(name = "0-正常,1-已删除")
    private Integer delFlag;

    /** 0-普通商品,1-组合商品 */
    @Excel(name = "0-普通商品,1-组合商品")
    private Integer itemType;

    public void failReason(YhMatchReason yhMatchReason) {
        this.failReason = yhMatchReason.getState();
    }

    public void matchState(YhMatchState yhMatchState) {
        this.matchState = yhMatchState.getState();
    }

    @JsonFormat
    public boolean isSpuCombine() {
        return Objects.nonNull(itemType) && itemType == NumberPool.INT_ONE;
    }

    @JsonFormat
    public boolean notSpuCombine() {
        return Objects.nonNull(itemType) && itemType == NumberPool.INT_ZERO;
    }

    public String getUnitKey() {
        return StringUtils.format("{}:{}", this.getMallAreaItemId(), this.getMallUnitType());
    }
}
