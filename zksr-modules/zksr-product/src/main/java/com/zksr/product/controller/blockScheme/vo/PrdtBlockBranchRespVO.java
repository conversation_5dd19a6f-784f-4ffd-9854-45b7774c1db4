package com.zksr.product.controller.blockScheme.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 经营屏蔽客户对象 prdt_block_branch
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Data
@ApiModel("经营屏蔽客户 - prdt_block_branch Response VO")
public class PrdtBlockBranchRespVO {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long blockBranchId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 方案编码 */
    @Excel(name = "方案编码")
    @ApiModelProperty(value = "方案编码")
    private String schemeNo;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 收货地址 */
    @Excel(name = "收货地址")
    @ApiModelProperty(value = "收货地址")
    private String branchAddr;
}
