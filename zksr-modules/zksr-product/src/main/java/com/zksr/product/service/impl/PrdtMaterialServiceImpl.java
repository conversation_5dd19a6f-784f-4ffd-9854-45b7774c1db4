package com.zksr.product.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtMaterialMapper;
import com.zksr.product.convert.material.PrdtMaterialConvert;
import com.zksr.product.domain.PrdtMaterial;
import com.zksr.product.controller.material.vo.PrdtMaterialPageReqVO;
import com.zksr.product.controller.material.vo.PrdtMaterialSaveReqVO;
import com.zksr.product.service.IPrdtMaterialService;
import org.springframework.transaction.annotation.Transactional;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 素材Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class PrdtMaterialServiceImpl implements IPrdtMaterialService {
    @Autowired
    private PrdtMaterialMapper prdtMaterialMapper;

    /**
     * 新增素材
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertPrdtMaterial(PrdtMaterialSaveReqVO createReqVO) {
        // 插入
        PrdtMaterial prdtMaterial = PrdtMaterialConvert.INSTANCE.convert(createReqVO);
            prdtMaterialMapper.insert(prdtMaterial);
        // 返回
        return prdtMaterial.getMaterialId();
    }

    /**
     * 修改素材
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrdtMaterial(PrdtMaterialSaveReqVO updateReqVO) {
        prdtMaterialMapper.updateById(PrdtMaterialConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除素材
     *
     * @param materialId 素材id
     */
    @Override
    public void deletePrdtMaterial(Long materialId) {
        // 删除
        prdtMaterialMapper.deleteById(materialId);
    }

    /**
     * 批量删除素材
     *
     * @param materialIds 需要删除的素材主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrdtMaterialByMaterialIds(Long[] materialIds) {
        for(Long materialId : materialIds){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deletePrdtMaterial(materialId);
        }
    }

    /**
     * 获得素材
     *
     * @param materialId 素材id
     * @return 素材
     */
    @Override
    public PrdtMaterial getPrdtMaterial(Long materialId) {
        return prdtMaterialMapper.selectById(materialId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtMaterial> getPrdtMaterialPage(PrdtMaterialPageReqVO pageReqVO) {
        return prdtMaterialMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Long materialId, Integer status) {
        prdtMaterialMapper.updateStatus(ListUtil.toList(materialId), status);
    }

    private void validatePrdtMaterialExists(Long materialId) {
        if (prdtMaterialMapper.selectById(materialId) == null) {
            throw exception(PRDT_MATERIAL_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.product.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 素材 TODO 补充编号 ==========
    // ErrorCode PRDT_MATERIAL_NOT_EXISTS = new ErrorCode(TODO 补充编号, "素材不存在");


}
