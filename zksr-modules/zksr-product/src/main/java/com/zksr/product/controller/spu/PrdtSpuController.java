package com.zksr.product.controller.spu;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.model.event.EsProductEventBuild;
import com.zksr.product.api.sku.vo.PrdtUpdateSkuStockReqVO;
import com.zksr.product.api.spu.excel.ProductImportExcel;
import com.zksr.product.api.spu.vo.*;
import com.zksr.product.controller.spu.dto.PrdtSpuUpdateRespDTO;
import com.zksr.product.controller.spu.vo.*;
import com.zksr.product.controller.supplierClass.vo.SupplierClassRateStatusRespVO;
import com.zksr.product.controller.supplierItem.PrdtSupplierItemController;
import com.zksr.product.domain.PrdtAreaItem;
import com.zksr.product.domain.PrdtSku;
import com.zksr.product.domain.PrdtSupplierItem;
import com.zksr.product.domain.excel.ProductBrandImportUpdateExcel;
import com.zksr.product.domain.excel.ProductCatgoryImportUpdateExcel;
import com.zksr.product.domain.excel.ProductExport;
import com.zksr.product.service.*;
import com.zksr.trade.api.order.vo.DcOrderPageReqApiVO;
import com.zksr.trade.api.order.vo.SupplierOrderDtlInfoExportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.ClassUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 商品SPUController
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Api(tags = "管理后台 - 商品SPU接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/spu")
public class PrdtSpuController {

    @Autowired
    private IPrdtSpuService prdtSpuService;

    @Autowired
    private IPrdtSkuService prdtSkuService;

    @Autowired
    private IPrdtSupplierClassService prdtSupplierClassService;

    @Autowired
    private IProductEventService productEventService;

    @Autowired
    private IPrdtAreaItemService prdtAreaItemService;

    @Autowired
    private IPrdtSupplierItemService prdtSupplierItemService;


    /**
     * @Description: 获取产品下拉列表
     * @Param: String product
     * @return: List<PrdtProductRespVO>
     * @Author: liuxingyu
     * @Date: 2024/4/12 9:01
     */
    @ApiOperation(value = "获取产品下拉列表", httpMethod = HttpMethod.GET)
    @GetMapping("/getProductDropdown")
    public CommonResult<PageResult<PrdtProductRespVO>> getProductDropdown(@Valid PrdtProductPageReqVO pageReqVO) {
        return success(prdtSpuService.getProductDropdown(pageReqVO));
    }

    /**
     * 新增商品SPU
     * !@商品 - 新增
     */
    @ApiOperation(value = "新增商品SPU", httpMethod = HttpMethod.POST)
    @RequiresPermissions("product:spu:add")
    @Log(title = "商品SPU", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtSpuGroupSaveInVO createReqVO) {
        Long spuId = prdtSpuService.insertPrdtSpu(createReqVO);
        // 开启共享
        if (Objects.nonNull(createReqVO.getShareFlag()) && createReqVO.getShareFlag()) {
            prdtSpuService.shareEnable(
                    PrdtSpuShareReqVO.builder().spuIdList(ListUtil.toList(spuId)).build()
            );
        }
        return success(spuId);
    }

    /**
     * 修改商品SPU
     * !@商品 - 修改
     */
    @ApiOperation(value = "修改商品SPU", httpMethod = HttpMethod.PUT, notes = "权限字符:product:spu:edit")
    @RequiresPermissions("product:spu:edit")
    @Log(title = "商品SPU", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtSpuGroupSaveInVO updateReqVO) {
        prdtSpuService.updatePrdtSpuAfter(
                prdtSpuService.updatePrdtSpu(updateReqVO)
        );
        return success(true);
    }

    /**
     * 修改商品SPU基本信息
     */
    @ApiOperation(value = "修改商品SPU基本信息", httpMethod = HttpMethod.PUT, notes = "权限字符:product:spu:edit-base")
    @RequiresPermissions("product:spu:edit-base")
    @Log(title = "商品SPU", businessType = BusinessType.UPDATE)
    @PutMapping("/editBase")
    public CommonResult<Boolean> editBase(@Valid @RequestBody PrdtSpuSaveReqVO prdtSpuSaveReqVO) {
        prdtSpuService.updatePrdtSpuAfter(
                prdtSpuService.updateSpuBase(prdtSpuSaveReqVO)
        );
        return success(true);
    }

    /**
     * 修改商品SKU库存
     */
    @ApiOperation(value = "修改商品SKU库存", httpMethod = HttpMethod.PUT, notes = "权限字符:product:spu:edit")
    @RequiresPermissions("product:spu:edit")
    @Log(title = "商品SPU", businessType = BusinessType.UPDATE)
    @PutMapping("/editStock")
    public CommonResult<BigDecimal> editStock(@Valid @RequestBody PrdtUpdateSkuStockReqVO skuStockReqVO) {
        // 获取sku信息
        PrdtSku prdtSku = prdtSkuService.getPrdtSku(skuStockReqVO.getSkuId());
        // 编辑库存数量
        BigDecimal editStock = prdtSpuService.editStock(skuStockReqVO);
        // 刷新ES和缓存信息
        prdtSpuService.updatePrdtSpuAfter(
                PrdtSpuUpdateRespDTO.builder()
                        .spuId(prdtSku.getSpuId())
                        .skuIdList(ListUtil.toList(skuStockReqVO.getSkuId()))
                        .build()
        );
        return success(editStock);
    }

    /**
     * 停用启用
     */
    @ApiOperation(value = "停用启用", httpMethod = HttpMethod.PUT)
    @RequiresPermissions("product:spu:remove")
    @Log(title = "商品SPU - 停用启用", businessType = BusinessType.UPDATE)
    @PutMapping("/{spuIds}/{status}")
    public CommonResult<Boolean> updateStatus(@PathVariable Long[] spuIds, @PathVariable Long status) {
        prdtSpuService.updateStatus(spuIds, status);
        //刷新ES
        productEventService.acceptEvent(EsProductEventBuild.spuEvent(ListUtil.toList(spuIds)));
        return success(true);
    }

    /**
     * 删除商品SPU
     */
    @ApiOperation(value = "删除商品SPU", httpMethod = HttpMethod.DEL)
    @RequiresPermissions("product:spu:remove")
    @Log(title = "商品SPU - 删除商品SPU", businessType = BusinessType.DELETE)
    @DeleteMapping("/{spuIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] spuIds) {
        prdtSpuService.deletePrdtSpuBySpuIds(spuIds);
        //刷新ES
        productEventService.acceptEvent(EsProductEventBuild.spuEvent(ListUtil.toList(spuIds)));
        return success(true);
    }


    /**
     * 获取商品SPU详细信息
     */
    @ApiOperation(value = "获得商品SPU详情", httpMethod = HttpMethod.GET)
    @RequiresPermissions("product:spu:query")
    @GetMapping(value = "/{spuId}")
    public CommonResult<PrdtSpuGroupInVO> getInfo(@PathVariable("spuId") Long spuId) {
        return success(prdtSpuService.getPrdtSpu(spuId));
    }

    /**
     * 分页查询商品SPU
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得商品SPU分页列表", httpMethod = HttpMethod.GET)
    @RequiresPermissions("product:spu:list")
    @DataScope(supplierAlias = "spu")
    public CommonResult<PageResult<PrdtSpuPageReqVO>> getPage(@Valid PrdtSpuPageReqVO pageReqVO) {
        return success(prdtSpuService.getPrdtSpuPage(pageReqVO));
    }

    /**
     * 分页查询商品SPU
     */
    @GetMapping("/spuList")
    @ApiOperation(value = "获得商品SPU唯一分页列表", httpMethod = HttpMethod.GET, notes = "权限字符: product:spu:spuList")
    @RequiresPermissions("product:spu:spuList")
    @DataScope(supplierAlias = "spu")
    public CommonResult<PageResult<PrdtSpuUniquePageRespVO>> getSpuPage(@Valid PrdtSpuPageReqVO pageReqVO) {
        return success(prdtSpuService.getPrdtSpuListPage(pageReqVO));
    }

    /**
     * 商品开启共享
     */
    @ApiOperation(value = "商品开启共享", httpMethod = HttpMethod.POST, notes = "权限字符: product:spu:share-enable")
    @RequiresPermissions("product:spu:share-enable")
    @Log(title = "商品SPU开启共享", businessType = BusinessType.UPDATE)
    @PostMapping("/shareEnable")
    public CommonResult<Boolean> shareEnable(@Valid @RequestBody PrdtSpuShareReqVO updateReqVO) {
        prdtSpuService.shareEnable(updateReqVO);
        return success(true);
    }

    /**
     * 商品关闭共享
     */
    @ApiOperation(value = "商品停用共享", httpMethod = HttpMethod.POST, notes = "权限字符: product:spu:share-disable")
    @RequiresPermissions("product:spu:share-disable")
    @Log(title = "商品SPU停用共享", businessType = BusinessType.UPDATE)
    @PostMapping("/shareDisable")
    public CommonResult<Boolean> shareDisable(@Valid @RequestBody PrdtSpuShareReqVO updateReqVO) {
        prdtSpuService.shareDisable(updateReqVO);
        return success(true);
    }

    /**
     * 上下架管理分页查询未上架商品SPU列表
     */
    @GetMapping("/listByNotItem")
    @ApiOperation(value = "上下架管理分页查询未上架商品SPU列表", httpMethod = HttpMethod.GET)
    public CommonResult<PageResult<PrdtSpuNotItemPageReqVo>> getPrdtSpuPageByNotItem(@Valid PrdtSpuNotItemPageReqVo pageReqVO) {
        return success(prdtSpuService.getPrdtSpuPageByNotItem(pageReqVO));
    }

    @PostMapping("/listByNotItemExport")
    @ApiOperation(value = "上架商品导出", httpMethod = HttpMethod.POST)
    public void getPrdtSpuPageByNotItemExport(HttpServletResponse response, PrdtSpuNotItemPageReqVo prdtSpuNotItemPageReqVo) throws IOException {
        List<PrdtSpuNotItemPageReqExportVo> list = prdtSpuService.getPrdtSpuPageByNotItemExport(prdtSpuNotItemPageReqVo);


        String path = "template" + File.separator + "商品上架导入-模板.xlsx";
        // 直接获取流
        ClassPathResource resource = new ClassPathResource(path);
        InputStream inputStream = resource.getInputStream();
        Workbook sheets = new XSSFWorkbook(inputStream);

        ExcelUtil<PrdtSpuNotItemPageReqExportVo> util = new ExcelUtil<>(PrdtSpuNotItemPageReqExportVo.class);
        util.exportExcel(response, list, "商品上架导入-模板", sheets, 1);
    }

    /**
     * 获取选中数据 (用于选中回显)
     *
     * @param spuIds spuID集合
     * @return 获取品牌I选中数据
     */
    @PostMapping("/getSelectedBatchInfo")
    @ApiOperation(value = "批量获取简略信息", httpMethod = HttpMethod.POST)
    public CommonResult<List<PrdtSpuSelectedRespVO>> getSelectedBatchInfo(@Valid @Size(min = NumberPool.INT_ONE) @RequestBody List<Long> spuIds) {
        return success(prdtSpuService.getSelectedPrdtSpu(spuIds));
    }

    /**
     * 获取入驻商 + 管理分类 参考佣金
     *
     * @param supplierId 入驻商ID
     * @param catgoryId  管理分类ID
     * @return 比例
     */
    @GetMapping("/getSupplierClassRate")
    @ApiOperation(value = " 获取入驻商 + 管理分类 参考佣金", httpMethod = HttpMethod.GET)
    public CommonResult<SupplierClassRateStatusRespVO> getSupplierClassRate(
            @ApiParam(name = "supplierId", value = "入驻商ID") Long supplierId,
            @ApiParam(name = "catgoryId", value = "一级管理分润ID") Long catgoryId
    ) {
        return success(prdtSupplierClassService.getSupplierClassRate(supplierId, catgoryId));
    }

    /**
     * 复制商品数据
     */
    @PostMapping("/copy")
    @RequiresPermissions("product:spu:copy")
    @ApiOperation(value = "商品拷贝", httpMethod = HttpMethod.POST, notes = "权限字符: product:spu:copy")
    public CommonResult<Long> copy(@RequestBody PrdtSpuSaveReqVO prdtSpuSaveReqVO) {
        return success(prdtSpuService.copySpu(prdtSpuSaveReqVO));
    }


    @ApiOperation(value = "导入商品基本信息", httpMethod = HttpMethod.POST)
    @Log(title = "商品SPU", businessType = BusinessType.IMPORT)
    @RequiresPermissions("product:spu:import")
    @PostMapping("/importData")
    public CommonResult<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<ProductImportExcel> util = new ExcelUtil<>(ProductImportExcel.class);
        List<ProductImportExcel> productList = util.importExcel(file.getInputStream(), 1);
        String message = prdtSpuService.importBaseProduct(productList);
        return success(message);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "获取商品基本信息导入模版", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<ProductImportExcel> util = new ExcelUtil<ProductImportExcel>(ProductImportExcel.class);
        String instructions = "填表说明：\n" +
                "1、商品名称：必填项；\n" +
                "2、三级类别编号：必填项，需要和系统中的商品管理类别三级类别的编号保持一致，且系统中的商品类别必须是启用状态，否则无法匹配，导入不成功；\n" +
                "3、品牌名称：非必填项，如果输入则必须和系统中启用的品牌保持一致，否则无法匹配，导入不成功；\n" +
                "4、最旧生产日期：商品最旧的生产日期，按照年月日输入，例如：2024-07-01；\n" +
                "5、最新生产日期：商品最新的生产日期，按照年月日输入，例如：2024-07-01，注意最新生产日期不能超过当前操作日，否则导入不成功；\n" +
                "6、保质期：非必填项，保质期按照天输入，仅限数字格式，否则导入不成功；\n" +
                "7、小单位：必填项，和系统中有的单位保持一致，否则无法匹配，导入不成功；\n" +
                "8、条码：必填项，限制13位字符长度，否则导入不成功；\n" +
                "9、库存数量：必填项，仅限数字，否则导入不成功；\n" +
                "10、限购：非必填，仅限数字，且不能大于库存数量，不填则默认为库存数；\n" +
                "11、订货组数：非必填项，默认为1；\n" +
                "12、起订量：非必填项，默认为1；\n" +
                "13、标准价：必填项，仅限数字输入，标准价不能低于供货价，否则导入不成功；\n" +
                "14、供货价：必填项，仅限数字输入，供货价不能高于标准价，否则导入不成功；\n" +
                "15、建议零售价：非必填项，仅限数字输入，否则导入不成功；\n" +
                //"16、商品详情（描述）：非必填项；\n" +
                "16、SPU辅助的商品编号：非必填项；\n" +
                "注意：商品导入到系统后会自动生成商品编号，编号唯一，此导入表仅支持小单位商品导入，如需添加多单位请于后台系统维护；";
        util.importTemplateExcel(response, "商品基本信息", StringUtils.EMPTY, instructions);
    }

    @ApiOperation(value = "批量修改商品品牌", httpMethod = HttpMethod.POST, notes = "权限标识 - product:spu:edit-brand")
    @Log(title = "批量修改商品品牌", businessType = BusinessType.IMPORT)
    @RequiresPermissions("product:spu:edit-brand")
    @PostMapping("/importUpdateBrandData")
    public CommonResult<String> importUpdateBrandData(MultipartFile file) throws Exception {
        ExcelUtil<ProductBrandImportUpdateExcel> util = new ExcelUtil<>(ProductBrandImportUpdateExcel.class);
        List<ProductBrandImportUpdateExcel> productList = util.importExcel(file.getInputStream(), 1);
        String message = prdtSpuService.importUpdateBrandData(productList);
        return success(message);
    }

    @PostMapping("/importUpdateProductBrandTemplate")
    @ApiOperation(value = "批量修改商品品牌导入模版", httpMethod = HttpMethod.POST)
    public void importUpdateProductBrandTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<ProductBrandImportUpdateExcel> util = new ExcelUtil<ProductBrandImportUpdateExcel>(ProductBrandImportUpdateExcel.class);
        String instructions = "填表说明：\n" +
                "1、入驻商编号：必填项，必须和系统中的编号保持一致，否则导入不成功；\n" +
                "2、商品编号：商品在系统中的唯一标识，必填，只允许填入数字，且必须和系统的中编号保持一致；\n" +
                "3、商品名称：非必填，和系统中的商品名称保持一致；\n" +
                "4、商品品牌：必填项，需填现系统中商品品牌，且必须和系统中的值保持一致，否则导入不成功";
        util.importTemplateExcel(response, "批量修改商品品牌", StringUtils.EMPTY, instructions);
    }

    @ApiOperation(value = "批量修改商品类别", httpMethod = HttpMethod.POST, notes = "权限标识 - product:spu:edit-category")
    @Log(title = "批量修改商品类别", businessType = BusinessType.IMPORT)
    @RequiresPermissions("product:spu:edit-category")
    @PostMapping("/importUpdateCategoryData")
    public CommonResult<String> importUpdateCategoryData(MultipartFile file) throws Exception {
        ExcelUtil<ProductCatgoryImportUpdateExcel> util = new ExcelUtil<>(ProductCatgoryImportUpdateExcel.class);
        List<ProductCatgoryImportUpdateExcel> productList = null;
        try {
            productList = util.importExcel(file.getInputStream(), 1);
        } catch (Exception e) {
            throw new ServiceException("请使用正确的模板进行上传");
        }
        String message = prdtSpuService.importUpdateCategoryData(productList);
        return success(message);
    }

    @PostMapping("/importUpdateProductCategoryTemplate")
    @ApiOperation(value = "批量修改商品类别导入模版", httpMethod = HttpMethod.POST)
    public void importUpdateProductCategoryTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<ProductCatgoryImportUpdateExcel> util = new ExcelUtil<ProductCatgoryImportUpdateExcel>(ProductCatgoryImportUpdateExcel.class);
        String instructions = "填表说明：\n" +
                "1、入驻商编号：必填项，必须和系统中的编号保持一致，否则导入不成功；\n" +
                "2、skuId：商品在系统中的唯一标识，必填，只允许填入数字，且必须和系统的中编号保持一致；\n" +
                "3、商品名称：非必填，和系统中的商品名称保持一致；\n" +
                "4、商品类别(三级管理类别)：必填项，需填现商品管理类别的三级类别，且必须要和平台管理类别，保持一致否则导入不成功；";
        util.importTemplateExcel(response, "批量修改商品类别", StringUtils.EMPTY, instructions);
    }

    @PostMapping("/exportProducts")
    @Log(title = "商品基本信息导出", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "商品基本信息导出", httpMethod = HttpMethod.POST, notes = "权限标识 - product:spu:export")
    @RequiresPermissions("product:spu:export")
    public void exportProducts(HttpServletResponse response, PrdtExportSpuVO prdtExportSpuVO) {
        prdtExportSpuVO.setSupplierId(SecurityUtils.getSupplierId());
        List<ProductExport> list = prdtSpuService.getExportProducts(prdtExportSpuVO);
        // 使用 Excel 工具类导出数据
        ExcelUtil<ProductExport> excelUtil = new ExcelUtil<>(ProductExport.class);
        excelUtil.exportExcel(response, list, "商品基本信息");
    }

    @PostMapping("/importProductImages")
    @RequiresPermissions("product:spu:batchImport")
    @ApiOperation(value = "批量上传商品图片", httpMethod = HttpMethod.POST)
    public CommonResult<String> importProductImages(@RequestParam("file") MultipartFile file) throws IOException {
        return CommonResult.success(prdtSpuService.importProductImages(file));
    }


    @PostMapping("/batchShelf")
    @RequiresPermissions("product:spu:batchShelf")
    @ApiOperation(value = "批量城市上架", httpMethod = HttpMethod.POST)
    public CommonResult<String> batchShelf(@RequestBody PrdtBatchShelfVO batchShelfVO) throws IOException {
        Long dcId = SecurityUtils.getLoginUser().getDcId(); // 运营商ID
        if (ToolUtil.isNotEmpty(dcId)) {
            return CommonResult.success(prdtSpuService.batchShelf(batchShelfVO));
        } else {
            return CommonResult.success(prdtSpuService.batchSupplierShelf(batchShelfVO));
        }
    }

    @PostMapping("/batchDownShelf")
    @RequiresPermissions("product:spu:batchShelf")
    @ApiOperation(value = "批量城市下架", httpMethod = HttpMethod.POST)
    public CommonResult<String> batchDownShelf(@RequestBody PrdtBatchShelfVO batchShelfVO) throws IOException {
        // 事务之外的刷新缓存事件
        Long dcId = SecurityUtils.getLoginUser().getDcId(); // 运营商ID
        if (ToolUtil.isNotEmpty(dcId)) {
            List<PrdtAreaItem> prdtAreaItems = prdtSpuService.batchDownShelf(batchShelfVO);
            prdtAreaItemService.cacheEvent(prdtAreaItems);
        } else {
            List<PrdtSupplierItem> prdtSupplierItems = prdtSpuService.batchSupplierDownShelf(batchShelfVO);
            prdtSupplierItemService.cacheEvent(prdtSupplierItems);
        }

        return CommonResult.success("");

    }


    @PostMapping("/checkBatchDownShelf")
    @RequiresPermissions("product:spu:batchShelf")
    @ApiOperation(value = "批量城市下架校验", httpMethod = HttpMethod.POST)
    public CommonResult<String> checkBatchDownShelf(@RequestBody @Valid List<String> supIds) throws IOException {
        // 事务之外的刷新缓存事件
        String warn = prdtSpuService.chefkBatchDownShelf(supIds.stream().map(Long::valueOf).collect(Collectors.toList()));
        return CommonResult.success(warn);

    }

    @ApiOperation(value = "上下架商品", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + PrdtSupplierItemController.Permissions.EDIT)
    @RequiresPermissions("product:spu:editShelf")
    @Log(title = "批量上下架入驻商商品", businessType = BusinessType.UPDATE)
    @PutMapping("/updateShelfStatus")
    public CommonResult<Boolean> updateShelfStatus(@RequestParam("skuId") String skuId,
                                                   @RequestParam(name = "minShelfStatus", required = false, defaultValue = "-1") Integer minShelfStatus,
                                                   @RequestParam(name = "midShelfStatus", required = false, defaultValue = "-1") Integer midShelfStatus,
                                                   @RequestParam(name = "largeShelfStatus", required = false, defaultValue = "-1") Integer largeShelfStatus) {
        // 事务之外的刷新缓存事件
        Long dcId = SecurityUtils.getLoginUser().getDcId(); // 运营商ID
        if (ToolUtil.isNotEmpty(dcId)) {
            List<PrdtAreaItem> supplierItems = prdtSpuService.updateShelfStatus(Long.valueOf(skuId), minShelfStatus, midShelfStatus, largeShelfStatus);
            prdtAreaItemService.cacheEvent(supplierItems);
        } else {
            List<PrdtSupplierItem> supplierItems = prdtSpuService.updateSupplierShelfStatus(Long.valueOf(skuId), minShelfStatus, midShelfStatus, largeShelfStatus);
            prdtSupplierItemService.cacheEvent(supplierItems);
        }
        return success(true);
    }


    @ApiOperation(value = "同步ERP库存", httpMethod = HttpMethod.POST)
    @RequiresPermissions("product:spu:remove")
    @Log(title = "同步ERP库存", businessType = BusinessType.UPDATE)
    @PostMapping("/syncStock")
    public CommonResult<Boolean> syncStock(@Valid @RequestBody List<Long> skuIds) {





        return success(true);
    }

}
