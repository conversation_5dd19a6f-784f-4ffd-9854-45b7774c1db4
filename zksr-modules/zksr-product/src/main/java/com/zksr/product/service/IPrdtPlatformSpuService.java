package com.zksr.product.service;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.platform.excel.ProductPlarformImportExcel;
import com.zksr.product.api.spu.vo.PrdtSpuGroupInVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSpuPageReqVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSpuSaveReqVO;
import com.zksr.product.controller.platform.vo.PrdtSpuGroupRespVO;
import com.zksr.product.controller.spu.vo.PrdtSpuShareReqVO;
import com.zksr.product.domain.PrdtPlatformSpu;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台商品库-商品SPUService接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
public interface IPrdtPlatformSpuService {

    /**
     * 获得平台商品库-商品SPU
     *
     * @param platformSpuId 商品SPU id
     * @return 平台商品库-商品SPU
     */
    public PrdtPlatformSpu getPrdtPlatformSpu(Long platformSpuId);

    /**
     * 获得平台商品库-商品SPU分页
     *
     * @param pageReqVO 分页查询
     * @return 平台商品库-商品SPU分页
     */
    PageResult<PrdtPlatformSpu> getPrdtPlatformSpuPage(PrdtPlatformSpuPageReqVO pageReqVO);

    /**
     * 获取自定义列表查询分页
     * @param pageReqVO
     * @return
     */
    PageResult<PrdtPlatformSpu> getPrdtPlatformSpuPageExt(PrdtPlatformSpuPageReqVO pageReqVO);

    /**
     * 更新产品库
     * @param spuId
     */
    void updatePlatformSpu(Long spuId);

    /**
     * 开启共享
     * @param updateReqVO
     */
    void shareEnable(PrdtSpuShareReqVO updateReqVO);

    /**
     * 关闭共享
     * @param updateReqVO
     */
    void shareDisable(PrdtSpuShareReqVO updateReqVO);

    /**
     * 获取共享商品详情
     * @param platformSpuId
     * @return
     */
    PrdtSpuGroupRespVO getPrdtPlatformSpuInfo(Long platformSpuId);

    /**
     * 更新商品引用数量
     * @param platformSpuId
     */
    void updateReferenced(Long platformSpuId);

    /**
     *  商品共享库导入共享商品信息
     * @param excelList
     * @return
     */
    String importPlatformProduct(List<ProductPlarformImportExcel> excelList);

    FileImportHandlerVo importPlatformProductEvent(List<ProductPlarformImportExcel> excelList, Long sysCode, Long fileImportId,Integer seq);

    /**
     *  商品共享库导入共享商品图片地址信息
     * @param excelList
     * @return
     */
    String importPlatformPicturesUrlInfo(List<ProductPlarformImportExcel> excelList);
}
