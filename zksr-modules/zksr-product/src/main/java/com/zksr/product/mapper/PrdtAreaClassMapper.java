package com.zksr.product.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRespDTO;
import com.zksr.product.controller.areaClass.vo.PrdtAreaClassPageReqVO;
import com.zksr.product.controller.catgory.dto.PrdtCatgoryCopyRespDTO;
import com.zksr.product.domain.PrdtAreaClass;
import com.zksr.product.domain.PrdtCatgory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import static com.zksr.common.core.pool.StringPool.LIMIT_ONE;


/**
 * 城市展示分类Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Mapper
public interface PrdtAreaClassMapper extends BaseMapperX<PrdtAreaClass> {
    default PageResult<PrdtAreaClass> selectPage(PrdtAreaClassPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtAreaClass>()
                .eqIfPresent(PrdtAreaClass::getAreaClassId, reqVO.getAreaClassId())
                .eqIfPresent(PrdtAreaClass::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrdtAreaClass::getAreaId, reqVO.getAreaId())
                .eqIfPresent(PrdtAreaClass::getPid, reqVO.getPid())
                .likeIfPresent(PrdtAreaClass::getAreaClassName, reqVO.getAreaClassName())
                .eqIfPresent(PrdtAreaClass::getIcon, reqVO.getIcon())
                .eqIfPresent(PrdtAreaClass::getSort, reqVO.getSort())
                .eqIfPresent(PrdtAreaClass::getStatus, reqVO.getStatus())
                .eqIfPresent(PrdtAreaClass::getDzwlFlag, reqVO.getDzwlFlag())
                .eqIfPresent(PrdtAreaClass::getSupplierId, reqVO.getSupplierId())
                .eq(PrdtAreaClass::getDelFlag, StringPool.ZERO)
                .applyScope(reqVO.getParams())
                .orderByAsc(PrdtAreaClass::getSort)
                .orderByDesc(PrdtAreaClass::getCreateTime));
    }

    /**
     * @Description: 获取所有城市展示分类信息
     * @Author: liuxingyu
     * @Date: 2024/3/23 9:08
     */
    default List<PrdtAreaClass> getAreaClassList(Long areaClassId, List<Long> areaIds, String status, Integer level) {
        return selectList(new LambdaQueryWrapper<PrdtAreaClass>()
                .eq(StringUtils.isNoneBlank(status), PrdtAreaClass::getStatus, status)
                .eq(ObjectUtil.isNotNull(level), PrdtAreaClass::getLevel, level)
                .ne(ObjectUtil.isNotNull(areaClassId), PrdtAreaClass::getAreaClassId, areaClassId)
                .in(ObjectUtil.isNotEmpty(areaIds), PrdtAreaClass::getAreaId, areaIds)
                .eq(PrdtAreaClass::getDelFlag, StringPool.ZERO)
                .orderByAsc(PrdtAreaClass::getSort)
                .orderByDesc(PrdtAreaClass::getCreateTime));
    }

    /**
     * @Description: 获取非电子围栏分类和非渠道展示分类
     * @Author: liuxingyu
     * @Date: 2024/3/27 9:09
     */
    List<PrdtAreaClass> prdtAreaClassMapper(@Param("areaId") Long areaId);

    /**
     * @Description: 获取渠道绑定的城市展示分类
     * @Author: liuxingyu
     * @Date: 2024/3/27 9:48
     */
    List<PrdtAreaClass> getAreaClassChannelList(@Param("channelId") Long channelId);

    /**
     * @Description: 获取入驻商绑定的城市展示分类
     * @Author: liuxingyu
     * @Date: 2024/3/27 15:15
     */
    default List<PrdtAreaClass> getAreaClassSupplierList(Long supplierId) {
        return selectList(new LambdaQueryWrapper<PrdtAreaClass>().eq(PrdtAreaClass::getSupplierId, supplierId));
    }

    /**
     * @Description: 获取门店绑定的城市展示分类(根据入驻商ID集合获取)
     * @Author: liuxingyu
     * @Date: 2024/3/28 17:46
     */
    List<PrdtAreaClass> getAreaClassBranchList(@Param("supplierIds") List<Long> supplierIds);

    /**
     * @Description: 校验名称
     * @Author: liuxingyu
     * @Date: 2024/4/1 17:56
     */
    default Long checkNameUnique(String name, Long pid, Long areaId, Long id) {
        return selectCount(new LambdaQueryWrapper<PrdtAreaClass>()
                .eq(PrdtAreaClass::getAreaClassName, name)
                .eq(PrdtAreaClass::getPid, pid)
                .eq(PrdtAreaClass::getAreaId, areaId)
                .eq(PrdtAreaClass::getDelFlag, StringPool.ZERO)
                .ne(ObjectUtil.isNotNull(id), PrdtAreaClass::getAreaClassId, id));
    }

    /**
     * 批量获取展示分类信息   (暂时用于支持数据回显)
     *
     * @param saleClassIds 展示分类ID集合
     * @return 展示分类集合
     */
    default List<PrdtAreaClass> selectSelectedAreaClass(List<Long> saleClassIds) {
        return selectList(new LambdaQueryWrapper<PrdtAreaClass>().in(PrdtAreaClass::getAreaClassId, saleClassIds));
    }

    /**
     * @Description: 根据PID获取城市展示分类数据
     * @Author: liuxingyu
     * @Date: 2024/4/16 17:45
     */
    default List<PrdtAreaClass> selectByPid(Long areaClassId) {
        return selectList(new LambdaQueryWrapper<PrdtAreaClass>().eq(PrdtAreaClass::getPid, areaClassId));
    }

    /**
     * @Description: 根据PID集合获取城市展示分类数据
     * @Author: liuxingyu
     * @Date: 2024/4/16 17:45
     */
    default List<PrdtAreaClass> selectByPidList(List<Long> areaClassIdList) {
        return selectList(new LambdaQueryWrapper<PrdtAreaClass>().in(PrdtAreaClass::getPid, areaClassIdList));
    }

    /**
     * @Description: 根据区域ID和渠道ID 获取城市展示分类
     * @Author: liuxingyu
     * @Date: 2024/4/24 10:55
     */
    List<PrdtAreaClass> getAreaClassAreaChannelList(@Param("areaId") Long areaId, @Param("chanelId") Long channelId);

    /**
     * @Description: 平台管理分类同步平台展示分类
     * @Author: liuxingyu
     * @Date: 2024/5/24 9:04
     */
    void copyToAreaClass(@Param("areaId") Long areaId);

    /**
     * 根据城市ID获取该城市下不存在的平台展示分类
     * @param areaId
     * @return
     */
    List<PrdtCatgoryCopyRespDTO> getAreaClassNotExistByAreaId(@Param("areaId") Long areaId);

    /**
     * 根据分类名 和 级别 获取分类信息
     * @param areaClassName
     * @param level
     * @return
     */
    default PrdtAreaClass selectByNameAndLevel(String areaClassName, Integer level) {
        return selectOne(new LambdaQueryWrapper<PrdtAreaClass>()
                .eq(PrdtAreaClass::getAreaClassName, areaClassName)
                .eq(PrdtAreaClass::getLevel, level));
    }

    /**
     * 根据城市ID获取该城市下展示分类总数
     * @param areaId
     * @return
     */
    default Long getAreaClassCountByAreaId(Long areaId) {
        return selectCount(new LambdaQueryWrapper<PrdtAreaClass>()
                .eq(PrdtAreaClass::getAreaId, areaId)
                .eq(PrdtAreaClass::getDelFlag, StringPool.ZERO)
        );
    }

    Integer selectCountByAreaItem(@Param("areaClassId") Long areaClassId);

    /**
     * 统计有效的下级
     *
     * @param areaClassId
     * @return
     */
    default Long selectCountValidateChild(Long areaClassId) {
        return selectCount(new LambdaQueryWrapper<PrdtAreaClass>()
                .eq(PrdtAreaClass::getPid, areaClassId)
                .eq(PrdtAreaClass::getDelFlag, StringPool.ZERO)
        );
    }

    /**
     * 区域下 城市展示三级分类名称是否在二级分类下重复
     * @param tertiaryCategoryName
     * @param secondaryCategoryId
     * @param regionCode
     * @return
     */
    default PrdtAreaClass checkUniqueTertiaryCategoryInSecondaryCategory(String tertiaryCategoryName, Long secondaryCategoryId, Long regionCode){
        return selectOne(new LambdaQueryWrapper<PrdtAreaClass>()
                .eq(PrdtAreaClass::getAreaClassName, tertiaryCategoryName)
                .eq(PrdtAreaClass::getPid, secondaryCategoryId)
                .eq(PrdtAreaClass::getAreaId, regionCode)
                .eq(PrdtAreaClass::getDelFlag, StringPool.ZERO)
        );
    }

    default PrdtAreaClass checkCategoryExistenceByNameAndLevel(String categoryName, int level, Long pid, Long regionCode) {
        // 创建查询构造器
        LambdaQueryWrapper<PrdtAreaClass> queryWrapper = new LambdaQueryWrapper<PrdtAreaClass>()
                .eq(PrdtAreaClass::getAreaClassName, categoryName)  // 分类名称
                .eq(PrdtAreaClass::getLevel, level)  // 分类级别
                .eq(PrdtAreaClass::getDelFlag, StringPool.ZERO)
                .eq(PrdtAreaClass::getAreaId, regionCode);  //
        if (pid != null) {
            queryWrapper.eq(PrdtAreaClass::getPid, pid);
        }
        // 返回查询结果（如果存在则返回第一个匹配的结果）
        return selectOne(queryWrapper.last(LIMIT_ONE));
    }


    /**
     * 根据条件获取城市分类集合
     * @param pidId
     * @param areaId
     * @param status
     * @param level
     * @return
     */
    default List<PrdtAreaClass> selectClassList(Long pidId,Long areaId, String status,Integer level) {
        return selectList(new LambdaQueryWrapperX<PrdtAreaClass>()
                .eqIfPresent(PrdtAreaClass::getStatus, status)
                .eqIfPresent(PrdtAreaClass::getLevel, level)
                .eqIfPresent(PrdtAreaClass::getPid, pidId)
                .eqIfPresent(PrdtAreaClass::getAreaId, areaId)
                .eq(PrdtAreaClass::getDelFlag, StringPool.ZERO));
    }


    default PrdtAreaClass selectClassByCopy(String name, Long pid, Long areaId,String status) {
        return selectOne(new LambdaQueryWrapperX<PrdtAreaClass>()
                .eqIfPresent(PrdtAreaClass::getAreaClassName, name)
                .eqIfPresent(PrdtAreaClass::getPid, pid)
                .eqIfPresent(PrdtAreaClass::getAreaId, areaId)
                .eqIfPresent(PrdtAreaClass::getStatus,status)
                .eq(PrdtAreaClass::getDelFlag, StringPool.ZERO));
    }

    default List<PrdtAreaClassExportVo> selectPrdtAreaClassExportList(@Param("reqVO") PrdtAreaClassExportVo reqVO){
        PageResult<PrdtAreaClass> prdtAreaClassPageResult = selectPage(reqVO, new LambdaQueryWrapperX<PrdtAreaClass>()
                .eqIfPresent(PrdtAreaClass::getAreaClassId, reqVO.getAreaClassId())
                .eqIfPresent(PrdtAreaClass::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrdtAreaClass::getAreaId, reqVO.getAreaId())
                .eqIfPresent(PrdtAreaClass::getPid, reqVO.getPid())
                .likeIfPresent(PrdtAreaClass::getAreaClassName, reqVO.getAreaClassName())
                .eqIfPresent(PrdtAreaClass::getIcon, reqVO.getIcon())
                .eqIfPresent(PrdtAreaClass::getSort, reqVO.getSort())
                .eqIfPresent(PrdtAreaClass::getStatus, reqVO.getStatus())
                .eqIfPresent(PrdtAreaClass::getDzwlFlag, reqVO.getDzwlFlag())
                .eqIfPresent(PrdtAreaClass::getSupplierId, reqVO.getSupplierId())
                .eq(PrdtAreaClass::getDelFlag, StringPool.ZERO)
                .applyScope(reqVO.getParams())
                .orderByAsc(PrdtAreaClass::getSort)
                .orderByDesc(PrdtAreaClass::getCreateTime));
        return HutoolBeanUtils.toBean(prdtAreaClassPageResult, PrdtAreaClassExportVo.class).getList();
    }

    /**
     * 根据区域编号和展示分类编号获取分类信息
     * @param areaId
     * @param areaClassId
     * @return
     */
    default PrdtAreaClass selectClassByAreaIdAndAreaClassId(Long areaId, Long areaClassId) {
        return selectOne(new LambdaQueryWrapperX<PrdtAreaClass>()
                .eqIfPresent(PrdtAreaClass::getAreaId, areaId)
                .eqIfPresent(PrdtAreaClass::getAreaClassId, areaClassId)
                .eq(PrdtAreaClass::getDelFlag, StringPool.ZERO));
    }
}
