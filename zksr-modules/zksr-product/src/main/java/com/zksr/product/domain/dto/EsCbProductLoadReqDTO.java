package com.zksr.product.domain.dto;

import com.zksr.product.api.model.event.EsCbProductEvent;
import com.zksr.product.api.model.event.EsReleaseProductEvent;
import com.zksr.product.api.model.event.EsSkuUpdateProductEvent;
import com.zksr.product.api.model.event.EsSpuUpdateProductEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 加载数据库数据搜索实体
 * @date 2024/3/1 9:41
 */
@Data
@NoArgsConstructor
public class EsCbProductLoadReqDTO {

    @ApiModelProperty("上架发布商品ID, 非商品ID")
    private List<Long> itemIds;

    @ApiModelProperty("组合商品spuId集合")
    private List<Long> spuCombineId;

    @ApiModelProperty(value = "最小ID", notes = "用于批量刷新")
    private Long minId;

    public EsCbProductLoadReqDTO(EsReleaseProductEvent data) {
        this.itemIds = data.getItemIds();
    }

    public EsCbProductLoadReqDTO(EsCbProductEvent data) {
        this.spuCombineId = data.getSpuCombineId();
    }

    public static EsCbProductLoadReqDTO build(EsReleaseProductEvent data) {
        return new EsCbProductLoadReqDTO(data);
    }

    public static EsCbProductLoadReqDTO build(EsCbProductEvent data) {
        return new EsCbProductLoadReqDTO(data);
    }
}
