package com.zksr.product.controller.supplierItem.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 入驻商上架商品对象 prdt_supplier_item
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Data
@ApiModel("入驻商上架商品 - prdt_supplier_item分页 Request VO")
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSupplierItemSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 入驻商上架商品id */
    @ApiModelProperty(value = "入驻商上架商品id",required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierItemId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 平台商展示分类id */
    @Excel(name = "平台商展示分类id")
    @ApiModelProperty(value = "平台商展示分类id",required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long saleClassId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id",required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    @ApiModelProperty(value = "商品SPU id" ,required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    @ApiModelProperty(value = "商品sku id",required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 上架状态-数据字典 */
    @Excel(name = "上架状态-数据字典")
    @ApiModelProperty(value = "上架状态-数据字典0 下架， 1上架",required = true)
    private Integer shelfStatus;

    /** 修改商品标识 */
    @Excel(name = "修改商品标识")
    @ApiModelProperty(value = "修改商品标识 0 修改排序 1 修改类别", example = "示例值")
    private Integer updateFlag;

    /** 排序序号 */
    @Excel(name = "排序序号")
    @ApiModelProperty(value = "排序序号", example = "示例值",required = true)
    private Integer sortNum;

    /** 小单位-上下架状态 */
    @Excel(name = "小单位-上下架状态")
    @ApiModelProperty(value = "小单位-上下架状态", example = "示例值")
    private Integer minShelfStatus;

    /** 中单位-上下架状态 */
    @Excel(name = "中单位-上下架状态")
    @ApiModelProperty(value = "中单位-上下架状态", example = "示例值")
    private Integer midShelfStatus;

    /** 大单位-上下架状态 */
    @Excel(name = "大单位-上下架状态")
    @ApiModelProperty(value = "大单位-上下架状态", example = "示例值")
    private Integer largeShelfStatus;

    /** 入驻商上架商品id集合 */
    @ApiModelProperty(value = "入驻商上架商品id集合")
    @JsonSerialize(using= CustomLongSerialize.class)
    private List<Long> supplierItemIds;

    /** 0-普通商品, 1-组合商品 */
    @Excel(name = "0-普通商品, 1-组合商品")
    @ApiModelProperty(value = "0-普通商品, 1-组合商品")
    private Integer itemType;


    public PrdtSupplierItemSaveReqVO(Long saleClassId,Long supplierId, Long spuId, Long skuId) {
        this.saleClassId = saleClassId;
        this.spuId = spuId;
        this.skuId = skuId;
        this.supplierId = supplierId;
    }
}
