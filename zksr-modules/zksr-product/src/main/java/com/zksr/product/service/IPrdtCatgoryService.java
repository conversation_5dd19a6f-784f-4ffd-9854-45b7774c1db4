package com.zksr.product.service;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.catgory.dto.CatgoryDeleteDTO;
import com.zksr.product.api.catgory.dto.CatgoryIdDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.api.catgory.excel.CategoryImportExcel;
import com.zksr.product.controller.catgory.vo.PrdtCatgoryPageReqVO;
import com.zksr.product.controller.catgory.vo.PrdtCatgoryRespVO;
import com.zksr.product.controller.catgory.vo.PrdtCatgorySaveReqVO;
import com.zksr.product.controller.supplierClass.vo.SupplierClassRateStatusRespVO;
import com.zksr.product.domain.PrdtCatgory;
import com.zksr.product.domain.dto.BoundProductInfoDTO;
import com.zksr.product.domain.excel.ProductImportExcel;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台商管理分类Service接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
public interface IPrdtCatgoryService {

    /**
     * 新增平台商管理分类
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtCatgory(@Valid PrdtCatgorySaveReqVO createReqVO);

    /**
     * 修改平台商管理分类
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public Long updatePrdtCatgory(@Valid PrdtCatgorySaveReqVO updateReqVO);

    /**
     * 删除平台商管理分类
     *
     * @param classId 平台商管理分类id
     */
    public void deletePrdtCatgory(Long classId);

    /**
     * 批量删除平台商管理分类
     *
     * @param classIds 需要删除的平台商管理分类主键集合
     * @return 结果
     */
    public void deletePrdtCatgoryByClassIds(Long[] classIds);

    /**
     * 获得平台商管理分类
     *
     * @param classId 平台商管理分类id
     * @return 平台商管理分类
     */
    public PrdtCatgory getPrdtCatgory(Long classId);

    /**
     * 获得平台商管理分类分页
     *
     * @param pageReqVO 分页查询
     * @return 平台商管理分类分页
     */
    PageResult<PrdtCatgory> getPrdtCatgoryPage(PrdtCatgoryPageReqVO pageReqVO);

    /**
     * @Description: 获取平台商管理分类子级列表
     * @Author: liuxingyu
     * @Date: 2024/2/19 15:57
     */
    PageResult<PrdtCatgoryRespVO> getSublevelCatgoryById(PrdtCatgoryPageReqVO pageReqVO);

    /**
     * @Description: 获取平台商管理分类列表
     * @Author: liuxingyu
     * @Date: 2024/2/27 14:35
     */
    List<PrdtCatgoryRespVO> getCatgoryList(Long catgoryId, Long supplierId, Integer level);

    List<PrdtCatgoryRespVO> getCatgoryByList(List<Long> catgoryId,Long supplierId);

    /**
     * @Description: 根据入驻商编号获取绑定的管理类别
     * @Author: liuxingyu
     * @Date: 2024/3/8 11:16
     */
    List<PrdtCatgoryRespVO> getCatgoryBySupplierId(Long supplierId);

    /**
    * @Description: 根据管理分类ID获取管理分类信息
    * @Author: liuxingyu
    * @Date: 2024/3/25 10:48
    */
    PrdtCatgory getCatgoryByCatgoryId(Long catgoryId);

    /**
     * 获取三级管理分类分润比例
     * @param catgoryId
     * @return
     */
    CatgoryRateDTO getThreeCatgoryByIdAndAreaId(Long catgoryId, Long areaId);

    /**
     * 清除缓存
     * @param catgoryId 管理分类ID
     */
    Long cleanCache(Long catgoryId);

    /**
     * 清除缓存
     * @param catgoryRateId 扣点ID
     */
    Long cleanRateCache(Long catgoryRateId);

    /**
    * @Description: 更改管理分类状态
    * @Author: liuxingyu
    * @Date: 2024/4/1 11:39
    */
    Integer changeStatus(PrdtCatgorySaveReqVO updateReqVO);

    /**
     * 获取管理分类按照名称和级别
     * @param categoryName  管理分类名称
     * @param level         分类级别
     * @return  管理分类
     */
    PrdtCatgory getCategoryByNameAndLevel(String categoryName, int level);

    /**
    * @Description: 根据平台code获取平台管理分类
    * @Author: liuxingyu
    * @Date: 2024/5/7 11:14
    */
    List<PrdtCatgory> getListBySysCode(Long sysCode);

    /**
    * @Description: 获取平台管理类别一级Id
    * @Author: liuxingyu
    * @Date: 2024/5/16 17:02
    */
    List<CatgoryIdDTO> getCatgoryFirstId();

    /**
    * @Description: 通过id集合获取管理分类信息
    * @Author: liuxingyu
    * @Date: 2024/5/18 15:21
    */
    List<PrdtCatgory> getCatgoryByIds(List<Long> catgoryIds);

    /**
    * @Description: 平台管理分类同步平台展示分类
    * @Author: liuxingyu
    * @Date: 2024/5/24 9:03
    */
    Boolean copyToAreaClass(Long areaId);

    /**
    * @Description: 平台管理分类同步平台展示分类
    * @Author: liuxingyu
    * @Date: 2024/5/24 9:03
    */
    Boolean copyToSaleClass(Long groupId);

    /**
    * @Description: 入驻商获取管理分类分页数据
    * @Author: liuxingyu
    * @Date: 2024/5/31 15:11
    */
    PageResult<PrdtCatgoryRespVO> getPageBySupplierId(PrdtCatgoryPageReqVO pageReqVO);

    /**
     * 导入平台管理类别数据
     * @return
     */
    String importBaseCategory(List<CategoryImportExcel> categoryList);

    FileImportHandlerVo importBaseCategoryEvent(List<CategoryImportExcel> categoryList, Long sysCode, Long fileImportId,Integer seq);


    /**
     * 删除平台管理类别时判断平台管理类别是否挂商品
     * @param catgoryId 类别ID
     * @param sysCode   平台商ID
     */
    CommonResult<List<BoundProductInfoDTO>>  isCatgoryHangGoods(Long catgoryId, Long sysCode);

    /**
     * 获取销售分润占比
     * @return
     */
    SupplierClassRateStatusRespVO getSaleClassRate();

    CatgoryDeleteDTO batchDeleteCatogory(List<Long> classIds);

    List<PrdtCatgoryRespVO> getCategoryBySupplierIds(List<String> supplierIds);
}
