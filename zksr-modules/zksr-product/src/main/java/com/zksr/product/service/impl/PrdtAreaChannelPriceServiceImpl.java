package com.zksr.product.service.impl;

import com.alicp.jetcache.Cache;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.controller.areaChannelPrice.vo.PrdtAreaChannelPriceRespVO;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.ChannelApi;
import com.zksr.system.api.channel.dto.ChannelDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtAreaChannelPriceMapper;
import com.zksr.product.domain.PrdtAreaChannelPrice;
import com.zksr.product.controller.areaChannelPrice.vo.PrdtAreaChannelPricePageReqVO;
import com.zksr.product.controller.areaChannelPrice.vo.PrdtAreaChannelPriceSaveReqVO;
import com.zksr.product.service.IPrdtAreaChannelPriceService;

import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 城市渠道价格Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Service
public class PrdtAreaChannelPriceServiceImpl implements IPrdtAreaChannelPriceService {
    @Autowired
    private PrdtAreaChannelPriceMapper prdtAreaChannelPriceMapper;

    @Autowired
    private AreaApi remoteAreaApi;

    @Autowired
    private ChannelApi remoteChannelApi;

    @Autowired
    @Qualifier("areaSalePriceCodeCache")
    private Cache<String, Integer> areaSalePriceCodeCache;

    /**
     * 新增城市渠道价格
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtAreaChannelPrice(PrdtAreaChannelPriceSaveReqVO createReqVO) {
        //校验商品区域城市 是否是二级
        remoteAreaApi.checkAreaByAreaId(createReqVO.getAreaId());

        // 插入
        PrdtAreaChannelPrice prdtAreaChannelPrice = HutoolBeanUtils.toBean(createReqVO, PrdtAreaChannelPrice.class);

        //清除Redis价格码
        removeRedisPrice(createReqVO.getAreaId(),createReqVO.getChannelId());
        // 返回
        return prdtAreaChannelPriceMapper.insertPrdtAreaChannelPrice(prdtAreaChannelPrice);
    }

    /**
     * 修改城市渠道价格
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtAreaChannelPrice(PrdtAreaChannelPriceSaveReqVO updateReqVO) {
        //校验商品区域城市 是否是二级
        remoteAreaApi.checkAreaByAreaId(updateReqVO.getAreaId());
        //清除Redis价格码
        removeRedisPrice(updateReqVO.getAreaId(),updateReqVO.getChannelId());
        prdtAreaChannelPriceMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrdtAreaChannelPrice.class));
    }

    /**
     * 删除城市渠道价格
     *
     * @param channelId 平台商id
     * @param areaId
     */
    @Override
    public void deletePrdtAreaChannelPrice(Long channelId, Long areaId) {
        //清除Redis价格码
        removeRedisPrice(areaId,channelId);
        // 删除
        prdtAreaChannelPriceMapper.deleteAreaChannelPrice(channelId,areaId);
    }

    /**
     * 批量删除城市渠道价格
     *
     * @param sysCodes 需要删除的城市渠道价格主键
     * @return 结果
     */
    @Override
    public void deletePrdtAreaChannelPriceBySysCodes(Long[] sysCodes) {
/*        for(Long sysCode : sysCodes){
            this.deletePrdtAreaChannelPrice(sysCode);
        }*/
    }

    /**
     * 获得城市渠道价格
     *
     * @param sysCode 平台商id
     * @return 城市渠道价格
     */
    @Override
    public PrdtAreaChannelPrice getPrdtAreaChannelPrice(Long sysCode) {
        return prdtAreaChannelPriceMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtAreaChannelPricePageReqVO> getPrdtAreaChannelPricePage(PrdtAreaChannelPricePageReqVO pageReqVO) {
        PageResult<PrdtAreaChannelPrice> result = prdtAreaChannelPriceMapper.selectPage(pageReqVO);
        PageResult<PrdtAreaChannelPricePageReqVO> resultPage = new PageResult<PrdtAreaChannelPricePageReqVO>(HutoolBeanUtils.toBean(result.getList(), PrdtAreaChannelPricePageReqVO.class),result.getTotal());
        //匹配分页数据
        List<PrdtAreaChannelPricePageReqVO> list = resultPage.getList();
        if(ToolUtil.isNotEmpty(list)){
            //获取城市列表
            CommonResult<Map<Long, AreaDTO>> areaMapResult = remoteAreaApi.getAreaMapBySysCode(SecurityUtils.getLoginUser().getSysCode(),SecurityUtils.getLoginUser().getDcId());
            Map<Long, AreaDTO> areaMap = areaMapResult.getData();
            Boolean areaMapFlag = false;
            if(ToolUtil.isNotEmpty(areaMap)){
                areaMapFlag = true;
            }
            Boolean areaFlag = areaMapFlag;

            //获取渠道列表
            CommonResult<Map<Long, ChannelDTO>> channelResult = remoteChannelApi.getChannelListByDcIdAndSysCode(SecurityUtils.getLoginUser().getSysCode(),SecurityUtils.getLoginUser().getDcId());
            Map<Long, ChannelDTO> channelMap = channelResult.getData();
            Boolean channelMapFlag = false;
            if(ToolUtil.isNotEmpty(channelMap)){
                channelMapFlag = true;
            }
            Boolean channelFlag = channelMapFlag;
            list.forEach(price ->{
                //获取城市列表
                if(areaFlag && ToolUtil.isNotEmpty(areaMap.get(price.getAreaId()))){
                    price.setAreaName(areaMap.get(price.getAreaId()).getAreaName());
                }

                //获取渠道列表
                if(channelFlag && ToolUtil.isNotEmpty(channelMap.get(price.getChannelId()))){
                    price.setChannelName(channelMap.get(price.getChannelId()).getChannelName());
                }

            });
        }
        return resultPage;
    }

    @Override
    public PrdtAreaChannelPrice getPriceByAreaIdAndChannelId(Long areaId, Long channelId) {
        return prdtAreaChannelPriceMapper.selectAreaChannelPrice(new PrdtAreaChannelPriceRespVO(areaId,channelId));
    }

    private void validatePrdtAreaChannelPriceExists(Long sysCode) {
        if (prdtAreaChannelPriceMapper.selectById(sysCode) == null) {
            throw exception(PRDT_AREA_CHANNEL_PRICE_NOT_EXISTS);
        }
    }

    /**
     * 清除Redis中的价格码
     * @param areaId
     * @param channelId
     */
    private void removeRedisPrice(Long areaId,Long channelId){
        //组装价格码的key
        StringJoiner priceCodeKey = new StringJoiner("-");
        priceCodeKey.add(areaId.toString());
        priceCodeKey.add(channelId.toString());
        //清除价格码缓存
        areaSalePriceCodeCache.remove(priceCodeKey.toString());

    }

}
