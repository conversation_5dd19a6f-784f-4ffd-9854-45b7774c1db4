package com.zksr.product.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRateDTO;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRespDTO;
import com.zksr.product.api.supplierClass.dto.SupplierClassRespDTO;
import com.zksr.product.controller.supplierClass.vo.PrdtSupplierClassBatchEditReqVO;
import com.zksr.product.controller.supplierClass.vo.SupplierClassRateStatusRespVO;
import com.zksr.product.convert.saleClass.SupplierClassRateConvert;
import com.zksr.product.convert.supplierClass.SupplierClassConvert;
import com.zksr.product.domain.PrdtCatgory;
import com.zksr.product.domain.PrdtSupplierClassRate;
import com.zksr.product.mapper.PrdtCatgoryMapper;
import com.zksr.product.mapper.PrdtSupplierClassRateMapper;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtSupplierClassMapper;
import com.zksr.product.domain.PrdtSupplierClass;
import com.zksr.product.controller.supplierClass.vo.PrdtSupplierClassPageReqVO;
import com.zksr.product.controller.supplierClass.vo.PrdtSupplierClassSaveReqVO;
import com.zksr.product.service.IPrdtSupplierClassService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 入驻商-平台商管理分类 关联关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class PrdtSupplierClassServiceImpl implements IPrdtSupplierClassService {

    @Autowired
    private PrdtSupplierClassMapper prdtSupplierClassMapper;

    @Autowired
    private PrdtSupplierClassRateMapper prdtSupplierClassRateMapper;

    @Autowired
    private IProductCacheService productCacheService;

    @Autowired
    private PrdtCatgoryMapper catgoryMapper;

    /**
     * 新增入驻商-平台商管理分类 关联关系
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtSupplierClass(PrdtSupplierClassSaveReqVO createReqVO) {
        // 插入
        PrdtSupplierClass prdtSupplierClass = HutoolBeanUtils.toBean(createReqVO, PrdtSupplierClass.class);
        prdtSupplierClassMapper.insert(prdtSupplierClass);
        // 返回
        return prdtSupplierClass.getCatgoryId();
    }

    /**
     * 修改入驻商-平台商管理分类 关联关系
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtSupplierClass(PrdtSupplierClassBatchEditReqVO updateReqVO) {
        if (!SecurityUtils.isSupplier()) {
            throw new ServiceException("非入驻商角色!");
        }
        updateReqVO.setSupplierId(SecurityUtils.getSupplierId());
        prdtSupplierClassMapper.batchEditCatgoryAfterConfig(updateReqVO);
    }

    /**
     * 删除入驻商-平台商管理分类 关联关系
     *
     * @param catgoryId 平台商管理分类id;平台商管理分类id
     */
    @Override
    public void deletePrdtSupplierClass(Long catgoryId) {
        // 删除
        prdtSupplierClassMapper.deleteById(catgoryId);
    }

    /**
     * 批量删除入驻商-平台商管理分类 关联关系
     *
     * @param catgoryIds 需要删除的入驻商-平台商管理分类 关联关系主键
     * @return 结果
     */
    @Override
    public void deletePrdtSupplierClassByCatgoryIds(Long[] catgoryIds) {
        for (Long catgoryId : catgoryIds) {
            this.deletePrdtSupplierClass(catgoryId);
        }
    }

    /**
     * 获得入驻商-平台商管理分类 关联关系
     *
     * @param catgoryId 平台商管理分类id;平台商管理分类id
     * @return 入驻商-平台商管理分类 关联关系
     */
    @Override
    public SupplierClassRespDTO getPrdtSupplierClass(Long catgoryId) {
        if (!SecurityUtils.isSupplier()) {
            throw new ServiceException("非入驻商角色!");
        }
        return prdtSupplierClassMapper.selectListPage(
                PrdtSupplierClassPageReqVO.builder()
                        .catgoryId(catgoryId)
                        .supplierId(SecurityUtils.getSupplierId())
                        .build()
        ).stream().findFirst().orElse(null);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<SupplierClassRespDTO> getPrdtSupplierClassPage(PrdtSupplierClassPageReqVO pageReqVO) {
        pageReqVO.setSupplierId(SecurityUtils.getSupplierId());
        Page<SupplierClassRespDTO> page = PageUtils.startPage(pageReqVO);
        return new PageResult<>(prdtSupplierClassMapper.selectListPage(pageReqVO), page.getTotal());
    }

    /**
     * @Description: 通过入驻商ID获取绑定的所有管理分类ID
     * @Author: liuxingyu
     * @Date: 2024/3/6 15:19
     */
    @Override
    public List<Long> getCatgoryIdListBySupplierId(Long supplierId) {
        return prdtSupplierClassMapper.getCatgoryIdListBySupplierId(supplierId);
    }

    /**
     * @Description: 新增入驻商绑定管理类别
     * @Author: liuxingyu
     * @Date: 2024/3/6 16:31
     */
    @Override
    @Transactional
    public Boolean insetBath(PrdtSupplierClassRespDTO supplierClass) {
        Long supplierId = supplierClass.getSupplierId();
        List<PrdtSupplierClass> prdtSupplierClassList = supplierClass.getCatgoryIds().stream()
                .map(x -> PrdtSupplierClass.builder()
                        .catgoryId(x)
                        .supplierId(supplierId)
                        .sysCode(supplierClass.getSysCode())
                        .build()
                ).collect(Collectors.toList());

        // 处理一级管理分类, 入驻商设定销售分润占比
        if (Objects.nonNull(supplierClass.getSupplierClassRateDTOS())) {
            // 绑定入驻商
            supplierClass.getSupplierClassRateDTOS().forEach(item -> {
                // 分润比例最大29%
                if (Objects.nonNull(item.getSaleTotalRate()) && NumberUtil.isGreater(item.getSaleTotalRate(), new BigDecimal("0.29"))) {
                    throw exception(PRDT_SPU_MAX_PROFIT_RATE);
                }
                item.setSupplierId(supplierId);
                PrdtSupplierClassRate rate = SupplierClassRateConvert.INSTANCE.convertPO(item);
                // 数据库值
                PrdtSupplierClassRate source = prdtSupplierClassRateMapper.selectBySupplierIdAndCatgoryId(supplierId, rate.getCatgoryId());
                if (Objects.nonNull(source)) {
                    // 有历史记录需要判断是否设置为null
                    prdtSupplierClassRateMapper.updatePO(rate);
                } else {
                    // 没有历史记录走新增
                    prdtSupplierClassRateMapper.insert(rate);
                }
            });
        } else {
            prdtSupplierClassRateMapper.deleteBySupplierId(supplierId);
        }
        return prdtSupplierClassMapper.insertBatch(prdtSupplierClassList);
    }

    /**
    * @Description: 根据入驻商ID修改管理类别绑定关系
    * @Author: liuxingyu
    * @Date: 2024/3/6 17:22
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBySupplierId(PrdtSupplierClassRespDTO respDto) {
        Long supplierId = respDto.getSupplierId();
        //通过入驻商ID 删除绑定关系
        prdtSupplierClassMapper.deleteBySupplierId(supplierId);
        if (ObjectUtil.isEmpty(respDto.getCatgoryIds())){
            return true;
        }
        // 处理一级管理分类, 入驻商设定销售分润占比
        if (Objects.nonNull(respDto.getSupplierClassRateDTOS())) {
            // 绑定入驻商
            respDto.getSupplierClassRateDTOS().forEach(item -> {
                // 分润比例最大29%
                if (Objects.nonNull(item.getSaleTotalRate()) && NumberUtil.isGreater(item.getSaleTotalRate(), new BigDecimal("0.29"))) {
                    throw exception(PRDT_SPU_MAX_PROFIT_RATE);
                }
                item.setSupplierId(supplierId);
                PrdtSupplierClassRate rate = SupplierClassRateConvert.INSTANCE.convertPO(item);
                // 数据库值
                PrdtSupplierClassRate source = prdtSupplierClassRateMapper.selectBySupplierIdAndCatgoryId(supplierId, rate.getCatgoryId());
                if (Objects.nonNull(source)) {
                    // 有历史记录需要判断是否设置为null
                    prdtSupplierClassRateMapper.updatePO(rate);
                } else {
                    // 没有历史记录走新增
                    prdtSupplierClassRateMapper.insert(rate);
                }
            });
        } else {
            prdtSupplierClassRateMapper.deleteBySupplierId(supplierId);
        }
        //重新新增绑定关系
        return insetBath(respDto);
    }

    @Override
    public List<PrdtSupplierClassRateDTO> getCatgoryRateListBySupplierId(Long supplierId) {
        return SupplierClassRateConvert.INSTANCE.convertDTOList(prdtSupplierClassRateMapper.selectBySupplierId(supplierId));
    }

    @Override
    public SupplierClassRateStatusRespVO getSupplierClassRate(Long supplierId, Long catgoryId) {
        // 查询平台商默认配置的比例
        SupplierDTO supplierDTO = productCacheService.getSupplierDTO(supplierId);
//        if(null == supplierDTO){
//            throw exception(SUPPLIER_QUERY_FAIL, supplierId);
//        }
        if (null == supplierDTO) {
            return new SupplierClassRateStatusRespVO(BigDecimal.ZERO, StringPool.ONE);
        }
        PayConfigDTO payConfigDTO = productCacheService.getPayConfigDTO(supplierDTO.getSysCode());
        if (null == payConfigDTO || Objects.isNull(payConfigDTO.getProfitModel())) {
            return new SupplierClassRateStatusRespVO(BigDecimal.ZERO, StringPool.ONE);
        }
        PrdtSupplierClassRate rate = prdtSupplierClassRateMapper.selectBySupplierIdAndCatgoryId(supplierId, catgoryId);
        if (Objects.nonNull(rate) && Objects.nonNull(rate.getSaleTotalRate())) {
            // 设置了入驻商管理分类占比
            return new SupplierClassRateStatusRespVO(rate.getSaleTotalRate(), payConfigDTO.getProfitModel());
        }

        // 管理分类扣点
        PrdtCatgory catgory = catgoryMapper.selectById(catgoryId);
        if (Objects.nonNull(catgory) && Objects.nonNull(catgory.getSaleTotalRate())) {
            // 设置了入驻商管理分类占比
            return new SupplierClassRateStatusRespVO(catgory.getSaleTotalRate(), payConfigDTO.getProfitModel());
        }

        if (StringUtils.isEmpty(payConfigDTO.getDefaultSaleTotalCategoryRate())) {
            // 没有设置默认分成比例0
            return new SupplierClassRateStatusRespVO(BigDecimal.ZERO, payConfigDTO.getProfitModel());
        }
        return new SupplierClassRateStatusRespVO(new BigDecimal(payConfigDTO.getDefaultSaleTotalCategoryRate()), payConfigDTO.getProfitModel());
    }

    private void validatePrdtSupplierClassExists(Long catgoryId) {
        if (prdtSupplierClassMapper.selectById(catgoryId) == null) {
            throw exception(PRDT_SUPPLIER_CLASS_NOT_EXISTS);
        }
    }

    @Override
    public List<PrdtSupplierClass> getSupplierClassAfterConfig(List<Long> supplierIds, List<Long> catgoryIds) {
        return prdtSupplierClassMapper.getSupplierClassAfterConfig(supplierIds, catgoryIds);
    }
}
