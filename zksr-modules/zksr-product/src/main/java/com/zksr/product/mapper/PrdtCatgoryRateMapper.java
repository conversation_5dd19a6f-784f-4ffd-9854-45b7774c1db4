package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRatePageReqVO;
import com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRateRespVO;
import com.zksr.product.domain.PrdtCatgoryRate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 城市级管理分类扣点设置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Mapper
public interface PrdtCatgoryRateMapper extends BaseMapperX<PrdtCatgoryRate> {
    default PageResult<PrdtCatgoryRate> selectPage(PrdtCatgoryRatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtCatgoryRate>()
                .eqIfPresent(PrdtCatgoryRate::getCatgoryRateId, reqVO.getCatgoryRateId())
                .eqIfPresent(PrdtCatgoryRate::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrdtCatgoryRate::getAreaId, reqVO.getAreaId())
                .eqIfPresent(PrdtCatgoryRate::getCatgoryId, reqVO.getCatgoryId())
                .eqIfPresent(PrdtCatgoryRate::getDcRate, reqVO.getDcRate())
                .eqIfPresent(PrdtCatgoryRate::getColonel1Rate, reqVO.getColonel1Rate())
                .eqIfPresent(PrdtCatgoryRate::getColonel2Rate, reqVO.getColonel2Rate())
                .orderByDesc(PrdtCatgoryRate::getCatgoryRateId));
    }

    /**
     * @Description: 运营商获取商品管理分类详情
     * @Author: liuxingyu
     * @Date: 2024/3/15 15:37
     */
    PrdtCatgoryRateRespVO getRateInfo(@Param("areaId") Long areaId, @Param("catgoryId") Long catgoryId);

    /**
     * @Description: 通过参数查询数据
     * @Author: liuxingyu
     * @Date: 2024/3/15 15:52
     */
    default PrdtCatgoryRate getIsSave(Long areaId, Long catgoryId) {
        return selectOne(new LambdaQueryWrapper<PrdtCatgoryRate>()
                .eq(PrdtCatgoryRate::getAreaId, areaId).eq(PrdtCatgoryRate::getCatgoryId, catgoryId));
    }

    /**
    * @Description: 获取设置扣点列表
    * @Author: liuxingyu
    * @Date: 2024/3/19 9:46
    */
    List<PrdtCatgoryRateRespVO> getCatgoryRateList(@Param("respVO") PrdtCatgoryRateRespVO respVO);
}
