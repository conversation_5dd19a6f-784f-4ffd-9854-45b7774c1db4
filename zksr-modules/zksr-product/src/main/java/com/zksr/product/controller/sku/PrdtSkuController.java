package com.zksr.product.controller.sku;

import javax.validation.Valid;
import javax.validation.constraints.Size;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.controller.sku.vo.PrdtSkuSelectedRespVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtSku;
import com.zksr.product.service.IPrdtSkuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.sku.vo.PrdtSkuPageReqVO;
import com.zksr.product.controller.sku.vo.PrdtSkuSaveReqVO;
import com.zksr.product.controller.sku.vo.PrdtSkuRespVO;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 商品SKUController
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Api(tags = "管理后台 - 商品SKU接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/sku")
public class PrdtSkuController {
    @Autowired
    private IPrdtSkuService prdtSkuService;

    /**
     * 新增商品SKU
     */
    @ApiOperation(value = "新增商品SKU", httpMethod = "POST")
    @RequiresPermissions("product:sku:add")
    @Log(title = "商品SKU", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtSkuSaveReqVO createReqVO) {
        return success(prdtSkuService.insertPrdtSku(createReqVO));
    }

    /**
     * 修改商品SKU
     */
    @ApiOperation(value = "修改商品SKU", httpMethod = "PUT")
    @RequiresPermissions("product:sku:edit")
    @Log(title = "商品SKU", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtSkuSaveReqVO updateReqVO) {
        prdtSkuService.updatePrdtSku(updateReqVO);
        return success(true);
    }

    /**
     * 删除商品SKU
     */
    @ApiOperation(value = "删除商品SKU(停用启用)", httpMethod = "GET")
    @RequiresPermissions("product:sku:remove")
    @Log(title = "商品SKU", businessType = BusinessType.DELETE)
    @DeleteMapping("/{skuIds}/{status}")
    public CommonResult<Boolean> remove(@PathVariable Long[] skuIds,@PathVariable Long status) {
        prdtSkuService.deletePrdtSkuBySkuIds(skuIds,status);
        return success(true);
    }

    /**
     * 获取商品SKU详细信息
     */
    @ApiOperation(value = "获得商品SKU详情", httpMethod = "GET")
    @RequiresPermissions("product:sku:query")
    @GetMapping(value = "/{skuId}")
    public CommonResult<PrdtSkuRespVO> getInfo(@PathVariable("skuId") Long skuId) {
        PrdtSku prdtSku = prdtSkuService.getPrdtSku(skuId);
        return success(HutoolBeanUtils.toBean(prdtSku, PrdtSkuRespVO.class));
    }

    /**
     * 分页查询商品SKU
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得商品SKU分页列表", httpMethod = "GET")
    @RequiresPermissions("product:sku:list")
    public CommonResult<PageResult<PrdtSkuRespVO>> getPage(@Valid PrdtSkuPageReqVO pageReqVO) {
        PageResult<PrdtSku> pageResult = prdtSkuService.getPrdtSkuPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, PrdtSkuRespVO.class));
    }

    /**
     * 获取选中数据 (用于选中回显)
     *
     * @param skuIds skuID集合
     * @return 获取SKU选中数据
     */
    @PostMapping("/getSelectedBatchInfo")
    @ApiOperation(value = "批量获取简略信息", httpMethod = "POST")
    public CommonResult<List<PrdtSkuSelectedRespVO>> getSelectedBatchInfo(@Valid @Size(min = NumberPool.INT_ONE) @RequestBody List<Long> skuIds) {
        return success(prdtSkuService.getSelectedPrdtSku(skuIds));
    }

    /**
     * 分页查询商品SKU
     */
    @GetMapping("/getSkuPage")
    @ApiOperation(value = "分页查询商品SKU", httpMethod = "GET")
    @RequiresPermissions("product:sku:list")
    public CommonResult<PageResult<PrdtSkuRespVO>> getSkuPage(@Valid PrdtSkuPageReqVO pageReqVO) {
        PageResult<PrdtSkuRespVO> pageResult = prdtSkuService.selectSkuPage(pageReqVO);
        return success(pageResult);
    }

    /**
     * 同步sku库存
     */
    @PostMapping("/syncSkuStock")
    @ApiOperation(value = "同步sku库存", httpMethod = "POST")
    @RequiresPermissions("product:sku:edit")
    @Log(title = "同步sku库存", businessType = BusinessType.UPDATE)
    public CommonResult<Boolean> syncSkuStock(@Valid @Size(min = NumberPool.INT_ONE, max = NumberPool.INT_NUM200, message = "一次同步数量范围在1-200之间") @RequestBody List<Long> skuIds) {
        prdtSkuService.syncSkuStock(skuIds);
        return success(true);
    }
}
