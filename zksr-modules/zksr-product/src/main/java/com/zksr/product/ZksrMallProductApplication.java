package com.zksr.product;

import com.zksr.common.security.annotation.EnableCustomConfig;
import com.zksr.common.security.annotation.EnableRyFeignClients;
import com.zksr.common.swagger.annotation.EnableCustomSwagger2;
import org.dromara.easyes.starter.register.EsMapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 商品模块
 *
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication(scanBasePackages={"com.zksr.common.elasticsearch.service", "com.zksr.product"})
@EsMapperScan("com.zksr.common.elasticsearch.mapper")
public class ZksrMallProductApplication {
    public static void main(String[] args) {
        SpringApplication.run(ZksrMallProductApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  商品模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                "                                                                                                                                 \n" +
                "                                                                                                                                 \n" +
                "                                                                                                                                 \n" +
                "                                                                                                                                 \n" +
                "                                                                                                                                 \n" +
                "                                                                                                                                 \n" +
                "    ssssssssss   uuuuuu    uuuuuu      cccccccccccccccc    cccccccccccccccc    eeeeeeeeeeee        ssssssssss       ssssssssss   \n" +
                "  ss::::::::::s  u::::u    u::::u    cc:::::::::::::::c  cc:::::::::::::::c  ee::::::::::::ee    ss::::::::::s    ss::::::::::s  \n" +
                "ss:::::::::::::s u::::u    u::::u   c:::::::::::::::::c c:::::::::::::::::c e::::::eeeee:::::eess:::::::::::::s ss:::::::::::::s \n" +
                "s::::::ssss:::::su::::u    u::::u  c:::::::cccccc:::::cc:::::::cccccc:::::ce::::::e     e:::::es::::::ssss:::::ss::::::ssss:::::s\n" +
                " s:::::s  ssssss u::::u    u::::u  c::::::c     cccccccc::::::c     ccccccce:::::::eeeee::::::e s:::::s  ssssss  s:::::s  ssssss \n" +
                "   s::::::s      u::::u    u::::u  c:::::c             c:::::c             e:::::::::::::::::e    s::::::s         s::::::s      \n" +
                "      s::::::s   u::::u    u::::u  c:::::c             c:::::c             e::::::eeeeeeeeeee        s::::::s         s::::::s   \n" +
                "ssssss   s:::::s u:::::uuuu:::::u  c::::::c     cccccccc::::::c     ccccccce:::::::e           ssssss   s:::::s ssssss   s:::::s \n" +
                "s:::::ssss::::::su:::::::::::::::uuc:::::::cccccc:::::cc:::::::cccccc:::::ce::::::::e          s:::::ssss::::::ss:::::ssss::::::s\n" +
                "s::::::::::::::s  u:::::::::::::::u c:::::::::::::::::c c:::::::::::::::::c e::::::::eeeeeeee  s::::::::::::::s s::::::::::::::s \n" +
                " s:::::::::::ss    uu::::::::uu:::u  cc:::::::::::::::c  cc:::::::::::::::c  ee:::::::::::::e   s:::::::::::ss   s:::::::::::ss  \n" +
                "  sssssssssss        uuuuuuuu  uuuu    cccccccccccccccc    cccccccccccccccc    eeeeeeeeeeeeee    sssssssssss      sssssssssss    \n" +
                "                                                                                                                                 \n" +
                "                                                                                                                                 \n" +
                "                                                                                                                                 \n" +
                "                                                                                                                                 \n" +
                "                                                                                                                                 \n" +
                "                                                                                                                                 \n" +
                "                                                                                                                                 ");
    }
}
