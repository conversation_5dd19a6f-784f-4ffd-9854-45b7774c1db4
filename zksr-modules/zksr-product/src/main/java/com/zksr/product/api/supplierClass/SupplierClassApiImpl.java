package com.zksr.product.api.supplierClass;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRateDTO;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRespDTO;
import com.zksr.product.api.supplierClass.dto.SupplierClassRespDTO;
import com.zksr.product.controller.supplierClass.vo.SupplierClassRateStatusRespVO;
import com.zksr.product.convert.supplierClass.SupplierClassConvert;
import com.zksr.product.service.IPrdtSupplierClassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@ApiIgnore
public class SupplierClassApiImpl implements SupplierClassApi{

    @Autowired
    private IPrdtSupplierClassService supplierClassService;

    /**
    * @Description: 通过入驻商ID获取绑定的所有管理分类ID
    * @Param: Long supplierId
    * @return: List<Long>
    * @Author: liuxingyu
    * @Date: 2024/3/6 15:09
    */
    @InnerAuth
    @Override
    public CommonResult<List<Long>> getCatgoryIdListBySupplierId(@RequestParam Long supplierId) {
        return success(supplierClassService.getCatgoryIdListBySupplierId(supplierId));
    }

    /**
     * @Description: 新增入驻商绑定管理类别
     * @Param: PrdtSupplierClassRespDto prdtSupplierClassRespDto
     * @return: CommonResult<Integer>
     * @Author: liuxingyu
     * @Date: 2024/3/6 16:28
     */
    @InnerAuth
    @Override
    public CommonResult<Boolean> insetBath(PrdtSupplierClassRespDTO prdtSupplierClassRespDto) {
        return success(supplierClassService.insetBath(prdtSupplierClassRespDto));
    }

    /**
    * @Description: 根据入驻商ID修改管理类别绑定关系
    * @Author: liuxingyu
    * @Date: 2024/3/6 17:18
    */
    @InnerAuth
    @Override
    public CommonResult<Boolean> updateBySupplierId(PrdtSupplierClassRespDTO respDto) {
        return success(supplierClassService.updateBySupplierId(respDto));
    }

    @Override
    public CommonResult<List<PrdtSupplierClassRateDTO>> getCatgoryRateListBySupplierId(Long supplierId) {
        return success(supplierClassService.getCatgoryRateListBySupplierId(supplierId));
    }

    @Override
    public CommonResult<BigDecimal> getSupplierClassRate(Long supplierId, Long catgoryId) {
        SupplierClassRateStatusRespVO supplierClassRate = supplierClassService.getSupplierClassRate(supplierId, catgoryId);
        return success(supplierClassRate.getSaleTotalRate());
    }

    @Override
    public CommonResult<List<SupplierClassRespDTO>> getSupplierClassAfterConfig(List<Long> supplierIds, List<Long> catgoryIds) {
        return success(
                SupplierClassConvert.INSTANCE.convertListRespDTO(
                        supplierClassService.getSupplierClassAfterConfig(supplierIds, catgoryIds)
                )
        );
    }
}
