package com.zksr.product.controller.product;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.elasticsearch.mapper.EsColonelAppBranchFullMapper;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.elasticsearch.service.*;
import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.api.model.EsProductEventType;
import com.zksr.product.api.model.event.EsProductEventBuild;
import com.zksr.product.api.model.event.EsSkuUpdateProductEvent;
import com.zksr.product.api.model.event.EsSpuUpdateProductEvent;
import com.zksr.product.cache.handler.impl.SkuUpdateProductHandler;
import com.zksr.product.cache.handler.impl.SpuUpdateProductHandler;
import com.zksr.product.controller.product.vo.ProductContentPageReqVO;
import com.zksr.product.controller.product.vo.ProductContentRespVO;
import com.zksr.product.service.IProductContentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 产品内容服务提供
 * @date 2024/3/2 15:52
 */
@Api(tags = "商品服务 - 产品内容提供", produces = "application/json")
@Validated
@RestController
@RequestMapping("/productContent")
public class ProductContentController {

    @Autowired
    private IProductContentService productContentService;

    @Autowired
    private SpuUpdateProductHandler spuUpdateProductHandler;

    @Autowired
    private EsProductService esProductService;

    /**
     * 获取平台品牌详细信息
     */
    @ApiOperation(value = "获取产品列表支持搜索", httpMethod = HttpMethod.POST, notes = "本地商品 || 全国商品 || 商品搜素 || 分类搜索 || 入驻商搜索 || 排序")
    @PostMapping(value = "/getList")
    public CommonResult<List<ProductContentRespVO>> getList(@RequestBody ProductContentPageReqVO pageReqVo) {
        return success(productContentService.getElasticSearchList(pageReqVo));
    }


    /**
     * 初始化商品
     */
    @ApiOperation(value = "初始化商品", httpMethod = HttpMethod.GET)
    @GetMapping(value = "/initProduct")
    public CommonResult<Boolean> getList() {
        esProductService.initIndex();
        EsProductEvent<EsSpuUpdateProductEvent> event = new EsProductEvent<>(EsProductEventType.SKU_UPDATE, new EsSpuUpdateProductEvent());
        spuUpdateProductHandler.execEvent(event);
        return success(true);
    }

    /**
     * 初始化商品02
     */
    @ApiOperation(value = "初始化商品02", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ROOT)
    @GetMapping(value = "/initProduct02")
    @RequiresPermissions(Permissions.ROOT)
    public CommonResult<Boolean> initProduct02(Integer initIndex) {
        productContentService.refreshProduct(initIndex);
        return success(true);
    }

    @Resource
    private EsColonelAppBranchFullMapper colonelAppBranchFullMapper;

    /**
     * 初始化ES门店
     */
    @ApiOperation(value = "初始化ES门店", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ROOT)
    @RequiresPermissions(Permissions.ROOT)
    @GetMapping(value = "/init3")
    public CommonResult<Boolean> init3() {
        try {colonelAppBranchFullMapper.deleteIndex("colonel_app_branch");} catch (Exception e) {}
        colonelAppBranchFullMapper.createIndex();
        return success(true);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 只允许超级管理员操作 */
        public static final String ROOT = "prdt:product-context:root";
    }
}
