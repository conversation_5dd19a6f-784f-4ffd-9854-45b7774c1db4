package com.zksr.product.controller.platform;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.controller.platform.vo.PrdtPlatformSkuPageReqVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSkuRespVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSkuSaveReqVO;
import com.zksr.product.convert.platform.PrdtPlatformSkuConvert;
import com.zksr.product.domain.PrdtPlatformSku;
import com.zksr.product.service.IPrdtPlatformSkuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台商品库-商品SKUController
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Api(tags = "管理后台 - 平台商品库-商品SKU接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/platform/sku")
public class PrdtPlatformSkuController {
    @Autowired
    private IPrdtPlatformSkuService prdtPlatformSkuService;

    /**
     * 新增平台商品库-商品SKU
     */
    @ApiOperation(value = "新增平台商品库-商品SKU", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "平台商品库-商品SKU", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtPlatformSkuSaveReqVO createReqVO) {
        return success(prdtPlatformSkuService.insertPrdtPlatformSku(createReqVO));
    }

    /**
     * 修改平台商品库-商品SKU
     */
    @ApiOperation(value = "修改平台商品库-商品SKU", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "平台商品库-商品SKU", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtPlatformSkuSaveReqVO updateReqVO) {
            prdtPlatformSkuService.updatePrdtPlatformSku(updateReqVO);
        return success(true);
    }

    /**
     * 删除平台商品库-商品SKU
     */
    @ApiOperation(value = "删除平台商品库-商品SKU", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "平台商品库-商品SKU", businessType = BusinessType.DELETE)
    @DeleteMapping("/{platformSkuIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] platformSkuIds) {
        prdtPlatformSkuService.deletePrdtPlatformSkuByPlatformSkuIds(platformSkuIds);
        return success(true);
    }

    /**
     * 获取平台商品库-商品SKU详细信息
     */
    @ApiOperation(value = "获得平台商品库-商品SKU详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{platformSkuId}")
    public CommonResult<PrdtPlatformSkuRespVO> getInfo(@PathVariable("platformSkuId") Long platformSkuId) {
        PrdtPlatformSku prdtPlatformSku = prdtPlatformSkuService.getPrdtPlatformSku(platformSkuId);
        return success(PrdtPlatformSkuConvert.INSTANCE.convert(prdtPlatformSku));
    }

    /**
     * 分页查询平台商品库-商品SKU
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台商品库-商品SKU分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtPlatformSkuRespVO>> getPage(@Valid PrdtPlatformSkuPageReqVO pageReqVO) {
        PageResult<PrdtPlatformSku> pageResult = prdtPlatformSkuService.getPrdtPlatformSkuPage(pageReqVO);
        return success(PrdtPlatformSkuConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:platform-sku:add";
        /** 编辑 */
        public static final String EDIT = "product:platform-sku:edit";
        /** 删除 */
        public static final String DELETE = "product:platform-sku:remove";
        /** 列表 */
        public static final String LIST = "product:platform-sku:list";
        /** 查询 */
        public static final String GET = "product:platform-sku:query";
        /** 停用 */
        public static final String DISABLE = "product:platform-sku:disable";
        /** 启用 */
        public static final String ENABLE = "product:platform-sku:enable";
    }
}
