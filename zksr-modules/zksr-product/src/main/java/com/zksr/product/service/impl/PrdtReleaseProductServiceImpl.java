package com.zksr.product.service.impl;

import com.github.pagehelper.Page;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.utils.DcUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.controller.spu.vo.PrdtReleaseProductReqVO;
import com.zksr.product.controller.spu.vo.PrdtReleaseProductRespVO;
import com.zksr.product.mapper.PrdtReleaseProductMapper;
import com.zksr.product.service.IPrdtActivityService;
import com.zksr.product.service.IPrdtReleaseProductService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.PageParam.PAGE_SIZE_NONE;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/4 8:36
 */
@Service
public class PrdtReleaseProductServiceImpl implements IPrdtReleaseProductService {

    @Autowired
    private PrdtReleaseProductMapper releaseProductMapper;

    @Autowired
    private IProductCacheService productCacheService;

    @Autowired
    private IPrdtActivityService activityService;

    @Override
    public PageResult<PrdtReleaseProductRespVO> getProductList(PrdtReleaseProductReqVO reqVO) {
        // 运营商数据隔离
        if (ProductType.LOCAL.getType().equals(reqVO.getProductType())) {
            // 本地使用运营区域隔离
            reqVO.setAreaIdList(DcUtils.getAreaList(SecurityUtils.getDcId()));
        } else {
            // 全国使用入驻商隔离
            reqVO.setSupplierList(DcUtils.getSupplierList(SecurityUtils.getDcId()));
        }
        // 加载促销范围限制
        activityService.loadActivityScopeByItemPage(reqVO);
        // 是否开启分页
        Page<PrdtReleaseProductRespVO> page = null;
        if (!PAGE_SIZE_NONE.equals(reqVO.getPageSize())) {
            page = PageUtils.startPage(reqVO);
        }
        // 获取数据
        List<PrdtReleaseProductRespVO> list;
        if (reqVO.getItemType() == NumberPool.INT_ZERO) {
            list = releaseProductMapper.selectType0(reqVO);
        } else {
            list = releaseProductMapper.selectType1(reqVO);
        }
        // 简单渲染操作
        for (PrdtReleaseProductRespVO productRespVO : list) {
            // 入驻商名称
            SupplierDTO supplierDTO = productCacheService.getSupplierDTO(productRespVO.getSupplierId());
            productRespVO.setSupplierName(Objects.nonNull(supplierDTO) ? supplierDTO.getSupplierName() : null);

            // 设置城市名称
            AreaDTO area = productCacheService.getAreaDto(productRespVO.getAreaId());
            productRespVO.setAreaName(Objects.nonNull(area) ? area.getAreaName() : null);
        }
        if (Objects.isNull(page)) {
            return PageResult.result((long) list.size(), list);
        }
        return PageResult.result(page, list);
    }
}
