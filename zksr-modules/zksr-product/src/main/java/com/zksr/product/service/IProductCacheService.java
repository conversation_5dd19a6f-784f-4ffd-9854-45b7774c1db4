package com.zksr.product.service;

import com.zksr.common.core.enums.ProductType;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.skuPrice.dto.SkuPriceDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import com.zksr.promotion.api.activity.dto.PrmActivityDTO;
import com.zksr.promotion.api.activity.dto.SkRuleDTO;
import com.zksr.promotion.api.activity.dto.SpRuleDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.dto.AppletAgreementPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.BasicSettingPolicyDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;

import java.util.List;

public interface IProductCacheService {

    public void setWxSessionKey(String key, String sessionKey);

    public String getWxSessionKey(String key);

    public PartnerDto getPartnerDto(String key);

    public BranchDTO getBranchDto(Long branchId);

    public ChannelDTO getChannelDto(Long channelId);

    public AreaDTO getAreaDto(Long areaId);

    public SupplierDTO getSupplierDTO(Long supplierId);

    public BasicSettingPolicyDTO getBasicSettingPolicyDTO(Long dcId);

    /**
    * @Description: 获取平台展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/26 20:29
    */
    List<SaleClassDTO> getSaleClassListBySysCode(Long sysCode);


    public AreaItemDTO getAreaItemDTO(Long areaItemId);

    public SupplierItemDTO getSupplierItemDTO(Long supplierItemId);

    public SpuDTO getSpuDTO(Long spuId);

    public SkuDTO getSkuDTO(Long skuId);

    /**
    * @Description: 获取门店绑定的分类
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:36
    */
    List<AreaClassDTO> getAreaClassBranch(Long branchId);

    /**
     * 获取管理分类分润比例
     * @param catgoryId 管理分类ID
     * @param areaId    区域城市ID
     * @return
     */
    CatgoryRateDTO getCatgoryByIdAndAreaId(Long catgoryId, Long areaId);

    public AppletBaseConfigDTO getAppletBaseConfigDTO(Long sysCode);

    public Integer getAreaSalePriceCodeCache(String key);

    public Integer getSupplierSalePriceCodeCache(String key);

    public SkuPriceDTO getSkuPriceDTOByAreaTypeCache(String key);

    /**
     * @Description: 获取业务员信息
     * @Author: chenmingqing
     * @Date: 2024/3/28 10:12
     */
    ColonelDTO getColonel(Long colonelId);

    public BrandDTO getBrandDTO(Long brandId);

    public CatgoryDTO getCatgoryDTO(Long catgoryId);

    /**
     * 重新设置 商品详情 spu 绑定的 所有 sku 商品
     * @param spuId
     * @param areaId
     * @param productType
     */
    void setSkuUnitGroup(Long spuId, Long areaId, Long classId, ProductType productType);

    /**
     * 移除 商品详情 spu 绑定的 所有 sku 商品
     * @param spuId
     * @param areaId
     * @param productType
     */
    void removeSkuUnitGroup(Long spuId, Long areaId, Long classId, ProductType productType);

    VisualSettingMasterDto getVisualMasterBySupplierId(Long supplierId);

    /**
     * 获取平台商支付配置
     * @param sysCode   平台Code
     * @return
     */
    PayConfigDTO getPayConfigDTO(Long sysCode);

    /**
     * 根据入驻商ID查询出商户的开放资源信息
     *
     * @param merchantId
     * @return
     */
    OpensourceDto getOpensourceByMerchantId(Long merchantId);

    /**
     * @param dcId    运营商ID
     * @return   运营商缓存对象
     */
    public DcDTO getDcDTO(Long dcId);

    /**
     * @param sysCode   平台商ID
     * @return  平台商小程序配置
     */
    public AppletAgreementPolicyDTO getAppletAgreementPolicyDTO(Long sysCode);

    /**
     * 获取促销活动信息
     * @param activityId
     * @return
     */
    PrmActivityDTO getActivityDTO(Long activityId);

    /**
     * 获取组合商品缓存
     */
    SpuCombineDTO getSpuCombineDTO(Long spuCombineId);

    /**
     * 获取促销活动spu
     * @param activityId
     * @return
     */
    List<ActivitySpuScopeDTO> getActivitySpuScopeList(Long activityId);

    /**
     * 获取促销活动 秒杀活动规则
     * @param activityId
     * @return
     */
    List<SkRuleDTO> getActivitySkRuleList(Long activityId);

    /**
     * 获取促销活动 特价活动规则
     * @param activityId
     * @return
     */
    List<SpRuleDTO> getActivitySpRuleList(Long activityId);
}
