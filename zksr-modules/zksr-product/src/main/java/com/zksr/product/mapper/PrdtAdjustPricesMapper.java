package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtAdjustPrices;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesPageReqVO;

import java.util.Date;
import java.util.List;


/**
 * 商品调价单主Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Mapper
public interface PrdtAdjustPricesMapper extends BaseMapperX<PrdtAdjustPrices> {
    default PageResult<PrdtAdjustPrices> selectPage(PrdtAdjustPricesPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtAdjustPrices>()
                    .eqIfPresent(PrdtAdjustPrices::getAdjustPricesId, reqVO.getAdjustPricesId())
                    .eqIfPresent(PrdtAdjustPrices::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtAdjustPrices::getDcId, reqVO.getDcId())
                    .eqIfPresent(PrdtAdjustPrices::getSupplierId, reqVO.getSupplierId())
                    .eqIfPresent(PrdtAdjustPrices::getAdjustPricesNo, reqVO.getAdjustPricesNo())
                    .eqIfPresent(PrdtAdjustPrices::getApproveState, reqVO.getApproveState())
                    .eqIfPresent(PrdtAdjustPrices::getApproveUserId, reqVO.getApproveUserId())
                    .eqIfPresent(PrdtAdjustPrices::getApproveBy, reqVO.getApproveBy())
                    .eqIfPresent(PrdtAdjustPrices::getApproveTime, reqVO.getApproveTime())
                    .eqIfPresent(PrdtAdjustPrices::getValidType, reqVO.getValidType())
                    .eqIfPresent(PrdtAdjustPrices::getValidTime, reqVO.getValidTime())
                    .eqIfPresent(PrdtAdjustPrices::getSkuNum, reqVO.getSkuNum())
                    .eqIfPresent(PrdtAdjustPrices::getMemo, reqVO.getMemo())
                .orderByDesc(PrdtAdjustPrices::getAdjustPricesId));
    }

    /**
     * 分页查询
     * @param reqVO
     * @return
     */
    List<PrdtAdjustPrices> selectListPage(PrdtAdjustPricesPageReqVO reqVO);

    /**
     * 更新调价单主表的sku条数
     * @param adjustPricesId
     * @param skuNum
     */
    default void updateSkuNum(Long adjustPricesId,Long skuNum){
        update(null,new LambdaUpdateWrapper<PrdtAdjustPrices>()
                .set(PrdtAdjustPrices::getSkuNum,skuNum)
                .eq(PrdtAdjustPrices::getAdjustPricesId,adjustPricesId));

    }

    /**
     * 按条件查询列表 -- 状态、时间 用于定时任务查询或状态、时间查询
     * @param sysCode
     * @param validType
     * @param validTime
     * @param taskExecuteStatus
     * @param approveState
     * @return
     */
    default List<PrdtAdjustPrices> selectListByOperateValid(Long sysCode, Long validType, Date validTime,Integer taskExecuteStatus,Long approveState){
        return selectList(new LambdaQueryWrapperX<PrdtAdjustPrices>()
                .eqIfPresent(PrdtAdjustPrices::getSysCode,sysCode)
                .eqIfPresent(PrdtAdjustPrices::getValidType,validType)
                .eqIfPresent(PrdtAdjustPrices::getTaskExecuteStatus,taskExecuteStatus)
                .leIfPresent(PrdtAdjustPrices::getValidTime,validTime)
                .eqIfPresent(PrdtAdjustPrices::getApproveState,approveState)
        );
    }
}
