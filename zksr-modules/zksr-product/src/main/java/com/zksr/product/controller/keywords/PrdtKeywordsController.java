package com.zksr.product.controller.keywords;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtKeywords;
import com.zksr.product.service.IPrdtKeywordsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.keywords.vo.PrdtKeywordsPageReqVO;
import com.zksr.product.controller.keywords.vo.PrdtKeywordsSaveReqVO;
import com.zksr.product.controller.keywords.vo.PrdtKeywordsRespVO;
import com.zksr.product.convert.keywords.PrdtKeywordsConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 搜索关键词词库Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Api(tags = "管理后台 - 搜索关键词词库接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/keywords")
public class PrdtKeywordsController {
    @Autowired
    private IPrdtKeywordsService prdtKeywordsService;

    /**
     * 新增搜索关键词词库
     */
    @ApiOperation(value = "新增搜索关键词词库", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    //@RequiresPermissions(Permissions.ADD)
    @Log(title = "搜索关键词词库", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtKeywordsSaveReqVO createReqVO) {
        return success(prdtKeywordsService.insertPrdtKeywords(createReqVO));
    }

    /**
     * 修改搜索关键词词库
     */
    @ApiOperation(value = "修改搜索关键词词库", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "搜索关键词词库", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtKeywordsSaveReqVO updateReqVO) {
            prdtKeywordsService.updatePrdtKeywords(updateReqVO);
        return success(true);
    }

    /**
     * 删除搜索关键词词库
     */
    @ApiOperation(value = "删除搜索关键词词库", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    //@RequiresPermissions(Permissions.DELETE)
    @Log(title = "搜索关键词词库", businessType = BusinessType.DELETE)
    @PostMapping("/{keywordsIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] keywordsIds) {
        prdtKeywordsService.deletePrdtKeywordsByKeywordsIds(keywordsIds);
        return success(true);
    }

    /**
     * 获取搜索关键词词库详细信息
     */
    @ApiOperation(value = "获得搜索关键词词库详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{keywordsId}")
    public CommonResult<PrdtKeywordsRespVO> getInfo(@PathVariable("keywordsId") Long keywordsId) {
        PrdtKeywords prdtKeywords = prdtKeywordsService.getPrdtKeywords(keywordsId);
        return success(PrdtKeywordsConvert.INSTANCE.convert(prdtKeywords));
    }

    /**
     * 分页查询搜索关键词词库
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得搜索关键词词库分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtKeywordsRespVO>> getPage(@Valid PrdtKeywordsPageReqVO pageReqVO) {
        return success(prdtKeywordsService.getPrdtKeywordsPage(pageReqVO));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:keywords:add";
        /** 编辑 */
        public static final String EDIT = "product:keywords:edit";
        /** 删除 */
        public static final String DELETE = "product:keywords:remove";
        /** 列表 */
        public static final String LIST = "product:keywords:list";
        /** 查询 */
        public static final String GET = "product:keywords:query";
        /** 停用 */
        public static final String DISABLE = "product:keywords:disable";
        /** 启用 */
        public static final String ENABLE = "product:keywords:enable";
    }
}
