package com.zksr.product.service;

import com.zksr.common.core.domain.vo.openapi.receive.SpuReceiveVO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.sku.vo.PrdtUpdateSkuStockReqVO;
import com.zksr.product.api.spu.dto.*;
import com.zksr.product.api.spu.excel.ProductImportExcel;
import com.zksr.product.api.spu.vo.*;
import com.zksr.product.controller.spu.dto.PrdtSpuUpdateRespDTO;
import com.zksr.product.controller.spu.vo.*;
import com.zksr.product.domain.PrdtAreaItem;
import com.zksr.product.domain.PrdtSpu;
import com.zksr.product.domain.PrdtSupplierItem;
import com.zksr.product.domain.excel.ProductBrandImportUpdateExcel;
import com.zksr.product.domain.excel.ProductCatgoryImportUpdateExcel;
import com.zksr.product.domain.excel.ProductExport;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品SPUService接口
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
public interface IPrdtSpuService {

    /**
     * 新增商品SPU
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtSpu(@Valid PrdtSpuGroupSaveInVO createReqVO);

    /**
     * 新增商品SPU
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtSpuOpen(@Valid PrdtSpuGroupSaveInVO createReqVO);

    /**
     * 修改商品SPU
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public PrdtSpuUpdateRespDTO updatePrdtSpu(@Valid PrdtSpuGroupSaveInVO updateReqVO);

    /**
     * 修改商品SPU
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public PrdtSpuUpdateRespDTO updatePrdtSpuOpen(@Valid PrdtSpuGroupSaveInVO updateReqVO);

    /**
     * 删除商品SPU
     *  @param spuId
     */
    public void deletePrdtSpu(Long spuId);

    /**
     * 批量删除商品SPU
     *
     * @param spuIds 需要删除的商品SPU主键集合
     * @return 结果
     */
    public void deletePrdtSpuBySpuIds(Long[] spuIds);

    /**
     * 停用启用 修改状态
     *
     * @param spuIds 需要修改状态的商品SPU主键集合
     * @param status
     * @return 结果
     */
    public void updateStatus(Long[] spuIds,Long status);


    /**
     * 获得商品SPU
     *
     * @param spuId 商品SPU_id
     * @return 商品SPU
     */
    public PrdtSpuGroupInVO getPrdtSpu(Long spuId);

    /**
     * 获得商品SPU分页
     *
     * @param pageReqVO 分页查询
     * @return 商品SPU分页
     */
    PageResult<PrdtSpuPageReqVO> getPrdtSpuPage(PrdtSpuPageReqVO pageReqVO);

    /**
     * 获得上下架管理分页查询未上架商品SPU列表
     *
     * @param pageReqVO 分页查询
     * @return 商品SPU分页
     */
    PageResult<PrdtSpuNotItemPageReqVo> getPrdtSpuPageByNotItem(PrdtSpuNotItemPageReqVo pageReqVO);

    List<PrdtSpuNotItemPageReqExportVo> getPrdtSpuPageByNotItemExport(PrdtSpuNotItemPageReqVo pageReqVO);


    /**
     * 获得商品SPU
     *
     * @param spuId 商品SPU_id
     * @return 商品SPU
     */
    public PrdtSpu getBySpuId(Long spuId);

    /**
     * 批量获取SPU信息 (展示用于多选数据回显)
     * @param spuIds    spuId 集合
     * @return  集合
     */
    List<PrdtSpuSelectedRespVO> getSelectedPrdtSpu(List<Long> spuIds);

    /**
    * @Description: 获取商品信息
    * @Author: liuxingyu
    * @Date: 2024/4/11 16:48
    */
    ProductDTO getByProduct(String param);

    /**
    * @Description: 获取产品下拉
    * @Author: liuxingyu
    * @Date: 2024/4/12 9:10
    */
    PageResult<PrdtProductRespVO> getProductDropdown(PrdtProductPageReqVO pageReqVO);

    /**
     * 导入基础商品数据
     * @param productList   商品数据
     * @return
     */
    String importBaseProduct(List<ProductImportExcel> productList);

    FileImportHandlerVo importBaseProductEvent(List<ProductImportExcel> productList, Long supplierId, Long sysCode, Long fileImportId,Integer seq);

    /**
     * 批量修改商品品牌
     * @param productList   商品数据
     * @return
     */
    String importUpdateBrandData(List<ProductBrandImportUpdateExcel> productList);

    /**
     * 批量修改商品类别
     * @param productList   商品数据
     * @return
     */
    String importUpdateCategoryData(List<ProductCatgoryImportUpdateExcel> productList);

    /**
     * 根据商品编号查询商品信息
     * @param spuNo
     * @return
     */
    PrdtSpu getBySpuNo(Long spuNo);
    /**
     * 根据商品编号查询商品信息
     * @param spuductOpenDTO
     * @return
     */
    Boolean updatePrdData(SpuductOpenDTO spuductOpenDTO);

    /**
     * 获取上架商品数据
     * @param spuId
     * @param productType
     * @return
     */
    List<SkuUnitGroupDTO> getSkuUnitGroupList(Long spuId, Long areaId, Long classId, String productType);

    /**
     * 获取商品SPU数据唯一列表
     * @param pageReqVO
     * @return
     */
    PageResult<PrdtSpuUniquePageRespVO> getPrdtSpuListPage(PrdtSpuPageReqVO pageReqVO);

    /**
     * 开启商品共享
     * @param updateReqVO
     */
    void shareEnable(PrdtSpuShareReqVO updateReqVO);

    /**
     * 关闭商品共享
     * @param updateReqVO
     */
    void shareDisable(PrdtSpuShareReqVO updateReqVO);

    /**
     * 更新商品基本信息
     * @param prdtSpuSaveReqVO
     */
    PrdtSpuUpdateRespDTO updateSpuBase(PrdtSpuSaveReqVO prdtSpuSaveReqVO);

    /**
     * 一键复制商品数据
     * @param prdtSpuSaveReqVO
     * @return  SPU ID
     */
    Long copySpu(PrdtSpuSaveReqVO prdtSpuSaveReqVO);
    /**
     * 新增或更新商品规格
     *
     * @param spuReceiveVO@return
     * @return
     */
    List<PrdtSpuUpdateRespDTO>  addOrUpdateSpu(SpuReceiveVO spuReceiveVO);

    /**
     * 更新SPU后续操作
     * @param prdtSpuUpdateRespDTO
     */
    void updatePrdtSpuAfter(PrdtSpuUpdateRespDTO prdtSpuUpdateRespDTO);

    /**
     * 导出选择商品基本信息数据
     * @param prdtExportSpuVO
     * @return
     */
    List<ProductExport> getExportProducts(PrdtExportSpuVO prdtExportSpuVO);

    /**
     * 编辑商品库存数量
     * @param skuStockReqVO 编辑数据
     * @return 最新stock
     */
    BigDecimal editStock(PrdtUpdateSkuStockReqVO skuStockReqVO);


    /**
     * 批量上传图片
     * @param file
     * @return
     */
    String importProductImages(MultipartFile file) throws IOException;

    /**
     * 获得商品SPU导出列表
     * @param vo
     * @return
     */
    List<SpuExportDTO> getSpuExportList(PrdtSpuPageReqVO vo);

    Map<Long, SpuDTO> listBySpuIds(List<Long> spuIds);

    String batchShelf(PrdtBatchShelfVO batchShelfVO);

    List<PrdtAreaItem> batchDownShelf(PrdtBatchShelfVO batchShelfVO);

    List<PrdtAreaItem> updateShelfStatus(Long spuId, Integer minShelfStatus, Integer midShelfStatus, Integer largeShelfStatus);

    String batchSupplierShelf(PrdtBatchShelfVO batchShelfVO);

    List<PrdtSupplierItem> batchSupplierDownShelf(PrdtBatchShelfVO batchShelfVO);

    List<PrdtSupplierItem> updateSupplierShelfStatus(Long skuId, Integer minShelfStatus, Integer midShelfStatus, Integer largeShelfStatus);

    String chefkBatchDownShelf(List<Long> shelfSpuList);
}
