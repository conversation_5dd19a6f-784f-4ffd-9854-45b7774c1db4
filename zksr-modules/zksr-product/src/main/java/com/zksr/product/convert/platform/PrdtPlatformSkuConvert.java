package com.zksr.product.convert.platform;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.platform.excel.ProductPlarformImportExcel;
import com.zksr.product.domain.PrdtPlatformSku;
import com.zksr.product.controller.platform.vo.PrdtPlatformSkuRespVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSkuSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.product.domain.PrdtSku;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 平台商品库-商品SKU 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-06-21
*/
@Mapper
public interface PrdtPlatformSkuConvert {

    PrdtPlatformSkuConvert INSTANCE = Mappers.getMapper(PrdtPlatformSkuConvert.class);

    PrdtPlatformSkuRespVO convert(PrdtPlatformSku prdtPlatformSku);

    PrdtPlatformSku convert(PrdtPlatformSkuSaveReqVO prdtPlatformSkuSaveReq);

    PageResult<PrdtPlatformSkuRespVO> convertPage(PageResult<PrdtPlatformSku> prdtPlatformSkuPage);

    PrdtPlatformSku convertParterSku(PrdtSku prdtSku);

    List<PrdtPlatformSkuRespVO> convert(List<PrdtPlatformSku> skus);

    @Mappings({
            @Mapping(source = "itemData.barcode", target = "barcode"),
            @Mapping(source = "itemData.midBarcode", target = "midBarcode"),
            @Mapping(source = "itemData.largeBarcode", target = "largeBarcode"),
    })
    @BeanMapping(ignoreByDefault = true)
    void convertImport(@MappingTarget PrdtPlatformSku prdtPlatformSku, ProductPlarformImportExcel itemData);
}