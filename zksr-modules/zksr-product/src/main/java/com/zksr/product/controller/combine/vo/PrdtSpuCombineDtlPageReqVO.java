package com.zksr.product.controller.combine.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 组合商品详情对象 prdt_spu_combine_dtl
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@ApiModel("组合商品详情 - prdt_spu_combine_dtl分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtSpuCombineDtlPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 组合商品ID */
    @Excel(name = "组合商品ID")
    @ApiModelProperty(value = "组合商品ID")
    private Long spuCombineId;

    /** 城市上架商品id */
    @Excel(name = "城市上架商品id")
    @ApiModelProperty(value = "城市上架商品id")
    private Long areaItemId;

    /** 全国上架商品id */
    @Excel(name = "全国上架商品id")
    @ApiModelProperty(value = "全国上架商品id")
    private Long supplierItemId;

    /** skuID */
    @Excel(name = "skuID")
    @ApiModelProperty(value = "skuID")
    private Long skuId;

    /** 商品单位大小 */
    @Excel(name = "商品单位大小")
    @ApiModelProperty(value = "商品单位大小")
    private Long skuUnitType;

    /** 数量 */
    @Excel(name = "数量")
    @ApiModelProperty(value = "数量")
    private Long qty;

    /** 是否为赠品 */
    @Excel(name = "是否为赠品")
    @ApiModelProperty(value = "是否为赠品")
    private Integer giftFlag;


}
