package com.zksr.product.controller.platform.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 平台商品库-商品SPU对象 prdt_platform_spu
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Data
@ApiModel("平台商品库-商品SPU - prdt_platform_spu分页 Request VO")
public class PrdtPlatformSpuSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 商品SPU id */
    @ApiModelProperty(value = "被复制次数")
    private Long platformSpuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private String supplierId;

    /** 分类名;一级分类&gt;二级分类&gt;三级分类 */
    @Excel(name = "分类名;一级分类&gt;二级分类&gt;三级分类")
    @ApiModelProperty(value = "分类名;一级分类&gt;二级分类&gt;三级分类")
    private String catgoryName;

    /** 品牌名 */
    @Excel(name = "品牌名")
    @ApiModelProperty(value = "品牌名")
    private String brandName;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    @ApiModelProperty(value = "商品SPU编号", required = true)
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面图")
    private String thumb;

    /** 封面视频（url） */
    @Excel(name = "封面视频", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面视频")
    private String thumbVideo;

    /** 详情页轮播（json） */
    @Excel(name = "详情页轮播", readConverterExp = "j=son")
    @ApiModelProperty(value = "详情页轮播")
    private String images;

    /** 详情信息(富文本) */
    @Excel(name = "详情信息(富文本)")
    @ApiModelProperty(value = "详情信息(富文本)")
    private String details;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否")
    private Integer isDelete;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Integer status;

    /** 商品规格 */
    @Excel(name = "商品规格")
    @ApiModelProperty(value = "商品规格")
    private String specName;

    /** 是否开启多规格 1-是 0-否 */
    @Excel(name = "是否开启多规格 1-是 0-否")
    @ApiModelProperty(value = "是否开启多规格 1-是 0-否")
    private Integer isSpecs;

    /** 最小单位-单品单位 数据字典（sys_prdt_unit） */
    @Excel(name = "最小单位-单品单位 数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "最小单位-单品单位 数据字典")
    private String minUnit;

    /** 中单位 数据字典（sys_prdt_unit） */
    @Excel(name = "中单位 数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "中单位 数据字典")
    private String midUnit;

    /** 中单位换算数量（换算成最小单位） */
    @Excel(name = "中单位换算数量", readConverterExp = "换=算成最小单位")
    @ApiModelProperty(value = "中单位换算数量")
    private Integer midSize;

    /** 大单位 数据字典（sys_prdt_unit） */
    @Excel(name = "大单位 数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "大单位 数据字典")
    private String largeUnit;

    /** 大单位换算数量（换算成最小单位） */
    @Excel(name = "大单位换算数量", readConverterExp = "换=算成最小单位")
    @ApiModelProperty(value = "大单位换算数量")
    private Integer largeSize;

    /** 平台商spu_id */
    @Excel(name = "平台商spu_id")
    @ApiModelProperty(value = "平台商spu_id")
    private Long spuId;

    /** 被复制次数 */
    @Excel(name = "被复制次数")
    @ApiModelProperty(value = "被复制次数")
    private Integer copyTimes;

}
