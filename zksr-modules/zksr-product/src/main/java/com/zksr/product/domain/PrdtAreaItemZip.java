package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 城市上架商品拉链表 prdt_area_item_zip
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@TableName(value = "prdt_area_item_zip")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PrdtAreaItemZip extends BaseEntity{
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long areaItemZipId;

    @Excel(name = "平台商ID")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    @Excel(name = "入驻商id")
    private Long supplierId;

    @Excel(name = "商品SPU id")
    private Long spuId;

    @Excel(name = "商品sku id")
    private Long skuId;

    @Excel(name = "城市id")
    private Long areaId;

    @Excel(name = "开始日期")
    private Date startDate;

    @Excel(name = "结束日期")
    private Date endDate;
}
