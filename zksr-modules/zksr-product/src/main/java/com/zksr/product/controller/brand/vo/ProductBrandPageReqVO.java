package com.zksr.product.controller.brand.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 平台品牌对象 prdt_brand
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
@ApiModel("平台品牌 - prdt_brand分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductBrandPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商品牌id */
    @ApiModelProperty(value = "0 正常, 1 停用")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long brandId;
    /** 品牌编号 */
    @Excel(name = "品牌编号")
    @ApiModelProperty(value = "品牌编号")
    private String brandNo;

    /**  商品品牌名称 */
    @Excel(name = " 商品品牌名称")
    @ApiModelProperty(value = " 商品品牌名称", required = true,   example = "示例值")
    private String brandName;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 0 正常, 1 停用 */
    @Excel(name = "0 正常, 1 停用")
    @ApiModelProperty(value = "品牌状态, 0 正常, 1 停用")
    private String status;

    /** 是否过滤已绑定品牌商, true 不显示已绑定, false 显示已绑定 */
    @ApiModelProperty(value = "是否过滤已绑定品牌商, true 不显示已绑定, false 显示已绑定")
    private Boolean ignoreHashMerchant;

    @Excel(name = "品牌商ID")
    private Long brandMerchantId;
}
