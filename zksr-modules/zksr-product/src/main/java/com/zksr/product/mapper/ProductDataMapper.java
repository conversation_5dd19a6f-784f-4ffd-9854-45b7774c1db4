package com.zksr.product.mapper;

import com.zksr.common.elasticsearch.domain.EsGlobalProduct;
import com.zksr.common.elasticsearch.domain.EsLocalProduct;
import com.zksr.common.elasticsearch.domain.EsProductGroup;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.domain.dto.EsCbProductLoadReqDTO;
import com.zksr.product.domain.dto.EsProductLoadReqDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品数据收集
 * @date 2024/3/1 8:35
 */
@Mapper
public interface ProductDataMapper {

    List<EsLocalProduct> selectByLocal(@Param("req") EsProductLoadReqDTO event);

    List<EsGlobalProduct> selectByGlobal(@Param("req") EsProductLoadReqDTO build);

    List<EsProductGroup> selectCbProductByLocal(@Param("req") EsCbProductLoadReqDTO req);

    List<EsProductGroup> selectCbProductByGlobal(@Param("req") EsCbProductLoadReqDTO req);

}
