package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.controller.combine.vo.PrdtSpuCombinePageRespVO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineRespVO;
import com.zksr.product.domain.PrdtSpuCombine;
import com.zksr.product.controller.combine.vo.PrdtSpuCombinePageReqVO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineSaveReqVO;

/**
 * 组合商品Service接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface IPrdtSpuCombineService {

    /**
     * 新增组合商品
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtSpuCombine(@Valid PrdtSpuCombineSaveReqVO createReqVO);

    /**
     * 修改组合商品
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtSpuCombine(@Valid PrdtSpuCombineSaveReqVO updateReqVO);

    /**
     * 删除组合商品
     *
     * @param spuCombineId 组合商品id
     */
    public void deletePrdtSpuCombine(Long spuCombineId);

    /**
     * 批量删除组合商品
     *
     * @param spuCombineIds 需要删除的组合商品主键集合
     * @return 结果
     */
    public void deletePrdtSpuCombineBySpuCombineIds(Long[] spuCombineIds);

    /**
     * 获得组合商品
     *
     * @param spuCombineId 组合商品id
     * @return 组合商品
     */
    public PrdtSpuCombineRespVO getPrdtSpuCombine(Long spuCombineId);

    /**
     * 获得组合商品分页
     *
     * @param pageReqVO 分页查询
     * @return 组合商品分页
     */
    PageResult<PrdtSpuCombinePageRespVO> getPrdtSpuCombinePage(PrdtSpuCombinePageReqVO pageReqVO);

    /**
     * 停用启用组合商品
     *
     * @param spuCombineId,status
     * @return
     */
    void editSpuCombineStatus(Long spuCombineId, Integer status);

}
