package com.zksr.product.convert.share;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtProductShare;
import com.zksr.product.controller.share.vo.PrdtProductShareRespVO;
import com.zksr.product.controller.share.vo.PrdtProductShareSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 商品分享 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-06
*/
@Mapper
public interface PrdtProductShareConvert {

    PrdtProductShareConvert INSTANCE = Mappers.getMapper(PrdtProductShareConvert.class);

    PrdtProductShareRespVO convert(PrdtProductShare prdtProductShare);

    PrdtProductShare convert(PrdtProductShareSaveReqVO prdtProductShareSaveReq);

    PageResult<PrdtProductShareRespVO> convertPage(PageResult<PrdtProductShare> prdtProductSharePage);
}