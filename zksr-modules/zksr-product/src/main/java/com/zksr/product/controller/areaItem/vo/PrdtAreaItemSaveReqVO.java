package com.zksr.product.controller.areaItem.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 城市上架商品对象 prdt_area_item
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Data
@ApiModel("城市上架商品 - prdt_area_item分页 Request VO")
@NoArgsConstructor
@AllArgsConstructor
public class PrdtAreaItemSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 城市上架商品id */
    @ApiModelProperty(value = "城市上架商品id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaItemId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 城市展示分类id */
    @Excel(name = "城市展示分类id")
    @ApiModelProperty(value = "城市展示分类id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaClassId;

    /** 上下架状态 */
    @Excel(name = "上下架状态")
    @ApiModelProperty(value = "上下架状态")
    private Integer shelfStatus;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    @ApiModelProperty(value = "商品SPU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    @ApiModelProperty(value = "商品sku id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 排序序号 */
    @Excel(name = "排序序号")
    @ApiModelProperty(value = "排序序号", example = "示例值")
    private Integer sortNum;

    /** 上架时间 */
    @Excel(name = "上架时间")
    @ApiModelProperty(value = "上架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelfDate; 		 // 上架时间

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 修改商品标识 */
    @Excel(name = "修改商品标识")
    @ApiModelProperty(value = "修改商品标识 0 修改排序 1 修改类别", example = "示例值")
    private Integer updateFlag;

    /** 小单位-上下架状态 */
    @Excel(name = "小单位-上下架状态")
    @ApiModelProperty(value = "小单位-上下架状态", example = "示例值")
    private Integer minShelfStatus;

    /** 中单位-上下架状态 */
    @Excel(name = "中单位-上下架状态")
    @ApiModelProperty(value = "中单位-上下架状态", example = "示例值")
    private Integer midShelfStatus;

    /** 大单位-上下架状态 */
    @Excel(name = "大单位-上下架状态")
    @ApiModelProperty(value = "大单位-上下架状态", example = "示例值")
    private Integer largeShelfStatus;


    /** 城市上架商品id集合 */
    @ApiModelProperty(value = "城市上架商品id集合")
    @JsonSerialize(using = CustomLongSerialize.class)
    private List<Long> areaItemIds;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 是否自动上下架标记1自动 */
    @ApiModelProperty(value = "是否自动上下架标记1自动")
    private Integer autoFlag;



    public PrdtAreaItemSaveReqVO(Long areaClassId,Long supplierId, Long spuId, Long skuId) {
        this.areaClassId = areaClassId;
        this.spuId = spuId;
        this.skuId = skuId;
        this.supplierId = supplierId;
    }


}
