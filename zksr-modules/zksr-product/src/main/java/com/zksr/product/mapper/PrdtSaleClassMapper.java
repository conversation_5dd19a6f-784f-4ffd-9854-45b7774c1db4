package com.zksr.product.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.api.saleClass.dto.SaleClassExportVo;
import com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo;
import com.zksr.product.controller.catgory.dto.PrdtCatgoryCopyRespDTO;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassPageReqVO;
import com.zksr.product.domain.PrdtAreaClass;
import com.zksr.product.domain.PrdtCatgory;
import com.zksr.product.domain.PrdtSaleClass;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import static com.zksr.common.core.pool.StringPool.LIMIT_ONE;


/**
 * 平台商展示分类Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface PrdtSaleClassMapper extends BaseMapperX<PrdtSaleClass> {
    default PageResult<PrdtSaleClass> selectPage(PrdtSaleClassPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtSaleClass>()
                .eqIfPresent(PrdtSaleClass::getSaleClassId, reqVO.getSaleClassId())
                .eqIfPresent(PrdtSaleClass::getSysCode, reqVO.getSysCode())
                .likeIfPresent(PrdtSaleClass::getName, reqVO.getName())
                .eqIfPresent(PrdtSaleClass::getPid, reqVO.getPid())
                .eqIfPresent(PrdtSaleClass::getIcon, reqVO.getIcon())
                .eqIfPresent(PrdtSaleClass::getMemo, reqVO.getMemo())
                .eqIfPresent(PrdtSaleClass::getSort, reqVO.getSort())
                .eqIfPresent(PrdtSaleClass::getStatus, reqVO.getStatus())
                .eqIfPresent(PrdtSaleClass::getGroupId, reqVO.getGroupId())
                .eq(PrdtSaleClass::getDelFlag, StringPool.ZERO)
                .orderByAsc(PrdtSaleClass::getLevel)
                .orderByAsc(PrdtSaleClass::getSort)
                .orderByAsc(PrdtSaleClass::getSaleClassId)
        );
    }

    /**
     * @Description: 获取上级目录列表(只有会三级目录, 只查询一二级)
     * @Author: liuxingyu
     * @Date: 2024/2/29 15:49
     */
    List<PrdtSaleClass> selectParentDirectoryList();

    /**
     * @Description: 校验名称是否唯一
     * @Author: liuxingyu
     * @Date: 2024/3/5 10:58
     */
    default Long selectNamecount(String name, Long pid, Long id) {
        return selectCount(new LambdaQueryWrapper<PrdtSaleClass>()
                .eq(PrdtSaleClass::getName, name)
                .eq(PrdtSaleClass::getPid, pid)
                .eq(PrdtSaleClass::getDelFlag, StringPool.ZERO)
                .ne(ObjectUtil.isNotNull(id), PrdtSaleClass::getSaleClassId, id));
    }

    /**
     * @Description: 获取平台商展示分类集合
     * @Author: liuxingyu
     * @Date: 2024/3/19 18:23
     */
    default List<PrdtSaleClass> getSaleClassList(PrdtSaleClassPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapper<PrdtSaleClass>()
                .ne(ObjectUtil.isNotNull(reqVO.getSaleClassId()), PrdtSaleClass::getSaleClassId, reqVO.getSaleClassId())
                .eq(ObjectUtil.isNotNull(reqVO.getLevel()), PrdtSaleClass::getLevel, reqVO.getLevel())
                .eq(ObjectUtil.isNotNull(reqVO.getStatus()), PrdtSaleClass::getStatus, reqVO.getStatus())
                .eq(PrdtSaleClass::getDelFlag, StringPool.ZERO)
        );
    }

    /**
     * @Description: 获取展示分类列表
     * @Author: liuxingyu
     * @Date: 2024/3/26 20:31
     */
    default List<PrdtSaleClass> getSaleClassListBySysCode(Long sysCode) {
        return selectList(
                new LambdaQueryWrapper<PrdtSaleClass>()
                        .eq(PrdtSaleClass::getSysCode, sysCode)
                        .eq(PrdtSaleClass::getStatus, NumberPool.INT_ONE)
                        .eq(PrdtSaleClass::getDelFlag, StringPool.ZERO)
                        .orderByAsc(PrdtSaleClass::getSort)
        );
    }

    default List<PrdtSaleClass> selectSelectedSaleClass(List<Long> saleClassIds) {
        return selectList(
                new LambdaQueryWrapper<PrdtSaleClass>()
                        .in(PrdtSaleClass::getSaleClassId, saleClassIds)
        );
    }

    /**
     * @Description: 平台管理分类同步平台展示分类
     * @Author: liuxingyu
     * @Date: 2024/5/24 9:27
     */
    void copyToSaleClass(@Param("groupId") Long groupId);

    /**
     * @Description: 通过父id获取平台展示分类集合
     * @Author: liuxingyu
     * @Date: 2024/6/6 15:50
     */
    default List<PrdtSaleClass> getListByPid(Long saleClassId) {
        return selectList(new LambdaQueryWrapper<PrdtSaleClass>().eq(PrdtSaleClass::getPid, saleClassId));
    }

    /**
     * @Description: 通过id集合获取平台展示分类集合
     * @Author: liuxingyu
     * @Date: 2024/6/6 15:54
     */
    default List<PrdtSaleClass> selectByPIds(List<Long> ids) {
        return selectList(new LambdaQueryWrapper<PrdtSaleClass>().in(PrdtSaleClass::getPid, ids));
    }

    /**
     * 根据城市管理分组ID获取该分组不存在的平台展示分类
     * @param groupId
     * @return
     */
    List<PrdtCatgoryCopyRespDTO> getSaleClassNotExistByGroupId(@Param("groupId") Long groupId);

    /**
     * 根据分类名 和 级别 获取分类信息
     * @param saleClassName
     * @param level
     * @return
     */
    default PrdtSaleClass selectByNameAndLevel(String saleClassName, Integer level) {
        return selectOne(new LambdaQueryWrapper<PrdtSaleClass>()
                .eq(PrdtSaleClass::getName, saleClassName)
                .eq(PrdtSaleClass::getLevel, level));
    }

    /**
     * 根据平台商SysCode获取展示分类总数
     * @param sysCode
     * @return
     */
    default Long getSaleClassBySysCode(Long sysCode) {
        return selectCount(new LambdaQueryWrapper<PrdtSaleClass>()
                .eq(PrdtSaleClass::getSysCode, sysCode)
                .eq(PrdtSaleClass::getDelFlag, StringPool.ZERO)
        );
    }

    default void setGroupIdNull(Long saleClassId) {
        update(
                PrdtSaleClass.builder().saleClassId(saleClassId).build(),
                new UpdateWrapper<PrdtSaleClass>()
                        .lambda()
                        .eq(PrdtSaleClass::getSaleClassId, saleClassId)
                        .set(PrdtSaleClass::getGroupId, null)
        );
    }

    default Long selectCountByPid(Long saleClassId) {
        return selectCount(new LambdaQueryWrapper<PrdtSaleClass>()
                .eq(PrdtSaleClass::getPid, saleClassId)
                .eq(PrdtSaleClass::getDelFlag, StringPool.ZERO)
        );
    }
    PrdtSaleClass checkTertiaryCategoryUniquenessInSecondaryCategory(@Param("tertiaryCategoryName") String tertiaryCategoryName,@Param("secondaryCategoryId")  Long secondaryCategoryId);

    default PrdtSaleClass getSaleClassByNameAndLevelAndParentId(String categoryName, int level, Long pid){
        LambdaQueryWrapper<PrdtSaleClass> queryWrapper = new LambdaQueryWrapper<PrdtSaleClass>()
                .eq(PrdtSaleClass::getName, categoryName)
                .eq(PrdtSaleClass::getDelFlag, StringPool.ZERO)
                .eq(PrdtSaleClass::getLevel, level);

        if (pid != null) {
            queryWrapper.eq(PrdtSaleClass::getPid, pid);
        }
        return selectOne(queryWrapper.last(LIMIT_ONE));
    }

    default List<SaleClassExportVo> selectSaleClassExportList(SaleClassExportVo reqVO){
        PageResult<PrdtSaleClass> prdtSaleClassPageResult = selectPage(reqVO, new LambdaQueryWrapperX<PrdtSaleClass>()
                .eqIfPresent(PrdtSaleClass::getSaleClassId, reqVO.getSaleClassId())
                .eqIfPresent(PrdtSaleClass::getSysCode, reqVO.getSysCode())
                .likeIfPresent(PrdtSaleClass::getName, reqVO.getName())
                .eqIfPresent(PrdtSaleClass::getPid, reqVO.getPid())
                .eqIfPresent(PrdtSaleClass::getIcon, reqVO.getIcon())
                .eqIfPresent(PrdtSaleClass::getSort, reqVO.getSort())
                .eqIfPresent(PrdtSaleClass::getStatus, reqVO.getStatus())
                .eq(PrdtSaleClass::getDelFlag, StringPool.ZERO)
                .orderByAsc(PrdtSaleClass::getLevel)
                .orderByAsc(PrdtSaleClass::getSort)
                .orderByAsc(PrdtSaleClass::getSaleClassId)
        );
        return HutoolBeanUtils.toBean(prdtSaleClassPageResult, SaleClassExportVo.class).getList();
    }
}
