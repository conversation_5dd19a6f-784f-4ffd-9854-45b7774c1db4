package com.zksr.product.convert.materialApply;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.materialApply.dto.MaterialApplyDTO;
import com.zksr.product.controller.materialApply.vo.PrdtMaterialApplyEchoReqVO;
import com.zksr.product.domain.PrdtMaterial;
import com.zksr.product.domain.PrdtMaterialApply;
import com.zksr.product.controller.materialApply.vo.PrdtMaterialApplyRespVO;
import com.zksr.product.controller.materialApply.vo.PrdtMaterialApplySaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 素材应用 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-01-09
*/
@Mapper
public interface PrdtMaterialApplyConvert {

    PrdtMaterialApplyConvert INSTANCE = Mappers.getMapper(PrdtMaterialApplyConvert.class);

    PrdtMaterialApplyRespVO convert(PrdtMaterialApply prdtMaterialApply);

    PrdtMaterialApply convert(PrdtMaterialApplySaveReqVO prdtMaterialApplySaveReq);

    PageResult<PrdtMaterialApplyRespVO> convertPage(PageResult<PrdtMaterialApply> prdtMaterialApplyPage);

    PrdtMaterialApplyRespVO convert(MaterialApplyDTO dto);

    List<PrdtMaterialApplySaveReqVO> convert(List<PrdtMaterialApply> prdtMaterialApply);

    PrdtMaterialApplySaveReqVO convert(PrdtMaterialApplyEchoReqVO reqVO);


}