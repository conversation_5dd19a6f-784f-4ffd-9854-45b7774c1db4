package com.zksr.product.api.saleClass;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.saleClass.dto.SaleClassExportVo;
import com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo;
import com.zksr.product.service.IPrdtSaleClassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@ApiIgnore
public class SaleClassApiImpl implements SaleClassApi {

    @Autowired
    private IPrdtSaleClassService saleClassService;

    /**
     * @Description: 根据平台商展示分类Id获取平台商展示分类
     * @Param: Long saleClassId
     * @return: CommonResult<SaleClassDto>
     * @Author: liuxingyu
     * @Date: 2024/3/25 15:02
     */
    @Override
    @InnerAuth
    public CommonResult<SaleClassDTO> getSaleClassBySaleClassId(Long saleClassId) {
        return CommonResult.success(HutoolBeanUtils.toBean(saleClassService.getSaleClassBySaleClassId(saleClassId), SaleClassDTO.class));
    }

    /**
     * @Description: 获取平台展示分类列表
     * @Param: Long sysCode
     * @return: List<SaleClassDTO>
     * @Author: liuxingyu
     * @Date: 2024/3/26 20:13
     */
    @Override
    @InnerAuth
    public CommonResult<List<SaleClassDTO>> getSaleClassListBySysCode(Long sysCode) {
        return CommonResult.success(HutoolBeanUtils.toBean(saleClassService.getSaleClassListBySysCode(sysCode), SaleClassDTO.class));
    }

    @Override
    public CommonResult<List<SaleClassExportVo>> getSaleClassExportList(SaleClassExportVo pageVo) {
        return success(saleClassService.getSaleClassExportList(pageVo));
    }
}
