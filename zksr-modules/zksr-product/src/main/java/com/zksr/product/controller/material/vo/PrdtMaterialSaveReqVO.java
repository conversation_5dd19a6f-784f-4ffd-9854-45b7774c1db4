package com.zksr.product.controller.material.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 素材对象 prdt_material
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@ApiModel("素材 - prdt_material分页 Request VO")
public class PrdtMaterialSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 素材id */
    @ApiModelProperty(value = "素材id")
    private Long materialId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 素材名称 */
    @Excel(name = "素材名称")
    @ApiModelProperty(value = "素材名称", required = true)
    private String name;

    /** 素材图片地址 */
    @Excel(name = "素材图片地址")
    @ApiModelProperty(value = "素材图片地址", required = true)
    private String img;

    /** 素材图片大小 */
    @Excel(name = "素材图片大小")
    @ApiModelProperty(value = "素材图片大小")
    private String imgSize;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Integer status;

}
