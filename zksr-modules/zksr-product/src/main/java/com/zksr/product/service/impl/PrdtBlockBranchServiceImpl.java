package com.zksr.product.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.product.controller.blockScheme.excel.BlockBranchExcelToListVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockBranchRespVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtBlockBranchMapper;
import com.zksr.product.convert.blockScheme.PrdtBlockBranchConvert;
import com.zksr.product.domain.PrdtBlockBranch;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockBranchPageReqVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockBranchSaveReqVO;
import com.zksr.product.service.IPrdtBlockBranchService;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 经营屏蔽客户Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Service
public class PrdtBlockBranchServiceImpl implements IPrdtBlockBranchService {
    @Autowired
    private PrdtBlockBranchMapper prdtBlockBranchMapper;
    @Autowired
    private BranchApi branchApi;

    /**
     * 新增经营屏蔽客户
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtBlockBranch(PrdtBlockBranchSaveReqVO createReqVO) {
        // 插入
        PrdtBlockBranch prdtBlockBranch = PrdtBlockBranchConvert.INSTANCE.convert(createReqVO);
        prdtBlockBranchMapper.insert(prdtBlockBranch);
        // 返回
        return prdtBlockBranch.getBlockBranchId();
    }

    /**
     * 修改经营屏蔽客户
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtBlockBranch(PrdtBlockBranchSaveReqVO updateReqVO) {
        prdtBlockBranchMapper.updateById(PrdtBlockBranchConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除经营屏蔽客户
     *
     * @param blockBranchId 主键
     */
    @Override
    public void deletePrdtBlockBranch(Long blockBranchId) {
        // 删除
        prdtBlockBranchMapper.deleteById(blockBranchId);
    }

    /**
     * 批量删除经营屏蔽客户
     *
     * @param blockBranchIds 需要删除的经营屏蔽客户主键
     * @return 结果
     */
    @Override
    public void deletePrdtBlockBranchByBlockBranchIds(Long[] blockBranchIds) {
        for(Long blockBranchId : blockBranchIds){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deletePrdtBlockBranch(blockBranchId);
        }
    }

    /**
     * 获得经营屏蔽客户
     *
     * @param blockBranchId 主键
     * @return 经营屏蔽客户
     */
    @Override
    public PrdtBlockBranch getPrdtBlockBranch(Long blockBranchId) {
        return prdtBlockBranchMapper.selectById(blockBranchId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtBlockBranch> getPrdtBlockBranchPage(PrdtBlockBranchPageReqVO pageReqVO) {
        return prdtBlockBranchMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PrdtBlockBranchRespVO> excelToList(List<BlockBranchExcelToListVO> branchList, String schemeNo, Long areaId) {
        if (CollectionUtils.isEmpty(branchList)) {
            throw new ServiceException("导入的Excel没有数据");
        }
        boolean anyBranchIdBlank = branchList.stream().anyMatch(branch -> StringUtils.isBlank(branch.getBranchId()));
        if (anyBranchIdBlank) {
            throw new ServiceException("客户编码不能为空");
        }
        List<Long> branchIds = branchList.stream().map(BlockBranchExcelToListVO::getBranchId)
                // String转Long
                .map(branchIdStr -> {
                    try {
                        return Long.parseLong(branchIdStr);
                    } catch (Exception e) {
                        throw new ServiceException("客户编码有误:" + branchIdStr);
                    }
                }).collect(Collectors.toList());

        // 校验是否重复
        branchIds.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .forEach((branchId, count) -> {
                    if (count > 1) {
                        throw new ServiceException("表格内客户编码重复:" + branchId);
                    }
                });

        if (StringUtils.isNotBlank(schemeNo)) {
            List<PrdtBlockBranch> existedBlockBranches = prdtBlockBranchMapper.selectList(Wrappers.lambdaQuery(PrdtBlockBranch.class)
                    .eq(PrdtBlockBranch::getSchemeNo, schemeNo)
                    .in(PrdtBlockBranch::getBranchId, branchIds));
            existedBlockBranches.stream().findFirst().ifPresent(existedBlockBranch -> {
                throw new ServiceException(String.format("方案内客户%s已存在，不能重复导入", existedBlockBranch.getBranchId()));
            });
        }

        Map<Long, BranchDTO> branchMap = branchApi.listByBranchIds(branchIds).getCheckedData();
        return branchIds.stream().map(branchId -> {
            BranchDTO branchDTO = branchMap.get(branchId);
            if (branchDTO == null || !StatusConstants.STATE_ENABLE.equals(branchDTO.getStatus())) {
                throw new ServiceException("客户编码有误:" + branchId);
            }
            if (areaId != null && !areaId.equals(branchDTO.getAreaId())) {
                throw new ServiceException(String.format("客户%s不属于选中城市", branchId));
            }
            return PrdtBlockBranchConvert.INSTANCE.convert(branchDTO);
        }).collect(Collectors.toList());
    }

    private void validatePrdtBlockBranchExists(Long blockBranchId) {
        if (prdtBlockBranchMapper.selectById(blockBranchId) == null) {
            throw exception(PRDT_BLOCK_BRANCH_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.product.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 经营屏蔽客户 TODO 补充编号 ==========
    // ErrorCode PRDT_BLOCK_BRANCH_NOT_EXISTS = new ErrorCode(TODO 补充编号, "经营屏蔽客户不存在");


}
