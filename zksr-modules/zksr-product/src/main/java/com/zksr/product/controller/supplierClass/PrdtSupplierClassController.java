package com.zksr.product.controller.supplierClass;

import javax.validation.Valid;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.supplierClass.dto.SupplierClassRespDTO;
import com.zksr.product.controller.supplierClass.vo.PrdtSupplierClassBatchEditReqVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.service.IPrdtSupplierClassService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.supplierClass.vo.PrdtSupplierClassPageReqVO;
import com.zksr.product.controller.supplierClass.vo.PrdtSupplierClassSaveReqVO;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 入驻商-平台商管理分类 关联关系Controller
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Api(tags = "管理后台 - 入驻商-平台商管理分类 关联关系接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/supplierClass")
public class PrdtSupplierClassController {
    @Autowired
    private IPrdtSupplierClassService prdtSupplierClassService;

    /**
     * 新增入驻商-平台商管理分类 关联关系
     */
    @ApiOperation(value = "新增入驻商-平台商管理分类 关联关系", httpMethod = "POST", notes = "product:supplierClass:add", hidden = true)
    @RequiresPermissions("product:supplierClass:add")
    @Log(title = "入驻商-平台商管理分类 关联关系", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtSupplierClassSaveReqVO createReqVO) {
        return success(prdtSupplierClassService.insertPrdtSupplierClass(createReqVO));
    }

    /**
     * 修改入驻商-平台商管理分类 关联关系
     */
    @ApiOperation(value = "修改入驻商-平台商管理分类 关联关系", httpMethod = "PUT", notes = Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "入驻商-平台商管理分类 关联关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtSupplierClassBatchEditReqVO updateReqVO) {
        prdtSupplierClassService.updatePrdtSupplierClass(updateReqVO);
        return success(true);
    }

    /**
     * 删除入驻商-平台商管理分类 关联关系
     */
    @ApiOperation(value = "删除入驻商-平台商管理分类 关联关系", httpMethod = "GET", notes = "product:supplierClass:remove", hidden = true)
    @RequiresPermissions("product:supplierClass:remove")
    @Log(title = "入驻商-平台商管理分类 关联关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{catgoryIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] catgoryIds) {
        prdtSupplierClassService.deletePrdtSupplierClassByCatgoryIds(catgoryIds);
        return success(true);
    }

    /**
     * 获取入驻商-平台商管理分类 关联关系详细信息
     */
    @ApiOperation(value = "获得入驻商-平台商管理分类 关联关系详情", httpMethod = "GET", notes = Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{catgoryId}")
    public CommonResult<SupplierClassRespDTO> getInfo(@PathVariable("catgoryId") Long catgoryId) {
        return success(prdtSupplierClassService.getPrdtSupplierClass(catgoryId));
    }

    /**
     * 分页查询入驻商-平台商管理分类 关联关系
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得入驻商-平台商管理分类 关联关系分页列表", httpMethod = "GET", notes = Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SupplierClassRespDTO>> getPage(@Valid PrdtSupplierClassPageReqVO pageReqVO) {
        return success(prdtSupplierClassService.getPrdtSupplierClassPage(pageReqVO));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 编辑 */
        public static final String EDIT = "product:supplierClass:edit";
        /** 列表 */
        public static final String LIST = "product:supplierClass:list";
        /** 查询 */
        public static final String GET = "product:supplierClass:query";
    }
}
