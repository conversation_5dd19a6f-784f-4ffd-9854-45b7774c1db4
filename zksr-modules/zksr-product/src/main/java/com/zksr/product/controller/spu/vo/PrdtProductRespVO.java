package com.zksr.product.controller.spu.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* @Description: 产品返回实体类
* @Author: liuxingyu
* @Date: 2024/4/12 9:03
*/
@ApiModel("产品返回实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PrdtProductRespVO {

    @ApiModelProperty("spuId")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long spuId;

    @ApiModelProperty("skuId")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;

    @ApiModelProperty("spu编号")
    private String spuNo;

    @ApiModelProperty("spu名称")
    private String spuName;

    @ApiModelProperty("国际条码")
    private String barcode;
}
