package com.zksr.product.convert.cache;

import com.zksr.common.elasticsearch.domain.EsProduct;
import com.zksr.common.elasticsearch.model.dto.ProductSearchDTO;
import com.zksr.product.api.content.dto.ReleaseItemChangeEventDTO;
import com.zksr.product.api.model.event.EsRemoveProductEvent;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/11/8
 * @desc
 */
@Mapper
public interface EsCacheConvert {
    static EsCacheConvert INSTANCE = Mappers.getMapper(EsCacheConvert.class);

    ProductSearchDTO convertSearchDTO(EsRemoveProductEvent data);

    ReleaseItemChangeEventDTO convertReleaseItemChangeEvent(EsProduct esProduct);
}
