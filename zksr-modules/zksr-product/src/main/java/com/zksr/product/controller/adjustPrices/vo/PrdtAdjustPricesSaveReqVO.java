package com.zksr.product.controller.adjustPrices.vo;

import lombok.*;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 商品调价单主对象 prdt_adjust_prices
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Data
@ApiModel("商品调价单主 - prdt_adjust_prices分页 Request VO")
public class PrdtAdjustPricesSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 单据ID */
    @ApiModelProperty(value = "单据ID")
    private Long adjustPricesId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 运营商ID */
    @Excel(name = "运营商ID")
    @ApiModelProperty(value = "运营商ID")
    private Long dcId;

    /** 入驻商ID */
    @Excel(name = "入驻商ID")
    @ApiModelProperty(value = "入驻商ID", required = true)
    private Long supplierId;

    /** 单据编号 */
    @Excel(name = "单据编号")
    @ApiModelProperty(value = "单据编号")
    private String adjustPricesNo;

    /** 审核状态：0-待审核,1-已审核,2-已作废 */
    @Excel(name = "审核状态：0-待审核,1-已审核,2-已作废")
    @ApiModelProperty(value = "审核状态：0-待审核,1-已审核,2-已作废")
    private Long approveState;

    /** 审核人Id */
    @Excel(name = "审核人Id")
    @ApiModelProperty(value = "审核人Id")
    private Long approveUserId;

    /** 审核人 */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人")
    private String approveBy;

    /** 审核时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "审核时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "审核时间")
    private Date approveTime;

    /** 生效类型：0-定时生效，1-立即生效 */
    @Excel(name = "生效类型：0-定时生效，1-立即生效")
    @ApiModelProperty(value = "生效类型：0-定时生效，1-立即生效")
    private Long validType;

    /** 生效时间 */
    @JsonFormat(pattern = YYYY_MM_DD)
    @DateTimeFormat(pattern = YYYY_MM_DD)
    @Excel(name = "生效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "生效时间")
    private Date validTime;

    /** SKU数 */
    @Excel(name = "SKU数")
    @ApiModelProperty(value = "SKU数")
    private Long skuNum;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;


    /** 价格类型选项：标准价：markPrice, 成本价：costPrice，已，号分隔 */
    @Excel(name = "价格类型选项：标准价：markPrice, 成本价：costPrice，已，号分隔")
    @ApiModelProperty(value = "价格类型选项：标准价：markPrice, 成本价：costPrice，已，号分隔")
    private String priceTypeStr;

    @ApiModelProperty(value = "定时调价执行状态 0-未执行，1-已执行")
    private Integer taskExecuteStatus;

    @ApiModelProperty(value = "订单明细集合")
    private List<PrdtAdjustPricesDtlSaveReqVO> dtlList;

}
