package com.zksr.product.controller.spu.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 商品SPU对象 prdt_spu
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Data
@ApiModel("商品SPU - prdt_spu Response VO")
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSpuSelectedRespVO {
    private static final long serialVersionUID = 1L;

    /** 商品SPU_id */
    @ApiModelProperty(value = "spuId")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称", example = "示例值")
    private String spuName;

}
