package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 平台商品库-商品SPU对象 prdt_platform_spu
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@TableName(value = "prdt_platform_spu")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PrdtPlatformSpu extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 商品SPU id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long platformSpuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private String supplierId;

    /** 分类名;一级分类&gt;二级分类&gt;三级分类 */
    @Excel(name = "分类名;一级分类&gt;二级分类&gt;三级分类")
    private String catgoryName;

    /** 品牌名 */
    @Excel(name = "品牌名")
    private String brandName;

    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    private String spuName;

    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    private String thumb;

    /** 封面视频（url） */
    @Excel(name = "封面视频", readConverterExp = "u=rl")
    private String thumbVideo;

    /** 详情页轮播（json） */
    @Excel(name = "详情页轮播", readConverterExp = "j=son")
    private String images;

    /** 详情信息(富文本) */
    @Excel(name = "详情信息(富文本)")
    private String details;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    private Integer isDelete;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    private Integer status;

    /** 商品规格 */
    @Excel(name = "商品规格")
    private String specName;

    /** 是否开启多规格 1-是 0-否 */
    @Excel(name = "是否开启多规格 1-是 0-否")
    private Integer isSpecs;

    /** 最小单位-单品单位 数据字典（sys_prdt_unit） */
    @Excel(name = "最小单位-单品单位 数据字典", readConverterExp = "s=ys_prdt_unit")
    private Integer minUnit;

    /** 中单位 数据字典（sys_prdt_unit） */
    @Excel(name = "中单位 数据字典", readConverterExp = "s=ys_prdt_unit")
    private Integer midUnit;

    /** 中单位换算数量（换算成最小单位） */
    @Excel(name = "中单位换算数量", readConverterExp = "换=算成最小单位")
    private Integer midSize;

    /** 大单位 数据字典（sys_prdt_unit） */
    @Excel(name = "大单位 数据字典", readConverterExp = "s=ys_prdt_unit")
    private Integer largeUnit;

    /** 大单位换算数量（换算成最小单位） */
    @Excel(name = "大单位换算数量", readConverterExp = "换=算成最小单位")
    private Integer largeSize;

    /** 平台商spu_id */
    @Excel(name = "平台商spu_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** 被复制次数 */
    @Excel(name = "被复制次数")
    private Integer copyTimes;

}
