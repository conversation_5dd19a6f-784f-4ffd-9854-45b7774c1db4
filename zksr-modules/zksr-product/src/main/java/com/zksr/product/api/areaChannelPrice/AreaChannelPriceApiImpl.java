package com.zksr.product.api.areaChannelPrice;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.areaChannelPrice.dto.AreaChannelPriceDTO;
import com.zksr.product.domain.PrdtAreaChannelPrice;
import com.zksr.product.service.IPrdtAreaChannelPriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class AreaChannelPriceApiImpl implements AreaChannelPriceApi{

    @Autowired
    private IPrdtAreaChannelPriceService prdtAreaChannelPriceService;

    @Override
    @InnerAuth
    public CommonResult<AreaChannelPriceDTO> getPriceByAreaIdAndChannelId(@RequestParam("areaId") Long areaId, @RequestParam("channelId") Long channelId) {

        return CommonResult.success(HutoolBeanUtils.toBean(prdtAreaChannelPriceService.getPriceByAreaIdAndChannelId(areaId, channelId),AreaChannelPriceDTO.class));
    }

    @Override
    public CommonResult<Integer> getPriceByKey(@RequestParam("key") String key) {
        Integer salePriceCode = null;

        //key为 城市ID 拼接 渠道ID 需要拆开
        String[] split = key.split("-");
        Long areaId = Long.valueOf(split[0]);
        Long channelId = Long.valueOf(split[1]);
        PrdtAreaChannelPrice price = prdtAreaChannelPriceService.getPriceByAreaIdAndChannelId(areaId, channelId);
        if(ToolUtil.isNotEmpty(price)){
            salePriceCode = Math.toIntExact(price.getSalePriceCode());
        }
        return CommonResult.success(salePriceCode);
    }
}
