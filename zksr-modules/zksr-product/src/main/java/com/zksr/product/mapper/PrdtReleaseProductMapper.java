package com.zksr.product.mapper;

import com.zksr.product.controller.spu.vo.PrdtReleaseProductReqVO;
import com.zksr.product.controller.spu.vo.PrdtReleaseProductRespVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2025/3/4
 * @desc
 */
@Mapper
public interface PrdtReleaseProductMapper {

    List<PrdtReleaseProductRespVO> selectType0(@Param("reqVO") PrdtReleaseProductReqVO reqVO);

    List<PrdtReleaseProductRespVO> selectType1(@Param("reqVO") PrdtReleaseProductReqVO reqVO);
}
