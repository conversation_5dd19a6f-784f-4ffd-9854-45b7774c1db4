package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品SPU对象 prdt_spu
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@TableName(value = "prdt_spu")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PrdtSpu extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 商品SPU_id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long spuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 平台商管理分类id */
    @Excel(name = "平台商管理分类id")
    private Long catgoryId;

    /** 平台商品牌id */
    @Excel(name = "平台商品牌id")
    private Long brandId;

    /** 成本价 */
    @Excel(name = "成本价")
    private BigDecimal costPrice;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    private String spuName;

    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    private String thumb;

    /** 封面视频（url） */
    @Excel(name = "封面视频", readConverterExp = "u=rl")
    private String thumbVideo;

    /** 详情页轮播（json） */
    @Excel(name = "详情页轮播", readConverterExp = "j=son")
    private String images;

    /** 详情信息(富文本) */
    @Excel(name = "详情信息(富文本)")
    private String details;

    /** 库存数量 */
    @Excel(name = "库存数量")
    private Long stock;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    private Long isDelete;

    /** 最旧生产日期 */
    @Excel(name = "最旧生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date oldestDate; 		 // 最旧生产日期

    /** 最新生产日期 */
    @Excel(name = "最新生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestDate; 		 // 最新生产日期

    /** 是否开启多规格 1-是 0-否 */
    @Excel(name = "是否开启多规格 1-是 0-否")
    private Long isSpecs;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    private Long status;

    /** 产地 */
    @Excel(name = "产地")
    private String originPlace;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 商品规格 */
    @Excel(name = "商品规格")
    private String specName;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @Excel(name = "最小单位-数据字典", readConverterExp = "sys_prdt_unit")
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    @Excel(name = "中单位-数据字典", readConverterExp = "sys_prdt_unit")
    private Long midUnit;

    /** 中单位换算数量（换算成最小单位）） */
    @Excel(name = "中单位换算数量（换算成最小单位）")
    private BigDecimal midSize;

    /** 大单位-数据字典（sys_prdt_unit） */
    @Excel(name = "大单位-数据字典", readConverterExp = "sys_prdt_unit")
    private Long largeUnit;

    /** 大单位换算数量（换算成最小单位）） */
    @Excel(name = "大单位换算数量（换算成最小单位）")
    private BigDecimal largeSize;

    /** 是否开启联动换算 1-是 0-否 */
    @Excel(name = "是否开启联动换算 1-是 0-否")
    private Integer isLinkage;

    /** 保质期 */
    @Excel(name = "保质期")
    private Integer expirationDate; 		 // 保质期

    /** 外部来源商品编号 */
    @Excel(name = "外部来源商品编号")
    private String sourceNo;

    /** 外部来源商品编号 */
    @Excel(name = "来源（B2B、ERP）")
    private String source;

    /** 共享状态 */
    @Excel(name = "共享状态,0-未共享,1-已共享")
    private Integer shareFlag;

    @Excel(name = "平台商品ID")
    private Long pSpuId;

    /** SPU辅助的商品编号 */
    @Excel(name = "SPU辅助的商品编号")
    private String auxiliarySpuNo;

    /** 关联关键词 */
    @Excel(name = "关联关键词")
    private String keywords;

    /**
     * 商品计价方式类型 (字典：spu_pricing_way) 默认为普通商品 1：普通商品 2：称重商品
     *  应商品新增后 商品计价方式永不能进行调整，故更新SPU时，将pricingWay字段 忽略
     * */
    @Excel(name = "商品计价方式类型")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Integer pricingWay;

    /** 是否零售(0否1是) */
    @Excel(name = "是否零售(0否1是)")
    private Integer enableRetail;

    /** 零售分润模式(1-不分润、2-按比例、3-按金额、4-按平台规则、5-按供货价差额) */
    @Excel(name = "零售分润模式 字典：retail_profit_mode")
    private Integer retailProfitMode;

    /** 是否B2b(0否1是) */
    @Excel(name = "是否B2b(0否1是)")
    private Integer enableWholesale;

    /** B2b分润模式(1-不分润、2-按比例、3-按金额、4-按平台规则、5-按供货价差额) */
    @Excel(name = "B2b分润模式，字典：wholesale_profit_mode")
    private Integer wholesaleProfitMode;

    /** 其他属性 */
    @Excel(name = "其他属性")
    private String otherAttr;
}
