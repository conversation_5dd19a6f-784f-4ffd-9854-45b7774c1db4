package com.zksr.product.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.product.api.brand.BrandApi;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.controller.blockScheme.excel.BlockBranchExcelToListVO;
import com.zksr.product.controller.blockScheme.excel.BlockSkuExcelToListVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSkuRespVO;
import com.zksr.product.convert.blockScheme.PrdtBlockBranchConvert;
import com.zksr.product.domain.PrdtBlockBranch;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtBlockSkuMapper;
import com.zksr.product.convert.blockScheme.PrdtBlockSkuConvert;
import com.zksr.product.domain.PrdtBlockSku;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSkuPageReqVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSkuSaveReqVO;
import com.zksr.product.service.IPrdtBlockSkuService;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 经营屏蔽skuService业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Service
public class PrdtBlockSkuServiceImpl implements IPrdtBlockSkuService {
    @Autowired
    private PrdtBlockSkuMapper prdtBlockSkuMapper;
    @Autowired
    private SkuApi skuApi;
    @Autowired
    private SpuApi spuApi;
    @Autowired
    private BrandApi brandApi;

    /**
     * 新增经营屏蔽sku
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtBlockSku(PrdtBlockSkuSaveReqVO createReqVO) {
        // 插入
        PrdtBlockSku prdtBlockSku = PrdtBlockSkuConvert.INSTANCE.convert(createReqVO);
        prdtBlockSkuMapper.insert(prdtBlockSku);
        // 返回
        return prdtBlockSku.getBlockSkuId();
    }

    /**
     * 修改经营屏蔽sku
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtBlockSku(PrdtBlockSkuSaveReqVO updateReqVO) {
        prdtBlockSkuMapper.updateById(PrdtBlockSkuConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除经营屏蔽sku
     *
     * @param blockSkuId 主键
     */
    @Override
    public void deletePrdtBlockSku(Long blockSkuId) {
        // 删除
        prdtBlockSkuMapper.deleteById(blockSkuId);
    }

    /**
     * 批量删除经营屏蔽sku
     *
     * @param blockSkuIds 需要删除的经营屏蔽sku主键
     * @return 结果
     */
    @Override
    public void deletePrdtBlockSkuByBlockSkuIds(Long[] blockSkuIds) {
        for(Long blockSkuId : blockSkuIds){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deletePrdtBlockSku(blockSkuId);
        }
    }

    /**
     * 获得经营屏蔽sku
     *
     * @param blockSkuId 主键
     * @return 经营屏蔽sku
     */
    @Override
    public PrdtBlockSku getPrdtBlockSku(Long blockSkuId) {
        return prdtBlockSkuMapper.selectById(blockSkuId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtBlockSku> getPrdtBlockSkuPage(PrdtBlockSkuPageReqVO pageReqVO) {
        return prdtBlockSkuMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PrdtBlockSkuRespVO> excelToList(List<BlockSkuExcelToListVO> skuList, String schemeNo) {
        if (CollectionUtils.isEmpty(skuList)) {
            throw new ServiceException("导入的Excel没有数据");
        }
        boolean anySkuIdBlank = skuList.stream().anyMatch(sku -> StringUtils.isBlank(sku.getSkuId()));
        if (anySkuIdBlank) {
            throw new ServiceException("商品编码不能为空");
        }
        List<Long> skuIds = skuList.stream().map(BlockSkuExcelToListVO::getSkuId)
                // String转Long
                .map(skuIdStr -> {
                    try {
                        return Long.parseLong(skuIdStr);
                    } catch (Exception e) {
                        throw new ServiceException("商品编码有误:" + skuIdStr);
                    }
                }).collect(Collectors.toList());

        // 校验是否重复
        skuIds.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .forEach((skuId, count) -> {
                    if (count > 1) {
                        throw new ServiceException("表格内商品编码重复:" + skuId);
                    }
                });

        if (StringUtils.isNotBlank(schemeNo)) {
            List<PrdtBlockSku> existedBlockSkus = prdtBlockSkuMapper.selectList(Wrappers.lambdaQuery(PrdtBlockSku.class)
                    .eq(PrdtBlockSku::getSchemeNo, schemeNo)
                    .in(PrdtBlockSku::getSkuId, skuIds));
            existedBlockSkus.stream().findFirst().ifPresent(existedBlockSku -> {
                throw new ServiceException(String.format("方案内商品%s已存在，不能重复导入", existedBlockSku.getSkuId()));
            });
        }

        Map<Long, SkuDTO> skuMap = skuApi.listBySkuIds(skuIds).getCheckedData();
        List<Long> spuIds = skuMap.values().stream().map(SkuDTO::getSpuId).collect(Collectors.toList());
        Map<Long, SpuDTO> spuMap = spuApi.listBySpuIds(spuIds).getCheckedData();
        List<Long> brandIds = spuMap.values().stream().map(SpuDTO::getBrandId).collect(Collectors.toList());
        Map<Long, BrandDTO> brandMap = brandApi.listByBrandIds(brandIds).getCheckedData();
        return skuIds.stream().map(skuId -> {
            SkuDTO skuDTO = skuMap.get(skuId);
            if (skuDTO == null || (skuDTO.getStatus() != null && !StatusConstants.STATE_ENABLE.equals(skuDTO.getStatus().intValue()))) {
                throw new ServiceException("商品编码有误:" + skuId);
            }
            SpuDTO spuDTO = spuMap.get(skuDTO.getSpuId());
            if (spuDTO == null || (spuDTO.getStatus() != null && !StatusConstants.STATE_ENABLE.equals(spuDTO.getStatus().intValue()))) {
                throw new ServiceException("商品编码有误:" + skuId);
            }
            BrandDTO brandDTO = brandMap.get(spuDTO.getBrandId());
            return PrdtBlockSkuConvert.INSTANCE.convert(skuDTO, spuDTO, brandDTO);
        }).collect(Collectors.toList());
    }

    private void validatePrdtBlockSkuExists(Long blockSkuId) {
        if (prdtBlockSkuMapper.selectById(blockSkuId) == null) {
            throw exception(PRDT_BLOCK_SKU_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.product.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 经营屏蔽sku TODO 补充编号 ==========
    // ErrorCode PRDT_BLOCK_SKU_NOT_EXISTS = new ErrorCode(TODO 补充编号, "经营屏蔽sku不存在");


}
