package com.zksr.product.api.supplierItem;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.product.api.supplierItem.vo.ApiSupplierItemPageReqVO;
import com.zksr.product.api.supplierItem.vo.PrdtSupplierItemPageRespVO;
import com.zksr.product.convert.item.PrdtSupplierItemConvert;
import com.zksr.product.domain.PrdtSupplierItem;
import com.zksr.product.service.IPrdtSkuService;
import com.zksr.product.service.IPrdtSupplierItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.stream.Collectors;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class SupplierItemApiImpl implements SupplierItemApi {

    @Autowired
    private IPrdtSupplierItemService prdtSupplierItemService;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private IPrdtSkuService skuService;

    @Override
    public CommonResult<SupplierItemDTO> getBySupplierItemId(Long supplierItemId) {
        PrdtSupplierItem supplierItem = prdtSupplierItemService.getBySupplierItemId(supplierItemId);
        return CommonResult.success(HutoolBeanUtils.toBean(supplierItem, SupplierItemDTO.class));
    }

    @Override
    public CommonResult<List<SupplierItemDTO>> getSupplierItemListByApi(SupplierItemDTO itemDTO) {
        return CommonResult.success(prdtSupplierItemService.getSupplierItemListByApi(itemDTO));
    }

    @Override
    public CommonResult<PageResult<PrdtSupplierItemPageRespVO>> getSupplierItemPageByApi(ApiSupplierItemPageReqVO supplierItemPageReqVO) {
        PageResult<PrdtSupplierItemPageRespVO> page = prdtSupplierItemService.getPrdtSupplierItemPage(PrdtSupplierItemConvert.INSTANCE.convertPageReq(supplierItemPageReqVO));
        return CommonResult.success(page);
    }

    @Override
    public CommonResult<Long> updateSaleQtyTotal(Long minSupplierItemId) {
        List<PrdtSupplierItem> totalList = prdtSupplierItemService.getSaleQtyTotalList(minSupplierItemId);
        // 更新已售
        skuService.updateSaleQty(totalList.stream().map(PrdtSupplierItem::getSkuId).collect(Collectors.toList()));
        if (totalList.isEmpty()) {
            return CommonResult.success(NumberPool.LOWER_GROUND_LONG);
        }
        return CommonResult.success(totalList.get(totalList.size() - 1).getSupplierItemId());
    }

    @Override
    public CommonResult<SupplierItemDTO> getSupplierItemBySpuCombineId(Long spuCombineId) {
        PrdtSupplierItem supplierItem = prdtSupplierItemService.getSupplierItemBySpuCombineId(spuCombineId);
        return CommonResult.success(HutoolBeanUtils.toBean(supplierItem, SupplierItemDTO.class));
    }

    @Override
    public CommonResult<Boolean> updateSupplierItem(SupplierItemDTO itemDTO) {
        prdtSupplierItemService.updatePrdtSupplierCombineItem(itemDTO);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<SupplierItemDTO> getSupplierItemByActivityId(Long activityId) {
        PrdtSupplierItem supplierItem = prdtSupplierItemService.getSupplierItemByActivityId(activityId);
        return CommonResult.success(HutoolBeanUtils.toBean(supplierItem, SupplierItemDTO.class));
    }

}
