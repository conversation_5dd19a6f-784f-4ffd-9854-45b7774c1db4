package com.zksr.product.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.controller.spu.vo.PrdtReleaseProductReqVO;
import com.zksr.product.controller.spu.vo.PrdtReleaseProductRespVO;

/**
 * <AUTHOR>
 * @time 2025/3/4
 * @desc
 */
public interface IPrdtReleaseProductService {
    PageResult<PrdtReleaseProductRespVO> getProductList(PrdtReleaseProductReqVO reqVO);
}
