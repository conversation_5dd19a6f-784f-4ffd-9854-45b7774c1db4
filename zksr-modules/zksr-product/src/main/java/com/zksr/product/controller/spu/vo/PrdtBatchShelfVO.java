package com.zksr.product.controller.spu.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("商品基本信息批量上架接收实体类")
public class PrdtBatchShelfVO {

    /**
     * 批量上架的spuId集合
     */
    private List<Long> shelfSpuList;

    @ApiModelProperty("小单位上架状态,0-未上架,1-已上架")
    private Integer minShelfStatus;

    @ApiModelProperty("中单位上架状态,0-未上架,1-已上架")
    private Integer midShelfStatus;

    @ApiModelProperty("大单位上架状态,0-未上架,1-已上架")
    private Integer largeShelfStatus;


    /**
     * 上架的城市和区域
     */
    private List<PrdtAreaIdCatgoryId> shelfAreaIdCatgoryIdList;



}
