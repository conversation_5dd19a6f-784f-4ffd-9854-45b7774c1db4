package com.zksr.product.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsProductGroup;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.controller.product.vo.ProductContentPageReqVO;
import com.zksr.product.controller.product.vo.ProductContentRespVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/3/2
 * @desc
 */
public interface IProductContentService {

    /**
     * 搜索商品数据
     * @param pageReqVo
     * @return
     */
    List<ProductContentRespVO> getElasticSearchList(ProductContentPageReqVO pageReqVo);

    /**
     * 搜索城市上下架商品
     * @param pageReqVo
     * @return
     */
    List<ProductContentRespVO> getElasticSearchAreaItemList(ProductContentPageReqVO pageReqVo);

    /**
     * 分页搜索商品数据
     * @param pageReqVo
     * @return
     */
    PageResult<ProductContentRespVO> getPageElasticSearchList(ProductContentPageReqVO pageReqVo);

    /**
     * 获取全国本地全量数据
     * @param bean
     * @return
     */
    List<ProductContentRespVO> getElasticSearchFullItemList(ProductContentPageReqVO bean);

    /**
     * 城市上架的三级展示分类数据
     */
    List<Long> getAreaItemReleaseThreeAreaClassList(Long areaId);

    /**
     * 城市上架的三级管理分类数据
     */
    List<Long> getAreaItemReleaseThreeCategoryList(Long areaId);

    /**
     * 全国上架的三级展示分类数据
     */
    List<Long> getGloablItemReleaseThreeClassList(Long sysCode);

    /**
     * 全国上架的三级展示分类数据
     */
    List<Long> getGloablItemReleaseThreeCategoryList(Long sysCode);


    /**
     * 全量刷新ES, 商品列表数据
     */
    void refreshProduct(Integer initIndex);

    /**
     * 渲染丰富组合促销上商品数据, 黑名单, 默认值等
     */
    void renderSpuCombineList(List<EsProductGroup> cbProductList);

    /**
     * 获取组合促销商品最小库存
     */
    BigDecimal getSpuCombineMinStock(SpuCombineDTO spuCombineDTO);

}
