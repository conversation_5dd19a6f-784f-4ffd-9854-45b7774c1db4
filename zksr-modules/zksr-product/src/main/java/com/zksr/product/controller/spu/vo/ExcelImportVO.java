package com.zksr.product.controller.spu.vo;

import com.zksr.common.core.annotation.Excel;
import lombok.Data;
import lombok.ToString;

import javax.print.attribute.standard.MediaSize;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/6/5 16:52
 */
@Data
@ToString
public class ExcelImportVO {

    @Excel(name = "商品ID")
    private Integer id;

    @Excel(name = "商品名称")
    private String spuName;

    @Excel(name = "商品编码")
    private String spuNo;

    @Excel(name = "商品主图")
    private String image;

    @Excel(name = "商品原价")
    private BigDecimal itemPrePrice;

    @Excel(name = "成本价")
    private BigDecimal costPrice;

    @Excel(name = "销售价")
    private BigDecimal salePrice;

    @Excel(name = "库存")
    private Long stock;

    @Excel(name = "是否多规格")
    private Long specs;

    @Excel(name = "规格组")
    private String propertyNames;

    @Excel(name = "规格单位")
    private String propertyVal;

    @Excel(name = "规格图片")
    private String skuImage;

    @Excel(name = "规格原价")
    private BigDecimal skuPrePrice;

    @Excel(name = "规格价格")
    private BigDecimal skuSalePrice;

    @Excel(name = "规格成本")
    private BigDecimal skuCostPrice;

    @Excel(name = "规格库存")
    private Long skuStock;

    @Excel(name = "规格条码")
    private String skuBarcode;
}
