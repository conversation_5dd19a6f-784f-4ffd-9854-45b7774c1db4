package com.zksr.product.controller.spu.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.product.api.sku.vo.PrdtSkuSpuGroupReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: SPU 唯一分页列表
 * @date 2024/6/21 8:50
 */
@Data
@ApiModel(description = "SPU唯一分页列表")
public class PrdtSpuUniquePageRespVO {

    @ApiModelProperty("spuId")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    @ApiModelProperty("商品编号")
    private String spuNo;

    @ApiModelProperty("商品名称")
    private String spuName;

    @ApiModelProperty("保质期,单位/天")
    private Integer expirationDate;

    @ApiModelProperty("最旧生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date oldestDate; 		 // 最旧生产日期

    @ApiModelProperty("最新生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestDate; 		 // 最新

    @ApiModelProperty("图片")
    private String thumb;

    @ApiModelProperty("三级管理分类")
    private String catgoryName3;

    @ApiModelProperty("二级管理分类")
    private String catgoryName2;

    @ApiModelProperty("一级管理分类")
    private String catgoryName1;

    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("小单位单位字典")
    private String minUnit;

    @ApiModelProperty("中单位字典")
    private String midUnit;

    @ApiModelProperty("大单位字典")
    private String largeUnit;

    @ApiModelProperty("入驻商ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty("入驻商名称")
    private String supplierName;

    @ApiModelProperty("大单位换算数量（换算成最小单位）")
    private Long largeSize;

    @ApiModelProperty("中单位换算数量（换算成最小单位）")
    private Long midSize;

    @ApiModelProperty("状态 1-启用, 2-停用")
    private Long status;

    @ApiModelProperty("共享状态,0-未共享,1-已共享")
    private Integer shareFlag;

    @ApiModelProperty(value = "来源（B2B、ERP）")
    private String source;

    /**
     * 是否为第三方同步商品，true表示同步，false表示不同步
     */
    @ApiModelProperty(value = "是否为第三方同步商品")
    private Boolean isSyncThirdParty;

    /** SPU辅助的商品编号 */
    @Excel(name = "SPU辅助的商品编号")
    @ApiModelProperty(value = "SPU辅助的商品编号")
    private String auxiliarySpuNo;

    /** 外部来源商品编号 */
    @Excel(name = "外部来源商品编号")
    @ApiModelProperty(value = "外部来源商品编号")
    private String sourceNo;

    /** 关联关键词 */
    @Excel(name = "关联关键词")
    @ApiModelProperty(value = "关联关键词")
    private String keywords;

    /** 是否关联 */
    @Excel(name = "是否关联")
    private Boolean isRelation;

    @ApiModelProperty("商品计价方式类型(字典：spu_pricing_way) 默认为普通商品 1：普通商品 2：称重商品")
    private Integer pricingWay;

    /** 详情页轮播 */
    @Excel(name = "详情页轮播")
    @ApiModelProperty(value = "详情页轮播")
    private String images;

    @ApiModelProperty("SKU列表")
    private List<PrdtSkuSpuGroupReqVO> skuList;

    @ApiModelProperty("spu上架城市")
    private String spuAreaName;
}
