package com.zksr.product.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.combine.SpuCombineDtlDTO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombinePageRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtSpuCombine;
import com.zksr.product.controller.combine.vo.PrdtSpuCombinePageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 组合商品Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Mapper
public interface PrdtSpuCombineMapper extends BaseMapperX<PrdtSpuCombine> {
    default PageResult<PrdtSpuCombine> selectPage(PrdtSpuCombinePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtSpuCombine>()
                .eqIfPresent(PrdtSpuCombine::getSpuCombineId, reqVO.getSpuCombineId())
                .eqIfPresent(PrdtSpuCombine::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrdtSpuCombine::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(PrdtSpuCombine::getAreaId, reqVO.getAreaId())
                .eqIfPresent(PrdtSpuCombine::getSpuCombineNo, reqVO.getSpuCombineNo())
                .likeIfPresent(PrdtSpuCombine::getSpuCombineName, reqVO.getSpuCombineName())
                .eqIfPresent(PrdtSpuCombine::getThumb, reqVO.getThumb())
                .eqIfPresent(PrdtSpuCombine::getThumbVideo, reqVO.getThumbVideo())
                .eqIfPresent(PrdtSpuCombine::getImages, reqVO.getImages())
                .eqIfPresent(PrdtSpuCombine::getDetails, reqVO.getDetails())
                .eqIfPresent(PrdtSpuCombine::getMemo, reqVO.getMemo())
                .eqIfPresent(PrdtSpuCombine::getIsDelete, reqVO.getIsDelete())
                .eqIfPresent(PrdtSpuCombine::getStatus, reqVO.getStatus())
                .likeIfPresent(PrdtSpuCombine::getSpecName, reqVO.getSpecName())
                .eqIfPresent(PrdtSpuCombine::getTotalLimit, reqVO.getTotalLimit())
                .eqIfPresent(PrdtSpuCombine::getMinOq, reqVO.getMinOq())
                .eqIfPresent(PrdtSpuCombine::getJumpOq, reqVO.getJumpOq())
                .eqIfPresent(PrdtSpuCombine::getMaxOq, reqVO.getMaxOq())
                .eqIfPresent(PrdtSpuCombine::getUnit, reqVO.getUnit())
                .eqIfPresent(PrdtSpuCombine::getSalePrice1, reqVO.getSalePrice1())
                .eqIfPresent(PrdtSpuCombine::getSalePrice2, reqVO.getSalePrice2())
                .eqIfPresent(PrdtSpuCombine::getSalePrice3, reqVO.getSalePrice3())
                .eqIfPresent(PrdtSpuCombine::getSalePrice4, reqVO.getSalePrice4())
                .eqIfPresent(PrdtSpuCombine::getSalePrice5, reqVO.getSalePrice5())
                .eqIfPresent(PrdtSpuCombine::getSalePrice6, reqVO.getSalePrice6())
                .orderByDesc(PrdtSpuCombine::getSpuCombineId));
    }

    Page<PrdtSpuCombinePageRespVO> selectPageSpuCombine(@Param("reqVO") PrdtSpuCombinePageReqVO reqVO, @Param("page") Page<PrdtSpuCombinePageReqVO> page);

    SpuCombineDTO selectSpuCombineDTO(@Param("spuCombineId") Long spuCombineId);

    List<SpuCombineDtlDTO> selectSpuCombineDtlList(@Param("spuCombineId") Long spuCombineId);
}
