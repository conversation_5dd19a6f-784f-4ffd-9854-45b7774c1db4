package com.zksr.product.controller.saleClass.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 平台商展示分类对象 prdt_sale_class
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@ApiModel("平台商展示分类 - prdt_sale_class分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtSaleClassPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商展示分类id */
    @ApiModelProperty(value = "平台商城市分组id")
    private Long saleClassId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 分类名称 */
    @Excel(name = "分类名称")
    @ApiModelProperty(value = "分类名称")
    private String name;

    /** 父id */
    @Excel(name = "父id")
    @ApiModelProperty(value = "父id")
    private Long pid;

    /** 分类图标 */
    @Excel(name = "分类图标")
    @ApiModelProperty(value = "分类图标")
    private String icon;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 排序 */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Long sort;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Long status;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id")
    private Long groupId;

    @ApiModelProperty(value = "展示类别级别")
    private Integer level;
}
