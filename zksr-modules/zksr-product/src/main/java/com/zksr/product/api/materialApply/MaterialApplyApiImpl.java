package com.zksr.product.api.materialApply;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.materialApply.dto.MaterialApplyDTO;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.product.controller.materialApply.vo.PrdtMaterialApplyEchoReqVO;
import com.zksr.product.controller.materialApply.vo.PrdtMaterialApplySaveReqVO;
import com.zksr.product.service.IPrdtMaterialApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class MaterialApplyApiImpl implements MaterialApplyApi {

    @Autowired
    private IPrdtMaterialApplyService materialApplyService;

    @Override
    public CommonResult<MaterialApplyDTO> getByMaterialApplyById(Long materialApplyId) {
        return success(HutoolBeanUtils.toBean(materialApplyService.getPrdtMaterialApply(materialApplyId),MaterialApplyDTO.class));
    }

    @Override
    public CommonResult<Boolean> addMaterialApply(MaterialApplyDTO dto) {
        materialApplyService.insertPrdtMaterialApply(ListUtil.toList(HutoolBeanUtils.toBean(dto, PrdtMaterialApplySaveReqVO.class)));
        return success(true);
    }

    @Override
    public CommonResult<Boolean> editMaterialApply(MaterialApplyDTO dto) {
        materialApplyService.updatePrdtMaterialApply(ListUtil.toList(HutoolBeanUtils.toBean(dto, PrdtMaterialApplySaveReqVO.class)));
        return success(true);
    }

    @Override
    public CommonResult<MaterialApplyDTO> getMaterialApplyByEchoReq(MaterialApplyDTO dto) {
        return success(HutoolBeanUtils.toBean(materialApplyService.getMaterialApplyByEchoReq(HutoolBeanUtils.toBean(dto, PrdtMaterialApplyEchoReqVO.class)),MaterialApplyDTO.class));
    }

    @Override
    public CommonResult<List<MaterialApplyDTO>> getByMaterialApplyByApplyIds(List<Long> applyIds) {
        return success(HutoolBeanUtils.toBean(materialApplyService.getByMaterialApplyByApplyIds(applyIds),MaterialApplyDTO.class));
    }

    @Override
    public CommonResult<MaterialApplyVO> getByMaterialApplyByMaterial(MaterialApplyVO vo) {
        return success(materialApplyService.getByMaterialApplyByMaterial(vo));
    }
}
