package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.product.controller.areaItem.excel.PrdtSupplierItemImportExcel;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemPageReqVO;
import com.zksr.product.api.supplierItem.vo.PrdtSupplierItemPageRespVO;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemRespVO;
import com.zksr.product.controller.supplierItem.vo.PrdtSupplierItemSaveReqVO;
import com.zksr.product.domain.PrdtSupplierItem;

import java.util.List;

/**
 * 入驻商上架商品Service接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
public interface IPrdtSupplierItemService {

    /**
     * 新增入驻商上架商品
     *
     * @param createReqVO 创建信息
     */
    public List<PrdtSupplierItem> insertPrdtSupplierItem(@Valid List<PrdtSupplierItemSaveReqVO> createReqVO);

    /**
     * 修改入驻商上架商品
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public List<PrdtSupplierItem> updatePrdtSupplierItem(@Valid PrdtSupplierItemSaveReqVO updateReqVO);

    /**
     * 批量上下架入驻商商品
     *
     * @param supplierItemIds 修改信息Id
     * @param minShelfStatus
     * @param midShelfStatus
     * @param largeShelfStatus
     * @return 结果
     */
    public List<PrdtSupplierItem> updatePrdtSupplierItemShelfStatus(Long[] supplierItemIds, Integer minShelfStatus, Integer midShelfStatus, Integer largeShelfStatus);


    /**
     * 删除入驻商上架商品
     *
     * @param supplierItemId 入驻商上架商品id
     */
    public void deletePrdtSupplierItem(Long supplierItemId);

    /**
     * 批量删除入驻商上架商品
     *
     * @param supplierItemIds 需要删除的入驻商上架商品主键集合
     * @return 结果
     */
    public void deletePrdtSupplierItemBySupplierItemIds(Long[] supplierItemIds);

    /**
     * 获得入驻商上架商品
     *
     * @param supplierItemId 入驻商上架商品id
     * @return 入驻商上架商品
     */
    public PrdtSupplierItemRespVO getPrdtSupplierItem(Long supplierItemId);

    /**
     * 获得入驻商上架商品分页
     *
     * @param pageReqVO 分页查询
     * @return 入驻商上架商品分页
     */
    PageResult<PrdtSupplierItemPageRespVO> getPrdtSupplierItemPage(PrdtSupplierItemPageReqVO pageReqVO);


    /**
     * 获得入驻商上架商品
     *
     * @param supplierItemId 入驻商上架商品id
     * @return 入驻商上架商品
     */
    public PrdtSupplierItem getBySupplierItemId(Long supplierItemId);

    public List<SupplierItemDTO> getSupplierItemListByApi(SupplierItemDTO itemDTO);

    /**
     * 统一处理上架缓存
     * @param supplierItems
     */
    void cacheEvent(List<PrdtSupplierItem> supplierItems);

    List<PrdtSupplierItem> getSaleQtyTotalList(Long minSupplierItemId);

    /**
     * 批量修改入驻商上架商品展示分类
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    List<PrdtSupplierItem> batchEditItemSaleClassId(@Valid PrdtSupplierItemSaveReqVO updateReqVO);

    String impordData(List<PrdtSupplierItemImportExcel> prdtSupplierList);


    /**
     * 获得入驻商上架商品
     *
     * @param spuCombineId 组合商品ID
     * @return 入驻商上架商品
     */
    public PrdtSupplierItem getSupplierItemBySpuCombineId(Long spuCombineId);

    /**
     * 修改入驻商组合促销上架商品
     *
     * @param supplierItemDTO 修改信息
     * @return 结果
     */
    public void updatePrdtSupplierCombineItem(SupplierItemDTO supplierItemDTO);

    /**
     * 获得入驻商上架商品
     *
     * @param activityId 活动ID
     * @return 入驻商上架商品
     */
    public PrdtSupplierItem getSupplierItemByActivityId(Long activityId);


}
