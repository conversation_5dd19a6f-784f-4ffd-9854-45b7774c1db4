package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 平台商品库-商品SKU对象 prdt_platform_sku
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@TableName(value = "prdt_platform_sku")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PrdtPlatformSku extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 商品库sku id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long platformSkuId;

    /** 商品库SPU id */
    @Excel(name = "商品库SPU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long platformSpuId;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    private Integer unit;

    /** 国际条码 */
    @Excel(name = "国际条码")
    private String barcode;

    /** 属性数组，JSON 格式 */
    @Excel(name = "属性数组，JSON 格式")
    private String properties;

    /** 封面图 */
    @Excel(name = "封面图")
    private String thumb;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    private Integer status;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    private Integer isDelete;

    /** 中单位-国际条码 */
    @Excel(name = "中单位-国际条码")
    private String midBarcode;

    /** 大单位-国际条码 */
    @Excel(name = "大单位-国际条码")
    private String largeBarcode;

}
