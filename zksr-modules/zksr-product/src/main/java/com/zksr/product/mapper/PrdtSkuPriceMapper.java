package com.zksr.product.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.controller.skuPrice.vo.PrdtSkuPriceRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtSkuPrice;
import com.zksr.product.controller.skuPrice.vo.PrdtSkuPricePageReqVO;
import org.apache.ibatis.annotations.Param;


/**
 * sku销售价Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Mapper
public interface PrdtSkuPriceMapper extends BaseMapperX<PrdtSkuPrice> {
    default PageResult<PrdtSkuPrice> selectPage(PrdtSkuPricePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtSkuPrice>()
                    .eqIfPresent(PrdtSkuPrice::getSkuPriceId, reqVO.getSkuPriceId())
                    .eqIfPresent(PrdtSkuPrice::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtSkuPrice::getAreaId, reqVO.getAreaId())
                    .eqIfPresent(PrdtSkuPrice::getSkuId, reqVO.getSkuId())
                    .eqIfPresent(PrdtSkuPrice::getSalePrice1, reqVO.getSalePrice1())
                    .eqIfPresent(PrdtSkuPrice::getSalePrice2, reqVO.getSalePrice2())
                    .eqIfPresent(PrdtSkuPrice::getSalePrice3, reqVO.getSalePrice3())
                    .eqIfPresent(PrdtSkuPrice::getSalePrice4, reqVO.getSalePrice4())
                    .eqIfPresent(PrdtSkuPrice::getSalePrice5, reqVO.getSalePrice5())
                    .eqIfPresent(PrdtSkuPrice::getSalePrice6, reqVO.getSalePrice6())
                    .eqIfPresent(PrdtSkuPrice::getType, reqVO.getType())
                    .eq(PrdtSkuPrice::getDeleted, NumberPool.INT_ZERO)
                .orderByDesc(PrdtSkuPrice::getSkuPriceId));
    }

    /**
     * 分页查询sku销售价
     * @param reqVO
     * @param page
     * @return
     */
    public Page<PrdtSkuPricePageReqVO> selectPageSkuPrice(@Param("reqVO") PrdtSkuPricePageReqVO reqVO,@Param("page") Page<PrdtSkuPricePageReqVO> page);

    /**
     * 分页查询sku销售价定价列表
     */
    public Page<PrdtSkuPricePageReqVO> selectPageSkuPriceByPricing(@Param("reqVO") PrdtSkuPricePageReqVO reqVO,@Param("page") Page<PrdtSkuPricePageReqVO> page);



    default PrdtSkuPrice getSkuPrice(PrdtSkuPriceRespVO reqVO) {
        return selectOne(
                new LambdaQueryWrapperX<PrdtSkuPrice>()
                .eqIfPresent(PrdtSkuPrice::getSkuPriceId, reqVO.getSkuPriceId())
                .eqIfPresent(PrdtSkuPrice::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrdtSkuPrice::getAreaId, reqVO.getAreaId())
                .eqIfPresent(PrdtSkuPrice::getSkuId, reqVO.getSkuId())
                .eqIfPresent(PrdtSkuPrice::getSalePrice1, reqVO.getSalePrice1())
                .eqIfPresent(PrdtSkuPrice::getSalePrice2, reqVO.getSalePrice2())
                .eqIfPresent(PrdtSkuPrice::getSalePrice3, reqVO.getSalePrice3())
                .eqIfPresent(PrdtSkuPrice::getSalePrice4, reqVO.getSalePrice4())
                .eqIfPresent(PrdtSkuPrice::getSalePrice5, reqVO.getSalePrice5())
                .eqIfPresent(PrdtSkuPrice::getSalePrice6, reqVO.getSalePrice6())
                .eqIfPresent(PrdtSkuPrice::getType, reqVO.getType())
                .eq(PrdtSkuPrice::getDeleted, NumberPool.INT_ZERO)
                .last(StringPool.LIMIT_ONE)
        );
    }

    void updateSkuPrice(@Param("skuId") Long skuId, @Param("deleted") Integer deleted);

    default PrdtSkuPrice getSkuPriceBySkuPriceId(Long skuPriceId){
        return selectOne(new LambdaQueryWrapperX<PrdtSkuPrice>()
                .eq(PrdtSkuPrice::getSkuPriceId, skuPriceId));
    }

    default PrdtSkuPrice getSkuPriceBySkuId(Long skuId,Long areaId){
        return selectOne(new LambdaQueryWrapperX<PrdtSkuPrice>()
                        .eq(PrdtSkuPrice::getSkuId, skuId)
                        .eq(PrdtSkuPrice::getAreaId, areaId)
                        .last("limit 1"));
    }
}
