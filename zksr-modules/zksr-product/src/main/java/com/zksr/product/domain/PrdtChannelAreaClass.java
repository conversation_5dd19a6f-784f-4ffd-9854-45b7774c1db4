package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.annotation.Excel;
import lombok.*;

/**
 * 渠道展示分类关联对象 prdt_channel_area_class
 *
 * <AUTHOR>
 * @date 2024-03-27
 */
@TableName(value = "prdt_channel_area_class")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtChannelAreaClass {
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 城市展示分类id;城市展示分类id，如果指定了渠道id,则只有指定渠道下的门店可见，电子围栏分类无无渠道 */
    @Excel(name = "城市展示分类id;城市展示分类id，如果指定了渠道id,则只有指定渠道下的门店可见，电子围栏分类无无渠道")
    private Long areaClassId;

    /** 渠道id */
    @Excel(name = "渠道id")
    private Long channelId;

}
