package com.zksr.product.controller.areaItem.excel;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.product.domain.PrdtSupplierItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

@Data
@ApiModel(description = "用于平台商导入上架")
public class PrdtSupplierItemImportExcel {

    @Excel(name = "上架三级展示分类编号" , headerColor = IndexedColors.RED)
    @ApiModelProperty(value = "上架三级展示分类编号")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long saleClassId;

    /** 商品sku id */
    @Excel(name = "skuId" , headerColor = IndexedColors.RED)
    @ApiModelProperty(value = "skuId")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 小单位-上下架状态 */
    @ApiModelProperty(value = "小单位-上下架状态", example = "示例值")
    private Integer minShelfStatus;

    /** 中单位-上下架状态 */
    @ApiModelProperty(value = "中单位-上下架状态", example = "示例值")
    private Integer midShelfStatus;

    /** 大单位-上下架状态 */
    @ApiModelProperty(value = "大单位-上下架状态", example = "示例值")
    private Integer largeShelfStatus;

    /** 小单位-上下架状态 */
    @Excel(name = "小单位" , headerColor = IndexedColors.RED)
    @ApiModelProperty(value = "小单位-上下架状态", example = "示例值")
    private String minShelfStatusName;

    /** 中单位-上下架状态 */
    @Excel(name = "中单位" , headerColor = IndexedColors.RED)
    @ApiModelProperty(value = "中单位-上下架状态", example = "示例值")
    private String  midShelfStatusName;

    /** 大单位-上下架状态 */
    @Excel(name = "大单位" , headerColor = IndexedColors.RED)
    @ApiModelProperty(value = "大单位-上下架状态", example = "示例值")
    private String largeShelfStatusName;

}