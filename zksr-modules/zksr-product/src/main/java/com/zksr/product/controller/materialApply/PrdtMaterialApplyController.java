package com.zksr.product.controller.materialApply;

import javax.validation.Valid;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.product.controller.materialApply.vo.*;
import com.zksr.product.convert.materialApply.PrdtMaterialApplyConvert;
import com.zksr.product.mapper.PrdtMaterialApplyMapper;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.service.IPrdtMaterialApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 素材应用Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Api(tags = "管理后台 - 素材应用接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/materialApply")
public class PrdtMaterialApplyController {
    @Autowired
    private IPrdtMaterialApplyService prdtMaterialApplyService;

    @Autowired
    private PrdtMaterialApplyMapper prdtMaterialApplyMapper;

    /**
     * 新增素材应用
     */
    @ApiOperation(value = "新增素材应用", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "素材应用", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Boolean> add(@Valid @RequestBody List<PrdtMaterialApplySaveReqVO> createReqVO) {
        prdtMaterialApplyService.insertPrdtMaterialApply(createReqVO);

        //清除缓存
        prdtMaterialApplyService.removeCache(createReqVO);
        return success(true);
    }

    /**
     * 修改素材应用
     */
    @ApiOperation(value = "修改素材应用", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "素材应用", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody List<PrdtMaterialApplySaveReqVO> updateReqVO) {
        prdtMaterialApplyService.updatePrdtMaterialApply(updateReqVO);

        //清除缓存
        prdtMaterialApplyService.removeCache(updateReqVO);
        return success(true);
    }

    /**
     * 删除素材应用
     */
    @ApiOperation(value = "删除素材应用", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "素材应用", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public CommonResult<Boolean> remove(@RequestBody List<Long> materialApplyIds) {
        if(!materialApplyIds.isEmpty()){
            prdtMaterialApplyService.deletePrdtMaterialApplyByMaterialApplyIds(materialApplyIds);

            //清除缓存
            prdtMaterialApplyService.removeCache(PrdtMaterialApplyConvert.INSTANCE.convert(prdtMaterialApplyMapper.selectBatchIds(materialApplyIds)));
        }

        return success(true);
    }

    /**
     * 获取素材应用详细信息
     */
    @ApiOperation(value = "获得素材应用详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/getInfo")
    public CommonResult<PrdtMaterialApplyEchoRespVO> getInfo(PrdtMaterialApplyEchoReqVO reqVO) {
        return success(prdtMaterialApplyService.getMaterialApplyList(reqVO));
    }

    /**
     * 分页查询素材应用
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得素材应用分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtMaterialApplyRespVO>> getPage(@Valid PrdtMaterialApplyPageReqVO pageReqVO) {
        //pageReqVO.setFuncScope(ObjectUtil.isNull(SecurityUtils.getLoginUser().getDcId()) ? PRM_FUNC_SCOPE_1 : PRM_FUNC_SCOPE_2);
        return success(prdtMaterialApplyService.getPrdtMaterialApplyPage(pageReqVO));
    }

    /**
     * 下架素材应用
     */
    @ApiOperation(value = "下架素材应用", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "素材应用", businessType = BusinessType.DELETE)
    @PutMapping("/removeMaterialApply")
    public CommonResult<Boolean> removeMaterialApply(@RequestBody PrdtMaterialApplyEchoReqVO reqVO) {
        prdtMaterialApplyService.removeMaterialApply(reqVO);
        //清除缓存
        prdtMaterialApplyService.removeCache((ListUtil.toList(PrdtMaterialApplyConvert.INSTANCE.convert(reqVO))));
        return success(true);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:materialApply:add";
        /** 编辑 */
        public static final String EDIT = "product:materialApply:edit";
        /** 删除 */
        public static final String DELETE = "product:materialApply:remove";
        /** 列表 */
        public static final String LIST = "product:materialApply:list";
        /** 查询 */
        public static final String GET = "product:materialApply:query";
        /** 停用 */
        public static final String DISABLE = "product:materialApply:disable";
        /** 启用 */
        public static final String ENABLE = "product:materialApply:enable";
    }
}
