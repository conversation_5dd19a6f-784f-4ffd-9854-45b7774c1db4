package com.zksr.product.convert.blockScheme;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.product.domain.PrdtBlockBranch;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockBranchRespVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockBranchSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 经营屏蔽客户 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-27
*/
@Mapper
public interface PrdtBlockBranchConvert {

    PrdtBlockBranchConvert INSTANCE = Mappers.getMapper(PrdtBlockBranchConvert.class);

    PrdtBlockBranchRespVO convert(PrdtBlockBranch prdtBlockBranch);

    PrdtBlockBranchRespVO convert(BranchDTO branchDTO);

    PrdtBlockBranch convert(PrdtBlockBranchSaveReqVO prdtBlockBranchSaveReq);

    PageResult<PrdtBlockBranchRespVO> convertPage(PageResult<PrdtBlockBranch> prdtBlockBranchPage);

    @Mappings({
            @Mapping(target = "createBy", expression = "java(com.zksr.common.security.utils.SecurityUtils.getUsername())"),
            @Mapping(target = "updateBy", expression = "java(com.zksr.common.security.utils.SecurityUtils.getUsername())"),
            @Mapping(target = "updateTime", expression = "java(com.zksr.common.core.utils.DateUtils.getNowDate())")
    })
    PrdtBlockBranch convert(String schemeNo, Long branchId);
}