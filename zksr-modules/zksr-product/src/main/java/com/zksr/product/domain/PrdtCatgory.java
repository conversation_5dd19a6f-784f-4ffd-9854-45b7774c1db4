package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;

/**
 * 平台商管理分类对象 prdt_catgory
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@TableName(value = "prdt_catgory")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtCatgory extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 平台商管理分类id */
    @TableId(type = IdType.AUTO)
    private Long catgoryId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 父id */
    @Excel(name = "父id")
    private Long pid;

    /** 分类名 */
    @Excel(name = "分类名")
    private String catgoryName;

    /** 分类图标 */
    @Excel(name = "分类图标")
    private String icon;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    /** 状态 0-停用 1-启用 */
    @Excel(name = "状态 0-停用 1-启用")
    private String status;

    /** 软件商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "软件商分润比例", readConverterExp = "只=一级分类设定")
    private BigDecimal softwareRate;

    /** 平台商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "平台商分润比例", readConverterExp = "只=一级分类设定")
    private BigDecimal partnerRate;

    /** 运营商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "运营商分润比例", readConverterExp = "只=一级分类设定")
    private BigDecimal dcRate;

    /** 业务员负责人分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "业务员负责人分润比例", readConverterExp = "只=一级分类设定")
    private BigDecimal colonel1Rate;

    /** 业务员分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "业务员分润比例", readConverterExp = "只=一级分类设定")
    private BigDecimal colonel2Rate;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 级别 */
    @Excel(name = "级别")
    private Integer level;

    /**
     * 是否绑定了商品 true 绑定商品
     */
    @TableField(exist = false)
    private Boolean isDisabled;

    /**
     * 销售占比利润, 百分比,最高29% = 0.29, 只有一级可以设置
     */
    @ApiModelProperty(value = "销售占比利润, 百分比,最高29% = 0.29, 只有一级可以设置")
    private BigDecimal saleTotalRate;
}
