package com.zksr.product.controller.blockScheme;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.product.controller.blockScheme.excel.BlockSkuExcelToListVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtBlockSku;
import com.zksr.product.service.IPrdtBlockSkuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.blockScheme.vo.PrdtBlockSkuPageReqVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSkuSaveReqVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSkuRespVO;
import com.zksr.product.convert.blockScheme.PrdtBlockSkuConvert;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 经营屏蔽skuController
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Api(tags = "管理后台 - 经营屏蔽sku接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/blockSku")
public class PrdtBlockSkuController {
    @Autowired
    private IPrdtBlockSkuService prdtBlockSkuService;

    /**
     * 新增经营屏蔽sku
     */
    @ApiOperation(value = "新增经营屏蔽sku", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "经营屏蔽sku", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtBlockSkuSaveReqVO createReqVO) {
        return success(prdtBlockSkuService.insertPrdtBlockSku(createReqVO));
    }

    /**
     * 修改经营屏蔽sku
     */
    @ApiOperation(value = "修改经营屏蔽sku", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "经营屏蔽sku", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtBlockSkuSaveReqVO updateReqVO) {
            prdtBlockSkuService.updatePrdtBlockSku(updateReqVO);
        return success(true);
    }

    /**
     * 删除经营屏蔽sku
     */
    @ApiOperation(value = "删除经营屏蔽sku", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "经营屏蔽sku", businessType = BusinessType.DELETE)
    @DeleteMapping("/{blockSkuIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] blockSkuIds) {
        prdtBlockSkuService.deletePrdtBlockSkuByBlockSkuIds(blockSkuIds);
        return success(true);
    }

    /**
     * 获取经营屏蔽sku详细信息
     */
    @ApiOperation(value = "获得经营屏蔽sku详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{blockSkuId}")
    public CommonResult<PrdtBlockSkuRespVO> getInfo(@PathVariable("blockSkuId") Long blockSkuId) {
        PrdtBlockSku prdtBlockSku = prdtBlockSkuService.getPrdtBlockSku(blockSkuId);
        return success(PrdtBlockSkuConvert.INSTANCE.convert(prdtBlockSku));
    }

    /**
     * 分页查询经营屏蔽sku
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得经营屏蔽sku分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtBlockSkuRespVO>> getPage(@Valid PrdtBlockSkuPageReqVO pageReqVO) {
        PageResult<PrdtBlockSku> pageResult = prdtBlockSkuService.getPrdtBlockSkuPage(pageReqVO);
        return success(PrdtBlockSkuConvert.INSTANCE.convertPage(pageResult));
    }

    @ApiOperation(value = "根据Excel获取商品信息", httpMethod = HttpMethod.POST)
    @PostMapping("/excelToList")
    public CommonResult<List<PrdtBlockSkuRespVO>> excelToList(@RequestParam("file") MultipartFile file,
                                                                 @RequestParam(value = "schemeNo", required = false) String schemeNo) throws Exception {
        ExcelUtil<BlockSkuExcelToListVO> util = new ExcelUtil<>(BlockSkuExcelToListVO.class);
        List<BlockSkuExcelToListVO> skuList = util.importExcel(file.getInputStream());
        return CommonResult.success(prdtBlockSkuService.excelToList(skuList, schemeNo));
    }

    @PostMapping("/excelToListTemplate")
    @ApiOperation(value = "根据Excel获取商品信息导入模版", httpMethod = HttpMethod.POST)
    public void excelToListTemplate(HttpServletResponse response) {
        ExcelUtil<BlockSkuExcelToListVO> util = new ExcelUtil<>(BlockSkuExcelToListVO.class);
        util.importTemplateExcel(response, "根据Excel获取客户信息", StringUtils.EMPTY, null);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:blockSku:add";
        /** 编辑 */
        public static final String EDIT = "product:blockSku:edit";
        /** 删除 */
        public static final String DELETE = "product:blockSku:remove";
        /** 列表 */
        public static final String LIST = "product:blockSku:list";
        /** 查询 */
        public static final String GET = "product:blockSku:query";
        /** 停用 */
        public static final String DISABLE = "product:blockSku:disable";
        /** 启用 */
        public static final String ENABLE = "product:blockSku:enable";
    }
}
