package com.zksr.product.controller.materialApply.vo;

import lombok.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import javax.validation.Constraint;
import javax.validation.constraints.NotNull;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 素材应用对象 prdt_material_apply
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@ApiModel("素材应用 - prdt_material_apply分页 Request VO")
public class PrdtMaterialApplySaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 素材应用id */
    @ApiModelProperty(value = "素材应用id")
    private Long materialApplyId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 素材id */
    @Excel(name = "素材id")
    @ApiModelProperty(value = "素材id", required = true)
    @NotNull(message = "素材信息不能为空")
    private Long materialId;

    /** 素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品 */
    @Excel(name = "素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品")
    @ApiModelProperty(value = "素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品", required = true)
    @NotNull(message = "素材应用类型不能为空，请选择是促销类型还是商品类型")
    private Integer applyType;

    /** 素材应用类型id */
    @Excel(name = "素材应用类型id")
    @ApiModelProperty(value = "素材应用类型id", required = true)
    @NotNull(message = "请选择应用该素材的促销或者商品")
    private Long applyId;

    /** 生效时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "生效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "生效时间", required = true)
    @NotNull(message = "请设置生效时间")
    private Date startTime;

    /** 失效时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "失效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "失效时间", required = true)
    @NotNull(message = "请设置失效时间")
    private Date endTime;

    /** 操作人 */
    @Excel(name = "操作人")
    @ApiModelProperty(value = "操作人")
    private Long applyUserId;

}
