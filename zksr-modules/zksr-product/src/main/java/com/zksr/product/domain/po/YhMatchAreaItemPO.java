package com.zksr.product.domain.po;

import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.product.controller.yhdata.vo.YhSkuUnitVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.rely.FieldType;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/10 11:30
 */
@Data
public class YhMatchAreaItemPO {

    @ApiModelProperty("本地上架ID")
    private Long areaItemId;

    @ApiModelProperty("skuId")
    private Long skuId;

    @ApiModelProperty(value = "小单位-上下架状态", notes = "0-未上架, 1-已上架")
    private Integer minShelfStatus;

    @ApiModelProperty(value = "中单位-上下架状态", notes = "0-未上架, 1-已上架")
    private Integer midShelfStatus;

    @ApiModelProperty(value = "大单位-上下架状态", notes = "0-未上架, 1-已上架")
    private Integer largeShelfStatus;

    @ApiModelProperty("入驻商ID")
    private Long supplierId;

    @ApiModelProperty(value = "三级展示分类ID")
    private Long threeSaleClassId;

    @ApiModelProperty("入驻商要货优先级,0最高, 1次之")
    private Long supplierYhSort = Long.MAX_VALUE;

    @ApiModelProperty("spuId")
    private Long spuId;

    @ApiModelProperty("商品名称")
    private String spuName;

    @ApiModelProperty("系统商品条码")
    private String barcode;

    @ApiModelProperty("中单位条码")
    private String midBarcode;

    @ApiModelProperty("大单位条码")
    private String largeBarcode;

    public Integer getUnitType() {
        if (this.minShelfStatus == 1) {
            return UnitTypeEnum.UNIT_SMALL.getType();
        }
        if (this.midShelfStatus == 1) {
            return UnitTypeEnum.UNIT_MIDDLE.getType();
        }
        return UnitTypeEnum.UNIT_LARGE.getType();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        YhMatchAreaItemPO that = (YhMatchAreaItemPO) o;
        return Objects.equals(areaItemId, that.areaItemId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(areaItemId);
    }

    public List<YhSkuUnitVO> getSkuUnit() {
        List<YhSkuUnitVO> unitVOS = new ArrayList<>();
        if (StringUtils.isNotEmpty(barcode)) {
            YhSkuUnitVO skuUnitVO = new YhSkuUnitVO();
            skuUnitVO.setAreaItemPO(this);
            skuUnitVO.setAreaItemId(this.areaItemId);
            skuUnitVO.setSpuId(this.spuId);
            skuUnitVO.setSkuId(this.skuId);
            skuUnitVO.setBarcode(this.barcode);
            skuUnitVO.setUnitSize(UnitTypeEnum.UNIT_SMALL.getType());
            skuUnitVO.setShelfStatus(minShelfStatus);
            unitVOS.add(skuUnitVO);
        }
        if (StringUtils.isNotEmpty(midBarcode)) {
            YhSkuUnitVO skuUnitVO = new YhSkuUnitVO();
            skuUnitVO.setAreaItemPO(this);
            skuUnitVO.setAreaItemId(this.areaItemId);
            skuUnitVO.setSpuId(this.spuId);
            skuUnitVO.setSkuId(this.skuId);
            skuUnitVO.setBarcode(this.midBarcode);
            skuUnitVO.setUnitSize(UnitTypeEnum.UNIT_MIDDLE.getType());
            skuUnitVO.setShelfStatus(midShelfStatus);
            unitVOS.add(skuUnitVO);
        }
        if (StringUtils.isNotEmpty(largeBarcode)) {
            YhSkuUnitVO skuUnitVO = new YhSkuUnitVO();
            skuUnitVO.setAreaItemPO(this);
            skuUnitVO.setAreaItemId(this.areaItemId);
            skuUnitVO.setSpuId(this.spuId);
            skuUnitVO.setSkuId(this.skuId);
            skuUnitVO.setBarcode(this.largeBarcode);
            skuUnitVO.setUnitSize(UnitTypeEnum.UNIT_LARGE.getType());
            skuUnitVO.setShelfStatus(largeShelfStatus);
            unitVOS.add(skuUnitVO);
        }
        return unitVOS;
    }
}
