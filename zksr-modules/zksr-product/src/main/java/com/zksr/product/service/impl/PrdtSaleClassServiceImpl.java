package com.zksr.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.saleClass.dto.SaleClassExportVo;
import com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo;
import com.zksr.product.controller.areaClass.vo.PrdtAreaClassRespVO;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassCopyReqVO;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassPageReqVO;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassRespVO;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassSaveReqVO;
import com.zksr.product.domain.PrdtAreaClass;
import com.zksr.product.domain.PrdtGroupSaleClass;
import com.zksr.product.domain.PrdtSaleClass;
import com.zksr.product.domain.PrdtSupplierItem;
import com.zksr.product.domain.excel.ProductSaleClassImportExcel;
import com.zksr.product.mapper.PrdtGroupSaleClassMapper;
import com.zksr.product.mapper.PrdtSaleClassMapper;
import com.zksr.product.mapper.PrdtSupplierItemMapper;
import com.zksr.product.service.IPrdtSaleClassService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_PARTNER_CONFIG_GLOBAL_DISABLE;

/**
 * 平台商展示分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class PrdtSaleClassServiceImpl implements IPrdtSaleClassService {

    @Autowired
    private PrdtSaleClassMapper prdtSaleClassMapper;

    @Autowired
    private PrdtGroupSaleClassMapper groupSaleClassMapper;

    @Autowired
    private Cache<Long, SaleClassDTO> saleClassDtoCache;

    @Autowired
    private Cache<Long, List<SaleClassDTO>> saleClassDtoListCache;

    @Autowired
    private PrdtSupplierItemMapper supplierItemMapper;

    @Autowired
    private IProductCacheService productCacheService;

    /**
     * 新增平台商展示分类
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertPrdtSaleClass(PrdtSaleClassSaveReqVO createReqVO) {
        //校验名称
        validate(createReqVO);
        //校验级别
        checkSaleClassLevel(createReqVO);
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        // 插入
        PrdtSaleClass prdtSaleClass = HutoolBeanUtils.toBean(createReqVO, PrdtSaleClass.class);
        prdtSaleClassMapper.insert(prdtSaleClass);
        // 关联表添加数据
        if(ToolUtil.isNotEmpty(prdtSaleClass.getGroupId())){
            PrdtGroupSaleClass prdtGroupSaleClass = new PrdtGroupSaleClass(sysCode, prdtSaleClass.getGroupId(), prdtSaleClass.getSaleClassId());
            groupSaleClassMapper.insert(prdtGroupSaleClass);
        }
        saleClassDtoListCache.remove(sysCode);
        // 返回
        return prdtSaleClass.getSaleClassId();
    }

    /**
     * 修改平台商展示分类
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrdtSaleClass(PrdtSaleClassSaveReqVO updateReqVO) {
        //修改平台商展示基本信息
        validate(updateReqVO);
        //同步子级
        //synSublevel(updateReqVO);
        prdtSaleClassMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrdtSaleClass.class));
        // 设置全国分组为空
        if (Objects.isNull(updateReqVO.getGroupId())) {
            prdtSaleClassMapper.setGroupIdNull(updateReqVO.getSaleClassId());
        }
        //修改平台商绑定城市分组
        groupSaleClassMapper.removeBindBySaleClassId(updateReqVO.getSaleClassId());
        //绑定平台商绑定城市分组
        if(ToolUtil.isNotEmpty(updateReqVO.getGroupId())){
            Long sysCode = SecurityUtils.getLoginUser().getSysCode();
            PrdtGroupSaleClass prdtGroupSaleClass = new PrdtGroupSaleClass(sysCode, updateReqVO.getGroupId(), updateReqVO.getSaleClassId());
            groupSaleClassMapper.insert(prdtGroupSaleClass);
        }
        //删除缓存
        saleClassDtoCache.remove(updateReqVO.getSaleClassId());
        saleClassDtoListCache.remove(SecurityUtils.getLoginUser().getSysCode());
    }

    /**
     * 删除平台商展示分类
     *
     * @param saleClassId 平台商展示分类id
     */
    @Override
    public void deletePrdtSaleClass(Long saleClassId) {
        PrdtSaleClass saleClass = prdtSaleClassMapper.selectById(saleClassId);
        saleClass.setDelFlag(StringPool.ONE);
        // 验证数据
        // 查询有没有下级
        {
            Long count = prdtSaleClassMapper.selectCountByPid(saleClassId);
            if (count > 0) {
                throw exception(PRDT_SALE_CLASS_HAS_CHILD, saleClass.getName());
            }
        }
        // 验证数据
        // 查询有没有发布商品
        {
            Long count = supplierItemMapper.selectCountReleaseBySaleClassId(saleClassId);
            if (count > 0) {
                throw exception(PRDT_SALE_CLASS_HAS_RELEASE, saleClass.getName());
            }
        }

        // 删除
        prdtSaleClassMapper.updateById(saleClass);
        saleClassDtoCache.remove(saleClassId);
        saleClassDtoListCache.remove(saleClass.getSysCode());
    }

    /**
     * 逻辑删除分类信息及清除展示分类相关联的缓存等信息
     * @param saleClassId
     */
    private void deleteClassAndRelevance(Long saleClassId){
        PrdtSaleClass saleClass = prdtSaleClassMapper.selectById(saleClassId);
        saleClass.setDelFlag(StringPool.ONE);

        // 删除
        prdtSaleClassMapper.updateById(saleClass);
        saleClassDtoCache.remove(saleClassId);
        saleClassDtoListCache.remove(saleClass.getSysCode());

    }

    /**
     * 批量删除平台商展示分类
     *
     * @param saleClassIds 需要删除的平台商展示分类主键
     * @return 结果
     */
    @Override
    public void deletePrdtSaleClassBySaleClassIds(Long[] saleClassIds) {
        for (Long saleClassId : saleClassIds) {
            this.deletePrdtSaleClass(saleClassId);
        }
    }

    /**
     * 获得平台商展示分类
     *
     * @param saleClassId 平台商展示分类id
     * @return 平台商展示分类
     */
    @Override
    public PrdtSaleClass getPrdtSaleClass(Long saleClassId) {
        return prdtSaleClassMapper.selectById(saleClassId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrdtSaleClass> getPrdtSaleClassPage(PrdtSaleClassPageReqVO pageReqVO) {
        return prdtSaleClassMapper.selectPage(pageReqVO);
    }

    /**
     * @Description: 获取平台商展示分类子级列表
     * @Author: liuxingyu
     * @Date: 2024/2/19 17:38
     */
    @Override
    public PageResult<PrdtSaleClassRespVO> getSublevelSaleClassById(PrdtSaleClassPageReqVO pageReqVO) {
        //通过ID 查询子级数据
        /*PrdtSaleClassPageReqVO reqVO = new PrdtSaleClassPageReqVO();
        reqVO.setPid(pageReqVO.getSaleClassId());
        PageResult<PrdtSaleClass> prdtSaleClasses = prdtSaleClassMapper.selectPage(reqVO);
        if (ObjectUtil.isEmpty(prdtSaleClasses.getList())) {
            //如果为空则返回自己
            prdtSaleClasses = prdtSaleClassMapper.selectPage(pageReqVO);
        }*/
        return HutoolBeanUtils.toBean(prdtSaleClassMapper.selectPage(pageReqVO), PrdtSaleClassRespVO.class);
    }

    /**
     * @Description: 获取平台商展示分类集合
     * @Author: liuxingyu
     * @Date: 2024/2/27 15:07
     */
    @Override
    public List<PrdtSaleClassRespVO> getSaleClassList(PrdtSaleClassPageReqVO reqVO) {
        return HutoolBeanUtils.toBean(prdtSaleClassMapper.getSaleClassList(reqVO), PrdtSaleClassRespVO.class);
    }

    /**
     * @Description: 根据平台商展示分类Id获取平台商展示分类
     * @Author: liuxingyu
     * @Date: 2024/3/25 15:03
     */
    @Override
    public PrdtSaleClass getSaleClassBySaleClassId(Long saleClassId) {
        return prdtSaleClassMapper.selectById(saleClassId);
    }

    /**
     * @Description: 获取平台展示分类列表
     * @Author: liuxingyu
     * @Date: 2024/3/26 20:16
     */
    @Override
    public List<PrdtSaleClass> getSaleClassListBySysCode(Long sysCode) {
        return prdtSaleClassMapper.getSaleClassListBySysCode(sysCode);
    }

    /**
     * 批量获取展示分类信息   (暂时用于支持数据回显)
     *
     * @param saleClassIds 展示分类ID集合
     * @return 展示分类集合
     */
    @Override
    public List<PrdtSaleClassRespVO> getSelectedSaleClass(List<Long> saleClassIds) {
        return BeanUtil.copyToList(prdtSaleClassMapper.selectSelectedSaleClass(saleClassIds), PrdtSaleClassRespVO.class);
    }

    /**
     * @Description: 变更平台展示分类状态
     * @Author: liuxingyu
     * @Date: 2024/5/6 16:22
     */
    @Override
    public Boolean changeStatus(PrdtSaleClassSaveReqVO updateReqVO) {
        if (ObjectUtil.isNull(updateReqVO.getSaleClassId()) || ObjectUtil.isNull(updateReqVO.getStatus())) {
            throw exception(PRDT_SALE_CLASS_CHANGE_STATUS_MISSING_PARAMETER);
        }
        PrdtSaleClass prdtSaleClass = new PrdtSaleClass();
        prdtSaleClass.setSaleClassId(updateReqVO.getSaleClassId());
        prdtSaleClass.setStatus(updateReqVO.getStatus());
        // 清楚缓存
        saleClassDtoListCache.remove(SecurityUtils.getLoginUser().getSysCode());
        return ObjectUtil.notEqual(prdtSaleClassMapper.updateById(prdtSaleClass), NumberPool.INT_ZERO);
    }

    @Override
    public String importSaleClassData(List<ProductSaleClassImportExcel> saleClassList) {
        if (saleClassList.isEmpty()) {
            // 如果导入数据为空，则不进行数据导入
            throw exception(PLATFORM_DISPLAY_CATEGORY_IMPORT_EMPTY);
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        Map<String, Long> insertedCategoriesMap  = new HashMap<>(); // 存储已插入的类别，避免重复插入
        Map<String, Long> levelSortMap = new HashMap<>(); // 存储每一级的排序号

        for (int line = 0; line < saleClassList.size(); line++) {
            if (failureMsg.length() > 2000) {
                break; // 避免错误信息过长
            }

            int cellNumber = line + 3; // Excel中的行号
            ProductSaleClassImportExcel itemData = saleClassList.get(line);

            try {
                // 校验导入数据的有效性
                validateSaleClassImportExcel(itemData, cellNumber);

                Long primaryCategoryId = processCategory(itemData.getPrimaryCategoryName(), 1, insertedCategoriesMap, null,levelSortMap);
                Long secondaryCategoryId = processCategory(itemData.getSecondaryCategoryName(), 2, insertedCategoriesMap, primaryCategoryId,levelSortMap);

                // 验证三级分类在当前二级分类下是否唯一
                PrdtSaleClass existingTertiaryCategory = prdtSaleClassMapper.checkTertiaryCategoryUniquenessInSecondaryCategory(itemData.getTertiaryCategoryName(), secondaryCategoryId);
                if (existingTertiaryCategory != null) {
                    throw new Exception(StringUtils.format("三级类别 '{}' 已存在于二级类别 '{}' 下", itemData.getTertiaryCategoryName(), itemData.getSecondaryCategoryName()));
                }

                processCategory(itemData.getTertiaryCategoryName(), 3, insertedCategoriesMap, secondaryCategoryId,levelSortMap);
                successNum++;
            } catch (Exception e) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据导入失败，错误信息：{}。", cellNumber, e.getMessage()));
            }
        }
        String resultMessage;
        // 返回导入结果
        if (failureNum > 0) {
            resultMessage = String.format("共导入%d条，成功%d条，失败%d条。失败原因如下：", saleClassList.size(), successNum, failureNum)
                    + failureMsg.toString();
            throw new ServiceException(resultMessage);
        } else {
            resultMessage =  String.format("恭喜您，数据已全部导入成功！共 %d 条", successNum);
        }
        return resultMessage;
    }

    @Override
    public void copySaleClass(PrdtSaleClassCopyReqVO vo) {

        // 验证允不允许使用本地商品
        AppletBaseConfigDTO appletBaseConfigDTO = productCacheService.getAppletBaseConfigDTO(SecurityUtils.getLoginUser().getSysCode());
        if (Objects.nonNull(appletBaseConfigDTO) && StringPool.ONE.equals(appletBaseConfigDTO.getSaleClassSwitch())) {
            throw exception(SYS_PARTNER_CONFIG_GLOBAL_DISABLE);
        }
        //复制前的全国展示分类id
        Long copySaleClassId = vo.getCopySaleClassId();

        //复制后的目标全国展示分类上级id
        Long targetSaleClassPid = vo.getTargetSaleClassPid();

        //单个复制
        //校验 目标父级分类ID、复制分类ID是否存在
        if(ToolUtil.isEmpty(copySaleClassId) || ToolUtil.isEmpty(targetSaleClassPid)){
            throw exception(PRDT_SALE_CLASS_COPY_DATA_NULL);
        }
        //获取 目标父级分类ID、复制分类ID对应的分类信息
        //复制分类
        PrdtSaleClass copyClass = prdtSaleClassMapper.selectById(copySaleClassId);
        //目标父级分类
        PrdtSaleClass targetPidClass = prdtSaleClassMapper.selectById(targetSaleClassPid);


        //校验该目标父级分类下是否已存在该复制分类
        // 验证名称唯一性
        Long count = prdtSaleClassMapper.selectNamecount(
                copyClass.getName(),
                targetSaleClassPid
                ,null);
        if (ObjectUtil.notEqual(NumberPool.LONG_ZERO, count)) {
            throw exception(PRDT_SALE_CLASS_NAME_NOT_UNIQUE);
        }

        //组装新分类数据
        PrdtSaleClass insertClass = assembleCopyClass(copyClass, targetPidClass, targetSaleClassPid);

        //新增
        prdtSaleClassMapper.insert(insertClass);

        //清除相关缓存
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        saleClassDtoListCache.remove(sysCode);
    }

    /**
     * 复制分类 - 组装新分类数据
     *
     * @param copyClass          复制前的分类信息
     * @param targetPidClass     目标父级分类信息
     * @param targetSaleClassPid 目标父级分类ID
     * @return
     */
    private PrdtSaleClass assembleCopyClass(PrdtSaleClass copyClass,PrdtSaleClass targetPidClass,Long targetSaleClassPid){
        //设置新增参数
        PrdtSaleClass insertClass = new PrdtSaleClass();
        insertClass.setPid(targetSaleClassPid);
        insertClass.setName(copyClass.getName());
        insertClass.setIcon(copyClass.getIcon());
        insertClass.setSort(copyClass.getSort());
        insertClass.setStatus(copyClass.getStatus());
        //设置等级 如果pid是0 则是最高级 新增的分类为一级分类
        if(NumberPool.LONG_ZERO == targetSaleClassPid){
            insertClass.setLevel(NumberPool.INT_ONE);
        }else{
            insertClass.setLevel(targetPidClass.getLevel() + NumberPool.INT_ONE);
        }

        //设置默认配置
        //删除标识 默认存在
        insertClass.setDelFlag(StringPool.ZERO);
        //平台商城市分组id默认 空

        //校验级别
        if (insertClass.getLevel().compareTo(NumberPool.INT_THREE) > NumberPool.INT_ZERO) {
            throw exception(PRDT_SALE_CLASS_LEVEL_EXCEED_MAX);
        }

        return insertClass;
    }

    private void validateSaleClassImportExcel(ProductSaleClassImportExcel itemData, int cellNumber) throws Exception {
        if (StringUtils.isEmpty(itemData.getPrimaryCategoryName()) || itemData.getPrimaryCategoryName().length() > 20) {
            throw new Exception(StringUtils.format("第{}行数据一级类别名称必填且长度不能超过20个字符", cellNumber));
        }
        if (StringUtils.isEmpty(itemData.getSecondaryCategoryName()) || itemData.getSecondaryCategoryName().length() > 20) {
            throw new Exception(StringUtils.format("第{}行数据二级类别名称必填且长度不能超过20个字符", cellNumber));
        }
        if (StringUtils.isEmpty(itemData.getTertiaryCategoryName()) || itemData.getTertiaryCategoryName().length() > 20) {
            throw new Exception(StringUtils.format("第{}行数据三级类别名称必填且长度不能超过20个字符", cellNumber));
        }
    }
    private Long processCategory(String categoryName, int level, Map<String, Long> categoryMap, Long pid,Map<String, Long> levelSortMap) throws Exception {
        PrdtSaleClass existingCategory;

        //检查一级类别是否已经存在
        if(level == 1){
            Long aLong = categoryMap.get(categoryName);
            if(aLong != null){
                return aLong;
            }
        }

        // 检查二级和三级类别在同一父级下是否存在
        existingCategory = prdtSaleClassMapper.checkTertiaryCategoryUniquenessInSecondaryCategory(categoryName, pid);
        if (existingCategory != null) {
            return existingCategory.getSaleClassId();
        }

        // 构建用于排序的键，以确保排序号在相同父类别和区域下是唯一的
        String sortKey = level + "_" + (pid != null ? pid : "root");
        Long sortOrder = levelSortMap.getOrDefault(sortKey, 0L) + 1L;
        levelSortMap.put(sortKey, sortOrder);  // 更新当前级别和父级别的排序号

        if (categoryMap.containsKey(categoryName)) {
            return categoryMap.get(categoryName);
        }

        PrdtSaleClassSaveReqVO createReqVO = new PrdtSaleClassSaveReqVO();
        if(level == 1){
            createReqVO.setPid(0L);
        }else{
            createReqVO.setPid(pid);
        }
        createReqVO.setName(categoryName);
        createReqVO.setStatus(1L); //状态
        createReqVO.setLevel(level); //级别
        createReqVO.setSort(sortOrder); //排序号
        //插入全国展示类别
        Long aLong = insertPrdtSaleClass(createReqVO);
        if(level == 1){
            categoryMap.put(categoryName, aLong);
        }
        return aLong;
    }

    @Override
    public void removeMultilevelClasses(Long[] saleClassIds) {
        //一键删除父类及子类。 注意，必须对父类即子类做上架商品判定，存在上架商品需要跳过当前分类和上一级父类。

        //转换为集合
        List<Long> saleClassIdList = ListUtil.toList(saleClassIds);

        //存在上架商品需要过滤的三级展示ID集合
        Set<Long> saleItemClassIdSet = new HashSet<>();

        //展示分类Map集合
        Map<Long, PrdtSaleClass> prdtSaleClassMap = new HashMap<>();


        //根据全国展示分类ID集合 获取全国上架商品集合
        List<PrdtSupplierItem> prdtSupplierItemList = supplierItemMapper.selectBySaleClassIdList(saleClassIdList);

        //如果上架商品集合存在 则查询展示分类集合 进行组装数据
        if(!prdtSupplierItemList.isEmpty()){
            prdtSaleClassMap = prdtSaleClassMapper.selectBatchIds(saleClassIdList).stream().collect(Collectors.toMap(PrdtSaleClass::getSaleClassId, x-> x));

            //组装成set集合 去除重复数据
            saleItemClassIdSet = prdtSupplierItemList.stream().map(PrdtSupplierItem::getSaleClassId).collect(Collectors.toSet());
        }

        //过滤 已存在上架数据的三级展示分类及父类
        for (Long x : saleItemClassIdSet) {
            //三级展示分类
            PrdtSaleClass thirdClass = prdtSaleClassMap.get(x);

            //二级展示分类
            PrdtSaleClass secondClass = prdtSaleClassMap.get(thirdClass.getPid());

            //一级展示分类
            PrdtSaleClass firstClass = prdtSaleClassMap.get(secondClass.getPid());

            //过滤
            saleClassIdList.remove(x);
            saleClassIdList.remove(secondClass.getSaleClassId());
            saleClassIdList.remove(firstClass.getSaleClassId());
        }

        //删除展示分类集合
        if(!saleClassIdList.isEmpty()){
            saleClassIdList.forEach(this::deleteClassAndRelevance);
        }else{
            throw exception(PRDT_SALE_CLASS_REMOVE_MULTILEVE);
        }
    }

    private void validatePrdtSaleClassExists(Long saleClassId) {
        if (prdtSaleClassMapper.selectById(saleClassId) == null) {
            throw exception(PRDT_SALE_CLASS_NOT_EXISTS);
        }
    }

    /**
     * @Description: 校验名称是否唯一
     * @Param: String name 名称, Integer pid 父Id
     * @Author: liuxingyu
     * @Date: 2024/2/27 15:57
     */
    private void validate(PrdtSaleClassSaveReqVO saleClassSaveReqVO) {
        Long count = prdtSaleClassMapper.selectNamecount(saleClassSaveReqVO.getName(), saleClassSaveReqVO.getPid(), saleClassSaveReqVO.getSaleClassId());
        if (ObjectUtil.notEqual(NumberPool.LONG_ZERO, count)) {
            throw exception(PRDT_SALE_CLASS_NAME_NOT_UNIQUE);
        }
        // 验证允不允许使用本地商品
        AppletBaseConfigDTO appletBaseConfigDTO = productCacheService.getAppletBaseConfigDTO(SecurityUtils.getLoginUser().getSysCode());
        if (Objects.nonNull(appletBaseConfigDTO) && StringPool.ONE.equals(appletBaseConfigDTO.getSaleClassSwitch())) {
            throw exception(SYS_PARTNER_CONFIG_GLOBAL_DISABLE);
        }
    }

    /**
     * @Description: 校验级别
     * @Author: liuxingyu
     * @Date: 2024/4/1 15:56
     */
    private void checkSaleClassLevel(PrdtSaleClassSaveReqVO createReqVO) {
        //ID不为空则修改
        if (ObjectUtil.isNotNull(createReqVO.getSaleClassId())) {
            PrdtSaleClass prdtSaleClass = prdtSaleClassMapper.selectById(createReqVO.getSaleClassId());
            if (ObjectUtil.notEqual(nullToZero(createReqVO.getPid()), nullToZero(prdtSaleClass.getPid()))) {
                //如果当前分类非末级则不允许改变层级(存在子级)
                List<PrdtSaleClass> list = prdtSaleClassMapper.getListByPid(createReqVO.getSaleClassId());
                if (ObjectUtil.isNotEmpty(list)) {
                    throw exception(PRDT_SALE_CLASS_IS_SUB_EXISTS);
                }
                List<PrdtSupplierItem> supplierItems = supplierItemMapper.selectBySaleClassId(createReqVO.getSaleClassId());
                if (ObjectUtil.isNotEmpty(supplierItems)) {
                    throw exception(PRDT_SALE_CLASS_BIND_ITEM_EXISTS);
                }
            }
        }
        //获取父级类别
        if (ObjectUtil.isNotNull(createReqVO.getPid()) && ObjectUtil.notEqual(createReqVO.getPid(), NumberPool.LONG_ZERO)) {
            PrdtSaleClass prdtSaleClass = prdtSaleClassMapper.selectById(createReqVO.getPid());
            if (ObjectUtil.isNull(prdtSaleClass) || ObjectUtil.isNull(prdtSaleClass.getLevel())) {
                throw exception(PRDT_SALE_CLASS_PID_ERROR);
            }
            //校验级别
            if ((ObjectUtil.equal(prdtSaleClass.getLevel(), NumberPool.INT_THREE))) {
                throw exception(PRDT_SALE_CLASS_LEVEL_EXCEED_MAX);
            }
            createReqVO.setLevel(prdtSaleClass.getLevel() + NumberPool.INT_ONE);
        } else {
            createReqVO.setLevel(NumberPool.INT_ONE);
        }
    }

    /**
     * @Description: 同步子级
     * @Author: liuxingyu
     * @Date: 2024/6/6 15:49
     */
    private void synSublevel(PrdtSaleClassSaveReqVO updateReqVO) {
        Integer level = updateReqVO.getLevel();
        //同步二级
        List<PrdtSaleClass> saleClassList = prdtSaleClassMapper.getListByPid(updateReqVO.getSaleClassId());
        if (ObjectUtil.isEmpty(saleClassList)) {
            return;
        }
        ++level;
        if (level > NumberPool.INT_ONE) {
            throw exception(PRDT_AREA_CLASS_SUB_LEVEL_MAX);
        }
        Integer level2 = level;
        saleClassList.forEach(x -> {
            x.setLevel(level2);
        });
        prdtSaleClassMapper.updateBatch(saleClassList);
        //同步三级
        List<Long> ids = saleClassList.stream().map(PrdtSaleClass::getSaleClassId).collect(Collectors.toList());
        List<PrdtSaleClass> saleClassList3 = prdtSaleClassMapper.selectByPIds(ids);
        if (ObjectUtil.isNotEmpty(saleClassList3)) {
            ++level;
            if (level > NumberPool.INT_ONE) {
                throw exception(PRDT_AREA_CLASS_SUB_LEVEL_MAX);
            }
            Integer level3 = level;
            saleClassList3.forEach(y -> {
                y.setLevel(level3);
            });
            prdtSaleClassMapper.updateBatch(saleClassList3);
        }
    }

    /**
     * @Description: 如果为null 转换成0
     * @Author: liuxingyu
     * @Date: 2024/6/7 11:19
     */
    private Long nullToZero(Long pid) {
        return ObjectUtil.isNull(pid) ? 0L : pid;
    }

    @Override
    public List<SaleClassExportVo> getSaleClassExportList(SaleClassExportVo pageVo) {
        List<SaleClassExportVo> saleClassExportList = prdtSaleClassMapper.selectSaleClassExportList(pageVo);
        for (SaleClassExportVo saleClassExportVo : saleClassExportList) {
            PrdtSaleClass prdtSaleClass = this.getPrdtSaleClass(saleClassExportVo.getPid());
            if (ToolUtil.isNotEmpty(prdtSaleClass)){
                saleClassExportVo.setPidName(prdtSaleClass.getName());
            }
        }
        return saleClassExportList;
    }


}
