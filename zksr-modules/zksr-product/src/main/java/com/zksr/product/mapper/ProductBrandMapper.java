package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.controller.brand.vo.ProductBrandPageReqVO;
import com.zksr.product.domain.ProductBrand;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.pool.StringPool.LIMIT_ONE;


/**
 * 平台品牌Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
@Mapper
public interface ProductBrandMapper extends BaseMapperX<ProductBrand> {
    default PageResult<ProductBrand> selectPage(ProductBrandPageReqVO reqVO) {
        LambdaQueryWrapperX<ProductBrand> queryWrapperX = new LambdaQueryWrapperX<ProductBrand>()
                .eqIfPresent(ProductBrand::getBrandId, reqVO.getBrandId())
                .eqIfPresent(ProductBrand::getBrandNo, reqVO.getBrandNo())
                .likeIfPresent(ProductBrand::getBrandName, reqVO.getBrandName())
                .eqIfPresent(ProductBrand::getMemo, reqVO.getMemo())
                .eqIfPresent(ProductBrand::getStatus, reqVO.getStatus())
                .orderByDesc(ProductBrand::getBrandId);

        if (Objects.nonNull(reqVO.getIgnoreHashMerchant()) && reqVO.getIgnoreHashMerchant()) {
            queryWrapperX.and(x -> x.isNull(ProductBrand::getBrandMerchantId).or().eq(ProductBrand::getBrandMerchantId, reqVO.getBrandMerchantId()));
        }
        return selectPage(reqVO, queryWrapperX);
    }

    default Long selectCountByBrandIdAndBrandNo(String brandNo, Long brandId) {
        return selectCount(
                Wrappers.lambdaQuery(ProductBrand.class)
                        .eq(ProductBrand::getBrandNo, brandNo)
                        .ne(Objects.nonNull(brandId), ProductBrand::getBrandId, brandId)
        );
    }

    default Long selectCountByBrandIdAndBrandName(String branName, Long brandId) {
        return selectCount(
                Wrappers.lambdaQuery(ProductBrand.class)
                        .eq(ProductBrand::getBrandName, branName)
                        .ne(Objects.nonNull(brandId), ProductBrand::getBrandId, brandId)
        );
    }

    default ProductBrand selectBrandByBrandName(String brandName) {
        return selectOne(
                new LambdaQueryWrapperX<ProductBrand>()
                        .eq(ProductBrand::getBrandName, brandName)
                        .last(LIMIT_ONE)
        );
    }

    /**
     * @Description: 根据id集合获取品牌信息
     * @Author: liuxingyu
     * @Date: 2024/5/18 15:17
     */
    default List<ProductBrand> getBrandByIds(List<Long> brandIds) {
        return selectList(
                new LambdaQueryWrapperX<ProductBrand>()
                        .in(ProductBrand::getBrandId, brandIds)
        );
    }

    /**
     * 置空品牌绑定品牌商
     *
     * @param brandMerchantId 品牌商ID
     */
    default void setNullMerchantId(Long brandMerchantId) {
        update(
                ProductBrand.builder()
                        .build(),
                new UpdateWrapper<ProductBrand>()
                        .lambda()
                        .set(ProductBrand::getBrandMerchantId, null)
                        .eq(ProductBrand::getBrandMerchantId, brandMerchantId));
    }

    default  ProductBrand selectByDcIdAndBrandName(Long sysCode, String brandName){
        return selectOne(
                new LambdaQueryWrapperX<ProductBrand>()
                        .eq(ProductBrand::getSysCode, sysCode)
                        .eq(ProductBrand::getBrandName, brandName)
                        .last(LIMIT_ONE)
        );
    }
}
