package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtGroupSaleClass;
import com.zksr.product.controller.groupSaleClass.vo.PrdtGroupSaleClassPageReqVO;


/**
 * 平台商城市分组-展示分类关联Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Mapper
public interface PrdtGroupSaleClassMapper extends BaseMapperX<PrdtGroupSaleClass> {
    default PageResult<PrdtGroupSaleClass> selectPage(PrdtGroupSaleClassPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtGroupSaleClass>()
                    .eqIfPresent(PrdtGroupSaleClass::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtGroupSaleClass::getGroupId, reqVO.getGroupId())
                    .eqIfPresent(PrdtGroupSaleClass::getSaleClassId, reqVO.getSaleClassId())
                .orderByDesc(PrdtGroupSaleClass::getSysCode));
    }

    /**
    * @Description: 通过展示分类Id解除绑定(删除)与城市分组关系
    * @Author: liuxingyu
    * @Date: 2024/3/12 10:43
    */
    default Integer removeBindBySaleClassId(Long saleClassId){
        return delete(new LambdaUpdateWrapper<PrdtGroupSaleClass>().eq(PrdtGroupSaleClass::getSaleClassId,saleClassId));
    }
}
