package com.zksr.product.controller.blockScheme.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

@Data
@ApiModel(description = "用于根据Excel获取客户信息")
public class BlockBranchExcelToListVO {
    @Excel(name = "客户编码", headerColor = IndexedColors.RED, headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String branchId;
    @Excel(name = "客户名称", headerColor = IndexedColors.BLACK, headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String branchName;
}
