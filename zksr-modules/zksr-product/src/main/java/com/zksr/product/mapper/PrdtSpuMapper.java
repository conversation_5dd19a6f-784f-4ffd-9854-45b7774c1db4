package com.zksr.product.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.api.spu.dto.SpuExportDTO;
import com.zksr.product.api.spu.vo.PrdtProductPageReqVO;
import com.zksr.product.api.spu.vo.PrdtSpuNotItemPageReqExportVo;
import com.zksr.product.api.spu.vo.PrdtSpuNotItemPageReqVo;
import com.zksr.product.controller.spu.vo.PrdtProductRespVO;
import com.zksr.product.api.spu.vo.PrdtSpuPageReqVO;
import com.zksr.product.controller.spu.vo.PrdtSpuRespVO;
import com.zksr.product.controller.spu.vo.PrdtSpuUniquePageRespVO;
import com.zksr.product.domain.PrdtSpu;
import com.zksr.product.domain.dto.BoundProductInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.*;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_SPU_EXISTS;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_SPU_SPUNO_EXISTS;


/**
 * 商品SPUMapper接口
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Mapper
@SuppressWarnings("all")
public interface PrdtSpuMapper extends BaseMapperX<PrdtSpu> {
    default PageResult<PrdtSpu> selectPage(PrdtSpuPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtSpu>()
                .eqIfPresent(PrdtSpu::getSpuId, reqVO.getSpuId())
                .eqIfPresent(PrdtSpu::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrdtSpu::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(PrdtSpu::getCatgoryId, reqVO.getCatgoryId())
                .eqIfPresent(PrdtSpu::getBrandId, reqVO.getBrandId())
                .eqIfPresent(PrdtSpu::getCostPrice, reqVO.getCostPrice())
                .eqIfPresent(PrdtSpu::getSpuNo, reqVO.getSpuNo())
                .likeIfPresent(PrdtSpu::getSpuName, reqVO.getSpuName())
                .eqIfPresent(PrdtSpu::getThumb, reqVO.getThumb())
                .eqIfPresent(PrdtSpu::getThumbVideo, reqVO.getThumbVideo())
                .eqIfPresent(PrdtSpu::getImages, reqVO.getImages())
                .eqIfPresent(PrdtSpu::getDetails, reqVO.getDetails())
                .eqIfPresent(PrdtSpu::getStock, reqVO.getStock())
                .eqIfPresent(PrdtSpu::getIsDelete, reqVO.getIsDelete())
                .orderByDesc(PrdtSpu::getSpuId));
    }

    //Spu分页查询
    public Page<PrdtSpuPageReqVO> selectPageSpu(@Param("reqVO") PrdtSpuPageReqVO reqVO, @Param("page") Page<PrdtSpuPageReqVO> page);

    //获得上下架管理分页查询未上架商品SPU列表
    public Page<PrdtSpuNotItemPageReqVo> selectPageSpuByNotItem(@Param("reqVO") PrdtSpuNotItemPageReqVo reqVO, @Param("page") Page<PrdtSpuNotItemPageReqVo> page);

    public List<PrdtSpuNotItemPageReqVo> selectPageSpuByNotItem(@Param("reqVO") PrdtSpuNotItemPageReqVo reqVO);

    //根据查询条件查询Spu对象
    default PrdtSpu selectSpuBySpu(PrdtSpuRespVO vo) {
        LambdaQueryWrapperX<PrdtSpu> wrapper = new LambdaQueryWrapperX<PrdtSpu>()
                .eqIfPresent(PrdtSpu::getSpuNo, vo.getSpuNo())
                .eqIfPresent(PrdtSpu::getSpuId, vo.getSpuId())
                .eqIfPresent(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0);
        return selectOne(wrapper);
    }

    //校验是否Spu //根据spu_id 、名称、是否删除查询
    default Long selectSpuByCheck(PrdtSpu spu) {
        //校验是否规格重名
        LambdaQueryWrapperX<PrdtSpu> wrapper = new LambdaQueryWrapperX<PrdtSpu>()
                .eq(PrdtSpu::getSpuNo, spu.getSpuNo())
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
                .eq(PrdtSpu::getStatus, PRDT_STATUS_1);
        return selectCount(wrapper);
    }

    default Long insertPrdtSpu(PrdtSpu spu) {
        //校验是否规格重名
        Long checkPrdtSpu = selectSpuByCheck(spu);
        if (ToolUtil.isNotEmpty(checkPrdtSpu) && checkPrdtSpu > 1) {
            throw exception(PRDT_SPU_SPUNO_EXISTS);
        }
        //设置默认值
        spu.setIsDelete(PRDT_IS_DELETE_0);
        if (ToolUtil.isEmpty(spu.getStatus())) {
            spu.setStatus(PRDT_STATUS_1);
        }
        // 插入
        insert(spu);
        // 返回
        return spu.getSpuId();
    }

    default Long insertPrdtSpuOpen(PrdtSpu spu) {
        //设置默认值
        spu.setIsDelete(PRDT_IS_DELETE_0);
        if (ToolUtil.isEmpty(spu.getStatus())) {
            spu.setStatus(PRDT_STATUS_1);
        }
        // 插入
        insert(spu);
        // 返回
        return spu.getSpuId();
    }

    /**
     * 批量获取SPU信息 (展示用于多选数据回显)
     *
     * @param spuIds spuId 集合
     * @return 集合
     */
    default List<PrdtSpu> selectSelectedPrdtSpu(List<Long> spuIds) {
        LambdaQueryWrapper<PrdtSpu> spuLambdaQueryWrapper = new LambdaQueryWrapperX<PrdtSpu>()
                .select(PrdtSpu::getSpuName, PrdtSpu::getSpuId)
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
                .in(PrdtSpu::getSpuId, spuIds);
        return selectList(spuLambdaQueryWrapper);
    }

    /**
     * @Description: 获取Spu信息
     * @Author: liuxingyu
     * @Date: 2024/4/11 16:51
     */
    default List<PrdtSpu> getByProduct(String param) {
        return selectList(new LambdaQueryWrapper<PrdtSpu>()
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
                .and(and ->
                        and.like(PrdtSpu::getSpuNo, param)
                                .or()
                                .like(PrdtSpu::getSpuName, param))
        );
    }

    /**
     * @Description: 获取产品下拉
     * @Author: liuxingyu
     * @Date: 2024/4/12 9:11
     */
    Page<PrdtProductRespVO> getProductDropdown(@Param("pageReqVO") PrdtProductPageReqVO pageReqVO, @Param("page") Page<PrdtProductPageReqVO> page);

    /**
     * @Description: 根据平台管理分类ID获取spu集合
     * @Author: liuxingyu
     * @Date: 2024/6/7 9:04
     */
    default List<PrdtSpu> selectByCatGoryId(Long catgoryId) {
        return selectList(new LambdaQueryWrapper<PrdtSpu>()
                .eq(PrdtSpu::getCatgoryId, catgoryId)
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0));
    }

    List<PrdtSpuUniquePageRespVO> selectPrdtSpuUniquePage(PrdtSpuPageReqVO pageReqVO);

    @InterceptorIgnore(tenantLine = "1")
    default Long selectCountByPSpuId(Long spuId) {
        return selectCount(new LambdaQueryWrapper<PrdtSpu>().eq(PrdtSpu::getPSpuId, spuId));
    }

    default Long selectCountBySpuNo(String spuNo, Long supplierId) {
        return selectCount(new LambdaQueryWrapper<PrdtSpu>()
                .eq(PrdtSpu::getSpuNo, spuNo)
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
                .eq(PrdtSpu::getSupplierId, supplierId));
    }

    default Long countByCategoryIdAndSysCode(Long catgoryId, Long sysCode) {
        return selectCount(new LambdaQueryWrapper<PrdtSpu>()
                .eq(PrdtSpu::getCatgoryId, catgoryId)
                .eq(PrdtSpu::getSysCode, sysCode)
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0));
    }

    default PrdtSpu selectBySpuNo(String spuNo, Long supplierId) {
        //校验是否规格重名
        LambdaQueryWrapperX<PrdtSpu> wrapper = new LambdaQueryWrapperX<PrdtSpu>()
                .eq(PrdtSpu::getSpuNo, spuNo)
                .eq(PrdtSpu::getSupplierId, supplierId)
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0);
        List<PrdtSpu> res = selectList(wrapper);
        return CollectionUtils.isEmpty(res) ? null : res.get(0);
//        return selectOne(wrapper);
    }

    List<BoundProductInfoDTO> getBoundProductInfoByCategoryIdAndSysCode(@Param("categoryId") Long categoryId, @Param("sysCode") Long sysCode);

    default boolean existsBySourceNoOrSource(String sourceNo, Long supplierId, String source) {
        Long aLong = selectCount(new LambdaQueryWrapper<PrdtSpu>()
                .eq(PrdtSpu::getSourceNo, sourceNo)
                .eq(PrdtSpu::getSupplierId, supplierId)
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
                .eq(PrdtSpu::getSource, source));
        return aLong > 0;
    }

    ;

    default PrdtSpu getBySourceNoOrSource(String sourceNo, Long supplierId, String source) {
        //校验是否规格重名
        LambdaQueryWrapperX<PrdtSpu> wrapper = new LambdaQueryWrapperX<PrdtSpu>()
                .eq(PrdtSpu::getSourceNo, sourceNo)
                .eq(PrdtSpu::getSupplierId, supplierId)
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
                .eq(PrdtSpu::getSource, source);
        return selectOne(wrapper);
    }

    ;

    default PrdtSpu selectBySpuNoAndSupplierId(String spuNo, Long supplierId) {
        return selectOne(new LambdaQueryWrapper<PrdtSpu>()
                .eq(PrdtSpu::getSpuNo, spuNo)
                .eq(PrdtSpu::getSupplierId, supplierId)
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0));
    }

    /**
     * 导出列表数据查询
     *
     * @param reqVO
     * @return
     */
    public List<SpuExportDTO> selectSpuExportList(@Param("reqVO") PrdtSpuPageReqVO reqVO);

    default void deleteSpuList(Long[] spuIds) {
        update(null, new LambdaUpdateWrapper<PrdtSpu>()
                .set(PrdtSpu::getIsDelete, PRDT_IS_DELETE_1)
                .in(PrdtSpu::getSpuId, spuIds));

    }

    default List<PrdtSpu> selectListBySpuNoAndSupplierId(String spuNo, Long supplierId) {
        LambdaQueryWrapperX<PrdtSpu> wrapper = new LambdaQueryWrapperX<PrdtSpu>()
                .eq(PrdtSpu::getSourceNo, spuNo)
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
                .eq(PrdtSpu::getSupplierId, supplierId);
        return selectList(wrapper);
    }

    /**
     * 根据关键字查询关联的spu关联的数量
     *
     * @param keyword
     * @return
     */
    default Long selectCountByKeyword(String keyword) {
        return selectCount(new LambdaQueryWrapper<PrdtSpu>()
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
                .like(PrdtSpu::getKeywords, keyword));
    }

    default PrdtSpu getBysysCodeAndSupplierId(Long supplierId, String sourceNo, String source) {
        LambdaQueryWrapper<PrdtSpu> spuQueryWrapper = new LambdaQueryWrapper<>();
        spuQueryWrapper.eq(PrdtSpu::getSourceNo, sourceNo)
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
                .eq(PrdtSpu::getSupplierId, supplierId)
                .eq(PrdtSpu::getSource, source);

        return this.selectOne(spuQueryWrapper);
    }

    default List<PrdtSpu> getBySupplierIdAndSourceNos(Long supplierId, List<String> sourceNos, String source) {
        LambdaQueryWrapper<PrdtSpu> spuQueryWrapper = new LambdaQueryWrapper<>();
        spuQueryWrapper.in(PrdtSpu::getSourceNo, sourceNos)
                .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
                .eq(PrdtSpu::getSupplierId, supplierId)
                .eq(PrdtSpu::getSource, source);

        return this.selectList(spuQueryWrapper);
    }
}
