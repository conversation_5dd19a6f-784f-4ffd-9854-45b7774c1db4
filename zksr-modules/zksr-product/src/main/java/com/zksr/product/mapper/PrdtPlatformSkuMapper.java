package com.zksr.product.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.domain.PrdtSku;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtPlatformSku;
import com.zksr.product.controller.platform.vo.PrdtPlatformSkuPageReqVO;

import java.util.List;

import static com.zksr.product.constant.ProductConstant.PRDT_IS_DELETE_0;
import static com.zksr.product.constant.ProductConstant.PRDT_IS_DELETE_1;


/**
 * 平台商品库-商品SKUMapper接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Mapper
public interface PrdtPlatformSkuMapper extends BaseMapperX<PrdtPlatformSku> {
    default PageResult<PrdtPlatformSku> selectPage(PrdtPlatformSkuPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtPlatformSku>()
                    .eqIfPresent(PrdtPlatformSku::getPlatformSkuId, reqVO.getPlatformSkuId())
                    .eqIfPresent(PrdtPlatformSku::getPlatformSpuId, reqVO.getPlatformSpuId())
                    .eqIfPresent(PrdtPlatformSku::getUnit, reqVO.getUnit())
                    .eqIfPresent(PrdtPlatformSku::getBarcode, reqVO.getBarcode())
                    .eqIfPresent(PrdtPlatformSku::getProperties, reqVO.getProperties())
                    .eqIfPresent(PrdtPlatformSku::getThumb, reqVO.getThumb())
                    .eqIfPresent(PrdtPlatformSku::getStatus, reqVO.getStatus())
                    .eqIfPresent(PrdtPlatformSku::getIsDelete, reqVO.getIsDelete())
                    .eqIfPresent(PrdtPlatformSku::getMidBarcode, reqVO.getMidBarcode())
                    .eqIfPresent(PrdtPlatformSku::getLargeBarcode, reqVO.getLargeBarcode())
                .orderByDesc(PrdtPlatformSku::getPlatformSkuId));
    }

    default void deleteByPlatformSpuId(Long platformSpuId) {
        update(
                PrdtPlatformSku.builder().isDelete((int) PRDT_IS_DELETE_1).build(),
                new LambdaQueryWrapperX<PrdtPlatformSku>()
                        .eq(PrdtPlatformSku::getPlatformSpuId, platformSpuId)
        );
    }

    default List<PrdtPlatformSku> selectByPlatformSpuId(Long platformSpuId) {
        return selectList(new LambdaQueryWrapperX<PrdtPlatformSku>()
                .eqIfPresent(PrdtPlatformSku::getPlatformSpuId, platformSpuId)
                .eqIfPresent(PrdtPlatformSku::getIsDelete, PRDT_IS_DELETE_0)
        );
    }
}
