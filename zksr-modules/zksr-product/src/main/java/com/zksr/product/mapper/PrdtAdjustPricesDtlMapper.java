package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.domain.PrdtPlatformPropertyVal;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtAdjustPricesDtl;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesDtlPageReqVO;

import java.util.List;

import static com.zksr.product.constant.ProductConstant.PRDT_IS_DELETE_0;


/**
 * 商品调价单明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Mapper
public interface PrdtAdjustPricesDtlMapper extends BaseMapperX<PrdtAdjustPricesDtl> {
    default PageResult<PrdtAdjustPricesDtl> selectPage(PrdtAdjustPricesDtlPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtAdjustPricesDtl>()
                    .eqIfPresent(PrdtAdjustPricesDtl::getAdjustPricesDtlId, reqVO.getAdjustPricesDtlId())
                    .eqIfPresent(PrdtAdjustPricesDtl::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtAdjustPricesDtl::getSpuId, reqVO.getSpuId())
                    .eqIfPresent(PrdtAdjustPricesDtl::getSkuId, reqVO.getSkuId())
                    .eqIfPresent(PrdtAdjustPricesDtl::getOldLargeMarkPrice, reqVO.getOldLargeMarkPrice())
                    .eqIfPresent(PrdtAdjustPricesDtl::getOldLargeCostPrice, reqVO.getOldLargeCostPrice())
                    .eqIfPresent(PrdtAdjustPricesDtl::getOldMidMarkPrice, reqVO.getOldMidMarkPrice())
                    .eqIfPresent(PrdtAdjustPricesDtl::getOldMidCostPrice, reqVO.getOldMidCostPrice())
                    .eqIfPresent(PrdtAdjustPricesDtl::getOldMinMarkPrice, reqVO.getOldMinMarkPrice())
                    .eqIfPresent(PrdtAdjustPricesDtl::getOldMinCostPrice, reqVO.getOldMinCostPrice())
                    .eqIfPresent(PrdtAdjustPricesDtl::getNewLargeMarkPrice, reqVO.getNewLargeMarkPrice())
                    .eqIfPresent(PrdtAdjustPricesDtl::getNewLargeCostPrice, reqVO.getNewLargeCostPrice())
                    .eqIfPresent(PrdtAdjustPricesDtl::getNewMidMarkPrice, reqVO.getNewMidMarkPrice())
                    .eqIfPresent(PrdtAdjustPricesDtl::getNewMidCostPrice, reqVO.getNewMidCostPrice())
                    .eqIfPresent(PrdtAdjustPricesDtl::getNewMinMarkPrice, reqVO.getNewMinMarkPrice())
                    .eqIfPresent(PrdtAdjustPricesDtl::getNewMinCostPrice, reqVO.getNewMinCostPrice())
                    .eqIfPresent(PrdtAdjustPricesDtl::getValidState, reqVO.getValidState())
                    .eqIfPresent(PrdtAdjustPricesDtl::getValidTime, reqVO.getValidTime())
                .orderByDesc(PrdtAdjustPricesDtl::getAdjustPricesDtlId));
    }

    /**
     * @Description: 根据主单号删除明细数据
     * @Author: chenmingqing
     * @Date: 2024/11/2 11:04
     */
    default Integer deleteByAdjustPricesId(Long adjustPricesId){
        return delete(new LambdaUpdateWrapper<PrdtAdjustPricesDtl>().eq(PrdtAdjustPricesDtl::getAdjustPricesId,adjustPricesId));
    }

    /**
     * @Description: 根据主单号查询明细数据
     * @Author: chenmingqing
     * @Date: 2024/11/2 11:04
     */
    default List<PrdtAdjustPricesDtl> selectByAdjustPricesId(Long adjustPricesId){
        return selectList(new LambdaQueryWrapperX<PrdtAdjustPricesDtl>()
                .eqIfPresent(PrdtAdjustPricesDtl::getAdjustPricesId, adjustPricesId));
    }
}
