package com.zksr.product.controller.catgoryRate;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRatePageReqVO;
import com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRateRespVO;
import com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRateSaveReqVO;
import com.zksr.product.domain.PrdtCatgoryRate;
import com.zksr.product.service.IPrdtCatgoryRateService;
import com.zksr.product.service.IPrdtCatgoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 城市级管理分类扣点设置Controller
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Api(tags = "管理后台 - 城市级管理分类扣点设置接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/catgoryRate")
public class PrdtCatgoryRateController {
    @Autowired
    private IPrdtCatgoryRateService prdtCatgoryRateService;

    @Autowired
    private IPrdtCatgoryService prdtCatgoryService;

    /**
     * @Description: 获取设置扣点列表
     * @Param: PrdtCatgoryRateRespVO respVO
     * @return: CommonResult<List<PrdtCatgoryRateRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/3/19 9:40
     */
    @ApiOperation(value = "获取设置扣点列表", httpMethod = HttpMethod.GET, notes = "product:catgoryRate:getCatgoryRateList")
    @RequiresPermissions("product:catgoryRate:getCatgoryRateList")
    @GetMapping("/getCatgoryRateList")
    public CommonResult<List<PrdtCatgoryRateRespVO>> getCatgoryRateList(PrdtCatgoryRateRespVO respVO) {
        return success(prdtCatgoryRateService.getCatgoryRateList(respVO));
    }

    /**
     * @Description: 运营商获取商品管理分类详情(城市级管理分类扣点设置)
     * @Param: Long areaId 城市ID,Long catgoryId 管理分类ID
     * @return: CommonResult<PrdtCatgoryRespVO>
     * @Author: liuxingyu
     * @Date: 2024/3/15 15:25
     */
    @ApiOperation(value = "运营商获取商品管理分类详情", httpMethod = HttpMethod.GET, notes = "product:catgoryRate:getRateInfo")
    @RequiresPermissions("product:catgoryRate:getRateInfo")
    @GetMapping("/getRateInfo")
    public CommonResult<PrdtCatgoryRateRespVO> getRateInfo(@ApiParam(name = "ApiParam", value = "城市ID", required = true) @RequestParam("areaId") Long areaId,
                                                           @ApiParam(name = "catgoryId", value = "管理分类ID", required = true) @RequestParam("catgoryId") Long catgoryId) {
        return success(prdtCatgoryRateService.getRateInfo(areaId, catgoryId));
    }

    /**
     * @Description: 商品类别设置二级扣点
     * @Param: PrdtCatgorySaveReqVO saveReqVO
     * @return: CommonResult<Long>
     * @Author: liuxingyu
     * @Date: 2024/3/15 9:26
     */
    @ApiOperation(value = "商品类别设置二级扣点", httpMethod = HttpMethod.POST, notes = "product:catgoryRate:installRate")
    @RequiresPermissions("product:catgoryRate:installRate")
    @PostMapping("/installRate")
    @Log(title = "平台商管理分类", businessType = BusinessType.INSERT)
    public CommonResult<Long> installRate(@RequestBody PrdtCatgoryRateSaveReqVO saveReqVO) {
        return success(
                prdtCatgoryService.cleanRateCache(
                    prdtCatgoryRateService.installRate(saveReqVO)
                )
        );
    }

    /**
     * 新增城市级管理分类扣点设置
     */
    @ApiOperation(value = "新增城市级管理分类扣点设置", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "城市级管理分类扣点设置", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtCatgoryRateSaveReqVO createReqVO) {
        return success(
                prdtCatgoryService.cleanRateCache(
                        prdtCatgoryRateService.insertPrdtCatgoryRate(createReqVO)
                )
        );
    }

    /**
     * 修改城市级管理分类扣点设置
     */
    @ApiOperation(value = "修改城市级管理分类扣点设置", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "城市级管理分类扣点设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtCatgoryRateSaveReqVO updateReqVO) {
        prdtCatgoryService.cleanRateCache(
                prdtCatgoryRateService.updatePrdtCatgoryRate(updateReqVO)
        );
        return success(true);
    }

    /**
     * 删除城市级管理分类扣点设置
     */
    @ApiOperation(value = "删除城市级管理分类扣点设置", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "城市级管理分类扣点设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{catgoryRateIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] catgoryRateIds) {
        prdtCatgoryRateService.deletePrdtCatgoryRateByCatgoryRateIds(catgoryRateIds);
        return success(true);
    }

    /**
     * 获取城市级管理分类扣点设置详细信息
     */
    @ApiOperation(value = "获得城市级管理分类扣点设置详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{catgoryRateId}")
    public CommonResult<PrdtCatgoryRateRespVO> getInfo(@PathVariable("catgoryRateId") Long catgoryRateId) {
        PrdtCatgoryRate prdtCatgoryRate = prdtCatgoryRateService.getPrdtCatgoryRate(catgoryRateId);
        return success(HutoolBeanUtils.toBean(prdtCatgoryRate, PrdtCatgoryRateRespVO.class));
    }

    /**
     * 分页查询城市级管理分类扣点设置
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得城市级管理分类扣点设置分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtCatgoryRateRespVO>> getPage(@Valid PrdtCatgoryRatePageReqVO pageReqVO) {
        PageResult<PrdtCatgoryRate> pageResult = prdtCatgoryRateService.getPrdtCatgoryRatePage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, PrdtCatgoryRateRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "product:catgoryRate:add";
        /**
         * 编辑
         */
        public static final String EDIT = "product:catgoryRate:edit";
        /**
         * 删除
         */
        public static final String DELETE = "product:catgoryRate:remove";
        /**
         * 列表
         */
        public static final String LIST = "product:catgoryRate:list";
        /**
         * 查询
         */
        public static final String GET = "product:catgoryRate:query";
        /**
         * 停用
         */
        public static final String DISABLE = "product:catgoryRate:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "product:catgoryRate:enable";
    }
}
