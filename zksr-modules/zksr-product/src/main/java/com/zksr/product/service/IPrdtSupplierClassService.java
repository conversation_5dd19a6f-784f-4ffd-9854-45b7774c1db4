package com.zksr.product.service;

import javax.validation.*;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRateDTO;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRespDTO;
import com.zksr.product.api.supplierClass.dto.SupplierClassRespDTO;
import com.zksr.product.controller.supplierClass.vo.PrdtSupplierClassBatchEditReqVO;
import com.zksr.product.controller.supplierClass.vo.SupplierClassRateStatusRespVO;
import com.zksr.product.controller.supplierClass.vo.PrdtSupplierClassPageReqVO;
import com.zksr.product.controller.supplierClass.vo.PrdtSupplierClassSaveReqVO;
import com.zksr.product.domain.PrdtSupplierClass;

import java.util.List;

/**
 * 入驻商-平台商管理分类 关联关系Service接口
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
public interface IPrdtSupplierClassService {

    /**
     * 新增入驻商-平台商管理分类 关联关系
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtSupplierClass(@Valid PrdtSupplierClassSaveReqVO createReqVO);

    /**
     * 修改入驻商-平台商管理分类 关联关系
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtSupplierClass(@Valid PrdtSupplierClassBatchEditReqVO updateReqVO);

    /**
     * 删除入驻商-平台商管理分类 关联关系
     *
     * @param catgoryId 平台商管理分类id;平台商管理分类id
     */
    public void deletePrdtSupplierClass(Long catgoryId);

    /**
     * 批量删除入驻商-平台商管理分类 关联关系
     *
     * @param catgoryIds 需要删除的入驻商-平台商管理分类 关联关系主键集合
     * @return 结果
     */
    public void deletePrdtSupplierClassByCatgoryIds(Long[] catgoryIds);

    /**
     * 获得入驻商-平台商管理分类 关联关系
     *
     * @param catgoryId 平台商管理分类id;平台商管理分类id
     * @return 入驻商-平台商管理分类 关联关系
     */
    public SupplierClassRespDTO getPrdtSupplierClass(Long catgoryId);

    /**
     * 获得入驻商-平台商管理分类 关联关系分页
     *
     * @param pageReqVO 分页查询
     * @return 入驻商-平台商管理分类 关联关系分页
     */
    PageResult<SupplierClassRespDTO> getPrdtSupplierClassPage(PrdtSupplierClassPageReqVO pageReqVO);

    /**
     * @Description: 通过入驻商ID获取绑定的所有管理分类ID
     * @Author: liuxingyu
     * @Date: 2024/3/6 15:19
     */
    List<Long> getCatgoryIdListBySupplierId(Long supplierId);

    /**
     * @Description: 新增入驻商绑定管理类别
     * @Author: liuxingyu
     * @Date: 2024/3/6 16:30
     */
    Boolean insetBath(PrdtSupplierClassRespDTO prdtSupplierClassRespDto);

    /**
    * @Description: 根据入驻商ID修改管理类别绑定关系
    * @Author: liuxingyu
    * @Date: 2024/3/6 17:19
    */
    Boolean updateBySupplierId(PrdtSupplierClassRespDTO respDto);

    /**
     * 获取入驻商一级管理分类销售分润占比
     * @param supplierId
     * @return
     */
    List<PrdtSupplierClassRateDTO> getCatgoryRateListBySupplierId(Long supplierId);

    /**
     * 获取入驻商 + 管理分类 参考佣金
     * @param supplierId    入驻商ID
     * @param catgoryId     管理分类ID
     * @return 比例
     */
    SupplierClassRateStatusRespVO getSupplierClassRate(Long supplierId, Long catgoryId);

    /**
     * 获取入驻商 + 管理分类 分类下商品是否可售后配置
     * @param supplierId
     * @param catgoryIds
     * @return
     */
    List<PrdtSupplierClass> getSupplierClassAfterConfig(List<Long> supplierIds, List<Long> catgoryIds);
}
