package com.zksr.product.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.skuPrice.dto.PrdtSkuPriceInfoExportVO;
import com.zksr.product.api.skuPrice.dto.SkuPriceDTO;
import com.zksr.product.api.skuPrice.excel.PrdtSkuPriceImportExportDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.controller.skuPrice.vo.PrdtSkuPriceRespVO;
import com.zksr.product.controller.skuPrice.vo.PrdtSkuPriceSaveInVo;
import com.zksr.product.controller.skuPrice.vo.*;
import com.zksr.product.api.propertyVal.vo.PrdtPropertyAndValVO;
import com.zksr.product.domain.PrdtSku;
import com.zksr.product.enums.PrdtSkuPriceOptionType;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtSkuPriceMapper;
import com.zksr.product.domain.PrdtSkuPrice;
import com.zksr.product.service.IPrdtSkuPriceService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.pool.NumberPool.INT_TWO;
import static com.zksr.product.constant.ProductConstant.*;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * sku销售价Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Service
@Slf4j
public class PrdtSkuPriceServiceImpl implements IPrdtSkuPriceService {
    @Autowired
    private PrdtSkuPriceMapper prdtSkuPriceMapper;

    // 入驻商信息调用服务
    @Autowired
    private SupplierApi remoteSupplierApi;

    @Autowired
    private IProductCacheService productCacheService;

    @Autowired
    private Cache<String, SkuPriceDTO> skuPriceDTOByAreaTypeCache;
    @Autowired
    private PrdtSkuServiceImpl prdtSkuService;
    @Resource
    @Lazy
    private PrdtSkuPriceServiceImpl prdtSkuPriceService;

    @Resource
    private DcApi dcApi;

    /**
     * 新增sku销售价
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PrdtSkuPriceSaveReqVO> insertPrdtSkuPrice(@Valid PrdtSkuPriceSaveInVo createReqVO) {
        //组装新增数据
        List<PrdtSkuPriceSaveReqVO> saveReqVo = assembleSaveDate(createReqVO);

/*        //用于清除缓存的key
        Set<String> key = new HashSet<>();

        //城市ID
        Long areaId = createReqVO.getAreaId();

        //价格方案类型
        Integer type = createReqVO.getType();*/

        //校验新增数据 配送价必须大于成本价
        saveReqVo.forEach(checkVo ->{
            //获取sku信息
            SkuDTO skuDTO = productCacheService.getSkuDTO(checkVo.getSkuId());
            if(ToolUtil.isNotEmpty(skuDTO) && (skuDTO.getCostPrice() != null || skuDTO.getMidCostPrice() != null || skuDTO.getLargeCostPrice() != null)){
                checkVo.setCostPrice(skuDTO.getCostPrice() != null ? skuDTO.getCostPrice() : null);
                checkVo.setMidCostPrice(skuDTO.getMidCostPrice() != null ? skuDTO.getMidCostPrice() : null);
                checkVo.setLargeCostPrice(skuDTO.getLargeCostPrice() != null ? skuDTO.getLargeCostPrice() : null);
                if(!checkVo.isValid()) throw exception(PRDT_SKU_CHECK_MARK_PRICE);
            }

/*            //组装Key
            key.add(areaId + "-" + skuDTO.getSkuId() + "-" + type);*/

        });

        // 插入
        List<PrdtSkuPrice> prdtSkuPrice = HutoolBeanUtils.toBean(saveReqVo, PrdtSkuPrice.class);

        prdtSkuPriceMapper.insertOrUpdateBatch(prdtSkuPrice);

/*        //插入成功后 清除所有缓存
        skuPriceDTOByAreaTypeCache.removeAll(key);*/

        // 返回
        return saveReqVo;
    }

    /**
     * 修改sku销售价
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrdtSkuPrice(PrdtSkuPriceSaveReqVO updateReqVO) {
        prdtSkuPriceMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrdtSkuPrice.class));
    }

    /**
     * 删除sku销售价
     *
     * @param skuPriceId sku销售价id
     */
    @Override
    public void deletePrdtSkuPrice(Long skuPriceId) {
        // 删除
        prdtSkuPriceMapper.deleteById(skuPriceId);
    }

    /**
     * 批量删除sku销售价
     *
     * @param skuPriceIds 需要删除的sku销售价主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrdtSkuPriceBySkuPriceIds(Long[] skuPriceIds) {

        List<PrdtSkuPrice> skuPriceList = prdtSkuPriceMapper.selectBatchIds(ListUtil.toList(skuPriceIds));
        for(Long skuPriceId : skuPriceIds){
            this.deletePrdtSkuPrice(skuPriceId);
        }

        //清除缓存
        removeCache(HutoolBeanUtils.toBean(skuPriceList, PrdtSkuPriceSaveReqVO.class));
    }

    /**
     * 获得sku销售价
     *
     * @param skuPriceId sku销售价id
     * @return sku销售价
     */
    @Override
    public PrdtSkuPrice getPrdtSkuPrice(Long skuPriceId) {
        return prdtSkuPriceMapper.selectById(skuPriceId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtSkuPricePageReqVO> getPrdtSkuPricePage(PrdtSkuPricePageReqVO pageReqVO) {
        Long dcId = SecurityUtils.getLoginUser() == null ? null : SecurityUtils.getLoginUser().getDcId();
        List<Long> areaIdList = new ArrayList<>();
        if(null != dcId){
            areaIdList = dcApi.getDcAreaList(dcId).getCheckedData();
        }
        // 将areaId列表赋值到pageReqVO
        pageReqVO.setAreaIdList(areaIdList);

        Page<PrdtSkuPricePageReqVO> page = new Page<>(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        Page<PrdtSkuPricePageReqVO> pricePage = prdtSkuPriceMapper.selectPageSkuPrice(pageReqVO, page);
        List<PrdtSkuPricePageReqVO> records = pricePage.getRecords();
        if(ToolUtil.isNotEmpty(records)) {
            //组装商品名称、设置入驻商名称
            records.forEach(record -> {
                //商品名称
                record.setSpuName(PropertyAndValDTO.getPropertiesSpuName(record.getProperties(),record.getSpuName()));

                if(ToolUtil.isNotEmpty(record.getSupplierId())) {
                    //设置入驻商名称
                    SupplierDTO supplierDTO = productCacheService.getSupplierDTO(record.getSupplierId());
                    if (ToolUtil.isNotEmpty(supplierDTO)) {
                        record.setSupplierName(supplierDTO.getSupplierName());
                    }
                }
            });
        }
        return new PageResult<>(pricePage.getRecords(),pricePage.getTotal());
    }

    /**
     * 分页查询sku销售价定价列表
     */
    @Override
    public PageResult<PrdtSkuPricePageReqVO> getPrdtSkuPricePageByPricing(PrdtSkuPricePageReqVO pageReqVO) {
        Page<PrdtSkuPricePageReqVO> page = new Page<>(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        Page<PrdtSkuPricePageReqVO> pricePage = prdtSkuPriceMapper.selectPageSkuPriceByPricing(pageReqVO, page);
        List<PrdtSkuPricePageReqVO> records = pricePage.getRecords();
        if(ToolUtil.isNotEmpty(records)) {
            //组装商品名称、设置入驻商名称
            records.forEach(record -> {
                //商品名称
                record.setSpuName(PropertyAndValDTO.getPropertiesSpuName(record.getProperties(),record.getSpuName()));
                if(ToolUtil.isNotEmpty(record.getSupplierId())) {
                    //设置入驻商名称
                    SupplierDTO supplierDTO = productCacheService.getSupplierDTO(record.getSupplierId());
                    if (ToolUtil.isNotEmpty(supplierDTO)) {
                        record.setSupplierName(supplierDTO.getSupplierName());
                    }
                }
            });
        }
        return new PageResult<>(pricePage.getRecords(),pricePage.getTotal());
    }

    @Override
    public PrdtSkuPrice getSkuPriceByAreaIdAndSkuIdAndType(Long areaId, Long skuId, Integer type) {
        return prdtSkuPriceMapper.getSkuPrice(new PrdtSkuPriceRespVO(areaId,skuId,type));
    }

    @Override
    public void removeCache(List<PrdtSkuPriceSaveReqVO> skuPriceList) {
        Set<String> key = new HashSet<>();
        skuPriceList.forEach(skuPrice -> {
            //组装Key
            key.add(skuPrice.getAreaId() + StringPool.DASH + skuPrice.getSkuId() + StringPool.DASH + skuPrice.getType());
        });

        skuPriceDTOByAreaTypeCache.removeAll(key);
    }

    public List<PrdtSkuPriceSaveReqVO> assembleSaveDate(PrdtSkuPriceSaveInVo createReqVO){
        List<PrdtSkuPriceSaveReqVO> saveReqVo = new ArrayList<>();
        //校验是批量定价还是列表改价
        if(ToolUtil.isEmpty(createReqVO.getSaveFlag())) throw exception(PRDT_SKU_PRICE_SAVE_FLAG);
        if(createReqVO.getSaveFlag() == PRDT_SKU_PRICE_SAVE_FLAG_0){
            if(ToolUtil.isEmpty(createReqVO.getSaveList())) throw exception(PRDT_SKU_PRICE_NOT_EXISTS);
            //需要过滤掉没有销售价的数据
            //没有销售价的数据
            List<PrdtSkuPriceSaveReqVO> filterSaveReqVo =createReqVO.getSaveList().stream()
                    .filter(vo -> ToolUtil.isEmpty(vo.getSalePrice1()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getSalePrice2()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getSalePrice3()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getSalePrice4()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getSalePrice5()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getSalePrice6()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getMidSalePrice1()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getMidSalePrice2()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getMidSalePrice3()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getMidSalePrice4()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getMidSalePrice5()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getMidSalePrice6()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getLargeSalePrice1()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getLargeSalePrice2()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getLargeSalePrice3()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getLargeSalePrice4()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getLargeSalePrice5()))
                    .filter(vo -> ToolUtil.isEmpty(vo.getLargeSalePrice6()))
                    .collect(Collectors.toList());

            //校验
            if(filterSaveReqVo.size() == createReqVO.getSaveList().size()) throw exception(PRDT_SKU_PRICE_NOT_SET);
            //去掉没有销售价的数据集合
            createReqVO.getSaveList().removeAll(filterSaveReqVo);
            saveReqVo = createReqVO.getSaveList();
            if(ToolUtil.isEmpty(saveReqVo)) throw exception(PRDT_SKU_PRICE_NOT_EXISTS);
        }else{
            //校验定价参数
            Stream.of(createReqVO.getPriceOption(),createReqVO.getPriceType(),createReqVO.getPriceRatio(),createReqVO.getUnitType())
                    .filter(Objects::isNull)
                    .findAny()
                    .ifPresent(x ->{
                        throw exception(PRDT_SKU_PRICE_PRICING_NOT_EXISTS);
                    });
            //根据查询条件查询对应的批量定价信息
            Page<PrdtSkuPricePageReqVO> page = new Page<>(PRDT_SKU_PRICE_MAX_SAVE_PAGE,PRDT_SKU_PRICE_MAX_SAVE_NUM);

            //组装查询条件
            PrdtSkuPricePageReqVO pageReqVO = new PrdtSkuPricePageReqVO()
                    .setSysCode(SecurityUtils.getLoginUser().getSysCode())
                    .setAreaId(createReqVO.getAreaId())
                    .setType(createReqVO.getType())
                    .setSupplierId(createReqVO.getSupplierId())
                    .setKeyword(createReqVO.getKeyword())
                    .setCatgoryId(createReqVO.getCatgoryId())
                    .setBrandId(createReqVO.getBrandId())
                    .setUnitType(createReqVO.getUnitType());
            Page<PrdtSkuPricePageReqVO> pricePage = prdtSkuPriceMapper.selectPageSkuPriceByPricing(pageReqVO, page);
            List<PrdtSkuPricePageReqVO> records = pricePage.getRecords();
            if(ToolUtil.isNotEmpty(records)){
                if(PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                    //小单位
                    if (PrdtSkuPriceOptionType.SALE_PRICE_1.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType()) && PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                        records.stream().forEach(x -> x.setSalePrice1(x.getCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_1.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType()) && PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                        records.stream().forEach(x -> x.setSalePrice1(x.getMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_2.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType()) && PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                        records.stream().forEach(x -> x.setSalePrice2(x.getCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_2.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType()) && PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                        records.stream().forEach(x -> x.setSalePrice2(x.getMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_3.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType()) && PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                        records.stream().forEach(x -> x.setSalePrice3(x.getCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_3.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType()) && PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                        records.stream().forEach(x -> x.setSalePrice3(x.getMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_4.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType()) && PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                        records.stream().forEach(x -> x.setSalePrice4(x.getCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_4.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType()) && PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                        records.stream().forEach(x -> x.setSalePrice4(x.getMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_5.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType()) && PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                        records.stream().forEach(x -> x.setSalePrice5(x.getCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_5.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType()) && PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                        records.stream().forEach(x -> x.setSalePrice5(x.getMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_6.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType()) && PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                        records.stream().forEach(x -> x.setSalePrice6(x.getCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_6.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType()) && PRDT_SKU_PRICE_UNIT_TYPE_1.equals(createReqVO.getUnitType())) {
                        records.stream().forEach(x -> x.setSalePrice6(x.getMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    }
                }

                if(PRDT_SKU_PRICE_UNIT_TYPE_2.equals(createReqVO.getUnitType())) {
                    //中单位
                    if (PrdtSkuPriceOptionType.SALE_PRICE_1.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setMidSalePrice1(x.getMidCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_1.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setMidSalePrice1(x.getMidMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_2.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setMidSalePrice2(x.getMidCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_2.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setMidSalePrice2(x.getMidMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_3.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setMidSalePrice3(x.getMidCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_3.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setMidSalePrice3(x.getMidMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_4.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setMidSalePrice4(x.getMidCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_4.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setMidSalePrice4(x.getMidMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_5.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setMidSalePrice5(x.getMidCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_5.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setMidSalePrice5(x.getMidMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_6.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setMidSalePrice6(x.getMidCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_6.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setMidSalePrice6(x.getMidMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    }
                }

                if(PRDT_SKU_PRICE_UNIT_TYPE_3.equals(createReqVO.getUnitType())) {
                    //大单位
                    if (PrdtSkuPriceOptionType.SALE_PRICE_1.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setLargeSalePrice1(x.getLargeCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_1.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setLargeSalePrice1(x.getLargeMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_2.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setLargeSalePrice2(x.getLargeCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_2.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setLargeSalePrice2(x.getLargeMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_3.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setLargeSalePrice3(x.getLargeCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_3.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setLargeSalePrice3(x.getLargeMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_4.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setLargeSalePrice4(x.getLargeCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_4.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setLargeSalePrice4(x.getLargeMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_5.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setLargeSalePrice5(x.getLargeCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_5.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setLargeSalePrice5(x.getLargeMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_6.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_1.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setLargeSalePrice6(x.getLargeCostPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    } else if (PrdtSkuPriceOptionType.SALE_PRICE_6.getName().equals(createReqVO.getPriceOption()) && PRDT_SKU_PRICE_PRICE_TYPE_2.equals(createReqVO.getPriceType())) {
                        records.stream().forEach(x -> x.setLargeSalePrice6(x.getLargeMarkPrice().multiply(createReqVO.getPriceRatio()).divide(new BigDecimal("100"))));
                    }
                }

                saveReqVo = HutoolBeanUtils.toBean(records,PrdtSkuPriceSaveReqVO.class);

            }
        }
        return saveReqVo;
    };


    @Override
    public void batchDeleteBySkuPriceIds(List<Long> skuPriceIds) {
        for (Long skuId : skuPriceIds) {
            prdtSkuPriceMapper.updateSkuPrice(skuId,1);
        }
    }

    @Override
    public String impordData(List<PrdtSkuPriceInfoExportVO> skuPriceList) {
        String resultMessage = "";
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        List<PrdtSkuPrice> result = new ArrayList<>();
        ArrayList<PrdtSkuPriceInfoExportVO> addList = new ArrayList<>();

        try {
        if (skuPriceList.isEmpty()) {
            // 如果导入数据为空，则不进行数据导入
            throw exception(500, "导入数据为空");
        }
        Map<String, Long> prdtSupplierMap = new HashMap<>(); // 存储已插入的类别，避免重复插入
        // 数据校验
        checkExcelData(skuPriceList, addList, failureMsg, failureNum);


        for (PrdtSkuPriceInfoExportVO prdtSkuPrice : addList) {
            try {
                PrdtSkuPriceSaveInVo createReqVO = new PrdtSkuPriceSaveInVo();
                createReqVO.setType(1);
                createReqVO.setAreaId(prdtSkuPrice.getAreaId());
                createReqVO.setSaveFlag(0);

//                if (ToolUtil.isEmpty(prdtSkuPrice.getSkuPriceId()) && ToolUtil.isNotEmpty(prdtSkuPriceMapper.getSkuPriceBySkuId(prdtSkuPrice.getSkuId(),prdtSkuPrice.getAreaId()))){
//                    throw new Exception(StringUtils.format("sku：{}已存在价格方案无法新增", prdtSkuPrice.getSkuId()));
//                }

//                log.info("价格方案导入,{},{}", prdtSkuPrice.getSkuPriceId(),JsonUtils.toJsonString(prdtSkuPrice));
                PrdtSkuPrice skuPriceBySkuId = null;
                if(null != prdtSkuPrice.getSkuPriceId()){
                    skuPriceBySkuId = prdtSkuPriceMapper.getSkuPriceBySkuPriceId(prdtSkuPrice.getSkuPriceId());

                }else {
                    skuPriceBySkuId = prdtSkuPriceMapper.getSkuPriceBySkuId(prdtSkuPrice.getSkuId(),prdtSkuPrice.getAreaId());
                }

                if (ToolUtil.isNotEmpty(skuPriceBySkuId)) {
                    prdtSkuPrice.setSkuPriceId(skuPriceBySkuId.getSkuPriceId());
                    prdtSkuPrice.setSkuId(skuPriceBySkuId.getSkuId());
                }

                List<PrdtSkuPriceSaveReqVO> vos = Collections.singletonList(BeanUtils.toBean(prdtSkuPrice, PrdtSkuPriceSaveReqVO.class));
                vos.stream().forEach(item->{item.setType(1);});
                createReqVO.setSaveList(vos);
                List<PrdtSkuPriceSaveReqVO> prdtSkuPriceSaveReqVOS = insertPrdtSkuPrice(createReqVO);
                prdtSkuPriceService.removeCache(prdtSkuPriceSaveReqVOS);
                successNum++;
            } catch (Exception e) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>数据导入修改失败，错误信息：{}。", e.getMessage()));
            }
        }
        } catch (Exception e) {
            failureNum++;
            failureMsg.append(StringUtils.format("<br/>数据导入修改失败，错误信息：{}。", e.getMessage()));
        }
        if (failureNum > 0) {
            resultMessage = String.format("共导入%d条，成功%d条，失败%d条。失败原因如下：", skuPriceList.size(), successNum, failureNum)
                    + failureMsg.toString();
        } else {
            resultMessage = String.format("恭喜您，数据已全部导入成功！共 %d 条", successNum);
        }
        return resultMessage;
    }

    private void checkExcelData(List<PrdtSkuPriceInfoExportVO> skuPriceList, ArrayList<PrdtSkuPriceInfoExportVO> addList, StringBuilder failureMsg, Integer failureNum) throws Exception {
        for (int line = 0; line < skuPriceList.size(); line++) {
            if (failureMsg.length() > 2000) {
                break;
            }
            int cellNumber = line + 3;
            PrdtSkuPriceInfoExportVO itemData = skuPriceList.get(line);
            // 导入数据校验
            if (ToolUtil.isEmpty(itemData.getBarcode())){
                throw new Exception(StringUtils.format("第{}行数据 条码 不能为空。请先修改数据再导入", cellNumber));
            }
            if (ToolUtil.isEmpty(itemData.getAreaId())){
                throw new Exception(StringUtils.format("第{}行数据 区域编号 不能为空。请先修改数据再导入", cellNumber));
            }
            if (ToolUtil.isEmpty(productCacheService.getAreaDto(itemData.getAreaId()))){
                throw new Exception(StringUtils.format("第{}行数据 区域编号 不存在。请先修改数据再导入", cellNumber));
            }
            if (ToolUtil.isEmpty(itemData.getSkuId())){
                throw new Exception(StringUtils.format("第{}行数据 skuId 不能为空。请先修改数据再导入", cellNumber));
            }
            if (ToolUtil.isEmpty(productCacheService.getSkuDTO(itemData.getSkuId()))){
                throw new Exception(StringUtils.format("第{}行数据 skuId 不存在。请先修改数据再导入", cellNumber));
            }
            if (ToolUtil.isEmpty(itemData.getSalePrice1())){
                throw new Exception(StringUtils.format("第{}行数据 配送价1 不能为空。请先修改数据再导入", cellNumber));
            }



            addList.add(itemData);
        }


    }
}
