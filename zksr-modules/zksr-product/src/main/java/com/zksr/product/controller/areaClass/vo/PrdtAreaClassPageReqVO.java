package com.zksr.product.controller.areaClass.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 城市展示分类对象 prdt_area_class
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@ApiModel("城市展示分类 - prdt_area_class分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtAreaClassPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 城市展示分类id */
    @ApiModelProperty(value = "入驻商id")
    private Long areaClassId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /** 父id */
    @Excel(name = "父id")
    @ApiModelProperty(value = "父id")
    private Long pid;

    /** 城市展示分类名 */
    @Excel(name = "城市展示分类名")
    @ApiModelProperty(value = "城市展示分类名")
    private String areaClassName;

    /** 分类图标 */
    @Excel(name = "分类图标")
    @ApiModelProperty(value = "分类图标")
    private String icon;

    /** 排序 */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Long sort;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private String status;

    /** 是否是电子围栏分类 */
    @Excel(name = "是否是电子围栏分类")
    @ApiModelProperty(value = "是否是电子围栏分类")
    private Long dzwlFlag;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;


}
