package com.zksr.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRatePageReqVO;
import com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRateRespVO;
import com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRateSaveReqVO;
import com.zksr.product.domain.PrdtCatgoryRate;
import com.zksr.product.mapper.PrdtCatgoryMapper;
import com.zksr.product.mapper.PrdtCatgoryRateMapper;
import com.zksr.product.service.IPrdtCatgoryRateService;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_CATGORY_RATE_ERROR;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_CATGORY_RATE_NOT_EXISTS;

/**
 * 城市级管理分类扣点设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Service
public class PrdtCatgoryRateServiceImpl implements IPrdtCatgoryRateService {
    @Autowired
    private PrdtCatgoryRateMapper prdtCatgoryRateMapper;

    @Autowired
    private PrdtCatgoryMapper prdtCatgoryMapper;
    @Resource
    private PartnerApi partnerApi;
    @Resource
    private PartnerConfigApi partnerConfigApi;


    /**
     * 新增城市级管理分类扣点设置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtCatgoryRate(PrdtCatgoryRateSaveReqVO createReqVO) {
        // 插入
        PrdtCatgoryRate prdtCatgoryRate = HutoolBeanUtils.toBean(createReqVO, PrdtCatgoryRate.class);
        prdtCatgoryRateMapper.insert(prdtCatgoryRate);
        // 返回
        return prdtCatgoryRate.getCatgoryRateId();
    }

    /**
     * 修改城市级管理分类扣点设置
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public Long updatePrdtCatgoryRate(PrdtCatgoryRateSaveReqVO updateReqVO) {
        prdtCatgoryRateMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrdtCatgoryRate.class));
        return updateReqVO.getCatgoryRateId();
    }

    /**
     * 删除城市级管理分类扣点设置
     *
     * @param catgoryRateId 城市级管理分类扣点设置id
     */
    @Override
    public void deletePrdtCatgoryRate(Long catgoryRateId) {
        // 删除
        prdtCatgoryRateMapper.deleteById(catgoryRateId);
    }

    /**
     * 批量删除城市级管理分类扣点设置
     *
     * @param catgoryRateIds 需要删除的城市级管理分类扣点设置主键
     * @return 结果
     */
    @Override
    public void deletePrdtCatgoryRateByCatgoryRateIds(Long[] catgoryRateIds) {
        for(Long catgoryRateId : catgoryRateIds){
            this.deletePrdtCatgoryRate(catgoryRateId);
        }
    }

    /**
     * 获得城市级管理分类扣点设置
     *
     * @param catgoryRateId 城市级管理分类扣点设置id
     * @return 城市级管理分类扣点设置
     */
    @Override
    public PrdtCatgoryRate getPrdtCatgoryRate(Long catgoryRateId) {
        return prdtCatgoryRateMapper.selectById(catgoryRateId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtCatgoryRate> getPrdtCatgoryRatePage(PrdtCatgoryRatePageReqVO pageReqVO) {
        return prdtCatgoryRateMapper.selectPage(pageReqVO);
    }


    /**
     * @Description: 商品类别设置二级扣点
     * @Author: liuxingyu
     * @Date: 2024/3/15 9:35
     */
    @Override
    public Long installRate(PrdtCatgoryRateSaveReqVO saveReqVO) {
        checkRate(saveReqVO);
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        PrdtCatgoryRate prdtCatgoryRate = BeanUtil.copyProperties(saveReqVO, PrdtCatgoryRate.class);
        prdtCatgoryRate.setSysCode(sysCode);
        //查询数据库 无则新增 有则修改
        PrdtCatgoryRate isSave = prdtCatgoryRateMapper.getIsSave(saveReqVO.getAreaId(), saveReqVO.getCatgoryId());
        if (ObjectUtil.isNull(isSave)){
            prdtCatgoryRateMapper.insert(prdtCatgoryRate);
            return prdtCatgoryRate.getCatgoryRateId();
        }
        prdtCatgoryRate.setCatgoryRateId(isSave.getCatgoryRateId());
        prdtCatgoryRateMapper.updateById(prdtCatgoryRate);
        return prdtCatgoryRate.getCatgoryRateId();
    }

    /**
     * @Description: 运营商获取商品管理分类详情
     * @Author: liuxingyu
     * @Date: 2024/3/15 15:37
     */
    @Override
    public PrdtCatgoryRateRespVO getRateInfo(Long areaId, Long catgoryId) {
        PrdtCatgoryRateRespVO respVO =prdtCatgoryRateMapper.getRateInfo(areaId, catgoryId);
        PartnerDto partnerDto =partnerApi.getBySysCode(SecurityUtils.getLoginUser().getSysCode()).getCheckedData();
        if(ToolUtil.isNotEmpty(partnerDto)){
            respVO.setSoftwareRate(partnerDto.getSoftwareRate());
        }
        PayConfigDTO payConfigDTO =partnerConfigApi.getPayConfig(SecurityUtils.getLoginUser().getSysCode()).getCheckedData();
        if(ToolUtil.isNotEmpty(payConfigDTO)){
            respVO.setPartnerRate(new BigDecimal(payConfigDTO.getDefaultSaleTotalCategoryRate()==null?"0":payConfigDTO.getDefaultSaleTotalCategoryRate()));
        }
        return respVO;
    }

    /**
    * @Description: 获取设置扣点列表
    * @Author: liuxingyu
    * @Date: 2024/3/19 9:46
    */
    @Override
    public List<PrdtCatgoryRateRespVO> getCatgoryRateList(PrdtCatgoryRateRespVO respVO) {
        if (ObjectUtil.isNull(respVO.getAreaId())){
            respVO.setAreaId(0L);
        }
        return prdtCatgoryRateMapper.getCatgoryRateList(respVO);
    }

    /**
     * @Description: 校验分润比例
     * @Param: PrdtCatgorySaveReqVO saveReqVO
     * @Author: liuxingyu
     * @Date: 2024/3/20 16:35
     */
    private void checkRate(PrdtCatgoryRateSaveReqVO saveReqVO){
        BigDecimal dcRate = saveReqVO.getDcRate();
        BigDecimal colonel1Rate = saveReqVO.getColonel1Rate();
        BigDecimal colonel2Rate = saveReqVO.getColonel2Rate();
        BigDecimal count = dcRate.add(colonel1Rate).add(colonel2Rate);
        if (ObjectUtil.notEqual(NumberPool.INT_ZERO,count.compareTo(new BigDecimal(StringPool.ONE)))){
            throw exception(PRDT_CATGORY_RATE_ERROR);
        }
    }

    private void validatePrdtCatgoryRateExists(Long catgoryRateId) {
        if (prdtCatgoryRateMapper.selectById(catgoryRateId) == null) {
            throw exception(PRDT_CATGORY_RATE_NOT_EXISTS);
        }
    }

}
