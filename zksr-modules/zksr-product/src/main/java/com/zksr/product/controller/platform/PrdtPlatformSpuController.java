package com.zksr.product.controller.platform;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.api.platform.excel.ProductPlarformImportExcel;
import com.zksr.product.api.spu.vo.PrdtSpuGroupInVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSpuPageReqVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSpuRespVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSpuSaveReqVO;
import com.zksr.product.controller.platform.vo.PrdtSpuGroupRespVO;
import com.zksr.product.controller.spu.vo.PrdtSpuShareReqVO;
import com.zksr.product.convert.platform.PrdtPlatformSpuConvert;
import com.zksr.product.domain.PrdtPlatformSpu;
import com.zksr.product.service.IPrdtPlatformSpuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台商品库-商品SPUController
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Api(tags = "管理后台 - 平台商品库-商品SPU接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/platform/spu")
public class PrdtPlatformSpuController {

    @Autowired
    private IPrdtPlatformSpuService prdtPlatformSpuService;

    /**
     * 获取平台商品库-商品SPU详细信息
     */
    @ApiOperation(value = "获得平台商品库-商品SPU详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{platformSpuId}")
    public CommonResult<PrdtSpuGroupRespVO> getInfo(@PathVariable("platformSpuId") Long platformSpuId) {
        return success(prdtPlatformSpuService.getPrdtPlatformSpuInfo(platformSpuId));
    }

    /**
     * 分页查询平台商品库-商品SPU
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台商品库-商品SPU分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtPlatformSpuRespVO>> getPage(@Valid PrdtPlatformSpuPageReqVO pageReqVO) {
        PageResult<PrdtPlatformSpu> pageResult = prdtPlatformSpuService.getPrdtPlatformSpuPageExt(pageReqVO);
        return success(PrdtPlatformSpuConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 商品开启共享
     */
    @ApiOperation(value = "商品开启共享", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "平台商品库开启共享", businessType = BusinessType.UPDATE)
    @PostMapping("/shareEnable")
    public CommonResult<Boolean> shareEnable(@Valid @RequestBody PrdtSpuShareReqVO updateReqVO) {
        prdtPlatformSpuService.shareEnable(updateReqVO);
        return success(true);
    }

    /**
     * 商品关闭共享
     */
    @ApiOperation(value = "商品停用共享", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "平台商品库停用共享", businessType = BusinessType.UPDATE)
    @PostMapping("/shareDisable")
    public CommonResult<Boolean> shareDisable(@Valid @RequestBody PrdtSpuShareReqVO updateReqVO) {
        prdtPlatformSpuService.shareDisable(updateReqVO);
        return success(true);
    }

    @ApiOperation(value = "商品共享库导入共享商品信息", httpMethod = HttpMethod.POST)
    @Log(title = "商品共享库导入共享商品信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/plarformImportData")
    public CommonResult<String> plarformImportData(MultipartFile file) throws Exception
    {
        ExcelUtil<ProductPlarformImportExcel> util = new ExcelUtil<>(ProductPlarformImportExcel.class);
        List<ProductPlarformImportExcel> productList = util.importExcel(file.getInputStream());
        String message = prdtPlatformSpuService.importPlatformProduct(productList);
        return success(message);
    }

    @ApiOperation(value = "商品共享库导入共享商品图片地址信息", httpMethod = HttpMethod.POST)
    @Log(title = "商品共享库导入共享商品图片信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/plarformImportPicturesUrlInfo")
    public CommonResult<String> plarformImportPicturesUrlInfo(MultipartFile file) throws Exception
    {
        ExcelUtil<ProductPlarformImportExcel> util = new ExcelUtil<>(ProductPlarformImportExcel.class);
        List<ProductPlarformImportExcel> productList = util.importExcel(file.getInputStream());
        String message = prdtPlatformSpuService.importPlatformPicturesUrlInfo(productList);
        return success(message);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:platform-spu:add";
        /** 编辑 */
        public static final String EDIT = "product:platform-spu:edit";
        /** 删除 */
        public static final String DELETE = "product:platform-spu:remove";
        /** 列表 */
        public static final String LIST = "product:platform-spu:list";
        /** 查询 */
        public static final String GET = "product:platform-spu:query";
        /** 停用 */
        public static final String DISABLE = "product:platform-spu:disable";
        /** 启用 */
        public static final String ENABLE = "product:platform-spu:enable";
        /** 启用 */
        public static final String IMPORT = "product:platform-spu:import";
    }
}
