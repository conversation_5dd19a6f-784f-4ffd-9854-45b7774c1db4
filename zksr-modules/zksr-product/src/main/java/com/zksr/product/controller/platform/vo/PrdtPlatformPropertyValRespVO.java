package com.zksr.product.controller.platform.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 规格值对象 prdt_platform_property_val
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Data
@ApiModel("规格值 - prdt_platform_property_val Response VO")
public class PrdtPlatformPropertyValRespVO {
    private static final long serialVersionUID = 1L;

    /** 规格值id */
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long platformPropertyValId;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    @ApiModelProperty(value = "商品SPU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 规格名称id */
    @Excel(name = "规格名称id")
    @ApiModelProperty(value = "规格名称id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long propertyId;

    /** 规格值名称 */
    @Excel(name = "规格值名称")
    @ApiModelProperty(value = "规格值名称")
    private String name;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否")
    private Integer isDelete;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)")
    private Integer status;

}
