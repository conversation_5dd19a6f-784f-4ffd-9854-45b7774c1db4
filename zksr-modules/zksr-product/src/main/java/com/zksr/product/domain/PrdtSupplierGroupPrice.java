package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 平台商城市分组价格对象 prdt_supplier_group_price
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@TableName(value = "prdt_supplier_group_price")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSupplierGroupPrice {
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    private Long groupId;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @Excel(name = "价格码-数据字典", readConverterExp = "1=，2，3，4，5，6")
    private Long salePriceCode;

}
