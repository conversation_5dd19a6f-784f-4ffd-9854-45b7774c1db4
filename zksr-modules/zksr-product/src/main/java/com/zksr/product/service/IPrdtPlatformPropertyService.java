package com.zksr.product.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyPageReqVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertySaveReqVO;
import com.zksr.product.domain.PrdtPlatformProperty;

import javax.validation.Valid;

/**
 * 规格名称Service接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
public interface IPrdtPlatformPropertyService {

    /**
     * 新增规格名称
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtPlatformProperty(@Valid PrdtPlatformPropertySaveReqVO createReqVO);

    /**
     * 修改规格名称
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtPlatformProperty(@Valid PrdtPlatformPropertySaveReqVO updateReqVO);

    /**
     * 删除规格名称
     *
     * @param platformPropertyId 规格名称id
     */
    public void deletePrdtPlatformProperty(Long platformPropertyId);

    /**
     * 批量删除规格名称
     *
     * @param platformPropertyIds 需要删除的规格名称主键集合
     * @return 结果
     */
    public void deletePrdtPlatformPropertyByPlatformPropertyIds(Long[] platformPropertyIds);

    /**
     * 获得规格名称
     *
     * @param platformPropertyId 规格名称id
     * @return 规格名称
     */
    public PrdtPlatformProperty getPrdtPlatformProperty(Long platformPropertyId);

    /**
     * 获得规格名称分页
     *
     * @param pageReqVO 分页查询
     * @return 规格名称分页
     */
    PageResult<PrdtPlatformProperty> getPrdtPlatformPropertyPage(PrdtPlatformPropertyPageReqVO pageReqVO);

}
