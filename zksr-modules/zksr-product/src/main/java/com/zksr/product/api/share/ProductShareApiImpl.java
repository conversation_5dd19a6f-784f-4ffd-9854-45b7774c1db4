package com.zksr.product.api.share;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.share.dto.BatchProductShareDTO;
import com.zksr.product.api.share.dto.PrdtProductShareRespDTO;
import com.zksr.product.controller.share.vo.BatchProductShareVO;
import com.zksr.product.service.IPrdtProductShareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/6 18:07
 * @商品分享内部api实现
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class ProductShareApiImpl implements ProductShareApi{

    @Autowired
    private IPrdtProductShareService prdtProductShareService;


    @Override
    public CommonResult<String> batchInsertProductShare(BatchProductShareDTO items) {
        return CommonResult.success(prdtProductShareService.insertPrdtProductShare(HutoolBeanUtils.toBean(items.getItemList(), BatchProductShareVO.class)));
    }

    @Override
    public CommonResult<List<PrdtProductShareRespDTO>> getShareProductInfo(String shareKey) {
        return CommonResult.success(HutoolBeanUtils.toBean(prdtProductShareService.getShareProductInfo(shareKey), PrdtProductShareRespDTO.class));
    }
}
