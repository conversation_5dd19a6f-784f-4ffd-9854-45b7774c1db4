package com.zksr.product.api.brand;

import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.brand.form.BrandImportForm;
import com.zksr.product.api.brand.vo.BindBrandMerchantReqVO;
import com.zksr.product.domain.ProductBrand;
import com.zksr.product.controller.brand.vo.ProductBrandSaveReqVO;
import com.zksr.product.service.IProductBrandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@ApiIgnore
public class BrandApiImpl implements BrandApi {

    @Autowired
    private IProductBrandService brandService;

    /**
     * @Description: 根据平台商品牌id获取平台商品牌
     * @Param: Long brandId
     * @return: CommonResult<BrandDto>
     * @Author: liuxingyu
     * @Date: 2024/3/25 14:55
     */
    @Override
    @InnerAuth
    public CommonResult<BrandDTO> getBrandByBrandId(Long brandId) {
        return success(HutoolBeanUtils.toBean(brandService.getBrandByBrandId(brandId), BrandDTO.class));
    }

    /**
     * 新增平台品牌
     *
     * @param brandDTO
     * @return
     */
    @Override
    public CommonResult<Long> add(BrandDTO brandDTO) {
        ProductBrandSaveReqVO saveReqVO = HutoolBeanUtils.toBean(brandDTO, ProductBrandSaveReqVO.class);
        return success(brandService.insertProductBrand(saveReqVO));
    }


    /**
     * 修改平台品牌
     *
     * @param brandDTO
     */
    @Override
    public CommonResult<Boolean> edit(BrandDTO brandDTO) {
        ProductBrandSaveReqVO updateReqVO = HutoolBeanUtils.toBean(brandDTO, ProductBrandSaveReqVO.class);
        brandService.updateProductBrand(updateReqVO);
        return success(Boolean.TRUE);
    }

    /**
     * 删除平台品牌
     *
     * @param brandIds
     */
    @Override
    public CommonResult<Boolean> remove(Long[] brandIds) {
        brandService.deleteProductBrandByBrandIds(brandIds);
        return success(Boolean.TRUE);
    }

    /**
     *  获取所有品牌
     */
    @Override
    @InnerAuth
    public CommonResult<List<BrandDTO>> getBrandList() {
        return success(HutoolBeanUtils.toBean(brandService.getBrand(),BrandDTO.class));
    }

    @Override
    public CommonResult<Boolean> bindBrandMerchant(BindBrandMerchantReqVO bindBrandMerchantReqVO) {
        brandService.bindBrandMerchant(bindBrandMerchantReqVO);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Map<Long, BrandDTO>> listByBrandIds(List<Long> brandIds) {
        return success(brandService.listByBrandIds(brandIds));
    }

    public CommonResult<String> importBrandData(BrandImportForm brandImportForm){
        String jsonString = JsonUtils.toJsonString(brandService.impordBrandDataEvent(brandImportForm.getList(), brandImportForm.getSysCode(), brandImportForm.getFileImportId(),brandImportForm.getSeq()));
        return success(jsonString);
    }

}
