package com.zksr.product.controller.blockScheme.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;
import java.util.List;


/**
 * 经营屏蔽方案对象 prdt_block_scheme
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Data
@ApiModel("经营屏蔽方案 - prdt_block_scheme Response VO")
public class PrdtBlockSchemeRespVO {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty(value = "备注")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long blockSchemeId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 方案编码 */
    @Excel(name = "方案编码")
    @ApiModelProperty(value = "方案编码")
    private String schemeNo;

    /** 方案名称 */
    @Excel(name = "方案名称")
    @ApiModelProperty(value = "方案名称")
    private String schemeName;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 状态：1正常 0禁用 */
    @Excel(name = "状态：1正常 0禁用")
    @ApiModelProperty(value = "状态：1正常 0禁用")
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 删除标志 0 未删除 1 已删除 */
    @ApiModelProperty(value = "备注")
    private Integer delFlag;

    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    @Excel(name = "创建人")
    private String createBy;

    /** 创建时间 */
    @Excel(name = "创建时间")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    @ApiModelProperty(value = "更新者")
    @Excel(name = "更新者")
    private String updateBy;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "客户列表")
    private List<PrdtBlockBranchRespVO> branchList;

    @ApiModelProperty(value = "商品列表")
    private List<PrdtBlockSkuRespVO> skuList;
}
