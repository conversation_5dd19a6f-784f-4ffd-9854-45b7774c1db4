package com.zksr.product.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.domain.vo.openapi.OrderDetailOpenDTO;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.catgory.dto.CatgoryDeleteDTO;
import com.zksr.product.api.catgory.dto.CatgoryIdDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.api.catgory.excel.CategoryImportExcel;
import com.zksr.product.controller.catgory.dto.PrdtCatgoryCopyRespDTO;
import com.zksr.product.controller.catgory.vo.PrdtCatgoryPageReqVO;
import com.zksr.product.controller.catgory.vo.PrdtCatgoryRespVO;
import com.zksr.product.controller.catgory.vo.PrdtCatgorySaveReqVO;
import com.zksr.product.controller.supplierClass.vo.SupplierClassRateStatusRespVO;
import com.zksr.product.domain.*;
import com.zksr.product.domain.dto.BoundProductInfoDTO;
import com.zksr.product.mapper.*;
import com.zksr.product.service.IPrdtCatgoryService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.FileImportApi;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.api.supplierOrder.SupplierOrderApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.STATUS_FAIL;
import static com.zksr.common.core.constant.StatusConstants.STATUS_SUCCESS;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 平台商管理分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
@Slf4j
public class PrdtCatgoryServiceImpl implements IPrdtCatgoryService {
    @Autowired
    private PrdtCatgoryMapper prdtCatgoryMapper;

    @Autowired
    private Cache<Long, CatgoryDTO> catgoryDtoCache;

    @Autowired
    private Cache<String, CatgoryRateDTO> catgoryRateCache;

    @Autowired
    private PrdtSupplierClassMapper prdtSupplierClassMapper;

    @Autowired
    private AreaApi areaApi;
    @Autowired
    private SupplierOrderApi supplierOrderApi;

    @Autowired
    private PrdtCatgoryRateMapper prdtCatgoryRateMapper;

    @Autowired
    @Qualifier("catgorySysCodeListDtoCache")
    private Cache<Long, List<CatgoryDTO>> catgorySysCodeListDtoCache;

    @Autowired
    @Qualifier("catgorySupplierListDtoCache")
    private Cache<Long, List<CatgoryDTO>> catgorySupplierListDtoCache;

    @Autowired
    private PrdtAreaClassMapper prdtAreaClassMapper;

    @Autowired
    private PrdtSaleClassMapper prdtSaleClassMapper;

    @Autowired
    private PrdtSpuMapper spuMapper;

    @Resource
    private SupplierApi supplierApi;

    @Autowired
    private IProductCacheService productCacheService;

    @Resource
    private FileImportApi fileImportApi;

    /**
     * 新增平台商管理分类
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtCatgory(PrdtCatgorySaveReqVO createReqVO) {
        //校验名称
        checkCatgoryNameUnique(createReqVO.getCatgoryName(), createReqVO.getPid(), null);
        checkCatgoryLevel(createReqVO);
        //校验分润比例
        if (ObjectUtil.equal(createReqVO.getLevel(), NumberPool.INT_ONE)) {
            checkRate(createReqVO);
        }
        // 插入
        PrdtCatgory prdtCatgory = HutoolBeanUtils.toBean(createReqVO, PrdtCatgory.class);
        prdtCatgoryMapper.insert(prdtCatgory);
        LoginUser user = SecurityUtils.getLoginUser();
        Long sysCode;
        if (user != null) {
            sysCode = user.getSysCode();
        } else {
            sysCode = SecurityContextHolder.getSysCode();
        }
        catgorySysCodeListDtoCache.remove(sysCode);
        return prdtCatgory.getCatgoryId();
    }

    /**
     * 修改平台商管理分类
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public Long updatePrdtCatgory(PrdtCatgorySaveReqVO updateReqVO) {
        PrdtCatgory prdtCatgory = prdtCatgoryMapper.selectById(updateReqVO.getCatgoryId());
        if (ObjectUtil.isNull(prdtCatgory)) {
            throw exception(PRDT_CATGORY_NOT_EXISTS);
        }
        //校验名称
        checkCatgoryNameUnique(updateReqVO.getCatgoryName(), updateReqVO.getPid(), updateReqVO.getCatgoryId());
        //校验级别
        checkCatgoryLevel(updateReqVO);
        //校验分润比例
        if (ObjectUtil.equal(updateReqVO.getLevel(), NumberPool.INT_ONE)) {
            checkRate(updateReqVO);
        }
        //同步子集
        //synSublevel(updateReqVO);
        //如果绑定了入驻商则不允许修改级别
        if (ObjectUtil.notEqual(prdtCatgory.getLevel(), updateReqVO.getLevel())) {
            List<PrdtSupplierClass> supplierClasses = prdtSupplierClassMapper.selectByCatgoryId(updateReqVO.getCatgoryId());
            if (ObjectUtil.isNotEmpty(supplierClasses)) {
                throw exception(PRDT_CATGORY_NOT_UPDATE);
            }
        }
        prdtCatgoryMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrdtCatgory.class));
        //删除缓存
        LoginUser user = SecurityUtils.getLoginUser();
        Long sysCode;
        if (user != null) {
            sysCode = user.getSysCode();
        } else {
            sysCode = SecurityContextHolder.getSysCode();
        }
        //异步更新删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                catgorySysCodeListDtoCache.remove(sysCode);
                catgoryDtoCache.remove(updateReqVO.getCatgoryId());
                //获取当前分类绑定的入驻商
                List<PrdtSupplierClass> supplierClasses = prdtSupplierClassMapper.selectByCatgoryId(updateReqVO.getCatgoryId());
                //删除缓存
                supplierClasses.forEach(x -> {
                    catgorySupplierListDtoCache.remove(x.getSupplierId());
                });
            } catch (Exception e) {
                log.error(" PrdtCatgoryServiceImpl.updatePrdtCatgory异常，", e);
            }
        });
        return updateReqVO.getCatgoryId();
    }

    /**
     * 删除平台商管理分类
     *
     * @param classId 平台商管理分类id
     */
    @Override
    public void deletePrdtCatgory(Long classId) {
        // 删除
        prdtCatgoryMapper.deleteById(classId);
    }

    /**
     * 批量删除平台商管理分类
     *
     * @param classIds 需要删除的平台商管理分类主键
     * @return 结果
     */
    @Override
    public void deletePrdtCatgoryByClassIds(Long[] classIds) {
        for (Long classId : classIds) {
            // 获取类别信息
            PrdtCatgory category = prdtCatgoryMapper.selectById(classId);
            if (category == null) {
                throw new ServiceException("类别编号不存在，请检查! " + classId);
            }

            // 根据类别层级进行删除
            switch (category.getLevel()) {
                case 1:
                    deletePrimaryCategory(classId);
                    break;
                case 2:
                    deleteSecondaryCategory(classId);
                    break;
                case 3:
                    deleteTertiaryCategory(classId);
                    break;
                default:
                    throw new ServiceException("当前类别的级别不存在: " + category.getLevel());
            }
        }
    }

    private void deletePrimaryCategory(Long categoryId) {
        // 获取一级类别下的所有二级类别
        List<PrdtCatgory> secondaryCategories = prdtCatgoryMapper.getListByPid(categoryId);
        for (PrdtCatgory secondaryCategory : secondaryCategories) {
            // 删除二级类别及其下的三级类别
            deleteSecondaryCategory(secondaryCategory.getCatgoryId());
        }
        // 删除一级类别
        prdtCatgoryMapper.deleteById(categoryId);
    }

    private void deleteSecondaryCategory(Long categoryId) {
        // 获取二级类别下的所有三级类别
        List<PrdtCatgory> tertiaryCategories = prdtCatgoryMapper.getListByPid(categoryId);
        for (PrdtCatgory tertiaryCategory : tertiaryCategories) {
            // 检查三级类别是否绑定商品，如果没有绑定则删除
            if (isCatgoryHangGoods(tertiaryCategory.getCatgoryId(), null).getData() == null) {
                prdtCatgoryMapper.deleteById(tertiaryCategory.getCatgoryId());
            } else {
                throw new ServiceException("不能删除，三级类别 " + tertiaryCategory.getCatgoryName() + " 已绑定商品。");
            }
        }
        // 检查二级类别是否绑定商品，如果没有绑定则删除
        if (isCatgoryHangGoods(categoryId, null).getData() == null) {
            prdtCatgoryMapper.deleteById(categoryId);
        } else {
            throw new ServiceException("不能删除，二级类别 " + categoryId + " 已绑定商品。");
        }
    }

    private void deleteTertiaryCategory(Long categoryId) {
        // 检查三级类别是否绑定商品，如果没有绑定则删除
        if (isCatgoryHangGoods(categoryId, null).getData() == null) {
            prdtCatgoryMapper.deleteById(categoryId);
        } else {
            throw new ServiceException("不能删除，三级类别 " + categoryId + " 已绑定商品。");
        }
    }

    /**
     * 获得平台商管理分类
     *
     * @param classId 平台商管理分类id
     * @return 平台商管理分类
     */
    @Override
    public PrdtCatgory getPrdtCatgory(Long classId) {
        return prdtCatgoryMapper.selectById(classId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrdtCatgory> getPrdtCatgoryPage(PrdtCatgoryPageReqVO pageReqVO) {
//        Long sysSupplierId = SecurityUtils.getSupplierId();
//        if (Objects.nonNull(sysSupplierId)) {
//            pageReqVO.setSupplierId(sysSupplierId);
//        }
        return PageResult.result(PageUtils.startPage(pageReqVO), prdtCatgoryMapper.selectPageExt(pageReqVO));
    }

    /**
     * @Description: 获取平台商管理分类子级列表
     * @Author: liuxingyu
     * @Date: 2024/2/19 15:56
     */
    @Override
    public PageResult<PrdtCatgoryRespVO> getSublevelCatgoryById(PrdtCatgoryPageReqVO pageReqVO) {
        //通过ID 查询子级数据
        /*PrdtCatgoryPageReqVO reqVO = new PrdtCatgoryPageReqVO();
        reqVO.setPid(pageReqVO.getCatgoryId());
        reqVO.setPageNo(pageReqVO.getPageNo());
        reqVO.setPageSize(pageReqVO.getPageSize());
        PageResult<PrdtCatgory> prdtCatgoryPageResult = prdtCatgoryMapper.selectPage(reqVO);
        if (ObjectUtil.isEmpty(prdtCatgoryPageResult.getList())) {
            //如果为空则返回自己
            prdtCatgoryPageResult = prdtCatgoryMapper.selectPage(pageReqVO);
        }*/
        return HutoolBeanUtils.toBean(prdtCatgoryMapper.selectPage(pageReqVO), PrdtCatgoryRespVO.class);
    }

    /**
     * @Description: 获取平台商管理分类列表
     * @Author: liuxingyu
     * @Date: 2024/2/27 14:35
     */
    @Override
    public List<PrdtCatgoryRespVO> getCatgoryList(Long catgoryId, Long supplierId, Integer level) {
        if (ObjectUtil.isNull(supplierId)) {
            supplierId = SecurityUtils.getSupplierId();
        }
        List<PrdtCatgory> prdtCatgorieList = prdtCatgoryMapper.getCatgoryList(catgoryId, supplierId, level);
        return HutoolBeanUtils.toBean(prdtCatgorieList, PrdtCatgoryRespVO.class);
    }

    /**
     * @Description: 获取平台商管理分类列表 并设置是否存在商品信息
     * @Author: zhegnsenbing
     * @Date: 2024/7/814:35
     */
    @Override
    public List<PrdtCatgoryRespVO> getCatgoryByList(List<Long> catgoryIds,Long supplierId) {
        List<PrdtCatgory> prdtCatgorieList = prdtCatgoryMapper.getCatgoryList(null, null,null );

        if (ObjectUtil.isNotEmpty(catgoryIds)) {
            List<Long> catgoryIdList = prdtCatgoryMapper.getCategoriesWithProducts(catgoryIds,supplierId);
            /**
             * 先保留这一块 这一块主要功能是 拿到绑定了入驻商和商品的三级类别 然后对三级类别拿到二级和一级设置他的disabled
             */
//            // 查询三级类别及其二级类别的所有信息
//            List<PrdtCatgory> thirdLevelCategories = prdtCatgoryMapper.selectByIds(catgoryIds);
//            Set<Long> secondLevelIds = thirdLevelCategories.stream().map(PrdtCatgory::getPid).collect(Collectors.toSet());
//            List<PrdtCatgory> secondLevelCategories = prdtCatgoryMapper.selectByIds(new ArrayList<>(secondLevelIds));
//
//            // 预处理数据
//            Map<Long, Long> thirdToSecondLevelMap = thirdLevelCategories.stream()
//                    .collect(Collectors.toMap(PrdtCatgory::getCatgoryId, PrdtCatgory::getPid));
//            Map<Long, List<Long>> secondToThirdLevelMap = new HashMap<>();
//            for (PrdtCatgory category : thirdLevelCategories) {
//                secondToThirdLevelMap
//                        .computeIfAbsent(category.getPid(), k -> new ArrayList<>())
//                        .add(category.getCatgoryId());
//            }
//
//            // 设置 setDisabled 字段
//            Set<Long> categoriesWithProducts = new HashSet<>(catgoryIdList);
            for (PrdtCatgory prdtCatgory : prdtCatgorieList) {
                Long catgoryId = prdtCatgory.getCatgoryId();
                if (catgoryIdList.contains(catgoryId)) {
                    prdtCatgory.setIsDisabled(true);
                }
//                else if (secondToThirdLevelMap.containsKey(catgoryId)) {
//                    // 检查是否为二级类别，并且只有一个三级类别绑定商品
//                    List<Long> thirdLevelIds = secondToThirdLevelMap.get(catgoryId);
//
//                    // 判断二级类别下是否只有一个三级类别
//                    if (thirdLevelIds != null && thirdLevelIds.size() == 1) { // 判断是否只有一个三级类别
//                        Long thirdLevelId = thirdLevelIds.get(0);
//                        if (categoriesWithProducts.contains(thirdLevelId)) { // 该三级类别是否绑定了商品
//                            prdtCatgory.setIsDisabled(true);
//                            // 禁用一级类别
//                            Long secondLevelId = prdtCatgory.getCatgoryId();
//                            Long firstLevelId = prdtCatgoryMapper.selectById(secondLevelId).getPid(); // 查询一级类别
//                            if (firstLevelId != null) {
//                                for (PrdtCatgory category : prdtCatgorieList) {
//                                    if (category.getCatgoryId().equals(firstLevelId)) {
//                                        category.setDisabled(true);
//                                        break;
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
            }
        }

        return HutoolBeanUtils.toBean(prdtCatgorieList, PrdtCatgoryRespVO.class);
    }


    /**
     * @Description: 根据入驻商编号获取绑定的管理类别
     * @Author: liuxingyu
     * @Date: 2024/3/8 11:19
     */
    @Override
    public List<PrdtCatgoryRespVO> getCatgoryBySupplierId(Long supplierId) {
        List<PrdtCatgory> catgory3 = prdtCatgoryMapper.getCatgoryBySupplierId(supplierId);
        if(CollectionUtils.isEmpty(catgory3)){
            return new ArrayList<>();
        }
        //获取所有的二级管理类别
        List<Long> collect = catgory3.stream().map(PrdtCatgory::getPid).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(collect)) {
            return new ArrayList<>();
        }
        List<PrdtCatgory> catgory2 = prdtCatgoryMapper.selectByIds(collect);
        if(CollectionUtils.isEmpty(catgory2)){
            return new ArrayList<>();
        }
        //获取所有的一级管理类别
        collect = catgory2.stream().map(PrdtCatgory::getPid).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(collect)) {
            return new ArrayList<>();
        }
        List<PrdtCatgory> catgory = prdtCatgoryMapper.selectByIds(collect);
        catgory3.addAll(catgory2);
        if(CollectionUtils.isNotEmpty(catgory)){
            catgory3.addAll(catgory);
        }

        catgory3 = catgory3.stream().distinct().collect(Collectors.toList());
        return HutoolBeanUtils.toBean(catgory3, PrdtCatgoryRespVO.class);
    }

    /**
     * @Description: 根据管理分类ID获取管理分类信息
     * @Author: liuxingyu
     * @Date: 2024/3/25 10:49
     */
    @Override
    public PrdtCatgory getCatgoryByCatgoryId(Long catgoryId) {
        return prdtCatgoryMapper.selectById(catgoryId);
    }

    /**
     * 获取三级管理分类分润比例
     *
     * @param catgoryId
     * @return
     */
    @Override
    public CatgoryRateDTO getThreeCatgoryByIdAndAreaId(Long catgoryId, Long areaId) {
        // 只查第三级分类
        return prdtCatgoryMapper.selectByIdAndLevelThree(catgoryId, areaId);
    }

    /**
     * 请在事务方法外进行缓存操作
     *
     * @param catgoryId 管理分类ID
     */
    @Override
    public Long cleanCache(Long catgoryId) {
        //删除缓存
        catgoryDtoCache.remove(catgoryId);
        PrdtCatgory catgory = prdtCatgoryMapper.selectById(catgoryId);
        if (NumberPool.INT_ONE == catgory.getLevel()) {
            List<AreaDTO> areaist = areaApi.getListBySysCode(catgory.getSysCode()).getCheckedData();
            // 如果是一级分类, 则需要删除一级分类下所有
            // 三级分类 + 城市分润
            // 为什么需要查所有, 因为缓存是按照 管理分类ID + 城市ID 做的, 可能有个城市没有配置城市分润比例
            List<PrdtCatgory> towList = prdtCatgoryMapper.selectByPid(catgory.getCatgoryId());
            for (PrdtCatgory prdtCatgory : towList) {
                cleanThreeCatgoryRateCache(prdtCatgory, areaist);
            }
        }
        return catgoryId;
    }

    @Override
    public Long cleanRateCache(Long catgoryRateId) {
        // 城市扣点设置的二级管理分类
        PrdtCatgoryRate catgoryRate = prdtCatgoryRateMapper.selectById(catgoryRateId);
        if (Objects.nonNull(catgoryRate.getAreaId())) {
            // 清除扣点设置有关的二级管理分类数据即可
            AreaDTO areaDTO = areaApi.getAreaByAreaId(catgoryRate.getAreaId()).getCheckedData();
            // 找出二级分类, 便利下的三级分类, 清除扣点信息
            cleanThreeCatgoryRateCache(prdtCatgoryMapper.selectById(catgoryRate.getCatgoryId()), new ArrayList<>(Collections.singletonList(areaDTO)));
        }
        return catgoryRateId;
    }

    /**
     * @Description: 更改管理分类状态
     * @Author: liuxingyu
     * @Date: 2024/4/1 11:39
     */
    @Override
    public Integer changeStatus(PrdtCatgorySaveReqVO updateReqVO) {
        // 获取本身和子级的关联商品
        Long catgoryId = updateReqVO.getCatgoryId();
        List<Long> catgoryIdList = new ArrayList<>();
        List<PrdtCatgory> downCatgories = prdtCatgoryMapper.selectByPid(catgoryId);
        if (ToolUtil.isNotEmpty(downCatgories)){
            throw exception(CATEGORY_NOT_DELETE_HAVE_SON);
        }
        catgoryIdList.add(catgoryId);
        List<String> existsProduct = prdtCatgoryMapper.isExistsProduct(catgoryIdList);
        if (ToolUtil.isNotEmpty(existsProduct)){
            throw exception(CATEGORY_NOT_DELETE_HAVE_SHELF);
        }

        //删除缓存
        int insertBatch = prdtCatgoryMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrdtCatgory.class));
        catgorySysCodeListDtoCache.remove(SecurityUtils.getLoginUser().getSysCode());
        catgoryDtoCache.remove(updateReqVO.getCatgoryId());
        return insertBatch;
    }

    @Override
    public PrdtCatgory getCategoryByNameAndLevel(String categoryName, int level) {
        return prdtCatgoryMapper.selectCategoryByNameAndLevel(categoryName, level);
    }

    /**
     * @Description: 根据平台code获取平台管理分类
     * @Author: liuxingyu
     * @Date: 2024/5/7 11:15
     */
    @Override
    public List<PrdtCatgory> getListBySysCode(Long sysCode) {
        return prdtCatgoryMapper.getListBySysCode(sysCode);
    }

    /**
     * @Description: 获取平台管理类别一级Id
     * @Author: liuxingyu
     * @Date: 2024/5/16 17:03
     */
    @Override
    public List<CatgoryIdDTO> getCatgoryFirstId() {
        return prdtCatgoryMapper.getCatgoryFirstId();
    }

    /**
     * @Description: 通过id集合获取管理分类信息
     * @Author: liuxingyu
     * @Date: 2024/5/18 15:22
     */
    @Override
    public List<PrdtCatgory> getCatgoryByIds(List<Long> catgoryIds) {
        return prdtCatgoryMapper.getCatgoryByIds(catgoryIds);
    }

    /**
     * @Description: 平台管理分类同步平台展示分类
     * @Author: liuxingyu
     * @Date: 2024/5/24 9:03
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean copyToAreaClass(Long areaId) {
//        prdtAreaClassMapper.copyToAreaClass(areaId);
        Long count = prdtAreaClassMapper.getAreaClassCountByAreaId(areaId);
        if (count > 0) {
            throw new ServiceException("所选城市已存在展示分类，不允许复制！！！");
        }

        // 获取该城市下没有的平台管理分类
        List<PrdtCatgoryCopyRespDTO> prdtCatgoryRespList = prdtAreaClassMapper.getAreaClassNotExistByAreaId(areaId);

        // 新增数据Map
        Map<String, PrdtAreaClass> prdtAreaClasseListMap = new HashMap<>();

        // 复制管理分类
        List<PrdtAreaClass> prdtAreaClasseList = prdtCatgoryRespList.stream().map(prdtCatgoryCopyRespDTO -> {
            PrdtAreaClass prdtAreaClass = new PrdtAreaClass();
            prdtAreaClass.setAreaId(areaId);
            prdtAreaClass.setSysCode(prdtCatgoryCopyRespDTO.getSysCode());
            prdtAreaClass.setAreaClassName(prdtCatgoryCopyRespDTO.getCatgoryName());
            prdtAreaClass.setLevel(prdtCatgoryCopyRespDTO.getLevel());
            prdtAreaClass.setStatus(prdtCatgoryCopyRespDTO.getStatus());
            prdtAreaClass.setIcon(prdtCatgoryCopyRespDTO.getIcon());
            prdtAreaClass.setSort(prdtCatgoryCopyRespDTO.getSort());

            prdtAreaClasseListMap.put(prdtCatgoryCopyRespDTO.getCatgoryKey(), prdtAreaClass);

            return prdtAreaClass;
        }).collect(Collectors.toList());

        //bug fix,判空再调用
        if(CollectionUtils.isNotEmpty(prdtAreaClasseList)){
            prdtAreaClassMapper.insertBatch(prdtAreaClasseList);
        }

        // 查询数据Map
        Map<String, PrdtCatgoryCopyRespDTO> prdtCatgoryRespListMap = prdtCatgoryRespList.stream()
                .collect(Collectors.toMap(
                        PrdtCatgoryCopyRespDTO::getCatgoryKey, // Map的键是姓名的组合
                        catgory -> catgory                                                // Map的值是Person对象本身
                ));

        // 更新二、三级管理分类 PID
        List<PrdtAreaClass> prdtAreaClasseUpdateList = new ArrayList<>();
        prdtAreaClasseListMap.forEach((catgoryKey, prdtAreaClass) -> {
            if (ToolUtil.isNotEmpty(prdtAreaClass.getLevel()) && prdtAreaClass.getLevel() != NumberPool.INT_ONE) {
                PrdtCatgoryCopyRespDTO catgoryCopyRespDTO = prdtCatgoryRespListMap.get(catgoryKey);
                PrdtAreaClass areaClass = prdtAreaClasseListMap.get(catgoryCopyRespDTO.getPcatgoryKey());
                prdtAreaClass.setPid(ToolUtil.isEmpty(areaClass) ? 0L : areaClass.getAreaClassId());
                prdtAreaClasseUpdateList.add(prdtAreaClass);
            }
        });

        //bug fix,判空再调用
        if(CollectionUtils.isNotEmpty(prdtAreaClasseUpdateList)){
            prdtAreaClassMapper.updateBatch(prdtAreaClasseUpdateList);
        }

        return true;
    }

    /**
     * @Description: 平台管理分类同步平台展示分类
     * @Author: liuxingyu
     * @Date: 2024/5/24 9:03
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean copyToSaleClass(Long groupId) {
//        prdtSaleClassMapper.copyToSaleClass(groupId);
        Long count = prdtSaleClassMapper.getSaleClassBySysCode(SecurityUtils.getLoginUser().getSysCode());
        if (count > 0) {
            throw new ServiceException("登录账号所对应的平台已存在展示分类，不允许复制！！！");
        }

        // 获取该城市下没有的平台管理分类
        List<PrdtCatgoryCopyRespDTO> prdtCatgoryRespList = prdtSaleClassMapper.getSaleClassNotExistByGroupId(groupId);

        // 新增数据Map
        Map<String, PrdtSaleClass> prdtSaleClasseListMap = new HashMap<>();
        // 复制管理分类
        List<PrdtSaleClass> prdtSaleClasseList = prdtCatgoryRespList.stream().map(prdtCatgoryCopyRespDTO -> {
            PrdtSaleClass prdtSaleClass = new PrdtSaleClass();
            prdtSaleClass.setGroupId(groupId);
            prdtSaleClass.setSysCode(prdtCatgoryCopyRespDTO.getSysCode());
            prdtSaleClass.setName(prdtCatgoryCopyRespDTO.getCatgoryName());
            prdtSaleClass.setLevel(prdtCatgoryCopyRespDTO.getLevel());
            prdtSaleClass.setStatus(Long.parseLong(prdtCatgoryCopyRespDTO.getStatus()));
            prdtSaleClass.setIcon(prdtCatgoryCopyRespDTO.getIcon());
            prdtSaleClass.setSort(prdtCatgoryCopyRespDTO.getSort());

            prdtSaleClasseListMap.put(prdtCatgoryCopyRespDTO.getCatgoryKey(), prdtSaleClass);
            return prdtSaleClass;
        }).collect(Collectors.toList());
        prdtSaleClassMapper.insertBatch(prdtSaleClasseList);

        // 查询数据Map
        Map<String, PrdtCatgoryCopyRespDTO> prdtCatgoryRespListMap = prdtCatgoryRespList.stream()
                .collect(Collectors.toMap(
                        PrdtCatgoryCopyRespDTO::getCatgoryKey, // Map的键是姓名的组合
                        catgory -> catgory                                                // Map的值是Person对象本身
                ));

        // 更新二、三级管理分类 PID
        List<PrdtSaleClass> prdtAreaClasseUpdateList = new ArrayList<>();
        prdtSaleClasseListMap.forEach((catgoryKey, prdtSaleClass) -> {
            if (ToolUtil.isNotEmpty(prdtSaleClass.getLevel()) && prdtSaleClass.getLevel() != NumberPool.INT_ONE) {
                PrdtCatgoryCopyRespDTO catgoryCopyRespDTO = prdtCatgoryRespListMap.get(catgoryKey);
                PrdtSaleClass saleClass = prdtSaleClasseListMap.get(catgoryCopyRespDTO.getPcatgoryKey());
                prdtSaleClass.setPid(ToolUtil.isEmpty(saleClass) ? 0L : saleClass.getSaleClassId());
                prdtAreaClasseUpdateList.add(prdtSaleClass);
            }
        });

//        // 更新二、三级管理分类 PID
//        List<PrdtSaleClass> prdtAreaClasseUpdateList = prdtSaleClasseList.stream()
//                .filter(prdtSaleClass -> ToolUtil.isNotEmpty(prdtSaleClass.getLevel()) && prdtSaleClass.getLevel() != NumberPool.INT_ONE)
//                .map(prdtSaleClass -> {
//                    PrdtCatgoryCopyRespDTO catgoryCopyRespDTO = prdtCatgoryRespListMap.get(prdtSaleClass.getName() + "_" + prdtSaleClass.getLevel());
//                    PrdtSaleClass saleClass = prdtSaleClasseListMap.get(catgoryCopyRespDTO.getPcatgoryName() + "_" + catgoryCopyRespDTO.getPlevel());
//                    if (ToolUtil.isEmpty(saleClass)) {
//                        saleClass = prdtSaleClassMapper.selectByNameAndLevel(catgoryCopyRespDTO.getPcatgoryName(), catgoryCopyRespDTO.getPlevel());
//                    }
//                    prdtSaleClass.setPid(ToolUtil.isEmpty(saleClass) ? 0L : saleClass.getSaleClassId());
//                    return prdtSaleClass;
//                }).collect(Collectors.toList());
        prdtSaleClassMapper.updateBatch(prdtAreaClasseUpdateList);
        return true;
    }

    /**
     * @Description: 入驻商获取管理分类分页数据
     * @Author: liuxingyu
     * @Date: 2024/5/31 15:11
     */
    @Override
    public PageResult<PrdtCatgoryRespVO> getPageBySupplierId(PrdtCatgoryPageReqVO pageReqVO) {
        Page<PrdtCatgoryPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<PrdtCatgoryRespVO> pageResult = prdtCatgoryMapper.getPageBySupplierId(page, pageReqVO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    public String importBaseCategory(List<CategoryImportExcel> categoryList) {
        return importBaseCategoryEvent(categoryList,SecurityUtils.getLoginUser().getSysCode(),null,0).getMsg();
    }

    @Override
    public FileImportHandlerVo importBaseCategoryEvent(List<CategoryImportExcel> categoryList,Long sysCode,Long fileImportId,Integer seq) {
        FileImportHandlerVo fileImportHandlerVo = new FileImportHandlerVo();
        List<SysFileImportDtl> sysFileImportDtls = new ArrayList<>();
        int successNum = 0;
        int failureNum = 0;
        int totalNum = categoryList.size();
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        fileImportHandlerVo.setSuccessNum(successNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setTotalNum(totalNum);


        if (categoryList.isEmpty()) {
            // 如果导入数据为空，则不进行数据导入
            fileImportHandlerVo.setFailureNum(categoryList.size());
            fileImportHandlerVo.setTotalNum(categoryList.size());
            fileImportHandlerVo.setMsg("导入数据为空");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
        }

        Map<String, Long> categoryMap = new HashMap<>(); // 存储已插入的类别，避免重复插入

        for (int line = 0; line < categoryList.size(); line++) {
            //导入明细
            SysFileImportDtl sysFileImportDtl = new SysFileImportDtl();
            sysFileImportDtl.setSysCode(sysCode);
            sysFileImportDtl.setCreateBy(sysFileImportDtl.getCreateBy());
            sysFileImportDtl.setCreateTime(new Date());
            sysFileImportDtl.setFileImportId(fileImportId);

            if (failureMsg.length() > 2000) {
                break;
            }
            int cellNumber = line + 3+seq;
            CategoryImportExcel itemData = categoryList.get(line);
            sysFileImportDtl.setDtlJson(JsonUtils.toJsonString(itemData));

            try {
                validateCategoryImportExcel(itemData, cellNumber);

                Long primaryCategoryId = processCategory(itemData.getPrimaryCategoryName(), 1, categoryMap, itemData.getPartnerRate(), null,sysCode);
                Long secondaryCategoryId = processCategory(itemData.getSecondaryCategoryName(), 2, categoryMap, null, primaryCategoryId,sysCode);

                PrdtCatgory existingTertiaryCategory = prdtCatgoryMapper.getCategoryByNameAndParentId(itemData.getTertiaryCategoryName(), secondaryCategoryId);
                if(existingTertiaryCategory != null){
                    // 验证三级分类在当前二级分类下是否唯一
                    throw new Exception(StringUtils.format("三级类别 '{}' 已存在于二级类别 '{}' 下", itemData.getTertiaryCategoryName(), secondaryCategoryId));
                }else{
                    processCategory(itemData.getTertiaryCategoryName(), 3, categoryMap, null, secondaryCategoryId,sysCode);
                }
                successNum++;
                sysFileImportDtl.setStatus(STATUS_SUCCESS);
                sysFileImportDtls.add(sysFileImportDtl);
            } catch (Exception e) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据导入失败，错误信息：{}。", cellNumber, e.getMessage()));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据导入失败，错误信息：{}。", cellNumber, e.getMessage()));
                sysFileImportDtls.add(sysFileImportDtl);
            }
        }
        String resultMessage;

        fileImportHandlerVo.setTotalNum(totalNum);
        fileImportHandlerVo.setSuccessNum(successNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setList(sysFileImportDtls);
        if (failureNum > 0) {
            resultMessage = String.format("共导入%d条，成功%d条，失败%d条。失败原因如下：", categoryList.size(), successNum, failureNum)
                    + failureMsg.toString();
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            fileImportHandlerVo.setMsg(failureMsg.toString());
            return fileImportHandlerVo;
        } else {
            resultMessage = String.format("恭喜您，数据已全部导入成功！共 %d 条", successNum);
        }
        fileImportHandlerVo.setStatus(STATUS_SUCCESS);
        fileImportHandlerVo.setMsg(failureMsg.toString());
        return fileImportHandlerVo;
    }

    private void validateCategoryImportExcel(CategoryImportExcel itemData, int cellNumber) throws Exception {
        if (StringUtils.isEmpty(itemData.getPrimaryCategoryName()) || itemData.getPrimaryCategoryName().length() > 20) {
            throw new Exception(StringUtils.format("第{}行数据一级类别名称必填且长度不能超过20个字符", cellNumber));
        }
        if (Objects.isNull(itemData.getPartnerRate()) || itemData.getPartnerRate().compareTo(BigDecimal.ONE) < 0 || itemData.getPartnerRate().compareTo(BigDecimal.valueOf(100)) > 0) {
            throw new Exception(StringUtils.format("第{}行数据平台分润比例必填且必须在1到100之间，并且不能包含百分号", cellNumber));
        }
        if (StringUtils.isEmpty(itemData.getSecondaryCategoryName()) || itemData.getSecondaryCategoryName().length() > 20) {
            throw new Exception(StringUtils.format("第{}行数据二级类别名称必填且长度不能超过20个字符", cellNumber));
        }
        if (StringUtils.isEmpty(itemData.getTertiaryCategoryName()) || itemData.getTertiaryCategoryName().length() > 20) {
            throw new Exception(StringUtils.format("第{}行数据三级类别名称必填且长度不能超过20个字符", cellNumber));
        }
    }

    private Long processCategory(String categoryName, int level, Map<String, Long> categoryMap, BigDecimal partnerRate, Long pid,Long sysCode) throws Exception {
        PrdtCatgory existingCategory;
        //检查一级类别是否已经存在
        if(level == 1){
            Long cachedId = categoryMap.get(categoryName);
            if(cachedId != null){
                return cachedId;
            }
        }

        // 检查二级和三级类别在同一父级下是否存在
        existingCategory = prdtCatgoryMapper.getCategoryByNameAndParentId(categoryName, pid);
        if (existingCategory != null) {
            return existingCategory.getCatgoryId();
        }

        PrdtCatgory category = new PrdtCatgory();
        category.setPid(pid);
        category.setCatgoryName(categoryName);
        category.setStatus("1");
        category.setLevel(level);
        category.setSysCode(sysCode);
        if (partnerRate != null) {
            category.setPartnerRate(partnerRate.divide(BigDecimal.valueOf(100)));
        }
        prdtCatgoryMapper.insert(category);
        if(level == 1){
            categoryMap.put(categoryName, category.getCatgoryId());
        }
        return category.getCatgoryId();
    }

    @Override
    public CommonResult<List<BoundProductInfoDTO>> isCatgoryHangGoods(Long categoryId, Long sysCode) {
        // 获取类别信息
        PrdtCatgory category = prdtCatgoryMapper.selectById(categoryId);
        if (category == null) {
            throw new ServiceException("类别编号不存在，请检查! " + categoryId);
        }

        // 根据类别层级进行检查
        List<BoundProductInfoDTO> boundProductInfos;
        switch (category.getLevel()) {
            case 1:
                boundProductInfos = checkPrimaryCategoryHangGoods(categoryId, sysCode);
                break;
            case 2:
                boundProductInfos = checkSecondaryCategoryHangGoods(categoryId, sysCode);
                break;
            case 3:
                boundProductInfos = checkTertiaryCategoryHangGoods(categoryId, sysCode);
                break;
            default:
                throw new ServiceException("当前类别的级别不存在!: " + category.getLevel());
        }

        boolean hasGoods = !boundProductInfos.isEmpty();
        return CommonResult.success(hasGoods ? boundProductInfos : null);
    }

    @Override
    public SupplierClassRateStatusRespVO getSaleClassRate() {
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        if (Objects.isNull(sysCode)) {
            return new SupplierClassRateStatusRespVO(BigDecimal.ZERO, StringPool.ONE);
        }
        // 查询平台商默认配置的比例
        PayConfigDTO payConfigDTO = productCacheService.getPayConfigDTO(sysCode);
        if (Objects.isNull(payConfigDTO)) {
            return new SupplierClassRateStatusRespVO(BigDecimal.ZERO, StringPool.ONE);
        }
        if (Objects.isNull(payConfigDTO.getProfitModel())) {
            return new SupplierClassRateStatusRespVO(BigDecimal.ZERO, StringPool.ONE);
        }
        if (StringUtils.isEmpty(payConfigDTO.getDefaultSaleTotalCategoryRate())) {
            // 没有设置默认分成比例0
            return new SupplierClassRateStatusRespVO(BigDecimal.ZERO, payConfigDTO.getProfitModel());
        }
        return new SupplierClassRateStatusRespVO(new BigDecimal(payConfigDTO.getDefaultSaleTotalCategoryRate()), payConfigDTO.getProfitModel());
    }

    private List<BoundProductInfoDTO> checkPrimaryCategoryHangGoods(Long categoryId, Long sysCode) {
        List<BoundProductInfoDTO> boundProductInfoList = new ArrayList<>();

        // 获取一级类别下的所有二级类别
        List<PrdtCatgory> secondaryCategories = prdtCatgoryMapper.getListByPid(categoryId);
        if (ToolUtil.isNotEmpty(secondaryCategories)){
            throw exception(CATEGORY_NOT_DELETE_HAVE_SON);
        }
        for (PrdtCatgory secondaryCategory : secondaryCategories) {
            // 检查每个二级类别下的所有三级类别
            boundProductInfoList.addAll(checkSecondaryCategoryHangGoods(secondaryCategory.getCatgoryId(), sysCode));
        }

        // 检查是否有商品绑定到当前一级类别
        boundProductInfoList.addAll(getBoundProductInfo(categoryId, sysCode));

        return boundProductInfoList;
    }


    private List<BoundProductInfoDTO> checkSecondaryCategoryHangGoods(Long categoryId, Long sysCode) {
        List<BoundProductInfoDTO> boundProductInfoList = new ArrayList<>();

        // 获取二级类别下的所有三级类别
        List<PrdtCatgory> tertiaryCategories = prdtCatgoryMapper.getListByPid(categoryId);
        if (ToolUtil.isNotEmpty(tertiaryCategories)){
            throw exception(CATEGORY_NOT_DELETE_HAVE_SON);
        }
        for (PrdtCatgory tertiaryCategory : tertiaryCategories) {
            // 检查每个三级类别是否绑定商品
            boundProductInfoList.addAll(checkTertiaryCategoryHangGoods(tertiaryCategory.getCatgoryId(), sysCode));
        }

        // 检查是否有商品绑定到当前二级类别
        boundProductInfoList.addAll(getBoundProductInfo(categoryId, sysCode));

        return boundProductInfoList;
    }

    private List<BoundProductInfoDTO> checkTertiaryCategoryHangGoods(Long categoryId, Long sysCode) {
        return getBoundProductInfo(categoryId, sysCode);
    }

    /**
     * 获取分类绑定的商品信息
     *
     * @param categoryId 分类ID
     * @param sysCode 系统代码
     * @return 绑定的商品信息列表
     */
    private List<BoundProductInfoDTO> getBoundProductInfo(Long categoryId, Long sysCode) {
        // 查询类别是否下过单
        if (Objects.isNull(sysCode)) {
           sysCode = SecurityUtils.getLoginUser().getSysCode(); // 获取当前平台商
        }
        List<OrderDetailOpenDTO> orderData = supplierOrderApi.getCategoryExistOrder(categoryId, sysCode).getData();
        if (ToolUtil.isNotEmpty(orderData)){
            throw exception(CATEGORY_NOT_DELETE);
        }
        // 查询是否有上架商品
        List<BoundProductInfoDTO> boundProductInfos = spuMapper.getBoundProductInfoByCategoryIdAndSysCode(categoryId, sysCode);

        // 查询所有入驻商信息
        CommonResult<Map<Long, SupplierDTO>> result = supplierApi.getUserInfoByMap(null, null);
        Map<Long, SupplierDTO> supplierMap = result.getData();

        // 替换入驻商编号为入驻商名称
        for (BoundProductInfoDTO boundProductInfo : boundProductInfos) {
            Long supplierId = boundProductInfo.getSupplierId();
            if (supplierMap.containsKey(supplierId)) {
                boundProductInfo.setSupplierName(supplierMap.get(supplierId).getSupplierName());
            }
        }

        return boundProductInfos;
    }

    /**
     * @Description: 同步子集
     * @Author: liuxingyu
     * @Date: 2024/6/6 14:59
     */
    private void synSublevel(PrdtCatgorySaveReqVO updateReqVO) {
        Integer level = updateReqVO.getLevel();
        //同步二级
        List<PrdtCatgory> catgoryList = prdtCatgoryMapper.getListByPid(updateReqVO.getCatgoryId());
        if (ObjectUtil.isEmpty(catgoryList)) {
            return;
        }
        ++level;
        if (level > NumberPool.INT_ONE) {
            throw exception(PRDT_AREA_CLASS_SUB_LEVEL_MAX);
        }
        //如果当前为 1级 添加到 某个 1级下面,那么当前分类为 2级
        //需要校验 是否配置了平台分润 及 如果当前是 2级 需要校验 是否设置了 运营商分润及业务员分润

        Integer level2 = level;
        catgoryList.forEach(x -> {
            x.setLevel(level2);
        });
        //同步三级
        List<Long> ids = catgoryList.stream().map(PrdtCatgory::getCatgoryId).collect(Collectors.toList());
        List<PrdtCatgory> catgoryList3 = prdtCatgoryMapper.selectByPIds(ids);
        if (ObjectUtil.isNotEmpty(catgoryList3)) {
            ++level;
            if (level > NumberPool.INT_ONE) {
                throw exception(PRDT_AREA_CLASS_SUB_LEVEL_MAX);
            }
            Integer level3 = level;
            catgoryList3.forEach(y -> {
                y.setLevel(level3);
            });
            prdtCatgoryMapper.updateBatch(catgoryList3);
        }
    }

    private void cleanThreeCatgoryRateCache(PrdtCatgory prdtCatgory, List<AreaDTO> areaist) {
        List<PrdtCatgory> threeList = prdtCatgoryMapper.selectByPid(prdtCatgory.getCatgoryId());
        threeList.forEach(item -> {
            if (!areaist.isEmpty()) {
                for (AreaDTO area : areaist) {
                    // 删除所有 三级管理分类 + 城市
                    catgoryRateCache.remove(RedisConstants.getCategoryRate(item.getCatgoryId(), area.getAreaId()));
                }
            }
        });
    }


    private void validatePrdtCatgoryExists(Long classId) {
        if (prdtCatgoryMapper.selectById(classId) == null) {
            throw exception(PRDT_CATGORY_NOT_EXISTS);
        }
    }

    /**
     * @Description: 校验平台管理类别级别
     * @Author: liuxingyu
     * @Date: 2024/4/15 9:31
     */
    private void checkCatgoryLevel(PrdtCatgorySaveReqVO createReqVO) {
        //ID不为空则修改
        if (ObjectUtil.isNotNull(createReqVO.getCatgoryId())) {
            PrdtCatgory prdtCatgory = prdtCatgoryMapper.selectById(createReqVO.getCatgoryId());
            if (ObjectUtil.notEqual(nullToZero(createReqVO.getPid()), nullToZero(prdtCatgory.getPid()))) {
                //如果当前分类非末级则不允许改变层级(存在子级)
                List<PrdtCatgory> list = prdtCatgoryMapper.getListByPid(createReqVO.getCatgoryId());
                if (ObjectUtil.isNotEmpty(list)) {
                    throw exception(PRDT_CATGORY_IS_SUB_EXISTS);
                }
                //校验是否绑定了商品
                List<PrdtSpu> spuList = spuMapper.selectByCatGoryId(createReqVO.getCatgoryId());
                if (ObjectUtil.isNotEmpty(spuList)) {
                    throw exception(PRDT_CATGORY_BIND_SPU_EXISTS);
                }
            }
        }
        //获取父级类别
        if (ObjectUtil.isNotNull(createReqVO.getPid()) && ObjectUtil.notEqual(createReqVO.getPid(), NumberPool.LONG_ZERO)) {
            PrdtCatgory prdtCatgory = prdtCatgoryMapper.selectById(createReqVO.getPid());
            //校验父级类别
            if (ObjectUtil.isNull(prdtCatgory) || ObjectUtil.isNull(prdtCatgory.getLevel())) {
                throw exception(PRDT_CATGORY_CATGORY_PID_ERROR);
            }
            //校验级别
            if (ObjectUtil.equal(prdtCatgory.getLevel(), NumberPool.INT_THREE)) {
                throw exception(PRDT_CATGORY_CATGORY_LEVEL_EXCEED_MAX);
            }
            createReqVO.setLevel(prdtCatgory.getLevel() + NumberPool.INT_ONE);
        } else {
            createReqVO.setLevel(NumberPool.INT_ONE);
        }
    }

    /**
     * @Description: 校验分润比例
     * @Param: PrdtCatgorySaveReqVO saveReqVO
     * @Author: liuxingyu
     * @Date: 2024/3/20 16:35
     */
    private void checkRate(PrdtCatgorySaveReqVO saveReqVO) {
        if (ObjectUtil.equal(NumberPool.INT_ONE, saveReqVO.getPartnerRate().compareTo(new BigDecimal(StringPool.ONE)))) {
            throw exception(PRDT_CATGORY_RATE_ERROR);
        }
        // 分润比例最大29%
        if (Objects.nonNull(saveReqVO.getSaleTotalRate()) && NumberUtil.isGreater(saveReqVO.getSaleTotalRate(), new BigDecimal("0.29"))) {
            throw exception(PRDT_SPU_MAX_PROFIT_RATE);
        }
    }

    /**
     * @Description: 校验分类名是否唯一
     * @Param: String name 名称, Integer pid 父Id
     * @Author: liuxingyu
     * @Date: 2024/2/27 16:12
     */
    private void checkCatgoryNameUnique(String name, Long pid, Long id) {
        Long count = prdtCatgoryMapper.selectCatgoryNameCount(name, pid, id);
        if (ObjectUtil.notEqual(NumberPool.LONG_ZERO, count)) {
            throw exception(PRDT_CATGORY_CATGORY_NAME_NOT_UNIQUE);
        }
    }

    /**
     * @Description: 如果为null 转换成0
     * @Author: liuxingyu
     * @Date: 2024/6/7 11:19
     */
    private Long nullToZero(Long pid) {
        return ObjectUtil.isNull(pid) ? 0L : pid;
    }

    @Override
    public CatgoryDeleteDTO batchDeleteCatogory(List<Long> classIds) {
        CatgoryDeleteDTO catgoryDeleteDTO = new CatgoryDeleteDTO();
        HashSet<Long> thirdCategoryIds = new HashSet<>();
        HashSet<String> thirdCategoryNames = new HashSet<>();
        HashSet<String> thirdCategoryPrdtSpuIds = new HashSet<>();
        HashSet<String> thirdCategoryPrdtSpuNames = new HashSet<>();
        HashSet<Long> orderCategoryIds = new HashSet<>();
        HashSet<String> orderCategoryNames = new HashSet<>();
        Long sysCode = SecurityUtils.getLoginUser().getSysCode(); // 获取当前平台商
        for (Long classId : classIds) {
            // 获取类别信息
            PrdtCatgory category = prdtCatgoryMapper.selectById(classId);
            if (category == null) {
                continue;
            }
            List<Long> allLevelClassIds = new ArrayList<>();
            List<Long> thridClassIds = new ArrayList<>();

            // 根据类别层级进行删除
            switch (category.getLevel()) {
                // 如果传过来的id是一级，就把123全部删掉
                case 1:
                    allLevelClassIds.add(classId); // 添加一级类别ID
                    List<PrdtCatgory> secondaryCategories = prdtCatgoryMapper.getListByPid(classId);
                    if (ToolUtil.isNotEmpty(secondaryCategories)){
                        for (PrdtCatgory secondaryCategory : secondaryCategories) {
                            allLevelClassIds.add(secondaryCategory.getCatgoryId()); // 添加二级类别ID
                            List<PrdtCatgory> tertiaryCategories = prdtCatgoryMapper.getListByPid(secondaryCategory.getCatgoryId());
                            if (ToolUtil.isNotEmpty(tertiaryCategories)){
                                for (PrdtCatgory tertiaryCategory : tertiaryCategories) {
                                    allLevelClassIds.add(tertiaryCategory.getCatgoryId()); // 添加三级类别ID
                                    thridClassIds.add(tertiaryCategory.getCatgoryId());
                                }
                            }
                        }
                    }
                    // 是否允许删除
                    CatgoryDeleteDTO catgoryDeleteData = deleteCategory(thridClassIds, sysCode);
                    if (ToolUtil.isEmpty(catgoryDeleteData.getThirdCategoryIds())){ // 没有删除失败的类别就将选择的三级类别删除
                        for (Long categoryId : allLevelClassIds) {
                            prdtCatgoryMapper.deleteById(categoryId); // 删除类别
                        }
                    }else {
                        thirdCategoryIds.addAll(catgoryDeleteData.getThirdCategoryIds()); // 删除失败的类别
                        thirdCategoryNames.addAll(catgoryDeleteData.getThirdCategoryNames()); // 删除失败的类别名称
                        thirdCategoryPrdtSpuIds.addAll(catgoryDeleteData.getThirdCategoryPrdtSpuIds()); // 删除失败类别关联的商品编号
                        thirdCategoryPrdtSpuNames.addAll(catgoryDeleteData.getThirdCategoryPrdtSpuNames()); // 删除失败类别关联的商品名称
                        orderCategoryIds.addAll(catgoryDeleteData.getOrderCategoryIds()); // 删除失败的类别关联的订单
                        orderCategoryNames.addAll(catgoryDeleteData.getOrderCategoryNames());
                    }
                    break;
                    // 如果是二级类别，就删掉23
                case 2:
                    allLevelClassIds.add(classId); // 添加二级类别ID
                    List<PrdtCatgory> tertiaryCategories = prdtCatgoryMapper.getListByPid(classId);
                    if (ToolUtil.isNotEmpty(tertiaryCategories)){
                        for (PrdtCatgory tertiaryCategory : tertiaryCategories) {
                            allLevelClassIds.add(tertiaryCategory.getCatgoryId()); // 添加三级类别ID
                            thridClassIds.add(tertiaryCategory.getCatgoryId());
                        }
                    }
                    CatgoryDeleteDTO catgoryDeleteData2 = deleteCategory(thridClassIds, sysCode);
                    if (ToolUtil.isEmpty(catgoryDeleteData2.getThirdCategoryIds())){ // 没有删除失败的类别就将选择的三级类别删除
                        for (Long categoryId : allLevelClassIds) {
                            prdtCatgoryMapper.deleteById(categoryId); // 删除类别
                        }
                    }else {
                        thirdCategoryIds.addAll(catgoryDeleteData2.getThirdCategoryIds()); // 删除失败的类别
                        thirdCategoryNames.addAll(catgoryDeleteData2.getThirdCategoryNames()); // 删除失败的类别名称
                        thirdCategoryPrdtSpuIds.addAll(catgoryDeleteData2.getThirdCategoryPrdtSpuIds()); // 删除失败类别关联的商品编号
                        thirdCategoryPrdtSpuNames.addAll(catgoryDeleteData2.getThirdCategoryPrdtSpuNames()); // 删除失败类别关联的商品名称
                        orderCategoryIds.addAll(catgoryDeleteData2.getOrderCategoryIds()); // 删除失败的类别关联的订单
                        orderCategoryNames.addAll(catgoryDeleteData2.getOrderCategoryNames());
                    }
                    break;
                    // 如果是三级类别，就删掉3
                case 3:
                    allLevelClassIds.add(classId); // 添加二级类别ID
                    CatgoryDeleteDTO catgoryDeleteData3 = deleteCategory(allLevelClassIds, sysCode);
                    if (ToolUtil.isEmpty(catgoryDeleteData3.getThirdCategoryIds())){ // 没有删除失败的类别就将选择的三级类别删除
                        for (Long categoryId : allLevelClassIds) {
                            prdtCatgoryMapper.deleteById(categoryId); // 删除类别
                        }
                    }else {
                        thirdCategoryIds.addAll(catgoryDeleteData3.getThirdCategoryIds()); // 删除失败的类别
                        thirdCategoryNames.addAll(catgoryDeleteData3.getThirdCategoryNames()); // 删除失败的类别名称
                        thirdCategoryPrdtSpuIds.addAll(catgoryDeleteData3.getThirdCategoryPrdtSpuIds()); // 删除失败类别关联的商品编号
                        thirdCategoryPrdtSpuNames.addAll(catgoryDeleteData3.getThirdCategoryPrdtSpuNames()); // 删除失败类别关联的商品名称
                        orderCategoryIds.addAll(catgoryDeleteData3.getOrderCategoryIds()); // 删除失败的类别关联的订单
                        orderCategoryNames.addAll(catgoryDeleteData3.getOrderCategoryNames());
                    }
                    break;
                default:
                    throw new ServiceException("当前类别的级别不存在: " + category.getLevel());
            }
        }
        catgoryDeleteDTO.setThirdCategoryIds(new ArrayList<>(thirdCategoryIds));
        catgoryDeleteDTO.setThirdCategoryNames(new ArrayList<>(thirdCategoryNames));
        catgoryDeleteDTO.setThirdCategoryPrdtSpuIds(new ArrayList<>(thirdCategoryPrdtSpuIds));
        catgoryDeleteDTO.setThirdCategoryPrdtSpuNames(new ArrayList<>(thirdCategoryPrdtSpuNames));
        catgoryDeleteDTO.setOrderCategoryIds(new ArrayList<>(orderCategoryIds));
        catgoryDeleteDTO.setOrderCategoryNames(new ArrayList<>(orderCategoryNames));
        return catgoryDeleteDTO;
    }

    @Override
    public List<PrdtCatgoryRespVO> getCategoryBySupplierIds(List<String> supplierIds) {
        List<PrdtCatgory> catgory3 = prdtCatgoryMapper.getCategoryBySupplierIds(supplierIds);
        if(CollectionUtils.isEmpty(catgory3)){
            return new ArrayList<>();
        }
        //获取所有的二级管理类别
        List<Long> collect = catgory3.stream().map(PrdtCatgory::getPid).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(collect)) {
            return new ArrayList<>();
        }
        List<PrdtCatgory> catgory2 = prdtCatgoryMapper.selectByIds(collect);
        if(CollectionUtils.isEmpty(catgory2)){
            return new ArrayList<>();
        }
        //获取所有的一级管理类别
        collect = catgory2.stream().map(PrdtCatgory::getPid).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(collect)) {
            return new ArrayList<>();
        }
        List<PrdtCatgory> catgory = prdtCatgoryMapper.selectByIds(collect);
        catgory3.addAll(catgory2);
        if(CollectionUtils.isNotEmpty(catgory)){
            catgory3.addAll(catgory);
        }

        // 根据catgoryId去重
        catgory3 = catgory3.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PrdtCatgory::getCatgoryId))), ArrayList::new));
        return HutoolBeanUtils.toBean(catgory3, PrdtCatgoryRespVO.class);
    }

    public CatgoryDeleteDTO deleteCategory(List<Long> categoryId, Long sysCode){
        CatgoryDeleteDTO catgoryDeleteDTO = new CatgoryDeleteDTO();
        ArrayList<Long> thirdCategoryIds = new ArrayList<>();
        ArrayList<String> thirdCategoryNames = new ArrayList<>();
        ArrayList<String> thirdCategoryPrdtSpuIds = new ArrayList<>();
        ArrayList<String> thirdCategoryPrdtSpuNames = new ArrayList<>();
        ArrayList<Long> orderCategoryIds = new ArrayList<>();
        ArrayList<String> orderCategoryNames = new ArrayList<>();
        for (Long classId : categoryId) {
            CatgoryDTO catgoryDTO = new CatgoryDTO();
            List<OrderDetailOpenDTO> orderData = supplierOrderApi.getCategoryExistOrder(classId, sysCode).getData();
            List<BoundProductInfoDTO> boundProductInfos = spuMapper.getBoundProductInfoByCategoryIdAndSysCode(classId, sysCode);
            if (ToolUtil.isNotEmpty(orderData) || ToolUtil.isNotEmpty(boundProductInfos)){
                thirdCategoryIds.add(classId);
                catgoryDTO = productCacheService.getCatgoryDTO(classId);
                thirdCategoryNames.add(catgoryDTO.getCatgoryName());
            }
            if (ToolUtil.isNotEmpty(orderData)){
                orderCategoryIds.add(classId); //类别有关联订单
                orderCategoryNames.add(catgoryDTO.getCatgoryName());
            }
            if (ToolUtil.isNotEmpty(boundProductInfos)){
                for (BoundProductInfoDTO boundProductInfo : boundProductInfos) {
                    thirdCategoryPrdtSpuIds.add(boundProductInfo.getProductNumber());
                    thirdCategoryPrdtSpuNames.add(boundProductInfo.getProductName());
                }
            }
        }
        catgoryDeleteDTO.setThirdCategoryIds(thirdCategoryIds);
        catgoryDeleteDTO.setThirdCategoryNames(thirdCategoryNames);
        catgoryDeleteDTO.setThirdCategoryPrdtSpuIds(thirdCategoryPrdtSpuIds);
        catgoryDeleteDTO.setThirdCategoryPrdtSpuNames(thirdCategoryPrdtSpuNames);
        catgoryDeleteDTO.setOrderCategoryIds(orderCategoryIds);
        catgoryDeleteDTO.setOrderCategoryNames(orderCategoryNames);
        return catgoryDeleteDTO;
    }



}
