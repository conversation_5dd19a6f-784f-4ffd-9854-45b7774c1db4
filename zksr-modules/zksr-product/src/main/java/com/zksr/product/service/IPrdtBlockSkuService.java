package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.controller.blockScheme.excel.BlockSkuExcelToListVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSkuRespVO;
import com.zksr.product.domain.PrdtBlockSku;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSkuPageReqVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSkuSaveReqVO;

import java.util.List;

/**
 * 经营屏蔽skuService接口
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
public interface IPrdtBlockSkuService {

    /**
     * 新增经营屏蔽sku
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtBlockSku(@Valid PrdtBlockSkuSaveReqVO createReqVO);

    /**
     * 修改经营屏蔽sku
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtBlockSku(@Valid PrdtBlockSkuSaveReqVO updateReqVO);

    /**
     * 删除经营屏蔽sku
     *
     * @param blockSkuId 主键
     */
    public void deletePrdtBlockSku(Long blockSkuId);

    /**
     * 批量删除经营屏蔽sku
     *
     * @param blockSkuIds 需要删除的经营屏蔽sku主键集合
     * @return 结果
     */
    public void deletePrdtBlockSkuByBlockSkuIds(Long[] blockSkuIds);

    /**
     * 获得经营屏蔽sku
     *
     * @param blockSkuId 主键
     * @return 经营屏蔽sku
     */
    public PrdtBlockSku getPrdtBlockSku(Long blockSkuId);

    /**
     * 获得经营屏蔽sku分页
     *
     * @param pageReqVO 分页查询
     * @return 经营屏蔽sku分页
     */
    PageResult<PrdtBlockSku> getPrdtBlockSkuPage(PrdtBlockSkuPageReqVO pageReqVO);

    List<PrdtBlockSkuRespVO> excelToList(List<BlockSkuExcelToListVO> skuList, String schemeNo);
}
