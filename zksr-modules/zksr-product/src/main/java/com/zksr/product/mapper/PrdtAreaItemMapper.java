package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.controller.areaItem.vo.PrdtAreaItemPageReqVO;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.controller.areaItem.vo.PrdtAreaItemRespVO;
import com.zksr.product.controller.spu.vo.SpuSkuReleaseListVO;
import com.zksr.product.domain.PrdtAreaItem;
import com.zksr.report.api.homePages.dto.HomePagesSkuDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.xmlbeans.impl.xb.ltgfmt.Code;

import java.util.List;

import static com.zksr.product.constant.ProductConstant.PRDT_SHELF_STATUS_1;


/**
 * 城市上架商品Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Mapper
@SuppressWarnings("all")
public interface PrdtAreaItemMapper extends BaseMapperX<PrdtAreaItem> {
    default PageResult<PrdtAreaItem> selectPage(PrdtAreaItemPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtAreaItem>()
                    .eqIfPresent(PrdtAreaItem::getAreaItemId, reqVO.getAreaItemId())
                    .eqIfPresent(PrdtAreaItem::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtAreaItem::getAreaId, reqVO.getAreaId())
                    .eqIfPresent(PrdtAreaItem::getAreaClassId, reqVO.getAreaClassId())
                    .eqIfPresent(PrdtAreaItem::getShelfStatus, reqVO.getShelfStatus())
                    .eqIfPresent(PrdtAreaItem::getSpuId, reqVO.getSpuId())
                    .eqIfPresent(PrdtAreaItem::getSkuId, reqVO.getSkuId())
                .orderByDesc(PrdtAreaItem::getAreaItemId));
    }

    //Spu分页查询
    public Page<PrdtAreaItemPageRespVO> selectPageAreaItemByPage(@Param("reqVO")PrdtAreaItemPageReqVO reqVO, @Param("page") Page<PrdtAreaItemPageReqVO> page);

    public List<PrdtAreaItemPageRespVO> selectPageAreaItemByPage(@Param("reqVO")PrdtAreaItemPageReqVO reqVO);

    //根据查询条件查询城市上架商品
    default PrdtAreaItem selectAreaItemByItem(PrdtAreaItemRespVO vo){
        LambdaQueryWrapperX<PrdtAreaItem> wrapper = new LambdaQueryWrapperX<PrdtAreaItem>()
                .eqIfPresent(PrdtAreaItem::getAreaItemId, vo.getAreaItemId())
                .eqIfPresent(PrdtAreaItem::getSkuId, vo.getSkuId())
                .eqIfPresent(PrdtAreaItem::getSpuId,vo.getSpuId())
                .eqIfPresent(PrdtAreaItem::getSortNum,vo.getSortNum())
                .eqIfPresent(PrdtAreaItem::getSupplierId,vo.getSupplierId())
                .eqIfPresent(PrdtAreaItem::getAreaClassId,vo.getAreaClassId());
        return selectOne(wrapper);
    }

    //校验城市上架商品是否存在
    default PrdtAreaItem selectAreaItemByCheck(PrdtAreaItem item){
        LambdaQueryWrapperX<PrdtAreaItem> wrapper = new LambdaQueryWrapperX<PrdtAreaItem>()
                .eqIfPresent(PrdtAreaItem::getSkuId, item.getSkuId())
                .eqIfPresent(PrdtAreaItem::getSpuId,item.getSpuId())
                .eqIfPresent(PrdtAreaItem::getSupplierId,item.getSupplierId())
                .eqIfPresent(PrdtAreaItem::getAreaId,item.getAreaId())
                .eqIfPresent(PrdtAreaItem::getAreaClassId,item.getAreaClassId());
        return selectOne(wrapper);
    }

    //根据ID查询城市上架商品列表
    default List<PrdtAreaItem> selectAreaItemListById(Long [] areaItemIds){
        LambdaQueryWrapper<PrdtAreaItem> wrapper = new LambdaQueryWrapperX<PrdtAreaItem>()
                .in(PrdtAreaItem::getAreaItemId,areaItemIds);
        return selectList(wrapper);
    }

    //新增上架商品
    default Long insertPrdtAreaItem(PrdtAreaItem item){
        //获取当前城市商品类型的最大排序号
        Integer sortNum = selectMaxSort(item);
        item.setSortNum(sortNum + Code.GREATER_THAN);
        // 插入
        insert(item);
        // 返回
        return item.getAreaItemId();
    }

    //获取该城市类别最大的商品排序序号
    public Integer selectMaxSort(@Param("item") PrdtAreaItem item);

    //批量上下架入驻商商品
    public void updatePrdtAreaItemShelfStatus(@Param("areaItemIds")Long [] supplierItemIds,
                                              @Param("minShelfStatus")Integer minShelfStatus,
                                              @Param("midShelfStatus")Integer midShelfStatus,
                                              @Param("largeShelfStatus")Integer largeShelfStatus,
                                              @Param("shelfStatus")Integer shelfStatus);

    //查询上架商品列表
    public List<PrdtAreaItemPageReqVO> selectAreaItemList(@Param("reqVO")PrdtAreaItemPageReqVO reqVO);

    public void updateAreaItemBatchShelfStatus(@Param("spuId")Long spuId, @Param("skuId")Long skuId,@Param("areaItemIds") List<Long> areaItemIds);
    //停用城市商品下架
    public void updateAreaItemBatchMidShelfStatus(@Param("spuId")Long spuId, @Param("skuId")Long skuId,@Param("areaItemIds") List<Long> areaItemIds);
    //停用城市商品下架
    public void updateAreaItemBatchLargeShelfStatus(@Param("spuId")Long spuId, @Param("skuId")Long skuId,@Param("areaItemIds") List<Long> areaItemIds);

    //查询城市已上架商品列表
    default List<PrdtAreaItem> selectAreaItemByList(PrdtAreaItemRespVO vo){
        LambdaQueryWrapper<PrdtAreaItem> wrapper = new LambdaQueryWrapperX<PrdtAreaItem>()
                .eqIfPresent(PrdtAreaItem::getSkuId, vo.getSkuId())
                .eqIfPresent(PrdtAreaItem::getSpuId,vo.getSpuId())
                .eqIfPresent(PrdtAreaItem::getSortNum,vo.getSortNum())
                .eqIfPresent(PrdtAreaItem::getSupplierId,vo.getSupplierId())
                .eqIfPresent(PrdtAreaItem::getAreaClassId,vo.getAreaClassId())
                .eqIfPresent(PrdtAreaItem::getShelfStatus,vo.getShelfStatus())
                .eqIfPresent(PrdtAreaItem::getAreaId,vo.getAreaId())
                .inIfPresent(PrdtAreaItem::getSkuId,vo.getSkuIdList());
        return selectList(wrapper);
    }

    //查询小程序商品上架商品列表
    public List<AreaItemDTO> selectAreaItemListByApi(@Param("reqVO")AreaItemDTO reqVO);

    List<SpuSkuReleaseListVO> selectByReleaseSpuId(@Param("spuId") Long spuId, @Param("areaId") Long areaId, @Param("classId") Long classId);

    /**
    * @Description: 根据城市展示分类ID获取城市上架商品集合
    * @Author: liuxingyu
    * @Date: 2024/6/7 8:58
    */
    default List<PrdtAreaItem> selectByAreaClassId(Long areaClassId){
        return selectList(new LambdaQueryWrapper<PrdtAreaItem>().eq(PrdtAreaItem::getAreaClassId,areaClassId));
    }

    default List<PrdtAreaItem> selectSaleQtyTotalList(Long minAreaItemId) {
        return selectList(
                new LambdaQueryWrapper<PrdtAreaItem>()
                        .gt(PrdtAreaItem::getAreaItemId, minAreaItemId)
                        .eq(PrdtAreaItem::getShelfStatus, NumberPool.INT_ONE)
                        .select(PrdtAreaItem::getSkuId, PrdtAreaItem::getAreaItemId)
                        .orderByAsc(PrdtAreaItem::getAreaItemId)
                        .last("LIMIT 1000")
        );
    }

    /**
     * 查询sku关联的有多少上架的
     *
     * @param skuId
     * @return
     */
    default Long selectBySkuIdReleaseCount(Long skuId) {
        return selectCount(
                new LambdaQueryWrapper<PrdtAreaItem>()
                        .eq(PrdtAreaItem::getSkuId, skuId)
                        .eq(PrdtAreaItem::getShelfStatus, NumberPool.INT_ONE)
        );
    }

    default PrdtAreaItem selectByAreaIdAndSaleId(Long skuId, Long areaId, Long areaClassId) {
        return selectOne(
                new LambdaQueryWrapper<PrdtAreaItem>()
                        .eq(PrdtAreaItem::getSkuId, skuId)
                        .eq(PrdtAreaItem::getAreaId, areaId)
                        .eq(PrdtAreaItem::getAreaClassId, areaClassId)
                        .last(StringPool.LIMIT_ONE)
        );
    }

    default List<PrdtAreaItem> selectByReleaseRefreshSpuId(Long spuId) {
        return selectList(
                new LambdaQueryWrapper<PrdtAreaItem>()
                        .select(
                                PrdtAreaItem::getAreaClassId,
                                PrdtAreaItem::getAreaId,
                                PrdtAreaItem::getSkuId,
                                PrdtAreaItem::getAreaItemId
                        )
                        .eq(PrdtAreaItem::getSpuId, spuId)
                        .eq(PrdtAreaItem::getShelfStatus, PRDT_SHELF_STATUS_1)
        );
    }

    /**
     * 根据城市展示分类ID集合 获取城市上架商品集合
     * @param areaClassIds
     * @return
     */
    default List<PrdtAreaItem> selectByAreaClassIdList(List<Long> areaClassIds){
        return selectList(new LambdaQueryWrapper<PrdtAreaItem>().in(PrdtAreaItem::getAreaClassId,areaClassIds));
    }

    default Long selectSpuSkuIsOnShelf(PrdtAreaItem areaItem){
         return selectCount(
                new LambdaQueryWrapper<PrdtAreaItem>()
                        .eq(PrdtAreaItem::getSupplierId, areaItem.getSupplierId())
                        .eq(PrdtAreaItem::getSpuId, areaItem.getSpuId())
                        .eq(PrdtAreaItem::getSkuId, areaItem.getSkuId())
                        .eq(PrdtAreaItem::getAreaId, areaItem.getAreaId())
                        .eq(PrdtAreaItem::getShelfStatus, PRDT_SHELF_STATUS_1)
        );
    }

    /**
     * 获取PC首页SKU数据
     * 注：这里查询运营商上架数据时需单独查询，不可根据区域城市进行分组查询结果，因一个商品SKU可以上架到同一个运营商下的不同的区域城市下。
     * @param reqVO
     * @return
     */
    List<HomePagesSkuDataRespDTO> getHomePagesSkuData(HomePagesReqVO reqVO);

    default PrdtAreaItem getAreaItemBySpuCombineId (Long spuCombineId){
        return selectOne(
                new LambdaQueryWrapper<PrdtAreaItem>()
                        .eq(PrdtAreaItem::getSpuCombineId, spuCombineId)
                        .last(StringPool.LIMIT_ONE)
        );
    }

    default void deleteBySpuCombineId(Long spuCombineId){
        delete(new LambdaQueryWrapper<PrdtAreaItem>().eq(PrdtAreaItem::getSpuCombineId, spuCombineId));
    }

    default PrdtAreaItem getAreaItemByActivityId (Long activityId){
        return selectOne(
                new LambdaQueryWrapper<PrdtAreaItem>()
                        .eq(PrdtAreaItem::getActivityId, activityId)
                        .last(StringPool.LIMIT_ONE)
        );
    }

    default List<PrdtAreaItem> getAreaItemShelfBySkuId(Long skuId, Integer shelfStatus){
        return selectList(
                new LambdaQueryWrapper<PrdtAreaItem>()
                        .eq(PrdtAreaItem::getSkuId, skuId)
                        .eq(shelfStatus != null, PrdtAreaItem::getShelfStatus, shelfStatus)
        );
    }

    default List<PrdtAreaItem> getAreaItemSpuIdList(List<Long> shelfSpuList){
        return selectList(
                new LambdaQueryWrapper<PrdtAreaItem>()
                        .select(PrdtAreaItem::getAreaItemId)
                        .in(PrdtAreaItem::getSpuId, shelfSpuList)
        );
    }
    default List<PrdtAreaItem> getBySkuId(Long skuId){
        return selectList(
                new LambdaQueryWrapper<PrdtAreaItem>()
                        .eq(PrdtAreaItem::getSkuId, skuId)
        );
    }
}
