package com.zksr.product.api.spu;

import com.zksr.common.core.domain.vo.openapi.receive.SpuReceiveVO;
import cn.hutool.core.bean.BeanUtil;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.spu.dto.*;
import com.zksr.product.api.spu.form.PrdSpuImportForm;
import com.zksr.product.api.spu.vo.PrdtSpuGroupInVO;
import com.zksr.product.api.spu.vo.PrdtSpuGroupSaveInVO;
import com.zksr.product.api.spu.vo.PrdtSpuNotItemPageReqVo;
import com.zksr.product.api.spu.vo.PrdtSpuPageReqVO;
import com.zksr.product.controller.spu.dto.PrdtSpuUpdateRespDTO;
import com.zksr.product.api.spu.vo.*;
import com.zksr.product.domain.PrdtSpu;
import com.zksr.product.service.IPrdtSpuService;
import com.zksr.trade.api.driver.form.TrdDriverImportForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
@InnerAuth
public class SpuApiImpl implements SpuApi {

    @Autowired
    private IPrdtSpuService prdtSpuService;

    @Override
    public CommonResult<SpuDTO> getBySpuId(Long spuId) {
        PrdtSpu spu = prdtSpuService.getBySpuId(spuId);
        return success(HutoolBeanUtils.toBean(spu, SpuDTO.class));
    }

    /**
    * @Description: 获取商品信息
    * @Author: liuxingyu
    * @Date: 2024/4/11 16:47
    */
    @Override
    @InnerAuth
    public CommonResult<ProductDTO> getByProduct(String param) {
        return success(prdtSpuService.getByProduct(param));
    }

    @Override
    public CommonResult<PageResult<PrdtSpuNotItemPageReqVo>> getSpuPageNotItemByApi(PrdtSpuNotItemPageReqVo reqVo) {
        return success(prdtSpuService.getPrdtSpuPageByNotItem(reqVo));
    }


    /**
     *   分页查询
     */
    @Override
    @InnerAuth
    public CommonResult<PageResult<PrdtSpuPageReqVO>> getPrdtList(PrdtSpuPageReqVO vo) {
        PageResult<PrdtSpuPageReqVO> prdtSpuPage = prdtSpuService.getPrdtSpuPage(vo);
        return success(prdtSpuPage);
    }

    /**
     *   获取商品信息
     * @param spuId ID
     */
    @Override
    @InnerAuth
    public CommonResult<PrdtSpuGroupInVO> getPrdtSpuInfo(Long spuId) {
        return success(prdtSpuService.getPrdtSpu(spuId));
    }

    @Override
    @InnerAuth
    public CommonResult<Long> addPrdtSpu(PrdtSpuGroupSaveInVO createReqVO) {
        return success(prdtSpuService.insertPrdtSpu(createReqVO));
    }

    /**
     * 新增商品SPU
     *
     * @param createReqVO
     */
    @Override
    @InnerAuth
    public CommonResult<Long> addPrdtSpuOpen(PrdtSpuGroupSaveInVO createReqVO) {
        return success(prdtSpuService.insertPrdtSpuOpen(createReqVO));
    }

    @Override
    @InnerAuth
    public CommonResult<Boolean> editPrdtSpu(PrdtSpuGroupSaveInVO updateReqVO) {
        prdtSpuService.updatePrdtSpuAfter(
                prdtSpuService.updatePrdtSpu(updateReqVO)
        );
        return success(true);
    }

    /**
     * 修改商品SPU Open
     *
     * @param updateReqVO
     */
    @Override
    public CommonResult<Boolean> editPrdtSpuOpen(PrdtSpuGroupSaveInVO updateReqVO) {
        prdtSpuService.updatePrdtSpuOpen(updateReqVO);
        return success(true);
    }

    @Override
    public CommonResult<List<SkuUnitGroupDTO>> getSkuUnitGroupList(Long spuId, Long areaId, Long classId, String productType) {
        return success(prdtSpuService.getSkuUnitGroupList(spuId, areaId, classId, productType));
    }

    /**
     * 更据商品编号查询商品
     *
     * @param spuNo
     * @return
     */
    @Override
    public CommonResult<SpuDTO> getBySpuNo(Long spuNo) {
        PrdtSpu spu = prdtSpuService.getBySpuNo(spuNo);
        return success(HutoolBeanUtils.toBean(spu, SpuDTO.class));
    }

    /**
     * 修改商品的生产日期
     *
     * @param sysCode
     * @param opensourceId
     * @param spuductOpenDTO
     */
    @Override
    public CommonResult<Boolean> updateProduct(Long sysCode, Long opensourceId, SpuductOpenDTO spuductOpenDTO) {
        // 修改商品信息
        //查询商品是否存在
        // 参数校验：检查列表是否为空
        if (BeanUtil.isEmpty(spuductOpenDTO)) {
            throw exception(THE_PRODUCT_LIST_PARAMETER_CANNOT_BE_EMPTY);
        }
        // 获取当前商品的编号
        String spuNo = spuductOpenDTO.getSpuNo();
        if (StringUtils.isEmpty(spuNo)) {
            throw exception(THE_PRODUCT_ID_PARAMETER_CANNOT_BE_EMPTY);
        }
        return success(prdtSpuService.updatePrdData(spuductOpenDTO));
    }

    /**
     * 新增商品或者修改商品
     *
     * @param spuReceiveVO
     */
    @Override
    public CommonResult<Boolean> addOrUpdateSpu(Long sysCode, Long opensourceId, SpuReceiveVO spuReceiveVO) {
        List<PrdtSpuUpdateRespDTO> spuUpdateRespList = prdtSpuService.addOrUpdateSpu(spuReceiveVO);

        //更新SPU后续操作 在事务处理后统一处理
        spuUpdateRespList.forEach(prdtSpuUpdateRespDTO -> {
            //更新SPU后续操作
            prdtSpuService.updatePrdtSpuAfter(prdtSpuUpdateRespDTO);
        });


        return success(true);
    }

    @Override
    public CommonResult<List<SpuExportDTO>> getSpuExportList(PrdtSpuPageReqVO vo) {
        return success(prdtSpuService.getSpuExportList(vo));
    }

    @Override
    public CommonResult<Map<Long, SpuDTO>> listBySpuIds(List<Long> spuIds) {
        return success(prdtSpuService.listBySpuIds(spuIds));
    }

    @Override
    public CommonResult<Boolean> associationKeyword(String keyword, List<Long> spuIds, Integer associationType) {
        if (ToolUtil.isEmpty(spuIds)) {
            return success(true); // 如果 spuIds 为空，直接返回成功
        }
        for (Long spuId : spuIds) {
            PrdtSpu spu = prdtSpuService.getBySpuId(spuId);
            if (ToolUtil.isNotEmpty(spu)) {
                String keywords = spu.getKeywords();
                if (associationType == 0) {
                    if (ToolUtil.isEmpty(keywords) || !keywords.contains(keyword)) {
                        spu.setKeywords(ToolUtil.isEmpty(keywords) ? keyword : keywords + "," + keyword);
                    }
                } else {
                    if (ToolUtil.isNotEmpty(keywords) && keywords.contains(keyword)) {
                        keywords = keywords.replace(keyword, "").replace(",,", ",").replaceAll("^,|,$", "");
                        spu.setKeywords(ToolUtil.isEmpty(keywords) ? " " : keywords);
                    }
                }
                prdtSpuService.updatePrdtSpuAfter(
                        prdtSpuService.updateSpuBase(HutoolBeanUtils.toBean(spu, PrdtSpuSaveReqVO.class))
                );
            }
        }
        return success(true);
    }


    public CommonResult<String> importDataEvent(PrdSpuImportForm form){
        return CommonResult.success(JsonUtils.toJsonString(prdtSpuService.importBaseProductEvent(form.getList(), form.getDcId(), form.getSysCode(), form.getFileImportId(), form.getSeq())));
    }


}
