package com.zksr.product.controller.areaChannelPrice.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 城市渠道价格对象 prdt_area_channel_price
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Data
@ApiModel("城市渠道价格 - prdt_area_channel_price Response VO")
@AllArgsConstructor
@NoArgsConstructor
public class PrdtAreaChannelPriceRespVO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 渠道id */
    @Excel(name = "渠道id")
    @ApiModelProperty(value = "渠道id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long channelId;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @Excel(name = "价格码-数据字典", readConverterExp = "1=，2，3，4，5，6")
    @ApiModelProperty(value = "价格码-数据字典")
    private Long salePriceCode;

    public PrdtAreaChannelPriceRespVO(Long areaId, Long channelId) {
        this.areaId = areaId;
        this.channelId = channelId;
    }
}
