package com.zksr.product.controller.adjustPrices.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 商品调价单明细对象 prdt_adjust_prices_dtl
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Data
@ApiModel("商品调价单明细 - prdt_adjust_prices_dtl Response VO")
public class PrdtAdjustPricesDtlRespVO {
    private static final long serialVersionUID = 1L;

    /** 单据明细ID */
    @ApiModelProperty(value = "生效时间")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long adjustPricesDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** spuId */
    @Excel(name = "spuId")
    @ApiModelProperty(value = "spuId")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** skuId */
    @Excel(name = "skuId")
    @ApiModelProperty(value = "skuId")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 大单位-原标准价 */
    @Excel(name = "大单位-原标准价")
    @ApiModelProperty(value = "大单位-原标准价")
    private BigDecimal oldLargeMarkPrice;

    /** 大单位-原成本价(供货价) */
    @Excel(name = "大单位-原成本价(供货价)")
    @ApiModelProperty(value = "大单位-原成本价(供货价)")
    private BigDecimal oldLargeCostPrice;

    /** 中单位-原标准价 */
    @Excel(name = "中单位-原标准价")
    @ApiModelProperty(value = "中单位-原标准价")
    private BigDecimal oldMidMarkPrice;

    /** 中单位-原成本价(供货价) */
    @Excel(name = "中单位-原成本价(供货价)")
    @ApiModelProperty(value = "中单位-原成本价(供货价)")
    private BigDecimal oldMidCostPrice;

    /** 小单位-原标准价 */
    @Excel(name = "小单位-原标准价")
    @ApiModelProperty(value = "小单位-原标准价")
    private BigDecimal oldMinMarkPrice;

    /** 小单位-原成本价(供货价) */
    @Excel(name = "小单位-原成本价(供货价)")
    @ApiModelProperty(value = "小单位-原成本价(供货价)")
    private BigDecimal oldMinCostPrice;

    /** 大单位-新标准价 */
    @Excel(name = "大单位-新标准价")
    @ApiModelProperty(value = "大单位-新标准价")
    private BigDecimal newLargeMarkPrice;

    /** 大单位-新新成本价(供货价) */
    @Excel(name = "大单位-新新成本价(供货价)")
    @ApiModelProperty(value = "大单位-新新成本价(供货价)")
    private BigDecimal newLargeCostPrice;

    /** 中单位-标准价 */
    @Excel(name = "中单位-标准价")
    @ApiModelProperty(value = "中单位-标准价")
    private BigDecimal newMidMarkPrice;

    /** 中单位-新成本价(供货价) */
    @Excel(name = "中单位-新成本价(供货价)")
    @ApiModelProperty(value = "中单位-新成本价(供货价)")
    private BigDecimal newMidCostPrice;

    /** 小单位-新标准价 */
    @Excel(name = "小单位-新标准价")
    @ApiModelProperty(value = "小单位-新标准价")
    private BigDecimal newMinMarkPrice;

    /** 小单位-新成本价(供货价) */
    @Excel(name = "小单位-新成本价(供货价)")
    @ApiModelProperty(value = "小单位-新成本价(供货价)")
    private BigDecimal newMinCostPrice;

    /** 生效状态：0-未生效，1-生效中，2-已生效 */
    @Excel(name = "生效状态：0-未生效，1-生效中，2-已生效")
    @ApiModelProperty(value = "生效状态：0-未生效，1-生效中，2-已生效")
    private Long validState;

    /** 生效时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "生效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "生效时间")
    private Date validTime;

    @ApiModelProperty(value = "属性")
    private String properties;

    /** 商品名称 */
    @ApiModelProperty("商品名称")
    private String spuName;

    @ApiModelProperty(value = "商品编号")
    private String spuNo;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

}
