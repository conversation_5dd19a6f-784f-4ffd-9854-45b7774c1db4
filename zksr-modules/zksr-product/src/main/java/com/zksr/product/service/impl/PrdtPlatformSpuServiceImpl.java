package com.zksr.product.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson2.JSONArray;
import com.github.pagehelper.Page;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.platform.excel.ProductPlarformImportExcel;
import com.zksr.product.api.property.vo.PrdtPropertySpuGroupReqVO;
import com.zksr.product.api.propertyVal.vo.PrdtPropertyAndValVO;
import com.zksr.product.api.propertyVal.vo.PrdtPropertyValSpuGroupReqVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSkuRespVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSpuPageReqVO;
import com.zksr.product.controller.platform.vo.PrdtSpuGroupRespVO;
import com.zksr.product.controller.property.vo.PrdtPropertyRespVO;
import com.zksr.product.controller.propertyVal.vo.PrdtPropertyValRespVO;
import com.zksr.product.controller.sku.vo.PrdtSkuRespVO;
import com.zksr.product.controller.spu.vo.PrdtSpuShareReqVO;
import com.zksr.product.convert.platform.PrdtPlatformPropertyConvert;
import com.zksr.product.convert.platform.PrdtPlatformPropertyValConvert;
import com.zksr.product.convert.platform.PrdtPlatformSkuConvert;
import com.zksr.product.convert.platform.PrdtPlatformSpuConvert;
import com.zksr.product.domain.*;
import com.zksr.product.mapper.*;
import com.zksr.product.service.IPrdtPlatformSpuService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.STATUS_FAIL;
import static com.zksr.common.core.constant.StatusConstants.STATUS_SUCCESS;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.*;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_SPU_IMPORT_EMPTY;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_SPU_REPEAT_SHARE;

/**
 * 平台商品库-商品SPUService业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Service
public class PrdtPlatformSpuServiceImpl implements IPrdtPlatformSpuService {

    @Autowired
    private PrdtPlatformSpuMapper prdtPlatformSpuMapper;

    @Autowired
    private PrdtPlatformSkuMapper platformSkuMapper;

    @Autowired
    private PrdtSpuMapper prdtSpuMapper;

    @Autowired
    private PrdtSkuMapper prdtSkuMapper;

    @Autowired
    private PrdtCatgoryMapper catgoryMapper;

    @Autowired
    private ProductBrandMapper productBrandMapper;

    @Autowired
    private PrdtPropertyMapper prdtPropertyMapper;

    @Autowired
    private PrdtPropertyValMapper prdtPropertyValMapper;

    @Autowired
    private PrdtPlatformPropertyValMapper prdtPlatformPropertyValMapper;

    @Autowired
    private PrdtPlatformPropertyMapper prdtPlatformPropertyMapper;

    @Autowired
    private IProductCacheService productCacheService;

    /**
     * 获得平台商品库-商品SPU
     *
     * @param platformSpuId 商品SPU id
     * @return 平台商品库-商品SPU
     */
    @Override
    public PrdtPlatformSpu getPrdtPlatformSpu(Long platformSpuId) {
        return prdtPlatformSpuMapper.selectById(platformSpuId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtPlatformSpu> getPrdtPlatformSpuPage(PrdtPlatformSpuPageReqVO pageReqVO) {
        return prdtPlatformSpuMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<PrdtPlatformSpu> getPrdtPlatformSpuPageExt(PrdtPlatformSpuPageReqVO pageReqVO) {
        Page<PrdtPlatformSpu> startPage = PageUtils.startPage(pageReqVO);
        return PageResult.result(startPage, prdtPlatformSpuMapper.selectPageExt(pageReqVO));
    }

    @Override
    @Transactional
    public void updatePlatformSpu(Long spuId) {
        PrdtSpu prdtSpu = prdtSpuMapper.selectById(spuId);
        if (!prdtSpu.getShareFlag().equals(NumberPool.INT_ONE)) {
            return;
        }
        PrdtPlatformSpu platformSpu = prdtPlatformSpuMapper.selectBySpuId(spuId);
        // 数据拷贝
        if (Objects.isNull(platformSpu)) {
            platformSpu = PrdtPlatformSpuConvert.INSTANCE.convertPartnerSpu(prdtSpu);
        }
        // 设置品牌名称
        ProductBrand brand = productBrandMapper.selectById(prdtSpu.getBrandId());
        if (Objects.nonNull(brand)) {
            platformSpu.setBrandName(brand.getBrandName());
        }
        // 查询三级分类
        PrdtCatgory catgory3 = catgoryMapper.selectById(prdtSpu.getCatgoryId());
        if (Objects.nonNull(catgory3)) {
            PrdtCatgory catgory2 = catgoryMapper.selectById(catgory3.getPid());
            PrdtCatgory catgory1 = catgoryMapper.selectById(catgory2.getPid());
            platformSpu.setCatgoryName(StringUtils.format("{}>{}>{}", catgory1.getCatgoryName(), catgory2.getCatgoryName(), catgory3.getCatgoryName()));
        }
        // 查询入驻商名称
        SupplierDTO supplierDTO = productCacheService.getSupplierDTO(prdtSpu.getSupplierId());
        if (Objects.nonNull(supplierDTO)) {
            platformSpu.setSupplierName(supplierDTO.getSupplierName());
        }
        prdtPlatformSpuMapper.insertOrUpdate(platformSpu);

        // 移除所有的SKU, 属性
        platformSkuMapper.deleteByPlatformSpuId(platformSpu.getPlatformSpuId());
        prdtPlatformPropertyMapper.deleteByPlatformSpuId(platformSpu.getPlatformSpuId());
        prdtPlatformPropertyValMapper.deleteByPlatformSpuId(platformSpu.getPlatformSpuId());

        // 复制规格属性
        List<PrdtProperty> prdtProperties = prdtPropertyMapper.selectPropertyByList(
                PrdtPropertyRespVO.builder()
                        .spuId(spuId)
                        .isDelete(PRDT_IS_DELETE_0)
                        .build()
        );

        HashMap<PrdtProperty, PrdtPlatformProperty> propertyMap = new HashMap<>();
        HashMap<PrdtPropertyVal, PrdtPlatformPropertyVal> propertyValMap = new HashMap<>();
        for (PrdtProperty property : prdtProperties) {
            PrdtPlatformProperty platformProperty = PrdtPlatformPropertyConvert.INSTANCE.convertPartnerProperty(property);
            platformProperty.setSpuId(platformSpu.getPlatformSpuId());
            prdtPlatformPropertyMapper.insert(platformProperty);
            propertyMap.put(property, platformProperty);

            // 拷贝属性值
            List<PrdtPropertyVal> prdtPropertyValList = prdtPropertyValMapper.selectPropertyValByList(
                    PrdtPropertyValRespVO.builder()
                            .propertyId(property.getPropertyId())
                            .isDelete(PRDT_IS_DELETE_0)
                            .build()
            );
            for (PrdtPropertyVal propertyVal : prdtPropertyValList) {
                PrdtPlatformPropertyVal platformPropertyVal = PrdtPlatformPropertyValConvert.INSTANCE.convertPartnerPropertyVal(propertyVal);
                platformPropertyVal.setPropertyId(platformProperty.getPlatformPropertyId());
                platformPropertyVal.setSpuId(platformSpu.getPlatformSpuId());
                prdtPlatformPropertyValMapper.insert(platformPropertyVal);
                propertyValMap.put(propertyVal, platformPropertyVal);
            }
        }

        // 复制SKU数据
        List<PrdtSku> prdtSkus = prdtSkuMapper.selectPrdtSkuByList(
                PrdtSkuRespVO.builder()
                        .spuId(spuId)
                        .isDelete(PRDT_IS_DELETE_0)
                        .build()
        );
        for (PrdtSku prdtSku : prdtSkus) {
            String properties = prdtSku.getProperties();
            if (StringUtils.isNotEmpty(properties)) {
                for (PrdtProperty property : propertyMap.keySet()) {
                    properties = properties.replace(property.getPropertyId().toString(), propertyMap.get(property).getPlatformPropertyId().toString());
                }
                for (PrdtPropertyVal propertyVal : propertyValMap.keySet()) {
                    properties = properties.replace(propertyVal.getPropertyValId().toString(), propertyValMap.get(propertyVal).getPlatformPropertyValId().toString());
                }
            }
            PrdtPlatformSku platformSku = PrdtPlatformSkuConvert.INSTANCE.convertParterSku(prdtSku);
            platformSku.setPlatformSpuId(platformSpu.getPlatformSpuId());
            platformSku.setProperties(properties);
            platformSkuMapper.insert(platformSku);
        }

    }

    /**
     * 开启共享
     * @param updateReqVO
     */
    @Override
    @Transactional
    @DistributedLock(lockName = RedisLockConstants.LOCK_SPU_SHARE, tryLock = true)
    public void shareEnable(PrdtSpuShareReqVO updateReqVO) {
        // 获取spu列表
        List<PrdtSpu> spuList = prdtSpuMapper.selectBatchIds(updateReqVO.getSpuIdList());
        ArrayList<PrdtSpu> updateList = new ArrayList<>();
        for (PrdtSpu prdtSpu : spuList) {
            // 判断是否已经开启共享
            if (prdtSpu.getShareFlag().equals(NumberPool.INT_ONE)) {
                throw exception(PRDT_SPU_REPEAT_SHARE, prdtSpu.getSpuName());
            }
            PrdtSpu updateItem = PrdtSpu.builder()
                    .spuId(prdtSpu.getSpuId())
                    .shareFlag(NumberPool.INT_ONE)
                    .build();
            updateList.add(updateItem);
        }
        // 批量更新
        prdtSpuMapper.updateBatch(updateList);

        // 开始拷贝数据
        for (PrdtSpu prdtSpu : spuList) {
            // 更新或者新增sku数据共享绑定
            updatePlatformSpu(prdtSpu.getSpuId());
            // 更新状态
            PrdtPlatformSpu platformSpu = prdtPlatformSpuMapper.selectBySpuId(prdtSpu.getSpuId());
            platformSpu.setStatus((int) PRDT_STATUS_1);
            prdtPlatformSpuMapper.updateById(platformSpu);
        }

    }

    /**
     * 关闭共享
     * @param updateReqVO
     */
    @Override
    @Transactional
    @DistributedLock(lockName = RedisLockConstants.LOCK_SPU_SHARE, tryLock = true)
    public void shareDisable(PrdtSpuShareReqVO updateReqVO) {
        // 获取spu列表
        List<PrdtSpu> spuList = prdtSpuMapper.selectBatchIds(updateReqVO.getSpuIdList());
        ArrayList<PrdtSpu> updateList = new ArrayList<>();
        for (PrdtSpu prdtSpu : spuList) {
            // 判断是否已经开启过共享了
            if (prdtSpu.getShareFlag().equals(NumberPool.INT_ZERO)) {
                continue;
            }
            PrdtSpu updateItem = PrdtSpu.builder()
                    .spuId(prdtSpu.getSpuId())
                    .shareFlag(NumberPool.INT_ZERO)
                    .build();
            updateList.add(updateItem);
        }
        if (!updateList.isEmpty()) {
            // 批量更新
            prdtSpuMapper.updateBatch(updateList);
            for (PrdtSpu prdtSpu : updateList) {
                PrdtPlatformSpu platformSpu = prdtPlatformSpuMapper.selectBySpuId(prdtSpu.getSpuId());
                platformSpu.setStatus((int) PRDT_STATUS_0);
                prdtPlatformSpuMapper.updateById(platformSpu);
            }
        }
    }

    @Override
    public PrdtSpuGroupRespVO getPrdtPlatformSpuInfo(Long platformSpuId) {
        PrdtSpuGroupRespVO spuGroupInVO = new PrdtSpuGroupRespVO();
        //获取Spu信息
        PrdtPlatformSpu spu = prdtPlatformSpuMapper.selectById(platformSpuId);
        spuGroupInVO.setSpu(spu);
        //获取Sku信息
        List<PrdtPlatformSku> skus = platformSkuMapper.selectByPlatformSpuId(platformSpuId);
        // 转换数据
        List<PrdtPlatformSkuRespVO> skuVoList = PrdtPlatformSkuConvert.INSTANCE.convert(skus);
        //是否是规格商品
        AtomicBoolean isProperty = new AtomicBoolean(false);
        //组装规格数据
        skuVoList.stream().forEach(skuVo -> {
            if(Objects.nonNull(skuVo.getProperties())){
                skuVo.setPropertyAndValList(JSONArray.parseArray(skuVo.getProperties(), PrdtPropertyAndValVO.class));
                isProperty.set(true);
            }
        });
        spuGroupInVO.setSkuList(skuVoList);
        //多规格数据组装
        if(isProperty.get()){
            List<PrdtPropertySpuGroupReqVO> propertyVoList = new ArrayList<PrdtPropertySpuGroupReqVO>();
            //获取规格信息
            List<PrdtProperty> properties = new ArrayList<>();
            List<PrdtPlatformProperty> platformProperties = prdtPlatformPropertyMapper.selectBySpuId(platformSpuId);
            for (PrdtPlatformProperty property : platformProperties) {
                PrdtProperty prdtProperty = HutoolBeanUtils.toBean(property, PrdtProperty.class);
                prdtProperty.setPropertyId(property.getPlatformPropertyId());
                properties.add(prdtProperty);
            }
            propertyVoList.addAll(HutoolBeanUtils.toBean(properties,PrdtPropertySpuGroupReqVO.class));
            propertyVoList.forEach(propertyVo -> {
                //获取规格对应的规格值
                List<PrdtPropertyVal> propertyVals = new ArrayList<>();
                List<PrdtPlatformPropertyVal> prdtPlatformPropertyVals = prdtPlatformPropertyValMapper.selectByPropertyValId(propertyVo.getPropertyId());
                for (PrdtPlatformPropertyVal platformPropertyVal : prdtPlatformPropertyVals) {
                    PrdtPropertyVal prdtPropertyVal = HutoolBeanUtils.toBean(platformPropertyVal, PrdtPropertyVal.class);
                    prdtPropertyVal.setPropertyValId(platformPropertyVal.getPlatformPropertyValId());
                    propertyVals.add(prdtPropertyVal);
                }
                propertyVo.setValList(HutoolBeanUtils.toBean(propertyVals, PrdtPropertyValSpuGroupReqVO.class));
            });
            spuGroupInVO.setPropertyList(propertyVoList);
        }
        return spuGroupInVO;
    }

    @Override
    public void updateReferenced(Long platformSpuId) {
        if (Objects.isNull(platformSpuId)) {
            return;
        }
        Long count = prdtSpuMapper.selectCountByPSpuId(platformSpuId);
        if (Objects.nonNull(count)) {
            // 更新引用数据
            PrdtPlatformSpu update = PrdtPlatformSpu.builder()
                    .platformSpuId(platformSpuId)
                    .copyTimes(count.intValue())
                    .build();
            prdtPlatformSpuMapper.updateById(update);
        }
    }

    public String importPlatformProduct(List<ProductPlarformImportExcel> excelList) {
        return importPlatformProductEvent(excelList, SecurityUtils.getLoginUser().getSysCode(),null,0).getMsg();
    }

    @Override
    public FileImportHandlerVo importPlatformProductEvent(List<ProductPlarformImportExcel> excelList,Long sysCode,Long fileImportId,Integer seq) {
        FileImportHandlerVo fileImportHandlerVo = new FileImportHandlerVo();
        List<SysFileImportDtl> sysFileImportDtls = new ArrayList<>();
        int successNum = excelList.size();
        int failureNum = 0;
        int totalNum = excelList.size();
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        fileImportHandlerVo.setSuccessNum(successNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setTotalNum(totalNum);

        if (excelList.isEmpty()) {
            // 如果导入数据为空，则不进行数据导入
            fileImportHandlerVo.setFailureNum(excelList.size());
            fileImportHandlerVo.setTotalNum(excelList.size());
            fileImportHandlerVo.setMsg("导入数据为空");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
//            throw exception(PRDT_SPU_IMPORT_EMPTY);
        }

        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, SysDictData> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictLabel, item -> item));
        Map<PrdtPlatformSpu, List<PrdtPlatformSku>> productCache = new LinkedHashMap<>();
//        int successNum = excelList.size();
//        int failureNum = 0;
//        StringBuilder successMsg = new StringBuilder();
//        StringBuilder failureMsg = new StringBuilder();
        for (int line = 0; line < excelList.size(); line++) {
            //导入明细
            SysFileImportDtl sysFileImportDtl = new SysFileImportDtl();
            sysFileImportDtl.setSysCode(sysCode);
            sysFileImportDtl.setCreateBy(sysFileImportDtl.getCreateBy());
            sysFileImportDtl.setCreateTime(new Date());
            sysFileImportDtl.setFileImportId(fileImportId);

            if (failureMsg.length() > 2000) {
                break;
            }
            int cellNumber = line + 2+seq;
            ProductPlarformImportExcel itemData = excelList.get(line);
            sysFileImportDtl.setDtlJson(JsonUtils.toJsonString(itemData));

            if (StringUtils.isEmpty(itemData.getSpuNo())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>商品</span>不能为空。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据商品不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (StringUtils.isEmpty(itemData.getSpuName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>商品名称</span>不能为空。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据商品名称不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
//            if (StringUtils.isEmpty(itemData.getCategoryName())) {
//                failureNum++;
//                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>管理类别</span>不能为空。", cellNumber));
//                continue;
//            }

            if (StringUtils.isEmpty(itemData.getBarcode())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>条码</span>不能为空。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据条码不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            // 转换导入实体
            PrdtPlatformSpu prdtPlatformSpu = new PrdtPlatformSpu();
            {
                PrdtPlatformSpuConvert.INSTANCE.convertImport(prdtPlatformSpu, itemData);
                prdtPlatformSpu
                        .setIsDelete(NumberPool.INT_ZERO)
                        .setStatus(NumberPool.INT_ZERO)
                        .setIsSpecs(NumberPool.INT_ZERO)
                ;
                if (unitMap.containsKey(itemData.getMinUnitLabel())) {
                    prdtPlatformSpu.setMinUnit(Integer.parseInt(unitMap.get(itemData.getMinUnitLabel()).getDictValue()));
                }
                if (unitMap.containsKey(itemData.getMidUnitLabel())) {
                    prdtPlatformSpu.setMidUnit(Integer.parseInt(unitMap.get(itemData.getMidUnitLabel()).getDictValue()));
                }
                if (unitMap.containsKey(itemData.getLargeUnitLabel())) {
                    prdtPlatformSpu.setLargeUnit(Integer.parseInt(unitMap.get(itemData.getLargeUnitLabel()).getDictValue()));
                }

            }
            PrdtPlatformSku prdtPlatformSku = new PrdtPlatformSku();
            {
                PrdtPlatformSkuConvert.INSTANCE.convertImport(prdtPlatformSku, itemData);
                prdtPlatformSku.setIsDelete(NumberPool.INT_ZERO)
                        .setStatus(NumberPool.INT_ONE)
                ;

                // 截取条码长度
                if (ToolUtil.isNotEmpty(prdtPlatformSku.getBarcode()) && prdtPlatformSku.getBarcode().length() > 64) {
                    prdtPlatformSku.setBarcode(prdtPlatformSku.getBarcode().substring(0, 63));
                }
                if (ToolUtil.isNotEmpty(prdtPlatformSku.getMidBarcode()) && prdtPlatformSku.getMidBarcode().length() > 64) {
                    prdtPlatformSku.setMidBarcode(prdtPlatformSku.getMidBarcode().substring(0, 63));
                }
                if (ToolUtil.isNotEmpty(prdtPlatformSku.getLargeBarcode()) && prdtPlatformSku.getLargeBarcode().length() > 64) {
                    prdtPlatformSku.setLargeBarcode(prdtPlatformSku.getLargeBarcode().substring(0, 63));
                }
            }
            productCache.put(prdtPlatformSpu, ListUtil.toList(prdtPlatformSku));
            sysFileImportDtl.setStatus(STATUS_SUCCESS);
            sysFileImportDtls.add(sysFileImportDtl);
        }
        fileImportHandlerVo.setTotalNum(totalNum);
        fileImportHandlerVo.setSuccessNum(totalNum-failureNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setList(sysFileImportDtls);
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            fileImportHandlerVo.setMsg(failureMsg.toString());
            return fileImportHandlerVo;
//            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            // 保存数据
            for (PrdtPlatformSpu spu : productCache.keySet()) {
                List<PrdtPlatformSku> skuList = productCache.get(spu);
                prdtPlatformSpuMapper.insert(spu);
                skuList.forEach(item -> item.setPlatformSpuId(spu.getPlatformSpuId()));
                platformSkuMapper.insertBatch(skuList);
            }
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
            fileImportHandlerVo.setStatus(STATUS_SUCCESS);
            fileImportHandlerVo.setMsg(successMsg.toString());
        }
        return fileImportHandlerVo;
    }


    @Override
    public String importPlatformPicturesUrlInfo(List<ProductPlarformImportExcel> excelList) {
        if (excelList.isEmpty()) {
            // 如果导入数据为空，则不进行数据导入
            throw exception(PRDT_SPU_IMPORT_EMPTY);
        }

        AtomicInteger successNum = new AtomicInteger();
        StringBuilder successMsg = new StringBuilder();
        excelList.forEach(item -> {
            List<PrdtPlatformSpu> spuList = prdtPlatformSpuMapper.selectBySpuNo(item.getSpuNo());
            if (ToolUtil.isNotEmpty(spuList)) {
                spuList.forEach(spu -> {
                    if (ToolUtil.isNotEmpty(item.getPictureUrl())) {
                        spu.setThumb(item.getPictureUrl());
                    }
                    if (ToolUtil.isNotEmpty(item.getDetailImages())){
                        spu.setImages(item.getDetailImages());
                    }

                });
                prdtPlatformSpuMapper.updateBatch(spuList);
                successNum.getAndIncrement();
            }

        });
        successMsg.insert(0, "数据导入成功！共 " + successNum + " 条" + "源数据" + excelList.size() + " 条");
        return successMsg.toString();
    }
}
