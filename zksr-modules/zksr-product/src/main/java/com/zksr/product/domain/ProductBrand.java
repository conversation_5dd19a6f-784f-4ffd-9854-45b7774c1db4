package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;

/**
 * 平台品牌对象 prdt_brand
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
@TableName(value = "prdt_brand")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductBrand extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 平台商品牌id */
    @TableId(type = IdType.AUTO)
    private Long brandId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 运营商ID */
    @Excel(name = "运营商ID")
    private Long dcId;

    /** 品牌编号 */
    @Excel(name = "品牌编号")
    private String brandNo;

    /**  商品品牌名称 */
    @Excel(name = " 商品品牌名称")
    private String brandName;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 0 正常, 1 停用 */
    @Excel(name = "品牌状态, 0 正常, 1 停用")
    private String status;

    @Excel(name = "品牌商ID")
    private Long brandMerchantId;
}
