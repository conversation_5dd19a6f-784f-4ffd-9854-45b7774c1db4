package com.zksr.product.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.product.domain.PrdtBlockScheme;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSchemePageReqVO;
import org.apache.ibatis.annotations.Param;


/**
 * 经营屏蔽方案Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Mapper
public interface PrdtBlockSchemeMapper extends BaseMapperX<PrdtBlockScheme> {

    Page<PrdtBlockScheme> selectPage(@Param("page") Page<PrdtBlockSchemePageReqVO> page, @Param("pageReqVO") PrdtBlockSchemePageReqVO pageReqVO);

}
