package com.zksr.product.controller.yhdata.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 门店批量要货对象 prdt_branch_yhdata
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@ApiModel("门店批量要货 - 要货批次数据统计")
@Data
public class PrdtBranchYhdataGroupRespVO {

    @ApiModelProperty(value = "要货批次号")
    private String posYhBatchNo;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "要货单创建时间")
    private Date createTime;

    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "要货sku数")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long yhSkuNum;

    @ApiModelProperty(value = "满足SKU数")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long matchSkuNum;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店手机号")
    private String branchContractPhone;

    @ApiModelProperty(value = "要货批次日")
    private Long batchYmd;
}
