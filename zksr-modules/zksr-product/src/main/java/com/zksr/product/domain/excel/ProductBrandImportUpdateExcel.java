package com.zksr.product.domain.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 批量修改商品品牌
 * @date 2024/7/9
 */
@Data
@ApiModel(description = "批量修改商品品牌")
public class ProductBrandImportUpdateExcel {
    @Excel(name = "入驻商编号", headerColor = IndexedColors.RED)
    private Long supplierCode;

    @Excel(name = "商品编号", headerColor = IndexedColors.RED)
    private String spuNo;

    @Excel(name = "商品名称")
    private String spuName;

    @Excel(name = "商品品牌", headerColor = IndexedColors.RED)
    private String brandName;
}
