package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 规格名称对象 prdt_platform_property
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@TableName(value = "prdt_platform_property")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtPlatformProperty extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 规格名称id */
    @Excel(name = "规格名称id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableId(type = IdType.ASSIGN_ID)
    private Long platformPropertyId;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 规格名称 */
    @Excel(name = "规格名称")
    private String name;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    private Integer isDelete;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    private Integer status;

}
