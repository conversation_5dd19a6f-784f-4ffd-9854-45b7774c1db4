package com.zksr.product.cache.handler.impl;

import com.zksr.common.elasticsearch.domain.EsGlobalProduct;
import com.zksr.common.elasticsearch.domain.EsProduct;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.common.elasticsearch.util.EsProductConvertUtil;
import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.api.model.EsProductEventType;
import com.zksr.product.api.model.event.EsReleaseProductEvent;
import com.zksr.product.cache.handler.IAbstractProductEventHandler;
import com.zksr.product.domain.dto.EsProductLoadReqDTO;
import com.zksr.product.mapper.ProductDataMapper;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 全国商品发布事件
 * @date 2024/2/29 19:45
 */
@Service(EsProductEventType.GLOBAL_RELEASE)
public class GlobalReleaseProductHandler extends IAbstractProductEventHandler<EsReleaseProductEvent> {

    @Autowired
    private ProductDataMapper productDataMapper;

    @Autowired
    private EsProductService esProductService;

    @Override
    public List<EsGlobalProduct> execEvent(EsProductEvent<EsReleaseProductEvent> event) {
        List<EsGlobalProduct> esGlobalProducts = productDataMapper.selectByGlobal(EsProductLoadReqDTO.build(event.getData()));
        // 渲染数据
        this.render(esGlobalProducts);
        esProductService.saveGlobalFull(esGlobalProducts);
        return esGlobalProducts;
    }
}
