package com.zksr.product.controller.blockScheme.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.math.BigDecimal;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 经营屏蔽sku对象 prdt_block_sku
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Data
@ApiModel("经营屏蔽sku - prdt_block_sku Response VO")
public class PrdtBlockSkuRespVO {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty(value = "商品sku_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long blockSkuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 方案编码 */
    @Excel(name = "方案编码")
    @ApiModelProperty(value = "方案编码")
    private String schemeNo;

    /** 商品sku_id */
    @Excel(name = "商品sku_id")
    @ApiModelProperty(value = "商品sku_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 商品spu_id */
    @Excel(name = "商品spu_id")
    @ApiModelProperty(value = "商品spu_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** 商品名称 */
    @Excel(name = "商品名称")
    @ApiModelProperty(value = "商品名称")
    private String spuName;

    /** 条形码 */
    @Excel(name = "条形码")
    @ApiModelProperty(value = "条形码")
    private String barcode;

    /** 品牌名称 */
    @Excel(name = "品牌名称")
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    /** 规格 */
    @Excel(name = "规格")
    @ApiModelProperty(value = "规格")
    private String specName;

    /** 标准价 */
    @Excel(name = "标准价")
    @ApiModelProperty(value = "标准价")
    private BigDecimal markPrice;
}
