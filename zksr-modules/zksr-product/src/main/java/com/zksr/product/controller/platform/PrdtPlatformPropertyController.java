package com.zksr.product.controller.platform;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyPageReqVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyRespVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertySaveReqVO;
import com.zksr.product.convert.platform.PrdtPlatformPropertyConvert;
import com.zksr.product.domain.PrdtPlatformProperty;
import com.zksr.product.service.IPrdtPlatformPropertyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 规格名称Controller
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Api(tags = "管理后台 - 规格名称接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/platform/property")
public class PrdtPlatformPropertyController {

    @Autowired
    private IPrdtPlatformPropertyService prdtPlatformPropertyService;

    /**
     * 新增规格名称
     */
    @ApiOperation(value = "新增规格名称", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "规格名称", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtPlatformPropertySaveReqVO createReqVO) {
        return success(prdtPlatformPropertyService.insertPrdtPlatformProperty(createReqVO));
    }

    /**
     * 修改规格名称
     */
    @ApiOperation(value = "修改规格名称", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "规格名称", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtPlatformPropertySaveReqVO updateReqVO) {
            prdtPlatformPropertyService.updatePrdtPlatformProperty(updateReqVO);
        return success(true);
    }

    /**
     * 删除规格名称
     */
    @ApiOperation(value = "删除规格名称", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "规格名称", businessType = BusinessType.DELETE)
    @DeleteMapping("/{platformPropertyIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] platformPropertyIds) {
        prdtPlatformPropertyService.deletePrdtPlatformPropertyByPlatformPropertyIds(platformPropertyIds);
        return success(true);
    }

    /**
     * 获取规格名称详细信息
     */
    @ApiOperation(value = "获得规格名称详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{platformPropertyId}")
    public CommonResult<PrdtPlatformPropertyRespVO> getInfo(@PathVariable("platformPropertyId") Long platformPropertyId) {
        PrdtPlatformProperty prdtPlatformProperty = prdtPlatformPropertyService.getPrdtPlatformProperty(platformPropertyId);
        return success(PrdtPlatformPropertyConvert.INSTANCE.convert(prdtPlatformProperty));
    }

    /**
     * 分页查询规格名称
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得规格名称分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtPlatformPropertyRespVO>> getPage(@Valid PrdtPlatformPropertyPageReqVO pageReqVO) {
        PageResult<PrdtPlatformProperty> pageResult = prdtPlatformPropertyService.getPrdtPlatformPropertyPage(pageReqVO);
        return success(PrdtPlatformPropertyConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:platform-property:add";
        /** 编辑 */
        public static final String EDIT = "product:platform-property:edit";
        /** 删除 */
        public static final String DELETE = "product:platform-property:remove";
        /** 列表 */
        public static final String LIST = "product:platform-property:list";
        /** 查询 */
        public static final String GET = "product:platform-property:query";
        /** 停用 */
        public static final String DISABLE = "product:platform-property:disable";
        /** 启用 */
        public static final String ENABLE = "product:platform-property:enable";
    }
}
