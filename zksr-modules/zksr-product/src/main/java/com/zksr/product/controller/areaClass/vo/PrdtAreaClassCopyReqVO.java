package com.zksr.product.controller.areaClass.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;



/**
*
* @date 2024/11/13 16:56
* <AUTHOR>
*/
@Data
@ApiModel("城市展示分类复制功能请求入参实体")
public class PrdtAreaClassCopyReqVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 单个复制前 -- 城市展示分类id */
    @ApiModelProperty(value = "单个复制前 -- 城市展示分类id")
    private Long copyAreaClassId;

    /** 单个复制后 -- 目标城市展示分类上级id */
    @ApiModelProperty(value = "单个复制后 -- 目标城市展示分类上级id")
    private Long targetAreaClassPid;

    /** 单个复制后 -- 目标城市id */
    @ApiModelProperty(value = "单个复制后 -- 目标城市id")
    private Long areaId;

    /** 一键复制前 -- 城市id */
    @ApiModelProperty(value = "一键复制前 -- 城市id")
    private Long copyAreaId;

    /** 一键复制后 -- 目标城市id */
    @ApiModelProperty(value = "一键复制后 -- 目标城市id")
    private Long targetAreaId;


    /** 复制类型 */
    @ApiModelProperty(value = "复制类型 0 单个展示分类复制  1 一键城市分类复制")
    private Integer copyType;

}
