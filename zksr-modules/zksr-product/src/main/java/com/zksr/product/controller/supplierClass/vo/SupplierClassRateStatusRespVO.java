package com.zksr.product.controller.supplierClass.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.PartnerProfitModelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 入驻商, 一级管理分类销售分润占比, 以及是否开启分润占比
 * @date 2024/9/13 16:47
 */
@Data
@ApiModel(description = "入驻商, 一级管理分类销售分润占比, 以及是否开启分润占比")
@AllArgsConstructor
@NoArgsConstructor
public class SupplierClassRateStatusRespVO {

    /**
     * 销售分润占比, 最大0.29 = 29%
     */
    @Excel(name = "销售分润占比, 最大0.29 = 29%")
    @ApiModelProperty("销售分润占比, 最大0.29 = 29%")
    private BigDecimal saleTotalRate;

    /**
     * 参见 {@link PartnerProfitModelEnum}
     */
    @ApiModelProperty("利润模式: 0=(售价*比例=利润), 1=(售价-进货价=利润)")
    private String profitModel;
}
