package com.zksr.product.api.yhdata.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 要货批次创建结果
 * @date 2024/12/10 10:31
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CreateYhDataRespVO {

    @ApiModelProperty(value = "平台商CODE", notes = "每个平台商, 同时只能处理一个补货, 用于订单上锁")
    private Long sysCode;

    @ApiModelProperty("要货单批次号(系统唯一)")
    private String yhBatchNo;
}
