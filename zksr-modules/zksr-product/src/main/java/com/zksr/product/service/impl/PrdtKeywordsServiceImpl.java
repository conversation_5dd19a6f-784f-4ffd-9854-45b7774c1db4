package com.zksr.product.service.impl;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.controller.keywords.vo.PrdtKeywordsRespVO;
import com.zksr.product.mapper.PrdtSpuMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtKeywordsMapper;
import com.zksr.product.convert.keywords.PrdtKeywordsConvert;
import com.zksr.product.domain.PrdtKeywords;
import com.zksr.product.controller.keywords.vo.PrdtKeywordsPageReqVO;
import com.zksr.product.controller.keywords.vo.PrdtKeywordsSaveReqVO;
import com.zksr.product.service.IPrdtKeywordsService;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 搜索关键词词库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class PrdtKeywordsServiceImpl implements IPrdtKeywordsService {

    @Autowired
    private PrdtKeywordsMapper prdtKeywordsMapper;

    @Autowired
    private PrdtSpuMapper prdtSpuMapper;

    /**
     * 新增搜索关键词词库
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtKeywords(PrdtKeywordsSaveReqVO createReqVO) {
        // 插入
        PrdtKeywords prdtKeywords = PrdtKeywordsConvert.INSTANCE.convert(createReqVO);

        PrdtKeywords checkKeywords = prdtKeywordsMapper.selectOne(PrdtKeywords::getKeyword, prdtKeywords.getKeyword());
        //校验关键词在词库是否存在
        if (ToolUtil.isNotEmpty(checkKeywords)) {
            //不进行插入 直接返回
            return checkKeywords.getKeywordsId();
        }
        prdtKeywords.setStatus(1L);
        prdtKeywordsMapper.insert(prdtKeywords);
        // 返回
        return prdtKeywords.getKeywordsId();
    }

    @Override
    public void insertPrdtKeywordsBatch(List<String> keywords, Long sysCode) {
        // 去重处理
        Set<String> uniqueKeywords = new LinkedHashSet<>(keywords);

        // 校验关键词在词库是否存在，如果存在则移除出集合
        List<String> filteredKeywords = uniqueKeywords.stream()
                .filter(keyword -> ToolUtil.isEmpty(prdtKeywordsMapper.selectOne(
                        PrdtKeywords::getKeyword, keyword,
                        PrdtKeywords::getSysCode, sysCode,
                        PrdtKeywords::getDelFlag, 0)))
                .collect(Collectors.toList());

        // 整合成 PrdtKeywords 集合
        List<PrdtKeywords> prdtKeywordsList = filteredKeywords.stream()
                .map(keyword -> {
                    PrdtKeywords prdtKeywords = new PrdtKeywords();
                    prdtKeywords.setSysCode(sysCode);
                    prdtKeywords.setKeyword(keyword);
                    prdtKeywords.setCreateBy(SecurityUtils.getUsername());
                    return prdtKeywords;
                })
                .collect(Collectors.toList());

        prdtKeywordsMapper.insertBatch(prdtKeywordsList);
    }


    @Override
    public boolean searchPrdtKeywords(String keyword, Long sysCode) {
        return ToolUtil.isNotEmpty(prdtKeywordsMapper.selectOne(PrdtKeywords::getKeyword, keyword, PrdtKeywords::getSysCode, sysCode, PrdtKeywords::getDelFlag, 0));
    }


    /**
     * 修改搜索关键词词库
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtKeywords(PrdtKeywordsSaveReqVO updateReqVO) {
        prdtKeywordsMapper.updateById(PrdtKeywordsConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除搜索关键词词库
     *
     * @param keywordsId 关键词id
     */
    @Override
    public void deletePrdtKeywords(Long keywordsId) {
        // 删除
        prdtKeywordsMapper.deleteById(keywordsId);
    }

    /**
     * 批量删除搜索关键词词库
     *
     * @param keywordsIds 需要删除的搜索关键词词库主键
     * @return 结果
     */
    @Override
    public void deletePrdtKeywordsByKeywordsIds(Long[] keywordsIds) {
        for (Long keywordsId : keywordsIds) {
            PrdtKeywords prdtKeywords = prdtKeywordsMapper.selectById(keywordsId);
            if (ToolUtil.isNotEmpty(prdtKeywords)) {
                prdtKeywords.setDelFlag("2");
                prdtKeywordsMapper.updateById(prdtKeywords);
            }
        }
    }

    /**
     * 获得搜索关键词词库
     *
     * @param keywordsId 关键词id
     * @return 搜索关键词词库
     */
    @Override
    public PrdtKeywords getPrdtKeywords(Long keywordsId) {
        return prdtKeywordsMapper.selectById(keywordsId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrdtKeywordsRespVO> getPrdtKeywordsPage(PrdtKeywordsPageReqVO pageReqVO) {
        PageResult<PrdtKeywords> pageResult =prdtKeywordsMapper.selectPage(pageReqVO);
        PageResult<PrdtKeywordsRespVO> result =PrdtKeywordsConvert.INSTANCE.convertPage(pageResult);
        result.getList().stream().forEach(item -> {
            Long spuCount =prdtSpuMapper.selectCountByKeyword(item.getKeyword());
            item.setSpuCount(spuCount);
        });

        return result;
    }

//    private void validatePrdtKeywordsExists(Long keywordsId) {
//        if (prdtKeywordsMapper.selectById(keywordsId) == null) {
//            throw exception(PRDT_KEYWORDS_NOT_EXISTS);
//        }
//    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.product.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 搜索关键词词库 TODO 补充编号 ==========
    // ErrorCode PRDT_KEYWORDS_NOT_EXISTS = new ErrorCode(TODO 补充编号, "搜索关键词词库不存在");


}
