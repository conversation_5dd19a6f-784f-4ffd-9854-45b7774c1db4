package com.zksr.product.mq;

import com.zksr.common.core.domain.vo.openapi.receive.CreateYhDataReqVO;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.product.api.yhdata.vo.CreateYhDataRespVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.messaging.support.MessageBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-11-04
 * @description:  产品模块消息队列生产者
 */
@Slf4j
@Configuration
public class ProductMqProducer {
    @Autowired
    private StreamBridge streamBridge;

    /**
     * 发送商品调价单审核成功消息
     * 消费 {@linkplain ProductMqConsumer#productAdjustPricesApproveEvent()}
     * @param adjustPricesDtlId   商品调价单明细ID
     */
    public void sendAdjustPricesDtlApprove(Long adjustPricesDtlId){
        log.info("发送 商品调价单明细审核成功消息：{} ", adjustPricesDtlId);
        Map<String, Object> headers = new HashMap<>();
        headers.put(MessageConst.PROPERTY_DELAY_TIME_LEVEL, 2);
        Message<Object> msg = new GenericMessage<>(adjustPricesDtlId, headers);
        streamBridge.send(
                MessageConstant.PRODUCT_ADJUST_PRICES_APPROVE_TOPIC_OUT_PUT,
                msg);
    }

    /**
     * 异步匹配要货单数据
     * 消费 {@link  ProductMqConsumer#yhDataMatch()}
     * @param reqVO 要货批次单据数据
     */
    public void sendYhDataMatch(CreateYhDataReqVO reqVO) {
        log.info("发送异步匹配要货单批次：{} ", reqVO.getYhBatchNo());
        boolean flag = streamBridge.send(
                MessageConstant.YH_DATA_MATCH_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(reqVO)
                        .build());
    }
}
