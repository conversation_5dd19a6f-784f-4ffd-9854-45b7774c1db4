package com.zksr.product.controller.share;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtProductShare;
import com.zksr.product.service.IPrdtProductShareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.share.vo.PrdtProductSharePageReqVO;
import com.zksr.product.controller.share.vo.PrdtProductShareSaveReqVO;
import com.zksr.product.controller.share.vo.PrdtProductShareRespVO;
import com.zksr.product.convert.share.PrdtProductShareConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.ArrayList;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 商品分享Controller
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Api(tags = "管理后台 - 商品分享接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/share")
public class PrdtProductShareController {
    @Autowired
    private IPrdtProductShareService prdtProductShareService;

    /**
     * 新增商品分享
     */
    @ApiOperation(value = "新增商品分享", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "商品分享", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtProductShareSaveReqVO createReqVO) {
        return success(null);
    }

    /**
     * 修改商品分享
     */
    @ApiOperation(value = "修改商品分享", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "商品分享", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtProductShareSaveReqVO updateReqVO) {
            prdtProductShareService.updatePrdtProductShare(updateReqVO);
        return success(true);
    }

    /**
     * 删除商品分享
     */
    @ApiOperation(value = "删除商品分享", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "商品分享", businessType = BusinessType.DELETE)
    @DeleteMapping("/{shareProductIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] shareProductIds) {
        prdtProductShareService.deletePrdtProductShareByShareProductIds(shareProductIds);
        return success(true);
    }

    /**
     * 获取商品分享详细信息
     */
    @ApiOperation(value = "获得商品分享详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{shareProductId}")
    public CommonResult<PrdtProductShareRespVO> getInfo(@PathVariable("shareProductId") Long shareProductId) {
        PrdtProductShare prdtProductShare = prdtProductShareService.getPrdtProductShare(shareProductId);
        return success(PrdtProductShareConvert.INSTANCE.convert(prdtProductShare));
    }

    /**
     * 分页查询商品分享
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得商品分享分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtProductShareRespVO>> getPage(@Valid PrdtProductSharePageReqVO pageReqVO) {
        PageResult<PrdtProductShare> pageResult = prdtProductShareService.getPrdtProductSharePage(pageReqVO);
        return success(PrdtProductShareConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:share:add";
        /** 编辑 */
        public static final String EDIT = "product:share:edit";
        /** 删除 */
        public static final String DELETE = "product:share:remove";
        /** 列表 */
        public static final String LIST = "product:share:list";
        /** 查询 */
        public static final String GET = "product:share:query";
        /** 停用 */
        public static final String DISABLE = "product:share:disable";
        /** 启用 */
        public static final String ENABLE = "product:share:enable";
    }
}
