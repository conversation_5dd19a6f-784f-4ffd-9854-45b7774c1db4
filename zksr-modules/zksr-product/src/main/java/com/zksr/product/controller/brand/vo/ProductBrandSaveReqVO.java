package com.zksr.product.controller.brand.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台品牌对象 prdt_brand
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
@Data
@ApiModel("平台品牌 - prdt_brand分页 Request VO")
public class ProductBrandSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 平台商品牌id */
    @ApiModelProperty(value = "品牌ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long brandId;

    /** 品牌编号 */
    @Excel(name = "品牌编号")
    @ApiModelProperty(value = "品牌编号", required = true,  example = "娃哈哈")
    private String brandNo;

    /**  商品品牌名称 */
    @Excel(name = " 商品品牌名称")
    @ApiModelProperty(value = " 商品品牌名称", required = true,   example = "whh100123")
    private String brandName;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 0 正常, 1 停用 */
    @Excel(name = "0 正常, 1 停用")
    @ApiModelProperty(value = "品牌状态, 0 正常, 1 停用")
    private String status;


}
