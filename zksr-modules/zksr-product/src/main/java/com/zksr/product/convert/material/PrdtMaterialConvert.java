package com.zksr.product.convert.material;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtMaterial;
import com.zksr.product.controller.material.vo.PrdtMaterialRespVO;
import com.zksr.product.controller.material.vo.PrdtMaterialSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 素材 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-01-09
*/
@Mapper
public interface PrdtMaterialConvert {

    PrdtMaterialConvert INSTANCE = Mappers.getMapper(PrdtMaterialConvert.class);

    PrdtMaterialRespVO convert(PrdtMaterial prdtMaterial);

    PrdtMaterial convert(PrdtMaterialSaveReqVO prdtMaterialSaveReq);

    PageResult<PrdtMaterialRespVO> convertPage(PageResult<PrdtMaterial> prdtMaterialPage);
}