package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.domain.PrdtProperty;
import com.zksr.product.controller.property.vo.PrdtPropertyPageReqVO;
import com.zksr.product.controller.property.vo.PrdtPropertySaveReqVO;

/**
 * 规格名称Service接口
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
public interface IPrdtPropertyService {

    /**
     * 新增规格名称
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtProperty(@Valid PrdtPropertySaveReqVO createReqVO);

    /**
     * 修改规格名称
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtProperty(@Valid PrdtPropertySaveReqVO updateReqVO);

    /**
     * 删除规格名称
     *
     * @param propertyId 规格名称id
     */
    public void deletePrdtProperty(Long propertyId);

    /**
     * 批量删除规格名称
     *
     * @param propertyIds 需要删除的规格名称主键集合
     * @return 结果
     */
    public void deletePrdtPropertyByPropertyIds(Long[] propertyIds);

    /**
     * 获得规格名称
     *
     * @param propertyId 规格名称id
     * @return 规格名称
     */
    public PrdtProperty getPrdtProperty(Long propertyId);

    /**
     * 获得规格名称分页
     *
     * @param pageReqVO 分页查询
     * @return 规格名称分页
     */
    PageResult<PrdtProperty> getPrdtPropertyPage(PrdtPropertyPageReqVO pageReqVO);

}
