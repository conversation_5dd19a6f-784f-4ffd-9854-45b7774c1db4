package com.zksr.product.api.platForm;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaClass.form.AreaClassImportForm;
import com.zksr.product.api.platform.PlatFormApi;
import com.zksr.product.api.platform.form.PlatFormImportForm;
import com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo;
import com.zksr.product.domain.PrdtAreaClass;
import com.zksr.product.service.IPrdtAreaClassService;
import com.zksr.product.service.IPrdtPlatformSpuService;
import com.zksr.trade.api.driver.form.TrdDriverImportForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@ApiIgnore
public class PlatFormApiImpl implements PlatFormApi {

    @Autowired
    private IPrdtPlatformSpuService prdtPlatformSpuService;

    public CommonResult<String> importDataEvent(PlatFormImportForm form){
        return CommonResult.success(JsonUtils.toJsonString(prdtPlatformSpuService.importPlatformProductEvent(form.getList(), form.getSysCode(), form.getFileImportId(), form.getSeq())));
    }

}
