package com.zksr.product.mapper;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyPageReqVO;
import com.zksr.product.domain.PrdtPlatformProperty;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

import static com.zksr.product.constant.ProductConstant.PRDT_IS_DELETE_0;
import static com.zksr.product.constant.ProductConstant.PRDT_IS_DELETE_1;


/**
 * 规格名称Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Mapper
public interface PrdtPlatformPropertyMapper extends BaseMapperX<PrdtPlatformProperty> {
    default PageResult<PrdtPlatformProperty> selectPage(PrdtPlatformPropertyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtPlatformProperty>()
                    .eqIfPresent(PrdtPlatformProperty::getPlatformPropertyId, reqVO.getPlatformPropertyId())
                    .eqIfPresent(PrdtPlatformProperty::getSpuId, reqVO.getSpuId())
                    .eqIfPresent(PrdtPlatformProperty::getSupplierId, reqVO.getSupplierId())
                    .likeIfPresent(PrdtPlatformProperty::getName, reqVO.getName())
                    .eqIfPresent(PrdtPlatformProperty::getIsDelete, reqVO.getIsDelete())
                    .eqIfPresent(PrdtPlatformProperty::getMemo, reqVO.getMemo())
                    .eqIfPresent(PrdtPlatformProperty::getStatus, reqVO.getStatus())
                .orderByDesc(PrdtPlatformProperty::getPlatformPropertyId));
    }

    default void deleteByPlatformSpuId(Long platformSpuId) {
        update(
                PrdtPlatformProperty.builder().isDelete((int) PRDT_IS_DELETE_1).build(),
                new LambdaQueryWrapperX<PrdtPlatformProperty>()
                        .eq(PrdtPlatformProperty::getSpuId, platformSpuId)
        );
    }

    default List<PrdtPlatformProperty> selectBySpuId(Long platformSpuId) {
        return selectList(new LambdaQueryWrapperX<PrdtPlatformProperty>()
                .eqIfPresent(PrdtPlatformProperty::getSpuId, platformSpuId)
                .eqIfPresent(PrdtPlatformProperty::getIsDelete, PRDT_IS_DELETE_0)
        );
    }

}
