package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 规格值对象 prdt_property_val
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@TableName(value = "prdt_property_val")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtPropertyVal extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 规格值id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long propertyValId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 规格名称id */
    @Excel(name = "规格名称id")
    private Long propertyId;

    /** 规格值名称 */
    @Excel(name = "规格值名称")
    private String name;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    private Long isDelete;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    private Long status;

    /** 商品SPU_id */
    @Excel(name = "商品SPU_id")
    private Long spuId;

}
