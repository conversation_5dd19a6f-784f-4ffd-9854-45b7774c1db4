package com.zksr.product.controller.materialApply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.product.controller.material.vo.PrdtMaterialRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;


/**
 * 素材应用查看详情（回显）对象
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@ApiModel("素材应用查看详情（回显）对象 ")
public class PrdtMaterialApplyEchoRespVO {
    private static final long serialVersionUID = 1L;

    /** 素材信息 */
    @ApiModelProperty(value = "素材信息")
    private PrdtMaterialRespVO materialRespVO;

    /** 生效时间类型;1-按促销时间 2-按指定时间 */
    @Excel(name = "生效时间类型;1-按促销时间 2-按指定时间")
    @ApiModelProperty(value = "生效时间类型;1-按促销时间 2-按指定时间")
    private Integer timeType;

    /** 素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品 */
    @Excel(name = "素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品")
    @ApiModelProperty(value = "素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品")
    private Integer applyType;

    /** 生效时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "生效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "生效时间")
    private Date startTime;

    /** 失效时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "失效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "失效时间")
    private Date endTime;

    /** 详情信息 */
    @Getter
    @ApiModelProperty(value = "详情信息")
    private List<Object> detailList;

/*    @EqualsAndHashCode(callSuper = true)
    @ApiModel(value = "城市上下架商品信息")
    @Data
    public static class AreaItem extends PrdtAreaItemPageRespVO{
        *//** 素材应用id *//*
        @ApiModelProperty(value = "素材应用ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long materialApplyId;
    }

    @EqualsAndHashCode(callSuper = true)
    @ApiModel(value = "全国上下架商品信息")
    @Data
    public static class SupplierItem extends PrdtSupplierItemPageRespVO{
        *//** 素材应用id *//*
        @ApiModelProperty(value = "素材应用ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long materialApplyId;
    }

    @EqualsAndHashCode(callSuper = true)
    @ApiModel(value = "促销信息")
    @Data
    public static class Activity extends ActivityDTO{
        *//** 素材应用id *//*
        @ApiModelProperty(value = "素材应用ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long materialApplyId;
    }*/

}
