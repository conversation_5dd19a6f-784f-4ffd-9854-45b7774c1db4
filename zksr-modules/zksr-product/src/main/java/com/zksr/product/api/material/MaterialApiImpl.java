package com.zksr.product.api.material;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.api.material.vo.MaterialCacheVO;
import com.zksr.product.mapper.PrdtMaterialMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/1/11 9:12
 */
@RestController
@ApiIgnore
@InnerAuth
public class MaterialApiImpl implements MaterialApi{

    @Autowired
    private PrdtMaterialMapper prdtMaterialMapper;

    @Override
    public CommonResult<MaterialCacheVO> getMaterialCache(Integer applyType, Long applyId) {
        MaterialCacheVO cacheVO = new MaterialCacheVO();
        cacheVO.setMaterialList(prdtMaterialMapper.selectByApplyType(applyType, applyId));
        return success(cacheVO);
    }
}
