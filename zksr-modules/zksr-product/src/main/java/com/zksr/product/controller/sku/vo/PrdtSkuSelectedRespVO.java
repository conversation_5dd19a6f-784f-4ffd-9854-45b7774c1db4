package com.zksr.product.controller.sku.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("商品SKU - prdt_sku Response VO")
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSkuSelectedRespVO {
    private static final long serialVersionUID = 1L;

    /** 商品SKU_id */
    @ApiModelProperty(value = "skuId")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称", example = "示例值")
    private String spuName;

    /** 属性数组，JSON 格式 */
    @Excel(name = "属性数组，JSON 格式")
    @ApiModelProperty(value = "属性数组，JSON 格式")
    private String properties;
}
