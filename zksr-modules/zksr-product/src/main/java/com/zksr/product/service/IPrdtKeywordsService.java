package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.controller.keywords.vo.PrdtKeywordsRespVO;
import com.zksr.product.domain.PrdtKeywords;
import com.zksr.product.controller.keywords.vo.PrdtKeywordsPageReqVO;
import com.zksr.product.controller.keywords.vo.PrdtKeywordsSaveReqVO;

import java.util.List;

/**
 * 搜索关键词词库Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IPrdtKeywordsService {

    /**
     * 新增搜索关键词词库
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtKeywords(@Valid PrdtKeywordsSaveReqVO createReqVO);


    /**
     * 批量新增搜索关键词词库
     *
     * @param keywords 关键词集合,sysCode 平台商ID
     */
    public void insertPrdtKeywordsBatch(List<String> keywords, Long sysCode);

    /**
     * 根据关键词搜索关键词词库
     *
     * @param keyword 关键词,sysCode 平台商ID
     */
    public boolean searchPrdtKeywords(String keyword, Long sysCode);

    /**
     * 修改搜索关键词词库
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtKeywords(@Valid PrdtKeywordsSaveReqVO updateReqVO);

    /**
     * 删除搜索关键词词库
     *
     * @param keywordsId 关键词id
     */
    public void deletePrdtKeywords(Long keywordsId);

    /**
     * 批量删除搜索关键词词库
     *
     * @param keywordsIds 需要删除的搜索关键词词库主键集合
     * @return 结果
     */
    public void deletePrdtKeywordsByKeywordsIds(Long[] keywordsIds);

    /**
     * 获得搜索关键词词库
     *
     * @param keywordsId 关键词id
     * @return 搜索关键词词库
     */
    public PrdtKeywords getPrdtKeywords(Long keywordsId);

    /**
     * 获得搜索关键词词库分页
     *
     * @param pageReqVO 分页查询
     * @return 搜索关键词词库分页
     */
    PageResult<PrdtKeywordsRespVO> getPrdtKeywordsPage(PrdtKeywordsPageReqVO pageReqVO);

}
