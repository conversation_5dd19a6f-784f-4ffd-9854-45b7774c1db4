package com.zksr.product.controller.spu.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: SPU 商品修改数据
 * @date 2024/8/6 9:46
 */
@Data
@ApiModel(description = "SPU商品修改数据")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PrdtSpuUpdateRespDTO {

    @ApiModelProperty("spuId")
    private Long spuId;

    @ApiModelProperty("sku集合")
    private List<Long> skuIdList;
}
