package com.zksr.product.service;

import com.zksr.common.core.domain.vo.openapi.CreateYhDataMatchReqVO;
import com.zksr.common.core.domain.vo.openapi.CreateYhDataMatchRespVO;
import com.zksr.common.core.domain.vo.openapi.receive.CreateYhDataReqVO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.yhdata.vo.*;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataGroupRespVO;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataPageReqVO;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataRespVO;
import com.zksr.product.domain.PrdtBranchYhdata;

import java.util.List;
import java.util.Map;

/**
 * 门店批量要货Service接口
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
public interface IPrdtBranchYhdataService {

    /**
     * 保存要货单
     * @param reqVO 要货数据
     */
    CommonResult<CreateYhDataRespVO> saveYhData(CreateYhDataReqVO reqVO);

    /**
     * 要货批次, 上线商品匹配
     * @param createYhDataRespVO 要货批次保存结果
     */
    void matchYhBatch(CreateYhDataRespVO createYhDataRespVO);

    /**
     * 获取补货单匹配状态
     * @param reqVO 查询请求
     * @return  匹配结果
     */
    CommonResult<CreateYhDataMatchRespVO> getBatchYhRes(CreateYhDataMatchReqVO reqVO);

    /**
     * 获取批次补货单入驻商一级展示分类集合
     * @param reqVO 查询请求
     * @return  集合
     */
    Map<Long, List<Long>> getBatchSupplierSaleClass(YhBatchSupplierSaleClassReqVO reqVO);

    /**
     * 从ES里面查询要货单数据
     * @param reqVO 查询请求
     * @return  分页数据
     */
    PageResult<YhBatchItemVO> getEsBatchItemList(YhBatchListReqVO reqVO);

    /**
     * 门店操作补货单数据
     */
    CommonResult<Boolean> branchBatchYhSave(YhBatchSaveReqVO reqVO);

    /**
     * 获得门店批量要货分页
     */
    PageResult<PrdtBranchYhdataRespVO> getPrdtBranchYhdataPage(PrdtBranchYhdataPageReqVO pageReqVO);

    /**
     * 批次要货批次数据统计
     */
    PageResult<PrdtBranchYhdataGroupRespVO> batchTotalPageList(PrdtBranchYhdataPageReqVO pageReqVO);

    /**
     * 移除要货单
     */
    void removeBatchYh(YhBatchRemoveReqVO reqVO);

    /**
     * 初始化要货单
     */
    void initData();
}
