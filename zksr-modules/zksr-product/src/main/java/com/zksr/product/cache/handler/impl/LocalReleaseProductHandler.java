package com.zksr.product.cache.handler.impl;

import com.zksr.common.elasticsearch.domain.EsLocalProduct;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.common.elasticsearch.util.EsProductConvertUtil;
import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.api.model.EsProductEventType;
import com.zksr.product.api.model.event.EsReleaseProductEvent;
import com.zksr.product.cache.handler.IAbstractProductEventHandler;
import com.zksr.product.domain.dto.EsProductLoadReqDTO;
import com.zksr.product.mapper.ProductDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 本地商品发布事件
 * @date 2024/2/29 19:45
 */
@Slf4j
@Service(EsProductEventType.LOCAL_RELEASE)
public class LocalReleaseProductHandler extends IAbstractProductEventHandler<EsReleaseProductEvent> {

    @Autowired
    private ProductDataMapper productDataMapper;

    @Autowired
    private EsProductService esProductService;

    @Override
    public List<EsLocalProduct> execEvent(EsProductEvent<EsReleaseProductEvent> event) {
        List<EsLocalProduct> fullProducts = productDataMapper.selectByLocal(EsProductLoadReqDTO.build(event.getData()));
        // 渲染数据
        this.render(fullProducts);
        esProductService.saveLocalFull(fullProducts);
        return fullProducts;
    }
}
