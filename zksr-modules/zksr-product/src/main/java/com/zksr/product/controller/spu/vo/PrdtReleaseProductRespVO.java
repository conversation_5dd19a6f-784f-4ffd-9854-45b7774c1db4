package com.zksr.product.controller.spu.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/4 8:40
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PrdtReleaseProductRespVO {

    @ApiModelProperty(value = "local-本地, global-全国", required = true)
    private String productType = "local";

    /** 0-普通商品, 1-组合商品 */
    @ApiModelProperty(value = "0-普通商品, 1-组合商品")
    private Integer itemType;

    /** 城市上架商品id */
    @ApiModelProperty(value = "城市上架商品id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long itemId;

    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    @ApiModelProperty(value = "上下架状态")
    private Integer shelfStatus;

    @ApiModelProperty(value = "商品SPU id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    @ApiModelProperty(value = "商品sku id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty(value = "平台商管理分类id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long catgoryId;

    @ApiModelProperty(value = "平台商品牌id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long brandId;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "商品SPU编号")
    private String spuNo;

    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stock;

    @ApiModelProperty(value = "国际条码")
    private String barcode;

    @ApiModelProperty(value = "单位-数据字典")
    private Long unit;

    @ApiModelProperty(value = "属性数组，JSON 格式")
    private String properties;

    @ApiModelProperty(value = "标准价")
    private BigDecimal markPrice;

    @ApiModelProperty(value = "建议零售价")
    private BigDecimal suggestPrice;

    @ApiModelProperty(value = "保质期")
    private Integer expirationDate;		 // 保质期

    @ApiModelProperty(value = "参考进价")
    private BigDecimal referencePrice;

    @ApiModelProperty(value = "参考售价")
    private BigDecimal referenceSalePrice;

    @ApiModelProperty(value = "平台商管理分类名称")
    private String catgoryName;

    @ApiModelProperty(value = "平台商品牌名称")
    private String brandName;

    @ApiModelProperty(value = "三级展示分类名称")
    private String className;

    @ApiModelProperty(value = "二级展示分类名称")
    private String secondClassName;

    @ApiModelProperty(value = "一级展示分类名称")
    private String firstClassName;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "封面图")
    private String thumb;

    @ApiModelProperty(value = "商品规格")
    private String specName;

    @ApiModelProperty(value = "小单位-上下架状态")
    private Integer minShelfStatus;

    @ApiModelProperty(value = "中单位-上下架状态")
    private Integer midShelfStatus;

    @ApiModelProperty(value = "大单位-上下架状态")
    private Integer largeShelfStatus;

    @ApiModelProperty(value = "中单位-国际条码")
    private String midBarcode;

    @ApiModelProperty(value = "中单位-标准价")
    private BigDecimal midMarkPrice;

    @ApiModelProperty(value = "中单位-成本价(供货价)")
    private BigDecimal midCostPrice;

    @ApiModelProperty(value = "中单位-建议零售价")
    private BigDecimal midSuggestPrice;

    @ApiModelProperty(value = "中单位-起订")
    private BigDecimal midMinOq;

    @ApiModelProperty(value = "中单位-订货组数")
    private BigDecimal midJumpOq;

    @ApiModelProperty(value = "中单位-限购")
    private BigDecimal midMaxOq;

    @ApiModelProperty(value = "大单位-国际条码")
    private String largeBarcode;

    @ApiModelProperty(value = "大单位-标准价")
    private BigDecimal largeMarkPrice;

    @ApiModelProperty(value = "大单位-成本价(供货价)")
    private BigDecimal largeCostPrice;

    @ApiModelProperty(value = "大单位-建议零售价")
    private BigDecimal largeSuggestPrice;

    @ApiModelProperty(value = "大单位-起订")
    private BigDecimal largeMinOq;

    @ApiModelProperty(value = "大单位-订货组数")
    private BigDecimal largeJumpOq;

    @ApiModelProperty(value = "大单位-限购")
    private BigDecimal largeMaxOq;

    @ApiModelProperty(value = "最小单位-数据字典（sys_prdt_unit）")
    private Long minUnit;

    @ApiModelProperty(value = "中单位-数据字典（sys_prdt_unit）")
    private Long midUnit;

    @ApiModelProperty(value = "中单位换算数量（换算成最小单位）")
    private BigDecimal midSize;

    @ApiModelProperty(value = "大单位-数据字典（sys_prdt_unit）")
    private Long largeUnit;

    @ApiModelProperty(value = "大单位换算数量（换算成最小单位）")
    private BigDecimal largeSize;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "活动开始时间, 活动商品")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityStartTime;

    @ApiModelProperty(value = "活动结束时间, 活动商品")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityEndTime;

    @ApiModelProperty(value = "组合商品ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuCombineId;

    @ApiModelProperty(value = "活动ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long activityId;

    @ApiModelProperty(value = "二级管理分类名称")
    private String secondCatgoryName;

    @ApiModelProperty(value = "一级管理分类名称")
    private String firstCatgoryName;
}
