package com.zksr.product.controller.skuPrice;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import cn.hutool.core.collection.ListUtil;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.skuPrice.dto.PrdtSkuPriceInfoExportVO;
import com.zksr.product.api.skuPrice.excel.PrdtSkuPriceImportExportDTO;
import com.zksr.product.controller.skuPrice.vo.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtSkuPrice;
import com.zksr.product.service.IPrdtSkuPriceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * sku销售价Controller
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Api(tags = "管理后台 - sku销售价接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/price")
public class PrdtSkuPriceController {
    @Autowired
    private IPrdtSkuPriceService prdtSkuPriceService;

    /**
     * 新增sku销售价
     */
    @ApiOperation(value = "新增sku销售价", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "sku销售价", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Boolean> add(@Valid @RequestBody PrdtSkuPriceSaveInVo createReqVO) {
        List<PrdtSkuPriceSaveReqVO> saveList = prdtSkuPriceService.insertPrdtSkuPrice(createReqVO);

        //清除缓存
        prdtSkuPriceService.removeCache(saveList);

        return success(true);
    }

    /**
     * 修改sku销售价
     */
    @ApiOperation(value = "修改sku销售价", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "sku销售价", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtSkuPriceSaveReqVO updateReqVO) {
            prdtSkuPriceService.updatePrdtSkuPrice(updateReqVO);

        //清除缓存
        prdtSkuPriceService.removeCache(ListUtil.toList(updateReqVO));

        return success(true);
    }

    /**
     * 删除sku销售价
     */
    @ApiOperation(value = "删除sku销售价", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "sku销售价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{skuPriceIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] skuPriceIds) {
        prdtSkuPriceService.deletePrdtSkuPriceBySkuPriceIds(skuPriceIds);
        return success(true);
    }

    /**
     * 获取sku销售价详细信息
     */
    @ApiOperation(value = "获得sku销售价详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{skuPriceId}")
    public CommonResult<PrdtSkuPriceRespVO> getInfo(@PathVariable("skuPriceId") Long skuPriceId) {
        PrdtSkuPrice prdtSkuPrice = prdtSkuPriceService.getPrdtSkuPrice(skuPriceId);
        return success(HutoolBeanUtils.toBean(prdtSkuPrice, PrdtSkuPriceRespVO.class));
    }

    /**
     * 分页查询sku销售价
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得sku销售价分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtSkuPricePageReqVO>> getPage(@Valid PrdtSkuPricePageReqVO pageReqVO) {
        return success(prdtSkuPriceService.getPrdtSkuPricePage(pageReqVO));
    }

    /**
     * 分页查询sku销售价定价列表
     */
    @GetMapping("/listPricing")
    @ApiOperation(value = "分页查询sku销售价定价列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtSkuPricePageReqVO>> getlistPricing(@Valid PrdtSkuPricePageReqVO pageReqVO) {
        return success(prdtSkuPriceService.getPrdtSkuPricePageByPricing(pageReqVO));
    }

    /**
     * 批量城市价格方案
     */
    @ApiOperation(value = "批量删除城市价格方案", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "sku销售价", businessType = BusinessType.DELETE)
    @PostMapping("/deletedSkuPriceIds")
    public CommonResult<Boolean> batchDelete(@RequestBody List<Long> skuIds) {
        prdtSkuPriceService.batchDeleteBySkuPriceIds(skuIds);
        return success(true);
    }


    @ApiOperation(value = "导入城市价格", httpMethod = HttpMethod.POST)
    @Log(title = "导入城市价格", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public CommonResult<String> importData(MultipartFile file) throws Exception
    {
        ExcelUtil<PrdtSkuPriceInfoExportVO> util = new ExcelUtil<>(PrdtSkuPriceInfoExportVO.class);
        List<PrdtSkuPriceInfoExportVO> categoryList = util.importExcel(file.getInputStream(),1);

        String message =  prdtSkuPriceService.impordData(categoryList);
        //清除缓存
        return success(message);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "获取上架导入模版", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException {
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        if (ToolUtil.isNotEmpty(dcId)){
            ExcelUtil<PrdtSkuPriceInfoExportVO> util = new ExcelUtil<>(PrdtSkuPriceInfoExportVO.class);
            String instructions = "填表说明：\n" +
                    "导入新增：增价格编码不可填。编辑时请先导出价格编码修改后再导入。新增价格编码不可填，否则会导入失败。";
            util.importTemplateExcel(response, "上架信息导入", StringUtils.EMPTY, instructions);
        }
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:price:add";
        /** 编辑 */
        public static final String EDIT = "product:price:edit";
        /** 删除 */
        public static final String DELETE = "product:price:remove";
        /** 列表 */
        public static final String LIST = "product:price:list";
        /** 查询 */
        public static final String GET = "product:price:query";
        /** 停用 */
        public static final String DISABLE = "product:price:disable";
        /** 启用 */
        public static final String ENABLE = "product:price:enable";
    }
}
