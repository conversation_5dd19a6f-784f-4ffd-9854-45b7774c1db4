package com.zksr.product.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.domain.vo.openapi.IncreaseUpdateStockDTO;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.enums.request.RequestType;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.*;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.sku.dto.SkuPricesRespDTO;
import com.zksr.common.core.domain.vo.openapi.receive.PrdInventoryVO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateReqVO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateVO;
import com.zksr.product.api.sku.vo.SkuPricesPageReqVO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.controller.sku.vo.PrdtSkuPageReqVO;
import com.zksr.product.controller.sku.vo.PrdtSkuRespVO;
import com.zksr.product.controller.sku.vo.PrdtSkuSaveReqVO;
import com.zksr.product.controller.sku.vo.PrdtSkuSelectedRespVO;
import com.zksr.product.domain.*;
import com.zksr.product.mapper.PrdtCatgoryMapper;
import com.zksr.product.mapper.PrdtSkuMapper;
import com.zksr.product.mapper.PrdtSpuMapper;
import com.zksr.product.mapper.PrdtSupplierClassRateMapper;
import com.zksr.product.service.IPrdtSkuService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.openapi.AnntoErpApi;
import com.zksr.system.api.openapi.dto.*;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.OpenApiConstants.DEFAULT_ORG_CODE;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.PRDT_IS_DELETE_0;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_SKU_NOT_EXISTS;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 商品SKUService业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@RefreshScope
@Service
@Slf4j
public class PrdtSkuServiceImpl implements IPrdtSkuService {
    @Autowired
    private PrdtSkuMapper prdtSkuMapper;

    @Autowired
    private PrdtSpuMapper prdtSpuMapper;

    @Autowired
    private Cache<Long, SkuDTO> skuDTOCache;

    @Resource
    private RedisStockService redisStockService;

    @Autowired
    private PrdtCatgoryMapper catgoryMapper;

    @Autowired
    private PrdtSupplierClassRateMapper supplierClassRateMapper;

    @Autowired
    private IProductCacheService productCacheService;

    @Autowired
    private AnntoErpApi anntoErpApi;

    @Autowired
    @Lazy
    private IPrdtSkuService prdtSkuService;

    @Value("${api.erp.stock.query:/queryStock}")
    private String apiErpStockQuery;

    @Value("${flagResetOccupiedQty:false}")
    private Boolean flagResetOccupiedQty;

    /**
     * 新增商品SKU
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtSku(PrdtSkuSaveReqVO createReqVO) {
        // 插入
        PrdtSku prdtSku = HutoolBeanUtils.toBean(createReqVO, PrdtSku.class);
        prdtSkuMapper.insert(prdtSku);
        // 返回
        return prdtSku.getSkuId();
    }

    /**
     * 修改商品SKU
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtSku(PrdtSkuSaveReqVO updateReqVO) {
        prdtSkuMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrdtSku.class));
    }

    /**
     * 删除商品SKU
     *
     * @param skuId  商品sku_id
     * @param status
     */
    @Override
    public void deletePrdtSku(Long skuId, Long status) {
        // 删除
        PrdtSku prdtSku = prdtSkuMapper.selectById(skuId);
        prdtSku.setStatus(status);
        // 删除  停用商品信息
        prdtSkuMapper.updateById(prdtSku);

        skuDTOCache.remove(skuId);
    }

    /**
     * 批量删除商品SKU
     *
     * @param skuIds 需要删除的商品SKU主键
     * @param status
     * @return 结果
     */
    @Override
    public void deletePrdtSkuBySkuIds(Long[] skuIds, Long status) {
        for (Long skuId : skuIds) {
            this.deletePrdtSku(skuId, status);
        }
    }

    /**
     * 获得商品SKU
     *
     * @param skuId 商品sku_id
     * @return 商品SKU
     */
    @Override
    public PrdtSku getPrdtSku(Long skuId) {
        return prdtSkuMapper.selectById(skuId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrdtSku> getPrdtSkuPage(PrdtSkuPageReqVO pageReqVO) {
        return prdtSkuMapper.selectPage(pageReqVO);
    }

    @Override
    public PrdtSku getBySkuId(Long skuId) {
        return prdtSkuMapper.selectById(skuId);
    }

    @Override
    public Long getSkuStockBySkuId(Long skuId) {
        return prdtSkuMapper.selectPrdtSkuByStock(skuId).longValue();
    }

    private void validatePrdtSkuExists(Long skuId) {
        if (prdtSkuMapper.selectById(skuId) == null) {
            throw exception(PRDT_SKU_NOT_EXISTS);
        }
    }


    @Override
    public List<PrdtSkuSelectedRespVO> getSelectedPrdtSku(List<Long> skuIds) {
        List<PrdtSkuSelectedRespVO> skuSelectList = prdtSkuMapper.getSelectedPrdtSku(skuIds);
        if (ToolUtil.isNotEmpty(skuSelectList)) {
            List<PrdtSkuSelectedRespVO> skuResultList = skuSelectList.stream().map(x -> {
                //商品名称
                x.setSpuName(PropertyAndValDTO.getPropertiesSpuName(x.getProperties(), x.getSpuName()));
                return x;
            }).collect(Collectors.toList());

            return skuResultList;
        }
        return skuSelectList;
    }

    @Override
    public void updateSaleQty(List<Long> skuList) {
        if (Objects.isNull(skuList)) {
            return;
        }
        List<PrdtSku> updateList = new ArrayList<>();
        for (Long skuId : skuList) {
            // 已售库存
            BigDecimal saledQty = redisStockService.getSkuSaledQty(skuId);

            // 已同步库存
            BigDecimal skuSyncedQty = redisStockService.getSkuSyncedQty(skuId);

            PrdtSku prdtSku = PrdtSku.builder()
                    .skuId(skuId)
                    .saleQty(saledQty)
                    .syncedQty(skuSyncedQty)
                    .build();
            // 计算无库存时间
            this.calculateNoStockOnline(prdtSku);
            updateList.add(prdtSku);
        }
        if (!updateList.isEmpty()) {
            prdtSkuMapper.updateBatch(updateList);
        }
    }

    @Override
    public Date getLastUpdateTime(String sourceNo, Long supplierId) {
        return prdtSkuMapper.getLastUpdateTime(sourceNo, String.valueOf(supplierId));
    }
    
    /**
     for skuId in skuIdList:
         if sku 不存在:
            rate = 0
         elif sku 有自定义占比:
             rate = sku.自定义占比
         elif 供应商+一级分类有占比:
            rate = 供应商+一级分类占比
         elif 一级分类有占比:
             rate = 一级分类占比
         elif 平台有默认占比:
            rate = 平台默认占比
         else:
             rate = 0
     * @param reqVO
     * @return
     */
    @Override
    public PrdtSkuSaleTotalRateVO getSkuSaleTotalRate(PrdtSkuSaleTotalRateReqVO reqVO) {
        PrdtSkuSaleTotalRateVO rateVO = new PrdtSkuSaleTotalRateVO();
        if (Objects.isNull(reqVO.getSkuIdList()) || reqVO.getSkuIdList().isEmpty()) {
            return rateVO;
        }
        List<PrdtSku> list = prdtSkuMapper.selectSaleTotalRate(reqVO.getSkuIdList());
        Map<Long, PrdtSku> rateMap = list.stream().collect(Collectors.toMap(PrdtSku::getSkuId, item -> item));

        IProductCacheService cacheService = SpringUtils.getBean(IProductCacheService.class);

        // 查询平台基础利润模式
        PayConfigDTO payConfigDTO = cacheService.getPayConfigDTO(reqVO.getSysCode());
        if (Objects.isNull(payConfigDTO)) {
            for (Long skuId : reqVO.getSkuIdList()) {
                rateVO.getRateMap().put(skuId, new PrdtSkuSaleTotalRateVO.RateConfig(BigDecimal.ZERO));
            }
            return rateVO;
        }

        // spu集合
        List<SpuDTO> spuDTOList = rateMap.values()
                .stream()
                .map(PrdtSku::getSpuId)
                .filter(Objects::nonNull)
                .distinct()
                .map(cacheService::getSpuDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, SpuDTO> spuDTOMap = spuDTOList.stream().collect(Collectors.toMap(SpuDTO::getSpuId, item -> item));

        // 三级管理分类集合
        List<Long> threeCategoryList = spuDTOList
                .stream()
                .map(SpuDTO::getCatgoryId)
                .collect(Collectors.toList());

        // 获取入驻商集合
        List<Long> supplierList = spuDTOList
                .stream()
                .map(SpuDTO::getSupplierId)
                .collect(Collectors.toList());


        // 查询三级和一级的关系
        List<PrdtCatgory> catgories = new ArrayList<>();
        if (!threeCategoryList.isEmpty()) {
            catgories = catgoryMapper.selectThreeByFirst(threeCategoryList);
        }
        Map<Long, PrdtCatgory> threeCategoryMap = catgories.stream().collect(Collectors.toMap(PrdtCatgory::getCatgoryId, item -> item));

        // 查询一级管理分类, 入驻商销售占比
        List<Long> firstCategoryIdList = new ArrayList<>(threeCategoryMap.values())
                .stream()
                .map(PrdtCatgory::getPid)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<String, PrdtSupplierClassRate> supplierClassRateMap = new HashMap<>();
        if (!supplierList.isEmpty() && !firstCategoryIdList.isEmpty()) {
            List<PrdtSupplierClassRate> supplierClassRates = supplierClassRateMapper.selectBySupplierAndCategoryList(
                    supplierList,
                    firstCategoryIdList
            );
            supplierClassRateMap = supplierClassRates.stream().collect(Collectors.toMap(
                    item -> StringUtils.format("{}:{}", item.getSupplierId(), item.getCatgoryId()),
                    item -> item
            ));
        }

        for (Long skuId : reqVO.getSkuIdList()) {

            // sku都不存在就是没有比例
            PrdtSku sku = rateMap.get(skuId);
            if (Objects.isNull(sku)) {
                rateVO.getRateMap().put(skuId, new PrdtSkuSaleTotalRateVO.RateConfig(BigDecimal.ZERO));
                continue;
            }

            // 如果sku设置了参数, 那就使用sku的
            if (Objects.nonNull(sku.getSaleTotalRate())) {
                rateVO.getRateMap().put(skuId, new PrdtSkuSaleTotalRateVO.RateConfig(sku.getSaleTotalRate()));
                continue;
            }

            SpuDTO spuDTO = spuDTOMap.get(sku.getSpuId());
            // 验证规律分类
            if (Objects.nonNull(spuDTO.getCatgoryId())) {
                // 查看入驻商 + 一级管理分类
                if (ToolUtil.isEmpty(threeCategoryMap.get(spuDTO.getCatgoryId()))) {
                    throw new ServiceException("管理类别不存在");
                }
                Long firstCatgoryId = threeCategoryMap.get(spuDTO.getCatgoryId()).getPid();
                String supplierCategoryKey = StringUtils.format("{}:{}", spuDTO.getSupplierId(), firstCatgoryId);
                if (supplierClassRateMap.containsKey(supplierCategoryKey)) {
                    PrdtSupplierClassRate supplierClassRate = supplierClassRateMap.get(supplierCategoryKey);
                    if (Objects.nonNull(supplierClassRate.getSaleTotalRate())) {
                        rateVO.getRateMap().put(skuId, new PrdtSkuSaleTotalRateVO.RateConfig(supplierClassRate.getSaleTotalRate()));
                        continue;
                    }
                }

                // 查看一级管理分类设置的
                if (threeCategoryMap.containsKey(spuDTO.getCatgoryId())) {
                    PrdtCatgory catgory = threeCategoryMap.get(spuDTO.getCatgoryId());
                    if (Objects.nonNull(catgory.getSaleTotalRate())) {
                        rateVO.getRateMap().put(skuId, new PrdtSkuSaleTotalRateVO.RateConfig(catgory.getSaleTotalRate()));
                        continue;
                    }
                }
            }

            // 查询使用平台的
            if (StringUtils.isNotEmpty(payConfigDTO.getDefaultSaleTotalCategoryRate())) {
                rateVO.getRateMap().put(skuId, new PrdtSkuSaleTotalRateVO.RateConfig(new BigDecimal(payConfigDTO.getDefaultSaleTotalCategoryRate())));
            } else {
                rateVO.getRateMap().put(skuId, new PrdtSkuSaleTotalRateVO.RateConfig(BigDecimal.ZERO));
            }
        }

        return rateVO;
    }

    @Override
    public List<SkuPricesRespDTO> getSkuPricesList(SkuPricesPageReqVO reqVO) {
        List<SkuPricesRespDTO> respDTOList = prdtSkuMapper.getSkuPricesInfoList(reqVO);
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, SysDictData> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictValue, item -> item));
        SysDictData defaultUnit = new SysDictData();
        respDTOList.forEach(item -> {
            SupplierDTO supplierDTO = productCacheService.getSupplierDTO(item.getSupplierId());
            Long largeSize = ToolUtil.isEmptyReturn(item.getLargeSize(), NumberPool.LONG_ONE);
            Long midSize = ToolUtil.isEmptyReturn(item.getMidSize(), NumberPool.LONG_ONE);


            String large = "";
            if (ToolUtil.isNotEmpty(item.getLargeUnit())) {
                large = StringUtils.format("{}{} = ", NumberPool.LONG_ONE, unitMap.getOrDefault(item.getLargeUnit() + "", defaultUnit).getDictLabel());
            }
            String mid = "";
            if (ToolUtil.isNotEmpty(item.getMidUnit())) {
                mid = StringUtils.format("{}{} = ", Objects.equals(largeSize, NumberPool.LONG_ONE) ? midSize : largeSize / midSize, unitMap.getOrDefault(item.getMidUnit() + "", defaultUnit).getDictLabel());
            }
            String min = "";
            if (ToolUtil.isNotEmpty(item.getMinUnit())) {
                min = StringUtils.format("{}{}", largeSize, unitMap.getOrDefault(item.getMinUnit() + "", defaultUnit).getDictLabel());
            }

            item.setSupplierName(ToolUtil.isEmpty(supplierDTO) ? "" : supplierDTO.getSupplierName())
                    .setProperties(PropertyAndValDTO.getProperties(item.getProperties()))
                    .setUnitRelation(StringUtils.format("{}{}{}", large, mid, min))
            ;
        });
        return respDTOList;
    }

    @Override
    public PageResult<PrdtSkuRespVO> selectSkuPage(PrdtSkuPageReqVO pageReqVO) {
        Page<PrdtSkuPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<PrdtSkuRespVO> pageResult = prdtSkuMapper.selectSkuPage(page, pageReqVO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public Map<Long, SkuDTO> listBySkuIds(List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Maps.newHashMap();
        }
        List<PrdtSku> prdtSkus = prdtSkuMapper.selectList(PrdtSku::getSkuId, skuIds.stream().distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(prdtSkus)) {
            return Maps.newHashMap();
        }
        return prdtSkus.stream()
                .collect(Collectors.toMap(PrdtSku::getSkuId, prdtSku -> Objects.requireNonNull(HutoolBeanUtils.toBean(prdtSku, SkuDTO.class))));
    }

    @Override
    public List<Long> getRelationSpuCombineList(List<Long> skuIds) {
        if (ObjectUtil.isEmpty(skuIds)) {
            return new ArrayList<>();
        }
        return prdtSkuMapper.selectRelationSpuCombineList(skuIds);
    }

    @Override
    public Boolean editInventory(PrdInventoryVO prdInventoryVO) {
        try {
            // 根据 sourceNo 和 getSupplierId 查询 spu_id
            LambdaQueryWrapper<PrdtSpu> spuQueryWrapper = new LambdaQueryWrapper<>();
            spuQueryWrapper.eq(PrdtSpu::getSourceNo, prdInventoryVO.getSourceNo())
                    .eq(PrdtSpu::getSysCode, prdInventoryVO.getSysCode())
                    .eq(PrdtSpu::getSupplierId, prdInventoryVO.getSupplierId())
                    .eq(PrdtSpu::getIsDelete, PRDT_IS_DELETE_0)
                    .eq(PrdtSpu::getSource, prdInventoryVO.getSource());

            // 查询 spu_id
            PrdtSpu prdtSpu = prdtSpuMapper.selectOne(spuQueryWrapper);

            if (ToolUtil.isEmpty(prdtSpu)) {
                log.error("没有找到对应的 入驻商ID:{},商品编号:{}", prdInventoryVO.getSupplierId(), prdInventoryVO.getSourceNo());
                throw exception(NO_CORRESPONDING_SPU_FOUND);
            }
            // 根据 spu_id 查询 SKU
            LambdaQueryWrapper<PrdtSku> skuQueryWrapper = new LambdaQueryWrapper<>();
            skuQueryWrapper
                    .eq(PrdtSku::getSourceNo, prdInventoryVO.getSourceNo())
                    .eq(PrdtSku::getIsDelete, PRDT_IS_DELETE_0)
                    .eq(PrdtSku::getSpuId, prdtSpu.getSpuId());

            // 查询 SKU 信息
            PrdtSku prdtSku = prdtSkuMapper.selectOne(skuQueryWrapper);
            if (ToolUtil.isEmpty(prdtSku)) {
                throw exception(NO_SKUS_FOUND);
            }

            // 获取数据库中的最新库存更新时间
            Date lastUpdateDate = prdtSku.getLastUpdateTime();
            if (null == prdInventoryVO.getLastUpdateTime()) {
                prdInventoryVO.setLastUpdateTime(new Date());
            }
            // 如果 lastUpdateTime 为空，直接执行更新
            if (lastUpdateDate == null || prdInventoryVO.getLastUpdateTime().getTime() > lastUpdateDate.getTime()) {
                BigDecimal stock = prdInventoryVO.getStock();
                // 更新库存
                prdtSku.setStock(stock);
                prdtSku.setLastUpdateTime(prdInventoryVO.getLastUpdateTime());
                // 计算无库存时间
                this.calculateNoStockShelf(prdtSku);
                // 执行数据库更新操作
                prdtSkuMapper.updateById(prdtSku);
                // 刷新缓存
                redisStockService.setSkuStock(prdtSku.getSkuId(), stock);
                // 重置占用库存开关
                if (flagResetOccupiedQty) {
                    redisStockService.setSkuOccupiedQty(prdtSku.getSkuId(), BigDecimal.ZERO);
                }
                log.error("满足条件更新库存之前的外部编码:{},可用库存数量：{}, 已售库存数量：{}, 更新后总库存：{}, 和最后库存更新时间:{},获取数据库中最新更新时间:{}", prdInventoryVO.getSourceNo(), prdInventoryVO.getStock(), prdtSku.getSaleQty(), stock, prdInventoryVO.getLastUpdateTime(), lastUpdateDate);
                return true;
            } else {
                // 如果不满足更新时间条件，直接返回
                return false;
            }
        } catch (ServiceException e) {
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error("{}更新商品库存异常,", prdInventoryVO.getSourceNo(), e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Boolean getAreaIdExistSpu(Long areaId, Long sysCode) {
        return ToolUtil.isNotEmpty(prdtSkuMapper.getAreaIdExistSpu(areaId, sysCode)) ? Boolean.TRUE : Boolean.FALSE;
    }

    @Override
    public Boolean getExistShelfSku(Long spuId) {
        return ToolUtil.isNotEmpty(prdtSkuMapper.getExistShelfSku(spuId)) ? Boolean.TRUE : Boolean.FALSE;
    }

    @Override
    public PrdtSku getByBarcode(String barcode, String spuName, Long supplierNo) {
        return prdtSkuMapper.getByBarcode(barcode, spuName, supplierNo);

    }

    @Override
    public void calculateNoStock(PrdtSku prdtSku, BigDecimal stock) {
        // 库存隐藏逻辑, 如果只查询当前有库存, 或者无库存, 但是无库存时间在N天内的
        // 有库存 || (无库存 && 无库存时间 > N天)
        PrdtSku sku = prdtSkuMapper.selectById(prdtSku.getSkuId());
        if (Objects.nonNull(sku)) {
            SpuDTO spu = productCacheService.getSpuDTO(sku.getSpuId());
            if (Objects.nonNull(spu)) {
                // 获取当前SPU有那些单位
                List<UnitTypeEnum> unitTypes = spu.hasUnitList();
                for (UnitTypeEnum unitType : unitTypes) {
                    // 单位转换比例有效性验证, 必须大于0
                    BigDecimal unitSizeQty = spu.getUnitSizeQty(unitType.getType());
                    if (StockUtil.isGreater(unitSizeQty, BigDecimal.ZERO)) {
                        if (StockUtil.isGreater(
                                BigDecimal.ONE,
                                StockUtil.stockDivide(stock, unitSizeQty)
                        )) {
                            // 不足一份
                            // 如果没有记录无库存时间, 或者记录的无库存时间 > 当前时间
                            Date stockTime = sku.getNoStockTime(unitType);
                            if (Objects.isNull(stockTime) || stockTime.getTime() > System.currentTimeMillis()) {
                                // 记录无库存时间
                                prdtSku.setNoStockTime(new Date(), unitType);
                            }
                        } else {
                            // 大于一份
                            prdtSku.setNoStockTime(DateUtil.parseDate(DateUtils.MAX_DATE), unitType);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void calculateNoStockOnline(PrdtSku sku) {
        // 剩余库存
        BigDecimal stock = redisStockService.getSurplusSaleQtyBigDecimal(sku.getSkuId());
        this.calculateNoStock(sku, stock);
    }

    @Override
    public void calculateNoStockShelf(PrdtSku sku) {
        this.calculateNoStock(sku, sku.getStock());
    }

    /**
     * 增量更新库存
     *
     * @param dto dto
     */
    @Override
    public Boolean increaseUpdateStock(IncreaseUpdateStockDTO dto) {

        List<IncreaseUpdateStockDTO.Detail> details = dto.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            return true;
        }
        String source = details.stream().map(IncreaseUpdateStockDTO.Detail::getSource).findFirst().orElse(null);
        List<String> erpItemNos = details.stream().map(IncreaseUpdateStockDTO.Detail::getErpItemNo).collect(Collectors.toList());
        // 批量查询SPU
        List<PrdtSpu> prdtSpus = prdtSpuMapper.getBySupplierIdAndSourceNos(dto.getSupplierId(), erpItemNos, source);
        // 检查是否有缺失的SPU
        Map<String, Long> spuMap = prdtSpus.stream().collect(Collectors.toMap(PrdtSpu::getSourceNo, PrdtSpu::getSpuId, (v1, v2) -> v2));
        List<String> nonExistErpItemNos = erpItemNos.stream().filter(erpItemNo -> !spuMap.containsKey(erpItemNo)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(nonExistErpItemNos)) {
            log.error("没有找到对应的spu,sourceNo={}", String.join(",", nonExistErpItemNos));
            throw exception(NO_CORRESPONDING_SPU_FOUND);
        }

        List<PrdtSku> prdtSkus = Lists.newArrayListWithCapacity(details.size());

        Map<String, BigDecimal> updateStockMap = details.stream().collect(Collectors.toMap(IncreaseUpdateStockDTO.Detail::getErpItemNo, IncreaseUpdateStockDTO.Detail::getUpdateStock, BigDecimal::add));
        Map<Long, BigDecimal> doneMap = Maps.newConcurrentMap();
        try {
            updateStockMap.forEach((erpItemNo, updateStock) -> {
                PrdtSku prdtSku = prdtSkuMapper.getBySpuIdAndSourceNo(erpItemNo, spuMap.get(erpItemNo));
                if (ToolUtil.isEmpty(prdtSku)) {
                    log.error("没有找到对应的sku,sourceNo={}", erpItemNo);
                    throw exception(NO_SKUS_FOUND);
                }

                // 更新redis
                redisStockService.incrSkuStock(prdtSku.getSkuId(), updateStock);
                doneMap.put(prdtSku.getSkuId(), updateStock);

                prdtSku.setStock(prdtSku.getStock().add(updateStock));
                prdtSku.setLastUpdateTime(new Date());
                prdtSku.setUpdateTime(new Date());
                prdtSkus.add(prdtSku);
            });

            // 批量更新数据库
            prdtSkuService.updateBatchTransactional(prdtSkus);
            log.info("增量更新库存成功,requestId={}", dto.getRequestId());
            return true;
        } catch (Exception e) {
            log.error("增量更新库存异常,requestId={}", dto.getRequestId(), e);
            rollbackSkuStock(doneMap);
            throw e;
        } finally {
            log.info("增量更新库存结束,requestId={}", dto.getRequestId());
        }
    }

    @Transactional
    @Override
    public void updateBatchTransactional(List<PrdtSku> prdtSkus) {
        // 每次批量更新200条
        prdtSkuMapper.updateBatch(prdtSkus, 200);
    }

    /**
     * 回滚库存
     *
     * @param doneMap 已处理的skuId
     */
    private void rollbackSkuStock(Map<Long, BigDecimal> doneMap) {
        log.debug("rollbackSkuStock:doneMap:{}", JSON.toJSONString(doneMap));
        for (Map.Entry<Long, BigDecimal> e : doneMap.entrySet()) {
            redisStockService.incrSkuStock(e.getKey(), e.getValue().negate());
        }
        log.debug("rollbackSkuStock:done");
    }

    @Override
    public void syncSkuStock(List<Long> skuIds) {
        log.info("手动同步库存开始，skuIds:{}", skuIds);
        skuIds = skuIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<PrdtSku> skus = prdtSkuMapper.selectBatchIds(skuIds);
        if (CollectionUtils.isEmpty(skus)) {
            return;
        }
        // 只同步erp的，且去除erp商品编码为空的sku
        skus = skus.stream().filter(sku -> SyncSourceType.ANNTOERP.getType().equals(sku.getSource()))
                .filter(sku -> StringUtils.isNotBlank(sku.getSourceNo())).collect(Collectors.toList());

        // 获取商品入驻商
        List<Long> spuIds = skus.stream().map(PrdtSku::getSpuId).distinct().collect(Collectors.toList());
        Map<Long, Long> spuSupplierMap = prdtSpuMapper.selectBatchIds(spuIds).stream().collect(Collectors.toMap(PrdtSpu::getSpuId, PrdtSpu::getSupplierId, (v1, v2) -> v2));

        // 按照入驻商、erp商品编码进行分组
        Map<Long, Map<String, List<PrdtSku>>> map = skus.stream().collect(Collectors.groupingBy(sku -> spuSupplierMap.get(sku.getSpuId()), Collectors.groupingBy(PrdtSku::getSourceNo)));
        map.forEach((supplierId, skuMap) -> {
            List<String> itemNos = new ArrayList<>(skuMap.keySet());
            ErpStockQueryRequestDTO data = new ErpStockQueryRequestDTO()
                    .setOrgCode(this.getOrgCode(supplierId))
                    .setItemNos(itemNos);
            AnntoErpRequestDTO requestDTO = new AnntoErpRequestDTO()
                    .setApi(apiErpStockQuery)
                    .setData(data)
                    .setReqId(IdUtils.fastSimpleUUID())
                    .setSupplierId(supplierId)
                    .setRequestType(RequestType.B2B_STOCK_OCCUPY_QUERY)
                    .setB2bRequestType(B2BRequestType.B2B_STOCK_OCCUPY_QUERY)
                    .setOperationType(OperationType.OTHER);

            log.info("查询ERP库存，入参: {}", JSON.toJSONString(requestDTO));
            CommonResult<AnntoErpResultDTO<String>> commonResult = anntoErpApi.sendErp(requestDTO);
            log.info("查询ERP库存，结果: {}", JSON.toJSONString(commonResult));
            if (commonResult == null || commonResult.isError() || commonResult.getCheckedData() == null || !commonResult.getCheckedData().isOk()) {
                throw new ServiceException("同步失败，查询ERP库存失败");
            }
            String responseContent = commonResult.getCheckedData().getResponseContent();
            List<ErpStockQueryResultDTO> queryResultList = JSON.parseArray(responseContent, ErpStockQueryResultDTO.class);
            if (CollectionUtils.isEmpty(queryResultList)) {
                return;
            }
            Map<String, BigDecimal> itemStockMap = queryResultList.stream().collect(Collectors.toMap(ErpStockQueryResultDTO::getItemNo, ErpStockQueryResultDTO::getUsableSaleQty, (v1, v2) -> v2));
            itemStockMap.forEach((itemNo, usableSaleQty) -> {
                List<PrdtSku> updateSkuList = skuMap.get(itemNo);
                if (CollectionUtils.isEmpty(updateSkuList)) {
                    return;
                }
                updateSkuList.forEach(updateSku -> {
                    // 更新数据库，计算总库存，usableSaleQty = stock
                    BigDecimal stock = usableSaleQty;
                    prdtSkuMapper.update(null, new LambdaUpdateWrapper<PrdtSku>()
                            .set(PrdtSku::getStock, stock)
                            .set(PrdtSku::getLastUpdateTime, new Date())
                            .eq(PrdtSku::getSkuId, updateSku.getSkuId()));
                    // 更新redis
                    redisStockService.setSkuStock(updateSku.getSkuId(), stock);
                });
            });
        });

    }

    private String getOrgCode(Long supplierId) {
        // ERP默认组织编码为100
        String orgCode = DEFAULT_ORG_CODE;
        OpensourceDto opensourceDto = productCacheService.getOpensourceByMerchantId(supplierId);
        if (opensourceDto != null && opensourceDto.getSendCode() != null) {
            String[] group = opensourceDto.getSendCode().split(StringPool.COLON);
            if (group.length > 1 && StringUtils.isNotBlank(group[1])) {
                orgCode = group[1].trim();
            }
        }
        return orgCode;
    }
}
