package com.zksr.product.controller.property.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 规格名称对象 prdt_property
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@ApiModel("规格名称 - prdt_property分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtPropertyPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 规格名称id */
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long propertyId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 规格名称 */
    @Excel(name = "规格名称")
    @ApiModelProperty(value = "规格名称")
    private String name;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否")
    private Long isDelete;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)")
    private Long status;

    /** 商品SPU_id */
    @Excel(name = "商品SPU_id")
    @ApiModelProperty(value = "商品SPU_id",  example = "商品SPU_id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;


}
