package com.zksr.product.mapper;

import com.google.common.collect.Lists;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtBlockSku;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSkuPageReqVO;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 经营屏蔽skuMapper接口
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Mapper
public interface PrdtBlockSkuMapper extends BaseMapperX<PrdtBlockSku> {
    default PageResult<PrdtBlockSku> selectPage(PrdtBlockSkuPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtBlockSku>()
                    .eqIfPresent(PrdtBlockSku::getBlockSkuId, reqVO.getBlockSkuId())
                    .eqIfPresent(PrdtBlockSku::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtBlockSku::getSchemeNo, reqVO.getSchemeNo())
                    .eqIfPresent(PrdtBlockSku::getSkuId, reqVO.getSkuId())
                .orderByDesc(PrdtBlockSku::getBlockSkuId));
    }

    default List<Long> selectSkuIdsBySchemeNo(String schemeNo) {
        return StringUtils.isEmpty(schemeNo) ? Lists.newArrayList() : selectList(new LambdaQueryWrapperX<PrdtBlockSku>()
                .select(PrdtBlockSku::getSkuId)
                .eq(PrdtBlockSku::getSchemeNo, schemeNo))
                .stream().map(PrdtBlockSku::getSkuId).collect(Collectors.toList());
    }
}
