package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * sku销售价对象 prdt_sku_price
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@TableName(value = "prdt_sku_price")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSkuPrice extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** sku销售价id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long skuPriceId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 商品sku id;商品sku id */
    @Excel(name = "商品sku id;商品sku id")
    private Long skuId;

    /** 销售价1 */
    @Excel(name = "销售价1")
    private BigDecimal salePrice1;

    /** 销售价2 */
    @Excel(name = "销售价2")
    private BigDecimal salePrice2;

    /** 销售价3 */
    @Excel(name = "销售价3")
    private BigDecimal salePrice3;

    /** 销售价4 */
    @Excel(name = "销售价4")
    private BigDecimal salePrice4;

    /** 销售价5 */
    @Excel(name = "销售价5")
    private BigDecimal salePrice5;

    /** 销售价6 */
    @Excel(name = "销售价6")
    private BigDecimal salePrice6;

    /** 类型(用来区分是全国商品，还是本地配送商品，数据字典);0-全国商品 1-本地配送商品 */
    @Excel(name = "类型(用来区分是全国商品，还是本地配送商品，数据字典);0-全国商品 1-本地配送商品")
    private Integer type;

    /** 中单位-销售价1 */
    @Excel(name = "中单位-销售价1")
    private BigDecimal midSalePrice1;

    /** 中单位-销售价2 */
    @Excel(name = "中单位-销售价2")
    private BigDecimal midSalePrice2;

    /** 中单位-销售价3 */
    @Excel(name = "中单位-销售价3")
    private BigDecimal midSalePrice3;

    /** 中单位-销售价4 */
    @Excel(name = "中单位-销售价4")
    private BigDecimal midSalePrice4;

    /** 中单位-销售价5 */
    @Excel(name = "中单位-销售价5")
    private BigDecimal midSalePrice5;

    /** 中单位-销售价6 */
    @Excel(name = "中单位-销售价6")
    private BigDecimal midSalePrice6;

    /** 大单位-销售价1 */
    @Excel(name = "大单位-销售价1")
    private BigDecimal largeSalePrice1;

    /** 大单位-销售价2 */
    @Excel(name = "大单位-销售价2")
    private BigDecimal largeSalePrice2;

    /** 大单位-销售价3 */
    @Excel(name = "大单位-销售价3")
    private BigDecimal largeSalePrice3;

    /** 大单位-销售价4 */
    @Excel(name = "大单位-销售价4")
    private BigDecimal largeSalePrice4;

    /** 大单位-销售价5 */
    @Excel(name = "大单位-销售价5")
    private BigDecimal largeSalePrice5;

    /** 大单位-销售价6 */
    @Excel(name = "大单位-销售价6")
    private BigDecimal largeSalePrice6;

    /** 删除标识（0-正常,1-删除） */
    @TableLogic
    private Integer deleted;
}
