package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.product.domain.PrdtSupplierItem;
import com.zksr.product.domain.PrdtSupplierItemZip;
import org.apache.ibatis.annotations.Mapper;

/**
 * 全国上架商品拉链表 Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Mapper
@SuppressWarnings("all")
public interface PrdtSupplierItemZipMapper extends BaseMapperX<PrdtSupplierItemZip> {
    PrdtSupplierItemZip selectPrdtSupplierItemZip(PrdtSupplierItem prdtSupplierItem);

    void updatePrdtSupplierItemZip(PrdtSupplierItemZip currentRelation);
}
