package com.zksr.product.cache.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.elasticsearch.domain.EsLocalProduct;
import com.zksr.common.elasticsearch.domain.EsProduct;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.api.content.dto.ReleaseItemChangeEventDTO;
import com.zksr.product.convert.cache.EsCacheConvert;
import com.zksr.product.mapper.ProductDataMapper;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/2/29 19:33
 */
@Slf4j
@SuppressWarnings("all")
public abstract class IAbstractProductEventHandler<T> implements IProductEventHandler<T> {

    @Autowired
    protected IProductCacheService productCacheService;

    @Autowired
    protected ProductDataMapper productDataMapper;

    @Autowired
    protected EsProductService esProductService;

    @Autowired
    protected RedisService redisService;

    @Autowired
    protected RedisStockService redisStockService;

    public void render(List<? extends EsProduct> list) {
        // 加载入驻商名称
        Map<Long, String> supplierMap = list.stream()
                .map(EsProduct::getSupplierId)
                .filter(Objects::nonNull)
                .distinct()
                .map(productCacheService::getSupplierDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(SupplierDTO::getSupplierId, SupplierDTO::getSupplierName));
        list.forEach(item -> item.setSupplierName(supplierMap.get(item.getSupplierId())));
        for (EsProduct product : list) {
            product.setStock(redisStockService.getSurplusSaleQtyBigDecimal(product.getSkuId()));
            product.setSaleQty(redisStockService.getSkuSaledQty(product.getSkuId()).longValue());
        }
    }

    @Override
    public void processData(EsProductEvent<T> event) {
        Class<T> tClass = getClassType();
        if (event.getData() instanceof JSONObject) {
            JSONObject object = (JSONObject) event.getData();
            event.setData(JSON.parseObject(object.toJSONString(), tClass));
        }
        // 清除上下文中的 sysCode 信息
        log.info("执行刷新 sysCode={}", SecurityContextHolder.getSysCode());
        SecurityContextHolder.remove();
        processAfter(execEvent(event));
    }

    /**
     * 刷新后事件
     */
    public void processAfter(List<? extends EsProduct> list) {
        if (list.isEmpty()) {
            return;
        }
        // 城市上架数据变更监听
        Set<ReleaseItemChangeEventDTO> cityIdList = list.stream().map(EsCacheConvert.INSTANCE::convertReleaseItemChangeEvent).filter(Objects::nonNull).collect(Collectors.toSet());
        redisService.setCacheSet(RedisConstants.RELEASE_CITY_CHANGE_SET, cityIdList);
    }

    /**
     * 子类实现具体发送数据到ES处理
     * @param event
     */
    public abstract List<? extends EsProduct> execEvent(EsProductEvent<T> event);

    public Class<T> getClassType() {
        // 获取当前类的 Class 对象
        Class<?> clazz = this.getClass();
        // 获取泛型超类
        Type genericSuperclass = clazz.getGenericSuperclass();
        // 将泛型超类转换为 ParameterizedType
        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            // 从 ParameterizedType 中获取实际类型参数
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length > 0) {
                return (Class<T>) actualTypeArguments[0];
            }
        }
        return null;
    }
}
