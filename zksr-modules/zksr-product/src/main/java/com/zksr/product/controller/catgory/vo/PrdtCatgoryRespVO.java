package com.zksr.product.controller.catgory.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 平台商管理分类对象 prdt_catgory
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Data
@ApiModel("平台商管理分类 - prdt_catgory Response VO")
public class PrdtCatgoryRespVO {
    private static final long serialVersionUID = 1L;

    /** 平台商管理分类id */
    @ApiModelProperty(value = "平台商管理分类id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long catgoryId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 父id */
    @Excel(name = "父id")
    @ApiModelProperty(value = "父id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pid;

    /** 分类名 */
    @Excel(name = "分类名")
    @ApiModelProperty(value = "分类名", example = "示例值")
    private String catgoryName;

    /** 分类图标 */
    @Excel(name = "分类图标")
    @ApiModelProperty(value = "分类图标", example = "示例值")
    private String icon;

    /** 排序 */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sort;

    /** 状态 0-停用 1-启用 */
    @Excel(name = "状态 0-停用 1-启用")
    @ApiModelProperty(value = "状态 0-停用 1-启用", example = "示例值")
    private String status;

    /** 平台商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "平台商分润比例", readConverterExp = "只=一级分类设定")
    @ApiModelProperty(value = "平台商分润比例", example = "示例值")
    private BigDecimal partnerRate;

    /** 运营商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "运营商分润比例", readConverterExp = "只=一级分类设定")
    @ApiModelProperty(value = "运营商分润比例", example = "示例值")
    private BigDecimal dcRate;

    /** 业务员负责人分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "业务员负责人分润比例", readConverterExp = "只=一级分类设定")
    @ApiModelProperty(value = "业务员负责人分润比例", example = "示例值")
    private BigDecimal colonel1Rate;

    /** 业务员分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "业务员分润比例", readConverterExp = "只=一级分类设定")
    @ApiModelProperty(value = "业务员分润比例", example = "示例值")
    private BigDecimal colonel2Rate;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "示例值")
    private String memo;

    /** 创建时间 */
    @Excel(name = "创建时间")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 级别 */
    @Excel(name = "级别")
    @ApiModelProperty(value = "级别")
    private Integer level;

    /**
     * 是否绑定了商品 true 绑定商品
     */
    @TableField(exist = false)
    private Boolean isDisabled;

    /**
     * 销售占比利润, 百分比,最高29% = 0.29, 只有一级可以设置
     */
    @ApiModelProperty(value = "销售占比利润, 百分比,最高29% = 0.29, 只有一级可以设置")
    private BigDecimal saleTotalRate;
}
