package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 商品分享对象 prdt_product_share
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@TableName(value = "prdt_product_share")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtProductShare extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** ID */
    @TableId
    private Long shareProductId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 商品上架ID */
    @Excel(name = "商品上架ID")
    private Long itemId;

    /** 商品单位 */
    @Excel(name = "商品单位")
    private Long unitSize;

    /** 发起分享IP */
    @Excel(name = "发起分享IP")
    private String remoteIp;

    /** 有效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "有效时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expirationTime;

    /** 分享key */
    @Excel(name = "分享key")
    private String shareKey;

}
