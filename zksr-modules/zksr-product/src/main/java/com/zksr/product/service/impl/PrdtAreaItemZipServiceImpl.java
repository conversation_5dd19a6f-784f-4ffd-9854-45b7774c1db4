package com.zksr.product.service.impl;

import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.product.domain.PrdtAreaItem;
import com.zksr.product.domain.PrdtAreaItemZip;
import com.zksr.product.mapper.PrdtAreaItemMapper;
import com.zksr.product.mapper.PrdtAreaItemZipMapper;
import com.zksr.product.service.IPrdtAreaItemZipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.Objects;

/**
 * 城市上架商品拉链表 Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Service
public class PrdtAreaItemZipServiceImpl implements IPrdtAreaItemZipService {
    @Autowired
    private PrdtAreaItemZipMapper prdtAreaItemZipMapper;

    @Autowired
    private PrdtAreaItemMapper prdtAreaItemMapper;

    @Override
    public void insertPrdtAreaItemZip(PrdtAreaItem areaItem) {
         //获取时间
        Date nowDate = DateUtils.getNowDate();
        Date endOfYearDate = DateUtils.toDate(LocalDateTime.of(2099, 12, 30, 23, 59, 59));
        PrdtAreaItemZip currentRelation = prdtAreaItemZipMapper.selectPrdtAreaItemZip(areaItem);
        if(ToolUtil.isEmpty(currentRelation)){
            PrdtAreaItemZip newRelation = new PrdtAreaItemZip();
            newRelation.setSysCode(areaItem.getSysCode());
            newRelation.setSupplierId(areaItem.getSupplierId());
            newRelation.setAreaId(areaItem.getAreaId());
            newRelation.setSpuId(areaItem.getSpuId());
            newRelation.setSkuId(areaItem.getSkuId());
            newRelation.setStartDate(nowDate);
            newRelation.setEndDate(endOfYearDate);
            prdtAreaItemZipMapper.insert(newRelation);
        }else{
            Long aLong = prdtAreaItemMapper.selectSpuSkuIsOnShelf(areaItem);
            if(aLong == 0){
                 // 更新当前关联关系的结束时间
                currentRelation.setEndDate(nowDate);
                prdtAreaItemZipMapper.updatePrdtAreaItemZip(currentRelation);

                PrdtAreaItemZip newPrdtAreaItemZip = HutoolBeanUtils.toBean(currentRelation, PrdtAreaItemZip.class);
                newPrdtAreaItemZip.setAreaItemZipId(null);
                newPrdtAreaItemZip.setCreateTime(nowDate);
                newPrdtAreaItemZip.setStartDate(nowDate);
                newPrdtAreaItemZip.setEndDate(endOfYearDate);
                prdtAreaItemZipMapper.insert(newPrdtAreaItemZip);
            }
        }

    }
}
