package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.product.domain.PrdtAreaItem;
import com.zksr.product.domain.PrdtAreaItemZip;

import org.apache.ibatis.annotations.Mapper;

import static com.zksr.product.constant.ProductConstant.PRDT_SHELF_STATUS_1;

/**
 * 全国上架商品拉链表 Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Mapper
@SuppressWarnings("all")
public interface PrdtAreaItemZipMapper extends BaseMapperX<PrdtAreaItemZip> {

     default PrdtAreaItemZip selectPrdtAreaItemZip(PrdtAreaItem areaItem){
        LambdaQueryWrapper<PrdtAreaItemZip> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PrdtAreaItemZip::getSupplierId, areaItem.getSupplierId())
                    .eq(PrdtAreaItemZip::getSpuId,areaItem.getSpuId())
                    .eq(PrdtAreaItemZip::getSkuId, areaItem.getSkuId())
                    .eq(PrdtAreaItemZip::getAreaId, areaItem.getAreaId())
                    .orderByDesc(PrdtAreaItemZip::getStartDate)
                    .last("LIMIT 1");
        return selectOne(queryWrapper);
    }

    void updatePrdtAreaItemZip(PrdtAreaItemZip relation);
}
