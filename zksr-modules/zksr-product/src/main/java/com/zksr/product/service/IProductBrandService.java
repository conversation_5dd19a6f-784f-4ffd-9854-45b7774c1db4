package com.zksr.product.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.brand.excel.BrandImportExcel;
import com.zksr.product.api.brand.vo.BindBrandMerchantReqVO;
import com.zksr.product.controller.brand.vo.ProductBrandPageReqVO;
import com.zksr.product.controller.brand.vo.ProductBrandRespVO;
import com.zksr.product.controller.brand.vo.ProductBrandSaveReqVO;
import com.zksr.product.domain.ProductBrand;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 平台品牌Service接口
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
public interface IProductBrandService {

    /**
     * 新增平台品牌
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertProductBrand(@Valid ProductBrandSaveReqVO createReqVO);

    /**
     * 修改平台品牌
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateProductBrand(@Valid ProductBrandSaveReqVO updateReqVO);

    /**
     * 删除平台品牌
     *
     * @param brandId 平台商品牌id
     */
    public void deleteProductBrand(Long brandId);

    /**
     * 批量删除平台品牌
     *
     * @param brandIds 需要删除的平台品牌主键集合
     * @return 结果
     */
    public void deleteProductBrandByBrandIds(Long[] brandIds);

    /**
     * 获得平台品牌
     *
     * @param brandId 平台商品牌id
     * @return 平台品牌
     */
    public ProductBrand getProductBrand(Long brandId);

    /**
     * 获得平台品牌分页
     *
     * @param pageReqVO 分页查询
     * @return 平台品牌分页
     */
    PageResult<ProductBrand> getProductBrandPage(ProductBrandPageReqVO pageReqVO);

    /**
     * 停用品牌
     * @param brandId
     */
    void disable(Long brandId);

    /**
     * 启用品牌
     * @param brandId
     */
    void enable(Long brandId);

    /**
    * @Description: 获取所有品牌
    * @Author: liuxingyu
    * @Date: 2024/3/22 13:21
    */
    List<ProductBrand> getBrand();

    /**
    * @Description: 根据平台商品牌id获取平台商品牌
    * @Author: liuxingyu
    * @Date: 2024/3/25 15:00
    */
    ProductBrand getBrandByBrandId(Long brandId);

    /**
     * 批量获取品牌 (暂用于选中数据回显)
     * @param brandIds  品牌ID集合
     * @return  品牌集合数据
     */
    List<ProductBrandRespVO> getSelectedBrand(List<Long> brandIds);

    /**
     * 获取品牌, 通过品牌名称
     * @param brandName 品牌名称
     * @return  品牌
     */
    ProductBrand getBrandByBrandName(String brandName);

    /**
    * @Description: 通过id集合获取品牌
    * @Author: liuxingyu
    * @Date: 2024/5/18 15:16
    */
    List<ProductBrand> getBrandByIds(List<Long> brandIds);

    /**
     * 绑定品牌商
     * @param bindBrandMerchantReqVO
     */
    void bindBrandMerchant(BindBrandMerchantReqVO bindBrandMerchantReqVO);

    /**
     * 平台品牌导入
     * @param brandList
     * @return
     */
    String impordBrandData(List<BrandImportExcel> brandList);

    FileImportHandlerVo impordBrandDataEvent(List<BrandImportExcel> brandList, Long sysCode, Long fileImportId,Integer seq);

    Map<Long, BrandDTO> listByBrandIds(List<Long> brandIds);
}
