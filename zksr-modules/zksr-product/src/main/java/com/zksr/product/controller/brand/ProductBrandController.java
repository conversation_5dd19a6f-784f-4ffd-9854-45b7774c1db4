package com.zksr.product.controller.brand;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.api.brand.excel.BrandImportExcel;
import com.zksr.product.controller.brand.vo.ProductBrandPageReqVO;
import com.zksr.product.controller.brand.vo.ProductBrandRespVO;
import com.zksr.product.controller.brand.vo.ProductBrandSaveReqVO;
import com.zksr.product.controller.platform.PrdtPlatformSpuController;
import com.zksr.product.domain.ProductBrand;
import com.zksr.product.domain.excel.CategoryImportExcel;
import com.zksr.product.service.IProductBrandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.IOException;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台品牌Controller
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
@Api(tags = "商品服务 - 平台品牌接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/brand")
public class ProductBrandController {
    @Autowired
    private IProductBrandService productBrandService;

    /**
    * @Description: id集合获取平台集合
    * @Param: List<Long> brandIds
    * @return: List<ProductBrandRespVO>
    * @Author: liuxingyu
    * @Date: 2024/5/18 15:11
    */
    @ApiOperation(value = "id集合获取平台集合", httpMethod = HttpMethod.POST)
    @PostMapping("/getBrandByIds")
    public CommonResult<List<ProductBrandRespVO>> getBrandByIds(@ApiParam(name = "brandIds", value = "平台品牌ID集合", required = true)
                                                                    @RequestBody List<Long> brandIds) {
        List<ProductBrand> productBrandList = productBrandService.getBrandByIds(brandIds);
        return success(HutoolBeanUtils.toBean(productBrandList, ProductBrandRespVO.class));
    }

    /**
     * @Description: 获取所有品牌
     * @return: CommonResult<Long>
     * @Author: liuxingyu
     * @Date: 2024/3/22 13:19
     */
    @ApiOperation(value = "获取所有品牌", httpMethod = HttpMethod.GET)
    @GetMapping("/getBrand")
    public CommonResult<List<ProductBrandRespVO>> getBrand() {
        return success(HutoolBeanUtils.toBean(productBrandService.getBrand(),ProductBrandRespVO.class));
    }

    /**
     * 新增平台品牌
     */
    @ApiOperation(value = "新增平台品牌", httpMethod = HttpMethod.POST, notes = "product:brand:add")
    @RequiresPermissions("product:brand:add")
    @Log(title = "平台品牌", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody ProductBrandSaveReqVO createReqVO) {
        return success(productBrandService.insertProductBrand(createReqVO));
    }

    /**
     * 修改平台品牌
     */
    @ApiOperation(value = "修改平台品牌", httpMethod = HttpMethod.PUT, notes = "product:brand:edit")
    @RequiresPermissions("product:brand:edit")
    @Log(title = "平台品牌", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody ProductBrandSaveReqVO updateReqVO) {
        productBrandService.updateProductBrand(updateReqVO);
        return success(true);
    }

    /**
     * 删除平台品牌
     */
    @ApiOperation(value = "删除平台品牌", httpMethod = HttpMethod.GET, notes = "product:brand:remove")
    @RequiresPermissions("product:brand:remove")
    @Log(title = "平台品牌", businessType = BusinessType.DELETE)
    @DeleteMapping("/{brandIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] brandIds) {
        productBrandService.deleteProductBrandByBrandIds(brandIds);
        return success(true);
    }

    /**
     * 获取平台品牌详细信息
     */
    @ApiOperation(value = "获得平台品牌详情", httpMethod = HttpMethod.GET, notes = "product:brand:query")
    @RequiresPermissions("product:brand:query")
    @GetMapping(value = "/{brandId}")
    public CommonResult<ProductBrandRespVO> getInfo(@PathVariable("brandId") Long brandId) {
        ProductBrand productBrand = productBrandService.getProductBrand(brandId);
        return success(HutoolBeanUtils.toBean(productBrand, ProductBrandRespVO.class));
    }

    /**
     * 分页查询平台品牌
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台品牌分页列表", httpMethod = HttpMethod.GET, notes = "product:brand:list")
    @RequiresPermissions("product:brand:list")
    public CommonResult<PageResult<ProductBrandRespVO>> getPage(@Valid ProductBrandPageReqVO pageReqVO) {
        PageResult<ProductBrand> pageResult = productBrandService.getProductBrandPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, ProductBrandRespVO.class));
    }


    /**
     * 停用平台品牌
     */
    @ApiOperation(value = "停用平台品牌", httpMethod = HttpMethod.PUT, notes = "product:brand:disable")
    @RequiresPermissions("product:brand:disable")
    @Log(title = "平台品牌", businessType = BusinessType.UPDATE)
    @PutMapping("/disable/{brandId}")
    public CommonResult<Boolean> disable(@ApiParam(name = "brandId", value = "平台品牌ID", required = true) @PathVariable("brandId") Long brandId) {
        productBrandService.disable(brandId);
        return success(true);
    }

    /**
     * 启用平台品牌
     */
    @ApiOperation(value = "启用平台品牌", httpMethod = HttpMethod.PUT, notes = "product:brand:enable")
    @RequiresPermissions("product:brand:enable")
    @Log(title = "平台品牌", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{brandId}")
    public CommonResult<Boolean> enable(@ApiParam(name = "brandId", value = "平台品牌ID", required = true) @PathVariable("brandId") Long brandId) {
        productBrandService.enable(brandId);
        return success(true);
    }

    /**
     * 获取选中数据 (用于选中回显)
     * @param brandIds    品牌ID集合
     * @return  获取品牌I选中数据
     */
    @PostMapping("/getSelectedBatchInfo")
    @ApiOperation(value = "批量获取简略信息", httpMethod = "POST")
    public CommonResult<List<ProductBrandRespVO>> getSelectedBatchInfo(@Valid @Size(min = NumberPool.INT_ONE) @RequestBody List<Long> brandIds) {
        return success(productBrandService.getSelectedBrand(brandIds));
    }

    @ApiOperation(value = "导入平台品牌信息", httpMethod = HttpMethod.POST)
    @Log(title = "导入平台品牌信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/importData")
    public CommonResult<String> importData(MultipartFile file) throws Exception
    {
        ExcelUtil<BrandImportExcel> util = new ExcelUtil<>(BrandImportExcel.class);
        List<BrandImportExcel> brandList = util.importExcel(file.getInputStream(), 1);
        String message = productBrandService.impordBrandData(brandList);
        return success(message);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "获取平台品牌信息模板", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<BrandImportExcel> util = new ExcelUtil<BrandImportExcel>(BrandImportExcel.class);
        String instructions ="填表说明:\n" +
                "1、品牌名称:必填项;\n" +
                "2、品牌状态:必填项 0-正常 1-停用;\n " +
                "3、品牌备注信息:选填项;";
        util.importTemplateExcel(response, "平台品牌信息导入", StringUtils.EMPTY, instructions);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        public static final String IMPORT = "product:brand:import";
    }

}
