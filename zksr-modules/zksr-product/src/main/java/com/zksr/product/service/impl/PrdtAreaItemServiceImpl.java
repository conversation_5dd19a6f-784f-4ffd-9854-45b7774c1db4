package com.zksr.product.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LFUCache;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.domain.vo.openapi.receive.AreaItemOpenDTO;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.redis.utils.DcUtils;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.areaItem.excel.PrdtAreaItemImportExcel;
import com.zksr.product.api.areaItem.excel.PrdtAreaItemSortImportExcel;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemExcelVO;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.model.event.EsProductEventBuild;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateReqVO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateVO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.spu.vo.PrdtSpuNotItemPageReqExportVo;
import com.zksr.product.controller.areaItem.vo.*;
import com.zksr.product.controller.sku.vo.PrdtSkuRespVO;
import com.zksr.product.convert.areaItem.AreaItemConvert;
import com.zksr.product.convert.item.PrdtAreaItemConvert;
import com.zksr.product.domain.*;
import com.zksr.product.mapper.*;
import com.zksr.product.service.*;
import com.zksr.product.service.IPrdtAreaItemService;
import com.zksr.product.service.IPrdtAreaItemZipService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.product.service.IProductEventService;
import com.zksr.promotion.api.activity.ActivityApi;
import com.zksr.report.api.homePages.dto.HomePagesSkuDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.FileImportApi;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.apache.commons.collections4.CollectionUtils;
import com.zksr.system.api.supplierArea.SupplierAreaApi;
import com.zksr.system.api.supplierArea.dto.SupplierAreaDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.xmlbeans.impl.xb.ltgfmt.Code;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.pool.NumberPool.INT_THREE;
import static com.zksr.product.constant.ProductConstant.*;
import static com.zksr.product.enums.ErrorCodeConstants.*;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_PARTNER_CONFIG_LOCAL_DISABLE;

/**
 * 城市上架商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Service
@Slf4j
public class PrdtAreaItemServiceImpl implements IPrdtAreaItemService {
    @Autowired
    private PrdtAreaItemMapper prdtAreaItemMapper;
    @Autowired
    private PrdtCatgoryMapper prdtCatgoryMapper;

    @Autowired
    private PrdtSpuMapper prdtSpuMapper;

    // 入驻商信息调用服务
    @Resource
    private SupplierApi remoteSupplierApi;

    @Autowired
    private IProductEventService productEventService;

    @Autowired
    private Cache<Long, AreaItemDTO> areaItemDTOCache;

    @Autowired
    private IProductCacheService productCacheService;

    @Autowired
    private PrdtSkuMapper prdtSkuMapper;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private PrdtAreaClassMapper prdtAreaClassMapper;

    @Resource
    private AreaApi remoteAreaApi;

    @Autowired
    private PrdtChannelAreaClassMapper prdtChannelAreaClassMapper;

    @Resource
    private ActivityApi activityApi;

    @Autowired
    private IPrdtAreaItemZipService prdtAreaItemZipService;

    @Autowired
    private IPrdtSkuService skuService;

    @Autowired
    private IPrdtActivityService activityService;
    @Resource
    private FileImportApi fileImportApi;

    @Resource
    private SupplierAreaApi supplierAreaApi;

    @Autowired
    private Cache<Long, CatgoryDTO> catgoryDtoCache;

    @Resource
    private IPrdtAreaClassService prdtAreaClassService;

    /**
     * 新增城市上架商品
     *
     * @param createReqVO 创建信息
     */
    @Override
    @Transactional
    public List<PrdtAreaItem> insertPrdtAreaItem(@Valid List<PrdtAreaItemSaveReqVO> createReqVO) {
        //校验城市上下架商品
        if(ToolUtil.isEmpty(createReqVO)) throw exception(PRDT_AREA_ITEM_NOT_EXISTS);
        List<PrdtAreaItem> areaItemList = HutoolBeanUtils.toBean(createReqVO, PrdtAreaItem.class);
        List<PrdtAreaItem> result = new ArrayList<>();
        //校验
        PrdtAreaItem prdtAreaItem = areaItemList.get(0);
        //校验商品城市展示类别 是否是第三级
        checkClassLevel(prdtAreaItem.getAreaClassId());
        //校验商品区域城市 是否是二级
        remoteAreaApi.checkAreaByAreaId(prdtAreaItem.getAreaId());

        /**
         * ================================================ 前置验证    ==========================================
         */
        releaseBeforeValidate(areaItemList);
        /**
         * ================================================ 前置验证    ==========================================
         */
        areaItemList.forEach(item -> {
            prdtAreaItemZipService.insertPrdtAreaItemZip(item);
            // 校验
            PrdtAreaItem checkItem = prdtAreaItemMapper.selectAreaItemByCheck(item);
            if (Objects.isNull(checkItem)) {
                checkItem = item;
                //获取当前城市商品类型的最大排序号
                Integer sortNum = prdtAreaItemMapper.selectMaxSort(item);
                checkItem.setSortNum(sortNum + Code.GREATER_THAN);
            }
            PrdtSpu prdtSpu = prdtSpuMapper.selectById(item.getSpuId());
            // 调整上架状态
            checkItem.setShelfDate(DateUtils.getNowDate());
            checkItem.setAreaClassId(item.getAreaClassId());
            // 小单位一定有
            checkItem.setShelfStatus(PRDT_SHELF_STATUS_1);
            checkItem.setMinShelfStatus(PRDT_SHELF_STATUS_1);
            // 中单位和大单位不一定有
            checkItem.setMidShelfStatus(Objects.nonNull(prdtSpu.getMidUnit()) ? PRDT_SHELF_STATUS_1 : PRDT_SHELF_STATUS_0);
            checkItem.setLargeShelfStatus(Objects.nonNull(prdtSpu.getLargeUnit()) ? PRDT_SHELF_STATUS_1 : PRDT_SHELF_STATUS_0);
            prdtAreaItemMapper.insertOrUpdate(checkItem);
            // 在这里，无论更新还是插入成功，都把item添加到新的列表
            result.add(item);
        });
        return result;
    }

    /**
     * 修改城市上架商品
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("all")
    public List<PrdtAreaItem> updatePrdtAreaItem(PrdtAreaItemSaveReqVO updateReqVO) {
        PrdtAreaItem prdtAreaItem = prdtAreaItemMapper.selectById(updateReqVO.getAreaItemId());
        // 收集缓存刷新信息
        ArrayList<PrdtAreaItem> eventItems = ListUtil.toList(prdtAreaItem);
        //校验商品区域城市 是否是二级
        remoteAreaApi.checkAreaByAreaId(prdtAreaItem.getAreaId());
        //校验
        if(ToolUtil.isEmpty(updateReqVO.getUpdateFlag())) throw exception(PRDT_AREA_ITEM_UPDATE_FALG_NOT_EXISTS);

        //修改顺序/类别
        //顺序
        if(PRDT_ITEM_UPDATE_FLAG_0 == updateReqVO.getUpdateFlag()){
            prdtAreaItem.setSortNum(updateReqVO.getSortNum());
            prdtAreaItemMapper.updateById(prdtAreaItem);
        }else{
            //校验类别
            checkClassLevel(updateReqVO.getAreaClassId());
            // 原始拷贝对象, 如果有新增会使用这个对象
            PrdtAreaItem newPrdtAreaItem = JSON.parseObject(JSON.toJSONString(prdtAreaItem), PrdtAreaItem.class);
            // 新展示分类
            PrdtAreaClass newPrdtAreaClass = prdtAreaClassMapper.selectById(updateReqVO.getAreaClassId());
            // 新展示分类关联的渠道
            List<PrdtChannelAreaClass> newPrdtChannelAreaClassList = prdtChannelAreaClassMapper.getListByAreaClassId(updateReqVO.getAreaClassId());

            // 1, 新的展示分类, 不是有渠道的, 并且不是电子围栏
            // 2, 并且新的展示没有商品
            // 3, 就可以直接修改展示分类
            // 查询新类别是不是已经有商品了
            PrdtAreaItem source = prdtAreaItemMapper.selectByAreaIdAndSaleId(prdtAreaItem.getSkuId(), prdtAreaItem.getAreaId(), updateReqVO.getAreaClassId());
            // 没有最简单了, 直接修改展示分类
            if (newPrdtAreaClass.getDzwlFlag() != NumberPool.LONG_ONE && newPrdtChannelAreaClassList.isEmpty() && Objects.isNull(source)) {
                prdtAreaItem.setAreaClassId(updateReqVO.getAreaClassId());
                prdtAreaItemMapper.updateById(prdtAreaItem);
                // 修改了展示类目, 由于展示类目是组合唯一键ID, 所以需要提前删除
                productEventService.executeEvent(EsProductEventBuild.removeItem(updateReqVO.getAreaItemId()));
            }else{
                // 修改类别逻辑
                // 1, 下架现在的类别,
                prdtAreaItem.setShelfStatus(PRDT_SHELF_STATUS_0);
                prdtAreaItem.setMinShelfStatus(PRDT_SHELF_STATUS_0);
                prdtAreaItem.setMidShelfStatus(PRDT_SHELF_STATUS_0);
                prdtAreaItem.setLargeShelfStatus(PRDT_SHELF_STATUS_0);
                prdtAreaItemMapper.updateById(prdtAreaItem);

                // 查询新类别是不是已经有商品了
                if (Objects.nonNull(source)) {
                    source.setShelfStatus(newPrdtAreaItem.getShelfStatus());
                    source.setMinShelfStatus(newPrdtAreaItem.getMinShelfStatus());
                    source.setMidShelfStatus(newPrdtAreaItem.getMidShelfStatus());
                    source.setLargeShelfStatus(newPrdtAreaItem.getLargeShelfStatus());
                    prdtAreaItemMapper.updateById(source);
                } else {
                    // 2, 复制上架新类别
                    newPrdtAreaItem.setAreaItemId(null);
                    newPrdtAreaItem.setAreaClassId(updateReqVO.getAreaClassId());
                    //顺序改为最大值
                    Integer maxSort = prdtAreaItemMapper.selectMaxSort(prdtAreaItem);
                    //设置顺序
                    newPrdtAreaItem.setSortNum(maxSort + NumberPool.INT_ONE);
                    prdtAreaItemMapper.insert(newPrdtAreaItem);
                    // 更新事件
                    eventItems.add(newPrdtAreaItem);
                }
            }
        }
        return eventItems;
    }

    /**
     * 批量上下架城市上架商品
     * @param areaItemIds 修改信息Id
     * @param minShelfStatus
     * @param midShelfStatus
     * @param largeShelfStatus
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PrdtAreaItem> updatePrdtAreaItemShelfStatus(Long[] areaItemIds, Integer minShelfStatus, Integer midShelfStatus, Integer largeShelfStatus) {
        //校验入参
        if(NumberPool.LOWER_GROUND == minShelfStatus && NumberPool.LOWER_GROUND == midShelfStatus && NumberPool.LOWER_GROUND == largeShelfStatus){
            throw exception(PRDT_AREA_ITEM_SHELF_STATUS_NOT_EXISTS);
        }
        //获取上下架商品信息
        List<PrdtAreaItem> itemList = prdtAreaItemMapper.selectAreaItemListById(areaItemIds);
        //上架时校验：如果存在已停用的商品 不允许批量上架
        //校验该次修改上架状态是否是改为上架
        boolean isShelfStatus = PRDT_SHELF_STATUS_1.equals(minShelfStatus)
                || PRDT_SHELF_STATUS_1.equals(midShelfStatus)
                || PRDT_SHELF_STATUS_1.equals(largeShelfStatus);

        if(isShelfStatus){
            //校验需要上架的商品是否存在SKU已停用的
            List<Long> skuList = itemList.stream().map(PrdtAreaItem::getSkuId).collect(Collectors.toList());
            Long skuStatusCount = prdtSkuMapper.selectSkuCount(new PrdtSkuRespVO(PRDT_STATUS_0, skuList));
            if(skuStatusCount > NumberPool.INT_ZERO){
                throw exception(PRDT_AREA_ITEM_SHELF_STATUS_SKU_STATIS);
            }
        }
        // 循环判断上架
        itemList.stream().forEach(item -> {
            // 可能因为其他的单位已经下架了, 这次下架导致整个SKU 是下架状态
            Integer tempShelfStatus =
                    (minShelfStatus > NumberPool.LOWER_GROUND   ? minShelfStatus : item.getMinShelfStatus())
                    + (midShelfStatus > NumberPool.LOWER_GROUND ? midShelfStatus : item.getMidShelfStatus())
                    + (largeShelfStatus > NumberPool.LOWER_GROUND ? largeShelfStatus : item.getLargeShelfStatus());
            Integer updateShelfStatus = tempShelfStatus > NumberPool.INT_ZERO ? NumberPool.INT_ONE : NumberPool.INT_ZERO;

            // 判断是否存在单位再操作
            SpuDTO spuDTO = productCacheService.getSpuDTO(item.getSpuId());
            Integer updateMinShelfStatus = ToolUtil.isNotEmpty(spuDTO.getMinUnit()) || minShelfStatus == NumberPool.INT_ZERO ? minShelfStatus : NumberPool.INT_ZERO;
            Integer updateMidShelfStatus = ToolUtil.isNotEmpty(spuDTO.getMidUnit()) || midShelfStatus == NumberPool.INT_ZERO ? midShelfStatus : NumberPool.INT_ZERO;
            Integer updateLargeShelfStatus = ToolUtil.isNotEmpty(spuDTO.getLargeUnit()) || largeShelfStatus == NumberPool.INT_ZERO ? largeShelfStatus : NumberPool.INT_ZERO;
            prdtAreaItemMapper.updatePrdtAreaItemShelfStatus(new Long[]{item.getAreaItemId()}, updateMinShelfStatus, updateMidShelfStatus, updateLargeShelfStatus, updateShelfStatus);
        });
        return itemList;
    }

    /**
     * 删除城市上架商品
     *
     * @param areaItemId 城市上架商品id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrdtAreaItem(Long areaItemId) {
        // 删除
        prdtAreaItemMapper.deleteById(areaItemId);
    }

    /**
     * 批量删除城市上架商品
     *
     * @param areaItemIds 需要删除的城市上架商品主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrdtAreaItemByAreaItemIds(Long[] areaItemIds) {
        for(Long areaItemId : areaItemIds){
            this.deletePrdtAreaItem(areaItemId);
        }
    }

    /**
     * 获得城市上架商品
     *
     * @param areaItemId 城市上架商品id
     * @return 城市上架商品
     */
    @Override
    public PrdtAreaItemRespVO getPrdtAreaItem(Long areaItemId) {
        PrdtAreaItem prdtAreaItem = prdtAreaItemMapper.selectById(areaItemId);
        PrdtAreaItemRespVO respVO = HutoolBeanUtils.toBean(prdtAreaItem, PrdtAreaItemRespVO.class);
        //获取分类信息
        respVO.setAreaClassLevelName(getClassLevelName(respVO.getAreaClassId()));
        return respVO;
    }

    /**
     * 查询分页数据
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<PrdtAreaItemPageRespVO> getPrdtAreaItemPage(PrdtAreaItemPageReqVO pageReqVO) {
        Page<PrdtAreaItemPageReqVO> page = new Page<>(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        // 查询运营商绑定的区域城市
        pageReqVO.setAreaIdList(DcUtils.getAreaList(SecurityUtils.getDcId()));
        if(ToolUtil.isNotEmpty(pageReqVO.getSpuId())){
            pageReqVO.setSpuIdList(ListUtil.toList(pageReqVO.getSpuId()));
        }
        // 加载促销范围限制
        activityService.loadActivityScopeByItemPage(pageReqVO);
        // 查询数据
        Page<PrdtAreaItemPageRespVO> areaItemPage = prdtAreaItemMapper.selectPageAreaItemByPage(pageReqVO, page);
        if(ToolUtil.isEmpty(areaItemPage.getRecords())){
            return new PageResult<>(areaItemPage.getRecords(),areaItemPage.getTotal());
        }
        // 渲染其他数据库的数据
        LFUCache<Long, SupplierDTO> supplierDTOMap = CacheUtil.newLFUCache(0);
        LFUCache<Long, AreaDTO> areaDTOMap = CacheUtil.newLFUCache(0);
        if(ToolUtil.isNotEmpty(areaItemPage.getRecords())) {
            //组装商品名称、设置入驻商名称
            areaItemPage.getRecords().forEach(record -> {
                //商品名称
                record.setSpuName(PropertyAndValDTO.getPropertiesSpuName(record.getProperties(),record.getSpuName()));
                if(ToolUtil.isNotEmpty(record.getSupplierId())) {
                    // 设置入驻商名称
                    SupplierDTO supplierDTO = supplierDTOMap.get(record.getSupplierId(), () -> productCacheService.getSupplierDTO(record.getSupplierId()));
                    if (Objects.nonNull(supplierDTO)) {
                        record.setSupplierName(supplierDTO.getSupplierName());
                    }
                    // 设置城市名称
                    AreaDTO area = areaDTOMap.get(record.getAreaId(), () -> productCacheService.getAreaDto(record.getAreaId()));
                    if (Objects.nonNull(area)) {
                        record.setAreaName(area.getAreaName());
                    }
                }
            });
        }
        return new PageResult<>(areaItemPage.getRecords(),areaItemPage.getTotal());
    }

    public List<PrdtSpuNotItemPageReqExportVo> getPrdtAreaItemExport(PrdtAreaItemPageReqVO pageReqVO) {
        // 查询运营商绑定的区域城市
        pageReqVO.setAreaIdList(DcUtils.getAreaList(SecurityUtils.getDcId()));
        if(ToolUtil.isNotEmpty(pageReqVO.getSpuId())){
            List<Long> spuIdList = new ArrayList<>();
            spuIdList.add(pageReqVO.getSpuId());
            pageReqVO.setSpuIdList(spuIdList);
        }
        // 查询数据
        List<PrdtAreaItemPageRespVO> areaItem = prdtAreaItemMapper.selectPageAreaItemByPage(pageReqVO);
        List<PrdtSpuNotItemPageReqExportVo> list = new ArrayList<>();
        for (PrdtAreaItemPageRespVO prdtAreaItemPageRespVO : areaItem) {
            PrdtSpuNotItemPageReqExportVo exportVo = new PrdtSpuNotItemPageReqExportVo();
            exportVo.setAreaId(prdtAreaItemPageRespVO.getAreaId());
            exportVo.setClassId(prdtAreaItemPageRespVO.getAreaClassId());
            exportVo.setSkuId(prdtAreaItemPageRespVO.getSkuId());
            exportVo.setMinShelfStatusName(Objects.equals(prdtAreaItemPageRespVO.getMinShelfStatus(), 1) ? "上架" : "下架");
            exportVo.setMidShelfStatusName(Objects.equals(prdtAreaItemPageRespVO.getMidShelfStatus(), 1) ? "上架" : "下架");
            exportVo.setLargeShelfStatusName(Objects.equals(prdtAreaItemPageRespVO.getLargeShelfStatus(), 1) ? "上架" : "下架");
            list.add(exportVo);
        }
        return list;
    }

    @Override
    public PrdtAreaItem getAreaItemId(Long areaItemId) {
        return prdtAreaItemMapper.selectById(areaItemId);
    }

    @Override
    public List<AreaItemDTO> getAreaItemListByApi(AreaItemDTO itemDTO) {
        return prdtAreaItemMapper.selectAreaItemListByApi(itemDTO);
    }

    @Override
    public void cacheEvent(List<PrdtAreaItem> areaItems) {
        //刷新ES
        productEventService.acceptEvent(EsProductEventBuild.spuEvent(new ArrayList<>(areaItems.stream().map(PrdtAreaItem::getSpuId).collect(Collectors.toSet()))));
        //清除缓存
        areaItemDTOCache.removeAll(areaItems.stream().map(PrdtAreaItem::getAreaItemId).collect(Collectors.toSet()));
        // 重新加载商品详情缓存
        Map<Long, List<PrdtAreaItem>> spuMap = areaItems.stream().collect(Collectors.groupingBy(PrdtAreaItem::getSpuId));
        spuMap.forEach((spuId, spuList) -> {
            spuList.forEach(areaItem -> productCacheService.setSkuUnitGroup(spuId, areaItem.getAreaId(), areaItem.getAreaClassId(), ProductType.LOCAL));
        });
        // 初始化库存
        for (PrdtAreaItem item : areaItems) {
            // 没有库存需要初始化库存
            if (!redisStockService.hashSkuStock(item.getSkuId())) {
                redisStockService.setSkuStock(item.getSkuId(), prdtSkuMapper.selectPrdtSkuByStock(item.getSkuId()));
            }
        }
    }

    @Override
    public List<PrdtAreaItem> getSaleQtyTotalList(Long minAreaItemId) {
        return prdtAreaItemMapper.selectSaleQtyTotalList(minAreaItemId);
    }

    @Override
    @Transactional
    @SuppressWarnings("all")
    public List<PrdtAreaItem> batchEditItemAreaClassId(PrdtAreaItemSaveReqVO updateReqVO) {
        List<PrdtAreaItem> prdtAreaItems = prdtAreaItemMapper.selectBatchIds(updateReqVO.getAreaItemIds());
        List<PrdtAreaItem> eventItems = new ArrayList<>(prdtAreaItems);
        //校验类别
        checkClassLevel(updateReqVO.getAreaClassId());
        // 新展示分类
        PrdtAreaClass newPrdtAreaClass = prdtAreaClassMapper.selectById(updateReqVO.getAreaClassId());
        // 新展示分类关联的渠道
        List<PrdtChannelAreaClass> newPrdtChannelAreaClassList = prdtChannelAreaClassMapper.getListByAreaClassId(updateReqVO.getAreaClassId());

        for (PrdtAreaItem prdtAreaItem : prdtAreaItems) {
            // 拷贝对象, 这就是操作的原始对象
            PrdtAreaItem newPrdtAreaItem = JSON.parseObject(JSON.toJSONString(prdtAreaItem), PrdtAreaItem.class);

            // 1, 新的展示分类, 不是有渠道的, 并且不是电子围栏
            // 2, 并且新的展示没有商品
            // 3, 就可以直接修改展示分类
            // 查询新类别是不是已经有商品了
            PrdtAreaItem source = prdtAreaItemMapper.selectByAreaIdAndSaleId(prdtAreaItem.getSkuId(), prdtAreaItem.getAreaId(), updateReqVO.getAreaClassId());
            // 没有最简单了, 直接修改展示分类
            if (newPrdtAreaClass.getDzwlFlag() != NumberPool.LONG_ONE && newPrdtChannelAreaClassList.isEmpty() && Objects.isNull(source)) {
                prdtAreaItem.setAreaClassId(updateReqVO.getAreaClassId());
                prdtAreaItemMapper.updateById(prdtAreaItem);
            }else{
                // 修改类别逻辑
                // 1, 下架现在的类别,
                prdtAreaItem.setShelfStatus(PRDT_SHELF_STATUS_0);
                prdtAreaItem.setMinShelfStatus(PRDT_SHELF_STATUS_0);
                prdtAreaItem.setMidShelfStatus(PRDT_SHELF_STATUS_0);
                prdtAreaItem.setLargeShelfStatus(PRDT_SHELF_STATUS_0);
                prdtAreaItemMapper.updateById(prdtAreaItem);

                // 查询新类别是不是已经有商品了
                if (Objects.nonNull(source)) {
                    source.setShelfStatus(newPrdtAreaItem.getShelfStatus());
                    source.setMinShelfStatus(newPrdtAreaItem.getMinShelfStatus());
                    source.setMidShelfStatus(newPrdtAreaItem.getMidShelfStatus());
                    source.setLargeShelfStatus(newPrdtAreaItem.getLargeShelfStatus());
                    prdtAreaItemMapper.updateById(source);
                } else {
                    // 2, 复制上架新类别
                    newPrdtAreaItem.setAreaItemId(null);
                    newPrdtAreaItem.setAreaClassId(updateReqVO.getAreaClassId());
                    //顺序改为最大值
                    Integer maxSort = prdtAreaItemMapper.selectMaxSort(prdtAreaItem);
                    //设置顺序
                    newPrdtAreaItem.setSortNum(maxSort + NumberPool.INT_ONE);
                    prdtAreaItemMapper.insert(newPrdtAreaItem);
                    // 更新事件
                    eventItems.add(newPrdtAreaItem);
                }
            }
        }
        // 修改了展示类目, 由于展示类目是组合唯一键ID, 所以需要提前删除
        productEventService.executeEvent(EsProductEventBuild.removeItem(updateReqVO.getAreaItemIds()));
        return prdtAreaItems;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PrdtAreaItem> copyAreaItem(PrdtAreaItemCopyReqVO vo) {
        //校验复制类型
        Integer copyType = vo.getCopyType();
        if(ToolUtil.isEmpty(copyType)){
            throw exception(PRDT_AREA_ITEM_COPY_TYPE_NULL);
        }

        //目标城市ID
        Long targetAreaId = vo.getTargetAreaId();

        //目标展示分类ID
        Long targetAreaClassId = vo.getTargetAreaClassId();

        //校验商品城市展示类别 是否是第三级
        checkClassLevel(targetAreaClassId);
        //校验商品区域城市 是否是二级
        remoteAreaApi.checkAreaByAreaId(targetAreaId);

        //需要复制到目标展示分类下的 上架商品集合
        List<PrdtAreaItemSaveReqVO> targetItemList = new ArrayList<>();

        //开始复制
        if(PRDT_CLASS_COPY_TYPE_0.equals(copyType)){
            //0 上架商品多选复制
            //需要复制的 上架商品ID集合
            List<Long> copyAreaItemIds = vo.getCopyAreaItemIds();
            //校验
            if(copyAreaItemIds.isEmpty()){
                throw exception(PRDT_AREA_ITEM_COPY_0_CHECK_NULL);
            }

            //开始复制
            //获取复制的上架商品信息
            List<PrdtAreaItem> copyAreaItemList = prdtAreaItemMapper.selectAreaItemListById(copyAreaItemIds.toArray(new Long[0]));

            //组装需要复制到目标分类下的上架商品数据
            assembleCopyAreaItemList(copyAreaItemList, targetAreaId, targetAreaClassId,targetItemList);


        }else if(PRDT_CLASS_COPY_TYPE_1.equals(copyType)){
            //1 一键复制上架商品
            //复制前的城市ID
            Long copyAreaId = vo.getCopyAreaId();

            //复制前的城市展示分类ID
            Long copyAreaClassId = vo.getCopyAreaClassId();

            //获取该城市、该展示分类下的所有上架商品信息
            //开始复制
            //获取复制的上架商品信息
            List<PrdtAreaItem> copyAreaItemList = prdtAreaItemMapper.selectAreaItemByList(new PrdtAreaItemRespVO(copyAreaId, copyAreaClassId));

            //组装需要复制到目标分类下的上架商品数据
            assembleCopyAreaItemList(copyAreaItemList, targetAreaId, targetAreaClassId,targetItemList);

        }

        //如果需要复制的上架商品在目标分类下不存在 则做新增
        if(!targetItemList.isEmpty()){
            //新增
            List<PrdtAreaItem> prdtAreaItems = insertPrdtAreaItem(targetItemList);

            //目前需求： 复制后的上架商品都必须是下架状态 将新增的上架商品调整为 未上架
            prdtAreaItemMapper.updatePrdtAreaItemShelfStatus(prdtAreaItems.stream().map(PrdtAreaItem::getAreaItemId).toArray(Long[]::new),PRDT_SHELF_STATUS_0,PRDT_SHELF_STATUS_0,PRDT_SHELF_STATUS_0,PRDT_SHELF_STATUS_0);

            return prdtAreaItems;
        }else{
            //如果所有需要复制的上架商品 在目标分类中都存在则提示
            throw exception(PRDT_AREA_ITEM_COPY_DATA_NULL);
        }
    }

    @Override
    public List<HomePagesSkuDataRespDTO> getHomePagesSkuData(HomePagesReqVO reqVO) {
        // 商品SKU上架数据
        List<HomePagesSkuDataRespDTO> skuShelfData = prdtAreaItemMapper.getHomePagesSkuData(reqVO);
        // 平台一级品类数据总数
        HomePagesSkuDataRespDTO categoryData = prdtCatgoryMapper.getHomePagesCategoryData(reqVO);
        skuShelfData.forEach(skuShelf -> {
            skuShelf.setCategory1Qty(categoryData.getCategory1Qty())
                    .setBeforeCategory1Qty(categoryData.getBeforeCategory1Qty())
                    ;
        });
        return skuShelfData;
    }

    @Override
    public PrdtAreaItem getAreaItemBySpuCombineId(Long spuCombineId) {
        if(ToolUtil.isNotEmpty(spuCombineId)){
            return prdtAreaItemMapper.getAreaItemBySpuCombineId(spuCombineId);
        }
        return new PrdtAreaItem();
    }

    @Override
    public void updateAreaCombineItem(AreaItemDTO itemDTO) {
        prdtAreaItemMapper.updateById(HutoolBeanUtils.toBean(itemDTO, PrdtAreaItem.class));
    }

    @Override
    public PrdtAreaItem getAreaItemByActivityId(Long activityId) {
        return prdtAreaItemMapper.getAreaItemByActivityId(activityId);
    }

    private void validatePrdtAreaItemExists(Long areaItemId) {
        if (prdtAreaItemMapper.selectById(areaItemId) == null) {
            throw exception(PRDT_AREA_ITEM_NOT_EXISTS);
        }
    }

    /**
     * 校验城市展示分类是否是三级分类
     * @param areaClassId
     */
    public void checkClassLevel(Long areaClassId){
        if(areaClassId == null){
            throw exception(PRDT_AREA_CLASS_IS_NULL);
        }
        PrdtAreaClass prdtAreaClass = prdtAreaClassMapper.selectById(areaClassId);
        if(ToolUtil.isEmpty(prdtAreaClass)){
            throw exception(PRDT_AREA_CLASS_NOT_EXISTS);
        }
        if(prdtAreaClass.getLevel() != INT_THREE){
            throw exception(PRDT_AREA_CLASS_LEVEL_NOT_MAKE);
        }
    }

    /**
     * 获取分类各级信息
     * @param areaClassId
     * @return
     */
    public String getClassLevelName(Long areaClassId){
        PrdtAreaClass prdtAreaClass = prdtAreaClassMapper.selectById(areaClassId);
        //校验当前商品是否是三级展示分类
        if(prdtAreaClass.getLevel() != INT_THREE){
            throw exception(PRDT_AREA_CLASS_LEVEL_NOT_MAKE);
        }

        //获取当前分类的一级、二级分类信息
        //获取二级
        PrdtAreaClass secondAreaClass = prdtAreaClassMapper.selectById(prdtAreaClass.getPid());
        if(ToolUtil.isEmpty(secondAreaClass)){
            throw exception(PRDT_AREA_CLASS_NOT_SECOND_LEVEL_EXISTS);
        }

        //获取一级
        PrdtAreaClass firstAreaClass = prdtAreaClassMapper.selectById(secondAreaClass.getPid());
        if(ToolUtil.isEmpty(secondAreaClass)){
            throw exception(PRDT_AREA_CLASS_NOT_FIRST_LEVEL_EXISTS);
        }

        //组装分类
        StringJoiner level = new StringJoiner(">>");
        level.add(firstAreaClass.getAreaClassName());
        level.add(secondAreaClass.getAreaClassName());
        level.add(prdtAreaClass.getAreaClassName());

        return level.toString();
    }

    /**
     * 组装 一键复制的上架商品信息
     * @param copyAreaItemList 需要复制的上架商品集合
     * @param targetAreaId 目标城市ID
     * @param targetAreaClassId 目标展示分类ID
     * @param targetItemList 需要复制到目标展示分类下的 上架商品集合
     */
    private void assembleCopyAreaItemList(List<PrdtAreaItem> copyAreaItemList,Long targetAreaId,Long targetAreaClassId,List<PrdtAreaItemSaveReqVO> targetItemList){

        //获取复制的上架商品对应的skuID
        List<Long> copySkuIdList = copyAreaItemList.stream().map(PrdtAreaItem::getSkuId).collect(Collectors.toList());

        //拿需要复制的skuID集合去查询目标城市、上架展示分类下的商品信息  如果存在需要复制的SKUID信息 则说明目标展示分类下已存在该上架商品
        //已存在目标分类下的上架商品信息
        List<PrdtAreaItem> existAreaItemList = prdtAreaItemMapper.selectAreaItemByList(new PrdtAreaItemRespVO(targetAreaId, targetAreaClassId, copySkuIdList));

        //如果存在 则需要把已存在的上架商品信息筛掉
        if(!existAreaItemList.isEmpty()){
            copySkuIdList.removeAll(existAreaItemList.stream().map(PrdtAreaItem::getSkuId).collect(Collectors.toList()));

            copyAreaItemList = copyAreaItemList.stream().filter(x -> copySkuIdList.contains(x.getSkuId())).collect(Collectors.toList());
        }

        //组装需要复制的上架商品信息
        copyAreaItemList.forEach(x -> {
            PrdtAreaItemSaveReqVO targetItem = new PrdtAreaItemSaveReqVO();
            targetItem.setAreaId(targetAreaId);
            targetItem.setAreaClassId(targetAreaClassId);
            targetItem.setSpuId(x.getSpuId());
            targetItem.setSkuId(x.getSkuId());
            targetItem.setSupplierId(x.getSupplierId());

            targetItemList.add(targetItem);

        });
    }


    /**
     * 上架前验证
     * @param areaItemList
     */
    public void releaseBeforeValidate(List<PrdtAreaItem> areaItemList) {
        // 验证允不允许使用本地商品
        AppletBaseConfigDTO appletBaseConfigDTO = productCacheService.getAppletBaseConfigDTO(SecurityUtils.getLoginUser().getSysCode());
        if (Objects.nonNull(appletBaseConfigDTO) && StringPool.TWO.equals(appletBaseConfigDTO.getSaleClassSwitch())) {
            throw exception(SYS_PARTNER_CONFIG_LOCAL_DISABLE);
        }
        // 验证上架利润比例
        PrdtSkuSaleTotalRateVO skuSaleTotalRate = skuService.getSkuSaleTotalRate(
                PrdtSkuSaleTotalRateReqVO
                        .builder()
                        .skuIdList(areaItemList.stream().map(PrdtAreaItem::getSkuId).distinct().collect(Collectors.toList()))
                        .sysCode(SecurityUtils.getLoginUser().getSysCode())
                        .build()
        );

        // 查询平台商默认配置的比例
        PayConfigDTO payConfigDTO = productCacheService.getPayConfigDTO(SecurityUtils.getLoginUser().getSysCode());
        areaItemList.forEach(item -> {
            // 校验城市上下架商品
            if (ToolUtil.isEmpty(item.getAreaId())) {
                throw exception(PRDT_AREA_ITEM_AREA_NOT_EXISTS);
            }
            SkuDTO skuDTO = productCacheService.getSkuDTO(item.getSkuId());
            SpuDTO prdtSpu = productCacheService.getSpuDTO(skuDTO.getSpuId());
            if (Objects.isNull(prdtSpu)) {
                throw exception(PRDT_SPU_SPU_ID_EXISTS);
            }
            if (Objects.isNull(prdtSpu.getCatgoryId())) {
                throw exception(PRDT_SPU_CATEGORY_NOT_EXISTS, prdtSpu.getSpuName());
            }

            // B2B支付, 利润比例最大29%
            if (Objects.nonNull(payConfigDTO) && PayChannelEnum.isB2b(payConfigDTO.getStoreOrderPayPlatform())) {
                if (StringPool.ONE.equals(payConfigDTO.getProfitModel())) {
                    // 销售 - 利润
                    if (Objects.nonNull(prdtSpu.getMinUnit())) {
                        // 小单位
                        if (NumberUtil.isGreater(skuDTO.getMarkPrice().subtract(skuDTO.getCostPrice()).divide(skuDTO.getMarkPrice(), 4, RoundingMode.HALF_UP), NumberPool.B2B_PAY_NAX_RATE)) {
                            throw exception(PRDT_SPU_MAX_PROFIT_RATE);
                        }
                    }
                    if (Objects.nonNull(prdtSpu.getMidUnit())) {
                        // 中单位
                        if (NumberUtil.isGreater(skuDTO.getMidMarkPrice().subtract(skuDTO.getMidCostPrice()).divide(skuDTO.getMidMarkPrice(), 4, RoundingMode.HALF_UP), NumberPool.B2B_PAY_NAX_RATE)) {
                            throw exception(PRDT_SPU_MAX_PROFIT_RATE);
                        }
                    }
                    if (Objects.nonNull(prdtSpu.getLargeUnit())) {
                        // 大单位
                        if (NumberUtil.isGreater(skuDTO.getLargeMarkPrice().subtract(skuDTO.getLargeCostPrice()).divide(skuDTO.getLargeMarkPrice(), 4, RoundingMode.HALF_UP), NumberPool.B2B_PAY_NAX_RATE)) {
                            throw exception(PRDT_SPU_MAX_PROFIT_RATE);
                        }
                    }
                } else {
                    // 销售 * 比例
                    PrdtSkuSaleTotalRateVO.RateConfig skuRate = skuSaleTotalRate.getSkuRate(item.getSkuId());
                    if (Objects.nonNull(skuRate) && NumberUtil.isGreater(skuRate.getRate(), NumberPool.B2B_PAY_NAX_RATE)) {
                        throw exception(PRDT_SPU_MAX_PROFIT_RATE);
                    }
                }
            }
        });
    }

    public String impordDataDc(List<PrdtAreaItemImportExcel> prdtAreaList){
        return impordDataDcEvent(prdtAreaList,SecurityUtils.getLoginUser().getSysCode(),null,SecurityUtils.getLoginUser().getDcId());
    }

    @Override
    public String impordDataDcEvent(List<PrdtAreaItemImportExcel> prdtAreaList,Long sysCode,Long fileImportId,Long dcId) {
        //导入记录
        SysFileImport sysFileImport = new SysFileImport();
        List<SysFileImportDtl> sysFileImportDtls = new ArrayList<>();
        Boolean f = false;
        if (fileImportId!=null){
            CommonResult<SysFileImport> fileImportById = fileImportApi.getFileImportById(fileImportId);
            if (fileImportById.isSuccess()&&fileImportById.getData()!=null){
                sysFileImport = fileImportById.getData();
                f = true;
            }else {
                throw new ServiceException("未查询到相关导入任务");
            }
        }
//        Long dcId = SecurityUtils.getLoginUser().getDcId();
        String resultMessage = "";
        if (ToolUtil.isNotEmpty(dcId)) {
            int successNum = 0;
            int failureNum = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();
            List<PrdtAreaItem> result = new ArrayList<>();
            try {
                if (prdtAreaList.isEmpty()) {
                    // 如果导入数据为空，则不进行数据导入
                    throw exception(THE_PLATFORM_MANAGEMENT_CATEGORY_IS_EMPTY);
                }
                Map<String, Long> prdtAreaMap = new HashMap<>(); // 存储已插入的类别，避免重复插入
                // 数据校验
                for (int line = 0; line < prdtAreaList.size(); line++) {
                    if (failureMsg.length() > 2000) {
                        break;
                    }
                    int cellNumber = line + 3;
                    PrdtAreaItemImportExcel itemData = prdtAreaList.get(line);
                    prdtAreaItemImportExcel(itemData, cellNumber);
                    //校验商品城市展示类别 是否是第三级
                    checkClassLevel(prdtAreaList.get(line).getAreaClassId());
                    //校验商品区域城市 是否是二级
                    remoteAreaApi.checkAreaByAreaId(prdtAreaList.get(line).getAreaId());
                }

                // 商品上架
                List<PrdtAreaItem> areaItemList = HutoolBeanUtils.toBean(prdtAreaList, PrdtAreaItem.class);
                areaItemList.stream().forEach(item -> {
                    // 根据sku找到spu再找到入驻商
                    SkuDTO skuDTO = productCacheService.getSkuDTO(item.getSkuId());
                    SpuDTO spuDTO = productCacheService.getSpuDTO(skuDTO.getSpuId());
                    if (ToolUtil.isNotEmpty(spuDTO)) {
                        item.setSpuId(spuDTO.getSpuId());
                        item.setSupplierId(spuDTO.getSupplierId());
                    }
                });

                releaseBeforeValidate(areaItemList);

                for (PrdtAreaItem item : areaItemList) {
                    try {
                        prdtAreaItemZipService.insertPrdtAreaItemZip(item);
                        // 校验
                        PrdtAreaItem checkItem = prdtAreaItemMapper.selectAreaItemByCheck(item);
                        if (Objects.isNull(checkItem)) {
                            checkItem = item;
                            //获取当前城市商品类型的最大排序号
                            Integer sortNum = prdtAreaItemMapper.selectMaxSort(item);
                            checkItem.setSortNum(sortNum + Code.GREATER_THAN);
                        }
                        PrdtSpu prdtSpu = prdtSpuMapper.selectById(item.getSpuId());
                        // 调整上架状态
                        checkItem.setShelfDate(DateUtils.getNowDate());
                        checkItem.setAreaClassId(item.getAreaClassId());
                        // 小单位一定有

                        checkItem.setMinShelfStatus(item.getMinShelfStatus());
                        // 中单位和大单位不一定有
                        if (ToolUtil.isNotEmpty(item.getMidShelfStatus())){
                            // 查询商品是否有对应的单位
                            if (ToolUtil.isEmpty(prdtSpu.getMidUnit()) && Objects.equals(item.getMidShelfStatus(), PRDT_SHELF_STATUS_1)){
                                failureNum++;
                                throw new Exception(StringUtils.format("{}商品没有中单位无法上下架",prdtSpu.getSpuName() ));
                            }
                            checkItem.setMidShelfStatus(item.getMidShelfStatus());
                        }
                        if (ToolUtil.isNotEmpty(item.getLargeShelfStatus())){
                            // 查询商品是否有对应的单位
                            if (ToolUtil.isEmpty(prdtSpu.getLargeUnit()) && Objects.equals(item.getLargeShelfStatus(), PRDT_SHELF_STATUS_1)){
                                failureNum++;
                                throw new Exception(StringUtils.format("{}商品没有大单位无法上下架",prdtSpu.getSpuName() ));
                            }
                            checkItem.setLargeShelfStatus(item.getLargeShelfStatus());
                        }

                        if (Objects.equals(item.getMinShelfStatus(), PRDT_SHELF_STATUS_0) && Objects.equals(item.getMidShelfStatus(), PRDT_SHELF_STATUS_0) && Objects.equals(item.getLargeShelfStatus(), PRDT_SHELF_STATUS_0)){
                            checkItem.setShelfStatus(0);
                        }else {
                            checkItem.setShelfStatus(1);
                        }
                        successNum++;
                        prdtAreaItemMapper.insertOrUpdate(checkItem);
                        // 在这里，无论更新还是插入成功，都把item添加到新的列表
                        result.add(item);
                    }catch (Exception e){
                        failureMsg.append(StringUtils.format("<br/>skuId数据导入失败，错误信息：{}。", e.getMessage()));
                    }
                }
                cacheEvent(areaItemList); // 刷新缓存es


            } catch (Exception e) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>skuId数据导入失败，错误信息：{}。", e.getMessage()));
            }
            if (failureNum > 0) {
                resultMessage = String.format("共导入%d条，成功%d条，失败%d条。失败原因如下：", prdtAreaList.size(), successNum, failureNum)
                        + failureMsg.toString();
                throw new ServiceException(resultMessage);
            } else {
                resultMessage = String.format("恭喜您，数据已全部导入成功！共 %d 条", successNum);
            }
        }
        return resultMessage;
    }

    @Override
    public List<PrdtAreaItemExcelVO> getAreaItemExportList(PrdtAreaItemPageReqVO pageReqVO) {
        List<PrdtAreaItemPageRespVO> prdtAreaItemPageRespVOS = this.getPrdtAreaItemPage(pageReqVO).getList();
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, String> unitMap = Optional.ofNullable(dictCache).orElseGet(ArrayList::new).stream()
                .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel, (v1, v2) -> v2));
        return prdtAreaItemPageRespVOS.stream().map(vo -> {
            PrdtAreaItemExcelVO excelVO = PrdtAreaItemConvert.INSTANCE.convertExcelVO(vo);
            Optional.ofNullable(vo.getUnit()).map(String::valueOf).map(unitMap::get).ifPresent(excelVO::setUnit);
            Optional.ofNullable(vo.getMidUnit()).map(String::valueOf).map(unitMap::get).ifPresent(excelVO::setMidUnit);
            Optional.ofNullable(vo.getLargeUnit()).map(String::valueOf).map(unitMap::get).ifPresent(excelVO::setLargeUnit);
            return excelVO;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importSortData(List<PrdtAreaItemSortImportExcel> prdtAreaItemSortList) {
        if (prdtAreaItemSortList.isEmpty()) {
            throw new ServiceException("导入数据为空");
        }

        Long dcId = SecurityUtils.getDcId();

        if (Objects.isNull(dcId)) {
            throw new ServiceException("只有运营商才能操作");
        }

        // 检查是否有重复的skuId
        List<String> duplicateSkuIds = prdtAreaItemSortList.stream()
                .map(PrdtAreaItemSortImportExcel::getSkuId)
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet().stream()
                .filter(e -> e.getValue() > 1L)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(duplicateSkuIds)) {
            String duplicate = String.join(",", duplicateSkuIds);
            throw new ServiceException(StringUtils.format("<br/>skuId为<span style='color:red'>{}</span>的数据存在多行", duplicate));
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        List<PrdtAreaItem> updateList = new ArrayList<>();

        for (int line = 0; line < prdtAreaItemSortList.size(); line++) {
            if (failureMsg.length() > 2000) {
                break;
            }

            List<String> errorList = new ArrayList<>();
            int cellNumber = line + 3;
            PrdtAreaItemSortImportExcel itemData = prdtAreaItemSortList.get(line);

            try {
                // 校验数据
                PrdtAreaItem prdtAreaItem = validateSortImportData(itemData, dcId, errorList);

                if (errorList.isEmpty() && Objects.nonNull(prdtAreaItem)) {
                    // 更新排序
                    prdtAreaItem.setUpdateTime(new Date());
                    prdtAreaItem.setSortNum(Integer.parseInt(itemData.getSort().trim()));
                    updateList.add(prdtAreaItem);
                    successNum++;
                } else {
                    failureNum++;
                    failureMsg.append("<br/>第").append(cellNumber).append("行数据：").append(String.join("，", errorList));
                }

            } catch (Exception e) {
                failureNum++;
                failureMsg.append("<br/>第").append(cellNumber).append("行数据：").append(e.getMessage());
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            prdtAreaItemMapper.updateBatch(updateList, 200);
            successMsg.append("恭喜您，数据已全部导入成功！共 ").append(successNum).append(" 条");
        }

        return successMsg.toString();
    }

    /**
     * 校验排序导入数据
     */
    private PrdtAreaItem validateSortImportData(PrdtAreaItemSortImportExcel itemData, Long dcId, List<String> errorList) {

        // 1. 校验入驻商编码
        Long supplierIdFromExcel = null;
        SupplierDTO supplierDTO = null;
        if (StringUtils.isBlank(itemData.getSupplierId())) {
            errorList.add("<span style='color:red'>入驻商编码</span>不能为空");
        } else if (!NumberUtil.isLong(itemData.getSupplierId().trim())) {
            errorList.add("<span style='color:red'>入驻商编码</span>格式有误");
        } else {
            supplierIdFromExcel = Long.parseLong(itemData.getSupplierId().trim());
            supplierDTO = remoteSupplierApi.getBySupplierId(supplierIdFromExcel).getCheckedData();
            if (Objects.isNull(supplierDTO)) {
                errorList.add("<span style='color:red'>入驻商编码</span>不存在");
            } else {
                // 校验入驻商是否启用
                if (!Long.valueOf(NumberPool.LONG_ONE).equals(supplierDTO.getStatus())) {
                    errorList.add("<span style='color:red'>入驻商</span>未启用");
                }

                // 校验入驻商是否在运营商的运营区域内
                if (Objects.nonNull(dcId) && !dcId.equals(supplierDTO.getDcId())) {
                    errorList.add("<span style='color:red'>入驻商</span>不在该运营商的运营区域内");
                }
            }
        }

        // 2. 校验SKU ID
        Long skuId = null;
        SkuDTO skuDTO = null;
        SpuDTO spuDTO = null;
        if (StringUtils.isBlank(itemData.getSkuId())) {
            errorList.add("<span style='color:red'>skuId</span>不能为空");
        } else if (!NumberUtil.isLong(itemData.getSkuId().trim())) {
            errorList.add("<span style='color:red'>skuId</span>格式有误");
        } else {
            skuId = Long.parseLong(itemData.getSkuId().trim());
            skuDTO = productCacheService.getSkuDTO(skuId);
            if (Objects.isNull(skuDTO)) {
                errorList.add("<span style='color:red'>skuId</span>不存在");
            } else {
                // 获取SPU信息
                spuDTO = productCacheService.getSpuDTO(skuDTO.getSpuId());
                if (Objects.isNull(spuDTO)) {
                    errorList.add("<span style='color:red'>skuId对应的商品</span>不存在");
                } else {
                    // 校验商品是否属于导入的入驻商
                    if (Objects.nonNull(supplierIdFromExcel) && !spuDTO.getSupplierId().equals(supplierIdFromExcel)) {
                        errorList.add("<span style='color:red'>商品</span>不属于该入驻商");
                    }

                    // 校验商品是否有效
                    if (!Long.valueOf(NumberPool.LONG_ONE).equals(spuDTO.getStatus())) {
                        errorList.add("<span style='color:red'>商品</span>未启用");
                    }
                }
            }
        }

        // 3. 校验三级分类编码
        Long areaClassId = null;
        if (StringUtils.isBlank(itemData.getAreaClassId())) {
            errorList.add("<span style='color:red'>三级分类编码</span>不能为空");
        } else if (!NumberUtil.isLong(itemData.getAreaClassId().trim())) {
            errorList.add("<span style='color:red'>三级分类编码</span>格式有误");
        } else {
            areaClassId = Long.parseLong(itemData.getAreaClassId().trim());
            PrdtAreaClass areaClass = prdtAreaClassMapper.selectById(areaClassId);
            if (Objects.isNull(areaClass)) {
                errorList.add("<span style='color:red'>三级分类编码</span>不存在");
            } else {
                // 校验是否为三级分类
                if (!Integer.valueOf(NumberPool.INT_THREE).equals(areaClass.getLevel())) {
                    errorList.add("<span style='color:red'>分类编码</span>不是三级分类");
                }

                // 校验分类是否有效
                if (!StringPool.ZERO.equals(areaClass.getDelFlag())) {
                    errorList.add("<span style='color:red'>三级分类</span>已删除");
                }

                // 校验分类是否属于该入驻商
                if (Objects.nonNull(supplierIdFromExcel) && Objects.nonNull(areaClass.getSupplierId())
                        && !areaClass.getSupplierId().equals(supplierIdFromExcel)) {
                    errorList.add("<span style='color:red'>三级分类</span>不属于该入驻商");
                }
            }
        }

        // 4. 校验排序号
        if (StringUtils.isBlank(itemData.getSort())) {
            errorList.add("<span style='color:red'>排序号</span>不能为空");
        } else if (!NumberUtil.isInteger(itemData.getSort().trim())) {
            errorList.add("<span style='color:red'>排序号</span>必须为正整数");
        } else {
            int sortNum = Integer.parseInt(itemData.getSort().trim());
            if (sortNum < 1) {
                errorList.add("<span style='color:red'>排序号</span>必须大于0的正整数");
            }
        }

        // 查找对应的城市上架商品
        PrdtAreaItem areaItem = null;
        if (errorList.isEmpty()) {
            areaItem = prdtAreaItemMapper.selectOne(
                    new LambdaQueryWrapper<PrdtAreaItem>()
                            .eq(PrdtAreaItem::getSkuId, skuId)
                            .eq(PrdtAreaItem::getAreaClassId, areaClassId)
            );
            if (Objects.isNull(areaItem)) {
                errorList.add("<span style='color:red'>商品</span>未上架到该分类");
            } else if (!Integer.valueOf(NumberPool.INT_ONE).equals(areaItem.getShelfStatus())) {
                // 校验商品是否上架
                errorList.add("<span style='color:red'>商品</span>未上架");
            }
        }
        return areaItem;
    }

    private void prdtAreaItemImportExcel(PrdtAreaItemImportExcel itemData, int cellNumber) throws Exception {
        if (ToolUtil.isEmpty(itemData.getAreaId())){
            throw new Exception(StringUtils.format("第{}行数据上架区域编号不能为空，请先修改数据再导入", cellNumber));
        }
        if (ToolUtil.isEmpty(itemData.getAreaClassId())){
            throw new Exception(StringUtils.format("第{}行数据上架三级展示分类编号不能为空，请先修改数据再导入", cellNumber));
        }
        if (ToolUtil.isEmpty(itemData.getSkuId())){
            throw new Exception(StringUtils.format("第{}行数据sku不能为空，请先修改数据再导入", cellNumber));
        }
        if (ToolUtil.isEmpty(itemData.getMinShelfStatus())){
            throw new Exception(StringUtils.format("第{}行数据小单位上架状态不能为空，请先修改数据再导入", cellNumber));
        }
        if (ToolUtil.isEmpty(itemData.getMidShelfStatus())){
            throw new Exception(StringUtils.format("第{}行数据中单位上架状态不能为空，请先修改数据再导入", cellNumber));
        }
        if (ToolUtil.isEmpty(itemData.getLargeShelfStatus())){
            throw new Exception(StringUtils.format("第{}行数据大单位上架状态不能为空，请先修改数据再导入", cellNumber));
        }


        // 校验区域是否存在
        AreaDTO areaDTO = productCacheService.getAreaDto(itemData.getAreaId());
        if (ToolUtil.isEmpty(areaDTO)) {
            throw new Exception(StringUtils.format("第{}行数据上架区域编号不存在！请先修改数据再导入", cellNumber));
        }
        // 校验展示分类是否存在
        PrdtAreaClass areaClass = prdtAreaClassMapper.selectById(itemData.getAreaClassId());
        if (ToolUtil.isEmpty(areaClass)) {
            throw new Exception(StringUtils.format("第{}行数据上架三级展示分类编号不存在！请先修改数据再导入", cellNumber));
        }

        // 根据区域编号和展示分类编号获取分类信息
        PrdtAreaClass prdtAreaClass = prdtAreaClassMapper.selectClassByAreaIdAndAreaClassId(itemData.getAreaId(), itemData.getAreaClassId());
        if (ToolUtil.isEmpty(prdtAreaClass)) {
            throw new Exception(
                    StringUtils.format("第{}行数据的上架三级展示分类【{}】不在上架区域编号【{}】中！请先修改数据再导入",
                            cellNumber,
                            itemData.getAreaClassId(),
                            itemData.getAreaId())
            );
        }

    }


    @Override
    public void addAreaItem(AreaItemOpenDTO areaItemOpenDTO) {
        List<Long> areaList = supplierAreaApi.selectListBySupplierId(areaItemOpenDTO.getSupplierId()).getCheckedData();
        log.info("入驻商{}关联区域={}",areaItemOpenDTO.getSupplierId(), JsonUtils.toJsonString(areaList));
        if(CollectionUtils.isEmpty(areaList)){
            return;
        }
        PrdtSpu spu = prdtSpuMapper.selectBySpuNo(areaItemOpenDTO.getSpuNo(),areaItemOpenDTO.getSupplierId());
        if(null == spu || null == spu.getCatgoryId()){
            return;
        }

        //匹配城市分类
        CatgoryDTO catgoryDTO = catgoryDtoCache.get(spu.getCatgoryId());
        if(null == catgoryDTO){
            return;
        }
        //3级管理分类名称
        String catgoryName3 = catgoryDTO.getCatgoryName();
        catgoryDTO = catgoryDtoCache.get(catgoryDTO.getPid());
        if(null == catgoryDTO){
            return;
        }
        //2级管理分类名称
        String catgoryName2 = catgoryDTO.getCatgoryName();

        catgoryDTO = catgoryDtoCache.get(catgoryDTO.getPid());

        if(null == catgoryDTO){
            return;
        }
        //1级管理分类名称
        String catgoryName1 = catgoryDTO.getCatgoryName();
        List<String> catgoryNameList = new ArrayList<>();
        catgoryNameList.add(catgoryName1);
        catgoryNameList.add(catgoryName2);
        catgoryNameList.add(catgoryName3);
//        List<Long> supplierIds = new ArrayList<>();
//        supplierIds.add(areaItemOpenDTO.getSupplierId());

        //查询入驻商的城市分类
//        List<AreaClassDTO> areaClassDTOList = areaClassApi.getAreaClassBranchList(supplierIds).getCheckedData();
//        if(CollectionUtils.isEmpty(areaClassDTOList)){
//            return;
//        }

        List<PrdtAreaItemSaveReqVO> createReqVOList = new ArrayList<>();
        areaList.forEach(areaItem->{
            PrdtAreaItemSaveReqVO saveReqVO = AreaItemConvert.INSTANCE.convert2PrdtAreaItemSaveReqVO(areaItemOpenDTO);
            saveReqVO.setAreaId(areaItem);
            // 匹配城市分类
            List<PrdtAreaClass> areaClassList = prdtAreaClassService.getAreaClass(areaItemOpenDTO.getSupplierId(), areaItem, null);
            if(CollectionUtils.isEmpty(areaClassList)){
                return;
            }
            // 1. 按level分组
            Map<Integer, List<PrdtAreaClass>> levelMap = areaClassList.stream().collect(Collectors.groupingBy(PrdtAreaClass::getLevel));
            List<PrdtAreaClass> level1List = levelMap.getOrDefault(1, Collections.emptyList());
            List<PrdtAreaClass> level2List = levelMap.getOrDefault(2, Collections.emptyList());
            List<PrdtAreaClass> level3List = levelMap.getOrDefault(3, Collections.emptyList());

            Long matchedAreaClassId = null;
            // 2. 先尝试管理1级匹配城市1级
            PrdtAreaClass match1 = level1List.stream().filter(x -> catgoryName1.equals(x.getAreaClassName())).findFirst().orElse(null);
            PrdtAreaClass match2 = level2List.stream().filter(x -> catgoryName2.equals(x.getAreaClassName())).findFirst().orElse(null);
            PrdtAreaClass match3 = level3List.stream().filter(x -> catgoryName3.equals(x.getAreaClassName())).findFirst().orElse(null);

            // 3. 按PRD规则递进匹配
            // 一级匹配不上，则用二级匹配一级，三级匹配二级
            if(match1 != null) {
                // 一级匹配上，找二级
                List<PrdtAreaClass> subLevel2 = level2List.stream().filter(x -> Objects.equals(x.getPid(), match1.getAreaClassId())).collect(Collectors.toList());
                PrdtAreaClass subMatch2 = subLevel2.stream().filter(x -> catgoryName2.equals(x.getAreaClassName())).findFirst().orElse(null);
                if(subMatch2 != null) {
                    // 二级也匹配上，找三级
                    List<PrdtAreaClass> subLevel3 = level3List.stream().filter(x -> Objects.equals(x.getPid(), subMatch2.getAreaClassId())).collect(Collectors.toList());
                    PrdtAreaClass subMatch3 = subLevel3.stream().filter(x -> catgoryName3.equals(x.getAreaClassName())).findFirst().orElse(null);
                    if(subMatch3 != null) {
                        matchedAreaClassId = subMatch3.getAreaClassId();
                    }
                } else {
                    // 二级匹配不上，三级匹配二级
                    List<PrdtAreaClass> subLevel3 = level3List.stream().filter(x -> Objects.equals(x.getPid(), match1.getAreaClassId())).collect(Collectors.toList());
                    PrdtAreaClass subMatch3 = subLevel3.stream().filter(x -> catgoryName3.equals(x.getAreaClassName())).findFirst().orElse(null);
                    if(subMatch3 != null) {
                        matchedAreaClassId = subMatch3.getAreaClassId();
                    }
                }
            } else {
                // 一级匹配不上，二级匹配一级
                PrdtAreaClass match1By2 = level1List.stream().filter(x -> catgoryName2.equals(x.getAreaClassName())).findFirst().orElse(null);
                if(match1By2 != null) {
                    // 找三级
                    List<PrdtAreaClass> subLevel3 = level3List.stream().filter(x -> Objects.equals(x.getPid(), match1By2.getAreaClassId())).collect(Collectors.toList());
                    PrdtAreaClass subMatch3 = subLevel3.stream().filter(x -> catgoryName3.equals(x.getAreaClassName())).findFirst().orElse(null);
                    if(subMatch3 != null) {
                        matchedAreaClassId = subMatch3.getAreaClassId();
                    }
                } else {
                    // 三级匹配二级
                    PrdtAreaClass match2By3 = level2List.stream().filter(x -> catgoryName3.equals(x.getAreaClassName())).findFirst().orElse(null);
                    if(match2By3 != null) {
                        matchedAreaClassId = match2By3.getAreaClassId();
                    }
                }
            }
            // 如果还没匹配上，最后尝试三级直接匹配
            if(matchedAreaClassId == null && match3 != null) {
                matchedAreaClassId = match3.getAreaClassId();
            }
            if(matchedAreaClassId == null) {
                // 匹配不上直接return
                return;
            }
            saveReqVO.setAreaClassId(matchedAreaClassId);
            createReqVOList.add(saveReqVO);
        });


        List<PrdtAreaItem> areaItems = insertPrdtAreaItem(createReqVOList);
        // 事务之外的刷新缓存事件
        cacheEvent(areaItems);

    }

}
