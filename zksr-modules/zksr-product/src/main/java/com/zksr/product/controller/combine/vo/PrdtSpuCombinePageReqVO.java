package com.zksr.product.controller.combine.vo;

import java.math.BigDecimal;
import java.util.List;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 组合商品对象 prdt_spu_combine
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@ApiModel("组合商品 - prdt_spu_combine分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtSpuCombinePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 组合商品id */
    @ApiModelProperty(value = "销售价6")
    private Long spuCombineId;

    @ApiModelProperty(value = "1-全国商品可用（平台商设定）2-本地商品可用（运营商设定）")
    private Integer funcScope;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /** 组合商品编号 */
    @Excel(name = "组合商品编号")
    @ApiModelProperty(value = "组合商品编号")
    private String spuCombineNo;

    /** 组合商品名 */
    @Excel(name = "组合商品名")
    @ApiModelProperty(value = "组合商品名")
    private String spuCombineName;

    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面图")
    private String thumb;

    /** 封面视频（url） */
    @Excel(name = "封面视频", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面视频")
    private String thumbVideo;

    /** 详情页轮播（json） */
    @Excel(name = "详情页轮播", readConverterExp = "j=son")
    @ApiModelProperty(value = "详情页轮播")
    private String images;

    /** 详情信息(富文本) */
    @Excel(name = "详情信息(富文本)")
    @ApiModelProperty(value = "详情信息(富文本)")
    private String details;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否")
    private Long isDelete;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Long status;

    /** 商品规格 */
    @Excel(name = "商品规格")
    @ApiModelProperty(value = "商品规格")
    private String specName;

    /** 总限量 */
    @Excel(name = "总限量")
    @ApiModelProperty(value = "总限量")
    private Long totalLimit;

    /** 起订 */
    @Excel(name = "起订")
    @ApiModelProperty(value = "起订")
    private Long minOq;

    /** 订货组数 */
    @Excel(name = "订货组数")
    @ApiModelProperty(value = "订货组数")
    private Long jumpOq;

    /** 限购 */
    @Excel(name = "限购")
    @ApiModelProperty(value = "限购")
    private Long maxOq;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "单位-数据字典")
    private Long unit;

    /** 标准价 */
    @Excel(name = "标准价")
    @ApiModelProperty(value = "标准价")
    private BigDecimal markPrice;

    /** 建议零售价 */
    @Excel(name = "建议零售价")
    @ApiModelProperty(value = "建议零售价")
    private BigDecimal suggestPrice;

    /** 销售价1 */
    @Excel(name = "销售价1")
    @ApiModelProperty(value = "销售价1")
    private BigDecimal salePrice1;

    /** 销售价2 */
    @Excel(name = "销售价2")
    @ApiModelProperty(value = "销售价2")
    private BigDecimal salePrice2;

    /** 销售价3 */
    @Excel(name = "销售价3")
    @ApiModelProperty(value = "销售价3")
    private BigDecimal salePrice3;

    /** 销售价4 */
    @Excel(name = "销售价4")
    @ApiModelProperty(value = "销售价4")
    private BigDecimal salePrice4;

    /** 销售价5 */
    @Excel(name = "销售价5")
    @ApiModelProperty(value = "销售价5")
    private BigDecimal salePrice5;

    /** 销售价6 */
    @Excel(name = "销售价6")
    @ApiModelProperty(value = "销售价6")
    private BigDecimal salePrice6;

    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /** 创建日期开始时间 */
    @ApiModelProperty(value = "创建日期开始时间")
    private String createStartTime;

    /** 创建日期结束时间 */
    @ApiModelProperty(value = "创建日期结束时间")
    private String createEndTime;

    /** 活动开始时间 */
    @ApiModelProperty(value = "活动开始时间")
    private String activityStartTime;

    /** 活动结束时间 */
    @ApiModelProperty(value = "活动结束时间")
    private String activityEndTime;

    /** 活动状态 */
    @ApiModelProperty(value = "活动状态")
    private Integer activityStatus;

    /** 活动ID集合 */
    @ApiModelProperty(value = "活动ID集合")
    private List<Long> activityIds;


}
