package com.zksr.product.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.api.catgory.dto.CatgoryIdDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.controller.catgory.vo.PrdtCatgoryPageReqVO;
import com.zksr.product.controller.catgory.vo.PrdtCatgoryRespVO;
import com.zksr.product.domain.PrdtCatgory;
import com.zksr.report.api.homePages.dto.HomePagesSkuDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.pool.StringPool.LIMIT_ONE;


/**
 * 平台商管理分类Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface PrdtCatgoryMapper extends BaseMapperX<PrdtCatgory> {
    default PageResult<PrdtCatgory> selectPage(PrdtCatgoryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtCatgory>()
                .eqIfPresent(PrdtCatgory::getCatgoryId, reqVO.getCatgoryId())
                .eqIfPresent(PrdtCatgory::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrdtCatgory::getPid, reqVO.getPid())
                .likeIfPresent(PrdtCatgory::getCatgoryName, reqVO.getCatgoryName())
                .eqIfPresent(PrdtCatgory::getIcon, reqVO.getIcon())
                .eqIfPresent(PrdtCatgory::getSort, reqVO.getSort())
                .eqIfPresent(PrdtCatgory::getStatus, reqVO.getStatus())
                .eqIfPresent(PrdtCatgory::getPartnerRate, reqVO.getPartnerRate())
                .eqIfPresent(PrdtCatgory::getDcRate, reqVO.getDcRate())
                .eqIfPresent(PrdtCatgory::getColonel1Rate, reqVO.getColonel1Rate())
                .eqIfPresent(PrdtCatgory::getColonel2Rate, reqVO.getColonel2Rate())
                .eqIfPresent(PrdtCatgory::getMemo, reqVO.getMemo())
                .eqIfPresent(PrdtCatgory::getLevel, reqVO.getLevel())
                .orderByDesc(PrdtCatgory::getCatgoryId));
    }

    /**
     * @Description: 通过名称和父Id查询数量
     * @Author: liuxingyu
     * @Date: 2024/3/4 10:53
     */
    default Long selectCatgoryNameCount(String name, Long pid, Long id) {
        return selectCount(new LambdaQueryWrapper<PrdtCatgory>()
                .eq(PrdtCatgory::getCatgoryName, name)
                .eq(PrdtCatgory::getPid, pid)
                .ne(ObjectUtil.isNotNull(id), PrdtCatgory::getCatgoryId, id));
    }

    /**
     * @Description: 根据入驻商编号获取绑定的管理类别
     * @Author: liuxingyu
     * @Date: 2024/3/8 11:40
     */
    List<PrdtCatgory> getCatgoryBySupplierId(Long supplierId);

    /**
     * @Description: 获取平台商管理分类列表
     * @Author: liuxingyu
     * @Date: 2024/3/19 15:25
     */
    List<PrdtCatgory> getCatgoryList(@Param("catgoryId") Long catgoryId, @Param("supplierId") Long supplierId, @Param("level") Integer level);

    /**
     * 获取管理类别有效分润比例
     *
     * @param catgoryId
     * @param areaId
     * @return
     */
    CatgoryRateDTO selectByIdAndLevelThree(@Param("catgoryId") Long catgoryId, @Param("areaId") Long areaId);

    /**
     * @param catgoryId 管理分类ID
     * @return 父级级别指定管理分类列表
     */
    default List<PrdtCatgory> selectByPid(Long catgoryId) {
        return selectList(
                Wrappers.lambdaQuery(PrdtCatgory.class)
                        .eq(PrdtCatgory::getPid, catgoryId)
        );
    }

    /**
     * @Description: 通过id集合获取管理分类集合
     * @Author: liuxingyu
     * @Date: 2024/4/15 19:20
     */
    default List<PrdtCatgory> selectByIds(List<Long> ids) {
        return selectList(new LambdaQueryWrapper<PrdtCatgory>().in(PrdtCatgory::getCatgoryId, ids));
    }

    /**
     * 根据名称和级别获取管理分类
     *
     * @param categoryName
     * @param level
     * @return
     */
    default PrdtCatgory selectCategoryByNameAndLevel(String categoryName, int level) {
        return selectOne(
                Wrappers.lambdaQuery(PrdtCatgory.class)
                        .eq(PrdtCatgory::getCatgoryName, categoryName)
                        .eq(PrdtCatgory::getLevel, level)
                        .last(LIMIT_ONE)
        );
    }

    /**
     * @Description: 根据平台code获取平台管理分类
     * @Author: liuxingyu
     * @Date: 2024/5/7 11:16
     */
    default List<PrdtCatgory> getListBySysCode(Long sysCode) {
        return selectList(new LambdaQueryWrapper<PrdtCatgory>()
                .eq(PrdtCatgory::getSysCode, sysCode)
                .eq(PrdtCatgory::getStatus, StringPool.ONE)
                .orderByAsc(PrdtCatgory::getSort));
    }

    /**
     * @Description: 获取平台管理类别一级Id
     * @Author: liuxingyu
     * @Date: 2024/5/16 17:03
     */
    List<CatgoryIdDTO> getCatgoryFirstId();

    /**
     * @Description: 通过id集合获取管理分类信息
     * @Author: liuxingyu
     * @Date: 2024/5/18 15:22
     */
    default List<PrdtCatgory> getCatgoryByIds(List<Long> catgoryIds) {
        return selectList(new LambdaQueryWrapper<PrdtCatgory>().in(PrdtCatgory::getCatgoryId, catgoryIds));
    }

    /**
     * @Description: 查询当前传入的分类Id的父级分类ID
     * @Author: zhegnsenbing
     * @Date: 2024/11/15 15:22
     */
    default List<Long> getPidCatgoryIds(List<Long> catgoryIds) {
        List<PrdtCatgory> prdtCatgoryList = selectList(new LambdaQueryWrapper<PrdtCatgory>()
                .in(PrdtCatgory::getCatgoryId, catgoryIds)
                .select(PrdtCatgory::getPid)
                .isNotNull(PrdtCatgory::getPid)); // 只查询 pid 并确保 pid 不为空
        if (ObjectUtil.isEmpty(prdtCatgoryList)) {
            return Collections.emptyList();
        }
        // 直接返回 pid 列表（不进行内部去重）
        return prdtCatgoryList.stream()
                .map(PrdtCatgory::getPid)
                .collect(Collectors.toList());
    }

    /**
     * @Description: 入驻商获取管理分类分页信息
     * @Author: liuxingyu
     * @Date: 2024/5/31 15:23
     */
    Page<PrdtCatgoryRespVO> getPageBySupplierId(@Param("page") Page<PrdtCatgoryPageReqVO> page, @Param("pageReqVO") PrdtCatgoryPageReqVO pageReqVO);

    /**
     * @Description: 通过pid获取管理类别集合
     * @Author: liuxingyu
     * @Date: 2024/6/6 15:06
     */
    default List<PrdtCatgory> getListByPid(Long catgoryId) {
        return selectList(new LambdaQueryWrapper<PrdtCatgory>().eq(PrdtCatgory::getPid, catgoryId));
    }

    /**
     * @Description: 根据父级id获取平台管理分类集合
     * @Author: liuxingyu
     * @Date: 2024/6/6 16:06
     */
    default List<PrdtCatgory> selectByPIds(List<Long> ids) {
        return selectList(new LambdaQueryWrapper<PrdtCatgory>().in(PrdtCatgory::getPid, ids));
    }

    /**
     * 根据入驻商、分类名称和级别获取管理分类
     * @param supplierId 入驻商ID
     * @param categoryId 管理分类ID
     * @param level 级别
     * @return
     */
    PrdtCatgory selectCategoryBySupplierAneNameAndLevel(@Param("supplierId") Long supplierId, @Param("categoryId") Long categoryId, @Param("level") int level);

    /**
     * 获取绑定了商品的类别ID
     * @param catgoryIds
     * @return
     */
    List<Long> getCategoriesWithProducts(@Param("catgoryIds")List<Long> catgoryIds,@Param("supplierId") Long supplierId);

    /**
     * 根据入驻商、分类Id和级别获取管理分类
     * @param supplierId 入驻商ID
     * @param categoryId 管理分类Id
     * @param level 级别
     * @return
     */
    PrdtCatgory selectCategoryBySupplierAndCategoryIdAndLevel(@Param("supplierId") Long supplierId, @Param("categoryId") String categoryId, @Param("level") int level);

    PrdtCatgory getCategoryByNameAndParentId(@Param("catgoryName") String catgoryName,@Param("pid") Long pid);

    default PrdtCatgory getCategoryByNameAndLevelAndParentId(String categoryName, int level, Long pid){
        LambdaQueryWrapper<PrdtCatgory> queryWrapper = new LambdaQueryWrapper<PrdtCatgory>()
                .eq(PrdtCatgory::getCatgoryName, categoryName)
                .eq(PrdtCatgory::getLevel, level);

        if (pid != null) {
            queryWrapper.eq(PrdtCatgory::getPid, pid);
        }
        return selectOne(queryWrapper.last(LIMIT_ONE));
    }

    List<PrdtCatgory> selectPageExt(PrdtCatgoryPageReqVO pageReqVO);

    List<PrdtCatgory> selectThreeByFirst(@Param("idList") List<Long> idList);

    /**
     * 获取PC首页SKU数据 - 查询一级管理分类总数
     * @param reqVO
     * @return
     */
    HomePagesSkuDataRespDTO getHomePagesCategoryData(HomePagesReqVO reqVO);

    List<String> isExistsProduct( @Param("catgoryIdList") List<Long> catgoryIdList);

    List<PrdtCatgory> getCategoryBySupplierIds(@Param("supplierIds") List<String> supplierIds);
}
