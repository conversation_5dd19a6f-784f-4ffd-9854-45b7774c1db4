package com.zksr.product.domain.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 批量修改商品类别
 * @date 2024/7/9
 */
@Data
@ApiModel(description = "批量修改商品类别")
public class ProductCatgoryImportUpdateExcel {
    @Excel(name = "入驻商编号", headerColor = IndexedColors.RED)
    private String supplierCode;

    @Excel(name = "skuId", headerColor = IndexedColors.RED)
    private String skuId;

    @Excel(name = "商品名称")
    private String spuName;

    @Excel(name = "商品类别(三级管理类别)", headerColor = IndexedColors.RED)
    private String catgoryId;

}
