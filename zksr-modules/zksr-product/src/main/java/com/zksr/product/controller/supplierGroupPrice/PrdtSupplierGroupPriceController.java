package com.zksr.product.controller.supplierGroupPrice;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtSupplierGroupPrice;
import com.zksr.product.service.IPrdtSupplierGroupPriceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.supplierGroupPrice.vo.PrdtSupplierGroupPricePageReqVO;
import com.zksr.product.controller.supplierGroupPrice.vo.PrdtSupplierGroupPriceSaveReqVO;
import com.zksr.product.controller.supplierGroupPrice.vo.PrdtSupplierGroupPriceRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台商城市分组价格Controller
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Api(tags = "管理后台 - 平台商城市分组价格接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/supplierGroupPrice")
public class PrdtSupplierGroupPriceController {
    @Autowired
    private IPrdtSupplierGroupPriceService prdtSupplierGroupPriceService;

    /**
     * 新增平台商城市分组价格
     */
    @ApiOperation(value = "新增平台商城市分组价格", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "平台商城市分组价格", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtSupplierGroupPriceSaveReqVO createReqVO) {
        return success(prdtSupplierGroupPriceService.insertPrdtSupplierGroupPrice(createReqVO));
    }

    /**
     * 修改平台商城市分组价格
     */
    @ApiOperation(value = "修改平台商城市分组价格", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "平台商城市分组价格", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtSupplierGroupPriceSaveReqVO updateReqVO) {
            prdtSupplierGroupPriceService.updatePrdtSupplierGroupPrice(updateReqVO);
        return success(true);
    }

    /**
     * 删除平台商城市分组价格
     */
    @ApiOperation(value = "删除平台商城市分组价格", httpMethod = "DELETE", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "平台商城市分组价格", businessType = BusinessType.DELETE)
    @DeleteMapping("/{groupId}/{areaId}")
    public CommonResult<Boolean> remove(@PathVariable("groupId") Long groupId,@PathVariable("areaId") Long areaId) {
        prdtSupplierGroupPriceService.deletePrdtSupplierGroupPrice(groupId,areaId);
        return success(true);
    }

    /**
     * 获取平台商城市分组价格详细信息
     */
    @ApiOperation(value = "获得平台商城市分组价格详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{sysCode}")
    public CommonResult<PrdtSupplierGroupPriceRespVO> getInfo(@PathVariable("sysCode") Long sysCode) {
        PrdtSupplierGroupPrice prdtSupplierGroupPrice = prdtSupplierGroupPriceService.getPrdtSupplierGroupPrice(sysCode);
        return success(HutoolBeanUtils.toBean(prdtSupplierGroupPrice, PrdtSupplierGroupPriceRespVO.class));
    }

    /**
     * 分页查询平台商城市分组价格
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台商城市分组价格分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtSupplierGroupPricePageReqVO>> getPage(@Valid PrdtSupplierGroupPricePageReqVO pageReqVO) {
        return success(prdtSupplierGroupPriceService.getPrdtSupplierGroupPricePage(pageReqVO));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:price:add";
        /** 编辑 */
        public static final String EDIT = "product:price:edit";
        /** 删除 */
        public static final String DELETE = "product:price:remove";
        /** 列表 */
        public static final String LIST = "product:price:list";
        /** 查询 */
        public static final String GET = "product:price:query";
        /** 停用 */
        public static final String DISABLE = "product:price:disable";
        /** 启用 */
        public static final String ENABLE = "product:price:enable";
    }
}
