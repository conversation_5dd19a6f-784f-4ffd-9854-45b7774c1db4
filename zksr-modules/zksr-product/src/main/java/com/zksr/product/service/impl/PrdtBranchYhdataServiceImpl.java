package com.zksr.product.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LFUCache;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.Page;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.domain.dto.car.AppCarUnitDTO;
import com.zksr.common.core.domain.vo.openapi.CreateYhDataMatchReqVO;
import com.zksr.common.core.domain.vo.openapi.CreateYhDataMatchRespVO;
import com.zksr.common.core.domain.vo.openapi.receive.CreateYhDataReqVO;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StockUtil;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.config.CustomIdGenerator;
import com.zksr.common.elasticsearch.domain.EsYhProduct;
import com.zksr.common.elasticsearch.service.EsYhProductService;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.yhdata.vo.*;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataGroupRespVO;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataPageReqVO;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataRespVO;
import com.zksr.product.controller.yhdata.vo.YhSkuUnitVO;
import com.zksr.product.convert.yhdata.PrdtBranchYhdataConvert;
import com.zksr.product.domain.PrdtBranchYhdata;
import com.zksr.product.domain.po.YhMatchAreaItemPO;
import com.zksr.product.enums.YhMatchReason;
import com.zksr.product.enums.YhMatchState;
import com.zksr.product.mapper.PrdtBranchYhdataMapper;
import com.zksr.product.service.IPrdtBranchYhdataService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.BranchTransitQtyReqVO;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 门店批量要货Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@Slf4j
@Service
@SuppressWarnings("all")
public class PrdtBranchYhdataServiceImpl implements IPrdtBranchYhdataService {

    @Autowired
    private PrdtBranchYhdataMapper prdtBranchYhdataMapper;

    @Autowired
    private IProductCacheService productCacheService;

    @Autowired
    private RedisStockService stockService;

    @Autowired
    private EsYhProductService yhProductService;

    @Resource
    private OrderApi orderApi;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CustomIdGenerator customIdGenerator;

    @Override
    @Transactional
    //@DistributedLock(lockName = RedisLockConstants.LOCK_SAVE_YH_DATA, condition = "#reqVO.sysCode", leaseTime = 600L, tryLock = true)
    public CommonResult<CreateYhDataRespVO> saveYhData(CreateYhDataReqVO reqVO) {
        BranchDTO branch = productCacheService.getBranchDto(reqVO.getBranchId());
        if (ObjectUtil.isEmpty(branch)) {
            return CommonResult.error(PRDT_YH_DATA_BRANCH_ERR);
        }
        Long cnt = prdtBranchYhdataMapper.selectCountByBatchNo(reqVO.getYhBatchNo());
        if (cnt > 0) {
            // 返回数据
            return CommonResult.success(new CreateYhDataRespVO(branch.getSysCode(), reqVO.getYhBatchNo()));
        }
        // 从缓存获取要货数据
        String itemListKey = RedisConstants.YH_TEMP_LIST + reqVO.getYhBatchNo();
        List<CreateYhDataReqVO.YhDtl> itemList = redisService.getCacheList(itemListKey);
        // 验证重复
        Map<String, List<CreateYhDataReqVO.YhDtl>> keyMap = itemList.stream()
                .collect(Collectors.groupingBy(item -> StringUtils.format("{}:{}", item.getPosSourceNo(), item.getPosBarcode())));
        Set<String> keySet = keyMap.keySet();
        for (String key : keySet) {
            if (keyMap.get(key).size() > 1) {
                return CommonResult.error(PRDT_YH_DATA_DTL_REPEAT.getCode(), StringUtils.format("{}___{}", key, PRDT_YH_DATA_DTL_REPEAT.getMsg()));
            }
        }
        // 要货批次日
        Integer batchYmd = Integer.parseInt(DateUtil.format(reqVO.getReceiveTime(), DatePattern.PURE_DATE_PATTERN));

        // 数据保存
        List<PrdtBranchYhdata> saveList = new ArrayList<>();
        for (int i = 0; i < itemList.size(); i++) {
            CreateYhDataReqVO.YhDtl yhDtl = itemList.get(i);
            PrdtBranchYhdata yhdata = PrdtBranchYhdataConvert.INSTANCE.convertPO(yhDtl);
            yhdata.setSysCode(branch.getSysCode());
            yhdata.setPosYhBatchNo(reqVO.getYhBatchNo());
            yhdata.setAreaId(branch.getAreaId());
            yhdata.setBranchId(branch.getBranchId());
            yhdata.matchState(YhMatchState.UNDEFINED);
            yhdata.setBatchYmd(batchYmd);
            yhdata.setPosMaxLateTime(reqVO.getMaxLateTime());
            yhdata.setLineNum(i + 1);
            saveList.add(yhdata);
        }
        // 保存数据
        prdtBranchYhdataMapper.insertBatch(saveList);
        redisService.deleteObject(itemListKey);
        // 返回数据
        return CommonResult.success(new CreateYhDataRespVO(branch.getSysCode(), reqVO.getYhBatchNo()));
    }

    /**
     * 匹配需求
     * 1、以平台商角色对接开发补货接口；
     * 2、优先以商品编码匹配B2B系统的SKU（外部编码），匹配结果以area_item_id为准；
     * 3、以商品编码匹配商品失败（匹配不到或者无库存），则根据国条匹配商品，可能会匹配到多个入驻商的商品，需要设定优先级排序（加个入驻商自动报货优先级，默认值为1）
     * 4、单位的处理，pos传过来的数量为最小库存单位的单位，B2B系统也应该按最小单位进行匹配，如果B2B系统没有上架最小单位，则依次按照中单位，大单位换算小单位匹配需求。
     * 5、查询接口，补货单商品全部匹配完成才通过查询接口返回pos;
     * 6、针对批量要货，新增接口，前期阶段该接口加锁，只允许一个请求调用该接口；
     * 7、补货订单支付成功后，补货数据表反写订单数据；
     * 8、补货数据表按生产环境实际可按年、月做分表
     * 9、补货单数据匹配完成之后存入es，用于满足前端查询、搜索需求
     * 10、补货单的数据修改同步修改es和mysql
     * @param createYhDataRespVO 要货批次保存结果
     */
    @Override
    @Transactional
    //@DistributedLock(lockName = RedisLockConstants.LOCK_MATCH_YH_DATA, condition = "#createYhDataRespVO.sysCode", leaseTime = 600L, tryLock = true)
    public void matchYhBatch(CreateYhDataRespVO createYhDataRespVO) {
        List<PrdtBranchYhdata> yhdataList = prdtBranchYhdataMapper.selectListByYhBatchNo(createYhDataRespVO.getYhBatchNo());
        if (yhdataList.isEmpty()) {
            log.error("匹配要货批次数据结束, 没有查询到要货数据, yhBatchNo={}", createYhDataRespVO.getYhBatchNo());
            return;
        }

        // 优先通过posSourceNo 匹配 => 商品SKU, source_no
        // 再通过条码去检索
        // 过滤无库存, 未上架商品
        // 判断优先入驻商信息
        // 优先使用小单位 > 中单位 > 大单位
        // 入驻商栈内缓存
        LFUCache<Long, SupplierDTO> supplierCache = CacheUtil.newLFUCache(0);
        // 转换ES数据
        List<EsYhProduct> esYhProducts = new ArrayList<>();
        for (PrdtBranchYhdata yhdata : yhdataList) {
            // 优先通过posSourceNo 匹配 => 商品SKU, source_no
            Result result = getResult(yhdata);
            // 如果一个都没有
            if (Objects.isNull(result)) {
                yhdata.matchState(YhMatchState.FAIL);
                yhdata.failReason(YhMatchReason.STOCK_DEFICIENCY);
                continue;
            }
            // 设置匹配结果
            yhdata.matchState(YhMatchState.SUCCESS);
            yhdata.setMallMatchSkuId(result.areaItemPO.getSkuId());
            yhdata.setMallAreaItemId(result.areaItemPO.getAreaItemId());
            yhdata.setMallUnitType(result.areaItemPO.getUnitSize());
            yhdata.setSourceYhQty(yhdata.getPosSuggestQty());

            // 要货单位如果和匹配单位不一致
            if (result.yhItemVO != result.areaItemPO) {
                // 如果要货单位和匹配单位不一致, 先转成最小单位数量 (要货单位转换比例 * 要货数量) / 匹配单位转换比例
                yhdata.setPosSuggestQty(
                        StockUtil.stockDivide(
                                StockUtil.stockMultiply(yhdata.getPosSuggestQty(), result.yhItemVO.getStockConvert()),
                                result.areaItemPO.getStockConvert()
                        ).intValue()
                );
            } else {
                // 根据匹配单位, 转换实际单位数量
                yhdata.setPosSuggestQty(yhdata.getPosSuggestQty());
            }

            // 上次补货数量
            PrdtBranchYhdata lastYhData = prdtBranchYhdataMapper.selectLastYhDataBySourceNo(yhdata.getPosSourceNo(), yhdata.getSysCode());
            if (Objects.nonNull(lastYhData)) {
                yhdata.setLastTime(lastYhData.getCreateTime());
                yhdata.setLastSubmitQty(lastYhData.getPosSuggestQty());
            }

            // 在途数量
            Long transitQty = orderApi.getBranchTransitQty(new BranchTransitQtyReqVO(yhdata.getBranchId(), yhdata.getMallAreaItemId())).getCheckedData();
            yhdata.setTransitQty(transitQty);

            // 重复补货
            List<EsYhProduct> yhProductList = yhProductService.getYhProductByBranchItemIdList(yhdata.getBranchId(), DateUtil.parse(String.valueOf(yhdata.getBatchYmd()), DatePattern.PURE_DATE_PATTERN), ListUtil.toList(result.areaItemPO.getAreaItemId()));
            Map<String, EsYhProduct> skuUnitMap = yhProductList.stream().collect(Collectors.toMap(EsYhProduct::unitKey, item -> item));
            if (skuUnitMap.containsKey(yhdata.getUnitKey())) {
                EsYhProduct oldProduct = skuUnitMap.get(yhdata.getUnitKey());
                // 增加数量
                // 2025年3月3日17:11:03, 决定不累计要货数量, 而采用直接覆盖, 如果上次的要货数据还没有下单, 又要货, 直接用新的要货覆盖
                // yhdata.setPosSuggestQty(yhdata.getPosSuggestQty() + oldProduct.getPosSuggestQty());
            }

            // 验证有效要货库存
            // 是否超过匹配单位最大库存数
            if (result.areaItemPO.getStock() < yhdata.getPosSuggestQty()) {
                // 设置未最大要货库存
                yhdata.setPosSuggestQty(result.areaItemPO.getStock().intValue());
            }

            // 校对起订组数
            Long jumpOq = result.areaItemPO.getJumpOq();
            if (Objects.nonNull(jumpOq) && jumpOq > 0) {
                long buchang = yhdata.getPosSuggestQty() % jumpOq;
                if (buchang > 0) {
                    // 补偿起订组数
                    yhdata.setPosSuggestQty(yhdata.getPosSuggestQty() +  (int) (jumpOq - buchang));
                }
            }

            // 有效库存小于 0, 或者还没达到起订量
            if ( Objects.nonNull(result.areaItemPO.getMinOq()) && result.areaItemPO.getMinOq() > yhdata.getPosSuggestQty() ) {
                yhdata.matchState(YhMatchState.FAIL);
                yhdata.failReason(YhMatchReason.STOCK_DEFICIENCY);
                continue;
            }
            // 转换ES数据
            EsYhProduct esProductVO = PrdtBranchYhdataConvert.INSTANCE.convertESProductVO(yhdata, result.areaItemPO.getAreaItemPO());
            if (Objects.nonNull(yhdata.getLastTime())) {
                esProductVO.setLastTime(DateUtil.formatDateTime(yhdata.getLastTime()));
                if (skuUnitMap.containsKey(yhdata.getUnitKey())) {
                    EsYhProduct oldProduct = skuUnitMap.get(yhdata.getUnitKey());
                    esProductVO.setYhId(oldProduct.getYhId());
                }
            }
            esYhProducts.add(esProductVO);
        }
        prdtBranchYhdataMapper.updateBatch(yhdataList);
        // 保存ES数据
        yhProductService.insertBatch(esYhProducts);
    }

    private Result getResult(PrdtBranchYhdata yhdata) {
        List<YhMatchAreaItemPO> matchAreaItemPOList = prdtBranchYhdataMapper.matchSkuSourceNo(yhdata.getPosSourceNo(), yhdata.getSysCode(), yhdata.getAreaId());
        // 再通过条码去检索
        matchAreaItemPOList.addAll(prdtBranchYhdataMapper.matchSkuBarcode(yhdata.getPosBarcode(), yhdata.getSysCode(), yhdata.getAreaId()));
        // 没匹配到
        if (matchAreaItemPOList.isEmpty()) {
            yhdata.matchState(YhMatchState.FAIL);
            yhdata.failReason(YhMatchReason.UNDEFINED);
            return null;
        }
        // 将查询的数据 去重以后 打宽
        List<YhSkuUnitVO> barcodeSkuList = new HashSet<>(matchAreaItemPOList)
            .stream()
            .map(YhMatchAreaItemPO::getSkuUnit)
            .flatMap(Collection::stream)
            .peek(item -> {
                SpuDTO spuDTO = productCacheService.getSpuDTO(item.getSpuId());
                SkuDTO skuDTO = productCacheService.getSkuDTO(item.getSkuId());
                item.setMinOq(skuDTO.getMinOq(item.getUnitSize()));
                item.setJumpOq(skuDTO.getJumpOq(item.getUnitSize()));
                item.setMaxOq(skuDTO.getMaxOq(item.getUnitSize()));
                // 当前条码有效库存
                long validateStock = StockUtil.stockDivide(stockService.getSurplusSaleQtyBigDecimal(item.getSkuId()), spuDTO.stockConvert(item.getUnitSize())).longValue();
                item.setStock(validateStock);
                item.setStockConvert(spuDTO.stockConvert(item.getUnitSize()));

                // 入驻商要货优先级
                SupplierDTO supplierDTO = productCacheService.getSupplierDTO(spuDTO.getSupplierId());
                if (ObjectUtil.isNotEmpty(supplierDTO) && Objects.nonNull(supplierDTO.getSupplierYhSort())) {
                    item.setSupplierYhSort(supplierDTO.getSupplierYhSort());
                }
            })
            .collect(Collectors.toList());

        // 优先级最高
        barcodeSkuList.sort(Comparator.comparing(YhSkuUnitVO::getSupplierYhSort).reversed());
        for (YhSkuUnitVO skuUnitVO : barcodeSkuList) {
            // 优先级1, 条码对上了, 上架, 并且还有库存
            if (yhdata.getPosBarcode().equals(skuUnitVO.getBarcode())
                    && skuUnitVO.getStock() > NumberPool.LONG_ZERO
                    && skuUnitVO.getShelfStatus() == NumberPool.INT_ONE) {
                return new Result(skuUnitVO, skuUnitVO);
            }
        }
        // 包含条码的SKU商品集合
        Map<Long, List<YhSkuUnitVO>> skuMap = barcodeSkuList.stream().collect(Collectors.groupingBy(YhSkuUnitVO::getSkuId));
        for (Long skuId : skuMap.keySet()) {
            List<YhSkuUnitVO> skuUnitVOS = skuMap.get(skuId);
            // 获得要货条码, 在我们系统的单位信息
            List<YhSkuUnitVO> barcodeList = skuUnitVOS.stream().filter(item -> item.getBarcode().equals(yhdata.getPosBarcode())).collect(Collectors.toList());
            barcodeList.sort(Comparator.comparing(YhSkuUnitVO::getUnitSize).reversed());
            YhSkuUnitVO yhbarcode = barcodeList.get(0);
            switch (yhbarcode.getUnitSize()) {
                case NumberPool.INT_ONE:
                {
                    List<YhSkuUnitVO> matchList = skuUnitVOS
                            .stream()
                            .filter(item -> item.getUnitSize() > yhbarcode.getUnitSize())
                            .filter(item -> item.getShelfStatus() == NumberPool.INT_ONE)
                            .collect(Collectors.toList());
                    matchList.sort(Comparator.comparing(YhSkuUnitVO::getUnitSize));
                    for (YhSkuUnitVO unitVO : matchList) {
                        // 只要>0, 说明当前单位就有至少有一份库存
                        if (unitVO.getStock() > 0) {
                            return new Result(yhbarcode, unitVO);
                        }
                    }
                }
                break;
                case NumberPool.INT_THREE:
                {
                    // 大单位 > 中单位 > 小单位
                    List<YhSkuUnitVO> matchList = skuUnitVOS.stream()
                            .filter(item -> item.getUnitSize() < yhbarcode.getUnitSize())
                            .filter(item -> item.getShelfStatus() == NumberPool.INT_ONE)
                            .collect(Collectors.toList());
                    matchList.sort(Comparator.comparing(YhSkuUnitVO::getUnitSize).reversed());
                    for (YhSkuUnitVO unitVO : matchList) {
                        // 只要>0, 说明当前单位就有至少有一份库存
                        if (unitVO.getStock() > 0) {
                            return new Result(yhbarcode, unitVO);
                        }
                    }
                }
                break;
                case NumberPool.INT_TWO:
                {
                    // 中单位 > 大单位 > 小单位
                    List<YhSkuUnitVO> matchList = skuUnitVOS
                            .stream()
                            .filter(item -> item.getShelfStatus() == NumberPool.INT_ONE)
                            .collect(Collectors.toList());
                    matchList.sort(Comparator.comparing(YhSkuUnitVO::getUnitSize).reversed());
                    for (YhSkuUnitVO unitVO : matchList) {
                        // 只要>0, 说明当前单位就有至少有一份库存
                        if (unitVO.getStock() > 0) {
                            return new Result(yhbarcode, unitVO);
                        }
                    }
                }
                break;
                default:
                    break;
            }
        }
        return null;
    }

    @Override
    public CommonResult<CreateYhDataMatchRespVO> getBatchYhRes(CreateYhDataMatchReqVO reqVO) {
        CreateYhDataMatchRespVO respVO = new CreateYhDataMatchRespVO(reqVO.getYhBatchNo());
        // 获取补货数据
        List<PrdtBranchYhdata> yhdataList = prdtBranchYhdataMapper.selectListByYhBatchNo(reqVO.getYhBatchNo());
        if (yhdataList.isEmpty()) {
            // 数据还没处理, 可能还在消息队列里面, 没有保存到数据库
            respVO.setBatchState(NumberPool.INT_ZERO);
            return CommonResult.success(respVO);
        }

        // 轮询处理
        for (PrdtBranchYhdata yhdata : yhdataList) {

            // 构建返回数据
            CreateYhDataMatchRespVO.YhDtl yhDtl = new CreateYhDataMatchRespVO.YhDtl();
            yhDtl.setLineNum(yhdata.getLineNum());
            yhDtl.setMatchRes(YhMatchReason.formValue(yhdata.getFailReason()).getName());
            yhDtl.setMatchState(yhdata.getMatchState());

            // 只有匹配成功的, 才返回价格库存信息
            if (Objects.nonNull(yhdata.getMallMatchSkuId())) {
                SkuDTO skuDTO = productCacheService.getSkuDTO(yhdata.getMallMatchSkuId());
                SpuDTO spuDTO = productCacheService.getSpuDTO(skuDTO.getSpuId());
                yhDtl.setCurrentPrice(skuDTO.getMarkPrice());
                // 返回匹配到的有效库存
                // 库存已经根据实际要货单位处理过的了
                yhDtl.setCurrentAvailableQty(yhdata.getPosSuggestQty().longValue());
                yhDtl.setStockConvert(spuDTO.stockConvert(yhdata.getMallUnitType()));
                yhDtl.setPosBarcode(yhdata.getPosBarcode());
            }
            respVO.getDtlList().add(yhDtl);
        }

        return CommonResult.success(respVO);
    }

    @Override
    public Map<Long, List<Long>> getBatchSupplierSaleClass(YhBatchSupplierSaleClassReqVO reqVO) {
        return yhProductService.getBatchSupplierSaleClass(reqVO.getBatchDate(), reqVO.getBranchId());
    }

    @Override
    public PageResult<YhBatchItemVO> getEsBatchItemList(YhBatchListReqVO reqVO) {
        // 获取ES的基础数据
        PageResult<EsYhProduct> pageResult = yhProductService.pageList(PrdtBranchYhdataConvert.INSTANCE.convertESSearchDTO(reqVO));
        return PrdtBranchYhdataConvert.INSTANCE.convertESPage(pageResult);
    }

    @Override
    @Transactional
    @DistributedLock(lockName = RedisLockConstants.LOCK_BRANCH_YH_DATA, condition = "#reqVO.branchId", leaseTime = 60L)
    public CommonResult<Boolean> branchBatchYhSave(YhBatchSaveReqVO reqVO) {
        BranchDTO branchDTO = productCacheService.getBranchDto(reqVO.getBranchId());
        // 删除
        List<Long> deleteYhIds = new ArrayList<>();
        // 修改
        List<PrdtBranchYhdata> updateList = new ArrayList<>();
        // 新增
        List<PrdtBranchYhdata> saveList = new ArrayList<>();
        for (YhBatchSaveReqVO.YhDtl yhDtl : reqVO.getYhDtls()) {
            // 删除
            if (yhDtl.getIsDelete()) {
                deleteYhIds.add(yhDtl.getYhId());
                continue;
            }
            // 修改
            Map<String, List<EsYhProduct>> skuMap = yhProductService.getYhProductByBranchSkuIdList(
                    reqVO.getBranchId(),
                    reqVO.getBatchDate(),
                    reqVO.getYhDtls()
                            .stream()
                            .map(YhBatchSaveReqVO.YhDtl::getSkuId)
                            .collect(Collectors.toList())
            ).stream().collect(Collectors.groupingBy(EsYhProduct::unitSkuKey));
            if (skuMap.containsKey(yhDtl.getUnitSkuKey())) {
                // 更新
                EsYhProduct product = skuMap.get(yhDtl.getUnitSkuKey()).get(0);
                PrdtBranchYhdata update = new PrdtBranchYhdata();
                update.setYhId(product.getYhId());
                if (yhDtl.getOverProductNum()) {
                    update.setPosSuggestQty(yhDtl.getProductNum());
                } else {
                    update.setPosSuggestQty(yhDtl.getProductNum() + product.getPosSuggestQty());
                }
                update.setChecked(yhDtl.getSelected() ? NumberPool.INT_ONE : NumberPool.INT_ZERO);
                updateList.add(update);
            } else {
                AreaItemDTO areaItem = productCacheService.getAreaItemDTO(yhDtl.getItemId());
                // 新增
                PrdtBranchYhdata yhdata = new PrdtBranchYhdata();
                yhdata.setBranchId(reqVO.getBranchId());
                yhdata.setMallAreaItemId(yhDtl.getItemId());
                yhdata.setMallUnitType(yhDtl.getUnitSize());
                yhdata.setMallMatchSkuId(yhDtl.getSkuId());
                yhdata.setPosSuggestQty(yhDtl.getProductNum());
                yhdata.setSysCode(branchDTO.getSysCode());
                yhdata.setBatchYmd(Integer.parseInt(DateUtil.format(reqVO.getBatchDate(), DatePattern.PURE_DATE_PATTERN)));
                yhdata.setChecked(NumberPool.INT_ONE);
                yhdata.setItemType(areaItem.getItemType());
                saveList.add(yhdata);
            }
        }
        // 删除数据
        if (!deleteYhIds.isEmpty()) {
            //prdtBranchYhdataMapper.delete(deleteYhIds);
            yhProductService.deleteById(deleteYhIds);
        }
        // 更新数据
        if (!updateList.isEmpty()) {
            //prdtBranchYhdataMapper.updateBatch(updateList);
            yhProductService.updateBatch(PrdtBranchYhdataConvert.INSTANCE.convertESPO(updateList));
        }
        // 新增数据
        if (!saveList.isEmpty()) {
            //prdtBranchYhdataMapper.insertBatch(saveList);
            // 保存数据
            saveBranchEsYhdata(saveList);
        }
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 过滤上架异常
     */
    @NotNull
    private List<YhMatchAreaItemPO> matchStock(List<YhMatchAreaItemPO> matchAreaItemPOList) {
        return matchAreaItemPOList.stream().filter(item -> {
            // 验证库存
            Long surplusSaleQty = stockService.getSurplusSaleQty(item.getSkuId());
            if (surplusSaleQty < NumberPool.INT_ONE) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
    }

    /**
     * 过滤库存异常
     */
    @NotNull
    private static List<YhMatchAreaItemPO> matchRelease(List<YhMatchAreaItemPO> matchAreaItemPOList) {
        return matchAreaItemPOList.stream().filter(item -> {
            // 一个都没上架
            if (item.getMinShelfStatus() + item.getMidShelfStatus() + item.getLargeShelfStatus() < NumberPool.INT_ONE) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtBranchYhdataRespVO> getPrdtBranchYhdataPage(PrdtBranchYhdataPageReqVO pageReqVO) {
        Page<PrdtBranchYhdataRespVO> page = PageUtils.startPage(pageReqVO);
        List<PrdtBranchYhdataRespVO> respVOList = prdtBranchYhdataMapper.selectListExt(pageReqVO);
        respVOList.forEach(item -> {
            SupplierDTO supplierDTO = productCacheService.getSupplierDTO(item.getSupplierId());
            if (Objects.nonNull(supplierDTO)) {
                item.setSupplierName(supplierDTO.getSupplierName());
            }
            // 转换sku名称
            item.setProperties(PropertyAndValDTO.getProperties(item.getProperties()));
        });
        return PageResult.result(page, respVOList);
    }

    @Override
    public PageResult<PrdtBranchYhdataGroupRespVO> batchTotalPageList(PrdtBranchYhdataPageReqVO pageReqVO) {
        Page<PrdtBranchYhdataGroupRespVO> page = PageUtils.startPage(pageReqVO);
        List<PrdtBranchYhdataGroupRespVO> groupRespVOS = prdtBranchYhdataMapper.selectBatchTotalPageList(pageReqVO);
        for (PrdtBranchYhdataGroupRespVO respVO : groupRespVOS) {
            BranchDTO branchDTO = productCacheService.getBranchDto(respVO.getBranchId());
            if (Objects.nonNull(branchDTO)) {
                respVO.setBranchName(branchDTO.getBranchName());
                respVO.setBranchContractPhone(branchDTO.getContactPhone());
            }
        }
        return PageResult.result(page, groupRespVOS);
    }

    @Override
    public void removeBatchYh(YhBatchRemoveReqVO reqVO) {
        log.info("删除要货单数据, orderNo={}, 详情数据={}", reqVO.getBusinessNo(), JSON.toJSON(reqVO.getYhIdList()));
        if (reqVO.getYhIdList().isEmpty()) {
            return;
        }
        //prdtBranchYhdataMapper.delete(reqVO.getYhIdList());
        yhProductService.deleteById(reqVO.getYhIdList());
    }

    @Override
    public void initData() {
        yhProductService.createIndex();
        // 轮询刷新数据到ES
        Long minId = NumberPool.LOWER_GROUND_LONG;
        for (;;) {
            List<PrdtBranchYhdata> yhdataList = prdtBranchYhdataMapper.selectAllForListData(minId);
            if (yhdataList.isEmpty()) {
                break;
            }
            saveBranchEsYhdata(yhdataList);
            minId = yhdataList.get(yhdataList.size() - 1).getYhId();
        }
    }


    private void saveBranchEsYhdata(List<PrdtBranchYhdata> saveList) {
        List<EsYhProduct> yhProductList = saveList.stream().map(item -> {
            EsYhProduct esYhProduct = PrdtBranchYhdataConvert.INSTANCE.convertESPO(item);
            esYhProduct.setYhId(customIdGenerator.nextId());
            esYhProduct.setAreaItemId(item.getMallAreaItemId());
            if (Objects.nonNull(item.getLastTime())) {
                esYhProduct.setLastTime(DateUtil.formatDateTime(item.getLastTime()));
            }
            // 组合商品渲染
            if (item.isSpuCombine()) {
                AreaItemDTO areaItemDTO = productCacheService.getAreaItemDTO(item.getMallAreaItemId());
                SpuCombineDTO combineDTO = productCacheService.getSpuCombineDTO(areaItemDTO.getSpuCombineId());
                esYhProduct.setSupplierId(combineDTO.getSupplierId());
                esYhProduct.setThreeSaleClassId(areaItemDTO.getAreaClassId());
                esYhProduct.setSkuId(NumberPool.LOWER_GROUND_LONG);
                esYhProduct.setSpuId(NumberPool.LOWER_GROUND_LONG);
                esYhProduct.setSpuName(combineDTO.getSpuCombineName());
                esYhProduct.setPosBarcode(combineDTO.getSpuCombineNo());
            }
            return esYhProduct;
        }).collect(Collectors.toList());

        // 普通商品数据, 富渲染
        List<Long> normallItemList = saveList.stream().filter(PrdtBranchYhdata::notSpuCombine).map(PrdtBranchYhdata::getMallAreaItemId).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(normallItemList)) {
            List<YhMatchAreaItemPO> matchAreaItemPOList = prdtBranchYhdataMapper.matchItemId(normallItemList);
            Map<Long, YhMatchAreaItemPO> itemMap = matchAreaItemPOList.stream().collect(Collectors.toMap(YhMatchAreaItemPO::getAreaItemId, item -> item));
            for (EsYhProduct product : yhProductList) {
                if (!itemMap.containsKey(product.getAreaItemId())) {
                    continue;
                }
                YhMatchAreaItemPO areaItemPO = itemMap.get(product.getAreaItemId());
                product.setSupplierId(areaItemPO.getSupplierId());
                product.setThreeSaleClassId(areaItemPO.getThreeSaleClassId());
                product.setSkuId(areaItemPO.getSkuId());
                product.setSpuId(areaItemPO.getSpuId());
                product.setSpuName(areaItemPO.getSpuName());
                product.setPosBarcode(areaItemPO.getBarcode());
            }
        }
        yhProductService.insertBatch(yhProductList);
    }

    private static class Result {
        public final YhSkuUnitVO yhItemVO;
        public final YhSkuUnitVO areaItemPO;

        public Result(YhSkuUnitVO yhItemVO, YhSkuUnitVO areaItemPO) {
            this.yhItemVO = yhItemVO;
            this.areaItemPO = areaItemPO;
        }
    }
}
