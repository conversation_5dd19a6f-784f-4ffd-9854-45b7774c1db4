package com.zksr.product.convert.platform;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.platform.excel.ProductPlarformImportExcel;
import com.zksr.product.controller.platform.vo.PrdtPlatformSpuRespVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformSpuSaveReqVO;
import com.zksr.product.domain.PrdtPlatformSpu;
import com.zksr.product.domain.PrdtSpu;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
* 平台商品库-商品SPU 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-06-21
*/
@Mapper
public interface PrdtPlatformSpuConvert {

    PrdtPlatformSpuConvert INSTANCE = Mappers.getMapper(PrdtPlatformSpuConvert.class);

    PrdtPlatformSpuRespVO convert(PrdtPlatformSpu prdtPlatformSpu);

    PrdtPlatformSpu convert(PrdtPlatformSpuSaveReqVO prdtPlatformSpuSaveReq);

    PageResult<PrdtPlatformSpuRespVO> convertPage(PageResult<PrdtPlatformSpu> prdtPlatformSpuPage);

    PrdtPlatformSpu convertPartnerSpu(PrdtSpu prdtSpu);

    @Mappings({
            @Mapping(source = "itemData.spuNo", target = "spuNo"),
            @Mapping(source = "itemData.spuName", target = "spuName"),
            @Mapping(source = "itemData.brandName", target = "brandName"),
            @Mapping(source = "itemData.categoryName", target = "catgoryName"),
            @Mapping(source = "itemData.midSize", target = "midSize"),
            @Mapping(source = "itemData.largeSize", target = "largeSize"),
    })
    @BeanMapping(ignoreByDefault = true)
    void convertImport(@MappingTarget PrdtPlatformSpu prdtPlatformSpu, ProductPlarformImportExcel itemData);
}