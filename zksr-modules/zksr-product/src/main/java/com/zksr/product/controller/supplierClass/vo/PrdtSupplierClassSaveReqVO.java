package com.zksr.product.controller.supplierClass.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 入驻商-平台商管理分类 关联关系对象 prdt_supplier_class
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Data
@ApiModel("入驻商-平台商管理分类 关联关系 - prdt_supplier_class分页 Request VO")
public class PrdtSupplierClassSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 平台商管理分类id;平台商管理分类id */
    @Excel(name = "平台商管理分类id;平台商管理分类id")
    @ApiModelProperty(value = "平台商管理分类id;平台商管理分类id", required = true,   example = "示例值")
    private Long catgoryId;

    /** 入驻商id;入驻商id */
    @Excel(name = "入驻商id;入驻商id")
    @ApiModelProperty(value = "入驻商id;入驻商id", required = true,   example = "示例值")
    private Long supplierId;

    /** 平台商id;平台商id */
    @Excel(name = "平台商id;平台商id")
    @ApiModelProperty(value = "平台商id;平台商id", required = true,   example = "示例值")
    private Long sysCode;

}
