package com.zksr.product.controller.areaClass;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.areaClass.excel.ProductAreaClassImportExcel;
import com.zksr.product.controller.areaClass.vo.*;
import com.zksr.product.domain.excel.BrandImportExcel;
import com.zksr.product.domain.excel.CategoryImportExcel;
import com.zksr.product.service.IPrdtAreaClassService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.IOException;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 城市展示分类Controller
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Api(tags = "管理后台 - 城市展示分类接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/areaClass")
public class PrdtAreaClassController {
    @Autowired
    private IPrdtAreaClassService prdtAreaClassService;

    /**
     * @Description: 获取城市展示分类子级列表
     * @Param: Long areaClassId
     * @return: List<PrdtAreaClassRespVO>
     * @Author: liuxingyu
     * @Date: 2024/4/13 9:22
     */
    @ApiOperation(value = "获取城市展示分类子级列表", httpMethod = HttpMethod.GET)
    @GetMapping("/getAreaCategorySubLevelList")
    public CommonResult<PageResult<PrdtAreaClassRespVO>> getAreaCategorySubLevelList(PrdtAreaClassPageReqVO reqVO) {
        return success(prdtAreaClassService.getAreaClassDetail(reqVO));
    }

    /**
     * @Description: 获取所有城市展示分类信息
     * @Param: Long areaClassId
     * @return: CommonResult<PrdtAreaClassRespVO>
     * @Author: liuxingyu
     * @Date: 2024/3/23 9:04
     */
    @ApiOperation(value = "获取所有城市展示分类信息", httpMethod = HttpMethod.GET)
    @GetMapping("/getAreaClassList/getList")
    public CommonResult<List<PrdtAreaClassRespVO>> getAreaClassList(@RequestParam(required = false) Long areaClassId,
                                                                    @RequestParam(required = false) Long areaId,
                                                                    @RequestParam(required = false) String status,
                                                                    @RequestParam(required = false) Integer level) {
        return success(HutoolBeanUtils.toBean(prdtAreaClassService.getAreaClassList(areaClassId, areaId, status, level), PrdtAreaClassRespVO.class));
    }

    /**
     * 新增城市展示分类
     * !@运营商展示分类  - 新增
     */
    @ApiOperation(value = "新增城市展示分类", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "城市展示分类", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtAreaClassSaveReqVO createReqVO) {
        return success(prdtAreaClassService.insertPrdtAreaClass(createReqVO, SecurityUtils.getLoginUser().getSysCode()));
    }

    /**
     * 修改城市展示分类
     */
    @ApiOperation(value = "修改城市展示分类", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "城市展示分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtAreaClassSaveReqVO updateReqVO) {
        UpdateAreaClassRespEvent updateAreaClassRespEvent = prdtAreaClassService.updatePrdtAreaClass(updateReqVO);
        prdtAreaClassService.reloadCache(updateAreaClassRespEvent);
        return success(true);
    }

    /**
     * 删除城市展示分类
     */
    @ApiOperation(value = "删除城市展示分类", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "城市展示分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{areaClassIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] areaClassIds) {
        prdtAreaClassService.deletePrdtAreaClassByAreaClassIds(areaClassIds);
        return success(true);
    }

    /**
     * 获取城市展示分类详细信息
     */
    @ApiOperation(value = "获得城市展示分类详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{areaClassId}")
    public CommonResult<PrdtAreaClassRespVO> getInfo(@PathVariable("areaClassId") Long areaClassId) {
        return success(prdtAreaClassService.getPrdtAreaClass(areaClassId));
    }

    /**
     * 分页查询城市展示分类
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得城市展示分类分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(dcAlias = "`prdt_area_class`")
    public CommonResult<PageResult<PrdtAreaClassRespVO>> getPage(@Valid PrdtAreaClassPageReqVO pageReqVO) {
        return success(prdtAreaClassService.getPrdtAreaClassPage(pageReqVO));
    }

    /**
     * 获取本地展示分类选中数据 (用于选中回显)
     *
     * @param saleClassIds 展示分类ID集合
     * @return 展示分类集合
     */
    @PostMapping("/getSelectedBatchInfo")
    @ApiOperation(value = "批量获取分类简略信息", httpMethod = "POST")
    public CommonResult<List<PrdtAreaClassRespVO>> getSelectedBatchInfo(@Valid @Size(min = NumberPool.INT_ONE) @RequestBody List<Long> saleClassIds) {
        return success(prdtAreaClassService.getSelectedAreaClass(saleClassIds));
    }

    /**
     * 一键批量删除城市展示分类
     */
    @ApiOperation(value = "一键批量删除城市展示多级分类", httpMethod = "DELETE", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "城市展示分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeMultilevelClasses/{areaClassIds}")
    public CommonResult<Boolean> removeMultilevelClasses(@PathVariable Long[] areaClassIds) {
        prdtAreaClassService.removeMultilevelClasses(areaClassIds);
        return success(true);
    }

    @ApiOperation(value = "导入运营商展示类别", httpMethod = HttpMethod.POST,notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORT)
    @Log(title = "导入运营商展示类别", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/importData")
    public CommonResult<String> importData(MultipartFile file) throws Exception
    {
        ExcelUtil<ProductAreaClassImportExcel> util = new ExcelUtil<>(ProductAreaClassImportExcel.class);
        List<ProductAreaClassImportExcel> areaClassList = util.importExcel(file.getInputStream(),1);
        String message = prdtAreaClassService.importAreaClassData(areaClassList);
        return success(message);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载运营商展示类别信息模板", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<ProductAreaClassImportExcel> util = new ExcelUtil<>(ProductAreaClassImportExcel.class);
        String instructions = "填表说明：\n" +
                "1、商品类别名称：必填项，支持文字和数字输入，最多仅限20个字符，超出字符导入会失败\n" +
                "2、区域编号：必填项，需要和系统的区域编号保持一致，否则导入不成功\n" +
                "注意：一级类别为主类目，二级属于一级的子类，三级为二级的子类，导入进去后类别默认为启用状态；\n" +
                "一级类别、二级类别、三级类别类别名不可重复，一级类别名称和二级类别名称相同则只会生成一级类别；\n" +
                "本地商品展示类别是根据城市来展示。所以导入的本地商品展示类别，需要根据区域编号去匹配城市去导入；";
        util.importTemplateExcel(response, "运营商展示类别信息导入", StringUtils.EMPTY, instructions);
    }

    /**
     * 修改城市展示分类
     */
    @ApiOperation(value = "城市展示类别复制", httpMethod = "POST")
    @Log(title = "城市展示类别复制", businessType = BusinessType.INSERT)
    @PostMapping("/copyAreaClass")
    public CommonResult<Boolean> copyAreaClass(@Valid @RequestBody PrdtAreaClassCopyReqVO vo) {
        prdtAreaClassService.copyAreaClass(vo);
        return success(true);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "product:areaClass:add";
        /**
         * 编辑
         */
        public static final String EDIT = "product:areaClass:edit";
        /**
         * 删除
         */
        public static final String DELETE = "product:areaClass:remove";
        /**
         * 列表
         */
        public static final String LIST = "product:areaClass:list";
        /**
         * 查询
         */
        public static final String GET = "product:areaClass:query";
        /**
         * 停用
         */
        public static final String DISABLE = "product:areaClass:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "product:areaClass:enable";
        /**
         * 导入
         */
        public static final String IMPORT = "product:areaClass:import";
    }
}
