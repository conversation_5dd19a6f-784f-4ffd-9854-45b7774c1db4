package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineDtlRespVO;
import com.zksr.product.domain.PrdtSpuCombineDtl;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineDtlPageReqVO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineDtlSaveReqVO;

import java.util.List;

/**
 * 组合商品详情Service接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface IPrdtSpuCombineDtlService {

    /**
     * 新增组合商品详情
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtSpuCombineDtl(@Valid PrdtSpuCombineDtlSaveReqVO createReqVO);

    /**
     * 修改组合商品详情
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtSpuCombineDtl(@Valid PrdtSpuCombineDtlSaveReqVO updateReqVO);

    /**
     * 删除组合商品详情
     *
     * @param sysCode 平台商id
     */
    public void deletePrdtSpuCombineDtl(Long sysCode);

    /**
     * 批量删除组合商品详情
     *
     * @param sysCodes 需要删除的组合商品详情主键集合
     * @return 结果
     */
    public void deletePrdtSpuCombineDtlBySysCodes(Long[] sysCodes);

    /**
     * 获得组合商品详情
     *
     * @param spuCombineId 组合商品ID
     * @return 组合商品详情
     */
    public List<PrdtSpuCombineDtlRespVO> getPrdtSpuCombineDtl(Long spuCombineId);

    /**
     * 获得组合商品详情分页
     *
     * @param pageReqVO 分页查询
     * @return 组合商品详情分页
     */
    PageResult<PrdtSpuCombineDtl> getPrdtSpuCombineDtlPage(PrdtSpuCombineDtlPageReqVO pageReqVO);

}
