package com.zksr.product.convert.platform;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtPlatformProperty;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertyRespVO;
import com.zksr.product.controller.platform.vo.PrdtPlatformPropertySaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.product.domain.PrdtProperty;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 规格名称 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-06-21
*/
@Mapper
public interface PrdtPlatformPropertyConvert {

    PrdtPlatformPropertyConvert INSTANCE = Mappers.getMapper(PrdtPlatformPropertyConvert.class);

    PrdtPlatformPropertyRespVO convert(PrdtPlatformProperty prdtPlatformProperty);

    PrdtPlatformProperty convert(PrdtPlatformPropertySaveReqVO prdtPlatformPropertySaveReq);

    PageResult<PrdtPlatformPropertyRespVO> convertPage(PageResult<PrdtPlatformProperty> prdtPlatformPropertyPage);

    PrdtPlatformProperty convertPartnerProperty(PrdtProperty property);
}