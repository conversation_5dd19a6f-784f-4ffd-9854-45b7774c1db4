package com.zksr.product.convert.combine;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtSpuCombineDtl;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineDtlRespVO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineDtlSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 组合商品详情 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-12-31
*/
@Mapper
public interface PrdtSpuCombineDtlConvert {

    PrdtSpuCombineDtlConvert INSTANCE = Mappers.getMapper(PrdtSpuCombineDtlConvert.class);

    PrdtSpuCombineDtlRespVO convert(PrdtSpuCombineDtl prdtSpuCombineDtl);

    PrdtSpuCombineDtl convert(PrdtSpuCombineDtlSaveReqVO prdtSpuCombineDtlSaveReq);

    PageResult<PrdtSpuCombineDtlRespVO> convertPage(PageResult<PrdtSpuCombineDtl> prdtSpuCombineDtlPage);
}