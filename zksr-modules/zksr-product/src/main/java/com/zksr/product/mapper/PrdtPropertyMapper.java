package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.controller.property.vo.PrdtPropertyRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtProperty;
import com.zksr.product.controller.property.vo.PrdtPropertyPageReqVO;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.*;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_PROPERTY_VAL_EXISTS;


/**
 * 规格名称Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Mapper
public interface PrdtPropertyMapper extends BaseMapperX<PrdtProperty> {
    default PageResult<PrdtProperty> selectPage(PrdtPropertyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtProperty>()
                    .eqIfPresent(PrdtProperty::getPropertyId, reqVO.getPropertyId())
                    .eqIfPresent(PrdtProperty::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtProperty::getSupplierId, reqVO.getSupplierId())
                    .likeIfPresent(PrdtProperty::getName, reqVO.getName())
                    .eqIfPresent(PrdtProperty::getIsDelete, reqVO.getIsDelete())
                    .eqIfPresent(PrdtProperty::getMemo, reqVO.getMemo())
                    .eqIfPresent(PrdtProperty::getStatus, reqVO.getStatus())
                .orderByDesc(PrdtProperty::getPropertyId));
    }

    //校验是否规格重名/根据spu_id 、名称、是否删除查询
    default PrdtProperty selectPropertyByCheck(PrdtProperty prdtProperty){
        //校验是否规格重名
        QueryWrapper<PrdtProperty> wrapper = new QueryWrapper<>();
        wrapper.eq("spu_id", prdtProperty.getSpuId());
        wrapper.eq("name", prdtProperty.getName());
        wrapper.eq("is_delete", PRDT_IS_DELETE_0);
        return selectOne(wrapper);
    }

    default Long insertPrdtProperty(PrdtProperty prdtProperty){
        //校验是否规格重名
        PrdtProperty checkPrdtProperty = selectPropertyByCheck(prdtProperty);
        if(ToolUtil.isNotEmpty(checkPrdtProperty)){
            throw exception(PRDT_PROPERTY_VAL_EXISTS);
        }
        //设置默认值
        prdtProperty.setIsDelete(PRDT_IS_DELETE_0);
        prdtProperty.setStatus(PRDT_STATUS_1);
        // 插入
        insert(prdtProperty);
        // 返回
        return prdtProperty.getPropertyId();
    }

    //根据查询条件查询规格名称集合
    default List<PrdtProperty> selectPropertyByList(PrdtPropertyRespVO vo){
        LambdaQueryWrapperX<PrdtProperty> wrapper = new LambdaQueryWrapperX<PrdtProperty>()
                .eqIfPresent(PrdtProperty::getPropertyId,vo.getPropertyId())
                .eqIfPresent(PrdtProperty::getSpuId,vo.getSpuId())
                .eqIfPresent(PrdtProperty::getIsDelete, vo.getIsDelete())
                .eqIfPresent(PrdtProperty::getStatus,vo.getStatus());
        return selectList(wrapper);
    }

    default long deleteByIds(List<Long> propertyIds){
        return update(null, new LambdaUpdateWrapper<PrdtProperty>()
                .set(PrdtProperty::getIsDelete,PRDT_IS_DELETE_1)
                .in(PrdtProperty::getPropertyId,propertyIds));
    }

    default List<PrdtProperty> selectBySpuId(Long spuId) {
        LambdaQueryWrapperX<PrdtProperty> wrapper = new LambdaQueryWrapperX<PrdtProperty>()
                .eqIfPresent(PrdtProperty::getSpuId, spuId)
                .eqIfPresent(PrdtProperty::getIsDelete, PRDT_IS_DELETE_0);
        return selectList(wrapper);
    }
}
