package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 城市展示分类对象 prdt_area_class
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@TableName(value = "prdt_area_class")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtAreaClass extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 城市展示分类id */
    @TableId(type = IdType.AUTO)
    private Long areaClassId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 父id */
    @Excel(name = "父id")
    private Long pid;

    /** 城市展示分类名 */
    @Excel(name = "城市展示分类名")
    private String areaClassName;

    /** 分类图标 */
    @Excel(name = "分类图标")
    private String icon;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 是否是电子围栏分类 */
    @Excel(name = "是否是电子围栏分类")
    private Long dzwlFlag;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 级别 */
    @Excel(name = "级别")
    private Integer level;

    /** 删除标志（0代表存在 1代表删除） */
    @Excel(name = "0代表存在 1代表删除")
    private String delFlag;

    /** 是否展示生产日期 0-关闭, 1-开启 */
    @Excel(name = "是否展示生产日期 0-关闭, 1-开启")
    @ApiModelProperty(value = "是否展示生产日期 0-关闭, 1-开启")
    private Integer showProduceDate;

    /** 生产日期格式 yy/MM/dd 年月日 yy/MM 年月 */
    @Excel(name = "生产日期格式 yy/MM/dd 年月日 yy/MM 年月")
    @ApiModelProperty(value = "生产日期格式 yy/MM/dd 年月日 yy/MM 年月")
    private String produceDateFormat;
}
