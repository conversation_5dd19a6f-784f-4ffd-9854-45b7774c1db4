package com.zksr.product.service;

import javax.validation.*;
import javax.xml.crypto.Data;

import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesRespVO;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesPageReqVO;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesSaveReqVO;

import java.util.Date;

/**
 * 商品调价单主Service接口
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
public interface IPrdtAdjustPricesService {

    /**
     * 新增商品调价单主
     *
     * @param createReqVO 创建信息
     * @param addType 新增商品类型 0 后台新增  1 导入新增
     * @return 结果
     */
    public Long insertPrdtAdjustPrices(@Valid PrdtAdjustPricesSaveReqVO createReqVO,Integer addType);

    /**
     * 修改商品调价单主
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtAdjustPrices(@Valid PrdtAdjustPricesSaveReqVO updateReqVO);

    /**
     * 删除商品调价单主
     *
     * @param adjustPricesId 单据ID
     */
    public void deletePrdtAdjustPrices(Long adjustPricesId);


    /**
     * 获得商品调价单主
     *
     * @param adjustPricesId 单据ID
     * @return 商品调价单主
     */
    public PrdtAdjustPricesRespVO getPrdtAdjustPrices(Long adjustPricesId);

    /**
     * 获得商品调价单主分页
     *
     * @param pageReqVO 分页查询
     * @return 商品调价单主分页
     */
    PageResult<PrdtAdjustPricesRespVO> getPrdtAdjustPricesPage(PrdtAdjustPricesPageReqVO pageReqVO);

    /**
     * 审核商品调价单
     * @param adjustPricesId
     */
    void approve(Long adjustPricesId);


    /**
     * 定时生效 商品调价
     * @param sysCode
     * @param validTime
     */
    void operateValidJob(Long sysCode, Date validTime);

}
