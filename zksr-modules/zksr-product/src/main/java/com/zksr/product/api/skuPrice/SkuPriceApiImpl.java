package com.zksr.product.api.skuPrice;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.skuPrice.dto.PrdtSkPageReqApiVO;
import com.zksr.product.api.skuPrice.dto.PrdtSkuPriceInfoExportVO;
import com.zksr.product.api.skuPrice.dto.SkuPriceDTO;
import com.zksr.product.controller.skuPrice.vo.PrdtSkuPricePageReqVO;
import com.zksr.product.domain.PrdtAreaChannelPrice;
import com.zksr.product.service.IPrdtSkuPriceService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.trade.api.order.vo.SupplierOrderDtlInfoExportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class SkuPriceApiImpl implements SkuPriceApi {

    @Autowired
    private IPrdtSkuPriceService prdtSkuPriceService;
    @Autowired
    private IProductCacheService productCacheService;
    @Resource
    private AreaApi areaApi;

    @Override
    public CommonResult<SkuPriceDTO> getSkuPriceByAreaIdAndSkuIdAndType(@RequestParam("areaId") Long areaId, @RequestParam("skuId") Long skuId, @RequestParam("type") Integer type) {
        return CommonResult.success(HutoolBeanUtils.toBean(prdtSkuPriceService.getSkuPriceByAreaIdAndSkuIdAndType(areaId,skuId,type),SkuPriceDTO.class));
    }

    @Override
    public CommonResult<SkuPriceDTO> getSkuPriceByKey(@RequestParam("key") String key) {

        //key为 城市ID 拼接 SkuID 、类型  需要拆开
        String[] split = key.split("-");
        Long areaId = Long.valueOf(split[0]);
        Long skuId = Long.valueOf(split[1]);
        Integer type = Integer.valueOf(split[2]);
        return CommonResult.success(HutoolBeanUtils.toBean(prdtSkuPriceService.getSkuPriceByAreaIdAndSkuIdAndType(areaId,skuId,type),SkuPriceDTO.class));
    }

    @Override
    public CommonResult<List<PrdtSkuPriceInfoExportVO>> getSkuPriceExportList(PrdtSkPageReqApiVO pageVO) {
        PageResult<PrdtSkuPricePageReqVO> prdtSkuPricePage = prdtSkuPriceService.getPrdtSkuPricePage(BeanUtils.toBean(pageVO,PrdtSkuPricePageReqVO.class));

        List<PrdtSkuPriceInfoExportVO> exportList = BeanUtils.toBean(prdtSkuPricePage.getList(), PrdtSkuPriceInfoExportVO.class);
        exportList = exportList.stream().map((item)->{
            SkuDTO skuDTO = productCacheService.getSkuDTO(item.getSkuId());
            if (ToolUtil.isNotEmpty(skuDTO)){
                item.setMinOq(skuDTO.getMinOq());
                item.setMidMinOq(skuDTO.getMidMinOq());
                item.setLargeMinOq(skuDTO.getLargeMinOq());
                item.setJumpOq(skuDTO.getJumpOq());
                item.setMidJumpOq(skuDTO.getMidJumpOq());
                item.setLargeJumpOq(skuDTO.getLargeJumpOq());
            }
            return item;
        }).collect(Collectors.toList());

        return CommonResult.success(exportList);
    }
}
