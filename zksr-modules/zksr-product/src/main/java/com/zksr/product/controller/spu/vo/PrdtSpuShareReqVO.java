package com.zksr.product.controller.spu.vo;

import cn.hutool.core.collection.ListUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品分享批量操作
 * @date 2024/6/20 15:49
 */
@Data
@ApiModel(description = "商品分享批量操作")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PrdtSpuShareReqVO {

    @ApiModelProperty("spu id集合")
    @NotNull(message = "至少选择一个商品开启共享")
    @Size(min = 1, message = "至少选择一个商品开启共享")
    private List<Long> spuIdList;

    public PrdtSpuShareReqVO(Long spuId) {
        this.spuIdList = ListUtil.toList(spuId);
    }
}
