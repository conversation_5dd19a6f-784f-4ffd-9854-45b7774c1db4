package com.zksr.product.controller.propertyVal.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

/**
 * 规格值对象 prdt_property_val
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Data
@ApiModel("规格值 - prdt_property_val分页 Request VO")
public class PrdtPropertyValSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 规格值id */
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)")
    private Long propertyValId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 规格名称id */
    @Excel(name = "规格名称id")
    @ApiModelProperty(value = "规格名称id")
    private Long propertyId;

    /** 规格值名称 */
    @Excel(name = "规格值名称")
    @ApiModelProperty(value = "规格值名称")
    private String name;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否")
    private Long isDelete;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)")
    private Long status;

    /** 商品SPU_id */
    @Excel(name = "商品SPU_id")
    @ApiModelProperty(value = "商品SPU_id",  example = "商品SPU_id")
    private Long spuId;

}
