package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 经营屏蔽客户对象 prdt_block_branch
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@TableName(value = "prdt_block_branch")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class PrdtBlockBranch extends BaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键 */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long blockBranchId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 方案编码 */
    @Excel(name = "方案编码")
    private String schemeNo;

    /** 门店id */
    @Excel(name = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

}
