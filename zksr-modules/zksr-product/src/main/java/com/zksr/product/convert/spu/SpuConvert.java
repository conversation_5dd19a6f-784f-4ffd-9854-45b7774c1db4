package com.zksr.product.convert.spu;

import com.zksr.product.api.spu.excel.ProductImportExcel;
import com.zksr.product.api.spu.vo.PrdtSpuSaveReqVO;
import com.zksr.product.domain.PrdtSpu;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/4/25
 * @desc
 */
@Mapper
public interface SpuConvert {

    SpuConvert INSTANCE = Mappers.getMapper(SpuConvert.class);

    @Mappings({
//            @Mapping(source = "itemData.spuNo", target = "spuNo"),
            @Mapping(source = "itemData.spuName", target = "spuName"),
//            @Mapping(source = "itemData.originPlace", target = "originPlace"),
            @Mapping(source = "itemData.oldestDate", target = "oldestDate"),
            @Mapping(source = "itemData.latestDate", target = "latestDate"),
//            @Mapping(source = "itemData.memo", target = "memo"),
            @Mapping(source = "itemData.details", target = "details"),
            @Mapping(source = "itemData.expirationDate", target = "expirationDate"),
            @Mapping(source = "itemData.auxiliarySpuNo", target = "auxiliarySpuNo"),
    })
    @BeanMapping(ignoreByDefault = true)
    void convertImport(@MappingTarget PrdtSpu prdtSpu, ProductImportExcel itemData);

    PrdtSpuSaveReqVO convertSaveReqVO(PrdtSpu spu);
}
