package com.zksr.product.controller.skuPrice.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

/**
 * sku销售价对象 prdt_sku_price
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Data
@ApiModel("sku销售价 - prdt_sku_price分页 Request VO")
@AllArgsConstructor
@NoArgsConstructor
public class PrdtSkuPriceSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** sku销售价id */
    @Excel(name = "sku销售价id")
    @ApiModelProperty(value = "sku销售价id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuPriceId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 商品sku id;商品sku id */
    @Excel(name = "商品sku id;商品sku id")
    @ApiModelProperty(value = "商品sku id;商品sku id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 销售价1 */
    @Excel(name = "销售价1")
    @ApiModelProperty(value = "销售价1")
    private BigDecimal salePrice1;

    /** 销售价2 */
    @Excel(name = "销售价2")
    @ApiModelProperty(value = "销售价2")
    private BigDecimal salePrice2;

    /** 销售价3 */
    @Excel(name = "销售价3")
    @ApiModelProperty(value = "销售价3")
    private BigDecimal salePrice3;

    /** 销售价4 */
    @Excel(name = "销售价4")
    @ApiModelProperty(value = "销售价4")
    private BigDecimal salePrice4;

    /** 销售价5 */
    @Excel(name = "销售价5")
    @ApiModelProperty(value = "销售价5")
    private BigDecimal salePrice5;

    /** 销售价6 */
    @Excel(name = "销售价6")
    @ApiModelProperty(value = "销售价6")
    private BigDecimal salePrice6;

    /** 类型(用来区分是全国商品，还是本地配送商品，数据字典);0-全国商品 1-本地配送商品 */
    @Excel(name = "类型(用来区分是全国商品，还是本地配送商品，数据字典);0-全国商品 1-本地配送商品")
    @ApiModelProperty(value = "类型(用来区分是全国商品，还是本地配送商品，数据字典);0-全国商品 1-本地配送商品")
    private Integer type;

    /** 成本价 */
    @Excel(name = "成本价")
    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    /** 中单位-销售价1 */
    @Excel(name = "中单位-销售价1")
    @ApiModelProperty(value = "中单位-销售价1")
    private BigDecimal midSalePrice1;

    /** 中单位-销售价2 */
    @Excel(name = "中单位-销售价2")
    @ApiModelProperty(value = "中单位-销售价2")
    private BigDecimal midSalePrice2;

    /** 中单位-销售价3 */
    @Excel(name = "中单位-销售价3")
    @ApiModelProperty(value = "中单位-销售价3")
    private BigDecimal midSalePrice3;

    /** 中单位-销售价4 */
    @Excel(name = "中单位-销售价4")
    @ApiModelProperty(value = "中单位-销售价4")
    private BigDecimal midSalePrice4;

    /** 中单位-销售价5 */
    @Excel(name = "中单位-销售价5")
    @ApiModelProperty(value = "中单位-销售价5")
    private BigDecimal midSalePrice5;

    /** 中单位-销售价6 */
    @Excel(name = "中单位-销售价6")
    @ApiModelProperty(value = "中单位-销售价6")
    private BigDecimal midSalePrice6;

    /** 大单位-销售价1 */
    @Excel(name = "大单位-销售价1")
    @ApiModelProperty(value = "大单位-销售价1")
    private BigDecimal largeSalePrice1;

    /** 大单位-销售价2 */
    @Excel(name = "大单位-销售价2")
    @ApiModelProperty(value = "大单位-销售价2")
    private BigDecimal largeSalePrice2;

    /** 大单位-销售价3 */
    @Excel(name = "大单位-销售价3")
    @ApiModelProperty(value = "大单位-销售价3")
    private BigDecimal largeSalePrice3;

    /** 大单位-销售价4 */
    @Excel(name = "大单位-销售价4")
    @ApiModelProperty(value = "大单位-销售价4")
    private BigDecimal largeSalePrice4;

    /** 大单位-销售价5 */
    @Excel(name = "大单位-销售价5")
    @ApiModelProperty(value = "大单位-销售价5")
    private BigDecimal largeSalePrice5;

    /** 大单位-销售价6 */
    @Excel(name = "大单位-销售价6")
    @ApiModelProperty(value = "大单位-销售价6")
    private BigDecimal largeSalePrice6;

    /** 中单位-标准价 */
    @Excel(name = "中单位-标准价")
    @ApiModelProperty(value = "中单位-标准价")
    private BigDecimal midMarkPrice;

    /** 中单位-成本价(供货价) */
    @Excel(name = "中单位-成本价(供货价)")
    @ApiModelProperty(value = "中单位-成本价(供货价)")
    private BigDecimal midCostPrice;

    /** 大单位-标准价 */
    @Excel(name = "大单位-标准价")
    @ApiModelProperty(value = "大单位-标准价")
    private BigDecimal largeMarkPrice;

    /** 大单位-成本价(供货价) */
    @Excel(name = "大单位-成本价(供货价)")
    @ApiModelProperty(value = "大单位-成本价(供货价)")
    private BigDecimal largeCostPrice;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @Excel(name = "最小单位-数据字典（sys_prdt_unit）", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "最小单位-数据字典（sys_prdt_unit）")
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    @Excel(name = "中单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "中单位-数据字典（sys_prdt_unit）")
    private Long midUnit;

    /** 大单位-数据字典（sys_prdt_unit） */
    @Excel(name = "大单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "大单位-数据字典（sys_prdt_unit）")
    private Long largeUnit;

    public PrdtSkuPriceSaveReqVO(BigDecimal salePrice1, BigDecimal salePrice2, BigDecimal salePrice3, BigDecimal salePrice4, BigDecimal salePrice5, BigDecimal salePrice6, BigDecimal costPrice) {
        this.salePrice1 = salePrice1;
        this.salePrice2 = salePrice2;
        this.salePrice3 = salePrice3;
        this.salePrice4 = salePrice4;
        this.salePrice5 = salePrice5;
        this.salePrice6 = salePrice6;
        this.costPrice = costPrice;
    }

    public boolean isValid(){
        boolean flag =true;
        if(costPrice != null){
            if(salePrice1 != null){
                if(salePrice1.compareTo(costPrice) < 0) return false;
            }
            if(salePrice2 != null){
                if(salePrice2.compareTo(costPrice) < 0) return false;
            }
            if(salePrice3 != null){
                if(salePrice3.compareTo(costPrice) < 0) return false;
            }
            if(salePrice4 != null){
                if(salePrice4.compareTo(costPrice) < 0) return false;
            }
            if(salePrice5 != null){
                if(salePrice5.compareTo(costPrice) < 0) return false;
            }
            if(salePrice6 != null){
                if(salePrice6.compareTo(costPrice) < 0) return false;
            }
        }

        if(midCostPrice != null){
            if(midSalePrice1 != null){
                if(midSalePrice1.compareTo(midCostPrice) < 0) return false;
            }
            if(midSalePrice2 != null){
                if(midSalePrice2.compareTo(midCostPrice) < 0) return false;
            }
            if(midSalePrice3 != null){
                if(midSalePrice3.compareTo(midCostPrice) < 0) return false;
            }
            if(midSalePrice4 != null){
                if(midSalePrice4.compareTo(midCostPrice) < 0) return false;
            }
            if(midSalePrice5 != null){
                if(midSalePrice5.compareTo(midCostPrice) < 0) return false;
            }
            if(midSalePrice6 != null){
                if(midSalePrice6.compareTo(midCostPrice) < 0) return false;
            }
        }

        if(largeCostPrice != null){
            if(largeSalePrice1 != null){
                if(largeSalePrice1.compareTo(largeCostPrice) < 0) return false;
            }
            if(largeSalePrice2 != null){
                if(largeSalePrice2.compareTo(largeCostPrice) < 0) return false;
            }
            if(largeSalePrice3 != null){
                if(largeSalePrice3.compareTo(largeCostPrice) < 0) return false;
            }
            if(largeSalePrice4 != null){
                if(largeSalePrice4.compareTo(largeCostPrice) < 0) return false;
            }
            if(largeSalePrice5 != null){
                if(largeSalePrice5.compareTo(largeCostPrice) < 0) return false;
            }
            if(largeSalePrice6 != null){
                if(largeSalePrice6.compareTo(largeCostPrice) < 0) return false;
            }
        }

        return flag;
    }

}
