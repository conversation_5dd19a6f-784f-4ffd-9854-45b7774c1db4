package com.zksr.product.controller.areaItem.vo;

import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销活动商品分页数据
 * @date 2025/1/22 14:55
 */
@Data
public class ActivityItemPage extends PageParam {

    /**
     * 管理分类白名单
     */
    @ApiModelProperty(value = "管理分类白名单")
    private List<Long> categoryIdList;

    /**
     * 管理分黑名单
     */
    @ApiModelProperty(value = "管理分黑名单")
    private List<Long> blackCategoryIdList;

    /**
     * 品牌白名单
     */
    @ApiModelProperty(value = "品牌白名单")
    private List<Long> brandIdList;

    /**
     * 品牌黑名单
     */
    @ApiModelProperty(value = "品牌黑名单")
    private List<Long> blackBrandIdList;

    /**
     * skuId 白名单
     */
    @ApiModelProperty(value = "skuId 白名单")
    private List<Long> skuIdList;

    /**
     * skuId 黑名单
     */
    @ApiModelProperty(value = "skuId 黑名单")
    private List<Long> blackSkuIdList;

    /**
     * 入驻商ID
     */
    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    /**
     * 促销活动ID
     */
    @ApiModelProperty(value = "促销活动ID")
    private Long activityId;

    /**
     * 管理分类ID
     */
    @ApiModelProperty(value = "促销活动ID")
    private Long catgoryId;

    /**
     * 上架状态,0-已下架, 1-上架中
     */
    @ApiModelProperty(value = "上架状态,0-已下架, 1-上架中")
    private Integer shelfStatus;
}
