package com.zksr.product.convert.blockScheme;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.domain.PrdtBlockSku;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSkuRespVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSkuSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 经营屏蔽sku 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-27
*/
@Mapper
public interface PrdtBlockSkuConvert {

    PrdtBlockSkuConvert INSTANCE = Mappers.getMapper(PrdtBlockSkuConvert.class);

    PrdtBlockSkuRespVO convert(PrdtBlockSku prdtBlockSku);

    PrdtBlockSku convert(PrdtBlockSkuSaveReqVO prdtBlockSkuSaveReq);

    PageResult<PrdtBlockSkuRespVO> convertPage(PageResult<PrdtBlockSku> prdtBlockSkuPage);

    default PrdtBlockSkuRespVO convert(SkuDTO skuDTO, SpuDTO spuDTO, BrandDTO brandDTO) {
        if (skuDTO == null) {
            return null;
        }
        PrdtBlockSkuRespVO respVO = new PrdtBlockSkuRespVO();
        respVO.setSkuId(skuDTO.getSkuId());
        respVO.setSpuId(skuDTO.getSpuId());
        respVO.setBarcode(skuDTO.getBarcode());
        respVO.setMarkPrice(skuDTO.getMarkPrice());
        if (spuDTO != null) {
            respVO.setSpuName(spuDTO.getSpuName());
            respVO.setSpecName(spuDTO.getSpecName());
        }
        if (brandDTO != null) {
            respVO.setBrandName(brandDTO.getBrandName());
        }
        return respVO;
    }

    @Mappings({
            @Mapping(target = "createBy", expression = "java(com.zksr.common.security.utils.SecurityUtils.getUsername())"),
            @Mapping(target = "updateBy", expression = "java(com.zksr.common.security.utils.SecurityUtils.getUsername())"),
            @Mapping(target = "updateTime", expression = "java(com.zksr.common.core.utils.DateUtils.getNowDate())")
    })
    PrdtBlockSku convert(String schemeNo, Long skuId);
}