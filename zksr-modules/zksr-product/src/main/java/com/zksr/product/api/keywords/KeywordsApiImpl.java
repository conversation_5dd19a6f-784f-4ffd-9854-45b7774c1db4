package com.zksr.product.api.keywords;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.service.IPrdtKeywordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/1/15 16:09
 * @关键词词库rpc服务实现
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
@InnerAuth
public class KeywordsApiImpl implements KeywordsApi{

    @Autowired
    private IPrdtKeywordsService prdtKeywordsService;

    @Override
    public CommonResult<Boolean> batchInsertKeywords(Long sysCode, List<String> keywords) {
        prdtKeywordsService.insertPrdtKeywordsBatch(keywords,sysCode);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> searchKeywords(Long sysCode, String keyword) {
        return CommonResult.success(prdtKeywordsService.searchPrdtKeywords(keyword,sysCode));
    }
}
