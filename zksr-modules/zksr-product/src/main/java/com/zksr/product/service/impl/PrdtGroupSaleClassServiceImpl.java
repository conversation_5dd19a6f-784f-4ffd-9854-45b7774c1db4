package com.zksr.product.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtGroupSaleClassMapper;
import com.zksr.product.domain.PrdtGroupSaleClass;
import com.zksr.product.controller.groupSaleClass.vo.PrdtGroupSaleClassPageReqVO;
import com.zksr.product.controller.groupSaleClass.vo.PrdtGroupSaleClassSaveReqVO;
import com.zksr.product.service.IPrdtGroupSaleClassService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 平台商城市分组-展示分类关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Service
public class PrdtGroupSaleClassServiceImpl implements IPrdtGroupSaleClassService {
    @Autowired
    private PrdtGroupSaleClassMapper prdtGroupSaleClassMapper;

    /**
     * 新增平台商城市分组-展示分类关联
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtGroupSaleClass(PrdtGroupSaleClassSaveReqVO createReqVO) {
        // 插入
        PrdtGroupSaleClass prdtGroupSaleClass = HutoolBeanUtils.toBean(createReqVO, PrdtGroupSaleClass.class);
        prdtGroupSaleClassMapper.insert(prdtGroupSaleClass);
        // 返回
        return prdtGroupSaleClass.getSysCode();
    }

    /**
     * 修改平台商城市分组-展示分类关联
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtGroupSaleClass(PrdtGroupSaleClassSaveReqVO updateReqVO) {
        prdtGroupSaleClassMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrdtGroupSaleClass.class));
    }

    /**
     * 删除平台商城市分组-展示分类关联
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deletePrdtGroupSaleClass(Long sysCode) {
        // 删除
        prdtGroupSaleClassMapper.deleteById(sysCode);
    }

    /**
     * 批量删除平台商城市分组-展示分类关联
     *
     * @param sysCodes 需要删除的平台商城市分组-展示分类关联主键
     * @return 结果
     */
    @Override
    public void deletePrdtGroupSaleClassBySysCodes(Long[] sysCodes) {
        for(Long sysCode : sysCodes){
            this.deletePrdtGroupSaleClass(sysCode);
        }
    }

    /**
     * 获得平台商城市分组-展示分类关联
     *
     * @param sysCode 平台商id
     * @return 平台商城市分组-展示分类关联
     */
    @Override
    public PrdtGroupSaleClass getPrdtGroupSaleClass(Long sysCode) {
        return prdtGroupSaleClassMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtGroupSaleClass> getPrdtGroupSaleClassPage(PrdtGroupSaleClassPageReqVO pageReqVO) {
        return prdtGroupSaleClassMapper.selectPage(pageReqVO);
    }

    private void validatePrdtGroupSaleClassExists(Long sysCode) {
        if (prdtGroupSaleClassMapper.selectById(sysCode) == null) {
            throw exception(PRDT_GROUP_SALE_CLASS_NOT_EXISTS);
        }
    }

}
