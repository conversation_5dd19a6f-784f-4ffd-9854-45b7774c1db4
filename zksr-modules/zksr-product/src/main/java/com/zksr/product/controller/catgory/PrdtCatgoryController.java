package com.zksr.product.controller.catgory;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.catgory.excel.CategoryImportExcel;
import com.zksr.product.api.catgory.dto.CatgoryDeleteDTO;
import com.zksr.product.controller.catgory.vo.PrdtCatgoryPageReqVO;
import com.zksr.product.controller.catgory.vo.PrdtCatgoryRespVO;
import com.zksr.product.controller.catgory.vo.PrdtCatgorySaveReqVO;
import com.zksr.product.controller.supplierClass.vo.SupplierClassRateStatusRespVO;
import com.zksr.product.domain.PrdtCatgory;
import com.zksr.product.domain.dto.BoundProductInfoDTO;
import com.zksr.product.service.IPrdtCatgoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台商管理分类Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Api(tags = "管理后台 - 平台商管理分类接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/catgory")
public class PrdtCatgoryController {
    @Autowired
    private IPrdtCatgoryService prdtCatgoryService;

    /**
    * @Description: 入驻商获取管理分类分页数据
    * @Param: PrdtCatgoryPageReqVO
    * @return: CommonResult<List<PrdtCatgoryRespVO>>
    * @Author: liuxingyu
    * @Date: 2024/5/31 15:09
    */
    @ApiOperation(value = "入驻商获取管理分类分页数据", httpMethod = HttpMethod.GET)
    @GetMapping("/getPageBySupplierId")
    public CommonResult<PageResult<PrdtCatgoryRespVO>> getPageBySupplierId(PrdtCatgoryPageReqVO pageReqVO) {
        return success(prdtCatgoryService.getPageBySupplierId(pageReqVO));
    }

    /**
     * @Description: 平台管理分类同步城市展示分类
     * @Param:
     * @return: CommonResult<Boolean>
     * @Author: liuxingyu
     * @Date: 2024/5/24 9:00
     */
    @ApiOperation(value = "平台管理分类同步城市展示分类", httpMethod = HttpMethod.POST)
    @PostMapping("/copyToAreaClass/{areaId}")
    public CommonResult<Boolean> copyToAreaClass(@PathVariable Long areaId){
        return success(prdtCatgoryService.copyToAreaClass(areaId));
    }

    /**
    * @Description: 平台管理分类同步平台展示分类
    * @Param:
    * @return: CommonResult<Boolean>
    * @Author: liuxingyu
    * @Date: 2024/5/24 9:00
    */
    @ApiOperation(value = "平台管理分类同步平台展示分类", httpMethod = HttpMethod.POST)
    @PostMapping("/copyToSaleClass")
    public CommonResult<Boolean> copyToSaleClass(){
        return success(prdtCatgoryService.copyToSaleClass(null));
    }

    /**
    * @Description: 根据id集合获取管理分类信息
    * @Param:  List<Long> catgoryIds
    * @return: List<PrdtCatgoryRespVO>
    * @Author: liuxingyu
    * @Date: 2024/5/18 15:19
    */
    @ApiOperation(value = "根据id集合获取管理分类信息", httpMethod = HttpMethod.POST)
    @PostMapping("/getCatgoryByIds")
    public CommonResult<List<PrdtCatgoryRespVO>> getCatgoryByIds(@ApiParam(name = "catgoryIds", value = "管理分类ID集合")
                                                                     @RequestBody List<Long> catgoryIds) {
        List<PrdtCatgory> catgoryByIds = prdtCatgoryService.getCatgoryByIds(catgoryIds);
        return success(HutoolBeanUtils.toBean(catgoryByIds,PrdtCatgoryRespVO.class));
    }


    /**
     * @Description: 更改管理分类状态
     * @Param: PrdtCatgorySaveReqVO
     * @return: CommonResult<Long>
     * @Author: liuxingyu
     * @Date: 2024/4/1 11:35
     */
    @ApiOperation(value = "更改管理分类状态", httpMethod = HttpMethod.PUT , notes = "product:catgory:changeStatus")
    @RequiresPermissions("product:catgory:changeStatus")
    @Log(title = "平台商管理分类", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public CommonResult<Integer> changeStatus(@RequestBody PrdtCatgorySaveReqVO updateReqVO) {
        return success(prdtCatgoryService.changeStatus(updateReqVO));
    }


    /**
     * @Description: 根据入驻商编号获取绑定的管理类别
     * @Param: Long supplierId
     * @return: CommonResult<List < PrdtCatgoryRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/3/8 11:12
     */
    @ApiOperation(value = "根据入驻商编号获取绑定的管理类别", httpMethod = HttpMethod.GET)
    @GetMapping("/getCatgoryBySupplierId")
    public CommonResult<List<PrdtCatgoryRespVO>> getCatgoryBySupplierId(@ApiParam(name = "supplierId", value = "入驻商编号") @RequestParam Long supplierId) {
        return success(prdtCatgoryService.getCatgoryBySupplierId(supplierId));
    }

    /**
     * @Description: 根据入驻商编号获取绑定的管理类别
     * @Param: List<Long> supplierIds
     * @return: CommonResult<List < PrdtCatgoryRespVO>>
     */
    @ApiOperation(value = "根据入驻商编号获取绑定的管理类别", httpMethod = HttpMethod.POST)
    @PostMapping("/getCategoryBySupplierIds")
    public CommonResult<List<PrdtCatgoryRespVO>> getCategoryBySupplierIds(@Valid @NotEmpty(message = "入驻商不能为空") @RequestBody List<String> supplierIds) {
        return success(prdtCatgoryService.getCategoryBySupplierIds(supplierIds));
    }

    /**
     * @Description: 获取平台商管理分类列表
     * @Param: Long catgoryId 返回数据剔除数据ID为传入的ID
     * @return: CommonResult<List < PrdtCatgoryRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/2/27 14:32
     */
    @ApiOperation(value = "获取平台商管理分类列表", httpMethod = "GET")
    @GetMapping("/getCatgoryList")
    public CommonResult<List<PrdtCatgoryRespVO>> getCatgoryList(@ApiParam(name = "catgoryId", value = "平台商管理分类id(非必传,返回数据剔除数据ID为传入的ID)")
                                                                @RequestParam(value = "catgoryId", required = false) Long catgoryId,
                                                                @RequestParam(value = "supplierId", required = false) Long supplierId,
                                                                @RequestParam(value = "level", required = false) Integer level) {
        Long sysSupplierId = SecurityUtils.getSupplierId();
        if (Objects.nonNull(sysSupplierId)) {
            return success(prdtCatgoryService.getCatgoryBySupplierId(sysSupplierId));
        }
        return success(prdtCatgoryService.getCatgoryList(catgoryId, supplierId, level));
    }

    /**
     * @Description: 获取平台商管理分类列表
     * @Param: Long catgoryId 返回数据剔除数据ID为传入的ID
     * @return: CommonResult<List < PrdtCatgoryRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/2/27 14:32
     */
    @ApiOperation(value = "获取入驻商详情判断类别是否绑定商品", httpMethod = HttpMethod.POST)
    @PostMapping("/getCatgoryByList")
    public CommonResult<List<PrdtCatgoryRespVO>> getCatgoryByList(@RequestBody PrdtCatgoryPageReqVO pageReqVO) {
        return success(prdtCatgoryService.getCatgoryByList(pageReqVO.getCatgoryIds(),pageReqVO.getSupplierId()));
    }

    /**
     * @Description: 获取平台商管理分类子级列表(若子级为空则返回自己)
     * @Param: PrdtCatgoryPageReqVO pageReqVO
     * @return: CommonResult<PageResult < PrdtCatgoryRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/2/19 15:42
     */
    @ApiOperation(value = "获取平台商管理分类子级列表", httpMethod = "GET")
    @GetMapping("/getSublevelCatgoryById")
    public CommonResult<PageResult<PrdtCatgoryRespVO>> getSublevelCatgoryById(
            @ApiParam(name = "catgoryId", value = "管理类别ID", required = true) @RequestParam("catgoryId") Long catgoryId,
            @ApiParam(name = "pageNo", value = "页码", required = true) @RequestParam("pageNo") Integer pageNo,
            @ApiParam(name = "pageSize", value = "每页条数", required = true) @RequestParam("pageSize") Integer pageSize) {
        PrdtCatgoryPageReqVO pageReqVO = new PrdtCatgoryPageReqVO();
        pageReqVO.setCatgoryId(catgoryId);
        pageReqVO.setPageNo(pageNo);
        pageReqVO.setPageSize(pageSize);
        return success(prdtCatgoryService.getSublevelCatgoryById(pageReqVO));
    }


    /**
     * 新增平台商管理分类
     * !@平台类别管理 - 新增
     */
    @ApiOperation(value = "新增平台商管理分类", httpMethod = "POST", notes = "product:catgory:add")
    @RequiresPermissions("product:catgory:add")
    @Log(title = "平台商管理分类", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtCatgorySaveReqVO createReqVO) {
        return success(prdtCatgoryService.cleanCache(
                prdtCatgoryService.insertPrdtCatgory(createReqVO)
        ));
    }

    /**
     * 修改平台商管理分类
     * !@平台类别管理 - 修改
     */
    @ApiOperation(value = "修改平台商管理分类", httpMethod = "PUT", notes = "product:catgory:edit")
    @RequiresPermissions("product:catgory:edit")
    @Log(title = "平台商管理分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtCatgorySaveReqVO updateReqVO) {
        prdtCatgoryService.cleanCache(
                prdtCatgoryService.updatePrdtCatgory(updateReqVO)
        );
        return success(true);
    }

    /**
     * 删除平台商管理分类
     */
    @ApiOperation(value = "删除平台商管理分类", httpMethod = HttpMethod.DEL, notes = "product:catgory:remove")
    @RequiresPermissions("product:catgory:remove")
    @Log(title = "平台商管理分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{classIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] classIds) {
        prdtCatgoryService.deletePrdtCatgoryByClassIds(classIds);
        return success(true);
    }

    /**
     * 获取平台商管理分类详细信息
     */
    @ApiOperation(value = "获得平台商管理分类详情", httpMethod = "GET", notes = "product:catgory:query")
    @RequiresPermissions("product:catgory:query")
    @GetMapping(value = "/{classId}")
    public CommonResult<PrdtCatgoryRespVO> getInfo(@PathVariable("classId") Long classId) {
        PrdtCatgory prdtCatgory = prdtCatgoryService.getPrdtCatgory(classId);
        return success(HutoolBeanUtils.toBean(prdtCatgory, PrdtCatgoryRespVO.class));
    }

    /**
     * 分页查询平台商管理分类
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台商管理分类分页列表", httpMethod = "GET", notes = "product:catgory:list")
    @RequiresPermissions("product:catgory:list")
    public CommonResult<PageResult<PrdtCatgoryRespVO>> getPage(@Valid PrdtCatgoryPageReqVO pageReqVO) {
        Long sysSupplierId = SecurityUtils.getSupplierId();
        if (Objects.nonNull(sysSupplierId)) {
            pageReqVO.setSupplierId(sysSupplierId);
        }
        PageResult<PrdtCatgory> pageResult = prdtCatgoryService.getPrdtCatgoryPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, PrdtCatgoryRespVO.class));
    }

    /**
     * 获取 参考销售参考佣金
     * @return 比例
     */
    @GetMapping("/getSupplierClassRate")
    @ApiOperation(value = " 获取管理分类 参考佣金", httpMethod = HttpMethod.GET)
    public CommonResult<SupplierClassRateStatusRespVO> getSupplierClassRate() {
        return success(prdtCatgoryService.getSaleClassRate());
    }


    @ApiOperation(value = "导入平台管理类别", httpMethod = HttpMethod.POST,notes = "product:catgory:import")
    @Log(title = "导入平台管理类别", businessType = BusinessType.IMPORT)
    @RequiresPermissions("product:catgory:import")
    @PostMapping("/importData")
    public CommonResult<String> importData(MultipartFile file) throws Exception
    {
        ExcelUtil<CategoryImportExcel> util = new ExcelUtil<>(CategoryImportExcel.class);
        List<CategoryImportExcel> categoryList = util.importExcel(file.getInputStream(),1);
        String message = prdtCatgoryService.importBaseCategory(categoryList);
        return success(message);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "平台管理类别信息模板", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<CategoryImportExcel> util = new ExcelUtil<CategoryImportExcel>(CategoryImportExcel.class);
        util.importCatgoryTemplateExcel(response, "平台管理类别信息", "填表说明:\n" +
                "1、商品类别名称:必填项,支持文字和数字输入，最多仅限20个字符，超出字符导入会失败\n" +
                "2、平台分润比例:必填项，仅支持数字，按照百分之几来算商品毛利的分润比例，如果后台设置了分润比列，则不会覆盖系统原有值，如需要更改则允许去后台重新编辑分润比例，导入只针对初始值的导入设置；\n " +
                "注意:一级类别为主类目，二级属于一级的子类，三级为二级的子类;");
    }

    /**
     *  删除平台管理类别时判断平台管理类别是否挂商品
     */
    @ApiOperation(value = "获得平台管理类别是否挂商品", httpMethod = HttpMethod.GET)
    @GetMapping(value = "/isCatgoryHangGoods")
    public CommonResult<List<BoundProductInfoDTO>> isCatgoryHangGoods(@ApiParam(name = "catgoryId", value = "管理类别ID", required = true) @RequestParam("catgoryId") Long catgoryId) {
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        CommonResult<List<BoundProductInfoDTO>> result = prdtCatgoryService.isCatgoryHangGoods(catgoryId, sysCode);
        return CommonResult.success(result.getCheckedData());
    }

    /**
     * 删除平台商管理分类
     */
    @ApiOperation(value = "删除平台商管理分类", httpMethod = HttpMethod.POST, notes = "product:catgory:remove")
    @RequiresPermissions("product:catgory:remove")
    @Log(title = "平台商管理分类", businessType = BusinessType.DELETE)
    @PostMapping("/batchDelete")
    public CommonResult<CatgoryDeleteDTO> remove(@RequestBody List<Long> classIds) {
        return success(prdtCatgoryService.batchDeleteCatogory(classIds));
    }


}
