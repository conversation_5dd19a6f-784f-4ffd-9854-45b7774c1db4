package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 组合商品详情对象 prdt_spu_combine_dtl
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@TableName(value = "prdt_spu_combine_dtl")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSpuCombineDtl extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 组合商品ID */
    @Excel(name = "组合商品ID")
    private Long spuCombineId;

    /** 城市上架商品id */
    @Excel(name = "城市上架商品id")
    private Long areaItemId;

    /** 全国上架商品id */
    @Excel(name = "全国上架商品id")
    private Long supplierItemId;

    /** skuID */
    @Excel(name = "skuID")
    private Long skuId;

    /** 商品单位大小 */
    @Excel(name = "商品单位大小")
    private Long skuUnitType;

    /** 数量 */
    @Excel(name = "数量")
    private Long qty;

    /** 是否为赠品 */
    @Excel(name = "是否为赠品")
    private Integer giftFlag;

}
