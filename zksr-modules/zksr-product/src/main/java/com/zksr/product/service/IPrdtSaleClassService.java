package com.zksr.product.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.saleClass.dto.SaleClassExportVo;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassCopyReqVO;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassPageReqVO;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassRespVO;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassSaveReqVO;
import com.zksr.product.domain.PrdtSaleClass;
import com.zksr.product.domain.excel.ProductSaleClassImportExcel;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台商展示分类Service接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
public interface IPrdtSaleClassService {

    /**
     * 新增平台商展示分类
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtSaleClass(@Valid PrdtSaleClassSaveReqVO createReqVO);

    /**
     * 修改平台商展示分类
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtSaleClass(@Valid PrdtSaleClassSaveReqVO updateReqVO);

    /**
     * 删除平台商展示分类
     *
     * @param saleClassId 平台商展示分类id
     */
    public void deletePrdtSaleClass(Long saleClassId);

    /**
     * 批量删除平台商展示分类
     *
     * @param saleClassIds 需要删除的平台商展示分类主键集合
     * @return 结果
     */
    public void deletePrdtSaleClassBySaleClassIds(Long[] saleClassIds);

    /**
     * 获得平台商展示分类
     *
     * @param saleClassId 平台商展示分类id
     * @return 平台商展示分类
     */
    public PrdtSaleClass getPrdtSaleClass(Long saleClassId);

    /**
     * 获得平台商展示分类分页
     *
     * @param pageReqVO 分页查询
     * @return 平台商展示分类分页
     */
    PageResult<PrdtSaleClass> getPrdtSaleClassPage(PrdtSaleClassPageReqVO pageReqVO);

    /**
     * @Description: 获取平台商展示分类子级列表
     * @Author: liuxingyu
     * @Date: 2024/2/19 17:37
     */
    PageResult<PrdtSaleClassRespVO> getSublevelSaleClassById(PrdtSaleClassPageReqVO pageReqVO);

    /**
     * @Description: 获取平台商展示分类集合
     * @Author: liuxingyu
     * @Date: 2024/2/27 15:06
     */
    List<PrdtSaleClassRespVO> getSaleClassList(PrdtSaleClassPageReqVO reqVO);

    /**
    * @Description: 根据平台商展示分类Id获取平台商展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/25 15:03
    */
    PrdtSaleClass getSaleClassBySaleClassId(Long saleClassId);

    /**
    * @Description: 获取平台展示分类列表
    * @Author: liuxingyu
    * @Date: 2024/3/26 20:15
    */
    List<PrdtSaleClass> getSaleClassListBySysCode(Long sysCode);

    /**
     * 批量获取展示分类信息   (暂时用于支持数据回显)
     * @param saleClassIds  展示分类ID集合
     * @return  展示分类集合
     */
    List<PrdtSaleClassRespVO> getSelectedSaleClass(List<Long> saleClassIds);

    /**
    * @Description: 变更平台展示分类状态
    * @Author: liuxingyu
    * @Date: 2024/5/6 16:21
    */
    Boolean changeStatus(PrdtSaleClassSaveReqVO updateReqVO);

    /**
     * 一键批量删除全国展示多级分类
     *
     * @param saleClassIds 需要删除的全国展示分类各个级别ID集合
     * @return 结果
     */
    public void removeMultilevelClasses(Long[] saleClassIds);

    /**
     * 导入平台展示类别
     * @param saleClassList
     * @return
     */
    String importSaleClassData(List<ProductSaleClassImportExcel> saleClassList);

    /**
     * 全国展示类别复制
     * @param vo
     */
    void copySaleClass(PrdtSaleClassCopyReqVO vo);

    List<SaleClassExportVo> getSaleClassExportList(SaleClassExportVo pageVo);
}
