package com.zksr.product.service.impl;

import com.alicp.jetcache.Cache;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.controller.supplierGroupPrice.vo.PrdtSupplierGroupPriceRespVO;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.group.GroupApi;
import com.zksr.system.api.group.dto.GroupDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtSupplierGroupPriceMapper;
import com.zksr.product.domain.PrdtSupplierGroupPrice;
import com.zksr.product.controller.supplierGroupPrice.vo.PrdtSupplierGroupPricePageReqVO;
import com.zksr.product.controller.supplierGroupPrice.vo.PrdtSupplierGroupPriceSaveReqVO;
import com.zksr.product.service.IPrdtSupplierGroupPriceService;

import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 平台商城市分组价格Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Service
public class PrdtSupplierGroupPriceServiceImpl implements IPrdtSupplierGroupPriceService {
    @Autowired
    private PrdtSupplierGroupPriceMapper prdtSupplierGroupPriceMapper;

    @Autowired
    private AreaApi remoteAreaApi;

    @Autowired
    private GroupApi remoteGroupApi;

    @Autowired
    @Qualifier("supplierSalePriceCodeCache")
    private Cache<String, Integer> supplierSalePriceCodeCache;

    /**
     * 新增平台商城市分组价格
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertPrdtSupplierGroupPrice(PrdtSupplierGroupPriceSaveReqVO createReqVO) {
        //校验商品区域城市 是否是二级
        remoteAreaApi.checkAreaByAreaId(createReqVO.getAreaId());
        // 插入
        PrdtSupplierGroupPrice prdtSupplierGroupPrice = HutoolBeanUtils.toBean(createReqVO, PrdtSupplierGroupPrice.class);

        //清除Redis价格码
        removeRedisPrice(createReqVO.getAreaId(),createReqVO.getGroupId());
        // 返回
        return prdtSupplierGroupPriceMapper.insertPrdtSupplierGroupPrice(prdtSupplierGroupPrice);
    }

    /**
     * 修改平台商城市分组价格
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtSupplierGroupPrice(PrdtSupplierGroupPriceSaveReqVO updateReqVO) {
        //校验商品区域城市 是否是二级
        remoteAreaApi.checkAreaByAreaId(updateReqVO.getAreaId());
        //清除Redis价格码
        removeRedisPrice(updateReqVO.getAreaId(),updateReqVO.getGroupId());
        prdtSupplierGroupPriceMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, PrdtSupplierGroupPrice.class));
    }

    /**
     * 删除平台商城市分组价格
     *
     * @param groupId 平台商id
     * @param areaId
     */
    @Override
    public void deletePrdtSupplierGroupPrice(Long groupId, Long areaId) {
        //清除Redis价格码
        removeRedisPrice(areaId,groupId);
        // 删除
        prdtSupplierGroupPriceMapper.deleteSupplierGroupPrice(groupId,areaId);
    }

    /**
     * 批量删除平台商城市分组价格
     *
     * @param sysCodes 需要删除的平台商城市分组价格主键
     * @return 结果
     */
    @Override
    public void deletePrdtSupplierGroupPriceBySysCodes(Long[] sysCodes) {

    }

    /**
     * 获得平台商城市分组价格
     *
     * @param sysCode 平台商id
     * @return 平台商城市分组价格
     */
    @Override
    public PrdtSupplierGroupPrice getPrdtSupplierGroupPrice(Long sysCode) {
        return prdtSupplierGroupPriceMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtSupplierGroupPricePageReqVO> getPrdtSupplierGroupPricePage(PrdtSupplierGroupPricePageReqVO pageReqVO) {
        PageResult<PrdtSupplierGroupPrice> result = prdtSupplierGroupPriceMapper.selectPage(pageReqVO);
        PageResult<PrdtSupplierGroupPricePageReqVO> resultPage = new PageResult<PrdtSupplierGroupPricePageReqVO>(HutoolBeanUtils.toBean(result.getList(), PrdtSupplierGroupPricePageReqVO.class),result.getTotal());
        //匹配分页数据
        List<PrdtSupplierGroupPricePageReqVO> list = resultPage.getList();
        if(ToolUtil.isNotEmpty(list)){
            //获取城市列表
            CommonResult<Map<Long, AreaDTO>> areaMapResult = remoteAreaApi.getAreaMapBySysCode(SecurityUtils.getLoginUser().getSysCode(),null );
            Map<Long, AreaDTO> areaMap = areaMapResult.getData();
            Boolean areaMapFlag = false;
            if(ToolUtil.isNotEmpty(areaMap)){
                areaMapFlag = true;
            }
            Boolean areaFlag = areaMapFlag;

            //获取城市分组列表
            CommonResult<Map<Long, GroupDTO>> groupResult = remoteGroupApi.getGroupListBySysCode(SecurityUtils.getLoginUser().getSysCode());
            Map<Long, GroupDTO> groupMap = groupResult.getData();
            Boolean groupMapFlag = false;
            if(ToolUtil.isNotEmpty(groupMap)){
                groupMapFlag = true;
            }
            Boolean groupFlag = groupMapFlag;
            list.forEach(price ->{
                //获取城市列表
                if(areaFlag && ToolUtil.isNotEmpty(areaMap.get(price.getAreaId()))){
                    price.setAreaName(areaMap.get(price.getAreaId()).getAreaName());
                }

                //获取平台商城市分组列表
                if(groupFlag && ToolUtil.isNotEmpty(groupMap.get(price.getGroupId()))){
                    price.setGroupName(groupMap.get(price.getGroupId()).getGroupName());
                }

            });
        }
        return resultPage;
    }

    @Override
    public PrdtSupplierGroupPrice getPriceByAreaIdAndGroupId(Long areaId, Long groupId) {
        return prdtSupplierGroupPriceMapper.selectSupplierGroupPrice(new PrdtSupplierGroupPriceRespVO(areaId,groupId));
    }

    private void validatePrdtSupplierGroupPriceExists(Long sysCode) {
        if (prdtSupplierGroupPriceMapper.selectById(sysCode) == null) {
            throw exception(PRDT_SUPPLIER_GROUP_PRICE_NOT_EXISTS);
        }
    }

    /**
     * 清除Redis中的价格码
     * @param areaId
     * @param groupId
     */
    private void removeRedisPrice(Long areaId,Long groupId){
        //组装价格码的key
        StringJoiner priceCodeKey = new StringJoiner("-");
        priceCodeKey.add(areaId.toString());
        priceCodeKey.add(groupId.toString());
        //清除价格码缓存
        supplierSalePriceCodeCache.remove(priceCodeKey.toString());

    }


}
