package com.zksr.product.api.areaItem;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.areaItem.form.PrdAreaItemImportForm;
import com.zksr.product.api.areaItem.vo.ApiAreaItemPageReqVO;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.convert.item.PrdtAreaItemConvert;
import com.zksr.product.domain.PrdtAreaItem;
import com.zksr.product.service.IPrdtAreaItemService;
import com.zksr.product.service.IPrdtSkuService;
import com.zksr.report.api.homePages.dto.HomePagesSkuDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.stream.Collectors;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class AreaItemApiImpl implements AreaItemApi {

    @Autowired
    private IPrdtAreaItemService prdtAreaItemService;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private IPrdtSkuService skuService;

    @Override
    public CommonResult<AreaItemDTO> getAreaItemId(@RequestParam("areaItemId") Long areaItemId) {
        PrdtAreaItem areaItem = prdtAreaItemService.getAreaItemId(areaItemId);
        return CommonResult.success(HutoolBeanUtils.toBean(areaItem, AreaItemDTO.class));
    }

    @Override
    public CommonResult<List<AreaItemDTO>> getAreaItemListByApi(AreaItemDTO itemDTO) {
        return CommonResult.success(prdtAreaItemService.getAreaItemListByApi(itemDTO));
    }

    @Override
    public CommonResult<PageResult<PrdtAreaItemPageRespVO>> getAreaItemPageByApi(ApiAreaItemPageReqVO areaItemPageReqVO) {
        PageResult<PrdtAreaItemPageRespVO> page = prdtAreaItemService.getPrdtAreaItemPage(PrdtAreaItemConvert.INSTANCE.convertPageReq(areaItemPageReqVO));
        return CommonResult.success(page);
    }

    /**
     * 更新用户统计数据
     * @param minAreaItemId 最小上架ID
     * @return  当前批次最小ID
     */
    @Override
    public CommonResult<Long> updateSaleQtyTotal(Long minAreaItemId) {
        List<PrdtAreaItem> totalList = prdtAreaItemService.getSaleQtyTotalList(minAreaItemId);
        // 更新已售
        skuService.updateSaleQty(totalList.stream().map(PrdtAreaItem::getSkuId).collect(Collectors.toList()));
        if (totalList.isEmpty()) {
            return CommonResult.success(NumberPool.LOWER_GROUND_LONG);
        }
        return CommonResult.success(totalList.get(totalList.size() - 1).getAreaItemId());
    }

    @Override
    public CommonResult<AreaItemDTO> getAreaItemBySpuCombineId(Long spuCombineId) {
        return CommonResult.success(HutoolBeanUtils.toBean(prdtAreaItemService.getAreaItemBySpuCombineId(spuCombineId), AreaItemDTO.class));
    }

    @Override
    public CommonResult<Boolean> updateAreaItem(AreaItemDTO itemDTO) {
        prdtAreaItemService.updateAreaCombineItem(itemDTO);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<AreaItemDTO> getAreaItemByActivityId(Long activityId) {
        return CommonResult.success(HutoolBeanUtils.toBean(prdtAreaItemService.getAreaItemByActivityId(activityId), AreaItemDTO.class));
    }

    @Override
    public CommonResult<List<HomePagesSkuDataRespDTO>> getHomePagesSkuData(HomePagesReqVO reqVO) {
        return CommonResult.success(prdtAreaItemService.getHomePagesSkuData(reqVO));
    }

    public CommonResult<String> importDataDcEvent(PrdAreaItemImportForm form){
        return CommonResult.success(prdtAreaItemService.impordDataDcEvent(form.getList(), form.getSysCode(),form.getFileImportId(), form.getDcId()));
    }
}
