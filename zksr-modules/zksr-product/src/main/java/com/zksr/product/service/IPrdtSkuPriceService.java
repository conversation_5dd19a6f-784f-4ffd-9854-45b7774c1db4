package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.api.skuPrice.dto.PrdtSkuPriceInfoExportVO;
import com.zksr.product.api.skuPrice.excel.PrdtSkuPriceImportExportDTO;
import com.zksr.product.controller.skuPrice.vo.PrdtSkuPriceImportExcel;
import com.zksr.product.controller.skuPrice.vo.PrdtSkuPriceSaveInVo;
import com.zksr.product.domain.PrdtSkuPrice;
import com.zksr.product.controller.skuPrice.vo.PrdtSkuPricePageReqVO;
import com.zksr.product.controller.skuPrice.vo.PrdtSkuPriceSaveReqVO;

import java.util.List;

/**
 * sku销售价Service接口
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
public interface IPrdtSkuPriceService {

    /**
     * 新增sku销售价
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public List<PrdtSkuPriceSaveReqVO> insertPrdtSkuPrice(@Valid PrdtSkuPriceSaveInVo createReqVO);

    /**
     * 修改sku销售价
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtSkuPrice(@Valid PrdtSkuPriceSaveReqVO updateReqVO);

    /**
     * 删除sku销售价
     *
     * @param skuPriceId sku销售价id
     */
    public void deletePrdtSkuPrice(Long skuPriceId);

    /**
     * 批量删除sku销售价
     *
     * @param skuPriceIds 需要删除的sku销售价主键集合
     * @return 结果
     */
    public void deletePrdtSkuPriceBySkuPriceIds(Long[] skuPriceIds);

    /**
     * 获得sku销售价
     *
     * @param skuPriceId sku销售价id
     * @return sku销售价
     */
    public PrdtSkuPrice getPrdtSkuPrice(Long skuPriceId);

    /**
     * 获得sku销售价分页
     *
     * @param pageReqVO 分页查询
     * @return sku销售价分页
     */
    PageResult<PrdtSkuPricePageReqVO> getPrdtSkuPricePage(PrdtSkuPricePageReqVO pageReqVO);

    /**
     * 分页查询sku销售价定价列表
     *
     * @param pageReqVO 分页查询
     * @return sku销售价分页
     */
    PageResult<PrdtSkuPricePageReqVO> getPrdtSkuPricePageByPricing(PrdtSkuPricePageReqVO pageReqVO);


    /**
     * 获得sku销售价
     *
     * @param areaId sku销售价id
     * @return sku销售价
     */

    public PrdtSkuPrice getSkuPriceByAreaIdAndSkuIdAndType(Long areaId,Long skuId,Integer type);

    void batchDeleteBySkuPriceIds(List<Long> skuPriceIds);
    /**
     * 清除销售价缓存
     *
     * @param skuPriceList sku销售价集合
     */

    public void removeCache(List<PrdtSkuPriceSaveReqVO> skuPriceList);

    String impordData(List<PrdtSkuPriceInfoExportVO> categoryList);
}
