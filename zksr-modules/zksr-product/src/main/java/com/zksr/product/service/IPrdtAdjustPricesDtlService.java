package com.zksr.product.service;

import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesSaveReqVO;
import com.zksr.product.domain.PrdtAdjustPrices;
import com.zksr.product.domain.PrdtAdjustPricesDtl;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesDtlSaveReqVO;

import java.util.List;

/**
 * 商品调价单明细Service接口
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
public interface IPrdtAdjustPricesDtlService {

    /**
     * 批量新增商品调价单明细
     *
     * @param prdtAdjustPrices
     * @param createReqVO      创建信息
     * @param addType
     * @return 结果
     */
    public Boolean insertPrdtAdjustPricesDtlBatch(PrdtAdjustPrices prdtAdjustPrices, List<PrdtAdjustPricesDtlSaveReqVO> createReqVO, Integer addType);


    /**
     * 批量更新商品调价单明细
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public void updatePrdtAdjustPricesDtlBatch(PrdtAdjustPricesSaveReqVO updateReqVO);


    /**
     * 删除商品调价单明细
     *
     * @param adjustPricesId 主单据ID
     */
    public void deletePrdtAdjustPricesDtlByAdjustPricesDtlId(Long adjustPricesId);


    /**
     * 获得商品调价单明细集合
     *
     * @param adjustPricesId 单据明细ID
     * @return 商品调价单明细
     */
    public List<PrdtAdjustPricesDtl> getPrdtAdjustPricesDtlList(Long adjustPricesId);

    /**
     * 修改商品调价单明细生效状态
     * @param adjustPricesId
     */
    public void updatePrdtAdjustPricesDtlValidState(List<PrdtAdjustPricesDtl> dtlList, Long validState);

    /**
     * 收到审核成功消息，更新商品价格
     * @param prdtAdjustPricesDtlId
     */
    public void adjustPricesDtlComplete(Long prdtAdjustPricesDtlId);


    /**
     * 新增或修改商品调价单明细时 需要校验
     *
     * @param delList
     * @param supplierId
     */
    public void checkAddOrUpdateDtlList(List<PrdtAdjustPricesDtlSaveReqVO> delList,Long supplierId);

}
