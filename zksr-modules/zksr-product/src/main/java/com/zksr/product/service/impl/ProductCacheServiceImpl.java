package com.zksr.product.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.redis.bean.VisualSettingMasterBySupplierIdBean;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.product.api.areaChannelPrice.AreaChannelPriceApi;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.AreaItemApi;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.brand.BrandApi;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.CatgoryApi;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.saleClass.SaleClassApi;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.skuPrice.SkuPriceApi;
import com.zksr.product.api.skuPrice.dto.SkuPriceDTO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SkuUnitGroupDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierGroupPrice.SupplierGrouplPriceApi;
import com.zksr.product.api.supplierItem.SupplierItemApi;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.product.mapper.PrdtSpuCombineMapper;
import com.zksr.product.service.IPrdtSpuService;
import com.zksr.product.service.IProductCacheService;
import com.zksr.promotion.api.activity.ActivityApi;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.ChannelApi;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.opensource.OpensourceApi;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.AppletAgreementPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.BasicSettingPolicyDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.VisualApi;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.common.redis.enums.RedisConstants.DAY_SECONDS;

@Service
@Slf4j
public class ProductCacheServiceImpl implements IProductCacheService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private Cache<String, PartnerDto> partnerDtoCache;

    @Autowired
    private Cache<Long, BranchDTO> branchDTOCache;

    @Autowired
    private Cache<Long, AreaDTO> areaDtoCache;

    @Autowired
    private Cache<Long, ChannelDTO> channelDTOCache;

    @Autowired
    private Cache<Long, SupplierDTO> supplierDTOCache;
    @Autowired
    private Cache<Long, DcDTO> dcDtoCache;

    @Autowired
    private Cache<Long, AreaClassDTO> areaClassDtoCache;

    @Autowired
    @Qualifier("areaClassBranchListDtoCache")
    private Cache<Long, List<AreaClassDTO>> areaClassBranchListDtoCache;

    @Autowired
    private Cache<Long, CatgoryDTO> catgoryDtoCache;

    @Autowired
    private Cache<Long, SaleClassDTO> saleClassDtoCache;

    @Autowired
    private Cache<Long, List<SaleClassDTO>> saleClassDtoListCache;

    @Autowired
    private Cache<Long, BrandDTO> brandDtoCache;

    @Autowired
    private Cache<Long, BasicSettingPolicyDTO> basicSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, AppletAgreementPolicyDTO> appletAgreementPolicyDTOCache;

    @Autowired
    private Cache<Long, AreaItemDTO> areaItemDTOCache;

    @Autowired
    private Cache<Long, SupplierItemDTO> supplierItemDTOCache;

    @Autowired
    private Cache<Long, SkuDTO> skuDTOCache;

    @Autowired
    private Cache<Long, SpuDTO> spuDTOCache;

    @Autowired
    private Cache<String, CatgoryRateDTO> catgoryRateCache;

    @Autowired
    private Cache<Long, AppletBaseConfigDTO> appletBaseConfigDTOCache;

    @Autowired
    @Qualifier("areaSalePriceCodeCache")
    private Cache<String, Integer> areaSalePriceCodeCache;

    @Autowired
    @Qualifier("supplierSalePriceCodeCache")
    private Cache<String, Integer> supplierSalePriceCodeCache;

    @Autowired
    private Cache<String, SkuPriceDTO> skuPriceDTOByAreaTypeCache;

    @Autowired
    private Cache<Long, ColonelDTO> colonelDTOCache;

    @Autowired
    private Cache<String, List<SkuUnitGroupDTO>> skuUnitGroupDTOCache;

    @Autowired
    @Qualifier("visualSettingMasterBySupplierIdCache")
    private Cache<Long, VisualSettingMasterDto> visualSettingMasterBySupplierIdCache;

    @Autowired
    private Cache<Long, PayConfigDTO> payConfigDTOCache;

    @Autowired
    private Cache<Long, OpensourceDto> opensourceDtoByMerchantIdCache;

    @Autowired
    private Cache<Long, PrmActivityDTO> activityDTOCache;

    @Autowired
    private Cache<Long, SpuCombineDTO> spuCombineCache;

    @Autowired
    private Cache<Long, List<ActivitySpuScopeDTO>> activitySpuScopeCache;

    @Autowired
    private Cache<Long, List<SkRuleDTO>> activitySkRuleCache;

    @Autowired
    private Cache<Long, List<SpRuleDTO>> activitySpRuleCache;

    @Resource
    private PartnerApi partnerApi;

    @Resource
    private AreaApi areaApi;

    @Resource
    private ChannelApi channelApi;

    @Resource
    private BranchApi branchApi;

    @Resource
    private SupplierApi supplierApi;

    @Resource
    private DcApi dcApi;

    @Resource
    private AreaClassApi areaClassApi;

    @Resource
    private CatgoryApi catgoryApi;

    @Resource
    private SaleClassApi saleClassApi;

    @Resource
    private BrandApi brandApi;

    @Resource
    private PartnerPolicyApi partnerPolicyApi;

    @Resource
    private AreaItemApi areaItemApi;

    @Resource
    private SupplierItemApi supplierItemApi;

    @Resource
    private SpuApi spuApi;

    @Resource
    private SkuApi skuApi;

    @Resource
    private PartnerConfigApi partnerConfigApi;

    @Resource
    private AreaChannelPriceApi areaChannelPriceApi;

    @Resource
    private SupplierGrouplPriceApi supplierGrouplPriceApi;

    @Resource
    private SkuPriceApi skuPriceApi;

    @Resource
    private ColonelApi colonelApi;

    @Resource
    private OpensourceApi opensourceApi;

    @Resource
    private ActivityApi activityApi;

    @Autowired
    private PrdtSpuCombineMapper prdtSpuCombineMapper;

    private static final String WX_SESSION_KEY = "wx_session_key:";

    @PostConstruct
    public void init() {
        //自动load（read through）
        partnerDtoCache.config().setLoader(this::loadPartnerDtoFromApi);
        branchDTOCache.config().setLoader(this::loadBranchDTOFromApi);
        areaDtoCache.config().setLoader(this::loadAreaDtoFromApi);
        channelDTOCache.config().setLoader(this::loadChannelDTOFromApi);
        supplierDTOCache.config().setLoader(this::loadSupplierDTOFromApi);
        dcDtoCache.config().setLoader(this::loadDcDtoFromApi);
        areaClassDtoCache.config().setLoader(this::loadAreaClassDtoFromApi);
        areaClassBranchListDtoCache.config().setLoader(this::loadAreaClassBranchListDtoFromApi);
        catgoryDtoCache.config().setLoader(this::loadCatgoryDtoFromApi);
        saleClassDtoCache.config().setLoader(this::loadSaleClassDtoFromApi);
        saleClassDtoListCache.config().setLoader(this::loadSaleClassDtoListFromApi);
        brandDtoCache.config().setLoader(this::loadBrandDtoFromApi);
        basicSettingPolicyDTOCache.config().setLoader(this::loadBasicSettingPolicyDtoFromApi);
        appletAgreementPolicyDTOCache.config().setLoader(this::loadAppletAgreementDTOFromApi);
        areaItemDTOCache.config().setLoader(this::loadAreaItemDtoFromApi);
        supplierItemDTOCache.config().setLoader(this::loadSupplierItemDtoFromApi);
        spuDTOCache.config().setLoader(this::loadSpuDtoFromApi);
        skuDTOCache.config().setLoader(this::loadSkuDtoFromApi);
        appletBaseConfigDTOCache.config().setLoader(this::loadAppletBaseConfigDtoFromApi);
        areaSalePriceCodeCache.config().setLoader(this::loadAreaChannelSalePriceCodeFromApi);
        supplierSalePriceCodeCache.config().setLoader(this::loadSupplierGroupSalePriceCodeFromApi);
        skuPriceDTOByAreaTypeCache.config().setLoader(this::loadSkuPriceDTOByAreaTypeFromApi);
        catgoryRateCache.config().setLoader(this::loadCatgoryRateCache);
        colonelDTOCache.config().setLoader(this::loadColonelDtoFromApi);
        payConfigDTOCache.config().setLoader(this::loadPayConfigDTOFromApi);
        opensourceDtoByMerchantIdCache.config().setLoader(this::loadOpensourceByMerchantIdFromApi);
        visualSettingMasterBySupplierIdCache.config().setLoader(this::loadVisualSettingMasterBySupplierIdFromApi);
        activityDTOCache.config().setLoader(this::loadActivityDTOFromApi);
        spuCombineCache.config().setLoader(this::loadSpuCombineDTOFromService);
        activitySpuScopeCache.config().setLoader(this::loadActivitySpuScopeListFromApi);
        activitySkRuleCache.config().setLoader(this::loadActivitySkRuleListFromApi);
        activitySpRuleCache.config().setLoader(this::loadActivitySpRuleListFromApi);
    }

    private List<SpRuleDTO> loadActivitySpRuleListFromApi(Long activityId) {
        return activityApi.getActivitySpRule(activityId).getCheckedData();
    }

    private List<SkRuleDTO> loadActivitySkRuleListFromApi(Long activityId) {
        return activityApi.getActivitySkRule(activityId).getCheckedData();
    }

    private List<ActivitySpuScopeDTO> loadActivitySpuScopeListFromApi(Long activityId) {
        List<ActivitySpuScopeDTO> result = activityApi.getActivitySpuScopeList(activityId).getCheckedData();
        if (Objects.isNull(result)) {
            result = new ArrayList<>();
        }
        return result;
    }


    private SpuCombineDTO loadSpuCombineDTOFromService(Long spuCombineId) {
        SpuCombineDTO spuCombine = prdtSpuCombineMapper.selectSpuCombineDTO(spuCombineId);
        spuCombine.setCombineDtls(prdtSpuCombineMapper.selectSpuCombineDtlList(spuCombineId));
        return spuCombine;
    }

    private PrmActivityDTO loadActivityDTOFromApi(Long activityId) {
        return activityApi.getActivityDto(activityId).getCheckedData();
    }

    private AppletAgreementPolicyDTO loadAppletAgreementDTOFromApi(Long sysCode) {
        return partnerPolicyApi.getAppletAgreementPolicy(sysCode).getCheckedData();
    }

    private PayConfigDTO loadPayConfigDTOFromApi(Long sysCode) {
        return partnerConfigApi.getPayConfig(sysCode).getCheckedData();
    }

    private OpensourceDto loadOpensourceByMerchantIdFromApi(Long merchantId) {
        OpensourceDto opensourceDto = opensourceApi.getOpensourceByMerchantId(merchantId).getCheckedData();
        if (Objects.isNull(opensourceDto)) {
            return new OpensourceDto();
        }
        return opensourceDto;
    }

    /**
    * @Description: 获取渠道绑定的城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/27 9:43
    */
    private CatgoryRateDTO loadCatgoryRateCache(String cacheKey) {
        String[] infos = cacheKey.split(":");
        return catgoryApi.getCatgoryByIdAndAreaId(Long.parseLong(infos[0]), Long.parseLong(infos[1])).getCheckedData();
    }

    /**
    * @Description: 获取门店展示绑定的展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:37
    */
    private List<AreaClassDTO> loadAreaClassBranchListDtoFromApi(Long branchId) {
        List<Long> supplierIds = branchApi.getSupplierByBranchId(branchId).getCheckedData();
        if (ObjectUtil.isEmpty(supplierIds)){
            return new ArrayList<>();
        }
        return areaClassApi.getAreaClassBranchList(supplierIds).getCheckedData();
    }
    /**
    * @Description: 获取平台展示分类列表
    * @Author: liuxingyu
    * @Date: 2024/3/26 20:27
    */
    private List<SaleClassDTO> loadSaleClassDtoListFromApi(Long sysCode) {
        return saleClassApi.getSaleClassListBySysCode(sysCode).getCheckedData();
    }

    private SupplierDTO loadSupplierDTOFromApi(Long supplierId) {
        return supplierApi.getBySupplierId(supplierId).getCheckedData();
    }

    private ChannelDTO loadChannelDTOFromApi(Long channelId) {
        return channelApi.getByChannelId(channelId).getCheckedData();
    }

    private AreaDTO loadAreaDtoFromApi(Long areaId) {
        return areaApi.getAreaByAreaId(areaId).getCheckedData();
    }

    private BranchDTO loadBranchDTOFromApi(Long branchId) {
        return branchApi.getByBranchId(branchId).getCheckedData();
    }

    private PartnerDto loadPartnerDtoFromApi(String key) {
        return partnerApi.getPartnerBySource(key).getCheckedData();
    }

    private DcDTO loadDcDtoFromApi(Long dcId) {
        return dcApi.getDcById(dcId).getCheckedData();
    }

    private AreaClassDTO loadAreaClassDtoFromApi(Long areaClassId) {
        return areaClassApi.getAreaClassByAreaClassId(areaClassId).getCheckedData();
    }

    private CatgoryDTO loadCatgoryDtoFromApi(Long catgoryId) {
        return catgoryApi.getCatgoryByCatgoryId(catgoryId).getCheckedData();
    }

    private SaleClassDTO loadSaleClassDtoFromApi(Long saleClassId) {
        return saleClassApi.getSaleClassBySaleClassId(saleClassId).getCheckedData();
    }

    private AppletBaseConfigDTO loadAppletBaseConfigDtoFromApi(Long sysCode){
        return partnerConfigApi.getAppletBaseConfig(sysCode).getCheckedData();
    }

    private BrandDTO loadBrandDtoFromApi(Long brandId) {
        return brandApi.getBrandByBrandId(brandId).getCheckedData();
    }

    private BasicSettingPolicyDTO loadBasicSettingPolicyDtoFromApi(Long dcId) {
        return partnerPolicyApi.getBasicSettingPolicy(dcId).getCheckedData();
    }


    private AreaItemDTO loadAreaItemDtoFromApi(Long areaItemId) {return areaItemApi.getAreaItemId(areaItemId).getCheckedData();}

    private SupplierItemDTO loadSupplierItemDtoFromApi(Long supplierItemId){return supplierItemApi.getBySupplierItemId(supplierItemId).getCheckedData();}

    private SpuDTO loadSpuDtoFromApi(Long spuId){return spuApi.getBySpuId(spuId).getCheckedData();}

    private SkuDTO loadSkuDtoFromApi(Long skuId){return skuApi.getBySkuId(skuId).getCheckedData();}

    private Integer loadAreaChannelSalePriceCodeFromApi(String key){
        return areaChannelPriceApi.getPriceByKey(key).getCheckedData();
    }

    private Integer loadSupplierGroupSalePriceCodeFromApi(String key){
        return supplierGrouplPriceApi.getPriceByKey(key).getCheckedData();
    }

    private SkuPriceDTO loadSkuPriceDTOByAreaTypeFromApi(String key){
        return skuPriceApi.getSkuPriceByKey(key).getCheckedData();
    }
    private ColonelDTO loadColonelDtoFromApi(Long colonelId){return colonelApi.getByColonelId(colonelId).getCheckedData();}

    private VisualSettingMasterDto loadVisualSettingMasterBySupplierIdFromApi(Long supplierId) {
        VisualSettingMasterDto visualSettingMasterDto = opensourceApi.getVisualSettingMasterByMerchantId(supplierId).getCheckedData();
        if (Objects.isNull(visualSettingMasterDto)) {
            return new VisualSettingMasterDto();
        }
        return visualSettingMasterDto;
    }

    private DcDTO loadDcDTO(Long dcId){
        return dcApi.getDcById(dcId).getCheckedData();
    }
    @Override
    public void setWxSessionKey(String key, String sessionKey) {
        redisService.setCacheObject(WX_SESSION_KEY + key, sessionKey);
        redisService.expire(WX_SESSION_KEY + key, DAY_SECONDS);
    }

    @Override
    public String getWxSessionKey(String key) {
        return redisService.getCacheObject(WX_SESSION_KEY + key);
    }

    @Override
    public PartnerDto getPartnerDto(String key) {
        return partnerDtoCache.get(key);
    }

    @Override
    public BranchDTO getBranchDto(Long branchId) {
        return branchDTOCache.get(branchId);
    }

    @Override
    public ChannelDTO getChannelDto(Long channelId) {
        return channelDTOCache.get(channelId);
    }

    @Override
    public AreaDTO getAreaDto(Long areaId) {
        if (Objects.isNull(areaId))
            return null;
        return areaDtoCache.get(areaId);
    }

    @Override
    public SupplierDTO getSupplierDTO(Long supplierId) {
        if (Objects.isNull(supplierId)) {
            return null;
        }
        return supplierDTOCache.get(supplierId);
    }

    @Override
    public BasicSettingPolicyDTO getBasicSettingPolicyDTO(Long dcId) {
        return basicSettingPolicyDTOCache.get(dcId);
    }

    @Override
    public AreaItemDTO getAreaItemDTO(Long areaItemId) {
        return areaItemDTOCache.get(areaItemId);
    }

    @Override
    public SupplierItemDTO getSupplierItemDTO(Long supplierItemId) {
        return supplierItemDTOCache.get(supplierItemId);
    }

    @Override
    public SpuDTO getSpuDTO(Long spuId) {
        return spuDTOCache.get(spuId);
    }

    @Override
    public SkuDTO getSkuDTO(Long skuId) {
        return skuDTOCache.get(skuId);
    }
    /**
    * @Description: 获取门店绑定的城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/28 18:59
    */
    @Override
    public List<AreaClassDTO> getAreaClassBranch(Long branchId) {
        return areaClassBranchListDtoCache.get(branchId);
    }

    /**
     * 获取管理分类分润比例
     * @param catgoryId 管理分类ID
     * @param areaId    区域城市ID
     * @return
     */
    @Override
    public CatgoryRateDTO getCatgoryByIdAndAreaId(Long catgoryId, Long areaId) {
        return catgoryRateCache.get(RedisConstants.getCategoryRate(catgoryId, areaId));
    }

    /**
    * @Description: 获取平台展示分类列表
    * @Author: liuxingyu
    * @Date: 2024/3/26 20:30
    */
    @Override
    public List<SaleClassDTO> getSaleClassListBySysCode(Long sysCode) {
        return saleClassDtoListCache.get(sysCode);
    }

    /**
    * @Description: 获取渠道绑定的城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/27 9:51
    */


    @Override
    public AppletBaseConfigDTO getAppletBaseConfigDTO(Long sysCode) {
        return appletBaseConfigDTOCache.get(sysCode);
    }

    @Override
    public Integer getAreaSalePriceCodeCache(String key) {
        return areaSalePriceCodeCache.get(key);
    }

    @Override
    public Integer getSupplierSalePriceCodeCache(String key) {
        return supplierSalePriceCodeCache.get(key);
    }

    @Override
    public SkuPriceDTO getSkuPriceDTOByAreaTypeCache(String key) {
        return skuPriceDTOByAreaTypeCache.get(key);
    }
    @Override
    public ColonelDTO getColonel(Long colonelId) {
        return colonelDTOCache.get(colonelId);
    }

    @Override
    public BrandDTO getBrandDTO(Long brandId) {
        return brandDtoCache.get(brandId);
    }

    @Override
    public CatgoryDTO getCatgoryDTO(Long catgoryId) {
        return catgoryDtoCache.get(catgoryId);
    }

    @Override
    public void setSkuUnitGroup(Long spuId, Long areaId, Long classId, ProductType productType) {
        List<SkuUnitGroupDTO> groupList = SpringUtils.getBean(IPrdtSpuService.class).getSkuUnitGroupList(spuId, areaId, classId, productType.getType());
        if (groupList.isEmpty()) {
            return;
        }
        skuUnitGroupDTOCache.put(
                RedisConstants.getSkuUnitKey(spuId, areaId, classId, productType.getType()),
                groupList
        );
    }

    @Override
    public void removeSkuUnitGroup(Long spuId, Long areaId, Long classId, ProductType productType) {
        skuUnitGroupDTOCache.remove(RedisConstants.getSkuUnitKey(spuId, areaId, classId, productType.getType()));
    }

    @Override
    /**
     * {@link com.zksr.common.redis.bean.VisualSettingMasterBySupplierIdBean#getVisualSettingMasterBySupplierIdCache }
     * {@link ProductCacheServiceImpl#loadVisualSettingMasterBySupplierIdFromApi}
     */
    public VisualSettingMasterDto getVisualMasterBySupplierId(Long supplierId) {
        return visualSettingMasterBySupplierIdCache.get(supplierId);
    }

    @Override
    public PayConfigDTO getPayConfigDTO(Long sysCode) {
        if (Objects.isNull(sysCode)) {
            return null;
        }
        return payConfigDTOCache.get(sysCode);
    }

    @Override
    public OpensourceDto getOpensourceByMerchantId(Long merchantId) {
        return opensourceDtoByMerchantIdCache.get(merchantId);
    }

    @Override
    public DcDTO getDcDTO(Long dcId) {
        if (Objects.isNull(dcId))
            return null;
        return dcDtoCache.get(dcId);
    }

    @Override
    public AppletAgreementPolicyDTO getAppletAgreementPolicyDTO(Long sysCode) {
        return appletAgreementPolicyDTOCache.get(sysCode);
    }

    @Override
    public PrmActivityDTO getActivityDTO(Long activityId) {
        if (Objects.isNull(activityId)) {
            return null;
        }
        return activityDTOCache.get(activityId);
    }

    @Override
    public SpuCombineDTO getSpuCombineDTO(Long spuCombineId) {
        if (Objects.isNull(spuCombineId)) {
            return null;
        }
        return spuCombineCache.get(spuCombineId);
    }

    @Override
    public List<ActivitySpuScopeDTO> getActivitySpuScopeList(Long activityId) {
        return activitySpuScopeCache.get(activityId);
    }

    @Override
    public List<SkRuleDTO> getActivitySkRuleList(Long activityId) {
        return activitySkRuleCache.get(activityId);
    }

    @Override
    public List<SpRuleDTO> getActivitySpRuleList(Long activityId) {
        return activitySpRuleCache.get(activityId);
    }

}
