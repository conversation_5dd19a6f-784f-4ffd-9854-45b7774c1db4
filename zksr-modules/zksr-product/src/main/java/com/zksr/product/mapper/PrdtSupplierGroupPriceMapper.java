package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.controller.supplierGroupPrice.vo.PrdtSupplierGroupPriceRespVO;
import com.zksr.product.domain.PrdtSupplierGroupPrice;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtSupplierGroupPrice;
import com.zksr.product.controller.supplierGroupPrice.vo.PrdtSupplierGroupPricePageReqVO;

import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_AREA_CHANNEL_PRICE_EXISTS;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_SUPPLIER_GROUP_PRICE_EXISTS;


/**
 * 平台商城市分组价格Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Mapper
public interface PrdtSupplierGroupPriceMapper extends BaseMapperX<PrdtSupplierGroupPrice> {
    default PageResult<PrdtSupplierGroupPrice> selectPage(PrdtSupplierGroupPricePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtSupplierGroupPrice>()
                    .eqIfPresent(PrdtSupplierGroupPrice::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtSupplierGroupPrice::getGroupId, reqVO.getGroupId())
                    .eqIfPresent(PrdtSupplierGroupPrice::getAreaId, reqVO.getAreaId())
                    .eqIfPresent(PrdtSupplierGroupPrice::getSalePriceCode, reqVO.getSalePriceCode())
                .orderByDesc(PrdtSupplierGroupPrice::getSysCode));
    }

    /**
     * 删除平台商城市分组价格信息
     * @param groupId  平台商城市分组id
     * @param areaId  城市id
     */
    default void deleteSupplierGroupPrice(Long groupId, Long areaId){
        delete(new LambdaQueryWrapper<PrdtSupplierGroupPrice>()
                .eq(PrdtSupplierGroupPrice::getGroupId,groupId)
                .eq(PrdtSupplierGroupPrice::getAreaId, areaId));
    }

    default Long selectSupplierGroupPriceByCheck(PrdtSupplierGroupPrice price){
        //校验是否存在
        LambdaQueryWrapper<PrdtSupplierGroupPrice> wrapper = new LambdaQueryWrapperX<PrdtSupplierGroupPrice>()
                .eq(PrdtSupplierGroupPrice::getAreaId, price.getAreaId())
                .eq(PrdtSupplierGroupPrice::getGroupId, price.getGroupId())
                .eq(PrdtSupplierGroupPrice::getSalePriceCode, price.getSalePriceCode())
                .eqIfPresent(PrdtSupplierGroupPrice::getSysCode,price.getSysCode());
        return selectCount(wrapper);
    }

    default Long insertPrdtSupplierGroupPrice(PrdtSupplierGroupPrice price){
        //校验是否存在
        Long checkPrice = selectSupplierGroupPriceByCheck(price);
        if(Objects.nonNull(checkPrice) && checkPrice > 0){
            throw exception(PRDT_SUPPLIER_GROUP_PRICE_EXISTS);
        }
        // 插入
        insert(price);
        return price.getAreaId();
    }

    default PrdtSupplierGroupPrice selectSupplierGroupPrice(PrdtSupplierGroupPriceRespVO respVO){
        //校验是否存在
        LambdaQueryWrapperX<PrdtSupplierGroupPrice> wrapper = new LambdaQueryWrapperX<PrdtSupplierGroupPrice>()
                .eq(PrdtSupplierGroupPrice::getAreaId, respVO.getAreaId())
                .eq(PrdtSupplierGroupPrice::getGroupId, respVO.getGroupId())
                .eqIfPresent(PrdtSupplierGroupPrice::getSysCode,respVO.getSysCode())
                .last(StringPool.LIMIT_ONE)
                ;
        return selectOne(wrapper);
    }
}
