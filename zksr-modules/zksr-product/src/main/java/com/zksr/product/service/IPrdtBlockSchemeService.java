package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSchemeRespVO;
import com.zksr.product.domain.PrdtBlockScheme;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSchemePageReqVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSchemeSaveReqVO;

import java.util.List;

/**
 * 经营屏蔽方案Service接口
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
public interface IPrdtBlockSchemeService {

    /**
     * 新增经营屏蔽方案
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtBlockScheme(@Valid PrdtBlockSchemeSaveReqVO createReqVO);

    /**
     * 修改经营屏蔽方案
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtBlockScheme(@Valid PrdtBlockSchemeSaveReqVO updateReqVO);

    /**
     * 删除经营屏蔽方案
     *
     * @param blockSchemeId 主键
     */
    public void deletePrdtBlockScheme(Long blockSchemeId);

    /**
     * 批量删除经营屏蔽方案
     *
     * @param blockSchemeIds 需要删除的经营屏蔽方案主键集合
     * @return 结果
     */
    public void deletePrdtBlockSchemeByBlockSchemeIds(List<Long> blockSchemeIds);

    /**
     * 获得经营屏蔽方案
     *
     * @param blockSchemeId 主键
     * @return 经营屏蔽方案
     */
    public PrdtBlockSchemeRespVO getPrdtBlockScheme(Long blockSchemeId);

    /**
     * 获得经营屏蔽方案分页
     *
     * @param pageReqVO 分页查询
     * @return 经营屏蔽方案分页
     */
    PageResult<PrdtBlockScheme> getPrdtBlockSchemePage(PrdtBlockSchemePageReqVO pageReqVO);

    void batchEnable(List<Long> blockSchemeIds);

    void batchDisable(List<Long> blockSchemeIds);

    /**
     * 根据客户编码获取被屏蔽的商品
     * @param branchId 客户编码
     * @return 被屏蔽的skuIds
     */
    List<Long> getBlockSkusByBranchId(Long branchId);
}
