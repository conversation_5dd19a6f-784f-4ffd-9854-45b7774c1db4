package com.zksr.product.controller.blockScheme.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import java.util.List;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 经营屏蔽方案对象 prdt_block_scheme
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@ApiModel("经营屏蔽方案 - prdt_block_scheme分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtBlockSchemePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty(value = "备注")
    private Long blockSchemeId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 方案编码 */
    @Excel(name = "方案编码")
    @ApiModelProperty(value = "方案编码", required = true)
    private String schemeNo;

    /** 方案名称 */
    @Excel(name = "方案名称")
    @ApiModelProperty(value = "方案名称", required = true)
    private String schemeName;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id列表", required = true)
    private List<Long> areaId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id", required = true)
    private Long supplierId;

    /** 状态：1正常 0禁用 */
    @Excel(name = "状态：1正常 0禁用")
    @ApiModelProperty(value = "状态：1正常 0禁用", required = true)
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", required = true)
    private String memo;

    /** 删除标志 0 未删除 1 已删除 */
    @ApiModelProperty(value = "备注", required = true)
    private Integer delFlag;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人", required = true)
    private String createBy;

    /** 商品skuId数组，多选，精确查询 */
    @ApiModelProperty(value = "商品skuId数组，多选，精确查询", required = true)
    private List<Long> skuIds;

    /** 门店branchId数组，多选，精确查询 */
    @ApiModelProperty(value = "门店branchId数组，多选，精确查询", required = true)
    private List<Long> branchIds;

    /** 运营商ID */
    @ApiModelProperty(value = "门店branchId数组，多选，精确查询", required = true)
    private Long dcId;

}
