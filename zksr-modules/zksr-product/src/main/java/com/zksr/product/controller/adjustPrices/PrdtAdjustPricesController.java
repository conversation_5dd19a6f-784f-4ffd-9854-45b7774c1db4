package com.zksr.product.controller.adjustPrices;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.product.controller.adjustPrices.excel.AdjustPricesImportExcel;
import com.zksr.product.convert.adjustPrices.PrdtAdjustPricesDtlConvert;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.service.IPrdtAdjustPricesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesPageReqVO;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesSaveReqVO;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesRespVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.product.constant.ProductConstant.PRDT_ADD_TYPE_0;
import static com.zksr.product.constant.ProductConstant.PRDT_ADD_TYPE_1;

/**
 * 商品调价单主Controller
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Api(tags = "管理后台 - 商品调价单主接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/prices")
public class PrdtAdjustPricesController {
    @Autowired
    private IPrdtAdjustPricesService prdtAdjustPricesService;

    /**
     * 新增商品调价单主
     */
    @ApiOperation(value = "新增商品调价单主", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "商品调价单主", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<String> add(@Valid @RequestBody PrdtAdjustPricesSaveReqVO createReqVO) {
        return success(String.valueOf(prdtAdjustPricesService.insertPrdtAdjustPrices(createReqVO,PRDT_ADD_TYPE_0)));
    }

    /**
     * 修改商品调价单主
     */
    @ApiOperation(value = "修改商品调价单主", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "商品调价单主", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtAdjustPricesSaveReqVO updateReqVO) {
            prdtAdjustPricesService.updatePrdtAdjustPrices(updateReqVO);
        return success(true);
    }

    /**
     * 删除商品调价单主
     */
    @ApiOperation(value = "删除商品调价单主", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "商品调价单主", businessType = BusinessType.DELETE)
    @DeleteMapping("/{adjustPricesId}")
    public CommonResult<Boolean> remove(@PathVariable Long adjustPricesId) {
        prdtAdjustPricesService.deletePrdtAdjustPrices(adjustPricesId);
        return success(true);
    }


    /**
     * 获取商品调价单主详细信息
     */
    @ApiOperation(value = "获得商品调价单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "getInfo/{adjustPricesId}")
    public CommonResult<PrdtAdjustPricesRespVO> getInfo(@PathVariable("adjustPricesId") Long adjustPricesId) {
        return success(prdtAdjustPricesService.getPrdtAdjustPrices(adjustPricesId));
    }


    /**
     * 分页查询商品调价单主
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得商品调价单主分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(dcFieldAlias = SystemConstants.DC_ID)
    public CommonResult<PageResult<PrdtAdjustPricesRespVO>> getPage(@Valid PrdtAdjustPricesPageReqVO pageReqVO) {
        return success(prdtAdjustPricesService.getPrdtAdjustPricesPage(pageReqVO));
    }

    @ApiOperation(value = "商品调价单批量审核", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.APPROVE)
    @Log(title = "商品调价单批量审核", businessType = BusinessType.UPDATE)
    @RequiresPermissions(Permissions.APPROVE)
    @PostMapping("/approveBatch")
    public CommonResult<Boolean> approveBatch(@RequestBody Long[] adjustPricesIds) throws IOException
    {
        for (Long adjustPricesId : adjustPricesIds) {
            prdtAdjustPricesService.approve(adjustPricesId);
        }
        return success(true);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "获取商品调价导入模板", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<AdjustPricesImportExcel> util = new ExcelUtil<AdjustPricesImportExcel>(AdjustPricesImportExcel.class);
        util.importTemplateExcel(response, "商品调价导入模板", StringUtils.EMPTY, null);
    }

    @ApiOperation(value = "导入商品调价单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORT)
    @Log(title = "导入商品调价单", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/importData")
    public CommonResult<String> importData( MultipartFile file,  PrdtAdjustPricesSaveReqVO saveReqVO) throws Exception
    {
        ExcelUtil<AdjustPricesImportExcel> util = new ExcelUtil<>(AdjustPricesImportExcel.class);
        List<AdjustPricesImportExcel> importList = util.importExcel(file.getInputStream());
        saveReqVO.setDtlList(PrdtAdjustPricesDtlConvert.INSTANCE.convertSaveReqList(importList));
        return success(String.valueOf(prdtAdjustPricesService.insertPrdtAdjustPrices(saveReqVO,PRDT_ADD_TYPE_1)));
    }



    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:prices:add";
        /** 编辑 */
        public static final String EDIT = "product:prices:edit";
        /** 删除 */
        public static final String DELETE = "product:prices:remove";
        /** 列表 */
        public static final String LIST = "product:prices:list";
        /** 查询 */
        public static final String GET = "product:prices:query";
        /** 停用 */
        public static final String DISABLE = "product:prices:disable";
        /** 启用 */
        public static final String ENABLE = "product:prices:enable";

        /** 审核 */
        public static final String APPROVE = "product:prices:approve";

        /** 导入 */
        public static final String IMPORT = "product:prices:import";

        /** 导出 */
        public static final String EXPORT = "product:prices:export";
    }
}
