package com.zksr.product.convert.adjustPrices;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.controller.adjustPrices.excel.AdjustPricesImportExcel;
import com.zksr.product.domain.PrdtAdjustPricesDtl;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesDtlRespVO;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesDtlSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 商品调价单明细 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-01
*/
@Mapper
public interface PrdtAdjustPricesDtlConvert {

    PrdtAdjustPricesDtlConvert INSTANCE = Mappers.getMapper(PrdtAdjustPricesDtlConvert.class);

    PrdtAdjustPricesDtlRespVO convert(PrdtAdjustPricesDtl prdtAdjustPricesDtl);

    PrdtAdjustPricesDtl convert(PrdtAdjustPricesDtlSaveReqVO prdtAdjustPricesDtlSaveReq);

    PageResult<PrdtAdjustPricesDtlRespVO> convertPage(PageResult<PrdtAdjustPricesDtl> prdtAdjustPricesDtlPage);

    List<PrdtAdjustPricesDtlSaveReqVO> convertSaveReqList(List<AdjustPricesImportExcel> AdjustPricesImportExcelList);

    List<PrdtAdjustPricesDtlRespVO> convertRespVoList(List<PrdtAdjustPricesDtl> prdtAdjustPricesDtls);
}