package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.controller.propertyVal.vo.PrdtPropertyValRespVO;
import com.zksr.product.domain.PrdtProperty;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtPropertyVal;
import com.zksr.product.controller.propertyVal.vo.PrdtPropertyValPageReqVO;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.*;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_PROPERTY_VAL_EXISTS;


/**
 * 规格值Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Mapper
public interface PrdtPropertyValMapper extends BaseMapperX<PrdtPropertyVal> {
    default PageResult<PrdtPropertyVal> selectPage(PrdtPropertyValPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtPropertyVal>()
                    .eqIfPresent(PrdtPropertyVal::getPropertyValId, reqVO.getPropertyValId())
                    .eqIfPresent(PrdtPropertyVal::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtPropertyVal::getSupplierId, reqVO.getSupplierId())
                    .eqIfPresent(PrdtPropertyVal::getPropertyId, reqVO.getPropertyId())
                    .likeIfPresent(PrdtPropertyVal::getName, reqVO.getName())
                    .eqIfPresent(PrdtPropertyVal::getIsDelete, reqVO.getIsDelete())
                    .eqIfPresent(PrdtPropertyVal::getMemo, reqVO.getMemo())
                    .eqIfPresent(PrdtPropertyVal::getStatus, reqVO.getStatus())
                .orderByDesc(PrdtPropertyVal::getPropertyValId));
    }

    //校验是否规格重名/根据spu_id 、名称、是否删除查询
    default PrdtPropertyVal selectPropertyValByCheck(PrdtPropertyVal val){
        //校验是否规格重名
        QueryWrapper<PrdtPropertyVal> wrapper = new QueryWrapper<>();
        wrapper.eq("spu_id", val.getSpuId());
        wrapper.eq("name", val.getName());
        wrapper.eq("is_delete", PRDT_IS_DELETE_0);
        wrapper.eq("property_id",val.getPropertyId());
        return selectOne(wrapper);
    }

    default Long insertPrdtPropertyVal(PrdtPropertyVal prdtPropertyVal){
        //校验是否规格重名
        PrdtPropertyVal checkPrdtPropertyVal = selectPropertyValByCheck(prdtPropertyVal);
        if(ToolUtil.isNotEmpty(checkPrdtPropertyVal)){
            throw exception(PRDT_PROPERTY_VAL_EXISTS);
        }
        //设置默认值
        prdtPropertyVal.setIsDelete(PRDT_IS_DELETE_0);
        prdtPropertyVal.setStatus(PRDT_STATUS_1);
        // 插入
        insert(prdtPropertyVal);
        // 返回
        return prdtPropertyVal.getPropertyValId();
    }

    //根据查询条件查询规格名称集合
    default List<PrdtPropertyVal> selectPropertyValByList(PrdtPropertyValRespVO vo){
        LambdaQueryWrapperX<PrdtPropertyVal> wrapper = new LambdaQueryWrapperX<PrdtPropertyVal>()
                .eqIfPresent(PrdtPropertyVal::getPropertyId,vo.getPropertyId())
                .eqIfPresent(PrdtPropertyVal::getSpuId,vo.getSpuId())
                .eqIfPresent(PrdtPropertyVal::getIsDelete, vo.getIsDelete())
                .eqIfPresent(PrdtPropertyVal::getStatus,vo.getStatus());
        return selectList(wrapper);
    }

    default long deleteByIds(List<Long> propertyValIds){
        return update(null, new LambdaUpdateWrapper<PrdtPropertyVal>()
                .set(PrdtPropertyVal::getIsDelete,PRDT_IS_DELETE_1)
                .in(PrdtPropertyVal::getPropertyValId,propertyValIds));
    }

    default List<PrdtPropertyVal> selectBySpuId(Long spuId) {
        LambdaQueryWrapperX<PrdtPropertyVal> wrapper = new LambdaQueryWrapperX<PrdtPropertyVal>()
                .eqIfPresent(PrdtPropertyVal::getSpuId, spuId)
                .eqIfPresent(PrdtPropertyVal::getIsDelete, PRDT_IS_DELETE_0);
        return selectList(wrapper);
    }
}
