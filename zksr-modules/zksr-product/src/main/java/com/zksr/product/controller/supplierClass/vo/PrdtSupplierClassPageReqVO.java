package com.zksr.product.controller.supplierClass.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 入驻商-平台商管理分类 关联关系对象 prdt_supplier_class
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@ApiModel("入驻商-平台商管理分类 关联关系 - prdt_supplier_class分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
public class PrdtSupplierClassPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商管理分类id;平台商管理分类id */
    @Excel(name = "平台商管理分类id;平台商管理分类id")
    @ApiModelProperty(value = "平台商管理分类id;平台商管理分类id")
    private Long catgoryId;

    /** 入驻商id;入驻商id */
    @Excel(name = "入驻商id;入驻商id")
    @ApiModelProperty(value = "入驻商id;入驻商id")
    private Long supplierId;

    /** 平台商id;平台商id */
    @Excel(name = "平台商id;平台商id")
    @ApiModelProperty(value = "平台商id;平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "管理分类名称")
    private String catgoryName;

    @ApiModelProperty(value = "状态：0：停用，1：启用")
    private String status;

}
