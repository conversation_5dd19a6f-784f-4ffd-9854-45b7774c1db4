package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.domain.PrdtChannelAreaClass;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 渠道展示分类关联Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-27
 */
@Mapper
public interface PrdtChannelAreaClassMapper extends BaseMapperX<PrdtChannelAreaClass> {

    /**
    * @Description: 城市展示分类绑定渠道
    * @Author: liuxingyu
    * @Date: 2024/3/27 11:03
    */
    default void bindChannel(Long areaClassId, List<Long> channelIds){
        //先删除绑定关系
        delete(new LambdaUpdateWrapper<PrdtChannelAreaClass>().eq(PrdtChannelAreaClass::getAreaClassId,areaClassId));
        //新增绑定关系
        List<PrdtChannelAreaClass> channelAreaClassList = channelIds.stream()
                .map(x -> new PrdtChannelAreaClass(SecurityUtils.getLoginUser().getSysCode(), areaClassId, x))
                .collect(Collectors.toList());
        insertBatch(channelAreaClassList);
    }

    /**
    * @Description: 根据城市分类获取绑定信息
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:57
    */
    default List<PrdtChannelAreaClass> getListByAreaClassId(Long areaClassId){
        return selectList(new LambdaQueryWrapper<PrdtChannelAreaClass>().eq(PrdtChannelAreaClass::getAreaClassId,areaClassId));
    }

    /**
    * @Description: 城市展示分类Id获取绑定关系
    * @Author: liuxingyu
    * @Date: 2024/4/15 20:18
    */
    default List<PrdtChannelAreaClass> selectByAreaClassId(Long areaClassId){
        return selectList(new LambdaQueryWrapper<PrdtChannelAreaClass>().eq(PrdtChannelAreaClass::getAreaClassId,areaClassId));
    }

    /**
    * @Description: 根据城市展示分类id集合获取绑定数据
    * @Author: liuxingyu
    * @Date: 2024/4/16 20:23
    */
    default List<PrdtChannelAreaClass> selectListByAreaIds(List<Long> area3Ids){
        return selectList(new LambdaUpdateWrapper<PrdtChannelAreaClass>().in(PrdtChannelAreaClass::getAreaClassId,area3Ids));
    }

    List<Long> getAreaClassChannelList(@Param("areaClassId") Long areaClassId);
}
