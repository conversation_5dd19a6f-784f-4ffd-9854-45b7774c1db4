package com.zksr.product.controller.platform.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 平台商品库-商品SKU对象 prdt_platform_sku
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Data
@ApiModel("平台商品库-商品SKU - prdt_platform_sku分页 Request VO")
public class PrdtPlatformSkuSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 商品库sku id */
    @ApiModelProperty(value = "大单位-国际条码")
    private Long platformSkuId;

    /** 商品库SPU id */
    @Excel(name = "商品库SPU id")
    @ApiModelProperty(value = "商品库SPU id")
    private Long platformSpuId;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "单位-数据字典")
    private Integer unit;

    /** 国际条码 */
    @Excel(name = "国际条码")
    @ApiModelProperty(value = "国际条码")
    private String barcode;

    /** 属性数组，JSON 格式 */
    @Excel(name = "属性数组，JSON 格式")
    @ApiModelProperty(value = "属性数组，JSON 格式")
    private String properties;

    /** 封面图 */
    @Excel(name = "封面图")
    @ApiModelProperty(value = "封面图")
    private String thumb;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Integer status;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否")
    private Integer isDelete;

    /** 中单位-国际条码 */
    @Excel(name = "中单位-国际条码")
    @ApiModelProperty(value = "中单位-国际条码")
    private String midBarcode;

    /** 大单位-国际条码 */
    @Excel(name = "大单位-国际条码")
    @ApiModelProperty(value = "大单位-国际条码")
    private String largeBarcode;

}
