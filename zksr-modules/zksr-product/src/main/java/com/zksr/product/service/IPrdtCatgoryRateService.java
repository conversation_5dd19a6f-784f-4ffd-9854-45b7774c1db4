package com.zksr.product.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRatePageReqVO;
import com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRateRespVO;
import com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRateSaveReqVO;
import com.zksr.product.domain.PrdtCatgoryRate;

import javax.validation.Valid;
import java.util.List;

/**
 * 城市级管理分类扣点设置Service接口
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
public interface IPrdtCatgoryRateService {

    /**
     * 新增城市级管理分类扣点设置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtCatgoryRate(@Valid PrdtCatgoryRateSaveReqVO createReqVO);

    /**
     * 修改城市级管理分类扣点设置
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public Long updatePrdtCatgoryRate(@Valid PrdtCatgoryRateSaveReqVO updateReqVO);

    /**
     * 删除城市级管理分类扣点设置
     *
     * @param catgoryRateId 城市级管理分类扣点设置id
     */
    public void deletePrdtCatgoryRate(Long catgoryRateId);

    /**
     * 批量删除城市级管理分类扣点设置
     *
     * @param catgoryRateIds 需要删除的城市级管理分类扣点设置主键集合
     * @return 结果
     */
    public void deletePrdtCatgoryRateByCatgoryRateIds(Long[] catgoryRateIds);

    /**
     * 获得城市级管理分类扣点设置
     *
     * @param catgoryRateId 城市级管理分类扣点设置id
     * @return 城市级管理分类扣点设置
     */
    public PrdtCatgoryRate getPrdtCatgoryRate(Long catgoryRateId);

    /**
     * 获得城市级管理分类扣点设置分页
     *
     * @param pageReqVO 分页查询
     * @return 城市级管理分类扣点设置分页
     */
    PageResult<PrdtCatgoryRate> getPrdtCatgoryRatePage(PrdtCatgoryRatePageReqVO pageReqVO);

    /**
     * @Description: 商品类别设置二级扣点
     * @Author: liuxingyu
     * @Date: 2024/3/15 9:35
     */
    Long installRate(PrdtCatgoryRateSaveReqVO saveReqVO);

    /**
     * @Description: 运营商获取商品管理分类详情
     * @Author: liuxingyu
     * @Date: 2024/3/15 15:37
     */
    PrdtCatgoryRateRespVO getRateInfo(Long areaId, Long catgoryId);

    /**
    * @Description: 获取设置扣点列表
    * @Author: liuxingyu
    * @Date: 2024/3/19 9:46
    */
    List<PrdtCatgoryRateRespVO> getCatgoryRateList(PrdtCatgoryRateRespVO respVO);
}
