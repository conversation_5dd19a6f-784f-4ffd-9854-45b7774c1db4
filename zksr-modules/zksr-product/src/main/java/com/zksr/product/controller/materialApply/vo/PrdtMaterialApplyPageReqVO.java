package com.zksr.product.controller.materialApply.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 素材应用对象 prdt_material_apply
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@ApiModel("素材应用 - prdt_material_apply分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtMaterialApplyPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 素材应用id */
    @ApiModelProperty(value = "素材应用ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long materialApplyId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 素材id */
    @Excel(name = "素材id")
    @ApiModelProperty(value = "素材id", required = true)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long materialId;

    /** 素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品 */
    @Excel(name = "素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品")
    @ApiModelProperty(value = "素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品", required = true)
    private Integer applyType;

    /** 素材应用类型id */
    @Excel(name = "素材应用类型id")
    @ApiModelProperty(value = "素材应用类型id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long applyId;

    /** 生效时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "生效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "生效时间")
    private Date startTime;

    /** 失效时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "失效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "失效时间")
    private Date endTime;

    /** 操作人 */
    @Excel(name = "操作人")
    @ApiModelProperty(value = "操作人",hidden = true)
    private Long applyUserId;

    /** 素材状态  见常量： PRDT_MATERIAL_APPLY_STATUS_0*/
    @Excel(name = "素材应用状态")
    @ApiModelProperty(value = "素材应用状态 0：未生效 1：生效中 2：已失效 不传值：全部展示")
    private Integer materialApplyStatus;

}
