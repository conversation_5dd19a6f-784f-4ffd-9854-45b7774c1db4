package com.zksr.product.controller.saleClass;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassCopyReqVO;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassPageReqVO;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassRespVO;
import com.zksr.product.controller.saleClass.vo.PrdtSaleClassSaveReqVO;
import com.zksr.product.domain.PrdtSaleClass;
import com.zksr.product.domain.excel.ProductSaleClassImportExcel;
import com.zksr.product.service.IPrdtSaleClassService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.IOException;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台商展示分类Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Api(tags = "管理后台 - 平台商展示分类接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/saleClass")
public class PrdtSaleClassController {
    @Autowired
    private IPrdtSaleClassService prdtSaleClassService;

    /**
     * @Description: 变更平台展示分类状态
     * @Param: PrdtSaleClassSaveReqVO
     * @return: CommonResult<List < PrdtSaleClassRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/5/6 16:18
     */
    @ApiOperation(value = "变更平台展示分类状态", httpMethod = HttpMethod.PUT, notes = "product:saleClass:changeStatus")
    @RequiresPermissions("product:saleClass:changeStatus")
    @Log(title = "平台商展示分类", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public CommonResult<Boolean> changeStatus(@RequestBody PrdtSaleClassSaveReqVO updateReqVO) {
        return success(prdtSaleClassService.changeStatus(updateReqVO));
    }

    /**
     * @Description: 获取平台商展示分类集合
     * @Param: Long saleClassId 非必填,填写后返回数据会剔除主键为传入的ID
     * @return: CommonResult<List < PrdtSaleClassRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/2/27 15:03
     */
    @ApiOperation(value = "获取平台商展示分类集合", httpMethod = HttpMethod.GET)
    @GetMapping("getSaleClassList")
    public CommonResult<List<PrdtSaleClassRespVO>> getSaleClassList(PrdtSaleClassPageReqVO reqVO) {
        return success(prdtSaleClassService.getSaleClassList(reqVO));
    }

    /**
     * @Description: 获取平台商展示分类子级列表(若子级为空则返回自己)
     * @Param: PrdtSaleClassPageReqVO pageReqVO
     * @return: CommonResult<List < PrdtSaleClassRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/2/19 17:33
     */
    @ApiOperation(value = "获取平台商展示分类子级列表", httpMethod = HttpMethod.GET)
    @GetMapping("/getSublevelSaleClassById")
    public CommonResult<PageResult<PrdtSaleClassRespVO>> getSublevelSaleClassById(
            @ApiParam(name = "saleClassId", value = "管理类别ID", required = true) @RequestParam("saleClassId") Long saleClassId,
            @ApiParam(name = "pageNo", value = "页码", required = true) @RequestParam("pageNo") Integer pageNo,
            @ApiParam(name = "pageSize", value = "每页条数", required = true) @RequestParam("pageSize") Integer pageSize) {
        PrdtSaleClassPageReqVO pageReqVO = new PrdtSaleClassPageReqVO();
        pageReqVO.setSaleClassId(saleClassId);
        pageReqVO.setPageNo(pageNo);
        pageReqVO.setPageSize(pageSize);
        return success(prdtSaleClassService.getSublevelSaleClassById(pageReqVO));
    }


    /**
     * 新增平台商展示分类
     */
    @ApiOperation(value = "新增平台商展示分类", httpMethod = HttpMethod.POST, notes = "product:saleClass:add")
    @RequiresPermissions("product:saleClass:add")
    @Log(title = "平台商展示分类", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtSaleClassSaveReqVO createReqVO) {
        return success(prdtSaleClassService.insertPrdtSaleClass(createReqVO));
    }

    /**
     * 修改平台商展示分类
     */
    @ApiOperation(value = "修改平台商展示分类", httpMethod = HttpMethod.PUT, notes = "product:saleClass:edit")
    @RequiresPermissions("product:saleClass:edit")
    @Log(title = "平台商展示分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtSaleClassSaveReqVO updateReqVO) {
        prdtSaleClassService.updatePrdtSaleClass(updateReqVO);
        return success(true);
    }

    /**
     * 删除平台商展示分类
     */
    @ApiOperation(value = "删除平台商展示分类", httpMethod = HttpMethod.PUT, notes = "product:saleClass:remove")
    @RequiresPermissions("product:saleClass:remove")
    @Log(title = "平台商展示分类", businessType = BusinessType.DELETE)
    @PutMapping("/{saleClassIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] saleClassIds) {
        prdtSaleClassService.deletePrdtSaleClassBySaleClassIds(saleClassIds);
        return success(true);
    }

    /**
     * 获取平台商展示分类详细信息
     */
    @ApiOperation(value = "获得平台商展示分类详情", httpMethod = HttpMethod.GET, notes = "product:saleClass:query")
    @RequiresPermissions("product:saleClass:query")
    @GetMapping(value = "/{saleClassId}")
    public CommonResult<PrdtSaleClassRespVO> getInfo(@PathVariable("saleClassId") Long saleClassId) {
        PrdtSaleClass prdtSaleClass = prdtSaleClassService.getPrdtSaleClass(saleClassId);
        return success(HutoolBeanUtils.toBean(prdtSaleClass, PrdtSaleClassRespVO.class));
    }

    /**
     * 分页查询平台商展示分类
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台商展示分类分页列表", httpMethod = HttpMethod.GET, notes = "product:saleClass:list")
    @RequiresPermissions("product:saleClass:list")
    public CommonResult<PageResult<PrdtSaleClassRespVO>> getPage(@Valid PrdtSaleClassPageReqVO pageReqVO) {
        PageResult<PrdtSaleClass> pageResult = prdtSaleClassService.getPrdtSaleClassPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, PrdtSaleClassRespVO.class));
    }

    @ApiOperation(value = "导入平台展示类别", httpMethod = HttpMethod.POST,notes = "product:saleClass:import")
    @Log(title = "导入平台展示类别", businessType = BusinessType.IMPORT)
    @RequiresPermissions("product:saleClass:import")
    @PostMapping("/importData")
    public CommonResult<String> importData(MultipartFile file) throws Exception
    {
        ExcelUtil<ProductSaleClassImportExcel> util = new ExcelUtil<>(ProductSaleClassImportExcel.class);
        List<ProductSaleClassImportExcel> saleClassList = util.importExcel(file.getInputStream(),1);
        String message = prdtSaleClassService.importSaleClassData(saleClassList);
        return success(message);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载平台展示类别信息模板", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<ProductSaleClassImportExcel> util = new ExcelUtil<ProductSaleClassImportExcel>(ProductSaleClassImportExcel.class);
        String instructions = "填表说明：\n" +
                "商品类别名称：必填项，支持文字和数字输入，最多仅限20个字符，超出字符导入会失败\n" +
                "注意：一级类别为主类目，二级属于一级的子类，三级为二级的子类，导入进去后类别默认为启用状态;";
        util.importTemplateExcel(response, "平台展示类别信息导入", StringUtils.EMPTY, instructions);
    }

    /**
     * 获取全国展示分类选中数据 (用于选中回显)
     *
     * @param saleClassIds 展示分类ID集合
     * @return 展示分类集合
     */
    @PostMapping("/getSelectedBatchInfo")
    @ApiOperation(value = "批量获取分类简略信息", httpMethod = HttpMethod.POST)
    public CommonResult<List<PrdtSaleClassRespVO>> getSelectedBatchInfo(@Valid @Size(min = NumberPool.INT_ONE) @RequestBody List<Long> saleClassIds) {
        return success(prdtSaleClassService.getSelectedSaleClass(saleClassIds));
    }

    /**
     * 一键批量删除全国展示分类
     */
    @ApiOperation(value = "一键批量删除全国展示多级分类", httpMethod = "DELETE", notes = "product:saleClass:remove")
    @RequiresPermissions("product:saleClass:remove")
    @Log(title = "全国展示分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeMultilevelClasses/{saleClassIds}")
    public CommonResult<Boolean> removeMultilevelClasses(@PathVariable Long[] saleClassIds) {
        prdtSaleClassService.removeMultilevelClasses(saleClassIds);
        return success(true);
    }

    /**
     * 修改平台展示分类
     */
    @ApiOperation(value = "平台展示类别复制", httpMethod = "POST")
    @Log(title = "平台展示类别复制", businessType = BusinessType.INSERT)
    @PostMapping("/copySaleClass")
    public CommonResult<Boolean> copySaleClass(@Valid @RequestBody PrdtSaleClassCopyReqVO vo) {
        prdtSaleClassService.copySaleClass(vo);
        return success(true);
    }
}
