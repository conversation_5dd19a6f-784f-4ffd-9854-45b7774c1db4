package com.zksr.product.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.areaClass.excel.ProductAreaClassImportExcel;
import com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRespDTO;
import com.zksr.product.controller.areaClass.vo.*;
import com.zksr.product.domain.PrdtAreaClass;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;

import javax.validation.Valid;
import java.util.List;

/**
 * 城市展示分类Service接口
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
public interface IPrdtAreaClassService {

    /**
     * 新增城市展示分类
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtAreaClass(@Valid PrdtAreaClassSaveReqVO createReqVO,Long sysCode);

    /**
     * 修改城市展示分类
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public UpdateAreaClassRespEvent updatePrdtAreaClass(@Valid PrdtAreaClassSaveReqVO updateReqVO);

    /**
     * 删除城市展示分类
     *
     * @param areaClassId 城市展示分类id
     */
    public void deletePrdtAreaClass(Long areaClassId);

    /**
     * 批量删除城市展示分类
     *
     * @param areaClassIds 需要删除的城市展示分类主键集合
     * @return 结果
     */
    public void deletePrdtAreaClassByAreaClassIds(Long[] areaClassIds);

    /**
     * 获得城市展示分类
     *
     * @param areaClassId 城市展示分类id
     * @return 城市展示分类
     */
    public PrdtAreaClassRespVO getPrdtAreaClass(Long areaClassId);

    /**
     * 获得城市展示分类分页
     *
     * @param pageReqVO 分页查询
     * @return 城市展示分类分页
     */
    PageResult<PrdtAreaClassRespVO> getPrdtAreaClassPage(PrdtAreaClassPageReqVO pageReqVO);

    /**
    * @Description: 获取所有城市展示分类信息
    * @Author: liuxingyu
    * @Date: 2024/3/23 9:07
    */
    List<PrdtAreaClass> getAreaClassList(Long areaClassId, Long areaId, String status, Integer level);

    /**
    * @Description: 根据Id获取城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/25 14:56
    */
    PrdtAreaClass getAreaClassByAreaClassId(Long areaClassId);

    /**
    * @Description: 获取门店绑定的城市展示分类(根据入驻商ID集合获取)
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:45
    */
    List<PrdtAreaClass> getAreaClassBranchList(List<Long> supplierIds);

    /**
     * 批量获取展示分类信息   (暂时用于支持数据回显)
     * @param saleClassIds  展示分类ID集合
     * @return  展示分类集合
     */
    List<PrdtAreaClassRespVO> getSelectedAreaClass(List<Long> saleClassIds);

    /**
    * @Description: 获取城市展示分类详情
    * @Author: liuxingyu
    * @Date: 2024/4/13 9:24
    */
    PageResult<PrdtAreaClassRespVO> getAreaClassDetail(PrdtAreaClassPageReqVO reqVO);

    /**
    * @Description: 根据门店区域和门店渠道获取城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/4/24 10:48
    */
    List<PrdtAreaClass> getAreaClassAreaChannelList(String key);

    /**
     * 刷新缓存
     * @param event 事件数据
     */
    void reloadCache(UpdateAreaClassRespEvent event);

    /**
     * 一键批量删除城市展示多级分类
     *
     * @param areaClassIds 需要删除的城市展示分类各个级别ID集合
     * @return 结果
     */
    public void removeMultilevelClasses(Long[] areaClassIds);

    /**
     * 导入运营商展示类别
     * @param areaClassList
     * @return
     */
    String importAreaClassData(List<ProductAreaClassImportExcel> areaClassList);

    FileImportHandlerVo importAreaClassDataEvent(List<ProductAreaClassImportExcel> areaClassList, Long sysCode, Long fileImportId, Long dcId,Integer seq);

    /**
     * 城市展示类别复制
     * @param vo
     */
    void copyAreaClass(PrdtAreaClassCopyReqVO vo);

    List<PrdtAreaClassExportVo> getPrdtAreaClassExportList(PrdtAreaClassExportVo pageVo);
}
