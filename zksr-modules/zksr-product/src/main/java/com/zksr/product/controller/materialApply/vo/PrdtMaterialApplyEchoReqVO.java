package com.zksr.product.controller.materialApply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;


/**
 * 素材应用查看详情（回显）/下架素材入参
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@ApiModel("素材应用查看详情（回显）/下架素材入参")
public class PrdtMaterialApplyEchoReqVO {
    private static final long serialVersionUID = 1L;

    /** 素材id */
    @Excel(name = "素材id")
    @ApiModelProperty(value = "素材id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long materialId;

    /** 素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品 */
    @Excel(name = "素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品")
    @ApiModelProperty(value = "素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品")
    private Integer applyType;

    /** 生效时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "生效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "生效时间")
    private Date startTime;

    /** 失效时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "失效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "失效时间")
    private Date endTime;

    /** 素材应用类型id */
    @Excel(name = "素材应用类型id")
    @ApiModelProperty(value = "素材应用类型id",hidden = true)
    private Long applyId;

}
