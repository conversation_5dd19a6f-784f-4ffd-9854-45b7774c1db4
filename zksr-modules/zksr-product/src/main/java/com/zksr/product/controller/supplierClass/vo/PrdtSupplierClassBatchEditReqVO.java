package com.zksr.product.controller.supplierClass.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 入驻商-平台商管理分类 关联关系对象 prdt_supplier_class
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@ApiModel("入驻商-平台商管理分类 关联关系 - prdt_supplier_class分页 Request VO")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSupplierClassBatchEditReqVO{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "平台商管理分类id集合")
    @NotNull(message = "管理分类ID集合[catgoryIds]必填")
    private List<Long> catgoryIds;

    @ApiModelProperty(value = "入驻商id", hidden = true)
    private Long supplierId;

    /**
     * 是否可售后 1：是，0：否 数据字典 sys_status_type）
     */
    @ApiModelProperty(value = "是否可售后")
    @NotNull(message = "是否可售后[isAfterSales]必填")
    private Integer isAfterSales;

    /**
     * 售后时间类型- 分钟：1，小时：2，天：3（数据字典 sys_time_type）
     */
    @ApiModelProperty(value = "售后时间类型")
    private Integer afterSalesTimeType;

    /**
     * 可售后时间
     */
    @ApiModelProperty(value = "可售后时间")
    private Long afterSalesTime;

}
