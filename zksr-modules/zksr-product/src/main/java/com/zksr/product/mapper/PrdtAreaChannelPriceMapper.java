package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.controller.areaChannelPrice.vo.PrdtAreaChannelPriceRespVO;
import com.zksr.product.domain.PrdtSpu;
import com.zksr.product.domain.PrdtSupplierGroupPrice;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtAreaChannelPrice;
import com.zksr.product.controller.areaChannelPrice.vo.PrdtAreaChannelPricePageReqVO;

import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.PRDT_IS_DELETE_0;
import static com.zksr.product.constant.ProductConstant.PRDT_STATUS_1;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_AREA_CHANNEL_PRICE_EXISTS;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_SPU_EXISTS;


/**
 * 城市渠道价格Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Mapper
public interface PrdtAreaChannelPriceMapper extends BaseMapperX<PrdtAreaChannelPrice> {
    default PageResult<PrdtAreaChannelPrice> selectPage(PrdtAreaChannelPricePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtAreaChannelPrice>()
                    .eqIfPresent(PrdtAreaChannelPrice::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtAreaChannelPrice::getAreaId, reqVO.getAreaId())
                    .eqIfPresent(PrdtAreaChannelPrice::getChannelId, reqVO.getChannelId())
                    .eqIfPresent(PrdtAreaChannelPrice::getSalePriceCode, reqVO.getSalePriceCode())
                .orderByDesc(PrdtAreaChannelPrice::getSysCode));
    }


    /**
     * 删除C城市渠道价格信息
     * @param channelId  渠道id
     * @param areaId  城市id
     */
    default void deleteAreaChannelPrice(Long channelId, Long areaId){
        delete(new LambdaQueryWrapper<PrdtAreaChannelPrice>()
                .eq(PrdtAreaChannelPrice::getChannelId,channelId)
                .eq(PrdtAreaChannelPrice::getAreaId, areaId));
    }

    default Long selectAreaChannelPriceByCheck(PrdtAreaChannelPrice price){
        //校验是否存在
        LambdaQueryWrapper<PrdtAreaChannelPrice> wrapper = new LambdaQueryWrapperX<PrdtAreaChannelPrice>()
                .eq(PrdtAreaChannelPrice::getAreaId, price.getAreaId())
                .eq(PrdtAreaChannelPrice::getChannelId, price.getChannelId())
                .eq(PrdtAreaChannelPrice::getSalePriceCode, price.getSalePriceCode())
                .eqIfPresent(PrdtAreaChannelPrice::getSysCode,price.getSysCode());
        return selectCount(wrapper);
    }

    default Long insertPrdtAreaChannelPrice(PrdtAreaChannelPrice price){
        //校验是否存在
        Long checkPrice = selectAreaChannelPriceByCheck(price);
        if(Objects.nonNull(checkPrice) && checkPrice > 0){
            throw exception(PRDT_AREA_CHANNEL_PRICE_EXISTS);
        }
        // 插入
        insert(price);
        return price.getAreaId();
    }

    default PrdtAreaChannelPrice selectAreaChannelPrice(PrdtAreaChannelPriceRespVO respVO){
        LambdaQueryWrapperX<PrdtAreaChannelPrice> wrapper = new LambdaQueryWrapperX<PrdtAreaChannelPrice>()
                .eq(PrdtAreaChannelPrice::getAreaId, respVO.getAreaId())
                .eq(PrdtAreaChannelPrice::getChannelId, respVO.getChannelId())
                .eqIfPresent(PrdtAreaChannelPrice::getSysCode,respVO.getSysCode());
        wrapper.last(StringPool.LIMIT_ONE);
        return selectOne(wrapper);
    }
}
