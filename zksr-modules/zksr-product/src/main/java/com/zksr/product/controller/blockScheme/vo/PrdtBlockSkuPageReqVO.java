package com.zksr.product.controller.blockScheme.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 经营屏蔽sku对象 prdt_block_sku
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@ApiModel("经营屏蔽sku - prdt_block_sku分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtBlockSkuPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty(value = "商品sku_id")
    private Long blockSkuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 方案编码 */
    @Excel(name = "方案编码")
    @ApiModelProperty(value = "方案编码", required = true)
    private String schemeNo;

    /** 商品sku_id */
    @Excel(name = "商品sku_id")
    @ApiModelProperty(value = "商品sku_id", required = true)
    private Long skuId;


}
