package com.zksr.product.convert.blockScheme;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtBlockScheme;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSchemeRespVO;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockSchemeSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 经营屏蔽方案 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-27
*/
@Mapper
public interface PrdtBlockSchemeConvert {

    PrdtBlockSchemeConvert INSTANCE = Mappers.getMapper(PrdtBlockSchemeConvert.class);

    PrdtBlockSchemeRespVO convert(PrdtBlockScheme prdtBlockScheme);

    @Mappings({
            @Mapping(target = "createBy", expression = "java(com.zksr.common.security.utils.SecurityUtils.getUsername())"),
            @Mapping(target = "updateBy", expression = "java(com.zksr.common.security.utils.SecurityUtils.getUsername())"),
            @Mapping(target = "memo", defaultValue = ""),
            @Mapping(target = "updateTime", expression = "java(com.zksr.common.core.utils.DateUtils.getNowDate())")
    })
    PrdtBlockScheme convertForInsert(PrdtBlockSchemeSaveReqVO prdtBlockSchemeSaveReq);

    PageResult<PrdtBlockSchemeRespVO> convertPage(PageResult<PrdtBlockScheme> prdtBlockSchemePage);

    @Mappings({
            @Mapping(target = "sysCode", ignore = true), // sysCode不更新
            @Mapping(target = "schemeNo", ignore = true), // 方案编码不更新
            @Mapping(target = "updateBy", expression = "java(com.zksr.common.security.utils.SecurityUtils.getUsername())")
    })
    PrdtBlockScheme convertForUpdate(PrdtBlockSchemeSaveReqVO updateReqVO);
}