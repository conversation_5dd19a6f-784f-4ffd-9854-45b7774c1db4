package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.sku.dto.SkuPricesRespDTO;
import com.zksr.product.api.sku.vo.SkuPricesPageReqVO;
import com.zksr.product.controller.sku.vo.PrdtSkuPageReqVO;
import com.zksr.product.controller.sku.vo.PrdtSkuRespVO;
import com.zksr.product.controller.sku.vo.PrdtSkuSelectedRespVO;
import com.zksr.product.domain.PrdtSku;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.zksr.common.core.pool.StringPool.LIMIT_ONE;
import static com.zksr.product.constant.ProductConstant.*;


/**
 * 商品SKUMapper接口
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Mapper
@SuppressWarnings("all")
public interface PrdtSkuMapper extends BaseMapperX<PrdtSku> {
    default PageResult<PrdtSku> selectPage(PrdtSkuPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtSku>()
                .eqIfPresent(PrdtSku::getSkuId, reqVO.getSkuId())
                .eqIfPresent(PrdtSku::getSysCode, reqVO.getSysCode())
                .eqIfPresent(PrdtSku::getSpuId, reqVO.getSpuId())
                .eqIfPresent(PrdtSku::getUnit, reqVO.getUnit())
                .eqIfPresent(PrdtSku::getBarcode, reqVO.getBarcode())
                .eqIfPresent(PrdtSku::getProperties, reqVO.getProperties())
                .eqIfPresent(PrdtSku::getThumb, reqVO.getThumb())
                .eqIfPresent(PrdtSku::getStock, reqVO.getStock())
                .eqIfPresent(PrdtSku::getMarkPrice, reqVO.getMarkPrice())
                .eqIfPresent(PrdtSku::getSuggestPrice, reqVO.getSuggestPrice())
                .eqIfPresent(PrdtSku::getCostPrice, reqVO.getCostPrice())
                .eqIfPresent(PrdtSku::getIsDelete, reqVO.getIsDelete())
                .orderByDesc(PrdtSku::getSkuId));
    }

    //根据查询条件查询SKU集合
    default List<PrdtSku> selectPrdtSkuByList(PrdtSkuRespVO vo) {
        LambdaQueryWrapperX<PrdtSku> wrapper = new LambdaQueryWrapperX<PrdtSku>()
                .eqIfPresent(PrdtSku::getSpuId, vo.getSpuId())
                .eqIfPresent(PrdtSku::getStatus, vo.getStatus())
                .eqIfPresent(PrdtSku::getIsDelete, vo.getIsDelete())
                .inIfPresent(PrdtSku::getSkuId, vo.getSkuList());
        return selectList(wrapper);
    }

    default Long selectSkuCount(PrdtSkuRespVO vo) {
        return selectCount(new LambdaQueryWrapperX<PrdtSku>()
                .eqIfPresent(PrdtSku::getSpuId, vo.getSpuId())
                .eqIfPresent(PrdtSku::getStatus, vo.getStatus())
                .inIfPresent(PrdtSku::getSkuId, vo.getSkuList()));
    }


    default Long insertPrdtSku(PrdtSku sku) {
        //设置默认值
        sku.setIsDelete(PRDT_IS_DELETE_0);
        sku.setStatus(PRDT_STATUS_1);
        // 插入
        insert(sku);
        // 返回
        return sku.getSkuId();
    }

    public void updateBatchPrdtSkuStatus(@Param("spuId") Long spuId, @Param("skuIds") List<Long> skuIds);

    //停用中单位sku
    public void updateBatchPrdtSkuMidStatus(@Param("spuId") Long spuId, @Param("skuIds") List<Long> skuIds);

    //停用大单位sku
    public void updateBatchPrdtSkuLargeStatus(@Param("spuId") Long spuId, @Param("skuIds") List<Long> skuIds);

    //根据查询条件查询SKU
    default PrdtSku selectPrdtSku(PrdtSkuRespVO vo) {
        LambdaQueryWrapperX<PrdtSku> wrapper = new LambdaQueryWrapperX<PrdtSku>()
                .eqIfPresent(PrdtSku::getSpuId, vo.getSpuId())
                .eqIfPresent(PrdtSku::getStatus, vo.getStatus())
                .eqIfPresent(PrdtSku::getSkuId, vo.getSkuId());
        return selectOne(wrapper);
    }

    //根据查询条件查询SKU库存
    default BigDecimal selectPrdtSkuByStock(Long skuId) {
        BigDecimal stock = BigDecimal.ZERO;

        LambdaQueryWrapperX<PrdtSku> wrapper = new LambdaQueryWrapperX<PrdtSku>()
                .eqIfPresent(PrdtSku::getSkuId, skuId);
        PrdtSku prdtSku = selectOne(wrapper);
        if (ToolUtil.isNotEmpty(prdtSku)) {
            stock = prdtSku.getStock();
        }
        return stock;
    }

    /**
     * @Description: 获取sku信息
     * @Author: liuxingyu
     * @Date: 2024/4/11 16:59
     */
    default List<PrdtSku> getByProduct(String param) {
        return selectList(new LambdaQueryWrapper<PrdtSku>()
                .like(PrdtSku::getBarcode, param)
                .eq(PrdtSku::getIsDelete, PRDT_IS_DELETE_0));
    }

    /**
     * 根据SPU ID 和 条码查询 SKU
     *
     * @param spuId
     * @param barcode
     * @return
     */
    default PrdtSku selectBySpuIdAndBarcode(Long spuId, String barcode) {
        LambdaQueryWrapperX<PrdtSku> wrapper = new LambdaQueryWrapperX<PrdtSku>()
                .eq(PrdtSku::getSpuId, spuId)
                .eq(PrdtSku::getBarcode, barcode)
                .eq(PrdtSku::getIsDelete, PRDT_IS_DELETE_0)
                .last(LIMIT_ONE);
        return selectOne(wrapper);
    }


    public List<PrdtSkuSelectedRespVO> getSelectedPrdtSku(@Param("skuIds") List<Long> skuIds);

    public List<Long> getSelectedPrdtSpuIds(@Param("skuIds") List<Long> skuIds);

    default long deleteByIds(List<Long> skuIds) {
        return update(null, new LambdaUpdateWrapper<PrdtSku>()
                .set(PrdtSku::getIsDelete, PRDT_IS_DELETE_1)
                .in(PrdtSku::getSkuId, skuIds));
    }

    default Long selectCountByBarcode(String barcode, List<Long> skuIdList) {
        LambdaQueryWrapper<PrdtSku> skuLambdaQueryWrapper = new LambdaQueryWrapperX<PrdtSku>()
                .and(w ->
                        w.eq(PrdtSku::getBarcode, barcode).or().eq(PrdtSku::getMidBarcode, barcode).or().eq(PrdtSku::getLargeBarcode, barcode)
                )
                .eq(PrdtSku::getIsDelete, PRDT_IS_DELETE_0);
        if (!skuIdList.isEmpty()) {
            skuLambdaQueryWrapper.ne(PrdtSku::getSkuId, skuIdList);
        }
        return selectCount(skuLambdaQueryWrapper);
    }

    default List<PrdtSku> selectBySpuId(Long spuId) {
        LambdaQueryWrapper<PrdtSku> skuLambdaQueryWrapper = new LambdaQueryWrapperX<PrdtSku>()
                .eq(PrdtSku::getSpuId, spuId)
                .eq(PrdtSku::getIsDelete, PRDT_IS_DELETE_0);
        return selectList(skuLambdaQueryWrapper);
    }

    void updateStatusEnable(@Param("spuId") Long spuId);

    void updateMidStatusEnable(@Param("spuId") Long spuId);

    void updateLargeStatusEnable(@Param("spuId") Long spuId);

    default List<PrdtSku> selectBySpuIdList(Set<Long> spuIdList) {
        LambdaQueryWrapper<PrdtSku> skuLambdaQueryWrapper = new LambdaQueryWrapperX<PrdtSku>()
                .in(PrdtSku::getSpuId, spuIdList)
                .eq(PrdtSku::getIsDelete, PRDT_IS_DELETE_0);
        return selectList(skuLambdaQueryWrapper);
    }


    Date getLastUpdateTime(@Param("sourceNo") String sourceNo, @Param("supplierId") String supplierId);

    default List<PrdtSku> selectSaleTotalRate(List<Long> skuIdList) {
        LambdaQueryWrapper<PrdtSku> skuLambdaQueryWrapper = new LambdaQueryWrapperX<PrdtSku>()
                .in(PrdtSku::getSkuId, skuIdList)
                .select(
                        PrdtSku::getSkuId,
                        PrdtSku::getSaleTotalRate,
                        PrdtSku::getSpuId
                );
        return selectList(skuLambdaQueryWrapper);
    }

    default void saveOrUpdateBatch(List<PrdtSku> skuUpdateOrInsertList) {
        for (PrdtSku prdtSku : skuUpdateOrInsertList) {
            if (Objects.nonNull(prdtSku.getSkuId())) {
                LambdaUpdateWrapper<PrdtSku> wrapperX = new LambdaUpdateWrapper<>();
                wrapperX.eq(PrdtSku::getSkuId, prdtSku.getSkuId());

                // 已售数据不更直接由后台页面提交编辑
                prdtSku.setSaleQty(null);

                // 置空销售占比
                if (Objects.isNull(prdtSku.getSaleTotalRate())) {
                    wrapperX.set(PrdtSku::getSaleTotalRate, null);
                }
                this.update(prdtSku, wrapperX);
            } else {
                this.insert(prdtSku);
            }
        }
    }

    /**
     * 获取SKU价格信息列表
     *
     * @param reqVO
     * @return
     */
    List<SkuPricesRespDTO> getSkuPricesInfoList(SkuPricesPageReqVO reqVO);

    Page<PrdtSkuRespVO> selectSkuPage(@Param("page") Page<PrdtSkuPageReqVO> page, @Param("pageReqVO") PrdtSkuPageReqVO pageReqVO);

    List<Long> selectRelationSpuCombineList(@Param("skuIds") List<Long> skuIds);

    default void deleteSkuListBySpuIds(Long[] spuIds) {
        update(null, new LambdaUpdateWrapper<PrdtSku>()
                .set(PrdtSku::getIsDelete, PRDT_IS_DELETE_1)
                .in(PrdtSku::getSpuId, spuIds));

    }

    List<SkuDTO> selectBySpuIdAreaShelfList(@Param("spuIdList") Set<Long> spuIdList, @Param("areaShelfStatus") Integer areaShelfStatus, @Param("supplierShelfStatus") Integer supplierShelfStatus);

    List<SkuDTO> selectBySpuIdShelfList2(@Param("spuIdList") Set<Long> spuIdList, @Param("shelfStatus") Integer shelfStatus);

//    default Boolean getAreaIdExistSpu(Long areaId, Long sysCode) {
//        List<PrdtSku> prdtSkus = selectList(new LambdaQueryWrapperX<PrdtSku>()
//                .eq(PrdtSku::getAreaId, areaId)
//                .eq(PrdtSku::getSysCode, sysCode)
//                .last(" LIMIT 1 ")
//        );
//        return ToolUtil.isNotEmpty(prdtSkus)?Boolean.TRUE:Boolean.FALSE;
//    }

    List<SkuDTO> getAreaIdExistSpu(@Param("areaId") Long areaId, @Param("sysCode") Long sysCode);

    List<SkuDTO> getExistShelfSku(@Param("spuId") Long spuId);

    List<SkuDTO> selectExistShelfSkuId(@Param("skuId") Long skuId);

    default List<PrdtSku> getListBySkuIds(@Param("skuIds") List<Long> skuIds) {
        return selectList(new LambdaQueryWrapperX<PrdtSku>().inIfPresent(PrdtSku::getSkuId, skuIds));
    }

    default PrdtSku getBySkuId(@Param("skuId") Long skuId) {
        return selectOne(new LambdaQueryWrapperX<PrdtSku>().eq(PrdtSku::getSkuId, skuId));
    }

    PrdtSku getByBarcode(@Param("barcode") String barcode, @Param("spuName") String spuName, @Param("supplierId") Long supplierId);

    default List<PrdtSku> getskuListBySpuIdList(List<Long> shelfSpuList) {
        LambdaQueryWrapper<PrdtSku> skuLambdaQueryWrapper = new LambdaQueryWrapperX<PrdtSku>()
                .in(PrdtSku::getSpuId, shelfSpuList)
                .eq(PrdtSku::getIsDelete, PRDT_IS_DELETE_0);
        return selectList(skuLambdaQueryWrapper);
    }

    default PrdtSku getBySpuIdAndSourceNo(String sourceNo, Long spuId) {
        LambdaQueryWrapper<PrdtSku> skuQueryWrapper = new LambdaQueryWrapper<>();
        skuQueryWrapper
                .eq(PrdtSku::getSourceNo, sourceNo)
                .eq(PrdtSku::getIsDelete, PRDT_IS_DELETE_0)
                .eq(PrdtSku::getSpuId, spuId);

        // 查询 SKU 信息
        return this.selectOne(skuQueryWrapper);
    }
}
