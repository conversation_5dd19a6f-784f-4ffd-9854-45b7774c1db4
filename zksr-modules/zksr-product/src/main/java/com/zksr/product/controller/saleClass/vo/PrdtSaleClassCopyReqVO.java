package com.zksr.product.controller.saleClass.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
*
* @date 2024/11/13 16:56
* <AUTHOR>
*/
@Data
@ApiModel("全国展示分类复制功能请求入参实体")
public class PrdtSaleClassCopyReqVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 全国展示分类id */
    @ApiModelProperty(value = "复制前的全国展示分类id")
    private Long copySaleClassId;

    /** 复制后的目标全国展示分类上级id */
    @ApiModelProperty(value = "复制后的目标全国展示分类上级id")
    private Long targetSaleClassPid;


}
