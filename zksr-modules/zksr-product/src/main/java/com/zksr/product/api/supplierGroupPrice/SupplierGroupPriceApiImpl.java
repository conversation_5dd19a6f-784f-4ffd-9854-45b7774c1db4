package com.zksr.product.api.supplierGroupPrice;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.supplierGroupPrice.dto.SupplierGrouplPriceDTO;
import com.zksr.product.domain.PrdtSupplierGroupPrice;
import com.zksr.product.service.IPrdtSupplierGroupPriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class SupplierGroupPriceApiImpl implements SupplierGrouplPriceApi {

    @Autowired
    private IPrdtSupplierGroupPriceService prdtSupplierGroupPriceService;


    @Override
    public CommonResult<SupplierGrouplPriceDTO> getPriceByAreaIdAndGroupId(@RequestParam("areaId") Long areaId, @RequestParam("groupId") Long groupId) {

        return CommonResult.success(HutoolBeanUtils.toBean(prdtSupplierGroupPriceService.getPriceByAreaIdAndGroupId(areaId,groupId),SupplierGrouplPriceDTO.class));
    }

    @Override
    public CommonResult<Integer> getPriceByKey(@RequestParam("key") String key) {
        Integer salePriceCode = null;

        //key为 城市ID 拼接 分组ID 需要拆开
        String[] split = key.split("-");
        Long areaId = Long.valueOf(split[0]);
        Long groupId = Long.valueOf(split[1]);
        PrdtSupplierGroupPrice price = prdtSupplierGroupPriceService.getPriceByAreaIdAndGroupId(areaId, groupId);
        if(ToolUtil.isNotEmpty(price)){
            salePriceCode = Math.toIntExact(price.getSalePriceCode());
        }
        return CommonResult.success(salePriceCode);
    }
}
