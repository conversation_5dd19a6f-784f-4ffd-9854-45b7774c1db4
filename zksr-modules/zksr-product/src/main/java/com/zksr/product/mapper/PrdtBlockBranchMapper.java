package com.zksr.product.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.domain.PrdtBlockScheme;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtBlockBranch;
import com.zksr.product.controller.blockScheme.vo.PrdtBlockBranchPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 经营屏蔽客户Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Mapper
public interface PrdtBlockBranchMapper extends BaseMapperX<PrdtBlockBranch> {
    default PageResult<PrdtBlockBranch> selectPage(PrdtBlockBranchPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtBlockBranch>()
                    .eqIfPresent(PrdtBlockBranch::getBlockBranchId, reqVO.getBlockBranchId())
                    .eqIfPresent(PrdtBlockBranch::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtBlockBranch::getSchemeNo, reqVO.getSchemeNo())
                    .eqIfPresent(PrdtBlockBranch::getBranchId, reqVO.getBranchId())
                .orderByDesc(PrdtBlockBranch::getBlockBranchId));
    }

    List<PrdtBlockScheme> selectBlockSchemesByBranchId(@Param("branchId") Long branchId);
}
