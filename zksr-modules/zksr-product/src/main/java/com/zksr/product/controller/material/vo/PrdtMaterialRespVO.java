package com.zksr.product.controller.material.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 素材对象 prdt_material
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@ApiModel("素材 - prdt_material Response VO")
public class PrdtMaterialRespVO {
    private static final long serialVersionUID = 1L;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    /** 素材id */
    @ApiModelProperty(value = "素材ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long materialId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 素材名称 */
    @Excel(name = "素材名称")
    @ApiModelProperty(value = "素材名称")
    private String name;

    /** 素材图片地址 */
    @Excel(name = "素材图片地址")
    @ApiModelProperty(value = "素材图片地址")
    private String img;

    /** 素材图片大小 */
    @Excel(name = "素材图片大小")
    @ApiModelProperty(value = "素材图片大小")
    private String imgSize;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Integer status;

}
