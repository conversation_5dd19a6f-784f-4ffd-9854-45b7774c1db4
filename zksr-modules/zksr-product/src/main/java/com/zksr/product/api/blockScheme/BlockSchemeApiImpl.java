package com.zksr.product.api.blockScheme;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.product.service.IPrdtBlockSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@ApiIgnore
public class BlockSchemeApiImpl implements BlockSchemeApi {

    @Autowired
    private IPrdtBlockSchemeService prdtBlockSchemeService;

    /**
     * 根据客户编码获取被屏蔽的商品
     * @param branchId 客户编码
     * @return 被屏蔽的skuIds
     */
    @Override
    @InnerAuth
    public CommonResult<List<Long>> getBlockSkusByBranchId(Long branchId) {
        return success(prdtBlockSchemeService.getBlockSkusByBranchId(branchId));
    }
}
