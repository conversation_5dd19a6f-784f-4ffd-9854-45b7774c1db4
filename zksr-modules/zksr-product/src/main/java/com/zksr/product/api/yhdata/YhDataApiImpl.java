package com.zksr.product.api.yhdata;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.domain.vo.openapi.CreateYhDataMatchReqVO;
import com.zksr.common.core.domain.vo.openapi.CreateYhDataMatchRespVO;
import com.zksr.common.core.domain.vo.openapi.receive.CreateYhDataReqVO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.product.api.yhdata.vo.*;
import com.zksr.product.mq.ProductMqProducer;
import com.zksr.product.service.IPrdtBranchYhdataService;
import com.zksr.product.service.IProductCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

import static com.zksr.product.enums.ErrorCodeConstants.PRDT_YH_DATA_BRANCH_ERR;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 补货单数据
 * @date 2024/12/10 9:56
 */
@ApiIgnore
@RestController
@InnerAuth
public class YhDataApiImpl implements YhDataApi {

    @Autowired
    private IPrdtBranchYhdataService branchYhdataService;

    @Autowired
    private ProductMqProducer productMqProducer;

    @Autowired
    private IProductCacheService productCacheService;

    @Override
    public CommonResult<Boolean> saveYhData(CreateYhDataReqVO reqVO) {
        // 获取门店, 用于平台商加锁补货单
        BranchDTO branch = productCacheService.getBranchDto(reqVO.getBranchId());
        if (ObjectUtil.isEmpty(branch)) {
            return CommonResult.error(PRDT_YH_DATA_BRANCH_ERR);
        }
        productMqProducer.sendYhDataMatch(reqVO);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<CreateYhDataMatchRespVO> getBatchYhRes(CreateYhDataMatchReqVO reqVO) {
        return branchYhdataService.getBatchYhRes(reqVO);
    }

    @Override
    public CommonResult<Map<Long, List<Long>>> getBatchSupplierSaleClass(YhBatchSupplierSaleClassReqVO reqVO) {
        return CommonResult.success(branchYhdataService.getBatchSupplierSaleClass(reqVO));
    }

    @Override
    public CommonResult<PageResult<YhBatchItemVO>> getBatchItemList(YhBatchListReqVO reqVO) {
        return CommonResult.success(branchYhdataService.getEsBatchItemList(reqVO));
    }

    @Override
    public CommonResult<Boolean> branchBatchYhSave(YhBatchSaveReqVO reqVO) {
        return branchYhdataService.branchBatchYhSave(reqVO);
    }

    @Override
    public CommonResult<Boolean> removeBatchYh(YhBatchRemoveReqVO reqVO) {
        branchYhdataService.removeBatchYh(reqVO);
        return CommonResult.success(Boolean.TRUE);
    }
}
