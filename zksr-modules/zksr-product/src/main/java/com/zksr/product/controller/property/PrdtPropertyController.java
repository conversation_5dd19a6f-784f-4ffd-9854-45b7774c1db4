package com.zksr.product.controller.property;

import javax.validation.Valid;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtProperty;
import com.zksr.product.service.IPrdtPropertyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.property.vo.PrdtPropertyPageReqVO;
import com.zksr.product.controller.property.vo.PrdtPropertySaveReqVO;
import com.zksr.product.controller.property.vo.PrdtPropertyRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 规格名称Controller
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Api(tags = "管理后台 - 规格名称接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/property")
public class PrdtPropertyController {
    @Autowired
    private IPrdtPropertyService prdtPropertyService;

    /**
     * 新增规格名称
     */
    @ApiOperation(value = "新增规格名称", httpMethod = "POST")
    @RequiresPermissions("product:property:add")
    @Log(title = "规格名称", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtPropertySaveReqVO createReqVO) {
        return success(prdtPropertyService.insertPrdtProperty(createReqVO));
    }

    /**
     * 修改规格名称
     */
    @ApiOperation(value = "修改规格名称", httpMethod = "PUT")
    @RequiresPermissions("product:property:edit")
    @Log(title = "规格名称", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtPropertySaveReqVO updateReqVO) {
            prdtPropertyService.updatePrdtProperty(updateReqVO);
        return success(true);
    }

    /**
     * 删除规格名称
     */
    @ApiOperation(value = "删除规格名称", httpMethod = "GET")
    @RequiresPermissions("product:property:remove")
    @Log(title = "规格名称", businessType = BusinessType.DELETE)
    @DeleteMapping("/{propertyIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] propertyIds) {
        prdtPropertyService.deletePrdtPropertyByPropertyIds(propertyIds);
        return success(true);
    }

    /**
     * 获取规格名称详细信息
     */
    @ApiOperation(value = "获得规格名称详情", httpMethod = "GET")
    @RequiresPermissions("product:property:query")
    @GetMapping(value = "/{propertyId}")
    public CommonResult<PrdtPropertyRespVO> getInfo(@PathVariable("propertyId") Long propertyId) {
        PrdtProperty prdtProperty = prdtPropertyService.getPrdtProperty(propertyId);
        return success(HutoolBeanUtils.toBean(prdtProperty, PrdtPropertyRespVO.class));
    }

    /**
     * 分页查询规格名称
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得规格名称分页列表", httpMethod = "GET")
    @RequiresPermissions("product:property:list")
    public CommonResult<PageResult<PrdtPropertyRespVO>> getPage(@Valid PrdtPropertyPageReqVO pageReqVO) {
        PageResult<PrdtProperty> pageResult = prdtPropertyService.getPrdtPropertyPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, PrdtPropertyRespVO.class));
    }

}
