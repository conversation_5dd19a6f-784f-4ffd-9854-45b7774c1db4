package com.zksr.product.service;

import com.zksr.product.api.model.EsProductEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/3/1
 * @desc 商品事件处理器
 */
public interface IProductEventService {

    /**
     * 接受事件异步处理
     * @param event 事件
     */
    void acceptEvent(EsProductEvent<?> event);

    /**
     * 执行事件
     * @param event 事件
     */
    void executeEvent(EsProductEvent<?> event);

    void createIndex();

    void testDemo1(List<Long> list);

    void testDemo2(List<Long> list);

    void testDemo3(Long spuId);

    void testDemo4(Long skuId);
}
