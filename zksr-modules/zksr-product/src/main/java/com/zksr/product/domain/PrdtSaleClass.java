package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 平台商展示分类对象 prdt_sale_class
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@TableName(value = "prdt_sale_class")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSaleClass extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 平台商展示分类id */
    @TableId(type = IdType.AUTO)
    private Long saleClassId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String name;

    /** 父id */
    @Excel(name = "父id")
    private Long pid;

    /** 分类图标 */
    @Excel(name = "分类图标")
    private String icon;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    private Long groupId;

    /** 级别 */
    @Excel(name = "级别")
    private Integer level;

    /** 删除状态 */
    @Excel(name = "0-未删除,1-已删除")
    private String delFlag;

    /** 是否展示生产日期 0-关闭, 1-开启 */
    @Excel(name = "是否展示生产日期 0-关闭, 1-开启")
    @ApiModelProperty(value = "是否展示生产日期 0-关闭, 1-开启")
    private Integer showProduceDate;

    /** 生产日期格式 yy/MM/dd 年月日 yy/MM 年月 */
    @Excel(name = "生产日期格式 yy/MM/dd 年月日 yy/MM 年月")
    @ApiModelProperty(value = "生产日期格式 yy/MM/dd 年月日 yy/MM 年月")
    private String produceDateFormat;
}
