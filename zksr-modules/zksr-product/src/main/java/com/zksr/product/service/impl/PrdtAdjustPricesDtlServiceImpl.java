package com.zksr.product.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesSaveReqVO;
import com.zksr.product.controller.spu.dto.PrdtSpuUpdateRespDTO;
import com.zksr.product.convert.sku.SkuConvert;
import com.zksr.product.domain.PrdtAdjustPrices;
import com.zksr.product.domain.PrdtSku;
import com.zksr.product.mapper.PrdtAdjustPricesMapper;
import com.zksr.product.service.IPrdtSkuService;
import com.zksr.product.service.IPrdtSpuService;
import com.zksr.product.service.IProductCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtAdjustPricesDtlMapper;
import com.zksr.product.convert.adjustPrices.PrdtAdjustPricesDtlConvert;
import com.zksr.product.domain.PrdtAdjustPricesDtl;
import com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesDtlSaveReqVO;
import com.zksr.product.service.IPrdtAdjustPricesDtlService;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.PRDT_ADD_TYPE_1;
import static com.zksr.product.enums.ErrorCodeConstants.*;


/**
 * 商品调价单明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Slf4j
@Service
public class PrdtAdjustPricesDtlServiceImpl implements IPrdtAdjustPricesDtlService {
    @Autowired
    private PrdtAdjustPricesDtlMapper prdtAdjustPricesDtlMapper;

    @Autowired
    private PrdtAdjustPricesMapper prdtAdjustPricesMapper;


    @Autowired
    private IPrdtSkuService prdtSkuService;
    @Autowired
    private IProductCacheService productCacheService;
    @Autowired
    private IPrdtSpuService prdtSpuService;


    @Override
    public Boolean insertPrdtAdjustPricesDtlBatch(PrdtAdjustPrices prdtAdjustPrices, List<PrdtAdjustPricesDtlSaveReqVO> createReqVO, Integer addType) {
        if (ToolUtil.isEmpty(createReqVO)) {
            throw new ServiceException("商品调价单明细不能为空");
        }

        createReqVO.forEach(dtl -> {
            //组装详情数据
            assembleDtl(dtl,addType);

            //设置主表ID
            dtl.setAdjustPricesId(prdtAdjustPrices.getAdjustPricesId());

        });

        //校验
        checkAddOrUpdateDtlList(createReqVO,prdtAdjustPrices.getSupplierId());

        List<PrdtAdjustPricesDtl> prdtAdjustPricesDtls = createReqVO.stream().map(PrdtAdjustPricesDtlConvert.INSTANCE::convert).collect(Collectors.toList());
        return prdtAdjustPricesDtlMapper.insertBatch(prdtAdjustPricesDtls);
    }


    @Override
    public void updatePrdtAdjustPricesDtlBatch(PrdtAdjustPricesSaveReqVO updateReqVO) {
        if (ToolUtil.isEmpty(updateReqVO.getDtlList())) {
            throw new RuntimeException("商品调价单明细不能为空");
        }
        // 先全部删除
        deletePrdtAdjustPricesDtlByAdjustPricesDtlId(updateReqVO.getAdjustPricesId());

        // 新增数据
        List<PrdtAdjustPricesDtl> insertPrdtAdjustPricesDtls = updateReqVO.getDtlList().stream()
                .map(adjustPrcesDtl -> {
                    SkuDTO skuDTO = productCacheService.getSkuDTO(adjustPrcesDtl.getSkuId());
                    if (ToolUtil.isEmpty(skuDTO)) {
                        throw exception(PRDT_SKU_NOT_EXISTS);
                    }
                    adjustPrcesDtl.setSpuId(skuDTO.getSpuId());
                    adjustPrcesDtl.setAdjustPricesId(updateReqVO.getAdjustPricesId());
                    adjustPrcesDtl.setAdjustPricesDtlId(null);
                    return PrdtAdjustPricesDtlConvert.INSTANCE.convert(adjustPrcesDtl);
                })
                .collect(Collectors.toList());

        if (ToolUtil.isNotEmpty(insertPrdtAdjustPricesDtls)) {
            prdtAdjustPricesDtlMapper.insertBatch(insertPrdtAdjustPricesDtls);
        }

        //更新主表sku条数
        //获取详情最新数据的条数
        Long dtlSize = (long) insertPrdtAdjustPricesDtls.size();
        if(updateReqVO.getSkuNum().compareTo(dtlSize) != 0){
            prdtAdjustPricesMapper.updateSkuNum(updateReqVO.getAdjustPricesId(), dtlSize);
        }

    }

    @Override
    public void deletePrdtAdjustPricesDtlByAdjustPricesDtlId(Long adjustPricesId) {
        // 删除
        prdtAdjustPricesDtlMapper.deleteByAdjustPricesId(adjustPricesId);
    }


    @Override
    public List<PrdtAdjustPricesDtl> getPrdtAdjustPricesDtlList(Long adjustPricesId) {
        return prdtAdjustPricesDtlMapper.selectByAdjustPricesId(adjustPricesId);
    }


    @Override
    public void updatePrdtAdjustPricesDtlValidState(List<PrdtAdjustPricesDtl> dtlList, Long validState) {
        dtlList.forEach(dtl -> {
            dtl.setValidState(validState);
            dtl.setValidTime(new Date());
        });
        prdtAdjustPricesDtlMapper.updateBatch(dtlList);
    }

    @Override
    public void adjustPricesDtlComplete(Long prdtAdjustPricesDtlId) {
        PrdtAdjustPricesDtl prdtAdjustPricesDtl = prdtAdjustPricesDtlMapper.selectById(prdtAdjustPricesDtlId);
        validatePrdtAdjustPricesDtlExists(prdtAdjustPricesDtl);
        if (!Objects.equals(prdtAdjustPricesDtl.getValidState(), NumberPool.LONG_ONE)) {
            log.error("调价详情ID：{},商品SKUID：{}的调价单明细状态异常!状态为:{}",prdtAdjustPricesDtlId,prdtAdjustPricesDtl.getSkuId(),prdtAdjustPricesDtl.getValidState());
            return;
        }

        PrdtSku sku = prdtSkuService.getPrdtSku(prdtAdjustPricesDtl.getSkuId());
        SpuDTO spuDTO = productCacheService.getSpuDTO(prdtAdjustPricesDtl.getSpuId());
        // 小单位标准价
        if (ToolUtil.isNotEmpty(prdtAdjustPricesDtl.getNewMinMarkPrice()) && ToolUtil.isNotEmpty(spuDTO.getMinUnit())) {
            sku.setMarkPrice(prdtAdjustPricesDtl.getNewMinMarkPrice());
        }
        // 小单位成本价
        if (ToolUtil.isNotEmpty(prdtAdjustPricesDtl.getNewMinCostPrice()) && ToolUtil.isNotEmpty(spuDTO.getMinUnit())) {
            sku.setCostPrice(prdtAdjustPricesDtl.getNewMinCostPrice());
        }

        // 中单位标准价
        if (ToolUtil.isNotEmpty(prdtAdjustPricesDtl.getNewMidMarkPrice()) && ToolUtil.isNotEmpty(spuDTO.getMidUnit())) {
            sku.setMidMarkPrice(prdtAdjustPricesDtl.getNewMidMarkPrice());
        }
        // 中单位成本价
        if (ToolUtil.isNotEmpty(prdtAdjustPricesDtl.getNewMidCostPrice()) && ToolUtil.isNotEmpty(spuDTO.getMidUnit())) {
            sku.setMidCostPrice(prdtAdjustPricesDtl.getNewMidCostPrice());
        }

        // 大单位标准价
        if (ToolUtil.isNotEmpty(prdtAdjustPricesDtl.getNewLargeMarkPrice()) && ToolUtil.isNotEmpty(spuDTO.getLargeUnit())) {
            sku.setLargeMarkPrice(prdtAdjustPricesDtl.getNewLargeMarkPrice());
        }
        // 大单位成本价
        if (ToolUtil.isNotEmpty(prdtAdjustPricesDtl.getNewLargeCostPrice()) && ToolUtil.isNotEmpty(spuDTO.getLargeUnit())) {
            sku.setLargeCostPrice(prdtAdjustPricesDtl.getNewLargeCostPrice());
        }
        // 更新SKU信息
        prdtSkuService.updatePrdtSku(SkuConvert.INSTANCE.convertPrdtSkuSaveReqVO(sku));

        // 修改商品调价单明细生效状态
        updatePrdtAdjustPricesDtlValidState(Collections.singletonList(prdtAdjustPricesDtl), NumberPool.LONG_TWO);

        // 更新SPU、SKU后续操作
        prdtSpuService.updatePrdtSpuAfter(PrdtSpuUpdateRespDTO.builder().spuId(prdtAdjustPricesDtl.getSpuId()).skuIdList(Arrays.asList(sku.getSkuId())).build());

    }

    @Override
    public void checkAddOrUpdateDtlList(List<PrdtAdjustPricesDtlSaveReqVO> delList,Long supplierId) {
        delList.forEach(dtl -> {
        //校验 商品调价单商品明细匹配入驻商与上传入驻商是否一致
        SkuDTO skuDTO = productCacheService.getSkuDTO(dtl.getSkuId());
        SpuDTO spuDTO = productCacheService.getSpuDTO(skuDTO.getSpuId());
        if (ToolUtil.isNotEmpty(spuDTO) && !Objects.equals(spuDTO.getSupplierId(), supplierId)) {
            throw exception(PRDT_ADJUST_PRICES_SUPPLIER_NOT_MATCH);
        }


        //校验 调增的价格不能为0或空
        boolean checkPriceZero = false;
        if(ToolUtil.isNotEmpty(skuDTO.getMarkPrice()) && ToolUtil.isNotEmpty(dtl.getOldMinMarkPrice())
                && (ToolUtil.isEmpty(dtl.getNewMinMarkPrice()) || dtl.getNewMinMarkPrice().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO )){
            checkPriceZero = true;
        }
        if(ToolUtil.isNotEmpty(skuDTO.getMidMarkPrice()) && ToolUtil.isNotEmpty(dtl.getOldMidMarkPrice())
                && (ToolUtil.isEmpty(dtl.getNewMidMarkPrice()) || dtl.getNewMidMarkPrice().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO )){
            checkPriceZero = true;
        }
        if(ToolUtil.isNotEmpty(skuDTO.getLargeMarkPrice()) && ToolUtil.isNotEmpty(dtl.getOldLargeMarkPrice())
                && (ToolUtil.isEmpty(dtl.getNewLargeMarkPrice()) || dtl.getNewLargeMarkPrice().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO )){
            checkPriceZero = true;
        }
        if(ToolUtil.isNotEmpty(skuDTO.getCostPrice()) && ToolUtil.isNotEmpty(dtl.getOldMinCostPrice())
                && (ToolUtil.isEmpty(dtl.getNewMinCostPrice()) || dtl.getNewMinCostPrice().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO )){
            checkPriceZero = true;
        }
        if(ToolUtil.isNotEmpty(skuDTO.getMidCostPrice()) && ToolUtil.isNotEmpty(dtl.getOldMidCostPrice())
                && (ToolUtil.isEmpty(dtl.getNewMidCostPrice()) || dtl.getNewMidCostPrice().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO )){
            checkPriceZero = true;
        }
        if(ToolUtil.isNotEmpty(skuDTO.getLargeCostPrice()) && ToolUtil.isNotEmpty(dtl.getOldLargeCostPrice())
                && (ToolUtil.isEmpty(dtl.getNewLargeCostPrice()) || dtl.getNewLargeCostPrice().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO )){
            checkPriceZero = true;
        }
        if(checkPriceZero){
            throw exception(PRDT_ADJUST_PRICES_CHECK_PRICE_ZERO,spuDTO.getSpuNo());
        }

        //校验 标准价不能低于供货价 优先大于新的供货价，新的供货价不存在 需要大于旧的；  如果没有调整标准价 新的供货价必须低于原标准价  供货价校验反之
            //小单位
            if(ToolUtil.isNotEmpty(dtl.getNewMinMarkPrice()) || ToolUtil.isNotEmpty(dtl.getNewMinCostPrice())){
                if((ToolUtil.isNotEmpty(dtl.getNewMinMarkPrice()) ? dtl.getNewMinMarkPrice() : skuDTO.getMarkPrice())
                                .compareTo((ToolUtil.isNotEmpty(dtl.getNewMinCostPrice()) ? dtl.getNewMinCostPrice() : skuDTO.getCostPrice()))
                        < NumberPool.INT_ZERO){
                    throw exception(PRDT_ADJUST_PRICES_CHECK_MIN_PRICE_SIZE,spuDTO.getSpuNo());
                }
            }

            //中单位
            if(ToolUtil.isNotEmpty(dtl.getNewMidMarkPrice()) || ToolUtil.isNotEmpty(dtl.getNewMidCostPrice())){
                if((ToolUtil.isNotEmpty(dtl.getNewMidMarkPrice()) ? dtl.getNewMidMarkPrice() : skuDTO.getMidMarkPrice())
                        .compareTo((ToolUtil.isNotEmpty(dtl.getNewMidCostPrice()) ? dtl.getNewMidCostPrice() : skuDTO.getMidCostPrice()))
                        < NumberPool.INT_ZERO){
                    throw exception(PRDT_ADJUST_PRICES_CHECK_MID_PRICE_SIZE,spuDTO.getSpuNo());
                }
            }

            //大单位
            if(ToolUtil.isNotEmpty(dtl.getNewLargeMarkPrice()) || ToolUtil.isNotEmpty(dtl.getNewLargeCostPrice())){
                if((ToolUtil.isNotEmpty(dtl.getNewLargeMarkPrice()) ? dtl.getNewLargeMarkPrice() : skuDTO.getLargeMarkPrice())
                        .compareTo((ToolUtil.isNotEmpty(dtl.getNewLargeCostPrice()) ? dtl.getNewLargeCostPrice() : skuDTO.getLargeCostPrice()))
                        < NumberPool.INT_ZERO){
                    throw exception(PRDT_ADJUST_PRICES_CHECK_LARGE_PRICE_SIZE,spuDTO.getSpuNo());
                }
            }

        });
    }


    private void validatePrdtAdjustPricesDtlExists(PrdtAdjustPricesDtl prdtAdjustPricesDtl) {
        if (ToolUtil.isEmpty(prdtAdjustPricesDtl)) {
            throw exception(PRDT_ADJUST_PRICES_DTL_NOT_EXISTS);
        }
    }

    private void assembleDtl(PrdtAdjustPricesDtlSaveReqVO dtl,Integer addType){
        SkuDTO skuDTO = productCacheService.getSkuDTO(dtl.getSkuId());

        SpuDTO spuDTO = productCacheService.getSpuDTO(skuDTO.getSpuId());
        if (ToolUtil.isEmpty(skuDTO)) {
            throw exception(PRDT_SKU_NOT_EXISTS);
        }
        if (ToolUtil.isEmpty(spuDTO)) {
            throw exception(PRDT_SPU_NOT_EXISTS);
        }
        dtl.setSpuId(skuDTO.getSpuId());


        //如果是导入时的新增 需要调整价格信息
        if(PRDT_ADD_TYPE_1.equals(addType)){
            //导入时 他的所有价格都会存入明细 如果没有设置新的价格 默认取原价格
            //导入时的原价取SKU实际价格，导入模板的各个原价只做参考
            //导入时，需要筛选SKU是否有大、中单位 如果是没有的 则过滤掉
            //导入的标准价不能低于供货价 优先大于新的供货价，新的供货价不存在 需要大于旧的；  如果没有导入标准价 新的供货价必须低于原标准价  (在校验方法中实现  不止导入、新增、修改也需要满足该条件)


            //设置小单位信息 如果没有设置新的价格 默认取原价格
            dtl.setOldMinMarkPrice(skuDTO.getMarkPrice());
            dtl.setOldMinCostPrice(skuDTO.getCostPrice());

            //小单位标准价
            dtl.setNewMinMarkPrice(ToolUtil.isNotEmpty(dtl.getNewMinMarkPrice()) ? dtl.getNewMinMarkPrice() : dtl.getOldMinMarkPrice());
            //小单位供货价
            dtl.setNewMinCostPrice(ToolUtil.isNotEmpty(dtl.getNewMinCostPrice()) ? dtl.getNewMinCostPrice() : dtl.getOldMinCostPrice());


            //设置中单位信息
            //校验是否存在中单位
            if(ToolUtil.isNotEmpty(spuDTO.getMidUnit())){
                //设置原价
                dtl.setOldMidMarkPrice(skuDTO.getMidMarkPrice());
                dtl.setOldMidCostPrice(skuDTO.getMidCostPrice());

                //设置新价格
                //中单位标准价
                dtl.setNewMidMarkPrice(ToolUtil.isNotEmpty(dtl.getNewMidMarkPrice()) ? dtl.getNewMidMarkPrice() : dtl.getOldMidMarkPrice());
                //中单位供货价
                dtl.setNewMidCostPrice(ToolUtil.isNotEmpty(dtl.getNewMidCostPrice()) ? dtl.getNewMidCostPrice() : dtl.getOldMidCostPrice());

            }else{
                //不存在中单位
                dtl.setOldMidMarkPrice(null);
                dtl.setOldMidCostPrice(null);
                dtl.setNewMidMarkPrice(null);
                dtl.setNewMidCostPrice(null);
            }


            //设置大单位信息
            //校验是否存在大单位
            if(ToolUtil.isNotEmpty(spuDTO.getLargeUnit())){
                //设置原价
                dtl.setOldLargeMarkPrice(skuDTO.getLargeMarkPrice());
                dtl.setOldLargeCostPrice(skuDTO.getLargeCostPrice());

                //设置新价格
                //大单位标准价
                dtl.setNewLargeMarkPrice(ToolUtil.isNotEmpty(dtl.getNewLargeMarkPrice()) ? dtl.getNewLargeMarkPrice() : dtl.getOldLargeMarkPrice());
                //大单位供货价
                dtl.setNewLargeCostPrice(ToolUtil.isNotEmpty(dtl.getNewLargeCostPrice()) ? dtl.getNewLargeCostPrice() : dtl.getOldLargeCostPrice());

            }else{
                //不存在大单位
                dtl.setOldLargeMarkPrice(null);
                dtl.setOldLargeCostPrice(null);
                dtl.setNewLargeMarkPrice(null);
                dtl.setNewLargeCostPrice(null);
            }
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.product.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 商品调价单明细 TODO 补充编号 ==========
    // ErrorCode PRDT_ADJUST_PRICES_DTL_NOT_EXISTS = new ErrorCode(TODO 补充编号, "商品调价单明细不存在");


}
