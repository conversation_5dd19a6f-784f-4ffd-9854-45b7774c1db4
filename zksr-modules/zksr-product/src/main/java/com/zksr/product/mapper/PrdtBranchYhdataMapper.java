package com.zksr.product.mapper;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataGroupRespVO;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataPageReqVO;
import com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataRespVO;
import com.zksr.product.domain.PrdtBranchYhdata;
import com.zksr.product.domain.po.YhMatchAreaItemPO;
import com.zksr.product.enums.YhMatchState;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * 门店批量要货Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@Mapper
public interface PrdtBranchYhdataMapper extends BaseMapperX<PrdtBranchYhdata> {
    default PageResult<PrdtBranchYhdata> selectPage(PrdtBranchYhdataPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtBranchYhdata>()
                    .eqIfPresent(PrdtBranchYhdata::getYhId, reqVO.getYhId())
                    .eqIfPresent(PrdtBranchYhdata::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtBranchYhdata::getPosYhBatchNo, reqVO.getPosYhBatchNo())
                    .eqIfPresent(PrdtBranchYhdata::getAreaId, reqVO.getAreaId())
                    .eqIfPresent(PrdtBranchYhdata::getBranchId, reqVO.getBranchId())
                    .likeIfPresent(PrdtBranchYhdata::getPosSkuName, reqVO.getPosSkuName())
                    .eqIfPresent(PrdtBranchYhdata::getPosSourceNo, reqVO.getPosSourceNo())
                    .eqIfPresent(PrdtBranchYhdata::getPosBarcode, reqVO.getPosBarcode())
                    .eqIfPresent(PrdtBranchYhdata::getMatchState, reqVO.getMatchState())
                .orderByDesc(PrdtBranchYhdata::getYhId));
    }

    default List<PrdtBranchYhdata> selectListByYhBatchNo(String yhBatchNo) {
        return selectList(new LambdaQueryWrapperX<PrdtBranchYhdata>()
                .eqIfPresent(PrdtBranchYhdata::getPosYhBatchNo, yhBatchNo)
        );
    }

    List<YhMatchAreaItemPO> matchSkuSourceNo(
            @Param("sourceNo") String sourceNo,
            @Param("sysCode") Long sysCode,
            @Param("areaId") Long areaId
    );

    List<YhMatchAreaItemPO> matchSkuBarcode(
            @Param("barcode") String barcode,
            @Param("sysCode") Long sysCode,
            @Param("areaId") Long areaId
    );

    List<YhMatchAreaItemPO> matchItemId(@Param("areaItemIdList") List<Long> areaItemIdList);

    default PrdtBranchYhdata selectLastYhDataBySourceNo(String posSourceNo, Long sysCode) {
        return selectOne(
                new LambdaQueryWrapperX<PrdtBranchYhdata>()
                        .eq(PrdtBranchYhdata::getSysCode, sysCode)
                        .eq(PrdtBranchYhdata::getPosSourceNo, posSourceNo)
                        .eq(PrdtBranchYhdata::getDelFlag, NumberPool.INT_ZERO)
                        .orderByDesc(PrdtBranchYhdata::getYhId)
                        .last(StringPool.LIMIT_ONE)
        );
    }

    default Long selectCountByBatchNo(String yhBatchNo) {
        return selectCount(
                new LambdaQueryWrapperX<PrdtBranchYhdata>()
                        .eq(PrdtBranchYhdata::getPosYhBatchNo, yhBatchNo)
        );
    }

    default void delete(List<Long> deleteYhIds) {
        // 逻辑删除
        LambdaQueryWrapperX<PrdtBranchYhdata> uw = new LambdaQueryWrapperX<>();
        uw.in(PrdtBranchYhdata::getYhId, deleteYhIds);

        // 删除标识
        PrdtBranchYhdata update = new PrdtBranchYhdata();
        update.setDelFlag(NumberPool.INT_ONE);
        update(update, uw);
    }

    List<PrdtBranchYhdataGroupRespVO> selectBatchTotalPageList(PrdtBranchYhdataPageReqVO reqVO);

    List<PrdtBranchYhdataRespVO> selectListExt(PrdtBranchYhdataPageReqVO pageReqVO);

    default List<PrdtBranchYhdata> selectAllForListData(Long minId) {
        return selectList(
                new LambdaQueryWrapperX<PrdtBranchYhdata>()
                        // 30天内的要货单
                        .gt(PrdtBranchYhdata::getBatchYmd, Integer.parseInt(DateUtil.format(DateUtil.offsetMonth(new Date(), -1), DatePattern.PURE_DATE_PATTERN)))
                        .eq(PrdtBranchYhdata::getDelFlag, NumberPool.INT_ZERO)
                        .eq(PrdtBranchYhdata::getMatchState, YhMatchState.SUCCESS.getState())
                        .gt(PrdtBranchYhdata::getYhId, minId)
                        .orderByAsc(PrdtBranchYhdata::getYhId)
                        .last("LIMIT 2000")
        );
    }
}
