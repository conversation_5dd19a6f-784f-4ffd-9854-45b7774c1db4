package com.zksr.product.controller.share.vo;

import lombok.*;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 商品分享对象 prdt_product_share
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Data
@ApiModel("商品分享 - prdt_product_share分页 Request VO")
public class PrdtProductShareSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value = "ID")
    private Long shareProductId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 商品上架ID */
    @Excel(name = "商品上架ID")
    @ApiModelProperty(value = "商品上架ID")
    private Long itemId;

    /** 商品单位 */
    @Excel(name = "商品单位")
    @ApiModelProperty(value = "商品单位")
    private Long unitSize;

    /** 发起分享IP */
    @Excel(name = "发起分享IP")
    @ApiModelProperty(value = "发起分享IP")
    private String remoteIp;

    /** 有效时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "有效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "有效时间")
    private Date expirationTime;

    /** 分享key */
    @Excel(name = "分享key")
    @ApiModelProperty(value = "分享key")
    private String shareKey;

    /** 上架商品ID集合 */
    @Excel(name = "分享key")
    @ApiModelProperty(value = "分享key")
    private List<BatchProductShareVO> batchProductShareVOList;



}
