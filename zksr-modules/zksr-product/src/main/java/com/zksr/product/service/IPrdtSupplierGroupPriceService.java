package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.domain.PrdtSupplierGroupPrice;
import com.zksr.product.controller.supplierGroupPrice.vo.PrdtSupplierGroupPricePageReqVO;
import com.zksr.product.controller.supplierGroupPrice.vo.PrdtSupplierGroupPriceSaveReqVO;

/**
 * 平台商城市分组价格Service接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface IPrdtSupplierGroupPriceService {

    /**
     * 新增平台商城市分组价格
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertPrdtSupplierGroupPrice(@Valid PrdtSupplierGroupPriceSaveReqVO createReqVO);

    /**
     * 修改平台商城市分组价格
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtSupplierGroupPrice(@Valid PrdtSupplierGroupPriceSaveReqVO updateReqVO);

    /**
     * 删除平台商城市分组价格
     *
     * @param groupId 平台商id
     * @param areaId
     */
    public void deletePrdtSupplierGroupPrice(Long groupId, Long areaId);

    /**
     * 批量删除平台商城市分组价格
     *
     * @param sysCodes 需要删除的平台商城市分组价格主键集合
     * @return 结果
     */
    public void deletePrdtSupplierGroupPriceBySysCodes(Long[] sysCodes);

    /**
     * 获得平台商城市分组价格
     *
     * @param sysCode 平台商id
     * @return 平台商城市分组价格
     */
    public PrdtSupplierGroupPrice getPrdtSupplierGroupPrice(Long sysCode);

    /**
     * 获得平台商城市分组价格分页
     *
     * @param pageReqVO 分页查询
     * @return 平台商城市分组价格分页
     */
    PageResult<PrdtSupplierGroupPricePageReqVO> getPrdtSupplierGroupPricePage(PrdtSupplierGroupPricePageReqVO pageReqVO);


    /**
     * 根据城市ID、分组ID获得平台商城市分组价格
     *
     * @param areaId
     * @return 平台商城市分组价格
     */
    public PrdtSupplierGroupPrice getPriceByAreaIdAndGroupId(Long areaId, Long groupId);


}
