package com.zksr.product.controller.combine;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.product.domain.PrdtSpuCombineDtl;
import com.zksr.product.service.IPrdtSpuCombineDtlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.product.controller.combine.vo.PrdtSpuCombineDtlPageReqVO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineDtlSaveReqVO;
import com.zksr.product.controller.combine.vo.PrdtSpuCombineDtlRespVO;
import com.zksr.product.convert.combine.PrdtSpuCombineDtlConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 组合商品详情Controller
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Api(tags = "管理后台 - 组合商品详情接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/combineDtl")
public class PrdtSpuCombineDtlController {
    @Autowired
    private IPrdtSpuCombineDtlService prdtSpuCombineDtlService;

    /**
     * 新增组合商品详情
     */
    @ApiOperation(value = "新增组合商品详情", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "组合商品详情", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody PrdtSpuCombineDtlSaveReqVO createReqVO) {
        return success(prdtSpuCombineDtlService.insertPrdtSpuCombineDtl(createReqVO));
    }

    /**
     * 修改组合商品详情
     */
    @ApiOperation(value = "修改组合商品详情", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "组合商品详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody PrdtSpuCombineDtlSaveReqVO updateReqVO) {
            prdtSpuCombineDtlService.updatePrdtSpuCombineDtl(updateReqVO);
        return success(true);
    }

    /**
     * 删除组合商品详情
     */
    @ApiOperation(value = "删除组合商品详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "组合商品详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sysCodes}")
    public CommonResult<Boolean> remove(@PathVariable Long[] sysCodes) {
        prdtSpuCombineDtlService.deletePrdtSpuCombineDtlBySysCodes(sysCodes);
        return success(true);
    }

    /**
     * 获取组合商品详情详细信息
     */
    @ApiOperation(value = "获得组合商品详情详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @GetMapping(value = "/{spuCombineId}")
    public CommonResult<List<PrdtSpuCombineDtlRespVO>> getInfo(@PathVariable("spuCombineId") Long spuCombineId) {
        List<PrdtSpuCombineDtlRespVO> prdtSpuCombineDtl = prdtSpuCombineDtlService.getPrdtSpuCombineDtl(spuCombineId);
        return success(prdtSpuCombineDtl);
    }

    /**
     * 分页查询组合商品详情
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得组合商品详情分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<PrdtSpuCombineDtlRespVO>> getPage(@Valid PrdtSpuCombineDtlPageReqVO pageReqVO) {
        PageResult<PrdtSpuCombineDtl> pageResult = prdtSpuCombineDtlService.getPrdtSpuCombineDtlPage(pageReqVO);
        return success(PrdtSpuCombineDtlConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "product:combine:add";
        /** 编辑 */
        public static final String EDIT = "product:combine:edit";
        /** 删除 */
        public static final String DELETE = "product:combine:remove";
        /** 列表 */
        public static final String LIST = "product:combine:list";
        /** 查询 */
        public static final String GET = "product:combine:query";
        /** 停用 */
        public static final String DISABLE = "product:combine:disable";
        /** 启用 */
        public static final String ENABLE = "product:combine:enable";
    }
}
