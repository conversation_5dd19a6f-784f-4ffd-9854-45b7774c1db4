package com.zksr.product.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.product.controller.share.vo.BatchProductShareVO;
import com.zksr.product.domain.PrdtProductShare;
import com.zksr.product.controller.share.vo.PrdtProductSharePageReqVO;
import com.zksr.product.controller.share.vo.PrdtProductShareSaveReqVO;

import java.util.List;

/**
 * 商品分享Service接口
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface IPrdtProductShareService {

    /**
     * 新增商品分享
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public String insertPrdtProductShare(@Valid List<BatchProductShareVO> createReqVO);

    /**
     * 修改商品分享
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updatePrdtProductShare(@Valid PrdtProductShareSaveReqVO updateReqVO);

    /**
     * 删除商品分享
     *
     * @param shareProductId ID
     */
    public void deletePrdtProductShare(Long shareProductId);

    /**
     * 批量删除商品分享
     *
     * @param shareProductIds 需要删除的商品分享主键集合
     * @return 结果
     */
    public void deletePrdtProductShareByShareProductIds(Long[] shareProductIds);

    /**
     * 获得商品分享
     *
     * @param shareProductId ID
     * @return 商品分享
     */
    public PrdtProductShare getPrdtProductShare(Long shareProductId);

    /**
     * 获得商品分享分页
     *
     * @param pageReqVO 分页查询
     * @return 商品分享分页
     */
    PageResult<PrdtProductShare> getPrdtProductSharePage(PrdtProductSharePageReqVO pageReqVO);


    /**
     * 根据分享Key获得商品分享记录
     *
     * @param shareKey
     * @return 商品分享集合
     */
    List<PrdtProductShare> getShareProductInfo(String shareKey);

}
