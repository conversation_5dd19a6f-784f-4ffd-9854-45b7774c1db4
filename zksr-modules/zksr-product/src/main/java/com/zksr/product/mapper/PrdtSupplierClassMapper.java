package com.zksr.product.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.api.supplierClass.dto.SupplierClassRespDTO;
import com.zksr.product.controller.supplierClass.vo.PrdtSupplierClassBatchEditReqVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtSupplierClass;
import com.zksr.product.controller.supplierClass.vo.PrdtSupplierClassPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 入驻商-平台商管理分类 关联关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Mapper
public interface PrdtSupplierClassMapper extends BaseMapperX<PrdtSupplierClass> {
    default PageResult<PrdtSupplierClass> selectPage(PrdtSupplierClassPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtSupplierClass>()
                    .eqIfPresent(PrdtSupplierClass::getCatgoryId, reqVO.getCatgoryId())
                    .eqIfPresent(PrdtSupplierClass::getSupplierId, reqVO.getSupplierId())
                    .eqIfPresent(PrdtSupplierClass::getSysCode, reqVO.getSysCode())
                .orderByDesc(PrdtSupplierClass::getCatgoryId));
    }

    /**
     * 分页查询入驻商管理分类信息（入驻商数据隔离）
     * @param reqVO
     * @return
     */
    List<SupplierClassRespDTO> selectListPage(@Param("reqVo") PrdtSupplierClassPageReqVO reqVo);

    /**
     * 批量修改平台入驻商管理类别售后时间配置
     * @param updateVo
     */
    void batchEditCatgoryAfterConfig(@Param("updateVo") PrdtSupplierClassBatchEditReqVO updateVo);

    /**
     * 获取入驻商 + 管理分类 分类下商品是否可售后配置
     * @param supplierIds
     * @param catgoryIds
     * @return
     */
    default List<PrdtSupplierClass> getSupplierClassAfterConfig(List<Long> supplierIds, List<Long> catgoryIds) {
        LambdaQueryWrapperX<PrdtSupplierClass> wrapper = new LambdaQueryWrapperX<>();
        wrapper.inIfPresent(PrdtSupplierClass::getSupplierId,supplierIds)
                        .inIfPresent(PrdtSupplierClass::getCatgoryId,catgoryIds);
        return selectList(wrapper);
    }

    /**
    * @Description: 通过入驻商ID获取绑定的所有管理分类ID
    * @Author: liuxingyu
    * @Date: 2024/3/6 15:20
    */
    default List<Long> getCatgoryIdListBySupplierId(Long supplierId){
        List<PrdtSupplierClass> supplierClasses = selectList(new LambdaQueryWrapper<PrdtSupplierClass>().eq(PrdtSupplierClass::getSupplierId, supplierId));
        if (ObjectUtil.isEmpty(supplierClasses)){
            return null;
        }
        return supplierClasses.stream().map(PrdtSupplierClass::getCatgoryId).collect(Collectors.toList());
    }

    /**
    * @Description: 通过ID删除入驻商与管理类别绑定关系
    * @Author: liuxingyu
    * @Date: 2024/3/6 17:20
    */
    default void deleteBySupplierId(Long supplierId){
        delete(new LambdaUpdateWrapper<PrdtSupplierClass>().eq(PrdtSupplierClass::getSupplierId,supplierId));
    }

    /**
    * @Description: 根据管理分类ID获取绑定集合
    * @Author: liuxingyu
    * @Date: 2024/4/15 20:10
    */
    default List<PrdtSupplierClass> selectByCatgoryId(Long catgoryId){
        return selectList(new LambdaUpdateWrapper<PrdtSupplierClass>().eq(PrdtSupplierClass::getCatgoryId,catgoryId));
    }

    /**
     * 删除平台管理类别时判断平台管理类别是否挂商品
     * @param catgoryId
     * @param sysCode
     * @return
     */
    default Long isCatgoryHangGoods(Long catgoryId, Long sysCode){
        return selectCount(new LambdaQueryWrapper<PrdtSupplierClass>()
                .eq(PrdtSupplierClass::getCatgoryId,catgoryId)
                .eq(PrdtSupplierClass::getSysCode,sysCode));
    }

}
