package com.zksr.product.controller.spu.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 商品SPU对象 prdt_spu
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Data
@ApiModel("商品SPU - prdt_spu Response VO")
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSpuRespVO {
    private static final long serialVersionUID = 1L;

    /** 商品SPU_id */
    @ApiModelProperty(value = "是否删除 1-是 0-否", example = "示例值")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", example = "示例值")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id", example = "示例值")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 平台商管理分类id */
    @Excel(name = "平台商管理分类id")
    @ApiModelProperty(value = "平台商管理分类id", example = "示例值")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long catgoryId;

    /** 平台商品牌id */
    @Excel(name = "平台商品牌id")
    @ApiModelProperty(value = "平台商品牌id", example = "示例值")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long brandId;

    /** 成本价 */
    @Excel(name = "成本价")
    @ApiModelProperty(value = "成本价", example = "示例值")
    private BigDecimal costPrice;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    @ApiModelProperty(value = "商品SPU编号", example = "示例值")
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称", example = "示例值")
    private String spuName;

    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面图", example = "示例值")
    private String thumb;

    /** 封面视频（url） */
    @Excel(name = "封面视频", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面视频", example = "示例值")
    private String thumbVideo;

    /** 详情页轮播（json） */
    @Excel(name = "详情页轮播", readConverterExp = "j=son")
    @ApiModelProperty(value = "详情页轮播", example = "示例值")
    private String images;

    /** 详情信息(富文本) */
    @Excel(name = "详情信息(富文本)")
    @ApiModelProperty(value = "详情信息(富文本)", example = "示例值")
    private String details;

    /** 库存数量 */
    @Excel(name = "库存数量")
    @ApiModelProperty(value = "库存数量", example = "示例值")
    private Long stock;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否", example = "示例值")
    private Long isDelete;

    /** 最旧生产日期 */
    @Excel(name = "最旧生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最旧生产日期",  example = "最旧生产日期")
    private Date oldestDate; 		 // 最旧生产日期

    /** 最新生产日期 */
    @Excel(name = "最新生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最新生产日期",  example = "最新生产日期")
    private Date latestDate; 		 // 最新生产日期

    /** 是否开启多规格 1-是 0-否 */
    @Excel(name = "是否开启多规格 1-是 0-否")
    @ApiModelProperty(value = "是否开启多规格 1-是 0-否",  example = "1-是 0-否")
    private Long isSpecs;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)", example = "示例值")
    private Long status;

    /** 产地 */
    @Excel(name = "产地")
    @ApiModelProperty(value = "产地", example = "示例值")
    private String originPlace;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "示例值")
    private String memo;

    /** 商品规格 */
    @Excel(name = "商品规格")
    @ApiModelProperty(value = "商品规格")
    private String specName;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @Excel(name = "最小单位-数据字典（sys_prdt_unit）", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "最小单位-数据字典（sys_prdt_unit）")
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    @Excel(name = "中单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "中单位-数据字典（sys_prdt_unit）")
    private Long midUnit;

    /** 中单位换算数量（换算成最小单位）） */
    @Excel(name = "中单位换算数量（换算成最小单位）")
    @ApiModelProperty(value = "中单位换算数量（换算成最小单位）")
    private Long midSize;

    /** 大单位-数据字典（sys_prdt_unit） */
    @Excel(name = "大单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "大单位-数据字典（sys_prdt_unit）")
    private Long largeUnit;

    /** 大单位换算数量（换算成最小单位）） */
    @Excel(name = "大单位换算数量（换算成最小单位）")
    @ApiModelProperty(value = "大单位换算数量（换算成最小单位）")
    private Long largeSize;

    /** 是否开启联动换算 1-是 0-否 */
    @Excel(name = "是否开启联动换算 1-是 0-否")
    @ApiModelProperty(value = "是否开启联动换算 1-是 0-否")
    private Integer isLinkage;

    /** 保质期 */
    @Excel(name = "保质期")
    @ApiModelProperty(value = "保质期" ,required = true)
    private Integer expirationDate;		 // 保质期

    /** SPU辅助的商品编号 */
    @Excel(name = "SPU辅助的商品编号")
    @ApiModelProperty(value = "SPU辅助的商品编号")
    private String auxiliarySpuNo;

    public PrdtSpuRespVO(Long spuId, String spuNo) {
        this.spuId = spuId;
        this.spuNo = spuNo;
    }

    public PrdtSpuRespVO(String spuNo) {
        this.spuNo = spuNo;
    }

}
