package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 素材应用对象 prdt_material_apply
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@TableName(value = "prdt_material_apply")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtMaterialApply extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 素材应用id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long materialApplyId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 素材id */
    @Excel(name = "素材id")
    private Long materialId;

    /** 素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品 */
    @Excel(name = "素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品")
    private Integer applyType;

    /** 素材应用类型id */
    @Excel(name = "素材应用类型id")
    private Long applyId;

    /** 生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生效时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 失效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "失效时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 操作人 */
    @Excel(name = "操作人")
    private Long applyUserId;

}
