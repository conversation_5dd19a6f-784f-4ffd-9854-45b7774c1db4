package com.zksr.product.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 经营屏蔽方案对象 prdt_block_scheme
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@TableName(value = "prdt_block_scheme")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class PrdtBlockScheme extends BaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键 */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long blockSchemeId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 方案编码 */
    @Excel(name = "方案编码")
    private String schemeNo;

    /** 方案名称 */
    @Excel(name = "方案名称")
    private String schemeName;

    /** 城市id */
    @Excel(name = "城市id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 状态：1正常 0禁用 */
    @Excel(name = "状态：1正常 0禁用")
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 删除标志 0 未删除 1 已删除 */
    private Integer delFlag;

}
