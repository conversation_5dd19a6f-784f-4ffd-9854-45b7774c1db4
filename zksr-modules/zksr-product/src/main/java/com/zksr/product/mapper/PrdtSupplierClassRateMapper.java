package com.zksr.product.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.database.query.QueryWrapperX;
import com.zksr.product.domain.PrdtSupplierClassRate;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2024/9/13
 * @desc
 */
@Mapper
public interface PrdtSupplierClassRateMapper extends BaseMapperX<PrdtSupplierClassRate> {
    default void deleteBySupplierId(Long supplierId) {
        delete(
                new LambdaQueryWrapperX<PrdtSupplierClassRate>()
                        .eq(PrdtSupplierClassRate::getSupplierId, supplierId)
        );
    }

    default PrdtSupplierClassRate selectBySupplierIdAndCatgoryId(Long supplierId, Long catgoryId) {
        return selectOne(
                new LambdaQueryWrapperX<PrdtSupplierClassRate>()
                        .eq(PrdtSupplierClassRate::getSupplierId, supplierId)
                        .eq(PrdtSupplierClassRate::getCatgoryId, catgoryId)
        );
    }

    default void updatePO(PrdtSupplierClassRate rate) {
        LambdaUpdateWrapper<PrdtSupplierClassRate> wrapperX = new LambdaUpdateWrapper<PrdtSupplierClassRate>()
                .eq(PrdtSupplierClassRate::getSupplierId, rate.getSupplierId())
                .eq(PrdtSupplierClassRate::getCatgoryId, rate.getCatgoryId());
        if (Objects.isNull(rate.getSaleTotalRate())) {
            wrapperX.set(PrdtSupplierClassRate::getSaleTotalRate, null);
        }
        update(rate, wrapperX );
    }

    default List<PrdtSupplierClassRate> selectBySupplierId(Long supplierId) {
        return selectList(
                new LambdaQueryWrapperX<PrdtSupplierClassRate>()
                        .eq(PrdtSupplierClassRate::getSupplierId, supplierId)
        );
    }

    default List<PrdtSupplierClassRate> selectBySupplierAndCategoryList(List<Long> supplierList, List<Long> categoryIdList) {
        return selectList(
                new LambdaQueryWrapperX<PrdtSupplierClassRate>()
                        .in(PrdtSupplierClassRate::getSupplierId, supplierList)
                        .in(PrdtSupplierClassRate::getCatgoryId, categoryIdList)
        );
    }
}
