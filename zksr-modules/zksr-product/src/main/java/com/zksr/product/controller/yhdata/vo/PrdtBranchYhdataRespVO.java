package com.zksr.product.controller.yhdata.vo;

import lombok.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 门店批量要货对象 prdt_branch_yhdata
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@Data
@ApiModel("门店批量要货 - prdt_branch_yhdata Response VO")
public class PrdtBranchYhdataRespVO {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "匹配失败原因")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long yhId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 要货批次号 */
    @Excel(name = "要货批次号")
    @ApiModelProperty(value = "要货批次号")
    private String posYhBatchNo;

    /** 区域城市id */
    @Excel(name = "区域城市id")
    @ApiModelProperty(value = "区域城市id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 商品名 */
    @Excel(name = "商品名")
    @ApiModelProperty(value = "商品名")
    private String posSkuName;

    /** 商品编码 */
    @Excel(name = "商品编码")
    @ApiModelProperty(value = "商品编码")
    private String posSourceNo;

    /** 国条码 */
    @Excel(name = "国条码")
    @ApiModelProperty(value = "国条码")
    private String posBarcode;

    /** 销售数量 */
    @Excel(name = "销售数量")
    @ApiModelProperty(value = "销售数量")
    private Integer posSalesQty;

    /** 实际库存数量 */
    @Excel(name = "实际库存数量")
    @ApiModelProperty(value = "实际库存数量")
    private Integer posStockQty;

    /** 门店库存数量 */
    @Excel(name = "门店库存数量")
    @ApiModelProperty(value = "门店库存数量")
    private Integer posBranchStockQty;

    /** 原始要货数量 */
    @Excel(name = "原始要货数量")
    @ApiModelProperty(value = "原始要货数量")
    private Integer sourceYhQty;

    /** 建议购买数量 */
    @Excel(name = "建议购买数量")
    @ApiModelProperty(value = "建议购买数量")
    private Integer posSuggestQty;

    /** 单位名称 */
    @Excel(name = "单位名称")
    @ApiModelProperty(value = "单位名称")
    private String posUnitName;

    /** 最迟订货时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "最迟订货时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "最迟订货时间")
    private Date posMaxLateTime;

    /** 区域城市上架商品id */
    @Excel(name = "区域城市上架商品id")
    @ApiModelProperty(value = "区域城市上架商品id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long mallAreaItemId;

    /** 匹配的sku id */
    @Excel(name = "匹配的sku id")
    @ApiModelProperty(value = "匹配的sku id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long mallMatchSkuId;

    /** 单位大小 */
    @Excel(name = "单位大小")
    @ApiModelProperty(value = "单位大小")
    private Integer mallUnitType;

    /** 匹配状态（数据字典）0-未匹配, 1-匹配成功, 2-匹配失败 */
    @Excel(name = "匹配状态", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "匹配状态")
    private Integer matchState;

    /** 匹配失败原因（数据字典）0-库存不足, 1-已下架, 2-未匹配到商品 */
    @Excel(name = "匹配失败原因", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "匹配失败原因")
    private Integer failReason;

    @ApiModelProperty(value = "spu名称")
    private String spuName;

    /** 入驻商ID */
    @Excel(name = "入驻商ID")
    @ApiModelProperty(value = "入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "sku属性")
    private String properties;

    /** 30天人均销量 */
    @ApiModelProperty("30天人均销量")
    private Integer pos30dayAvgSales;

    /** 安全库存天数 */
    @ApiModelProperty("安全库存天数")
    private Integer posSafetyDays;

    /** 安全库存 */
    @ApiModelProperty("安全库存")
    private Integer posSafetyStock;

    /** 上次补货数量 */
    @ApiModelProperty("上次补货数量")
    private Integer lastSubmitQty;
}
