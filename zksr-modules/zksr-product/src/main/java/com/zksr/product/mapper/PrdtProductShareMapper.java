package com.zksr.product.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.domain.PrdtProductShare;
import com.zksr.product.controller.share.vo.PrdtProductSharePageReqVO;

import java.util.List;


/**
 * 商品分享Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Mapper
public interface PrdtProductShareMapper extends BaseMapperX<PrdtProductShare> {
    default PageResult<PrdtProductShare> selectPage(PrdtProductSharePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrdtProductShare>()
                    .eqIfPresent(PrdtProductShare::getShareProductId, reqVO.getShareProductId())
                    .eqIfPresent(PrdtProductShare::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(PrdtProductShare::getItemId, reqVO.getItemId())
                    .eqIfPresent(PrdtProductShare::getUnitSize, reqVO.getUnitSize())
                    .eqIfPresent(PrdtProductShare::getRemoteIp, reqVO.getRemoteIp())
                    .eqIfPresent(PrdtProductShare::getExpirationTime, reqVO.getExpirationTime())
                    .eqIfPresent(PrdtProductShare::getShareKey, reqVO.getShareKey())
                .orderByDesc(PrdtProductShare::getShareProductId));
    }

    default List<PrdtProductShare> getShareProductInfoByShareKey(String shareKey) {
        return selectList(new LambdaQueryWrapperX<PrdtProductShare>()
                .eq(PrdtProductShare::getShareKey, shareKey));
    }

}
