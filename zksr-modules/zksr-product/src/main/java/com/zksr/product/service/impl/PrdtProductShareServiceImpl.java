package com.zksr.product.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.ip.IpUtils;
import com.zksr.common.core.web.pojo.PageResult;

import com.zksr.product.controller.share.vo.BatchProductShareVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.product.mapper.PrdtProductShareMapper;
import com.zksr.product.convert.share.PrdtProductShareConvert;
import com.zksr.product.domain.PrdtProductShare;
import com.zksr.product.controller.share.vo.PrdtProductSharePageReqVO;
import com.zksr.product.controller.share.vo.PrdtProductShareSaveReqVO;
import com.zksr.product.service.IPrdtProductShareService;

import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * 商品分享Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Service
public class PrdtProductShareServiceImpl implements IPrdtProductShareService {
    @Autowired
    private PrdtProductShareMapper prdtProductShareMapper;



    /**
     * 新增商品分享
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public String insertPrdtProductShare(List<BatchProductShareVO> createReqVO) {
        // 检查输入参数是否为空
        if (createReqVO == null || ToolUtil.isEmpty(createReqVO)) {
            return null;
        }

        // 初始化共享密钥和远程IP
        String shareKey = RandomUtil.randomString(48);
        String remoteIp = IpUtils.getIpAddr(ServletUtils.getRequest());
        Date expirationTime = DateUtils.getDateAdd(30);
        // 创建产品分享列表
        List<PrdtProductShare> prdtProductShareList = createReqVO.stream()
                .map(batchProductShareVO -> {
                    PrdtProductShare prdtProductShare = HutoolBeanUtils.toBean(batchProductShareVO, PrdtProductShare.class);
                    prdtProductShare.setExpirationTime(expirationTime);
                    prdtProductShare.setShareKey(shareKey);
                    prdtProductShare.setRemoteIp(remoteIp);
                    return prdtProductShare;
                })
                .collect(Collectors.toList());

        try {
            // 批量插入数据
            prdtProductShareMapper.insertBatch(prdtProductShareList);
        } catch (Exception e) {
            // 记录异常日志
            throw exception(SHARE_PRODUCT_SAVE_FAIL);
        }

        // 返回成功标志
        return shareKey;
    }


    /**
     * 修改商品分享
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updatePrdtProductShare(PrdtProductShareSaveReqVO updateReqVO) {
        prdtProductShareMapper.updateById(PrdtProductShareConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除商品分享
     *
     * @param shareProductId ID
     */
    @Override
    public void deletePrdtProductShare(Long shareProductId) {
        // 删除
        prdtProductShareMapper.deleteById(shareProductId);
    }

    /**
     * 批量删除商品分享
     *
     * @param shareProductIds 需要删除的商品分享主键
     * @return 结果
     */
    @Override
    public void deletePrdtProductShareByShareProductIds(Long[] shareProductIds) {
        for(Long shareProductId : shareProductIds){
            this.deletePrdtProductShare(shareProductId);
        }
    }

    /**
     * 获得商品分享
     *
     * @param shareProductId ID
     * @return 商品分享
     */
    @Override
    public PrdtProductShare getPrdtProductShare(Long shareProductId) {
        return prdtProductShareMapper.selectById(shareProductId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<PrdtProductShare> getPrdtProductSharePage(PrdtProductSharePageReqVO pageReqVO) {
        return prdtProductShareMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PrdtProductShare> getShareProductInfo(String shareKey) {
        return prdtProductShareMapper.getShareProductInfoByShareKey(shareKey);
    }


/*    private void validatePrdtProductShareExists(Long shareProductId) {
        if (prdtProductShareMapper.selectById(shareProductId) == null) {
            throw exception(PRDT_PRODUCT_SHARE_NOT_EXISTS);
        }
    }*/

    // TODO 待办：请将下面的错误码复制到 com.zksr.product.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 商品分享 TODO 补充编号 ==========
    // ErrorCode PRDT_PRODUCT_SHARE_NOT_EXISTS = new ErrorCode(TODO 补充编号, "商品分享不存在");


}
