<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtAreaClassMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="prdtAreaClassMapper" resultType="com.zksr.product.domain.PrdtAreaClass">
        SELECT
            pa.*
        FROM
            (
                SELECT DISTINCT
                    pa.area_class_id,
                    pa.sys_code,
                    pa.area_id,
                    pa.pid,
                    pa.area_class_name,
                    pa.icon,
                    pa.sort,
                    pa.STATUS,
                    pa.dzwl_flag,
                    pa.supplier_id
                FROM
                    prdt_area_class pa
                        INNER JOIN prdt_area_class pa2 ON pa2.pid = pa.area_class_id
                        AND pa.STATUS = '1'
                        AND pa2.STATUS = '1'
                        INNER JOIN prdt_area_class pa3 ON pa3.pid = pa2.area_class_id
                        AND pa3.STATUS = '1'
                        LEFT JOIN prdt_channel_area_class pc ON pa3.area_class_id = pc.area_class_id
                WHERE
                    pa.LEVEL = 1
                  AND pa.dzwl_flag = 0
                  AND pc.area_class_id IS NULL
                  AND pa3.area_id = #{areaId}
                ORDER BY
                    pa.sort,
                    pa.create_time
            ) pa UNION ALL
        SELECT
            pa2.*
        FROM
            (
                SELECT DISTINCT
                    pa2.area_class_id,
                    pa2.sys_code,
                    pa2.area_id,
                    pa2.pid,
                    pa2.area_class_name,
                    pa2.icon,
                    pa2.sort,
                    pa2.STATUS,
                    pa2.dzwl_flag,
                    pa2.supplier_id
                FROM
                    prdt_area_class pa
                        INNER JOIN prdt_area_class pa2 ON pa2.pid = pa.area_class_id
                        AND pa.STATUS = '1'
                        AND pa2.STATUS = '1'
                        INNER JOIN prdt_area_class pa3 ON pa3.pid = pa2.area_class_id
                        AND pa3.STATUS = '1'
                        LEFT JOIN prdt_channel_area_class pc ON pa3.area_class_id = pc.area_class_id
                WHERE
                    pa.LEVEL = 1
                  AND pa.dzwl_flag = 0
                  AND pc.area_class_id IS NULL
                  AND pa3.area_id = #{areaId}
                ORDER BY
                    pa2.sort,
                    pa2.create_time
            ) pa2 UNION ALL
        SELECT
            pa3.*
        FROM
            (
                SELECT
                    pa3.area_class_id,
                    pa3.sys_code,
                    pa3.area_id,
                    pa3.pid,
                    pa3.area_class_name,
                    pa3.icon,
                    pa3.sort,
                    pa3.STATUS,
                    pa3.dzwl_flag,
                    pa3.supplier_id
                FROM
                    prdt_area_class pa
                        INNER JOIN prdt_area_class pa2 ON pa2.pid = pa.area_class_id
                        AND pa.STATUS = '1'
                        AND pa2.STATUS = '1'
                        INNER JOIN prdt_area_class pa3 ON pa3.pid = pa2.area_class_id
                        AND pa3.STATUS = '1'
                        LEFT JOIN prdt_channel_area_class pc ON pa3.area_class_id = pc.area_class_id
                WHERE
                    pa.LEVEL = 1
                  AND pa.dzwl_flag = 0
                  AND pc.area_class_id IS NULL
                  AND pa3.area_id = #{areaId}
                ORDER BY
                    pa3.sort,
                    pa3.create_time
            ) pa3
    </select>
    <select id="getAreaClassChannelList" resultType="com.zksr.product.domain.PrdtAreaClass">
        SELECT
            pa.*
        FROM
            (
                SELECT DISTINCT
                    pa.area_class_id,
                    pa.sys_code,
                    pa.area_id,
                    pa.pid,
                    pa.area_class_name,
                    pa.icon,
                    pa.sort,
                    pa.STATUS,
                    pa.dzwl_flag,
                    pa.supplier_id
                FROM
                    prdt_area_class pa
                        INNER JOIN prdt_area_class pa2 ON pa2.pid = pa.area_class_id
                        AND pa.STATUS = '1'
                        AND pa2.STATUS = '1'
                        INNER JOIN prdt_area_class pa3 ON pa3.pid = pa2.area_class_id
                        AND pa3.STATUS = '1'
                        LEFT JOIN prdt_channel_area_class pc ON pa3.area_class_id = pc.area_class_id
                WHERE
                    pa.LEVEL = 1
                  AND pa.dzwl_flag = 0
                  AND pc.channel_id = #{channelId}
                ORDER BY
                    pa.sort,
                    pa.create_time
            ) pa UNION ALL
        SELECT
            pa2.*
        FROM
            (
                SELECT DISTINCT
                    pa2.area_class_id,
                    pa2.sys_code,
                    pa2.area_id,
                    pa2.pid,
                    pa2.area_class_name,
                    pa2.icon,
                    pa2.sort,
                    pa2.STATUS,
                    pa2.dzwl_flag,
                    pa2.supplier_id
                FROM
                    prdt_area_class pa
                        INNER JOIN prdt_area_class pa2 ON pa2.pid = pa.area_class_id
                        AND pa.STATUS = '1'
                        AND pa2.STATUS = '1'
                        INNER JOIN prdt_area_class pa3 ON pa3.pid = pa2.area_class_id
                        AND pa3.STATUS = '1'
                        LEFT JOIN prdt_channel_area_class pc ON pa3.area_class_id = pc.area_class_id
                WHERE
                    pa.LEVEL = 1
                  AND pa.dzwl_flag = 0
                  AND pc.channel_id = #{channelId}

                ORDER BY
                    pa2.sort,
                    pa2.create_time
            ) pa2 UNION ALL
        SELECT
            pa3.*
        FROM
            (
                SELECT
                    pa3.area_class_id,
                    pa3.sys_code,
                    pa3.area_id,
                    pa3.pid,
                    pa3.area_class_name,
                    pa3.icon,
                    pa3.sort,
                    pa3.STATUS,
                    pa3.dzwl_flag,
                    pa3.supplier_id
                FROM
                    prdt_area_class pa
                        INNER JOIN prdt_area_class pa2 ON pa2.pid = pa.area_class_id
                        AND pa.STATUS = '1'
                        AND pa2.STATUS = '1'
                        INNER JOIN prdt_area_class pa3 ON pa3.pid = pa2.area_class_id
                        AND pa3.STATUS = '1'
                        LEFT JOIN prdt_channel_area_class pc ON pa3.area_class_id = pc.area_class_id
                WHERE
                    pa.LEVEL = 1
                  AND pa.dzwl_flag = 0
                  AND pc.channel_id = #{channelId}

                ORDER BY
                    pa3.sort,
                    pa3.create_time
            ) pa3
    </select>

    <select id="getAreaClassBranchList" resultType="com.zksr.product.domain.PrdtAreaClass">
        SELECT
            pa.*
        FROM
            (
                SELECT DISTINCT
                    pa.area_class_id,
                    pa.sys_code,
                    pa.area_id,
                    pa.pid,
                    pa.area_class_name,
                    pa.icon,
                    pa.sort,
                    pa.`status`,
                    pa.dzwl_flag,
                    pa.supplier_id,
                    pa.create_time,
                    pa.level
                FROM
                    prdt_area_class pa
                    INNER JOIN prdt_area_class pa2 ON pa2.pid = pa.area_class_id AND pa.`status` = '1' AND pa2.`status` = '1'
                    INNER JOIN prdt_area_class pa3 ON pa3.pid = pa2.area_class_id AND pa3.`status` = '1'
                    LEFT JOIN prdt_channel_area_class pc ON pa3.area_class_id = pc.area_class_id
                WHERE
                    pa.LEVEL = 1
                    AND pa.dzwl_flag = 1
                    AND pc.channel_id IS NULL
                    AND pa.del_flag = '0'
                    AND pa2.del_flag = '0'
                    AND pa3.del_flag = '0'
                    AND pa.supplier_id IN
                    <foreach collection="supplierIds" item="supplierId" open="(" separator="," close=")">
                        #{supplierId}
                    </foreach>
                ORDER BY
                    pa.sort,
                    pa.create_time
            ) pa UNION ALL
        SELECT
            pa2.*
        FROM
            (
                SELECT DISTINCT
                    pa2.area_class_id,
                    pa2.sys_code,
                    pa2.area_id,
                    pa2.pid,
                    pa2.area_class_name,
                    pa2.icon,
                    pa2.sort,
                    pa2.`status`,
                    pa2.dzwl_flag,
                    pa2.supplier_id,
                    pa2.create_time,
                    pa2.level
                FROM
                    prdt_area_class pa
                    INNER JOIN prdt_area_class pa2 ON pa2.pid = pa.area_class_id AND pa.`status` = '1' AND pa2.`status` = '1'
                    INNER JOIN prdt_area_class pa3 ON pa3.pid = pa2.area_class_id AND pa3.`status` = '1'
                    LEFT JOIN prdt_channel_area_class pc ON pa3.area_class_id = pc.area_class_id
                WHERE
                    pa.LEVEL = 1
                    AND pa.dzwl_flag = 1
                    AND pc.channel_id IS NULL
                    AND pa.del_flag = '0'
                    AND pa2.del_flag = '0'
                    AND pa3.del_flag = '0'
                    AND pa.supplier_id IN
                    <foreach collection="supplierIds" item="supplierId" open="(" separator="," close=")">
                        #{supplierId}
                    </foreach>
                ORDER BY
                    pa2.sort,
                    pa2.create_time
            ) pa2 UNION ALL
        SELECT
            pa3.*
        FROM
            (
                SELECT
                    pa3.area_class_id,
                    pa3.sys_code,
                    pa3.area_id,
                    pa3.pid,
                    pa3.area_class_name,
                    pa3.icon,
                    pa3.sort,
                    pa3.`status`,
                    pa3.dzwl_flag,
                    pa3.supplier_id,
                    pa3.create_time,
                    pa3.level
                FROM
                    prdt_area_class pa
                    INNER JOIN prdt_area_class pa2 ON pa2.pid = pa.area_class_id AND pa.`status` = '1' AND pa2.`status` = '1'
                    INNER JOIN prdt_area_class pa3 ON pa3.pid = pa2.area_class_id AND pa3.`status` = '1'
                    LEFT JOIN prdt_channel_area_class pc ON pa3.area_class_id = pc.area_class_id
                WHERE
                    pa.LEVEL = 1
                    AND pa.dzwl_flag = 1
                    AND pc.channel_id IS NULL
                    AND pa.del_flag = '0'
                    AND pa2.del_flag = '0'
                    AND pa3.del_flag = '0'
                    AND pa.supplier_id IN
                    <foreach collection="supplierIds" item="supplierId" open="(" separator="," close=")">
                        #{supplierId}
                    </foreach>
                ORDER BY
                    pa3.sort,
                    pa3.create_time
            ) pa3
    </select>

    <select id="getAreaClassAreaChannelList" resultType="com.zksr.product.domain.PrdtAreaClass">
        SELECT
            pac.area_class_id,
            pac.sys_code,
            pac.area_id,
            pac.pid,
            pac.area_class_name,
            pac.icon,
            pac.sort,
            pac.status,
            pac.level,
            pac.dzwl_flag,
            pac.supplier_id
        FROM
            prdt_area_class pac
        WHERE
            pac.area_id = #{areaId}
            AND pac.dzwl_flag = 0
            AND pac.status = 1
            AND pac.del_flag = 0
            AND (
                NOT EXISTS ( SELECT pcac.area_class_id FROM prdt_channel_area_class pcac WHERE pcac.area_class_id = pac.area_class_id )
                <if test="null != chanelId and chanelId != 0 ">
                    OR EXISTS ( SELECT pcac.area_class_id FROM prdt_channel_area_class pcac WHERE pcac.channel_id = #{chanelId} AND pcac.area_class_id = pac.area_class_id )
                </if>
                )
        ORDER BY
            pac.sort,
            pac.create_time
    </select>


    <insert id="copyToAreaClass">
        INSERT INTO prdt_area_class (
            area_class_id,
            sys_code,
            create_by,
            create_time,
            update_by,
            update_time,
            area_id,
            pid,
            area_class_name,
            icon,
            sort,
            status,
            dzwl_flag,
            supplier_id,
            level
        ) SELECT NULL,
                 sys_code,
                 create_by,
                 create_time,
                 update_by,
                 update_time,
                 #{areaId},
                 pid,
                 catgory_name,
                 icon,
                 sort,
                 status,
                 0,
                 NULL,
                 level
        FROM
            prdt_catgory
        WHERE
            STATUS = 1
          AND catgory_name NOT IN ( SELECT area_class_name FROM prdt_area_class WHERE area_id = #{areaId} );
    </insert>

    <!-- 根据城市ID获取该城市下不存在的平台展示分类 -->
    <select id="getAreaClassNotExistByAreaId" resultType="com.zksr.product.controller.catgory.dto.PrdtCatgoryCopyRespDTO">
        SELECT
               catgory.catgory_id,
               catgory.sys_code,
               catgory.catgory_name,
               catgory.icon,
               catgory.sort,
               catgory.status,
               catgory.level,
               catgory.pid,
               pcatgory.level AS plevel,
               pcatgory.catgory_name AS pcatgoryName,
               pcatgory2.level AS plevel2,
               pcatgory2.catgory_name AS pcatgoryName2
        FROM
            prdt_catgory catgory
        LEFT JOIN prdt_catgory pcatgory ON pcatgory.catgory_id = catgory.pid
        LEFT JOIN prdt_catgory pcatgory2 ON pcatgory2.catgory_id = pcatgory.pid
        WHERE
            catgory.STATUS = 1
          AND catgory.level in (1, 2, 3)
          AND catgory.catgory_name NOT IN (
        SELECT area_class_name FROM prdt_area_class WHERE 1=1
        and del_flag = 0 and area_id = #{areaId}
        );
    </select>
    <select id="selectCountByAreaItem" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            (
                SELECT
                    pac3.area_class_id
                FROM
                    prdt_area_class pac3
                WHERE
                    pac3.area_class_id = #{areaClassId}
                UNION
                SELECT
                    pac3.area_class_id
                FROM
                    prdt_area_class pac2
                    LEFT JOIN prdt_area_class pac3 ON pac3.pid = pac2.area_class_id
                WHERE
                    pac2.area_class_id = #{areaClassId}
                UNION
                SELECT
                    pac3.area_class_id
                FROM
                    prdt_area_class pac1
                    LEFT JOIN prdt_area_class pac2 ON pac2.pid = pac1.area_class_id
                    LEFT JOIN prdt_area_class pac3 ON pac3.pid = pac2.area_class_id
                WHERE
                    pac2.area_class_id = #{areaClassId}
            ) as p3acip2p3aci
            LEFT JOIN prdt_area_item pai ON pai.area_class_id = p3acip2p3aci.area_class_id
        WHERE
            pai.shelf_status = 1
    </select>
    <select id="selectPrdtAreaClassExportList"
            resultType="com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo">


    </select>
    <!-- 根据入驻商id、城市id、分类名称列表查询城市分类 -->
    <select id="getAreaClassBySupplierAreaAndNames" resultType="com.zksr.product.domain.PrdtAreaClass">
        SELECT *
        FROM prdt_area_class
        WHERE del_flag = '0'
<!--          AND supplier_id = #{supplierId}-->
          AND area_id = #{areaId}
          <if test="areaClassNames != null and areaClassNames.size > 0">
            AND area_class_name IN
            <foreach collection="areaClassNames" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
          </if>
    </select>
</mapper>
