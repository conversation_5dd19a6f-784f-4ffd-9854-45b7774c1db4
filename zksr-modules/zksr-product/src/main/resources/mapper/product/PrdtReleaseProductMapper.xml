<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtReleaseProductMapper">

    <select id="selectType0" resultType="com.zksr.product.controller.spu.vo.PrdtReleaseProductRespVO">
        SELECT
            <if test="reqVO.productType == 'local'">
                'local' AS productType,
                item.area_item_id AS itemId,
                item.area_id,
                class1.area_class_name firstClassName,
                class2.area_class_name secondClassName,
                class3.area_class_name className,
            </if>
            <if test="reqVO.productType == 'global'">
                'global' AS productType,
                item.supplier_item_id AS itemId,
                class1.name firstClassName,
                class2.name secondClassName,
                class3.name className,
            </if>
            spu.spu_id,
            sku.sku_id,
            spu.supplier_id,
            spu.spu_no,
            spu.spu_name,
            brand.brand_name,
            catgory.catgory_name,
            catgory2.catgory_name secondCatgoryName,
            catgory1.catgory_name firstCatgoryName,
            ifnull( item.shelf_status, 0 ) shelfStatus,
            sku.barcode,
            sku.unit,
            sku.properties,
            sku.mark_price,
            sku.cost_price,
            sku.stock,
            sku.suggest_price,
            sku.reference_price,
            sku.reference_sale_price,
            spu.expiration_date,
            item.shelf_date,
            item.min_shelf_status,
            item.mid_shelf_status,
            item.large_shelf_status,
            spu.thumb,
            spu.spec_name,
            spu.min_unit,
            spu.mid_unit,
            spu.mid_size,
            spu.large_unit,
            spu.large_size,
            sku.mid_barcode,
            sku.mid_mark_price,
            sku.mid_cost_price,
            sku.mid_suggest_price,
            sku.mid_min_oq,
            sku.mid_jump_oq,
            sku.mid_max_oq,
            sku.large_barcode,
            sku.large_mark_price,
            sku.large_cost_price,
            sku.large_suggest_price,
            sku.large_min_oq,
            sku.large_jump_oq,
            sku.large_max_oq
        FROM
            <if test="reqVO.productType == 'local'">
                prdt_area_item item
                LEFT JOIN prdt_area_class class3 ON item.area_class_id = class3.area_class_id
                LEFT JOIN prdt_area_class class2 ON class3.pid = class2.area_class_id
                LEFT JOIN prdt_area_class class1 ON class2.pid = class1.area_class_id
            </if>
            <if test="reqVO.productType == 'global'">
                prdt_supplier_item item
                LEFT JOIN prdt_sale_class class3 ON class3.sale_class_id = item.sale_class_id
                LEFT JOIN prdt_sale_class class2 ON class3.pid = class2.sale_class_id
                LEFT JOIN prdt_sale_class class1 ON class2.pid = class1.sale_class_id
            </if>
            LEFT JOIN prdt_sku sku ON sku.sku_id = item.sku_id
            LEFT JOIN prdt_spu spu ON spu.spu_id = sku.spu_id
            LEFT JOIN prdt_brand brand ON brand.brand_id = spu.brand_id
            LEFT JOIN prdt_catgory catgory ON catgory.catgory_id = spu.catgory_id
            LEFT JOIN prdt_catgory catgory2 ON catgory2.catgory_id = catgory.pid
            LEFT JOIN prdt_catgory catgory1 ON catgory1.catgory_id = catgory2.pid
        WHERE
            sku.is_delete = 0
            AND sku.status = 1
            AND (
                item.shelf_status = 1
                <!-- 如果指定了上架ID查询, 使状态查询失效 -->
                <if test="reqVO.itemIdList != null and reqVO.itemIdList.size > 0">
                   OR 1 = 1
                </if>
            )
            <if test="reqVO.saleClassId !=null">
                <if test="reqVO.productType == 'local'">
                    and item.area_class_id = #{reqVO.saleClassId}
                </if>
                <if test="reqVO.productType == 'global'">
                    and item.sale_class_id = #{reqVO.saleClassId}
                </if>
            </if>
            <if test="reqVO.areaId !=null">
                and item.area_id = #{reqVO.areaId}
            </if>
            <if test="reqVO.brandId !=null">
                and brand.brand_id = #{reqVO.brandId}
            </if>
            <if test="reqVO.skuId !=null">
                and sku.sku_id = #{reqVO.skuId}
            </if>
            <if test="reqVO.spuNo !=null">
                and spu.spu_no = #{reqVO.spuNo}
            </if>
            <if test="reqVO.supplierId !=null">
                and item.supplier_id = #{reqVO.supplierId}
            </if>
            <if test="reqVO.catgoryId !=null">
                and spu.catgory_id = #{reqVO.catgoryId}
            </if>
            <if test="null != reqVO.shelfStatus" >
                AND item.shelf_status = #{reqVO.shelfStatus}
            </if>
            <if test="null != reqVO.itemType" >
                AND item.item_type = #{reqVO.itemType}
            </if>
            <if test="reqVO.keyword !=null and reqVO.keyword !='' ">
                and (spu.spu_no like concat('%', #{reqVO.keyword}, '%')
                or spu.spu_name like concat('%', #{reqVO.keyword}, '%')
                or sku.barcode like concat('%', #{reqVO.keyword}, '%') )
            </if>
            <if test="reqVO.areaIdList != null and reqVO.areaIdList.size > 0">
                and item.area_id IN
                <foreach collection="reqVO.areaIdList" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
            <if test="reqVO.skuIdList != null and reqVO.skuIdList.size > 0">
                and item.sku_id IN
                <foreach collection="reqVO.skuIdList" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
            <if test="reqVO.spuIdList != null and reqVO.spuIdList.size > 0">
                and spu.spu_id IN
                <foreach collection="reqVO.spuIdList" item="spuId" open="(" separator="," close=")">
                    #{spuId}
                </foreach>
            </if>
            <if test="reqVO.itemIdList != null and reqVO.itemIdList.size > 0">
                <if test="reqVO.productType == 'local'">
                    and item.area_item_id IN
                    <foreach collection="reqVO.itemIdList" item="itemId" open="(" separator="," close=")">
                        #{itemId}
                    </foreach>
                </if>
                <if test="reqVO.productType == 'global'">
                    and item.supplier_item_id IN
                    <foreach collection="reqVO.itemIdList" item="itemId" open="(" separator="," close=")">
                        #{itemId}
                    </foreach>
                </if>
            </if>
            <if test="reqVO.categoryIdList != null and reqVO.categoryIdList.size > 0">
                and spu.catgory_id IN
                <foreach collection="reqVO.categoryIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.blackCategoryIdList != null and reqVO.blackCategoryIdList.size > 0">
                and spu.catgory_id NOT IN
                <foreach collection="reqVO.blackCategoryIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.brandIdList != null and reqVO.brandIdList.size > 0">
                and spu.brand_id IN
                <foreach collection="reqVO.brandIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.blackBrandIdList != null and reqVO.blackBrandIdList.size > 0">
                and spu.brand_id NOT IN
                <foreach collection="reqVO.blackBrandIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.blackSkuIdList != null and reqVO.blackSkuIdList.size > 0">
                and item.sku_id NOT IN
                <foreach collection="reqVO.blackSkuIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.supplierList != null and reqVO.supplierList.size > 0">
                and spu.supplier_id IN
                <foreach collection="reqVO.supplierList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            ${reqVO.params.dataScope}
    </select>
    <select id="selectType1" resultType="com.zksr.product.controller.spu.vo.PrdtReleaseProductRespVO">
        SELECT
            <if test="reqVO.productType == 'local'">
                'local' AS productType,
                item.area_item_id AS itemId,
                item.area_id,
                class1.area_class_name firstClassName,
                class2.area_class_name secondClassName,
                class3.area_class_name className,
            </if>
            <if test="reqVO.productType == 'global'">
                'global' AS productType,
                item.supplier_item_id AS itemId,
                class1.name firstClassName,
                class2.name secondClassName,
                class3.name className,
            </if>
            item.activity_id,
            item.spu_combine_id,
            sce.spu_combine_no AS spuNo,
            sce.spu_combine_name AS spuName,
            sce.unit,
            sce.mark_price,
            sce.suggest_price,
            sce.supplier_id,
            sce.thumb,
            catgory.catgory_name,
            catgory2.catgory_name secondCatgoryName,
            catgory1.catgory_name firstCatgoryName,
            ifnull( item.shelf_status, 0 ) shelfStatus,
            item.shelf_date,
            item.min_shelf_status,
            item.mid_shelf_status,
            item.large_shelf_status,
            item.activity_start_time,
            item.activity_end_time
        FROM
            <if test="reqVO.productType == 'local'">
                prdt_area_item item
                LEFT JOIN prdt_area_class class3 ON item.area_class_id = class3.area_class_id
                LEFT JOIN prdt_area_class class2 ON class3.pid = class2.area_class_id
                LEFT JOIN prdt_area_class class1 ON class2.pid = class1.area_class_id
            </if>
            <if test="reqVO.productType == 'global'">
                prdt_supplier_item item
                LEFT JOIN prdt_sale_class class3 ON class3.sale_class_id = item.sale_class_id
                LEFT JOIN prdt_sale_class class2 ON class3.pid = class2.sale_class_id
                LEFT JOIN prdt_sale_class class1 ON class2.pid = class1.sale_class_id
            </if>
            LEFT JOIN prdt_spu_combine sce ON sce.spu_combine_id = item.spu_combine_id
            LEFT JOIN prdt_catgory catgory ON catgory.catgory_id = sce.category_id
            LEFT JOIN prdt_catgory catgory2 ON catgory2.catgory_id = catgory.pid
            LEFT JOIN prdt_catgory catgory1 ON catgory1.catgory_id = catgory2.pid
        WHERE
            item.activity_end_time &gt; NOW()
            AND (
                item.shelf_status = 1
                <!-- 如果指定了上架ID查询, 使状态查询失效 -->
                <if test="reqVO.itemIdList != null and reqVO.itemIdList.size > 0">
                    OR 1 = 1
                </if>
            )
            <if test="reqVO.saleClassId !=null">
                <if test="reqVO.productType == 'local'">
                    and item.area_class_id = #{reqVO.saleClassId}
                </if>
                <if test="reqVO.productType == 'global'">
                    and item.sale_class_id = #{reqVO.saleClassId}
                </if>
            </if>
            <if test="reqVO.areaId !=null">
                and item.area_id = #{reqVO.areaId}
            </if>
            <if test="reqVO.supplierId !=null">
                and item.supplier_id = #{reqVO.supplierId}
            </if>
            <if test="reqVO.catgoryId !=null">
                and sce.catgory_id = #{reqVO.catgoryId}
            </if>
            <if test="null != reqVO.shelfStatus" >
                AND item.shelf_status = #{reqVO.shelfStatus}
            </if>
            <if test="null != reqVO.itemType" >
                AND item.item_type = #{reqVO.itemType}
            </if>
            <if test="reqVO.keyword !=null and reqVO.keyword !='' ">
                and (sce.spu_combine_no like concat('%', #{reqVO.keyword}, '%')
                or sce.spu_combine_name like concat('%', #{reqVO.keyword}, '%')
                )
            </if>
            <if test="reqVO.areaIdList != null and reqVO.areaIdList.size > 0">
                and item.area_id IN
                <foreach collection="reqVO.areaIdList" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
            <if test="reqVO.itemIdList != null and reqVO.itemIdList.size > 0">
                <if test="reqVO.productType == 'local'">
                    and item.area_item_id IN
                    <foreach collection="reqVO.itemIdList" item="itemId" open="(" separator="," close=")">
                        #{itemId}
                    </foreach>
                </if>
                <if test="reqVO.productType == 'global'">
                    and item.supplier_item_id IN
                    <foreach collection="reqVO.itemIdList" item="itemId" open="(" separator="," close=")">
                        #{itemId}
                    </foreach>
                </if>
            </if>
            <if test="reqVO.categoryIdList != null and reqVO.categoryIdList.size > 0">
                and sce.category_id IN
                <foreach collection="reqVO.categoryIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.blackCategoryIdList != null and reqVO.blackCategoryIdList.size > 0">
                and sce.category_id NOT IN
                <foreach collection="reqVO.blackCategoryIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.supplierList != null and reqVO.supplierList.size > 0">
                and sce.supplier_id IN
                <foreach collection="reqVO.supplierList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            ${reqVO.params.dataScope}
        ORDER BY
            sce.spu_combine_id DESC
    </select>
</mapper>