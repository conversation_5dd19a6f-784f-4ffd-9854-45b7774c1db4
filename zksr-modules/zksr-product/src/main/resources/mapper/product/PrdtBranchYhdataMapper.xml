<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtBranchYhdataMapper">
    <resultMap type="com.zksr.product.domain.po.YhMatchAreaItemPO" id="YhMatchAreaItemResult">
        <result property="areaItemId"       column="area_item_id" />
        <result property="skuId"            column="area_item_id" />
        <result property="minShelfStatus"   column="min_shelf_status" />
        <result property="midShelfStatus"   column="mid_shelf_status" />
        <result property="largeShelfStatus" column="large_shelf_status" />
        <result property="supplierId"       column="supplier_id" />
        <result property="threeSaleClassId" column="area_class_id" />
        <result property="spuId"            column="spu_id" />
        <result property="spuName"          column="spu_name" />
    </resultMap>

    <sql id="selectYhMatchAreaItemPO">
        SELECT
            item.area_item_id,
            item.sku_id,
            item.min_shelf_status,
            item.mid_shelf_status,
            item.large_shelf_status,
            spu.supplier_id,
            c1.area_class_id AS threeSaleClassId,
            sku.spu_id,
            spu.spu_name,
            sku.barcode,
            sku.mid_barcode,
            sku.large_barcode
        FROM
            prdt_sku sku
            INNER JOIN prdt_spu spu on sku.spu_id = spu.spu_id
            INNER JOIN prdt_area_item item on item.sku_id = sku.sku_id
            INNER JOIN prdt_area_class c3 ON c3.area_class_id = item.area_class_id
            INNER JOIN prdt_area_class c2 ON c2.area_class_id = c3.pid
            INNER JOIN prdt_area_class c1 ON c1.area_class_id = c2.pid
    </sql>


    <select id="matchSkuSourceNo" resultType="com.zksr.product.domain.po.YhMatchAreaItemPO">
            <include refid="selectYhMatchAreaItemPO"/>
        WHERE
            sku.is_delete = 0
            AND spu.is_delete = 0
            AND spu.`status` = 1
            AND item.area_id = #{areaId}
            AND sku.source_no = #{sourceNo}
    </select>
    <select id="matchSkuBarcode" resultType="com.zksr.product.domain.po.YhMatchAreaItemPO">
           <include refid="selectYhMatchAreaItemPO"/>
        WHERE
          sku.is_delete = 0
          AND spu.is_delete = 0
          AND spu.`status` = 1
          AND item.area_id = #{areaId}
          AND (sku.barcode = #{barcode} OR sku.mid_barcode = #{barcode} OR sku.large_barcode = #{barcode})
    </select>
    <select id="matchItemId" resultType="com.zksr.product.domain.po.YhMatchAreaItemPO">
            <include refid="selectYhMatchAreaItemPO"/>
        WHERE
            item.area_item_id IN
            <foreach collection="areaItemIdList" item="itemId" open="(" close=")" separator=",">
                #{itemId}
            </foreach>
    </select>
    <select id="selectBatchTotalPageList"
            resultType="com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataGroupRespVO">
        SELECT
            pby.pos_yh_batch_no,
            MIN(pby.create_time) AS createTime,
            MAX(pby.batch_ymd) AS batchYmd,
            pby.branch_id,
            COUNT(DISTINCT pby.pos_barcode) yhSkuNum,
            SUM(CASE WHEN pby.match_state = 1 THEN 1 ELSE 0 END) matchSkuNum
        FROM
            prdt_branch_yhdata pby
        WHERE 1=1
            <if test="startTime != null">
                AND pby.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="branchId != null">
                AND pby.branch_id = #{branchId}
            </if>
        GROUP BY
            pby.pos_yh_batch_no,
            pby.branch_id
        ORDER BY
            pby.pos_yh_batch_no DESC
    </select>
    <select id="selectListExt" resultType="com.zksr.product.controller.yhdata.vo.PrdtBranchYhdataRespVO">
        SELECT
            pby.yh_id,
            pby.sys_code,
            pby.create_by,
            pby.create_time,
            pby.update_by,
            pby.update_time,
            pby.pos_yh_batch_no,
            pby.area_id,
            pby.branch_id,
            pby.pos_sku_name,
            pby.pos_source_no,
            pby.pos_barcode,
            pby.pos_sales_qty,
            pby.pos_stock_qty,
            pby.pos_branch_stock_qty,
            pby.pos_suggest_qty,
            pby.source_yh_qty,
            pby.pos_unit_name,
            pby.pos_max_late_time,
            pby.pos30day_avg_sales,
            pby.pos_safety_days,
            pby.pos_safety_stock,
            pby.mall_area_item_id,
            pby.mall_match_sku_id,
            pby.mall_unit_type,
            pby.last_time,
            pby.last_submit_qty,
            pby.transit_qty,
            pby.checked,
            pby.match_state,
            pby.fail_reason,
            pby.line_num,
            pby.batch_ymd,
            pby.del_flag,
            spu.spu_name spuName,
            spu.supplier_id,
            sku.properties
        FROM
            prdt_branch_yhdata pby
            LEFT JOIN prdt_sku sku ON pby.mall_match_sku_id = sku.sku_id
            LEFT JOIN prdt_spu spu ON spu.spu_id = sku.spu_id
        WHERE 1=1
            <if test='posYhBatchNo != null and posYhBatchNo != ""'>
                AND pby.pos_yh_batch_no = #{posYhBatchNo}
            </if>
            <if test='posBarcode != null and posBarcode != ""'>
                AND pby.pos_barcode = #{posBarcode}
            </if>
            <if test='posSourceNo != null and posSourceNo != ""'>
                AND pby.pos_source_no = #{posSourceNo}
            </if>
            <if test='posSkuName != null and posSkuName != ""'>
                AND pby.pos_sku_name LIKE CONCAT('%', #{posSkuName}, '%')
            </if>
            <if test='spuName != null and spuName != ""'>
                AND spu.spu_name LIKE CONCAT('%', #{spuName}, '%')
            </if>
            <if test="branchId != null">
                AND pby.branch_id = #{branchId}
            </if>
            <if test="matchState != null">
                AND pby.match_state = #{matchState}
            </if>
            <if test="startTime != null">
                AND pby.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        ORDER BY
            pby.yh_id DESC
    </select>
</mapper>
