<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtBlockBranchMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectBlockSchemesByBranchId" resultType="com.zksr.product.domain.PrdtBlockScheme">
        SELECT
            bs.scheme_no,
            bs.scheme_name,
            bs.area_id,
            bs.supplier_id,
            bs.status,
            bs.memo,
            bs.create_time
        FROM prdt_block_branch bb
        LEFT JOIN prdt_block_scheme bs ON bb.scheme_no = bs.scheme_no
        <where>
                bb.branch_id = #{branchId}
            AND bs.del_flag = 0
        </where>
    </select>
</mapper>