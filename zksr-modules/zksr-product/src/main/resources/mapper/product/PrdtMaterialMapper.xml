<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtMaterialMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectByApplyType" resultType="com.zksr.product.api.material.vo.MaterialInfoVO">
        SELECT
            pma.material_id,
            pm.img,
            pma.start_time,
            pma.end_time
        FROM
            prdt_material_apply pma
            INNER JOIN prdt_material pm ON pm.material_id = pma.material_id
        WHERE
            pma.apply_type = #{applyType}
            AND pma.apply_id = #{applyId}
            AND pma.end_time &gt; NOW()
            AND pm.status = 1
        ORDER BY
            pma.apply_id DESC
    </select>
</mapper>