<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtSpuCombineMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectPageSpuCombine" resultType="com.zksr.product.controller.combine.vo.PrdtSpuCombinePageRespVO">
        SELECT
            combine.spu_combine_id AS spuCombineId,
            combine.create_by AS createBy,
            combine.create_time AS createTime,
            combine.supplier_id AS supplierId,
            combine.area_id AS areaId,
            combine.spu_combine_no AS spuCombineNo,
            combine.spu_combine_name AS spuCombineName,
            combine.status AS status,
            combine.spec_name AS specName,
            combine.total_limit AS totalLimit,
            combine.min_oq AS minOq,
            combine.jump_oq AS jumpOq,
            combine.max_oq AS maxOq,
            combine.mark_price AS markPrice,
            combine.suggest_price AS suggestPrice,
            combine.sale_price1 AS salePrice1,
            combine.sale_price2 AS salePrice2,
            combine.sale_price3 AS salePrice3,
            combine.sale_price4 AS salePrice4,
            combine.sale_price5 AS salePrice5,
            combine.sale_price6 AS salePrice6,
            combine.unit AS unit,
            combine.thumb As thumb,
            combine.thumb_video AS thumbVideo,
            combine.images AS images,
            combine.details AS details,
            combine.memo AS memo,
            combine.category_id AS categoryId,
            IFNULL(pai.area_item_id, psi.supplier_item_id) AS itemId,
            COALESCE(pai.activity_id, psi.activity_id) AS activityId,
            COALESCE(pai.activity_start_time, psi.activity_start_time) AS activityStartTime,
            COALESCE(pai.activity_end_time, psi.activity_end_time) AS activityEndTime
        FROM
            prdt_spu_combine combine
            LEFT JOIN prdt_area_item pai ON combine.spu_combine_id = pai.spu_combine_id
            LEFT JOIN prdt_supplier_item psi ON combine.spu_combine_id = psi.spu_combine_id
        WHERE 1=1
            <if test="reqVO.spuCombineName != null and reqVO.spuCombineName !=''">
                AND combine.spu_combine_name LIKE CONCAT('%',#{reqVO.spuCombineName},'%')
            </if>
            <if test="reqVO.createBy != null and reqVO.createBy !=''">
                AND combine.create_by LIKE CONCAT('%',#{reqVO.createBy},'%')
            </if>
            <if test="reqVO.createStartTime != null and reqVO.createStartTime !='' and reqVO.createEndTime != null and reqVO.createEndTime !=''">
                AND combine.create_time BETWEEN #{reqVO.createStartTime} AND #{reqVO.createEndTime}
            </if>
            <if test="reqVO.activityStartTime != null and reqVO.activityStartTime !='' and reqVO.activityEndTime != null and reqVO.activityEndTime !=''">
                AND (
                (pai.activity_start_time &gt;= #{reqVO.activityStartTime}
                AND pai.activity_end_time &lt;= #{reqVO.activityEndTime})
                OR
                (psi.activity_start_time &gt;= #{reqVO.activityStartTime}
                AND psi.activity_end_time &lt;= #{reqVO.activityEndTime})
                )
            </if>
            <if test="reqVO.activityIds != null and reqVO.activityIds.size() > 0">
                AND ( (pai.spu_combine_id IS NOT NULL AND
                pai.activity_id IN
                <foreach collection="reqVO.activityIds" item="activityId" separator="," open="(" close=")">
                    #{activityId}
                </foreach>)
                or( psi.spu_combine_id IS NOT NULL AND
                psi.activity_id IN
                <foreach collection="reqVO.activityIds" item="activityId" separator="," open="(" close=")">
                    #{activityId}
                </foreach>)
                )
            </if>
            <if test="reqVO.areaId != null">
                AND pai.area_id = #{reqVO.areaId}
            </if>
            ${reqVO.params.dataScope}
        order by
            combine.spu_combine_id DESC
    </select>
    <select id="selectSpuCombineDTO" resultType="com.zksr.product.api.combine.SpuCombineDTO">
        SELECT * FROM prdt_spu_combine WHERE spu_combine_id = #{spuCombineId}
    </select>
    <select id="selectSpuCombineDtlList" resultType="com.zksr.product.api.combine.SpuCombineDtlDTO">
        SELECT * FROM prdt_spu_combine_dtl WHERE spu_combine_id = #{spuCombineId}
    </select>
</mapper>