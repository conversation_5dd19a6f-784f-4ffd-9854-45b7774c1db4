<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtSaleClassMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectParentDirectoryList" resultType="com.zksr.product.domain.PrdtSaleClass">
        SELECT
            fpsc.sale_class_id,
            fpsc.NAME,
            fpsc.pid
        FROM
            prdt_sale_class fpsc
        WHERE
            fpsc.pid IS NULL or fpsc.pid = 0
        UNION
        SELECT
            spsc.sale_class_id,
            spsc.NAME,
            spsc.pid
        FROM
            prdt_sale_class fpsc
                LEFT JOIN prdt_sale_class spsc ON fpsc.sale_class_id = spsc.pid
        WHERE
            fpsc.pid IS NULL or fpsc.pid = 0
    </select>

    <insert id="copyToSaleClass">
        INSERT INTO prdt_sale_class (
            sale_class_id,
            sys_code,
            name,
            pid,
            icon,
            memo,
            sort,
            status,
            group_id,
            create_by,
            create_time,
            update_by,
            update_time,
            level
        ) SELECT NULL,
                 sys_code,
                 catgory_name,
                 pid,
                 icon,
                 memo,
                 sort,
                 status,
                 #{groupId},
                 create_by,
                 create_time,
                 update_by,
                 update_time,
                 level
        FROM prdt_catgory
        WHERE
            STATUS = 1
          AND catgory_name NOT IN ( SELECT NAME FROM prdt_sale_class );
    </insert>

    <!-- 根据城市管理分组ID获取该分组不存在的平台展示分类 -->
    <select id="getSaleClassNotExistByGroupId"
            resultType="com.zksr.product.controller.catgory.dto.PrdtCatgoryCopyRespDTO">
        SELECT
            catgory.catgory_id,
            catgory.sys_code,
            catgory.catgory_name,
            catgory.icon,
            catgory.sort,
            catgory.status,
            catgory.level,
            catgory.pid,
            pcatgory.level AS plevel,
            pcatgory.catgory_name AS pcatgoryName,
            pcatgory2.catgory_name AS pcatgoryName2
        FROM
            prdt_catgory catgory
            LEFT JOIN prdt_catgory pcatgory ON pcatgory.catgory_id = catgory.pid
            LEFT JOIN prdt_catgory pcatgory2 ON pcatgory2.catgory_id = pcatgory.pid
        WHERE
            catgory.STATUS = 1
            AND catgory.level in (1, 2, 3)
            <!-- AND catgory.catgory_name NOT IN (
                SELECT
                    saleClass.name
                FROM
                    prdt_sale_class saleClass
                    LEFT JOIN prdt_group_sale_class groupSaleClass ON saleClass.sale_class_id = groupSaleClass.sale_class_id
                WHERE
                    groupSaleClass.group_id = #{groupId}); -->
    </select>
    <select id="checkTertiaryCategoryUniquenessInSecondaryCategory"
            resultType="com.zksr.product.domain.PrdtSaleClass">
        SELECT
            *
        FROM
            prdt_sale_class
        WHERE
            del_flag = '0'
            AND status = '1'
            <if test="tertiaryCategoryName != null and tertiaryCategoryName != ''">
                AND  name = #{tertiaryCategoryName}
            </if>
            <if test="secondaryCategoryId != null and secondaryCategoryId != ''">
                AND pid = #{secondaryCategoryId}
            </if>
        LIMIT 1
    </select>
</mapper>
