<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.ProductDataMapper">

    <select id="selectByLocal" resultType="com.zksr.common.elasticsearch.domain.EsLocalProduct">
        SELECT
            pai.area_item_id  AS id,  -- 组合SKU主键
            pai.area_item_id AS itemId,                   -- 上架商品ID
            spu.spu_name,   --  SKU名称
            spu.min_unit AS unit,       --  单位
            spu.mid_unit AS midUnit,       --  单位
            spu.large_unit AS largeUnit,       --  单位
            spu.latest_date AS latestDate,      --  最新生产日期
            spu.oldest_date AS oldestDate,      --  最后生产日期
            sku.properties, --  规格属性
            sku.barcode,        --  小单位商品条码
            sku.mid_barcode,    --  中单位商品条码
            sku.large_barcode,  --  小单位商品条码
            spu.thumb,      --  封面图片
            spu.thumb_video,    --  封面规格
            spu.spu_id,     --  SPU_ID
            sku.sku_id,     --  SKU_ID
            spu.catgory_id, --  管理分类ID
            spu.sys_code,   --  平台商
            class3.area_class_name AS className,  --  城市三级展示分类
            class3.area_class_id AS class3Id,  --  城市展示类目ID
            class2.area_class_id AS class2Id,  --  城市二级展示分类
            class1.area_class_id AS class1Id,  --  城市一级展示分类
            spu.supplier_id,    --  入驻商ID
            spu.brand_id,       --  品牌ID
            brand.brand_name,   --  品牌名称
            sku.mark_price,             --  小单位标准价
            sku.mid_mark_price,         --  中单位标准价
            sku.large_mark_price,       --  中单位标准价
            sku.suggest_price,          --  大单位建议售价, 原价
            sku.mid_suggest_price,      --  中单位建议售价, 原价
            sku.large_suggest_price,    --  大单位建议售价, 原价
            (CASE WHEN sku.is_delete = 1 THEN 0 ELSE pai.shelf_status END) shelfStatus,   --  上架状态
            pai.min_shelf_status,       --  小单位上架状态
            pai.mid_shelf_status,       --  中单位上架状态
            pai.large_shelf_status,     --  大单位上架状态
            pai.area_id,         --  城市ID
            -1 groupId,          --  默认城市分组
            spu.spec_name,       -- 商品规格
            spu.origin_place,    -- 产地
            spu.is_specs,        -- 是否是多规格商品
            pai.sort_num,        -- 上架商品排序序号
            sku.min_oq,          -- 起订
            sku.jump_oq,         -- 订货组数
            sku.max_oq,          -- 限购
            sku.mid_min_oq,          -- 中单位起订
            sku.mid_jump_oq,         -- 中单位订货组数
            sku.mid_max_oq,          -- 中单位限购
            sku.large_min_oq,          -- 大单位起订
            sku.large_jump_oq,         -- 大单位订货组数
            sku.large_max_oq,          -- 大单位限购
            sku.stock,           -- 库存
            sku.sale_qty,         -- 销量
            spu.mid_size,       -- 中单位库存比例
            spu.large_size,     -- 大单位库存比例
            IFNULL(pai.item_type, 0) itemType,   --  0-普通商品, 1-组合商品
            IFNULL(spu.keywords, '') AS `keywords`, -- 防止null未充值
            sku.min_no_stock_time,  --  小单位无库存时间
            sku.mid_no_stock_time,  --  中单位无库存时间
            sku.large_no_stock_time, --  大单位无库存时间
            sku.sale_type,          -- 销售类型(0-零售、1-批发)
            sku.retail_price,       -- 零售价
            sku.mid_retail_price,   -- 中单位零售价
            sku.large_retail_price, -- 大单位零售价
            spu.enable_retail      -- 是否零售(0否1是)
        FROM
            prdt_area_item pai
            INNER JOIN prdt_spu spu ON pai.spu_id = spu.spu_id
            INNER JOIN prdt_sku sku ON pai.sku_id = sku.sku_id
            LEFT  JOIN prdt_brand brand ON spu.brand_id = brand.brand_id
            LEFT  JOIN prdt_area_class class3 ON class3.area_class_id = pai.area_class_id
            LEFT  JOIN prdt_area_class class2 ON class2.area_class_id = class3.pid
            LEFT  JOIN prdt_area_class class1 ON class1.area_class_id = class2.pid
        <where>
            <if test="req.itemIds != null and req.itemIds.size > 0">
                AND pai.area_item_id IN
                <foreach collection="req.itemIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.skuIds != null and req.skuIds.size > 0">
                AND pai.sku_id IN
                <foreach collection="req.skuIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.spuIds != null and req.spuIds.size > 0">
                AND pai.spu_id IN
                <foreach collection="req.spuIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.minId != null">
                AND pai.area_item_id &gt; #{req.minId}
            </if>
        </where>
        ORDER BY
            pai.area_item_id ASC
        <if test="req.minId != null">
            LIMIT 500
        </if>
    </select>
    <select id="selectByGlobal" resultType="com.zksr.common.elasticsearch.domain.EsGlobalProduct">
        SELECT
            pai.supplier_item_id  AS id,  -- 组合SKU主键
            pai.supplier_item_id AS itemId,                   -- 上架商品ID
            spu.spu_name,   --  SKU名称
            spu.min_unit AS unit,       --  单位
            spu.mid_unit AS midUnit,       --  单位
            spu.large_unit AS largeUnit,       --  单位
            spu.latest_date AS latestDate,      --  最新生产日期
            spu.oldest_date AS oldestDate,      --  最后生产日期
            sku.properties, --  规格属性
            sku.barcode,        --  小单位商品条码
            sku.mid_barcode,    --  中单位商品条码
            sku.large_barcode,  --  小单位商品条码
            spu.thumb,      --  封面图片
            spu.thumb_video,    --  封面规格
            spu.spu_id,     --  SPU_ID
            sku.sku_id,     --  SKU_ID
            spu.catgory_id, --  管理分类ID
            spu.sys_code,   --  平台商
            class3.name AS className,  --  城市三级展示分类
            class3.sale_class_id AS class3Id,  --  城市展示类目ID
            class2.sale_class_id AS class2Id,  --  城市二级展示分类
            class1.sale_class_id AS class1Id,  --  城市一级展示分类
            spu.supplier_id,    --  入驻商ID
            spu.brand_id,       --  品牌ID
            brand.brand_name,   --  品牌名称
            sku.mark_price,             --  小单位标准价
            sku.mid_mark_price,         --  中单位标准价
            sku.large_mark_price,       --  中单位标准价
            sku.suggest_price,          --  大单位建议售价, 原价
            sku.mid_suggest_price,      --  中单位建议售价, 原价
            sku.large_suggest_price,    --  大单位建议售价, 原价
            (CASE WHEN sku.is_delete = 1 THEN 0 ELSE pai.shelf_status END) shelfStatus,   --  上架状态
            pai.min_shelf_status,       --  小单位上架状态
            pai.mid_shelf_status,       --  中单位上架状态
            pai.large_shelf_status,     --  大单位上架状态
            -1 areaId,         --  城市ID
            IFNULL(class3.group_id, -1) groupId,          --  默认城市分组
            spu.spec_name,       -- 商品规格
            spu.origin_place,    -- 产地
            spu.is_specs,        -- 是否是多规格商品
            pai.sort_num,        -- 上架商品排序序号
            sku.min_oq,          -- 起订
            sku.jump_oq,          -- 订货组数
            sku.max_oq,          -- 限购
            sku.mid_min_oq,          -- 中单位起订
            sku.mid_jump_oq,         -- 中单位订货组数
            sku.mid_max_oq,          -- 中单位限购
            sku.large_min_oq,          -- 大单位起订
            sku.large_jump_oq,         -- 大单位订货组数
            sku.large_max_oq,          -- 大单位限购
            sku.stock,           -- 库存
            sku.sale_qty,         -- 销量
            spu.mid_size,       -- 中单位库存比例
            spu.large_size,     -- 大单位库存比例
            IFNULL(pai.item_type, 0) itemType,   --  0-普通商品, 1-组合商品
            IFNULL(spu.keywords, '') AS `keywords`, -- 防止null未充值
            sku.min_no_stock_time,  --  小单位无库存时间
            sku.mid_no_stock_time,  --  中单位无库存时间
            sku.large_no_stock_time, --  大单位无库存时间
            sku.sale_type,          -- 销售类型(0-零售、1-批发)
            sku.retail_price,       -- 零售价
            sku.mid_retail_price,   -- 中单位零售价
            sku.large_retail_price, -- 大单位零售价
            spu.enable_retail      -- 是否零售(0否1是)
        FROM
            prdt_supplier_item pai
            INNER JOIN prdt_spu spu ON pai.spu_id = spu.spu_id
            INNER JOIN prdt_sku sku ON pai.sku_id = sku.sku_id
            LEFT  JOIN prdt_brand brand ON spu.brand_id = brand.brand_id
            LEFT  JOIN prdt_sale_class class3 ON class3.sale_class_id = pai.sale_class_id
            LEFT  JOIN prdt_sale_class class2 ON class2.sale_class_id = class3.pid
            LEFT  JOIN prdt_sale_class class1 ON class1.sale_class_id = class2.pid
        <where>
            <if test="req.itemIds != null and req.itemIds.size > 0">
                AND pai.supplier_item_id IN
                <foreach collection="req.itemIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.skuIds != null and req.skuIds.size > 0">
                AND pai.sku_id IN
                <foreach collection="req.skuIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.spuIds != null and req.spuIds.size > 0">
                AND pai.spu_id IN
                <foreach collection="req.spuIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.minId != null">
                AND pai.supplier_item_id &gt; #{req.minId}
            </if>
        </where>
        ORDER BY
            pai.supplier_item_id ASC
            <if test="req.minId != null">
                LIMIT 500
            </if>
    </select>
    <select id="selectCbProductByLocal" resultType="com.zksr.common.elasticsearch.domain.EsProductGroup">
        SELECT
            'local' type,
            pai.area_id,
            pai.activity_id,
            pai.spu_combine_id,
            pai.area_item_id  AS id,  -- 组合SKU主键
            pai.area_item_id AS itemId,                   -- 上架商品ID
            psc.spu_combine_name AS spu_name,   --  SKU名称
            psc.unit AS unit,       --  单位
            psc.spu_combine_no barcode,    --  商品条码
            psc.thumb,      --  封面图片
            psc.thumb_video,    --  封面规格
            pai.sys_code,   --  平台商
            class3.area_class_name AS className,  --  城市三级展示分类
            class3.area_class_id AS class3Id,  --  城市展示类目ID
            class2.area_class_id AS class2Id,  --  城市二级展示分类
            class1.area_class_id AS class1Id,  --  城市一级展示分类
            psc.category_id AS catgoryId,    --  管理分类ID
            psc.supplier_id,    --  入驻商ID
            psc.mark_price,             --  小单位标准价
            psc.suggest_price,          --  大单位建议售价, 原价
            (CASE WHEN psc.status = 0 THEN 0 ELSE pai.shelf_status END) shelfStatus,   --  上架状态
            pai.min_shelf_status,       --  小单位上架状态
            psc.spec_name,       -- 商品规格
            pai.sort_num,        -- 上架商品排序序号
            psc.min_oq,          -- 起订
            psc.jump_oq,          -- 订货组数
            psc.max_oq,          -- 限购
            0 stock,           -- 库存
            0 sale_qty,         -- 销量
            IFNULL(pai.item_type, 0) itemType,   --  0-普通商品, 1-组合商品
            (UNIX_TIMESTAMP(pai.activity_start_time) * 1000) AS activityStartTime,  --  活动开始时间, 毫秒值
            (UNIX_TIMESTAMP(pai.activity_end_time) * 1000) AS activityEndTime        --  活动结束时间, 毫秒值
        FROM
            prdt_area_item pai
            INNER JOIN prdt_spu_combine psc ON psc.spu_combine_id = pai.spu_combine_id
            LEFT  JOIN prdt_area_class class3 ON class3.area_class_id = pai.area_class_id
            LEFT  JOIN prdt_area_class class2 ON class2.area_class_id = class3.pid
            LEFT  JOIN prdt_area_class class1 ON class1.area_class_id = class2.pid
        WHERE
            pai.activity_end_time > NOW()
            <if test="req.itemIds != null and req.itemIds.size > 0">
                AND pai.area_item_id IN
                <foreach collection="req.itemIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.spuCombineId != null and req.spuCombineId.size > 0">
                AND psc.spu_combine_id IN
                <foreach collection="req.spuCombineId" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.minId != null">
                AND pai.area_item_id &gt; #{req.minId}
            </if>
        ORDER BY
            pai.area_item_id ASC
            <if test="req.minId != null">
                LIMIT 500
            </if>
    </select>
    <select id="selectCbProductByGlobal" resultType="com.zksr.common.elasticsearch.domain.EsProductGroup">
        SELECT
            'global' type,
            -1 AS areaId,
            pai.activity_id,
            pai.spu_combine_id,
            pai.supplier_item_id  AS id,  -- 组合SKU主键
            pai.supplier_item_id AS itemId,                   -- 上架商品ID
            psc.spu_combine_name AS spu_name,   --  SKU名称
            psc.unit AS unit,       --  单位
            psc.spu_combine_no barcode,    --  商品条码
            psc.thumb,      --  封面图片
            psc.thumb_video,    --  封面规格
            pai.sys_code,   --  平台商
            class3.name AS className,  --  城市三级展示分类
            class3.sale_class_id AS class3Id,  --  城市展示类目ID
            class2.sale_class_id AS class2Id,  --  城市二级展示分类
            class1.sale_class_id AS class1Id,  --  城市一级展示分类
            psc.category_id AS catgoryId,    --  管理分类ID
            psc.supplier_id,    --  入驻商ID
            psc.mark_price,             --  小单位标准价
            psc.suggest_price,          --  大单位建议售价, 原价
            (CASE WHEN psc.status = 0 THEN 0 ELSE pai.shelf_status END) shelfStatus,   --  上架状态
            pai.min_shelf_status,       --  小单位上架状态
            psc.spec_name,       -- 商品规格
            pai.sort_num,        -- 上架商品排序序号
            psc.min_oq,          -- 起订
            psc.jump_oq,          -- 订货组数
            psc.max_oq,          -- 限购
            0 stock,           -- 库存
            0 sale_qty,         -- 销量
            IFNULL(pai.item_type, 0) itemType,   --  0-普通商品, 1-组合商品
            (UNIX_TIMESTAMP(pai.activity_start_time) * 1000) AS activityStartTime,  --  活动开始时间, 毫秒值
            (UNIX_TIMESTAMP(pai.activity_end_time) * 1000) AS activityEndTime        --  活动结束时间, 毫秒值
        FROM
            prdt_supplier_item pai
            INNER JOIN prdt_spu_combine psc ON psc.spu_combine_id = pai.spu_combine_id
            LEFT  JOIN prdt_sale_class class3 ON class3.sale_class_id = pai.sale_class_id
            LEFT  JOIN prdt_sale_class class2 ON class2.sale_class_id = class3.pid
            LEFT  JOIN prdt_sale_class class1 ON class1.sale_class_id = class2.pid
        WHERE
            pai.activity_end_time > NOW()
            <if test="req.itemIds != null and req.itemIds.size > 0">
                AND pai.supplier_item_id IN
                <foreach collection="req.itemIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.spuCombineId != null and req.spuCombineId.size > 0">
                AND psc.spu_combine_id IN
                <foreach collection="req.spuCombineId" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.minId != null">
                AND pai.supplier_item_id &gt; #{req.minId}
            </if>
        ORDER BY
            pai.supplier_item_id ASC
            <if test="req.minId != null">
                LIMIT 500
            </if>
    </select>
</mapper>