<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtAreaItemZipMapper">
    <update id="updatePrdtAreaItemZip">
        UPDATE prdt_area_item_zip
       <set>
			<if test="endDate != null">end_date = #{endDate},</if>
		</set>
        WHERE
            sys_code = #{sysCode}
            AND supplier_id = #{supplierId}
            AND spu_id = #{spuId}
            AND sku_id = #{skuId}
    </update>
</mapper>
