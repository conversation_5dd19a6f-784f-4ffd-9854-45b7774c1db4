<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtAreaItemMapper">

    <select id = "selectPageAreaItemByPage"  resultType="com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO">
        SELECT
           item.area_item_id,
           spu.spu_id,
           sku.sku_id,
           spu.supplier_id,
           spu.spu_no,
           spu.spu_name,
           brand.brand_name,
           catgory.catgory_name,
           catgory2.catgory_name secondCatgoryName,
           catgory1.catgory_name firstCatgoryName,
           sku.STATUS,
           ifnull( item.shelf_status, 0 ) shelfStatus,
           sku.barcode,
           sku.unit,
           sku.properties,
           sku.mark_price,
           sku.cost_price,
           sku.stock,
           sku.suggest_price,
           sku.reference_price,
           sku.reference_sale_price,
           spu.expiration_date,
           item.shelf_date,
           item.sort_num,
           item.area_class_id,
           item.min_shelf_status,
           item.mid_shelf_status,
           item.large_shelf_status,
           spu.thumb,
           spu.spec_name,
           spu.min_unit,
           spu.mid_unit,
           spu.mid_size,
           spu.large_unit,
           spu.large_size,
           sku.mid_barcode,
           sku.mid_mark_price,
           sku.mid_cost_price,
           sku.mid_suggest_price,
           sku.mid_min_oq,
           sku.mid_jump_oq,
           sku.mid_max_oq,
           sku.large_barcode,
           sku.large_mark_price,
           sku.large_cost_price,
           sku.large_suggest_price,
           sku.large_min_oq,
           sku.large_jump_oq,
           sku.large_max_oq,
           pac1.area_class_name firstAreaClassName,
           pac2.area_class_name secondAreaClassName,
           pac3.area_class_name areaClassName,
           item.area_id,
           pma.material_apply_id,      <!-- 素材应用ID -->
           pma.material_id,            <!-- 素材id -->
           pm.img                  <!-- 素材图片地址 -->
        FROM
            prdt_area_item item
            LEFT JOIN prdt_sku sku ON sku.sku_id = item.sku_id
            LEFT JOIN prdt_spu spu ON spu.spu_id = sku.spu_id
            LEFT JOIN prdt_brand brand ON brand.brand_id = spu.brand_id
            LEFT JOIN prdt_catgory catgory ON catgory.catgory_id = spu.catgory_id
            LEFT JOIN prdt_catgory catgory2 ON catgory2.catgory_id = catgory.pid
            LEFT JOIN prdt_catgory catgory1 ON catgory1.catgory_id = catgory2.pid
            LEFT JOIN prdt_area_class pac3 ON item.area_class_id = pac3.area_class_id
            LEFT JOIN prdt_area_class pac2 ON pac3.pid = pac2.area_class_id
            LEFT JOIN prdt_area_class pac1 ON pac2.pid = pac1.area_class_id
            LEFT JOIN prdt_material_apply pma ON pma.apply_id = item.area_item_id
                      and pma.apply_type = 3
                      and pma.start_time &lt;= now()
                      and pma.end_time &gt; now()
            LEFT JOIN prdt_material pm ON pm.material_id = pma.material_id
        WHERE
            sku.is_delete = 0
            <if test="reqVO.areaClassId !=null">
                and item.area_class_id = #{reqVO.areaClassId}
            </if>
            <if test="reqVO.skuStatus !=null">
                and sku.status = #{reqVO.skuStatus}
            </if>
            <if test="reqVO.areaId !=null">
                and item.area_id = #{reqVO.areaId}
            </if>
            <if test="reqVO.brandId !=null">
                and brand.brand_id = #{reqVO.brandId}
            </if>
            <if test="reqVO.skuId !=null">
                and sku.sku_id = #{reqVO.skuId}
            </if>
            <if test="reqVO.spuNo !=null">
                and spu.spu_no = #{reqVO.spuNo}
            </if>
            <if test="reqVO.supplierId !=null">
                and item.supplier_id = #{reqVO.supplierId}
            </if>
            <if test="reqVO.catgoryId !=null">
                and spu.catgory_id = #{reqVO.catgoryId}
            </if>
            <if test="null != reqVO.shelfStatus" >
                AND item.shelf_status = #{reqVO.shelfStatus}
            </if>
            <if test="null != reqVO.itemType" >
                AND item.item_type = #{reqVO.itemType}
            </if>
            <if test="reqVO.keyword !=null and reqVO.keyword !='' ">
                and (spu.spu_no like concat('%', #{reqVO.keyword}, '%')
                or spu.spu_name like concat('%', #{reqVO.keyword}, '%')
                or sku.barcode like concat('%', #{reqVO.keyword}, '%') )
            </if>
            <if test="reqVO.areaIdList != null and reqVO.areaIdList.size > 0">
                and item.area_id IN
                <foreach collection="reqVO.areaIdList" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
            <if test="reqVO.skuIdList != null and reqVO.skuIdList.size > 0">
                and item.sku_id IN
                <foreach collection="reqVO.skuIdList" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
            <if test="reqVO.spuIdList != null and reqVO.spuIdList.size > 0">
                and spu.spu_id IN
                <foreach collection="reqVO.spuIdList" item="spuId" open="(" separator="," close=")">
                    #{spuId}
                </foreach>
            </if>
            <if test="reqVO.areaItemIdList != null and reqVO.areaItemIdList.size > 0">
                and item.area_item_id IN
                <foreach collection="reqVO.areaItemIdList" item="areaItemId" open="(" separator="," close=")">
                    #{areaItemId}
                </foreach>
            </if>
            <if test="reqVO.itemIdList != null and reqVO.itemIdList.size > 0">
                and item.area_item_id IN
                <foreach collection="reqVO.itemIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.categoryIdList != null and reqVO.categoryIdList.size > 0">
                and spu.catgory_id IN
                <foreach collection="reqVO.categoryIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.blackCategoryIdList != null and reqVO.blackCategoryIdList.size > 0">
                and spu.catgory_id NOT IN
                <foreach collection="reqVO.blackCategoryIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.brandIdList != null and reqVO.brandIdList.size > 0">
                and spu.brand_id IN
                <foreach collection="reqVO.brandIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.blackBrandIdList != null and reqVO.blackBrandIdList.size > 0">
                and spu.brand_id NOT IN
                <foreach collection="reqVO.blackBrandIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.blackSkuIdList != null and reqVO.blackSkuIdList.size > 0">
                and item.sku_id NOT IN
                <foreach collection="reqVO.blackSkuIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="reqVO.supplierIds != null and reqVO.supplierIds.size > 0">
                AND spu.supplier_id in
                <foreach collection="reqVO.supplierIds" item="supplierId" open="(" separator="," close=")">
                    #{supplierId}
                </foreach>
            </if>
            ${reqVO.params.dataScope}
            <if test="reqVO.sortord !=null and reqVO.sortOrder !=null">
                order by ${reqVO.sortord}
                <if test="reqVO.sortOrder == 1 ">
                    desc
                </if>
            </if>


    </select>

    <update id="updatePrdtAreaItemShelfStatus">
        update prdt_area_item set
        <if test="shelfStatus !=null and shelfStatus != -1">
            shelf_status = #{shelfStatus},
        </if>
        <if test="minShelfStatus !=null and minShelfStatus != -1">
            min_shelf_status = #{minShelfStatus},
        </if>
        <if test="midShelfStatus !=null and midShelfStatus != -1">
            mid_shelf_status = #{midShelfStatus},
        </if>
        <if test="largeShelfStatus !=null and largeShelfStatus != -1">
            large_shelf_status = #{largeShelfStatus},
        </if>
        shelf_date = now()  where
        area_item_id in
        <foreach collection="areaItemIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectMaxSort" resultType="java.lang.Integer">
        select ifnull(max(sort_num),0) maxSortNum from prdt_area_item
        <where>
            <if test="item.supplierId != null and item.supplierId != '' ">
                and supplier_id = #{item.supplierId}
            </if>
            <if test="item.areaClassId != null and item.areaClassId != '' ">
                and area_class_id = #{item.areaClassId}
            </if>
        </where>
    </select>

    <select id = "selectAreaItemList"  resultType="com.zksr.product.controller.areaItem.vo.PrdtAreaItemPageReqVO">
        select item.area_item_id areaItemId,spu.spu_id spuId,sku.sku_id skuId,spu.supplier_id supplierId,spu.spu_no spuNo,spu.spu_name spuName,
        brand.brand_name brandName,catgory.catgory_name catgoryName,sku.status,
        ifnull(item.shelf_status,0) shelfStatus,
        sku.barcode,sku.unit,sku.properties,sku.mark_price markPrice,sku.stock,sku.suggest_price suggestPrice,
        sku.reference_price referencePrice,sku.reference_sale_price referenceSalePrice,spu.expiration_date expirationDate,
        item.shelf_date shelfDate,item.sort_num sortNum,item.area_class_id areaClassId,class.area_class_name areaClassName
        from
        prdt_area_item item
        left join prdt_sku sku  on sku.sku_id = item.sku_id and sku.sys_code = item.sys_code
        left join prdt_spu spu on spu.spu_id = sku.spu_id and spu.sys_code = sku.sys_code
        left join prdt_brand brand on brand.brand_id = spu.brand_id and brand.sys_code = spu.sys_code
        left join prdt_catgory catgory on catgory.catgory_id = spu.catgory_id  and catgory.sys_code = spu.sys_code
        left join prdt_area_class class on class.area_class_id = item.area_class_id and class.sys_code = item.sys_code
        where
        sku.is_delete = 0
            <if test="reqVO.areaClassId !=null">
                and item.area_class_id = #{reqVO.areaClassId}
            </if>
            <if test="reqVO.areaId !=null">
                and item.area_id = #{reqVO.areaId}
            </if>
            <if test="null != reqVO.itemType" >
                AND item.item_type = #{reqVO.itemType}
            </if>
            <if test="reqVO.keyword !=null and reqVO.keyword !='' ">
                and (spu.spu_no like concat('%', #{reqVO.keyword}, '%')
                or spu.spu_name like concat('%', #{reqVO.keyword}, '%')
                or sku.barcode like concat('%', #{reqVO.keyword}, '%') )
            </if>
            <if test="reqVO.shelfStatus !=null">
                and item.shelf_status = #{reqVO.shelfStatus}
            </if>
            <if test="reqVO.spuId !=null">
                and item.spu_id = #{reqVO.spuId}
            </if>
            <if test="reqVO.supplierId !=null">
                and item.supplier_id = #{reqVO.supplierId}
            </if>


        <if test="reqVO.sortord !=null and reqVO.sortOrder !=null">
            order by ${reqVO.sortord}
            <if test="reqVO.sortOrder == 1 ">
                desc
            </if>
        </if>
    </select>

    <update id="updateAreaItemBatchShelfStatus">
        update prdt_area_item set shelf_status = 0, min_shelf_status = 0, mid_shelf_status = 0, large_shelf_status = 0
        <where>
            <if test="spuId !=null">
               and spu_id = #{spuId}
            </if>
            <if test="skuId !=null">
               and sku_id = #{skuId}
            </if>
            <if test="areaItemIds != null">
              and area_item_id in
                <foreach collection="areaItemIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </update>

    <update id="updateAreaItemBatchMidShelfStatus">
        update prdt_area_item set mid_shelf_status = 0
        <where>
            <if test="spuId !=null">
                and spu_id = #{spuId}
            </if>
            <if test="skuId !=null">
                and sku_id = #{skuId}
            </if>
            <if test="areaItemIds != null">
                and area_item_id in
                <foreach collection="areaItemIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </update>

    <update id="updateAreaItemBatchLargeShelfStatus">
        update prdt_area_item set large_shelf_status = 0
        <where>
            <if test="spuId !=null">
                and spu_id = #{spuId}
            </if>
            <if test="skuId !=null">
                and sku_id = #{skuId}
            </if>
            <if test="areaItemIds != null">
                and area_item_id in
                <foreach collection="areaItemIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </update>


    <select id = "selectAreaItemListByApi"  resultType="com.zksr.product.api.areaItem.dto.AreaItemDTO">
        select item.area_item_id areaItemId,spu.spu_id spuId,sku.sku_id skuId,spu.supplier_id supplierId,spu.spu_no spuNo,spu.spu_name spuName,
        brand.brand_name brandName,catgory.catgory_name catgoryName,sku.status,
        ifnull(item.shelf_status,0) shelfStatus,
        sku.barcode,sku.unit,sku.properties,sku.mark_price markPrice,sku.stock,sku.suggest_price suggestPrice,
        sku.reference_price referencePrice,sku.reference_sale_price referenceSalePrice,spu.expiration_date expirationDate,
        item.shelf_date shelfDate,item.sort_num sortNum,item.area_class_id areaClassId,class.area_class_name areaClassName,
        sku.min_oq minOq,sku.jump_oq jumpOq,sku.max_oq maxOq
        from
        prdt_area_item item
        left join prdt_sku sku  on sku.sku_id = item.sku_id and sku.sys_code = item.sys_code
        left join prdt_spu spu on spu.spu_id = sku.spu_id and spu.sys_code = sku.sys_code
        left join prdt_brand brand on brand.brand_id = spu.brand_id and brand.sys_code = spu.sys_code
        left join prdt_catgory catgory on catgory.catgory_id = spu.catgory_id  and catgory.sys_code = spu.sys_code
        left join prdt_area_class class on class.area_class_id = item.area_class_id and class.sys_code = item.sys_code
        where
            sku.is_delete = 0
            <if test="reqVO.areaClassId !=null">
                and item.area_class_id = #{reqVO.areaClassId}
            </if>
            <if test="reqVO.areaId !=null">
                and item.area_id = #{reqVO.areaId}
            </if>

            <if test="reqVO.keyword !=null and reqVO.keyword !='' ">
                and (spu.spu_no like concat('%', #{reqVO.keyword}, '%')
                or spu.spu_name like concat('%', #{reqVO.keyword}, '%')
                or sku.barcode like concat('%', #{reqVO.keyword}, '%') )
            </if>
            <if test="reqVO.shelfStatus !=null">
                and item.shelf_status = #{reqVO.shelfStatus}
            </if>
            <if test="reqVO.spuId !=null">
                and item.spu_id = #{reqVO.spuId}
            </if>
            <if test="reqVO.supplierId !=null">
                and item.supplier_id = #{reqVO.supplierId}
            </if>
            <if test="null != reqVO.itemType" >
                AND item.item_type = #{reqVO.itemType}
            </if>

        <if test="reqVO.sortord !=null and reqVO.sortOrder !=null">
            order by ${reqVO.sortord}
            <if test="reqVO.sortOrder == 1 ">
                desc
            </if>
        </if>
    </select>
    <select id="selectByReleaseSpuId" resultType="com.zksr.product.controller.spu.vo.SpuSkuReleaseListVO">
        SELECT
            pai.area_item_id as itemId,
            pai.min_shelf_status,
            pai.mid_shelf_status,
            pai.large_shelf_status,
            pai.sku_id,
            sku.thumb,
            sku.properties,
            sku.expiration_date,
            sku.stock,
            sku.mark_price,
            sku.suggest_price,
            sku.barcode,
            spu.min_unit,
            sku.min_oq,
            sku.jump_oq,
            sku.max_oq,
            sku.mid_mark_price,
            sku.mid_barcode,
            spu.mid_unit,
            sku.mid_min_oq,
            sku.mid_jump_oq,
            sku.mid_max_oq,
            sku.mid_suggest_price,
            sku.large_mark_price,
            sku.large_barcode,
            spu.large_unit,
            sku.large_min_oq,
            sku.large_jump_oq,
            sku.large_max_oq,
            sku.large_suggest_price
        FROM
            prdt_area_item pai
            LEFT JOIN prdt_sku sku ON pai.sku_id = sku.sku_id
            LEFT JOIN prdt_spu spu ON spu.spu_id = pai.spu_id
        WHERE
            pai.spu_id = #{spuId}
            AND pai.area_id = #{areaId}
            AND pai.area_class_id = #{classId}
            AND pai.shelf_status = '1'
            AND sku.is_delete = 0
        ORDER BY
            pai.area_item_id desc
    </select>

    <select id="getHomePagesSkuData" resultType="com.zksr.report.api.homePages.dto.HomePagesSkuDataRespDTO">
        SELECT
            itemShelf.sys_code,
            <if test="null != isSupplier and isSupplier != 0">
                itemShelf.supplier_id,
            </if>
            COUNT(DISTINCT CASE WHEN DATE_FORMAT(itemShelf.create_time, '%Y%m%d') &lt;= DATE_FORMAT(now(), '%Y%m%d') THEN itemShelf.sku_id END) AS skuShelfQty,
            COUNT(DISTINCT CASE WHEN DATE_FORMAT(itemShelf.create_time, '%Y%m%d') &lt; DATE_FORMAT(DATE_SUB(now(), INTERVAL 1 DAY), '%Y%m%d') THEN itemShelf.sku_id END) AS beforeSkuShelfQty,
--             COUNT(DISTINCT CASE WHEN DATE_FORMAT(itemShelf.create_time, '%Y%m%d') &lt; DATE_FORMAT(now(), '%Y%m%d') THEN pc2.pid END) AS category1Qty,
--             COUNT(DISTINCT CASE WHEN DATE_FORMAT(itemShelf.create_time, '%Y%m%d') &lt; DATE_FORMAT(DATE_SUB(now(), INTERVAL 1 DAY), '%Y%m%d') THEN pc2.pid END) AS beforeCategory1Qty,
            COUNT(DISTINCT CASE WHEN DATE_FORMAT(itemShelf.create_time, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d') THEN itemShelf.sku_id END) AS skuNewShelfQty,
            COUNT(DISTINCT CASE WHEN DATE_FORMAT(itemShelf.create_time, '%Y%m%d') = DATE_FORMAT(DATE_SUB(now(), INTERVAL 1 DAY), '%Y%m%d') THEN itemShelf.sku_id END) AS beforeSkuNewShelfQty,
            (
                SELECT
                    COUNT(1)
                FROM
                    prdt_sku sku
                WHERE
                    sku.sys_code = #{sysCode}
            ) AS skuTotalQty
        FROM (
            SELECT
                sys_code,
                area_id,
                supplier_id,
                create_time,
                sku_id,
                spu_id
            FROM
                prdt_area_item
            WHERE
                sys_code = #{sysCode}
                AND create_time &lt;= CONCAT(#{endDate}, ' 23:59:59.999')
                <if test="null != isDc and isDc != 0 and null != areaIds and areaIds.size > 0">
                    AND area_id IN
                    <foreach collection="areaIds" item="areaId" open="(" separator="," close=")">
                        #{areaId}
                    </foreach>
                </if>

            <if test="null != isDc and isDc == 0">
                UNION ALL

                SELECT
                    sys_code,
                    null,
                    supplier_id,
                    create_time,
                    sku_id,
                    spu_id
                FROM
                    prdt_supplier_item
                WHERE
                    sys_code = #{sysCode}
                    AND create_time &lt;= CONCAT(#{endDate}, ' 23:59:59.999')
            </if>

        ) itemShelf
--             LEFT JOIN prdt_spu ps ON itemShelf.spu_id = ps.spu_id
--             LEFT JOIN prdt_catgory pc3 On ps.catgory_id = pc3.catgory_id
--             LEFT JOIN prdt_catgory pc2 On pc3.pid = pc2.catgory_id
        WHERE
            itemShelf.sys_code = #{sysCode}
        GROUP BY
            itemShelf.sys_code
            <if test="null != isSupplier and isSupplier != 0">
                , itemShelf.supplier_id
            </if>
    </select>
</mapper>