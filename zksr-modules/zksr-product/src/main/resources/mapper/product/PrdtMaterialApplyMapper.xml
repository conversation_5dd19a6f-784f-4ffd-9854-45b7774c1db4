<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtMaterialApplyMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id = "selectPageMaterialApply"  resultType="com.zksr.product.controller.materialApply.vo.PrdtMaterialApplyRespVO">
        SELECT
            pma.material_id,    <!-- 素材id -->
            pma.apply_type,     <!-- 素材应用类型 -->
            pma.start_time,     <!-- 生效时间 -->
            pma.end_time,       <!-- 失效时间 -->
<!--            pma.create_time,    &lt;!&ndash; 打标时间 &ndash;&gt;-->
            pm.img              <!-- 图片地址 -->
<!--            case
                when  start_time &gt; now() then 0                          &lt;!&ndash; 未生效 &ndash;&gt;
                when  start_time &lt;= now() and end_time &gt; now() then 1 &lt;!&ndash; 生效中 &ndash;&gt;
                when  end_time &lt; now() then 2                            &lt;!&ndash; 已失效 &ndash;&gt;
                else 2 end materialApplyStatus                              &lt;!&ndash; 素材应用状态 &ndash;&gt;-->
        FROM
            prdt_material_apply pma
        LEFT JOIN prdt_material pm ON pm.material_id = pma.material_id
        <where>
            <if test="reqVO.applyType != null and reqVO.applyType != ''">
                AND pma.apply_type = #{reqVO.applyType}
            </if>
            <if test="reqVO.startTime != null and reqVO.endTime != null">
                AND pma.start_time &gt;= #{reqVO.startTime}
                AND pma.end_time &lt;= #{reqVO.endTime}
            </if>
            <if test="reqVO.materialApplyStatus == 0 ">
                AND pma.start_time &gt; now()
            </if>
            <if test="reqVO.materialApplyStatus == 1 ">
                AND pma.start_time &lt;= now()
                AND pma.end_time &gt; now()
            </if>
            <if test="reqVO.materialApplyStatus == 2 ">
                AND pma.end_time &lt;= now()
            </if>
        </where>
        GROUP BY
            pma.material_id,
            pma.apply_type,
            pma.start_time,
            pma.end_time,
            <!-- pma.create_time, -->
            pm.img
        ORDER BY
            pma.start_time DESC

    </select>

    <select id = "getByMaterialApplyByApplyIds"  resultType="com.zksr.product.controller.materialApply.vo.PrdtMaterialApplyRespVO">
        SELECT
        pma.material_apply_id,    <!-- 素材应用id -->
        pma.material_id,    <!-- 素材id -->
        pma.apply_type,     <!-- 素材应用类型 -->
        pma.apply_id,     <!-- 素材应用id -->
        pma.start_time,     <!-- 生效时间 -->
        pma.end_time,       <!-- 失效时间 -->
        pm.img              <!-- 图片地址 -->
        FROM
        prdt_material_apply pma
        LEFT JOIN prdt_material pm ON pm.material_id = pma.material_id
        where
                pma.apply_id IN
                <foreach collection="applyIds" item="applyId" open="(" separator="," close=")">
                    #{applyId}
                </foreach>
                AND pma.start_time &lt;= now()
                AND pma.end_time &gt;= now()

    </select>

    <select id = "getByMaterialApplyByMaterial"  resultType="com.zksr.product.api.materialApply.vo.MaterialApplyVO">
        SELECT
        pma.material_apply_id,    <!-- 素材应用id -->
        pma.material_id,    <!-- 素材id -->
        pma.apply_type,     <!-- 素材应用类型 -->
        pma.apply_id,       <!-- 素材应用id -->
        pma.start_time,     <!-- 生效时间 -->
        pma.end_time,       <!-- 失效时间 -->
        pm.img,              <!-- 图片地址 -->
        pm.name,              <!-- 素材名称 -->
        pm.img_size              <!-- 素材大小 -->
        FROM
        prdt_material_apply pma
        LEFT JOIN prdt_material pm ON pm.material_id = pma.material_id
        <where>
        <if test="reqVO.applyType != null ">
            AND pma.apply_type = #{reqVO.applyType}
        </if>
        <if test="reqVO.startTime != null and reqVO.endTime != null">
            AND pma.start_time = #{reqVO.startTime}
            AND pma.end_time = #{reqVO.endTime}
        </if>
        <if test="reqVO.materialId != null ">
            AND pma.material_id = #{reqVO.materialId}
        </if>
        <if test="reqVO.applyId != null ">
            AND pma.apply_id = #{reqVO.applyId}
        </if>
        </where>
    </select>

</mapper>