<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtBlockSchemeMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPage" resultType="com.zksr.product.domain.PrdtBlockScheme">
        SELECT
            DISTINCT scheme.block_scheme_id,
            scheme.scheme_no,
            scheme.scheme_name,
            scheme.status,
            scheme.memo,
            scheme.create_by,
            scheme.create_time,
            scheme.update_by,
            scheme.update_time
        FROM
            prdt_block_scheme scheme
        LEFT JOIN prdt_block_branch branch ON branch.scheme_no = scheme.scheme_no
        LEFT JOIN prdt_block_sku sku ON sku.scheme_no = scheme.scheme_no
        <where>
            scheme.del_flag = 0
            <if test="pageReqVO.sysCode != null">
                AND scheme.sys_code = #{pageReqVO.sysCode}
            </if>
            <if test="pageReqVO.schemeNo != null and pageReqVO.schemeNo != ''">
                AND scheme.scheme_no = #{pageReqVO.schemeNo}
            </if>
            <if test="pageReqVO.schemeName != null and pageReqVO.schemeName != ''">
                AND scheme.scheme_name LIKE CONCAT('%', #{pageReqVO.schemeName}, '%')
            </if>
            <if test="pageReqVO.status != null">
                AND scheme.status = #{pageReqVO.status}
            </if>
            <if test="pageReqVO.createBy != null and pageReqVO.createBy != ''">
                AND scheme.create_by LIKE CONCAT('%', #{pageReqVO.createBy}, '%')
            </if>
            <if test="pageReqVO.branchIds != null and pageReqVO.branchIds.size() > 0">
                AND branch.branch_id IN
                <foreach collection="pageReqVO.branchIds" item="branchId" separator="," open="(" close=")">
                    #{branchId}
                </foreach>
            </if>
            <if test="pageReqVO.skuIds != null and pageReqVO.skuIds.size() > 0">
                AND sku.sku_id IN
                <foreach collection="pageReqVO.skuIds" item="skuId" separator="," open="(" close=")">
                    #{skuId}
                </foreach>
            </if>
            <if test="pageReqVO.areaId != null and pageReqVO.areaId.size() > 0">
                AND scheme.area_id IN
                <foreach collection="pageReqVO.areaId" item="areaIdItem" separator="," open="(" close=")">
                    #{areaIdItem}
                </foreach>
            </if>
        </where>
        ORDER BY scheme.create_time DESC
    </select>
</mapper>