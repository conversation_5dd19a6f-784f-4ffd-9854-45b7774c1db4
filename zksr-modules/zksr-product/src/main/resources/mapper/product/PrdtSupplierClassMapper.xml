<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtSupplierClassMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectListPage" resultType="com.zksr.product.api.supplierClass.dto.SupplierClassRespDTO">
        SELECT
            psc.catgory_id,
            psc.supplier_id,
            pc3.catgory_name,
            pc3.`status`,
            pc2.catgory_name AS pidName,
            psc.is_after_sales,
            psc.after_sales_time_type,
            psc.after_sales_time
        FROM
            prdt_supplier_class psc
            LEFT JOIN prdt_catgory pc3 ON psc.catgory_id = pc3.catgory_id
            LEFT JOIN prdt_catgory pc2 ON pc3.pid = pc2.catgory_id
        WHERE
            psc.supplier_id = #{reqVo.supplierId}
            <if test="null != reqVo.catgoryName">
                AND pc3.catgory_name LIKE CONCAT('%', #{reqVo.catgoryName}, '%')
            </if>
            <if test="null != reqVo.status" >
                AND pc3.`status` = #{reqVo.status}
            </if>
            <if test="null != reqVo.catgoryId" >
                AND psc.catgory_id = #{reqVo.catgoryId}
            </if>
    </select>


    <update id="batchEditCatgoryAfterConfig">

        UPDATE
            prdt_supplier_class
        SET
            is_after_sales = #{updateVo.isAfterSales},
            after_sales_time_type = #{updateVo.afterSalesTimeType},
            after_sales_time = #{updateVo.afterSalesTime}
        WHERE
            supplier_id = #{updateVo.supplierId}
            AND catgory_id IN
                <foreach collection="updateVo.catgoryIds" item="catgoryId" open="(" separator="," close=")">
                    #{catgoryId}
                </foreach>

    </update>
</mapper>