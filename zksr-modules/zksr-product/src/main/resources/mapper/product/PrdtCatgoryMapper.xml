<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtCatgoryMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getCatgoryBySupplierId" resultType="com.zksr.product.domain.PrdtCatgory">
        SELECT pc.catgory_id,
               pc.sys_code,
               pc.create_by,
               pc.create_time,
               pc.update_by,
               pc.update_time,
               pc.pid,
               pc.catgory_name,
               pc.icon,
               pc.sort,
               pc.status,
               pc.partner_rate,
               pc.dc_rate,
               pc.colonel1_rate,
               pc.colonel2_rate,
               pc.memo,
               pc.level
        FROM prdt_catgory pc
                 LEFT JOIN prdt_supplier_class psc ON pc.catgory_id = psc.catgory_id
        WHERE pc.status = '1'
          and psc.supplier_id = #{supplierId}
        ORDER BY pc.create_time DESC
    </select>
    <select id="selectByIdAndLevelThree" resultType="com.zksr.product.api.catgory.dto.CatgoryRateDTO">
        SELECT
            pc3.catgory_id catgoryId,   --  管理分类ID
            pc3.sys_code sysCode,       --  平台ID
            pc3.pid,    --  父级ID
            pc3.catgory_name catgoryName,   --  管理分类名称
            pc3.status,
            pc1.partner_rate partnerRate,   --  平台商分润比例
            IFNULL(pcr.dc_rate, pc1.dc_rate) dcRate,    --  运营商分润比例
            IFNULL(pcr.colonel1_rate, pc1.colonel1_rate) colonel1Rate,  --  业务员负责人分润比例
            IFNULL(pcr.colonel2_rate, pc1.colonel2_rate) colonel2Rate   --  业务员分润比例
        FROM
            prdt_catgory pc3
            INNER JOIN prdt_catgory pc2 ON pc2.catgory_id = pc3.pid AND pc2.status = '1'
            INNER JOIN prdt_catgory pc1 ON pc1.catgory_id = pc2.pid AND pc2.status = '1'
            LEFT JOIN prdt_catgory_rate pcr ON pcr.catgory_id = pc2.catgory_id AND pcr.area_id = #{areaId}
        WHERE
            pc3.catgory_id = #{catgoryId}
            AND pc3.status = '1'
            AND pc3.level = 3   --  只查三级
    </select>
    <select id="getCatgoryList" resultType="com.zksr.product.domain.PrdtCatgory">
        SELECT
            *
        FROM
            prdt_catgory
        <where>
            <if test="catgoryId != null">
                AND catgory_id = #{catgoryId}
            </if>
            <if test="supplierId != null">
                AND catgory_id IN (SELECT catgory_id FROM prdt_supplier_class WHERE supplier_id = #{supplierId})
            </if>
            <if test="level != null">
                AND `level` = #{level}
            </if>
        </where>
    </select>
    <select id="getCatgoryFirstId" resultType="com.zksr.product.api.catgory.dto.CatgoryIdDTO">
        SELECT
            pc3.catgory_id catgoryId,
            pc1.catgory_id catgoryFirstId
        FROM
            prdt_catgory pc3
                LEFT JOIN prdt_catgory pc2 ON pc3.pid = pc2.catgory_id
                LEFT JOIN prdt_catgory pc1 ON pc2.pid = pc1.catgory_id
        WHERE
            pc1.catgory_id IS NOT NULL
    </select>
    <select id="getPageBySupplierId" resultType="com.zksr.product.controller.catgory.vo.PrdtCatgoryRespVO">
        SELECT
            pc.*
        FROM
            prdt_catgory pc
        where
            pc.status = 1
            <if test="null != pageReqVO.level">
                AND pc.level = #{pageReqVO.level}
            </if>
            <if test="null != pageReqVO.catgoryId">
                AND pc.catgory_id like concat('%',#{pageReqVO.catgoryId},'%')
            </if>
            <if test="null != pageReqVO.catgoryName">
                AND pc.catgory_name like concat('%',#{pageReqVO.catgoryName},'%')
            </if>
            <if test="null != pageReqVO.supplierId">
                AND pc.catgory_id IN (
                    <!-- 三级 -->
                    SELECT psc.catgory_id FROM prdt_supplier_class psc WHERE psc.supplier_id = #{pageReqVO.supplierId}
                    <!-- 二级 -->
                    UNION
                    SELECT
                        pc.pid
                    FROM
                        prdt_supplier_class psc
                        LEFT JOIN prdt_catgory pc ON pc.catgory_id = psc.catgory_id
                    WHERE
                        psc.supplier_id = #{pageReqVO.supplierId}
                    <!-- 一级级 -->
                    UNION
                    SELECT
                        pc1.pid
                    FROM
                        prdt_catgory pc1 WHERE pc1.catgory_id IN (
                            SELECT
                                pc.pid
                            FROM
                                prdt_supplier_class psc
                                LEFT JOIN prdt_catgory pc ON pc.catgory_id = psc.catgory_id
                            WHERE
                                psc.supplier_id = #{pageReqVO.supplierId}
                        )
                )
            </if>
    </select>

    <!-- 根据供应商ID和分类名称和分类级别查询分类-->
    <select id="selectCategoryBySupplierAneNameAndLevel" resultType="com.zksr.product.domain.PrdtCatgory">
        SELECT
            *
        FROM
            prdt_catgory
        WHERE
            catgory_id = #{categoryId}
            AND level = #{level}
            AND catgory_id IN (SELECT catgory_id FROM prdt_supplier_class WHERE supplier_id = #{supplierId})
            AND status = '1'
        limit 1
    </select>
    <select id="getCategoriesWithProducts" resultType="java.lang.Long">
        SELECT
            DISTINCT pc.catgory_id AS catgoryId
        FROM prdt_catgory pc
            LEFT JOIN prdt_spu ps ON pc.catgory_id = ps.catgory_id
        WHERE ps.catgory_id IN
        <foreach collection="catgoryIds" item="catgoryId" open="(" separator="," close=")">
            #{catgoryId}
        </foreach>
        <if test="supplierId != null">
            AND ps.supplier_id = #{supplierId}
        </if>
    </select>

    <!-- 根据供应商ID和分类Id和分类级别查询分类-->
    <select id="selectCategoryBySupplierAndCategoryIdAndLevel" resultType="com.zksr.product.domain.PrdtCatgory">
        SELECT
            *
        FROM
            prdt_catgory
        WHERE
            catgory_Id = #{categoryId}
          AND level = #{level}
          AND catgory_id IN (SELECT catgory_id FROM prdt_supplier_class WHERE supplier_id = #{supplierId})
          AND status = '1'
    </select>
    <select id="selectPageExt" resultType="com.zksr.product.domain.PrdtCatgory">
        SELECT
            *
        FROM
            prdt_catgory
        <where>
            <if test="catgoryId != null">
                AND catgory_id = #{catgoryId}
            </if>
            <if test="sysCode != null">
                AND sys_code = #{sysCode}
            </if>
            <if test="pid != null">
                AND pid = #{pid}
            </if>
            <if test="catgoryName != null">
                AND catgory_name LIKE concat('%', #{catgoryName}, '%')
            </if>
            <if test="icon != null">
                AND icon = #{icon}
            </if>
            <if test="sort != null">
                AND sort = #{sort}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="partnerRate != null">
                AND partner_rate = #{partnerRate}
            </if>
            <if test="dcRate != null">
                AND dc_rate = #{dcRate}
            </if>
            <if test="colonel1Rate != null">
                AND colonel1_rate = #{colonel1Rate}
            </if>
            <if test="colonel2Rate != null">
                AND colonel2_rate = #{colonel2Rate}
            </if>
            <if test="memo != null">
                AND memo = #{memo}
            </if>
            <if test="level != null">
                AND `level` = #{level}
            </if>
            <if test="supplierId != null">
                AND catgory_id IN (SELECT catgory_id FROM prdt_supplier_class WHERE supplier_id = #{supplierId})
            </if>
        </where>
        ORDER BY
            catgory_id DESC
    </select>
    <select id="selectThreeByFirst" resultType="com.zksr.product.domain.PrdtCatgory">
        SELECT
            pc3.catgory_id,
            pc1.catgory_id pid,
            pc1.sale_total_rate
        FROM
            prdt_catgory pc3
            LEFT JOIN prdt_catgory pc2 ON pc2.catgory_id = pc3.pid
            LEFT JOIN prdt_catgory pc1 ON pc1.catgory_id = pc2.pid
        WHERE
            pc3.catgory_id IN
            <foreach collection="idList" item="catgoryId" open="(" separator="," close=")">
                #{catgoryId}
            </foreach>
    </select>
    <select id="getCategoryByNameAndParentId" resultType="com.zksr.product.domain.PrdtCatgory">
        SELECT
            *
        FROM
            prdt_catgory
        WHERE
            status = '1'
            <if test="catgoryName != null">
                AND catgory_name = #{catgoryName}
            </if>
            <if test="pid != null">
                AND pid = #{pid}
            </if>
            LIMIT 1
    </select>

    <select id="getHomePagesCategoryData" resultType="com.zksr.report.api.homePages.dto.HomePagesSkuDataRespDTO">
        SELECT
            COUNT(DISTINCT CASE WHEN DATE_FORMAT(create_time, '%Y%m%d') &lt;= DATE_FORMAT(now(), '%Y%m%d') THEN catgory_id END) AS category1Qty,
            COUNT(DISTINCT CASE WHEN DATE_FORMAT(create_time, '%Y%m%d') &lt; DATE_FORMAT(now(), '%Y%m%d') THEN catgory_id END) AS beforeCategory1Qty
        FROM
            prdt_catgory
        WHERE
            sys_code = #{sysCode}
            AND create_time &lt;= CONCAT(#{endDate}, ' 23:59:59.999')
            AND level = 1
    </select>
    <select id="isExistsProduct" resultType="java.lang.String">
        SELECT prdt_spu.spu_no FROM prdt_spu
        <where>
            catgory_id in
            <foreach collection="catgoryIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </where>
        group by prdt_spu.spu_no
    </select>

    <select id="getCategoryBySupplierIds" resultType="com.zksr.product.domain.PrdtCatgory">
        SELECT pc.catgory_id,
            pc.sys_code,
            pc.create_by,
            pc.create_time,
            pc.update_by,
            pc.update_time,
            pc.pid,
            pc.catgory_name,
            pc.icon,
            pc.sort,
            pc.status,
            pc.partner_rate,
            pc.dc_rate,
            pc.colonel1_rate,
            pc.colonel2_rate,
            pc.memo,
            pc.level
        FROM prdt_catgory pc
        LEFT JOIN prdt_supplier_class psc ON pc.catgory_id = psc.catgory_id
        WHERE pc.status = '1'
        <if test="supplierIds != null and supplierIds.size() > 0">
            AND psc.supplier_id IN
            <foreach collection="supplierIds" item="supplierId" separator="," open="(" close=")">
                #{supplierId}
            </foreach>
        </if>
        ORDER BY pc.create_time DESC
    </select>
</mapper>
