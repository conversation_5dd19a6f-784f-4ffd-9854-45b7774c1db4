<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtChannelAreaClassMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getAreaClassChannelList" resultType="java.lang.Long">
        <!-- 兼容一级, 二级, 三级展示分类查询渠道 -->
        SELECT
            channel.channel_id
        FROM (
            <!-- 一级 -->
            SELECT
                pcac.channel_id
            FROM
                prdt_area_class pac
                LEFT JOIN prdt_area_class pac2 ON pac2.pid = pac.area_class_id
                LEFT JOIN prdt_area_class pac3 ON pac3.pid = pac2.area_class_id
                LEFT JOIN prdt_channel_area_class pcac ON pcac.area_class_id = pac3.area_class_id
            WHERE
                pac.area_class_id = #{areaClassId}
            UNION
            <!-- 二级 -->
            SELECT
                pcac.channel_id
            FROM
                prdt_area_class pac2
                LEFT JOIN prdt_area_class pac3 ON pac3.pid = pac2.area_class_id
                LEFT JOIN prdt_channel_area_class pcac ON pcac.area_class_id = pac3.area_class_id
            WHERE
                pac2.area_class_id = #{areaClassId}
            <!-- 三级 -->
            UNION
            SELECT
                pcac.channel_id
            FROM
                prdt_area_class pac3
                LEFT JOIN prdt_channel_area_class pcac ON pcac.area_class_id = pac3.area_class_id
            WHERE
                pac3.area_class_id = #{areaClassId}
        ) channel
        WHERE
            <!-- 忽略空值 -->
            channel.channel_id IS NOT NULL
    </select>
</mapper>
