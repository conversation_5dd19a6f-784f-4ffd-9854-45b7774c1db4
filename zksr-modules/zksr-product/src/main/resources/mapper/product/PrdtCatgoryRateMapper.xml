<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtCatgoryRateMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getRateInfo" resultType="com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRateRespVO">
        SELECT pc.catgory_id,
               pc.catgory_name,
               pc.pid,
               pc.status,
               pc.partner_rate,
               pc.software_rate,
               pc.memo,
               pcr.dc_rate,
               pcr.colonel1_rate,
               pcr.colonel2_rate
        FROM prdt_catgory pc
                 LEFT JOIN prdt_catgory_rate pcr ON pc.catgory_id = pcr.catgory_id
            AND pcr.area_id = #{areaId}
        WHERE pc.catgory_id = #{catgoryId}
    </select>

    <select id="getCatgoryRateList"
            resultType="com.zksr.product.controller.catgoryRate.vo.PrdtCatgoryRateRespVO">
        SELECT * FROM
            (
                SELECT
                    pc.catgory_id,
                    pc.catgory_name,
                    pc.pid,
                    pc.partner_rate,
                    pc.dc_rate,
                    pc.colonel1_rate,
                    pc.colonel2_rate,
                    pc.status
                FROM
                    prdt_catgory pc
                WHERE
                    pc.LEVEL = 1

                UNION

                SELECT DISTINCT
                    pc.catgory_id,
                    pc.catgory_name,
                    pc.pid,
                    pc.partner_rate,
                    pcr.dc_rate,
                    pcr.colonel1_rate,
                    pcr.colonel2_rate,
                    pc.status
                FROM
                    prdt_catgory pc
                LEFT JOIN prdt_catgory_rate pcr
                    ON pc.catgory_id = pcr.catgory_id AND pcr.area_id = #{respVO.areaId}
                WHERE
                    pc.LEVEL != 1
            ) catgory
        <where>
            <if test="null != respVO.catgoryId">
                and catgory.catgory_id = #{respVO.catgoryId}
            </if>
            <if test="null != respVO.catgoryName and '' != respVO.catgoryName">
                and catgory.catgory_name  LIKE CONCAT('%', #{respVO.catgoryName},'%')
            </if>
            <if test="null != respVO.status and '' != respVO.status">
                and catgory.status = #{respVO.status}
            </if>
        </where>
    </select>
</mapper>
