<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtSkuMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <update id="updateBatchPrdtSkuStatus">
        update prdt_sku set status = 0, mid_status = 0, large_status = 0
        <where>
        <if test="spuId !=null">
          and  spu_id = ${spuId}
        </if>
        <if test="skuIds != null">
        and sku_id in
        <foreach collection="skuIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        </if>
        </where>
    </update>

    <update id="updateBatchPrdtSkuMidStatus">
        update prdt_sku set mid_status = 0
        <where>
            <if test="spuId !=null">
                and  spu_id = ${spuId}
            </if>
            <if test="skuIds != null">
                and sku_id in
                <foreach collection="skuIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </update>

    <update id="updateBatchPrdtSkuLargeStatus">
        update prdt_sku set large_status = 0
        <where>
            <if test="spuId !=null">
                and  spu_id = ${spuId}
            </if>
            <if test="skuIds != null">
                and sku_id in
                <foreach collection="skuIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </update>
    <update id="updateStatusEnable">
        <!-- 启用未删除的所有sku -->
        UPDATE prdt_sku SET status = 1 WHERE spu_id = #{spuId} AND is_delete = 0
    </update>

    <update id="updateMidStatusEnable">
        <!-- 启用未删除的所有sku -->
        UPDATE prdt_sku SET mid_status = 1 WHERE spu_id = #{spuId} AND is_delete = 0
    </update>

    <update id="updateLargeStatusEnable">
        <!-- 启用未删除的所有sku -->
        UPDATE prdt_sku SET large_status = 1 WHERE spu_id = #{spuId} AND is_delete = 0
    </update>

    <select id="getSelectedPrdtSku" resultType="com.zksr.product.controller.sku.vo.PrdtSkuSelectedRespVO">
        SELECT
        	sku_id,
        	spu_name,
        	properties
        FROM
        	prdt_sku sku
        	LEFT JOIN prdt_spu spu ON spu.spu_id = sku.spu_id
        where
        spu.is_delete = 0 and sku.is_delete = 0 and
        sku_id in
        <foreach collection="skuIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getSelectedPrdtSpuIds" resultType="java.lang.Long">
        SELECT
            spu.spu_id
        FROM
            prdt_sku sku
            LEFT JOIN prdt_spu spu ON spu.spu_id = sku.spu_id
        where
            spu.is_delete = 0 and sku.is_delete = 0 and
            sku_id in
            <foreach collection="skuIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>
    <select id="getLastUpdateTime" resultType="java.util.Date">
        SELECT
            sku.last_update_time AS lastUpdateTime
        FROM prdt_sku sku
                 LEFT JOIN prdt_spu spu ON sku.spu_id = spu.spu_id
        WHERE sku.source_no = #{sourceNo}
          AND spu.supplier_id = #{supplierId}
          AND spu.is_delete = 0 and sku.is_delete = 0
    </select>

    <select id="getSkuPricesInfoList" parameterType="com.zksr.product.api.sku.vo.SkuPricesPageReqVO"
        resultType="com.zksr.product.api.sku.dto.SkuPricesRespDTO">
        SELECT
            spu.spu_no
            , sku.sku_id
            , spu.spu_name
            , spu.supplier_id
            , thirdCatgory.catgory_name AS thirdCatgoryName
            , secondCatgory.catgory_name AS secondCatgoryName
            , firstCatgory.catgory_name AS firstCatgoryName
            , brand.brand_name
            , sku.properties
            , sku.large_mark_price
            , sku.large_cost_price
            , sku.mid_mark_price
            , sku.mid_cost_price
            , sku.mark_price AS minMarkPrice
            , sku.cost_price AS minCostPrice
            , spu.mid_size
            , spu.mid_unit
            , spu.large_size
            , spu.large_unit
            , spu.min_unit
            , sku.retail_price
            , sku.profit_rate
            , sku.profit_amount
            , sku.mid_retail_price
            , sku.mid_profit_rate
            , sku.mid_profit_amount
            , sku.large_retail_price
            , sku.large_profit_rate
            , sku.large_profit_amount
        FROM
            prdt_sku sku
            LEFT JOIN prdt_spu spu ON sku.spu_id = spu.spu_id
            LEFT JOIN prdt_catgory thirdCatgory ON spu.catgory_id = thirdCatgory.catgory_id
            LEFT JOIN prdt_catgory secondCatgory ON thirdCatgory.pid = secondCatgory.catgory_id
            LEFT JOIN prdt_catgory firstCatgory ON secondCatgory.pid = firstCatgory.catgory_id
            LEFT JOIN prdt_brand brand ON spu.brand_id = brand.brand_id
        WHERE
            sku.is_delete = 0 and spu.is_delete = 0
            <if test='status != null'>
                AND sku.status = #{status}
            </if>
            <if test='sysCode != null'>
                AND sku.sys_code = #{sysCode}
            </if>
            <if test='validStockQty != null'>
                AND (sku.stock - sku.sale_qty - sku.synced_qty) &lt; #{validStockQty}
            </if>
            <if test='brandId != null'>
                AND spu.brand_id = #{brandId}
            </if>
            <if test='supplierId != null'>
                AND spu.supplier_id = #{supplierId}
            </if>
            <if test='catgoryId != null'>
                AND spu.catgory_id = #{catgoryId}
            </if>
            <if test='spuNo != null and spuNo != ""'>
                AND spu.spu_no LIKE CONCAT('%', #{spuNo}, '%')
            </if>
            <if test='spuName != null and spuName != ""'>
                AND spu.spu_name LIKE CONCAT('%', #{spuName}, '%')
            </if>
        ORDER BY
            sku.spu_id
            DESC
    </select>

    <select id="selectSkuPage" resultType="com.zksr.product.controller.sku.vo.PrdtSkuRespVO">
        SELECT
            sku.sku_id,
            sku.spu_id,
            spu.spu_name,
            sku.barcode,
            brand.brand_name,
            spu.spec_name,
            sku.mark_price,
            spu.supplier_id
        FROM
            prdt_sku sku
        LEFT JOIN
            prdt_spu spu ON sku.spu_id = spu.spu_id
        LEFT JOIN
            prdt_brand brand ON spu.brand_id = brand.brand_id
        <where>
            spu.is_delete = 0 and sku.is_delete = 0
            <if test="pageReqVO.sysCode != null">
                AND sku.sys_code = #{pageReqVO.sysCode}
            </if>
            <if test="pageReqVO.skuId != null">
                AND sku.sku_id = #{pageReqVO.skuId}
            </if>
            <if test="pageReqVO.spuId != null">
                AND sku.spu_id = #{pageReqVO.spuId}
            </if>
            <if test="pageReqVO.spuName != null and pageReqVO.spuName != ''">
                AND spu.spu_name LIKE CONCAT('%', #{pageReqVO.spuName}, '%')
            </if>
            <if test="pageReqVO.supplierId != null and pageReqVO.supplierId != ''">
                AND spu.supplier_id = #{pageReqVO.supplierId}
            </if>
        </where>
    </select>
    <select id="selectRelationSpuCombineList" resultType="java.lang.Long">
        SELECT
            pai.spu_combine_id
        FROM
            prdt_area_item pai
            INNER JOIN prdt_spu_combine psc ON psc.spu_combine_id = pai.spu_combine_id
        WHERE
            pai.activity_end_time &gt; NOW()
            AND pai.sku_id IN
            <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                #{skuId}
            </foreach>
        UNION
        SELECT
            psi.spu_combine_id
        FROM
            prdt_supplier_item psi
            INNER JOIN prdt_spu_combine psc ON psc.spu_combine_id = psi.spu_combine_id
        WHERE
            psi.activity_end_time &gt; NOW()
            AND psi.sku_id IN
            <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                #{skuId}
            </foreach>
    </select>
    <select id="selectBySpuIdAreaShelfList" resultType="com.zksr.product.api.sku.dto.SkuDTO">
        select sku.sku_id as skuId,
        <choose>
            <when test="areaShelfStatus == null">
                IFNULL(MAX(area.min_shelf_status ), 0)  as areaMinShelfStatus,
                IFNULL(MAX(area.mid_shelf_status ), 0)  as areaMidShelfStatus,
                IFNULL(MAX(area.large_shelf_status ), 0)  as areaLargeShelfStatus,
                IFNULL(MAX(supplier.min_shelf_status ), 0)  as supplierMinShelfStatus,
                IFNULL(MAX(supplier.mid_shelf_status ), 0)  as supplierMidShelfStatus,
                IFNULL(MAX(supplier.large_shelf_status ), 0)  as supplierLargeShelfStatus
            </when>
            <when test="(areaShelfStatus != null and areaShelfStatus == 0) or (supplierShelfStatus != null and supplierShelfStatus == 0)">
                IFNULL(MIN(area.min_shelf_status), 0)  as areaMinShelfStatus,
                IFNULL(MIN(area.mid_shelf_status), 0)  as areaMidShelfStatus,
                IFNULL(MIN(area.large_shelf_status ), 0)  as areaLargeShelfStatus,
                IFNULL(MIN(supplier.min_shelf_status ), 0)  as supplierMinShelfStatus,
                IFNULL(MIN(supplier.mid_shelf_status ), 0)  as supplierMidShelfStatus,
                IFNULL(MIN(supplier.large_shelf_status ), 0)  as supplierLargeShelfStatus
            </when>
            <when test="(areaShelfStatus != null and areaShelfStatus == 1) or (supplierShelfStatus != null and supplierShelfStatus == 1)">
                IFNULL(MAX(area.min_shelf_status ), 0)  as areaMinShelfStatus,
                IFNULL(MAX(area.mid_shelf_status ), 0)  as areaMidShelfStatus,
                IFNULL(MAX(area.large_shelf_status ), 0)  as areaLargeShelfStatus,
                IFNULL(MAX(supplier.min_shelf_status ), 0)  as supplierMinShelfStatus,
                IFNULL(MAX(supplier.mid_shelf_status ), 0)  as supplierMidShelfStatus,
                IFNULL(MAX(supplier.large_shelf_status ), 0)  as supplierLargeShelfStatus
            </when>
        </choose>
        from prdt_sku sku
        left join prdt_area_item area on area.sku_id = sku.sku_id
        left join prdt_supplier_item supplier on supplier.sku_id = sku.sku_id
        <where>
            sku.is_delete = 0
            <if test="spuIdList != null and spuIdList.size > 0">
                and sku.spu_id in
                <foreach collection="spuIdList" item="spuId" separator="," open="(" close=")">
                    #{spuId}
                </foreach>
            </if>
        </where>
        <if test="areaShelfStatus != null ">
            group by sku.sku_id
        </if>
        <if test="areaShelfStatus == null">
            group by sku.sku_id,area.area_item_id,area.min_shelf_status,area.mid_shelf_status,area.large_shelf_status,supplier.min_shelf_status,supplier.mid_shelf_status,supplier.large_shelf_status
        </if>
        HAVING
        1=1
        <if test="areaShelfStatus != null and areaShelfStatus == 0">
            AND (areaMinShelfStatus = 0 and areaMidShelfStatus = 0 and areaLargeShelfStatus = 0)
        </if>
        <if test="areaShelfStatus != null and areaShelfStatus == 1">
            AND (areaMinShelfStatus = 1 or areaMidShelfStatus = 1 or areaLargeShelfStatus = 1)
        </if>
        <if test="supplierShelfStatus != null and supplierShelfStatus == 0">
            AND (supplierMinShelfStatus = 0 and supplierMidShelfStatus = 0 and supplierLargeShelfStatus = 0)
        </if>
        <if test="supplierShelfStatus != null and supplierShelfStatus == 1">
            AND (supplierMinShelfStatus = 1 or supplierMidShelfStatus = 1 or supplierLargeShelfStatus = 1)
        </if>
    </select>
    <select id="getAreaIdExistSpu" resultType="com.zksr.product.api.sku.dto.SkuDTO">
        SELECT * from
            prdt_sku prdtSku
        where
            (
                EXISTS (SELECT 1 from prdt_area_item areaItem where areaItem.sku_id = prdtSku.sku_id and areaItem.area_id = #{areaId} and areaItem.sys_code = #{sysCode})
                )
    </select>

    <select id="getExistShelfSku" resultType="com.zksr.product.api.sku.dto.SkuDTO">
        SELECT prdtSpu.* from
            prdt_spu prdtSpu
            left join prdt_sku prdtSku on prdtSpu.spu_id = prdtSku.spu_id
        where
            prdtSpu.spu_id = #{spuId}
            and (
                EXISTS (SELECT 1 from prdt_area_item areaItem where areaItem.sku_id = prdtSku.sku_id and areaItem.shelf_status = 1)
                    OR
                EXISTS (SELECT 1 from prdt_supplier_item supplier where supplier.sku_id = prdtSku.sku_id and supplier.shelf_status = 1)
                )
    </select>
    <select id="selectExistShelfSkuId" resultType="com.zksr.product.api.sku.dto.SkuDTO">
        SELECT prdtSku.* from
             prdt_sku prdtSku
        where
            prdtSku.sku_id = #{skuId}
          and (
                EXISTS (SELECT 1 from prdt_area_item areaItem where areaItem.sku_id = prdtSku.sku_id and areaItem.shelf_status = 1)
                OR
                EXISTS (SELECT 1 from prdt_supplier_item supplier where supplier.sku_id = prdtSku.sku_id and supplier.shelf_status = 1)
            )
    </select>
    <select id="getByBarcode" resultType="com.zksr.product.domain.PrdtSku">
        SELECT
            *
        FROM
            prdt_sku sku
                LEFT JOIN prdt_spu spu ON sku.spu_id = spu.spu_id
        where sku.barcode = #{barcode}
          and spu.spu_name = #{spuName}
          and spu.supplier_id = #{supplierId}
    </select>
</mapper>