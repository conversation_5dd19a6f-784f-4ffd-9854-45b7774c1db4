<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtPlatformSpuMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPageExt" resultType="com.zksr.product.domain.PrdtPlatformSpu">
        SELECT
            *
        FROM
            prdt_platform_spu pps
        WHERE 1=1
            <if test="reqVO.platformSpuId != null">
                AND pps.platform_spu_id = #{reqVO.platformSpuId}
            </if>
            <if test="reqVO.barcode != null">
                AND pps.platform_spu_id IN (SELECT platform_spu_id FROM prdt_platform_sku WHERE barcode LIKE CONCAT('%', #{reqVO.barcode}, '%'))
            </if>
            <if test="reqVO.sysCode != null">
                AND pps.sys_code = #{reqVO.sysCode}
            </if>
            <if test="reqVO.supplierId != null">
                AND pps.supplier_id = #{reqVO.supplierId}
            </if>
            <if test="reqVO.catgoryName != null">
                AND pps.catgory_name LIKE CONCAT('%', #{reqVO.catgoryName}, '%')
            </if>
            <if test="reqVO.brandName != null">
                AND pps.brand_name LIKE CONCAT('%', #{reqVO.brandName}, '%')
            </if>
            <if test="reqVO.spuNo != null">
                AND pps.spu_no LIKE CONCAT('%', #{reqVO.spuNo}, '%')
            </if>
            <if test="reqVO.spuName != null">
                AND pps.spu_name LIKE CONCAT('%', #{reqVO.spuName}, '%')
            </if>
            <if test="reqVO.thumb != null">
                AND pps.thumb = #{reqVO.thumb}
            </if>
            <if test="reqVO.thumbVideo != null">
                AND pps.thumb_video = #{reqVO.thumbVideo}
            </if>
            <if test="reqVO.images != null">
                AND pps.images = #{reqVO.images}
            </if>
            <if test="reqVO.details != null">
                AND pps.details = #{reqVO.details}
            </if>
            <if test="reqVO.isDelete != null">
                AND pps.is_delete = #{reqVO.isDelete}
            </if>
            <if test="reqVO.status != null">
                AND pps.status = #{reqVO.status}
            </if>
            <if test="reqVO.specName != null">
                AND pps.spec_name LIKE CONCAT('%', #{reqVO.specName}, '%')
            </if>
            <if test="reqVO.isSpecs != null">
                AND pps.is_specs = #{reqVO.isSpecs}
            </if>
            <if test="reqVO.minUnit != null">
                AND pps.min_unit = #{reqVO.minUnit}
            </if>
            <if test="reqVO.midUnit != null">
                AND pps.mid_unit = #{reqVO.midUnit}
            </if>
            <if test="reqVO.midSize != null">
                AND pps.mid_size = #{reqVO.midSize}
            </if>
            <if test="reqVO.largeUnit != null">
                AND pps.large_unit = #{reqVO.largeUnit}
            </if>
            <if test="reqVO.largeSize != null">
                AND pps.large_size = #{reqVO.largeSize}
            </if>
            <if test="reqVO.spuId != null">
                AND pps.spu_id = #{reqVO.spuId}
            </if>
            <if test="reqVO.copyTimes != null">
                AND pps.copy_times = #{reqVO.copyTimes}
            </if>
        ORDER BY
            pps.platform_spu_id DESC
    </select>

</mapper>