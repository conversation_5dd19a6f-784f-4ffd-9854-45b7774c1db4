<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtSkuPriceMapper">

    <select id="selectPageSkuPrice" resultType="com.zksr.product.controller.skuPrice.vo.PrdtSkuPricePageReqVO">
        SELECT
            price.sku_price_id,
            spu.supplier_id,
            spu.spu_no,
            spu.spu_name,
            brand.brand_name,
            catgory.catgory_name,
            sku.sku_id,
            sku.barcode,
            sku.unit,
            sku.properties,
            sku.cost_price,
            sku.mark_price,
            price.sale_price1,
            price.sale_price2,
            price.sale_price3,
            price.sale_price4,
            price.sale_price5,
            price.sale_price6,
            price.type,
            price.area_id,
            sku.mid_mark_price,
            sku.mid_cost_price,
            price.mid_sale_price1,
            price.mid_sale_price2,
            price.mid_sale_price3,
            price.mid_sale_price4,
            price.mid_sale_price5,
            price.mid_sale_price6,
            sku.large_mark_price,
            sku.large_cost_price,
            price.large_sale_price1,
            price.large_sale_price2,
            price.large_sale_price3,
            price.large_sale_price4,
            price.large_sale_price5,
            price.large_sale_price6,
            spu.min_unit,
            spu.mid_unit,
            spu.large_unit
        FROM
            prdt_sku_price price
            LEFT JOIN prdt_sku sku ON sku.sku_id = price.sku_id
            LEFT JOIN prdt_spu spu ON spu.spu_id = sku.spu_id
            LEFT JOIN prdt_brand brand ON brand.brand_id = spu.brand_id
            LEFT JOIN prdt_catgory catgory ON catgory.catgory_id = spu.catgory_id
        <where>
            price.deleted = 0
            and exists(select 1 from prdt_sku sku where sku.sku_id = price.sku_id and price.deleted = 0)
            <if test="reqVO.supplierId != null and reqVO.supplierId != '' ">
                and spu.supplier_id = #{reqVO.supplierId}
            </if>
            <if test="reqVO.brandId !=null and reqVO.brandId !='' ">
                and spu.brand_id = #{reqVO.brandId}
            </if>
            <if test="reqVO.catgoryId !=null and reqVO.catgoryId !='' ">
                and spu.catgory_id = #{reqVO.catgoryId}
            </if>
            <if test="reqVO.areaIdList != null and reqVO.areaIdList.size() > 0">
                AND price.area_id IN
                <foreach collection="reqVO.areaIdList" item="areaIdItem" separator="," open="(" close=")">
                    #{areaIdItem}
                </foreach>
            </if>
            <if test="reqVO.areaId !=null and reqVO.areaId !='' ">
                and price.area_id = #{reqVO.areaId}
            </if>
            <if test="reqVO.keyword !=null and reqVO.keyword !='' ">
                and (spu.spu_no like concat('%', #{reqVO.keyword}, '%')
                or spu.spu_name like concat('%', #{reqVO.keyword}, '%')
                or sku.barcode like concat('%', #{reqVO.keyword}, '%') )
            </if>
            <if test="reqVO.type !=null and reqVO.type !='' ">
                and price.type = #{reqVO.type}
            </if>
        </where>
        ORDER BY
            price.sku_price_id DESC,
            spu.spu_id DESC
    </select>


    <select id="selectPageSkuPriceByPricing" resultType="com.zksr.product.controller.skuPrice.vo.PrdtSkuPricePageReqVO">
        SELECT
           spu.supplier_id,
           spu.spu_no,
           spu.spu_name,
           brand.brand_name,
           catgory.catgory_name,
           sku.barcode,
           sku.unit,
           sku.properties,
           sku.cost_price,
           sku.mark_price,
           sku.mid_mark_price,
           sku.mid_cost_price,
           sku.large_mark_price,
           sku.large_cost_price,
           spu.min_unit,
           spu.mid_unit,
           spu.large_unit,
           sku.sku_id,
        <if test="reqVO.type == 0">
            price0.sku_price_id,
            price0.sale_price1,
            price0.sale_price2,
            price0.sale_price3,
            price0.sale_price4,
            price0.sale_price5,
            price0.sale_price6,
            price0.mid_sale_price1,
            price0.mid_sale_price2,
            price0.mid_sale_price3,
            price0.mid_sale_price4,
            price0.mid_sale_price5,
            price0.mid_sale_price6,
            price0.large_sale_price1,
            price0.large_sale_price2,
            price0.large_sale_price3,
            price0.large_sale_price4,
            price0.large_sale_price5,
            price0.large_sale_price6,
            CASE
            WHEN price0.type IS NULL THEN
            #{reqVO.type} else price0.type end type,
            CASE
            WHEN price0.area_id IS NULL THEN
            #{reqVO.areaId} else price0.area_id end area_id
        </if>
        <if test="reqVO.type == 1">
            price1.sku_price_id,
            price1.sale_price1,
            price1.sale_price2,
            price1.sale_price3,
            price1.sale_price4,
            price1.sale_price5,
            price1.sale_price6,
            price1.mid_sale_price1,
            price1.mid_sale_price2,
            price1.mid_sale_price3,
            price1.mid_sale_price4,
            price1.mid_sale_price5,
            price1.mid_sale_price6,
            price1.large_sale_price1,
            price1.large_sale_price2,
            price1.large_sale_price3,
            price1.large_sale_price4,
            price1.large_sale_price5,
            price1.large_sale_price6,
            CASE
            WHEN price1.type IS NULL THEN
            #{reqVO.type} else price1.type end type,
            CASE
            WHEN price1.area_id IS NULL THEN
            #{reqVO.areaId} else price1.area_id end area_id
        </if>
        FROM
            prdt_sku sku
            LEFT JOIN prdt_sku_price price0 ON price0.sku_id = sku.sku_id and price0.type = 0 AND price0.deleted = 0
            <if test="reqVO.areaId !=null">
                AND (price0.area_id = #{reqVO.areaId})
            </if>
            LEFT JOIN prdt_sku_price price1 ON price1.sku_id = sku.sku_id and price1.type = 1  AND price1.deleted = 0
            <if test="reqVO.areaId !=null">
                AND (price1.area_id = #{reqVO.areaId})
            </if>
            LEFT JOIN prdt_spu spu ON spu.spu_id = sku.spu_id
            LEFT JOIN prdt_brand brand ON brand.brand_id = spu.brand_id
            LEFT JOIN prdt_catgory catgory ON catgory.catgory_id = spu.catgory_id
        WHERE
            sku.`status` = 1
            AND spu.`status` = 1
            <if test="reqVO.supplierId != null ">
                and spu.supplier_id = #{reqVO.supplierId}
            </if>
            <if test="reqVO.brandId !=null">
                and spu.brand_id = #{reqVO.brandId}
            </if>
            <if test="reqVO.catgoryId !=null">
                and spu.catgory_id = #{reqVO.catgoryId}
            </if>
            <if test="reqVO.keyword !=null and reqVO.keyword !='' ">
                and (spu.spu_no like concat('%', #{reqVO.keyword}, '%')
                or spu.spu_name like concat('%', #{reqVO.keyword}, '%')
                or sku.barcode like concat('%', #{reqVO.keyword}, '%') )
            </if>
            <if test="reqVO.unitType !=null">
                <if test="reqVO.unitType == 'minUnit'">
                    and sku.cost_price is not NULL
                </if>
                <if test="reqVO.unitType == 'midUnit'">
                    and sku.mid_cost_price is not NULL
                    and spu.mid_size is not NULL
                </if>
                <if test="reqVO.unitType == 'largeUnit'">
                    and sku.large_cost_price is not NULL
                    and spu.large_size is not NULL
                </if>
            </if>
        ORDER BY
            sku.sku_id DESC
    </select>

    <update id="updateSkuPrice">
        update prdt_sku_price set deleted = #{deleted} where sku_id = #{skuId}
    </update>


</mapper>