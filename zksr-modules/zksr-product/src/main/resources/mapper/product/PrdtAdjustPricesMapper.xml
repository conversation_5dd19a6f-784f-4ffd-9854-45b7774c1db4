<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtAdjustPricesMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectListPage"
            parameterType="com.zksr.product.controller.adjustPrices.vo.PrdtAdjustPricesPageReqVO"
            resultType="com.zksr.product.domain.PrdtAdjustPrices">
        SELECT
            adjust_prices_id
            , supplier_id
            , dc_id
            , adjust_prices_no
            , create_by
            , create_time
            , approve_state
            , approve_by
            , approve_time
            , sku_num
            , memo
        FROM
            prdt_adjust_prices
        <where>
            <if test="null != supplierId">
                AND supplier_id = #{supplierId}
            </if>
            <if test="null != dcId">
                AND dc_id = #{dcId}
            </if>
            <if test="null != adjustPricesNo and adjustPricesNo != ''.toString()">
                AND adjust_prices_no LIKE  concat('%',#{adjustPricesNo},'%')
            </if>
            <if test="null != createBy and createBy != ''.toString()">
                AND create_by LIKE  concat('%',#{createBy},'%')
            </if>
            <if test="null != createStartTime and null != createEndTime">
                AND create_time BETWEEN concat(#{createStartTime},'00:00:00.000') AND concat(#{createEndTime},'23:59:59.999')
            </if>
            <if test="null != approveBy and approveBy != ''.toString()">
                AND approve_by LIKE  concat('%',#{approveBy},'%')
            </if>
            <if test="null != approveStartTime and null != approveEndTime">
                AND approve_time BETWEEN concat(#{approveStartTime},'00:00:00.000') AND concat(#{approveEndTime},'23:59:59.999')
            </if>
            <if test="null != approveState">
                AND approve_state = #{approveState}
            </if>
            <if test="null != spuIds and spuIds.size > 0">
                AND adjust_prices_id IN (
                    SELECT
                        adjust_prices_id
                    FROM
                        prdt_adjust_prices_dtl
                    WHERE
                        spu_id IN
                        <foreach collection="spuIds" item="spuId" open="(" separator="," close=")">
                            #{spuId}
                        </foreach>
                )
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>