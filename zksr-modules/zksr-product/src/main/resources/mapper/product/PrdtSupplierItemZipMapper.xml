<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtSupplierItemZipMapper">
    <update id="updatePrdtSupplierItemZip">
        UPDATE prdt_supplier_item_zip
        <set>
             <if test="endDate != null">end_date = #{endDate},</if>
        </set>
        WHERE sys_code = #{sysCode}
            and supplier_id = #{supplierId}
            and spu_id = #{spuId}
            and sku_id = #{skuId}
    </update>
    <select id="selectPrdtSupplierItemZip" resultType="com.zksr.product.domain.PrdtSupplierItemZip">
        SELECT
            *
        FROM prdt_supplier_item_zip
        WHERE
          supplier_id = #{supplierId}
          and spu_id = #{spuId}
          and sku_id = #{skuId}
        ORDER BY start_date DESC
        LIMIT 1
    </select>
</mapper>
