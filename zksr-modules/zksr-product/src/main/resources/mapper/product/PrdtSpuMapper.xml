<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.product.mapper.PrdtSpuMapper">

    <select id = "selectPageSpu"  resultType="com.zksr.product.api.spu.vo.PrdtSpuPageReqVO">
		SELECT
			spu.spu_id spuId,
			spu.supplier_id supplierId,
			spu.spu_no spuNo,
			spu.spu_name spuName,
			brand.brand_name brandName,
			catgory.catgory_name catgoryName,
			spu.status,
			spu.origin_place,
			spu.latest_date,
			spu.oldest_date,
			spu.spec_name,
			spu.memo,
		    sku.sku_id,                 <!-- 商品SKUID-->
			spu.expiration_date,		<!--保质期(天数)-->
			spu.mid_size, 				<!-- 中单位换算数量（换算成最小单位）-->
			spu.large_size, 			<!-- 大单位换算数量（换算成最小单位）-->
			spu.min_unit, 				<!-- 最小单位-数据字典（sys_prdt_unit）-->
			spu.mid_unit,   			<!-- 中单位-数据字典（sys_prdt_unit）-->
			spu.large_unit, 			<!-- 大单位-数据字典（sys_prdt_unit）-->
			sku.barcode, 				<!-- 产品条码，最小单位-->
			sku.mid_barcode, 			<!-- 产品条码，中单位-->
			sku.large_barcode, 			<!-- 产品条码，大单位-->
			sku.mark_price,				<!-- 标准价，最小单位-->
			sku.mid_mark_price, 		<!-- 标准价，中单位-->
			sku.large_mark_price, 		<!-- 标准价，大单位-->
			sku.suggest_price,			<!-- 建议零售价，最小单位-->
			sku.mid_suggest_price,		<!-- 建议零售价，中单位-->
			sku.large_suggest_price,	<!-- 建议零售价，大单位-->
			sku.cost_price, 			<!-- 成本(供货)价，最小单位-->
			sku.mid_cost_price, 		<!-- 成本(供货)价，中单位-->
			sku.large_cost_price, 		<!-- 成本(供货)价，大单位-->
			(CASE WHEN localReleaseState.sku_id IS NOT NULL AND localReleaseState.shelf_status = 1 THEN 1 ELSE IFNULL(globalReleaseState.shelf_status, 0) END) shelfStatus
		FROM
			prdt_spu spu
			LEFT JOIN prdt_brand brand ON brand.brand_id = spu.brand_id
			LEFT JOIN prdt_catgory catgory ON catgory.catgory_id = spu.catgory_id
			LEFT JOIN prdt_sku sku ON sku.spu_id = spu.spu_id
			LEFT JOIN (
				SELECT sku_id, MAX(shelf_status) shelf_status FROM prdt_area_item GROUP BY sku_id
			) localReleaseState ON localReleaseState.sku_id = sku.sku_id
			LEFT JOIN (
				SELECT sku_id, MAX(shelf_status) shelf_status FROM prdt_supplier_item GROUP BY sku_id
			) globalReleaseState ON globalReleaseState.sku_id = sku.sku_id
		WHERE
            spu.is_delete = 0 and sku.is_delete = 0
            <if test="reqVO.status !=null ">
				and spu.status = #{reqVO.status}
			</if>
			<if test="reqVO.supplierId !=null ">
				and spu.supplier_id = #{reqVO.supplierId}
			</if>
			<if test="reqVO.brandId !=null ">
				and spu.brand_id = #{reqVO.brandId}
			</if>
			<if test="reqVO.catgoryId !=null  ">
				and spu.catgory_id = #{reqVO.catgoryId}
			</if>
			<if test="reqVO.keyword !=null and reqVO.keyword !='' ">
				 and (spu.spu_no like concat('%', #{reqVO.keyword}, '%')
				 or spu.spu_name like concat('%', #{reqVO.keyword}, '%'))
			</if>
			<if test="reqVO.supplierIdList != null and reqVO.supplierIdList.size > 0">
				and spu.supplier_id IN
				<foreach collection="reqVO.supplierIdList" item="supplierId" open="(" separator="," close=")">
					#{supplierId}
				</foreach>
			</if>
			<if test="reqVO.spuName != null and reqVO.spuName != ''">
				AND spu.spu_name like concat('%', #{reqVO.spuName}, '%')
			</if>
			<if test="reqVO.spuNo != null and reqVO.spuNo != ''">
				AND spu.spu_no like concat('%', #{reqVO.spuNo}, '%')
			</if>
			<if test="reqVO.shelfStatus != null">
			    <!-- 本地未上架, 全国也未上架 -->
				<if test="reqVO.shelfStatus == 0">
					AND (localReleaseState.sku_id IS NULL OR localReleaseState.shelf_status = 0)
					AND (globalReleaseState.sku_id IS NULL OR globalReleaseState.shelf_status = 0)
				</if>
			    <!-- 本地或者全国有一个上架就行 -->
				<if test="reqVO.shelfStatus == 1">
					AND (
					    localReleaseState.shelf_status = 1
						OR globalReleaseState.shelf_status = 1
					)
				</if>
			</if>
		${reqVO.params.dataScope}
		ORDER BY spu.spu_id DESC
    </select>

	<select id = "selectPageSpuByNotItem"  resultType="com.zksr.product.api.spu.vo.PrdtSpuNotItemPageReqVo">
		SELECT
			spu.spu_id,
			sku.sku_id,
			spu.supplier_id,
			spu.spu_no,
			spu.spu_name,
			brand.brand_name,
			catgory.catgory_name,
			sku.STATUS,
			sku.is_delete,
			<if test = "reqVO.itemType != null and reqVO.itemType != '' " > ifnull( item.shelf_status, 0 ) shelfStatus,</if>
			<if test="reqVO.itemType != null and reqVO.itemType == 0 ">item.sale_class_id classId,</if>
			<if test="reqVO.itemType != null and reqVO.itemType == 1 ">item.area_class_id classId,</if>
			sku.barcode,
			sku.unit,
			sku.properties,
			sku.mark_price,
			sku.stock,
			sku.suggest_price,
			sku.reference_price,
			sku.reference_sale_price,
			spu.expiration_date,
			spu.min_unit,
			spu.mid_unit,
			spu.mid_size,
			spu.large_unit,
			spu.large_size,
			sku.mid_barcode,
			sku.mid_mark_price,
			sku.mid_cost_price,
			sku.mid_suggest_price,
			sku.mid_min_oq,
			sku.mid_jump_oq,
			sku.mid_max_oq,
			sku.large_barcode,
			sku.large_mark_price,
			sku.large_cost_price,
			sku.large_suggest_price,
			sku.large_min_oq,
			sku.large_jump_oq,
			sku.large_max_oq
		from
			prdt_sku sku
			INNER JOIN prdt_spu spu on spu.spu_id = sku.spu_id
			LEFT JOIN prdt_brand brand on brand.brand_id = spu.brand_id
			LEFT JOIN prdt_catgory catgory on catgory.catgory_id = spu.catgory_id
			<if test="reqVO.itemType != null and reqVO.itemType == 0 ">
				LEFT JOIN prdt_supplier_item item on item.sku_id = sku.sku_id
			</if>
			<if test="reqVO.itemType != null and reqVO.itemType == 1 ">
				LEFT JOIN prdt_area_item item on item.sku_id = sku.sku_id
			</if>
			<if test="reqVO.classId !=null">
				<if test="reqVO.itemType != null and reqVO.itemType == 0 ">
					AND item.sale_class_id = #{reqVO.classId}
				</if>
				<if test="reqVO.itemType != null and reqVO.itemType == 1 ">
					AND item.area_class_id = #{reqVO.classId}
				</if>
			</if>
		WHERE
			sku.status = 1
		  	AND spu.is_delete = 0 and sku.is_delete = 0
			AND spu.status = 1
		  	<!-- 查询未上架数据 -->
			<if test="reqVO.itemType != null and reqVO.itemType == 0 and reqVO.shelfStatus != null and reqVO.shelfStatus == 0">
				AND (item.supplier_item_id IS NULL OR item.shelf_status = 0 )
			</if>
			<if test="reqVO.itemType != null and reqVO.itemType == 1 and reqVO.shelfStatus != null and reqVO.shelfStatus == 0">
				AND (item.area_item_id IS NULL OR item.shelf_status = 0 )
			</if>
			<if test="reqVO.itemType != null and reqVO.itemType == 0 and reqVO.shelfStatus != null and reqVO.shelfStatus == 1">
				AND (item.supplier_item_id IS NOT NULL AND item.shelf_status = 1 )
			</if>
			<if test="reqVO.itemType != null and reqVO.itemType == 1 and reqVO.shelfStatus != null and reqVO.shelfStatus == 1">
				AND (item.area_item_id IS NOT NULL AND item.shelf_status = 1 )
			</if>

			<if test="reqVO.supplierId !=null ">
				and spu.supplier_id = #{reqVO.supplierId}
			</if>
			<if test="reqVO.brandId !=null">
				and spu.brand_id = #{reqVO.brandId}
			</if>
			<if test="reqVO.catgoryId !=null">
				and spu.catgory_id = #{reqVO.catgoryId}
			</if>
			<if test="reqVO.keyword !=null and reqVO.keyword !='' ">
				and (spu.spu_no like concat('%', #{reqVO.keyword}, '%')
				or spu.spu_name like concat('%', #{reqVO.keyword}, '%')
				or sku.barcode like concat('%', #{reqVO.keyword}, '%') )
			</if>
			<if test="reqVO.supplierIdList != null and reqVO.supplierIdList.size > 0">
				AND spu.supplier_id in
				<foreach collection="reqVO.supplierIdList" item="supplierId" open="(" separator="," close=")">
					#{supplierId}
				</foreach>
			</if>
			<if test="reqVO.supplierIds != null and reqVO.supplierIds.size > 0">
				AND spu.supplier_id in
				<foreach collection="reqVO.supplierIds" item="supplierId" open="(" separator="," close=")">
					#{supplierId}
				</foreach>
			</if>
			<if test="reqVO.stockMin != null">
				AND (sku.stock - sku.sale_qty - sku.synced_qty) &gt;= #{reqVO.stockMin}
			</if>
			<if test="reqVO.stockMax != null">
				AND (sku.stock - sku.sale_qty - sku.synced_qty) &lt;= #{reqVO.stockMax}
			</if>
	</select>

	<select id="getProductDropdown" resultType="com.zksr.product.controller.spu.vo.PrdtProductRespVO">
		SELECT
			psu.spu_id,
 			<!-- psk.sku_id, -->
			psu.spu_name,
			psu.spu_no
 			<!-- psk.barcode -->
		FROM
			prdt_spu psu
		<!-- INNER JOIN prdt_sku psk ON psu.spu_id = psk.spu_id -->
where
		psu.is_delete = 0
    <if test="null != pageReqVO.supplierIds and pageReqVO.supplierIds.size > 0 ">
       and psu.supplier_id in
       <foreach collection="pageReqVO.supplierIds" item="supplierId" open="(" separator="," close=")">
           #{supplierId}
       </foreach>
   </if>
   <if test="null != pageReqVO.product and pageReqVO.product != '' ">
       and (
               psu.spu_name like concat('%',#{pageReqVO.product},'%')
               or
               psu.spu_no like concat('%',#{pageReqVO.product},'%')
       <!--	or
               psk.barcode like concat('%',#{pageReqVO.product},'%') -->
					)
			</if>
	</select>
	<select id="selectPrdtSpuUniquePage"
			resultType="com.zksr.product.controller.spu.vo.PrdtSpuUniquePageRespVO">
		SELECT
			sp.spu_id AS spuId,
			sp.spu_no AS spuNo,
			sp.spu_name AS spuName,
			sp.expiration_date AS expirationDate,
			sp.oldest_date AS oldestDate,
			sp.latest_date AS latestDate,
			sp.thumb,
			pc1.catgory_name AS catgoryName1,
			pc2.catgory_name AS catgoryName2,
			pc3.catgory_name AS catgoryName3,
			brand.brand_name AS brandName,
			sp.min_unit AS minUnit,
			sp.mid_unit AS midUnit,
			sp.large_unit AS largeUnit,
			sp.supplier_id AS supplierId,
			sp.large_size AS largeSize,
			sp.mid_size AS midSize,
			sp.status,
			sp.share_flag AS shareFlag,
			sp.source AS source,
		    sp.source_no AS sourceNo,  <!-- 外部来源商品编号-->
		    sp.auxiliary_spu_no AS auxiliarySpuNo,   <!-- SPU辅助的商品编号-->
			sp.keywords AS keywords,
			sp.images,
			sp.pricing_way AS pricingWay <!-- 商品计价方式类型-->
		FROM
			prdt_spu sp
			LEFT JOIN prdt_catgory pc3 ON pc3.catgory_id = sp.catgory_id
			LEFT JOIN prdt_catgory pc2 ON pc3.pid = pc2.catgory_id
			LEFT JOIN prdt_catgory pc1 ON pc2.pid = pc1.catgory_id
			LEFT JOIN prdt_brand brand ON brand.brand_id = sp.brand_id
		WHERE
			sp.is_delete = 0
			<if test='status != null'>
				AND sp.status = #{status}
			</if>
			<if test='sysCode != null'>
				AND sp.sys_code = #{sysCode}
			</if>
			<if test='validStockQty != null'>
				AND sp.spu_id IN (
				SELECT spu_id FROM prdt_sku WHERE (stock - sale_qty - synced_qty) &lt; #{validStockQty}
				)
			</if>
			<if test='brandId != null'>
				AND sp.brand_id = #{brandId}
			</if>
			<if test='supplierId != null'>
				AND sp.supplier_id = #{supplierId}
			</if>
			<if test='catgoryId != null'>
				AND (sp.catgory_id = #{catgoryId} OR pc2.catgory_id = #{catgoryId} OR pc1.catgory_id = #{catgoryId})
			</if>
			<if test='spuNo != null and spuNo != ""'>
				AND sp.spu_no LIKE CONCAT('%', #{spuNo}, '%')
			</if>
			<if test='spuNoList != null and spuNoList.size > 0'>
				AND sp.spu_no IN
				<foreach collection="spuNoList" item="spuNo" open="(" separator="," close=")">
					#{spuNo}
				</foreach>
			</if>
		    <if test='sourceNo != null and sourceNo != ""'>
		    	AND sp.source_no LIKE CONCAT('%', #{sourceNo}, '%')
		    </if>
		    <if test='auxiliarySpuNo != null and auxiliarySpuNo != ""'>
		    	AND sp.auxiliary_spu_no LIKE CONCAT('%', #{auxiliarySpuNo}, '%')
		    </if>
			<if test='spuName != null and spuName != ""'>
				AND sp.spu_name LIKE CONCAT('%', #{spuName}, '%')
			</if>
			<if test='barcode != null and barcode != ""'>
				AND sp.spu_id IN (
				SELECT spu_id FROM prdt_sku WHERE prdt_sku.barcode LIKE CONCAT('%', #{barcode}, '%')
				)
			</if>
			<if test="areaShelfStatus !=null and areaShelfStatus == 0 ">
				AND NOT EXISTS ( SELECT 1 FROM prdt_area_item pai WHERE sp.spu_id = pai.spu_id and pai.shelf_status = 1 )
			</if>
			<if test="areaShelfStatus !=null and areaShelfStatus == 1 ">
				AND EXISTS ( SELECT 1 FROM prdt_area_item pai WHERE sp.spu_id = pai.spu_id and pai.shelf_status = 1 )
			</if>
			<if test="supplierShelfStatus !=null and supplierShelfStatus == 0 ">
				AND NOT EXISTS ( SELECT 1 FROM prdt_supplier_item pai WHERE sp.spu_id = pai.spu_id and pai.shelf_status = 1 )
			</if>
			<if test="supplierShelfStatus !=null and supplierShelfStatus == 1 ">
				AND EXISTS ( SELECT 1 FROM prdt_supplier_item pai WHERE sp.spu_id = pai.spu_id and pai.shelf_status = 1 )
			</if>
			<if test='keyword != null and keyword != ""'>
				AND (sp.spu_name LIKE CONCAT('%', #{keyword}, '%') OR sp.spu_no LIKE CONCAT('%', #{keyword}, '%')
				     OR sp.spu_id IN (
				         SELECT spu_id FROM prdt_sku WHERE prdt_sku.barcode LIKE CONCAT('%', #{keyword}, '%'))
				     )
			</if>
			<if test="supplierIdList != null and supplierIdList.size > 0">
				and sp.supplier_id IN
				<foreach collection="supplierIdList" item="supplierId" open="(" separator="," close=")">
					#{supplierId}
				</foreach>
			</if>
			<if test='hasMainImage != null'>
				<if test='hasMainImage == true'>
					AND sp.thumb IS NOT NULL AND sp.thumb != ''
				</if>
				<if test='hasMainImage == false'>
					AND (sp.thumb IS NULL OR sp.thumb = '')
				</if>
			</if>
			<if test='stockMin != null'>
				AND sp.spu_id IN (
				SELECT spu_id FROM prdt_sku WHERE (stock - sale_qty - synced_qty) &gt;= #{stockMin}
				)
			</if>
			<if test='stockMax != null'>
				AND sp.spu_id IN (
				SELECT spu_id FROM prdt_sku WHERE (stock - sale_qty - synced_qty) &lt;= #{stockMax}
				)
			</if>
			<if test='keywords != null and keywords != "" and hasKeywords == true'>
				AND sp.keywords LIKE CONCAT('%', #{keywords}, '%')
			</if>
			<if test='pricingWay != null'>
				AND sp.pricing_way = #{pricingWay}
			</if>
		ORDER BY
			<if test='keywords != null and keywords != ""'>
				CASE
				WHEN sp.keywords LIKE CONCAT('%', #{keywords}, '%') THEN 0
				ELSE 1
				END ASC,
				sp.spu_id DESC
			</if>
			<if test='keywords == null'>
				sp.spu_id DESC
			</if>
	</select>
	<select id="getBoundProductInfoByCategoryIdAndSysCode" resultType="com.zksr.product.domain.dto.BoundProductInfoDTO">
		SELECT
			spu_name AS productName,
			spu_no AS productNumber,
			supplier_id AS supplierId
		FROM
			prdt_spu prdtSpu
		WHERE
			catgory_id = #{categoryId} AND is_delete = 0
			<if test='sysCode != null'>
				AND sys_code = #{sysCode}
			</if>
			AND (
			EXISTS(SELECT 1 FROM prdt_area_item areaItem where areaItem.spu_id = prdtSpu.spu_id and areaItem.shelf_status = 1)
			or
			EXISTS(SELECT 1 FROM prdt_supplier_item supplierItem where supplierItem.spu_id = prdtSpu.spu_id and supplierItem.shelf_status = 1)
			)
	</select>

	<select id = "selectSpuExportList"  resultType="com.zksr.product.api.spu.dto.SpuExportDTO">
		SELECT
            spu.spu_id,
            spu.supplier_id,
            spu.spu_no,
            spu.spu_name,
            brand.brand_name,
            thirdCatgory.catgory_name AS thirdCatgoryName,
            thirdCatgory.catgory_id AS thirdCatgoryId,
            secondCatgory.catgory_name AS secondCatgoryName,
            firstCatgory.catgory_name AS firstCatgoryName,
            case spu.status
            when 1 then '启用'
            when 0 then '停用'
            else '启用' end
            status,
            sku.sku_id,                 <!-- 商品SKUID-->
            spu.mid_size, 				<!-- 中单位换算数量（换算成最小单位）-->
            spu.large_size, 			<!-- 大单位换算数量（换算成最小单位）-->
            spu.min_unit, 				<!-- 最小单位-数据字典（sys_prdt_unit）-->
            spu.mid_unit,   			<!-- 中单位-数据字典（sys_prdt_unit）-->
            spu.large_unit, 			<!-- 大单位-数据字典（sys_prdt_unit）-->
            sku.barcode, 				<!-- 产品条码，最小单位-->
            sku.mid_barcode, 			<!-- 产品条码，中单位-->
            sku.large_barcode, 			<!-- 产品条码，大单位-->
            sku.mark_price,				<!-- 标准价，最小单位-->
            sku.mid_mark_price, 		<!-- 标准价，中单位-->
            sku.large_mark_price, 		<!-- 标准价，大单位-->
            sku.cost_price, 			<!-- 成本(供货)价，最小单位-->
            sku.mid_cost_price, 		<!-- 成本(供货)价，中单位-->
            sku.large_cost_price, 		<!-- 成本(供货)价，大单位-->
            sku.stock,                  <!-- 库存-->
            sku.properties,              <!-- 规格信息-->
            spu.source_no AS sourceNo,  <!-- 外部来源商品编号-->
            spu.auxiliary_spu_no AS auxiliarySpuNo,   <!-- SPU辅助的商品编号 -->
			case sku.status
			when 1 then '启用'
			when 0 then '停用'
			else '启用' end
            skuStatus,
            sku.min_oq,					<!-- 起订-->
            sku.mid_min_oq,				<!-- 中单位起订-->
            sku.large_min_oq,			<!-- 大单位起订-->
            sku.jump_oq,				<!-- 起订倍数-->
            sku.mid_jump_oq,			<!-- 中单位起订倍数-->
            sku.large_jump_oq,			<!-- 大单位起订倍数-->
			sku.max_oq,					<!-- 限购-->
			sku.mid_max_oq,				<!-- 中单位限购-->
			sku.large_max_oq			<!-- 大单位限购-->
		FROM
		prdt_spu spu
		LEFT JOIN prdt_brand brand ON brand.brand_id = spu.brand_id
		LEFT JOIN prdt_catgory thirdCatgory ON spu.catgory_id = thirdCatgory.catgory_id
		LEFT JOIN prdt_catgory secondCatgory ON thirdCatgory.pid = secondCatgory.catgory_id
		LEFT JOIN prdt_catgory firstCatgory ON secondCatgory.pid = firstCatgory.catgory_id
		LEFT JOIN prdt_sku sku ON sku.spu_id = spu.spu_id
		WHERE
			spu.is_delete = 0 and sku.is_delete = 0
			<if test="reqVO.status !=null ">
				and spu.status = #{reqVO.status}
			</if>
			<if test='reqVO.sysCode != null'>
				AND spu.sys_code = #{reqVO.sysCode}
			</if>
			<if test='reqVO.validStockQty != null'>
				AND (sku.stock - sku.sale_qty - sku.synced_qty) &lt; #{reqVO.validStockQty}
			</if>
			<if test="reqVO.supplierId !=null ">
				and spu.supplier_id = #{reqVO.supplierId}
			</if>
			<if test="reqVO.brandId !=null ">
				and spu.brand_id = #{reqVO.brandId}
			</if>
			<if test="reqVO.catgoryId !=null  ">
				and spu.catgory_id = #{reqVO.catgoryId}
			</if>
			<if test="reqVO.keyword !=null and reqVO.keyword !='' ">
				and (spu.spu_no like concat('%', #{reqVO.keyword}, '%')
				or spu.spu_name like concat('%', #{reqVO.keyword}, '%'))
			</if>
			<if test='reqVO.sourceNo != null and reqVO.sourceNo != ""'>
				AND spu.source_no LIKE CONCAT('%', #{reqVO.sourceNo}, '%')
			</if>
			<if test='reqVO.auxiliarySpuNo != null and reqVO.auxiliarySpuNo != ""'>
				AND spu.auxiliary_spu_no LIKE CONCAT('%', #{reqVO.auxiliarySpuNo}, '%')
			</if>
			<if test="reqVO.supplierIdList != null and reqVO.supplierIdList.size > 0">
				and spu.supplier_id IN
				<foreach collection="reqVO.supplierIdList" item="supplierId" open="(" separator="," close=")">
					#{supplierId}
				</foreach>
			</if>
			<if test="reqVO.spuIdList != null and reqVO.spuIdList.size > 0">
				and spu.spu_id IN
				<foreach collection="reqVO.spuIdList" item="spu" open="(" separator="," close=")">
					#{spu}
				</foreach>
			</if>
			<if test='reqVO.hasMainImage != null'>
				<if test='reqVO.hasMainImage == true'>
					AND spu.thumb IS NOT NULL AND spu.thumb != ''
				</if>
				<if test='reqVO.hasMainImage == false'>
					AND (spu.thumb IS NULL OR spu.thumb = '')
				</if>
			</if>
			<if test='reqVO.stockMin != null'>
				AND (sku.stock - sku.sale_qty - sku.synced_qty) &gt;= #{reqVO.stockMin}
			</if>
			<if test='reqVO.stockMax != null'>
				AND (sku.stock - sku.sale_qty - sku.synced_qty) &lt;= #{reqVO.stockMax}
			</if>
			<if test="reqVO.shelfStatus !=null and reqVO.shelfStatus == 0 ">
				AND NOT EXISTS ( SELECT 1 FROM prdt_area_item pai WHERE spu.spu_id = pai.spu_id and pai.shelf_status = 1 )
			</if>
			<if test="reqVO.shelfStatus !=null and reqVO.shelfStatus == 1">
				AND EXISTS ( SELECT 1 FROM prdt_area_item pai WHERE spu.spu_id = pai.spu_id and pai.shelf_status = 1 )
			</if>
		    ${reqVO.params.dataScope}
		ORDER BY spu.spu_id DESC
	</select>
</mapper>
