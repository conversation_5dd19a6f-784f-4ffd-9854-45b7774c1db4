# Tomcat
server:
  port: 9601

# Spring
spring:
  application:
    # 应用名称
    name: zksr-gen
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: zksr9876
        namespace: zksr-cloud
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: zksr9876
        namespace: zksr-cloud
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-redis-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}



