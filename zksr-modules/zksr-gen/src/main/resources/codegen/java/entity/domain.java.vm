package ${packageName}.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
#foreach ($import in $importList)
import ${import};
#end
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@TableName(value = "${tableName}")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ${ClassName} extends BaseEntity {
    private static final long serialVersionUID=1L;

#foreach ($column in $columns)
    #if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
        #if($column.list)
            #set($parentheseIndex=$column.columnComment.indexOf("（"))
            #if($parentheseIndex != -1)
                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
            #else
                #set($comment=$column.columnComment)
            #end
            #if($parentheseIndex != -1)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
            #elseif($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
            #else
    @Excel(name = "${comment}")
            #end
        #end
        #if ('1' == ${column.isPk})##处理主键
    @TableId#if (${column.javaType} == 'String')(type = IdType.INPUT)#end
        #end
    #if($column.columnType == "bigint(20)" || $column.columnType == "int(20)")
    @JsonSerialize(using = CustomLongSerialize.class)
    #end
    private $column.javaType $column.javaField;

    #end
#end
}
