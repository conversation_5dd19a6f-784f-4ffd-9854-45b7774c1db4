package ${packageName}.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
#foreach ($column in $columns)
#if($column.javaField == 'createTime' || $column.javaField == 'updateTime')
import com.zksr.common.core.utils.DateUtils;
#break
#end
#end
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
#if($table.sub)
import java.util.ArrayList;
import org.springframework.transaction.annotation.Transactional;
import ${packageName}.domain.${subClassName};
#end
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.convert.${businessName}.${ClassName}Convert;
import ${packageName}.domain.${ClassName};
import ${packageName}.controller.${businessName}.vo.${ClassName}PageReqVO;
import ${packageName}.controller.${businessName}.vo.${ClassName}SaveReqVO;
import ${packageName}.service.I${ClassName}Service;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static ${packageName}.enums.ErrorCodeConstants.*;

/**
 * ${functionName}Service业务层处理
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
public class ${ClassName}ServiceImpl implements I${ClassName}Service {
    @Autowired
    private ${ClassName}Mapper ${className}Mapper;

    /**
     * 新增${functionName}
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insert${ClassName}(${ClassName}SaveReqVO createReqVO) {
        // 插入
        ${ClassName} ${className} = ${ClassName}Convert.INSTANCE.convert(createReqVO);
        ${className}Mapper.insert(${className});
        // 返回
        return ${className}.get${pkColumn.capJavaField}();
    }

    /**
     * 修改${functionName}
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void update${ClassName}(${ClassName}SaveReqVO updateReqVO) {
        ${className}Mapper.updateById(${ClassName}Convert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除${functionName}
     *
     * @param ${pkColumn.javaField} ${pkColumn.columnComment}
     */
    @Override
    public void delete${ClassName}(Long ${pkColumn.javaField}) {
        // 删除
        ${className}Mapper.deleteById(${pkColumn.javaField});
    }

    /**
     * 批量删除${functionName}
     *
     * @param ${pkColumn.javaField}s 需要删除的${functionName}主键
     * @return 结果
     */
    @Override
    public void delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        for(Long ${pkColumn.javaField} : ${pkColumn.javaField}s){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.delete${ClassName}(${pkColumn.javaField});
        }
    }

    /**
     * 获得${functionName}
     *
     * @param ${pkColumn.javaField} ${pkColumn.columnComment}
     * @return ${functionName}
     */
    @Override
    public ${ClassName} get${ClassName}(Long ${pkColumn.javaField}) {
        return ${className}Mapper.selectById(${pkColumn.javaField});
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<${ClassName}> get${ClassName}Page(${ClassName}PageReqVO pageReqVO) {
        return ${className}Mapper.selectPage(pageReqVO);
    }

    private void validate${ClassName}Exists(Long ${pkColumn.javaField}) {
        if (${className}Mapper.selectById(${pkColumn.javaField}) == null) {
            throw exception(${tableName.toUpperCase()}_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 ${packageName}.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== ${functionName} TODO 补充编号 ==========
    // ErrorCode ${tableName.toUpperCase()}_NOT_EXISTS = new ErrorCode(TODO 补充编号, "${functionName}不存在");


}
