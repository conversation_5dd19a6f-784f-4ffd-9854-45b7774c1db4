package ${packageName}.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import ${packageName}.domain.${ClassName};
import ${packageName}.controller.${businessName}.vo.${ClassName}PageReqVO;
import ${packageName}.controller.${businessName}.vo.${ClassName}SaveReqVO;

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service {

    /**
     * 新增${functionName}
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insert${ClassName}(@Valid ${ClassName}SaveReqVO createReqVO);

    /**
     * 修改${functionName}
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void update${ClassName}(@Valid ${ClassName}SaveReqVO updateReqVO);

    /**
     * 删除${functionName}
     *
     * @param ${pkColumn.javaField} ${pkColumn.columnComment}
     */
    public void delete${ClassName}(Long ${pkColumn.javaField});

    /**
     * 批量删除${functionName}
     *
     * @param ${pkColumn.javaField}s 需要删除的${functionName}主键集合
     * @return 结果
     */
    public void delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaType}[] ${pkColumn.javaField}s);

    /**
     * 获得${functionName}
     *
     * @param ${pkColumn.javaField} ${pkColumn.columnComment}
     * @return ${functionName}
     */
    public ${ClassName} get${ClassName}(Long ${pkColumn.javaField});

    /**
     * 获得${functionName}分页
     *
     * @param pageReqVO 分页查询
     * @return ${functionName}分页
     */
    PageResult<${ClassName}> get${ClassName}Page(${ClassName}PageReqVO pageReqVO);

}
