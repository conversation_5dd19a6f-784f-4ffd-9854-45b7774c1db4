package ${packageName}.controller.${businessName};

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import ${packageName}.controller.${businessName}.vo.${ClassName}PageReqVO;
import ${packageName}.controller.${businessName}.vo.${ClassName}SaveReqVO;
import ${packageName}.controller.${businessName}.vo.${ClassName}RespVO;
import ${packageName}.convert.${businessName}.${ClassName}Convert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Api(tags = "管理后台 - ${functionName}接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/${businessName}")
public class ${ClassName}Controller {
    @Autowired
    private I${ClassName}Service ${className}Service;

    /**
     * 新增${functionName}
     */
    @ApiOperation(value = "新增${functionName}", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody ${ClassName}SaveReqVO createReqVO) {
        return success(${className}Service.insert${ClassName}(createReqVO));
    }

    /**
     * 修改${functionName}
     */
    @ApiOperation(value = "修改${functionName}", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody ${ClassName}SaveReqVO updateReqVO) {
            ${className}Service.update${ClassName}(updateReqVO);
        return success(true);
    }

    /**
     * 删除${functionName}
     */
    @ApiOperation(value = "删除${functionName}", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @DeleteMapping("/{${pkColumn.javaField}s}")
    public CommonResult<Boolean> remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        ${className}Service.delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaField}s);
        return success(true);
    }

    /**
     * 获取${functionName}详细信息
     */
    @ApiOperation(value = "获得${functionName}详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{${pkColumn.javaField}}")
    public CommonResult<${ClassName}RespVO> getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        ${ClassName} ${className} = ${className}Service.get${ClassName}(${pkColumn.javaField});
        return success(${ClassName}Convert.INSTANCE.convert(${className}));
    }

    /**
     * 分页查询${functionName}
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得${functionName}分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<${ClassName}RespVO>> getPage(@Valid ${ClassName}PageReqVO pageReqVO) {
        PageResult<${table.className}> pageResult = ${className}Service.get${ClassName}Page(pageReqVO);
        return success(${ClassName}Convert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "${permissionPrefix}:add";
        /** 编辑 */
        public static final String EDIT = "${permissionPrefix}:edit";
        /** 删除 */
        public static final String DELETE = "${permissionPrefix}:remove";
        /** 列表 */
        public static final String LIST = "${permissionPrefix}:list";
        /** 查询 */
        public static final String GET = "${permissionPrefix}:query";
        /** 停用 */
        public static final String DISABLE = "${permissionPrefix}:disable";
        /** 启用 */
        public static final String ENABLE = "${permissionPrefix}:enable";
    }
}
