package ${packageName}.controller.${businessName}.vo;

import lombok.*;
#foreach ($import in $importList)
import ${import};
#end
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@ApiModel("${functionName} - ${tableName} Response VO")
public class ${ClassName}RespVO {
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($parentheseIndex != -1)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
#elseif($column.javaType == 'Date')
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "${comment}", width = 30, dateFormat = YYYY_MM_DD)
#else
    @Excel(name = "${comment}")
#end
#end
    @ApiModelProperty(value = "${comment}")
    #if($column.columnType == "bigint(20)" || $column.columnType == "int(20)")
    @JsonSerialize(using = CustomLongSerialize.class)
    #end
    private $column.javaType $column.javaField;

#end
#end
}
