package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import com.zksr.common.core.web.pojo.PageResult;
import ${packageName}.domain.${ClassName};
import ${packageName}.controller.${businessName}.vo.${ClassName}RespVO;
import ${packageName}.controller.${businessName}.vo.${ClassName}SaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* ${functionName} 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date ${datetime}
*/
@Mapper
public interface ${ClassName}Convert {

    ${ClassName}Convert INSTANCE = Mappers.getMapper(${ClassName}Convert.class);

    ${ClassName}RespVO convert(${ClassName} ${className});

    ${ClassName} convert(${ClassName}SaveReqVO ${className}SaveReq);

    PageResult<${ClassName}RespVO> convertPage(PageResult<${ClassName}> ${className}Page);
}