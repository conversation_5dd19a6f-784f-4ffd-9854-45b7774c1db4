package ${packageName}.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import ${packageName}.domain.${ClassName};
import ${packageName}.controller.${businessName}.vo.${ClassName}PageReqVO;
#if($table.sub)
import ${packageName}.domain.${subClassName};
#end

## 字段模板
#macro(listCondition)
    #foreach ($column in $columns)
        #if (${column.queryType} != $null)
            #set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
            #if (${column.queryType} == "EQ")##情况一，= 的时候
                    .eqIfPresent(${ClassName}::get${JavaField}, reqVO.get${JavaField}())
            #end
            #if (${column.queryType} == "NE")##情况二，!= 的时候
                    .neIfPresent(${ClassName}::get${JavaField}, reqVO.get${JavaField}())
            #end
            #if (${column.queryType} == "GT")##情况三，> 的时候
                    .gtIfPresent(${ClassName}::get${JavaField}, reqVO.get${JavaField}())
            #end
            #if (${column.queryType} == "LT")##情况五，< 的时候
                    .ltIfPresent(${ClassName}::get${JavaField}, reqVO.get${JavaField}())
            #end
            #if (${column.queryType} == "LIKE")##情况七，Like 的时候
                    .likeIfPresent(${ClassName}::get${JavaField}, reqVO.get${JavaField}())
            #end
            #if (${column.queryType} == "BETWEEN")##情况八，Between 的时候
                    .betweenIfPresent(${ClassName}::get${JavaField}, reqVO.get${JavaField}())
            #end
        #end
    #end
#end

/**
 * ${functionName}Mapper接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Mapper
public interface ${ClassName}Mapper extends BaseMapperX<${ClassName}> {
    default PageResult<${ClassName}> selectPage(${ClassName}PageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<${ClassName}>()
            #listCondition()
                .orderByDesc(${ClassName}::get${pkColumn.capJavaField}));## 大多数情况下，id 倒序

    }
}
