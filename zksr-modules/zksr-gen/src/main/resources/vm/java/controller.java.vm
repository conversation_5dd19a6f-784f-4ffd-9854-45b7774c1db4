package ${packageName}.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import com.zksr.common.core.web.controller.BaseController;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.core.web.domain.AjaxResultBase;
import com.zksr.common.core.utils.poi.ExcelUtil;

import javax.validation.Valid;
import com.zksr.common.core.web.page.TableDataInfo;

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RestController
@RequestMapping("/${businessName}")
@Api(tags = "${functionName}接口", produces = "application/json")
@Validated
public class ${ClassName}Controller extends BaseController {
    @Autowired
    private I${ClassName}Service ${className}Service;

    /**
     * 查询${functionName}列表
     */
    @ApiOperation(value = "查询${functionName}列表", httpMethod = "GET", notes = "权限字符: ${permissionPrefix}")
    @RequiresPermissions("${permissionPrefix}:list")
    @GetMapping("/list")
    public TableDataInfo<${ClassName}> list(${ClassName} ${className}) {
        startPage();
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return getDataTable(list);
    }

    /**
     * 导出${functionName}列表
     */
    @ApiOperation(value = "导出${functionName}列表", httpMethod = "POST", notes = "权限字符: ${permissionPrefix}")
    @RequiresPermissions("${permissionPrefix}:export")
    @Log(title = "导出${functionName}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ${ClassName} ${className}) {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        ExcelUtil<${ClassName}> util = new ExcelUtil<${ClassName}>(${ClassName}.class);
        util.exportExcel(response, list, "${functionName}数据");
    }

    /**
     * 获取${functionName}详细信息
     */
    @ApiOperation(value = "获取${functionName}详细信息", httpMethod = "GET", notes = "权限字符: ${permissionPrefix}")
    @RequiresPermissions("${permissionPrefix}:query")
    @GetMapping(value = "/{${pkColumn.javaField}}")
    public AjaxResultBase<${ClassName}> getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return successBase(${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @ApiOperation(value = "新增${functionName}", httpMethod = "POST", notes = "权限字符: ${permissionPrefix}")
    @RequiresPermissions("${permissionPrefix}:add")
    @Log(title = "新增${functionName}", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResultBase<Long> add(@Valid @RequestBody ${ClassName} ${className}) {
        return successBase(${className}Service.insert${ClassName}(${className}));
    }

    /**
     * 修改${functionName}
     */
    @ApiOperation(value = "修改${functionName}", httpMethod = "PUT", notes = "权限字符: ${permissionPrefix}")
    @RequiresPermissions("${permissionPrefix}:edit")
    @Log(title = "修改${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody ${ClassName} ${className}) {
        return toAjax(${className}Service.update${ClassName}(${className}));
    }

    /**
     * 删除${functionName}
     */
    @ApiOperation(value = "删除${functionName}", httpMethod = "DELETE", notes = "权限字符: ${permissionPrefix}")
    @RequiresPermissions("${permissionPrefix}:remove")
    @Log(title = "删除${functionName}", businessType = BusinessType.DELETE)
	@DeleteMapping("/{${pkColumn.javaField}s}")
    public AjaxResult remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return toAjax(${className}Service.delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaField}s));
    }
}
