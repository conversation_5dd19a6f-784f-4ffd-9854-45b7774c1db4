# Tomcat
server:
  port: 8200

# Spring
spring:
  application:
    # 应用名称
    name: zksr-demo
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: zksr9876
        namespace: zksr-cloud
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: zksr9876
        namespace: zksr-cloud
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          # - application-rocketmq-demo-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-redis-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

    #spring-cloud-stream 配置
    stream:
      function:
        # spring-cloud-stream规范 函数式编程定义的BEAN name（这里只定义了消费者入口）
        definition: "mytopic;\
                     mytopic2;\
                     mytopicConcurrency;\
                     mytopicDelay;\
                     mytopicOrderly;\
                     myObjectTopic;\
                     mytopicDlqListener\
                     "

      rocketmq:
        binder:
          name-server: 127.0.0.1:9876
        bindings:
          mytopicOrderly-out-0:
            producer:
              group: mytopicOrderly_output_group
              messageQueueSelector: orderlyMessageQueueSelector
          mytopicOrderly-in-0:
            consumer:
              subscription: 'TagA || TagC || TagD'
              push:
                orderly: true

          mytopic2-out-0:
            producer:
              group: mytopic2_luoxiang

          mytopic-in-0:
            consumer:
              push:
                delayLevelWhenNextConsume: -1


      bindings:
        # Binding的名称规范。Binding的命名是：<functionName>-in/out-<index>。  out-消息流出-消息生产者  in-消息流入-消息消费者
        # producer是函数名（也是Bean名） out代表这个Binding是向外写出的 而index是输入或输出绑定的索引。对于典型的单个输入/输出函数，它始终为 0
        mytopic-out-0:
          destination: mytopic
        mytopic-in-0:
          destination: mytopic
          group: mytopic_input_group
          consumer:
            concurrency: 3
        mytopicDlqListener-in-0:
          destination: "%DLQ%mytopic_input_group"
          group: mytopicdlq_input_group

        mytopic2-out-0:
          destination: mytopic2
        mytopic2-in-0:
          destination: mytopic2
          group: mytopic2_input_group
        mytopic2bak-in-0:
          destination: mytopic2
          group: mytopic2bak_input_group

        mytopicConcurrency-out-0:
          destination: mytopicConcurrency
        mytopicConcurrency-in-0:
          destination: mytopicConcurrency
          group: mytopicConcurrency_input_group
          consumer:
            concurrency: 3 # 并发消费

        mytopicDelay-out-0:
          destination: mytopicDelay
        mytopicDelay-in-0:
          destination: mytopicDelay
          group: mytopicDelay_input_group

        mytopicOrderly-out-0:
          destination: mytopicOrderly
        mytopicOrderly-in-0:
          destination: mytopicOrderly
          group: mytopicOrderly_input_group

        myObjectTopic-out-0:
          destination: myObjectTopic
        myObjectTopic-in-0:
          destination: myObjectTopic
          group: myObjectTopic_input_group
# knife4j公共配置
knife4j:
  enable: true

