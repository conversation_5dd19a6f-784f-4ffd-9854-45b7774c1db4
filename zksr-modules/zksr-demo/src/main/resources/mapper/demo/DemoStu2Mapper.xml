<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.demo.mapper.DemoStu2Mapper">

    <resultMap type="com.zksr.demo.domain.DemoStu2" id="DemoStu2Result">
        <result property="stuId"    column="stu_id"    />
        <result property="sysCode"    column="sys_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="stuName"    column="stu_name"    />
        <result property="phone"    column="phone"    />
        <result property="stuDate"    column="stu_date"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectDemoStu2Vo">
        select stu_id, sys_code, create_by, create_time, update_by, update_time, stu_name, phone, stu_date, status from demo_stu2
    </sql>

    <select id="selectDemoStu2List" parameterType="com.zksr.demo.domain.DemoStu2" resultMap="DemoStu2Result">
        <include refid="selectDemoStu2Vo"/>
        <where>
            <if test="sysCode != null "> and sys_code = #{sysCode}</if>
            <if test="stuName != null  and stuName != ''"> and stu_name like concat('%', #{stuName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="stuDate != null "> and stu_date = #{stuDate}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectDemoStu2ByStuId" parameterType="Long" resultMap="DemoStu2Result">
        <include refid="selectDemoStu2Vo"/>
        where stu_id = #{stuId}
    </select>

    <insert id="insertDemoStu2" parameterType="com.zksr.demo.domain.DemoStu2">
        insert into demo_stu2
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stuId != null">stu_id,</if>
            <if test="sysCode != null">sys_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="stuName != null">stu_name,</if>
            <if test="phone != null">phone,</if>
            <if test="stuDate != null">stu_date,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stuId != null">#{stuId},</if>
            <if test="sysCode != null">#{sysCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="stuName != null">#{stuName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="stuDate != null">#{stuDate},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateDemoStu2" parameterType="com.zksr.demo.domain.DemoStu2">
        update demo_stu2
        <trim prefix="SET" suffixOverrides=",">
            <if test="sysCode != null">sys_code = #{sysCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="stuName != null">stu_name = #{stuName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="stuDate != null">stu_date = #{stuDate},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where stu_id = #{stuId}
    </update>

    <delete id="deleteDemoStu2ByStuId" parameterType="Long">
        delete from demo_stu2 where stu_id = #{stuId}
    </delete>

    <delete id="deleteDemoStu2ByStuIds" parameterType="String">
        delete from demo_stu2 where stu_id in
        <foreach item="stuId" collection="array" open="(" separator="," close=")">
            #{stuId}
        </foreach>
    </delete>
</mapper>
