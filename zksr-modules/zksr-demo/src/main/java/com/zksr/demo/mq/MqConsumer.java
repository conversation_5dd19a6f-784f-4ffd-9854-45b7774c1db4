package com.zksr.demo.mq;

import com.alibaba.cloud.stream.binder.rocketmq.support.RocketMQMessageConverterSupport;
import com.alibaba.fastjson.JSON;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.common.rocketmq.dto.MqUserDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;

import java.util.function.Consumer;


@Configuration
@Slf4j
public class MqConsumer {

    @Bean
    public Consumer<Message<String>> mytopic() {
        return (data) -> {
            String payload = data.getPayload();
            MessageHeaders headers = data.getHeaders();
            Object headerFor = headers.get("for");
            int i = 10/0;
            log.info(MessageConstant.MYTOPIC_TOPIC +"接收一条消息：" + payload);
            //log.info("getHeaders headerFor：" + headerFor);
        };
    }

    @Bean
    public Consumer<Message<String>> mytopic2() {
        return (data) -> {
            String payload = data.getPayload();
            MessageHeaders headers = data.getHeaders();
            Object headerFor = headers.get("for");
            log.info(MessageConstant.MYTOPIC2_TOPIC +"接收一条消息：" + payload);
            log.info("getHeaders headerFor：" + headerFor);
        };
    }

    @Bean
    public Consumer<Message<String>> mytopicConcurrency() {
        return (data) -> {
            String payload = data.getPayload();
            MessageHeaders headers = data.getHeaders();
            Object headerFor = headers.get("for");
            log.info(MessageConstant.MYTOPICCONCURRENCY_TOPIC +"接收一条消息：" + payload);
            log.info("getHeaders headerFor：" + headerFor);
        };
    }

    @Bean
    public Consumer<Message<String>> mytopicDelay() {
        return (data) -> {
            String payload = data.getPayload();
            MessageHeaders headers = data.getHeaders();
            Object headerFor = headers.get("for");
            Object delayLevel = headers.get(MessageConst.PROPERTY_DELAY_TIME_LEVEL);
            log.info(MessageConstant.MYTOPICDELAY_TOPIC +"接收一条消息：" + payload);
            log.info("getHeaders headerFor：" + headerFor);
            log.info("getHeaders delayLevel：" + delayLevel);
        };
    }

    @Bean
    public Consumer<Message<String>> mytopicOrderly() {
        return (data) -> {
            String payload = data.getPayload();
            MessageHeaders headers = data.getHeaders();
            String tagHeaderKey = RocketMQMessageConverterSupport.toRocketHeaderKey(
                    MessageConst.PROPERTY_TAGS).toString();
            log.info(MessageConstant.MYTOPICORDERLY_TOPIC + " 接收一条消息: " + payload + " TAG:" +
                    data.getHeaders().get(tagHeaderKey).toString());
            //log.info("getHeaders headerFor：" + headerFor);
            try {
                Thread.sleep(100);
            }
            catch (InterruptedException ignored) {
            }
        };
    }

    @Bean
    public Consumer<Message<MqUserDto>> myObjectTopic() {
        return (data) -> {
            MqUserDto user = data.getPayload();
            MessageHeaders headers = data.getHeaders();
            Object headerFor = headers.get("for");

            String userJson = JSON.toJSONString(user);
            log.info(MessageConstant.MYOBJECTTOPIC_TOPIC +"接收一条消息："+ userJson);
            log.info("getHeaders headerFor：" + headerFor);
        };
    }

    @Bean
    public Consumer<Message<String>> mytopicDlqListener() {
        return (data) -> {
            String payload = data.getPayload();
            MessageHeaders headers = data.getHeaders();
            Object headerFor = headers.get("for");
            //int i = 10/0;
            log.info("com.zksr.demo.mq.MqConsumer.mytopicDlqListener接收一条消息：" + payload);
            //log.info("getHeaders headerFor：" + headerFor);
        };
    }

}
