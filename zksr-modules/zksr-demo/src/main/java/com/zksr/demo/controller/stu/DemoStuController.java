package com.zksr.demo.controller.stu;

import javax.validation.Valid;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.demo.controller.stu.vo.StuPageReqVO;
import com.zksr.demo.controller.stu.vo.StuRespVO;
import com.zksr.demo.controller.stu.vo.StuSaveReqVo;
import com.zksr.demo.convert.stu.DemoStuConvert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.demo.domain.DemoStu;
import com.zksr.demo.service.IDemoStuService;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 学生信息管理Controller
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/stu")
@Api(tags = "学生信息管理接口", produces = "application/json")
public class DemoStuController
{
    @Autowired
    private IDemoStuService demoStuService;

//    /**
//     * 查询学生信息管理列表
//     */
//    @ApiOperation(value = "查询学生信息管理列表", httpMethod = "GET")
//    @RequiresPermissions("demo:stu:list")
//    @GetMapping("/list")
//    public TableDataInfo list(DemoStu demoStu) {
//        startPage();
//        List<DemoStu> list = demoStuService.selectDemoStuList(demoStu);
//        return getDataTable(list);
//    }


//    /**
//     * 导出学生信息管理列表
//     */
//    @ApiOperation(value = "导出学生信息管理列表", httpMethod = "POST")
//    @RequiresPermissions("demo:stu:export")
//    @Log(title = "学生信息管理", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, DemoStu demoStu)
//    {
//        List<DemoStu> list = demoStuService.selectDemoStuList(demoStu);
//        ExcelUtil<DemoStu> util = new ExcelUtil<DemoStu>(DemoStu.class);
//        util.exportExcel(response, list, "学生信息管理数据");
//    }
//
//    /**
//     * 获取学生信息管理详细信息
//     */
//    @ApiOperation(value = "获取学生信息管理详细信息", httpMethod = "GET")
//    @RequiresPermissions("demo:stu:query")
//    @GetMapping(value = "/{stuId}")
//    public AjaxResult getInfo(@PathVariable("stuId") Long stuId)
//    {
//        return success(demoStuService.selectDemoStuByStuId(stuId));
//    }
//
//    /**
//     * 新增学生信息管理
//     */
//    @ApiOperation(value = "新增学生信息管理", httpMethod = "POST")
//    @RequiresPermissions("demo:stu:add")
//    @Log(title = "学生信息管理", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody DemoStu demoStu)
//    {
//        return toAjax(demoStuService.insertDemoStu(demoStu));
//    }
//
//    /**
//     * 修改学生信息管理
//     */
//    @ApiOperation(value = "新增学生信息管理", httpMethod = "POST")
//    @RequiresPermissions("demo:stu:edit")
//    @Log(title = "学生信息管理", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody DemoStu demoStu)
//    {
//        return toAjax(demoStuService.updateDemoStu(demoStu));
//    }
//
//    /**
//     * 删除学生信息管理
//     */
//    @ApiOperation(value = "删除学生信息管理", httpMethod = "DELETE")
//    @RequiresPermissions("demo:stu:remove")
//    @Log(title = "学生信息管理", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{stuIds}")
//    public AjaxResult remove(@PathVariable Long[] stuIds)
//    {
//        return toAjax(demoStuService.deleteDemoStuByStuIds(stuIds));
//    }

    /**
     * 查询学生信息管理列表
     */
    @ApiOperation(value = "查询学生信息管理列表", httpMethod = "GET")
    @RequiresPermissions("demo:stu:list")
    @GetMapping("/list")
    public CommonResult<PageResult<StuRespVO>> list(StuPageReqVO pageVO) {
//        PageResult<DemoStu> pageResult = demoStuService.getStuPage(pageVO);
//        return success(DemoStuConvert.INSTANCE.convertPage(pageResult));
        return null;
    }

    /**
     * 新增学生信息管理
     */
    @PostMapping
    @ApiOperation(value = "新增学生信息管理", httpMethod = "POST")
    @RequiresPermissions("demo:stu:add")
    public CommonResult<Long> createDemoStu(@Valid @RequestBody StuSaveReqVo createReqVO) {
        return success(demoStuService.createDemoStu(createReqVO));
    }

    /**
     * 修改学生信息管理
     */
    @ApiOperation(value = "修改学生信息管理", httpMethod = "PUT")
    @RequiresPermissions("demo:stu:edit")
    @Log(title = "学生信息管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> updateDemoStu(@Valid @RequestBody StuSaveReqVo updateReqVO) {
        demoStuService.updateDemoStu(updateReqVO);
        return success(true);
    }


//    @DeleteMapping("/delete")
//    @Operation(summary = "删除学生课程")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('infra:demo03-course:delete')")

    /**
     * 删除学生信息管理
     */
    @ApiOperation(value = "删除学生信息管理", httpMethod = "DELETE")
    @RequiresPermissions("demo:stu:remove")
    @Log(title = "删除学生信息管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{stuIds}")
    @ApiImplicitParam(name = "stuId", value = "学生id", required = true)
    public CommonResult<Boolean> deleteDemoStu(@PathVariable Long[] stuIds) {
//        demoStuService.deleteDemoStu(stuId);
//        return success(true);
        return null;
    }

//    @GetMapping("/get")
//    @Operation(summary = "获得学生课程")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('infra:demo03-course:query')")

    /**
     * 获取学生信息管理详细信息
     */
    @ApiOperation(value = "获取学生信息管理详细信息", httpMethod = "GET")
    @RequiresPermissions("demo:stu:query")
    @GetMapping(value = "/{stuId}")
    public CommonResult<StuRespVO> getDemoStu(@PathVariable("stuId") Long stuId) {
        DemoStu demoStu = demoStuService.getDemoStu(stuId);
        return success(HutoolBeanUtils.toBean(demoStu, StuRespVO.class));
    }
}
