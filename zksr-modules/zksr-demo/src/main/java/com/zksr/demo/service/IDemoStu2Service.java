package com.zksr.demo.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.demo.domain.DemoStu2;
import com.zksr.demo.controller.stu2.vo.DemoStu2PageReqVO;
import com.zksr.demo.controller.stu2.vo.DemoStu2SaveReqVO;

/**
 * 学生信息2Service接口
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
public interface IDemoStu2Service {

    /**
     * 新增学生信息2
     *
     * @param demoStu2 学生信息2
     * @return 结果
     */
    public Long insertDemoStu2(@Valid DemoStu2SaveReqVO demoStu2);

    /**
     * 修改学生信息2
     *
     * @param demoStu2 学生信息2
     * @return 结果
     */
    public void updateDemoStu2(@Valid DemoStu2SaveReqVO demoStu2);

    /**
     * 删除学生信息2
     *
     * @param stuId 用户ID
     */
    public void deleteDemoStu2(Long stuId);

    /**
     * 批量删除学生信息2
     *
     * @param stuIds 需要删除的学生信息2主键集合
     * @return 结果
     */
    public void deleteDemoStu2ByStuIds(Long[] stuIds);

    /**
     * 获得学生信息2
     *
     * @param stuId 用户ID
     * @return 学生信息2
     */
    public DemoStu2 getDemoStu2(Long stuId);

    /**
     * 获得学生信息2分页
     *
     * @param pageReqVO 分页查询
     * @return 学生信息2分页
     */
    PageResult<DemoStu2> getDemoStu2Page(DemoStu2PageReqVO pageReqVO);

}
