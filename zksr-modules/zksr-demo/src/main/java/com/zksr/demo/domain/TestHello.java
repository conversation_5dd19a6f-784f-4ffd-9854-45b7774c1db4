package com.zksr.demo.domain;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 测试代码生产对象 test_hello
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@TableName(value = "test_hello")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("测试代码生产")
public class TestHello extends BaseEntity{
private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId
    private Long id;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updated;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date created;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

}
