package com.zksr.demo.convert.stu;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.demo.controller.stu.vo.StuRespVO;
import com.zksr.demo.domain.DemoStu;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DemoStuConvert {
    DemoStuConvert INSTANCE = Mappers.getMapper(DemoStuConvert.class);

    StuRespVO convert(DemoStu demoStu);

    PageResult<StuRespVO> convertPage(PageResult<DemoStu> page);
}
