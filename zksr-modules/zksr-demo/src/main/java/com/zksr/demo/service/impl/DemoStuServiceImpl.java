package com.zksr.demo.service.impl;

import java.util.List;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.demo.controller.stu.vo.StuPageReqVO;
import com.zksr.demo.controller.stu.vo.StuSaveReqVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zksr.demo.mapper.DemoStuMapper;
import com.zksr.demo.domain.DemoStu;
import com.zksr.demo.service.IDemoStuService;

/**
 * 学生信息管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
@Validated
public class DemoStuServiceImpl implements IDemoStuService {
    @Autowired
    private DemoStuMapper demoStuMapper;
//
//    /**
//     * 查询学生信息管理
//     *
//     * @param stuId 学生信息管理主键
//     * @return 学生信息管理
//     */
//    @Override
//    public DemoStu selectDemoStuByStuId(Long stuId)
//    {
//        return demoStuMapper.selectDemoStuByStuId(stuId);
//    }
//
//    /**
//     * 新增学生信息管理
//     *
//     * @param demoStu 学生信息管理
//     * @return 结果
//     */
//    @Override
//    public int insertDemoStu(DemoStu demoStu)
//    {
//        demoStu.setCreateTime(DateUtils.getNowDate());
//        return demoStuMapper.insert(demoStu);
//    }
//
//    /**
//     * 修改学生信息管理
//     *
//     * @param demoStu 学生信息管理
//     * @return 结果
//     */
//    @Override
//    public int updateDemoStu(DemoStu demoStu)
//    {
//        demoStu.setUpdateTime(DateUtils.getNowDate());
//        return demoStuMapper.updateById(demoStu);
//    }
//
//    /**
//     * 批量删除学生信息管理
//     *
//     * @param stuIds 需要删除的学生信息管理主键
//     * @return 结果
//     */
//    @Override
//    public int deleteDemoStuByStuIds(Long[] stuIds)
//    {
//        return demoStuMapper.deleteDemoStuByStuIds(stuIds);
//    }
//
//    /**
//     * 删除学生信息管理信息
//     *
//     * @param stuId 学生信息管理主键
//     * @return 结果
//     */
//    @Override
//    public int deleteDemoStuByStuId(Long stuId)
//    {
//        return demoStuMapper.deleteDemoStuByStuId(stuId);
//    }

    @Override
    public Long createDemoStu(StuSaveReqVo createReqVO) {
        // 插入
        DemoStu demo03Course = HutoolBeanUtils.toBean(createReqVO, DemoStu.class);
        demoStuMapper.insert(demo03Course);
        // 返回
        return demo03Course.getStuId();
    }

    @Override
    public void updateDemoStu(StuSaveReqVo updateReqVO) {
        // 校验存在
        /*validateDemo03CourseExists(updateReqVO.getStuId());
        // 更新
        Demo03CourseDO updateObj = BeanUtils.toBean(updateReqVO, Demo03CourseDO.class);
        demo03CourseMapper.updateById(updateObj);*/
    }

    @Override
    public void deleteDemoStu(Long stuId) {

    }

    @Override
    public DemoStu getDemoStu(Long stuId) {
        return null;
    }

//    @Override
//    public void deleteDemo03Course(Long id) {
//        // 校验存在
//        validateDemo03CourseExists(id);
//        // 删除
//        //demo03CourseMapper.deleteById(id);
//    }

    private void validateDemo03CourseExists(Long id) {
        /*if (demo03CourseMapper.selectById(id) == null) {
            throw exception(DEMO03_COURSE_NOT_EXISTS);
        }*/
    }

//    @Override
//    public Demo03CourseDO getDemo03Course(Long id) {
//        return demo03CourseMapper.selectById(id);
//    }
//
//    @Override
//    public PageResult<Demo03CourseDO> getDemo03CoursePage(Demo03CoursePageReqVO pageReqVO) {
//        return demo03CourseMapper.selectPage(pageReqVO);
//    }



    @Override
    public PageResult<DemoStu> getDemoStuPage(StuPageReqVO pageReqVO) {
        return demoStuMapper.selectPage(pageReqVO);
    }

    /**
     * 查询学生信息管理列表
     *
     * @param demoStu 学生信息管理
     * @return 学生信息管理
     */
    @Override
    public List<DemoStu> selectDemoStuList(DemoStu demoStu) {
        return demoStuMapper.selectList(demoStu);
    }
}
