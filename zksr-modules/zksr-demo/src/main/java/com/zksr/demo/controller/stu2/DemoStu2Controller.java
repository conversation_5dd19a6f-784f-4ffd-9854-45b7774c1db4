package com.zksr.demo.controller.stu2;

import javax.validation.Valid;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.demo.domain.DemoStu2;
import com.zksr.demo.service.IDemoStu2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.demo.controller.stu2.vo.DemoStu2PageReqVO;
import com.zksr.demo.controller.stu2.vo.DemoStu2SaveReqVO;
import com.zksr.demo.controller.stu2.vo.DemoStu2RespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 学生信息2Controller
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@Api(tags = "管理后台 - 学生信息2接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/stu2")
public class DemoStu2Controller {
    @Autowired
    private IDemoStu2Service demoStu2Service;

    /**
     * 新增学生信息2
     */
    @ApiOperation(value = "新增学生信息2", httpMethod = "POST")
    @RequiresPermissions("demo:stu2:add")
    @Log(title = "学生信息2", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody DemoStu2SaveReqVO demoStu2) {
        return success(demoStu2Service.insertDemoStu2(demoStu2));
    }

    /**
     * 修改学生信息2
     */
    @ApiOperation(value = "修改学生信息2", httpMethod = "PUT")
    @RequiresPermissions("demo:stu2:edit")
    @Log(title = "学生信息2", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody DemoStu2SaveReqVO demoStu2) {
            demoStu2Service.updateDemoStu2(demoStu2);
        return success(true);
    }

    /**
     * 删除学生信息2
     */
    @ApiOperation(value = "删除学生信息2", httpMethod = "GET")
    @RequiresPermissions("demo:stu2:remove")
    @Log(title = "学生信息2", businessType = BusinessType.DELETE)
    @DeleteMapping("/{stuIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] stuIds) {
        demoStu2Service.deleteDemoStu2ByStuIds(stuIds);
        return success(true);
    }

    /**
     * 获取学生信息2详细信息
     */
    @ApiOperation(value = "获得学生信息2详情", httpMethod = "GET")
    @RequiresPermissions("demo:stu2:query")
    @GetMapping(value = "/{stuId}")
    public CommonResult<DemoStu2RespVO> getInfo(@PathVariable("stuId") Long stuId) {
        DemoStu2 demoStu2 = demoStu2Service.getDemoStu2(stuId);
        return success(HutoolBeanUtils.toBean(demoStu2, DemoStu2RespVO.class));
    }

    /**
     * 分页查询学生信息2
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得学生信息2分页列表", httpMethod = "GET")
    @RequiresPermissions("demo:stu2:page")
    public CommonResult<PageResult<DemoStu2RespVO>> getPage(@Valid DemoStu2PageReqVO pageReqVO) {
        PageResult<DemoStu2> pageResult = demoStu2Service.getDemoStu2Page(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, DemoStu2RespVO.class));
    }

}
