package com.zksr.demo.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.utils.bean.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.demo.domain.TestHello;
import com.zksr.demo.service.ITestHelloService;
import com.zksr.common.core.web.controller.BaseController;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.core.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.demo.controller.vo.TestHelloPageReqVo;
import com.zksr.demo.controller.vo.TestHelloSaveReqVo;
import com.zksr.demo.controller.vo.TestHelloRespVo;
import com.zksr.common.core.web.page.TableDataInfo;

/**
 * 测试代码生产Controller
 * 
 * <AUTHOR>
 * @date 2024-01-18
 */
@Api(tags = "测试代码生产接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/hello")
public class TestHelloController extends BaseController
{
    @Autowired
    private ITestHelloService testHelloService;


    @GetMapping("/page")
    @ApiOperation(value = "获得测试代码生产分页列表", httpMethod = "GET")
    @RequiresPermissions("system:hello:page")
    public TableDataInfo<TestHelloRespVo> getPage(@Valid TestHelloPageReqVo pageReqVo) {
        PageResult<TestHello> pageResult = testHelloService.getTestHelloPage(pageReqVo);
        return getDataTable(BeanUtils.toBean(pageResult.getList(), TestHelloRespVo.class));
    }

    /**
     * 查询测试代码生产列表
     */
    @ApiOperation(value = "获得测试代码生产列表", httpMethod = "GET")
    @RequiresPermissions("system:hello:list")
    @GetMapping("/list")
    public TableDataInfo list(TestHello testHello)
    {
        startPage();
        List<TestHello> list = testHelloService.list(new QueryWrapper<>(testHello));
        return getDataTable(list);
    }

    /**
     * 获取测试代码生产详细信息
     */
    @ApiOperation(value = "获得测试代码生产详情", httpMethod = "GET")
    @RequiresPermissions("system:hello:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(testHelloService.getById(id));
    }

    /**
     * 新增测试代码生产
     */
    @ApiOperation(value = "新增测试代码生产", httpMethod = "POST")
    @RequiresPermissions("system:hello:add")
    @Log(title = "测试代码生产", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody TestHelloSaveReqVo testHello)
    {
        return toAjax(testHelloService.insertTestHello(testHello));
    }

    /**
     * 修改测试代码生产
     */
    @ApiOperation(value = "修改测试代码生产", httpMethod = "POST")
    @RequiresPermissions("system:hello:edit")
    @Log(title = "测试代码生产", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody TestHelloSaveReqVo testHello)
    {
        return toAjax(testHelloService.updateTestHello(testHello));
    }

    /**
     * 删除测试代码生产
     */
    @ApiOperation(value = "删除测试代码生产", httpMethod = "GET")
    @RequiresPermissions("system:hello:remove")
    @Log(title = "测试代码生产", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(testHelloService.removeByIds(Arrays.asList(ids)));
    }
}
