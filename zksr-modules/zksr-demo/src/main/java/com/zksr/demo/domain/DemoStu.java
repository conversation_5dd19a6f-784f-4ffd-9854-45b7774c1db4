package com.zksr.demo.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 学生信息管理对象 demo_stu
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@TableName(value = "demo_stu")
@ApiModel("学生信息管理对象")
public class DemoStu extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long stuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    private Long sysCode;

    /** 学生姓名 */
    @Excel(name = "学生姓名")
    private String stuName;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phone;

    /** 登录日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登录日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date stuDate;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private Integer status;

    public void setStuId(Long stuId)
    {
        this.stuId = stuId;
    }

    public Long getStuId()
    {
        return stuId;
    }
    public void setSysCode(Long sysCode)
    {
        this.sysCode = sysCode;
    }

    public Long getSysCode()
    {
        return sysCode;
    }
    public void setStuName(String stuName)
    {
        this.stuName = stuName;
    }

    public String getStuName()
    {
        return stuName;
    }
    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getPhone()
    {
        return phone;
    }
    public void setStuDate(Date stuDate)
    {
        this.stuDate = stuDate;
    }

    public Date getStuDate()
    {
        return stuDate;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("stuId", getStuId())
            .append("sysCode", getSysCode())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("stuName", getStuName())
            .append("phone", getPhone())
            .append("stuDate", getStuDate())
            .append("status", getStatus())
            .toString();
    }
}
