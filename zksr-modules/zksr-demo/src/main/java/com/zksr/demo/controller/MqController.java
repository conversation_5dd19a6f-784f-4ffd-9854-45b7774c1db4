package com.zksr.demo.controller;

import com.zksr.common.rocketmq.dto.MqUserDto;
import com.zksr.demo.mq.MqProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.integration.support.StringObjectMapBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 消息测试类Controller
 */
@RestController
@RequestMapping("/mqMessage")
@Slf4j
public class MqController {

//    @Autowired
//    private StreamBridge streamBridge;

    @Autowired
    private MqProducer mqProducer;

    /**
     * 发送消息
     */
    @PostMapping("/sendMessage")
    private Map sendMessage(String msg){
        mqProducer.sendMytopicMsg(msg);
        return null;
    }

    /**
     * 发送消息2
     */
    @PostMapping("/sendMessage2")
    private Map sendMessage2(String msg){
        mqProducer.sendMytopicMsg2(msg);
        return null;
    }

    /**
     * 发送消息 - 测试并发消费
     * @return
     */
    @PostMapping("/sendMessageConsumeConcurrency")
    private Map sendMessageConsumeConcurrency(){
        String msg = "hello world!:";
        for (int i = 0; i < 100; i++) {
            mqProducer.sendMytopicConcurrency(msg + i);
        }
        return null;
    }

    /**
     * 发送消息-延时消息
     */
    @PostMapping("/sendMessageDelay")
    private Map sendMessageDelay(String msg){
        mqProducer.sendMytopicMsgDelay(msg);
        return null;
    }

    /**
     * 发送消息-顺序消息
     */
    @PostMapping("/sendMessageOrderly")
    private Map sendMessageOrderly(){
        String msg = "hello world!:";
        for (int i = 0; i < 100; i++) {
            mqProducer.sendMytopicOrderly(msg + i, i);
        }
        return null;
    }

    /**
     * 发送对象
     */
    @PostMapping("/sendObjMessage")
    private Map sendMessage(){
        MqUserDto user = new MqUserDto();
        user.setId("100");
        user.setName("freeman");
        user.setMeta(new StringObjectMapBuilder()
                .put("hobbies", Arrays.asList("movies", "songs")).put("age", 21)
                .get());

        mqProducer.sendMyObjectTopicMsg(user);
        return null;
    }
}
