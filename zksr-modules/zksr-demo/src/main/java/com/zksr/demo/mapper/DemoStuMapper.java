package com.zksr.demo.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.demo.controller.stu.vo.StuPageReqVO;
import com.zksr.demo.domain.DemoStu;

/**
 * 学生信息管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface DemoStuMapper extends BaseMapperX<DemoStu> {

    default PageResult<DemoStu> selectPage(StuPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DemoStu>()
                .likeIfPresent(DemoStu::getStuName, reqVO.getStuName())
                .eqIfPresent(DemoStu::getStatus, reqVO.getStatus())
                .orderByAsc(DemoStu::getStuDate));
    }

    default List<DemoStu> selectList(DemoStu demoStu) {
        return selectList(new LambdaQueryWrapperX<DemoStu>()
                .likeIfPresent(DemoStu::getStuName, demoStu.getStuName())
                .eqIfPresent(DemoStu::getStatus, demoStu.getStatus())
                .orderByAsc(DemoStu::getStuDate));
    }

    /**
     * 查询学生信息管理
     *
     * @param stuId 学生信息管理主键
     * @return 学生信息管理
     */
    public DemoStu selectDemoStuByStuId(Long stuId);

    /**
     * 查询学生信息管理列表
     *
     * @param demoStu 学生信息管理
     * @return 学生信息管理集合
     */
    public List<DemoStu> selectDemoStuList(DemoStu demoStu);

    /**
     * 新增学生信息管理
     *
     * @param demoStu 学生信息管理
     * @return 结果
     */
    public int insertDemoStu(DemoStu demoStu);

    /**
     * 修改学生信息管理
     *
     * @param demoStu 学生信息管理
     * @return 结果
     */
    public int updateDemoStu(DemoStu demoStu);

    /**
     * 删除学生信息管理
     *
     * @param stuId 学生信息管理主键
     * @return 结果
     */
    public int deleteDemoStuByStuId(Long stuId);

    /**
     * 批量删除学生信息管理
     *
     * @param stuIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDemoStuByStuIds(Long[] stuIds);
}
