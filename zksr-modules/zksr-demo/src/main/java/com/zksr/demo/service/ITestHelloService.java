package com.zksr.demo.service;

import java.util.List;
import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zksr.demo.domain.TestHello;
import com.zksr.demo.controller.vo.TestHelloPageReqVo;
import com.zksr.demo.controller.vo.TestHelloSaveReqVo;

/**
 * 测试代码生产Service接口
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
public interface ITestHelloService extends IService<TestHello> {

    /**
     * 获得测试代码生产分页
     *
     * @param pageReqVo 分页查询
     * @return 测试代码生产分页
     */
    PageResult<TestHello> getTestHelloPage(TestHelloPageReqVo pageReqVo);

    /**
     * 新增测试代码生产
     *
     * @param testHello 测试代码生产
     * @return 结果
     */
    public int insertTestHello(@Valid TestHelloSaveReqVo testHello);

    /**
     * 修改测试代码生产
     *
     * @param testHello 测试代码生产
     * @return 结果
     */
    public int updateTestHello(@Valid TestHelloSaveReqVo testHello);

}
