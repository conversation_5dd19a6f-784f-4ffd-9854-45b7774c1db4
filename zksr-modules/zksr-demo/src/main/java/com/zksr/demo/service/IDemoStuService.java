package com.zksr.demo.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.demo.controller.stu.vo.StuPageReqVO;
import com.zksr.demo.controller.stu.vo.StuSaveReqVo;
import com.zksr.demo.domain.DemoStu;

import javax.validation.Valid;

/**
 * 学生信息管理Service接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IDemoStuService
{
//    /**
//     * 查询学生信息管理
//     *
//     * @param stuId 学生信息管理主键
//     * @return 学生信息管理
//     */
//    public DemoStu selectDemoStuByStuId(Long stuId);
//
//    /**
//     * 查询学生信息管理列表
//     *
//     * @param demoStu 学生信息管理
//     * @return 学生信息管理集合
//     */
//    public List<DemoStu> selectDemoStuList(DemoStu demoStu);
//
//    /**
//     * 新增学生信息管理
//     *
//     * @param demoStu 学生信息管理
//     * @return 结果
//     */
//    public int insertDemoStu(DemoStu demoStu);
//
//    /**
//     * 修改学生信息管理
//     *
//     * @param demoStu 学生信息管理
//     * @return 结果
//     */
//    public int updateDemoStu(DemoStu demoStu);
//
//    /**
//     * 批量删除学生信息管理
//     *
//     * @param stuIds 需要删除的学生信息管理主键集合
//     * @return 结果
//     */
//    public int deleteDemoStuByStuIds(Long[] stuIds);
//
//    /**
//     * 删除学生信息管理信息
//     *
//     * @param stuId 学生信息管理主键
//     * @return 结果
//     */
//    public int deleteDemoStuByStuId(Long stuId);


    /**
     * 创建学生信息管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDemoStu(@Valid StuSaveReqVo createReqVO);

    /**
     * 更新学生信息管理
     *
     * @param updateReqVO 更新信息
     */
    void updateDemoStu(@Valid StuSaveReqVo updateReqVO);

    /**
     * 删除学生信息管理
     *
     * @param stuId 学生信息ID
     */
    void deleteDemoStu(Long stuId);

    /**
     * 获得学生信息管理
     *
     * @param stuId 学生信息ID
     * @return 学生课程
     */
    DemoStu getDemoStu(Long stuId);

    /**
     * 获得学生课程分页
     *
     * @param pageReqVO 分页查询
     * @return 学生课程分页
     */
    PageResult<DemoStu> getDemoStuPage(StuPageReqVO pageReqVO);

    /**
     * 查询学生信息管理列表
     *
     * @param demoStu 学生信息管理
     * @return 学生信息管理集合
     */
    public List<DemoStu> selectDemoStuList(DemoStu demoStu);
}
