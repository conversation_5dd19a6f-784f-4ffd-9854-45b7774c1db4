package com.zksr.demo.mq;

import com.alibaba.fastjson2.JSON;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.common.rocketmq.dto.MqUserDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.messaging.support.MessageBuilder;

import java.util.HashMap;
import java.util.Map;

import static com.zksr.demo.mq.OrderlyMessageQueueSelector.tags;

@Configuration
@Slf4j
public class MqProducer {


    @Autowired
    private StreamBridge streamBridge;

    public void sendMytopicMsg(String orderNo){
        log.info("sendMytopicMsg发送消息：" + orderNo);
        boolean flag = streamBridge.send(
                MessageConstant.MYTOPIC_MESSAGE_OUTPUT,
                MessageBuilder.withPayload("hello spring cloud stream:" + orderNo)
                        .setHeader("for", "这是一个请求头～")
                        .build());
        log.info("sendMytopicMsg发送消息：" + flag);
    }

    public void sendMytopicMsg2(String orderNo){
        log.info("sendMytopicMsg2发送消息：" + orderNo);
        Map<String, Object> headers = new HashMap<>();
        headers.put("for", "这是一个请求头～");
        Message<String> msg = new GenericMessage("hello spring cloud stream:" + orderNo, headers);
        boolean flag = streamBridge.send(MessageConstant.MYTOPIC2_MESSAGE_OUTPUT, msg);
        log.info("sendMytopicMsg2发送消息：" + flag);
    }

    public void sendMytopicConcurrency(String orderNo){
        log.info("sendMytopicMsgConcurrency发送消息：" + orderNo);
        Map<String, Object> headers = new HashMap<>();
        headers.put("for", "这是一个请求头～");
        Message<String> msg = new GenericMessage("hello spring cloud stream:" + orderNo, headers);
        boolean flag = streamBridge.send(MessageConstant.MYTOPICCONCURRENCY_MESSAGE_OUTPUT, msg);
        log.info("sendMytopicMsgConcurrency发送消息：" + flag);
    }

    public void sendMytopicMsgDelay(String orderNo){
        log.info("sendMytopicMsgDelay发送消息：" + orderNo);
        Map<String, Object> headers = new HashMap<>();
        headers.put("for", "这是一个请求头～");
        /**
         *延时消息等级分为18个：1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
         *例如 setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL,3)将等级设置3，对应等级的秒数是10s
         */
        headers.put(MessageConst.PROPERTY_DELAY_TIME_LEVEL, 2);
        Message<String> msg = new GenericMessage("hello spring cloud stream:" + orderNo, headers);
        boolean flag = streamBridge.send(MessageConstant.MYTOPICDELAY_MESSAGE_OUTPUT, msg);
        log.info("sendMytopicMsgDelay发送消息：" + flag);
    }

    public void sendMytopicOrderly(String orderNo, int i){
        log.info("sendMytopicOrderly发送消息：" + orderNo);
        Map<String, Object> headers = new HashMap<>();
        headers.put("for", "这是一个请求头～");
        headers.put(MessageConst.PROPERTY_KEYS, orderNo);
        headers.put(MessageConst.PROPERTY_TAGS, tags[i % tags.length]);
        headers.put(MessageConst.PROPERTY_ORIGIN_MESSAGE_ID, i);
        Message<String> msg = new GenericMessage("hello spring cloud stream:" + orderNo, headers);
        boolean flag = streamBridge.send(MessageConstant.MYTOPICORDERLY_MESSAGE_OUTPUT, msg);
        log.info("sendMytopicOrderly发送消息：" + flag);
    }

    public void sendMyObjectTopicMsg(MqUserDto user){
        log.info("sendMytopicObjMsg发送消息：" + JSON.toJSON(user));
        boolean flag = streamBridge.send(
                MessageConstant.MYOBJECTTOPIC_MESSAGE_OUTPUT,
                MessageBuilder.withPayload(user)
                        .setHeader("for", "这是一个请求头2～")
                        .build());
        log.info("sendMytopicObjMsg发送消息：" + flag);
    }

}
