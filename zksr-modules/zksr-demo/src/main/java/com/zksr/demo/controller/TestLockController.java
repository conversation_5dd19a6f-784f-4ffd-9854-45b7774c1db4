package com.zksr.demo.controller;

import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.demo.dto.UserDto;
import com.zksr.demo.service.LockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: redis 注解锁 demo
 * @date 2024/3/29 14:55
 */
@RestController
public class TestLockController {

    @Autowired
    private LockService lockService;

    @RequestMapping("testLock1")
    public String testLock1() {
        UserDto userDto = new UserDto();
        userDto.setName("dog");
        userDto.setAge(123);
        {
            Thread thread = new Thread(new Runnable() {
                @Override
                public void run() {
                    System.out.println("我是1, 我已就位");
                    lockService.payGame("cat", 111);
                    System.out.println(StringUtils.format("{} 时间 {}", "我是1", DateUtil.formatDateTime(new Date())));
                }
            });
            Thread thread2 = new Thread(new Runnable() {
                @Override
                public void run() {
                    System.out.println("我是2, 我已就位");
                    lockService.payGame("cat", 111);
                    System.out.println(StringUtils.format("{} 时间 {}", "我是2", DateUtil.formatDateTime(new Date())));
                }
            });
            thread2.start();
            thread.start();
        }
        {
            Thread thread = new Thread(new Runnable() {
                @Override
                public void run() {
                    System.out.println("我是3, 我已就位");
                    lockService.payGame( new UserDto(), new UserDto(), userDto);
                    System.out.println(StringUtils.format("{} 时间 {}", "我是3", DateUtil.formatDateTime(new Date())));
                }
            });
            Thread thread2 = new Thread(new Runnable() {
                @Override
                public void run() {
                    System.out.println("我是4, 我已就位");
                    lockService.payGame( new UserDto(), new UserDto(), userDto);
                    System.out.println(StringUtils.format("{} 时间 {}", "我是4", DateUtil.formatDateTime(new Date())));
                }
            });
            thread2.start();
            thread.start();
        }
        return "SUCCESS";
    }
}
