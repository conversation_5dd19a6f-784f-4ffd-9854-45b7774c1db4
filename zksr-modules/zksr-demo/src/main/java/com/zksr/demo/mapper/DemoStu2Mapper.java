package com.zksr.demo.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.demo.domain.DemoStu2;
import com.zksr.demo.controller.stu2.vo.DemoStu2PageReqVO;


/**
 * 学生信息2Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@Mapper
public interface DemoStu2Mapper extends BaseMapperX<DemoStu2> {
    default PageResult<DemoStu2> selectPage(DemoStu2PageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DemoStu2>()
                    .eqIfPresent(DemoStu2::getStuId, reqVO.getStuId())
                    .eqIfPresent(DemoStu2::getSysCode, reqVO.getSysCode())
                    .likeIfPresent(DemoStu2::getStuName, reqVO.getStuName())
                    .eqIfPresent(DemoStu2::getPhone, reqVO.getPhone())
                    .eqIfPresent(DemoStu2::getStuDate, reqVO.getStuDate())
                    .eqIfPresent(DemoStu2::getStatus, reqVO.getStatus())
                .orderByDesc(DemoStu2::getStuId));
    }
}
