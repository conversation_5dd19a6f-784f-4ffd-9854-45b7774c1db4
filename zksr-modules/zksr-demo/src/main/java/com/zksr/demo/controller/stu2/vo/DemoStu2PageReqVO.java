package com.zksr.demo.controller.stu2.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 学生信息2对象 demo_stu2
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@ApiModel("学生信息2 - demo_stu2分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DemoStu2PageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @ApiModelProperty(value = "状态")
    private Long stuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 学生姓名 */
    @Excel(name = "学生姓名")
    @ApiModelProperty(value = "学生姓名")
    private String stuName;

    /** 手机号码 */
    @Excel(name = "手机号码")
    @ApiModelProperty(value = "手机号码")
    private String phone;

    /** 登录日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "登录日期", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "登录日期")
    private Date stuDate;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "状态")
    private Integer status;


}
