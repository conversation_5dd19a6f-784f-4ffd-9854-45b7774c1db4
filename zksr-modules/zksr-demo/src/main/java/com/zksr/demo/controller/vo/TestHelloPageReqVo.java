package com.zksr.demo.controller.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.pojo.PageParam;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 测试代码生产对象 test_hello
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@ApiModel("测试代码生产 - test_hello分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TestHelloPageReqVo extends PageParam{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "名称")
    private Long id;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updated;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date created;

    /** 名称 */
    @Excel(name = "名称")
    @ApiModelProperty(value = "名称", required = true,   example = "示例值")
    private String name;


}
