package com.zksr.demo.service.impl;

import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.demo.dto.UserDto;
import com.zksr.demo.service.LockService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/3/29 14:57
 */
@Service
public class LockServiceImpl implements LockService {

    /**
     * 从对象获取锁
     * @param userDto2  无效干扰参数测试
     * @param userDto3  无效干扰参数测试
     * @param userDto   有效参数
     *
     * prefix 前缀必填
     * suffix 可不填
     */
    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_DEMO_PRE, condition = "#userDto.name + #userDto.age", suffix = RedisLockConstants.LOCK_DEMO_POST)
    public void payGame(UserDto userDto2, UserDto userDto3, UserDto userDto) {
        System.out.println("do something");
        try {
            Thread.sleep(3000L);
        } catch (InterruptedException e) {

            throw new RuntimeException(e);
        }
    }

    /**
     * 基本数据类型从获取锁拼接
     * @param name
     * @param age
     */
    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_DEMO_PRE, condition = "#name + #age")
    public void payGame(String name, Integer age) {

        System.out.println("do something");
        try {
            Thread.sleep(3000L);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
