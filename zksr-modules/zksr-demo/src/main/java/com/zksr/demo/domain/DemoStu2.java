package com.zksr.demo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 学生信息2对象 demo_stu2
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@TableName(value = "demo_stu2")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemoStu2 extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 用户ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long stuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    private Long sysCode;

    /** 学生姓名 */
    @Excel(name = "学生姓名")
    private String stuName;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phone;

    /** 登录日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登录日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date stuDate;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private Integer status;

}
