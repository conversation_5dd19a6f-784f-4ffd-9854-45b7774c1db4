package com.zksr.demo.service.impl;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zksr.demo.mapper.TestHelloMapper;
import com.zksr.demo.domain.TestHello;
import com.zksr.demo.controller.vo.TestHelloPageReqVo;
import com.zksr.demo.controller.vo.TestHelloSaveReqVo;
import com.zksr.demo.service.ITestHelloService;
import com.zksr.common.core.web.pojo.PageResult;
/**
 * 测试代码生产Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Service
public class TestHelloServiceImpl extends ServiceImpl<TestHelloMapper, TestHello> implements ITestHelloService
{
    @Autowired
    private TestHelloMapper testHelloMapper;

    /**
    * 查询分页数据
    * @param pageReqVo
    * @return
    */
    @Override
    public PageResult<TestHello> getTestHelloPage(TestHelloPageReqVo pageReqVo) {
        return testHelloMapper.selectPage(pageReqVo);
    }

    /**
     * 新增测试代码生产
     *
     * @param testHello 测试代码生产
     * @return 结果
     */
    @Override
    public int insertTestHello(TestHelloSaveReqVo testHello)
    {
        return testHelloMapper.insert(BeanUtil.toBean(testHello, TestHello.class));
    }

    /**
     * 修改测试代码生产
     *
     * @param testHello 测试代码生产
     * @return 结果
     */
    @Override
    public int updateTestHello(TestHelloSaveReqVo testHello)
    {
        return testHelloMapper.updateById(BeanUtil.toBean(testHello, TestHello.class));
    }

}
