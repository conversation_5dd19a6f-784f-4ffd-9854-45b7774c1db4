package com.zksr.demo.service.impl;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheUpdate;
import com.alicp.jetcache.anno.Cached;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.demo.mapper.DemoStu2Mapper;
import com.zksr.demo.domain.DemoStu2;
import com.zksr.demo.controller.stu2.vo.DemoStu2PageReqVO;
import com.zksr.demo.controller.stu2.vo.DemoStu2SaveReqVO;
import com.zksr.demo.service.IDemoStu2Service;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.redis.enums.RedisConstants.DEMO_STU2_KEY;
import static com.zksr.demo.enums.ErrorCodeConstants.*;

/**
 * 学生信息2Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@Service
public class DemoStu2ServiceImpl implements IDemoStu2Service {
    @Autowired
    private DemoStu2Mapper demoStu2Mapper;

    /**
     * 新增学生信息2
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertDemoStu2(DemoStu2SaveReqVO createReqVO) {
        // 插入
        DemoStu2 demoStu2 = HutoolBeanUtils.toBean(createReqVO, DemoStu2.class);
        demoStu2Mapper.insert(demoStu2);
        // 返回
        return demoStu2.getStuId();
    }

    /**
     * 修改学生信息2
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @CacheUpdate(name=DEMO_STU2_KEY, key="#updateReqVO.stuId", value="#updateReqVO")
    public void updateDemoStu2(DemoStu2SaveReqVO updateReqVO) {
        demoStu2Mapper.updateById(HutoolBeanUtils.toBean(updateReqVO, DemoStu2.class));
    }

    /**
     * 删除学生信息2
     *
     * @param stuId 用户ID
     */
    @Override
    @CacheInvalidate(name=DEMO_STU2_KEY, key="#stuId")
    public void deleteDemoStu2(Long stuId) {
        // 删除
            demoStu2Mapper.deleteById(stuId);
    }

    /**
     * 批量删除学生信息2
     *
     * @param stuIds 需要删除的学生信息2主键
     * @return 结果
     */
    @Override
    public void deleteDemoStu2ByStuIds(Long[] stuIds) {
        for(Long stuId : stuIds){
            this.deleteDemoStu2(stuId);
        }
    }

    /**
     * 获得学生信息2
     *
     * @param stuId 用户ID
     * @return 学生信息2
     */
    @Override
    @Cached(name=DEMO_STU2_KEY, key="#stuId", expire = 3600)
    public DemoStu2 getDemoStu2(Long stuId) {
        return demoStu2Mapper.selectById(stuId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<DemoStu2> getDemoStu2Page(DemoStu2PageReqVO pageReqVO) {
        return demoStu2Mapper.selectPage(pageReqVO);
    }

    private void validateDemoStu2Exists(Long stuId) {
        if (demoStu2Mapper.selectById(stuId) == null) {
            throw exception(DEMO_STU2_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.demo.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 学生信息2 TODO 补充编号 ==========
    // ErrorCode DEMO_STU2_NOT_EXISTS = new ErrorCode(TODO 补充编号, "学生信息2不存在");


}
