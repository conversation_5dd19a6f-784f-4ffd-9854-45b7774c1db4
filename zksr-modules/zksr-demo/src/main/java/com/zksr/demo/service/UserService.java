/*
 * Copyright © ${project.inceptionYear} organization baomidou
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.zksr.demo.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.zksr.demo.entity.User;

import java.util.List;

public interface UserService extends IService<User> {

    List<User> selectMasterUsers();

    List<User> selectSlaveUsers();

    List<User> selectLambdaMasterUsers();

    List<User> selectLambdaSlaveUsers();

    List<User> selectSlaveAnnotationUsers();

    void addUser(User user);

    void deleteUserById(Long id);
}
