package com.zksr.demo.api;

import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.demo.api.stu2.DemoStu2Api;
import com.zksr.demo.api.stu2.dto.DemoStu2Dto;
import com.zksr.demo.domain.DemoStu2;
import com.zksr.demo.service.IDemoStu2Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * DemoStu2 API 接口实现类
 */

@RestController // 提供 RESTful API 接口，给 Feign 调用
public class DemoStu2ApiImpl implements DemoStu2Api {

    @Autowired
    private IDemoStu2Service demoStu2Service;

    @InnerAuth
    @Override
    public CommonResult<DemoStu2Dto> getStu2Info(Long stuId) {
        DemoStu2Dto dto = new DemoStu2Dto();
        DemoStu2 demoStu2 = demoStu2Service.getDemoStu2(stuId);
        BeanUtils.copyProperties(demoStu2, dto);
        return success(dto);
    }
}
