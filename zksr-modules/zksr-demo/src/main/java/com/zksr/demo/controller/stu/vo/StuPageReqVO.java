package com.zksr.demo.controller.stu.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StuPageReqVO extends PageParam {

    /** 学生姓名 */
    @NotNull(message = "学生姓名不能为空")
    @ApiModelProperty(value = "学生姓名", required = true, example = "小明")
    private String stuName;

    /** 手机号码 */
    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty(value = "手机号", required = true, example = "15601691300")
    private String phone;

    /** 登录日期 */
    @ApiModelProperty(value = "登录日期", required = true, example = "2024-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stuDate;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态，参见 CommonStatusEnum 枚举类", required = true, example = "1")
    private Integer status;

}
