package com.zksr.demo.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.demo.domain.TestHello;
import com.zksr.demo.controller.vo.TestHelloPageReqVo;


/**
 * 测试代码生产Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Mapper
public interface TestHelloMapper extends BaseMapperX<TestHello> {
    default PageResult<TestHello> selectPage(TestHelloPageReqVo reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TestHello>()
                    .eqIfPresent(TestHello::getId, reqVO.getId())
                    .eqIfPresent(TestHello::getUpdated, reqVO.getUpdated())
                    .eqIfPresent(TestHello::getCreated, reqVO.getCreated())
                    .likeIfPresent(TestHello::getName, reqVO.getName())
                .orderByDesc(TestHello::getId));
    }
}
