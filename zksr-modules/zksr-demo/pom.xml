<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zksr</groupId>
        <artifactId>zksr-modules</artifactId>
        <version>3.6.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zksr-modules-demo</artifactId>

    <description>
        zksr-modules-demo系统模块
    </description>

    <dependencies>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- Zksr-Mall Common DataSource -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-datasource</artifactId>
        </dependency>

        <!-- Zksr-Mall Common DataScope -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-mp</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Log -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-log</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Swagger -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-swagger</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Swagger -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-rocketmq</artifactId>
        </dependency>


    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
