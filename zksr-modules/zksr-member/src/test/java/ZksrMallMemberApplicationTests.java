import com.zksr.common.redis.service.RedisService;
import com.zksr.member.ZksrMallMemberApplication;
import com.zksr.member.controller.colonelApp.vo.MemColonelAppCustomerListPageReqVO;
import com.zksr.member.service.IMemBranchLifecycleZipService;
import com.zksr.member.service.IMemColonelAppService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.validation.Valid;
import java.util.concurrent.TimeUnit;

import static com.zksr.member.constant.MemberConstant.BRANCH_LIFECYCLE_OPERATION_TYPE_2;
import static com.zksr.member.constant.MemberConstant.BRANCH_LIFECYCLE_OPERATION_TYPE_3;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/9/10 10:04
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallMemberApplication.class)
@Slf4j
public class ZksrMallMemberApplicationTests {

    /*@Autowired
    private IMemColonelAppService memColonelAppService;

    @Test
    public void testRedisService() throws InterruptedException {
        MemColonelAppCustomerListPageReqVO req = new MemColonelAppCustomerListPageReqVO();
        req.setPageNo(1);
        req.setPageSize(100);
        req.setLat(28.212865);
        req.setLon(112.892182);
        req.setSortField(2);
        memColonelAppService.getMemColonelAppCustomerPage(req);
    }*/

    @Autowired
    private RedisService redisService;
    @Test
    public void testRedisService() throws InterruptedException {
        //存入缓存 (3天)
        String key = "prdt_spu_dto:471183574809640960:sysCode";
        //redisService.setCacheObject(key,null, 60 * 60 * 24 * 3L, TimeUnit.SECONDS);
        redisService.incrByCacheObject(key);
    }

    @Autowired
    private IMemBranchLifecycleZipService memBranchLifecycleZipService;
    @Test
    public void testBranchLifecycleZipService() throws InterruptedException {
        //memBranchLifecycleZipService.updateBranchLifecycle(470098537220440064L, BRANCH_LIFECYCLE_OPERATION_TYPE_2,595495660943704064L );

//        memBranchLifecycleZipService.updateBranchLifecycle(470098537220440064L, BRANCH_LIFECYCLE_OPERATION_TYPE_3,595495660943704064L );
    }

}
