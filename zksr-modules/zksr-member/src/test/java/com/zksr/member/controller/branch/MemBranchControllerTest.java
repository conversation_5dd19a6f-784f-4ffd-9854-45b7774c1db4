package com.zksr.member.controller.branch;


import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.standaloneSetup;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zksr.member.ZksrMallMemberApplication;
import com.zksr.member.controller.branch.vo.MemBranchSaveReqVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import java.math.BigDecimal;

/**
 * @Author: chenyj8
 * @Desciption: 测试类
 */
@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallMemberApplication.class)
public class MemBranchControllerTest {
//    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private MemBranchController memBranchController;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(memBranchController)
                .setValidator(new LocalValidatorFactoryBean())
                .build();
    }

    @Test
//    @WithMockUser(username = "zksr", roles = {"member:branch:add"}) // 模拟一个有权限的用户
    public void testAdd() throws Exception {
        MemBranchSaveReqVO createReqVO = new MemBranchSaveReqVO();
        createReqVO.setSysCode(4L);
        createReqVO.setBranchName("BranchName");
        createReqVO.setAreaId(18L);
        createReqVO.setColonelId(460321014102589440L);
        createReqVO.setBranchAddr("BranchAddr");
        createReqVO.setLatitude(new BigDecimal(12.00000000));
        createReqVO.setLongitude(new BigDecimal(123.00000000));
        createReqVO.setChannelId(0L);
        createReqVO.setContactName("11");
        createReqVO.setContactPhone("13200000000");
        createReqVO.setProvinceName("测试省");
        createReqVO.setCityName("测试市");
        createReqVO.setDistrictName("测试区县");

        //测试全局异常，不存在的字段
//        createReqVO.setLifecycleStage1(11);

        // 设置createReqVO的属性

        mockMvc.perform(post("/branch")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createReqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0)) // 假设CommonResult的code为0表示成功
//                .andExpect(jsonPath("$.data").isNumber())
                .andDo(MockMvcResultHandlers.print());
    }


}