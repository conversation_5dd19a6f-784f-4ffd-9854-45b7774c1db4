package com.zksr.member.service;

import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.ZksrMallMemberApplication;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterAuditVO;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterSaveReqVO;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterPageReqVO;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterPageRespVO;
import com.zksr.member.mapper.MemMemberRegisterMapper;
import com.zksr.trade.api.order.dto.TrdOrderPageReqDTO;
import com.zksr.trade.api.order.dto.TrdOrderRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Author: chenyj8
 * @Desciption: 类功能描述
 */
@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallMemberApplication.class)
public class MemMemberRegisterServiceTest {

    @Resource
    private IMemMemberRegisterService memMemberRegisterService;

    @Autowired
    private MemMemberRegisterMapper memMemberRegisterMapper;

    @BeforeEach
    void setUp() {

    }

    /**
     * 小程序注册用户
     */
    @Test
    public void testInsertUserMemberRegister(){
        MemMemberRegisterSaveReqVO createReqVO = new MemMemberRegisterSaveReqVO();
        createReqVO.setUserName("13650762700");
        Boolean bool = memMemberRegisterService.insertUserMemberRegister(createReqVO);
        System.out.println(bool);

    }


    /**
     * PC注册
     */
    @Test
    public void testInsertMemMemberRegister(){
        MemMemberRegisterSaveReqVO createReqVO = new MemMemberRegisterSaveReqVO();
        createReqVO.setUserName("13650762700");
        Long id = memMemberRegisterService.insertMemMemberRegister(createReqVO);
        System.out.println(id);

    }

    @Test
    public void testGetMemMemberRegisterPage(){
        MemMemberRegisterPageReqVO pageReqVO = new MemMemberRegisterPageReqVO();
        pageReqVO.setApproveFlag(1);

        // 获取当前日期和时间
        Calendar calendar1 = Calendar.getInstance();

        // 将日期向前推5天
        calendar1.add(Calendar.DAY_OF_MONTH, -30);

        // 获取5天前的Date对象
        Date setStartTime = calendar1.getTime();

        // 获取当前日期和时间
        Calendar calendar2 = Calendar.getInstance();

        // 将日期向前推5天
        calendar2.add(Calendar.DAY_OF_MONTH, -10);

        // 获取5天前的Date对象
        Date setEndTime = calendar2.getTime();

//        pageReqVO.setStartTime(setStartTime);
//        pageReqVO.setEndTime(setEndTime);

        pageReqVO.setColonelId(500835449218629632L);

        PageResult<MemMemberRegisterPageRespVO> rs = memMemberRegisterService.getMemMemberRegisterPage(pageReqVO);


        System.out.println(JsonUtils.toJsonString(rs));
    }


    @Test
    public void testBatchAuditMemberRegister(){
        MemMemberRegisterAuditVO auditVO = new MemMemberRegisterAuditVO();

        Long[] ids = new Long[]{481461388604833792L};

        auditVO.setMemberRegisterIds(ids);
        auditVO.setHdfkSupport(1);
        auditVO.setChannelId(1L);
        auditVO.setSalePriceCode(1L);
        auditVO.setHdfkMaxAmt(BigDecimal.valueOf(9999999));
        auditVO.setIsPayOnline(1);

        memMemberRegisterService.batchAuditMemberRegister(auditVO);

    }

    @Test
    public void testMemberRegisterApprove(){
        List<Long> memberRegisterIds = new ArrayList<>();
        memberRegisterIds.add(481461388604833792L);
        //审核
        memMemberRegisterMapper.memberRegisterApprove(memberRegisterIds, null, null);
    }
}
