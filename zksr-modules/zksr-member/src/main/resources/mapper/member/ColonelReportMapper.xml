<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.ColonelReportMapper">


    <select id="selectLastVisitTime" resultType="java.util.Date">
        SELECT create_time FROM mem_colonel_visit_log WHERE branch_id = #{branchId} ORDER BY colonel_visit_log_id DESC LIMIT 1
    </select>
    <select id="countMonthVisit" resultType="java.lang.Long">
        SELECT COUNT(1) FROM mem_colonel_visit_log WHERE branch_id = #{branchId} AND create_time BETWEEN CONCAT(#{monthDate}, '-01') AND DATE_ADD(CONCAT(#{monthDate}, '-01'), INTERVAL 1 MONTH)
    </select>
    <select id="selectLastVisitTimeList" resultType="com.zksr.member.api.colonel.vo.MemColonelRespVO">
        SELECT branch_id as branchId , max(create_time) as lastVisitDate
        FROM mem_colonel_visit_log
        <where>
            <if test="branchIds != null and branchIds.size > 0">
                mem_colonel_visit_log.branch_id in
                <foreach collection="branchIds" item="branchId" separator="," open="(" close=")">
                    #{branchId}
                </foreach>
            </if>
        </where>
        group by branch_id
    </select>

</mapper>