<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemColonelTargetMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectPage" resultType="com.zksr.member.controller.colonelTarget.vo.MemColonelTargetRespVO">
        SELECT
	        target.colonel_target_id
	        ,target.create_by
	        ,target.create_time
	        ,target.colonel_id
	        ,target.target_year
	        ,target.target_month
	        ,target.sales_money
	        ,target.month_new_customer
	        ,target.month_visit_customer
	        ,target.month_activity_customer
			,target.month_order_count
			,target.month_avg_order_value
			,target.month_first_sale_count
	        ,target.memo
	        ,target.`status`
	        ,colonel.colonel_name
	        ,colonel.dept_id
	        ,colonel.area_id
        FROM mem_colonel_target  target
        LEFT JOIN mem_colonel colonel ON colonel.colonel_id = target.colonel_id
        <where>
            <if test="null != reqVO.colonelName and '' != reqVO.colonelName ">
				AND colonel.colonel_name like concat('%', #{reqVO.colonelName}, '%')
            </if>
            <if test="null != reqVO.targetYear and '' != reqVO.targetYear ">
				AND target.target_year like concat('%', #{reqVO.targetYear}, '%')
			</if>
			<if test="null != reqVO.targetMonth and '' != reqVO.targetMonth ">
				AND target.target_month like concat('%', #{reqVO.targetMonth}, '%')
			</if>
			<if test="null != reqVO.status">
				AND target.status = #{reqVO.status}
			</if>
			<if test="null != reqVO.areaId">
				AND colonel.area_id = #{reqVO.areaId}
			</if>
			${reqVO.params.dataScope}
        </where>
		order by target.create_time desc
    </select>
    <select id="selectListByColonelIdsAndParams" resultType="com.zksr.member.domain.MemColonelTarget">
		SELECT
		    *
		FROM
		    mem_colonel_target mct
			LEFT JOIN mem_colonel mc ON mc.colonel_id = mct.colonel_id
		WHERE
		    mct.colonel_id IN
			<foreach collection="colonelIds" open="(" close=")" separator="," item="colonelId">
				#{colonelId}
			</foreach>
			<if test="null != reqVO.colonelName and '' != reqVO.colonelName ">
				AND mc.colonel_name like '%${reqVO.colonelName}%'
			</if>
			<if test="null != reqVO.targetYear and '' != reqVO.targetYear ">
				AND mct.target_year like '%${reqVO.targetYear}%'
			</if>
			<if test="null != reqVO.targetMonth and '' != reqVO.targetMonth ">
				AND mct.target_month like '%${reqVO.targetMonth}%'
			</if>
			<if test="null != reqVO.status">
				AND mct.status = ${reqVO.status}
			</if>
			<if test="null != reqVO.areaId">
				AND mc.area_id = ${reqVO.areaId}
			</if>
	</select>
	<select id="selectTargetMonthByColonelId" resultType="java.math.BigDecimal">
		SELECT
		    IFNULL(SUM(mct.sales_money), 0)
		FROM
		    mem_colonel_target mct
		WHERE
		    colonel_id IN
			<foreach collection="colonelList" open="(" close=")" separator="," item="colonelId">
				#{colonelId}
			</foreach>
			AND target_year = #{year}
			AND target_month = #{month}
			AND (del_flag = 0 OR del_flag IS NULL)
	</select>
    <select id="getColonelMonthlySalesTarget" resultType="java.math.BigDecimal">
		SELECT
			IFNULL(SUM(sales_money),0)
		FROM
			mem_colonel_target
		WHERE
			colonel_id = #{colonelId}
			AND target_year = #{targetYear}
			AND target_month = #{targetMonth}
			AND status = 0
			AND del_flag = 0
	</select>
</mapper>