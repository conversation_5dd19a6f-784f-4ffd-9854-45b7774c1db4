<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemColonelVisitLogMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 根据时间获取业务员最新的一条拜访记录 -->
    <select id="getLatestRecordsForEachColonel" resultType="com.zksr.member.controller.visitLog.vo.MemColonelVisitLogRespVO">
        SELECT m.*,
               c.colonel_name colonel<PERSON><PERSON>,
               b.branch_name branchName
        FROM (
                 SELECT colonel_id, MAX(create_time) AS maxCreateDate
                 FROM mem_colonel_visit_log
                 GROUP BY colonel_id
             ) latest_dates
                 JOIN mem_colonel_visit_log m
                      ON latest_dates.colonel_id = m.colonel_id AND latest_dates.maxCreateDate = m.create_time
                 LEFT JOIN mem_colonel c ON m.colonel_id = c.colonel_id
                 LEFT JOIN mem_branch b ON m.branch_id = b.branch_id
        WHERE  1=1 and m.create_time between #{startTime} and #{endTime}
    </select>

    <select id="getColonelVisitLogByParam" resultType="com.zksr.member.controller.visitLog.vo.MemColonelVisitLogCollectRespVO">
        SELECT
        c.colonel_Id AS colonelId,
        c.colonel_name AS colonelName,
        DATE_FORMAT(v.createDate, '%Y-%m-%d') AS createDate,
        v.sign_in_date AS startTime,
        v.sign_out_date AS endTime
        FROM
        (
        SELECT
        colonel_id,
        DATE(create_time) AS createDate,
        MIN(sign_in_date) AS sign_in_date,
        MAX(sign_out_date) AS sign_out_date
        FROM
        mem_colonel_visit_log
        WHERE
        create_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
        GROUP BY
        colonel_id, DATE(create_time)
        ) v
        LEFT JOIN mem_colonel c ON v.colonel_id = c.colonel_id
        where 1=1
        <if test="reqVO.areaId != null">
            AND c.area_id = #{reqVO.areaId}
        </if>
        <if test="reqVO.colonelId != null">
            AND c.colonel_id = #{reqVO.colonelId}
        </if>
        ORDER BY
        v.createDate;
    </select>
    <select id="getColonelVisitLogNumByDate" resultType="Long">
        SELECT
        IFNULL( SUM( day_counts.count_within_hour ), 0 ) AS total_count_within_hours
        FROM
        (
        SELECT
        DATE( create_time ) AS sign_in_day,
        COUNT(*) AS count_within_hour
        FROM
        mem_colonel_visit_log
        WHERE 1=1
        AND visit_flag = 1
        <if test="colonelId != null and colonelId !='' ">
            AND colonel_id = #{colonelId}
        </if>
        <if test="startTime!=null and endTime!=null">
            AND create_time &gt;= DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND create_time &lt; DATE_FORMAT(DATE_ADD(#{startTime}, INTERVAL 1 DAY), '%Y-%m-%d')
            AND DATE_FORMAT ( sign_out_date, '%H:%i' ) BETWEEN DATE_FORMAT (#{startTime}, '%H:%i' )
            AND DATE_FORMAT (#{endTime}, '%H:%i' )
        </if>
        <if test="visitIntervalTime!=null and visitIntervalTime !='' and visitIntervalTime =='short' ">
            AND CAST(visit_interval_time AS UNSIGNED) &lt; 300
        </if>
        <if test="visitIntervalTime!=null and visitIntervalTime !='' and visitIntervalTime =='medium'  ">
            AND (CAST(visit_interval_time AS UNSIGNED) &gt; 300 and CAST(visit_interval_time AS UNSIGNED) &lt; 1800)
        </if>
        <if test="visitIntervalTime!=null and visitIntervalTime !='' and visitIntervalTime =='long'">
            AND CAST(visit_interval_time AS UNSIGNED) &gt; 1800
        </if>
        GROUP BY
        sign_in_day
        ) day_counts;
    </select>
    <select id="getColonelVisitLogInfo" resultType="com.zksr.trade.api.order.dto.ColonelOrderDaySettleDTO"
            parameterType="com.zksr.trade.api.order.vo.ColonelOrderDaySettleVO">
        SELECT
            colonel_id,
            DATE_FORMAT(create_time, '%Y-%m-%d') AS settleCreateDate
            ,COUNT(DISTINCT branch_id) as visitQty
        FROM
            mem_colonel_visit_log
        WHERE
            sys_code = #{sysCode}
            AND colonel_id IN
                <foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            AND create_time BETWEEN CONCAT(#{startDate},' 00:00:00.000') AND CONCAT(#{endDate},' 23:59:59.999')
            AND visit_flag = 1
        GROUP BY
            colonel_id,
            DATE_FORMAT(create_time, '%Y-%m-%d')
    </select>

    <select id="getBranchVisitLogByBranchIdAndDate" resultType="com.zksr.common.elasticsearch.domain.EsColonelAppBranch">
        SELECT
            mcvl.branch_id AS branchId
            , mcvl.sys_code AS sysCode
            , DATE_FORMAT(mcvl.create_time, '%Y-%m') AS createYearMonth
            ,MAX(mcvl.create_time) AS lastVisitTime
        FROM
            mem_colonel_visit_log mcvl
        WHERE
	        mcvl.branch_id = #{branchId}
	        AND mcvl.sys_code = #{sysCode}
            AND mcvl.create_time BETWEEN CONCAT(#{startTime},' 00:00:00.000') AND CONCAT(#{endTime},' 23:59:59.999')
        GROUP BY
            mcvl.branch_id
            , mcvl.sys_code
            , DATE_FORMAT(mcvl.create_time, '%Y-%m')
        ORDER BY
            mcvl.branch_id
            , DATE_FORMAT(mcvl.create_time, '%Y-%m') ASC
    </select>

    <!-- 根据门店和拜访时间查询拜访记录 -->
    <select id="getBranchVisitLog" resultType="com.zksr.member.controller.visitLog.vo.MemColonelVisitLogRespVO">
        SELECT
            log.colonel_visit_log_id AS visit_log_id,
            log.sys_code AS system_code,
            log.create_by AS creator,
            log.create_time AS creation_time,
            log.update_by AS updater,
            log.update_time AS update_time,
            log.colonel_id AS colonel_id,
            log.branch_id AS branch_id,
            log.sign_in_longitude AS sign_in_longitude,
            log.sign_in_latitude AS sign_in_latitude,
            log.sign_in_address AS sign_in_address,
            log.sign_in_distance AS sign_in_distance,
            log.sign_in_img_urls AS sign_in_img_urls,
            log.sign_in_date AS sign_in_date,
            log.sign_out_longitude AS sign_out_longitude,
            log.sign_out_latitude AS sign_out_latitude,
            log.sign_out_address AS sign_out_address,
            log.sign_out_distance AS sign_out_distance,
            log.sign_out_date AS sign_out_date,
            log.visit_flag AS visit_status,
            log.visit_interval_time AS visit_interval,
            log.memo AS memo,
            mc.colonel_name AS colonel_name,
            mc.colonel_phone AS colonel_phone
        FROM mem_colonel_visit_log log
                 LEFT JOIN mem_colonel mc ON log.colonel_id = mc.colonel_id
        WHERE log.branch_id = #{branchId}
          AND log.create_time BETWEEN #{startTime} AND #{endTime}
          AND log.visit_flag = 1
        ORDER BY log.create_time;
    </select>

</mapper>