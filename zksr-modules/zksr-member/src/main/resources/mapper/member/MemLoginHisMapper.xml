<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemLoginHisMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectLoginTimeByUser" resultType="com.zksr.member.domain.MemLoginHis">
        select
        max(h.create_time) createTime,
        member_phone memberPhone
        from
        zksr_member.mem_login_his h
        where
        h.sys_code =#{sysCode}
        and h.member_phone in
        <foreach collection="userList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by
        h.sys_code ,
        h.member_phone;

    </select>
</mapper>