<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemSearchHisMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectSearchHisPage" resultType="com.zksr.member.controller.searchHis.vo.MemSearchHisRespVO">
        SELECT msh.search_his_id,
        msh.search_his_id AS searchHisId,
        msh.create_time AS createTime,
        msh.words AS words,
        mm.member_name AS memberName,
        mb.branch_id AS branchId,
        mb.branch_name AS branchName,
        mb.branch_addr AS branchAddress,
        mb.area_id AS areaId,
        mc.colonel_name AS colonelName
        FROM mem_search_his msh
        LEFT JOIN mem_branch mb ON msh.branch_id = mb.branch_id
        LEFT JOIN mem_member mm ON msh.member_id = mm.member_id
        LEFT JOIN mem_colonel mc ON mb.colonel_id = mc.colonel_id
        <where>
            <!-- 搜索时间类型  1：今天  2：本周  3：本月 筛选-->
            <if test="reqVO.searchTimeType == 1">
                AND msh.create_time &gt;= DATE_FORMAT(NOW(), '%Y-%m-%d')
            </if>
            <if test="reqVO.searchTimeType == 2">
                AND msh.create_time &gt;= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), '%Y-%m-%d')
            </if>
            <if test="reqVO.searchTimeType == 3">
                AND msh.create_time &gt;= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL DAYOFMONTH(CURDATE())-1 DAY),
                '%Y-%m-%d')
            </if>
            <!-- 搜索关键词-->
            <if test="reqVO.words != null and reqVO.words != ''">
                AND msh.words LIKE CONCAT('%', #{reqVO.words}, '%')
            </if>
            <!-- 业务员名称-->
            <if test="reqVO.colonelName != null and reqVO.colonelName != ''">
                AND mc.colonel_name LIKE CONCAT('%', #{reqVO.colonelName}, '%')
            </if>
            <!-- 业务员ID-->
            <if test="reqVO.colonelId != null">
                AND mc.colonel_id = #{reqVO.colonelId}
            </if>
            <if test="reqVO.branchName != null and reqVO.branchName != ''">
                AND mb.branch_name LIKE CONCAT('%', #{reqVO.branchName}, '%')
            </if>
            <if test="reqVO.branchId != null">
                AND mb.branch_id = #{reqVO.branchId}
            </if>
            <if test="reqVO.memberName != null and reqVO.memberName != ''">
                AND mm.member_name LIKE CONCAT('%', #{reqVO.memberName}, '%')
            </if>
            <if test="reqVO.areaId != null">
                AND mb.area_id = #{reqVO.areaId}
            </if>
        </where>
    </select>

</mapper>