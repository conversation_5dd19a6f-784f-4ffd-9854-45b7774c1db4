<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemBranchMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- 根据门店ID查询门店和业务员信息 -->
    <select id="getMemBranchByBranchId" resultType="com.zksr.member.api.branch.dto.BranchDTO">
        SELECT
	        branch.branch_id,
	        branch.branch_name,
	        branch.longitude,
	        branch.latitude,
	        branch.branch_addr,
	        branch.area_id,
	        branch.channel_id,
	        branch.`status`,
	        branch.colonel_id,
	        col.colonel_level,
			branch.contact_name,
			branch.contact_phone,
			branch.branch_images,
			branch.memo,
	        col.pcolonel_id AS pColonelId,
	        pcol.colonel_level AS pColonelLevel,
	        branch.three_area_city_id,
	        branch.lifecycle_stage
        FROM mem_branch branch
        LEFT JOIN mem_colonel col ON branch.colonel_id = col.colonel_id
        LEFT JOIN mem_colonel pcol ON col.pcolonel_id = pcol.colonel_id
        WHERE branch.branch_id = ${branchId}
    </select>

	<!-- 查询用户绑定门店信息列表 -->
	<select id="getBranchListByMemberId" resultType="com.zksr.member.api.branch.dto.BranchDTO">
        SELECT
	        branch.branch_id,
	        branch.branch_name,
	        branch.area_id,
	        branch.colonel_id,
	        branch.branch_addr,
	        branch.longitude,
	        branch.latitude,
	        branch.channel_id,
	        branch.group_id,
	        branch.contact_name,
	        branch.contact_phone,
	        branch.memo,
	        branch.expiration_date,
	        branch.`status`,
	        mbr.is_default
        FROM
            mem_branch branch
        	LEFT JOIN mem_branch_user mbr ON mbr.branch_id = branch.branch_id
        WHERE
            mbr.member_id = #{memberId}
			AND branch.status = 1
          	AND branch.del_flag = 0
        	<if test="areaId != null">
				AND branch.area_id = #{areaId}
			</if>
        	<if test='condition != null and condition != ""'>
				AND (
					branch.branch_name LIKE CONCAT('%', #{condition}, '%')
				    OR
					branch.branch_addr LIKE CONCAT('%', #{condition}, '%')
				)
			</if>
        ORDER BY
            branch.branch_id DESC
    </select>
	<!-- 查询用户绑定未审核门店信息列表 -->
	<select id="getUnauditedBranchListByMemberId" resultType="com.zksr.member.api.branch.dto.BranchDTO">
		SELECT
			mbr.branch_name,
			mbr.area_id,
			mbr.colonel_id,
			mbr.branch_addr,
			mbr.longitude,
			mbr.latitude,
			mbr.channel_id,
			mbr.contact_name,
			mbr.contact_phone,
			mbr.memo,
			mbr.status
		FROM
			mem_branch_register mbr
			LEFT JOIN mem_member member
			ON mbr.contact_phone = member.user_name
		WHERE
			mbr.status = 1
			AND mbr.approve_flag = 0
		  	AND member.member_id = #{memberId}
			<if test="areaId != null">
				AND mbr.area_id = #{areaId}
			</if>
			<if test='condition != null and condition != ""'>
				AND (
				mbr.branch_name LIKE CONCAT('%', #{condition}, '%')
				OR
				mbr.branch_addr LIKE CONCAT('%', #{condition}, '%')
				)
			</if>
		ORDER BY
			mbr.branch_register_id DESC;
	</select>

	<!-- 批量查询业务员绑定的门店列表 -->
	<select id="getBranchListByColonelIds" resultType="com.zksr.member.controller.branch.vo.MemBranchPageReqVO">
		SELECT
			branch.branch_id branchId,
			branch.branch_name branchName,
			branch.area_id areaId,
			branch.colonel_id colonelId,
			branch.branch_addr branchAddr,
			branch.longitude longitude,
			branch.latitude latitude,
			branch.channel_id channelId,
			branch.group_id groupId,
			branch.contact_name contactName,
			branch.contact_phone contactPhone,
			branch.memo memo,
			branch.expiration_date expirationDate,
			branch.`status`status,
			branch.branch_images branchImages
		FROM
		    mem_branch branch
		WHERE
			branch.del_flag = 0
			<if test="null != colonelIds and colonelIds.size > 0 ">
				and branch.colonel_id in
				<foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
					#{colonelId}
				</foreach>
			</if>
			<if test="reqVO.keyword !=null and reqVO.keyword !='' ">
				and (branch.branch_name like concat('%', #{reqVO.keyword}, '%')
				or branch.branch_addr like concat('%', #{reqVO.keyword}, '%')
				or branch.contact_name like concat('%', #{reqVO.keyword}, '%') )
			</if>
	</select>

	<update id="updateStatusByBranchIds">
		update
		    mem_branch
		set
		    status = #{branchDTO.status},
		    update_time = now(),
			expiration_date = null
		where
			sys_code = #{branchDTO.sysCode}
		  	and branch_id in
			<foreach collection="branchDTO.branchIds" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
	</update>


	<!-- 查询筛选条件内已拜访和未拜访的门店 -->
	<select id="getBranchListByVisit" resultType="com.zksr.member.controller.branch.vo.MemBranchPageReqVO">
		SELECT
		branch.branch_id AS branchId,
		branch.branch_name AS branchName,
		branch.colonel_id AS colonelId,
		branch.branch_addr AS branchAddr,
		branch.contact_name AS contactName,
		branch.branch_images branchImages,
		branch.contact_phone AS contactPhone
		FROM mem_branch branch
		<if test="reqVO.visitStatus == 0 ">
			LEFT JOIN (
			SELECT DISTINCT branch_id
			FROM mem_colonel_visit_log
			WHERE create_time BETWEEN #{reqVO.startTime} and #{reqVO.endTime} and visit_flag = 1
			) AS visited_branches ON branch.branch_id = visited_branches.branch_id
		</if>
		<if test="reqVO.visitStatus == 1">
			LEFT JOIN (
			SELECT DISTINCT branch_id
			FROM mem_colonel_visit_log
			WHERE create_time BETWEEN #{reqVO.startTime} and #{reqVO.endTime}
			) AS visited_branches ON branch.branch_id = visited_branches.branch_id
		</if>
		<if test="null != colonelIds and colonelIds.size > 0">
			INNER JOIN (SELECT DISTINCT colonel_id FROM mem_branch WHERE colonel_id IN
			<foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
				#{colonelId}
			</foreach>) filter ON branch.colonel_id = filter.colonel_id
		</if>
		WHERE
		    branch.del_flag = 0
			<if test="reqVO.visitStatus == 0">
				AND visited_branches.branch_id IS NOT NULL
			</if>
			<if test="reqVO.visitStatus == 1">
				AND visited_branches.branch_id IS NULL
			</if>
		order by
		    branch.branch_id DESC
	</select>

	<select id="getBranchIdListByVisit" resultType="java.lang.Long">
		SELECT
			branch.branch_id
		FROM
			mem_branch branch
			LEFT JOIN (
				SELECT
				    DISTINCT branch_id
				FROM
				    mem_colonel_visit_log
				WHERE
				    create_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
					<if test="null != reqVO.visitStatus and reqVO.visitStatus == '0'.toString()">
						AND visit_flag = 1
					</if>
			) AS visited_branches ON branch.branch_id = visited_branches.branch_id
		WHERE
			branch.del_flag = 0
			AND branch.status = 1
			AND branch.colonel_id IN
			<foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
				#{colonelId}
			</foreach>
			<choose>
				<when test="null != reqVO.visitStatus and reqVO.visitStatus == '0'.toString()">
					AND visited_branches.branch_id IS NOT NULL
				</when>
				<otherwise>
					AND visited_branches.branch_id IS NULL
				</otherwise>
			</choose>
	</select>

    <select id="selectBranchByTimeRange"
            resultType="com.zksr.common.core.domain.vo.openapi.syncCall.SyncBranchCallDTO">
		SELECT
			mb.*,
			mc.user_id AS userId,
			mc.colonel_name AS colonelName
		FROM
			mem_branch mb
			LEFT JOIN mem_colonel mc ON mb.colonel_id = mc.colonel_id
			WHERE
			mb.del_flag = 0
			<!-- 按区域ID过滤 -->
			<if test="areaList != null and areaList.size() > 0">
				AND mb.area_id IN
				<foreach collection="areaList" item="area" open="(" separator="," close=")">
					#{area.areaId}
				</foreach>
			</if>
		ORDER BY
			mb.branch_id DESC
	</select>
	<select id="getBranchCountFromOtherSource"
			resultType="com.zksr.member.api.branch.dto.ColonelBranchCountDTO">
		SELECT
			colonel_id,
			COUNT(*) AS branch_count
		FROM
			mem_branch
		WHERE
			colonel_id IN
			<foreach collection="colonelIds" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
			AND del_flag = 0
			AND status = 1
			AND DATE_FORMAT(create_time, '%Y%m') = #{currentMonthId}
		GROUP BY
			colonel_id
	</select>

	<select id="getHomePagesBranchData" resultType="com.zksr.report.api.homePages.dto.HomePagesBranchDataRespDTO">
		SELECT
			resTable.sys_code,
			<if test="null != isDc and isDc != 0">
				resTable.area_id,
			</if>
			SUM(resTable.branchAddQty) AS branchAddQty,
			SUM(resTable.beforeBranchAddQty) AS beforeBranchAddQty,
			SUM(resTable.branchTotalQty) AS branchTotalQty,
			SUM(resTable.beforeBranchTotalQty) AS beforeBranchTotalQty,
			SUM(resTable.visitBranchQty) AS visitBranchQty,
			SUM(resTable.beforeVisitBranchQty) AS beforeVisitBranchQty,
			SUM(resTable.colonelTotalQty) AS colonelTotalQty,
			SUM(resTable.loginBranchQty) AS loginBranchQty,
			SUM(resTable.beforeLoginBranchQty) AS beforeLoginBranchQty
		FROM (
			SELECT
				mb.sys_code,
				<if test="null != isDc and isDc != 0">
					mb.area_id,
				</if>
				COUNT(DISTINCT CASE WHEN DATE_FORMAT(mb.create_time, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d') THEN mb.branch_id END) AS branchAddQty,
				COUNT(DISTINCT CASE WHEN DATE_FORMAT(mb.create_time, '%Y%m%d') = DATE_FORMAT(DATE_SUB(now(), INTERVAL 1 DAY), '%Y%m%d') THEN mb.branch_id END) AS beforeBranchAddQty,
				COUNT(DISTINCT mb.branch_id) AS branchTotalQty,
				COUNT(DISTINCT CASE WHEN DATE_FORMAT(mb.create_time, '%Y%m%d') &lt; DATE_FORMAT(now(), '%Y%m%d') THEN mb.branch_id END) AS beforeBranchTotalQty,
				0 AS visitBranchQty,
				0 AS beforeVisitBranchQty,
				0 AS colonelTotalQty,
				0 AS loginBranchQty,
				0 AS beforeLoginBranchQty
			FROM
				mem_branch mb
			WHERE
				mb.sys_code = #{sysCode}
				AND mb.create_time &lt;= CONCAT(#{endDate}, ' 23:59:59.999')
			GROUP BY
				mb.sys_code
				<if test="null != isDc and isDc != 0">
					, mb.area_id
				</if>

			UNION ALL

			SELECT
				mcvl.sys_code,
				<if test="null != isDc and isDc != 0">
					mb.area_id,
				</if>
				0 AS branchAddQty,
				0 AS beforeBranchAddQty,
				0 AS branchTotalQty,
				0 AS beforeBranchTotalQty,
				COUNT(DISTINCT CASE WHEN DATE_FORMAT(mcvl.create_time, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d') THEN mcvl.branch_id END) AS visitBranchQty,
				COUNT(DISTINCT CASE WHEN DATE_FORMAT(mcvl.create_time, '%Y%m%d') = DATE_FORMAT(DATE_SUB(now(), INTERVAL 1 DAY), '%Y%m%d') THEN mcvl.branch_id END) AS beforeVisitBranchQty,
				0 AS colonelTotalQty,
				0 AS loginBranchQty,
				0 AS beforeLoginBranchQty
			FROM
				mem_colonel_visit_log mcvl
				LEFT JOIn mem_branch mb ON mcvl.sys_code = mb.sys_code AND mcvl.branch_id = mb.branch_id
			WHERE
				mcvl.sys_code = #{sysCode}
				AND mcvl.visit_flag = 1
				AND mcvl.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
			GROUP BY
				mcvl.sys_code
				<if test="null != isDc and isDc != 0">
					, mb.area_id
				</if>

			UNION ALL

			SELECT
				mc.sys_code,
				<if test="null != isDc and isDc != 0">
					mc.area_id,
				</if>
				0 AS branchAddQty,
				0 AS beforeBranchAddQty,
				0 AS branchTotalQty,
				0 AS beforeBranchTotalQty,
				0 AS visitBranchQty,
				0 AS beforeVisitBranchQty,
				COUNT(DISTINCT mc.colonel_id) AS colonelTotalQty,
				0 AS loginBranchQty,
				0 AS beforeLoginBranchQty
			FROM
				mem_colonel mc
			WHERE
				mc.sys_code = #{sysCode}
				AND mc.create_time &lt;= CONCAT(#{endDate}, ' 23:59:59.999')
			GROUP BY
				mc.sys_code
				<if test="null != isDc and isDc != 0">
					, mc.area_id
				</if>

			UNION ALL

			SELECT
				mlh.sys_code,
				<if test="null != isDc and isDc != 0">
					mb.area_id,
				</if>
				0 AS branchAddQty,
				0 AS beforeBranchAddQty,
				0 AS branchTotalQty,
				0 AS beforeBranchTotalQty,
				0 AS visitBranchQty,
				0 AS beforeVisitBranchQty,
				0 AS colonelTotalQty,
				COUNT(DISTINCT CASE WHEN DATE_FORMAT(mlh.create_time, '%Y%m%d') = DATE_FORMAT(#{startDate}, '%Y%m%d') THEN mlh.branch_id END) AS loginBranchQty,
				COUNT(DISTINCT CASE WHEN DATE_FORMAT(mlh.create_time, '%Y%m%d') = DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), '%Y%m%d') THEN mlh.branch_id END) AS beforeLoginBranchQty
			FROM
				mem_login_his mlh
				LEFT JOIN mem_branch mb ON mlh.branch_id = mb.branch_id
			WHERE
				mlh.sys_code = #{sysCode}
				AND mlh.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
			GROUP BY
				mlh.sys_code
				<if test="null != isDc and isDc != 0">
					, mb.area_id
				</if>
		) resTable
		GROUP BY
			resTable.sys_code
			<if test="null != isDc and isDc != 0">
				, resTable.area_id
			</if>
	</select>

	<select id="getBranchRegisterTime" resultType="java.util.Date">
		SELECT
			CASE
				WHEN mbr.create_time IS NOT NULL THEN mbr.create_time
				WHEN mmr.create_time IS NOT NULL THEN mmr.create_time
				ELSE mbh.create_time
				END AS register_time
		FROM
			mem_branch mbh
				LEFT JOIN mem_branch_register mbr ON mbh.branch_id = mbr.branch_id
				LEFT JOIN mem_member_register mmr ON mbh.branch_id = mmr.branch_id
		WHERE
			mbh.branch_id = #{branchId} limit 1
	</select>

	<select id="getColonelBranchLifecycle" resultType="com.zksr.member.api.branchLifecycle.dto.ColonelBranchLifecycleQtyDTO">
		SELECT
			count( 1 ) branchQty,
			sum( CASE lifecycle_stage WHEN 1 THEN 1 ELSE 0 END ) newCustomerQty,
			sum( CASE lifecycle_stage WHEN 2 THEN 1 ELSE 0 END ) activeQty,
			sum( CASE lifecycle_stage WHEN 3 THEN 1 ELSE 0 END ) silentQty,
			sum( CASE lifecycle_stage WHEN 4 THEN 1 ELSE 0 END ) lostProtectionPeriodQty
		FROM
			mem_branch
		WHERE
			lifecycle_stage is not null
		    AND colonel_id = #{colonelId}
		    AND del_flag = 0
		    AND STATUS = 1

	</select>
    <select id="selectPage" resultType="com.zksr.member.domain.MemBranch">
		SELECT mem_branch.*
		FROM mem_branch mem_branch
		<!-- 动态JOIN：只有在需要时才进行JOIN -->
		<if test="reqVO.registerStartTime != null and reqVO.registerEndTime != null">
			left join mem_branch_register mbr on mem_branch.branch_id = mbr.branch_id
			left join mem_member_register mmr on mem_branch.branch_id = mmr.branch_id
		</if>
		<where>
			<if test="reqVO.branchId != null">
				AND mem_branch.branch_id LIKE CONCAT('%', #{reqVO.branchId}, '%')
			</if>
			<if test="reqVO.sysCode != null">
				AND mem_branch.sys_code = #{reqVO.sysCode}
			</if>
			<if test="reqVO.branchName != null">
				AND mem_branch.branch_name LIKE CONCAT('%', #{reqVO.branchName}, '%')
			</if>
			<if test="reqVO.areaId != null">
				AND mem_branch.area_id = #{reqVO.areaId}
			</if>
			<if test="reqVO.areaIds != null and reqVO.areaIds.size() > 0">
				AND mem_branch.area_id IN
				<foreach collection="reqVO.areaIds" item="id" open="(" separator="," close=")">
					#{id}
				</foreach>
			</if>
			<if test="reqVO.colonelId != null">
				AND mem_branch.colonel_id = #{reqVO.colonelId}
			</if>
			<if test="reqVO.branchAddr != null">
				AND mem_branch.branch_addr LIKE CONCAT('%', #{reqVO.branchAddr}, '%')
			</if>
			<if test="reqVO.longitude != null">
				AND mem_branch.longitude = #{reqVO.longitude}
			</if>
			<if test="reqVO.latitude != null">
				AND mem_branch.latitude = #{reqVO.latitude}
			</if>
			<if test="reqVO.channelId != null">
				AND mem_branch.channel_id = #{reqVO.channelId}
			</if>
			<if test="reqVO.groupId != null">
				AND mem_branch.group_id = #{reqVO.groupId}
			</if>
			<if test="reqVO.contactName != null and reqVO.contactName != ''">
				AND mem_branch.contact_name LIKE CONCAT('%', #{reqVO.contactName}, '%')
			</if>
			<if test="reqVO.contactPhone != null and reqVO.contactPhone != ''">
				AND mem_branch.contact_phone LIKE CONCAT('%', #{reqVO.contactPhone}, '%')
			</if>
			<if test="reqVO.memo != null">
				AND mem_branch.memo = #{reqVO.memo}
			</if>
			<if test="reqVO.status != null">
				AND mem_branch.status = #{reqVO.status}
			</if>
			<if test="reqVO.firstOrderFlag != null">
				<if test="reqVO.firstOrderFlag == 0">
					AND (mem_branch.first_order_flag = 0 OR mem_branch.first_order_flag IS NULL)
				</if>
				<if test="reqVO.firstOrderFlag == 1">
					AND mem_branch.first_order_flag = 1
				</if>
			</if>
			<if test="reqVO.colonelIds != null and reqVO.colonelIds.size() > 0">
				AND mem_branch.colonel_id IN
				<foreach collection="reqVO.colonelIds" item="id" open="(" separator="," close=")">
					#{id}
				</foreach>
			</if>
			<if test="reqVO.branchIds != null and reqVO.branchIds.size() > 0">
				AND mem_branch.branch_id IN
				<foreach collection="reqVO.branchIds" item="id" open="(" separator="," close=")">
					#{id}
				</foreach>
			</if>
			<if test="reqVO.branchNo != null">
				AND mem_branch.branch_no LIKE CONCAT('%', #{reqVO.branchNo}, '%')
			</if>
			<!-- 动态应用条件 -->
			<if test="reqVO.isOpenSeas != null and reqVO.isOpenSeas == 1">
				AND mem_branch.colonel_id IS NOT NULL
			</if>
			<if test="reqVO.isOpenSeas != null and reqVO.isOpenSeas == 2">
				AND mem_branch.colonel_id IS NULL
			</if>
			<if test="reqVO.registerStartTime != null and reqVO.registerEndTime != null ">
				AND (
				(mbr.create_time BETWEEN #{reqVO.registerStartTime} AND #{reqVO.registerEndTime})
				OR
				(mmr.create_time BETWEEN #{reqVO.registerStartTime} AND #{reqVO.registerEndTime})
				 <if test="reqVO.registerType == null or reqVO.registerType == 2">
					 OR
					 (mem_branch.create_time BETWEEN #{reqVO.registerStartTime} AND #{reqVO.registerEndTime})
				</if>
				)
			</if>
			<if test="reqVO.registerType != null and reqVO.registerType == 0">
				AND  EXISTS(SELECT 1 FROM mem_member_register mmr WHERE mmr.branch_id = mem_branch.branch_id)
			</if>
			<if test="reqVO.registerType != null and reqVO.registerType == 1">
				AND  EXISTS(SELECT 1 FROM mem_branch_register mbr WHERE mbr.branch_id = mem_branch.branch_id)

			</if>
			<if test="reqVO.registerType != null and reqVO.registerType == 2">
				AND ( NOT EXISTS(SELECT 1 FROM mem_branch_register mbr WHERE mbr.branch_id = mem_branch.branch_id)
				AND
				NOT EXISTS(SELECT 1 FROM mem_member_register mmr WHERE mmr.branch_id = mem_branch.branch_id) )
			</if>
			<if test="reqVO.lifecycleStage != null">
				AND mem_branch.lifecycle_stage = #{reqVO.lifecycleStage}
			</if>
			AND mem_branch.del_flag = 0
			${reqVO.params.dataScope}
		</where>
		ORDER BY mem_branch.branch_id DESC
	</select>
	<select id="getFirstSaleBranchNumByColonelIdAndYearMonth" resultType="java.lang.Long">
		SELECT
			count( 1 )
		FROM
			mem_branch
		WHERE
			colonel_id = #{colonelId}
		  AND SUBSTRING( first_order_no, 3, 4 ) = #{yearMonth}
	</select>

</mapper>
