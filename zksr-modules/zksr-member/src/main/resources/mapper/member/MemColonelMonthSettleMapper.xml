<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemColonelMonthSettleMapper">

	<select id="getColonelSettleMonthList"
			resultType="com.zksr.member.controller.colonelTarget.vo.MemColonelActualSaleRespVO">
		SELECT
			mcms.colonel_id,
			mcms.settle_month_date
			,IFNULL((mcms.branch_order_amt + mcms.business_order_amt - mcms.branch_refund_amt), 0) AS saleAmt
			,IFNULL(mcms.add_branch_qty, 0) AS addBranchQty
			,IFNULL(mcms.sale_branch_qty, 0) AS saleBranchQty
			,IFNULL(mcms.visit_qty, 0) AS visitQty
			,IFNULL(mcms.order_qty, 0) AS orderCount
			,ROUND(IFNULL((mcms.branch_order_amt + mcms.business_order_amt - mcms.branch_refund_amt), 0)/IFNULL(mcms.order_qty, 1), 2) AS avgOrderValue
			,mcms.settle_month_date AS targetYearMonth
		FROM
			mem_colonel_month_settle mcms
		WHERE
			mcms.colonel_id IN
				<foreach collection="colonelIds" item="colonel" open="(" separator="," close=")">
					#{colonel}
				</foreach>
		  	AND mcms.settle_month_date IN
				<foreach collection="settleMonthDates" item="settleMonthDate" open="(" separator="," close=")">
					#{settleMonthDate}
				</foreach>
		ORDER BY mcms.colonel_id
	</select>
</mapper>