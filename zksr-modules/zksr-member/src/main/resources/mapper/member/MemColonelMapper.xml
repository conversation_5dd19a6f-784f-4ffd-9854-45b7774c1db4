<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemColonelMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 分页查询未绑定关系的业务员 -->
    <select id="getColonelNotRelationPage" resultType="com.zksr.member.api.colonel.vo.MemColonelRespVO">
        SELECT
            colonel.colonel_id,
			colonel.colonel_name,
			colonel.sex,
			colonel.colonel_level,
			colonel.is_colonel_admin,
			colonel.pcolonel_id,
			pColonel.colonel_name AS pColonelName,
			colonel.colonel_phone,
			colonel.birthday,
			colonel.birthplace,
			colonel.dept_id,
			colonel.app_order_price_adjust,
			colonel.app_after_price_adjust,
			colonel.order_auto_approve
        FROM
            mem_colonel colonel
			LEFT JOIN mem_colonel pColonel on colonel.pcolonel_id = pColonel.colonel_id
        WHERE
            colonel.colonel_id NOT IN ( SELECT colonel_id FROM mem_colonel_relation )
          	AND colonel.del_flag = 0
            <if test=" null != colonelName and ''.toString() != colonelName ">
				AND (colonel.colonel_name like '%${colonelName}%' or colonel.colonel_phone like '%${colonelName}%' or colonel.colonel_id like '%${colonelName}%')
			</if>
    </select>

	<!-- 分页查询业务员统计列表 -->
	<select id="getColonelStatisticsListPage" resultType="com.zksr.member.controller.colonelApp.vo.MemColonelStatisticsListRespVo">
		SELECT
			c.colonel_id colonelId,
			c.colonel_name colonelName,
			COUNT(b.branch_id) AS customersTotal
		FROM
			mem_colonel c
				LEFT JOIN
			mem_branch b ON c.colonel_id = b.colonel_id
		WHERE
		    1=1
		<if test="null != colonelIds and colonelIds.size > 0 ">
			and c.colonel_id in
			<foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
				#{colonelId}
			</foreach>
		</if>
		<if test="areaId !=null and areaId !='' ">
			and c.area_id = #{areaId}
		</if>
		GROUP BY
			c.colonel_id, c.colonel_name
		ORDER BY
		    customersTotal DESC, c.colonel_id
	</select>

	<select id="getColonelListByAreaIds" resultType="com.zksr.member.domain.MemColonel">
		SELECT
			*
		FROM
			mem_colonel mc
		WHERE
			mc.del_flag = 0
			<if test="null != areaIds and areaIds.size > 0 ">
				AND mc.area_id IN
				<foreach collection="areaIds" item="areaId" open="(" separator="," close=")">
					#{areaId}
				</foreach>
			</if>
	</select>
    <select id="selectListPage" resultType="com.zksr.member.domain.MemColonel">
		SELECT
		    *
		FROM
		    mem_colonel mc
		WHERE
			mc.del_flag = 0
			<if test="null != reqVo.colonelId"> AND mc.colonel_id = #{reqVo.colonelId} </if>
			<if test="null != reqVo.colonelName and reqVo.colonelName != ''"> AND mc.colonel_name LIKE CONCAT('%', #{reqVo.colonelName},'%')  </if>
			<if test="null != reqVo.colonelPhone and reqVo.colonelPhone != ''"> AND mc.colonel_phone LIKE CONCAT('%', #{reqVo.colonelPhone},'%') </if>
			<if test="null != reqVo.status"> AND mc.status = #{reqVo.status} </if>
			<if test="null != reqVo.areaId"> AND mc.area_id = #{reqVo.areaId} </if>
			<if test="null != reqVo.auditState"> AND mc.audit_state = #{reqVo.auditState} </if>
			<if test="null != reqVo.isColonelAdmin"> AND mc.is_colonel_admin = #{reqVo.isColonelAdmin} </if>
			${reqVo.params.dataScope}
		ORDER BY
		    mc.colonel_id DESC
	</select>
</mapper>