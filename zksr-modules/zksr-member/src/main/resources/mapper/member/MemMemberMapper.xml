<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemMemberMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectPage" resultType="com.zksr.member.api.member.vo.MemMemberRespVO">
        SELECT
        member.member_id
        ,member.member_phone
        ,member.member_name
        ,member.user_name
        ,member.`status`
        ,userBranch.branchName
        ,member.memo
        FROM
        mem_member member
        INNER JOIN (
        SELECT
        mem.member_id,
        GROUP_CONCAT( branch.branch_name SEPARATOR ',' ) AS branchName
        FROM
        mem_member mem
        LEFT JOIN mem_branch_user branchUser ON mem.member_id = branchUser.member_id
        LEFT JOIN mem_branch branch ON branchUser.branch_id = branch.branch_id
        <where>
            <if test="reqVO.areaIdList != null and reqVO.areaIdList.size > 0">
                and branch.area_id IN
                <foreach collection="reqVO.areaIdList" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
            <if test="reqVO.dcId != null">
                OR mem.dc_id = #{reqVO.dcId}
            </if>
        </where>
        GROUP BY
        mem.member_id
        ) userBranch ON member.member_id = userBranch.member_id
        <where>
            <if test="null != reqVO.memberPhone and '' != reqVO.memberPhone ">
                AND member.member_phone like '%${reqVO.memberPhone}%'
            </if>
            <if test="null != reqVO.memberName and '' != reqVO.memberName ">
                AND member.member_name like '%${reqVO.memberName}%'
            </if>
            <if test="null != reqVO.userName and '' != reqVO.userName ">
                AND member.user_name like '%${reqVO.userName}%'
            </if>
            <if test="null != reqVO.status ">
                AND member.status = ${reqVO.status}
            </if>

            <if test="null != reqVO.branchName and '' != reqVO.branchName ">
                AND userBranch.branchName like '%${reqVO.branchName}%'
            </if>

            <!-- 这里过滤掉业务员代客下单生成的商城账号-->
            AND member.is_colonel = 0
        </where>
        ORDER BY member.member_id DESC
    </select>

    <update id="updateStatusByMemberIds">
        update
            mem_member
        set
            status = #{memberDTO.status},
            update_time = now(),
            expiration_date = null
        where
            sys_code = #{memberDTO.sysCode}
            and member_id in
            <foreach collection="memberDTO.memberIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </update>


    <!-- 更新用户信息 -->
    <update id="updateMemberUserInfoByMemberId" parameterType="com.zksr.member.domain.MemMember">
        UPDATE
            mem_member
        SET
            avatar = #{avatar}
            , update_time = now()
        WHERE member_id = #{memberId}
    </update>


</mapper>