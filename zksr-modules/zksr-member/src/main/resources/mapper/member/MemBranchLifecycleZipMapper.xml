<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemBranchLifecycleZipMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPageLifecycle" resultType="com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipRespVO">
        select
            blz.branch_id, -- 门店ID
            blz.lifecycle_stage, -- 门店生命周期阶段
            blz.start_date, -- 变更日期
            blz.last_lifecycle_stage, -- 上一个生命周期阶段
            blz.start_memo, -- 备注信息
            mbh.branch_no, -- 门店编号
            mbh.branch_name, -- 门店名称
            mbh.channel_id, -- 渠道ID
            mbh.area_id, -- 区域ID
            mbh.contact_name, -- 联系人姓名
            mbh.contact_phone, -- 联系人电话
            mcl.colonel_name, -- 业务员姓名
            case when mbr.create_time is not null then mbr.create_time
                 when mmr.create_time is not null then mmr.create_time
                else mbh.create_time end register_time -- 注册时间
            from
            mem_branch_lifecycle_zip blz
            inner join mem_branch  mbh on blz.branch_id = mbh.branch_id
            left join mem_colonel mcl on mbh.colonel_id = mcl.colonel_id
            left join mem_branch_register mbr on mbh.branch_id = mbr.branch_id
            left join mem_member_register mmr on mbh.branch_id = mmr.branch_id
            <where>
                <if test="reqVO.branchId != null">
                    and blz.branch_id = #{reqVO.branchId}
                </if>
                <if test="reqVO.lifecycleStage != null">
                    and blz.lifecycle_stage = #{reqVO.lifecycleStage}
                </if>
                <if test="reqVO.lastLifecycleStage != null">
                    and blz.last_lifecycle_stage = #{reqVO.lastLifecycleStage}
                </if>
                <if test="reqVO.changeStartDate != null and reqVO.changeEndDate != null">
                    and blz.start_date BETWEEN #{reqVO.changeStartDate} AND #{reqVO.changeEndDate}
                </if>
                <if test="reqVO.areaIdList != null and reqVO.areaIdList.size() > 0">
                    and mbh.area_id in
                    <foreach collection="reqVO.areaIdList" item="areaIdItem" separator="," open="(" close=")">
                        #{areaIdItem}
                    </foreach>
                </if>
                <if test="reqVO.channelId != null">
                    and mbh.channel_id = #{reqVO.channelId}
                </if>
                <if test="reqVO.colonelId != null">
                    and mbh.colonel_id = #{reqVO.colonelId}
                </if>
            </where>
            order by  blz.start_date desc,blz.end_date desc

    </select>

    <select id="getAllNewLifecycleStageBySysCode" resultType="com.zksr.member.domain.MemBranchLifecycleZip">
        SELECT
        zip.*
        FROM
        mem_branch_lifecycle_zip zip
        LEFT JOIN mem_branch mbh ON zip.branch_id = mbh.branch_id
        LEFT JOIN mem_branch_lifecycle_zip newerZip ON zip.branch_id = newerZip.branch_id
        AND zip.start_date &lt; newerZip.start_date
        WHERE
        mbh.sys_code = #{sysCode}
        AND mbh.del_flag = 0
        -- AND mbh.STATUS = 1  产品说 停用的 也需要筛选生命周期信息
        AND newerZip.branch_id IS NULL
        AND zip.sys_code = #{sysCode}
        <if test="null != branchIdList and branchIdList.size > 0">
            and zip.branch_id IN
            <foreach collection="branchIdList" item="branchId" open="(" separator="," close=")">
                #{branchId}
            </foreach>
        </if>
        -- 如果存在两条开始时间一致的 已 正处于生命周期的数据为首
        order by zip.end_date desc

    </select>


</mapper>