<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemColonelBranchTargetMapper">

	<select id="getMemColonelBranchTargetPageTotal"
			resultType="com.zksr.member.controller.colonelTarget.vo.ColonelBranchTargetRespVO"
			parameterType="com.zksr.member.controller.colonelTarget.vo.MemColonelBranchTargetPageReqVO">
		SELECT
		    mcbr.sys_code
		    , mcbr.branch_id
			, mcbr.target_sales_money
		FROM
			mem_colonel_branch_target mcbr
			LEFT JOIN mem_colonel_target mct ON mcbr.colonel_id = mct.colonel_id AND mcbr.target_year = mct.target_year AND mcbr.target_month = mct.target_month
		WHERE
			mcbr.colonel_id = #{colonelId}
			AND mcbr.target_year = #{targetYear}
			AND mcbr.target_month = #{targetMonth}
			<if test="null != branchName and branchName != ''">
				AND mcbr.branch_name LIKE concat('%', #{branchName}, '%')
			</if>
			<if test="null != branchContactPhone and branchContactPhone != ''">
				AND mcbr.branch_contact_phone = #{branchContactPhone}
			</if>
			<if test="null != branchId">
				AND mcbr.branch_id = #{branchId}
			</if>
			<if test="null != status and status != -1">
				AND mct.status = #{status}
			</if>
	</select>
	<select id="getMemColonelBranchTargetPage"
			resultType="com.zksr.member.controller.colonelTarget.vo.ColonelBranchTargetRespVO">
		SELECT
			mcbr.colonel_id
		    , mcbr.sys_code
			, mcbr.branch_id
			, mcbr.branch_name
			, mcbr.branch_contact_name
			, mcbr.branch_contact_phone
		    , mb.area_id
			, mcbr.target_year
			, mcbr.target_month
			, mcbr.target_sales_money
			, mcbr.memo
			, mct.sales_money AS colonelTargetSalesMoney
			, mcbr.colonel_branch_target_id
		FROM
			mem_colonel_branch_target mcbr
			LEFT JOIN mem_colonel_target mct ON mcbr.colonel_id = mct.colonel_id AND mcbr.target_year = mct.target_year AND mcbr.target_month = mct.target_month
			LEFT JOIN mem_branch mb ON mcbr.branch_id = mb.branch_id
		WHERE
			mcbr.colonel_id = #{reqVO.colonelId}
			AND mcbr.target_year = #{reqVO.targetYear}
			AND mcbr.target_month = #{reqVO.targetMonth}
			<if test="null != reqVO.branchName and reqVO.branchName != ''">
				AND mcbr.branch_name LIKE concat('%', #{reqVO.branchName}, '%')
			</if>
			<if test="null != reqVO.branchContactPhone and reqVO.branchContactPhone != ''">
				AND mcbr.branch_contact_phone = #{reqVO.branchContactPhone}
			</if>
			<if test="null != reqVO.branchId">
				AND mcbr.branch_id = #{reqVO.branchId}
			</if>
			<if test="null != reqVO.status and reqVO.status != -1">
				AND mct.status = #{reqVO.status}
			</if>
	</select>

	<update id="updateMemColonelBranchTarget" parameterType="com.zksr.member.controller.colonelApp.vo.ColonelAppBranchTargetUpdateVO">
		UPDATE
		    mem_colonel_branch_target
		SET
			target_sales_money = #{targetSalesMoney}
			, update_time = NOW()
		WHERE
			colonel_branch_target_id = #{colonelBranchTargetId}
	</update>
</mapper>