<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemColonelRelationMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectPage" resultType="com.zksr.member.controller.relation.vo.MemColonelRelationRespVO">
        SELECT
	        relation.colonel_relation_id
	        ,relation.admin_colonel_id
	        ,relation.colonel_id
	        ,colonel.colonel_name
	        ,colonel.dept_id
	        ,colonel.colonel_level
	        ,colonel.sex
            ,case when colonel.pcolonel_id =  relation.admin_colonel_id then 1 else 0 end isDefault
        FROM mem_colonel_relation relation
        LEFT JOIN mem_colonel colonel ON relation.colonel_id = colonel.colonel_id
        <where>
            <if test=" null != reqVO.colonelName and ''.toString() != reqVO.colonelName ">
                AND colonel.colonel_name like concat('%', #{reqVO.colonelName}, '%')
            </if>
            <if test=" null != reqVO.colonelPhone and ''.toString() != reqVO.colonelPhone ">
                AND colonel.colonel_phone like concat('%', #{reqVO.colonelPhone}, '%')
            </if>
            <if test=" null != reqVO.colonelId ">
                AND relation.colonel_id like concat('%', #{reqVO.colonelId}, '%')
            </if>
            <if test=" null != reqVO.adminColonelId ">
                AND relation.admin_colonel_id = #{reqVO.adminColonelId}
            </if>
            ${reqVO.params.dataScope}
        </where>
    </select>

    <delete id="deleteByColonelId">
        DELETE FROM mem_colonel_relation WHERE colonel_id = #{colonelId}
    </delete>
</mapper>