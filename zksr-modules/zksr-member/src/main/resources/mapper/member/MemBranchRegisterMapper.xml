<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemBranchRegisterMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getColonelBranchRegisterInfo" resultType="com.zksr.trade.api.order.dto.ColonelOrderDaySettleDTO"
            parameterType="com.zksr.trade.api.order.vo.ColonelOrderDaySettleVO">
        SELECT
            colonel_id
            ,DATE_FORMAT(approve_date, '%Y-%m-%d') AS settleCreateDate
            ,COUNT(1) AS addBranchQty
        FROM
            mem_branch_register
        WHERE
            sys_code = #{sysCode}
            AND colonel_id IN
                <foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            AND approve_flag = 1
            AND approve_date BETWEEN CONCAT(#{startDate},' 00:00:00.000') AND CONCAT(#{endDate},' 23:59:59.999')
        GROUP BY
            colonel_id,
            DATE_FORMAT(approve_date, '%Y-%m-%d')
    </select>
    <select id="getMemBranchRegisterByBranchId" resultType="com.zksr.member.domain.MemBranchRegister">
        select *
        from mem_branch_register
        where branch_id = #{branchId} limit 1
    </select>
</mapper>