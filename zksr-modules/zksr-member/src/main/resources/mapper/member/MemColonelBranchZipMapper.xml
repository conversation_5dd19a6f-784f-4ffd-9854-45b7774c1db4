<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemColonelBranchZipMapper">
	<update id="updateMemColonelBranchZip">
		update mem_colonel_branch_zip
		<set>
			<if test="endDate != null">end_date = #{endDate},</if>
		</set>
		where sys_code = #{sysCode}
		AND branch_id = #{branchId}
		AND colonel_id = #{colonelId}
	</update>
</mapper>
