<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemColonelDaySettleMapper">

	<!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 分页查询未绑定关系的业务员 -->
    <select id="getListBySysCodeAndDate" resultType="com.zksr.member.domain.MemColonelMonthSettle">
		SELECT
			table_monty.*
			 ,table_day30.visitDay30AvgQty
			 ,table_day30.addBranchDay30AvgQty
			 ,table_day30.saleBranchDay30AvgQty
		FROM (
			SELECT
				colonel_id
				,SUM(order_qty) AS orderQty
				,SUM(branch_order_amt) AS branchOrderAmt
				,SUM(business_order_qty) AS businessOrderQty
				,SUM(business_order_amt) AS businessOrderAmt
				,SUM(percentage_amt) AS percentageAmt
				,SUM(branch_refund_amt) AS branchRefundAmt
				,SUM(visit_qty) AS visitQty
				,SUM(add_branch_qty) AS addBranchQty
				,SUM(sale_branch_qty) AS saleBranchQty
				,(IFNULL(SUM(visit_qty), 0) / DAY(CURDATE())) AS visitMonthAvgQty
		   		,(IFNULL(SUM(add_branch_qty), 0) / DAY(CURDATE())) AS addBranchMonthAvgQty
		   		,(IFNULL(SUM(sale_branch_qty), 0) / DAY(CURDATE())) AS saleBranchMonthAvgQty
			FROM
				mem_colonel_day_settle
			WHERE
				sys_code = #{sysCode}
				AND settle_create_date between #{settleStartDate} AND #{settleEndDate}
				<if test="null != colonelId">
					AND colonel_id = #{colonelId}
				</if>
			GROUP BY
			    colonel_id
		) table_monty
		LEFT JOIN (
			SELECT
				colonel_id
				,(IFNULL(SUM(visit_qty), 0) / 30) AS visitDay30AvgQty
				,(IFNULL(SUM(add_branch_qty), 0) / 30) AS addBranchDay30AvgQty
				,(IFNULL(SUM(sale_branch_qty), 0) / 30) AS saleBranchDay30AvgQty
			FROM
			    mem_colonel_day_settle
			WHERE
			    sys_code = #{sysCode}
				AND settle_create_date &gt;=  DATE_SUB(CURDATE(),INTERVAL 30 DAY)
				<if test="null != colonelId">
					AND colonel_id = #{colonelId}
				</if>
			GROUP BY
			    colonel_id
		) table_day30 ON table_monty.colonel_id = table_day30.colonel_id
    </select>


	<delete id="deleteColonelDaySettleBySysCodeAndDate">
		DELETE FROM mem_colonel_day_settle
		WHERE
		    sys_code = #{sysCode}
			AND settle_create_date = #{date}
			<if test="null != colonelId">
				AND colonel_id = #{colonelId}
			</if>
	</delete>
</mapper>