<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemColonelHierarchyZipMapper">
	<update id="updateMemColonelHierarchyZip">
		update mem_colonel_hierarchy_zip
		<set>
			<if test="endDate != null">end_date = #{endDate},</if>
		</set>
		    where sys_code = #{sysCode}
		    AND pcolonel_id = #{pcolonelId}
			AND colonel_id = #{colonelId}
	</update>
</mapper>
