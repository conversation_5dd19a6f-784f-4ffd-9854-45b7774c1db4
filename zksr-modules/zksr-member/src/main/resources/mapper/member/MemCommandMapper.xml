<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.member.mapper.MemCommandMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- 分页查询返回Map 新-->
    <resultMap id="colonelAppRespMap" type="com.zksr.member.api.command.dto.CommandAddOrderRespDTO">
        <id property="commandId" column="command_id"/>
        <result property="branchId" column="order_no"/>
        <result property="branchId" column="pub_id"/>
        <result property="state" column="status"/>
        <collection property="commandAddOrderExecDTOList" ofType="com.zksr.member.api.command.dto.CommandAddOrderExecDTO">
            <id property="ordinaryCommandId" column="childCommandId"/>
            <result property="execRes" column="exec_res"/>
            <result property="ordinaryStatus" column="ordinaryStatus"/>
        </collection>
    </resultMap>

    <select id="getColonelAppCommandPage" parameterType="com.zksr.member.api.command.vo.CommandPageReqVO" resultMap="colonelAppRespMap">
        SELECT
            mc.command_id,
            mc.sys_code,
            mc.command_level,
            mc.pub_merchant_type,
            mc.pub_id,
            mc.status,

            mcd.command_id AS childCommandId,
            mcd.exec_res,
            mcd.status AS ordinaryStatus

        FROM
            mem_command mc
            LEFT JOIN (
                SELECT
                    command_id,
                    pid,
                    status,
                    exec_res,
                    memo
                FROM
                    mem_command
                WHERE
                    command_date BETWEEN CONCAT(#{searchDate},' 00:00:00.000') AND CONCAT(#{searchDate},' 23:59:59.999')
                    AND status != 0
                    AND command_level IN (1, 2)
            ) mcd ON mc.command_id = mcd.pid
        WHERE
            mc.command_level = 0
            AND mc.pub_merchant_type = #{pubMerchantType}
            AND mc.command_date BETWEEN CONCAT(#{searchDate},' 00:00:00.000') AND CONCAT(#{searchDate},' 23:59:59.999')
            AND mc.pub_id IN
            <foreach collection="branchIds" item="branchId" separator="," open="(" close=")">
                #{branchId}
            </foreach>
            <choose>
                <when test="null != commandState and commandState == 0"> /*待加单*/
                    AND mc.status != 0
                    AND NOT EXISTS (
                        SELECT
                            command_id
                        FROM
                            mem_command
                        WHERE
                            pid = mc.command_id
                            AND command_level IN (1, 2)
                            AND status = 1
                    )
                </when>
                <when test="null != commandState and commandState == 1"> /*加单中*/
                    AND mc.status != 0
                    AND mcd.status = 1
                </when>
                <when test="null != commandState and commandState == 2"> /*加单完成*/
                    AND mcd.status = 2
                </when>
                <otherwise> /*其他状态直接查询为空*/
                    mc.status = -1
                </otherwise>
            </choose>
    </select>
</mapper>