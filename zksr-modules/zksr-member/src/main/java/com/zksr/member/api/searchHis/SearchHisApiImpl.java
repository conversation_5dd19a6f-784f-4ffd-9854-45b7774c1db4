package com.zksr.member.api.searchHis;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.member.api.searchHis.vo.SearchHisReqVO;
import com.zksr.member.controller.searchHis.vo.MemSearchHisSaveReqVO;
import com.zksr.member.service.IMemSearchHisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @Date 2025/1/16 17:28
 * @搜索词条rpc服务实现
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Slf4j
@ApiIgnore
@InnerAuth
public class SearchHisApiImpl implements SearchHisApi {

    @Autowired
    private IMemSearchHisService memSearchHis;

    @Override
    public CommonResult<Long> addSearchHis(SearchHisReqVO searchHisReqVO) {
        Long searchHisId =memSearchHis.insertMemSearchHis(HutoolBeanUtils.toBean(searchHisReqVO, MemSearchHisSaveReqVO.class));
        return CommonResult.success(searchHisId);
    }
}
