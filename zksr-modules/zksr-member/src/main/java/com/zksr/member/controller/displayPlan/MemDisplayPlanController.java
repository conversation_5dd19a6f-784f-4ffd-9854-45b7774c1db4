package com.zksr.member.controller.displayPlan;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemDisplayPlan;
import com.zksr.member.service.IMemDisplayPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanPageReqVO;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanSaveReqVO;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 陈列计划Controller
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Api(tags = "管理后台 - 陈列计划接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/displayPlan")
public class MemDisplayPlanController {
    @Autowired
    private IMemDisplayPlanService memDisplayPlanService;

    /**
     * 新增陈列计划
     */
    @ApiOperation(value = "新增陈列计划", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "陈列计划", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemDisplayPlanSaveReqVO createReqVO) {
        return success(memDisplayPlanService.insertMemDisplayPlan(createReqVO));
    }

    /**
     * 修改陈列计划
     */
    @ApiOperation(value = "修改陈列计划", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "陈列计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemDisplayPlanSaveReqVO updateReqVO) {
            memDisplayPlanService.updateMemDisplayPlan(updateReqVO);
        return success(true);
    }

    /**
     * 删除陈列计划
     */
    @ApiOperation(value = "删除陈列计划", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "陈列计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] planIds) {
        memDisplayPlanService.deleteMemDisplayPlanByPlanIds(planIds);
        return success(true);
    }

    /**
     * 获取陈列计划详细信息
     */
    @ApiOperation(value = "获得陈列计划详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{planId}")
    public CommonResult<MemDisplayPlanRespVO> getInfo(@PathVariable("planId") Long planId) {
        MemDisplayPlan memDisplayPlan = memDisplayPlanService.getMemDisplayPlan(planId);
        return success(HutoolBeanUtils.toBean(memDisplayPlan, MemDisplayPlanRespVO.class));
    }

    /**
     * 分页查询陈列计划
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得陈列计划分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemDisplayPlanRespVO>> getPage(@Valid MemDisplayPlanPageReqVO pageReqVO) {
        PageResult<MemDisplayPlan> pageResult = memDisplayPlanService.getMemDisplayPlanPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, MemDisplayPlanRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:plan:add";
        /** 编辑 */
        public static final String EDIT = "member:plan:edit";
        /** 删除 */
        public static final String DELETE = "member:plan:remove";
        /** 列表 */
        public static final String LIST = "member:plan:list";
        /** 查询 */
        public static final String GET = "member:plan:query";
        /** 停用 */
        public static final String DISABLE = "member:plan:disable";
        /** 启用 */
        public static final String ENABLE = "member:plan:enable";
    }
}
