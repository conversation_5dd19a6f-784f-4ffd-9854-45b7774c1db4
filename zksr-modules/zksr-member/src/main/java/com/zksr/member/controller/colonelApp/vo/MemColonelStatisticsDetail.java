package com.zksr.member.controller.colonelApp.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2024/8/6 15:05
 * @业务员App - 业务员列表统计明细
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("业务员App - 业务员列表统计明细")
public class MemColonelStatisticsDetail {

    /** 今日 */
    @ApiModelProperty(value = "今日")
    @Excel(name = "今日")
    private Long todayNum=0L;

    /** 昨日 */
    @ApiModelProperty(value = "昨日")
    @Excel(name = "昨日")
    private Long yesterdayNum=0L;;

    /** 前日 */
    @ApiModelProperty(value = "前日")
    @Excel(name = "前日")
    private Long theDayBeforeYesterdayNum=0L;;

    /** 日平均 */
    @ApiModelProperty(value = "日平均")
    @Excel(name = "日平均")
    private Double dailyMeanNum=0.0;

    /** 本月 */
    @ApiModelProperty(value = "本月")
    @Excel(name = "本月")
    private Long currentMonthNum=0L;;

}
