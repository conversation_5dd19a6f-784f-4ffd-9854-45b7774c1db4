package com.zksr.member.convert.address;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.member.MemMemberAddressRespDTO;
import com.zksr.member.api.member.dto.MemMemberAddressPageReqDTO;
import com.zksr.member.api.member.dto.MemMemberAddressSaveReqDTO;
import com.zksr.member.api.member.vo.MemberAddressRespDTO;
import com.zksr.member.controller.address.vo.MemMemberAddressPageReqVO;
import com.zksr.member.domain.MemMemberAddress;
import com.zksr.member.controller.address.vo.MemMemberAddressRespVO;
import com.zksr.member.controller.address.vo.MemMemberAddressSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 用户地址 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-06-30
*/
@Mapper
public interface MemMemberAddressConvert {

    MemMemberAddressConvert INSTANCE = Mappers.getMapper(MemMemberAddressConvert.class);

    MemMemberAddressRespVO convert(MemMemberAddress memMemberAddress);

    MemMemberAddress convert(MemMemberAddressSaveReqVO memMemberAddressSaveReq);

    PageResult<MemMemberAddressRespVO> convertPage(PageResult<MemMemberAddress> memMemberAddressPage);

    MemMemberAddressSaveReqDTO convert2MemMemberAddressSaveReqDTO(MemMemberAddressSaveReqVO vo);

    MemMemberAddressSaveReqVO convert2MemMemberAddressSaveReqVO(MemMemberAddressSaveReqDTO vo);

    MemMemberAddressPageReqVO convert2MemMemberAddressPageReqVO(MemMemberAddressPageReqDTO pageReqVO);

    PageResult<MemberAddressRespDTO> convert2MemberAddressRespDTO(PageResult<MemMemberAddress> memMemberAddressPage);

    MemMemberAddressRespDTO convert2MemMemberAddressRespDTO(MemMemberAddress memMemberAddress);
}