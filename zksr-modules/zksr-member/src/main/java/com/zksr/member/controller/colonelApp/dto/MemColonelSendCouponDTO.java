package com.zksr.member.controller.colonelApp.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("业务员App - 业务员发券请求实体类")
public class MemColonelSendCouponDTO {
    /** 优惠券模板id */
    @ApiModelProperty(value = "优惠券模板id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    /** 门店编号 */
    @ApiModelProperty(value = "优惠券模板id")
    private List<Long> branchIds;
}
