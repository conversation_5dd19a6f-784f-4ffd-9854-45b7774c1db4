package com.zksr.member.controller.branch.balance;

import com.zksr.account.api.balance.dto.MemBranchBalanceFlowDTO;
import com.zksr.account.api.balance.vo.AccBalanceFlowRespVO;
import com.zksr.account.api.balance.vo.MemBranchBalanceRespVO;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.controller.branch.balance.dto.MemBranchBalancePageReqDTO;
import com.zksr.member.service.IMemBranchBalanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 门店-余额管理Controller
 */
@Api(tags = "门店管理-余额管理", produces = "application/json")
@Validated
@RestController
@Slf4j
@RequestMapping("/branchBalance")
public class MemBranchBalanceController {

    @Autowired
    private IMemBranchBalanceService memBranchBalanceService;

    @GetMapping("/getPageList")
    @ApiOperation(value = "获得门店余额分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + MemBranchBalanceController.Permissions.LIST)
    @RequiresPermissions(MemBranchBalanceController.Permissions.LIST)
    @DataScope(dcAlias = "`mem_branch`")
    public CommonResult<PageResult<MemBranchBalanceRespVO>> getPageList(@Valid MemBranchBalancePageReqDTO pageReqDTO) {
        PageResult<MemBranchBalanceRespVO> pageResult = memBranchBalanceService.getMemBranchBalancePage(pageReqDTO);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/getDetail")
    @ApiOperation(value = "获取充值或退款详情信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + MemBranchBalanceController.Permissions.DETAIL)
    @RequiresPermissions(MemBranchBalanceController.Permissions.DETAIL)
    public CommonResult<MemBranchBalanceRespVO> getDetailInfo(@RequestParam(value = "branchId") Long branchId, @RequestParam(value = "operType", required = false) Integer operType) {
        MemBranchBalanceRespVO pageResult = memBranchBalanceService.getMemBranchBalanceDetailInfo(branchId, operType);
        return CommonResult.success(pageResult);
    }

    @PostMapping("/recharge")
    @ApiOperation(value = "门店余额充值", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + MemBranchBalanceController.Permissions.RECHARGE)
    @RequiresPermissions(MemBranchBalanceController.Permissions.RECHARGE)
    public CommonResult<String> recharge(@Valid @RequestBody MemBranchBalancePageReqDTO pageReqDTO) {
        String balanceId = memBranchBalanceService.recharge(pageReqDTO);
        return CommonResult.success(balanceId);
    }

    @PostMapping("/refund")
    @ApiOperation(value = "门店余额退款", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + MemBranchBalanceController.Permissions.REFUND)
    @RequiresPermissions(MemBranchBalanceController.Permissions.REFUND)
    public CommonResult<String> refund(@Valid @RequestBody MemBranchBalancePageReqDTO pageReqDTO) {
        String balanceId = memBranchBalanceService.refund(pageReqDTO);
        return CommonResult.success(balanceId);
    }

    @GetMapping("/getFlowPageList")
    @ApiOperation(value = "获得门店余额流水分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + MemBranchBalanceController.Permissions.FLOW_PAGE_LIST)
    @RequiresPermissions(MemBranchBalanceController.Permissions.FLOW_PAGE_LIST)
    public CommonResult<PageResult<AccBalanceFlowRespVO>> getFlowPageList(@Valid MemBranchBalanceFlowDTO pageReqDTO) {
        return memBranchBalanceService.getFlowPageList(pageReqDTO);
    }

    /**
     * 权限字符
     */
    public static class Permissions {

        // 列表
        public static final String LIST = "member:branchBalance:list";

        // 获取详情
        public static final String DETAIL = "member:branchBalance:detail";

        // 充值
        public static final String RECHARGE = "member:branchBalance:recharge";

        // 退款
        public static final String REFUND = "member:branchBalance:refund";

        // 余额流水列表分页查询
        public static final String FLOW_PAGE_LIST = "member:branchBalance:flowPageList";
    }

}
