package com.zksr.member.service;

import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.skuPrice.dto.SkuPriceDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.page.dto.PagesConfigDTO;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.dto.*;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.trade.api.order.vo.OrderStatusVO;

import java.math.BigDecimal;
import java.util.List;

public interface IMemberCacheService {

    public void setWxSessionKey(String key, String sessionKey);

    public String getWxSessionKey(String key);

    public PartnerDto getPartnerDto(String key);

    public BranchDTO getBranchDto(Long branchId);

    public ChannelDTO getChannelDto(Long channelId);

    public AreaDTO getAreaDto(Long areaId);

    public SupplierDTO getSupplierDTO(Long supplierId);

    public BasicSettingPolicyDTO getBasicSettingPolicyDTO(Long dcId);

    /**
    * @Description: 获取平台展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/26 20:29
    */
    List<SaleClassDTO> getSaleClassListBySysCode(Long sysCode);


    public AreaItemDTO getAreaItemDTO(Long areaItemId);

    public SupplierItemDTO getSupplierItemDTO(Long supplierItemId);

    public SpuDTO getSpuDTO(Long spuId);

    public SkuDTO getSkuDTO(Long skuId);

    /**
    * @Description: 获取门店绑定的分类
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:36
    */
    List<AreaClassDTO> getAreaClassBranch(Long branchId);

    /**
     * 获取管理分类分润比例
     * @param catgoryId 管理分类ID
     * @param areaId    区域城市ID
     * @return
     */
    CatgoryRateDTO getCatgoryByIdAndAreaId(Long catgoryId, Long areaId);

    public AppletBaseConfigDTO getAppletBaseConfigDTO(Long sysCode);

    public Integer getAreaSalePriceCodeCache(String key);

    public Integer getSupplierSalePriceCodeCache(Long areaId, Long areaGourpId);

    public SkuPriceDTO getSkuPriceDTOByAreaTypeCache(Long areaId, Long skuId, Integer productType);

    /**
     * @Description: 获取业务员信息
     * @Author: chenmingqing
     * @Date: 2024/3/28 10:12
     */
    ColonelDTO getColonel(Long colonelId);

    public BrandDTO getBrandDTO(Long brandId);

    public CouponTemplateDTO getCouponTemplate(Long couponTemplateId);

    public List<CouponSpuScopeDTO> getCouponSpuScopeList(Long couponTemplateId);

    /**
    * @Description: 获取平台商商城小程序配置
    * @Author: liuxingyu
    * @Date: 2024/4/10 15:59
    */
    AppletAgreementPolicyDTO getAppletAgreementPolicy(Long sysCode);


    /**
     * 获取首页配置
     * @param sysCode   平台商编号
     * @param channel   渠道编号
     * @param areaId    区域编号
     * @return
     */
    List<PagesConfigDTO> getPagesConfigDTO(Long sysCode, Long channel, Long areaId);

    /**
    * @Description: 根据门店区域和门店渠道获取城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/4/24 10:22
    */
    List<AreaClassDTO> getAreaClassAreaChannel(Long areaId, Long channelId);

    ColonelSettingPolicyDTO getColonelSettingPolicyDTO(Long dcId);

    /**
     * 获取订单角标数据统计
     * @param colonelId 业务员ID
     * @return
     */
    OrderStatusVO getOrderStatusQty(Long colonelId);

    /**
     * @param sysCode   平台ID
     * @return  根据sysCode 获取支付配置
     */
    PayConfigDTO getPayConfigDTO(Long sysCode);

    /**
     * 移除门店缓存
     * @param branchId  门店ID
     */
    void deleteBranchById(Long branchId);

    /**
     * 获取管理分类
     */
    CatgoryDTO getCategoryDTO(Long categoryId);

    /**
     * 获取平台商商城小程序配置
     * @param sysCode 平台商编号
     * @return
     */
    PartnerMiniSettingPolicyDTO getPartnerMiniSettingPolicy(Long sysCode);

    /**
     * 获取可视化接口详情配置
     * @param key
     * @return
     */
    VisualSettingDetailDto getVisualDetailBySupplier(String key);


    /**
     * 获取平台商账号默认密码
     * @param sysCode 平台商编号
     * @return
     */
    String getDefaultPassword(Long sysCode);

    /**
     * 获取运营商生命周期配置
     * @param dcId 运营商ID
     * @return
     */
    BranchLifecycleSettingPolicyDTO getBranchLifecycleSettingPolicyDTO(Long dcId);

    /**
     * 获取平台提现配置
     * @param sysCode   平台商编号
     * @return
     */
    public WithdrawalSettingPolicyDTO getWithdrawalSetting(Long sysCode);

    /**
     * 获取运营商信息
     * @param dcId
     * @return
     */
    DcDTO getDcDTO(Long dcId);
}
