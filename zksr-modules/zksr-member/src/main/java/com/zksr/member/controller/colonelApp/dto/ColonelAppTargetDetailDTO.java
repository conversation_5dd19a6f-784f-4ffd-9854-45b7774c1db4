package com.zksr.member.controller.colonelApp.dto;

import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("业务员App - 业务员目标明细返回对象")
public class ColonelAppTargetDetailDTO {

    @ApiModelProperty(value = "业务员Id")
    private Long colonelId;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "性别(数据字典：sys_user_sex 0：男 1：女)")
    private Integer sex;

    @ApiModelProperty(value = "业务员手机号")
    private String colonelPhone;

    @ApiModelProperty(value = "区域Id")
    private Long areaId;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "管理客户数量")
    private Long manageBranchNum;

    @ApiModelProperty(value = "目标年份（yyyy）")
    private String targetYear;

    @ApiModelProperty(value = "目标月份（MM）")
    private String targetMonth;

    @ApiModelProperty(value = "目标销售额")
    private BigDecimal targetSalesAmt;

    @ApiModelProperty(value = "实际销售额")
    private BigDecimal salesAmt = BigDecimal.ZERO;;

    @ApiModelProperty(value = "目标新开客户数")
    private Long targetAddBranchNum;

    @ApiModelProperty(value = "实际新开客户数")
    private Long addBranchNum = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "目标活跃客户数")
    private Long targetSaleBranchNum;

    @ApiModelProperty(value = "实际活跃客户数")
    private Long saleBranchNum = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "目标拜访客户数")
    private Long targetVisitBranchNum;

    @ApiModelProperty(value = "实际拜访客户数")
    private Long visitBranchNum = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "目标下单笔数")
    private Long targetOrderNum = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "实际下单笔数")
    private Long orderNum = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "目标客单价")
    private BigDecimal targetAvgPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "实际客单价")
    private BigDecimal avgPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "目标首次动销客户")
    private Long targetFirstSaleBranchNum = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "实际首次动销客户")
    private Long firstSaleBranchNum = NumberPool.LONG_ZERO;
}
