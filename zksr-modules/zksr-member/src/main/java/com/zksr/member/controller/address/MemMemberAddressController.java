package com.zksr.member.controller.address;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.member.convert.address.MemMemberAddressConvert;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemMemberAddress;
import com.zksr.member.service.IMemMemberAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.address.vo.MemMemberAddressPageReqVO;
import com.zksr.member.controller.address.vo.MemMemberAddressSaveReqVO;
import com.zksr.member.controller.address.vo.MemMemberAddressRespVO;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 用户地址Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Api(tags = "管理后台 - 用户地址接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/address")
public class MemMemberAddressController {
    @Autowired
    private IMemMemberAddressService memMemberAddressService;

    /**
     * 新增用户地址
     */
    @ApiOperation(value = "新增用户地址", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "用户地址", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemMemberAddressSaveReqVO createReqVO) {
        return success(memMemberAddressService.insertMemMemberAddress(createReqVO));
    }

    /**
     * 修改用户地址
     */
    @ApiOperation(value = "修改用户地址", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "用户地址", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemMemberAddressSaveReqVO updateReqVO) {
            memMemberAddressService.updateMemMemberAddress(updateReqVO);
        return success(true);
    }

    /**
     * 删除用户地址
     */
    @ApiOperation(value = "删除用户地址", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "用户地址", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public CommonResult<Boolean> remove(@PathVariable Long[] ids) {
        memMemberAddressService.deleteMemMemberAddressByIds(ids);
        return success(true);
    }

    /**
     * 获取用户地址详细信息
     */
    @ApiOperation(value = "获得用户地址详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{id}")
    public CommonResult<MemMemberAddressRespVO> getInfo(@PathVariable("id") Long id) {
        MemMemberAddress memMemberAddress = memMemberAddressService.getMemMemberAddress(id);
        return success(MemMemberAddressConvert.INSTANCE.convert(memMemberAddress));
    }

    /**
     * 分页查询用户地址
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得用户地址分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemMemberAddressRespVO>> getPage(@Valid MemMemberAddressPageReqVO pageReqVO) {
        PageResult<MemMemberAddress> pageResult = memMemberAddressService.getMemMemberAddressPage(pageReqVO);
        return success(MemMemberAddressConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:address:add";
        /** 编辑 */
        public static final String EDIT = "member:address:edit";
        /** 删除 */
        public static final String DELETE = "member:address:remove";
        /** 列表 */
        public static final String LIST = "member:address:list";
        /** 查询 */
        public static final String GET = "member:address:query";
        /** 停用 */
        public static final String DISABLE = "member:address:disable";
        /** 启用 */
        public static final String ENABLE = "member:address:enable";
    }
}
