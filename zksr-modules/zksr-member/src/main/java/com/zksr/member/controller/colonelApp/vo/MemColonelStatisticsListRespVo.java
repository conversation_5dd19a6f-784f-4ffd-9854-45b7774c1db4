package com.zksr.member.controller.colonelApp.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2024/8/6 15:43
 * @业务员App - 业务员列表统计列表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("业务员App - 业务员列表统计列表")
public class MemColonelStatisticsListRespVo {

    /** 业务员id */
    @ApiModelProperty(value = "业务员id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 业务员手机号 */
    @Excel(name = "业务员手机号")
    @ApiModelProperty(value = "业务员手机号")
    private String colonelPhone;

    /** 业务员名 */
    @Excel(name = "业务员名")
    @ApiModelProperty(value = "业务员名")
    private String colonelName;

    /** 业务员级别（职务） */
    @Excel(name = "业务员级别", readConverterExp = "职=务")
    @ApiModelProperty(value = "业务员级别 数据字典(1:业务专员,2:业务经理)")
    private Long colonelLevel;

    /** 性别（数据字典） */
    @Excel(name = "性别", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "性别")
    private Integer sex;

    /** 业务员头像 */
    @Excel(name = "业务员头像")
    @ApiModelProperty(value = "业务员头像")
    private String avatarImages;


    /** 客户总数 */
    @Excel(name = "客户总数", readConverterExp = "客户总数")
    @ApiModelProperty(value = "客户总数")
    private Integer customersTotal;

    /** 拓店统计数据 */
    @Excel(name = "拓店统计数据", readConverterExp = "拓店统计数据")
    @ApiModelProperty(value = "拓店统计数据")
    private MemColonelStatisticsDetail addBranchStatistics;

    /** 拜访统计数据 */
    @Excel(name = "拜访统计数据", readConverterExp = "拜访统计数据")
    @ApiModelProperty(value = "拜访统计数据")
    private MemColonelStatisticsDetail visitStatistics;

    /** 动销统计数据 */
    @Excel(name = "动销统计数据", readConverterExp = "动销统计数据")
    @ApiModelProperty(value = "动销统计数据")
    private MemColonelStatisticsDetail saleBranchStatistics;

}
