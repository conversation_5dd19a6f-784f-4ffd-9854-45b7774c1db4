package com.zksr.member.controller.relation.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 业务员关系对象 mem_colonel_relation
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Data
@ApiModel("业务员关系 - mem_colonel_relation Response VO")
public class MemColonelRelationRespVO {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelRelationId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 管理业务员ID */
    @Excel(name = "管理业务员ID")
    @ApiModelProperty(value = "管理业务员ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long adminColonelId;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelId;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "部门ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long deptId;

    @ApiModelProperty(value = "业务员职务")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelLevel;

    @ApiModelProperty(value = "性别")
    private Integer sex;

    @ApiModelProperty(value = "是否默认 0否，1：是 默认不可删除")
    private Integer isDefault;



}
