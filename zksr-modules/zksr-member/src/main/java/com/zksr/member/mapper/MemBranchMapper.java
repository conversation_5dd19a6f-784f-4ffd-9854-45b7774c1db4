package com.zksr.member.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncBranchCallDTO;
import com.zksr.common.core.enums.branch.BranchLifecycleTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.ColonelBranchCountDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.branch.vo.BranchListForReqVO;
import com.zksr.member.api.branch.vo.MemberBranchReqVO;
import com.zksr.member.api.branchLifecycle.dto.ColonelBranchLifecycleQtyDTO;
import com.zksr.member.controller.branch.vo.BranchDropdownReqVO;
import com.zksr.member.controller.branch.vo.MemBranchPageReqVO;
import com.zksr.member.controller.colonelApp.vo.MemColonelAppCustomerListPageReqVO;
import com.zksr.member.domain.MemBranch;
import com.zksr.report.api.homePages.dto.HomePagesBranchDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.system.api.area.dto.AreaDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.STATE_ENABLE;


/**
 * 门店信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@SuppressWarnings("all")
@Mapper
public interface MemBranchMapper extends BaseMapperX<MemBranch> {
    Page<MemBranch> selectPage(@Param("page") Page<MemBranch> page, @Param("reqVO") MemBranchPageReqVO reqVO);


    /**
     * 根据门店ID查询门店和业务员信息
     * @param branchId
     * @return
     */
    BranchDTO getMemBranchByBranchId(@Param("branchId") Long branchId);

    /**
     * 查询用户绑定门店信息列表
     * @param reqVO
     * @return
     */
     List<BranchDTO> getBranchListByMemberId(MemberBranchReqVO reqVO);

    /**
     * 用于选择数据回显
     * @param branchIds 门店ID集合
     * @return  门店ID集合对应的数据集
     */
    default List<MemBranch> selectSelectedMemberBranch(List<Long> branchIds) {
        return selectList(new LambdaQueryWrapperX<MemBranch>()
                .in(MemBranch::getBranchId, branchIds)
                .select(
                        MemBranch::getBranchId,
                        MemBranch::getBranchName
                )
                .orderByDesc(MemBranch::getBranchId));
    }

    /**
    * @Description: 获取门店下拉选
    * @Author: liuxingyu
    * @Date: 2024/4/12 10:24
    */
    default List<MemBranch> getBranchDropdown(BranchDropdownReqVO dropdownReqVO){
        LambdaQueryWrapper<MemBranch> lambdaQueryWrapper = new LambdaQueryWrapper<MemBranch>()
                .in(ObjectUtil.isNotEmpty(dropdownReqVO.getAreaIdList()), MemBranch::getAreaId, dropdownReqVO.getAreaIdList())
                .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
                .like(StringUtils.isNotEmpty(dropdownReqVO.getBranch()), MemBranch::getBranchName, dropdownReqVO.getBranch())
                .orderByDesc(MemBranch::getStatus);

        // 是否只查询简单字段
        if (Objects.nonNull(dropdownReqVO.getSimpleFields()) && dropdownReqVO.getSimpleFields()) {
            lambdaQueryWrapper.select(MemBranch::getBranchName, MemBranch::getBranchId);
        }
        return selectList(lambdaQueryWrapper);
    }

    default List<MemBranch> getAllExpirationDateBranchList(Long sysCode){
        return selectList(new LambdaQueryWrapper<MemBranch>()
                .ge(MemBranch::getCreateTime, DateUtils.getDateScopeByMonth(-6))
                .eq(MemBranch::getStatus,STATE_ENABLE)
                .lt(MemBranch::getExpirationDate,new Date())
                .eq(MemBranch::getSysCode,sysCode));
    }

    void updateStatusByBranchIds(@Param("branchDTO") BranchDTO branchDTO);

    default void updateBranch(MemBranch branch){
        updateById(branch);
    }

    /**
     * 根据条件批量查询业务员绑定的门店列表
     * @param colonelIds
     * @return
     */
    Page<MemBranchPageReqVO> getBranchListByColonelIds(@Param("reqVO") MemColonelAppCustomerListPageReqVO reqVO,@Param("page") Page<MemColonelAppCustomerListPageReqVO> page, @Param("colonelIds") List<Long> colonelIds);

    default void updateLastLoginTime(Long sysCode, Long branchId){
        update(null,new LambdaUpdateWrapper<MemBranch>()
                        .set(MemBranch::getLastLoginTime,new Date())
                        .eq(MemBranch::getSysCode,sysCode)
                        .eq(MemBranch::getBranchId,branchId));

    }


    /**
     * 根据关键字筛选出符合关键字的门店ID集合
     * @param reqVO
     * @return
     */
    default List<Long> selectMemBranchListByParam(MemBranchPageReqVO reqVO) {
        LambdaQueryWrapper<MemBranch> wrapper = new LambdaQueryWrapper<MemBranch>()
            .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
            .eq(ObjectUtil.isNotEmpty(reqVO.getAreaId()), MemBranch::getAreaId, reqVO.getAreaId());

        // 使用 when 方法确保只有在 keywords 不为空时才添加 like 条件
        if (ObjectUtil.isNotEmpty(reqVO.getKeywords())) {
            wrapper.and(w -> w
                .like(MemBranch::getBranchId, reqVO.getKeywords())
                .or()
                .like(MemBranch::getBranchName, reqVO.getKeywords())
            );
        }

        List<MemBranch> memBranchList = selectList(wrapper);

        if (ObjectUtil.isEmpty(memBranchList)) {
            return Collections.emptyList(); // 返回空列表而不是 null
        }

        return memBranchList.stream().map(MemBranch::getBranchId).collect(Collectors.toList());
    }



    /**
     * 根据条件查询已拜访和未拜访的门店
     * @param colonelIds
     * @return
     */
    Page<MemBranchPageReqVO> getBranchListByVisit(@Param("reqVO") MemColonelAppCustomerListPageReqVO reqVO,@Param("page") Page<MemColonelAppCustomerListPageReqVO> page, @Param("colonelIds") List<Long> colonelIds);

    /**
     * 根据条件查询已拜访和未拜访的门店Id（用于查询es进行排序操作）
     * @param colonelIds
     * @return
     */
    List<Long> getBranchIdListByVisit(@Param("reqVO") MemColonelAppCustomerListPageReqVO reqVO, @Param("colonelIds") List<Long> colonelIds);


    /**
     *  根据门店编号查询门店信息
     * @param branchNo
     * @return
     */
    default MemBranch selectMemBranchByBranchNo(String branchNo){
        return selectOne(new LambdaQueryWrapperX<MemBranch>()
                .eqIfPresent(MemBranch::getBranchNo, branchNo));
    }

    /**
     *  根据业务员编号集合查询门店信息
     * @param colonels
     * @return
     */
    default List<MemBranch> selectMemBranchByColonels(List<Long> colonels){
        return selectList(
                new LambdaQueryWrapperX<MemBranch>()
                        .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
                        .inIfPresent(MemBranch::getColonelId, colonels)
        );
    }

    /**
     *  业务员APP 根据筛选条件 查询业务员所绑定的门店信息
     * @param colonels
     * @return
     */
    default List<MemBranch> selectBranchByColonelApp(MemColonelAppCustomerListPageReqVO pageReqVO,List<Long> colonels){

        LambdaQueryWrapperX<MemBranch> query = new LambdaQueryWrapperX<MemBranch>()
                .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
                .eq(MemBranch::getStatus, STATE_ENABLE)
                .inIfPresent(MemBranch::getColonelId, colonels)
                .eqIfPresent(MemBranch::getLifecycleStage,pageReqVO.getLifecycleStage());

        //首单标识
        if(ToolUtil.isNotEmpty(pageReqVO.getFirstOrderFlag())){
            if(Objects.equals(pageReqVO.getFirstOrderFlag(), NumberPool.INT_ONE)){
                query.eq(MemBranch::getFirstOrderFlag, NumberPool.INT_ONE);
            }else  if(Objects.equals(pageReqVO.getFirstOrderFlag(), NumberPool.INT_ZERO)){
                query.and(and -> {
                    and.eq(MemBranch::getFirstOrderFlag, NumberPool.INT_ZERO)
                            .or()
                       .isNull(MemBranch::getFirstOrderFlag);
                });
            }
        }

        return selectList(query);
    }

    /**
     *  根据业务员编号和门店名称集合查询门店信息
     * @param colonels
     * @return
     */
    default List<MemBranch> selectMemBranchByColonelsAndBranchName(List<Long> colonels,String branchName){
        return selectList(
                new LambdaQueryWrapperX<MemBranch>()
                        .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
                        .inIfPresent(MemBranch::getColonelId, colonels)
                        .likeIfPresent(MemBranch::getBranchName, branchName)
        );
    }

    /**
     * 根据业务员id 查询管理绑定门店数量
     * @param colonelId
     * @return
     */
    default Long selectBranchCountByColonel(Long colonelId){
        return selectCount(new LambdaQueryWrapperX<MemBranch>()
                .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
                .eq(MemBranch::getColonelId, colonelId));
    }

    default List<MemBranch> selectListByParams(MemBranchPageReqVO reqVO){
        return selectList(new LambdaQueryWrapperX<MemBranch>()
                .eqIfPresent(MemBranch::getSysCode, reqVO.getSysCode())
                .eqIfPresent(MemBranch::getStatus, reqVO.getStatus())
                .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
        );
    }

    default List<Long> selectAllBranchIdList(Long minBranchId) {
        List<MemBranch> memBranches = selectList(
                new LambdaQueryWrapperX<MemBranch>()
                        .select(
                                MemBranch::getBranchId
                        )
                        .gt(MemBranch::getBranchId, minBranchId)
                        .orderByAsc(MemBranch::getBranchId)
                        .last("LIMIT 500")
        );
        return memBranches.stream().map(MemBranch::getBranchId).collect(Collectors.toList());
    }

    default List<Long> selectAllBranchIdListBySysCode(Long sysCode) {
        List<MemBranch> memBranches = selectList(
                new LambdaQueryWrapperX<MemBranch>()
                        .select(
                                MemBranch::getBranchId
                        )
                        .eq(MemBranch::getSysCode, sysCode)
                        .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
                        .eq(MemBranch::getStatus, 1)
                        .orderByAsc(MemBranch::getBranchId)
        );
        return memBranches.stream().map(MemBranch::getBranchId).collect(Collectors.toList());
    }

    List<SyncBranchCallDTO> selectBranchByTimeRange(@Param("areaList")  List<AreaDTO> areaList);

    /**
     * 使用此方法需要注意, 某些重要的信息会设置为NULL
     * @param bean
     */
    default void updateBranchFillNull(MemBranch bean) {
        LambdaUpdateWrapper<MemBranch> uw = new LambdaUpdateWrapper();
        uw.eq(MemBranch::getBranchId, bean.getBranchId());
        /*if (Objects.isNull(bean.getColonelId())) {
            uw.set(MemBranch::getColonelId, null);
        }
        if (Objects.isNull(bean.getChannelId())) {
            uw.set(MemBranch::getChannelId, null);
        }
        if (Objects.isNull(bean.getSalePriceCode())) {
            uw.set(MemBranch::getSalePriceCode, null);
        }*/
        this.update(bean, uw);
    }

    default List<MemBranch> selectListNormal() {
        return selectList(
                new LambdaUpdateWrapper<MemBranch>()
                        .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
        );
    }

    default List<MemBranch> selectListFor(BranchListForReqVO reqVO) {
        LambdaQueryWrapper<MemBranch> memBranchLambdaQueryWrapper = new LambdaQueryWrapperX<MemBranch>()
                .gtIfPresent(MemBranch::getBranchId, reqVO.getMinId())
                .eqIfPresent(MemBranch::getSysCode, reqVO.getSysCode())
                .orderByAsc(MemBranch::getBranchId);
        if (Objects.nonNull(reqVO.getSize())) {
            memBranchLambdaQueryWrapper.last("LIMIT " + reqVO.getSize());
        }
        return selectList(memBranchLambdaQueryWrapper);
    }

    List<BranchDTO> getUnauditedBranchListByMemberId(MemberBranchReqVO memberBranchReqVO);

    List<ColonelBranchCountDTO> getBranchCountFromOtherSource(@Param("colonelIds") List<String> colonelIds, @Param("currentMonthId")  String currentMonthId);

    /**
     * 获取PC首页门店数据
     * @param reqVO
     * @return
     */
    List<HomePagesBranchDataRespDTO> getHomePagesBranchData(HomePagesReqVO reqVO);

    /**
     * 根据区域id查询门店
     * @param reqVO
     * @return
     */
    default List<BranchDTO> getbranchListByArea(Long dcAreaId){
        List<MemBranch> memBranches = selectList(
                new LambdaUpdateWrapper<MemBranch>()
                        .eq(MemBranch::getAreaId, dcAreaId)
        );
        return memBranches.stream().map(memBranch -> Objects.requireNonNull(HutoolBeanUtils.toBean(memBranch, BranchDTO.class))).collect(Collectors.toList());
    }

    default void updateBranchColonelId(MemBranchSaveReqVO updateReqVO) {
        update(HutoolBeanUtils.toBean(updateReqVO, MemBranch.class),
                new LambdaUpdateWrapper<MemBranch>()
                        .eq(MemBranch::getBranchId, updateReqVO.getBranchId())
                        .set(MemBranch::getColonelId, updateReqVO.getColonelId()));
    }
    default void updateBranchChannelId(MemBranchSaveReqVO updateReqVO) {
        update(HutoolBeanUtils.toBean(updateReqVO, MemBranch.class),
                new LambdaUpdateWrapper<MemBranch>()
                        .eq(MemBranch::getBranchId, updateReqVO.getBranchId())
                        .set(MemBranch::getChannelId, updateReqVO.getChannelId()));
    }
    default List<MemBranch> getListBranchByColoelId(Long colonelId){
        return selectList(new LambdaQueryWrapper<MemBranch>()
                .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
                .eq(MemBranch::getColonelId, colonelId));
    }

    /**
     *  根据门店Id编号集合查询门店信息
     * @param branchIds
     * @return
     */
    default List<MemBranch> selectMemBranchByBranchIds(List<Long> branchIds){
        return selectList(
                new LambdaQueryWrapperX<MemBranch>()
                        .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
                        .inIfPresent(MemBranch::getBranchId, branchIds)
        );
    }

    default List<MemBranch> getAreaIdExistBranch(Long areaId, Long sysCode){
        return selectList(new LambdaQueryWrapperX<MemBranch>()
                .eq(MemBranch::getAreaId, areaId)
                .eq(MemBranch::getSysCode, sysCode)
        );
    }

    default List<MemBranch> getMemBranchByChannelId(Long channelId, Long sysCode){
        return selectList(
                new LambdaQueryWrapperX<MemBranch>()
                        .eq(MemBranch::getChannelId, channelId)
                        .eq(MemBranch::getSysCode, sysCode));
    }

    /**
     * 修改门店信息的首单相关信息
     * @param branchDTO
     */
    default void updateFirstOrder(BranchDTO branchDTO){
        update(null, new LambdaUpdateWrapper<MemBranch>()
                .set(MemBranch::getFirstOrderNo, branchDTO.getFirstOrderNo())
                .set(MemBranch::getFirstOrderFlag,branchDTO.getFirstOrderFlag())
                .eq(MemBranch::getBranchId,branchDTO.getBranchId()));

    }

    default List<Long> getBranchIdListByBranchName(String branchName, Long sysCode){
        return selectList(
                new LambdaQueryWrapperX<MemBranch>()
                        .eq(MemBranch::getSysCode, sysCode)
                        .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
                        .eq(MemBranch::getStatus, 1)
                        .like(MemBranch::getBranchName, branchName)
        ).stream().map(MemBranch::getBranchId).collect(Collectors.toList());
    }

    void deleteBranchByBranchId(Long branchId);

    /**
     * 根据平台商ID 查询平台下所有正常使用门店
     * @param sysCode
     * @return
     */
    default List<MemBranch> selectAllBranchBySysCode(Long sysCode) {
        return selectList(new LambdaQueryWrapperX<MemBranch>()
                        .eq(MemBranch::getSysCode, sysCode)
                        .eq(MemBranch::getDelFlag, DelFlagConstants.NORMAL)
                        //.eq(MemBranch::getStatus, STATE_ENABLE)  产品说 停用的 也需要筛选生命周期信息
        );
    }

    Date getBranchRegisterTime(@Param("branchId") Long branchId);

    default void updateBatchBranchLifecycle(Set<Long> branchIdSet){
        update(null,new LambdaUpdateWrapper<MemBranch>()
                .set(MemBranch::getLifecycleStage, BranchLifecycleTypeEnum.NEW_CUSTOMER.getType())
                .in(MemBranch::getBranchId, branchIdSet)
        );
    }

    /**
     * 获取该业务员所管理的门店生命周期信息
     * @param colonelId
     * @return
     */
    ColonelBranchLifecycleQtyDTO getColonelBranchLifecycle(@Param("colonelId") Long colonelId);


    /**
     * 根据业务员ID和年月查询首次动销客户数量
     * @param
     * @return
     */
    Long getFirstSaleBranchNumByColonelIdAndYearMonth(@Param("colonelId")Long colonelId,@Param("yearMonth")String yearMonth);

    /**
     * 获取门店列表(支持查询附近门店)
     * @param reqVO
     * @return
     */
    Page<MemBranch> selectAppPage(@Param("page") Page<MemBranch> page, @Param("reqVO") MemBranchPageReqVO reqVO);

}
