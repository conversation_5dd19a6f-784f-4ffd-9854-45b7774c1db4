package com.zksr.member.controller.ColonelTidy.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 业务员理货记录对象 mem_colonel_tidy
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@ApiModel("业务员理货记录 - mem_colonel_tidy分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemColonelTidyPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 业务员理货记录id */
    @ApiModelProperty(value = "理货Id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelTidyId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 门店ID */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 理货图片 */
    @Excel(name = "理货图片")
    @ApiModelProperty(value = "理货图片")
    private String tidyImg;

    /** 理货说明 */
    @Excel(name = "理货说明")
    @ApiModelProperty(value = "理货说明")
    private String tidyDescr;

    /** 开始时间 */
    @Excel(name = "开始时间")
    @ApiModelProperty(value = "开始时间")
    private String beginCreateTime;

    /** 结束时间 */
    @Excel(name = "结束时间")
    @ApiModelProperty(value = "结束时间")
    private String endCreateTime;

    /** 搜索关键字 */
    @Excel(name = "搜索关键字")
    @ApiModelProperty(value = "搜索关键字")
    private String keywords;

    @ApiModelProperty(value = "门店Id集合")
    private List<Long> branchIdList;

    /** 城市Id */
    @Excel(name = "城市Id")
    @ApiModelProperty(value = "城市Id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 理货id集合 */
    @Excel(name = "理货id集合")
    @ApiModelProperty(value = "理货id集合")
    private List<String> colonelTidyIdList;


}
