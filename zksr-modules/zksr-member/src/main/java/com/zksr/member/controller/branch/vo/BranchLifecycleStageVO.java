package com.zksr.member.controller.branch.vo;

import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* 查询门店状态变更记录 请求实体
* @date 2025/3/12 11:06
* <AUTHOR>
*/
@Data
@ApiModel("门店查询门店状态变更记录 请求实体")
public class BranchLifecycleStageVO {
    @ApiModelProperty(value = "门店近一年所有的状态变更")
    private List<MemBranchLifecycleZipRespVO> allLifecycleStageList;

    @ApiModelProperty(value = "门店近一年阶段状态变更")
    private List<MemBranchLifecycleZipRespVO> branchLifecycleStageList;
}
