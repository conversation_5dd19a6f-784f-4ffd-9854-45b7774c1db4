package com.zksr.member.controller.colonelApp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "业务员APP相关系统配置")
public class ColonelAppPolicyRespDTO {

    @ApiModelProperty("平台本地商品售后类型 （0：入驻商审核，1：业务员审核）")
    private String localItemAfterType;

    @ApiModelProperty("平台本地商品售后审核类型 （0：入驻商审核，1：业务员审核）")
    private String localItemAfterApproveType;
}
