package com.zksr.member.controller.branch.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 门店信息对象 mem_branch
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Data
@ApiModel("门店信息 - mem_branch分页 Request VO")
public class MemBranchSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 门店id */
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /** 业务员id */
    @Excel(name = "业务员id")
    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 渠道id */
    @Excel(name = "渠道id")
    @ApiModelProperty(value = "渠道id")
    private Long channelId;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id")
    private Long groupId;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 审核状态 */
    @Excel(name = "审核状态")
    @ApiModelProperty(value = "1已审核 0未审核")
    private Integer auditState;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @Excel(name = "价格码-数据字典", readConverterExp = "1=，2，3，4，5，6")
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;

    /** 门头照 */
    @Excel(name = "门头照")
    @ApiModelProperty(value = "门头照")
    private String branchImages;

    @Excel(name = "是否支持货到付款(0,否 1,是)")
    @ApiModelProperty(value = "是否支持货到付款(0,否 1,是)")
    private Integer hdfkSupport;

    /** 最后一次登陆时间 */
    @Excel(name = "最后一次登陆时间")
    @ApiModelProperty(value = "最后一次登陆时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;


    /** 省份 */
    @Excel(name = "省份")
    @ApiModelProperty(value = "省份")
    private String provinceName;

    /** 城市 */
    @Excel(name = "城市")
    @ApiModelProperty(value = "城市")
    private String cityName;

    /** 区县 */
    @Excel(name = "区县")
    @ApiModelProperty(value = "区县")
    private String districtName;

    /** 首单单号 */
    @Excel(name = "首单单号")
    @ApiModelProperty("首单单号")
    private String firstOrderNo;

    /** 生命周期阶段 */
    @Excel(name = "生命周期阶段")
    @ApiModelProperty("生命周期阶段")
    private Integer lifecycleStage;

    /** 分销渠道（如美云销等) */
    @ApiModelProperty(value = "分销渠道（如美云销等)")
    private String distributionChannel;

    /** 分销模式（如O2O等) */
    @ApiModelProperty(value = "分销模式（如O2O等)")
    private String distributionMode;

    /** 分润模式（如按商品指定金额等) */
    @ApiModelProperty(value = "分润模式（如按商品指定金额等)")
    private String profitMode;

    /** 分润比例 */
    @ApiModelProperty(value = "分润比例")
    @Excel(name = "分润比例")
    private BigDecimal profitRate;

    /** 商户号 */
    @ApiModelProperty(value = "商户号")
    private String merchantNo;

    /** 商户号进件单号 */
    @ApiModelProperty(value = "商户号进件单号")
    private String merchantApplyNo;

    /** 商户号全称 */
    @ApiModelProperty(value = "商户号全称")
    private String merchantFullName;
    
    /** 纳税人识别码 */
    @ApiModelProperty(value = "纳税人识别码")
    private String taxpayerCode;

}
