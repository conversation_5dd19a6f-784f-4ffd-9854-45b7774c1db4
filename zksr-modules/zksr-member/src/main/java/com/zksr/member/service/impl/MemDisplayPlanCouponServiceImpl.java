package com.zksr.member.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemDisplayPlanCouponMapper;
import com.zksr.member.domain.MemDisplayPlanCoupon;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanCouponPageReqVO;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanCouponSaveReqVO;
import com.zksr.member.service.IMemDisplayPlanCouponService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 陈列计划优惠明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Service
public class MemDisplayPlanCouponServiceImpl implements IMemDisplayPlanCouponService {
    @Autowired
    private MemDisplayPlanCouponMapper memDisplayPlanCouponMapper;

    /**
     * 新增陈列计划优惠明细
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemDisplayPlanCoupon(MemDisplayPlanCouponSaveReqVO createReqVO) {
        // 插入
        MemDisplayPlanCoupon memDisplayPlanCoupon = HutoolBeanUtils.toBean(createReqVO, MemDisplayPlanCoupon.class);
        memDisplayPlanCouponMapper.insert(memDisplayPlanCoupon);
        // 返回
        return memDisplayPlanCoupon.getPlanCouponId();
    }

    /**
     * 修改陈列计划优惠明细
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemDisplayPlanCoupon(MemDisplayPlanCouponSaveReqVO updateReqVO) {
        memDisplayPlanCouponMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, MemDisplayPlanCoupon.class));
    }

    /**
     * 删除陈列计划优惠明细
     *
     * @param planCouponId 主键ID
     */
    @Override
    public void deleteMemDisplayPlanCoupon(Long planCouponId) {
        // 删除
        memDisplayPlanCouponMapper.deleteById(planCouponId);
    }

    /**
     * 批量删除陈列计划优惠明细
     *
     * @param planCouponIds 需要删除的陈列计划优惠明细主键
     * @return 结果
     */
    @Override
    public void deleteMemDisplayPlanCouponByPlanCouponIds(Long[] planCouponIds) {
        for(Long planCouponId : planCouponIds){
            this.deleteMemDisplayPlanCoupon(planCouponId);
        }
    }

    /**
     * 获得陈列计划优惠明细
     *
     * @param planCouponId 主键ID
     * @return 陈列计划优惠明细
     */
    @Override
    public MemDisplayPlanCoupon getMemDisplayPlanCoupon(Long planCouponId) {
        return memDisplayPlanCouponMapper.selectById(planCouponId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemDisplayPlanCoupon> getMemDisplayPlanCouponPage(MemDisplayPlanCouponPageReqVO pageReqVO) {
        return memDisplayPlanCouponMapper.selectPage(pageReqVO);
    }

    private void validateMemDisplayPlanCouponExists(Long planCouponId) {
        if (memDisplayPlanCouponMapper.selectById(planCouponId) == null) {
            throw exception(MEM_DISPLAY_PLAN_COUPON_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 陈列计划优惠明细 TODO 补充编号 ==========
    // ErrorCode MEM_DISPLAY_PLAN_COUPON_NOT_EXISTS = new ErrorCode(TODO 补充编号, "陈列计划优惠明细不存在");


}
