package com.zksr.member.convert.searchHis;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemSearchHis;
import com.zksr.member.controller.searchHis.vo.MemSearchHisRespVO;
import com.zksr.member.controller.searchHis.vo.MemSearchHisSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 搜索历史 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-01-15
*/
@Mapper
public interface MemSearchHisConvert {

    MemSearchHisConvert INSTANCE = Mappers.getMapper(MemSearchHisConvert.class);

    MemSearchHisRespVO convert(MemSearchHis memSearchHis);

    MemSearchHis convert(MemSearchHisSaveReqVO memSearchHisSaveReq);

    PageResult<MemSearchHisRespVO> convertPage(PageResult<MemSearchHis> memSearchHisPage);
}