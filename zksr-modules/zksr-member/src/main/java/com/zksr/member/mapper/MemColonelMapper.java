package com.zksr.member.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import com.zksr.member.controller.colonelApp.vo.MemColonelStatisticsListReqVo;
import com.zksr.member.controller.colonelApp.vo.MemColonelStatisticsListRespVo;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemColonel;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 业务员信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Mapper
public interface MemColonelMapper extends BaseMapperX<MemColonel> {
    /**
     * 分页查询业务员信息（未运营商数据隔离）
     * @param reqVO
     * @return
     */
    default PageResult<MemColonel> selectPage(MemColonelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemColonel>()
                    .eqIfPresent(MemColonel::getColonelId, reqVO.getColonelId())
                    .eqIfPresent(MemColonel::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemColonel::getAreaId, reqVO.getAreaId())
                    .likeIfPresent(MemColonel::getColonelPhone, reqVO.getColonelPhone())
                    .likeIfPresent(MemColonel::getColonelName, reqVO.getColonelName())
                    .eqIfPresent(MemColonel::getColonelLevel, reqVO.getColonelLevel())
                    .eqIfPresent(MemColonel::getPcolonelId, reqVO.getPcolonelId())
                    .eqIfPresent(MemColonel::getSex, reqVO.getSex())
                    .eqIfPresent(MemColonel::getStatus, reqVO.getStatus())
                    .eqIfPresent(MemColonel::getBirthday, reqVO.getBirthday())
                    .eqIfPresent(MemColonel::getBirthplace, reqVO.getBirthplace())
                    .eqIfPresent(MemColonel::getEntryDate, reqVO.getEntryDate())
                    .eqIfPresent(MemColonel::getEdu, reqVO.getEdu())
                    .eqIfPresent(MemColonel::getIdcard, reqVO.getIdcard())
                    .eqIfPresent(MemColonel::getPercentageRate, reqVO.getPercentageRate())
                    .eqIfPresent(MemColonel::getContactAddr, reqVO.getContactAddr())
                    .eqIfPresent(MemColonel::getMemo, reqVO.getMemo())
                    .eqIfPresent(MemColonel::getIsColonelAdmin, reqVO.getIsColonelAdmin())
                    .eqIfPresent(MemColonel::getAuditState, reqVO.getAuditState())
                    .eqIfPresent(MemColonel::getSource, reqVO.getSource())
                    .eq(MemColonel::getDelFlag, DelFlagConstants.NORMAL)
                .orderByDesc(MemColonel::getColonelId));
    }

    /**
     * 分页查询业务员信息（运营商数据隔离）
     * @param reqVO
     * @return
     */
    List<MemColonel> selectListPage(@Param("reqVo") MemColonelPageReqVO reqVO);

    /**
     * 分页查询未绑定关系的业务员
     * @param reqVO
     * @return
     */
    List<MemColonelRespVO> getColonelNotRelationPage(MemColonelPageReqVO reqVO);

    /**
     * 根据系统编码SysCode查询(状态正常)业务员
     * @param sysCode
     * @return
     */
    default List<MemColonel> getListBySysCode(Long sysCode) {
        return selectList(new LambdaQueryWrapperX<MemColonel>()
                .eqIfPresent(MemColonel::getSysCode, sysCode)
                .eqIfPresent(MemColonel::getStatus, NumberPool.INT_ONE)
                .eqIfPresent(MemColonel::getAuditState, NumberPool.INT_ONE)
                .eq(MemColonel::getDelFlag, DelFlagConstants.NORMAL)
        );
    }

    /**
     * 根据 业务员名称 查询 业务员
     * @param sysCode
     * @return
     */
    default MemColonel getColonelByColenelName(Long sysCode, String pcolonelName) {
        return selectOne(
                new LambdaQueryWrapperX<MemColonel>()
                        .eqIfPresent(MemColonel::getSysCode, sysCode)
                        .eqIfPresent(MemColonel::getColonelName, pcolonelName)
                        .eq(MemColonel::getDelFlag, DelFlagConstants.NORMAL)
        );
    }

    /**
     * 获取某个业务员的直接下属业务员列表
     * @param colonelId
     * @return
     */
    default List<MemColonel> getSubordinates(Long colonelId){
        return selectList(
                new LambdaQueryWrapperX<MemColonel>()
                    .eqIfPresent(MemColonel::getAuditState,1)
                    .eqIfPresent(MemColonel::getPcolonelId, colonelId)
                    .eq(MemColonel::getDelFlag, DelFlagConstants.NORMAL)
                    .orderByDesc(MemColonel::getColonelId));
    }

    /**
     * 获取某个业务员的所发展的业务员信息
     * @param colonelId
     * @return
     */
    default List<MemColonel> selectDevelopmentSalesmanByColonelId(Long colonelId){
        return selectList(
                new LambdaQueryWrapperX<MemColonel>()
                    .eqIfPresent(MemColonel::getDevelopPeopleId,colonelId)
                    .eq(MemColonel::getDelFlag, DelFlagConstants.NORMAL)
                    .orderByDesc(MemColonel::getColonelId)
        );
    }
    /**
     * 获取业务员名称
     */
    default String getColonelName(Long colonelId){
        return selectOne(new LambdaQueryWrapperX<MemColonel>()
                .eqIfPresent(MemColonel::getColonelId, colonelId)
                .select(MemColonel::getColonelName)).getColonelName();
    }

    /**
     * 获取业务员统计列表
     */
    List<MemColonelStatisticsListRespVo> getColonelStatisticsListPage(@Param("page") Page<MemColonelStatisticsListReqVo> page, @Param("colonelIds") List<Long> colonelIds, @Param("areaId") Long areaId);

    /**
     *  查询业务员列表
     * @param areaIds
     * @return
     */
    List<MemColonel> getColonelListByAreaIds(@Param("areaIds") List<Long> areaIds);

    default List<Long> getColonelIdList(Long sysCode,Integer status,String colonelName,String colonelPhone,List<Long> areaIds){
        return selectList(new LambdaQueryWrapperX<MemColonel>()
                .eqIfPresent(MemColonel::getSysCode, sysCode)
                .eqIfPresent(MemColonel::getColonelPhone,colonelPhone)
                .eq(MemColonel::getStatus,status)
                .eq(MemColonel::getDelFlag, DelFlagConstants.NORMAL)
                .eq(MemColonel::getAuditState,NumberPool.INT_ONE)
                .likeIfPresent(MemColonel::getColonelName, colonelName)
                .inIfPresent(MemColonel::getAreaId,areaIds)
                .select(MemColonel::getColonelId)).stream().map(MemColonel::getColonelId).collect(Collectors.toList());
    }

     default List<MemColonel> getAreaIdExistColone(Long areaId, Long sysCode){
         return selectList(new LambdaQueryWrapperX<MemColonel>()
                 .eq(MemColonel::getAreaId, areaId)
                 .eq(MemColonel::getSysCode, sysCode)
         );
     }
}
