package com.zksr.member.service;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import com.zksr.common.core.domain.vo.openapi.MemBranchSyncReqVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.file.api.model.ImportResultVo;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncBranchCallDTO;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.file.api.model.ImportResultVo;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.BranchSupplierDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.branch.excel.MemBranchImportExcel;
import com.zksr.member.api.branch.vo.BranchDeleteReqVO;
import com.zksr.member.api.branch.vo.MemberBranchReqVO;
import com.zksr.member.api.branchLifecycle.dto.ColonelBranchLifecycleQtyDTO;
import com.zksr.member.controller.branch.balance.dto.MemBranchBalancePageReqDTO;
import com.zksr.member.controller.branch.vo.BranchDropdownReqVO;
import com.zksr.member.controller.branch.vo.BranchBatchEditVO;
import com.zksr.member.controller.branch.vo.MemBranchPageReqVO;
import com.zksr.member.controller.branch.dto.MemBranchRespDTO;
import com.zksr.member.controller.branch.vo.*;
import com.zksr.member.domain.MemBranch;
import com.zksr.report.api.homePages.dto.HomePagesBranchDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;

/**
 * 门店信息Service接口
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
public interface IMemBranchService {

    /**
     * 新增门店信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemBranch(@Valid MemBranchSaveReqVO createReqVO);

    /**
     * 修改门店信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemBranch(@Valid MemBranchSaveReqVO updateReqVO);

    /**
     * 修改门店信息的首单相关信息
     *
     * @param branchDTO
     * @return
     */
    void updateFirstOrder(BranchDTO branchDTO);

    /**
     * 删除门店信息
     *
     * @param branchId 门店id
     */
    public void deleteMemBranch(Long branchId);

    /**
     * 批量删除门店信息
     *
     * @param branchIds 需要删除的门店信息主键集合
     * @return 结果
     */
    public BranchDeleteReqVO deleteMemBranchByBranchIds(Long[] branchIds);

    /**
     * 获得门店信息
     *
     * @param branchId 门店id
     * @return 门店信息
     */
    public MemBranch getMemBranch(Long branchId);

    /**
     * 获得门店信息分页
     *
     * @param pageReqVO 分页查询
     * @return 门店信息分页
     */
    PageResult<MemBranch> getMemBranchPage(MemBranchPageReqVO pageReqVO);

    /**
     * 根据门店ID查询门店和业务员信息
     * @param branchId
     * @return
     */
    BranchDTO getMemBranchByBranchId(Long branchId);

    /**
     * 停用门店
     * @param branchId
     */
    public void disable(Long branchId);

    /**
     * 启用门店
     * @param branchId
     */
    public void enable(Long branchId);

    /**
     * 批量审核门店信息
     *
     * @param branchIds 需要审核的门店信息主键集合
     * @return 结果
     */
    public void batchAudit(Long[] branchIds);

    /**
     * 更新缓存
     * @param branchId
     */
    public void reloadBranchDTOCache(Long branchId);

    /**
    * @Description: 门店绑定入驻商
    * @Author: liuxingyu
    * @Date: 2024/3/28 16:47
    */
    void bindSupplier(BranchSupplierDTO branchSupplierDTO);

    /**
    * @Description: 根据门店编号获取绑定的入驻商
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:09
    */
    List<Long> getSupplierByBranchId(Long branchId);

    /**
    * @Description: 根据入驻商ID获取门店ID
    * @Author: liuxingyu
    * @Date: 2024/3/28 18:15
    */
    List<Long> getBySupplier(Long supplierId);

    /**
     * 根据用户编号查询用户绑定默认门店信息
     * @param memberId
     * @param sysCode
     * @return
     */
    MemBranch getDefaultBranchByMemberId(Long memberId,Long sysCode);

    /**
     * 根据用户编号查询用户绑定门店信息列表
     * @param memberId
     * @return
     */
    List<BranchDTO> getBranchListByMemberId(MemberBranchReqVO memberBranchReqVO);

    /**
     * 根据用户编号查询未审核用户绑定门店信息列表
     * @param memberBranchReqVO
     * @return
     */
    List<BranchDTO> getUnauditedBranchListByMemberId(MemberBranchReqVO memberBranchReqVO);

    /**
     * 获取集合选中数据 (暂时用于多选回显别名)
     * @param branchIds    门店ID集合
     * @return  门店ID集合对应的数据集
     */
    List<MemBranchSelectedRespVO> getSelectedMemberBranch(List<Long> branchIds);

    /**
    * @Description: 获取门店下拉选
    * @Author: liuxingyu
    * @Date: 2024/4/12 10:23
    */
    List<MemBranchRespVO> getBranchDropdown(BranchDropdownReqVO dropdownReqVO);

    /**
     * 获取半年内所有已过期的门店信息
     * @return 失效天数
     * @param sysCode
     */
    List<BranchDTO> getAllExpirationDateBranchList(Long sysCode);


    /**
     * 批量修改门店的状态
     * @param branchDTO
     * @return
     */
    void updateStatusByBranchIds(BranchDTO branchDTO);

    /**
     * 更新最后一次登陆时间
     * @param sysCode
     * @param branchId
     */
    void updateLastLoginTime(Long sysCode,Long branchId);

    /**
    * @Description: 获取门店集合
    * @Author: liuxingyu
    * @Date: 2024/5/15 9:04
    */
    List<MemBranchRespVO> getBranchList();

    /**
     * @Description: 获取门店集合
     * @Author: liuxingyu
     * @Date: 2024/5/15 9:04
     */
    List<MemBranchRespDTO> getBranchListPage();

    /**
    * @Description: 入驻商绑定门店
    * @Author: liuxingyu
    * @Date: 2024/5/15 11:03
    */
    Integer bindBranch(BranchSupplierDTO branchSupplierDTO);


    /**
     * 门店、门店用户信息导入
     * @param branchList
     * @return
     */
    String impordData(List<MemBranchImportExcel> branchList);

    FileImportHandlerVo impordDataEvent(List<MemBranchImportExcel> branchList, Long fileImportId, Long dcId, Long sysCode,Integer seq);

    /**
     * 发送ERP 门店信息
     *
     * @return 结果
     */
    public void sendErp(MemBranchSaveReqVO data, OperationType type);

    /**
     * 更新微信商户认证信息
     * @param branchId  门店ID
     * @param openid    认证openid
     */
    void updateWechatMerchantAuthOpenid(Long branchId, String openid);

    /**
     * 刷新ES门店下单销售信息
     * @param sysCode 平台商编码
     */
    void refreshEsBranchSalesInfoJobHandler(Long sysCode);

    /**
     * 初始化日期区间ES门店下单销售信息
     * @param sysCode 平台商编码
     * @param startDate 开始日期（yyyy-MM-dd）
     * @param endDate 结束日期（yyyy-MM-dd）
     */
    void initEsBranchSalesInfoJobHandler(Long sysCode, String startDate, String endDate);


    List<Long> getAllBranchIdList(Long minBranchId);

    PageResult<SyncBranchCallDTO> getBranchDataPage(Integer pageNum, Integer pageSize);

    /**
     * 同步门店信息给第三方
     * @param branchId 门店ID
     * @param operationTypeCode  操作类型
     */
    void syncBranchData(Long branchId,String operationTypeCode);

    Map<Long, BranchDTO> listByBranchIds(List<Long> branchIds);

    /**
     * 获取PC首页门店数据
     * @param reqVO
     * @return
     */
    List<HomePagesBranchDataRespDTO> getHomePagesBranchData(HomePagesReqVO reqVO);

    List<BranchDTO> getbranchListByArea(Long dcAreaId);
    /**
     * 修改门店公海信息
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemBranchSeasTime(@Valid MemBranchSaveReqVO updateReqVO);

    /**
     * 区域门店信息初始化同步到入驻商第三方系统
     * @param vo
     */
    public void syncAreaBranch(MemBranchSyncReqVO vo);

    List<Long> getAllBranchIdListBySysCode(Long sysCode);

    /**
     * 门店批量编辑
     * @param reqVo
     */
    public String branchBatchEdit(BranchBatchEditVO reqVo);

    /**
     * 门店校验
     * @param branch
     * @param branchId
     * @return
     */
    public String branchVerify(MemBranch branch,Long branchId);

    List<MemBranch> getAreaIdExistBranch(Long orderNo, Long sysCode);

    List<MemBranch> getMemBranchByChannelId(Long channelId, Long sysCode);

    List<Long> getBranchIdListByBranchName(String branchName,Long sysCode);

    /**
     * 获取该业务员所管理的门店生命周期信息
     * @return
     */
    ColonelBranchLifecycleQtyDTO getColonelBranchLifecycle();

    ImportResultVo importBranchExcel(List<MemBranchImportExcel> branchList,String type);

    Page<MemBranch> selectMemBranchBalancePage(Page<MemBranch> page, MemBranchBalancePageReqDTO pageReqDTO);

    MemBranch getBranchByOuterMerchantCode(Long sysCode, String outerMerchantCode);
}
