package com.zksr.member.controller.colonelApp.dto;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.member.controller.colonelTarget.vo.ColonelBranchTargetRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("业务员App - 业务员目标返回汇总对象")
public class ColonelAppTargetRespDTO {

    @ApiModelProperty(value = "业务员目标总销售额")
    private BigDecimal targetSalesAmtTotal = BigDecimal.ZERO;

    @ApiModelProperty(value = "业务员总销售额")
    private BigDecimal salesAmtTotal = BigDecimal.ZERO;

    @ApiModelProperty(value = "目标总新开客户数")
    private Long targetAddBranchNumTotal = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "总新开客户数")
    private Long addBranchNumTotal = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "目标总活跃客户数")
    private Long targetSaleBranchNumTotal = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "总活跃客户数")
    private Long saleBranchNumTotal = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "目标总拜访客户数")
    private Long targetVisitBranchNumTotal = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "总拜访客户数")
    private Long visitBranchNumTotal = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "目标总下单笔数")
    private Long targetOrderNumTotal = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "总下单笔数")
    private Long orderNumTotal = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "目标总客单价")
    private BigDecimal targetAvgPriceTotal = BigDecimal.ZERO;

    @ApiModelProperty(value = "总客单价")
    private BigDecimal avgPriceTotal = BigDecimal.ZERO;

    @ApiModelProperty(value = "目标总首次动销客户")
    private Long targetFirstSaleBranchNumTotal = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "总首次动销客户")
    private Long firstSaleBranchNumTotal = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "数据总数")
    private Long total;

    @ApiModelProperty(value = "业务员目标详情分页数据")
    private List<ColonelAppTargetDetailDTO> colonelAppTargetDetailDTOList;
}
