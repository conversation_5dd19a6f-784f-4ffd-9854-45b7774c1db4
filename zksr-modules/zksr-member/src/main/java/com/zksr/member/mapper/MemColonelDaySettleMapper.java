package com.zksr.member.mapper;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import com.zksr.member.domain.MemColonel;
import com.zksr.member.domain.MemColonelDaySettle;
import com.zksr.member.domain.MemColonelMonthSettle;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * 业务员 业务日结表Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Mapper
public interface MemColonelDaySettleMapper extends BaseMapperX<MemColonelDaySettle> {
    /**
     * 根据系统编码SysCode 和 日期查询业务员 日结数据汇总
     * @param sysCode
     * @return
     */
    List<MemColonelMonthSettle> getListBySysCodeAndDate(@Param("sysCode") Long sysCode, @Param("settleStartDate") Date settleStartDate,
                                                        @Param("settleEndDate") Date settleEndDate, @Param("colonelId") Long colonelId);



    Integer deleteColonelDaySettleBySysCodeAndDate(@Param("sysCode") Long sysCode, @Param("date") Date date, @Param("colonelId") Long colonelId);


    default MemColonelDaySettle getByColonelIdAndDate(@Param("sysCode") Long sysCode, @Param("settleDate") Date settleDate,@Param("colonelId") Long colonelId) {
        return selectOne(new LambdaQueryWrapperX<MemColonelDaySettle>()
                .eq(MemColonelDaySettle::getSysCode, sysCode)
                .eq(MemColonelDaySettle::getSettleCreateDate, settleDate)
                .eqIfPresent(MemColonelDaySettle::getColonelId, colonelId)
        );
    }
}
