package com.zksr.member.mapper;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.domain.MemColonelBranchZip;
import org.apache.ibatis.annotations.Mapper;

/**
 * 门店业务员关系拉链Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@SuppressWarnings("all")
@Mapper
public interface MemColonelBranchZipMapper extends BaseMapperX<MemColonelBranchZip> {
    default MemColonelBranchZip selectMemColonelBranchZipByBranchId(MemBranch memBranch){
        LambdaQueryWrapper<MemColonelBranchZip> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemColonelBranchZip::getSysCode, memBranch.getSysCode())
                    .eq(MemColonelBranchZip::getBranchId, memBranch.getBranchId())
                    .orderByDesc(MemColonelBranchZip::getStartDate)
                    .last("LIMIT 1");
        return selectOne(queryWrapper);
    }

    void updateMemColonelBranchZip(MemColonelBranchZip currentRelation);
}
