package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.domain.MemBranchUser;
import com.zksr.member.controller.branchUser.vo.MemBranchUserPageReqVO;
import com.zksr.member.controller.branchUser.vo.MemBranchUserSaveReqVO;

import java.util.List;

/**
 * 门店用户关联关系Service接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface IMemBranchUserService {

    /**
     * 新增门店用户关联关系
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemBranchUser(@Valid MemBranchUserSaveReqVO createReqVO);

    /**
     * 修改门店用户关联关系
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemBranchUser(@Valid MemBranchUserSaveReqVO updateReqVO);

    /**
     * 删除门店用户关联关系
     *
     * @param sysCode 平台商id
     */
    public void deleteMemBranchUser(Long sysCode);

    /**
     * 批量删除门店用户关联关系
     *
     * @param sysCodes 需要删除的门店用户关联关系主键集合
     * @return 结果
     */
    public void deleteMemBranchUserBySysCodes(Long[] sysCodes);

    /**
     * 获得门店用户关联关系
     *
     * @param sysCode 平台商id
     * @return 门店用户关联关系
     */
    public MemBranchUser getMemBranchUser(Long sysCode);

    /**
     * 获得门店用户关联关系分页
     *
     * @param pageReqVO 分页查询
     * @return 门店用户关联关系分页
     */
    PageResult<MemBranchUser> getMemBranchUserPage(MemBranchUserPageReqVO pageReqVO);

    /**
     * 根据用户编号查询用户绑定门店信息
     * @param memberId
     * @return
     */
    List<MemBranchUser> getMemBranchUserByMemberId(Long memberId);

    /**
     * 根据门店ID查询门店用户数量
     * @param branchId
     * @return
     */
    Long countByBranchId(Long branchId);

}
