package com.zksr.member.mq;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.GeoUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.elasticsearch.domain.EsColonelAppBranch;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.common.elasticsearch.service.EsColonelAppBranchService;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonelApp.dto.PageDataDTO;
import com.zksr.member.api.colonelApp.dto.PageDataReqDTO;
import com.zksr.member.domain.MemBranchRegister;
import com.zksr.member.domain.MemColonelVisitLog;
import com.zksr.member.service.IMemBranchRegisterService;
import com.zksr.member.service.IMemColonelVisitLogService;
import com.zksr.member.service.IMemberCacheService;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.ColonelAppBranchOrderDTO;
import com.zksr.trade.api.order.dto.ColonelAppPageOrderDTO;
import com.zksr.trade.api.order.dto.TrdSettleDTO;
import com.zksr.trade.api.orderSettle.OrderSettleApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.CacheConstants.COLONEL_APP_PAGE_DATA;
import static com.zksr.member.constant.MemberConstant.*;

/**
*
 * 业务员APP模块 消费者
* <AUTHOR>
* @date 2024/5/9 11:39
*/
@Configuration
@Slf4j
public class MemberAppMqConsumer {

    @Autowired
    private EsColonelAppBranchService colonelAppBranchService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IMemberCacheService memberCacheService;

    @Autowired
    private OrderApi orderApi;

    @Autowired
    private IMemColonelVisitLogService memColonelVisitLogService;

    @Autowired
    private IMemBranchRegisterService memBranchRegisterService;

    @Autowired
    private AfterApi afterApi;

    @Autowired
    private OrderSettleApi orderSettleApi;


    /**
     * 业务员APP客户信息统计Es消费者
     * 订单来源 : {@link com.zksr.trade.mq.TradeMqProducer#sendEsColonelAppBranchEvent(ColonelAppBranchDTO)}
     * 登陆来源 : {@link com.zksr.portal.mq.mall.MallMqProducer#sendEsColonelAppBranchEvent(ColonelAppBranchDTO)}
     * 基础来源 : {@link MemberAppMqProducer#sendEsColonelAppBranchBaseEvent(ColonelAppBranchDTO)}
     * @return
     */
    @Bean
    @SuppressWarnings("all")
    public Consumer<ColonelAppBranchDTO> colonelAppBranchEvent() {
        return (data) -> {
            log.info("收到业务员APP统计客户信息消息事件:{}", data);
            if(ToolUtil.isEmpty(data) || ToolUtil.isEmpty(data.getBranchId()) || ToolUtil.isEmpty(data.getSysCode()) || ToolUtil.isEmpty(data.getType())){
                log.error("收到业务员APP统计客户信息异常：{}",data);
            }

            String createYearMonth = DateUtils.dateTimeNow(DateUtils.YYYY_MM);
            //设置ES的ID
            String id = data.getBranchId() + StringPool.UNDERSCORE + data.getSysCode() + StringPool.UNDERSCORE + createYearMonth;

            //查询ES 获取当前客户信息
            EsColonelAppBranch branch = colonelAppBranchService.seachColonelAppBranchById(id);

            if(Objects.isNull(branch)){
                branch = new EsColonelAppBranch();
                branch.setId(id)
                        .setBranchId(data.getBranchId())
                        .setSysCode(data.getSysCode())
                        .setCreateYearMonth(createYearMonth)
                ;
            }
            // 设置基础信息
            BranchDTO branchDTO = memberCacheService.getBranchDto(data.getBranchId());
            if (Objects.nonNull(branchDTO)) {
                branch.setColonelId(branchDTO.getColonelId());
                branch.setAreaId(branchDTO.getAreaId());
                branch.setKeyword(StringUtils.format("{},{},{},{}", branchDTO.getBranchName(), branchDTO.getContactName(), branchDTO.getBranchAddr(), branchDTO.getContactPhone()));
                if (GeoUtils.isValidCoordinate(branchDTO.getLongitude(), branchDTO.getLatitude())) {
                    branch.setLocation(StringUtils.format("{},{}", branchDTO.getLatitude(), branchDTO.getLongitude()));
                }
            }

            //如果客户信息存在 则更新 不存在则组装数据
            assembleBranch(branch,data);

            log.info("业务员APP统计客户信息消息本次更新内容为：{}" , branch.toString());

            //更新门店ES数据
            colonelAppBranchService.saveColonelAppBranch(branch);
        };
    }

    /**
     * 业务员APP首页信息消息消费
     * 订单来源 : {@link com.zksr.trade.mq.TradeMqProducer#sendColoenlAppPageDataEvent(PageDataReqDTO)}
     * 基础来源 : {@link MemberAppMqProducer#sendColoenlAppPageDataEvent(PageDataReqDTO)}
     * @return
     */
    //@DistributedLock(prefix = RedisLockConstants.LOCK_COLONEL_APP, condition = "#data.colonelId", tryLock = true)
    @Bean
    public Consumer<PageDataReqDTO> colonelAppPageDataEvent(){
        return (data) -> {
            log.info("收到业务员APP统计首页信息消息事件:{}", data);
            if(ToolUtil.isEmpty(data)
                    || ToolUtil.isEmpty(data.getColonelId())
                    || ToolUtil.isEmpty(data.getSysCode())
                    || ToolUtil.isEmpty(data.getType())){
                log.error("收到业务员APP统计首页信息异常：{}",data);
                return;
            }

            //设置Key
            String key = CacheConstants.getColonelAppPageDataKey(data.getColonelId(), DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()));
            //获取缓存数据
            PageDataDTO pageDataDTO = redisService.getCacheObject(key);
            if(ToolUtil.isEmpty(pageDataDTO)){
                pageDataDTO = new PageDataDTO();

            }

            //组装缓存数据
            assemblePageData(pageDataDTO,data);

            log.info("业务员APP统计首页信息消息本次更新内容为：{}", pageDataDTO.toString());

             //存入缓存 (3天)
            redisService.setCacheObject(key,pageDataDTO, 60 * 60 * 24 * 3L, TimeUnit.SECONDS);
        };
    }

    /**
     * 组装门店ES数据
     * @param esBranch
     * @param branchDTO
     */
    private void assembleBranch(EsColonelAppBranch esBranch,ColonelAppBranchDTO branchDTO){
        switch(branchDTO.getType()){
            case ES_COLONEL_APP_BRANCH_TYPE_1:
                esBranch.setLastLoginTime(new Date());
                break;

            case ES_COLONEL_APP_BRANCH_TYPE_2:
                //获取该门店当月的订单信息
                List<ColonelAppBranchOrderDTO> branchOrderList = orderApi.getColonelAppBranchOrder(branchDTO.getBranchId()).getCheckedData();
                if(branchOrderList == null || branchOrderList.isEmpty()){
                    break;
                }

                //过滤掉实际销售金额小于0的订单
                List<ColonelAppBranchOrderDTO> orderList = branchOrderList.stream().filter(x -> x.getRealSaleAmt().compareTo(BigDecimal.ZERO) > NumberPool.INT_ZERO).collect(Collectors.toList());
                if(!orderList.isEmpty()){
                    //设置金额和数量
                    esBranch.setLastBuyTime(orderList.get(0).getCreateTime());
                    esBranch.setLastBuyAmt(orderList.get(0).getOrderAmt());
                    esBranch.setNowMonthOrderQty((long) orderList.size());
                    esBranch.setNowMonthOrderAmt(orderList.stream().map(ColonelAppBranchOrderDTO::getRealSaleAmt).reduce(BigDecimal.ZERO,BigDecimal::add));
                }
                break;

            case ES_COLONEL_APP_BRANCH_TYPE_3:
                esBranch.setLastVisitTime(new Date());
                break;

            case ES_COLONEL_APP_BRANCH_TYPE_4:
                break;

            default:
                break;
        }
    }

    /**
     * 组装业务员首页缓存数据
     * @param pageDataDTO
     * @param reqDTO
     */
    private void assemblePageData(PageDataDTO pageDataDTO,PageDataReqDTO reqDTO){

        //设置缓存数据
        pageDataDTO.setColonelId(reqDTO.getColonelId());
        pageDataDTO.setSysCode(reqDTO.getSysCode());

        switch(reqDTO.getType()){
            case COLONEL_APP_Page_DATA_TYPE_1:
                //消息发送类型 1、业务员签到
                //获取拜访门店信息
                List<MemColonelVisitLog> visitLogList = memColonelVisitLogService.getLogListByColonelIdAndDate(reqDTO.getColonelId(), DateUtils.getDayStartDate(), DateUtils.getDayEndDate());
                //去重
                Set<Long> visitBranchIds = visitLogList.stream().map(MemColonelVisitLog::getBranchId).collect(Collectors.toSet());
                pageDataDTO.setVisitQty((long) visitBranchIds.size());
                pageDataDTO.setVisitBranchIds(visitBranchIds);
                break;

            case COLONEL_APP_Page_DATA_TYPE_2:
                //消息发送类型 2、业务员拓店
                List<MemBranchRegister> registerList = memBranchRegisterService.getRegisterListByColonelIdAndDate(reqDTO.getColonelId(), DateUtils.getDayStartDate(), DateUtils.getDayEndDate());
                pageDataDTO.setAddBranchQty((registerList != null ? (long)registerList.size() : NumberPool.LONG_ZERO));
                break;

            case COLONEL_APP_Page_DATA_TYPE_3:
                //消息发送类型 3、客户下单
                //增加门店下单金额、业务员下单、下单数量、动销门店数量、业务员提成

                //=========订单信息===========
                //业务员下单金额（业务员代客下单）
                BigDecimal businessOrderAmt = BigDecimal.ZERO;
                //业务员下单数量（业务员代客下单）
                Long businessOrderQty = NumberPool.LONG_ZERO;
                //门店下单金额
                BigDecimal branchOrderAmt = BigDecimal.ZERO;
                //门店下单数量
                Long orderQty = NumberPool.LONG_ZERO;
                //动销门店集合
                Set<Long> saleBranchIds = new HashSet<>();

                for (ColonelAppPageOrderDTO order : orderApi.getColonelAppPageOrder(reqDTO.getColonelId()).getCheckedData()) {
                    //设置订单信息
                    if(order.getColonelFlag() == NumberPool.INT_ONE) {  // 业务员下单
                        businessOrderAmt = businessOrderAmt.add(order.getRealSaleAmt());
                        businessOrderQty ++;
                    }else{
                        branchOrderAmt = branchOrderAmt.add(order.getRealSaleAmt());
                        orderQty ++;
                    }

                    //设置动销门店信息
                    saleBranchIds.add(order.getBranchId());
                }
                pageDataDTO.setBusinessOrderAmt(businessOrderAmt)
                        .setBusinessOrderQty(businessOrderQty)
                        .setBranchOrderAmt(branchOrderAmt)
                        .setOrderQty(orderQty)
                        .setSaleBranchIds(saleBranchIds)
                        .setPercentageAmt(orderSettleApi.getColonelAppPercentageAmt(reqDTO.getColonelId()).getCheckedData()) //设置业务员提成
                        .setSaleBranchQty((long) saleBranchIds.size());

                break;

            case COLONEL_APP_Page_DATA_TYPE_4:
                //消息发送类型 4、客户售后退货
                //增加退货金额 计算业务员退货后减少的提成
                pageDataDTO.setBranchRefundAmt(afterApi.getColonelAppPageAfterAmt(reqDTO.getColonelId()).getCheckedData());
                pageDataDTO.setPercentageAmt(orderSettleApi.getColonelAppPercentageAmt(reqDTO.getColonelId()).getCheckedData());

/*                //增加退货金额、减少下单数量 (2024-7-23 首页下单数量修改为只统计下单数)
                pageDataDTO.setBranchRefundAmt(pageDataDTO.getBranchRefundAmt() != null ? pageDataDTO.getBranchRefundAmt().add(reqDTO.getBranchRefundAmt()) : reqDTO.getBranchRefundAmt());
                //pageDataDTO.setOrderQty(pageDataDTO.getOrderQty() != null ? pageDataDTO.getOrderQty() - NumberPool.LONG_ONE : NumberPool.LOWER_GROUND_LONG);

                //计算业务员退货后减少的提成
                //筛选当前业务员的结算金额
                pageDataDTO.setPercentageAmt(reqDTO.getColonelSettleAmt().add(pageDataDTO.getPercentageAmt() != null ? pageDataDTO.getPercentageAmt() : BigDecimal.ZERO));*/
                break;

            case COLONEL_APP_PAGE_DATA_TYPE_5:
                //消息发送类型 5、客户下单、客户售后退货 (处理上级业务员的提成金额缓存)
                //计算业务员提成
                //筛选当前业务员的结算金额
                pageDataDTO.setPercentageAmt(orderSettleApi.getColonelAppPercentageAmt(reqDTO.getColonelId()).getCheckedData());
                break;
            default:
                break;
        }
    }


}
