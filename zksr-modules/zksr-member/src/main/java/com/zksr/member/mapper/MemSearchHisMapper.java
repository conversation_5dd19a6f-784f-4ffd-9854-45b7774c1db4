package com.zksr.member.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.controller.searchHis.vo.MemSearchHisRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemSearchHis;
import com.zksr.member.controller.searchHis.vo.MemSearchHisPageReqVO;
import org.apache.ibatis.annotations.Param;


/**
 * 搜索历史Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Mapper
public interface MemSearchHisMapper extends BaseMapperX<MemSearchHis> {
    default PageResult<MemSearchHis> selectPage(MemSearchHisPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemSearchHis>()
                    .eqIfPresent(MemSearchHis::getSearchHisId, reqVO.getSearchHisId())
                    .eqIfPresent(MemSearchHis::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemSearchHis::getMemberId, reqVO.getMemberId())
                    .eqIfPresent(MemSearchHis::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(MemSearchHis::getWords, reqVO.getWords())
                .orderByDesc(MemSearchHis::getSearchHisId));
    }

    Page<MemSearchHisRespVO> selectSearchHisPage (@Param("reqVO") MemSearchHisPageReqVO reqVO,@Param("page")Page<MemSearchHisPageReqVO> page);
}
