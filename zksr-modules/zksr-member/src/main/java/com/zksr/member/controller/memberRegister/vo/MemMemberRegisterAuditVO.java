package com.zksr.member.controller.memberRegister.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;


/**
* 用户注册信息审核接口实体VO
* @date 2024/12/31 16:02
* <AUTHOR>
*/
@ApiModel("用户注册信息审核接口实体VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemMemberRegisterAuditVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 用户注册ID集合 */
    @Excel(name = "用户注册ID集合")
    @ApiModelProperty(value = "用户注册ID集合")
    private Long[] memberRegisterIds;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    @NotNull(message = "请选择门店需要绑定的业务员信息")
    private Long colonelId;


    /** 渠道ID */
    @Excel(name = "渠道ID")
    @ApiModelProperty(value = "渠道ID")
    private Long channelId;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;

    @Excel(name = "是否支持货到付款", readConverterExp = "0-不支持,1-支持")
    @ApiModelProperty(value = "是否支持货到付款(0,否 1,是)")
    private Integer hdfkSupport;

    @Excel(name = "货到付款最大可欠款金额")
    @ApiModelProperty("货到付款最大可欠款金额")
    private BigDecimal hdfkMaxAmt;

    @ApiModelProperty("是否支持在线收款 0：否，1：是")
    private Integer isPayOnline;

}
