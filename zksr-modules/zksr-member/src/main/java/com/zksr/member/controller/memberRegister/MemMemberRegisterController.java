package com.zksr.member.controller.memberRegister;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.member.controller.memberRegister.vo.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemMemberRegister;
import com.zksr.member.service.IMemMemberRegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.convert.memberRegister.MemMemberRegisterConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 用户注册信息Controller
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@Api(tags = "管理后台 - 用户注册信息接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/memberRegister")
public class MemMemberRegisterController {
    @Autowired
    private IMemMemberRegisterService memMemberRegisterService;

    /**
     * 新增用户注册信息
     */
    @ApiOperation(value = "新增用户注册信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "用户注册信息", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemMemberRegisterSaveReqVO createReqVO) {
        return success(memMemberRegisterService.insertMemMemberRegister(createReqVO));
    }

    /**
     * 修改用户注册信息
     */
    @ApiOperation(value = "修改用户注册信息", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "用户注册信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemMemberRegisterSaveReqVO updateReqVO) {
            memMemberRegisterService.updateMemMemberRegister(updateReqVO);
        return success(true);
    }

    /**
     * 删除用户注册信息
     */
    @ApiOperation(value = "删除用户注册信息", httpMethod = HttpMethod.DEL, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "用户注册信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{memberRegisterIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] memberRegisterIds) {
        memMemberRegisterService.deleteMemMemberRegisterByMemberRegisterIds(memberRegisterIds);
        return success(true);
    }

    /**
     * 获取用户注册信息详细信息
     */
    @ApiOperation(value = "获得用户注册信息详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{memberRegisterId}")
    public CommonResult<MemMemberRegisterRespVO> getInfo(@PathVariable("memberRegisterId") Long memberRegisterId) {
        MemMemberRegister memMemberRegister = memMemberRegisterService.getMemMemberRegister(memberRegisterId);
        return success(MemMemberRegisterConvert.INSTANCE.convert(memMemberRegister));
    }

    /**
     * 分页查询用户注册信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得用户注册信息分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(dcAlias = "`mem_member_register`")
    public CommonResult<PageResult<MemMemberRegisterPageRespVO>> getPage(@Valid MemMemberRegisterPageReqVO pageReqVO) {
        return success(memMemberRegisterService.getMemMemberRegisterPage(pageReqVO));
    }

    /**
     * 审核用户注册信息
     */
    @ApiOperation(value = "审核用户注册信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.AUDIT)
    @RequiresPermissions(Permissions.AUDIT)
    @Log(title = "审核用户注册信息", businessType = BusinessType.OTHER)
    @PostMapping("/auditMemberRegister")
    public CommonResult<Boolean> auditMemberRegister(@Valid @RequestBody MemMemberRegisterAuditVO auditVO) {
        memMemberRegisterService.batchAuditMemberRegister(auditVO);
        return success(true);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:memberRegister:add";
        /** 编辑 */
        public static final String EDIT = "member:memberRegister:edit";
        /** 删除 */
        public static final String DELETE = "member:memberRegister:remove";
        /** 列表 */
        public static final String LIST = "member:memberRegister:list";
        /** 查询 */
        public static final String GET = "member:memberRegister:query";
        /** 停用 */
        public static final String DISABLE = "member:memberRegister:disable";
        /** 启用 */
        public static final String ENABLE = "member:memberRegister:enable";
        /** 审核 */
        public static final String AUDIT = "member:memberRegister:audit";
    }
}
