package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 业务员信息对象 mem_colonel
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@TableName(value = "mem_colonel")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MemColonel extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 业务员id */
    @TableId(type = IdType.ASSIGN_ID)
    @Excel(name = "业务员id")
    private Long colonelId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 业务员手机号 */
    @Excel(name = "业务员手机号")
    private String colonelPhone;

    /** 业务员名 */
    @Excel(name = "业务员名")
    private String colonelName;

    /** 业务员级别（职务） */
    @Excel(name = "业务员级别", readConverterExp = "职=务")
    private Long colonelLevel;

    /** 父业务员id */
    @Excel(name = "父业务员id")
    private Long pcolonelId;

    /** 性别（数据字典） */
    @Excel(name = "性别", readConverterExp = "数=据字典")
    private Integer sex;

    /** 状态 1正常 0停用 */
    @Excel(name = "状态  1正常 0停用")
    private Integer status;

    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date birthday;

    /** 籍贯 */
    @Excel(name = "籍贯")
    private String birthplace;

    /** 入职日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "入职日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date entryDate;

    /** 学历(数据字典) */
    @Excel(name = "学历(数据字典)")
    private String edu;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idcard;

    /** 提成系数 */
    @Excel(name = "提成系数")
    private BigDecimal percentageRate;

    /** 联系地址 */
    @Excel(name = "联系地址")
    private String contactAddr;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 是否是业务管理员（1:是，2：否） */
    @Excel(name = "是否是业务管理员", readConverterExp = "Y:是，N:否")
    private String isColonelAdmin;

    /** 部门 */
    @Excel(name = "部门")
    private Long deptId;

    /** APP下单改价（1:是，2：否） */
    @Excel(name = "APP下单改价", readConverterExp = "Y:是，N:否")
    private String appOrderPriceAdjust;

    /** APP退货改价（1:是，2：否） */
    @Excel(name = "APP退货改价", readConverterExp = "Y:是，N:否")
    private String appAfterPriceAdjust;

    /** 下单自动审核（1:是，2：否） */
    @Excel(name = "下单自动审核", readConverterExp = "Y:是，N:否")
    private String orderAutoApprove;

    /** 删除状态  (0正常  2已删除) */
    @ApiModelProperty(value = "状态")
    private Integer delFlag;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 来源 APP或PC */
//    @Excel(name = "来源", readConverterExp = "APP，PC")
    private String source;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditTime;

    /** 审核状态 */
    @Excel(name = "审核状态")
    @ApiModelProperty(value = "0待审核 1审核通过 2审核不通过")
    private Integer auditState = 0;

    /** 审核备注 */
    @Excel(name = "审核备注")
    private String auditMemo;

    /** 发展人id */
    private Long developPeopleId;

    /** 业务员头像 */
    private String avatarImages;

    /** 业务员头像修改时间 */
    private Date avatarUpdateTime;

    @Excel(name = "签名密钥 （公钥）")
    @ApiModelProperty(value = "签名密钥 （公钥）")
    private String signSecret;

    @Excel(name = "签名密钥 (私钥)")
    @ApiModelProperty(value = "签名密钥 (私钥)")
    private String signSecretPrivate;

    @ApiModelProperty("三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    private Long threeAreaCityId;;

    /**
     * 公众号openid
     */
    private String publishOpenid;
}