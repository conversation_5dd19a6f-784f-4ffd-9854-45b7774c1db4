package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.domain.MemMemberAddress;
import com.zksr.member.controller.address.vo.MemMemberAddressPageReqVO;
import com.zksr.member.controller.address.vo.MemMemberAddressSaveReqVO;

/**
 * 用户地址Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IMemMemberAddressService {

    /**
     * 新增用户地址
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemMemberAddress(@Valid MemMemberAddressSaveReqVO createReqVO);

    /**
     * 修改用户地址
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemMemberAddress(@Valid MemMemberAddressSaveReqVO updateReqVO);

    /**
     * 删除用户地址
     *
     * @param id ID主键
     */
    public void deleteMemMemberAddress(Long id);

    /**
     * 批量删除用户地址
     *
     * @param ids 需要删除的用户地址主键集合
     * @return 结果
     */
    public void deleteMemMemberAddressByIds(Long[] ids);

    /**
     * 获得用户地址
     *
     * @param id ID主键
     * @return 用户地址
     */
    public MemMemberAddress getMemMemberAddress(Long id);

    /**
     * 获得用户地址分页
     *
     * @param pageReqVO 分页查询
     * @return 用户地址分页
     */
    PageResult<MemMemberAddress> getMemMemberAddressPage(MemMemberAddressPageReqVO pageReqVO);

}
