package com.zksr.member.controller.loginHis;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.controller.loginHis.vo.MemLoginHisPageReqVO;
import com.zksr.member.controller.loginHis.vo.MemLoginHisRespVO;
import com.zksr.member.controller.loginHis.vo.MemLoginHisSaveReqVO;
import com.zksr.member.convert.loginHis.MemLoginHisConvert;
import com.zksr.member.domain.MemLoginHis;
import com.zksr.member.service.IMemLoginHisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 登录历史Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Api(tags = "管理后台 - 登录历史接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/loginHis")
public class MemLoginHisController {
    @Autowired
    private IMemLoginHisService memLoginHisService;

    /**
     * 新增登录历史
     */
    @ApiOperation(value = "新增登录历史", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "登录历史", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemLoginHisSaveReqVO createReqVO) {
        return success(memLoginHisService.insertMemLoginHis(createReqVO));
    }

    /**
     * 修改登录历史
     */
    @ApiOperation(value = "修改登录历史", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "登录历史", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemLoginHisSaveReqVO updateReqVO) {
            memLoginHisService.updateMemLoginHis(updateReqVO);
        return success(true);
    }

    /**
     * 删除登录历史
     */
    @ApiOperation(value = "删除登录历史", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "登录历史", businessType = BusinessType.DELETE)
    @DeleteMapping("/{loginHisIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] loginHisIds) {
        memLoginHisService.deleteMemLoginHisByLoginHisIds(loginHisIds);
        return success(true);
    }

    /**
     * 获取登录历史详细信息
     */
    @ApiOperation(value = "获得登录历史详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{loginHisId}")
    public CommonResult<MemLoginHisRespVO> getInfo(@PathVariable("loginHisId") Long loginHisId) {
        MemLoginHis memLoginHis = memLoginHisService.getMemLoginHis(loginHisId);
        return success(MemLoginHisConvert.INSTANCE.convert(memLoginHis));
    }

    /**
     * 分页查询登录历史
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得登录历史分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemLoginHisRespVO>> getPage(@Valid MemLoginHisPageReqVO pageReqVO) {
        PageResult<MemLoginHis> pageResult = memLoginHisService.getMemLoginHisPage(pageReqVO);
        return success(MemLoginHisConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:loginHis:add";
        /** 编辑 */
        public static final String EDIT = "member:loginHis:edit";
        /** 删除 */
        public static final String DELETE = "member:loginHis:remove";
        /** 列表 */
        public static final String LIST = "member:loginHis:list";
        /** 查询 */
        public static final String GET = "member:loginHis:query";
        /** 停用 */
        public static final String DISABLE = "member:loginHis:disable";
        /** 启用 */
        public static final String ENABLE = "member:loginHis:enable";
    }
}
