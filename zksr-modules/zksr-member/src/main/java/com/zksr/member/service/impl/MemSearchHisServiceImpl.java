package com.zksr.member.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LFUCache;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.member.vo.MemMemberPageReqVO;
import com.zksr.member.controller.searchHis.vo.MemSearchHisRespVO;
import com.zksr.member.service.IMemberCacheService;
import com.zksr.product.api.keywords.KeywordsApi;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemSearchHisMapper;
import com.zksr.member.convert.searchHis.MemSearchHisConvert;
import com.zksr.member.domain.MemSearchHis;
import com.zksr.member.controller.searchHis.vo.MemSearchHisPageReqVO;
import com.zksr.member.controller.searchHis.vo.MemSearchHisSaveReqVO;
import com.zksr.member.service.IMemSearchHisService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 搜索历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class MemSearchHisServiceImpl implements IMemSearchHisService {

    @Autowired
    private MemSearchHisMapper memSearchHisMapper;

    @Autowired
    private IMemberCacheService memberCacheService;

    @Resource
    private KeywordsApi keywordsApi;

    @Resource
    private SpuApi spuApi;

    /**
     * 新增搜索历史
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemSearchHis(MemSearchHisSaveReqVO createReqVO) {
        // 插入
        MemSearchHis memSearchHis = MemSearchHisConvert.INSTANCE.convert(createReqVO);
        memSearchHisMapper.insert(memSearchHis);
        // 返回
        return memSearchHis.getSearchHisId();
    }

    /**
     * 修改搜索历史
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemSearchHis(MemSearchHisSaveReqVO updateReqVO) {
        memSearchHisMapper.updateById(MemSearchHisConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除搜索历史
     *
     * @param searchHisId 搜索历史id
     */
    @Override
    public void deleteMemSearchHis(Long searchHisId) {
        // 删除
        memSearchHisMapper.deleteById(searchHisId);
    }

    /**
     * 批量删除搜索历史
     *
     * @param searchHisIds 需要删除的搜索历史主键
     * @return 结果
     */
    @Override
    public void deleteMemSearchHisBySearchHisIds(Long[] searchHisIds) {
        for (Long searchHisId : searchHisIds) {
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deleteMemSearchHis(searchHisId);
        }
    }

    /**
     * 获得搜索历史
     *
     * @param searchHisId 搜索历史id
     * @return 搜索历史
     */
    @Override
    public MemSearchHis getMemSearchHis(Long searchHisId) {
        return memSearchHisMapper.selectById(searchHisId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<MemSearchHisRespVO> getMemSearchHisPage(MemSearchHisPageReqVO pageReqVO) {
        Page<MemSearchHisPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<MemSearchHisRespVO> memSearchHisPage = memSearchHisMapper.selectSearchHisPage(pageReqVO, page);
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        if (ToolUtil.isEmpty(memSearchHisPage)) {
            return new PageResult<>(memSearchHisPage.getRecords(), memSearchHisPage.getTotal());
        }
        LFUCache<Long, AreaDTO> areaCache = CacheUtil.newLFUCache(0);
        memSearchHisPage.getRecords().forEach(memSearchHis -> {
            // 区域
            AreaDTO areaDTO = Objects.nonNull(memSearchHis.getAreaId()) ? areaCache.get(memSearchHis.getAreaId(), () -> memberCacheService.getAreaDto(memSearchHis.getAreaId())) : null;
            if (Objects.nonNull(areaDTO)) {
                memSearchHis.setArea(areaDTO.getAreaName());
            }

            // 是否加入词库
            if (Objects.nonNull(memSearchHis.getWords())) {
                memSearchHis.setJoin(keywordsApi.searchKeywords(sysCode, memSearchHis.getWords()).getData());
            }

        });
        return new PageResult<>(memSearchHisPage.getRecords(), memSearchHisPage.getTotal());
    }

    @Override
    public boolean batchAssociationSpu(String keywords, List<Long> spuIds, Integer associationType) {
        if (ToolUtil.isEmpty(spuIds) || ToolUtil.isEmpty(keywords)) throw exception(MEM_SEARCH_WORD_NOT_EXISTS);
        spuApi.associationKeyword(keywords, spuIds, associationType);
        return true;
    }


//    private void validateMemSearchHisExists(Long searchHisId) {
//        if (memSearchHisMapper.selectById(searchHisId) == null) {
//            throw exception(MEM_SEARCH_HIS_NOT_EXISTS);
//        }
//    }

// TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
// ========== 搜索历史 TODO 补充编号 ==========
// ErrorCode MEM_SEARCH_HIS_NOT_EXISTS = new ErrorCode(TODO 补充编号, "搜索历史不存在");


}
