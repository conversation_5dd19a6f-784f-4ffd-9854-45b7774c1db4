package com.zksr.member.controller.colonelApp.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/24 9:35
 * @注释
 */
@ApiModel("业务员App-客户列表分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemColonelAppCustomerListPageReqVO extends PageParam {
    private static final long serialVersionUID = 1L;

    /** 门店名称 */
    @Excel(name = "搜索关键字 客户、联系人、地址、联系电话")
    @ApiModelProperty(value = "搜索关键字 客户、联系人、地址、联系电话")
    private String keyword;

    /** 拜访状态 0 已拜访  1 未拜访 */
    @Excel(name = "拜访状态 0 已拜访  1 未拜访")
    @ApiModelProperty(value = "拜访状态 0 已拜访  1 未拜访")
    private String visitStatus;

    /** 开始时间 */
    @Excel(name = "开始时间")
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /** 门店名称 */
    @Excel(name = "结束时间")
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /** 门店ID */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    private Long branchId;


    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    /** 排序字段 */
    @ApiModelProperty(value = "排序字段, 1-最近下单时间, 2-距离排序(仅支持升序), 3-本月订单金额")
    private Integer sortField;

    /** 排序字段 */
    @ApiModelProperty(value = "排序类型, asc-升序, des-降序")
    private String sort = StringPool.DES;

    @ApiModelProperty(value = "当前业务员纬度坐标, 计算相对距离必须传入")
    private Double lat;

    @ApiModelProperty(value = "当前业务员精度坐标, 计算相对距离必须传入")
    private Double lon;

    @Excel(name = "进入公海时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date seasTime;

    @ApiModelProperty("公海查询 0 查询所有 1查询普通 2查询公海")
    private Integer isOpenSeas = NumberPool.INT_ZERO;

    /** 生命周期阶段 */
    @Excel(name = "生命周期阶段")
    @ApiModelProperty("生命周期阶段")
    private Integer lifecycleStage;

    @ApiModelProperty("是否首单, 新客标识, 首单标识 1-是 0-否")
    private Integer firstOrderFlag;
}
