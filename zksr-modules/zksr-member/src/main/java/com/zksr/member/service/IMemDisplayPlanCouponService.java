package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.domain.MemDisplayPlanCoupon;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanCouponPageReqVO;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanCouponSaveReqVO;

/**
 * 陈列计划优惠明细Service接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
public interface IMemDisplayPlanCouponService {

    /**
     * 新增陈列计划优惠明细
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemDisplayPlanCoupon(@Valid MemDisplayPlanCouponSaveReqVO createReqVO);

    /**
     * 修改陈列计划优惠明细
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemDisplayPlanCoupon(@Valid MemDisplayPlanCouponSaveReqVO updateReqVO);

    /**
     * 删除陈列计划优惠明细
     *
     * @param planCouponId 主键ID
     */
    public void deleteMemDisplayPlanCoupon(Long planCouponId);

    /**
     * 批量删除陈列计划优惠明细
     *
     * @param planCouponIds 需要删除的陈列计划优惠明细主键集合
     * @return 结果
     */
    public void deleteMemDisplayPlanCouponByPlanCouponIds(Long[] planCouponIds);

    /**
     * 获得陈列计划优惠明细
     *
     * @param planCouponId 主键ID
     * @return 陈列计划优惠明细
     */
    public MemDisplayPlanCoupon getMemDisplayPlanCoupon(Long planCouponId);

    /**
     * 获得陈列计划优惠明细分页
     *
     * @param pageReqVO 分页查询
     * @return 陈列计划优惠明细分页
     */
    PageResult<MemDisplayPlanCoupon> getMemDisplayPlanCouponPage(MemDisplayPlanCouponPageReqVO pageReqVO);

}
