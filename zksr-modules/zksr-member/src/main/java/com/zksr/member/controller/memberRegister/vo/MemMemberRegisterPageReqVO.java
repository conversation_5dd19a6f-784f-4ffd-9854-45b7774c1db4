package com.zksr.member.controller.memberRegister.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 用户注册信息对象 mem_member_register
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@ApiModel("用户注册信息 - mem_member_register分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemMemberRegisterPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 用户注册信息ID */
    @ApiModelProperty(value = "备注")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberRegisterId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 用户名 */
    @Excel(name = "用户名")
    @ApiModelProperty(value = "用户名")
    private String memberName;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 区域城市名 */
    @Excel(name = "区域城市名")
    @ApiModelProperty(value = "区域城市名")
    private String areaName;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 用户账号 */
    @Excel(name = "用户账号")
    @ApiModelProperty(value = "用户账号")
    private String userName;

    /** 用户密码 */
    @Excel(name = "用户密码")
    @ApiModelProperty(value = "用户密码")
    private String password;

    /** 门头照 */
    @Excel(name = "门头照")
    @ApiModelProperty(value = "门头照")
    private String branchImages;

    /** 审核人 */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long approveMan;

    /** 审核人名称 */
    @Excel(name = "审核人名称")
    @ApiModelProperty(value = "审核人名称")
    private String approveManName;

    /** 审核标识 */
    @Excel(name = "审核标识")
    @ApiModelProperty(value = "审核标识")
    private Integer approveFlag;

    /** 审核时间 */
    @Excel(name = "审核时间")
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveDate;

    /** 用户自动审核标识 */
    @Excel(name = "用户自动审核标识")
    @ApiModelProperty(value = "用户自动审核标识")
    private Integer memberApproveFlag;

    /** 用户过期时间;过了过期时间，则停用 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "用户过期时间;过了过期时间，则停用", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "用户过期时间;过了过期时间，则停用")
    private Date memberExpirationDate;

    /** 门店自动审核标识 */
    @Excel(name = "门店自动审核标识")
    @ApiModelProperty(value = "门店自动审核标识")
    private Integer branchApproveFlag;

    /** 门店过期时间;过了过期时间，则停用 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "门店过期时间;过了过期时间，则停用", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "门店过期时间;过了过期时间，则停用")
    private Date branchExpirationDate;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 渠道ID */
    @Excel(name = "渠道ID")
    @ApiModelProperty(value = "渠道ID")
    private Long channelId;

    /** 渠道名称 */
    @Excel(name = "渠道名称")
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /**
     * 业务员id
     */
    @Excel(name = "业务员id")
    private Long colonelId;

    /**
     * 业务员名称
     */
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;


}
