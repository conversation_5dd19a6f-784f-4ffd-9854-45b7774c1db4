package com.zksr.member.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.domain.MemMemberRegister;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemBranchUser;
import com.zksr.member.controller.branchUser.vo.MemBranchUserPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import static com.zksr.common.core.constant.StatusConstants.STATUS_1;
import static com.zksr.common.core.constant.StatusConstants.STATUS_2;


/**
 * 门店用户关联关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Mapper
public interface MemBranchUserMapper extends BaseMapperX<MemBranchUser> {
    default PageResult<MemBranchUser> selectPage(MemBranchUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemBranchUser>()
                    .eqIfPresent(MemBranchUser::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemBranchUser::getMemberId, reqVO.getMemberId())
                    .eqIfPresent(MemBranchUser::getBranchId, reqVO.getBranchId())
                .orderByDesc(MemBranchUser::getSysCode));
    }

    /**
     * 根据用户编号查询用户绑定门店信息
     * @param memberId
     * @return
     */
    default List<MemBranchUser> getMemBranchUserByMemberId(Long memberId){
        return selectList(new LambdaQueryWrapper<MemBranchUser>().eq(MemBranchUser::getMemberId, memberId));
    }

    /**
     * 根据用户编号删除用户绑定门店信息
     * @param memberId
     * @return
     */
    default void deleteBranchUserByMemberId(Long memberId){
        delete(new LambdaQueryWrapper<MemBranchUser>().eq(MemBranchUser::getMemberId, memberId));
    }


    /**
     * 根据用户编号查询用户绑定默认门店信息
     * @param memberId
     * @return
     */
    MemBranchUser getDefaultMemBranchUserByMemberId(@Param("memberId") Long memberId);

    default void updateDefaultBranch(Long memberId, Long branchId,Long sysCode){
        //查询当前用户的默认门店 修改为否
        MemBranchUser defaultBranch = getDefaultMemBranchUserByMemberId(memberId);
        if(ToolUtil.isNotEmpty(defaultBranch)) {
            updateBranchUserFlagByMemberId(defaultBranch.getMemberId(),defaultBranch.getBranchId(),STATUS_2);

        }
        //设置该门店为用户的默认门店
        updateBranchUserFlagByMemberId(memberId,branchId,STATUS_1);
    }


    /**
     * 根据用户、门店ID设置默认标识
     * @param memberId
     * @return
     */
    default void updateBranchUserFlagByMemberId(Long memberId,Long branchId,String status){
        //设置该门店为用户的默认门店
        update(null,new LambdaUpdateWrapper<MemBranchUser>()
                .eq(MemBranchUser::getMemberId,memberId)
                .eq(MemBranchUser::getBranchId,branchId)
                .set(MemBranchUser::getIsDefault,status));
    }

     /**
     * 根据门店ID获取memberId
     * @param branchId
     * @return
     */
    default Long getMemberIdByBranchId(Long branchId) {
        MemBranchUser memBranchUser = selectOne(new LambdaQueryWrapper<MemBranchUser>()
                .eq(MemBranchUser::getBranchId, branchId)
                .select(MemBranchUser::getMemberId));

        // 检查 memBranchUser 是否为 null
        if (memBranchUser == null) {
            return null;
        }

        return memBranchUser.getMemberId();
    }


    /**
     * 根据 branchId 统计门店用户数量（使用内置查询条件）
     */
    default Long countByBranchId(Long branchId) {
        return selectCount(new LambdaQueryWrapper<MemBranchUser>()
                .eq(MemBranchUser::getBranchId, branchId));
    }

}
