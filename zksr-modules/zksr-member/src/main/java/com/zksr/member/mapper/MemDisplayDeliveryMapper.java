package com.zksr.member.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemDisplayDelivery;
import com.zksr.member.controller.displayDelivery.vo.MemDisplayDeliveryPageReqVO;


/**
 * 陈列计划兑付Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Mapper
public interface MemDisplayDeliveryMapper extends BaseMapperX<MemDisplayDelivery> {
    default PageResult<MemDisplayDelivery> selectPage(MemDisplayDeliveryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemDisplayDelivery>()
                    .eqIfPresent(MemDisplayDelivery::getDeliveryId, reqVO.getDeliveryId())
                    .eqIfPresent(MemDisplayDelivery::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemDisplayDelivery::getPlanId, reqVO.getPlanId())
                    .eqIfPresent(MemDisplayDelivery::getFinishSum, reqVO.getFinishSum())
                    .eqIfPresent(MemDisplayDelivery::getPic, reqVO.getPic())
                    .eqIfPresent(MemDisplayDelivery::getCouponId, reqVO.getCouponId())
                    .eqIfPresent(MemDisplayDelivery::getMemo, reqVO.getMemo())
                    .eqIfPresent(MemDisplayDelivery::getDelFlag, reqVO.getDelFlag())
                .orderByDesc(MemDisplayDelivery::getDeliveryId));
    }
}
