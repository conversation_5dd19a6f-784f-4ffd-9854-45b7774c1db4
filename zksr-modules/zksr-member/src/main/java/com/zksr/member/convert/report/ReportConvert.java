package com.zksr.member.convert.report;

import com.zksr.member.controller.colonelApp.vo.ColonelBranchAnalyseInfoRespVO;
import com.zksr.report.api.branch.vo.BranchAnalyseInfoRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/12/10
 * @desc
 */
@Mapper
public interface ReportConvert {

    ReportConvert INSTANCE = Mappers.getMapper(ReportConvert.class);

    ColonelBranchAnalyseInfoRespVO convertColonelBranchAnalyse(BranchAnalyseInfoRespVO respVO);
}
