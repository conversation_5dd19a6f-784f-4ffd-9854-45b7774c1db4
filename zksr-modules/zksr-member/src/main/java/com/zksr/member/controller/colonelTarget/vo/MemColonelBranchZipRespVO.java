package com.zksr.member.controller.colonelTarget.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 门店业务员关系拉链表 mem_colonel_branch_zip
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Data
@ApiModel("门店业务员关系拉链表 - mem_colonel_branch_zip Response VO")
public class MemColonelBranchZipRespVO extends BaseEntity{
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long colonelBranchZipId;

    @Excel(name = "平台商ID")
    private Long sysCode;

    @Excel(name = "门店ID")
    private Long branchId;

    @Excel(name = "业务员ID")
    private Long colonelId;

    @Excel(name = "业务员名称")
    private String colonelName;

    @Excel(name = "开始日期")
    private Date startDate;

    @Excel(name = "结束日期")
    private Date endDate;
}
