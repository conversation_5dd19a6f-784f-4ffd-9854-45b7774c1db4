package com.zksr.member.service.impl;

import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.member.domain.MemColonel;
import com.zksr.member.domain.MemColonelHierarchyZip;
import com.zksr.member.mapper.MemColonelHierarchyZipMapper;
import com.zksr.member.service.IMemColonelHierarchyZipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.Objects;

/**
 * 业务员上下级关系拉链表 Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Service
public class MemColonelHierarchyZipServiceImpl implements IMemColonelHierarchyZipService {
    @Autowired
    private MemColonelHierarchyZipMapper memColonelHierarchyZipMapper;
    @Override
    @Transactional
    public void insertMemColonelHierarchyZip(MemColonelSaveReqVO reqVO, MemColonel pmemColonel) {
        //获取时间
        Date nowDate = DateUtils.getNowDate();
        Date endOfYearDate = DateUtils.toDate(LocalDateTime.of(2099, 12, 30, 23, 59, 59));
        MemColonelHierarchyZip currentRelation = null;
        if(ToolUtil.isNotEmpty(pmemColonel)){
            currentRelation = memColonelHierarchyZipMapper.selectMemColonelHierarchyZipBycolonelId(pmemColonel);
        }
        if(ToolUtil.isEmpty(currentRelation)){
            MemColonelHierarchyZip newRelation = new MemColonelHierarchyZip();
            newRelation.setSysCode(reqVO.getSysCode());
            newRelation.setPcolonelId(reqVO.getPcolonelId());
            newRelation.setColonelId(reqVO.getColonelId());
            newRelation.setStartDate(nowDate);
            newRelation.setEndDate(endOfYearDate);
            memColonelHierarchyZipMapper.insert(newRelation);
        }else{
            if(Objects.equals(currentRelation.getSysCode(), reqVO.getSysCode())
                && Objects.equals(currentRelation.getColonelId(), reqVO.getColonelId())
                && !Objects.equals(currentRelation.getPcolonelId(), reqVO.getPcolonelId())){

                currentRelation.setEndDate(nowDate);
                memColonelHierarchyZipMapper.updateMemColonelHierarchyZip(currentRelation);

                MemColonelHierarchyZip newMemColonelHierarchyZip = HutoolBeanUtils.toBean(currentRelation, MemColonelHierarchyZip.class);
                newMemColonelHierarchyZip.setColonelHierarchyZipId(null);
                newMemColonelHierarchyZip.setStartDate(nowDate);
                newMemColonelHierarchyZip.setCreateTime(nowDate);
                newMemColonelHierarchyZip.setEndDate(endOfYearDate);
                newMemColonelHierarchyZip.setPcolonelId(reqVO.getPcolonelId());
                memColonelHierarchyZipMapper.insert(newMemColonelHierarchyZip);
            }
        }
    }
}
