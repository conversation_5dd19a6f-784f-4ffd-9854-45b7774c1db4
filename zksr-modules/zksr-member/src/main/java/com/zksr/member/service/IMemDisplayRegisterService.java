package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.domain.MemDisplayRegister;
import com.zksr.member.controller.displayRegister.vo.MemDisplayRegisterPageReqVO;
import com.zksr.member.controller.displayRegister.vo.MemDisplayRegisterSaveReqVO;

/**
 * 陈列计划打卡记录Service接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
public interface IMemDisplayRegisterService {

    /**
     * 新增陈列计划打卡记录
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemDisplayRegister(@Valid MemDisplayRegisterSaveReqVO createReqVO);

    /**
     * 修改陈列计划打卡记录
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemDisplayRegister(@Valid MemDisplayRegisterSaveReqVO updateReqVO);

    /**
     * 删除陈列计划打卡记录
     *
     * @param registerId 主键ID
     */
    public void deleteMemDisplayRegister(Long registerId);

    /**
     * 批量删除陈列计划打卡记录
     *
     * @param registerIds 需要删除的陈列计划打卡记录主键集合
     * @return 结果
     */
    public void deleteMemDisplayRegisterByRegisterIds(Long[] registerIds);

    /**
     * 获得陈列计划打卡记录
     *
     * @param registerId 主键ID
     * @return 陈列计划打卡记录
     */
    public MemDisplayRegister getMemDisplayRegister(Long registerId);

    /**
     * 获得陈列计划打卡记录分页
     *
     * @param pageReqVO 分页查询
     * @return 陈列计划打卡记录分页
     */
    PageResult<MemDisplayRegister> getMemDisplayRegisterPage(MemDisplayRegisterPageReqVO pageReqVO);

}
