package com.zksr.member.convert.branch;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsColonelAppBranch;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;

import com.zksr.member.api.branch.excel.MemBranchImportExcel;
import com.zksr.member.controller.branch.vo.MemBranchAccountRespVO;
import com.zksr.member.controller.colonelApp.vo.MemColonelAppCustomerListPageReqVO;
import com.zksr.member.controller.colonelApp.vo.MemColonelAppCustomerListRespVO;
import com.zksr.member.domain.MemBranch;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/4/12
 * @desc
 */
@Mapper
public interface BranchConvert {

    BranchConvert INSTANCE = Mappers.getMapper(BranchConvert.class);

    PageResult<MemBranchAccountRespVO> conver(PageResult<MemBranch> pageResult);

    @Mappings({
            @Mapping(source = "itemData.branchName", target = "branchName"),
            @Mapping(source = "itemData.contactName", target = "contactName"),
            @Mapping(source = "itemData.branchAddr", target = "branchAddr"),
            @Mapping(source = "itemData.contactPhone", target = "contactPhone"),
            @Mapping(source = "itemData.memo", target = "memo"),
            @Mapping(source = "itemData.branchNo", target = "branchNo"),
    })
    @BeanMapping(ignoreByDefault = true)
    void convertImport(@MappingTarget MemBranch prdtBranch, MemBranchImportExcel itemData);

    ColonelAppBranchDTO convertEsBranchSearchDTO(MemColonelAppCustomerListPageReqVO pageReqVO);

    @Mappings({
            @Mapping(source = "branchDTO.branchId", target = "branchId"),
            @Mapping(source = "branchDTO.branchName", target = "branchName"),
            @Mapping(source = "branchDTO.colonelId", target = "colonelId"),
            @Mapping(source = "branchDTO.branchAddr", target = "branchAddr"),
            @Mapping(source = "branchDTO.longitude", target = "longitude"),
            @Mapping(source = "branchDTO.latitude", target = "latitude"),
            @Mapping(source = "branchDTO.contactName", target = "contactName"),
            @Mapping(source = "branchDTO.contactPhone", target = "contactPhone"),
            @Mapping(source = "branchDTO.branchImages", target = "branchImages"),
            @Mapping(source = "esBranch.nowMonthOrderQty", target = "nowMonthOrderQty"),
            @Mapping(source = "esBranch.nowMonthOrderAmt", target = "nowMonthOrderAmt"),
            @Mapping(source = "esBranch.lastMonthOrderQty", target = "lastMonthOrderQty"),
            @Mapping(source = "esBranch.lastMonthOrderAmt", target = "lastMonthOrderAmt"),
            @Mapping(source = "esBranch.lastBuyTime", target = "lastBuyTime"),
            @Mapping(source = "esBranch.lastBuyAmt", target = "lastBuyAmt"),
            @Mapping(source = "esBranch.lastAccessSystemTime", target = "lastAccessSystemTime"),
            @Mapping(source = "esBranch.lastLoginTime", target = "lastLoginTime"),
            @Mapping(source = "esBranch.lastVisitTime", target = "lastVisitTime"),
            @Mapping(source = "branchDTO.lifecycleStage", target = "lifecycleStage"),
            @Mapping(source = "branchDTO.firstOrderFlag", target = "firstOrderFlag"),
    })
    @BeanMapping(ignoreByDefault = true)
    MemColonelAppCustomerListRespVO convertAppCustomerRespVO(BranchDTO branchDTO, EsColonelAppBranch esBranch);

    List<BranchDTO> convertDTOList(List<MemBranch> memBranches);

    MemBranch convertPO(MemBranchSaveReqVO updateReqVO);

    MemBranchSaveReqVO convertUpdateVO(MemBranch branch);
}
