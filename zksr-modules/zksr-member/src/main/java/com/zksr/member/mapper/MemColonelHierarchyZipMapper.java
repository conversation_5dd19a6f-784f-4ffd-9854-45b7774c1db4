package com.zksr.member.mapper;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.member.domain.MemColonel;
import com.zksr.member.domain.MemColonelHierarchyZip;
import org.apache.ibatis.annotations.Mapper;

/**
 * 业务员上下级关系拉链表 Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@SuppressWarnings("all")
@Mapper
public interface MemColonelHierarchyZipMapper extends BaseMapperX<MemColonelHierarchyZip> {
    default MemColonelHierarchyZip selectMemColonelHierarchyZipBycolonelId(MemColonel pmemColonel){
        LambdaQueryWrapper<MemColonelHierarchyZip> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemColonelHierarchyZip::getSysCode, pmemColonel.getSysCode())
                    .eq(MemColonelHierarchyZip::getColonelId, pmemColonel.getColonelId())
                    .orderByDesc(MemColonelHierarchyZip::getStartDate)
                    .last("LIMIT 1");
        return selectOne(queryWrapper);
    }

    void updateMemColonelHierarchyZip(MemColonelHierarchyZip currentRelation);
}
