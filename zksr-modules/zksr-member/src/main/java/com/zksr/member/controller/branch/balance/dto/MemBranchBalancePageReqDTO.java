package com.zksr.member.controller.branch.balance.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 门店-余额管理分页查询参数
 */
@ApiModel("门店-余额管理分页查询参数Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class MemBranchBalancePageReqDTO extends PageParam {

    private static final long serialVersionUID = -755535269748907777L;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("门店ID")
    private Long branchId;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    @ApiModelProperty("门店编号")
    private String branchNo;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店用户")
    private String contactPhone;

    @ApiModelProperty(value = "状态：1正常,0停用")
    private Integer status;

    @ApiModelProperty(value = "交易单号")
    private String tradeNo;

    @ApiModelProperty(value = "操作金额")
    private BigDecimal opAmt;

    @ApiModelProperty(value = "源交易单号")
    private String sourceTradeNo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "跟据金额排序：ASC正序,DESC倒序")
    private String orderBy;

}
