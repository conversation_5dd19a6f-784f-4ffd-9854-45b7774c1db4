package com.zksr.member.service.impl;

import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.file.api.file.FileApi;
import com.zksr.file.api.file.vo.ImageInfoReqVO;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyRespVO;
import com.zksr.member.controller.branch.vo.MemBranchPageReqVO;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogRespVO;
import com.zksr.member.mapper.MemBranchMapper;
import com.zksr.member.service.IMemberCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemColonelTidyMapper;
import com.zksr.member.convert.ColonelTidy.MemColonelTidyConvert;
import com.zksr.member.domain.MemColonelTidy;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyPageReqVO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidySaveReqVO;
import com.zksr.member.service.IMemColonelTidyService;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 业务员理货记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Service
public class MemColonelTidyServiceImpl implements IMemColonelTidyService {
    @Autowired
    private MemColonelTidyMapper memColonelTidyMapper;

    @Autowired
    private MemBranchMapper memBranchMapper;

    @Autowired
    private IMemberCacheService memberCacheService;

    @Resource
    private FileApi fileApi;

    /**
     * 新增业务员理货记录
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemColonelTidy(MemColonelTidySaveReqVO createReqVO) {
        // 插入
        MemColonelTidy memColonelTidy = MemColonelTidyConvert.INSTANCE.convert(createReqVO);
        memColonelTidyMapper.insert(memColonelTidy);



        // 返回
        return memColonelTidy.getColonelTidyId();
    }

    /**
     * 修改业务员理货记录
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemColonelTidy(MemColonelTidySaveReqVO updateReqVO) {
        memColonelTidyMapper.updateById(MemColonelTidyConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除业务员理货记录
     *
     * @param colonelTidyId 业务员理货记录id
     */
    @Override
    public void deleteMemColonelTidy(Long colonelTidyId) {
        // 删除
        memColonelTidyMapper.deleteById(colonelTidyId);
    }

    /**
     * 批量删除业务员理货记录
     *
     * @param colonelTidyIds 需要删除的业务员理货记录主键
     * @return 结果
     */
    @Override
    public void deleteMemColonelTidyByColonelTidyIds(Long[] colonelTidyIds) {
        for(Long colonelTidyId : colonelTidyIds){
            this.deleteMemColonelTidy(colonelTidyId);
        }
    }

    /**
     * 获得业务员理货记录
     *
     * @param colonelTidyId 业务员理货记录id
     * @return 业务员理货记录
     */
    @Override
    public MemColonelTidy getMemColonelTidy(Long colonelTidyId) {
        return memColonelTidyMapper.selectById(colonelTidyId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemColonelTidyRespVO> getMemColonelTidyPage(MemColonelTidyPageReqVO pageReqVO) {
        List<Long> branchIdList = null;
        if(ToolUtil.isNotEmpty(pageReqVO.getKeywords()) || ToolUtil.isNotEmpty(pageReqVO.getAreaId())){
            MemBranchPageReqVO req = new MemBranchPageReqVO();
            req.setKeywords(pageReqVO.getKeywords());
            req.setAreaId(pageReqVO.getAreaId());
            // 根据关键字查询门店ID集合
            branchIdList = memBranchMapper.selectMemBranchListByParam(req);
            if(ToolUtil.isEmpty(branchIdList)){
                return new PageResult<MemColonelTidyRespVO>();
            }
        }
        pageReqVO.setBranchIdList(branchIdList);
        PageResult<MemColonelTidy> pageResult =memColonelTidyMapper.selectPage(pageReqVO);
        // 将查询结果转换为响应对象
        PageResult<MemColonelTidyRespVO> resp = HutoolBeanUtils.toBean(pageResult, MemColonelTidyRespVO.class);

        resp.getList().forEach(item -> {
            BranchDTO branchDTO =memberCacheService.getBranchDto(Long.parseLong(item.getBranchId()));
            item.setBranchName(branchDTO.getBranchName());
            ColonelDTO colonelDTO =memberCacheService.getColonel(Long.parseLong(item.getColonelId()));
            item.setDeptId(colonelDTO.getDeptId());
            item.setColonelName(colonelDTO.getColonelName());
        });
        return resp;
    }

    @Override
    public byte[] getColonelTidyImageZip(MemColonelTidyPageReqVO pageReqVO) throws IOException {
        List<Long> branchIdList = null;
        if(ToolUtil.isNotEmpty(pageReqVO.getKeywords()) || ToolUtil.isNotEmpty(pageReqVO.getAreaId())){
            MemBranchPageReqVO req = new MemBranchPageReqVO();
            req.setKeywords(pageReqVO.getKeywords());
            req.setAreaId(pageReqVO.getAreaId());
            // 根据关键字查询门店ID集合
            branchIdList = memBranchMapper.selectMemBranchListByParam(req);
        }
        pageReqVO.setBranchIdList(branchIdList);
        PageResult<MemColonelTidy> pageResult =memColonelTidyMapper.selectPage(pageReqVO);
        List<ImageInfoReqVO> imageInfoReqVOList = new ArrayList<>();
        pageResult.getList().forEach(item -> {
            BranchDTO branchDTO =memberCacheService.getBranchDto(item.getBranchId());
            if(ToolUtil.isNotEmpty(item.getTidyImg())) {
                if (item.getTidyImg().contains(",")) {
                    List<String> ImgeUrls = Arrays.asList(item.getTidyImg().split(","));
                    ImgeUrls.forEach(url -> {
                        ImageInfoReqVO imageInfoReqVO = new ImageInfoReqVO();
                        imageInfoReqVO.setUrl(url);
                        imageInfoReqVO.setDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, item.getCreateTime()));
                        imageInfoReqVO.setName(branchDTO.getBranchName() + "-" + DateUtils.parseDateToStr("HH-mm-ss", item.getCreateTime()) + ".jpeg");
                        imageInfoReqVOList.add(imageInfoReqVO);
                    });
                } else {
                    ImageInfoReqVO imageInfoReqVO = new ImageInfoReqVO();
                    imageInfoReqVO.setUrl(item.getTidyImg());
                    imageInfoReqVO.setDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, item.getCreateTime()));
                    imageInfoReqVO.setName(branchDTO.getBranchName() + "-" + DateUtils.parseDateToStr("HH-mm-ss", item.getCreateTime()) + ".jpeg");
                    imageInfoReqVOList.add(imageInfoReqVO);
                }
                ;
            }
        });
        return fileApi.downloadImages(imageInfoReqVOList);
    }

    private void validateMemColonelTidyExists(Long colonelTidyId) {
        if (memColonelTidyMapper.selectById(colonelTidyId) == null) {
            throw exception(MEM_COLONEL_TIDY_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 业务员理货记录 TODO 补充编号 ==========
    // ErrorCode MEM_COLONEL_TIDY_NOT_EXISTS = new ErrorCode(TODO 补充编号, "业务员理货记录不存在");


}
