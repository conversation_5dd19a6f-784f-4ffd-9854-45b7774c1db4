package com.zksr.member.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import com.zksr.member.controller.colonel.excel.MemColonelImportExcel;
import com.zksr.member.domain.MemColonel;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 业务员 业务结算 信息Service接口
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
public interface IMemColonelSettleService {

    /**
     * 业务员 业务日结计算
     * @param sysCode
     */
    public void colonelDaySettlementJob(Long sysCode, Date date);

    /**
     * 业务员 业务月结计算
     * @param sysCode
     */
    public void colonelMonthSettlementJob(Long sysCode, Date date, Long colonelId);

    /**
     * 业务员 业务结算（用于调整错误数据）
     * @param sysCode
     */
    public void colonelSettlementJob(Long sysCode, String startDate, String endDate);
}
