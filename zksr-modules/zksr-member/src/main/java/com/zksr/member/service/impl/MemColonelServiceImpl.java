package com.zksr.member.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.github.pagehelper.Page;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.SysYesNoEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.JwtUtils;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.member.api.colonel.excel.MemColonelImportExcel;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import com.zksr.member.api.colonel.vo.WxColonelPublishOpenidBindReqVO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyPageReqVO;
import com.zksr.member.controller.relation.vo.MemColonelRelationPageReqVO;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogPageReqVO;
import com.zksr.member.convert.colonel.MemberColonelConvert;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.domain.MemColonel;
import com.zksr.member.domain.MemColonelRelation;
import com.zksr.member.domain.MemColonelTidy;
import com.zksr.member.mapper.*;
import com.zksr.member.service.IMemColonelHierarchyZipService;
import com.zksr.member.service.IMemColonelService;
import com.zksr.member.service.IMemberCacheService;
import com.zksr.system.api.RemoteUserService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.ColonelUserDTO;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.TrdColonelAppOrderListPageReqVO;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.STATUS_FAIL;
import static com.zksr.common.core.constant.StatusConstants.STATUS_SUCCESS;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 业务员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class MemColonelServiceImpl implements IMemColonelService {

    @Autowired
    private MemColonelMapper memColonelMapper;

    @Autowired
    private MemColonelRelationMapper memColonelRelationMapper;

    @Resource
    private PlatformMerchantApi platformMerchantApi;

    @Resource
    private DcApi remotedcApi;

    @Autowired
    private Cache<Long, ColonelDTO> colonelDTOCache;

    @Resource
    private OrderApi orderApi;

    @Resource
    private AreaCityApi areaCityApi;

    @Autowired
    private MemColonelVisitLogMapper memColonelVisitLogMapper;

    @Autowired
    private MemColonelTidyMapper memColonelTidyMapper;

    @Resource
    private AreaApi areaApi;

    @Resource
    private RemoteUserService userService;

    @Autowired
    private IMemColonelHierarchyZipService memColonelHierarchyZipService;

    @Autowired
    private RedisService redisService;
    @Autowired
    private MemBranchMapper memBranchMapper;

    @Autowired
    private IMemberCacheService memberCacheService;

    /**
     * 新增业务员信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @DSTransactional
    @Override
    public Long insertMemColonel(MemColonelSaveReqVO createReqVO) {
        // 插入
        MemColonel memColonel = MemberColonelConvert.INSTANCE.convert(createReqVO);
        if(ObjectUtil.isEmpty(createReqVO.getSource())){
            memColonel.setSource("PC");
            memColonel.setAuditState(StatusConstants.AUDIT_STATE_1);
        }
        if(StatusConstants.STATUS_2.equals(memColonel.getIsColonelAdmin())){
            if(ObjectUtil.isEmpty(memColonel.getPcolonelId())){
                throw  new ServiceException("管理员为否，上级业务员必填!");
            }
        }
        memColonelMapper.insert(memColonel);
        if (StringUtils.isNotNull(memColonel.getPcolonelId())) { //当上级业务员不为空时，添加业务员关系数据
            MemColonelRelation relation = new MemColonelRelation();
            relation.setColonelId(memColonel.getColonelId());
            relation.setAdminColonelId(memColonel.getPcolonelId());
            memColonelRelationMapper.insert(relation);
        }
        // 保存支付平台商户信息
        savePayPlatformMerchant(createReqVO, memColonel.getColonelId());

        //添加业务员登陆用户
        createReqVO.setColonelId(memColonel.getColonelId());
        Long userId = remotedcApi.insertColonelUser(HutoolBeanUtils.toBean(createReqVO, ColonelUserDTO.class)).getCheckedData();
         //用户信息绑定业务员
         memColonel.setUserId(userId);
         memColonelMapper.updateById(memColonel);
        return memColonel.getColonelId();
    }

    /**
     * 修改业务员信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @DSTransactional
    @Override
    public void updateMemColonel(MemColonelSaveReqVO updateReqVO) {
        if(StatusConstants.STATUS_2.equals(updateReqVO.getIsColonelAdmin())){
            if(ObjectUtil.isEmpty(updateReqVO.getPcolonelId())){
                throw  new ServiceException("管理员为否，上级业务员必填!");
            }
        }
        //上级必须为管理员
        if(ObjectUtil.isNotEmpty(updateReqVO.getPcolonelId())){
            MemColonel memColonel = memColonelMapper.selectById(updateReqVO.getPcolonelId());
            if(StatusConstants.STATUS_2.equals(memColonel.getIsColonelAdmin())){
                throw  new ServiceException("上级业务员必须为业务管理员!");
            }
        }
        MemColonel pmemColonel = memColonelMapper.selectById(updateReqVO.getColonelId());
        // 如果业务员有管理门店则不能修改区域
        if (!Objects.equals(pmemColonel.getAreaId(), updateReqVO.getAreaId())){
            List<MemBranch> listBranchByColonelId = memBranchMapper.getListBranchByColoelId(updateReqVO.getColonelId());
            if (!listBranchByColonelId.isEmpty()){
                throw  new ServiceException("业务员有管理门店，不能修改区域!");
            }
        }

        if (StringUtils.isNotNull(updateReqVO.getPcolonelId()) && !Objects.equals(pmemColonel.getPcolonelId(), updateReqVO.getPcolonelId())) { //当上级业务员不为空、且更换过上级业务员 时 更新业务员关系数据
            // 删除业务员关系数据
            memColonelRelationMapper.deleteByColonelId(updateReqVO.getColonelId());

//            MemColonelRelationPageReqVO relationReqVO = new MemColonelRelationPageReqVO();
////            relationReqVO.setAdminColonelId(updateReqVO.getPcolonelId());
//            relationReqVO.setColonelId(updateReqVO.getColonelId());
//            //
//            List<MemColonelRelation> relationList  = memColonelRelationMapper.getColonelRelation(relationReqVO);
//            if (StringUtils.isEmpty(relationList)){  //当前绑定的业务员未绑定时进入
//
//            }

            // 新增业务员关系
            MemColonelRelation relation = new MemColonelRelation();
            relation.setColonelId(updateReqVO.getColonelId());
            relation.setAdminColonelId(updateReqVO.getPcolonelId());
            relation.setSysCode(updateReqVO.getSysCode());
            memColonelRelationMapper.insert(relation);

            memColonelHierarchyZipService.insertMemColonelHierarchyZip(updateReqVO, pmemColonel);
        }

        // 密码如果不在指定范围内 错误
        String password = updateReqVO.getPassword();
        if(ToolUtil.isNotEmpty(password) && (password.length() < UserConstants.PASSWORD_MIN_LENGTH || password.length() > UserConstants.PASSWORD_MAX_LENGTH)){
            throw exception(MEM_MEMBER_PASSWORD_NOT_LENGTH);
        }
        //更新业务员用户账号信息
        remotedcApi.updateColonelUser(HutoolBeanUtils.toBean(updateReqVO,ColonelUserDTO.class));
//        MemColonel colonel = memColonelMapper.selectById(updateReqVO.getColonelId());
//        if (colonel.getStatus() != updateReqVO.getStatus()) {
//            String newStatus = updateReqVO.getStatus() == 0 ? "1" : "0";
//            remotedcApi.updateColonelUserStatus(updateReqVO.getUserId(), newStatus);
//        }

        memColonelMapper.updateById(MemberColonelConvert.INSTANCE.convert(updateReqVO));
        // 保存支付平台商户信息
        savePayPlatformMerchant(updateReqVO, updateReqVO.getColonelId());

        //清除业务员缓存
        removeColonelCache(updateReqVO.getColonelId());
    }

    /**
     * 业务员审核
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @DSTransactional
    @Override
    public void salesmanAudit(MemColonelSaveReqVO updateReqVO) {
        if(StatusConstants.STATUS_2.equals(updateReqVO.getIsColonelAdmin())){
            if(ObjectUtil.isEmpty(updateReqVO.getPcolonelId())){
                throw  new ServiceException("管理员为否，上级业务员必填!");
            }
        }
        if(ObjectUtil.isNotEmpty(updateReqVO.getPcolonelId())){
             MemColonel memColonel = memColonelMapper.selectById(updateReqVO.getPcolonelId());
            if(StatusConstants.STATUS_2.equals(memColonel.getIsColonelAdmin())){
                throw  new ServiceException("上级业务员必须为业务管理员!");
            }
        }
        //判断是否审核通过 还是不通过
        if(updateReqVO.getAuditState() == 1){
            if (StringUtils.isNotNull(updateReqVO.getPcolonelId())) { //当上级业务员不为空时，更新业务员关系数据
                MemColonelRelationPageReqVO relationReqVO = new MemColonelRelationPageReqVO();
                relationReqVO.setAdminColonelId(updateReqVO.getPcolonelId());
                relationReqVO.setColonelId(updateReqVO.getColonelId());
                List<MemColonelRelation> relationList  = memColonelRelationMapper.getColonelRelation(relationReqVO);
                if (StringUtils.isEmpty(relationList)){  //当前绑定的业务员未绑定时进入
                    MemColonelRelation relation = new MemColonelRelation();
                    relation.setColonelId(updateReqVO.getColonelId());
                    relation.setAdminColonelId(updateReqVO.getPcolonelId());
                    relation.setSysCode(updateReqVO.getSysCode());
                    memColonelRelationMapper.insert(relation);
                }
                memColonelHierarchyZipService.insertMemColonelHierarchyZip(updateReqVO, null);
            }
            updateReqVO.setAuditBy(SecurityUtils.getLoginUser().getUsername());
            updateReqVO.setAuditTime(new Date());
            updateReqVO.setStatus(StatusConstants.STATE_ENABLE);
            updateReqVO.setEntryDate(new Date());
            remotedcApi.updateColonelUserStatus(updateReqVO.getUserId(), StatusConstants.STATE_DISABLE.toString());
            memColonelMapper.updateById(MemberColonelConvert.INSTANCE.convert(updateReqVO));
            // 保存支付平台商户信息
            savePayPlatformMerchant(updateReqVO, updateReqVO.getColonelId());
            //清除业务员缓存
            removeColonelCache(updateReqVO.getColonelId());
        }else if(updateReqVO.getAuditState() == 2){
            updateReqVO.setAuditBy(SecurityUtils.getLoginUser().getUsername());
            updateReqVO.setAuditTime(new Date());
            updateReqVO.setStatus(StatusConstants.STATE_DISABLE);
            updateReqVO.setEntryDate(new Date());
            remotedcApi.updateColonelUserStatus(updateReqVO.getUserId(), StatusConstants.STATE_ENABLE.toString());
            memColonelMapper.updateById(MemberColonelConvert.INSTANCE.convert(updateReqVO));
            //清除业务员缓存
            removeColonelCache(updateReqVO.getColonelId());
        }
    }

    /**
     * 删除业务员信息
     *
     * @param colonelId 业务员id
     */
    @Override
    @Transactional
    public void deleteMemColonel(Long colonelId) {
        MemColonel memColonel = memColonelMapper.selectById(colonelId);
        memColonel.setDelFlag(Integer.parseInt(DelFlagConstants.DISABLE));
        // 逻辑删除
        memColonelMapper.updateById(memColonel);
        // 逻辑删除用户
        userService.deleteByUserId(memColonel.getUserId()).checkError();
        //清除业务员缓存
        removeColonelCache(colonelId);
    }

    /**
     * 批量删除业务员信息
     *
     * @param colonelIds 需要删除的业务员信息主键
     * @return 结果
     */
    @Override
    public void deleteMemColonelByColonelIds(Long[] colonelIds) {
        for(Long colonelId : colonelIds){
            this.deleteMemColonel(colonelId);
        }
    }

    /**
     * 获得业务员信息
     *
     * @param colonelId 业务员id
     * @return 业务员信息
     */
    @Override
    public MemColonel getMemColonel(Long colonelId) {
        return memColonelMapper.selectById(colonelId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemColonel> getMemColonelPage(MemColonelPageReqVO pageReqVO) {
        Page<MemColonel> page = PageUtils.startPage(pageReqVO);
//        return memColonelMapper.selectPage(pageReqVO);
        return new PageResult<>(memColonelMapper.selectListPage(pageReqVO), page.getTotal());
    }

    @Override
    public PageResult<MemColonelRespVO> getColonelNotRelationPage(MemColonelPageReqVO pageReqVO) {
        Page<MemColonelRespVO> page = PageUtils.startPage(pageReqVO);
        return new PageResult<>(memColonelMapper.getColonelNotRelationPage(pageReqVO), page.getTotal());
    }

    @Override
    public void disable(Long colonelId) {
       MemColonel colonel = memColonelMapper.selectById(colonelId);
       colonel.setStatus(StatusConstants.STATE_DISABLE);
       memColonelMapper.updateById(colonel);
        // 更新 sys_user 表中的状态
        remotedcApi.updateColonelUserStatus(colonel.getUserId(), "1");
        //清除业务员缓存
        removeColonelCache(colonelId);
    }

    @Override
    public void enable(Long colonelId) {
        MemColonel colonel = memColonelMapper.selectById(colonelId);
        colonel.setStatus(StatusConstants.STATE_ENABLE);
        memColonelMapper.updateById(colonel);
        // 更新 sys_user 表中的状态
        remotedcApi.updateColonelUserStatus(colonel.getUserId(), "0");
        //清除业务员缓存
        removeColonelCache(colonelId);
    }

    @Override
    public PageResult<MemColonelRespVO> MemColonelSalesAmtList(MemColonelPageReqVO pageReqVO) {
        Page<MemColonel> page = PageUtils.startPage(pageReqVO);
        PageResult<MemColonel> pageResult = new PageResult<>(memColonelMapper.selectListPage(pageReqVO), page.getTotal());
        PageResult<MemColonelRespVO> respVO = HutoolBeanUtils.toBean(pageResult, MemColonelRespVO.class);
        respVO.getList().forEach(item -> {
            TrdColonelAppOrderListPageReqVO reqVO = new TrdColonelAppOrderListPageReqVO();
            Long visitNum = memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId()+"",pageReqVO.getStartTime(),pageReqVO.getEndTime(),null);
            reqVO.setColonelId(item.getColonelId());
            reqVO.setStartTime(pageReqVO.getStartTime());
            reqVO.setEndTime(pageReqVO.getEndTime());
            BigDecimal saleAmount =orderApi.getSaleAmount(reqVO);
            item.setSalesAmt(saleAmount);
            item.setVisitNum(visitNum);
            if(ToolUtil.isEmpty(item.getSalesAmt())){
                item.setSalesAmt(BigDecimal.ZERO);
            }
        });
        return respVO;
    }

    @Override
    public PageResult<MemColonelRespVO> getMemColonelNumPage(MemColonelPageReqVO pageReqVO) {
        PageResult<MemColonel> page =memColonelMapper.selectPage(pageReqVO);
        PageResult<MemColonelRespVO> respVO = HutoolBeanUtils.toBean(page, MemColonelRespVO.class);
        respVO.getList().forEach(item -> {

            MemColonelVisitLogPageReqVO reqVO = new MemColonelVisitLogPageReqVO();
            reqVO.setVisitFlag(1);
            reqVO.setStartTime(pageReqVO.getStartTime());
            reqVO.setEndTime(pageReqVO.getEndTime());
            reqVO.setColonelId(item.getColonelId());
            Long visitNum = memColonelVisitLogMapper.selectPage(reqVO).getTotal();


            MemColonelTidyPageReqVO tidyReqVO = new MemColonelTidyPageReqVO();
            tidyReqVO.setBeginCreateTime(pageReqVO.getStartTime());
            tidyReqVO.setEndCreateTime(pageReqVO.getEndTime());
            tidyReqVO.setColonelId(item.getColonelId());
            tidyReqVO.setPageNo(1);
            tidyReqVO.setPageSize(100);
            PageResult<MemColonelTidy> tidyPage =memColonelTidyMapper.selectPage(tidyReqVO);
            item.setColonelTidyNum(tidyPage.getTotal().longValue());
            item.setVisitNum(visitNum);
        });
        return respVO;
    }

    public String impordData(List<MemColonelImportExcel> colonelList){
        return impordDataEvent(colonelList,SecurityUtils.getLoginUser().getFuncScop(),SecurityUtils.getLoginUser().getSysCode(),null,0).getMsg();
    }

    @Override
    public FileImportHandlerVo impordDataEvent(List<MemColonelImportExcel> colonelList, String funcScop, Long sysCode, Long fileImportId, Integer seq) {
        FileImportHandlerVo fileImportHandlerVo = new FileImportHandlerVo();
        List<SysFileImportDtl> sysFileImportDtls = new ArrayList<>();
        int successNum = colonelList.size();
        int failureNum = 0;
        int totalNum = colonelList.size();
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        fileImportHandlerVo.setSuccessNum(successNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setTotalNum(totalNum);

        if (colonelList.isEmpty()) {
            // 如果导入数据为空，则不进行数据导入
            fileImportHandlerVo.setFailureNum(colonelList.size());
            fileImportHandlerVo.setTotalNum(colonelList.size());
            fileImportHandlerVo.setMsg("导入数据为空");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;

        }
//        String funcScop = SecurityUtils.getLoginUser().getFuncScop();
        if (Objects.isNull(funcScop) || !funcScop.equalsIgnoreCase("DC")) {
            fileImportHandlerVo.setFailureNum(colonelList.size());
            fileImportHandlerVo.setTotalNum(colonelList.size());
            fileImportHandlerVo.setMsg("非运营商角色");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
//            throw new ServiceException("非运营商角色");
        }


        // 获取字典数据 业务员职务
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_COLONEL_LEVEL);
        Map<String, SysDictData> colonelLevelMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictLabel, item -> item));

        // 获取字典数据 系统是否  Y:是 N：否
        List<SysDictData> dictCache1 = DictUtils.getDictCache(DictTypeConstants.SYS_YES_NO);
        Map<String, SysDictData> yesNoMap = (Objects.nonNull(dictCache1) ? dictCache1 : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictLabel, item -> item));

        // 获取字典数据 系统是否  Y:是 N：否
        List<SysDictData> dictCache2 = DictUtils.getDictCache(DictTypeConstants.SYS_USER_SEX);
        Map<String, SysDictData> sexMap = (Objects.nonNull(dictCache2) ? dictCache2 : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictLabel, item -> item));

        // 获取字典数据 停用、启用
//        List<SysDictData> dictCache3 = DictUtils.getDictCache(DictTypeConstants.SYS_COMMON_STATUS);
//        Map<String, SysDictData> statusMap = (Objects.nonNull(dictCache3) ? dictCache3 : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictLabel, item -> item));

//        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        //获取城市区域数据
        List<AreaDTO> areaDTOList = areaApi.getListBySysCode(sysCode).getCheckedData();
        Map<String, AreaDTO> areaDTOMap = CollectionUtils.convertMap(areaDTOList, AreaDTO::getAreaName);

        Map<MemColonel, ColonelUserDTO> colonelCache = new LinkedHashMap<>();
//        int successNum = colonelList.size();
//        int failureNum = 0;
//        StringBuilder successMsg = new StringBuilder();
//        StringBuilder failureMsg = new StringBuilder();

        // 学历字典
        List<SysDictData> eduCache = DictUtils.getDictCache(DictTypeConstants.TYPE_OF_EDUCATION);
        Map<String, String> eduCacheMap = new HashMap<>();
        if (Objects.nonNull(eduCache)) {
            eduCacheMap = eduCache.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        }

        for (int line = 0; line < colonelList.size(); line++) {
            //导入明细
            SysFileImportDtl sysFileImportDtl = new SysFileImportDtl();
            sysFileImportDtl.setSysCode(sysCode);
            sysFileImportDtl.setCreateBy(sysFileImportDtl.getCreateBy());
            sysFileImportDtl.setCreateTime(new Date());
            sysFileImportDtl.setFileImportId(fileImportId);

            if (failureMsg.length() > 2000) {
                break;
            }
            int cellNumber = line + 3+seq;
            MemColonelImportExcel data = colonelList.get(line);
            sysFileImportDtl.setDtlJson(JsonUtils.toJsonString(data));

            if (StringUtils.isEmpty(data.getColonelName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>业务员名称不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据业务员名称不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (data.getColonelName().length() > 20) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>业务员名称字符长度超长20。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据业务员名称字符长度超长20。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (StringUtils.isEmpty(data.getSexName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>性别不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据性别不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            SysDictData sexDictData =sexMap.get(data.getSexName());
            if (Objects.isNull(sexDictData)) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>性别不存在。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据性别不存在。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (StringUtils.isEmpty(data.getColonelPhone())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>手机号不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据手机号不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (data.getColonelPhone().length() > 11) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>手机号字符长度超长11。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据手机号字符长度超长11。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (StringUtils.isEmpty(data.getUserName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>业务员账号不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据业务员账号不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (data.getUserName().length() > 11) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>业务员账号字符长度超长11。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据业务员账号字符长度超长11。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            Boolean checkColonelUser = remotedcApi.checkColonelUser(data.getUserName(), data.getColonelPhone()).getCheckedData();
            if (checkColonelUser) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>业务员账号或手机号已存在，不可创建业务员账户。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据业务员账号或手机号已存在，不可创建业务员账户。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }


            if (StringUtils.isEmpty(data.getAreaName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>城市区域不能为空。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据城市区域不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            AreaDTO area = areaDTOMap.get(data.getAreaName());
            if (Objects.isNull(area)) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>城市区域不存在。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据城市区域不存在。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (StringUtils.isEmpty(data.getColonelLevelName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>职务不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据职务不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            SysDictData colonelLevelDictData = colonelLevelMap.get(data.getColonelLevelName());
            if (Objects.isNull(colonelLevelDictData)) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>职务不存在。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据职务不存在。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (StringUtils.isEmpty(data.getIsColonelAdmin())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>是否是业务管理员不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据是否是业务管理员不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            SysDictData sysNoDictData = yesNoMap.get(data.getIsColonelAdmin());
            if (Objects.isNull(sysNoDictData)) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>是否是业务管理员状态不存在。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据是否是业务管理员状态不存在。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            MemColonel pcolonel = null;
            if (StringUtils.isNotEmpty(data.getPcolonelName())) {
                pcolonel = memColonelMapper.getColonelByColenelName(sysCode, data.getPcolonelName());
                if (Objects.isNull(pcolonel)) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>上级业务员不存在。</span>。", cellNumber));
                    sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                    sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据上级业务员不存在。", cellNumber));
                    sysFileImportDtls.add(sysFileImportDtl);
                    continue;
                }
            }

            if (StringUtils.isNotEmpty(data.getIdcard()) && data.getIdcard().length() > 18) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>身份证号字段长度超出18。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据身份证号字段长度超出18。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }


            if (StringUtils.isEmpty(data.getAppOrderPriceAdjust())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>是否支持APP下单改价不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据是否支持APP下单改价不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            SysDictData sysNoDictData1 = yesNoMap.get(data.getAppOrderPriceAdjust());
            if (Objects.isNull(sysNoDictData1)) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>是否支持APP下单改价状态不存在。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据<span style='color:red'>是否支持APP下单改价状态不存在。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (StringUtils.isEmpty(data.getAppAfterPriceAdjust())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>是否支持APP退货改价不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据是否支持APP退货改价不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            SysDictData sysNoDictData2 = yesNoMap.get(data.getAppAfterPriceAdjust());
            if (Objects.isNull(sysNoDictData2)) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>是否支持APP退货改价状态不存在。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据是否支持APP退货改价状态不存在。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (StringUtils.isEmpty(data.getOrderAutoApprove())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>是否支持下单APP自动审核不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据是否支持下单APP自动审核不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            SysDictData sysNoDictData3 = yesNoMap.get(data.getOrderAutoApprove());
            if (Objects.isNull(sysNoDictData3)) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>是否支持下单APP自动审核状态不存在。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据是否支持下单APP自动审核状态不存在。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }



            if (ObjectUtil.isNull(data.getPercentageRate())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>提成系数不能为空。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据提成系数不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (NumberUtil.isGreater(BigDecimal.ONE, data.getPercentageRate()) || NumberUtil.isGreater(data.getPercentageRate(), new BigDecimal("100"))) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>提成系数只能是1-100之间的整数。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据提成系数只能是1-100之间的整数。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }


            if (StringUtils.isNotEmpty(data.getContactAddr()) && data.getContactAddr().length() > 500) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>联系地址字段超出500长度。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据联系地址字段超出500长度。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (StringUtils.isNotEmpty(data.getMemo()) && data.getMemo().length() > 500) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>备注字段超出500长度。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据备注字段超出500长度。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (StringUtils.isNotEmpty(data.getStatusName()) && Objects.isNull(SysYesNoEnum.getYesNo(data.getStatusName()))) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>是否启用字段填写状态不存在。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据是否启用字段填写状态不存在。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            Long threeAreaCityId = null;
            // 校验城市(省/市/县区)
            if (StringUtils.isNotEmpty(data.getCity())) {
                threeAreaCityId = validateCity(data.getCity());
                if (threeAreaCityId == null) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>城市(省/市/县区)格式不正确或不存在。</span>", cellNumber));
                    sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                    sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据城市(省/市/县区)格式不正确或不存在。", cellNumber));
                    sysFileImportDtls.add(sysFileImportDtl);
                    continue;
                }
            }else{
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>城市(省/市/县区)必填。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据城市(省/市/县区)必填。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if(StringUtils.isNotEmpty(data.getPassword())){
                String tips = JwtUtils.passwordCheckByTips(data.getPassword());
                if(StringUtils.isNotEmpty(tips)){
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>{}。</span>。", cellNumber,tips));
                    sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                    sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据{}。。", cellNumber,tips));
                    sysFileImportDtls.add(sysFileImportDtl);
                    continue;
                }
            }

            // 转换导入实体 业务员信息
            MemColonel memColonel = new MemColonel();
            {
                MemberColonelConvert.INSTANCE.convertImport(memColonel, data);
                // 转换学历字典值
                if (eduCacheMap.containsKey(memColonel.getEdu())) {
                    // 转换字典
                    memColonel.setEdu(eduCacheMap.get(memColonel.getEdu()));
                }

                memColonel.setSex(Integer.parseInt(sexDictData.getDictValue()))
                        .setAreaId(area.getAreaId())
                        .setAuditState(1)
                        .setSource("PC")
                        .setColonelLevel(Long.parseLong(colonelLevelDictData.getDictValue()))
                        .setIsColonelAdmin(sysNoDictData.getDictValue())
                        .setPcolonelId(ObjectUtil.isNull(pcolonel) ? null : pcolonel.getColonelId())
                        .setStatus(ObjectUtil.isNull(data.getStatusName()) ? StatusConstants.FLAG_TRUE : SysYesNoEnum.getYesNo(data.getStatusName()))
                        .setDelFlag(NumberPool.INT_ZERO)
                        .setAppOrderPriceAdjust(sysNoDictData1.getDictValue())
                        .setAppAfterPriceAdjust(sysNoDictData2.getDictValue())
                        .setOrderAutoApprove(sysNoDictData3.getDictValue())
                        .setThreeAreaCityId(threeAreaCityId)
                ;
            }

//            // 转换导入实体 业务员用户信息
            ColonelUserDTO colonelUser = new ColonelUserDTO();
            {
                colonelUser.setColonelName(memColonel.getColonelName())
                        .setUserName(data.getUserName())
                        .setPassword(StringUtils.isEmpty(data.getPassword()) ? memberCacheService.getDefaultPassword(sysCode) : data.getPassword())
                        .setColonelPhone(memColonel.getColonelPhone())
                ;

            }
            colonelCache.put(memColonel, colonelUser);
            sysFileImportDtl.setStatus(STATUS_SUCCESS);
            sysFileImportDtls.add(sysFileImportDtl);
        }

        fileImportHandlerVo.setTotalNum(totalNum);
        fileImportHandlerVo.setSuccessNum(totalNum-failureNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setList(sysFileImportDtls);
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            fileImportHandlerVo.setMsg(failureMsg.toString());
            return fileImportHandlerVo;
//            throw new ServiceException(failureMsg.toString());
        } else {
            colonelCache.forEach((colonel, colonelUser) -> {
                colonel.setSysCode(sysCode);
                // 新增业务员
                memColonelMapper.insert(colonel);

                // 新增业务员账号，用于业务员APP登录
                colonelUser.setColonelId(colonel.getColonelId());
                Long userId = remotedcApi.insertColonelUser(colonelUser).getCheckedData();

                // 更新用户和门店绑定关系
                colonel.setUserId(userId);
                memColonelMapper.updateById(colonel);
            });
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
            fileImportHandlerVo.setStatus(STATUS_SUCCESS);
            fileImportHandlerVo.setMsg(successMsg.toString());
        }
        return fileImportHandlerVo;
    }

    /**
     * 校验省/市/区是否存在，并获取第三级城市 ID。
     *
     * @param city 省/市/区，格式如：湖南省/长沙市/雨花区
     * @return 如果省市区有效，返回第三级城市 ID；否则返回 null
     */
    public Long validateCity(String city) {
        if (StringUtils.isEmpty(city)) {
            throw new ServiceException("城市信息不能为空");
        }

        String[] cityParts = city.split("/");
        if (cityParts.length != 3) {
            throw new ServiceException("城市格式不正确，正确格式为 省/市/县区");
        }

        // 依次验证 省、市、区是否存在，并获取它们的ID
        Long provinceId = getCityIdByNameAndDeep(cityParts[0], 0, null);
        if (provinceId == null) {
            return null;
        }

        Long cityId = getCityIdByNameAndDeep(cityParts[1], 1, provinceId);
        if (cityId == null) {
            return null;
        }

        Long districtId = getCityIdByNameAndDeep(cityParts[2], 2, cityId);
        return districtId;
    }

    private Long getCityIdByNameAndDeep(String name, int deep, Long parentId) {
        // 调用远程服务查询
        SysAreaCityRespVO result = areaCityApi.getByNameAndParent(name,parentId,deep).getCheckedData();
        if (ToolUtil.isNotEmpty(result)) {
            return result.getAreaCityId();
        }
        return null;
    }

    @Override
    public List<ColonelDTO> getSubordinates(Long colonelId) {
        List<MemColonel> subordinates = memColonelMapper.getSubordinates(colonelId);
        return HutoolBeanUtils.toBean(subordinates, ColonelDTO.class);
    }

    @Override
    public List<ColonelDTO> selectDevelopmentSalesmanByColonelId(Long colonelId) {
        List<MemColonel> developmentSalesman = memColonelMapper.selectDevelopmentSalesmanByColonelId(colonelId);
        return HutoolBeanUtils.toBean(developmentSalesman, ColonelDTO.class);
    }
    @Override
    public String getColonelName(Long colonelId){
        return memColonelMapper.getColonelName(colonelId);
    }

    private void validateMemColonelExists(Long colonelId) {
        if (memColonelMapper.selectById(colonelId) == null) {
            throw exception(MEM_COLONEL_NOT_EXISTS);
        }
    }

    @Override
    public List<MemColonel> getMemColonelList() {
        String funcScop = SecurityUtils.getLoginUser().getFuncScop();
        if (Objects.isNull(funcScop) || !funcScop.equalsIgnoreCase("DC")) {
            return memColonelMapper.getListBySysCode(SecurityUtils.getLoginUser().getSysCode());
        }

        List<AreaDTO> areaDTOList =
                areaApi.getAreaBySyscodeAndDcId(SecurityUtils.getLoginUser().getSysCode(), SecurityUtils.getLoginUser().getDcId()).getCheckedData();
        return memColonelMapper.getColonelListByAreaIds(CollectionUtils.convertList(areaDTOList, AreaDTO::getAreaId));
    }

    @Override
    public List<Long> getAllChildColonel(Long colonelId) {
        HashSet<Long> res = new HashSet<>();
        List<MemColonelRelation> relationList = memColonelRelationMapper.selectByAdmin(ListUtil.toList(colonelId));
        int counter = 0;
        for (;;) {
            counter++;
            if (counter > 100) {
                break;
            }
            List<Long> colonelList = relationList.stream().map(MemColonelRelation::getColonelId).collect(Collectors.toList());
            colonelList = colonelList.stream().filter(item -> !res.contains(item)).collect(Collectors.toList());
            res.addAll(colonelList);
            relationList = memColonelRelationMapper.selectByAdmin(colonelList);
            if (relationList.isEmpty()) {
                break;
            }
        }
        res.add(colonelId);
        return new ArrayList<>(res);
    }

    @Override
    public CommonResult<Boolean> updateColonelPublishOpenidBind(WxColonelPublishOpenidBindReqVO reqVO) {
        Long colonelId = redisService.getCacheObject(RedisConstants.COLONEL_PRE_BIND_WX_PUBLISH_OPENID + reqVO.getBindKey());
        if (Objects.isNull(colonelId)) {
            return CommonResult.error(MEM_COLONEL_BIND_PUBLISH_KEY_ERR);
        }
        // 更新业务员公众号openid
        MemColonel update = new MemColonel();
        update.setColonelId(colonelId);
        update.setPublishOpenid(reqVO.getOpenid());
        memColonelMapper.updateById(update);
        // 移除缓存
        colonelDTOCache.remove(colonelId);
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 保存平台商户信息
     * @param saveReqVO     保存数据
     * @param colonelId     业务员ID
     */
    private void savePayPlatformMerchant(MemColonelSaveReqVO saveReqVO, Long colonelId) {
        if (ObjectUtil.isNotEmpty(saveReqVO.getPlatformSimpleBindList())) {
            for (PlatformSimpleBindVO simpleBindVO : saveReqVO.getPlatformSimpleBindList()) {
                PlatformMerchantDTO merchant = BeanUtils.toBean(simpleBindVO, PlatformMerchantDTO.class);
                merchant.setMerchantId(colonelId)
                        .setMerchantType(MerchantTypeEnum.COLONEL.getType())
                        .setSysCode(SecurityUtils.getLoginUser().getSysCode());
                platformMerchantApi.savePlatformMerchant(merchant).checkError();
            }
        }
    }

    /**
     * 清除业务员缓存
     * @param colonelId
     */
    @Override
    public void removeColonelCache(Long colonelId){
        colonelDTOCache.remove(colonelId);
    }

    /**
     * @param sysCode
     * @return
     */
    @Override
    public List<MemColonel> getAreaIdExistColone(Long areaId, Long sysCode) {
        return memColonelMapper.getAreaIdExistColone(areaId, sysCode);
    }

    @Override
    public void updateMemColonelPassword(MemColonelSaveReqVO updateReqVO) {
        String password = updateReqVO.getPassword();
        if(ToolUtil.isNotEmpty(password) && (password.length() < UserConstants.PASSWORD_MIN_LENGTH || password.length() > UserConstants.PASSWORD_MAX_LENGTH)){
            throw exception(MEM_MEMBER_PASSWORD_NOT_LENGTH);
        }
        //更新业务员用户账号信息
        remotedcApi.updateColonelUser(HutoolBeanUtils.toBean(updateReqVO,ColonelUserDTO.class));
        //清除业务员缓存
        removeColonelCache(updateReqVO.getColonelId());
    }
}
