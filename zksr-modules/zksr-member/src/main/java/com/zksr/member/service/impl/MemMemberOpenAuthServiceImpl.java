package com.zksr.member.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.b2bAuth.dto.MemMemberOpenAuthDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemMemberOpenAuthMapper;
import com.zksr.member.convert.memberB2bAuth.MemMemberOpenAuthConvert;
import com.zksr.member.domain.MemMemberOpenAuth;
import com.zksr.member.controller.memberB2bAuth.vo.MemMemberOpenAuthPageReqVO;
import com.zksr.member.controller.memberB2bAuth.vo.MemMemberOpenAuthSaveReqVO;
import com.zksr.member.service.IMemMemberOpenAuthService;

import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * b2b openid 认证Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Service
public class MemMemberOpenAuthServiceImpl implements IMemMemberOpenAuthService {
    @Autowired
    private MemMemberOpenAuthMapper memMemberOpenAuthMapper;

    /**
     * 新增b2b openid 认证
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemMemberOpenAuth(MemMemberOpenAuthSaveReqVO createReqVO) {
        // 插入
        MemMemberOpenAuth memMemberOpenAuth = MemMemberOpenAuthConvert.INSTANCE.convert(createReqVO);
        memMemberOpenAuthMapper.insert(memMemberOpenAuth);
        // 返回
        return memMemberOpenAuth.getB2bAuthOpenId();
    }

    /**
     * 修改b2b openid 认证
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemMemberOpenAuth(MemMemberOpenAuthSaveReqVO updateReqVO) {
        memMemberOpenAuthMapper.updateById(MemMemberOpenAuthConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除b2b openid 认证
     *
     * @param b2bAuthOpenId ${pkColumn.columnComment}
     */
    @Override
    public void deleteMemMemberOpenAuth(Long b2bAuthOpenId) {
        // 删除
        memMemberOpenAuthMapper.deleteById(b2bAuthOpenId);
    }

    /**
     * 批量删除b2b openid 认证
     *
     * @param b2bAuthOpenIds 需要删除的b2b openid 认证主键
     * @return 结果
     */
    @Override
    public void deleteMemMemberOpenAuthByB2bAuthOpenIds(Long[] b2bAuthOpenIds) {
        for(Long b2bAuthOpenId : b2bAuthOpenIds){
            this.deleteMemMemberOpenAuth(b2bAuthOpenId);
        }
    }

    /**
     * 获得b2b openid 认证
     *
     * @param b2bAuthOpenId ${pkColumn.columnComment}
     * @return b2b openid 认证
     */
    @Override
    public MemMemberOpenAuth getMemMemberOpenAuth(Long b2bAuthOpenId) {
        return memMemberOpenAuthMapper.selectById(b2bAuthOpenId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemMemberOpenAuth> getMemMemberOpenAuthPage(MemMemberOpenAuthPageReqVO pageReqVO) {
        return memMemberOpenAuthMapper.selectPage(pageReqVO);
    }

    @Override
    public MemMemberOpenAuth getBind(String appid, String openid, Long branchId) {
        return memMemberOpenAuthMapper.selectByAppidAndOpenidAndBranchId(appid, openid, branchId);
    }

    @Override
    public void updateAuth(MemMemberOpenAuthDTO authDTO) {
        MemMemberOpenAuth openAuth = memMemberOpenAuthMapper.selectByAppidAndOpenidAndBranchId(authDTO.getAppid(), authDTO.getOpenid(), authDTO.getBranchId());
        if (Objects.nonNull(openAuth)) {
            return;
        }
        memMemberOpenAuthMapper.insert(MemMemberOpenAuthConvert.INSTANCE.convertPO(authDTO));
    }

}
