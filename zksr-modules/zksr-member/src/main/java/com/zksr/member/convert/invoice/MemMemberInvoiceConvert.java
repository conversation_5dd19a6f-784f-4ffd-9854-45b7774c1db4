package com.zksr.member.convert.invoice;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.member.dto.MemMemberInvoicePageReqDTO;
import com.zksr.member.api.member.dto.MemMemberInvoiceRespDTO;
import com.zksr.member.api.member.dto.MemMemberInvoiceSaveReqDTO;
import com.zksr.member.controller.invoice.vo.MemMemberInvoicePageReqVO;
import com.zksr.member.domain.MemMemberInvoice;
import com.zksr.member.controller.invoice.vo.MemMemberInvoiceRespVO;
import com.zksr.member.controller.invoice.vo.MemMemberInvoiceSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 用户发票 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-07-03
*/
@Mapper
public interface MemMemberInvoiceConvert {

    MemMemberInvoiceConvert INSTANCE = Mappers.getMapper(MemMemberInvoiceConvert.class);

    MemMemberInvoiceRespVO convert(MemMemberInvoice memMemberInvoice);

    MemMemberInvoice convert(MemMemberInvoiceSaveReqVO memMemberInvoiceSaveReq);

    PageResult<MemMemberInvoiceRespVO> convertPage(PageResult<MemMemberInvoice> memMemberInvoicePage);

    MemMemberInvoiceSaveReqVO convert2MemMemberInvoiceSaveReqVO(MemMemberInvoiceSaveReqDTO createReqDTO);

    MemMemberInvoicePageReqVO convert2MemMemberInvoicePageReqVO(MemMemberInvoicePageReqDTO pageReqVO);

    PageResult<MemMemberInvoiceRespDTO> convert2MemMemberInvoiceRespDTO(PageResult<MemMemberInvoice> rs);

    MemMemberInvoiceRespDTO convert2MemMemberInvoiceResp(MemMemberInvoice rs);

}