package com.zksr.member.controller.colonelApp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员首页决策数据统计response
 * @date 2024/11/23 9:07
 */
@Data
@ApiModel(description = "业务员首页决策数据统计response")
public class ColonelDecisionTotalRespVO {

    @ApiModelProperty("本月目标统计")
    private TargetTotal currentMonthTotal;

    @ApiModelProperty("上月目标统计")
    private TargetTotal beforeMonthTotal;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(description = "任务统计")
    public static class TargetTotal {

        @ApiModelProperty("月份")
        private String month;

        @ApiModelProperty("总任务指标")
        private BigDecimal totalTarget;

        @ApiModelProperty("已完成指标")
        private BigDecimal finishTarget;
    }
}
