package com.zksr.member.controller.branchRegister.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/9 14:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MemBranchRegisterSaveResp {

    @ApiModelProperty("门店注册ID")
    private Long branchRegisterId;

    @ApiModelProperty("自动审核创建的门店ID")
    private Long branchId;
}
