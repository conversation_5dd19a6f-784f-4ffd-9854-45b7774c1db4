package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 陈列计划优惠明细对象 mem_display_plan_coupon
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@TableName(value = "mem_display_plan_coupon")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemDisplayPlanCoupon extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long planCouponId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 陈列计划ID */
    @Excel(name = "陈列计划ID")
    private Long planId;

    /** 优惠劵ID */
    @Excel(name = "优惠劵ID")
    private Long couponId;

    /** 约定兑付总数 */
    @Excel(name = "约定兑付总数")
    private Long exchangeSum;

    /** 已兑付总数 */
    @Excel(name = "已兑付总数")
    private Long finishSum;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 删除状态(0:正常，2：删除) */
    private Integer delFlag;

}
