package com.zksr.member.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.zksr.account.api.balance.BalanceApi;
import com.zksr.account.api.balance.BalanceFlowApi;
import com.zksr.account.api.balance.dto.MemBranchBalanceDTO;
import com.zksr.account.api.balance.dto.MemBranchBalanceFlowDTO;
import com.zksr.account.api.balance.vo.AccBalanceFlowRespVO;
import com.zksr.account.api.balance.vo.AccBalanceRespVO;
import com.zksr.account.api.balance.vo.MemBranchBalanceRespVO;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisService;
import com.zksr.member.controller.branch.balance.dto.MemBranchBalancePageReqDTO;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.service.IMemBranchBalanceService;
import com.zksr.member.service.IMemBranchService;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.PartnerMiniSettingPolicyDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

@Slf4j
@Service
public class MemBranchBalanceServiceImpl implements IMemBranchBalanceService {

    @Autowired
    private BalanceApi balanceApi;

    @Autowired
    private BalanceFlowApi balanceFlowApi;

    @Autowired
    private PartnerPolicyApi partnerPolicyApi;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IMemBranchService memBranchService;

    /**
     * 门店余额管理查询分页查询
     */
    @Override
    public PageResult<MemBranchBalanceRespVO> getMemBranchBalancePage(MemBranchBalancePageReqDTO pageReqDTO) {
        Page<MemBranch> page = new Page<>(pageReqDTO.getPageNo(), pageReqDTO.getPageSize());
        Page<MemBranch> respPage = memBranchService.selectMemBranchBalancePage(page, pageReqDTO);
        List<MemBranch> dataList = respPage.getRecords();
        if (ToolUtil.isEmpty(dataList)) {
            return new PageResult<>(new ArrayList<>(), respPage.getTotal());
        }
        // 组装返回数据
        List<MemBranchBalanceRespVO> resultList = HutoolBeanUtils.toBean(dataList, MemBranchBalanceRespVO.class);
        List<Long> branchIdList = dataList.stream().map(MemBranch::getBranchId).filter(Objects::nonNull).collect(Collectors.toList());
        // 查询账户系统获取门店余额数据
        CommonResult<List<AccBalanceRespVO>> commonResult = balanceApi.getBalanceInfoList(branchIdList);
        List<AccBalanceRespVO> balanceRespVOList = commonResult.getCheckedData();
        Map<Long, BigDecimal> balanceMap = balanceRespVOList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AccBalanceRespVO::getBranchId, AccBalanceRespVO::getAmt, (x, y) -> x));
        // 赋值余额
        resultList.forEach(t -> t.setAmt(balanceMap.getOrDefault(t.getBranchId(), BigDecimal.ZERO)));
        return new PageResult<>(resultList, respPage.getTotal());
    }

    /**
     * 充值或退款获取门店余额明细且生成交易单号
     */
    @Override
    public MemBranchBalanceRespVO getMemBranchBalanceDetailInfo(Long branchId, Integer operType) {
        MemBranch memBranch = memBranchService.getMemBranch(branchId);
        if (Objects.isNull(memBranch)) {
            throw exception(MEM_BRANCH_REGISTER_NOT_EXISTS);
        }
        MemBranchBalanceRespVO respVO = new MemBranchBalanceRespVO();
        BeanUtils.copyProperties(memBranch, respVO);
        // 查询账户系统获取门店余额数据
        CommonResult<List<AccBalanceRespVO>> commonResult = balanceApi.getBalanceInfoList(Lists.newArrayList(branchId));
        List<AccBalanceRespVO> balanceRespVOList = commonResult.getCheckedData();
        Map<Long, BigDecimal> balanceMap = balanceRespVOList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AccBalanceRespVO::getBranchId, AccBalanceRespVO::getAmt, (x, y) -> x));
        respVO.setAmt(balanceMap.getOrDefault(branchId, BigDecimal.ZERO));
        // 生成订单号前缀,operType:0为充值，1为退款
        if (Objects.nonNull(operType)) {
            String tradeNoPreFix = operType.equals(0) ? "YECZ" : "YETK";
            respVO.setTradeNo(tradeNoPreFix + redisService.getBalanceOrderNo());
        }
        return respVO;
    }

    /**
     * 门店余额充值
     */
    @Override
    public String recharge(MemBranchBalancePageReqDTO pageReqDTO) {
        this.verifyReqParam(pageReqDTO);
        // 判断该门店是否存在
        MemBranch memBranch = memBranchService.getMemBranch(pageReqDTO.getBranchId());
        if (Objects.isNull(memBranch)) {
            throw exception(MEM_BRANCH_REGISTER_NOT_EXISTS);
        }
        if (memBranch.getStatus().equals(NumberPool.INT_ZERO)) {
            throw exception(BRANCH_DISABLE_NOT_ALLOW_OPER);
        }
        // 获取平台商余额支付配置
        CommonResult<PartnerMiniSettingPolicyDTO> commonResult = partnerPolicyApi.getPartnerMiniSettingPolicy(memBranch.getSysCode());
        PartnerMiniSettingPolicyDTO policyDTO = commonResult.getCheckedData();
        // 是否开启余额管理/支付(0:关闭，1:开启，默认为关闭)
        String openBalancePay = policyDTO.getOpenBalancePay();
        if (!StringUtils.equals(openBalancePay, "1")) {
            throw exception(BALANCE_PAY_NOT_OPEN);
        }
        // 请求账户系统进行余额充值
        MemBranchBalanceDTO balanceDTO = new MemBranchBalanceDTO();
        BeanUtils.copyProperties(pageReqDTO, balanceDTO);
        balanceDTO.setSysCode(memBranch.getSysCode());
        balanceDTO.setOperUserName(SecurityContextHolder.getUserName());
        CommonResult<String> rechargeResult = balanceApi.recharge(balanceDTO);
        return rechargeResult.getCheckedData();
    }

    /**
     * 校验参数
     */
    private void verifyReqParam(MemBranchBalancePageReqDTO pageReqDTO) {
        if (Objects.isNull(pageReqDTO.getBranchId())) {
            throw exception(BRANCH_ID_NOT_EMPTY);
        }
        if (StringUtils.isEmpty(pageReqDTO.getTradeNo())) {
            throw exception(TRADE_NO_NOT_EMPTY);
        }
        if (Objects.isNull(pageReqDTO.getOpAmt()) || pageReqDTO.getOpAmt().compareTo(BigDecimal.ZERO) <= 0) {
            throw exception(OP_AMT_NOT_NULL);
        }
        if (pageReqDTO.getOpAmt().compareTo(NumberPool.MAX_PRICE) > 0) {
            throw exception(OP_AMT_NOT_NULL);
        }
    }

    @Override
    public String refund(MemBranchBalancePageReqDTO pageReqDTO) {
        this.verifyReqParam(pageReqDTO);
        Long branchId = pageReqDTO.getBranchId();
        // 判断该门店是否存在
        MemBranch memBranch = memBranchService.getMemBranch(branchId);
        if (Objects.isNull(memBranch)) {
            throw exception(MEM_BRANCH_REGISTER_NOT_EXISTS);
        }
        if (memBranch.getStatus().equals(NumberPool.INT_ZERO)) {
            throw exception(BRANCH_DISABLE_NOT_ALLOW_OPER);
        }
        // 获取平台商余额支付配置
        CommonResult<PartnerMiniSettingPolicyDTO> commonResult = partnerPolicyApi.getPartnerMiniSettingPolicy(memBranch.getSysCode());
        PartnerMiniSettingPolicyDTO policyDTO = commonResult.getCheckedData();
        // 是否开启余额管理/支付(0:关闭，1:开启，默认为关闭)
        String openBalancePay = policyDTO.getOpenBalancePay();
        if (!StringUtils.equals(openBalancePay, "1")) {
            throw exception(BALANCE_PAY_NOT_OPEN);
        }
        // 查询账户系统获取门店余额数据
        CommonResult<List<AccBalanceRespVO>> balanceResult = balanceApi.getBalanceInfoList(Lists.newArrayList(branchId));
        List<AccBalanceRespVO> balanceRespVOList = balanceResult.getCheckedData();
        Map<Long, BigDecimal> balanceMap = balanceRespVOList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AccBalanceRespVO::getBranchId, AccBalanceRespVO::getAmt, (x, y) -> x));
        BigDecimal balanceAmt = balanceMap.getOrDefault(branchId, BigDecimal.ZERO);
        // 校验门店余额是否满足退款金额
        if (balanceAmt.compareTo(pageReqDTO.getOpAmt()) < 0) {
            throw exception(BALANCE_AMT_NOT_ENOUGH);
        }
        // 请求账户系统进行余额退款
        MemBranchBalanceDTO balanceDTO = new MemBranchBalanceDTO();
        BeanUtils.copyProperties(pageReqDTO, balanceDTO);
        balanceDTO.setSysCode(memBranch.getSysCode());
        balanceDTO.setOperUserName(SecurityContextHolder.getUserName());
        CommonResult<String> rechargeResult = balanceApi.refund(balanceDTO);
        return rechargeResult.getCheckedData();
    }

    @Override
    public CommonResult<PageResult<AccBalanceFlowRespVO>> getFlowPageList(MemBranchBalanceFlowDTO pageReqDTO) {
        return balanceFlowApi.getFlowPageList(pageReqDTO);
    }

}
