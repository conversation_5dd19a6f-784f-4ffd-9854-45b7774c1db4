package com.zksr.member.convert.colonel;

import com.zksr.member.controller.colonelApp.dto.ColonelAppPolicyRespDTO;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerPolicy.dto.AppletAgreementPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.BasicSettingPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.PartnerMiniSettingPolicyDTO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ColonelAppConvert {
    ColonelAppConvert INSTANCE = Mappers.getMapper(ColonelAppConvert.class);


    @Mappings({
            @Mapping(source = "partnerMiniSettingPolicyDTO.localItemAfterType", target = "localItemAfterType"),
            @Mapping(source = "partnerMiniSettingPolicyDTO.localItemAfterApproveType", target = "localItemAfterApproveType")
    })
    @BeanMapping(ignoreByDefault = true)
    ColonelAppPolicyRespDTO convertColonelAppPolicyDTO(PartnerMiniSettingPolicyDTO partnerMiniSettingPolicyDTO);
}
