package com.zksr.member.convert.branchLifeCycleZip;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemBranchLifecycleZip;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipRespVO;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 门店生命周期拉链 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-03-07
*/
@Mapper
public interface MemBranchLifecycleZipConvert {

    MemBranchLifecycleZipConvert INSTANCE = Mappers.getMapper(MemBranchLifecycleZipConvert.class);

    MemBranchLifecycleZipRespVO convert(MemBranchLifecycleZip memBranchLifecycleZip);

    MemBranchLifecycleZip convert(MemBranchLifecycleZipSaveReqVO memBranchLifecycleZipSaveReq);

    PageResult<MemBranchLifecycleZipRespVO> convertPage(PageResult<MemBranchLifecycleZip> memBranchLifecycleZipPage);

    List<MemBranchLifecycleZipRespVO> convertList(List<MemBranchLifecycleZip> memBranchLifecycleZips);
}