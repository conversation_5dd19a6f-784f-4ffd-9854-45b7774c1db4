package com.zksr.member.controller.relation.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 业务员关系对象 mem_colonel_relation
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@ApiModel("业务员关系 - mem_colonel_relation分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
public class MemColonelRelationPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelRelationId;

    /** 管理业务员ID */
    @Excel(name = "管理业务员ID")
    @ApiModelProperty(value = "管理业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long adminColonelId;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    @Excel(name = "业务员名称")
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @Excel(name = "业务员手机号")
    @ApiModelProperty(value = "业务员手机号")
    private String colonelPhone;

    public MemColonelRelationPageReqVO(Long adminColonelId) {
        this.adminColonelId = adminColonelId;
    }
}
