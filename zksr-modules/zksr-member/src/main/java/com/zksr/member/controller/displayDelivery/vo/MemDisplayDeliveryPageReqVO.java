package com.zksr.member.controller.displayDelivery.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 陈列计划兑付对象 mem_display_delivery
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@ApiModel("陈列计划兑付 - mem_display_delivery分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemDisplayDeliveryPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long deliveryId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 陈列计划ID */
    @Excel(name = "陈列计划ID")
    @ApiModelProperty(value = "陈列计划ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long planId;

    /** 兑换数量 */
    @Excel(name = "兑换数量")
    @ApiModelProperty(value = "兑换数量")
    private Long finishSum;

    /** 照片 */
    @Excel(name = "照片")
    @ApiModelProperty(value = "照片")
    private String pic;

    /** 优惠劵ID */
    @Excel(name = "优惠劵ID")
    @ApiModelProperty(value = "优惠劵ID")
    private Long couponId;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 删除状态(0:正常，2：删除) */
    @ApiModelProperty(value = "删除状态")
    private Integer delFlag;


}
