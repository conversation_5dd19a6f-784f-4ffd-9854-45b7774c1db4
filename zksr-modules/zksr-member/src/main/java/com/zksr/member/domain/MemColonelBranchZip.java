package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * 门店业务员关系拉链表 mem_colonel_branch_zip
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@TableName(value = "mem_colonel_branch_zip")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MemColonelBranchZip extends BaseEntity{
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long colonelBranchZipId;

    @Excel(name = "平台商ID")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    @Excel(name = "门店ID")
    private Long branchId;

    @Excel(name = "业务员ID")
    private Long colonelId;

    @Excel(name = "开始日期")
    private Date startDate;

    @Excel(name = "结束日期")
    private Date endDate;
}
