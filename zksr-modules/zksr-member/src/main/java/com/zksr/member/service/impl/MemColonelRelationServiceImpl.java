package com.zksr.member.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.controller.relation.vo.MemColonelRelationRespVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemColonelRelationMapper;
import com.zksr.member.domain.MemColonelRelation;
import com.zksr.member.controller.relation.vo.MemColonelRelationPageReqVO;
import com.zksr.member.controller.relation.vo.MemColonelRelationSaveReqVO;
import com.zksr.member.service.IMemColonelRelationService;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 业务员关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Service
public class MemColonelRelationServiceImpl implements IMemColonelRelationService {
    @Autowired
    private MemColonelRelationMapper memColonelRelationMapper;

    /**
     * 新增业务员关系
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemColonelRelation(MemColonelRelationSaveReqVO createReqVO) {

        if (StringUtils.isEmpty(createReqVO.getColonelIds()) && StringUtils.isNull(createReqVO.getColonelId()))
            throw exception(-1,"绑定业务员为空！");
        // 插入
        List<MemColonelRelation> relations = new ArrayList<>();
        MemColonelRelation memColonelRelation = HutoolBeanUtils.toBean(createReqVO, MemColonelRelation.class);
        if (StringUtils.isNull(memColonelRelation.getColonelId())){
            relations = createReqVO.getColonelIds().stream().map(key ->{
                MemColonelRelation relation = new MemColonelRelation();
                relation.setAdminColonelId(createReqVO.getAdminColonelId());
                relation.setColonelId(key);
                return  relation;
            }).collect(Collectors.toList());
        } else {
            relations.add(memColonelRelation);
        }
        memColonelRelationMapper.insertBatch(relations);
        // 返回
        return memColonelRelation.getColonelRelationId();
    }

    /**
     * 修改业务员关系
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemColonelRelation(MemColonelRelationSaveReqVO updateReqVO) {
        memColonelRelationMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, MemColonelRelation.class));
    }

    /**
     * 删除业务员关系
     *
     * @param colonelRelationId 主键ID
     */
    @Override
    public void deleteMemColonelRelation(Long colonelRelationId) {
        // 删除
        memColonelRelationMapper.deleteById(colonelRelationId);
    }

    /**
     * 批量删除业务员关系
     *
     * @param colonelRelationIds 需要删除的业务员关系主键
     * @return 结果
     */
    @Override
    public void deleteMemColonelRelationByColonelRelationIds(Long[] colonelRelationIds) {
        for(Long colonelRelationId : colonelRelationIds){
            this.deleteMemColonelRelation(colonelRelationId);
        }
    }

    /**
     * 获得业务员关系
     *
     * @param colonelRelationId 主键ID
     * @return 业务员关系
     */
    @Override
    public MemColonelRelation getMemColonelRelation(Long colonelRelationId) {
        return memColonelRelationMapper.selectById(colonelRelationId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemColonelRelationRespVO> getMemColonelRelationPage(MemColonelRelationPageReqVO pageReqVO) {
        Page<MemColonelRelationPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<MemColonelRelationRespVO> respPage = memColonelRelationMapper.selectPage(pageReqVO, page);
        return new PageResult<>(respPage.getRecords(), respPage.getTotal());
    }

    private void validateMemColonelRelationExists(Long colonelRelationId) {
        if (memColonelRelationMapper.selectById(colonelRelationId) == null) {
            throw exception(MEM_COLONEL_RELATION_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 业务员关系 TODO 补充编号 ==========
    // ErrorCode MEM_COLONEL_RELATION_NOT_EXISTS = new ErrorCode(TODO 补充编号, "业务员关系不存在");


}
