package com.zksr.member.controller.colonelApp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("业务员App-业务员客户销售目标设置更改请求VO Request VO")
@Data
@ToString(callSuper = true)
public class ColonelAppBranchTargetUpdateVO {

    @ApiModelProperty(value = "主键Id", required = true)
    @NotNull(message = "业务员客户目标设置ID不能为空")
    private Long colonelBranchTargetId;

    @ApiModelProperty(value = "更改后的目标金额")
    @NotNull(message = "业务员客户目标设置 金额 不能为空")
    private String targetSalesMoney;
}
