package com.zksr.member.controller.colonelApp.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024/8/7 14:49
 * 业务员app - 业务员列表统计列表传参Vo
 */
@Data
@ApiModel("业务员app - 业务员列表统计列表传参Vo")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemColonelStatisticsListReqVo extends PageParam {
    private static final long serialVersionUID = 1L;

    /** 业务员id */
    @ApiModelProperty(value = "业务员id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;


}
