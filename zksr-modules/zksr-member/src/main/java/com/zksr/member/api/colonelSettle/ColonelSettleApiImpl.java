package com.zksr.member.api.colonelSettle;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.service.IMemColonelSettleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Slf4j
@ApiIgnore
public class ColonelSettleApiImpl implements ColonelSettleApi{

    @Autowired
    private IMemColonelSettleService memColonelSettleService;

    @Override
    public CommonResult<Boolean> colonelDaySettlementJob(Long sysCode, Date date) {
        memColonelSettleService.colonelDaySettlementJob(sysCode, date);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> colonel<PERSON><PERSON>hSettlementJob(Long sysCode, Date date) {
        memColonelSettleService.colonelMonthSettlementJob(sysCode, date, null);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> colonelSettlementJob(Long sysCode, String startDate, String endDate) {
        memColonelSettleService.colonelSettlementJob(sysCode, startDate, endDate);
        return CommonResult.success(Boolean.TRUE);
    }
}
