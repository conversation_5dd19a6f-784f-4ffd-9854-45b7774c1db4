package com.zksr.member.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.controller.relation.vo.MemColonelRelationRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.member.domain.MemColonelRelation;
import com.zksr.member.controller.relation.vo.MemColonelRelationPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;


/**
 * 业务员关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Mapper
public interface MemColonelRelationMapper extends BaseMapperX<MemColonelRelation> {
//    default PageResult<MemColonelRelation> selectPage(MemColonelRelationPageReqVO reqVO) {
//        return selectPage(reqVO, new LambdaQueryWrapperX<MemColonelRelation>()
//                    .eqIfPresent(MemColonelRelation::getColonelRelationId, reqVO.getColonelRelationId())
//                    .eqIfPresent(MemColonelRelation::getSysCode, reqVO.getSysCode())
//                    .eqIfPresent(MemColonelRelation::getAdminColonelId, reqVO.getAdminColonelId())
//                    .eqIfPresent(MemColonelRelation::getColonelId, reqVO.getColonelId())
//                .orderByDesc(MemColonelRelation::getColonelRelationId));
//    }

    /**
     * 分页查询业务员关系
     * @param reqVO
     * @param page
     * @return
     */
    Page<MemColonelRelationRespVO> selectPage(@Param("reqVO") MemColonelRelationPageReqVO reqVO, @Param("page") Page<MemColonelRelationPageReqVO> page);


    default List<MemColonelRelation> getColonelRelation(MemColonelRelationPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MemColonelRelation>()
                    .eqIfPresent(MemColonelRelation::getColonelRelationId, reqVO.getColonelRelationId())
                    .eqIfPresent(MemColonelRelation::getAdminColonelId, reqVO.getAdminColonelId())
                    .eqIfPresent(MemColonelRelation::getColonelId, reqVO.getColonelId())
                .orderByDesc(MemColonelRelation::getColonelRelationId));
    }

    /**
     * 根据业务员ID清除掉该业务员的所有关联关系
     * @param colonelId
     * @return
     */
    int deleteByColonelId(Long colonelId);

    default List<MemColonelRelation> selectByAdmin(List<Long> colonelId) {
        if (colonelId.isEmpty()) {
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapperX<MemColonelRelation>()
                .in(MemColonelRelation::getAdminColonelId, colonelId));
    }
}
