package com.zksr.member.controller.branch.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("指定业务员接收类")
public class MemBranchDirectReqVo {

    @ApiModelProperty(value = "门店id")
    private String branchId;

    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    @Excel(name = "业务员id")
    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    @ApiModelProperty(value = "区域id")
    private Long areaId;

}
