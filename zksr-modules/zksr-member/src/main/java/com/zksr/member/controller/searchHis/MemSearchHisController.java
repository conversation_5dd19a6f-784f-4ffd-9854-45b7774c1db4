package com.zksr.member.controller.searchHis;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.controller.searchHis.vo.AssociationSpuReqVO;
import com.zksr.product.api.keywords.KeywordsApi;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemSearchHis;
import com.zksr.member.service.IMemSearchHisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.searchHis.vo.MemSearchHisPageReqVO;
import com.zksr.member.controller.searchHis.vo.MemSearchHisSaveReqVO;
import com.zksr.member.controller.searchHis.vo.MemSearchHisRespVO;
import com.zksr.member.convert.searchHis.MemSearchHisConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.List;
import java.util.stream.Collectors;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 搜索历史Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Api(tags = "管理后台 - 搜索历史接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/searchHis")
public class MemSearchHisController {

    @Autowired
    private IMemSearchHisService memSearchHisService;

    @Resource
    private KeywordsApi keywordsApi;

    /**
     * 新增搜索历史
     */
    @ApiOperation(value = "新增搜索历史", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "搜索历史", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemSearchHisSaveReqVO createReqVO) {
        return success(memSearchHisService.insertMemSearchHis(createReqVO));
    }

    /**
     * 修改搜索历史
     */
    @ApiOperation(value = "修改搜索历史", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "搜索历史", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemSearchHisSaveReqVO updateReqVO) {
        memSearchHisService.updateMemSearchHis(updateReqVO);
        return success(true);
    }

    /**
     * 删除搜索历史
     */
    @ApiOperation(value = "删除搜索历史", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "搜索历史", businessType = BusinessType.DELETE)
    @DeleteMapping("/{searchHisIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] searchHisIds) {
        memSearchHisService.deleteMemSearchHisBySearchHisIds(searchHisIds);
        return success(true);
    }

    /**
     * 获取搜索历史详细信息
     */
    @ApiOperation(value = "获得搜索历史详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{searchHisId}")
    public CommonResult<MemSearchHisRespVO> getInfo(@PathVariable("searchHisId") Long searchHisId) {
        MemSearchHis memSearchHis = memSearchHisService.getMemSearchHis(searchHisId);
        return success(MemSearchHisConvert.INSTANCE.convert(memSearchHis));
    }

    /**
     * 分页查询搜索历史
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得搜索历史分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemSearchHisRespVO>> getPage(@Valid MemSearchHisPageReqVO pageReqVO) {
        return success(memSearchHisService.getMemSearchHisPage(pageReqVO));
    }

    /**
     * 一键加入词库
     */
    @ApiOperation(value = "一键加入词库", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "一键加入词库", businessType = BusinessType.INSERT)
    @PostMapping("/addKeywords")
    public CommonResult<Boolean> addKeywords(@RequestBody List<String> keywords) {
        keywordsApi.batchInsertKeywords(SecurityUtils.getLoginUser().getSysCode(), keywords);
        return success(true);
    }

    /**
     * 批量关联spu
     */
    @ApiOperation(value = "批量关联spu", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "批量关联spu", businessType = BusinessType.INSERT)
    @PostMapping("/batchAssociationSpu")
    public CommonResult<Boolean> batchAssociationSpu(@Valid AssociationSpuReqVO associationSpuReqVO) {
//        List<Long> spuIdLongs = spuIds.stream()
//                .map(Long::parseLong)
//                .collect(Collectors.toList());
        return success(memSearchHisService.batchAssociationSpu(associationSpuReqVO.getKeywords(), associationSpuReqVO.getSpuIds(), 0));
    }

    /**
     * 批量取消关联spu
     */
    @ApiOperation(value = "批量取消关联spu", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "批量取消关联spu", businessType = BusinessType.INSERT)
    @PostMapping("/batchCancelAssociationSpu")
    public CommonResult<Boolean> batchCancelAssociationSpu(@Valid AssociationSpuReqVO associationSpuReqVO) {
        return success(memSearchHisService.batchAssociationSpu(associationSpuReqVO.getKeywords(), associationSpuReqVO.getSpuIds(), 1));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "member:searchHis:add";
        /**
         * 编辑
         */
        public static final String EDIT = "member:searchHis:edit";
        /**
         * 删除
         */
        public static final String DELETE = "member:searchHis:remove";
        /**
         * 列表
         */
        public static final String LIST = "member:searchHis:list";
        /**
         * 查询
         */
        public static final String GET = "member:searchHis:query";
        /**
         * 停用
         */
        public static final String DISABLE = "member:searchHis:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "member:searchHis:enable";
    }
}
