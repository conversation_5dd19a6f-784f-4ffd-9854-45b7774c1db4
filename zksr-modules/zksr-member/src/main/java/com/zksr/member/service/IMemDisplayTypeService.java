package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.domain.MemDisplayType;
import com.zksr.member.controller.displayType.vo.MemDisplayTypePageReqVO;
import com.zksr.member.controller.displayType.vo.MemDisplayTypeSaveReqVO;

/**
 * 陈列类型Service接口
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
public interface IMemDisplayTypeService {

    /**
     * 新增陈列类型
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemDisplayType(@Valid MemDisplayTypeSaveReqVO createReqVO);

    /**
     * 修改陈列类型
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemDisplayType(@Valid MemDisplayTypeSaveReqVO updateReqVO);

    /**
     * 删除陈列类型
     *
     * @param displayId 陈列类型编号，主键
     */
    public void deleteMemDisplayType(Long displayId);

    /**
     * 批量删除陈列类型
     *
     * @param displayIds 需要删除的陈列类型主键集合
     * @return 结果
     */
    public void deleteMemDisplayTypeByDisplayIds(Long[] displayIds);

    /**
     * 获得陈列类型
     *
     * @param displayId 陈列类型编号，主键
     * @return 陈列类型
     */
    public MemDisplayType getMemDisplayType(Long displayId);

    /**
     * 获得陈列类型分页
     *
     * @param pageReqVO 分页查询
     * @return 陈列类型分页
     */
    PageResult<MemDisplayType> getMemDisplayTypePage(MemDisplayTypePageReqVO pageReqVO);

}
