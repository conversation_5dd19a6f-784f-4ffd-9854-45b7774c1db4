package com.zksr.member.controller.colonelApp.vo;

import com.zksr.common.core.enums.PayChannelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员绑定小程序
 * @date 2024/9/24 10:01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "业务员绑定小程序状态 response VO")
public class ColonelBindAppletOpenidRespVO {

    @ApiModelProperty("true-已绑定, false-未绑定")
    private boolean wxB2bind;

    @ApiModelProperty("绑定key, 有效期30分钟, 1800秒")
    private String bindKey;

    @ApiModelProperty("跳转的appid")
    private String appid;

    /**
     * 支付平台 参见 {@link PayChannelEnum}
     */
    @ApiModelProperty("支付平台: mock-模拟支付, hlb-合利宝, mideaPay-美的支付, wxb2b-微信B2B支付(强制要求绑定)")
    private String platform;

    @ApiModelProperty("微信昵称")
    private String nickName;

    @ApiModelProperty("微信头像")
    private String profile;
}
