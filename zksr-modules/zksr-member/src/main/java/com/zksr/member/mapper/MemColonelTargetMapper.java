package com.zksr.member.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.controller.colonelApp.vo.ColonelAppTargetPageVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelTargetRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemColonelTarget;
import com.zksr.member.controller.colonelTarget.vo.MemColonelTargetPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


/**
 * 业务员目标设置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Mapper
public interface MemColonelTargetMapper extends BaseMapperX<MemColonelTarget> {
//    default PageResult<MemColonelTarget> selectPage(MemColonelTargetPageReqVO reqVO) {
//        return selectPage(reqVO, new LambdaQueryWrapperX<MemColonelTarget>()
//                    .eqIfPresent(MemColonelTarget::getColonelTargetId, reqVO.getColonelTargetId())
//                    .eqIfPresent(MemColonelTarget::getColonelId, reqVO.getColonelId())
//                    .eqIfPresent(MemColonelTarget::getTargetYear, reqVO.getTargetYear())
//                    .eqIfPresent(MemColonelTarget::getTargetMonth, reqVO.getTargetMonth())
//                    .eqIfPresent(MemColonelTarget::getSalesMoney, reqVO.getSalesMoney())
//                    .eqIfPresent(MemColonelTarget::getMonthNewCustomer, reqVO.getMonthNewCustomer())
//                    .eqIfPresent(MemColonelTarget::getMonthActivityCustomer, reqVO.getMonthActivityCustomer())
//                    .eqIfPresent(MemColonelTarget::getMonthVisitCustomer, reqVO.getMonthVisitCustomer())
//                    .eqIfPresent(MemColonelTarget::getMemo, reqVO.getMemo())
//                    .eqIfPresent(MemColonelTarget::getStatus, reqVO.getStatus())
//                .orderByDesc(MemColonelTarget::getColonelTargetId));
//    }

    Page<MemColonelTargetRespVO> selectPage(@Param("reqVO") MemColonelTargetPageReqVO reqVO, @Param("page") Page<MemColonelTargetPageReqVO> page);

    /**
     * 根据业务员ID和目标年月查询业务员目标设置
     * @param colonelIds 业务员ID集合
     * @param targetYear 年（YYYY）
     * @param targetMonth 月（MM）
     * @param status  0: 保存状态 1：设置完成
     * @return
     */
    default List<MemColonelTarget> selectListByColonelIdsAndYearMonth(List<Long> colonelIds, String targetYear, String targetMonth, Integer status) {
        return selectList(new LambdaQueryWrapperX<MemColonelTarget>()
                .inIfPresent(MemColonelTarget::getColonelId, colonelIds)
                .eqIfPresent(MemColonelTarget::getTargetYear, targetYear)
                .eqIfPresent(MemColonelTarget::getTargetMonth, targetMonth)
                .eqIfPresent(MemColonelTarget::getStatus, status));
    }


    BigDecimal getColonelMonthlySalesTarget(@Param("colonelId") Long colonelId, @Param("targetYear") int targetYear, @Param("targetMonth")int targetMonth);
    /**
     * 根据业务员ID和目标年月查询业务员目标设置
     * @param colonelIds 业务员ID集合
     * @param reqVO 查询条件实体
     * @return
     */
    List<MemColonelTarget> selectListByColonelIdsAndParams(@Param("colonelIds") List<Long> colonelIds, @Param("reqVO") ColonelAppTargetPageVO reqVO);

    /**
     * 获取业务员目标总计
     * @param colonelList   业务员列表
     * @param year          年
     * @param month         余额
     * @return  目标总计
     */
    BigDecimal selectTargetMonthByColonelId(@Param("colonelList") List<Long> colonelList, @Param("year") String year, @Param("month") String month);
}
