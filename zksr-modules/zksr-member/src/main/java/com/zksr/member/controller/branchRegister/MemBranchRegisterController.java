package com.zksr.member.controller.branchRegister;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.member.controller.memberRegister.MemMemberRegisterController;
import com.zksr.member.service.IMemBranchService;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemBranchRegister;
import com.zksr.member.service.IMemBranchRegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterPageReqVO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterSaveReqVO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterRespVO;
import com.zksr.member.convert.branchRegister.MemBranchRegisterConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 门店注册信息Controller
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@Api(tags = "管理后台 - 门店注册信息接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/branchRegister")
public class MemBranchRegisterController {
    @Autowired
    private IMemBranchRegisterService memBranchRegisterService;

    @Autowired
    private IMemBranchService memBranchService;

    /**
     * 新增门店注册信息
     */
    @ApiOperation(value = "新增门店注册信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "门店注册信息", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemBranchRegisterSaveReqVO createReqVO) {
        return success(memBranchRegisterService.insertMemBranchRegister(createReqVO).getBranchRegisterId());
    }

    /**
     * 修改门店注册信息
     */
    @ApiOperation(value = "修改门店注册信息", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "门店注册信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemBranchRegisterSaveReqVO updateReqVO) {
            memBranchRegisterService.updateMemBranchRegister(updateReqVO);
        return success(true);
    }

    /**
     * 删除门店注册信息
     */
    @ApiOperation(value = "删除门店注册信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "门店注册信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{branchRegisterIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] branchRegisterIds) {
        memBranchRegisterService.deleteMemBranchRegisterByBranchRegisterIds(branchRegisterIds);
        return success(true);
    }

    /**
     * 获取门店注册信息详细信息
     */
    @ApiOperation(value = "获得门店注册信息详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{branchRegisterId}")
    public CommonResult<MemBranchRegisterRespVO> getInfo(@PathVariable("branchRegisterId") Long branchRegisterId) {
        MemBranchRegister memBranchRegister = memBranchRegisterService.getMemBranchRegister(branchRegisterId);
        return success(MemBranchRegisterConvert.INSTANCE.convert(memBranchRegister));
    }

    /**
     * 分页查询门店注册信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得门店注册信息分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(dcAlias = "`mem_branch_register`")
    public CommonResult<PageResult<MemBranchRegisterPageReqVO>> getPage(@Valid MemBranchRegisterPageReqVO pageReqVO) {
        return success(memBranchRegisterService.getMemBranchRegisterPage(pageReqVO));
    }

    /**
     * 审核门店注册信息
     */
    @ApiOperation(value = "审核门店注册信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.AUDIT)
    @RequiresPermissions(Permissions.AUDIT)
    @Log(title = "审核用户注册信息", businessType = BusinessType.OTHER)
    @PostMapping("/auditBranchRegister")
    public CommonResult<Boolean> auditBranchRegister(@RequestBody Long[] branchRegisterIds) {
        List<Long> branchIdList = memBranchRegisterService.batchAuditBranchRegister(branchRegisterIds);
        branchIdList.forEach(memBranchService::reloadBranchDTOCache);
        return success(true);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:branchRegister:add";
        /** 编辑 */
        public static final String EDIT = "member:branchRegister:edit";
        /** 删除 */
        public static final String DELETE = "member:branchRegister:remove";
        /** 列表 */
        public static final String LIST = "member:branchRegister:list";
        /** 查询 */
        public static final String GET = "member:branchRegister:query";
        /** 停用 */
        public static final String DISABLE = "member:branchRegister:disable";
        /** 启用 */
        public static final String ENABLE = "member:branchRegister:enable";
        /** 审核 */
        public static final String AUDIT = "member:branchRegister:audit";
    }
}
