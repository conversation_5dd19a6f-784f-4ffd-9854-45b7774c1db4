package com.zksr.member.convert.colonel;

import com.zksr.member.api.colonelApp.dto.PageDataDTO;
import com.zksr.trade.api.order.dto.ColonelOrderDaySettleDTO;
import com.zksr.member.domain.MemColonelDaySettle;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/8/8
 * @desc 业务员日结表对象封装转换器
 */
@Mapper
public interface MemberColonelDaySettleConvert {

    MemberColonelDaySettleConvert INSTANCE = Mappers.getMapper(MemberColonelDaySettleConvert.class);

    @Mappings({
            @Mapping(source = "pageDataDTO.colonelId", target = "colonelId"),
            @Mapping(source = "pageDataDTO.orderQty", target = "orderQty"),
            @Mapping(source = "pageDataDTO.percentageAmt", target = "percentageAmt"),
            @Mapping(source = "pageDataDTO.branchOrderAmt", target = "branchOrderAmt"),
            @Mapping(source = "pageDataDTO.branchRefundAmt", target = "branchRefundAmt"),
            @Mapping(source = "pageDataDTO.visitQty", target = "visitQty"),
            @Mapping(source = "pageDataDTO.addBranchQty", target = "addBranchQty"),
            @Mapping(source = "pageDataDTO.saleBranchQty", target = "saleBranchQty"),
            @Mapping(source = "pageDataDTO.businessOrderQty", target = "businessOrderQty"),
            @Mapping(source = "pageDataDTO.businessOrderAmt", target = "businessOrderAmt")
    })
    @BeanMapping(ignoreByDefault = true)
    void convert(@MappingTarget MemColonelDaySettle colonelDaySettle, PageDataDTO pageDataDTO);


    MemColonelDaySettle convert(ColonelOrderDaySettleDTO dataDTO);

}
