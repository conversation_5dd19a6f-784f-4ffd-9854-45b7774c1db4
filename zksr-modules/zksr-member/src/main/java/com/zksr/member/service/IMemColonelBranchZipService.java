package com.zksr.member.service;

import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelBranchZipRespVO;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.domain.MemColonelBranchZip;

/**
 * 门店业务员关系拉链表 Service接口
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
public interface IMemColonelBranchZipService {
    void insertMemColonelBranchZip(MemBranchSaveReqVO reqVO, MemBranch memBranch);

    MemColonelBranchZipRespVO selectMemColonelBranchZip(MemBranch memBranch);

    void updateMemColonelBranchZip(MemBranchSaveReqVO reqVO, MemBranch memBranch);
}
