package com.zksr.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.colonel.ColonelApiImpl;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.controller.branch.vo.MemBranchPageReqVO;
import com.zksr.member.controller.branch.vo.MemBranchRespVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelBranchZipRespVO;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.domain.MemColonelBranchZip;
import com.zksr.member.mapper.MemColonelBranchZipMapper;
import com.zksr.member.service.IMemColonelBranchZipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 门店业务员关系拉链表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Service
public class MemColonelBranchZipServiceImpl implements IMemColonelBranchZipService {
    @Autowired
    private MemColonelBranchZipMapper memColonelBranchZipMapper;
    @Autowired
    private ColonelApiImpl colonelApi;
    @Override
    @Transactional
    public void insertMemColonelBranchZip(MemBranchSaveReqVO reqVO, MemBranch memBranch) {
         //获取时间
        Date nowDate = DateUtils.getNowDate();
        Date endOfYearDate = DateUtils.toDate(LocalDateTime.of(2099, 12, 30, 23, 59, 59));
        MemColonelBranchZip currentRelation = null;
        if(ToolUtil.isNotEmpty(memBranch)){
            // 查询当前门店与业务员的关联关系
            currentRelation = memColonelBranchZipMapper.selectMemColonelBranchZipByBranchId(memBranch);
        }
        if(ToolUtil.isEmpty(currentRelation)){
            MemColonelBranchZip newRelation = new MemColonelBranchZip();
            newRelation.setSysCode(reqVO.getSysCode());
            newRelation.setBranchId(reqVO.getBranchId());
            newRelation.setColonelId(reqVO.getColonelId());
            newRelation.setStartDate(nowDate);
            newRelation.setEndDate(endOfYearDate);
            memColonelBranchZipMapper.insert(newRelation);
        }else{
            if(Objects.equals(currentRelation.getSysCode(), memBranch.getSysCode())
            && Objects.equals(currentRelation.getBranchId(), reqVO.getBranchId())
            && !Objects.equals(currentRelation.getColonelId(), reqVO.getColonelId())){
                // 更新当前关联关系的结束时间
                currentRelation.setEndDate(nowDate);
                memColonelBranchZipMapper.updateMemColonelBranchZip(currentRelation);

                MemColonelBranchZip newMemColonelBranchZip = HutoolBeanUtils.toBean(currentRelation, MemColonelBranchZip.class);
                newMemColonelBranchZip.setColonelBranchZipId(null);
                newMemColonelBranchZip.setCreateTime(nowDate);
                newMemColonelBranchZip.setStartDate(nowDate);
                newMemColonelBranchZip.setEndDate(endOfYearDate);
                newMemColonelBranchZip.setColonelId(reqVO.getColonelId());
                memColonelBranchZipMapper.insert(newMemColonelBranchZip);
            }
        }
    }

    /**
     * 获取门店上任业务员
     * @param memBranch
     * @return
     */
    @Override
    public MemColonelBranchZipRespVO selectMemColonelBranchZip(MemBranch memBranch) {
        MemColonelBranchZip memColonelBranchZip = memColonelBranchZipMapper.selectMemColonelBranchZipByBranchId(memBranch);
        MemColonelBranchZipRespVO memColonelBranchZipRespVO = new MemColonelBranchZipRespVO();
        if (Objects.nonNull(memColonelBranchZip)){
            BeanUtils.copyProperties(memColonelBranchZip, memColonelBranchZipRespVO);
            ColonelDTO colonel = colonelApi.getByColonelId(memColonelBranchZip.getColonelId()).getData();
            if (Objects.nonNull(colonel)){
                memColonelBranchZipRespVO.setColonelName(colonel.getColonelName());
            }
        }
        return memColonelBranchZipRespVO;
    }

    @Override
    @Transactional
    public void updateMemColonelBranchZip(MemBranchSaveReqVO reqVO, MemBranch memBranch) {
        //获取时间
        Date nowDate = DateUtils.getNowDate();
        Date endOfYearDate = DateUtils.toDate(LocalDateTime.of(2099, 12, 30, 23, 59, 59));
        MemColonelBranchZip currentRelation = null;
        if(ToolUtil.isNotEmpty(memBranch)){
            // 查询当前门店与业务员的关联关系
            currentRelation = memColonelBranchZipMapper.selectMemColonelBranchZipByBranchId(memBranch);
        }
        if(ToolUtil.isNotEmpty(currentRelation) && Objects.equals(currentRelation.getSysCode(), memBranch.getSysCode())
                && Objects.equals(currentRelation.getBranchId(), reqVO.getBranchId())){
            // 更新当前关联关系的结束时间
            currentRelation.setEndDate(nowDate);
            memColonelBranchZipMapper.updateMemColonelBranchZip(currentRelation);
        }
    }
}
