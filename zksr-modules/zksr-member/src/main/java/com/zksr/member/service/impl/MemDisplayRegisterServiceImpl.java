package com.zksr.member.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemDisplayRegisterMapper;
import com.zksr.member.domain.MemDisplayRegister;
import com.zksr.member.controller.displayRegister.vo.MemDisplayRegisterPageReqVO;
import com.zksr.member.controller.displayRegister.vo.MemDisplayRegisterSaveReqVO;
import com.zksr.member.service.IMemDisplayRegisterService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 陈列计划打卡记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Service
public class MemDisplayRegisterServiceImpl implements IMemDisplayRegisterService {
    @Autowired
    private MemDisplayRegisterMapper memDisplayRegisterMapper;

    /**
     * 新增陈列计划打卡记录
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemDisplayRegister(MemDisplayRegisterSaveReqVO createReqVO) {
        // 插入
        MemDisplayRegister memDisplayRegister = HutoolBeanUtils.toBean(createReqVO, MemDisplayRegister.class);
        memDisplayRegisterMapper.insert(memDisplayRegister);
        // 返回
        return memDisplayRegister.getRegisterId();
    }

    /**
     * 修改陈列计划打卡记录
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemDisplayRegister(MemDisplayRegisterSaveReqVO updateReqVO) {
        memDisplayRegisterMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, MemDisplayRegister.class));
    }

    /**
     * 删除陈列计划打卡记录
     *
     * @param registerId 主键ID
     */
    @Override
    public void deleteMemDisplayRegister(Long registerId) {
        // 删除
        memDisplayRegisterMapper.deleteById(registerId);
    }

    /**
     * 批量删除陈列计划打卡记录
     *
     * @param registerIds 需要删除的陈列计划打卡记录主键
     * @return 结果
     */
    @Override
    public void deleteMemDisplayRegisterByRegisterIds(Long[] registerIds) {
        for(Long registerId : registerIds){
            this.deleteMemDisplayRegister(registerId);
        }
    }

    /**
     * 获得陈列计划打卡记录
     *
     * @param registerId 主键ID
     * @return 陈列计划打卡记录
     */
    @Override
    public MemDisplayRegister getMemDisplayRegister(Long registerId) {
        return memDisplayRegisterMapper.selectById(registerId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemDisplayRegister> getMemDisplayRegisterPage(MemDisplayRegisterPageReqVO pageReqVO) {
        return memDisplayRegisterMapper.selectPage(pageReqVO);
    }

    private void validateMemDisplayRegisterExists(Long registerId) {
        if (memDisplayRegisterMapper.selectById(registerId) == null) {
            throw exception(MEM_DISPLAY_REGISTER_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 陈列计划打卡记录 TODO 补充编号 ==========
    // ErrorCode MEM_DISPLAY_REGISTER_NOT_EXISTS = new ErrorCode(TODO 补充编号, "陈列计划打卡记录不存在");


}
