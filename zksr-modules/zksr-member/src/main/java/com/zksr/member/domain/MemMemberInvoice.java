package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 用户发票对象 mem_member_invoice
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@TableName(value = "mem_member_invoice")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemMemberInvoice extends BaseEntity {
    private static final long serialVersionUID=1L;

    /** ID主键 */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 发票类型,10电子普通发票20专用发票 */
    @Excel(name = "发票类型,10电子普通发票20专用发票")
    private Long invoiceType;

    /** 发票抬头类型,10个人20单位 */
    @Excel(name = "发票抬头类型,10个人20单位")
    private Long titleType;

    /** 发票抬头 */
    @Excel(name = "发票抬头")
    private String invoiceTitle;

    /** 纳税人识别码 */
    @Excel(name = "纳税人识别码")
    private String taxpayerCode;

    /** 单位地址 */
    @Excel(name = "单位地址")
    private String companyAddress;

    /** 单位电话 */
    @Excel(name = "单位电话")
    private String companyPhone;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 开户银行 */
    @Excel(name = "开户银行")
    private String bankName;

    /** 银行账户 */
    @Excel(name = "银行账户")
    private String bankAccount;

    /** 是否删除：0-否，1-是 */
    @TableLogic
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 备注 */
    private String remark;

}
