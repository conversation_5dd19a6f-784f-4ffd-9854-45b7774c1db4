package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 操作指令对象 mem_command
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@TableName(value = "mem_command")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MemCommand extends BaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long commandId;

    /** 平台商id */
    @Excel(name = "平台商id")
    private Long sysCode;

    /** 0-指令锚点，1-普通指令 */
    @Excel(name = "0-指令锚点，1-普通指令")
    private Integer commandLevel;

    /** 指令锚点id，仅普通指令 */
    @Excel(name = "指令锚点id，仅普通指令")
    private Long pid;

    /** 指令有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "指令有效期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date commandDate;

    /** 指令类型（数据字典）(加单、拜访等) */
    @Excel(name = "指令类型", readConverterExp = "数=据字典")
    private Integer commandType;

    /** 0-作废 1-进行中  2-完成  */
    @Excel(name = "0-作废 1-进行中  2-完成 ")
    private Integer status;

    /** 指令发布角色 colonel-业务员 */
    @Excel(name = "指令发布角色 colonel-业务员")
    private String pubMerchantType;

    /** 指令发布人id */
    @Excel(name = "指令发布人id")
    private Long pubId;

    /** 指令执行角色 branch-门店 */
    @Excel(name = "指令执行角色 branch-门店")
    private String execMerchantType;

    /** 指令执行人id */
    @Excel(name = "指令执行人id")
    private Long execId;

    /** 执行结果 */
    @Excel(name = "执行结果")
    private String execRes;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;


}
