package com.zksr.member.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemDisplayPlanCoupon;
import com.zksr.member.mapper.MemDisplayPlanCouponMapper;
import com.zksr.member.service.IMemDisplayPlanCouponService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemDisplayPlanMapper;
import com.zksr.member.domain.MemDisplayPlan;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanPageReqVO;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanSaveReqVO;
import com.zksr.member.service.IMemDisplayPlanService;

import javax.swing.text.StyledEditorKit;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 陈列计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Service
public class MemDisplayPlanServiceImpl implements IMemDisplayPlanService {
    @Autowired
    private MemDisplayPlanMapper memDisplayPlanMapper;
    @Autowired
    private IMemDisplayPlanCouponService iMemDisplayPlanCouponServiceImpl;

    /**
     * 新增陈列计划
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @DSTransactional
    @Override
    public Long insertMemDisplayPlan(MemDisplayPlanSaveReqVO createReqVO) {
        // 插入
        MemDisplayPlan memDisplayPlan = HutoolBeanUtils.toBean(createReqVO, MemDisplayPlan.class);
        if (StringUtils.isEmpty(createReqVO.getSubList())) throw exception(MEM_DISPLAY_PLAN_COUPON_NOT_EXISTS);
        memDisplayPlanMapper.insert(memDisplayPlan);
        createReqVO.getSubList().forEach(entry -> {
            entry.setPlanId(memDisplayPlan.getPlanId());  //绑定主键值
            iMemDisplayPlanCouponServiceImpl.insertMemDisplayPlanCoupon(entry);
        });
        // 返回
        return memDisplayPlan.getPlanId();
    }

    /**
     * 修改陈列计划
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemDisplayPlan(MemDisplayPlanSaveReqVO updateReqVO) {
        memDisplayPlanMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, MemDisplayPlan.class));
    }

    /**
     * 删除陈列计划
     *
     * @param planId 陈列计划编号，主键
     */
    @Override
    public void deleteMemDisplayPlan(Long planId) {
        // 删除
        memDisplayPlanMapper.deleteById(planId);
    }

    /**
     * 批量删除陈列计划
     *
     * @param planIds 需要删除的陈列计划主键
     * @return 结果
     */
    @Override
    public void deleteMemDisplayPlanByPlanIds(Long[] planIds) {
        for(Long planId : planIds){
            this.deleteMemDisplayPlan(planId);
        }
    }

    /**
     * 获得陈列计划
     *
     * @param planId 陈列计划编号，主键
     * @return 陈列计划
     */
    @Override
    public MemDisplayPlan getMemDisplayPlan(Long planId) {
        return memDisplayPlanMapper.selectById(planId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemDisplayPlan> getMemDisplayPlanPage(MemDisplayPlanPageReqVO pageReqVO) {
        return memDisplayPlanMapper.selectPage(pageReqVO);
    }

    private void validateMemDisplayPlanExists(Long planId) {
        if (memDisplayPlanMapper.selectById(planId) == null) {
            throw exception(MEM_DISPLAY_PLAN_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 陈列计划 TODO 补充编号 ==========
    // ErrorCode MEM_DISPLAY_PLAN_NOT_EXISTS = new ErrorCode(TODO 补充编号, "陈列计划不存在");


}
