package com.zksr.member.service.impl;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.CouponReceiveType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.utils.DcUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.branchRegister.dto.BranchRegisterDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonelApp.dto.PageDataReqDTO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterPageReqVO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterSaveReqVO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterSaveResp;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterSaveReqVO;
import com.zksr.member.convert.branchRegister.MemBranchRegisterConvert;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.domain.MemBranchRegister;
import com.zksr.member.domain.MemBranchUser;
import com.zksr.member.domain.MemMember;
import com.zksr.member.mapper.MemBranchRegisterMapper;
import com.zksr.member.mapper.MemBranchUserMapper;
import com.zksr.member.mapper.MemColonelMapper;
import com.zksr.member.mapper.MemMemberMapper;
import com.zksr.member.mq.MemberAppMqProducer;
import com.zksr.member.service.*;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.api.coupon.vo.NormalCouponReceiveSingleAsyncReqVo;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.commonMessage.SubscribeApi;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.DcDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

import static com.zksr.common.core.constant.UserConstants.DEFAULT_PASSWORD;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.constant.MemberConstant.COLONEL_APP_Page_DATA_TYPE_2;
import static com.zksr.member.enums.ErrorCodeConstants.MEM_BRANCH_REGISTER_NOT_EXISTS;

/**
 * 门店注册信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@Service
@SuppressWarnings("all")
public class MemBranchRegisterServiceImpl implements IMemBranchRegisterService {
    @Autowired
    private MemBranchRegisterMapper memBranchRegisterMapper;

    @Autowired
    private IMemMemberService memMemberService;

    @Autowired
    private AreaApi remoteAreaApi;

    @Autowired
    private DcApi remoteDcApi;

    @Autowired
    private MemColonelMapper memColonelMapper;

    @Autowired
    private IMemberCacheService memberCacheServiceImpl;

    @Autowired
    private MemberAppMqProducer memberAppMqProducer;

    @Autowired
    private MemMemberMapper memMemberMapper;

    @Resource
    private CouponApi couponService;

    @Autowired
    private MemBranchUserMapper memBranchUserMapper;

    @Resource
    private SubscribeApi subscribeApi;
    @Resource
    private MemBranchServiceImpl memBranchService;



    /**
     * 新增门店注册信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemBranchRegisterSaveResp insertMemBranchRegister(MemBranchRegisterSaveReqVO createReqVO) {
        MemBranch memBranch = HutoolBeanUtils.toBean(createReqVO, MemBranch.class);
        String s = memBranchService.branchVerify(memBranch, null);
        if (StringUtils.isNotEmpty(s)){
            throw new ServiceException(s);
        }

        // 插入
        MemBranchRegister memBranchRegister = HutoolBeanUtils.toBean(createReqVO, MemBranchRegister.class);
        memBranchRegisterMapper.insert(memBranchRegister);
        createReqVO.setBranchRegisterId(memBranchRegister.getBranchRegisterId());

        MemBranchRegisterSaveResp saveResp = MemBranchRegisterSaveResp
                .builder()
                .branchRegisterId(memBranchRegister.getBranchRegisterId())
                .build();

        //如果是门店自动审核 则进行审核
        if (createReqVO.getBranchApproveFlag() == StatusConstants.REGISTER_APPROVE_FLAG_1) {
            //组装注册数据 新增用户和门店
            Long branchId = insertUserBranchByApprove(createReqVO);
            saveResp.setBranchId(branchId);
            if(ToolUtil.isNotEmpty(createReqVO.getColonelId()) && ToolUtil.isNotEmpty(branchId)){
                MemBranchSaveReqVO memBranchSaveReqVO = new MemBranchSaveReqVO();
                memBranchSaveReqVO.setBranchId(branchId);
                memBranchSaveReqVO.setColonelId(createReqVO.getColonelId());
                memBranchSaveReqVO.setSysCode(createReqVO.getSysCode());
                memColonelBranchZipService.insertMemColonelBranchZip(memBranchSaveReqVO,null);
            }

            //审核
            memBranchRegisterMapper.branchRegisterApprove(Arrays.asList(memBranchRegister.getBranchRegisterId()), null);
            processCouponForBranchRegistration(branchId, createReqVO.getSysCode(),memBranchRegister.getAreaId());
        }

        // 返回
        return saveResp;
    }

    /**
     * 修改门店注册信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMemBranchRegister(MemBranchRegisterSaveReqVO updateReqVO) {
        memBranchRegisterMapper.updateById(MemBranchRegisterConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除门店注册信息
     *
     * @param branchRegisterId 门店注册信息ID
     */
    @Override
    public void deleteMemBranchRegister(Long branchRegisterId) {
        // 删除
        memBranchRegisterMapper.deleteById(branchRegisterId);
    }

    /**
     * 批量删除门店注册信息
     *
     * @param branchRegisterIds 需要删除的门店注册信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMemBranchRegisterByBranchRegisterIds(Long[] branchRegisterIds) {
        memBranchRegisterMapper.deleteMemBranchRegisterByBranchRegisterIds(branchRegisterIds);
    }

    /**
     * 获得门店注册信息
     *
     * @param branchRegisterId 门店注册信息ID
     * @return 门店注册信息
     */
    @Override
    public MemBranchRegister getMemBranchRegister(Long branchRegisterId) {
        return memBranchRegisterMapper.selectById(branchRegisterId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<MemBranchRegisterPageReqVO> getMemBranchRegisterPage(MemBranchRegisterPageReqVO pageReqVO) {
        pageReqVO.setAreaIdList(DcUtils.getAreaList(SecurityUtils.getDcId()));
        PageResult<MemBranchRegister> result = memBranchRegisterMapper.selectPage(pageReqVO);

        PageResult<MemBranchRegisterPageReqVO> pageResult = new PageResult<>(HutoolBeanUtils.toBean(result.getList(), MemBranchRegisterPageReqVO.class), result.getTotal());
        if (ToolUtil.isNotEmpty(pageResult.getList())) {
            //匹配分页数据中的ID对应信息
            pageResult.getList().forEach(branchRegister -> {
                if(null != branchRegister.getAreaId()){
                    AreaDTO areaDTO = memberCacheServiceImpl.getAreaDto(branchRegister.getAreaId());
                    if (ToolUtil.isNotEmpty(areaDTO)) {
                        branchRegister.setAreaName(areaDTO.getAreaName());
                    }
                }
                if(null != branchRegister.getApproveMan()){
                    CommonResult<DcDTO> dcResult = remoteDcApi.getDcById(branchRegister.getApproveMan());
                    if (dcResult.isSuccess()) {
                        DcDTO dcDTO = dcResult.getCheckedData();
                        if (ToolUtil.isNotEmpty(dcDTO)) {
                            branchRegister.setApproveManName(dcDTO.getDcName());
                        }
                    }
                }

                if (ToolUtil.isNotEmpty(branchRegister.getColonelId())) {
                    ColonelDTO colonelDTO = memberCacheServiceImpl.getColonel(branchRegister.getColonelId());
                    if (ToolUtil.isNotEmpty(colonelDTO)) {
                        branchRegister.setColonelName(colonelDTO.getColonelName());
                    }
                }

                if (ToolUtil.isNotEmpty(branchRegister.getChannelId())) {
                    ChannelDTO channelDto = memberCacheServiceImpl.getChannelDto(branchRegister.getChannelId());
                    if (ToolUtil.isNotEmpty(channelDto)) {
                        branchRegister.setChannelName(channelDto.getChannelName());
                    }
                }

            });
        }
        return pageResult;
    }

    @Override
    public MemBranchRegister getBranchRegisterById(Long branchRegisterId) {
        return memBranchRegisterMapper.selectById(branchRegisterId);
    }
    @Autowired
    private IMemColonelBranchZipService memColonelBranchZipService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchAuditBranchRegister(Long[] branchRegisterIds) {
        ArrayList<Long> branchIdList = new ArrayList<>();
        List<Long> ids = Arrays.asList(branchRegisterIds);
        //查询门店注册信息列表
        List<MemBranchRegister> memBranchRegisters = memBranchRegisterMapper.selectBatchIds(ids);
        List<MemBranchRegisterSaveReqVO> saveVoList = HutoolBeanUtils.toBean(memBranchRegisters, MemBranchRegisterSaveReqVO.class);

        Map<Long, MemBranchRegisterSaveReqVO>  map = new HashMap<>();
        //生成用户及门店信息
        saveVoList.forEach(saveReqVO -> {
            Long branchId = insertUserBranchByApprove(saveReqVO);
            branchIdList.add(branchId);
            map.put(branchId,saveReqVO);
        });

        Long userId = SecurityUtils.getUserId();
        //审核
        memBranchRegisterMapper.branchRegisterApprove(ids, userId);


        //门店审核后 循环审核门店ID给门店添加优惠券
        for (Long branchId : branchIdList) {
            processCouponForBranchRegistration(branchId, map.get(branchId).getSysCode(),map.get(branchId).getAreaId());
        }

        for (MemBranchRegisterSaveReqVO memBranchRegisterSaveReqVO : saveVoList) {
            if(ToolUtil.isNotEmpty(memBranchRegisterSaveReqVO.getColonelId())){
                MemBranchSaveReqVO memBranchSaveReqVO = new MemBranchSaveReqVO();
                memBranchSaveReqVO.setBranchId(memBranchRegisterSaveReqVO.getBranchRegisterId());
                memBranchSaveReqVO.setColonelId(memBranchRegisterSaveReqVO.getColonelId());
                memBranchSaveReqVO.setSysCode(memBranchRegisterSaveReqVO.getSysCode());
                memColonelBranchZipService.insertMemColonelBranchZip(memBranchSaveReqVO,null);
            }
        }
        return branchIdList;
    }

    /**
     * @Description: 商城小程序添加门店
     * @Author: liuxingyu
     * @Date: 2024/6/7 16:45
     */
    @Override
    public Long addBranch(BranchRegisterDTO registerDTO) {
        //添加门店注册信息
        //获取用户信息
        MemMember memMember = memMemberMapper.selectById(registerDTO.getMemberId());
        registerDTO.setContactName(memMember.getMemberName());
        registerDTO.setContactPhone(memMember.getMemberPhone());
        MemBranchRegisterSaveReqVO saveReqVO = HutoolBeanUtils.toBean(registerDTO, MemBranchRegisterSaveReqVO.class);
        return insertMemBranchRegister(saveReqVO).getBranchRegisterId();
    }

    @Override
    public List<MemBranchRegister> getBranchRegisterByUserName(String userName) {
        return memBranchRegisterMapper.getMemBranchRegisterByUserName(userName);
    }

    @Override
    public List<MemBranchRegister> getRegisterListByColonelIdAndDate(Long colonelId, Date startTime, Date endTime) {
        return memBranchRegisterMapper.getMemBranchRegisterListByColonelIdAndDate(colonelId, startTime, endTime);
    }


    private void validateMemBranchRegisterExists(Long branchRegisterId) {
        if (memBranchRegisterMapper.selectById(branchRegisterId) == null) {
            throw exception(MEM_BRANCH_REGISTER_NOT_EXISTS);
        }
    }

    /**
     * 组装注册数据 新增用户和门店
     *
     * @param createReqVO
     */
    public Long insertUserBranchByApprove(MemBranchRegisterSaveReqVO createReqVO) {
        //组装数据
        MemMemberRegisterSaveReqVO saveReqVO = HutoolBeanUtils.toBean(createReqVO, MemMemberRegisterSaveReqVO.class);
        saveReqVO.setMemberName(createReqVO.getContactName())
                .setUserName(createReqVO.getContactPhone())
                .setPassword(SecurityUtils.encryptPassword(memberCacheServiceImpl.getDefaultPassword(createReqVO.getSysCode())))
                .setUserType(createReqVO.getUserType());

        //新增用户和门店信息
        Long branchId = memMemberService.insertMemberRegister(saveReqVO);

        // 关联门店和门店注册表信息
        memMemberService.updateRegisterBranchId(branchId, createReqVO.getBranchRegisterId());

        // 发门店调整
        memberAppMqProducer.sendEsColonelAppBranchBaseEvent(branchId);

        // 发送业务员APP首页信息 业务员拓店MQ
        memberAppMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(createReqVO.getColonelId(), createReqVO.getSysCode(), COLONEL_APP_Page_DATA_TYPE_2));

        // 处理门店注册时间
        subscribeApi.branchCreate(branchId, createReqVO.getSysCode());
        return branchId;
    }

    private void processCouponForBranchRegistration(Long branchId, Long sysCode, Long areaId) {
        // 判断是否有优惠券
        List<CouponTemplateDTO> couponTemplateDTOList = couponService.selectValidListBySysCodeAndReceiveType(sysCode, CouponReceiveType.REGISTER.getType());

        if (couponTemplateDTOList.size() <= 2) {
            for (CouponTemplateDTO couponTemplateDTO: couponTemplateDTOList) {
                Long couponTemplateId = couponTemplateDTO.getCouponTemplateId();
                Integer receiveScope = couponTemplateDTO.getReceiveScope();

                // 判断优惠券的领券范围
                if (receiveScope == NumberPool.INT_ZERO || receiveScope == NumberPool.INT_TWO) {
                    // 判断优惠券城市是否包含当前门店城市
                    boolean isEligibleForCoupon = true;

                    if (receiveScope == NumberPool.INT_TWO) {
                        String couponCityId = couponTemplateDTO.getReceiveScopeApplyIds();
                        isEligibleForCoupon = couponCityId != null && couponCityId.contains(areaId + "");
                    }

                    if (isEligibleForCoupon) {
                        // 优惠券生效
                        Map<Long, Long> couponStatusMap = new HashMap<>();
                        couponStatusMap.put(couponTemplateId, Long.parseLong(couponTemplateDTO.getStatus()+""));
                        Long memberId = memBranchUserMapper.getMemberIdByBranchId(branchId);
                        couponService.saveNormalCouponReceiveSingle(new NormalCouponReceiveSingleAsyncReqVo(branchId, memberId, couponTemplateId, true,null,couponStatusMap));
                    }
                }
            }
        }
    }

    @Override
    public MemBranchRegister getMemBranchRegisterByBranchId(Long branchId) {
        return memBranchRegisterMapper.getMemBranchRegisterByBranchId(branchId);
    }
}
