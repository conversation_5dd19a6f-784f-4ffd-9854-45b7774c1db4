package com.zksr.member.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branchRegister.dto.BranchRegisterDTO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterPageReqVO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterSaveReqVO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterSaveResp;
import com.zksr.member.domain.MemBranchRegister;
import com.zksr.member.domain.MemColonelVisitLog;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 门店注册信息Service接口
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
public interface IMemBranchRegisterService {

    /**
     * 新增门店注册信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public MemBranchRegisterSaveResp insertMemBranchRegister(@Valid MemBranchRegisterSaveReqVO createReqVO);

    /**
     * 修改门店注册信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemBranchRegister(@Valid MemBranchRegisterSaveReqVO updateReqVO);

    /**
     * 删除门店注册信息
     *
     * @param branchRegisterId 门店注册信息ID
     */
    public void deleteMemBranchRegister(Long branchRegisterId);

    /**
     * 批量删除门店注册信息
     *
     * @param branchRegisterIds 需要删除的门店注册信息主键集合
     * @return 结果
     */
    public void deleteMemBranchRegisterByBranchRegisterIds(Long[] branchRegisterIds);

    /**
     * 获得门店注册信息
     *
     * @param branchRegisterId 门店注册信息ID
     * @return 门店注册信息
     */
    public MemBranchRegister getMemBranchRegister(Long branchRegisterId);

    /**
     * 获得门店注册信息分页
     *
     * @param pageReqVO 分页查询
     * @return 门店注册信息分页
     */
    PageResult<MemBranchRegisterPageReqVO> getMemBranchRegisterPage(MemBranchRegisterPageReqVO pageReqVO);

    public MemBranchRegister getBranchRegisterById(Long branchRegisterId);

    List<Long> batchAuditBranchRegister(Long[] branchRegisterIds);

    /**
    * @Description: 商城小程序添加门店
    * @Author: liuxingyu
    * @Date: 2024/6/7 16:45
    */
    Long addBranch(BranchRegisterDTO registerDTO);

    /**
     * 根据用户登录名（手机号）查询门店申请信息
     * @param userName
     * @return
     */
    public List<MemBranchRegister> getBranchRegisterByUserName(String userName);

    /**
     * 根据时间区间获取某业务员的扩店信息
     * @param colonelId
     * @param startTime
     * @param endTime
     * @return
     */
    List<MemBranchRegister> getRegisterListByColonelIdAndDate(Long colonelId, Date startTime, Date endTime);

    MemBranchRegister getMemBranchRegisterByBranchId(Long branchId);
}
