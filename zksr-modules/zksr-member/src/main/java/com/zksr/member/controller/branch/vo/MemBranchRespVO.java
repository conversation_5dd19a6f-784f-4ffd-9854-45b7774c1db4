package com.zksr.member.controller.branch.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 门店信息对象 mem_branch
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Data
@ApiModel("门店信息 - mem_branch Response VO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MemBranchRespVO {
    private static final long serialVersionUID = 1L;

    /** 门店id */
    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    /** 门店编号 */
    @ApiModelProperty(value = "门店编号")
    private String branchNo;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 业务员id */
    @Excel(name = "业务员id")
    @ApiModelProperty(value = "业务员id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelId;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 渠道id */
    @Excel(name = "渠道id")
    @ApiModelProperty(value = "渠道id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long channelId;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long groupId;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditTime;

    /** 审核状态 */
    @Excel(name = "审核状态")
    @ApiModelProperty(value = "1已审核 0未审核")
    private Integer auditState;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @Excel(name = "价格码-数据字典", readConverterExp = "1=，2，3，4，5，6")
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;

    /** 门头照 */
    @Excel(name = "门头照")
    @ApiModelProperty(value = "门头照")
    private String branchImages;


    @Excel(name = "是否支持货到付款(0,否 1,是)")
    @ApiModelProperty(value = "是否支持货到付款(0,否 1,是)")
    private Integer hdfkSupport;

    /** 最后一次登陆时间 */
    @Excel(name = "最后一次登陆时间")
    @ApiModelProperty(value = "最后一次登陆时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    /** 签到ID */
    @Excel(name = "签到ID")
    @ApiModelProperty(value = "签到ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelVisitLogId;

    /** 未签退的门店ID */
    @Excel(name = "未签退的门店ID")
    @ApiModelProperty(value = "未签退的门店ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long UnSignBranchId;

    @Excel(name = "货到付款最大可欠款金额")
    @ApiModelProperty("货到付款最大可欠款金额")
    private BigDecimal hdfkMaxAmt;

    @ApiModelProperty("一级区域城市ID, 省市区关联(省份)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long firstAreaCityId;

    @ApiModelProperty("二级区域城市ID, 省市区关联(城市)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long secondAreaCityId;

    @ApiModelProperty("三级区域城市ID, 省市区关联(区域)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long threeAreaCityId;

    /** 渠道名称 */
    @Excel(name = "渠道名称")
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /** 城市名称 */
    @Excel(name = "城市名称")
    @ApiModelProperty(value = "城市名称")
    private String areaName;

    @ApiModelProperty("门店标签信息")
    private List<BranchTagEnum> branchTags;

    @Excel(name = "进入公海时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date seasTime;

    /** 省份 */
    @Excel(name = "省份")
    @ApiModelProperty(value = "省份")
    private String provinceName;

    /** 城市 */
    @Excel(name = "城市")
    @ApiModelProperty(value = "城市")
    private String cityName;

    /** 区县 */
    @Excel(name = "区县")
    @ApiModelProperty(value = "区县")
    private String districtName;


    @Excel(name = "门店注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;

    @Excel(name = "门店注册类型")
    @ApiModelProperty("0 自主注册 1 业务员拓店 2 后台导入")
    private Integer registerType;

    @Excel(name = "门店注册类型")
    @ApiModelProperty("0 自主注册 1 业务员拓店 2 后台导入")
    private String registerTypeName;

    /** 首单单号 */
    @Excel(name = "首单单号")
    @ApiModelProperty("首单单号")
    private String firstOrderNo;

    /** 生命周期阶段 */
    @Excel(name = "生命周期阶段")
    @ApiModelProperty("生命周期阶段")
    private Integer lifecycleStage;

    /** 首单单号 */
    @Excel(name = "下个生命周期阶段提示信息")
    @ApiModelProperty("首单单号")
    private String nextBranchLifecycleMessage;

    @ApiModelProperty("是否首单, 新客标识, 首单标识 1-是 0-否")
    private Integer firstOrderFlag;

    @ApiModelProperty("是否支持在线收款 0：否，1：是")
    private Integer isPayOnline;

    /** 分销渠道（如美云销等) */
    @ApiModelProperty(value = "分销渠道（如美云销等)")
    private String distributionChannel;

    /** 分销模式（如O2O等) */
    @ApiModelProperty(value = "分销模式（如O2O等)")
    private String distributionMode;

    /** 分润模式（如按商品指定金额等) */
    @ApiModelProperty(value = "分润模式（如按商品指定金额等)")
    private String profitMode;

    /** 分润比例 */
    @ApiModelProperty(value = "分润比例")
    @Excel(name = "分润比例")
    private BigDecimal profitRate;

    /** 商户号 */
    @ApiModelProperty(value = "商户号")
    private String merchantNo;

    /** 商户号进件单号 */
    @ApiModelProperty(value = "商户号进件单号")
    private String merchantApplyNo;

    /** 商户号全称 */
    @ApiModelProperty(value = "商户号全称")
    private String merchantFullName;

    @ApiModelProperty(value = "距上次拜访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Long lastVisitDays;

    @ApiModelProperty(value = "距上次订货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Long lastOrderDays;


    /** 纳税人识别码 */
    @ApiModelProperty(value = "纳税人识别码")
    private String taxpayerCode;

    /**
     * 省份编码
     */
    @ApiModelProperty("省份编码")
    private String provinceCode;

    /**
     * 城市编码
     */
    @ApiModelProperty("城市编码")
    private String cityCode;

    /**
     * 区县编码
     */
    @ApiModelProperty("区县编码")
    private String districtCode;

    /**
     * 街道编码
     */
    @ApiModelProperty("街道编码")
    private String streetCode;

    /**
     * 城市区域名称
     */
    @ApiModelProperty("城市区域名称")
    private String cityRegionName;

    /**
     * 门店用户数
     */
    @ApiModelProperty("门店用户数")
    private Long branchUserCount;


    @ApiModelProperty("门店余额账户金额")
    private BigDecimal balanceAmt;


    /** 开启零售（0 否 1 是) */
    @ApiModelProperty(value = "开启零售（0 否 1 是)")
    private Integer enableRetail;

    /** 开启O2O（0 否 1 是) */
    @ApiModelProperty(value = "开启O2O（0 否 1 是)")
    private Integer enableO2o;

    @ApiModelProperty("外部商户编码")
    private String outerMerchantCode;
}
