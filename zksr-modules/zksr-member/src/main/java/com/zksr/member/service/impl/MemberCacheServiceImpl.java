package com.zksr.member.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.member.service.IMemberCacheService;
import com.zksr.product.api.areaChannelPrice.AreaChannelPriceApi;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.AreaItemApi;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.brand.BrandApi;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.CatgoryApi;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.api.saleClass.SaleClassApi;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.skuPrice.SkuPriceApi;
import com.zksr.product.api.skuPrice.dto.SkuPriceDTO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierGroupPrice.SupplierGrouplPriceApi;
import com.zksr.product.api.supplierItem.SupplierItemApi;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.product.constant.ProductConstant;
import com.zksr.product.enums.PrdtSkuPriceOptionType;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.ChannelApi;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.opensource.OpensourceApi;
import com.zksr.system.api.page.PagesConfigApi;
import com.zksr.system.api.page.dto.PagesConfigDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.*;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.OrderStatusReqVO;
import com.zksr.trade.api.order.vo.OrderStatusVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.xmlbeans.impl.xb.ltgfmt.Code;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;

import static com.zksr.common.core.constant.StatusConstants.FLAG_TRUE;
import static com.zksr.common.core.constant.UserConstants.DEFAULT_PASSWORD;
import static com.zksr.common.redis.enums.RedisConstants.DAY_SECONDS;

@Service
@Slf4j
@SuppressWarnings("all")
public class MemberCacheServiceImpl implements IMemberCacheService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RedisService redisService;

    @Autowired
    private Cache<String, PartnerDto> partnerDtoCache;

    @Autowired
    private Cache<Long, BranchDTO> branchDTOCache;

    @Autowired
    private Cache<Long, AreaDTO> areaDtoCache;

    @Autowired
    private Cache<Long, ChannelDTO> channelDTOCache;

    @Autowired
    private Cache<Long, SupplierDTO> supplierDTOCache;
    @Autowired
    private Cache<Long, DcDTO> dcDtoCache;

    @Autowired
    private Cache<Long, AreaClassDTO> areaClassDtoCache;

    @Autowired
    @Qualifier("areaClassBranchListDtoCache")
    private Cache<Long, List<AreaClassDTO>> areaClassBranchListDtoCache;

    @Autowired
    @Qualifier("areaClassAreaChannelListDtoCache")
    private Cache<String, List<AreaClassDTO>> areaClassAreaChannelListDtoCache;

    @Autowired
    private Cache<Long, CatgoryDTO> catgoryDtoCache;

    @Autowired
    private Cache<Long, SaleClassDTO> saleClassDtoCache;

    @Autowired
    private Cache<Long, List<SaleClassDTO>> saleClassDtoListCache;

    @Autowired
    private Cache<Long, BrandDTO> brandDtoCache;

    @Autowired
    private Cache<Long, BasicSettingPolicyDTO> basicSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, AreaItemDTO> areaItemDTOCache;

    @Autowired
    private Cache<Long, SupplierItemDTO> supplierItemDTOCache;

    @Autowired
    private Cache<Long, SkuDTO> skuDTOCache;

    @Autowired
    private Cache<Long, SpuDTO> spuDTOCache;

    @Autowired
    private Cache<String, CatgoryRateDTO> catgoryRateCache;

    @Autowired
    private Cache<Long, AppletBaseConfigDTO> appletBaseConfigDTOCache;

    @Autowired
    @Qualifier("areaSalePriceCodeCache")
    private Cache<String, Integer> areaSalePriceCodeCache;

    @Autowired
    @Qualifier("supplierSalePriceCodeCache")
    private Cache<String, Integer> supplierSalePriceCodeCache;

    @Autowired
    private Cache<String, SkuPriceDTO> skuPriceDTOByAreaTypeCache;

    @Autowired
    private Cache<Long, ColonelDTO> colonelDTOCache;

    @Autowired
    private Cache<Long, CouponTemplateDTO> couponTemplateCache;

    @Autowired
    private Cache<Long, List<CouponSpuScopeDTO>> couponSpuScopeCache;

    @Autowired
    private Cache<Long,AppletAgreementPolicyDTO> appletAgreementPolicyDTOCache;

    @Autowired
    private Cache<String, List<PagesConfigDTO>> pagesConfigCache;

    @Autowired
    private Cache<Long, ColonelSettingPolicyDTO> colonelSettingPolicyCache;

    @Autowired
    private Cache<Long, OrderStatusVO> orderStatusTotalCache;

    @Autowired
    private Cache<Long, PayConfigDTO> payConfigDTOCache;

    @Autowired
    private Cache<Long, PartnerMiniSettingPolicyDTO> partnerMiniSettingPolicyDTOCache;

    @Autowired
    @Qualifier("visualSettingDetailBySupplierIdCache")
    private Cache<String, VisualSettingDetailDto> visualSettingDetailBySupplierIdCache;

    @Autowired
    private Cache<Long, BranchLifecycleSettingPolicyDTO> branchLifecycleSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, WithdrawalSettingPolicyDTO> withdrawalSettingPolicyCache;

    @Autowired
    private PartnerApi partnerApi;

    @Autowired
    private AreaApi areaApi;

    @Autowired
    private ChannelApi channelApi;

    @Autowired
    private BranchApi branchApi;

    @Autowired
    private SupplierApi supplierApi;

    @Autowired
    private DcApi dcApi;

    @Autowired
    private AreaClassApi areaClassApi;

    @Autowired
    private CatgoryApi catgoryApi;

    @Autowired
    private SaleClassApi saleClassApi;

    @Autowired
    private BrandApi brandApi;

    @Autowired
    private PartnerPolicyApi partnerPolicyApi;

    @Autowired
    private AreaItemApi areaItemApi;

    @Autowired
    private SupplierItemApi supplierItemApi;

    @Autowired
    private SpuApi spuApi;

    @Autowired
    private SkuApi skuApi;

    @Autowired
    private PartnerConfigApi partnerConfigApi;

    @Autowired
    private AreaChannelPriceApi areaChannelPriceApi;

    @Autowired
    private SupplierGrouplPriceApi supplierGrouplPriceApi;

    @Autowired
    private SkuPriceApi skuPriceApi;

    @Autowired
    private ColonelApi colonelApi;

    @Autowired
    private CouponApi couponApi;

    @Autowired
    private PagesConfigApi pagesConfigApi;
    @Autowired
    private OrderApi orderApi;

    @Autowired
    private OpensourceApi opensourceApi;


    private static final String WX_SESSION_KEY = "wx_session_key:";

    @PostConstruct
    public void init() {
        //自动load（read through）
        partnerDtoCache.config().setLoader(this::loadPartnerDtoFromApi);
        branchDTOCache.config().setLoader(this::loadBranchDTOFromApi);
        areaDtoCache.config().setLoader(this::loadAreaDtoFromApi);
        channelDTOCache.config().setLoader(this::loadChannelDTOFromApi);
        supplierDTOCache.config().setLoader(this::loadSupplierDTOFromApi);
        dcDtoCache.config().setLoader(this::loadDcDtoFromApi);
        areaClassDtoCache.config().setLoader(this::loadAreaClassDtoFromApi);
        areaClassBranchListDtoCache.config().setLoader(this::loadAreaClassBranchListDtoFromApi);
        areaClassAreaChannelListDtoCache.config().setLoader(this::loadAreaClassAreaChannelListDtoFromApi);
        catgoryDtoCache.config().setLoader(this::loadCatgoryDtoFromApi);
        saleClassDtoCache.config().setLoader(this::loadSaleClassDtoFromApi);
        saleClassDtoListCache.config().setLoader(this::loadSaleClassDtoListFromApi);
        brandDtoCache.config().setLoader(this::loadBrandDtoFromApi);
        basicSettingPolicyDTOCache.config().setLoader(this::loadBasicSettingPolicyDtoFromApi);
        areaItemDTOCache.config().setLoader(this::loadAreaItemDtoFromApi);
        supplierItemDTOCache.config().setLoader(this::loadSupplierItemDtoFromApi);
        spuDTOCache.config().setLoader(this::loadSpuDtoFromApi);
        skuDTOCache.config().setLoader(this::loadSkuDtoFromApi);
        appletBaseConfigDTOCache.config().setLoader(this::loadAppletBaseConfigDtoFromApi);
        areaSalePriceCodeCache.config().setLoader(this::loadAreaChannelSalePriceCodeFromApi);
        supplierSalePriceCodeCache.config().setLoader(this::loadSupplierGroupSalePriceCodeFromApi);
        skuPriceDTOByAreaTypeCache.config().setLoader(this::loadSkuPriceDTOByAreaTypeFromApi);
        catgoryRateCache.config().setLoader(this::loadCatgoryRateCache);
        colonelDTOCache.config().setLoader(this::loadColonelDtoFromApi);
        couponTemplateCache.config().setLoader(this::loadCouponTemplateFromApi);
        couponSpuScopeCache.config().setLoader(this::loadCouponSpuScopeCache);
        appletAgreementPolicyDTOCache.config().setLoader(this::loadAppletAgreementPolicyDTOFromApi);
        pagesConfigCache.config().setLoader(this::loadPagesConfig);
        colonelSettingPolicyCache.config().setLoader(this::loadColonelSettingPolicyDtoFromApi);
        orderStatusTotalCache.config().setLoader(this::loadOrderStatusTotalFromApi);
        payConfigDTOCache.config().setLoader(this::loadPayConfigDTOFromApi);
        partnerMiniSettingPolicyDTOCache.config().setLoader(this::loadPartnerMiniSettingPolicyDTOFromApi);
        visualSettingDetailBySupplierIdCache.config().setLoader(this::loadVisualSettingDetailBySupplierIdCacheService);
        branchLifecycleSettingPolicyDTOCache.config().setLoader(this::loadBranchLifecycleSettingPolicyDtoFromApi);
        withdrawalSettingPolicyCache.config().setLoader(this::loadWithdrawalSettingPolicyCache);
    }

    private WithdrawalSettingPolicyDTO loadWithdrawalSettingPolicyCache(Long sysCode) {
        return partnerPolicyApi.getWithdrawalSettingPolicy(sysCode).getCheckedData();
    }

    private PayConfigDTO loadPayConfigDTOFromApi(Long sysCode) {
        return partnerConfigApi.getPayConfig(sysCode).getCheckedData();
    }

    /**
    * @Description: 根据门店区域和门店渠道获取城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/4/24 10:39
    */
    private List<AreaClassDTO> loadAreaClassAreaChannelListDtoFromApi(String key) {
        return areaClassApi.getAreaClassAreaChannelList(key).getCheckedData();
    }

    private List<PagesConfigDTO> loadPagesConfig(String pageKey) {
        return pagesConfigApi.getPagesConfig(pageKey).getCheckedData();
    }

    /**
    * @Description: 获取平台商商城小程序配置
    * @Author: liuxingyu
    * @Date: 2024/4/10 16:02
    */
    private AppletAgreementPolicyDTO loadAppletAgreementPolicyDTOFromApi(Long sysCode) {
        return partnerPolicyApi.getAppletAgreementPolicy(sysCode).getCheckedData();
    }


    private List<CouponSpuScopeDTO> loadCouponSpuScopeCache(Long couponTemplateId) {
        return couponApi.getCouponSpuScopeList(couponTemplateId).getCheckedData();
    }


    /**
    * @Description: 获取渠道绑定的城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/27 9:43
    */
    private CatgoryRateDTO loadCatgoryRateCache(String cacheKey) {
        String[] infos = cacheKey.split(":");
        return catgoryApi.getCatgoryByIdAndAreaId(Long.parseLong(infos[0]), Long.parseLong(infos[1])).getCheckedData();
    }

    /**
    * @Description: 获取门店展示绑定的展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:37
    */
    private List<AreaClassDTO> loadAreaClassBranchListDtoFromApi(Long branchId) {
        List<Long> supplierIds = branchApi.getSupplierByBranchId(branchId).getCheckedData();
        if (ObjectUtil.isEmpty(supplierIds)){
            return null;
        }
        return areaClassApi.getAreaClassBranchList(supplierIds).getCheckedData();
    }
    /**
    * @Description: 获取平台展示分类列表
    * @Author: liuxingyu
    * @Date: 2024/3/26 20:27
    */
    private List<SaleClassDTO> loadSaleClassDtoListFromApi(Long sysCode) {
        return saleClassApi.getSaleClassListBySysCode(sysCode).getCheckedData();
    }

    private SupplierDTO loadSupplierDTOFromApi(Long supplierId) {
        return supplierApi.getBySupplierId(supplierId).getCheckedData();
    }

    private ChannelDTO loadChannelDTOFromApi(Long channelId) {
        return channelApi.getByChannelId(channelId).getCheckedData();
    }

    private AreaDTO loadAreaDtoFromApi(Long areaId) {
        return areaApi.getAreaByAreaId(areaId).getCheckedData();
    }

    private BranchDTO loadBranchDTOFromApi(Long branchId) {
        return branchApi.getByBranchId(branchId).getCheckedData();
    }

    private PartnerDto loadPartnerDtoFromApi(String key) {
        return partnerApi.getPartnerBySource(key).getCheckedData();
    }

    private DcDTO loadDcDtoFromApi(Long dcId) {
        return dcApi.getDcById(dcId).getCheckedData();
    }

    private AreaClassDTO loadAreaClassDtoFromApi(Long areaClassId) {
        return areaClassApi.getAreaClassByAreaClassId(areaClassId).getCheckedData();
    }

    private CatgoryDTO loadCatgoryDtoFromApi(Long catgoryId) {
        return catgoryApi.getCatgoryByCatgoryId(catgoryId).getCheckedData();
    }

    private SaleClassDTO loadSaleClassDtoFromApi(Long saleClassId) {
        return saleClassApi.getSaleClassBySaleClassId(saleClassId).getCheckedData();
    }

    private AppletBaseConfigDTO loadAppletBaseConfigDtoFromApi(Long sysCode){
        return partnerConfigApi.getAppletBaseConfig(sysCode).getCheckedData();
    }

    private BrandDTO loadBrandDtoFromApi(Long brandId) {
        return brandApi.getBrandByBrandId(brandId).getCheckedData();
    }

    private BasicSettingPolicyDTO loadBasicSettingPolicyDtoFromApi(Long dcId) {
        return partnerPolicyApi.getBasicSettingPolicy(dcId).getCheckedData();
    }


    private AreaItemDTO loadAreaItemDtoFromApi(Long areaItemId) {return areaItemApi.getAreaItemId(areaItemId).getCheckedData();}

    private SupplierItemDTO loadSupplierItemDtoFromApi(Long supplierItemId){return supplierItemApi.getBySupplierItemId(supplierItemId).getCheckedData();}

    private SpuDTO loadSpuDtoFromApi(Long spuId){return spuApi.getBySpuId(spuId).getCheckedData();}

    private SkuDTO loadSkuDtoFromApi(Long skuId){return skuApi.getBySkuId(skuId).getCheckedData();}

    private Integer loadAreaChannelSalePriceCodeFromApi(String key){
        //为了避免重复、频繁的查询数据库获取价格码 如果为空也设置进Redis  在后台新增修改价格码时 进行维护
        Integer checkedData = areaChannelPriceApi.getPriceByKey(key).getCheckedData();
        return checkedData;
    }

    private Integer loadSupplierGroupSalePriceCodeFromApi(String key){
        //为了避免重复、频繁的查询数据库获取价格码 如果为空也设置进Redis  在后台新增修改价格码时 进行维护
        Integer checkedData = supplierGrouplPriceApi.getPriceByKey(key).getCheckedData();
        return checkedData;
    }

    private SkuPriceDTO loadSkuPriceDTOByAreaTypeFromApi(String key){
        return skuPriceApi.getSkuPriceByKey(key).getCheckedData();
    }
    private ColonelDTO loadColonelDtoFromApi(Long colonelId){return colonelApi.getByColonelId(colonelId).getCheckedData();}

    private CouponTemplateDTO loadCouponTemplateFromApi(Long couponTemplateId){
        return couponApi.getCouponTemplate(couponTemplateId).getCheckedData();
    }
    private OrderStatusVO loadOrderStatusTotalFromApi(Long colonelId) {
        // 这里是查询业务员管理门店订单角标缓存
        return orderApi.getOrderStatus(new OrderStatusReqVO(2, colonelId));
    }

    /**
     * 获取运营商业务员配置
     * @param dcId
     * @return
     */
    private ColonelSettingPolicyDTO loadColonelSettingPolicyDtoFromApi(Long dcId) {
        return partnerPolicyApi.getColonelSettingPolicy(dcId).getCheckedData();
    }

    private PartnerMiniSettingPolicyDTO loadPartnerMiniSettingPolicyDTOFromApi(Long sysCode) {
        return partnerPolicyApi.getPartnerMiniSettingPolicy(sysCode).getCheckedData();
    }

    private VisualSettingDetailDto loadVisualSettingDetailBySupplierIdCacheService(String key){
        VisualSettingDetailDto visualSettingDetailDto = opensourceApi.getVisualSettingDetailByMerchantId(key).getCheckedData();
        if (Objects.isNull(visualSettingDetailDto)) {
            return new VisualSettingDetailDto();
        }
        return visualSettingDetailDto;
    }

    /**
     * 获取运营商门店生命周期配置
     * @param dcId
     * @return
     */
    private BranchLifecycleSettingPolicyDTO loadBranchLifecycleSettingPolicyDtoFromApi(Long dcId) {
        BranchLifecycleSettingPolicyDTO branchLifecycleSettingPolicyDto = partnerPolicyApi.getBranchLifecycleSettingPolicy(dcId).getCheckedData();
        if (Objects.isNull(branchLifecycleSettingPolicyDto)) {
            return new BranchLifecycleSettingPolicyDTO();
        }
        return branchLifecycleSettingPolicyDto;
    }

    @Override
    public void setWxSessionKey(String key, String sessionKey) {
        redisService.setCacheObject(WX_SESSION_KEY + key, sessionKey);
        redisService.expire(WX_SESSION_KEY + key, DAY_SECONDS);
    }

    @Override
    public String getWxSessionKey(String key) {
        return redisService.getCacheObject(WX_SESSION_KEY + key);
    }

    @Override
    public PartnerDto getPartnerDto(String key) {
        return partnerDtoCache.get(key);
    }

    @Override
    public BranchDTO getBranchDto(Long branchId) {
        return branchDTOCache.get(branchId);
    }

    @Override
    public ChannelDTO getChannelDto(Long channelId) {
        if (Objects.isNull(channelId)) {
            return null;
        }
        return channelDTOCache.get(channelId);
    }

    @Override
    public AreaDTO getAreaDto(Long areaId) {
        if (Objects.isNull(areaId)) {
            return null;
        }
        return areaDtoCache.get(areaId);
    }

    @Override
    public SupplierDTO getSupplierDTO(Long supplierId) {
        return supplierDTOCache.get(supplierId);
    }

    @Override
    public BasicSettingPolicyDTO getBasicSettingPolicyDTO(Long dcId) {
        return basicSettingPolicyDTOCache.get(dcId);
    }

    @Override
    public AreaItemDTO getAreaItemDTO(Long areaItemId) {
        return areaItemDTOCache.get(areaItemId);
    }

    @Override
    public SupplierItemDTO getSupplierItemDTO(Long supplierItemId) {
        return supplierItemDTOCache.get(supplierItemId);
    }

    @Override
    public SpuDTO getSpuDTO(Long spuId) {
        return spuDTOCache.get(spuId);
    }

    @Override
    public SkuDTO getSkuDTO(Long skuId) {
        return skuDTOCache.get(skuId);
    }

    /**
    * @Description: 获取门店绑定的城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/28 18:59
    */
    @Override
    public List<AreaClassDTO> getAreaClassBranch(Long branchId) {
        return areaClassBranchListDtoCache.get(branchId);
    }

    /**
     * 获取管理分类分润比例
     * @param catgoryId 管理分类ID
     * @param areaId    区域城市ID
     * @return
     */
    @Override
    public CatgoryRateDTO getCatgoryByIdAndAreaId(Long catgoryId, Long areaId) {
        return catgoryRateCache.get(RedisConstants.getCategoryRate(catgoryId, areaId));
    }

    /**
    * @Description: 获取平台展示分类列表
    * @Author: liuxingyu
    * @Date: 2024/3/26 20:30
    */
    @Override
    public List<SaleClassDTO> getSaleClassListBySysCode(Long sysCode) {
        return saleClassDtoListCache.get(sysCode);
    }

    /**
    * @Description: 获取渠道绑定的城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/27 9:51
    */


    @Override
    public AppletBaseConfigDTO getAppletBaseConfigDTO(Long sysCode) {
        return appletBaseConfigDTOCache.get(sysCode);
    }

    @Override
    public Integer getAreaSalePriceCodeCache(String key) {
        return areaSalePriceCodeCache.get(key);
    }

    @Override
    public Integer getSupplierSalePriceCodeCache(Long areaId, Long areaGourpId) {
        return supplierSalePriceCodeCache.get(StringUtils.format("{}-{}", areaId, areaGourpId));
    }

    @Override
    public SkuPriceDTO getSkuPriceDTOByAreaTypeCache(Long areaId, Long skuId, Integer productType) {
        return skuPriceDTOByAreaTypeCache.get(StringUtils.format("{}-{}-{}", areaId, skuId, productType));
    }
    @Override
    public ColonelDTO getColonel(Long colonelId) {
        if (Objects.isNull(colonelId)) {
            return null;
        }
        return colonelDTOCache.get(colonelId);
    }

    @Override
    public BrandDTO getBrandDTO(Long brandId) {
        return brandDtoCache.get(brandId);
    }

    @Override
    public CouponTemplateDTO getCouponTemplate(Long couponTemplateId) {
        return couponTemplateCache.get(couponTemplateId);
    }

    @Override
    public List<CouponSpuScopeDTO> getCouponSpuScopeList(Long couponTemplateId) {
        return couponSpuScopeCache.get(couponTemplateId);
    }

    @Override
    public AppletAgreementPolicyDTO getAppletAgreementPolicy(Long sysCode) {
        return appletAgreementPolicyDTOCache.get(sysCode);
    }

    @Override
    public List<PagesConfigDTO> getPagesConfigDTO(Long sysCode, Long channel, Long areaId) {
        return pagesConfigCache.get(RedisConstants.getPageConfigKey(sysCode, channel, areaId));
    }

    /**
    * @Description: 根据门店区域和门店渠道获取城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/4/24 10:36
    */
    @Override
    public List<AreaClassDTO> getAreaClassAreaChannel(Long areaId, Long channelId) {
        if (ObjectUtil.isNull(channelId)){
            channelId = 0L;
        }
        String key = areaId+"-"+channelId;
        return areaClassAreaChannelListDtoCache.get(key);
    }

    @Override
    public ColonelSettingPolicyDTO getColonelSettingPolicyDTO(Long dcId) {
        return colonelSettingPolicyCache.get(dcId);
    }


    @Override
    public OrderStatusVO getOrderStatusQty(Long colonelId) {
        return orderStatusTotalCache.get(colonelId);
    }

    @Override
    public PayConfigDTO getPayConfigDTO(Long sysCode) {
        return payConfigDTOCache.get(sysCode);
    }

    @Override
    public void deleteBranchById(Long branchId) {
        if (Objects.isNull(branchId)) {
            return;
        }
        branchDTOCache.remove(branchId);
    }

    @Override
    public CatgoryDTO getCategoryDTO(Long categoryId) {
        if (Objects.isNull(categoryId)) {
            return null;
        }
        return catgoryDtoCache.get(categoryId);
    }

    @Override
    public PartnerMiniSettingPolicyDTO getPartnerMiniSettingPolicy(Long sysCode) {
        return partnerMiniSettingPolicyDTOCache.get(sysCode);
    }

    @Override
    public VisualSettingDetailDto getVisualDetailBySupplier(String key) {
        return visualSettingDetailBySupplierIdCache.get(key);
    }

    @Override
    public String getDefaultPassword(Long sysCode) {
        PartnerMiniSettingPolicyDTO partnerSettingPolicyDTO = partnerMiniSettingPolicyDTOCache.get(sysCode);
        return Optional.ofNullable(partnerSettingPolicyDTO)
                 .map(policyDto -> {
                    return ToolUtil.isEmptyReturn(policyDto.getDefalutPassword(), DEFAULT_PASSWORD);
                }).orElse(DEFAULT_PASSWORD);
    }

    @Override
    public BranchLifecycleSettingPolicyDTO getBranchLifecycleSettingPolicyDTO(Long dcId) {
        return branchLifecycleSettingPolicyDTOCache.get(dcId);
    }

    @Override
    public WithdrawalSettingPolicyDTO getWithdrawalSetting(Long sysCode) {
        return withdrawalSettingPolicyCache.get(sysCode);
    }

    @Override
    public DcDTO getDcDTO(Long dcId) {
        if (Objects.isNull(dcId))
            return null;
        return dcDtoCache.get(dcId);
    }
}
