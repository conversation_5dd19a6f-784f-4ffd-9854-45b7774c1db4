package com.zksr.member.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.member.dto.MemMemberSaveReqVO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.member.api.member.vo.MemMemberPageReqVO;
import com.zksr.member.api.member.vo.MemMemberRespVO;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterSaveReqVO;
import com.zksr.member.domain.MemMember;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户信息Service接口
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
public interface IMemMemberService {

    /**
     * 新增用户信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemMember(@Valid MemMemberSaveReqVO createReqVO);

    /**
     * 修改用户信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemMember(@Valid MemMemberSaveReqVO updateReqVO);

    /**
     * 删除用户信息
     *
     * @param memberId 用户id;用户id
     */
    public void deleteMemMember(Long memberId);

    /**
     * 批量删除用户信息
     *
     * @param memberIds 需要删除的用户信息主键集合
     * @return 结果
     */
    public void deleteMemMemberByMemberIds(Long[] memberIds);

    /**
     * 获得用户信息
     *
     * @param memberId 用户id;用户id
     * @return 用户信息
     */
    public MemMember getMemMember(Long memberId);

    /**
     * 获得用户信息分页
     *
     * @param pageReqVO 分页查询
     * @return 用户信息分页
     */
    PageResult<MemMemberRespVO> getMemMemberPage(MemMemberPageReqVO pageReqVO);

    MemMember getInfoByMobileAndOpenid(Long sysCode, String mobile, String openid,String username);

    /**
     * 停用用户
     * @param memberId
     */
    public void disable(Long memberId);

    /**
     * 启用用户
     * @param memberId
     */
    public void enable(Long memberId);


    /**
     * 通过门店/用户注册信息 审核后 生成门店、用户信息
     * @param saveReqVO
     * @return  门店ID
     */
    public Long insertMemberRegister(MemMemberRegisterSaveReqVO saveReqVO);

    /**
     * 修改用户的默认门店
     * @param memberId
     * @param branchId
     * @param sysCode
     */
    public void updateDefaultBranch(Long memberId, Long branchId,Long sysCode);

    /**
     * 获取半年内所有已过期的用户信息
     * @return 失效天数
     * @param sysCode
     */
    List<MemberDTO> getAllExpirationDateMemberList(Long sysCode);

    /**
     * 批量修改用户的状态
     * @param memberDTO
     * @return
     */
    void updateStatusByMemberIds(MemberDTO memberDTO);

    /**
     * 新增子用户
     * @param memberDTO
     */
    void insertChildUser(MemberDTO memberDTO);

    List<MemberDTO> childUserList(Long memberId);

    Boolean updateMemberToken(MemMember memMember);

    /**
     * 更新用户密码
     * @param member
     * @return
     */
    void updateMemberPwd(MemMember member);

    void updateMemberPublishOpenid(Long memberId, String publishOpenid);

    /**
     * 更新用户信息
     * @param member
     * @return
     */
    void updateMemberUserInfoByMemberId(MemMember member);

    /**
     * 根据colonelId获取用户信息
     * @param colonelId 业务员ID
     * @param sysCode 平台商ID
     * @return
     */
    MemMember getMemBerByColonelId(Long colonelId, Long sysCode);

    MemMember createColonelMember(MemberDTO memberDTO);

    /**
     * 修改用户设备ID
     * @param memberDTO
     * @return
     */
    void updateMemberDeviceId(MemberDTO memberDTO);

    void updateMemMemberPassword(MemMemberSaveReqVO updateReqVO);

    void updateRegisterBranchId(Long branchId, Long branchRegisterId);

    /**
     * 自动登录，当用户不存在时，新增用户信息
     */
    MemMember createMemberCaseAutoLogin(MemMemberSaveReqVO createReqVO);

    /**
     * 通过外部用户编码，获取用户信息
     */
    MemMember getMemberByOuterUserCode(Long sysCode, String outerUserCode);
}
