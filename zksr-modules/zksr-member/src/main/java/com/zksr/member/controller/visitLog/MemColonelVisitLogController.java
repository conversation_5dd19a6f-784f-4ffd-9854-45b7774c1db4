package com.zksr.member.controller.visitLog;

import com.alibaba.nacos.common.utils.JacksonUtils;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresLogin;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.controller.visitLog.vo.*;
import com.zksr.member.domain.MemColonelVisitLog;
import com.zksr.member.service.IMemColonelVisitLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 业务员拜访日志Controller
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@Api(tags = "管理后台 - 业务员拜访日志接口", produces = "application/json")
@Validated
@Slf4j
@RestController
@RequestMapping("/visitLog")
public class MemColonelVisitLogController {
    @Autowired
    private IMemColonelVisitLogService memColonelVisitLogService;

    @RequiresLogin
    @PostMapping("/signIn")
    @ApiOperation("签到")
    public R<Long> signIn(@RequestBody MemColonelVisitLogSaveReqVO signInDto) {
        log.info("B2B-签到实体：" + JacksonUtils.toJson(signInDto));
        return memColonelVisitLogService.signInInner(signInDto);
    }

    @RequiresLogin
    @GetMapping("/signInOrOutFlag")
    @ApiOperation("签到")
    public R<String> signInOrOutFlag(Long colonelId, Long consumerNo) {
        log.info("signInOrOutFlagInner colonelId:{} consumerNo:{}", colonelId, consumerNo);
        return memColonelVisitLogService.signInOrOutFlagInner(colonelId, consumerNo);
    }
    /**
     * 签退
     * @param reqVo
     * @return
     */
    @ApiIgnore
    @RequiresLogin
    @PutMapping("/signOut")
    @ApiOperation("签到")
    public R<Boolean> signOutInner(@RequestBody MemColonelVisitLogSaveReqVO reqVo) {
        return memColonelVisitLogService.signOutInner(reqVo);
    }


    /**
     * 放弃拜访
     *
     * @param consumerNo 客户编号
     */
    @ApiIgnore
    @RequiresLogin
    @GetMapping("/signWaive")
    @ApiOperation("放弃拜访")
    public R<Boolean> waiveInner(@RequestParam("colonelId") String colonelId, @RequestParam("consumerNo") String consumerNo) {
        return memColonelVisitLogService.waiveInner(Long.valueOf(colonelId), Long.valueOf(consumerNo));
    }
    @ApiOperation(value = "", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "业务员拜访日志", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemColonelVisitLogSaveReqVO createReqVO) {
        return success(memColonelVisitLogService.insertMemColonelVisitLog(createReqVO));
    }

    /**
     * 修改业务员拜访日志
     */
    @ApiOperation(value = "修改业务员拜访日志", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "业务员拜访日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemColonelVisitLogSaveReqVO updateReqVO) {
            memColonelVisitLogService.updateMemColonelVisitLog(updateReqVO);
        return success(true);
    }

    /**
     * 删除业务员拜访日志
     */
    @ApiOperation(value = "删除业务员拜访日志", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "业务员拜访日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{visitIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] visitIds) {
        memColonelVisitLogService.deleteMemColonelVisitLogByVisitIds(visitIds);
        return success(true);
    }

    /**
     * 获取业务员拜访日志详细信息
     */
    @ApiOperation(value = "获得业务员拜访日志详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{colonelVisitLogId}")
    public CommonResult<MemColonelVisitLogRespVO> getInfo(@PathVariable("colonelVisitLogId") Long colonelVisitLogId) {
        MemColonelVisitLog memColonelVisitLog = memColonelVisitLogService.getMemColonelVisitLog(colonelVisitLogId);
        return success(HutoolBeanUtils.toBean(memColonelVisitLog, MemColonelVisitLogRespVO.class));
    }

    /**
     * 分页查询业务员拜访日志
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得业务员拜访日志分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    public CommonResult<PageResult<MemColonelVisitLogRespVO>> getPage(@Valid MemColonelVisitLogPageReqVO pageReqVO) {
        return success(memColonelVisitLogService.getMemColonelVisitLogPage(pageReqVO));
    }

    /**
     * 分页查询业务员拜访日志
     */
    @GetMapping("/getColonelVisitLogCollectList")
    @ApiOperation(value = "获得业务员拜访汇总分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    public CommonResult<PageResult<MemColonelVisitLogCollectRespVO>> getColonelVisitLogCollectList(@Valid MemColonelVisitLogPageReqVO pageReqVO) {
        return success(memColonelVisitLogService.getColonelVisitLogCollectList(pageReqVO));
    }

    @GetMapping("/getLatestRecordsForEachColonel")
    @ApiOperation(value = "根据时间获取业务员最新的一条拜访记录", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    public CommonResult<List<MemColonelVisitLog>> getLatestRecordsForEachColonel(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        return success(memColonelVisitLogService.getLatestRecordsForEachColonel(startTime,endTime));
    }

    @GetMapping("/getVisitLogImageZip")
    @ApiOperation(value = "拜访日志下载选中的图片", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DOWNLOAD)
    @RequiresPermissions(Permissions.DOWNLOAD)
    public void getVisitLogImageZip(HttpServletResponse response, MemColonelVisitLogPageReqVO pageReqVO) throws IOException {

        response.reset();
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(("图片导出.zip").getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));

        try {
            // 直接获取完整的ZIP文件内容作为byte[]
            byte[] zipBytes = memColonelVisitLogService.getVisitLogImageZip(pageReqVO);

            // 将整个ZIP文件内容写入响应流
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(zipBytes);
            outputStream.flush();
        } catch (Exception e) {
            throw new RuntimeException("Error streaming ZIP file", e);
        } finally {
            // 注意：这里无需关闭ZipOutputStream，因为我们并没有创建它。但通常会关闭OutputStream，但在HttpServletResponse的场景下，这通常由容器管理，不推荐手动关闭。
        }
    }

    /**
     * 分页查询业务员拜访行程信息
     */
    @GetMapping("/getColonelVisitLogTravelPath")
    @ApiOperation(value = "分页查询业务员拜访行程信息", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    public CommonResult<List<MemColonelVisitLogTravelPathRespVO>> getColonelVisitLogTravelPath(@Valid MemColonelVisitLogPageReqVO pageReqVO) {
        return success(memColonelVisitLogService.getColonelVisitLogTravelPath(pageReqVO));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:visitLog:add";
        /** 编辑 */
        public static final String EDIT = "member:visitLog:edit";
        /** 删除 */
        public static final String DELETE = "member:visitLog:remove";
        /** 列表 */
        public static final String LIST = "member:visitLog:list";
        /** 查询 */
        public static final String GET = "member:visitLog:query";
        /** 下载 */
        public static final String DOWNLOAD = "member:visitLog:download";
    }

}
