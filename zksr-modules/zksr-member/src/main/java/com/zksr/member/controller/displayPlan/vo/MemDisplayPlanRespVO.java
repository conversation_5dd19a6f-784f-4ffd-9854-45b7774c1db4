package com.zksr.member.controller.displayPlan.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 陈列计划对象 mem_display_plan
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Data
@ApiModel("陈列计划 - mem_display_plan Response VO")
public class MemDisplayPlanRespVO {
    private static final long serialVersionUID = 1L;

    /** 陈列计划编号，主键 */
    @ApiModelProperty(value = "陈列计划编号，主键")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long planId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 陈列类型ID */
    @Excel(name = "陈列类型ID")
    @ApiModelProperty(value = "陈列类型ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long displayTypeId;

    /** 门店ID */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "结束时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 审核人 */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人")
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "审核时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "审核时间")
    private Date auditTime;

    /** 审核状态（0:未审核,1：已审核,2：已作废） */
    @Excel(name = "审核状态", readConverterExp = "0=:未审核,1：已审核,2：已作废")
    @ApiModelProperty(value = "审核状态")
    private Integer auditState;

    /** 生效类型（0:未生效，1：生效中，2：已完成，3：已终止） */
    @Excel(name = "生效类型", readConverterExp = "0=:未生效，1：生效中，2：已完成，3：已终止")
    @ApiModelProperty(value = "生效类型")
    private Integer type;

    /** 终止原因 */
    @Excel(name = "终止原因")
    @ApiModelProperty(value = "终止原因")
    private String terminateReason;

    /** 删除状态(0:正常，2：删除) */
    @ApiModelProperty(value = "删除状态")
    private Integer delFlag;

}
