package com.zksr.member.controller.displayRegister.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 陈列计划打卡记录对象 mem_display_register
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@ApiModel("陈列计划打卡记录 - mem_display_register分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemDisplayRegisterPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long registerId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 陈列计划ID */
    @Excel(name = "陈列计划ID")
    @ApiModelProperty(value = "陈列计划ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long planId;

    /** 照片 */
    @Excel(name = "照片")
    @ApiModelProperty(value = "照片")
    private String pic;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 删除状态(0:正常，2：删除) */
    @ApiModelProperty(value = "删除状态")
    private Long delFlag;


}
