package com.zksr.member.convert.memberRegister;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemMemberRegister;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterRespVO;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 用户注册信息 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-23
*/
@Mapper
public interface MemMemberRegisterConvert {

    MemMemberRegisterConvert INSTANCE = Mappers.getMapper(MemMemberRegisterConvert.class);

    MemMemberRegisterRespVO convert(MemMemberRegister memMemberRegister);

    MemMemberRegister convert(MemMemberRegisterSaveReqVO memMemberRegisterSaveReq);

    PageResult<MemMemberRegisterRespVO> convertPage(PageResult<MemMemberRegister> memMemberRegisterPage);
}