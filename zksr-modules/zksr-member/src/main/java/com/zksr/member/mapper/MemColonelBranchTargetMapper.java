package com.zksr.member.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import com.zksr.member.controller.colonelApp.vo.ColonelAppBranchTargetUpdateVO;
import com.zksr.member.controller.colonelTarget.vo.ColonelBranchTargetRespVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelBranchTargetPageReqVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelBranchTargetRespVO;
import com.zksr.member.domain.MemColonel;
import com.zksr.member.domain.MemColonelBranchTarget;
import com.zksr.member.domain.MemColonelTarget;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;

@Mapper
public interface MemColonelBranchTargetMapper extends BaseMapperX<MemColonelBranchTarget> {
    default List<MemColonelBranchTarget> getListByColonelTarget(MemColonelTarget colonelTarget) {
        return selectList(new LambdaQueryWrapperX<MemColonelBranchTarget>()
                .eqIfPresent(MemColonelBranchTarget::getSysCode, colonelTarget.getSysCode())
                .eqIfPresent(MemColonelBranchTarget::getColonelId, colonelTarget.getColonelId())
                .eqIfPresent(MemColonelBranchTarget::getTargetYear, colonelTarget.getTargetYear())
                .eqIfPresent(MemColonelBranchTarget::getTargetMonth, colonelTarget.getTargetMonth()));
    }


    /**
     * 查询业务员门店目标设置全部数据
     * @param reqVO
     * @return
     */
    List<ColonelBranchTargetRespVO> getMemColonelBranchTargetPageTotal(MemColonelBranchTargetPageReqVO reqVO);

    /**
     * 分页查询业务员门店目标设置
     * @param reqVO
     * @param page
     * @return
     */
    Page<ColonelBranchTargetRespVO> getMemColonelBranchTargetPage(@Param("reqVO") MemColonelBranchTargetPageReqVO reqVO, @Param("page") Page<ColonelBranchTargetRespVO> page);

    /**
     * 更新业务员门店目标设置金额
     * @param reqVo
     * @return
     */
    int updateMemColonelBranchTarget(ColonelAppBranchTargetUpdateVO reqVo);

    /**
     * 根据业务员ID,目标年和月 获取门店目标ID集合
     * @param colonelId,year,month
     * @return
     */
    default List<Long> getBranchTargetIds(Long colonelId, String year, String month) {
        return selectList(new LambdaQueryWrapperX<MemColonelBranchTarget>()
                .eq(MemColonelBranchTarget::getColonelId, colonelId)
                .eq(MemColonelBranchTarget::getTargetYear, year)
                .eq(MemColonelBranchTarget::getTargetMonth, month))
                .stream().map(MemColonelBranchTarget::getColonelBranchTargetId).collect(Collectors.toList());
    }

    /**
     * 获取门店月任务目标
     */
    default MemColonelBranchTarget selectBranchTagByYearMonth(String year, String month, Long branchId) {
        return selectOne(new LambdaQueryWrapperX<MemColonelBranchTarget>()
                .eq(MemColonelBranchTarget::getBranchId, branchId)
                .eq(MemColonelBranchTarget::getTargetYear, year)
                .eq(MemColonelBranchTarget::getTargetMonth, month)
                .eq(MemColonelBranchTarget::getDelFlag, DelFlagConstants.NORMAL)
        );
    }
}
