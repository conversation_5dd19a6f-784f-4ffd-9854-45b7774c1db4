package com.zksr.member.controller.branch.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店下拉数据请求
 * @date 2025/1/23 17:04
 */
@Data
public class BranchDropdownReqVO {

    @ApiModelProperty("门店名称")
    private String branch;

    @ApiModelProperty("城市ID集合")
    private List<Long> areaIdList;

    @ApiModelProperty("简单字段")
    private Boolean simpleFields;
}
