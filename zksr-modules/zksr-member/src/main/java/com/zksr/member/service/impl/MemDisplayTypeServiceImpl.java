package com.zksr.member.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemDisplayTypeMapper;
import com.zksr.member.domain.MemDisplayType;
import com.zksr.member.controller.displayType.vo.MemDisplayTypePageReqVO;
import com.zksr.member.controller.displayType.vo.MemDisplayTypeSaveReqVO;
import com.zksr.member.service.IMemDisplayTypeService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 陈列类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@Service
public class MemDisplayTypeServiceImpl implements IMemDisplayTypeService {
    @Autowired
    private MemDisplayTypeMapper memDisplayTypeMapper;

    /**
     * 新增陈列类型
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemDisplayType(MemDisplayTypeSaveReqVO createReqVO) {
        // 插入
        MemDisplayType memDisplayType = HutoolBeanUtils.toBean(createReqVO, MemDisplayType.class);
        memDisplayTypeMapper.insert(memDisplayType);
        // 返回
        return memDisplayType.getDisplayId();
    }

    /**
     * 修改陈列类型
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemDisplayType(MemDisplayTypeSaveReqVO updateReqVO) {
        memDisplayTypeMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, MemDisplayType.class));
    }

    /**
     * 删除陈列类型
     *
     * @param displayId 陈列类型编号，主键
     */
    @Override
    public void deleteMemDisplayType(Long displayId) {
        // 删除
        memDisplayTypeMapper.deleteById(displayId);
    }

    /**
     * 批量删除陈列类型
     *
     * @param displayIds 需要删除的陈列类型主键
     * @return 结果
     */
    @Override
    public void deleteMemDisplayTypeByDisplayIds(Long[] displayIds) {
        for(Long displayId : displayIds){
            this.deleteMemDisplayType(displayId);
        }
    }

    /**
     * 获得陈列类型
     *
     * @param displayId 陈列类型编号，主键
     * @return 陈列类型
     */
    @Override
    public MemDisplayType getMemDisplayType(Long displayId) {
        return memDisplayTypeMapper.selectById(displayId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemDisplayType> getMemDisplayTypePage(MemDisplayTypePageReqVO pageReqVO) {
        return memDisplayTypeMapper.selectPage(pageReqVO);
    }

    private void validateMemDisplayTypeExists(Long displayId) {
        if (memDisplayTypeMapper.selectById(displayId) == null) {
            throw exception(MEM_DISPLAY_TYPE_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 陈列类型 TODO 补充编号 ==========
    // ErrorCode MEM_DISPLAY_TYPE_NOT_EXISTS = new ErrorCode(TODO 补充编号, "陈列类型不存在");


}
