package com.zksr.member.mapper;

import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.controller.colonelTarget.vo.MemColonelActualSaleRespVO;
import com.zksr.member.domain.MemColonel;
import com.zksr.member.domain.MemColonelDaySettle;
import com.zksr.member.domain.MemColonelMonthSettle;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * 业务员 业务月结表Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Mapper
public interface MemColonelMonthSettleMapper extends BaseMapperX<MemColonelMonthSettle> {

    /**
     *  根据业务员id和系统编码查询 业务员月结信息
     * @param colonelId 业务员ID
     * @param sysCode 平台编码
     * @return
     */
    default MemColonelMonthSettle getInfoByColonelIdAndSysCode(Long colonelId, Long sysCode, String settleMonthDate){
        return selectOne(new LambdaQueryWrapperX<MemColonelMonthSettle>()
                .eqIfPresent(MemColonelMonthSettle::getColonelId,colonelId)
                .eqIfPresent(MemColonelMonthSettle::getSysCode, sysCode)
                .eqIfPresent(MemColonelMonthSettle::getSettleMonthDate, settleMonthDate));
    }

    /**
     *  根据业务员id和系统编码查询 业务员月结信息（集合列表）
     * @param colonelId 业务员ID
     * @param sysCode 平台编码
     * @return
     */
    default List<MemColonelMonthSettle> getListInfoByColonelIdAndSysCode(Set<Long> colonelId, Long sysCode, String settleMonthDate){
        return selectList(new LambdaQueryWrapperX<MemColonelMonthSettle>()
                .inIfPresent(MemColonelMonthSettle::getColonelId,colonelId)
                .eqIfPresent(MemColonelMonthSettle::getSysCode, sysCode)
                .eqIfPresent(MemColonelMonthSettle::getSettleMonthDate, settleMonthDate));
    }

    /**
     * 查询业务员月结信息
     * @param colonelIds 业务员ID集合
     * @param settleMonthDates 月结日期集合
     * @return
     */
    List<MemColonelActualSaleRespVO> getColonelSettleMonthList(@Param("colonelIds") List<Long> colonelIds, @Param("settleMonthDates") List<String> settleMonthDates);

}
