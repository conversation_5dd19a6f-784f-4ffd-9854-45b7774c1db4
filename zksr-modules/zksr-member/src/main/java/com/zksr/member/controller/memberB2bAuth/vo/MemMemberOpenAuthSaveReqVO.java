package com.zksr.member.controller.memberB2bAuth.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * b2b openid 认证对象 mem_member_open_auth
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Data
@ApiModel("b2b openid 认证 - mem_member_open_auth分页 Request VO")
public class MemMemberOpenAuthSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "小程序appid")
    private Long b2bAuthOpenId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 微信小程序openid */
    @Excel(name = "微信小程序openid")
    @ApiModelProperty(value = "微信小程序openid")
    private String openid;

    /** 门店ID */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    /** 0-未授权, 1-已认证授权 */
    @Excel(name = "0-未授权, 1-已认证授权")
    @ApiModelProperty(value = "0-未授权, 1-已认证授权")
    private Integer authState;

    /** 小程序appid */
    @Excel(name = "小程序appid")
    @ApiModelProperty(value = "小程序appid")
    private String appid;

}
