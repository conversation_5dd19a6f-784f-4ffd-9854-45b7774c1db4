package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.domain.MemDisplayDelivery;
import com.zksr.member.controller.displayDelivery.vo.MemDisplayDeliveryPageReqVO;
import com.zksr.member.controller.displayDelivery.vo.MemDisplayDeliverySaveReqVO;

/**
 * 陈列计划兑付Service接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
public interface IMemDisplayDeliveryService {

    /**
     * 新增陈列计划兑付
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemDisplayDelivery(@Valid MemDisplayDeliverySaveReqVO createReqVO);

    /**
     * 修改陈列计划兑付
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemDisplayDelivery(@Valid MemDisplayDeliverySaveReqVO updateReqVO);

    /**
     * 删除陈列计划兑付
     *
     * @param deliveryId 主键ID
     */
    public void deleteMemDisplayDelivery(Long deliveryId);

    /**
     * 批量删除陈列计划兑付
     *
     * @param deliveryIds 需要删除的陈列计划兑付主键集合
     * @return 结果
     */
    public void deleteMemDisplayDeliveryByDeliveryIds(Long[] deliveryIds);

    /**
     * 获得陈列计划兑付
     *
     * @param deliveryId 主键ID
     * @return 陈列计划兑付
     */
    public MemDisplayDelivery getMemDisplayDelivery(Long deliveryId);

    /**
     * 获得陈列计划兑付分页
     *
     * @param pageReqVO 分页查询
     * @return 陈列计划兑付分页
     */
    PageResult<MemDisplayDelivery> getMemDisplayDeliveryPage(MemDisplayDeliveryPageReqVO pageReqVO);

}
