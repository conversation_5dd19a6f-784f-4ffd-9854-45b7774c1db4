package com.zksr.member.controller.colonelTarget;

import javax.validation.Valid;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.member.controller.branch.MemBranchController;
import com.zksr.member.controller.colonelApp.dto.ColonelAppTargetRespDTO;
import com.zksr.member.controller.colonelApp.vo.ColonelAppTargetPageVO;
import com.zksr.member.controller.colonelTarget.vo.*;
import com.zksr.member.service.IMemColonelBranchTargetService;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemColonelTarget;
import com.zksr.member.service.IMemColonelTargetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.common.core.web.page.TableDataInfo;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 业务员目标设置Controller
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Api(tags = "管理后台 - 业务员目标设置接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/colonelTarget")
public class MemColonelTargetController {
    @Autowired
    private IMemColonelTargetService memColonelTargetService;
    @Autowired
    private IMemColonelBranchTargetService memColonelBranchTargetService;

    /**
     * 新增业务员目标设置
     */
    @ApiOperation(value = "新增业务员目标设置", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "业务员目标设置", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemColonelTargetSaveReqVO createReqVO) {
        return success(memColonelTargetService.insertMemColonelTarget(createReqVO));
    }

    @ApiOperation(value = "新增批量业务员目标设置", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "业务员目标设置", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addBatch")
    public CommonResult<Boolean> addBatch(@Valid @RequestBody List<MemColonelTargetSaveReqVO> createReqVO) {
        memColonelTargetService.insertBatchMemColonelTarget(createReqVO);
        return success(Boolean.TRUE);
    }

    /**
     * 修改业务员目标设置
     */
    @ApiOperation(value = "修改业务员目标设置", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "业务员目标设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemColonelTargetSaveReqVO updateReqVO) {
            memColonelTargetService.updateMemColonelTarget(updateReqVO);
        return success(true);
    }

    /**
     * 删除业务员目标设置
     */
    @ApiOperation(value = "删除业务员目标设置", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "业务员目标设置", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public CommonResult<Boolean> remove(@RequestBody Long[] colonelTargetIds) {
        memColonelTargetService.deleteMemColonelTargetByColonelTargetIds(colonelTargetIds);
        return success(true);
    }

    /**
     * 获取业务员目标设置详细信息
     */
    @ApiOperation(value = "获得业务员目标设置详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{colonelTargetId}")
    public CommonResult<MemColonelTargetRespVO> getInfo(@PathVariable("colonelTargetId") Long colonelTargetId) {
        MemColonelTarget memColonelTarget = memColonelTargetService.getMemColonelTarget(colonelTargetId);
        return success(HutoolBeanUtils.toBean(memColonelTarget, MemColonelTargetRespVO.class));
    }

    /**
     * 分页查询业务员目标设置
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得业务员目标设置分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(dcAlias = "colonel", dcFieldAlias = SystemConstants.AREA_ID)
    public CommonResult<PageResult<MemColonelTargetRespVO>> getPage(@Valid MemColonelTargetPageReqVO pageReqVO) {
        return success(memColonelTargetService.getMemColonelTargetPage(pageReqVO));
    }

    /**
     * 停用门店
     */
    @ApiOperation(value = "停用", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "停用", businessType = BusinessType.UPDATE)
    @PutMapping("/disable/{colonelTargetId}")
    public CommonResult<Boolean> disable(@ApiParam(name = "colonelTargetId", value = "ID", required = true) @PathVariable("colonelTargetId") Long colonelTargetId) {
        memColonelTargetService.disable(colonelTargetId);
        return success(true);
    }

    /**
     * 启用
     */
    @ApiOperation(value = "启用", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "启用", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{colonelTargetId}")
    public CommonResult<Boolean> enable(@ApiParam(name = "colonelTargetId", value = "ID", required = true) @PathVariable("colonelTargetId") Long colonelTargetId) {
        memColonelTargetService.enable(colonelTargetId);
        return success(true);
    }

    @ApiOperation(value = "获取业务员近两月的实际销售数据", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping("/getColonelActualSalesInfo")
    public CommonResult<List<MemColonelActualSaleRespVO>> getColonelActualSalesInfo(@Valid MemColonelTargetPageReqVO pageReqVO) {
        return success(memColonelTargetService.getColonelActualSalesInfo(pageReqVO));
    }

    @ApiOperation(value = "获取业务员门店目标查询", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping("/getColonelBranchPage")
    public CommonResult<MemColonelBranchTargetRespVO> getColonelBranchPage(@Valid MemColonelBranchTargetPageReqVO pageReqVO) {
        pageReqVO.setStatus(NumberPool.LOWER_GROUND);
        return success(memColonelBranchTargetService.getColonelBranchTargetPage(pageReqVO));
    }

    /**
     * 批量完成业务员目标设置
     */
    @ApiOperation(value = "批量完成业务员目标设置", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.COMPLETED)
    @RequiresPermissions(Permissions.COMPLETED)
    @Log(title = "批量完成业务员目标设置", businessType = BusinessType.DELETE)
    @PostMapping("/settingCompleted")
    public CommonResult<Boolean> settingCompleted(@RequestBody Long[] colonelTargetIds) {
        memColonelTargetService.settingCompleted(colonelTargetIds);
        return success(true);
    }

    @ApiOperation(value = "业务员PC目标查询报表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping("/getColonelTargetPage")
    public CommonResult<ColonelAppTargetRespDTO> getColonelTargetPage(@Valid ColonelAppTargetPageVO pageVO) {
        return success(memColonelTargetService.getColonelTargetReportPage(pageVO));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:colonelTarget:add";
        /** 编辑 */
        public static final String EDIT = "member:colonelTarget:edit";
        /** 删除 */
        public static final String DELETE = "member:colonelTarget:remove";
        /** 列表 */
        public static final String LIST = "member:colonelTarget:list";
        /** 查询 */
        public static final String GET = "member:colonelTarget:query";
        /** 停用 */
        public static final String DISABLE = "member:colonelTarget:disable";
        /** 启用 */
        public static final String ENABLE = "member:colonelTarget:enable";
        /** 设置完成 */
        public static final String COMPLETED = "member:colonelTarget:completed";
    }

}
