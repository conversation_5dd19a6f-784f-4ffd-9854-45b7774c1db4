package com.zksr.member.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.elasticsearch.domain.EsColonelAppBranch;
import com.zksr.common.elasticsearch.service.EsColonelAppBranchService;
import com.zksr.member.controller.colonelApp.vo.ColonelAppBranchTargetUpdateVO;
import com.zksr.member.controller.colonelTarget.vo.ColonelBranchTargetRespVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelActualSaleRespVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelBranchTargetPageReqVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelBranchTargetRespVO;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.domain.MemColonelBranchTarget;
import com.zksr.member.domain.MemColonelTarget;
import com.zksr.member.mapper.MemBranchMapper;
import com.zksr.member.mapper.MemColonelBranchTargetMapper;
import com.zksr.member.mapper.MemColonelMonthSettleMapper;
import com.zksr.member.service.IMemColonelBranchTargetService;
import com.zksr.member.service.IMemberCacheService;
import com.zksr.system.api.area.dto.AreaDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务员门店目标设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@Service
public class IMemColonelBranchTargetServiceImpl implements IMemColonelBranchTargetService {
    @Autowired
    private MemColonelBranchTargetMapper memColonelBranchTargetMapper;
    @Autowired
    private MemBranchMapper memBranchMapper;
    @Autowired
    private MemColonelMonthSettleMapper memColonelMonthSettleMapper;


    @Autowired
    private EsColonelAppBranchService colonelAppBranchService;
    @Autowired
    private IMemberCacheService memberCacheService;
    @Override
    public List<Long> insertMemColonelBranchTarget(List<MemColonelTarget> memColonelTargetList) {

        List<Long> colonelIds = memColonelTargetList.stream().map(MemColonelTarget::getColonelId).collect(Collectors.toList());
        // 根据业务员查询全部客户
        List<MemBranch> memBranchList = memBranchMapper.selectMemBranchByColonels(colonelIds);
        Map<Long, List<MemBranch>> memBranchMap = CollectionUtils.convertMultiMap(memBranchList, MemBranch::getColonelId);


        // 业务员上月实际销售信息
        List<String> targetMonths = new ArrayList<>();
        targetMonths.add(DateUtils.getYearMonthRange(-1, DateUtils.getNowDate()));
        List<MemColonelActualSaleRespVO> colonelActualSaleRespVOS = memColonelMonthSettleMapper.getColonelSettleMonthList(colonelIds, targetMonths);
        Map<Long, MemColonelActualSaleRespVO> colonelActualSaleRespVOMap = CollectionUtils.convertMap(colonelActualSaleRespVOS, MemColonelActualSaleRespVO::getColonelId);

        List<MemColonelBranchTarget> colonelBranchTargets = new ArrayList<>();

        memColonelTargetList.forEach(colonelTarget -> {
            if (memBranchMap.containsKey(colonelTarget.getColonelId())) {
                // 业务员上月实际总销售额
//                BigDecimal saleAmt = colonelActualSaleRespVOMap.containsKey(colonelTarget.getColonelId()) ? colonelActualSaleRespVOMap.get(colonelTarget.getColonelId()).getSaleAmt() : BigDecimal.ZERO;

                colonelBranchTargets.addAll(
                        memBranchMap.get(colonelTarget.getColonelId()).stream().map(branch -> {
                            // 业务员门店目标销售额
                            BigDecimal targetSaleAmt = getTargetSaleAmt(colonelTarget, branch.getBranchId(), colonelActualSaleRespVOMap);

                            return MemColonelBranchTarget.builder()
                                    .colonelId(colonelTarget.getColonelId())
                                    .branchId(branch.getBranchId())
                                    .branchName(branch.getBranchName())
                                    .branchContactName(branch.getContactName())
                                    .branchContactPhone(branch.getContactPhone())
                                    .targetYear(colonelTarget.getTargetYear())
                                    .targetMonth(colonelTarget.getTargetMonth())
                                    .targetSalesMoney(targetSaleAmt)
                                    .build();
                        }).collect(Collectors.toList())
                );
            }
        });
        // 批量新增业务员门店目标设置
        memColonelBranchTargetMapper.insertBatch(colonelBranchTargets);
        return colonelBranchTargets.stream().map(MemColonelBranchTarget::getColonelBranchTargetId).collect(Collectors.toList());
    }

    @Override
    public void updateMemColonelBranchTarget(MemColonelTarget memColonelTarget) {
       List<MemColonelBranchTarget> colonelBranchTargets = memColonelBranchTargetMapper.getListByColonelTarget(memColonelTarget);
       if (ToolUtil.isEmpty(colonelBranchTargets) || colonelBranchTargets.isEmpty()) {
           return;
       }
        // 业务员上月实际销售信息
        List<String> targetMonths = new ArrayList<>();
        targetMonths.add(DateUtils.getYearMonthRange(-1, DateUtils.getNowDate()));
        List<MemColonelActualSaleRespVO> colonelActualSaleRespVOS = memColonelMonthSettleMapper.getColonelSettleMonthList(Collections.singletonList(memColonelTarget.getColonelId()), targetMonths);
        Map<Long, MemColonelActualSaleRespVO> colonelActualSaleRespVOMap = CollectionUtils.convertMap(colonelActualSaleRespVOS, MemColonelActualSaleRespVO::getColonelId);

        colonelBranchTargets.forEach(colonelBranchTarget -> {
            memColonelTarget.setSysCode(colonelBranchTarget.getSysCode());
            colonelBranchTarget.setTargetSalesMoney(getTargetSaleAmt(memColonelTarget, colonelBranchTarget.getBranchId(), colonelActualSaleRespVOMap));
        });
        memColonelBranchTargetMapper.updateBatch(colonelBranchTargets);
    }


    @Override
    public MemColonelBranchTargetRespVO getColonelBranchTargetPage(MemColonelBranchTargetPageReqVO pageReqVO) {
        MemColonelBranchTargetRespVO resultRespVo = new MemColonelBranchTargetRespVO();
        // 业务员门店目标分页总数据
        List<ColonelBranchTargetRespVO> colonelBranchTargetList = memColonelBranchTargetMapper.getMemColonelBranchTargetPageTotal(pageReqVO);
        if (ToolUtil.isEmpty(colonelBranchTargetList) || colonelBranchTargetList.isEmpty()) {
            return resultRespVo;
        }

        // 分页查询业务员门店目标数据
        Page<ColonelBranchTargetRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<ColonelBranchTargetRespVO> colonelBranchTargetPage = memColonelBranchTargetMapper.getMemColonelBranchTargetPage(pageReqVO, page);

        resultRespVo.setClonelBranchTargetList(colonelBranchTargetPage.getRecords());
        resultRespVo.setTotal(colonelBranchTargetPage.getTotal());

        String date = pageReqVO.getTargetYear() + StringPool.DASH + pageReqVO.getTargetMonth();
        Map<Long, BigDecimal> esBranchSaleAmtMap = new HashMap<>();

        // 查询月份总销售金额 业务员实际销售总金额
        BigDecimal saleAmtTotal = colonelBranchTargetList.stream().map(colonelBranchTarget -> {
            BigDecimal saleAmt = getBranchSaleAmt(colonelBranchTarget.getBranchId(), colonelBranchTarget.getSysCode(), date);
            esBranchSaleAmtMap.put(colonelBranchTarget.getBranchId(), saleAmt);
            return saleAmt;
        }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        // 查询月份每个门店的实际销售额
        resultRespVo.getClonelBranchTargetList().forEach(colonelBranchTarget -> {
            // 当前查询月份的销售额
            colonelBranchTarget.setSalesMoney(esBranchSaleAmtMap.get(colonelBranchTarget.getBranchId()));
            // 门店近一月完成总销售额
            colonelBranchTarget.setBranchLastOneMonthSalesMoney(getBranchSaleAmt(colonelBranchTarget.getBranchId(), colonelBranchTarget.getSysCode(), DateUtils.getYearMonthRange(-1, DateUtils.getNowDate())));
            // 门店近两月完成总销售额
            colonelBranchTarget.setBranchLastTwoMonthSalesMoney(getBranchSaleAmt(colonelBranchTarget.getBranchId(), colonelBranchTarget.getSysCode(), DateUtils.getYearMonthRange(-2, DateUtils.getNowDate())));

            AreaDTO areaDTO = memberCacheService.getAreaDto(colonelBranchTarget.getAreaId());
            if (ToolUtil.isNotEmpty(areaDTO)) {
                colonelBranchTarget.setAreaName(areaDTO.getAreaName());
            }
        });

        resultRespVo.setSaleAmtTotal(saleAmtTotal);
        // 业务员目标总销售额
        resultRespVo.setTargetAmtTotal(colonelBranchTargetPage.getRecords().get(NumberPool.INT_ZERO).getColonelTargetSalesMoney());

        // 门店自设目标销售额
        BigDecimal branchTargetAmtTotal = colonelBranchTargetList.stream()
                .map(ColonelBranchTargetRespVO::getTargetSalesMoney)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        resultRespVo.setBranchTargetAmtTotal(branchTargetAmtTotal);
        resultRespVo.setSaleAmtTotal(saleAmtTotal);
        return resultRespVo;
    }

    /**
     * 得到门店目标销售额
     * @param memColonelTarget
     * @param branchId
     * @param colonelActualSaleRespVOMap
     * @return
     */
    private BigDecimal getTargetSaleAmt(MemColonelTarget memColonelTarget, Long branchId, Map<Long, MemColonelActualSaleRespVO> colonelActualSaleRespVOMap) {
        // 业务员上月实际总销售额
        BigDecimal saleAmt = colonelActualSaleRespVOMap.containsKey(memColonelTarget.getColonelId()) ? colonelActualSaleRespVOMap.get(memColonelTarget.getColonelId()).getSaleAmt() : BigDecimal.ONE;
        if (NumberUtil.isGreaterOrEqual( BigDecimal.ZERO, saleAmt)) {
            return BigDecimal.ZERO;
        }
        BigDecimal lastMonthSaleAmt = getBranchSaleAmt(branchId, memColonelTarget.getSysCode(), DateUtils.getYearMonthRange(-1, DateUtils.getNowDate()));
        return memColonelTarget.getSalesMoney().multiply(lastMonthSaleAmt.divide(saleAmt, RoundingMode.HALF_UP))
                .setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
    }


    /**
     * 获取当月门店实际销售额
     * @param branchId 门店Id
     * @param sysCode 平台商ID
     * @param date 日期(年月) YYYY-MM
     * @return
     */
    @Override
    public BigDecimal getBranchSaleAmt(Long branchId, Long sysCode, String date) {
        //设置ES的ID
        String id = branchId + StringPool.UNDERSCORE + sysCode + StringPool.UNDERSCORE + date;
        //查询ES 获取当前客户信息
        EsColonelAppBranch colonelAppBranch = colonelAppBranchService.seachColonelAppBranchById(id);
        // 门店本月月实际销售额
        BigDecimal nowMonthSaleAmt = BigDecimal.ZERO;
        if (ToolUtil.isNotEmpty(colonelAppBranch) && ToolUtil.isNotEmpty(colonelAppBranch.getNowMonthOrderAmt())) {
            nowMonthSaleAmt = colonelAppBranch.getNowMonthOrderAmt();
        }
        return nowMonthSaleAmt;
    }

    @Transactional
    @Override
    public void updateColoenBranchTarget(List<ColonelAppBranchTargetUpdateVO> reqVO) {
        reqVO.forEach(updateVO -> {
            memColonelBranchTargetMapper.updateMemColonelBranchTarget(updateVO);
        });
    }

    @Override
    public List<Long> getBranchTargetIds(Long colonelId, String year, String month) {
        return memColonelBranchTargetMapper.getBranchTargetIds(colonelId, year, month);
    }

    @Override
    public void deleteMemColonelBranchTarget(Long colonelBranchTargetId) {
        memColonelBranchTargetMapper.deleteById(colonelBranchTargetId);
    }

    @Override
    public void deleteMemColonelBranchTargetByColonelBranchTargets(List<Long> colonelBranchTargetsIds) {
        memColonelBranchTargetMapper.deleteBatchIds(colonelBranchTargetsIds);
    }


}



