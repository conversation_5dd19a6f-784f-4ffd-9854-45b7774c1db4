package com.zksr.member.controller.colonelApp;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.alicp.jetcache.Cache;
import com.zksr.account.api.account.vo.ColonelAccountRespVO;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantRegisterReqVO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantRespVO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantUploadPicReqVO;
import com.zksr.account.api.withdraw.dto.SaveWithdrawDTO;
import com.zksr.account.api.withdraw.dto.WithdrawDTO;
import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.SourceType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.exception.enums.GlobalErrorCodeConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.file.FileUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageParam;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.file.api.file.FileApi;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.branchLifecycle.dto.ColonelBranchLifecycleQtyDTO;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.member.api.colonel.vo.MemShopAppRecodeReqVO;
import com.zksr.member.api.colonelApp.dto.PageDataDTO;
import com.zksr.member.api.command.dto.CommandAddOrderRespDTO;
import com.zksr.member.api.command.vo.CommandAddOrderVO;
import com.zksr.member.api.command.vo.CommandPageReqVO;
import com.zksr.member.api.command.vo.CommandSaveVO;
import com.zksr.member.api.command.vo.CommandSkuAddShopCartSaveVO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyPageReqVO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyRespVO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidySaveReqVO;
import com.zksr.member.controller.branch.MemBranchController;
import com.zksr.member.controller.branch.vo.MemBranchDirectReqVo;
import com.zksr.member.controller.branch.vo.MemBranchPageReqVO;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipPageReqVO;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipRespVO;
import com.zksr.member.controller.colonelApp.dto.ColonelAppPolicyRespDTO;
import com.zksr.member.controller.colonelApp.dto.ColonelAppTargetRespDTO;
import com.zksr.member.controller.colonelApp.dto.MemColonelSendCouponDTO;
import com.zksr.member.controller.colonelApp.vo.*;
import com.zksr.member.controller.colonelTarget.vo.MemColonelBranchTargetPageReqVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelBranchTargetRespVO;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogRespVO;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogSaveReqVO;
import com.zksr.member.convert.ColonelTidy.MemColonelTidyConvert;
import com.zksr.member.convert.colonel.ColonelAppConvert;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.domain.MemColonelTidy;
import com.zksr.member.service.*;
import com.zksr.promotion.api.coupon.dto.ColonelQuotaDTO;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.api.coupon.dto.CouponReceiveResultDTO;
import com.zksr.promotion.api.coupon.vo.ColonelAppPrmCouponTemplatePageReqVO;
import com.zksr.promotion.api.coupon.vo.ColonelAppPrmCouponTemplateRespVO;
import com.zksr.promotion.api.coupon.vo.CouponPageReqVO;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.area.vo.SysAreaCityPageReqVO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.bank.BankChannelApi;
import com.zksr.system.api.bank.vo.SysBankChannelPageReqVO;
import com.zksr.system.api.bank.vo.SysBankChannelRespVO;
import com.zksr.system.api.dcArea.DcAreaApi;
import com.zksr.system.api.dcArea.dto.DcAreaDTO;
import com.zksr.system.api.domain.SysFile;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.BasicSettingPolicyDTO;
import com.zksr.system.api.supplierArea.SupplierAreaApi;
import com.zksr.system.api.supplierArea.dto.SupplierDzwlAreaDTO;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.after.dto.*;
import com.zksr.trade.api.after.vo.*;
import com.zksr.trade.api.express.OrderExpressApi;
import com.zksr.trade.api.express.dto.ExpressResDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.ColonelAppOrderListTotalDTO;
import com.zksr.trade.api.order.dto.TrdOrderPageReqDTO;
import com.zksr.trade.api.order.dto.TrdOrderRespDTO;
import com.zksr.trade.api.order.vo.*;
import com.zksr.member.controller.branch.vo.MemBranchRespVO;
import com.zksr.member.controller.colonelApp.vo.ColonelAppTrdCarRespVO;
import com.zksr.trade.api.order.vo.TrdColonelAppOrderDetailRespVO;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterSaveReqVO;
import com.zksr.system.api.model.LoginUser;
import com.zksr.trade.api.orderSettle.OrderSettleApi;
import com.zksr.trade.api.orderSettle.dto.ColonelFixSettleTotalRespVO;
import com.zksr.trade.api.orderSettle.dto.ColonelFloatSettleTotalReqVO;
import com.zksr.trade.api.orderSettle.dto.ColonelFloatSettleTotalRespVO;
import com.zksr.trade.api.orderSettle.dto.OrderSettleColonelResDTO;
import com.zksr.trade.api.orderSettle.vo.OrderSettlePageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.member.enums.AppErrorCodeConstants.APP_COLONEL_CHECK_COLONEL_ID;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_AREA_ITEM_AREA_NOT_EXISTS;

/**
 * <AUTHOR>
 * @Date 2024/4/16 14:29
 * @业务员AppController
 */
@Api(tags = "业务员app - 业务员App接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/colonelApp")
public class MemColonelAppController {

    @Autowired
    private IMemColonelAppService memColonelAppService;

    @Resource
    private OrderSettleApi orderSettleApi;

    @Autowired
    private IMemberCacheService memberCacheService;

    @Resource
    private FileApi remoteFileApi;

    @Resource
    private PlatformMerchantApi platformMerchantApi;

    @Resource
    private BankChannelApi bankChannelApi;

    @Resource
    private AreaCityApi areaCityApi;

    @Resource
    private SupplierAreaApi supplierAreaApi;

    @Resource
    private OrderApi orderApi;

    @Resource
    private AfterApi afterApi;

    @Autowired
    private IMemColonelBranchTargetService memColonelBranchTargetService;

    @Autowired
    private IMemColonelTargetService memColonelTargetService;
    @Autowired
    private IMemCommandService memCommandService;
    @Autowired
    private IMemColonelBranchZipService memColonelBranchZipServiceImpl;
    @Autowired
    private Cache<Long, BasicSettingPolicyDTO> basicSettingPolicyCache;
    @Autowired
    private IMemBranchService memBranchService;
    @Autowired
    private PartnerPolicyApi partnerPolicyApi;
    @Autowired
    private DcAreaApi remoteDcAreaApi;
    @Autowired
    private BranchApi remoteBranchApi;
    @Autowired
    private OrderExpressApi orderExpressApi;

    @Autowired
    private IMemBranchLifecycleZipService memBranchLifecycleZipService;
    /**
     * 新增门店信息
     */
    @ApiOperation(value = "业务员App-拓店", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD_BRANCH)
    @RequiresPermissions(Permissions.ADD_BRANCH)
    @Log(title = "业务员App-拓店", businessType = BusinessType.INSERT)
    @PostMapping("/addBranchRegister")
    public CommonResult<Boolean> addBranchRegister(@Valid @RequestBody MemBranchRegisterSaveReqVO createReqVO) {
        memColonelAppService.addBranchRegister(createReqVO);
        return success(true);
    }

    /**
     * 扩店校验手机号
     */
    @ApiOperation(value = "扩店校验手机号", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD_BRANCH)
    @RequiresPermissions(Permissions.ADD_BRANCH)
    @GetMapping("/checkAddBranchPhone")
    public CommonResult<Boolean> checkAddBranchPhone(@RequestParam String contactPhone) {
        return success(memColonelAppService.checkAddBranchPhone(contactPhone));
    }



    /**
     * 修改门店信息
     */
    @ApiOperation(value = "修改门店信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD_BRANCH)
    @RequiresPermissions(Permissions.ADD_BRANCH)
    @Log(title = "修改门店信息", businessType = BusinessType.UPDATE)
    @PostMapping("/editColoneBranch")
    public CommonResult<Boolean> editColoneBranch(@Valid @RequestBody MemColonelAppSaveBranchReqVo updateReqVO) {
        memColonelAppService.editColoneBranch(updateReqVO);
        return success(true);
    }

    /**
     * 获取区域城市接口
     *
     */
    @ApiOperation("获取区域城市接口")
    @GetMapping(value = "/cityList")
    public CommonResult<List<AreaDTO>> cityList() {
        //根据sysCode获取 已经分配运营商的城市列表
        List<AreaDTO> areaDTOList =memColonelAppService.getcityList();
        return success(areaDTOList);
    }

    /**
     * 业务员App客户列表
     */
    @GetMapping("/customerList")
    @ApiOperation(value = "业务员App客户列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<PageResult<MemColonelAppCustomerListRespVO>> getCustomerListPage(@Valid MemColonelAppCustomerListPageReqVO pageReqVO) {
        return success(memColonelAppService.getMemColonelAppCustomerPage(pageReqVO));
    }


    /**
     * 分页查询门店信息
     */
    @GetMapping("/branchList")
    @ApiOperation(value = "获得门店信息分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @RequiresPermissions(Permissions.MEEBER)
    @DataScope(dcAlias = "`mem_branch`")
    public CommonResult<PageResult<MemBranchRespVO>> getBranchPage(@Valid MemBranchPageReqVO pageReqVO) {
        if (pageReqVO.getIsPage()==0) pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<MemBranch> pageResult = memColonelAppService.getBranchPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, MemBranchRespVO.class));
    }

    /**
     * 获取业务员信息
     */
    @GetMapping("/getColonel")
    @ApiOperation(value = "获取业务员信息", httpMethod = HttpMethod.GET)
    public CommonResult<ColonelDTO> getColonel() {
        return success(memColonelAppService.getColonel());
    }


    /**
     * 查询我管理的业务员
     */
    @GetMapping("/getColonelStatisticsList")
    @ApiOperation(value = "查询业务员统计列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<PageResult<MemColonelStatisticsListRespVo>> getColonelStatisticsList(@Valid MemColonelStatisticsListReqVo reqVo) {
        PageResult<MemColonelStatisticsListRespVo> colonelList = memColonelAppService.getColonelStatisticsList(reqVo);
        return success(colonelList);
    }


    /**
     * 业务员App 我的代办订单
     */
    @GetMapping("/getPendingOrderList")
    @ApiOperation(value = "业务员App 我的代办订单(销售)", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER)
    @RequiresPermissions(Permissions.ORDER)
    public CommonResult<PageResult<TrdOrderRespDTO>> getPendingOrderList(@Valid TrdOrderPageReqDTO orderPageReqVO) {
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        orderPageReqVO.setStartTime(DateUtils.getDateAdd(-90)); // 开始日期  当前往前推90天
        orderPageReqVO.setEndTime(DateUtil.endOfDay(new Date()));// 到期日期  当前23：59：59
        orderPageReqVO.setColonelId(colonelId);
        return success(orderApi.pageOrderList(orderPageReqVO));
    }

    @GetMapping("/getPendingOrderInfo")
    @ApiOperation(value = "业务员App 我的代办订单(销售)明细", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER)
    @RequiresPermissions(Permissions.ORDER)
    public CommonResult<TrdOrderRespDTO> getPendingOrderInfo(@Valid TrdOrderPageReqDTO orderPageReqVO) {
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        orderPageReqVO.setStartTime(DateUtils.getDateAdd(-90)); // 开始日期  当前往前推90天
        orderPageReqVO.setEndTime(DateUtil.endOfDay(new Date()));// 到期日期  当前23：59：59
        orderPageReqVO.setColonelId(colonelId);
        return orderApi.getOrderInfo(orderPageReqVO);
    }

    @GetMapping("/getPendingAfterOrderList")
    @ApiOperation(value = "业务员App 我的代办订单(售后)", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER)
    @RequiresPermissions(Permissions.ORDER)
    public CommonResult<PageResult<AfterOrderPageRespDTO>> getPendingAfterOrderList(@Valid AfterOrderPageReqVO reqVO) {
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        reqVO.setStartDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getDateAdd(-89))); // 开始日期  当前往前推90天
        reqVO.setEndDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD,DateUtil.endOfDay(new Date())));// 到期日期  当前23：59：59
        reqVO.setColonelId(colonelId);
        return success(afterApi.pageAfter(reqVO));
    }

    @GetMapping("/getPendingAfterOrderInfo")
    @ApiOperation(value = "业务员App 我的代办订单(售后)明细", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER)
    @RequiresPermissions(Permissions.ORDER)
    public CommonResult<AfterOrderPageRespDTO> getPendingAfterOrderInfo(@Valid AfterOrderPageReqVO reqVO) {
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        reqVO.setStartDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getDateAdd(-89))); // 开始日期  当前往前推90天
        reqVO.setEndDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD,DateUtil.endOfDay(new Date())));// 到期日期  当前23：59：59
        reqVO.setColonelId(colonelId);
        return afterApi.getAfterOrderInfo(reqVO);
    }

    /**
     * 业务员App 我的代办订单角标数量
     */
    @GetMapping("/getMyPendingOrderStatusQty")
    @ApiOperation(value = "业务员App 我的代办订单角标数量", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER)
    @RequiresPermissions(Permissions.ORDER)
    public CommonResult<OrderStatusVO> getMyPendingOrderStatusQty() {
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        return success(memberCacheService.getOrderStatusQty(colonelId));
    }

    /**
     * 获取选中业务员详情信息
     */
    @GetMapping("/getColonelById")
    @ApiOperation(value = "获取选中业务员详情信息", httpMethod = HttpMethod.GET)
    public CommonResult<ColonelDTO> getColonelById(@RequestParam(value = "colonelId") Long colonelId) {
        return success(memColonelAppService.getColonel(colonelId));
    }

    /**
     * 查询我管理的业务员
     */
    @GetMapping("/getManagementClerkList")
    @ApiOperation(value = "查询我管理的业务员", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DEVELOPMENT_SALESMAN)
    @RequiresPermissions(Permissions.DEVELOPMENT_SALESMAN)
    public CommonResult<PageResult<ColonelDTO>> getManagementClerkList(@Valid MemColonelPageReqVO pageReqVO) {
        PageResult<ColonelDTO> managementClerkList = memColonelAppService.getManagementClerkList(pageReqVO);
        return success(managementClerkList);
    }

    /**-
     * 查询我发展的业务员
     */
    @GetMapping("/getDevelopmentSalesmanList")
    @ApiOperation(value = "查询我发展的业务员", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DEVELOPMENT_SALESMAN)
    @RequiresPermissions(Permissions.DEVELOPMENT_SALESMAN)
    public CommonResult<PageResult<ColonelDTO>> getDevelopmentSalesmanList(@Valid MemColonelPageReqVO pageReqVO) {
        PageResult<ColonelDTO> developmentSalesmanList = memColonelAppService.getDevelopmentSalesmanList(pageReqVO);
        return success(developmentSalesmanList);
    }

    /**
     * 新增业务员
     */
    @ApiOperation(value = "新增业务员", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.DEVELOPMENT_SALESMAN)
    @RequiresPermissions(Permissions.DEVELOPMENT_SALESMAN)
    @Log(title = "新增业务员", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addColonel")
    public CommonResult<Long> addColonel(@Valid @RequestBody MemColonelSaveReqVO createReqVO) {
        return success( memColonelAppService.addColonel(createReqVO));
    }

    /**
     * 业务员App订单列表
     */
    @GetMapping("/orderList")
    @ApiOperation(value = "业务员App订单列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER)
    @RequiresPermissions(Permissions.ORDER)
    public CommonResult<ColonelAppOrderListTotalDTO> getOrderListPage(@Valid TrdColonelAppOrderListPageReqVO pageReqVO) {
        return success(memColonelAppService.getMemColonelOrderListPage(pageReqVO));
    }

    /**
     * 获取订单详细信息
     */
    @ApiOperation(value = "获得订单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER)
    @RequiresPermissions(Permissions.ORDER)
    @GetMapping(value = "/getOrderDetail")
    public CommonResult<List<TrdColonelAppOrderDetailRespVO>> getOrderDetail(@RequestParam(value = "orderId") Long orderId,
                                                                             @RequestParam(value = "orderType") String orderType) {
        List<TrdColonelAppOrderDetailRespVO> result =  memColonelAppService.getOrderDetail(orderId, orderType);

       return success(HutoolBeanUtils.toBean(result, TrdColonelAppOrderDetailRespVO.class));
    }

    /**
     * 获取老板购物车
     */
    @ApiOperation(value = "获取老板购物车", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER)
    @RequiresPermissions(Permissions.ORDER)
    @GetMapping(value = "/getBranchShoppingCart")
    public CommonResult<List<ColonelAppTrdCarRespVO>> getBranchShoppingCart(@RequestParam(value = "branchId") Long branchId) {
        return success(memColonelAppService.getBranchShoppingCart(branchId));
    }

    /**
     * 业务员提成统计
     */
    @ApiOperation(value = "固定业务员提成统计", httpMethod = HttpMethod.GET, notes = Permissions.ORDER_SETTLE)
    @RequiresPermissions(Permissions.ORDER_SETTLE)
    @GetMapping(value = "/getFixSettleTotal")
    public CommonResult<ColonelFixSettleTotalRespVO> getSettleTotal() {
        return orderSettleApi.getColonelSettleTotal(SecurityUtils.getLoginUser().getColonelId());
    }

    /**
     * 业务员提成统计
     */
    @ApiOperation(value = "范围业务员提成统计", httpMethod = HttpMethod.POST, notes = Permissions.ORDER_SETTLE)
    @RequiresPermissions(Permissions.ORDER_SETTLE)
    @PostMapping(value = "/getFloatSettleTotal")
    public CommonResult<ColonelFloatSettleTotalRespVO> getFloatSettleTotal(@RequestBody ColonelFloatSettleTotalReqVO reqVO) {
        reqVO.setColonelId(SecurityUtils.getLoginUser().getColonelId());
        return orderSettleApi.getColonelSettleTotalRange(reqVO);
    }


    /**
     * 获取业务员订单提成明细
     */
    @ApiOperation(value = "获取业务员订单提成明细", httpMethod = HttpMethod.GET, notes = Permissions.ORDER_SETTLE)
    @RequiresPermissions(Permissions.ORDER_SETTLE)
    @GetMapping(value = "/getColonelOrderSettleInfoPage")
    public CommonResult<PageResult<OrderSettleColonelResDTO>> getColonelOrderSettleInfoPage(@Valid OrderSettlePageVO pageVO) {
        LoginUser user = SecurityUtils.getLoginUser(); // 当前登录用户
        pageVO.setMerchantId(user.getSysUser().getColonelId());
        pageVO.setMerchantType("colonel");
        return success(orderSettleApi.getColonelOrderSettleInfoPage(pageVO));
    }

    /**
     * 获取业务员门店详情信息
     */
    @GetMapping("/getColonelBranchDetail")
    @ApiOperation(value = "获取业务员门店详情信息", httpMethod = HttpMethod.GET)
    public CommonResult<MemBranchRespVO> getColonelBranchDetail(@RequestParam(value = "branchId") Long branchId) {
        return success(memColonelAppService.getColonelBranchDetail(branchId));
    }


    /**
     * 获取业务员账户
     */
    @ApiOperation(value = "获取业务员账户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER_SETTLE)
    @RequiresPermissions(Permissions.ORDER_SETTLE)
    @PostMapping(value = "/queryColonelAccount")
    public CommonResult<ColonelAccountRespVO> queryColonelAccount() {
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        return success(memColonelAppService.getColonelAccount(colonelId));
    }

    /**
     * 新增入业务员提现单
     */
    @ApiOperation(value = "新增入业务员提现单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER_SETTLE)
    @RequiresPermissions(Permissions.ORDER_SETTLE)
    @Log(title = "新增入业务员提现单", businessType = BusinessType.INSERT)
    @PostMapping(value = "/withdraw")
    public CommonResult<Long> withdraw(@Valid @RequestBody SaveWithdrawDTO createReqVO) {
        createReqVO.setMerchantId(SecurityUtils.getLoginUser().getColonelId());
        createReqVO.setMerchantType(MerchantTypeEnum.COLONEL.getType());
        // 创建入业务员提现单
        return success(memColonelAppService.saveWithdraw(createReqVO));
    }

    /**
     *
     * 获取入业务员账户提现单详细信息
     */
    @ApiOperation(value = "获得入业务员账户提现单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER_SETTLE)
    @RequiresPermissions(Permissions.ORDER_SETTLE)
    @GetMapping(value = "/getWithdrawInfo/{withdrawId}")
    public CommonResult<WithdrawDTO> getWithdrawInfo(@PathVariable("withdrawId") Long withdrawId) {
        return success(memColonelAppService.getWithdrawInfo(withdrawId));
    }

    /**
     * 获取入业务员提现列表
     */
    @GetMapping("/withdrawPage")
    @ApiOperation(value = "获得入业务员账户提现单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER_SETTLE)
    @RequiresPermissions(Permissions.ORDER_SETTLE)
    public CommonResult<PageResult<WithdrawDTO>> withdrawPage(@Valid AccWithdrawPageReqVO pageReqVO) {
        // 入业务员 / 业务员
        pageReqVO.setMerchantTypeList(ListUtil.toList(MerchantTypeEnum.COLONEL.getType()));
        pageReqVO.setMerchantId(SecurityUtils.getLoginUser().getColonelId());
        PageResult<WithdrawDTO> pageResult = memColonelAppService.getAccWithdrawPage(pageReqVO);
        return success(pageResult);
    }

    /**
     * 新增业务员拜访日志
     */
    @ApiOperation(value = "新增业务员拜访日志", httpMethod = HttpMethod.POST)
    @RequiresPermissions(Permissions.MEEBER)
    @Log(title = "业务员拜访日志", businessType = BusinessType.INSERT)
    @PostMapping("/addColonelAppVisitLog")
    public CommonResult<Long> addColonelAppVisitLog(@Valid @RequestBody MemColonelVisitLogSaveReqVO createReqVO) {
        return success(memColonelAppService.insertMemColonelVisitLog(createReqVO));
    }

    /**
     * 业务员App拜访列表
     */
    @GetMapping("/CustomerVisitList")
    @ApiOperation(value = "业务员App拜访列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<PageResult<MemColonelAppCustomerListRespVO>> getCustomerVisitListPage(@Valid MemColonelAppCustomerListPageReqVO pageReqVO) {
        PageResult<MemColonelAppCustomerListRespVO> pageResult = memColonelAppService.getCustomerVisitListPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, MemColonelAppCustomerListRespVO.class));
    }

    /**
     * 业务员App拜访详情列表
     */
    @GetMapping("/CustomerVisitDetailList")
    @ApiOperation(value = "业务员App拜访详情列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<PageResult<MemColonelVisitLogRespVO>> getCustomerVisitDetailListPage(@Valid MemColonelAppCustomerListPageReqVO pageReqVO) {
        return success(memColonelAppService.getCustomerVisitDetailListPage(pageReqVO));
    }


    /**
     * 业务员App拜门店拜访历史记录
     */
    @GetMapping("/branchVisitDetailList")
    @ApiOperation(value = "业务员App拜门店拜访历史记录", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<List<MemColonelVisitLogRespVO>> branchVisitDetailList(@Valid MemColonelAppCustomerListPageReqVO pageReqVO) {
        return success(memColonelAppService.branchVisitDetailList(pageReqVO));
    }

    /**
     * 修改业务员拜访日志
     */
    @ApiOperation(value = "修改业务员拜访日志", httpMethod = HttpMethod.POST)
    @RequiresPermissions(Permissions.MEEBER)
    @Log(title = "业务员拜访日志", businessType = BusinessType.UPDATE)
    @PostMapping("/editColonelAppVisitLog")
    public CommonResult<Boolean> editColonelAppVisitLog(@Valid @RequestBody MemColonelVisitLogSaveReqVO updateReqVO) {
        memColonelAppService.updateMemColonelVisitLog(updateReqVO);
        return success(true);
    }

    /**
     * 删除业务员拜访日志
     */
    @ApiOperation(value = "删除业务员拜访日志", httpMethod = HttpMethod.GET)
    @RequiresPermissions(Permissions.MEEBER)
    @Log(title = "业务员拜访日志", businessType = BusinessType.DELETE)
    @GetMapping("/removeColonelAppVisitLog/{visitIds}")
    public CommonResult<Boolean> removeColonelAppVisitLog(@PathVariable Long[] visitIds) {
        memColonelAppService.deleteMemColonelVisitLogByVisitIds(visitIds);
        return success(true);
    }

    /**
     * 新增业务员理货记录
     */
    @ApiOperation(value = "新增业务员理货记录", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @RequiresPermissions(Permissions.MEEBER)
    @Log(title = "业务员理货记录", businessType = BusinessType.INSERT)
    @PostMapping("/addColonelAppTidy")
    public CommonResult<Long> addColonelAppTidy(@Valid @RequestBody MemColonelTidySaveReqVO createReqVO) {
        return success(memColonelAppService.insertMemColonelTidy(createReqVO));
    }

    /**
     * 获取业务员理货记录详细信息
     */
    @ApiOperation(value = "获得业务员理货记录详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @RequiresPermissions(Permissions.MEEBER)
    @GetMapping("/getColonelAppTidyInfo/{colonelTidyId}")
    public CommonResult<MemColonelTidyRespVO> getColonelAppTidyInfo(@PathVariable("colonelTidyId") Long colonelTidyId) {
        MemColonelTidy memColonelTidy = memColonelAppService.getMemColonelTidy(colonelTidyId);
        return success(MemColonelTidyConvert.INSTANCE.convert(memColonelTidy));
    }


    /**
     * 分页查询业务员理货记录
     */
    @GetMapping("/selectColonelAppTidyList")
    @ApiOperation(value = "获得业务员理货记录分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<PageResult<MemColonelTidyRespVO>> selectColonelAppTidyList(@Valid MemColonelTidyPageReqVO pageReqVO) {
        PageResult<MemColonelTidy> pageResult = memColonelAppService.getMemColonelTidyPage(pageReqVO);
        return success(MemColonelTidyConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 业务员首页统计信息
     */
    @GetMapping("/getPageData")
    @ApiOperation(value = "业务员首页统计信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<PageDataDTO> getPageData() {
        return success(memColonelAppService.getPageData());
    }


    /**
     * 注册支付平台商户
     */
    @ApiOperation(value = "注册支付平台商户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.MERCHANT)
    @RequiresPermissions(Permissions.MERCHANT)
    @Log(title = "支付平台商户", businessType = BusinessType.INSERT)
    @PostMapping("/register")
    public CommonResult<Long> register(@Valid @RequestBody AccPlatformMerchantRegisterReqVO reqVO) {
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        reqVO.setMerchantId(colonelId);
        reqVO.setMerchantType(MerchantTypeEnum.COLONEL.getType());
        return platformMerchantApi.register(reqVO);
    }

    /**
     * 上传商户资质
     */
    @ApiOperation(value = "上传商户资质", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.MERCHANT + ", 目前仅需要上传 FRONT_OF_ID_CAR(身份证正面), BACK_OF_ID_CARD(身份证反面), UNIFIED_CODE_CERTIFICATE(三证合一营业执照), PERMIT_FOR_BANK_ACCOUNT(开户许可证)")
    @RequiresPermissions(Permissions.MERCHANT)
    @Log(title = "上传商户资质", businessType = BusinessType.UPDATE)
    @PostMapping("/uploadPic")
    public CommonResult<Long> uploadPic(@Valid @RequestBody AccPlatformMerchantUploadPicReqVO reqVO) {
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        reqVO.setMerchantId(colonelId);
        reqVO.setMerchantType(MerchantTypeEnum.COLONEL.getType());
        platformMerchantApi.uploadPic(reqVO).checkError();
        return success(NumberPool.LONG_ONE);
    }

    /**
     * 更新支付平台商户
     */
    @ApiOperation(value = "更新支付平台商户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.MERCHANT)
    @RequiresPermissions(Permissions.MERCHANT)
    @Log(title = "更新支付平台商户", businessType = BusinessType.UPDATE)
    @PostMapping("/updateRegister")
    public CommonResult<Long> updateRegister(@Valid @RequestBody AccPlatformMerchantRegisterReqVO reqVO) {
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        reqVO.setMerchantId(colonelId);
        reqVO.setMerchantType(MerchantTypeEnum.COLONEL.getType());
        return platformMerchantApi.updateRegister(reqVO);
    }


    /**
     * 获取支付平台商户信息
     */
    @ApiOperation(value = "获取支付平台商户信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MERCHANT)
    @RequiresPermissions(Permissions.MERCHANT)
    @GetMapping("/getPlatformMerchant")
    public CommonResult<AccPlatformMerchantRespVO> getPlatformMerchant() {
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        if (Objects.isNull(colonelId)) {
            throw new ServiceException("非业务员角色");
        }
        return platformMerchantApi.getPlatformMerchantRespVO(MerchantTypeEnum.COLONEL.getType(), colonelId, sysCode);
    }

    /**
     * 银行联行号查询
     */
    @ApiOperation(value = "银行联行号查询", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.MERCHANT)
    @RequiresPermissions(Permissions.MERCHANT)
    @PostMapping("/getBankChannel")
    public CommonResult<PageResult<SysBankChannelRespVO>> getBankChannel(@RequestBody SysBankChannelPageReqVO reqVO) {
        return bankChannelApi.getPage(reqVO);
    }

    /**
     * 获取省市区
     * @param pageReqVO 省市区请求
     * @return  省市区列表
     */
    @ApiOperation(value = "获取系统省市区", httpMethod = HttpMethod.POST)
    @PostMapping("/getAreaCityPage")
    CommonResult<PageResult<SysAreaCityRespVO>> getAreaCityPage(@RequestBody SysAreaCityPageReqVO pageReqVO) {
        return areaCityApi.getPage(pageReqVO);
    }

    /**
     * 获取业务员APP更新配置
     * @return
     */
    @ApiOperation(value = "获取业务员APP更新配置", httpMethod = HttpMethod.GET)
    @GetMapping("/getUpdateInfo")
    CommonResult<ColonelAppUpdateInfoVO> getUpdateInfo() {
        return success(memColonelAppService.getUpdateInfo());
    }

    /**
     * 文件上传请求
     */
    @PostMapping("uploadAvatar")
    @ApiOperation("业务员头像上传")
    public R<SysFile> upload(MultipartFile file)
    {
        try {
            // 上传并返回访问地址
            String url = remoteFileApi.uploadFileByMail(file).getCheckedData();

            // 更新头像上传状态和修改时间
            memColonelAppService.updateAvatarInfo(url);

            SysFile sysFile = new SysFile();
            sysFile.setName(FileUtils.getName(url));
            sysFile.setUrl(url);
            return R.ok(sysFile);
        }
        catch (Exception e)
        {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取业务员门店目标设置查询", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX)
    @GetMapping("/getColonelBranchTargetPage")
    public CommonResult<MemColonelBranchTargetRespVO> getColonelBranchTargetPage(@Valid MemColonelBranchTargetPageReqVO pageReqVO) {
        pageReqVO.setStatus(NumberPool.INT_ONE);
        return success(memColonelBranchTargetService.getColonelBranchTargetPage(pageReqVO));
    }


    @ApiOperation(value = "更新业务员门店目标设置", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX)
    @PostMapping("/updateColoenBranchTarget")
    public CommonResult<Boolean> updateColoenBranchTarget(@Valid @RequestBody List<ColonelAppBranchTargetUpdateVO> reqVO) {
        memColonelBranchTargetService.updateColoenBranchTarget(reqVO);
        return success(Boolean.TRUE);
    }

    @ApiOperation(value = "业务员App目标完成查询", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX)
    @GetMapping("/getColonelTargetPage")
    public CommonResult<ColonelAppTargetRespDTO> getColonelTargetPage(@Valid ColonelAppTargetPageVO pageVO) {
        return success(memColonelTargetService.getColonelTargetReportPage(pageVO));
    }

    @ApiOperation(value = "获取商城二维码 (返回base64字符图片)", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD_BRANCH)
    @PostMapping("/getShopAppRecode")
    @RequiresPermissions(Permissions.ADD_BRANCH)
    public CommonResult<String> getShopAppRecode(@Valid MemShopAppRecodeReqVO reqVO) {
        return success(memColonelAppService.getShopAppRecode(reqVO));
    }


    @ApiOperation(value = "获取业务员微信B2b支付openid绑定状态", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ACCOUNT)
    @GetMapping("/getWxB2bOpenidBindStatus")
    @RequiresPermissions(Permissions.ACCOUNT)
    public CommonResult<ColonelBindAppletOpenidRespVO> getWxB2bOpenidBindStatus() {
        return success(memColonelAppService.getWxB2bOpenidBindStatus());
    }

    @ApiOperation(value = "获取业务公众号绑定秘钥", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @GetMapping("/getPublishOpenidBindKey")
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<ColonelBindPublishOpenidRespVO> getPublishOpenidBindKey() {
        return success(memColonelAppService.getPublishOpenidBindKey());
    }

    @ApiOperation(value = "客户-查看优惠券接口", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.VIEW_COUPONS )
    @PostMapping("/getBranchCouponList")
    @RequiresPermissions(Permissions.VIEW_COUPONS)
    public CommonResult<PageResult<CouponDTO>> getBranchCouponList(@Valid @RequestBody CouponPageReqVO receiveReq) {
        if(ToolUtil.isEmpty(receiveReq.getBranchId())){
            CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(),"没传门店ID!");
        }
        receiveReq.setCouponBatchId(null);
        return success(memColonelAppService.getBranchCouponList(receiveReq));
    }
    @ApiOperation(value = "客户-查看业务员发券额度", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.VIEW_COUPONS)
    @GetMapping("/getColonelQuota")
    @RequiresPermissions(Permissions.VIEW_COUPONS)
    public CommonResult<ColonelQuotaDTO> getColonelQuota(){
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        return success(memColonelAppService.getColonelQuota(colonelId));
    }

    @ApiOperation(value = "客户-查看业务员发券管理", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.VIEW_COUPONS)
    @PostMapping("/getAvailableCouponsForSalesperson")
    @RequiresPermissions(Permissions.VIEW_COUPONS)
    public CommonResult<PageResult<ColonelAppPrmCouponTemplateRespVO>> getAvailableCouponsForSalesperson(@Valid @RequestBody ColonelAppPrmCouponTemplatePageReqVO pageReqVO){
        return success(memColonelAppService.getAvailableCouponsForSalesperson(pageReqVO));
    }


    @ApiOperation(value = "发券管理-去发券（查询门店信息）", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.VIEW_COUPONS)
    @GetMapping("/getStoresForSalesperson")
    @RequiresPermissions(Permissions.VIEW_COUPONS)
    public CommonResult<List<MemBranch>> getStoresForSalesperson(@RequestParam("couponTemplateId") Long couponTemplateId,@RequestParam(value = "branchName", required = false) String branchName) {
        // 调用服务层方法获取当前业务员下的门店信息
        return success(memColonelAppService.getStoresForSalesperson(couponTemplateId,branchName));
    }

    @ApiOperation(value = "发券管理-查询发券记录", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.VIEW_COUPONS)
    @GetMapping("/getCouponSendRecordsForSalesperson")
    @RequiresPermissions(Permissions.VIEW_COUPONS)
    public CommonResult<List<MemBranch>> getCouponReceivedBranchs(@RequestParam("couponTemplateId") Long couponTemplateId,@RequestParam(value = "branchName", required = false) String branchName) {
        // 调用服务层方法查询发券记录
        return success(memColonelAppService.getCouponReceivedBranchs(couponTemplateId,branchName));
    }

    @ApiOperation(value = "业务员发放优惠券", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SALESMAN_ISSUE_COUPONS)
    @PostMapping("/sendCouponsFromSalesperson")
    @RequiresPermissions(Permissions.SALESMAN_ISSUE_COUPONS)
    public CommonResult<List<CouponReceiveResultDTO>> sendCouponsFromSalesperson(@Valid @RequestBody MemColonelSendCouponDTO pageReqVO) {
        List<CouponReceiveResultDTO> couponReceiveResultDTOS = memColonelAppService.sendCouponsFromSalesperson(pageReqVO);
        return success(couponReceiveResultDTOS);
    }

    /**
     * 根据城市ID获取到该区域内绑定电子围栏的入驻商信息
     *
     * @return
     */
    @ApiOperation("根据城市ID获取到该区域内绑定电子围栏的入驻商信息")
    @GetMapping(value = "/getDzwlSupplierInfoByAreaId")
    @ResponseBody
    public CommonResult<List<SupplierDzwlAreaDTO>> getDzwlSupplierInfoByAreaId(@RequestParam("areaId") Long areaId) {
        return success(supplierAreaApi.getDzwlSupplierInfoByAreaId(areaId).getCheckedData());
    }

    @ApiOperation("业务员领取门店")
    @PostMapping(value = "/directColonel")
    @ResponseBody
    public CommonResult<String> directColonel(@RequestBody @Valid MemBranchDirectReqVo pageReqVO) {
        MemBranch memBranch = HutoolBeanUtils.toBean(pageReqVO, MemBranch.class);
        MemBranchSaveReqVO memBranchSaveReqVO = HutoolBeanUtils.toBean(pageReqVO, MemBranchSaveReqVO.class);
        Date nowDate = new Date();
        //1.先根据所选城市 查询是否开启自动审核
        if(ToolUtil.isEmpty(pageReqVO.getAreaId())) throw exception(PRDT_AREA_ITEM_AREA_NOT_EXISTS);
        //根据城市ID获取对应的运营商基础配置
        CommonResult<DcAreaDTO> dcAreaResult = remoteDcAreaApi.getDcAreaByDcIdOrAreaId(null, pageReqVO.getAreaId(), pageReqVO.getSysCode());
        if(ToolUtil.isEmpty(dcAreaResult.getData())) throw new ServiceException("该城市未分配运营商");;
        DcAreaDTO dcAreaDTO = dcAreaResult.getData();
        BasicSettingPolicyDTO basicSettingPolicyDTO = partnerPolicyApi.getBasicSettingPolicy(dcAreaDTO.getDcId()).getData();
        if (ToolUtil.isNotEmpty(basicSettingPolicyDTO) && !Objects.equals(basicSettingPolicyDTO.getIsSeas(),"1")){
            return CommonResult.error(500,"未开启公海客户功能");
        }
        if (ToolUtil.isNotEmpty(basicSettingPolicyDTO) && ToolUtil.isNotEmpty(basicSettingPolicyDTO.getSeasProtection()) ) {
            Integer seasProtection = Integer.valueOf(basicSettingPolicyDTO.getSeasProtection());
            if (seasProtection <= 0){
                return CommonResult.error(500,"请先设置业务员领取配置");
            }
            BranchDTO branchDto = memberCacheService.getBranchDto(memBranch.getBranchId());
            if (ToolUtil.isNotEmpty(branchDto) && ToolUtil.isNotEmpty(branchDto.getColonelId())){
                return CommonResult.error(500,"来晚了一步，客户已经被认领了请刷新-");
            }
            LocalDateTime localDate = nowDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalDateTime newLocalDate = localDate.plusDays(seasProtection);
            Date newSeasDate = Date.from(newLocalDate.atZone(ZoneId.systemDefault()).toInstant());
            memBranchSaveReqVO.setSeasTime(newSeasDate);
            memBranchSaveReqVO.setColonelId(pageReqVO.getColonelId());
        }else {
            return CommonResult.error(500,"请先设置业务员领取配置");
        }
        memBranchService.updateMemBranch(memBranchSaveReqVO);
        memBranchService.reloadBranchDTOCache(memBranchSaveReqVO.getBranchId());
        // 将改门店与业务员刷入es中
        remoteBranchApi.refreshEsBranchBase(memBranch.getBranchId());
        return success("领取成功");
    }

    @GetMapping("/getSeasProtection")
    @ApiOperation(value = "获取业务员保护期时间", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<Integer> getCustomerListPage(@RequestParam("areaId") Long areaId,@RequestParam("sysCode") Long sysCode) {
        Integer seasProtection = null;
        //1.先根据所选城市 查询是否开启自动审核
        if(ToolUtil.isEmpty(areaId)) throw exception(PRDT_AREA_ITEM_AREA_NOT_EXISTS);
        //根据城市ID获取对应的运营商基础配置
        CommonResult<DcAreaDTO> dcAreaResult = remoteDcAreaApi.getDcAreaByDcIdOrAreaId(null, areaId, sysCode);
        if(ToolUtil.isEmpty(dcAreaResult.getData())) throw new ServiceException("该城市未分配运营商");;
        DcAreaDTO dcAreaDTO = dcAreaResult.getData();
        BasicSettingPolicyDTO basicSettingPolicyDTO = partnerPolicyApi.getBasicSettingPolicy(dcAreaDTO.getDcId()).getData();
        if (ToolUtil.isNotEmpty(basicSettingPolicyDTO)){
             seasProtection = Integer.valueOf(basicSettingPolicyDTO.getSeasProtection());
        }
        return success(seasProtection);
    }
    @GetMapping("/getBasicSettingPolicy")
    @ApiOperation(value = "获取运营商基础设置", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<BasicSettingPolicyDTO> getBasicSettingPolicy(@RequestParam("areaId") Long areaId,@RequestParam("sysCode") Long sysCode) {
        Integer seasProtection = null;
        //1.先根据所选城市 查询是否开启自动审核
        if(ToolUtil.isEmpty(areaId)) throw exception(PRDT_AREA_ITEM_AREA_NOT_EXISTS);
        //根据城市ID获取对应的运营商基础配置
        CommonResult<DcAreaDTO> dcAreaResult = remoteDcAreaApi.getDcAreaByDcIdOrAreaId(null, areaId, sysCode);
        if(ToolUtil.isEmpty(dcAreaResult.getData())) throw new ServiceException("该城市未分配运营商");;
        DcAreaDTO dcAreaDTO = dcAreaResult.getData();
        BasicSettingPolicyDTO basicSettingPolicyDTO = partnerPolicyApi.getBasicSettingPolicy(dcAreaDTO.getDcId()).getData();
        if (ToolUtil.isEmpty(basicSettingPolicyDTO)){
            throw new ServiceException("获取运营商基础配置失败");
        }
        return success(basicSettingPolicyDTO);
    }

    /**
     * @Description: 获取系统配置信息
     * @Author: chenmingqing
     * @Date: 2025/1/12 9:45
     */
    @ApiOperation(value = "获取系统配置信息", httpMethod = HttpMethod.GET)
    @GetMapping("/getColonelAppPolicy")
    public CommonResult<ColonelAppPolicyRespDTO> getColonelAppPolicy(){
        return CommonResult.success(
                ColonelAppConvert.INSTANCE.convertColonelAppPolicyDTO(
                        memberCacheService.getPartnerMiniSettingPolicy(SecurityUtils.getLoginUser().getSysCode())
                )
        );
    }

    /**
     * 获取订单明细物流信息
     *
     * @param phone 收件人手机号
     * @param courierCompanyNo 快递公司编码
     * @param courierNumber 快递单号
     * @return
     */
    @GetMapping("/getExpressInfoByCourier")
    @ApiOperation("获取订单明细物流信息")
    public CommonResult<ExpressResDTO> getExpressInfoByCourier(@RequestParam(value = "phone") String phone,
                                                               @RequestParam(value = "courierCompanyNo") String courierCompanyNo,
                                                               @RequestParam(value = "courierNumber") String courierNumber) {
        ExpressResDTO expressResDTO = orderExpressApi.getExpressInfoByCourier(phone, courierCompanyNo, courierNumber).getCheckedData();
        return CommonResult.success(expressResDTO);
    }

//===============================================业务员售后==========================================================

    /**
     * 业务员APP售后接口-订单列表页跳售后提交页
     */
    @ApiOperation(value = "业务员APP售后接口-订单售后跳提交页", httpMethod = HttpMethod.POST)
    @PostMapping(value = "/orderAfter")
    public CommonResult<OrderAfterResDTO> orderAfter(@RequestBody OrderAfterRequest request){
        return success( afterApi.orderAfterInitiate(request).getCheckedData());
    }

    /**
     * 业务员APP售后接口-售后订单提交
     */
    @ApiOperation(value = "业务员APP售后接口-售后订单提交", httpMethod = HttpMethod.POST)
    @Log(title = "售后单退货数据提交", businessType = BusinessType.INSERT)
    @PostMapping(value = "/saveOrderAfter")
    public CommonResult<OrderAfterSaveResDTO> saveOrderAfter(@RequestBody OrderAfterSaveRequest request){
        request.setSource(SourceType.COLONELAPP.getType());
        request.setCreateUserName(SecurityUtils.getLoginUser().getUsername());
        return success(afterApi.saveAfterOrder(request).getCheckedData());
    }

    /**
     * 售后接口-获取门店合单售后商品列表
     */
    @ApiOperation(value = "业务员APP售后接口-获取门店合单售后商品列表", httpMethod = HttpMethod.POST)
    @PostMapping(value = "/branchSkuMergeAfterList")
    public CommonResult<List<BranchSkuMergeAfterResDTO>> branchSkuMergeAfterList(@RequestBody BranchSkuMergeAfterReqVO reqVo){
        return success( afterApi.branchSkuMergeAfterList(reqVo).getCheckedData());
    }

    /**
     * 业务员APP售后接口-合单售后跳提交页
     */
    @ApiOperation(value = "业务员APP售后接口-合单售后跳提交页", httpMethod = HttpMethod.POST)
    @PostMapping(value = "/orderMergeAfter")
    public CommonResult<OrderMergeAfterResDTO> orderMergeAfter(@RequestBody BranchSkuMergeAfterReqVO reqVo){
        return success( afterApi.orderMergeAfter(reqVo).getCheckedData());
    }

    /**
     * 业务员APP售后接口-合单售后订单提交
     */
    @ApiOperation(value = "业务员APP售后接口-合单售后订单提交", httpMethod = HttpMethod.POST)
    @PostMapping(value = "/saveOrderMergeAfter")
    public CommonResult<OrderAfterSaveResDTO> saveOrderMergeAfter(@RequestBody OrderMergeAfterRequest request){
        request.setSource(SourceType.COLONELAPP.getType());
        return success(afterApi.saveMergeAfterOrder(request).getCheckedData());
    }

    @ApiOperation(value = "业务员APP售后接口-取消售后单退货", httpMethod = HttpMethod.PUT)
    @Log(title = "业务员APP售后接口-取消售后单退货", businessType = BusinessType.UPDATE)
    @PutMapping("/cancelAfterReturn/{afterId}")
    public CommonResult<Boolean> cancelAfterReturn(@ApiParam(name = "afterId", value = "售后订单ID", required = true) @PathVariable("afterId") Long afterId) {
        return success(afterApi.cancelAfterReturn(afterId).getCheckedData());
    }

    @ApiOperation(value = "业务员APP售后接口-审核售后单退货", httpMethod = HttpMethod.POST)
    @Log(title = "业务员APP售后接口-审核售后单退货", businessType = BusinessType.UPDATE)
    @PostMapping("/approveAfterReturn")
    public CommonResult<Boolean> approveAfterReturn(@RequestBody AfterApproveDtlEditVO editVO) {
        editVO.setSource(SourceType.COLONELAPP.getType());
        return success(afterApi.approveAfterReturn(editVO).getCheckedData());
    }

    @ApiOperation(value = "业务员APP售后接口-审核售后单退款", httpMethod = HttpMethod.POST)
    @Log(title = "业务员APP售后接口-审核售后单退款", businessType = BusinessType.UPDATE)
    @PostMapping("/approveAfterRefund")
    public CommonResult<Boolean> approveAfterRefund(@RequestBody AfterApproveDtlEditVO editVO) {
        editVO.setSource(SourceType.COLONELAPP.getType());
        return success(afterApi.approveAfterRefund(editVO).getCheckedData());
    }


    //===============================================业务员售后==========================================================

    //==========================================业务员加单 start ========================================================
    @ApiOperation(value = "业务员APP加单-列表查询", httpMethod = HttpMethod.GET)
    @GetMapping("/getCommandList")
    public CommonResult<PageResult<CommandAddOrderRespDTO>> getCommandList(@Valid CommandPageReqVO pageReqVO) {
        return success(memCommandService.getMemCommandPage(pageReqVO));
    }

    @ApiOperation(value = "业务员APP加单-移除加单", httpMethod = HttpMethod.PUT)
    @PutMapping("/removeAnchorCommand/{anchorCommandId}")
    public CommonResult<Boolean> removeAnchorCommand(@ApiParam(value = "加单锚点ID", required = true) @PathVariable("anchorCommandId") Long anchorCommandId) {
        return success(memCommandService.removeAnchorCommand(anchorCommandId));
    }

    @ApiOperation(value = "业务员APP加单- 增加普通加单指令", httpMethod = HttpMethod.POST)
    @PostMapping("/createAddOrderCommand")
    public CommonResult<String> createAddOrderCommand(@RequestBody CommandAddOrderVO saveVO) {
        return success(memCommandService.addOrderCommandHandle(saveVO));
    }

//    @ApiOperation(value = "业务员APP加单- 加入购物车", httpMethod = HttpMethod.POST)
//    @PostMapping("/commandAddShopCart")
//    public CommonResult<Boolean> commandAddShopCart(@RequestBody CommandSkuAddShopCartSaveVO saveVO) {
//        return success(memCommandService.createAddOrderCommand(saveVO));
//    }


//    @ApiOperation(value = "业务员APP加单- 获取普通加单指令商品清单", httpMethod = HttpMethod.POST)
//    @PostMapping("/ordinaryCommandSkuList")
//    public CommonResult<PageResult<CommandAddOrderRespDTO>> ordinaryCommandSkuList(@Valid CommandSkuListReqVO pageReqVO) {
////        pageReqVO.setSearchDate(DateUtil.beginOfDay(DateUtils.getNowDate()));
//        return success(memCommandService.getMemCommandPage(pageReqVO));
//    }




    //==========================================业务员加单 end ==========================================================


    //==========================================门店生命周期 start ========================================================

    @ApiOperation(value = "门店生命周期-客户变更状态列表查询", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @GetMapping("/getBranchLifecycleZipPage")
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<PageResult<MemBranchLifecycleZipRespVO>> getBranchLifecycleZipPage(@Valid MemBranchLifecycleZipPageReqVO pageReqVO) {
        //客户变更状态列表查询
        //获取业务员信息
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        if (Objects.isNull(colonelId)) {
            throw exception(APP_COLONEL_CHECK_COLONEL_ID);
        }
        pageReqVO.setColonelId(colonelId);

        return success(memBranchLifecycleZipService.getMemBranchLifecycleZipPage(pageReqVO));
    }

    @ApiOperation(value = "门店生命周期-获取该业务员所管理的门店生命周期信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @GetMapping("/getColonelBranchLifecycle")
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<ColonelBranchLifecycleQtyDTO> getColonelBranchLifecycle() {
        return success(memBranchService.getColonelBranchLifecycle());
    }


    /**
     * 查询门店状态变更记录
     */
    @ApiOperation(value = "门店生命周期-查询门店状态变更记录", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MEEBER)
    @GetMapping("/getColonelAppBranchLifecycleStageList")
    @RequiresPermissions(Permissions.MEEBER)
    public CommonResult<List<MemBranchLifecycleZipRespVO>> getColonelAppBranchLifecycleStageList(@Valid @RequestParam("branchId") Long branchId) {
        return success(memBranchLifecycleZipService.getBranchLifecycleStageList(branchId));
    }


    //==========================================门店生命周期 end ========================================================

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 扩店权限 */
        public static final String ADD_BRANCH = "member:colonelApp:addBranch";
        /** 账户权限 */
        public static final String ACCOUNT = "member:colonelApp:account";
        /** 客户权限 */
        public static final String MEEBER = "member:colonelApp:member";
        /** 订单权限 */
        public static final String ORDER = "member:colonelApp:order";
        /** 业务员提成权限 */
        public static final String ORDER_SETTLE = "member:colonelApp:orderSettle";
        /** 商户操作权限 */
        public static final String MERCHANT = "member:colonelApp:merchant";
        /** 业务员APP 新增、查询我发展、查询我管理 */
        public static final String DEVELOPMENT_SALESMAN = "member:colonelApp:developmentSalesman";
        /** 业务员APP 查看优惠券，查看业务员发券额度，查看业务员发券管理 */
        public static final String VIEW_COUPONS = "member:colonelApp:viewCoupons";
        /** 业务员APP 业务员主动发券 */
        public static final String SALESMAN_ISSUE_COUPONS = "member:colonelApp:salesmanIssueCoupons";
    }


}
