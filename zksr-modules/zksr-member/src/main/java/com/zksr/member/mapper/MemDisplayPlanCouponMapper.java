package com.zksr.member.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemDisplayPlanCoupon;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanCouponPageReqVO;


/**
 * 陈列计划优惠明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Mapper
public interface MemDisplayPlanCouponMapper extends BaseMapperX<MemDisplayPlanCoupon> {
    default PageResult<MemDisplayPlanCoupon> selectPage(MemDisplayPlanCouponPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemDisplayPlanCoupon>()
                    .eqIfPresent(MemDisplayPlanCoupon::getPlanCouponId, reqVO.getPlanCouponId())
                    .eqIfPresent(MemDisplayPlanCoupon::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemDisplayPlanCoupon::getPlanId, reqVO.getPlanId())
                    .eqIfPresent(MemDisplayPlanCoupon::getCouponId, reqVO.getCouponId())
                    .eqIfPresent(MemDisplayPlanCoupon::getExchangeSum, reqVO.getExchangeSum())
                    .eqIfPresent(MemDisplayPlanCoupon::getFinishSum, reqVO.getFinishSum())
                    .eqIfPresent(MemDisplayPlanCoupon::getMemo, reqVO.getMemo())
                    .eqIfPresent(MemDisplayPlanCoupon::getDelFlag, reqVO.getDelFlag())
                .orderByDesc(MemDisplayPlanCoupon::getPlanCouponId));
    }
}
