package com.zksr.member.controller.displayType;

import javax.validation.Valid;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemDisplayType;
import com.zksr.member.service.IMemDisplayTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.displayType.vo.MemDisplayTypePageReqVO;
import com.zksr.member.controller.displayType.vo.MemDisplayTypeSaveReqVO;
import com.zksr.member.controller.displayType.vo.MemDisplayTypeRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 陈列类型Controller
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@Api(tags = "管理后台 - 陈列类型接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/displayType")
public class MemDisplayTypeController {
    @Autowired
    private IMemDisplayTypeService memDisplayTypeService;

    /**
     * 新增陈列类型
     */
    @ApiOperation(value = "新增陈列类型", httpMethod = "POST")
    @RequiresPermissions("member:type:add")
    @Log(title = "陈列类型", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemDisplayTypeSaveReqVO createReqVO) {
        return success(memDisplayTypeService.insertMemDisplayType(createReqVO));
    }

    /**
     * 修改陈列类型
     */
    @ApiOperation(value = "修改陈列类型", httpMethod = "PUT")
    @RequiresPermissions("member:type:edit")
    @Log(title = "陈列类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemDisplayTypeSaveReqVO updateReqVO) {
            memDisplayTypeService.updateMemDisplayType(updateReqVO);
        return success(true);
    }

    /**
     * 删除陈列类型
     */
    @ApiOperation(value = "删除陈列类型", httpMethod = "GET")
    @RequiresPermissions("member:type:remove")
    @Log(title = "陈列类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{displayIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] displayIds) {
        memDisplayTypeService.deleteMemDisplayTypeByDisplayIds(displayIds);
        return success(true);
    }

    /**
     * 获取陈列类型详细信息
     */
    @ApiOperation(value = "获得陈列类型详情", httpMethod = "GET")
    @RequiresPermissions("member:type:query")
    @GetMapping(value = "/{displayId}")
    public CommonResult<MemDisplayTypeRespVO> getInfo(@PathVariable("displayId") Long displayId) {
        MemDisplayType memDisplayType = memDisplayTypeService.getMemDisplayType(displayId);
        return success(HutoolBeanUtils.toBean(memDisplayType, MemDisplayTypeRespVO.class));
    }

    /**
     * 分页查询陈列类型
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得陈列类型分页列表", httpMethod = "GET")
    @RequiresPermissions("member:type:list")
    public CommonResult<PageResult<MemDisplayTypeRespVO>> getPage(@Valid MemDisplayTypePageReqVO pageReqVO) {
        PageResult<MemDisplayType> pageResult = memDisplayTypeService.getMemDisplayTypePage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, MemDisplayTypeRespVO.class));
    }

}
