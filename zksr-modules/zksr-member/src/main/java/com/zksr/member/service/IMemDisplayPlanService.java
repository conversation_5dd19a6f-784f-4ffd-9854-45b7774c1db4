package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.domain.MemDisplayPlan;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanPageReqVO;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanSaveReqVO;

/**
 * 陈列计划Service接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
public interface IMemDisplayPlanService {

    /**
     * 新增陈列计划
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemDisplayPlan(@Valid MemDisplayPlanSaveReqVO createReqVO);

    /**
     * 修改陈列计划
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemDisplayPlan(@Valid MemDisplayPlanSaveReqVO updateReqVO);

    /**
     * 删除陈列计划
     *
     * @param planId 陈列计划编号，主键
     */
    public void deleteMemDisplayPlan(Long planId);

    /**
     * 批量删除陈列计划
     *
     * @param planIds 需要删除的陈列计划主键集合
     * @return 结果
     */
    public void deleteMemDisplayPlanByPlanIds(Long[] planIds);

    /**
     * 获得陈列计划
     *
     * @param planId 陈列计划编号，主键
     * @return 陈列计划
     */
    public MemDisplayPlan getMemDisplayPlan(Long planId);

    /**
     * 获得陈列计划分页
     *
     * @param pageReqVO 分页查询
     * @return 陈列计划分页
     */
    PageResult<MemDisplayPlan> getMemDisplayPlanPage(MemDisplayPlanPageReqVO pageReqVO);

}
