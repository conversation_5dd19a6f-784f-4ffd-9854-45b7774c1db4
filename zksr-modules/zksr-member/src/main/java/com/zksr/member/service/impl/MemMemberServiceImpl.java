package com.zksr.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.utils.DcUtils;
import com.zksr.common.security.service.MallTokenService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.member.dto.MemMemberSaveReqVO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.member.api.member.vo.MemMemberPageReqVO;
import com.zksr.member.api.member.vo.MemMemberRespVO;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterSaveReqVO;
import com.zksr.member.domain.*;
import com.zksr.member.mapper.*;
import com.zksr.member.service.IMemBranchService;
import com.zksr.member.service.IMemMemberService;
import com.zksr.system.api.group.GroupApi;
import com.zksr.system.api.group.dto.GroupDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 用户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@Service
public class MemMemberServiceImpl implements IMemMemberService {
    @Autowired
    private MemMemberMapper memMemberMapper;

    @Autowired
    private MemBranchUserMapper memBranchUserMapper;

    @Autowired
    private MemBranchMapper memBranchMapper;

    @Autowired
    private MemBranchSupplierMapper memBranchSupplierMapper;

    @Autowired
    private MallTokenService mallTokenService;

    @Autowired
    private Cache<Long, MemberDTO> memberDTOCache;

    @Autowired
    private GroupApi groupApi;

    @Autowired
    private IMemBranchService memBranchService;

    @Autowired
    private MemBranchRegisterMapper memBranchRegisterMapper;

    @Autowired
    private MemMemberRegisterMapper memMemberRegisterMapper;
    @Autowired
    private MemLoginHisMapper memLoginHisMapper;

    /**
     * 新增用户信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @DSTransactional
    @Override
    public Long insertMemMember(MemMemberSaveReqVO createReqVO) {
        if (createReqVO.getChannel() == 1) {
            return appUserAdd(createReqVO);
        }
        //校验用户账号是否已存在
        MemMember checkMemMember = memMemberMapper.selectByMobileAndOpenid(null, null, null, createReqVO.getUserName());
        if(Objects.nonNull(checkMemMember)){
            throw exception(MEM_MEMBER_USERNAME_IS_EXISTS);
        }

        // 密码如果不在指定范围内 错误
        passwordCheck(createReqVO.getPassword());
//        if (createReqVO.getPassword().length() < UserConstants.PASSWORD_MIN_LENGTH
//                || createReqVO.getPassword().length() > UserConstants.PASSWORD_MAX_LENGTH)
//        {
//            throw exception(MEM_MEMBER_PASSWORD_NOT_LENGTH);
//        }

        // 插入
        MemMember memMember = HutoolBeanUtils.toBean(createReqVO, MemMember.class);
        memMember.setStatus(STATE_ENABLE);
        //默认店长属性
        memMember.setIsShopManager(FLAG_TRUE);
        memMember.setPassword(SecurityUtils.encryptPassword(memMember.getPassword()));
        memMember.setDcId(SecurityUtils.getDcId());
        memMemberMapper.insert(memMember);
        if (StringUtils.isNotEmpty(createReqVO.getBranchIds())){
            bindMemBranch(createReqVO.getBranchIds(), memMember.getMemberId(),null);
            // 新增默认第一个门店为默认门店
            memBranchUserMapper.updateBranchUserFlagByMemberId(memMember.getMemberId(),createReqVO.getBranchIds().get(0),STATUS_1);
        }
        // 返回
        return memMember.getMemberId();
    }

    /**
     * 客户通用户处理
     *
     * @return
     */
    private Long appUserAdd(MemMemberSaveReqVO createReqVO) {
        MemMember memMember = HutoolBeanUtils.toBean(createReqVO, MemMember.class);
        if (Objects.isNull(memMember)) {
            throw exception(MEM_MEMBER_PARAM_CONVERT);
        }
        MemMember myMemMember = memMemberMapper.selectByMobileAndOpenid(null, null, null, createReqVO.getUserName());
        //管理门店不能重复重复插入
        if (Objects.nonNull(myMemMember)) {
            List<MemBranchUser> userList = memBranchUserMapper.getMemBranchUserByMemberId(myMemMember.getMemberId());
            List<Long> branchIdList = userList.stream().map(MemBranchUser::getBranchId).collect(Collectors.toList());
            List<Long> intersection = createReqVO.getBranchIds().stream()
                    .filter(branchIdList::contains)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(intersection)) {
                throw exception(MEM_MEMBER_PARAM_REPEAT);
            }
        }
        // 密码如果不在指定范围内 错误
        passwordCheck(createReqVO.getPassword());
        memMember.setStatus(STATE_ENABLE);
        //默认店长属性
        memMember.setIsShopManager(FLAG_TRUE);
        memMember.setPassword(SecurityUtils.encryptPassword(memMember.getPassword()));
        memMember.setDcId(SecurityUtils.getDcId());
        if (Objects.isNull(myMemMember)) {
            memMemberMapper.insert(memMember);
        } else {
            memMember.setMemberId(myMemMember.getMemberId());
            memMemberMapper.updateById(memMember);
        }
        if (StringUtils.isNotEmpty(createReqVO.getBranchIds())) {
            bindMemBranch(createReqVO.getBranchIds(), memMember.getMemberId(), null);
            // 新增默认第一个门店为默认门店
            memBranchUserMapper.updateBranchUserFlagByMemberId(memMember.getMemberId(), createReqVO.getBranchIds().get(0), STATUS_1);
        }
        // 返回
        return memMember.getMemberId();
    }

    public void passwordCheck(String password){
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            throw new ServiceException("密码长度必须在8到16个字符之间");
        }
        if (!Pattern.matches(UserConstants.PASSWORD_PATTERN, password)) {
            throw new ServiceException("必须包含至少一个字母、一个数字和一个特殊字符，长度在8到16个字符之间");
        }
    }

    /**
     * 修改用户信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemMember(MemMemberSaveReqVO updateReqVO) {
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();

        Long memberId = updateReqVO.getMemberId();
        if(ToolUtil.isEmpty(memberId)){
            throw exception(MEM_MEMBER_NOT_EXISTS);
        }
        // 密码如果不在指定范围内 错误
        if (ToolUtil.isNotEmpty(updateReqVO.getPassword())){
        String password = updateReqVO.getPassword();
        passwordCheck(password);
        updateReqVO.setPassword(SecurityUtils.encryptPassword(password));
        }
//        if(ToolUtil.isNotEmpty(password)){
//            if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
//                    || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
//            {
//                throw exception(MEM_MEMBER_PASSWORD_NOT_LENGTH);
//            }
//            updateReqVO.setPassword(SecurityUtils.encryptPassword(password));
//        }

        memMemberMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, MemMember.class));
        if (StringUtils.isNotEmpty(updateReqVO.getBranchIds())) {
            //查询当前用户的默认门店
//            MemBranchUser defaultBranch = memBranchUserMapper.getDefaultMemBranchUserByMemberId(memberId,sysCode);

            //删除当前的用户门店绑定信息
            memBranchUserMapper.deleteBranchUserByMemberId(memberId);
            //重新绑定用户门店信息，注：不添加syscode
            bindMemBranch(updateReqVO.getBranchIds(), memberId, null);

            //如果存在默认门店 则修改门店用户关系后 重新设置默认门店
//            if(ToolUtil.isNotEmpty(defaultBranch)){
//                memBranchUserMapper.updateBranchUserFlagByMemberId(defaultBranch.getMemberId(),defaultBranch.getBranchId(),STATUS_1);
//            }
            memBranchUserMapper.updateBranchUserFlagByMemberId(memberId, updateReqVO.getBranchIds().get(0),STATUS_1);
            // 清空登录缓存，让用户重新登录
            MemMember memMember = memMemberMapper.selectById(updateReqVO.getMemberId());
            mallTokenService.logoutByTokenUuid(memMember.getLoginToken());
        }
        refreshCache(memberId);
    }

    /**
     * 删除用户信息
     *
     * @param memberId 用户id;用户id
     */
    @Override
    public void deleteMemMember(Long memberId) {
        // 删除
        memMemberMapper.deleteById(memberId);
    }

    /**
     * 批量删除用户信息
     *
     * @param memberIds 需要删除的用户信息主键
     * @return 结果
     */
    @Override
    public void deleteMemMemberByMemberIds(Long[] memberIds) {
        for(Long memberId : memberIds){
            this.deleteMemMember(memberId);
        }
    }

    /**
     * 获得用户信息
     *
     * @param memberId 用户id;用户id
     * @return 用户信息
     */
    @Override
    public MemMember getMemMember(Long memberId) {
        return memMemberMapper.selectById(memberId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemMemberRespVO> getMemMemberPage(MemMemberPageReqVO pageReqVO) {
        Long dcId = SecurityUtils.getLoginUser() == null ? null : SecurityUtils.getLoginUser().getDcId();

        if (Objects.nonNull(dcId)) {
            pageReqVO.setAreaIdList(DcUtils.getAreaList(dcId));
            pageReqVO.setDcId(dcId);
        }
        Page<MemMemberPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<MemMemberRespVO> memberRespPage =  memMemberMapper.selectPage(pageReqVO, page);
        List<MemMemberRespVO> list = memberRespPage.getRecords();
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> userList = list.stream().map(MemMemberRespVO::getUserName).collect(Collectors.toList());
            List<MemLoginHis> hisList = memLoginHisMapper.selectLoginTimeByUser(userList, SecurityUtils.getLoginUser().getSysCode());
            Map<String, Date> hisMap = hisList.stream().collect(Collectors.toMap(MemLoginHis::getMemberPhone, MemLoginHis::getCreateTime));
            list.forEach(it -> it.setLastLoginTime(hisMap.get(it.getMemberPhone())));
        }
        return new PageResult<>(memberRespPage.getRecords(),memberRespPage.getTotal());
    }

    @Override
    public MemMember getInfoByMobileAndOpenid(Long sysCode, String mobile, String openid, String username) {
        return memMemberMapper.selectByMobileAndOpenid(sysCode, mobile, openid,username);
    }

    @Override
    public void disable(Long memberId) {
        MemMember memMember = memMemberMapper.selectById(memberId);
        memMember.setStatus(StatusConstants.STATE_DISABLE);
        memMemberMapper.updateById(memMember);
        refreshCache(memberId);
    }

    @Override
    public void enable(Long memberId) {
        MemMember memMember = memMemberMapper.selectById(memberId);
        memMember.setStatus(STATE_ENABLE);
        if(ToolUtil.isNotEmpty(memMember.getExpirationDate())){
            //后台启用用户后 需要将失效时间清除
            memMember.setExpirationDate(null);
        }
        memMemberMapper.updateById(memMember);
        refreshCache(memberId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertMemberRegister(MemMemberRegisterSaveReqVO saveReqVO) {
        //获取门店及用户信息

        // 新增用户注册信息 或 根据选择的用户查询用户信息
        MemMember memMember = createMemberRegister(saveReqVO);
        if (ToolUtil.isEmpty(memMember))
            throw exception(MEM_MEMBER_NOT_EXISTS);

        // 未存在城市信息，说明是简易注册，不执行注册门店逻辑
        if (ToolUtil.isEmpty(saveReqVO.getAreaId())){
            return null;
        }
        if(null == saveReqVO.getHdfkSupport()){
            saveReqVO.setHdfkSupport(NumberPool.INT_ONE);
        }

        // 新增门店注册信息
        MemBranch branch = createMemBranchRegister(saveReqVO);
        if (ToolUtil.isEmpty(branch))
            throw exception(MEM_BRANCH_NOT_EXISTS);

        // 关联门店和门店注册表信息
        if (ToolUtil.isNotEmpty(branch.getBranchId())&&ToolUtil.isNotEmpty(saveReqVO.getMemberRegisterId())){
            this.updateRegisterBranchId(branch.getBranchId(),saveReqVO.getMemberRegisterId());
        }


        // 新增门店和电子围栏入驻商的绑定关系, 入驻商信息不为空时进入
        if (ToolUtil.isNotEmpty(saveReqVO.getSupplierIds())) {
            branchBindDzwlSupplier(branch.getBranchId(), Arrays.stream(saveReqVO.getSupplierIds().split(",")).map(s -> Long.valueOf(s.trim())).collect(Collectors.toSet()));
        }

        //用户绑定门店关系
         if (ToolUtil.isNotEmpty(saveReqVO.getUserType()) && saveReqVO.getUserType() == COLONEL_USER_TYPE_1) {
             bindMemBranch(Arrays.asList(branch.getBranchId()), memMember.getMemberId(),null);
        } else {
             bindMemBranch(Arrays.asList(branch.getBranchId()), memMember.getMemberId(),memMember.getSysCode());
         }

        //推送 同步门店信息
        //通过注册信息生成门店的入口比较多  统一在此处推送
        memBranchService.syncBranchData(branch.getBranchId(), OperationType.ADD.getCode());

        return branch.getBranchId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDefaultBranch(Long memberId, Long branchId, Long sysCode) {
        memBranchUserMapper.updateDefaultBranch(memberId,branchId,sysCode);
    }

    @Override
    public List<MemberDTO> getAllExpirationDateMemberList(Long sysCode) {
        return HutoolBeanUtils.toBean(memMemberMapper.getAllExpirationDateMemberList(sysCode),MemberDTO.class);
    }

    @Override
    public void updateStatusByMemberIds(MemberDTO memberDTO) {
        memMemberMapper.updateStatusByMemberIds(memberDTO);
    }

    @Override
    public void insertChildUser(MemberDTO memberDTO) {
        //查询父级用户信息  是否是体检用户
        MemMember pMemMember = memMemberMapper.selectById(memberDTO.getPid());

        // 插入
        MemMember memMember = HutoolBeanUtils.toBean(memberDTO, MemMember.class);
        memMember.setStatus(STATE_ENABLE);
        if(pMemMember.getExpirationDate() != null){
            memMember.setExpirationDate(pMemMember.getExpirationDate());
        }

        memMemberMapper.insert(memMember);

        //用户绑定门店关系
        bindMemBranch(Arrays.asList(memberDTO.getBranchId()), memMember.getMemberId(),memMember.getSysCode());

    }

    @Override
    public List<MemberDTO> childUserList(Long memberId) {
        return HutoolBeanUtils.toBean(memMemberMapper.getMemberList(new MemMemberRespVO(memberId)),MemberDTO.class);
    }


    @Override
    public Boolean updateMemberToken(MemMember memMember) {
        return memMemberMapper.updateById(memMember) > 0;
    }

    @Override
    public void updateMemberPublishOpenid(Long memberId, String publishOpenid) {
        MemMember memMember = memMemberMapper.selectById(memberId);
        MemMember update = MemMember.builder()
                .memberId(memberId)
                .publishOpenid(publishOpenid)
                .expirationDate(memMember.getExpirationDate())
                .build();
        memMemberMapper.updateById(update);
        refreshCache(memberId);
    }

    @Override
    public void updateMemberUserInfoByMemberId(MemMember member) {
        memMemberMapper.updateById(member);
        refreshCache(member.getMemberId());
    }

    @Override
    public MemMember getMemBerByColonelId(Long colonelId, Long sysCode) {
        return memMemberMapper.getMemberByColonelId(colonelId, sysCode);
    }

    @Override
    public MemMember createColonelMember(MemberDTO memberDTO) {
        MemMember memMember = HutoolBeanUtils.toBean(memberDTO, MemMember.class);
        memMemberMapper.insert(memMember);
        return memMember;
    }

    @Override
    public void updateMemberDeviceId(MemberDTO memberDTO) {
        MemMember update = MemMember.builder()
                .memberId(memberDTO.getMemberId())
                .deviceId(memberDTO.getDeviceId())
                .build();
        memMemberMapper.updateById(update);
    }

    public void refreshCache(Long memberId) {
        memberDTOCache.put(memberId, BeanUtil.toBean(memMemberMapper.selectById(memberId), MemberDTO.class));
    }

    @Override
    public void updateMemberPwd(MemMember member) {
        memMemberMapper.updateMemberPwd(member);
    }

    private void validateMemMemberExists(Long memberId) {
        if (memMemberMapper.selectById(memberId) == null) {
            throw exception(MEM_MEMBER_NOT_EXISTS);
        }
    }

    /**
     * 用户绑定门店
     * @param branchId
     * @param memberId
     */
    private void bindMemBranch(List<Long> branchId, Long memberId,Long sysCode){
        List<MemBranchUser> memBranchUserList = branchId.stream().map(key -> {
            MemBranchUser memBranchUser = new MemBranchUser();
            memBranchUser.setMemberId(memberId);
            memBranchUser.setBranchId(key);
            //如果syscode不为空 则是通过注册 进行的门店绑定 该门店为默认门店
            if (ToolUtil.isNotEmpty(sysCode)){
                memBranchUser.setSysCode(sysCode);
                memBranchUser.setIsDefault(STATUS_1);
            };
            return memBranchUser;
        }).collect(Collectors.toList());
        memBranchUserMapper.insertBatch(memBranchUserList);
    }

    /**
     * 新用户注册 或 业务员扩店注册，存在用户信息时 需要新增门店信息 并且将门店与用户绑定
     * @param saveReqVO
     * @return
     */
    private MemMember createMemberRegister(MemMemberRegisterSaveReqVO saveReqVO) {
        MemMember memMember = new MemMember();
        //如果是门店注册，存在用户信息时 需要新增门店信息 并且将门店与用户绑定
        if(ToolUtil.isNotEmpty(saveReqVO.getUserType()) && saveReqVO.getUserType() == COLONEL_USER_TYPE_1){
            memMember = memMemberMapper.selectByMobileAndOpenid(null,null,null,saveReqVO.getUserName());
            if(ToolUtil.isEmpty(memMember)){
                throw exception(MEM_MEMBER_BRANCH_REGISTER_NOT_EXISTS);
            }

        }else{
            //校验
            MemMember checkMemMember = memMemberMapper.selectByMobileAndOpenid(saveReqVO.getSysCode(), null, null, saveReqVO.getUserName());
            if(ToolUtil.isNotEmpty(checkMemMember)){
                throw exception(MEM_MEMBER_USERNAME_IS_EXISTS);
            }

            //新用户 新增用户信息
            memMember.setSysCode(saveReqVO.getSysCode())
                    .setMemberPhone(saveReqVO.getUserName())
                    .setMemberName(saveReqVO.getMemberName())
                    .setStatus(STATE_ENABLE)
                    .setUserName(saveReqVO.getUserName())
                    .setPassword(saveReqVO.getPassword())
                    //注册默认为店长用户
                    .setIsShopManager(FLAG_TRUE);

            if(saveReqVO.getMemberApproveFlag() == REGISTER_APPROVE_FLAG_1  && ToolUtil.isNotEmpty(saveReqVO.getMemberExpirationDate())){
                memMember.setExpirationDate(saveReqVO.getMemberExpirationDate());
            }

            // 插入用户
            memMemberMapper.insert(memMember);
        }
        return memMember;
    }

    /**
     * 新用户注册门店 或 业务员扩店注册
     * @param saveReqVO
     * @return
     */
    private MemBranch createMemBranchRegister(MemMemberRegisterSaveReqVO saveReqVO) {
        //门店信息
        MemBranch branch = new MemBranch();
        branch.setSysCode(saveReqVO.getSysCode())
                .setBranchNo(saveReqVO.getBranchNo())
                .setBranchName(saveReqVO.getBranchName())
                .setAreaId(saveReqVO.getAreaId())
                .setBranchAddr(saveReqVO.getBranchAddr())
                .setLongitude(saveReqVO.getLongitude())
                .setLatitude(saveReqVO.getLatitude())
                .setContactName(saveReqVO.getMemberName())
                .setContactPhone(saveReqVO.getUserName())
                .setMemo(saveReqVO.getMemo())
                .setStatus(STATE_ENABLE)
                .setChannelId(saveReqVO.getChannelId())
                .setColonelId(saveReqVO.getColonelId())
                .setBranchImages(saveReqVO.getBranchImages())
                .setThreeAreaCityId(saveReqVO.getThreeAreaCityId())
                .setProvinceName(saveReqVO.getProvinceName())
                .setCityName(saveReqVO.getCityName())
                .setDistrictName(saveReqVO.getDistrictName())
                .setHdfkSupport(saveReqVO.getHdfkSupport())
                .setHdfkMaxAmt(saveReqVO.getHdfkMaxAmt())
                .setSalePriceCode(saveReqVO.getSalePriceCode())
                .setIsPayOnline(saveReqVO.getIsPayOnline())
        ;

        if (Objects.equals(saveReqVO.getBranchApproveFlag(), REGISTER_APPROVE_FLAG_1) && ToolUtil.isNotEmpty(saveReqVO.getBranchExpirationDate())) {
            branch.setExpirationDate(saveReqVO.getBranchExpirationDate());
        }
        GroupDTO defaultGrouping = groupApi.getDefaultGrouping().getCheckedData();
        if(ToolUtil.isNotEmpty(defaultGrouping.getGroupId())){
            branch.setGroupId(defaultGrouping.getGroupId());
        }

        // 插入门店
        memBranchMapper.insert(branch);

        // 更新门店编号
        if(org.apache.commons.lang3.StringUtils.isEmpty(saveReqVO.getBranchNo())){
            branch.setBranchNo(branch.getBranchId().toString());
            memBranchMapper.updateById(branch);
        }

        return branch;
    }

    /**
     *  门店绑定多电子围栏入驻商
     * @param branchId 门店id
     * @param supplierIds 入驻商id集合
     */
    private void branchBindDzwlSupplier(Long branchId, Set<Long> supplierIds) {
        List<MemBranchSupplier> branchSupplierList = supplierIds.stream().map(supplierId -> {
            MemBranchSupplier branchSupplier = new MemBranchSupplier();
            branchSupplier.setBranchId(branchId);
            branchSupplier.setSupplierId(supplierId);
            return branchSupplier;
        }).collect(Collectors.toList());



        memBranchSupplierMapper.insertBatch(branchSupplierList);
    }

    /**
     * 修改门店用户密码
     * @param updateReqVO
     */
    @Override
    public void updateMemMemberPassword(MemMemberSaveReqVO updateReqVO) {
        Long memberId = updateReqVO.getMemberId();
        if(ToolUtil.isEmpty(memberId)){
            throw exception(MEM_MEMBER_NOT_EXISTS);
        }
        String password = updateReqVO.getPassword();
        passwordCheck(password);
        updateReqVO.setPassword(SecurityUtils.encryptPassword(password));
        // 清空登录缓存，让用户重新登录
        MemMember memMember = memMemberMapper.selectById(updateReqVO.getMemberId());
        mallTokenService.logoutByTokenUuid(memMember.getLoginToken());
        memMemberMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, MemMember.class));
        refreshCache(memberId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRegisterBranchId(Long branchId, Long branchRegisterId) {

        memBranchRegisterMapper.updateMemBranchRegisterBranchId(branchId, branchRegisterId);

        memMemberRegisterMapper.updateMemMemberRegisterBranchId(branchId, branchRegisterId);

    }

    @Override
    @DSTransactional
    @DistributedLock(prefix = RedisLockConstants.LOCK_MEMBER_REGISTER, condition = "#createReqVO.userName", tryLock = true)
    public MemMember createMemberCaseAutoLogin(MemMemberSaveReqVO createReqVO) {
        Long sysCode = createReqVO.getSysCode();
        // 用户账号存在，返回当前账号
        MemMember checkMemMember = memMemberMapper.selectByMobileAndOpenid(sysCode, null, null, createReqVO.getUserName());
        if (Objects.nonNull(checkMemMember)) {
            return checkMemMember;
        }
        // 密码如果不在指定范围内 错误
        passwordCheck(createReqVO.getPassword());
        // 插入
        MemMember memMember = HutoolBeanUtils.toBean(createReqVO, MemMember.class);
        memMember.setStatus(STATE_ENABLE);
        memMember.setIsShopManager(FLAG_FALSE);
        memMember.setPassword(SecurityUtils.encryptPassword(memMember.getPassword()));
        memMember.setDcId(SecurityUtils.getDcId());
        memMemberMapper.insert(memMember);
        if (StringUtils.isNotEmpty(createReqVO.getBranchIds())) {
            bindMemBranch(createReqVO.getBranchIds(), memMember.getMemberId(), sysCode);
        }
        // 返回
        return memMember;
    }

    @Override
    public MemMember getMemberByOuterUserCode(Long sysCode, String outerUserCode) {
        return memMemberMapper.getMemberByOuterUserCode(sysCode, outerUserCode);
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 用户信息 TODO 补充编号 ==========
    // ErrorCode MEM_MEMBER_NOT_EXISTS = new ErrorCode(TODO 补充编号, "用户信息不存在");


}
