package com.zksr.member.controller.branchRegister.vo;

import com.zksr.common.core.exception.ErrorCode;
import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 门店注册信息对象 mem_branch_register
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@Data
@ApiModel("门店注册信息 - mem_branch_register分页 Request VO")
public class MemBranchRegisterSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 门店注册信息ID */
    @ApiModelProperty(value = "备注")
    private Long branchRegisterId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /**
     * 门店编码
     */
    @Excel(name = "门店编码")
    @ApiModelProperty(value = "门店编码")
    private String branchNo;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 业务员id */
    @Excel(name = "业务员id")
    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    /** 渠道id */
    @Excel(name = "渠道id")
    @ApiModelProperty(value = "渠道id")
    private Long channelId;

    /** 门头照 */
    @Excel(name = "门头照")
    @ApiModelProperty(value = "门头照")
    private String branchImages;

    /** 审核人 */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人")
    private Long approveMan;

    /** 审核标识 */
    @Excel(name = "审核标识")
    @ApiModelProperty(value = "审核标识")
    private Integer approveFlag;

    /** 审核时间 */
    @Excel(name = "审核时间")
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveDate;

    /** 门店自动审核标识 */
    @Excel(name = "门店自动审核标识")
    @ApiModelProperty(value = "门店自动审核标识")
    private Integer branchApproveFlag;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 用户类型 0 新用户  1老用户 */
    @Excel(name = "用户类型")
    @ApiModelProperty(value = "用户类型 0 新用户  1老用户")
    private Integer userType;

    /** 电子围栏入驻商ID信息，以,逗号间隔 */
    @ApiModelProperty(value = "电子围栏入驻商ID信息，以,逗号间隔")
    private String supplierIds;

    @ApiModelProperty("三级区域城市ID, 省市区关联")
    private Long threeAreaCityId;


    /** 省份 */
    @Excel(name = "省份")
    @ApiModelProperty(value = "省份")
    private String provinceName;

    /** 城市 */
    @Excel(name = "城市")
    @ApiModelProperty(value = "城市")
    private String cityName;

    /** 区县 */
    @Excel(name = "区县")
    @ApiModelProperty(value = "区县")
    private String districtName;

    @ApiModelProperty(value = "是否同步创建账号")
    private Integer syncCreateAccount;

    @ApiModelProperty(value = "用户密码")
    private String password;

    @ApiModelProperty("错误信息")
    private ErrorCode errorCode;
}
