package com.zksr.member.controller.colonelApp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 获取商城app小程序二维码
 * @date 2024/9/9 15:02
 */
@Data
@ApiModel(description = "获取商城app小程序二维码")
public class MemShopAppRecodeReqVO {

    @ApiModelProperty(value = "跳转路径", required = true)
    private String page;

    @ApiModelProperty(value = "参数", required = true)
    private String scene;

    @ApiModelProperty(value = "环境 (默认正式)。正式版为 release，体验版为 trial，开发版为 develop")
    private String env_version;
}
