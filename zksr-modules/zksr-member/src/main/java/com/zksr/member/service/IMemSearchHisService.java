package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.controller.searchHis.vo.MemSearchHisRespVO;
import com.zksr.member.domain.MemSearchHis;
import com.zksr.member.controller.searchHis.vo.MemSearchHisPageReqVO;
import com.zksr.member.controller.searchHis.vo.MemSearchHisSaveReqVO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 搜索历史Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IMemSearchHisService {

    /**
     * 新增搜索历史
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemSearchHis(@Valid MemSearchHisSaveReqVO createReqVO);

    /**
     * 修改搜索历史
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemSearchHis(@Valid MemSearchHisSaveReqVO updateReqVO);

    /**
     * 删除搜索历史
     *
     * @param searchHisId 搜索历史id
     */
    public void deleteMemSearchHis(Long searchHisId);

    /**
     * 批量删除搜索历史
     *
     * @param searchHisIds 需要删除的搜索历史主键集合
     * @return 结果
     */
    public void deleteMemSearchHisBySearchHisIds(Long[] searchHisIds);

    /**
     * 获得搜索历史
     *
     * @param searchHisId 搜索历史id
     * @return 搜索历史
     */
    public MemSearchHis getMemSearchHis(Long searchHisId);

    /**
     * 获得搜索历史分页
     *
     * @param pageReqVO 分页查询
     * @return 搜索历史分页
     */
    PageResult<MemSearchHisRespVO> getMemSearchHisPage(MemSearchHisPageReqVO pageReqVO);


    /**
     * 批量关联或者取消关联Spu
     * @param keywords 关键词
     * @param spuIds spuId集合
     * @param associationType 关联类型 0：关联 1：取消关联
     */
    boolean batchAssociationSpu(String keywords, List<Long> spuIds,Integer associationType);

}
