package com.zksr.member.api.colonel;

import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.member.api.colonel.form.MemColonelImportForm;
import com.zksr.member.api.colonel.dto.MemColonelVisitLogRespDTO;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import com.zksr.member.api.colonel.vo.MemShopAppRecodeReqVO;
import com.zksr.member.api.colonel.vo.MemColonelVisitLogVO;
import com.zksr.member.api.colonel.vo.WxColonelPublishOpenidBindReqVO;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogPageReqVO;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogRespVO;
import com.zksr.member.convert.colonel.MemberColonelConvert;
import com.zksr.member.domain.MemColonel;
import com.zksr.member.domain.MemColonelBranchZip;
import com.zksr.member.mapper.ColonelReportMapper;
import com.zksr.member.mapper.MemColonelBranchZipMapper;
import com.zksr.member.mapper.MemColonelMapper;
import com.zksr.member.service.IMemColonelAppService;
import com.zksr.member.service.IMemColonelService;
import com.zksr.member.service.IMemColonelTargetService;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import com.zksr.member.service.IMemColonelVisitLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年03月28日 09:42
 * @description: ColonelApiImpl
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Slf4j
@ApiIgnore
public class ColonelApiImpl implements ColonelApi{
    @Autowired
    private IMemColonelService memColonelService;

    @Autowired
    private MemColonelMapper memColonelMapper;

    @Autowired
    private IMemColonelTargetService memColonelTargetService;
    @Autowired
    private ColonelReportMapper colonelReportMapper;
    @Autowired
    private MemColonelBranchZipMapper memColonelBranchZipMapper;
    @Autowired
    private IMemColonelVisitLogService memColonelVisitLogService;
    @Autowired
    @Lazy
    private IMemColonelAppService memColonelAppService;

    @Override
    public CommonResult<ColonelDTO> getByColonelId(Long colonelId) {
       MemColonel colonel = memColonelService.getMemColonel(colonelId);
        return CommonResult.success(HutoolBeanUtils.toBean(colonel, ColonelDTO.class));
    }

    /**
     * 新增业务员信息
     *
     * @param createReqVO
     */
    @Override
    public CommonResult<Long> add(MemColonelSaveReqVO createReqVO) {
        Long result = memColonelService.insertMemColonel(createReqVO);
        return CommonResult.success(result);
    }

    /**
     * 修改业务员信息
     *
     * @param updateReqVO
     */
    @Override
    public CommonResult<Boolean> edit(MemColonelSaveReqVO updateReqVO) {
        memColonelService.updateMemColonel(updateReqVO);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<PageResult<MemColonelRespVO>> getPage(MemColonelPageReqVO pageReqVO) {
        return CommonResult.success(MemberColonelConvert.INSTANCE.convertRespVO(memColonelService.getMemColonelPage(pageReqVO)));
    }


    /**
     * 获取某个业务员的直接下属业务员列表
     * @param colonelId 业务员ID
     * @return
     */
    @Override
    public CommonResult<List<ColonelDTO>> getSubordinates(Long colonelId) {
        return CommonResult.success(memColonelService.getSubordinates(colonelId));
    }

    /**
     * 获取某个业务员的所发展的业务员信息
     * @param colonelId 业务员ID
     * @return
     */
    @Override
    public CommonResult<List<ColonelDTO>> selectDevelopmentSalesmanByColonelId(Long colonelId) {
        return CommonResult.success(memColonelService.selectDevelopmentSalesmanByColonelId(colonelId));
    }

    @Override
    public CommonResult<List<Long>> getAllChildColonel(Long colonelId) {
        return CommonResult.success( memColonelService.getAllChildColonel(colonelId) );
    }

    @Override
    public CommonResult<Boolean> updateColonelPublishOpenidBind(WxColonelPublishOpenidBindReqVO reqVO) {
        return memColonelService.updateColonelPublishOpenidBind(reqVO);
    }

    @Override
    public CommonResult<List<Long>> getColonelIdList(Long sysCode,Integer status,String colonelName,String colonelPhone,List<Long> areaIds) {
        return CommonResult.success(memColonelMapper.getColonelIdList(sysCode,status,colonelName,colonelPhone,areaIds));
    }

    /**
     * 获取业务员的目标月份销售额
     * @param colonelId 业务员ID
     * @return
     */
    @Override
    public CommonResult<BigDecimal> getColonelMonthlySalesTarget(Long colonelId) {
        return CommonResult.success(memColonelTargetService.getColonelMonthlySalesTarget(colonelId));
    }

    /**
     * 获取上次拜访时间
     * @return
     */
    @Override
    public CommonResult<List<MemColonelRespVO>> getLastVisitTime(List<Long> branchIds) {
        return CommonResult.success(colonelReportMapper.selectLastVisitTimeList(branchIds));
    }


    /**
     * 修改业务员和门店zip表
     * @param branchId
     * @param colonelId
     * @param sysCode
     */
    @Override
    public void deleteColonelBranchZip(Long branchId, Long colonelId, Long sysCode) {
        MemColonelBranchZip memColonelBranchZip = new MemColonelBranchZip();
        memColonelBranchZip.setBranchId(branchId);
        memColonelBranchZip.setColonelId(colonelId);
        memColonelBranchZip.setSysCode(sysCode);
        memColonelBranchZipMapper.updateMemColonelBranchZip(memColonelBranchZip);
    }

    public CommonResult<String> importDataEvent(@RequestBody MemColonelImportForm form){
        return CommonResult.success(JsonUtils.toJsonString(memColonelService.impordDataEvent(form.getList(), form.getFuncScop(), form.getSysCode(), form.getFileImportId(), form.getSeq())));
    }

    @Override
    public CommonResult<List<MemColonelVisitLogRespDTO>> getColonelVisitLog(MemColonelVisitLogVO MemColonelVisitLogVO) {
        List<MemColonelVisitLogRespVO> list = memColonelVisitLogService.getMemColonelVisitLogPage(HutoolBeanUtils.toBean(MemColonelVisitLogVO, MemColonelVisitLogPageReqVO.class)).getList();
        if(ToolUtil.isEmpty(list)){
            return CommonResult.success(new ArrayList<>());
        }
        return CommonResult.success(HutoolBeanUtils.toBean(list, MemColonelVisitLogRespDTO.class));
    }

    public CommonResult<String> getShopAppRecode(MemShopAppRecodeReqVO reqVO){
        return CommonResult.success(memColonelAppService.getShopAppRecode(reqVO));
    }

    @Override
    public CommonResult<List<MemColonelRespVO>> getAreaIdExistColone(Long areaId, Long sysCode) {
        return CommonResult.success(BeanUtils.toBean(memColonelService.getAreaIdExistColone(areaId,sysCode), MemColonelRespVO.class));
    }
}
