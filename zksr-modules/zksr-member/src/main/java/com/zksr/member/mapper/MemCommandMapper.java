package com.zksr.member.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.api.command.dto.CommandAddOrderRespDTO;
import com.zksr.member.api.command.vo.CommandPageReqVO;
import com.zksr.member.api.command.vo.CommandReqVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.member.domain.MemCommand;

import java.util.List;


/**
 * 操作指令Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@Mapper
public interface MemCommandMapper extends BaseMapperX<MemCommand> {
//    default PageResult<MemCommand> selectPage(MemCommandPageReqVO reqVO) {
//        return selectPage(reqVO, new LambdaQueryWrapperX<MemCommand>()
//                    .eqIfPresent(MemCommand::getColonelId, reqVO.getColonelId())
//                    .eqIfPresent(MemCommand::getCommandId, reqVO.getCommandId())
//                    .eqIfPresent(MemCommand::getSysCode, reqVO.getSysCode())
//                    .eqIfPresent(MemCommand::getSysCode, reqVO.getSysCode())
//                    .eqIfPresent(MemCommand::getAreaId, reqVO.getAreaId())
//                    .eqIfPresent(MemCommand::getCommandLevel, reqVO.getCommandLevel())
//                    .eqIfPresent(MemCommand::getColonelPhone, reqVO.getColonelPhone())
//                    .eqIfPresent(MemCommand::getPid, reqVO.getPid())
//                    .likeIfPresent(MemCommand::getColonelName, reqVO.getColonelName())
//                    .eqIfPresent(MemCommand::getCommandDate, reqVO.getCommandDate())
//                    .eqIfPresent(MemCommand::getColonelLevel, reqVO.getColonelLevel())
//                    .eqIfPresent(MemCommand::getCommandType, reqVO.getCommandType())
//                    .eqIfPresent(MemCommand::getPcolonelId, reqVO.getPcolonelId())
//                    .eqIfPresent(MemCommand::getStatus, reqVO.getStatus())
//                    .eqIfPresent(MemCommand::getSex, reqVO.getSex())
//                    .eqIfPresent(MemCommand::getPubMerchantType, reqVO.getPubMerchantType())
//                    .eqIfPresent(MemCommand::getStatus, reqVO.getStatus())
//                    .eqIfPresent(MemCommand::getPubId, reqVO.getPubId())
//                    .eqIfPresent(MemCommand::getBirthday, reqVO.getBirthday())
//                    .eqIfPresent(MemCommand::getExecMerchantType, reqVO.getExecMerchantType())
//                    .eqIfPresent(MemCommand::getBirthplace, reqVO.getBirthplace())
//                    .eqIfPresent(MemCommand::getExecId, reqVO.getExecId())
//                    .eqIfPresent(MemCommand::getEntryDate, reqVO.getEntryDate())
//                    .eqIfPresent(MemCommand::getExecRes, reqVO.getExecRes())
//                    .eqIfPresent(MemCommand::getEdu, reqVO.getEdu())
//                    .eqIfPresent(MemCommand::getMemo, reqVO.getMemo())
//                    .eqIfPresent(MemCommand::getIdcard, reqVO.getIdcard())
//                    .eqIfPresent(MemCommand::getPercentageRate, reqVO.getPercentageRate())
//                    .eqIfPresent(MemCommand::getContactAddr, reqVO.getContactAddr())
//                    .eqIfPresent(MemCommand::getMemo, reqVO.getMemo())
//                    .eqIfPresent(MemCommand::getIsColonelAdmin, reqVO.getIsColonelAdmin())
//                    .eqIfPresent(MemCommand::getDeptId, reqVO.getDeptId())
//                    .eqIfPresent(MemCommand::getAppOrderPriceAdjust, reqVO.getAppOrderPriceAdjust())
//                    .eqIfPresent(MemCommand::getAppAfterPriceAdjust, reqVO.getAppAfterPriceAdjust())
//                    .eqIfPresent(MemCommand::getOrderAutoApprove, reqVO.getOrderAutoApprove())
//                .orderByDesc(MemCommand::getColonelId));
//    }

    default MemCommand selectByCondition(CommandReqVO reqVO) {
        return selectOne(new LambdaQueryWrapperX<MemCommand>()
                .eqIfPresent(MemCommand::getCommandId, reqVO.getCommandId())
                .eqIfPresent(MemCommand::getCommandLevel, reqVO.getCommandLevel())
                .eqIfPresent(MemCommand::getCommandDate, reqVO.getCommandDate())
                .eqIfPresent(MemCommand::getCommandType, reqVO.getCommandType())
                .eqIfPresent(MemCommand::getStatus, reqVO.getStatus())
                .eqIfPresent(MemCommand::getPubMerchantType, reqVO.getPubMerchantType())
                .eqIfPresent(MemCommand::getPubId, reqVO.getPubId())
                .eqIfPresent(MemCommand::getExecMerchantType, reqVO.getExecMerchantType())
                .eqIfPresent(MemCommand::getExecId, reqVO.getExecId())
                .eqIfPresent(MemCommand::getExecRes, reqVO.getExecRes())
                .eqIfPresent(MemCommand::getMemo, reqVO.getMemo())
        );
    }

    List<CommandAddOrderRespDTO> getColonelAppCommandPage(CommandPageReqVO pageReqVO);

    default List<MemCommand> selectByCommandIds(List<Long> commandIds, Integer commandStatus) {
        return selectList(new LambdaQueryWrapperX<MemCommand>()
                .inIfPresent(MemCommand::getCommandId, commandIds)
                .eqIfPresent(MemCommand::getStatus, commandStatus)
        );
    }

}
