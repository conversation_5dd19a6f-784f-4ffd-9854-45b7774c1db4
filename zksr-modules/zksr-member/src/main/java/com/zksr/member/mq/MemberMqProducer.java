package com.zksr.member.mq;

import com.zksr.common.core.domain.vo.openapi.SyncBranchSendDTO;
import com.zksr.common.rocketmq.constant.MessageConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.support.MessageBuilder;

import java.util.List;

/**
*
 * 用户模块 生产者
* <AUTHOR>
* @date 2024/5/9 11:39
*/
@Configuration
@Slf4j
public class MemberMqProducer {
    @Autowired
    private StreamBridge streamBridge;

    /**
     * 门店信息同步
     * @param data
     * 消费 {@link com.zksr.system.mq.SyncDataConsumer#syncDataBranchEvent()}
     */
    public void sendSyncDataBranchEvent(SyncBranchSendDTO data){
        log.info("推送门店信息同步数据:{}", data);
        streamBridge.send(
                MessageConstant.SYNC_DATA_BRANCH_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(data).build());

    }


    /**
     * 发送门店获取经纬度信息事件
     * @param branchIdList 门店ID集合
     */
    public void sendBranchGetLongitudeAndLatitudeEvent(List<Long> branchIdList){
        log.info("发送门店获取经纬度信息事件 : 门店编号:{},",branchIdList);
        streamBridge.send(
                MessageConstant.BRANCH_GET_LONGITUDE_AND_LATITUDE_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(branchIdList).build());
    }


}
