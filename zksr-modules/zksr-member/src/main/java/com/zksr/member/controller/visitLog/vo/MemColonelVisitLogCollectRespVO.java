package com.zksr.member.controller.visitLog.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @Date 2024/5/10 10:02
 * 业务员拜访汇总
 */
@Data
@ApiModel("业务员拜访汇总 - mem_colonel_visit_log Response VO")
public class MemColonelVisitLogCollectRespVO {
    private static final long serialVersionUID = 1L;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    private String colonelId;

    /** 门店ID */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    private String branchId;

    /** 创建时间 */
    @JsonFormat(pattern = YYYY_MM_DD)
    @Excel(name = "创建时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "创建时间")
    private Date createDate;


    /** 开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "开始时间", width = 30, dateFormat = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "结束时间", width = 30, dateFormat = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /** 业务员名称 */
    @Excel(name = "业务员名称")
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    /** 签到时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "签到时间", width = 30, dateFormat = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "签到时间")
    private Date signInDate;

    /** 签退时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "签退时间", width = 30, dateFormat = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "签退时间")
    private Date signOutDate;

    /** 有效拜访数量 */
    @Excel(name = "有效拜访数量")
    @ApiModelProperty(value = "有效拜访数量")
    private Integer validVisitNumber;

    /** 0-5分钟拜访时间数量 */
    @Excel(name = "0-5分钟拜访时间数量")
    @ApiModelProperty(value = "0-5分钟拜访时间数量")
    private Integer shortTimeNumber;

    /** 5-30分钟拜访时间数量 */
    @Excel(name = "5-30分钟拜访时间数量")
    @ApiModelProperty(value = "5-30分钟拜访时间数量")
    private Integer mediumTimeNumber;

    /** 30分钟以上拜访时间数量 */
    @Excel(name = "30分钟以上拜访时间数量")
    @ApiModelProperty(value = "30分钟以上拜访时间数量")
    private Integer longTimeNumber;

    /** 0-7点拜访时间数量 */
    @Excel(name = "0-7点拜访时间数量")
    @ApiModelProperty(value = "0-7点拜访时间数量")
    private Integer zeroToSevenTimeNumber;

    /** 7-8点拜访时间数量 */
    @Excel(name = "7-8点拜访时间数量")
    @ApiModelProperty(value = "7-8点拜访时间数量")
    private Integer sevenToEightTimeNumber;

    /** 8-9点拜访时间数量 */
    @Excel(name = "8-9点拜访时间数量")
    @ApiModelProperty(value = "8-9点拜访时间数量")
    private Integer eightToNineTimeNumber;

    /** 9-10点拜访时间数量 */
    @Excel(name = "9-10点拜访时间数量")
    @ApiModelProperty(value = "9-10点拜访时间数量")
    private Integer nineToTenTimeNumber;

    /** 10-11点拜访时间数量 */
    @Excel(name = "10-11点拜访时间数量")
    @ApiModelProperty(value = "10-11点拜访时间数量")
    private Integer tenToElevenTimeNumber;

    /** 11-12点拜访时间数量 */
    @Excel(name = "11-12点拜访时间数量")
    @ApiModelProperty(value = "11-12点拜访时间数量")
    private Integer elevenToTwelveTimeNumber;

    /** 12-13点拜访时间数量 */
    @Excel(name = "12-13点拜访时间数量")
    @ApiModelProperty(value = "12-13点拜访时间数量")
    private Integer twelveToThirteenTimeNumber;

    /** 13-14点拜访时间数量 */
    @Excel(name = "13-14点拜访时间数量")
    @ApiModelProperty(value = "13-14点拜访时间数量")
    private Integer thirteenToFourteenTimeNumber;

    /** 14-15点拜访时间数量 */
    @Excel(name = "14-15点拜访时间数量")
    @ApiModelProperty(value = "14-15点拜访时间数量")
    private Integer fourteenToFifteenTimeNumber;

    /** 15-16点拜访时间数量 */
    @Excel(name = "15-16点拜访时间数量")
    @ApiModelProperty(value = "15-16点拜访时间数量")
    private Integer fifteenToSixteenTimeNumber;

    /** 16-17点拜访时间数量 */
    @Excel(name = "16-17点拜访时间数量")
    @ApiModelProperty(value = "16-17点拜访时间数量")
    private Integer sixteenToSeventeenTimeNumber;

    /** 17-18点拜访时间数量 */
    @Excel(name = "17-18点拜访时间数量")
    @ApiModelProperty(value = "17-18点拜访时间数量")
    private Integer seventeenToEighteenTimeNumber;

    /** 18-24点拜访时间数量 */
    @Excel(name = "18-24点拜访时间数量")
    @ApiModelProperty(value = "18-24点拜访时间数量")
    private Integer eighteenToTwentyFourTimeNumber;
}
