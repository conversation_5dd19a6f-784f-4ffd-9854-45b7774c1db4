package com.zksr.member.controller.ColonelTidy;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.member.controller.visitLog.MemColonelVisitLogController;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogPageReqVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemColonelTidy;
import com.zksr.member.service.IMemColonelTidyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyPageReqVO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidySaveReqVO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyRespVO;
import com.zksr.member.convert.ColonelTidy.MemColonelTidyConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 业务员理货记录Controller
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Api(tags = "管理后台 - 业务员理货记录接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/ColonelTidy")
public class MemColonelTidyController {
    @Autowired
    private IMemColonelTidyService memColonelTidyService;

    /**
     * 新增业务员理货记录
     */
    @ApiOperation(value = "新增业务员理货记录", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "业务员理货记录", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemColonelTidySaveReqVO createReqVO) {
        return success(memColonelTidyService.insertMemColonelTidy(createReqVO));
    }

    /**
     * 修改业务员理货记录
     */
    @ApiOperation(value = "修改业务员理货记录", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "业务员理货记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemColonelTidySaveReqVO updateReqVO) {
            memColonelTidyService.updateMemColonelTidy(updateReqVO);
        return success(true);
    }

    /**
     * 删除业务员理货记录
     */
    @ApiOperation(value = "删除业务员理货记录", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "业务员理货记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{colonelTidyIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] colonelTidyIds) {
        memColonelTidyService.deleteMemColonelTidyByColonelTidyIds(colonelTidyIds);
        return success(true);
    }

    /**
     * 获取业务员理货记录详细信息
     */
    @ApiOperation(value = "获得业务员理货记录详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{colonelTidyId}")
    public CommonResult<MemColonelTidyRespVO> getInfo(@PathVariable("colonelTidyId") Long colonelTidyId) {
        MemColonelTidy memColonelTidy = memColonelTidyService.getMemColonelTidy(colonelTidyId);
        return success(MemColonelTidyConvert.INSTANCE.convert(memColonelTidy));
    }

    /**
     * 分页查询业务员理货记录
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得业务员理货记录分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemColonelTidyRespVO>> getPage(@Valid MemColonelTidyPageReqVO pageReqVO) {
        PageResult<MemColonelTidyRespVO> result =  memColonelTidyService.getMemColonelTidyPage(pageReqVO);
        return success(result);
    }

    @GetMapping("/getColonelTidyImageZip")
    @ApiOperation(value = "理货下载选中的图片", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DOWNLOAD)
    @RequiresPermissions(Permissions.DOWNLOAD)
    public void getColonelTidyImageZip(HttpServletResponse response, MemColonelTidyPageReqVO pageReqVO) throws IOException {

        response.reset();
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(("图片导出.zip").getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));

        try {
            // 直接获取完整的ZIP文件内容作为byte[]
            byte[] zipBytes = memColonelTidyService.getColonelTidyImageZip(pageReqVO);

            // 将整个ZIP文件内容写入响应流
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(zipBytes);
            outputStream.flush();
        } catch (Exception e) {
            throw new RuntimeException("Error streaming ZIP file", e);
        } finally {
            // 注意：这里无需关闭ZipOutputStream，因为我们并没有创建它。但通常会关闭OutputStream，但在HttpServletResponse的场景下，这通常由容器管理，不推荐手动关闭。
        }
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:ColonelTidy:add";
        /** 编辑 */
        public static final String EDIT = "member:ColonelTidy:edit";
        /** 删除 */
        public static final String DELETE = "member:ColonelTidy:remove";
        /** 列表 */
        public static final String LIST = "member:ColonelTidy:list";
        /** 查询 */
        public static final String GET = "member:ColonelTidy:query";
        /** 下载 */
        public static final String DOWNLOAD = "member:ColonelTidy:download";
    }
}
