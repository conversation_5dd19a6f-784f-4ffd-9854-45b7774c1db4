package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;

@TableName(value = "mem_colonel_branch_target")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemColonelBranchTarget extends BaseEntity {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    @Excel(name = "主键ID")
    private Long colonelBranchTargetId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 业务员Id */
    @Excel(name = "业务员Id")
    private Long colonelId;

    /** 门店Id */
    @Excel(name = "门店Id")
    private Long branchId;

    @Excel(name = "门店名称")
    private String branchName;

    @Excel(name = "门店联系人")
    private String branchContactName;

    @Excel(name = "门店联系电话")
    private String branchContactPhone;

    /** 目标年份 */
    @Excel(name = "目标年份")
    private String targetYear;

    /** 目标月份 */
    @Excel(name = "目标月份")
    private String targetMonth;

    /** 销售额 */
    @Excel(name = "目标销售额")
    private BigDecimal targetSalesMoney;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 删除状态  (0正常  2已删除) */
    @ApiModelProperty(value = "状态", example = "0")
    private String delFlag;
}
