package com.zksr.member.controller.displayType.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 陈列类型对象 mem_display_type
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@Data
@ApiModel("陈列类型 - mem_display_type分页 Request VO")
public class MemDisplayTypeSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 陈列类型编号，主键 */
    @ApiModelProperty(value = "备注")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long displayId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true,   example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 陈列类型名称 */
    @Excel(name = "陈列类型名称")
    @ApiModelProperty(value = "陈列类型名称")
    private String displayName;

    /** 开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "结束时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /** 审核状态（0:未审核,1：已审核,2：已作废） */
    @Excel(name = "审核状态", readConverterExp = "0=:未审核,1：已审核,2：已作废")
    @ApiModelProperty(value = "审核状态")
    private Integer auditState;

    /** 审核人 */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人")
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "审核时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "审核时间")
    private Date auditTime;

    /** 生效类型（0:未生效，1：生效中，2：终止） */
    @Excel(name = "生效类型", readConverterExp = "0=:未生效，1：生效中，2：终止")
    @ApiModelProperty(value = "生效类型")
    private Integer type;

    /** 删除状态(0:正常，2：删除) */
    @ApiModelProperty(value = "生效类型")
    private Integer delFlag;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

}
