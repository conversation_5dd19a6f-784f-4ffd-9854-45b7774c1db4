package com.zksr.member.controller.complain;


import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.member.api.complain.MemComplainVO;
import com.zksr.member.api.complain.dto.MemComplainDTO;
import com.zksr.member.domain.MemComplain;
import com.zksr.member.service.IMemComplainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <p>
 * 投诉信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-06
 */

@Api(tags = "投诉相关接口", produces = "application/json")
@Validated
@RestController
@Slf4j
@RequestMapping("/complain")
public class MemComplainController {

    @Autowired
    private IMemComplainService memComplainService;

    /**
     * 查询投诉列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询投诉列表")
    //TODO 后期记得补上权限    @RequiresPermissions(MemDisplayDeliveryController.Permissions.LIST)
    public PageResult<MemComplainVO> getComplainList(@Valid MemComplainVO memComplainVO) {
        PageResult<MemComplainVO> pageResult = memComplainService.getComplainList(memComplainVO);
        return pageResult;
    }


    /**
     * 新增投诉信息
     */
    @ApiOperation(value = "新增投诉信息")
    //@RequiresPermissions(MemMemberController.Permissions.ADD)
    @Log(title = "用户信息", businessType = BusinessType.INSERT)
    @PostMapping("/addComplain")
    public CommonResult<String> add(@Valid @RequestBody MemComplainDTO memComplainDTO) {
        return memComplainService.insertComplain(memComplainDTO);
    }


    /**
     * 查询投诉详情
     */
    @GetMapping("/{complainId}")
    @ApiOperation(value = "查询投诉详情")
    public MemComplainVO getComplainByComplainId(@PathVariable("complainId") String complainId) {
        MemComplainVO memComplainVO = memComplainService.getComplainByComplainId(complainId);
        return memComplainVO;
    }


    @PostMapping("/pageList")
    @ApiOperation(value = "查询投诉列表")
    //@RequiresPermissions(MemDisplayDeliveryController.Permissions.LIST)
    public CommonResult<PageResult<MemComplainVO>> getComplainPageList(@Valid @RequestBody MemComplainVO memComplainVO) {
        PageResult<MemComplain> pageResult = memComplainService.getComplainPageList(memComplainVO);
        return  success(HutoolBeanUtils.toBean(pageResult, MemComplainVO.class));
    }


    @ApiOperation(value = "处理投诉信息")
    @PutMapping("/editComplainId")
    public CommonResult<Boolean> editColoneBranch(@Valid @RequestBody MemComplainVO memComplainVO) {
        memComplainService.updateComplain(memComplainVO);
        return success(true);
    }



    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:complain:add";
        /** 编辑 */
        public static final String EDIT = "system:complain:edit";
        /** 删除 */
        public static final String DELETE = "system:complain:remove";
        /** 列表 */
        public static final String LIST = "system:complain:list";
        /** 查询 */
        public static final String GET = "system:complain:query";
        /** 停用 */
        public static final String DISABLE = "system:complain:disable";
        /** 启用 */
        public static final String ENABLE = "system:complain:enable";
    }





}
