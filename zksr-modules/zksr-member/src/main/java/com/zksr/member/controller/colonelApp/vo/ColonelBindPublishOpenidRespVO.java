package com.zksr.member.controller.colonelApp.vo;

import com.zksr.common.core.enums.PayChannelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员绑定小程序
 * @date 2024/9/24 10:01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "业务员绑定公众号状态 response VO")
public class ColonelBindPublishOpenidRespVO {

    @ApiModelProperty("绑定key, 有效期30分钟, 1800秒")
    private String bindKey;

    @ApiModelProperty("跳转的appid")
    private String appid;
}
