package com.zksr.member.controller.invoice;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.member.convert.invoice.MemMemberInvoiceConvert;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemMemberInvoice;
import com.zksr.member.service.IMemMemberInvoiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.invoice.vo.MemMemberInvoicePageReqVO;
import com.zksr.member.controller.invoice.vo.MemMemberInvoiceSaveReqVO;
import com.zksr.member.controller.invoice.vo.MemMemberInvoiceRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 用户发票Controller
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Api(tags = "管理后台 - 用户发票接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/invoice")
public class MemMemberInvoiceController {
    @Autowired
    private IMemMemberInvoiceService memMemberInvoiceService;

    /**
     * 新增用户发票
     */
    @ApiOperation(value = "新增用户发票", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "用户发票", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemMemberInvoiceSaveReqVO createReqVO) {
        return success(memMemberInvoiceService.insertMemMemberInvoice(createReqVO));
    }

    /**
     * 修改用户发票
     */
    @ApiOperation(value = "修改用户发票", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "用户发票", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemMemberInvoiceSaveReqVO updateReqVO) {
            memMemberInvoiceService.updateMemMemberInvoice(updateReqVO);
        return success(true);
    }

    /**
     * 删除用户发票
     */
    @ApiOperation(value = "删除用户发票", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "用户发票", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public CommonResult<Boolean> remove(@PathVariable Long[] ids) {
        memMemberInvoiceService.deleteMemMemberInvoiceByIds(ids);
        return success(true);
    }

    /**
     * 获取用户发票详细信息
     */
    @ApiOperation(value = "获得用户发票详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{id}")
    public CommonResult<MemMemberInvoiceRespVO> getInfo(@PathVariable("id") Long id) {
        MemMemberInvoice memMemberInvoice = memMemberInvoiceService.getMemMemberInvoice(id);
        return success(MemMemberInvoiceConvert.INSTANCE.convert(memMemberInvoice));
    }

    /**
     * 分页查询用户发票
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得用户发票分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemMemberInvoiceRespVO>> getPage(@Valid MemMemberInvoicePageReqVO pageReqVO) {
        PageResult<MemMemberInvoice> pageResult = memMemberInvoiceService.getMemMemberInvoicePage(pageReqVO);
        return success(MemMemberInvoiceConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:invoice:add";
        /** 编辑 */
        public static final String EDIT = "member:invoice:edit";
        /** 删除 */
        public static final String DELETE = "member:invoice:remove";
        /** 列表 */
        public static final String LIST = "member:invoice:list";
        /** 查询 */
        public static final String GET = "member:invoice:query";
        /** 停用 */
        public static final String DISABLE = "member:invoice:disable";
        /** 启用 */
        public static final String ENABLE = "member:invoice:enable";
    }
}
