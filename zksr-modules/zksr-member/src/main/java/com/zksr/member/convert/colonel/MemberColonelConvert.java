package com.zksr.member.convert.colonel;

import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.member.api.colonel.excel.MemColonelImportExcel;
import com.zksr.member.controller.colonel.vo.MemColonelAccountRespVO;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import com.zksr.member.domain.MemColonel;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/4/9
 * @desc 业务员对象封装转换器
 */
@Mapper
public interface MemberColonelConvert {

    MemberColonelConvert INSTANCE = Mappers.getMapper(MemberColonelConvert.class);

    MemColonelRespVO convert(MemColonel colonel);

    MemColonel convert(MemColonelSaveReqVO saveReqVO);

    PageResult<MemColonelRespVO> convertPage(PageResult<MemColonel> pageResult);

    PageResult<MemColonelAccountRespVO> convert(PageResult<MemColonel> pageResult);

    @Mappings({
            @Mapping(source = "account.platform", target = "payPlatform"),
            @Mapping(source = "account.accountId", target = "accountId"),
            @Mapping(source = "account.withdrawableAmt", target = "withdrawableAmt"),
            @Mapping(source = "account.frozenAmt", target = "frozenAmt"),
            @Mapping(source = "account.creditAmt", target = "creditAmt")
    })
    @BeanMapping(ignoreByDefault = true)
    void convert(@MappingTarget MemColonelAccountRespVO item, AccAccountDTO account);


    @Mappings({
            @Mapping(source = "itemData.colonelName", target = "colonelName"),
            @Mapping(source = "itemData.colonelPhone", target = "colonelPhone"),
            @Mapping(source = "itemData.idcard", target = "idcard"),
            @Mapping(source = "itemData.birthday", target = "birthday"),
            @Mapping(source = "itemData.birthplace", target = "birthplace"),
            @Mapping(source = "itemData.edu", target = "edu"),
            @Mapping(source = "itemData.entryDate", target = "entryDate"),
            @Mapping(source = "itemData.percentageRate", target = "percentageRate"),
            @Mapping(source = "itemData.contactAddr", target = "contactAddr"),
            @Mapping(source = "itemData.memo", target = "memo"),
    })
    @BeanMapping(ignoreByDefault = true)
    void convertImport(@MappingTarget MemColonel colonel, MemColonelImportExcel itemData);

    PageResult<MemColonelRespVO> convertRespVO(PageResult<MemColonel> memColonelPage);

    MemColonel convert(ColonelDTO colonelDTO);
}
