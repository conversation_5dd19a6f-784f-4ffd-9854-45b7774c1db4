package com.zksr.member.controller.colonelApp.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/4/16 14:49
 * 业务员app保存门店对象
 */
@Data
@ApiModel("业务员app - mem_colonel_app Request VO")
public class MemColonelAppSaveBranchReqVo {
    private static final long serialVersionUID = 1L;

    /** 业务员id */
    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 门店名称 */
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 门头照 */
    @Excel(name = "门头照")
    @ApiModelProperty(value = "门头照")
    private String branchImages;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 渠道id */
    @Excel(name = "渠道id")
    @ApiModelProperty(value = "渠道id")
    private Long channelId;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 门店id */
    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty("三级区域城市ID, 省市区关联(区域)")
    private Long threeAreaCityId;
}
