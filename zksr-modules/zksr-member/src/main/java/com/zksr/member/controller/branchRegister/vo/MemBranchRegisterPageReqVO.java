package com.zksr.member.controller.branchRegister.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;

/**
 * 门店注册信息对象 mem_branch_register
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@ApiModel("门店注册信息 - mem_branch_register分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemBranchRegisterPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 门店注册信息ID */
    @ApiModelProperty(value = "备注")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchRegisterId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 区域城市名 */
    @Excel(name = "区域城市名")
    @ApiModelProperty(value = "区域城市名")
    private String areaName;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 业务员id */
    @Excel(name = "业务员id")
    @ApiModelProperty(value = "业务员id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 业务员名 */
    @Excel(name = "业务员名")
    @ApiModelProperty(value = "业务员名")
    private String colonelName;

    /** 渠道id */
    @Excel(name = "渠道id")
    @ApiModelProperty(value = "渠道id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long channelId;

    /** 渠道名称 */
    @Excel(name = "渠道名称")
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /** 门头照 */
    @Excel(name = "门头照")
    @ApiModelProperty(value = "门头照")
    private String branchImages;

    /** 审核人 */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long approveMan;

    /** 审核人名称 */
    @Excel(name = "审核人名称")
    @ApiModelProperty(value = "审核人名称")
    private String approveManName;

    /** 审核标识 */
    @Excel(name = "审核标识")
    @ApiModelProperty(value = "审核标识")
    private Integer approveFlag;

    /** 审核时间 */
    @Excel(name = "审核时间")
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveDate;

    /** 门店自动审核标识 */
    @Excel(name = "门店自动审核标识")
    @ApiModelProperty(value = "门店自动审核标识")
    private Integer branchApproveFlag;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 用户类型 0 新用户  1老用户 */
    @Excel(name = "用户类型")
    @ApiModelProperty(value = "用户类型 0 新用户  1老用户")
    private Integer userType;

    @Excel(name = "区域ID集合")
    @ApiModelProperty("区域ID集合")
    private List<Long> areaIdList;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
