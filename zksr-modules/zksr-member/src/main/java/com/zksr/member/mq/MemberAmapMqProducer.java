package com.zksr.member.mq;

import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.system.api.supplier.dto.CheckGeofenceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.support.MessageBuilder;

/**
 * @Description: 猎鹰服务MQ
 * @Author: liuxingyu
 * @Date: 2024/3/28 9:31
 */
@Slf4j
@Configuration
public class MemberAmapMqProducer {

    @Autowired
    private StreamBridge streamBridge;

    /**
     * @Description: 电子围栏校验(生产者)
     * @Author: liuxingyu
     * @Date: 2024/3/28 15:08
     */
    public void checkGeofence(CheckGeofenceDTO checkGeofenceDTO){
        log.info("发送电子围栏校验消息：{} ", checkGeofenceDTO);
        streamBridge.send(MessageConstant.SYSTEM_CHECK_GEOFENCE_TOPIC_OUT_PUT, MessageBuilder.withPayload(checkGeofenceDTO).build());
    }
}
