package com.zksr.member.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.member.domain.MemBranchSupplier;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 门店入驻商关联关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-28
 */
@Mapper
public interface MemBranchSupplierMapper extends BaseMapperX<MemBranchSupplier> {

    /**
    * @Description: 根据门店编号删除绑定的入驻商
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:11
    */
    default void deleteByBranchId(Long branchId){
        delete(new LambdaUpdateWrapper<MemBranchSupplier>().eq(MemBranchSupplier::getBranchId,branchId));
    }

    /**
    * @Description: 根据门店编号获取绑定的入驻商
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:11
    */
    default List<MemBranchSupplier> getSupplierByBranchId(Long branchId){
        return selectList(new LambdaQueryWrapper<MemBranchSupplier>().eq(MemBranchSupplier::getBranchId,branchId));
    }

    /**
    * @Description: 根据入驻商编号获取门店编号
    * @Author: liuxingyu
    * @Date: 2024/3/28 18:17
    */
    default List<MemBranchSupplier> getBySupplier(Long supplierId){
        return selectList(new LambdaQueryWrapper<MemBranchSupplier>().eq(MemBranchSupplier::getSupplierId,supplierId));
    }

    /**
    * @Description: 根据入驻商编号删除绑定关系
    * @Author: liuxingyu
    * @Date: 2024/5/15 11:04
    */
    default Integer deleteBySupplierId(Long supplierId){
        return delete(new LambdaUpdateWrapper<MemBranchSupplier>().eq(MemBranchSupplier::getSupplierId,supplierId));
    }
}
