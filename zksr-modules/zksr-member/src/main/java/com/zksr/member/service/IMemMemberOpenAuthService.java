package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.api.b2bAuth.dto.MemMemberOpenAuthDTO;
import com.zksr.member.domain.MemMemberOpenAuth;
import com.zksr.member.controller.memberB2bAuth.vo.MemMemberOpenAuthPageReqVO;
import com.zksr.member.controller.memberB2bAuth.vo.MemMemberOpenAuthSaveReqVO;

/**
 * b2b openid 认证Service接口
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
public interface IMemMemberOpenAuthService {

    /**
     * 新增b2b openid 认证
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemMemberOpenAuth(@Valid MemMemberOpenAuthSaveReqVO createReqVO);

    /**
     * 修改b2b openid 认证
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemMemberOpenAuth(@Valid MemMemberOpenAuthSaveReqVO updateReqVO);

    /**
     * 删除b2b openid 认证
     *
     * @param b2bAuthOpenId ${pkColumn.columnComment}
     */
    public void deleteMemMemberOpenAuth(Long b2bAuthOpenId);

    /**
     * 批量删除b2b openid 认证
     *
     * @param b2bAuthOpenIds 需要删除的b2b openid 认证主键集合
     * @return 结果
     */
    public void deleteMemMemberOpenAuthByB2bAuthOpenIds(Long[] b2bAuthOpenIds);

    /**
     * 获得b2b openid 认证
     *
     * @param b2bAuthOpenId ${pkColumn.columnComment}
     * @return b2b openid 认证
     */
    public MemMemberOpenAuth getMemMemberOpenAuth(Long b2bAuthOpenId);

    /**
     * 获得b2b openid 认证分页
     *
     * @param pageReqVO 分页查询
     * @return b2b openid 认证分页
     */
    PageResult<MemMemberOpenAuth> getMemMemberOpenAuthPage(MemMemberOpenAuthPageReqVO pageReqVO);

    /**
     * 获取微信是否绑定
     * @param appid
     * @param openid
     * @param branchId
     * @return
     */
    MemMemberOpenAuth getBind(String appid, String openid, Long branchId);

    /**
     * 更新认证授权
     * @param authDTO
     */
    void updateAuth(MemMemberOpenAuthDTO authDTO);
}
