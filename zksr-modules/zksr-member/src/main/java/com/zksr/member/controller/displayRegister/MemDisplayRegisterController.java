package com.zksr.member.controller.displayRegister;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemDisplayRegister;
import com.zksr.member.service.IMemDisplayRegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.displayRegister.vo.MemDisplayRegisterPageReqVO;
import com.zksr.member.controller.displayRegister.vo.MemDisplayRegisterSaveReqVO;
import com.zksr.member.controller.displayRegister.vo.MemDisplayRegisterRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 陈列计划打卡记录Controller
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Api(tags = "管理后台 - 陈列计划打卡记录接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/displayRegister")
public class MemDisplayRegisterController {
    @Autowired
    private IMemDisplayRegisterService memDisplayRegisterService;

    /**
     * 新增陈列计划打卡记录
     */
    @ApiOperation(value = "新增陈列计划打卡记录", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "陈列计划打卡记录", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemDisplayRegisterSaveReqVO createReqVO) {
        return success(memDisplayRegisterService.insertMemDisplayRegister(createReqVO));
    }

    /**
     * 修改陈列计划打卡记录
     */
    @ApiOperation(value = "修改陈列计划打卡记录", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "陈列计划打卡记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemDisplayRegisterSaveReqVO updateReqVO) {
            memDisplayRegisterService.updateMemDisplayRegister(updateReqVO);
        return success(true);
    }

    /**
     * 删除陈列计划打卡记录
     */
    @ApiOperation(value = "删除陈列计划打卡记录", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "陈列计划打卡记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{registerIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] registerIds) {
        memDisplayRegisterService.deleteMemDisplayRegisterByRegisterIds(registerIds);
        return success(true);
    }

    /**
     * 获取陈列计划打卡记录详细信息
     */
    @ApiOperation(value = "获得陈列计划打卡记录详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{registerId}")
    public CommonResult<MemDisplayRegisterRespVO> getInfo(@PathVariable("registerId") Long registerId) {
        MemDisplayRegister memDisplayRegister = memDisplayRegisterService.getMemDisplayRegister(registerId);
        return success(HutoolBeanUtils.toBean(memDisplayRegister, MemDisplayRegisterRespVO.class));
    }

    /**
     * 分页查询陈列计划打卡记录
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得陈列计划打卡记录分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemDisplayRegisterRespVO>> getPage(@Valid MemDisplayRegisterPageReqVO pageReqVO) {
        PageResult<MemDisplayRegister> pageResult = memDisplayRegisterService.getMemDisplayRegisterPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, MemDisplayRegisterRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:register:add";
        /** 编辑 */
        public static final String EDIT = "member:register:edit";
        /** 删除 */
        public static final String DELETE = "member:register:remove";
        /** 列表 */
        public static final String LIST = "member:register:list";
        /** 查询 */
        public static final String GET = "member:register:query";
        /** 停用 */
        public static final String DISABLE = "member:register:disable";
        /** 启用 */
        public static final String ENABLE = "member:register:enable";
    }
}
