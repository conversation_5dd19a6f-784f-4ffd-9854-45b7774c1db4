package com.zksr.member.controller.branch;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.google.common.collect.Lists;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.balance.BalanceApi;
import com.zksr.account.api.balance.vo.AccBalanceRespVO;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.domain.vo.openapi.MemBranchSyncReqVO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.exception.ErrorCode;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.exception.enums.GlobalErrorCodeConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageParam;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.model.ImportResultVo;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.branch.vo.BranchDeleteReqVO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.controller.branch.dto.MemBranchRespDTO;
import com.zksr.member.controller.branch.excel.MemBranchImportExcel;
import com.zksr.member.controller.branch.excel.MemBranchImportO2OExcel;
import com.zksr.member.controller.branch.vo.*;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipRespVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelBranchZipRespVO;
import com.zksr.member.convert.branch.BranchConvert;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.domain.MemBranchRegister;
import com.zksr.member.domain.MemMemberRegister;
import com.zksr.member.service.*;
import com.zksr.report.api.branch.RptBranchApi;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.AppletAgreementPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.BasicSettingPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.WithdrawalSettingPolicyDTO;
import com.zksr.system.api.sms.SmsApi;
import com.zksr.system.api.sms.dto.SmsNoticeDTO;
import com.zksr.trade.api.order.OrderApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.web.pojo.CommonResult.error;
import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.member.enums.ErrorCodeConstants.MEM_BRANCH_USER_CREATE_ERR;

/**
 * 门店信息Controller
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Api(tags = "管理后台 - 门店信息接口", produces = "application/json")
@Validated
@RestController
@Slf4j
@RequestMapping("/branch")
public class MemBranchController {

    @Autowired
    private IMemBranchService memBranchService;

    @Autowired
    private IMemberCacheService memberCacheService;

    @Resource
    private AccountApi accountApi;

    @Resource
    private AreaCityApi areaCityApi;

    @Resource
    private PartnerPolicyApi partnerPolicyApi;
    @Autowired
    private IMemBranchRegisterService memBranchRegisterService;
    @Autowired
    private IMemMemberRegisterService memMemberRegisterService;

    @Resource
    private RptBranchApi rptBranchApi;

    @Autowired
    private IMemColonelBranchZipService memColonelBranchZipServiceImpl;

    @Autowired
    private Cache<Long, BasicSettingPolicyDTO> basicSettingPolicyCache;

    @Autowired
    private IMemBranchLifecycleZipService branchLifecycleZipService;

    @Autowired
    private Cache<Long, BranchDTO> branchDTOCache;

    @Autowired
    private PartnerApi partnerApi;

    @Autowired
    private IMemColonelVisitLogService iMemColonelVisitLogService;

    @Autowired
    private IMemBranchUserService iMemBranchUserService;

    @Resource
    private OrderApi orderApi;
    @Autowired
    private SmsApi smsApi;

    @Autowired
    private BalanceApi balanceApi;

    /**
     * @Description: 获取门店集合
     * @Param:
     * @return: CommonResult<List<MemBranchRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/5/15 9:03
     */
    @ApiOperation(value = "获取门店集合",httpMethod = HttpMethod.GET)
    @GetMapping("/getBranchList")
    public CommonResult<List<MemBranchRespVO>> getBranchList(){
        return success(memBranchService.getBranchList());
    }

    /**
     * @Description: 获取门店集合
     * @Param:
     * @return: CommonResult<List<MemBranchRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/5/15 9:03
     */
    @ApiOperation(value = "获取门店集合(用于入驻商画电子围栏时调用)",httpMethod = HttpMethod.GET)
    @GetMapping("/getBranchListPage")
    public CommonResult<List<MemBranchRespDTO>> getBranchListPage(){
        return success(memBranchService.getBranchListPage());
    }

    /**
     * @Description: 获取门店下拉选
     * @Param: String branch
     * @return: List<MemBranchRespVO>
     * @Author: liuxingyu
     * @Date: 2024/4/12 10:21
     */
    @ApiOperation(value = "获取门店下拉选",httpMethod = HttpMethod.GET)
    @GetMapping("/getBranchDropdown")
    public CommonResult<List<MemBranchRespVO>> getBranchDropdown(BranchDropdownReqVO dropdownReqVO){
        return success(memBranchService.getBranchDropdown(dropdownReqVO));
    }

    /**
     * !@门店 - 新增门店信息
     */
    @ApiOperation(value = "新增门店信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "门店信息", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<String> add(@Valid @RequestBody MemBranchSaveReqVO createReqVO) {
        if (ToolUtil.isNotEmpty(createReqVO.getAreaId()) && ToolUtil.isEmpty(createReqVO.getHdfkMaxAmt())) {
            AreaDTO areaDTO = memberCacheService.getAreaDto(createReqVO.getAreaId());
            if(ToolUtil.isEmpty(areaDTO.getDcId())) {
                throw new ServiceException("该城市未分配运营商");
            }
            BasicSettingPolicyDTO basicSettingPolicyDTO = memberCacheService.getBasicSettingPolicyDTO(areaDTO.getDcId());
            if (ToolUtil.isNotEmpty(basicSettingPolicyDTO)) {
                createReqVO.setHdfkMaxAmt(ToolUtil.isEmpty(basicSettingPolicyDTO.getBranchDefaultHdfkMaxAmt()) ? new BigDecimal("0") : new BigDecimal(basicSettingPolicyDTO.getBranchDefaultHdfkMaxAmt()));
            }
        }
        Long branchId = memBranchService.insertMemBranch(createReqVO);
        memBranchService.reloadBranchDTOCache(branchId);
        //推送 同步门店信息
        memBranchService.syncBranchData(branchId, OperationType.ADD.getCode());
        // 0716   客户通新增门店返回特殊处理
        if (ToolUtil.isNumberNotEmptyAndNotZero(createReqVO.getSyncCreateAccount())) {
            ErrorCode errorCode = createReqVO.getErrorCode();
            if (Objects.nonNull(errorCode)) {
                return error(errorCode.getCode(), errorCode.getMsg());
            }
            String appletName = "未知";
            CommonResult<AppletAgreementPolicyDTO> commonResult = partnerPolicyApi.getAppletAgreementPolicy(SecurityUtils.getLoginUser().getSysCode());
            if (commonResult.isSuccess()) {
                appletName = commonResult.getData().getPartnerName();
            }
            SmsNoticeDTO smsNoticeDTO = new SmsNoticeDTO();
            smsNoticeDTO.setPhone(createReqVO.getContactPhone());
            smsNoticeDTO.setContext("账号：" + createReqVO.getContactPhone() + "，密码：" + createReqVO.getPassword() + "。微信搜索小程序 “" + appletName + "” 即可下单，注册即享限时优惠劵，请尽快使用！");
            smsNoticeDTO.setSystemCode(SecurityContextHolder.getSysCode());
            smsApi.sendNoticeSms(smsNoticeDTO);
            CommonResult<String> stringCommonResult = new CommonResult<>();
            stringCommonResult.setMsg("门店和账号已经创建，账号和密码已短信发送至注册手机");
            stringCommonResult.setData(String.valueOf(branchId));
            stringCommonResult.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
            return stringCommonResult;
        }
        return success(String.valueOf(branchId));
    }
    /**
     * !@门店 - 修改门店信息
     */
    @ApiOperation(value = "修改门店信息", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "门店信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemBranchSaveReqVO updateReqVO) {
        memBranchService.updateMemBranch(updateReqVO);
        memBranchService.reloadBranchDTOCache(updateReqVO.getBranchId());
        //推送 同步门店信息
        memBranchService.syncBranchData(updateReqVO.getBranchId(),OperationType.UPDATE.getCode());
        return success(true);
    }

    @ApiOperation(value = "app修改门店信息", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @Log(title = "门店信息", businessType = BusinessType.UPDATE)
    @PutMapping("/appBranchEdit")
    public CommonResult<Boolean> appBranchEdit(@Valid @RequestBody MemBranchSaveReqVO updateReqVO) {
        memBranchService.updateMemBranch(updateReqVO);
        memBranchService.reloadBranchDTOCache(updateReqVO.getBranchId());
        //推送 同步门店信息
        memBranchService.syncBranchData(updateReqVO.getBranchId(), OperationType.UPDATE.getCode());
        return success(true);
    }
    /**
     * 删除门店信息
     */
    @ApiOperation(value = "删除门店信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "门店信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public CommonResult<BranchDeleteReqVO> remove(@RequestBody Long[] branchIds) {
        BranchDeleteReqVO branchDeleteReqVO = memBranchService.deleteMemBranchByBranchIds(branchIds);
        return success(branchDeleteReqVO);
    }

    /**
     * 获取门店信息详细信息
     */
    @ApiOperation(value = "获得门店信息详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{branchId}")
    public CommonResult<MemBranchRespVO> getInfo(@PathVariable("branchId") Long branchId) {
        MemBranch memBranch = memBranchService.getMemBranch(branchId);
        MemBranchRespVO branchRespVO = HutoolBeanUtils.toBean(memBranch, MemBranchRespVO.class);
        if (Objects.nonNull(branchRespVO) && Objects.nonNull(branchRespVO.getThreeAreaCityId())) {

            SysAreaCityRespVO three = areaCityApi.getById(memBranch.getThreeAreaCityId()).getCheckedData();
            if (Objects.nonNull(three)) {
                SysAreaCityRespVO second = areaCityApi.getById(three.getPid()).getCheckedData();
                SysAreaCityRespVO first = areaCityApi.getById(second.getPid()).getCheckedData();
                branchRespVO.setSecondAreaCityId(second.getAreaCityId());
                branchRespVO.setFirstAreaCityId(first.getAreaCityId());
            }

            //增加平台商
            Long sysCode = memBranch.getSysCode();
            if (!Objects.isNull(sysCode)) {
                PartnerDto checkedData = partnerApi.getPartnerBySource(sysCode.toString()).getCheckedData();
                if (Objects.nonNull(checkedData)) {
                    branchRespVO.setEnableO2o(checkedData.getEnableO2o());
                    branchRespVO.setEnableRetail(checkedData.getEnableRetail());
                }
            }

            //获取该门店的下阶段生命周期信息提醒
            branchRespVO.setNextBranchLifecycleMessage(branchLifecycleZipService.getNextBranchLifecycleMessage(branchId));
        }
        if (Objects.nonNull(branchRespVO)) {
            // 查询账户系统获取门店余额数据
            CommonResult<List<AccBalanceRespVO>> commonResult = balanceApi.getBalanceInfoList(Lists.newArrayList(branchId));
            List<AccBalanceRespVO> balanceRespVOList = commonResult.getCheckedData();
            Map<Long, BigDecimal> balanceMap = balanceRespVOList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AccBalanceRespVO::getBranchId, AccBalanceRespVO::getAmt, (x, y) -> x));
            branchRespVO.setBalanceAmt(balanceMap.getOrDefault(branchId, BigDecimal.ZERO));
        }
        // 查询门店上月标签
        try {
            branchRespVO.setBranchTags(rptBranchApi.getBranchLastMonthTag(branchId).getCheckedData());
        } catch (Exception e){
            // @TODO: 这个逻辑以后删除, 为什么加是因为其他的环境可能没有配置, report服务模块
            log.error("获取门店标签数据异常", e);
        }
        return success(branchRespVO);
    }

    /**
     * 分页查询门店信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得门店信息分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(dcAlias = "`mem_branch`")
    public CommonResult<PageResult<MemBranchRespVO>> getPage(@Valid MemBranchPageReqVO pageReqVO) {
        if (pageReqVO.getIsPage()==0) {
            pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        }
        PageResult<MemBranch> pageResult = memBranchService.getMemBranchPage(pageReqVO);
        PageResult<MemBranchRespVO> branchRespVOPageResult = HutoolBeanUtils.toBean(pageResult, MemBranchRespVO.class);
        branchRespVOPageResult.getList().forEach(branchRespVO -> {
            if (ToolUtil.isNotEmpty(branchRespVO.getColonelId())) {
                ColonelDTO colonelDTO = memberCacheService.getColonel(branchRespVO.getColonelId());
                if (ToolUtil.isNotEmpty(colonelDTO)) {
                    branchRespVO.setColonelName(colonelDTO.getColonelName());
                }
            }

            // 渠道
            ChannelDTO channelDto = memberCacheService.getChannelDto(branchRespVO.getChannelId());
            if (Objects.nonNull(channelDto)) {
                branchRespVO.setChannelName(channelDto.getChannelName());
            }

            // 区域
            AreaDTO areaDTO = memberCacheService.getAreaDto(branchRespVO.getAreaId());
            if (Objects.nonNull(areaDTO)) {
                branchRespVO.setAreaName(areaDTO.getAreaName());
            }

            // 注册时间与注册类型
            MemMemberRegister memberRegister = memMemberRegisterService.getMemberRegisterByBranchId(branchRespVO.getBranchId());
            if (Objects.nonNull(memberRegister)){
                branchRespVO.setRegisterTime(memberRegister.getCreateTime());
                branchRespVO.setRegisterType(0);
                branchRespVO.setRegisterTypeName("自主注册");
            }
            MemBranchRegister memBranchRegister = memBranchRegisterService.getMemBranchRegisterByBranchId(branchRespVO.getBranchId());
            if (Objects.nonNull(memBranchRegister)){
                branchRespVO.setRegisterTime(memBranchRegister.getCreateTime());
                branchRespVO.setRegisterType(1);
                branchRespVO.setRegisterTypeName("业务员拓店");
            }
            if (Objects.isNull(memberRegister) && Objects.isNull(memBranchRegister)){
                BranchDTO branchDto = memberCacheService.getBranchDto(branchRespVO.getBranchId());
                branchRespVO.setRegisterTime(branchDto.getCreateTime());
                branchRespVO.setRegisterType(2);
                branchRespVO.setRegisterTypeName("后台导入");
            }
        });
        return success(branchRespVOPageResult);
    }

    /**
     * 停用门店
     */
    @ApiOperation(value = "停用门店", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "停用门店", businessType = BusinessType.UPDATE)
    @PutMapping("/disable/{branchId}")
    public CommonResult<Boolean> disable(@ApiParam(name = "branchId", value = "门店ID", required = true) @PathVariable("branchId") Long branchId) {
        memBranchService.disable(branchId);
        memBranchService.reloadBranchDTOCache(branchId);
        //推送 同步门店信息
        memBranchService.syncBranchData(branchId,OperationType.UPDATE.getCode());
        return success(true);
    }

    /**
     * 启用门店
     */
    @ApiOperation(value = "启用门店", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "启用门店", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{branchId}")
    public CommonResult<Boolean> enable(@ApiParam(name = "branchId", value = "门店ID", required = true) @PathVariable("branchId") Long branchId) {
        memBranchService.enable(branchId);
        memBranchService.reloadBranchDTOCache(branchId);
        //推送 同步门店信息
        memBranchService.syncBranchData(branchId,OperationType.UPDATE.getCode());
        return success(true);
    }


    /**
     * 审核门店信息
     */
    @ApiOperation(value = "审核门店信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.AUDIT)
    @RequiresPermissions(Permissions.AUDIT)
    @Log(title = "审核门店信息", businessType = BusinessType.OTHER)
    @PostMapping("/auditBranch")
    public CommonResult<Boolean> auditBranch(@RequestBody Long[] branchIds) {
        memBranchService.batchAudit(branchIds);
        for(Long branchId : branchIds){
            memBranchService.reloadBranchDTOCache(branchId);
            //推送 同步门店信息
            memBranchService.syncBranchData(branchId,OperationType.ADD.getCode());
        }
        return success(true);
    }

    /**
     * 获取门店选中数据 (用于选中回显)
     * @param branchIds    门店ID集合
     * @return  获取门店选中数据
     */
    @PostMapping("/getSelectedBatchInfo")
    @ApiOperation(value = "批量获取城市简略信息", httpMethod = HttpMethod.POST)
    public CommonResult<List<MemBranchSelectedRespVO>> getSelectedBatchInfo(@RequestBody List<Long> branchIds) {
        return success(memBranchService.getSelectedMemberBranch(branchIds));
    }


    /**
     * 分页查询门店商账户
     */
    @GetMapping("/accountList")
    @ApiOperation(value = "获得门店账户分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ACCOUNT_LIST)
    @RequiresPermissions(Permissions.ACCOUNT_LIST)
    @DataScope(dcAlias = "`mem_branch`")
    public CommonResult<PageResult<MemBranchAccountRespVO>> getAccountPage(@Valid MemBranchPageReqVO pageReqVO) {
        PageResult<MemBranch> pageResult = memBranchService.getMemBranchPage(pageReqVO);
        // 转换成账户列表
        PageResult<MemBranchAccountRespVO> accountResult = BranchConvert.INSTANCE.conver(pageResult);
        // 获取账户余额
        accountResult.getList().forEach(item -> {
            // 门店欠款
            {
                List<AccAccountDTO> accountDTOList = accountApi.getAccountListByReq(
                        AccAccountDTO
                                .builder()
                                .merchantId(item.getBranchId())
                                .merchantType(MerchantTypeEnum.BRANCH_DEBT.getType())
                                .build()
                ).getCheckedData();
                for (AccAccountDTO accountDTO : accountDTOList) {
                    item.setDebtAmt(item.getDebtAmt().add(accountDTO.getWithdrawableAmt()));
                }
            }
            // 门店储值
            {
                List<AccAccountDTO> accountDTOList = accountApi.getAccountListByReq(
                        AccAccountDTO
                                .builder()
                                .merchantId(item.getBranchId())
                                .merchantType(MerchantTypeEnum.BRANCH.getType())
                                .accountType(NumberPool.INT_ZERO)
                                .build()
                ).getCheckedData();
                for (AccAccountDTO accountDTO : accountDTOList) {
                    item.setWithdrawableAmt(item.getWithdrawableAmt().add(accountDTO.getWithdrawableAmt().subtract(accountDTO.getFrozenAmt())));
                    item.setFrozenAmt(item.getFrozenAmt().add(accountDTO.getFrozenAmt()));
                }
            }
            // 门店赠送
            {
                List<AccAccountDTO> accountDTOList = accountApi.getAccountListByReq(
                        AccAccountDTO
                                .builder()
                                .merchantId(item.getBranchId())
                                .merchantType(MerchantTypeEnum.BRANCH.getType())
                                .accountType(NumberPool.INT_ONE)
                                .build()
                ).getCheckedData();
                for (AccAccountDTO accountDTO : accountDTOList) {
                    item.setGiveAmt(item.getGiveAmt().add(accountDTO.getWithdrawableAmt().subtract(accountDTO.getFrozenAmt())));
                    item.setFrozenAmt(item.getFrozenAmt().add(accountDTO.getFrozenAmt()));
                }
            }
            // 计算提现手续费
            WithdrawalSettingPolicyDTO withdrawalSetting = memberCacheService.getWithdrawalSetting(item.getSysCode());
            if (Objects.nonNull(withdrawalSetting) && StringUtils.isNotEmpty(withdrawalSetting.getBranchWithdrawRate())) {
                item.setBranchWithdrawRate(new BigDecimal(withdrawalSetting.getBranchWithdrawRate()));
            }
        });
        return success(accountResult);
    }


    @ApiOperation(value = "导入门店信息", httpMethod = HttpMethod.POST)
    @Log(title = "导入门店信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/importData")
    //!@导入 - 门店导入
    public CommonResult<ImportResultVo> importData(MultipartFile file) throws Exception
    {
        ExcelUtil<com.zksr.member.api.branch.excel.MemBranchImportExcel> util = new ExcelUtil<>(com.zksr.member.api.branch.excel.MemBranchImportExcel.class);
        List<com.zksr.member.api.branch.excel.MemBranchImportExcel> branchList = util.importExcel(file.getInputStream(), 1);
        ImportResultVo result = memBranchService.importBranchExcel(branchList,"NORMAL");
        return success(result);
    }

    @ApiOperation(value = "导入门店信息O2O", httpMethod = HttpMethod.POST)
    @Log(title = "导入门店信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/importDataO2O")
    //!@导入 - 门店O2O导入
    public CommonResult<ImportResultVo> importDataO2O(MultipartFile file) throws Exception
    {
        ExcelUtil<com.zksr.member.api.branch.excel.MemBranchImportExcel> util = new ExcelUtil<>(com.zksr.member.api.branch.excel.MemBranchImportExcel.class);
        List<com.zksr.member.api.branch.excel.MemBranchImportExcel> branchList = util.importExcel(file.getInputStream(), 1);
        ImportResultVo result = memBranchService.importBranchExcel(branchList,"O2O");
        return success(result);
    }


    @PostMapping ("/selectAfterColonel")
    @ApiOperation(value = "获取门店上次业务员", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<MemColonelBranchZipRespVO> selectAfterColonel(@RequestBody @Valid MemBranchPageReqVO pageReqVO) {
        MemBranch memBranch = HutoolBeanUtils.toBean(pageReqVO, MemBranch.class);
        MemColonelBranchZipRespVO memColonelBranchZipRespVO = memColonelBranchZipServiceImpl.selectMemColonelBranchZip(memBranch);
        return success(memColonelBranchZipRespVO);
    }

    @PostMapping ("/directColonel")
    @ApiOperation(value = "指定业务员", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult directColonel(@RequestBody @Valid MemBranchPageReqVO pageReqVO) {
        MemBranch memBranch = HutoolBeanUtils.toBean(pageReqVO, MemBranch.class);
        MemBranchSaveReqVO memBranchSaveReqVO = HutoolBeanUtils.toBean(pageReqVO, MemBranchSaveReqVO.class);
        Date nowDate = new Date();
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        BasicSettingPolicyDTO basicSettingPolicyDTO = partnerPolicyApi.getBasicSettingPolicy(dcId).getData();
        if (ToolUtil.isNotEmpty(basicSettingPolicyDTO) && !Objects.equals(basicSettingPolicyDTO.getIsSeas(),"1")){
            return CommonResult.error(500,"未开启公海客户功能");
        }
        if (ToolUtil.isNotEmpty(basicSettingPolicyDTO) && ToolUtil.isNotEmpty(basicSettingPolicyDTO.getSeasProtection())) {
            Integer seasDay = Integer.valueOf(basicSettingPolicyDTO.getSeasProtection());
            if (seasDay <= 0){
                return CommonResult.error(500,"请先设置业务员领取配置");
            }
            BranchDTO branchDto = memberCacheService.getBranchDto(memBranch.getBranchId());
            if (ToolUtil.isNotEmpty(branchDto) && ToolUtil.isNotEmpty(branchDto.getColonelId())){
                return CommonResult.error(500,"来晚了一步，客户已经被认领了请刷新-");
            }
            LocalDateTime localDate = nowDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalDateTime newLocalDate = localDate.plusDays(seasDay);
            Date newSeasDate = Date.from(newLocalDate.atZone(ZoneId.systemDefault()).toInstant());
            memBranchSaveReqVO.setSeasTime(newSeasDate);
        }
        memBranchService.updateMemBranch(memBranchSaveReqVO);
        memBranchService.reloadBranchDTOCache(memBranchSaveReqVO.getBranchId());
        return success(true);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "获取门店信息导入模版", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<MemBranchImportExcel> util = new ExcelUtil<MemBranchImportExcel>(MemBranchImportExcel.class);
        String instructions ="填表说明：\n" +
                "1、门店编号：必填项，支持字母加数字的组合,最多25字符限制;\n" +
                "2、门店名称：必填项，最多20个字符，超出导入不成功；\n" +
                "3、门店联系人：必填项，最多20个字符，超出导入不成功；\n" +
                "4、联系电话：必填项，仅限11位数字，超出导入不成功；（注意：此手机号为商城登陆账号，默认密码为123456）\n" +
                "5、联系地址：必填项，地址越详细，定位更精准，不填则导入不成功；\n" +
                "6、渠道类型：非必填项，须和系统有的渠道类型保持一致，否则导入不成功；\n" +
                "7、是否启用：是否启用：非必填，是和否，如果为是，则账号新增即启用，如果为否，则账号新增后自动禁用；\n" +
                "8、城市区域：必填项，须和系统中的城市区域保持一致，否则导入不成功；\n" +
                "9、业务员：必填项，须和系统有的业务员保持一致（业务员状态必须是启用状态），否则导入不成功；\n" +
                "10、是否支持货到付款：非必填项，是和否，如果为是，则需要设置货到付款最大可欠款金额，不填则导入不成功，如果为否，则不需要填最大可欠款金额；\n" +
                "11、备注：非必填项，最多500个字符，否则导入不成功；" +
                "12、平台城市分组：非必填，若填写须和系统有的平台城市分组保持一致，否则导入不成功，不填则默认系统第一个分组；\n" +
                "13、城市(省市区)：必填，须和系统有的省市区保持一致，填写规则为省/市/县区 例如：湖南省/长沙市/雨花区；";
        util.importTemplateExcel(response, "门店信息导入", StringUtils.EMPTY, instructions);
    }

    @PostMapping("/importTemplateO2O")
    @ApiOperation(value = "获取门店信息导入模版", httpMethod = HttpMethod.POST)
    //!@导入模版 - 门店O2O导入
    public void importTemplateO2O(HttpServletResponse response) throws IOException
    {
        ExcelUtil<MemBranchImportO2OExcel> util = new ExcelUtil<MemBranchImportO2OExcel>(MemBranchImportO2OExcel.class);
        String instructions ="填表说明：\n" +
                "1、门店编号：必填项，支持字母加数字的组合,最多25字符限制;\n" +
                "2、门店名称：必填项，最多20个字符，超出导入不成功；\n" +
                "3、门店联系人：必填项，最多20个字符，超出导入不成功；\n" +
                "4、联系电话：必填项，仅限11位数字，超出导入不成功；（注意：此手机号为商城登陆账号，默认密码为123456）\n" +
                "5、联系地址：必填项，地址越详细，定位更精准，不填则导入不成功；\n" +
                "6、渠道类型：非必填项，须和系统有的渠道类型保持一致，否则导入不成功；\n" +
                "7、是否启用：是否启用：非必填，是和否，如果为是，则账号新增即启用，如果为否，则账号新增后自动禁用；\n" +
                "8、城市区域：必填项，须和系统中的城市区域保持一致，否则导入不成功；\n" +
                "9、业务员：必填项，须和系统有的业务员保持一致（业务员状态必须是启用状态），否则导入不成功；\n" +
                "10、是否支持货到付款：非必填项，是和否，如果为是，则需要设置货到付款最大可欠款金额，不填则导入不成功，如果为否，则不需要填最大可欠款金额；\n" +
                "11、备注：非必填项，最多500个字符，否则导入不成功；" +
                "12、平台城市分组：非必填，若填写须和系统有的平台城市分组保持一致，否则导入不成功，不填则默认系统第一个分组；\n" +
                "13、城市(省市区)：必填，须和系统有的省市区保持一致，填写规则为省/市/县区 例如：湖南省/长沙市/雨花区；\n" +
                "14、分销渠道：必填项，如美的零售等，须和系统有的分销渠道保持一致，否则导入不成功；\n" +
                "15、分销模式：非必填项，如O2O等，须和系统有的分销模式保持一致，否则导入不成功；\n" +
                "16、分润模式：非必填项，如按商品指定金额等，须和系统有的分润模式保持一致，否则导入不成功；\n" +
                "17、分润比例：非必填项，数值类型，填写格式需符合系统要求，否则导入不成功；\n" +
                "18、商户号：非必填项，须和系统有的商户号保持一致，否则导入不成功；\n" +
                "19、商户号进件单号：非必填项，须和系统有的商户号进件单号保持一致，否则导入不成功；\n" +
                "20、商户号全称：非必填项，须和系统有的商户号全称保持一致，否则导入不成功；\n" +
                "21、纳税人识别码：非必填项；\n" +
                "22、美云销商户编码：分销渠道=美的零售，且分销模式=O2O时，为必填项；"
                ;
        util.importTemplateExcel(response, "门店信息导入", StringUtils.EMPTY, instructions);
    }

    /**
     *  区域门店信息初始化同步到入驻商第三方系统
     */
    @ApiOperation(value = "区域门店信息初始化同步到入驻商第三方系统", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SYNC)
    @RequiresPermissions(Permissions.SYNC)
    @Log(title = "区域门店信息初始化", businessType = BusinessType.OTHER)
    @PostMapping("/syncAreaBranch")
    public CommonResult<Boolean> syncAreaBranch(@Valid @RequestBody MemBranchSyncReqVO vo) {
        memBranchService.syncAreaBranch(vo);
        return success(true);
    }

    /**
     *  门店批量编辑
     */
    @ApiOperation(value = "门店批量编辑", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "门店批量编辑", businessType = BusinessType.UPDATE)
    @PostMapping("/branchBatchEdit")
    public CommonResult<String> branchBatchEdit(@Valid @RequestBody BranchBatchEditVO reqVo) {
        return success(memBranchService.branchBatchEdit(reqVo));
    }

    /**
     * 查询门店状态变更记录
     */
    @GetMapping("/getBranchLifecycleStageList")
    @ApiOperation(value = "查询门店状态变更记录", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    public CommonResult<List<MemBranchLifecycleZipRespVO>> getBranchLifecycleStageList(@Valid @RequestParam("branchId") Long branchId) {
        return success(branchLifecycleZipService.getBranchLifecycleStageList(branchId));
    }

    /**
     *  门店批量翻新
     */
    @ApiOperation(value = "门店批量翻新", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "门店批量翻新", businessType = BusinessType.UPDATE)
    @PostMapping("/branchLifecycleBatchNew")
    public CommonResult<Boolean> branchLifecycleBatchNew(@Valid @RequestBody List<Long> branchIdList) {
        Set<Long> newBranchIdList = branchLifecycleZipService.branchLifecycleBatchNew(branchIdList);

        //清除缓存
        if(ToolUtil.isNotEmpty(newBranchIdList)){
            branchDTOCache.removeAll(newBranchIdList);
        }


        return success(true);
    }


    /**
     * 分页查询门店信息
     */
    @GetMapping("/app-list")
    @ApiOperation(value = "获得门店信息分页列表(客户通APP)", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @DataScope(dcAlias = "`mem_branch`")
    public CommonResult<PageResult<MemBranchRespVO>> getAppPage(@Valid MemBranchPageReqVO pageReqVO) {
        if (pageReqVO.getIsPage() == 0) pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<MemBranch> pageResult = memBranchService.getMemBranchPage(pageReqVO);
        PageResult<MemBranchRespVO> branchRespVOPageResult = HutoolBeanUtils.toBean(pageResult, MemBranchRespVO.class);
        branchRespVOPageResult.getList().forEach(branchRespVO -> {
            if (ToolUtil.isNotEmpty(branchRespVO.getColonelId())) {
                ColonelDTO colonelDTO = memberCacheService.getColonel(branchRespVO.getColonelId());
                if (ToolUtil.isNotEmpty(colonelDTO)) {
                    branchRespVO.setColonelName(colonelDTO.getColonelName());
                }
            }

           /* // 渠道
            ChannelDTO channelDto = memberCacheService.getChannelDto(branchRespVO.getChannelId());
            if (Objects.nonNull(channelDto)) {
                branchRespVO.setChannelName(channelDto.getChannelName());
            }

            // 区域
            AreaDTO areaDTO = memberCacheService.getAreaDto(branchRespVO.getAreaId());
            if (Objects.nonNull(areaDTO)) {
                branchRespVO.setAreaName(areaDTO.getAreaName());
            }*/

            // 注册时间与注册类型
           /* MemMemberRegister memberRegister = memMemberRegisterService.getMemberRegisterByBranchId(branchRespVO.getBranchId());
            if (Objects.nonNull(memberRegister)){
                branchRespVO.setRegisterTime(memberRegister.getCreateTime());
                branchRespVO.setRegisterType(0);
                branchRespVO.setRegisterTypeName("自主注册");
            }
            MemBranchRegister memBranchRegister = memBranchRegisterService.getMemBranchRegisterByBranchId(branchRespVO.getBranchId());
            if (Objects.nonNull(memBranchRegister)){
                branchRespVO.setRegisterTime(memBranchRegister.getCreateTime());
                branchRespVO.setRegisterType(1);
                branchRespVO.setRegisterTypeName("业务员拓店");
            }
            if (Objects.isNull(memberRegister) && Objects.isNull(memBranchRegister)){
                BranchDTO branchDto = memberCacheService.getBranchDto(branchRespVO.getBranchId());
                branchRespVO.setRegisterTime(branchDto.getCreateTime());
                branchRespVO.setRegisterType(2);
                branchRespVO.setRegisterTypeName("后台导入");
            }*/

            //距上次拜访时间
            Long lastVisitDays = iMemColonelVisitLogService.getDaysSinceLastVisitByBranchId(branchRespVO.getBranchId());
            if (!Objects.isNull(lastVisitDays)) {
                branchRespVO.setLastVisitDays(lastVisitDays);
            }

            //距上次订货时间
            Long lastOrderDays = orderApi.getOrderLastTimeDescriptionByBranchId(branchRespVO.getBranchId()).getCheckedData();
            if (!Objects.isNull(lastOrderDays)) {
                branchRespVO.setLastOrderDays(lastOrderDays);
            }

        });
        return success(branchRespVOPageResult);
    }

    /**
     * 获取门店信息详细信息(客户通APP)
     */
    @ApiOperation(value = "获得门店信息详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @GetMapping(value = "/getBranchInfo/{branchId}")
    public CommonResult<MemBranchRespVO> getBranchInfo(@PathVariable("branchId") Long branchId) {
        MemBranch memBranch = memBranchService.getMemBranch(branchId);
        MemBranchRespVO branchRespVO = HutoolBeanUtils.toBean(memBranch, MemBranchRespVO.class);
        if (Objects.nonNull(branchRespVO)/* && Objects.nonNull(branchRespVO.getThreeAreaCityId())*/) {
            /*SysAreaCityRespVO three = areaCityApi.getById(memBranch.getThreeAreaCityId()).getCheckedData();
            if (Objects.nonNull(three)) {
                SysAreaCityRespVO second = areaCityApi.getById(three.getPid()).getCheckedData();
                SysAreaCityRespVO first = areaCityApi.getById(second.getPid()).getCheckedData();
                branchRespVO.setSecondAreaCityId(second.getAreaCityId());
                branchRespVO.setFirstAreaCityId(first.getAreaCityId());
            }*/

            branchRespVO.setBranchUserCount(iMemBranchUserService.countByBranchId(branchId));
            //获取该门店的下阶段生命周期信息提醒
            /*branchRespVO.setNextBranchLifecycleMessage(branchLifecycleZipService.getNextBranchLifecycleMessage(branchId));*/
        }
        // 查询门店上月标签
        /*try {
            branchRespVO.setBranchTags(rptBranchApi.getBranchLastMonthTag(branchId).getCheckedData());
        } catch (Exception e){
            // @TODO: 这个逻辑以后删除, 为什么加是因为其他的环境可能没有配置, report服务模块
            log.error("获取门店标签数据异常", e);
        }*/
        return success(branchRespVO);
    }

    /**
     * 修改门店信息(客户通APP)
     */
    @ApiOperation(value = "修改门店信息(客户通APP)", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @Log(title = "修改门店信息(客户通APP)", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/editBranchInfo")
    public CommonResult<Boolean> editBranchInfo(@Valid @RequestBody MemBranchSaveReqVO updateReqVO) {
        memBranchService.updateMemBranch(updateReqVO);
        memBranchService.reloadBranchDTOCache(updateReqVO.getBranchId());
        //推送 同步门店信息
        memBranchService.syncBranchData(updateReqVO.getBranchId(), OperationType.UPDATE.getCode());
        return success(true);
    }

    /**
     * !@门店 - 新增门店信息（客户通APP）
     */
    @ApiOperation(value = "新增门店信息（客户通APP）", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @Log(title = "门店信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public CommonResult<String> addBranchInfo(@Valid @RequestBody MemBranchSaveReqVO createReqVO) {
        if (ToolUtil.isNotEmpty(createReqVO.getAreaId()) && ToolUtil.isEmpty(createReqVO.getHdfkMaxAmt())) {
            AreaDTO areaDTO = memberCacheService.getAreaDto(createReqVO.getAreaId());
            if(ToolUtil.isEmpty(areaDTO.getDcId())) {
                throw new ServiceException("该城市未分配运营商");
            }
            BasicSettingPolicyDTO basicSettingPolicyDTO = memberCacheService.getBasicSettingPolicyDTO(areaDTO.getDcId());
            if (ToolUtil.isNotEmpty(basicSettingPolicyDTO)) {
                createReqVO.setHdfkMaxAmt(ToolUtil.isEmpty(basicSettingPolicyDTO.getBranchDefaultHdfkMaxAmt()) ? new BigDecimal("0") : new BigDecimal(basicSettingPolicyDTO.getBranchDefaultHdfkMaxAmt()));
            }
        }
        CommonResult<String> stringCommonResult = new CommonResult<>();
        Long branchId = memBranchService.insertMemBranch(createReqVO);
        memBranchService.reloadBranchDTOCache(branchId);
        //推送 同步门店信息
        memBranchService.syncBranchData(branchId, OperationType.ADD.getCode());
        // 0716   客户通新增门店返回特殊处理
        if (ToolUtil.isNumberNotEmptyAndNotZero(createReqVO.getSyncCreateAccount())) {
            ErrorCode errorCode = createReqVO.getErrorCode();
            if (Objects.nonNull(errorCode)) {
                if(MEM_BRANCH_USER_CREATE_ERR.getCode().equals(errorCode.getCode())){
                    //对前端APP特殊处理（因前端控件问题）
                    return error(200, errorCode.getMsg(), ObjectUtil.isNotEmpty(branchId)?String.valueOf(branchId): null);
                }
                return error(errorCode.getCode(), errorCode.getMsg());
            }
            String appletName = "未知";
            CommonResult<AppletAgreementPolicyDTO> commonResult = partnerPolicyApi.getAppletAgreementPolicy(SecurityUtils.getLoginUser().getSysCode());
            if (commonResult.isSuccess()) {
                appletName = commonResult.getData().getPartnerName();
            }
            SmsNoticeDTO smsNoticeDTO = new SmsNoticeDTO();
            smsNoticeDTO.setPhone(createReqVO.getContactPhone());
            smsNoticeDTO.setContext("账号：" + createReqVO.getContactPhone() + "，密码：" + createReqVO.getPassword() + "。微信搜索小程序 “" + appletName + "” 即可下单，注册即享限时优惠劵，请尽快使用！");
            smsNoticeDTO.setSystemCode(SecurityContextHolder.getSysCode());
            smsApi.sendNoticeSms(smsNoticeDTO);
            stringCommonResult.setMsg("门店和账号已经创建，账号和密码已短信发送至注册手机");
            stringCommonResult.setData(String.valueOf(branchId));
            stringCommonResult.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
            return stringCommonResult;
        }
        stringCommonResult.setMsg("门店创建成功");
        stringCommonResult.setData(String.valueOf(branchId));
        stringCommonResult.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        return stringCommonResult;/*success(String.valueOf(branchId));*/
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:branch:add";
        /** 编辑 */
        public static final String EDIT = "member:branch:edit";
        /** 删除 */
        public static final String DELETE = "member:branch:remove";
        /** 列表 */
        public static final String LIST = "member:branch:list";
        /** 查询 */
        public static final String GET = "member:branch:query";
        /** 停用 */
        public static final String DISABLE = "member:branch:disable";
        /** 启用 */
        public static final String ENABLE = "member:branch:enable";
        /** 审核 */
        public static final String AUDIT = "member:branch:audit";
        /** 账户列表 */
        public static final String ACCOUNT_LIST = "member:branch:accountList";

        public static final String IMPORT = "member:branch:import";

        /** 同步 */
        public static final String SYNC = "member:branch:sync";
    }
}
