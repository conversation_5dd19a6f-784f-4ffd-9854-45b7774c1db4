package com.zksr.member.api.complain;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.complain.dto.MemComplainDTO;
import com.zksr.member.api.complain.excel.MemComplainExcel;
import com.zksr.member.domain.MemComplain;
import com.zksr.member.service.IMemComplainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2024/5/7 18:29
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Slf4j
@ApiIgnore
public class ComplainApiImpl implements ComplainApi {
    @Autowired
    private IMemComplainService memComplainService;

    /**
     * 查询投诉列表
     *
     * @param memComplainVO
     * @return
     */
    //@InnerAuth
    @Override
    public PageResult<MemComplainVO> getComplainList(MemComplainVO memComplainVO) {

        PageResult<MemComplainVO> pageResult = memComplainService.getComplainList(memComplainVO);
        return pageResult;
    }

    @Override
    public MemComplainVO getComplainById(String complainId) {
        MemComplainVO complain = memComplainService.getComplainByComplainId(complainId);
        return complain;
    }

    @Override
    public CommonResult<String> addComplain(MemComplainDTO memComplainDTO) {
        log.info("投诉apiImpl");
        return memComplainService.insertComplain(memComplainDTO);
    }

    public CommonResult<List<MemComplainExcel>> getComplainExcel(MemComplainVO memComplainVO){
        return CommonResult.success(HutoolBeanUtils.toBean(memComplainService.getComplainPageList(memComplainVO).getList(),MemComplainExcel.class));
    }
}
