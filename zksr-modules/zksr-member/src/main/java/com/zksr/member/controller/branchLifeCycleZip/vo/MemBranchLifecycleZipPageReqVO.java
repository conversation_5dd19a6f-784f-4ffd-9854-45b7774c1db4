package com.zksr.member.controller.branchLifeCycleZip.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.*;
import java.util.List;

/**
 * 门店生命周期拉链对象 mem_branch_lifecycle_zip
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@ApiModel("门店生命周期拉链 - mem_branch_lifecycle_zip分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemBranchLifecycleZipPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 门店生命周期拉链表ID */
    @ApiModelProperty(value = "门店生命周期拉链表ID")
    private Long branchLifecycleZipId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id", required = true)
    private Long branchId;

    /** 上一次生命周期code */
    @Excel(name = "上一次生命周期code")
    @ApiModelProperty(value = "上一次生命周期code")
    private Integer lastLifecycleStage;

    /** 生命周期code */
    @Excel(name = "生命周期code")
    @ApiModelProperty(value = "生命周期code", required = true)
    private Integer lifecycleStage;

    /** 开始日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "开始日期", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "开始日期", required = true)
    private Date startDate;

    /** 结束日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "结束日期", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "结束日期", required = true)
    private Date endDate;

    /** 触发事件备注 */
    @Excel(name = "触发事件备注")
    @ApiModelProperty(value = "触发事件备注")
    private String startMemo;

    /** 开始日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "变更开始日期", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "变更开始日期", required = true)
    private Date changeStartDate;

    /** 结束日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "变更结束日期", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "变更结束日期", required = true)
    private Date changeEndDate;

    /** 城市ID */
    @Excel(name = "城市ID")
    @ApiModelProperty(value = "城市ID", required = true)
    private Long areaId;

    /** 城市ID列表 */
    @ApiModelProperty(value = "城市ID列表", required = false)
    private List<Long> areaIdList;

    /** 渠道ID */
    @Excel(name = "渠道ID")
    @ApiModelProperty(value = "渠道ID", required = true)
    private Long channelId;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID", required = true)
    private Long colonelId;

}
