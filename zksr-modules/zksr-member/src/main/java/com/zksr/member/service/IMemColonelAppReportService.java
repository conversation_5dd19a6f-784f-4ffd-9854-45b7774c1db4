package com.zksr.member.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.controller.colonelApp.vo.ColonelBranchAnalyseInfoRespVO;
import com.zksr.report.api.branch.vo.ColonelDecisionTotalReqVO;
import com.zksr.member.controller.colonelApp.vo.ColonelDecisionTotalRespVO;
import com.zksr.report.api.branch.vo.*;

/**
 * <AUTHOR>
 * @Date 2024/4/16 14:37
 * 业务员app报表
 */
public interface IMemColonelAppReportService {

    /**
     * 获取业务员本月上月目标任务完成统计
     */
    ColonelDecisionTotalRespVO getDecisionTotal(ColonelDecisionTotalReqVO reqVO);

    /**
     * 业务员首页门店等级统计
     */
    ColonelIndexBranchLevelTotalRespVO indexBranchLevelTotal(ColonelDecisionTotalReqVO reqVO);

    /**
     * 业务员首页月周下单统计
     */
    ColonelBranchMonthWeekTotalRespVO indexWeekSaleTotal(ColonelDecisionTotalReqVO reqVO);

    /**
     * 业务员APP首页-月订货类别占比
     */
    ColonelBranchSaleCategoryTotalRespVO indexSaleCategoryTotal(ColonelDecisionTotalReqVO reqVO);

    /**
     * 业务员管理门店标签统计
     */
    ColonelLevelTotalRespVO branchLevelColonelList(ColonelBranchTagPageReqVO reqVO);

    /**
     * 业务员标签门店列表
     * */
    PageResult<ColonelTagBranchRespVO> tagLevelBranchList(TagBranchListReqVO reqVO);

    /**
     * 门店数据分析详情
     */
    ColonelBranchAnalyseInfoRespVO branchAnalyseInfo(BranchAnalyseInfoReqVO reqVO);

    /**
     * 24小时下单数量
     */
    ColonelHoursSaleRespVO hourSale(ColonelDecisionTotalReqVO reqVO);
}
