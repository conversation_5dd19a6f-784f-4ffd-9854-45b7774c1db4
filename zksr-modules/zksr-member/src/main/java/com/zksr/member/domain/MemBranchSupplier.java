package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.annotation.Excel;
import lombok.*;

/**
 * 门店入驻商关联关系对象 mem_branch_supplier
 *
 * <AUTHOR>
 * @date 2024-03-28
 */
@TableName(value = "mem_branch_supplier")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemBranchSupplier{
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 门店id;门店id */
    @Excel(name = "门店id;门店id")
    private Long branchId;

}
