package com.zksr.member.controller.colonel;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantPageReqVO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantRespVO;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageParam;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.member.api.colonel.excel.MemColonelImportExcel;
import com.zksr.member.controller.colonel.vo.MemColonelAccountRespVO;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import com.zksr.member.convert.colonel.MemberColonelConvert;
import com.zksr.member.domain.MemColonel;
import com.zksr.member.service.IMemColonelService;
import com.zksr.system.api.RemoteUserService;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.GET_PAY_PLATFORM_ERR;
import static com.zksr.account.enums.ErrorCodeConstants.THE_SALESMAN_NAME_DOES_NOT_EXIST;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 业务员信息Controller
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Api(tags = "管理后台 - 业务员信息接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/colonel")
public class MemColonelController {
    @Autowired
    private IMemColonelService memColonelService;

    @Autowired
    private PlatformMerchantApi platformMerchantApi;

    @Resource
    private AccountApi accountApi;

    @Autowired
    private RemoteUserService remoteUserService;

    @Resource
    private AreaCityApi areaCityApi;

    /**
     * 新增业务员信息
     */
    @ApiOperation(value = "新增业务员信息", httpMethod = "POST", notes = "权限字符:member:colonel:add")
    @RequiresPermissions("member:colonel:add")
    @Log(title = "业务员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemColonelSaveReqVO createReqVO) {
        return success(memColonelService.insertMemColonel(createReqVO));
    }

    /**
     * 修改业务员信息
     */
    @ApiOperation(value = "修改业务员信息", httpMethod = "PUT", notes = "权限字符:member:colonel:edit")
    @RequiresPermissions("member:colonel:edit")
    @Log(title = "业务员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemColonelSaveReqVO updateReqVO) {
        memColonelService.updateMemColonel(updateReqVO);
        return success(true);
    }

    /**
     * 业务员审核
     */
    @ApiOperation(value = "业务员审核", httpMethod = "PUT", notes = "权限字符:member:colonel:audit")
    @RequiresPermissions("member:colonel:audit")
    @Log(title = "业务员审核", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/salesmanAudit")
    public CommonResult<Boolean> salesmanAudit(@Valid @RequestBody MemColonelSaveReqVO updateReqVO) {
        memColonelService.salesmanAudit(updateReqVO);
        return success(true);
    }

    /**
     * 删除业务员信息
     */
    @ApiOperation(value = "删除业务员信息", httpMethod = "POST", notes = "权限字符:member:colonel:remove")
    @RequiresPermissions("member:colonel:remove")
    @Log(title = "业务员信息", businessType = BusinessType.DELETE)
    @PostMapping(value = "/remove")
    public CommonResult<Boolean> remove(@RequestBody Long[] colonelIds) {
        memColonelService.deleteMemColonelByColonelIds(colonelIds);
        return success(true);
    }

    /**
     * 获取业务员信息详细信息
     */
    @ApiOperation(value = "获得业务员信息详情", httpMethod = "GET", notes = "权限字符:member:colonel:query")
    //@RequiresPermissions("member:colonel:query")
    @GetMapping(value = "/{colonelId}")
    public CommonResult<MemColonelRespVO> getInfo(@PathVariable("colonelId") Long colonelId) {
        MemColonel memColonel = memColonelService.getMemColonel(colonelId);
        // 获取支付平台信息
        CommonResult<PlatformMerchantDTO> platformMerchant = platformMerchantApi.getPlatformMerchant(MerchantTypeEnum.COLONEL.getType(), memColonel.getColonelId(), memColonel.getSysCode());
        if (!platformMerchant.isSuccess()) {
            throw exception(GET_PAY_PLATFORM_ERR);
        }
        // 转换返回实体
        MemColonelRespVO colonelRespVO = MemberColonelConvert.INSTANCE.convert(memColonel);
        // 获取进件信息
        List<AccPlatformMerchantRespVO> merchantRespVOS = platformMerchantApi.getMerchantList(
                AccPlatformMerchantPageReqVO.builder()
                        .merchantId(memColonel.getColonelId())
                        .build()
        ).getCheckedData();
        colonelRespVO.setPlatformSimpleBindList(BeanUtils.toBean(merchantRespVOS, PlatformSimpleBindVO.class));

        //获取业务员的用户账号
        CommonResult<String> userIdResult = remoteUserService.getBySysUserId(memColonel.getUserId());
        if (userIdResult.isSuccess()){
            colonelRespVO.setUserName(userIdResult.getData());
        }
        if(ObjectUtil.isNotEmpty(memColonel.getDevelopPeopleId())){
            String colonelName = memColonelService.getColonelName(memColonel.getDevelopPeopleId());
            if(StringUtils.isEmpty(colonelName)){
                throw exception(THE_SALESMAN_NAME_DOES_NOT_EXIST);
            }
            colonelRespVO.setDevelopPeopleName(colonelName);
        }
        if (Objects.nonNull(memColonel) && Objects.nonNull(memColonel.getThreeAreaCityId())) {
            SysAreaCityRespVO three = areaCityApi.getById(memColonel.getThreeAreaCityId()).getCheckedData();
            if (Objects.nonNull(three)) {
                SysAreaCityRespVO second = areaCityApi.getById(three.getPid()).getCheckedData();
                SysAreaCityRespVO first = areaCityApi.getById(second.getPid()).getCheckedData();
                colonelRespVO.setSecondAreaCityId(second.getAreaCityId());
                colonelRespVO.setFirstAreaCityId(first.getAreaCityId());
            }
        }
        return success(colonelRespVO);
    }

    /**
     * 分页查询业务员信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得业务员信息分页列表", httpMethod = "GET", notes = "权限字符:member:colonel:list")
    @RequiresPermissions("member:colonel:list")
    @DataScope(dcAlias = "mc")
    public CommonResult<PageResult<MemColonelRespVO>> getPage(@Valid MemColonelPageReqVO pageReqVO) {
        Integer auditState = pageReqVO.getAuditState();
        // 如果 auditState 为空，则默认设置为 1
        if (auditState != 1) {
            // 如果 auditState 不为空且不为 1，则使用当前传过来的值
            pageReqVO.setAuditState(auditState);
        }
        if(auditState == 3){
            //查看全部
            pageReqVO.setAuditState(null);
        }
        PageResult<MemColonel> pageResult = memColonelService.getMemColonelPage(pageReqVO);
        return success(MemberColonelConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 分页查询业务员管理员信息
     */
    @GetMapping("/colonelAdmin")
    @ApiOperation(value = "分页查询业务员管理员信息", httpMethod = "GET", notes = "权限字符:member:colonel:list")
    @RequiresPermissions("member:colonel:list")
    @DataScope(dcAlias = "mc", dcFieldAlias = SystemConstants.AREA_ID)
    public CommonResult<PageResult<MemColonelRespVO>> getPageColonelAdmin(@Valid MemColonelPageReqVO pageReqVO) {
        pageReqVO.setIsColonelAdmin(StatusConstants.STATUS_1);
        pageReqVO.setStatus(StatusConstants.STATE_ENABLE);
        if (pageReqVO.getIsPage()==0) pageReqVO.setPageSize(999);
        PageResult<MemColonel> pageResult = memColonelService.getMemColonelPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, MemColonelRespVO.class));
    }

    /**
     * 分页查询未绑定关系的业务员
     */
    @GetMapping("/colonelNotRelation")
    @ApiOperation(value = "分页查询未绑定关系的业务员", httpMethod = "GET", notes = "权限字符:member:colonel:list")
    @RequiresPermissions("member:colonel:list")
    public CommonResult<PageResult<MemColonelRespVO>> getPageColonelNotRelation(@Valid MemColonelPageReqVO pageReqVO) {
        pageReqVO.setIsColonelAdmin(StatusConstants.STATUS_2);
        return success(memColonelService.getColonelNotRelationPage(pageReqVO));
    }

    /**
     * 停用业务员
     */
    @ApiOperation(value = "停用业务员", httpMethod = HttpMethod.PUT, notes = "member:colonel:disable")
    @RequiresPermissions("member:colonel:disable")
    @Log(title = "停用业务员", businessType = BusinessType.UPDATE)
    @PutMapping("/disable/{colonelId}")
    public CommonResult<Boolean> disable(@ApiParam(name = "colonelId", value = "停用业务员", required = true) @PathVariable("colonelId") Long colonelId) {
        memColonelService.disable(colonelId);
        return success(true);
    }

    /**
     * 启用业务员
     */
    @ApiOperation(value = "启用业务员", httpMethod = HttpMethod.PUT, notes = "member:colonel:enable")
    @RequiresPermissions("member:colonel:enable")
    @Log(title = "启用业务员", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{colonelId}")
    public CommonResult<Boolean> enable(@ApiParam(name = "colonelId", value = "启用业务员", required = true) @PathVariable("colonelId") Long colonelId) {
        memColonelService.enable(colonelId);
        return success(true);
    }

    /**
     * 分页查询账户列表
     */
    @GetMapping("/accountList")
    @ApiOperation(value = "获得业务员账户分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ACCOUNT_LIST)
    @RequiresPermissions(Permissions.ACCOUNT_LIST)
    public CommonResult<PageResult<MemColonelAccountRespVO>> getAccountPage(@Valid MemColonelPageReqVO pageReqVO) {
        PageResult<MemColonel> pageResult = memColonelService.getMemColonelPage(pageReqVO);
        // 转换成账户列表
        PageResult<MemColonelAccountRespVO> accountResult = MemberColonelConvert.INSTANCE.convert(pageResult);

        // 获取账户余额
        accountResult.getList().forEach(item -> {
            AccAccountDTO account = accountApi.getAccount(item.getColonelId(), MerchantTypeEnum.COLONEL.getType(), PayChannelEnum.WALLET.getCode()).getCheckedData();
            // 写入金额
            MemberColonelConvert.INSTANCE.convert(item, account);
        });
        return success(accountResult);
    }

    /**
     * 分页查询账户列表
     */
    @GetMapping("/MemColonelSalesAmtList")
    @ApiOperation(value = "获得业务员销售金额分页列表", httpMethod = HttpMethod.GET, notes = "权限字符:member:colonel:list")
    @RequiresPermissions("member:colonel:list")
    @DataScope(dcAlias = "mc", dcFieldAlias = SystemConstants.AREA_ID)
    public CommonResult<PageResult<MemColonelRespVO>> MemColonelSalesAmtList(@Valid MemColonelPageReqVO pageReqVO) {
        return success(memColonelService.MemColonelSalesAmtList(pageReqVO));
    }

    /**
     * 分页查询业务员信息
     */
    @GetMapping("/getMemColonelNumPage")
    @ApiOperation(value = "获得业务员拜访次数或者理货次数", httpMethod = "GET", notes = "权限字符:member:colonel:list")
    @RequiresPermissions("member:colonel:list")
    public CommonResult<PageResult<MemColonelRespVO>> getMemColonelNumPage(@Valid MemColonelPageReqVO pageReqVO) {
        return success(memColonelService.getMemColonelNumPage(pageReqVO));
    }

    @ApiOperation(value = "导入业务员信息", httpMethod = HttpMethod.POST)
    @Log(title = "导入业务员信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/importData")
    public CommonResult<String> importData(MultipartFile file) throws Exception
    {
        ExcelUtil<MemColonelImportExcel> util = new ExcelUtil<>(MemColonelImportExcel.class);
        List<MemColonelImportExcel> colonelList = util.importExcel(file.getInputStream(), 1);
        String message = memColonelService.impordData(colonelList);
        return success(message);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "获取业务员信息导入模版", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<MemColonelImportExcel> util = new ExcelUtil<>(MemColonelImportExcel.class);
        String instructions ="填表说明：\n" +
                "1、姓名：必填项，限制字符20个字，超出或不填则导入不成功；\n" +
                "2、性别：必填项，男或女，不填则导入不成功；\n" +
                "3、手机号：必填项，仅限11位数字，超出或不填导入不成功；\n" +
                "4、业务员账号：必填项，仅限11位数字，超出或不填导入不成功；\n" +
                "5、密码：非必填项，不填则默认为123456；\n" +
                "6、所属城市：必填项，须和系统中的城市区域保持一致，超出或不填导入不成功；\n" +
                "7、职务：必填项，业务专员或业务经理，不填则导入不成功；\n" +
                "8、是否业务管理员：必填项，不填则导入不成功；\n" +
                "9、上级业务员：非必填，填入的上级业务员必须与系统保持一致，否则导入不成功；\n" +
                "10、身份证号：必填项，最多18位字符，超出或不填导入不成功；\n" +
                "11、出生日期：非必填，按照年月日显示，例如：2002-01-01；\n" +
                "12、籍贯、学历：非必填；\n" +
                "13、入职日期：非必填，按照年月日显示，例如：2002-01-01；\n" +
                "14、是否启用：非必填项，不填则默认为是，启用；\n" +
                "15、是否支持APP下单改价：必填项，是和否，是则业务员允许在APP上修改门店下单的订单价格，否则为不允许，不填则导入不成功；\n" +
                "16、是否支持APP退货改价：必填项，是和否，是则业务员允许在APP上帮门店退货时修改退货订单价格，否则为不允许，不填则导入不成功；\n" +
                "17、是否支持下单自动审核：必填项，是和否，是则业务员在APP上可以直接帮门店下单且自动生成在线订单，否则为不允许，不填则导入不成功；\n" +
                "18、提成系数（%）（请输入1-100的数字）：必填项，仅限1-100的数字，否则导入不成功，不填则导入不成功；\n" +
                "19、联系地址、备注：非必填项，最多500个字符，否则导入不成功；\n" +
                "20、城市(省市区)：必填项，须和系统有的省市区保持一致，填写规则为省/市/县区 例如：湖南省/长沙市/雨花区；";
        util.importTemplateExcel(response, "业务员信息导入模板", StringUtils.EMPTY, instructions);
    }

    /**
     * 修改业务员密码
     */
    @ApiOperation(value = "修改业务员密码", httpMethod = "PUT", notes = "权限字符:member:colonel:edit-password")
    @RequiresPermissions("member:colonel:edit-password")
    @Log(title = "业务员信息", businessType = BusinessType.UPDATE)
    @PutMapping("/editColonelPassword")
    public CommonResult<Boolean> editColonelPassword(@Valid @RequestBody MemColonelSaveReqVO updateReqVO) {
        memColonelService.updateMemColonelPassword(updateReqVO);
        return success(true);
    }
    /**
     * 权限字符
     */
    public static class Permissions {
        /** 账户列表 */
        public static final String ACCOUNT_LIST = "system:dc:accountList";

        // 导入
        public static final String IMPORT = "member:colonel:import";
    }
}
