package com.zksr.member.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemDisplayDeliveryMapper;
import com.zksr.member.domain.MemDisplayDelivery;
import com.zksr.member.controller.displayDelivery.vo.MemDisplayDeliveryPageReqVO;
import com.zksr.member.controller.displayDelivery.vo.MemDisplayDeliverySaveReqVO;
import com.zksr.member.service.IMemDisplayDeliveryService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 陈列计划兑付Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Service
public class MemDisplayDeliveryServiceImpl implements IMemDisplayDeliveryService {
    @Autowired
    private MemDisplayDeliveryMapper memDisplayDeliveryMapper;

    /**
     * 新增陈列计划兑付
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemDisplayDelivery(MemDisplayDeliverySaveReqVO createReqVO) {
        // 插入
        MemDisplayDelivery memDisplayDelivery = HutoolBeanUtils.toBean(createReqVO, MemDisplayDelivery.class);
        memDisplayDeliveryMapper.insert(memDisplayDelivery);
        // 返回
        return memDisplayDelivery.getDeliveryId();
    }

    /**
     * 修改陈列计划兑付
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemDisplayDelivery(MemDisplayDeliverySaveReqVO updateReqVO) {
        memDisplayDeliveryMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, MemDisplayDelivery.class));
    }

    /**
     * 删除陈列计划兑付
     *
     * @param deliveryId 主键ID
     */
    @Override
    public void deleteMemDisplayDelivery(Long deliveryId) {
        // 删除
        memDisplayDeliveryMapper.deleteById(deliveryId);
    }

    /**
     * 批量删除陈列计划兑付
     *
     * @param deliveryIds 需要删除的陈列计划兑付主键
     * @return 结果
     */
    @Override
    public void deleteMemDisplayDeliveryByDeliveryIds(Long[] deliveryIds) {
        for(Long deliveryId : deliveryIds){
            this.deleteMemDisplayDelivery(deliveryId);
        }
    }

    /**
     * 获得陈列计划兑付
     *
     * @param deliveryId 主键ID
     * @return 陈列计划兑付
     */
    @Override
    public MemDisplayDelivery getMemDisplayDelivery(Long deliveryId) {
        return memDisplayDeliveryMapper.selectById(deliveryId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemDisplayDelivery> getMemDisplayDeliveryPage(MemDisplayDeliveryPageReqVO pageReqVO) {
        return memDisplayDeliveryMapper.selectPage(pageReqVO);
    }

    private void validateMemDisplayDeliveryExists(Long deliveryId) {
        if (memDisplayDeliveryMapper.selectById(deliveryId) == null) {
            throw exception(MEM_DISPLAY_DELIVERY_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 陈列计划兑付 TODO 补充编号 ==========
    // ErrorCode MEM_DISPLAY_DELIVERY_NOT_EXISTS = new ErrorCode(TODO 补充编号, "陈列计划兑付不存在");


}
