package com.zksr.member.convert.branchRegister;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemBranchRegister;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterRespVO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 门店注册信息 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-23
*/
@Mapper
public interface MemBranchRegisterConvert {

    MemBranchRegisterConvert INSTANCE = Mappers.getMapper(MemBranchRegisterConvert.class);

    MemBranchRegisterRespVO convert(MemBranchRegister memBranchRegister);

    MemBranchRegister convert(MemBranchRegisterSaveReqVO memBranchRegisterSaveReq);

    PageResult<MemBranchRegisterRespVO> convertPage(PageResult<MemBranchRegister> memBranchRegisterPage);
}