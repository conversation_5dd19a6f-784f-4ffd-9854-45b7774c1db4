package com.zksr.member.controller.colonel.vo;

import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 业务员信息对象 mem_colonel
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Data
@ApiModel("业务员信息 - mem_colonel account VO")
public class MemColonelAccountRespVO extends MemColonelRespVO {

    /** 可提现金额 */
    @ApiModelProperty(value = "可提现金额")
    private BigDecimal withdrawableAmt = BigDecimal.ZERO;

    /** 冻结金额 */
    @ApiModelProperty(value = "冻结金额")
    private BigDecimal frozenAmt = BigDecimal.ZERO;

    /** 授信额度 */
    @ApiModelProperty(value = "授信额度")
    private BigDecimal creditAmt = BigDecimal.ZERO;

    /** 账户ID */
    @ApiModelProperty(value = "账户ID")
    private Long accountId;

    /** 支付平台 */
    @ApiModelProperty(value = "支付平台")
    private String payPlatform;
}
