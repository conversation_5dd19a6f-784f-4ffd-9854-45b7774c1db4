package com.zksr.member.mapper;

import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/11/28
 * @desc
 */
@Mapper
public interface ColonelReportMapper {
    Date selectLastVisitTime(@Param("branchId") Long branchId);

    Long countMonthVisit(@Param("monthDate") String monthDate, @Param("branchId") Long branchId);

    List<MemColonelRespVO> selectLastVisitTimeList(@Param("branchIds") List<Long> branchIds);
}
