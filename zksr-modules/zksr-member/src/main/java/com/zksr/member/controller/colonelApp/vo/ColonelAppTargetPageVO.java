package com.zksr.member.controller.colonelApp.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("业务员App-业务员目标查询分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ColonelAppTargetPageVO extends PageParam {

    @ApiModelProperty(value = "业务员Id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelId;

    @ApiModelProperty(value = "区域Id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    @ApiModelProperty(value = "目标年份（yyyy）", required = true)
    private String targetYear;

    @ApiModelProperty(value = "目标月份（MM）", required = true)
    private String targetMonth;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "目标状态", hidden = true)
    private Integer status;
}
