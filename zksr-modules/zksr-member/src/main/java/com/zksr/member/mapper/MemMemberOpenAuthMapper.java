package com.zksr.member.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.api.b2bAuth.dto.MemMemberOpenAuthDTO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemMemberOpenAuth;
import com.zksr.member.controller.memberB2bAuth.vo.MemMemberOpenAuthPageReqVO;


/**
 * b2b openid 认证Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Mapper
public interface MemMemberOpenAuthMapper extends BaseMapperX<MemMemberOpenAuth> {
    default PageResult<MemMemberOpenAuth> selectPage(MemMemberOpenAuthPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemMemberOpenAuth>()
                    .eqIfPresent(MemMemberOpenAuth::getB2bAuthOpenId, reqVO.getB2bAuthOpenId())
                    .eqIfPresent(MemMemberOpenAuth::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemMemberOpenAuth::getOpenid, reqVO.getOpenid())
                    .eqIfPresent(MemMemberOpenAuth::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(MemMemberOpenAuth::getAuthState, reqVO.getAuthState())
                    .eqIfPresent(MemMemberOpenAuth::getAppid, reqVO.getAppid())
                .orderByDesc(MemMemberOpenAuth::getB2bAuthOpenId));
    }

    default MemMemberOpenAuth selectByAppidAndOpenidAndBranchId(String appid, String openid, Long branchId) {
        return selectOne(new LambdaQueryWrapperX<MemMemberOpenAuth>()
                .eq(MemMemberOpenAuth::getOpenid, openid)
                .eq(MemMemberOpenAuth::getBranchId, branchId)
                .eq(MemMemberOpenAuth::getAppid, appid));
    }

}
