package com.zksr.member.mapper;


import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.api.complain.MemComplainVO;
import com.zksr.member.domain.MemComplain;


/**
 * <p>
 * 投诉信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-06
 */
public interface MemComplainMapper extends BaseMapperX<MemComplain> {

    default PageResult<MemComplain> selectPage(MemComplainVO complainVO) {
        LambdaQueryWrapperX<MemComplain> wrapperX = new LambdaQueryWrapperX<>();
        if (complainVO.getPhone()!=null && complainVO.getPhone()!=""){
            wrapperX.likeIfPresent(MemComplain::getPhone, complainVO.getPhone());
        }
        if (complainVO.getComplainId()!=null && complainVO.getComplainId()!=""){
            wrapperX.likeIfPresent(MemComplain::getComplainId, complainVO.getComplainId());
        }
        if (complainVO.getStatus()!=null && complainVO.getStatus()!=""){
            wrapperX.likeIfPresent(MemComplain::getStatus, complainVO.getStatus());
        }
        wrapperX
                .eqIfPresent(MemComplain::getComplainType, complainVO.getComplainType())
                .gtIfPresent(MemComplain::getCreateTime, complainVO.getCreateBinTime())
                .ltIfPresent(MemComplain::getCreateTime, complainVO.getCreateEndTime())
                .gtIfPresent(MemComplain::getProcessTime, complainVO.getProcessBinTime())
                .ltIfPresent(MemComplain::getProcessTime, complainVO.getProcessEndTime());
        if (complainVO.getTargetUsername() != null &&complainVO.getTargetUsername() !=""){
            wrapperX.like(MemComplain::getTargetUsername, complainVO.getTargetUsername())
                    .or()
                    .like(MemComplain::getTargetPhone, complainVO.getTargetUsername());
        }
        wrapperX.orderByDesc(MemComplain::getCreateTime);
        return selectPage(complainVO,wrapperX);
    }

}
