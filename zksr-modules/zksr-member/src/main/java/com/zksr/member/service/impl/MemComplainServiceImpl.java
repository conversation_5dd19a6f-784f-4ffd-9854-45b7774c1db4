package com.zksr.member.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zksr.common.core.exception.CheckedException;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.complain.MemComplainVO;
import com.zksr.member.api.complain.dto.MemComplainDTO;
import com.zksr.member.domain.MemComplain;
import com.zksr.member.mapper.MemComplainMapper;
import com.zksr.member.service.IMemComplainService;
import com.zksr.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 投诉信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-06
 */
@Service
@Slf4j
public class MemComplainServiceImpl extends ServiceImpl<MemComplainMapper, MemComplain> implements IMemComplainService {

    @Autowired
    private MemComplainMapper memoComplainMapper;
    /**
     * 查询用户的投诉列表
     *
     * @param memComplainVO
     * @return
     */
    @Override
    public PageResult<MemComplainVO> getComplainList(MemComplainVO memComplainVO) {
        LambdaQueryWrapper<MemComplain> queryWrapper = new LambdaQueryWrapper<MemComplain>();
        queryWrapper.orderByDesc(MemComplain::getCreateTime);
        queryWrapper.eq(MemComplain::getMemberId, memComplainVO.getMemberId());
        if (memComplainVO.getStatus() != null && memComplainVO.getStatus() !="") {
            queryWrapper.eq(MemComplain::getStatus, memComplainVO.getStatus());
        }else{
            queryWrapper.in(MemComplain::getStatus, StringPool.ZERO,StringPool.ONE);
        }
        List<MemComplain> memComplainList = memoComplainMapper.selectList(queryWrapper);
        List<MemComplainVO> memComplainVOS=new ArrayList<>(memComplainList.size());
        for (MemComplain memComplain : memComplainList) {
            MemComplainVO complainVO = new MemComplainVO();
            // 设置VO对象的属性，这里假设MemComplainVO有与MemComplain相同的属性
            BeanUtils.copyProperties(memComplain, complainVO);
            complainVO.setComplainId(memComplain.getComplainId().toString());
            complainVO.setBranchId(memComplain.getBranchId().toString());
            complainVO.setMemberId(memComplain.getMemberId().toString());
            memComplainVOS.add(complainVO);
        }
        memComplainVOS.stream().forEach(
                m -> {
                    String maskedPhone = m.getPhone().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
                    m.setPhone(maskedPhone);
                });
        PageResult<MemComplainVO> pageResult = new PageResult<>();
        pageResult.setList(memComplainVOS);
        return pageResult;
    }

    /**
     * 新增投诉信息
     *
     * @param memComplainDTO
     * @return
     */
    @Override
    public CommonResult<String> insertComplain(MemComplainDTO memComplainDTO) {
        MemComplain memComplain = HutoolBeanUtils.toBean(memComplainDTO, MemComplain.class);

        try {
            int insert = memoComplainMapper.insert(memComplain);
        } catch (Exception e) {
            log.error("新增投诉信息失败",e);
            if (memComplainDTO.getComplainContent().length() > 500 ){
                throw new CheckedException("新增投诉信息失败,内容过长");
            }else {
                throw new CheckedException("新增投诉信息失败");
            }

        }
        return CommonResult.success(String.valueOf(memComplain.getComplainId()));
    }

    /**
     * 查询投诉详情
     *
     * @param complainId
     * @return
     */
    @Override
    public MemComplainVO getComplainByComplainId(String complainId) {
        if (complainId==null){
            throw new CheckedException("投诉ID不能为空");
        }
        MemComplain memComplain = memoComplainMapper.selectById(complainId);
        if (memComplain!=null){
            MemComplainVO memComplainVO = HutoolBeanUtils.toBean(memComplain, MemComplainVO.class);
            return memComplainVO;
        }
        return null;
    }

    /**
     * 后台分页查询投诉列表
     *
     * @param memComplainVO
     * @return
     */
    @Override
    public PageResult<MemComplain> getComplainPageList(MemComplainVO memComplainVO) {
        PageResult<MemComplain> pageResult =  memoComplainMapper.selectPage(memComplainVO);
        return pageResult;
    }

    /**
     * 处理投诉信息
     *
     * @param memComplainVO
     */
    @Override
    public void updateComplain(MemComplainVO memComplainVO) {
        MemComplain memComplain = HutoolBeanUtils.toBean(memComplainVO, MemComplain.class);
        memComplain.setStatus(StringPool.ONE);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String username = loginUser.getSysUser().getNickName();
        memComplain.setProcessBy(username);
        memComplain.setUpdateBy(username);
        memComplain.setUpdateTime(new Date());
        memComplain.setProcessTime(LocalDateTime.now());
        memoComplainMapper.updateById(memComplain);
    }
}
