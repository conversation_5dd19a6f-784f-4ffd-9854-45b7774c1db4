package com.zksr.member.controller.branch.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 门店信息对象 mem_branch
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Data
@ApiModel("门店信息 - mem_branch Response VO")
public class MemBranchRespDTO{
    private static final long serialVersionUID = 1L;

    /** 门店id */
    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    /** 门店编号 */
    @ApiModelProperty(value = "门店编号")
    private String branchNo;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;


    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

}
