package com.zksr.member.mq;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonelApp.dto.PageDataReqDTO;
import com.zksr.member.service.IMemberCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.support.MessageBuilder;

import java.util.Objects;

import static com.zksr.member.constant.MemberConstant.ES_COLONEL_APP_BRANCH_TYPE_3;
import static com.zksr.member.constant.MemberConstant.ES_COLONEL_APP_BRANCH_TYPE_4;

/**
*
 * 业务员APP模块 生产者
* <AUTHOR>
* @date 2024/5/9 11:39
*/
@Configuration
@Slf4j
public class MemberAppMqProducer {

    @Autowired
    private IMemberCacheService memberCacheService;

    @Autowired
    private StreamBridge streamBridge;

    /**
     * 业务员APP统计客户信息
     * @param branchDTO
     */
    public void sendEsColonelAppBranchEvent(ColonelAppBranchDTO branchDTO){
        log.info("业务员APP统计客户信息类型:更新拜访时间,门店编号:{},平台商编号{}",branchDTO.getBranchId(),branchDTO.getSysCode());
        branchDTO.setType(ES_COLONEL_APP_BRANCH_TYPE_3);
        streamBridge.send(
                MessageConstant.COLONEL_APP_BRANCH_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(branchDTO).build());

    }

    /**
     * 业务员APP统计首页信息
     * @param reqDTO
     */
    public void sendColoenlAppPageDataEvent(PageDataReqDTO reqDTO){
        log.info("业务员APP首页信息发送消息,业务员编号:{},平台商编号{},类型{}",reqDTO.getColonelId(),reqDTO.getSysCode(),reqDTO.getType());
        streamBridge.send(
                MessageConstant.COLONEL_APP_PAGE_DATA_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(reqDTO)
                        // 延迟30秒
                        .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, NumberPool.INT_FOUR)
                        .build());
    }


    /**
     * 业务员APP统计客户信息
     * 消费者 {@link com.zksr.member.mq.MemberAppMqConsumer#colonelAppBranchEvent()}
     * @param branchId  门店ID
     */
    public void sendEsColonelAppBranchBaseEvent(Long branchId){
        BranchDTO branchDTO = memberCacheService.getBranchDto(branchId);
        if (Objects.isNull(branchDTO))
            return;
        ColonelAppBranchDTO appBranchDTO = new ColonelAppBranchDTO(branchId, branchDTO.getSysCode());
        log.info("基础信息, GEO信息,门店编号:{},平台商编号{}",appBranchDTO.getBranchId(), appBranchDTO.getSysCode());
        appBranchDTO.setType(ES_COLONEL_APP_BRANCH_TYPE_4);
        streamBridge.send(
                MessageConstant.COLONEL_APP_BRANCH_TOPIC_OUT_PUT,
                org.springframework.integration.support.MessageBuilder
                        .withPayload(appBranchDTO)
                        .build());

    }
}
