package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.controller.colonelApp.dto.ColonelAppTargetRespDTO;
import com.zksr.member.controller.colonelApp.vo.ColonelAppTargetPageVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelActualSaleRespVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelTargetRespVO;
import com.zksr.member.domain.MemColonelTarget;
import com.zksr.member.controller.colonelTarget.vo.MemColonelTargetPageReqVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelTargetSaveReqVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 业务员目标设置Service接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface IMemColonelTargetService {

    /**
     * 新增业务员目标设置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemColonelTarget(@Valid MemColonelTargetSaveReqVO createReqVO);

    /**
     * 批量新增业务员目标设置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public void insertBatchMemColonelTarget(@Valid List<MemColonelTargetSaveReqVO> createReqVO);

    /**
     * 修改业务员目标设置
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemColonelTarget(@Valid MemColonelTargetSaveReqVO updateReqVO);

    /**
     * 删除业务员目标设置
     *
     * @param colonelTargetId 主键ID
     */
    public void deleteMemColonelTarget(Long colonelTargetId);

    /**
     * 批量删除业务员目标设置
     *
     * @param colonelTargetIds 需要删除的业务员目标设置主键集合
     * @return 结果
     */
    public void deleteMemColonelTargetByColonelTargetIds(Long[] colonelTargetIds);

    /**
     * 获得业务员目标设置
     *
     * @param colonelTargetId 主键ID
     * @return 业务员目标设置
     */
    public MemColonelTarget getMemColonelTarget(Long colonelTargetId);

    /**
     * 获得业务员目标设置分页
     *
     * @param pageReqVO 分页查询
     * @return 业务员目标设置分页
     */
    PageResult<MemColonelTargetRespVO> getMemColonelTargetPage(MemColonelTargetPageReqVO pageReqVO);


    /**
     * 停用
     * @param colonelTargetId
     */
    public void disable(Long colonelTargetId);

    /**
     * 启用
     * @param colonelTargetId
     */
    public void enable(Long colonelTargetId);

    /**
     * 获取业务员近两月的实际销售数据
     * @param pageReqVO
     */
    public List<MemColonelActualSaleRespVO> getColonelActualSalesInfo(MemColonelTargetPageReqVO pageReqVO);

    /**
     * 批量设置完成业务员目标
     * @param colonelTargetIds
     */
    public void settingCompleted(Long[] colonelTargetIds);

    /**
     * 分页查询业务员目标报表
     * @param pageVO
     * @return
     */
    ColonelAppTargetRespDTO getColonelTargetReportPage(ColonelAppTargetPageVO pageVO);

    /**
     *获取业务员的目标月份销售额
     * @param colonelId
     * @return
     */
    BigDecimal getColonelMonthlySalesTarget(Long colonelId);
}
