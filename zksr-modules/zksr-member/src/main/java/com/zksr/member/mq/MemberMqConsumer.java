package com.zksr.member.mq;
import com.zksr.common.core.domain.vo.openapi.SyncBranchSendDTO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.elasticsearch.domain.EsColonelAppBranch;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.common.elasticsearch.service.EsColonelAppBranchService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.colonelApp.dto.PageDataDTO;
import com.zksr.member.api.colonelApp.dto.PageDataReqDTO;
import com.zksr.member.api.command.vo.CommandAddOrderVO;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.mapper.MemBranchMapper;
import com.zksr.member.service.IMemBranchService;
import com.zksr.member.service.IMemCommandService;
import com.zksr.system.api.amap.AmapApi;
import com.zksr.system.api.amap.dto.LongitudeAndLatitudeResult;
import com.zksr.system.api.amap.vo.LongitudeAndLatitudeParam;
import com.zksr.trade.api.order.dto.TrdSettleDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.CacheConstants.COLONEL_APP_PAGE_DATA;
import static com.zksr.member.constant.MemberConstant.*;

/**
*
 * 用户模块 消费者
* <AUTHOR>
* @date 2024/5/9 11:39
*/
@Configuration
@Slf4j
public class MemberMqConsumer {

    @Autowired
    private EsColonelAppBranchService colonelAppBranchService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IMemBranchService memBranchService;

    @Autowired
    private AmapApi amapApi;

    @Autowired
    private MemBranchMapper memBranchMapper;

    @Autowired
    private MemberMqProducer memberMqProducer;
    @Autowired
    private IMemCommandService memCommandService;


    @Bean
    public Consumer<List<Long>> branchGetLongitudeAndLatitudeEvent() {
        return (data) -> {
            log.info("收到 门店获取经纬度信息事件 : 门店编号:{},",data);
            if(ToolUtil.isEmpty(data)){
                log.error("收到 门店获取经纬度信息事件数据为空：{}",data);
            }

            data.forEach(branchId->{
               MemBranch branch =  memBranchService.getMemBranch(branchId);
               if (ToolUtil.isEmpty(branch)) {
                   log.error("收到门店获取经纬度信息事件数据单个异常，门店ID：{}",branchId);
               }

               try {
                  LongitudeAndLatitudeResult result = amapApi.getBranchLongitudeAndLatitude(LongitudeAndLatitudeParam.builder().address(branch.getBranchAddr()).build()).getCheckedData();
                  if (result.getStatus() == NumberPool.INT_ONE) {
                      String location = result.getGeocodes().get(0).getLocation();
                      //经度
                      branch.setLongitude(new BigDecimal(location.split(",")[0]));
                      // 纬度
                      branch.setLatitude(new BigDecimal(location.split(",")[1]));
                      // 更新数据
                      memBranchMapper.updateBranch(branch);
                      // 清除缓存
                      memBranchService.reloadBranchDTOCache(branch.getBranchId());

                      //memBranchService.sendErp(HutoolBeanUtils.toBean(branch, MemBranchSaveReqVO.class), OperationType.ADD);
                  } else {
                      log.error("门店{}获取经纬度信息失败：{}", branchId, result.getInfo());
                  }
               } catch (Exception e) {

                   log.error("门店{}获取经纬度信息异常：{}", branchId, e.getMessage(), e);
               }
                //推送门店信息给ERP
                //推送 同步门店信息
                memBranchService.syncBranchData(branch.getBranchId(),OperationType.ADD.getCode());
            });
        };
    }


    /**
     * 加单指令消息 消费者
     * {@link com.zksr.trade.mq.TradeMqProducer#sendAddOrderCommand}
     * @return
     */
    @Bean
    public Consumer<CommandAddOrderVO> commandAddOrderEvent() {
        return (data) -> {
            log.info("收到 加单指令消息 : 加单时间:{},门店ID:{},类型:{}",data.getCreateTime(),data.getBranchId(),data.getType());
            memCommandService.addOrderCommandHandle(data);
        };
    }

}
