package com.zksr.member.controller.searchHis.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 搜索历史对象 mem_search_his
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@ApiModel("搜索历史 - mem_search_his分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemSearchHisPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 搜索历史id */
    @ApiModelProperty(value = "搜索词")
    private Long searchHisId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 门店用户id */
    @Excel(name = "门店用户id")
    @ApiModelProperty(value = "门店用户id")
    private Long memberId;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 搜索词 */
    @Excel(name = "搜索词")
    @ApiModelProperty(value = "搜索词")
    private String words;

    /** 业务员名称 */
    @Excel(name = "业务员名称")
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 城市区域ID */
    @Excel(name = "城市区域ID")
    @ApiModelProperty(value = "城市区域ID")
    private Long areaId;

    /** 门店用户名称 */
    @Excel(name = "门店用户名称")
    @ApiModelProperty(value = "门店用户名称")
    private String memberName;

    /** 搜索时间类型  1：今天  2：本周  3：本月*/
    @Excel(name = "搜索时间类型  1：今天  2：本周  3：本月")
    @ApiModelProperty(value = "搜索时间类型  1：今天  2：本周  3：本月")
    private Integer searchTimeType;
}
