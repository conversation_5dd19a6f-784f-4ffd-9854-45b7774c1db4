package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 门店信息对象 mem_branch
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@TableName(value = "mem_branch")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MemBranch extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 门店id */
    @TableId(type = IdType.ASSIGN_ID)
    @Excel(name = "门店id")
    private Long branchId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 门店名称 */
    @Excel(name = "门店名称")
    private String branchName;


    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 业务员id */
    @Excel(name = "业务员id")
    private Long colonelId;

    /** 门店地址 */
    @Excel(name = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 渠道id */
    @Excel(name = "渠道id")
    private Long channelId;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    private Long groupId;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    /** 删除状态(0:正常，2：删除) */
    private Integer delFlag;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditTime;

    /** 审核状态 */
    @Excel(name = "审核状态")
    @ApiModelProperty(value = "1已审核 0未审核")
    private Integer auditState = 0;

    /** 过期时间 */
    @Excel(name = "过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate; 		 // 过期时间

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @Excel(name = "价格码-数据字典", readConverterExp = "1=，2，3，4，5，6")
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;

    @Excel(name = "是否支持货到付款(0,否 1,是)")
    @ApiModelProperty(value = "是否支持货到付款(0,否 1,是)")
    private Integer hdfkSupport;
    /** 门头照 */
    @Excel(name = "门头照")
    private String branchImages;

    /** 最后一次登陆时间 */
    @Excel(name = "最后一次登陆时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    @Excel(name = "货到付款最大可欠款金额")
    @ApiModelProperty("货到付款最大可欠款金额")
    private BigDecimal hdfkMaxAmt;

    /** 门店编号 */
    @Excel(name = "门店编号")
    private String branchNo;

    @ApiModelProperty("三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    private Long threeAreaCityId;

    @ApiModelProperty("微信商户认证openid")
    private String wechatMerchantAuthOpenid;

    @ApiModelProperty("是否首单, 新客标识, 首单标识 1-是 0-否")
    private Integer firstOrderFlag;

    @Excel(name = "进入公海时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date seasTime;

    /** 省份 */
    @Excel(name = "省份")
    @ApiModelProperty(value = "省份")
    private String provinceName;

    /** 城市 */
    @Excel(name = "城市")
    @ApiModelProperty(value = "城市")
    private String cityName;

    /** 区县 */
    @Excel(name = "区县")
    @ApiModelProperty(value = "区县")
    private String districtName;

    /** 首单单号 */
    @ApiModelProperty("首单单号")
    private String firstOrderNo;

    @TableField(exist = false)
    @ApiModelProperty("大单位上架状态,0-未上架,1-已上架")
    private Long branchRegisterId ;

    /** 生命周期阶段 */
    @Excel(name = "生命周期阶段")
    private Integer lifecycleStage;

    @ApiModelProperty("是否支持在线收款 0：否，1：是")
    private Integer isPayOnline;

    /**
     * 分销渠道（如美云销等)
     */
    @ApiModelProperty(value = "分销渠道（如美云销等)")
    private String distributionChannel;

    /**
     * 分销模式（如O2O等)
     */
    @ApiModelProperty(value = "分销模式（如O2O等)")
    private String distributionMode;

    /**
     * 分润模式（如按商品指定金额等)
     */
    @ApiModelProperty(value = "分润模式（如按商品指定金额等)")
    private String profitMode;

    /**
     * 分润比例
     */
    @ApiModelProperty(value = "分润比例 百分比的小数表现形式，1%表示为0.01")
    @Excel(name = "分润比例")
    private BigDecimal profitRate;

    /**
     * 商户号
     */
    @ApiModelProperty(value = "商户号")
    private String merchantNo;

    /**
     * 商户号进件单号
     */
    @ApiModelProperty(value = "商户号进件单号")
    private String merchantApplyNo;

    /**
     * 商户号全称
     */
    @ApiModelProperty(value = "商户号全称")
    private String merchantFullName;

    /** 省份编码 */
    @ApiModelProperty("省份编码")
    private String provinceCode;

    /** 城市编码 */
    @ApiModelProperty("城市编码")
    private String cityCode;

    /** 区县编码 */
    @ApiModelProperty("区县编码")
    private String districtCode;

    /** 街道编码 */
    @ApiModelProperty("街道编码")
    private String streetCode;
    /** 纳税人识别码 */
    @ApiModelProperty(value = "纳税人识别码")
    private String taxpayerCode;

    /** 城市区域名称 */
    @ApiModelProperty("城市区域名称")
    private String cityRegionName;

    @ApiModelProperty("外部商户编码")
    private String outerMerchantCode;
}
