package com.zksr.member.convert.loginHis;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.controller.loginHis.vo.MemLoginHisRespVO;
import com.zksr.member.controller.loginHis.vo.MemLoginHisSaveReqVO;
import com.zksr.member.domain.MemLoginHis;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 登录历史 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-12-19
*/
@Mapper
public interface MemLoginHisConvert {

    MemLoginHisConvert INSTANCE = Mappers.getMapper(MemLoginHisConvert.class);

    MemLoginHisRespVO convert(MemLoginHis memLoginHis);

    MemLoginHis convert(MemLoginHisSaveReqVO memLoginHisSaveReq);

    PageResult<MemLoginHisRespVO> convertPage(PageResult<MemLoginHis> memLoginHisPage);
}