package com.zksr.member.controller.colonelApp.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.elasticsearch.domain.EsColonelAppBranch;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.rely.FieldType;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/4/24 10:43
 * 业务员App - 客户列表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("业务员App - 客户列表")
public class MemColonelAppCustomerListRespVO {
    private static final long serialVersionUID = 1L;

    /** 门店id */
    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 业务员id */
    @Excel(name = "业务员id")
    @ApiModelProperty(value = "业务员id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelId;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 距离 */
    @Excel(name = "距离(米)")
    @ApiModelProperty(value = "距离(米)")
    private Double distance;

    /** 门店门头照 */
    @Excel(name = "门店门头照")
    @ApiModelProperty(value = "门店门头照 ")
    private String  branchImages;

    /**  -------订单信息------*/

    /** 本月订单笔数 */
    @ApiModelProperty("本月订单笔数")
    private Long nowMonthOrderQty;

    /** 本月订单金额 */
    @ApiModelProperty("本月订单金额")
    private BigDecimal nowMonthOrderAmt;

    /** 上月订单笔数 */
    @ApiModelProperty("上月订单笔数")
    private Long lastMonthOrderQty;

    /** 上月订单金额 */
    @ApiModelProperty("上月订单金额")
    private BigDecimal lastMonthOrderAmt;

    /** 用户最近一次订货时间 */
    @ApiModelProperty(value = "最近一次订货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastBuyTime;

    @ApiModelProperty("最近一次订货金额")
    private BigDecimal lastBuyAmt;

    /**  -------客户登陆------*/

    /** 用户上次访问系统时间 */
    @ApiModelProperty(value = "上次访问系统时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastAccessSystemTime;

    /** 用户最近登陆时间 */
    @ApiModelProperty(value = "最近登陆时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    /** ------业务员签到------*/

    /** 业务员最近一次拜访时间 */
    @ApiModelProperty(value = "最近一次拜访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastVisitTime;

    /** 业务员已拜访次数 */
    @ApiModelProperty("业务员已拜访次数")
    private Long visitNum;

    /** ------购物车信息------*/
    @ApiModelProperty("最近加购时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastCarAddTime;

    @ApiModelProperty("进入公海时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date seasTime;

    @ApiModelProperty(value = "上次业务员")
    private String lastColonel;

    /** 生命周期阶段 */
    @Excel(name = "生命周期阶段")
    @ApiModelProperty("生命周期阶段")
    private Integer lifecycleStage;

    public static MemColonelAppCustomerListRespVO getAppCustomer(MemColonelAppCustomerListRespVO respVO,EsColonelAppBranch esVO){
        respVO.setNowMonthOrderAmt(esVO.getNowMonthOrderAmt())
                .setNowMonthOrderQty(esVO.getNowMonthOrderQty())
                .setLastMonthOrderAmt(esVO.getLastMonthOrderAmt())
                .setLastMonthOrderQty(esVO.getLastMonthOrderQty())
                .setLastBuyTime(esVO.getLastBuyTime())
                .setLastAccessSystemTime(esVO.getLastAccessSystemTime())
                .setLastLoginTime(esVO.getLastLoginTime())
                .setLastVisitTime(esVO.getLastVisitTime())
                .setLastBuyAmt(esVO.getLastBuyAmt());
        return respVO;
    }

    @ApiModelProperty("公海查询 0 查询所有 1查询普通 2查询公海")
    private Integer isOpenSeas;

    @ApiModelProperty("是否首单, 新客标识, 首单标识 1-是 0-否")
    private Integer firstOrderFlag;
}
