package com.zksr.member.controller.branchLifeCycleZip.vo;

import com.alibaba.fastjson2.JSONObject;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 门店生命周期拉链对象 mem_branch_lifecycle_zip
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Data
@ApiModel("门店生命周期拉链 - mem_branch_lifecycle_zip Response VO")
public class MemBranchLifecycleZipRespVO {

    /** 门店生命周期拉链表ID */
    @ApiModelProperty(value = "门店生命周期拉链表ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchLifecycleZipId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    /** 上一次生命周期code */
    @Excel(name = "上一次生命周期code")
    @ApiModelProperty(value = "上一次生命周期code")
    private Integer lastLifecycleStage;

    /** 生命周期code */
    @Excel(name = "生命周期code")
    @ApiModelProperty(value = "生命周期code")
    private Integer lifecycleStage;

    /** 开始日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "开始日期", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    /** 结束日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "结束日期", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    /** 触发事件备注 */
    @Excel(name = "触发事件备注")
    @ApiModelProperty(value = "触发事件备注")
    private String startMemo;

    @Excel(name = "门店编号")
    @ApiModelProperty("门店编号")
    private String branchNo;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 渠道id */
    @Excel(name = "渠道id")
    @ApiModelProperty(value = "渠道id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long channelId;

    /** 渠道名称 */
    @Excel(name = "渠道名称")
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 城市名称 */
    @Excel(name = "城市名称")
    @ApiModelProperty(value = "城市名称")
    private String areaName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "触发事件备注项")
    private StartMemoData startMemoData;

    @ApiModel(value = "触发事件备注项")
    @Data
    public static class StartMemoData {
        @ApiModelProperty(value = "业务员名称")
        private String colonelName;

        @ApiModelProperty(value = "下单金额")
        private BigDecimal amt;

        @ApiModelProperty(value = "触发事件")
        private String event;

        @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
        @ApiModelProperty(value = "触发下单时间")
        private Date orderDate;

        @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
        @ApiModelProperty(value = "触发时间")
        private Date eventDate;

        @ApiModelProperty(value = "订单编号")
        private String orderNo;

        @ApiModelProperty(value = "是否是订单取消")
        private Boolean isOrderCancel = false;

    }

    public void parseStartMemoData(){
        if (startMemo != null && !startMemo.trim().isEmpty()) {
            startMemoData = JSONObject.parseObject(startMemo, StartMemoData.class);
        }
    }
}
