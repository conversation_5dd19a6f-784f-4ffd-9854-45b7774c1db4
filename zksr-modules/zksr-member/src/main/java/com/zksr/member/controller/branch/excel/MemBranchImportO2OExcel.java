package com.zksr.member.controller.branch.excel;

import java.math.BigDecimal;

import org.apache.poi.ss.usermodel.IndexedColors;

import com.zksr.common.core.annotation.Excel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "用于门店O2O信息导入")
public class MemBranchImportO2OExcel extends MemBranchImportExcel{

    /** 分销渠道（如美云销等) */
    @Excel(name = "分销渠道", headerColor = IndexedColors.RED,combo = {"美的零售"})
    private String distributionChannel;

    /** 分销模式（如O2O等) */
    @Excel(name = "分销模式",combo = {"O2O"})
    private String distributionMode;

    /** 分润模式（如按商品指定金额等) */
    @Excel(name = "分润模式", combo = {"全部","按比例","按平台规则"})
    private String profitMode;

    /** 分润比例 */
    @Excel(name = "分润比例")
    private BigDecimal profitRate;

    /** 商户号 */
    @Excel(name = "商户号")
    private String merchantNo;

    /** 商户号进件单号 */
    @Excel(name = "商户号进件单号")
    private String merchantApplyNo;

    /** 商户号全称 */
    @Excel(name = "商户号全称")
    private String merchantFullName;
    
    /** 纳税人识别码 */
    @Excel(name = "纳税人识别码")
    private String taxpayerCode;

    /** 美云销商户编码 */
    @Excel(name = "美云销商户编码")
    private String outerMerchantCode;
}
