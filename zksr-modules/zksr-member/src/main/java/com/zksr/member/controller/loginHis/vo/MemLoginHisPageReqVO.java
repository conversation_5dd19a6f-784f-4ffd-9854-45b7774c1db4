package com.zksr.member.controller.loginHis.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 登录历史对象 mem_login_his
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@ApiModel("登录历史 - mem_login_his分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemLoginHisPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 登录历史id */
    @ApiModelProperty(value = "spu_id")
    private Long loginHisId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 日期;yyyyMMdd */
    @Excel(name = "日期;yyyyMMdd")
    @ApiModelProperty(value = "日期;yyyyMMdd")
    private Long dateId;

    /** 微信openid;后台登录信息获取 */
    @Excel(name = "微信openid;后台登录信息获取")
    @ApiModelProperty(value = "微信openid;后台登录信息获取")
    private String wxOpenid;

    /** 用户手机号;后台登录信息获取 */
    @Excel(name = "用户手机号;后台登录信息获取")
    @ApiModelProperty(value = "用户手机号;后台登录信息获取")
    private String memberPhone;

    /** 用户名;后台登录信息获取 */
    @Excel(name = "用户名;后台登录信息获取")
    @ApiModelProperty(value = "用户名;后台登录信息获取")
    private String memberUsername;

    /** 用户id;后台登录信息获取 */
    @Excel(name = "用户id;后台登录信息获取")
    @ApiModelProperty(value = "用户id;后台登录信息获取")
    private Long memberId;

    /** 门店id;后台登录信息获取 */
    @Excel(name = "门店id;后台登录信息获取")
    @ApiModelProperty(value = "门店id;后台登录信息获取")
    private Long branchId;

    /** ip地址;http_request */
    @Excel(name = "ip地址;http_request")
    @ApiModelProperty(value = "ip地址;http_request")
    private String ip;

    /** ip地址归属地;http_request */
    @Excel(name = "ip地址归属地;http_request")
    @ApiModelProperty(value = "ip地址归属地;http_request")
    private String district;

    /** 类型（数据字典）;0-登陆  1-访问 */
    @Excel(name = "类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "类型")
    private String tp;

    /** 设备id;前端传（HttpHeader） */
    @Excel(name = "设备id;前端传", readConverterExp = "H=ttpHeader")
    @ApiModelProperty(value = "设备id;前端传")
    private String deviceId;

    /** 设备类型;前端传（HttpHeader） */
    @Excel(name = "设备类型;前端传", readConverterExp = "H=ttpHeader")
    @ApiModelProperty(value = "设备类型;前端传")
    private String deviceType;

    /** 设备品牌;前端传（HttpHeader） */
    @Excel(name = "设备品牌;前端传", readConverterExp = "H=ttpHeader")
    @ApiModelProperty(value = "设备品牌;前端传")
    private String deviceBrand;

    /** 设备型号;前端传（HttpHeader） */
    @Excel(name = "设备型号;前端传", readConverterExp = "H=ttpHeader")
    @ApiModelProperty(value = "设备型号;前端传")
    private String deviceModel;

    /** 系统名称;前端传（HttpHeader） */
    @Excel(name = "系统名称;前端传", readConverterExp = "H=ttpHeader")
    @ApiModelProperty(value = "系统名称;前端传")
    private String osName;

    /** 操作系统版本;前端传（HttpHeader） */
    @Excel(name = "操作系统版本;前端传", readConverterExp = "H=ttpHeader")
    @ApiModelProperty(value = "操作系统版本;前端传")
    private String osVersion;

    /** pc app xcx */
    @Excel(name = "pc app xcx")
    @ApiModelProperty(value = "pc app xcx")
    private String port;

    /** spu_id */
    @Excel(name = "spu_id")
    @ApiModelProperty(value = "spu_id")
    private Long spuId;

    /** 创建日期开始时间 */
    @Excel(name = "创建日期开始时间")
    @ApiModelProperty(value = "创建日期开始时间")
    private String createTimeBegin;

    /** 创建日期结束时间 */
    @Excel(name = "创建日期结束时间")
    @ApiModelProperty(value = "创建日期结束时间")
    private String createTimeEnd;


}
