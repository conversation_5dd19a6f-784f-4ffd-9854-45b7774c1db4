package com.zksr.member.service.impl;

import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.memberRegister.dto.MemberRegisterDTO;
import com.zksr.member.controller.memberRegister.vo.*;
import com.zksr.member.service.IMemMemberService;
import com.zksr.member.service.IMemberCacheService;
import com.zksr.system.api.RemoteUserService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemMemberRegisterMapper;
import com.zksr.member.convert.memberRegister.MemMemberRegisterConvert;
import com.zksr.member.domain.MemMemberRegister;
import com.zksr.member.service.IMemMemberRegisterService;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.constant.StatusConstants.REGISTER_APPROVE_FLAG_1;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 用户注册信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@Service
@Slf4j
public class MemMemberRegisterServiceImpl implements IMemMemberRegisterService {
    @Autowired
    private MemMemberRegisterMapper memMemberRegisterMapper;

    @Autowired
    private IMemMemberService memMemberService;

    @Autowired
    private AreaApi remoteAreaApi;

    @Autowired
    private RemoteUserService userService;

    @Autowired
    private IMemberCacheService memberCacheService;

    /**
     * 新增用户注册信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(prefix = RedisLockConstants.LOCK_MEMBER_REGISTER, condition = "#createReqVO.userName", tryLock = true)
    public Long insertMemMemberRegister(MemMemberRegisterSaveReqVO createReqVO) {
        if(StringUtils.isEmpty(createReqVO.getUserName())){
            throw new ServiceException("用户账号不能为空");
        }
        Long sysCode = MallSecurityUtils.getLoginMember() == null ? createReqVO.getSysCode() : MallSecurityUtils.getLoginMember().getSysCode();
        //先查询是否存在用户
        MemMemberRegisterRespVO respVO = new MemMemberRegisterRespVO();
        respVO.setUserName(createReqVO.getUserName());
        respVO.setSysCode(sysCode);
        log.info(" 用户注册,{},{}", JsonUtils.toJsonString(respVO),JsonUtils.toJsonString(createReqVO));

        MemMemberRegister memMember = memMemberRegisterMapper.getMemMemberRegister(respVO);
        if(null != memMember){
            throw new ServiceException(String.format("用户已存在[%s]",createReqVO.getUserName()));
        }
        // 插入
        MemMemberRegister memMemberRegister = MemMemberRegisterConvert.INSTANCE.convert(createReqVO);
        memMemberRegisterMapper.insert(memMemberRegister);
        // 返回
        return memMemberRegister.getMemberRegisterId();
    }

    /**
     * 修改用户注册信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMemMemberRegister(MemMemberRegisterSaveReqVO updateReqVO) {
        memMemberRegisterMapper.updateById(MemMemberRegisterConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除用户注册信息
     *
     * @param memberRegisterId 用户注册信息ID
     */
    @Override
    public void deleteMemMemberRegister(Long memberRegisterId) {
        // 删除
        memMemberRegisterMapper.deleteById(memberRegisterId);
    }

    /**
     * 批量删除用户注册信息
     *
     * @param memberRegisterIds 需要删除的用户注册信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMemMemberRegisterByMemberRegisterIds(Long[] memberRegisterIds) {
        memMemberRegisterMapper.deleteMemMemberRegisterByMemberRegisterIds(memberRegisterIds);
    }

    /**
     * 获得用户注册信息
     *
     * @param memberRegisterId 用户注册信息ID
     * @return 用户注册信息
     */
    @Override
    public MemMemberRegister getMemMemberRegister(Long memberRegisterId) {
        return memMemberRegisterMapper.selectById(memberRegisterId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemMemberRegisterPageRespVO> getMemMemberRegisterPage(MemMemberRegisterPageReqVO pageReqVO) {

        PageResult<MemMemberRegister> result = memMemberRegisterMapper.selectPage(pageReqVO);

        PageResult<MemMemberRegisterPageRespVO> pageResult = new PageResult<>(HutoolBeanUtils.toBean(result.getList(), MemMemberRegisterPageRespVO.class),result.getTotal());
        if(ToolUtil.isNotEmpty(pageResult.getList())){
            //匹配分页数据中的ID对应信息
            pageResult.getList().forEach(memberRegister -> {
                if(null != memberRegister.getAreaId()){
                    CommonResult<AreaDTO> areaDTOResult = remoteAreaApi.getAreaByAreaId(memberRegister.getAreaId());
                    if(areaDTOResult.isSuccess()){
                        AreaDTO areaDTO = areaDTOResult.getCheckedData();
                        if(ToolUtil.isNotEmpty(areaDTO)){
                            memberRegister.setAreaName(areaDTO.getAreaName());
                        }

                    }
                }

                // 从系统管理员查询用户信息
                if (Objects.nonNull(memberRegister.getApproveMan())) {
                    SysUser sysUser = userService.getSysUser(memberRegister.getApproveMan()).getCheckedData();
                    if (Objects.nonNull(sysUser)) {
                        memberRegister.setApproveManName(sysUser.getNickName());
                    }
                }

                if(null != memberRegister.getChannelId()){
                    // 渠道
                    ChannelDTO channelDto = memberCacheService.getChannelDto(memberRegister.getChannelId());
                    if (Objects.nonNull(channelDto)) {
                        memberRegister.setChannelName(channelDto.getChannelName());
                    }

                }

                //业务员名称
                if(null != memberRegister.getColonelId()){
                    // 业务员
                    ColonelDTO colonelDTO = memberCacheService.getColonel(memberRegister.getColonelId());
                    if (Objects.nonNull(colonelDTO)) {
                        memberRegister.setColonelName(colonelDTO.getColonelName());
                    }

                }

            });
        }
        return pageResult;
    }

    @Override
    public MemMemberRegister getMemberRegisterById(Long memberRegisterId) {
        return memMemberRegisterMapper.selectById(memberRegisterId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(prefix = RedisLockConstants.LOCK_MEMBER_REGISTER, condition = "#saveReqVO.userName", tryLock = true)
    public Boolean insertUserMemberRegister(MemMemberRegisterSaveReqVO saveReqVO) {
        if(StringUtils.isEmpty(saveReqVO.getUserName())){
            throw new ServiceException("用户账号不能为空");
        }
        Long sysCode = MallSecurityUtils.getLoginMember() == null ? saveReqVO.getSysCode() : MallSecurityUtils.getLoginMember().getSysCode();
        //先查询是否存在用户
        MemMemberRegisterRespVO respVO = new MemMemberRegisterRespVO();
        respVO.setUserName(saveReqVO.getUserName());
        respVO.setSysCode(sysCode);
        log.info(" 用户注册,{},{}", JsonUtils.toJsonString(respVO),JsonUtils.toJsonString(saveReqVO));

        MemMemberRegister memMember = memMemberRegisterMapper.getMemMemberRegister(respVO);
        if(null != memMember){
            throw new ServiceException(String.format("用户已存在[%s]",saveReqVO.getUserName()));
        }

        MemMemberRegister memMemberRegister = HutoolBeanUtils.toBean(saveReqVO, MemMemberRegister.class);
        //新增注册信息数据
        memMemberRegisterMapper.insert(memMemberRegister);

        //校验用户是否自动审核  审核的话 则需要创建有时效的用户及门店信息
        if(saveReqVO.getMemberApproveFlag() == REGISTER_APPROVE_FLAG_1){
            memMemberService.insertMemberRegister(saveReqVO);
            memMemberRegisterMapper.memberRegisterApprove(Arrays.asList(memMemberRegister.getMemberRegisterId()), null, null);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAuditMemberRegister(MemMemberRegisterAuditVO auditVO) {


        List<Long> ids = Arrays.asList(auditVO.getMemberRegisterIds());
        //查询用户注册信息列表
        List<MemMemberRegister> memMemberRegisterList = memMemberRegisterMapper.selectBatchIds(ids);
        List<MemMemberRegisterSaveReqVO> saveVoList = HutoolBeanUtils.toBean(memMemberRegisterList,MemMemberRegisterSaveReqVO.class);
        //设置业务员ID
        saveVoList.forEach(saveVo -> {
            //业务员
            saveVo.setColonelId(auditVO.getColonelId());
            //渠道
            saveVo.setChannelId(auditVO.getChannelId());
            //货到付款标记
            saveVo.setHdfkSupport(auditVO.getHdfkSupport());
            //货到付款欠款金额
            saveVo.setHdfkMaxAmt(auditVO.getHdfkMaxAmt());
            //是否支持在线收款
            saveVo.setIsPayOnline(auditVO.getIsPayOnline());
            //价格码
            saveVo.setSalePriceCode(auditVO.getSalePriceCode());

        });

        //生成用户及门店信息
        saveVoList.forEach(saveReqVO -> {
            memMemberService.insertMemberRegister(saveReqVO);
        });

        Long userId = SecurityUtils.getUserId();
        //审核
        memMemberRegisterMapper.memberRegisterApprove(ids, userId, auditVO);

    }

    @Override
    public MemberRegisterDTO getMemberRegisterByUserName(Long sysCode, String userName) {
        return HutoolBeanUtils.toBean(memMemberRegisterMapper.getMemMemberRegister(new MemMemberRegisterRespVO(sysCode, userName)),MemberRegisterDTO.class);
    }

    private void validateMemMemberRegisterExists(Long memberRegisterId) {
        if (memMemberRegisterMapper.selectById(memberRegisterId) == null) {
            throw exception(MEM_MEMBER_REGISTER_NOT_EXISTS);
        }
    }

    @Override
    public MemMemberRegister getMemberRegisterByBranchId(Long branchId) {
        return memMemberRegisterMapper.getMemberRegisterByBranchId(branchId);
    }
}
