package com.zksr.member.controller.colonelApp.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/5/6 9:53
 * 业务员App-查看老板购物车RespVO
 */
@Data
@ApiModel("业务员App-查看老板购物车")
public class ColonelAppTrdCarRespVO {

    @Excel(name = "商品SPU id")
    @ApiModelProperty(value = "商品SPU id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    @Excel(name = "商品skuId")
    @ApiModelProperty(value = "商品skuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    @Excel(name = "spu名称")
    @ApiModelProperty("spu名称")
    private String spuName;

    @Excel(name = "数量")
    @ApiModelProperty(value = "数量")
    private Long qty;

    @Excel(name = "库存数量")
    @ApiModelProperty(value = "库存数量")
    private Long stock;

    @Excel(name = "销售价格")
    @ApiModelProperty(value = "销售价格", required = true, example = "99.99")
    private BigDecimal salePrice;

    @Excel(name = "总价")
    @ApiModelProperty(value = "总价", required = true, example = "99.99")
    private BigDecimal totalPrice;

    @Excel(name = "封面图")
    @ApiModelProperty("封面图")
    private String thumb;

    @Excel(name = "单位-数据字典（sys_prdt_unit）")
    @ApiModelProperty("单位-数据字典（sys_prdt_unit）")
    private String unit;

    @ApiModelProperty(value = "单位类型, 1-最小单位, 2-中单位, 3-大单位", notes = "7")
    private Integer unitSize;

}
