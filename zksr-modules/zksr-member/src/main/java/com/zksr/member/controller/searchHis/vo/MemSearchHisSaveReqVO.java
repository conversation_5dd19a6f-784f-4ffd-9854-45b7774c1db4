package com.zksr.member.controller.searchHis.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 搜索历史对象 mem_search_his
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel("搜索历史 - mem_search_his分页 Request VO")
public class MemSearchHisSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 搜索历史id */
    @ApiModelProperty(value = "搜索词")
    private Long searchHisId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 门店用户id */
    @Excel(name = "门店用户id")
    @ApiModelProperty(value = "门店用户id")
    private Long memberId;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 搜索词 */
    @Excel(name = "搜索词")
    @ApiModelProperty(value = "搜索词")
    private String words;

}
