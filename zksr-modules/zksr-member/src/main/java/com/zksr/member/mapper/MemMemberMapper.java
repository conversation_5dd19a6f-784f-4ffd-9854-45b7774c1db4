package com.zksr.member.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.member.api.member.vo.MemMemberRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.member.domain.MemMember;
import com.zksr.member.api.member.vo.MemMemberPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.constant.StatusConstants.STATE_ENABLE;


/**
 * 用户信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@Mapper
public interface MemMemberMapper extends BaseMapperX<MemMember> {
//    default PageResult<MemMember> selectPage(MemMemberPageReqVO reqVO) {
//        return selectPage(reqVO, new LambdaQueryWrapperX<MemMember>()
//                    .eqIfPresent(MemMember::getMemberId, reqVO.getMemberId())
//                    .eqIfPresent(MemMember::getSysCode, reqVO.getSysCode())
//                    .eqIfPresent(MemMember::getMemberPhone, reqVO.getMemberPhone())
//                    .likeIfPresent(MemMember::getMemberName, reqVO.getMemberName())
//                    .eqIfPresent(MemMember::getWxUnionid, reqVO.getWxUnionid())
//                    .eqIfPresent(MemMember::getAvatar, reqVO.getAvatar())
//                    .eqIfPresent(MemMember::getStatus, reqVO.getStatus())
//                    .eqIfPresent(MemMember::getXcxOpenid, reqVO.getXcxOpenid())
//                    .eqIfPresent(MemMember::getRegisterColonelId, reqVO.getRegisterColonelId())
//                    .eqIfPresent(MemMember::getLoginToken, reqVO.getLoginToken())
//                .orderByDesc(MemMember::getMemberId));
//    }
    default MemMember selectByMobileAndOpenid(Long sysCode, String mobile, String openid,String userName){
        //return selectOne(MemMember::getSysCode, sysCode, MemMember::getMemberPhone, mobile, MemMember::getXcxOpenid, openid);
        return selectOne(
                new LambdaQueryWrapperX<MemMember>()
                        .eqIfPresent(MemMember::getSysCode, sysCode)
                        .eqIfPresent(MemMember::getMemberPhone, mobile)
                        .eqIfPresent(MemMember::getXcxOpenid, openid)
                        .eqIfPresent(MemMember::getUserName,userName)
                        .last(StringPool.LIMIT_ONE)
        );
    }

    Page<MemMemberRespVO> selectPage(@Param("reqVO") MemMemberPageReqVO reqVO, @Param("page") Page<MemMemberPageReqVO> page);

    default List<MemMember> getAllExpirationDateMemberList(Long sysCode){
        return selectList(new LambdaQueryWrapper<MemMember>()
                .ge(MemMember::getCreateTime, DateUtils.getDateScopeByMonth(-6))
                .eq(MemMember::getStatus,STATE_ENABLE)
                .lt(MemMember::getExpirationDate,new Date())
                .eq(MemMember::getSysCode,sysCode));
    }

    void updateStatusByMemberIds(@Param("memberDTO")MemberDTO memberDTO);

    default List<MemMember> getMemberList(MemMemberRespVO respVO){
        return selectList(new LambdaQueryWrapperX<MemMember>()
                .eqIfPresent(MemMember::getStatus,respVO.getStatus())
                .eqIfPresent(MemMember::getMemberId,respVO.getMemberId())
                .eqIfPresent(MemMember::getPid,respVO.getPid()));
    }

    /**
     *  根据用户编号更新用户密码
     * @param member
     */
    default void updateMemberPwd(MemMember member){
        update(null, new LambdaUpdateWrapper<MemMember>()
                .set(MemMember::getPassword, member.getPassword())
                .eq(MemMember::getMemberPhone, member.getMemberPhone())
        );
    }

    /**
     *  根据用户编号更新用户信息
     * @param member
     */
    void updateMemberUserInfoByMemberId(MemMember member);

    /**
     * 根据用户手机号查询用户信息
     * @param memberPhone
     * @return
     */
    default MemMember getMemberByMemberPhone(String  memberPhone, Long sysCode){
        return selectOne(new LambdaQueryWrapperX<MemMember>()
                .eqIfPresent(MemMember::getMemberPhone, memberPhone)
                .eqIfPresent(MemMember::getSysCode, sysCode));
    }

    /**
     * 根据用户手机号查询用户信息
     * @param colonelId 业务员ID
     * @param sysCode 平台商ID
     * @return
     */
    default MemMember getMemberByColonelId(Long colonelId, Long sysCode){
        return selectOne(new LambdaQueryWrapperX<MemMember>()
                .eqIfPresent(MemMember::getRelateColonelId, colonelId)
                .eqIfPresent(MemMember::getSysCode, sysCode));
    }

    /**
     * 通过外部用户编码，获取用户信息
     */
    default MemMember getMemberByOuterUserCode(Long sysCode, String outerUserCode){
        return selectOne(new LambdaQueryWrapperX<MemMember>()
                .eqIfPresent(MemMember::getSysCode, sysCode)
                .eq(MemMember::getOuterUserCode, outerUserCode)
                .last(StringPool.LIMIT_ONE)
        );
    }
}
