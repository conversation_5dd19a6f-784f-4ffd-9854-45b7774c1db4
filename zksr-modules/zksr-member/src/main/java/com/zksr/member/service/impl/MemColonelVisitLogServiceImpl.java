package com.zksr.member.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LFUCache;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.file.FileApi;
import com.zksr.file.api.file.vo.ImageInfoReqVO;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.controller.branch.vo.MemBranchPageReqVO;
import com.zksr.member.controller.visitLog.vo.*;
import com.zksr.member.domain.MemColonelVisitLog;
import com.zksr.member.mapper.MemBranchMapper;
import com.zksr.member.mapper.MemColonelVisitLogMapper;
import com.zksr.member.service.IMemColonelVisitLogService;
import com.zksr.member.service.IMemberCacheService;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.TrdColonelAppOrderListPageReqVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.MEM_COLONEL_VISIT_LOG_NOT_EXISTS;

/**
 * 业务员拜访日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@Service
public class MemColonelVisitLogServiceImpl implements IMemColonelVisitLogService {
    @Autowired
    private MemColonelVisitLogMapper memColonelVisitLogMapper;

    @Autowired
    private MemBranchMapper memBranchMapper;

    @Autowired
    private IMemberCacheService memberCacheService;

    @Resource
    private FileApi fileApi;

    @Resource
    private OrderApi orderApi;



    /**
     * 新增业务员拜访日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemColonelVisitLog(MemColonelVisitLogSaveReqVO createReqVO) {
        // 插入
        MemColonelVisitLog memColonelVisitLog = HutoolBeanUtils.toBean(createReqVO, MemColonelVisitLog.class);
        memColonelVisitLogMapper.insert(memColonelVisitLog);
        // 返回
        return memColonelVisitLog.getColonelVisitLogId();
    }

    /**
     * 获取当天的拜访记录
     *
     * @param colonelId   业务员Id
     * @param branchId    门店
     * @param currentTime 当前时间
     * @return dto
     */
    public MemColonelVisitLog getTodayVisitLog(Long colonelId, Long branchId, String currentTime) {
        String signInDateSql = "DATE_FORMAT(sign_in_date,'%Y-%m-%d')='" + currentTime + "'";
        return memColonelVisitLogMapper.selectOne(Wrappers.<MemColonelVisitLog>query().lambda()
                .eq(MemColonelVisitLog::getColonelId, colonelId)
                .eq(MemColonelVisitLog::getBranchId, branchId)
                .ne(MemColonelVisitLog::getVisitFlag, "-1")
                .apply(signInDateSql)
                .last("limit 1"));
    }

    @Override
    public R<Long> signInInner(MemColonelVisitLogSaveReqVO createReqVO) {
        createReqVO.setBranchId(createReqVO.getConsumerNo());
        MemColonelVisitLog todayVisitLog = this.getTodayVisitLog(createReqVO.getColonelId(), createReqVO.getConsumerNo(), DateUtils.getDate());
        if (ObjectUtil.isNotNull(todayVisitLog)) {
            return R.fail("该门店已签到，签到失败");
        }
        createReqVO.setSignInDate(new Date());
        createReqVO.setVisitFlag(0);
        MemColonelVisitLog memColonelVisitLog = HutoolBeanUtils.toBean(createReqVO, MemColonelVisitLog.class);
        if (Objects.isNull(memColonelVisitLog)) {
            return R.fail("入参不能为空");
        }
        if (SecurityUtils.getLoginUser().getSysCode() == null) {
            return R.fail("不存在平台商id 不能签到");
        }
        memColonelVisitLog.setSysCode(SecurityUtils.getLoginUser().getSysCode());
        int num = memColonelVisitLogMapper.insert(memColonelVisitLog);
        if (num > 0) {
            return R.ok(memColonelVisitLog.getColonelVisitLogId());
        }
        return R.fail("签到失败");
    }

    @Override
    public R<Boolean> signOutInner(MemColonelVisitLogSaveReqVO createReqVO) {
        MemColonelVisitLog todayVisitLog = this.getTodayVisitLog(createReqVO.getColonelId(), createReqVO.getConsumerNo(), DateUtils.getDate());
        if (ObjectUtil.isNull(todayVisitLog)) {
            return R.fail("该客户未签到，签退失败");
        }
        if (ObjectUtil.notEqual(0, todayVisitLog.getVisitFlag())) {
            return R.fail("该客户签到状态已变更，签退失败");
        }
        createReqVO.setColonelVisitLogId(todayVisitLog.getColonelVisitLogId());
        createReqVO.setSignOutDate(new Date());
        createReqVO.setVisitFlag(1);
        MemColonelVisitLog memColonelVisitLog = HutoolBeanUtils.toBean(createReqVO, MemColonelVisitLog.class);
        if (Objects.isNull(memColonelVisitLog)) {
            return R.fail("入参不能为空");
        }
        //查询出拜访间隔时间
        if (ToolUtil.isNotEmpty(memColonelVisitLog.getSignOutDate())) {
            long startTimestamp = todayVisitLog.getSignInDate().getTime();
            long endTimestamp = memColonelVisitLog.getSignOutDate().getTime();
            long differenceInMilliseconds = endTimestamp - startTimestamp;
            memColonelVisitLog.setVisitIntervalTime(differenceInMilliseconds / 1000 + "");
        }
        if (memColonelVisitLogMapper.updateById(memColonelVisitLog) > 0) {
            return R.ok();
        }
        return R.fail("签退失败");
    }

    @Override
    public R<Boolean> waiveInner(Long colonelId, Long consumerNo) {
        MemColonelVisitLog todayVisitLog = this.getTodayVisitLog(colonelId, consumerNo, DateUtils.getDate());
        if (ObjectUtil.isNull(todayVisitLog)) {
            return R.fail("该客户未签到，放弃拜访失败");
        }
        if (ObjectUtil.notEqual(0, todayVisitLog.getVisitFlag())) {
            return R.fail("该客户签到状态已变更，签退失败");
        }
        MemColonelVisitLog memColonelVisitLog = HutoolBeanUtils.toBean(todayVisitLog, MemColonelVisitLog.class);
        if (Objects.isNull(memColonelVisitLog)) {
            return R.fail("客户签到记录不能为空");
        }
        memColonelVisitLog.setColonelVisitLogId(todayVisitLog.getColonelVisitLogId());
        memColonelVisitLog.setVisitFlag(-1);
        if (memColonelVisitLogMapper.updateById(todayVisitLog) > 0) {
            return R.ok();
        }
        return R.fail("放弃拜访失败");
    }

    @Override
    public R<String> signInOrOutFlagInner(Long colonelId, Long consumerNo) {
        MemColonelVisitLog todayVisitLog = this.getTodayVisitLog(colonelId, consumerNo, DateUtils.getDate());
        if (ObjectUtil.isNull(todayVisitLog)) {
            return R.ok("signIn");
        }
        if (ObjectUtil.equal(0, todayVisitLog.getVisitFlag())) {
            return R.ok("signOut");
        }
        return R.ok();
    }

    /**
     * 修改业务员拜访日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemColonelVisitLog(MemColonelVisitLogSaveReqVO updateReqVO) {
        MemColonelVisitLog memColonelVisitLog = memColonelVisitLogMapper.selectById(updateReqVO.getColonelVisitLogId());

        //查询出拜访间隔时间
        if (ToolUtil.isNotEmpty(updateReqVO.getSignOutDate())){
            long startTimestamp = memColonelVisitLog.getSignInDate().getTime();
            long endTimestamp = updateReqVO.getSignOutDate().getTime();

            long differenceInMilliseconds = endTimestamp - startTimestamp;
            updateReqVO.setVisitIntervalTime(differenceInMilliseconds / 1000+"");
        }

        memColonelVisitLogMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, MemColonelVisitLog.class));
    }

    /**
     * 删除业务员拜访日志
     *
     * @param visitId 主键ID
     */
    @Override
    public void deleteMemColonelVisitLog(Long visitId) {
        // 删除
        memColonelVisitLogMapper.deleteById(visitId);
    }

    /**
     * 批量删除业务员拜访日志
     *
     * @param visitIds 需要删除的业务员拜访日志主键
     * @return 结果
     */
    @Override
    public void deleteMemColonelVisitLogByVisitIds(Long[] visitIds) {
        for(Long visitId : visitIds){
            this.deleteMemColonelVisitLog(visitId);
        }
    }

    /**
     * 获得业务员拜访日志
     *
     * @param visitId 主键ID
     * @return 业务员拜访日志
     */
    @Override
    public MemColonelVisitLog getMemColonelVisitLog(Long visitId) {
        return memColonelVisitLogMapper.selectById(visitId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemColonelVisitLogRespVO> getMemColonelVisitLogPage(MemColonelVisitLogPageReqVO pageReqVO) {
        List<Long> branchIdList = null;
        if(ToolUtil.isNotEmpty(pageReqVO.getKeywords()) || ToolUtil.isNotEmpty(pageReqVO.getAreaId())){
            MemBranchPageReqVO req = new MemBranchPageReqVO();
            req.setKeywords(pageReqVO.getKeywords());
            req.setAreaId(pageReqVO.getAreaId());
            // 根据关键字查询门店ID集合
            branchIdList = memBranchMapper.selectMemBranchListByParam(req);
            if(ToolUtil.isEmpty(branchIdList)){
                return new PageResult<MemColonelVisitLogRespVO>();
            }
        }

        pageReqVO.setBranchIdList(branchIdList);
        // 根据经过处理的页面请求对象进行分页查询
        PageResult<MemColonelVisitLog> memColonelVisitLog = memColonelVisitLogMapper.selectPage(pageReqVO);
        // 将查询结果转换为响应对象
        PageResult<MemColonelVisitLogRespVO> resp = HutoolBeanUtils.toBean(memColonelVisitLog, MemColonelVisitLogRespVO.class);
        LFUCache<Long, ColonelDTO> colonelCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, BranchDTO> branchCache = CacheUtil.newLFUCache(0);
        // 遍历结果列表，填充门店名称和部门ID
        resp.getList().forEach(item -> {
            // 使用缓存获取 branch 信息
            Long branchId =item.getBranchId();
            if (branchId != null) {
                BranchDTO branch = branchCache.get(branchId,
                        () -> memberCacheService.getBranchDto(branchId));
                item.setBranchLongitude(branch.getLongitude());
                item.setBranchLatitude(branch.getLatitude());
                item.setBranchName(branch.getBranchName());
            }

            // 使用缓存获取 Colonel 信息
            Long colonelId = item.getColonelId();
            if (colonelId != null) {
                ColonelDTO colonel = colonelCache.get(colonelId,
                        () -> memberCacheService.getColonel(colonelId));
                item.setColonelName(colonel != null ? colonel.getColonelName() : null);
                item.setDeptId(colonel != null ? colonel.getDeptId() : null);
            }
            if(ToolUtil.isNotEmpty(item.getVisitIntervalTime())){
                // 计算并设置访问间隔时间（小时）
                double minutes = Math.round((Double.parseDouble(item.getVisitIntervalTime()) / 60.0) * 100) / 100.0;
                item.setVisitIntervalTime(minutes+"");
            }

        });

        return resp;
    }

    @Override
    public PageResult<MemColonelVisitLogCollectRespVO> getColonelVisitLogCollectList(MemColonelVisitLogPageReqVO pageReqVO) {
        Page<MemColonelVisitLogPageReqVO> page = new Page<>(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        Page<MemColonelVisitLogCollectRespVO> resp =memColonelVisitLogMapper.getColonelVisitLogByParam(pageReqVO,page);
        resp.getRecords().forEach(item -> {
            String dateTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD,item.getCreateDate());
            item.setShortTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 00:00:00",dateTime+" 23:59:59","short").intValue());
            item.setMediumTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 00:00:00",dateTime+" 23:59:59","medium").intValue());
            item.setLongTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 00:00:00",dateTime+" 23:59:59","long").intValue());
            item.setValidVisitNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 00:00:00",dateTime+" 23:59:59",null).intValue());
            item.setZeroToSevenTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 00:00:00",dateTime+" 07:00:00",null).intValue());
            item.setSevenToEightTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 07:00:00",dateTime+" 08:00:00",null).intValue());
            item.setEightToNineTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 08:00:00",dateTime+" 09:00:00",null).intValue());
            item.setNineToTenTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 09:00:00",dateTime+" 10:00:00",null).intValue());
            item.setTenToElevenTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 10:00:00",dateTime+" 11:00:00",null).intValue());
            item.setElevenToTwelveTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 11:00:00",dateTime+" 12:00:00",null).intValue());
            item.setTwelveToThirteenTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 12:00:00",dateTime+" 13:00:00",null).intValue());
            item.setThirteenToFourteenTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 13:00:00",dateTime+" 14:00:00",null).intValue());
            item.setFourteenToFifteenTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 14:00:00",dateTime+" 15:00:00",null).intValue());
            item.setFifteenToSixteenTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 15:00:00",dateTime+" 16:00:00",null).intValue());
            item.setSixteenToSeventeenTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 16:00:00",dateTime+" 17:00:00",null).intValue());
            item.setSeventeenToEighteenTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 17:00:00",dateTime+" 18:00:00",null).intValue());
            item.setEighteenToTwentyFourTimeNumber(memColonelVisitLogMapper.getColonelVisitLogNumByDate(item.getColonelId(),dateTime+" 18:00:00",dateTime+" 23:59:59",null).intValue());

        });
        return new PageResult<MemColonelVisitLogCollectRespVO>(resp.getRecords(),resp.getTotal());
    }

    @Override
    public List<MemColonelVisitLog> getLatestRecordsForEachColonel(String startTime, String endTime) {
        return memColonelVisitLogMapper.getLatestRecordsForEachColonel(startTime,endTime);
    }

    @Override
    public byte[] getVisitLogImageZip(MemColonelVisitLogPageReqVO pageReqVO) throws IOException {
        pageReqVO.setVisitFlag(1);
        PageResult<MemColonelVisitLog> resp =memColonelVisitLogMapper.selectPage(pageReqVO);
        List<ImageInfoReqVO> imageInfoReqVOList = new ArrayList<>();
        resp.getList().forEach(item -> {
            if(ToolUtil.isNotEmpty(item.getSignInImgUrls())) {
                ImageInfoReqVO imageInfoReqVO = new ImageInfoReqVO();
                imageInfoReqVO.setUrl(item.getSignInImgUrls());
                imageInfoReqVO.setDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, item.getSignInDate()));
                BranchDTO branchDTO = memberCacheService.getBranchDto(item.getBranchId());
                imageInfoReqVO.setName(branchDTO.getBranchName() + "-" + DateUtils.parseDateToStr("HH-mm-ss", item.getSignInDate()) + ".jpeg");
                imageInfoReqVOList.add(imageInfoReqVO);
            }
        });
        return fileApi.downloadImages(imageInfoReqVOList);
    }

    @Override
    public List<MemColonelVisitLogTravelPathRespVO> getColonelVisitLogTravelPath(MemColonelVisitLogPageReqVO pageReqVO) {
        pageReqVO.setVisitFlag(1);
        PageResult<MemColonelVisitLog> memColonelVisitLog = memColonelVisitLogMapper.selectPage(pageReqVO);
        // 将查询结果转换为响应对象
        PageResult<MemColonelVisitLogTravelPathRespVO> resp = HutoolBeanUtils.toBean(memColonelVisitLog, MemColonelVisitLogTravelPathRespVO.class);
        resp.getList().sort(Comparator.comparing(MemColonelVisitLogTravelPathRespVO::getSignInDate));

        List<MemColonelVisitLogTravelPathRespVO> list = new ArrayList<>(resp.getList().size()); // 准备一个新列表存放处理后的数据

        for (int i = 0; i < resp.getList().size(); i++) {
            MemColonelVisitLogTravelPathRespVO current = resp.getList().get(i);

            // 对于第一条记录，没有前一条记录，直接设置在途时间为0或者不设置，取决于业务需求
            if (i == resp.getList().size()-1) {
                current.setTravelTime("0");
            } else {
                MemColonelVisitLogTravelPathRespVO previous = resp.getList().get(i + 1);

                // 计算在途时间
                LocalDateTime prevSignInDateTime = previous.getSignInDate().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime();
                LocalDateTime currSignOutDateTime = current.getSignOutDate().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime();
                long travelTimeMinutes = ChronoUnit.MINUTES.between(currSignOutDateTime,prevSignInDateTime);
                current.setTravelTime(Math.abs(travelTimeMinutes) + "");
            }

            BranchDTO branchDTO = memberCacheService.getBranchDto(Long.parseLong(current.getBranchId()));
            current.setBranchName(branchDTO.getBranchName());
            current.setContactPhone(branchDTO.getContactPhone());
            current.setContactName(branchDTO.getContactName());
            current.setBranchAddr(branchDTO.getBranchAddr());
            current.setBranchLatitude(branchDTO.getLatitude());
            current.setBranchLongitude(branchDTO.getLongitude());
            current.setBranchImages(branchDTO.getBranchImages());

            TrdColonelAppOrderListPageReqVO reqVO = new TrdColonelAppOrderListPageReqVO();
            reqVO.setBranchId(branchDTO.getBranchId());
            reqVO.setStartTime(pageReqVO.getStartTime());
            reqVO.setEndTime(pageReqVO.getEndTime());
            BigDecimal saleAmount = orderApi.getSaleAmount(reqVO);
            current.setSalesAmt(ObjectUtils.defaultIfNull(saleAmount, BigDecimal.ZERO)); // 使用工具类简化空值处理

            // 设置时间格式
            current.setSignInDateHHMMSS(DateUtils.parseDateToStr("HH:mm:ss", current.getSignInDate()));
            current.setSignOutDateHHMMSS(DateUtils.parseDateToStr("HH:mm:ss", current.getSignOutDate()));

            // 计算并设置访问间隔时间（分钟）
            double minutes = Math.round(Double.parseDouble(current.getVisitIntervalTime()) / 60.0);
            current.setVisitIntervalTime(minutes + "");

            list.add(current);
        }

        return list;
    }

    @Override
    public List<MemColonelVisitLog> getLogListByColonelIdAndDate(Long colonelId, Date startTime, Date endTime) {
        return memColonelVisitLogMapper.getLogListByColonelIdAndDate(colonelId,startTime, endTime);
    }


    private void validateMemColonelVisitLogExists(Long visitId) {
        if (memColonelVisitLogMapper.selectById(visitId) == null) {
            throw exception(MEM_COLONEL_VISIT_LOG_NOT_EXISTS);
        }
    }

    @Override
    public long getDaysSinceLastVisitByBranchId(Long branchId) {
        Long days = memColonelVisitLogMapper.getDaysSinceLastVisitByBranchId(branchId);
        return days == null ? -1 : days;
    }


    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 业务员拜访日志 TODO 补充编号 ==========
    // ErrorCode MEM_COLONEL_VISIT_LOG_NOT_EXISTS = new ErrorCode(TODO 补充编号, "业务员拜访日志不存在");


}
