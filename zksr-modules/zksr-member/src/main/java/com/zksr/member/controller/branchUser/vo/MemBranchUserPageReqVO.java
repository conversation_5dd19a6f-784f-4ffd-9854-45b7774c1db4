package com.zksr.member.controller.branchUser.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 门店用户关联关系对象 mem_branch_user
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@ApiModel("门店用户关联关系 - mem_branch_user分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemBranchUserPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty(value = "用户id")
    private Long memberId;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private Long branchId;


}
