package com.zksr.member.controller.colonelTarget.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Accessors(chain = true)
@ApiModel("业务员批量新增页面返回数据接口")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemColonelActualSaleRespVO{

    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelTargetId;

    /** 业务员Id */
    @ApiModelProperty(value = "业务员Id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelId;

    /** 业务员名称 */
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "区域城市ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    @ApiModelProperty(value = "区域城市名称")
    private String areaName;

    @ApiModelProperty(value = "业务员职务（级别 数据字典：sys_colonel_level）")
    private Long colonelLevel;

    /** 目标年月 */
    @ApiModelProperty(value = "目标年月（YYYY-MM）")
    private String targetYearMonth;

    /** 销售额 */
    @ApiModelProperty(value = "销售额")
    private BigDecimal saleAmt;

    /** 新开客户数量 */
    @ApiModelProperty(value = "新开客户数量")
    private Long addBranchQty;

    /** 活跃客户数量 */
    @ApiModelProperty(value = "活跃客户数量")
    private Long saleBranchQty;

    /** 拜访客户数量 */
    @ApiModelProperty(value = "拜访客户数量")
    private Long visitQty;

    @ApiModelProperty(value = "状态: -1: 当前时间两月的数据 0 保存，1 设置完成")
    private Integer status;

    /** 下单数量 */
    @ApiModelProperty(value = "下单数量")
    private Long orderCount;

    /** 客单价 */
    @ApiModelProperty(value = "客单价")
    private BigDecimal avgOrderValue;

    @ApiModelProperty(value = "首次动销数量")
    private Long firstSaleCount;


}
