package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 业务员目标设置对象 mem_colonel_target
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@TableName(value = "mem_colonel_target")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemColonelTarget extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    @Excel(name = "主键ID")
    private Long colonelTargetId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 业务员Id */
    @Excel(name = "业务员Id")
    private Long colonelId;

    /** 目标年份 */
    @Excel(name = "目标年份")
    private String targetYear;

    /** 目标月份 */
    @Excel(name = "目标月份")
    private String targetMonth;

    /** 销售额 */
    @Excel(name = "销售额")
    private BigDecimal salesMoney;

    /** 月新开客户数量 */
    @Excel(name = "月新开客户数量")
    private Long monthNewCustomer;

    /** 月活动客户数量 */
    @Excel(name = "月活动客户数量")
    private Long monthActivityCustomer;

    /** 月拜访客户数量 */
    @Excel(name = "月拜访客户数量")
    private Long monthVisitCustomer;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 状态 1正常 0停用 */
    @Excel(name = "状态 1正常 0停用")
    private Integer status;

    /** 删除状态  (0正常  2已删除) */
    @ApiModelProperty(value = "状态", example = "0")
    private String delFlag;

    /** 月下单数量 */
    @Excel(name = "月下单数量")
    private Long monthOrderCount;

    /** 月客单价 */
    @Excel(name = "月客单价")
    private BigDecimal monthAvgOrderValue;

    /** 月首次动销数量 */
    @Excel(name = "月首次动销数量")
    private Long monthFirstSaleCount;

}
