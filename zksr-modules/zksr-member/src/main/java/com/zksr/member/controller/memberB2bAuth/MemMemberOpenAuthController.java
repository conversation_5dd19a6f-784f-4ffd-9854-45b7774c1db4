package com.zksr.member.controller.memberB2bAuth;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemMemberOpenAuth;
import com.zksr.member.service.IMemMemberOpenAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.memberB2bAuth.vo.MemMemberOpenAuthPageReqVO;
import com.zksr.member.controller.memberB2bAuth.vo.MemMemberOpenAuthSaveReqVO;
import com.zksr.member.controller.memberB2bAuth.vo.MemMemberOpenAuthRespVO;
import com.zksr.member.convert.memberB2bAuth.MemMemberOpenAuthConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * B2B支付门店认证Controller
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Api(tags = "管理后台 - B2B支付门店认证接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/memberB2bAuth")
public class MemMemberOpenAuthController {
    @Autowired
    private IMemMemberOpenAuthService memMemberOpenAuthService;

    /**
     * 新增B2B支付门店认证
     */
    @ApiOperation(value = "新增B2B支付门店认证", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "B2B支付门店认证", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemMemberOpenAuthSaveReqVO createReqVO) {
        return success(memMemberOpenAuthService.insertMemMemberOpenAuth(createReqVO));
    }

    /**
     * 修改B2B支付门店认证
     */
    @ApiOperation(value = "修改B2B支付门店认证", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "B2B支付门店认证", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemMemberOpenAuthSaveReqVO updateReqVO) {
            memMemberOpenAuthService.updateMemMemberOpenAuth(updateReqVO);
        return success(true);
    }

    /**
     * 删除B2B支付门店认证
     */
    @ApiOperation(value = "删除B2B支付门店认证", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "B2B支付门店认证", businessType = BusinessType.DELETE)
    @DeleteMapping("/{b2bAuthOpenIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] b2bAuthOpenIds) {
        memMemberOpenAuthService.deleteMemMemberOpenAuthByB2bAuthOpenIds(b2bAuthOpenIds);
        return success(true);
    }

    /**
     * 获取B2B支付门店认证详细信息
     */
    @ApiOperation(value = "获得B2B支付门店认证详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{b2bAuthOpenId}")
    public CommonResult<MemMemberOpenAuthRespVO> getInfo(@PathVariable("b2bAuthOpenId") Long b2bAuthOpenId) {
        MemMemberOpenAuth memMemberOpenAuth = memMemberOpenAuthService.getMemMemberOpenAuth(b2bAuthOpenId);
        return success(MemMemberOpenAuthConvert.INSTANCE.convert(memMemberOpenAuth));
    }

    /**
     * 分页查询B2B支付门店认证
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得B2B支付门店认证分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemMemberOpenAuthRespVO>> getPage(@Valid MemMemberOpenAuthPageReqVO pageReqVO) {
        PageResult<MemMemberOpenAuth> pageResult = memMemberOpenAuthService.getMemMemberOpenAuthPage(pageReqVO);
        return success(MemMemberOpenAuthConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:memberB2bAuth:add";
        /** 编辑 */
        public static final String EDIT = "member:memberB2bAuth:edit";
        /** 删除 */
        public static final String DELETE = "member:memberB2bAuth:remove";
        /** 列表 */
        public static final String LIST = "member:memberB2bAuth:list";
        /** 查询 */
        public static final String GET = "member:memberB2bAuth:query";
        /** 停用 */
        public static final String DISABLE = "member:memberB2bAuth:disable";
        /** 启用 */
        public static final String ENABLE = "member:memberB2bAuth:enable";
    }
}
