package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 投诉信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("mem_complain")
@ApiModel(value="MemComplain对象", description="投诉信息表")
public class MemComplain extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Excel(name = "投诉信息ID")
    @TableId(value = "complain_id", type = IdType.ASSIGN_ID)
    private Long complainId;

    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    @Excel(name = "用户ID")
    private Long memberId;

    @Excel(name = "用户名称")
    private String username;

    @Excel(name = "用户手机号")
    private String phone;

    @Excel(name = "所在门店ID")
    private Long branchId;

    @Excel(name = "投诉说明")
    private String complainContent;

    @Excel(name = "投诉对象类型", readConverterExp = "投诉对象字典(1.商品2.物流3.业务员4.司机)")
    private Integer complainType;

    @Excel(name = "投诉凭证")
    private String complainImage;

    @Excel(name = "投诉对象名称")
    private String targetUsername;

    @Excel(name = "投诉对象手机号(非必填)")
    private String targetPhone;

    @Excel(name = "投诉处理回复")
    private String complainReply;

    @Excel(name = "投诉方业务ID")
    private Long businessId;

    @Excel(name = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processTime;

    @Excel(name = "处理人")
    private String processBy;
    
    @Excel(name = "状态", readConverterExp = "状态,0-待处理,1-已回复")
    private String status;


}
