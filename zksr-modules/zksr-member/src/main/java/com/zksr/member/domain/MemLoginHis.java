package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 登录历史对象 mem_login_his
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@TableName(value = "mem_login_his")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemLoginHis {
    private static final long serialVersionUID = 1L;

    /**
     * 登录历史id
     */
    @TableId
    private Long loginHisId;

    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /**
     * 日期;yyyyMMdd
     */
    @Excel(name = "日期;yyyyMMdd")
    private Long dateId;

    /**
     * 微信openid;后台登录信息获取
     */
    @Excel(name = "微信openid;后台登录信息获取")
    private String wxOpenid;

    /**
     * 用户手机号;后台登录信息获取
     */
    @Excel(name = "用户手机号;后台登录信息获取")
    private String memberPhone;

    /**
     * 用户名;后台登录信息获取
     */
    @Excel(name = "用户名;后台登录信息获取")
    private String memberUsername;

    /**
     * 用户id;后台登录信息获取
     */
    @Excel(name = "用户id;后台登录信息获取")
    private Long memberId;

    /**
     * 门店id;后台登录信息获取
     */
    @Excel(name = "门店id;后台登录信息获取")
    private Long branchId;

    /**
     * ip地址;http_request
     */
    @Excel(name = "ip地址;http_request")
    private String ip;

    /**
     * ip地址归属地;http_request
     */
    @Excel(name = "ip地址归属地;http_request")
    private String district;

    /**
     * 类型（数据字典）;0-登陆  1-访问
     */
    @Excel(name = "类型", readConverterExp = "数=据字典")
    private String tp;

    /**
     * 设备id;前端传（HttpHeader）
     */
    @Excel(name = "设备id;前端传", readConverterExp = "H=ttpHeader")
    private String deviceId;

    /**
     * 设备类型;前端传（HttpHeader）
     */
    @Excel(name = "设备类型;前端传", readConverterExp = "H=ttpHeader")
    private String deviceType;

    /**
     * 设备品牌;前端传（HttpHeader）
     */
    @Excel(name = "设备品牌;前端传", readConverterExp = "H=ttpHeader")
    private String deviceBrand;

    /**
     * 设备型号;前端传（HttpHeader）
     */
    @Excel(name = "设备型号;前端传", readConverterExp = "H=ttpHeader")
    private String deviceModel;

    /**
     * 系统名称;前端传（HttpHeader）
     */
    @Excel(name = "系统名称;前端传", readConverterExp = "H=ttpHeader")
    private String osName;

    /**
     * 操作系统版本;前端传（HttpHeader）
     */
    @Excel(name = "操作系统版本;前端传", readConverterExp = "H=ttpHeader")
    private String osVersion;

    /**
     * pc app xcx
     */
    @Excel(name = "pc app xcx")
    private String port;

    /**
     * spu_id
     */
    @Excel(name = "spu_id")
    private Long spuId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

}
