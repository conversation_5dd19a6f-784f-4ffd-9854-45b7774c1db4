package com.zksr.member.mapper;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.controller.loginHis.vo.MemLoginHisPageReqVO;
import com.zksr.member.domain.MemLoginHis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 登录历史Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Mapper
public interface MemLoginHisMapper extends BaseMapperX<MemLoginHis> {
    default PageResult<MemLoginHis> selectPage(MemLoginHisPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemLoginHis>()
                .eqIfPresent(MemLoginHis::getLoginHisId, reqVO.getLoginHisId())
                .eqIfPresent(MemLoginHis::getSysCode, reqVO.getSysCode())
                .eqIfPresent(MemLoginHis::getDateId, reqVO.getDateId())
                .eqIfPresent(MemLoginHis::getWxOpenid, reqVO.getWxOpenid())
                .eqIfPresent(MemLoginHis::getMemberPhone, reqVO.getMemberPhone())
                .likeIfPresent(MemLoginHis::getMemberUsername, reqVO.getMemberUsername())
                .eqIfPresent(MemLoginHis::getMemberId, reqVO.getMemberId())
                .eqIfPresent(MemLoginHis::getBranchId, reqVO.getBranchId())
                .eqIfPresent(MemLoginHis::getIp, reqVO.getIp())
                .eqIfPresent(MemLoginHis::getDistrict, reqVO.getDistrict())
                .eqIfPresent(MemLoginHis::getTp, reqVO.getTp())
                .eqIfPresent(MemLoginHis::getDeviceId, reqVO.getDeviceId())
                .eqIfPresent(MemLoginHis::getDeviceType, reqVO.getDeviceType())
                .eqIfPresent(MemLoginHis::getDeviceBrand, reqVO.getDeviceBrand())
                .eqIfPresent(MemLoginHis::getDeviceModel, reqVO.getDeviceModel())
                .likeIfPresent(MemLoginHis::getOsName, reqVO.getOsName())
                .eqIfPresent(MemLoginHis::getOsVersion, reqVO.getOsVersion())
                .eqIfPresent(MemLoginHis::getPort, reqVO.getPort())
                .eqIfPresent(MemLoginHis::getSpuId, reqVO.getSpuId())
                .betweenIfPresent(MemLoginHis::getCreateTime, reqVO.getCreateTimeBegin(), reqVO.getCreateTimeEnd())
                .orderByDesc(MemLoginHis::getLoginHisId));
    }

    /**
     * 根据tp+member_id+branch_id+port+date_id+device_id查询数据
     *
     * @param memLoginHis
     * @return
     */
    default MemLoginHis selectMemLoginHis(MemLoginHis memLoginHis) {
        return selectOne(new LambdaQueryWrapperX<MemLoginHis>()
                .eqIfPresent(MemLoginHis::getTp, memLoginHis.getTp())
                .eqIfPresent(MemLoginHis::getMemberId, memLoginHis.getMemberId())
                .eqIfPresent(MemLoginHis::getBranchId, memLoginHis.getBranchId())
                .eqIfPresent(MemLoginHis::getPort, memLoginHis.getPort())
                .eqIfPresent(MemLoginHis::getDateId, memLoginHis.getDateId())
                .eqIfPresent(MemLoginHis::getDeviceId, memLoginHis.getDeviceId())
        );
    }

    List<MemLoginHis> selectLoginTimeByUser(@Param("userList") List<String> userList, @Param("sysCode") Long sysCode);

}
