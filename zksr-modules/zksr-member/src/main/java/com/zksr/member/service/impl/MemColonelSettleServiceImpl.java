package com.zksr.member.service.impl;

import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.redis.service.RedisService;
import com.zksr.member.api.colonelApp.dto.PageDataDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.ColonelOrderDaySettleDTO;
import com.zksr.trade.api.order.vo.ColonelOrderDaySettleVO;
import com.zksr.member.convert.colonel.MemberColonelDaySettleConvert;
import com.zksr.member.domain.*;
import com.zksr.member.mapper.*;
import com.zksr.member.service.IMemColonelSettleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class MemColonelSettleServiceImpl implements IMemColonelSettleService {

    @Autowired
    private MemColonelMapper memColonelMapper;

    @Autowired
    private RedisService redisService;
    @Autowired
    private MemColonelDaySettleMapper memColonelDaySettleMapper;
    @Autowired
    private MemColonelMonthSettleMapper memColonelMonthSettleMapper;
    @Autowired
    private MemColonelVisitLogMapper memColonelVisitLogMapper;

    @Autowired
    private MemBranchRegisterMapper memBranchRegisterMapper;

    @Autowired
    private OrderApi orderApi;

    @Transactional
    @Override
    public void colonelDaySettlementJob(Long sysCode, Date date) {
        // 1、获取当前平台下状态正常所有业务员
        List<MemColonel> colonelList = memColonelMapper.getListBySysCode(sysCode);
        Set<Long> colonelIdList = CollectionUtils.convertSet(colonelList, MemColonel::getColonelId);

//        List<MemColonelDaySettle> colonelDaySettleList = new ArrayList<>();
        colonelIdList.forEach(colonelId -> {
            //获取业务员 业务数据缓存写入到日结表中
            String key = CacheConstants.getColonelAppPageDataKey(colonelId, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, date));
            PageDataDTO dataDTO = redisService.getCacheObject(key);
            if (ToolUtil.isEmpty(dataDTO)) {
                return;
            }
            // 2、将业务员业务数据缓存写入到日结表中
            MemColonelDaySettle colonelDaySettle = new MemColonelDaySettle();
            MemberColonelDaySettleConvert.INSTANCE.convert(colonelDaySettle, dataDTO);
            colonelDaySettle.setSysCode(sysCode);
            colonelDaySettle.setSettleCreateDate(DateUtils.getNowDate());
//            colonelDaySettleList.add(colonelDaySettle);
            memColonelDaySettleMapper.insert(colonelDaySettle);

            colonelMonthSettlementJob(sysCode, date, colonelId);
        });

    }

    @Override
    public void colonelMonthSettlementJob(Long sysCode, Date date, Long colonelId) {
       List<MemColonelMonthSettle> colonelMonthSettleList = memColonelDaySettleMapper.getListBySysCodeAndDate(sysCode, DateUtil.beginOfMonth(date), DateUtil.endOfMonth(date), colonelId);

       Set<Long> colonelIdList = CollectionUtils.convertSet(colonelMonthSettleList, MemColonelMonthSettle::getColonelId);
       List<MemColonelMonthSettle> memColonelMonthSettleList = memColonelMonthSettleMapper.getListInfoByColonelIdAndSysCode(colonelIdList, sysCode, DateUtils.parseDateToStr(DateUtils.YYYY_MM, date));
       Map<Long, MemColonelMonthSettle> colonelMonthSettleMap = memColonelMonthSettleList.stream().collect(Collectors.toMap(MemColonelMonthSettle::getColonelId, colonelMonthSettle -> colonelMonthSettle));

       // 新增数据集合
       List<MemColonelMonthSettle> addList = new ArrayList<>();
       // 更新数据集合
       List<MemColonelMonthSettle> updateList = new ArrayList<>();

       colonelMonthSettleList.forEach(colonelMonthSettle -> {
           colonelMonthSettle.setSysCode(sysCode);
           colonelMonthSettle.setSettleMonthDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM, date));
            if (colonelMonthSettleMap.containsKey(colonelMonthSettle.getColonelId())) { // 存在值，更新
                colonelMonthSettle.setColonelMonthSettleId(colonelMonthSettleMap.get(colonelMonthSettle.getColonelId()).getColonelMonthSettleId());
                updateList.add(colonelMonthSettle);
            } else { // 不存在值，新增
                addList.add(colonelMonthSettle);
            }
        });
        // 新增
       if (!addList.isEmpty())  memColonelMonthSettleMapper.insertBatch(addList);
       // 更新
       if (!updateList.isEmpty()) memColonelMonthSettleMapper.updateBatch(updateList);

    }

    @Transactional
    @Override
    public void colonelSettlementJob(Long sysCode, String startDate, String endDate) {
        // 1、获取当前平台下状态正常所有业务员
        List<MemColonel> colonelList = memColonelMapper.getListBySysCode(sysCode);

        // 2、根据业务员 、 平台编码、 调整开始时间 、调整结束时间 获取订单数据
        colonelList.forEach(colonel -> {
            List<Long> colonelIdList = new ArrayList<>();
            colonelIdList.add(colonel.getColonelId());
            ColonelOrderDaySettleVO colonelDaySettleVO = new ColonelOrderDaySettleVO(sysCode, colonelIdList, startDate, endDate);
            // 获取业务员 需日结 计算数据 【门店下单数量、门店下单金额、业务下单数量、业务下单金额、门店退货金额、动销门店数量】
           List<ColonelOrderDaySettleDTO> colonelOrderList = orderApi.getColonelOrderBusinessSettle(colonelDaySettleVO).getCheckedData();

           // 获取业务员 需日结 计算数据 【拜访门店数量】
            List<ColonelOrderDaySettleDTO> colonelVisitList =  memColonelVisitLogMapper.getColonelVisitLogInfo(colonelDaySettleVO);
            Map<String, ColonelOrderDaySettleDTO> colonelVisitMap = colonelVisitList.stream().collect(
                    Collectors.toMap(
                            ColonelOrderDaySettleDTO:: getMapKey,
                            colonelVisit -> colonelVisit));
           // 获取业务员 需日结 计算数据 【拓店数量】
            List<ColonelOrderDaySettleDTO> colonelBranchRegisterList = memBranchRegisterMapper.getColonelBranchRegisterInfo(colonelDaySettleVO);
            Map<String, ColonelOrderDaySettleDTO> colonelBranchRegisterMap = colonelBranchRegisterList.stream().collect(
                    Collectors.toMap(
                            ColonelOrderDaySettleDTO:: getMapKey,
                            colonelBranchRegister -> colonelBranchRegister));
            // 将
            colonelVisitList.stream()
                    .filter(cvl -> colonelOrderList.stream().noneMatch(col -> Objects.equals(col.getMapKey(), cvl.getMapKey())))
                    .forEach(colonelOrderList::add);

            colonelBranchRegisterList.stream()
                    .filter(cbrl -> colonelOrderList.stream().noneMatch(col -> Objects.equals(col.getMapKey(), cbrl.getMapKey())))
                    .forEach(colonelOrderList::add);



            colonelOrderList.stream()
                    .filter(col -> colonelVisitMap.containsKey(col.getMapKey()) || colonelBranchRegisterMap.containsKey(col.getMapKey()))
                    .forEach(col -> {
                        ColonelOrderDaySettleDTO visit =  colonelVisitMap.get(col.getMapKey());
                        ColonelOrderDaySettleDTO branchRegister = colonelBranchRegisterMap.get(col.getMapKey());
                        if (ToolUtil.isNotEmpty(visit))
                            col.setVisitQty(visit.getVisitQty());

                        if (ToolUtil.isNotEmpty(branchRegister))
                            col.setAddBranchQty(branchRegister.getAddBranchQty());
                    });

//            List<MemColonelDaySettle> colonelDaySettleList = colonelOrderList.stream().map(col -> {
//                col.setSysCode(sysCode);
//                return MemberColonelDaySettleConvert.INSTANCE.convert(col);
//            }).collect(Collectors.toList());

            colonelOrderList.forEach(col -> {
                col.setSysCode(sysCode);
                MemColonelDaySettle colonelDaySettle = MemberColonelDaySettleConvert.INSTANCE.convert(col);
                // 删除日结信息
                memColonelDaySettleMapper.deleteColonelDaySettleBySysCodeAndDate(sysCode, colonelDaySettle.getSettleCreateDate(), colonel.getColonelId());

                // 新增日结信息
                memColonelDaySettleMapper.insert(colonelDaySettle);
            });
//            // 操作 日结信息
//            memColonelDaySettleMapper.insertBatch(colonelDaySettleList);


            // 3、执行月结操作
           List<String> monthRangeList = DateUtils.getMonthRange(startDate, endDate);
           monthRangeList.forEach(month -> {
               colonelMonthSettlementJob(sysCode, DateUtils.parseDate(month), colonel.getColonelId());
           });

        });

    }




}
