package com.zksr.member.controller.branch.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 门店信息对象 mem_branch
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Data
@ApiModel("门店信息 - mem_branch account VO")
public class MemBranchAccountRespVO extends MemBranchRespVO{

    /** 可用储值余额 */
    @ApiModelProperty(value = "可用储值余额")
    private BigDecimal withdrawableAmt = BigDecimal.ZERO;

    /** 冻结储值金额 */
    @ApiModelProperty(value = "冻结储值金额")
    private BigDecimal frozenAmt = BigDecimal.ZERO;

    /** 赠送余额 */
    @ApiModelProperty(value = "赠送余额")
    private BigDecimal giveAmt = BigDecimal.ZERO;

    /** 欠款 */
    @ApiModelProperty(value = "欠款")
    private BigDecimal debtAmt = BigDecimal.ZERO;

    /** 门店提现手续费 */
    @ApiModelProperty(value = "门店提现手续费", notes = "需要/100使用")
    private BigDecimal branchWithdrawRate = BigDecimal.ZERO;
}
