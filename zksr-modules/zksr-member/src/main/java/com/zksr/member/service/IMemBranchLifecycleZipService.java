package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.api.branchLifecycle.dto.ColonelBranchLifecycleQtyDTO;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipRespVO;
import com.zksr.member.domain.MemBranchLifecycleZip;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipPageReqVO;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipSaveReqVO;
import com.zksr.system.api.partnerPolicy.dto.BranchLifecycleSettingPolicyDTO;
import com.zksr.trade.api.order.vo.TrdOrder;

import java.util.List;
import java.util.Set;

/**
 * 门店生命周期拉链Service接口
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface IMemBranchLifecycleZipService {

    /**
     * 新增门店生命周期拉链
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemBranchLifecycleZip(@Valid MemBranchLifecycleZipSaveReqVO createReqVO);

    /**
     * 修改门店生命周期拉链
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemBranchLifecycleZip(@Valid MemBranchLifecycleZipSaveReqVO updateReqVO);

    /**
     * 删除门店生命周期拉链
     *
     * @param branchLifecycleZipId 门店生命周期拉链表ID
     */
    public void deleteMemBranchLifecycleZip(Long branchLifecycleZipId);

    /**
     * 批量删除门店生命周期拉链
     *
     * @param branchLifecycleZipIds 需要删除的门店生命周期拉链主键集合
     * @return 结果
     */
    public void deleteMemBranchLifecycleZipByBranchLifecycleZipIds(Long[] branchLifecycleZipIds);

    /**
     * 获得门店生命周期拉链
     *
     * @param branchLifecycleZipId 门店生命周期拉链表ID
     * @return 门店生命周期拉链
     */
    public MemBranchLifecycleZip getMemBranchLifecycleZip(Long branchLifecycleZipId);

    /**
     * 获得门店生命周期拉链分页
     *
     * @param pageReqVO 分页查询
     * @return 门店生命周期拉链分页
     */
    PageResult<MemBranchLifecycleZipRespVO> getMemBranchLifecycleZipPage(MemBranchLifecycleZipPageReqVO pageReqVO);

    /**
     * 获取该门店下阶段生命周期提示信息
     * @param branchId
     * @return
     */
    String getNextBranchLifecycleMessage(Long branchId);

    /**
     * 根据门店ID获取生命周期配置信息
     * @param branchId
     * @return
     */
    BranchLifecycleSettingPolicyDTO getBranchLifecycleSetting(Long branchId);

    /**
     * 查询门店状态变更记录
     *
     * @param branchId
     * @return
     */
    List<MemBranchLifecycleZipRespVO> getBranchLifecycleStageList(Long branchId);

    /**
     * 刷新门店生命周期拉链
     * @param sysCode
     */
    void refreshBranchLifecycleZipHandler(Long sysCode);

    /**
     * 门店批量翻新
     * @param branchIdList
     */
    Set<Long> branchLifecycleBatchNew(List<Long> branchIdList);

    /**
     * 更新门店生命周期（注册、下单 更新生命周期入口）
     * @param branchId
     * @param operationType
     * @param orderId
     */
    public void updateBranchLifecycle(Long branchId,Integer operationType,Long orderId,Long afterId);

}
