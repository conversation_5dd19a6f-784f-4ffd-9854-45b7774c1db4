package com.zksr.member.controller.relation.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import java.util.List;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 业务员关系对象 mem_colonel_relation
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Data
@ApiModel("业务员关系 - mem_colonel_relation分页 Request VO")
public class MemColonelRelationSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelRelationId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 管理业务员ID */
    @Excel(name = "管理业务员ID")
    @ApiModelProperty(value = "管理业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long adminColonelId;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 删除状态  (0正常  2已删除) */
    @ApiModelProperty(value = "状态", example = "0")
    private String delFlag;

    @ApiModelProperty(value = "业务员ID集合")
    private List<Long> colonelIds;
}
