package com.zksr.member.controller.visitLog.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @Date 2024/5/10 10:02
 * 业务员拜访行程轨迹 - mem_colonel_visit_log Response VO
 */
@Data
@ApiModel("业务员拜访行程轨迹 - mem_colonel_visit_log Response VO")
public class MemColonelVisitLogTravelPathRespVO {

    private static final long serialVersionUID = 1L;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    private String colonelId;

    /** 门店ID */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    private String branchId;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "签到时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "签到时间")
    private Date signInDate;

    /** 签退时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "签退时间", width = 30, dateFormat = YYYY_MM_DD)
    private Date signOutDate;

    /** 销售金额 */
    @Excel(name = "销售金额")
    @ApiModelProperty(value = "销售金额")
    private BigDecimal salesAmt = BigDecimal.ZERO;

    /** 拜访间隔时间 */
    @Excel(name = "拜访间隔时间")
    @ApiModelProperty(value = "拜访间隔时间")
    private String visitIntervalTime;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /**在途时间 */
    @Excel(name = "在途时间")
    @ApiModelProperty(value = "在途时间")
    private String travelTime;

    /** 签到时间（时分秒） */
    @Excel(name = "签到时间（时分秒）", width = 30)
    @ApiModelProperty(value = "签到时间（时分秒）")
    private String signInDateHHMMSS;

    /** 签退时间（时分秒） */
    @Excel(name = "签退时间（时分秒）", width = 30)
    @ApiModelProperty(value = "签退时间（时分秒）")
    private String signOutDateHHMMSS;

    /** 门店经度 */
    @Excel(name = "门店经度")
    @ApiModelProperty(value = "门店经度")
    private BigDecimal branchLongitude;

    /** 门店纬度 */
    @Excel(name = "门店纬度")
    @ApiModelProperty(value = "门店纬度")
    private BigDecimal branchLatitude;

    /** 门头照 */
    @Excel(name = "门头照")
    @ApiModelProperty(value = "门头照")
    private String branchImages;

}
