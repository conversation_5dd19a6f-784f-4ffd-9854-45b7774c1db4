package com.zksr.member.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemDisplayPlan;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanPageReqVO;


/**
 * 陈列计划Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Mapper
public interface MemDisplayPlanMapper extends BaseMapperX<MemDisplayPlan> {
    default PageResult<MemDisplayPlan> selectPage(MemDisplayPlanPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemDisplayPlan>()
                    .eqIfPresent(MemDisplayPlan::getPlanId, reqVO.getPlanId())
                    .eqIfPresent(MemDisplayPlan::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemDisplayPlan::getDisplayTypeId, reqVO.getDisplayTypeId())
                    .eqIfPresent(MemDisplayPlan::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(MemDisplayPlan::getStartTime, reqVO.getStartTime())
                    .eqIfPresent(MemDisplayPlan::getEndTime, reqVO.getEndTime())
                    .eqIfPresent(MemDisplayPlan::getMemo, reqVO.getMemo())
                    .eqIfPresent(MemDisplayPlan::getAuditBy, reqVO.getAuditBy())
                    .eqIfPresent(MemDisplayPlan::getAuditTime, reqVO.getAuditTime())
                    .eqIfPresent(MemDisplayPlan::getAuditState, reqVO.getAuditState())
                    .eqIfPresent(MemDisplayPlan::getType, reqVO.getType())
                    .eqIfPresent(MemDisplayPlan::getTerminateReason, reqVO.getTerminateReason())
                    .eqIfPresent(MemDisplayPlan::getDelFlag, reqVO.getDelFlag())
                .orderByDesc(MemDisplayPlan::getPlanId));
    }
}
