package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * b2b openid 认证对象 mem_member_open_auth
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@TableName(value = "mem_member_open_auth")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemMemberOpenAuth extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long b2bAuthOpenId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 微信小程序openid */
    @Excel(name = "微信小程序openid")
    private String openid;

    /** 门店ID */
    @Excel(name = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 0-未授权, 1-已认证授权 */
    @Excel(name = "0-未授权, 1-已认证授权")
    private Integer authState;

    /** 小程序appid */
    @Excel(name = "小程序appid")
    private String appid;

}
