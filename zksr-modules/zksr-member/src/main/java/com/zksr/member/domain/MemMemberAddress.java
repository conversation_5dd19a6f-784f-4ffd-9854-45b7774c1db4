package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 用户地址对象 mem_member_address
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@TableName(value = "mem_member_address")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemMemberAddress extends BaseEntity {
    private static final long serialVersionUID=1L;

    /** ID主键 */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId) */
    @Excel(name = "三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long threeAreaCityId;

    /** 省份 */
    @Excel(name = "省份")
    private String provinceName;

    /** 城市 */
    @Excel(name = "城市")
    private String cityName;

    /** 区县 */
    @Excel(name = "区县")
    private String districtName;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 详细收货地址 */
    @Excel(name = "详细收货地址")
    private String deliveryAddress;

    /** 是否删除：0-否，1-是 */
    @TableLogic
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 备注 */
    private String remark;

}
