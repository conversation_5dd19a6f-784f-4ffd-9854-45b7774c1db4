package com.zksr.member.api.memberRegister;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.member.api.memberRegister.dto.MemberRegisterDTO;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterSaveReqVO;
import com.zksr.member.service.IMemMemberRegisterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Slf4j
@ApiIgnore
public class MemberRegisterApiImpl implements MemberRegisterApi {

    @Autowired
    private IMemMemberRegisterService memMemberRegisterService;

    @Override
    @InnerAuth
    public CommonResult<MemberRegisterDTO> getMemberRegisterById(Long memberRegisterId) {
        return success(HutoolBeanUtils.toBean(memMemberRegisterService.getMemberRegisterById(memberRegisterId),MemberRegisterDTO.class));
    }

    @Override
    @InnerAuth
    public CommonResult<Boolean> insertUserMemberRegister(MemberRegisterDTO memberRegisterDTO) {
        return success(memMemberRegisterService.insertUserMemberRegister(HutoolBeanUtils.toBean(memberRegisterDTO, MemMemberRegisterSaveReqVO.class)));
    }

    @Override
    public CommonResult<MemberRegisterDTO> getMemberRegisterByUserName(Long sysCode, String userName) {
        return success(memMemberRegisterService.getMemberRegisterByUserName(sysCode, userName));
    }
}
