package com.zksr.member.api.loginHis;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.loginHis.vo.MemLoginHisVO;
import com.zksr.member.controller.loginHis.vo.MemLoginHisSaveReqVO;
import com.zksr.member.service.IMemLoginHisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @Date 2024/12/19 14:59
 * @登录日志rpc 接口实现类
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class LoginHisApiImpl implements LoginHisApi {

    @Autowired
    private IMemLoginHisService memLoginHisService;

    @Override
    public CommonResult<Long> addLoginHis(MemLoginHisVO createReqVO) {
        return CommonResult.success(memLoginHisService.insertMemLoginHis(HutoolBeanUtils.toBean(createReqVO, MemLoginHisSaveReqVO.class)));
    }
}
