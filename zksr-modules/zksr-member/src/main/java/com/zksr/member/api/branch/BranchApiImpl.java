package com.zksr.member.api.branch;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LFUCache;
import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncBranchCallDTO;
import com.zksr.common.core.enums.branch.BranchLifecycleTypeEnum;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.BranchSupplierDTO;
import com.zksr.member.api.branch.dto.ColonelBranchCountDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.branch.excel.MemBranchImportExcel;
import com.zksr.member.api.branch.form.BranchImportForm;
import com.zksr.member.api.branch.vo.BranchListForReqVO;
import com.zksr.member.api.branch.vo.MemberBranchReqVO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.controller.branch.vo.MemBranchPageReqVO;
import com.zksr.member.convert.branch.BranchConvert;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.domain.MemBranchRegister;
import com.zksr.member.domain.MemMemberRegister;
import com.zksr.member.mapper.MemBranchMapper;
import com.zksr.member.mq.MemberAppMqProducer;
import com.zksr.member.service.*;
import com.zksr.member.service.IMemBranchLifecycleZipService;
import com.zksr.member.service.IMemBranchService;
import com.zksr.member.service.IMemColonelBranchZipService;
import com.zksr.member.service.IMemberCacheService;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.trade.api.orderSettle.vo.BranchStatementExportVo;
import com.zksr.trade.api.orderSettle.vo.BranchStatementPageVo;
import com.zksr.report.api.homePages.dto.HomePagesBranchDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Slf4j
@ApiIgnore
public class BranchApiImpl implements BranchApi {

    @Autowired
    private IMemBranchService memBranchService;

    @Autowired
    private MemberAppMqProducer appMqProducer;

    @Autowired
    private MemBranchMapper branchMapper;

    @Autowired
    private IMemberCacheService memberCacheService;

    @Resource
    private AreaCityApi areaCityApi;

    @Resource
    private PartnerApi partnerApi;
    @Autowired
    private IMemColonelBranchZipService memColonelBranchZipService;
    @Autowired
    private IMemBranchRegisterService memBranchRegisterService;
    @Autowired
    private IMemMemberRegisterService memMemberRegisterService;

    @Autowired
    private IMemBranchLifecycleZipService memBranchLifecycleZipService;
    @InnerAuth
    @Override
    public CommonResult<BranchDTO> getByBranchId(Long branchId) {
        MemBranch membranch = memBranchService.getMemBranch(branchId);
        BranchDTO branchDTO = HutoolBeanUtils.toBean(membranch, BranchDTO.class);
         if (Objects.nonNull(branchDTO) && Objects.nonNull(branchDTO.getThreeAreaCityId())) {
            SysAreaCityRespVO three = areaCityApi.getById(branchDTO.getThreeAreaCityId()).getCheckedData();
            if (Objects.nonNull(three)) {
                SysAreaCityRespVO second = areaCityApi.getById(three.getPid()).getCheckedData();
                SysAreaCityRespVO first = areaCityApi.getById(second.getPid()).getCheckedData();
                branchDTO.setSecondAreaCityId(second.getAreaCityId());
                branchDTO.setFirstAreaCityId(first.getAreaCityId());
            }
        }
        System.out.println(branchDTO);
        return success(branchDTO);
    }

    /**
    * @Description: 门店绑定入驻商
    * @Author: liuxingyu
    * @Date: 2024/3/28 16:46
    */
    @Override
    @InnerAuth
    public void bindSupplier(BranchSupplierDTO branchSupplierDTO) {
        memBranchService.bindSupplier(branchSupplierDTO);
    }

    /**
    * @Description: 根据门店编号获取入驻商
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:09
    */
    @Override
    @InnerAuth
    public CommonResult<List<Long>> getSupplierByBranchId(Long branchId) {
        return CommonResult.success(memBranchService.getSupplierByBranchId(branchId));
    }

    /**
    * @Description: 根据入驻商ID获取门店ID
    * @Author: liuxingyu
    * @Date: 2024/3/28 18:15
    */
    @Override
    @InnerAuth
    public CommonResult<List<Long>> getBySupplier(Long supplierId) {
        return CommonResult.success(memBranchService.getBySupplier(supplierId));
    }

    @Override
    @InnerAuth
    public CommonResult<BranchDTO> getDefaultBranchByMemberId(@RequestParam("memberId") Long memberId,@RequestParam("sysCode")Long sysCode) {
        return CommonResult.success(HutoolBeanUtils.toBean(memBranchService.getDefaultBranchByMemberId(memberId,sysCode),BranchDTO.class));
    }

    @Override
    @InnerAuth
    public CommonResult<List<BranchDTO>> getBranchListByMemberId(MemberBranchReqVO memberBranchReqVO) {
        return CommonResult.success(memBranchService.getBranchListByMemberId(memberBranchReqVO));
    }

    @Override
    @InnerAuth
    public CommonResult<List<BranchDTO>> getUnauditedBranchListByMemberId(MemberBranchReqVO memberBranchReqVO) {
        return CommonResult.success(memBranchService.getUnauditedBranchListByMemberId(memberBranchReqVO));
    }

    @Override
    public CommonResult<List<BranchDTO>> getAllExpirationDateBranchList(Long sysCode) {
        return CommonResult.success(memBranchService.getAllExpirationDateBranchList(sysCode));
    }

    @Override
    public CommonResult<Boolean> updateStatusByBranchIds(BranchDTO branchDTO) {
        memBranchService.updateStatusByBranchIds(branchDTO);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> updateLastLoginTimeByApi(@RequestParam("sysCode")Long sysCode, @RequestParam("branchId")Long branchId) {
        memBranchService.updateLastLoginTime(sysCode,branchId);
        return success(true);
    }

    /**
    * @Description: 入驻商绑定门店
    * @Author: liuxingyu
    * @Date: 2024/5/15 11:00
    */
    @Override
    public CommonResult<Integer> bindBranch(BranchSupplierDTO branchSupplierDTO) {
        return success(memBranchService.bindBranch(branchSupplierDTO));
    }

    @Override
    public CommonResult<List<Long>> getAllBranchIdList(Long minBranchId) {
        return CommonResult.success(memBranchService.getAllBranchIdList(minBranchId));
    }

    @Override
    public CommonResult<Boolean> updateWechatMerchantAuthOpenid(Long branchId, String openid) {
        memBranchService.updateWechatMerchantAuthOpenid(branchId, openid);
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 修改门店信息
     *
     * @param updateReqVO
     * @return
     */
    @Override
    public CommonResult<Boolean> edit(MemBranchSaveReqVO updateReqVO) {
        memBranchService.updateMemBranch(updateReqVO);
        memBranchService.reloadBranchDTOCache(updateReqVO.getBranchId());
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 修改门店信息的首单相关信息
     *
     * @param branchDTO
     * @return
     */
    @Override
    public CommonResult<Boolean> updateFirstOrder(BranchDTO branchDTO) {
        memBranchService.updateFirstOrder(branchDTO);
        memBranchService.reloadBranchDTOCache(branchDTO.getBranchId());
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 新增门店信息
     *
     * @param createReqVO
     * @return
     */
    @Override
    public CommonResult<Long> add(MemBranchSaveReqVO createReqVO) {
        return CommonResult.success(memBranchService.insertMemBranch(createReqVO));

    }

    @Override
    public CommonResult<Boolean> refreshEsBranchSalesInfoJobHandler(Long sysCode) {
        memBranchService.refreshEsBranchSalesInfoJobHandler(sysCode);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> initEsBranchSalesInfoHandler(Long sysCode, String startDate, String endDate) {
        memBranchService.initEsBranchSalesInfoJobHandler(sysCode, startDate, endDate);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> refreshEsBranchBase(Long branchId) {
        appMqProducer.sendEsColonelAppBranchBaseEvent(branchId);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<PageResult<SyncBranchCallDTO>> getBranchDataPage(Integer pageNum, Integer pageSize) {
        return CommonResult.success(memBranchService.getBranchDataPage(pageNum, pageSize));
    }

    @Override
    public CommonResult<List<BranchDTO>> getBranchListForBy(BranchListForReqVO reqVO) {
        return success(BranchConvert.INSTANCE.convertDTOList(branchMapper.selectListFor(reqVO)));
    }

    @Override
    public CommonResult<Long> selectBranchCountByColonel(Long colonelId) {
        return success(branchMapper.selectBranchCountByColonel(colonelId));
    }

    @Override
    public CommonResult<List<ColonelBranchCountDTO>> getBranchCountFromOtherSource(List<String> colonelIds, String currentMonthId) {
        return success(branchMapper.getBranchCountFromOtherSource(colonelIds, currentMonthId));
    }

    @Override
    public CommonResult<Map<Long, BranchDTO>> listByBranchIds(List<Long> branchIds) {
        return CommonResult.success(memBranchService.listByBranchIds(branchIds));
    }

    @Override
    public CommonResult<List<BranchStatementExportVo>> getBranchStatementPage(BranchStatementPageVo pageVO) {
        MemBranchPageReqVO pageReqVO = HutoolBeanUtils.toBean(pageVO, MemBranchPageReqVO.class);
        PageResult<MemBranch> pageResult = memBranchService.getMemBranchPage(pageReqVO);
        PageResult<BranchStatementExportVo> branchRespVOPageResult = HutoolBeanUtils.toBean(pageResult, BranchStatementExportVo.class);

        LFUCache<Long, ColonelDTO> colonelCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, ChannelDTO> channelCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, AreaDTO> areaCache = CacheUtil.newLFUCache(0);

        branchRespVOPageResult.getList().forEach(branchRespVO -> {
            if (ToolUtil.isNotEmpty(branchRespVO.getColonelId())) {
                ColonelDTO colonel = colonelCache.get(branchRespVO.getColonelId(), () -> memberCacheService.getColonel(branchRespVO.getColonelId()));
                if (ToolUtil.isNotEmpty(colonel)) {
                    branchRespVO.setColonelName(colonel.getColonelName());
                }
            }
            // 渠道
            ChannelDTO channelDto = Objects.nonNull(branchRespVO.getChannelId()) ? channelCache.get(branchRespVO.getChannelId(), () -> memberCacheService.getChannelDto(branchRespVO.getChannelId())) : null;
            if (Objects.nonNull(channelDto)) {
                branchRespVO.setChannelName(channelDto.getChannelName());
            }

            // 区域
            AreaDTO areaDTO = Objects.nonNull(branchRespVO.getAreaId()) ? areaCache.get(branchRespVO.getAreaId(), () -> memberCacheService.getAreaDto(branchRespVO.getAreaId())) : null;
            if (Objects.nonNull(areaDTO)) {
                branchRespVO.setAreaName(areaDTO.getAreaName());
            }
            // 平台商名称
            PartnerDto partnerDto = partnerApi.getBySysCode(branchRespVO.getSysCode()).getData();
            if (Objects.nonNull(partnerDto)){
                branchRespVO.setSysName(partnerDto.getPartnerName());
            }
            // 状态显示
            if (Objects.nonNull(branchRespVO.getStatus()) && "1".equals(branchRespVO.getStatus())){
                branchRespVO.setStatus("启用");
            }else {
                branchRespVO.setStatus("禁用");
            }
            if (Objects.nonNull(branchRespVO.getAuditState()) && branchRespVO.getAuditState() == 1){
                branchRespVO.setAuditStateName("已审核");
            }else {
                branchRespVO.setAuditStateName("未审核");
            }

            MemMemberRegister memberRegister = memMemberRegisterService.getMemberRegisterByBranchId(branchRespVO.getBranchId());
            if (Objects.nonNull(memberRegister)){
                branchRespVO.setRegisterTime(memberRegister.getCreateTime());
                branchRespVO.setRegisterTypeName("自主注册");
            }
            MemBranchRegister memBranchRegister = memBranchRegisterService.getMemBranchRegisterByBranchId(branchRespVO.getBranchId());
            if (Objects.nonNull(memBranchRegister)){
                branchRespVO.setRegisterTime(memBranchRegister.getCreateTime());
                branchRespVO.setRegisterTypeName("业务员拓店");
            }
            if (Objects.isNull(memberRegister) && Objects.isNull(memBranchRegister)){
                BranchDTO branchDto = memberCacheService.getBranchDto(branchRespVO.getBranchId());
                branchRespVO.setRegisterTime(branchDto.getCreateTime());
                branchRespVO.setRegisterTypeName("后台导入");
            }
            if (NumberUtil.isInteger(branchRespVO.getLifecycleStage())) {
                Integer lifecycleStage = Integer.valueOf(branchRespVO.getLifecycleStage());
                branchRespVO.setLifecycleStage(BranchLifecycleTypeEnum.getNameByType(lifecycleStage));
            }
        });
        return success(branchRespVOPageResult.getList());
    }

    @Override
    public CommonResult<List<HomePagesBranchDataRespDTO>> getHomePagesBranchData(HomePagesReqVO reqVO) {
        return CommonResult.success(memBranchService.getHomePagesBranchData(reqVO));
    }

    @Override
    public CommonResult<List<BranchDTO>> getBranchListByArea(Long dcAreaId) {
        return CommonResult.success(memBranchService.getbranchListByArea(dcAreaId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> editSeasTime(MemBranchSaveReqVO updateReqVO) {
        MemBranch memBranch = new MemBranch();
        memBranch.setSysCode(updateReqVO.getSysCode());
        memBranch.setBranchId(updateReqVO.getBranchId());
        memColonelBranchZipService.updateMemColonelBranchZip(updateReqVO,memBranch); // 删除门店业务员拉链表数据

        updateReqVO.setColonelId(null);
        memBranchService.updateMemBranchSeasTime(updateReqVO);
        memBranchService.reloadBranchDTOCache(updateReqVO.getBranchId());

        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public void deleteBranchById(Long branchId) {
        memberCacheService.deleteBranchById(branchId);
    }

    @Override
    public CommonResult<List<Long>> getAllBranchIdListBySysCode(Long sysCode) {
        return success(memBranchService.getAllBranchIdListBySysCode(sysCode));
    }

    public CommonResult<String> importDataEvent(BranchImportForm form){
        return success(JsonUtils.toJsonString(memBranchService.impordDataEvent(form.getBranchList(),form.getFileImportId(),form.getDcId(),form.getSysCode(), form.getSeq())));
    }

    @Override
    public CommonResult<List<BranchDTO>> getAreaIdExistBranch(Long orderNo, Long sysCode) {
        return success(BeanUtils.toBean(memBranchService.getAreaIdExistBranch(orderNo,sysCode), BranchDTO.class));
    }

    @Override
    public CommonResult<List<Long>> getBranchIdListByBranchName(String branchName, Long sysCode) {
        return success(memBranchService.getBranchIdListByBranchName(branchName,sysCode));
    }

    @Override
    public CommonResult<Boolean> refreshBranchLifecycleZipHandler(Long sysCode) {
        memBranchLifecycleZipService.refreshBranchLifecycleZipHandler(sysCode);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> updateBranchLifecycle(Long branchId, Integer operationType, Long orderId,Long afterId) {
        memBranchLifecycleZipService.updateBranchLifecycle(branchId,operationType,orderId,afterId);
        return success(true);
    }

    @Override
    public CommonResult<List<BranchDTO>> getBranchListByChannelId(Long channelId, Long sysCode) {
        return success(BeanUtils.toBean(memBranchService.getMemBranchByChannelId(channelId,sysCode), BranchDTO.class));
    }
}
