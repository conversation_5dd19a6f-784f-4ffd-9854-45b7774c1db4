package com.zksr.member.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.command.dto.CommandAddOrderRespDTO;
import com.zksr.member.api.command.vo.CommandAddOrderVO;
import com.zksr.member.api.command.vo.CommandPageReqVO;
import com.zksr.member.api.command.vo.CommandSaveVO;

/**
 * 操作指令Service接口
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
public interface IMemCommandService {

    /**
     * 指令处理
     * @param data
     */
    String addOrderCommandHandle(CommandAddOrderVO data);

    /**
     * 分页查询操作指令
     * @param pageReqVO
     * @return
     */
    PageResult<CommandAddOrderRespDTO> getMemCommandPage(CommandPageReqVO pageReqVO);

    /**
     * 移除加单锚点指令
     * @param anchorCommandId
     * @return
     */
    Boolean removeAnchorCommand(Long anchorCommandId);


}
