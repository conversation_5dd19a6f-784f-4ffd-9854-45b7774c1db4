package com.zksr.member.service;

import com.zksr.common.core.domain.R;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.controller.visitLog.vo.*;
import com.zksr.member.domain.MemColonelVisitLog;

import javax.validation.Valid;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * 业务员拜访日志Service接口
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
public interface IMemColonelVisitLogService {

    /**
     * 新增业务员拜访日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemColonelVisitLog(@Valid MemColonelVisitLogSaveReqVO createReqVO);

    R<Long> signInInner(@Valid MemColonelVisitLogSaveReqVO createReqVO);

    R<Boolean> signOutInner(@Valid MemColonelVisitLogSaveReqVO wmsVisitConsumerLogDto);

    /**
     * 放弃拜访
     *
     * @param consumerNo 客户编号
     * @return bool
     */
    R<Boolean> waiveInner(Long colonelId, Long consumerNo);


    R<String> signInOrOutFlagInner(Long colonelId, Long consumerNo);

    /**
     * 修改业务员拜访日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemColonelVisitLog(@Valid MemColonelVisitLogSaveReqVO updateReqVO);

    /**
     * 删除业务员拜访日志
     *
     * @param visitId 主键ID
     */
    public void deleteMemColonelVisitLog(Long visitId);

    /**
     * 批量删除业务员拜访日志
     *
     * @param visitIds 需要删除的业务员拜访日志主键集合
     * @return 结果
     */
    public void deleteMemColonelVisitLogByVisitIds(Long[] visitIds);

    /**
     * 获得业务员拜访日志
     *
     * @param visitId 主键ID
     * @return 业务员拜访日志
     */
    public MemColonelVisitLog getMemColonelVisitLog(Long visitId);

    /**
     * 获得业务员拜访日志分页
     *
     * @param pageReqVO 分页查询
     * @return 业务员拜访日志分页
     */
    PageResult<MemColonelVisitLogRespVO> getMemColonelVisitLogPage(MemColonelVisitLogPageReqVO pageReqVO);

    /**
     * 获得业务员拜访汇总分页
     *
     * @param pageReqVO 分页查询
     * @return 获得业务员拜访汇总分页
     */
    PageResult<MemColonelVisitLogCollectRespVO> getColonelVisitLogCollectList(MemColonelVisitLogPageReqVO pageReqVO);

    /**
     * 获得业务员拜访汇总分页
     *
     * @param startTime,endTime
     * @return 获得业务员拜访汇总分页
     */
    List<MemColonelVisitLog> getLatestRecordsForEachColonel(String startTime,String endTime);

    /**
     * 拜访日志下载选中的图片
     *
     * @param pageReqVO
     * @return 拜访日志下载选中的图片
     */
    byte[] getVisitLogImageZip(MemColonelVisitLogPageReqVO pageReqVO) throws IOException;

    /**
     * 分页查询业务员拜访行程信息
     *
     * @param pageReqVO
     * @return 分页查询业务员拜访行程信息
     */
    List<MemColonelVisitLogTravelPathRespVO> getColonelVisitLogTravelPath(MemColonelVisitLogPageReqVO pageReqVO);


    /**
     * 根据时间区间获取某业务员的拜访信息
     * @param colonelId
     * @param startTime
     * @param endTime
     * @return
     */
    List<MemColonelVisitLog> getLogListByColonelIdAndDate(Long colonelId,Date startTime,Date endTime);


    /**
     * 根据门店ID获取距离上次拜访的天!
     * @param branchId
     * @return
     */
    long getDaysSinceLastVisitByBranchId(Long branchId);

}
