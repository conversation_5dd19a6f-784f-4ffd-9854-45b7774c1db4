package com.zksr.member.controller.branch.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("门店批量编辑 请求实体")
public class BranchBatchEditVO {
    @ApiModelProperty(value = "门店id集合")
    private List<Long> branchIds;

    @ApiModelProperty(value = "区域ID")
    private Long areaId;

    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    @ApiModelProperty(value = "渠道id")
    private Long channelId;

    @ApiModelProperty(value = "价格码")
    private Long salePriceCode;
}
