package com.zksr.member.api.b2bAuth;

import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.b2bAuth.dto.MemMemberOpenAuthDTO;
import com.zksr.member.convert.memberB2bAuth.MemMemberOpenAuthConvert;
import com.zksr.member.service.IMemMemberOpenAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信b2b门店认证授权
 * @date 2024/8/15 18:29
 */
@RestController
@ApiIgnore
public class B2bAuthApiImpl implements B2bAuthApi{

    @Autowired
    private IMemMemberOpenAuthService memMemberOpenAuthService;

    @Override
    public CommonResult<MemMemberOpenAuthDTO> getBind(String appid, String openid, Long branchId) {
        return success(MemMemberOpenAuthConvert.INSTANCE.convertDTO(memMemberOpenAuthService.getBind(appid, openid, branchId)));
    }

    @Override
    public void updateAuth(MemMemberOpenAuthDTO authDTO) {
        if (StringUtils.isEmpty(authDTO.getOpenid())) {
            return;
        }
        memMemberOpenAuthService.updateAuth(authDTO);
    }
}
