package com.zksr.member.service;

import com.zksr.account.api.account.vo.ColonelAccountRespVO;
import com.zksr.account.api.withdraw.dto.SaveWithdrawDTO;
import com.zksr.account.api.withdraw.dto.WithdrawDTO;
import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.member.api.colonel.vo.MemShopAppRecodeReqVO;
import com.zksr.member.api.colonelApp.dto.PageDataDTO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyPageReqVO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidySaveReqVO;
import com.zksr.member.controller.branch.vo.MemBranchPageReqVO;
import com.zksr.member.controller.branch.vo.MemBranchRespVO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterSaveReqVO;
import com.zksr.member.controller.colonelApp.dto.MemColonelSendCouponDTO;
import com.zksr.member.controller.colonelApp.vo.*;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogRespVO;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogSaveReqVO;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.domain.MemColonelTidy;
import com.zksr.promotion.api.coupon.dto.ColonelQuotaDTO;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.api.coupon.dto.CouponReceiveResultDTO;
import com.zksr.promotion.api.coupon.vo.ColonelAppPrmCouponTemplatePageReqVO;
import com.zksr.promotion.api.coupon.vo.ColonelAppPrmCouponTemplateRespVO;
import com.zksr.promotion.api.coupon.vo.CouponPageReqVO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.trade.api.order.dto.ColonelAppOrderListTotalDTO;
import com.zksr.trade.api.order.vo.TrdColonelAppOrderDetailRespVO;
import com.zksr.trade.api.order.vo.TrdColonelAppOrderListPageReqVO;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/16 14:37
 * 业务员APP拓店Service接口
 */
public interface IMemColonelAppService {

    /**
     * 业务员app新增门店
     *
     * @param createReqVO 创建信息
     */
    public void addBranchRegister(@Valid MemBranchRegisterSaveReqVO createReqVO);

    /**
     * 扩店校验手机号
     * @param contactPhone
     * @return
     */
    public boolean checkAddBranchPhone(String contactPhone);

    /**
     * 业务员app修改门店
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void editColoneBranch(@Valid MemColonelAppSaveBranchReqVo updateReqVO);

    /**
     * 获取区域城市接口
     * @param
     * @return
     */
    public List<AreaDTO> getcityList();


    /**
     * 业务员app客户列表
     *
     * @param pageReqVO
     * @return 结果
     */
    public PageResult<MemColonelAppCustomerListRespVO>  getMemColonelAppCustomerPage(@Valid MemColonelAppCustomerListPageReqVO pageReqVO);

    /**
     * 业务员app--业务员统计列表
     *
     * @param areaId,colonelId
     * @return 结果
     */
    PageResult<MemColonelStatisticsListRespVo> getColonelStatisticsList(@Valid MemColonelStatisticsListReqVo reqVo);

        /**
         * 获取业务员信息
         * @return
         */
    ColonelDTO getColonel();

    /**
     * 获取选中业务员信息详情
     * @return
     */
    ColonelDTO getColonel(Long colonelId);

    /**
     * 业务员app订单和售后列表
     *
     * @param pageReqVO
     * @return 结果
     */
    public ColonelAppOrderListTotalDTO getMemColonelOrderListPage(@Valid TrdColonelAppOrderListPageReqVO pageReqVO);

    /**
     * 业务员app订单和售后详情
     *
     * @param orderId
     * @return 结果
     */
    public List<TrdColonelAppOrderDetailRespVO> getOrderDetail(@Valid Long orderId, @Valid String orderType);

    /**
     * 获取老板购物车
     *
     * @param branchId
     * @return 结果
     */
    public List<ColonelAppTrdCarRespVO> getBranchShoppingCart(@Valid Long branchId);

    /**
     * 获取业务员账户
     * @param colonelId
     * @return
     */
    ColonelAccountRespVO getColonelAccount(Long colonelId);

    /**
     * 保存业务员提现单
     * @param createReqVO
     * @return
     */
    Long saveWithdraw(SaveWithdrawDTO createReqVO);

    /**
     * 获取提现单详情
     * @param withdrawId
     * @return
     */
    WithdrawDTO getWithdrawInfo(Long withdrawId);

    /**
     * 获取提现单分页数据
     * @param pageReqVO
     * @return
     */
    PageResult<WithdrawDTO> getAccWithdrawPage(AccWithdrawPageReqVO pageReqVO);

    /**
     * 获取门店详情
     * @param branchId
     * @return
     */
    public MemBranchRespVO getColonelBranchDetail(@Valid Long branchId);

    /**
     * 新增业务员拜访日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemColonelVisitLog(@Valid MemColonelVisitLogSaveReqVO createReqVO);

    /**
     * 修改业务员拜访日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemColonelVisitLog(@Valid MemColonelVisitLogSaveReqVO updateReqVO);

    /**
     * 删除业务员拜访日志
     *
     * @param visitIds 主键ID
     */
    public void deleteMemColonelVisitLogByVisitIds(Long[] visitIds);

    /**
     * 新增业务员理货记录
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemColonelTidy(@Valid MemColonelTidySaveReqVO createReqVO);

    /**
     * 获得业务员理货记录
     *
     * @param colonelTidyId 业务员理货记录id
     * @return 业务员理货记录
     */
    public MemColonelTidy getMemColonelTidy(Long colonelTidyId);

    /**
     * 获得业务员理货记录分页
     *
     * @param pageReqVO 分页查询
     * @return 业务员理货记录分页
     */
    PageResult<MemColonelTidy> getMemColonelTidyPage(MemColonelTidyPageReqVO pageReqVO);

    /**
     * 业务员首页统计信息
     */
    PageDataDTO getPageData();


    /**
     * 业务员app拜访列表
     *
     * @param pageReqVO
     * @return 结果
     */
    public PageResult<MemColonelAppCustomerListRespVO>  getCustomerVisitListPage(@Valid MemColonelAppCustomerListPageReqVO pageReqVO);


    /**
     * 业务员app拜访详情列表
     *
     * @param pageReqVO
     * @return 结果
     */
    public PageResult<MemColonelVisitLogRespVO>  getCustomerVisitDetailListPage(@Valid MemColonelAppCustomerListPageReqVO pageReqVO);

    /**
     * 获得门店信息分页
     *
     * @param pageReqVO 分页查询
     * @return 门店信息分页
     */
    PageResult<MemBranch> getMemBranchPage(MemBranchPageReqVO pageReqVO);

    /**
     * 查询我管理的业务员
     */
    PageResult<ColonelDTO> getManagementClerkList(MemColonelPageReqVO pageReqVO);

    /**
     * 获取下级业务员列表
     */
    List<ColonelDTO> getColonelJuniorMember(Long colonelId);

    /**
     * 查询我发展的业务员
     * @return
     */
    PageResult<ColonelDTO> getDevelopmentSalesmanList(MemColonelPageReqVO pageReqVO);

    /**
     * 新增业务员
     * @param createReqVO
     * @return
     */
    Long addColonel(MemColonelSaveReqVO createReqVO);

    void updateAvatarInfo(String url);

    /**
     * 获取业务员更新配置
     * @return
     */
    ColonelAppUpdateInfoVO getUpdateInfo();

    /**
     * 获取商城小程序二维码
     * @param reqVO 请求
     * @return base64 字符
     */
    String getShopAppRecode(MemShopAppRecodeReqVO reqVO);

//    ColonelBindOpenidRespVO getWxB2bOpenidBindStatus();

    /**
     * 客户-查看优惠券接口
     * @param receiveReq
     * @return
     */
    PageResult<CouponDTO> getBranchCouponList(CouponPageReqVO receiveReq);

    /**
     * 客户-查看业务员发券额度
     * @param colonelId
     * @return
     */
    ColonelQuotaDTO getColonelQuota(Long colonelId);

    /**
     * 客户-查看业务员可发优惠券
     * @return
     */
    PageResult<ColonelAppPrmCouponTemplateRespVO> getAvailableCouponsForSalesperson(ColonelAppPrmCouponTemplatePageReqVO pageReqVO);

    /**
     * 发券管理-去发券（查询门店信息）
     * @param couponTemplateId
     * @return
     */
    List<MemBranch> getStoresForSalesperson(Long couponTemplateId,String branchName);

    /**
     * 发券管理-查询发券记录
     * @param couponTemplateId
     * @return
     */
    List<MemBranch> getCouponReceivedBranchs(Long couponTemplateId,String branchName);

    /**
     * 业务员发放优惠券
     */
    List<CouponReceiveResultDTO> sendCouponsFromSalesperson(MemColonelSendCouponDTO pageReqVO);

    /**
     * 获取业务员appid绑定状态
     * @return
     */
    ColonelBindAppletOpenidRespVO getWxB2bOpenidBindStatus();

    /**
     * 获取业务员公众号绑定key
     */
    ColonelBindPublishOpenidRespVO getPublishOpenidBindKey();

    /**
     * 门店拜访历史记录
     */
    List<MemColonelVisitLogRespVO> branchVisitDetailList(MemColonelAppCustomerListPageReqVO pageReqVO);


    /**
     * 获取当前业务员管理的门店（门店区域必须与业务员区域一致，并且无需查询下级业务员门店）
     */
    PageResult<MemBranch> getBranchPage(MemBranchPageReqVO pageReqVO);
}
