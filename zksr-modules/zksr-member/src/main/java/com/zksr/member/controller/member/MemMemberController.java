package com.zksr.member.controller.member;

import javax.validation.Valid;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.member.dto.MemMemberSaveReqVO;
import com.zksr.member.api.member.dto.MemMemberUpdatePasswordReqVO;
import com.zksr.member.domain.MemBranchUser;
import com.zksr.member.service.IMemBranchUserService;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemMember;
import com.zksr.member.service.IMemMemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.api.member.vo.MemMemberPageReqVO;
import com.zksr.member.api.member.vo.MemMemberRespVO;

import java.util.List;
import java.util.stream.Collectors;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 用户信息Controller
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@Api(tags = "管理后台 - 用户信息接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/member")
public class MemMemberController {
    @Autowired
    private IMemMemberService memMemberService;
    @Autowired
    private IMemBranchUserService memBranchUserService;

    /**
     * 新增用户信息
     */
    @ApiOperation(value = "新增用户信息", httpMethod = "POST", notes = Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "用户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemMemberSaveReqVO createReqVO) {
        return success(memMemberService.insertMemMember(createReqVO));
    }

    /**
     * 修改用户信息
     */
    @ApiOperation(value = "修改用户信息", httpMethod = "PUT", notes = Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "用户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemMemberSaveReqVO updateReqVO) {
            memMemberService.updateMemMember(updateReqVO);
        return success(true);
    }

    /**
     * 修改用户信息
     */
    @ApiOperation(value = "App修改用户信息", httpMethod = "PUT", notes = Permissions.EDIT)
    @Log(title = "用户信息", businessType = BusinessType.UPDATE)
    @PutMapping("/appUserEdit")
    public CommonResult<Boolean> appUserEdit(@Valid @RequestBody MemMemberSaveReqVO updateReqVO) {
        memMemberService.updateMemMember(updateReqVO);
        return success(true);
    }
    /**
     * 删除用户信息
     */
    @ApiOperation(value = "删除用户信息", httpMethod = "POST", notes = Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "用户信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public CommonResult<Boolean> remove(@RequestBody Long[] memberIds) {
        memMemberService.deleteMemMemberByMemberIds(memberIds);
        return success(true);
    }

    /**
     * 获取用户信息详细信息
     */
    @ApiOperation(value = "获得用户信息详情", httpMethod = "GET", notes = Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{memberId}")
    public CommonResult<MemMemberRespVO> getInfo(@PathVariable("memberId") Long memberId) {
        MemMember memMember = memMemberService.getMemMember(memberId);
        MemMemberRespVO respVo = HutoolBeanUtils.toBean(memMember, MemMemberRespVO.class);
        List<MemBranchUser> memBranchUsers = memBranchUserService.getMemBranchUserByMemberId(memberId);
        respVo.setBranchIds(memBranchUsers.stream().map(key->{ return key.getBranchId().toString(); }).collect(Collectors.toList()));
        return success(respVo);
    }

    /**
     * 分页查询用户信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得用户信息分页列表", httpMethod = "GET", notes = Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemMemberRespVO>> getPage(@Valid MemMemberPageReqVO pageReqVO) {
        PageResult<MemMemberRespVO> pageResult = memMemberService.getMemMemberPage(pageReqVO);
        return success(pageResult);
    }

    /**
     * 停用用户
     */
    @ApiOperation(value = "停用用户", httpMethod = HttpMethod.PUT, notes = Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "停用用户", businessType = BusinessType.UPDATE)
    @PutMapping("/disable/{memberId}")
    public CommonResult<Boolean> disable(@ApiParam(name = "memberId", value = "用户ID", required = true) @PathVariable("memberId") Long memberId) {
        memMemberService.disable(memberId);
        return success(true);
    }

    /**
     * 启用用户
     */
    @ApiOperation(value = "启用用户", httpMethod = HttpMethod.PUT, notes = Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "启用用户", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{memberId}")
    public CommonResult<Boolean> enable(@ApiParam(name = "memberId", value = "用户ID", required = true) @PathVariable("memberId") Long memberId) {
        memMemberService.enable(memberId);
        return success(true);
    }


    /**
     * 修改用户密码信息
     */
    @ApiOperation(value = "修改门店用户密码信息", httpMethod = "PUT", notes = "member:member:edit-password")
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "用户信息", businessType = BusinessType.UPDATE)
    @PutMapping("/editMemberPassword")
    public CommonResult<Boolean> editMemberPassword(@Valid @RequestBody MemMemberUpdatePasswordReqVO updateReqVO) {
        memMemberService.updateMemMemberPassword(BeanUtils.toBean(updateReqVO, MemMemberSaveReqVO.class));
        return success(true);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:member:add";
        /** 编辑 */
        public static final String EDIT = "member:member:edit";
        /** 删除 */
        public static final String DELETE = "member:member:remove";
        /** 列表 */
        public static final String LIST = "member:member:list";
        /** 查询 */
        public static final String GET = "member:member:query";
        /** 停用 */
        public static final String DISABLE = "member:member:disable";
        /** 启用 */
        public static final String ENABLE = "member:member:enable";
        /** 审核 */
        public static final String AUDIT = "member:member:audit";
    }

}
