package com.zksr.member.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.Page;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.CommandLevelTypeEnum;
import com.zksr.common.core.enums.CommandMerchantTypeEnum;
import com.zksr.common.core.enums.CommandTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.command.dto.CommandAddOrderExecDTO;
import com.zksr.member.api.command.dto.CommandAddOrderRespDTO;
import com.zksr.member.api.command.vo.*;
import com.zksr.member.convert.command.MemCommandConvert;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.mapper.MemBranchMapper;
import com.zksr.member.service.IMemberCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemCommandMapper;
import com.zksr.member.domain.MemCommand;
import com.zksr.member.service.IMemCommandService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.account.enums.ErrorCodeConstants.BALANCE_ERR;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.AppErrorCodeConstants.APP_COLONEL_ANCHOR_COMMAND_NOT_EXISTS;
import static com.zksr.member.enums.AppErrorCodeConstants.APP_COLONEL_ORDINARY_COMMAND_PERFORM_EXISTS;

/**
 * 操作指令Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@Slf4j
@Service
public class MemCommandServiceImpl implements IMemCommandService {
    @Autowired
    private MemCommandMapper memCommandMapper;

    @Autowired
    private MemBranchMapper memBranchMapper;

    @Autowired
    private IMemberCacheService memberCacheService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(prefix = RedisLockConstants.ADD_ORDER_COMMAND, condition = "#data.branchId", tryLock = true)
    public String addOrderCommandHandle(CommandAddOrderVO data) {
        BranchDTO branchDTO = memberCacheService.getBranchDto(data.getBranchId());
        if (ToolUtil.isEmpty(branchDTO)) {
            log.error("加单数据异常，门店ID不存在：{}", data.getBranchId());
            return StringUtils.format("加单数据异常，门店ID不存在：{}", data.getBranchId());
        }

        CommandReqVO commandReqVO = CommandReqVO.builder()
                .sysCode(branchDTO.getSysCode())
                .commandLevel(NumberPool.INT_ZERO)
                .commandType(CommandTypeEnum.ADD_ORDER.getType())
                .status(StatusConstants.COMMAND_STATUS_1)
                .commandDate(DateUtil.beginOfDay(data.getCreateTime()))
                .pubMerchantType(CommandMerchantTypeEnum.BRANCH.getType())
                .pubId(data.getBranchId())
                .build();

        // 返回数据字段处理
        String resultInfo = "";

        switch (data.getType()) {
            // 订单下单增加锚点指令
            case StatusConstants.COMMAND_OPERATE_1:
                resultInfo = anchorCommandHanlder(commandReqVO, data);
                break;

            // 业务员增加普通指令
            case StatusConstants.COMMAND_OPERATE_2:
                if (SecurityUtils.isColonel()) {
                    resultInfo = createOrdinaryCommandHanlder(commandReqVO, data);
                }
                break;

            // 更新普通执行执行结果（加入购物车）
            case StatusConstants.COMMAND_OPERATE_3:
                if (SecurityUtils.isColonel()) {
                    ordinaryCommandHanlder(data, Collections.singletonList(data.getCommandId()), StatusConstants.COMMAND_STATUS_1);
                }
                break;

            // 更新普通执行执行结果（下单更新）
            case StatusConstants.COMMAND_OPERATE_4:
                ordinaryCommandHanlder(data, data.getCommandIds(), StatusConstants.COMMAND_STATUS_1);
                break;
            default:
                log.error("加单数据异常，未知操作类型：{}", data.getType());
                break;
        }
        return resultInfo;
    }

    @Override
    public PageResult<CommandAddOrderRespDTO> getMemCommandPage(CommandPageReqVO pageReqVO) {

        // 前端未传递具体门店ID，则查询当前业务员下 绑定的门店信息
        if (ToolUtil.isEmpty(pageReqVO.getBranchId())) {
            // 查询当前业务员下
            List<MemBranch> memBranches = memBranchMapper.selectMemBranchByColonels(Collections.singletonList(SecurityUtils.getLoginUser().getColonelId()));
            if (ToolUtil.isEmpty(memBranches)) {
                return PageResult.empty();
            }
            pageReqVO.setBranchIds(memBranches.stream().map(MemBranch::getBranchId).collect(Collectors.toList()));
        } else {
            pageReqVO.setBranchIds(Collections.singletonList(pageReqVO.getBranchId()));
        }

        Page<CommandAddOrderRespDTO> page = PageUtils.startPage(pageReqVO);
        pageReqVO.setSearchDate(DateUtils.getDate());
        pageReqVO.setPubMerchantType(CommandMerchantTypeEnum.BRANCH.getType());
        List<CommandAddOrderRespDTO> commandRespDTOList = memCommandMapper.getColonelAppCommandPage(pageReqVO);

        commandRespDTOList.forEach(command -> {
            BranchDTO branchDTO = memberCacheService.getBranchDto(command.getBranchId());
            command.setBranchName(branchDTO.getBranchName())
                    .setBranchContactName(branchDTO.getContactName())
                    .setBranchContactPhone(branchDTO.getContactPhone())
                    .setBranchAddr(branchDTO.getBranchAddr())
                    .setBranchImg(branchDTO.getBranchImages())
            ;
            command.setState(pageReqVO.getCommandState());
            command.getCommandAddOrderExecDTOList().forEach(commandExecDTO -> {
                CommandAddOrderExecDTO commandAddOrderExecDto = JSON.parseObject(commandExecDTO.getExecRes(), CommandAddOrderExecDTO.class);
                if (ToolUtil.isNotEmpty(commandAddOrderExecDto)) {
                    commandExecDTO.setCompleteTime(commandAddOrderExecDto.getCompleteTime())
                            .setPushAmt(commandAddOrderExecDto.getPushAmt())
                            .setPushSkuQty(commandAddOrderExecDto.getPushSkuQty())
                            .setOrderAmt(commandAddOrderExecDto.getOrderAmt())
                            .setOrderSkuQty(commandAddOrderExecDto.getOrderSkuQty())
                            .setAchievementRate(commandAddOrderExecDto.getAchievementRate())
                    ;
                }
            });
        });
        return PageResult.result(page, commandRespDTOList);
    }

    @Override
    public Boolean removeAnchorCommand(Long anchorCommandId) {
        MemCommand memCommand = memCommandMapper.selectById(anchorCommandId);
        if (ToolUtil.isEmpty(memCommand)) {
            throw exception(APP_COLONEL_ANCHOR_COMMAND_NOT_EXISTS);
        }
        memCommand.setStatus(StatusConstants.COMMAND_STATUS_0);
        return memCommandMapper.updateById(memCommand) > NumberPool.INT_ZERO;
    }

    /**
     * 锚点加单指令处理
     * @param commandReqVO
     * @return
     */
    private String anchorCommandHanlder(CommandReqVO commandReqVO, CommandAddOrderVO data) {
        // 查询 加单指令锚点是否存在
        MemCommand command = memCommandMapper.selectByCondition(commandReqVO);
        if (ToolUtil.isEmpty(command)) {
            // 增加指令锚点
            command = MemCommand.builder()
                    .sysCode(commandReqVO.getSysCode())
                    .commandLevel(CommandLevelTypeEnum.LEVEL_0.getType())
                    .commandDate(DateUtil.beginOfDay(data.getCreateTime()))
                    .commandType(CommandTypeEnum.ADD_ORDER.getType())
                    .status(StatusConstants.COMMAND_STATUS_1)
                    .pubMerchantType(CommandMerchantTypeEnum.BRANCH.getType())
                    .pubId(data.getBranchId())
                    .memo(data.getMemo())
                    .build();
            memCommandMapper.insert(command);
        }
        // 增加无需执行普通指令
        MemCommand memCommand = MemCommand.builder()
                .sysCode(commandReqVO.getSysCode())
                .commandLevel(CommandLevelTypeEnum.LEVEL_2.getType())
                .commandDate(DateUtil.beginOfDay(data.getCreateTime()))
                .commandType(CommandTypeEnum.ADD_ORDER.getType())
                .status(StatusConstants.COMMAND_STATUS_2)
                .pubMerchantType(CommandMerchantTypeEnum.BRANCH.getType())
                .pubId(data.getBranchId())
                .memo(data.getMemo())
                .pid(command.getCommandId())
                .execRes(
                        JSON.toJSONString(
                                CommandAddOrderExecVO.builder()
                                        .orderAmt(data.getOrderAmt())
                                        .orderSkuQty(data.getOrderSkuQty())
                                        .achievementRate(BigDecimal.ZERO)
                                        .completeTime(DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, data.getCreateTime())))
                                        .build()
                        )
                )
                .build();
        memCommandMapper.insert(memCommand);
        return command.getCommandId().toString();
    }

    /**
     * 普通加单指令处理
     * @param data
     */
    private void ordinaryCommandHanlder(CommandAddOrderVO data, List<Long> commandIds, Integer commandStatus) {
        // 查询 普通加单指令是否存在
        List<MemCommand> commands = memCommandMapper.selectByCommandIds(commandIds, commandStatus);
        if (ToolUtil.isEmpty(commands)) { // 普通加单指令不存在则直接返回
            log.error("加单数据异常，普通加单指令不存在或指令已完成：{}", data.getCommandIds());
            return;
        }
        // 这里最终只取第一条的执行结果
        MemCommand command = commands.get(NumberPool.INT_ZERO);
        // 普通指令执行结果 - 初始数据
        CommandAddOrderExecVO commandAddOrderExecVO = JSON.parseObject(command.getExecRes(), CommandAddOrderExecVO.class);
        if (ToolUtil.isEmpty(commandAddOrderExecVO)) {
            commandAddOrderExecVO = new CommandAddOrderExecVO();
        }

        if (Objects.equals(data.getType(), StatusConstants.COMMAND_OPERATE_3)) {
            commandAddOrderExecVO.setPushAmt(data.getPushAmt()); // 推送金额
            commandAddOrderExecVO.setPushSkuQty(data.getPushSkuQty()); // 推送sku数量
            command.setExecRes(JSON.toJSONString(commandAddOrderExecVO)) // 操作结果
            ;
        }
        if (Objects.equals(data.getType(), StatusConstants.COMMAND_OPERATE_4)) {
            commandAddOrderExecVO.setOrderAmt(data.getOrderAmt()); // 下单金额
            commandAddOrderExecVO.setOrderSkuQty(data.getOrderSkuQty()); // 下单sku数量
            commandAddOrderExecVO.setCompleteTime(DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, data.getCreateTime()))); // 下单时间
            commandAddOrderExecVO.setAchievementRate(
                    BigDecimal.valueOf(data.getOrderSkuQty()).divide(BigDecimal.valueOf(commandAddOrderExecVO.getPushSkuQty()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
            );
            command.setStatus(StatusConstants.COMMAND_STATUS_2) // 下单完成
                    .setExecRes(JSON.toJSONString(commandAddOrderExecVO)) // 操作结果
            ;
        }
        // 更新普通加单指令
        memCommandMapper.updateById(command);
    }

    /**
     * 创建普通加单指令
     * @param commandReqVO
     * @param data
     */
    private String createOrdinaryCommandHanlder(CommandReqVO commandReqVO, CommandAddOrderVO data) {
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        commandReqVO.setCommandLevel(NumberPool.INT_ONE)
                .setPubMerchantType(CommandMerchantTypeEnum.COLONEL.getType())
                .setExecMerchantType(CommandMerchantTypeEnum.BRANCH.getType())
                .setExecId(data.getBranchId())
                .setPubId(colonelId)
        ;
        // 查询 普通加单指令是否存在
        MemCommand command = memCommandMapper.selectByCondition(commandReqVO);
        if (ToolUtil.isNotEmpty(command)) {
            throw exception(APP_COLONEL_ORDINARY_COMMAND_PERFORM_EXISTS);
        }


        // 增加普通指令
        MemCommand memCommand = MemCommand.builder()
                .sysCode(commandReqVO.getSysCode())
                .commandLevel(NumberPool.INT_ONE)
                .commandDate(DateUtil.beginOfDay(DateUtil.date()))
                .commandType(CommandTypeEnum.ADD_ORDER.getType())
                .status(StatusConstants.COMMAND_STATUS_1)
                .pubMerchantType(CommandMerchantTypeEnum.COLONEL.getType())
                .pubId(colonelId)
                .execMerchantType(CommandMerchantTypeEnum.BRANCH.getType())
                .execId(data.getBranchId())
                .memo(data.getMemo())
                .pid(data.getCommandId())
                .build();
        memCommandMapper.insert(memCommand);
        return memCommand.getCommandId().toString();
    }


//    private void validateMemCommandExists(Long colonelId) {
//        if (memCommandMapper.selectById(colonelId) == null) {
//            throw exception(MEM_COMMAND_NOT_EXISTS);
//        }
//    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 菜单权限 TODO 补充编号 ==========
    // ErrorCode MEM_COMMAND_NOT_EXISTS = new ErrorCode(TODO 补充编号, "菜单权限不存在");


}
