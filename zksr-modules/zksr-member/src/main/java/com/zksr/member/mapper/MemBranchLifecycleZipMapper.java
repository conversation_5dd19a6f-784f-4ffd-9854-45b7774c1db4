package com.zksr.member.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branchLifecycle.dto.ColonelBranchLifecycleQtyDTO;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemBranchLifecycleZip;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * 门店生命周期拉链Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Mapper
public interface MemBranchLifecycleZipMapper extends BaseMapperX<MemBranchLifecycleZip> {
    default PageResult<MemBranchLifecycleZip> selectPage(MemBranchLifecycleZipPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemBranchLifecycleZip>()
                    .eqIfPresent(MemBranchLifecycleZip::getBranchLifecycleZipId, reqVO.getBranchLifecycleZipId())
                    .eqIfPresent(MemBranchLifecycleZip::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemBranchLifecycleZip::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(MemBranchLifecycleZip::getLifecycleStage, reqVO.getLifecycleStage())
                    .eqIfPresent(MemBranchLifecycleZip::getStartDate, reqVO.getStartDate())
                    .eqIfPresent(MemBranchLifecycleZip::getEndDate, reqVO.getEndDate())
                    .eqIfPresent(MemBranchLifecycleZip::getStartMemo, reqVO.getStartMemo())
                .orderByDesc(MemBranchLifecycleZip::getBranchLifecycleZipId));
    }

    /**
     * 查询最新的 门店生命周期变更记录信息
     * @param branchId
     * @return
     */
    default MemBranchLifecycleZip getLatestBranchLifecycleZip(Long branchId){
        return selectOne(new LambdaQueryWrapperX<MemBranchLifecycleZip>()
                .eq(MemBranchLifecycleZip::getBranchId,branchId)
                .orderByDesc(MemBranchLifecycleZip::getStartDate)
                .last(StringPool.LIMIT_ONE));
    }

    /**
     * 查询最新的 门店生命周期变更记录信息 不包含当前入参ID
     * @param branchId
     * @return
     */
    default MemBranchLifecycleZip getLatestBranchLifecycleZipByNotId(Long branchId,Long branchLifecycleZipId){
        return selectOne(new LambdaQueryWrapperX<MemBranchLifecycleZip>()
                .eq(MemBranchLifecycleZip::getBranchId,branchId)
                .ne(MemBranchLifecycleZip::getBranchLifecycleZipId,branchLifecycleZipId)
                .orderByDesc(MemBranchLifecycleZip::getStartDate)
                .last(StringPool.LIMIT_ONE));
    }

    /**
     * 查询一段时间内 门店的生命周期变更记录信息
     * @param branchId
     * @return
     */
    default List<MemBranchLifecycleZip> getAllLifecycleStageListByBranchIdAndDate(Long branchId, Date startDate){
        return selectList(new LambdaQueryWrapperX<MemBranchLifecycleZip>()
                .eq(MemBranchLifecycleZip::getBranchId, branchId)
                .ge(MemBranchLifecycleZip::getStartDate, startDate)
                .orderByDesc(MemBranchLifecycleZip::getStartDate).orderByDesc(MemBranchLifecycleZip::getEndDate)
        );
    }

    /**
     * 门店生命周期列表
     * @param reqVO
     * @param page
     * @return
     */
    Page<MemBranchLifecycleZipRespVO> selectPageLifecycle(@Param("reqVO") MemBranchLifecycleZipPageReqVO reqVO, @Param("page") Page<MemBranchLifecycleZipPageReqVO> page);

    /**
     * 根据门店ID集合 /平台商ID 查询该平台商下  所有门店的最新生命周期信息
     *
     * @param sysCode
     * @param branchIdList
     * @return
     */
    List<MemBranchLifecycleZip> getAllNewLifecycleStageBySysCode(@Param("sysCode")Long sysCode,@Param("branchIdList")List<Long> branchIdList);

}
