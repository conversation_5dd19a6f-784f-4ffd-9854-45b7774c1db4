package com.zksr.member.controller.colonelTarget.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.trade.api.order.vo.TrdColonelAppOrderListRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 业务员门店目标设置返回对象 mem_colonel_branch_target
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@Data
@ApiModel("业务员目标设置 - mem_colonel_branch_target Response VO")
public class MemColonelBranchTargetRespVO {

    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    @ApiModelProperty(value = "业务员目标销售总金额")
    private BigDecimal targetAmtTotal;

    @ApiModelProperty(value = "业务员实际销售总金额")
    private BigDecimal saleAmtTotal;

    @ApiModelProperty(value = "门店自设目标销售额")
    private BigDecimal branchTargetAmtTotal;


    @ApiModelProperty(value = "总数")
    private Long total;

    @ApiModelProperty(value = "业务员门店目标分页数据")
    private List<ColonelBranchTargetRespVO> clonelBranchTargetList;
}
