package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.domain.MemMemberInvoice;
import com.zksr.member.controller.invoice.vo.MemMemberInvoicePageReqVO;
import com.zksr.member.controller.invoice.vo.MemMemberInvoiceSaveReqVO;

/**
 * 用户发票Service接口
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface IMemMemberInvoiceService {

    /**
     * 新增用户发票
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemMemberInvoice(@Valid MemMemberInvoiceSaveReqVO createReqVO);

    /**
     * 修改用户发票
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemMemberInvoice(@Valid MemMemberInvoiceSaveReqVO updateReqVO);

    /**
     * 删除用户发票
     *
     * @param id ID主键
     */
    public void deleteMemMemberInvoice(Long id);

    /**
     * 批量删除用户发票
     *
     * @param ids 需要删除的用户发票主键集合
     * @return 结果
     */
    public void deleteMemMemberInvoiceByIds(Long[] ids);

    /**
     * 获得用户发票
     *
     * @param id ID主键
     * @return 用户发票
     */
    public MemMemberInvoice getMemMemberInvoice(Long id);

    /**
     * 获得用户发票分页
     *
     * @param pageReqVO 分页查询
     * @return 用户发票分页
     */
    PageResult<MemMemberInvoice> getMemMemberInvoicePage(MemMemberInvoicePageReqVO pageReqVO);

}
