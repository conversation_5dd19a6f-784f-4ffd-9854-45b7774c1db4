package com.zksr.member.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.GeoUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.controller.colonelApp.vo.ColonelBranchAnalyseInfoRespVO;
import com.zksr.member.controller.colonelApp.vo.ColonelDecisionTotalRespVO;
import com.zksr.member.convert.report.ReportConvert;
import com.zksr.member.domain.MemColonelBranchTarget;
import com.zksr.member.mapper.ColonelReportMapper;
import com.zksr.member.mapper.MemColonelBranchTargetMapper;
import com.zksr.member.mapper.MemColonelTargetMapper;
import com.zksr.member.service.IMemColonelAppReportService;
import com.zksr.member.service.IMemColonelService;
import com.zksr.member.service.IMemberCacheService;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.report.api.branch.RptBranchApi;
import com.zksr.report.api.branch.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员app报表
 * @date 2024/11/23 9:02
 */
@Service
public class IMemColonelAppReportServiceImpl implements IMemColonelAppReportService {

    @Autowired
    private IMemColonelService colonelService;

    @Autowired
    private IMemberCacheService memberCacheService;

    @Autowired
    private MemColonelTargetMapper colonelTargetMapper;

    @Resource
    private RptBranchApi rptBranchApi;

    @Autowired
    private ColonelReportMapper colonelReportMapper;

    @Autowired
    private MemColonelBranchTargetMapper colonelBranchTargetMapper;

    @Override
    public ColonelDecisionTotalRespVO getDecisionTotal(ColonelDecisionTotalReqVO reqVO) {

        // 本月
        String year = reqVO.getYear();
        String month = reqVO.getMonth();
        // 上月
        DateTime monthDate = DateUtil.parse(reqVO.getMonthDate(), DatePattern.NORM_MONTH_PATTERN);
        DateTime beforeMonthDate = DateUtil.offsetMonth(monthDate, -1);
        String beforeYear = DateUtil.formatDate(beforeMonthDate).split(StringPool.DASH)[0];
        String beforeMonth = DateUtil.formatDate(beforeMonthDate).split(StringPool.DASH)[1];
        // 获取业务员数据范围
        List<Long> colonelIdList = getQueryColonelScope(reqVO);

        // 获取业务员本月目标总额
        BigDecimal currentMonthTarget = colonelTargetMapper.selectTargetMonthByColonelId(colonelIdList, year, month);
        // 获取业务员上月目标总额
        BigDecimal beforeMonthTarget = colonelTargetMapper.selectTargetMonthByColonelId(colonelIdList, beforeYear, beforeMonth);

        // 获取业务员本月管理门店销售额
        BigDecimal currentMonthSaleAmt = rptBranchApi.totalColonelBranchSale(new RptColonelBranchReqVO(colonelIdList, reqVO.getMonthId())).getCheckedData();
        // 获取业务员上月管理门店销售额
        BigDecimal beforeMonthSaleAmt = rptBranchApi.totalColonelBranchSale(new RptColonelBranchReqVO(colonelIdList, Integer.parseInt(DateUtil.format(beforeMonthDate, DatePattern.SIMPLE_MONTH_PATTERN)))).getCheckedData();

        // 数据组装返回
        ColonelDecisionTotalRespVO respVO = new ColonelDecisionTotalRespVO();
        respVO.setCurrentMonthTotal(ColonelDecisionTotalRespVO.TargetTotal
                .builder()
                .month(reqVO.getMonthDate())
                .totalTarget(currentMonthTarget)
                .finishTarget(currentMonthSaleAmt)
                .build()
        );
        respVO.setBeforeMonthTotal(ColonelDecisionTotalRespVO.TargetTotal
                .builder()
                .month(DateUtil.format(beforeMonthDate, DatePattern.NORM_MONTH_PATTERN))
                .totalTarget(beforeMonthTarget)
                .finishTarget(beforeMonthSaleAmt)
                .build()
        );
        return respVO;
    }

    @Override
    public ColonelIndexBranchLevelTotalRespVO indexBranchLevelTotal(ColonelDecisionTotalReqVO reqVO) {
        // 获取业务员数据范围
        List<Long> colonelIdList = getQueryColonelScope(reqVO);
        return rptBranchApi.indexBranchLevelTotal(
                new RptColonelBranchReqVO(
                        colonelIdList,
                        reqVO.getMonthId()
                )
        ).getCheckedData();
    }

    @Override
    public ColonelBranchMonthWeekTotalRespVO indexWeekSaleTotal(ColonelDecisionTotalReqVO reqVO) {
        // 获取业务员数据范围
        List<Long> colonelIdList = getQueryColonelScope(reqVO);
        return rptBranchApi.indexWeekSaleTotal(
                new RptColonelBranchReqVO(
                        colonelIdList,
                        reqVO.getMonthId()
                )
        ).getCheckedData();
    }

    @Override
    public ColonelBranchSaleCategoryTotalRespVO indexSaleCategoryTotal(ColonelDecisionTotalReqVO reqVO) {
        // 获取业务员数据范围
        List<Long> colonelIdList = getQueryColonelScope(reqVO);
        ColonelBranchSaleCategoryTotalRespVO respVO = rptBranchApi.indexSaleCategoryTotal(new RptColonelBranchReqVO(colonelIdList, reqVO.getMonthId())).getCheckedData();
        // 渲染管理分类名称
        respVO.getCategoryList().forEach(item -> {
            CatgoryDTO catgoryDTO = memberCacheService.getCategoryDTO(item.getCategoryId());
            if (Objects.nonNull(catgoryDTO)) {
                item.setCategoryName(catgoryDTO.getCatgoryName());
            }
        });
        // 排序
        respVO.getCategoryList().sort(Comparator.comparing(CategorySaleTotalVO::getOrderAmt).reversed());
        return respVO;
    }

    @Override
    public ColonelLevelTotalRespVO branchLevelColonelList(ColonelBranchTagPageReqVO reqVO) {
        // 获取业务员数据范围
        List<Long> colonelIdList = getQueryColonelScope(reqVO);
        reqVO.setColonelIdList(colonelIdList);
        ColonelLevelTotalRespVO respVO = rptBranchApi.branchLevelColonelList(reqVO).getCheckedData();
        // 渲染管理业务员名称
        respVO.getList().forEach(item -> {
            ColonelDTO colonelDTO = memberCacheService.getColonel(item.getColonelId());
            if (Objects.nonNull(colonelDTO)) {
                item.setColonelName(colonelDTO.getColonelName());
            }
        });
        return respVO;
    }

    @Override
    public PageResult<ColonelTagBranchRespVO> tagLevelBranchList(TagBranchListReqVO reqVO) {
        // 获取业务员数据范围
        PageResult<ColonelTagBranchRespVO> pageResult = rptBranchApi.tagLevelBranchList(reqVO).getCheckedData();
        pageResult.getList().forEach(item -> {
            // 获取门店信息
            BranchDTO branchDTO = memberCacheService.getBranchDto(item.getBranchId());
            GeoUtils.Point centerPoint = null;
            if (Objects.nonNull(reqVO.getLat())) {
                centerPoint = new GeoUtils.Point(reqVO.getLon(), reqVO.getLat());
            }

            // 计算相对距离
            if (Objects.nonNull(centerPoint) && Objects.nonNull(branchDTO.getLatitude())) {
                GeoUtils.Point point = new GeoUtils.Point(branchDTO.getLongitude().doubleValue(), branchDTO.getLatitude().doubleValue());
                item.setDistance(GeoUtils.calculateDistanceInMeters(centerPoint, point));
            }

            // 上次拜访
            Date lastVisitTime = colonelReportMapper.selectLastVisitTime(item.getBranchId());
            item.setBeforeVisitTime(lastVisitTime);

            // 月度任务额
            MemColonelBranchTarget branchTarget = colonelBranchTargetMapper.selectBranchTagByYearMonth(reqVO.getYear(), reqVO.getMonth(), item.getBranchId());
            if (Objects.nonNull(branchTarget)) {
                item.setTargetAmt(branchTarget.getTargetSalesMoney());
            }

            // 本月拜访次数
            Long monthVisitCount = colonelReportMapper.countMonthVisit(reqVO.getMonthDate(), item.getBranchId());
            item.setMonthVisitCnt(monthVisitCount);
        });
        return pageResult;
    }

    @Override
    public ColonelBranchAnalyseInfoRespVO branchAnalyseInfo(BranchAnalyseInfoReqVO reqVO) {
        BranchAnalyseInfoRespVO respVO = rptBranchApi.branchAnalyseInfo(reqVO).getCheckedData();
        // 组装门店任务目标
        for (BranchTargetProcessVO process : respVO.getTargetProcesses()) {
            // 月度任务额
            MemColonelBranchTarget branchTarget = colonelBranchTargetMapper.selectBranchTagByYearMonth(process.getYear(), process.getMonth(), reqVO.getBranchId());
            if (Objects.nonNull(branchTarget)) {
                process.setTargetAmt(branchTarget.getTargetSalesMoney());
            }
        }
        ColonelBranchAnalyseInfoRespVO resp = ReportConvert.INSTANCE.convertColonelBranchAnalyse(respVO);
        if (Objects.nonNull(resp)) {
            resp.setBranch(memberCacheService.getBranchDto(reqVO.getBranchId()));
        }
        return resp;
    }

    @Override
    public ColonelHoursSaleRespVO hourSale(ColonelDecisionTotalReqVO reqVO) {
        // 获取业务员数据范围
        List<Long> colonelIdList = getQueryColonelScope(reqVO);
        RptColonelBranchReqVO rptColonelBranchReqVO = new RptColonelBranchReqVO(
                colonelIdList,
                reqVO.getMonthId()
        );
        return rptBranchApi.getHoursSaleCount(rptColonelBranchReqVO).getCheckedData();
    }

    /**
     * 获取查询业务员范围
     */
    private List<Long> getQueryColonelScope(ColonelDecisionTotalReqVO reqVO) {
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        List<Long> colonelIdList;
        ColonelDTO colonel = memberCacheService.getColonel(colonelId);
        // 指定业务员查询, 或者不是业务员管理员
        if (reqVO.assign() || StringPool.N.toUpperCase().equals(colonel.getIsColonelAdmin())) {
            // 指定业务员查询
            colonelIdList = ListUtil.toList(colonelId);
        } else {
            // 下级业务员集合
            colonelIdList = colonelService.getAllChildColonel(colonelId);
        }
        return colonelIdList;
    }
}
