package com.zksr.member.controller.colonelTarget.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 业务员门店目标设置对象 mem_colonel_branch_target分页
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@ApiModel("业务员目标设置 - mem_colonel_branch_target分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemColonelBranchTargetPageReqVO extends PageParam {
    @ApiModelProperty(value = "业务员Id", required = true)
    private Long colonelId;

    @ApiModelProperty(value = "门店Id")
    private String branchId;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店联系人")
    private String branchContactName;

    @ApiModelProperty(value = "门店联系电话")
    private String branchContactPhone;

    /** 目标年份 */
    @ApiModelProperty(value = "目标年份（yyyy）", required = true)
    private String targetYear;

    /** 目标月份 */
    @ApiModelProperty(value = "目标月份（MM）", required = true)
    private String targetMonth;

    @ApiModelProperty(value = "状态（-1： 查询全部 0：报错，1：设置完成）")
    private Integer status;

}
