package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 业务员上下级关系拉链表 mem_colonel_hierarchy_zip
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@TableName(value = "mem_colonel_hierarchy_zip")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MemColonelHierarchyZip extends BaseEntity{
    private static final long serialVersionUID=1L;
    @TableId(type = IdType.ASSIGN_ID)
    private Long colonelHierarchyZipId;

    @Excel(name = "平台商ID")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    @Excel(name = "上级业务员id")
    private Long pcolonelId;

    @Excel(name = "业务员ID")
    private Long colonelId;

    @Excel(name = "开始日期")
    private Date startDate;

    @Excel(name = "结束日期")
    private Date endDate;
}
