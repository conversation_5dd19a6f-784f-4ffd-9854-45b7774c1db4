package com.zksr.member.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemMemberAddress;
import com.zksr.member.controller.address.vo.MemMemberAddressPageReqVO;


/**
 * 用户地址Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Mapper
public interface MemMemberAddressMapper extends BaseMapperX<MemMemberAddress> {
    default PageResult<MemMemberAddress> selectPage(MemMemberAddressPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemMemberAddress>()
                    .eqIfPresent(MemMemberAddress::getId, reqVO.getId())
                    .eqIfPresent(MemMemberAddress::getMemberId, reqVO.getMemberId())
                    .eqIfPresent(MemMemberAddress::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemMemberAddress::getThreeAreaCityId, reqVO.getThreeAreaCityId())
                    .likeIfPresent(MemMemberAddress::getProvinceName, reqVO.getProvinceName())
                    .likeIfPresent(MemMemberAddress::getCityName, reqVO.getCityName())
                    .likeIfPresent(MemMemberAddress::getDistrictName, reqVO.getDistrictName())
                    .likeIfPresent(MemMemberAddress::getContactName, reqVO.getContactName())
                    .eqIfPresent(MemMemberAddress::getContactPhone, reqVO.getContactPhone())
                    .eqIfPresent(MemMemberAddress::getDeliveryAddress, reqVO.getDeliveryAddress())
                    .eqIfPresent(MemMemberAddress::getRemark, reqVO.getRemark())
                    .eqIfPresent(MemMemberAddress::getDelFlag, reqVO.getDelFlag())
                    .eqIfPresent(MemMemberAddress::getVersion, reqVO.getVersion())
                .orderByDesc(MemMemberAddress::getId));
    }
}
