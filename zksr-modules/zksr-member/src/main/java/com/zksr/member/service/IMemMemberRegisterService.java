package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.api.memberRegister.dto.MemberRegisterDTO;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterAuditVO;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterPageRespVO;
import com.zksr.member.domain.MemMemberRegister;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterPageReqVO;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterSaveReqVO;

/**
 * 用户注册信息Service接口
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
public interface IMemMemberRegisterService {

    /**
     * 新增用户注册信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemMemberRegister(@Valid MemMemberRegisterSaveReqVO createReqVO);

    /**
     * 修改用户注册信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemMemberRegister(@Valid MemMemberRegisterSaveReqVO updateReqVO);

    /**
     * 删除用户注册信息
     *
     * @param memberRegisterId 用户注册信息ID
     */
    public void deleteMemMemberRegister(Long memberRegisterId);

    /**
     * 批量删除用户注册信息
     *
     * @param memberRegisterIds 需要删除的用户注册信息主键集合
     * @return 结果
     */
    public void deleteMemMemberRegisterByMemberRegisterIds(Long[] memberRegisterIds);

    /**
     * 获得用户注册信息
     *
     * @param memberRegisterId 用户注册信息ID
     * @return 用户注册信息
     */
    public MemMemberRegister getMemMemberRegister(Long memberRegisterId);

    /**
     * 获得用户注册信息分页
     *
     * @param pageReqVO 分页查询
     * @return 用户注册信息分页
     */
    PageResult<MemMemberRegisterPageRespVO> getMemMemberRegisterPage(MemMemberRegisterPageReqVO pageReqVO);

    /**
     * 根据ID获取用户注册信息
     * @param memberRegisterId
     * @return
     */
    MemMemberRegister getMemberRegisterById(Long memberRegisterId);

    /**
     * 用小程序用户注册新增用户注册信息
     * @param saveReqVO
     * @return
     */
    Boolean insertUserMemberRegister(MemMemberRegisterSaveReqVO saveReqVO);

    void batchAuditMemberRegister(MemMemberRegisterAuditVO auditVO);

    MemberRegisterDTO getMemberRegisterByUserName(Long sysCode, String userName);

    MemMemberRegister getMemberRegisterByBranchId(Long branchId);
}
