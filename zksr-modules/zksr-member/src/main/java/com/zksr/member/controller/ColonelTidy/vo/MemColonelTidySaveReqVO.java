package com.zksr.member.controller.ColonelTidy.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 业务员理货记录对象 mem_colonel_tidy
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Data
@ApiModel("业务员理货记录 - mem_colonel_tidy分页 Request VO")
public class MemColonelTidySaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    /** 门店ID */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    /** 理货图片 */
    @Excel(name = "理货图片")
    @ApiModelProperty(value = "理货图片")
    private String tidyImg;

    /** 理货说明 */
    @Excel(name = "理货说明")
    @ApiModelProperty(value = "理货说明")
    private String tidyDescr;

}
