package com.zksr.member.api.branchRegister;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.member.api.branchRegister.dto.BranchRegisterDTO;
import com.zksr.member.domain.MemBranchRegister;
import com.zksr.member.service.IMemBranchRegisterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Slf4j
@ApiIgnore
public class BranchRegisterApiImpl implements BranchRegisterApi {

    @Autowired
    private IMemBranchRegisterService memBranchRegisterService;

    @Override
    @InnerAuth
    public CommonResult<BranchRegisterDTO> getBranchRegisterById(Long branchRegisterId) {
        return success(HutoolBeanUtils.toBean(memBranchRegisterService.getBranchRegisterById(branchRegisterId),BranchRegisterDTO.class));
    }

    /**
    * @Description: 添加门店
    * @Author: liuxingyu
    * @Date: 2024/6/7 16:32
    */
    @Override
    @InnerAuth
    public CommonResult<Long> addBranch(BranchRegisterDTO registerDTO) {
        return success(memBranchRegisterService.addBranch(registerDTO));
    }

    @Override
    public CommonResult<Boolean> getBranchRegisterByUserName(String userName) {
        List<MemBranchRegister> branchRegisterList = memBranchRegisterService.getBranchRegisterByUserName(userName);
        if (ToolUtil.isEmpty(branchRegisterList)) {
            return success(Boolean.FALSE);
        }
        return success(Boolean.TRUE);
    }
}
