package com.zksr.member.service;

import com.zksr.member.controller.colonelApp.vo.ColonelAppBranchTargetUpdateVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelBranchTargetPageReqVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelBranchTargetRespVO;
import com.zksr.member.domain.MemColonelTarget;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 业务员门店目标设置Service接口
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
public interface IMemColonelBranchTargetService {

    /**
     * 批量新增业务员门店目标设置
     * @param memColonelTargetList 业务员目标设置集合
     * @return
     */
    public List<Long> insertMemColonelBranchTarget(List<MemColonelTarget> memColonelTargetList);

    /**
     * 更新业务员门店目标设置 (pc变更)
     * @param memColonelTarget
     */
    public void updateMemColonelBranchTarget(MemColonelTarget memColonelTarget);

    /**
     *  分页查询业务员门店目标设置
     * @param pageReqVO
     * @return
     */
    public MemColonelBranchTargetRespVO getColonelBranchTargetPage(MemColonelBranchTargetPageReqVO pageReqVO);

    /**
     * 获取门店年月实际销售额
     * @param branchId 门店id
     * @param sysCode 平台id
     * @param date 年月
     * @return
     */
    public BigDecimal getBranchSaleAmt(Long branchId, Long sysCode, String date);

    /**
     * 更新业务员门店目标设置（业务员APP手动更改）
     * @param reqVO
     */
    public void updateColoenBranchTarget(List<ColonelAppBranchTargetUpdateVO> reqVO);

    /**
     * 根据业务员ID,目标年和月 获取门店目标ID集合
     * @param
     */
    public List<Long> getBranchTargetIds(Long colonelId, String year, String month);

    /**
     * 删除业务员门店目标
     *
     * @param colonelBranchTargetId 主键ID
     */
    public void deleteMemColonelBranchTarget(Long colonelBranchTargetId);

    /**
     * 批量删除业务员门店目标
     *
     * @param colonelBranchTargetsIds 需要删除的业务员目标设置主键集合
     * @return 结果
     */
    public void deleteMemColonelBranchTargetByColonelBranchTargets(List<Long> colonelBranchTargetsIds);
}
