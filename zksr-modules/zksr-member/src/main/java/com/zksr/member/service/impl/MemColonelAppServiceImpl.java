package com.zksr.member.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantWxb2bDTO;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.vo.ColonelAccountRespVO;
import com.zksr.account.api.withdraw.WithdrawApi;
import com.zksr.account.api.withdraw.dto.SaveWithdrawDTO;
import com.zksr.account.api.withdraw.dto.WithdrawDTO;
import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.domain.dto.car.AppCarInfoDTO;
import com.zksr.common.core.enums.CouponReceiveType;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.erpUtils.RSAUtils;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.*;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageParam;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsColonelAppBranch;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.common.elasticsearch.service.EsColonelAppBranchService;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.enums.RedisCouponConstants;
import com.zksr.common.redis.service.RedisCarService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.common.redis.utils.RedisWxTokenUtil;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.member.api.colonel.vo.MemShopAppRecodeReqVO;
import com.zksr.member.api.colonelApp.dto.PageDataDTO;
import com.zksr.member.api.colonelApp.dto.PageDataReqDTO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyPageReqVO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidySaveReqVO;
import com.zksr.member.controller.branch.vo.MemBranchRespVO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterSaveReqVO;
import com.zksr.member.controller.branch.vo.MemBranchPageReqVO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterSaveResp;
import com.zksr.member.controller.colonelApp.dto.ColonelAppTargetDetailDTO;
import com.zksr.member.controller.colonelApp.dto.ColonelAppTargetRespDTO;
import com.zksr.member.controller.colonelApp.dto.MemColonelSendCouponDTO;
import com.zksr.member.controller.colonelApp.vo.*;
import com.zksr.member.controller.colonelTarget.vo.MemColonelBranchZipRespVO;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogPageReqVO;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogRespVO;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogSaveReqVO;
import com.zksr.member.convert.ColonelTidy.MemColonelTidyConvert;
import com.zksr.member.convert.branch.BranchConvert;
import com.zksr.member.convert.colonel.MemberColonelConvert;
import com.zksr.member.domain.*;
import com.zksr.member.mapper.*;
import com.zksr.member.mq.MemberAppMqProducer;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.*;
import com.zksr.promotion.api.coupon.vo.ColonelAppPrmCouponTemplatePageReqVO;
import com.zksr.promotion.api.coupon.vo.ColonelAppPrmCouponTemplateRespVO;
import com.zksr.promotion.api.coupon.vo.CouponPageReqVO;
import com.zksr.promotion.api.coupon.vo.NormalCouponReceiveSingleAsyncReqVo;
import com.zksr.promotion.utils.CouponScopeUtil;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.BasicSettingPolicyDTO;
import com.zksr.trade.api.car.CarApi;
import com.zksr.trade.api.car.vo.TrdCarApiRespVO;
import com.zksr.trade.api.order.dto.ColonelAppOrderListTotalDTO;
import com.zksr.trade.api.order.vo.TrdColonelAppOrderDetailRespVO;
import com.zksr.member.controller.relation.vo.MemColonelRelationPageReqVO;
import com.zksr.member.service.IMemColonelAppService;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.TrdColonelAppOrderListPageReqVO;
import com.zksr.member.service.*;
import com.zksr.system.api.dcArea.DcAreaApi;
import com.zksr.system.api.dcArea.dto.DcAreaDTO;
import com.zksr.system.api.partnerPolicy.dto.ColonelSettingPolicyDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import javax.validation.Valid;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zksr.common.core.constant.StatusConstants.*;
import static com.zksr.common.core.constant.StatusConstants.REGISTER_APPROVE_FLAG_0;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.constant.MemberConstant.COLONEL_APP_Page_DATA_TYPE_1;
import static com.zksr.member.enums.AppErrorCodeConstants.*;
import static com.zksr.member.enums.ErrorCodeConstants.MEM_BRANCH_NOT_EXISTS;
import static com.zksr.member.enums.ErrorCodeConstants.MEM_MEMBER_PASSWORD_NOT_LENGTH;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_AREA_ITEM_AREA_NOT_EXISTS;

/**
 * <AUTHOR>
 * @Date 2024/4/16 15:14
 * @业务员app-新增门店业务层
 */
@Service
@Slf4j
public class MemColonelAppServiceImpl implements IMemColonelAppService {
    @Autowired
    private Cache<Long, ColonelDTO> colonelDTOCache;

    @Autowired
    private MemBranchMapper memBranchMapper;

    @Autowired
    private MemColonelRelationMapper memColonelRelationMapper;

    @Autowired
    private IMemBranchRegisterService memBranchRegisterService;

    @Autowired
    private IMemMemberService memMemberService;

    @Autowired
    private IMemMemberRegisterService memMemberRegisterService;

    @Autowired
    private DcAreaApi remoteDcAreaApi;

    @Autowired
    private IMemberCacheService memberCacheService;
    @Resource
    private OrderApi orderApi;

    @Resource
    private AfterApi afterApi;

    @Autowired
    private RedisStockService redisStockService;

    @Resource
    private CarApi carApi;

    @Autowired
    private AreaApi remoteAreaApi;

    @Resource
    private AccountApi accountApi;

    @Resource
    private CouponApi couponApi;

    @Resource
    private PlatformMerchantApi platformMerchantApi;

    @Resource
    private WithdrawApi withdrawApi;

    @Autowired
    private EsColonelAppBranchService colonelAppBranchService;

    @Autowired
    private IMemColonelAppSkuPriceService memColonelAppSkuPriceService;

    @Autowired
    private MemColonelVisitLogMapper memColonelVisitLogMapper;

    @Autowired
    private MemColonelTidyMapper memColonelTidyMapper;

    @Autowired
    private MemberAppMqProducer memberAppMqProducer;

    @Autowired
    private RedisService redisService;

    @Autowired
    private MemColonelMapper memColonelMapper;

    @Autowired
    private IMemColonelService memColonelService;

    @Autowired
    private RedisCarService redisCarService;

    @Resource
    private ColonelApi colonelApi;
    @Autowired
    private AreaCityApi areaCityApi;

    @Autowired
    private MemColonelDaySettleMapper memColonelDaySettleMapper;

    @Autowired
    private MemColonelMonthSettleMapper memColonelMonthSettleMapper;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private MemBranchUserMapper memBranchUserMapper;
    @Autowired
    private BranchApi branchApi;
    @Autowired
    private Cache<Long, BasicSettingPolicyDTO> basicSettingPolicyCache;
    @Autowired
    private IMemColonelBranchZipService memColonelBranchZipServiceImpl;
    @Autowired
    private PartnerPolicyApi partnerPolicyApi;
    @Autowired
    private IMemberCacheService memberCacheServiceImpl;
    @Autowired
    private IMemColonelBranchZipService memColonelBranchZipService;
    @Resource
    private PartnerApi partnerApi;

    @Autowired
    private IMemBranchLifecycleZipService branchLifecycleZipService;

    @Override
    public void addBranchRegister(@Valid MemBranchRegisterSaveReqVO createReqVO) {
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        //获取token中的业务员ID
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        if(Objects.isNull(colonelId)) throw exception(APP_COLONEL_CHECK_COLONEL_ID);
        createReqVO.setColonelId(colonelId);

        //校验商品区域城市 是否是二级
        remoteAreaApi.checkAreaByAreaId(createReqVO.getAreaId());

//        //校验手机号是否存在用户信息
//        String userName = createReqVO.getContactPhone();
//        MemMember checkMemMember = memMemberService.getInfoByMobileAndOpenid(null,null,null,userName);
//        if(ToolUtil.isNotEmpty(checkMemMember)){
//            throw exception(APP_COLONEL_CHECK_MEMBER_USERNAME);
//        }
//        //校验手机号是否存在用户注册信息
//        MemberRegisterDTO checkMemberRegister = memMemberRegisterService.getMemberRegisterByUserName(userName);
//        if(ToolUtil.isNotEmpty(checkMemberRegister)){
//            throw exception(APP_COLONEL_CHECK_MEMBER_REGISTER_USERNAME);
//        }

        //获取运营商配置
        //1.先根据所选城市 查询是否开启自动审核
        Long areaId = createReqVO.getAreaId();
        if(ToolUtil.isEmpty(areaId)) throw exception(PRDT_AREA_ITEM_AREA_NOT_EXISTS);

        //根据城市ID获取对应的运营商基础配置
        CommonResult<DcAreaDTO> dcAreaResult = remoteDcAreaApi.getDcAreaByDcIdOrAreaId(null, areaId, sysCode);
        if(ToolUtil.isEmpty(dcAreaResult.getData())) throw new ServiceException("该城市未分配运营商");;
        DcAreaDTO dcAreaDTO = dcAreaResult.getData();
        //获取运营商配置
        ColonelSettingPolicyDTO config = memberCacheService.getColonelSettingPolicyDTO(dcAreaDTO.getDcId());

        createReqVO.setSysCode(sysCode);
        //设置默认值
        createReqVO.setStatus(STATE_ENABLE);
        createReqVO.setApproveFlag(AUDIT_STATE_0);
        createReqVO.setBranchApproveFlag(REGISTER_APPROVE_FLAG_0);


        if(ToolUtil.isNotEmpty(config)){
            if(StatusConstants.REGISTER_APPROVE_FLAG_1.equals(Integer.valueOf(config.getColonelExpandBranchType()))){
                createReqVO.setBranchApproveFlag(REGISTER_APPROVE_FLAG_1);
            }
        }

        //新增门店注册信息
        MemBranchRegisterSaveResp saveResp = memBranchRegisterService.insertMemBranchRegister(createReqVO);
        if (Objects.nonNull(saveResp.getBranchId())) {
            // 发门店调整
            memberAppMqProducer.sendEsColonelAppBranchBaseEvent(saveResp.getBranchId());
        }
    }

    @Override
    public boolean checkAddBranchPhone(String contactPhone) {

        //校验手机号是否存在用户信息
        String userName = contactPhone;
        MemMember checkMemMember = memMemberService.getInfoByMobileAndOpenid(null,null,null,userName);
        if(ToolUtil.isNotEmpty(checkMemMember)){
            return false;
        }
        return true;
    }

    @Override
    public void editColoneBranch(MemColonelAppSaveBranchReqVo updateReqVO) {
        // 更新数据
        MemBranch memBranch = memBranchMapper.selectById(updateReqVO.getBranchId());
        if (ObjectUtil.isNull(memBranch)) {
            throw exception(MEM_BRANCH_NOT_EXISTS);
        }
        // 如果区域有变，
        if (ToolUtil.isNotEmpty(updateReqVO.getAreaId())  && !Objects.equals(memBranch.getAreaId(),updateReqVO.getAreaId())){ // 删除业务员，将门店业务员置空
            updateReqVO.setColonelId(null);
            memColonelBranchZipService.updateMemColonelBranchZip(HutoolBeanUtils.toBean(updateReqVO, MemBranchSaveReqVO.class),memBranch); // 删除门店业务员拉链表数据
            memBranchMapper.updateBranchColonelId(HutoolBeanUtils.toBean(updateReqVO, MemBranchSaveReqVO.class));
        }
        memBranchMapper.updateBranch(HutoolBeanUtils.toBean(updateReqVO, MemBranch.class));
        // 这个方法没有加缓存, 可以直接移除缓存
        memberCacheService.deleteBranchById(updateReqVO.getBranchId());
    }

    @Override
    public List<AreaDTO> getcityList() {
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();

        MemColonel colonel = memColonelMapper.selectById(colonelId);

        Map<Long, AreaDTO> resultMap = new HashMap<>();
        if(ToolUtil.isNotEmpty(colonel)){
            Long areaId = colonel.getAreaId();

             CommonResult<DcAreaDTO> dcAreaResult =remoteDcAreaApi.getDcAreaByDcIdOrAreaId(null,areaId,colonel.getSysCode());

            if (ToolUtil.isNotEmpty(dcAreaResult.getData())) {
                DcAreaGroupDTO dcAreaGroupDTO = remoteDcAreaApi.getByDcId(dcAreaResult.getData().getDcId()).getData();
                dcAreaGroupDTO.getAreaIds().stream()
                        .map(id -> remoteAreaApi.getAreaByAreaId(id).getData())
                        .filter(Objects::nonNull) // 确保AreaDTO不为空
                        .forEach(areaDTO -> {
                            if (areaDTO.getPid() != null) { // 检查pid是否非空
                                resultMap.putIfAbsent(areaDTO.getAreaId(), areaDTO); // 只有当Map中没有该pid时才添加
                                AreaDTO pidAreaDTO =remoteAreaApi.getAreaByAreaId(areaDTO.getPid()).getData();
                                resultMap.putIfAbsent(pidAreaDTO.getAreaId(), pidAreaDTO);
                            }else{
                                resultMap.putIfAbsent(areaDTO.getAreaId(), areaDTO);
                            }
                        });
            }
        }

        return new ArrayList<>(resultMap.values());
    }

    @Override
    public PageResult<MemColonelAppCustomerListRespVO> getMemColonelAppCustomerPage(MemColonelAppCustomerListPageReqVO pageReqVO) {
        //获取当前业务员ID
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        if(Objects.isNull(colonelId))
            throw exception(APP_COLONEL_CHECK_COLONEL_ID);

        GeoUtils.Point centerPoint = null;
        if (Objects.nonNull(pageReqVO.getLat())) {
            centerPoint = new GeoUtils.Point(pageReqVO.getLon(), pageReqVO.getLat());
        }

        //查询该业务员是不是管理业务员
        List<MemColonelRelation> memColonelRelation = memColonelRelationMapper.getColonelRelation(new MemColonelRelationPageReqVO(colonelId));
        List<Long> colonelIds = memColonelRelation.stream().map(MemColonelRelation::getColonelId).collect(Collectors.toList());
        colonelIds.add(colonelId);

        //获取业务员绑定、筛选条件过后的门店信息
        List<MemBranch> memBranches = memBranchMapper.selectBranchByColonelApp(pageReqVO,colonelIds);

        //如果筛选出来没有门店信息 并且查询的并不是公海 则直接过滤掉
        if ((ToolUtil.isEmpty(memBranches) || memBranches.isEmpty()) && !Objects.equals(pageReqVO.getIsOpenSeas(),NumberPool.INT_TWO)) {
            return new PageResult<>();
        }

        memBranches = memBranches.stream().filter((item)->{
            if (ToolUtil.isNotEmpty(pageReqVO.getIsOpenSeas()) && pageReqVO.getIsOpenSeas() == 1 && ToolUtil.isNotEmpty(item.getColonelId())){
                return true;
            }
            if (ToolUtil.isNotEmpty(pageReqVO.getIsOpenSeas()) && pageReqVO.getIsOpenSeas() == 2 && ToolUtil.isEmpty(item.getColonelId())){
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        List<Long> branchIds = memBranches.stream()
                .filter(memBranch -> memBranch.getStatus() != null && memBranch.getStatus().equals(NumberPool.INT_ONE))
                .map(MemBranch::getBranchId)
                .collect(Collectors.toList());


        // 查询 ES 门店数据, 支持GEO排序, 销量排序
        // 查询ES
        ColonelAppBranchDTO search = BranchConvert.INSTANCE.convertEsBranchSearchDTO(pageReqVO);
        search.setBranchIds(branchIds);
        search.setColonelIds(colonelIds);
        if (pageReqVO.getIsOpenSeas() == 2){
            search.setColonelId(null);
            search.setColonelIds(null);
            search.setIsOpenSeas(2);
        }
        search.setCreateYearMonth(DateUtils.dateTimeNow(DateUtils.YYYY_MM));
        search.setSysCode(SecurityUtils.getLoginUser().getSysCode());
        ColonelDTO colonel = memberCacheService.getColonel(colonelId);
        if (ToolUtil.isNotEmpty(colonel)){
            search.setAreaId(colonel.getAreaId());
        }
        PageResult<EsColonelAppBranch> esBranchList = colonelAppBranchService.searchColonelAppBranchPage(search);

        // 开始渲染门店数据
        List<MemColonelAppCustomerListRespVO> resultList = new ArrayList<>();
        for (EsColonelAppBranch esBranch : esBranchList.getList()) {
            BranchDTO branchDTO = memberCacheService.getBranchDto(esBranch.getBranchId());
            // 合并渲染数据
            MemColonelAppCustomerListRespVO respVO = BranchConvert.INSTANCE.convertAppCustomerRespVO(branchDTO, esBranch);

            // 获取最近加购时间
            AppCarInfoDTO carInfo = redisCarService.getCarInfo(esBranch.getBranchId());
            respVO.setLastCarAddTime(carInfo.getLastAddTime());
            if(ToolUtil.isNotEmpty(branchDTO) && ToolUtil.isNotEmpty(branchDTO.getSeasTime()))respVO.setSeasTime(branchDTO.getSeasTime()); // 公海时间
            MemBranch memBranch = HutoolBeanUtils.toBean(branchDTO, MemBranch.class);
            if (ToolUtil.isNotEmpty(memBranch)){
                MemColonelBranchZipRespVO memColonelBranchZipRespVO = memColonelBranchZipServiceImpl.selectMemColonelBranchZip(memBranch); // 上次业务员
                if (ToolUtil.isNotEmpty(memColonelBranchZipRespVO))respVO.setLastColonel(memColonelBranchZipRespVO.getColonelName());
            }
            if (pageReqVO.getIsOpenSeas() == 2){
                if (ToolUtil.isNotEmpty(respVO.getColonelId())){
                    continue;
                }
            }

            resultList.add(respVO);

            // 计算距离
            if (Objects.nonNull(centerPoint) && Objects.nonNull(respVO.getLatitude())) {
                GeoUtils.Point point = new GeoUtils.Point(respVO.getLongitude().doubleValue(), respVO.getLatitude().doubleValue());
                respVO.setDistance(GeoUtils.calculateDistanceInMeters(centerPoint, point));
            }
        }
        return  new PageResult<>(resultList, esBranchList.getTotal());
    }

    @Override
    public PageResult<MemColonelStatisticsListRespVo> getColonelStatisticsList(MemColonelStatisticsListReqVo reqVo) {
        //获取token中的业务员ID
        Long colonelId = reqVo.getColonelId();
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        List<Long> colonelIds=new ArrayList<>();
        if(ToolUtil.isEmpty(colonelId)){
            colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();

            MemColonelRelationPageReqVO relationPageReqVO = new MemColonelRelationPageReqVO();
            relationPageReqVO.setAdminColonelId(colonelId);

            //查询该业务员是不是管理业务员
            List<MemColonelRelation> memColonelRelation = memColonelRelationMapper.getColonelRelation(relationPageReqVO);
            if(ToolUtil.isNotEmpty(memColonelRelation) && !memColonelRelation.isEmpty()){
                colonelIds= memColonelRelation.stream().map(MemColonelRelation::getColonelId).collect(Collectors.toList());
            }
            colonelIds.add(colonelId);
        }else{
            colonelIds.add(colonelId);
        }
        Page<MemColonelStatisticsListReqVo> page = new Page<>(reqVo.getPageNo(),reqVo.getPageSize());

        List<MemColonelStatisticsListRespVo> resp = memColonelMapper.getColonelStatisticsListPage(page,colonelIds, reqVo.getAreaId());
        if(ToolUtil.isEmpty(resp)){
            return  PageResult.empty();
        }
        resp.forEach(item -> {
            MemColonel memColonel =  memColonelMapper.selectById(item.getColonelId());
            item.setSex(memColonel.getSex());
            item.setColonelPhone(memColonel.getColonelPhone());
            item.setColonelLevel(memColonel.getColonelLevel());
            item.setAvatarImages(memColonel.getAvatarImages());

            //获取业务员今日数据
            //设置Key
            String key = CacheConstants.getColonelAppPageDataKey(item.getColonelId(), DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()));

            //获取业务员首页缓存
            PageDataDTO dataDTO = redisService.getCacheObject(key);

            //昨天的日期(因为日结的数据是隔天凌晨生成  所以今天的日期生成的是昨天的数据)
            Date yesterday = subtractDay(0);
            //前天的日期(因为日结的数据是隔天凌晨生成  所以今天的日期生成的是昨天的数据)
            Date beforeYesterday = subtractDay(-1);

            //拓店的统计数据
            item.setAddBranchStatistics(prepareStatistics(sysCode, item, yesterday, beforeYesterday,"addBranch",dataDTO));
            //动销的统计数据
            item.setSaleBranchStatistics(prepareStatistics(sysCode, item, yesterday, beforeYesterday,"saleBranch",dataDTO));
            //拜访的统计数据
            item.setVisitStatistics(prepareStatistics(sysCode, item, yesterday, beforeYesterday,"visit",dataDTO));

        });


        return new PageResult<>(resp,page.getTotal());
    }

    public MemColonelStatisticsDetail prepareStatistics(Long sysCode, MemColonelStatisticsListRespVo item, Date yesterday, Date beforeYesterday, String type, PageDataDTO dataDTO) {
        if (sysCode == null || item == null || yesterday == null || beforeYesterday == null) {
            return new MemColonelStatisticsDetail();
        }

        MemColonelStatisticsDetail statistics = new MemColonelStatisticsDetail();

        // 获取昨日和前日数据
        MemColonelDaySettle yesterdaySettle = memColonelDaySettleMapper.getByColonelIdAndDate(sysCode, yesterday, item.getColonelId());
        MemColonelDaySettle beforeYesterdaySettle = memColonelDaySettleMapper.getByColonelIdAndDate(sysCode, beforeYesterday, item.getColonelId());

        // 设置昨日和前日的数据
        setDayData(statistics, yesterdaySettle, beforeYesterdaySettle, type);

        // 设置今日的数据
        if (dataDTO != null) {
            long todayNum = getTypeValue(dataDTO, type);
            statistics.setTodayNum(todayNum);
        }

        // 获取并设置月度数据
        MemColonelMonthSettle monthSettle = memColonelMonthSettleMapper.getInfoByColonelIdAndSysCode(item.getColonelId(), sysCode, DateUtils.parseDateToStr(DateUtils.YYYY_MM, new Date()));
        if (monthSettle != null) {
            setMonthlyData(statistics, monthSettle, type);
        }

        return statistics;
    }

    private void setDayData(MemColonelStatisticsDetail statistics, MemColonelDaySettle yesterdaySettle, MemColonelDaySettle beforeYesterdaySettle, String type) {
        if (yesterdaySettle != null) {
            long yesterdayNum = getTypeValue(yesterdaySettle, type);
            statistics.setYesterdayNum(yesterdayNum);
        }

        if (beforeYesterdaySettle != null) {
            long beforeYesterdayNum = getTypeValue(beforeYesterdaySettle, type);
            statistics.setTheDayBeforeYesterdayNum(beforeYesterdayNum);
        }
    }

    private long getTypeValue(Object data, String type) {
        switch (type) {
            case "addBranch":
                return data instanceof PageDataDTO ? ((PageDataDTO) data).getAddBranchQty() == null ? 0L : ((PageDataDTO) data).getAddBranchQty().longValue() :
                        data instanceof MemColonelDaySettle ? ((MemColonelDaySettle) data).getAddBranchQty() == null ? 0L : ((MemColonelDaySettle) data).getAddBranchQty().longValue() : 0L;
            case "saleBranch":
                return data instanceof PageDataDTO ? ((PageDataDTO) data).getSaleBranchQty() == null ? 0L : ((PageDataDTO) data).getSaleBranchQty().longValue() :
                        data instanceof MemColonelDaySettle ? ((MemColonelDaySettle) data).getSaleBranchQty() == null ? 0L : ((MemColonelDaySettle) data).getSaleBranchQty().longValue() : 0L;
            case "visit":
                return data instanceof PageDataDTO ? ((PageDataDTO) data).getVisitQty() == null ? 0L : ((PageDataDTO) data).getVisitQty().longValue() :
                        data instanceof MemColonelDaySettle ? ((MemColonelDaySettle) data).getVisitQty() == null ? 0L : ((MemColonelDaySettle) data).getVisitQty().longValue() : 0L;
            default:
                return 0L;
        }
    }

    private void setMonthlyData(MemColonelStatisticsDetail statistics, MemColonelMonthSettle monthSettle, String type) {
        switch (type) {
            case "addBranch":
                statistics.setDailyMeanNum(monthSettle.getAddBranchDay30AvgQty() == null ? 0.0 : monthSettle.getAddBranchDay30AvgQty());
                statistics.setCurrentMonthNum(monthSettle.getAddBranchQty() == null ? 0L : monthSettle.getAddBranchQty()+statistics.getTodayNum());
                break;
            case "saleBranch":
                statistics.setDailyMeanNum(monthSettle.getSaleBranchDay30AvgQty() == null ? 0.0 : monthSettle.getSaleBranchDay30AvgQty());
                statistics.setCurrentMonthNum(monthSettle.getSaleBranchQty() == null ? 0L : monthSettle.getSaleBranchQty()+statistics.getTodayNum());
                break;
            case "visit":
                statistics.setDailyMeanNum(monthSettle.getVisitDay30AvgQty() == null ? 0.0 : monthSettle.getVisitDay30AvgQty());
                statistics.setCurrentMonthNum(monthSettle.getVisitQty() == null ? 0L : monthSettle.getVisitQty()+statistics.getTodayNum());
                break;
        }
    }



    @Override
    public ColonelDTO getColonel() {
        //获取token中的业务员ID
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        if(Objects.isNull(colonelId)) throw exception(APP_COLONEL_CHECK_COLONEL_ID);

        ColonelDTO colonelDTO = memberCacheService.getColonel(colonelId);
        if(ObjectUtil.isNotEmpty(colonelDTO) && (ToolUtil.isEmpty(colonelDTO.getSignSecret()) || ToolUtil.isEmpty(colonelDTO.getSignSecretPrivate()))){
            Map<String, String> signSecret = RSAUtils.createKeysNew(2000);
            colonelDTO.setSignSecret(signSecret.get("publicKey"));
            colonelDTO.setSignSecretPrivate(signSecret.get("privateKey"));
            // 更改业务员签名密钥（RSA公钥）
            memColonelMapper.updateById(MemberColonelConvert.INSTANCE.convert(colonelDTO));
            // 清空业务缓存
            memColonelService.removeColonelCache(colonelId);
        }
        // 获取业务员所在平台对应小程序配置 (用于业务员APP跳转商城购物车使用)
        AppletBaseConfigDTO appletBaseConfigDTO = memberCacheService.getAppletBaseConfigDTO(colonelDTO.getSysCode());
        if (ToolUtil.isNotEmpty(appletBaseConfigDTO) && ToolUtil.isNotEmpty(appletBaseConfigDTO.getAppId())) {
            colonelDTO.setAppId(appletBaseConfigDTO.getAppId());
            colonelDTO.setMiniJumpAddress(appletBaseConfigDTO.getMiniJumpAddress());
        }
        PartnerDto partnerDto = memberCacheService.getPartnerDto(colonelDTO.getSysCode() + "");
        if (ToolUtil.isNotEmpty(partnerDto)) {
            colonelDTO.setPartnerName(partnerDto.getPartnerName());
        }

        //获取业务员所绑定的运营商
        if(ToolUtil.isNotEmpty(colonelDTO.getAreaId())){
            //区域信息
            AreaDTO areaDto = memberCacheService.getAreaDto(colonelDTO.getAreaId());
            if(ToolUtil.isNotEmpty(areaDto) && ToolUtil.isNotEmpty(areaDto.getDcId())){
                DcDTO dcDTO = memberCacheService.getDcDTO(areaDto.getDcId());
                if(ToolUtil.isNotEmpty(dcDTO)){
                    colonelDTO.setDcId(dcDTO.getDcId());
                    colonelDTO.setDcName(dcDTO.getDcName());
                }
            }
        }

        return colonelDTO;
    }

    @Override
    public ColonelDTO getColonel(Long colonelId) {
        if(Objects.isNull(colonelId)) throw exception(APP_COLONEL_CHECK_COLONEL_ID);
        ColonelDTO colonel = memberCacheService.getColonel(colonelId);
        if(ObjectUtil.isNotEmpty(colonel.getPcolonelId())){
            String colonelName = memColonelMapper.getColonelName(colonel.getPcolonelId());
            colonel.setPcolonelName(colonelName);
        }
        // 计算并设置入职时长
        if (colonel.getEntryDate() != null) {
            String entryDuration = calculateEntryDuration(colonel.getEntryDate());
            colonel.setEntryDuration(entryDuration);
        }
        return colonel;
    }

    @Override
    public ColonelAppOrderListTotalDTO getMemColonelOrderListPage(TrdColonelAppOrderListPageReqVO pageReqVO) {
//        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
//        MemColonelRelationPageReqVO relationPageReqVO = new MemColonelRelationPageReqVO();
//        relationPageReqVO.setAdminColonelId(colonelId);
//        List<Long> colonelIds=new ArrayList<>();
//
//        //查询该业务员是不是管理业务员
//        List<MemColonelRelation> memColonelRelation = memColonelRelationMapper.getColonelRelation(relationPageReqVO);
//        if(ToolUtil.isNotEmpty(memColonelRelation) && !memColonelRelation.isEmpty()){
//            colonelIds= memColonelRelation.stream().map(MemColonelRelation::getColonelId).collect(Collectors.toList());
//        }
//
//        colonelIds.add(colonelId);
//        pageReqVO.setColonelIds(colonelIds);


        if (ToolUtil.isEmpty(pageReqVO.getBranchIds()) || pageReqVO.getBranchIds().isEmpty()) {
            // 查询当前业务员以及下级业务员的所有门店
            MemBranchPageReqVO reqVo = new MemBranchPageReqVO();
            reqVo.setPageSize(PageParam.PAGE_SIZE_NONE); // 去除分页设置
            List<MemBranch>  branches = getMemBranchPage(reqVo).getList();
            if (ToolUtil.isEmpty(branches) || branches.isEmpty()) {
                return new ColonelAppOrderListTotalDTO();
            }
            pageReqVO.setBranchIds(branches.stream().map(MemBranch::getBranchId).collect(Collectors.toList()));
        }

        //查销售订单
        if(Objects.equals(pageReqVO.getOrderType(), COLONEL_APP_ORDER_TYPE)){
            return orderApi.coloneAppPageOrderList(pageReqVO).getCheckedData();
        }else {//退货订单
            return afterApi.selectMemColonelAppAfterOrder(pageReqVO).getCheckedData();
        }
    }

    @Override
    public List<TrdColonelAppOrderDetailRespVO> getOrderDetail(Long orderId, String orderType) {
        List<TrdColonelAppOrderDetailRespVO> result = new ArrayList<>();
        //查销售订单
        if("1".equals(orderType)){
            result =  orderApi.getMemColonelAppOrderDetail(orderId);
        }else {//退货订单
            result =  afterApi.getMemColonelAppAfterOrderDetail(orderId);
        }
        return result;
    }

    @Override
    public List<ColonelAppTrdCarRespVO> getBranchShoppingCart(Long branchId) {

        // 空值检查
        if (ToolUtil.isEmpty(branchId)) {
            throw exception(APP_COLONEL_CHECK_BRANCH_ID);
        }

        List<TrdCarApiRespVO> trdCar =carApi.getTrdCarList(branchId);
        List<ColonelAppTrdCarRespVO> resp= HutoolBeanUtils.toBean(trdCar,ColonelAppTrdCarRespVO.class);
        resp.forEach(item->{
            item.setStock(redisStockService.getSurplusSaleQty(item.getSkuId()));
            SkuDTO sku = memberCacheService.getSkuDTO(item.getSkuId());
            SpuDTO spu = memberCacheService.getSpuDTO(sku.getSpuId());
            item.setSpuName(spu.getSpuName());
            item.setThumb(spu.getThumb());
            if(ToolUtil.isNotEmpty(sku.getUnit())){
                item.setUnit(sku.getUnit().toString());
            }
            BranchDTO branchDto = memberCacheService.getBranchDto(branchId);
            BigDecimal salePrice = memColonelAppSkuPriceService.getAreaSkuPrice(branchDto,item.getUnitSize(), item.getSkuId());
            item.setSalePrice(salePrice);
            item.setTotalPrice(salePrice.multiply(BigDecimal.valueOf(item.getQty())));
        });
        return resp;
    }

    @Override
    public ColonelAccountRespVO getColonelAccount(Long colonelId) {
        return accountApi.getColonelAccount(colonelId).getCheckedData();
    }

    @Override
    public Long saveWithdraw(SaveWithdrawDTO createReqVO) {
        return withdrawApi.createColonelWithdraw(createReqVO).getCheckedData();
    }

    @Override
    public WithdrawDTO getWithdrawInfo(Long withdrawId) {
        return withdrawApi.getWithdrawInfo(withdrawId).getCheckedData();
    }

    @Override
    public PageResult<WithdrawDTO> getAccWithdrawPage(AccWithdrawPageReqVO pageReqVO) {
        return withdrawApi.getWithdrawPage(pageReqVO).getCheckedData();
    }

    @Override
    public MemBranchRespVO getColonelBranchDetail(Long branchId) {
        // 空值检查
        if (ToolUtil.isEmpty(branchId)) {
            throw exception(APP_COLONEL_CHECK_BRANCH_ID);
        }

        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();

        BranchDTO branchDto =memBranchMapper.getMemBranchByBranchId(branchId);

        MemBranchRespVO resp = HutoolBeanUtils.toBean(branchDto,MemBranchRespVO.class);

        //判断该门店是否有签到未签退的记录
        MemColonelVisitLog visitLog = memColonelVisitLogMapper.checkBranchVisitLog(colonelId);
        if(ToolUtil.isNotEmpty(visitLog)){
            resp.setColonelVisitLogId(visitLog.getColonelVisitLogId());
            resp.setUnSignBranchId(visitLog.getBranchId());
        }
        if (ToolUtil.isNotEmpty(resp) && ToolUtil.isNotEmpty(resp.getThreeAreaCityId())){
            SysAreaCityRespVO three = areaCityApi.getById(resp.getThreeAreaCityId()).getCheckedData();
            if (Objects.nonNull(three)) {
                SysAreaCityRespVO second = areaCityApi.getById(three.getPid()).getCheckedData();
                SysAreaCityRespVO first = areaCityApi.getById(second.getPid()).getCheckedData();
                resp.setSecondAreaCityId(second.getAreaCityId());
                resp.setFirstAreaCityId(first.getAreaCityId());
            }
        }

        //设置生命周期下阶段提示
        if(ToolUtil.isNotEmpty(resp) && ToolUtil.isNotEmpty(branchDto.getLifecycleStage())){
            resp.setNextBranchLifecycleMessage(branchLifecycleZipService.getNextBranchLifecycleMessage(branchId));
        }
        return resp;
    }

    @Override
    public Long insertMemColonelVisitLog(MemColonelVisitLogSaveReqVO createReqVO) {
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();

        Long sysCode = SecurityUtils.getLoginUser().getSysCode();

        Long branchId = createReqVO.getBranchId();

        //判断该门店是否有签到未签退的记录
        MemColonelVisitLog visitLog = memColonelVisitLogMapper.checkBranchVisitLog(colonelId);
        if(ToolUtil.isNotEmpty(visitLog)){
            throw exception(APP_COLONEL_CHECK_VISIT_LOG);
        }

        log.info("新增业务员拜访签到，data:{}",createReqVO);

        createReqVO.setColonelId(colonelId);
        // 插入
        MemColonelVisitLog memColonelVisitLog = HutoolBeanUtils.toBean(createReqVO, MemColonelVisitLog.class);
        memColonelVisitLogMapper.insert(memColonelVisitLog);

        //发送业务员APP客户信息业务员签到MQ
        memberAppMqProducer.sendEsColonelAppBranchEvent(new ColonelAppBranchDTO(branchId,sysCode));

        //发送业务员APP首页信息 业务员签到MQ
        memberAppMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(colonelId,sysCode,branchId,COLONEL_APP_Page_DATA_TYPE_1));

        // 返回
        return memColonelVisitLog.getColonelVisitLogId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMemColonelVisitLog(MemColonelVisitLogSaveReqVO updateReqVO) {
        MemColonelVisitLog memColonelVisitLog = memColonelVisitLogMapper.selectById(updateReqVO.getColonelVisitLogId());

        //查询出拜访间隔时间
        if (ToolUtil.isNotEmpty(updateReqVO.getSignOutDate())){
            long startTimestamp = memColonelVisitLog.getSignInDate().getTime();
            long endTimestamp = updateReqVO.getSignOutDate().getTime();

            long differenceInMilliseconds = endTimestamp - startTimestamp;
            updateReqVO.setVisitIntervalTime(differenceInMilliseconds / 1000+"");
        }

        memColonelVisitLogMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, MemColonelVisitLog.class));

        // 当业务员签到拜访成功后更新门店进入公海时间
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        ColonelDTO colonel = memberCacheServiceImpl.getColonel(colonelId);
        if (ToolUtil.isNotEmpty(colonel)){
            DcAreaDTO dcAreaDTO = remoteDcAreaApi.getDcAreaByDcIdOrAreaId(null, colonel.getAreaId(), colonel.getSysCode()).getCheckedData();
            if (ToolUtil.isNotEmpty(dcAreaDTO) && ToolUtil.isNotEmpty(dcAreaDTO.getDcId()) && ToolUtil.isNotEmpty(updateReqVO.getBranchId())){
                BasicSettingPolicyDTO basicSettingPolicyDTO = partnerPolicyApi.getBasicSettingPolicy(dcAreaDTO.getDcId()).getData();
                BranchDTO branchDTO = branchApi.getByBranchId(updateReqVO.getBranchId()).getData();
                if (ToolUtil.isNotEmpty(branchDTO) && ToolUtil.isNotEmpty(basicSettingPolicyDTO) && ToolUtil.isNotEmpty(basicSettingPolicyDTO.getSeasDay())){
                    MemBranchSaveReqVO memBranchSaveReqVO = new MemBranchSaveReqVO();
                    memBranchSaveReqVO.setBranchId(updateReqVO.getBranchId());
                    Integer seasTime = Integer.valueOf(basicSettingPolicyDTO.getSeasDay());
                    if (ToolUtil.isNotEmpty(seasTime) && seasTime >0){
                        LocalDateTime localDate = new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        LocalDateTime newLocalDate = localDate.plusDays(seasTime);
                        Date newSeasDate = Date.from(newLocalDate.atZone(ZoneId.systemDefault()).toInstant());
                        memBranchSaveReqVO.setSeasTime(newSeasDate);
                        branchApi.edit(memBranchSaveReqVO);
                    }
                }
            }
        }
    }

    @Override
    public void deleteMemColonelVisitLogByVisitIds(Long[] visitIds) {
        for(Long visitId : visitIds){
            memColonelVisitLogMapper.deleteById(visitId);
        }
    }

    @Override
    public Long insertMemColonelTidy(MemColonelTidySaveReqVO createReqVO) {
        // 插入
        MemColonelTidy memColonelTidy = MemColonelTidyConvert.INSTANCE.convert(createReqVO);
        memColonelTidyMapper.insert(memColonelTidy);



        // 返回
        return memColonelTidy.getColonelTidyId();
    }

    @Override
    public MemColonelTidy getMemColonelTidy(Long colonelTidyId) {
        return memColonelTidyMapper.selectById(colonelTidyId);
    }

    @Override
    public PageResult<MemColonelTidy> getMemColonelTidyPage(MemColonelTidyPageReqVO pageReqVO) {
        //Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        //pageReqVO.setColonelId(colonelId);
        return memColonelTidyMapper.selectPage(pageReqVO);
    }

    @Override
    public PageDataDTO getPageData() {
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();

        MemColonelRelationPageReqVO relationPageReqVO = new MemColonelRelationPageReqVO();
        relationPageReqVO.setAdminColonelId(colonelId);
        //查询当前业务员下 管理的所有业务员关系
        List<MemColonelRelation> memColonelRelation = memColonelRelationMapper.getColonelRelation(relationPageReqVO);
        List<String> keys = Stream.concat(
                        memColonelRelation.stream().map(relation -> {
                            return CacheConstants.getColonelAppPageDataKey(relation.getColonelId(), DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()));
                        }),
                        // 加上当前业务员 key
                        Stream.of(CacheConstants.getColonelAppPageDataKey(colonelId, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate())))
                ).collect(Collectors.toList());

        // redis 管道查询
        List<PageDataDTO> pageDataList =  redisService.executePipelinedQuery(keys);
        PageDataDTO dataDTO = new PageDataDTO();
        dataDTO.setColonelId(colonelId);
        dataDTO.setSysCode(SecurityUtils.getLoginUser().getSysCode());
        if (ToolUtil.isNotEmpty(pageDataList) && !pageDataList.isEmpty()) {
            pageDataList.forEach(pageData -> {
                if (ToolUtil.isEmpty(pageData))
                    return;
                dataDTO.setOrderQty(ToolUtil.isEmptyReturn(dataDTO.getOrderQty(), NumberPool.LONG_ZERO) + ToolUtil.isEmptyReturn(pageData.getOrderQty(), NumberPool.LONG_ZERO));
                dataDTO.setPercentageAmt(ToolUtil.isEmptyReturn(dataDTO.getPercentageAmt(), BigDecimal.ZERO).add(ToolUtil.isEmptyReturn(pageData.getPercentageAmt(), BigDecimal.ZERO)));
                dataDTO.setBranchOrderAmt(ToolUtil.isEmptyReturn(dataDTO.getBranchOrderAmt(), BigDecimal.ZERO).add(ToolUtil.isEmptyReturn(pageData.getBranchOrderAmt(), BigDecimal.ZERO)));
                dataDTO.setBranchRefundAmt(ToolUtil.isEmptyReturn(dataDTO.getBranchRefundAmt(), BigDecimal.ZERO).add(ToolUtil.isEmptyReturn(pageData.getBranchRefundAmt(), BigDecimal.ZERO)));
                dataDTO.setVisitQty(ToolUtil.isEmptyReturn(dataDTO.getVisitQty(), NumberPool.LONG_ZERO) + ToolUtil.isEmptyReturn(pageData.getVisitQty(), NumberPool.LONG_ZERO));
                dataDTO.setAddBranchQty(ToolUtil.isEmptyReturn(dataDTO.getAddBranchQty(), NumberPool.LONG_ZERO) + ToolUtil.isEmptyReturn(pageData.getAddBranchQty(), NumberPool.LONG_ZERO));
                dataDTO.setSaleBranchQty(ToolUtil.isEmptyReturn(dataDTO.getSaleBranchQty(), NumberPool.LONG_ZERO) + ToolUtil.isEmptyReturn(pageData.getSaleBranchQty(), NumberPool.LONG_ZERO));
                dataDTO.setBusinessOrderQty(ToolUtil.isEmptyReturn(dataDTO.getBusinessOrderQty(), NumberPool.LONG_ZERO) + ToolUtil.isEmptyReturn(pageData.getBusinessOrderQty(), NumberPool.LONG_ZERO));
                dataDTO.setBusinessOrderAmt(ToolUtil.isEmptyReturn(dataDTO.getBusinessOrderAmt(), BigDecimal.ZERO).add(ToolUtil.isEmptyReturn(pageData.getBusinessOrderAmt(), BigDecimal.ZERO)));
            });
        }

        //设置Key
//        String key = CacheConstants.getColonelAppPageDataKey(colonelId, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()));

//        //获取业务员首页缓存
//        PageDataDTO dataDTO = redisService.getCacheObject(key);
//
//        if(ToolUtil.isEmpty(dataDTO)){
//            dataDTO = new PageDataDTO();
//        }
        return dataDTO;
    }

    @Override
    public PageResult<MemColonelAppCustomerListRespVO> getCustomerVisitListPage(MemColonelAppCustomerListPageReqVO pageReqVO) {
        //获取当前业务员ID
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        if(Objects.isNull(colonelId)) throw exception(APP_COLONEL_CHECK_COLONEL_ID);

        Page<MemColonelAppCustomerListPageReqVO> page = new Page<>(pageReqVO.getPageNo(),pageReqVO.getPageSize());
        // 获取当前业务员及管理的下级业务员ID
        List<Long> colonelIds= getColonels(colonelId);

        //        Page<MemBranchPageReqVO> branchList =  memBranchMapper.getBranchListByVisit(pageReqVO,page,colonelIds);
        //        List<MemColonelAppCustomerListRespVO> resp = HutoolBeanUtils.toBean(branchList.getRecords(), MemColonelAppCustomerListRespVO.class);

        // 根据业务员和时间段查询 已拜访或未拜访的 门店ID列表
        List<Long> branchIds = memBranchMapper.getBranchIdListByVisit(pageReqVO,colonelIds);

        GeoUtils.Point centerPoint = null;
        if (Objects.nonNull(pageReqVO.getLat())) {
            centerPoint = new GeoUtils.Point(pageReqVO.getLon(), pageReqVO.getLat());
        }
        // 查询ES门店信息排序
        // 查询ES
        ColonelAppBranchDTO search = BranchConvert.INSTANCE.convertEsBranchSearchDTO(pageReqVO);
        search.setBranchIds(branchIds);
        search.setColonelId(null);
        search.setColonelIds(null);
        search.setCreateYearMonth(DateUtils.dateTimeNow(DateUtils.YYYY_MM));
        search.setSysCode(SecurityUtils.getLoginUser().getSysCode());
        PageResult<EsColonelAppBranch> esBranchList = colonelAppBranchService.searchColonelAppBranchPage(search);

        // 开始渲染门店数据
        List<MemColonelAppCustomerListRespVO> resp = new ArrayList<>();
        for (EsColonelAppBranch esBranch : esBranchList.getList()) {
            BranchDTO branchDTO = memberCacheService.getBranchDto(esBranch.getBranchId());
            // 合并渲染数据
            MemColonelAppCustomerListRespVO respVO = BranchConvert.INSTANCE.convertAppCustomerRespVO(branchDTO, esBranch);
            resp.add(respVO);
            // 计算距离
            if (Objects.nonNull(centerPoint) && Objects.nonNull(respVO.getLatitude())) {
                GeoUtils.Point point = new GeoUtils.Point(respVO.getLongitude().doubleValue(), respVO.getLatitude().doubleValue());
                respVO.setDistance(GeoUtils.calculateDistanceInMeters(centerPoint, point));
            }
        }

        if(ToolUtil.isNotEmpty(resp)) {
            if ("0".equals(pageReqVO.getVisitStatus()))
                resp.forEach(r -> {
                    Long visitNum = memColonelVisitLogMapper.getColonelVisitLogNumByBranchId(r.getBranchId(), pageReqVO.getStartTime(), pageReqVO.getEndTime());
                    r.setVisitNum(visitNum);
                });
        }
        return  new PageResult<>(resp,esBranchList.getTotal());
    }

    @Override
    public PageResult<MemColonelVisitLogRespVO> getCustomerVisitDetailListPage(MemColonelAppCustomerListPageReqVO pageReqVO) {
        MemColonelVisitLogPageReqVO visitLogPageReqVO =HutoolBeanUtils.toBean(pageReqVO, MemColonelVisitLogPageReqVO.class);
        visitLogPageReqVO.setVisitFlag(1);
        PageResult<MemColonelVisitLog> pageResult =memColonelVisitLogMapper.selectPage(visitLogPageReqVO);
        return HutoolBeanUtils.toBean(pageResult,MemColonelVisitLogRespVO.class);
    }

    @Override
    public PageResult<MemBranch> getMemBranchPage(MemBranchPageReqVO pageReqVO) {
        //获取当前业务员ID
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        // 获取当前业务员及管理的下级业务员ID
        List<Long> colonelIds= getColonels(colonelId);
        pageReqVO.setColonelIds(colonelIds);
        Page<MemBranch> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<MemBranch> memBranchPage = memBranchMapper.selectPage(page, pageReqVO);
        return new PageResult<>(memBranchPage.getRecords(), memBranchPage.getTotal());
    }
    @Override
    public PageResult<ColonelDTO> getManagementClerkList(MemColonelPageReqVO pageReqVO) {
        // 获取token中的业务员ID
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        if (Objects.isNull(colonelId)) throw exception(APP_COLONEL_CHECK_COLONEL_ID);

        // 获取所有下属业务员信息
        List<ColonelDTO> subordinates = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        getAllSubordinates(colonelId, subordinates, visited, 0);

        // 进行模糊查询过滤
        if (ObjectUtil.isNotEmpty(pageReqVO.getColonelName())) {
            subordinates = subordinates.stream()
                    .filter(subordinate -> subordinate.getColonelName().contains(pageReqVO.getColonelName()))
                    .collect(Collectors.toList());
        }

        // 计算并设置入职时长
        subordinates.forEach(subordinate -> {
            if (subordinate.getEntryDate() != null) {
                subordinate.setEntryDuration(calculateEntryDuration(subordinate.getEntryDate()));
            }
        });

        // 分页处理
        int pageNo = pageReqVO.getPageNo();
        int pageSize = pageReqVO.getPageSize();
        int start = (pageNo - 1) * pageSize;
        int end = Math.min(start + pageSize, subordinates.size());
        List<ColonelDTO> paginatedList = subordinates.subList(start, end);

        return new PageResult<>(paginatedList, (long) subordinates.size());
    }

    @Override
    public List<ColonelDTO> getColonelJuniorMember(Long colonelId) {
        // 获取所有下属业务员信息
        List<ColonelDTO> subordinates = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        getAllSubordinates(colonelId, subordinates, visited, 0);
        subordinates.add(getColonel(colonelId));
        return subordinates.stream().distinct().collect(Collectors.toList());
    }

    private void getAllSubordinates(Long colonelId, List<ColonelDTO> subordinates, Set<Long> visited, int depth) {
        final int MAX_DEPTH = 100; // 设定一个合理的递归深度限制，防止死循环
        if (depth > MAX_DEPTH) {
            throw new ServiceException("递归深度超过最大限制，可能存在死循环");
        }
        // 检查是否已访问过该业务员，防止重复访问
        if (visited.contains(colonelId)) {
            return;
        }
        // 标记当前业务员为已访问
        visited.add(colonelId);
        // 获取直接下属业务员
        List<ColonelDTO> directSubordinates = colonelApi.getSubordinates(colonelId).getCheckedData();
        for (ColonelDTO subordinate : directSubordinates) {
            subordinates.add(subordinate);
            getAllSubordinates(subordinate.getColonelId(), subordinates, visited, depth + 1);
        }
    }

    @Override
    public PageResult<ColonelDTO> getDevelopmentSalesmanList(MemColonelPageReqVO pageReqVO) {
        //获取token中的业务员ID
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        if(Objects.isNull(colonelId)) throw exception(APP_COLONEL_CHECK_COLONEL_ID);
        List<ColonelDTO> colonelDTOS = colonelApi.selectDevelopmentSalesmanByColonelId(colonelId).getCheckedData();
        // 进行模糊查询过滤
        if (ObjectUtil.isNotEmpty(pageReqVO.getColonelName())) {
            colonelDTOS = colonelDTOS.stream()
                    .filter(dto -> dto.getColonelName().contains(pageReqVO.getColonelName()))
                    .collect(Collectors.toList());
        }

        // 计算并设置入职时长
        colonelDTOS.forEach(dto -> {
            if (dto.getEntryDate() != null) {
                dto.setEntryDuration(calculateEntryDuration(dto.getEntryDate()));
            }
        });

        // 分页处理
        int pageNo = pageReqVO.getPageNo();
        int pageSize = pageReqVO.getPageSize();
        int start = (pageNo - 1) * pageSize;
        int end = Math.min(start + pageSize, colonelDTOS.size());
        List<ColonelDTO> paginatedList = colonelDTOS.subList(start, end);
        return new PageResult<>(paginatedList, (long) colonelDTOS.size());
    }
    private String calculateEntryDuration(Date entryDate) {
        LocalDate startDate = entryDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate currentDate = LocalDate.now();
        Period period = Period.between(startDate, currentDate);
        int years = period.getYears();
        int months = period.getMonths();
        int days = period.getDays();
        StringBuilder duration = new StringBuilder();
        if (years == 0 && months == 0 && days > 0) {
            // 少于一月，显示XX天
            duration.append(days).append("天");
        } else if (months == 0 && days == 0) {
            // 精确到年，例如：1年、2年
            duration.append(years).append("年");
        } else if (years > 0 && months == 0) {
            // 精确到年，例如：1年、2年
            duration.append(years).append("年");
        } else if (years > 0 && months > 0) {
            // 大于12个月并不是整年，显示XX年XX月
            duration.append(years).append("年").append(months).append("个月");
        } else if (months > 0 && days == 0) {
            // 少于12月大于1月，显示XX月
            duration.append(months).append("个月");
        } else if (months > 0) {
            // 少于12月大于1月，显示XX月
            duration.append(months).append("个月");
        }
        return duration.toString();
    }

    @Override
    public Long addColonel(MemColonelSaveReqVO createReqVO) {
        //获取token中的业务员ID
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        if(Objects.isNull(colonelId)) throw exception(APP_COLONEL_CHECK_COLONEL_ID);
        if(ObjectUtil.isEmpty(createReqVO.getColonelPhone())){
            throw exception(APP_COLONEL_CHECK_PHONE_LOG);
        }
        if (!isValidPhone(createReqVO.getColonelPhone())) {
            throw exception(APP_COLONEL_INVALID_PHONE_FORMAT);
        }
        if (!isValidIdCard(createReqVO.getIdcard())) {
            throw exception(APP_COLONEL_INVALID_IDCARD_FORMAT);
        }
        if(ObjectUtil.isEmpty(createReqVO.getColonelName())){
            throw exception(APP_COLONEL_CHECK_NAME_LOG);
        }
        if(ObjectUtil.isEmpty(createReqVO.getPassword())){
            throw exception(APP_COLONEL_CHECK_PASSWORD_LOG);
        }
        // 密码如果不在指定范围内 错误
        if (createReqVO.getPassword().length() < UserConstants.PASSWORD_MIN_LENGTH
                || createReqVO.getPassword().length() > UserConstants.PASSWORD_MAX_LENGTH
                || !Pattern.matches(UserConstants.PASSWORD_PATTERN, createReqVO.getPassword())
        ) {
            throw exception(MEM_MEMBER_PASSWORD_NOT_LENGTH);
        }
        if(ObjectUtil.isEmpty(createReqVO.getSex())){
            throw exception(APP_COLONEL_CHECK_SEX_LOG);
        }
        if(ObjectUtil.isEmpty(createReqVO.getIdcard())){
            throw exception(APP_COLONEL_CHECK_IDCARD_LOG);
        }
         //审核状态 默认待审核
         createReqVO.setAuditState(0);
         //发展人id
         createReqVO.setDevelopPeopleId(colonelId);
         //来源
         createReqVO.setSource("APP");
         //业务员账户
         createReqVO.setUserName(createReqVO.getColonelPhone());
         //业务员级别
         createReqVO.setColonelLevel(1L);
         //业务员状态 默认停用
         createReqVO.setStatus(0);
         return colonelApi.add(createReqVO).getCheckedData();
    }

    @Override
    public void updateAvatarInfo(String url) {
        // 假设有获取当前业务员ID的方法
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        if(ObjectUtil.isEmpty(colonelId)){
            throw new ServiceException("当前用户登录异常!");
        }
        // 更新头像上传状态和修改时间
        MemColonel colonel = memColonelMapper.selectById(colonelId);
        colonel.setAvatarImages(url);
        colonel.setAvatarUpdateTime(new Date());

        memColonelMapper.updateById(colonel);
        //清空业务员缓存
        colonelDTOCache.remove(colonelId);
    }

    @Override
    public ColonelAppUpdateInfoVO getUpdateInfo() {
        String updateStr = redisSysConfigService.get(CacheConstants.COLONEL_APP_VERSION);
        if (StringUtils.isNotEmpty(updateStr)) {
            return JSON.parseObject(updateStr, ColonelAppUpdateInfoVO.class);
        }
        return null;
    }

    @Override
    public String getShopAppRecode(MemShopAppRecodeReqVO reqVO) {
        Long sysCode = null;
        if (StringUtils.isNotEmpty(reqVO.getSysSource())){
            PartnerDto partnerDto = memberCacheService.getPartnerDto(reqVO.getSysSource());
            sysCode = partnerDto.getSysCode();
        }else {
            sysCode = SecurityUtils.getLoginUser().getSysCode();
        }
//        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        reqVO.setSysSource(null);
        AppletBaseConfigDTO configDTO = memberCacheService.getAppletBaseConfigDTO(sysCode);
        if (Objects.isNull(configDTO) || StringUtils.isEmpty(configDTO.getAppId())) {
            throw exception(SHOP_RECODE_CONFIG_ERR);
        }
        String accessToken = RedisWxTokenUtil.getAppletAccessToken(configDTO.getAppId());
        if (StringUtils.isEmpty(accessToken)) {
            throw exception(SHOP_RECODE_ACCESS_TOKEN_ERR);
        }
        String url = StringUtils.format("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={}", accessToken);
        log.info("微信accesstoken---{}",url);
        log.info("微信二维码传参---{}",JSON.toJSONString(reqVO));
        byte[] bytes = HttpRequest.post(url).body(JSON.toJSONString(reqVO)).execute().bodyBytes();
        String result = new String(bytes);
        log.info("获取小程序二维码返回---{}",result);
        try {
            JSONObject resultObj = JSON.parseObject(result);
            if (resultObj.containsKey("errmsg")) {
                throw new ServiceException(resultObj.getString("errmsg"));
            }
        } catch (ServiceException e) {
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error(" getShopAppRecode异常,", e);
        }
        return "data:image/jpeg;base64," + Base64.encode(bytes);
    }

    /**
     *  客户-查看优惠券接口
     * @param receiveReq
     * @return
     */
    @Override
    public PageResult<CouponDTO> getBranchCouponList(CouponPageReqVO receiveReq) {
        return couponApi.getCouponList(receiveReq);
    }

    /**
     *  客户-查看业务员发券额度
     * @param colonelId
     * @return
     */
    @Override
    public ColonelQuotaDTO getColonelQuota(Long colonelId) {
        return couponApi.getColonelQuota(colonelId).getCheckedData();
    }

    /**
     * 客户-查看业务员发券管理
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<ColonelAppPrmCouponTemplateRespVO> getAvailableCouponsForSalesperson(ColonelAppPrmCouponTemplatePageReqVO pageReqVO) {
        PageResult<ColonelAppPrmCouponTemplateRespVO> result = new PageResult();
        pageReqVO.setReceiveType(CouponReceiveType.SALESMAN.getType());
//        pageReqVO.setDiscountType(NumberPool.INT_ZERO);
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        //查询可用优惠券
        try {
            // 查询可用优惠券
            PageResult<ColonelAppPrmCouponTemplateRespVO> colonelAppPrmCouponTemplateList = couponApi.getColonelAppPrmCouponTemplatePage(pageReqVO).getCheckedData();

            List<ColonelAppPrmCouponTemplateRespVO> filteredList = new ArrayList<>();
            Map<Long, List<CouponDTO>> listMap = new HashMap<>();
            CommonResult<List<CouponDTO>> commonResult = couponApi.getCouponListByBranchId(pageReqVO.getBranchId());
            if (commonResult.isSuccess()) {
                listMap = new HashMap<>(commonResult.getData().stream().collect(Collectors.groupingBy(CouponDTO::getCouponTemplateId)));
            }
            for (ColonelAppPrmCouponTemplateRespVO colonelAppPrmCouponTemplateRespVO : colonelAppPrmCouponTemplateList.getList()) {
                try {
                    if(ToolUtil.isNotEmpty(pageReqVO.getBranchId())){
                        Long couponTemplateId = colonelAppPrmCouponTemplateRespVO.getCouponTemplateId();
                        String stockUsedKey = RedisCouponConstants.getStockUsedKey(couponTemplateId);
                        // 验证库存 若已经领完 则过滤该条记录
                        Integer stockUsed = redisService.getCacheObject(stockUsedKey);
                        if (Objects.isNull(stockUsed) || stockUsed + 1 > colonelAppPrmCouponTemplateRespVO.getCouponQty()) {
                            continue;
                        }
                        // 查询门店是领取是否已经达到领取限制 达到领取限制 则过滤该条记录
                        Integer limit = colonelAppPrmCouponTemplateRespVO.getLimit();
                        if (limit > 0) {
                            List<CouponDTO> couponDTOList = listMap.get(colonelAppPrmCouponTemplateRespVO.getCouponTemplateId());
                            if (CollectionUtils.isNotEmpty(couponDTOList)) {
                                if (limit == couponDTOList.size()) {
                                    continue;
                                }
                            }
                        }
                        // 校验过滤黑白名单
                        List<CouponReceiveScopeDTO> couponSpuScopeList = couponApi.getCouponReceiveScopeList(colonelAppPrmCouponTemplateRespVO.getCouponTemplateId()).getCheckedData();
                        BranchDTO branchDTO = memberCacheService.getBranchDto(pageReqVO.getBranchId());
                        // 查看当前的门店是否在白名单 如果在白名单才能够看到优惠券
                        if (CouponScopeUtil.conditionReceiveCopeApplyId(HutoolBeanUtils.toBean(branchDTO, CouponScopeUtil.ReceiveScopeValidVO.class), couponSpuScopeList)) {
                            // 查询门店领取了多少张优惠券
                            int branchReceiveQty = couponApi.getBranchReceiveQty(colonelAppPrmCouponTemplateRespVO.getCouponTemplateId(), branchDTO.getBranchId());
                            colonelAppPrmCouponTemplateRespVO.setLimit(colonelAppPrmCouponTemplateRespVO.getLimit() - branchReceiveQty);
                            filteredList.add(colonelAppPrmCouponTemplateRespVO);
                        }
                    }else{
//                        int finalQty = calculateFinalCouponQuantity(colonelAppPrmCouponTemplateRespVO, colonelId);
//                        if (finalQty > 0) {
//                            colonelAppPrmCouponTemplateRespVO.setMaxLimit(finalQty);
//                        }
                        filteredList.add(colonelAppPrmCouponTemplateRespVO);
                    }
                } catch (Exception e) {
                    log.error("处理优惠券模板时出错: {}", colonelAppPrmCouponTemplateRespVO.getCouponTemplateId(), e);
                }
            }
            result.setList(filteredList);
            result.setTotal((long) filteredList.size());
        } catch (Exception e) {
            log.error("获取业务员可用优惠券时出错", e);
            throw new ServiceException("获取业务员可用优惠券时出错");
        }
        return result;
    }

    private int calculateFinalCouponQuantity(ColonelAppPrmCouponTemplateRespVO couponTemplateRespVO, Long colonelId) {
        // 检查优惠券的剩余数量是否大于 0
        int remainingQty = (couponTemplateRespVO.getCouponQty() != null ? couponTemplateRespVO.getCouponQty() : 0) -
                (couponTemplateRespVO.getReceiveCount() != null ? couponTemplateRespVO.getReceiveCount() : 0);

        // 获取业务员的剩余额度和券的面值
        ColonelQuotaDTO colonelQuotaDTO = couponApi.getColonelQuota(colonelId).getCheckedData();
        BigDecimal businessmanLimit = colonelQuotaDTO.getRemainingQuota();
        BigDecimal discountAmt = couponTemplateRespVO.getDiscountAmt();

        // 计算业务员可以发放的最大数量
        int businessmanMaxQty = businessmanLimit.divide(discountAmt, 0, BigDecimal.ROUND_DOWN).intValue();

        // 取剩余数量和业务员最大数量中的较小值
        return Math.min(remainingQty, businessmanMaxQty);
    }

    /**
     * 发券管理-去发券（查询门店信息）
     * @param couponTemplateId
     * @return
     */
    @Override
    public List<MemBranch> getStoresForSalesperson(Long couponTemplateId,String branchName) {
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        //查询该业务员是不是管理业务员
        List<MemColonelRelation> memColonelRelation = memColonelRelationMapper.getColonelRelation(new MemColonelRelationPageReqVO(colonelId));
        List<Long> colonelIds = memColonelRelation.stream().map(MemColonelRelation::getColonelId).collect(Collectors.toList());
        colonelIds.add(colonelId);

        // 获取业务员绑定的门店信息
        List<MemBranch> memBranches = memBranchMapper.selectMemBranchByColonelsAndBranchName(colonelIds,branchName);

        // 查询优惠券模板的最大可发放数量
        CouponTemplateDTO couponTemplate = couponApi.getCouponTemplate(couponTemplateId).getCheckedData();
        if (couponTemplate == null || couponTemplate.getLimit() == null || couponTemplate.getLimit() <= 0) {
            throw new ServiceException("未获取到有效的优惠券模板信息！");
        }
        int maxIssueQuantity = couponTemplate.getLimit();

        // 使用流操作过滤掉不符合条件的门店
        List<MemBranch> filteredMemBranches = memBranches.stream()
                .filter(memBranch -> {
                    // 查询当前门店已发放的优惠券数量
                    Integer issuedQuantity = couponApi.getBranchReceiveQty(couponTemplateId, memBranch.getBranchId());
                    issuedQuantity = issuedQuantity == null ? 0 : issuedQuantity;

                    // 如果已发放数量大于等于模板最大可发放数量，则排除该门店
                    if (issuedQuantity >= maxIssueQuantity) {
                        return false;
                    }
                    List<CouponReceiveScopeDTO> couponSpuScopeList = couponApi.getCouponReceiveScopeList(couponTemplateId).getCheckedData();
                    BranchDTO branchDTO = memberCacheService.getBranchDto(memBranch.getBranchId());
                    return CouponScopeUtil.conditionReceiveCopeApplyId(HutoolBeanUtils.toBean(branchDTO, CouponScopeUtil.ReceiveScopeValidVO.class), couponSpuScopeList);
                })
                .collect(Collectors.toList());

        return filteredMemBranches;
    }

    /**
     * 发券管理-查询发券记录
     * @param couponTemplateId
     * @return
     */
    @Override
    public List<MemBranch> getCouponReceivedBranchs(Long couponTemplateId,String branchName) {
        List<MemBranch> filteredBranches = new ArrayList<>();
        List<Long> branchIds = couponApi.getCouponReceivedBranchs(couponTemplateId).getCheckedData();
        if(ToolUtil.isEmpty(branchIds)){
            return filteredBranches;
        }
       filteredBranches = memBranchMapper.selectList(new LambdaQueryWrapper<MemBranch>()
                .in(MemBranch::getBranchId, branchIds)
                .like(StringUtils.isNotBlank(branchName), MemBranch::getBranchName, branchName)
                .orderByDesc(MemBranch::getCreateTime));

        return filteredBranches;
    }

    /**
     * 业务员发放优惠券
     */
    @Override
    public List<CouponReceiveResultDTO> sendCouponsFromSalesperson(MemColonelSendCouponDTO pageReqVO) {
        // 发送优惠券门店信息
        List<Long> branchIds = ToolUtil.isNotEmpty(pageReqVO.getBranchIds()) ? pageReqVO.getBranchIds() : new ArrayList<>();

        // 获取业务员ID
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();

        // 获取优惠券模板
        CouponTemplateDTO couponTemplateDTO = couponApi.getCouponTemplate(pageReqVO.getCouponTemplateId()).getCheckedData();
        if (ToolUtil.isEmpty(couponTemplateDTO)) {
            throw new ServiceException("未获取到优惠券模板信息!");
        }

        // 存储成功的结果列表
        List<CouponReceiveResultDTO> successList = new ArrayList<>();
        Map<Long, String> errMap = new HashMap<>();
        for (Long branchId : branchIds) {
            // 每次循环动态获取最新的业务员配额
//            ColonelQuotaDTO colonelQuotaDTO = couponApi.getColonelQuota(colonelId).getCheckedData();

            // 检查业务员的额度
//            if (colonelQuotaDTO.getRemainingQuota().compareTo(BigDecimal.ZERO) <= 0) {
//                CouponReceiveResultDTO errorResult = new CouponReceiveResultDTO();
//                errorResult.setCode(1);
//                errorResult.setMsg("剩余额度不足，无法发放优惠券");
//                return Collections.singletonList(errorResult);
//            }
//            if (colonelQuotaDTO.getRemainingQuota().compareTo(couponTemplateDTO.getDiscountAmt()) < 0) {
//                CouponReceiveResultDTO errorResult = new CouponReceiveResultDTO();
//                errorResult.setCode(1);
//                errorResult.setMsg("剩余额度不够发券，请调整业务员发券额度!");
//                return Collections.singletonList(errorResult);
//            }
            // 查询门店信息
            String branchName = memberCacheService.getBranchDto(branchId).getBranchName();
            if (ToolUtil.isEmpty(branchName)) {
                log.warn("未找到门店ID:{} 的信息，跳过处理", branchId);
                continue;
            }

            // 构建请求对象
            NormalCouponReceiveSingleAsyncReqVo reqVo = new NormalCouponReceiveSingleAsyncReqVo();
            reqVo.setBranchId(branchId);
            reqVo.setCouponTemplateId(couponTemplateDTO.getCouponTemplateId());
            reqVo.setCheckStock(true);

            // 同步发送领取优惠券请求
            CouponReceiveResultDTO couponReceiveResultDTO = couponApi.saveNormalCouponReceiveSingle(reqVo);
            if (couponReceiveResultDTO.getCode() == 0) {
                // 更新业务员额度
//                couponApi.updateColonelQuota(colonelId, couponTemplateDTO.getDiscountAmt());
                // 添加成功的结果
                couponReceiveResultDTO.setBranchName(branchName);
                successList.add(couponReceiveResultDTO);
            } else {
                log.warn("门店ID:{} 发券失败，原因: {}", branchId, couponReceiveResultDTO.getMsg());
                errMap.put(branchId, couponReceiveResultDTO.getMsg());
            }
        }
        // 返回结果
        if (MapUtils.isNotEmpty(errMap)) {
            List<CouponReceiveResultDTO> list = new ArrayList<>();
            errMap.forEach((key, value) -> {
                CouponReceiveResultDTO result = new CouponReceiveResultDTO();
                result.setCode(1);
                result.setBranchId(key);
                result.setMsg(value);
                list.add(result);
            });
            return list;
        }
        return successList;
    }
    @Override
    public ColonelBindAppletOpenidRespVO getWxB2bOpenidBindStatus() {
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        ColonelDTO colonel = memberCacheService.getColonel(colonelId);

        // 小程序配置
        AppletBaseConfigDTO appletBaseConfigDTO = memberCacheService.getAppletBaseConfigDTO(colonel.getSysCode());
        PayConfigDTO payConfigDTO = memberCacheService.getPayConfigDTO(colonel.getSysCode());

        // 获取支付配置
        PlatformMerchantDTO merchantDTO = platformMerchantApi.getPlatformMerchant(MerchantTypeEnum.COLONEL.getType(), colonelId, PayChannelEnum.WX_B2B_PAY.getCode()).getCheckedData();
        // 微信商户信息
        PlatformMerchantWxb2bDTO merchantWxb2bDTO = platformMerchantApi.getWxB2bPlatformMerchant(colonelId, MerchantTypeEnum.COLONEL.getType()).getCheckedData();

        // 绑定配置
        // 使用业务员ID 作为key防止, key过多
        String randomString = RandomUtil.randomString(32);
        String bindKey = colonelId + StringPool.UNDERSCORE + randomString;
        redisService.setCacheObject(RedisConstants.COLONEL_PRE_BIND_WX_OPENID + colonelId, randomString, 30L, TimeUnit.MINUTES);

        ColonelBindAppletOpenidRespVO respVO = ColonelBindAppletOpenidRespVO.builder()
                .wxB2bind(Objects.nonNull(merchantDTO) && StringUtils.isNotEmpty(merchantDTO.getAltMchNo()))
                .nickName(Objects.nonNull(merchantDTO) ? merchantDTO.getAltMchName() : StringPool.EMPTY)
                .profile(Objects.nonNull(merchantWxb2bDTO) ? merchantWxb2bDTO.getProfile() : StringPool.EMPTY)
                .bindKey(bindKey)
                .appid(Objects.nonNull(appletBaseConfigDTO) ? appletBaseConfigDTO.getAppId() : null)
                .platform(Objects.nonNull(payConfigDTO) ? payConfigDTO.getStoreOrderPayPlatform() : null)
                .build();
        if (Objects.nonNull(merchantDTO)) {
            respVO.setNickName(merchantDTO.getAltMchName());
        }
        return respVO;
    }

    @Override
    public ColonelBindPublishOpenidRespVO getPublishOpenidBindKey() {
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        ColonelDTO colonel = memberCacheService.getColonel(colonelId);
        // 小程序配置
        AppletBaseConfigDTO appletBaseConfigDTO = memberCacheService.getAppletBaseConfigDTO(colonel.getSysCode());

        // 绑定配置
        // 使用业务员ID 作为key防止, key过多
        String randomString = RandomUtil.randomString(32);
        String bindKey = colonelId + StringPool.UNDERSCORE + randomString;
        redisService.setCacheObject(RedisConstants.COLONEL_PRE_BIND_WX_PUBLISH_OPENID + randomString, colonelId, 30L, TimeUnit.MINUTES);

        return ColonelBindPublishOpenidRespVO.builder()
            .bindKey(bindKey)
            .appid(Objects.nonNull(appletBaseConfigDTO) ? appletBaseConfigDTO.getAppId() : null)
            .build();
    }

    @Override
    public List<MemColonelVisitLogRespVO> branchVisitDetailList(MemColonelAppCustomerListPageReqVO pageReqVO) {
        return memColonelVisitLogMapper.getBranchVisitLog(pageReqVO.getBranchId(), pageReqVO.getStartTime(), pageReqVO.getEndTime());
    }

    private boolean isValidPhone(String phone) {
        // 手机号格式校验正则表达式（中国大陆手机号）
        String regex = "^1[3-9]\\d{9}$";
        return phone.matches(regex);
    }

    private boolean isValidIdCard(String idCard) {
        // 身份证号格式校验正则表达式（支持15位和18位身份证号）
        String regex = "^(\\d{15}|\\d{17}[0-9Xx])$";
        return idCard.matches(regex);
    }

    /**
     * 获取今天以前的几天的日期，并格式化为 "yyyy_MM_dd" 格式的 Date 对象。
     * @return 格式化的昨天日期
     */
    public static Date subtractDay(int day) {
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();

        // 减去一天
        calendar.add(Calendar.DAY_OF_MONTH, day);

        // 设置日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy_MM_dd");

        // 将昨天的日期格式化为字符串
        String yesterdayStr = sdf.format(calendar.getTime());

        // 将字符串重新解析为 Date 对象
        try {
            return sdf.parse(yesterdayStr);
        } catch (Exception e) {
            throw new RuntimeException("Error parsing date", e);
        }
    }

    /**
     * 获取当前业务员及管理的下级业务员ID
     * @param colonelId 业务员ID
     * @return
     */
    private List<Long> getColonels(Long colonelId) {
        if(Objects.isNull(colonelId)) throw exception(APP_COLONEL_CHECK_COLONEL_ID);

        MemColonelRelationPageReqVO relationPageReqVO = new MemColonelRelationPageReqVO();
        relationPageReqVO.setAdminColonelId(colonelId);
        //查询该业务员是不是管理业务员
        List<MemColonelRelation> memColonelRelation = memColonelRelationMapper.getColonelRelation(relationPageReqVO);
        List<Long> colonelIds= new ArrayList<>();
        if(!memColonelRelation.isEmpty()){
            colonelIds= memColonelRelation.stream().map(MemColonelRelation::getColonelId).collect(Collectors.toList());
            colonelIds.add(colonelId);
        }else{
            colonelIds.add(colonelId);
        }
        return colonelIds;
    }

    @Override
    public PageResult<MemBranch> getBranchPage(MemBranchPageReqVO pageReqVO) {
        //获取当前业务员ID
        Long colonelId = SecurityUtils.getLoginUser().getSysUser().getColonelId();
        ColonelDTO colonel = memberCacheService.getColonel(colonelId);
        pageReqVO.setColonelId(colonelId);
        if (ToolUtil.isNotEmpty(colonel)){
            pageReqVO.setAreaId(colonel.getAreaId());
        }
        Page<MemBranch> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<MemBranch> memBranchPage = memBranchMapper.selectPage(page, pageReqVO);
        return new PageResult<>(memBranchPage.getRecords(), memBranchPage.getTotal());
    }
}
