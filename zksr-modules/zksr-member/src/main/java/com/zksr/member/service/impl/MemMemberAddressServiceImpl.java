package com.zksr.member.service.impl;

import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.convert.address.MemMemberAddressConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemMemberAddressMapper;
import com.zksr.member.domain.MemMemberAddress;
import com.zksr.member.controller.address.vo.MemMemberAddressPageReqVO;
import com.zksr.member.controller.address.vo.MemMemberAddressSaveReqVO;
import com.zksr.member.service.IMemMemberAddressService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 用户地址Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
@Slf4j
public class MemMemberAddressServiceImpl implements IMemMemberAddressService {
    @Autowired
    private MemMemberAddressMapper memMemberAddressMapper;

    /**
     * 新增用户地址
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemMemberAddress(MemMemberAddressSaveReqVO createReqVO) {
        // 插入
        MemMemberAddress memMemberAddress = MemMemberAddressConvert.INSTANCE.convert(createReqVO);
        try {
            memMemberAddressMapper.insert(memMemberAddress);
        } catch (Exception e) {
            log.error(" 保存用户地址失败,",e);
            throw new ServiceException("保存失败，请检查是否已存在该地址.");
        }
        // 返回
        return memMemberAddress.getId();
    }

    /**
     * 修改用户地址
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemMemberAddress(MemMemberAddressSaveReqVO updateReqVO) {
        try {
            memMemberAddressMapper.updateById(MemMemberAddressConvert.INSTANCE.convert(updateReqVO));
        } catch (Exception e) {
            log.error(" 更新用户地址失败,",e);
            throw new ServiceException("更新失败，请检查是否已存在该地址.");
        }
    }

    /**
     * 删除用户地址
     *
     * @param id ID主键
     */
    @Override
    public void deleteMemMemberAddress(Long id) {
        // 删除
        memMemberAddressMapper.deleteById(id);
    }

    /**
     * 批量删除用户地址
     *
     * @param ids 需要删除的用户地址主键
     * @return 结果
     */
    @Override
    public void deleteMemMemberAddressByIds(Long[] ids) {
        for(Long id : ids){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deleteMemMemberAddress(id);
        }
    }

    /**
     * 获得用户地址
     *
     * @param id ID主键
     * @return 用户地址
     */
    @Override
    public MemMemberAddress getMemMemberAddress(Long id) {
        return memMemberAddressMapper.selectById(id);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemMemberAddress> getMemMemberAddressPage(MemMemberAddressPageReqVO pageReqVO) {
        return memMemberAddressMapper.selectPage(pageReqVO);
    }

    private void validateMemMemberAddressExists(Long id) {
        if (memMemberAddressMapper.selectById(id) == null) {
//            throw exception(MEM_MEMBER_ADDRESS_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 用户地址 TODO 补充编号 ==========
    // ErrorCode MEM_MEMBER_ADDRESS_NOT_EXISTS = new ErrorCode(TODO 补充编号, "用户地址不存在");


}
