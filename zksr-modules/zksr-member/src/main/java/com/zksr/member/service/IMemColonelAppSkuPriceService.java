package com.zksr.member.service;

import com.zksr.member.api.branch.dto.BranchDTO;

import java.math.BigDecimal;

public interface IMemColonelAppSkuPriceService {

    /**
     * 获取本地配送销售价
     * @param branchDTO
     * @param skuId
     * @return
     */
    public BigDecimal getAreaSkuPrice(BranchDTO branchDTO, Integer unitSize, Long skuId);

    /**
     * 获取全国配送销售价
     * @param branchDTO
     * @param skuId
     * @return
     */
    public BigDecimal getSupplierSkuPrice(BranchDTO branchDTO, Integer unitSize, Long skuId);
}
