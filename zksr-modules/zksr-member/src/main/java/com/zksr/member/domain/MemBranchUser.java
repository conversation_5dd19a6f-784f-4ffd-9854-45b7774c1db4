package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 门店用户关联关系对象 mem_branch_user
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@TableName(value = "mem_branch_user")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemBranchUser extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 用户id */
    @Excel(name = "用户id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long memberId;

    /** 门店id */
    @Excel(name = "门店id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    /** 是否默认 */
    @Excel(name = "是否默认数据字典")
    private String isDefault = "N";

}
