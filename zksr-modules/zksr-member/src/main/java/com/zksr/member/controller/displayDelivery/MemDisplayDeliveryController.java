package com.zksr.member.controller.displayDelivery;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemDisplayDelivery;
import com.zksr.member.service.IMemDisplayDeliveryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.displayDelivery.vo.MemDisplayDeliveryPageReqVO;
import com.zksr.member.controller.displayDelivery.vo.MemDisplayDeliverySaveReqVO;
import com.zksr.member.controller.displayDelivery.vo.MemDisplayDeliveryRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 陈列计划兑付Controller
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Api(tags = "管理后台 - 陈列计划兑付接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/displayDelivery")
public class MemDisplayDeliveryController {
    @Autowired
    private IMemDisplayDeliveryService memDisplayDeliveryService;

    /**
     * 新增陈列计划兑付
     */
    @ApiOperation(value = "新增陈列计划兑付", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "陈列计划兑付", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemDisplayDeliverySaveReqVO createReqVO) {
        return success(memDisplayDeliveryService.insertMemDisplayDelivery(createReqVO));
    }

    /**
     * 修改陈列计划兑付
     */
    @ApiOperation(value = "修改陈列计划兑付", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "陈列计划兑付", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemDisplayDeliverySaveReqVO updateReqVO) {
            memDisplayDeliveryService.updateMemDisplayDelivery(updateReqVO);
        return success(true);
    }

    /**
     * 删除陈列计划兑付
     */
    @ApiOperation(value = "删除陈列计划兑付", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "陈列计划兑付", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deliveryIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] deliveryIds) {
        memDisplayDeliveryService.deleteMemDisplayDeliveryByDeliveryIds(deliveryIds);
        return success(true);
    }

    /**
     * 获取陈列计划兑付详细信息
     */
    @ApiOperation(value = "获得陈列计划兑付详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{deliveryId}")
    public CommonResult<MemDisplayDeliveryRespVO> getInfo(@PathVariable("deliveryId") Long deliveryId) {
        MemDisplayDelivery memDisplayDelivery = memDisplayDeliveryService.getMemDisplayDelivery(deliveryId);
        return success(HutoolBeanUtils.toBean(memDisplayDelivery, MemDisplayDeliveryRespVO.class));
    }

    /**
     * 分页查询陈列计划兑付
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得陈列计划兑付分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemDisplayDeliveryRespVO>> getPage(@Valid MemDisplayDeliveryPageReqVO pageReqVO) {
        PageResult<MemDisplayDelivery> pageResult = memDisplayDeliveryService.getMemDisplayDeliveryPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, MemDisplayDeliveryRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:delivery:add";
        /** 编辑 */
        public static final String EDIT = "member:delivery:edit";
        /** 删除 */
        public static final String DELETE = "member:delivery:remove";
        /** 列表 */
        public static final String LIST = "member:delivery:list";
        /** 查询 */
        public static final String GET = "member:delivery:query";
        /** 停用 */
        public static final String DISABLE = "member:delivery:disable";
        /** 启用 */
        public static final String ENABLE = "member:delivery:enable";
    }
}
