package com.zksr.member.controller.colonelTarget.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 业务员目标设置对象 mem_colonel_target
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Data
@ApiModel("业务员目标设置 - mem_colonel_target分页 Request VO")
public class MemColonelTargetSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long colonelTargetId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 业务员Id */
    @Excel(name = "业务员Id")
    @ApiModelProperty(value = "业务员Id")
    private Long colonelId;

    /** 目标年份 */
    @Excel(name = "目标年份")
    @ApiModelProperty(value = "目标年份")
    private String targetYear;

    /** 目标月份 */
    @Excel(name = "目标月份")
    @ApiModelProperty(value = "目标月份")
    private String targetMonth;

    /** 销售额 */
    @Excel(name = "销售额")
    @ApiModelProperty(value = "销售额")
    private BigDecimal salesMoney;

    /** 月新开客户数量 */
    @Excel(name = "月新开客户数量")
    @ApiModelProperty(value = "月新开客户数量")
    private Long monthNewCustomer;

    /** 月活动客户数量 */
    @Excel(name = "月活动客户数量")
    @ApiModelProperty(value = "月活动客户数量")
    private Long monthActivityCustomer;

    /** 月拜访客户数量 */
    @Excel(name = "月拜访客户数量")
    @ApiModelProperty(value = "月拜访客户数量")
    private Long monthVisitCustomer;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态 1正常 0停用 */
    @Excel(name = "状态 0 保存，1 设置完成")
    @ApiModelProperty(value = "状态 0 保存，1 设置完成")
    private Integer status;


    /** 月下单数量 */
    @Excel(name = "月下单数量")
    @ApiModelProperty(value = "月下单数量")
    private Long monthOrderCount;

    /** 月客单价 */
    @Excel(name = "月客单价")
    @ApiModelProperty(value = "月客单价")
    private BigDecimal monthAvgOrderValue;

    /** 月首次动销数量 */
    @ApiModelProperty(value = "月首次动销数量")
    @Excel(name = "月首次动销数量")
    private Long monthFirstSaleCount;
}
