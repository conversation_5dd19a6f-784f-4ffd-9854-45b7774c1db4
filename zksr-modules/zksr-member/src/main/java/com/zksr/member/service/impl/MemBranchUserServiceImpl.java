package com.zksr.member.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.controller.branchUser.vo.MemBranchUserPageReqVO;
import com.zksr.member.controller.branchUser.vo.MemBranchUserSaveReqVO;
import com.zksr.member.domain.MemBranchUser;
import com.zksr.member.mapper.MemBranchUserMapper;
import com.zksr.member.service.IMemBranchUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.MEM_BRANCH_USER_NOT_EXISTS;

/**
 * 门店用户关联关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Service
public class MemBranchUserServiceImpl implements IMemBranchUserService {
    @Autowired
    private MemBranchUserMapper memBranchUserMapper;

    /**
     * 新增门店用户关联关系
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemBranchUser(MemBranchUserSaveReqVO createReqVO) {
        // 插入
        MemBranchUser memBranchUser = HutoolBeanUtils.toBean(createReqVO, MemBranchUser.class);
        memBranchUserMapper.insert(memBranchUser);
        // 返回
        return memBranchUser.getSysCode();
    }

    /**
     * 修改门店用户关联关系
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemBranchUser(MemBranchUserSaveReqVO updateReqVO) {
        memBranchUserMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, MemBranchUser.class));
    }

    /**
     * 删除门店用户关联关系
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deleteMemBranchUser(Long sysCode) {
        // 删除
        memBranchUserMapper.deleteById(sysCode);
    }

    /**
     * 批量删除门店用户关联关系
     *
     * @param sysCodes 需要删除的门店用户关联关系主键
     * @return 结果
     */
    @Override
    public void deleteMemBranchUserBySysCodes(Long[] sysCodes) {
        for(Long sysCode : sysCodes){
            this.deleteMemBranchUser(sysCode);
        }
    }

    /**
     * 获得门店用户关联关系
     *
     * @param sysCode 平台商id
     * @return 门店用户关联关系
     */
    @Override
    public MemBranchUser getMemBranchUser(Long sysCode) {
        return memBranchUserMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemBranchUser> getMemBranchUserPage(MemBranchUserPageReqVO pageReqVO) {
        return memBranchUserMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MemBranchUser> getMemBranchUserByMemberId(Long memberId) {
        return memBranchUserMapper.getMemBranchUserByMemberId(memberId);
    }

    private void validateMemBranchUserExists(Long sysCode) {
        if (memBranchUserMapper.selectById(sysCode) == null) {
            throw exception(MEM_BRANCH_USER_NOT_EXISTS);
        }
    }

    @Override
    public Long countByBranchId(Long branchId) {
        return memBranchUserMapper.countByBranchId(branchId);
    }

    @Override
    public int saveMemBranchUser(MemBranchUser saveDto) {
        return memBranchUserMapper.insert(saveDto);
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 门店用户关联关系 TODO 补充编号 ==========
    // ErrorCode MEM_BRANCH_USER_NOT_EXISTS = new ErrorCode(TODO 补充编号, "门店用户关联关系不存在");


}
