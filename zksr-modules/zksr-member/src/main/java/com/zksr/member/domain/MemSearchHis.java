package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 搜索历史对象 mem_search_his
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@TableName(value = "mem_search_his")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemSearchHis extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 搜索历史id */
    @TableId
    private Long searchHisId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 门店用户id */
    @Excel(name = "门店用户id")
    private Long memberId;

    /** 门店id */
    @Excel(name = "门店id")
    private Long branchId;

    /** 搜索词 */
    @Excel(name = "搜索词")
    private String words;

}
