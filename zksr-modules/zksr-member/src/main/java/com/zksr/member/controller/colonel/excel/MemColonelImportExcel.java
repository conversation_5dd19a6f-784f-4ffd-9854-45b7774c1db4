package com.zksr.member.controller.colonel.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "用于业务员信息导入")
public class MemColonelImportExcel {

    @Excel(name = "业务员名称", headerColor = IndexedColors.RED)
    private String colonelName;

    @Excel(name = "性别", prompt = "1: 女 0：男",  headerColor = IndexedColors.RED)
    private String sexName;

    @Excel(name = "手机号",  headerColor = IndexedColors.RED)
    private String colonelPhone;

    @Excel(name = "业务员账号",  headerColor = IndexedColors.RED)
    private String userName;

    @Excel(name = "密码")
    private String password;

    @Excel(name = "所属城市", headerColor = IndexedColors.RED)
    private String areaName;

    @Excel(name = "职务", headerColor = IndexedColors.RED)
    private String colonelLevelName;

    @Excel(name = "是否业务管理员", headerColor = IndexedColors.RED)
    private String isColonelAdmin;

    @Excel(name = "上级管理员")
    private String pcolonelName;

    @Excel(name = "身份证号", headerColor = IndexedColors.RED)
    private String idcard;

    @Excel(name = "出生日期")
    private Date birthday;

    @Excel(name = "籍贯")
    private String birthplace;

    @Excel(name = "学历")
    private String edu;

    @Excel(name = "入职日期")
    private Date entryDate;

    @Excel(name = "是否启用")
    private String statusName;

    @Excel(name = "是否支持APP下单改价", headerColor = IndexedColors.RED)
    private String appOrderPriceAdjust;

    @Excel(name = "是否支持APP退货改价", headerColor = IndexedColors.RED)
    private String appAfterPriceAdjust;

    @Excel(name = "是否支持下单自动审核", headerColor = IndexedColors.RED)
    private String orderAutoApprove;

    @Excel(name = "提成系数", prompt = "1-100整数", headerColor = IndexedColors.RED)
    private BigDecimal percentageRate;

    @Excel(name = "联系地址")
    private String contactAddr;

    @Excel(name = "备注")
    private String memo;

    @Excel(name = "城市(省/市/县区)", headerColor = IndexedColors.RED)
    private String city;
}
