package com.zksr.member.service.impl;

import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemMemberInvoiceMapper;
import com.zksr.member.convert.invoice.MemMemberInvoiceConvert;
import com.zksr.member.domain.MemMemberInvoice;
import com.zksr.member.controller.invoice.vo.MemMemberInvoicePageReqVO;
import com.zksr.member.controller.invoice.vo.MemMemberInvoiceSaveReqVO;
import com.zksr.member.service.IMemMemberInvoiceService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 用户发票Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
@Slf4j
public class MemMemberInvoiceServiceImpl implements IMemMemberInvoiceService {
    @Autowired
    private MemMemberInvoiceMapper memMemberInvoiceMapper;

    /**
     * 新增用户发票
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemMemberInvoice(MemMemberInvoiceSaveReqVO createReqVO) {
        MemMemberInvoice memMemberInvoice = null;
        try {
            // 插入
            memMemberInvoice = MemMemberInvoiceConvert.INSTANCE.convert(createReqVO);
            memMemberInvoiceMapper.insert(memMemberInvoice);
        } catch (Exception e) {
            log.error(" 保存用户发票失败,",e);
            throw new ServiceException("保存失败，请检查是否已存在该发票信息.");
        }
        // 返回
        return memMemberInvoice.getId();
    }

    /**
     * 修改用户发票
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemMemberInvoice(MemMemberInvoiceSaveReqVO updateReqVO) {
        try {
            memMemberInvoiceMapper.updateById(MemMemberInvoiceConvert.INSTANCE.convert(updateReqVO));
        } catch (Exception e) {
            log.error(" 更新用户发票失败,",e);
            throw new ServiceException("更新失败，请检查是否已存在该发票信息.");
        }
    }

    /**
     * 删除用户发票
     *
     * @param id ID主键
     */
    @Override
    public void deleteMemMemberInvoice(Long id) {
        // 删除
        memMemberInvoiceMapper.deleteById(id);
    }

    /**
     * 批量删除用户发票
     *
     * @param ids 需要删除的用户发票主键
     * @return 结果
     */
    @Override
    public void deleteMemMemberInvoiceByIds(Long[] ids) {
        for(Long id : ids){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deleteMemMemberInvoice(id);
        }
    }

    /**
     * 获得用户发票
     *
     * @param id ID主键
     * @return 用户发票
     */
    @Override
    public MemMemberInvoice getMemMemberInvoice(Long id) {
        return memMemberInvoiceMapper.selectById(id);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemMemberInvoice> getMemMemberInvoicePage(MemMemberInvoicePageReqVO pageReqVO) {
        return memMemberInvoiceMapper.selectPage(pageReqVO);
    }

    private void validateMemMemberInvoiceExists(Long id) {
        if (memMemberInvoiceMapper.selectById(id) == null) {
//            throw exception(MEM_MEMBER_INVOICE_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 用户发票 TODO 补充编号 ==========
    // ErrorCode MEM_MEMBER_INVOICE_NOT_EXISTS = new ErrorCode(TODO 补充编号, "用户发票不存在");


}
