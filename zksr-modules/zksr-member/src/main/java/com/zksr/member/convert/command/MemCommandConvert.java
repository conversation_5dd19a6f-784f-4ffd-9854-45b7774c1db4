package com.zksr.member.convert.command;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.command.vo.CommandSaveVO;
import com.zksr.member.domain.MemCommand;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 菜单权限 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-02-10
*/
@Mapper
public interface MemCommandConvert {

    MemCommandConvert INSTANCE = Mappers.getMapper(MemCommandConvert.class);
//
//    MemCommandRespVO convert(MemCommand memCommand);
//
    MemCommand convert(CommandSaveVO commandSaveReq);
//
//    PageResult<MemCommandRespVO> convertPage(PageResult<MemCommand> memCommandPage);
}