package com.zksr.member.controller.colonelTarget.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 业务员门店目标设置返回对象 mem_colonel_branch_target
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@Data
@ApiModel("业务员门店目标设置 - ColonelBranchTargetRespVO Response VO")
public class ColonelBranchTargetRespVO {
    @ApiModelProperty(value = "主键Id")
    private Long colonelBranchTargetId;

    @ApiModelProperty(value = "平台商ID", hidden = true)
    private Long sysCode;

    @ApiModelProperty(value = "业务员ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelId;

    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店联系人")
    private String branchContactName;

    @ApiModelProperty(value = "门店联系电话")
    private String branchContactPhone;

    @ApiModelProperty(value = "城市区域ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    @ApiModelProperty(value = "城市区域名称")
    private String areaName;

    @ApiModelProperty(value = "目标年份")
    private String targetYear;

    @ApiModelProperty(value = "目标月份")
    private String targetMonth;

    @ApiModelProperty(value = "业务员门店目标销售额")
    private BigDecimal targetSalesMoney;

    @ApiModelProperty(value = "业务员门店实际销售额")
    private BigDecimal salesMoney;

    @ApiModelProperty(value = "备注")
    private String memo;


    @ApiModelProperty(value = "业务员目标销售额")
    private BigDecimal colonelTargetSalesMoney;

    @ApiModelProperty(value = "门店近一月实际销售额")
    private BigDecimal branchLastOneMonthSalesMoney;

    @ApiModelProperty(value = "业务员门店近二月实际销售额")
    private BigDecimal branchLastTwoMonthSalesMoney;
}
