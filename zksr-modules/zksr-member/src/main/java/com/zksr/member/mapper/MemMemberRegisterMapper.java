package com.zksr.member.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterAuditVO;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterRespVO;
import com.zksr.member.domain.MemBranchRegister;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemMemberRegister;
import com.zksr.member.controller.memberRegister.vo.MemMemberRegisterPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.constant.StatusConstants.*;


/**
 * 用户注册信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@Mapper
public interface MemMemberRegisterMapper extends BaseMapperX<MemMemberRegister> {
    default PageResult<MemMemberRegister> selectPage(MemMemberRegisterPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemMemberRegister>()
                    .eqIfPresent(MemMemberRegister::getMemberRegisterId, reqVO.getMemberRegisterId())
                    .eqIfPresent(MemMemberRegister::getSysCode, reqVO.getSysCode())
                    .likeIfPresent(MemMemberRegister::getMemberName, reqVO.getMemberName())
                    .likeIfPresent(MemMemberRegister::getBranchName, reqVO.getBranchName())
                    .eqIfPresent(MemMemberRegister::getAreaId, reqVO.getAreaId())
                    .eqIfPresent(MemMemberRegister::getBranchAddr, reqVO.getBranchAddr())
                    .eqIfPresent(MemMemberRegister::getLongitude, reqVO.getLongitude())
                    .eqIfPresent(MemMemberRegister::getLatitude, reqVO.getLatitude())
                    .likeIfPresent(MemMemberRegister::getUserName, reqVO.getUserName())
                    .eqIfPresent(MemMemberRegister::getPassword, reqVO.getPassword())
                    .eqIfPresent(MemMemberRegister::getBranchImages, reqVO.getBranchImages())
                    .eqIfPresent(MemMemberRegister::getApproveMan, reqVO.getApproveMan())
                    .eqIfPresent(MemMemberRegister::getApproveFlag, reqVO.getApproveFlag())
                    .eqIfPresent(MemMemberRegister::getMemberApproveFlag, reqVO.getMemberApproveFlag())
                    .eqIfPresent(MemMemberRegister::getMemberExpirationDate, reqVO.getMemberExpirationDate())
                    .eqIfPresent(MemMemberRegister::getBranchApproveFlag, reqVO.getBranchApproveFlag())
                    .eqIfPresent(MemMemberRegister::getBranchExpirationDate, reqVO.getBranchExpirationDate())
                    .eqIfPresent(MemMemberRegister::getMemo, reqVO.getMemo())
                    .eqIfPresent(MemMemberRegister::getStatus,reqVO.getStatus())
                    .eqIfPresent(MemMemberRegister::getColonelId,reqVO.getColonelId())
                    .eqIfPresent(MemMemberRegister::getChannelId,reqVO.getChannelId())
                    .betweenIfPresent(MemMemberRegister::getApproveDate, reqVO.getStartTime(), reqVO.getEndTime())
                    .applyScope(reqVO.getParams())
                .orderByDesc(MemMemberRegister::getMemberRegisterId));
    }

    /**
     * 批量审核
     * @param memberRegisterIds
     * @param approveMan
     */
    default void memberRegisterApprove(List<Long> memberRegisterIds,Long approveMan, MemMemberRegisterAuditVO auditVO){

//        update(null, new LambdaUpdateWrapper<MemMemberRegister>()
//                .set(MemMemberRegister::getApproveFlag,AUDIT_STATE_1)
//                .set(MemMemberRegister::getApproveDate,new Date())
//                .set(MemMemberRegister::getApproveMan,approveMan)
//                .set(null != auditVO,MemMemberRegister::getHdfkSupport, auditVO.getHdfkSupport())
//                .set(null != auditVO,MemMemberRegister::getHdfkMaxAmt, auditVO.getHdfkMaxAmt())
//                .set(null != auditVO,MemMemberRegister::getChannelId, auditVO.getChannelId())
//                .set(null != auditVO,MemMemberRegister::getIsPayOnline, auditVO.getIsPayOnline())
//                .set(null != auditVO,MemMemberRegister::getSalePriceCode, auditVO.getSalePriceCode())
//                .set(null != auditVO,MemMemberRegister::getColonelId, auditVO.getColonelId())
//
//                .in(MemMemberRegister::getMemberRegisterId,memberRegisterIds));

        LambdaUpdateWrapper<MemMemberRegister> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MemMemberRegister::getApproveFlag, AUDIT_STATE_1)
                .set(MemMemberRegister::getApproveDate, new Date())
                .set(MemMemberRegister::getApproveMan, approveMan);

        if (auditVO != null) {
            updateWrapper.set(MemMemberRegister::getHdfkSupport, auditVO.getHdfkSupport())
                    .set(MemMemberRegister::getHdfkMaxAmt, auditVO.getHdfkMaxAmt())
                    .set(MemMemberRegister::getChannelId, auditVO.getChannelId())
                    .set(MemMemberRegister::getIsPayOnline, auditVO.getIsPayOnline())
                    .set(MemMemberRegister::getSalePriceCode, auditVO.getSalePriceCode())
                    .set(MemMemberRegister::getColonelId, auditVO.getColonelId());
        }

        updateWrapper.in(MemMemberRegister::getMemberRegisterId, memberRegisterIds);
        update(null, updateWrapper);

    }

    /**
     * 批量停用
     * @param memberRegisterIds
     */
    default void deleteMemMemberRegisterByMemberRegisterIds(Long[] memberRegisterIds){
        update(null, new LambdaUpdateWrapper<MemMemberRegister>()
                .set(MemMemberRegister::getStatus,STATE_DISABLE)
                .in(MemMemberRegister::getMemberRegisterId,memberRegisterIds)
                .eq(MemMemberRegister::getApproveFlag,AUDIT_STATE_0));
    }

    default MemMemberRegister getMemMemberRegister(MemMemberRegisterRespVO respVO){
        LambdaQueryWrapperX<MemMemberRegister> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(MemMemberRegister::getUserName,respVO.getUserName());
        wrapper.eqIfPresent(MemMemberRegister::getSysCode, respVO.getSysCode());
        List<MemMemberRegister> rsList = selectList(wrapper);
        return CollectionUtils.isEmpty(rsList) ? null : rsList.get(0);

    }

    default void updateMemMemberRegisterBranchId(Long branchId, Long branchRegisterId){
        update(null, new LambdaUpdateWrapper<MemMemberRegister>()
                .eq(MemMemberRegister::getMemberRegisterId,branchRegisterId)
                .set(MemMemberRegister::getBranchId,branchId));
    }

    MemMemberRegister getMemberRegisterByBranchId(@Param("branchId") Long branchId);
}
