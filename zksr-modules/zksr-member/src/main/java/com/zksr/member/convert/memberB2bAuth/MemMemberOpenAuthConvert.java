package com.zksr.member.convert.memberB2bAuth;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.b2bAuth.dto.MemMemberOpenAuthDTO;
import com.zksr.member.domain.MemMemberOpenAuth;
import com.zksr.member.controller.memberB2bAuth.vo.MemMemberOpenAuthRespVO;
import com.zksr.member.controller.memberB2bAuth.vo.MemMemberOpenAuthSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* b2b openid 认证 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-08-15
*/
@Mapper
public interface MemMemberOpenAuthConvert {

    MemMemberOpenAuthConvert INSTANCE = Mappers.getMapper(MemMemberOpenAuthConvert.class);

    MemMemberOpenAuthRespVO convert(MemMemberOpenAuth memMemberOpenAuth);

    MemMemberOpenAuth convert(MemMemberOpenAuthSaveReqVO memMemberOpenAuthSaveReq);

    PageResult<MemMemberOpenAuthRespVO> convertPage(PageResult<MemMemberOpenAuth> memMemberOpenAuthPage);

    MemMemberOpenAuthDTO convertDTO(MemMemberOpenAuth bind);

    MemMemberOpenAuth convertPO(MemMemberOpenAuthDTO authDTO);
}