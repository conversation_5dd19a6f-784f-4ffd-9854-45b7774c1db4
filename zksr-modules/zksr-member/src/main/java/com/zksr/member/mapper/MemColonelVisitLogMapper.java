package com.zksr.member.mapper;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.elasticsearch.domain.EsColonelAppBranch;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogRespVO;
import com.zksr.trade.api.order.dto.ColonelOrderDaySettleDTO;
import com.zksr.trade.api.order.vo.ColonelOrderDaySettleVO;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogCollectRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemColonelVisitLog;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 业务员拜访日志Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@Mapper
public interface MemColonelVisitLogMapper extends BaseMapperX<MemColonelVisitLog> {
    default PageResult<MemColonelVisitLog> selectPage(MemColonelVisitLogPageReqVO reqVO) {
        List<Long> colonelVisitLogIdList= new ArrayList<>();
        if(ToolUtil.isNotEmpty(reqVO.getColonelVisitLogIdList())){
            reqVO.getColonelVisitLogIdList().forEach(item->{
                colonelVisitLogIdList.add(Long.parseLong(item));
            });
        }
        return selectPage(reqVO, new LambdaQueryWrapperX<MemColonelVisitLog>()
                    .eqIfPresent(MemColonelVisitLog::getColonelVisitLogId, reqVO.getColonelVisitLogId())
                    .eqIfPresent(MemColonelVisitLog::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemColonelVisitLog::getColonelId, reqVO.getColonelId())
                    .eqIfPresent(MemColonelVisitLog::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(MemColonelVisitLog::getVisitFlag, reqVO.getVisitFlag())
                .betweenIfPresent(MemColonelVisitLog::getCreateTime, reqVO.getStartTime(), reqVO.getEndTime())
                .inIfPresent(MemColonelVisitLog::getBranchId, reqVO.getBranchIdList())
                .inIfPresent(MemColonelVisitLog::getColonelVisitLogId,colonelVisitLogIdList )
                .orderByDesc(MemColonelVisitLog::getCreateTime));
    }

    default MemColonelVisitLog checkBranchVisitLog(Long colonelId) {
        String beginDate = DateUtil.format(new Date(),"yyyy-MM-dd")+" 00:00:00";
        String endDate = DateUtil.format(new Date(),"yyyy-MM-dd")+" 23:59:59";

        return selectOne(new LambdaQueryWrapperX<MemColonelVisitLog>()
                .eqIfPresent(MemColonelVisitLog::getColonelId, colonelId)
                .eqIfPresent(MemColonelVisitLog::getVisitFlag, 0)
                .betweenIfPresent(MemColonelVisitLog::getCreateTime, beginDate, endDate)
        );
    }

    Long getColonelVisitLogNumByDate(@Param("colonelId") String colonelId,@Param("startTime")String startTime, @Param("endTime")String endTime,@Param("visitIntervalTime")String visitIntervalTime);

    List<MemColonelVisitLog> getLatestRecordsForEachColonel(@Param("startTime") String startTime, @Param("endTime") String endTime);

    Page<MemColonelVisitLogCollectRespVO> getColonelVisitLogByParam(@Param("reqVO")MemColonelVisitLogPageReqVO reqVO,@Param("page") Page<MemColonelVisitLogPageReqVO> page);

    default Long getColonelVisitLogNumByBranchId(Long branchId,String startTime,String endTime) {
        return selectCount(new LambdaQueryWrapperX<MemColonelVisitLog>()
                .eqIfPresent(MemColonelVisitLog::getBranchId, branchId)
                .betweenIfPresent(MemColonelVisitLog::getCreateTime, startTime, endTime)
                .eqIfPresent(MemColonelVisitLog::getVisitFlag, 1)
        );
    }

    /**
     *  根据业务员获取拜访门店信息（根据业务员和日期进行分组）
     * @param reqVO
     * @return
     */
    List<ColonelOrderDaySettleDTO> getColonelVisitLogInfo(ColonelOrderDaySettleVO reqVO);

    /**
     *  根据门店和日期区间查询每月门店最近一次的拜访时间
     * @param branchId 门店ID
     * @param sysCode 系统编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<EsColonelAppBranch> getBranchVisitLogByBranchIdAndDate(@Param("branchId") Long branchId, @Param("sysCode") Long sysCode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     *  根据门店和日期区间查询门店拜访记录
     * @param branchId 门店ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<MemColonelVisitLogRespVO> getBranchVisitLog(@Param("branchId") Long branchId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据时间区间获取某业务员的拜访信息
     * @param colonelId 业务员ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 业务员拜访信息
     */
    default List<MemColonelVisitLog> getLogListByColonelIdAndDate(Long colonelId, Date startTime, Date endTime) {
        return selectList(new LambdaQueryWrapperX<MemColonelVisitLog>()
                .eqIfPresent(MemColonelVisitLog::getColonelId, colonelId)
                .betweenIfPresent(MemColonelVisitLog::getCreateTime, startTime, endTime)
        );
    }

    /**
     * 根据门店ID获取距离上次拜访的天!
     * @param branchId
     * @return
     */
    Long getDaysSinceLastVisitByBranchId(@Param("branchId") Long branchId);


}
