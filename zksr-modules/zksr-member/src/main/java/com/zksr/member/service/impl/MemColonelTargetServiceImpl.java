package com.zksr.member.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.service.EsColonelAppBranchService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonelApp.dto.PageDataDTO;
import com.zksr.member.controller.colonelApp.dto.ColonelAppTargetDetailDTO;
import com.zksr.member.controller.colonelApp.dto.ColonelAppTargetRespDTO;
import com.zksr.member.controller.colonelApp.vo.ColonelAppTargetPageVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelActualSaleRespVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelTargetRespVO;
import com.zksr.member.controller.relation.vo.MemColonelRelationPageReqVO;
import com.zksr.member.convert.colonelTarget.MemColonelTargetConvert;
import com.zksr.member.domain.MemColonel;
import com.zksr.member.domain.MemColonelMonthSettle;
import com.zksr.member.domain.MemColonelRelation;
import com.zksr.member.mapper.*;
import com.zksr.member.service.*;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.domain.MemColonelTarget;
import com.zksr.member.controller.colonelTarget.vo.MemColonelTargetPageReqVO;
import com.zksr.member.controller.colonelTarget.vo.MemColonelTargetSaveReqVO;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 业务员目标设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Service
public class MemColonelTargetServiceImpl implements IMemColonelTargetService {
    @Autowired
    private MemColonelTargetMapper memColonelTargetMapper;
    @Autowired
    private MemColonelMonthSettleMapper memColonelMonthSettleMapper;
    @Autowired
    private MemColonelMapper memColonelMapper;
    @Autowired
    private MemColonelRelationMapper memColonelRelationMapper;
    @Autowired
    private MemBranchMapper memBranchMapper;

    @Autowired
    private IMemberCacheService memberCacheService;
    @Autowired
    private IMemColonelService memColonelService;
    @Autowired
    private EsColonelAppBranchService colonelAppBranchService;
    @Autowired
    private IMemColonelBranchTargetService memColonelBranchTargetService;
    @Autowired
    private RedisService redisService;

    /**
     * 新增业务员目标设置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemColonelTarget(MemColonelTargetSaveReqVO createReqVO) {
        // 校验业务员目标设置是否存在
        List<MemColonelTarget> colonelTargetList = memColonelTargetMapper.selectListByColonelIdsAndYearMonth(
                Collections.singletonList(createReqVO.getColonelId()), createReqVO.getTargetYear(), createReqVO.getTargetMonth(), null);
        if (ToolUtil.isNotEmpty(colonelTargetList) || !colonelTargetList.isEmpty()) {
            throw exception(MEM_COLONEL_TARGET_EXISTS);
        }

        // 插入
        MemColonelTarget memColonelTarget = HutoolBeanUtils.toBean(createReqVO, MemColonelTarget.class);
//        memColonelTarget.setStatus(StatusConstants.AUDIT_STATE_0);  //新增默认未审核
        memColonelTargetMapper.insert(memColonelTarget);

        // 新增业务员门店目标设置
        memColonelBranchTargetService.insertMemColonelBranchTarget(Collections.singletonList(memColonelTarget));
        // 返回
        return memColonelTarget.getColonelTargetId();
    }

    @Transactional
    @Override
    public void insertBatchMemColonelTarget(List<MemColonelTargetSaveReqVO> createReqVO) {
        if (ToolUtil.isNotEmpty(createReqVO)) {
            createReqVO.forEach(memColonelTargetReqVo -> {
                // 存在 则更新数据，否则就新增
                if (ToolUtil.isNotEmpty(memColonelTargetReqVo.getColonelTargetId())) {
                    memColonelTargetReqVo.setMemo(ToolUtil.isEmpty(memColonelTargetReqVo.getMemo()) ? null : memColonelTargetReqVo.getMemo());
                    updateMemColonelTarget(memColonelTargetReqVo);
                } else {
                    insertMemColonelTarget(memColonelTargetReqVo);
                }
            });

//            // 插入
//            List<MemColonelTarget> memColonelTargetList =MemColonelTargetConvert.INSTANCE.convertList(createReqVO);
//            memColonelTargetMapper.insertBatch(memColonelTargetList);
//
//            // 新增业务员门店目标设置
//            memColonelBranchTargetService.insertMemColonelBranchTarget(memColonelTargetList);
        }
    }

    /**
     * 修改业务员目标设置
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemColonelTarget(MemColonelTargetSaveReqVO updateReqVO) {
        MemColonelTarget memColonelTarget = HutoolBeanUtils.toBean(updateReqVO, MemColonelTarget.class);
        memColonelTargetMapper.updateById(memColonelTarget);

        memColonelBranchTargetService.updateMemColonelBranchTarget(memColonelTarget);
    }

    /**
     * 删除业务员目标设置
     *
     * @param colonelTargetId 主键ID
     */
    @Override
    public void deleteMemColonelTarget(Long colonelTargetId) {
        // 删除
        memColonelTargetMapper.deleteById(colonelTargetId);
    }

    /**
     * 批量删除业务员目标设置
     *
     * @param colonelTargetIds 需要删除的业务员目标设置主键
     * @return 结果
     */
    @Override
    public void deleteMemColonelTargetByColonelTargetIds(Long[] colonelTargetIds) {
        for(Long colonelTargetId : colonelTargetIds){
            MemColonelTarget memColonelTarget = memColonelTargetMapper.selectById(colonelTargetId);
            List<Long> branchTargetIds =memColonelBranchTargetService.getBranchTargetIds(memColonelTarget.getColonelId(), memColonelTarget.getTargetYear(), memColonelTarget.getTargetMonth());
            memColonelBranchTargetService.deleteMemColonelBranchTargetByColonelBranchTargets(branchTargetIds);
            this.deleteMemColonelTarget(colonelTargetId);
        }
    }

    /**
     * 获得业务员目标设置
     *
     * @param colonelTargetId 主键ID
     * @return 业务员目标设置
     */
    @Override
    public MemColonelTarget getMemColonelTarget(Long colonelTargetId) {
        return memColonelTargetMapper.selectById(colonelTargetId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemColonelTargetRespVO> getMemColonelTargetPage(MemColonelTargetPageReqVO pageReqVO) {
        Page<MemColonelTargetPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<MemColonelTargetRespVO> respPage = memColonelTargetMapper.selectPage(pageReqVO, page);
        return new PageResult<>(respPage.getRecords(), respPage.getTotal());
    }

    @Override
    public void disable(Long colonelTargetId) {
        MemColonelTarget target = memColonelTargetMapper.selectById(colonelTargetId);
        target.setStatus(StatusConstants.STATE_DISABLE);
        target.setUpdateBy(SecurityUtils.getUsername());
        target.setUpdateTime(DateUtils.getNowDate());
        memColonelTargetMapper.updateById(target);
    }

    @Override
    public void enable(Long colonelTargetId) {
        MemColonelTarget target = memColonelTargetMapper.selectById(colonelTargetId);
        target.setStatus(StatusConstants.STATE_ENABLE);
        target.setUpdateBy(SecurityUtils.getUsername());
        target.setUpdateTime(DateUtils.getNowDate());
        memColonelTargetMapper.updateById(target);
    }


    @Override
    public List<MemColonelActualSaleRespVO> getColonelActualSalesInfo(MemColonelTargetPageReqVO pageReqVO) {
        /**
         * 1、查询业务员，
         * 若是平台商，则查询所有业务员，
         * 若是运营商，则查询运营区域城市内的业务员，
         */
        List<MemColonel> colonels = memColonelService.getMemColonelList();
        if (ToolUtil.isEmpty(colonels) || colonels.isEmpty()) {
            throw exception(MEM_COLONEL_NOT_EXISTS);
        }
        Map<Long, MemColonel> colonelMap = CollectionUtils.convertMap(colonels, MemColonel::getColonelId);

        // 查询业务员月结算数据
        List<Long> colonelIds = colonels.stream().map(MemColonel::getColonelId).collect(Collectors.toList());
        List<String> targetMonths = new ArrayList<>();
        targetMonths.add(DateUtils.getYearMonthRange(-1, DateUtils.getNowDate()));
        targetMonths.add(DateUtils.getYearMonthRange(-2, DateUtils.getNowDate()));
        List<MemColonelActualSaleRespVO> colonelActualSaleRespVOS = memColonelMonthSettleMapper.getColonelSettleMonthList(colonelIds, targetMonths);


        // 查询业务员目标数据（根据年月）
        String targetYear = ToolUtil.isEmptyReturn(pageReqVO.getTargetYear(), DateUtils.parseDateToStr(DateUtils.YYYY, new Date()));
        String targetMonth = ToolUtil.isEmptyReturn(pageReqVO.getTargetMonth(), DateUtils.parseDateToStr("MM", new Date()));
        List<MemColonelTarget> colonelTargetList = memColonelTargetMapper.selectListByColonelIdsAndYearMonth(colonelIds, targetYear, targetMonth, null);
        Map<Long, MemColonelTarget> colonelTargetMap = CollectionUtils.convertMap(colonelTargetList, MemColonelTarget::getColonelId);
        // 补全合并返回结果集
        colonelActualSaleRespVOS.addAll(colonels.stream().filter(
                colonel -> !colonelActualSaleRespVOS.stream().map(MemColonelActualSaleRespVO::getColonelId).collect(Collectors.toList()).contains(colonel.getColonelId())
                ).map(colonel -> {
                    return new MemColonelActualSaleRespVO()
                            .setColonelId(colonel.getColonelId())
                            .setSaleAmt(BigDecimal.ZERO)
                            .setAddBranchQty(NumberPool.LONG_ZERO)
                            .setSaleBranchQty(NumberPool.LONG_ZERO)
                            .setVisitQty(NumberPool.LONG_ZERO)
                            .setOrderCount(NumberPool.LONG_ZERO)
                            .setAvgOrderValue(BigDecimal.ZERO)
                            .setFirstSaleCount(NumberPool.LONG_ZERO)
                            ;
                }).collect(Collectors.toList())
        );

        List<MemColonelActualSaleRespVO> writeBackList = new ArrayList<>();
        colonelActualSaleRespVOS.forEach(colonelActualSaleRespVO -> {
            colonelActualSaleRespVO.setStatus(NumberPool.LOWER_GROUND); // 状态为-1 说明是当前时间两月的数据
            MemColonel colonel = colonelMap.get(colonelActualSaleRespVO.getColonelId());
            colonelActualSaleRespVO.setColonelName(colonel.getColonelName())
                    .setAreaId(colonel.getAreaId())
                    .setColonelLevel(colonel.getColonelLevel());

            if (ToolUtil.isNotEmpty(colonelActualSaleRespVO.getAreaId())) {
               AreaDTO areaDTO =  memberCacheService.getAreaDto(colonelActualSaleRespVO.getAreaId());
                colonelActualSaleRespVO.setAreaName(areaDTO.getAreaName());
            }

            String yearMonth = colonelActualSaleRespVO.getTargetYearMonth();
            if(ToolUtil.isNotEmpty(yearMonth)) {
                //yearMonth的格式为yyyy-MM  但是我要把它改成yyMM格式
                String yearLastTwo = yearMonth.substring(2, 4);
                String month = yearMonth.substring(5);
                colonelActualSaleRespVO.setFirstSaleCount(memBranchMapper.getFirstSaleBranchNumByColonelIdAndYearMonth(colonelActualSaleRespVO.getColonelId(), yearLastTwo + month));
            }

            // 数据回写   产品要求如此
            if (colonelTargetMap.containsKey(colonelActualSaleRespVO.getColonelId())) {
                // 判断新增集合中是否存在当前业务员，若存在则不回写
                boolean isTrue = writeBackList.stream().anyMatch(wb -> Objects.equals(wb.getColonelId(), colonelActualSaleRespVO.getColonelId()));
                if (!isTrue) {
                    MemColonelTarget colonelTarget = colonelTargetMap.get(colonelActualSaleRespVO.getColonelId());
                    MemColonelActualSaleRespVO  saleRespVO = new MemColonelActualSaleRespVO();
                    saleRespVO.setColonelId(colonelActualSaleRespVO.getColonelId())
                            .setColonelTargetId(colonelTarget.getColonelTargetId())
                            .setColonelName(colonelActualSaleRespVO.getColonelName())
                            .setAreaId(colonelActualSaleRespVO.getAreaId())
                            .setAreaName(colonelActualSaleRespVO.getAreaName())
                            .setColonelLevel(colonelActualSaleRespVO.getColonelLevel())
                            .setStatus(colonelTarget.getStatus())
                            .setTargetYearMonth(colonelTarget.getTargetYear() + StringPool.DASH + colonelTarget.getTargetMonth())
                            .setSaleAmt(colonelTarget.getSalesMoney())
                            .setAddBranchQty(colonelTarget.getMonthNewCustomer())
                            .setSaleBranchQty(colonelTarget.getMonthActivityCustomer())
                            .setVisitQty(colonelTarget.getMonthVisitCustomer())
                            .setOrderCount(colonelTarget.getMonthOrderCount())
                            .setAvgOrderValue(colonelTarget.getMonthAvgOrderValue())
                            .setFirstSaleCount(colonelTarget.getMonthFirstSaleCount());

                    writeBackList.add(saleRespVO);
                }
            }
        });
        colonelActualSaleRespVOS.addAll(writeBackList);
    return colonelActualSaleRespVOS;
    }

    @Transactional
    @Override
    public void settingCompleted(Long[] colonelTargetIds) {
        for(Long colonelTargetId : colonelTargetIds){
            enable(colonelTargetId);
        }
    }

    @Override
    public ColonelAppTargetRespDTO getColonelTargetReportPage(ColonelAppTargetPageVO pageVO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
//        if (!StringUtils.equals(loginUser.getFuncScop(), SystemConstants.FUNC_SCOPE_COLONEL)
//                || !StringUtils.equals(loginUser.getFuncScop(), SystemConstants.FUNC_SCOPE_DC)) {
//            throw new ServiceException("非业务员账号");
//        }
        List<Long> keys = new ArrayList<>();
        if (StringUtils.equals(loginUser.getFuncScop(), SystemConstants.FUNC_SCOPE_COLONEL)) {
            MemColonelRelationPageReqVO relationPageReqVO = new MemColonelRelationPageReqVO();
            relationPageReqVO.setAdminColonelId(loginUser.getColonelId());
            //查询当前业务员下 管理的所有业务员关系
            List<MemColonelRelation> memColonelRelation = memColonelRelationMapper.getColonelRelation(relationPageReqVO);
            keys = Stream.concat(
                    memColonelRelation.stream().map(MemColonelRelation::getColonelId),
                    // 加上当前业务员 key
                    Stream.of(loginUser.getColonelId())
            ).collect(Collectors.toList());
        } else {
            List<MemColonel> colonels = memColonelService.getMemColonelList();
            keys = colonels.stream().map(MemColonel::getColonelId).collect(Collectors.toList());
        }


        // 查询业务员目标数据
        pageVO.setStatus(StatusConstants.STATE_ENABLE);
        List<MemColonelTarget> colonelTargetList = memColonelTargetMapper.selectListByColonelIdsAndParams(keys, pageVO);
        if (ToolUtil.isEmpty(colonelTargetList)) {
            return new ColonelAppTargetRespDTO();
        }

        // 查询业务员目标实际数据（业务员月结表）
        String settleMonthDate = pageVO.getTargetYear() + StringPool.DASH + pageVO.getTargetMonth();
        List<MemColonelMonthSettle> colonelMonthSettleList = memColonelMonthSettleMapper.getListInfoByColonelIdAndSysCode(new HashSet<>(keys), loginUser.getSysCode(), settleMonthDate);
        Map<Long, MemColonelMonthSettle> colonelMonthSettleMap = CollectionUtils.convertMap(colonelMonthSettleList, MemColonelMonthSettle::getColonelId);

        ColonelAppTargetRespDTO colonelAppTargetRespDTO = new ColonelAppTargetRespDTO();
        SimpleDateFormat sdf = new SimpleDateFormat("yyMM");
        colonelTargetList.forEach(colonelTarget -> {
            // 业务员目标总销售额
            colonelAppTargetRespDTO.setTargetSalesAmtTotal(colonelAppTargetRespDTO.getTargetSalesAmtTotal().add(colonelTarget.getSalesMoney()));
            // 业务员目标总新开客户数
            colonelAppTargetRespDTO.setTargetAddBranchNumTotal(colonelAppTargetRespDTO.getTargetAddBranchNumTotal() + colonelTarget.getMonthNewCustomer());
            // 业务员目标总活跃客户数
            colonelAppTargetRespDTO.setTargetSaleBranchNumTotal(colonelAppTargetRespDTO.getTargetSaleBranchNumTotal() + colonelTarget.getMonthActivityCustomer());
            // 业务员目标总拜访客户数
            colonelAppTargetRespDTO.setTargetVisitBranchNumTotal(colonelAppTargetRespDTO.getTargetVisitBranchNumTotal() + colonelTarget.getMonthVisitCustomer());
            // 业务员目标总下单笔数
            colonelAppTargetRespDTO.setTargetOrderNumTotal(colonelAppTargetRespDTO.getTargetOrderNumTotal() + ToolUtil.isEmptyReturn(colonelTarget.getMonthOrderCount(), NumberPool.LONG_ZERO));
            // 业务员目标总客单价
            colonelAppTargetRespDTO.setTargetAvgPriceTotal(colonelAppTargetRespDTO.getTargetAvgPriceTotal().add(ToolUtil.isEmptyReturn(colonelTarget.getMonthAvgOrderValue(), BigDecimal.ZERO)));
            // 业务员目标总动销客户
            colonelAppTargetRespDTO.setTargetFirstSaleBranchNumTotal(colonelAppTargetRespDTO.getTargetFirstSaleBranchNumTotal() + ToolUtil.isEmptyReturn(colonelTarget.getMonthFirstSaleCount(), NumberPool.LONG_ZERO));

            // 当前业务员月结数据
            MemColonelMonthSettle colonelMonthSettle = colonelMonthSettleMap.get(colonelTarget.getColonelId());
            if (ToolUtil.isNotEmpty(colonelMonthSettle)) {
                BigDecimal salesAmt = ToolUtil.isEmptyReturn(colonelMonthSettle.getBranchOrderAmt(), BigDecimal.ZERO)
                        .add(ToolUtil.isEmptyReturn(colonelMonthSettle.getBusinessOrderAmt(), BigDecimal.ZERO))
                        .subtract(ToolUtil.isEmptyReturn(colonelMonthSettle.getBranchRefundAmt(), BigDecimal.ZERO));
                // 业务员实际总销售额
                colonelAppTargetRespDTO.setSalesAmtTotal(colonelAppTargetRespDTO.getSalesAmtTotal().add(salesAmt));
                // 业务员实际总新开客户数
                colonelAppTargetRespDTO.setAddBranchNumTotal(colonelAppTargetRespDTO.getAddBranchNumTotal() + ToolUtil.isEmptyReturn(colonelMonthSettle.getAddBranchQty(), NumberPool.LONG_ZERO));
                // 业务员实际总活跃客户数
                colonelAppTargetRespDTO.setSaleBranchNumTotal(colonelAppTargetRespDTO.getSaleBranchNumTotal() + ToolUtil.isEmptyReturn(colonelMonthSettle.getSaleBranchQty(), NumberPool.LONG_ZERO));
                // 业务员实际总拜访客户数
                colonelAppTargetRespDTO.setVisitBranchNumTotal(colonelAppTargetRespDTO.getVisitBranchNumTotal() + ToolUtil.isEmptyReturn(colonelMonthSettle.getVisitQty(), NumberPool.LONG_ZERO));
                // 业务员实际总下单笔数
                colonelAppTargetRespDTO.setOrderNumTotal(colonelAppTargetRespDTO.getOrderNumTotal() + ToolUtil.isEmptyReturn(colonelMonthSettle.getOrderQty(), NumberPool.LONG_ZERO));
                // 业务员实际总客单价(总销售额/总下单笔数) 如果销售额为0则客单价为0
                colonelAppTargetRespDTO.setAvgPriceTotal(colonelAppTargetRespDTO.getAvgPriceTotal().add(ToolUtil.isEmptyReturn(salesAmt.divide(new BigDecimal(colonelMonthSettle.getOrderQty()), 2, BigDecimal.ROUND_HALF_UP), BigDecimal.ZERO)));
                // 业务员实际总动销客户
                String yearMonth = sdf.format(new Date());
                colonelAppTargetRespDTO.setFirstSaleBranchNumTotal(memBranchMapper.getFirstSaleBranchNumByColonelIdAndYearMonth(colonelTarget.getColonelId(),yearMonth));

            }
            //获取缓存数据
            PageDataDTO pageDataDTO = redisService.getCacheObject(CacheConstants.getColonelAppPageDataKey(colonelTarget.getColonelId(), DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate())));
            if (ToolUtil.isNotEmpty(pageDataDTO)) {
                // 业务员当天实际销售额
                BigDecimal saleDateAmt = ToolUtil.isEmptyReturn(pageDataDTO.getBranchOrderAmt(), BigDecimal.ZERO)
                        .add(ToolUtil.isEmptyReturn(pageDataDTO.getBusinessOrderAmt(), BigDecimal.ZERO))
                        .subtract(ToolUtil.isEmptyReturn(pageDataDTO.getBranchRefundAmt(), BigDecimal.ZERO));
                // 业务员实际总销售额
                colonelAppTargetRespDTO.setSalesAmtTotal(colonelAppTargetRespDTO.getSalesAmtTotal().add(saleDateAmt));
                // 业务员实际总新开客户数
                colonelAppTargetRespDTO.setAddBranchNumTotal(colonelAppTargetRespDTO.getAddBranchNumTotal() + ToolUtil.isEmptyReturn(pageDataDTO.getAddBranchQty(), NumberPool.LONG_ZERO));
                // 业务员实际总活跃客户数
                colonelAppTargetRespDTO.setSaleBranchNumTotal(colonelAppTargetRespDTO.getSaleBranchNumTotal()  + ToolUtil.isEmptyReturn(pageDataDTO.getSaleBranchQty(), NumberPool.LONG_ZERO));
                // 业务员实际总拜访客户数
                colonelAppTargetRespDTO.setVisitBranchNumTotal(colonelAppTargetRespDTO.getVisitBranchNumTotal()  + ToolUtil.isEmptyReturn(pageDataDTO.getVisitQty(), NumberPool.LONG_ZERO));
            }


        });

        com.github.pagehelper.Page<MemColonelTarget> page = PageUtils.startPage(pageVO);
        pageVO.setStatus(StatusConstants.STATE_ENABLE);
        List<MemColonelTarget> colonelTargePage = memColonelTargetMapper.selectListByColonelIdsAndParams(keys, pageVO);

        colonelAppTargetRespDTO.setTotal(page.getTotal());
        colonelAppTargetRespDTO.setColonelAppTargetDetailDTOList(
                colonelTargePage.stream().map(colonelTarget -> {
                    ColonelDTO colonelDTO = memberCacheService.getColonel(colonelTarget.getColonelId());
                    // 查询业务员管理门店数量
                    Long manageBrnahcNum = memBranchMapper.selectBranchCountByColonel(colonelDTO.getColonelId());

                    ColonelAppTargetDetailDTO colonelAppTargetDetailDTO = new ColonelAppTargetDetailDTO();
                    colonelAppTargetDetailDTO
                            .setColonelId(colonelTarget.getColonelId())
                            .setColonelName(colonelDTO.getColonelName())
                            .setColonelPhone(colonelDTO.getColonelPhone())
                            .setAreaId(colonelDTO.getAreaId())
                            .setManageBranchNum(manageBrnahcNum)
                            .setTargetSalesAmt(colonelTarget.getSalesMoney())
                            .setTargetAddBranchNum(colonelTarget.getMonthNewCustomer())
                            .setTargetSaleBranchNum(colonelTarget.getMonthActivityCustomer())
                            .setTargetVisitBranchNum(colonelTarget.getMonthVisitCustomer())
                            .setTargetOrderNum(colonelTarget.getMonthOrderCount())
                            .setTargetAvgPrice(colonelTarget.getMonthAvgOrderValue())
                            .setTargetFirstSaleBranchNum(colonelTarget.getMonthFirstSaleCount())
                            .setSex(colonelDTO.getSex())
                            .setTargetYear(colonelTarget.getTargetYear())
                            .setTargetMonth(colonelTarget.getTargetMonth())
                    ;


                    AreaDTO areaDTO = memberCacheService.getAreaDto(colonelDTO.getAreaId());
                    if (ToolUtil.isNotEmpty(areaDTO)) {
                        colonelAppTargetDetailDTO.setAreaName(areaDTO.getAreaName());
                    }

                    // 当前业务员月结数据
                    MemColonelMonthSettle colonelMonthSettle = colonelMonthSettleMap.get(colonelTarget.getColonelId());
                    if (ToolUtil.isNotEmpty(colonelMonthSettle)) {
                        BigDecimal salesAmt = ToolUtil.isEmptyReturn(colonelMonthSettle.getBranchOrderAmt(), BigDecimal.ZERO).add(ToolUtil.isEmptyReturn(colonelMonthSettle.getBusinessOrderAmt(), BigDecimal.ZERO))
                                        .subtract(ToolUtil.isEmptyReturn(colonelMonthSettle.getBranchRefundAmt(), BigDecimal.ZERO));
                        colonelAppTargetDetailDTO.setSalesAmt(colonelAppTargetDetailDTO.getSalesAmt().add(salesAmt));
                        colonelAppTargetDetailDTO.setAddBranchNum(colonelAppTargetDetailDTO.getAddBranchNum() + ToolUtil.isEmptyReturn(colonelMonthSettle.getAddBranchQty(), NumberPool.LONG_ZERO));
                        colonelAppTargetDetailDTO.setSaleBranchNum(colonelAppTargetDetailDTO.getSaleBranchNum() + ToolUtil.isEmptyReturn(colonelMonthSettle.getSaleBranchQty(), NumberPool.LONG_ZERO));
                        colonelAppTargetDetailDTO.setVisitBranchNum(colonelAppTargetDetailDTO.getVisitBranchNum() + ToolUtil.isEmptyReturn(colonelMonthSettle.getVisitQty(), NumberPool.LONG_ZERO));
                        colonelAppTargetDetailDTO.setOrderNum(colonelAppTargetDetailDTO.getOrderNum() + ToolUtil.isEmptyReturn(colonelMonthSettle.getOrderQty(), NumberPool.LONG_ZERO));
                        colonelAppTargetDetailDTO.setAvgPrice(colonelAppTargetDetailDTO.getAvgPrice().add(ToolUtil.isEmptyReturn(salesAmt.divide(new BigDecimal(colonelMonthSettle.getOrderQty()), 2, BigDecimal.ROUND_HALF_UP), BigDecimal.ZERO)));
                        String yearMonth = sdf.format(new Date());
                        Long firstSaleBranchNum=  memBranchMapper.getFirstSaleBranchNumByColonelIdAndYearMonth(colonelTarget.getColonelId(),yearMonth);
                        colonelAppTargetDetailDTO.setFirstSaleBranchNum(ToolUtil.isEmptyReturn(colonelAppTargetDetailDTO.getFirstSaleBranchNum(), NumberPool.LONG_ZERO)+firstSaleBranchNum);
                    }

                    //获取缓存数据 业务员当天数据
                    PageDataDTO pageDataDTO = redisService.getCacheObject(CacheConstants.getColonelAppPageDataKey(colonelTarget.getColonelId(), DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate())));
                    if (ToolUtil.isNotEmpty(pageDataDTO)) {
                        // 业务员当天实际销售额
                        BigDecimal saleDateAmt = ToolUtil.isEmptyReturn(pageDataDTO.getBranchOrderAmt(), BigDecimal.ZERO)
                                .add(ToolUtil.isEmptyReturn(pageDataDTO.getBusinessOrderAmt(), BigDecimal.ZERO))
                                .subtract(ToolUtil.isEmptyReturn(pageDataDTO.getBranchRefundAmt(), BigDecimal.ZERO));
                        colonelAppTargetDetailDTO.setSalesAmt(colonelAppTargetDetailDTO.getSalesAmt().add(saleDateAmt));
                        colonelAppTargetDetailDTO.setAddBranchNum(colonelAppTargetDetailDTO.getAddBranchNum() + ToolUtil.isEmptyReturn(pageDataDTO.getAddBranchQty(), NumberPool.LONG_ZERO));
                        colonelAppTargetDetailDTO.setSaleBranchNum(colonelAppTargetDetailDTO.getSaleBranchNum() + ToolUtil.isEmptyReturn(pageDataDTO.getSaleBranchQty(), NumberPool.LONG_ZERO));
                        colonelAppTargetDetailDTO.setVisitBranchNum(colonelAppTargetDetailDTO.getVisitBranchNum() + ToolUtil.isEmptyReturn(pageDataDTO.getVisitQty(), NumberPool.LONG_ZERO));
                    }

                    return colonelAppTargetDetailDTO;

                }).collect(Collectors.toList())
        );




        return colonelAppTargetRespDTO;
    }

    @Override
    public BigDecimal getColonelMonthlySalesTarget(Long colonelId) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 拆分出年份和月份
        int targetYear = currentDate.getYear(); // 获取年份，例如 2024
        int targetMonth = currentDate.getMonthValue(); // 获取月份，例如 12
        // 调用 Mapper 方法传递年份和月份
        return memColonelTargetMapper.getColonelMonthlySalesTarget(colonelId, targetYear, targetMonth);
    }

    private void validateMemColonelTargetExists(Long colonelTargetId) {
        if (memColonelTargetMapper.selectById(colonelTargetId) == null) {
            throw exception(MEM_COLONEL_TARGET_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 业务员目标设置 TODO 补充编号 ==========
    // ErrorCode MEM_COLONEL_TARGET_NOT_EXISTS = new ErrorCode(TODO 补充编号, "业务员目标设置不存在");


}
