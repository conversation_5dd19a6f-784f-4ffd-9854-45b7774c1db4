package com.zksr.member.service.impl;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.controller.loginHis.vo.MemLoginHisPageReqVO;
import com.zksr.member.controller.loginHis.vo.MemLoginHisSaveReqVO;
import com.zksr.member.convert.loginHis.MemLoginHisConvert;
import com.zksr.member.domain.MemLoginHis;
import com.zksr.member.mapper.MemLoginHisMapper;
import com.zksr.member.service.IMemLoginHisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 登录历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class MemLoginHisServiceImpl implements IMemLoginHisService {
    @Autowired
    private MemLoginHisMapper memLoginHisMapper;

    /**
     * 新增登录历史
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertMemLoginHis(MemLoginHisSaveReqVO createReqVO) {
        // 插入
        MemLoginHis memLoginHis = MemLoginHisConvert.INSTANCE.convert(createReqVO);

        //校验数据 tp+member_id+branch_id+port+date_id+device_id 联合唯一，一天只记录一条记录
        if (ToolUtil.isNotEmpty(memLoginHisMapper.selectMemLoginHis(memLoginHis))) {
           return null;
        }

        memLoginHisMapper.insert(memLoginHis);
        // 返回
        return memLoginHis.getLoginHisId();
    }

    /**
     * 修改登录历史
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateMemLoginHis(MemLoginHisSaveReqVO updateReqVO) {
        memLoginHisMapper.updateById(MemLoginHisConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除登录历史
     *
     * @param loginHisId 登录历史id
     */
    @Override
    public void deleteMemLoginHis(Long loginHisId) {
        // 删除
        memLoginHisMapper.deleteById(loginHisId);
    }

    /**
     * 批量删除登录历史
     *
     * @param loginHisIds 需要删除的登录历史主键
     * @return 结果
     */
    @Override
    public void deleteMemLoginHisByLoginHisIds(Long[] loginHisIds) {
        for(Long loginHisId : loginHisIds){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deleteMemLoginHis(loginHisId);
        }
    }

    /**
     * 获得登录历史
     *
     * @param loginHisId 登录历史id
     * @return 登录历史
     */
    @Override
    public MemLoginHis getMemLoginHis(Long loginHisId) {
        return memLoginHisMapper.selectById(loginHisId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<MemLoginHis> getMemLoginHisPage(MemLoginHisPageReqVO pageReqVO) {
        return memLoginHisMapper.selectPage(pageReqVO);
    }

//    private void validateMemLoginHisExists(Long loginHisId) {
//        if (memLoginHisMapper.selectById(loginHisId) == null) {
//            throw exception(MEM_LOGIN_HIS_NOT_EXISTS);
//        }
//    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 登录历史 TODO 补充编号 ==========
    // ErrorCode MEM_LOGIN_HIS_NOT_EXISTS = new ErrorCode(TODO 补充编号, "登录历史不存在");


}
