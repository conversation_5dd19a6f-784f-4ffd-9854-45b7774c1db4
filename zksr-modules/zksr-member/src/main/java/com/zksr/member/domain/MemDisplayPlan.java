package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 陈列计划对象 mem_display_plan
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@TableName(value = "mem_display_plan")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemDisplayPlan extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 陈列计划编号，主键 */
    @TableId(type = IdType.ASSIGN_ID)
    @Excel(name = "陈列计划编号")
    private Long planId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 陈列类型ID */
    @Excel(name = "陈列类型ID")
    private Long displayTypeId;

    /** 门店ID */
    @Excel(name = "门店ID")
    private Long branchId;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditTime;

    /** 审核状态（0:未审核,1：已审核,2：已作废） */
    @Excel(name = "审核状态", readConverterExp = "0=:未审核,1：已审核,2：已作废")
    private Integer auditState;

    /** 生效类型（0:未生效，1：生效中，2：已完成，3：已终止） */
    @Excel(name = "生效类型", readConverterExp = "0=:未生效，1：生效中，2：已完成，3：已终止")
    private Integer type;

    /** 终止原因 */
    @Excel(name = "终止原因")
    private String terminateReason;

    /** 删除状态(0:正常，2：删除) */
    private Integer delFlag;

}
