package com.zksr.member.controller.displayRegister.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 陈列计划打卡记录对象 mem_display_register
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Data
@ApiModel("陈列计划打卡记录 - mem_display_register Response VO")
public class MemDisplayRegisterRespVO {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "备注")
    private Long registerId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 陈列计划ID */
    @Excel(name = "陈列计划ID")
    @ApiModelProperty(value = "陈列计划ID")
    private Long planId;

    /** 照片 */
    @Excel(name = "照片")
    @ApiModelProperty(value = "照片")
    private String pic;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 删除状态(0:正常，2：删除) */
    @ApiModelProperty(value = "备注")
    private Long delFlag;

}
