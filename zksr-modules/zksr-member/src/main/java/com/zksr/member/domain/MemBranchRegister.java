package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 门店注册信息对象 mem_branch_register
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@TableName(value = "mem_branch_register")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemBranchRegister extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 门店注册信息ID */
    @TableId
    private Long branchRegisterId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /**
     * 门店编码
     */
    @Excel(name = "门店编码")
    private String branchNo;

    /** 门店名称 */
    @Excel(name = "门店名称")
    private String branchName;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 门店地址 */
    @Excel(name = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 业务员id */
    @Excel(name = "业务员id")
    private Long colonelId;

    /** 渠道id */
    @Excel(name = "渠道id")
    private Long channelId;

    /** 门头照 */
    @Excel(name = "门头照")
    private String branchImages;

    /** 审核人 */
    @Excel(name = "审核人")
    private Long approveMan;

    /** 审核标识 */
    @Excel(name = "审核标识")
    private Integer approveFlag;

    /** 审核时间 */
    @Excel(name = "审核时间")
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveDate;

    /** 门店自动审核标识 */
    @Excel(name = "门店自动审核标识")
    private Integer branchApproveFlag;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    /** 用户类型 0 新用户  1老用户 */
    @Excel(name = "用户类型")
    private Integer userType;

    /** 电子围栏入驻商ID信息，以,逗号间隔 */
    @Excel(name = "电子围栏入驻商ID信息")
    private String supplierIds;

    @ApiModelProperty("三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    private Long threeAreaCityId;

    @ApiModelProperty("门店id")
    private Long branchId;

}
