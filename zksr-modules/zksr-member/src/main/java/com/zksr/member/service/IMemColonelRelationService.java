package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.controller.relation.vo.MemColonelRelationRespVO;
import com.zksr.member.domain.MemColonelRelation;
import com.zksr.member.controller.relation.vo.MemColonelRelationPageReqVO;
import com.zksr.member.controller.relation.vo.MemColonelRelationSaveReqVO;

/**
 * 业务员关系Service接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface IMemColonelRelationService {

    /**
     * 新增业务员关系
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemColonelRelation(@Valid MemColonelRelationSaveReqVO createReqVO);

    /**
     * 修改业务员关系
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemColonelRelation(@Valid MemColonelRelationSaveReqVO updateReqVO);

    /**
     * 删除业务员关系
     *
     * @param colonelRelationId 主键ID
     */
    public void deleteMemColonelRelation(Long colonelRelationId);

    /**
     * 批量删除业务员关系
     *
     * @param colonelRelationIds 需要删除的业务员关系主键集合
     * @return 结果
     */
    public void deleteMemColonelRelationByColonelRelationIds(Long[] colonelRelationIds);

    /**
     * 获得业务员关系
     *
     * @param colonelRelationId 主键ID
     * @return 业务员关系
     */
    public MemColonelRelation getMemColonelRelation(Long colonelRelationId);

    /**
     * 获得业务员关系分页
     *
     * @param pageReqVO 分页查询
     * @return 业务员关系分页
     */
    PageResult<MemColonelRelationRespVO> getMemColonelRelationPage(MemColonelRelationPageReqVO pageReqVO);

}
