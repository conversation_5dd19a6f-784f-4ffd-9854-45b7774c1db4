package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 业务员拜访日志对象 mem_colonel_visit_log
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@TableName(value = "mem_colonel_visit_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemColonelVisitLog extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long colonelVisitLogId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    private Long colonelId;

    /** 门店ID */
    @Excel(name = "门店ID")
    private Long branchId;

    /** 签到经度 */
    @Excel(name = "签到经度")
    private String signInLongitude;

    /** 签到纬度 */
    @Excel(name = "签到纬度")
    private String signInLatitude;

    /** 签到地址 */
    @Excel(name = "签到地址")
    private String signInAddress;

    /** 签到距离 */
    @Excel(name = "签到距离")
    private String signInDistance;

    /** 签到图片链接：多个以英文,隔开 */
    @Excel(name = "签到图片链接：多个以英文,隔开")
    private String signInImgUrls;

    /** 签到时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签到时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date signInDate;

    /** 签退经度 */
    @Excel(name = "签退经度")
    private String signOutLongitude;

    /** 签退维度 */
    @Excel(name = "签退维度")
    private String signOutLatitude;

    /** 签退地址 */
    @Excel(name = "签退地址")
    private String signOutAddress;

    /** 签退距离 */
    @Excel(name = "签退距离")
    private String signOutDistance;

    /** 签退时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签退时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date signOutDate;

    /** 拜访状态 0签到，1签退，2作废 */
    @Excel(name = "拜访状态 0签到，1签退，2作废")
    private Integer visitFlag;

    /** 拜访间隔时间 */
    @Excel(name = "拜访间隔时间")
    private String visitIntervalTime;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 删除状态(0:正常，2：删除) */
    private Integer delFlag;

}
