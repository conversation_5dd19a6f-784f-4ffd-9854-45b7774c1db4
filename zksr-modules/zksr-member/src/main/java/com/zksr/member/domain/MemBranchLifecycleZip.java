package com.zksr.member.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 门店生命周期拉链对象 mem_branch_lifecycle_zip
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@TableName(value = "mem_branch_lifecycle_zip")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemBranchLifecycleZip extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 门店生命周期拉链表ID */
    @TableId
    private Long branchLifecycleZipId;

    /** 平台商id */
    @Excel(name = "平台商id")
    private Long sysCode;

    /** 门店id */
    @Excel(name = "门店id")
    private Long branchId;

    /** 上一次生命周期code */
    @Excel(name = "上一次生命周期code")
    private Integer lastLifecycleStage;

    /** 生命周期code */
    @Excel(name = "生命周期code")
    private Integer lifecycleStage;

    /** 开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 触发事件备注 */
    @Excel(name = "触发事件备注")
    private String startMemo;

}
