package com.zksr.member.convert.colonel;

import com.zksr.member.api.colonelApp.dto.PageDataDTO;
import com.zksr.member.domain.MemColonelDaySettle;
import com.zksr.member.domain.MemColonelMonthSettle;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/4/9
 * @desc 业务员对象封装转换器
 */
@Mapper
public interface MemberColonelMonthSettleConvert {

    MemberColonelMonthSettleConvert INSTANCE = Mappers.getMapper(MemberColonelMonthSettleConvert.class);


//    MemColonelMonthSettle convert(MemColonelMonthSettle memColonelMonthSettle);

}
