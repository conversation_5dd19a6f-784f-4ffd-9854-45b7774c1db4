package com.zksr.member.controller.searchHis.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/1/20 11:28
 * @注释
 */
@ApiModel("搜索历史")
@Data
public class AssociationSpuReqVO {

    /** 关键词 */
    @ApiModelProperty(value = "关键词")
    @Excel(name = "关键词")
    private String keywords;

    /** spuIds */
    @ApiModelProperty(value = "spuIds")
    @Excel(name = "spuIds")
    @JsonSerialize(using = CustomLongSerialize.class)
    private List<Long> spuIds;
}
