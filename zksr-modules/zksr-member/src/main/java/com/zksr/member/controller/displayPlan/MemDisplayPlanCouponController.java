package com.zksr.member.controller.displayPlan;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemDisplayPlanCoupon;
import com.zksr.member.service.IMemDisplayPlanCouponService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanCouponPageReqVO;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanCouponSaveReqVO;
import com.zksr.member.controller.displayPlan.vo.MemDisplayPlanCouponRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 陈列计划优惠明细Controller
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Api(tags = "管理后台 - 陈列计划优惠明细接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/displayPlanCoupon")
public class MemDisplayPlanCouponController {
    @Autowired
    private IMemDisplayPlanCouponService memDisplayPlanCouponService;

    /**
     * 新增陈列计划优惠明细
     */
    @ApiOperation(value = "新增陈列计划优惠明细", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "陈列计划优惠明细", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemDisplayPlanCouponSaveReqVO createReqVO) {
        return success(memDisplayPlanCouponService.insertMemDisplayPlanCoupon(createReqVO));
    }

    /**
     * 修改陈列计划优惠明细
     */
    @ApiOperation(value = "修改陈列计划优惠明细", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "陈列计划优惠明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemDisplayPlanCouponSaveReqVO updateReqVO) {
            memDisplayPlanCouponService.updateMemDisplayPlanCoupon(updateReqVO);
        return success(true);
    }

    /**
     * 删除陈列计划优惠明细
     */
    @ApiOperation(value = "删除陈列计划优惠明细", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "陈列计划优惠明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planCouponIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] planCouponIds) {
        memDisplayPlanCouponService.deleteMemDisplayPlanCouponByPlanCouponIds(planCouponIds);
        return success(true);
    }

    /**
     * 获取陈列计划优惠明细详细信息
     */
    @ApiOperation(value = "获得陈列计划优惠明细详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{planCouponId}")
    public CommonResult<MemDisplayPlanCouponRespVO> getInfo(@PathVariable("planCouponId") Long planCouponId) {
        MemDisplayPlanCoupon memDisplayPlanCoupon = memDisplayPlanCouponService.getMemDisplayPlanCoupon(planCouponId);
        return success(HutoolBeanUtils.toBean(memDisplayPlanCoupon, MemDisplayPlanCouponRespVO.class));
    }

    /**
     * 分页查询陈列计划优惠明细
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得陈列计划优惠明细分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemDisplayPlanCouponRespVO>> getPage(@Valid MemDisplayPlanCouponPageReqVO pageReqVO) {
        PageResult<MemDisplayPlanCoupon> pageResult = memDisplayPlanCouponService.getMemDisplayPlanCouponPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, MemDisplayPlanCouponRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:coupon:add";
        /** 编辑 */
        public static final String EDIT = "member:coupon:edit";
        /** 删除 */
        public static final String DELETE = "member:coupon:remove";
        /** 列表 */
        public static final String LIST = "member:coupon:list";
        /** 查询 */
        public static final String GET = "member:coupon:query";
        /** 停用 */
        public static final String DISABLE = "member:coupon:disable";
        /** 启用 */
        public static final String ENABLE = "member:coupon:enable";
    }
}
