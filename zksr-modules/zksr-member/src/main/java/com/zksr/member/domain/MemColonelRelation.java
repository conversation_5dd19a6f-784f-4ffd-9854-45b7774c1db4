package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 业务员关系对象 mem_colonel_relation
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@TableName(value = "mem_colonel_relation")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemColonelRelation extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelRelationId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 管理业务员ID */
    @Excel(name = "管理业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long adminColonelId;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 删除状态  (0正常  2已删除) */
    @ApiModelProperty(value = "状态", example = "0")
    private String delFlag;

}
