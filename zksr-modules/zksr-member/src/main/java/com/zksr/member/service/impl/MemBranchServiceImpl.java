package com.zksr.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alicp.jetcache.Cache;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.vo.BranchBalanceRespVO;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.domain.ImportErrorVo;
import com.zksr.common.core.domain.erp.ErpBean;
import com.zksr.common.core.domain.vo.openapi.BranchOpenDTO;
import com.zksr.common.core.domain.vo.openapi.MemBranchSyncReqVO;
import com.zksr.common.core.domain.vo.openapi.SyncBranchSendDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncBranchCallDTO;
import com.zksr.common.core.enums.CouponReceiveType;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.SysYesNoEnum;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.enums.request.VisualTemplateType;
import com.zksr.common.core.exception.ErrorCode;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.*;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.core.utils.file.FileUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsColonelAppBranch;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.common.elasticsearch.service.EsColonelAppBranchService;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisCarService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.OpenapiSecurityUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.file.FileApi;
import com.zksr.file.api.model.ImportResultVo;
import com.zksr.file.api.model.SysFile;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.BranchSupplierDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.branch.excel.MemBranchImportExcel;
import com.zksr.member.api.branch.vo.BranchDeleteReqVO;
import com.zksr.member.api.branch.vo.MemberBranchReqVO;
import com.zksr.member.api.branchLifecycle.dto.ColonelBranchLifecycleQtyDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.dto.MemMemberSaveReqVO;
import com.zksr.member.controller.branch.balance.dto.MemBranchBalancePageReqDTO;
import com.zksr.member.controller.branch.dto.MemBranchRespDTO;
import com.zksr.member.controller.branch.vo.*;
import com.zksr.member.convert.branch.BranchConvert;
import com.zksr.member.domain.*;
import com.zksr.member.mapper.*;
import com.zksr.member.mq.MemberAmapMqProducer;
import com.zksr.member.mq.MemberAppMqProducer;
import com.zksr.member.mq.MemberMqProducer;
import com.zksr.member.service.IMemBranchService;
import com.zksr.member.service.IMemColonelBranchZipService;
import com.zksr.member.service.IMemMemberService;
import com.zksr.member.service.IMemberCacheService;
import com.zksr.product.api.areaChannelPrice.AreaChannelPriceApi;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.api.coupon.vo.NormalCouponReceiveSingleAsyncReqVo;
import com.zksr.report.api.homePages.dto.HomePagesBranchDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.system.api.RemoteUserService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.channel.ChannelApi;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dcArea.DcAreaApi;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.FileImportApi;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import com.zksr.system.api.group.GroupApi;
import com.zksr.system.api.group.dto.GroupDTO;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import com.zksr.system.api.openapi.OpenApi;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.supplierArea.SupplierAreaApi;
import com.zksr.system.api.supplierArea.dto.SupplierAreaDTO;
import com.zksr.system.api.supplierArea.dto.SupplierDzwlAreaDTO;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.EsBranchOrderSalesRespDTO;
import com.zksr.trade.api.order.vo.EsBranchOrderSalesReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;
import static com.zksr.common.redis.enums.RedisSyncConstants.SYNC_AREA_BRANCH;
import static com.zksr.member.enums.AppErrorCodeConstants.APP_COLONEL_CHECK_COLONEL_ID;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 门店信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Service
@Slf4j
public class MemBranchServiceImpl implements IMemBranchService {
    @Autowired
    private MemBranchMapper memBranchMapper;

    @Autowired
    private Cache<Long, BranchDTO> branchDTOCache;

    @Autowired
    private MemBranchSupplierMapper memBranchSupplierMapper;

    @Autowired
    private MemberAmapMqProducer amapMqProducer;

    @Autowired
    private MemBranchUserMapper memBranchUserMapper;
    @Autowired
    private MemColonelVisitLogMapper memColonelVisitLogMapper;

    @Autowired
    private DcAreaApi dcAreaApi;

    @Autowired
    private AreaApi areaApi;

    @Autowired
    private AreaCityApi areaCityApi;

    @Autowired
    private OpenApi openApi;

    @Autowired
    private GroupApi groupApi;
    @Autowired
    private OrderApi orderApi;

    @Autowired
    private IMemberCacheService memberCacheServiceImpl;

    @Autowired
    private SupplierAreaApi supplierAreaApi;

    @Autowired
    private ChannelApi channelApi;

    @Autowired
    private MemColonelMapper memColonelMapper;

    @Autowired
    private MemMemberMapper memMemberMapper;

    @Autowired
    private MemberMqProducer memberMqProducer;

    @Autowired
    @Qualifier("areaClassBranchListDtoCache")
    private Cache<Long, List<AreaClassDTO>> areaClassBranchListDtoCache;

    @Autowired
    private EsColonelAppBranchService colonelAppBranchService;

    @Autowired
    private IMemberCacheService memberCacheService;

    @Autowired
    private MemberAppMqProducer memberAppMqProducer;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private IMemColonelBranchZipService memColonelBranchZipService;
    @Autowired
    @Lazy
    private IMemMemberService memMemberService;
    @Autowired
    private RedisCarService redisCarService;

    @Resource
    private RemoteUserService remoteUserService;
    @Resource
    private CouponApi couponService;

    @Resource
    private SupplierApi remoteSupplierApi;

    @Autowired
    private RedisService redisService;
    @Resource
    private FileImportApi fileImportApi;
    @Resource
    private SpuApi spuApi;
    @Resource
    private AreaChannelPriceApi areaChannelPriceApi;

    @Resource
    private AccountApi accountApi;

    @Autowired
    private FileApi fileApi;

    /**
     * 新增门店信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertMemBranch(MemBranchSaveReqVO createReqVO) {
        // 插入
        MemBranch memBranch = HutoolBeanUtils.toBean(createReqVO, MemBranch.class);
        String s = branchVerify(memBranch,null);
        if (StringUtils.isNotEmpty(s)){
            throw new ServiceException(s);
        }
        if (ToolUtil.isNotEmpty(createReqVO.getColonelId())&& !Objects.equals(0L,createReqVO.getColonelId()) && !memBranchColonelNotArea(createReqVO.getAreaId(), createReqVO.getColonelId())){
            throw exception(MEM_BRANCH_COLONEL_NOT_AREA);
        }
        memBranchMapper.insert(memBranch);
        // 返回
        //mq校验电子围栏并绑定城市展示分类
        //如果经纬度变更,调用MQ绑定入驻商
        bindSupplier(createReqVO, memBranch);

        // 更新门店信息 将门店ID写入到门店编号中
        memBranch.setBranchNo(memBranch.getBranchId().toString());
        memBranchMapper.updateById(memBranch);
        if (createReqVO.getSyncCreateAccount() == null) {
            //根据门店联系手机号 校验用户账号是否已存在， 存在，则直接绑定用户，不存在则创建完成后，再绑定用户
            MemMember memMember = memMemberMapper.selectByMobileAndOpenid(memBranch.getSysCode(), memBranch.getContactPhone(), null, null);
            boolean isExist = true;
            if (ToolUtil.isEmpty(memMember)) {
                memMember = new MemMember();
                if (!memBranch.getContactPhone().matches("\\d+") || memBranch.getContactPhone().length() != 11) {
                    throw exception(MEM_MEMBER_USERNAME_CHECK_ERR);
                }
                isExist = false;
                // 新增用户
                memMember.setMemberPhone(memBranch.getContactPhone())
                        .setMemberName(memBranch.getContactName())
                        .setStatus(StatusConstants.FLAG_TRUE)
                        .setUserName(memBranch.getContactPhone())
                        .setPassword(SecurityUtils.encryptPassword(memberCacheService.getDefaultPassword(memBranch.getSysCode())))
                        .setIsShopManager(NumberPool.INT_ONE) // 默认设置为店长用户
                ;
                memMemberMapper.insert(memMember);
            }
            // 新增门店用户绑定关系
            memBranchUserMapper.insert(
                    MemBranchUser.builder()
                            .memberId(memMember.getMemberId())
                            .branchId(memBranch.getBranchId())
                            .isDefault(isExist ? STATUS_2 : STATUS_1).build() // 存在，不设置为默认门店，新增用户设置为默认门店
            );
        } else {
            //0714 增加同步创建用户 开关 0.关 1.开
            if (createReqVO.getSyncCreateAccount() == 1) {
                MemMemberSaveReqVO reqVo = new MemMemberSaveReqVO();
                reqVo.setUserName(createReqVO.getContactPhone());
                reqVo.setMemberName(createReqVO.getContactName());
                reqVo.setRegisterColonelId(createReqVO.getColonelId());
                reqVo.setBranchIds(Collections.singletonList(memBranch.getBranchId()));
                reqVo.setPassword(createReqVO.getPassword());
                reqVo.setMemberPhone(createReqVO.getContactPhone());
                try {
                    memMemberService.insertMemMember(reqVo);
                } catch (Exception e) {
                    log.info("门店编码为{}的门店注册同步创建用户失败:", memBranch.getBranchId(), e);
                    String result = MEM_BRANCH_USER_CREATE_ERR.getMsg().replace("{msg}", e.getMessage());
                    ErrorCode errorCode = new ErrorCode(MEM_BRANCH_USER_CREATE_ERR.getCode(), result);
                    createReqVO.setErrorCode(errorCode);
                }
            } else {
                log.info("门店编码:{} 不同步创建账号:", memBranch.getBranchId());
            }
        }
        if(ToolUtil.isNotEmpty(createReqVO.getColonelId())){
            createReqVO.setBranchId(memBranch.getBranchId());
            memColonelBranchZipService.insertMemColonelBranchZip(createReqVO,memBranch);
        }
        processCouponForBranchRegistration(memBranch.getBranchId(), memBranch.getSysCode(),memBranch.getAreaId());

        return memBranch.getBranchId();
    }

    /**
     * 门店校验
     * @param branch
     * @return
     */
    public String branchVerify(MemBranch branch,Long branchId){
        List<MemBranch> memBranches = memBranchMapper.selectList(new LambdaQueryWrapper<MemBranch>().eq(MemBranch::getAreaId, branch.getAreaId()).eq(MemBranch::getBranchName, branch.getBranchName()).eq(MemBranch::getContactPhone, branch.getContactPhone()).ne(ObjectUtil.isNotNull(branchId),MemBranch::getBranchId,branchId));
        if (StringUtils.isNotEmpty(memBranches)){
            return "已存在相同的门店和电话组合，请重新填写门店资料或电话";
        }

        List<MemBranch> memBranches1 = memBranchMapper.selectList(new LambdaQueryWrapper<MemBranch>().eq(MemBranch::getAreaId, branch.getAreaId()).eq(MemBranch::getBranchAddr, branch.getBranchAddr()).eq(MemBranch::getContactPhone, branch.getContactPhone()).ne(ObjectUtil.isNotNull(branchId),MemBranch::getBranchId,branchId));
        if (StringUtils.isNotEmpty(memBranches1)){
            return "已存在相同的电话名称和地址组合，请重新填写电话或者地址资料";
        }

        return "";

    }

    /**
     * 修改门店信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMemBranch(MemBranchSaveReqVO updateReqVO) {
        MemBranch memBranch = memBranchMapper.selectById(updateReqVO.getBranchId());
        if (ObjectUtil.isNull(memBranch)) {
            throw exception(MEM_BRANCH_NOT_EXISTS);
        }
        // 修改门店区域城市, 必须是在同一个运营商下
        if (Objects.nonNull(updateReqVO.getAreaId())) {
            BranchBalanceRespVO checkedData = accountApi.getBranchBalance(memBranch.getBranchId()).getCheckedData();
            if (Objects.nonNull(checkedData.getBalance())) {
                if (Objects.nonNull(memBranch.getAreaId())) {
                    AreaDTO newArea = areaApi.getAreaByAreaId(updateReqVO.getAreaId()).getCheckedData();
                    AreaDTO oldArea = areaApi.getAreaByAreaId(memBranch.getAreaId()).getCheckedData();
                    if (Objects.nonNull(oldArea.getDcId()) && Objects.isNull(newArea.getDcId())) {
                        throw exception(NEW_BRANCH_AREA_NOT_DC);
                    }
                    if (Objects.nonNull(oldArea.getDcId()) && !oldArea.getDcId().equals(newArea.getDcId())) {
                        throw exception(NEW_BRANCH_AREA_NOT_DC);
                    }
                }
            }
        }
        // 修改的门店区域和原来的门店区域不一致，将业务员置空
        if (ToolUtil.isNotEmpty(updateReqVO.getAreaId()) && !Objects.equals(memBranch.getAreaId(),updateReqVO.getAreaId())){
            updateReqVO.setColonelId(0L);
        }
        // 如果前端传来colonelId为0时，将业务员置空
        if (ToolUtil.isNotEmpty(updateReqVO.getColonelId()) && Objects.equals(0L,updateReqVO.getColonelId())){
            updateReqVO.setColonelId(null);
            memColonelBranchZipService.updateMemColonelBranchZip(updateReqVO,memBranch); // 修改门店业务员拉链表数据
            memBranchMapper.updateBranchColonelId(updateReqVO);
        }
        if (ToolUtil.isEmpty(updateReqVO.getChannelId())){
            memBranchMapper.updateBranchChannelId(updateReqVO);
        }
        // 业务员和门店是否在同一个城市
        if (ToolUtil.isEmpty(updateReqVO.getAreaId())){
            updateReqVO.setAreaId(memBranch.getAreaId());
        }
        if (ToolUtil.isNotEmpty(updateReqVO.getColonelId())&& !Objects.equals(0L,updateReqVO.getColonelId()) && !memBranchColonelNotArea(updateReqVO.getAreaId(), updateReqVO.getColonelId())){
            throw exception(MEM_BRANCH_COLONEL_NOT_AREA);
        }

//        memBranchMapper.updateById(BranchConvert.INSTANCE.convertPO(updateReqVO));

        MemBranch memBranch1 = BranchConvert.INSTANCE.convertPO(updateReqVO);

        memBranchMapper.updateById(memBranch1);
        if(ToolUtil.isNotEmpty(updateReqVO.getColonelId()) &&  !ObjectUtil.equals(memBranch.getColonelId(), updateReqVO.getColonelId())){
            memColonelBranchZipService.insertMemColonelBranchZip(updateReqVO,memBranch);
        }
        // 修改城市
        changeAreaCar(memBranch, updateReqVO);
        // 如果经纬度变更,调用MQ绑定入驻商
        bindSupplier(updateReqVO, memBranch);
    }

    @Override
    public void updateFirstOrder(BranchDTO branchDTO) {
        memBranchMapper.updateFirstOrder(branchDTO);
    }

    /**
     * 如果修改城市, 需要删除购物车
     */
    private void changeAreaCar(MemBranch memBranch, MemBranchSaveReqVO updateReqVO) {
        if (Objects.nonNull(updateReqVO.getAreaId()) && !memBranch.getAreaId().equals(updateReqVO.getAreaId())) {
            redisCarService.resetCar(memBranch.getBranchId(), ProductType.LOCAL.getType());
        }
    }

    /**
     * 计算门店在哪些入驻商的电子围栏里面
     */
    private void bindSupplier(MemBranchSaveReqVO updateReqVO, MemBranch memBranch) {
        if ( Objects.nonNull(updateReqVO.getLatitude()) && Objects.nonNull(updateReqVO.getLongitude())) {
            ArrayList<Long> bindSupplierList = new ArrayList<>();
            List<SupplierDzwlAreaDTO> areaDTOS = supplierAreaApi.getDzwlSupplierInfoByAreaId(updateReqVO.getAreaId()).getCheckedData();
            for (SupplierDzwlAreaDTO areaDTO : areaDTOS) {
                SupplierDTO supplierDTO = memberCacheService.getSupplierDTO(areaDTO.getSupplierId());
                if (StringUtils.isNotEmpty(supplierDTO.getDzwlInfo())) {

                    List<GeoUtils.Point> points = Arrays.stream(supplierDTO.getDzwlInfo().split(StringPool.SEMICOLON))
                            .map(str -> {
                                String[] split = str.split(StringPool.COMMA);
                                double lon = Double.parseDouble(split[0]);
                                double lat = Double.parseDouble(split[1]);
                                return new GeoUtils.Point(lon, lat);
                            }).collect(Collectors.toList());
                    boolean pointInPolygon = GeoUtils.isPointInPolygon(
                            updateReqVO.getLongitude().doubleValue(),
                            updateReqVO.getLatitude().doubleValue(),
                            points
                    );
                    if (pointInPolygon) {
                        bindSupplierList.add(supplierDTO.getSupplierId());
                    }
                }
            }

            BranchSupplierDTO bindDTO = new BranchSupplierDTO();
            bindDTO.setBranchId(memBranch.getBranchId());
            bindDTO.setSupplierIds(bindSupplierList);
            bindDTO.setSysCode(memBranch.getSysCode());
            bindSupplier(bindDTO);
        }
    }


    /**
     * 删除门店信息
     *
     * @param branchId 门店id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMemBranch(Long branchId) {
        MemBranch branch = memBranchMapper.selectById(branchId);
        // 逻辑删除
        branch.setDelFlag(Integer.parseInt(DelFlagConstants.DISABLE));
        memBranchMapper.updateById(branch);

        //清除缓存
        branchDTOCache.remove(branchId);
        memberAppMqProducer.sendEsColonelAppBranchBaseEvent(branchId);
    }

    /**
     * 批量删除门店信息
     *
     * @param branchIds 需要删除的门店信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BranchDeleteReqVO deleteMemBranchByBranchIds(Long[] branchIds) {
        BranchDeleteReqVO branchDeleteReqVO = new BranchDeleteReqVO();
        HashSet<String> orderOnBranchNames = new HashSet<>();
        HashSet<String> colonelOnBranchNames = new HashSet<>();
        HashSet<String> channelOnBranchNames = new HashSet<>();
        HashSet<String> groupOnBranchNames = new HashSet<>();
        HashSet<String> deleteBranchNames = new HashSet<>();

        for (Long branchId : branchIds) {
            BranchDTO branchDto = memberCacheService.getBranchDto(branchId);
            if (ToolUtil.isEmpty(branchDto)){
                continue;
            }

            // 是否生成订单
            Boolean branchOrder = orderApi.getBranchIdExistOrder(branchId).getData();
            if (branchOrder){
                orderOnBranchNames.add(branchDto.getBranchName());
                continue;
            }

            // 绑定业务员
            if (ToolUtil.isNotEmpty(branchDto.getColonelId())){
                colonelOnBranchNames.add(branchDto.getBranchName());
                continue;
            }
            // 门店是否绑定渠道
            if(ToolUtil.isNotEmpty(branchDto.getChannelId())){
                channelOnBranchNames.add(branchDto.getBranchName());
                continue;
            }
            // 全国分组
//            if (ToolUtil.isNotEmpty(branchDto.getGroupId())){
//                groupOnBranchNames.add(branchDto.getBranchName());
//                continue;
//            }

            // 删除门店
            this.deleteMemBranch(branchId);
            deleteBranchNames.add(branchDto.getBranchName());
        }
        branchDeleteReqVO.setOrderOnBranchNames(new ArrayList<>(orderOnBranchNames));
        branchDeleteReqVO.setColonelOnBranchNames(new ArrayList<>(colonelOnBranchNames));
        branchDeleteReqVO.setChannelOnBranchNames(new ArrayList<>(channelOnBranchNames));
        branchDeleteReqVO.setGroupOnBranchNames(new ArrayList<>(groupOnBranchNames));
        branchDeleteReqVO.setDeleteBranchNames(new ArrayList<>(deleteBranchNames));
        return branchDeleteReqVO;
    }

    /**
     * 获得门店信息
     *
     * @param branchId 门店id
     * @return 门店信息
     */
    @Override
    public MemBranch getMemBranch(Long branchId) {
        return memBranchMapper.selectById(branchId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<MemBranch> getMemBranchPage(MemBranchPageReqVO pageReqVO) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<MemBranch> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<MemBranch> respPage;

        //支持门店距离查询
        if(ObjectUtil.isNotEmpty(pageReqVO.getDistance())){
             respPage = memBranchMapper.selectAppPage(page, pageReqVO);
        }else{
            respPage= memBranchMapper.selectPage(page, pageReqVO);
        }

        return new PageResult<>(respPage.getRecords(), respPage.getTotal());
    }

    @Override
    public BranchDTO getMemBranchByBranchId(Long branchId) {
        return memBranchMapper.getMemBranchByBranchId(branchId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disable(Long branchId) {
        MemBranch memBranch = memBranchMapper.selectById(branchId);
        memBranch.setStatus(StatusConstants.STATE_DISABLE);
        memBranchMapper.updateById(memBranch);
        //清除缓存
        branchDTOCache.remove(branchId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(Long branchId) {
        MemBranch memBranch = memBranchMapper.selectById(branchId);
        memBranch.setStatus(StatusConstants.STATE_ENABLE);
        if(ToolUtil.isNotEmpty(memBranch.getExpirationDate())) {
            //后台启用门店后 需要将失效时间清除
            memBranch.setExpirationDate(null);
        }
        memBranchMapper.updateById(memBranch);
        //清除缓存
        branchDTOCache.remove(branchId);
    }


    @DSTransactional
    @Override
    public void batchAudit(Long[] branchIds) {
        for (Long branchId : branchIds) {
            MemBranch memBranch = memBranchMapper.selectById(branchId);
            if (memBranch.getAuditState() == 1)
                throw exception(-1, "门店已经审核，不可重复审核！");
            memBranch.setAuditState(StatusConstants.AUDIT_STATE_1);
            memBranch.setAuditBy(SecurityContextHolder.getUserName());
            memBranch.setAuditTime(DateUtils.getNowDate());
            memBranchMapper.updateById(memBranch);
        }
    }

    @Override
    public void reloadBranchDTOCache(Long branchId) {
        MemBranch memBranch = memBranchMapper.selectById(branchId);
        BranchDTO branchDTO = HutoolBeanUtils.toBean(memBranch, BranchDTO.class);
        branchDTOCache.put(branchId, branchDTO);
        areaClassBranchListDtoCache.remove(branchId);
        // 发送门店ES调整
        memberAppMqProducer.sendEsColonelAppBranchBaseEvent(branchId);
    }

    /**
     * @Description: 门店绑定入驻商
     * @Author: liuxingyu
     * @Date: 2024/3/28 16:48
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindSupplier(BranchSupplierDTO branchSupplierDTO) {
        if (ObjectUtil.isNull(branchSupplierDTO.getBranchId())) {
            throw new ServiceException("门店绑定入驻商,门店编号不能为空");
        }
        memBranchSupplierMapper.deleteByBranchId(branchSupplierDTO.getBranchId());
        List<Long> supplierIds = branchSupplierDTO.getSupplierIds();
        if (ObjectUtil.isNotEmpty(supplierIds)) {
            //封装对象
            List<MemBranchSupplier> memBranchSuppliers = supplierIds
                    .stream()
                    .map(x -> new MemBranchSupplier(branchSupplierDTO.getSysCode(), x, branchSupplierDTO.getBranchId()))
                    .collect(Collectors.toList());
            memBranchSupplierMapper.insertBatch(memBranchSuppliers);
        }
    }

    /**
     * @Description: 根据门店编号获取绑定的入驻商
     * @Author: liuxingyu
     * @Date: 2024/3/28 17:10
     */
    @Override
    public List<Long> getSupplierByBranchId(Long branchId) {
        List<MemBranchSupplier> memBranchSuppliers = memBranchSupplierMapper.getSupplierByBranchId(branchId);
        return memBranchSuppliers.stream().map(MemBranchSupplier::getSupplierId).collect(Collectors.toList());
    }

    /**
     * @Description: 根据入驻商ID获取门店ID
     * @Author: liuxingyu
     * @Date: 2024/3/28 18:16
     */
    @Override
    public List<Long> getBySupplier(Long supplierId) {
        List<MemBranchSupplier> memBranchSupplierList = memBranchSupplierMapper.getBySupplier(supplierId);
        return memBranchSupplierList.stream().map(MemBranchSupplier::getBranchId).collect(Collectors.toList());
    }

    @Override
    public MemBranch getDefaultBranchByMemberId(Long memberId, Long sysCode) {
        MemBranch branch = new MemBranch();
        MemBranchUser defaultBranchUser = memBranchUserMapper.getDefaultMemBranchUserByMemberId(memberId);
        if (ToolUtil.isNotEmpty(defaultBranchUser)) {
            branch = memBranchMapper.selectById(defaultBranchUser.getBranchId());
        }
        return branch;
    }

    @Override
    public List<BranchDTO> getBranchListByMemberId(MemberBranchReqVO memberBranchReqVO) {
        return memBranchMapper.getBranchListByMemberId(memberBranchReqVO);
    }

    @Override
    public List<BranchDTO> getUnauditedBranchListByMemberId(MemberBranchReqVO memberBranchReqVO) {
        return memBranchMapper.getUnauditedBranchListByMemberId(memberBranchReqVO);
    }


    /**
     * 获取集合选中数据 (暂时用于多选回显别名)
     *
     * @param branchIds 门店ID集合
     * @return 门店ID集合对应的数据集
     */
    @Override
    public List<MemBranchSelectedRespVO> getSelectedMemberBranch(List<Long> branchIds) {
        return BeanUtil.copyToList(memBranchMapper.selectSelectedMemberBranch(branchIds), MemBranchSelectedRespVO.class);

    }

    /**
     * @Description: 获取门店下拉选
     * @Author: liuxingyu
     * @Date: 2024/4/12 10:24
     */
    @Override
    public List<MemBranchRespVO> getBranchDropdown(BranchDropdownReqVO dropdownReqVO) {
        List<Long> areaIds = new ArrayList<>();
        if (ObjectUtil.isNotNull(SecurityUtils.getLoginUser().getDcId())) {
            DcAreaGroupDTO dcAreaGroupDTO = dcAreaApi.getByDcId(SecurityUtils.getLoginUser().getDcId()).getCheckedData();
            if (ObjectUtil.isNull(dcAreaGroupDTO) || ObjectUtil.isEmpty(dcAreaGroupDTO.getAreaIds())) {
                return new ArrayList<>();
            }
            areaIds = dcAreaGroupDTO.getAreaIds();
        }
        if (ObjectUtil.isNotNull(SecurityUtils.getSupplierId())) {
            List<AreaDTO> areaDTOList = areaApi.getBySupplierId(SecurityUtils.getSupplierId()).getCheckedData();
            if (ObjectUtil.isNull(areaDTOList) || ObjectUtil.isEmpty(areaDTOList)) {
                return new ArrayList<>();
            }
            areaIds = areaDTOList.stream().map(AreaDTO::getAreaId).collect(Collectors.toList());
        }
        dropdownReqVO.setAreaIdList(areaIds);
        return HutoolBeanUtils.toBean(memBranchMapper.getBranchDropdown(dropdownReqVO), MemBranchRespVO.class);
    }

    @Override
    public List<BranchDTO> getAllExpirationDateBranchList(Long sysCode) {
        return HutoolBeanUtils.toBean(memBranchMapper.getAllExpirationDateBranchList(sysCode), BranchDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusByBranchIds(BranchDTO branchDTO) {
        memBranchMapper.updateStatusByBranchIds(branchDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLastLoginTime(Long sysCode, Long branchId) {
        memBranchMapper.updateLastLoginTime(sysCode, branchId);
    }

    /**
     * @Description: 获取门店集合
     * @Author: liuxingyu
     * @Date: 2024/5/15 9:04
     */
    @Override
    public List<MemBranchRespVO> getBranchList() {
        return HutoolBeanUtils.toBean(memBranchMapper.selectListNormal(), MemBranchRespVO.class);
    }

    @Override
    public List<MemBranchRespDTO> getBranchListPage() {
        return HutoolBeanUtils.toBean(memBranchMapper.selectListNormal(), MemBranchRespDTO.class);
    }

    /**
     * @Description: 入驻商绑定门店
     * @Author: liuxingyu
     * @Date: 2024/5/15 11:03
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer bindBranch(BranchSupplierDTO branchSupplierDTO) {
        if (ObjectUtil.isNull(branchSupplierDTO.getSupplierId())) {
            throw new ServiceException("入驻商绑定门店,入驻商编号不能为空");
        }
        memBranchSupplierMapper.deleteBySupplierId(branchSupplierDTO.getSupplierId());
        List<Long> branchIds = branchSupplierDTO.getBranchIds();
        if (ObjectUtil.isNotEmpty(branchIds)) {
            //封装对象
            List<MemBranchSupplier> memBranchSuppliers = branchIds
                    .stream()
                    .map(x -> new MemBranchSupplier(branchSupplierDTO.getSysCode(), branchSupplierDTO.getSupplierId(), x))
                    .collect(Collectors.toList());
            memBranchSupplierMapper.insertBatch(memBranchSuppliers);

            for (Long branchId : branchIds) {
                areaClassBranchListDtoCache.remove(branchId);
            }

        }


        return null;
    }

    @Override
    public String impordData(List<MemBranchImportExcel> branchList){
        return impordDataEvent(branchList,null,SecurityUtils.getDcId(),SecurityUtils.getLoginUser().getSysCode(),0).getMsg();
    }


    public FileImportHandlerVo impordDataEvent(List<MemBranchImportExcel> branchList, Long fileImportId,Long dcId,Long sysCode,Integer seq) {
        log.info("门店导入开始");
        FileImportHandlerVo fileImportHandlerVo = new FileImportHandlerVo();
        List<SysFileImportDtl> sysFileImportDtls = new ArrayList<>();
        int successNum = branchList.size();
        int failureNum = 0;
        int totalNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        fileImportHandlerVo.setSuccessNum(successNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setTotalNum(totalNum);

        // 如果导入数据为空，则不进行数据导入
        if (branchList.isEmpty()) {
            fileImportHandlerVo.setFailureNum(branchList.size());
            fileImportHandlerVo.setTotalNum(branchList.size());
            fileImportHandlerVo.setMsg("导入数据为空");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
        }
//        Long dcId = SecurityUtils.getDcId();

        if (Objects.isNull(dcId)) {
            fileImportHandlerVo.setFailureNum(branchList.size());
            fileImportHandlerVo.setTotalNum(branchList.size());
            fileImportHandlerVo.setMsg("非运营商角色");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
        }


        // 获取字典数据 停用、启用
//        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_COMMON_STATUS);
//        Map<String, SysDictData> statusMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictLabel, item -> item));
        // 获取渠道数据
        List<ChannelDTO> channelDTOList = channelApi.getChannelList().getCheckedData();
        Map<String, ChannelDTO> channelMap = convertMap(channelDTOList, ChannelDTO::getChannelName);

//        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        //获取城市区域数据
        List<AreaDTO> areaDTOList = areaApi.getListBySysCode(sysCode).getCheckedData();
        Map<String, AreaDTO> areaDTOMap = convertMap(areaDTOList, AreaDTO::getAreaName);

        List<MemColonel> colonelDTOList = memColonelMapper.getListBySysCode(sysCode);
        Map<String, MemColonel> colonelDTOMap = convertMap(colonelDTOList, MemColonel::getColonelName);

        // 获取平台城市分组数据
        List<GroupDTO> groupDTOList = groupApi.getListBySysCode(sysCode).getCheckedData();
        Map<String, GroupDTO> groupMap = convertMap(groupDTOList, GroupDTO::getGroupName);

        Map<MemBranch, MemMember> branchCache = new LinkedHashMap<>();
        Map<String, SysFileImportDtl> branchFileImport = new LinkedHashMap<>();

        for (int line = 0; line < branchList.size(); line++) {
            //导入明细
            SysFileImportDtl sysFileImportDtl = new SysFileImportDtl();
            sysFileImportDtl.setSysCode(sysCode);
            sysFileImportDtl.setCreateBy(sysFileImportDtl.getCreateBy());
            sysFileImportDtl.setCreateTime(new Date());
            sysFileImportDtl.setFileImportId(fileImportId);

            if (failureMsg.length() > 2000) {
                break;
            }
            int cellNumber = line + 3 +seq;
            MemBranchImportExcel data = branchList.get(line);
            sysFileImportDtl.setDtlJson(JsonUtils.toJsonString(data));

            try {


            if (StringUtils.isEmpty(data.getBranchNo())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>门店编号不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据门店编号不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (data.getBranchNo().length() > 25) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>门店编号字符长度超长25。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据门店编号字符长度超长25。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (!data.getBranchNo().matches("[a-zA-Z0-9]+")) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>门店编号只能包括数字或字母！。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据门店编号只能包括数字或字母！。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            MemBranch branch = memBranchMapper.selectMemBranchByBranchNo(data.getBranchNo());
            if (ObjectUtil.isNotEmpty(branch)) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>门店编号已存在！。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据门店编号已存在！。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            boolean isBranch = branchCache.keySet().stream().anyMatch(key -> Objects.equals(key.getBranchNo(), data.getBranchNo()));
            if (isBranch) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>门店编号【{}】再当前导入数据存在重复数据！。</span>", cellNumber, data.getBranchNo()));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据门店编号【{}】再当前导入数据存在重复数据！。", cellNumber, data.getBranchNo()));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (StringUtils.isEmpty(data.getBranchName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>门店名称不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据门店名称不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (data.getBranchName().length() > 50) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>门店名称字符长度超长50。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据门店名称字符长度超长50。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (StringUtils.isEmpty(data.getContactName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>门店联系人不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据门店联系人不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (data.getContactName().length() > 20) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>门店联系人字符长度超长20。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据门店联系人字符长度超长20。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (StringUtils.isEmpty(data.getContactPhone())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>联系电话不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据联系电话不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (data.getContactPhone().length() != 11) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>联系电话字符长度只能是11位。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据联系电话字符长度只能是11位。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (!data.getContactPhone().matches("\\d+")) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>联系电话只能是纯数字！</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据联系电话只能是纯数字。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
//            MemMember member = memMemberMapper.getMemberByMemberPhone(data.getContactPhone(), sysCode);
//            if (StringUtils.isNotEmpty(data.getContactPhone()) && ObjectUtil.isNotNull(member)) {
//                failureNum++;
//                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>联系电话已存在，不可导入重复的手机号。</span>", cellNumber));
//                continue;
//            }

            if (StringUtils.isEmpty(data.getBranchAddr())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>联系地址不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据联系地址不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            ChannelDTO channel = null;
            if (StringUtils.isNotEmpty(data.getChannelName())) {
                channel = channelMap.get(data.getChannelName());
                if (Objects.isNull(channel)) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>渠道类型不存在。</span>。", cellNumber));
                    sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                    sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据渠道类型不存在。", cellNumber));
                    sysFileImportDtls.add(sysFileImportDtl);
                    continue;
                }
            }
            Integer status = null;
            if (StringUtils.isNotEmpty(data.getStatusName())) {
                status = SysYesNoEnum.getYesNo(data.getStatusName());
                if (Objects.isNull(status)) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>是否启用状态不存在。</span>。", cellNumber));
                    sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                    sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据是否启用状态不存在。", cellNumber));
                    sysFileImportDtls.add(sysFileImportDtl);
                    continue;
                }
            }

            if (StringUtils.isEmpty(data.getAreaName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>城市区域不能为空。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据城市区域不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            AreaDTO area = null;
            if (StringUtils.isNotEmpty(data.getAreaName())) {
                area = areaDTOMap.get(data.getAreaName());
                if (Objects.isNull(area)) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>城市区域不存在。</span>。", cellNumber));
                    sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                    sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据城市区域不存在。", cellNumber));
                    sysFileImportDtls.add(sysFileImportDtl);
                    continue;
                }
            }

            if (StringUtils.isEmpty(data.getColonelName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>业务员不能为空。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据业务员不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            MemColonel colonel = null;
            if (StringUtils.isNotEmpty(data.getColonelName())) {
                colonel = colonelDTOMap.get(data.getColonelName());
                if (Objects.isNull(colonel)) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>业务员不存在。</span>。", cellNumber));
                    sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                    sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据业务员不存在。", cellNumber));
                    sysFileImportDtls.add(sysFileImportDtl);
                    continue;
                }
            }

            Integer yesNo = null;
            if (StringUtils.isNotEmpty(data.getHdfkSupportName())) {
                yesNo = SysYesNoEnum.getYesNo(data.getHdfkSupportName());
                if (Objects.isNull(yesNo)) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>是否支持货到付款不存在。</span>。", cellNumber));
                    sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                    sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据是否支持货到付款不存在。", cellNumber));
                    sysFileImportDtls.add(sysFileImportDtl);
                    continue;
                }

                if (ObjectUtil.isEmpty(data.getHdfkMaxAmt()) && yesNo == 1) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>货到付款最大可欠款金额为空。</span>。", cellNumber));
                    sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                    sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据货到付款最大可欠款金额为空。", cellNumber));
                    sysFileImportDtls.add(sysFileImportDtl);
                    continue;
                }

            }

            if (StringUtils.isNotEmpty(data.getMemo()) && data.getMemo().length() > 500) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>备注字段超出500长度。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据备注字段超出500长度。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            // 校验平台城市分组
            GroupDTO group = null;
            if (StringUtils.isNotEmpty(data.getGroupName())) {
                group = groupMap.get(data.getGroupName());
                if (Objects.isNull(group)) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>平台城市分组不存在。</span>", cellNumber));
                    sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                    sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据平台城市分组不存在。", cellNumber));
                    sysFileImportDtls.add(sysFileImportDtl);
                    continue;
                }
            } else {
                GroupDTO defaultGrouping = groupApi.getDefaultGrouping().getCheckedData();
                if(ToolUtil.isEmpty(defaultGrouping.getGroupId())){
                    throw new ServiceException("全国分组为空");
                }
                group = defaultGrouping; // 若未填写，则默认系统第一个分组
            }

            Long threeAreaCityId = null;
            // 校验城市(省/市/县区)
            if (StringUtils.isNotEmpty(data.getCity())) {
                threeAreaCityId = validateCity(data.getCity());
                if (threeAreaCityId == null) {
                    failureNum++;
                    failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>城市(省/市/县区)格式不正确或不存在。</span>", cellNumber));
                    sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                    sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据城市(省/市/县区)格式不正确或不存在。", cellNumber));
                    sysFileImportDtls.add(sysFileImportDtl);
                    continue;
                }
            }else{
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>城市(省/市/县区)必填。</span>。", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据城市(省/市/县区)必填。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            if (!Objects.equals(colonel.getAreaId(), area.getAreaId())){
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>门店与业务员不在同一区域。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据门店与业务员不在同一区域。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            // 转换导入实体 门店信息
            MemBranch memBranch = new MemBranch();
            {
                BranchConvert.INSTANCE.convertImport(memBranch, data);
                //省市区填值已做校验，validateCity
                String[] cityParts = data.getCity().split("/");
                memBranch.setAreaId(area.getAreaId())
                        .setColonelId(colonel.getColonelId())
                        .setChannelId(ObjectUtil.isNull(channel) ? null : channel.getChannelId())
                        .setStatus(ObjectUtil.isNull(status) ? StatusConstants.FLAG_TRUE : status)
                        .setDelFlag(NumberPool.INT_ZERO)
                        .setGroupId(group.getGroupId())
                        .setThreeAreaCityId(threeAreaCityId)
                        .setHdfkSupport(ObjectUtil.isNull(yesNo) ? StatusConstants.FLAG_FALSE : yesNo)
                        .setHdfkMaxAmt(ObjectUtil.isNull(data.getHdfkMaxAmt()) ? BigDecimal.ZERO : data.getHdfkMaxAmt())
                        .setSysCode(sysCode)
                        .setProvinceName(cityParts[0])
                        .setCityName(cityParts[1])
                        .setDistrictName(cityParts[2])
                ;
            }

            String s = branchVerify(memBranch,null);
            if (StringUtils.isNotEmpty(s)){
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>{}。</span>", cellNumber,s));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据{}。", cellNumber,s));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }

            // 转换导入实体 用户信息
            MemMember memMember = new MemMember();
            {
                memMember.setMemberPhone(data.getContactPhone())
                        .setMemberName(data.getContactName())
                        .setStatus(ObjectUtil.isNull(status) ? StatusConstants.FLAG_TRUE : status)
                        .setUserName(data.getContactPhone())
                        .setPassword(SecurityUtils.encryptPassword(memberCacheService.getDefaultPassword(sysCode)))
                ;

            }
            branchCache.put(memBranch, memMember);
            branchFileImport.put(memBranch.getBranchNo(),sysFileImportDtl);

            sysFileImportDtl.setStatus(STATUS_SUCCESS);
            sysFileImportDtls.add(sysFileImportDtl);
            }catch (Exception e){
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据{}。", cellNumber,e.getMessage()));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据{}。", cellNumber,e.getMessage()));
                sysFileImportDtls.add(sysFileImportDtl);
            }
        }

        fileImportHandlerVo.setTotalNum(successNum);
        fileImportHandlerVo.setSuccessNum(successNum - failureNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setList(sysFileImportDtls);
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            fileImportHandlerVo.setMsg(failureMsg.toString());
            fileImportHandlerVo.setSuccessNum(0);
            fileImportHandlerVo.setFailureNum(fileImportHandlerVo.getTotalNum());
            sysFileImportDtls.stream().filter(t -> StatusConstants.STATUS_SUCCESS==t.getStatus()).forEach(t -> {
                t.setStatus(StatusConstants.STATUS_FAIL);
                t.setFailReason("数据格式正确，由其他数据失败引起的导入失败");
            });
            return fileImportHandlerVo;
        } else {
            for (Map.Entry<MemBranch, MemMember> entry : branchCache.entrySet()) {
                try {
                    SpringUtils.getBean(MemBranchServiceImpl.class).importBranch(entry.getKey(), entry.getValue(), sysCode);
                } catch (Exception e) {
                    failureMsg.append(e.getMessage());
                    failureNum ++;
                    SysFileImportDtl sysFileImportDtl = branchFileImport.get(entry.getKey().getBranchNo());
                    if (sysFileImportDtl!=null){
                        sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                        sysFileImportDtl.setFailReason(e.getMessage());
                    }

                }
            }
            fileImportHandlerVo.setTotalNum(successNum);
            fileImportHandlerVo.setSuccessNum(successNum - failureNum);
            fileImportHandlerVo.setFailureNum(failureNum);
            fileImportHandlerVo.setList(sysFileImportDtls);
            if (failureNum > 0) {
                successMsg.insert(0, "很抱歉！导入成功数量【"+ (successNum - failureNum) +"】、导入失败数量【"+ failureNum +"】，错误如下：" + failureMsg);
                fileImportHandlerVo.setStatus(STATUS_FAIL);
                fileImportHandlerVo.setMsg(failureMsg.toString());
                return fileImportHandlerVo;
            } else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
                fileImportHandlerVo.setStatus(STATUS_SUCCESS);
                fileImportHandlerVo.setMsg(failureMsg.toString());
            }

        }
        return fileImportHandlerVo;
    }

    @DistributedLock(prefix = RedisLockConstants.LOCK_BRANCH_IMPORT, condition = "#sysCode + #branch.branchNo", tryLock = true, leaseTime = 100L)
    @Transactional(rollbackFor = Exception.class)
    public void importBranch(MemBranch branch, MemMember member, Long sysCode) {
        MemBranch branchExists = memBranchMapper.selectMemBranchByBranchNo(branch.getBranchNo());
        if (ObjectUtil.isNotEmpty(branchExists)) {
            throw new ServiceException(StringUtils.format("<br/><span style='color:red'>门店编号【{}】已存在</span>", branch.getBranchNo()));
        }
        // 新增门店
        memBranchMapper.insert(branch);

        // 根据门店用户手机号查询是否存在商城用户
        MemMember memberExists = memMemberMapper.getMemberByMemberPhone(member.getMemberPhone(), sysCode);
        /**
         * 校验门店用户手机号是否已存在 商城用户
         * 存在，则使用之前账号绑定
         * 不存在，则添加新账号绑定
         */
        if (ToolUtil.isEmpty(memberExists)) {
            memMemberMapper.insert(member);
        } else {
            member = memberExists;
        }

        // 新增门店用户绑定关系
        memBranchUserMapper.insert(
                MemBranchUser.builder()
                        .memberId(member.getMemberId())
                        .branchId(branch.getBranchId())
                        .isDefault(ToolUtil.isEmpty(memberExists) ? STATUS_1 : STATUS_2)  // 若新增用户则设置默认门店，否则不设置默认
                        .build()
        );
        processCouponForBranchRegistration(branch.getBranchId(), branch.getSysCode(),branch.getAreaId());

        // 发送MQ 异步处理门店经纬度查询
        memberMqProducer.sendBranchGetLongitudeAndLatitudeEvent(Collections.singletonList(branch.getBranchId()));
    }

    /**
     * 校验省/市/区是否存在，并获取第三级城市 ID。
     *
     * @param city 省/市/区，格式如：湖南省/长沙市/雨花区
     * @return 如果省市区有效，返回第三级城市 ID；否则返回 null
     */
    public Long validateCity(String city) {
        if (StringUtils.isEmpty(city)) {
            throw new ServiceException("城市信息不能为空");
        }

        String[] cityParts = city.split("/");
        if (cityParts.length != 3) {
            throw new ServiceException("城市格式不正确，正确格式为 省/市/县区");
        }

        // 依次验证 省、市、区是否存在，并获取它们的ID
        Long provinceId = getCityIdByNameAndDeep(cityParts[0], 0, null);
        if (provinceId == null) {
            return null;
        }

        Long cityId = getCityIdByNameAndDeep(cityParts[1], 1, provinceId);
        if (cityId == null) {
            return null;
        }

        Long districtId = getCityIdByNameAndDeep(cityParts[2], 2, cityId);
        return districtId;
    }

    private Long getCityIdByNameAndDeep(String name, Integer deep, Long pid) {
        // 调用远程服务查询
        SysAreaCityRespVO result = areaCityApi.getByNameAndParent(name,pid,deep).getCheckedData();
        if (ToolUtil.isNotEmpty(result)) {
            return result.getAreaCityId();
        }
        return null;
    }

    /**
     * 推送门店信息给ERP
     * @param data
     * @param type
     */
    @Override
    public void sendErp(MemBranchSaveReqVO data,OperationType type){
        //推送门店信息给ERP
        //获取该门店的入驻商信息
        List<SupplierAreaDTO> supplierAreaDTOS = supplierAreaApi.getSupplierAreaByAreaId(data.getAreaId()).getCheckedData();
        if(supplierAreaDTOS.size() > 0){
            List<Long> supplierIds = supplierAreaDTOS.stream().map(SupplierAreaDTO::getSupplierId).collect(Collectors.toList());
            ErpBean bean = new ErpBean();
            bean.setSupplierIdList(supplierIds);
            bean.setType(type);
            bean.setBranchOpenDTO(HutoolBeanUtils.toBean(data, BranchOpenDTO.class));
            openApi.synErpBranch(bean);
        }

    }

    @Override
    public void updateWechatMerchantAuthOpenid(Long branchId, String openid) {
        MemBranch branch = memBranchMapper.selectById(branchId);
        // 如果本来没设置, 或者本次设置就是空, 那就更新
        if (StringUtils.isEmpty(branch.getWechatMerchantAuthOpenid()) || StringUtils.isEmpty(openid)) {
            MemBranch memBranch = new MemBranch();
            memBranch.setBranchId(branchId);
            memBranch.setWechatMerchantAuthOpenid(openid);
            memBranchMapper.updateById(memBranch);
        }
        //清除缓存
        branchDTOCache.remove(branchId);
    }


    @Override
    public void refreshEsBranchSalesInfoJobHandler(Long sysCode) {
        // 查询平台下所有的 状态正常的 门店信息
        Long minBranchId = NumberPool.LOWER_GROUND_LONG;
        // 上月
        String lastMonth = DateUtils.getYearMonthRange(-1, DateUtils.getNowDate());
        // 本月
        String month = DateUtils.dateTimeNow(DateUtils.YYYY_MM);
        for (;;) {
            // 查询平台下所有的 状态正常的 门店信息
            List<MemBranch> branchList = memBranchMapper.selectList(
                    Wrappers.lambdaQuery(MemBranch.class)
                            .eq(MemBranch::getSysCode, sysCode)
                            .gt(MemBranch::getBranchId, minBranchId)
                            .orderByAsc(MemBranch::getBranchId)
                            .last("LIMIT 2000")
            );

            branchList.forEach(branch -> {
                // 当月ES key
                String monthKey = StringUtils.format("{}_{}_{}", branch.getBranchId(), branch.getSysCode(), month);
                // 上月ES key
                String lastMonthKey = StringUtils.format("{}_{}_{}", branch.getBranchId(), branch.getSysCode(), lastMonth);
                // 组合查询ES的id
                List<String> ids = Arrays.asList(monthKey, lastMonthKey);

                //查询ES
                List<EsColonelAppBranch> esBranchList = colonelAppBranchService.seachColonelAppBranch(new ColonelAppBranchDTO(ids));
                Map<String, EsColonelAppBranch> esBranchMap = CollectionUtils.convertMap(esBranchList, EsColonelAppBranch::getId);

                EsColonelAppBranch esColonelAppBranch = new EsColonelAppBranch();
                // 校验本月是否已存在ES信息
                if (ToolUtil.isNotEmpty(esBranchMap) && esBranchMap.containsKey(monthKey)) {
                    esColonelAppBranch = esBranchMap.get(monthKey);
                } else {
                    esColonelAppBranch.setId(monthKey)
                            .setBranchId(branch.getBranchId())
                            .setSysCode(branch.getSysCode())
                            .setCreateYearMonth(month)
                            .setNowMonthOrderQty(NumberPool.LONG_ZERO)
                            .setNowMonthOrderAmt(BigDecimal.ZERO)
                            .setLastMonthOrderQty(NumberPool.LONG_ZERO)
                            .setLastMonthOrderAmt(BigDecimal.ZERO)
                            .setLastBuyTime(null)
                            .setLastBuyAmt(BigDecimal.ZERO)
                            .setLastAccessSystemTime(null)
                            .setLastLoginTime(null)
                            .setLastVisitTime(null)
                            .setAreaId(branch.getAreaId())
                            .setKeyword(StringUtils.format("{},{},{},{}", branch.getBranchName(), branch.getContactName(), branch.getBranchAddr(), branch.getContactPhone()))
                            .setColonelId(branch.getColonelId())
                    ;
                }

                // 是否存在上月数据
                if (ToolUtil.isNotEmpty(esBranchMap) && esBranchMap.containsKey(lastMonthKey)) {
                    // 上个月的订单数据数据变成本月
                    esColonelAppBranch.setLastMonthOrderQty(esBranchMap.get(lastMonthKey).getNowMonthOrderQty());
                    esColonelAppBranch.setLastMonthOrderAmt(esBranchMap.get(lastMonthKey).getNowMonthOrderAmt());
                }
                // 验证GEO有效
                if (GeoUtils.isValidCoordinate(branch.getLongitude(), branch.getLatitude())) {
                    esColonelAppBranch.setLocation(StringUtils.format("{},{}", branch.getLatitude(), branch.getLongitude()));
                }
                colonelAppBranchService.saveColonelAppBranch(esColonelAppBranch);
            });
            // 结束循环
            if (branchList.isEmpty()) {
                break;
            }
            minBranchId = branchList.get(branchList.size() - 1).getBranchId();
        }
    }

    @Override
    public void initEsBranchSalesInfoJobHandler(Long sysCode, String startDate, String endDate) {
        // 初始化索引
        // colonelAppBranchService.initIndex();
        // 清除当前平台下的ES门店数据，之前有一些已经手动删除的门店没有更新ES
        ColonelAppBranchDTO search = new ColonelAppBranchDTO();
        search.setSysCode(sysCode);
        int deleteCount = colonelAppBranchService.deleteColonelAppBranch(search);
        log.info(" ===============平台编号【{}】初始化删除ES门店数据量：{}=================", sysCode, deleteCount);
        // 得到日期区间的日期集合
        List<String> monthRangeList = DateUtils.getMonthRange(startDate, endDate);
        Long minBranchId = NumberPool.LOWER_GROUND_LONG;
        for (;;) {
            // 查询平台下所有的 状态正常的 门店信息
            List<MemBranch> branchList = memBranchMapper.selectList(
                    Wrappers.lambdaQuery(MemBranch.class)
                            .eq(MemBranch::getSysCode, sysCode)
                            .gt(MemBranch::getBranchId, minBranchId)
                            .orderByAsc(MemBranch::getBranchId)
                            .last("LIMIT 2000")
            );
            branchList.forEach(branch -> {
                // 根据门店和日期区间查询每月门店最近一次的拜访时间
                List<EsColonelAppBranch> esColonelAppBranchList = memColonelVisitLogMapper.getBranchVisitLogByBranchIdAndDate(branch.getBranchId(), sysCode, startDate, endDate);
                Map<String, EsColonelAppBranch> esColonelAppBranchMap = convertMap(esColonelAppBranchList, EsColonelAppBranch::getCreateYearMonth);

                // 根据门店和日期区间查询每月门店的订单数据
                List<EsBranchOrderSalesRespDTO> esBranchOrderSalesRespDTOList = orderApi.getBranchOrderSales(EsBranchOrderSalesReqVO
                        .builder()
                        .branchId(branch.getBranchId())
                        .sysCode(sysCode)
                        .startDate(startDate)
                        .endDate(endDate)
                        .build()
                ).getCheckedData();
                Map<String, EsBranchOrderSalesRespDTO> esBranchOrderSalesRespDTOMap = convertMap(esBranchOrderSalesRespDTOList, EsBranchOrderSalesRespDTO::getCreateYearMonth);

                // 门店新增初始化门店ES信息
                List<EsColonelAppBranch> saveBranchList = monthRangeList.stream().map(month -> {
                    // 当前循环 年月 拜访数据
                    EsColonelAppBranch esColonelAppBranch = esColonelAppBranchMap.get(month);
                    // 当前循环 年月 订单数据
                    EsBranchOrderSalesRespDTO esBranchOrderSalesRespDTO = esBranchOrderSalesRespDTOMap.get(month);
                    // 当前循环 年月的上月 订单数据
                    EsBranchOrderSalesRespDTO lastEsBranchOrderSalesRespDTO = esBranchOrderSalesRespDTOMap.get(DateUtils.getYearMonthRange(-1, DateUtils.parseDate(month)));

                    if (ToolUtil.isEmpty(esColonelAppBranch) && ToolUtil.isEmpty(esBranchOrderSalesRespDTO) && ToolUtil.isEmpty(lastEsBranchOrderSalesRespDTO)) {
                        //return null;
                    }
                    EsColonelAppBranch colonelAppBranch = new EsColonelAppBranch()
                            .setId(branch.getBranchId() + StringPool.UNDERSCORE + branch.getSysCode() + StringPool.UNDERSCORE + month)
                            .setBranchId(branch.getBranchId())
                            .setSysCode(branch.getSysCode())
                            .setAreaId(branch.getAreaId())
                            .setCreateYearMonth(month)
                            .setNowMonthOrderQty(ToolUtil.isEmpty(esBranchOrderSalesRespDTO) ? NumberPool.LONG_ZERO : esBranchOrderSalesRespDTO.getOrderQtyCount())
                            .setNowMonthOrderAmt(ToolUtil.isEmpty(esBranchOrderSalesRespDTO) ? BigDecimal.ZERO : esBranchOrderSalesRespDTO.getOrderAmtSum())
                            .setLastMonthOrderQty(ToolUtil.isEmpty(lastEsBranchOrderSalesRespDTO) ? NumberPool.LONG_ZERO : lastEsBranchOrderSalesRespDTO.getOrderQtyCount())
                            .setLastMonthOrderAmt(ToolUtil.isEmpty(lastEsBranchOrderSalesRespDTO) ? BigDecimal.ZERO : lastEsBranchOrderSalesRespDTO.getOrderAmtSum())
                            .setLastBuyTime(ToolUtil.isEmpty(esBranchOrderSalesRespDTO) ? null : esBranchOrderSalesRespDTO.getLastBuyTime())
                            .setLastBuyAmt(ToolUtil.isEmpty(esBranchOrderSalesRespDTO) ? BigDecimal.ZERO : esBranchOrderSalesRespDTO.getLastBuyAmt())
                            .setLastAccessSystemTime(ToolUtil.isEmpty(esBranchOrderSalesRespDTO) ? null : esBranchOrderSalesRespDTO.getLastAccessSystemTime())
                            .setLastLoginTime(ToolUtil.isEmpty(esBranchOrderSalesRespDTO) ? null : esBranchOrderSalesRespDTO.getLastLoginTime())
                            .setLastVisitTime(ToolUtil.isEmpty(esColonelAppBranch) ? null : esColonelAppBranch.getLastVisitTime())
                            .setKeyword(StringUtils.format("{},{},{},{}", branch.getBranchName(), branch.getContactName(), branch.getBranchAddr(), branch.getContactPhone()))
                            .setColonelId(branch.getColonelId());

                    // 验证GEO有效
                    if (GeoUtils.isValidCoordinate(branch.getLongitude(), branch.getLatitude())) {
                        colonelAppBranch.setLocation(StringUtils.format("{},{}", branch.getLatitude(), branch.getLongitude()));
                    }
                    return colonelAppBranch;
                }).filter(Objects::nonNull).collect(Collectors.toList());

                // 执行新增操作
                colonelAppBranchService.saveBatchColonelAppBranch(saveBranchList);
            });

            if (branchList.isEmpty()) {
                break;
            }
            minBranchId = branchList.get(branchList.size() - 1).getBranchId();
        }
    }

    @Override
    public List<Long> getAllBranchIdList(Long minBranchId) {
        return memBranchMapper.selectAllBranchIdList(minBranchId);
    }

    @Override
    public PageResult<SyncBranchCallDTO> getBranchDataPage(Integer pageNum, Integer pageSize) {
        // 获取入驻商编号
        Long supplierId = OpenapiSecurityUtils.getLoginOpensource().getOpensourceDto().getMerchantId();

        // Redis 锁的 key，确保每个入驻商只能在 1 分钟内调用一次
        String lockKey = "branch:sync:lock:" + supplierId;

        // 尝试获取锁，设置 60 秒过期时间
        Boolean isLocked = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 60, TimeUnit.SECONDS);

        // 如果无法获取锁，表示该商家在 1 分钟内已经调用过
        if (!Boolean.TRUE.equals(isLocked)) {
            throw new ServiceException("同一个入驻商账号在一分钟内只能调用一次");
        }

        try {
            // 获取区域信息
            List<AreaDTO> areaList = areaApi.getBySupplierId(supplierId).getCheckedData();

            // 启动分页查询
            PageHelper.startPage(pageNum, pageSize);
            List<SyncBranchCallDTO> branchDataList = memBranchMapper.selectBranchByTimeRange(areaList);
            for (SyncBranchCallDTO syncBranchCallDTO : branchDataList) {
                if (ToolUtil.isNotEmpty(syncBranchCallDTO.getUserId()) && ToolUtil.isNotEmpty(syncBranchCallDTO.getColonelName())) {
                    syncBranchCallDTO.setColonelName(syncBranchCallDTO.getColonelName());
                    syncBranchCallDTO.setColonelPhone(remoteUserService.getSysUser(syncBranchCallDTO.getUserId()).getCheckedData().getUserName());
                }
            }
            // 使用 PageHelper 的 Page 对象来构造 PageResult
            Page<SyncBranchCallDTO> page = (Page<SyncBranchCallDTO>) branchDataList;

            // 返回分页结果
            return PageResult.result(page, page.getResult());
        } finally {
            // 释放锁
            redisTemplate.delete(lockKey);
        }
    }

    @Override
    public void syncBranchData(Long branchId, String operationTypeCode) {
        //推送 同步门店信息
        memberMqProducer.sendSyncDataBranchEvent(new SyncBranchSendDTO(branchId,operationTypeCode));

    }


    private void processCouponForBranchRegistration(Long branchId, Long sysCode, Long areaId) {
    // 判断是否有优惠券
    List<CouponTemplateDTO> couponTemplateDTOList = couponService.selectValidListBySysCodeAndReceiveType(sysCode, CouponReceiveType.REGISTER.getType());

    // 生效的注册发券正常全国和本地各只有一条，如果有多个则暂停发券
    if (couponTemplateDTOList.size() <= 2) {
        for (CouponTemplateDTO couponTemplateDTO: couponTemplateDTOList) {
            Long couponTemplateId = couponTemplateDTO.getCouponTemplateId();
            Integer receiveScope = couponTemplateDTO.getReceiveScope();

            // 判断优惠券的领券范围
            if (receiveScope == NumberPool.INT_ZERO || receiveScope == NumberPool.INT_TWO) {
                // 判断优惠券城市是否包含当前门店城市
                boolean isEligibleForCoupon = true;

                if (receiveScope == NumberPool.INT_TWO) {
                    String couponCityId = couponTemplateDTO.getReceiveScopeApplyIds();
                    isEligibleForCoupon = couponCityId != null && couponCityId.contains(areaId + "");
                }
                if (isEligibleForCoupon) {
                    // 优惠券生效
                    Map<Long, Long> couponStatusMap = new HashMap<>();
                    couponStatusMap.put(couponTemplateId, Long.parseLong(couponTemplateDTO.getStatus()+""));
                    couponService.saveNormalCouponReceiveSingle(new NormalCouponReceiveSingleAsyncReqVo(branchId,  null, couponTemplateId, true,null,couponStatusMap));
                }
            }
        }
    }
}

    @Override
    public Map<Long, BranchDTO> listByBranchIds(List<Long> branchIds) {
        if (branchIds == null || branchIds.isEmpty()) {
            return Maps.newHashMap();
        }
        List<MemBranch> memBranches = memBranchMapper.selectList(MemBranch::getBranchId, branchIds.stream().distinct().collect(Collectors.toList()));
        if (memBranches == null || memBranches.isEmpty()) {
            return Maps.newHashMap();
        }
        return memBranches.stream()
                .collect(Collectors.toMap(MemBranch::getBranchId, membranch -> Objects.requireNonNull(HutoolBeanUtils.toBean(membranch, BranchDTO.class))));
    }

    @Override
    public List<HomePagesBranchDataRespDTO> getHomePagesBranchData(HomePagesReqVO reqVO) {
        return memBranchMapper.getHomePagesBranchData(reqVO);
    }

    @Override
    public List<BranchDTO> getbranchListByArea(Long dcAreaId) {
        return memBranchMapper.getbranchListByArea(dcAreaId);
    }

    @Override
    public void updateMemBranchSeasTime(MemBranchSaveReqVO updateReqVO) {
        MemBranch memBranch = memBranchMapper.selectById(updateReqVO.getBranchId());
        if (ObjectUtil.isNull(memBranch)) {
            throw exception(MEM_BRANCH_NOT_EXISTS);
        }
        memBranchMapper.updateBranchColonelId((updateReqVO));
        memBranchMapper.updateById(BranchConvert.INSTANCE.convertPO(updateReqVO));
        if(ToolUtil.isNotEmpty(updateReqVO.getColonelId()) &&  !ObjectUtil.equals(memBranch.getColonelId(), updateReqVO.getColonelId())){
            memColonelBranchZipService.insertMemColonelBranchZip(updateReqVO,memBranch);
        }
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_SYNC_AREA_BRANCH, condition = "#vo.supplierId")
    public void syncAreaBranch(MemBranchSyncReqVO vo) {
        Long supplierId = vo.getSupplierId();
        //校验该入驻商是否对接了第三方
        if(!remoteSupplierApi.checkSyncConfig(supplierId).getCheckedData()){
            throw exception(MEM_MEMBER_BRANCH_CHECK_SYNC_SUPPLIER_OPENSOURCE);
        }
        //校验 该入驻商是否配置了推送门店信息可视化接口
        VisualSettingDetailDto visualSettingDetailDto = memberCacheServiceImpl.getVisualDetailBySupplier(supplierId + StringPool.COLON + VisualTemplateType.BRANCH_TYPE.getType());
        if(ToolUtil.isEmpty(visualSettingDetailDto)
                || ToolUtil.isEmpty(visualSettingDetailDto.getVisualDetailId())){
            throw exception(MEM_MEMBER_BRANCH_CHECK_SYNC_SUPPLIER_TEMPLATE);
        }

        String lock = StringUtils.format("{}_{}_{}",SYNC_AREA_BRANCH,supplierId,vo.getAreaId());

        //校验该入驻商、该区域是否十分钟内已推送过
        if(redisService.hasKey(lock)){
            throw exception(MEM_MEMBER_BRANCH_CHECK_SYNC_AREA_LOCK);
        }

        //推送 同步门店信息
        memberMqProducer.sendSyncDataBranchEvent(new SyncBranchSendDTO(vo));

        //加锁
        redisService.setCacheObject(lock,lock,600L,TimeUnit.SECONDS);
    }

    @Override
    public List<Long> getAllBranchIdListBySysCode(Long sysCode) {
        return memBranchMapper.selectAllBranchIdListBySysCode(sysCode);
    }
    /**
     * 判断业务员与门店是否在同一区域城市
     * @param memBranchAreaId 门店对象
     * @param colonelId 业务员id
     */
    public Boolean memBranchColonelNotArea(Long memBranchAreaId,Long colonelId){
        ColonelDTO colonel = memberCacheService.getColonel(colonelId);
        if (ToolUtil.isEmpty(colonel) || ToolUtil.isEmpty(colonel.getAreaId())){
            return Boolean.FALSE;
        }
        if (!Objects.equals(colonel.getAreaId(), memBranchAreaId)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public String branchBatchEdit(BranchBatchEditVO reqVo) {
        // 校验当前区域 、 业务员 、 渠道 是否存在
        AreaDTO areaDTO = memberCacheService.getAreaDto(reqVo.getAreaId());
        if (ToolUtil.isEmpty(areaDTO)) {
            throw exception(MEM_BRANCH_AREA_NOT_EXISTS);
        }
        if (ToolUtil.isNotEmpty(reqVo.getChannelId())) {
            ChannelDTO channelDTO = memberCacheService.getChannelDto(reqVo.getChannelId());
            if (ToolUtil.isEmpty(channelDTO)) {
                throw exception(MEM_BRANCH_CHANNEL_NOT_EXISTS);
            }
        }

        if (ToolUtil.isNotEmpty(reqVo.getColonelId())) {
            ColonelDTO colonelDTO = memberCacheService.getColonel(reqVo.getColonelId());
            if (ToolUtil.isEmpty(colonelDTO)) {
                throw exception(MEM_BRANCH_COLONEL_NOT_EXISTS);
            }
            if (!Objects.equals(colonelDTO.getAreaId(), areaDTO.getAreaId())) {
                throw exception(MEM_BRANCH_CHANNEL_AREA__NOT_EXISTS, areaDTO.getAreaName(), colonelDTO.getColonelName());
            }
        }
        StringBuffer msg = new StringBuffer();
        if (ToolUtil.isNotEmpty(reqVo.getBranchIds())) {
            IMemBranchService memBranchService = SpringUtils.getBean(MemBranchServiceImpl.class);
            List<MemBranch> memBranches = memBranchMapper.selectMemBranchByBranchIds(reqVo.getBranchIds());

            for (MemBranch memBranch : memBranches) {
                try {
                    MemBranchSaveReqVO updateReqVO = BranchConvert.INSTANCE.convertUpdateVO(memBranch);
                    updateReqVO.setAreaId(reqVo.getAreaId());
                    updateReqVO.setColonelId(reqVo.getColonelId());
                    updateReqVO.setChannelId(ToolUtil.isEmptyReturn(reqVo.getChannelId(), memBranch.getChannelId()));
                    updateReqVO.setSalePriceCode(reqVo.getSalePriceCode());

                    // 更新门店信息
                    memBranchService.updateMemBranch(updateReqVO);
                    // 更新缓存
                    memBranchService.reloadBranchDTOCache(memBranch.getBranchId());
                    //推送 同步门店信息
                    memBranchService.syncBranchData(memBranch.getBranchId(), OperationType.UPDATE.getCode());
                } catch (Exception e) {
                    log.error("门店【{}】批量编辑操作失败:{}！", memBranch.getBranchId(), e.getMessage());
                    msg.append(StringUtils.format("<br/><span style='color:red'>门店【{}】批量编辑操作失败:{}！</span>", memBranch.getBranchId(),  e.getMessage()));
                }
            }
            if (ToolUtil.isEmpty(msg)) {
                msg.append("批量编辑操作成功！");
            }
        }
        return msg.toString();
    }

    @Override
    public List<MemBranch> getAreaIdExistBranch(Long areaId, Long sysCode) {
        return memBranchMapper.getAreaIdExistBranch(areaId,sysCode);
    }

    @Override
    public List<MemBranch> getMemBranchByChannelId(Long channelId, Long sysCode) {
        return memBranchMapper.getMemBranchByChannelId(channelId, sysCode);
    }

    @Override
    public List<Long> getBranchIdListByBranchName(String branchName, Long sysCode) {
        return memBranchMapper.getBranchIdListByBranchName(
                branchName,
                sysCode
        );
    }

    @Override
    public ColonelBranchLifecycleQtyDTO getColonelBranchLifecycle() {
        //业务员获取所管理门店的生命周期信息
        //获取业务员信息
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        if (Objects.isNull(colonelId)) {
            throw exception(APP_COLONEL_CHECK_COLONEL_ID);
        }


        return memBranchMapper.getColonelBranchLifecycle(colonelId);
    }


    @Override
    public ImportResultVo importBranchExcel(List<MemBranchImportExcel> branchList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(branchList)) {
            throw new ServiceException("导入数据为空");
        }
        Long dcId = SecurityUtils.getDcId();
        if (Objects.isNull(dcId)) {
            throw new ServiceException("非运营商角色");
        }
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();

        // 获取渠道数据
        List<ChannelDTO> channelDTOList = channelApi.getChannelList().getCheckedData();
        Map<String, ChannelDTO> channelMap = convertMap(channelDTOList, ChannelDTO::getChannelName);

        //获取城市区域数据
        List<AreaDTO> areaDTOList = areaApi.getListBySysCode(sysCode).getCheckedData();
        Map<String, AreaDTO> areaDTOMap = convertMap(areaDTOList, AreaDTO::getAreaName);

        List<MemColonel> colonelDTOList = memColonelMapper.getListBySysCode(sysCode);
        Map<String, MemColonel> colonelDTOMap = convertMap(colonelDTOList, MemColonel::getColonelName);

        // 获取平台城市分组数据
        List<GroupDTO> groupDTOList = groupApi.getListBySysCode(sysCode).getCheckedData();
        Map<String, GroupDTO> groupMap = convertMap(groupDTOList, GroupDTO::getGroupName);

        Map<MemBranch, MemMember> branchCache = new LinkedHashMap<>();
        for (MemBranchImportExcel data : branchList) {
            // 校验门店编号
            if (StringUtils.isEmpty(data.getBranchNo())) {
                data.appendImportErrorMsg("门店编号不能为空");
            } else if (data.getBranchNo().length() > 25) {
                data.appendImportErrorMsg("门店编号字符长度超长25");
            } else if (!data.getBranchNo().matches("[a-zA-Z0-9]+")) {
                data.appendImportErrorMsg("门店编号只能包括数字或字母");
            } else {
                MemBranch branch = memBranchMapper.selectMemBranchByBranchNo(data.getBranchNo());
                if (ObjectUtil.isNotEmpty(branch)) {
                    data.appendImportErrorMsg("门店编号已存在");
                } else {
                    boolean isBranch = branchCache.keySet().stream().anyMatch(key -> Objects.equals(key.getBranchNo(), data.getBranchNo()));
                    if (isBranch) {
                        data.appendImportErrorMsg(StringUtils.format("门店编号【{}】在当前导入数据存在重复数据", data.getBranchNo()));
                    }
                }
            }

            // 校验门店名称
            if (StringUtils.isEmpty(data.getBranchName())) {
                data.appendImportErrorMsg("门店名称不能为空");
            } else if (data.getBranchName().length() > 50) {
                data.appendImportErrorMsg("门店名称字符长度超长50");
            } else {
            }

            // 校验门店联系人
            if (StringUtils.isEmpty(data.getContactName())) {
                data.appendImportErrorMsg("门店联系人不能为空");
            } else if (data.getContactName().length() > 20) {
                data.appendImportErrorMsg("门店联系人字符长度超长20");
            } else {
            }

            // 校验联系电话
            if (StringUtils.isEmpty(data.getContactPhone())) {
                data.appendImportErrorMsg("联系电话不能为空");
            } else if (data.getContactPhone().length() != 11) {
                data.appendImportErrorMsg("联系电话字符长度只能是11位");
            } else if (!data.getContactPhone().matches("\\d+")) {
                data.appendImportErrorMsg("联系电话只能是纯数字");
            } else {
            }

            // 校验联系地址
            if (StringUtils.isEmpty(data.getBranchAddr())) {
                data.appendImportErrorMsg("联系地址不能为空");
            } else {
            }

            // 校验渠道类型
            ChannelDTO channel = null;
            if (StringUtils.isNotEmpty(data.getChannelName())) {
                channel = channelMap.get(data.getChannelName());
                if (Objects.isNull(channel)) {
                    data.appendImportErrorMsg("渠道类型不存在");
                }
            }

            // 校验是否启用状态
            Integer status = null;
            if (StringUtils.isNotEmpty(data.getStatusName())) {
                status = SysYesNoEnum.getYesNo(data.getStatusName());
                if (Objects.isNull(status)) {
                    data.appendImportErrorMsg("是否启用状态不存在");
                }
            }

            // 校验城市区域
            AreaDTO area = null;
            if (StringUtils.isEmpty(data.getAreaName())) {
                data.appendImportErrorMsg("城市区域不能为空");
            } else {
                area = areaDTOMap.get(data.getAreaName());
                if (Objects.isNull(area)) {
                    data.appendImportErrorMsg("城市区域不存在");
                }
            }

            // 校验业务员
            MemColonel colonel = null;
            if (StringUtils.isEmpty(data.getColonelName())) {
                data.appendImportErrorMsg("业务员不能为空");
            } else {
                colonel = colonelDTOMap.get(data.getColonelName());
                if (Objects.isNull(colonel)) {
                    data.appendImportErrorMsg("业务员不存在");
                }
            }

            // 校验是否支持货到付款
            Integer yesNo = null;
            if (StringUtils.isNotEmpty(data.getHdfkSupportName())) {
                yesNo = SysYesNoEnum.getYesNo(data.getHdfkSupportName());
                if (Objects.isNull(yesNo)) {
                    data.appendImportErrorMsg("是否支持货到付款不存在");
                } else {
                    if (ObjectUtil.isEmpty(data.getHdfkMaxAmt()) && yesNo == 1) {
                        data.appendImportErrorMsg("货到付款最大可欠款金额为空");
                    }
                }
            }

            if (StringUtils.isNotEmpty(data.getMemo()) && data.getMemo().length() > 500) {
                data.appendImportErrorMsg("备注字段超出500长度");
            }

            // 校验平台城市分组
            GroupDTO group = null;
            if (StringUtils.isNotEmpty(data.getGroupName())) {
                group = groupMap.get(data.getGroupName());
                if (Objects.isNull(group)) {
                    data.appendImportErrorMsg("平台城市分组不存在");
                }
            } else {
                GroupDTO defaultGrouping = groupApi.getDefaultGrouping().getCheckedData();
                if(ToolUtil.isEmpty(defaultGrouping.getGroupId())){
                    throw new ServiceException("全国分组为空");
                }
                group = defaultGrouping; // 若未填写，则默认系统第一个分组
            }

            Long threeAreaCityId = null;
            // 校验城市(省/市/县区)
            if (StringUtils.isNotEmpty(data.getCity())) {
                String[] cityParts = data.getCity().split("/");
                if (cityParts.length != 3) {
                    data.appendImportErrorMsg("城市格式不正确，正确格式为 省/市/县区");
                } else {
                    // 依次验证 省、市、区是否存在，并获取它们的ID
                    Long provinceId = getCityIdByNameAndDeep(cityParts[0], 0, null);
                    if (provinceId != null) {
                        Long cityId = getCityIdByNameAndDeep(cityParts[1], 1, provinceId);
                        if (cityId != null) {
                            threeAreaCityId = getCityIdByNameAndDeep(cityParts[2], 2, cityId);
                        }
                    }
                    if (threeAreaCityId == null) {
                        data.appendImportErrorMsg("城市(省/市/县区)格式不正确或不存在");
                    }
                }
            }else{
                data.appendImportErrorMsg("城市(省/市/县区)必填");
            }

            // 转换导入实体 门店信息
            MemBranch memBranch = new MemBranch();
            {
                BranchConvert.INSTANCE.convertImport(memBranch, data);
                memBranch.setAreaId(Optional.ofNullable(area).map(AreaDTO::getAreaId).orElse(null))
                        .setColonelId(Optional.ofNullable(colonel).map(MemColonel::getColonelId).orElse(null))
                        .setChannelId(ObjectUtil.isNull(channel) ? null : channel.getChannelId())
                        .setStatus(ObjectUtil.isNull(status) ? FLAG_TRUE : status)
                        .setDelFlag(NumberPool.INT_ZERO)
                        .setGroupId(Optional.ofNullable(group).map(GroupDTO::getGroupId).orElse(null))
                        .setThreeAreaCityId(threeAreaCityId)
                        .setHdfkSupport(ObjectUtil.isNull(yesNo) ? FLAG_FALSE : yesNo)
                        .setHdfkMaxAmt(ObjectUtil.isNull(data.getHdfkMaxAmt()) ? BigDecimal.ZERO : data.getHdfkMaxAmt())
                        .setSysCode(sysCode);
                if (threeAreaCityId != null) {
                    //省市区填值已做校验，validateCity
                    String[] cityParts = data.getCity().split("/");
                    memBranch.setProvinceName(cityParts[0])
                            .setCityName(cityParts[1])
                            .setDistrictName(cityParts[2]);
                }
            }
            String s = branchVerify(memBranch,null);
            if (StringUtils.isNotEmpty(s)) {
                data.appendImportErrorMsg(s);
            }

            // 转换导入实体 用户信息
            MemMember memMember = new MemMember();
            {
                memMember.setMemberPhone(data.getContactPhone())
                        .setMemberName(data.getContactName())
                        .setStatus(ObjectUtil.isNull(status) ? StatusConstants.FLAG_TRUE : status)
                        .setUserName(data.getContactPhone())
                        .setPassword(SecurityUtils.encryptPassword(memberCacheService.getDefaultPassword(sysCode)))
                ;

            }
            branchCache.put(memBranch, memMember);

            if (!data.isHasError()) {
                try {
                    SpringUtils.getBean(MemBranchServiceImpl.class).importBranch(memBranch, memMember, sysCode);
                } catch (Exception e) {
                    data.appendImportErrorMsg(e.getMessage());
                }
            }
        }
        List<MemBranchImportExcel> errorVos = branchList.stream().filter(ImportErrorVo::isHasError).collect(Collectors.toList());
        ImportResultVo importResultVo = new ImportResultVo();
        importResultVo.setTotal(branchList.size());
        importResultVo.setFailCount(errorVos.size());
        importResultVo.setSuccessCount(importResultVo.getTotal() - importResultVo.getFailCount());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(errorVos)) {
            ExcelUtil<MemBranchImportExcel> util = new ExcelUtil<>(MemBranchImportExcel.class);
            MultipartFile errorFile = util.dataToMultipartFile(errorVos, "门店导入错误信息");
            SysFile sysFile = uploadImportErrorFile(errorFile);
            importResultVo.setSysFile(sysFile);
        }
        return importResultVo;
    }

    @Override
    public com.baomidou.mybatisplus.extension.plugins.pagination.Page<MemBranch> selectMemBranchBalancePage(com.baomidou.mybatisplus.extension.plugins.pagination.Page<MemBranch> page, MemBranchBalancePageReqDTO pageReqDTO) {
        return memBranchMapper.selectMemBranchBalancePage(page, pageReqDTO);
    }

    private SysFile uploadImportErrorFile(MultipartFile file) {
        CommonResult<String> result;
        try {
            result = fileApi.uploadFileByMail(file);
            if (result == null || !result.isSuccess()) {
                throw new ServiceException(StrUtil.format("上传文件失败, result: {}", JSON.toJSONString(result)));
            }
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new RuntimeException(e);
        }
        String url = result.getCheckedData();
        SysFile sysFile = new SysFile();
        sysFile.setName(FileUtils.getName(url));
        sysFile.setUrl(url);
        return sysFile;
    }

    /**
     * 计算两个经纬度之间的直线距离（单位：公里）
     * @param lat1 第一点纬度
     * @param lon1 第一点经度
     * @param lat2 第二点纬度
     * @param lon2 第二点经度
     * @return 距离（公里）
     */
    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371; // 地球半径（公里）

        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);

        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    }

}
