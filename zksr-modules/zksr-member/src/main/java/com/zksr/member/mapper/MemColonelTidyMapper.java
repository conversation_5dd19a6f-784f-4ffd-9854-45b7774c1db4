package com.zksr.member.mapper;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.domain.MemColonelVisitLog;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemColonelTidy;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyPageReqVO;

import java.util.ArrayList;
import java.util.List;


/**
 * 业务员理货记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Mapper
public interface MemColonelTidyMapper extends BaseMapperX<MemColonelTidy> {
    default PageResult<MemColonelTidy> selectPage(MemColonelTidyPageReqVO reqVO) {

        List<Long> colonelTidyIdList = new ArrayList<>();
        if(ToolUtil.isNotEmpty(reqVO.getColonelTidyIdList())){
            reqVO.getColonelTidyIdList().forEach(item->{
                colonelTidyIdList.add(Long.parseLong(item));
            });
        }
        return selectPage(reqVO, new LambdaQueryWrapperX<MemColonelTidy>()
                    .eqIfPresent(MemColonelTidy::getColonelTidyId, reqVO.getColonelTidyId())
                    .eqIfPresent(MemColonelTidy::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemColonelTidy::getColonelId, reqVO.getColonelId())
                    .eqIfPresent(MemColonelTidy::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(MemColonelTidy::getTidyImg, reqVO.getTidyImg())
                    .eqIfPresent(MemColonelTidy::getTidyDescr, reqVO.getTidyDescr())
                .inIfPresent(MemColonelTidy::getBranchId, reqVO.getBranchIdList())
                .betweenIfPresent(MemColonelTidy::getCreateTime, reqVO.getBeginCreateTime(), reqVO.getEndCreateTime())
                .inIfPresent(MemColonelTidy::getColonelTidyId,colonelTidyIdList )
                .orderByDesc(MemColonelTidy::getColonelTidyId));
    }
}
