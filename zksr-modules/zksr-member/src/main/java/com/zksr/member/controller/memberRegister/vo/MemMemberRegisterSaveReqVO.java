package com.zksr.member.controller.memberRegister.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 用户注册信息对象 mem_member_register
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@Data
@ApiModel("用户注册信息 - mem_member_register分页 Request VO")
@Accessors(chain = true)
public class MemMemberRegisterSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 用户注册信息ID */
    @ApiModelProperty(value = "备注")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberRegisterId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 用户名 */
    @Excel(name = "用户名")
    @ApiModelProperty(value = "用户名")
    private String memberName;

    /**
     * 门店编码
     */
    @ApiModelProperty(value = "门店编码")
    private String branchNo;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 用户账号（联系号码） */
    @Excel(name = "用户账号（联系号码）")
    @ApiModelProperty(value = "用户账号（联系号码）")
    @NotEmpty(message = "用户账号（联系号码）不能为空")
    private String userName;

    /** 用户密码 */
    @Excel(name = "用户密码")
    @ApiModelProperty(value = "用户密码")
    private String password;

    /** 门头照 */
    @Excel(name = "门头照")
    @ApiModelProperty(value = "门头照")
    private String branchImages;

    /** 审核人 */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人")
    private Long approveMan;

    /** 审核标识 */
    @Excel(name = "审核标识")
    @ApiModelProperty(value = "审核标识")
    private Integer approveFlag;

    /** 审核时间 */
    @Excel(name = "审核时间")
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveDate;

    /** 用户自动审核标识 */
    @Excel(name = "用户自动审核标识")
    @ApiModelProperty(value = "用户自动审核标识")
    private Integer memberApproveFlag;

    /** 用户过期时间;过了过期时间，则停用 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "用户过期时间;过了过期时间，则停用", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "用户过期时间;过了过期时间，则停用")
    private Date memberExpirationDate;

    /** 门店自动审核标识 */
    @Excel(name = "门店自动审核标识")
    @ApiModelProperty(value = "门店自动审核标识")
    private Integer branchApproveFlag;

    /** 门店过期时间;过了过期时间，则停用 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "门店过期时间;过了过期时间，则停用", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "门店过期时间;过了过期时间，则停用")
    private Date branchExpirationDate;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 业务员id */
    @Excel(name = "业务员id")
    @ApiModelProperty(value = "业务员id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 渠道id */
    @Excel(name = "渠道id")
    @ApiModelProperty(value = "渠道id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long channelId;

    /** 用户类型 0 新用户  1老用户 */
    @Excel(name = "用户类型")
    @ApiModelProperty(value = "用户类型 0 新用户  1老用户")
    private Integer userType;

    /** 电子围栏入驻商ID信息，以,逗号间隔 */
    @ApiModelProperty(value = "电子围栏入驻商ID信息，以,逗号间隔")
    private String supplierIds;

    @ApiModelProperty("三级区域城市ID, 省市区关联")
    private Long threeAreaCityId;

    @ApiModelProperty(value = "是否支持货到付款(0,否 1,是)")
    private Integer hdfkSupport;

    /** 省份 */
    @ApiModelProperty(value = "省份")
    private String provinceName;

    /** 城市 */
    @ApiModelProperty(value = "城市")
    private String cityName;

    /** 区县 */
    @ApiModelProperty(value = "区县")
    private String districtName;


    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;


    @Excel(name = "货到付款最大可欠款金额")
    @ApiModelProperty("货到付款最大可欠款金额")
    private BigDecimal hdfkMaxAmt;

    @ApiModelProperty("是否支持在线收款 0：否，1：是")
    private Integer isPayOnline;

}
