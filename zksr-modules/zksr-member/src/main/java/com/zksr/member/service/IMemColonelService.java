package com.zksr.member.service;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.member.api.colonel.excel.MemColonelImportExcel;
import com.zksr.member.api.colonel.vo.WxColonelPublishOpenidBindReqVO;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import com.zksr.member.domain.MemColonel;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;

import javax.validation.Valid;
import java.util.List;

/**
 * 业务员信息Service接口
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
public interface IMemColonelService {

    /**
     * 新增业务员信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemColonel(@Valid MemColonelSaveReqVO createReqVO);

    /**
     * 修改业务员信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemColonel(@Valid MemColonelSaveReqVO updateReqVO);

    /**
     * 业务员审核
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void salesmanAudit(@Valid MemColonelSaveReqVO updateReqVO);

    /**
     * 删除业务员信息
     *
     * @param colonelId 业务员id
     */
    public void deleteMemColonel(Long colonelId);

    /**
     * 批量删除业务员信息
     *
     * @param colonelIds 需要删除的业务员信息主键集合
     * @return 结果
     */
    public void deleteMemColonelByColonelIds(Long[] colonelIds);

    /**
     * 获得业务员信息
     *
     * @param colonelId 业务员id
     * @return 业务员信息
     */
    public MemColonel getMemColonel(Long colonelId);

    /**
     * 获得业务员信息分页
     *
     * @param pageReqVO 分页查询
     * @return 业务员信息分页
     */
    PageResult<MemColonel> getMemColonelPage(MemColonelPageReqVO pageReqVO);

    /**
     * 分页查询未绑定关系的业务员
     *
     * @param pageReqVO 分页查询
     * @return 分页查询未绑定关系的业务员
     */
    PageResult<MemColonelRespVO> getColonelNotRelationPage(MemColonelPageReqVO pageReqVO);

    /**
     * 停用业务员信息
     * @param colonelId
     */
    public void disable(Long colonelId);

    /**
     * 启用业务员信息
     * @param colonelId
     */
    public void enable(Long colonelId);

    /**
     * 获得业务员销售金额信息
     *
     * @param pageReqVO 分页查询
     * @return 获得业务员销售金额信息分页
     */
    public PageResult<MemColonelRespVO> MemColonelSalesAmtList(MemColonelPageReqVO pageReqVO);

    /**
     * 获得业务员拜访次数或者理货次数
     *
     * @param pageReqVO 分页查询
     * @return 获得业务员拜访次数或者理货次数
     */
    public PageResult<MemColonelRespVO> getMemColonelNumPage(MemColonelPageReqVO pageReqVO);

    /**
     * 业务员信息导入
     * @param colonelList
     * @return
     */
    String impordData(List<MemColonelImportExcel> colonelList);

    FileImportHandlerVo impordDataEvent(List<MemColonelImportExcel> colonelList, String funcScop, Long sysCode, Long fileImportId,Integer seq);

    /**
     * 获取某个业务员的直接下属业务员列表
     * @param colonelId
     * @return
     */
    List<ColonelDTO> getSubordinates(Long colonelId);

    /**
     * 获取某个业务员的所发展的业务员信息
     * @param colonelId
     * @return
     */
    List<ColonelDTO> selectDevelopmentSalesmanByColonelId(Long colonelId);

    /**
     * 根据业务员ID获取名称
     * @return
     */
    String getColonelName(Long colonelId);

    /**
     * 清空业务员缓存
     * @param colonelId
     */
    public void removeColonelCache(Long colonelId);

    /**
     * 得到当前登录账号所能查询到的业务员信息
     * @return
     */
    public List<MemColonel> getMemColonelList();

    /**
     * 获取业务员所有子集
     */
    List<Long> getAllChildColonel(Long colonelId);

    /**
     * 业务员绑定微信公众号openid
     */
    CommonResult<Boolean> updateColonelPublishOpenidBind(WxColonelPublishOpenidBindReqVO reqVO);

    List<MemColonel> getAreaIdExistColone(Long areaId, Long sysCode);

    void updateMemColonelPassword(MemColonelSaveReqVO updateReqVO);
}
