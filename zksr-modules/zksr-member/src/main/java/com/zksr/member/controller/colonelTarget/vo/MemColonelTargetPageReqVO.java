package com.zksr.member.controller.colonelTarget.vo;

import java.math.BigDecimal;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 业务员目标设置对象 mem_colonel_target
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@ApiModel("业务员目标设置 - mem_colonel_target分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemColonelTargetPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long colonelTargetId;

    /** 业务员Id */
    @Excel(name = "业务员Id")
    @ApiModelProperty(value = "业务员Id")
    private Long colonelId;

    /** 目标年份 */
    @Excel(name = "目标年份")
    @ApiModelProperty(value = "目标年份")
    private String targetYear;

    /** 目标月份 */
    @Excel(name = "目标月份")
    @ApiModelProperty(value = "目标月份")
    private String targetMonth;

    /** 销售额 */
    @Excel(name = "销售额")
    @ApiModelProperty(value = "销售额")
    private BigDecimal salesMoney;

    /** 月新开客户数量 */
    @Excel(name = "月新开客户数量")
    @ApiModelProperty(value = "月新开客户数量")
    private Long monthNewCustomer;

    /** 月活动客户数量 */
    @Excel(name = "月活动客户数量")
    @ApiModelProperty(value = "月活动客户数量")
    private Long monthActivityCustomer;

    /** 月拜访客户数量 */
    @Excel(name = "月拜访客户数量")
    @ApiModelProperty(value = "月拜访客户数量")
    private Long monthVisitCustomer;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态 1正常 0停用 */
    @Excel(name = "状态 1正常 0停用")
    @ApiModelProperty(value = "状态 1正常 0停用")
    private Integer status;


    @Excel(name = "业务员姓名")
    @ApiModelProperty(value = "业务员姓名")
    private String colonelName;


    @ApiModelProperty(value = "区域城市ID")
    private Long areaId;
}
