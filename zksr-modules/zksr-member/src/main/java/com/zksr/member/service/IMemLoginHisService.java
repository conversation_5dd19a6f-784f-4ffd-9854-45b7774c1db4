package com.zksr.member.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.controller.loginHis.vo.MemLoginHisPageReqVO;
import com.zksr.member.controller.loginHis.vo.MemLoginHisSaveReqVO;
import com.zksr.member.domain.MemLoginHis;

import javax.validation.Valid;

/**
 * 登录历史Service接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IMemLoginHisService {

    /**
     * 新增登录历史
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemLoginHis(@Valid MemLoginHisSaveReqVO createReqVO);

    /**
     * 修改登录历史
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemLoginHis(@Valid MemLoginHisSaveReqVO updateReqVO);

    /**
     * 删除登录历史
     *
     * @param loginHisId 登录历史id
     */
    public void deleteMemLoginHis(Long loginHisId);

    /**
     * 批量删除登录历史
     *
     * @param loginHisIds 需要删除的登录历史主键集合
     * @return 结果
     */
    public void deleteMemLoginHisByLoginHisIds(Long[] loginHisIds);

    /**
     * 获得登录历史
     *
     * @param loginHisId 登录历史id
     * @return 登录历史
     */
    public MemLoginHis getMemLoginHis(Long loginHisId);

    /**
     * 获得登录历史分页
     *
     * @param pageReqVO 分页查询
     * @return 登录历史分页
     */
    PageResult<MemLoginHis> getMemLoginHisPage(MemLoginHisPageReqVO pageReqVO);

}
