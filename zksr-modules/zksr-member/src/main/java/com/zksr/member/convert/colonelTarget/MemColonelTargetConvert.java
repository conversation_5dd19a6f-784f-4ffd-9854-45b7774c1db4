package com.zksr.member.convert.colonelTarget;

import com.zksr.member.controller.colonelTarget.vo.MemColonelTargetSaveReqVO;
import com.zksr.member.domain.MemColonelTarget;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 业务员目标对象封装转换器
 */
@Mapper
public interface MemColonelTargetConvert {
    MemColonelTargetConvert INSTANCE = Mappers.getMapper(MemColonelTargetConvert.class);

    List<MemColonelTarget> convertList(List<MemColonelTargetSaveReqVO> saveReqVO);
}
