package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 用户信息对象 mem_member
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@TableName(value = "mem_member")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MemMember extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 用户id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    @ApiModelProperty("运营商ID")
    private Long dcId;

    /** 用户手机号 */
    @Excel(name = "用户手机号")
    private String memberPhone;

    /** 用户名 */
    @Excel(name = "用户名")
    private String memberName;

    /** 微信unionid */
    @Excel(name = "微信unionid")
    private String wxUnionid;

    /** 头像 */
    @Excel(name = "头像")
    private String avatar;

    /** 状态：1正常  0禁用 */
    @Excel(name = "状态：1正常  0禁用")
    private Integer status;

    /** 小程序openid */
    @Excel(name = "小程序openid")
    private String xcxOpenid;

    /** 注册业务员id */
    @Excel(name = "注册业务员id")
    private Long registerColonelId;

    /** 登录token */
    @Excel(name = "登录token")
    private String loginToken;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 最后一次登录时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "最后一次登录时间", width = 30, dateFormat = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "最后一次登录时间")
    private Date lastLoginTime;

    /** 过期时间 */
    @Excel(name = "过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate; 		 // 过期时间


    /** 用户账号 */
    @Excel(name = "用户账号")
    private String userName;

    /** 用户密码 */
    @Excel(name = "用户密码")
    private String password;

    /** 是否为店长用户 */
    @Excel(name = "是否为店长用户")
    private Integer isShopManager;

    /** 父ID */
    @Excel(name = "父ID")
    private Long pid;

    @ApiModelProperty("公众号openid")
    private String publishOpenid;

    @ApiModelProperty("是否是业务员 1：是 0：否")
    private Integer isColonel;

    @ApiModelProperty("关联业务员ID")
    private Long relateColonelId;

    @ApiModelProperty("设备ID")
    private String deviceId;

    @ApiModelProperty("外部客户编码")
    private String outerUserCode;
}
