package com.zksr.member.controller.searchHis.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 搜索历史对象 mem_search_his
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel("搜索历史 - mem_search_his Response VO")
public class MemSearchHisRespVO {
    private static final long serialVersionUID = 1L;

    /** 搜索历史id */
    @ApiModelProperty(value = "搜索词")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long searchHisId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 门店用户id */
    @Excel(name = "门店用户id")
    @ApiModelProperty(value = "门店用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /** 门店用户名称 */
    @Excel(name = "门店用户名称")
    @ApiModelProperty(value = "门店用户名称")
    private String memberName;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddress;

    /** 所属城市ID */
    @Excel(name = "所属城市ID")
    @ApiModelProperty(value = "所属城市ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 所属城市 */
    @Excel(name = "所属城市")
    @ApiModelProperty(value = "所属城市")
    private String area;

    /** 搜索词 */
    @Excel(name = "搜索词")
    @ApiModelProperty(value = "搜索词")
    private String words;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 业务员名称 */
    @Excel(name = "业务员名称")
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;

    /** 是否已加入词库 */
    @Excel(name = "是否已加入词库")
    @ApiModelProperty(value = "是否已加入词库")
    private boolean isJoin;

}
