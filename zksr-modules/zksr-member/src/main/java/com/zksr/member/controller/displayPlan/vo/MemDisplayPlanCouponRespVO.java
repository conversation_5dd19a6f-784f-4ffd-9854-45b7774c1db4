package com.zksr.member.controller.displayPlan.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 陈列计划优惠明细对象 mem_display_plan_coupon
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Data
@ApiModel("陈列计划优惠明细 - mem_display_plan_coupon Response VO")
public class MemDisplayPlanCouponRespVO {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "备注")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long planCouponId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 陈列计划ID */
    @Excel(name = "陈列计划ID")
    @ApiModelProperty(value = "陈列计划ID")
    private Long planId;

    /** 优惠劵ID */
    @Excel(name = "优惠劵ID")
    @ApiModelProperty(value = "优惠劵ID")
    private Long couponId;

    /** 约定兑付总数 */
    @Excel(name = "约定兑付总数")
    @ApiModelProperty(value = "约定兑付总数")
    private Long exchangeSum;

    /** 已兑付总数 */
    @Excel(name = "已兑付总数")
    @ApiModelProperty(value = "已兑付总数")
    private Long finishSum;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 删除状态(0:正常，2：删除) */
    @ApiModelProperty(value = "备注")
    private Integer delFlag;

}
