package com.zksr.member.controller.relation;

import javax.validation.Valid;

import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.member.controller.colonelTarget.MemColonelTargetController;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemColonelRelation;
import com.zksr.member.service.IMemColonelRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.relation.vo.MemColonelRelationPageReqVO;
import com.zksr.member.controller.relation.vo.MemColonelRelationSaveReqVO;
import com.zksr.member.controller.relation.vo.MemColonelRelationRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 业务员关系Controller
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Api(tags = "管理后台 - 业务员关系接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/relation")
public class MemColonelRelationController {
    @Autowired
    private IMemColonelRelationService memColonelRelationService;

    /**
     * 新增业务员关系
     */
    @ApiOperation(value = "新增业务员关系", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "业务员关系", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemColonelRelationSaveReqVO createReqVO) {
        return success(memColonelRelationService.insertMemColonelRelation(createReqVO));
    }

    /**
     * 修改业务员关系
     */
    @ApiOperation(value = "修改业务员关系", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "业务员关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemColonelRelationSaveReqVO updateReqVO) {
            memColonelRelationService.updateMemColonelRelation(updateReqVO);
        return success(true);
    }

    /**
     * 删除业务员关系
     */
    @ApiOperation(value = "删除业务员关系", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "业务员关系", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public CommonResult<Boolean> remove(@RequestBody Long[] colonelRelationIds) {
        memColonelRelationService.deleteMemColonelRelationByColonelRelationIds(colonelRelationIds);
        return success(true);
    }

    /**
     * 获取业务员关系详细信息
     */
    @ApiOperation(value = "获得业务员关系详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{colonelRelationId}")
    public CommonResult<MemColonelRelationRespVO> getInfo(@PathVariable("colonelRelationId") Long colonelRelationId) {
        MemColonelRelation memColonelRelation = memColonelRelationService.getMemColonelRelation(colonelRelationId);
        return success(HutoolBeanUtils.toBean(memColonelRelation, MemColonelRelationRespVO.class));
    }

    /**
     * 分页查询业务员关系
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得业务员关系分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(dcAlias = "colonel", dcFieldAlias = SystemConstants.AREA_ID)
    public CommonResult<PageResult<MemColonelRelationRespVO>> getPage(@Valid MemColonelRelationPageReqVO pageReqVO) {
        return success(memColonelRelationService.getMemColonelRelationPage(pageReqVO));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:relation:add";
        /** 编辑 */
        public static final String EDIT = "member:relation:edit";
        /** 删除 */
        public static final String DELETE = "member:relation:remove";
        /** 列表 */
        public static final String LIST = "member:relation:list";
        /** 查询 */
        public static final String GET = "member:relation:query";
    }

}
