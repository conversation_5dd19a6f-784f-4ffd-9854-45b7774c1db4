package com.zksr.member.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemMemberInvoice;
import com.zksr.member.controller.invoice.vo.MemMemberInvoicePageReqVO;


/**
 * 用户发票Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Mapper
public interface MemMemberInvoiceMapper extends BaseMapperX<MemMemberInvoice> {
    default PageResult<MemMemberInvoice> selectPage(MemMemberInvoicePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemMemberInvoice>()
                    .eqIfPresent(MemMemberInvoice::getId, reqVO.getId())
                    .eqIfPresent(MemMemberInvoice::getMemberId, reqVO.getMemberId())
                    .eqIfPresent(MemMemberInvoice::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemMemberInvoice::getInvoiceType, reqVO.getInvoiceType())
                    .eqIfPresent(MemMemberInvoice::getTitleType, reqVO.getTitleType())
                    .eqIfPresent(MemMemberInvoice::getInvoiceTitle, reqVO.getInvoiceTitle())
                    .eqIfPresent(MemMemberInvoice::getTaxpayerCode, reqVO.getTaxpayerCode())
                    .eqIfPresent(MemMemberInvoice::getCompanyAddress, reqVO.getCompanyAddress())
                    .eqIfPresent(MemMemberInvoice::getCompanyPhone, reqVO.getCompanyPhone())
                    .likeIfPresent(MemMemberInvoice::getContactName, reqVO.getContactName())
                    .eqIfPresent(MemMemberInvoice::getContactPhone, reqVO.getContactPhone())
                    .likeIfPresent(MemMemberInvoice::getBankName, reqVO.getBankName())
                    .eqIfPresent(MemMemberInvoice::getBankAccount, reqVO.getBankAccount())
                    .eqIfPresent(MemMemberInvoice::getRemark, reqVO.getRemark())
                    .eqIfPresent(MemMemberInvoice::getDelFlag, reqVO.getDelFlag())
                    .eqIfPresent(MemMemberInvoice::getVersion, reqVO.getVersion())
                .orderByDesc(MemMemberInvoice::getId));
    }
}
