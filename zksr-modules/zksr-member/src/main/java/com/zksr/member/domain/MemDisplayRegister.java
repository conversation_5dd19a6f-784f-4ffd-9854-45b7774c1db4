package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 陈列计划打卡记录对象 mem_display_register
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@TableName(value = "mem_display_register")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemDisplayRegister extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long registerId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 陈列计划ID */
    @Excel(name = "陈列计划ID")
    private Long planId;

    /** 照片 */
    @Excel(name = "照片")
    private String pic;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 删除状态(0:正常，2：删除) */
    private Long delFlag;

}
