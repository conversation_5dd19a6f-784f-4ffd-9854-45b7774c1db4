package com.zksr.member.controller.colonelApp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员APP更新配置
 * @date 2024/8/19 15:12
 */
@Data
@ApiModel(description = "业务员APP更新配置")
public class ColonelAppUpdateInfoVO {

    @ApiModelProperty("appVersion使用这个来判断版本是否需要更新")
    private String appVersion;

    @ApiModelProperty("这个只是app显示更新版本")
    private String appVersionName;

    @ApiModelProperty("appVersion使  使用时间戳填充(IOS)")
    private String iosVersion;

    @ApiModelProperty("这个只是app显示更新版本(IOS)")
    private String iosVersionName;

    @ApiModelProperty("app版本更新内容 (安卓)")
    private String appVersionContent;

    @ApiModelProperty("app版本更新内容 (IOS)")
    private String iosVersionContent;

    @ApiModelProperty("app更新包位置, 仅支持安卓")
    private String appUpdateUrl;

    @ApiModelProperty("是否强制更新,0-不强制, 1-强制更新 (安卓)")
    private String forceUpdate;

    @ApiModelProperty("是否强制更新,0-不强制, 1-强制更新 (IOS)")
    private String iosForceUpdate;
}
