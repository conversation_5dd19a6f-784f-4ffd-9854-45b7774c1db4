package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

/**
 * 业务员 业务日结表 对象 mem_colonel_day_settle
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@TableName(value = "mem_colonel_day_settle")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MemColonelDaySettle extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    @Excel(name = "主键ID")
    private Long colonelDaySettleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 结算创建日期 */
    @Excel(name = "结算创建日期")
    private Date settleCreateDate;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    private Long colonelId;

    /** 下单数量（客户下单） */
    @Excel(name = "下单数量")
    private Long orderQty;

    /** 门店下单金额 */
    @Excel(name = "门店下单金额")
    private BigDecimal branchOrderAmt;

    /** 下单数量（业务员代客下单） */
    @Excel(name = "业务下单数量（业务员代客下单）")
    private Long businessOrderQty;

    /** 业务下单金额（业务员代客下单） */
    @Excel(name = "业务下单金额（业务员代客下单）")
    private BigDecimal businessOrderAmt;

    /** 业务提成 */
    @Excel(name = "业务提成")
    private BigDecimal percentageAmt;

    /** 门店退货金额 */
    @Excel(name = "门店退货金额")
    private BigDecimal branchRefundAmt;

    /** 拜访门店数量 */
    @Excel(name = "拜访门店数量")
    private Long visitQty;

    /** 拓店数量 */
    @Excel(name = "拓店数量")
    private Long addBranchQty;

    /** 动销门店数量 */
    @Excel(name = "动销门店数量")
    private Long saleBranchQty;
}