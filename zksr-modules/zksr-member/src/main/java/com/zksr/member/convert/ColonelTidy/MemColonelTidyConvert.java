package com.zksr.member.convert.ColonelTidy;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemColonelTidy;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyRespVO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidySaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 业务员理货记录 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-20
*/
@Mapper
public interface MemColonelTidyConvert {

    MemColonelTidyConvert INSTANCE = Mappers.getMapper(MemColonelTidyConvert.class);

    MemColonelTidyRespVO convert(MemColonelTidy memColonelTidy);

    MemColonelTidy convert(MemColonelTidySaveReqVO memColonelTidySaveReq);

    PageResult<MemColonelTidyRespVO> convertPage(PageResult<MemColonelTidy> memColonelTidyPage);
}