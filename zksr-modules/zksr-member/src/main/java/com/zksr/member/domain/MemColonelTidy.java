package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 业务员理货记录对象 mem_colonel_tidy
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@TableName(value = "mem_colonel_tidy")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemColonelTidy extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 业务员理货记录id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long colonelTidyId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    private Long colonelId;

    /** 门店ID */
    @Excel(name = "门店ID")
    private Long branchId;

    /** 理货图片 */
    @Excel(name = "理货图片")
    private String tidyImg;

    /** 理货说明 */
    @Excel(name = "理货说明")
    private String tidyDescr;

}
