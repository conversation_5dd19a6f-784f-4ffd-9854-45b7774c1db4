package com.zksr.member.controller.colonelTarget.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 业务员目标设置对象 mem_colonel_target
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Data
@ApiModel("业务员目标设置 - mem_colonel_target Response VO")
public class MemColonelTargetRespVO {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelTargetId;

    /** 业务员Id */
    @Excel(name = "业务员Id")
    @ApiModelProperty(value = "业务员Id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelId;

    /** 目标年份 */
    @Excel(name = "目标年份")
    @ApiModelProperty(value = "目标年份")
    private String targetYear;

    /** 目标月份 */
    @Excel(name = "目标月份")
    @ApiModelProperty(value = "目标月份")
    private String targetMonth;

    /** 销售额 */
    @Excel(name = "销售额")
    @ApiModelProperty(value = "销售额")
    private BigDecimal salesMoney;

    /** 月新开客户数量 */
    @Excel(name = "月新开客户数量")
    @ApiModelProperty(value = "月新开客户数量")
    private Long monthNewCustomer;

    /** 月活动客户数量 */
    @Excel(name = "月活动客户数量")
    @ApiModelProperty(value = "月活动客户数量")
    private Long monthActivityCustomer;

    /** 月拜访客户数量 */
    @Excel(name = "月拜访客户数量")
    @ApiModelProperty(value = "月拜访客户数量")
    private Long monthVisitCustomer;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态 1正常 0停用 */
    @Excel(name = "状态 1正常 0停用")
    @ApiModelProperty(value = "状态 1正常 0停用")
    private Integer status;

    @Excel(name = "部门ID")
    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    @Excel(name = "业务员名称")
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;


    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "创建时间", width = 30, dateFormat = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "区域城市ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 月下单数量 */
    @Excel(name = "月下单数量")
    @ApiModelProperty(value = "月下单数量")
    private Long monthOrderCount;

    /** 月客单价 */
    @Excel(name = "月客单价")
    @ApiModelProperty(value = "月客单价")
    private BigDecimal monthAvgOrderValue;

    /** 月首次动销数量 */
    @ApiModelProperty(value = "月首次动销数量")
    @Excel(name = "月首次动销数量")
    private Long monthFirstSaleCount;

}
