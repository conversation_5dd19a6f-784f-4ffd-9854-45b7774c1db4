package com.zksr.member.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemDisplayType;
import com.zksr.member.controller.displayType.vo.MemDisplayTypePageReqVO;


/**
 * 陈列类型Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@Mapper
public interface MemDisplayTypeMapper extends BaseMapperX<MemDisplayType> {
    default PageResult<MemDisplayType> selectPage(MemDisplayTypePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemDisplayType>()
                    .eqIfPresent(MemDisplayType::getDisplayId, reqVO.getDisplayId())
                    .eqIfPresent(MemDisplayType::getSysCode, reqVO.getSysCode())
                    .likeIfPresent(MemDisplayType::getDisplayName, reqVO.getDisplayName())
                    .eqIfPresent(MemDisplayType::getStartTime, reqVO.getStartTime())
                    .eqIfPresent(MemDisplayType::getEndTime, reqVO.getEndTime())
                    .eqIfPresent(MemDisplayType::getAuditState, reqVO.getAuditState())
                    .eqIfPresent(MemDisplayType::getAuditBy, reqVO.getAuditBy())
                    .eqIfPresent(MemDisplayType::getAuditTime, reqVO.getAuditTime())
                    .eqIfPresent(MemDisplayType::getType, reqVO.getType())
                    .eqIfPresent(MemDisplayType::getDelFlag, reqVO.getDelFlag())
                    .eqIfPresent(MemDisplayType::getMemo, reqVO.getMemo())
                .orderByDesc(MemDisplayType::getDisplayId));
    }
}
