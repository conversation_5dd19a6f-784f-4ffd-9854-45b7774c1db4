package com.zksr.member.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.complain.MemComplainVO;
import com.zksr.member.api.complain.dto.MemComplainDTO;
import com.zksr.member.domain.MemComplain;


/**
 * <p>
 * 投诉信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-06
 */
public interface IMemComplainService extends IService<MemComplain> {
    /**
     * 查询用户的投诉列表
     * @param memComplainVO
     * @return
     */
    PageResult<MemComplainVO> getComplainList(MemComplainVO memComplainVO);

    /**
     * 新增投诉信息
     * @param memComplainVO
     * @return
     */
    CommonResult<String> insertComplain(MemComplainDTO  memComplainVO);

    /**
     * 查询投诉详情
     * @param complainId
     * @return
     */
    MemComplainVO getComplainByComplainId(String complainId);

    /**
     * 后台分页查询投诉列表
     * @param memComplainVO
     * @return
     */
    PageResult<MemComplain> getComplainPageList(MemComplainVO memComplainVO);

    /**
     * 处理投诉信息
     * @param memComplainVO
     */
    void updateComplain(MemComplainVO memComplainVO);
}
