package com.zksr.member.controller.visitLog.vo;

import lombok.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 业务员拜访日志对象 mem_colonel_visit_log
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@Data
@ApiModel("业务员拜访日志 - mem_colonel_visit_log分页 Request VO")
public class MemColonelVisitLogSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Excel(name = "主键Id")
    @ApiModelProperty(value = "主键Id")
    private Long colonelVisitLogId;

    @ApiModelProperty("客户编码")
    private Long consumerNo;
    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", example = "示例值")
    private Long sysCode;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    /** 门店ID */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    /** 签到经度 */
    @Excel(name = "签到经度")
    @ApiModelProperty(value = "签到经度")
    private String signInLongitude;

    /** 签到纬度 */
    @Excel(name = "签到纬度")
    @ApiModelProperty(value = "签到纬度")
    private String signInLatitude;

    /** 签到地址 */
    @Excel(name = "签到地址")
    @ApiModelProperty(value = "签到地址")
    private String signInAddress;

    /** 签到距离 */
    @Excel(name = "签到距离")
    @ApiModelProperty(value = "签到距离")
    private String signInDistance;

    /** 签到图片链接：多个以英文,隔开 */
    @Excel(name = "签到图片链接：多个以英文,隔开")
    @ApiModelProperty(value = "签到图片链接：多个以英文,隔开")
    private String signInImgUrls;

    /** 签到时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "签到时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "签到时间")
    private Date signInDate;

    /** 签退经度 */
    @Excel(name = "签退经度")
    @ApiModelProperty(value = "签退经度")
    private String signOutLongitude;

    /** 签退维度 */
    @Excel(name = "签退维度")
    @ApiModelProperty(value = "签退维度")
    private String signOutLatitude;

    /** 签退地址 */
    @Excel(name = "签退地址")
    @ApiModelProperty(value = "签退地址")
    private String signOutAddress;

    /** 签退距离 */
    @Excel(name = "签退距离")
    @ApiModelProperty(value = "签退距离")
    private String signOutDistance;

    /** 签退时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "签退时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "签退时间")
    private Date signOutDate;

    /** 拜访状态 0签到，1签退，2作废 */
    @Excel(name = "拜访状态 0签到，1签退，2作废")
    @ApiModelProperty(value = "拜访状态 0签到，1签退，2作废")
    private Integer visitFlag;

    /** 拜访间隔时间 */
    @Excel(name = "拜访间隔时间")
    @ApiModelProperty(value = "拜访间隔时间")
    private String visitIntervalTime;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 删除状态(0:正常，2：删除) */
    @ApiModelProperty(value = "备注")
    private Integer delFlag;

}
