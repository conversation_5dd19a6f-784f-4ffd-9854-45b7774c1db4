package com.zksr.member.controller.colonelApp;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.controller.colonelApp.vo.ColonelBranchAnalyseInfoRespVO;
import com.zksr.member.controller.colonelApp.vo.ColonelDecisionTotalRespVO;
import com.zksr.member.service.IMemColonelAppReportService;
import com.zksr.report.api.branch.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @Date 2024/4/16 14:29
 * @业务员AppController
 */
@Api(tags = "业务员app - 业务员App报表接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/colonelAppReport")
public class MemColonelAppReportController {

    @Autowired
    private IMemColonelAppReportService colonelAppReportService;

    /**
     * 业务员APP首页-任务销售统计
     */
    @ApiOperation(value = "业务员APP首页-任务销售统计", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.BASE_REPORT)
    @RequiresPermissions(Permissions.BASE_REPORT)
    @PostMapping("/decisionTotal")
    public CommonResult<ColonelDecisionTotalRespVO> decisionTotal(@Valid @RequestBody ColonelDecisionTotalReqVO reqVO) {
        return success(colonelAppReportService.getDecisionTotal(reqVO));
    }


    /**
     * 业务员APP首页-门店客户等级统计数据
     */
    @ApiOperation(value = "业务员APP首页-门店客户等级统计数据", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.BASE_REPORT)
    @RequiresPermissions(Permissions.BASE_REPORT)
    @PostMapping("/branchLevelTotal")
    public CommonResult<ColonelIndexBranchLevelTotalRespVO> indexBranchLevelTotal(@Valid @RequestBody ColonelDecisionTotalReqVO reqVO) {
        return success(colonelAppReportService.indexBranchLevelTotal(reqVO));
    }

    /**
     * 业务员APP首页-月门店周下单统计
     */
    @ApiOperation(value = "业务员APP首页-月门店周下单统计", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.BASE_REPORT)
    @RequiresPermissions(Permissions.BASE_REPORT)
    @PostMapping("/indexWeekSaleTotal")
    public CommonResult<ColonelBranchMonthWeekTotalRespVO> indexWeekSaleTotal(@Valid @RequestBody ColonelDecisionTotalReqVO reqVO) {
        return success(colonelAppReportService.indexWeekSaleTotal(reqVO));
    }

    /**
     * 业务员APP首页-月订货类别占比
     */
    @ApiOperation(value = "业务员APP首页-月订货类别占比", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.BASE_REPORT)
    @RequiresPermissions(Permissions.BASE_REPORT)
    @PostMapping("/indexSaleCategoryTotal")
    public CommonResult<ColonelBranchSaleCategoryTotalRespVO> indexSaleCategoryTotal(@Valid @RequestBody ColonelDecisionTotalReqVO reqVO) {
        return success(colonelAppReportService.indexSaleCategoryTotal(reqVO));
    }

    /**
     * 业务员APP首页-24小时下单数量
     */
    @ApiOperation(value = "业务员APP首页-24小时下单数量", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.BASE_REPORT)
    @RequiresPermissions(Permissions.BASE_REPORT)
    @PostMapping("/hourSale")
    public CommonResult<ColonelHoursSaleRespVO> hourSale(@Valid @RequestBody ColonelDecisionTotalReqVO reqVO) {
        return success(colonelAppReportService.hourSale(reqVO));
    }

    /**
     * 业务员APP首页-业务员门店等级数量
     */
    @ApiOperation(value = "业务员APP首页-业务员门店等级数量", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.BASE_REPORT)
    @RequiresPermissions(Permissions.BASE_REPORT)
    @PostMapping("/branchLevelColonelList")
    public CommonResult<ColonelLevelTotalRespVO> branchLevelColonelList(@Valid @RequestBody ColonelBranchTagPageReqVO reqVO) {
        return success(colonelAppReportService.branchLevelColonelList(reqVO));
    }

    /**
     * 业务员APP首页-业务员具体标签-门店列表
     */
    @ApiOperation(value = "业务员APP首页-业务员具体标签-门店列表", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.BASE_REPORT)
    @RequiresPermissions(Permissions.BASE_REPORT)
    @PostMapping("/tagBranchList")
    public CommonResult<PageResult<ColonelTagBranchRespVO>> tagBranchList(@RequestBody TagBranchListReqVO reqVO) {
        return success(colonelAppReportService.tagLevelBranchList(reqVO));
    }


    /**
     * 业务员APP首页-门店数据分析
     */
    @ApiOperation(value = "业务员APP首页-门店数据分析", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.BASE_REPORT)
    @RequiresPermissions(Permissions.BASE_REPORT)
    @PostMapping("/branchAnalyseInfo")
    public CommonResult<ColonelBranchAnalyseInfoRespVO> branchAnalyseInfo(@Valid @RequestBody BranchAnalyseInfoReqVO reqVO) {
        return success(colonelAppReportService.branchAnalyseInfo(reqVO));
    }




    /**
     * 权限字符
     */
    public static class Permissions {
        /** 基础报表 */
        public static final String BASE_REPORT = "member:colonelApp:baseReport";
    }


}
