package com.zksr.member.controller.branchUser;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemBranchUser;
import com.zksr.member.service.IMemBranchUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.branchUser.vo.MemBranchUserPageReqVO;
import com.zksr.member.controller.branchUser.vo.MemBranchUserSaveReqVO;
import com.zksr.member.controller.branchUser.vo.MemBranchUserRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 门店用户关联关系Controller
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@Api(tags = "管理后台 - 门店用户关联关系接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/user")
public class MemBranchUserController {
    @Autowired
    private IMemBranchUserService memBranchUserService;

    /**
     * 新增门店用户关联关系
     */
    @ApiOperation(value = "新增门店用户关联关系", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "门店用户关联关系", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemBranchUserSaveReqVO createReqVO) {
        return success(memBranchUserService.insertMemBranchUser(createReqVO));
    }

    /**
     * 修改门店用户关联关系
     */
    @ApiOperation(value = "修改门店用户关联关系", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "门店用户关联关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemBranchUserSaveReqVO updateReqVO) {
            memBranchUserService.updateMemBranchUser(updateReqVO);
        return success(true);
    }

    /**
     * 删除门店用户关联关系
     */
    @ApiOperation(value = "删除门店用户关联关系", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "门店用户关联关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sysCodes}")
    public CommonResult<Boolean> remove(@PathVariable Long[] sysCodes) {
        memBranchUserService.deleteMemBranchUserBySysCodes(sysCodes);
        return success(true);
    }

    /**
     * 获取门店用户关联关系详细信息
     */
    @ApiOperation(value = "获得门店用户关联关系详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{sysCode}")
    public CommonResult<MemBranchUserRespVO> getInfo(@PathVariable("sysCode") Long sysCode) {
        MemBranchUser memBranchUser = memBranchUserService.getMemBranchUser(sysCode);
        return success(HutoolBeanUtils.toBean(memBranchUser, MemBranchUserRespVO.class));
    }

    /**
     * 分页查询门店用户关联关系
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得门店用户关联关系分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemBranchUserRespVO>> getPage(@Valid MemBranchUserPageReqVO pageReqVO) {
        PageResult<MemBranchUser> pageResult = memBranchUserService.getMemBranchUserPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, MemBranchUserRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:user:add";
        /** 编辑 */
        public static final String EDIT = "member:user:edit";
        /** 删除 */
        public static final String DELETE = "member:user:remove";
        /** 列表 */
        public static final String LIST = "member:user:list";
        /** 查询 */
        public static final String GET = "member:user:query";
        /** 停用 */
        public static final String DISABLE = "member:user:disable";
        /** 启用 */
        public static final String ENABLE = "member:user:enable";
    }
}
