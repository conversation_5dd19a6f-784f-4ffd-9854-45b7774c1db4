package com.zksr.member.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyRespVO;
import com.zksr.member.controller.visitLog.vo.MemColonelVisitLogPageReqVO;
import com.zksr.member.domain.MemColonelTidy;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidyPageReqVO;
import com.zksr.member.controller.ColonelTidy.vo.MemColonelTidySaveReqVO;

import java.io.IOException;

/**
 * 业务员理货记录Service接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
public interface IMemColonelTidyService {

    /**
     * 新增业务员理货记录
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertMemColonelTidy(@Valid MemColonelTidySaveReqVO createReqVO);

    /**
     * 修改业务员理货记录
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateMemColonelTidy(@Valid MemColonelTidySaveReqVO updateReqVO);

    /**
     * 删除业务员理货记录
     *
     * @param colonelTidyId 业务员理货记录id
     */
    public void deleteMemColonelTidy(Long colonelTidyId);

    /**
     * 批量删除业务员理货记录
     *
     * @param colonelTidyIds 需要删除的业务员理货记录主键集合
     * @return 结果
     */
    public void deleteMemColonelTidyByColonelTidyIds(Long[] colonelTidyIds);

    /**
     * 获得业务员理货记录
     *
     * @param colonelTidyId 业务员理货记录id
     * @return 业务员理货记录
     */
    public MemColonelTidy getMemColonelTidy(Long colonelTidyId);

    /**
     * 获得业务员理货记录分页
     *
     * @param pageReqVO 分页查询
     * @return 业务员理货记录分页
     */
    PageResult<MemColonelTidyRespVO> getMemColonelTidyPage(MemColonelTidyPageReqVO pageReqVO);

    /**
     * 下载业务员理货记录的图片
     *
     * @param pageReqVO
     * @return 下载业务员理货记录的图片
     */
    byte[] getColonelTidyImageZip(MemColonelTidyPageReqVO pageReqVO) throws IOException;

}
