package com.zksr.member.api.member;

import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.member.api.member.dto.MemMemberSaveReqVO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.member.api.member.vo.MemberUpdatePwdVO;
import com.zksr.member.api.member.vo.MemberUpdateUserInfoVO;
import com.zksr.member.domain.MemMember;
import com.zksr.member.service.IMemMemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.*;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Slf4j
@ApiIgnore
public class MemberApiImpl implements MemberApi {

    @Autowired
    private IMemMemberService memMemberService;

    @InnerAuth
    @Override
    public CommonResult<MemberDTO> memberRegister(MemMemberSaveReqVO createReqVO) {
        Long memberId = memMemberService.insertMemMember(createReqVO);
        MemMember memMember = memMemberService.getMemMember(memberId);
        return success(HutoolBeanUtils.toBean(memMember, MemberDTO.class));
    }

    @InnerAuth
    @Override
    public CommonResult<MemberDTO> getInfoByMobileAndOpenid(@RequestParam(name = "sysCode", required = false) Long sysCode,
                                                            @RequestParam(name = "mobile", required = false) String mobile,
                                                            @RequestParam(name = "openid", required = false) String openid,
                                                            @RequestParam(name = "userName", required = false) String userName) {
        Long sysCode2 =  SecurityContextHolder.getSysCode();
        log.info("sysCode:{}", sysCode2);

        MemMember memMember = memMemberService.getInfoByMobileAndOpenid(sysCode, mobile, openid,userName);
        return success(HutoolBeanUtils.toBean(memMember, MemberDTO.class));
    }

    @InnerAuth
    @Override
    public CommonResult<MemberDTO> getMemBerByMemberId(Long memberId) {
        MemMember memMember = memMemberService.getMemMember(memberId);
        return success(HutoolBeanUtils.toBean(memMember, MemberDTO.class));
    }

    @Override
    @InnerAuth
    public CommonResult<Boolean> updateDefaultBranch(@RequestParam("memberId") Long memberId, @RequestParam("branchId") Long branchId, @RequestParam("sysCode")Long sysCode) {
        memMemberService.updateDefaultBranch(memberId,branchId,sysCode);
        return success(true);
    }

    @Override
    @InnerAuth
    public CommonResult<List<MemberDTO>> getAllExpirationDateMemberList(Long sysCode) {
        return CommonResult.success(memMemberService.getAllExpirationDateMemberList(sysCode));
    }

    @Override
    @InnerAuth
    public CommonResult<Boolean> updateStatusByMemberIds(MemberDTO memberDTO) {
        memMemberService.updateStatusByMemberIds(memberDTO);
        return CommonResult.success(true);
    }

    @Override
    @InnerAuth
    public CommonResult<Boolean> insertChildUser(MemberDTO memberDTO) {
        memMemberService.insertChildUser(memberDTO);
        return success(true);
    }

    @Override
    @InnerAuth
    public CommonResult<List<MemberDTO>> childUserList(@RequestParam("memberId")Long memberId) {
        return success(memMemberService.childUserList(memberId));
    }

    @Override
    public CommonResult<Boolean> updateMemberToken(MemberDTO memberDTO) {
        return success(memMemberService.updateMemberToken(HutoolBeanUtils.toBean(memberDTO, MemMember.class)));
    }

    @Override
    public CommonResult<Boolean> updateMemberPublishOpenid(Long memberId, String publishOpenid) {
        memMemberService.updateMemberPublishOpenid(memberId, publishOpenid);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> updateMemberPwd(MemberUpdatePwdVO memberUpdatePwdVO) {
        MemMember member = new MemMember();
        member.setMemberPhone(memberUpdatePwdVO.getPhone());
        member.setPassword(memberUpdatePwdVO.getPassword());
        memMemberService.updateMemberPwd(member);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> updateMemberUserInfoByMemberId(MemberUpdateUserInfoVO memberUpdateUserInfoVO) {
        memMemberService.updateMemberUserInfoByMemberId(HutoolBeanUtils.toBean(memberUpdateUserInfoVO, MemMember.class));
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<MemberDTO> getMemBerByColonelId(Long colonelId, Long sysCode) {
        return success(HutoolBeanUtils.toBean(memMemberService.getMemBerByColonelId(colonelId, sysCode), MemberDTO.class));
    }

    @Override
    public CommonResult<MemberDTO> createColonelMember(MemberDTO memberDTO) {
        return success(HutoolBeanUtils.toBean(memMemberService.createColonelMember(memberDTO), MemberDTO.class));
    }

    @Override
    public CommonResult<Boolean> updateMemberDeviceId(MemberDTO memberDTO) {
        memMemberService.updateMemberDeviceId(memberDTO);
        return success(Boolean.TRUE);
    }
}
