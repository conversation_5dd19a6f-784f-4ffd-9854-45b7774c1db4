package com.zksr.member.api.member;

import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.member.api.member.dto.*;
import com.zksr.member.api.member.vo.MemberAddressRespDTO;
import com.zksr.member.api.member.vo.MemberUpdatePwdVO;
import com.zksr.member.api.member.vo.MemberUpdateUserInfoVO;
import com.zksr.member.controller.address.vo.MemMemberAddressPageReqVO;
import com.zksr.member.controller.address.vo.MemMemberAddressSaveReqVO;
import com.zksr.member.controller.invoice.vo.MemMemberInvoicePageReqVO;
import com.zksr.member.controller.invoice.vo.MemMemberInvoiceSaveReqVO;
import com.zksr.member.convert.address.MemMemberAddressConvert;
import com.zksr.member.convert.invoice.MemMemberInvoiceConvert;
import com.zksr.member.domain.MemMember;
import com.zksr.member.domain.MemMemberAddress;
import com.zksr.member.domain.MemMemberInvoice;
import com.zksr.member.service.IMemMemberAddressService;
import com.zksr.member.service.IMemMemberInvoiceService;
import com.zksr.member.service.IMemMemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.*;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Slf4j
@ApiIgnore
public class MemberApiImpl implements MemberApi {

    @Autowired
    private IMemMemberService memMemberService;

    @Autowired
    private IMemMemberAddressService memMemberAddressService;

    @Resource
    private IMemMemberInvoiceService memMemberInvoiceService;


    @InnerAuth
    @Override
    public CommonResult<MemberDTO> memberRegister(MemMemberSaveReqVO createReqVO) {
        Long memberId = memMemberService.insertMemMember(createReqVO);
        MemMember memMember = memMemberService.getMemMember(memberId);
        return success(HutoolBeanUtils.toBean(memMember, MemberDTO.class));
    }

    @InnerAuth
    @Override
    public CommonResult<MemberDTO> getInfoByMobileAndOpenid(@RequestParam(name = "sysCode", required = false) Long sysCode,
                                                            @RequestParam(name = "mobile", required = false) String mobile,
                                                            @RequestParam(name = "openid", required = false) String openid,
                                                            @RequestParam(name = "userName", required = false) String userName) {
        Long sysCode2 =  SecurityContextHolder.getSysCode();
        log.info("sysCode:{}", sysCode2);

        MemMember memMember = memMemberService.getInfoByMobileAndOpenid(sysCode, mobile, openid,userName);
        return success(HutoolBeanUtils.toBean(memMember, MemberDTO.class));
    }

    @InnerAuth
    @Override
    public CommonResult<MemberDTO> getMemBerByMemberId(Long memberId) {
        MemMember memMember = memMemberService.getMemMember(memberId);
        return success(HutoolBeanUtils.toBean(memMember, MemberDTO.class));
    }

    @Override
    @InnerAuth
    public CommonResult<Boolean> updateDefaultBranch(@RequestParam("memberId") Long memberId, @RequestParam("branchId") Long branchId, @RequestParam("sysCode")Long sysCode) {
        memMemberService.updateDefaultBranch(memberId,branchId,sysCode);
        return success(true);
    }

    @Override
    @InnerAuth
    public CommonResult<List<MemberDTO>> getAllExpirationDateMemberList(Long sysCode) {
        return CommonResult.success(memMemberService.getAllExpirationDateMemberList(sysCode));
    }

    @Override
    @InnerAuth
    public CommonResult<Boolean> updateStatusByMemberIds(MemberDTO memberDTO) {
        memMemberService.updateStatusByMemberIds(memberDTO);
        return CommonResult.success(true);
    }

    @Override
    @InnerAuth
    public CommonResult<Boolean> insertChildUser(MemberDTO memberDTO) {
        memMemberService.insertChildUser(memberDTO);
        return success(true);
    }

    @Override
    @InnerAuth
    public CommonResult<List<MemberDTO>> childUserList(@RequestParam("memberId")Long memberId) {
        return success(memMemberService.childUserList(memberId));
    }

    @Override
    public CommonResult<Boolean> updateMemberToken(MemberDTO memberDTO) {
        return success(memMemberService.updateMemberToken(HutoolBeanUtils.toBean(memberDTO, MemMember.class)));
    }

    @Override
    public CommonResult<Boolean> updateMemberPublishOpenid(Long memberId, String publishOpenid) {
        memMemberService.updateMemberPublishOpenid(memberId, publishOpenid);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> updateMemberPwd(MemberUpdatePwdVO memberUpdatePwdVO) {
        MemMember member = new MemMember();
        member.setMemberPhone(memberUpdatePwdVO.getPhone());
        member.setPassword(memberUpdatePwdVO.getPassword());
        memMemberService.updateMemberPwd(member);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> updateMemberUserInfoByMemberId(MemberUpdateUserInfoVO memberUpdateUserInfoVO) {
        memMemberService.updateMemberUserInfoByMemberId(HutoolBeanUtils.toBean(memberUpdateUserInfoVO, MemMember.class));
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<MemberDTO> getMemBerByColonelId(Long colonelId, Long sysCode) {
        return success(HutoolBeanUtils.toBean(memMemberService.getMemBerByColonelId(colonelId, sysCode), MemberDTO.class));
    }

    @Override
    public CommonResult<MemberDTO> createColonelMember(MemberDTO memberDTO) {
        return success(HutoolBeanUtils.toBean(memMemberService.createColonelMember(memberDTO), MemberDTO.class));
    }

    @Override
    public CommonResult<Boolean> updateMemberDeviceId(MemberDTO memberDTO) {
        memMemberService.updateMemberDeviceId(memberDTO);
        return success(Boolean.TRUE);
    }

    /**
     * 新增用户地址
     */
    @Override
    public CommonResult<Long> addMemberAddress(MemMemberAddressSaveReqDTO createReqDTO) {
        MemMemberAddressSaveReqVO createReqVO = MemMemberAddressConvert.INSTANCE.convert2MemMemberAddressSaveReqVO(createReqDTO);

        return success(memMemberAddressService.insertMemMemberAddress(createReqVO));
    }

    /**
     * 修改用户地址
     */
    @Override
    public CommonResult<Boolean> updateMemberAddress(MemMemberAddressSaveReqDTO createReqDTO) {
        MemMemberAddressSaveReqVO createReqVO = MemMemberAddressConvert.INSTANCE.convert2MemMemberAddressSaveReqVO(createReqDTO);
        memMemberAddressService.updateMemMemberAddress(createReqVO);
        return success(Boolean.TRUE);
    }

    /**
     * 查询用户地址
     */
    @Override
    public CommonResult<PageResult<MemberAddressRespDTO>> getMemberAddress(MemMemberAddressPageReqDTO pageReqVO) {
        MemMemberAddressPageReqVO pageReq = MemMemberAddressConvert.INSTANCE.convert2MemMemberAddressPageReqVO(pageReqVO);
        PageResult<MemMemberAddress> rs = memMemberAddressService.getMemMemberAddressPage(pageReq);
        return success(MemMemberAddressConvert.INSTANCE.convert2MemberAddressRespDTO(rs));
    }

    /**
     * 新增用户发票
     * @param createReqDTO
     * @return
     */
    @Override
    public CommonResult<Long> addMemberInvoice(MemMemberInvoiceSaveReqDTO createReqDTO) {
        MemMemberInvoiceSaveReqVO createReqVO = MemMemberInvoiceConvert.INSTANCE.convert2MemMemberInvoiceSaveReqVO(createReqDTO);

        return success(memMemberInvoiceService.insertMemMemberInvoice(createReqVO));
    }

    /**
     * 修改用户发票
     */
    @Override
    public CommonResult<Boolean> updateMemberInvoice(MemMemberInvoiceSaveReqDTO createReqDTO) {
        MemMemberInvoiceSaveReqVO createReqVO = MemMemberInvoiceConvert.INSTANCE.convert2MemMemberInvoiceSaveReqVO(createReqDTO);
        memMemberInvoiceService.updateMemMemberInvoice(createReqVO);
        return success(Boolean.TRUE);
    }

    /**
     * 查询用户发票
     * @param pageReqVO
     * @return
     */
    @Override
    public CommonResult<PageResult<MemMemberInvoiceRespDTO>> getMemberInvoice(MemMemberInvoicePageReqDTO pageReqVO) {
        MemMemberInvoicePageReqVO pageReq = MemMemberInvoiceConvert.INSTANCE.convert2MemMemberInvoicePageReqVO(pageReqVO);
        PageResult<MemMemberInvoice> rs = memMemberInvoiceService.getMemMemberInvoicePage(pageReq);
        return success(MemMemberInvoiceConvert.INSTANCE.convert2MemMemberInvoiceRespDTO(rs));
    }

    /**
     * 查询用户发票根据指定id
     * @param id
     * @return
     */
    @Override
    public CommonResult<MemMemberInvoiceRespDTO> getMemberInvoiceById(Long id) {
        MemMemberInvoice memMemberInvoice = memMemberInvoiceService.getMemMemberInvoice(id);
        return success(MemMemberInvoiceConvert.INSTANCE.convert2MemMemberInvoiceResp(memMemberInvoice));
    }


    @InnerAuth
    @Override
    public CommonResult<MemberDTO> createMemberCaseAutoLogin(MemMemberSaveReqVO createReqVO) {
        MemMember memMember = memMemberService.createMemberCaseAutoLogin(createReqVO);
        return success(HutoolBeanUtils.toBean(memMember, MemberDTO.class));
    }

    @InnerAuth
    @Override
    public CommonResult<MemberDTO> getMemberByOuterUserCode(Long sysCode, String outerUserCode) {
        MemMember memMember = memMemberService.getMemberByOuterUserCode(sysCode, outerUserCode);
        return success(HutoolBeanUtils.toBean(memMember, MemberDTO.class));
    }
}
