package com.zksr.member.controller.branchLifeCycleZip;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.member.domain.MemBranchLifecycleZip;
import com.zksr.member.service.IMemBranchLifecycleZipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipPageReqVO;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipSaveReqVO;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipRespVO;
import com.zksr.member.convert.branchLifeCycleZip.MemBranchLifecycleZipConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 门店生命周期拉链Controller
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Api(tags = "管理后台 - 门店生命周期拉链接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/branchLifeCycleZip")
public class MemBranchLifecycleZipController {
    @Autowired
    private IMemBranchLifecycleZipService memBranchLifecycleZipService;

    /**
     * 新增门店生命周期拉链
     */
    @ApiOperation(value = "新增门店生命周期拉链", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "门店生命周期拉链", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody MemBranchLifecycleZipSaveReqVO createReqVO) {
        return success(memBranchLifecycleZipService.insertMemBranchLifecycleZip(createReqVO));
    }

    /**
     * 修改门店生命周期拉链
     */
    @ApiOperation(value = "修改门店生命周期拉链", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "门店生命周期拉链", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody MemBranchLifecycleZipSaveReqVO updateReqVO) {
            memBranchLifecycleZipService.updateMemBranchLifecycleZip(updateReqVO);
        return success(true);
    }

    /**
     * 删除门店生命周期拉链
     */
    @ApiOperation(value = "删除门店生命周期拉链", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "门店生命周期拉链", businessType = BusinessType.DELETE)
    @DeleteMapping("/{branchLifecycleZipIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] branchLifecycleZipIds) {
        memBranchLifecycleZipService.deleteMemBranchLifecycleZipByBranchLifecycleZipIds(branchLifecycleZipIds);
        return success(true);
    }

    /**
     * 获取门店生命周期拉链详细信息
     */
    @ApiOperation(value = "获得门店生命周期拉链详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{branchLifecycleZipId}")
    public CommonResult<MemBranchLifecycleZipRespVO> getInfo(@PathVariable("branchLifecycleZipId") Long branchLifecycleZipId) {
        MemBranchLifecycleZip memBranchLifecycleZip = memBranchLifecycleZipService.getMemBranchLifecycleZip(branchLifecycleZipId);
        return success(MemBranchLifecycleZipConvert.INSTANCE.convert(memBranchLifecycleZip));
    }


    /**
     * 分页查询门店生命周期拉链
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得门店生命周期拉链分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<MemBranchLifecycleZipRespVO>> getPage(@Valid MemBranchLifecycleZipPageReqVO pageReqVO) {
        return success(memBranchLifecycleZipService.getMemBranchLifecycleZipPage(pageReqVO));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "member:branchLifeCycleZip:add";
        /** 编辑 */
        public static final String EDIT = "member:branchLifeCycleZip:edit";
        /** 删除 */
        public static final String DELETE = "member:branchLifeCycleZip:remove";
        /** 列表 */
        public static final String LIST = "member:branchLifeCycleZip:list";
        /** 查询 */
        public static final String GET = "member:branchLifeCycleZip:query";
        /** 停用 */
        public static final String DISABLE = "member:branchLifeCycleZip:disable";
        /** 启用 */
        public static final String ENABLE = "member:branchLifeCycleZip:enable";
    }
}
