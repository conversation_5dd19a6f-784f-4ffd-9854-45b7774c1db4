package com.zksr.member.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.domain.MemColonelVisitLog;
import com.zksr.trade.api.order.dto.ColonelOrderDaySettleDTO;
import com.zksr.trade.api.order.vo.ColonelOrderDaySettleVO;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemBranchRegister;
import com.zksr.member.controller.branchRegister.vo.MemBranchRegisterPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.constant.StatusConstants.*;


/**
 * 门店注册信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@Mapper
public interface MemBranchRegisterMapper extends BaseMapperX<MemBranchRegister> {
    default PageResult<MemBranchRegister> selectPage(MemBranchRegisterPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemBranchRegister>()
                    .eqIfPresent(MemBranchRegister::getBranchRegisterId, reqVO.getBranchRegisterId())
                    .eqIfPresent(MemBranchRegister::getSysCode, reqVO.getSysCode())
                    .likeIfPresent(MemBranchRegister::getBranchName, reqVO.getBranchName())
                    .likeIfPresent(MemBranchRegister::getContactName, reqVO.getContactName())
                    .eqIfPresent(MemBranchRegister::getContactPhone, reqVO.getContactPhone())
                    .eqIfPresent(MemBranchRegister::getBranchAddr, reqVO.getBranchAddr())
                    .eqIfPresent(MemBranchRegister::getLongitude, reqVO.getLongitude())
                    .eqIfPresent(MemBranchRegister::getLatitude, reqVO.getLatitude())
                    .eqIfPresent(MemBranchRegister::getColonelId, reqVO.getColonelId())
                    .eqIfPresent(MemBranchRegister::getChannelId, reqVO.getChannelId())
                    .eqIfPresent(MemBranchRegister::getBranchImages, reqVO.getBranchImages())
                    .eqIfPresent(MemBranchRegister::getApproveMan, reqVO.getApproveMan())
                    .eqIfPresent(MemBranchRegister::getApproveFlag, reqVO.getApproveFlag())
                    .eqIfPresent(MemBranchRegister::getBranchApproveFlag, reqVO.getBranchApproveFlag())
                    .eqIfPresent(MemBranchRegister::getMemo, reqVO.getMemo())
                    .eqIfPresent(MemBranchRegister::getStatus,reqVO.getStatus())
                    .betweenIfPresent(MemBranchRegister::getCreateTime, reqVO.getStartTime(), reqVO.getEndTime())
                    .applyScope(reqVO.getParams())
                .orderByDesc(MemBranchRegister::getBranchRegisterId));
    }

    /**
     * 批量审核
     * @param branchRegisterIds
     * @param approveMan
     */
    default void branchRegisterApprove(List<Long> branchRegisterIds, Long approveMan){

        update(null, new LambdaUpdateWrapper<MemBranchRegister>().
                set(MemBranchRegister::getApproveFlag,AUDIT_STATE_1).
                set(MemBranchRegister::getApproveDate,new Date()).
                set(MemBranchRegister::getApproveMan,approveMan).
                in(MemBranchRegister::getBranchRegisterId,branchRegisterIds));
    }

    /**
     * 批量停用
     * @param branchRegisterIds
     */
    default void deleteMemBranchRegisterByBranchRegisterIds(Long[] branchRegisterIds){
        update(null, new LambdaUpdateWrapper<MemBranchRegister>()
                .set(MemBranchRegister::getStatus,STATE_DISABLE)
                .in(MemBranchRegister::getBranchRegisterId,branchRegisterIds)
                .eq(MemBranchRegister::getApproveFlag,AUDIT_STATE_0));
    }

    default MemBranchRegister getMemBranchRegister(MemBranchRegisterRespVO respVO){
        LambdaQueryWrapperX<MemBranchRegister> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(MemBranchRegister::getBranchRegisterId,respVO.getBranchRegisterId());
        return selectOne(wrapper);
    }


    /**
     * 根据用户登录名（手机号）查询门店申请信息
     * @param userName  用户登录名（手机号）
     * @return
     */
    default List<MemBranchRegister> getMemBranchRegisterByUserName(String userName){
        LambdaQueryWrapperX<MemBranchRegister> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(MemBranchRegister::getContactPhone, userName);
        return selectList(wrapper);
    }

    /**
     * 获取业务员 拓店数量信息（根据业务员和日期分组）
     * @param reqVO
     * @return
     */
    List<ColonelOrderDaySettleDTO> getColonelBranchRegisterInfo(ColonelOrderDaySettleVO reqVO);


    /**
     * 根据时间区间获取某业务员的扩店信息
     * @param colonelId 业务员ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 扩店信息
     */
    default List<MemBranchRegister> getMemBranchRegisterListByColonelIdAndDate(Long colonelId, Date startTime, Date endTime){
        return selectList(new LambdaQueryWrapperX<MemBranchRegister>()
                .eqIfPresent(MemBranchRegister::getColonelId, colonelId)
                .betweenIfPresent(MemBranchRegister::getApproveDate, startTime, endTime)
        );
    }

    default void updateMemBranchRegisterBranchId(Long branchId, Long branchRegisterId){
        update(null, new LambdaUpdateWrapper<MemBranchRegister>()
                .eq(MemBranchRegister::getBranchRegisterId, branchRegisterId)
                .set(MemBranchRegister::getBranchId, branchId));
    }

    MemBranchRegister getMemBranchRegisterByBranchId(@Param("branchId") Long branchId);
}
