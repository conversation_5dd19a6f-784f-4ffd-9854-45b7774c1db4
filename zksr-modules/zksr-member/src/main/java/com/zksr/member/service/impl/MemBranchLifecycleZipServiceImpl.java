package com.zksr.member.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.enums.branch.BranchLifecycleTypeEnum;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branchLifecycle.dto.BranchLifecycleStageStartMemoDataDTO;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipRespVO;
import com.zksr.member.domain.MemBranch;
import com.zksr.member.mapper.MemBranchMapper;
import com.zksr.member.service.IMemberCacheService;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.partnerPolicy.dto.BranchLifecycleSettingPolicyDTO;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.TrdOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.member.mapper.MemBranchLifecycleZipMapper;
import com.zksr.member.convert.branchLifeCycleZip.MemBranchLifecycleZipConvert;
import com.zksr.member.domain.MemBranchLifecycleZip;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipPageReqVO;
import com.zksr.member.controller.branchLifeCycleZip.vo.MemBranchLifecycleZipSaveReqVO;
import com.zksr.member.service.IMemBranchLifecycleZipService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.FLAG_TRUE;
import static com.zksr.common.core.constant.StatusConstants.STATE_DISABLE;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.DateUtils.DATETIME_MAX;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;
import static com.zksr.member.constant.MemberConstant.*;
import static com.zksr.member.enums.ErrorCodeConstants.*;

/**
 * 门店生命周期拉链Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Service
@Slf4j
public class MemBranchLifecycleZipServiceImpl implements IMemBranchLifecycleZipService {
    @Autowired
    private MemBranchLifecycleZipMapper memBranchLifecycleZipMapper;
    
    @Autowired
    private IMemberCacheService memberCacheService;

    @Autowired
    private MemBranchMapper memBranchMapper;

    @Autowired
    private OrderApi orderApi;

    @Autowired
    private Cache<Long, BranchDTO> branchDTOCache;

    @Autowired
    private AfterApi afterApi;

    @Resource
    private DcApi dcApi;


    /**
     * 新增门店生命周期拉链
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertMemBranchLifecycleZip(MemBranchLifecycleZipSaveReqVO createReqVO) {
        // 插入
        MemBranchLifecycleZip memBranchLifecycleZip = MemBranchLifecycleZipConvert.INSTANCE.convert(createReqVO);
        memBranchLifecycleZipMapper.insert(memBranchLifecycleZip);
        // 返回
        return memBranchLifecycleZip.getBranchLifecycleZipId();
    }

    /**
     * 修改门店生命周期拉链
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMemBranchLifecycleZip(MemBranchLifecycleZipSaveReqVO updateReqVO) {
        memBranchLifecycleZipMapper.updateById(MemBranchLifecycleZipConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除门店生命周期拉链
     *
     * @param branchLifecycleZipId 门店生命周期拉链表ID
     */
    @Override
    public void deleteMemBranchLifecycleZip(Long branchLifecycleZipId) {
        // 删除
        memBranchLifecycleZipMapper.deleteById(branchLifecycleZipId);
    }

    /**
     * 批量删除门店生命周期拉链
     *
     * @param branchLifecycleZipIds 需要删除的门店生命周期拉链主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMemBranchLifecycleZipByBranchLifecycleZipIds(Long[] branchLifecycleZipIds) {
        for(Long branchLifecycleZipId : branchLifecycleZipIds){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deleteMemBranchLifecycleZip(branchLifecycleZipId);
        }
    }

    /**
     * 获得门店生命周期拉链
     *
     * @param branchLifecycleZipId 门店生命周期拉链表ID
     * @return 门店生命周期拉链
     */
    @Override
    public MemBranchLifecycleZip getMemBranchLifecycleZip(Long branchLifecycleZipId) {
        return memBranchLifecycleZipMapper.selectById(branchLifecycleZipId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<MemBranchLifecycleZipRespVO> getMemBranchLifecycleZipPage(MemBranchLifecycleZipPageReqVO pageReqVO) {
        Long dcId = SecurityUtils.getLoginUser() == null ? null : SecurityUtils.getLoginUser().getDcId();
        List<Long> areaIdList = new ArrayList<>();
        if(null != dcId){
            areaIdList = dcApi.getDcAreaList(dcId).getCheckedData();
        }
        // 将areaIdList赋值到pageReqVO
        pageReqVO.setAreaIdList(areaIdList);

        Page<MemBranchLifecycleZipPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<MemBranchLifecycleZipRespVO> branchLifecycleZipPage = memBranchLifecycleZipMapper.selectPageLifecycle(pageReqVO, page);

        //组装列表数据
        branchLifecycleZipPage.getRecords().forEach(record -> {
            if(record.getAreaId() != null){
                AreaDTO areaDto = memberCacheService.getAreaDto(record.getAreaId());
                if(ToolUtil.isNotEmpty(areaDto)){
                    record.setAreaName(areaDto.getAreaName());
                }
            }

            if(record.getChannelId() != null){
                ChannelDTO channelDto = memberCacheService.getChannelDto(record.getChannelId());
                if(ToolUtil.isNotEmpty(channelDto)){
                    record.setChannelName(channelDto.getChannelName());
                }
            }

            //获取JSON数据信息
            record.parseStartMemoData();


        });

        return new PageResult<>(branchLifecycleZipPage.getRecords(),branchLifecycleZipPage.getTotal());
    }

    @Override
    public String getNextBranchLifecycleMessage(Long branchId) {
        //获取门店生命周期配置
        BranchLifecycleSettingPolicyDTO branchLifecycleSetting = getBranchLifecycleSetting(branchId);
        if (ToolUtil.isEmpty(branchLifecycleSetting)){
            return null;
        }

        //获取该门店最新一条生命周期拉链信息
        MemBranchLifecycleZip latestBranchLifecycleZip = memBranchLifecycleZipMapper.getLatestBranchLifecycleZip(branchId);

        if(ToolUtil.isEmpty(latestBranchLifecycleZip)){
            return null;
        }

        //获取门店的注册时间 用于比对下一阶段生命周期的开始时间
        Date startDate = memBranchMapper.getBranchRegisterTime(branchId);

        TrdOrder order = orderApi.getBranchLatestOrderByBranchId(branchId).getCheckedData();

        //如果下过单 则取下单时间
        if(ToolUtil.isNotEmpty(order)){
            startDate = order.getCreateTime();
        }

        //获取门店的最后一次下单时间

        //下一阶段天数
        Integer day = null;
        //枚举 下一阶段
        BranchLifecycleTypeEnum branchLifecycleTypeEnum = null;
        //匹配生命周期信息  对应BranchLifecycleTypeEnum的枚举值
        switch (latestBranchLifecycleZip.getLifecycleStage()){
            case 1:
                //新客  下一阶段是沉默
                Long nextStageDistanceDay = getNextStageDistanceDay(startDate, branchLifecycleSetting.convertNewCustomerDays());
                if(nextStageDistanceDay == null){
                    return null;
                }else{
                    day = nextStageDistanceDay.intValue();
                    branchLifecycleTypeEnum = BranchLifecycleTypeEnum.SILENT;
                }
                break;
            case 2:
                //活跃  下一阶段是沉默
                Long nextStageDistanceDay2 = getNextStageDistanceDay(startDate, branchLifecycleSetting.convertActiveDays());
                if(nextStageDistanceDay2 == null){
                    return null;
                }else{
                    day = nextStageDistanceDay2.intValue();
                    branchLifecycleTypeEnum = BranchLifecycleTypeEnum.SILENT;
                }
                break;
            case 3:
                //沉默  下一阶段是流失
                Long nextStageDistanceDay3 = getNextStageDistanceDay(startDate, branchLifecycleSetting.convertSilentDays());
                if(nextStageDistanceDay3 == null){
                    return null;
                }else{
                    day = nextStageDistanceDay3.intValue();
                    branchLifecycleTypeEnum = BranchLifecycleTypeEnum.LOST;
                }
                break;
            case 4:
                //流失  下一阶段是进入公海
                Long nextStageDistanceDay4 = getNextStageDistanceDay(startDate, branchLifecycleSetting.convertLostProtectionPeriodDays());
                if(nextStageDistanceDay4 == null){
                    return null;
                }else{
                    day = nextStageDistanceDay4.intValue();
                    branchLifecycleTypeEnum = BranchLifecycleTypeEnum.PUBLIC_SEA;
                }
                break;
            default:
                return null;
        }

        //说明已经流失 并且进入了公海
        if(branchLifecycleTypeEnum == BranchLifecycleTypeEnum.PUBLIC_SEA && day < 0){
            return "已进入公海";
        }else {
            //如果是负数 则默认为0天
            day = Math.max(0, day);
            return String.format("预计%s天后进入%s",day, branchLifecycleTypeEnum.getName());
        }

    }

    @Override
    public BranchLifecycleSettingPolicyDTO getBranchLifecycleSetting(Long branchId) {
        //获取门店的区域
        BranchDTO branchDto = memberCacheService.getBranchDto(branchId);

        if(ToolUtil.isEmpty(branchDto) || ToolUtil.isEmpty(branchDto.getAreaId())){
            return null;
        }

        //区域信息
        AreaDTO areaDto = memberCacheService.getAreaDto(branchDto.getAreaId());

        if(ToolUtil.isEmpty(areaDto) || ToolUtil.isEmpty(areaDto.getDcId())){
            return null;
        }

        //获取运营商 生命周期配置
        BranchLifecycleSettingPolicyDTO branchLifecycleSettingPolicyDTO = memberCacheService.getBranchLifecycleSettingPolicyDTO(areaDto.getDcId());
        if(ToolUtil.isEmpty(branchLifecycleSettingPolicyDTO)
                || branchLifecycleSettingPolicyDTO.isNullByBranchLifecycle()){
            return null;
        }

        return branchLifecycleSettingPolicyDTO;
    }

    @Override
    public List<MemBranchLifecycleZipRespVO> getBranchLifecycleStageList(Long branchId) {
        List<MemBranchLifecycleZipRespVO> branchLifecycleZipist = MemBranchLifecycleZipConvert.INSTANCE.convertList(memBranchLifecycleZipMapper.getAllLifecycleStageListByBranchIdAndDate(branchId, DateUtils.getDateScopeByYear(-1)));
        //组装数据
        for (MemBranchLifecycleZipRespVO branchLifecycle : branchLifecycleZipist) {
            branchLifecycle.parseStartMemoData();
        }

        return branchLifecycleZipist;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshBranchLifecycleZipHandler(Long sysCode) {

        //获取该平台商下所有门店上一次的门店生命周期拉链信息
        List<MemBranchLifecycleZip> memBranchLifecycleZipList = memBranchLifecycleZipMapper.getAllNewLifecycleStageBySysCode(sysCode,null );

        //根据门店ID 组装
        Map<Long, MemBranchLifecycleZip> branchLifecycleZipMap = new HashMap<>();

        if(ToolUtil.isNotEmpty(memBranchLifecycleZipList)){
            branchLifecycleZipMap = memBranchLifecycleZipList.stream().collect(Collectors.toMap(MemBranchLifecycleZip::getBranchId, Function.identity(),(n1,n2) -> n1));
        }

        // 生命周期配置  城市 -  配置
        Map<Long,BranchLifecycleSettingPolicyDTO> branchLifecycleSettingMap = new HashMap<>();
        //未配置生命周期配置的区域城市
        Set<Long> areaNotSetting = new HashSet<>();

        //需要更新的门店信息
        List<MemBranch> updateBranchList = new ArrayList<>();
        //需要更新或新增的门店周期拉链信息
        List<MemBranchLifecycleZip> updateLifecycleZipList =new ArrayList<>();

        //根据sysCode 查询平台商下的所有门店信息
        List<MemBranch> memBranchList = memBranchMapper.selectAllBranchBySysCode(sysCode);
        if(ToolUtil.isNotEmpty(memBranchList)){
            //获取各个门店最新的订单信息
            List<TrdOrder> latestOrderList = orderApi.getBranchLatestOrderByBranchIdList(sysCode).getCheckedData();

            Map<Long, TrdOrder> latestOrderMap = latestOrderList.stream().collect(Collectors.toMap(TrdOrder::getBranchId, Function.identity(),(n1,n2) -> n1));

            //根据门店ID集合查询各个门店下最近的一次下单记录信息
            for (MemBranch memBranch : memBranchList) {
                   //1、获取该门店的生命周期配置
                   if(ToolUtil.isEmpty(memBranch.getAreaId()) || areaNotSetting.contains(memBranch.getAreaId())){
                       //没有区域ID 或 区域没有设置运营商或没有配置生命周期配置  直接跳过
                       continue;
                   }

                   BranchLifecycleSettingPolicyDTO branchLifecycleSetting = branchLifecycleSettingMap.get(memBranch.getAreaId());
                   if(ToolUtil.isEmpty(branchLifecycleSetting)){
                       branchLifecycleSetting = getBranchLifecycleSetting(memBranch.getBranchId());
                       //如果还是没有获取到配置 说明该区域没有设置运营商或没有配置生命周期配置
                       if(ToolUtil.isEmpty(branchLifecycleSetting)){
                           areaNotSetting.add(memBranch.getAreaId());
                           continue;
                       }
                   }

                   branchLifecycleSettingMap.put(memBranch.getAreaId(),branchLifecycleSetting);

                   // 2、获取该门店最近的下单信息
                   TrdOrder trdOrder = latestOrderMap.get(memBranch.getBranchId());

                   //3、获取该门店的上一次的生命周期信息
                   MemBranchLifecycleZip lastBranchLifecycleZip = branchLifecycleZipMap.get(memBranch.getBranchId());

                   //4、获取该门店的最新的生命周期信息

                   //如果没有上一次生命周期 说明是新店
                   if(ToolUtil.isEmpty(lastBranchLifecycleZip)){
                       //新店信息
                       MemBranchLifecycleZip newCustomerBranchLifecycleZip = assembleMemBranchLifecycleZip(memBranch,trdOrder,branchLifecycleSetting,null,BranchLifecycleTypeEnum.NEW_CUSTOMER.getType());

                       //添加到更新生命周期信息记录中
                       updateLifecycleZipList.add(newCustomerBranchLifecycleZip);
                       memBranch.setLifecycleStage(BranchLifecycleTypeEnum.NEW_CUSTOMER.getType());

                       //说明是新店并且下单
                       if(ToolUtil.isNotEmpty(trdOrder)){
                           //将新店周期的结束时间设置为当前时间 作为结束
                           newCustomerBranchLifecycleZip.setEndDate(DateUtils.getNowDate());

                           //活跃信息
                           MemBranchLifecycleZip activeBranchLifecycleZip = assembleMemBranchLifecycleZip(memBranch,trdOrder,branchLifecycleSetting,BranchLifecycleTypeEnum.NEW_CUSTOMER.getType(),BranchLifecycleTypeEnum.ACTIVE.getType());
                           //添加到更新生命周期信息记录中
                           updateLifecycleZipList.add(activeBranchLifecycleZip);
                           memBranch.setLifecycleStage(BranchLifecycleTypeEnum.ACTIVE.getType());
                       }
                       updateBranchList.add(memBranch);

                   }else{
                       MemBranchLifecycleZip memBranchLifecycleZipNew = getNewMemBranchLifecycleZip(memBranch,trdOrder,branchLifecycleSetting,lastBranchLifecycleZip);

                       if(ToolUtil.isNotEmpty(memBranchLifecycleZipNew)){
                           //添加到更新生命周期信息记录中
                           updateLifecycleZipList.add(memBranchLifecycleZipNew);
                           memBranch.setLifecycleStage(memBranchLifecycleZipNew.getLifecycleStage());
                           updateBranchList.add(memBranch);

                           //上一个生命周期阶段结束
                           lastBranchLifecycleZip.setEndDate(DateUtils.getNowDate());
                           updateLifecycleZipList.add(lastBranchLifecycleZip);

                           //查看生命周期配置   是否禁用登录账号
                           if(Objects.equals(branchLifecycleSetting.convertIsStopLogin(),FLAG_TRUE)
                                   && Objects.equals(memBranchLifecycleZipNew.getLifecycleStage(),BranchLifecycleTypeEnum.LOST.getType())){
                               //禁用登录账号
                               memBranch.setStatus(STATE_DISABLE);
                           }
                       }
                   }

            }

            //存在门店信息变动
            if(ToolUtil.isNotEmpty(updateBranchList)){
                memBranchMapper.updateBatch(updateBranchList);

                //更新门店缓存
                branchDTOCache.removeAll(updateBranchList.stream().map(MemBranch::getBranchId).collect(Collectors.toSet()));
            }

            //存在生命周期变动
            if(ToolUtil.isNotEmpty(updateLifecycleZipList)){
                memBranchLifecycleZipMapper.insertOrUpdateBatch(updateLifecycleZipList);
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Set<Long> branchLifecycleBatchNew(List<Long> branchIdList) {
        //需要翻新/修改的门店生命周期信息
        List<MemBranchLifecycleZip> newBranchLifecycleZipList = new ArrayList<>();

        //需要翻新的门店信息
        Set<Long> newBranchIdSet = new HashSet<>();

        //查询门店信息
        List<MemBranch> memBranchList = memBranchMapper.selectBatchIds(branchIdList);

        //筛选出门店状态为 沉默、流失阶段的门店信息
        List<MemBranch> newBranchList = memBranchList.stream().filter(x -> Objects.equals(x.getLifecycleStage(), BranchLifecycleTypeEnum.SILENT.getType())
                || Objects.equals(x.getLifecycleStage(), BranchLifecycleTypeEnum.LOST.getType())).collect(Collectors.toList());

        //如果没有需要处理的门店信息
        if(ToolUtil.isEmpty(newBranchList)){
            return newBranchIdSet;
        }

        //查询这些门店当前的生命周期信息
        List<MemBranchLifecycleZip> lastLifecycleStageList = memBranchLifecycleZipMapper.getAllNewLifecycleStageBySysCode(memBranchList.get(0).getSysCode(), branchIdList);

        // 生命周期配置  城市 -  配置
        Map<Long,BranchLifecycleSettingPolicyDTO> branchLifecycleSettingMap = new HashMap<>();
        //未配置生命周期配置的区域城市
        Set<Long> areaNotSetting = new HashSet<>();

        //根据门店ID 组装
        Map<Long, MemBranchLifecycleZip> branchLifecycleZipMap = new HashMap<>();

        if(ToolUtil.isNotEmpty(lastLifecycleStageList)){
            branchLifecycleZipMap = lastLifecycleStageList.stream().collect(Collectors.toMap(MemBranchLifecycleZip::getBranchId, Function.identity(),(n1,n2) -> n1));
        }

        for (MemBranch memBranch : newBranchList) {
            //匹配运营商生命周期配置
            //获取该门店的生命周期配置
            if(ToolUtil.isEmpty(memBranch.getAreaId()) || areaNotSetting.contains(memBranch.getAreaId())){
                //没有区域ID 或 区域没有设置运营商或没有配置生命周期配置  直接跳过
                continue;
            }

            BranchLifecycleSettingPolicyDTO branchLifecycleSetting = branchLifecycleSettingMap.get(memBranch.getAreaId());
            if(ToolUtil.isEmpty(branchLifecycleSetting)){
                branchLifecycleSetting = getBranchLifecycleSetting(memBranch.getBranchId());
                //如果还是没有获取到配置 说明该区域没有设置运营商或没有配置生命周期配置
                if(ToolUtil.isEmpty(branchLifecycleSetting)){
                    areaNotSetting.add(memBranch.getAreaId());
                    continue;
                }
            }

            branchLifecycleSettingMap.put(memBranch.getAreaId(),branchLifecycleSetting);


            MemBranchLifecycleZip lastBranchLifecycleZip = branchLifecycleZipMap.get(memBranch.getBranchId());
            Integer lastLifecycleStage = null;

            //修改上一个生命周期信息
            if(ToolUtil.isNotEmpty(lastBranchLifecycleZip)){
                lastLifecycleStage = lastBranchLifecycleZip.getLifecycleStage();
                lastBranchLifecycleZip.setEndDate(DateUtils.getNowDate());
                newBranchLifecycleZipList.add(lastBranchLifecycleZip);
            }

            //翻新的门店生命周期信息
            newBranchLifecycleZipList.add(assembleMemBranchLifecycleZip(memBranch,null,branchLifecycleSetting,lastLifecycleStage,BranchLifecycleTypeEnum.NEW_CUSTOMER.getType()));
            newBranchIdSet.add(memBranch.getBranchId());
        }

        //插入/修改新的门店生命周期信息
        if(ToolUtil.isNotEmpty(newBranchLifecycleZipList)){
            memBranchLifecycleZipMapper.insertOrUpdateBatch(newBranchLifecycleZipList);
        }

        //修改门店的生命周期属性
        if(ToolUtil.isNotEmpty(newBranchIdSet)){
            memBranchMapper.updateBatchBranchLifecycle(newBranchIdSet);
        }

        return newBranchIdSet;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBranchLifecycle(Long branchId,Integer lifecycleStage,Long orderId,Long afterId) {
        //获取该门店的生命周期配置
        BranchLifecycleSettingPolicyDTO branchLifecycleSetting = getBranchLifecycleSetting(branchId);
        //如果不存在该门店所属运营商的生命周期配置  则不更新
        if(ToolUtil.isNotEmpty(branchLifecycleSetting)){
            //门店信息
            MemBranch memBranch = memBranchMapper.selectById(branchId);

            //订单信息
            TrdOrder order = new TrdOrder();
            if(ToolUtil.isNotEmpty(orderId)){
                order = orderApi.getOrderByOrderId(orderId);
            }

            //需要更新的门店生命周期信息
            List<MemBranchLifecycleZip> updateLifecycleZipList = new ArrayList<>();

            //处理生命周期
            switch (lifecycleStage){
                case BRANCH_LIFECYCLE_OPERATION_TYPE_1:
                    //门店注册
                    //新增新客生命周期信息
                    updateLifecycleZipList.add(assembleMemBranchLifecycleZip(memBranch,order,branchLifecycleSetting,null,BranchLifecycleTypeEnum.NEW_CUSTOMER.getType()));
                    //修改该门店的生命周期信息
                    memBranch.setLifecycleStage(BranchLifecycleTypeEnum.NEW_CUSTOMER.getType());
                    break;
                case BRANCH_LIFECYCLE_OPERATION_TYPE_2:
                    //订单下单
                    //查询门店当前的生命周期信息
                    MemBranchLifecycleZip lastBranchLifecycleZip = memBranchLifecycleZipMapper.getLatestBranchLifecycleZip(branchId);
                    //如果已经是活跃 则不处理 不是活跃则新增活跃生命周期信息 及更新原生命周期信息
                    if(ToolUtil.isNotEmpty(lastBranchLifecycleZip) && !Objects.equals(lastBranchLifecycleZip.getLifecycleStage(),BranchLifecycleTypeEnum.ACTIVE.getType())){
                        //新增活跃生命周期
                        updateLifecycleZipList.add(assembleMemBranchLifecycleZip(memBranch,order,branchLifecycleSetting,lastBranchLifecycleZip.getLifecycleStage(),BranchLifecycleTypeEnum.ACTIVE.getType()));
                        //更新原生命周期
                        lastBranchLifecycleZip.setEndDate(DateUtils.getNowDate());
                        updateLifecycleZipList.add(lastBranchLifecycleZip);

                        //修改该门店的生命周期信息
                        memBranch.setLifecycleStage(BranchLifecycleTypeEnum.ACTIVE.getType());
                    }
                    break;
                case BRANCH_LIFECYCLE_OPERATION_TYPE_3:
                    //订单取消
                    //查询门店当前的生命周期信息
                    MemBranchLifecycleZip lastBranchLifecycleZip2 = memBranchLifecycleZipMapper.getLatestBranchLifecycleZip(branchId);

                    //如果当前门店生命周期是活跃 并且事件触发信息中的订单号是该取消的订单号 则需要将该生命周期信息修改为失效 并还原当前的生命周期
                    if(ToolUtil.isNotEmpty(lastBranchLifecycleZip2) && Objects.equals(lastBranchLifecycleZip2.getLifecycleStage(),BranchLifecycleTypeEnum.ACTIVE.getType())){
                        //获取事件触发信息
                        BranchLifecycleStageStartMemoDataDTO stageStartMemoData = JSONObject.parseObject(lastBranchLifecycleZip2.getStartMemo(),BranchLifecycleStageStartMemoDataDTO.class);
                        if(ToolUtil.isNotEmpty(stageStartMemoData) && Objects.equals(stageStartMemoData.getOrderNo(),order.getOrderNo())){
                            //修改该生命周期信息 并还原当前的生命周期
                            lastBranchLifecycleZip2.setEndDate(DateUtils.getNowDate());
                            updateLifecycleZipList.add(lastBranchLifecycleZip2);
                            //获取该门店原生命周期
                            MemBranchLifecycleZip historyBranchLifecycle = memBranchLifecycleZipMapper.getLatestBranchLifecycleZipByNotId(branchId, lastBranchLifecycleZip2.getBranchLifecycleZipId());

                            //获取售后单信息
                            TrdAfter after = afterApi.getAfterById(afterId).getCheckedData();

                            //组装数据
                            MemBranchLifecycleZip newBranchLifecycleZip = new MemBranchLifecycleZip();
                            newBranchLifecycleZip.setSysCode(historyBranchLifecycle.getSysCode());
                            newBranchLifecycleZip.setBranchId(historyBranchLifecycle.getBranchId());
                            newBranchLifecycleZip.setLastLifecycleStage(lastBranchLifecycleZip2.getLifecycleStage());
                            newBranchLifecycleZip.setLifecycleStage(historyBranchLifecycle.getLifecycleStage());
                            newBranchLifecycleZip.setStartDate(DateUtils.getNowDate());
                            newBranchLifecycleZip.setEndDate(DateUtils.dateTime(YYYY_MM_DD_HH_MM_SS,DATETIME_MAX));
                            //组装触发事件信息
                            BranchLifecycleStageStartMemoDataDTO startMemoData = new BranchLifecycleStageStartMemoDataDTO();
                            if(ToolUtil.isNotEmpty(memBranch.getColonelId()) && ToolUtil.isNotEmpty(memberCacheService.getColonel(memBranch.getColonelId()))){
                                startMemoData.setColonelName(memberCacheService.getColonel(memBranch.getColonelId()).getColonelName());
                            }
                            startMemoData.setOrderNo(order.getOrderNo());

                            //设置订单售后取消信息
                            startMemoData.setIsOrderCancel(true);
                            startMemoData.setEvent("售后取消。售后订单号：" + after.getAfterNo());
                            startMemoData.setAmt(after.getRefundAmt());
                            startMemoData.setOrderDate(after.getCreateTime());

                            newBranchLifecycleZip.setStartMemo(JSONObject.toJSONString(startMemoData));
                            updateLifecycleZipList.add(newBranchLifecycleZip);
                            memBranch.setLifecycleStage(newBranchLifecycleZip.getLifecycleStage());

                        }
                    }
                    break;
                default:
                    break;
            }

            //更新生命周期信息
            if(!updateLifecycleZipList.isEmpty()){
                memBranchLifecycleZipMapper.insertOrUpdateBatch(updateLifecycleZipList);
                memBranchMapper.updateById(memBranch);
                //清除缓存
                branchDTOCache.remove(branchId);
            }
        }
    }

    private void validateMemBranchLifecycleZipExists(Long branchLifecycleZipId) {
        if (memBranchLifecycleZipMapper.selectById(branchLifecycleZipId) == null) {
            throw exception(MEM_BRANCH_LIFECYCLE_ZIP_NOT_EXISTS);
        }
    }

    /**
     * 获取距离下一阶段周期的天数
     * @param startDate
     * @param nextStageDay
     * @return
     */
    public Long getNextStageDistanceDay(Date startDate, Integer nextStageDay){
        //如果开始时间为空 或者下一阶段天数为空
        if(ToolUtil.isEmpty(startDate) || ToolUtil.isEmpty(nextStageDay)){
            return null;
        }

        return DateUtils.getRemain(DateUtils.getNowDate(), DateUtils.getDateAddByDate(startDate,nextStageDay), ChronoUnit.DAYS);

    }

    /**
     * 组装门店生命周期信息
     * @param memBranch
     * @param branchLifecycleSetting
     * @param lastLifecycleStage
     * @param lifecycleStage
     * @return
     */
    public MemBranchLifecycleZip assembleMemBranchLifecycleZip(MemBranch memBranch,TrdOrder trdOrder,BranchLifecycleSettingPolicyDTO branchLifecycleSetting,Integer lastLifecycleStage,Integer lifecycleStage){
        //组装门店生命周期信息
        MemBranchLifecycleZip memBranchLifecycleZip = new MemBranchLifecycleZip();
        memBranchLifecycleZip.setSysCode(memBranch.getSysCode());
        memBranchLifecycleZip.setBranchId(memBranch.getBranchId());
        memBranchLifecycleZip.setLastLifecycleStage(lastLifecycleStage);
        memBranchLifecycleZip.setLifecycleStage(lifecycleStage);
        memBranchLifecycleZip.setStartDate(DateUtils.getNowDate());
        memBranchLifecycleZip.setEndDate(DateUtils.dateTime(YYYY_MM_DD_HH_MM_SS,DATETIME_MAX));


        //组装触发事件信息
        BranchLifecycleStageStartMemoDataDTO startMemoData = new BranchLifecycleStageStartMemoDataDTO();
        if(ToolUtil.isNotEmpty(memBranch.getColonelId()) && ToolUtil.isNotEmpty(memberCacheService.getColonel(memBranch.getColonelId()))){
            startMemoData.setColonelName(memberCacheService.getColonel(memBranch.getColonelId()).getColonelName());
        }

        if(Objects.equals(lifecycleStage,BranchLifecycleTypeEnum.NEW_CUSTOMER.getType())){
            if(ToolUtil.isNotEmpty(lastLifecycleStage)){
                startMemoData.setEvent("门店翻新");
                startMemoData.setEventDate(DateUtils.getNowDate());
            }else{
                startMemoData.setEvent("注册");
                //获取注册时间
                startMemoData.setEventDate(memBranchMapper.getBranchRegisterTime(memBranch.getBranchId()));
            }
        }else{
            if(Objects.equals(lifecycleStage,BranchLifecycleTypeEnum.ACTIVE.getType())){
                startMemoData.setEvent("下单");
            }else if(Objects.equals(lifecycleStage,BranchLifecycleTypeEnum.SILENT.getType())){
                startMemoData.setEvent( branchLifecycleSetting.getActiveDays() + "天未下单");
            }else if (Objects.equals(lifecycleStage,BranchLifecycleTypeEnum.LOST.getType())){
                startMemoData.setEvent( branchLifecycleSetting.getSilentDays() + "天未下单");
            }
            startMemoData.setEventDate(DateUtils.getNowDate());

            //新店无需匹配订单信息 所以排除新店
            if(ToolUtil.isNotEmpty(trdOrder)){
                startMemoData.setAmt(trdOrder.getOrderAmt());
                startMemoData.setOrderDate(trdOrder.getCreateTime());
                startMemoData.setOrderNo(trdOrder.getOrderNo());
            }

        }

        memBranchLifecycleZip.setStartMemo(JSON.toJSONString(startMemoData));

        return memBranchLifecycleZip;

    }


    /**
     * 获取该门店的最新的生命周期信息
     * @param memBranch
     * @param trdOrder
     * @param branchLifecycleSetting
     * @param lastBranchLifecycleZip
     * @return
     */
    public MemBranchLifecycleZip getNewMemBranchLifecycleZip(MemBranch memBranch, TrdOrder trdOrder,BranchLifecycleSettingPolicyDTO branchLifecycleSetting,MemBranchLifecycleZip lastBranchLifecycleZip){
        //门店上一个生命周期阶段
        Integer lastLifecycleStage = lastBranchLifecycleZip.getLifecycleStage();

        //最新生命周期阶段
        Integer lifecycleStage = null;
        //匹配该门店当前的生命周期阶段
        //门店注册时间
        Date branchRegisterTime = memBranchMapper.getBranchRegisterTime(memBranch.getBranchId());
        //当前最新下单时间
        Date orderDate = ToolUtil.isNotEmpty(trdOrder) ? trdOrder.getCreateTime() : branchRegisterTime;


        // 上次下单距离当前时间的天数
        long distanceOrderDaysLong = DateUtils.getRemain(orderDate, DateUtils.getNowDate(), ChronoUnit.DAYS);

        Integer distanceOrderDays = (int) distanceOrderDaysLong;

        //流失
        if(distanceOrderDays > branchLifecycleSetting.convertSilentDays()){
            lifecycleStage = BranchLifecycleTypeEnum.LOST.getType();
        }else if(distanceOrderDays > branchLifecycleSetting.convertActiveDays()){
        //沉默
            lifecycleStage = BranchLifecycleTypeEnum.SILENT.getType();
        }else if(distanceOrderDays <= branchLifecycleSetting.convertNewCustomerDays()
                || distanceOrderDays <= branchLifecycleSetting.convertActiveDays()){
            //新客
            //比较两个时间 是否为同一时刻
            if(DateUtils.isSameInstant(branchRegisterTime,orderDate)){
                lifecycleStage = BranchLifecycleTypeEnum.NEW_CUSTOMER.getType();
            }else{
                //活跃
                lifecycleStage = BranchLifecycleTypeEnum.ACTIVE.getType();
            }
        }

        //如果该门店的最新生命周期阶段 与上一个生命周期阶段一致  则不更新
        if(lifecycleStage == null || Objects.equals(lifecycleStage,lastLifecycleStage)){
            return null;
        }

        return assembleMemBranchLifecycleZip(memBranch,trdOrder,branchLifecycleSetting,lastLifecycleStage,lifecycleStage);
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.member.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 门店生命周期拉链 TODO 补充编号 ==========
    // ErrorCode MEM_BRANCH_LIFECYCLE_ZIP_NOT_EXISTS = new ErrorCode(TODO 补充编号, "门店生命周期拉链不存在");
}
