package com.zksr.member.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 用户注册信息对象 mem_member_register
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@TableName(value = "mem_member_register")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemMemberRegister extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 用户注册信息ID */
    @TableId
    private Long memberRegisterId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 用户名 */
    @Excel(name = "用户名")
    private String memberName;

    /**
     * 门店编码
     */
    @Excel(name = "门店编码")
    private String branchNo;

    /** 门店名称 */
    @Excel(name = "门店名称")
    private String branchName;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 门店地址 */
    @Excel(name = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 用户账号 */
    @Excel(name = "用户账号")
    private String userName;

    /** 用户密码 */
    @Excel(name = "用户密码")
    private String password;

    /** 门头照 */
    @Excel(name = "门头照")
    private String branchImages;

    /** 审核人 */
    @Excel(name = "审核人")
    private Long approveMan;

    /** 审核标识 */
    @Excel(name = "审核标识")
    private Integer approveFlag;

    /** 审核时间 */
    @Excel(name = "审核时间")
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveDate;

    /** 用户自动审核标识 */
    @Excel(name = "用户自动审核标识")
    private Integer memberApproveFlag;

    /** 用户过期时间;过了过期时间，则停用 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "用户过期时间;过了过期时间，则停用", width = 30, dateFormat = "yyyy-MM-dd")
    private Date memberExpirationDate;

    /** 门店自动审核标识 */
    @Excel(name = "门店自动审核标识")
    private Integer branchApproveFlag;

    /** 门店过期时间;过了过期时间，则停用 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "门店过期时间;过了过期时间，则停用", width = 30, dateFormat = "yyyy-MM-dd")
    private Date branchExpirationDate;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    @ApiModelProperty("三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    private Long threeAreaCityId;

    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    @Excel(name = "渠道ID")
    private Long channelId;

    @ApiModelProperty(value = "门店Id")
    private Long branchId;

    /** 省份 */
    @ApiModelProperty(value = "省份")
    private String provinceName;

    /** 城市 */
    @ApiModelProperty(value = "城市")
    private String cityName;

    /** 区县 */
    @ApiModelProperty(value = "区县")
    private String districtName;


    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;

    @Excel(name = "是否支持货到付款", readConverterExp = "0-不支持,1-支持")
    @ApiModelProperty(value = "是否支持货到付款(0,否 1,是)")
    private Integer hdfkSupport;

    @Excel(name = "货到付款最大可欠款金额")
    @ApiModelProperty("货到付款最大可欠款金额")
    private BigDecimal hdfkMaxAmt;

    @ApiModelProperty("是否支持在线收款 0：否，1：是")
    private Integer isPayOnline;

}
