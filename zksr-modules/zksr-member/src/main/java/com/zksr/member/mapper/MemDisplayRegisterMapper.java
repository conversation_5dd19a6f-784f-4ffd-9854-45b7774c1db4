package com.zksr.member.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.domain.MemDisplayRegister;
import com.zksr.member.controller.displayRegister.vo.MemDisplayRegisterPageReqVO;


/**
 * 陈列计划打卡记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Mapper
public interface MemDisplayRegisterMapper extends BaseMapperX<MemDisplayRegister> {
    default PageResult<MemDisplayRegister> selectPage(MemDisplayRegisterPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemDisplayRegister>()
                    .eqIfPresent(MemDisplayRegister::getRegisterId, reqVO.getRegisterId())
                    .eqIfPresent(MemDisplayRegister::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(MemDisplayRegister::getPlanId, reqVO.getPlanId())
                    .eqIfPresent(MemDisplayRegister::getPic, reqVO.getPic())
                    .eqIfPresent(MemDisplayRegister::getMemo, reqVO.getMemo())
                    .eqIfPresent(MemDisplayRegister::getDelFlag, reqVO.getDelFlag())
                .orderByDesc(MemDisplayRegister::getRegisterId));
    }
}
