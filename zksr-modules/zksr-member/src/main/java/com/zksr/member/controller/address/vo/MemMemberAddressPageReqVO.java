package com.zksr.member.controller.address.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 用户地址对象 mem_member_address
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@ApiModel("用户地址 - mem_member_address分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemMemberAddressPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** ID主键 */
    @ApiModelProperty(value = "版本号")
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty(value = "用户id", required = true)
    private Long memberId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId) */
    @Excel(name = "三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    @ApiModelProperty(value = "三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    private Long threeAreaCityId;

    /** 省份 */
    @Excel(name = "省份")
    @ApiModelProperty(value = "省份")
    private String provinceName;

    /** 城市 */
    @Excel(name = "城市")
    @ApiModelProperty(value = "城市")
    private String cityName;

    /** 区县 */
    @Excel(name = "区县")
    @ApiModelProperty(value = "区县")
    private String districtName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人", required = true)
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话", required = true)
    private String contactPhone;

    /** 详细收货地址 */
    @Excel(name = "详细收货地址")
    @ApiModelProperty(value = "详细收货地址", required = true)
    private String deliveryAddress;

    /** 是否删除：0-否，1-是 */
    @ApiModelProperty(value = "详细收货地址", required = true)
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号", required = true)
    private Long version;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", required = true)
    private String remark;

}
