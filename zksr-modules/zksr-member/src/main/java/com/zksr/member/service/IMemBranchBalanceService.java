package com.zksr.member.service;

import com.zksr.account.api.balance.dto.MemBranchBalanceFlowDTO;
import com.zksr.account.api.balance.vo.AccBalanceFlowRespVO;
import com.zksr.account.api.balance.vo.MemBranchBalanceRespVO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.controller.branch.balance.dto.MemBranchBalancePageReqDTO;

import javax.validation.Valid;

/**
 * 门店余额管理service接口
 */
public interface IMemBranchBalanceService {

    // 分页查询当前门店余额
    PageResult<MemBranchBalanceRespVO> getMemBranchBalancePage(MemBranchBalancePageReqDTO pageReqDTO);

    // 充值或退款查询明细
    MemBranchBalanceRespVO getMemBranchBalanceDetailInfo(Long branchId, Integer operType);

    // 充值操作
    String recharge(MemBranchBalancePageReqDTO pageReqDTO);

    // 退款操作
    String refund(@Valid MemBranchBalancePageReqDTO pageReqDTO);

    // 分页查询获取门店余额流水
    CommonResult<PageResult<AccBalanceFlowRespVO>> getFlowPageList(@Valid MemBranchBalanceFlowDTO pageReqDTO);
}
