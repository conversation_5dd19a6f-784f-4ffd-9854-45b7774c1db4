package com.zksr.member.controller.ColonelTidy.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 业务员理货记录对象 mem_colonel_tidy
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Data
@ApiModel("业务员理货记录 - mem_colonel_tidy Response VO")
public class MemColonelTidyRespVO {
    private static final long serialVersionUID = 1L;

    /** 业务员理货记录id */
    @ApiModelProperty(value = "理货说明")
    private String colonelTidyId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    private String colonelId;

    /** 门店ID */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    private String branchId;

    /** 理货图片 */
    @Excel(name = "理货图片")
    @ApiModelProperty(value = "理货图片")
    private String tidyImg;

    /** 理货说明 */
    @Excel(name = "理货说明")
    @ApiModelProperty(value = "理货说明")
    private String tidyDescr;

    /** 理货说明 */
    @Excel(name = "理货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "理货时间")
    private Date createTime;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 部门ID */
    @Excel(name = "部门ID")
    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    /** 业务员名称 */
    @Excel(name = "业务员名称")
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

}
