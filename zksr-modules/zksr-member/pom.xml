<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zksr</groupId>
        <artifactId>zksr-modules</artifactId>
        <version>3.6.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zksr-modules-member</artifactId>

    <description>
        zksr-modules-member会员模块
    </description>

    <dependencies>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- Zksr-Mall Common DataSource -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-datasource</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Database -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-mp</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Log -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-log</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Swagger -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-swagger</artifactId>
        </dependency>

        <!-- 文件|导出| 接口 -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-file</artifactId>
        </dependency>

        <!-- 报表数据 -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-report</artifactId>
        </dependency>

        <!-- 系统解耦 -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-system</artifactId>
        </dependency>

        <!-- 消息中间件 -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- elasticsearch -->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.14.0</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>7.14.0</version>
        </dependency>

        <!-- 单元测试 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
