package com.zksr.trade.service;

import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.ZksrMallTradeApplication;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.TrdOrderMiniHeadRespDTO;
import com.zksr.trade.api.order.dto.TrdOrderMiniRespDTO;
import com.zksr.trade.api.order.dto.TrdOrderPageReqDTO;
import com.zksr.trade.api.order.dto.TrdOrderRespDTO;
import com.zksr.trade.api.order.vo.DcOrderPageReqApiVO;
import com.zksr.trade.api.order.vo.SupplierOrderDtlInfoExportVO;
import com.zksr.trade.api.order.vo.SupplierOrderExportVO;
import com.zksr.trade.controller.order.vo.DcOrderPageReqVO;
import com.zksr.trade.controller.order.vo.DcSupplierOrderPageRespVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: chenyj8
 * @Desciption: 类功能描述
 */
@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallTradeApplication.class)
public class TrdOrderServiceTest {

    @Resource
    private ITrdOrderService trdOrderService;

    @Resource
    private OrderApi orderApi;

    @BeforeEach
    void setUp() {

    }

    /**
     * PC 订单列表查询
     */
//    @Test
//    public void testgetOperatorOrderPageListNew(){
//        DcOrderPageReqVO dcOrderPageReqVO = new DcOrderPageReqVO();
////        dcOrderPageReqVO.setPushStatus(0);
//        dcOrderPageReqVO.setPageNo(1);
//        dcOrderPageReqVO.setPageSize(10);
//        dcOrderPageReqVO.setOrderType(100);
//
//        PageResult<DcSupplierOrderPageRespVO> result = trdOrderService.getOperatorOrderPageListNew(dcOrderPageReqVO);
//        System.out.println(JsonUtils.toJsonString(result));
//    }


    /**
     * PC 订单列表查询
     */
    @Test
    public void testGetOperatorOrderPageListNew(){
        DcOrderPageReqVO dcOrderPageReqVO = new DcOrderPageReqVO();
//        dcOrderPageReqVO.setPushStatus(0);
        dcOrderPageReqVO.setPageNo(1);
        dcOrderPageReqVO.setPageSize(10);
        dcOrderPageReqVO.setOrderType(100);
        String startTimeString = "2025-06-05 00:00:00";
        String endTimeString = "2025-07-05 23:59:59";
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = null;
        Date endTime = null;
        try {
            startTime = formatter.parse(startTimeString);
            endTime = formatter.parse(endTimeString);
            System.out.println("Date object: " + startTime);
            System.out.println("Date object: " + endTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        dcOrderPageReqVO.setStartTime(startTime);
        dcOrderPageReqVO.setEndTime(endTime);

        PageResult<DcSupplierOrderPageRespVO> result = trdOrderService.getOperatorOrderPageListNew(dcOrderPageReqVO);
        System.out.println(JsonUtils.toJsonString(result));
    }

    /**
     * 订单导出-商品维度
     */
    @Test
    public void testGetSupplierOrderDtlInfoExport(){
        DcOrderPageReqApiVO dcOrderPageReqVO = new DcOrderPageReqApiVO();
        dcOrderPageReqVO.setOrderNo("XS250624890075044124");
        dcOrderPageReqVO.setPageNo(0);
//        DcOrderPageReqVO param = new DcOrderPageReqVO();
//        param.setOrderNo("XS240331510000059688");
        String startTimeString = "2025-06-05 00:00:00";
        String endTimeString = "2025-07-05 23:59:59";
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = null;
        Date endTime = null;
        try {
            startTime = formatter.parse(startTimeString);
            endTime = formatter.parse(endTimeString);
            System.out.println("Date object: " + startTime);
            System.out.println("Date object: " + endTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        dcOrderPageReqVO.setStartTime(startTime);
        dcOrderPageReqVO.setEndTime(endTime);

        List<SupplierOrderDtlInfoExportVO> result = trdOrderService.getSupplierOrderDtlInfoExport(dcOrderPageReqVO);

        System.out.println(JsonUtils.toJsonString(result));

    }

    /**
     * 订单导出-订单维度
     */
    @Test
    public void testSelectSupplierOrderExport(){
        DcOrderPageReqVO param = new DcOrderPageReqVO();
        param.setOrderNo("XS240331510000059688");
        List<Integer> deliveryStates = new ArrayList<>();
        deliveryStates.add(0);
        deliveryStates.add(10);
        deliveryStates.add(20);
        deliveryStates.add(21);
        param.setDeliveryStates(deliveryStates);
        List<SupplierOrderExportVO> result = trdOrderService.selectSupplierOrderExport(param);

        System.out.println(JsonUtils.toJsonString(result));

    }

    @Test
    public void testOrderCancel(){
        String supplierOrderNo = "XSS250620160001300384";
        System.out.println("testOrderCancel supplierOrderNo: " + supplierOrderNo);
        trdOrderService.orderCancel(supplierOrderNo);

    }

    @Test
    public void testPageOrderList(){
        TrdOrderPageReqDTO orderPageReqVO = new TrdOrderPageReqDTO();
        orderPageReqVO.setDeliveryState(21);
        orderPageReqVO.setPageNo(1);
        orderPageReqVO.setPageSize(10);
//        orderPageReqVO.setOrderType(0);
        //orderPageReqVO.setSupplierOrderId(1111L);


        CommonResult<PageResult<TrdOrderMiniHeadRespDTO>> result = trdOrderService.pageOrderList(orderPageReqVO);

        System.out.println(JsonUtils.toJsonString(result));
    }

    /**
     * 小程序订单列表
     */
    @Test
    public void testMiniPageOrderList(){
        TrdOrderPageReqDTO orderPageReqVO = new TrdOrderPageReqDTO();
        orderPageReqVO.setDeliveryState(100);
        orderPageReqVO.setPageNo(1);
        orderPageReqVO.setPageSize(10);
        orderPageReqVO.setOrderType(0);

        orderApi.miniPageOrderList(orderPageReqVO);
    }

    @Test
    public void testGetOrderInfo(){
        TrdOrderPageReqDTO orderPageReqVO = new TrdOrderPageReqDTO();
        orderPageReqVO.setKeyWords("XS250609230000049360");

        TrdOrderRespDTO dto = trdOrderService.getOrderInfo(orderPageReqVO);
        System.out.println(dto.getMemo());
    }

    /**
    * @description: 测试美的付分账流程
    * @param: []
    * @return: void
    * @author: 陈永培
    * @createtime: 2025/7/10 21:11
    */
    @Test
    public void testOrderCreateSettleTransfer() {
        Long sysCode = 4L;
        Long orderId = 636270177747927040L;
        // 调用被测试方法
        trdOrderService.orderCreateSettleTransfer(sysCode, orderId);
    }
}
