package com.zksr.trade;

import cn.hutool.poi.excel.BigExcelWriter;
import com.zksr.common.core.constant.OpenApiConstants;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.domain.vo.openapi.receive.OrderReceiveCallbackVO;
import com.zksr.common.core.utils.poi.BigExcelUtil;
import com.zksr.common.core.utils.poi.ExportSupplier;
import com.zksr.common.database.query.QueryWrapperX;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.member.api.colonelApp.dto.PageDataReqDTO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.orderSettle.dto.OrderSettleResDTO;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.mapper.TrdOrderMapper;
import com.zksr.trade.mq.TradeMqProducer;
import com.zksr.trade.service.ITrdHdfkPayService;
import com.zksr.trade.service.ITrdOrderService;
import com.zksr.trade.service.ITrdSupplierOrderService;
import com.zksr.trade.service.impl.handler.TrdNormalOrderHandlerServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.zksr.member.constant.MemberConstant.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallTradeApplication.class)
@Slf4j
public class ZksrMallTradeApplicationTests {

    /*@Autowired
    private ITrdHdfkPayService trdHdfkPayService;

    @Test
    public void testRedisService() throws InterruptedException {

        trdHdfkPayService.orderPaySuccessCallback("DF240530020000240911","hlb","0");
    }*/

    /*@Autowired
    private Consumer<Long> storeProductExpressEvent;

    @Autowired
    private TrdOrderMapper orderMapper;

    @Test
    public void test() throws InterruptedException {

        List<TrdOrder> trdOrders = orderMapper.selectList(
                new QueryWrapperX<TrdOrder>()
                        .lambda()
                        .eq(TrdOrder::getMemberId, 466979106766028800L)
        );
        for (TrdOrder order : trdOrders) {
            storeProductExpressEvent.accept(order.getOrderId());
        }

    }*/



/*    @Autowired
    private TradeMqProducer tradeMqProducer;

    @Test
    public void testRedisService() throws InterruptedException {

*//*        *//**//**
         * 4、B2B发送 退款信息 推送ERP
         *//**//*
        tradeMqProducer.sendSyncDataReceiptEvent(
                new SyncReceiptSendDTO()
                        .setSheetType(OpenApiConstants.ORDER_SHS)
                        .setSupplierDtlIdList(Arrays.asList(506391427510697986L))
        );*//*


        log.info("开始发送本地售后订单：{}","SH240913390000012550");
        tradeMqProducer.sendSyncDataAfterEvent("SH240913390000012550");
    }*/


/*
    @Autowired
    private ITrdSupplierOrderService trdSupplierOrderService;

    @Test
    public void testRedisService() throws InterruptedException {
        OrderReceiveCallbackVO vo = new OrderReceiveCallbackVO();
        vo.setSourceOrderNo("test123");
        vo.setSupplierOrderNo("XSS240914240000202453");
        trdSupplierOrderService.orderReceiveCallback(vo);
    }
*/



    /*@Autowired
    private TradeMqProducer tradeMqProducer;

    @Test
    public void testRedisService() throws InterruptedException {

        BigExcelUtil<OrderSettleResDTO> util = new BigExcelUtil<>(OrderSettleResDTO.class);
        ExportSupplier<List<OrderSettleResDTO>> list = new ExportSupplier<List<OrderSettleResDTO>>() {
            @Override
            public List<OrderSettleResDTO> get(BigExcelWriter writer) {
                ArrayList<OrderSettleResDTO> objects = new ArrayList<>();
                objects.add(new OrderSettleResDTO());
                return objects;
            }
        };
        util.exportExcelFunLimit("E:\\tmp\\hello112121.xlsx","", list);
    }*/


    @Autowired
    private TrdNormalOrderHandlerServiceImpl trdNormalOrderHandlerService;

    @Test
    public void testAfterOrderPay() throws InterruptedException {

        TrdOrder trdOrder = new TrdOrder();
        trdOrder.setAreaId(148L);
        trdOrder.setSysCode(4L);
        trdOrder.setBranchId(460322186628661248L);
        trdNormalOrderHandlerService.afterOrderPay(trdOrder);


//        TrdOrder trdOrder = new TrdOrder();
//        trdOrder.setAreaId(82L);
//        trdOrder.setSysCode(8L);
//        trdOrder.setBranchId(460322186628661248L);
//        trdNormalOrderHandlerService.afterOrderPay(trdOrder);
//




    }


    @Autowired
    private TradeMqProducer tradeMqProducer;

    @Test
    public void testMemberAppMq() throws InterruptedException {
        //业务员客户列表
        //tradeMqProducer.sendEsColonelAppBranchEvent(new ColonelAppBranchDTO(584362869473050626L,4L,null,ES_COLONEL_APP_ORDER_STATUS_0));

        //业务员客户列表
        tradeMqProducer.sendEsColonelAppBranchEvent(new ColonelAppBranchDTO(474404662844325889L,4L,null,ES_COLONEL_APP_ORDER_STATUS_0));


        //业务员首页
//        tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(473648138714120192L, 4L, null, null, null, COLONEL_APP_Page_DATA_TYPE_1));
//        tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(473648138714120192L, 4L, null, null, null, COLONEL_APP_Page_DATA_TYPE_2));
//        tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(473648138714120192L, 4L, null, null, null, COLONEL_APP_Page_DATA_TYPE_3));
//        tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(473648138714120192L, 4L, null, null, null, COLONEL_APP_Page_DATA_TYPE_4));
//        tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(473648138714120192L, 4L, null, null, null, COLONEL_APP_PAGE_DATA_TYPE_5));
    }

    @Autowired
    private ITrdOrderService trdOrderService;
    @Test
    public void testOrderCancel() throws InterruptedException {
        //业务员客户列表
        //tradeMqProducer.sendEsColonelAppBranchEvent(new ColonelAppBranchDTO(584362869473050626L,4L,null,ES_COLONEL_APP_ORDER_STATUS_0));

        //业务员客户列表
        trdOrderService.orderCancel("XS250306040000780405");


        //业务员首页
//        tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(473648138714120192L, 4L, null, null, null, COLONEL_APP_Page_DATA_TYPE_1));
//        tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(473648138714120192L, 4L, null, null, null, COLONEL_APP_Page_DATA_TYPE_2));
//        tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(473648138714120192L, 4L, null, null, null, COLONEL_APP_Page_DATA_TYPE_3));
//        tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(473648138714120192L, 4L, null, null, null, COLONEL_APP_Page_DATA_TYPE_4));
//        tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(473648138714120192L, 4L, null, null, null, COLONEL_APP_PAGE_DATA_TYPE_5));
    }



}
