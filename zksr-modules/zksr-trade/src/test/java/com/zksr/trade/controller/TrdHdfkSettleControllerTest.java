package com.zksr.trade.controller;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.zksr.trade.ZksrMallTradeApplication;
import com.zksr.trade.api.order.vo.HomePagesCurrentSalesDataReqVO;
import com.zksr.trade.api.order.vo.SupplierOrderCountAllPageReqVO;
import com.zksr.trade.api.order.vo.SupplierOrderCountPageReqVO;
import com.zksr.trade.controller.hdfk.TrdHdfkSettleController;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettlePageReqVO;
import com.zksr.trade.controller.order.TrdOrderController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import java.time.LocalDate;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @Author: chenyj8
 * @Desciption: 测试类
 */
@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallTradeApplication.class)
public class TrdHdfkSettleControllerTest {
//    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private TrdHdfkSettleController trdHdfkSettleController;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(trdHdfkSettleController)
                .setValidator(new LocalValidatorFactoryBean())
                .build();
    }

    @Test
    public void testGetPage() throws Exception {

        TrdHdfkSettlePageReqVO pageReqVO = new TrdHdfkSettlePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);

        mockMvc.perform(get("/hdfkSettle/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(pageReqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0)) // 假设CommonResult的code为0表示成功
//                .andExpect(jsonPath("$.data").isNumber())
                .andDo(MockMvcResultHandlers.print());
    }


    @Test
//    @WithMockUser(username = "zksr", roles = {"member:branch:add"}) // 模拟一个有权限的用户
    public void testCountSupplierOrder() throws Exception {
        SupplierOrderCountPageReqVO pageReqVO = new SupplierOrderCountPageReqVO();
//        pageReqVO.setPageNo(2);
//        pageReqVO.setPageSize(20);
        Page page = new Page<>(2,30);
        page.setTotal(375);
        pageReqVO.setPage(page);
        pageReqVO.setSaasTenantCode("annto");
        pageReqVO.setStartDate(LocalDate.now().minusDays(1000L));

        mockMvc.perform(post("/order/countSupplierOrder")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(pageReqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0)) // 假设CommonResult的code为0表示成功
//                .andExpect(jsonPath("$.data").isNumber())
                .andDo(MockMvcResultHandlers.print());
    }


    @Test
//    @WithMockUser(username = "zksr", roles = {"member:branch:add"}) // 模拟一个有权限的用户
    public void testCountSupplierOrderAll() throws Exception {
        SupplierOrderCountAllPageReqVO pageReqVO = new SupplierOrderCountAllPageReqVO();
//        pageReqVO.setPageNo(2);
//        pageReqVO.setPageSize(20);
        Page page = new Page<>(2,30);
        page.setTotal(375);
        pageReqVO.setStartDate(LocalDate.now().minusDays(1000L));
        pageReqVO.setPage(page);
        pageReqVO.setSaasTenantCode("annto");
        mockMvc.perform(post("/order/countSupplierOrderAll")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(pageReqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0)) // 假设CommonResult的code为0表示成功
//                .andExpect(jsonPath("$.data").isNumber())
                .andDo(MockMvcResultHandlers.print());
    }

    @Test
    public void testGetHomePagesCurrentDaySales() throws Exception {
        HomePagesCurrentSalesDataReqVO reqVO = new HomePagesCurrentSalesDataReqVO();
        reqVO.setDcId(7L);
        reqVO.setSupplierId(15580493955L);
        reqVO.setSysCode(4L);
        mockMvc.perform(post("/order/getHomePagesCurrentDaySales")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0)) // 假设CommonResult的code为0表示成功
//                .andExpect(jsonPath("$.data").isNumber())
                .andDo(MockMvcResultHandlers.print());
    }


}