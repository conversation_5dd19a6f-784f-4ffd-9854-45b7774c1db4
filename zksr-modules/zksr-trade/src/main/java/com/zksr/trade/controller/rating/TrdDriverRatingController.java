package com.zksr.trade.controller.rating;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdDriverRating;
import com.zksr.trade.service.ITrdDriverRatingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.rating.vo.TrdDriverRatingPageReqVO;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingSaveReqVO;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingRespVO;
import com.zksr.trade.convert.rating.TrdDriverRatingConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 司机评分Controller
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@Api(tags = "管理后台 - 司机评分接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/rating")
public class TrdDriverRatingController {
    @Autowired
    private ITrdDriverRatingService trdDriverRatingService;

    /**
     * 新增司机评分
     */
    @ApiOperation(value = "新增司机评分", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "司机评分", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdDriverRatingSaveReqVO createReqVO) {
        return success(trdDriverRatingService.insertTrdDriverRating(createReqVO));
    }

    /**
     * 修改司机评分
     */
    @ApiOperation(value = "修改司机评分", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "司机评分", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdDriverRatingSaveReqVO updateReqVO) {
            trdDriverRatingService.updateTrdDriverRating(updateReqVO);
        return success(true);
    }

    /**
     * 删除司机评分
     */
    @ApiOperation(value = "删除司机评分", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "司机评分", businessType = BusinessType.DELETE)
    @DeleteMapping("/{driverRatingIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] driverRatingIds) {
        trdDriverRatingService.deleteTrdDriverRatingByDriverRatingIds(driverRatingIds);
        return success(true);
    }

    /**
     * 获取司机评分详细信息
     */
    @ApiOperation(value = "获得司机评分详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{driverRatingId}")
    public CommonResult<TrdDriverRatingRespVO> getInfo(@PathVariable("driverRatingId") Long driverRatingId) {
        return success(trdDriverRatingService.getTrdDriverRating(driverRatingId));
    }

    /**
     * 分页查询司机评分
     */
    @PostMapping("/list")
    @ApiOperation(value = "获得司机评分分页列表", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdDriverRatingRespVO>> getPage(@Valid TrdDriverRatingPageReqVO pageReqVO) {
        return success(trdDriverRatingService.getTrdDriverRatingPage(pageReqVO));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:rating:add";
        /** 编辑 */
        public static final String EDIT = "trade:rating:edit";
        /** 删除 */
        public static final String DELETE = "trade:rating:remove";
        /** 列表 */
        public static final String LIST = "trade:rating:list";
        /** 查询 */
        public static final String GET = "trade:rating:query";
        /** 停用 */
        public static final String DISABLE = "trade:rating:disable";
        /** 启用 */
        public static final String ENABLE = "trade:rating:enable";
    }
}
