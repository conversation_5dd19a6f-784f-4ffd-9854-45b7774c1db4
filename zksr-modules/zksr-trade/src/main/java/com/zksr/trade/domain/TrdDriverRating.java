package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 司机评分对象 trd_driver_rating
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@TableName(value = "trd_driver_rating")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdDriverRating extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 司机评分 */
    @TableId
    private Long driverRatingId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 司机id */
    @Excel(name = "司机id")
    private Long driverId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long memberId;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    private Long supplierOrderId;

    /** 评价维度1字典code;例：0 */
    @Excel(name = "评价维度1字典code;例：0")
    private String slot1Code;

    /** 评价维度1字典值;例：司机态度 */
    @Excel(name = "评价维度1字典值;例：司机态度")
    private String slot1Val;

    /** 评价维度1-得分字典;例：4 */
    @Excel(name = "评价维度1-得分字典;例：4")
    private Integer slot1ScoreCode;

    /** 评价维度1-得分字典值;例：满意 */
    @Excel(name = "评价维度1-得分字典值;例：满意")
    private String slot1ScoreCodeVal;

    /** 评价维度2字典code;例：1 */
    @Excel(name = "评价维度2字典code;例：1")
    private String slot2Code;

    /** 评价维度2字典值;例：配送时效 */
    @Excel(name = "评价维度2字典值;例：配送时效")
    private String slot2Val;

    /** 评价维度2-得分字典;例：5 */
    @Excel(name = "评价维度2-得分字典;例：5")
    private Integer slot2Score;

    /** 评价维度2-得分字典值;例：非常满意 */
    @Excel(name = "评价维度2-得分字典值;例：非常满意")
    private String slot2ScoreCodeVal;

    /** 评价维度3字典code;例：2 */
    @Excel(name = "评价维度3字典code;例：2")
    private String slot3Code;

    /** 评价维度3字典值;例：商品完好 */
    @Excel(name = "评价维度3字典值;例：商品完好")
    private String slot3Val;

    /** 评价维度3-得分;例：3 */
    @Excel(name = "评价维度3-得分;例：3")
    private Integer slot3Score;

    /** 评价维度1-得分字典值;例：一般满意 */
    @Excel(name = "评价维度1-得分字典值;例：一般满意")
    private String scoreCodeVal;

    /** 原因code;例：0,1 */
    @Excel(name = "原因code;例：0,1")
    private String reasonCode;

    /** 低分原因code字典值;例：态度不好,送货不及时 */
    @Excel(name = "低分原因code字典值;例：态度不好,送货不及时")
    private String reasonVal;

    /** 反馈信息;例：加油！努力！ */
    @Excel(name = "反馈信息;例：加油！努力！")
    private String fedbackMsg;

    /** 反馈图片;例： */
    @Excel(name = "反馈图片;例：")
    private String fedbackPics;

}
