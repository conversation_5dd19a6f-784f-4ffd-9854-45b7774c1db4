package com.zksr.trade.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.KdniaoUtil;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.partnerConfig.dto.CourierConfigDTO;
import com.zksr.trade.api.express.dto.ExpressResDTO;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressReqVO;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.mapper.TrdSupplierOrderDtlMapper;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdOrderExpressMapper;
import com.zksr.trade.convert.orderExpress.TrdOrderExpressConvert;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressPageReqVO;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressSaveReqVO;
import com.zksr.trade.service.ITrdOrderExpressService;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.error;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 订单快递Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Service
@Slf4j
public class TrdOrderExpressServiceImpl implements ITrdOrderExpressService {
    @Autowired
    private TrdOrderExpressMapper trdOrderExpressMapper;
    @Autowired
    private TrdCacheService trdCacheService;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;
    @Autowired
    private List<ITrdOrderHandlerService> trdOrderHandlerServices;

    /**
     * 新增订单快递
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdOrderExpress(TrdOrderExpressSaveReqVO createReqVO) {
        // 插入
        TrdOrderExpress trdOrderExpress = TrdOrderExpressConvert.INSTANCE.convert(createReqVO);
        trdOrderExpressMapper.insert(trdOrderExpress);
        // 返回
        return trdOrderExpress.getOrderExpressId();
    }

    /**
     * 修改订单快递
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdOrderExpress(TrdOrderExpressSaveReqVO updateReqVO) {
        trdOrderExpressMapper.updateById(TrdOrderExpressConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除订单快递
     *
     * @param orderExpressId 订单快递id
     */
    @Override
    public void deleteTrdOrderExpress(Long orderExpressId) {
        // 删除
        trdOrderExpressMapper.deleteById(orderExpressId);
    }

    /**
     * 批量删除订单快递
     *
     * @param orderExpressIds 需要删除的订单快递主键
     * @return 结果
     */
    @Override
    public void deleteTrdOrderExpressByOrderExpressIds(Long[] orderExpressIds) {
        for(Long orderExpressId : orderExpressIds){
            this.deleteTrdOrderExpress(orderExpressId);
        }
    }

    /**
     * 获得订单快递
     *
     * @param orderExpressId 订单快递id
     * @return 订单快递
     */
    @Override
    public TrdOrderExpress getTrdOrderExpress(Long orderExpressId) {
        return trdOrderExpressMapper.selectById(orderExpressId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdOrderExpress> getTrdOrderExpressPage(TrdOrderExpressPageReqVO pageReqVO) {
        return trdOrderExpressMapper.selectPage(pageReqVO);
    }

    @Override
    public ExpressResDTO getExpressInfoByCourier(String phone, String courierCompanyNo, String courierNumber) {
        if(ToolUtil.isEmpty(courierCompanyNo) || ToolUtil.isEmpty(courierNumber)){
            throw new ServiceException("未填写物流信息！");
        }
        Long sysCode = ToolUtil.isEmpty(SecurityUtils.getLoginUser()) ? MallSecurityUtils.getLoginMember().getSysCode() : SecurityUtils.getLoginUser().getSysCode();
        CourierConfigDTO courierConfig = trdCacheService.getCourierConfigDTO(sysCode);
        if(ToolUtil.isEmpty(courierConfig) || ToolUtil.isEmpty(courierConfig.getAppKey()) || ToolUtil.isEmpty(courierConfig.getCourierPlatform()) || ToolUtil.isEmpty(courierConfig.getMerchantId())){
            log.error("平台【{}】,未配置快递鸟接口请求参数",SecurityUtils.getLoginUser().getSysCode());
            throw new ServiceException("平台编号"+SecurityUtils.getLoginUser().getSysCode()+"未配置快递鸟接口请求参数");
        }
        try {
            String result = KdniaoUtil.orderOnlineByJson(phone,courierCompanyNo,courierNumber,courierConfig.getAppKey(),courierConfig.getMerchantId());
            return JSONObject.parseObject(result, ExpressResDTO.class);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw new ServiceException("查看物流信息失败");
        }
    }

    @Override
    public void updateOrderExpressByOrderExpressId(TrdOrderExpressReqVO reqVO) {
      TrdOrderExpress orderExpress = trdOrderExpressMapper.selectById(reqVO.getOrderExpressId());
      if (ToolUtil.isEmpty(orderExpress))
          throw exception(TRD_ORDER_EXPRESS_NOT_EXISTS);
      orderExpress.setExpressNo(reqVO.getCourierNumber());
      orderExpress.setExpressCom(reqVO.getExpressCom());
      orderExpress.setExpressComNo(reqVO.getExpressComNo());
      trdOrderExpressMapper.updateById(orderExpress);
    }

    @Override
    @Transactional
    public void saveOrderExpress(TrdOrderExpressSaveReqVO reqSaveVO) {
        if (ToolUtil.isEmpty(reqSaveVO) || ToolUtil.isEmpty(reqSaveVO.getExpressNo())) {
            throw new ServiceException("快递单号不能为空!");
        }

        List<String> expressNoList = Arrays.asList(reqSaveVO.getExpressNo().split(";"));
        List<TrdOrderExpress> trdOrderExpressList = expressNoList.stream().map(expressNo -> {
            TrdOrderExpress trdOrderExpress = TrdOrderExpressConvert.INSTANCE.convert(reqSaveVO);
            trdOrderExpress.setExpressNo(expressNo);
            return trdOrderExpress;
        }).collect(Collectors.toList());
        trdOrderExpressMapper.insertBatch(trdOrderExpressList);

        /**
         * 更新订单商品状态 ，当当前商品状态为 备货中或者代发货 时才更改
         */
        // 查询订单明细数据
        TrdSupplierOrderDtl orderDtl = trdSupplierOrderDtlMapper.selectById(reqSaveVO.getSupplierOrderDtlId());
        if (Objects.equals(orderDtl.getDeliveryState(), DeliveryStatusEnum.PREAREGOODS.getCode())
                || Objects.equals(orderDtl.getDeliveryState(), DeliveryStatusEnum.WAIT_FH.getCode())) {
            // 修改订单明细状态为待收货
            trdOrderHandlerServices.forEach(handler -> handler.orderOutbound(orderDtl));
        }

    }

    @Override
    public List<TrdOrderExpress> getOrderExpressInfoByOrderDtlIds(List<Long> supplierOrderDtlIds) {
        return trdOrderExpressMapper.getOrderExpressInfoByOrderDtlIds(new HashSet<>(supplierOrderDtlIds));
    }

    private void validateTrdOrderExpressExists(Long orderExpressId) {
        if (trdOrderExpressMapper.selectById(orderExpressId) == null) {
            throw exception(TRD_ORDER_EXPRESS_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 订单快递 TODO 补充编号 ==========
    // ErrorCode TRD_ORDER_EXPRESS_NOT_EXISTS = new ErrorCode(TODO 补充编号, "订单快递不存在");


}
