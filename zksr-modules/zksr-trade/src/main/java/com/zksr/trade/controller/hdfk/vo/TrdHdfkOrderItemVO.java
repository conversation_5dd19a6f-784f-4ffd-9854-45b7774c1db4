package com.zksr.trade.controller.hdfk.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/29 8:37
 */
@Data
public class TrdHdfkOrderItemVO {
    @ApiModelProperty("订单Id")
    private Long orderId;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("入驻商订单号")
    private String supplierOrderNo;

    @ApiModelProperty("入驻商ID")
    private Long supplierId;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("入驻商订单ID")
    private Long supplierOrderId;

    @ApiModelProperty("商品图片")
    private String thumb;

    @ApiModelProperty("skuId")
    private Long skuId;

    @ApiModelProperty("spu名称")
    private String spuName;

    @ApiModelProperty("商品数量")
    private Integer totalNum;

    @ApiModelProperty("应付金额")
    private BigDecimal settleAmt;

    @ApiModelProperty("订单状态, 0-待配货,3-待发货,4-待收货,5-已收货,6-已完成,7-备货中,40-待装车")
    private Long deliveryState;

    @ApiModelProperty("入驻商商品详情ID")
    private Long supplierOrderDtlId;

    @ApiModelProperty("收货后售后数量")
    private Long afterTotalNum;

    @ApiModelProperty("精确成交单价（6位小数）")
    private BigDecimal exactPrice;
}
