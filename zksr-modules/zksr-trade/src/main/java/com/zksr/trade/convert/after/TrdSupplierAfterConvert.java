package com.zksr.trade.convert.after;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.after.dto.TrdSupplierAfterDTO;
import com.zksr.trade.domain.TrdSupplierAfter;
import com.zksr.trade.controller.after.supplierVo.TrdSupplierAfterRespVO;
import com.zksr.trade.controller.after.supplierVo.TrdSupplierAfterSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 入驻商售后单 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-20
*/
@Mapper
public interface TrdSupplierAfterConvert {

    TrdSupplierAfterConvert INSTANCE = Mappers.getMapper(TrdSupplierAfterConvert.class);

    TrdSupplierAfterRespVO convert(TrdSupplierAfter trdSupplierAfter);

    TrdSupplierAfter convert(TrdSupplierAfterSaveReqVO trdSupplierAfterSaveReq);

    PageResult<TrdSupplierAfterRespVO> convertPage(PageResult<TrdSupplierAfter> trdSupplierAfterPage);

    TrdSupplierAfterDTO convertDTO(TrdSupplierAfter trdSupplierAfter);

}
