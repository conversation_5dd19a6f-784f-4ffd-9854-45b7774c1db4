package com.zksr.trade.controller.after;

import javax.validation.Valid;

import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.enums.SourceType;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.trade.api.order.vo.AfterApproveDtlEditVO;
import com.zksr.trade.api.order.vo.AfterDtlReqVO;
import com.zksr.trade.controller.after.dto.AfterDtlRespDTO;
import com.zksr.trade.controller.after.dto.AfterPageRespDTO;
import com.zksr.trade.controller.after.vo.*;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.service.ITrdAfterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 售后单Controller
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Api(tags = "管理后台 - 售后单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/after")
public class TrdAfterController {
    @Autowired
    private ITrdAfterService trdAfterService;

    /**
     * 新增售后单
     */
    @ApiOperation(value = "新增售后单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "售后单", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdAfterSaveReqVO createReqVO) {
        return success(trdAfterService.insertTrdAfter(createReqVO));
    }

    /**
     * 修改售后单
     */
    @ApiOperation(value = "修改售后单", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "售后单", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdAfterSaveReqVO updateReqVO) {
            trdAfterService.updateTrdAfter(updateReqVO);
        return success(true);
    }

    /**
     * 删除售后单
     */
    @ApiOperation(value = "删除售后单", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "售后单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{afterIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] afterIds) {
        trdAfterService.deleteTrdAfterByAfterIds(afterIds);
        return success(true);
    }

    /**
     * 获取售后单操作详细信息
     */
    @ApiOperation(value = "获得售后单操作详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/getAfterDtlInfo")
    public CommonResult<AfterDtlRespDTO> getAfterDtlInfo(@Valid AfterDtlReqVO reqVO) {
        reqVO.setSupplierId(SecurityUtils.getSupplierId());
        return success(trdAfterService.getTrdAfterDtl(reqVO));
    }

    /**
     * 分页查询售后单
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得售后单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(supplierAlias = "supAfter", dcAlias = "tAfter", dcFieldAlias = SystemConstants.DC_ID)
    public CommonResult<PageResult<AfterPageRespDTO>> getPage(@Valid AfterPageReqVO pageReqVO) {
        pageReqVO.setSupplierId(SecurityUtils.getSupplierId());
        return success(trdAfterService.getTrdAfterPage(pageReqVO));
    }

    @ApiOperation(value = "取消售后单退货（同意申请前）", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.APPROVE)
    @RequiresPermissions(Permissions.APPROVE)
    @Log(title = "取消售后单退货（同意申请前）", businessType = BusinessType.UPDATE)
    @PutMapping("/cancelAfterReturn/{afterId}")
    public CommonResult<Boolean> cancelAfterReturn(@ApiParam(name = "afterId", value = "售后订单ID", required = true) @PathVariable("afterId") Long afterId) {
        trdAfterService.cancelAfterReturn(afterId);
        return success(true);
    }

    @ApiOperation(value = "审核售后单退货", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.APPROVE)
    @RequiresPermissions(Permissions.APPROVE)
    @Log(title = "审核售后单退货", businessType = BusinessType.UPDATE)
    @PostMapping("/approveAfterReturn")
    public CommonResult<Boolean> approveAfterReturn(@RequestBody AfterApproveDtlEditVO editVO) {
        trdAfterService.approveAfterReturn(editVO);
        return success(true);
    }

    @ApiOperation(value = "撤销售后单退货（同意申请后）", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.APPROVE)
    @RequiresPermissions(Permissions.APPROVE)
    @Log(title = "撤销售后单退货（同意申请后）", businessType = BusinessType.UPDATE)
    @PutMapping("/revokeAfterReturn/{supplierAfterNo}")
    public CommonResult<Boolean> revokeAfterReturn(@ApiParam(name = "supplierAfterNo", value = "入驻商售后订单编号", required = true) @PathVariable("supplierAfterNo") String supplierAfterNo) {
        trdAfterService.cancelAfter(supplierAfterNo, SourceType.PC.getType());
        return success(true);
    }

    @ApiOperation(value = "审核售后单退款", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.APPROVE)
    @RequiresPermissions(Permissions.APPROVE)
    @Log(title = "审核售后单退款", businessType = BusinessType.UPDATE)
    @PostMapping("/approveAfterRefund")
    public CommonResult<Boolean> approveAfterRefund(@RequestBody AfterApproveDtlEditVO editVO) {
        trdAfterService.approveAfterRefund(editVO);
        return success(true);
    }

    @ApiOperation(value = "售后单退款失败重新发送", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.APPROVE)
    @RequiresPermissions(Permissions.APPROVE)
    @Log(title = "售后单退款失败重新发起", businessType = BusinessType.UPDATE)
    @PostMapping("/afterRefundFailResend")
    public CommonResult<Boolean> afterRefundFailResend(@RequestBody AfterApproveDtlEditVO editVO) {
        trdAfterService.approveAfterRefund(editVO);
        return success(true);
    }

    @ApiOperation(value = "批量审核售后单退款", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.APPROVE)
    @RequiresPermissions(Permissions.APPROVE)
    @Log(title = "批量审核售后单退款", businessType = BusinessType.UPDATE)
    @PutMapping("/approveAfterBatchRefund")
    public CommonResult<String> approveAfterBatchRefund(@RequestBody List<AfterDtlReqVO> reqVOS) {
        return success(trdAfterService.approveAfterBatchRefund(reqVOS));
    }



    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:after:add";
        /** 编辑 */
        public static final String EDIT = "trade:after:edit";
        /** 删除 */
        public static final String DELETE = "trade:after:remove";
        /** 列表 */
        public static final String LIST = "trade:after:list";
        /** 查询 */
        public static final String GET = "trade:after:query";
        /** 停用 */
        public static final String DISABLE = "trade:after:disable";
        /** 启用 */
        public static final String ENABLE = "trade:after:enable";
        /**
         * 审核售后单
         */
        public static final String APPROVE = "trade:after:approve";
    }
}
