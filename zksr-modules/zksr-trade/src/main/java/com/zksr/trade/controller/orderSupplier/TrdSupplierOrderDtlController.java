package com.zksr.trade.controller.orderSupplier;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderDtlPageReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderDtlRespVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderDtlSaveReqVO;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.service.ITrdSupplierOrderDtlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 入驻商订单明细Controller
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Api(tags = "管理后台 - 入驻商订单明细接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/orderSupplierDtl")
public class TrdSupplierOrderDtlController {
    @Autowired
    private ITrdSupplierOrderDtlService trdSupplierOrderDtlService;

    /**
     * 新增入驻商订单明细
     */
    @ApiOperation(value = "新增入驻商订单明细", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "入驻商订单明细", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdSupplierOrderDtlSaveReqVO createReqVO) {
        return success(trdSupplierOrderDtlService.insertTrdSupplierOrderDtl(createReqVO));
    }

    /**
     * 修改入驻商订单明细
     */
    @ApiOperation(value = "修改入驻商订单明细", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "入驻商订单明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdSupplierOrderDtlSaveReqVO updateReqVO) {
        trdSupplierOrderDtlService.updateTrdSupplierOrderDtl(updateReqVO);
        return success(true);
    }

    /**
     * 删除入驻商订单明细
     */
    @ApiOperation(value = "删除入驻商订单明细", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "入驻商订单明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{supplierOrderDtlIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] supplierOrderDtlIds) {
        trdSupplierOrderDtlService.deleteTrdSupplierOrderDtlBySupplierOrderDtlIds(supplierOrderDtlIds);
        return success(true);
    }

    /**
     * 获取入驻商订单明细详细信息
     */
    @ApiOperation(value = "获得入驻商订单明细详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{supplierOrderDtlId}")
    public CommonResult<TrdSupplierOrderDtlRespVO> getInfo(@PathVariable("supplierOrderDtlId") Long supplierOrderDtlId) {
        TrdSupplierOrderDtl trdSupplierOrderDtl = trdSupplierOrderDtlService.getTrdSupplierOrderDtl(supplierOrderDtlId);
        return success(HutoolBeanUtils.toBean(trdSupplierOrderDtl, TrdSupplierOrderDtlRespVO.class));
    }

    /**
     * 分页查询入驻商订单明细
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得入驻商订单明细分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdSupplierOrderDtlRespVO>> getPage(@Valid TrdSupplierOrderDtlPageReqVO pageReqVO) {
        PageResult<TrdSupplierOrderDtl> pageResult = trdSupplierOrderDtlService.getTrdSupplierOrderDtlPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, TrdSupplierOrderDtlRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "trade:dtl:add";
        /**
         * 编辑
         */
        public static final String EDIT = "trade:dtl:edit";
        /**
         * 删除
         */
        public static final String DELETE = "trade:dtl:remove";
        /**
         * 列表
         */
        public static final String LIST = "trade:dtl:list";
        /**
         * 查询
         */
        public static final String GET = "trade:dtl:query";
        /**
         * 停用
         */
        public static final String DISABLE = "trade:dtl:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "trade:dtl:enable";
    }
}
