package com.zksr.trade.controller.after.supplierVo;

import java.math.BigDecimal;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

/**
 * 入驻商售后单对象 trd_supplier_after
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@ApiModel("入驻商售后单 - trd_supplier_after分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class TrdSupplierAfterPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 入驻商订单id */
    @ApiModelProperty(value = "退款金额;实际退款金额")
    private Long supplierAfterId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 入驻商售后单编号 */
    @Excel(name = "入驻商售后单编号")
    @ApiModelProperty(value = "入驻商售后单编号")
    private String supplierAfterNo;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    /** 订单id */
    @Excel(name = "订单id")
    @ApiModelProperty(value = "订单id", required = true)
    private Long orderId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号", required = true)
    private String orderNo;

    /** 售后单id */
    @Excel(name = "售后单id")
    @ApiModelProperty(value = "售后单id", required = true)
    private Long afterId;

    /** 售后单编号 */
    @Excel(name = "售后单编号")
    @ApiModelProperty(value = "售后单编号", required = true)
    private String afterNo;

    /** 配送费 */
    @Excel(name = "配送费")
    @ApiModelProperty(value = "配送费")
    private BigDecimal transAmt;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id", required = true)
    private Long supplierId;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal subDiscountAmt;

    /** 退款金额;实际退款金额 */
    @Excel(name = "退款金额;实际退款金额")
    @ApiModelProperty(value = "退款金额;实际退款金额")
    private BigDecimal subRefundAmt;

    public TrdSupplierAfterPageReqVO(String orderNo) {
        this.orderNo = orderNo;
    }


}
