package com.zksr.trade.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdHdfkPay;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayPageReqVO;

import java.util.Objects;


/**
 * 货到付款付款单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Mapper
public interface TrdHdfkPayMapper extends BaseMapperX<TrdHdfkPay> {
    default PageResult<TrdHdfkPay> selectPage(TrdHdfkPayPageReqVO reqVO) {
        LambdaQueryWrapperX<TrdHdfkPay> lambdaQueryWrapperX = new LambdaQueryWrapperX<TrdHdfkPay>()
                .eqIfPresent(TrdHdfkPay::getHdfkPayNo, reqVO.getHdfkPayNo())
                .eqIfPresent(TrdHdfkPay::getPaySource, reqVO.getPaySource())
                .orderByDesc(TrdHdfkPay::getHdfkPayId);
        // 创建时间时间
        if (Objects.nonNull(reqVO.getStartProcessTime())) {
            lambdaQueryWrapperX.between(TrdHdfkPay::getCreateTime, reqVO.getStartProcessTime(), reqVO.getEndProcessTime());
        }
        return selectPage(reqVO, lambdaQueryWrapperX);
    }

    default TrdHdfkPay selectByPayNo(String orderNo) {
        return selectOne(new LambdaQueryWrapperX<TrdHdfkPay>()
                .eqIfPresent(TrdHdfkPay::getHdfkPayNo, orderNo)
        );
    }
}
