package com.zksr.trade.controller.orderExpressImport.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

/**
 * 快递导入记录对象 trd_express_import
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Data
@ApiModel("快递导入记录 - trd_express_import分页 Request VO")
public class TrdExpressImportSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 导入文件名 */
    @Excel(name = "导入文件名")
    @ApiModelProperty(value = "导入文件名")
    private String fileName;

    /** 导入文件下载地址 */
    @Excel(name = "导入文件下载地址")
    @ApiModelProperty(value = "导入文件下载地址")
    private String fileUrl;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 导入总数 */
    @Excel(name = "导入总数")
    @ApiModelProperty(value = "导入总数")
    private Long totalNum = 0L;

    /** mq发送数量 */
    @Excel(name = "mq发送数量")
    @ApiModelProperty(value = "mq发送数量")
    private Long mqSendNum = 0L;

    /** mq接收数量 */
    @Excel(name = "mq接收数量")
    @ApiModelProperty(value = "mq接收数量")
    private Long mqReceiveNum = 0L;

    /** 成功条数 */
    @Excel(name = "成功条数")
    @ApiModelProperty(value = "成功条数")
    private Long successNum = 0L;

    /** 失败条数 */
    @Excel(name = "失败条数")
    @ApiModelProperty(value = "失败条数")
    private Long failNum = 0L;

    /** 是否更新已存在的数据;是否更新已存在的数据 */
    @Excel(name = "是否更新已存在的数据;是否更新已存在的数据")
    @ApiModelProperty(value = "是否更新已存在的数据;是否更新已存在的数据")
    private Integer updateSupport;

}
