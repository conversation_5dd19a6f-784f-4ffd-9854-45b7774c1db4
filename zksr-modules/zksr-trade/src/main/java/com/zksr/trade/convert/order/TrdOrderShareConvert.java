package com.zksr.trade.convert.order;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdOrderShare;
import com.zksr.trade.api.order.vo.TrdOrderShareRespVO;
import com.zksr.trade.api.order.vo.TrdOrderShareSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 订单分享 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-10-29
*/
@Mapper
public interface TrdOrderShareConvert {

    TrdOrderShareConvert INSTANCE = Mappers.getMapper(TrdOrderShareConvert.class);

    TrdOrderShareRespVO convert(TrdOrderShare trdOrderShare);

    TrdOrderShare convert(TrdOrderShareSaveReqVO trdOrderShareSaveReq);

    PageResult<TrdOrderShareRespVO> convertPage(PageResult<TrdOrderShare> trdOrderSharePage);
}