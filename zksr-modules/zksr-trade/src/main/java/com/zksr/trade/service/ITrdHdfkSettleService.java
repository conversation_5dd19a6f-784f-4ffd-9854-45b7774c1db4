package com.zksr.trade.service;

import javax.validation.*;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.api.hdfk.dto.HdfkNoPaymentTotalDTO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkOrderRespVO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkReqVO;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveReqVO;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveRespVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSkuItemRespVO;
import com.zksr.trade.domain.TrdHdfkSettle;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettlePageReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSaveReqVO;

import java.util.List;

/**
 * 货到付款结算Service接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface ITrdHdfkSettleService {

    /**
     * 新增货到付款结算
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public void insertTrdHdfkSettle(@Valid List<TrdHdfkSettleSaveReqVO> createReqVO);

    /**
     * 获得货到付款结算
     *
     * @param hdfkSettleId 订单结算id
     * @return 货到付款结算
     */
    public TrdHdfkSettle getTrdHdfkSettle(Long hdfkSettleId);

    /**
     * 获得货到付款结算分页
     *
     * @param pageReqVO 分页查询
     * @return 货到付款结算分页
     */
    PageResult<TrdHdfkSettle> getTrdHdfkSettlePage(TrdHdfkSettlePageReqVO pageReqVO);

    /**
     * 获取门店货到付款未付款订单列表
     * @param branchHdfkReqVO
     * @return
     */
    List<BranchHdfkOrderRespVO> getBranchHdfkList(BranchHdfkReqVO branchHdfkReqVO);

    /**
     * 获取门店货到付款未付款统计
     * @param branchHdfkReqVO
     * @return
     */
    HdfkNoPaymentTotalDTO getBranchHdfkNoPaymentTotal(BranchHdfkReqVO branchHdfkReqVO);

    /**
     * 获取sku纬度清算列表
     * @param pageReqVO
     * @return
     */
    PageResult<TrdHdfkSettleSkuItemRespVO> getTrdHdfkSettleSkuItemPage(TrdHdfkSettlePageReqVO pageReqVO);

    /**
     * 获取列表记录
     * @param hdfkPayId
     * @return
     */
    List<TrdHdfkSettleSkuItemRespVO> getTrdHdfkSettleSkuItemRespVOByHdfkId(Long hdfkPayId);

    /**
     * 根据入驻商订单编号获得货到付款结算
     *
     * @param supplierOrderNo 订单编号
     * @return 货到付款结算
     */
    public List<TrdHdfkSettle> getTrdHdfkSettleList(String supplierOrderNo);
}
