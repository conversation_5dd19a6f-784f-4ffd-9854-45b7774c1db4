package com.zksr.trade.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.domain.dto.car.AppCarItemDTO;
import com.zksr.common.core.domain.vo.car.AppCarPageReqVO;
import com.zksr.common.core.domain.vo.car.AppCarPageRespVO;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.enums.RedisCarConstants;
import com.zksr.common.redis.service.RedisCarService;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.trade.api.car.dto.AppCarEventDTO;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.trade.api.car.dto.AppCarInitDTO;
import com.zksr.common.core.domain.dto.car.AppCarStorageDTO;
import com.zksr.trade.api.car.vo.TrdCarApiRespVO;
import com.zksr.trade.controller.car.vo.TrdCarPageReqVO;
import com.zksr.trade.controller.car.vo.TrdCarSaveReqVO;
import com.zksr.trade.domain.TrdCar;
import com.zksr.trade.mapper.TrdCarMapper;
import com.zksr.trade.service.ITrdCarService;
import com.zksr.trade.service.TrdCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 购物车Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Service
@SuppressWarnings("all")
public class TrdCarServiceImpl implements ITrdCarService {

    @Autowired
    private TrdCarMapper trdCarMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private RedisCarService redisCarService;

    /**
     * 新增购物车
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdCar(TrdCarSaveReqVO createReqVO) {
        // 插入
        TrdCar trdCar = HutoolBeanUtils.toBean(createReqVO, TrdCar.class);
        trdCarMapper.insert(trdCar);
        // 返回
        return trdCar.getCarId();
    }

    /**
     * 修改购物车
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdCar(TrdCarSaveReqVO updateReqVO) {
        trdCarMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, TrdCar.class));
    }

    /**
     * 删除购物车
     *
     * @param carId 购物车id
     */
    @Override
    public void deleteTrdCar(Long carId) {
        // 删除
        trdCarMapper.deleteById(carId);
    }

    /**
     * 批量删除购物车
     *
     * @param carIds 需要删除的购物车主键
     * @return 结果
     */
    @Override
    public void deleteTrdCarByCarIds(Long[] carIds) {
        for(Long carId : carIds){
            this.deleteTrdCar(carId);
        }
    }

    /**
     * 获得购物车
     *
     * @param carId 购物车id
     * @return 购物车
     */
    @Override
    public TrdCar getTrdCar(Long carId) {
        return trdCarMapper.selectById(carId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdCar> getTrdCarPage(TrdCarPageReqVO pageReqVO) {
        return trdCarMapper.selectPage(pageReqVO);
    }

    /**
     * 处理购物车消息
     * @param carEvent  购物车事件
     */
    @Override
    public void processCarEvent(AppCarEventDTO carEvent) {
        // 检查redis key 还存在不, 不存在则数据库为0
        // 存在,  则更新redis数据状态
        if (AppCarEventDTO.TYPE_STANDARD.equals(carEvent.getType())) {
            List<AppCarIdDTO> carIdList = carEvent.getCarIds();
            for (AppCarIdDTO carId : carIdList) {
                SkuDTO sku = trdCacheService.getSkuDTO(carId.getSkuId());
                if (Objects.isNull(sku)) {
                    continue;
                }

                // 从数据库查询最新状态
                TrdCar car = trdCarMapper.selectByBranchIdAndSkuId(carId.getBranchId(), carId.getSkuId());
                if (Objects.isNull(car)) {
                    car = new TrdCar()
                            .setBranchId(carId.getBranchId())
                            .setSupplierItemId(carId.getSupplierItemId())
                            .setAreaItemId(carId.getAreaItemId())
                            .setSkuId(sku.getSkuId())
                            .setSpuId(sku.getSpuId())
                            .setSysCode(sku.getSysCode())
                            .setSupplierId(carId.getSupplierId());
                    trdCarMapper.insert(car);
                }
                String carItemKey = RedisCarConstants.getSupplierItemKey(carId.getBranchId(), carId.getType(), carId.getUnitSize(), carId.itemId());
                if (redisTemplate.hasKey(carItemKey)) {
                    List<String> list = redisTemplate.opsForHash().multiGet(carItemKey, AppCarStorageDTO.fields);
                    AppCarStorageDTO storageDTO = AppCarStorageDTO.build(list);
                    boolean selected = redisTemplate.opsForSet().isMember(RedisCarConstants.getBranchSelectKey(carId.getBranchId(), carId.getType()), carId.getId());

                    TrdCar update = new TrdCar();
                    update.setCarId(car.getCarId());
                    update.setRecommendFlag(storageDTO.getRecommendFlag());
                    update.setSelected(selected ? NumberPool.INT_ONE : NumberPool.INT_ZERO);
                    update.setQty(storageDTO.getProductNum().longValue());
                    trdCarMapper.updateById(update);
                } else {
                    // redis 都没有那自然是没有 选中 || 推荐 || 数量
                    /*TrdCar update = new TrdCar();
                    update.setCarId(car.getCarId());
                    update.setRecommendFlag(NumberPool.INT_ZERO);
                    update.setSelected(NumberPool.INT_ZERO);
                    update.setQty(NumberPool.LONG_ZERO);
                    trdCarMapper.updateById(update);*/
                    trdCarMapper.deleteById(car.getCarId());
                }
            }
        } else if (AppCarEventDTO.TYPE_CLEAN_ALL.equals(carEvent.getType())) {
            // 全部删除
            // 数量全部变成0
            TrdCar update = new TrdCar();
            update.setRecommendFlag(NumberPool.INT_ZERO);
            update.setSelected(NumberPool.INT_ZERO);
            update.setQty(NumberPool.LONG_ZERO);
            /*trdCarMapper.update(
                    update,
                    Wrappers.lambdaUpdate(TrdCar.class)
                            .eq(TrdCar::getBranchId, carEvent.getBranchId())
            );*/
            trdCarMapper.delete(
                    Wrappers.lambdaUpdate(TrdCar.class)
                            .eq(TrdCar::getBranchId, carEvent.getBranchId())
            );
        } else if (AppCarEventDTO.TYPE_SELECTED_ALL.equals(carEvent.getType())) {
            // 全部选中
            TrdCar update = new TrdCar();
            update.setSelected(NumberPool.INT_ONE);
            trdCarMapper.update(
                    update,
                    Wrappers.lambdaUpdate(TrdCar.class)
                            .eq(TrdCar::getBranchId, carEvent.getBranchId())
            );
        } else if (AppCarEventDTO.TYPE_UNSELECTED_ALL.equals(carEvent.getType())) {
            // 全不选
            TrdCar update = new TrdCar();
            update.setSelected(NumberPool.INT_ZERO);
            trdCarMapper.update(
                    update,
                    Wrappers.lambdaUpdate(TrdCar.class)
                            .eq(TrdCar::getBranchId, carEvent.getBranchId())
            );
        }
    }

    @Override
    public List<AppCarInitDTO> getInitData(Long branchId, Long minId) {
        List<AppCarInitDTO> dtoList = trdCarMapper.selectInitData(branchId, minId);
        dtoList.forEach(item -> {
            if (Objects.nonNull(item.getSupplierItemId()) && item.getSupplierItemId() > 0L) {
                item.setType(ProductType.GLOBAL.getType());
            } else {
                item.setType(ProductType.LOCAL.getType());
            }
        });
        return dtoList;
    }

    @Override
    public List<TrdCarApiRespVO> getTrdCarList(Long branchId) {

        TrdCarPageReqVO reqVO = new TrdCarPageReqVO();
        reqVO.setBranchId(branchId);

        PageResult<TrdCar> trdCarPage = trdCarMapper.selectPage(reqVO);

        return HutoolBeanUtils.toBean(trdCarPage.getList(), TrdCarApiRespVO.class);
    }

    @Override
    public void saveRedisCar(Long branchId) {
        // 删除门店所有数据
        trdCarMapper.deleteByBranchId(branchId);
        // 获取全国
        // 获取本地
        ArrayList<ProductType> productTypes = ListUtil.toList(ProductType.GLOBAL, ProductType.LOCAL);
        ArrayList<TrdCar> trdCars = new ArrayList<>();
        for (ProductType productType : productTypes) {
            Integer pageNo = 1;
            for (;;) {
                AppCarPageReqVO req = new AppCarPageReqVO();
                req.setBranchId(branchId);
                req.setProductType(productType.getType());
                req.setPageNo(pageNo);
                AppCarPageRespVO appCarPageRespVO = redisCarService.getAppCarItemListPage(req);
                if (appCarPageRespVO.getList().isEmpty()) {
                    return;
                }
                for (AppCarItemDTO itemDTO : appCarPageRespVO.getList()) {
                    TrdCar car = new TrdCar()
                            .setBranchId(branchId)
                            .setSupplierItemId(itemDTO.getSupplierItemId())
                            .setAreaItemId(itemDTO.getAreaItemId())
                            .setSkuId(itemDTO.getSkuId())
                            .setSpuId(itemDTO.getSpuId())
                            .setSupplierId(itemDTO.getSupplierId())
                            .setUnitSize(itemDTO.getUnitSize())
                            .setUnit(itemDTO.getUnit());
                    trdCars.add(car);
                }
                pageNo++;
            }
        }
        if (!trdCars.isEmpty()) {
            trdCarMapper.insertBatch(trdCars);
        }
    }
}
