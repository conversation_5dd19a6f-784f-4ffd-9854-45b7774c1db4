package com.zksr.trade.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.controller.status.vo.TrdExpressStatusPageReqVO;
import com.zksr.trade.controller.status.vo.TrdExpressStatusSaveReqVO;
import com.zksr.trade.domain.TrdExpressStatus;

import javax.validation.Valid;


/**
 * 物流状态（ERP-&gt;B2B）Service接口
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
public interface ITrdExpressStatusService {

    /**
     * 新增物流状态（ERP-&gt;B2B）
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdExpressStatus(@Valid TrdExpressStatusSaveReqVO createReqVO);

    /**
     * 修改物流状态（ERP-&gt;B2B）
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdExpressStatus(@Valid TrdExpressStatusSaveReqVO updateReqVO);

    /**
     * 删除物流状态（ERP-&gt;B2B）
     *
     * @param id 主键ID
     */
    public void deleteTrdExpressStatus(Long id);

    /**
     * 批量删除物流状态（ERP-&gt;B2B）
     *
     * @param ids 需要删除的物流状态（ERP-&gt;B2B）主键集合
     * @return 结果
     */
    public void deleteTrdExpressStatusByIds(Long[] ids);

    /**
     * 获得物流状态（ERP-&gt;B2B）
     *
     * @param id 主键ID
     * @return 物流状态（ERP-&gt;B2B）
     */
    public TrdExpressStatus getTrdExpressStatus(Long id);

    /**
     * 获得物流状态（ERP-&gt;B2B）分页
     *
     * @param pageReqVO 分页查询
     * @return 物流状态（ERP-&gt;B2B）分页
     */
    PageResult<TrdExpressStatus> getTrdExpressStatusPage(TrdExpressStatusPageReqVO pageReqVO);

}
