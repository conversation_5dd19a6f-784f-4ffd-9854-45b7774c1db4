package com.zksr.trade.api.hdfk;

import com.zksr.common.core.domain.vo.openapi.receive.OrderHdfkSettleVO;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.trade.api.hdfk.dto.HdfkNoPaymentTotalDTO;
import com.zksr.trade.api.hdfk.dto.HdfkPayDTO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkOrderRespVO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkReqVO;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveReqVO;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveRespVO;
import com.zksr.trade.domain.TrdHdfkSettle;
import com.zksr.trade.service.ITrdHdfkPayService;
import com.zksr.trade.service.ITrdHdfkSettleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 货到付款api
 * @date 2024/5/29 8:34
 */
@ApiIgnore
@RestController
@InnerAuth
public class HdfkApiImpl implements HdfkApi{

    @Autowired
    private ITrdHdfkSettleService hdfkSettleService;

    @Autowired
    private ITrdHdfkPayService hdfkPayService;

    @Override
    public CommonResult<List<BranchHdfkOrderRespVO>> getBranchHdfkList(BranchHdfkReqVO branchHdfkReqVO) {
        return CommonResult.success(hdfkSettleService.getBranchHdfkList(branchHdfkReqVO));
    }

    @Override
    public CommonResult<HdfkNoPaymentTotalDTO> getBranchHdfkNoPaymentTotal(BranchHdfkReqVO branchHdfkReqVO) {
        return CommonResult.success(hdfkSettleService.getBranchHdfkNoPaymentTotal(branchHdfkReqVO));
    }

    @Override
    public CommonResult<HdfkPaySaveRespVO> createPay(HdfkPaySaveReqVO paySaveReqVO) {
        return CommonResult.success(hdfkPayService.createPay(paySaveReqVO));
    }

    @Override
    public CommonResult<Boolean> orderPaySuccessCallback(String orderNo, String payPlatform, String payWay) {
        hdfkPayService.orderPaySuccessCallback(orderNo, payPlatform, payWay);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<HdfkPayDTO> getHdfkPayById(Long hdfkPayId) {
        return CommonResult.success(HutoolBeanUtils.toBean(hdfkPayService.getTrdHdfkPay(hdfkPayId),HdfkPayDTO.class));
    }

    @Override
    public CommonResult<List<HdfkPayDTO>> getHdfkPayListByIds(List<Long> hdfkPayIdList) {
        return CommonResult.success(HutoolBeanUtils.toBean(hdfkPayService.getTrdHdfkPayListByIds(hdfkPayIdList),HdfkPayDTO.class));
    }

    @Override
    @Transactional
    public CommonResult<Boolean> addHdfkSettle(Long sysCode,OrderHdfkSettleVO orderHdfkSettleVO) {
        List<TrdHdfkSettle> trdHdfkSettleList = hdfkSettleService.getTrdHdfkSettleList(orderHdfkSettleVO.getSupplierOrderNo());
        if(ToolUtil.isEmpty(trdHdfkSettleList)){
            throw new ServiceException("该订单没有货到付款结算信息");
        }
        List<Long> supplierOrderDtlIdList = new ArrayList<>();
        for (TrdHdfkSettle trdHdfkSettle : trdHdfkSettleList) {
            supplierOrderDtlIdList.add(trdHdfkSettle.getSupplierOrderDtlId());
        }
        HdfkPaySaveReqVO hdfkPaySaveReqVO = new HdfkPaySaveReqVO();
        hdfkPaySaveReqVO.setSupplierOrderDtlIdList(supplierOrderDtlIdList);
        hdfkPaySaveReqVO.setSysCode(sysCode);
        hdfkPaySaveReqVO.setTips(orderHdfkSettleVO.getTips());
        hdfkPaySaveReqVO.setVoucher(orderHdfkSettleVO.getVoucher());

        hdfkPayService.saveThirdPartyTrdHdfkPay(hdfkPaySaveReqVO);
        return CommonResult.success(Boolean.TRUE);
    }
}
