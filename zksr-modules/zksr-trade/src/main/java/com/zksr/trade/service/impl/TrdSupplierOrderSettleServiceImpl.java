package com.zksr.trade.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdSupplierOrderSettleMapper;
import com.zksr.trade.domain.TrdSupplierOrderSettle;
import com.zksr.trade.controller.orderSettle.vo.TrdSupplierOrderSettlePageReqVO;
import com.zksr.trade.controller.orderSettle.vo.TrdSupplierOrderSettleSaveReqVO;
import com.zksr.trade.service.ITrdSupplierOrderSettleService;

import java.math.BigDecimal;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 入驻商订单结算信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Service
public class TrdSupplierOrderSettleServiceImpl implements ITrdSupplierOrderSettleService {
    @Autowired
    private TrdSupplierOrderSettleMapper trdSupplierOrderSettleMapper;

    /**
     * 新增入驻商订单结算信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdSupplierOrderSettle(TrdSupplierOrderSettleSaveReqVO createReqVO) {
        // 插入
        TrdSupplierOrderSettle trdSupplierOrderSettle = HutoolBeanUtils.toBean(createReqVO, TrdSupplierOrderSettle.class);
        trdSupplierOrderSettleMapper.insert(trdSupplierOrderSettle);
        // 返回
        return trdSupplierOrderSettle.getSupplierOrderDtlId();
    }

    /**
     * 修改入驻商订单结算信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdSupplierOrderSettle(TrdSupplierOrderSettleSaveReqVO updateReqVO) {
        trdSupplierOrderSettleMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, TrdSupplierOrderSettle.class));
    }

    /**
     * 删除入驻商订单结算信息
     *
     * @param supplierOrderDtlId 入驻商订单明细id
     */
    @Override
    public void deleteTrdSupplierOrderSettle(Long supplierOrderDtlId) {
        // 删除
        trdSupplierOrderSettleMapper.deleteById(supplierOrderDtlId);
    }

    /**
     * 批量删除入驻商订单结算信息
     *
     * @param supplierOrderDtlIds 需要删除的入驻商订单结算信息主键
     * @return 结果
     */
    @Override
    public void deleteTrdSupplierOrderSettleBySupplierOrderDtlIds(Long[] supplierOrderDtlIds) {
        for(Long supplierOrderDtlId : supplierOrderDtlIds){
            this.deleteTrdSupplierOrderSettle(supplierOrderDtlId);
        }
    }

    /**
     * 获得入驻商订单结算信息
     *
     * @param supplierOrderDtlId 入驻商订单明细id
     * @return 入驻商订单结算信息
     */
    @Override
    public TrdSupplierOrderSettle getTrdSupplierOrderSettle(Long supplierOrderDtlId) {
        return trdSupplierOrderSettleMapper.selectById(supplierOrderDtlId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdSupplierOrderSettle> getTrdSupplierOrderSettlePage(TrdSupplierOrderSettlePageReqVO pageReqVO) {
        return trdSupplierOrderSettleMapper.selectPage(pageReqVO);
    }

    @Override
    public BigDecimal getColonelAppPercentageAmt(Long colonelId) {
        return trdSupplierOrderSettleMapper.getColonelAppPercentageAmt(colonelId);
    }

    private void validateTrdSupplierOrderSettleExists(Long supplierOrderDtlId) {
        if (trdSupplierOrderSettleMapper.selectById(supplierOrderDtlId) == null) {
            throw exception(TRD_SUPPLIER_ORDER_SETTLE_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 入驻商订单结算信息 TODO 补充编号 ==========
    // ErrorCode TRD_SUPPLIER_ORDER_SETTLE_NOT_EXISTS = new ErrorCode(TODO 补充编号, "入驻商订单结算信息不存在");


}
