package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 订单优惠明细对象 trd_order_discount_dtl
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@TableName(value = "trd_order_discount_dtl")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdOrderDiscountDtl extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 主键id */
    @Excel(name = "主键id")
    @TableId(type= IdType.ASSIGN_ID)
    private Long orderDiscountDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 订单id */
    @Excel(name = "订单id")
    private Long orderId;

    /** 门店id */
    @Excel(name = "门店id")
    private Long branchId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    private Long skuId;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 优惠类型（数据字典） */
    @Excel(name = "优惠类型", readConverterExp = "数=据字典")
    private String discountType;

    /** 优惠id */
    @Excel(name = "优惠id")
    private Long discountId;

    /** 优惠名称 */
    @Excel(name = "优惠名称")
    private String discountName;

    /** 活动优惠金额(分摊的)(new) */
    @Excel(name = "活动优惠金额(分摊的)(new)")
    private BigDecimal activityDiscountAmt;

    /** 优惠金额(分摊的)(new) */
    @Excel(name = "优惠金额(分摊的)(new)")
    private BigDecimal couponDiscountAmt;

    /** 优惠金额(不分摊的)(new) */
    @Excel(name = "优惠金额(不分摊的)(new)")
    private BigDecimal couponDiscountAmt2;

    /** 赠品类型;0-商品 1-优惠券 */
    @Excel(name = "赠品类型;0-商品 1-优惠券")
    private Long giftType;

    /** 赠品sku;gift_type=0 则记录;gift_type=1 则记录 */
    @Excel(name = "赠品sku;gift_type=0 则记录;gift_type=1 则记录")
    private Long giftSkuId;

    /** 赠品sku优惠券模板 */
    @Excel(name = "赠品sku优惠券模板")
    private Long giftCouponTemplateId;

    /** 赠品数量 */
    @Excel(name = "赠品数量")
    private Long giftQty;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    private Long supplierOrderId;

    /**
     * 活动优惠规则ID
     */
    private Long discountRuleId;

    @Excel(name = "优惠券模板ID ,用于优惠劵报表")
    private Long couponTemplateId;


    /**
     * 赠品优惠时使用，存放参与此赠品优惠的订单明细编号对应的行号，多个以;分号分隔
     */
    @Excel(name = "赠品优惠时使用，存放参与此赠品优惠的订单明细编号对应的行号，多个以;分号分隔")
    private String orderDtlNumStr;

    /** 赠品单位大小 */
    @Excel(name = "赠品单位大小")
    private Integer giftUnitType;

    /** 赠品单位 */
    @Excel(name = "赠品单位")
    private String giftUnit;

    /** 赠品单位换算数量 */
    @Excel(name = "赠品单位换算数量")
    private Long giftUnitSize ;

    /**
     * 活动满足条件
     * 满足多少金额或数量参与促销 （目前只有满赠、买赠使用）
     */
    @Excel(name = "活动满足条件;满足多少金额或数量参与促销 （目前只有满赠、买赠使用）")
    private String discountCondition;
}
