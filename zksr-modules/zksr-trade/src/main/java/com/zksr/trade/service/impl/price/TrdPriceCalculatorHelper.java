package com.zksr.trade.service.impl.price;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.trade.api.after.vo.OrderAfterResp;
import com.zksr.trade.api.after.vo.OrderAfterSaveRequest;
import com.zksr.trade.convert.after.TrdAfterConvert;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年04月21日 08:39
 * @description: TrdPriceCalculatorHelper
 */
public class TrdPriceCalculatorHelper {
    /**
     *  售后订单 价格计算
     * @param request
     */
    public static OrderAfterResp buildAfterPriceCalculateResp(OrderAfterSaveRequest request){
       OrderAfterResp resp =  TrdAfterConvert.INSTANCE.convert(request);

        resp.setRefundAmt(BigDecimal.ZERO);
        resp.getSupplierOrders().forEach(supplierOrder -> {
            supplierOrder.setSubRefundAmt(BigDecimal.ZERO);
            supplierOrder.getSupplierOrderDtls().forEach(dtl -> {
                dtl.setRefundAmt(dtl.getRefundPrice().multiply(new BigDecimal(dtl.getRefundQty() +"")).setScale(StatusConstants.PRICE_RESERVE_2, BigDecimal.ROUND_HALF_UP));
                supplierOrder.setSubRefundAmt(supplierOrder.getSubRefundAmt().add(dtl.getRefundAmt()));
            });
            resp.setRefundAmt(resp.getRefundAmt().add(supplierOrder.getSubRefundAmt()));
        });
        return resp;
    }
}
