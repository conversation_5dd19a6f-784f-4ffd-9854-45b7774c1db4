package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.domain.TrdAfterLog;
import com.zksr.trade.controller.after.logVo.TrdAfterLogPageReqVO;
import com.zksr.trade.controller.after.logVo.TrdAfterLogSaveReqVO;

/**
 * 售后日志Service接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
public interface ITrdAfterLogService {

    /**
     * 新增售后日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdAfterLog(@Valid TrdAfterLogSaveReqVO createReqVO);

    /**
     * 修改售后日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdAfterLog(@Valid TrdAfterLogSaveReqVO updateReqVO);

    /**
     * 删除售后日志
     *
     * @param afterLogId 售后日志
     */
    public void deleteTrdAfterLog(Long afterLogId);

    /**
     * 批量删除售后日志
     *
     * @param afterLogIds 需要删除的售后日志主键集合
     * @return 结果
     */
    public void deleteTrdAfterLogByAfterLogIds(Long[] afterLogIds);

    /**
     * 获得售后日志
     *
     * @param afterLogId 售后日志
     * @return 售后日志
     */
    public TrdAfterLog getTrdAfterLog(Long afterLogId);

    /**
     * 获得售后日志分页
     *
     * @param pageReqVO 分页查询
     * @return 售后日志分页
     */
    PageResult<TrdAfterLog> getTrdAfterLogPage(TrdAfterLogPageReqVO pageReqVO);

}
