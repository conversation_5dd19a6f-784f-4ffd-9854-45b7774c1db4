package com.zksr.trade.service;

import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.activity.dto.ActivityDTO;
import com.zksr.promotion.api.activity.dto.PrmActivityDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.dto.CourierConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.dto.*;
import com.zksr.system.api.partnerConfig.dto.DeviceSettingConfigDTO;
import com.zksr.system.api.software.dto.SoftwareDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;

/**
 * 缓存Service接口
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
public interface TrdCacheService {
    /**
     * 获取SKU
     *
     * @param skuId
     * @return
     */
    public SkuDTO getSkuDTO(Long skuId);

    /**
     * 获取门店缓存
     *
     * @param branchId
     * @return
     */
    public BranchDTO getBranchDTO(Long branchId);

    /**
     * @Description: 获取入驻商信息
     * @Author: liuxingyu
     * @Date: 2024/4/10 16:40
     */
    SupplierDTO getSupplierDTO(Long supplierId);

    /**
     * @Description: 获取spu信息
     * @Author: liuxingyu
     * @Date: 2024/4/10 16:40
     */
    SpuDTO getSpuDTO(Long supId);

    /**
     * @Description: 获取业务员信息
     * @Author: liuxingyu
     * @Date: 2024/4/10 16:42
     */
    ColonelDTO getColonelDTO(Long colonelId);

    /**
     * @param sysCode 平台ID
     * @return 根据sysCode 获取支付配置
     */
    public PayConfigDTO getPayConfigDTO(Long sysCode);

    /**
     * 获取平台商快递查询设置
     *
     * @param sysCode
     * @return
     */
    public CourierConfigDTO getCourierConfigDTO(Long sysCode);

    /**
     * 获取入驻商芯烨配置
     *
     * @param supplierId
     * @return
     */
    public XpYunSettingPolicyDTO getXpYunSettingPolicyDTO(Long supplierId);

    /**
     * 获取入驻商售后配置信息
     *
     * @param supplierId
     * @return
     */
    public AfterSaleSettingPolicyDTO getAfterSaleSettingPolicy(Long supplierId);

    /**
     * 获取入驻商飞鹅配置
     *
     * @param supplierId
     * @return
     */
    public FeieYunSettingPolicyDTO getFeieYunSettingPolicyDTO(Long supplierId);

    /**
     * @Description: 获取平台商设备配置
     * @Author: liuxingyu
     * @Date: 2024/4/26 9:33
     */
    DeviceSettingConfigDTO getDeviceSettingPolicyDTO(Long sysCode);

    /**
     * 获取优惠劵模板
     *
     * @param couponTemplateId
     * @return
     */
    public CouponTemplateDTO getCouponTemplate(Long couponTemplateId);


    /**
     * 根据DcId查询出运营商订单参数配置
     *
     * @param dcId
     * @return
     */
    public OrderSettingPolicyDTO getOrderSettingPolicyInfo(Long dcId);

    /**
     * 根据入驻商ID查询出商户的开放资源信息
     *
     * @param merchantId
     * @return
     */
    OpensourceDto getOpensourceByMerchantId(Long merchantId);

    /**
     * 移除订单角标统计
     *
     * @param branchId
     */
    void clearOrderTotal(Long branchId);

    /**
     * 根据品牌ID查询品牌信息
     *
     * @param brandId
     * @return
     */
    public BrandDTO getBrandDTO(Long brandId);

    /**
     * 根据分类ID查询分类信息
     *
     * @param catgoryId
     * @return
     */
    public CatgoryDTO getCatgoryDTO(Long catgoryId);

    /**
     * @Description: 获取区域城市信息
     * @Author: liuxingyu
     * @Date: 2024/8/12 15:51
     */
    AreaDTO getByAreaId(Long areaId);

    /**
     * 获取可视化详情
     *
     * @param supplierIdAndTemplate
     * @return
     */
    VisualSettingDetailDto getVisualDetailBySupplier(String supplierIdAndTemplate);

    /**
     * 获取可视化主表
     *
     * @param supplierId
     * @return
     */
    VisualSettingMasterDto getVisualMasterBySupplier(Long supplierId);

    /**
     * 获取member缓存
     *
     * @param memberId
     * @return
     */
    MemberDTO getMemberDTO(Long memberId);

    /**
     * 获取账户
     *
     * @param merchantId   商户ID
     * @param merchantType 商户类型    {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param platform     支付平台    {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return 账户资金情况
     */
    public AccAccountDTO getAccount(Long merchantId, String merchantType, String platform);

    /**
     * @param activityId
     * @return
     */
    PrmActivityDTO getActivityDTO(Long activityId);

    /**
     * 获取平台商信息
     *
     * @param key
     * @return
     */
    PartnerDto getPartnerDto(String key);

    /**
     * @param dcId 运营商ID
     * @return 运营商缓存对象
     */
    public DcDTO getDcDTO(Long dcId);

    ChannelDTO getChannelDTO(Long channelId);


    /**
     * 获取组合商品缓存
     */
    SpuCombineDTO getSpuCombineDTO(Long spuCombineId);

    /**
     * 获取入驻商其他配置
     * @param supplierId 入驻商编号
     * @return
     */
    SupplierOtherSettingPolicyDTO getPartnerSupplierOtherSettingPolicy(Long supplierId);

    /**
     * 获取本地上架商品
     */
    AreaItemDTO getAreaItemDTO(Long areaItemId);

    /**
     * 获取全国上架商品
     */
    SupplierItemDTO getSupplierItemDTO(Long supplierItemId);

    /**
     * 获取全国上架展示分类
     */
    SaleClassDTO getSaleClassDTO(Long saleClassId);

    /**
     * 本地上架分类
     */
    AreaClassDTO getAreaClassDTO(Long saleClassId);
}
