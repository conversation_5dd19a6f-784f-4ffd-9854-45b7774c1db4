package com.zksr.trade.controller.order.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 订单对象 trd_order
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Data
@ApiModel("订单 - trd_order分页 Request VO")
public class TrdOrderSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 订单id */
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 运营商id */
    @Excel(name = "运营商id")
    @ApiModelProperty(value = "运营商id")
    private Long dcId;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty(value = "用户id")
    private Long memberId;

    /** 业务员id */
    @Excel(name = "业务员id")
    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    /** 业务员级别 */
    @Excel(name = "业务员级别")
    @ApiModelProperty(value = "业务员级别")
    private Long colonelLevel;

    /** 父业务员id */
    @Excel(name = "父业务员id")
    @ApiModelProperty(value = "父业务员id")
    private Long pcolonelId;

    /** 父业务员级别 */
    @Excel(name = "父业务员级别")
    @ApiModelProperty(value = "父业务员级别")
    private Long pcolonelLevel;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消 */
    @Excel(name = "支付状态", readConverterExp = "数=据字典sys_pay_state")
    @ApiModelProperty(value = "支付状态")
    private Integer payState;

    /** 支付平台(数据字典);从sys_partner表字段jy_platform获取 */
    @Excel(name = "支付平台(数据字典);从sys_partner表字段jy_platform获取")
    @ApiModelProperty(value = "支付平台(数据字典);从sys_partner表字段jy_platform获取")
    private String platform;

    /** 支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款 */
    @Excel(name = "支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款")
    @ApiModelProperty(value = "支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款")
    private String payWay;

    /** 支付时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "支付时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmt;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

}
