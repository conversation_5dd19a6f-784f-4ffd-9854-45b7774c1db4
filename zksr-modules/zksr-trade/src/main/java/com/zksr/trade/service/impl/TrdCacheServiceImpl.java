package com.zksr.trade.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alicp.jetcache.Cache;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.bean.MemberDTOCacheBean;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.AreaItemApi;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.brand.BrandApi;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.CatgoryApi;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.saleClass.SaleClassApi;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.spuCombine.SpuCombineApi;
import com.zksr.product.api.supplierItem.SupplierItemApi;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.activity.ActivityApi;
import com.zksr.promotion.api.activity.dto.ActivityDTO;
import com.zksr.promotion.api.activity.dto.PrmActivityDTO;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.ChannelApi;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.opensource.OpensourceApi;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.CourierConfigDTO;
import com.zksr.system.api.partnerConfig.dto.DeviceSettingConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.*;
import com.zksr.system.api.software.SoftwareApi;
import com.zksr.system.api.software.dto.SoftwareDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.trade.api.order.vo.OrderStatusVO;
import com.zksr.trade.service.TrdCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 缓存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Service
public class TrdCacheServiceImpl implements TrdCacheService {
    private static final Logger log = LoggerFactory.getLogger(TrdCacheServiceImpl.class);

    @Autowired
    private Cache<String, PartnerDto> partnerDtoCache;

    @Autowired
    private Cache<Long, SkuDTO> skuDTOCache;
    @Autowired
    private Cache<Long, SpuDTO> spuDTOCache;
    @Autowired
    private Cache<Long, BranchDTO> branchDTOCache;
    @Autowired
    private Cache<Long, SupplierDTO> supplierDTOCache;
    @Autowired
    private Cache<Long, ColonelDTO> colonelDTOCache;
    @Autowired
    private Cache<Long, PayConfigDTO> payConfigDTOCache;
    @Autowired
    private Cache<Long, CourierConfigDTO> courierConfigDTOCache;
    @Autowired
    private Cache<Long, XpYunSettingPolicyDTO> xpYunSettingPolicyDTOCache;
    @Autowired
    private Cache<Long, AfterSaleSettingPolicyDTO> afterSaleSettingPolicyDTOCache;
    @Autowired
    private Cache<Long, FeieYunSettingPolicyDTO> feieYunSettingPolicyDTOCache;
    @Autowired
    private Cache<Long, DeviceSettingConfigDTO> deviceSettingPolicyDTOCache;
    @Autowired
    private Cache<Long, CouponTemplateDTO> couponTemplateCache;
    @Autowired
    private Cache<Long, OrderSettingPolicyDTO> orderSettingPolicyCache;

    @Autowired
    private Cache<Long, OpensourceDto> opensourceDtoByMerchantIdCache;

    @Autowired
    private Cache<Long, OrderStatusVO> orderStatusTotalCache;
    @Autowired
    private Cache<Long, BrandDTO> brandDtoCache;
    @Autowired
    private Cache<Long, CatgoryDTO> catgoryDtoCache;
    @Autowired
    private Cache<Long, AreaDTO> areaDTOCache;
    @Autowired
    private Cache<String, AccAccountDTO> accountCache;

    @Autowired
    @Qualifier("visualSettingDetailBySupplierIdCache")
    private Cache<String, VisualSettingDetailDto> visualSettingDetailBySupplierIdCache;

    @Autowired
    @Qualifier("visualSettingMasterBySupplierIdCache")
    private Cache<Long, VisualSettingMasterDto> visualSettingMasterBySupplierIdCache;

    @Autowired
    private Cache<Long, MemberDTO> memberDTOCache;

    @Autowired
    private Cache<Long, PrmActivityDTO> activityDTOCache;

    @Autowired
    private Cache<Long, DcDTO> dcDTOCache;

    @Autowired
    private Cache<Long, ChannelDTO> channelDTOCache;

    @Autowired
    private Cache<Long, SpuCombineDTO> spuCombineCache;


    @Autowired
    private Cache<Long, SupplierOtherSettingPolicyDTO> partnerSupplierOtherSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, AreaItemDTO> areaItemDTOCache;

    @Autowired
    private Cache<Long, SupplierItemDTO> supplierItemDTOCache;

    @Autowired
    private Cache<Long, SaleClassDTO> saleClassDtoCache;

    @Autowired
    private Cache<Long, AreaClassDTO> areaClassDtoCache;

    @Autowired
    private PartnerApi partnerApi;

    @Autowired
    private SkuApi skuApi;
    @Autowired
    private SpuApi spuApi;
    @Autowired
    private BranchApi branchApi;
    @Autowired
    private SupplierApi supplierApi;
    @Autowired
    private ColonelApi colonelApi;
    @Autowired
    private PartnerConfigApi partnerConfigApi;
    @Autowired
    private PartnerPolicyApi partnerPolicyApi;
    @Autowired
    private CouponApi couponApi;
    @Autowired
    private OpensourceApi opensourceApi;
    @Autowired
    private BrandApi brandApi;
    @Autowired
    private CatgoryApi catgoryApi;
    @Autowired
    private AreaApi areaApi;
    @Autowired
    private AccountApi accountApi;
    @Resource
    private MemberApi memberApi;
    @Resource
    private ActivityApi activityApi;
    @Resource
    private DcApi dcApi;

    @Resource
    private ChannelApi channelApi;

    @Resource
    private SpuCombineApi spuCombineApi;

    @Resource
    private AreaItemApi areaItemApi;

    @Resource
    private SupplierItemApi supplierItemApi;

    @Resource
    private AreaClassApi areaClassApi;

    @Resource
    private SaleClassApi saleClassApi;

    @PostConstruct
    public void init() {
        //自动load（read through）
        partnerDtoCache.config().setLoader(this::loadPartnerDtoFromApi);
        skuDTOCache.config().setLoader(this::loadSkuDtoFromApi);
        spuDTOCache.config().setLoader(this::loadSpuDtoFromApi);
        branchDTOCache.config().setLoader(this::loadBranchDtoFromApi);
        supplierDTOCache.config().setLoader(this::loadSupplierDTOCacheFromApi);
        colonelDTOCache.config().setLoader(this::loadColonelDTOCacheFromApi);
        payConfigDTOCache.config().setLoader(this::loadPayConfigDTO);
        courierConfigDTOCache.config().setLoader(this::loadCourierConfigDTO);
        xpYunSettingPolicyDTOCache.config().setLoader(this::loadXpYunSettingDTO);
        feieYunSettingPolicyDTOCache.config().setLoader(this::loadFeieYunSettingDTO);
        afterSaleSettingPolicyDTOCache.config().setLoader(this::loadAfterSaleSettingPolicyDTO);
        deviceSettingPolicyDTOCache.config().setLoader(this::loadDeviceSaleSettingPolicyDTO);
        couponTemplateCache.config().setLoader(this::loadCouponTemplateFromApi);
        orderSettingPolicyCache.config().setLoader(this::loadOrderSettingPolicyFromApi);
        opensourceDtoByMerchantIdCache.config().setLoader(this::loadOpensourceByMerchantIdFromApi);
        brandDtoCache.config().setLoader(this::loadBrandDtoFromApi);
        catgoryDtoCache.config().setLoader(this::loadCatgoryDtoFromApi);
        areaDTOCache.config().setLoader(this::loadAreaDtoFromApi);
        memberDTOCache.config().setLoader(this::loadMemberDTOFromApi);
        visualSettingDetailBySupplierIdCache.config().setLoader(this::loadVisualSettingDetailBySupplierIdFromApi);
        visualSettingMasterBySupplierIdCache.config().setLoader(this::loadVisualSettingMasterBySupplierIdFromApi);
        accountCache.config().setLoader(this::loadAccount);
        activityDTOCache.config().setLoader(this::loadActivityDTOFromApi);
        dcDTOCache.config().setLoader(this::loadDcDTO);
        channelDTOCache.config().setLoader(this::loadChannelDTO);
        spuCombineCache.config().setLoader(this::loadSpuCombineDTOFromApi);
        partnerSupplierOtherSettingPolicyDTOCache.config().setLoader(this::loadPartnerSupplierOtherSettingPolicyDTOFromApi);
        areaItemDTOCache.config().setLoader(this::loadAreaItemDtoFromApi);
        supplierItemDTOCache.config().setLoader(this::loadSupplierItemDtoFromApi);
        saleClassDtoCache.config().setLoader(this::loadSaleClassDtoFromApi);
        areaClassDtoCache.config().setLoader(this::loadAreaClassDtoFromApi);
    }

    private AreaClassDTO loadAreaClassDtoFromApi(Long areaClassId) {
        return areaClassApi.getAreaClassByAreaClassId(areaClassId).getCheckedData();
    }

    private SaleClassDTO loadSaleClassDtoFromApi(Long saleClassId) {
        return saleClassApi.getSaleClassBySaleClassId(saleClassId).getCheckedData();
    }

    private AreaItemDTO loadAreaItemDtoFromApi(Long areaItemId) {return areaItemApi.getAreaItemId(areaItemId).getCheckedData();}

    private SupplierItemDTO loadSupplierItemDtoFromApi(Long supplierItemId){return supplierItemApi.getBySupplierItemId(supplierItemId).getCheckedData();}

    private SupplierOtherSettingPolicyDTO loadPartnerSupplierOtherSettingPolicyDTOFromApi(Long supplierId) {
        return partnerPolicyApi.getPartnerSupplierOtherSettingPolicy(supplierId).getCheckedData();
    }

    private PrmActivityDTO loadActivityDTOFromApi(Long activityId) {
        return activityApi.getActivityDto(activityId).getCheckedData();
    }

    private MemberDTO loadMemberDTOFromApi(Long memberId) {
        return memberApi.getMemBerByMemberId(memberId).getCheckedData();
    }

    private PartnerDto loadPartnerDtoFromApi(String key) {
        if (!NumberUtil.isNumber(key)){
            return partnerApi.getPartnerBySource(key).getCheckedData();
        } else {
            return partnerApi.getBySysCode(Long.valueOf(key)).getCheckedData();
        }
    }

    /**
     * @Description: 获取区域城市信息
     * @Author: liuxingyu
     * @Date: 2024/8/12 15:53
     */
    private AreaDTO loadAreaDtoFromApi(Long areaId) {
        return areaApi.getAreaByAreaId(areaId).getCheckedData();
    }

    /**
     * @Description: 获取平台商设备配置x
     * @Author: liuxingyu
     * @Date: 2024/4/26 9:35
     */
    private DeviceSettingConfigDTO loadDeviceSaleSettingPolicyDTO(Long sysCode) {
        return partnerConfigApi.getDeviceSettingConfig(sysCode).getCheckedData();
    }

    /**
     * @Description: 获取入驻商飞鹅打印配置
     * @Author: liuxingyu
     * @Date: 2024/4/25 15:01
     */
    private FeieYunSettingPolicyDTO loadFeieYunSettingDTO(Long supplierId) {
        return partnerPolicyApi.getFeieYunSettingPolicy(supplierId).getCheckedData();

    }

    /**
     * @Description: 获取平台商芯烨配置缓存
     * @Author: liuxingyu
     * @Date: 2024/4/22 9:09
     */
    private XpYunSettingPolicyDTO loadXpYunSettingDTO(Long supplierId) {
        return partnerPolicyApi.getXpYunSettingPolicy(supplierId).getCheckedData();
    }

    /**
     * @Description: 获取业务员信息
     * @Author: liuxingyu
     * @Date: 2024/4/10 16:43
     */
    private ColonelDTO loadColonelDTOCacheFromApi(Long colonelId) {
        return colonelApi.getByColonelId(colonelId).getCheckedData();
    }

    /**
     * @Description: 获取spu信息
     * @Author: liuxingyu
     * @Date: 2024/4/10 16:39
     */
    private SpuDTO loadSpuDtoFromApi(Long supId) {
        return spuApi.getBySpuId(supId).getCheckedData();
    }

    /**
     * @Description: 根据入驻商ID获取入驻商信息
     * @Author: liuxingyu
     * @Date: 2024/4/10 16:36
     */
    private SupplierDTO loadSupplierDTOCacheFromApi(Long supplierId) {
        return supplierApi.getBySupplierId(supplierId).getCheckedData();
    }

    private SkuDTO loadSkuDtoFromApi(Long skuId) {
        return skuApi.getBySkuId(skuId).getCheckedData();
    }

    private BranchDTO loadBranchDtoFromApi(Long branchId) {
        return branchApi.getByBranchId(branchId).getCheckedData();
    }

    private PayConfigDTO loadPayConfigDTO(Long sysCode) {
        return partnerConfigApi.getPayConfig(sysCode).getCheckedData();
    }

    private CourierConfigDTO loadCourierConfigDTO(Long sysCode) {
        return partnerConfigApi.getCourierConfig(sysCode).getCheckedData();
    }

    private AfterSaleSettingPolicyDTO loadAfterSaleSettingPolicyDTO(Long supplierId) {
        return partnerPolicyApi.getAfterSaleSettingPolicy(supplierId).getCheckedData();
    }

    private CouponTemplateDTO loadCouponTemplateFromApi(Long couponTemplateId) {
        return couponApi.getCouponTemplate(couponTemplateId).getCheckedData();
    }

    private OrderSettingPolicyDTO loadOrderSettingPolicyFromApi(Long dcId) {
        return partnerPolicyApi.getOrderSettingPolicy(dcId).getCheckedData();
    }

    private OpensourceDto loadOpensourceByMerchantIdFromApi(Long merchantId) {
        OpensourceDto opensourceDto = opensourceApi.getOpensourceByMerchantId(merchantId).getCheckedData();
        if (Objects.isNull(opensourceDto)) {
            return new OpensourceDto();
        }
        return opensourceDto;
    }

    private BrandDTO loadBrandDtoFromApi(Long brandId) {
        return brandApi.getBrandByBrandId(brandId).getCheckedData();
    }

    private CatgoryDTO loadCatgoryDtoFromApi(Long catgoryId) {
        return catgoryApi.getCatgoryByCatgoryId(catgoryId).getCheckedData();
    }

    private VisualSettingDetailDto loadVisualSettingDetailBySupplierIdFromApi(String supplierIdAndTemplate){
        VisualSettingDetailDto visualSettingDetailDto = opensourceApi.getVisualSettingDetailByMerchantId(supplierIdAndTemplate).getCheckedData();
        if (Objects.isNull(visualSettingDetailDto)) {
            return new VisualSettingDetailDto();
        }
        return visualSettingDetailDto;
    }

    private VisualSettingMasterDto loadVisualSettingMasterBySupplierIdFromApi(Long supplierId) {
        VisualSettingMasterDto visualSettingMasterDto = opensourceApi.getVisualSettingMasterByMerchantId(supplierId).getCheckedData();
        if (Objects.isNull(visualSettingMasterDto)) {
            return new VisualSettingMasterDto();
        }
        return visualSettingMasterDto;
    }

    private AccAccountDTO loadAccount(String cacheKey){
        String[] data = cacheKey.split(StringPool.COLON);
        Long merchantId = Long.parseLong(data[0]);
        String merchantType = data[1];
        String platform = data[2];
        AccAccountDTO accountDTO = accountApi.getAccount(
                Long.parseLong(data[0]),
                merchantType,
                platform
        ).getCheckedData();
        if (Objects.isNull(accountDTO)) {
            accountDTO = new AccAccountDTO();
            accountDTO.setWithdrawableAmt(BigDecimal.ZERO)
                    .setFrozenAmt(BigDecimal.ZERO)
                    .setCreditAmt(BigDecimal.ZERO)
            ;
        }
        return accountDTO;
    }


    @Override
    public SkuDTO getSkuDTO(Long skuId) {
        return skuDTOCache.get(skuId);
    }

    @Override
    public BranchDTO getBranchDTO(Long branchId) {
        return branchDTOCache.get(branchId);
    }

    /**
     * @Description: 获取入驻商信息
     * @Author: liuxingyu
     * @Date: 2024/4/10 16:38
     */
    @Override
    public SupplierDTO getSupplierDTO(Long supplierId) {
        return supplierDTOCache.get(supplierId);
    }

    /**
     * @Description: 获取Spu信息
     * @Author: liuxingyu
     * @Date: 2024/4/10 16:41
     */
    @Override
    public SpuDTO getSpuDTO(Long supId) {
        return spuDTOCache.get(supId);
    }

    /**
     * @Description: 获取业务员信息
     * @Author: liuxingyu
     * @Date: 2024/4/10 16:44
     */
    @Override
    public ColonelDTO getColonelDTO(Long colonelId) {
        return colonelDTOCache.get(colonelId);
    }

    @Override
    public PayConfigDTO getPayConfigDTO(Long sysCode) {
        return payConfigDTOCache.get(sysCode);
    }

    @Override
    public CourierConfigDTO getCourierConfigDTO(Long sysCode) {
        return courierConfigDTOCache.get(sysCode);
    }

    /**
     * @Description: 获取芯烨配置缓存
     * @Author: liuxingyu
     * @Date: 2024/4/22 9:11
     */
    @Override
    public XpYunSettingPolicyDTO getXpYunSettingPolicyDTO(Long supplierId) {
        return xpYunSettingPolicyDTOCache.get(supplierId);
    }

    /**
     * @Description: 获取飞鹅配置缓存
     * @Author: liuxingyu
     * @Date: 2024/4/25 15:00
     */
    @Override
    public FeieYunSettingPolicyDTO getFeieYunSettingPolicyDTO(Long supplierId) {
        return feieYunSettingPolicyDTOCache.get(supplierId);
    }

    /**
     * @Description: 获取平台商设备配置
     * @Author: liuxingyu
     * @Date: 2024/4/26 9:34
     */
    @Override
    public DeviceSettingConfigDTO getDeviceSettingPolicyDTO(Long sysCode) {
        return deviceSettingPolicyDTOCache.get(sysCode);
    }

    @Override
    public AfterSaleSettingPolicyDTO getAfterSaleSettingPolicy(Long supplierId) {
        if (Objects.isNull(supplierId)) {
            return new AfterSaleSettingPolicyDTO();
        }
        return afterSaleSettingPolicyDTOCache.get(supplierId);
    }

    @Override
    public CouponTemplateDTO getCouponTemplate(Long couponTemplateId) {
        return couponTemplateCache.get(couponTemplateId);
    }

    @Override
    public OrderSettingPolicyDTO getOrderSettingPolicyInfo(Long dcId) {
        if (Objects.isNull(dcId)) {
            return new OrderSettingPolicyDTO();
        }
        OrderSettingPolicyDTO orderSettingPolicyDTO = orderSettingPolicyCache.get(dcId);
        if (ToolUtil.isEmpty(orderSettingPolicyDTO)) {
            orderSettingPolicyDTO = new OrderSettingPolicyDTO();
        }
        return orderSettingPolicyDTO;
    }

    @Override
    public OpensourceDto getOpensourceByMerchantId(Long merchantId) {
        return opensourceDtoByMerchantIdCache.get(merchantId);
    }

    @Override
    public void clearOrderTotal(Long branchId) {
        orderStatusTotalCache.remove(branchId);
    }

    @Override
    public BrandDTO getBrandDTO(Long brandId) {
        // 如果品牌不存在, 或者小于等于0, 就不查了直接返回null
        if (Objects.isNull(brandId) || brandId <= NumberPool.LONG_ZERO) {
            return null;
        }
        return brandDtoCache.get(brandId);
    }

    @Override
    public CatgoryDTO getCatgoryDTO(Long catgoryId) {
        return catgoryDtoCache.get(catgoryId);
    }

    @Override
    public AreaDTO getByAreaId(Long areaId) {
        return areaDTOCache.get(areaId);
    }

    @Override
    public VisualSettingDetailDto getVisualDetailBySupplier(String supplierIdAndTemplate) {
        return visualSettingDetailBySupplierIdCache.get(supplierIdAndTemplate);
    }

    @Override
    public VisualSettingMasterDto getVisualMasterBySupplier(Long supplierId) {
        return visualSettingMasterBySupplierIdCache.get(supplierId);
    }

    @Override
    public MemberDTO getMemberDTO(Long memberId) {
        return memberDTOCache.get(memberId);
    }

    @Override
    public AccAccountDTO getAccount(Long merchantId, String merchantType, String platform) {
        return accountCache.get(RedisConstants.getAccountKey(merchantId, merchantType, platform));
    }

    @Override
    public PrmActivityDTO getActivityDTO(Long activityId) {
        if (Objects.isNull(activityId)) {
            return null;
        }
        return activityDTOCache.get(activityId);
    }

    @Override
    public PartnerDto getPartnerDto(String key) {
        return partnerDtoCache.get(key);
    }

    @Override
    public DcDTO getDcDTO(Long dcId) {
        return dcDTOCache.get(dcId);
    }

    private DcDTO loadDcDTO(Long dcId){
        return dcApi.getDcById(dcId).getCheckedData();
    }

    public ChannelDTO getChannelDTO(Long channelId){
        return channelDTOCache.get(channelId);
    }

    private ChannelDTO loadChannelDTO(Long channelId){
        return channelApi.getByChannelId(channelId).getCheckedData();
    }

    private SpuCombineDTO loadSpuCombineDTOFromApi(Long spuCombineId) {
        return spuCombineApi.getSpuCombine(spuCombineId).getCheckedData();
    }


    @Override
    public SpuCombineDTO getSpuCombineDTO(Long spuCombineId) {
        if (Objects.isNull(spuCombineId)) {
            return null;
        }
        return spuCombineCache.get(spuCombineId);
    }

    @Override
    public SupplierOtherSettingPolicyDTO getPartnerSupplierOtherSettingPolicy(Long supplierId) {
        if (Objects.isNull(supplierId)) {
            return null;
        }
        return partnerSupplierOtherSettingPolicyDTOCache.get(supplierId);
    }

    @Override
    public AreaItemDTO getAreaItemDTO(Long areaItemId) {
        return areaItemDTOCache.get(areaItemId);
    }

    @Override
    public SupplierItemDTO getSupplierItemDTO(Long supplierItemId) {
        return supplierItemDTOCache.get(supplierItemId);
    }

    @Override
    public SaleClassDTO getSaleClassDTO(Long saleClassId) {
        if (Objects.isNull(saleClassId)) {
            return null;
        }
        return saleClassDtoCache.get(saleClassId);
    }

    @Override
    public AreaClassDTO getAreaClassDTO(Long saleClassId) {
        if (Objects.isNull(saleClassId)) {
            return null;
        }
        return areaClassDtoCache.get(saleClassId);
    }
}
