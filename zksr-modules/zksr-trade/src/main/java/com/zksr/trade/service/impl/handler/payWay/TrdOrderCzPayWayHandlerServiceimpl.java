package com.zksr.trade.service.impl.handler.payWay;

import com.alibaba.fastjson2.JSON;
import com.zksr.account.api.pay.PayApi;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.account.model.pay.vo.PayWxB2bCreateDivideReqVO;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.OrderPayWayEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.dto.OrderPayInfoRespDTO;
import com.zksr.trade.api.order.dto.TrdSupplierResDto;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdSupplierPageVO;
import com.zksr.trade.domain.TrdSettle;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.mapper.TrdOrderMapper;
import com.zksr.trade.mapper.TrdSettleMapper;
import com.zksr.trade.mapper.TrdSupplierOrderDtlMapper;
import com.zksr.trade.service.handler.payWay.ITrdOrderPayWayHandlerService;
import com.zksr.trade.service.price.ITrdPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 储值 支付方式处理实现 - 在线支付
 */
@Service
@Slf4j
@Order(ITrdOrderPayWayHandlerService.ORDER_CZ_ONLINE)
public class TrdOrderCzPayWayHandlerServiceimpl implements ITrdOrderPayWayHandlerService {

    @Autowired
    private TrdSettleMapper trdSettleMapper;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;

    @Autowired
    private TrdOrderMapper trdOrderMapper;

    @Autowired
    private ITrdPriceService trdPriceService;


    @Autowired
    private PayApi payApi;



    @Override
    public Boolean isPlatform(String platform, String payWay) {
        return Objects.equals(PayChannelEnum.WALLET.getCode(), platform) && Objects.equals(OrderPayWayEnum.WALLET.getPayWay(), payWay);
    }

    @Override
    public Boolean isPlatform(String platform) {
        return Objects.equals(PayChannelEnum.WALLET.getCode(), platform);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderCompleteCreateSettleTransfer(TrdOrder tor, List<TrdSupplierOrder> tsoList, List<TrdSupplierOrderDtl> tsodList, List<TrdSettle> settleLists) {
        boolean isAllComplete = tsodList.stream()
                .filter(dtl -> !Objects.equals(dtl.getDeliveryState(), DeliveryStatusEnum.CANCEL.getCode()))
                .allMatch(dtl -> Objects.equals(dtl.getDeliveryState(), DeliveryStatusEnum.COMPLETE.getCode()));
        // 全部商品已全部完成
        if (isAllComplete){
            settleLists.stream()
                    // 储值支付 只需要分账给 业务员一个角色，其他分账方不需要
                    .filter(settle -> Objects.equals(settle.getMerchantType(), MerchantTypeEnum.COLONEL.getType()))
                    .collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
                    .forEach((merchantId, settleList) -> {
                        PayWxB2bCreateDivideReqVO reqVO =
                                new PayWxB2bCreateDivideReqVO(tor.getOrderNo(), settleList.get(NumberPool.INT_ZERO).getMerchantId(), settleList.get(NumberPool.INT_ZERO).getMerchantType());
                        // 发送分润账户分账请求
                        log.info("发送订单【{}】，发送储值支付分润账户分账请求:{}", tor.getOrderNo(), JSON.toJSONString(reqVO));
                        CreateDivideRespVO respVO = payApi.divide(reqVO).getCheckedData();
                        if (respVO.isSuccess()) {
                            settleList.forEach(settle -> {
                                settle.setState(StatusConstants.SETTLE_STATE_2); // 更新结算状态为结算中
                            });
                        }
                        log.info("发送订单【{}】，发送储值支付分润账户分账请求返回结果:{}", tor.getOrderNo(), JSON.toJSONString(respVO));
                        trdSettleMapper.updateBatch(settleList);
                    });
        }
    }

    @Override
    public void orderSettleAccountInfo(List<TrdSupplierResDto> payInfoList, Map<Long, List<TrdSupplierOrderDtl>> supplierItemMap,
                                                          TrdSupplierPageVO pageVo, List<TrdSettle> settles, OrderPayInfoRespDTO respDTO) {
        TrdOrder tor = trdOrderMapper.selectById(respDTO.getOrderId());
        // 计算储值支付本金支付金额、储值赠金支付金额
        trdPriceService.calculatorCzPayWayGiveAmt(tor, pageVo);
        respDTO.setWalletGiveAmt(tor.getCzGivePayAmt());

        payInfoList.stream().collect(Collectors.toMap(TrdSupplierResDto::getSupplierOrderId, item -> item)).forEach((supplierOrderId, supplierResDTO) -> {
            if (supplierItemMap.containsKey(supplierOrderId)) {
                List<String> itemList = supplierItemMap.get(supplierOrderId).stream()
                        .map(item ->
                                StringUtils.format("{}", item.getSpuName().trim())
                        ).collect(Collectors.toList());

                settles.stream()
                        // 储值支付 只需要分账给 业务员一个角色，其他分账方不需要
                        .filter(settle -> Objects.equals(supplierResDTO.getSupplierId(), settle.getSupplierId()) && Objects.equals(settle.getMerchantType(), MerchantTypeEnum.COLONEL.getType()))
                        .collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
                        .forEach((merchantId, settleList) -> {
                            TrdSupplierResDto supplierResDto = TrdSupplierResDto.builder()
                                    .subOrderNo(pageVo.getOrderNo())
                                    .subPayFee(BigDecimal.ZERO)
                                    .platform(pageVo.getStoreOrderPayPlatform())
                                    .merchantId(settleList.get(NumberPool.INT_ZERO).getMerchantId())
                                    .merchantType(settleList.get(NumberPool.INT_ZERO).getMerchantType())
                                    .itemInfo(StringUtils.join(itemList, StringPool.COMMA))
                                    .build();
                            BigDecimal settleAmt = settleList.stream().map(TrdSettle::getSettleAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                            supplierResDto.setSubOrderAmt(settleAmt);
                            if (settleAmt.compareTo(BigDecimal.ZERO) > NumberPool.INT_ZERO) {
                                respDTO.getSupplierList().add(supplierResDto);
//                                // 入驻商分账金额 - 分账方金额
//                                supplierResDTO.setSubOrderAmt(supplierResDTO.getSubOrderAmt().subtract(settleAmt));
                            }
                        });
            }
        });
    }
}
