package com.zksr.trade.controller.app.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 品牌商首页销售数据列表请求 - response vo
 * @date 2024/8/6 14:17
 */
@Data
@ApiModel(description = "品牌商首页销售数据列表响应数据")
public class BrandHomeSaleListRespVO {

    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("品牌ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long brandId;

    @ApiModelProperty("销售订单数")
    private Long saleOrder;

    @ApiModelProperty("销售金额")
    private BigDecimal saleAmt;

    @ApiModelProperty("售后单数")
    private Long afterOrder;

    @ApiModelProperty("售后金额")
    private BigDecimal afterAmt;

}
