package com.zksr.trade.controller.hdfk.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 货到付款结算对象 trd_hdfk_settle
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@ApiModel("货到付款结算 - trd_hdfk_settle分页 Request VO")
public class TrdHdfkSettleSaveReqVO {
    private static final long serialVersionUID = 1L;


    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id", required = true)
    private Long supplierId;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 订单id */
    @Excel(name = "订单id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    /** 全国商品订单编号 */
    @Excel(name = "全国商品订单编号")
    @ApiModelProperty(value = "全国商品订单编号")
    private String supplierOrderNo;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    @ApiModelProperty(value = "入驻商订单id")
    private Long supplierOrderId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    @ApiModelProperty(value = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    @ApiModelProperty(value = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 售后编号;仅售后写 */
    @Excel(name = "售后编号;仅售后写")
    @ApiModelProperty(value = "售后编号;仅售后写")
    private String supplierAfterDtlNo;

    /** 售后单id;仅售后写 */
    @Excel(name = "售后单id;仅售后写")
    @ApiModelProperty(value = "售后单id;仅售后写")
    private Long supplierAfterDtlId;

    /** 0-订单  1-售后 */
    @Excel(name = "0-订单  1-售后")
    @ApiModelProperty(value = "0-订单  1-售后", required = true)
    private Integer settleType;

    /** 应结算金额 */
    @Excel(name = "应结算金额")
    @ApiModelProperty(value = "应结算金额", required = true)
    private BigDecimal settleAmt;

    /** 结算状态 0=未结算，1=已结算 */
    @Excel(name = "结算状态 0=未结算，1=已结算")
    private Integer state;

    /** 结算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结算时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date settleTime;

    /** 货到付款付款单号;生成付款单后反写 */
    @Excel(name = "货到付款付款单号;生成付款单后反写")
    private String hdfkPayNo;

    /** 货到付款付款单明细;生成付款单后反写 */
    @Excel(name = "货到付款付款单明细;生成付款单后反写")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long fdfkPayDtlId;
}
