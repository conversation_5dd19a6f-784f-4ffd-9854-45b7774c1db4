package com.zksr.trade.controller.after.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@Builder
@ApiModel("售后单对应订单入驻商数据 Response DTO")
public class AfterOrderRespDTO {

    @ApiModelProperty(value = "订单单号")
    private String orderNo;

    @ApiModelProperty(value = "入驻商订单单号")
    private String supplierOrderNo;

    @ApiModelProperty(value = "订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderCreateTime;

    @ApiModelProperty(value = "付款方式（数据字典KEY：sys_pay_way）")
    private  Long payWay;

    @ApiModelProperty(value = "入驻商订单实付金额")
    private BigDecimal subPayAmt;

    @ApiModelProperty(value = "入驻商订单金额")
    private BigDecimal subOrderAmt;

    @ApiModelProperty(value = "入驻商订单优惠金额")
    private BigDecimal subDiscountAmt;

    @ApiModelProperty("订单商户支付流水号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long payFlowId;

    @ApiModelProperty(value = "入驻商订单门店余额支付金额")
    private BigDecimal subPayBalanceAmt;

}
