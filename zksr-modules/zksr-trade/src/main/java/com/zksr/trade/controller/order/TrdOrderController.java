package com.zksr.trade.controller.order;

import com.github.pagehelper.Page;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.trade.api.order.vo.*;
import com.zksr.system.api.supplier.vo.SupplierOrderVO;
import com.zksr.trade.api.express.dto.ExpressResDTO;
import com.zksr.trade.controller.order.vo.*;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressReqVO;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressSaveReqVO;
import com.zksr.trade.controller.orderExpressImport.dto.TrdExportOrderDtlDTO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExportOrderDtlVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlSaveReqVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportSaveReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderPageReqVO;
import com.zksr.trade.domain.TrdExpressImport;
import com.zksr.trade.mq.TradeMqProducer;
import com.zksr.trade.print.PrintBean;
import com.zksr.trade.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.trade.enums.ErrorCodeConstants.TRD_EXPRESS_IMPORT_NULL;

/**
 * 订单Controller
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Slf4j
@Api(tags = "管理后台 - 订单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/order")
public class TrdOrderController {
    @Autowired
    private ITrdOrderService trdOrderService;
    @Autowired
    private ITrdSupplierOrderDtlService trdSupplierOrderDtlService;
    @Autowired
    private ITrdExpressImportService trdExpressImportService;
    @Autowired
    private TradeMqProducer tradeMqProducer;
    @Autowired
    private ITrdExpressImportDtlService trdExpressImportDtlService;
    @Autowired
    private ITrdOrderExpressService trdOrderExpressService;
    @Autowired
    private PrintBean printBean;
    @Autowired
    private ITrdSupplierOrderService trdSupplierOrderService;


    /**
     * @Description: 获取运营商订单管理
     * @Param: DcOrderPageReqVO
     * @return: CommonResult<PageResult < TrdOrderRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/4/9 8:55
     */
    @ApiOperation(value = "获取运营商订单管理", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + "trade:order:getOperatorOrderPageList")
    @RequiresPermissions("trade:order:getOperatorOrderPageList")
    @PostMapping("/getOperatorOrderPageList")
    public CommonResult<PageResult<DcOrderPageRespVO>> getOperatorOrderPageList(@RequestBody DcOrderPageReqVO dcOrderPageReqVO) {
        return success(trdOrderService.getOperatorOrderPageList(dcOrderPageReqVO));
    }

    /**
     * PC订单查询（新）
     * @param dcOrderPageReqVO
     * @return
     */
    @ApiOperation(value = "获取运营商订单管理(新)", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + "trade:order:getOperatorOrderPageList")
    @RequiresPermissions("trade:order:getOperatorOrderPageList")
    @PostMapping("/getOperatorOrderPageListNew")
    @DataScope(dcAlias = "tor", dcFieldAlias = SystemConstants.DC_ID, supplierAlias = "tsord")
    public CommonResult<PageResult<DcSupplierOrderPageRespVO>> getOperatorOrderPageListNew(@RequestBody DcOrderPageReqVO dcOrderPageReqVO) {
        return success(trdOrderService.getOperatorOrderPageListNew(dcOrderPageReqVO));
    }

    /**
     * PC订单调用
     * @param dcOrderPageReqVO
     * @return
     */
    @ApiOperation(value = "获取运营商订单管理(新) 打印数据", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + "trade:order:getOperatorOrderPageList")
    @RequiresPermissions("trade:order:getOperatorOrderPageList")
    @PostMapping("/getOperatorOrderPageListNewPrint")
    public CommonResult<List<PcOrderPrintMasterVO>> getOperatorOrderPageListNewPrint(@RequestBody DcOrderPageReqVO dcOrderPageReqVO) {
        return success(trdOrderService.getOperatorOrderPageListNewPrint(dcOrderPageReqVO));
    }

    @ApiOperation(value = "获取运营商订单详情(入驻商订单维度)", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + "trade:order:getOperatorOrderPageList")
    @RequiresPermissions("trade:order:getOperatorOrderPageList")
    @GetMapping("/getOperatorOrderInfoDetail")
    public CommonResult<DcSupplierOrderPageRespVO> getOperatorOrderInfoDetail(@RequestParam("supplierOrderId") Long supplierOrderId, @RequestParam("deliveryState") Integer deliveryState) {
        return success(trdOrderService.getOperatorOrderInfoDetail(supplierOrderId, deliveryState));
    }


    /**
     * 新增订单
     */
    @ApiOperation(value = "新增订单", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdOrderSaveReqVO createReqVO) {
        return success(trdOrderService.insertTrdOrder(createReqVO));
    }

    /**
     * 修改订单
     */
    @ApiOperation(value = "修改订单", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdOrderSaveReqVO updateReqVO) {
        trdOrderService.updateTrdOrder(updateReqVO);
        return success(true);
    }

    /**
     * 删除订单
     */
    @ApiOperation(value = "删除订单", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] orderIds) {
        trdOrderService.deleteTrdOrderByOrderIds(orderIds);
        return success(true);
    }

    /**
     * 获取订单详细信息
     */
    @ApiOperation(value = "获得订单详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{orderId}")
    public CommonResult<TrdOrderRespVO> getInfo(@PathVariable("orderId") Long orderId) {
        TrdOrder trdOrder = trdOrderService.getTrdOrder(orderId);
        return success(HutoolBeanUtils.toBean(trdOrder, TrdOrderRespVO.class));
    }

    /**
     * 分页查询订单
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得订单分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdOrderRespVO>> getPage(@Valid TrdOrderPageReqVO pageReqVO) {
        PageResult<TrdOrder> pageResult = trdOrderService.getTrdOrderPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, TrdOrderRespVO.class));
    }


    /**
     * 获取供应商下全国订单未发货的单据日期
     */
    @GetMapping("/getNotDeliveryNationwideOrderDate")
    @ApiOperation(value = "获取供应商下全国订单未发货的单据日期", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<List<String>> getNotDeliveryNationwideOrderDate() {
        return success(trdSupplierOrderDtlService.getNotDeliveryNationwideOrderDate(SecurityUtils.getSupplierId()));
    }


    /**
     * 导出选择数据的订单信息
     */
    @PostMapping("/exportSupplierOrderDtl")
    @ApiOperation(value = "导出选择数据的订单信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDEREXPRESSEXPORT)
    @RequiresPermissions(Permissions.ORDEREXPRESSEXPORT)
    public void exportSupplierOrderDtl(HttpServletResponse response, TrdExportOrderDtlVO expressVo) {
        expressVo.setSupplierId(SecurityUtils.getSupplierId());
        List<TrdExportOrderDtlDTO> list = trdSupplierOrderDtlService.getNotDeliveryNationwideOrderDtl(expressVo);
        ExcelUtil<TrdExportOrderDtlDTO> util = new ExcelUtil<>(TrdExportOrderDtlDTO.class);
        util.exportExcel(response, list, "orderExpress");
    }

    /**
     * 订单明细数据导出（页面查询数据）
     */
    @PostMapping("/exportSupplierOrderInfo")
    @ApiOperation(value = "入驻商订单数据导出", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDEREXPRESSEXPORT)
    @RequiresPermissions(Permissions.ORDEREXPRESSEXPORT)
    @DataScope(dcAlias = "tor", dcFieldAlias = SystemConstants.DC_ID, supplierAlias = "tsord")
    public void exportSupplierOrderInfo(HttpServletResponse response, DcOrderPageReqApiVO dcOrderPageReqVO) {
        List<SupplierOrderDtlInfoExportVO> list = trdOrderService.getSupplierOrderDtlInfoExport(dcOrderPageReqVO);
        ExcelUtil<SupplierOrderDtlInfoExportVO> util = new ExcelUtil<>(SupplierOrderDtlInfoExportVO.class);
        util.exportExcel(response, list, "入驻商订单导出");
    }


    @ApiOperation(value = "导入入驻商订单快递信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDEREXPRESSIMPORT)
    @RequiresPermissions(Permissions.ORDEREXPRESSIMPORT)
    @Log(title = "导入入驻商订单快递信息", businessType = BusinessType.INSERT)
    @PostMapping("/importSupplierOrderExpress")
    public CommonResult<Boolean> importSupplierOrderExpress(@RequestBody TrdExpressImportSaveReqVO createReqVO) {
        log.info("文件名称{},文件地址{}", createReqVO.getFileName(), createReqVO.getFileUrl());
        ExcelUtil<TrdExportOrderDtlDTO> util = new ExcelUtil<>(TrdExportOrderDtlDTO.class);
        List<TrdExportOrderDtlDTO> orderExportList = util.getImportExcelData(createReqVO.getFileUrl());
        if (ToolUtil.isEmpty(orderExportList) || orderExportList.size() == 0) {
            throw exception(TRD_EXPRESS_IMPORT_NULL);
        }
        log.info("/order/importSupplierOrderExpress接口导入行数：" + orderExportList.size());
        // 新增导入信息数据主表
        createReqVO.setSupplierId(SecurityUtils.getSupplierId());
        TrdExpressImport trdExpressImport = trdExpressImportService.insertTrdExpressImport(createReqVO);
        try {
            orderExportList.stream().forEach(orderExportDtl -> {
                if (ToolUtil.isEmpty(orderExportDtl.getSupplierOrderDtlNo()))
                    return;
                // 新增导入信息明细
                TrdExpressImportDtlSaveReqVO importDtl = new TrdExpressImportDtlSaveReqVO();
                importDtl.setSupplierOrderDtlNo(orderExportDtl.getSupplierOrderDtlNo());
                importDtl.setExpressNo(orderExportDtl.getExpressNo());
                importDtl.setExpressCom(orderExportDtl.getExpressCom());
                importDtl.setExpressComNo(orderExportDtl.getExpressComNo());
                importDtl.setExpressImportId(trdExpressImport.getExpressImportId());

                // 当上传的数据没有快递单号、 快递公司、快递公司编码 时，直接将状态更新为失败
//                if (ToolUtil.isEmpty(importDtl.getExpressNo()) || ToolUtil.isEmpty(importDtl.getExpressCom()) || ToolUtil.isEmpty(importDtl.getExpressComNo())) {
//                    importDtl.setFailReason("当前数据无快递单号或快递公司或快递公司编码！");
//                    importDtl.setStatus(StatusConstants.STATUS_FAIL);
//                    trdExpressImport.setFailNum(trdExpressImport.getFailNum() + 1);
//                    trdExpressImport.setMqReceiveNum(trdExpressImport.getMqReceiveNum() + 1);
//                }

                Long importDtlId = trdExpressImportDtlService.insertTrdExpressImportDtl(importDtl);

                trdExpressImport.setMqSendNum(trdExpressImport.getMqSendNum() + 1);
                trdExpressImport.setTotalNum(trdExpressImport.getTotalNum() + 1);
                trdExpressImportService.updateTrdExpressImport(trdExpressImport);


                //发送mq发货  当状态为空时才进入
                if (ToolUtil.isEmpty(importDtl.getStatus())) {
                    importDtl.setExpressImportDtlId(importDtlId);
                    tradeMqProducer.sendSupplierOrderEvent(importDtl);
                }

            });
        } catch (Exception e) {
            log.error("批量发货失败ordOrderExpressLog｛｝" + trdExpressImport + "报错信息：" + e.getMessage(), e);
        }
        return success(Boolean.TRUE);
    }


    /**
     * 查看快递物流信息
     *
     * @param phone            手机号
     * @param courierCompanyNo 快递公司编码
     * @param courierNumber    快递单号
     * @return
     */
    @ApiOperation(value = "查看快递物流信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @GetMapping("/getExpressInfoByCourier")
    public CommonResult<ExpressResDTO> getExpressInfoByCourier(@RequestParam(value = "phone") String phone,
                                                               @RequestParam(value = "courierCompanyNo") String courierCompanyNo,
                                                               @RequestParam(value = "courierNumber") String courierNumber) {
        return success(trdOrderExpressService.getExpressInfoByCourier(phone, courierCompanyNo, courierNumber));
    }

    /**
     * 修改快递单号
     *
     * @param reqVO
     * @return
     */
    @ApiOperation(value = "修改快递单号", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @Log(title = "修改快递单号", businessType = BusinessType.UPDATE)
    @PostMapping("/editOrderExpressByOrderExpressId")
    public CommonResult<Boolean> editOrderExpressByOrderExpressId(@RequestBody TrdOrderExpressReqVO reqVO) {
        trdOrderExpressService.updateOrderExpressByOrderExpressId(reqVO);
        return success(Boolean.TRUE);
    }

    @ApiOperation(value = "保存单个商品快递单号", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @Log(title = "保存单个商品快递单号", businessType = BusinessType.INSERT)
    @RequiresPermissions(Permissions.LIST)
    @PostMapping("/saveOrderExpress")
    public CommonResult<Boolean> saveOrderExpress(@RequestBody TrdOrderExpressSaveReqVO reqSaveVO) {
        trdOrderExpressService.saveOrderExpress(reqSaveVO);
        return success(Boolean.TRUE);
    }



    /**
     * 订单打印
     * @param reqVO
     * @return
     */
    @ApiOperation(value = "订单打印", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER_PRINT)
    @RequiresPermissions(Permissions.ORDER_PRINT)
    @PostMapping("/orderPrint")
    public CommonResult<Boolean> orderPrint(@RequestBody TrdSupplierOrderPageReqVO reqVO) {
        printBean.print(reqVO.getOrderId(), reqVO.getSysCode(), reqVO.getSupplierOrderId());
        return success(Boolean.TRUE);
    }

    /**
     * 订单手动推送第三方
     * @param supplierOrderNoList
     * @return
     */
    @ApiOperation(value = "订单手动推送第三方", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER_SYNC)
    @RequiresPermissions(Permissions.ORDER_SYNC)
    @Log(title = "订单手动推送第三方", businessType = BusinessType.INSERT)
    @PostMapping("/orderSync")
    public CommonResult<String> orderSync(@RequestBody List<String> supplierOrderNoList) {
        return success(trdSupplierOrderService.batchSyncSupplierOrder(supplierOrderNoList));
    }

    /**
     * 校验订单是否是手动推送第三方
     * @param
     * @return
     */
    @ApiOperation(value = "校验订单是否是手动推送第三方", httpMethod = HttpMethod.GET)
    @GetMapping("/checkOrderSync")
    public CommonResult<Boolean> checkOrderSync() {
        return success(trdSupplierOrderService.checkOrderSync());
    }


    @ApiOperation(value = "调整入驻商订单备注", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SUPPLIERORDER_MEMO_EDIT)
    @RequiresPermissions(Permissions.SUPPLIERORDER_MEMO_EDIT)
    @PostMapping("/supplierOrderMemoEdit")
    public CommonResult<Boolean> supplierOrderMemoEdit(@RequestBody SupplierMemoEditReqVO reqVO) {
        trdSupplierOrderService.supplierOrderMemoEdit(reqVO);
        return success(Boolean.TRUE);
    }

    /**
     * 入驻商订单发货出库（PC）
     */
    @ApiOperation(value = "入驻商订单发货出库（PC发货，本地订单）", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SUPPLIERORDER_OUTBOUND)
    @RequiresPermissions(Permissions.SUPPLIERORDER_OUTBOUND)
    @PostMapping("/orderOutbound")
    public CommonResult<Boolean> orderOutbound(@RequestBody TrdOrderOperVO trdOrderOperVO) {
        trdOrderOperVO.setSupplierId(SecurityUtils.getSupplierId());
        trdOrderService.orderOutbound(trdOrderOperVO);
        return success(true);
    }

    /**
     * 入驻商单据配送完成（PC收货）
     */
    @ApiOperation(value = "单据配送完成（PC收货，本地订单）", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SUPPLIERORDER_TAKE_DELIVERY)
    @RequiresPermissions(Permissions.SUPPLIERORDER_TAKE_DELIVERY)
    @PostMapping("/orderTakeDelivery")
    public CommonResult<Boolean> orderTakeDelivery(@RequestBody TrdOrderOperVO trdOrderOperVO) {
        trdOrderOperVO.setSupplierId(SecurityUtils.getSupplierId());
        trdOrderService.orderTakeDelivery(trdOrderOperVO);
        return success(true);
    }

    /**
     * 入驻商单据订单打印次数调整 （默认 + 1）
     */
    @ApiOperation(value = "入驻商单据订单打印次数调整", httpMethod = HttpMethod.POST)
    @Log(title = "入驻商单据订单打印次数调整", businessType = BusinessType.UPDATE)
    @PostMapping("/editOrderPrintQty")
    public CommonResult<Boolean> editOrderPrintQty(@RequestBody  String[] supplierOrderNos) {
        trdOrderService.editOrderPrintQty(supplierOrderNos);
        return success(true);
    }

    /**
     * 欠款订单查询
     * @param dcOrderPageReqVO
     * @return
     */
    @ApiOperation(value = "欠款订单查询", httpMethod = HttpMethod.POST)
    @RequiresPermissions("trade:order:getDebtOrderPageList")
    @PostMapping("/getDebtOrderPageList")
    @DataScope(dcAlias = "tor", dcFieldAlias = SystemConstants.DC_ID, supplierAlias = "tsord")
    public CommonResult<PageResult<DcSupplierOrderPageRespVO>> getDebtOrderPageList(@RequestBody DcOrderPageReqVO dcOrderPageReqVO) {
        return success(trdOrderService.getDebtOrderPageList(dcOrderPageReqVO));
    }

    /**
     * 欠款订单查询
     * @param dcOrderPageReqVO
     * @return
     */
    @ApiOperation(value = "欠款订单总欠款金额查询", httpMethod = HttpMethod.POST)
    @RequiresPermissions("trade:order:getDebtOrderPageList")
    @PostMapping("/getDebtOrderAmt")
    @DataScope(dcAlias = "tor", dcFieldAlias = SystemConstants.DC_ID, supplierAlias = "tsord")
    public CommonResult<BigDecimal> getDebtOrderAmt(@RequestBody DcOrderPageReqVO dcOrderPageReqVO) {
        return success(trdOrderService.getDebtOrderAmt(dcOrderPageReqVO));
    }

    @ApiOperation(value = "获取运营商订单")
//    @RequiresPermissions("trade:order:selectSupplierOrder")
    @GetMapping("/selectSupplierOrder")
    public CommonResult<PageResult<SupplierOrderVO>> selectSupplierOrder(DcOrderPageReqVO pageReqVO){
        Page<SupplierOrderVO> page = PageUtils.startPage(pageReqVO);
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        pageReqVO.setDcId(dcId);
        return success(new PageResult<>(trdOrderService.selectSupplierOrder(pageReqVO), page.getTotal()));
    }

    @ApiOperation(value = "统计入驻商订单")
    @PostMapping("/countSupplierOrder")
    @Log(title = "统计入驻商订单量", businessType = BusinessType.OTHER)
    public CommonResult<PageResult<SupplierOrderCountRespVO>> countSupplierOrder(@RequestBody SupplierOrderCountPageReqVO pageReqVO){
        Page<SupplierOrderCountRespVO> page = PageUtils.startPage(pageReqVO);

        return success(new PageResult<>(trdOrderService.countSupplierOrder(pageReqVO), page.getTotal()));
    }

    @ApiOperation(value = "按平台商统计总单量")
    @PostMapping("/countSupplierOrderAll")
    @Log(title = "按平台商统计总单量", businessType = BusinessType.OTHER)
    public CommonResult<PageResult<SupplierOrderCountAllRespVO>> countSupplierOrderAll(@RequestBody SupplierOrderCountAllPageReqVO pageReqVO){
        Page<SupplierOrderCountAllRespVO> page = PageUtils.startPage(pageReqVO);

        return success(new PageResult<>(trdOrderService.countSupplierOrderAll(pageReqVO), page.getTotal()));
    }

    @ApiOperation(value = "统计当日销售额")
    @PostMapping("/getHomePagesCurrentDaySales")
//    @Log(title = "统计当日销售额", businessType = BusinessType.OTHER)
    public CommonResult<HomePagesCurrentSalesDataRespVO> getHomePagesCurrentDaySales(@RequestBody HomePagesCurrentSalesDataReqVO reqVO){
        log.info("统计当日销售额,{}", JsonUtils.toJsonString(reqVO));
        return success(trdOrderService.getHomePagesCurrentDaySales(reqVO));
    }

    /**
     * 根据branchId统计当前月和上月订单笔数和订单总金额
     *
     * @param branchId 门店ID
     * @return 统计结果
     */
    @ApiOperation(value = "根据branchId统计当前月和上月订单笔数和订单总金额", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    /*@RequiresPermissions(Permissions.LIST)*/
    @GetMapping("/getMonthlyOrderStats")
    public CommonResult<Map<String, Object>> countOrderStatsByBranchId(@RequestParam("branchId") Long branchId) {
        Map<String, Object> result = trdOrderService.countOrderStatsByBranchId(branchId);
        return success(result);
    }
    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "trade:order:add";
        /**
         * 编辑
         */
        public static final String EDIT = "trade:order:edit";
        /**
         * 删除
         */
        public static final String DELETE = "trade:order:remove";
        /**
         * 列表
         */
        public static final String LIST = "trade:order:list";
        /**
         * 查询
         */
        public static final String GET = "trade:order:query";
        /**
         * 停用
         */
        public static final String DISABLE = "trade:order:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "trade:order:enable";

        /**
         * 订单快递信息导入
         */
        public static final String ORDEREXPRESSIMPORT = "trade:orderExpress:orderExportImport";

        /**
         * 导出
         */
        public static final String ORDEREXPRESSEXPORT = "trade:orderExpress:orderExportExport";

        /**
         * 订单打印
         */
        public static final String ORDER_PRINT = "trade:order:orderPrint";

        /**
         * 订单手动推送
         */
        public static final String ORDER_SYNC = "trade:supplierOrder:sync";

        /**
         * 修改入驻商订单备注
         */
        public static final String SUPPLIERORDER_MEMO_EDIT = "trade:supplierOrder:memoEdit";

        /**
         * 入驻商订单发货出库（PC发货，本地订单）
         */
        public static final String SUPPLIERORDER_OUTBOUND = "trade:supplierOrder:outbound";

        /**
         * 入驻商订单收货（PC收货，本地订单）
         */
        public static final String SUPPLIERORDER_TAKE_DELIVERY = "trade:supplierOrder:takeDelivery";
        /**
         * 入驻商欠款单据订单查询
         */
        public static final String GET_DEBT_ORDER_PAGE_LIST = "trade:order:getDebtOrderPageList";
    }
}
