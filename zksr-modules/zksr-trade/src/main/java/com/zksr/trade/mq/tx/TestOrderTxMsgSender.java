package com.zksr.trade.mq.tx;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.common.rocketmq.tx.AbstractTxMsgSender;
import com.zksr.trade.api.order.dto.TestOrderDto;
import com.zksr.trade.domain.Order;
import com.zksr.trade.mapper.OrderDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.MimeTypeUtils;

import javax.annotation.Resource;

@Service("testOrderTxMsgSender")
@Slf4j
public class TestOrderTxMsgSender extends AbstractTxMsgSender<TestOrderDto> {

    @Autowired
    private StreamBridge streamBridge;

    @Resource
    private OrderDAO orderDAO;

    @Override
    protected boolean sendTxMsg(TestOrderDto order) {
        log.info("发送测试事务消息：{} ", order);

        MessageBuilder builder = MessageBuilder.withPayload(order);
        builder.setHeader(MessageHeaders.CONTENT_TYPE, MimeTypeUtils.APPLICATION_JSON);

        boolean flag = streamBridge.send(
                MessageConstant.TEST_TRANSACTION_TOPIC_OUT_PUT,
                builder.build());
        log.info("发送测试事务消息:"+ flag);
        return flag;
    }

    @Override
    protected boolean chklocalTx(TestOrderDto order) {
        Order o = orderDAO.selectOne(new LambdaQueryWrapperX<Order>()
        .eqIfPresent(Order::getOrderNo, order.getOrderNo()));
        if(ToolUtil.isNotEmpty(o)){
            log.info("placeOrder 事务确认已提交");
            return true;
        }
        if(ToolUtil.isEmpty(o)){
            log.info("placeOrder 事务未正确提交");
            throw new RuntimeException("事务未正确提交");
        }
        return false;
    }
}
