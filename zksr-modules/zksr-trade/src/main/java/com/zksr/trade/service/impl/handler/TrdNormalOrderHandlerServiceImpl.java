package com.zksr.trade.service.impl.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.transfer.TransferApi;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.erp.dto.ErpOrderDTO;
import com.zksr.common.core.domain.vo.openapi.receive.OrderCancelReceiveCallbackVO;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.enums.request.RequestType;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisCacheService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.combine.SpuCombineDtlDTO;
import com.zksr.promotion.api.activity.ActivityApi;
import com.zksr.promotion.api.activity.dto.ActivityDTO;
import com.zksr.promotion.api.activity.dto.CbRuleDTO;
import com.zksr.promotion.api.activity.dto.PrmActivityDTO;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponReceiveDTO;
import com.zksr.system.api.dcArea.DcAreaApi;
import com.zksr.system.api.dcArea.dto.DcAreaDTO;
import com.zksr.system.api.openapi.AnntoErpApi;
import com.zksr.system.api.openapi.dto.AnntoErpRequestDTO;
import com.zksr.system.api.openapi.dto.AnntoErpResultDTO;
import com.zksr.system.api.openapi.dto.ErpStockRequestDTO;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.BasicSettingPolicyDTO;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.trade.api.after.dto.OrderAfterDtlResDTO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdOrderInfo;
import com.zksr.trade.api.order.vo.TrdPayOrderPageVO;
import com.zksr.trade.controller.order.vo.TrdOrderPageReqVO;
import com.zksr.trade.controller.order.vo.TrdOrderSupplierOperVo;
import com.zksr.trade.domain.*;
import com.zksr.trade.mapper.*;
import com.zksr.trade.mq.TradeMqProducer;
import com.zksr.trade.service.ITrdSettleService;
import com.zksr.trade.service.ITrdSupplierOrderService;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import com.zksr.trade.service.handler.activity.ITrdOrderActivityHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.OpenApiConstants.DEFAULT_ORG_CODE;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;
import static com.zksr.trade.enums.ErrorCodeConstants.*;


/**
 * <AUTHOR>
 * @date 2024年04月2日 19:58
 * @description: 正常订单实现操作（trade）
 */
@Service
@Slf4j
@Order(ITrdOrderHandlerService.ORDER_ONLINE)
public class TrdNormalOrderHandlerServiceImpl implements ITrdOrderHandlerService {

    @Autowired
    private TrdOrderMapper trdOrderMapper;
    @Autowired
    private ITrdSettleService trdSettleService;
    @Autowired
    private RedisCacheService redisCacheService;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;
    @Autowired
    private TrdOrderLogMapper orderLogMapper;
    @Autowired
    private AccountApi accountApi;
    @Autowired
    private TrdSettleMapper trdSettleMapper;
    @Autowired
    private TransferApi transferApi;
    @Autowired
    private RedisService redisService;
    @Autowired
    private RedisStockService redisStockService;
    @Autowired
    private TradeMqProducer tradeMqProducer;
    @Autowired
    private TrdOrderDiscountDtlMapper trdOrderDiscountDtlMapper;
    @Autowired
    private TrdCacheService trdCacheService;
    @Autowired
    private RedisActivityService redisActivityService;
    @Autowired
    private CouponApi couponApi;
    @Autowired
    private TrdSupplierOrderMapper supplierOrderMapper;
    @Autowired
    private TrdSupplierAfterDtlMapper trdSupplierAfterDtlMapper;
    @Autowired
    private TrdSupplierOrderSettleMapper trdSupplierOrderSettleMapper;
    @Autowired
    private BranchApi branchApi;
    @Autowired
    private Cache<Long, BasicSettingPolicyDTO> basicSettingPolicyCache;
    @Autowired
    private PartnerPolicyApi partnerPolicyApi;
    @Autowired
    private DcAreaApi remoteDcAreaApi;
    @Resource
    private ActivityApi activityApi;
    @Autowired
    private AnntoErpApi anntoErpApi;
    @Autowired
    private List<ITrdOrderActivityHandlerService> trdOrderActivityHandlerServices;

    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;

    @Autowired
    @Lazy
    private ITrdSupplierOrderService trdSupplierOrderService;

    @Value("${api.erp.order.cancel:/orderCancelAnnto}")
    private String apiErpOrderCancel;

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public void orderPay(TrdPayOrderPageVO pageVo, TrdOrder order) {
        // 更新主订单状态数据
        order.setPayState(PayStateEnum.getAlreadyPayState(pageVo.getPayWay()));
        order.setPlatform(pageVo.getPayPlatform());
        order.setPayWay(pageVo.getPayWay());
        order.setPayTime(DateUtils.getNowDate());
        trdOrderMapper.updateById(order);

        // 更新入驻商订单状态数据
        List<TrdSupplierOrder> supplierOrderList = supplierOrderMapper.selectListByOrderId(order.getOrderId());
        supplierOrderList.forEach(supplierOrder -> {
            supplierOrder.setPayState(order.getPayState());
            // 订单状态变更为待发货
            supplierOrder.setDeliveryState(SupplierOrderStatusEnum.WAIT_DELIVER.getCode());
        });
        supplierOrderMapper.updateBatch(supplierOrderList);

        // 更新入驻商订单明细状态数据  // 更新入驻商订单状态数据
        trdSupplierOrderDtlMapper.updateDeliveryByOrderId(new TrdOrderSupplierOperVo()
                .setDeliveryStatus(DeliveryStatusEnum.WAIT_FH.getCode())
                .setPlatform(pageVo.getPayPlatform())
                .setPayState(order.getPayState())
                .setIsFilterCancelStatus(StatusConstants.FLAG_FALSE)
                .setOrderId(order.getOrderId()));

        // 添加订单明细日志
        saveOrderLog(order, DeliveryStatusEnum.WAIT_FH.getCode(), StatusConstants.OPER_TYPE_UPDATE, DeliveryStatusEnum.WAIT_FH.getContent(), null, null);
    }

    @Override
    public void afterOrderPay(TrdOrder order) {
        /**
         * 支付成功删除 缓存未支付订单，防止到时间后自动取消了订单
         */
        Long createTime = System.currentTimeMillis();
        if (ToolUtil.isNotEmpty(order.getCreateTime())) {
            createTime = order.getCreateTime().getTime();
        }
        redisCacheService.delUnpayOrderList(new TrdOrderInfo(order.getOrderNo(), createTime, order.getSysCode(), order.getDcId()));

        List<TrdSettle> settleList = trdSettleMapper.getSettleListByOrderId(order.getOrderId());
        if (ToolUtil.isNotEmpty(settleList)) {
            log.error("订单{}已生成流水，重复回调操作!", order.getOrderNo());
            return;
        }

        /**
         * 保存订单付款后结算信息
         */
        List<TrdSettle> settles = trdSettleService.createBatchSettle(order)
                // 数据过滤  当付款方式等于储值支付时，过滤掉非业务员的结算流水
                .stream().filter(settle ->
                        !Objects.equals(order.getPayWay(), OrderPayWayEnum.WALLET.getPayWay())
                                || (Objects.equals(order.getPayWay(), OrderPayWayEnum.WALLET.getPayWay()) && Objects.equals(settle.getMerchantType(), MerchantTypeEnum.COLONEL.getType()))
                ).collect(Collectors.toList());
        trdSettleMapper.insertBatch(settles);

//        // 修改对应门店的进入公海时间
        try {
            if (ToolUtil.isNotEmpty(order.getAreaId()) || ToolUtil.isNotEmpty(order.getSysCode()) || ToolUtil.isNotEmpty(order.getBranchId())) {
                //根据城市ID获取对应的运营商基础配置
                DcAreaDTO dcAreaDTO = remoteDcAreaApi.getDcAreaByDcIdOrAreaId(null, order.getAreaId(), order.getSysCode()).getCheckedData();
                if (ToolUtil.isNotEmpty(dcAreaDTO)) {
                    BasicSettingPolicyDTO basicSettingPolicyDTO = partnerPolicyApi.getBasicSettingPolicy(dcAreaDTO.getDcId()).getData();

                    BranchDTO branchDTO = branchApi.getByBranchId(order.getBranchId()).getData();
                    if (ToolUtil.isNotEmpty(branchDTO) && ToolUtil.isNotEmpty(basicSettingPolicyDTO) && ToolUtil.isNotEmpty(basicSettingPolicyDTO.getSeasDay())) {

                        MemBranchSaveReqVO memBranchSaveReqVO = new MemBranchSaveReqVO();
                        memBranchSaveReqVO.setBranchId(order.getBranchId());
                        Integer seasDay = Integer.valueOf(basicSettingPolicyDTO.getSeasDay());
                        if (ToolUtil.isNotEmpty(seasDay) && seasDay > 0) {
                            LocalDateTime localDate = new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                            LocalDateTime newLocalDate = localDate.plusDays(seasDay);
                            Date newSeasDate = Date.from(newLocalDate.atZone(ZoneId.systemDefault()).toInstant());
                            memBranchSaveReqVO.setSeasTime(newSeasDate);
                            branchApi.edit(memBranchSaveReqVO);
                        }
                    }
                }
            }
        } catch (Exception exception) {
            log.error("订单信息:" + order.getOrderNo());
            log.error("修改进入公海时间失败", exception);
        }


        // 发送订单打印MQ
//        TrdOrderSaveReqVO reqVo = new TrdOrderSaveReqVO();
//        reqVo.setSysCode(order.getSysCode());
//        reqVo.setOrderId(order.getOrderId());
//        tradeMqProducer.sendStorePrintOrderEvent(reqVo);
//
//        // 发送常购商品MQ
//        tradeMqProducer.sendEsStoreProductEvent(order.getOrderId());

        //发送业务员APP客户信息订单MQ
//        tradeMqProducer.sendEsColonelAppBranchEvent(new ColonelAppBranchDTO(order.getBranchId(),order.getSysCode(),order.getPayAmt(),ES_COLONEL_APP_ORDER_STATUS_1));

        //发送业务员APP首页信息 客户下单订单MQ
//        tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(order.getColonelId(),order.getSysCode(),order.getBranchId(),order.getPayAmt(), HutoolBeanUtils.toBean(settles, TrdSettleDTO.class),COLONEL_APP_Page_DATA_TYPE_3));

        // 组装主订单信息、发送MQ
        //tradeMqProducer.sendErpTradeOrderEvent(new OrderSendMqOpenDTO(order.getOrderNo(), order.getColonelId(), ORDER_XSS, order.getPayWay(), order.getBranchId(), order.getDcId()));

        //组装推送销售订单同步信息 发送MQ
//        tradeMqProducer.sendSyncDataOrderEvent(new SyncOrderSendDTO(order.getOrderNo(),order.getDcId()));

        Long afterOrderPayEndTime = System.currentTimeMillis();
        log.info("订单{}进入Trade模块支付回调更新订单之后其他操作结束时间{}", order.getOrderNo(), afterOrderPayEndTime);
    }

    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("all")
    @Override
    public void orderCancel(TrdSupplierOrder trdSupplierOrder) {
        if (StringUtils.isNull(trdSupplierOrder)) {
            throw exception(TRD_ORDER_NOT_EXISTS);
        }

        TrdOrder orderVo = trdOrderMapper.getOrderByOrderNo(new TrdOrderPageReqVO(trdSupplierOrder.getOrderNo()));

        boolean needReleaseErpStock = true;
        // 未付款取消
        if (PayStateEnum.PAY_NOT_ONLINE.getCode().equals(trdSupplierOrder.getPayState())) {
            // 更新入驻商订单状态为 已取消
            trdSupplierOrderMapper.update(null, new LambdaUpdateWrapper<TrdSupplierOrder>()
                    .set(TrdSupplierOrder::getPayState, PayStateEnum.PAY_CANCEL.getCode())
                    .set(TrdSupplierOrder::getDeliveryState, SupplierOrderStatusEnum.CANCELED.getCode())
                    .set(TrdSupplierOrder::getUpdateTime, DateUtils.getNowDate())
                    .eq(TrdSupplierOrder::getSupplierOrderId, trdSupplierOrder.getSupplierOrderId()));

            // 根据子订单ID将订单商品状态更改成已取消
            trdSupplierOrderDtlMapper.update(null, new LambdaUpdateWrapper<TrdSupplierOrderDtl>()
                    .set(TrdSupplierOrderDtl::getDeliveryState, DeliveryStatusEnum.CANCEL.getCode())
                    .set(TrdSupplierOrderDtl::getUpdateTime, DateUtils.getNowDate())
                    .eq(TrdSupplierOrderDtl::getSupplierOrderId, trdSupplierOrder.getSupplierOrderId()));

            // 所有子单都取消后，把主单状态改为已取消
            List<TrdSupplierOrder> supplierOrders = trdSupplierOrderMapper.selectList(new LambdaQueryWrapper<TrdSupplierOrder>()
                    .eq(TrdSupplierOrder::getOrderId, trdSupplierOrder.getOrderId()));
            boolean isAllCancelled = supplierOrders.stream().allMatch(t -> PayStateEnum.PAY_CANCEL.getCode().equals(t.getPayState()));
            if (isAllCancelled) {
                trdOrderMapper.update(null, new LambdaUpdateWrapper<TrdOrder>()
                        .eq(TrdOrder::getOrderId, trdSupplierOrder.getOrderId())
                        .set(TrdOrder::getUpdateTime, DateUtils.getNowDate())
                        .set(TrdOrder::getPayState, PayStateEnum.PAY_CANCEL.getCode()));

                //  取消成功 删除 缓存未支付订单，防止订单重复处理
                Long createTime = System.currentTimeMillis();
                if (ToolUtil.isNotEmpty(orderVo.getCreateTime())) {
                    createTime = orderVo.getCreateTime().getTime();
                }
                redisCacheService.delUnpayOrderList(new TrdOrderInfo(orderVo.getOrderNo(), createTime, orderVo.getSysCode(), orderVo.getDcId()));
            }

            orderNotPayCancel(trdSupplierOrder);

            // 返还库存数量
            List<TrdSupplierOrderDtl> orderDtlS = trdSupplierOrderDtlMapper.selectListBySupplierOrderId(trdSupplierOrder.getSupplierOrderId());
            orderDtlS.forEach(item -> {
                redisStockService.incrSkuSaledQty(item.getSkuId(), item.getTotalNum().multiply(NumberPool.BIGDECIMAL_GROUND));
                redisStockService.incrSkuOccupiedQty(item.getSkuId(), item.getTotalNum().multiply(NumberPool.BIGDECIMAL_GROUND));
            });
        } else {
            // 已支付
            if (PayStateEnum.PAY_ALREADY_ONLINE.getCode().equals(trdSupplierOrder.getPayState())) {
                Date tenMinutesAgo = DateUtils.addMinutes(new Date(), -10);
                // 支付10分钟后才能取消
                if (trdSupplierOrder.getUpdateTime() == null || tenMinutesAgo.before(trdSupplierOrder.getUpdateTime())) {
                    throw exception(WX_NOT_SETTLE);
                }
            }

            // 更新订单支付状态 为 取消中状态（5）
            trdSupplierOrderMapper.update(null, new LambdaUpdateWrapper<TrdSupplierOrder>()
                    .set(TrdSupplierOrder::getPayState, PayStateEnum.PAY_BEING_CANCELLED.getCode())
                    .set(TrdSupplierOrder::getDeliveryState, SupplierOrderStatusEnum.AFTER.getCode())
                    .set(TrdSupplierOrder::getUpdateTime, DateUtils.getNowDate())
                    .eq(TrdSupplierOrder::getSupplierOrderId, trdSupplierOrder.getSupplierOrderId()));

            trdSupplierOrderDtlMapper.update(null, new LambdaUpdateWrapper<TrdSupplierOrderDtl>()
                    .set(TrdSupplierOrderDtl::getDeliveryState, DeliveryStatusEnum.CANCELLING.getCode())
                    .set(TrdSupplierOrderDtl::getUpdateTime, DateUtils.getNowDate())
                    .eq(TrdSupplierOrderDtl::getSupplierOrderId, trdSupplierOrder.getSupplierOrderId()));

            Long supplierId = trdSupplierOrder.getSupplierId();
            VisualSettingMasterDto visualMaster = trdCacheService.getVisualMasterBySupplier(supplierId);
            // 未推送ERP的订单 或者 没有对接安得ERP 直接模拟回调
            if (PushStatusEnum.UN_PUSHED.getCode().equals(trdSupplierOrder.getPushStatus())
                    || (visualMaster == null || !SyncSourceType.ANNTOERP.getCode().equals(visualMaster.getSourceType()))) {
                OrderCancelReceiveCallbackVO vo = new OrderCancelReceiveCallbackVO()
                        .setSupplierOrderNo(trdSupplierOrder.getSupplierOrderNo());
                trdSupplierOrderService.orderCancelReceiveCallback(vo);
            } else {
                ErpOrderDTO erpOrderDTO = new ErpOrderDTO();
                erpOrderDTO.setB2bSheetNo(trdSupplierOrder.getSupplierOrderNo());
                CommonResult<AnntoErpResultDTO<String>> result = anntoErpApi.sendErp(new AnntoErpRequestDTO()
                        .setApi(apiErpOrderCancel)
                        .setData(erpOrderDTO)
                        .setReqId(UuidUtils.generateUuid())
                        .setSupplierId(trdSupplierOrder.getSupplierId())
                        .setRequestType(RequestType.SYNC_B2B_ORDER_CANCEL)
                        .setB2bRequestType(B2BRequestType.SYNC_B2B_ORDER_CANCEL)
                        .setOperationType(OperationType.OTHER)
                        .setAutoFail(true));
                if (result == null || result.isError() || result.getCheckedData() == null || !result.getCheckedData().isOk()) {
                    throw exception(TRD_ORDER_CANNOT_CANCEL);
                }

                needReleaseErpStock= false;
            }
        }

        if (orderVo != null) {
            // 添加订单明细日志
            saveOrderLog(orderVo, DeliveryStatusEnum.CANCEL.getCode(), StatusConstants.OPER_TYPE_UPDATE, DeliveryStatusEnum.CANCEL.getContent(), null, null);
        }

        if (needReleaseErpStock) {
            // 释放ERP预占库存
            this.releaseErpStock(trdSupplierOrder);
        }
    }

    @Override
    public void orderOutbound(TrdOrder orderVo, Long supplierId) {
        log.info("orderOutbound={},{}", JsonUtils.toJsonString(orderVo),supplierId);
        checkOrder(orderVo);
        /**
         * 根据订单ID将订单商品状态更改成待装车
         */

        trdSupplierOrderDtlMapper.updateDeliveryByOrderId(new TrdOrderSupplierOperVo()
                .setOrderId(orderVo.getOrderId())
                .setSupplierId(supplierId)
                .setItemType(StatusConstants.ITEM_TYPE_1)  // 这里针对全国和本地商品固定取消 ，目前再整个订单中要么是全国要么是本地商品，不会存在混合
                .setDeliveryStatus(DeliveryStatusEnum.WAIT_SH.getCode()));

        List<TrdSupplierOrder> supplierOrderList = trdSupplierOrderMapper.selectListByOrderId(orderVo.getOrderId());

        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(supplierOrderList)){
            supplierOrderList.forEach(supplierOrderItem -> {
                supplierOrderItem.setDeliveryState(SupplierOrderStatusEnum.WAIT_RECEIVED.getCode());
                supplierOrderItem.setUpdateTime(DateUtils.getNowDate());
                trdSupplierOrderMapper.updateById(supplierOrderItem);
            });
        }

        // 添加订单明细日志
        saveOrderLog(orderVo, DeliveryStatusEnum.WAIT_SH.getCode(), StatusConstants.OPER_TYPE_UPDATE, DeliveryStatusEnum.WAIT_SH.getContent(), supplierId, StatusConstants.ITEM_TYPE_1);

    }

    @Override
    public void orderEntrucking(TrdOrder orderVo, Long supplierId) {
        checkOrder(orderVo);
        /**
         * 根据订单ID将订单商品状态更改成已装车
         */
        trdSupplierOrderDtlMapper.updateDeliveryByOrderId(new TrdOrderSupplierOperVo()
                .setOrderId(orderVo.getOrderId())
                .setSupplierId(supplierId)
                .setItemType(StatusConstants.ITEM_TYPE_1)
                .setDeliveryStatus(DeliveryStatusEnum.COMPLETE_ZC.getCode())
                .setDeliveryTime(DateUtils.getNowDate()));

        // 添加订单明细日志
        saveOrderLog(orderVo, DeliveryStatusEnum.COMPLETE_ZC.getCode(), StatusConstants.OPER_TYPE_UPDATE, DeliveryStatusEnum.COMPLETE_ZC.getContent(), supplierId, StatusConstants.ITEM_TYPE_1);

    }

    @Override
    public void orderCancelEntrucking(TrdOrder orderVo, Long supplierId) {
        checkOrder(orderVo);
        /**
         * 根据订单ID将订单商品状态更改成待装车
         */
        trdSupplierOrderDtlMapper.updateDeliveryByOrderId(new TrdOrderSupplierOperVo()
                .setOrderId(orderVo.getOrderId())
                .setSupplierId(supplierId)
                .setItemType(StatusConstants.ITEM_TYPE_1)
                .setDeliveryStatus(DeliveryStatusEnum.WAIT_ZC.getCode()));

        // 添加订单明细日志
        saveOrderLog(orderVo, DeliveryStatusEnum.WAIT_ZC.getCode(), StatusConstants.OPER_TYPE_UPDATE, DeliveryStatusEnum.WAIT_ZC.getContent(), supplierId, StatusConstants.ITEM_TYPE_1);

    }

    @Override
    public void orderTakeDelivery(TrdSupplierOrderDtl orderDtl) {
        // 当订单明细状态为已经全部取消了，收货不做任何处理, 调用方面没有处理，只能这里来处理
        if (ObjectUtil.equals(orderDtl.getDeliveryState(), DeliveryStatusEnum.CANCEL.getCode())) {
            return;
        }
        if (orderDtl.getDeliveryState() != DeliveryStatusEnum.COMPLETE_ZC.getCode() && orderDtl.getDeliveryState() != DeliveryStatusEnum.WAIT_SH.getCode()) {
            throw exception(TRD_SUPPLIER_ORDER_CHECK_DELIVERY_STATE, orderDtl.getSupplierOrderNo());
        }
        /**
         * 根据订单ID将订单商品状态更改成已收货
         */
        orderDtl.setDeliveryState(DeliveryStatusEnum.COMPLETE_SH.getCode());
        orderDtl.setReceiveTime(DateUtils.getNowDate());
        trdSupplierOrderDtlMapper.updateById(orderDtl);

        //更新订单头收货状态
        TrdSupplierOrder trdSupplierOrder = supplierOrderMapper.selectOrderBySupplierOrderNo(orderDtl.getSupplierOrderNo());
        if (trdSupplierOrder != null) {
            trdSupplierOrder.setDeliveryState(SupplierOrderStatusEnum.RECEIVED.getCode());
            supplierOrderMapper.updateById(trdSupplierOrder);
        }

        // 添加订单明细日志
        saveOrderDtlLog(orderDtl, DeliveryStatusEnum.COMPLETE_SH.getCode(), StatusConstants.OPER_TYPE_UPDATE, DeliveryStatusEnum.COMPLETE_SH.getContent());
    }

    @Override
    public void orderComplete(TrdSupplierOrderDtl orderDtl) {
        if (!Objects.equals(orderDtl.getDeliveryState(), DeliveryStatusEnum.COMPLETE_SH.getCode())) {
            throw new ServiceException("当前状态不能确认完成");
        }

        TrdSupplierOrder trdSupplierOrder = supplierOrderMapper.selectOrderBySupplierOrderNo(orderDtl.getSupplierOrderNo());
        if (trdSupplierOrder != null) {
            trdSupplierOrder.setDeliveryState(SupplierOrderStatusEnum.RECEIVED.getCode());
            supplierOrderMapper.updateById(trdSupplierOrder);
        }

        /**
         * 根据订单ID将订单商品状态更改成已完成
         */
        orderDtl.setDeliveryState(DeliveryStatusEnum.COMPLETE.getCode());
        orderDtl.setCompleteTime(DateUtils.getNowDate());
        trdSupplierOrderDtlMapper.updateById(orderDtl);

        /**
         * 将对应的结算流水变更成 待结算（用于后续的订单生成转账单或提交分账）
         */
        List<TrdSettle> settles = trdSettleService.getSettleBySupplierOrderDtlId(Collections.singletonList(orderDtl.getSupplierOrderDtlId()));
        settles.forEach(settle -> {
            settle.setState(StatusConstants.SETTLE_STATE_3);
        });
        if (ToolUtil.isNotEmpty(settles)) {
            trdSettleMapper.updateBatch(settles);
        }

        // 添加订单明细日志
        saveOrderDtlLog(orderDtl, DeliveryStatusEnum.COMPLETE.getCode(), StatusConstants.OPER_TYPE_UPDATE, DeliveryStatusEnum.COMPLETE.getContent());
    }

    @Override
    public void orderPreareGoods(TrdSupplierOrderDtl orderDtl) {
        /**
         * 根据订单ID将订单商品状态更改成备货中
         */
        trdSupplierOrderDtlMapper.updateDeliveryByOrderId(new TrdOrderSupplierOperVo()
                .setOrderId(orderDtl.getOrderId())
                .setSupplierId(orderDtl.getSupplierId())
                .setItemType(StatusConstants.ITEM_TYPE_0)
                .setDeliveryStatus(DeliveryStatusEnum.PREAREGOODS.getCode())
                .setSupplierOrderDtlId(orderDtl.getSupplierOrderDtlId()));

        // 添加订单明细日志
        saveOrderDtlLog(orderDtl, DeliveryStatusEnum.PREAREGOODS.getCode(), StatusConstants.OPER_TYPE_UPDATE, DeliveryStatusEnum.PREAREGOODS.getContent());
    }

    @Override
    public void orderOutbound(TrdSupplierOrderDtl orderDtl) {
        /**
         * 将订单商品状态更改成待收货
         */
        trdSupplierOrderDtlMapper.updateDeliveryByOrderId(new TrdOrderSupplierOperVo()
                .setOrderId(orderDtl.getOrderId())
                .setSupplierId(orderDtl.getSupplierId())
                .setItemType(StatusConstants.ITEM_TYPE_0)
                .setDeliveryStatus(DeliveryStatusEnum.WAIT_SH.getCode())
                .setDeliveryTime(DateUtils.getNowDate())
                .setSupplierOrderDtlId(orderDtl.getSupplierOrderDtlId()));

        // 添加订单明细日志
        saveOrderDtlLog(orderDtl, DeliveryStatusEnum.WAIT_SH.getCode(), StatusConstants.OPER_TYPE_UPDATE, DeliveryStatusEnum.WAIT_SH.getContent());
    }

    @Override
    public void returnAfterActivitySaleQty(List<TrdAfterDiscountDtl> disoucntDtlList, TrdAfter tar, Map<Long, TrdSupplierAfterDtl> supplierAfterDtlMap, List<TrdOrderDiscountDtl> orderDiscountDtlList) {
        // 返还秒杀、特价、买赠、满赠活动库存
        disoucntDtlList.forEach(discount -> {
            try {
                // 这里发货前返回
                BigDecimal returnQty = discount.getGiftQty().multiply(NumberPool.BIGDECIMAL_GROUND);
                switch (TrdDiscountTypeEnum.getDiscountType(discount.getDiscountType())) {
                    case SK:
                        handleSkDiscount(discount, tar, supplierAfterDtlMap);
                        break;
                    case SP:
                        handleSpDiscount(discount, tar, supplierAfterDtlMap);
                        break;
                    case BG:
                        redisActivityService.setBgSaleNum(discount.getDiscountId(), discount.getDiscountRuleId(), returnQty, null, null);
                        break;
                    case FG:
                        redisActivityService.setFgSaleNum(discount.getDiscountId(), discount.getDiscountRuleId(), returnQty, null, null);
                        break;
                    case CB:
                        handleCbDiscount(discount, tar, supplierAfterDtlMap, orderDiscountDtlList);
                        break;
                    default:
                        break;
                }
            } catch (Exception e) {
                // 记录日志并继续处理其他折扣类型
                log.error("Error processing discount type: " + discount.getDiscountType(), e);
            }
        });

        // 活动只参加一次标识回退
        disoucntDtlList.stream()
                .filter(orderDiscount -> !Objects.equals(TrdDiscountTypeEnum.COUPON.getType(), orderDiscount.getDiscountType()))
                .map(TrdAfterDiscountDtl::getDiscountId).filter(Objects::nonNull).distinct().forEach(activityId -> {
                    try {
                        PrmActivityDTO activityDTO = trdCacheService.getActivityDTO(activityId);
                        if (Objects.nonNull(activityDTO.getTimesRule()) && activityDTO.getTimesRule() == NumberPool.INT_ONE) {
                            redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(activityId, tar.getBranchId()));
                        }
                    } catch (Exception e) {
                        log.error("Error resetting times rule for activity ID: " + activityId, e);
                    }
                });
    }

    @Override
    public void orderTakeDeliveryReceiveCoupon(Long orderId, Set<Long> supplierOrderIds) {
        TrdOrder trdOrder = trdOrderMapper.selectById(orderId);

        supplierOrderIds.stream().forEach(supplierOrderId -> {
            List<TrdSupplierOrderDtl> supplierOrderDtlList = trdSupplierOrderDtlMapper.selectListBySupplierOrderId(supplierOrderId);
            // 入驻商订单明细是否已全部收货
            Boolean isAllTakeDelivery = supplierOrderDtlList.stream().anyMatch(item -> item.getDeliveryState() == DeliveryStatusEnum.COMPLETE_SH.getCode());
            // 为false时 直接返回
            if (!isAllTakeDelivery) return;

            // 查询订单优惠明细表，是否存在赠品优惠劵
            List<TrdOrderDiscountDtl> orderDiscountDtlList = trdOrderDiscountDtlMapper.selectListBySupplierId(supplierOrderId);
            if (ToolUtil.isEmpty(orderDiscountDtlList)) return;

            // 获取入驻商订单下的所有赠送的优惠劵模板
            orderDiscountDtlList.stream()
                    .filter(this::checkGiftCouponSending) // 验证是否赠送优惠券
                    .forEach(disoucnt -> {
                        // 因赠优惠券数量可能会有多张，故这里循环发送
                        for (int i = 0; i < disoucnt.getGiftQty(); i++) {
                            //发送优惠劵
                            couponApi.receiveCouponNotCheck(new CouponReceiveDTO(Lists.newArrayList(disoucnt.getGiftCouponTemplateId()), trdOrder.getBranchId(), trdOrder.getMemberId(), null));
                        }
                    });

        });
    }

    @Override
    public void handleCancelledOrderPaymentSuccess(TrdOrder tor) {
        // 查询订单明细数据
        List<TrdSupplierOrderDtl> tsodList = trdSupplierOrderDtlMapper.selectListByOrderId(tor.getOrderId());
        if (ToolUtil.isEmpty(tsodList) || tsodList.isEmpty()) {
            log.info("订单【{}】已取消，未查询到订单明细数据", tor.getOrderNo());
            return;
        }
        tsodList.forEach(item -> {
            // 还原已取消下单商品库存
            redisStockService.incrSkuSaledQty(item.getSkuId(), item.getTotalNum().negate());
            redisStockService.incrSkuOccupiedQty(item.getSkuId(), item.getTotalNum().negate());
        });

        // 查询参与本次订单活动的商品数量
        List<TrdOrderDiscountDtl> toddList = trdOrderDiscountDtlMapper.selectListByOrderId(tor.getOrderId());
        if (ToolUtil.isEmpty(toddList) || toddList.isEmpty()) {
            return;
        }

        Map<Long, TrdSupplierOrderDtl> supplierOrderDtlMap = convertMap(tsodList, TrdSupplierOrderDtl::getSupplierOrderDtlId);
        // 订单取消促销活动数据返回
        trdOrderActivityHandlerServices.forEach(service -> {
            service.restoreCancelledOrderDiscounts(toddList, tor, supplierOrderDtlMap);
        });
    }

    /**
     * 校验订单状态
     *
     * @param orderVo
     */
    private void checkOrder(TrdOrder orderVo) {
        if (StringUtils.isNull(orderVo)) {
            throw exception(TRD_ORDER_NOT_EXISTS);
        }
        if (orderVo.getPayState() != PayStateEnum.PAY_ALREADY_ONLINE.getCode() && orderVo.getPayState() != PayStateEnum.PAY_NOT_HDFK.getCode()
                && orderVo.getPayState() != PayStateEnum.PAY_ALREADY_HDFK.getCode()) {
            throw exception(TRD_ORDER_NOT_PAY);
        }
    }

    /**
     * 新增订单操作日志
     *
     * @param orderVo    主订单
     * @param afterState 更新后状态
     * @param operType   操作类型
     * @param content    信息
     */
    private void saveOrderLog(TrdOrder orderVo, Long afterState, Long operType, String content, Long supplierId, Integer itemType) {
        TrdSupplierOrderDtl orderDtl = new TrdSupplierOrderDtl();
        orderDtl.setOrderId(orderVo.getOrderId());
        orderDtl.setSupplierId(supplierId);
        orderDtl.setItemType(itemType);
        List<TrdSupplierOrderDtl> sOrderDtlList = trdSupplierOrderDtlMapper.getSupplierOrdeDtl(orderDtl);
        List<TrdOrderLog> orderLogs = sOrderDtlList.stream().map(dtl -> {
            TrdOrderLog orderLog = new TrdOrderLog();
            orderLog.setSupplierOrderDtlId(dtl.getSupplierOrderDtlId());
            orderLog.setSupplierOrderDtlNo(dtl.getSupplierOrderDtlNo());
            orderLog.setBeforeState(dtl.getDeliveryState());
            orderLog.setAfterState(afterState);
            orderLog.setOperateType(operType);
            orderLog.setContent(content);
            orderLog.setSysCode(orderVo.getSysCode());
            return orderLog;
        }).collect(Collectors.toList());
        orderLogMapper.insertBatch(orderLogs);
        //清空门店我的订单状态数据缓存
        trdCacheService.clearOrderTotal(orderVo.getBranchId());
        //清空业务员下单门店订单状态数据缓存
        trdCacheService.clearOrderTotal(orderVo.getColonelId());
    }

    /**
     * 新增订单明细操作日志
     *
     * @param orderDtl   订单明细
     * @param afterState 更新后状态
     * @param operType   操作类型
     * @param content    信息
     */
    private void saveOrderDtlLog(TrdSupplierOrderDtl orderDtl, Long afterState, Long operType, String content) {
        TrdOrderLog orderLog = new TrdOrderLog();
        orderLog.setSupplierOrderDtlId(orderDtl.getSupplierOrderDtlId());
        orderLog.setSupplierOrderDtlNo(orderDtl.getSupplierOrderDtlNo());
        orderLog.setBeforeState(orderDtl.getDeliveryState());
        orderLog.setAfterState(afterState);
        orderLog.setOperateType(operType);
        orderLog.setContent(content);
        orderLog.setSysCode(orderDtl.getSysCode());
        orderLogMapper.insert(orderLog);

        TrdOrder trdOrder = trdOrderMapper.selectById(orderDtl.getOrderId());
        //清空门店我的订单状态数据缓存
        trdCacheService.clearOrderTotal(trdOrder.getBranchId());
        //清空业务员下单门店订单状态数据缓存
        trdCacheService.clearOrderTotal(trdOrder.getColonelId());
    }

    /**
     * 未付款取消
     */
    private void orderNotPayCancel(TrdSupplierOrder trdSupplierOrder) {
        // 返还参与本次订单活动的商品数量
        List<TrdOrderDiscountDtl> orderDiscountDtlList = trdOrderDiscountDtlMapper.getListBySupplierOrderId(trdSupplierOrder.getSupplierOrderId());
        if (ToolUtil.isEmpty(orderDiscountDtlList)) {
            return;
        }

        List<TrdSupplierOrderDtl> supplierOrderDtlList = trdSupplierOrderDtlMapper.selectListBySupplierOrderId(trdSupplierOrder.getSupplierOrderId());
        Map<Long, TrdSupplierOrderDtl> supplierOrderDtlMap = convertMap(supplierOrderDtlList, TrdSupplierOrderDtl::getSupplierOrderDtlId);

        TrdOrder trdOrder = trdOrderMapper.selectById(trdSupplierOrder.getOrderId());
        if (trdOrder == null) {
            log.error("订单不存在orderId={}", trdSupplierOrder.getOrderId());
            return;
        }
        // 订单取消促销活动数据返回
        trdOrderActivityHandlerServices.forEach(service -> {
            service.orderCancelReturnActivity(orderDiscountDtlList, trdOrder, supplierOrderDtlMap);
        });
    }

    /**
     * TODO 这里未考虑订单已支付取消问题 （如订单资金如何退回、优惠劵是否返还、利润分成计算数据是否删除问题）
     * 已付款取消
     *
     * @param orderVo
     */
    private void orderAlreadyPayCancel(TrdOrder orderVo) {

    }

    /**
     * 根据订单结算流水生成入驻商账户的冻结流水 (弃用)
     */
    private List<AccAccountFlowDTO> createAccountFreezeFlow(List<TrdSettle> settles) {
        List<AccAccountFlowDTO> accAccountFlowDTOS = new ArrayList<>();
        Map<Long, List<TrdSettle>> settleMap = settles.stream().collect(Collectors.groupingBy(TrdSettle::getSupplierId));
        settleMap.forEach((key, value) -> {
            AccAccountFlowDTO flowDTO = new AccAccountFlowDTO();
            flowDTO.setSysCode(value.get(0).getSysCode());
            flowDTO.setBusiType(AccountBusiType.SUPPLIER_CREATE_ORDER.getType());
            flowDTO.setBusiId(value.get(0).getSupplierOrderId());
            flowDTO.setBusiFields(AccountBusiTypeField.FROZEN_AMT.getField());
            flowDTO.setPlatform(value.get(0).getPlatform());
            flowDTO.setMerchantType(MerchantTypeEnum.SUPPLIER.getType());
            flowDTO.setMerchantId(key);
            BigDecimal supplierAmt = value.stream().map(settle -> settle.getSettleAmt()).reduce((amt1, amt2) -> amt1.add(amt2)).get();
            flowDTO.setBusiFrozenAmt(supplierAmt);
            accAccountFlowDTOS.add(flowDTO);
        });
        return accAccountFlowDTOS;
    }

    /**
     * 根据结算流水生成转账单 （弃用）
     */
//    private void createSettleTransfer(TrdSupplierOrderDtl orderDtl){
//        TrdOrder trdOrder = trdOrderMapper.selectById(orderDtl.getOrderId());
//        if (PayChannelEnum.isB2b(trdOrder.getPlatform())) { // B2B微信支付不生成转账流水
//            return;
//        }
//
//        List<TrdSettle> settles = trdSettleMapper.selectSettleBySupplierDtlId(orderDtl.getSupplierOrderDtlId());
//        // 得到当前入驻商结算的冻结金额
//        Map<Long,BigDecimal> freezeAmtMap = settles.stream().collect(Collectors.groupingBy(TrdSettle::getMerchantId, Collectors.reducing(BigDecimal.ZERO,TrdSettle::getSettleAmt,BigDecimal::add)));
//        settles.stream().forEach(settle -> {
//            TransferSaveDTO transferSaveDTO = new TransferSaveDTO();
//            transferSaveDTO.setSettleId(settle.getSettleId());
//            transferSaveDTO.setTransferNo(SheetTypeConstants.ZZ+redisService.getUniqueNumber());
//            transferSaveDTO.setPlatform(settle.getPlatform());
//
//            BigDecimal freezeAmt = freezeAmtMap.get(settle.getMerchantId());
//
//            if (settle.getSettleAmt().compareTo(BigDecimal.ZERO) == -1){ //当结算金额为负数是说明是售后
//                transferSaveDTO.setSourceMerchantId(settle.getMerchantId());
//                transferSaveDTO.setSourceMerchantType(settle.getMerchantType());
//                transferSaveDTO.setTargetMerchantId(settle.getSupplierId());
//                transferSaveDTO.setTargetMerchantType(MerchantTypeEnum.SUPPLIER.getType());
//                transferSaveDTO.setTransferAmt(settle.getSettleAmt().negate());
//            }
//            if (settle.getSettleAmt().compareTo(BigDecimal.ZERO) == 1) {
//                transferSaveDTO.setSourceMerchantId(settle.getSupplierId());
//                transferSaveDTO.setSourceMerchantType(MerchantTypeEnum.SUPPLIER.getType());
//                transferSaveDTO.setTargetMerchantId(settle.getMerchantId());
//                transferSaveDTO.setTargetMerchantType(settle.getMerchantType());
//                transferSaveDTO.setTransferAmt(settle.getSettleAmt());
//                transferSaveDTO.setSettleAmt(freezeAmt);
//            }
//            settle.setState(StatusConstants.SETTLE_STATE_2);
//            if (settle.getSettleAmt().compareTo(BigDecimal.ZERO) == 0){ // 当结算金额为0时，不执行转账操作，直接更改状态为结算
//                settle.setState(StatusConstants.SETTLE_STATE_1);
//                settle.setSettleTime(DateUtils.getNowDate());
//            }
//            trdSettleMapper.updateById(settle);
//            // 当前状态为待结算或结算中才能生成转账单
//            if (Objects.equals(settle.getState(), StatusConstants.SETTLE_STATE_2)
//                    || Objects.equals(settle.getState(), StatusConstants.SETTLE_STATE_0)) {
//                transferApi.createTransfer(transferSaveDTO);
//            }
//
//        });
//    }


    /**
     * 检测是否赠送优惠券
     *
     * @param discountDtl
     * @return
     */
    private Boolean checkGiftCouponSending(TrdOrderDiscountDtl discountDtl) {
        // 默认不赠送优惠券
        boolean flag = false;
        // 根据订单优惠表中的 入驻商订单ID + 入驻商订单明细行号查询出参与对应活动规则的商品信息
        List<TrdSupplierOrderDtl> dtls = trdSupplierOrderDtlMapper.selectListBySupplierOrderId(discountDtl.getSupplierOrderId());

        // 已售后总数量
        List<OrderAfterDtlResDTO> orderAfterDtlResDTOS = trdSupplierAfterDtlMapper.getAfterDtlNumBySupplierDtlId(
                dtls.stream().map(TrdSupplierOrderDtl::getSupplierOrderDtlId).collect(Collectors.toList())
        );
        Map<Long, OrderAfterDtlResDTO> orderAfterDtlResDTOMap = convertMap(orderAfterDtlResDTOS, OrderAfterDtlResDTO::getSupplierOrderDtlId);

        // 优惠劵赠送规则商品实际总收货数量
        Long qty = dtls
                .stream().filter(dtl -> discountDtl.getOrderDtlNumStr().contains(dtl.getLineNum() + ""))
                .map(dtl -> {
                    OrderAfterDtlResDTO afterDtlResDTO = orderAfterDtlResDTOMap.get(dtl.getSupplierOrderDtlId());
                    BigDecimal returnNum = BigDecimal.ZERO; // 当前订单明细已售后最小单位数量
                    if (ToolUtil.isNotEmpty(afterDtlResDTO)) {
                        returnNum = afterDtlResDTO.getSumReturnNum();
                    }
                    // 优惠劵赠送规则商品实际总收货数量 = （订单下单最小单位数量 - 已售后最小单位数量）/ 订单下单单位换算数量   注：计算出小数会直接舍弃
                    return dtl.getTotalNum().subtract(returnNum).divide(dtl.getOrderUnitSize(), 0, RoundingMode.DOWN).longValue();
                }).reduce(NumberPool.LONG_ZERO, Long::sum);
        // 优惠劵赠送规则实际总收货金额
        BigDecimal amt = dtls
                .stream().filter(dtl -> discountDtl.getOrderDtlNumStr().contains(dtl.getLineNum() + ""))
                .map(dtl -> {
                    OrderAfterDtlResDTO afterDtlResDTO = orderAfterDtlResDTOMap.get(dtl.getSupplierOrderDtlId());
                    BigDecimal returnAmt = BigDecimal.ZERO; // 当前订单明细已售后最小单位数量
                    if (ToolUtil.isNotEmpty(afterDtlResDTO)) {
                        returnAmt = afterDtlResDTO.getSumReturnAmt();
                    }
                    // 优惠劵赠送规则实际总收货金额 = 订单最终金额 - 已售后金额）
                    return dtl.getResAmt().subtract(returnAmt);
                }).reduce(BigDecimal.ZERO, BigDecimal::add);


        if (Objects.equals(discountDtl.getDiscountType(), TrdDiscountTypeEnum.BG.getType())) { // 买赠
            flag = qty >= Long.parseLong(discountDtl.getDiscountCondition());
        } else if (Objects.equals(discountDtl.getDiscountType(), TrdDiscountTypeEnum.FG.getType())) { // 满赠
            flag = amt.compareTo(new BigDecimal(discountDtl.getDiscountCondition())) >= 0;
        }
        return flag;
    }


    private void handleSkDiscount(TrdAfterDiscountDtl discount, TrdAfter tar, Map<Long, TrdSupplierAfterDtl> supplierAfterDtlMap) {
        TrdSupplierAfterDtl afterDtl = supplierAfterDtlMap.get(discount.getSupplierAfterDtlId());
        if (afterDtl != null) {
            BigDecimal returnUnitQtyNegate = afterDtl.getReturnUnitQty().negate();
            if (UnitTypeEnum.UNIT_SMALL.getType().equals(afterDtl.getReturnUnitType())) {
                // set秒杀活动商品门店已购数量 返还（小单位）
                redisActivityService.setSkSaleNum(tar.getBranchId(), discount.getDiscountId(), discount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                // set秒杀活动商品已购总数量 返还（小单位）
                redisActivityService.setSkSaleNum(discount.getDiscountId(), discount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
            } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(afterDtl.getReturnUnitType())) {
                // set秒杀活动商品门店已购数量 返还（中单位）
                redisActivityService.setSkMidSaleNum(tar.getBranchId(), discount.getDiscountId(), discount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                // set秒杀活动商品已购总数量 返还（中单位）
                redisActivityService.setSkMidSaleNum(discount.getDiscountId(), discount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
            } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(afterDtl.getReturnUnitType())) {
                // set秒杀活动商品门店已购数量 返还（大单位）
                redisActivityService.setSkLargeSaleNum(tar.getBranchId(), discount.getDiscountId(), discount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                // set秒杀活动商品已购总数量 返还（大单位）
                redisActivityService.setSkLargeSaleNum(discount.getDiscountId(), discount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
            }

        }
    }

    private void handleSpDiscount(TrdAfterDiscountDtl discount, TrdAfter tar, Map<Long, TrdSupplierAfterDtl> supplierAfterDtlMap) {
        TrdSupplierAfterDtl afterDtl = supplierAfterDtlMap.get(discount.getSupplierAfterDtlId());
        if (afterDtl != null) {
            // 获取促销活动数据
            ActivityDTO activityDTO = trdCacheService.getActivityDTO(discount.getDiscountId());
            // 获取 本地售后商品对应的订单数据
            TrdSupplierOrderDtl supplierOrderDtl = trdSupplierOrderDtlMapper.selectById(afterDtl.getSupplierOrderDtlId());

            // 总活动缓存过期时间
            long ruleTime = DateUtils.getRemainSecond(DateUtils.getNowDate(), activityDTO.getEndTime());
            // 总活动使用数量
            BigDecimal saleQty = afterDtl.getReturnQty().multiply(NumberPool.BIGDECIMAL_GROUND);
            // 活动商品已使用数量
            BigDecimal itemSaleQty = afterDtl.getReturnQty().multiply(NumberPool.BIGDECIMAL_GROUND);
            // 验证当前活动存在规则 且 参与活动的商品已全部发货前售后
            if (Objects.nonNull(activityDTO.getTimesRule()) && Objects.equals(supplierOrderDtl.getTotalNum(), supplierOrderDtl.getCancelQty())) {
                switch (SpActivityTimesRuleEnum.formValue(activityDTO.getTimesRule())) {
                    case RULE_LEVEL_0: // 每日一次
                    case RULE_LEVEL_1: // 活动期间内仅一次
                        saleQty = NumberPool.BIGDECIMAL_MAX.subtract(supplierOrderDtl.getCancelQty().subtract(afterDtl.getReturnQty())).multiply(NumberPool.BIGDECIMAL_GROUND);
                        ruleTime = NumberPool.LONG_ONE;
                        break;
                    case RULE_LEVEL_4: // 商品级别每日一次
                    case RULE_LEVEL_5: // 商品级别仅一次
                        saleQty = BigDecimal.ZERO; // 不设置门店活动已购数量
                        ruleTime = NumberPool.LONG_ONE;
                        // 当天商品仅销售一次，将商品活动缓存数量拉到最大
                        // 商品活动期间内仅销售一次，将商品活动缓存数量拉到最大
                        itemSaleQty = NumberPool.BIGDECIMAL_MAX.subtract(supplierOrderDtl.getCancelQty().subtract(afterDtl.getReturnQty())).multiply(NumberPool.BIGDECIMAL_GROUND);
                        break;
                }
            }
            redisActivityService.setSpTotalSaleNum(tar.getBranchId(), discount.getDiscountId(), saleQty, ruleTime, TimeUnit.SECONDS);
//            redisActivityService.setSpSaleNum(tar.getBranchId(), discount.getDiscountId(), discount.getDiscountRuleId(), itemSaleQty, null, null);
//            redisActivityService.setSpSaleNum(discount.getDiscountId(), discount.getDiscountRuleId(), itemSaleQty, null, null);
            // 返还门店已购买数量
            BigDecimal returnUnitQtyNegate = afterDtl.getReturnUnitQty().negate();
            if (UnitTypeEnum.UNIT_SMALL.getType().equals(afterDtl.getReturnUnitType())) {
                redisActivityService.setSpSaleNum(tar.getBranchId(), discount.getDiscountId(), discount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                redisActivityService.setSpSaleNum(discount.getDiscountId(), discount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
            } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(afterDtl.getReturnUnitType())) {
                redisActivityService.setSpMidSaleNum(tar.getBranchId(), discount.getDiscountId(), discount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                redisActivityService.setSpMidSaleNum(discount.getDiscountId(), discount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
            } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(afterDtl.getReturnUnitType())) {
                redisActivityService.setSpLargeSaleNum(tar.getBranchId(), discount.getDiscountId(), discount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                redisActivityService.setSpLargeSaleNum(discount.getDiscountId(), discount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
            }
        }
    }

    private void handleCbDiscount(TrdAfterDiscountDtl discount, TrdAfter tar, Map<Long, TrdSupplierAfterDtl> supplierAfterDtlMap, List<TrdOrderDiscountDtl> orderDiscountDtlList) {
        // 获取活动规则数据
        List<CbRuleDTO> cbRuleDTOList = activityApi.getActivityCbRule(discount.getDiscountId()).getCheckedData()
                .stream().filter(item -> Objects.equals(item.getCbRuleId(), discount.getDiscountRuleId()))
                .collect(Collectors.toList());

        if (ToolUtil.isNotEmpty(cbRuleDTOList)) {
            // 获取组合商品数据
            SpuCombineDTO spuCombineDTO = trdCacheService.getSpuCombineDTO(cbRuleDTOList.get(NumberPool.INT_ZERO).getSpuCombineId());
            if (ToolUtil.isNotEmpty(spuCombineDTO)) {
                // 组合商品数据转换map
                Map<String, SpuCombineDtlDTO> stringListMap = CollectionUtils.convertMap(spuCombineDTO.getCombineDtls(), combineDtl -> StringUtils.format("{}_{}", combineDtl.getSkuId(), combineDtl.getSkuUnitType()));

                // 获取当前售后商品所对应的促销单据 对应的所有订单明细数据
                List<TrdSupplierOrderDtl> supplierOrderDtlList = trdSupplierOrderDtlMapper.selectListByDtlIdList(
                        orderDiscountDtlList.stream()
                                .filter(orderDiscountDtl -> Objects.equals(orderDiscountDtl.getDiscountId(), discount.getDiscountId()) && Objects.equals(orderDiscountDtl.getDiscountRuleId(), discount.getDiscountRuleId()))
                                .map(TrdOrderDiscountDtl::getSupplierOrderDtlId).collect(Collectors.toList())
                );

                // 过滤出售后商品最小单位数量 可以返还促销的最小单位数量
                BigDecimal cancelQty = supplierOrderDtlList.stream().map(tsod -> {
                    String mapKey = StringUtils.format("{}_{}", tsod.getSkuId(), tsod.getOrderUnitType());
                    if (!stringListMap.containsKey(mapKey)) {
                        return BigDecimal.ZERO;
                    }
                    return tsod.getCancelQty().divide(tsod.getOrderUnitSize(), 0, RoundingMode.DOWN).divide(BigDecimal.valueOf(stringListMap.get(mapKey).getQty()), 0, RoundingMode.DOWN);
                }).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);

                if (cancelQty.compareTo(BigDecimal.ZERO) > 0) {
                    redisActivityService.setCbSaleNum(discount.getDiscountId(), discount.getDiscountRuleId(), cancelQty.multiply(NumberPool.BIGDECIMAL_GROUND), null, null);
                }
            }
        }
    }

    private void releaseErpStock(TrdSupplierOrder supplierOrder) {
        ErpStockRequestDTO data = new ErpStockRequestDTO()
                .setB2bSheetNo(supplierOrder.getSupplierOrderNo())
                .setSheetSource(SyncSourceType.B2B.getType())
                .setOrgCode(this.getOrgCode(supplierOrder.getSupplierId()));
        AnntoErpRequestDTO requestDTO = new AnntoErpRequestDTO()
                .setApi("/rollbackStockOccupy")
                .setData(data)
                .setReqId(IdUtils.fastSimpleUUID())
                .setSupplierId(supplierOrder.getSupplierId())
                .setRequestType(RequestType.B2B_STOCK_OCCUPY_ROLLBACK)
                .setB2bRequestType(B2BRequestType.B2B_STOCK_OCCUPY_ROLLBACK)
                .setOperationType(OperationType.UPDATE);
        log.info("订单取消回滚ERP库存占用，入参: {}", com.alibaba.fastjson2.JSON.toJSONString(requestDTO));
        CommonResult<AnntoErpResultDTO<String>> commonResult = anntoErpApi.sendErp(requestDTO);
        log.info("订单取消回滚ERP库存占用，结果: {}", JSON.toJSONString(commonResult));
        if (commonResult == null || commonResult.isError() || commonResult.getCheckedData() == null || !commonResult.getCheckedData().isOk()) {
            log.error("订单取消回滚ERP库存占用失败，supplierOrderNo: {}", supplierOrder.getSupplierOrderNo());
        }
    }

    private String getOrgCode(Long supplierId) {
        // ERP默认组织编码为100
        String orgCode = DEFAULT_ORG_CODE;
        OpensourceDto opensourceDto = trdCacheService.getOpensourceByMerchantId(supplierId);
        if (opensourceDto != null && opensourceDto.getSendCode() != null) {
            String[] group = opensourceDto.getSendCode().split(StringPool.COLON);
            if (group.length > 1 && StringUtils.isNotBlank(group[1])) {
                orgCode = group[1].trim();
            }
        }
        return orgCode;
    }
}
