package com.zksr.trade.controller.orderExpress;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.service.ITrdOrderExpressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressPageReqVO;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressSaveReqVO;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressRespVO;
import com.zksr.trade.convert.orderExpress.TrdOrderExpressConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 订单快递Controller
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Api(tags = "管理后台 - 订单快递接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/express")
public class TrdOrderExpressController {
    @Autowired
    private ITrdOrderExpressService trdOrderExpressService;

    /**
     * 新增订单快递
     */
    @ApiOperation(value = "新增订单快递", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "订单快递", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdOrderExpressSaveReqVO createReqVO) {
        return success(trdOrderExpressService.insertTrdOrderExpress(createReqVO));
    }

    /**
     * 修改订单快递
     */
    @ApiOperation(value = "修改订单快递", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "订单快递", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdOrderExpressSaveReqVO updateReqVO) {
            trdOrderExpressService.updateTrdOrderExpress(updateReqVO);
        return success(true);
    }

    /**
     * 删除订单快递
     */
    @ApiOperation(value = "删除订单快递", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "订单快递", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderExpressIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] orderExpressIds) {
        trdOrderExpressService.deleteTrdOrderExpressByOrderExpressIds(orderExpressIds);
        return success(true);
    }

    /**
     * 获取订单快递详细信息
     */
    @ApiOperation(value = "获得订单快递详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{orderExpressId}")
    public CommonResult<TrdOrderExpressRespVO> getInfo(@PathVariable("orderExpressId") Long orderExpressId) {
        TrdOrderExpress trdOrderExpress = trdOrderExpressService.getTrdOrderExpress(orderExpressId);
        return success(TrdOrderExpressConvert.INSTANCE.convert(trdOrderExpress));
    }

    /**
     * 分页查询订单快递
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得订单快递分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdOrderExpressRespVO>> getPage(@Valid TrdOrderExpressPageReqVO pageReqVO) {
        PageResult<TrdOrderExpress> pageResult = trdOrderExpressService.getTrdOrderExpressPage(pageReqVO);
        return success(TrdOrderExpressConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:express:add";
        /** 编辑 */
        public static final String EDIT = "trade:express:edit";
        /** 删除 */
        public static final String DELETE = "trade:express:remove";
        /** 列表 */
        public static final String LIST = "trade:express:list";
        /** 查询 */
        public static final String GET = "trade:express:query";
        /** 停用 */
        public static final String DISABLE = "trade:express:disable";
        /** 启用 */
        public static final String ENABLE = "trade:express:enable";
    }
}
