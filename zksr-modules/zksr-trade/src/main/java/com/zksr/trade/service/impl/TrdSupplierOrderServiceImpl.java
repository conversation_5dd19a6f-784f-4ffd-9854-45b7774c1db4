package com.zksr.trade.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.domain.vo.openapi.receive.*;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.enums.request.VisualTemplateType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.dictData.DictDataApi;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.trade.api.after.dto.OrderAfterSaveResDTO;
import com.zksr.trade.api.after.vo.OrderAfterRequest;
import com.zksr.trade.api.after.vo.OrderAfterSaveRequest;
import com.zksr.trade.api.order.dto.OrderReceiptRespDTO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.controller.order.vo.SupplierMemoEditReqVO;
import com.zksr.trade.controller.order.vo.TrdOrderPageReqVO;
import com.zksr.trade.domain.*;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.mapper.*;
import com.zksr.trade.mq.TradeMqProducer;
import com.zksr.trade.service.ITrdAfterService;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderPageReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderSaveReqVO;
import com.zksr.trade.service.ITrdSupplierOrderService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.OpenApiConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 入驻商订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Service
@Slf4j
public class TrdSupplierOrderServiceImpl implements ITrdSupplierOrderService {
    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;

    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;

    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private TrdOrderMapper trdOrderMapper;

    @Autowired
    private TrdOrderExpressMapper trdOrderExpressMapper;

    @Autowired
    private ITrdAfterService trdAfterService;

    @Autowired
    private List<ITrdOrderHandlerService> trdOrderHandlerServices;

    @Autowired
    private TrdExpressStatusMapper trdExpressStatusMapper;

    @Autowired
    private DictDataApi dictDataApi;

    @Autowired
    private TradeMqProducer tradeMqProducer;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private TrdSupplierAfterDtlMapper trdSupplierAfterDtlMapper;

    @Autowired
    private TrdSupplierAfterMapper trdSupplierAfterMapper;

    @Autowired
    private SkuApi skuApi;

    @Autowired
    private TrdOrderLogMapper orderLogMapper;

    /**
     * 新增入驻商订单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdSupplierOrder(TrdSupplierOrderSaveReqVO createReqVO) {
        // 插入
        TrdSupplierOrder trdSupplierOrder = HutoolBeanUtils.toBean(createReqVO, TrdSupplierOrder.class);
        trdSupplierOrderMapper.insert(trdSupplierOrder);
        // 返回
        return trdSupplierOrder.getSupplierOrderId();
    }

    /**
     * 修改入驻商订单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdSupplierOrder(TrdSupplierOrderSaveReqVO updateReqVO) {
        trdSupplierOrderMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, TrdSupplierOrder.class));
    }

    /**
     * 删除入驻商订单
     *
     * @param supplierOrderId 入驻商订单id
     */
    @Override
    public void deleteTrdSupplierOrder(Long supplierOrderId) {
        // 删除
        trdSupplierOrderMapper.deleteById(supplierOrderId);
    }

    /**
     * 批量删除入驻商订单
     *
     * @param supplierOrderIds 需要删除的入驻商订单主键
     * @return 结果
     */
    @Override
    public void deleteTrdSupplierOrderBySupplierOrderIds(Long[] supplierOrderIds) {
        for (Long supplierOrderId : supplierOrderIds) {
            this.deleteTrdSupplierOrder(supplierOrderId);
        }
    }

    /**
     * 获得入驻商订单
     *
     * @param supplierOrderId 入驻商订单id
     * @return 入驻商订单
     */
    @Override
    public TrdSupplierOrder getTrdSupplierOrder(Long supplierOrderId) {
        return trdSupplierOrderMapper.selectById(supplierOrderId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<TrdSupplierOrder> getTrdSupplierOrderPage(TrdSupplierOrderPageReqVO pageReqVO) {
        return trdSupplierOrderMapper.selectPage(pageReqVO);
    }

    /**
     * OpenApi销售订单出库回传
     *
     * @param vo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveOrderOutboundReturn(OrderOutboundReturnVO vo) {
        log.info("开始处理销售订单出库回传data:{}", vo);
        //入驻商订单号
        String supplierOrderNo = vo.getSupplierOrderNo();

        //校验
        if (ToolUtil.isEmpty(supplierOrderNo)) {
            throw exception(OPEN_TRD_SUPPLIER_ORDER_NO_IS_NULL);
        }

        //获取入驻商订单主表
        TrdSupplierOrder supplierOrder = trdSupplierOrderMapper.selectOrderBySupplierOrderNo(supplierOrderNo);

        //校验
        if (ToolUtil.isEmpty(supplierOrder)) {
            throw exception(OPEN_TRD_SUPPLIER_ORDER_IS_NULL);
        }

        //获取订单主表  用于获取门店信息
        TrdOrder order = trdOrderMapper.getOrderByOrderNo(new TrdOrderPageReqVO(supplierOrder.getOrderNo()));

        //获取门店信息
        BranchDTO branchDTO = trdCacheService.getBranchDTO(order.getBranchId());

        //平台商ID
        Long sysCode = supplierOrder.getSysCode();

        //获取物流信息数据字段
        List<SysDictData> sysDictData = dictDataApi.selectDictDataList(new SysDictData("sys_express_company_code"));
        //组装物流信息Map
        Map<String, String> expressDataMap = sysDictData.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));


        //外部单号在订单回调是否已经绑定
        //设置外部单号
        if (ToolUtil.isNotEmpty(vo.getSourceOrderNo())) {
            supplierOrder.setDeliveryState(SupplierOrderStatusEnum.WAIT_RECEIVED.getCode());

            supplierOrder.setSourceOrderNo(vo.getSourceOrderNo());
            //supplierOrder.setMemo(Objects.nonNull(supplierOrder.getMemo()) ? supplierOrder.getMemo() + "," + vo.getMemo() : vo.getMemo());
            trdSupplierOrderMapper.updateById(supplierOrder);
        }


        //获取入驻商详情信息
        List<TrdSupplierOrderDtl> trdSupplierOrderDtlList = trdSupplierOrderDtlMapper.selectListBySupplierOrderNo(supplierOrderNo);

        //回传的订单详情数据
        List<OrderOutboundReturnDetailVO> detailVoList = vo.getDetailVoList();

        //回传订单详情数量差异集合
        List<OrderOutboundReturnDetailCyVO> cyVOList = new ArrayList<>();

        //物流信息集合
        List<TrdOrderExpress> expressList = new ArrayList<>();

        //回传的订单详情数据转为Map  key: 第三方商品编号 - 行号
        Map<String, OrderOutboundReturnDetailVO> detailVOMap = detailVoList.stream()
                .collect(Collectors.toMap(x -> x.getErpItemNo() + StringPool.DASH + x.getLineNum(), x -> x));

        //处理回传的订单信息
        log.info("开始处理销售订单出库回传订单详情数据trdSupplierOrderDtlList:{}", trdSupplierOrderDtlList);
        log.info("开始处理销售订单出库回传详情商品数据detailVOMap:{}", detailVOMap);

        //组装订单详情数据
        //是否校验订单详情商品条数
        if (vo.isCheckDetailNumFlag()) {
            //校验订单详情
            if (trdSupplierOrderDtlList.size() != detailVoList.size()) {
                throw exception(OPEN_TRD_SUPPLIER_ORDER_DEL_SIZE_NONE);
            }
            assembleDtlListByExact(trdSupplierOrderDtlList, detailVOMap, supplierOrder, expressDataMap, branchDTO, expressList, cyVOList);
        } else {
            assembleDtlList(trdSupplierOrderDtlList, detailVOMap, supplierOrder, expressDataMap, branchDTO, expressList, cyVOList);
        }

        log.info("更新销售订单详情data:{}", trdSupplierOrderDtlList);
        //更新订单详情的发货信息
        trdSupplierOrderDtlMapper.updateBatch(trdSupplierOrderDtlList);

        //如果存在差异 生成仅退款的售后单(发货前售后)
        if (!cyVOList.isEmpty()) {
            supplierAfterSave(cyVOList, order, supplierOrder);
        }

        //修改订单详情中 状态不是取消 并且是待发货的改为待收货
        //设置订单状态
        trdSupplierOrderDtlMapper.updateBatchDeliveryStateBySupplierOrderNo(supplierOrderNo, DeliveryStatusEnum.WAIT_SH.getCode(), DeliveryStatusEnum.WAIT_FH.getCode());

        if (!expressList.isEmpty()) {
            //新增物流信息
            log.info("更新销售订单物流信息data:{}", expressList);
            trdOrderExpressMapper.insertBatch(expressList);
        }

        //写入物流流水
        trdExpressStatusMapper.insert(new TrdExpressStatus(vo.getSupplierOrderNo(), ReceiveLogisticsStatusEnum.DPS.getCode(), sysCode));

    }

    @Override
    public List<TrdSupplierOrder> getSupplierOrderListByOrderNo(String orderNo) {
        return trdSupplierOrderMapper.selectListByOrderNo(orderNo);
    }

    @Override
    public TrdSupplierOrder getSupplierOrderBySupplierOrderNo(String supplierOrderNo) {
        return trdSupplierOrderMapper.selectOrderBySupplierOrderNo(supplierOrderNo);
    }

    @Override
    public void updateByPushStatus(String supplierOrderNo, Integer pushStatus) {
        trdSupplierOrderMapper.updateByPushStatus(supplierOrderNo, pushStatus);
    }

    /**
     * 批量推送入驻商订单
     *
     * @param supplierOrderNoList
     * @return
     */
    @Override
    public String batchSyncSupplierOrder(List<String> supplierOrderNoList) {
        //获取入驻商编号
        Long supplierId = SecurityUtils.getLoginUser().getSysUser().getSupplierId();
        if (ToolUtil.isEmpty(supplierId)) {
            throw exception(TRD_SUPPLIER_ORDER_NOT_ROLE);
        }
        //校验该入驻商是否对接第三方系统 获取该入驻商订单可视化模板
        if (ToolUtil.isEmpty(trdCacheService.getVisualDetailBySupplier(supplierId + StringPool.COLON + VisualTemplateType.ORDER_TYPE.getType()))) {
            throw exception(TRD_SUPPLIER_ORDER_NOT_TEMPLATTE);
        }


        //已推送信息拼接符
        StringBuilder msg = new StringBuilder();
        String result;

        //获取订单信息
        List<TrdSupplierOrder> supplierOrderList = trdSupplierOrderMapper.selectListBySupplierOrderNoList(supplierOrderNoList);
        Map<String, TrdSupplierOrder> supplierOrderMap = supplierOrderList.stream().collect(Collectors.toMap(TrdSupplierOrder::getSupplierOrderNo, x -> x));

        //推送订单
        Set<String> supplierOrderNoSet = new HashSet<>(supplierOrderNoList);
        for (String supplierOderNo : supplierOrderNoSet) {
            TrdSupplierOrder trdSupplierOrder = supplierOrderMap.get(supplierOderNo);
            //校验
            if (ToolUtil.isEmpty(trdSupplierOrder)) {
                msg.append(StringUtils.format("<span style='color:red'>入驻商订单号：{}</span>未获取到入驻商订单信息。<br/>", supplierOderNo));
                continue;
            }
            if (ORDER_SYNC_FLAG_1.equals(trdSupplierOrder.getPushStatus()) || ORDER_SYNC_FLAG_2.equals(trdSupplierOrder.getPushStatus())) {
                msg.append(StringUtils.format("<span style='color:red'>入驻商订单号：{}</span>已推送给第三方系统。<br/>", supplierOderNo));
                continue;
            }
            //发送同步销售入驻商订单MQ
            tradeMqProducer.sendSyncSupplierOrderEvent(new SyncDataDTO(supplierOderNo, supplierId, VisualTemplateType.ORDER_TYPE.getType(), trdSupplierOrder.getSysCode()));
        }

        if (ObjectUtil.isEmpty(msg)) {
            result = "已全部推送";
        } else {
            result = msg.substring(0, msg.lastIndexOf("<br/>"));
        }

        return result;
    }

    /**
     * 校验订单是否是手动推送第三方
     *
     * @param
     */
    @Override
    public Boolean checkOrderSync() {
        Boolean result = false;
        //获取入驻商编号
        Long supplierId = SecurityUtils.getLoginUser().getSysUser().getSupplierId();
        if (ToolUtil.isEmpty(supplierId)) {
            return result;
        }
        //校验该入驻商是否对接第三方系统 获取该入驻商订单可视化模板
        if (ToolUtil.isEmpty(trdCacheService.getVisualDetailBySupplier(supplierId + StringPool.COLON + VisualTemplateType.ORDER_TYPE.getType()))) {
            return result;
        }
        //校验是否开启手动推送
        OpensourceDto opensourceDto = trdCacheService.getOpensourceByMerchantId(supplierId);
        if (ToolUtil.isEmpty(opensourceDto) || ToolUtil.isEmpty(opensourceDto.getOrderAutoPush()) || SysYesNoEnum.YES.getCode().equals(opensourceDto.getOrderAutoPush())) {
            return result;
        }
        return true;
    }

    @Override
    public void syncOrderResponseSuccess(SyncDataDTO data) {
        //推送成功后更改推送标志
        updateByPushStatus(data.getDataId(), ORDER_SYNC_FLAG_1);

        //写入待出库物流流水
        trdExpressStatusMapper.insert(new TrdExpressStatus(data.getDataId(), ReceiveLogisticsStatusEnum.DCK.getCode(), data.getSysCode()));

/*        //更新库存同步流水
        syncOrderStockCache(data);*/
    }


    @Override
    public void supplierOrderMemoEdit(SupplierMemoEditReqVO reqVO) {
        TrdSupplierOrder supplierOrder = trdSupplierOrderMapper.selectById(reqVO.getSupplierOrderId());
        if (ToolUtil.isEmpty(supplierOrder)) {
            throw exception(TRD_SUPPLIER_ORDER_NOT_EXISTS);
        }
        supplierOrder.setMemo(reqVO.getSupplierOrderMemo());
        trdSupplierOrderMapper.updateById(supplierOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderReceiveCallback(OrderReceiveCallbackVO vo) {
        //更新库存信息
        syncOrderStockCache(vo);

        //绑定第三方订单编号
        TrdSupplierOrder trdSupplierOrder = trdSupplierOrderMapper.selectOrderBySupplierOrderNo(vo.getSupplierOrderNo());
        //校验
        if (ToolUtil.isEmpty(trdSupplierOrder)) {
            log.info("同步订单 同步库存缓存失败，未获取到对应的订单信息:{}", vo);
        }
        trdSupplierOrder.setSourceOrderNo(vo.getSourceOrderNo());
        trdSupplierOrderMapper.updateById(trdSupplierOrder);

        //接收成功后更改推送标志
        updateByPushStatus(trdSupplierOrder.getSupplierOrderNo(), ORDER_SYNC_FLAG_2);

    }

    @Override
    public List<OrderReceiptRespDTO> getOrderReceiptInfoBySupplierOrderNo(SyncReceiptSendDTO reqVo) {
        return trdSupplierOrderMapper.getOrderReceiptInfoBySupplierOrderNo(reqVo);
    }

    @Override
    public List<OrderReceiptRespDTO> getOrderAfterReceiptInfoBySupplierOrderNo(SyncReceiptSendDTO reqVo) {
        return trdSupplierOrderMapper.getOrderAfterReceiptInfoBySupplierOrderNo(reqVo);
    }

    @Override
    public void orderCancel(OrderCancelVO vo) {

        //入驻商订单号
        String supplierOrderNo = vo.getSupplierOrderNo();

        //校验
        if (ToolUtil.isEmpty(supplierOrderNo)) {
            throw exception(OPEN_TRD_SUPPLIER_ORDER_NO_IS_NULL);
        }

        //获取入驻商订单主表
        TrdSupplierOrder supplierOrder = trdSupplierOrderMapper.selectOrderBySupplierOrderNo(supplierOrderNo);

        //校验
        if (ToolUtil.isEmpty(supplierOrder)) {
            throw exception(OPEN_TRD_SUPPLIER_ORDER_IS_NULL);
        }

        //获取订单主表
        TrdOrder order = trdOrderMapper.getOrderByOrderNo(new TrdOrderPageReqVO(supplierOrder.getOrderNo()));

        //整单取消 组装售后流程数据
        //获取入驻商详情信息
        List<TrdSupplierOrderDtl> trdSupplierOrderDtlList = trdSupplierOrderDtlMapper.selectListBySupplierOrderNo(supplierOrderNo);


        //回传订单详情数量差异集合
        List<OrderOutboundReturnDetailCyVO> cyVOList = new ArrayList<>();

        //销售订单详情
        trdSupplierOrderDtlList.forEach(x -> {

            //订单商品最小单位数量
            BigDecimal orderQty = BigDecimal.valueOf(x.getOrderUnitQty()).multiply(x.getOrderUnitSize());

            //订单商品最小单位金额
            BigDecimal totalAmt = x.getTotalAmt();

            //组装差异数据
            OrderOutboundReturnDetailCyVO cyVo = new OrderOutboundReturnDetailCyVO();
            cyVo.setSupplierOrderDtlId(x.getSupplierOrderDtlId());
            cyVo.setTotalNum(orderQty);
            cyVo.setRefundPrice(x.getExactPrice());
            cyVo.setRefundQty(orderQty);
            cyVo.setRefundAmt(totalAmt);
            cyVo.setUnitType(UnitTypeEnum.UNIT_SMALL.getType());
            cyVOList.add(cyVo);

        });

        //生成整单取消(发货前售后)
        supplierAfterSave(cyVOList, order, supplierOrder);


    }

    @Override
    public List<Long> deleteSpuCheckSupplierOrderDtl(Long[] spuIds) {
        return trdSupplierOrderDtlMapper.deleteSpuCheckSupplierOrderDtl(spuIds);
    }

    /**
     * 单入驻商订单取消回调
     *
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void orderCancelReceiveCallback(OrderCancelReceiveCallbackVO vo) {
        log.info("接到订单取消回调，vo:{}", JSON.toJSONString(vo));
        String supplierOrderNo = vo.getSupplierOrderNo();

        TrdSupplierOrder supplierOrder = trdSupplierOrderMapper.selectOrderBySupplierOrderNo(supplierOrderNo);
        if (supplierOrder == null) {
            log.error("不存在入驻商订单：{}", supplierOrderNo);
            return;
        }

        Long orderId = supplierOrder.getOrderId();

        TrdOrder orderVo = trdOrderMapper.selectById(orderId);

        Map<Long, List<TrdSupplierOrderDtl>> supplierOrderDtlMap = trdSupplierOrderDtlMapper.selectListByOrderId(orderId).stream().collect(Collectors.groupingBy(TrdSupplierOrderDtl::getSupplierOrderId));

        // 生成售后单组装数据
        OrderAfterSaveRequest request = new OrderAfterSaveRequest();
        request.setOrderId(orderId);
        request.setOrderNo(orderVo.getOrderNo());
        request.setAfterType(AfterTypeEnum.REDUNDONLY.getState());
        request.setDeliveryState(DeliveryStatusEnum.WAIT_FH.getCode());
        List<OrderAfterRequest.SupplierOrder> supplierOrderReqs = Lists.newArrayList();
        // 入驻商订单
        OrderAfterRequest.SupplierOrder supplierOrderReq = new OrderAfterRequest.SupplierOrder();
        supplierOrderReq.setSupplierOrderId(supplierOrder.getSupplierOrderId());
        supplierOrderReq.setSupplierOrderDtls(new ArrayList<>());

        List<TrdSupplierOrderDtl> trdSupplierOrderDtls = supplierOrderDtlMap.get(supplierOrder.getSupplierOrderId());
        if (CollectionUtils.isNotEmpty(trdSupplierOrderDtls)) {
            // 入驻商订单明细
            List<OrderAfterRequest.SupplierOrder.SupplierOrderDtl> supplierOrderDtlReqs = trdSupplierOrderDtls.stream().map(dtl -> {
                OrderAfterRequest.SupplierOrder.SupplierOrderDtl supplierOrderDtlReq = new OrderAfterRequest.SupplierOrder.SupplierOrderDtl();
                supplierOrderDtlReq.setSupplierOrderDtlId(dtl.getSupplierOrderDtlId());
                supplierOrderDtlReq.setUnitType(dtl.getOrderUnitType());
                supplierOrderDtlReq.setTotalNum(dtl.getTotalNum());
                supplierOrderDtlReq.setRefundQty(new BigDecimal(dtl.getOrderUnitQty()));
                supplierOrderDtlReq.setRefundPrice(dtl.getOrderUnitPrice());
                supplierOrderDtlReq.setAfterPhase(StatusConstants.AFTER_PHASE_1);
                return supplierOrderDtlReq;
            }).collect(Collectors.toList());
            supplierOrderReq.getSupplierOrderDtls().addAll(supplierOrderDtlReqs);
        }
        supplierOrderReqs.add(supplierOrderReq);
        request.setSupplierOrders(supplierOrderReqs);

        // 生成售后单并执行取消逻辑
        OrderAfterSaveResDTO resp = trdAfterService.saveOrderAfter(request);

        updateStatus(supplierOrderNo, orderId);
        // 添加订单明细日志
        saveOrderLog(orderVo, DeliveryStatusEnum.CANCEL.getCode(), StatusConstants.OPER_TYPE_UPDATE, DeliveryStatusEnum.CANCEL.getContent(), null, null);

    }

    /**
     * 更新订单状态
     *
     * @param supplierOrderNo 入驻商订单号
     * @param orderId
     */
    private void updateStatus(String supplierOrderNo, Long orderId) {
        /**
         * 更新入驻商订单支付状态为2 已取消
         */
        trdSupplierOrderMapper.update(null, new LambdaUpdateWrapper<TrdSupplierOrder>()
                .set(TrdSupplierOrder::getDeliveryState, SupplierOrderStatusEnum.CANCELED.getCode())
                .set(TrdSupplierOrder::getPayState, PayStateEnum.PAY_CANCEL.getCode())
                .set(TrdSupplierOrder::getUpdateTime, DateUtils.getNowDate())
                .eq(TrdSupplierOrder::getSupplierOrderNo, supplierOrderNo));

        /**
         * 根据订单ID将订单商品状态更改成已取消
         */
        trdSupplierOrderDtlMapper.update(null, new LambdaUpdateWrapper<TrdSupplierOrderDtl>()
//                .set(TrdSupplierOrderDtl::getPayState, PayStateEnum.PAY_CANCEL.getCode())
                .set(TrdSupplierOrderDtl::getDeliveryState, DeliveryStatusEnum.CANCEL.getCode())
                .set(TrdSupplierOrderDtl::getUpdateTime, DateUtils.getNowDate())
                .eq(TrdSupplierOrderDtl::getSupplierOrderNo, supplierOrderNo));

        /**
         * 查询主单下的所有入驻商订单是否都已取消
         */
        List<TrdSupplierOrder> supplierOrders = trdSupplierOrderMapper.selectListByOrderId(orderId);
        boolean isAllCancelled = supplierOrders.stream().allMatch(t -> PayStateEnum.PAY_CANCEL.getCode().equals(t.getPayState()));
        if (isAllCancelled) {
            // 所有入驻商订单都取消，则更改主单状态为已取消
            trdOrderMapper.update(null, new LambdaUpdateWrapper<TrdOrder>()
                    .set(TrdOrder::getPayState, PayStateEnum.PAY_CANCEL.getCode())
                    .set(TrdOrder::getUpdateTime, DateUtils.getNowDate())
                    .eq(TrdOrder::getOrderId, orderId));

        }
    }


    private void validateTrdSupplierOrderExists(Long supplierOrderId) {
        if (trdSupplierOrderMapper.selectById(supplierOrderId) == null) {
            throw exception(TRD_SUPPLIER_ORDER_NOT_EXISTS);
        }
    }


    /**
     * 生成售后单
     *
     * @param cyVOList
     * @param order
     * @param supplierOrder
     */
    public void supplierAfterSave(List<OrderOutboundReturnDetailCyVO> cyVOList, TrdOrder order, TrdSupplierOrder supplierOrder) {
        OrderAfterSaveRequest request = new OrderAfterSaveRequest();

        //组装售后主订单数据
        request.setAfterType(AfterTypeEnum.REDUNDONLY.getState());
        request.setSource(SourceType.OTHERSYSTEM.getType());
        request.setOrderId(order.getOrderId());
        request.setOrderNo(order.getOrderNo());
        request.setDeliveryState(DeliveryStatusEnum.WAIT_FH.getCode());

        //组装售后入驻商订单主表信息(差异售后单)
        OrderAfterRequest.SupplierOrder supplierOrderAfter = new OrderAfterRequest.SupplierOrder();
        supplierOrderAfter.setSupplierId(supplierOrder.getSupplierId());
        supplierOrderAfter.setSupplierOrderId(supplierOrder.getSupplierOrderId());
        supplierOrderAfter.setTransNo(SheetTypeConstants.SHC);

        //转换售后入驻商详情
        List<OrderAfterRequest.SupplierOrder.SupplierOrderDtl> supplierOrderDtlAfterList = HutoolBeanUtils.toBean(cyVOList, OrderAfterRequest.SupplierOrder.SupplierOrderDtl.class);

        supplierOrderAfter.setSupplierOrderDtls(supplierOrderDtlAfterList);
        request.setSupplierOrders(ListUtil.toList(supplierOrderAfter));
        log.info("生成差异售后单:{}", request);
        trdAfterService.saveOrderAfter(request);

        //更新同步库存
        syncAfterStockCache(supplierOrder.getSupplierOrderNo(), supplierOrder.getSupplierId());
    }

    /**
     * 同步订单 更新库存信息（同步库存缓存、商品总库存信息）
     *
     * @param vo
     */
    public void syncOrderStockCache(OrderReceiveCallbackVO vo) {
        //查询入驻商订单详情
        List<TrdSupplierOrderDtl> trdSupplierOrderDtls = trdSupplierOrderDtlMapper.selectListBySupplierOrderNo(vo.getSupplierOrderNo());

        //校验
        if (ToolUtil.isEmpty(trdSupplierOrderDtls) || trdSupplierOrderDtls.isEmpty()) {
            log.info("同步订单 同步库存缓存失败，未获取到对应的订单详情信息:{}", vo);
            throw exception(OPEN_TRD_SUPPLIER_ORDER_SYNC_STOCK, vo);
        }

        //订单详情更新库存数量
        BigDecimal updateStockQty = BigDecimal.ZERO;


        //批量修改 更新同步库存信息
        for (TrdSupplierOrderDtl dtl : trdSupplierOrderDtls) {
            //校验该订单商品详情是否已经同步
            if (OPEN_FLAG_1.equals(dtl.getSyncStock())) {
                continue;
            }

            //更新同步库存缓存信息
            //商品最小单位信息 数量
            BigDecimal orderTotalNum = dtl.getTotalNum();
            BigDecimal orderCancelQty = dtl.getCancelQty();

            //计算商品最小单位同步库存信息
            BigDecimal updateStock = orderTotalNum.subtract(orderCancelQty);
            //更新同步库存缓存  这个是增量
//            redisStockService.incrSkuSyncedQty(dtl.getSkuId(),updateStock);
            //释放库存
            try {
                redisStockService.incrSkuOccupiedQty(dtl.getSkuId(), updateStock.negate());
                redisStockService.incrSkuStockQty(dtl.getSkuId(), updateStock.negate());
            } catch (Exception e) {
                log.error("订单回调更新总库存和已占用库存失败,error:", e);
            }
            // 更新数据库库存
            SkuDTO sku = skuApi.getBySkuId(dtl.getSkuId()).getCheckedData();
            if (sku != null) {
                sku.setStock(sku.getStock().subtract(updateStock));
//                sku.setOccupiedQty(sku.getOccupiedQty().subtract(updateStock));
                skuApi.updateBySku(sku);
            }

            //设置库存同步标识
            dtl.setSyncStock(OPEN_FLAG_1);
            updateStockQty = updateStockQty.add(BigDecimal.ONE);

        }

        //更新商品总库存信息
        List<OrderReceiveCallbackStockVO> detail = vo.getDetail();
        //校验是否推送了订单商品总库存信息
        if (detail != null && !detail.isEmpty()) {
            //组装推送的订单库存信息
            Map<String, OrderReceiveCallbackStockVO> stockMap = detail.stream()
                    .collect(Collectors.toMap(OrderReceiveCallbackStockVO::getErpItemNo,
                            x -> x,
                            (existing, replacement) -> existing)); // 处理键冲突，保留第一个遇到的值

            //筛选该订单涉及库存的商品信息 进行库存更新
            for (Long skuId : trdSupplierOrderDtls.stream().map(TrdSupplierOrderDtl::getSkuId).collect(Collectors.toSet())) {
                SkuDTO sku = skuApi.getBySkuId(skuId).getCheckedData();
                OrderReceiveCallbackStockVO stockVO = stockMap.get(sku.getSourceNo());
                if (ToolUtil.isEmpty(stockVO) || ToolUtil.isEmpty(stockVO.getLastUpdateTime())) {
                    //如果该订单商品未匹配到第三方推送的库存信息 则说明未推送该商品库存信息 直接过滤
                    log.info("订单接收成功通知接口--单据编号为{}，更新商品SkuID：{}库存失败，未匹配到第三方推送的该商品库存信息", vo.getSupplierOrderNo(), skuId);
                } else {
                    // 获取数据库中的最新库存更新时间
                    Date lastUpdateDate = sku.getLastUpdateTime();
                    // 如果 lastUpdateTime 为空，直接执行更新
                    if (lastUpdateDate == null || stockVO.getLastUpdateTime().after(lastUpdateDate)) {
                        // 更新库存
                        sku.setStock(stockVO.getStock());
                        sku.setLastUpdateTime(stockVO.getLastUpdateTime());
                        // 执行数据库更新操作
                        skuApi.updateBySku(sku);
                        // 刷新缓存
                        redisStockService.setSkuStock(sku.getSkuId(), sku.getStock());
                        log.info("订单接收成功通知接口--单据编号为{}，更新商品SkuID：{},库存数量：{},和最后库存更新时间:{},数据库中更新前的更新时间:{}", vo.getSupplierOrderNo(), skuId, stockVO.getStock(), stockVO.getLastUpdateTime(), lastUpdateDate);
                    } else {
                        // 如果不满足更新时间条件 直接过滤
                        log.info("订单接收成功通知接口--单据编号为{}，更新商品SkuID：{}库存失败，本次库存更新时间:{},数据库中的更新时间:{}，本次库存更新时间小于当前数据库中的更新时间", vo.getSupplierOrderNo(), skuId, stockVO.getLastUpdateTime(), lastUpdateDate);
                    }
                }

            }

        }


        //更新
        if (updateStockQty.compareTo(BigDecimal.ZERO) > 0) {
            trdSupplierOrderDtlMapper.updateBatch(trdSupplierOrderDtls);
        }
    }


    /**
     * 同步差异售后订单 同步库存缓存
     *
     * @param supplierOrderNo
     */
    public void syncAfterStockCache(String supplierOrderNo, Long supplierId) {
        //获取该入驻商订单的差异售后订单
        TrdSupplierAfter trdSupplierAfter = trdSupplierAfterMapper.selectAfterBySupplierOrderNoAndSupplierIdAndTransNo(supplierOrderNo, supplierId, ListUtil.toList(SheetTypeConstants.SHC, "SHC"));
        if (ToolUtil.isEmpty(trdSupplierAfter)) {
            log.info("差异售后订单：更新同步库存缓存失败，未获取到对应的差异订单信息，data:{}", supplierOrderNo);
            throw exception(OPEN_TRD_SUPPLIER_AFTER_SHC_SYNC_STOCK, supplierOrderNo);
        }

        //查询入驻商差异订单详情
        List<TrdSupplierAfterDtl> supplierAfterDtls = trdSupplierAfterDtlMapper.selectListBySupplierAfterNo(trdSupplierAfter.getSupplierAfterNo());
        if (ToolUtil.isEmpty(supplierAfterDtls) || supplierAfterDtls.isEmpty()) {
            log.info("差异售后订单：更新同步库存缓存失败，未获取到对应的差异订单详情信息，data:{}", supplierOrderNo);
            throw exception(OPEN_TRD_SUPPLIER_AFTER_SHC_SYNC_STOCK, supplierOrderNo);
        }

        BigDecimal updateStockQty = BigDecimal.ZERO;
        //批量修改 同步库存缓存信息
        for (TrdSupplierAfterDtl dtl : supplierAfterDtls) {
            //校验该订单商品详情是否已经同步
            if (OPEN_FLAG_1.equals(dtl.getSyncStock())) {
                continue;
            }
            //计算商品最小单位同步库存信息 退货 取负数
            BigDecimal updateStock = dtl.getReturnQty().negate();
            //更新同步库存缓存  这个是增量
//            redisStockService.incrSkuSyncedQty(dtl.getSkuId(), updateStock);
            //更新销售库存缓存  这个是增量
            redisStockService.incrSkuSaledQty(dtl.getSkuId(), updateStock);
            //设置库存同步标识
            dtl.setSyncStock(OPEN_FLAG_1);
            updateStockQty = updateStockQty.add(BigDecimal.ONE);
        }

        //更新
        if (updateStockQty.compareTo(BigDecimal.ZERO) > 0) {
            trdSupplierAfterDtlMapper.updateBatch(supplierAfterDtls);
        }
    }

    /**
     * 组装订单详情相关数据 精确组装 要求：订单发货详情数据需与订单详情数量一致
     *
     * @param trdSupplierOrderDtlList 订单详情数据集合
     * @param detailVOMap             订单发货详情数据Map集合
     * @param supplierOrder           入驻商订单信息
     * @param expressDataMap          物流数据字典信息
     * @param branchDTO               门店信息
     * @param expressList             物流集合信息（需要组装的数据）
     * @param cyVOList                发货差异数据集合（需要组装的数据）
     */
    private void assembleDtlListByExact(List<TrdSupplierOrderDtl> trdSupplierOrderDtlList,
                                        Map<String, OrderOutboundReturnDetailVO> detailVOMap,
                                        TrdSupplierOrder supplierOrder,
                                        Map<String, String> expressDataMap,
                                        BranchDTO branchDTO,
                                        List<TrdOrderExpress> expressList,
                                        List<OrderOutboundReturnDetailCyVO> cyVOList) {

        //销售订单详情
        trdSupplierOrderDtlList.forEach(x -> {
            SpuDTO spuDTO = trdCacheService.getSpuDTO(x.getSpuId());
            log.info("匹配商品SPu信息spuDTO:{}", spuDTO);
            //匹配回传的数据
            OrderOutboundReturnDetailVO detailVO = detailVOMap.get(spuDTO.getSourceNo() + StringPool.DASH + x.getLineNum());
            //校验行号商品详情是否匹配
            if (ToolUtil.isEmpty(detailVO)) {
                throw exception(OPEN_TRD_SUPPLIER_ORDER_DEL_LINE_NONE, spuDTO.getSourceNo());
            }

            //订单商品最小单位数量
            BigDecimal orderQty = BigDecimal.valueOf(x.getOrderUnitQty()).multiply(x.getOrderUnitSize());

            //校验  发货数量不能大于订单数量
            if (detailVO.getSendUnitQty().compareTo(orderQty) > 0) {
                throw exception(OPEN_TRD_SUPPLIER_ORDER_DEL_CHECK_DELIVER_QTY, detailVO.getErpItemNo());
            }

            //设置发货数量信息(最小单位)
            x.setSendUnit(spuDTO.getMinUnit().toString());
            x.setSendUnitType(UnitTypeEnum.UNIT_SMALL.getType());
            x.setSendUnitQty(detailVO.getSendUnitQty());
            x.setSendQty(detailVO.getSendUnitQty());
            x.setSendUnitSize(NumberPool.LONG_ONE);

            //x.setMemo(Objects.nonNull(x.getMemo()) ? x.getMemo() + "," + detailVO.getMemo() : detailVO.getMemo());

            //校验物流信息
            if (ToolUtil.isNotEmpty(detailVO.getExpressComNo()) && ToolUtil.isNotEmpty(detailVO.getExpressNo())) {
                //设置物流信息
                TrdOrderExpress express = new TrdOrderExpress();
                express.setOrderId(supplierOrder.getOrderId());
                express.setOrderNo(supplierOrder.getOrderNo());
                express.setSupplierOrderDtlId(x.getSupplierOrderDtlId());
                express.setSupplierOrderDtlNo(x.getSupplierOrderDtlNo());
                express.setExpressNo(detailVO.getExpressNo());
                express.setExpressComNo(detailVO.getExpressComNo());
                express.setExpressCom(ToolUtil.isNotEmpty(expressDataMap.get(detailVO.getExpressComNo())) ?
                        expressDataMap.get(detailVO.getExpressComNo()) : detailVO.getExpressComNo());
                express.setReceiveMan(branchDTO.getContactName());
                express.setReceivePhone(branchDTO.getContactPhone());
                express.setAddress(branchDTO.getBranchAddr());
                express.setRealInTransitTime(new Date());
                express.setState(NumberPool.LONG_ZERO);
                express.setSysCode(supplierOrder.getSysCode());
                expressList.add(express);
            }


            //校验是否存在差异
            if (detailVO.getSendUnitQty().compareTo(orderQty) != 0) {
                //差异数量
                BigDecimal cyQty = orderQty.subtract(detailVO.getSendUnitQty());

                //差异金额  取六位小数
                BigDecimal cyAmt = cyQty.multiply(x.getExactPrice()).setScale(StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);

                //组装差异数据
                OrderOutboundReturnDetailCyVO cyVo = new OrderOutboundReturnDetailCyVO();
                cyVo.setSupplierOrderDtlId(x.getSupplierOrderDtlId());
                cyVo.setTotalNum(orderQty);
                cyVo.setRefundPrice(x.getExactPrice());
                cyVo.setRefundQty(cyQty);
                cyVo.setRefundAmt(cyAmt);
                cyVo.setUnitType(UnitTypeEnum.UNIT_SMALL.getType());
                cyVOList.add(cyVo);

            /*    //设置订单详情的取消数据
                x.setCancelQty(cyQty);
                x.setCancelAmt(cyAmt);
                x.setCancelUnit(spuDTO.getMinUnit().toString());
                x.setCancelUnitType(UnitTypeEnum.UNIT_SMALL.getType());
                x.setCancelUnitQty(cyQty);
                x.setSendUnitSize(NumberPool.LONG_ONE);*/
            }

        });
    }


    /**
     * 组装订单详情相关数据 不要求订单发货详情数据需与订单详情数量一致
     *
     * @param trdSupplierOrderDtlList 订单详情数据集合
     * @param detailVOMap             订单发货详情数据Map集合
     * @param supplierOrder           入驻商订单信息
     * @param expressDataMap          物流数据字典信息
     * @param branchDTO               门店信息
     * @param expressList             物流集合信息（需要组装的数据）
     * @param cyVOList                发货差异数据集合（需要组装的数据）
     */
    private void assembleDtlList(List<TrdSupplierOrderDtl> trdSupplierOrderDtlList,
                                 Map<String, OrderOutboundReturnDetailVO> detailVOMap,
                                 TrdSupplierOrder supplierOrder,
                                 Map<String, String> expressDataMap,
                                 BranchDTO branchDTO,
                                 List<TrdOrderExpress> expressList,
                                 List<OrderOutboundReturnDetailCyVO> cyVOList) {

        //销售订单详情
        trdSupplierOrderDtlList.forEach(x -> {
            SpuDTO spuDTO = trdCacheService.getSpuDTO(x.getSpuId());
            log.info("匹配商品SPu信息spuDTO:{}", spuDTO);

            //匹配详情信息的标识字段
            String itemDetailNo = spuDTO.getSourceNo() + StringPool.DASH + x.getLineNum();
            //匹配回传的数据
            OrderOutboundReturnDetailVO detailVO = detailVOMap.get(itemDetailNo);
            //校验行号商品详情是否匹配 如果匹配不上 则说明该商品详情 第三方未回传
            if (ToolUtil.isEmpty(detailVO)) {
                log.info("订单发货，订单编号：{}，未回传订单详情ID:{}", supplierOrder.getSupplierOrderNo(), x.getSupplierOrderDtlId());
                //直接做差异处理
                //默认组装成发货订单信息为0
                detailVO = new OrderOutboundReturnDetailVO();
                detailVO.setLineNum(x.getLineNum())
                        .setErpItemNo(spuDTO.getSourceNo())
                        .setSendUnitQty(BigDecimal.ZERO);

            }

            //订单商品最小单位数量
            BigDecimal orderQty = BigDecimal.valueOf(x.getOrderUnitQty()).multiply(x.getOrderUnitSize());

            //校验  发货数量不能大于订单数量
            if (detailVO.getSendUnitQty().compareTo(orderQty) > 0) {
                throw exception(OPEN_TRD_SUPPLIER_ORDER_DEL_CHECK_DELIVER_QTY, detailVO.getErpItemNo());
            }
            //设置发货数量信息(最小单位)
            x.setSendUnit(spuDTO.getMinUnit().toString());
            x.setSendUnitType(UnitTypeEnum.UNIT_SMALL.getType());
            x.setSendUnitQty(detailVO.getSendUnitQty());
            x.setSendQty(detailVO.getSendUnitQty());
            x.setSendUnitSize(NumberPool.LONG_ONE);

            //x.setMemo(Objects.nonNull(x.getMemo()) ? x.getMemo() + "," + detailVO.getMemo() : detailVO.getMemo());

            //校验物流信息
            if (ToolUtil.isNotEmpty(detailVO.getExpressComNo()) && ToolUtil.isNotEmpty(detailVO.getExpressNo())) {
                //设置物流信息
                TrdOrderExpress express = new TrdOrderExpress();
                express.setOrderId(supplierOrder.getOrderId());
                express.setOrderNo(supplierOrder.getOrderNo());
                express.setSupplierOrderDtlId(x.getSupplierOrderDtlId());
                express.setSupplierOrderDtlNo(x.getSupplierOrderDtlNo());
                express.setExpressNo(detailVO.getExpressNo());
                express.setExpressComNo(detailVO.getExpressComNo());
                express.setExpressCom(ToolUtil.isNotEmpty(expressDataMap.get(detailVO.getExpressComNo())) ?
                        expressDataMap.get(detailVO.getExpressComNo()) : detailVO.getExpressComNo());
                express.setReceiveMan(branchDTO.getContactName());
                express.setReceivePhone(branchDTO.getContactPhone());
                express.setAddress(branchDTO.getBranchAddr());
                express.setRealInTransitTime(new Date());
                express.setState(NumberPool.LONG_ZERO);
                express.setSysCode(supplierOrder.getSysCode());
                expressList.add(express);
            }


            //校验是否存在差异
            if (detailVO.getSendUnitQty().compareTo(orderQty) != 0) {
                //差异数量
                BigDecimal cyQty = orderQty.subtract(detailVO.getSendUnitQty());

                //差异金额
                BigDecimal cyAmt = cyQty.multiply(x.getExactPrice()).setScale(StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);
                ;

                //组装差异数据
                OrderOutboundReturnDetailCyVO cyVo = new OrderOutboundReturnDetailCyVO();
                cyVo.setSupplierOrderDtlId(x.getSupplierOrderDtlId());
                cyVo.setTotalNum(orderQty);
                cyVo.setRefundPrice(x.getExactPrice());
                cyVo.setRefundQty(cyQty);
                cyVo.setRefundAmt(cyAmt);
                cyVo.setUnitType(UnitTypeEnum.UNIT_SMALL.getType());
                cyVOList.add(cyVo);
            }

        });
    }

    /**
     * 新增订单操作日志
     *
     * @param orderVo    主订单
     * @param afterState 更新后状态
     * @param operType   操作类型
     * @param content    信息
     */
    private void saveOrderLog(TrdOrder orderVo, Long afterState, Long operType, String content, Long supplierId, Integer itemType) {
        TrdSupplierOrderDtl orderDtl = new TrdSupplierOrderDtl();
        orderDtl.setOrderId(orderVo.getOrderId());
        orderDtl.setSupplierId(supplierId);
        orderDtl.setItemType(itemType);
        List<TrdSupplierOrderDtl> sOrderDtlList = trdSupplierOrderDtlMapper.getSupplierOrdeDtl(orderDtl);
        List<TrdOrderLog> orderLogs = sOrderDtlList.stream().map(dtl -> {
            TrdOrderLog orderLog = new TrdOrderLog();
            orderLog.setSupplierOrderDtlId(dtl.getSupplierOrderDtlId());
            orderLog.setSupplierOrderDtlNo(dtl.getSupplierOrderDtlNo());
            orderLog.setBeforeState(dtl.getDeliveryState());
            orderLog.setAfterState(afterState);
            orderLog.setOperateType(operType);
            orderLog.setContent(content);
            orderLog.setSysCode(orderVo.getSysCode());
            return orderLog;
        }).collect(Collectors.toList());
        orderLogMapper.insertBatch(orderLogs);
        //清空门店我的订单状态数据缓存
        trdCacheService.clearOrderTotal(orderVo.getBranchId());
        //清空业务员下单门店订单状态数据缓存
        trdCacheService.clearOrderTotal(orderVo.getColonelId());
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 入驻商订单 TODO 补充编号 ==========
    // ErrorCode TRD_SUPPLIER_ORDER_NOT_EXISTS = new ErrorCode(TODO 补充编号, "入驻商订单不存在");

}
