package com.zksr.trade.controller.trdPackage;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageDtlPageReqVO;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageDtlRespVO;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageDtlSaveReqVO;
import com.zksr.trade.domain.TrdPackageDtl;
import com.zksr.trade.convert.trdPackage.TrdPackageDtlConvert;
import com.zksr.trade.service.ITrdPackageDtlService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 包裹明细Controller
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Api(tags = "管理后台 - 包裹明细接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/dtl")
public class TrdPackageDtlController {
    @Autowired
    private ITrdPackageDtlService trdPackageDtlService;

    /**
     * 新增包裹明细
     */
    @ApiOperation(value = "新增包裹明细", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "包裹明细", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdPackageDtlSaveReqVO createReqVO) {
        return success(trdPackageDtlService.insertTrdPackageDtl(createReqVO));
    }

    /**
     * 修改包裹明细
     */
    @ApiOperation(value = "修改包裹明细", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "包裹明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdPackageDtlSaveReqVO updateReqVO) {
            trdPackageDtlService.updateTrdPackageDtl(updateReqVO);
        return success(true);
    }

    /**
     * 删除包裹明细
     */
    @ApiOperation(value = "删除包裹明细", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "包裹明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{packageDtlIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] packageDtlIds) {
        trdPackageDtlService.deleteTrdPackageDtlByPackageDtlIds(packageDtlIds);
        return success(true);
    }

    /**
     * 获取包裹明细详细信息
     */
    @ApiOperation(value = "获得包裹明细详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{packageDtlId}")
    public CommonResult<TrdPackageDtlRespVO> getInfo(@PathVariable("packageDtlId") Long packageDtlId) {
        TrdPackageDtl trdPackageDtl = trdPackageDtlService.getTrdPackageDtl(packageDtlId);
        return success(TrdPackageDtlConvert.INSTANCE.convert(trdPackageDtl));
    }

    /**
     * 分页查询包裹明细
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得包裹明细分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdPackageDtlRespVO>> getPage(@Valid TrdPackageDtlPageReqVO pageReqVO) {
        PageResult<TrdPackageDtl> pageResult = trdPackageDtlService.getTrdPackageDtlPage(pageReqVO);
        return success(TrdPackageDtlConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:dtl:add";
        /** 编辑 */
        public static final String EDIT = "trade:dtl:edit";
        /** 删除 */
        public static final String DELETE = "trade:dtl:remove";
        /** 列表 */
        public static final String LIST = "trade:dtl:list";
        /** 查询 */
        public static final String GET = "trade:dtl:query";
        /** 停用 */
        public static final String DISABLE = "trade:dtl:disable";
        /** 启用 */
        public static final String ENABLE = "trade:dtl:enable";
    }
}
