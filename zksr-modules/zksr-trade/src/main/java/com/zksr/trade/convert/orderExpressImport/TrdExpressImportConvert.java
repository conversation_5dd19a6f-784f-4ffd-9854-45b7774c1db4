package com.zksr.trade.convert.orderExpressImport;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdExpressImport;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportRespVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 快递导入记录 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-15
*/
@Mapper
public interface TrdExpressImportConvert {

    TrdExpressImportConvert INSTANCE = Mappers.getMapper(TrdExpressImportConvert.class);

    TrdExpressImportRespVO convert(TrdExpressImport trdExpressImport);

    TrdExpressImport convert(TrdExpressImportSaveReqVO trdExpressImportSaveReq);

    PageResult<TrdExpressImportRespVO> convertPage(PageResult<TrdExpressImport> trdExpressImportPage);
}