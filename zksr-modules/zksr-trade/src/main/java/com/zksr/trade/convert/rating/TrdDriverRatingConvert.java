package com.zksr.trade.convert.rating;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.driver.vo.DriverRatingReqVO;
import com.zksr.trade.domain.TrdDriverRating;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingRespVO;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 司机评分 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-12-11
*/
@Mapper
public interface TrdDriverRatingConvert {

    TrdDriverRatingConvert INSTANCE = Mappers.getMapper(TrdDriverRatingConvert.class);

    TrdDriverRatingRespVO convert(TrdDriverRating trdDriverRating);

    TrdDriverRating convert(TrdDriverRatingSaveReqVO trdDriverRatingSaveReq);

    PageResult<TrdDriverRatingRespVO> convertPage(PageResult<TrdDriverRating> trdDriverRatingPage);

    TrdDriverRatingSaveReqVO convertSaveReqVO(DriverRatingReqVO driverRatingReqVO);


}