package com.zksr.trade.controller.hdfk.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 货到付款付款单对象 trd_hdfk_pay
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@ApiModel("货到付款付款单 - trd_hdfk_pay分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdHdfkPayPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("货到付款单据号")
    private String hdfkPayNo;

    @ApiModelProperty("处理开始时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date startProcessTime;

    @ApiModelProperty("处理结束时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date endProcessTime;

    @ApiModelProperty("付款单来源(数据字典), 0-app, 1-后台")
    private Integer paySource;

    @ApiModelProperty("处理人")
    private String processUser;
}
