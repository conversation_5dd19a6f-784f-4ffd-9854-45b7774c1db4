package com.zksr.trade.controller.car.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 购物车对象 trd_car
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Data
@ApiModel("购物车 - trd_car Response VO")
public class TrdCarRespVO {
    private static final long serialVersionUID = 1L;

    /** 购物车id */
    @ApiModelProperty(value = "是否业务员推荐")
    private Long carId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createMemberId;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty(value = "更新人")
    private String updateMemberId;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 入驻商上架商品id */
    @Excel(name = "入驻商上架商品id")
    @ApiModelProperty(value = "入驻商上架商品id")
    private Long supplierItemId;

    /** 城市上架商品id */
    @Excel(name = "城市上架商品id")
    @ApiModelProperty(value = "城市上架商品id")
    private Long areaItemId;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    @ApiModelProperty(value = "商品SPU id")
    private Long spuId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    @ApiModelProperty(value = "商品sku id")
    private Long skuId;

    /** 数量 */
    @Excel(name = "数量")
    @ApiModelProperty(value = "数量")
    private Long qty;

    /** 是否选中 1-是 0-否 */
    @Excel(name = "是否选中 1-是 0-否")
    @ApiModelProperty(value = "是否选中 1-是 0-否")
    private Integer selected;

    /** 是否业务员推荐（1-是 0-否） */
    @Excel(name = "是否业务员推荐", readConverterExp = "1=-是,0=-否")
    @ApiModelProperty(value = "是否业务员推荐")
    private Integer recommendFlag;

}
