package com.zksr.trade.controller.orderSupplier.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 入驻商订单明细对象 trd_supplier_order_dtl
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Data
@ApiModel("入驻商订单明细 - trd_supplier_order_dtl分页 Request VO")
public class TrdSupplierOrderDtlSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 入驻商订单明细id */
    @ApiModelProperty(value = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    @ApiModelProperty(value = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    @ApiModelProperty(value = "入驻商订单id")
    private Long supplierOrderId;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 0-本地配送商品 1-全国一件代发商品 */
    @Excel(name = "0-本地配送商品 1-全国一件代发商品")
    @ApiModelProperty(value = "0-本地配送商品 1-全国一件代发商品")
    private String itemType;

    /** 上架商品id */
    @Excel(name = "上架商品id")
    @ApiModelProperty(value = "上架商品id")
    private String itemId;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    @ApiModelProperty(value = "商品SPU id")
    private Long spuId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    @ApiModelProperty(value = "商品sku id")
    private Long skuId;

    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面图")
    private String thumb;

    /** 封面视频（url） */
    @Excel(name = "封面视频", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面视频")
    private String thumbVideo;

    /** 详情页轮播（json） */
    @Excel(name = "详情页轮播", readConverterExp = "j=son")
    @ApiModelProperty(value = "详情页轮播")
    private String images;

    /** 详情信息(富文本) */
    @Excel(name = "详情信息(富文本)")
    @ApiModelProperty(value = "详情信息(富文本)")
    private String details;

    /** 商品数量 */
    @Excel(name = "商品数量")
    @ApiModelProperty(value = "商品数量")
    private Long totalNum;

    /** 合计金额（price*total_num） */
    @Excel(name = "合计金额", readConverterExp = "p=rice*total_num")
    @ApiModelProperty(value = "合计金额")
    private BigDecimal totalAmt;

    /** 成交价 */
    @Excel(name = "成交价")
    @ApiModelProperty(value = "成交价")
    private BigDecimal price;

    /** 原销售价 */
    @Excel(name = "原销售价")
    @ApiModelProperty(value = "原销售价")
    private BigDecimal salePrice;

    /** *订单状态（数据字典） */
    @Excel(name = "*订单状态", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "*订单状态")
    private Long deliveryState;

    /** 优惠金额(分摊的) */
    @Excel(name = "优惠金额(分摊的)")
    @ApiModelProperty(value = "优惠金额(分摊的)")
    private BigDecimal discountAmt;

    /** 是否是赠品 1-是  0-否 */
    @Excel(name = "是否是赠品 1-是  0-否")
    @ApiModelProperty(value = "是否是赠品 1-是  0-否")
    private String giftFlag;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 优惠金额(不分摊的) */
    @Excel(name = "优惠金额(不分摊的)")
    @ApiModelProperty(value = "优惠金额(不分摊的)")
    private BigDecimal discountAmt2;

    /** 是否已经同步 1-是 0-否 */
    @Excel(name = "是否已经同步 1-是 0-否")
    @ApiModelProperty(value = "是否已经同步 1-是 0-否")
    private Integer syncFlag;

    /** 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消 3-货到付款未收款 4-货到付款已收款*/
    @Excel(name = "支付状态", readConverterExp = "数=据字典sys_pay_state")
    private Integer payState;
}
