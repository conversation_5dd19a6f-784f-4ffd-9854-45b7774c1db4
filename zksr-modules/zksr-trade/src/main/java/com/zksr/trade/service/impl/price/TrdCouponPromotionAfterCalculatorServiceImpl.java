package com.zksr.trade.service.impl.price;

import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponApplyDTO;
import com.zksr.promotion.api.coupon.dto.CouponReturnDTO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.convert.afterDiscount.TrdAfterDiscountDtlConvert;
import com.zksr.trade.domain.*;
import com.zksr.trade.mapper.TrdOrderDiscountDtlMapper;
import com.zksr.trade.mapper.TrdSupplierOrderDtlMapper;
import com.zksr.trade.service.ITrdAfterDiscountDtlService;
import com.zksr.trade.service.price.ITrdPromotionAfterCalculatorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年05月16日 15:52
 * @description: 订单售后优惠劵反还验证逻辑（发货前售后）
 */
@Service
@Order(ITrdPromotionAfterCalculatorService.ORDER_COUPON)
@Slf4j
public class TrdCouponPromotionAfterCalculatorServiceImpl implements ITrdPromotionAfterCalculatorService {
    @Autowired
    private TrdOrderDiscountDtlMapper trdOrderDiscountDtlMapper;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;
    @Autowired
    private CouponApi couponApi;
    @Autowired
    private ITrdAfterDiscountDtlService trdAfterDiscountDtlService;

    @Override
    public void calculatePromotionAfter(TrdOrder trdOrder, List<TrdSupplierOrderDtl> trdSupplierOrderDtls, TrdAfter trdAfter, List<TrdSupplierAfterDtl> trdSupplierAfterDtls) {
        CouponReturnDTO couponReturnDTO = new CouponReturnDTO();
        couponReturnDTO.setMemo(trdAfter.getReason());
        couponReturnDTO.setCouponList(new ArrayList<>());// 用于存放可以返还的优惠劵信息
        /**
         * 1、根据订单查询订单优惠明细表，将所有的优惠劵优惠明细查询出
         * 一张优惠劵对应多条明细
         */
        List<TrdOrderDiscountDtl> trdOrderDiscountDtls = trdOrderDiscountDtlMapper.selectList(trdOrder.getOrderId(), TrdDiscountTypeEnum.COUPON.getType());
        // 未找到订单使用优惠劵，直接返回
        if (ToolUtil.isEmpty(trdOrderDiscountDtls) || trdOrderDiscountDtls.size() <= 0)
            return;

        // 根据优惠ID进行分组
        Map<Long, List<TrdOrderDiscountDtl>> trdOrderDiscountDtlMap = trdOrderDiscountDtls.stream().collect(Collectors.groupingBy(TrdOrderDiscountDtl::getDiscountId));
        // 根据入驻商明细ID进行分组
        Map<Long, TrdSupplierAfterDtl> TrdSupplierAfterDtlMap = CollectionUtils.convertMap(trdSupplierAfterDtls, TrdSupplierAfterDtl::getSupplierOrderDtlId);

        // 获取订单明细信息
        List<TrdSupplierOrderDtl> supplierOrderDtls = trdSupplierOrderDtlMapper.selectListByOrderId(trdOrder.getOrderId());
        Map<Long, TrdSupplierOrderDtl> trdSupplierOrderDtlMap = CollectionUtils.convertMap(supplierOrderDtls, TrdSupplierOrderDtl::getSupplierOrderDtlId);

        trdOrderDiscountDtlMap.forEach((key, value) -> {
            List<TrdSupplierOrderDtl> supplierOrderDtlRes = value.stream().map(trdOrderDiscountDtl -> {
               return trdSupplierOrderDtlMap.get(trdOrderDiscountDtl.getSupplierOrderDtlId());
            }).collect(Collectors.toList());
            /**
             * 当前优惠劵所匹配的商品确认全部取消后（下单数量 == 发货前取消数量）
             * true：改张优惠劵可以退
             * false： 优惠劵核销的商品没有退货完毕，不予返回
             */
           boolean boo = supplierOrderDtlRes.stream().allMatch(orderDtl -> orderDtl.getCancelQty().compareTo(orderDtl.getTotalNum()) == 0);
           if (boo) {  // true：改优惠劵可以退
               CouponApplyDTO couponApplyDTO = new CouponApplyDTO();
               couponApplyDTO.setCouponId(key);
               couponApplyDTO.setRelateOrderNo(trdOrder.getOrderNo());
               couponReturnDTO.getCouponList().add(couponApplyDTO);

//               // 新增售后优惠信息
//               List<TrdAfterDiscountDtl> afterDiscountDtls = TrdAfterDiscountDtlConvert.INSTANCE.convertOrderDiscountList(value);
//               afterDiscountDtls.forEach(afterDiscount -> {
//                   afterDiscount.setAfterId(trdAfter.getAfterId());
//                   afterDiscount.setAfterNo(trdAfter.getAfterNo());
//                   TrdSupplierAfterDtl afterDtl = TrdSupplierAfterDtlMap.get(afterDiscount.getSupplierOrderDtlId());
//                   afterDiscount.setSupplierAfterDtlId(afterDtl.getSupplierAfterDtlId());
//                   afterDiscount.setSupplierAfterDtlNo(afterDtl.getSupplierAfterDtlNo());
//               });
//               // 批量新增售后优惠信息
//               trdAfterDiscountDtlService.insertBatchTrdAfterDiscountDtl(afterDiscountDtls);
           }
        });

        if (!couponReturnDTO.getCouponList().isEmpty()){
            log.info("返还优惠劵信息{}", couponReturnDTO);
            couponApi.returnCoupon(couponReturnDTO); // 返还优惠卷
        }
    }
}
