package com.zksr.trade.controller.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("首页订单返回实体")
public class HomeOrdersRespVO {
    @ApiModelProperty("时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    @ApiModelProperty("合计营业额")
    private BigDecimal countTotalAmt;

    @ApiModelProperty("合计订单数")
    private Long countOrder;

    @ApiModelProperty("合计销售数量")
    private Long countTotalNum;
}
