package com.zksr.trade.controller.rating.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 司机评分对象 trd_driver_rating
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@Data
@ApiModel("司机评分 - trd_driver_rating Response VO")
public class TrdDriverRatingRespVO {
    private static final long serialVersionUID = 1L;

    /**
     * 司机评分
     */
    @ApiModelProperty(value = "反馈图片;例：")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long driverRatingId;

    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    @ApiModelProperty(value = "用户名称")
    private String memberName;

    /**
     * 司机id
     */
    @Excel(name = "司机id")
    @ApiModelProperty(value = "司机id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long driverId;

    /**
     * 门店ID
     */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /**
     * 门店名称
     */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /**
     * 司机名称
     */
    @Excel(name = "司机名称")
    @ApiModelProperty(value = "司机名称")
    private String driverName;

    /**
     * 司机电话
     */
    @Excel(name = "司机电话")
    @ApiModelProperty(value = "司机电话")
    private String driverPhone;

    /**
     * 综合评分
     */
    @Excel(name = "综合评分")
    @ApiModelProperty(value = "综合评分")
    private BigDecimal totalScore;

    /**
     * 入驻商订单id
     */
    @Excel(name = "入驻商订单id")
    @ApiModelProperty(value = "入驻商订单id")
    private Long supplierOrderId;

    /**
     * 评价维度1字典code;例：0
     */
    @Excel(name = "评价维度1字典code;例：0")
    @ApiModelProperty(value = "评价维度1字典code;例：0")
    private String slot1Code;

    /**
     * 评价维度1字典值;例：司机态度
     */
    @Excel(name = "评价维度1字典值;例：司机态度")
    @ApiModelProperty(value = "评价维度1字典值;例：司机态度")
    private String slot1Val;

    /**
     * 评价维度1-得分字典;例：4
     */
    @Excel(name = "评价维度1-得分字典;例：4")
    @ApiModelProperty(value = "评价维度1-得分字典;例：4")
    private Integer slot1ScoreCode;

    /**
     * 评价维度1-得分字典值;例：满意
     */
    @Excel(name = "评价维度1-得分字典值;例：满意")
    @ApiModelProperty(value = "评价维度1-得分字典值;例：满意")
    private String slot1ScoreCodeVal;

    /**
     * 评价维度2字典code;例：1
     */
    @Excel(name = "评价维度2字典code;例：1")
    @ApiModelProperty(value = "评价维度2字典code;例：1")
    private String slot2Code;

    /**
     * 评价维度2字典值;例：配送时效
     */
    @Excel(name = "评价维度2字典值;例：配送时效")
    @ApiModelProperty(value = "评价维度2字典值;例：配送时效")
    private String slot2Val;

    /**
     * 评价维度2-得分字典;例：5
     */
    @Excel(name = "评价维度2-得分字典;例：5")
    @ApiModelProperty(value = "评价维度2-得分字典;例：5")
    private Integer slot2Score;

    /**
     * 评价维度2-得分字典值;例：非常满意
     */
    @Excel(name = "评价维度2-得分字典值;例：非常满意")
    @ApiModelProperty(value = "评价维度2-得分字典值;例：非常满意")
    private String slot2ScoreCodeVal;

    /**
     * 评价维度3字典code;例：2
     */
    @Excel(name = "评价维度3字典code;例：2")
    @ApiModelProperty(value = "评价维度3字典code;例：2")
    private String slot3Code;

    /**
     * 评价维度3字典值;例：商品完好
     */
    @Excel(name = "评价维度3字典值;例：商品完好")
    @ApiModelProperty(value = "评价维度3字典值;例：商品完好")
    private String slot3Val;

    /**
     * 评价维度3-得分;例：3
     */
    @Excel(name = "评价维度3-得分;例：3")
    @ApiModelProperty(value = "评价维度3-得分;例：3")
    private Integer slot3Score;

    /**
     * 评价维度1-得分字典值;例：一般满意
     */
    @Excel(name = "评价维度1-得分字典值;例：一般满意")
    @ApiModelProperty(value = "评价维度1-得分字典值;例：一般满意")
    private String scoreCodeVal;

    /**
     * 原因code;例：0,1
     */
    @Excel(name = "原因code;例：0,1")
    @ApiModelProperty(value = "原因code;例：0,1")
    private String reasonCode;

    /**
     * 低分原因code字典值;例：态度不好,送货不及时
     */
    @Excel(name = "低分原因code字典值;例：态度不好,送货不及时")
    @ApiModelProperty(value = "低分原因code字典值;例：态度不好,送货不及时")
    private String reasonVal;

    /**
     * 反馈信息;例：加油！努力！
     */
    @Excel(name = "反馈信息;例：加油！努力！")
    @ApiModelProperty(value = "反馈信息;例：加油！努力！")
    private String fedbackMsg;

    /**
     * 反馈图片;例：
     */
    @Excel(name = "反馈图片;例：")
    @ApiModelProperty(value = "反馈图片;例：")
    private String fedbackPics;

    /**
     * 关联订单号
     */
    @Excel(name = "关联订单号")
    @ApiModelProperty(value = "关联订单号")
    private String orderNo;

    /**
     * 关联子订单号
     */
    @Excel(name = "关联子订单号")
    @ApiModelProperty(value = "关联子订单号")
    private String supplierOrderNo;

    /**
     * 单据类型
     */
    @Excel(name = "单据类型")
    @ApiModelProperty(value = "单据类型")
    private String orderType;

    @ApiModelProperty("司机评论时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

}
