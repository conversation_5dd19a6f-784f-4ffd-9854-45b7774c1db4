package com.zksr.trade.mq;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.domain.erp.ErpBean;
import com.zksr.common.core.domain.erp.dto.AfterSheetOpenDto;
import com.zksr.common.core.domain.erp.dto.ErpReceiptDTO;
import com.zksr.common.core.domain.vo.openapi.*;
import com.zksr.common.core.enums.OrderPayWayEnum;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.enums.ReceiveLogisticsStatusEnum;
import com.zksr.common.core.enums.request.RequestType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.elasticsearch.domain.EsStoreProduct;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonelApp.dto.PageDataReqDTO;
import com.zksr.product.api.catgory.CatgoryApi;
import com.zksr.product.api.catgory.dto.CatgoryIdDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.openapi.OpenApi;
import com.zksr.trade.api.after.vo.PayRefundVO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.car.dto.AppCarEventDTO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdPayOrderPageVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlSaveReqVO;
import com.zksr.trade.controller.orderSettle.vo.TrdSupplierOrderSettlePageReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderPageReqVO;
import com.zksr.trade.controller.status.vo.TrdExpressStatusSaveReqVO;
import com.zksr.trade.domain.*;
import com.zksr.trade.mapper.*;
import com.zksr.trade.print.PrintBean;
import com.zksr.trade.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.OpenApiConstants.*;
import static com.zksr.member.constant.MemberConstant.*;

/**
 * <AUTHOR>
 * @date 2024年04月16日 19:20
 * @description: trade模块消费者
 */
@Configuration
@Slf4j
public class TradeMqConsumer {

    @Autowired
    private ITrdExpressImportDtlService trdExpressImportDtlService;

    @Autowired
    private ITrdOrderService orderService;

    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private EsProductService esProductService;

    @Autowired
    private PrintBean printBean;

    @Resource
    private CatgoryApi catgoryApi;

    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;
    @Autowired
    private TrdOrderMapper orderMapper;
    @Autowired
    private TrdSupplierAfterDtlMapper trdSupplierAfterDtlMapper;
    @Autowired
    private TrdAfterMapper afterMapper;
    @Autowired
    private TrdSupplierOrderSettleMapper trdSupplierOrderSettleMapper;

    @Resource
    private ITrdSupplierAfterDtlService trdSupplierAfterDtlService;

    @Resource
    private ITrdSupplierAfterService trdSupplierAfterService;

    @Resource
    private OpenApi openApi;

    @Autowired
    private ITrdExpressStatusService trdExpressStatusService;

    @Autowired
    private ITrdAfterService trdAfterService;

    @Autowired
    private ITrdCarService carService;

    @Autowired
    private TradeMqProducer tradeMqProducer;

    @Autowired
    private BranchApi branchApi;

/*    @Value("${erp.url}")
    private String url;
    @Value("${erp.publicKey}")
    private String publicKey;*/

    /**
     * 消息来源 {@link TradeMqProducer#sendSupplierOrderEvent(TrdExpressImportDtlSaveReqVO)}
     *
     * @return
     */
    @Bean
    public Consumer<TrdExpressImportDtlSaveReqVO> supplierOrderExpressEvent() {
        return (data) -> {
            log.info("收到入驻商一件代发订单事件：{} ", data);
            trdExpressImportDtlService.supplierOrderExpressReceive(data);
        };
    }

    /**
     * @Description: 门店下单统计常购商品Es消费者 (接收生产者{@link com.zksr.common.rocketmq.constant.MessageConstant.ORDER_PAY_UPDATE_SUCCESS_TOPIC}发送的消息)
     * @Author: liuxingyu
     * @Date: 2024/4/29 10:44
     */
    @Bean
    public Consumer<TrdOrder> storeProductExpressEvent() {
        return (data) -> {
            log.info("收到门店下单统计常购商品Es消息,订单编号:{}", data.getOrderNo());
            if (ObjectUtil.isEmpty(data)) {
                log.error("门店下单统计常购商品订单为空");
                return;
            }
            //更新门店生命周期
            try{
                branchApi.updateBranchLifecycle(data.getBranchId(),BRANCH_LIFECYCLE_OPERATION_TYPE_2 , data.getOrderId(),null);
            }catch (Exception e){
                //如果新增门店生命周期失败了  不影响门店发送
                log.error("门店生命周期---新增门店下单信息异常",e);

            }

            orderService.updateFrequentByOrder(data);
        };
    }

    /**
     * @Description: 门店打印消费者  (接收生产者{@link com.zksr.common.rocketmq.constant.MessageConstant.ORDER_PAY_UPDATE_SUCCESS_TOPIC}发送的消息)
     * @Author: liuxingyu
     * @Date: 2024/5/6 15:49
     */
    @Bean
    public Consumer<TrdOrder> storePrintExpressEvent() {
        return (data) -> {
            log.info("收到门店打印消费者消息,订单ID:{},平台编号:{}, 订单编号:{}", data.getOrderId(), data.getSysCode(), data.getOrderNo());
            if (ObjectUtil.isNull(data.getOrderId()) || ObjectUtil.isNull(data.getSysCode())) {
                log.error("门店打印缺少参数,订单ID:{},平台编号:{}, 订单编号:{}", data.getOrderId(), data.getSysCode(), data.getOrderNo());
                return;
            }
            printBean.print(data.getOrderId(), data.getSysCode(), null);
        };
    }

    @Bean
    public Consumer<TrdPayOrderPageVO> orderHdfkSuccessEvent() {
        return (data) -> {
            log.info("收到订单货到付款默认支付事件：{} ", data);
            orderService.orderPaySuccessCallback(data);
        };
    }

    @Bean
    public Consumer<Long> afterApproveEvent() {
        return (data) -> {
            log.info("收到售后订单审核消息事件,DATA：{} ", data);
            trdAfterService.afterApprove(data);
        };
    }

    @Bean
    public Consumer<TrdOrder> orderPayUpdateSuccessEvent() {
        return (data) -> {
            log.info("订单支付成功处理消息（用于处理订单支付完成后的业务逻辑）事件：{} ", data);
            orderService.afterOrderPayUpdateSuccess(data);
        };
    }

    @Bean
    public Consumer<PayRefundVO> afterOrderUpdateSuccess() {
        return (data) -> {
            log.info("同意退款之后,货到付款无需付款操作完成 执行退款剩余操作：{} ", data);
            trdAfterService.afterRefundSuccessCallback(data);
        };
    }



    /**
     * 购物车数据监听
     * 消费者 {@link TradeMqProducer#sendProductCarEvent(AppCarEventDTO)}
     * @return
     */
    @Bean
    public Consumer<AppCarEventDTO> productCarEvent() {
        return (data) -> {
            log.info("收到购物车事件消息：{} ", data);
            carService.processCarEvent(data);
        };
    }

    /**
     * @Description: 订单消息监听【ES 业务员客户订单】 (接收生产者{@link com.zksr.common.rocketmq.constant.MessageConstant.ORDER_PAY_UPDATE_SUCCESS_TOPIC}发送的消息)
     * @Author: chenmingqing
     * @Date: 2024/8/2 15:49
     */
    @Bean
    public Consumer<TrdOrder> orderToEsColonelAppBranchEvent() {
        return (data) -> {
            log.info("收到订单消息--用于记录ES 业务员客户订单,订单ID:{},平台编号:{}, 订单编号:{}", data.getOrderId(), data.getSysCode(), data.getOrderNo());
            tradeMqProducer.sendEsColonelAppBranchEvent(new ColonelAppBranchDTO(data.getBranchId(),data.getSysCode(),data.getPayAmt(),ES_COLONEL_APP_ORDER_STATUS_1));
        };
    }

    /**
     * @Description: 订单消息监听【业务员APP统计首页订单】 (接收生产者{@link com.zksr.common.rocketmq.constant.MessageConstant.ORDER_PAY_UPDATE_SUCCESS_TOPIC}发送的消息)
     * @Author: chenmingqing
     * @Date: 2024/8/2 15:49
     */
    @Bean
    public Consumer<TrdOrder> orderToColoenlAppPageDataEvent() {
        return (data) -> {
            log.info("收到订单消息--用于记录 业务员APP统计首页订单,订单ID:{},平台编号:{}, 订单编号:{}", data.getOrderId(), data.getSysCode(), data.getOrderNo());
            if (ToolUtil.isEmpty(data.getColonelId())) {
                log.info("订单没有业务员信息，不处理【业务员APP统计首页订单】消息");
                return;
            }



            /**
             * 获取入驻商订单明细利润分成信息
             *
             */

            TrdSupplierOrderSettlePageReqVO settlePageVo = new TrdSupplierOrderSettlePageReqVO();
            settlePageVo.setOrderNo(data.getOrderNo());
            List<TrdSupplierOrderSettle> supplierOrderSettles = trdSupplierOrderSettleMapper.selectList(settlePageVo);
            // 订单上级业务员提成金额
            BigDecimal colonel1Amt = BigDecimal.ZERO;
            // 订单业务员提成金额
            BigDecimal colonel2Amt = supplierOrderSettles.stream().map(TrdSupplierOrderSettle::getColonel2Amt).reduce(BigDecimal.ZERO, BigDecimal::add);

            if (Objects.equals(data.getColonelId(), data.getPcolonelId())) {
                colonel2Amt = colonel2Amt.add(supplierOrderSettles.stream().map(TrdSupplierOrderSettle::getColonel1Amt).reduce(BigDecimal.ZERO, BigDecimal::add));
            } else if (ToolUtil.isNotEmpty(data.getPcolonelId())) {
                colonel1Amt = supplierOrderSettles.stream().map(TrdSupplierOrderSettle::getColonel1Amt).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            // 当前业务员MQ消息
            tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(data.getColonelId(),data.getSysCode(),data.getBranchId(),data.getPayAmt(), colonel2Amt, data.getColonelFlag(), COLONEL_APP_Page_DATA_TYPE_3));

            if (NumberUtil.isGreater(colonel1Amt, BigDecimal.ZERO)) {
                // 上级业务员MQ消息 （只处理提成金额信息）
                tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(data.getPcolonelId(),data.getSysCode(),data.getBranchId(),data.getPayAmt(), colonel1Amt,COLONEL_APP_PAGE_DATA_TYPE_5));
            }

        };
    }

/*    *//**
     * @Description: 订单消息监听 【推送给第三方系统订单】 (接收生产者{@link com.zksr.common.rocketmq.constant.MessageConstant.ORDER_PAY_UPDATE_SUCCESS_TOPIC}发送的消息)
     * @Author: chenmingqing
     * @Date: 2024/8/2 15:49
     *//*
    @Bean
    public Consumer<TrdOrder> orderToSyncDataOrderEvent() {
        return (data) -> {
            log.info("收到订单消息--用于推送给第三方系统订单,订单ID:{},平台编号:{}, 订单编号:{}", data.getOrderId(), data.getSysCode(), data.getOrderNo());
            tradeMqProducer.sendSyncDataOrderEvent(data.getOrderNo());
        };
    }*/

    /**
     * @Description: 订单消息监听 【推送给第三方系统已支付的销售订单收款单】 (接收生产者{@link com.zksr.common.rocketmq.constant.MessageConstant.ORDER_PAY_UPDATE_SUCCESS_TOPIC}发送的消息)
     * @Author: jjc
     * @Date: 2024/10/17 15:49
     */
    @Bean
    public Consumer<TrdOrder> orderToSyncDataReceiptEvent() {
        return (data) -> {
            log.info("收到订单消息--用于推送给第三方系统已支付的销售订单收款单,订单ID:{},平台编号:{}, 订单编号:{}", data.getOrderId(), data.getSysCode(), data.getOrderNo());

            //组装推送已支付的销售订单收款单 同步信息 发送MQ（只推送线上支付已支付的   货到付款的过滤掉）
            if(OrderPayWayEnum.ONLINE.getPayWay().equals(data.getPayWay())){
                //获取该订单下单所有入驻商订单号
                List<TrdSupplierOrder> supplierOrderList = trdSupplierOrderMapper.selectListByOrderId(data.getOrderId());
                //按入驻商订单信息推送收款单
                supplierOrderList.forEach(x -> {
                    tradeMqProducer.sendSyncDataReceiptEvent(
                            new SyncReceiptSendDTO()
                                    .setSheetType(SheetTypeConstants.XSS)
                                    .setSupplierSheetNo(x.getSupplierOrderNo())
//                        .setSupplierDtlIdList(new ArrayList<>(supplierOrderDtlIdList))
                    );
                });
            }
        };
    }

}
