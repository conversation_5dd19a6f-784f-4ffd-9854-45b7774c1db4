package com.zksr.trade.controller.after.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.enums.PayStateEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.trade.api.express.TrdOrderExpress;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月22日 16:26
 * @description: AfterDtlRespDTO
 */
@ApiModel("售后单明细 - PC　查询详情 Response DTO")
@Data
@Accessors(chain = true)
public class AfterDtlRespDTO {

    @ApiModelProperty(value = "售后ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long afterId;

    @ApiModelProperty(value = "售后创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date afterCreateTime;

    @ApiModelProperty(value = "订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderCreateTime;

    @ApiModelProperty(value = "订单支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderPayTime;

    @ApiModelProperty(value = "付款方式（数据字典KEY：sys_pay_way）")
    @JsonSerialize(using= CustomLongSerialize.class)
    private  Long payWay;

    @ApiModelProperty(value = "售后类型（数据字典KEY：sys_after_type）")
    @JsonSerialize(using= CustomLongSerialize.class)
    private  Long afterType;

    @ApiModelProperty(value = "退货原因")
    private String reason;

    @ApiModelProperty(value = "退款失败原因")
    private String refundFailReason;

    @ApiModelProperty(value = "申请时上传的凭证照片")
    private String applyImgs;

    @ApiModelProperty(value = "售后单号")
    private String afterNo;

    @ApiModelProperty(value = "商品类型（数据字典KEY：sys_product_type）")
    @JsonSerialize(using= CustomLongSerialize.class)
    private  Long itemType;

    @ApiModelProperty(value = "退款类型（数据字典KEY：sys_refund_type）")
    @JsonSerialize(using= CustomLongSerialize.class)
    private  Long refundType;

    @ApiModelProperty(value = "订单单号")
    private String orderNo;

    @ApiModelProperty(value = "门店编号")
    private String branchNo;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店地址")
    private String branchAddress;

    @ApiModelProperty(value = "门店联系人")
    private String branchContactName;

    @ApiModelProperty(value = "门店联系人手机号")
    private String branchContactPhone;

    @ApiModelProperty(value = "门店头像")
    private String branchImages;

    @ApiModelProperty(value = "售后金额")
    private BigDecimal returnAmtTotal;

    /**
     * 参见 {@link PayStateEnum}
     */
    @ApiModelProperty(value = "订单支付状态, 0-未支付,1-已支付,2-订单取消,3-货到付款未收款,4-货到付款已收款")
    private Integer orderPayState;

    @ApiModelProperty(value = "入驻商订单信息集合")
    private List<AfterSupplier> afterSuppliers;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderTotalAmt;

    @ApiModelProperty(value = "处理状态处理中状态{0：(处理中状态全部)，1：待处理， 2：未退货，3：退货中，4：已退货，5：待退款，6：退款中，7：退款失败, 8:已处理（完成），9：已拒绝}")
    private Long handleState;

    @ApiModelProperty(value = "是否是推送给第三方的售后订单 false 本地订单  true 推送第三方订单")
    private boolean syncFlag;

    @Data
    public static class AfterSupplier {

        @ApiModelProperty(value = "入驻商ID")
        @JsonSerialize(using= CustomLongSerialize.class)
        private  Long supplierId;

        @ApiModelProperty(value = "入驻商名称")
        private String supplierName;

        @ApiModelProperty(value = "入驻商售后订单单号")
        private String supplierAfterNo;

        @ApiModelProperty(value = "入驻商联系人")
        private String supplierContactName;

        @ApiModelProperty(value = "入驻商联系电话")
        private String supplierContactPhone;

        @ApiModelProperty(value = "入驻商地址")
        private String supplierContactAddress;

        @ApiModelProperty(value = "售后收货手机号")
        private String returnPhone;

        @ApiModelProperty(value = "售后收货地址")
        private String returnAddr;

        @ApiModelProperty(value = "入驻商售后对应订单数据")
        private AfterOrderRespDTO orderInfo;

        @ApiModelProperty(value = "入驻商售后对应物流状态数据")
        private List<AfterDeliveryDTO> deliveryDTOList;



        @ApiModelProperty(value = "入驻商订单明细信息集合")
        private List<AfterSupplierDtl> afterSupplierDtls;

        @Data
        public static class AfterSupplierDtl {
            @ApiModelProperty(value = "售后单明细ID")
            @JsonSerialize(using= CustomLongSerialize.class)
            private  Long supplierAfterDtlId;

            @ApiModelProperty(value = "售后单明细编号")
            private  String supplierAfterDtlNo;

            @ApiModelProperty(value = "退货单价")
            private BigDecimal returnPrice;

            @ApiModelProperty(value = "订单数量")
            private Long orderTotalNum;

            @ApiModelProperty(value = "订单金额")
            private BigDecimal orderTotalAmt;

            @ApiModelProperty(value = "退货数量")
            private BigDecimal returnQty;

            @ApiModelProperty(value = "退货金额")
            private BigDecimal returnAmt;

            @ApiModelProperty(value = "商品名称")
            private String spuName;

            @ApiModelProperty(value = "SPU编码")
            private String spuNo;

            @ApiModelProperty(value = "SKU条码")
            private String barcode;

            @ApiModelProperty(value = "图片地址")
            private String thumb;

            @ApiModelProperty(value = "单位-数据字典（sys_prdt_unit）")
            private String unit;

            @ApiModelProperty(value = "规格属性")
            private String properties;

            @ApiModelProperty(value = "订单创建时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private Date orderCreateTime;

            @ApiModelProperty(value = "订单支付时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private Date orderPayTime;

            @ApiModelProperty(value = "付款方式（数据字典KEY：sys_pay_way）")
            private  Long payWay;

            @ApiModelProperty(value = "skuId")
            @JsonSerialize(using= CustomLongSerialize.class)
            private  Long skuId;

            @ApiModelProperty(value = "换算单位数量")
            private String convertQty;

            @ApiModelProperty(value = "售后购买单位实际销售单价")
            private BigDecimal returnSalesUnitPrice;

            @ApiModelProperty(value = "小单位单位名称-数据字典（sys_prdt_unit）")
            private String unitName;

            @ApiModelProperty(value = "小单位退货单价")
            private BigDecimal returnUnitPrice;

            @ApiModelProperty(value = "退货单位名称-数据字典（sys_prdt_unit）")
            private String returnUnitName;

            @ApiModelProperty(value = "退货单位数量")
            private BigDecimal returnUnitQty;

            @ApiModelProperty("快递信息集合")
            private List<TrdOrderExpress> expressesList;

            @ApiModelProperty(value = "订单购买单位")
            private String orderUnit;

            @ApiModelProperty(value = "订单购买单位数量")
            private BigDecimal orderUnitQty;

            @ApiModelProperty(value = "订单商品可售后数量（最小单位）")
            private BigDecimal waitReturnQty;

            @ApiModelProperty(value = "订单商品可售后金额")
            private BigDecimal waitReturnAmt;

            @ApiModelProperty(value = "原申请退货最小单位数量")
            private BigDecimal originalReturnQty;

            @ApiModelProperty(value = "原申请退货金额")
            private BigDecimal originalReturnAmt;

            @ApiModelProperty("商品计价方式类型(字典：spu_pricing_way) 默认为普通商品 1：普通商品（只能是小数） 2：称重商品(允许为小数)")
            private Integer pricingWay = NumberPool.INT_ONE;
        }
    }





}
