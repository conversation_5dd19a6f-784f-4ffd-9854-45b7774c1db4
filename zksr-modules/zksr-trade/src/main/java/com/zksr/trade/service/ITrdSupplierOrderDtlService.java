package com.zksr.trade.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.controller.orderExpressImport.dto.TrdExportOrderDtlDTO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExportOrderDtlVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderDtlPageReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderDtlSaveReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderRespVO;
import com.zksr.trade.domain.TrdSupplierOrderDtl;

import javax.validation.Valid;
import java.util.List;

/**
 * 入驻商订单明细Service接口
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
public interface ITrdSupplierOrderDtlService {

    /**
     * 新增入驻商订单明细
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdSupplierOrderDtl(@Valid TrdSupplierOrderDtlSaveReqVO createReqVO);

    /**
     * 修改入驻商订单明细
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdSupplierOrderDtl(@Valid TrdSupplierOrderDtlSaveReqVO updateReqVO);

    /**
     * 删除入驻商订单明细
     *
     * @param supplierOrderDtlId 入驻商订单明细id
     */
    public void deleteTrdSupplierOrderDtl(Long supplierOrderDtlId);

    /**
     * 批量删除入驻商订单明细
     *
     * @param supplierOrderDtlIds 需要删除的入驻商订单明细主键集合
     * @return 结果
     */
    public void deleteTrdSupplierOrderDtlBySupplierOrderDtlIds(Long[] supplierOrderDtlIds);

    /**
     * 获得入驻商订单明细
     *
     * @param supplierOrderDtlId 入驻商订单明细id
     * @return 入驻商订单明细
     */
    public TrdSupplierOrderDtl getTrdSupplierOrderDtl(Long supplierOrderDtlId);

    /**
     * 获得入驻商订单明细分页
     *
     * @param pageReqVO 分页查询
     * @return 入驻商订单明细分页
     */
    PageResult<TrdSupplierOrderDtl> getTrdSupplierOrderDtlPage(TrdSupplierOrderDtlPageReqVO pageReqVO);

    /**
    * @Description: 获取入驻商小程序订单详情
    * @Author: liuxingyu
    * @Date: 2024/4/11 9:52
    */
    List<TrdSupplierOrderRespVO> getSupplierOrderDtl(Long orderId);

    /**
     * 获取供应商下全国订单未发货的单据日期
     * @param supplierId
     * @return
     */
    List<String> getNotDeliveryNationwideOrderDate(Long supplierId);

    /**
     * 根据入驻商信息获取供应商下全国订单未发货的单据明细
     * @param expressVo
     * @return
     */
    List<TrdExportOrderDtlDTO> getNotDeliveryNationwideOrderDtl(TrdExportOrderDtlVO expressVo);

    /**
     * 根据订单明细单号查询订单明细
     * @param supplierOrderDtlNo
     * @return
     */
    TrdSupplierOrderDtl getSupplierOrderDtlBySupplierOrderDtlNo(String supplierOrderDtlNo);

    /**
     * 根据入驻商订单单号查询订单明细
     * @param supplierOrderNo
     * @return
     */
    List<TrdSupplierOrderDtl> getListBySupplierOrderNo(String supplierOrderNo);

    /**
     * 根据ID集合查询订单明细
     * @param ids
     * @return
     */
    List<TrdSupplierOrderDtl> getListByIds(List<Long> ids);

    /**
     * 根据多个订单号查询入住商订单详情
     * @param orderNos
     * @return
     */
    List<com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderDtlVO> getSupplierOrderDtlByOrderNos(List<String> orderNos);


    List<TrdSupplierOrderDtl>  getCategoryExistOrder(Long categoryId, Long sysCode);
}
