package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.domain.TrdExpressImport;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportPageReqVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportSaveReqVO;

/**
 * 快递导入记录Service接口
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
public interface ITrdExpressImportService {

    /**
     * 新增快递导入记录
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public TrdExpressImport insertTrdExpressImport(@Valid TrdExpressImportSaveReqVO createReqVO);

    /**
     * 修改快递导入记录
     *
     * @param trdExpressImport 修改信息
     * @return 结果
     */
    public void updateTrdExpressImport(@Valid TrdExpressImport trdExpressImport);

    /**
     * 删除快递导入记录
     *
     * @param expressImportId 快递导入记录id
     */
    public void deleteTrdExpressImport(Long expressImportId);

    /**
     * 批量删除快递导入记录
     *
     * @param expressImportIds 需要删除的快递导入记录主键集合
     * @return 结果
     */
    public void deleteTrdExpressImportByExpressImportIds(Long[] expressImportIds);

    /**
     * 获得快递导入记录
     *
     * @param expressImportId 快递导入记录id
     * @return 快递导入记录
     */
    public TrdExpressImport getTrdExpressImport(Long expressImportId);

    /**
     * 获得快递导入记录分页
     *
     * @param pageReqVO 分页查询
     * @return 快递导入记录分页
     */
    PageResult<TrdExpressImport> getTrdExpressImportPage(TrdExpressImportPageReqVO pageReqVO);

}
