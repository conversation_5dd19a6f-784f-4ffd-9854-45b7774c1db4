package com.zksr.trade.controller.settle.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 订单结算对象 trd_settle
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Data
@ApiModel("订单结算 - trd_settle分页 Request VO")
public class TrdSettleSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 订单结算id */
    @ApiModelProperty(value = "支付平台(数据字典);从订单表来")
    private Long settleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 订单id */
    @Excel(name = "订单id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    /** 全国商品订单编号 */
    @Excel(name = "全国商品订单编号")
    @ApiModelProperty(value = "全国商品订单编号")
    private String supplierOrderNo;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    @ApiModelProperty(value = "入驻商订单id")
    private Long supplierOrderId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    @ApiModelProperty(value = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    @ApiModelProperty(value = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 售后编号 */
    @Excel(name = "售后编号")
    @ApiModelProperty(value = "售后编号")
    private String afterNo;

    /** 售后单id */
    @Excel(name = "售后单id")
    @ApiModelProperty(value = "售后单id")
    private Long afterId;

    /** 结算时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "结算时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "结算时间")
    private Date settleTime;

    /** 结算状态 0=未结算，1=已结算 */
    @Excel(name = "结算状态 0=未结算，1=已结算")
    @ApiModelProperty(value = "结算状态 0=未结算，1=已结算", required = true)
    private Integer state;

    /** 结算金额 */
    @Excel(name = "结算金额")
    @ApiModelProperty(value = "结算金额", required = true)
    private BigDecimal settleAmt;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "商户类型", required = true)
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty(value = "商户id", required = true)
    private Long merchantId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id", required = true)
    private Long supplierId;

    /** 支付平台(数据字典);从订单表来 */
    @Excel(name = "支付平台(数据字典);从订单表来")
    @ApiModelProperty(value = "支付平台(数据字典);从订单表来", required = true)
    private String platform;

}
