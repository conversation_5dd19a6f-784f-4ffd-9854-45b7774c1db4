package com.zksr.trade.mapper;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.trade.controller.status.vo.TrdExpressStatusPageReqVO;
import com.zksr.trade.domain.TrdExpressStatus;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 物流状态（ERP-&gt;B2B）Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@Mapper
public interface TrdExpressStatusMapper extends BaseMapperX<TrdExpressStatus> {
    default PageResult<TrdExpressStatus> selectPage(TrdExpressStatusPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdExpressStatus>()
                    .eqIfPresent(TrdExpressStatus::getId, reqVO.getId())
                    .eqIfPresent(TrdExpressStatus::getSupplierOrderNo,reqVO.getSupplierOrderNo())
                    .eqIfPresent(TrdExpressStatus::getLogisticsStatus, reqVO.getLogisticsStatus())
                    .eqIfPresent(TrdExpressStatus::getSort, reqVO.getSort())
                .orderByDesc(TrdExpressStatus::getId));
    }


    default List<TrdExpressStatus> selectBySupplierOrderNo(String supplierOrderNo) {
        return selectList(new LambdaQueryWrapperX<TrdExpressStatus>()
                .eq(TrdExpressStatus::getSupplierOrderNo, supplierOrderNo));
    }
}
