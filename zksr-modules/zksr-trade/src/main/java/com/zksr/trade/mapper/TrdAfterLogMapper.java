package com.zksr.trade.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdAfterLog;
import com.zksr.trade.controller.after.logVo.TrdAfterLogPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 售后日志Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Mapper
public interface TrdAfterLogMapper extends BaseMapperX<TrdAfterLog> {
    default PageResult<TrdAfterLog> selectPage(TrdAfterLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdAfterLog>()
                    .eqIfPresent(TrdAfterLog::getAfterLogId, reqVO.getAfterLogId())
                    .eqIfPresent(TrdAfterLog::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdAfterLog::getSupplierAfterDtlId, reqVO.getSupplierAfterDtlId())
                    .eqIfPresent(TrdAfterLog::getSupplierAfterDtlNo, reqVO.getSupplierAfterDtlNo())
                    .eqIfPresent(TrdAfterLog::getBeforeState, reqVO.getBeforeState())
                    .eqIfPresent(TrdAfterLog::getAfterState, reqVO.getAfterState())
                    .eqIfPresent(TrdAfterLog::getOperateType, reqVO.getOperateType())
                    .eqIfPresent(TrdAfterLog::getContent, reqVO.getContent())
                .orderByDesc(TrdAfterLog::getAfterLogId));
    }

    /**
     * 根据入驻商售后订单明细ID 获取入驻商售后订单明细操作记录
     * @param supplierAfterDtlId 入驻商售后订单明细ID
     * @return
     */
    List<TrdAfterLog> selectListBySupplierAfterDtlId(@Param("supplierAfterDtlId") Long supplierAfterDtlId);
}
