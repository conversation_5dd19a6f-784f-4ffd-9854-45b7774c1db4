package com.zksr.trade.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.account.api.pay.PayFlowApi;
import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.common.core.constant.OpenApiConstants;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.domain.erp.dto.AfterDetailOpenDto;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.enums.AfterHandleStateEnum;
import com.zksr.common.core.enums.LogisticsStatusEnum;
import com.zksr.common.core.domain.vo.openapi.TrdSupAfterDtlDetail;
import com.zksr.common.core.domain.vo.openapi.TrdSupAfterDtlRequest;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonelApp.dto.PageDataReqDTO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.report.api.homePages.dto.HomePagesOrderAfterDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.opensource.OpensourceApi;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partnerPolicy.dto.AfterSaleSettingPolicyDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.trade.api.after.dto.AfterOrderPageRespDTO;
import com.zksr.trade.api.after.dto.AfterSupplierRespDTO;
import com.zksr.trade.api.after.dto.OrderAfterDtlResDTO;
import com.zksr.trade.api.after.dto.OrderAfterSaveResDTO;
import com.zksr.trade.api.after.vo.*;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.api.order.dto.ColonelAppOrderListTotalDTO;
import com.zksr.trade.api.order.vo.*;
import com.zksr.trade.controller.after.dto.AfterDeliveryDTO;
import com.zksr.trade.controller.after.dto.AfterDtlListDTO;
import com.zksr.trade.controller.after.dto.AfterDtlRespDTO;
import com.zksr.trade.controller.after.dto.AfterPageRespDTO;
import com.zksr.trade.controller.after.vo.AfterPageReqVO;
import com.zksr.trade.controller.after.vo.OrderAfterSaveVO;
import com.zksr.trade.controller.after.vo.TrdAfterSaveReqVO;
import com.zksr.trade.convert.after.TrdAfterConvert;
import com.zksr.trade.convert.after.TrdAfterOrderConvert;
import com.zksr.trade.convert.afterDiscount.TrdAfterDiscountDtlConvert;
import com.zksr.trade.domain.*;
import com.zksr.trade.mapper.*;
import com.zksr.trade.mq.TradeMqProducer;
import com.zksr.trade.service.ITrdAfterService;
import com.zksr.trade.service.ITrdOrderService;
import com.zksr.trade.service.ITrdSettleService;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.ITrdAfterHandlerService;
import com.zksr.trade.service.handler.activity.ITrdOrderActivityHandlerService;
import com.zksr.trade.service.price.ITrdPriceService;
import com.zksr.trade.service.price.ITrdPromotionAfterCalculatorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.OpenApiConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;
import static com.zksr.member.constant.MemberConstant.*;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 售后单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Service
@Slf4j
public class TrdAfterServiceImpl implements ITrdAfterService {
    @Autowired
    private TrdAfterMapper trdAfterMapper;
    @Autowired
    private TrdOrderMapper trdOrderMapper;
    @Autowired
    private TrdCacheService trdCacheService;
    @Autowired
    private ITrdOrderService trdOrderService;
    @Autowired
    private ITrdPriceService trdPriceService;
    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;
    @Autowired
    private TrdSupplierOrderSettleMapper supplierOrderSettleMapper;
    @Autowired
    private TrdOrderDiscountDtlMapper trdOrderDiscountDtlMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private TrdSupplierAfterMapper trdSupplierAfterMapper;
    @Autowired
    private TrdSupplierAfterDtlMapper trdSupplierAfterDtlMapper;
    @Autowired
    private TrdSupplierAfterSettleMapper trdSupplierAfterSettleMapper;
    @Autowired
    private TrdSettleMapper trdSettleMapper;
    @Autowired
    private TrdAfterLogMapper trdAfterLogMapper;
    @Autowired
    private TrdAfterDiscountDtlMapper trdAfterDiscountDtlMapper;
    @Autowired
    private List<ITrdAfterHandlerService> trdAfterHandlerService;
    @Autowired
    private TrdOrderExpressMapper trdOrderExpressMapper;
    @Autowired
    private ITrdSettleService trdSettleService;
    @Autowired
    private List<ITrdPromotionAfterCalculatorService> trdPromotionAfterCalculatorServices;

    @Autowired
    private TradeMqProducer tradeMqProducer;

    @Resource
    private OpensourceApi opensourceApi;

    @Resource
    private PayFlowApi payFlowApi;

    @Autowired
    private TrdExpressStatusMapper trdExpressStatusMapper;

    @Resource
    private BranchApi branchApi;

    /**
     * 新增售后单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdAfter(TrdAfterSaveReqVO createReqVO) {
        // 插入
        TrdAfter trdAfter = TrdAfterConvert.INSTANCE.convert(createReqVO);
        trdAfterMapper.insert(trdAfter);
        // 返回
        return trdAfter.getAfterId();
    }

    /**
     * 修改售后单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdAfter(TrdAfterSaveReqVO updateReqVO) {
        trdAfterMapper.updateById(TrdAfterConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除售后单
     *
     * @param afterId 售后单id
     */
    @Override
    public void deleteTrdAfter(Long afterId) {
        // 删除
        trdAfterMapper.deleteById(afterId);
    }

    /**
     * 批量删除售后单
     *
     * @param afterIds 需要删除的售后单主键
     * @return 结果
     */
    @Override
    public void deleteTrdAfterByAfterIds(Long[] afterIds) {
        for (Long afterId : afterIds) {
            this.deleteTrdAfter(afterId);
        }
    }

    /**
     * 获得售后单详情
     *
     * @param reqVO
     * @return 售后单
     */
    @Override
    public AfterDtlRespDTO getTrdAfterDtl(AfterDtlReqVO reqVO) {
        List<AfterDtlListDTO> dtlListDTOS = trdAfterMapper.selectAfterDtlList(reqVO);

        Set<SpuDTO> spuDTOS = new HashSet<>();
        Set<SkuDTO> skuDTOS = new HashSet<>();
        Set<SupplierDTO> supplierDTOS = new HashSet<>();

        dtlListDTOS.forEach(dtl -> {
            spuDTOS.add(trdCacheService.getSpuDTO(dtl.getSpuId()));
            skuDTOS.add(trdCacheService.getSkuDTO(dtl.getSkuId()));
            //获取该订单详情的入驻商信息
            SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(dtl.getSupplierId());

            //设置 该入驻商是否对接第三方
            OpensourceDto opensourceDto = trdCacheService.getOpensourceByMerchantId(supplierDTO.getSupplierId());
            supplierDTO.setSyncFlag(ToolUtil.isNotEmpty(opensourceDto) && ToolUtil.isNotEmpty(opensourceDto.getOpensourceId()));
            supplierDTOS.add(supplierDTO);
            BranchDTO branchDTO = trdCacheService.getBranchDTO(dtl.getBranchId());
            if (ObjectUtils.isNotEmpty(branchDTO)) {
                dtl.setBranchName(branchDTO.getBranchName());
                dtl.setBranchAddress(branchDTO.getBranchAddr());
                dtl.setBranchContactName(branchDTO.getContactPhone());
                dtl.setBranchContactPhone(branchDTO.getContactPhone());
                dtl.setBranchImages(branchDTO.getBranchImages());
                dtl.setBranchNo(branchDTO.getBranchNo());
            }
        });

        AfterSaleSettingPolicyDTO policyDTO = trdCacheService.getAfterSaleSettingPolicy(reqVO.getSupplierId());
        List<TrdOrderExpress> expressList = trdOrderExpressMapper.getOrderExpressInfoByOrderDtlIds(dtlListDTOS.stream().map(AfterDtlListDTO::getSupplierAfterDtlId).collect(Collectors.toSet()));

        // 查询订单支付流水号
        PayFlowDTO payFlowDTO = payFlowApi.getPaySuccessFlow(dtlListDTOS.get(0).getOrderNo()).getCheckedData();

        // 售后单数据转换
        AfterDtlRespDTO afterRespDTO = TrdAfterConvert.INSTANCE.convertResp(dtlListDTOS, supplierDTOS, spuDTOS, skuDTOS, policyDTO, expressList);

        afterRespDTO.getAfterSuppliers().forEach(supplier -> {
            // 入驻商售后单物流信息处理
            supplier.setDeliveryDTOList(getSupplierAfterDeliveryLog(dtlListDTOS.get(NumberPool.INT_ZERO)));
            // 渲染 订单商户支付流水号
            if (Objects.nonNull(payFlowDTO)) {
                supplier.getOrderInfo().setPayFlowId(payFlowDTO.getPayFlowId());
            }
        });
        return afterRespDTO;
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<AfterPageRespDTO> getTrdAfterPage(AfterPageReqVO pageReqVO) {
        PageResult<AfterPageRespDTO> result = new PageResult<>();
        Page<AfterPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<AfterPageRespDTO> pageResult = trdAfterMapper.selectPage(pageReqVO, page);
        pageResult.getRecords().forEach(record -> {
            BranchDTO branchDTO = trdCacheService.getBranchDTO(record.getBranchId());
            if (ObjectUtils.isNotEmpty(branchDTO)) {
                record.setBranchName(branchDTO.getBranchName());
                record.setBranchAddress(branchDTO.getBranchAddr());
                record.setBranchPhone(branchDTO.getContactPhone());
            }
            if (ObjectUtils.isNotEmpty(record.getColonelId())) {
                ColonelDTO colonelDTO = trdCacheService.getColonelDTO(record.getColonelId());
                if (ObjectUtils.isNotEmpty(colonelDTO)) {
                    record.setColonelName(colonelDTO.getColonelName());
                }
            }
        });
        result.setList(pageResult.getRecords());
        result.setTotal(page.getTotal());
        return result;
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_AFTER_ORDER, condition = "#after.afterId", tryLock = true)
    public void cancelAfter(String supplierAfterNo, Long sourceType) {
        TrdSupplierAfter trdSupplierAfter = trdSupplierAfterMapper.selectAfterBySupplierAfterNo(supplierAfterNo);
        if (ToolUtil.isEmpty(trdSupplierAfter)) {
            throw exception(TRD_SUPPLIER_AFTER_NOT_EXISTS);
        }

        // 获取售后单信息
        TrdAfter after = trdAfterMapper.selectById(trdSupplierAfter.getAfterId());
        // 查询入驻商售后订单
        List<TrdSupplierAfter> tsaList = trdSupplierAfterMapper.selectListByOrderNo(after.getAfterNo());
        // 获取售后单明细信息
        List<TrdSupplierAfterDtl> dtlList = trdSupplierAfterDtlMapper.selectListByAfterId(trdSupplierAfter.getAfterId());
        // 查询销售主订单
        TrdOrder tor = trdOrderMapper.selectById(after.getOrderId());
        // 查询入驻商销售订单
        List<TrdSupplierOrder> tsoList = trdSupplierOrderMapper.selectListByOrderId(after.getOrderId());

        // 校验售后单是否可以取消
        checkeAfterCancelData(after, dtlList, AfterHandleStateEnum.REVOKE.getCode(), sourceType);

        //更新订单表已退款手续费金额
        trdAfterHandlerService.forEach(service -> {
            service.revokeAfterReturn(tor, tsoList, after, tsaList, dtlList);
        });

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(prefix = RedisLockConstants.LOCK_AFTER_ORDER, condition = "#afterId", tryLock = true)
    public void cancelAfterReturn(Long afterId) {
        // 获取售后单信息
        TrdAfter after = trdAfterMapper.selectById(afterId);

        // 获取售后单明细信息
        List<TrdSupplierAfterDtl> dtlList = trdSupplierAfterDtlMapper.selectListByAfterId(afterId);

        // 校验售后单是否可以取消
        checkeAfterCancelData(after, dtlList, AfterHandleStateEnum.CANCEL.getCode(), SourceType.PC.getType());

        // 更新数据
        trdAfterHandlerService.forEach(service -> {
            service.cancelAfterReturn(after, dtlList);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(prefix = RedisLockConstants.LOCK_AFTER_ORDER, condition = "#editVO.afterId", tryLock = true)
    public void approveAfterReturn(AfterApproveDtlEditVO editVO) {
        checkeApproveData(editVO);
        /**
         * 更新售后入驻商订单表 收货收货号和收货地址
         */
        TrdSupplierAfter supplierAfter = trdSupplierAfterMapper.selectByAfterIdAndSupplierId(editVO.getAfterId(), editVO.getSupplierId());
        supplierAfter.setReturnPhone(editVO.getReturnPhone());
        supplierAfter.setReturnAddr(editVO.getReturnAddr());
        trdSupplierAfterMapper.updateById(supplierAfter);

        /**
         * 更新售后详情退货数量及审核
         */
        AtomicBoolean isUpdateQty = new AtomicBoolean(false);

        editVO.getAfterDtlList().forEach(dtl -> {
            TrdSupplierAfterDtl afterDtl = trdSupplierAfterDtlMapper.selectById(dtl.getSupplierAfterDtlId());
            BigDecimal returnUnitSize = afterDtl.getReturnUnitSize();
            if (ToolUtil.isEmpty(afterDtl)) {
                throw new ServiceException("明细数据ID{}数据不存在！" + dtl.getSupplierAfterDtlId());
            }
            if (dtl.getRealityReturnQty().compareTo(afterDtl.getReturnQty()) != NumberPool.LONG_ZERO && !Objects.equals(editVO.getSource(), SourceType.COLONELAPP.getType())) { // 更改过数量，需要重新计算售后订单金额
                isUpdateQty.set(true);
                afterDtl.setReturnQty(dtl.getRealityReturnQty());
//                if (afterDtl.getReturnQty().remainder(returnUnitSize).compareTo(BigDecimal.ZERO) == NumberPool.LONG_ONE) {
//                    throw new ServiceException(StringUtils.format("明细数据ID：{}，退货数量：{}，换算数量：{}，计算错误！" + dtl.getSupplierAfterDtlId(), dtl.getRealityReturnQty(), returnUnitSize));
//                }
                // 这里应前端页面提供的是商品最小单位数量，故
                afterDtl.setReturnUnitQty(afterDtl.getReturnQty().divide(returnUnitSize, StatusConstants.PRICE_RESERVE_3, RoundingMode.HALF_UP)); // 退货单位数量更改成页面提供的数量 = 页面数量是最小单位数量 / 售后单位数量规格数量
            }
            if (ToolUtil.isNotEmpty(afterDtl.getAfterType())) {
                afterDtl.setAfterType(editVO.getAfterType());
            }

            // 售后订单审核同意申请操作
            trdAfterHandlerService.forEach(service -> {
                service.approveAfterReturnOrderDtl(afterDtl);
            });
        });

        // 入驻商售后订单审核
        trdAfterHandlerService.forEach(service -> {
            service.approveSupplierAfter(supplierAfter);
        });

        TrdAfter trdAfter = trdAfterMapper.selectByAfterId(editVO.getAfterId());
        if (isUpdateQty.get()) { // 更改过数量，需要重新计算售后订单金额
            // 重新计算售后订单数量、金额
            trdPriceService.recalculateAfterAmt(trdAfter);
        } else {
            // 查询销售主订单
            TrdOrder tor = trdOrderMapper.selectById(trdAfter.getOrderId());
            // 查询入驻商销售订单
            List<TrdSupplierOrder> tsoList = trdSupplierOrderMapper.selectListByOrderId(trdAfter.getOrderId());
            // 更新订单已退款手续费金额
            trdPriceService.updateOrderInfo(tor, tsoList, trdAfter, Collections.singletonList(supplierAfter), StringPool.PLUS);
        }


        if (Objects.equals(supplierAfter.getTransNo(), SheetTypeConstants.SHS)
                || Objects.equals(supplierAfter.getTransNo(), "SHS")) {
            log.info("开始发送本地售后订单：{}", supplierAfter.getAfterNo());
            tradeMqProducer.sendSyncDataAfterEvent(supplierAfter.getAfterNo());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(prefix = RedisLockConstants.LOCK_AFTER_ORDER, condition = "#editVO.afterId", tryLock = true)
    public void approveAfterRefund(AfterApproveDtlEditVO editVO) {
        checkeApproveData(editVO);

        TrdAfter trdAfter = trdAfterMapper.selectById(editVO.getAfterId());
//        TrdOrder trdOrder = trdOrderMapper.selectById(trdAfter.getOrderId());

        List<AfterDtl> afterDtlList = editVO.getAfterDtlList();

        //增加校验  推送给第三方的售后订单 需要退货确认后才能同意退款   （差异、拒收不需要）
        TrdSupplierAfterDtl supplierAfterDtl = trdSupplierAfterDtlMapper.selectById(afterDtlList.get(NumberPool.INT_ZERO).getSupplierAfterDtlId());
        TrdSupplierAfter trdSupplierAfter = trdSupplierAfterMapper.selectByAfterIdAndSupplierId(trdAfter.getAfterId(), supplierAfterDtl.getSupplierId());

        //如果是第三方售后单，并且第三方售后单状态为未推送，则不允许退款
        OpensourceDto opensourceByMerchantId = opensourceApi.getOpensourceByMerchantId(trdSupplierAfter.getSupplierId()).getCheckedData();
        if (ToolUtil.isNotEmpty(opensourceByMerchantId) && !Objects.equals(AfterPhaseEnum.REDUND_BEFORE_SHIPMENT.getState(), supplierAfterDtl.getAfterPhase())) {
            if ((Objects.equals(trdSupplierAfter.getTransNo(), SheetTypeConstants.SHS)
                    || Objects.equals(trdSupplierAfter.getTransNo(), "SHS"))
                    && (ORDER_SYNC_FLAG_0.equals(trdSupplierAfter.getPushStatus()))) {
                throw exception(TRD_SUPPLIER_AFTER_DTL_CHECK_NOT_PUSHED_APPROVAL);
            }
        }

        if ((Objects.equals(trdSupplierAfter.getTransNo(), SheetTypeConstants.SHS) || Objects.equals(trdSupplierAfter.getTransNo(), "SHS"))
                && (ORDER_SYNC_FLAG_1.equals(trdSupplierAfter.getPushStatus()) || ORDER_SYNC_FLAG_2.equals(trdSupplierAfter.getPushStatus()))
                && !Objects.equals(AfterPhaseEnum.REDUND_BEFORE_SHIPMENT.getState(), supplierAfterDtl.getAfterPhase())) {

            //已推送第三方的售后订单需要退款的售后详情状态必须都是 退货完成
            List<TrdSupplierAfterDtl> supplierAfterDtls = trdSupplierAfterDtlMapper
                    .selectListByDtlIdList(afterDtlList.stream().map(AfterDtl::getSupplierAfterDtlId).collect(Collectors.toList()));

            //筛选状态不是退货完成的售后详情信息
            List<TrdSupplierAfterDtl> checkList = supplierAfterDtls.stream().filter(x -> !ReturnStateEnum.RETURN_THWC.getState().equals(x.getReturnState())).collect(Collectors.toList());
            if (ToolUtil.isNotEmpty(checkList) && !checkList.isEmpty()) {
                throw exception(TRD_SUPPLIER_AFTER_DTL_CHECK_OTHER_AFTER_APPROVE_AFTER_REFUND);
            }
        }


        List<TrdSupplierAfterDtl> afterDtls = new ArrayList<>();
        afterDtlList.stream().forEach(dtl -> {
            TrdSupplierAfterDtl afterDtl = trdSupplierAfterDtlMapper.selectById(dtl.getSupplierAfterDtlId());
            if (ToolUtil.isEmpty(afterDtl)) {
                throw new ServiceException("明细数据ID{}数据不存在！" + dtl.getSupplierAfterDtlId());
            }

            // 售后订单审核同意退款操作
            trdAfterHandlerService.forEach(service -> {
                service.approveAfterRefundOrderDtl(afterDtl, trdAfter);
            });
            afterDtls.add(afterDtl);
        });

        // 计算储值支付本金支付金额、储值赠金支付金额
        trdPriceService.calculatorAfterCzPayWayGiveAmt(trdAfter);

        // 发起退款申请
        trdAfterHandlerService.forEach(service -> {
            service.createPayRefundOrder(trdSupplierAfter.getOrderNo(), trdSupplierAfter.getSupplierOrderNo(), trdAfter, afterDtls);
        });
    }

    @Override
    public String approveAfterBatchRefund(List<AfterDtlReqVO> reqVOS) {
        StringBuffer msg = new StringBuffer();
        ITrdAfterService trdAfterService = SpringUtils.getBean(TrdAfterServiceImpl.class);
        for (AfterDtlReqVO reqVO : reqVOS) {
            try {
                // 根据售后业务单号查询 业务明细数据
                AfterDtlRespDTO afterDtlRespDTO = trdAfterService.getTrdAfterDtl(reqVO);
                if (ToolUtil.isEmpty(afterDtlRespDTO)) {
                    continue;
                }
                // 审核退款操作
                trdAfterService.approveAfterRefund(TrdAfterConvert.INSTANCE.batchAfterDtlEditVOResp(afterDtlRespDTO, reqVO));
            } catch (Exception e) {
                log.error("售后订单ID【{}】批量审核同意退款失败:{}！", reqVO.getAfterId(), e.getMessage());
                msg.append(StringUtils.format("<br/><span style='color:red'>售后订单号【{}】批量审核同意退款失败:{}！</span>", reqVO.getAfterId(), e.getMessage()));
            }
        }
        if (ToolUtil.isEmpty(msg)) {
            msg.append("批量审核同意操作成功！");
        }
        return msg.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(prefix = RedisLockConstants.LOCK_AFTER_ORDER, condition = "#request.orderId")
    public OrderAfterSaveResDTO saveOrderAfter(OrderAfterSaveRequest request) {
        log.info("保存售后订单,request:{}", JSON.toJSONString(request));
        OrderAfterResp resp = trdPriceService.calculateAfterOrderPrice(request);

        List<Long> supplierOrderIds = request.getSupplierOrders().stream().map(OrderAfterRequest.SupplierOrder::getSupplierOrderId).collect(Collectors.toList());

        // 查询订单主表数据
        TrdOrder trdOrder = trdOrderMapper.selectById(request.getOrderId());
        // 查询订单入驻商表数据
        List<TrdSupplierOrder> supplierOrders = trdSupplierOrderMapper.selectList(new LambdaQueryWrapper<TrdSupplierOrder>().in(TrdSupplierOrder::getSupplierOrderId, supplierOrderIds));
        // 查询入驻商订单明细表数据
        List<TrdSupplierOrderDtl> supplierOrderDtls = trdSupplierOrderDtlMapper.selectList(new LambdaQueryWrapper<TrdSupplierOrderDtl>().in(TrdSupplierOrderDtl::getSupplierOrderId, supplierOrderIds));
        // 查询入驻商订单明细结算数据
        List<TrdSupplierOrderSettle> supplierOrderSettles = supplierOrderSettleMapper.selectList(new LambdaQueryWrapper<TrdSupplierOrderSettle>().in(TrdSupplierOrderSettle::getSupplierOrderId, supplierOrderIds));
        // 查询订单明细优惠数据
        List<TrdOrderDiscountDtl> orderDiscountDtls = trdOrderDiscountDtlMapper.selectList(new LambdaQueryWrapper<TrdOrderDiscountDtl>().in(TrdOrderDiscountDtl::getSupplierOrderId, supplierOrderIds));

        // 构建订单
        OrderAfterSaveVO afterSaveVO = buildAfterOrder(resp, trdOrder, supplierOrders, supplierOrderDtls, supplierOrderSettles, orderDiscountDtls);


        // 售后订单保存前操作
        trdAfterHandlerService.forEach(service -> {
            service.beforeAfterCreate(afterSaveVO, trdOrder, supplierOrders);
        });

        // 保存订单
        TrdAfter after = saveAfter(afterSaveVO);

        // 判断订单是否是发货前售后，发货前售后发送售后订单审核消息
        if (Objects.equals(DeliveryStatusEnum.WAIT_FH.getCode(), resp.getDeliveryState())) {
            tradeMqProducer.sendAfterApproveEvent(after.getAfterId());
        }


        return HutoolBeanUtils.toBean(after, OrderAfterSaveResDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(prefix = RedisLockConstants.LOCK_AFTER_ORDER, condition = "#request.supplierId", tryLock = true)
    public OrderAfterSaveResDTO saveMergeAfterOrder(OrderMergeAfterRequest request) {
        // 构建订单
        OrderAfterSaveVO afterSaveVO = buildMergeAfterOrder(request);

//        // 售后订单保存前操作
//        trdAfterHandlerService.forEach(service -> {  service.beforeAfterCreate(afterSaveVO, trdOrder);  });
//
//        // 保存订单
//        TrdAfter after = saveAfter(afterSaveVO);
//
//        // 判断订单是否是发货前售后，发货前售后发送售后订单审核消息
//        if (Objects.equals(DeliveryStatusEnum.WAIT_FH.getCode(), resp.getDeliveryState())) {
//            tradeMqProducer.sendAfterApproveEvent(after.getAfterId());
//        }


//        return HutoolBeanUtils.toBean(after, OrderAfterSaveResDTO.class);
        return null;
    }

    @Override
    public PageResult<AfterOrderPageRespDTO> pageAfter(AfterOrderPageReqVO reqVO) {
        //先判断是否是查询全部数据
        PageResult<AfterOrderPageRespDTO> result = new PageResult<>();
        Page<AfterOrderPageReqVO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        // 查询主表数据用于分页
        Page<AfterOrderPageRespDTO> pageResult = trdAfterMapper.pageAfter(reqVO, page);

        if (!pageResult.getRecords().isEmpty()) {
            // 根据查询主表数据查询入驻商售后信息和明细信息
            List<Long> afterIds = pageResult.getRecords().stream().map(AfterOrderPageRespDTO::getAfterId).collect(Collectors.toList());
            List<AfterSupplierRespDTO> supplierRespDTOS = trdSupplierAfterMapper.getSupplierAfterByAfterId(afterIds);
            supplierRespDTOS.forEach(supplierAfter -> {
                SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(supplierAfter.getSupplierId());
                supplierAfter.setSupplierName(supplierDTO.getSupplierName());
                supplierAfter.setSupplierAfterQty(0L);
                supplierAfter.setSupplierAfterAmt(BigDecimal.ZERO);
                supplierAfter.getAfterSupplierDtlRespDTOList().forEach(afterDtl -> {
                    supplierAfter.setSupplierAfterQty(supplierAfter.getSupplierAfterQty() + afterDtl.getReturnQty());
                    supplierAfter.setSupplierAfterAmt(supplierAfter.getSupplierAfterAmt().add(afterDtl.getReturnAmt()));

                    SpuDTO spuDTO = trdCacheService.getSpuDTO(afterDtl.getSpuId());
                    if (ToolUtil.isNotEmpty(spuDTO)) {
                        afterDtl.setSpuName(spuDTO.getSpuName());
                        afterDtl.setThumb(spuDTO.getThumb());
                        afterDtl.setSpuNo(spuDTO.getSpuNo());
                    }

                    SkuDTO skuDTO = trdCacheService.getSkuDTO(afterDtl.getSkuId());
                    if (ToolUtil.isNotEmpty(skuDTO)) {
                        afterDtl.setProperties(PropertyAndValDTO.getProperties(skuDTO.getProperties()));
                        afterDtl.setItemBarcode(skuDTO.getBarcode()); // 商品条码
                    }

                });
            });

            Map<Long, List<AfterSupplierRespDTO>> supplierRespDTOMap = supplierRespDTOS.stream().collect(Collectors.groupingBy(AfterSupplierRespDTO::getAfterId));
            pageResult.getRecords().forEach(after -> {
                BranchDTO branchDTO = trdCacheService.getBranchDTO(after.getBranchId());
                if (ToolUtil.isNotEmpty(branchDTO)) {
                    after.setBranchName(branchDTO.getBranchName());
                    after.setContactName(branchDTO.getContactName());
                    after.setContactPhone(branchDTO.getContactPhone());
                }

                after.setAfterSupplierRespDTOS(supplierRespDTOMap.get(after.getAfterId()));
            });
        }
        result.setList(pageResult.getRecords());
        result.setTotal(page.getTotal());
        return result;
    }

    @Override
    public ColonelAppOrderListTotalDTO selectMemColoneAppAfterOrderListTotal(TrdColonelAppOrderListPageReqVO orderPageReqVO) {
        return trdAfterMapper.selectMemColoneAppAfterOrderListTotal(orderPageReqVO);
    }

    @Override
    public PageResult<TrdColonelAppOrderListRespVO> selectMemColoneAppAfterOrder(TrdColonelAppOrderListPageReqVO orderPageReqVO) {
        PageResult<TrdColonelAppOrderListRespVO> result = new PageResult<>();
        Page<TrdColonelAppOrderListPageReqVO> page = new Page<>(orderPageReqVO.getPageNo(), orderPageReqVO.getPageSize());
        Page<TrdColonelAppOrderListRespVO> pageResult = trdAfterMapper.selectMemColoneAppAfterOrder(orderPageReqVO, page, orderPageReqVO.getColonelIds());
        pageResult.getRecords().forEach(order -> {
            BranchDTO branch = trdCacheService.getBranchDTO(order.getBranchId());
            order.setBranchName(branch.getBranchName());
            order.setColonelFlag(NumberPool.INT_ZERO);
            order.setBranchAddress(branch.getBranchAddr());
        });
        result.setList(pageResult.getRecords());
        result.setTotal(pageResult.getTotal());
        return result;
    }

    @Override
    public List<AfterOrderPageRespDTO> selectMemColoneAppAfterOrderNew(TrdColonelAppOrderListPageReqVO orderPageReqVO) {
        List<AfterOrderPageRespDTO> list = trdAfterMapper.selectMemColoneAppAfterOrderNew(orderPageReqVO);
        list.forEach(tar -> {
            //  渲染售后订单门店基础数据
            BranchDTO branchDTO = trdCacheService.getBranchDTO(tar.getBranchId());
            if (ToolUtil.isNotEmpty(branchDTO)) {
                tar.setBranchName(branchDTO.getBranchName())
                        .setBranchAddress(branchDTO.getBranchAddr())
                        .setAfterOrderQty(NumberPool.LONG_ZERO)
                        .setAfterOrderAmt(BigDecimal.ZERO)
                ;
            }

            tar.getAfterSupplierRespDTOS().forEach(tsa -> {
                tsa.setAfterId(tar.getAfterId());

                //  渲染售后入驻商订单入驻商基础数据
                SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(tsa.getSupplierId());
                if (ToolUtil.isNotEmpty(supplierDTO)) {
                    tsa.setSupplierName(supplierDTO.getSupplierName())
                            .setSupplierAfterQty(NumberPool.LONG_ZERO)
                            .setSupplierAfterAmt(BigDecimal.ZERO)
                    ;
                }

                tsa.getAfterSupplierDtlRespDTOList().forEach(tsad -> {
                    //  渲染售后订单 数量、金额字段
                    tar.setAfterOrderQty(tar.getAfterOrderQty() + tsad.getReturnQty())
                            .setAfterOrderAmt(tar.getAfterOrderAmt().add(tsad.getReturnAmt()))
                    ;
                    //  渲染售后入驻商订单 数量、金额字段
                    tsa.setSupplierAfterQty(tsa.getSupplierAfterQty() + tsad.getReturnQty())
                            .setSupplierAfterAmt(tsa.getSupplierAfterAmt().add(tsad.getReturnAmt()))
                    ;

                    //  渲染售后入驻商订单明细SKU基础数据
                    SkuDTO skuDTO = trdCacheService.getSkuDTO(tsad.getSkuId());
                    if (ToolUtil.isNotEmpty(skuDTO)) {
                        tsad.setProperties(PropertyAndValDTO.getProperties(skuDTO.getProperties()))
                                .setItemBarcode(skuDTO.getUnitBarcode(tsad.getReturnUnitType()))
                        ;
                    }
                    //  渲染售后入驻商订单明细SPU基础数据
                    SpuDTO spuDTO = trdCacheService.getSpuDTO(tsad.getSpuId());
                    if (ToolUtil.isNotEmpty(spuDTO)) {
                        tsad.setSpuNo(spuDTO.getSpuNo())
                                .setSpuName(spuDTO.getSpuName())
                                .setThumb(spuDTO.getThumb())
                        ;
                    }

                });

            });
        });
        return list;
    }

    @Override
    public AfterApproveDtlEditVO getAfterDtlByDtlId(TrdSupAfterDtlRequest request) {
        List<AfterDtl> afterDtls = trdAfterMapper.getAfterDtlByDtlId(request.getDetailList().stream().map(TrdSupAfterDtlDetail::getItemNo).map(Long::valueOf).collect(Collectors.toList()), request);
        AfterApproveDtlEditVO dtlEditVO = trdAfterMapper.getAfterDtlByOrderNo(request.getOrderNo());
        dtlEditVO.setAfterDtlList(afterDtls);
        return dtlEditVO;
    }

    @Override
    public List<TrdColonelAppOrderDetailRespVO> getMemColoneAppAfterOrderDetail(Long afterId) {
        List<TrdSupplierAfterDtl> supplierAfterDtls = trdSupplierAfterDtlMapper.selectListByAfterId(afterId);
        return supplierAfterDtls.stream().map(detail -> {
            TrdColonelAppOrderDetailRespVO detailRespVO = HutoolBeanUtils.toBean(detail, TrdColonelAppOrderDetailRespVO.class);
            SpuDTO spu = trdCacheService.getSpuDTO(detail.getSpuId());
            SkuDTO sku = trdCacheService.getSkuDTO(detail.getSkuId());
            SupplierDTO supplier = trdCacheService.getSupplierDTO(detail.getSupplierId());

            if (ToolUtil.isNotEmpty(spu)) {
                detailRespVO.setSpuName(spu.getSpuName());
                detailRespVO.setSpuNo(spu.getSpuNo());
                detailRespVO.setThumb(spu.getThumb());
            }

            if (ToolUtil.isNotEmpty(sku)) {
                detailRespVO.setBarcode(sku.getBarcode());
            }
            detailRespVO.setTotalNum(detail.getReturnQty());
            detailRespVO.setTotalAmt(detail.getReturnAmt());
            detailRespVO.setPrice(detail.getReturnPrice());
            if (ToolUtil.isNotEmpty(supplier)) {
                detailRespVO.setSupplierName(supplier.getSupplierName());
            }
            return detailRespVO;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(prefix = RedisLockConstants.LOCK_AFTER_ORDER, condition = "#expressSaveVO.afterId", tryLock = true)
    public void uploadAfterExpressDelivery(OrderAfterUploadExpressSaveVO expressSaveVO) {
        if (ToolUtil.isEmpty(expressSaveVO) || ToolUtil.isEmpty(expressSaveVO.getSupplierAfters())) {
            throw new ServiceException("提交数据为空！");
        }

        TrdSupplierAfter supplierAfter = trdSupplierAfterMapper.selectByAfterIdAndSupplierId(expressSaveVO.getAfterId(), expressSaveVO.getSupplierAfters().get(0).getSupplierId());
        List<AfterDetailOpenDto> afterDetailOpenDtos = new ArrayList<>();
        expressSaveVO.getSupplierAfters().stream()
                .flatMap((supplierOrder -> supplierOrder.getSupplierAfterDtls().stream()))
                .forEach(supplierOrderDtl -> {
                    TrdSupplierAfterDtl afterDtl = trdSupplierAfterDtlMapper.selectById(supplierOrderDtl.getSupplierAfterDtlId());
                    // 新增快递信息到订单物流表
                    TrdOrderExpress express = new TrdOrderExpress();
                    express.setOrderId(expressSaveVO.getAfterId());
                    express.setOrderNo(expressSaveVO.getAfterNo());
                    express.setSupplierOrderDtlId(supplierOrderDtl.getSupplierAfterDtlId());
                    express.setExpressNo(supplierOrderDtl.getExpressNo());
                    express.setExpressCom(supplierOrderDtl.getExpressCom());
                    express.setExpressComNo(supplierOrderDtl.getExpressCom());
                    express.setState(LogisticsStatusEnum.ZW.getCode());
                    express.setSupplierOrderDtlNo(afterDtl.getSupplierAfterDtlNo());
                    trdOrderExpressMapper.insert(express);

                    // 更新售后订单状态为退货中
                    trdAfterHandlerService.forEach(service -> {
                        service.submitLogisticsAfterOrderDtl(afterDtl);
                    });

/*                    AfterDetailOpenDto afterDetailOpenDto = new AfterDetailOpenDto();

                    SkuDTO skuDTO = trdCacheService.getSkuDTO(afterDtl.getSkuId());
                    afterDetailOpenDto.setItemNo(String.valueOf(skuDTO.getSourceNo()));
                    afterDetailOpenDto.setRemake(afterDtl.getMemo());
                    afterDetailOpenDto.setDetailQty(BigDecimal.valueOf(afterDtl.getReturnQty()));
                    String line = afterDtl.getSupplierAfterDtlNo();
                    afterDetailOpenDto.setExternalLineNo(line.substring(line.length()-1));
                    afterDetailOpenDto.setItemPrice(skuDTO.getMarkPrice());
                    afterDetailOpenDto.setItemType(afterDtl.getGiftFlag().toString());
                    afterDetailOpenDto.setRealQty(new BigDecimal(afterDtl.getOrderUnitQty().toString()));
                    afterDetailOpenDto.setDetailPrice(afterDtl.getReturnAmt());
                    afterDetailOpenDto.setMinDetailQty(new BigDecimal(afterDtl.getOrderUnitQty().toString()).multiply(new BigDecimal(afterDtl.getOrderUnitSize().toString())));
                    if(ObjectUtils.isNotEmpty(supplierOrderDtl.getExpressNo())){
                        afterDetailOpenDto.setExpressNo(supplierOrderDtl.getExpressNo());
                    }
                    if(ObjectUtils.isNotEmpty(supplierOrderDtl.getExpressCom())){
                        afterDetailOpenDto.setExpressCom(supplierOrderDtl.getExpressCom());
                    }

                    String packageType = null;
                    String itemBarcode = null;
                    //设置大中小单位信息
                    if(UnitTypeEnum.S(afterDtl.getOrderUnitType())){
                        packageType = UnitTypeEnum.UNIT_SMALL.getErpType();
                        itemBarcode = skuDTO.getBarcode();
                    }else if(UnitTypeEnum.M(afterDtl.getOrderUnitType())){
                        packageType = UnitTypeEnum.UNIT_MIDDLE.getErpType();
                        itemBarcode = skuDTO.getMidBarcode();
                    }else if(UnitTypeEnum.L(afterDtl.getOrderUnitType())){
                        packageType = UnitTypeEnum.UNIT_LARGE.getErpType();
                        itemBarcode = skuDTO.getLargeBarcode();
                    }
                    afterDetailOpenDto.setPackageType(packageType);
                    afterDetailOpenDto.setItemBarcode(itemBarcode);

                    afterDetailOpenDtos.add(afterDetailOpenDto);*/
                });

/*        if (SheetTypeConstants.SHS.equals(supplierAfter.getTransNo())) {
            TrdAfter trdAfter = trdAfterMapper.selectOne(new LambdaQueryWrapperX<TrdAfter>().eq(TrdAfter::getAfterNo, supplierAfter.getAfterNo()));
            AfterSheetOpenDto afterSheetOpenDto = new AfterSheetOpenDto();
            afterSheetOpenDto.setSubList(afterDetailOpenDtos);
            afterSheetOpenDto.setSheetNo(supplierAfter.getSupplierAfterNo());
            afterSheetOpenDto.setTransNo("XT");
            afterSheetOpenDto.setSheetDate(DateUtils.getTime());
            afterSheetOpenDto.setRefundStyle(trdAfter.getPayWay());
            afterSheetOpenDto.setConsumerNo(trdAfter.getBranchId().toString());

            BranchDTO branchDTO = trdCacheService.getBranchDTO(trdAfter.getBranchId());
            afterSheetOpenDto.setSenderDetailAddr(branchDTO.getBranchAddr());
            afterSheetOpenDto.setSenderTel(branchDTO.getContactPhone());
            afterSheetOpenDto.setSenderMobile(branchDTO.getContactPhone());
            afterSheetOpenDto.setSenderName(branchDTO.getBranchName());


//            log.info("全国、发送mq实体数据：{},业务员id：{}，退款方式：{}，门店id：{}",afterSheetOpenDto,trdAfter.getColonelId(),trdAfter.getPayWay(),trdAfter.getBranchId());
//            tradeMqProducer.sendErpTradeOrderEvent(new OrderSendMqOpenDTO(afterSheetOpenDto, trdAfter.getColonelId(), trdAfter.getPayWay(), trdAfter.getBranchId(), String.valueOf(supplierAfter.getSupplierAfterId()), trdAfter.getDcId()));
            log.info("开始发送全国售后订单：{}",trdAfter.getAfterNo());
            tradeMqProducer.sendSyncDataAfterEvent(trdAfter.getAfterNo());

        }*/

        if (Objects.equals(supplierAfter.getTransNo(), SheetTypeConstants.SHS) || Objects.equals(supplierAfter.getTransNo(), "SHS")) {
            log.info("开始发送全国售后订单：{}", supplierAfter.getAfterNo());
            tradeMqProducer.sendSyncDataAfterEvent(supplierAfter.getAfterNo());

        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void afterRefundSuccessCallback(PayRefundVO refundVO) {
        /**
         * 退款成功
         * 1、更新售后订单商品状态为退款完成 {@link com.zksr.common.core.enums.RefundStateEnum}
         */
        List<TrdSupplierAfterDtl> afterDtl = trdSupplierAfterDtlMapper.selectAfterDtlByAfterDtlNo(refundVO.getRefundNo());
        if (ToolUtil.isEmpty(afterDtl)) {
            return;
        }
        // 退款事件处理
        trdAfterHandlerService.forEach(service -> service.payRefundSuccess(afterDtl));

        //统计退货金额
        BigDecimal returnAmt = afterDtl.stream().map(TrdSupplierAfterDtl::getReturnAmt).reduce(BigDecimal.ZERO, BigDecimal::add);

        //查询售后订单信息
        TrdAfter tar = trdAfterMapper.selectById(afterDtl.get(0).getAfterId());
        /**
         * 2、写入订单结算数据
         */
        List<TrdSettle> settles = trdSettleService.createBatchAfterSettle(afterDtl)
                // 数据过滤  当付款方式等于储值支付时，过滤掉非业务员的结算流水
                .stream().filter(settle ->
                        !Objects.equals(tar.getPayWay(), OrderPayWayEnum.WALLET.getPayWay())
                                || (Objects.equals(tar.getPayWay(), OrderPayWayEnum.WALLET.getPayWay()) && Objects.equals(settle.getMerchantType(), MerchantTypeEnum.COLONEL.getType()))
                ).collect(Collectors.toList());
        trdSettleMapper.insertBatch(settles);
        // 更新结算状态
        trdSettleService.updateSettle(afterDtl);

        /**
         * 3、取消冻结入驻商分成利润金额流水 TODO 应入驻商充值方案弃用，故此代码注释
         */
//        trdSettleService.cancelSupplierFreezeFlow(settles, afterDtl.get(0));

        //发送业务员APP首页信息 客户下单订单MQ
        if (ToolUtil.isNotEmpty(tar) && ToolUtil.isNotEmpty(tar.getColonelId())) {
            // 当前业务员提成售后金额
            BigDecimal returnColonel2Amt =
                    settles.stream().filter(settle -> settle.getMerchantType().equalsIgnoreCase(MerchantTypeEnum.COLONEL.getType()) && Objects.equals(settle.getMerchantId(), tar.getColonelId()))
                            .map(TrdSettle::getSettleAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 当前上级业务员提成售后金额
            BigDecimal returnColonel1Amt =
                    settles.stream().filter(settle -> settle.getMerchantType().equalsIgnoreCase(MerchantTypeEnum.COLONEL.getType()) && Objects.equals(settle.getMerchantId(), tar.getPcolonelId()))
                            .map(TrdSettle::getSettleAmt).reduce(BigDecimal.ZERO, BigDecimal::add);

            tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(tar.getColonelId(), tar.getSysCode(), tar.getBranchId(), returnAmt, returnColonel2Amt, COLONEL_APP_Page_DATA_TYPE_4));
            if (NumberUtil.isGreater(returnColonel1Amt, BigDecimal.ZERO)) {
                tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(tar.getPcolonelId(), tar.getSysCode(), tar.getBranchId(), returnAmt, returnColonel1Amt, COLONEL_APP_PAGE_DATA_TYPE_5));
            }
        }

        // B2B 生成的发货前退款单不推送第三方  （根据第三方数据生成的发货前退款单需要推送 --差异退货单）
        if (Objects.equals(afterDtl.get(NumberPool.INT_ZERO).getAfterPhase(), AfterPhaseEnum.REDUND_BEFORE_SHIPMENT.getState())
                && !Objects.equals(tar.getSource(), NumberPool.LONG_FIVE)) {
            log.info("退款单：{}属于由B2B生成的发货前退款，不予推送第三方！", tar.getAfterNo());
            return;
        }

        /**
         * 4、B2B发送  付款的退款信息 推送ERP
         */
        tradeMqProducer.sendSyncDataReceiptEvent(
                new SyncReceiptSendDTO()
                        .setSheetType(SheetTypeConstants.SHS)
                        .setSupplierSheetNo(afterDtl.get(NumberPool.INT_ZERO).getSupplierAfterNo())
//                        .setSupplierDtlIdList(afterDtl.stream().map(TrdSupplierAfterDtl::getSupplierAfterDtlId).collect(Collectors.toList()))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void afterRefundFailCallback(PayRefundVO refundVO) {
        /**
         * 退款失败  更新售后订单商品状态为退款失败 {@link com.zksr.common.core.enums.RefundStateEnum}
         */
        List<TrdSupplierAfterDtl> afterDtls = trdSupplierAfterDtlMapper.selectAfterDtlByAfterDtlNo(refundVO.getRefundNo());
        afterDtls.forEach(dtl -> {
            dtl.setRefundFailReason(refundVO.getErrorMsg());
            trdAfterHandlerService.forEach(service -> {
                service.payRefundFail(dtl);
            });
        });

    }

    @Override
    public TrdAfter getTrdAfterByAfterNo(String afterNo) {
        return trdAfterMapper.getTrdAfterByAfterNo(afterNo);
    }

    @Override
    public TrdAfter getTrdAfterById(Long afterId) {
        return trdAfterMapper.selectById(afterId);
    }

    @DistributedLock(prefix = RedisLockConstants.LOCK_AFTER_ORDER, condition = "#afterId", tryLock = true, leaseTime = 100L)
    @Override
    public void afterApprove(Long afterId) {
        log.info("售后单开始审核,afterId:{}", afterId);
        TrdAfter after = trdAfterMapper.selectByAfterId(afterId);
        if (ToolUtil.isEmpty(after)) {
            log.error("售后订单ID【{}】未查询到数据！", afterId);
            return;
        }
        // 查询入驻商售后订单
        List<TrdSupplierAfter> tsaList = trdSupplierAfterMapper.selectListByOrderNo(after.getAfterNo());
        // 查询售后订单明细
        List<TrdSupplierAfterDtl> afterDtlList = trdSupplierAfterDtlMapper.selectListByAfterId(after.getAfterId());
        // 查询售后优惠明细数据
        List<TrdAfterDiscountDtl> afterDiscountDtlList = trdAfterDiscountDtlMapper.selectListByAfterId(after.getAfterId());

        if (Objects.equals(afterDtlList.get(NumberPool.INT_ZERO).getApproveState(), ApproveStateEnum.COMPLETE_APPROVE.getState())) {
            log.error("售后订单【{}】状态为 已审核，重复操作！", after.getAfterNo());
            return;
        }

        List<String> supplierOrderNos = tsaList.stream().map(TrdSupplierAfter::getSupplierOrderNo).collect(Collectors.toList());

        // 查询销售主订单
        TrdOrder tor = trdOrderMapper.selectById(after.getOrderId());
        // 查询订单入驻商表数据
        List<TrdSupplierOrder> tsoList = trdSupplierOrderMapper.selectListBySupplierOrderNoList(supplierOrderNos);
        // 查询入驻商订单明细表数据
        List<TrdSupplierOrderDtl> tsodList = trdSupplierOrderDtlMapper.selectList(new LambdaQueryWrapper<TrdSupplierOrderDtl>().in(TrdSupplierOrderDtl::getSupplierOrderNo, supplierOrderNos));
        // 查询入驻商订单优惠表表数据
        List<Long> supplierOrderIds = tsoList.stream().map(TrdSupplierOrder::getSupplierOrderId).collect(Collectors.toList());
        List<TrdOrderDiscountDtl> toddList = trdOrderDiscountDtlMapper.selectList(new LambdaQueryWrapper<TrdOrderDiscountDtl>().in(TrdOrderDiscountDtl::getSupplierOrderId, supplierOrderIds));

        // 售后订单审核操作处理
        SpringUtils.getBean(TrdAfterServiceImpl.class).afterApproveSubmit(tor, tsoList, tsodList, toddList, after, tsaList, afterDtlList, afterDiscountDtlList);

        //创建订单后(发货前售后返还商品库存及促销库存操作)
        trdAfterHandlerService.forEach(service -> {
            service.afterOrderAfterReturnActivity(tor, tsoList, tsodList, toddList, after, afterDtlList, afterDiscountDtlList);
        });


        //该订单 不是取消状态的 入驻商订单详情条数
        //校验 该订单是否是首单订单  并且该售后单取消时  是否是整个订单都处于取消
        if (trdSupplierOrderDtlMapper.checkOrderCancel(tor.getOrderId()) == NumberPool.LONG_ZERO) {
            //首单校验
            //订单取消  校验是否是首单
            BranchDTO branchDTO = trdCacheService.getBranchDTO(tor.getBranchId());
            if (branchDTO.fistOrderNoFlagTrue(tor.getOrderNo())) {
                //如果是首单
                //校验该门店是否存在已支付的订单
                //校验是否是首单单号取消
                //如果是首单单号取消 需要校验该门店是否存在已支付的订单
                //如果不存在，修改首单标识、清除首单单号
                //如果存在，则不是首单了
                if (trdSupplierOrderDtlMapper.checkOrderDeliveryStatusCountByBranchId(tor.getBranchId(), tor.getOrderId()) == NumberPool.INT_ZERO) {
                    //修改首单标识、清除首单单号
                    branchDTO.setFirstOrderNo(null);
                    branchDTO.setFirstOrderFlag(null);
                    branchApi.updateFirstOrder(branchDTO);
                }
            }

            //订单取消 需要更新门店生命周期
            try {
                branchApi.updateBranchLifecycle(
                        tor.getBranchId(),
                        BRANCH_LIFECYCLE_OPERATION_TYPE_3,
                        tor.getOrderId(), after.getAfterId()
                );
            } catch (Exception e) {
                //如果新增门店生命周期失败了  不影响订单取消
                log.error("门店生命周期---新增门店下单取消信息异常", e);
            }

        }
    }

    /**
     * 售后订单审核操作处理
     *
     * @param tor                  总订单数据
     * @param tsoList              订单入驻商数据
     * @param tsodList             订单明细商品数据
     * @param toddList             订单优惠数据
     * @param after                售后订单数据
     * @param afterDtlList         售后订单明细数据
     * @param afterDiscountDtlList 售后订单明细优惠数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void afterApproveSubmit(TrdOrder tor, List<TrdSupplierOrder> tsoList, List<TrdSupplierOrderDtl> tsodList, List<TrdOrderDiscountDtl> toddList,
                                   TrdAfter after, List<TrdSupplierAfter> tsaList, List<TrdSupplierAfterDtl> afterDtlList, List<TrdAfterDiscountDtl> afterDiscountDtlList) {
        // 更新订单已退款手续费金额
        trdPriceService.updateOrderInfo(tor, tsoList, after, tsaList, StringPool.PLUS);

        // 售后订单保存后操作
        afterDtlList.forEach(afterDtl -> {
            TrdSupplierOrderDtl tsod = tsodList.stream().filter(tsods -> Objects.equals(tsods.getSupplierOrderDtlId(), afterDtl.getSupplierOrderDtlId())).findFirst().orElse(null);
            trdAfterHandlerService.forEach(service -> {
                service.afterOrderAfterCreate(afterDtl, tor, tsod);
            });
        });

        // 更新入驻商订单为已取消
//        tsoList.forEach(tso -> {
//            tso.setDeliveryState(SupplierOrderStatusEnum.CANCELED.getCode());
//        });

        // 重新计算下订单的数量和金额进行更改 (发货前售后)
        trdPriceService.recalculateOrderAmt(tor, tsoList, tsodList);

        // 计算储值支付本金支付金额、储值赠金支付金额
        trdPriceService.calculatorAfterCzPayWayGiveAmt(after);

        // 发起退款申请
        trdAfterHandlerService.forEach(service -> {
            for (TrdSupplierOrder supplierOrder : tsoList) {
                service.createPayRefundOrder(supplierOrder.getOrderNo(), supplierOrder.getSupplierOrderNo(), after, afterDtlList);
            }
        });

        // 更新
        trdSupplierOrderMapper.updateBatch(tsoList);
        trdSupplierOrderDtlMapper.updateBatch(tsodList);
    }

    @Override
    public Integer getUnfinishedAfterByOrderId(Long orderId) {
        return trdAfterMapper.selectUnfinishedAfterByOrderId(orderId);
    }

    @Override
    public List<HomePagesOrderAfterDataRespDTO> getHomePagesOrderAfterData(HomePagesReqVO reqVO) {
        return trdAfterMapper.getHomePagesOrderAfterData(reqVO);
    }

    @Override
    public BigDecimal getColonelAppPageAfterAmt(Long colonelId) {
        return trdAfterMapper.getColonelAppPageAfterAmt(colonelId);
    }

    // 构建订单
    private OrderAfterSaveVO buildAfterOrder(OrderAfterResp resp, TrdOrder trdOrder, List<TrdSupplierOrder> supplierOrders,
                                             List<TrdSupplierOrderDtl> supplierOrderDtls, List<TrdSupplierOrderSettle> supplierOrderSettles, List<TrdOrderDiscountDtl> orderDiscountDtls) {
        // 构建主售后订单
        TrdAfter after = TrdAfterOrderConvert.INSTANCE.convertAfter(trdOrder, resp);
        after.setAfterNo(SheetTypeConstants.SH + redisService.getUniqueNumber());
        after.setIsCancel(StatusConstants.ISCANCEL_0);
        after.setRefundAmt(BigDecimal.ZERO); // 总售后订单金额
        after.setReturnDiscountAmt(BigDecimal.ZERO); // 本次售后订单优惠金额
        after.setReturnOrderAmt(BigDecimal.ZERO); // 订单本次售后退款金额（不包括优惠）
        after.setCreateBy(resp.getCreateUserName());


        OrderAfterSaveVO afterSaveVO = new OrderAfterSaveVO();
        afterSaveVO.setAfterVO(after);
        afterSaveVO.setSupplierAfterVOs(new ArrayList<>());
        afterSaveVO.setSupplierAfterDtlVOs(new ArrayList<>());
        afterSaveVO.setSupplierAfterSettleVOs(new ArrayList<>());
        afterSaveVO.setAfterLogVOs(new ArrayList<>());
        afterSaveVO.setAfterDiscountDtlVOs(new ArrayList<>());

        /**
         * 这里需要根据订单明细ID或单号查询出已售后的数量
         * 1：如果当前是最后一次售后，则售后金额 = 订单明细金额- 已售后金额
         * 2：如果不是最后一次售后，则售后金额 = 单价 * 售后数量
         */
        List<Long> orderDtls = resp.getSupplierOrders().stream().flatMap(supplierOrder -> supplierOrder.getSupplierOrderDtls().stream())
                .map(OrderAfterResp.SupplierOrder.SupplierOrderDtl::getSupplierOrderDtlId).collect(Collectors.toList());
        // 已售后总数量
        List<OrderAfterDtlResDTO> orderAfterDtlResDTOS = trdSupplierAfterDtlMapper.getAfterDtlNumBySupplierDtlId(orderDtls);
        Map<Long, OrderAfterDtlResDTO> orderAfterDtlResDTOMap = convertMap(orderAfterDtlResDTOS, OrderAfterDtlResDTO::getSupplierOrderDtlId);

        // 售后类型  发货前售后为 仅退款   收货后售后为 退货退款
        Long afterType = Objects.equals(DeliveryStatusEnum.WAIT_FH.getCode(), resp.getDeliveryState()) ? AfterTypeEnum.REDUNDONLY.getState() : AfterTypeEnum.RETURNSANDREFUNDS.getState();

        // 构建售后入驻商订单
        resp.getSupplierOrders().forEach(supplierAfter -> {
            supplierOrders.stream().filter((supplierOrder -> supplierOrder.getSupplierOrderId().compareTo(supplierAfter.getSupplierOrderId()) == 0))
                    .forEach(supplier -> {
                        // 校验入驻商订单是否已发送 第三方系统  如果是出库订单回传是允许差异退货的
//                        if (ToolUtil.isNotEmpty(supplier)
//                                && ToolUtil.isNotEmpty(supplier.getPushStatus())
//                                && ( Objects.equals(supplier.getPushStatus(), OpenApiConstants.ORDER_SYNC_FLAG_1)
//                                        || Objects.equals(supplier.getPushStatus(), OpenApiConstants.ORDER_SYNC_FLAG_2) )
//                                && Objects.equals(afterType, AfterTypeEnum.REDUNDONLY.getState())
//                                && !Objects.equals(resp.getSource(),NumberPool.LONG_FIVE)) {
//                            throw exception(TRD_ORDER_NOT_AFTER, supplier.getOrderNo());
//                        }

                        TrdSupplierAfter supplierAfterOrder = TrdAfterOrderConvert.INSTANCE.convertSupplierAfter(supplier);
                        supplierAfterOrder.setAfterNo(after.getAfterNo());
                        supplierAfterOrder.setSupplierAfterNo(SheetTypeConstants.SHS + redisService.getUniqueNumber());
                        supplierAfterOrder.setSubRefundAmt(BigDecimal.ZERO); // 入驻商售后订单金额
                        supplierAfterOrder.setSubRefundFee(BigDecimal.ZERO); // 入驻商售后订单手续费
                        supplierAfterOrder.setSourceOrderNo(supplierAfter.getSourceOrderNo());
                        supplierAfterOrder.setTransNo(supplierAfter.getTransNo());
                        supplierAfterOrder.setReturnSubOrderAmt(BigDecimal.ZERO);
                        supplierAfterOrder.setSubDiscountAmt(BigDecimal.ZERO);
                        supplierAfterOrder.setCreateBy(resp.getCreateUserName());

                        //新增售后订单时 重置推送状态  由销售订单转换的售后订单 推送状态会变成和销售订单一致 所以需要重置
                        supplierAfterOrder.setPushStatus(ORDER_SYNC_FLAG_0);

                        afterSaveVO.getSupplierAfterVOs().add(supplierAfterOrder);

                        // 构建售后入驻商订单明细
                        AtomicInteger i = new AtomicInteger();
                        supplierAfter.getSupplierOrderDtls().forEach(afterDtl -> {
                            supplierOrderDtls.stream().filter((orderDtl -> orderDtl.getSupplierOrderDtlId().compareTo(afterDtl.getSupplierOrderDtlId()) == 0))
                                    .forEach(orderDtl -> {
                                        i.getAndIncrement();

                                        SpuDTO spu = trdCacheService.getSpuDTO(orderDtl.getSpuId());
                                        // 构建售后订单明细数据
                                        TrdSupplierAfterDtl supplierAfterDtl = TrdAfterOrderConvert.INSTANCE.convertSupplierAfterDtlCreate(orderDtl, afterDtl, after,
                                                orderAfterDtlResDTOMap.get(orderDtl.getSupplierOrderDtlId()), spu);
                                        /**
                                         * 售后状态
                                         */
                                        supplierAfterDtl.setAfterType(resp.getAfterType());
                                        //更新售后详情订单中的同步库存状态 订单生成售后订单的时候把同步库存状态变更成了1 导致库存缓存无法刷新造成库存不一致问题
                                        supplierAfterDtl.setSyncStock(OPEN_FLAG_0);
                                        supplierAfterDtl.setSupplierAfterNo(supplierAfterOrder.getSupplierAfterNo());
                                        supplierAfterDtl.setSupplierAfterDtlNo(supplierAfterOrder.getSupplierAfterNo() + "_" + i);
                                        supplierAfterDtl.setLineNum(i.longValue());
                                        supplierAfterDtl.setPayway(trdOrder.getPayWay());
                                        supplierAfterDtl.setCreateBy(resp.getCreateUserName());
                                        afterSaveVO.getSupplierAfterDtlVOs().add(supplierAfterDtl);

                                        supplierAfterOrder.setSubRefundAmt(supplierAfterOrder.getSubRefundAmt().add(supplierAfterDtl.getReturnAmt()));
                                        supplierAfterOrder.setSubDiscountAmt(supplierAfterOrder.getSubDiscountAmt()
                                                .add(supplierAfterDtl.getReturnActivityDiscountAmt())
                                                .add(supplierAfterDtl.getReturnCouponDiscountAmt())
                                                .add(supplierAfterDtl.getReturnCouponDiscountAmt2())
                                        );

                                        // 构建售后入驻商订单结算信息
                                        supplierOrderSettles.stream().filter((orderSettle -> orderSettle.getSupplierOrderDtlId().compareTo(afterDtl.getSupplierOrderDtlId()) == 0))
                                                .forEach(orderSettle -> {
                                                    TrdSupplierAfterSettle trdSupplierAfterSettle = TrdAfterOrderConvert.INSTANCE.convertSupplierAfterSettle(orderSettle, afterDtl);
                                                    trdSupplierAfterSettle.setSupplierAfterDtlNo(supplierAfterDtl.getSupplierAfterDtlNo())
                                                            .setSupplierAfterNo(supplierAfterOrder.getSupplierAfterNo())
                                                            .setReturnQty(supplierAfterDtl.getReturnQty())
                                                            .setReturnPrice(supplierAfterDtl.getReturnPrice())
                                                            .setReturnAmt(supplierAfterDtl.getReturnAmt())
                                                            .setRefundAmt(supplierAfterDtl.getRefundAmt())
                                                            // 这里需要将售后订单结算信息中的 储值计算金额设置为0，不然后面计算会有问题
                                                            .setReturnCzPrincipalRate(BigDecimal.ZERO)
                                                            .setReturnCzPrincipalPayAmt(BigDecimal.ZERO)
                                                            .setReturnCzGivePayAmt(BigDecimal.ZERO)
                                                    ;

                                                    // 售后单退款结算信息计算
                                                    trdPriceService.calculateAfterRateAmt(trdSupplierAfterSettle);

                                                    afterSaveVO.getSupplierAfterSettleVOs().add(trdSupplierAfterSettle);
                                                });

                                        // 构建售后订单明细优惠信息
                                        orderDiscountDtls.stream().filter(orderDiscountDtl -> Objects.equals(orderDiscountDtl.getSupplierOrderDtlId(), afterDtl.getSupplierOrderDtlId()))
                                                .forEach(orderDiscountDtl -> {
                                                    TrdAfterDiscountDtl trdAfterDiscountDtl = TrdAfterDiscountDtlConvert.INSTANCE.convertOrderDiscount(orderDiscountDtl);
                                                    trdAfterDiscountDtl.setAfterNo(after.getAfterNo());
                                                    trdAfterDiscountDtl.setSupplierAfterDtlNo(supplierAfterDtl.getSupplierAfterDtlNo());
                                                    trdAfterDiscountDtl.setCouponDiscountAmt(supplierAfterDtl.getReturnCouponDiscountAmt());
                                                    trdAfterDiscountDtl.setCouponDiscountAmt2(supplierAfterDtl.getReturnCouponDiscountAmt2());
                                                    trdAfterDiscountDtl.setActivityDiscountAmt(supplierAfterDtl.getReturnActivityDiscountAmt());
                                                    trdAfterDiscountDtl.setGiftQty(supplierAfterDtl.getReturnUnitQty());

                                                    // 活动优惠金额 （分摊）
                                                    if (ToolUtil.isNotEmpty(orderDiscountDtl.getActivityDiscountAmt())) {
                                                        BigDecimal singleActivityDiscountAmt = orderDiscountDtl.getActivityDiscountAmt().divide(new BigDecimal(orderDtl.getTotalNum() + ""), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);
                                                        trdAfterDiscountDtl.setActivityDiscountAmt(singleActivityDiscountAmt.multiply(new BigDecimal(supplierAfterDtl.getReturnQty() + "")).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));
                                                    }

                                                    // 优惠劵优惠金额 （分摊）
                                                    if (ToolUtil.isNotEmpty(orderDiscountDtl.getCouponDiscountAmt())) {
                                                        BigDecimal singleCouponDiscountAmt = orderDiscountDtl.getCouponDiscountAmt().divide(new BigDecimal(orderDtl.getTotalNum() + ""), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);
                                                        trdAfterDiscountDtl.setCouponDiscountAmt(singleCouponDiscountAmt.multiply(new BigDecimal(supplierAfterDtl.getReturnQty() + "")).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));
                                                    }


                                                    // 优惠劵优惠金额 （不分摊）
                                                    if (ToolUtil.isNotEmpty(orderDiscountDtl.getCouponDiscountAmt2())) {
                                                        BigDecimal singleCouponDiscountAmt2 = orderDiscountDtl.getCouponDiscountAmt2().divide(new BigDecimal(orderDtl.getTotalNum() + ""), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);
                                                        trdAfterDiscountDtl.setCouponDiscountAmt2(singleCouponDiscountAmt2.multiply(new BigDecimal(supplierAfterDtl.getReturnQty() + "")).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));
                                                    }

                                                    afterSaveVO.getAfterDiscountDtlVOs().add(trdAfterDiscountDtl);
                                                });

                                        //构建售后入驻商订单明细日志
                                        TrdAfterLog afterLog = new TrdAfterLog();
                                        afterLog.setSupplierAfterDtlNo(supplierAfterDtl.getSupplierAfterDtlNo());
                                        afterLog.setBeforeState(0L);
                                        afterLog.setAfterState(AfterHandleStateEnum.DCL.getCode());
                                        afterLog.setContent(AfterHandleStateEnum.DCL.getContent());
                                        afterLog.setOperateType(StatusConstants.OPER_TYPE_ADD);
                                        afterSaveVO.getAfterLogVOs().add(afterLog);
                                    });
                        });
                        // 入驻商订单本次售后退款金额（不包括优惠） = 售后单实际退款金额 + 售后单优惠金额
                        supplierAfterOrder.setReturnSubOrderAmt(supplierAfterOrder.getSubRefundAmt().add(supplierAfterOrder.getSubDiscountAmt()));
                        after.setRefundAmt(after.getRefundAmt().add(supplierAfterOrder.getSubRefundAmt()));
                        after.setReturnOrderAmt(after.getReturnOrderAmt().add(supplierAfterOrder.getReturnSubOrderAmt()));
                        after.setReturnDiscountAmt(after.getReturnDiscountAmt().add(supplierAfterOrder.getSubDiscountAmt()));
                    });
        });
        return afterSaveVO;
    }

    // 构建订单
    private OrderAfterSaveVO buildMergeAfterOrder(OrderMergeAfterRequest request) {

//        OrderAfterResp resp = trdPriceService.calculateAfterOrderPrice(request);

//        // 查询订单主表数据
//        TrdOrder trdOrder = trdOrderMapper.selectById(request.getOrderId());
//        // 查询订单入驻商表数据
////        List<TrdSupplierOrder> supplierOrders = trdSupplierOrderMapper.selectListByOrderId(request.getOrderId());
//        // 查询入驻商订单明细表数据
//        List<TrdSupplierOrderDtl> supplierOrderDtls = trdSupplierOrderDtlMapper.selectListByOrderId(request.getOrderId());
//        // 查询入驻商订单明细结算数据
//        List<TrdSupplierOrderSettle> supplierOrderSettles = supplierOrderSettleMapper.selectListByOrderId(request.getOrderId());
//        // 查询订单明细优惠数据
//        List<TrdOrderDiscountDtl> orderDiscountDtls = trdOrderDiscountDtlMapper.selectListByOrderId(request.getOrderId());


        // 构建主售后订单
        TrdAfter after = TrdAfterOrderConvert.INSTANCE.convertAfter1(request);
        after.setAfterNo(SheetTypeConstants.SH + redisService.getUniqueNumber());
        after.setIsCancel(StatusConstants.ISCANCEL_0);
        after.setRefundAmt(BigDecimal.ZERO); // 总售后订单金额
        after.setReturnDiscountAmt(BigDecimal.ZERO); // 本次售后订单优惠金额
        after.setReturnOrderAmt(BigDecimal.ZERO); // 订单本次售后退款金额（不包括优惠）

        // 构建入驻商售后订单
        TrdSupplierAfter supplierAfter = TrdAfterOrderConvert.INSTANCE.convertSupplierAfter(request);
        supplierAfter.setAfterNo(after.getAfterNo());
        supplierAfter.setSupplierAfterNo(SheetTypeConstants.SHS + redisService.getUniqueNumber());
        supplierAfter.setSubRefundAmt(BigDecimal.ZERO); // 入驻商售后订单金额
        supplierAfter.setSourceOrderNo(supplierAfter.getSourceOrderNo());
        supplierAfter.setTransNo(supplierAfter.getTransNo());
        supplierAfter.setReturnSubOrderAmt(BigDecimal.ZERO);
        supplierAfter.setSubDiscountAmt(BigDecimal.ZERO);


        OrderAfterSaveVO afterSaveVO = new OrderAfterSaveVO();
        afterSaveVO.setAfterVO(after);
        afterSaveVO.setSupplierAfterVOs(Collections.singletonList(supplierAfter));
        afterSaveVO.setSupplierAfterDtlVOs(new ArrayList<>());
        afterSaveVO.setSupplierAfterSettleVOs(new ArrayList<>());
        afterSaveVO.setAfterLogVOs(new ArrayList<>());
        afterSaveVO.setAfterDiscountDtlVOs(new ArrayList<>());


        request.getSupplierOrderDtls().forEach(tsod -> {

            // 售后类型  发货前售后为 仅退款   收货后售后为 退货退款
            Long afterType = Objects.equals(DeliveryStatusEnum.WAIT_FH.getCode(), request.getDeliveryState()) ? AfterTypeEnum.REDUNDONLY.getState() : AfterTypeEnum.RETURNSANDREFUNDS.getState();

            // 查询订单入驻商表数据
            TrdSupplierOrder tso = trdSupplierOrderMapper.selectById(tsod.getSupplierOrderId());
            if (ToolUtil.isEmpty(tso)) {
                throw exception(TRD_ORDER_NOT_EXISTS);
            }
            // 校验入驻商订单是否已发送 第三方系统  如果是出库订单回传是允许差异退货的
            if (ToolUtil.isNotEmpty(tso.getPushStatus())
                    && (Objects.equals(tso.getPushStatus(), OpenApiConstants.ORDER_SYNC_FLAG_1)
                    || Objects.equals(tso.getPushStatus(), OpenApiConstants.ORDER_SYNC_FLAG_2))
                    && Objects.equals(afterType, AfterTypeEnum.REDUNDONLY.getState())
                    && !Objects.equals(request.getSource(), NumberPool.LONG_FIVE)) {
                throw exception(TRD_ORDER_NOT_AFTER, tso.getOrderNo());
            }
            // 构建售后入驻商订单明细

        });


//
//        /**
//         * 这里需要根据订单明细ID或单号查询出已售后的数量
//         * 1：如果当前是最后一次售后，则售后金额 = 订单明细金额- 已售后金额
//         * 2：如果不是最后一次售后，则售后金额 = 单价 * 售后数量
//         */
//        List<Long> orderDtls = resp.getSupplierOrders().stream().flatMap(supplierOrder -> supplierOrder.getSupplierOrderDtls().stream())
//                .map(OrderAfterResp.SupplierOrder.SupplierOrderDtl::getSupplierOrderDtlId).collect(Collectors.toList());
//        // 已售后总数量
//        List<OrderAfterDtlResDTO> orderAfterDtlResDTOS = trdSupplierAfterDtlMapper.getAfterDtlNumBySupplierDtlId(orderDtls);
//        Map<Long, OrderAfterDtlResDTO> orderAfterDtlResDTOMap = convertMap(orderAfterDtlResDTOS, OrderAfterDtlResDTO::getSupplierOrderDtlId);
//
//        // 售后类型  发货前售后为 仅退款   收货后售后为 退货退款
//        Long afterType = Objects.equals(DeliveryStatusEnum.WAIT_FH.getCode(), resp.getDeliveryState()) ? AfterTypeEnum.REDUNDONLY.getState() : AfterTypeEnum.RETURNSANDREFUNDS.getState();
//
//        // 构建售后入驻商订单
//        resp.getSupplierOrders().forEach(supplierAfter -> {
//            supplierOrders.stream().filter((supplierOrder -> supplierOrder.getSupplierOrderId().compareTo(supplierAfter.getSupplierOrderId()) == 0))
//                    .forEach(supplier -> {
//
//                        TrdSupplierAfter supplierAfterOrder = TrdAfterOrderConvert.INSTANCE.convertSupplierAfter(supplier);
//                        supplierAfterOrder.setAfterNo(after.getAfterNo());
//                        supplierAfterOrder.setSupplierAfterNo(SheetTypeConstants.SHS + redisService.getUniqueNumber());
//                        supplierAfterOrder.setSubRefundAmt(BigDecimal.ZERO); // 入驻商售后订单金额
//                        supplierAfterOrder.setSourceOrderNo(supplierAfter.getSourceOrderNo());
//                        supplierAfterOrder.setTransNo(supplierAfter.getTransNo());
//                        supplierAfterOrder.setReturnSubOrderAmt(BigDecimal.ZERO);
//                        supplierAfterOrder.setSubDiscountAmt(BigDecimal.ZERO);
//
//                        //新增售后订单时 重置推送状态  由销售订单转换的售后订单 推送状态会变成和销售订单一致 所以需要重置
//                        supplierAfterOrder.setPushStatus(ORDER_SYNC_FLAG_0);
//
//                        afterSaveVO.getSupplierAfterVOs().add(supplierAfterOrder);
//
//                        // 构建售后入驻商订单明细
//                        AtomicInteger i = new AtomicInteger();
//                        supplierAfter.getSupplierOrderDtls().forEach(afterDtl -> {
//                            supplierOrderDtls.stream().filter((orderDtl -> orderDtl.getSupplierOrderDtlId().compareTo(afterDtl.getSupplierOrderDtlId()) == 0))
//                                    .forEach(orderDtl -> {
//                                        i.getAndIncrement();
//
//                                        SpuDTO spu = trdCacheService.getSpuDTO(orderDtl.getSpuId());
//                                        // 构建售后订单明细数据
//                                        TrdSupplierAfterDtl supplierAfterDtl = TrdAfterOrderConvert.INSTANCE.convertSupplierAfterDtlCreate(orderDtl, afterDtl, after,
//                                                orderAfterDtlResDTOMap.get(orderDtl.getSupplierOrderDtlId()), spu);
//                                        /**
//                                         * 售后状态
//                                         */
//                                        supplierAfterDtl.setAfterType(resp.getAfterType());
//                                        //更新售后详情订单中的同步库存状态 订单生成售后订单的时候把同步库存状态变更成了1 导致库存缓存无法刷新造成库存不一致问题
//                                        supplierAfterDtl.setSyncStock(OPEN_FLAG_0);
//                                        supplierAfterDtl.setSupplierAfterNo(supplierAfterOrder.getSupplierAfterNo());
//                                        supplierAfterDtl.setSupplierAfterDtlNo(supplierAfterOrder.getSupplierAfterNo() + "_" + i);
//                                        supplierAfterDtl.setLineNum(i.longValue());
//                                        supplierAfterDtl.setPayway(trdOrder.getPayWay());
//                                        afterSaveVO.getSupplierAfterDtlVOs().add(supplierAfterDtl);
//
//                                        supplierAfterOrder.setSubRefundAmt(supplierAfterOrder.getSubRefundAmt().add(supplierAfterDtl.getReturnAmt()));
//                                        supplierAfterOrder.setSubDiscountAmt(supplierAfterOrder.getSubDiscountAmt()
//                                                .add(supplierAfterDtl.getReturnActivityDiscountAmt())
//                                                .add(supplierAfterDtl.getReturnCouponDiscountAmt())
//                                                .add(supplierAfterDtl.getReturnCouponDiscountAmt2())
//                                        );
//
//                                        // 构建售后入驻商订单结算信息
//                                        supplierOrderSettles.stream().filter((orderSettle -> orderSettle.getSupplierOrderDtlId().compareTo(afterDtl.getSupplierOrderDtlId()) == 0))
//                                                .forEach(orderSettle -> {
//                                                    TrdSupplierAfterSettle trdSupplierAfterSettle = TrdAfterOrderConvert.INSTANCE.convertSupplierAfterSettle(orderSettle, afterDtl);
//                                                    trdSupplierAfterSettle.setSupplierAfterDtlNo(supplierAfterDtl.getSupplierAfterDtlNo())
//                                                            .setSupplierAfterNo(supplierAfterOrder.getSupplierAfterNo())
//                                                            .setReturnQty(supplierAfterDtl.getReturnQty())
//                                                            .setReturnPrice(supplierAfterDtl.getReturnPrice())
//                                                            .setReturnAmt(supplierAfterDtl.getReturnAmt())
//                                                            .setRefundAmt(supplierAfterDtl.getRefundAmt())
//                                                    ;
//
//                                                    // 售后单退款结算信息计算
//                                                    trdPriceService.calculateAfterRateAmt(trdSupplierAfterSettle);
//
//                                                    afterSaveVO.getSupplierAfterSettleVOs().add(trdSupplierAfterSettle);
//                                                });
//
//                                        // 构建售后订单明细优惠信息
//                                        orderDiscountDtls.stream().filter(orderDiscountDtl -> Objects.equals(orderDiscountDtl.getSupplierOrderDtlId(), afterDtl.getSupplierOrderDtlId()))
//                                                .forEach(orderDiscountDtl -> {
//                                                    TrdAfterDiscountDtl trdAfterDiscountDtl = TrdAfterDiscountDtlConvert.INSTANCE.convertOrderDiscount(orderDiscountDtl);
//                                                    trdAfterDiscountDtl.setAfterNo(after.getAfterNo());
//                                                    trdAfterDiscountDtl.setSupplierAfterDtlNo(supplierAfterDtl.getSupplierAfterDtlNo());
//                                                    trdAfterDiscountDtl.setCouponDiscountAmt(supplierAfterDtl.getReturnCouponDiscountAmt());
//                                                    trdAfterDiscountDtl.setCouponDiscountAmt2(supplierAfterDtl.getReturnCouponDiscountAmt2());
//                                                    trdAfterDiscountDtl.setActivityDiscountAmt(supplierAfterDtl.getReturnActivityDiscountAmt());
//                                                    trdAfterDiscountDtl.setGiftQty(supplierAfterDtl.getReturnUnitQty());
//                                                    afterSaveVO.getAfterDiscountDtlVOs().add(trdAfterDiscountDtl);
//                                                });
//
//                                        //构建售后入驻商订单明细日志
//                                        TrdAfterLog afterLog = new TrdAfterLog();
//                                        afterLog.setSupplierAfterDtlNo(supplierAfterDtl.getSupplierAfterDtlNo());
//                                        afterLog.setBeforeState(0L);
//                                        afterLog.setAfterState(AfterHandleStateEnum.DCL.getCode());
//                                        afterLog.setContent(AfterHandleStateEnum.DCL.getContent());
//                                        afterLog.setOperateType(StatusConstants.OPER_TYPE_ADD);
//                                        afterSaveVO.getAfterLogVOs().add(afterLog);
//                                    });
//                        });
//                        // 入驻商订单本次售后退款金额（不包括优惠） = 售后单实际退款金额 + 售后单优惠金额
//                        supplierAfterOrder.setReturnSubOrderAmt(supplierAfterOrder.getSubRefundAmt().add(supplierAfterOrder.getSubDiscountAmt()));
//                        after.setRefundAmt(after.getRefundAmt().add(supplierAfterOrder.getSubRefundAmt()));
//                        after.setReturnOrderAmt(after.getReturnOrderAmt().add(supplierAfterOrder.getReturnSubOrderAmt()));
//                        after.setReturnDiscountAmt(after.getReturnDiscountAmt().add(supplierAfterOrder.getSubDiscountAmt()));
//                    });
//        });
        return afterSaveVO;
    }

    // 保存订单操作
    private TrdAfter saveAfter(OrderAfterSaveVO afterSaveVO) {
        /**
         * 1、 保存售后主订单
         */
        TrdAfter after = afterSaveVO.getAfterVO();
        trdAfterMapper.insert(after);

        /**
         * 2、保存售后入驻商订单
         */
        List<TrdSupplierAfter> supplierAfterList = afterSaveVO.getSupplierAfterVOs().stream().map(supplierAfter -> {
            supplierAfter.setAfterId(after.getAfterId());
            return supplierAfter;
        }).collect(Collectors.toList());
        trdSupplierAfterMapper.insertBatch(supplierAfterList);

        /**
         *  3、保存售后入驻商订单明细
         */
        Map<String, TrdSupplierAfter> supplierAfterMap = supplierAfterList.stream().collect(Collectors.toMap(TrdSupplierAfter::getSupplierAfterNo, TrdSupplierAfter -> TrdSupplierAfter));
        List<TrdSupplierAfterDtl> supplierAfterDtlList = afterSaveVO.getSupplierAfterDtlVOs().stream().map(supplierAfterDtl -> {
            TrdSupplierAfter supplierAfter = supplierAfterMap.get(supplierAfterDtl.getSupplierAfterNo());
            supplierAfterDtl.setSupplierAfterId(supplierAfter.getSupplierAfterId());
            supplierAfterDtl.setAfterId(after.getAfterId());
            return supplierAfterDtl;
        }).collect(Collectors.toList());
        trdSupplierAfterDtlMapper.insertBatch(supplierAfterDtlList);

        /**
         * 4、保存售后入驻商订单明细结算
         */
        Map<String, TrdSupplierAfterDtl> supplierAfterDtlMap = supplierAfterDtlList.stream().collect(Collectors.toMap(TrdSupplierAfterDtl::getSupplierAfterDtlNo, TrdSupplierAfterDtl -> TrdSupplierAfterDtl));
        List<TrdSupplierAfterSettle> supplierAfterSettleList = afterSaveVO.getSupplierAfterSettleVOs().stream().map(trdSupplierAfterSettle -> {
            TrdSupplierAfterDtl supplierAfterDtl = supplierAfterDtlMap.get(trdSupplierAfterSettle.getSupplierAfterDtlNo());
            TrdSupplierAfter supplierAfter = supplierAfterMap.get(supplierAfterDtl.getSupplierAfterNo());
            trdSupplierAfterSettle.setAfterId(after.getAfterId());
            trdSupplierAfterSettle.setSupplierAfterDtlId(supplierAfterDtl.getSupplierAfterDtlId());
            trdSupplierAfterSettle.setSupplierAfterId(supplierAfter.getSupplierAfterId());
            return trdSupplierAfterSettle;
        }).collect(Collectors.toList());
        trdSupplierAfterSettleMapper.insertBatch(supplierAfterSettleList);

        /**
         * 5、保存售后订单日志
         */
        afterSaveVO.getAfterLogVOs().forEach(afterDtlLog -> {
            TrdSupplierAfterDtl supplierAfterDtl = supplierAfterDtlMap.get(afterDtlLog.getSupplierAfterDtlNo());
            afterDtlLog.setSupplierAfterDtlId(supplierAfterDtl.getSupplierAfterDtlId());
        });
        trdAfterLogMapper.insertBatch(afterSaveVO.getAfterLogVOs());

        /**
         *  6、保存订单售后优惠信息
         */
        if (ToolUtil.isNotEmpty(afterSaveVO.getAfterDiscountDtlVOs()) && !afterSaveVO.getAfterDiscountDtlVOs().isEmpty()) {
            afterSaveVO.getAfterDiscountDtlVOs().forEach(afterDiscountDtl -> {
                TrdSupplierAfterDtl supplierAfterDtl = supplierAfterDtlMap.get(afterDiscountDtl.getSupplierAfterDtlNo());
                afterDiscountDtl.setSupplierAfterDtlId(supplierAfterDtl.getSupplierAfterDtlId());
                afterDiscountDtl.setAfterId(after.getAfterId());
            });
            trdAfterDiscountDtlMapper.insertBatch(afterSaveVO.getAfterDiscountDtlVOs());
        }

        //清空门店我的订单状态数据缓存
        trdCacheService.clearOrderTotal(after.getBranchId());
        //清空业务员下单门店订单状态数据缓存
        trdCacheService.clearOrderTotal(after.getColonelId());

        return after;
    }

    private void validateTrdAfterExists(Long afterId) {
        if (trdAfterMapper.selectById(afterId) == null) {
            throw exception(TRD_AFTER_NOT_EXISTS);
        }
    }

    // 验证审核数据
    private void checkeApproveData(AfterApproveDtlEditVO editVO) {
        if (ToolUtil.isEmpty(editVO) || ToolUtil.isEmpty(editVO.getAfterDtlList()) || editVO.getAfterDtlList().size() <= 0) {
            throw new ServiceException("提交明细数据为空！");
        }
    }

    /**
     * 校验售后单是否可以取消
     *
     * @param after
     * @param dtlList
     * @param cancelStateType 枚举类：AfterHandleStateEnum  9：同意申请前取消  10：同意申请后取撤销
     */
    public void checkeAfterCancelData(TrdAfter after, List<TrdSupplierAfterDtl> dtlList, Long cancelStateType, Long sourceType) {
        // 校验售后单主表
        if (ToolUtil.isEmpty(after)) {
            throw exception(TRD_AFTER_NOT_EXISTS);
        }
        // 校验是否已取消
        if (Objects.equals(after.getIsCancel(), NumberPool.LONG_ONE)) {
            throw exception(TRD_AFTER_ALREADY_CANCEL, after.getAfterNo());
        }
        // 校验是否是pc端取消 且 入驻商是否已对接第三方
        if (AfterHandleStateEnum.isRevoke(cancelStateType) && SourceType.isPc(sourceType)) {
            Long supplierId = dtlList.get(NumberPool.INT_ZERO).getSupplierId();
            VisualSettingDetailDto visualSettingDetailDto = trdCacheService.getVisualDetailBySupplier(supplierId + StringPool.COLON + B2BRequestType.SYNC_B2B_AFTER_SHEET.getB2bType());
            if (ToolUtil.isNotEmpty(visualSettingDetailDto) && ToolUtil.isNotEmpty(visualSettingDetailDto.getVisualDetailId())) {
                throw exception(TRD_SUPPLIER_AFTER_PUSHED, supplierId);
            }
        }
        // 校验售后单明细
        if (ToolUtil.isEmpty(dtlList)) {
            throw exception(TRD_SUPPLIER_AFTER_DTL_NOT_EXISTS);
        }
        // 更新明细表
        dtlList.forEach(dtl -> {
            // 校验是否已取消
            if (Objects.equals(dtl.getIsCancel(), NumberPool.LONG_ONE)) {
                throw exception(TRD_SUPPLIER_AFTER_DTL_ALREADY_CANCEL, dtl.getSupplierAfterDtlId());
            }
            // 校验是否已审核
            if (Objects.equals(dtl.getApproveState(), NumberPool.LONG_ONE) && AfterHandleStateEnum.isCalcel(cancelStateType)) {
                throw exception(TRD_AFTER_ALREADY_APPROVE, dtl.getSupplierAfterDtlId());
            }
            // 当前状态为撤销售后，当审核状态不为 已审核 或者 退款状态不是 未退款，则不能撤销售后
            if (AfterHandleStateEnum.isRevoke(cancelStateType)
                    && (!Objects.equals(dtl.getApproveState(), NumberPool.LONG_ONE) || !Objects.equals(dtl.getRefundState(), NumberPool.LONG_ZERO))
            ) {
                throw exception(TRD_SUPPLIER_AFTER_ALREADY_CANCEL, dtl.getSupplierAfterDtlId());
            }

            dtl.setIsCancel(StatusConstants.ISCANCEL_1);
            if (ToolUtil.isNotEmpty(SecurityUtils.getLoginUser()) && SecurityUtils.isSupplier()) { // 入驻商操作时，同时将审核状态更改成拒绝\
                // 当前状态为 已审核状态 说明是撤销售后，将审核状态更新为 撤销售后状态 ，否则为拒绝
                dtl.setApproveState(Objects.equals(dtl.getApproveState(), NumberPool.LONG_ONE) ? NumberPool.LONG_THREE : NumberPool.LONG_TWO);
            } else {
                dtl.setApproveState(NumberPool.LONG_ZERO);
            }
        });
        after.setIsCancel(StatusConstants.ISCANCEL_1);
    }

    /**
     * 入驻商售后订单查询 操作物流信息
     *
     * @param afterDtl 入驻商售后订单明细
     * @return
     */
    private List<AfterDeliveryDTO> getSupplierAfterDeliveryLog(AfterDtlListDTO afterDtl) {

        List<AfterDeliveryDTO> afterDeliveryList = new ArrayList<>();

        // 获取第一条售后明细数据状态
//        TrdSupplierAfterDtl trdSupplierAfterDtl = trdSupplierAfterDtlMapper.selectById(afterDtl.getSupplierAfterDtlId());

        /**
         * 处理B2B当前自己的售后流程状态
         */
        List<TrdAfterLog> trdAfterLogList = trdAfterLogMapper.selectListBySupplierAfterDtlId(afterDtl.getSupplierAfterDtlId());
        trdAfterLogList.forEach(afterLog -> {
            AfterLogisticsStatusEnum afterLogisticsStatusEnum = AfterLogisticsStatusEnum.getAfterLogisticsStatusEnum(String.valueOf(afterLog.getAfterState()));
            if (ToolUtil.isNotEmpty(afterLogisticsStatusEnum)) {
                // 本地商品不渲染 上传物流单号节点
                if (afterLogisticsStatusEnum.getCode().equals(AfterLogisticsStatusEnum.LOGISTCS.getCode()) && Objects.equals(afterDtl.getItemType(), NumberPool.LONG_ONE)) {
                    return;
                }
                afterDeliveryList.add(
                        AfterDeliveryDTO.builder()
                                .operateTime(afterLog.getCreateTime())
                                .state(afterLogisticsStatusEnum.getCode())
                                .statusName(afterLogisticsStatusEnum.getName())
                                .build()
                );
            }
        });

        /**
         * 处理B2B第三方物流状态
         */
        List<TrdExpressStatus> trdExpressStatusList = trdExpressStatusMapper.selectBySupplierOrderNo(afterDtl.getSupplierAfterNo());
        trdExpressStatusList.forEach(afterStatusLog -> {
            afterDeliveryList.add(
                    AfterDeliveryDTO.builder()
                            .operateTime(afterStatusLog.getStartTime())
                            .state(afterStatusLog.getLogisticsStatus())
                            .statusName(afterStatusLog.getLogisticsStatusInfo())
                            .sourceOrderNo(afterStatusLog.getSourceOrderNo())
                            .describe(afterStatusLog.getMemo())
                            .build()
            );
        });

        return afterDeliveryList.stream()
                .sorted(Comparator.comparing(AfterDeliveryDTO::getOperateTime, Comparator.nullsFirst(Date::compareTo))).collect(Collectors.toList());
    }


    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 售后单 TODO 补充编号 ==========
    // ErrorCode TRD_AFTER_NOT_EXISTS = new ErrorCode(TODO 补充编号, "售后单不存在");


}
