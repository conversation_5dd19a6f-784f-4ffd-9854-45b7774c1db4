package com.zksr.trade.convert.car;

import com.zksr.common.core.domain.dto.car.AppCarItemDTO;
import com.zksr.trade.api.car.vo.TrdCarApiRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/9 18:41
 */
@Mapper
public interface TrdCarConvert {

    TrdCarConvert INSTANCE = Mappers.getMapper(TrdCarConvert.class);

    @Mappings({
            @Mapping(source = "productNum", target = "qty")
    })
    TrdCarApiRespVO convert(AppCarItemDTO carItemDTO);

    List<TrdCarApiRespVO> convert(List<AppCarItemDTO> list);

}
