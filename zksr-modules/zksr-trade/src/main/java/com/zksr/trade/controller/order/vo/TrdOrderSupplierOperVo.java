package com.zksr.trade.controller.order.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年04月10日 18:42
 * @description: TrdOrderSupplierOperVo
 */
@Data
@Accessors(chain = true)
public class TrdOrderSupplierOperVo {
    /** 订单ID */
    private Long orderId;
    /** 入驻商ID */
    private Long supplierId;
    /** 商品状态 */
    private Long deliveryStatus;
    /** 发货时间 */
    private Date deliveryTime;
    /** 收货时间 */
    private Date receiveTime;
    /** 完成时间 */
    private Date completeTime;

    /** 0 :全国 1：本地 */
    private Integer itemType;
    /** 订单明细ID */
    private Long supplierOrderDtlId;

    /**
     * 支付平台
     */
    private String platform;
    /**
     * 支付状态
     */
    private Integer payState;

    /**
     *  否过滤已取消数据 1: 是 ，0 ：否
     */
    private Integer isFilterCancelStatus;
}
