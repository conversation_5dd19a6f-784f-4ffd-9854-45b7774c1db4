package com.zksr.trade.controller.orderSettle;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdSupplierOrderSettle;
import com.zksr.trade.service.ITrdSupplierOrderSettleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.orderSettle.vo.TrdSupplierOrderSettlePageReqVO;
import com.zksr.trade.controller.orderSettle.vo.TrdSupplierOrderSettleSaveReqVO;
import com.zksr.trade.controller.orderSettle.vo.TrdSupplierOrderSettleRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 入驻商订单结算信息Controller
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Api(tags = "管理后台 - 入驻商订单结算信息接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/orderSettle")
public class TrdSupplierOrderSettleController {
    @Autowired
    private ITrdSupplierOrderSettleService trdSupplierOrderSettleService;

    /**
     * 新增入驻商订单结算信息
     */
    @ApiOperation(value = "新增入驻商订单结算信息", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "入驻商订单结算信息", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdSupplierOrderSettleSaveReqVO createReqVO) {
        return success(trdSupplierOrderSettleService.insertTrdSupplierOrderSettle(createReqVO));
    }

    /**
     * 修改入驻商订单结算信息
     */
    @ApiOperation(value = "修改入驻商订单结算信息", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "入驻商订单结算信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdSupplierOrderSettleSaveReqVO updateReqVO) {
            trdSupplierOrderSettleService.updateTrdSupplierOrderSettle(updateReqVO);
        return success(true);
    }

    /**
     * 删除入驻商订单结算信息
     */
    @ApiOperation(value = "删除入驻商订单结算信息", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "入驻商订单结算信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{supplierOrderDtlIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] supplierOrderDtlIds) {
        trdSupplierOrderSettleService.deleteTrdSupplierOrderSettleBySupplierOrderDtlIds(supplierOrderDtlIds);
        return success(true);
    }

    /**
     * 获取入驻商订单结算信息详细信息
     */
    @ApiOperation(value = "获得入驻商订单结算信息详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{supplierOrderDtlId}")
    public CommonResult<TrdSupplierOrderSettleRespVO> getInfo(@PathVariable("supplierOrderDtlId") Long supplierOrderDtlId) {
        TrdSupplierOrderSettle trdSupplierOrderSettle = trdSupplierOrderSettleService.getTrdSupplierOrderSettle(supplierOrderDtlId);
        return success(HutoolBeanUtils.toBean(trdSupplierOrderSettle, TrdSupplierOrderSettleRespVO.class));
    }

    /**
     * 分页查询入驻商订单结算信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得入驻商订单结算信息分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdSupplierOrderSettleRespVO>> getPage(@Valid TrdSupplierOrderSettlePageReqVO pageReqVO) {
        PageResult<TrdSupplierOrderSettle> pageResult = trdSupplierOrderSettleService.getTrdSupplierOrderSettlePage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, TrdSupplierOrderSettleRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:settle:add";
        /** 编辑 */
        public static final String EDIT = "trade:settle:edit";
        /** 删除 */
        public static final String DELETE = "trade:settle:remove";
        /** 列表 */
        public static final String LIST = "trade:settle:list";
        /** 查询 */
        public static final String GET = "trade:settle:query";
        /** 停用 */
        public static final String DISABLE = "trade:settle:disable";
        /** 启用 */
        public static final String ENABLE = "trade:settle:enable";
    }
}
