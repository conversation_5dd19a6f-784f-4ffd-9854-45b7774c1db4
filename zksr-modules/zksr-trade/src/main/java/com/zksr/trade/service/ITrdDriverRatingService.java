package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingRespVO;
import com.zksr.trade.domain.TrdDriverRating;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingPageReqVO;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingSaveReqVO;

/**
 * 司机评分Service接口
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
public interface ITrdDriverRatingService {

    /**
     * 新增司机评分
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdDriverRating(@Valid TrdDriverRatingSaveReqVO createReqVO);

    /**
     * 修改司机评分
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdDriverRating(@Valid TrdDriverRatingSaveReqVO updateReqVO);

    /**
     * 删除司机评分
     *
     * @param driverRatingId 司机评分
     */
    public void deleteTrdDriverRating(Long driverRatingId);

    /**
     * 批量删除司机评分
     *
     * @param driverRatingIds 需要删除的司机评分主键集合
     * @return 结果
     */
    public void deleteTrdDriverRatingByDriverRatingIds(Long[] driverRatingIds);

    /**
     * 获得司机评分
     *
     * @param driverRatingId 司机评分
     * @return 司机评分
     */
    public TrdDriverRatingRespVO getTrdDriverRating(Long driverRatingId);

    /**
     * 获得司机评分分页
     *
     * @param pageReqVO 分页查询
     * @return 司机评分分页
     */
    PageResult<TrdDriverRatingRespVO> getTrdDriverRatingPage(TrdDriverRatingPageReqVO pageReqVO);

}
