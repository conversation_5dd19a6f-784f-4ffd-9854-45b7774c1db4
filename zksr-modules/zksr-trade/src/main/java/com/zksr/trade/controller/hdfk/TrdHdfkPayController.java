package com.zksr.trade.controller.hdfk;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayPageReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayRespVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPaySaveReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSkuItemRespVO;
import com.zksr.trade.convert.hdfk.TrdHdfkPayConvert;
import com.zksr.trade.domain.TrdHdfkPay;
import com.zksr.trade.service.ITrdHdfkPayService;
import com.zksr.trade.service.ITrdHdfkSettleService;
import com.zksr.trade.service.ITrdSettleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 货到付款付款单Controller
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Api(tags = "管理后台 - 货到付款付款单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/hdfkPay")
public class TrdHdfkPayController {

    @Autowired
    private ITrdHdfkPayService trdHdfkPayService;

    @Autowired
    private ITrdHdfkSettleService trdSettleService;
    /**
     * 新增货到付款付款单
     */
    @ApiOperation(value = "新增货到付款付款单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "货到付款付款单", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody HdfkPaySaveReqVO createReqVO) {
        return success(trdHdfkPayService.insertTrdHdfkPay(createReqVO));
    }

    /*
    *//**
     * 修改货到付款付款单
     *//*
    @ApiOperation(value = "修改货到付款付款单", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "货到付款付款单", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdHdfkPaySaveReqVO updateReqVO) {
            trdHdfkPayService.updateTrdHdfkPay(updateReqVO);
        return success(true);
    }

    *//**
     * 删除货到付款付款单
     *//*
    @ApiOperation(value = "删除货到付款付款单", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "货到付款付款单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{hdfkPayIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] hdfkPayIds) {
        trdHdfkPayService.deleteTrdHdfkPayByHdfkPayIds(hdfkPayIds);
        return success(true);
    }

    */
    /**
     * 获取货到付款付款单详细信息
     */
    @ApiOperation(value = "获得货到付款付款单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{hdfkPayId}")
    public CommonResult<TrdHdfkPayRespVO> getInfo(@PathVariable("hdfkPayId") Long hdfkPayId) {
        TrdHdfkPay trdHdfkPay = trdHdfkPayService.getTrdHdfkPay(hdfkPayId);
        TrdHdfkPayRespVO payRespVO = TrdHdfkPayConvert.INSTANCE.convert(trdHdfkPay);
        payRespVO.setSkuItemRespVOS(trdSettleService.getTrdHdfkSettleSkuItemRespVOByHdfkId(trdHdfkPay.getHdfkPayId()));
        return success(payRespVO);
    }

    /**
     * 分页查询货到付款付款单
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得货到付款付款单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdHdfkPayRespVO>> getPage(@Valid TrdHdfkPayPageReqVO pageReqVO) {
        PageResult<TrdHdfkPay> pageResult = trdHdfkPayService.getTrdHdfkPayPage(pageReqVO);
        return success(TrdHdfkPayConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:hdfk-pay:add";
        /** 编辑 */
        public static final String EDIT = "trade:hdfk-pay:edit";
        /** 删除 */
        public static final String DELETE = "trade:hdfk-pay:remove";
        /** 列表 */
        public static final String LIST = "trade:hdfk-pay:list";
        /** 查询 */
        public static final String GET = "trade:hdfk-pay:query";
        /** 停用 */
        public static final String DISABLE = "trade:hdfk-pay:disable";
        /** 启用 */
        public static final String ENABLE = "trade:hdfk-pay:enable";
    }
}
