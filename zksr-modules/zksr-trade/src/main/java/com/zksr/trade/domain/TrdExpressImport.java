package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 快递导入记录对象 trd_express_import
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@TableName(value = "trd_express_import")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdExpressImport extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 快递导入记录id */
    @TableId(type= IdType.ASSIGN_ID)
    private Long expressImportId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 导入文件名 */
    @Excel(name = "导入文件名")
    private String fileName;

    /** 导入文件下载地址 */
    @Excel(name = "导入文件下载地址")
    private String fileUrl;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 导入总数 */
    @Excel(name = "导入总数")
    private Long totalNum;

    /** mq发送数量 */
    @Excel(name = "mq发送数量")
    private Long mqSendNum;

    /** mq接收数量 */
    @Excel(name = "mq接收数量")
    private Long mqReceiveNum;

    /** 成功条数 */
    @Excel(name = "成功条数")
    private Long successNum;

    /** 失败条数 */
    @Excel(name = "失败条数")
    private Long failNum;

    /** 是否更新已存在的数据;是否更新已存在的数据 */
    @Excel(name = "是否更新已存在的数据;是否更新已存在的数据")
    private Integer updateSupport;

}
