package com.zksr.trade.controller.orderExpressImport.vo;

import cn.hutool.db.DaoTemplate;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import java.util.Date;


/**
 * 快递导入记录对象 trd_express_import
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Data
@ApiModel("快递导入记录 - trd_express_import Response VO")
public class TrdExpressImportRespVO {
    private static final long serialVersionUID = 1L;

    /** 快递导入记录id */
    @ApiModelProperty(value = "ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long expressImportId;

    /** 导入文件名 */
    @Excel(name = "导入文件名")
    @ApiModelProperty(value = "导入文件名")
    private String fileName;

    /** 导入文件下载地址 */
    @Excel(name = "导入文件下载地址")
    @ApiModelProperty(value = "导入文件下载地址")
    private String fileUrl;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 导入总数 */
    @Excel(name = "导入总数")
    @ApiModelProperty(value = "导入总数")
    private Long totalNum;

    /** mq发送数量 */
    @Excel(name = "mq发送数量")
    @ApiModelProperty(value = "mq发送数量")
    private Long mqSendNum;

    /** mq接收数量 */
    @Excel(name = "mq接收数量")
    @ApiModelProperty(value = "mq接收数量")
    private Long mqReceiveNum;

    /** 成功条数 */
    @Excel(name = "成功条数")
    @ApiModelProperty(value = "成功条数")
    private Long successNum;

    /** 失败条数 */
    @Excel(name = "失败条数")
    @ApiModelProperty(value = "失败条数")
    private Long failNum;

    /** 是否更新已存在的数据;是否更新已存在的数据 */
    @Excel(name = "是否更新已存在的数据;是否更新已存在的数据")
    @ApiModelProperty(value = "是否更新已存在的数据;是否更新已存在的数据")
    private Integer updateSupport;

    @ApiModelProperty(value = "导入人员名称")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

}
