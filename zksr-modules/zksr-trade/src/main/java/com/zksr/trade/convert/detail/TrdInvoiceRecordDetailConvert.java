package com.zksr.trade.convert.detail;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdInvoiceRecordDetail;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceRecordDetailRespVO;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceRecordDetailSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 发票明细 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-07-19
*/
@Mapper
public interface TrdInvoiceRecordDetailConvert {

    TrdInvoiceRecordDetailConvert INSTANCE = Mappers.getMapper(TrdInvoiceRecordDetailConvert.class);

    TrdInvoiceRecordDetailRespVO convert(TrdInvoiceRecordDetail trdInvoiceRecordDetail);

    TrdInvoiceRecordDetail convert(TrdInvoiceRecordDetailSaveReqVO trdInvoiceRecordDetailSaveReq);

    PageResult<TrdInvoiceRecordDetailRespVO> convertPage(PageResult<TrdInvoiceRecordDetail> trdInvoiceRecordDetailPage);
}