package com.zksr.trade.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdDriver;
import com.zksr.trade.controller.driver.vo.TrdDriverPageReqVO;


/**
 * 司机档案Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@Mapper
public interface TrdDriverMapper extends BaseMapperX<TrdDriver> {
    default PageResult<TrdDriver> selectPage(TrdDriverPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdDriver>()
                    .eqIfPresent(TrdDriver::getDriverId, reqVO.getDriverId())
                    .eqIfPresent(TrdDriver::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdDriver::getSupplierId, reqVO.getSupplierId())
                    .likeIfPresent(TrdDriver::getDriverName, reqVO.getDriverName())
                    .eqIfPresent(TrdDriver::getDriverPhone, reqVO.getDriverPhone())
                .orderByDesc(TrdDriver::getDriverId));
    }

    //校验司机手机号唯一
    default TrdDriver checkDriverPhoneUnique(String driverPhone){
        return selectOne(new LambdaQueryWrapperX<TrdDriver>().eq(TrdDriver::getDriverPhone,driverPhone));
    }
}
