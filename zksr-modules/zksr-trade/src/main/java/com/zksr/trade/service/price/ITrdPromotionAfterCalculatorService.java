package com.zksr.trade.service.price;

import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年03月26日 10:40
 * @description: 订单售后营销（优惠劵、活动）返回逻辑计算的接口
 */
public interface ITrdPromotionAfterCalculatorService {
    /**
     * 门店正常价格计算
     */
//    int ORDER_MEMBER_LEVEL = 1;
    /**
     * 订单优惠劵价格计算
     */
    int ORDER_COUPON = 5;

    /**
     * 订单优惠劵价格计算
     */
    int ORDER_ACTIVITY = 6;

    void calculatePromotionAfter(TrdOrder trdOrder, List<TrdSupplierOrderDtl> trdSupplierOrderDtls, TrdAfter trdAfter, List<TrdSupplierAfterDtl> trdSupplierAfterDtls);
}
