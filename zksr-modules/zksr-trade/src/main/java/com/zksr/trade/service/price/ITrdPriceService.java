package com.zksr.trade.service.price;

import com.zksr.trade.api.after.vo.OrderAfterResp;
import com.zksr.trade.api.after.vo.OrderAfterSaveRequest;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdSupplierPageVO;
import com.zksr.trade.domain.TrdSupplierAfter;
import com.zksr.trade.domain.TrdSupplierAfterSettle;
import com.zksr.trade.domain.TrdSupplierOrderDtl;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月20日 18:21
 * @description: 价格计算接口
 */
public interface ITrdPriceService {
    /**
     * 售后订单价格计算
     * @param request
     */
    public OrderAfterResp calculateAfterOrderPrice(OrderAfterSaveRequest request);

    /**
     * 计算 售后订单结算数据
     * @return
     */
    public void calculateAfterRateAmt(TrdSupplierAfterSettle trdSupplierAfterSettle);

    /**
     * 重新计算订单数量、金额
     */
    public void recalculateOrderAmt(TrdOrder tor, List<TrdSupplierOrder> tsoList, List<TrdSupplierOrderDtl> tsodList);

    /**
     * 重新计算售后订单数量、金额
     * @param trdAfter
     */
    public void recalculateAfterAmt(TrdAfter trdAfter);

    /**
     * 计算 售后订单退款手续费用
     * @param trdAfter 售后主订单
     * @param trdSupplierAfters 售后入驻商订单列表
     * @param trdSupplierAfterSettles 售后订单结算列表
     * @param trdOrder 销售主订单
     * @param tsoList 销售入驻商订单
     */
    public void recalculateAfterPayFee(TrdAfter trdAfter, List<TrdSupplierAfter> trdSupplierAfters, List<TrdSupplierAfterSettle> trdSupplierAfterSettles, TrdOrder trdOrder, List<TrdSupplierOrder> tsoList);


    /**
     * 用于货到付款收款后退款计算手续费
     * 订单明细货到付款收款成功，重新计算订单结算 手续费信息
     * @param trdSupplierOrderDtlList
     */
    public void recalculateHdfkOrderReceiveSuccessPayFee(Long hdfkPayId, List<TrdSupplierOrderDtl> trdSupplierOrderDtlList);

    /**
     * 计算储值支付本金支付金额、储值赠金支付金额
     * @param after
     */
    public void calculatorAfterCzPayWayGiveAmt(TrdAfter after);


    /**
     * 储值支付计算 本金支付金额、储值赠金支付金额
     * @param tor
     */
    public void calculatorCzPayWayGiveAmt(TrdOrder tor, TrdSupplierPageVO pageVo);

    /**
     * 更新销售订单信息 （入驻商售后申请审核同意操作、售后自动审核操作、撤销售后审核操作、售后差异重新计算操作）
     * @param tor 订单数据
     * @param tsoList 入驻商订单数据
     * @param tar 售后订单数据
     * @param tsaList 入驻商售后订单数据
     * @param operateType + ： 标识增加操作，-：标识减少操作
     */
    public void updateOrderInfo(TrdOrder tor, List<TrdSupplierOrder> tsoList, TrdAfter tar, List<TrdSupplierAfter> tsaList, String operateType);
}
