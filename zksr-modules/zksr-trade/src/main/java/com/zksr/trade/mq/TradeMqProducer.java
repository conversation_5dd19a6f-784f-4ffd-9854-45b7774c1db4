package com.zksr.trade.mq;

import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.member.api.colonelApp.dto.PageDataReqDTO;
import com.zksr.member.api.command.vo.CommandAddOrderVO;
import com.zksr.trade.api.after.vo.PayRefundVO;
import com.zksr.trade.api.car.dto.AppCarEventDTO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdPayOrderPageVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlSaveReqVO;
import com.zksr.trade.service.ITrdCarService;
import com.zksr.trade.service.TrdCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.messaging.support.MessageBuilder;

import java.util.HashMap;
import java.util.Map;

import static com.zksr.member.constant.MemberConstant.ES_COLONEL_APP_BRANCH_TYPE_2;

/**
 * <AUTHOR>
 * @date 2024年04月16日 19:20
 * @description: trade模块 生产者
 */
@Configuration
@Slf4j
public class TradeMqProducer {
    @Autowired
    private StreamBridge streamBridge;

    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private ITrdCarService carService;

    /**
     * 发送入驻商一件代发订单
     * @param importDtl
     */
    public void sendSupplierOrderEvent(TrdExpressImportDtlSaveReqVO importDtl){
        log.info("入驻商一件代发订单事件消息：{} ", importDtl);
        streamBridge.send(
                MessageConstant.SUPPLIER_ORDER_EXPRESS_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(importDtl).build());
    }

    /**
     * 门店下单统计常购商品Es消息
     */
//    public void sendEsStoreProductEvent(Long orderId){
//        log.info("发送门店下单统计常购商品Es消息:{}",orderId);
//        streamBridge.send(
//                MessageConstant.STORE_PRODUCT_EXPRESS_TOPIC_OUT_PUT,
//                MessageBuilder.withPayload(orderId).build());
//    }
//
//    /**
//    * @Description: 门店下单打印机打印订单给入驻商(只需要orderId和sysCode)
//    * @Author: liuxingyu
//    * @Date: 2024/5/6 15:41
//    */
//    public void sendStorePrintOrderEvent(TrdOrderSaveReqVO orderVO){
//        log.info("门店下单打印机打印订单给入驻商,订单编号:{},平台编号:{}",orderVO.getOrderId(),orderVO.getSysCode());
//        streamBridge.send(
//                MessageConstant.STORE_PRINT_TOPIC_OUT_PUT,
//                MessageBuilder.withPayload(orderVO).build());
//    }

    /**
     * 业务员APP统计客户信息
     * 消费者 {@link com.zksr.member.mq.MemberAppMqConsumer#colonelAppBranchEvent()}
     * @param branchDTO
     */
    public void sendEsColonelAppBranchEvent(ColonelAppBranchDTO branchDTO){
        log.info("业务员APP统计客户信息类型:更新订单信息,门店编号:{},平台商编号{}",branchDTO.getBranchId(),branchDTO.getSysCode());
        branchDTO.setType(ES_COLONEL_APP_BRANCH_TYPE_2);
        streamBridge.send(
                MessageConstant.COLONEL_APP_BRANCH_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(branchDTO).build());

    }

    /**
     * 业务员APP统计首页信息
     * @param reqDTO
     */
    public void sendColoenlAppPageDataEvent(PageDataReqDTO reqDTO){
        log.info("业务员APP首页信息发送消息,业务员编号:{},平台商编号{},类型{}",reqDTO.getColonelId(),reqDTO.getSysCode(),reqDTO.getType());
        Map<String, Object> headers = new HashMap<>();
        headers.put(MessageConst.PROPERTY_DELAY_TIME_LEVEL, 2);
        Message<Object> msg = new GenericMessage<>(reqDTO, headers);
        streamBridge.send(
                MessageConstant.COLONEL_APP_PAGE_DATA_TOPIC_OUT_PUT,
                msg);
    }

    public void sendSyncDataAfterEvent(String afterNo){
        log.info("推送售后订单信息同步数据:订单信息{}", afterNo);
        streamBridge.send(
                MessageConstant.SYNC_DATA_AFTER_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(afterNo).build());
    }


    /**
     * 发送货到付款订单成功消息
     * @param data
     */
    public void sendOrderHdfkSuccessEvent(TrdPayOrderPageVO  data){
        log.info("发送货到付款订单支付成功消息，订单信息{}", data);
        Map<String, Object> headers = new HashMap<>();
        headers.put(MessageConst.PROPERTY_DELAY_TIME_LEVEL, 2);
        Message<Object> msg = new GenericMessage<>(data, headers);
        streamBridge.send(
                MessageConstant.ORDER_HDFK_SUCCESS_TOPIC_OUT_PUT,
                msg);
    }

    /**
     *  发送ERP收款单据信息
     * @param data
     */
    public void sendSyncDataReceiptEvent(SyncReceiptSendDTO data){
        log.info("发送B2B收款或退款信息推送到ERP系统消息，内容：{}", data);
        Map<String, Object> headers = new HashMap<>();
        headers.put(MessageConst.PROPERTY_DELAY_TIME_LEVEL, 2);
        Message<Object> msg = new GenericMessage<>(data, headers);
        streamBridge.send(
                MessageConstant.SYNC_DATA_RECEIPT_TOPIC_OUT_PUT,
                msg);
    }

    /**
     * 发送售后订单审核消息
     * @param afterId
     */
    public void sendAfterApproveEvent(Long  afterId){
        log.info("发送售后订单审核消息，售后订单Id信息{}", afterId);
        Map<String, Object> headers = new HashMap<>();
        headers.put(MessageConst.PROPERTY_DELAY_TIME_LEVEL, 2); // 延迟5秒发送
        Message<Object> msg = new GenericMessage<>(afterId, headers);
        streamBridge.send(
                MessageConstant.AFTER_APPROVE_TOPIC_OUT_PUT,
                msg);
    }

    /**
     * 发送订单更新成功处理消息（用于处理订单支付完成后的业务逻辑）
     * @param data
     */
    public void sendOrderPayUpdateSuccessEvent(TrdOrder data){
        log.info("发送订单支付成功处理消息（用于处理订单支付完成后的业务逻辑），订单信息{}", data);
        streamBridge.send(
                MessageConstant.ORDER_PAY_UPDATE_SUCCESS_TOPIC_OUT_PUT,
                data);
    }


    /**
     * 发送商品购物车变动
     * 消费者 {@link TradeMqConsumer#productCarEvent()}
     * @param carId
     */
    public void sendProductCarEvent(AppCarEventDTO carEvent){
        log.info("发送购物车事件消息：{} ", carEvent);
        boolean flag = streamBridge.send(
                MessageConstant.PRODUCT_CAR_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(carEvent).build());
    }

    /**
     * 入驻商销售订单手动推送消息入口
     * @param syncDataDTO
     */
    public void sendSyncSupplierOrderEvent(SyncDataDTO syncDataDTO){
        log.info("推送同步数据:{}",syncDataDTO.toString());
        Map<String, Object> headers = new HashMap<>();
        headers.put("for", "这是一个请求头～");
        headers.put(MessageConst.PROPERTY_DELAY_TIME_LEVEL, syncDataDTO.getPropertyDelayTimeLevel());
        Message<SyncDataDTO> msg = new GenericMessage(syncDataDTO, headers);
        streamBridge.send(
                MessageConstant.SUPPLIER_SYNC_DATA_ORDER_TOPIC_OUT_PUT,
                msg);
    }

    /**
     * 同意退款之后，货到付款无需付款的操作
     * @param refundVO
     */
    public void sendRefundNotification(PayRefundVO refundVO) {
        log.info("用于处理货到付款无需付款,售后订单退款成功回调{}", refundVO);
        streamBridge.send(
                MessageConstant.AFTER_ORDER_UPDATE_SUCCESS_TOPIC_OUT_PUT,
                refundVO);
    }

    /**
     *  发送加单指令消息
     * @param reqVo
     */
    public void sendAddOrderCommand(CommandAddOrderVO reqVo) {
        log.info("发送加单指令消息：{} ", reqVo);
        streamBridge.send(
                MessageConstant.COMMAND_ADD_ORDER_TOPIC_OUT_PUT,
                reqVo);
    }
}
