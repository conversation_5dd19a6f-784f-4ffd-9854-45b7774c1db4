package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 货到付款付款单对象 trd_hdfk_pay
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@TableName(value = "trd_hdfk_pay")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdHdfkPay extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 货到付款付款单id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long hdfkPayId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 付款单号 */
    @Excel(name = "付款单号")
    private String hdfkPayNo;

    /** 门店id */
    @Excel(name = "门店id")
    private Long branchId;

    /** 支付状态（数据字典sys_pay_state）；0-未支付 1-已支付 */
    @Excel(name = "支付状态", readConverterExp = "数=据字典sys_pay_state")
    private Integer payState;

    /** 支付方式(数据字典sys_hdfk_pay_way));0-在线支付 */
    @Excel(name = "支付方式(数据字典sys_hdfk_pay_way));0-在线支付")
    private String payWay;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    /** 支付金额；实际支付金额 */
    @Excel(name = "支付金额；实际支付金额")
    private BigDecimal payAmt;

    /** 订单金额；未减去优惠的订单金额 */
    @Excel(name = "订单金额；未减去优惠的订单金额")
    private BigDecimal orderAmt;

    /** 支付公司收取的支付费率 */
    @Excel(name = "支付公司收取的支付费率")
    private BigDecimal payRate;

    /** 支付平台手续费；(pay_amt*pay_rate) 四舍五入 */
    @Excel(name = "支付平台手续费；(pay_amt*pay_rate) 四舍五入")
    private BigDecimal payFee;

    /** 支付平台(数据字典); */
    @Excel(name = "支付平台(数据字典);")
    private String platform;

    /** 付款单来源(数据字典), 0-app, 1-后台 */
    @Excel(name = "付款单来源(数据字典), 0-app, 1-后台")
    private Integer paySource;

    @ApiModelProperty("备注")
    private String tips;

    @ApiModelProperty("凭证")
    private String voucher;
}
