package com.zksr.trade.controller.driver.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 司机档案对象 trd_driver
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@ApiModel("司机档案 - trd_driver分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdDriverPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 司机id */
    @ApiModelProperty(value = "司机手机号")
    private Long driverId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id", required = true)
    private Long supplierId;

    /** 司机名 */
    @Excel(name = "司机名")
    @ApiModelProperty(value = "司机名")
    private String driverName;

    /** 司机手机号 */
    @Excel(name = "司机手机号")
    @ApiModelProperty(value = "司机手机号")
    private String driverPhone;


}
