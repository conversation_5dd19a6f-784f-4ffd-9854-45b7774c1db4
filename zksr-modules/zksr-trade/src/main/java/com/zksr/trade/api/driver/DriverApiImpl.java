package com.zksr.trade.api.driver;

import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.driver.dto.DriverDTO;
import com.zksr.trade.api.driver.dto.DriverRatingDTO;
import com.zksr.trade.api.driver.excel.TrdDriverImportExcel;
import com.zksr.trade.api.driver.form.TrdDriverImportForm;
import com.zksr.trade.api.driver.vo.DriverRatingReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderSaveReqVO;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingSaveReqVO;
import com.zksr.trade.convert.driver.TrdDriverConvert;
import com.zksr.trade.convert.rating.TrdDriverRatingConvert;
import com.zksr.trade.domain.TrdDriver;
import com.zksr.trade.service.ITrdDriverRatingService;
import com.zksr.trade.service.ITrdDriverService;
import com.zksr.trade.service.ITrdSupplierOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @Date 2024/12/11 15:40
 * 司机相关rpc接口实现
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class DriverApiImpl implements DriverApi {

    @Autowired
    private ITrdDriverRatingService trdDriverRatingService;

    @Autowired
    private ITrdDriverService trdDriverService;

    @Autowired
    private ITrdSupplierOrderService trdSupplierOrderService;

    @Override
    public CommonResult<Boolean> saveDriverRating(DriverRatingReqVO DriverRatingReqVO) {
        TrdDriverRatingSaveReqVO trdDriverRatingSaveReqVO = TrdDriverRatingConvert.INSTANCE.convertSaveReqVO(DriverRatingReqVO);
        Long driverRatingId =trdDriverRatingService.insertTrdDriverRating(trdDriverRatingSaveReqVO);
        if (driverRatingId != null) {
            //补充订单评价状态和评价ID
            TrdSupplierOrder trdSupplierOrder =trdSupplierOrderService.getTrdSupplierOrder(DriverRatingReqVO.getSupplierOrderId());
            trdSupplierOrder.setDriverId(DriverRatingReqVO.getDriverId());
            trdSupplierOrder.setDriverRatingFlag(1);
            trdSupplierOrder.setDriverRatingId(driverRatingId);
            trdSupplierOrderService.updateTrdSupplierOrder(HutoolBeanUtils.toBean(trdSupplierOrder, TrdSupplierOrderSaveReqVO.class));
            return CommonResult.success(true);
        }
        return CommonResult.error(500,"司机评分保存失败");
    }

    @Override
    public CommonResult<DriverDTO> getDriverInfo(Long driverId) {
        TrdDriver trdDriver =trdDriverService.getTrdDriver(driverId);
        if(ToolUtil.isEmpty(trdDriver)){
            return CommonResult.success(new DriverDTO());
        }
        return CommonResult.success(HutoolBeanUtils.toBean(trdDriver, DriverDTO.class));
    }

    @Override
    public CommonResult<DriverRatingDTO> getDriverRatingInfo(Long driverRatingId) {
        return CommonResult.success(HutoolBeanUtils.toBean(trdDriverRatingService.getTrdDriverRating(driverRatingId), DriverRatingDTO.class));
    }

    public CommonResult<String> importDataEvent(TrdDriverImportForm form){
        return CommonResult.success(JsonUtils.toJsonString(trdDriverService.impordDataEvent(form.getList(), form.getDcId(), form.getSysCode(), form.getFileImportId(), form.getSeq())));
    }
}
