package com.zksr.trade.controller.after;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.controller.orderSupplier.TrdSupplierOrderController;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderSaveReqVO;
import com.zksr.trade.domain.TrdSupplierAfter;
import com.zksr.trade.service.ITrdSupplierAfterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.after.supplierVo.TrdSupplierAfterPageReqVO;
import com.zksr.trade.controller.after.supplierVo.TrdSupplierAfterSaveReqVO;
import com.zksr.trade.controller.after.supplierVo.TrdSupplierAfterRespVO;
import com.zksr.trade.convert.after.TrdSupplierAfterConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 入驻商售后单Controller
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Api(tags = "管理后台 - 入驻商售后单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/afterSupplier")
public class TrdSupplierAfterController {
    @Autowired
    private ITrdSupplierAfterService trdSupplierAfterService;

    /**
     * 新增入驻商售后单
     */
    @ApiOperation(value = "新增入驻商售后单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "入驻商售后单", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdSupplierAfterSaveReqVO createReqVO) {
        return success(trdSupplierAfterService.insertTrdSupplierAfter(createReqVO));
    }

    /**
     * 修改入驻商售后单
     */
    @ApiOperation(value = "修改入驻商售后单", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "入驻商售后单", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdSupplierAfterSaveReqVO updateReqVO) {
            trdSupplierAfterService.updateTrdSupplierAfter(updateReqVO);
        return success(true);
    }

    /**
     * 删除入驻商售后单
     */
    @ApiOperation(value = "删除入驻商售后单", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "入驻商售后单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{supplierAfterIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] supplierAfterIds) {
        trdSupplierAfterService.deleteTrdSupplierAfterBySupplierAfterIds(supplierAfterIds);
        return success(true);
    }

    /**
     * 获取入驻商售后单详细信息
     */
    @ApiOperation(value = "获得入驻商售后单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{supplierAfterId}")
    public CommonResult<TrdSupplierAfterRespVO> getInfo(@PathVariable("supplierAfterId") Long supplierAfterId) {
        TrdSupplierAfter trdSupplierAfter = trdSupplierAfterService.getTrdSupplierAfter(supplierAfterId);
        return success(TrdSupplierAfterConvert.INSTANCE.convert(trdSupplierAfter));
    }

    /**
     * 分页查询入驻商售后单
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得入驻商售后单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdSupplierAfterRespVO>> getPage(@Valid TrdSupplierAfterPageReqVO pageReqVO) {
        PageResult<TrdSupplierAfter> pageResult = trdSupplierAfterService.getTrdSupplierAfterPage(pageReqVO);
        return success(TrdSupplierAfterConvert.INSTANCE.convertPage(pageResult));
    }
    

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:after:add";
        /** 编辑 */
        public static final String EDIT = "trade:after:edit";
        /** 删除 */
        public static final String DELETE = "trade:after:remove";
        /** 列表 */
        public static final String LIST = "trade:after:list";
        /** 查询 */
        public static final String GET = "trade:after:query";
        /** 停用 */
        public static final String DISABLE = "trade:after:disable";
        /** 启用 */
        public static final String ENABLE = "trade:after:enable";
    }
}
