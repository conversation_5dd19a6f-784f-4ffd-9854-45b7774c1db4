package com.zksr.trade.service.impl.handler.payWay;

import com.zksr.account.api.transfer.TransferApi;
import com.zksr.account.api.transfer.dto.TransferSaveDTO;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.service.RedisService;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.dto.OrderPayInfoRespDTO;
import com.zksr.trade.api.order.dto.TrdSupplierResDto;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdSupplierPageVO;
import com.zksr.trade.domain.TrdSettle;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.mapper.TrdSettleMapper;
import com.zksr.trade.service.handler.payWay.ITrdOrderPayWayHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 通用支付方式处理实现
 */
@Service
@Slf4j
@Order(ITrdOrderPayWayHandlerService.ORDER_NONE_ONLINE)
public class TrdOrderPayWayHandlerServiceimpl implements ITrdOrderPayWayHandlerService {

    @Autowired
    private RedisService redisService;
    @Autowired
    private TrdSettleMapper trdSettleMapper;
    @Autowired
    private TransferApi transferApi;

    @Override
    public Boolean isPlatform(String platform, String payWay) {
        return Objects.equals(PayChannelEnum.NONE.getCode(), platform) && Objects.equals("100", payWay);
    }

    @Override
    public Boolean isPlatform(String platform) {
        return Objects.equals(PayChannelEnum.NONE.getCode(), platform);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderCompleteCreateSettleTransfer(TrdOrder tor, List<TrdSupplierOrder> tsoList, List<TrdSupplierOrderDtl> tsodList, List<TrdSettle> settleList) {
//        settleList.forEach(settle -> {
//            TransferSaveDTO transferSaveDTO = new TransferSaveDTO();
//            transferSaveDTO.setSettleId(settle.getSettleId());
//            transferSaveDTO.setTransferNo(SheetTypeConstants.ZZ + redisService.getUniqueNumber());
//            transferSaveDTO.setPlatform(settle.getPlatform());
//
//            if (settle.getSettleAmt().compareTo(BigDecimal.ZERO) < 0) { //当结算金额为负数是说明是售后
//                transferSaveDTO.setSourceMerchantId(settle.getMerchantId());
//                transferSaveDTO.setSourceMerchantType(settle.getMerchantType());
//                transferSaveDTO.setTargetMerchantId(settle.getSupplierId());
//                transferSaveDTO.setTargetMerchantType(MerchantTypeEnum.SUPPLIER.getType());
//                transferSaveDTO.setTransferAmt(settle.getSettleAmt().negate());
//            }
//            if (settle.getSettleAmt().compareTo(BigDecimal.ZERO) > 0) {
//                transferSaveDTO.setSourceMerchantId(settle.getSupplierId());
//                transferSaveDTO.setSourceMerchantType(MerchantTypeEnum.SUPPLIER.getType());
//                transferSaveDTO.setTargetMerchantId(settle.getMerchantId());
//                transferSaveDTO.setTargetMerchantType(settle.getMerchantType());
//                transferSaveDTO.setTransferAmt(settle.getSettleAmt());
//
//                // 根据订单明细ID + 账户类型 + 账户ID 查询对应的结算数据（包括订单和售后）
//                List<TrdSettle> list = trdSettleMapper.getSettleBySupplierOrderDtlIdAndMerchantId(settle.getSupplierOrderDtlId(), settle.getMerchantId(), settle.getMerchantType());
//                // 结算数据汇总得到解冻金额
//                BigDecimal settleAmt = list.stream().map(TrdSettle::getSettleAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
//                transferSaveDTO.setSettleAmt(settleAmt);
//            }
//            settle.setState(StatusConstants.SETTLE_STATE_2);
//            if (settle.getSettleAmt().compareTo(BigDecimal.ZERO) == 0) { // 当结算金额为0时，不执行转账操作，直接更改状态为结算
//                settle.setState(StatusConstants.SETTLE_STATE_1);
//                settle.setSettleTime(DateUtils.getNowDate());
//            }
//            trdSettleMapper.updateById(settle);
//            // 当前状态为待结算或结算中才能生成转账单
//            if (Objects.equals(settle.getState(), StatusConstants.SETTLE_STATE_2)
//                    || Objects.equals(settle.getState(), StatusConstants.SETTLE_STATE_0)) {
//                log.info("订单{}转账分润！", transferSaveDTO);
//                transferApi.createTransfer(transferSaveDTO);
//            }
//        });
    }


    @Override
    public void orderSettleAccountInfo(List<TrdSupplierResDto> payInfoList, Map<Long, List<TrdSupplierOrderDtl>> supplierItemMap,
                                                          TrdSupplierPageVO pageVo, List<TrdSettle> settles, OrderPayInfoRespDTO respDTO) {
        payInfoList.stream().collect(Collectors.toMap(TrdSupplierResDto::getSupplierOrderId, item -> item)).forEach((supplierOrderId, supplierResDTO) -> {
            if (supplierItemMap.containsKey(supplierOrderId)) {
                // 入驻商分账方信息
                List<String> itemList = supplierItemMap.get(supplierOrderId).stream()
                        .map(item ->
                                StringUtils.format("{}", item.getSpuName())
                        ).collect(Collectors.toList());
                supplierResDTO.setItemInfo(StringUtils.join(itemList, StringPool.COMMA))
                        .setMerchantId(supplierResDTO.getSupplierId())
                        .setMerchantType(MerchantTypeEnum.SUPPLIER.getType());
                respDTO.getSupplierList().add(supplierResDTO);
            }
        });
    }




}
