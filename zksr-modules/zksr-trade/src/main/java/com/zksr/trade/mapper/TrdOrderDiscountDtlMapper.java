package com.zksr.trade.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.trade.domain.TrdOrderDiscountDtl;

import java.util.List;


/**
 * 订单优惠明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Mapper
public interface TrdOrderDiscountDtlMapper extends BaseMapperX<TrdOrderDiscountDtl> {
    /**
     * 根据主单ID 获取入驻商订单优惠明细信息
     * @param orderId 主单ID
     * @param discountType 优惠类型 {@link com.zksr.common.core.enums.TrdDiscountTypeEnum}
     * @return
     */
    default List<TrdOrderDiscountDtl> selectList(Long orderId, String discountType) {
        return selectList(new LambdaQueryWrapperX<TrdOrderDiscountDtl>()
                .eqIfPresent(TrdOrderDiscountDtl::getOrderId, orderId)
                .eqIfPresent(TrdOrderDiscountDtl::getDiscountType, discountType));
    }

    /**
     * 根据入驻商订单ID 获取入驻商订单赠送优惠券明细信息
     * @param supplierOrderId 入驻商订单ID
     * @return
     */
    default List<TrdOrderDiscountDtl> selectListBySupplierId(Long supplierOrderId) {
        return selectList(new LambdaQueryWrapperX<TrdOrderDiscountDtl>()
                .eqIfPresent(TrdOrderDiscountDtl::getSupplierOrderId, supplierOrderId)
                .eqIfPresent(TrdOrderDiscountDtl::getGiftType, NumberPool.INT_ONE));
    }

    /**
     * 根据入驻商订单ID 获取入驻商订单赠送优惠券明细信息
     * @param supplierOrderId 入驻商订单ID
     * @return
     */
    default List<TrdOrderDiscountDtl> selectGiftListBySupplierId(Long supplierOrderId) {
        return selectList(new LambdaQueryWrapperX<TrdOrderDiscountDtl>()
                .eqIfPresent(TrdOrderDiscountDtl::getSupplierOrderId, supplierOrderId));
    }


    /**
     * 根据订单ID 获取订单明细优惠详情
     * @param orderId 入驻商订单ID
     * @return
     */
    default List<TrdOrderDiscountDtl> selectListByOrderId(Long orderId) {
        return selectList(new LambdaQueryWrapperX<TrdOrderDiscountDtl>()
                .eqIfPresent(TrdOrderDiscountDtl::getOrderId, orderId));
    }

    /**
     * 根据入驻商订单ID 获取入驻商订单优惠明细信息
     * @param supplierOrderId 入驻商订单ID
     * @return
     */
    default List<TrdOrderDiscountDtl> getListBySupplierOrderId(Long supplierOrderId) {
        return selectList(new LambdaQueryWrapperX<TrdOrderDiscountDtl>()
                .eqIfPresent(TrdOrderDiscountDtl::getSupplierOrderId, supplierOrderId));
    }
}
