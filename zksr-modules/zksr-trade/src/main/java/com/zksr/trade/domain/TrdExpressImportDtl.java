package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 快递导入明细对象 trd_express_import_dtl
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@TableName(value = "trd_express_import_dtl")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdExpressImportDtl extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 快递导入明细id */
    @TableId(type= IdType.ASSIGN_ID)
    private Long expressImportDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 状态0成功 1失败 */
    @Excel(name = "状态0成功 1失败")
    private Integer status;

    /** 订单id;订单id */
    @Excel(name = "订单id;订单id")
    private Long orderId;

    /** 订单编号;订单编号 */
    @Excel(name = "订单编号;订单编号")
    private String orderNo;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 快递单号 */
    @Excel(name = "快递单号")
    private String expressNo;

    /** 快递公司 */
    @Excel(name = "快递公司")
    private String expressCom;

    /** 快递公司编码 */
    @Excel(name = "快递公司编码")
    private String expressComNo;

    /** 失败原因 */
    @Excel(name = "失败原因")
    private String failReason;

    /** 快递导入记录id */
    @Excel(name = "快递导入记录id")
    private Long expressImportId;

}
