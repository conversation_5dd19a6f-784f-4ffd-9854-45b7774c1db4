package com.zksr.trade.service.impl.price;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.domain.TrdAfterDiscountDtl;
import com.zksr.trade.domain.TrdOrderDiscountDtl;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.mapper.TrdAfterDiscountDtlMapper;
import com.zksr.trade.mapper.TrdOrderDiscountDtlMapper;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import com.zksr.trade.service.price.ITrdPromotionAfterCalculatorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;

/**
 * <AUTHOR>
 * @date 2024年06月06日 17:47
 * @description: 发货前售后 返还活动库存数据
 */
@Service
@Order(ITrdPromotionAfterCalculatorService.ORDER_ACTIVITY)
@Slf4j
public class TrdActivityPromotionAfterCalculatorServiceImpl implements ITrdPromotionAfterCalculatorService{
    @Autowired
    private TrdOrderDiscountDtlMapper trdOrderDiscountDtlMapper;
    @Autowired
    private TrdAfterDiscountDtlMapper trdAfterDiscountDtlMapper;
    @Autowired
    private List<ITrdOrderHandlerService> trdOrderHandlerServices;

    @Override
    public void calculatePromotionAfter(TrdOrder trdOrder, List<TrdSupplierOrderDtl> trdSupplierOrderDtls, TrdAfter trdAfter, List<TrdSupplierAfterDtl> trdSupplierAfterDtls) {
// 空值检查
        if (trdOrder == null || trdSupplierOrderDtls == null || trdAfter == null || trdSupplierAfterDtls == null) {
            return;
        }

        // 构建供应商售后详情映射
        Map<Long, TrdSupplierAfterDtl> supplierAfterDtlMap = convertMap(trdSupplierAfterDtls, TrdSupplierAfterDtl::getSupplierOrderDtlId);

        // 过滤出需要处理的供应商订单详情
        List<TrdSupplierOrderDtl> supplierOrderDtlList = trdSupplierOrderDtls.stream()
                .filter(orderDtl -> supplierAfterDtlMap.containsKey(orderDtl.getSupplierOrderDtlId()))
                .collect(Collectors.toList());

        if (ToolUtil.isEmpty(supplierOrderDtlList)) {
            return;
        }

        // 获取本次售后订单活动匹配的销售订单的促销数据
        if (!supplierOrderDtlList.isEmpty()) {
            Long supplierOrderId = supplierOrderDtlList.get(0).getSupplierOrderId();
            List<TrdOrderDiscountDtl> orderDiscountDtlList = trdOrderDiscountDtlMapper.selectGiftListBySupplierId(supplierOrderId);

            // 获取本次售后订单促销数据
            List<TrdAfterDiscountDtl> discountDtlList = trdAfterDiscountDtlMapper.selectListByAfterId(trdAfter.getAfterId());

            if (ToolUtil.isEmpty(discountDtlList) && ToolUtil.isEmpty(orderDiscountDtlList)) {
                return;
            }

            // 构建售后详情映射
            Map<Long, TrdSupplierAfterDtl> afterDtlMap = convertMap(trdSupplierAfterDtls, TrdSupplierAfterDtl::getSupplierAfterDtlId);

            // 参与活动数量返还
            trdOrderHandlerServices.forEach(handler -> handler.returnAfterActivitySaleQty(discountDtlList, trdAfter, afterDtlMap, orderDiscountDtlList));
        }
    }
}
