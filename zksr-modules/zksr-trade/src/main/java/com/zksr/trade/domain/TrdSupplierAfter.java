package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 入驻商售后单对象 trd_supplier_after
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@TableName(value = "trd_supplier_after")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdSupplierAfter extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 入驻商订单id */
    @TableId
    private Long supplierAfterId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 入驻商售后单编号 */
    @Excel(name = "入驻商售后单编号")
    private String supplierAfterNo;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    private String supplierOrderNo;

    /** 订单id */
    @Excel(name = "订单id")
    private Long orderId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 售后单id */
    @Excel(name = "售后单id")
    private Long afterId;

    /** 售后单编号 */
    @Excel(name = "售后单编号")
    private String afterNo;

    /** 配送费 */
    @Excel(name = "配送费")
    private BigDecimal transAmt;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    private BigDecimal subDiscountAmt;

    /** 退款金额;实际退款金额 */
    @Excel(name = "退款金额;实际退款金额")
    private BigDecimal subRefundAmt;

    /** 入驻商订单本次售后退款金额（不包括优惠） */
    @Excel(name = "入驻商订单本次售后退款金额（不包括优惠）")
    private BigDecimal returnSubOrderAmt;


    /** 支付公司收取的支付费率;从订单 */
    @Excel(name = "支付公司收取的支付费率;从订单")
    private BigDecimal payRate;

    /** 支付平台手续费;(refund_amt*pay_rate) 四舍五入 */
    @Excel(name = "支付平台手续费;(sub_refund_amt*pay_rate) 四舍五入")
    private BigDecimal subRefundFee;

    /** 售后收货手机号 */
    @Excel(name = "售后收货手机号")
    private String returnPhone;

    /** 售后收货地址 */
    @Excel(name = "售后收货地址")
    private String returnAddr;

    /** 外部订单号 */
    @Excel(name = "外部订单号")
    private String sourceOrderNo;

    /** 订单类型 */
    @Excel(name = "订单类型 SheetTypeConstants")
    private String transNo;

    /** 推送状态 0未推送 1已推送 2已接收*/
    @Excel(name = "推送状态")
    private Integer pushStatus;

}
