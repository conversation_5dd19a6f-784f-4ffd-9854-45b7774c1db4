package com.zksr.trade.print;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.partnerPolicy.dto.XpYunSettingPolicyDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.enums.PayType;
import com.zksr.system.enums.UnitType;
import com.zksr.trade.print.vo.PrintSupplierOrderDtlVO;
import com.zksr.trade.print.vo.PrintSupplierOrderVO;
import com.zksr.trade.service.ITrdOrderService;
import com.zksr.trade.service.TrdCacheService;
import lombok.extern.slf4j.Slf4j;
import net.xpyun.platform.opensdk.service.PrintService;
import net.xpyun.platform.opensdk.vo.ObjectRestResponse;
import net.xpyun.platform.opensdk.vo.PrintRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.util.List;

/**
 * @Description: 打印service
 * @Author: liuxingyu
 * @Date: 2024/4/19 15:11
 */
@Component
@Slf4j
public class XpYunBean {

    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private ITrdOrderService trdOrderService;

    private static final PrintService service = new PrintService();

    /**
     * @Description: 打印订单
     * @Author: liuxingyu
     * @Date: 2024/4/19 15:21
     */
    public void PrintOrder(Long orderId, Long sysCode, Long supplierOrderId) {
        //通过订单id获取订单信息
        List<PrintSupplierOrderVO> printSupplierOrderVO = trdOrderService.printGetByOrderId(orderId, sysCode, supplierOrderId);
        if (ObjectUtil.isEmpty(printSupplierOrderVO)) {
            log.error("打印订单异常:未获取到订单信息,订单id{}", orderId);
            return;
        }
        log.info("进入芯烨打印====================================================");
        //打印每个入驻商
        for (PrintSupplierOrderVO supplierOrderVO : printSupplierOrderVO) {
            //获取入驻商配置信息
            XpYunSettingPolicyDTO xpYunSettingPolicyDTO = trdCacheService.getXpYunSettingPolicyDTO(supplierOrderVO.getSupplierId());
            if (ObjectUtil.isNull(xpYunSettingPolicyDTO)) {
                log.error("芯烨打印机未获取到配置,入驻商ID为{}", supplierOrderVO.getSupplierId());
                return;
            }
            //入驻商信息
            SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(supplierOrderVO.getSupplierId());
            String supplierName = ObjectUtil.isNotNull(supplierDTO) ? supplierDTO.getSupplierName() : "";
            //业务员信息
            ColonelDTO colonelDTO = ObjectUtil.isNull(supplierOrderVO.getColonelId()) ? null : trdCacheService.getColonelDTO(supplierOrderVO.getColonelId());
            String coloneName = ObjectUtil.isNotNull(colonelDTO) ? colonelDTO.getColonelName() : "";
            //门店信息
            BranchDTO branchDTO = trdCacheService.getBranchDTO(supplierOrderVO.getBranchId());
            String branchName = "";
            String contactName = "";
            String contactPhone = "";
            String branchAddr = "";
            if (ObjectUtil.isNotNull(branchDTO)) {
                branchName = branchDTO.getBranchName();
                contactName = branchDTO.getContactName();
                contactPhone = branchDTO.getContactPhone();
                branchAddr = branchDTO.getBranchAddr();
            }
            StringBuilder stringBuilder = new StringBuilder();
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<C><QR140>").append(supplierOrderVO.getOrderNo()).append("</QR></C>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<BR><BR><C><B><BOLD>配送单</BOLD></B></C><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L>站 点：").append(supplierName).append("<BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L>业务员：").append(coloneName).append("<BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L>单 号：").append(supplierOrderVO.getOrderNo()).append("<BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L>订单日期：").append(DateUtil.format(supplierOrderVO.getCreateTime(), "yyyy-MM-dd HH:mm:ss")).append("<BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L>店 名：<BOLD>").append(branchName).append("</BOLD><BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L><N>联 系 人：").append(contactName).append(" </N><BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L><N>联系电话：").append(contactPhone).append(" </N><BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L>客户地址：").append(branchAddr).append("<BR></L>");

            //商品信息
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<C><N>————————————————————————</N><BR></C>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<N> 序列      条码                品名</N><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<N> 数量      原价      现价      合计      备注</N><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<C><N>-----------------------------------------------</N><BR></C>");
            List<PrintSupplierOrderDtlVO> supplierOrderDtlVOList = supplierOrderVO.getSupplierOrderDtlVOList();
            int i = 0;
            for (PrintSupplierOrderDtlVO orderDtl : supplierOrderDtlVOList) {
                if (ObjectUtil.isNull(orderDtl)) {
                    continue;
                }
                ++i;
                //SKU 对象
                SkuDTO skuDTO = trdCacheService.getSkuDTO(orderDtl.getSkuId());
                String barcode = ObjectUtil.isNotNull(skuDTO) ? skuDTO.getBarcode() : "";
                Long unit = ObjectUtil.isNotNull(skuDTO) ? skuDTO.getUnit() : null;
                //Spu对象
                SpuDTO spuDTO = trdCacheService.getSpuDTO(orderDtl.getSpuId());
                String supName = ObjectUtil.isNotNull(spuDTO) ? spuDTO.getSpuName() : "";

                //填充序号/条码/品名
                appendContent(stringBuilder, xpYunSettingPolicyDTO, "<N>【").append(i).append("】    ");
                appendContent(stringBuilder, xpYunSettingPolicyDTO, XpYunUtil.refillQuantity(barcode, 16));
                appendContent(stringBuilder, xpYunSettingPolicyDTO, " ");
                appendContent(stringBuilder, xpYunSettingPolicyDTO, XpYunUtil.itemNameSubString("", supName));
                appendContent(stringBuilder, xpYunSettingPolicyDTO, "</N><BR>");
                //填充数量/原价/现价/合计/备注
                appendContent(stringBuilder, xpYunSettingPolicyDTO, "<N> <BOLD>");
                appendContent(stringBuilder, xpYunSettingPolicyDTO, XpYunUtil.refillQuantity(orderDtl.getTotalNum() + UnitType.matchingLabel(unit), 7));
                appendContent(stringBuilder, xpYunSettingPolicyDTO, "</BOLD>");
                appendContent(stringBuilder, xpYunSettingPolicyDTO, XpYunUtil.refillQuantity(orderDtl.getSalePrice().setScale(2, RoundingMode.HALF_UP).toPlainString(), 10));
                appendContent(stringBuilder, xpYunSettingPolicyDTO, "<BOLD>");
                appendContent(stringBuilder, xpYunSettingPolicyDTO, XpYunUtil.refillQuantity(orderDtl.getPrice().setScale(2, RoundingMode.HALF_UP).toPlainString() + "/"
                        + UnitType.matchingLabel(unit), 10));
                appendContent(stringBuilder, xpYunSettingPolicyDTO, "</BOLD>");
                appendContent(stringBuilder, xpYunSettingPolicyDTO, XpYunUtil.refillQuantity(orderDtl.getTotalAmt().setScale(2, RoundingMode.HALF_UP).toPlainString(), 10));
                appendContent(stringBuilder, xpYunSettingPolicyDTO, StringUtils.isBlank(orderDtl.getMemo()) ? "空" : orderDtl.getMemo()).append("</N><BR><BR>");
                appendContent(stringBuilder, xpYunSettingPolicyDTO, "<C><N>————————————————————————</N><BR></C>");
            }
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<N><L>支付方式：" + PayType.matchingLabel(supplierOrderVO.getPayWay()) + "<L></N><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<B><L>应收金额:" + (supplierOrderVO.getSubOrderAmt()) + "<L></B><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<B><L>拒收金额:<HB>￥</HB><N>_____________</N><L></B><N> 无修改不用填</N><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<B><L>实收金额:<HB>￥</HB><N>_____________</N><L></B><N> 无修改不用填</N><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<B><L>配送员：<N>________________</N><L></B><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L><HB>是否有冻品</HB>： <B>□</B> <HB>是</HB> <B>□</B> <HB>否</HB><L><BR><BR>");
            // appendContent(stringBuilder, xpYunSettingPolicyDTO,"<B><L>出货单备注：<L></B><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<B><L>客户备注:<L></B><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<B><L>老板备注:<L></B><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<BR><BR><N><L>客服电话:<BOLD>0731-00000000</BOLD> (周一至周六行政班时间联系)<L></N>");
            // appendContent(stringBuilder, xpYunSettingPolicyDTO,"<N><L>温馨提示: 签收前,请确认整件数量即可,明细数量需在到货24小时内自行清点,少货差异请联系客服为您处理<L></N><N><L>如有冻品,请开箱确认完好后再签字!!<L></N><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<N><C>================================================<C></N><BR><BR><BR><BR><B><C>签收回执单<C></B><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L>单 号：" + supplierOrderVO.getOrderNo() + "<BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L>订单日期：" + DateUtil.format(supplierOrderVO.getCreateTime(), "yyyy-MM-dd HH:mm:ss") + "<BR><BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L>业务员：" + coloneName + "<BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L>店 名：<BOLD>" + branchName + "</BOLD><BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L><N>联 系 人：" + contactName + "</N><BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L><N>联系电话：" + contactPhone + " </N><BR></L>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L>客户地址：" + branchAddr + "<BR><BR></L>");
//	    appendContent(stringBuilder, xpYunSettingPolicyDTO,"<N><L>整单合计："+master.getSheetAmt().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString()+"<L></N><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<N><L>订单金额：" + supplierOrderVO.getSubOrderAmt().setScale(2, RoundingMode.HALF_UP).toPlainString() + "<L> <R></R></N><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<N><L>优惠金额：" + supplierOrderVO.getSubDiscountAmt().setScale(2, RoundingMode.HALF_UP).toPlainString() + "<L> <R></R></N><BR>");
//	    appendContent(stringBuilder, xpYunSettingPolicyDTO,"<N><L>应付金额："+master.getSheetAmt().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString()+"<L> <R></R></N><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<N><L>支付方式：" + PayType.matchingLabel(supplierOrderVO.getPayWay()) + "<L></N><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<B><L>应收金额:" + (supplierOrderVO.getSubOrderAmt()) + "<L></B><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<B><L>拒收金额:<HB>￥</HB><N>_____________</N><L></B><N> 无修改不用填</N><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<B><L>实收金额:<HB>￥</HB><N>_____________</N><L></B><N> 无修改不用填</N><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<B><L>客户签收:<N>__________________</N><L></B><BR><BR>");
            //appendContent(stringBuilder, xpYunSettingPolicyDTO,"<N><L>温馨提示: 签收前,请确认整件数量即可,明细数量需在到货24小时内自行清点,少货差异请联系客服为您处理<L></N><N><L>如有冻品,请开箱确认完好后再签字!!</L></N><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<L><HB>是否有冻品</HB>： <B>□</B> <HB>是</HB> <B>□</B> <HB>否</HB><L><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<B><L>冻品客户确认:<N>__________________</N><L></B><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<HB><L>如有冻品,请开箱确认完好后再签字!!<L></HB><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<B><L>签收备注:<L></B><BR>.<BR>.<BR>.<BR>.<BR>.<BR>.<BR>.<BR><BR><BR>");
            appendContent(stringBuilder, xpYunSettingPolicyDTO, "<N><C>================================================<C></N><BR><BR></HB>");
            //根据配置的打印次数循环打印
            String quantity = xpYunSettingPolicyDTO.getQuantity();
            if (StringUtils.isBlank(quantity)){
                //如果未配置默认打印一次
                print(xpYunSettingPolicyDTO, stringBuilder.toString());
            }else{
                Integer count = Integer.valueOf(quantity);
                for (int j = 0; j < count; j++) {
                    print(xpYunSettingPolicyDTO, stringBuilder.toString());
                }
            }
        }
    }

    /**
     * @Description: 追加字符串
     * @Author: liuxingyu
     * @Date: 2024/4/24 18:49
     */
    private static StringBuilder appendContent(StringBuilder content, XpYunSettingPolicyDTO xpYunSettingPolicyDTO, String contentString) {
        if (ObjectUtil.equal(null, content)) {
            content = new StringBuilder();
        }
        StringBuilder oldString = content;
        StringBuilder newString = content.append(contentString);
        if (newString.toString().getBytes().length > 4096) {
            print(xpYunSettingPolicyDTO, oldString.toString());
            content.setLength(0);
            content.append(contentString);
        }
        //手动置null
        oldString = null;
        newString = null;
        return content;
    }


    /**
     * @Description: 打印
     * @Author: liuxingyu
     * @Date: 2024/4/24 18:33
     */
    private static void print(XpYunSettingPolicyDTO xpYunSettingPolicyDTO, String content) {
        PrintRequest printRequest = new PrintRequest();
        printRequest.setUser(xpYunSettingPolicyDTO.getUser());
        printRequest.setTimestamp(XpYunUtil.getUnix());
        printRequest.setSign(XpYunUtil.getSign(xpYunSettingPolicyDTO.getUser(), xpYunSettingPolicyDTO.getKey()));
        printRequest.setSn(xpYunSettingPolicyDTO.getSn());
        printRequest.setContent(content);
        ObjectRestResponse<String> print = service.print(printRequest);
        log.info("返回打印结果======================");
        if (print.getCode() != 0) {
            log.error("芯烨打印异常返回,异常信息{}", print.getMsg());
        }
    }
}
