package com.zksr.trade.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.divide.DivideFlowApi;
import com.zksr.account.api.divide.dto.DivideAmtDTO;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.domain.vo.openapi.receive.*;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.trade.api.after.vo.OrderAfterRequest;
import com.zksr.trade.api.after.vo.OrderAfterSaveRequest;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.dto.OrderReceiptRespDTO;
import com.zksr.trade.api.order.dto.TrdSupplierResDto;
import com.zksr.trade.api.order.vo.AfterApproveDtlEditVO;
import com.zksr.trade.api.order.vo.AfterDtl;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.controller.after.supplierVo.TrdSupplierAfterPageReqVO;
import com.zksr.trade.controller.after.supplierVo.TrdSupplierAfterSaveReqVO;
import com.zksr.trade.controller.order.vo.TrdOrderPageReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderSaveReqVO;
import com.zksr.trade.convert.after.TrdSupplierAfterConvert;
import com.zksr.trade.domain.*;
import com.zksr.trade.mapper.*;
import com.zksr.trade.service.ITrdAfterService;
import com.zksr.trade.service.ITrdSettleService;
import com.zksr.trade.service.ITrdSupplierAfterService;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import com.zksr.trade.service.price.ITrdPriceService;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import static com.zksr.common.core.constant.OpenApiConstants.ORDER_SYNC_FLAG_1;
import static com.zksr.common.core.constant.OpenApiConstants.ORDER_SYNC_FLAG_2;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 入驻商售后单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Service
@Slf4j
public class TrdSupplierAfterServiceImpl implements ITrdSupplierAfterService {
    @Autowired
    private TrdSupplierAfterMapper trdSupplierAfterMapper;

    @Autowired
    private TrdAfterMapper trdAfterMapper;

    @Autowired
    private TrdSupplierAfterDtlMapper trdSupplierAfterDtlMapper;

    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private ITrdAfterService trdAfterService;

    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;

    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;

    @Autowired
    private TrdOrderMapper trdOrderMapper;

    @Autowired
    private List<ITrdOrderHandlerService> trdOrderHandlerServices;

    @Autowired
    private ITrdPriceService trdPriceService;

    @Autowired
    private TrdDriverMapper trdDriverMapper;

    @Autowired
    private ITrdSettleService trdSettleService;

    @Autowired
    private TrdSettleMapper trdSettleMapper;

    @Autowired
    private TrdSupplierOrderSettleMapper trdSupplierOrderSettleMapper;
    
    @Resource
    private AccountApi accountApi;
    
    @Resource
    private DivideFlowApi divideFlowApi;

    /**
     * 新增入驻商售后单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdSupplierAfter(TrdSupplierAfterSaveReqVO createReqVO) {
        // 插入
        TrdSupplierAfter trdSupplierAfter = TrdSupplierAfterConvert.INSTANCE.convert(createReqVO);
        trdSupplierAfterMapper.insert(trdSupplierAfter);
        // 返回
        return trdSupplierAfter.getSupplierAfterId();
    }

    /**
     * 修改入驻商售后单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdSupplierAfter(TrdSupplierAfterSaveReqVO updateReqVO) {
        trdSupplierAfterMapper.updateById(TrdSupplierAfterConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除入驻商售后单
     *
     * @param supplierAfterId 入驻商订单id
     */
    @Override
    public void deleteTrdSupplierAfter(Long supplierAfterId) {
        // 删除
        trdSupplierAfterMapper.deleteById(supplierAfterId);
    }

    /**
     * 批量删除入驻商售后单
     *
     * @param supplierAfterIds 需要删除的入驻商售后单主键
     * @return 结果
     */
    @Override
    public void deleteTrdSupplierAfterBySupplierAfterIds(Long[] supplierAfterIds) {
        for(Long supplierAfterId : supplierAfterIds){
            this.deleteTrdSupplierAfter(supplierAfterId);
        }
    }

    /**
     * 获得入驻商售后单
     *
     * @param supplierAfterId 入驻商订单id
     * @return 入驻商售后单
     */
    @Override
    public TrdSupplierAfter getTrdSupplierAfter(Long supplierAfterId) {
        return trdSupplierAfterMapper.selectById(supplierAfterId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdSupplierAfter> getTrdSupplierAfterPage(TrdSupplierAfterPageReqVO pageReqVO) {
        return trdSupplierAfterMapper.selectPage(pageReqVO);
    }

    //!@开放API - 退货确认 - 入口
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveSalesReturn(AfterSalesReturnVO vo) {
        //售后单号
        String supplierAfterNo = vo.getSupplierAfterNo();
        
        //入驻商单号
        String supplierOrderNo = vo.getSupplierOrderNo();

        //校验
        if(ToolUtil.isEmpty(supplierAfterNo) && ToolUtil.isEmpty(supplierOrderNo)){
            throw exception(OPEN_TRD_SUPPLIER_AFTER_NO_IS_NULL);
        }

        //获取入驻商售后单主表信息
        TrdSupplierAfter trdSupplierAfter = null;
        if (ToolUtil.isNotEmpty(supplierAfterNo)) {
            trdSupplierAfter = trdSupplierAfterMapper.selectAfterBySupplierAfterNo(supplierAfterNo);
        }
        
        //如果没有售后单号，则用入驻商订单号 + trans_no = SHJ 查询
        if (null == trdSupplierAfter && ToolUtil.isNotEmpty(supplierOrderNo)) {
            trdSupplierAfter = trdSupplierAfterMapper.selectAfterBySupplierOrderNoAndTransNo(supplierOrderNo ,SheetTypeConstants.SHJ);
            supplierAfterNo = trdSupplierAfter.getSupplierAfterNo();
        }

        //校验
        if(ToolUtil.isEmpty(trdSupplierAfter)){
            throw exception(OPEN_TRD_SUPPLIER_AFTER_IS_NULL);
        }

        //获取入驻商售后单详情信息
        List<TrdSupplierAfterDtl> trdSupplierAfterDtlList = trdSupplierAfterDtlMapper.selectListBySupplierAfterNo(supplierAfterNo);

        //获取售后单信息
        //TrdAfter trdAfter = trdAfterMapper.selectById(trdSupplierAfter.getAfterId());

        //订单详情回传实收信息
        List<AfterSalesReturnDetailVO> detailVoList = vo.getDetail();

        //回传的订单详情数据转为Map  key: 第三方商品编号 - 行号
        Map<String, AfterSalesReturnDetailVO> detailVOMap = detailVoList.stream()
                .collect(Collectors.toMap(x -> x.getErpItemNo() + StringPool.DASH + x.getLineNum(), x -> x));

        //校验订单详情
        if(trdSupplierAfterDtlList.size() != detailVoList.size()){
            throw exception(OPEN_TRD_SUPPLIER_AFTER_DEL_SIZE_NONE);
        }

        //实收数量是否变动
        AtomicBoolean isUpdateQty = new AtomicBoolean(false);

        //处理详情数据
        trdSupplierAfterDtlList.forEach(x -> {
            SpuDTO spuDTO = trdCacheService.getSpuDTO(x.getSpuId());
            //匹配回传的数据
            AfterSalesReturnDetailVO detailVO = detailVOMap.get(spuDTO.getSourceNo() + StringPool.DASH + x.getLineNum());

            //校验行号商品详情是否匹配
            if(ToolUtil.isEmpty(detailVO)){
                throw exception(OPEN_TRD_SUPPLIER_ORDER_DEL_LINE_NONE,spuDTO.getSourceNo());
            }

            //实收数量
            BigDecimal returnQty = detailVO.getReturnQty();

            //校验实收数量不能大于 订单应退数量
            if(returnQty.compareTo(x.getReturnQty()) > 0){
                throw exception(OPEN_TRD_SUPPLIER_AFTER_DEL_CHECK_RETURN_QTY,spuDTO.getSourceNo());
            }

            //校验实收数量是否有变动
            if(returnQty.compareTo(x.getReturnQty()) != 0){
                isUpdateQty.set(true);
            }

            //设置最小单位实退数量
            x.setReturnQty(returnQty);

            //校验实退数量是否是符合当前退货规格 如不符合该商品按最小单位退
            BigDecimal returnUnitSize = x.getReturnUnitSize();

            boolean isReturnUnit = returnQty.remainder(returnUnitSize).compareTo(BigDecimal.ZERO) == NumberPool.INT_ZERO;

            if(isReturnUnit){
                //按当前退货单位设置退货确认信息
                //计算单位数量
                x.setReturnUnitQty(returnQty.divide(returnUnitSize, StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));

            }else{
                //由于实退数量不符合当前退货规格 按最小单位
                x.setReturnUnit(spuDTO.getMinUnit().toString());
                x.setReturnUnitType(UnitTypeEnum.UNIT_SMALL.getType());
                x.setReturnUnitQty(returnQty);
                x.setReturnUnitSize(BigDecimal.ONE);
                x.setReturnSalesUnitPrice(x.getExactReturnPrice());
            }

            //更改售后状态
            x.setReturnState(ReturnStateEnum.RETURN_THWC.getState());

            //设置退货完成时间
            x.setReturnTime(new Date());

            /*//实退金额 两位小数
            BigDecimal returnAmt = x.getReturnPrice()
                    .multiply(new BigDecimal(returnQty.toString()))
                    .setScale(StatusConstants.PRICE_RESERVE_2, BigDecimal.ROUND_HALF_UP);


            x.setExactReturnAmt(x.getExactReturnPrice()
                    .multiply(new BigDecimal(returnQty.toString()))
                    .setScale(StatusConstants.PRICE_RESERVE_6, BigDecimal.ROUND_HALF_UP));
            x.setReturnAmt(returnAmt);
            x.setRefundAmt(returnAmt);
            x.setOrderdtlRefundAmt(x.getOrderdtlRefundAmt().add(returnAmt));*/



        });

        //修改入驻商售后单主表
        trdSupplierAfter.setSourceOrderNo(vo.getSourceOrderNo());

        /*//退货总金额
        BigDecimal sumRefundAmt = trdSupplierAfterDtlList.stream().map(TrdSupplierAfterDtl::getReturnAmt).reduce(BigDecimal.ZERO,BigDecimal::add);

        //计算当前入驻商售后订单总金额 与 原订单金额差额
        BigDecimal cyAmt = trdSupplierAfter.getSubRefundAmt().subtract(sumRefundAmt);

        trdSupplierAfter.setSubRefundAmt(sumRefundAmt);


        //修改售后单主表金额
        trdAfter.setRefundAmt(trdAfter.getRefundAmt().subtract(cyAmt));
        //trdAfter.setOrderRefundAmt(trdAfter.getOrderRefundAmt().subtract(cyAmt));*/

        //更新数据
        //trdAfterMapper.updateById(trdAfter);
        trdSupplierAfterMapper.updateById(trdSupplierAfter);
        trdSupplierAfterDtlMapper.updateBatch(trdSupplierAfterDtlList);

        if (isUpdateQty.get()) { // 更改过数量，需要重新计算售后订单金额
            // 重新计算售后订单数量、金额    查询出最新的售后订单 然后更新
            trdPriceService.recalculateAfterAmt(trdAfterMapper.selectByAfterId(trdSupplierAfter.getAfterId()));
        }

/*        //组装并审核同意退款——售后单
        assembleApproveAfter(trdAfter,trdSupplierAfterDtlList,trdSupplierAfter.getSupplierId());*/

    }

    @Override
    public List<TrdSupplierAfterDtl> getSupplierAfterListBySupplierAfterNo(String supplierAfterNo) {
        return trdSupplierAfterDtlMapper.getSupplierAfterListBySupplierAfterNo(supplierAfterNo);
    }

    @Override
    public List<TrdSupplierAfterDtl> getListByIds(List<Long> ids) {
        return trdSupplierAfterDtlMapper.selectListByDtlIdList(ids);
    }

    @Override
    public TrdSupplierAfter getSupAfterBySupAfterNo(String supplierAfterNo) {
        return trdSupplierAfterMapper.selectOneBySupAfterNo(supplierAfterNo);
    }

    @Override
    public List<TrdSupplierAfter> getSupAfterByOrderNo(String AfterNo) {
        return trdSupplierAfterMapper.selectListByOrderNo(AfterNo);
    }

    //!@开放API - 收货确认 - 1、入口
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean receiveOrderTakeDelivery(ReceiveOrderTakeDeliveryVO vo) {
        //异步处理  无需要返回的售后订单编号
        boolean result = true;

        //入驻商订单号
        String supplierOrderNo = vo.getSupplierOrderNo();

        //校验
        if(ToolUtil.isEmpty(supplierOrderNo)){
            throw exception(OPEN_TRD_SUPPLIER_ORDER_NO_IS_NULL);
        }

        //获取入驻商订单主表
        TrdSupplierOrder supplierOrder = trdSupplierOrderMapper.selectOrderBySupplierOrderNo(supplierOrderNo);

        //校验
        if(ToolUtil.isEmpty(supplierOrder)){
            throw exception(OPEN_TRD_SUPPLIER_ORDER_IS_NULL);
        }

        // -------------订单收货收货------------------
        receiveSupplierOrderState(vo);

        //新增司机评价系统： 处理司机信息
        //司机名称
        String driverName = vo.getDriverName();
        //司机电话
        String driverPhone = vo.getDriverPhone();

        //处理司机信息
        if(ToolUtil.isNotEmpty(driverPhone) && ToolUtil.isNotEmpty(driverName)) {
            //根据司机电话查询入驻商司机信息
            TrdDriver trdDriver = trdDriverMapper.checkDriverPhoneUnique(driverPhone);
            //如果该手机号不存在司机 则新增司机
            if (ToolUtil.isEmpty(trdDriver)){
                //组装司机信息
                trdDriver = new TrdDriver();
                trdDriver.setSysCode(supplierOrder.getSysCode());
                trdDriver.setSupplierId(supplierOrder.getSupplierId());
                trdDriver.setDriverPhone(driverPhone);
                trdDriver.setDriverName(driverName);
                //新增司机
                trdDriverMapper.insert(trdDriver);
            }
            //绑定订单
            supplierOrder.setDriverId(trdDriver.getDriverId());

        }

        supplierOrder.setDeliveryState(SupplierOrderStatusEnum.RECEIVED.getCode());
        //更新订单信息
        trdSupplierOrderMapper.updateById(supplierOrder);

        // ---------------校验拒收-----------------
        //获取拒收单据详情
        List<ReceiveOrderTakeDeliveryDetailVO> detailVoList = vo.getDetail();

        //计算是否产生拒收商品数量
        BigDecimal sumJsQty = detailVoList.stream()
                .filter(detailVo -> detailVo != null && detailVo.getRejectableQty() != null)
                .map(ReceiveOrderTakeDeliveryDetailVO::getRejectableQty)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //获取入驻商详情信息
        List<TrdSupplierOrderDtl> trdSupplierOrderDtlList = trdSupplierOrderDtlMapper.selectListBySupplierOrderNo(supplierOrderNo);

        //获取订单主表  用于获取门店信息
        TrdOrder order = trdOrderMapper.getOrderByOrderNo(new TrdOrderPageReqVO(supplierOrder.getOrderNo()));


        //回传订单详情数量拒收集合
        List<OrderOutboundReturnDetailCyVO> jsVOList = new ArrayList<>();

        //是否校验订单详情商品条数
        if(vo.isCheckDetailNumFlag()){
            //校验拒收数量是否存在 不存在则只做收货处理
            if(sumJsQty.compareTo(BigDecimal.ZERO) > 0) {
                //校验是否已经生成了拒收单
                TrdSupplierAfter checkJsAfter = trdSupplierAfterMapper.selectAfterBySupplierOrderNoAndSupplierIdAndTransNo(supplierOrder.getSupplierOrderNo(), supplierOrder.getSupplierId(), ListUtil.toList(SheetTypeConstants.SHJ,"SHJ"));
                if (Objects.nonNull(checkJsAfter)) {
                    throw exception(OPEN_TRD_SUPPLIER_ORDER_CHECK_JS_AFTER, supplierOrder.getSourceOrderNo());
                }
                //校验订单详情
                if (trdSupplierOrderDtlList.size() != detailVoList.size()) {
                    throw exception(OPEN_TRD_SUPPLIER_ORDER_DEL_JS_SIZE_NONE);
                }

                // ---------------拒收流程-----------------
                assembleReceiveOrderDtlListByExact(detailVoList,trdSupplierOrderDtlList,jsVOList);

                //---------------拒收操作-----------------
                executeJsAfter(jsVOList,trdSupplierOrderDtlList,order,supplierOrder);

            }

        }else{
            //校验拒收数量是否存在 不存在则只做收货处理 如果拒收数量大于0  或者 返回收货详情条数不等于订单详情条数
            if (sumJsQty.compareTo(BigDecimal.ZERO) > 0 || trdSupplierOrderDtlList.size() != detailVoList.size()) {
                //校验是否已经生成了拒收单
                TrdSupplierAfter checkJsAfter = trdSupplierAfterMapper.selectAfterBySupplierOrderNoAndSupplierIdAndTransNo(supplierOrder.getSupplierOrderNo(), supplierOrder.getSupplierId(), ListUtil.toList(SheetTypeConstants.SHJ,"SHJ"));
                if (Objects.nonNull(checkJsAfter)) {
                    throw exception(OPEN_TRD_SUPPLIER_ORDER_CHECK_JS_AFTER, supplierOrder.getSourceOrderNo());
                }

                // ---------------拒收流程-----------------
                assembleReceiveOrderDtlList(detailVoList,trdSupplierOrderDtlList,jsVOList);

                //---------------拒收操作-----------------
                executeJsAfter(jsVOList,trdSupplierOrderDtlList,order,supplierOrder);
            }

        }

        // 订单收货数量汇总
        BigDecimal sumReceiveQty = detailVoList.stream()
                .filter(detailVo -> detailVo != null && detailVo.getReceiveQty() != null)
                .map(ReceiveOrderTakeDeliveryDetailVO::getReceiveQty)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 订单收货数量大于0 才进入发券操作;
        if (sumReceiveQty.compareTo(BigDecimal.ZERO) > 0) {
             //获取入驻商详情信息 最新的入驻商详情信息
            List<TrdSupplierOrderDtl> realDtlList = trdSupplierOrderDtlMapper.selectListBySupplierOrderNo(vo.getSupplierOrderNo());
            if (ToolUtil.isNotEmpty(realDtlList) && !realDtlList.isEmpty()) {
                 // 订单赠送优惠劵发放
                trdOrderHandlerServices.forEach(handler -> handler.orderTakeDeliveryReceiveCoupon(realDtlList.get(NumberPool.INT_ZERO).getOrderId(),
                        realDtlList.stream().map(TrdSupplierOrderDtl::getSupplierOrderId).collect(Collectors.toSet())));
            }
        }

        // 如果是O2O分销模式，生成分账和结算
        createSettleAndAdvideFlowByO2O(order, supplierOrderNo);
        
        return result;
    }
    
    /**
     * !@O2O - 1、生成分账和结算记录
     * !@开放API - 收货确认 - 3、生成分账和结算记录
     * @param order
     * @param supplierOrderNo
     */
    private void createSettleAndAdvideFlowByO2O(TrdOrder order, String supplierOrderNo) {
        
        if (!DistributionModeEnum.O2O.getCode().equals(order.getDistributionMode())) {
            return;
        }
        
        // 获取入驻商订单和明细
        List<TrdSupplierOrderDtl> orderDtlList = trdSupplierOrderDtlMapper.selectListBySupplierOrderNo(supplierOrderNo);
        
        // 遍历明细处理分成
        List<TrdSupplierOrderSettle> updateSettleList = new ArrayList<>();
        for (TrdSupplierOrderDtl dtl : orderDtlList) {
            
            // 获取已存在的结算记录
            TrdSupplierOrderSettle settle = trdSupplierOrderSettleMapper.selectSettleByOrderDtlId(dtl.getSupplierOrderDtlId());
            if (settle == null) {
                log.error("未找到订单明细对应的结算记录，supplierOrderDtlId: {}", dtl.getSupplierOrderDtlId());
                continue;
            }
            
            // 获取SKU信息
            SkuDTO skuDTO = trdCacheService.getSkuDTO(dtl.getSkuId());
            Integer unit = dtl.getOrderUnitType(); // 0-小 1-中 2-大
            BigDecimal profitAmount = BigDecimal.ZERO;
            BigDecimal retailPrice = BigDecimal.ZERO;
            
            // 根据单位类型获取对应的分润金额和零售价
            if (UnitTypeEnum.UNIT_SMALL.getType() == unit) {
                profitAmount = skuDTO.getProfitAmount() == null ? BigDecimal.ZERO : skuDTO.getProfitAmount();
                retailPrice = skuDTO.getRetailPrice() == null ? BigDecimal.ZERO : skuDTO.getRetailPrice();
            } else if (UnitTypeEnum.UNIT_MIDDLE.getType() == unit) {
                profitAmount = skuDTO.getMidProfitAmount() == null ? BigDecimal.ZERO : skuDTO.getMidProfitAmount();
                retailPrice = skuDTO.getMidRetailPrice() == null ? BigDecimal.ZERO : skuDTO.getMidRetailPrice();
            } else if (UnitTypeEnum.UNIT_LARGE.getType() == unit) {
                profitAmount = skuDTO.getLargeProfitAmount() == null ? BigDecimal.ZERO : skuDTO.getLargeProfitAmount();
                retailPrice = skuDTO.getLargeRetailPrice() == null ? BigDecimal.ZERO : skuDTO.getLargeRetailPrice();
            }
            
            // 计算实际数量 = 发货数量 - 拒收数量
            BigDecimal sendQty = dtl.getSendUnitQty() == null ? BigDecimal.ZERO : dtl.getSendUnitQty();
            BigDecimal rejectQty = dtl.getRejectUnitQty() == null ? BigDecimal.ZERO : dtl.getRejectUnitQty();
            BigDecimal actualQty = sendQty.subtract(rejectQty);
            
            // 入驻商分成 = (零售价 - 分润金额) * 实际数量
            BigDecimal supplierAmt = retailPrice.subtract(profitAmount).multiply(actualQty);
            // 门店分成 = 分润金额 * 实际数量
            BigDecimal branchAmt = profitAmount.multiply(actualQty);
            // 更新实际数量
            settle.setItemQty(actualQty);
            //更新实际支付（有些已经退款了）
            settle.setPayAmt(retailPrice.multiply(actualQty));
            settle.setSupplierAmt(supplierAmt);
            settle.setBranchAmt(branchAmt);
            settle.setBranchRate(branchAmt.divide(retailPrice, 2, BigDecimal.ROUND_HALF_UP));
            settle.setProfit(branchAmt);
            updateSettleList.add(settle);
        }
        
        log.info("createSettleAndAdvideFlowByO2O-> supplierOrderNo：{}",supplierOrderNo, JSON.toJSONString(updateSettleList));
        
        // 批量更新结算记录
        if (!updateSettleList.isEmpty()) {
            trdSupplierOrderSettleMapper.updateBatch(updateSettleList);
        }
        
        // 检查是否已存在结算记录
        List<TrdSettle> existingSettles = trdSettleMapper.selectList(
            new LambdaQueryWrapper<TrdSettle>()
                .eq(TrdSettle::getOrderId, order.getOrderId())
                .isNull(TrdSettle::getAfterId) // 确保不是售后单的结算记录
        );
        
        if (!existingSettles.isEmpty()) {
            log.info("入驻商订单{}已存在结算记录，跳过生成", supplierOrderNo);
            return;
        }
        
        // 生成TrdSettle结算记录
        List<TrdSettle> trdSettles = trdSettleService.createBatchSettle(order);
        if (!trdSettles.isEmpty()) {
            
            //设置为待结算
            trdSettles.stream().forEach(settle ->settle.setState(StatusConstants.SETTLE_STATE_3));
            trdSettleMapper.insertBatch(trdSettles);
            
            //根据各个商户类型合并分账金额
            DivideAmtDTO divideAmtDTO = new DivideAmtDTO();
            divideAmtDTO.setSupplierOrderNo(supplierOrderNo);
            trdSettles.stream()
            .collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
            .forEach((key, settleList) -> {
                BigDecimal settleAmt = settleList.stream().map(TrdSettle::getSettleAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                divideAmtDTO.getAccountDivideAmt().put(key,settleAmt);
            });
            
            //生成分账流水
            try {
                
                divideFlowApi.createAdvideFlow(divideAmtDTO);
            } catch (Exception e) {
                log.error("生成分账流水失败，supplierOrderNo: {}", supplierOrderNo, e);
                // 这里不抛出异常，因为分账流水生成失败不影响主流程
            }
        }
    }
    
    @Override
    public List<OrderReceiptRespDTO> getOrderReceiptInfoBySupplierAfterNo(SyncReceiptSendDTO reqVo) {
        return trdSupplierAfterMapper.getOrderReceiptInfoBySupplierAfterNo(reqVo);
    }

    @Override
    public void syncAfterResponseSuccess(SyncDataDTO data) {
        //推送成功后更改推送标志
        updateByPushStatus(data.getDataId(),ORDER_SYNC_FLAG_1 );
    }

    @Override
    public void updateByPushStatus(String supplierAfterNo, Integer pushStatus) {
        trdSupplierAfterMapper.updateByPushStatus(supplierAfterNo,pushStatus );
    }

    @Override
    public void afterCancel(AfterCancelVO vo) {

        //直接调用取消售后单退货（同意申请后）接口
        trdAfterService.cancelAfter(vo.getSupplierAfterNo(), SourceType.OTHERSYSTEM.getType());

    }
    
    @Override
    public void createO2OSettleAndDivide(TrdSupplierOrderSaveReqVO createReqVO) {
        String supplierOrderNo = createReqVO.getSupplierOrderNo();
        //获取入驻商订单主表
        TrdSupplierOrder supplierOrder = trdSupplierOrderMapper.selectOrderBySupplierOrderNo(supplierOrderNo);
        //获取订单主表  用于获取门店信息
        TrdOrder order = trdOrderMapper.getOrderByOrderNo(new TrdOrderPageReqVO(supplierOrder.getOrderNo()));
        this.createSettleAndAdvideFlowByO2O(order,supplierOrderNo);
    }
    
    private void validateTrdSupplierAfterExists(Long supplierAfterId) {
        if (trdSupplierAfterMapper.selectById(supplierAfterId) == null) {
            throw exception(TRD_SUPPLIER_AFTER_NOT_EXISTS);
        }
    }

    /**
     * 组装并同意退款——售后单
     * @param trdAfter
     * @param trdSupplierAfterDtlList
     * @param supplierId
     */
    private void assembleApproveAfter(TrdAfter trdAfter, List<TrdSupplierAfterDtl> trdSupplierAfterDtlList,Long supplierId){
        //获取门店信息
        BranchDTO branchDTO = trdCacheService.getBranchDTO(trdAfter.getBranchId());

        AfterApproveDtlEditVO approveDtlEditVO = new AfterApproveDtlEditVO();

        //组装主表数据
        approveDtlEditVO.setReturnPhone(branchDTO.getContactPhone());
        approveDtlEditVO.setReturnAddr(branchDTO.getBranchAddr());
        approveDtlEditVO.setHandleState(AfterHandleStateEnum.DCL.getCode());
        approveDtlEditVO.setSupplierId(supplierId);
        approveDtlEditVO.setAfterId(trdAfter.getAfterId());

        //明细列表
        List<AfterDtl> afterDtlList = new ArrayList<>();

        //组装明细数据
        trdSupplierAfterDtlList.stream().forEach(x->{
            AfterDtl afterDtl = new AfterDtl();
            afterDtl.setSupplierAfterDtlId(x.getSupplierAfterDtlId());
            afterDtl.setSupplierAfterDtlNo(x.getSupplierAfterDtlNo());
            afterDtl.setReturnQty(x.getReturnQty());
            afterDtl.setReturnAmt(x.getReturnAmt());
            afterDtl.setRealityReturnQty(x.getReturnQty());
            afterDtl.setRealityReturnAmt(x.getReturnAmt());

            afterDtlList.add(afterDtl);
        });

        approveDtlEditVO.setAfterDtlList(afterDtlList);

        //审核同意退货——售后单
        trdAfterService.approveAfterReturn(approveDtlEditVO);
    }

    /**
     * 根据拒收单号组装并同意退款——（拒收）售后单
     * @param supplierAfter
     * @param supplierId
     */
    private AfterApproveDtlEditVO assembleApproveAfter(Long supplierId,TrdSupplierAfter supplierAfter){
        //查询已保存的售后单、入驻商售后单详情
        TrdAfter trdAfter = trdAfterMapper.selectById(supplierAfter.getAfterId());

        //获取入驻商售后单详情信息
        List<TrdSupplierAfterDtl> trdSupplierAfterDtlList = trdSupplierAfterDtlMapper.selectListBySupplierAfterNo(supplierAfter.getSupplierAfterNo());


        //获取门店信息
        BranchDTO branchDTO = trdCacheService.getBranchDTO(trdAfter.getBranchId());

        AfterApproveDtlEditVO approveDtlEditVO = new AfterApproveDtlEditVO();

        //组装主表数据
        approveDtlEditVO.setReturnPhone(branchDTO.getContactPhone());
        approveDtlEditVO.setReturnAddr(branchDTO.getBranchAddr());
        approveDtlEditVO.setHandleState(AfterHandleStateEnum.DCL.getCode());
        approveDtlEditVO.setSupplierId(supplierId);
        approveDtlEditVO.setAfterId(trdAfter.getAfterId());

        //明细列表
        List<AfterDtl> afterDtlList = new ArrayList<>();

        //组装明细数据
        trdSupplierAfterDtlList.stream().forEach(x->{
            AfterDtl afterDtl = new AfterDtl();
            afterDtl.setSupplierAfterDtlId(x.getSupplierAfterDtlId());
            afterDtl.setSupplierAfterDtlNo(x.getSupplierAfterDtlNo());
            afterDtl.setReturnQty(x.getReturnQty());
            afterDtl.setReturnAmt(x.getReturnAmt());
            afterDtl.setRealityReturnQty(x.getReturnQty());
            afterDtl.setRealityReturnAmt(x.getReturnAmt());

            afterDtlList.add(afterDtl);
        });

        approveDtlEditVO.setAfterDtlList(afterDtlList);

        //审核同意退货——售后单
        trdAfterService.approveAfterReturn(approveDtlEditVO);

        return approveDtlEditVO;
    }


    /**
     * 生成售后单
     * @param jsVOList
     * @param order
     * @param supplierOrder
     //* @param sourceSHSOrderNo 外部拒收单号
     */
    public void supplierAfterJsSave(List<OrderOutboundReturnDetailCyVO> jsVOList, TrdOrder order, TrdSupplierOrder supplierOrder){
        OrderAfterSaveRequest request = new OrderAfterSaveRequest();

        //组装售后主订单数据
        request.setAfterType(AfterTypeEnum.RETURNSANDREFUNDS.getState());
        request.setSource(NumberPool.LONG_FIVE);
        request.setOrderId(String.valueOf(order.getOrderId()));
        request.setOrderNo(order.getOrderNo());
        request.setDeliveryState(DeliveryStatusEnum.COMPLETE_SH.getCode());

        //组装售后入驻商订单主表信息
        OrderAfterRequest.SupplierOrder supplierOrderAfter = new OrderAfterRequest.SupplierOrder();
        supplierOrderAfter.setSupplierId(supplierOrder.getSupplierId());
        supplierOrderAfter.setSupplierOrderId(supplierOrder.getSupplierOrderId());
        //supplierOrderAfter.setSourceOrderNo(sourceSHSOrderNo);
        //设置订单类型
        supplierOrderAfter.setTransNo(SheetTypeConstants.SHJ);

        //转换售后入驻商详情
        List<OrderAfterRequest.SupplierOrder.SupplierOrderDtl> supplierOrderDtlAfterList = HutoolBeanUtils.toBean(jsVOList, OrderAfterRequest.SupplierOrder.SupplierOrderDtl.class);


        supplierOrderAfter.setSupplierOrderDtls(supplierOrderDtlAfterList);
        request.setSupplierOrders(ListUtil.toList(supplierOrderAfter));
        trdAfterService.saveOrderAfter(request);
    }

    /**
     * 更新订单收货状态
     * @param vo
     */
    public void receiveSupplierOrderState(ReceiveOrderTakeDeliveryVO vo){
        //获取入驻商详情信息
        List<TrdSupplierOrderDtl> trdSupplierOrderDtlList = trdSupplierOrderDtlMapper.selectListBySupplierOrderNo(vo.getSupplierOrderNo());

        trdSupplierOrderDtlList.stream().forEach(dtl ->{
            //设置收货是否收款
            //dtl.setIsProceeds(vo.getIsProceeds());
            trdOrderHandlerServices.forEach(handler -> handler.orderTakeDelivery(dtl));
        });
        // 订单收货完成
        trdOrderHandlerServices.forEach(handler -> handler.orderTakeDeliveryFinish(trdSupplierOrderDtlList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void afterOrderReceiveCallback(AfterOrderReceiveCallbackVO vo) {
        //绑定第三方退货订单编号
        TrdSupplierAfter trdSupplierAfter = trdSupplierAfterMapper.selectAfterBySupplierAfterNo(vo.getSupplierAfterNo());
        trdSupplierAfter.setSourceOrderNo(vo.getSourceOrderNo());
        trdSupplierAfter.setPushStatus(ORDER_SYNC_FLAG_2);
        trdSupplierAfterMapper.updateById(trdSupplierAfter);
    }

    /**
     * 组装订单收货详情相关数据 精确组装 要求：订单收货详情数据需与订单详情数量一致
     * @param detailVoList 收货详情集合
     * @param trdSupplierOrderDtlList 订单详情集合
     * @param jsVOList 拒收集合（需要组装数据）
     */
    private void assembleReceiveOrderDtlListByExact(List<ReceiveOrderTakeDeliveryDetailVO> detailVoList, List<TrdSupplierOrderDtl> trdSupplierOrderDtlList, List<OrderOutboundReturnDetailCyVO> jsVOList){

        //拒收订单详情数据转为Map  key: 第三方商品编号 - 行号
        Map<String, ReceiveOrderTakeDeliveryDetailVO> detailVOMap = detailVoList.stream()
                .collect(Collectors.toMap(x -> x.getErpItemNo() + StringPool.DASH + x.getLineNum(), x -> x));

        trdSupplierOrderDtlList.forEach(x -> {
            SpuDTO spuDTO = trdCacheService.getSpuDTO(x.getSpuId());
            //匹配拒收数据
            String erpItemNo = spuDTO.getSourceNo() + StringPool.DASH + x.getLineNum();
            ReceiveOrderTakeDeliveryDetailVO detailVO = detailVOMap.get(erpItemNo);

            //校验行号商品详情是否匹配
            if (ToolUtil.isEmpty(detailVO)) {
                throw exception(OPEN_TRD_SUPPLIER_ORDER_DEL_LINE_NONE, spuDTO.getSourceNo());
            }

            //如果商品信息存在拒收数量 则生成对应的拒收数据
            if (detailVO.getRejectableQty().compareTo(BigDecimal.ZERO) > 0) {
                //拒收商品信息匹配上后 需要修改入驻商订单详情
                //总数
                BigDecimal orderQty = x.getSendUnitQty().multiply(BigDecimal.valueOf(x.getSendUnitSize()));

                //拒收数量
                BigDecimal jsQty = detailVO.getRejectableQty();

                //拒收金额
                BigDecimal jsAmt =jsQty.multiply(x.getExactPrice());

                //设置销售详情拒收信息
                //原拒收数量
                BigDecimal rejectQty = Objects.isNull(x.getRejectUnitQty()) ? BigDecimal.ZERO : x.getRejectUnitQty();
                x.setRejectUnit(spuDTO.getMinUnit().intValue());
                x.setRejectUnitType(UnitTypeEnum.UNIT_SMALL.getType().longValue());
                x.setRejectUnitQty(rejectQty.add(jsQty));
                x.setRejectQty(rejectQty.add(jsQty));
                x.setRejectUnitSize(BigDecimal.ZERO);

                //校验  当前拒收数量 +  订单收货数量 + 订单原拒收数量 = 订单发货数量
                if (orderQty.compareTo(rejectQty.add(jsQty).add(detailVO.getReceiveQty())) != 0) {
                    throw exception(OPEN_TRD_SUPPLIER_AFTER_JS_DEL_QTY_SUM_NONE, erpItemNo);
                }


                //组装拒收数据
                OrderOutboundReturnDetailCyVO jsVo = new OrderOutboundReturnDetailCyVO();
                jsVo.setSupplierOrderDtlId(x.getSupplierOrderDtlId());
                jsVo.setTotalNum(orderQty);
                jsVo.setRefundPrice(x.getExactPrice());
                jsVo.setRefundQty(jsQty);
                jsVo.setRefundAmt(jsAmt);
                jsVo.setUnitType(UnitTypeEnum.UNIT_SMALL.getType());
                jsVOList.add(jsVo);
            }

            //设置收货是否收款
            //x.setIsProceeds(vo.getIsProceeds());

        });

    }

    /**
     * 组装订单收货详情相关数据 不要求订单收货详情数据需与订单详情数量一致
     * @param detailVoList 收货详情集合
     * @param trdSupplierOrderDtlList 订单详情集合
     * @param jsVOList 拒收集合（需要组装数据）
     */
    private void assembleReceiveOrderDtlList(List<ReceiveOrderTakeDeliveryDetailVO> detailVoList, List<TrdSupplierOrderDtl> trdSupplierOrderDtlList, List<OrderOutboundReturnDetailCyVO> jsVOList){

        //拒收订单详情数据转为Map  key: 第三方商品编号 - 行号
        Map<String, ReceiveOrderTakeDeliveryDetailVO> detailVOMap = detailVoList.stream()
                .collect(Collectors.toMap(x -> x.getErpItemNo() + StringPool.DASH + x.getLineNum(), x -> x));

        trdSupplierOrderDtlList.forEach(x -> {
            SpuDTO spuDTO = trdCacheService.getSpuDTO(x.getSpuId());
            //匹配拒收数据
            String erpItemNo = spuDTO.getSourceNo() + StringPool.DASH + x.getLineNum();
            ReceiveOrderTakeDeliveryDetailVO detailVO = detailVOMap.get(erpItemNo);

            //总数
            BigDecimal orderQty = x.getSendUnitQty().multiply(BigDecimal.valueOf(x.getSendUnitSize()));

            //校验行号商品详情是否匹配
            if (ToolUtil.isEmpty(detailVO)) {
                log.info("订单收货，订单编号：{}，未回传订单详情ID:{}",x.getSupplierOrderNo(),x.getSupplierOrderDtlId());
                //直接做拒收处理
                //默认组装成收货订单信息为0 所有数量拒收
                detailVO = new ReceiveOrderTakeDeliveryDetailVO();
                detailVO.setLineNum(x.getLineNum())
                        .setErpItemNo(spuDTO.getSourceNo())
                        .setReceiveQty(BigDecimal.ZERO)
                        .setRejectableQty(orderQty);
            }

            //如果商品信息存在拒收数量 则生成对应的拒收数据
            if (detailVO.getRejectableQty().compareTo(BigDecimal.ZERO) > 0) {
                //拒收商品信息匹配上后 需要修改入驻商订单详情
                //拒收数量
                BigDecimal jsQty = detailVO.getRejectableQty();

                //拒收金额
                BigDecimal jsAmt = new BigDecimal(jsQty.toString()).multiply(x.getExactPrice());

                //设置销售详情拒收信息
                //原拒收数量
                BigDecimal rejectQty = Objects.isNull(x.getRejectUnitQty()) ? BigDecimal.ZERO : x.getRejectUnitQty();
                x.setRejectUnit(spuDTO.getMinUnit().intValue());
                x.setRejectUnitType(UnitTypeEnum.UNIT_SMALL.getType().longValue());
                x.setRejectUnitQty(rejectQty.add(jsQty));
                x.setRejectQty(rejectQty.add(jsQty));
                x.setRejectUnitSize(BigDecimal.ZERO);

                //校验  当前拒收数量 +  订单收货数量 + 订单原拒收数量 = 订单发货数量
                if (orderQty.compareTo(rejectQty.add(jsQty).add(detailVO.getReceiveQty())) != 0) {
                    throw exception(OPEN_TRD_SUPPLIER_AFTER_JS_DEL_QTY_SUM_NONE, erpItemNo);
                }


                //组装拒收数据
                OrderOutboundReturnDetailCyVO jsVo = new OrderOutboundReturnDetailCyVO();
                jsVo.setSupplierOrderDtlId(x.getSupplierOrderDtlId());
                jsVo.setTotalNum(orderQty);
                jsVo.setRefundPrice(x.getExactPrice());
                jsVo.setRefundQty(jsQty);
                jsVo.setRefundAmt(jsAmt);
                jsVo.setUnitType(UnitTypeEnum.UNIT_SMALL.getType());
                jsVOList.add(jsVo);
            }

            //设置收货是否收款
            //x.setIsProceeds(vo.getIsProceeds());

        });

    }



    private void executeJsAfter(List<OrderOutboundReturnDetailCyVO> jsVOList,List<TrdSupplierOrderDtl> trdSupplierOrderDtlList,TrdOrder order,TrdSupplierOrder supplierOrder){
        //校验 拒收单据信息是否异常
        if (ObjectUtil.isNull(jsVOList) || jsVOList.isEmpty()) {
            throw exception(OPEN_TRD_SUPPLIER_AFTER_JS_DEL_SIZE_NONE);
        }

        //拒收订单业务操作：

        //更新订单详情
        trdSupplierOrderDtlMapper.updateBatch(trdSupplierOrderDtlList);

        //生成拒收售后单
        supplierAfterJsSave(jsVOList, order, supplierOrder);

        //默认同意退货申请
        //组装并审核同意退款——拒收售后单
        //根据第三方订单号查询已保存入驻商售后单
        TrdSupplierAfter supplierAfter = trdSupplierAfterMapper.selectAfterBySupplierOrderNoAndSupplierIdAndTransNo(supplierOrder.getSupplierOrderNo(), supplierOrder.getSupplierId(), ListUtil.toList(SheetTypeConstants.SHJ,"SHJ"));
        //supplierAfterNo = supplierAfter.getSupplierAfterNo();

        //审核同意退货——售后单  返回组装同意退货的数据结构信息  用于组装同意退款信息
        AfterApproveDtlEditVO afterApproveDtlEditVO = assembleApproveAfter(supplierOrder.getSupplierId(), supplierAfter);

        //审核同意退款——售后单
        afterApproveDtlEditVO.setHandleState(AfterHandleStateEnum.THZ.getCode());
        afterApproveDtlEditVO.setProductType(NumberPool.LONG_ONE);
        trdAfterService.approveAfterRefund(afterApproveDtlEditVO);
    }

    private void assembleDtlListByExac(){

    }


    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 入驻商售后单 TODO 补充编号 ==========
    // ErrorCode TRD_SUPPLIER_AFTER_NOT_EXISTS = new ErrorCode(TODO 补充编号, "入驻商售后单不存在");


}
