package com.zksr.trade.print.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 打印订单VO
 * @Author: liuxingyu
 * @Date: 2024/4/19 15:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PrintSupplierOrderVO {
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 业务员id
     */
    private Long colonelId;

    /**
     * 业务员名称
     */
    private Long colonelName;

    /**
     * 门店id
     */
    private Long branchId;

    /**
     * 门店地址
     */
    private String branchAddr;

    /**
     * 支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款
     */
    private String payWay;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 入驻商ID
     */
    private Long supplierId;

    /**
     * 入驻商订单id
     */
    private Long supplierOrderId;

    /**
     * 入驻商订单编号
     */
    private String supplierOrderNo;

    /**
     * 优惠金额
     **/
    private BigDecimal subDiscountAmt;

    /**
     * 支付金额
     **/
    private BigDecimal subPayAmt;

    /**
     * 订单金额 未减去优惠的订单金额
     **/
    private BigDecimal subOrderAmt;

    /**
     * 订单商品总数量
     **/
    private BigDecimal subOrderNum;

    /**
     * 入驻商订单详情集合
     */
    private List<PrintSupplierOrderDtlVO> supplierOrderDtlVOList;
}
