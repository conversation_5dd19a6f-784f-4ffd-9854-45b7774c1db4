package com.zksr.trade.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdOrderLog;
import com.zksr.trade.controller.orderLog.vo.TrdOrderLogPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 订单日志Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@Mapper
public interface TrdOrderLogMapper extends BaseMapperX<TrdOrderLog> {
    default PageResult<TrdOrderLog> selectPage(TrdOrderLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdOrderLog>()
                    .eqIfPresent(TrdOrderLog::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdOrderLog::getSupplierOrderDtlId, reqVO.getSupplierOrderDtlId())
                    .eqIfPresent(TrdOrderLog::getSupplierOrderDtlNo, reqVO.getSupplierOrderDtlNo())
                    .eqIfPresent(TrdOrderLog::getBeforeState, reqVO.getBeforeState())
                    .eqIfPresent(TrdOrderLog::getAfterState, reqVO.getAfterState())
                    .eqIfPresent(TrdOrderLog::getOperateType, reqVO.getOperateType())
                    .eqIfPresent(TrdOrderLog::getContent, reqVO.getContent())
                .orderByDesc(TrdOrderLog::getSysCode));
    }

    /**
     * 根据入驻商订单明细ID 获取入驻商订单明细操作记录
     * @param supplierOrderDtlId 入驻商订单明细ID
     * @return
     */
//    default List<TrdOrderLog> selectListBySupplierOrderDtlId(Long supplierOrderDtlId) {
//        return selectList(new LambdaQueryWrapperX<TrdOrderLog>()
//                .eqIfPresent(TrdOrderLog::getSupplierOrderDtlId, supplierOrderDtlId));
//    }

    List<TrdOrderLog> selectListBySupplierOrderDtlId(@Param("supplierOrderDtlId") Long supplierOrderDtlId);

}
