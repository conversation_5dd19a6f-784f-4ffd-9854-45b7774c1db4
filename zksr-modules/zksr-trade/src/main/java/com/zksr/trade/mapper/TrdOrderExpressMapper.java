package com.zksr.trade.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressPageReqVO;

import java.util.List;
import java.util.Set;


/**
 * 订单快递Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Mapper
public interface TrdOrderExpressMapper extends BaseMapperX<TrdOrderExpress> {
    default PageResult<TrdOrderExpress> selectPage(TrdOrderExpressPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdOrderExpress>()
                    .eqIfPresent(TrdOrderExpress::getOrderExpressId, reqVO.getOrderExpressId())
                    .eqIfPresent(TrdOrderExpress::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdOrderExpress::getOrderId, reqVO.getOrderId())
                    .eqIfPresent(TrdOrderExpress::getOrderNo, reqVO.getOrderNo())
                    .eqIfPresent(TrdOrderExpress::getSupplierOrderDtlId, reqVO.getSupplierOrderDtlId())
                    .eqIfPresent(TrdOrderExpress::getSupplierOrderDtlNo, reqVO.getSupplierOrderDtlNo())
                    .eqIfPresent(TrdOrderExpress::getExpressNo, reqVO.getExpressNo())
                    .eqIfPresent(TrdOrderExpress::getExpressCom, reqVO.getExpressCom())
                    .eqIfPresent(TrdOrderExpress::getExpressComNo, reqVO.getExpressComNo())
                    .eqIfPresent(TrdOrderExpress::getReceiveMan, reqVO.getReceiveMan())
                    .eqIfPresent(TrdOrderExpress::getReceivePhone, reqVO.getReceivePhone())
                    .eqIfPresent(TrdOrderExpress::getAddress, reqVO.getAddress())
                    .eqIfPresent(TrdOrderExpress::getLatestInfo, reqVO.getLatestInfo())
                    .eqIfPresent(TrdOrderExpress::getExpressImportDtlId, reqVO.getExpressImportDtlId())
                    .eqIfPresent(TrdOrderExpress::getRealInTransitTime, reqVO.getRealInTransitTime())
                    .eqIfPresent(TrdOrderExpress::getState, reqVO.getState())
                .orderByDesc(TrdOrderExpress::getOrderExpressId));
    }

    /**
     * 根据单据明细Id查询快递单号信息
     * @param supplierOrderDtlIds
     * @return
     */
    default List<TrdOrderExpress> getOrderExpressInfoByOrderDtlIds(Set<Long> supplierOrderDtlIds){
        return selectList(new LambdaQueryWrapperX<TrdOrderExpress>()
        .inIfPresent(TrdOrderExpress::getSupplierOrderDtlId, supplierOrderDtlIds));
    }

    default TrdOrderExpress getOrderExpressBySupAfterDtlId(Long supplierAfterDtlId){
        return selectOne(new LambdaQueryWrapper<TrdOrderExpress>()
                .eq(TrdOrderExpress::getSupplierOrderDtlId,supplierAfterDtlId));
    }

    /**
     * 根据单据明细id和快递单号查询 快递信息
     * @param dtlId 明细ID
     * @param expressNo 快递单号
     * @return
     */
    default List<TrdOrderExpress> getOrderExpressExistByOrderDtlIdAndExpressNo(Long dtlId, String expressNo){
        return selectList(new LambdaQueryWrapper<TrdOrderExpress>()
                .eq(TrdOrderExpress::getSupplierOrderDtlId, dtlId)
                .eq(TrdOrderExpress::getExpressNo, expressNo));
    }
}
