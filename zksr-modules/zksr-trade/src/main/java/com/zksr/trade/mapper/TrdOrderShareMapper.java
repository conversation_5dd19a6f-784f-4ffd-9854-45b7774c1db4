package com.zksr.trade.mapper;

import cn.hutool.core.date.DateUtil;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdOrderShare;
import com.zksr.trade.controller.order.vo.TrdOrderSharePageReqVO;


/**
 * 订单分享Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-29
 */
@Mapper
public interface TrdOrderShareMapper extends BaseMapperX<TrdOrderShare> {
    default PageResult<TrdOrderShare> selectPage(TrdOrderSharePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdOrderShare>()
                    .eqIfPresent(TrdOrderShare::getShareOrderId, reqVO.getShareOrderId())
                    .eqIfPresent(TrdOrderShare::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdOrderShare::getSupplierOrderId, reqVO.getSupplierOrderId())
                    .eqIfPresent(TrdOrderShare::getOrderId, reqVO.getOrderId())
                    .eqIfPresent(TrdOrderShare::getRemoteIp, reqVO.getRemoteIp())
                    .eqIfPresent(TrdOrderShare::getExpirationTime, reqVO.getExpirationTime())
                .orderByDesc(TrdOrderShare::getShareOrderId));
    }

    default Long selectValidCnt(Long orderId) {
        return selectCount(new LambdaQueryWrapperX<TrdOrderShare>()
                .eqIfPresent(TrdOrderShare::getOrderId, orderId)
                .gt(TrdOrderShare::getExpirationTime, DateUtil.date())
        );
    }

    default TrdOrderShare selectByShareKey(String shareKey) {
        return selectOne(new LambdaQueryWrapperX<TrdOrderShare>()
                .eqIfPresent(TrdOrderShare::getShareKey, shareKey)
        );
    }
}
