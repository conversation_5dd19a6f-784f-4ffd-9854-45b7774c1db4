package com.zksr.trade.convert.trdPackage;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageRespVO;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageSaveReqVO;
import com.zksr.trade.domain.TrdPackage;

/**
* 包裹 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-07-18
*/
@Mapper
public interface TrdPackageConvert {

    TrdPackageConvert INSTANCE = Mappers.getMapper(TrdPackageConvert.class);

    TrdPackageRespVO convert(TrdPackage trdPackage);

    TrdPackage convert(TrdPackageSaveReqVO trdPackageSaveReq);

    PageResult<TrdPackageRespVO> convertPage(PageResult<TrdPackage> trdPackagePage);
}