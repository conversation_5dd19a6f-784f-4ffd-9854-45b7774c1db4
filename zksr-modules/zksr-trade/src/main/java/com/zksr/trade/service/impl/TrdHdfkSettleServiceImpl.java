package com.zksr.trade.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.api.hdfk.dto.HdfkNoPaymentTotalDTO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkOrderRespVO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkOrderItemVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSkuItemRespVO;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.mapper.TrdSupplierOrderDtlMapper;
import com.zksr.trade.mapper.TrdSupplierOrderMapper;
import com.zksr.trade.service.ITrdHdfkPayService;
import com.zksr.trade.service.TrdCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdHdfkSettleMapper;
import com.zksr.trade.convert.hdfk.TrdHdfkSettleConvert;
import com.zksr.trade.domain.TrdHdfkSettle;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettlePageReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSaveReqVO;
import com.zksr.trade.service.ITrdHdfkSettleService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 货到付款结算Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Service
public class TrdHdfkSettleServiceImpl implements ITrdHdfkSettleService {

    @Autowired
    private TrdHdfkSettleMapper trdHdfkSettleMapper;

    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;

    @Autowired
    private TrdSupplierOrderDtlMapper supplierOrderDtlMapper;

    @Autowired
    private TrdCacheService trdCacheService;

    @Resource
    private AccountApi accountApi;

    @Autowired
    private ITrdHdfkPayService hdfkPayService;

    /**
     * 新增货到付款结算
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    public void insertTrdHdfkSettle(List<TrdHdfkSettleSaveReqVO> createReqVO) {
        if (Objects.isNull(createReqVO) || createReqVO.isEmpty()) {
            throw new ServiceException("结算信息不能为空");
        }
        // 如果上下文中已经有sysCode了 就不需要添加sysCode了
        Long sysCode = SecurityContextHolder.hasSysCode();
        List<TrdHdfkSettle> hdfkSettles = createReqVO.stream().map(TrdHdfkSettleConvert.INSTANCE::convert).collect(Collectors.toList());
        DateTime date = DateUtil.date();
        hdfkSettles.forEach(item -> {
            if (Objects.nonNull(sysCode)) {
                item.setSysCode(null);
            }
            // 都是同步操作的, 直接结算成功
            item.setSettleTime(date);
            item.setState(NumberPool.INT_ONE);
        });
        hdfkSettles.stream().collect(Collectors.groupingBy(TrdHdfkSettle::getSupplierOrderDtlId)).forEach((supplierOrderDtlId, list) -> {
            TrdSupplierOrderDtl supplierOrderDtl = supplierOrderDtlMapper.selectById(supplierOrderDtlId);
            if ( Objects.isNull(supplierOrderDtl.getPayState()) || supplierOrderDtl.getPayState() != NumberPool.INT_THREE) {
                throw new ServiceException(StringUtils.format("当前入驻商订单商品非货到付款未付款状态 supplierOrderDtlId_{}", supplierOrderDtlId));
            }
        });
        // 插入
        trdHdfkSettleMapper.insertBatch(hdfkSettles);
        List<AccAccountFlowDTO> flowList = new ArrayList<>();
        for (TrdHdfkSettle hdfkSettle : hdfkSettles) {
            AccAccountFlowDTO flowDTO = AccAccountFlowDTO.builder()
                    .sysCode(hdfkSettle.getSysCode())
                    .busiWithdrawableAmt(BigDecimal.ZERO.subtract(hdfkSettle.getSettleAmt()))
                    .busiType(AccountBusiType.BRANCH_HDFK_ORDER.getType())
                    .busiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField())
                    .merchantType(MerchantTypeEnum.BRANCH_DEBT.getType())
                    .merchantId(hdfkSettle.getBranchId())
                    .platform(PayChannelEnum.NONE.getCode())
                    .busiId(hdfkSettle.getHdfkSettleId())
                    .build();
            flowList.add(flowDTO);
        }
        // 生成门店欠款流水 并 执行流水
        accountApi.saveAccountFlowAndProcess(flowList).checkError();
    }

    /**
     * 获得货到付款结算
     *
     * @param hdfkSettleId 订单结算id
     * @return 货到付款结算
     */
    @Override
    public TrdHdfkSettle getTrdHdfkSettle(Long hdfkSettleId) {
        return trdHdfkSettleMapper.selectById(hdfkSettleId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdHdfkSettle> getTrdHdfkSettlePage(TrdHdfkSettlePageReqVO pageReqVO) {
        return trdHdfkSettleMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BranchHdfkOrderRespVO> getBranchHdfkList(BranchHdfkReqVO branchHdfkReqVO) {
        if (StringUtils.isNotEmpty(branchHdfkReqVO.getCondition())) {
            String condition = StrUtil.removeAll(branchHdfkReqVO.getCondition(), "[^0-9]");
            // 如果传参的数字大于18, 则认定未订单号查询
            if (condition.length() > 18) {
                branchHdfkReqVO.setOrderNo(branchHdfkReqVO.getCondition());
                branchHdfkReqVO.setCondition(null);
            }
        }
        // 设置分页数
        branchHdfkReqVO.setPageNo((branchHdfkReqVO.getPageNo() - 1) * branchHdfkReqVO.getPageSize());

        List<TrdHdfkOrderItemVO> itemListData = trdHdfkSettleMapper.selectHdfkOrderItemList(branchHdfkReqVO);
        Map<String, List<TrdHdfkOrderItemVO>> supplierOrderMap = itemListData.stream().collect(Collectors.groupingBy(item -> StringUtils.format("{}:{}", item.getSupplierOrderId(), item.getDeliveryState())));
        // 获取入驻商订单列表
        ArrayList<String> supplierOrderIdList = ListUtil.toList(supplierOrderMap.keySet());
        List<BranchHdfkOrderRespVO> result = new ArrayList<>();
        for (String key : supplierOrderIdList) {
            List<TrdHdfkOrderItemVO> hdfkOrderItemVOS = supplierOrderMap.get(key);
            TrdHdfkOrderItemVO firstOrderItemVO = hdfkOrderItemVOS.get(NumberPool.INT_ZERO);

            SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(firstOrderItemVO.getSupplierId());

            // 支付金额
            BigDecimal payAmt = hdfkOrderItemVOS.stream().map(TrdHdfkOrderItemVO::getSettleAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal payAmt = hdfkOrderItemVOS.stream()
//                    .map(hdfkOrderItem -> {
//                        long itemNum = hdfkOrderItem.getTotalNum() - hdfkOrderItem.getAfterTotalNum();
//                        return hdfkOrderItem.getExactPrice().multiply(BigDecimal.valueOf(itemNum));
//                    }).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (payAmt.compareTo(BigDecimal.ZERO) > 0) {
                BranchHdfkOrderRespVO build = BranchHdfkOrderRespVO.builder()
                        .createTime(firstOrderItemVO.getCreateTime())
                        .supplierOrderNo(firstOrderItemVO.getSupplierOrderNo())
                        .orderId(firstOrderItemVO.getOrderId())
                        .orderNo(firstOrderItemVO.getOrderNo())
                        .supplierOrderId(firstOrderItemVO.getSupplierOrderId())
                        .supplierId(firstOrderItemVO.getSupplierId())
                        .deliveryState(firstOrderItemVO.getDeliveryState())
                        .payAmt(payAmt)
                        .supplierName(supplierDTO.getSupplierName())
                        .avatar(supplierDTO.getAvatar())
                        .itemList(TrdHdfkSettleConvert.INSTANCE.convertHdfkOrderItemList(hdfkOrderItemVOS))
                        .build();
                result.add(build);
            }
        }
        // 最新的排在最前
        result.sort(Comparator.comparing(BranchHdfkOrderRespVO::getSupplierOrderId).reversed());
        return result;
    }

    @Override
    public HdfkNoPaymentTotalDTO getBranchHdfkNoPaymentTotal(BranchHdfkReqVO branchHdfkReqVO) {
        return trdHdfkSettleMapper.selectBranchHdfkNoPaymentTotal(branchHdfkReqVO);
    }

    @Override
    public PageResult<TrdHdfkSettleSkuItemRespVO> getTrdHdfkSettleSkuItemPage(TrdHdfkSettlePageReqVO pageReqVO) {
        Page<TrdHdfkSettleSkuItemRespVO> page = PageUtils.startPage(pageReqVO);
        List<TrdHdfkSettleSkuItemRespVO> list = trdHdfkSettleMapper.selectHdfkSkuItemList(pageReqVO);

        // 渲染规格参数
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, String> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        list.forEach(item -> {
            SkuDTO skuDTO = trdCacheService.getSkuDTO(item.getSkuId());
            TrdHdfkSettleConvert.INSTANCE.buidSetSkuItemRespVO(
                    item,
                    trdCacheService.getSupplierDTO(item.getSupplierId()),
                    trdCacheService.getBranchDTO(item.getBranchId()),
                    PropertyAndValDTO.getProperties(skuDTO.getProperties())
            );
            // 设置规格单位名称
            item.setUnitName(unitMap.get(String.valueOf(item.getOrderUnit())));
        });
        return PageResult.result(page, list);
    }

    @Override
    public List<TrdHdfkSettleSkuItemRespVO> getTrdHdfkSettleSkuItemRespVOByHdfkId(Long hdfkPayId) {
        TrdHdfkSettlePageReqVO pageReqVO = new TrdHdfkSettlePageReqVO();
        pageReqVO.setHdfkPayId(hdfkPayId);
        List<TrdHdfkSettleSkuItemRespVO> list = trdHdfkSettleMapper.selectHdfkSkuItemList(pageReqVO);
        // 渲染规格参数
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, String> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        list.forEach(item -> {
            SkuDTO skuDTO = trdCacheService.getSkuDTO(item.getSkuId());
            TrdHdfkSettleConvert.INSTANCE.buidSetSkuItemRespVO(
                    item,
                    trdCacheService.getSupplierDTO(item.getSupplierId()),
                    trdCacheService.getBranchDTO(item.getBranchId()),
                    PropertyAndValDTO.getProperties(skuDTO.getProperties())
            );
            // 设置规格单位名称
            item.setUnitName(unitMap.get(String.valueOf(item.getOrderUnit())));
        });
        return list;
    }

    @Override
    public List<TrdHdfkSettle> getTrdHdfkSettleList(String supplierOrderNo) {
       return trdHdfkSettleMapper.getTrdHdfkSettleList(supplierOrderNo);
    }

}
