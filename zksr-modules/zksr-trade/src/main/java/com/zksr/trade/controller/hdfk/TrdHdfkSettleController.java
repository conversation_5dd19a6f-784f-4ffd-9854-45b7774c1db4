package com.zksr.trade.controller.hdfk;

import javax.validation.Valid;

import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSkuItemRespVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdHdfkSettle;
import com.zksr.trade.service.ITrdHdfkSettleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettlePageReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSaveReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleRespVO;
import com.zksr.trade.convert.hdfk.TrdHdfkSettleConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 货到付款结算Controller
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Api(tags = "管理后台 - 货到付款结算接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/hdfkSettle")
public class TrdHdfkSettleController {
    @Autowired
    private ITrdHdfkSettleService trdHdfkSettleService;

    /**
     * 分页查询货到付款结算
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得货到付款结算分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(supplierAlias = "tsod", dcAlias = "tso", dcFieldAlias = SystemConstants.DC_ID)
    public CommonResult<PageResult<TrdHdfkSettleSkuItemRespVO>> getPage(@Valid TrdHdfkSettlePageReqVO pageReqVO) {
        PageResult<TrdHdfkSettleSkuItemRespVO> pageResult = trdHdfkSettleService.getTrdHdfkSettleSkuItemPage(pageReqVO);
        return success(pageResult);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:hdfk-settle:add";
        /** 编辑 */
        public static final String EDIT = "trade:hdfk-settle:edit";
        /** 删除 */
        public static final String DELETE = "trade:hdfk-settle:remove";
        /** 列表 */
        public static final String LIST = "trade:hdfk-settle:list";
        /** 查询 */
        public static final String GET = "trade:hdfk-settle:query";
        /** 停用 */
        public static final String DISABLE = "trade:hdfk-settle:disable";
        /** 启用 */
        public static final String ENABLE = "trade:hdfk-settle:enable";
    }
}
