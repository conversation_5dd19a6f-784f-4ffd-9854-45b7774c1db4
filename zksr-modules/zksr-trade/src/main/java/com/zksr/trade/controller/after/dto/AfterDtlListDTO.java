package com.zksr.trade.controller.after.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.enums.PayStateEnum;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年04月22日 17:15
 * @description: AfterDtlListDTO
 */
@ApiModel("售后单明细 - PC　查询详情列表 Response DTO")
@Data
public class AfterDtlListDTO {

    @ApiModelProperty(value = "售后ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long afterId;

    @ApiModelProperty(value = "售后单明细ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private  Long supplierAfterDtlId;

    @ApiModelProperty(value = "售后单明细编号")
    private  String supplierAfterDtlNo;

    @ApiModelProperty(value = "skuId")
    private  Long skuId;

    @ApiModelProperty(value = "商品SPU id")
    private Long spuId;

    @ApiModelProperty(value = "售后创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date afterCreateTime;

    @ApiModelProperty(value = "订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderCreateTime;

    @ApiModelProperty(value = "订单支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderPayTime;

    @ApiModelProperty(value = "付款方式（数据字典KEY：sys_pay_way）")
    private  Long payWay;

    @ApiModelProperty(value = "退货单价")
    private BigDecimal returnPrice;

    @ApiModelProperty(value = "订单数量")
    private Long orderTotalNum;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderTotalAmt;

    @ApiModelProperty(value = "退货数量")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "退货金额")
    private BigDecimal returnAmt;

    @ApiModelProperty(value = "售后类型")
    private  Long afterType;

    @ApiModelProperty(value = "退货原因")
    private String reason;

    @ApiModelProperty(value = "申请时上传的凭证照片")
    private String applyImgs;

    @ApiModelProperty(value = "入驻商ID")
    private  Long supplierId;

    @ApiModelProperty(value = "售后单号")
    private String afterNo;

    @ApiModelProperty(value = "商品类型")
    private  Long itemType;

    @ApiModelProperty(value = "订单单号")
    private String orderNo;

    @ApiModelProperty(value = "售后地址")
    private String returnAddr;

    @ApiModelProperty(value = "售后手机号")
    private String returnPhone;

    @ApiModelProperty(value = "门店Id")
    private Long branchId;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店地址")
    private String branchAddress;

    @ApiModelProperty(value = "退款失败原因")
    private String refundFailReason;

    /**
     * 参见 {@link PayStateEnum}
     */
    @ApiModelProperty(value = "订单支付状态, 0-未支付,1-已支付,2-订单取消,3-货到付款未收款,4-货到付款已收款")
    private Integer orderPayState;

    @ApiModelProperty(value = "门店联系人")
    private String branchContactName;

    @ApiModelProperty(value = "门店联系人手机号")
    private String branchContactPhone;

    @ApiModelProperty(value = "门店头像")
    private String branchImages;

    @ApiModelProperty(value = "门店编号")
    private String branchNo;

    @ApiModelProperty(value = "退货单位", notes = "例如小单位: 件, 中单位: 箱, 大单位: 车")
    private String returnUnit;

    @ApiModelProperty(value = "处理状态处理中状态{0：(处理中状态全部)，1：待处理， 2：未退货，3：退货中，4：已退货，5：待退款，6：退款中，7：退款失败, 8:已处理（完成），9：已拒绝}")
    private Long handleState;

    @ApiModelProperty(value = "售后单位数量")
    private BigDecimal returnUnitQty;

    @ApiModelProperty(value = "售后单位换算数量")
    private Integer returnUnitSize;

    @ApiModelProperty(value = "售后购买单位实际销售单价")
    private BigDecimal returnSalesUnitPrice;

    @ApiModelProperty(value = "订单购买单位", notes = "从订单，最小单位的单位，中单位的单位，大单位的单位")
    private String orderUnit;

     @ApiModelProperty(value = "订单购买单位大小")
    private Integer orderUnitType;

    @ApiModelProperty(value = "订单购买单位数量")
    private BigDecimal orderUnitQty;

    @ApiModelProperty(value = "订单换算数量")
    private BigDecimal orderUnitSize;

    @ApiModelProperty(value = "订单商品可售后数量（最小单位）")
    private BigDecimal waitReturnQty;

    @ApiModelProperty(value = "订单商品可售后金额")
    private BigDecimal waitReturnAmt;

    @ApiModelProperty(value = "入驻商售后订单编号")
    private String supplierAfterNo;

    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    @ApiModelProperty(value = "入驻商订单实付金额")
    private BigDecimal subPayAmt;

    @ApiModelProperty(value = "入驻商订单金额")
    private BigDecimal subOrderAmt;

    @ApiModelProperty(value = "入驻商订单优惠金额")
    private BigDecimal subDiscountAmt;

    @ApiModelProperty(value = "原申请退货最小单位数量")
    private BigDecimal originalReturnQty;

    @ApiModelProperty(value = "原申请退货金额")
    private BigDecimal originalReturnAmt;

    @ApiModelProperty(value = "入驻商订单门店余额支付金额")
    private BigDecimal subPayBalanceAmt;

}
