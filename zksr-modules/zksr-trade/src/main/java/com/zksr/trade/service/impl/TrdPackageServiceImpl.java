package com.zksr.trade.service.impl;

import java.util.List;

import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zksr.trade.controller.trdPackage.vo.TrdPackagePageReqVO;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageSaveReqVO;
import com.zksr.trade.convert.trdPackage.TrdPackageConvert;
import com.zksr.trade.mapper.TrdPackageMapper;
import com.zksr.trade.domain.TrdPackage;
import com.zksr.trade.service.ITrdPackageService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 包裹Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class TrdPackageServiceImpl implements ITrdPackageService {
	@Autowired
	private TrdPackageMapper trdPackageMapper;
	
	/**
	 * 新增包裹
	 *
	 * @param createReqVO 创建信息
	 * @return 结果
	 */
	@Override
	public Long insertTrdPackage(TrdPackageSaveReqVO createReqVO) {
		// 插入
		TrdPackage trdPackage = TrdPackageConvert.INSTANCE.convert(createReqVO);
		trdPackageMapper.insert(trdPackage);
		// 返回
		return trdPackage.getPackageId();
	}
	
	/**
	 * 修改包裹
	 *
	 * @param updateReqVO 修改信息
	 * @return 结果
	 */
	@Override
	public void updateTrdPackage(TrdPackageSaveReqVO updateReqVO) {
		trdPackageMapper.updateById(TrdPackageConvert.INSTANCE.convert(updateReqVO));
	}
	
	/**
	 * 删除包裹
	 *
	 * @param packageId 包裹ID
	 */
	@Override
	public void deleteTrdPackage(Long packageId) {
		// 删除
		trdPackageMapper.deleteById(packageId);
	}
	
	/**
	 * 批量删除包裹
	 *
	 * @param packageIds 需要删除的包裹主键
	 * @return 结果
	 */
	@Override
	public void deleteTrdPackageByPackageIds(Long[] packageIds) {
		for(Long packageId : packageIds){
			// @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
			// this.deleteTrdPackage(packageId);
		}
	}
	
	/**
	 * 获得包裹
	 *
	 * @param packageId 包裹ID
	 * @return 包裹
	 */
	@Override
	public TrdPackage getTrdPackage(Long packageId) {
		return trdPackageMapper.selectById(packageId);
	}
	
	/**
	 * 查询分页数据
	 * @param pageReqVO
	 * @return
	 */
	@Override
	public PageResult<TrdPackage> getTrdPackagePage(TrdPackagePageReqVO pageReqVO) {
		return trdPackageMapper.selectPage(pageReqVO);
	}
	
	
	/**
	 * 根据入驻商订单号查询包裹列表
	 * @param supplierOrderNo 入驻商订单号
	 * @return 包裹列表
	 */
	@Override
	public List<TrdPackage> getPackageListBySupplierOrderNo(String supplierOrderNo) {
		return trdPackageMapper.selectListBySupplierOrderNo(supplierOrderNo);
	}
	
	/**
	 * 根据入驻商订单号查询包裹列表
	 * @return 包裹列表
	 */
	@Override
	public TrdPackage getPackageByOrderIdAndPackageNo(Long supplierOrderId, String packageNo) {
		return trdPackageMapper.getPackageByOrderIdAndPackageNo(supplierOrderId,packageNo);
	}
	
	private void validateTrdPackageExists(Long packageId) {
		if (trdPackageMapper.selectById(packageId) == null) {
			throw exception(TRD_PACKAGE_NOT_EXISTS);
		}
	}
	
	// TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
	// ========== 包裹 TODO 补充编号 ==========
	// ErrorCode TRD_PACKAGE_NOT_EXISTS = new ErrorCode(TODO 补充编号, "包裹不存在");
	
	
}
