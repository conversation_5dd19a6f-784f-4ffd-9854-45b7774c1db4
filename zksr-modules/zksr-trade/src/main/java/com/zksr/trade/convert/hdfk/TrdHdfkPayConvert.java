package com.zksr.trade.convert.hdfk;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdHdfkPay;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayRespVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPaySaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 货到付款付款单 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-28
*/
@Mapper
public interface TrdHdfkPayConvert {

    TrdHdfkPayConvert INSTANCE = Mappers.getMapper(TrdHdfkPayConvert.class);

    TrdHdfkPayRespVO convert(TrdHdfkPay trdHdfkPay);

    TrdHdfkPay convert(TrdHdfkPaySaveReqVO trdHdfkPaySaveReq);

    PageResult<TrdHdfkPayRespVO> convertPage(PageResult<TrdHdfkPay> trdHdfkPayPage);
}