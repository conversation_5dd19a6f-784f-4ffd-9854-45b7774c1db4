package com.zksr.trade.convert.info;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdInvoiceInfo;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceInfoRespVO;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceInfoSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 发票主 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-07-19
*/
@Mapper
public interface TrdInvoiceInfoConvert {

    TrdInvoiceInfoConvert INSTANCE = Mappers.getMapper(TrdInvoiceInfoConvert.class);

    TrdInvoiceInfoRespVO convert(TrdInvoiceInfo trdInvoiceInfo);

    TrdInvoiceInfo convert(TrdInvoiceInfoSaveReqVO trdInvoiceInfoSaveReq);

    PageResult<TrdInvoiceInfoRespVO> convertPage(PageResult<TrdInvoiceInfo> trdInvoiceInfoPage);
}