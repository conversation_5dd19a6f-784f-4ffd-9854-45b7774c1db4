package com.zksr.trade.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdOrderLogMapper;
import com.zksr.trade.domain.TrdOrderLog;
import com.zksr.trade.controller.orderLog.vo.TrdOrderLogPageReqVO;
import com.zksr.trade.controller.orderLog.vo.TrdOrderLogSaveReqVO;
import com.zksr.trade.service.ITrdOrderLogService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 订单日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@Service
public class TrdOrderLogServiceImpl implements ITrdOrderLogService {
    @Autowired
    private TrdOrderLogMapper trdOrderLogMapper;

    /**
     * 新增订单日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdOrderLog(TrdOrderLogSaveReqVO createReqVO) {
        // 插入
        TrdOrderLog trdOrderLog = HutoolBeanUtils.toBean(createReqVO, TrdOrderLog.class);
        trdOrderLogMapper.insert(trdOrderLog);
        // 返回
        return trdOrderLog.getSysCode();
    }

    /**
     * 修改订单日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdOrderLog(TrdOrderLogSaveReqVO updateReqVO) {
        trdOrderLogMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, TrdOrderLog.class));
    }

    /**
     * 删除订单日志
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deleteTrdOrderLog(Long sysCode) {
        // 删除
        trdOrderLogMapper.deleteById(sysCode);
    }

    /**
     * 批量删除订单日志
     *
     * @param sysCodes 需要删除的订单日志主键
     * @return 结果
     */
    @Override
    public void deleteTrdOrderLogBySysCodes(Long[] sysCodes) {
        for(Long sysCode : sysCodes){
            this.deleteTrdOrderLog(sysCode);
        }
    }

    /**
     * 获得订单日志
     *
     * @param sysCode 平台商id
     * @return 订单日志
     */
    @Override
    public TrdOrderLog getTrdOrderLog(Long sysCode) {
        return trdOrderLogMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdOrderLog> getTrdOrderLogPage(TrdOrderLogPageReqVO pageReqVO) {
        return trdOrderLogMapper.selectPage(pageReqVO);
    }

    private void validateTrdOrderLogExists(Long sysCode) {
        if (trdOrderLogMapper.selectById(sysCode) == null) {
            throw exception(TRD_ORDER_LOG_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 订单日志 TODO 补充编号 ==========
    // ErrorCode TRD_ORDER_LOG_NOT_EXISTS = new ErrorCode(TODO 补充编号, "订单日志不存在");


}
