package com.zksr.trade.controller.order.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("入驻商首页返回实体类")
public class HomePageRespVO {
    @ApiModelProperty("当月销售额")
    private Long monthAmount;

    @ApiModelProperty("当日订单")
    private Long todayOrder;

    @ApiModelProperty("当日销售数量")
    private Long todayAmount;

    @ApiModelProperty("三十天销售数据")
    private List<HomeOrdersRespVO> thirtyDaysOrders;
}
