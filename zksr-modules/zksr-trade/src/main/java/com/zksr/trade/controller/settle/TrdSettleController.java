package com.zksr.trade.controller.settle;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.orderSettle.dto.OrderSettleResDTO;
import com.zksr.trade.api.orderSettle.vo.OrderSettlePageVO;
import com.zksr.trade.controller.settle.vo.TrdSettlePageReqVO;
import com.zksr.trade.controller.settle.vo.TrdSettleRespVO;
import com.zksr.trade.controller.settle.vo.TrdSettleSaveReqVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdSettle;
import com.zksr.trade.service.ITrdSettleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 订单结算Controller
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Api(tags = "管理后台 - 订单结算接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/settle")
public class TrdSettleController {
    @Autowired
    private ITrdSettleService trdSettleService;

    /**
     * 新增订单结算
     */
    @ApiOperation(value = "新增订单结算", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "订单结算", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdSettleSaveReqVO createReqVO) {
        return success(trdSettleService.insertTrdSettle(createReqVO));
    }

    /**
     * 修改订单结算
     */
    @ApiOperation(value = "修改订单结算", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "订单结算", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdSettleSaveReqVO updateReqVO) {
            trdSettleService.updateTrdSettle(updateReqVO);
        return success(true);
    }

    /**
     * 删除订单结算
     */
    @ApiOperation(value = "删除订单结算", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "订单结算", businessType = BusinessType.DELETE)
    @DeleteMapping("/{settleIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] settleIds) {
        trdSettleService.deleteTrdSettleBySettleIds(settleIds);
        return success(true);
    }

    /**
     * 获取订单结算详细信息
     */
    @ApiOperation(value = "获得订单结算详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{settleId}")
    public CommonResult<TrdSettleRespVO> getInfo(@PathVariable("settleId") Long settleId) {
        TrdSettle trdSettle = trdSettleService.getTrdSettle(settleId);
        return success(HutoolBeanUtils.toBean(trdSettle, TrdSettleRespVO.class));
    }

    /**
     * 分页查询订单结算
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得订单结算分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdSettleRespVO>> getPage(@Valid TrdSettlePageReqVO pageReqVO) {
        PageResult<TrdSettle> pageResult = trdSettleService.getTrdSettlePage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, TrdSettleRespVO.class));
    }

    /**
     * 分页查询账户订单结算流水（平台商、运营商、业务员））
     */
    @GetMapping("/getAccountOrderSettleFlowPageList")
    @ApiOperation(value = "分页查询账户订单结算流水（平台商、运营商、业务员）", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
//    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<OrderSettleResDTO>> getAccountOrderSettleFlowPageList(@Valid OrderSettlePageVO pageReqVO) {
        return success(trdSettleService.getOrderSettleInfoPage(pageReqVO));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:settle:add";
        /** 编辑 */
        public static final String EDIT = "trade:settle:edit";
        /** 删除 */
        public static final String DELETE = "trade:settle:remove";
        /** 列表 */
        public static final String LIST = "trade:settle:list";
        /** 查询 */
        public static final String GET = "trade:settle:query";
        /** 停用 */
        public static final String DISABLE = "trade:settle:disable";
        /** 启用 */
        public static final String ENABLE = "trade:settle:enable";
    }
}
