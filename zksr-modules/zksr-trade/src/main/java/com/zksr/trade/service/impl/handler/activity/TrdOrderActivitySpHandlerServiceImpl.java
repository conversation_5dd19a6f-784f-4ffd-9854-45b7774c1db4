package com.zksr.trade.service.impl.handler.activity;

import com.zksr.common.core.enums.SpActivityTimesRuleEnum;
import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.promotion.api.activity.dto.ActivityDTO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.domain.TrdAfterDiscountDtl;
import com.zksr.trade.domain.TrdOrderDiscountDtl;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.activity.ITrdOrderActivityHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;

/**
 *  订单优惠活动处理器 ———— 特价促销
 */
@Service
@Slf4j
public class TrdOrderActivitySpHandlerServiceImpl implements ITrdOrderActivityHandlerService {

    @Autowired
    private RedisActivityService redisActivityService;
    @Autowired
    private TrdCacheService trdCacheService;
    @Autowired
    private RedisService redisService;

    // 返还特价活动库存
    @Override
    public void orderCancelReturnActivity(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {
        toddList.stream()
                .filter(orderDiscount -> Objects.equals(TrdDiscountTypeEnum.SP.getType(), orderDiscount.getDiscountType()))
                .forEach(orderDiscount -> {
                    TrdSupplierOrderDtl supplierOrderDtl = tsodMap.get(orderDiscount.getSupplierOrderDtlId());
                    if (supplierOrderDtl == null) {
                        return;
                    }

                    ActivityDTO activityDTO = trdCacheService.getActivityDTO(orderDiscount.getDiscountId());

                    // 活动商品已使用数量
                    BigDecimal saleQty = supplierOrderDtl.getTotalNum().multiply(NumberPool.BIGDECIMAL_GROUND);
                    // 活动次数限定规则
                    if (Objects.nonNull(activityDTO.getTimesRule())) {
                        switch (SpActivityTimesRuleEnum.formValue(activityDTO.getTimesRule())){
                            case RULE_LEVEL_0: // 每日一次
                            case RULE_LEVEL_1: // 活动期间内仅一次
                                // 移除活动仅参加一次标识
                                redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), tor.getBranchId()));
                                break;
                            case RULE_LEVEL_4: // 商品级别每日一次
                            case RULE_LEVEL_5: // 商品级别仅一次
                                // 移除活动仅参加一次标识
                                redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), tor.getBranchId()));
                                break;
                        }
                    }
                    // set特价活动商品门店已购数量
                    BigDecimal orderUnitQtyNegate = BigDecimal.valueOf(supplierOrderDtl.getOrderUnitQty()).negate();
                    if (UnitTypeEnum.UNIT_SMALL.getType().equals(supplierOrderDtl.getOrderUnitType())) {
                        redisActivityService.setSpSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQtyNegate, null, null);
                        // set特价活动商品已购总数量（小单位）
                        redisActivityService.setSpSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQtyNegate, null, null);
                    } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(supplierOrderDtl.getOrderUnitType())) {
                        redisActivityService.setSpMidSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQtyNegate, null, null);
                        // set特价活动商品已购总数量（中单位）
                        redisActivityService.setSpMidSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQtyNegate, null, null);
                    } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(supplierOrderDtl.getOrderUnitType())) {
                        redisActivityService.setSpLargeSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQtyNegate, null, null);
                        // set特价活动商品已购总数量（大单位）
                        redisActivityService.setSpLargeSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQtyNegate, null, null);
                    }


                });
    }

    @Override
    public void afterCancelReturnActivity(TrdOrder tor, List<TrdSupplierOrderDtl> tsodList, List<TrdOrderDiscountDtl> toddList, TrdAfter tar, List<TrdSupplierAfterDtl> tsadList, List<TrdAfterDiscountDtl> taddList) {
        // 构建售后详情映射
        Map<Long, TrdSupplierAfterDtl> afterDtlMap = convertMap(tsadList, TrdSupplierAfterDtl::getSupplierAfterDtlId);
        taddList.stream()
                .filter(tadd -> Objects.equals(TrdDiscountTypeEnum.SP.getType(), tadd.getDiscountType()))
                .collect(Collectors.groupingBy(TrdAfterDiscountDtl::getDiscountId))
                .forEach((key, value) -> {

                    // 根据促销活动Id 查询出所参与的所有商品
                    List<Long> tsodIdList = toddList.stream().filter(todd -> Objects.equals(key, todd.getDiscountId())).map(TrdOrderDiscountDtl::getSupplierOrderDtlId).collect(Collectors.toList());

                    // 获取活动参与商品列表是否已全部售后
                    boolean boo = tsodList.stream().filter(tsod -> tsodIdList.contains(tsod.getSupplierOrderDtlId()))
                            .allMatch(tsod -> tsod.getTotalNum().compareTo(tsod.getCancelQty()) == 0);

                    ActivityDTO activityDTO = trdCacheService.getActivityDTO(key);
                    // 活动次数限定规则
                    if (Objects.nonNull(activityDTO.getTimesRule())) {
                        // 活动级别 每日一次 或 仅一次
                        if (SpActivityTimesRuleEnum.isRuleLevel0OrLevel1(activityDTO.getTimesRule()) && boo) {
                            // 移除活动仅参加一次标识
                            redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(key, tor.getBranchId()));
                        }
                    }


                    value.forEach(tadd -> {
                        TrdSupplierAfterDtl tsad = afterDtlMap.get(tadd.getSupplierAfterDtlId());
                        // 活动商品已使用数量
                        BigDecimal returnQty = tsad.getReturnQty().multiply(NumberPool.BIGDECIMAL_GROUND);

                        // 活动次数限定规则
                        if (Objects.nonNull(activityDTO.getTimesRule())) {
                            // 获取活动参与商品列表是否已全部售后
                            boolean itemBoo = tsodList.stream().filter(tsod -> Objects.equals(tsod.getSupplierOrderDtlId(), tsad.getSupplierOrderDtlId()))
                                    .allMatch(tsod -> tsod.getTotalNum().compareTo(tsod.getCancelQty()) == 0);

                            // 当商品已经全部售后，且类型为【商品级别每日一次】、【商品级别仅一次】
                            if (SpActivityTimesRuleEnum.isRuleLevel4OrLevel5(activityDTO.getTimesRule()) && itemBoo) {
                                // 移除活动仅参加一次标识
                                redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(tadd.getDiscountId(), tadd.getDiscountRuleId(), tor.getBranchId()));
                            }

                        }
                        // set特价活动商品门店已购数量
//                        redisActivityService.setSpSaleNum(tor.getBranchId(), tadd.getDiscountId(), tadd.getDiscountRuleId(), returnQty, null, null);

                        // 返还门店已购买数量
                        BigDecimal returnUnitQtyNegate = tsad.getReturnUnitQty().negate();
                        if (UnitTypeEnum.UNIT_SMALL.getType().equals(tsad.getReturnUnitType())) {
                            redisActivityService.setSpSaleNum(tor.getBranchId(), tadd.getDiscountId(), tadd.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                            // set特价活动商品已购总数量（小单位）
                            redisActivityService.setSpSaleNum(tadd.getDiscountId(), tadd.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                        } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(tsad.getReturnUnitType())) {
                            redisActivityService.setSpMidSaleNum(tor.getBranchId(), tadd.getDiscountId(), tadd.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                            // set特价活动商品已购总数量（中单位）
                            redisActivityService.setSpMidSaleNum(tadd.getDiscountId(), tadd.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                        } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(tsad.getReturnUnitType())) {
                            redisActivityService.setSpLargeSaleNum(tor.getBranchId(), tadd.getDiscountId(), tadd.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                            // set特价活动商品已购总数量（大单位）
                            redisActivityService.setSpLargeSaleNum(tadd.getDiscountId(), tadd.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                        }


                    });
                });
    }

    @Override
    public void restoreCancelledOrderDiscounts(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {
        toddList.stream()
                .filter(orderDiscount -> Objects.equals(TrdDiscountTypeEnum.SP.getType(), orderDiscount.getDiscountType()))
                .forEach(orderDiscount -> {
                    TrdSupplierOrderDtl supplierOrderDtl = tsodMap.get(orderDiscount.getSupplierOrderDtlId());
                    // 获取活动信息
                    ActivityDTO activity = trdCacheService.getActivityDTO(orderDiscount.getDiscountId());
                    // 总活动缓存过期时间
                    long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());
                    // 活动商品已使用数量
                    BigDecimal saleQty = supplierOrderDtl.getTotalNum();
                    // 活动次数限定规则
                    if (Objects.nonNull(activity.getTimesRule())) {
                        switch (SpActivityTimesRuleEnum.formValue(activity.getTimesRule())){
                            case RULE_LEVEL_0: // 每日一次
                                time = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate()); // 当天23：59：59过期
                                redisService.setCacheObject(
                                        RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), tor.getBranchId()), StringPool.ONE, time, TimeUnit.SECONDS
                                );
                                break;
                            case RULE_LEVEL_1: // 活动期间内仅一次
                                // 标记活动已经达到限制
                                redisService.setCacheObject(
                                        RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), tor.getBranchId()), StringPool.ONE, time, TimeUnit.SECONDS
                                );
                                break;
                            case RULE_LEVEL_4: // 商品级别每日一次
                                time = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate()); // 商品活动缓存过期时间 调整为当天23：59：59过期
                                // 标记活动已经达到限制
                                redisService.setCacheObject(
                                        RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), tor.getBranchId()),
                                        StringPool.ONE,
                                        time,
                                        TimeUnit.SECONDS
                                );
                                break;
                            case RULE_LEVEL_5: // 商品级别仅一次
                                // 标记活动已经达到限制
                                redisService.setCacheObject(
                                        RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), tor.getBranchId()),
                                        StringPool.ONE,
                                        time,
                                        TimeUnit.SECONDS
                                );
                                break;
                        }
                    }
                    //  恢复已取消订单 特价活动商品门店已购数量
//                    redisActivityService.setSpSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), saleQty, null, null);

                    BigDecimal orderUnitQty = BigDecimal.valueOf(supplierOrderDtl.getOrderUnitQty());
                    if (UnitTypeEnum.UNIT_SMALL.getType().equals(supplierOrderDtl.getOrderUnitType())) {
                        redisActivityService.setSpSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQty, null, null);
                        //  恢复已取消订单 特价活动商品已购总数量（小单位）
                        redisActivityService.setSpSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQty, null, null);
                    } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(supplierOrderDtl.getOrderUnitType())) {
                        redisActivityService.setSpMidSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQty, null, null);
                        //  恢复已取消订单 特价活动商品已购总数量（中单位）
                        redisActivityService.setSpMidSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQty, null, null);
                    } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(supplierOrderDtl.getOrderUnitType())) {
                        redisActivityService.setSpLargeSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQty, null, null);
                        //  恢复已取消订单 特价活动商品已购总数量（大单位）
                        redisActivityService.setSpLargeSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQty, null, null);
                    }

                });
    }
}
