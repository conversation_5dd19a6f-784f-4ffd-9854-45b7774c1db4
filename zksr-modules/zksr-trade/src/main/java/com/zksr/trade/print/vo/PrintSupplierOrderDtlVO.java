package com.zksr.trade.print.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Description: 打印入驻商订单详情VO
 * @Author: liuxingyu
 * @Date: 2024/4/19 15:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PrintSupplierOrderDtlVO {
    /**
     * 入驻商订单明细id
     */
    private Long supplierOrderDtlId;

    /**
     * 商品SPU id
     */
    private Long spuId;

    /**
     * 商品sku id
     */
    private Long skuId;

    /**
     * 商品spu 名称
     */
    private String spuName;

    /**
     * 商品sku 名称
     */
    private String spkName;

    /**
     * 商品数量
     */
    private Long totalNum;

    /**
     * 原销售价
     */
    private BigDecimal salePrice;

    /**
     * 成交价
     */
    private BigDecimal price;

    /**
     * 合计金额（price*total_num）
     */
    private BigDecimal totalAmt;

    /**
     * 备注
     */
    private String memo;

    /**
     * 订单购买单位;最小单位的单位，中单位的单位，大单位的单位
     */
    private Integer orderUnit;

    /**
     * 订单购买单位数量;购买的是中单位，即为中单位数量。小单位、大单位同理
     */
    private Integer orderUnitQty;

    /**
     * 订单换算数量;小单位为1，中、大单位的换算数量
     */
    private Integer orderUnitSize;

    /**
     * 订单购买单位价格
     */
    private BigDecimal orderUnitPrice;

    /**
     * 发货前取消金额
     */
    private BigDecimal cancelAmt;

    /**
     * 发货前取消数量
     */
    private Integer cancelQty;
}
