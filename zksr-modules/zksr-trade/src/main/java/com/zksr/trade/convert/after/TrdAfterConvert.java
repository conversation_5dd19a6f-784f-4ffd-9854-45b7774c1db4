package com.zksr.trade.convert.after;

import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.enums.SpuPricingWayEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.partnerPolicy.dto.AfterSaleSettingPolicyDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.api.after.vo.OrderAfterResp;
import com.zksr.trade.api.after.vo.OrderAfterSaveRequest;
import com.zksr.trade.api.order.vo.AfterApproveDtlEditVO;
import com.zksr.trade.api.order.vo.AfterDtl;
import com.zksr.trade.api.order.vo.AfterDtlReqVO;
import com.zksr.trade.controller.after.dto.AfterDtlListDTO;
import com.zksr.trade.controller.after.dto.AfterDtlRespDTO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.controller.after.dto.AfterOrderRespDTO;
import com.zksr.trade.controller.after.vo.TrdAfterRespVO;
import com.zksr.trade.controller.after.vo.TrdAfterSaveReqVO;
import com.zksr.trade.api.express.TrdOrderExpress;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;

/**
* 售后单 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-20
*/
@Mapper
public interface TrdAfterConvert {

    TrdAfterConvert INSTANCE = Mappers.getMapper(TrdAfterConvert.class);

    TrdAfterRespVO convert(TrdAfter trdAfter);

    TrdAfter convert(TrdAfterSaveReqVO trdAfterSaveReq);

    PageResult<TrdAfterRespVO> convertPage(PageResult<TrdAfter> trdAfterPage);

    OrderAfterResp convert(OrderAfterSaveRequest request);

//    @Mappings({
//            @Mapping(source = "dtlListDTOS.branchId", target = "branchId")
//    })


    AfterDtlRespDTO convertResp0(AfterDtlListDTO dtlListDTO);

    @Mappings({
            @Mapping(source = "spuDTO.thumb", target = "thumb"),
            @Mapping(source = "spuDTO.spuNo", target = "spuNo"),
            @Mapping(source = "skuDTO.barcode", target = "barcode"),
            @Mapping(source = "dtlListDTO.skuId", target = "skuId")
    })
    AfterDtlRespDTO.AfterSupplier.AfterSupplierDtl convertResp2(AfterDtlListDTO dtlListDTO, SpuDTO spuDTO, SkuDTO skuDTO);


    default AfterDtlRespDTO convertResp(List<AfterDtlListDTO> dtlListDTOS, Set<SupplierDTO> supplierDTOS, Set<SpuDTO> spuDTOS, Set<SkuDTO> skuDTOS, AfterSaleSettingPolicyDTO policyDTO
                                        ,List<TrdOrderExpress> expressList) {
        // 取第一条数据 转换售后单主单信息
        AfterDtlRespDTO respDTO = convertResp0(dtlListDTOS.get(NumberPool.INT_ZERO));
        respDTO.setReturnAmtTotal(BigDecimal.ZERO);
        respDTO.setAfterSuppliers(new ArrayList<>());

        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, SysDictData> unitMap = Optional.ofNullable(dictCache).orElse(Collections.emptyList()).stream()
                .collect(Collectors.toMap(SysDictData::getDictValue, Function.identity()));

        Map<Long, List<TrdOrderExpress>> expressMap = expressList.stream().collect(Collectors.groupingBy(TrdOrderExpress::getSupplierOrderDtlId));
        Map<Long, SupplierDTO> supplierDTOMap = convertMap(supplierDTOS, SupplierDTO::getSupplierId);
        Map<Long, SpuDTO> spuDTOMap = convertMap(spuDTOS, SpuDTO::getSpuId);
        Map<Long, SkuDTO> skuDTOMap = convertMap(skuDTOS, SkuDTO::getSkuId);

        Map<Long, List<AfterDtlListDTO>> dtlListMap = dtlListDTOS.stream().collect(Collectors.groupingBy(AfterDtlListDTO::getSupplierId));
        dtlListMap.forEach((key, value) -> {

            SupplierDTO supplierDTO = supplierDTOMap.get(key);
            AfterDtlRespDTO.AfterSupplier afterSupplier = new AfterDtlRespDTO.AfterSupplier();
            respDTO.getAfterSuppliers().add(afterSupplier);

            // 取第一条数据
            AfterDtlListDTO afterDtlResp = value.get(NumberPool.INT_ZERO);
            afterSupplier.setSupplierId(key)
                    .setSupplierName(supplierDTO.getSupplierName())
                    .setSupplierAfterNo(afterDtlResp.getSupplierAfterNo())
                    .setSupplierContactName(supplierDTO.getContactName())
                    .setSupplierContactPhone(supplierDTO.getContactPhone())
                    .setSupplierContactAddress(supplierDTO.getContactAddress())
                    .setReturnAddr(afterDtlResp.getReturnAddr())
                    .setReturnPhone(afterDtlResp.getReturnPhone())
                    // 映射订单信息
                    .setOrderInfo(
                            convertOrderInfoResp(afterDtlResp)
                    )
            ;

            //校验 该售后订单的入驻商售后订单是否有对接了第三方的入驻商
            respDTO.setSyncFlag(ToolUtil.isEmptyReturn(supplierDTO.isSyncFlag(), Boolean.FALSE));

            // 如果退货地址没有, 使用入驻商默认信息
            // https://www.gitlink.org.cn/J13548784186/SaaS-B2B/issues/1743
            if (StringUtils.isEmpty(afterSupplier.getReturnAddr())) {
                afterSupplier.setReturnAddr(supplierDTO.getContactAddress());
            }
            if (StringUtils.isEmpty(afterSupplier.getReturnPhone())) {
                afterSupplier.setReturnPhone(supplierDTO.getContactPhone());
            }

            // 当入驻商收货信息配置不为空且 当前入驻商售后订单表中无数据时写入
            if (ToolUtil.isNotEmpty(policyDTO) && (ToolUtil.isEmpty(afterSupplier.getReturnAddr()) || ToolUtil.isEmpty(afterSupplier.getReturnPhone()))) {
                afterSupplier.setReturnAddr(policyDTO.getAfterSaleAddress())
                        .setReturnPhone(policyDTO.getAfterSalePhone());
            }


            afterSupplier.setAfterSupplierDtls(new ArrayList<>());
            value.forEach(afterDtl -> {

                SpuDTO spuDTO = spuDTOMap.get(afterDtl.getSpuId());
                SkuDTO skuDTO = skuDTOMap.get(afterDtl.getSkuId());
                List<TrdOrderExpress> expresses = expressMap.get(afterDtl.getSupplierAfterDtlId());
                AfterDtlRespDTO.AfterSupplier.AfterSupplierDtl afterSupplierDtl = convertResp2(afterDtl, spuDTO, skuDTO);

                String unitName = unitMap.getOrDefault(spuDTO.getMinUnit() + "", new SysDictData()).getDictLabel();
                String convertQty = "1" + unitMap.getOrDefault(afterDtl.getOrderUnit(), new SysDictData()).getDictLabel() + "=" + afterDtl.getOrderUnitSize() + unitName;

                afterSupplier.getAfterSupplierDtls().add(afterSupplierDtl);
                afterSupplierDtl.setProperties(PropertyAndValDTO.getProperties(afterSupplierDtl.getProperties()))
                        .setOrderCreateTime(respDTO.getOrderCreateTime())
                        .setOrderPayTime(respDTO.getOrderPayTime())
                        .setExpressesList(expresses)
                        .setPayWay(respDTO.getPayWay())
                        .setUnit(afterDtl.getReturnUnit())
                        .setConvertQty(convertQty)
                        .setUnitName(unitName)
                        .setReturnUnitPrice(afterDtl.getReturnPrice())
                        .setReturnUnitName(unitMap.getOrDefault(afterDtl.getReturnUnit(), new SysDictData()).getDictLabel())
                        .setPricingWay(ToolUtil.isEmptyReturn(spuDTO.getPricingWay(), SpuPricingWayEnum.ORDINARY.getType()))
                ;

                respDTO.setReturnAmtTotal(respDTO.getReturnAmtTotal().add(afterSupplierDtl.getReturnAmt()));
            });

        });
        return respDTO;
    }

    @Mappings({
            @Mapping(source = "afterDtlResp.orderNo", target = "orderNo"),
            @Mapping(source = "afterDtlResp.supplierOrderNo", target = "supplierOrderNo"),
            @Mapping(source = "afterDtlResp.orderCreateTime", target = "orderCreateTime"),
            @Mapping(source = "afterDtlResp.subOrderAmt", target = "subOrderAmt"),
            @Mapping(source = "afterDtlResp.subDiscountAmt", target = "subDiscountAmt"),
            @Mapping(source = "afterDtlResp.subPayAmt", target = "subPayAmt"),
            @Mapping(source = "afterDtlResp.payWay", target = "payWay"),
            @Mapping(source = "afterDtlResp.subPayBalanceAmt", target = "subPayBalanceAmt"),
    })
    @BeanMapping(ignoreByDefault = true)
    public AfterOrderRespDTO convertOrderInfoResp(AfterDtlListDTO afterDtlResp);

    default AfterApproveDtlEditVO batchAfterDtlEditVOResp(AfterDtlRespDTO afterDtlRespDTO, AfterDtlReqVO reqVO) {
        // 转换主单数据
        AfterApproveDtlEditVO afterApproveDtlEditVO = batchAfterDtlEditVOResp0(afterDtlRespDTO, reqVO);
        // 转换明细数据
        afterApproveDtlEditVO.setAfterDtlList(batchAfterDtlEditVOResp1(afterDtlRespDTO.getAfterSuppliers().get(NumberPool.INT_ZERO).getAfterSupplierDtls()));
        return afterApproveDtlEditVO;
    }

    @Mappings({
            @Mapping(source = "reqVO.productType", target = "productType"),
            @Mapping(source = "reqVO.handleState", target = "handleState"),
            @Mapping(source = "reqVO.afterId", target = "afterId"),
            @Mapping(source = "reqVO.source", target = "source"),
            @Mapping(source = "afterDtlRespDTO.afterType", target = "afterType"),
    })
    @BeanMapping(ignoreByDefault = true)
    public AfterApproveDtlEditVO batchAfterDtlEditVOResp0(AfterDtlRespDTO afterDtlRespDTO, AfterDtlReqVO reqVO);

    @Mappings({
            @Mapping(source = "afterDtlDTO.supplierAfterDtlId", target = "supplierAfterDtlId"),
            @Mapping(source = "afterDtlDTO.supplierAfterDtlNo", target = "supplierAfterDtlNo"),
            @Mapping(source = "afterDtlDTO.returnQty", target = "returnQty"),
            @Mapping(source = "afterDtlDTO.returnAmt", target = "returnAmt"),
            @Mapping(source = "afterDtlDTO.returnQty", target = "realityReturnQty"),
            @Mapping(source = "afterDtlDTO.returnAmt", target = "realityReturnAmt"),
    })
    @BeanMapping(ignoreByDefault = true)
    public List<AfterDtl> batchAfterDtlEditVOResp1(List<AfterDtlRespDTO.AfterSupplier.AfterSupplierDtl> afterDtlDTO);


}
