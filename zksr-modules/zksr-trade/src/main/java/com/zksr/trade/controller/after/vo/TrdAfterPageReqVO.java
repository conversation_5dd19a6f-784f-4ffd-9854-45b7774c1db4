package com.zksr.trade.controller.after.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 售后单对象 trd_after
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@ApiModel("售后单 - trd_after分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdAfterPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 售后单id */
    @ApiModelProperty(value = "支付平台手续费;从订单表 (pay_amt*pay_rate) 四舍五入")
    private Long afterId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 售后单编号 */
    @Excel(name = "售后单编号")
    @ApiModelProperty(value = "售后单编号", required = true)
    private String afterNo;

    /** 运营商id */
    @Excel(name = "运营商id")
    @ApiModelProperty(value = "运营商id")
    private Long dcId;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty(value = "用户id")
    private Long memberId;

    /** 业务员id */
    @Excel(name = "业务员id")
    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    /** 业务员级别 */
    @Excel(name = "业务员级别")
    @ApiModelProperty(value = "业务员级别")
    private Long colonelLevel;

    /** 父业务员id */
    @Excel(name = "父业务员id")
    @ApiModelProperty(value = "父业务员id")
    private Long pcolonelId;

    /** 父业务员级别 */
    @Excel(name = "父业务员级别")
    @ApiModelProperty(value = "父业务员级别")
    private Long pcolonelLevel;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 退货原因 */
    @Excel(name = "退货原因")
    @ApiModelProperty(value = "退货原因")
    private String reason;

    /** 退款金额 */
    @Excel(name = "退款金额")
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmt;

    /** 退货说明 */
    @Excel(name = "退货说明")
    @ApiModelProperty(value = "退货说明")
    private String descr;

    /** 订单退款金额;包含本次售后单和之前未取消的售后单的退款金额之和 */
    @Excel(name = "订单退款金额;包含本次售后单和之前未取消的售后单的退款金额之和")
    @ApiModelProperty(value = "订单退款金额;包含本次售后单和之前未取消的售后单的退款金额之和")
    private BigDecimal orderRefundAmt;

    /** 订单支付金额;从订单表 */
    @Excel(name = "订单支付金额;从订单表")
    @ApiModelProperty(value = "订单支付金额;从订单表")
    private BigDecimal payAmt;

    /** 退款方式（数据字典）;从订单表 0-在线支付 1-储值支付 2-货到付款 */
    @Excel(name = "退款方式", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "退款方式")
    private String payway;

    /** 支付平台(数据字典);从订单表 */
    @Excel(name = "支付平台(数据字典);从订单表")
    @ApiModelProperty(value = "支付平台(数据字典);从订单表")
    private String platform;

    /** 订单支付时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "订单支付时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "订单支付时间")
    private Date payTime;

    /** 退货完成时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "退货完成时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "退货完成时间")
    private Date returnTime;

    /** 退款完成时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "退款完成时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "退款完成时间")
    private Date refundTime;

    /** 是否已取消 0未取消 1已取消 */
    @Excel(name = "是否已取消 0未取消 1已取消")
    @ApiModelProperty(value = "是否已取消 0未取消 1已取消")
    private Long isCancel;

    /** 来源(数据字典);1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序 */
    @Excel(name = "来源(数据字典);1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序")
    @ApiModelProperty(value = "来源(数据字典);1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序")
    private Long source;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 申请时上传的凭证照片 */
    @Excel(name = "申请时上传的凭证照片")
    @ApiModelProperty(value = "申请时上传的凭证照片")
    private String applyImgs;

    /** 回填快递单时上传的凭证照片 */
    @Excel(name = "回填快递单时上传的凭证照片")
    @ApiModelProperty(value = "回填快递单时上传的凭证照片")
    private String expressImgs;

    /** 支付公司收取的支付费率;从订单表 */
    @Excel(name = "支付公司收取的支付费率;从订单表")
    @ApiModelProperty(value = "支付公司收取的支付费率;从订单表")
    private BigDecimal payRate;

    /** 支付平台手续费;从订单表 (pay_amt*pay_rate) 四舍五入 */
    @Excel(name = "支付平台手续费;从订单表 (pay_amt*pay_rate) 四舍五入")
    @ApiModelProperty(value = "支付平台手续费;从订单表 (pay_amt*pay_rate) 四舍五入")
    private BigDecimal payFee;


}
