package com.zksr.trade.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.OrderPayWayEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayWayEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.sysconfig.GlobalPayConfigDTO;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveReqVO;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveRespVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayPageReqVO;
import com.zksr.trade.domain.*;
import com.zksr.trade.mapper.*;
import com.zksr.trade.service.ITrdHdfkPayService;
import com.zksr.trade.service.ITrdOrderService;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.payWay.ITrdOrderPayWayHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 货到付款付款单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Service
public class TrdHdfkPayServiceImpl implements ITrdHdfkPayService {

    @Autowired
    private TrdHdfkPayMapper trdHdfkPayMapper;

    @Autowired
    private TrdSupplierOrderDtlMapper supplierOrderDtlMapper;

    @Autowired
    private TrdHdfkSettleMapper trdHdfkSettleMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private TrdHdfkPayDtlMapper trdHdfkPayDtlMapper;

    @Autowired
    private TrdSettleMapper trdSettleMapper;

    @Resource
    private AccountApi accountApi;

    @Resource
    private PlatformMerchantApi platformMerchantApi;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private List<ITrdOrderPayWayHandlerService> trdOrderPayWayHandlerServices;

    /**
     * 后台直接新增
     * @param createReqVO
     * @return
     */
    @Override
    @Transactional
    @DistributedLock(lockName = RedisLockConstants.LOCK_ADMIN_HDFK_CLEAR)
    public Long insertTrdHdfkPay(HdfkPaySaveReqVO createReqVO) {
        createReqVO.setPayWay(PayWayEnum.OFFLINE.getPayWay());
        createReqVO.setPlatform(PayChannelEnum.NONE.getCode());
        createReqVO.setPaySource(NumberPool.INT_ONE);

        // B2B支付不允许清账
        List<TrdSupplierOrderDtl> trdSupplierOrderDtls = supplierOrderDtlMapper.selectBatchIds(createReqVO.getSupplierOrderDtlIdList());
        for (TrdSupplierOrderDtl orderDtl : trdSupplierOrderDtls) {
            if (PayChannelEnum.isB2b(orderDtl.getPlatform())) {
                // 线下清账不支持微信B2B支付方式, 微信B2B支付请使用在线收款
                throw exception(TRD_HDFK_NOT_SUPPORT_B2B_PAY);
            }
        }

        // 按照门店分组
        List<TrdHdfkSettle> settleList = trdHdfkSettleMapper.selectByPayOrderDtlId(createReqVO.getSupplierOrderDtlIdList());
        for (TrdHdfkSettle trdHdfkSettle : settleList) {
            OpensourceDto opensourceByMerchantId = trdCacheService.getOpensourceByMerchantId(trdHdfkSettle.getSupplierId());
            if(ToolUtil.isNotEmpty(opensourceByMerchantId) && opensourceByMerchantId.getIsHdfkSettle() == NumberPool.INT_ONE){
                throw exception(TRD_HDFK_SUPPLIER_NOT_B2B_SETTLE);
            }
        }
        if (settleList.isEmpty()) {
            throw exception(TRD_HDFK_CLEAR_SETTLES_NULL);
        }

        // 门店分组
        Map<Long, List<TrdHdfkSettle>> branchMap = settleList.stream().collect(Collectors.groupingBy(TrdHdfkSettle::getBranchId));
        branchMap.forEach((branchId, settles) -> {
            createReqVO.setSupplierOrderDtlIdList(settles.stream().map(TrdHdfkSettle::getSupplierOrderDtlId).distinct().collect(Collectors.toList()));
            HdfkPaySaveRespVO respVO = this.createPay(createReqVO);
            this.orderPaySuccessCallback(respVO.getOrderNo(), createReqVO.getPlatform(), createReqVO.getPayWay());
        });
        return NumberPool.LONG_ONE;
    }

    /**
     * 第三方调用新增货到收款单
     * @param createReqVO
     * @return
     */
    @Override
    @Transactional
    @DistributedLock(lockName = RedisLockConstants.LOCK_THIRD_PARTY_HDFK_CLEAR)
    public Long saveThirdPartyTrdHdfkPay(HdfkPaySaveReqVO createReqVO) {
        createReqVO.setPayWay(PayWayEnum.OFFLINE.getPayWay());
        createReqVO.setPlatform(PayChannelEnum.NONE.getCode());
        createReqVO.setPaySource(NumberPool.INT_ONE);

        // B2B支付不允许清账
        List<TrdSupplierOrderDtl> trdSupplierOrderDtls = supplierOrderDtlMapper.selectBatchIds(createReqVO.getSupplierOrderDtlIdList());
        for (TrdSupplierOrderDtl orderDtl : trdSupplierOrderDtls) {
            if (PayChannelEnum.isB2b(orderDtl.getPlatform())) {
                // 线下清账不支持微信B2B支付方式, 微信B2B支付请使用在线收款
                throw exception(TRD_HDFK_NOT_SUPPORT_B2B_PAY);
            }
        }

        // 按照门店分组
        List<TrdHdfkSettle> settleList = trdHdfkSettleMapper.selectByPayOrderDtlId(createReqVO.getSupplierOrderDtlIdList());
        if (settleList.isEmpty()) {
            throw exception(TRD_HDFK_CLEAR_SETTLES_NULL);
        }

        // 门店分组
        Map<Long, List<TrdHdfkSettle>> branchMap = settleList.stream().collect(Collectors.groupingBy(TrdHdfkSettle::getBranchId));
        branchMap.forEach((branchId, settles) -> {
            createReqVO.setSupplierOrderDtlIdList(settles.stream().map(TrdHdfkSettle::getSupplierOrderDtlId).distinct().collect(Collectors.toList()));
            HdfkPaySaveRespVO respVO = this.createPay(createReqVO);
            this.orderPaySuccessCallback(respVO.getOrderNo(), createReqVO.getPlatform(), createReqVO.getPayWay());
        });
        return NumberPool.LONG_ONE;
    }

    @Override
    @Transactional
    public HdfkPaySaveRespVO createPay(HdfkPaySaveReqVO paySaveReqVO) {
        List<Long> supplierOrderDtlIdList = paySaveReqVO.getSupplierOrderDtlIdList();

        List<TrdHdfkSettle> settleList = trdHdfkSettleMapper.selectByPayOrderDtlId(supplierOrderDtlIdList);
        if (settleList.isEmpty()) {
            throw exception(TRD_HDFK_CLEAR_SETTLES_NULL);
        }

        TrdHdfkSettle hdfkSettle = settleList.get(0);
        BigDecimal totalAmt = settleList.stream().map(TrdHdfkSettle::getSettleAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 查询商品明细下单数据和售后数据 ，计算收款金额
//        List<TrdHdfkOrderItemVO> itemListData = trdHdfkSettleMapper.selectHdfkOrderItemListByOrderDtlIds(supplierOrderDtlIdList);
//        BigDecimal totalAmt = itemListData.stream()
//                .map(hdfkOrderItem -> {
//                    long itemNum = hdfkOrderItem.getTotalNum() - hdfkOrderItem.getAfterTotalNum();
//                    return hdfkOrderItem.getExactPrice().multiply(BigDecimal.valueOf(itemNum));
//                }).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 新增支付
        TrdHdfkPay hdfkPay = TrdHdfkPay.builder()
                .payAmt(totalAmt)
                .branchId(hdfkSettle.getBranchId())
                .hdfkPayNo("DF" + redisService.getUniqueNumber())
                .payAmt(totalAmt)
                .sysCode(paySaveReqVO.getSysCode())
                .paySource(paySaveReqVO.getPaySource())
                .tips(paySaveReqVO.getTips())
                .voucher(paySaveReqVO.getVoucher())
                .build();

        if (StringUtils.isNotEmpty(paySaveReqVO.getPlatform()) &&
                PayChannelEnum.NONE.getCode().equals(paySaveReqVO.getPlatform())) {
            hdfkPay.setPlatform(paySaveReqVO.getPlatform());
            hdfkPay.setPayRate(BigDecimal.ZERO);
            hdfkPay.setPayFee(BigDecimal.ZERO);
        } else {
            // 获取支付平台
            PayConfigDTO payConfigDTO = trdCacheService.getPayConfigDTO(paySaveReqVO.getSysCode());
            // 获取支付平台手续费
            GlobalPayConfigDTO globalPayConfig = redisSysConfigService.getGlobalPayConfig(payConfigDTO.getStoreOrderPayPlatform());
            if (Objects.isNull(globalPayConfig)) {
                throw exception(GLOBAL_PAY_CONFIG_NOT_EXIST);
            }
            hdfkPay.setPayRate(globalPayConfig.getPay().getFreeRate());
            hdfkPay.setPlatform(payConfigDTO.getStoreOrderPayPlatform());
            hdfkPay.setPayFee(totalAmt.multiply(globalPayConfig.getPay().getFreeRate()).setScale(2, RoundingMode.HALF_UP));
            // 手续费最低一分钱
            if (NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, hdfkPay.getPayFee())) {
                hdfkPay.setPayFee(new BigDecimal("0.01"));
            }
            if (NumberUtil.isGreaterOrEqual(hdfkPay.getPayFee(), totalAmt)) {
                throw exception(FREE_GT_PAY_AMT);
            }
        }

        trdHdfkPayMapper.insert(hdfkPay);
        // 分账信息
        List<HdfkPaySaveRespVO.OrderSettlementDTO> settlementDTOS = new ArrayList<>();
        BigDecimal temFee = hdfkPay.getPayFee();

        Map<Long, List<TrdHdfkSettle>> supplierMap = settleList.stream().collect(Collectors.groupingBy(TrdHdfkSettle::getSupplierId));
        ArrayList<Long> supplierIdList = ListUtil.toList(supplierMap.keySet());

        // 非下单支付收款
        if (!PayChannelEnum.NONE.getCode().equals(hdfkPay.getPlatform())) {

            // 查询订单结算信息表，用余B2B微信支付在线分账信息
            List<TrdSettle> settles = trdSettleMapper.getSettleListBySupplierOrderDtlIds(supplierOrderDtlIdList);

            // 获取支付平台
            PayConfigDTO payConfigDTO = trdCacheService.getPayConfigDTO(paySaveReqVO.getSysCode());

            for (int line = 0; line < supplierIdList.size(); line++) {
                Long supplierId = supplierIdList.get(line);
                List<TrdHdfkSettle> hdfkSettles = supplierMap.get(supplierId);
                AccAccountDTO accountDTO = accountApi.getSupplierAccount(supplierId).getCheckedData();
                if (Objects.isNull(accountDTO.getPlatformAccount()) || StringUtils.isEmpty(accountDTO.getPlatformAccount().getAltMchNo())) {
                    throw exception(NOT_EXIST_SUPPLIER_MERCHANT);
                }
                BigDecimal supplierAmt = hdfkSettles.stream().map(TrdHdfkSettle::getSettleAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal itemFree = supplierAmt.divide(totalAmt, 8, RoundingMode.HALF_UP).multiply(temFee).setScale(2, RoundingMode.HALF_UP);
                if (NumberUtil.isGreater(itemFree, temFee)) {
                    itemFree = temFee;
                }
                if (line == supplierIdList.size() - 1) {
                    itemFree = temFee;
                }
                temFee = temFee.subtract(itemFree);

                // 订单支付分账信息处理
                getPayWayHandlerService(payConfigDTO.getStoreOrderPayPlatform(), OrderPayWayEnum.HDFK.getPayWay()).orderHdfkSettleAccountInfo(accountDTO, supplierId, supplierAmt, itemFree, settles, payConfigDTO, settlementDTOS);

//                HdfkPaySaveRespVO.OrderSettlementDTO orderSttlementDTO =new HdfkPaySaveRespVO.OrderSettlementDTO(
//                        accountDTO.getPlatformAccount().getAltMchNo(),
//                        supplierAmt.subtract(itemFree),
//                        supplierId,
//                        MerchantTypeEnum.SUPPLIER.getType()
//                );
//                settlementDTOS.add(orderSttlementDTO);
//
//                // 兼容合并订单结算分账信息，用于B2B微信支付, 过滤掉结算为0 的数据
//                if (Objects.nonNull(payConfigDTO) && PayChannelEnum.isB2b(payConfigDTO.getStoreOrderPayPlatform())) {
//                    settles.stream()
//                            .filter(settle -> Objects.equals(supplierId, settle.getSupplierId()))
//                            .collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
//                            .forEach((merchant, merchantSettleList) -> {
//                                // 商户Id
//                                Long merchantId = merchantSettleList.get(NumberPool.INT_ZERO).getMerchantId();
//                                // 商户类型
//                                String merchantType = merchantSettleList.get(NumberPool.INT_ZERO).getMerchantType();
//
//                                // 获取支付配置
//                                PlatformMerchantDTO merchantDTO = platformMerchantApi.getPlatformMerchant(merchantType, merchantId, PayChannelEnum.WX_B2B_PAY.getCode()).getCheckedData();
//
//                                BigDecimal settleAmt = merchantSettleList.stream().map(TrdSettle::getSettleAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
//                                HdfkPaySaveRespVO.OrderSettlementDTO orderSttlement =new HdfkPaySaveRespVO.OrderSettlementDTO(
//                                        ToolUtil.isEmpty(merchantDTO) ? null : merchantDTO.getAltMchNo(), // 这里第三方商户号未查询到默认为null
//                                        settleAmt,
//                                        merchantId,
//                                        merchantType
//                                );
//                                if (settleAmt.compareTo(BigDecimal.ZERO) > NumberPool.INT_ZERO) {
//                                    settlementDTOS.add(orderSttlement);
//                                    // 入驻商分账金额 - 分账方金额
//                                    orderSttlementDTO.setAmt(orderSttlementDTO.getAmt().subtract(settleAmt));
//                                }
//                            });
//                }
            }
        }

        // 保存支付批量的结算
        List<TrdHdfkPayDtl> trdHdfkPayDtls = settleList.stream().map(item ->
                TrdHdfkPayDtl.builder()
                        .hdfkPayId(hdfkPay.getHdfkPayId())
                        .hdfkSettleId(item.getHdfkSettleId())
                        .settleAmt(item.getSettleAmt())
                        .sysCode(item.getSysCode())
                        .build()
        ).collect(Collectors.toList());
        trdHdfkPayDtlMapper.insertBatch(trdHdfkPayDtls);

        return HdfkPaySaveRespVO.builder()
                .orderNo(hdfkPay.getHdfkPayNo())
                .busiId(hdfkPay.getHdfkPayId())
                .payAmt(hdfkPay.getPayAmt())
                .settlementDTOS(settlementDTOS)
                .build();
    }

    @Override
    @Transactional
    @DistributedLock(prefix = RedisLockConstants.LOCK_HDFK_ORDER, condition = "#orderNo", tryLock = true)
    public void orderPaySuccessCallback(String orderNo, String payPlatform, String payWay) {
        TrdHdfkPay hdfkPay = trdHdfkPayMapper.selectByPayNo(orderNo);
        if (hdfkPay.getPayState() == NumberPool.INT_ONE) {
            return;
        }
        TrdHdfkPay update = TrdHdfkPay.builder()
                .hdfkPayId(hdfkPay.getHdfkPayId())
                .payTime(new Date())
                .platform(payPlatform)
                .payState(NumberPool.INT_ONE)
                .payWay(payWay)
                .build();
        // 解除门店欠款
        List<TrdHdfkPayDtl> hdfkPayDtls = trdHdfkPayDtlMapper.selectByPayId(hdfkPay.getHdfkPayId());
        Map<Long, TrdHdfkPayDtl> trdHdfkPayDtlMap = hdfkPayDtls.stream().collect(Collectors.toMap(TrdHdfkPayDtl::getHdfkSettleId, item -> item));
        // 设置货到付款详情数据
        List<TrdHdfkSettle> trdHdfkSettles = trdHdfkSettleMapper.selectBatchIds(hdfkPayDtls.stream().map(TrdHdfkPayDtl::getHdfkSettleId).collect(Collectors.toList()));
        //判断订单入驻商是否开启了货到付款清账功能
        OpensourceDto opensourceDto = trdCacheService.getOpensourceByMerchantId(trdHdfkSettles.get(0).getSupplierId());
        if(ToolUtil.isNotEmpty(opensourceDto)){
            if(opensourceDto.getIsHdfkSettle() == 1){
                update.setPayState(NumberPool.INT_TWO);
            }
        }
        trdHdfkPayMapper.updateById(update);
        ArrayList<TrdHdfkSettle> updateSettles = new ArrayList<>();
        trdHdfkSettles.forEach(item -> {
            item.setHdfkPayNo(hdfkPay.getHdfkPayNo());
            if (trdHdfkPayDtlMap.containsKey(item.getHdfkSettleId())) {
                item.setFdfkPayDtlId(trdHdfkPayDtlMap.get(item.getHdfkSettleId()).getHdfkPayDtlId());
            }
            updateSettles.add(
                    TrdHdfkSettle.builder()
                            .hdfkSettleId(item.getHdfkSettleId())
                            .fdfkPayDtlId(item.getFdfkPayDtlId())
                            .hdfkPayNo(hdfkPay.getHdfkPayNo())
                            .build()
            );
        });
        // 更新结算数据
        if (!updateSettles.isEmpty()) {
            trdHdfkSettleMapper.updateBatch(updateSettles);
        }

        // 判断门店还需要解除多少欠款
        Map<Long, List<TrdHdfkSettle>> supplierOrderDtlMap = trdHdfkSettles.stream().collect(Collectors.groupingBy(TrdHdfkSettle::getSupplierOrderDtlId));
        List<AccAccountFlowDTO> flowList = new ArrayList<>();
        supplierOrderDtlMap.forEach((supplierOrderDtlId, list) -> {
            // 需要结算的零不操作
            BigDecimal settleAmt = list.stream().map(TrdHdfkSettle::getSettleAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, settleAmt)) {
                return;
            }
            // 把余额加回来
            AccAccountFlowDTO flowDTO = AccAccountFlowDTO.builder()
                    .sysCode(hdfkPay.getSysCode())
                    .busiWithdrawableAmt(settleAmt)
                    .busiType(AccountBusiType.BRANCH_HDFK.getType())
                    .busiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField())
                    .merchantType(MerchantTypeEnum.BRANCH_DEBT.getType())
                    .merchantId(hdfkPay.getBranchId())
                    .platform(PayChannelEnum.NONE.getCode())
                    .busiId(supplierOrderDtlId)
                    .build();
            flowList.add(flowDTO);
        });
        // 货到付款处理入驻商订单明细状态变更
        getTrdOrderService().hdfkOrderPaySuccessUpdateStatus(supplierOrderDtlMap.keySet(), payPlatform, update.getHdfkPayId(), update.getPayWay());

        // 保存货到付款订单付款流水 并结算
        accountApi.saveAccountFlowAndProcess(flowList).checkError();
    }

    @Override
    public PageResult<TrdHdfkPay> getTrdHdfkPayPage(TrdHdfkPayPageReqVO pageReqVO) {
        return trdHdfkPayMapper.selectPage(pageReqVO);
    }

    @Override
    public TrdHdfkPay getTrdHdfkPay(Long hdfkPayId) {
        return trdHdfkPayMapper.selectById(hdfkPayId);
    }

    @Override
    public List<TrdHdfkPay> getTrdHdfkPayListByIds(List<Long> hdfkPayIdList) {
        return trdHdfkPayMapper.selectBatchIds(hdfkPayIdList);
    }

    ITrdOrderService getTrdOrderService() {
        return SpringUtils.getBean(ITrdOrderService.class);
    }

    /**
     * 获取支付方式处理器
     * @param platform
     * @return
     */
    private ITrdOrderPayWayHandlerService getPayWayHandlerService(String platform, String payWay) {
        return trdOrderPayWayHandlerServices.stream().filter(handler -> handler.isPlatform(platform, payWay))
                .findFirst().orElse(trdOrderPayWayHandlerServices.get(0));
    }
}
