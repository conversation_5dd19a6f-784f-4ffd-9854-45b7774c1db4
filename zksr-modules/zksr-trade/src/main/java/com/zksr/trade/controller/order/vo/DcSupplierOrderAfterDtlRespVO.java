package com.zksr.trade.controller.order.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/11/16 11:06
 * 订单明细商品售后返回实体
 */
@ApiModel("PC端订单明细查询页面返回实体")
@Data
public class DcSupplierOrderAfterDtlRespVO {

    @ApiModelProperty("差异类型(b2b)")
    private String afterType;

    @ApiModelProperty("差异类型(外部)")
    private String afterOrderType;

    @ApiModelProperty("退货单位")
    private String afterUnit;

    @ApiModelProperty("退货单位名称")
    private String unitName;

    @ApiModelProperty("退货数量")
    private Long afterNum;

    @ApiModelProperty("退货金额")
    private BigDecimal afterAmt;
}
