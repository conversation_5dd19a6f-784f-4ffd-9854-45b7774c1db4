package com.zksr.trade.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdAfterDiscountDtlMapper;
import com.zksr.trade.domain.TrdAfterDiscountDtl;
import com.zksr.trade.service.ITrdAfterDiscountDtlService;

import java.util.List;


/**
 * 售后优惠明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
@Service
public class TrdAfterDiscountDtlServiceImpl implements ITrdAfterDiscountDtlService {
    @Autowired
    private TrdAfterDiscountDtlMapper trdAfterDiscountDtlMapper;

    /**
     * 新增售后优惠明细
     *
     * @param trdAfterDiscountDtl 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdAfterDiscountDtl(TrdAfterDiscountDtl trdAfterDiscountDtl) {
        // 插入
        trdAfterDiscountDtlMapper.insert(trdAfterDiscountDtl);
        // 返回
        return trdAfterDiscountDtl.getAfterDiscountDtlId();
    }

    @Override
    public void insertBatchTrdAfterDiscountDtl(List<TrdAfterDiscountDtl> trdAfterDiscountDtls) {
        trdAfterDiscountDtlMapper.insertBatch(trdAfterDiscountDtls);
    }


    private void validateTrdAfterDiscountDtlExists(Long afterDiscountDtlId) {
        if (trdAfterDiscountDtlMapper.selectById(afterDiscountDtlId) == null) {
//            throw exception(TRD_AFTER_DISCOUNT_DTL_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 售后优惠明细 TODO 补充编号 ==========
    // ErrorCode TRD_AFTER_DISCOUNT_DTL_NOT_EXISTS = new ErrorCode(TODO 补充编号, "售后优惠明细不存在");


}
