package com.zksr.trade.mapper;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.domain.vo.openapi.TradeDetail;
import com.zksr.common.core.domain.vo.openapi.TradeOutErpRequest;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncAfterOrderCallDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncOrderCallDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncOrderPageReqDTO;
import com.zksr.common.core.enums.PayStateEnum;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.elasticsearch.domain.EsStoreProduct;
import com.zksr.common.core.domain.vo.openapi.CustomApiDetail;
import com.zksr.common.core.domain.vo.openapi.CustomApiMaster;
import com.zksr.report.api.homePages.dto.HomePagesCurrentSalesDataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesOrderSalesDataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.trade.api.order.vo.SupplierOrderCountPageReqVO;
import com.zksr.trade.api.order.vo.SupplierOrderCountRespVO;
import com.zksr.system.api.supplier.vo.SupplierOrderVO;
import com.zksr.trade.api.after.dto.BranchSkuMergeAfterResDTO;
import com.zksr.trade.api.after.vo.BranchSkuMergeAfterReqVO;
import com.zksr.trade.api.order.dto.*;
import com.zksr.trade.api.order.vo.ColonelOrderDaySettleVO;
import com.zksr.trade.api.after.dto.OrderAfterDtlDTO;
import com.zksr.trade.api.after.vo.OrderAfterRequest;
import com.zksr.trade.api.order.vo.*;
import com.zksr.trade.controller.app.vo.BrandHomePageRespVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListInfoRespVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListReqVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListRespVO;
import com.zksr.trade.controller.order.vo.*;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.print.vo.PrintSupplierOrderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 订单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Mapper
public interface TrdOrderMapper extends BaseMapperX<TrdOrder> {
    default PageResult<TrdOrder> selectPage(TrdOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdOrder>()
                .eqIfPresent(TrdOrder::getOrderId, reqVO.getOrderId())
                .eqIfPresent(TrdOrder::getSysCode, reqVO.getSysCode())
                .eqIfPresent(TrdOrder::getDcId, reqVO.getDcId())
                .eqIfPresent(TrdOrder::getAreaId, reqVO.getAreaId())
                .eqIfPresent(TrdOrder::getMemberId, reqVO.getMemberId())
                .eqIfPresent(TrdOrder::getColonelId, reqVO.getColonelId())
                .eqIfPresent(TrdOrder::getColonelLevel, reqVO.getColonelLevel())
                .eqIfPresent(TrdOrder::getPcolonelId, reqVO.getPcolonelId())
                .eqIfPresent(TrdOrder::getPcolonelLevel, reqVO.getPcolonelLevel())
                .eqIfPresent(TrdOrder::getBranchId, reqVO.getBranchId())
                .eqIfPresent(TrdOrder::getLongitude, reqVO.getLongitude())
                .eqIfPresent(TrdOrder::getLatitude, reqVO.getLatitude())
                .eqIfPresent(TrdOrder::getBranchAddr, reqVO.getBranchAddr())
                .eqIfPresent(TrdOrder::getPayState, reqVO.getPayState())
                .eqIfPresent(TrdOrder::getPlatform, reqVO.getPlatform())
                .eqIfPresent(TrdOrder::getPayWay, reqVO.getPayWay())
                .eqIfPresent(TrdOrder::getPayTime, reqVO.getPayTime())
                .eqIfPresent(TrdOrder::getDiscountAmt, reqVO.getDiscountAmt())
                .eqIfPresent(TrdOrder::getMemo, reqVO.getMemo())
                .eqIfPresent(TrdOrder::getDelFlag, reqVO.getDelFlag())
                .orderByDesc(TrdOrder::getOrderId));
    }

    /**
     * 根据单号查询订单信息
     *
     * @param reqVO
     * @return
     */
    default TrdOrder getOrderByOrderNo(TrdOrderPageReqVO reqVO) {
        return selectOne(new LambdaQueryWrapperX<TrdOrder>()
                .eqIfPresent(TrdOrder::getOrderNo, reqVO.getOrderNo()));
    }

    // TODO 目前只是用户端商城小程序使用

    /**
     * @Description: 分页获取订单数据(获取所有数据)
     * @Author: chenmingqing
     * @Date: 2025/3/26 14:44
     */
    List<TrdOrderMiniRespDTO> selectPageAll(@Param("orderPageReqVO") TrdOrderPageReqDTO orderPageReqVO);

    /**
     * 新逻辑，订单状态取入驻商订单头表
     * @param orderPageReqVO
     * @return
     */
    List<TrdOrderMiniRespDTO> selectPageAllNew2(@Param("orderPageReqVO") TrdOrderPageReqDTO orderPageReqVO);

    /**
     * 新逻辑，订单状态取入驻商订单头表
     * @param orderPageReqVO
     * @return
     */
    List<TrdOrderMiniHeadRespDTO> selectPageAllNew3(@Param("orderPageReqVO") TrdOrderPageReqDTO orderPageReqVO);


    /**
     * @Description: 获取入驻商小程序订单分页列表
     * @Author: liuxingyu
     * @Date: 2024/4/8 11:23
     */
    Page<MiniProgramRespVO> getMerchantMiniProgramOrderPageList(@Param("orderPageReqVO") TrdOrderPageReqDTO trdOrderPageReqDTO,
                                                                @Param("supplierId") Long supplierId,
                                                                @Param("page") Page<TrdOrderPageReqDTO> page);

    /**
     * @Description: 获取运营商订单管理
     * @Author: liuxingyu
     * @Date: 2024/4/9 16:13
     */
    Page<DcOrderPageRespVO> getOperatorOrderPageList(@Param("page") Page<DcOrderPageReqVO> page,
                                                     @Param("param") DcOrderPageReqVO param,
                                                     @Param("supplierIds") List<Long> supplierIds,
                                                     @Param("spuIds") List<Long> spuIds,
                                                     @Param("skuIds") List<Long> skuIds,
                                                     @Param("branchIds") List<Long> branchIds,
                                                     @Param("colonelIds") List<Long> colonelIds);

    /**
     * @Description: 获取运营商订单管理列表总数（新）
     * @param orderPageReqVO
     * @return
     */
    Long selectOrderPageListNewCount(@Param("param") DcOrderPageReqVO param,
                                     @Param("spuIds") List<Long> spuIds,
                                     @Param("skuIds") List<Long> skuIds);

    /**
     * @Description: 获取运营商订单管理（新）
     * @param param
     * @param spuIds
     * @param skuIds
     * @return
     */
    List<DcSupplierOrderPageRespVO> getOperatorOrderPageListNew(@Param("param") DcOrderPageReqVO param,
                                                                @Param("spuIds") List<Long> spuIds,
                                                                @Param("skuIds") List<Long> skuIds);


    /**
     * 根据条件获取可售后的订单数据
     *
     * @param request
     */
    List<OrderAfterDtlDTO> getOrderAfterInfo(OrderAfterRequest request);

    /**
     * 根据条件获取门店合单售后商品列表
     *
     * @param reqVo
     */
    List<BranchSkuMergeAfterResDTO> branchSkuMergeAfterList(BranchSkuMergeAfterReqVO reqVo);

    /**
     * @Description: 打印获取订单信息
     * @Author: liuxingyu
     * @Date: 2024/4/19 16:00
     */
    List<PrintSupplierOrderVO> printGetByOrderId(@Param("orderId") Long orderId, @Param("sysCode") Long sysCode, @Param("supplierOrderId") Long supplierOrderId);


    /**
     * 获取门店待付款、待发货、待收货、已收货 状态订单数量
     *
     * @param
     * @return
     */
    OrderStatusVO getOrderStatus(OrderStatusReqVO reqVO);

    /**
     * 获取当月在途金额、收货金额、退货金额
     *
     * @param branchId 门店ID
     * @return
     */
    OrderAmountStatisticsVO getBranchMonthOrderAmount(@Param("branchId") Long branchId,
                                @Param("beginTime") Date beginTime,
                                @Param("endTime") Date endTime);

    Integer getSaleAfterNum(OrderStatusReqVO reqVO);

    /**
     *  业务员app订单列表 汇总数据
     * @param memColonelAppOrderListPageReqVO
     * @return
     */
    ColonelAppOrderListTotalDTO selectMemColoneAppOrderListTotal(@Param("reqVO") TrdColonelAppOrderListPageReqVO memColonelAppOrderListPageReqVO);


    /**
     * @Description: 业务员app订单列表
     * @Author: liuxingyu
     * @Date: 2024/4/26 15:00
     */
    Page<TrdColonelAppOrderListRespVO> selectMemColoneAppOrder(@Param("reqVO") TrdColonelAppOrderListPageReqVO memColonelAppOrderListPageReqVO, @Param("page") Page<TrdColonelAppOrderListPageReqVO> page, @Param("colonelIds") List<Long> colonelIds);

    /**
     * @Description: 获取业务员App订单 (新)
     * @Author: 陈明清
     * @Date: 2025/1/9 18:18
     */
    List<TrdOrderRespDTO> selectMemColoneAppOrderNew(TrdColonelAppOrderListPageReqVO reqVO);

    /**
     * @Description: 获取门店商品下单信息
     * @Author: liuxingyu
     * @Date: 2024/4/29 10:55
     */
    List<EsStoreProduct> getListByBranch(@Param("branchId") Long branchId, @Param("skuIdList") List<Long> skuId, @Param("unitSize") Integer unitSize);

    /**
    * @Description: ES根据订单ID获取订单明细列表
    * @Author: liuxingyu
    * @Date: 2024/5/7 17:36
    */
    List<EsStoreProduct> getEsListByOrderId(@Param("orderId") Long orderId);


    /**
     * 根据条件获取某段时间的订单销售金额
     *
     * @param reqVO
     * @return
     */
    BigDecimal getSaleAmount (@Param("reqVO") TrdColonelAppOrderListPageReqVO reqVO);

    /**
     * 根据订单id获取入住商订单信息
     * @param orderId
     * @return
     */
    List<TradeOutErpRequest> getTrdSupplierOrderListByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据入驻商订单编号查询入驻商订单详细
     * @param supplierOrderNo
     * @return
     */
    List<TradeDetail> getTrdSupplierOrderDtlListBySupOrderNo(@Param("supplierOrderNo") String supplierOrderNo);

    CustomApiMaster getCustomApiMaster(@Param("customApiMaster") CustomApiMaster customApiMaster);

    List<CustomApiDetail> getCustomApiDetail(@Param("apiNo") String apiNo);

    default Long selectBranchIdByOrderId(Long orderId) {
        return selectOne(
                new LambdaQueryWrapperX<TrdOrder>()
                        .eq(TrdOrder::getOrderId, orderId)
                        .select(TrdOrder::getBranchId)
        ).getBranchId();
    }

    /**
     * @Description: 获取订单列表总数
     * @param orderPageReqVO
     * @return
     */
    Long selectPageAllNewCount(@Param("orderPageReqVO") TrdOrderPageReqDTO orderPageReqVO);

    /**
     * @Description: 分页获取订单列表 （商城小程序、商城APP、业务员小程序我的代办）
     * @param orderPageReqVO
     * @return
     */
    List<TrdOrderRespDTO> selectPageAllNew(@Param("orderPageReqVO") TrdOrderPageReqDTO orderPageReqVO);

    /**
     * 获取品牌商首页统计数据
     * @param brandList 品牌列表
     * @param areaList  城市列表
     * @return
     */
    BrandHomePageRespVO selectByBrandHomeSaleData(@Param("brandList") List<Long> brandList, @Param("areaList") List<Long> areaList);

    /**
     * 获取品牌商首页列表数据
     * @param reqVO
     * @return
     */
    List<BrandHomeSaleListRespVO> selectByBrandHomeSaleList(@Param("reqVO") BrandHomeSaleListReqVO reqVO);

    /**
     * 获取品牌商品牌商品销售数据
     * @param reqVO
     * @return
     */
    List<BrandHomeSaleListInfoRespVO> selectByBrandHomeSaleInfoList(@Param("reqVO") BrandHomeSaleListReqVO reqVO);

    /**
     * @Description: 获取业务员订单业务结算信息 (需日结数据)
     * @param reqVO 参数实体
     * @return
     */
    List<ColonelOrderDaySettleDTO> getColonelOrderBusinessSettle(ColonelOrderDaySettleVO reqVO);

    /**
     * 根据门店、日期区间 查询门店订单销售数据汇总 (按照年月进行拆分)
     * @param reqVO
     * @return
     */
    List<EsBranchOrderSalesRespDTO> getBranchOrderSalesByParams(EsBranchOrderSalesReqVO reqVO);

    /**
     * 获取订单列表打印数据
     * @param dcOrderPageReqVO
     * @return
     */
    List<PcOrderPrintDetailVO> getOperatorOrderPageListNewPrint(DcOrderPageReqVO dcOrderPageReqVO);

    List<SyncOrderCallDTO> getSupplierOrderOpenDTOList(@Param("reqVO") SyncOrderPageReqDTO reqVO,@Param("orderDelayPushTime") Integer orderDelayPushTime,@Param("supplierId") Long supplierId);

    Long getSupplierOrderOpenDTOListCount(@Param("reqVO") SyncOrderPageReqDTO reqVO,@Param("orderDelayPushTime") Integer orderDelayPushTime,@Param("supplierId") Long supplierId);

    List<SyncAfterOrderCallDTO> getSupplierAfterOrderOpenDTOList(@Param("reqVO") SyncOrderPageReqDTO reqVO, @Param("supplierId") Long supplierId);

    Long getSupplierAfterOrderOpenDTOListCount(@Param("reqVO") SyncOrderPageReqDTO reqVO,@Param("supplierId") Long supplierId);
    /**
     * 根据 实际支付单号（订单单号或货到付款单号） 获取订单明细Id集合
     * @param sheetNo
     * @return
     */
    List<Long> getOrderAndAfterSettleIdBySheetNo(@Param("sheetNo") String sheetNo);

    /**
     * 根据查询条件获取当前订单明细信息导出数据
     * @param param
     * @return
     */
    List<SupplierOrderDtlInfoExportVO> getSupplierOrderDtlInfoExport(@Param("param") DcOrderPageReqApiVO param,
                                                                   @Param("spuIds") List<Long> spuIds,
                                                                   @Param("skuIds") List<Long> skuIds);

    /**
     * 获取平台商首页当前销售、欠款、退单实时数据
     * @param reqVO
     * @return
     */
    List<HomePagesCurrentSalesDataRespDTO> getHomePagesCurrentSalesData(HomePagesReqVO reqVO);

    /**
     * 获取订单销售数据
     * @param reqVO
     * @return
     */
    List<HomePagesOrderSalesDataRespDTO> getHomePagesOrderSalesData(HomePagesReqVO reqVO);

    /**
     * 查询区域销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesAreaSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 查询运营商销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesDcSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 查询商品销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesItemSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 查询一级品类销售数据TOP10数据 -
     * 注：这里查询平台商一级管理分类销售数据应单个查询，因订单中存储的是三级管理分类，不可跨库联表查询
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesCategorySalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 查询入驻商销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesSupplierSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 查询业务员销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesColonelSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 查询门店销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesBranchSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 根据 用户账号或门店账号查询出是否下过订单
     * @param type member：用户，branch：门店
     * @param ids 用户或门店账号ID
     * @return 返回下过单的用户或门店ID
     */
    List<Long> checkMemberOrBranchExistsOrderSaleInfo(@Param("type")String type, @Param("sysCode") Long sysCode, @Param("ids") List<Long> ids);

    default List<TrdOrder> selectByStoreProductInitOrderList(Long minId) {
        return selectList(
                new LambdaQueryWrapperX<TrdOrder>()
                        .in(TrdOrder::getPayState,
                                PayStateEnum.PAY_ALREADY_ONLINE.getCode(),
                                PayStateEnum.PAY_NOT_HDFK.getCode(),
                                PayStateEnum.PAY_ALREADY_HDFK.getCode()
                        )
                        .gt(TrdOrder::getCreateTime, DateUtil.offsetMonth(new Date(), -3))
                        .gt(Objects.nonNull(minId), TrdOrder::getOrderId, minId)
                        .orderByAsc(TrdOrder::getOrderId)
                        .last("LIMIT 500")
        );
    }

    List<DcSupplierOrderPageRespVO> getDebtOrderPageList(@Param("param") DcOrderPageReqVO param);

    HomePagesCurrentSalesDataRespDTO getDebtOrderPageListCount(@Param("param") DcOrderPageReqVO param);


    /**
     * 根据门店ID获取业务员APP客户信息中的订单信息（当月）
     * @param branchId
     * @return
     */
    List<ColonelAppBranchOrderDTO> getColonelAppBranchOrder(@Param("branchId") Long branchId);

    /**
     * 根据业务员ID获取业务员APP首页的订单信息（当天）
     * @param colonelId
     * @return
     */
    List<ColonelAppPageOrderDTO> getColonelAppPageOrder(@Param("colonelId") Long colonelId);

    default Boolean getAreaIdExistOrder(Long areaId, Long sysCode){
        List<TrdOrder> orderList = selectList(
                new LambdaQueryWrapperX<TrdOrder>()
                        .eq(TrdOrder::getAreaId, areaId)
                        .eq(TrdOrder::getSysCode, sysCode)
                        .last("LIMIT 1")
        );

        return ToolUtil.isNotEmpty(orderList)?Boolean.TRUE:Boolean.FALSE;
    }

    default Boolean getBranchIdExistOrder(Long branchId){
        List<TrdOrder> orderList = selectList(
                new LambdaQueryWrapperX<TrdOrder>()
                        .eq(TrdOrder::getBranchId, branchId)
                        .last("LIMIT 1")
        );

        return ToolUtil.isNotEmpty(orderList)?Boolean.TRUE:Boolean.FALSE;
    }

    OrderCutAmtDTO selectOrderCntAmt(
            @Param("startDate") DateTime startDate,
            @Param("endDate") DateTime endDate,
            @Param("cacheKey") OrderCutAmtDTO.CacheKey cacheKey);


    List<TrdOrder> getBranchLatestOrderByBranchIdList( @Param("sysCode")Long sysCode);

    TrdOrder getBranchLatestOrderByBranchId( @Param("branchId")Long branchId);

    List<SupplierOrderVO> selectSupplierOrder(@Param("param") DcOrderPageReqVO param);

    List<SupplierOrderExportVO> selectSupplierOrderExport(@Param("param") DcOrderPageReqVO param);

    /**
     * 统计订单量
     * @param param
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    List<SupplierOrderCountRespVO> countSupplierOrder(@Param("param")SupplierOrderCountPageReqVO param);

    /**
     * 按平台商统计总单量
     * @param param
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    List<SupplierOrderCountAllRespVO> countSupplierOrderAll(@Param("param")SupplierOrderCountAllPageReqVO param);

    /**
     * 统计当日销售额
     * @param param
     * @return
     */
    HomePagesCurrentSalesDataRespVO getHomePagesCurrentDaySales(@Param("param")HomePagesCurrentSalesDataReqVO param);


    /**
     * 根据门店ID获取最近一次下单时间
     * @param branchId 门店ID
     * @return 最近一次下单时间，若无记录则返回 null
     */
    /**
     * 根据门店ID获取最近一次下单时间
     * @param branchId 门店ID
     * @return 最近一次下单时间，若无记录则返回 null
     */
    default Date getLatestOrderCreateTimeByBranchId(Long branchId) {
        if (branchId == null) {
            return null;
        }

        List<TrdOrder> orders = selectList(
                new LambdaQueryWrapperX<TrdOrder>()
                        .eq(TrdOrder::getBranchId, branchId)
                        .select(TrdOrder::getCreateTime)
                        .orderByDesc(TrdOrder::getCreateTime)
                        .last("LIMIT 1")
        );
        return orders.isEmpty() ? null : orders.get(0).getCreateTime();
    }



    /**
     * 根据branchId统计当前月和上月订单笔数和订单总金额
     *
     * @param branchId 门店ID
     * @return 统计结果
     */
    List<Map<String, Object>> countOrderStatsByBranchId(@Param("branchId")Long branchId);

}
