package com.zksr.trade.convert.driver;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.driver.excel.TrdDriverImportExcel;
import com.zksr.trade.domain.TrdDriver;
import com.zksr.trade.controller.driver.vo.TrdDriverRespVO;
import com.zksr.trade.controller.driver.vo.TrdDriverSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 司机档案 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-12-11
*/
@Mapper
public interface TrdDriverConvert {

    TrdDriverConvert INSTANCE = Mappers.getMapper(TrdDriverConvert.class);

    TrdDriverRespVO convert(TrdDriver trdDriver);

    TrdDriver convert(TrdDriverSaveReqVO trdDriverSaveReq);

    PageResult<TrdDriverRespVO> convertPage(PageResult<TrdDriver> trdDriverPage);

    @BeanMapping(ignoreByDefault = true)
    void convertImport(@MappingTarget TrdDriver trdDriver, TrdDriverImportExcel itemData);
}