package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 订单分享对象 trd_order_share
 *
 * <AUTHOR>
 * @date 2024-10-29
 */
@TableName(value = "trd_order_share")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdOrderShare extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long shareOrderId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    /** 入驻商订单ID */
    @Excel(name = "入驻商订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierOrderId;

    /** 订单ID */
    @Excel(name = "订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long orderId;

    /** 发起分享IP */
    @Excel(name = "发起分享IP")
    private String remoteIp;

    /** 有效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "有效时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expirationTime;

    /** 分享KEY */
    @Excel(name = "分享KEY")
    @ApiModelProperty(value = "分享KEY")
    private String shareKey;
}
