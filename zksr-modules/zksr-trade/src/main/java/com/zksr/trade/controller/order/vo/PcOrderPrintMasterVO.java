package com.zksr.trade.controller.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单PC打印主信息
 * @date 2024/9/12 15:26
 */
@Data
@ApiModel(description = "订单PC打印主信息")
public class PcOrderPrintMasterVO {

    @ApiModelProperty("入驻商名称")
    private String supplierName;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    @ApiModelProperty("下单时间")
    private Date createTime;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("子订单编号")
    private String supplierOrderNo;

    @ApiModelProperty("交易单号")
    private String transferNo;

    @ApiModelProperty("付款方式")
    private String payWay;

    @ApiModelProperty("订单收货地址")
    private String receiveAddr;

    @ApiModelProperty("买家/收货人名称")
    private String receiveName;

    @ApiModelProperty("门店名称")
    private String branchName;

    @ApiModelProperty("门店联系人手机号")
    private String branchContactPhone;

    @ApiModelProperty("订单总价")
    private BigDecimal totalAmt;

    @ApiModelProperty("总商品数")
    private Integer totalNum;

    @ApiModelProperty("总价大写")
    private String totalAmtCase;

    @ApiModelProperty("备注")
    private String memo;

    @ApiModelProperty("打印次数")
    private Long printQty;

    @ApiModelProperty("详情数据")
    private List<PcOrderPrintDetailVO> detaiList;
}
