package com.zksr.trade.mq.tx;

import com.zksr.common.rocketmq.tx.AbstractTxMsgSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("demoTxMsgSender")
public class DemoTxMsgSender extends AbstractTxMsgSender<String> {
    @Override
    protected boolean sendTxMsg(String s) {
        log.info("DemoTxMsgSender sendTxMsg：{} ", s);
        return false;
    }

    @Override
    protected boolean chklocalTx(String s) {
        log.info("DemoTxMsgSender chklocalTx：{} ", s);
        return false;
    }
}
