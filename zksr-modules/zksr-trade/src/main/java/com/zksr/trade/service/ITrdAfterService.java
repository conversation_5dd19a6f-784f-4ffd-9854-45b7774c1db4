package com.zksr.trade.service;

import com.zksr.common.core.domain.vo.openapi.TrdSupAfterDtlRequest;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.report.api.homePages.dto.HomePagesOrderAfterDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.trade.api.after.dto.AfterOrderPageRespDTO;
import com.zksr.trade.api.after.dto.OrderAfterSaveResDTO;
import com.zksr.trade.api.after.vo.*;
import com.zksr.trade.api.order.dto.ColonelAppOrderListTotalDTO;
import com.zksr.trade.api.order.vo.*;
import com.zksr.trade.controller.after.dto.AfterDtlRespDTO;
import com.zksr.trade.controller.after.dto.AfterPageRespDTO;
import com.zksr.trade.controller.after.vo.AfterPageReqVO;
import com.zksr.trade.controller.after.vo.TrdAfterSaveReqVO;
import com.zksr.trade.api.after.vo.TrdAfter;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 售后单Service接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
public interface ITrdAfterService {

    /**
     * 新增售后单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdAfter(@Valid TrdAfterSaveReqVO createReqVO);

    /**
     * 修改售后单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdAfter(@Valid TrdAfterSaveReqVO updateReqVO);

    /**
     * 删除售后单
     *
     * @param afterId 售后单id
     */
    public void deleteTrdAfter(Long afterId);

    /**
     * 批量删除售后单
     *
     * @param afterIds 需要删除的售后单主键集合
     * @return 结果
     */
    public void deleteTrdAfterByAfterIds(Long[] afterIds);

    /**
     * 获得售后单详情
     *
     * @param reqVO
     * @return 售后单
     */
    public AfterDtlRespDTO getTrdAfterDtl(AfterDtlReqVO reqVO);

    /**
     * 获得售后单分页
     *
     * @param pageReqVO 分页查询
     * @return 售后单分页
     */
    PageResult<AfterPageRespDTO> getTrdAfterPage(AfterPageReqVO pageReqVO);

    /**
     * 取消售后单退货（同意申请后）
     * @param supplierAfterNo 入驻商售后编号
     */
    public void cancelAfter(String supplierAfterNo, Long sourceType);

    /**
     * 取消售后单退货（同意申请前）
     * @param afterId
     */
    public void cancelAfterReturn(Long afterId);

    /**
     * 审核同意退货——售后单
     * @param editVO
     */
    public void approveAfterReturn(AfterApproveDtlEditVO editVO);

    /**
     * 审核同意退款 或 退款失败重新发起 ——售后单
     * @param editVO
     */
    public void approveAfterRefund(AfterApproveDtlEditVO editVO);

    /**
     * 批量审核同意退款 —— 售后单
     * @param reqVOS
     */
    public String approveAfterBatchRefund(List<AfterDtlReqVO> reqVOS);

    /**
     * 保存售后订单
     * @param request
     * @return
     */
    OrderAfterSaveResDTO saveOrderAfter(OrderAfterSaveRequest request);

    /**
     * 保存售后订单 - 合单售后 TODO 待处理
     * @param request
     * @return
     */
    OrderAfterSaveResDTO saveMergeAfterOrder(OrderMergeAfterRequest request);

    /**
     * 分页查询售后订单（商城小程序）
     * @param reqVO
     * @return
     */
    PageResult<AfterOrderPageRespDTO> pageAfter(AfterOrderPageReqVO reqVO);


    /**
     * 售后订单上传物流单号提交（商城小程序）
     * @param expressSaveVO
     * @return
     */
    void uploadAfterExpressDelivery(OrderAfterUploadExpressSaveVO expressSaveVO);

    /**
     * 售后订单退款成功回调 (在线支付)
     * @param refundVO
     */
    void afterRefundSuccessCallback(PayRefundVO refundVO);

    /**
     * 售后订单退款失败回调 (在线支付)
     * @param refundVO
     */
    void afterRefundFailCallback(PayRefundVO refundVO);

    /**
     * @Description: 获取业务员App售后订单 汇总数据
     * @Author: liyi
     * @Date: 2024/4/26 10:29
     */
    ColonelAppOrderListTotalDTO selectMemColoneAppAfterOrderListTotal(TrdColonelAppOrderListPageReqVO orderPageReqVO);

    /**
     * @Description: 获取业务员App售后订单
     * @Author: liyi
     * @Date: 2024/4/26 10:29
     */
    PageResult<TrdColonelAppOrderListRespVO> selectMemColoneAppAfterOrder(TrdColonelAppOrderListPageReqVO orderPageReqVO);

    /**
     * @Description: 获取业务员App售后订单 （新）
     * @Author: liyi
     * @Date: 2024/4/26 10:29
     */
    List<AfterOrderPageRespDTO> selectMemColoneAppAfterOrderNew(TrdColonelAppOrderListPageReqVO orderPageReqVO);

    /**
     * @Description: 获取业务员App售后订单详情
     * @Author: liyi
     * @Date: 2024/4/26 10:29
     */
    public List<TrdColonelAppOrderDetailRespVO> getMemColoneAppAfterOrderDetail(Long orderId);

    AfterApproveDtlEditVO getAfterDtlByDtlId(TrdSupAfterDtlRequest request);

    /**
     *  售后订单自动审核
     * @param afterId
     */
    void afterApprove(Long afterId);

    TrdAfter getTrdAfterByAfterNo(String afterNo);

    TrdAfter getTrdAfterById(Long afterId);

    /**
     * 根据订单ID校验是否存在未完成售后订单
     * @param orderId
     * @return
     */
    Integer getUnfinishedAfterByOrderId(Long orderId);

    /**
     *  获取订单退货销售数据
     * @param reqVO
     * @return
     */
    List<HomePagesOrderAfterDataRespDTO> getHomePagesOrderAfterData(HomePagesReqVO reqVO);

    /**
     * 根据业务员ID获取业务员APP首页的售后金额（当天）
     *
     * @param colonelId
     * @return
     */
    BigDecimal getColonelAppPageAfterAmt(Long colonelId);
}
