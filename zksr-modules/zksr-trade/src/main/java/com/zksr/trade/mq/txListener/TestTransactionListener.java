package com.zksr.trade.mq.txListener;

import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.trade.api.order.dto.TestOrderDto;
import com.zksr.trade.domain.Order;
import com.zksr.trade.domain.TrdHdfkPay;
import com.zksr.trade.mapper.OrderDAO;
import com.zksr.trade.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.LocalTransactionState;
import org.apache.rocketmq.client.producer.TransactionListener;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("testTransactionListener")
@Slf4j
public class TestTransactionListener implements TransactionListener {

    @Resource
    private OrderDAO orderDAO;

    @Resource
    private OrderService orderService;

    /**
     * 执行本地事务
     * @param message
     * @param o
     * @return
     */
    @Override
    public LocalTransactionState executeLocalTransaction(Message message, Object o) {
        try{
            log.info("executeLocalTransaction message:" + JSON.toJSONString(message));
            //return null;
            String msg = new String(message.getBody());
            TestOrderDto orderDto = JSON.parseObject(msg, TestOrderDto.class);
            Order order = HutoolBeanUtils.toBean(orderDto, Order.class);
            orderService.saveOrder(order);
            return LocalTransactionState.COMMIT_MESSAGE;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return LocalTransactionState.ROLLBACK_MESSAGE;
        }
    }

    @Override
    public LocalTransactionState checkLocalTransaction(MessageExt messageExt) {
//        return null;
        String msg = String.valueOf(messageExt.getBody());
        Order order = JSON.parseObject(msg, Order.class);

        log.info("checkLocalTransaction messageExt:" + JSON.toJSONString(order));

        Order o = orderDAO.selectOne(new LambdaQueryWrapperX<Order>()
                .eqIfPresent(Order::getOrderNo, order.getOrderNo()));
        if(ToolUtil.isNotEmpty(o)){
            log.info("checkLocalTransaction 确认已提交");
            return LocalTransactionState.COMMIT_MESSAGE;
        }
        if(ToolUtil.isEmpty(o)){
            log.info("checkLocalTransaction 确认已提交");
            return LocalTransactionState.ROLLBACK_MESSAGE;
        }
        return LocalTransactionState.UNKNOW;
    }
}
