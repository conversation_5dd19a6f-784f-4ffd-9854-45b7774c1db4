package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.domain.TrdExpressImportDtl;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlPageReqVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlSaveReqVO;

import java.util.List;

/**
 * 快递导入明细Service接口
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
public interface ITrdExpressImportDtlService {

    /**
     * 新增快递导入明细
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdExpressImportDtl(@Valid TrdExpressImportDtlSaveReqVO createReqVO);

    /**
     * 修改快递导入明细
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdExpressImportDtl(@Valid TrdExpressImportDtlSaveReqVO updateReqVO);

    /**
     * 删除快递导入明细
     *
     * @param expressImportDtlId 快递导入明细id
     */
    public void deleteTrdExpressImportDtl(Long expressImportDtlId);

    /**
     * 批量删除快递导入明细
     *
     * @param expressImportDtlIds 需要删除的快递导入明细主键集合
     * @return 结果
     */
    public void deleteTrdExpressImportDtlByExpressImportDtlIds(Long[] expressImportDtlIds);

    /**
     * 获得快递导入明细
     *
     * @param expressImportDtlId 快递导入明细id
     * @return 快递导入明细
     */
    public TrdExpressImportDtl getTrdExpressImportDtl(Long expressImportDtlId);

    /**
     * 获得快递导入明细分页
     *
     * @param pageReqVO 分页查询
     * @return 快递导入明细分页
     */
    PageResult<TrdExpressImportDtl> getTrdExpressImportDtlPage(TrdExpressImportDtlPageReqVO pageReqVO);

    /**
     * 入驻商一件代发订单数据处理
     */
    public void supplierOrderExpressReceive(TrdExpressImportDtlSaveReqVO trdExpressImportDtl);

    /**
     * 根据导入id和状态查询成功或失败数据
     * @param expressImportId
     * @param status
     * @return
     */
    public List<TrdExpressImportDtl> getExpressImportInfo (Long expressImportId, Integer status);

}
