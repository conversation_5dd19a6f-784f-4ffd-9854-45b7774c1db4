package com.zksr.trade.controller.after;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.service.ITrdSupplierAfterDtlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.after.supplierDtlVo.TrdSupplierAfterDtlPageReqVO;
import com.zksr.trade.controller.after.supplierDtlVo.TrdSupplierAfterDtlSaveReqVO;
import com.zksr.trade.controller.after.supplierDtlVo.TrdSupplierAfterDtlRespVO;
import com.zksr.trade.convert.after.TrdSupplierAfterDtlConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 售后单明细Controller
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Api(tags = "管理后台 - 售后单明细接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/afterSuppleirDtl")
public class TrdSupplierAfterDtlController {
    @Autowired
    private ITrdSupplierAfterDtlService trdSupplierAfterDtlService;

    /**
     * 新增售后单明细
     */
    @ApiOperation(value = "新增售后单明细", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "售后单明细", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdSupplierAfterDtlSaveReqVO createReqVO) {
        return success(trdSupplierAfterDtlService.insertTrdSupplierAfterDtl(createReqVO));
    }

    /**
     * 修改售后单明细
     */
    @ApiOperation(value = "修改售后单明细", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "售后单明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdSupplierAfterDtlSaveReqVO updateReqVO) {
            trdSupplierAfterDtlService.updateTrdSupplierAfterDtl(updateReqVO);
        return success(true);
    }

    /**
     * 删除售后单明细
     */
    @ApiOperation(value = "删除售后单明细", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "售后单明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{supplierAfterDtlIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] supplierAfterDtlIds) {
        trdSupplierAfterDtlService.deleteTrdSupplierAfterDtlBySupplierAfterDtlIds(supplierAfterDtlIds);
        return success(true);
    }

    /**
     * 获取售后单明细详细信息
     */
    @ApiOperation(value = "获得售后单明细详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{supplierAfterDtlId}")
    public CommonResult<TrdSupplierAfterDtlRespVO> getInfo(@PathVariable("supplierAfterDtlId") Long supplierAfterDtlId) {
        TrdSupplierAfterDtl trdSupplierAfterDtl = trdSupplierAfterDtlService.getTrdSupplierAfterDtl(supplierAfterDtlId);
        return success(TrdSupplierAfterDtlConvert.INSTANCE.convert(trdSupplierAfterDtl));
    }

    /**
     * 分页查询售后单明细
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得售后单明细分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdSupplierAfterDtlRespVO>> getPage(@Valid TrdSupplierAfterDtlPageReqVO pageReqVO) {
        PageResult<TrdSupplierAfterDtl> pageResult = trdSupplierAfterDtlService.getTrdSupplierAfterDtlPage(pageReqVO);
        return success(TrdSupplierAfterDtlConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:dtl:add";
        /** 编辑 */
        public static final String EDIT = "trade:dtl:edit";
        /** 删除 */
        public static final String DELETE = "trade:dtl:remove";
        /** 列表 */
        public static final String LIST = "trade:dtl:list";
        /** 查询 */
        public static final String GET = "trade:dtl:query";
        /** 停用 */
        public static final String DISABLE = "trade:dtl:disable";
        /** 启用 */
        public static final String ENABLE = "trade:dtl:enable";
    }
}
