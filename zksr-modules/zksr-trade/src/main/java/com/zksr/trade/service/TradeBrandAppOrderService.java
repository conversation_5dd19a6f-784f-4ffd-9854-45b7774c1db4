package com.zksr.trade.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.controller.app.vo.BrandHomePageRespVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListInfoRespVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListReqVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/8/6
 * @desc
 */
public interface TradeBrandAppOrderService {

    /**
     * 获取品牌商首页数据统计
     * @return
     */
    BrandHomePageRespVO getBrandHomePage();

    /**
     * 获取品牌商首页列表数据
     * @param reqVO
     * @return
     */
    List<BrandHomeSaleListRespVO> getHomeSaleList(BrandHomeSaleListReqVO reqVO);

    /**
     * 品牌列表获取某个品牌商品销售数据
     * @param reqVO
     * @return
     */
    List<BrandHomeSaleListInfoRespVO> getHomeSaleListInfo(BrandHomeSaleListReqVO reqVO);
}
