package com.zksr.trade.convert.after;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdAfterLog;
import com.zksr.trade.controller.after.logVo.TrdAfterLogRespVO;
import com.zksr.trade.controller.after.logVo.TrdAfterLogSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 售后日志 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-20
*/
@Mapper
public interface TrdAfterLogConvert {

    TrdAfterLogConvert INSTANCE = Mappers.getMapper(TrdAfterLogConvert.class);

    TrdAfterLogRespVO convert(TrdAfterLog trdAfterLog);

    TrdAfterLog convert(TrdAfterLogSaveReqVO trdAfterLogSaveReq);

    PageResult<TrdAfterLogRespVO> convertPage(PageResult<TrdAfterLog> trdAfterLogPage);
}