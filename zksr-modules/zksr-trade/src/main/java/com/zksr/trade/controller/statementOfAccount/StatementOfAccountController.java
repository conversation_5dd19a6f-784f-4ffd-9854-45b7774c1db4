package com.zksr.trade.controller.statementOfAccount;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.orderSettle.dto.OrderSettleResDTO;
import com.zksr.trade.api.orderSettle.vo.OrderSettlePageVO;
import com.zksr.trade.controller.settle.TrdSettleController;
import com.zksr.trade.controller.statementOfAccount.vo.OrderCommissionStatementReqVO;
import com.zksr.trade.controller.statementOfAccount.vo.OrderCommissionStatementRespVO;
import com.zksr.trade.service.ITrdSettleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 订单结算Controller
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Api(tags = "管理后台 - 对账单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/statementOfAccount")
public class StatementOfAccountController {

    @Autowired
    private ITrdSettleService trdSettleService;

    @GetMapping("/getAccountOrderSettleFlowPageList")
    @ApiOperation(value = "分页查询订单分佣对账单", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    public CommonResult<PageResult<OrderCommissionStatementRespVO>> getOrderStatementOfAccountPageList(@Valid OrderCommissionStatementReqVO pageReqVO) {
        return success(trdSettleService.getStatementOfAccountPage(pageReqVO));
    }


    public static class Permissions {
        /** 订单分佣对账单 */
        public static final String LIST = "trade:settle:orderStatementOfAccountList";

    }

}
