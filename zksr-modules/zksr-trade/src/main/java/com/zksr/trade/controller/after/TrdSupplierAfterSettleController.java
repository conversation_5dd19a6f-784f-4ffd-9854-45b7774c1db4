package com.zksr.trade.controller.after;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdSupplierAfterSettle;
import com.zksr.trade.service.ITrdSupplierAfterSettleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.after.settleVo.TrdSupplierAfterSettlePageReqVO;
import com.zksr.trade.controller.after.settleVo.TrdSupplierAfterSettleSaveReqVO;
import com.zksr.trade.controller.after.settleVo.TrdSupplierAfterSettleRespVO;
import com.zksr.trade.convert.after.TrdSupplierAfterSettleConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 售后结算信息Controller
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Api(tags = "管理后台 - 售后结算信息接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/afterSupplierDtlSettle")
public class TrdSupplierAfterSettleController {
    @Autowired
    private ITrdSupplierAfterSettleService trdSupplierAfterSettleService;

    /**
     * 新增售后结算信息
     */
    @ApiOperation(value = "新增售后结算信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "售后结算信息", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdSupplierAfterSettleSaveReqVO createReqVO) {
        return success(trdSupplierAfterSettleService.insertTrdSupplierAfterSettle(createReqVO));
    }

    /**
     * 修改售后结算信息
     */
    @ApiOperation(value = "修改售后结算信息", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "售后结算信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdSupplierAfterSettleSaveReqVO updateReqVO) {
            trdSupplierAfterSettleService.updateTrdSupplierAfterSettle(updateReqVO);
        return success(true);
    }

    /**
     * 删除售后结算信息
     */
    @ApiOperation(value = "删除售后结算信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "售后结算信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{afterSettleIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] afterSettleIds) {
        trdSupplierAfterSettleService.deleteTrdSupplierAfterSettleByAfterSettleIds(afterSettleIds);
        return success(true);
    }

    /**
     * 获取售后结算信息详细信息
     */
    @ApiOperation(value = "获得售后结算信息详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{afterSettleId}")
    public CommonResult<TrdSupplierAfterSettleRespVO> getInfo(@PathVariable("afterSettleId") Long afterSettleId) {
        TrdSupplierAfterSettle trdSupplierAfterSettle = trdSupplierAfterSettleService.getTrdSupplierAfterSettle(afterSettleId);
        return success(TrdSupplierAfterSettleConvert.INSTANCE.convert(trdSupplierAfterSettle));
    }

    /**
     * 分页查询售后结算信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得售后结算信息分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdSupplierAfterSettleRespVO>> getPage(@Valid TrdSupplierAfterSettlePageReqVO pageReqVO) {
        PageResult<TrdSupplierAfterSettle> pageResult = trdSupplierAfterSettleService.getTrdSupplierAfterSettlePage(pageReqVO);
        return success(TrdSupplierAfterSettleConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:settle:add";
        /** 编辑 */
        public static final String EDIT = "trade:settle:edit";
        /** 删除 */
        public static final String DELETE = "trade:settle:remove";
        /** 列表 */
        public static final String LIST = "trade:settle:list";
        /** 查询 */
        public static final String GET = "trade:settle:query";
        /** 停用 */
        public static final String DISABLE = "trade:settle:disable";
        /** 启用 */
        public static final String ENABLE = "trade:settle:enable";
    }
}
