package com.zksr.trade.service.impl.handler.activity;

import com.zksr.common.core.enums.FgActivityTimesRuleEnum;
import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.promotion.api.activity.dto.ActivityDTO;
import com.zksr.promotion.api.activity.dto.PrmActivityDTO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.domain.TrdAfterDiscountDtl;
import com.zksr.trade.domain.TrdOrderDiscountDtl;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.activity.ITrdOrderActivityHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *  订单优惠活动处理器 ———— 满赠促销
 */
@Service
@Slf4j
public class TrdOrderActivityFgHandlerServiceImpl implements ITrdOrderActivityHandlerService {
    @Autowired
    private RedisActivityService redisActivityService;
    @Autowired
    private TrdCacheService trdCacheService;
    @Autowired
    private RedisService redisService;
    @Override
    public void orderCancelReturnActivity(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {
        // 返还满赠活动库存
        toddList.stream().filter(orderDiscount -> Objects.equals(TrdDiscountTypeEnum.FG.getType(), orderDiscount.getDiscountType()))
                .forEach(orderDiscount -> {

                    ActivityDTO activityDTO = trdCacheService.getActivityDTO(orderDiscount.getDiscountId());

                    if (Objects.nonNull(activityDTO.getTimesRule())) {
                        // 移除活动仅参加一次标识
                        redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), tor.getBranchId()));
                    }

                    BigDecimal saleQty = BigDecimal.valueOf(orderDiscount.getGiftQty()).multiply(NumberPool.BIGDECIMAL_GROUND);

                    redisActivityService.setFgSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), saleQty, null, null);
                });
    }

    @Override
    public void afterCancelReturnActivity(TrdOrder tor, List<TrdSupplierOrderDtl> tsodList, List<TrdOrderDiscountDtl> toddList, TrdAfter tar, List<TrdSupplierAfterDtl> tsadList, List<TrdAfterDiscountDtl> taddList) {
        // 返还满赠活动库存
        taddList.stream().filter(tadd -> Objects.equals(TrdDiscountTypeEnum.FG.getType(), tadd.getDiscountType()))
                .collect(Collectors.groupingBy(TrdAfterDiscountDtl::getDiscountId))
                .forEach((key, value) -> {
                    // 根据促销活动Id 查询出所参与的所有商品
                    List<Long> tsodIdList = toddList.stream().filter(todd -> Objects.equals(key, todd.getDiscountId())).map(TrdOrderDiscountDtl::getSupplierOrderDtlId).collect(Collectors.toList());

                    // 获取活动参与商品列表是否已全部售后
                    boolean boo = tsodList.stream().filter(tsod -> tsodIdList.contains(tsod.getSupplierOrderDtlId()))
                            .allMatch(tsod -> tsod.getTotalNum().compareTo(tsod.getCancelQty()) == 0);

                    ActivityDTO activityDTO = trdCacheService.getActivityDTO(key);
                    if (Objects.nonNull(activityDTO.getTimesRule()) && boo) {
                        // 移除活动仅参加一次标识
                        redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(key, tor.getBranchId()));
                    }

                    // 返还赠品库存
                    value.forEach(tadd -> {
                        BigDecimal returnQty = tadd.getGiftQty().multiply(NumberPool.BIGDECIMAL_GROUND);
                        redisActivityService.setFgSaleNum(tadd.getDiscountId(), tadd.getDiscountRuleId(), returnQty, null, null);
                    });
                });




    }

    @Override
    public void restoreCancelledOrderDiscounts(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {
        //恢复已取消订单的 满赠活动库存
        toddList.stream().filter(orderDiscount -> Objects.equals(TrdDiscountTypeEnum.FG.getType(), orderDiscount.getDiscountType()))
                .forEach(orderDiscount -> {
                    // 获取满赠活动数据
                    ActivityDTO activity = trdCacheService.getActivityDTO(orderDiscount.getDiscountId());
                    long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());
                    if (Objects.nonNull(activity.getTimesRule())) {
                        switch (FgActivityTimesRuleEnum.formValue(activity.getTimesRule())){
                            case RULE_LEVEL_0: // 每日一次
                                time = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate());
                                break;
                            case RULE_LEVEL_1: // 活动期间内仅一次
                                time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());
                                break;
                        }
                        // 标记活动已经达到限制
                        redisService.setCacheObject(RedisActivityConstants.getTimesRuleKey(activity.getActivityId(), tor.getBranchId()), StringPool.ONE, time, TimeUnit.SECONDS);
                    }

                    BigDecimal saleQty = BigDecimal.valueOf(orderDiscount.getGiftQty());
                    // 恢复 缓存满赠活动已购数量
                    redisActivityService.setFgSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), saleQty, null, null);
                });
    }
}
