package com.zksr.trade.convert.after;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.after.dto.SupplierAfterDtlDTO;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.controller.after.supplierDtlVo.TrdSupplierAfterDtlRespVO;
import com.zksr.trade.controller.after.supplierDtlVo.TrdSupplierAfterDtlSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 售后单明细 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-20
*/
@Mapper
public interface TrdSupplierAfterDtlConvert {

    TrdSupplierAfterDtlConvert INSTANCE = Mappers.getMapper(TrdSupplierAfterDtlConvert.class);

    TrdSupplierAfterDtlRespVO convert(TrdSupplierAfterDtl trdSupplierAfterDtl);

    TrdSupplierAfterDtl convert(TrdSupplierAfterDtlSaveReqVO trdSupplierAfterDtlSaveReq);

    PageResult<TrdSupplierAfterDtlRespVO> convertPage(PageResult<TrdSupplierAfterDtl> trdSupplierAfterDtlPage);

    List<SupplierAfterDtlDTO> convertDTOList(List<TrdSupplierAfterDtl> trdSupplierAfterDtlBatch);
}