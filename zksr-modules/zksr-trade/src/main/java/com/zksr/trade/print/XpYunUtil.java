package com.zksr.trade.print;


import net.xpyun.platform.opensdk.util.HashSignUtil;

/**
 * @Description: 打印工具类
 * @Author: liuxingyu
 * @Date: 2024/4/19 10:18
 */
public class XpYunUtil {

    public static String itemNameSubString(String itemSubnoNew,String itemSubno) {
        if(itemSubno.length()>20){
            itemSubnoNew+=itemSubno.substring(0,10)+"</N><BR><N>";
            String isn = itemSubno.substring(10,itemSubno.length());
            if(isn.length()>20){
                itemSubnoNew+=itemNameSubStringSplit("",isn);
            }else{
                itemSubnoNew+= "                          "+isn;
            }
            return itemSubnoNew;
        }else{
            return itemSubno;
        }
    }


    public static String itemNameSubStringSplit(String itemSubnoNew,String itemSubno){
        if(itemSubno.length()>20){
            itemSubnoNew+= "                          "+itemSubno.substring(0,10)+"</N><BR><N>";
            String isn = itemSubno.substring(10,itemSubno.length());
            if(isn.length()>20){
                itemSubnoNew+=itemNameSubStringSplit("",isn);
            }else{
                itemSubnoNew+= "                          "+isn;
            }
            return itemSubnoNew;
        }else{
            return "                          "+itemSubno;
        }
    }

    /**
     * @Description: 补齐长度
     * @Param: String field 参数字段,int n 所需长度
     * @return: String field
     * @Author: liuxingyu
     * @Date: 2024/4/22 9:50
     */
    public static String refillQuantity(String field, int n) {
        StringBuilder barcodeBuilder = new StringBuilder(field);
        while (barcodeBuilder.length() < n) {
            barcodeBuilder.append(" ");
        }
        return barcodeBuilder.toString();
    }

    /**
     * @Description: 对参数 user + UserKEY + timestamp
     * @Param: String userName,String userKey
     * @return: String sign
     * @Author: liuxingyu
     * @Date: 2024/4/20 9:13
     */
    public static String getSign(String userName, String userKey) {
        return HashSignUtil.sign(userName + userKey + getUnix());
    }

    /**
     * @Description: 当前 UNIX 时间戳，10 位，精确到秒
     * @Param:
     * @return:
     * @Author: liuxingyu
     * @Date: 2024/4/20 9:09
     */
    public static String getUnix() {
        // 获取当前时间的毫秒数
        long currentTimeMillis = System.currentTimeMillis();
        // 将毫秒数转换为秒数（10位 UNIX 时间戳）
        long unixTimeStamp = currentTimeMillis / 1000;
        return String.valueOf(unixTimeStamp);
    }

}
