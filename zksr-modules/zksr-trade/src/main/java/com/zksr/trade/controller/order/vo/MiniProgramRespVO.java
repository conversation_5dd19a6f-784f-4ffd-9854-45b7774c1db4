package com.zksr.trade.controller.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.trade.api.order.dto.TrdSupplierOrderDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MiniProgramRespVO {
    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long memberId;

    /**
     * 门店id
     */
    @ApiModelProperty("门店id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long branchId;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    /**
     * 门店地址
     */
    @ApiModelProperty("门店地址")
    private String branchAddr;

    /**
     * 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消
     */
    @ApiModelProperty("支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消")
    private Integer payState;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 优惠金额
     */
    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmt;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String memo;

    /**
     * 支付金额
     */
    @ApiModelProperty("支付金额")
    private BigDecimal payAmt;

    /**
     * 单据金额
     */
    @ApiModelProperty("单据金额")
    private BigDecimal orderAmt;

    /** 创建者 */
    @ApiModelProperty("创建者")
    private String createBy;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 入驻商订单列表
     */
    @ApiModelProperty("入驻商订单列表")
    private List<TrdSupplierOrderDTO> supplierOrderList;

    /**
     * 订单状态
     */
    @ApiModelProperty("订单状态")
    private Integer deliveryState;

    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    private String branchName;

    /**
     * 门店联系人
     */
    @ApiModelProperty("门店联系人")
    private String contactName;

    /**
     * 门店联系电话
     */
    @ApiModelProperty("门店联系电话")
    private String contactPhone;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private String payWay;

    /**
     * 门店头像
     */
    @ApiModelProperty("门店头像")
    private String branchUrl;

    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    private Long totalNum;

    /**
     * 合计金额
     */
    @ApiModelProperty("合计金额")
    private BigDecimal totalAmt;
}
