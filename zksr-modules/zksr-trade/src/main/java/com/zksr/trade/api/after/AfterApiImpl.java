package com.zksr.trade.api.after;

import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.domain.vo.openapi.TrdSupAfterDtlRequest;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.trade.api.after.dto.*;
import com.zksr.trade.api.after.vo.AfterOrderPageReqVO;
import com.zksr.trade.api.after.vo.OrderAfterRequest;
import com.zksr.trade.api.after.vo.OrderAfterSaveRequest;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.api.order.dto.ColonelAppOrderListTotalDTO;
import com.zksr.trade.api.order.dto.TrdOrderRespDTO;
import com.zksr.trade.api.order.vo.*;
import com.zksr.trade.api.after.vo.*;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.controller.after.dto.AfterPageRespDTO;
import com.zksr.trade.controller.after.vo.AfterPageReqVO;
import com.zksr.trade.convert.after.TrdSupplierAfterConvert;
import com.zksr.trade.convert.after.TrdSupplierAfterDtlConvert;
import com.zksr.trade.mapper.TrdAfterMapper;
import com.zksr.trade.mapper.TrdOrderExpressMapper;
import com.zksr.trade.service.ITrdAfterService;
import com.zksr.trade.service.ITrdOrderService;
import com.zksr.trade.service.ITrdSupplierAfterDtlService;
import com.zksr.trade.service.ITrdSupplierAfterService;
import com.zksr.trade.service.impl.TrdCacheServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年04月20日 10:47
 * @description: AfterApiImpl
 */
@RestController
@Slf4j
@ApiIgnore
public class AfterApiImpl implements AfterApi{
    @Autowired
    private ITrdAfterService trdAfterService;

    @Autowired
    private ITrdOrderService trdOrderService;

    @Autowired
    private TrdOrderExpressMapper trdOrderExpressMapper;

    @Autowired
    private TrdAfterMapper trdAfterMapper;

    @Autowired
    private ITrdSupplierAfterDtlService supplierAfterDtlService;

    @Autowired
    private ITrdSupplierAfterService supplierAfterService;

    @Override
    public CommonResult<OrderAfterResDTO> orderAfterInitiate(OrderAfterRequest request) {
        log.info("售后申请入参:{}", JSON.toJSONString(request));
        return CommonResult.success(trdOrderService.getOrderAfterInfoByOrderInfo(request));
    }

    @Override
    public CommonResult<List<BranchSkuMergeAfterResDTO>> branchSkuMergeAfterList(BranchSkuMergeAfterReqVO reqVo) {
        return CommonResult.success(trdOrderService.branchSkuMergeAfterList(reqVo));
    }

    @Override
    public CommonResult<OrderMergeAfterResDTO> orderMergeAfter(BranchSkuMergeAfterReqVO reqVo) {
        return CommonResult.success(trdOrderService.orderMergeAfter(reqVo));
    }

    @Override
    public CommonResult<OrderAfterSaveResDTO> saveAfterOrder(OrderAfterSaveRequest request) {
        return CommonResult.success(trdAfterService.saveOrderAfter(request));
    }

    @Override
    public CommonResult<OrderAfterSaveResDTO> saveMergeAfterOrder(OrderMergeAfterRequest request) {
        return CommonResult.success(trdAfterService.saveMergeAfterOrder(request));
    }

    @Override
    public PageResult<AfterOrderPageRespDTO> pageAfter(AfterOrderPageReqVO reqVO) {
        return trdAfterService.pageAfter(reqVO);
    }

    @Override
    public CommonResult<AfterOrderPageRespDTO> getAfterOrderInfo(AfterOrderPageReqVO reqVO) {
        PageResult<AfterOrderPageRespDTO> pageResult = trdAfterService.pageAfter(reqVO);
        if (pageResult.getList().isEmpty()){
            return CommonResult.success(trdAfterService.pageAfter(reqVO).getList().get(NumberPool.INT_ZERO));
        } else {
            return CommonResult.success(new AfterOrderPageRespDTO());
        }
    }

    @Override
    public CommonResult<ColonelAppOrderListTotalDTO> selectMemColonelAppAfterOrder(TrdColonelAppOrderListPageReqVO orderPageReqVO) {
        ColonelAppOrderListTotalDTO result = trdAfterService.selectMemColoneAppAfterOrderListTotal(orderPageReqVO);
        if (ToolUtil.isNotEmpty(result) && result.getNumTotal() > 0) {
            orderPageReqVO.setPageNo((orderPageReqVO.getPageNo() - 1) * orderPageReqVO.getPageSize());
            List<AfterOrderPageRespDTO> list = trdAfterService.selectMemColoneAppAfterOrderNew(orderPageReqVO);
            result.setList(list);
        } else {
            result.setList(new ArrayList<>());
            result.setAmtTotal(BigDecimal.ZERO);
        }
        result.setTotal(result.getNumTotal());
        return CommonResult.success(result);
    }

    @Override
    public List<TrdColonelAppOrderDetailRespVO> getMemColonelAppAfterOrderDetail(Long afterId) {
        return trdAfterService.getMemColoneAppAfterOrderDetail(afterId);
    }

    @Override
    public AfterApproveDtlEditVO getAfterDtlByDtlId(TrdSupAfterDtlRequest request) {
        return trdAfterService.getAfterDtlByDtlId(request);
    }

    @Override
    public CommonResult<Boolean> uploadAfterExpressDelivery(OrderAfterUploadExpressSaveVO expressSaveVO) {
        trdAfterService.uploadAfterExpressDelivery(expressSaveVO);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> cancelAfterReturn(Long afterId) {
        trdAfterService.cancelAfterReturn(afterId);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> afterRefundSuccessCallback(PayRefundVO refundVO) {
        log.info("收到售后退款成功信息, notify : {}", JSON.toJSONString(refundVO));
        trdAfterService.afterRefundSuccessCallback(refundVO);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> afterRefundFailCallback(PayRefundVO refundVO) {
        log.info("收到售后退款失败信息, notify : {}", JSON.toJSONString(refundVO));
        trdAfterService.afterRefundFailCallback(refundVO);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> approveAfterReturn(AfterApproveDtlEditVO editVO) {
        trdAfterService.approveAfterReturn(editVO);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<TrdAfter> getAfterByAfterNo(String afterNo) {
        return CommonResult.success(trdAfterService.getTrdAfterByAfterNo(afterNo));
    }

    /**
     * 根据售后单明细id查询物流信息
     * @param supplierAfterDtlId
     * @return
     */
    @Override
    public TrdOrderExpress getOrderExpressBySupAfterDtlId(Long supplierAfterDtlId) {
        return trdOrderExpressMapper.getOrderExpressBySupAfterDtlId(supplierAfterDtlId);
    }

    @Override
    public CommonResult<TrdAfter> getAfterById(Long afterId) {
        return CommonResult.success(trdAfterService.getTrdAfterById(afterId));
    }

    @Override
    public CommonResult<Boolean> approveAfterRefund(AfterApproveDtlEditVO editVO) {
        trdAfterService.approveAfterRefund(editVO);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> checkNotFinishAfterByOrderId(Long orderId) {
        Integer unfinishedAfterCount = trdAfterService.getUnfinishedAfterByOrderId(orderId);
        if (unfinishedAfterCount > NumberPool.INT_ZERO) {
            return CommonResult.success(true);
        }
        return CommonResult.success(false);
    }

    @Override
    public CommonResult<List<Map<String, Object>>> getPendingRefundAmount(String currentMonthId, List<String> branchIds) {
        return CommonResult.success(trdAfterMapper.getPendingRefundAmount(currentMonthId,branchIds));
    }

    @Override
    public CommonResult<List<Map<String, Object>>> getColonelPendingRefundAmount(String currentMonthId, List<String> colonelIds) {
        return CommonResult.success(trdAfterMapper.getColonelPendingRefundAmount(currentMonthId,colonelIds));
    }

    @Override
    public CommonResult<List<SupplierAfterDtlDTO>> getAfterDtlListById(List<Long> supplierAfterDtlIdList) {
        return CommonResult.success(TrdSupplierAfterDtlConvert.INSTANCE.convertDTOList(supplierAfterDtlService.getTrdSupplierAfterDtlBatch(supplierAfterDtlIdList)));
    }

    @Override
    public CommonResult<List<SupplierAfterDtlDTO>> getAfterDtlListBySupplierAfterId(Long supplierAfterId) {
        return CommonResult.success(TrdSupplierAfterDtlConvert.INSTANCE.convertDTOList(supplierAfterDtlService.getAfterDtlListBySupplierAfterId(supplierAfterId)));
    }

    @Override
    public CommonResult<TrdSupplierAfterDTO> getSupplierAfter(Long supplierAfterId) {
        return CommonResult.success(TrdSupplierAfterConvert.INSTANCE.convertDTO(supplierAfterService.getTrdSupplierAfter(supplierAfterId)));
    }

    @Override
    public CommonResult<BigDecimal> getColonelAppPageAfterAmt(Long colonelId) {
        return CommonResult.success(trdAfterService.getColonelAppPageAfterAmt(colonelId));
    }

    @Override
    public CommonResult<List<AfterOrderExportVO>> getAfterExportPage(AfterOrderPageReqVO pageVO) {
        AfterPageReqVO pageReqVO = BeanUtils.toBean(pageVO, AfterPageReqVO.class);
        PageResult<AfterPageRespDTO> trdAfterPage = trdAfterService.getTrdAfterPage(pageReqVO);
        PageResult<AfterOrderExportVO> afterOrderResult = HutoolBeanUtils.toBean(trdAfterPage, AfterOrderExportVO.class);
        return CommonResult.success(afterOrderResult.getList());
    }
}
