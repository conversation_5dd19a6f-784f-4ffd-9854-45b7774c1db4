package com.zksr.trade.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.supplierOrder.dto.OrderDiscountDtlDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdOrderDiscountDtlMapper;
import com.zksr.trade.service.ITrdOrderDiscountDtlService;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 订单优惠明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Service
public class TrdOrderDiscountDtlServiceImpl implements ITrdOrderDiscountDtlService {
    @Autowired
    private TrdOrderDiscountDtlMapper trdOrderDiscountDtlMapper;

    @Override
    public List<OrderDiscountDtlDTO> getListBySupplierOrderId(Long supplierOrderId) {
        return HutoolBeanUtils.toBean(trdOrderDiscountDtlMapper.getListBySupplierOrderId(supplierOrderId),OrderDiscountDtlDTO.class);
    }
}
