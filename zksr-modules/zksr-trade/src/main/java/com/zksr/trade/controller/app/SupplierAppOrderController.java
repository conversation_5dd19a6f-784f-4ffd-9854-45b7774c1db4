package com.zksr.trade.controller.app;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.trade.api.order.dto.TrdOrderPageReqDTO;
import com.zksr.trade.api.orderSettle.dto.OrderSupplierSettleResDTO;
import com.zksr.trade.api.orderSettle.vo.OrderSupplierSettlePageVo;
import com.zksr.trade.controller.order.vo.HomePageReqVO;
import com.zksr.trade.controller.order.vo.HomePageRespVO;
import com.zksr.trade.controller.order.vo.MiniProgramRespVO;
import com.zksr.trade.controller.order.vo.TrdOrderOperVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderRespVO;
import com.zksr.trade.service.ITrdOrderService;
import com.zksr.trade.service.ITrdSettleService;
import com.zksr.trade.service.ITrdSupplierOrderDtlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * @Description: 入驻商小程序订单Controller
 * @Author: liuxingyu
 * @Date: 2024/4/13 17:06
 */
@Api(tags = "入驻商小程序 - 订单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/supplierAppOrder")
public class SupplierAppOrderController {

    @Autowired
    private ITrdOrderService trdOrderService;

    @Autowired
    private ITrdSupplierOrderDtlService trdSupplierOrderDtlService;

    @Autowired
    private ITrdSettleService trdSettleService;

    /**
     * @Description: 获取首页数据
     * @Param: HomePageReqVo homePageReqVo
     * @return:
     * @Author: liuxingyu
     * @Date: 2024/3/22 16:15
     */
    @ApiOperation(value = "获取首页数据", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + "trade:supplierAppOrder:getHomePage")
    @RequiresPermissions("trade:supplierAppOrder:getHomePage")
    @GetMapping("/getHomePage")
    public CommonResult<HomePageRespVO> getHomePage(HomePageReqVO homePageReqVo) {
        return success(trdOrderService.getHomePage(homePageReqVo));
    }

    /**
     * @Description: 获取入驻商小程序订单分页列表
     * @Param: TrdOrderPageReqDTO
     * @return: CommonResult<PageResult < MiniProgramRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/4/8 11:02
     */
    @ApiOperation(value = "获取入驻商小程序订单分页列表", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + "trade:supplierAppOrder:getMerchantMiniProgramOrderPageList")
    @RequiresPermissions("trade:supplierAppOrder:getMerchantMiniProgramOrderPageList")
    @PostMapping("/getMerchantMiniProgramOrderPageList")
    public CommonResult<PageResult<MiniProgramRespVO>> getMerchantMiniProgramOrderPageList(@RequestBody TrdOrderPageReqDTO trdOrderPageReqDTO) {
        return success(trdOrderService.getMerchantMiniProgramOrderPageList(trdOrderPageReqDTO));
    }

    /**
     * @Description: 获取入驻商小程序订单详情
     * @Param: Long orderId
     * @return: CommonResult<TrdSupplierOrderRespVO>
     * @Author: liuxingyu
     * @Date: 2024/4/11 9:48
     */
    @ApiOperation(value = "获取入驻商小程序订单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + "trade:supplierAppOrder:getSupplierOrderDtl")
    @RequiresPermissions("trade:supplierAppOrder:getSupplierOrderDtl")
    @GetMapping("/getSupplierOrderDtl")
    public CommonResult<List<TrdSupplierOrderRespVO>> getSupplierOrderDtl(@RequestParam("orderId") Long orderId) {
        return success(trdSupplierOrderDtlService.getSupplierOrderDtl(orderId));
    }


    /**
     * 获取订单登录入驻商的订单结算数据
     * @return
     */
    @PostMapping("/getSupplierOrderSettle")
    @ApiOperation(value = "获取订单登录入驻商的订单结算数据", httpMethod = "GET", notes = Permissions.GETSUPPLIERORDERSETTLE)
    @RequiresPermissions(Permissions.GETSUPPLIERORDERSETTLE)
    public CommonResult<List<OrderSupplierSettleResDTO>> getSupplierOrderSettle(@RequestBody OrderSupplierSettlePageVo pageReqVO){
        pageReqVO.setSupplierId(SecurityUtils.getSupplierId());
        return success(trdSettleService.getSupperOrderSettleByDate(pageReqVO));
    }

    /**
     * 获取订单登录入驻商的订单结算数据明细
     * @return
     */
    @PostMapping("/getSupplierOrderSettleDtl")
    @ApiOperation(value = "获取订单登录入驻商的订单结算数据明细", httpMethod = "GET", notes = Permissions.GETSUPPLIERORDERSETTLE)
    @RequiresPermissions(Permissions.GETSUPPLIERORDERSETTLE)
    public CommonResult<List<OrderSupplierSettleResDTO>> getSupplierOrderSettleDtl(@RequestBody OrderSupplierSettlePageVo pageReqVO){
        pageReqVO.setSupplierId(SecurityUtils.getSupplierId());
        return success(trdSettleService.getSupperOrderSettleDtlByDateTime(pageReqVO));
    }

    /**
     * 订单发货出库
     */
    @ApiOperation(value = "订单发货出库", httpMethod = HttpMethod.POST)
    @PostMapping("/orderOutbound")
    public CommonResult<Boolean> orderOutbound(@RequestBody TrdOrderOperVO trdOrderOperVO) {
        trdOrderOperVO.setSupplierId(SecurityUtils.getSupplierId());
        trdOrderService.orderOutbound(trdOrderOperVO);
        return success(true);
    }

    /**
     * 订单装车
     */
    @ApiOperation(value = "订单装车", httpMethod = HttpMethod.POST)
    @PostMapping("/orderEntrucking")
    public CommonResult<Boolean> orderEntrucking(@RequestBody TrdOrderOperVO trdOrderOperVO) {
        trdOrderOperVO.setSupplierId(SecurityUtils.getSupplierId());
        trdOrderService.orderEntrucking(trdOrderOperVO);
        return success(true);
    }

    /**
     * 订单取消装车
     */
    @ApiOperation(value = "订单取消装车", httpMethod = HttpMethod.POST)
    @PostMapping("/orderCancelEntrucking")
    public CommonResult<Boolean> orderCancelEntrucking(@RequestBody TrdOrderOperVO trdOrderOperVO) {
        trdOrderOperVO.setSupplierId(SecurityUtils.getSupplierId());
        trdOrderService.orderCancelEntrucking(trdOrderOperVO);
        return success(true);
    }

    /**
     * 单据配送完成（收货）
     */
    @ApiOperation(value = "单据配送完成（收货）", httpMethod = HttpMethod.POST)
    @PostMapping("/orderTakeDelivery")
    public CommonResult<Boolean> orderTakeDelivery(@RequestBody TrdOrderOperVO trdOrderOperVO) {
        trdOrderOperVO.setSupplierId(SecurityUtils.getSupplierId());
        trdOrderService.orderTakeDelivery(trdOrderOperVO);
        return success(true);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 入驻商的订单结算数据查询 */
        public static final String GETSUPPLIERORDERSETTLE = "trade:supplierAppOrder:getSupplierOrderSettle";
    }
}
