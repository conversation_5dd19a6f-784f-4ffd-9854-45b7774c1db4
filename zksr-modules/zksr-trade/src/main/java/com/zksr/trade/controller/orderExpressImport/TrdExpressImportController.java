package com.zksr.trade.controller.orderExpressImport;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlRespVO;
import com.zksr.trade.convert.orderExpressImport.TrdExpressImportDtlConvert;
import com.zksr.trade.domain.TrdExpressImportDtl;
import com.zksr.trade.service.ITrdExpressImportDtlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdExpressImport;
import com.zksr.trade.service.ITrdExpressImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportPageReqVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportRespVO;
import com.zksr.trade.convert.orderExpressImport.TrdExpressImportConvert;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 快递导入记录Controller
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Api(tags = "管理后台 - 快递导入记录接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/orderExpressImport")
@Slf4j
public class TrdExpressImportController {
    @Autowired
    private ITrdExpressImportService trdExpressImportService;
    @Autowired
    private ITrdExpressImportDtlService trdExpressImportDtlService;



    /**
     * 根据导入id和状态查询成功或失败数据
     */
    @ApiOperation(value = "根据导入id和状态查询成功或失败数据", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/getExpressImportInfo")
    public CommonResult<List<TrdExpressImportDtlRespVO>> getExpressImportInfo(@RequestParam("expressImportId") Long expressImportId, @RequestParam("status") Integer status) {
        List<TrdExpressImportDtl> trdExpressImports = trdExpressImportDtlService.getExpressImportInfo(expressImportId, status);
        return success(TrdExpressImportDtlConvert.INSTANCE.convertList(trdExpressImports));
    }

    /**
     * 分页查询快递导入记录
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得快递导入记录分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdExpressImportRespVO>> getPage(@Valid TrdExpressImportPageReqVO pageReqVO) {
        String supplierId = SecurityUtils.getSupplierId()+"";
        if (supplierId != null && !supplierId.isEmpty()) {
            pageReqVO.setSupplierId(supplierId);
        }
        PageResult<TrdExpressImport> pageResult = trdExpressImportService.getTrdExpressImportPage(pageReqVO);
        return success(TrdExpressImportConvert.INSTANCE.convertPage(pageResult));
    }




    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:import:add";
        /** 编辑 */
        public static final String EDIT = "trade:import:edit";
        /** 删除 */
        public static final String DELETE = "trade:import:remove";
        /** 列表 */
        public static final String LIST = "trade:import:list";
        /** 查询 */
        public static final String GET = "trade:import:query";
        /** 停用 */
        public static final String DISABLE = "trade:import:disable";
        /** 启用 */
        public static final String ENABLE = "trade:import:enable";
    }
}
