package com.zksr.trade.service;

import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveReqVO;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveRespVO;
import com.zksr.trade.domain.TrdHdfkPay;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayPageReqVO;

import java.util.List;

/**
 * 货到付款付款单Service接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface ITrdHdfkPayService {

    /**
     * 直接新增货到付款
     * @param createReqVO
     * @return
     */
    Long insertTrdHdfkPay(HdfkPaySaveReqVO createReqVO);

    /**
     * 第三方调用新增货到付款
     * @param createReqVO
     * @return
     */
    Long saveThirdPartyTrdHdfkPay(HdfkPaySaveReqVO createReqVO);

    /**
     * 创建货到付款单
     * @param paySaveReqVO
     * @return
     */
    HdfkPaySaveRespVO createPay(HdfkPaySaveReqVO paySaveReqVO);

    /**
     * 货到付款支付成功回调
     * @param orderNo       订单号
     * @param payPlatform   支付平台
     * @param payWay        支付方式
     */
    void orderPaySuccessCallback(String orderNo, String payPlatform, String payWay);

    /**
     * 获取货到付款记录列表
     * @param pageReqVO
     * @return
     */
    PageResult<TrdHdfkPay> getTrdHdfkPayPage(TrdHdfkPayPageReqVO pageReqVO);

    /**
     * 获取货到付款单
     * @param hdfkPayId
     * @return
     */
    TrdHdfkPay getTrdHdfkPay(Long hdfkPayId);

    /**
     * 获取货到付款单集合
     *
     * @param hdfkPayIdList
     * @return
     */
    List<TrdHdfkPay> getTrdHdfkPayListByIds(List<Long> hdfkPayIdList);
}
