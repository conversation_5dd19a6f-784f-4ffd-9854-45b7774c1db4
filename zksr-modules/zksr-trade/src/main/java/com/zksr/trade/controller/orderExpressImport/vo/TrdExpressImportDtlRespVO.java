package com.zksr.trade.controller.orderExpressImport.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import java.util.Date;


/**
 * 快递导入明细对象 trd_express_import_dtl
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Data
@ApiModel("快递导入明细 - trd_express_import_dtl Response VO")
public class TrdExpressImportDtlRespVO {
    private static final long serialVersionUID = 1L;

    /** 快递导入明细id */
    @ApiModelProperty(value = "快递导入明细id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long expressImportDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 状态0成功 1失败 */
    @Excel(name = "状态0成功 1失败")
    @ApiModelProperty(value = "状态0成功 1失败")
    private Integer status;

    /** 订单id;订单id */
    @Excel(name = "订单id;订单id")
    @ApiModelProperty(value = "订单id;订单id")
    private Long orderId;

    /** 订单编号;订单编号 */
    @Excel(name = "订单编号;订单编号")
    @ApiModelProperty(value = "订单编号;订单编号")
    private String orderNo;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    @ApiModelProperty(value = "入驻商订单明细id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    @ApiModelProperty(value = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 快递单号 */
    @Excel(name = "快递单号")
    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    /** 快递公司 */
    @Excel(name = "快递公司")
    @ApiModelProperty(value = "快递公司")
    private String expressCom;

    /** 快递公司编码 */
    @Excel(name = "快递公司编码")
    @ApiModelProperty(value = "快递公司编码")
    private String expressComNo;

    /** 失败原因 */
    @Excel(name = "失败原因")
    @ApiModelProperty(value = "失败原因")
    private String failReason;

    /** 快递导入记录id */
    @Excel(name = "快递导入记录id")
    @ApiModelProperty(value = "快递导入记录id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long expressImportId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

}
