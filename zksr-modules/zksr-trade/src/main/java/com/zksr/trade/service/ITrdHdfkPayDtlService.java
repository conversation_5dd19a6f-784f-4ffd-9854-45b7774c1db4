package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.domain.TrdHdfkPayDtl;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayDtlPageReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayDtlSaveReqVO;

/**
 * 货到付款付款单明细Service接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface ITrdHdfkPayDtlService {

    /**
     * 新增货到付款付款单明细
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdHdfkPayDtl(@Valid TrdHdfkPayDtlSaveReqVO createReqVO);

    /**
     * 修改货到付款付款单明细
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdHdfkPayDtl(@Valid TrdHdfkPayDtlSaveReqVO updateReqVO);

    /**
     * 删除货到付款付款单明细
     *
     * @param hdfkPayDtlId 货到付款付款单id
     */
    public void deleteTrdHdfkPayDtl(Long hdfkPayDtlId);

    /**
     * 批量删除货到付款付款单明细
     *
     * @param hdfkPayDtlIds 需要删除的货到付款付款单明细主键集合
     * @return 结果
     */
    public void deleteTrdHdfkPayDtlByHdfkPayDtlIds(Long[] hdfkPayDtlIds);

    /**
     * 获得货到付款付款单明细
     *
     * @param hdfkPayDtlId 货到付款付款单id
     * @return 货到付款付款单明细
     */
    public TrdHdfkPayDtl getTrdHdfkPayDtl(Long hdfkPayDtlId);

    /**
     * 获得货到付款付款单明细分页
     *
     * @param pageReqVO 分页查询
     * @return 货到付款付款单明细分页
     */
    PageResult<TrdHdfkPayDtl> getTrdHdfkPayDtlPage(TrdHdfkPayDtlPageReqVO pageReqVO);

}
