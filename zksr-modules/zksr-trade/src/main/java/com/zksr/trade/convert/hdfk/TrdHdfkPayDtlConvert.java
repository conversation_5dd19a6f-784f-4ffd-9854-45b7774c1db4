package com.zksr.trade.convert.hdfk;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdHdfkPayDtl;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayDtlRespVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayDtlSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 货到付款付款单明细 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-28
*/
@Mapper
public interface TrdHdfkPayDtlConvert {

    TrdHdfkPayDtlConvert INSTANCE = Mappers.getMapper(TrdHdfkPayDtlConvert.class);

    TrdHdfkPayDtlRespVO convert(TrdHdfkPayDtl trdHdfkPayDtl);

    TrdHdfkPayDtl convert(TrdHdfkPayDtlSaveReqVO trdHdfkPayDtlSaveReq);

    PageResult<TrdHdfkPayDtlRespVO> convertPage(PageResult<TrdHdfkPayDtl> trdHdfkPayDtlPage);
}