package com.zksr.trade.controller.invoice.vo;

import java.math.BigDecimal;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 发票操作历史（含开票信息冗余）对象 trd_invoice_record
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@ApiModel("发票操作历史（含开票信息冗余） - trd_invoice_record分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdInvoiceRecordPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "操作备注")
    private Long id;

    /** 发票主表ID */
    @Excel(name = "发票主表ID")
    @ApiModelProperty(value = "发票主表ID", required = true)
    private Long invoiceId;

    /** 上游传入唯一流水号 */
    @Excel(name = "上游传入唯一流水号")
    @ApiModelProperty(value = "上游传入唯一流水号", required = true)
    private String bizId;

    /** 主业务单号 */
    @Excel(name = "主业务单号")
    @ApiModelProperty(value = "主业务单号")
    private String businessNo;

    /** 开票渠道:1：入驻商开票,2：美的付开票 */
    @Excel(name = "开票渠道:1：入驻商开票,2：美的付开票")
    @ApiModelProperty(value = "开票渠道:1：入驻商开票,2：美的付开票", required = true)
    private Long channel;

    /** 开票场景:1：佣金发票,2：订单发票 */
    @Excel(name = "开票场景:1：佣金发票,2：订单发票")
    @ApiModelProperty(value = "开票场景:1：佣金发票,2：订单发票", required = true)
    private Long businessType;

    /** 操作类型：CREATE-创建，UPDATE-更新，VOID-作废，ATTACHMENT_CALLBACK-附件回调 */
    @Excel(name = "操作类型：CREATE-创建，UPDATE-更新，VOID-作废，ATTACHMENT_CALLBACK-附件回调")
    @ApiModelProperty(value = "操作类型：CREATE-创建，UPDATE-更新，VOID-作废，ATTACHMENT_CALLBACK-附件回调", required = true)
    private String operationType;

    /** 处理状态 */
    @Excel(name = "处理状态:1、未处理，2、处理中、3、已处理")
    @ApiModelProperty(value = "处理状态:1、未处理，2、处理中、3、已处理")
    private Integer processStatus;


    /** 0非直接开票、1直接开票 */
    @Excel(name = "0非直接开票、1直接开票")
    @ApiModelProperty(value = "0非直接开票、1直接开票")
    private Long immediateInvoice;

    /** 发票类型：1-电子普票 2-电子专票 3-纸质普票 4-纸质专票 */
    @Excel(name = "发票类型：1-电子普票 2-电子专票 3-纸质普票 4-纸质专票")
    @ApiModelProperty(value = "发票类型：1-电子普票 2-电子专票 3-纸质普票 4-纸质专票")
    private Long invoiceType;

    /** 发票含税总金额 */
    @Excel(name = "发票含税总金额")
    @ApiModelProperty(value = "发票含税总金额")
    private BigDecimal invoiceAmt;

    /** 发票抬头：1个人 2企业 */
    @Excel(name = "发票抬头：1个人 2企业")
    @ApiModelProperty(value = "发票抬头：1个人 2企业")
    private Long invoiceHead;

    /** 销方税号 */
    @Excel(name = "销方税号")
    @ApiModelProperty(value = "销方税号")
    private String taxpayerCode;

    /** 购方税号 */
    @Excel(name = "购方税号")
    @ApiModelProperty(value = "购方税号")
    private String payTaxpayerCode;

    /** 购方名称 */
    @Excel(name = "购方名称")
    @ApiModelProperty(value = "购方名称")
    private String payTaxpayerName;

    /** 购方地址 */
    @Excel(name = "购方地址")
    @ApiModelProperty(value = "购方地址")
    private String payUnitAddress;

    /** 购方电话 */
    @Excel(name = "购方电话")
    @ApiModelProperty(value = "购方电话")
    private String payFixedPhoneNumber;

    /** 购方银行信息（名称+账号） */
    @Excel(name = "购方银行信息", readConverterExp = "名=称+账号")
    @ApiModelProperty(value = "购方银行信息")
    private String payBankInfo;

    /** 购方邮箱 */
    @Excel(name = "购方邮箱")
    @ApiModelProperty(value = "购方邮箱")
    private String mail;

    /** 票面备注 */
    @Excel(name = "票面备注")
    @ApiModelProperty(value = "票面备注")
    private String remarks;

    /** 客户备注 */
    @Excel(name = "客户备注")
    @ApiModelProperty(value = "客户备注")
    private String customerRemarks;

    /** 操作人 */
    @Excel(name = "操作人")
    @ApiModelProperty(value = "操作人")
    private String operator;

    /** 操作备注 */
    @Excel(name = "操作备注")
    @ApiModelProperty(value = "操作备注")
    private String operationRemark;


}
