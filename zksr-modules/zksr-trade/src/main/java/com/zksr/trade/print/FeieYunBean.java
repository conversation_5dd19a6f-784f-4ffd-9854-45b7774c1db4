package com.zksr.trade.print;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.partnerPolicy.dto.FeieYunSettingPolicyDTO;
import com.zksr.trade.print.vo.PrintSupplierOrderDtlVO;
import com.zksr.trade.print.vo.PrintSupplierOrderVO;
import com.zksr.trade.service.ITrdOrderService;
import com.zksr.trade.service.TrdCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 飞鹅打印bean
 * @Author: liuxingyu
 * @Date: 2024/4/25 14:49
 */

@Component
@Slf4j
public class FeieYunBean {
    private static final String URL = "http://api.feieyun.cn/Api/Open/";

    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private ITrdOrderService trdOrderService;

    //二维码跳转url
    private static final String orderUrl = "https://b2badmin.zksr.cn";

    /**
     * @Description: 打印功能
     * @Author: liuxingyu
     * @Date: 2024/4/25 14:50
     */
    public void PrintOrder(Long orderId, Long sysCode, Long supplierOrderId) {
        //通过订单id获取订单信息
        List<PrintSupplierOrderVO> printSupplierOrderVO = trdOrderService.printGetByOrderId(orderId, sysCode, supplierOrderId);
        if (ObjectUtil.isEmpty(printSupplierOrderVO)) {
            log.error("打印订单异常:未获取到订单信息,订单id{}", orderId);
            return;
        }
        log.info("进入飞鹅打印====================================================");
        //获取单位字典信息
        List<SysDictData> sysDictDataList = DictUtils.getDictCache("sys_prdt_unit");
        Map<String, String> sysDictDataMap = sysDictDataList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        //获取支付方式字典信息
        List<SysDictData> payWayList = DictUtils.getDictCache("sys_pay_way");
        Map<String, String> payWayMap = payWayList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        //打印每个入驻商
        for (PrintSupplierOrderVO supplierOrderVO : printSupplierOrderVO) {
            //获取入驻商配置信息
            FeieYunSettingPolicyDTO feieYunSettingPolicyDTO = trdCacheService.getFeieYunSettingPolicyDTO(supplierOrderVO.getSupplierId());
            if (ObjectUtil.isNull(feieYunSettingPolicyDTO)) {
                log.error("飞鹅打印机未获取到配置,入驻商ID为{}", supplierOrderVO.getSupplierId());
                return;
            }
            //业务员信息
            ColonelDTO colonelDTO = ObjectUtil.isNull(supplierOrderVO.getColonelId()) ? null : trdCacheService.getColonelDTO(supplierOrderVO.getColonelId());
            String coloneName = ObjectUtil.isNotNull(colonelDTO) ? colonelDTO.getColonelName() : " ";
            String colonePhone = ObjectUtil.isNotNull(colonelDTO) ? colonelDTO.getColonelPhone() : " ";
            //门店信息
            BranchDTO branchDTO = trdCacheService.getBranchDTO(supplierOrderVO.getBranchId());
            String contactName = " ";
            String contactPhone = " ";
            String branchAddr = " ";
            String area = " ";
            if (ObjectUtil.isNotNull(branchDTO)) {
                contactName = branchDTO.getContactName();
                contactPhone = branchDTO.getContactPhone();
                branchAddr = branchDTO.getBranchAddr();
                AreaDTO areaDTO = trdCacheService.getByAreaId(branchDTO.getAreaId());
                area = ObjectUtil.isNull(areaDTO) ? " " : areaDTO.getAreaName();
            }
            StringBuilder stringBuilder = new StringBuilder();
            //获取支付方式
            //获取单位
            String payWay = payWayMap.get(String.valueOf(supplierOrderVO.getPayWay()));
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "<BR><BR><C><B><BOLD>--" + payWay + "--</BOLD></B></C><BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "<BR><C><B><BOLD>正品低价进好货</BOLD></B></C><BR><BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "订单时间：").append(DateUtil.format(supplierOrderVO.getCreateTime(), "yyyy-MM-dd HH:mm:ss")).append("<BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "订单编号：").append(supplierOrderVO.getSupplierOrderNo()).append("<BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "所属区域：").append(area).append("<BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "生意顾问客户经理：").append(coloneName).append("<BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "生意顾问联系号码：").append(colonePhone).append("<BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "<B>姓  名：").append(contactName).append("</B><BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "<B>电  话：").append(contactPhone).append("</B><BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "门店名称：").append(branchDTO.getBranchName()).append("<BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "收货地址：").append(branchAddr).append("<BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "配送方式：").append("请确认地址").append("<BR>");

            //商品信息
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "<C>---------------------商品---------------------<BR></C>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, " 商品名称        数量        原价        金额 <BR>");
            List<PrintSupplierOrderDtlVO> supplierOrderDtlVOList = supplierOrderVO.getSupplierOrderDtlVOList();
            int sumNumber = 0;
            BigDecimal sumAmt = new BigDecimal(0);
            BigDecimal sumActualAmt = new BigDecimal(0);
            for (PrintSupplierOrderDtlVO orderDtl : supplierOrderDtlVOList) {
                if (ObjectUtil.isNull(orderDtl)) {
                    continue;
                }
                //Spu对象
                SpuDTO spuDTO = trdCacheService.getSpuDTO(orderDtl.getSpuId());
                String supName = ObjectUtil.isNotNull(spuDTO) ? spuDTO.getSpuName() : "";
                //获取单位
                String unit = sysDictDataMap.get(String.valueOf(orderDtl.getOrderUnit()));

                appendContent(stringBuilder, feieYunSettingPolicyDTO, "<BOLD>");
                //填充品名(商品名称 + 规格数量 + 规格单位 + 生产日期(老) )
                appendContent(stringBuilder, feieYunSettingPolicyDTO, supName + "*" + orderDtl.getOrderUnitSize() + unit + updateDate(spuDTO.getOldestDate()));
                appendContent(stringBuilder, feieYunSettingPolicyDTO, "</BOLD>");
                appendContent(stringBuilder, feieYunSettingPolicyDTO, "<BR>");
                //填充数量/单价/金额
                appendContent(stringBuilder, feieYunSettingPolicyDTO, XpYunUtil.refillQuantity("", 17));
                appendContent(stringBuilder, feieYunSettingPolicyDTO, "<BOLD>");
                //计算数量 (订单数量-取消数量)/订单换算数量
                long num = (orderDtl.getTotalNum() - orderDtl.getCancelQty()) / orderDtl.getOrderUnitSize();
                appendContent(stringBuilder, feieYunSettingPolicyDTO, XpYunUtil.refillQuantity("X" + num, 11));
                appendContent(stringBuilder, feieYunSettingPolicyDTO, XpYunUtil.refillQuantity(orderDtl.getOrderUnitPrice().setScale(2, RoundingMode.HALF_UP).toPlainString(), 11));
                //计算金额 数量*原价
                BigDecimal numBigDecimal = new BigDecimal(num);
                BigDecimal dtlSumAmt = numBigDecimal.multiply(orderDtl.getOrderUnitPrice()).setScale(2, RoundingMode.HALF_UP);
                appendContent(stringBuilder, feieYunSettingPolicyDTO, dtlSumAmt + "元").append("<BR>");
                appendContent(stringBuilder, feieYunSettingPolicyDTO, "</BOLD>");
                // 合计数量/金额/实际付款金额
                sumNumber += num;
                sumAmt = sumAmt.add(dtlSumAmt);
                sumActualAmt = sumActualAmt.add(orderDtl.getTotalAmt().subtract(orderDtl.getCancelAmt()));
            }
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "<C>————————————————————————<BR></C>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "<BOLD>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "商品总数：<RIGHT>" + sumNumber + "</RIGHT><BR>");
            //计算总金额 原价总和
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "原价总计:<RIGHT>" + sumAmt + "元</RIGHT><BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "************************************************<BR>");
            // 订单金额(total_amt)-取消金额(cancel_amt)
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "实付金额:<RIGHT>" + sumActualAmt + "元</RIGHT><BR>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "</BOLD>");
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "<C>————————————————————————<BR></C>");
            //跳转路径拼接参数
            String builderUrl = orderUrl + "?orderNo=" + supplierOrderVO.getOrderNo() +
                    "&supplierOrderNo=" + supplierOrderVO.getSupplierOrderNo() +
                    "&payWay=" + supplierOrderVO.getPayWay() +
                    "&branchId=" + supplierOrderVO.getBranchId();
            appendContent(stringBuilder, feieYunSettingPolicyDTO, "<C><B><BOLD><QR>").append(builderUrl).append("</QR></BOLD></B></C>");
            //根据配置的打印次数循环打印
            String quantity = feieYunSettingPolicyDTO.getQuantity();
            if (StringUtils.isBlank(quantity)){
                //如果未配置默认打印一次
                print(feieYunSettingPolicyDTO, stringBuilder.toString());
            }else{
                Integer count  = Integer.valueOf(quantity);
                for (int j = 0; j < count; j++) {
                    print(feieYunSettingPolicyDTO, stringBuilder.toString());
                }
            }
        }
    }

    /**
     * @Description: 处理时间
     * @Author: liuxingyu
     * @Date: 2024/8/12 15:41
     */
    private static String updateDate(Date date) {
        if (ObjectUtil.isNull(date)) {
            return " ";
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy'年'M'月'");
        return simpleDateFormat.format(date);
    }

    /**
     * @Description: 追加字符串
     * @Author: liuxingyu
     * @Date: 2024/4/24 18:49
     */
    private static StringBuilder appendContent(StringBuilder content, FeieYunSettingPolicyDTO feieYunSettingPolicyDTO, String contentString) {
        if (ObjectUtil.equal(null, content)) {
            content = new StringBuilder();
        }
        StringBuilder oldString = content;
        StringBuilder newString = content.append(contentString);
        if (newString.toString().getBytes().length > 4096) {
            print(feieYunSettingPolicyDTO, oldString.toString());
            content.setLength(0);
            content.append(contentString);
        }
        //手动置null
        oldString = null;
        newString = null;
        return content;
    }


    /**
     * @Description: 打印
     * @Author: liuxingyu
     * @Date: 2024/4/24 18:33
     */
    private static void print(FeieYunSettingPolicyDTO feieYunSettingPolicyDTO, String content) {
        //通过POST请求，发送打印信息到服务器
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(30000)//读取超时
                .setConnectTimeout(30000)//连接超时
                .build();

        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .build();

        HttpPost post = new HttpPost(URL);
        List<NameValuePair> nvps = new ArrayList<NameValuePair>();
        nvps.add(new BasicNameValuePair("user", feieYunSettingPolicyDTO.getUser()));
        String STIME = String.valueOf(System.currentTimeMillis() / 1000);
        nvps.add(new BasicNameValuePair("stime", STIME));
        nvps.add(new BasicNameValuePair("sig", signature(feieYunSettingPolicyDTO.getUser(), feieYunSettingPolicyDTO.getKey(), STIME)));
        nvps.add(new BasicNameValuePair("apiname", "Open_printMsg"));//固定值,不需要修改
        nvps.add(new BasicNameValuePair("sn", feieYunSettingPolicyDTO.getSn()));
        nvps.add(new BasicNameValuePair("content", content));
        nvps.add(new BasicNameValuePair("times", "1"));//打印联数

        CloseableHttpResponse response = null;
        String result = null;
        try {
            post.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));
            response = httpClient.execute(post);
            int statecode = response.getStatusLine().getStatusCode();
            if (statecode == 200) {
                HttpEntity httpentity = response.getEntity();
                if (httpentity != null) {
                    //服务器返回的JSON字符串，建议要当做日志记录起来
                    result = EntityUtils.toString(httpentity);
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    String msg = jsonObject.getString("msg");
                    log.info("飞鹅打印返回信息:" + msg);
                }
            }
        } catch (Exception e) {
            log.error(" FeieYunBean.print异常1，", e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                log.error(" FeieYunBean.print异常2，", e);
            }
            try {
                post.abort();
            } catch (Exception e) {
                log.error(" FeieYunBean.print异常3，", e);
            }
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error(" FeieYunBean.print异常4，", e);
            }
        }
    }


    //生成签名字符串
    private static String signature(String USER, String UKEY, String STIME) {
        String s = DigestUtils.sha1Hex(USER + UKEY + STIME);
        return s;
    }
}
