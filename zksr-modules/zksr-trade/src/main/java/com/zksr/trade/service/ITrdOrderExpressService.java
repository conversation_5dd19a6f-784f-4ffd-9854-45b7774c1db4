package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.api.express.dto.ExpressResDTO;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressPageReqVO;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressReqVO;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressSaveReqVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 订单快递Service接口
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
public interface ITrdOrderExpressService {

    /**
     * 新增订单快递
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdOrderExpress(@Valid TrdOrderExpressSaveReqVO createReqVO);

    /**
     * 修改订单快递
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdOrderExpress(@Valid TrdOrderExpressSaveReqVO updateReqVO);

    /**
     * 删除订单快递
     *
     * @param orderExpressId 订单快递id
     */
    public void deleteTrdOrderExpress(Long orderExpressId);

    /**
     * 批量删除订单快递
     *
     * @param orderExpressIds 需要删除的订单快递主键集合
     * @return 结果
     */
    public void deleteTrdOrderExpressByOrderExpressIds(Long[] orderExpressIds);

    /**
     * 获得订单快递
     *
     * @param orderExpressId 订单快递id
     * @return 订单快递
     */
    public TrdOrderExpress getTrdOrderExpress(Long orderExpressId);

    /**
     * 获得订单快递分页
     *
     * @param pageReqVO 分页查询
     * @return 订单快递分页
     */
    PageResult<TrdOrderExpress> getTrdOrderExpressPage(TrdOrderExpressPageReqVO pageReqVO);

    /**
     * 查询快递信息
     * @param phone 收件人手机号
     * @param courierCompanyNo 快递公司编码
     * @param courierNumber 快递单号
     * @return
     */
    public ExpressResDTO getExpressInfoByCourier(String phone, String courierCompanyNo, String courierNumber);

    /**
     * 修改快递单号
     * @param orderExpressId 订单快递表ID
     * @param courierNumber 快递单号
     */
    public void updateOrderExpressByOrderExpressId(TrdOrderExpressReqVO reqVO);

    /**
     * 订单快递保存
     * @param reqSaveVO
     * @return
     */
    public void saveOrderExpress(TrdOrderExpressSaveReqVO reqSaveVO);

    List<TrdOrderExpress> getOrderExpressInfoByOrderDtlIds(List<Long> supplierOrderDtlIds);

}
