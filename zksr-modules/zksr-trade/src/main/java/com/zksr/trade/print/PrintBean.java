package com.zksr.trade.print;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.system.api.partnerConfig.dto.DeviceSettingConfigDTO;
import com.zksr.trade.service.TrdCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 打印Bean
 * @Author: liuxingyu
 * @Date: 2024/4/26 9:29
 */
@Component
@Slf4j
public class PrintBean {
    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private XpYunBean xpYunBean;

    @Autowired
    private FeieYunBean feieYunBean;


    /**
     * @Description: 打印
     * @Author: liuxingyu
     * @Date: 2024/4/26 9:30
     */
    public void print(Long orderId, Long sysCode, Long supplierOrderId) {
        DeviceSettingConfigDTO deviceSettingConfigDTO = trdCacheService.getDeviceSettingPolicyDTO(sysCode);
        if (ObjectUtil.isNull(deviceSettingConfigDTO) || StringUtils.isBlank(deviceSettingConfigDTO.getPrintSetting())) {
            log.error("平台商未配置打印设置,平台商Id为{}",sysCode);
            return;
        }
        //对应字典 1:芯烨打印机 2:飞鹅打印机
        String printSetting = deviceSettingConfigDTO.getPrintSetting();
        switch (printSetting) {
            case "1":
                xpYunBean.PrintOrder(orderId, sysCode, supplierOrderId);
                break;
            case "2":
                feieYunBean.PrintOrder(orderId, sysCode, supplierOrderId);
                break;
            default:
                break;
        }
    }
}
