package com.zksr.trade.controller.orderExpress.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年04月18日 19:39
 * @description: TrdOrderExpressReqVO
 */
@Data
@ApiModel("订单快递 - trd_order_express Request VO")
public class TrdOrderExpressReqVO {
    /** 订单快递id */
    @ApiModelProperty(value = "订单快递id", required = true)
    private Long orderExpressId;

    @ApiModelProperty(value = "订单快递单号",  required = true)
    private String courierNumber;

    @ApiModelProperty(value = "快递公司", required = true)
    private String expressCom;

    @ApiModelProperty(value = "快递公司编码", required = true)
    private String expressComNo;
}
