package com.zksr.trade.controller.order;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdOrderShare;
import com.zksr.trade.service.ITrdOrderShareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.order.vo.TrdOrderSharePageReqVO;
import com.zksr.trade.api.order.vo.TrdOrderShareRespVO;
import com.zksr.trade.convert.order.TrdOrderShareConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 订单分享Controller
 *
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "管理后台 - 订单分享接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/orderShare")
public class TrdOrderShareController {
    @Autowired
    private ITrdOrderShareService trdOrderShareService;

    /**
     * 获取订单分享详细信息
     */
    @ApiOperation(value = "获得订单分享详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{shareOrderId}")
    public CommonResult<TrdOrderShareRespVO> getInfo(@PathVariable("shareOrderId") Long shareOrderId) {
        TrdOrderShare trdOrderShare = trdOrderShareService.getTrdOrderShare(shareOrderId);
        return success(TrdOrderShareConvert.INSTANCE.convert(trdOrderShare));
    }

    /**
     * 分页查询订单分享
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得订单分享分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdOrderShareRespVO>> getPage(@Valid TrdOrderSharePageReqVO pageReqVO) {
        PageResult<TrdOrderShare> pageResult = trdOrderShareService.getTrdOrderSharePage(pageReqVO);
        return success(TrdOrderShareConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "order:orderShare:add";
        /** 编辑 */
        public static final String EDIT = "order:orderShare:edit";
        /** 删除 */
        public static final String DELETE = "order:orderShare:remove";
        /** 列表 */
        public static final String LIST = "order:orderShare:list";
        /** 查询 */
        public static final String GET = "order:orderShare:query";
        /** 停用 */
        public static final String DISABLE = "order:orderShare:disable";
        /** 启用 */
        public static final String ENABLE = "order:orderShare:enable";
    }
}
