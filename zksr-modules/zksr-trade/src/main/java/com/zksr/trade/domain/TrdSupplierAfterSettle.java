package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 售后结算信息对象 trd_supplier_after_settle
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@TableName(value = "trd_supplier_after_settle")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TrdSupplierAfterSettle extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 售后结算信息id;售后结算信息id */
    @TableId(type= IdType.ASSIGN_ID)
    private Long afterSettleId;

    /** 平台商id;平台商id */
    @Excel(name = "平台商id;平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 售后单id;售后单id */
    @Excel(name = "售后单id;售后单id")
    private Long afterId;

    /** 售后编号;售后编号 */
    @Excel(name = "售后编号;售后编号")
    private String afterNo;

    /** 入驻商订单明细编号;入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号;入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 入驻商订单明细id;入驻商订单明细id */
    @Excel(name = "入驻商订单明细id;入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 商品数量;商品数量
     * SAASB-507 【成都佰润】散装称重商品需求
     * */
    @Excel(name = "商品数量;商品数量")
    private BigDecimal returnQty;

    /** 商品销售价;从订单 商品销售价 */
    @Excel(name = "商品销售价;从订单 商品销售价")
    private BigDecimal returnPrice;

    /** 商品金额;商品金额 */
    @Excel(name = "商品金额;商品金额")
    private BigDecimal returnAmt;

    /** 运费;从订单 运费 */
    @Excel(name = "运费;从订单 运费")
    private BigDecimal transAmt;

    /** 入驻商结算金额;入驻商结算金额 */
    @Excel(name = "入驻商结算金额;入驻商结算金额")
    private BigDecimal supplierAmt;

    /** 利润;利润 */
    @Excel(name = "利润;利润")
    private BigDecimal profit;

    /** 入驻商成本价;从订单 入驻商成本价 */
    @Excel(name = "入驻商成本价;从订单 入驻商成本价")
    private BigDecimal costPrice;

    /**
     * 商品销售利润比例 占比, 最大0.29 = 29% 从订单表来
     */
    private BigDecimal saleTotalRate;

    /**
     * 利润模式 0=(售价*比例=利润), 1=(售价-进货价=利润) 从订单表来
     */
    private String profitModel;


    /** 软件商分润比例 */
    private BigDecimal softwareRate;

    /** 软件商结算金额 */
    private BigDecimal softwareAmt;

    /** 运营商分润比例;从订单 运营商分润比例，百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "运营商分润比例;从订单 运营商分润比例，百分比的小数表现形式，1%表示为0.01")
    private BigDecimal dcRate;

    /** 运营商结算金额;运营商结算金额 */
    @Excel(name = "运营商结算金额;运营商结算金额")
    private BigDecimal dcAmt;

    /** 平台商分润比例;从订单 平台商分润比例，百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "平台商分润比例;从订单 平台商分润比例，百分比的小数表现形式，1%表示为0.01")
    private BigDecimal partnerRate;

    /** 平台商结算金额;平台商结算金额 */
    @Excel(name = "平台商结算金额;平台商结算金额")
    private BigDecimal partnerAmt;

    /** 业务员负责人分润比例;从订单 业务员负责人分润比例，百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "业务员负责人分润比例;从订单 业务员负责人分润比例，百分比的小数表现形式，1%表示为0.01")
    private BigDecimal colonel1Rate;

    /** 业务员结算金额;业务员结算金额 */
    @Excel(name = "业务员结算金额;业务员结算金额")
    private BigDecimal colonel1Amt;

    /** 业务员分润比例;从订单 业务员分润比例，百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "业务员分润比例;从订单 业务员分润比例，百分比的小数表现形式，1%表示为0.01")
    private BigDecimal colonel2Rate;

    /** 业务员结算金额;业务员结算金额 */
    @Excel(name = "业务员结算金额;业务员结算金额")
    private BigDecimal colonel2Amt;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundAmt;

    /** 支付公司收取的支付费率;从订单 */
    @Excel(name = "支付公司收取的支付费率;从订单")
    private BigDecimal payRate;

    /** 支付平台手续费;(refund_amt*pay_rate) 四舍五入 */
    @Excel(name = "支付平台手续费;(refund_amt*pay_rate) 四舍五入")
    private BigDecimal refundFee;

    /** 入驻商分钱;pay_amt - pay_fee */
    @Excel(name = "入驻商分钱;pay_amt - pay_fee")
    private BigDecimal supplierRefundDivideAmt;

    /** 售后单明细ID */
    @Excel(name = "售后单明细ID")
    private Long supplierAfterDtlId;

    /** 售后单明细编号 */
    @Excel(name = "售后单明细编号")
    private String supplierAfterDtlNo;

    /** 入驻商售后单ID */
    @Excel(name = "售后单ID")
    private Long supplierAfterId;

    /** 入驻商售后单编号 */
    @Excel(name = "售后单编号")
    private String supplierAfterNo;

    /**
     * 总运营商分润比例
     */
    @Excel(name = "总运营商分润比例")
    private BigDecimal allDcRate;

    /**
     * 运营商设置的运营商分润比例
     */
    @Excel(name = "运营商设置的运营商分润比例")
    private BigDecimal settingDcRate;

    /**
     * 运营商设置的业务员管理员分润比例
     */
    @Excel(name = "运营商设置的业务员管理员分润比例")
    private BigDecimal settingColonel1Rate;

    /**
     * 业务员管理员提成系数
     */
    @Excel(name = "业务员管理员提成系数")
    private BigDecimal colonel1Percentage;

    /**
     * 满额业务员管理员分润金额
     */
    @Excel(name = "满额业务员管理员分润金额")
    private BigDecimal allColonel1Amt;


    /**
     * 运营商设置的业务员分润比例
     */
    @Excel(name = "运营商设置的业务员分润比例")
    private BigDecimal settingColonel2Rate;

    /**
     * 业务员提成系数
     */
    @Excel(name = "业务员提成系数")
    private BigDecimal colonel2Percentage;

    /**
     * 满额业务员分润金额
     */
    @Excel(name = "满额业务员分润金额")
    private BigDecimal allColonel2Amt;

    /**
     * 售后储值本金比率
     */
    @Excel(name = "售后储值本金比率")
    private BigDecimal returnCzPrincipalRate = BigDecimal.ZERO;

    /**
     * 售后储值本金支付金额
     */
    @Excel(name = "售后储值本金支付金额")
    private BigDecimal returnCzPrincipalPayAmt = BigDecimal.ZERO;

    /**
     * 售后储值赠金支付金额
     */
    @Excel(name = "售后储值赠金支付金额")
    private BigDecimal returnCzGivePayAmt = BigDecimal.ZERO;

    /**
     * 售后余额退款金额（新余额支付功能）
     */
    @Excel(name = "售后余额退款金额")
    private BigDecimal refundBalanceAmt = BigDecimal.ZERO;

    /**
     * 售后总退款金额
     */
    @Excel(name = "售后总退款金额")
    private BigDecimal totalRefundAmt = BigDecimal.ZERO;

    /**
     * 用来处理退款
     */
    @TableField(exist = false)
    private String supplierOrderNo;
}
