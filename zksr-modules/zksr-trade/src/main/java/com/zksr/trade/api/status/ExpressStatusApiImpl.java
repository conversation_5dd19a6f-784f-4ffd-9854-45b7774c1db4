package com.zksr.trade.api.status;

import com.zksr.common.core.domain.vo.openapi.receive.OrderStateReturnVO;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.utils.OpenapiSecurityUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.LoginOpensource;
import com.zksr.system.api.model.LoginUser;
import com.zksr.trade.controller.status.vo.TrdExpressStatusSaveReqVO;
import com.zksr.trade.service.ITrdExpressStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

/**
 *
 * <AUTHOR>
 * @date 2024/6/4 16:26
 */
@RestController
@ApiIgnore
public class ExpressStatusApiImpl implements ExpressStatusApi {


    @Autowired
    private ITrdExpressStatusService trdExpressStatusService;

    @Override
    public CommonResult<Boolean> saveExpressStatus(Long sysCode, Long opensourceId, Object saveVo) {
        trdExpressStatusService.insertTrdExpressStatus(HutoolBeanUtils.toBean(saveVo, TrdExpressStatusSaveReqVO.class));
        return CommonResult.success(true);
    }
}
