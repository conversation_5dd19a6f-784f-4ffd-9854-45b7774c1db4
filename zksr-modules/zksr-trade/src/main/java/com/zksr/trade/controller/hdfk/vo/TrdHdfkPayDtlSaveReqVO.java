package com.zksr.trade.controller.hdfk.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 货到付款付款单明细对象 trd_hdfk_pay_dtl
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@ApiModel("货到付款付款单明细 - trd_hdfk_pay_dtl分页 Request VO")
public class TrdHdfkPayDtlSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 货到付款付款单id */
    @ApiModelProperty(value = "货到付款付款单id")
    private Long hdfkPayDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 付款单号 */
    @Excel(name = "付款单号")
    @ApiModelProperty(value = "付款单号")
    private String payNo;

    /** 付款单id */
    @Excel(name = "付款单id")
    @ApiModelProperty(value = "付款单id")
    private Long hdfkPayId;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id", required = true)
    private Long branchId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id", required = true)
    private Long supplierId;

    /** 支付金额；实际支付金额 */
    @Excel(name = "支付金额；实际支付金额")
    @ApiModelProperty(value = "支付金额；实际支付金额")
    private BigDecimal payAmt;

    /** 结算金额 */
    @Excel(name = "结算金额")
    @ApiModelProperty(value = "结算金额")
    private BigDecimal settleAmt;

    /** 支付公司收取的支付费率 */
    @Excel(name = "支付公司收取的支付费率")
    @ApiModelProperty(value = "支付公司收取的支付费率")
    private BigDecimal payRate;

    /** 支付平台手续费；(pay_amt*pay_rate) 四舍五入 */
    @Excel(name = "支付平台手续费；(pay_amt*pay_rate) 四舍五入")
    @ApiModelProperty(value = "支付平台手续费；(pay_amt*pay_rate) 四舍五入")
    private BigDecimal payFee;

    /** 分账金额 */
    @Excel(name = "分账金额")
    @ApiModelProperty(value = "分账金额")
    private BigDecimal divideAmt;

}
