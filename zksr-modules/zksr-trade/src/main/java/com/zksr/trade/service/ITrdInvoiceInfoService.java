package com.zksr.trade.service;

import javax.validation.*;

import com.zksr.account.api.divide.dto.AccDivideDtlDTO;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.api.invoice.dto.InvoiceCallbackDTO;
import com.zksr.trade.api.invoice.dto.InvoiceProcessDTO;
import com.zksr.trade.domain.TrdInvoiceInfo;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceInfoPageReqVO;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceInfoSaveReqVO;
import com.zksr.trade.domain.TrdInvoiceRecord;

/**
 * 发票主Service接口
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface ITrdInvoiceInfoService {

    /**
     * 新增发票主
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdInvoiceInfo(@Valid TrdInvoiceInfoSaveReqVO createReqVO);

    /**
     * 修改发票主
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdInvoiceInfo(@Valid TrdInvoiceInfoSaveReqVO updateReqVO);

    /**
     * 删除发票主
     *
     * @param id 主键ID
     */
    public void deleteTrdInvoiceInfo(Long id);

    /**
     * 批量删除发票主
     *
     * @param ids 需要删除的发票主主键集合
     * @return 结果
     */
    public void deleteTrdInvoiceInfoByIds(Long[] ids);

    /**
     * 获得发票主
     *
     * @param id 主键ID
     * @return 发票主
     */
    public TrdInvoiceInfo getTrdInvoiceInfo(Long id);

    /**
     * 获得发票主分页
     *
     * @param pageReqVO 分页查询
     * @return 发票主分页
     */
    PageResult<TrdInvoiceInfo> getTrdInvoiceInfoPage(TrdInvoiceInfoPageReqVO pageReqVO);


    /**
     * 开蓝牌
     * @param invoiceProcessDTO
     * @return
     */
     boolean blueInvoiceProcess(InvoiceProcessDTO invoiceProcessDTO);

     boolean invoiceCallback(InvoiceCallbackDTO invoiceCallbackDTO);
    
    /**
     * 判断此分账记录是否已经开过发票
     * @param accDivideDtlDTO
     * @return
     */
    TrdInvoiceRecord checkPassInvoiceRecord(AccDivideDtlDTO accDivideDtlDTO);
}
