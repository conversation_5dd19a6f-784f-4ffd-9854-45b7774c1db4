package com.zksr.trade.convert.trdPackage;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageDtlRespVO;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageDtlSaveReqVO;
import com.zksr.trade.domain.TrdPackageDtl;

/**
* 包裹明细 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-07-18
*/
@Mapper
public interface TrdPackageDtlConvert {

    TrdPackageDtlConvert INSTANCE = Mappers.getMapper(TrdPackageDtlConvert.class);

    TrdPackageDtlRespVO convert(TrdPackageDtl trdPackageDtl);

    TrdPackageDtl convert(TrdPackageDtlSaveReqVO trdPackageDtlSaveReq);

    PageResult<TrdPackageDtlRespVO> convertPage(PageResult<TrdPackageDtl> trdPackageDtlPage);
}