package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.domain.TrdPackageDtl;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageDtlPageReqVO;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageDtlSaveReqVO;

import java.util.List;

/**
 * 包裹明细Service接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface ITrdPackageDtlService {

    /**
     * 新增包裹明细
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdPackageDtl(@Valid TrdPackageDtlSaveReqVO createReqVO);

    /**
     * 修改包裹明细
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdPackageDtl(@Valid TrdPackageDtlSaveReqVO updateReqVO);

    /**
     * 删除包裹明细
     *
     * @param packageDtlId 包裹明细ID
     */
    public void deleteTrdPackageDtl(Long packageDtlId);

    /**
     * 批量删除包裹明细
     *
     * @param packageDtlIds 需要删除的包裹明细主键集合
     * @return 结果
     */
    public void deleteTrdPackageDtlByPackageDtlIds(Long[] packageDtlIds);

    /**
     * 获得包裹明细
     *
     * @param packageDtlId 包裹明细ID
     * @return 包裹明细
     */
    public TrdPackageDtl getTrdPackageDtl(Long packageDtlId);

    /**
     * 获得包裹明细分页
     *
     * @param pageReqVO 分页查询
     * @return 包裹明细分页
     */
    PageResult<TrdPackageDtl> getTrdPackageDtlPage(TrdPackageDtlPageReqVO pageReqVO);

    /**
     * 根据包裹ID查询包裹明细列表
     * @param packageId 包裹ID
     * @return 包裹明细列表
     */
    List<TrdPackageDtl> getPackageDtlListByPackageId(Long packageId);
}
