package com.zksr.trade.controller.after.settleVo;

import java.math.BigDecimal;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 售后结算信息对象 trd_supplier_after_settle
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@ApiModel("售后结算信息 - trd_supplier_after_settle分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdSupplierAfterSettlePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 售后结算信息id;售后结算信息id */
    @ApiModelProperty(value = "入驻商分钱;pay_amt - pay_fee")
    private Long afterSettleId;

    /** 平台商id;平台商id */
    @Excel(name = "平台商id;平台商id")
    @ApiModelProperty(value = "平台商id;平台商id")
    private Long sysCode;

    /** 售后单id;售后单id */
    @Excel(name = "售后单id;售后单id")
    @ApiModelProperty(value = "售后单id;售后单id")
    private Long afterId;

    /** 售后编号;售后编号 */
    @Excel(name = "售后编号;售后编号")
    @ApiModelProperty(value = "售后编号;售后编号")
    private String afterNo;

    /** 入驻商订单明细编号;入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号;入驻商订单明细编号")
    @ApiModelProperty(value = "入驻商订单明细编号;入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 入驻商订单明细id;入驻商订单明细id */
    @Excel(name = "入驻商订单明细id;入驻商订单明细id")
    @ApiModelProperty(value = "入驻商订单明细id;入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 商品数量;商品数量 */
    @Excel(name = "商品数量;商品数量")
    @ApiModelProperty(value = "商品数量;商品数量")
    private Long returnQty;

    /** 商品销售价;从订单 商品销售价 */
    @Excel(name = "商品销售价;从订单 商品销售价")
    @ApiModelProperty(value = "商品销售价;从订单 商品销售价")
    private BigDecimal returnPrice;

    /** 商品金额;商品金额 */
    @Excel(name = "商品金额;商品金额")
    @ApiModelProperty(value = "商品金额;商品金额")
    private BigDecimal returnAmt;

    /** 运费;从订单 运费 */
    @Excel(name = "运费;从订单 运费")
    @ApiModelProperty(value = "运费;从订单 运费")
    private BigDecimal transAmt;

    /** 入驻商结算金额;入驻商结算金额 */
    @Excel(name = "入驻商结算金额;入驻商结算金额")
    @ApiModelProperty(value = "入驻商结算金额;入驻商结算金额")
    private BigDecimal supplierAmt;

    /** 利润;利润 */
    @Excel(name = "利润;利润")
    @ApiModelProperty(value = "利润;利润")
    private BigDecimal profit;

    /** 入驻商成本价;从订单 入驻商成本价 */
    @Excel(name = "入驻商成本价;从订单 入驻商成本价")
    @ApiModelProperty(value = "入驻商成本价;从订单 入驻商成本价")
    private BigDecimal costPrice;

    /** 软件商分润比例 */
    @ApiModelProperty(value = "软件商分润比例")
    private BigDecimal softwareRate;

    /** 软件商结算金额 */
    @ApiModelProperty(value = "软件商结算金额")
    private BigDecimal softwareAmt;

    /** 运营商分润比例;从订单 运营商分润比例，百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "运营商分润比例;从订单 运营商分润比例，百分比的小数表现形式，1%表示为0.01")
    @ApiModelProperty(value = "运营商分润比例;从订单 运营商分润比例，百分比的小数表现形式，1%表示为0.01")
    private BigDecimal dcRate;

    /** 运营商结算金额;运营商结算金额 */
    @Excel(name = "运营商结算金额;运营商结算金额")
    @ApiModelProperty(value = "运营商结算金额;运营商结算金额")
    private BigDecimal dcAmt;

    /** 平台商分润比例;从订单 平台商分润比例，百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "平台商分润比例;从订单 平台商分润比例，百分比的小数表现形式，1%表示为0.01")
    @ApiModelProperty(value = "平台商分润比例;从订单 平台商分润比例，百分比的小数表现形式，1%表示为0.01")
    private BigDecimal partnerRate;

    /** 平台商结算金额;平台商结算金额 */
    @Excel(name = "平台商结算金额;平台商结算金额")
    @ApiModelProperty(value = "平台商结算金额;平台商结算金额")
    private BigDecimal partnerAmt;

    /** 业务员负责人分润比例;从订单 业务员负责人分润比例，百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "业务员负责人分润比例;从订单 业务员负责人分润比例，百分比的小数表现形式，1%表示为0.01")
    @ApiModelProperty(value = "业务员负责人分润比例;从订单 业务员负责人分润比例，百分比的小数表现形式，1%表示为0.01")
    private BigDecimal colonel1Rate;

    /** 业务员结算金额;业务员结算金额 */
    @Excel(name = "业务员结算金额;业务员结算金额")
    @ApiModelProperty(value = "业务员结算金额;业务员结算金额")
    private BigDecimal colonel1Amt;

    /** 业务员分润比例;从订单 业务员分润比例，百分比的小数表现形式，1%表示为0.01 */
    @Excel(name = "业务员分润比例;从订单 业务员分润比例，百分比的小数表现形式，1%表示为0.01")
    @ApiModelProperty(value = "业务员分润比例;从订单 业务员分润比例，百分比的小数表现形式，1%表示为0.01")
    private BigDecimal colonel2Rate;

    /** 业务员结算金额;业务员结算金额 */
    @Excel(name = "业务员结算金额;业务员结算金额")
    @ApiModelProperty(value = "业务员结算金额;业务员结算金额")
    private BigDecimal colonel2Amt;

    /** 退款金额 */
    @Excel(name = "退款金额")
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmt;

    /** 支付公司收取的支付费率;从订单 */
    @Excel(name = "支付公司收取的支付费率;从订单")
    @ApiModelProperty(value = "支付公司收取的支付费率;从订单")
    private BigDecimal payRate;

    /** 支付平台手续费;(refund_amt*pay_rate) 四舍五入 */
    @Excel(name = "支付平台手续费;(refund_amt*pay_rate) 四舍五入")
    @ApiModelProperty(value = "支付平台手续费;(refund_amt*pay_rate) 四舍五入")
    private BigDecimal refundFee;

    /** 入驻商分钱;pay_amt - pay_fee */
    @Excel(name = "入驻商分钱;pay_amt - pay_fee")
    @ApiModelProperty(value = "入驻商分钱;pay_amt - pay_fee")
    private BigDecimal supplierRefundDivideAmt;


}
