package com.zksr.trade.controller.orderExpressImport;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdExpressImportDtl;
import com.zksr.trade.service.ITrdExpressImportDtlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlPageReqVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlSaveReqVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlRespVO;
import com.zksr.trade.convert.orderExpressImport.TrdExpressImportDtlConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 快递导入明细Controller
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Api(tags = "管理后台 - 快递导入明细接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/orderExpressImportDtl")
public class TrdExpressImportDtlController {
    @Autowired
    private ITrdExpressImportDtlService trdExpressImportDtlService;

    /**
     * 新增快递导入明细
     */
    @ApiOperation(value = "新增快递导入明细", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "快递导入明细", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdExpressImportDtlSaveReqVO createReqVO) {
        return success(trdExpressImportDtlService.insertTrdExpressImportDtl(createReqVO));
    }

    /**
     * 修改快递导入明细
     */
    @ApiOperation(value = "修改快递导入明细", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "快递导入明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdExpressImportDtlSaveReqVO updateReqVO) {
            trdExpressImportDtlService.updateTrdExpressImportDtl(updateReqVO);
        return success(true);
    }

    /**
     * 删除快递导入明细
     */
    @ApiOperation(value = "删除快递导入明细", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "快递导入明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{expressImportDtlIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] expressImportDtlIds) {
        trdExpressImportDtlService.deleteTrdExpressImportDtlByExpressImportDtlIds(expressImportDtlIds);
        return success(true);
    }

    /**
     * 获取快递导入明细详细信息
     */
    @ApiOperation(value = "获得快递导入明细详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{expressImportDtlId}")
    public CommonResult<TrdExpressImportDtlRespVO> getInfo(@PathVariable("expressImportDtlId") Long expressImportDtlId) {
        TrdExpressImportDtl trdExpressImportDtl = trdExpressImportDtlService.getTrdExpressImportDtl(expressImportDtlId);
        return success(TrdExpressImportDtlConvert.INSTANCE.convert(trdExpressImportDtl));
    }

    /**
     * 分页查询快递导入明细
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得快递导入明细分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdExpressImportDtlRespVO>> getPage(@Valid TrdExpressImportDtlPageReqVO pageReqVO) {
        PageResult<TrdExpressImportDtl> pageResult = trdExpressImportDtlService.getTrdExpressImportDtlPage(pageReqVO);
        return success(TrdExpressImportDtlConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:dtl:add";
        /** 编辑 */
        public static final String EDIT = "trade:dtl:edit";
        /** 删除 */
        public static final String DELETE = "trade:dtl:remove";
        /** 列表 */
        public static final String LIST = "trade:dtl:list";
        /** 查询 */
        public static final String GET = "trade:dtl:query";
        /** 停用 */
        public static final String DISABLE = "trade:dtl:disable";
        /** 启用 */
        public static final String ENABLE = "trade:dtl:enable";
    }
}
