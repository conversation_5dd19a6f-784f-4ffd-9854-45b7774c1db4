package com.zksr.trade.service.impl.handler.payWay;

import cn.hutool.core.map.MapUtil;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.common.core.constant.PayOrderSubmitExtras;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.OrderPayWayEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.domain.TrdSettle;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierAfterSettle;
import com.zksr.trade.service.handler.payWay.ITrdAfterPayWayHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 储值 支付方式处理实现 - 在线支付
 */
@Service
@Slf4j
@Order(ITrdAfterPayWayHandlerService.ORDER_CZ_ONLINE)
public class TrdAfterCzPayWayHandlerServiceimpl implements ITrdAfterPayWayHandlerService {

    @Autowired
    private PlatformMerchantApi platformMerchantApi;



    @Override
    public Boolean isPlatform(String platform, String payWay) {
        return Objects.equals(PayChannelEnum.WALLET.getCode(), platform) && Objects.equals(OrderPayWayEnum.WALLET.getPayWay(), payWay);
    }

    @Override
    public Boolean isPlatform(String platform) {
        return Objects.equals(PayChannelEnum.WALLET.getCode(), platform);
    }

    @Override
    public void afterMerchantSettlement(Long supplierId,
                                        TrdAfter after,
                                        PlatformMerchantDTO merchantDTO,
                                        Map<Long, TrdSupplierAfterSettle> supplierAfterSettleMap,
                                        List<TrdSettle> afterSettleList,
                                        List<TrdSupplierAfterDtl> supplierAfterDtlList,
                                        PayRefundOrderSubmitReqVO refundReqVo) {
        // 扩展参数当前支付门店ID
        refundReqVo.setExtras(MapUtil.of(PayOrderSubmitExtras.BRANCH_ID, after.getBranchId().toString()));

        supplierAfterDtlList.forEach(afterDtl -> {
            TrdSupplierAfterSettle afterSettle = supplierAfterSettleMap.get(afterDtl.getSupplierAfterDtlId());
            refundReqVo.setRefundAmt(afterSettle.getRefundAmt().add(refundReqVo.getRefundAmt()));
            refundReqVo.setWalletGiveAmt(refundReqVo.getWalletGiveAmt().add(afterSettle.getReturnCzGivePayAmt()));
        });

        // 储值支付合并订单结算分账信息
        afterSettleList.stream()
                .filter(settle -> Objects.equals(supplierId, settle.getSupplierId()) && Objects.equals(settle.getMerchantType(), MerchantTypeEnum.COLONEL.getType()))
                .collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
                .forEach((merchantKey, settleList) -> {
                    Long merchantId = Long.valueOf(merchantKey.split("_")[NumberPool.INT_ZERO]);
                    String merchantType = merchantKey.split("_")[NumberPool.INT_ONE];

                    // 获取当前结算的账户信息
                    PlatformMerchantDTO merchant = platformMerchantApi.getPlatformMerchant(merchantType, merchantId, after.getPlatform()).getCheckedData();
                    String accountNo = merchantId + "";
                    // 分账信息  获取当前支付方式 属性【是否支持在线分账】值
                    if (ToolUtil.isNotEmpty(merchant) && PayChannelEnum.getPayOnlineSupportDivide(after.getPlatform())) {
                        accountNo = merchant.getAltMchNo();
                    }

                    OrderSettlementDTO orderMerchantSettlement = OrderSettlementDTO.builder()
                            .accountNo(accountNo)
                            .merchantId(merchantId)
                            .merchantType(merchantType)
                            .build();

                    // 因结算信息表中售后订单填写的金额为负数，所以需要取反
                    BigDecimal settleAmt = settleList.stream().map(TrdSettle::getSettleAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).multiply(new BigDecimal("-1"));
                    orderMerchantSettlement.setAmt(settleAmt);
                    if (settleAmt.compareTo(BigDecimal.ZERO) > NumberPool.INT_ZERO) {
                        refundReqVo.getSettlements().add(orderMerchantSettlement);
                    }
                });
    }
}
