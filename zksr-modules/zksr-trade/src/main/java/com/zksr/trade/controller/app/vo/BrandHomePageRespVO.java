package com.zksr.trade.controller.app.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 品牌商首页数据统计
 * @date 2024/8/6 11:26
 */
@Data
@ApiModel(description = "品牌商首页数据统计")
public class BrandHomePageRespVO {

    @ApiModelProperty("当日订单")
    private Long todayOrder;

    @ApiModelProperty("当日销售额")
    private BigDecimal todayTotalAmt;

    @ApiModelProperty("当日退单")
    private Long todayAfter;

    @ApiModelProperty("当日退单额")
    private Long todayAfterAmt;
}
