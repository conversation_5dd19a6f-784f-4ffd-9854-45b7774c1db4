package com.zksr.trade.service.impl;

import com.zksr.common.core.enums.PayStateEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.elasticsearch.mapper.EsStoreProductFullMapper;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.mapper.TrdOrderMapper;
import com.zksr.trade.service.ITrdOrderService;
import com.zksr.trade.service.ITrdRootService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/7 8:59
 */
@Slf4j
@Service
public class TrdRootServiceImpl implements ITrdRootService {

    @Autowired
    private TrdOrderMapper orderMapper;

    @Autowired
    private ITrdOrderService orderService;

    @Resource
    private EsStoreProductFullMapper storeProductFullMapper;

    @Override
    public void refreshStoreProduct() {
        // 第一步删除全部数据
        storeProductFullMapper.delete(new LambdaEsQueryWrapper<>());

        // 轮询处理三个月内的订单数据
        // 已支付订单, 或者货到付款订单
        // 1-已支付,3-货到付款未收款,4-货到付款已收款
        /**
         * {@link PayStateEnum}
         */
        Long minId = null;
        Long counter = NumberPool.LONG_ZERO;
        for (;;) {
            List<TrdOrder> orderList = orderMapper.selectByStoreProductInitOrderList(minId);
            if (orderList.isEmpty()) {
                break;
            }
            orderList.forEach(orderService::updateFrequentByOrder);
            minId = orderList.get(orderList.size() - 1).getOrderId();
            counter += orderList.size();
            log.info("刷新常购数据订单, 已处理条目数: {}", counter);
        }
    }
}
