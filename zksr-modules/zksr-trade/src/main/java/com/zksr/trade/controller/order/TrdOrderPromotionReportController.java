package com.zksr.trade.controller.order;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.api.order.dto.ActivityCustomerOrderUseDetailDTO;
import com.zksr.trade.api.order.dto.ActivityCustomerOrderUseTotalDTO;
import com.zksr.trade.api.order.dto.CouponCustomerOrderUseDetilDTO;
import com.zksr.trade.api.order.dto.CouponCustomerOrderUseTotalDTO;
import com.zksr.trade.controller.order.vo.DcOrderPageReqVO;
import com.zksr.trade.controller.order.vo.DcOrderPageRespVO;
import com.zksr.trade.controller.order.vo.TrdOrderPromitionReportPageReqVO;
import com.zksr.trade.service.ITrdOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 *  订单优惠信息报表控制器
 */
@Slf4j
@Api(tags = "管理后台 - 订单优惠报表查询接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/orderPromotionReport")
public class TrdOrderPromotionReportController {

    @Autowired
    private ITrdOrderService trdOrderService;

    //=============================================优惠劵============================================
    /**
     * 获取使用该优惠劵模板的订单报表
     */
    @ApiOperation(value = "获取使用该优惠劵模板的订单报表" , httpMethod = HttpMethod.GET)
//    @RequiresPermissions("trade:order:getOperatorOrderPageList")
    @GetMapping("/getOperatorOrderPageList")
    public CommonResult<PageResult<CouponCustomerOrderUseTotalDTO>> getOrderDiscountReport(@Valid TrdOrderPromitionReportPageReqVO pageReqVO) {
        return success(trdOrderService.getCouponCustomerOrderUseDetailTotal(pageReqVO));
    }


    /**
     * 获取该优惠劵模板对应订单商品数据详情
     */
    @ApiOperation(value = "获取该优惠劵模板对应订单商品数据详情", httpMethod = HttpMethod.GET)
//    @RequiresPermissions("trade:order:getOperatorOrderPageList")
    @GetMapping("/getOrderDiscountReportDetail")
    public CommonResult<PageResult<CouponCustomerOrderUseDetilDTO>> getOrderDiscountReportDetail(@Valid TrdOrderPromitionReportPageReqVO pageReqVO) {
        return success(trdOrderService.getCouponCustomerOrderUseDetail(pageReqVO));
    }


    //=============================================促销活动============================================

    /**
     * 获取使用该活动的客户订单报表（按客户汇总）
     */
    @ApiOperation(value = "获取使用该活动的客户订单报表（按客户汇总）" , httpMethod = HttpMethod.GET)
//    @RequiresPermissions("trade:order:getOperatorOrderPageList")
    @GetMapping("/getCustomerActivityReport")
    public CommonResult<PageResult<ActivityCustomerOrderUseTotalDTO>> getCustomerActivityReport(@Valid TrdOrderPromitionReportPageReqVO pageReqVO) {
        return success(trdOrderService.getActivityCustomerTotal(pageReqVO));
    }


    /**
     * 获取使用该活动 指定客户的订单报表（按客户订单汇总）
     */
    @ApiOperation(value = "获取使用该活动 指定客户的订单报表（按客户订单汇总）" , httpMethod = HttpMethod.GET)
//    @RequiresPermissions("trade:order:getOperatorOrderPageList")
    @GetMapping("/getOrderActivityReport")
    public CommonResult<PageResult<ActivityCustomerOrderUseTotalDTO>> getOrderActivityReport(@Valid TrdOrderPromitionReportPageReqVO pageReqVO) {
        return success(trdOrderService.getActivityCustomerOrderTotal(pageReqVO));
    }

    /**
     * 获取使用该活动 指定客户、指定订单的明细数据
     */
    @ApiOperation(value = "获取使用该活动 指定客户、指定订单的明细数据" , httpMethod = HttpMethod.GET)
//    @RequiresPermissions("trade:order:getOperatorOrderPageList")
    @GetMapping("/getOrderActivityReportDetail")
    public CommonResult<PageResult<ActivityCustomerOrderUseDetailDTO>> getOrderActivityReportDetail(@Valid TrdOrderPromitionReportPageReqVO pageReqVO) {
        return success(trdOrderService.getActivityCustomerOrderDetail(pageReqVO));
    }




}
