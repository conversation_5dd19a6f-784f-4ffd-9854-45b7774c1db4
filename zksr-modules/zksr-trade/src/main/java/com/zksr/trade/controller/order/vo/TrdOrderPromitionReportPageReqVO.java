package com.zksr.trade.controller.order.vo;

import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("订单营销报表请求实体类")
public class TrdOrderPromitionReportPageReqVO extends PageParam {

    @ApiModelProperty(value = "优惠卷模板ID")
    private Long couponTemplateId;

    @ApiModelProperty(value = "客户ID（目前是门店ID）")
    private Long customerId;

    @ApiModelProperty(value = "开始时间(年-月-日)")
    private String startDate;

    @ApiModelProperty(value = "结束时间(年-月-日)")
    private String endDate;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户ID集合")
    private List<Long> customerIds;

}
