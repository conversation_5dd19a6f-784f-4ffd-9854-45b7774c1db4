package com.zksr.trade.controller.trdPackage;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.controller.trdPackage.vo.TrdPackagePageReqVO;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageRespVO;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageSaveReqVO;
import com.zksr.trade.convert.trdPackage.TrdPackageConvert;
import com.zksr.trade.domain.TrdPackage;
import com.zksr.trade.service.ITrdPackageService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 包裹Controller
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Api(tags = "管理后台 - 包裹接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/package")
public class TrdPackageController {
    @Autowired
    private ITrdPackageService trdPackageService;

    /**
     * 新增包裹
     */
    @ApiOperation(value = "新增包裹", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "包裹", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdPackageSaveReqVO createReqVO) {
        return success(trdPackageService.insertTrdPackage(createReqVO));
    }

    /**
     * 修改包裹
     */
    @ApiOperation(value = "修改包裹", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "包裹", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdPackageSaveReqVO updateReqVO) {
            trdPackageService.updateTrdPackage(updateReqVO);
        return success(true);
    }

    /**
     * 删除包裹
     */
    @ApiOperation(value = "删除包裹", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "包裹", businessType = BusinessType.DELETE)
    @DeleteMapping("/{packageIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] packageIds) {
        trdPackageService.deleteTrdPackageByPackageIds(packageIds);
        return success(true);
    }

    /**
     * 获取包裹详细信息
     */
    @ApiOperation(value = "获得包裹详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{packageId}")
    public CommonResult<TrdPackageRespVO> getInfo(@PathVariable("packageId") Long packageId) {
        TrdPackage trdPackage = trdPackageService.getTrdPackage(packageId);
        return success(TrdPackageConvert.INSTANCE.convert(trdPackage));
    }

    /**
     * 分页查询包裹
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得包裹分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdPackageRespVO>> getPage(@Valid TrdPackagePageReqVO pageReqVO) {
        PageResult<TrdPackage> pageResult = trdPackageService.getTrdPackagePage(pageReqVO);
        return success(TrdPackageConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:package:add";
        /** 编辑 */
        public static final String EDIT = "trade:package:edit";
        /** 删除 */
        public static final String DELETE = "trade:package:remove";
        /** 列表 */
        public static final String LIST = "trade:package:list";
        /** 查询 */
        public static final String GET = "trade:package:query";
        /** 停用 */
        public static final String DISABLE = "trade:package:disable";
        /** 启用 */
        public static final String ENABLE = "trade:package:enable";
    }
}
