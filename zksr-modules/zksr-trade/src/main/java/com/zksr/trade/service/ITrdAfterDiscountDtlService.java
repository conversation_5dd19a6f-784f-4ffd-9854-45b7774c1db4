package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.domain.TrdAfterDiscountDtl;

import java.util.List;

/**
 * 售后优惠明细Service接口
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
public interface ITrdAfterDiscountDtlService {

    /**
     * 新增售后优惠明细
     *
     * @param trdAfterDiscountDtl 创建信息
     * @return 结果
     */
    public Long insertTrdAfterDiscountDtl(TrdAfterDiscountDtl trdAfterDiscountDtl);

    /**
     * 批量新增售后优惠明细
     *
     * @param trdAfterDiscountDtls 创建信息
     * @return 结果
     */
    public void insertBatchTrdAfterDiscountDtl(List<TrdAfterDiscountDtl> trdAfterDiscountDtls);

}
