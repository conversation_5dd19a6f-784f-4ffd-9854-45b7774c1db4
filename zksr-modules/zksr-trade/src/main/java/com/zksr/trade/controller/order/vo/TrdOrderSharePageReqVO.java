package com.zksr.trade.controller.order.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 订单分享对象 trd_order_share
 *
 * <AUTHOR>
 * @date 2024-10-29
 */
@ApiModel("订单分享 - trd_order_share分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdOrderSharePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "有效时间")
    private Long shareOrderId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 入驻商订单ID */
    @Excel(name = "入驻商订单ID")
    @ApiModelProperty(value = "入驻商订单ID")
    private Long supplierOrderId;

    /** 订单ID */
    @Excel(name = "订单ID")
    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    /** 发起分享IP */
    @Excel(name = "发起分享IP")
    @ApiModelProperty(value = "发起分享IP")
    private String remoteIp;

    /** 有效时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "有效时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "有效时间")
    private Date expirationTime;


}
