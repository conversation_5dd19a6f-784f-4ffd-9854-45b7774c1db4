package com.zksr.trade.service.impl.handler;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.balance.BalanceApi;
import com.zksr.account.api.balance.dto.MemBranchBalanceDTO;
import com.zksr.account.api.pay.PayApi;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.constant.PayOrderSubmitExtras;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.database.config.CustomIdGenerator;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.trade.api.after.vo.PayRefundVO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.controller.after.vo.OrderAfterSaveVO;
import com.zksr.trade.domain.*;
import com.zksr.trade.mapper.*;
import com.zksr.trade.mq.TradeMqProducer;
import com.zksr.trade.service.ITrdAfterService;
import com.zksr.trade.service.ITrdSettleService;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.ITrdAfterHandlerService;
import com.zksr.trade.service.handler.activity.ITrdOrderActivityHandlerService;
import com.zksr.trade.service.handler.payWay.ITrdAfterPayWayHandlerService;
import com.zksr.trade.service.impl.price.BalanceRefundCalculationService;
import com.zksr.trade.service.price.ITrdPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;
import static com.zksr.member.constant.MemberConstant.ES_COLONEL_APP_ORDER_STATUS_0;
import static com.zksr.trade.enums.ErrorCodeConstants.TRD_SUPPLIER_AFTER_DTL_SETTLE_NOT_EXISTS;
import static com.zksr.trade.enums.ErrorCodeConstants.TRD_SUPPLIER_ORDER_DTL_SETTLE_NOT_EXISTS;

/**
 * <AUTHOR>
 * @date 2024年04月21日 17:58
 * @description: 正常售后订单操作
 */
@Service
@Slf4j
@Order(ITrdAfterHandlerService.ORDER_AFTER)
public class TrdAfterHandlerServiceImpl implements ITrdAfterHandlerService {

    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;
    @Autowired
    private TrdSupplierAfterDtlMapper trdSupplierAfterDtlMapper;
    @Autowired
    private TrdSupplierAfterSettleMapper trdSupplierAfterSettleMapper;
    @Autowired
    private TrdSupplierOrderSettleMapper trdSupplierOrderSettleMapper;
    @Autowired
    private PayApi payApi;
    @Autowired
    private ITrdSettleService trdSettleService;
    @Autowired
    private RedisStockService redisStockService;
    @Autowired
    private TrdOrderMapper trdOrderMapper;
    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private AccountApi accountApi;
    @Autowired
    private PlatformMerchantApi platformMerchantApi;
    @Autowired
    private TrdAfterLogMapper afterLogMapper;
    @Autowired
    private TradeMqProducer tradeMqProducer;
    @Autowired
    private TrdCacheService trdCacheService;
    @Autowired
    private TrdAfterMapper trdAfterMapper;
    @Autowired
    private List<ITrdAfterPayWayHandlerService> payWayHandlerServices;

    @Autowired
    private CustomIdGenerator generator;

    @Autowired
    private List<ITrdOrderActivityHandlerService> trdOrderActivityHandlerServices;

    @Autowired
    private SkuApi skuApi;

    @Autowired
    private BalanceRefundCalculationService balanceRefundCalculationService;

    @Autowired
    private BalanceApi balanceApi;


    //订单保存前
    @Override
    public void beforeAfterCreate(OrderAfterSaveVO afterSaveVO, TrdOrder trdOrder, List<TrdSupplierOrder> tsoList) {
        // 计算订单的手续费用
        getTrdPriceService().recalculateAfterPayFee(afterSaveVO.getAfterVO(), afterSaveVO.getSupplierAfterVOs(), afterSaveVO.getSupplierAfterSettleVOs(), trdOrder, tsoList);

        // 计算余额退款金额
        balanceRefundCalculationService.calculateBalanceRefundAmount(afterSaveVO.getAfterVO(), afterSaveVO.getSupplierAfterSettleVOs());
    }

    // 创建订单后(发货前售后)
    @Override
    public void afterOrderAfterCreate(TrdSupplierAfterDtl afterDtl, TrdOrder trdOrder, TrdSupplierOrderDtl tsod) {
        // 执行审核同意操作
        approveAfterReturnOrderDtl(afterDtl);

        // 处理发货前售后更新订单明细信息
        afterUpdateOrderDtlInfo(afterDtl, tsod);

        // 执行提交物流信息操作
        submitLogisticsAfterOrderDtl(afterDtl);

        // 执行审核同意退款操作
        approveAfterRefundOrderDtl(afterDtl, null);

        //发送业务员APP客户信息订单MQ 金额取负数
        tradeMqProducer.sendEsColonelAppBranchEvent(new ColonelAppBranchDTO(trdOrder.getBranchId(), trdOrder.getSysCode(), trdOrder.getPayAmt(), ES_COLONEL_APP_ORDER_STATUS_0));
    }

    @Override
    public void afterOrderAfterReturnActivity(TrdOrder tor, List<TrdSupplierOrder> tsoList, List<TrdSupplierOrderDtl> tsodList, List<TrdOrderDiscountDtl> toddList,
                                              TrdAfter after, List<TrdSupplierAfterDtl> afterDtlList, List<TrdAfterDiscountDtl> afterDiscountDtlList) {
        // 发货前售后 返还已售库存
        afterDtlList.forEach(tsad -> {
            SkuDTO skuDTO = skuApi.getBySkuId(tsad.getSkuId()).getCheckedData();
            redisStockService.incrSkuSaledQty(tsad.getSkuId(), tsad.getReturnQty().multiply(NumberPool.BIGDECIMAL_GROUND));
            skuDTO.setSaleQty(skuDTO.getSaleQty().subtract(tsad.getReturnQty()));

            Long supplierOrderId = tsodList.stream().filter(tsod -> ObjectUtil.equals(tsod.getSupplierOrderDtlId(), tsad.getSupplierOrderDtlId())).findFirst().map(TrdSupplierOrderDtl::getSupplierOrderId).orElse(null);
            Integer pushStatus = tsoList.stream().filter(tso -> ObjectUtil.equals(tso.getSupplierOrderId(), supplierOrderId)).findFirst().map(TrdSupplierOrder::getPushStatus).orElse(PushStatusEnum.UN_PUSHED.getCode());
            if (PushStatusEnum.UN_PUSHED.getCode().equals(pushStatus)) {
                // 未推送，释放已占用库存
                redisStockService.incrSkuOccupiedQty(tsad.getSkuId(), tsad.getReturnQty().negate());
            } else {
                // 已推送，返还总库存
                redisStockService.incrSkuStockQty(tsad.getSkuId(), tsad.getReturnQty());
                skuDTO.setStock(skuDTO.getStock().add(tsad.getReturnQty()));
            }
            skuApi.updateBySku(skuDTO);
        });

        // 发货前售后需要验证优惠劵、赠品活动退单事项
        trdOrderActivityHandlerServices.forEach(service -> {
            service.afterCancelReturnActivity(tor, tsodList, toddList, after, afterDtlList, afterDiscountDtlList);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeAfterReturn(TrdOrder tor, List<TrdSupplierOrder> tsoList, TrdAfter after, List<TrdSupplierAfter> tsaList, List<TrdSupplierAfterDtl> dtlList) {
        // 更新售后单明细表
        trdSupplierAfterDtlMapper.updateBatch(dtlList);
        // 更新主表
        trdAfterMapper.updateById(after);

        // 更新订单已退款手续费金额
        getTrdPriceService().updateOrderInfo(tor, tsoList, after, tsaList, StringPool.DASH);

        dtlList.forEach(afterDtl -> {
            saveOrderDtlLog(afterDtl, AfterHandleStateEnum.THZ.getCode(), AfterHandleStateEnum.REVOKE.getCode(), StatusConstants.OPER_TYPE_UPDATE, AfterHandleStateEnum.REVOKE.getContent());
        });

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(prefix = RedisLockConstants.LOCK_AFTER_ORDER, condition = "#after.afterId", tryLock = true)
    public void cancelAfterReturn(TrdAfter after, List<TrdSupplierAfterDtl> dtlList) {
        // 更新售后单明细表
        trdSupplierAfterDtlMapper.updateBatch(dtlList);
        // 更新主表
        trdAfterMapper.updateById(after);

        dtlList.forEach(afterDtl -> {
            saveOrderDtlLog(afterDtl, AfterHandleStateEnum.DCL.getCode(), AfterHandleStateEnum.CANCEL.getCode(), StatusConstants.OPER_TYPE_UPDATE, AfterHandleStateEnum.CANCEL.getContent());
        });
    }

    @Override
    public void approveAfterReturnOrderDtl(TrdSupplierAfterDtl afterDtl) {
        if (!Objects.equals(ApproveStateEnum.WAIT_APPROVE.getState(), afterDtl.getApproveState())) {
            throw new ServiceException("数据{}不是待审核状态，本次操作失败！" + afterDtl.getSupplierAfterDtlNo());
        }

        afterDtl.setApproveState(ApproveStateEnum.COMPLETE_APPROVE.getState());
        afterDtl.setReturnApproveTime(DateUtils.getNowDate());
        afterDtl.setReturnState(ReturnStateEnum.RETURN_DTH.getState());
        trdSupplierAfterDtlMapper.updateById(afterDtl);

        // 添加订单操作日志
        saveOrderDtlLog(afterDtl, AfterHandleStateEnum.DCL.getCode(), AfterHandleStateEnum.WTH.getCode(), StatusConstants.OPER_TYPE_UPDATE, AfterHandleStateEnum.WTH.getContent());

        // 如果是本地商品退货审核， 默认提交物流单号  发货后售后
        if (Objects.equals(afterDtl.getAfterPhase(), AfterPhaseEnum.REDUND_AFTER_SHIPMENT.getState()) &&
                (afterDtl.getItemType().compareTo(ProductType.LOCAL.getCode().longValue()) == 0 || Objects.equals(afterDtl.getAfterType(), AfterTypeEnum.REDUNDONLY.getState()))
        ) {
            submitLogisticsAfterOrderDtl(afterDtl);
        }

    }


    /**
     * 处理发货前售后更新订单明细信息
     *
     * @param afterDtl
     * @param orderDtl
     */
    private void afterUpdateOrderDtlInfo(TrdSupplierAfterDtl afterDtl, TrdSupplierOrderDtl orderDtl) {
        if (Objects.isNull(orderDtl)) {
            throw new ServiceException(StringUtils.format("发货前售后取消，订单明细【{}】未查询到数据！", afterDtl.getSupplierOrderDtlId()));
        }

        orderDtl.setCancelQty(ToolUtil.isEmptyReturn(orderDtl.getCancelQty(), BigDecimal.ZERO).add(afterDtl.getReturnQty()));
        orderDtl.setCancelAmt(ToolUtil.isEmptyReturn(orderDtl.getCancelAmt(), BigDecimal.ZERO).add(afterDtl.getReturnAmt()));
        // 发货前取消 退货单位字段信息
        orderDtl.setCancelUnit(afterDtl.getReturnUnit());
        orderDtl.setCancelUnitType(afterDtl.getReturnUnitType());
        orderDtl.setCancelUnitQty(afterDtl.getReturnUnitQty());
        orderDtl.setCancelUnitSize(afterDtl.getReturnUnitSize());
        orderDtl.setUpdateTime(DateUtils.getNowDate());
        if (orderDtl.getCancelQty().compareTo(orderDtl.getTotalNum()) > 0) {
            throw new ServiceException("商品SKUID{}取消数量大于订单销售数量，审核失败！" + orderDtl.getSkuId());
        }

        // 如果下单数据和取消数量相等，将该商品状态更为 售后取消
        if (orderDtl.getCancelQty().compareTo(orderDtl.getTotalNum()) == 0) {
            orderDtl.setDeliveryState(DeliveryStatusEnum.CANCEL.getCode());
        }

        trdSupplierOrderDtlMapper.updateById(orderDtl);
    }


    @Override
    public void submitLogisticsAfterOrderDtl(TrdSupplierAfterDtl afterDtl) {
        if (!Objects.equals(ApproveStateEnum.COMPLETE_APPROVE.getState(), afterDtl.getApproveState())
                || !Objects.equals(ReturnStateEnum.RETURN_DTH.getState(), afterDtl.getReturnState())) {
            throw new ServiceException("数据{}不是已同意审核状态，且退货状态不是未退货状态，本次操作失败！" + afterDtl.getSupplierAfterDtlNo());
        }

        afterDtl.setReturnState(ReturnStateEnum.RETURN_THZ.getState());
        if (ObjectUtil.equals(afterDtl.getAfterType(), AfterTypeEnum.REDUNDONLY.getState())) {
            afterDtl.setReturnState(ReturnStateEnum.RETURN_THWC.getState());
            afterDtl.setRefundState(RefundStateEnum.REfUND_DTK.getState());
        }
        trdSupplierAfterDtlMapper.updateById(afterDtl);

        // 添加订单操作日志
        saveOrderDtlLog(afterDtl, AfterHandleStateEnum.WTH.getCode(), AfterHandleStateEnum.THZ.getCode(), StatusConstants.OPER_TYPE_UPDATE, AfterHandleStateEnum.THZ.getContent());
    }

    @Override
    public void approveAfterRefundOrderDtl(TrdSupplierAfterDtl afterDtl, TrdAfter trdAfter) {
        if (!Objects.equals(ApproveStateEnum.COMPLETE_APPROVE.getState(), afterDtl.getApproveState())
                || Objects.equals(ReturnStateEnum.RETURN_DTH.getState(), afterDtl.getReturnState())
                || (!Objects.equals(RefundStateEnum.REfUND_DTK.getState(), afterDtl.getRefundState()) && !Objects.equals(RefundStateEnum.REFUND_FALL.getState(), afterDtl.getRefundState()))
        ) {
            throw new ServiceException("数据{}是未同意审核状态 或 退货状态不为退货中 或 退款状态不为待退款状态 ，本次操作失败！" + afterDtl.getSupplierAfterDtlNo());
        }
        afterDtl.setReturnState(ReturnStateEnum.RETURN_THWC.getState());
        afterDtl.setRefundState(RefundStateEnum.REFUND_TKZ.getState());
        trdSupplierAfterDtlMapper.updateById(afterDtl);

        // 添加订单操作日志
        saveOrderDtlLog(afterDtl, AfterHandleStateEnum.THZ.getCode(), AfterHandleStateEnum.TKZ.getCode(), StatusConstants.OPER_TYPE_UPDATE, AfterHandleStateEnum.TKZ.getContent());

//        // 发起退款申请
//        createPayRefundOrder(afterDtl, trdOrder);
    }

    @Override
    public void payRefundSuccess(List<TrdSupplierAfterDtl> afterDtlList) {
        for (TrdSupplierAfterDtl afterDtl : afterDtlList) {
            /**
             * 1、更新售后订单商品状态为退款完成 {@link com.zksr.common.core.enums.RefundStateEnum}
             */
            afterDtl.setRefundState(RefundStateEnum.REFUND_TKWC.getState());
            afterDtl.setRefundTime(DateUtils.getNowDate());
            trdSupplierAfterDtlMapper.updateById(afterDtl);

            // 售后订单明细结算信息
            TrdSupplierAfterSettle supplierAfterSettle = trdSupplierAfterSettleMapper.selectByAfterDtlId(afterDtl.getSupplierAfterDtlId());
            if (ToolUtil.isEmpty(supplierAfterSettle)) {
                throw exception(TRD_SUPPLIER_AFTER_DTL_SETTLE_NOT_EXISTS, afterDtl.getSupplierAfterDtlId());
            }
            // 订单明细结算信息
            TrdSupplierOrderSettle supplierOrderSettle = trdSupplierOrderSettleMapper.selectSettleByOrderDtlId(afterDtl.getSupplierOrderDtlId());
            if (ToolUtil.isEmpty(supplierOrderSettle)) {
                throw exception(TRD_SUPPLIER_ORDER_DTL_SETTLE_NOT_EXISTS, afterDtl.getSupplierOrderDtlId());
            }

            // 更新订单明细结算 已退款金额和手续费
            supplierOrderSettle.setRefundPayAmt(supplierOrderSettle.getRefundPayAmt().add(supplierAfterSettle.getRefundAmt()));
            supplierOrderSettle.setRefundPayFee(supplierOrderSettle.getRefundPayFee().add(supplierAfterSettle.getRefundFee()));
            trdSupplierOrderSettleMapper.updateById(supplierOrderSettle);

            // 添加订单操作日志
            saveOrderDtlLog(afterDtl, AfterHandleStateEnum.TKZ.getCode(), AfterHandleStateEnum.TKWC.getCode(), StatusConstants.OPER_TYPE_UPDATE, AfterHandleStateEnum.TKWC.getContent());
        }
    }

    @Override
    public void payRefundFail(TrdSupplierAfterDtl afterDtl) {
        /**
         * 1、更新售后订单商品状态为退款失败 {@link com.zksr.common.core.enums.RefundStateEnum}
         */
        afterDtl.setRefundState(RefundStateEnum.REFUND_FALL.getState());
        trdSupplierAfterDtlMapper.updateById(afterDtl);

        // 添加订单操作日志
        saveOrderDtlLog(afterDtl, AfterHandleStateEnum.TKZ.getCode(), AfterHandleStateEnum.TKFALL.getCode(), StatusConstants.OPER_TYPE_UPDATE, AfterHandleStateEnum.TKFALL.getContent());
    }

    @Override
    //!@订单 - 取消 - 3、模拟回调 - 4、创建退款订单
    //!@同意退款 - 2、创建退款订单
    public void createPayRefundOrder(String orderNo, String trdSupplierNo, TrdAfter after, List<TrdSupplierAfterDtl> afterDtls) {
        // 当前进入方式是货到付款时直接返回
        if (after.getPayWay().equals(OrderPayWayEnum.HDFK.getPayWay())) return;

        PayRefundOrderSubmitReqVO refundReqVo = new PayRefundOrderSubmitReqVO();
        refundReqVo.setBusiId(after.getAfterId())
                .setOrderNo(orderNo)
                .setSupplierOrderNo(trdSupplierNo) // 支付是入驻商订单号，退款必须也是一致
                .setRefundAmt(BigDecimal.ZERO)
                .setWalletGiveAmt(BigDecimal.ZERO)
                .setSysCode(after.getSysCode())
                .setOrderType(OrderTypeConstants.MALL)
                .setRefundNo(SheetTypeConstants.TK + redisService.getUniqueNumber())
                .setExtras(new HashMap<>())
                .setSettlements(new ArrayList<>())
        ;


        // 根据售后订单明细ID 查询 售后订单明细结算流水，用于生成退款申请单
        List<TrdSupplierAfterSettle> afterSettles = trdSupplierAfterSettleMapper.selectSettleByAfterDtlId(
                afterDtls.stream()
                        .map(TrdSupplierAfterDtl::getSupplierAfterDtlId)
                        .collect(Collectors.toList())
        );
        Map<Long, TrdSupplierAfterSettle> supplierAfterSettleMap = convertMap(afterSettles, TrdSupplierAfterSettle::getSupplierAfterDtlId);

        // 创建售后订单明细生成流水， 目前用于B2B微信支付退款
        List<TrdSettle> afterSettleList = trdSettleService.createBatchAfterSettle(afterDtls);

        // 根据入驻商ID分组处理
        Map<Long, List<TrdSupplierAfterDtl>> supplierAfterDtlMap = afterDtls.stream().collect(Collectors.groupingBy(TrdSupplierAfterDtl::getSupplierId));

        BigDecimal refundBalanceAmt = BigDecimal.ZERO;
        for (Map.Entry<Long, List<TrdSupplierAfterDtl>> entry : supplierAfterDtlMap.entrySet()) {
            Long key = entry.getKey();
            List<TrdSupplierAfterDtl> value = entry.getValue();
            // 退款申请需要入驻商订单号
            TrdSupplierOrderDtl orderDtl = trdSupplierOrderDtlMapper.selectById(value.get(0).getSupplierOrderDtlId());
            refundReqVo.getExtras().put(PayOrderSubmitExtras.SUPPLIER_ORDER_NO, orderDtl.getSupplierOrderNo());

            // 获取当前入驻商的第三方账户信息，用于退款
            PlatformMerchantDTO merchantDTO = platformMerchantApi.getPlatformMerchant(MerchantTypeEnum.SUPPLIER.getType(), key, after.getPlatform()).getCheckedData();
//            AccAccountDTO account = accountApi.getSupplierAccount(key).getCheckedData();
            if (Objects.equals(OrderPayWayEnum.ONLINE.getPayWay(), after.getPayWay())
                    && PayChannelEnum.getPayOnlineSupportDivide(after.getPlatform())
                    && (ToolUtil.isEmpty(merchantDTO) || ToolUtil.isEmpty(merchantDTO.getAltMchNo()))) {
                throw new ServiceException("入驻商[" + key + "]无第三方账户信息！");
            }
//            String supplierAccountNo = key + "";
//            // 分账信息  获取当前支付方式 属性【是否支持在线分账】值
//            if (PayChannelEnum.getPayOnlineSupportDivide(after.getPlatform())) {
//                supplierAccountNo = merchantDTO.getAltMchNo();
//            }
//
//            OrderSettlementDTO orderSettlementDTO = OrderSettlementDTO.builder()
//                    .accountNo(supplierAccountNo)
//                    .merchantId(key)
//                    .merchantType(MerchantTypeEnum.SUPPLIER.getType())
//                    .amt(BigDecimal.ZERO)
//                    .build();

            value.forEach(afterDtl -> {
//                TrdSupplierAfterSettle afterSettle = supplierAfterSettleMap.get(afterDtl.getSupplierAfterDtlId());
//                orderSettlementDTO.setAmt(orderSettlementDTO.getAmt().add(afterSettle.getSupplierRefundDivideAmt()));
//                refundReqVo.setRefundAmt(afterSettle.getRefundAmt().add(refundReqVo.getRefundAmt()));
                // 回写退款单号到售后订单明细中
                afterDtl.setRefundNo(refundReqVo.getRefundNo());
                trdSupplierAfterDtlMapper.updateById(afterDtl);
            });
            // 支付获取售后退款结算分账数据- 售后退款结算分账数据处理
            getPayWayHandlerService(after.getPlatform())
                    .afterMerchantSettlement(key, after, merchantDTO, supplierAfterSettleMap, afterSettleList, value, refundReqVo);
//            // 兼容合并订单结算分账信息，用于B2B微信支付, 过滤掉结算为0 的数据
//            if (PayChannelEnum.isB2b(after.getPlatform())) {
//                createMerchantSettle(afterSettleList, key, after.getPlatform(), orderSettlementDTO, refundReqVo);
//            }
//            refundReqVo.getSettlements().add(orderSettlementDTO);

            // 计算余额退款金额
            refundBalanceAmt = refundBalanceAmt.add(calculateRefundBalanceAmounts(supplierAfterSettleMap, value));
        }
        // 退还门店余额
        refundBranchBalance(after.getBranchId(), refundBalanceAmt, refundReqVo);
        log.info("发起退款请求,req={}", JSON.toJSONString(refundReqVo));
        if (NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, refundReqVo.getRefundAmt())) {
            log.info("售后订单{}本次退款金额小于等于0，不发起退款请求，直接发起退款成功操作！", after.getAfterNo());
            //售后订单退款成功回调
            tradeMqProducer.sendRefundNotification(
                    new PayRefundVO()
                            .setRefundNo(refundReqVo.getRefundNo())
                            .setStatus(PayRefundStatusEnum.SUCCESS.getStatus())
                            .setOrderNo(refundReqVo.getOrderNo())
                            .setOrderType(refundReqVo.getOrderType())
                            //TODO 为什么要写死？
                            .setOutRefundNo("111111111111")
            );
        } else {
            // 发起退款接口
            payApi.submitPayOrder(refundReqVo).getCheckedData();
        }
    }


    // B2B 微信支付合并订单结算分账信息
    private void createMerchantSettle(List<TrdSettle> afterSettleList, Long supplierId, String platform, OrderSettlementDTO orderSettlementDTO, PayRefundOrderSubmitReqVO refundReqVo) {
        afterSettleList.stream()
                .filter(settle -> Objects.equals(supplierId, settle.getSupplierId()))
                .collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
                .forEach((merchantKey, settleList) -> {
                    Long merchantId = Long.valueOf(merchantKey.split("_")[NumberPool.INT_ZERO]);
                    String merchantType = merchantKey.split("_")[NumberPool.INT_ONE];

                    // 获取当前结算的账户信息
//                    AccAccountDTO accountDTO = trdCacheService.getAccount(merchantId, merchantType, platform);
                    PlatformMerchantDTO merchantDTO = platformMerchantApi.getPlatformMerchant(merchantType, merchantId, platform).getCheckedData();
                    String accountNo = merchantId + "";
                    // 分账信息  获取当前支付方式 属性【是否支持在线分账】值
                    if (ToolUtil.isNotEmpty(merchantDTO) && PayChannelEnum.getPayOnlineSupportDivide(platform)) {
                        accountNo = merchantDTO.getAltMchNo();
                    }

                    OrderSettlementDTO orderMerchantSettlement = OrderSettlementDTO.builder()
                            .accountNo(accountNo)
                            .merchantId(merchantId)
                            .merchantType(merchantType)
                            .build();

                    // 因结算信息表中售后订单填写的金额为负数，所以需要取反
                    BigDecimal settleAmt = settleList.stream().map(TrdSettle::getSettleAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).multiply(new BigDecimal("-1"));
                    orderMerchantSettlement.setAmt(settleAmt);
                    if (settleAmt.compareTo(BigDecimal.ZERO) > NumberPool.INT_ZERO) {
                        refundReqVo.getSettlements().add(orderMerchantSettlement);
                        // 入驻商分账金额 - 分账方金额
                        orderSettlementDTO.setAmt(orderSettlementDTO.getAmt().subtract(settleAmt));
                    }
                });
    }


    /**
     * 计算支付手续费
     */
//    private void calculatorAfterPayFee(OrderAfterSaveVO afterSaveVO, TrdOrder trdOrder){
//        /**
//         * 计算总订单的支付手续费  手续费率取源订单中的手续费率
//         */
//        BigDecimal payRate = afterSaveVO.getAfterVO().getPayRate();
//        BigDecimal payFee = BigDecimal.ZERO;
//        if (OrderPayWayEnum.ONLINE.getPayWay().equals(afterSaveVO.getAfterVO().getPayWay())) {
//            payFee = afterSaveVO.getAfterVO().getRefundAmt().multiply(payRate).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);  //订单手续费总金额
//
//            // 当前售后总金额 大于等于 订单总金额时，退款支付手续费金额 = 订单手续费金额 - 已售后手续费金额
//            if (NumberUtil.isGreaterOrEqual(afterSaveVO.getAfterVO().getRefundAmt().add(trdOrder.getRefundPayAmt()), trdOrder.getPayAmt())) {
//                payFee = trdOrder.getPayFee().subtract(trdOrder.getRefundPayFee());
//            }
//
////            if (payFee.compareTo(BigDecimal.ZERO) == 0) payFee = new BigDecimal("0.01"); // 最低手续费为0.01
//        }
//        afterSaveVO.getAfterVO().setPayFee(payFee);
//
//
//        Map<String, List<TrdSupplierAfterSettle>> afterSettleS = afterSaveVO.getSupplierAfterSettleVOs().stream().collect(Collectors.groupingBy(TrdSupplierAfterSettle::getSupplierAfterNo));
//        /**
//         * 计算入驻商订单的手续费
//         */
//        AtomicInteger i = new AtomicInteger();
//        final BigDecimal[] allocationAmtCount = {BigDecimal.ZERO};  // 已扣减总金额
//        BigDecimal finalPayFee = payFee;
//
//        List<TrdSupplierAfter> supplierAfters = afterSaveVO.getSupplierAfterVOs().stream()
//                .sorted(Comparator.comparing(TrdSupplierAfter::getSubRefundAmt, Comparator.nullsFirst(BigDecimal::compareTo)))
//                .collect(Collectors.toList());
//        supplierAfters.forEach(supplierAfter -> {
//            i.getAndIncrement();
//            BigDecimal subPayFee = BigDecimal.ZERO;  // 入驻商订单手续费金额
//            if (i.get() == afterSaveVO.getSupplierAfterVOs().size()) {
//                subPayFee = finalPayFee.subtract(allocationAmtCount[0]);   // 最后一个入驻商得到剩下的手续金额
//            } else {
//                subPayFee = supplierAfter.getSubRefundAmt().multiply(payRate).setScale(StatusConstants.PRICE_RESERVE_2, BigDecimal.ROUND_HALF_UP);
//            }
//            allocationAmtCount[0] = allocationAmtCount[0].add(subPayFee);
//            supplierAfter.setPayRate(payRate);
//            supplierAfter.setSubRefundFee(subPayFee);
//
//            /**
//             * 计算订单结算数据手续费
//             */
//            final BigDecimal[] allocationSupplierAmtCount = {BigDecimal.ZERO};  // 入驻商已扣减总金额
//            AtomicInteger k = new AtomicInteger();
//            afterSettleS.get(supplierAfter.getSupplierAfterNo()).forEach(settle ->{
//                k.getAndIncrement();
//                BigDecimal subDtlPayFee = BigDecimal.ZERO;  // 入驻商商品手续费金额
//                if (k.get() == afterSettleS.get(supplierAfter.getSupplierAfterNo()).size()) {
//                    subDtlPayFee = supplierAfter.getSubRefundFee().subtract(allocationSupplierAmtCount[0]);   // 最后一个商品得到剩下的入驻商手续金额
//                } else {
//                    subDtlPayFee = settle.getRefundAmt().multiply(payRate).setScale(StatusConstants.PRICE_RESERVE_2, BigDecimal.ROUND_HALF_UP);
//                }
//                allocationSupplierAmtCount[0] = allocationSupplierAmtCount[0].add(subDtlPayFee);
//                settle.setPayRate(payRate);
//                settle.setRefundFee(subDtlPayFee);
//                settle.setSupplierRefundDivideAmt(settle.getRefundAmt().subtract(settle.getRefundFee()));
//            });
//        });
//    }

    /**
     * 计算余额退款金额
     */
    private BigDecimal calculateRefundBalanceAmounts(Map<Long, TrdSupplierAfterSettle> supplierAfterSettleMap, List<TrdSupplierAfterDtl> supplierAfterDtlList) {
        BigDecimal refundBalanceAmt = BigDecimal.ZERO;
        for (TrdSupplierAfterDtl afterDtl : supplierAfterDtlList) {
            TrdSupplierAfterSettle afterSettle = supplierAfterSettleMap.get(afterDtl.getSupplierAfterDtlId());
            refundBalanceAmt = refundBalanceAmt.add(afterSettle.getRefundBalanceAmt());
        }
        return refundBalanceAmt;
    }

    /**
     * 退还门店余额
     */
    private void refundBranchBalance(Long branchId, BigDecimal refundBalanceAmt, PayRefundOrderSubmitReqVO refundReqVo) {
        if (Objects.isNull(refundBalanceAmt) || refundBalanceAmt.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        // 组建余额支付订单取消参数
        MemBranchBalanceDTO balanceDTO = new MemBranchBalanceDTO();
        balanceDTO.setBranchId(branchId);
        balanceDTO.setSysCode(refundReqVo.getSysCode());
        balanceDTO.setTradeNo(refundReqVo.getOrderNo());
        balanceDTO.setOpAmt(refundBalanceAmt);
        balanceDTO.setOperUserName(SecurityContextHolder.getUserName());
        CommonResult<String> operResult = balanceApi.OrderPayCancel(balanceDTO);
        log.info("===================取消订单：{}，回退支付金额流水记录：{}", refundReqVo.getOrderNo(), JSON.toJSONString(operResult));
        if (operResult.isError()) {
            throw new ServiceException("门店余额支付退款失败");
        }
    }

    /**
     * 新增订单明细操作日志
     *
     * @param dtl         订单明细
     * @param beforeState 更新前状态
     * @param afterState  更新后状态
     * @param operType    操作类型
     * @param content     信息
     */
    private void saveOrderDtlLog(TrdSupplierAfterDtl dtl, Long beforeState, Long afterState, Long operType, String content) {
        TrdAfterLog log = new TrdAfterLog();
        log.setSysCode(dtl.getSysCode());
        log.setSupplierAfterDtlId(dtl.getSupplierAfterDtlId());
        log.setSupplierAfterDtlNo(dtl.getSupplierAfterDtlNo());
        log.setBeforeState(beforeState);
        log.setAfterState(afterState);
        log.setOperateType(operType);
        log.setContent(content);
        afterLogMapper.insert(log);

        TrdAfter trdAfter = trdAfterMapper.selectById(dtl.getAfterId());
        //清空门店我的订单状态数据缓存
        trdCacheService.clearOrderTotal(trdAfter.getBranchId());
        //清空业务员下单门店订单状态数据缓存
        trdCacheService.clearOrderTotal(trdAfter.getColonelId());
    }

    ITrdAfterService getTrdAfterService() {
        return SpringUtils.getBean(ITrdAfterService.class);
    }

    /**
     * 获取支付方式处理器
     *
     * @param platform
     * @return
     */
    private ITrdAfterPayWayHandlerService getPayWayHandlerService(String platform) {
        return payWayHandlerServices.stream().filter(handler -> handler.isPlatform(platform))
                .findFirst().orElse(payWayHandlerServices.get(0));
    }

    private ITrdPriceService getTrdPriceService() {
        return SpringUtils.getBean(ITrdPriceService.class);
    }
}
