package com.zksr.trade.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdExpressImport;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportPageReqVO;


/**
 * 快递导入记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Mapper
public interface TrdExpressImportMapper extends BaseMapperX<TrdExpressImport> {
    default PageResult<TrdExpressImport> selectPage(TrdExpressImportPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdExpressImport>()
                    .eqIfPresent(TrdExpressImport::getExpressImportId, reqVO.getExpressImportId())
                    .eqIfPresent(TrdExpressImport::getSysCode, reqVO.getSysCode())
                    .likeIfPresent(TrdExpressImport::getFileName, reqVO.getFileName())
                    .eqIfPresent(TrdExpressImport::getFileUrl, reqVO.getFileUrl())
                    .eqIfPresent(TrdExpressImport::getSupplierId, reqVO.getSupplierId())
                    .eqIfPresent(TrdExpressImport::getTotalNum, reqVO.getTotalNum())
                    .eqIfPresent(TrdExpressImport::getMqSendNum, reqVO.getMqSendNum())
                    .eqIfPresent(TrdExpressImport::getMqReceiveNum, reqVO.getMqReceiveNum())
                    .eqIfPresent(TrdExpressImport::getSuccessNum, reqVO.getSuccessNum())
                    .eqIfPresent(TrdExpressImport::getFailNum, reqVO.getFailNum())
                    .eqIfPresent(TrdExpressImport::getUpdateSupport, reqVO.getUpdateSupport())
                .orderByDesc(TrdExpressImport::getExpressImportId));
    }
}
