package com.zksr.trade.service.impl.handler.activity;

import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponApplyDTO;
import com.zksr.promotion.api.coupon.dto.CouponReturnDTO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.domain.TrdAfterDiscountDtl;
import com.zksr.trade.domain.TrdOrderDiscountDtl;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.mapper.TrdOrderDiscountDtlMapper;
import com.zksr.trade.mapper.TrdSupplierOrderDtlMapper;
import com.zksr.trade.service.handler.activity.ITrdOrderActivityHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *  订单优惠活动处理器 ———— 优惠券
 */
@Service
@Slf4j
public class TrdOrderCouponHandlerServiceImpl implements ITrdOrderActivityHandlerService {

    @Autowired
    private CouponApi couponApi;
    @Autowired
    private TrdOrderDiscountDtlMapper trdOrderDiscountDtlMapper;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;
    @Override
    public void orderCancelReturnActivity(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {
        CouponReturnDTO couponReturnDTO = new CouponReturnDTO();
        couponReturnDTO.setMemo("订单未付款取消");
        couponReturnDTO.setCouponList(new ArrayList<>());// 用于存放可以返还的优惠劵信息

        toddList.stream()
                .filter(discountDtl -> Objects.equals(TrdDiscountTypeEnum.COUPON.getType(), discountDtl.getDiscountType()))
                .collect(Collectors.groupingBy(TrdOrderDiscountDtl::getDiscountId))
                .forEach((key, value) -> {
                    CouponApplyDTO couponApplyDTO = new CouponApplyDTO();
                    couponApplyDTO.setCouponId(key);
                    couponApplyDTO.setRelateOrderNo(tor.getOrderNo());
                    couponReturnDTO.getCouponList().add(couponApplyDTO);
                });

        if (ToolUtil.isNotEmpty(couponReturnDTO.getCouponList()) && !couponReturnDTO.getCouponList().isEmpty()) {
            log.info("订单{}未付款返还优惠劵信息{}", tor.getOrderNo(), couponReturnDTO);
            couponApi.returnCoupon(couponReturnDTO); // 返还优惠卷
        }
    }

    @Override
    public void afterCancelReturnActivity(TrdOrder tor, List<TrdSupplierOrderDtl> tsodList, List<TrdOrderDiscountDtl> toddList,
                                          TrdAfter tar, List<TrdSupplierAfterDtl> tsadList, List<TrdAfterDiscountDtl> taddList)
    {
        CouponReturnDTO couponReturnDTO = new CouponReturnDTO();
        couponReturnDTO.setMemo(tar.getReason());
        couponReturnDTO.setCouponList(new ArrayList<>());// 用于存放可以返还的优惠劵信息

        // 根据订单查询订单优惠明细表，将所有的优惠劵优惠明细查询出 一张优惠劵对应多条明细
        List<TrdOrderDiscountDtl> trdOrderDiscountDtls = trdOrderDiscountDtlMapper.selectList(tor.getOrderId(), TrdDiscountTypeEnum.COUPON.getType());
        // 未找到订单使用优惠劵，直接返回
        if (ToolUtil.isEmpty(trdOrderDiscountDtls) || trdOrderDiscountDtls.size() <= 0)
            return;

        // 根据优惠ID进行分组
        Map<Long, List<TrdOrderDiscountDtl>> trdOrderDiscountDtlMap = trdOrderDiscountDtls.stream().collect(Collectors.groupingBy(TrdOrderDiscountDtl::getDiscountId));
        // 根据入驻商明细ID进行分组
        Map<Long, TrdSupplierOrderDtl> trdSupplierOrderDtlMap = CollectionUtils.convertMap(tsodList, TrdSupplierOrderDtl::getSupplierOrderDtlId);

        trdOrderDiscountDtlMap.forEach((key, value) -> {
            List<TrdSupplierOrderDtl> supplierOrderDtlRes = value.stream().map(trdOrderDiscountDtl -> {
                return trdSupplierOrderDtlMap.get(trdOrderDiscountDtl.getSupplierOrderDtlId());
            }).collect(Collectors.toList());
            /**
             * 当前优惠劵所匹配的商品确认全部取消后（下单数量 == 发货前取消数量）
             * true：改张优惠劵可以退
             * false： 优惠劵核销的商品没有退货完毕，不予返回
             */
            boolean boo = supplierOrderDtlRes.stream().allMatch(orderDtl -> orderDtl.getCancelQty().compareTo(orderDtl.getTotalNum()) == 0);
            if (boo) {  // true：改优惠劵可以退
                CouponApplyDTO couponApplyDTO = new CouponApplyDTO();
                couponApplyDTO.setCouponId(key);
                couponApplyDTO.setRelateOrderNo(tor.getOrderNo());
                couponReturnDTO.getCouponList().add(couponApplyDTO);
            }
        });

        if (!couponReturnDTO.getCouponList().isEmpty()) {
            log.info("返还优惠劵信息{}", couponReturnDTO);
            couponApi.returnCoupon(couponReturnDTO); // 返还优惠卷
        }
    }


    @Override
    public void restoreCancelledOrderDiscounts(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {
        List<CouponApplyDTO> cadList = new ArrayList<>();
        toddList.stream()
                .filter(discountDtl -> Objects.equals(TrdDiscountTypeEnum.COUPON.getType(), discountDtl.getDiscountType()))
                .collect(Collectors.groupingBy(TrdOrderDiscountDtl::getDiscountId))
                .forEach((key, value) -> {
                    CouponApplyDTO couponApplyDTO = new CouponApplyDTO();
                    couponApplyDTO.setCouponId(key);
                    couponApplyDTO.setRelateOrderNo(tor.getOrderNo());
                    cadList.add(couponApplyDTO);
                });

        if (ToolUtil.isNotEmpty(cadList) && !cadList.isEmpty()) {
            log.info("恢复已取消订单{}使用优惠劵信息{}", tor.getOrderNo(), cadList);
            couponApi.applyCoupon(cadList).getCheckedData(); // 优惠券核销
        }
    }
}
