package com.zksr.trade.service.handler.payWay;

import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.domain.TrdSettle;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierAfterSettle;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年02月27日 19:54
 * @description: 售后支付方式 处理器 handler 接口
 *
 */
@Component
public interface ITrdAfterPayWayHandlerService {
    /**
     * 通用处理支付方式
     */
    int ORDER_NONE_ONLINE = 0;
    /**
     * B2B 微信支付（在线支付）
     */
    int ORDER_B2BWX_ONLINE = 1;

    /**
     * B2B 微信支付（在线支付）
     */
    int ORDER_B2BWX_HDFK = 2;

    /**
     * 储值支付（在线支付）
     */
    int ORDER_CZ_ONLINE = 3;

    /**
     * 合利宝支付（在线支付）
     */
    int ORDER_HLB_ONLINE = 4;

    /**
     * 获取当前支付方式
     * @param platform 支付平台
     * @param payWay 支付方式
     * @return true/false
     */
    public Boolean isPlatform(String platform, String payWay);

    /**
     * 获取当前支付方式
     * @param platform 支付平台
     * @return true/false
     */
    public Boolean isPlatform(String platform);


    /**
     * 支付获取售后退款结算分账数据- 售后退款结算分账数据处理
     * @param supplierId 入驻商ID
     * @param after 售后总订单
     * @param merchantDTO 入驻商商户号信息（除储值支付外使用）
     * @param supplierAfterSettleMap 入驻商售后结算分账数据
     * @param afterSettleList 售后结算信息
     * @param supplierAfterDtlList 入驻商售后结算分账明细数据
     * @param refundReqVo 退款请求参数
     */
    default void afterMerchantSettlement(Long supplierId,
                                         TrdAfter after,
                                         PlatformMerchantDTO merchantDTO,
                                         Map<Long, TrdSupplierAfterSettle> supplierAfterSettleMap,
                                         List<TrdSettle> afterSettleList,
                                         List<TrdSupplierAfterDtl> supplierAfterDtlList,
                                         PayRefundOrderSubmitReqVO refundReqVo) {}
}
