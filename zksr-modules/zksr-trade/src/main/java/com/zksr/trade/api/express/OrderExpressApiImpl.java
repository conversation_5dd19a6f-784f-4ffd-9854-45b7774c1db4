package com.zksr.trade.api.express;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.trade.api.express.dto.ExpressResDTO;
import com.zksr.trade.service.ITrdOrderExpressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @date 2024年04月18日 10:59
 * @description: OrderExpressApiImpl
 */
@RestController
@ApiIgnore
public class OrderExpressApiImpl implements OrderExpressApi {
    @Autowired
    private ITrdOrderExpressService trdOrderExpressService;

    @Override
    public CommonResult<ExpressResDTO> getExpressInfoByCourier(String phone, String courierCompanyNo, String courierNumber) {
        return CommonResult.success(trdOrderExpressService.getExpressInfoByCourier(phone, courierCompanyNo, courierNumber));
    }
}
