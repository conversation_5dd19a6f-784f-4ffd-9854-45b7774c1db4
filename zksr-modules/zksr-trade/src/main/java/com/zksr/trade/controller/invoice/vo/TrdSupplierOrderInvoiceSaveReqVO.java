package com.zksr.trade.controller.invoice.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 用户发票对象 trd_supplier_order_invoice
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
@ApiModel("用户发票 - trd_supplier_order_invoice分页 Request VO")
public class TrdSupplierOrderInvoiceSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** ID主键 */
    @ApiModelProperty(value = "版本号")
    private Long id;

    /** 用户发票id */
    @Excel(name = "用户发票id")
    @ApiModelProperty(value = "用户发票id", required = true)
    private Long memberInvoiceId;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty(value = "用户id", required = true)
    private Long memberId;

    /** 入驻商订单ID */
    @Excel(name = "入驻商订单ID")
    @ApiModelProperty(value = "入驻商订单ID", required = true)
    private Long supplierOrderId;

    /** 入驻商订单号 */
    @Excel(name = "入驻商订单号")
    @ApiModelProperty(value = "入驻商订单号", required = true)
    private String supplierOrderNo;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 发票类型,10电子普通发票20专用发票 */
    @Excel(name = "发票类型,10电子普通发票20专用发票")
    @ApiModelProperty(value = "发票类型,10电子普通发票20专用发票", required = true)
    private Long invoiceType;

    /** 发票抬头类型,10个人20单位 */
    @Excel(name = "发票抬头类型,10个人20单位")
    @ApiModelProperty(value = "发票抬头类型,10个人20单位", required = true)
    private Long titleType;

    /** 发票抬头 */
    @Excel(name = "发票抬头")
    @ApiModelProperty(value = "发票抬头")
    private String invoiceTitle;

    /** 纳税人识别码 */
    @Excel(name = "纳税人识别码")
    @ApiModelProperty(value = "纳税人识别码")
    private String taxpayerCode;

    /** 单位地址 */
    @Excel(name = "单位地址")
    @ApiModelProperty(value = "单位地址")
    private String companyAddress;

    /** 单位电话 */
    @Excel(name = "单位电话")
    @ApiModelProperty(value = "单位电话", required = true)
    private String companyPhone;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人", required = true)
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话", required = true)
    private String contactPhone;

    /** 开户银行 */
    @Excel(name = "开户银行")
    @ApiModelProperty(value = "开户银行", required = true)
    private String bankName;

    /** 银行账户 */
    @Excel(name = "银行账户")
    @ApiModelProperty(value = "银行账户", required = true)
    private String bankAccount;

    /** 发票PDF文件下载/访问URL  */
    @Excel(name = "发票PDF文件下载/访问URL ")
    @ApiModelProperty(value = "发票PDF文件下载/访问URL ")
    private String pdfUrl;


    /** 发票号 */
    @ApiModelProperty(name = "发票号")
    private String invoiceNo;

    /** 蓝票发票号码
     红票开票成功，返回对应红冲的蓝票发票号码，无对应蓝票则无 */
//    @ApiModelProperty(name = "蓝票发票号码")
//    private String blueInvoiceNo;

    /** 开票日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "开票日期")
    private Date invoiceDate;

    /** 发票ofd文件下载/访问URL  */
    @ApiModelProperty(name = "发票ofd文件下载/访问URL ")
    private String ofdUrl;

    /** 发票xml文件下载/访问URL  */
    @ApiModelProperty(name = "发票xml文件下载/访问URL ")
    private String xmlUrl;

    /** 发票含税金额  */
    @ApiModelProperty(name = "发票含税金额 ")
    private BigDecimal amtContainTax;

    /** 发票不含税金额  */
    @ApiModelProperty(name = "发票不含税金额 ")
    private BigDecimal amtNotContainTax;

    /** 是否删除：0-否，1-是 */
    @ApiModelProperty(value = "发票PDF文件下载/访问URL ", required = true)
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号", required = true)
    private Long version;

}
