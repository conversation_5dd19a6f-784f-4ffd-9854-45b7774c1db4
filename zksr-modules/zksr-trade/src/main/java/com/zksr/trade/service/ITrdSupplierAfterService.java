package com.zksr.trade.service;

import javax.validation.*;

import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.domain.vo.openapi.receive.*;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.domain.vo.openapi.receive.AfterOrderReceiveCallbackVO;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.api.order.dto.OrderReceiptRespDTO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderSaveReqVO;
import com.zksr.trade.domain.TrdSupplierAfter;
import com.zksr.trade.controller.after.supplierVo.TrdSupplierAfterPageReqVO;
import com.zksr.trade.controller.after.supplierVo.TrdSupplierAfterSaveReqVO;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;

import java.util.List;

/**
 * 入驻商售后单Service接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
public interface ITrdSupplierAfterService {

    /**
     * 新增入驻商售后单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdSupplierAfter(@Valid TrdSupplierAfterSaveReqVO createReqVO);

    /**
     * 修改入驻商售后单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdSupplierAfter(@Valid TrdSupplierAfterSaveReqVO updateReqVO);

    /**
     * 删除入驻商售后单
     *
     * @param supplierAfterId 入驻商订单id
     */
    public void deleteTrdSupplierAfter(Long supplierAfterId);

    /**
     * 批量删除入驻商售后单
     *
     * @param supplierAfterIds 需要删除的入驻商售后单主键集合
     * @return 结果
     */
    public void deleteTrdSupplierAfterBySupplierAfterIds(Long[] supplierAfterIds);

    /**
     * 获得入驻商售后单
     *
     * @param supplierAfterId 入驻商订单id
     * @return 入驻商售后单
     */
    public TrdSupplierAfter getTrdSupplierAfter(Long supplierAfterId);

    /**
     * 获得入驻商售后单分页
     *
     * @param pageReqVO 分页查询
     * @return 入驻商售后单分页
     */
    PageResult<TrdSupplierAfter> getTrdSupplierAfterPage(TrdSupplierAfterPageReqVO pageReqVO);

    /**
     *OpenApi 销售/拒收退货回传回传
     * @param vo
     * @return
     */
    void receiveSalesReturn(AfterSalesReturnVO vo);

    /**
     * OpenApi 订单收货回传（接收拒收退货单）
     *
     * @param vo
     * @return 拒收单号
     */
    boolean receiveOrderTakeDelivery(ReceiveOrderTakeDeliveryVO vo);

    List<TrdSupplierAfter> getSupAfterByOrderNo(String AfterNo);

    TrdSupplierAfter getSupAfterBySupAfterNo(String supplierAfterNo);

    List<TrdSupplierAfterDtl> getSupplierAfterListBySupplierAfterNo(String supplierAfterNo);

    /**
     * 根据ID集合查询订单明细
     * @param ids
     * @return
     */
    List<TrdSupplierAfterDtl> getListByIds(List<Long> ids);

    void afterOrderReceiveCallback(AfterOrderReceiveCallbackVO vo);

    /**
     * 根据供应商售后订单号查询退款信息
     * @param reqVo
     * @return
     */
    List<OrderReceiptRespDTO> getOrderReceiptInfoBySupplierAfterNo(SyncReceiptSendDTO reqVo);

    /**
     * 售后订单推送成功后处理
     * @param data
     */
    void syncAfterResponseSuccess(SyncDataDTO data);

    /**
     * 更新售后订单推送标志
     *
     * @param supplierAfterNo
     * @param pushStatus
     */
    public void updateByPushStatus(String supplierAfterNo,Integer pushStatus);


    /**
     * 退货确认前取消(售后取消)
     * @param vo
     */
    void afterCancel(AfterCancelVO vo);
    
    /**
     * O2O结算和分成
     * @param createReqVO
     */
	void createO2OSettleAndDivide(TrdSupplierOrderSaveReqVO createReqVO);
}
