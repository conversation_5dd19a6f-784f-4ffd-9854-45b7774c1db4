package com.zksr.trade.service.impl;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.enums.LogisticsStatusEnum;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.convert.orderExpress.TrdOrderExpressConvert;
import com.zksr.trade.domain.*;
import com.zksr.trade.mapper.*;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.convert.orderExpressImport.TrdExpressImportDtlConvert;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlPageReqVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlSaveReqVO;
import com.zksr.trade.service.ITrdExpressImportDtlService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 快递导入明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Service
@Slf4j
public class TrdExpressImportDtlServiceImpl implements ITrdExpressImportDtlService {
    @Autowired
    private TrdExpressImportDtlMapper trdExpressImportDtlMapper;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;
    @Autowired
    private List<ITrdOrderHandlerService> trdOrderHandlerServices;
    @Autowired
    private TrdOrderExpressMapper trdOrderExpressMapper;
    @Autowired
    private TrdOrderMapper trdOrderMapper;
    @Autowired
    private TrdCacheService trdCacheService;
    @Autowired
    private TrdExpressImportMapper trdExpressImportMapper;

    /**
     * 新增快递导入明细
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdExpressImportDtl(TrdExpressImportDtlSaveReqVO createReqVO) {
        // 插入
        TrdExpressImportDtl trdExpressImportDtl = TrdExpressImportDtlConvert.INSTANCE.convert(createReqVO);
        trdExpressImportDtlMapper.insert(trdExpressImportDtl);
        // 返回
        return trdExpressImportDtl.getExpressImportDtlId();
    }

    /**
     * 修改快递导入明细
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdExpressImportDtl(TrdExpressImportDtlSaveReqVO updateReqVO) {
        trdExpressImportDtlMapper.updateById(TrdExpressImportDtlConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除快递导入明细
     *
     * @param expressImportDtlId 快递导入明细id
     */
    @Override
    public void deleteTrdExpressImportDtl(Long expressImportDtlId) {
        // 删除
        trdExpressImportDtlMapper.deleteById(expressImportDtlId);
    }

    /**
     * 批量删除快递导入明细
     *
     * @param expressImportDtlIds 需要删除的快递导入明细主键
     * @return 结果
     */
    @Override
    public void deleteTrdExpressImportDtlByExpressImportDtlIds(Long[] expressImportDtlIds) {
        for(Long expressImportDtlId : expressImportDtlIds){
            this.deleteTrdExpressImportDtl(expressImportDtlId);
        }
    }

    /**
     * 获得快递导入明细
     *
     * @param expressImportDtlId 快递导入明细id
     * @return 快递导入明细
     */
    @Override
    public TrdExpressImportDtl getTrdExpressImportDtl(Long expressImportDtlId) {
        return trdExpressImportDtlMapper.selectById(expressImportDtlId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdExpressImportDtl> getTrdExpressImportDtlPage(TrdExpressImportDtlPageReqVO pageReqVO) {
        return trdExpressImportDtlMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional
    @DistributedLock(prefix = RedisLockConstants.LOCK_ORDER_EXPRESS, condition = "#trdExpressImportDtl.supplierOrderDtlNo", tryLock = true)
    public void supplierOrderExpressReceive(TrdExpressImportDtlSaveReqVO trdExpressImportDtl) {
        TrdExpressImportDtl importDtl = trdExpressImportDtlMapper.selectById(trdExpressImportDtl.getExpressImportDtlId());
        if (ToolUtil.isEmpty(importDtl)){
            log.info("发货失败，trdExpressImportDtl信息不存在，trdExpressImportDtl{}"+trdExpressImportDtl.getExpressImportDtlId());
            return;
        }
        if (ToolUtil.isNotEmpty(importDtl.getStatus())) {
            log.info("请勿重复处理，expressImportDtlId{}"+trdExpressImportDtl.getExpressImportDtlId()+"status{}"+trdExpressImportDtl.getStatus());
            return;
        }
        // 获取订单快递导入主表
        TrdExpressImport trdImport = trdExpressImportMapper.selectById(importDtl.getExpressImportId());
        // 查询订单明细数据
        TrdSupplierOrderDtl orderDtl = trdSupplierOrderDtlMapper.getSupplierOrderDtlBySupplierOrderDtlNo(trdExpressImportDtl.getSupplierOrderDtlNo());

        // 未查找到明细数据
        if (ToolUtil.isEmpty(orderDtl)) {
            importDtl.setStatus(StatusConstants.STATUS_FAIL);
            importDtl.setFailReason("订单明细编号【" + importDtl.getSupplierOrderDtlNo() + "】未查找到数据！");
            trdExpressImportDtlMapper.updateById(importDtl);

            trdImport.setMqReceiveNum(trdImport.getMqReceiveNum() + 1);
            trdImport.setFailNum(trdImport.getFailNum() + 1);
            trdExpressImportMapper.updateById(trdImport);
            return;
        }

        // 获取收货人信息
        TrdOrder trdOrder = trdOrderMapper.selectById(orderDtl.getOrderId());
        BranchDTO branchDTO = trdCacheService.getBranchDTO(trdOrder.getBranchId());

        // 未上传物流信息
        if (ToolUtil.isEmpty(importDtl.getExpressNo()) || ToolUtil.isEmpty(importDtl.getExpressCom()) || ToolUtil.isEmpty(importDtl.getExpressComNo())) {
            importDtl.setFailReason("当前数据无快递单号或快递公司或快递公司编码！");
            importDtl.setOrderId(orderDtl.getOrderId());
            importDtl.setOrderNo(trdOrder.getOrderNo());
            importDtl.setSupplierOrderDtlId(orderDtl.getSupplierOrderDtlId());

            importDtl.setStatus(StatusConstants.STATUS_FAIL);
            trdExpressImportDtlMapper.updateById(importDtl);

            trdImport.setMqReceiveNum(trdImport.getMqReceiveNum() + 1);
            trdImport.setFailNum(trdImport.getFailNum() + 1);
            trdExpressImportMapper.updateById(trdImport);
            return;
        }




        String receiveMan = "";
        String receivePhone = "";
        String address = "";
        if (ToolUtil.isNotEmpty(branchDTO)) {
            receiveMan = branchDTO.getContactName();
            receivePhone = branchDTO.getContactPhone();
            address = branchDTO.getBranchAddr();
        }

        /**
         * 更新快递导入明细表数据
         */
        importDtl.setStatus(StatusConstants.STATUS_SUCCESS);
        importDtl.setOrderId(orderDtl.getOrderId());
        importDtl.setOrderNo(trdOrder.getOrderNo());
        importDtl.setSupplierOrderDtlId(orderDtl.getSupplierOrderDtlId());
        trdExpressImportDtlMapper.updateById(importDtl);

        /**
         * 更新快递导入主表表数据
         */
        trdImport.setMqReceiveNum(trdImport.getMqReceiveNum() + 1);
        trdImport.setSuccessNum(trdImport.getSuccessNum() + 1);
        trdExpressImportMapper.updateById(trdImport);


        /**
         * 同一个商品、同一个快递单号，再订单快递表中查询出数据后，执行更新快递信息操作
         */
        List<TrdOrderExpress> orderExpressList = trdOrderExpressMapper.getOrderExpressExistByOrderDtlIdAndExpressNo(importDtl.getSupplierOrderDtlId(), importDtl.getExpressNo());
        if (ToolUtil.isNotEmpty(orderExpressList)) {
            orderExpressList.forEach(orderExpress -> {
                orderExpress.setExpressCom(importDtl.getExpressCom());
                orderExpress.setExpressCom(importDtl.getExpressComNo());
            });
            trdOrderExpressMapper.updateBatch(orderExpressList);
            return;
        }

        /**
         *  新增订单快递表
         */
        TrdOrderExpress express = TrdOrderExpressConvert.INSTANCE.convert(importDtl, receiveMan, receivePhone, address);
        express.setState(LogisticsStatusEnum.ZW.getCode());
        trdOrderExpressMapper.insert(express);

        /**
         * 更新订单商品状态 ，当当前商品状态为 备货中或者代发货 时才更改
         */
        if (Objects.equals(orderDtl.getDeliveryState(), DeliveryStatusEnum.PREAREGOODS.getCode())
                || Objects.equals(orderDtl.getDeliveryState(), DeliveryStatusEnum.WAIT_FH.getCode())){
            // 修改订单明细状态为待收货
            trdOrderHandlerServices.forEach(handler -> handler.orderOutbound(orderDtl));
        }

    }

    @Override
    public List<TrdExpressImportDtl> getExpressImportInfo(Long expressImportId, Integer status) {
        return trdExpressImportDtlMapper.getExpressImportInfo(expressImportId, status);
    }

    private void validateTrdExpressImportDtlExists(Long expressImportDtlId) {
        if (trdExpressImportDtlMapper.selectById(expressImportDtlId) == null) {
            throw exception(TRD_EXPRESS_IMPORT_DTL_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 快递导入明细 TODO 补充编号 ==========
    // ErrorCode TRD_EXPRESS_IMPORT_DTL_NOT_EXISTS = new ErrorCode(TODO 补充编号, "快递导入明细不存在");


}
