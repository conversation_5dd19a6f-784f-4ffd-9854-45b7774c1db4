package com.zksr.trade.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.controller.orderExpressImport.dto.TrdExportOrderDtlDTO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExportOrderDtlVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderRespVO;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdSupplierOrderDtlMapper;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderDtlPageReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderDtlSaveReqVO;
import com.zksr.trade.service.ITrdSupplierOrderDtlService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 入驻商订单明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Service
public class TrdSupplierOrderDtlServiceImpl implements ITrdSupplierOrderDtlService {
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;

    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private List<ITrdOrderHandlerService> trdOrderHandlerServices;

    /**
     * 新增入驻商订单明细
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdSupplierOrderDtl(TrdSupplierOrderDtlSaveReqVO createReqVO) {
        // 插入
        TrdSupplierOrderDtl trdSupplierOrderDtl = HutoolBeanUtils.toBean(createReqVO, TrdSupplierOrderDtl.class);
        trdSupplierOrderDtlMapper.insert(trdSupplierOrderDtl);
        // 返回
        return trdSupplierOrderDtl.getSupplierOrderDtlId();
    }

    /**
     * 修改入驻商订单明细
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdSupplierOrderDtl(TrdSupplierOrderDtlSaveReqVO updateReqVO) {
        trdSupplierOrderDtlMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, TrdSupplierOrderDtl.class));
    }

    /**
     * 删除入驻商订单明细
     *
     * @param supplierOrderDtlId 入驻商订单明细id
     */
    @Override
    public void deleteTrdSupplierOrderDtl(Long supplierOrderDtlId) {
        // 删除
        trdSupplierOrderDtlMapper.deleteById(supplierOrderDtlId);
    }

    /**
     * 批量删除入驻商订单明细
     *
     * @param supplierOrderDtlIds 需要删除的入驻商订单明细主键
     * @return 结果
     */
    @Override
    public void deleteTrdSupplierOrderDtlBySupplierOrderDtlIds(Long[] supplierOrderDtlIds) {
        for(Long supplierOrderDtlId : supplierOrderDtlIds){
            this.deleteTrdSupplierOrderDtl(supplierOrderDtlId);
        }
    }

    /**
     * 获得入驻商订单明细
     *
     * @param supplierOrderDtlId 入驻商订单明细id
     * @return 入驻商订单明细
     */
    @Override
    public TrdSupplierOrderDtl getTrdSupplierOrderDtl(Long supplierOrderDtlId) {
        return trdSupplierOrderDtlMapper.selectById(supplierOrderDtlId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdSupplierOrderDtl> getTrdSupplierOrderDtlPage(TrdSupplierOrderDtlPageReqVO pageReqVO) {
        return trdSupplierOrderDtlMapper.selectPage(pageReqVO);
    }

    /**
    * @Description: 获取入驻商小程序订单详情
    * @Author: liuxingyu
    * @Date: 2024/4/11 9:53
    */
    @Override
    public List<TrdSupplierOrderRespVO> getSupplierOrderDtl(Long orderId) {
         List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
            Map<String, SysDictData> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictValue, item -> item));
        List<TrdSupplierOrderRespVO> supplierOrderRespVOS = HutoolBeanUtils.toBean(
                trdSupplierOrderDtlMapper.getSupplierOrderDtl(orderId, SecurityUtils.getSupplierId()), TrdSupplierOrderRespVO.class);
        supplierOrderRespVOS = supplierOrderRespVOS.stream().peek(x->{
            //实际发货数量
            x.setTotalNum(x.getTotalNum()-x.getCancelQty());

            if (unitMap.containsKey(x.getOrderUnit())) {
                // 设置规格单位名称
                x.setUnit(unitMap.get(x.getOrderUnit()).getDictLabel());
            }
            //填充商品信息
            if (ObjectUtil.isNotNull(x.getSpuId())){
                SpuDTO spuDTO = trdCacheService.getSpuDTO(x.getSpuId());
                if (ObjectUtil.isNotNull(spuDTO)){
                    x.setSpuName(spuDTO.getSpuName());
                    x.setThumb(spuDTO.getThumb());
                    x.setSpecName(spuDTO.getSpecName());
                    x.setSpuNo(spuDTO.getSpuNo());
                }
            }
            if (ObjectUtil.isNotNull(x.getSkuId())){
                SkuDTO skuDTO = trdCacheService.getSkuDTO(x.getSkuId());
                if (ObjectUtil.isNotNull(skuDTO)){
                    x.setBarcode(skuDTO.getBarcode());
                    x.setProperties(PropertyAndValDTO.getProperties(skuDTO.getProperties()));

                }
            }
        }).collect(Collectors.toList());
        return supplierOrderRespVOS;
    }

    @Override
    public List<String> getNotDeliveryNationwideOrderDate(Long supplierId) {
        return trdSupplierOrderDtlMapper.getNotDeliveryNationwideOrderDate(supplierId);
    }

    @Override
    public List<TrdExportOrderDtlDTO> getNotDeliveryNationwideOrderDtl(TrdExportOrderDtlVO expressVo) {
       List<TrdExportOrderDtlDTO> exportDtls = trdSupplierOrderDtlMapper.getNotDeliveryNationwideOrderDtl(expressVo);
        List<TrdExportOrderDtlDTO> returnExports = new ArrayList<>(exportDtls.size());
        exportDtls.stream().forEach(dtl -> {
            SpuDTO spuDTO = trdCacheService.getSpuDTO(dtl.getSpuId());
            if (ToolUtil.isNotEmpty(spuDTO)){
                dtl.setSpuName(spuDTO.getSpuName());

            }
            SkuDTO skuDTO = trdCacheService.getSkuDTO(dtl.getSkuId());
            if (ToolUtil.isNotEmpty(skuDTO)) {
                dtl.setSpecName(PropertyAndValDTO.getProperties(skuDTO.getProperties()));
                dtl.setBarcode(skuDTO.getBarcode());
            }

            SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(dtl.getSupplierId());
            if (ToolUtil.isNotEmpty(supplierDTO)) {
                dtl.setSupplierName(supplierDTO.getSupplierName());
                dtl.setDeliveryBy(supplierDTO.getContactName());
                dtl.setDeliveryIphone(supplierDTO.getContactPhone());
            }
            BranchDTO branchDTO = trdCacheService.getBranchDTO(dtl.getBranchId());
            if (ToolUtil.isNotEmpty(branchDTO)) {
                dtl.setBranchReceiveBy(branchDTO.getContactName());
                dtl.setBranchIphone(branchDTO.getContactPhone());
                dtl.setBranchAddress(branchDTO.getBranchAddr());
            }
            returnExports.add(dtl);
//            Long num = dtl.getTotalNum();
//            for (int i = 0; i< num; i++) {
//                dtl.setTotalNum(1l);
//                returnExports.add(dtl);
//            }
            // 修改订单明细状态为备货中
//            trdOrderHandlerServices.forEach(handler -> handler.orderPreareGoods(trdSupplierOrderDtlMapper.selectById(dtl.getSupplierOrderDtlId())));
        });
        return returnExports;
    }

    @Override
    public TrdSupplierOrderDtl getSupplierOrderDtlBySupplierOrderDtlNo(String supplierOrderDtlNo) {
        return trdSupplierOrderDtlMapper.getSupplierOrderDtlBySupplierOrderDtlNo(supplierOrderDtlNo);
    }

    @Override
    public List<TrdSupplierOrderDtl> getListBySupplierOrderNo(String supplierOrderNo) {
        return trdSupplierOrderDtlMapper.selectListBySupplierOrderNo(supplierOrderNo);
    }

    @Override
    public List<TrdSupplierOrderDtl> getListByIds(List<Long> ids) {
        return trdSupplierOrderDtlMapper.selectListByDtlIdList(ids);
    }

    @Override
    public List<com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderDtlVO> getSupplierOrderDtlByOrderNos(List<String> orderNos) {
        return trdSupplierOrderDtlMapper.getSupplierOrderDtlByOrderNos(orderNos);
    }


    private void validateTrdSupplierOrderDtlExists(Long supplierOrderDtlId) {
        if (trdSupplierOrderDtlMapper.selectById(supplierOrderDtlId) == null) {
            throw exception(TRD_SUPPLIER_ORDER_DTL_NOT_EXISTS);
        }
    }

    @Override
    public List<TrdSupplierOrderDtl>  getCategoryExistOrder(Long categoryId, Long sysCode) {
        return trdSupplierOrderDtlMapper.getCategoryExistOrder(categoryId,sysCode);
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 入驻商订单明细 TODO 补充编号 ==========
    // ErrorCode TRD_SUPPLIER_ORDER_DTL_NOT_EXISTS = new ErrorCode(TODO 补充编号, "入驻商订单明细不存在");


}
