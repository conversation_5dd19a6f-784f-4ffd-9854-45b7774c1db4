package com.zksr.trade.service.impl.handler;

import cn.hutool.core.date.DateUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.system.api.partnerPolicy.dto.SupplierOtherSettingPolicyDTO;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.dto.OrderCutAmtDTO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.mapper.TrdSupplierOrderMapper;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:    订单截团金额计算
 * @date 2024/6/13 16:46
 */
@Slf4j
@Service
@Order(ITrdOrderHandlerService.ORDER_SUBSCRIBE)
public class TrdCutOrderHandlerServiceImpl implements ITrdOrderHandlerService {

    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private TrdSupplierOrderMapper supplierOrderMapper;

    @Autowired
    private Cache<String, OrderCutAmtDTO> orderCutAmtCache;

    @Override
    public void afterOrderPay(TrdOrder order) {
        log.info("收到支付回调, 准备重置门店截单金额计算, orderNo={}", order.getOrderNo());
        Long createTime = System.currentTimeMillis();
        if(ToolUtil.isNotEmpty(order.getCreateTime())){
            createTime = order.getCreateTime().getTime();
        }
        List<TrdSupplierOrder> trdSupplierOrders = supplierOrderMapper.selectListByOrderId(order.getOrderId());
        for (TrdSupplierOrder supplierOrder : trdSupplierOrders) {
            // 如果没有设置截团时间, 则没有补单机制, 需要每次都验证起送金额
            SupplierOtherSettingPolicyDTO settingPolicy = trdCacheService.getPartnerSupplierOtherSettingPolicy(supplierOrder.getSupplierId());
            // 入驻商没有设置, 或者关闭加单机制, 都不用管已下单金额
            if (Objects.isNull(settingPolicy) || StringUtils.isEmpty(settingPolicy.getBetOrder()) || StringPool.ZERO.equals(settingPolicy.getBetOrder())) {
                continue;
            }
            OrderCutAmtDTO.CacheKey cacheKey = new OrderCutAmtDTO.CacheKey();
            // 计算截单时间
            cacheKey.setCutTime(settingPolicy.getCutTime());
            cacheKey.setSupplierId(supplierOrder.getSupplierId());
            cacheKey.setBranchId(order.getBranchId());
            cacheKey.setDate(DateUtils.getCutDate(cacheKey.getCutTime(), DateUtil.date(createTime)));
            try {
                // 移除本地计算
                cacheKey.setProductType(ProductType.LOCAL.getType());
                orderCutAmtCache.remove(cacheKey.toString());
                // 移除全国计算
                cacheKey.setProductType(ProductType.GLOBAL.getType());
                orderCutAmtCache.remove(cacheKey.toString());
            } catch (Exception e) {
                log.error("重置订单截团金额计算异常 " + cacheKey, e);
            }
        }
    }
}
