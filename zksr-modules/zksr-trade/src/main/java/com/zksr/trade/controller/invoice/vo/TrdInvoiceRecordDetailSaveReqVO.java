package com.zksr.trade.controller.invoice.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 发票明细对象 trd_invoice_record_detail
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@ApiModel("发票明细 - trd_invoice_record_detail分页 Request VO")
public class TrdInvoiceRecordDetailSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "税额")
    private Long id;

    /** 发票主表ID */
    @Excel(name = "发票主表ID")
    @ApiModelProperty(value = "发票主表ID", required = true)
    private Long invoiceId;

    /** 上游传入唯一流水号 */
    @Excel(name = "上游传入唯一流水号")
    @ApiModelProperty(value = "上游传入唯一流水号", required = true)
    private String bizId;

    /** 开票项唯一ID */
    @Excel(name = "开票项唯一ID")
    @ApiModelProperty(value = "开票项唯一ID", required = true)
    private String bizDetailId;

    /** 业务单号 */
    @Excel(name = "业务单号")
    @ApiModelProperty(value = "业务单号", required = true)
    private String businessNo;

    /** 商品名称 */
    @Excel(name = "商品名称")
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /** 税收分类编码 */
    @Excel(name = "税收分类编码")
    @ApiModelProperty(value = "税收分类编码")
    private String taxCode;

    /** 税率 */
    @Excel(name = "税率")
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    /** 数量 */
    @Excel(name = "数量")
    @ApiModelProperty(value = "数量")
    private BigDecimal goodCount;

    /** 含税单价 */
    @Excel(name = "含税单价")
    @ApiModelProperty(value = "含税单价")
    private BigDecimal taxUnitPrice;

    /** 含税金额 */
    @Excel(name = "含税金额")
    @ApiModelProperty(value = "含税金额", required = true)
    private BigDecimal amtContainTax;

    /** 税额 */
    @Excel(name = "税额")
    @ApiModelProperty(value = "税额")
    private BigDecimal amtTax;

}
