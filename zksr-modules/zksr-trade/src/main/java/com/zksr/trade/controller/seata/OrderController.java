/*
 *  Copyright 1999-2021 Seata.io Group.
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.zksr.trade.controller.seata;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.seata.StockFeignClient;
import com.zksr.trade.service.OrderService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * Program Name: springcloud-nacos-seata
 * <p>
 * Description:
 * <p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/8/28 4:05 PM
 */
@RestController
@RequestMapping("order")
public class OrderController {

    /**
     * 本案例来自seata-example 工程示例
     *
     * 准备工作
     * 1.安装seata_server，使用nacos作为seata_server的配置中心和注册中心
     *
     * 2.demo中有两个服务，分别是zksr-product和zksr-trade，所以新增seata_server配置如下
     *
     * ~~~properties
     * service.vgroup_mapping.zksr-product-group=default
     * service.vgroup_mapping.zksr-trade-group=default
     * ~~~
     *
     * 3.执行sql
     * zksr-trade库执行
     *DROP TABLE IF EXISTS `order_tbl`;
     * CREATE TABLE `order_tbl` (
     *   `id` int(11) NOT NULL AUTO_INCREMENT,
     *   `user_id` varchar(255) DEFAULT NULL,
     *   `commodity_code` varchar(255) DEFAULT NULL,
     *   `count` int(11) DEFAULT 0,
     *   `money` int(11) DEFAULT 0,
     *   PRIMARY KEY (`id`)
     * ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
     *
     * CREATE TABLE `undo_log`
     * (
     *   `id`            BIGINT(20)   NOT NULL AUTO_INCREMENT,
     *   `branch_id`     BIGINT(20)   NOT NULL,
     *   `xid`           VARCHAR(100) NOT NULL,
     *   `context`       VARCHAR(128) NOT NULL,
     *   `rollback_info` LONGBLOB     NOT NULL,
     *   `log_status`    INT(11)      NOT NULL,
     *   `log_created`   DATETIME     NOT NULL,
     *   `log_modified`  DATETIME     NOT NULL,
     *   `ext`           VARCHAR(100) DEFAULT NULL,
     *   PRIMARY KEY (`id`),
     *   UNIQUE KEY `ux_undo_log` (`xid`, `branch_id`)
     * ) ENGINE = InnoDB
     *   AUTO_INCREMENT = 1
     *   DEFAULT CHARSET = utf8;
     *
     *
     * zksr-product库执行
     *DROP TABLE IF EXISTS `stock_tbl`;
     * CREATE TABLE `stock_tbl` (
     *   `id` int(11) NOT NULL AUTO_INCREMENT,
     *   `commodity_code` varchar(255) DEFAULT NULL,
     *   `count` int(11) DEFAULT 0,
     *   PRIMARY KEY (`id`),
     *   UNIQUE KEY (`commodity_code`)
     * ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
     *
     * CREATE TABLE `undo_log`
     * (
     *   `id`            BIGINT(20)   NOT NULL AUTO_INCREMENT,
     *   `branch_id`     BIGINT(20)   NOT NULL,
     *   `xid`           VARCHAR(100) NOT NULL,
     *   `context`       VARCHAR(128) NOT NULL,
     *   `rollback_info` LONGBLOB     NOT NULL,
     *   `log_status`    INT(11)      NOT NULL,
     *   `log_created`   DATETIME     NOT NULL,
     *   `log_modified`  DATETIME     NOT NULL,
     *   `ext`           VARCHAR(100) DEFAULT NULL,
     *   PRIMARY KEY (`id`),
     *   UNIQUE KEY `ux_undo_log` (`xid`, `branch_id`)
     * ) ENGINE = InnoDB
     *   AUTO_INCREMENT = 1
     *   DEFAULT CHARSET = utf8;
     *
     * -- 初始化库存模拟数据
     * INSERT INTO seata_stock.stock_tbl (id, commodity_code, count) VALUES (1, 'product-1', 9999999);
     * INSERT INTO seata_stock.stock_tbl (id, commodity_code, count) VALUES (2, 'product-2', 0);
     *
     * 4.修改pom文件
     * zksr-product 和 zksr-trade 分别增加
     *         <dependency>
     *             <groupId>com.zksr</groupId>
     *             <artifactId>zksr-common-seata</artifactId>
     *         </dependency>
     *
     *
     * 5.修改配置文件
     * zksr-product增加如下配置
     * # seata配置
     * spring:
     *   datasource:
     *     dynamic:
     *       seata: true
     *       seata-mode: AT #支持XA及AT模式,默认AT
     *
     * seata:
     *   # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启
     *   enabled: true
     *   # Seata 应用编号，默认为 ${spring.application.name}
     *   application-id: ${spring.application.name}
     *   # Seata 事务组编号，用于 TC 集群名
     *   tx-service-group: ${spring.application.name}-group
     *   # 关闭自动代理
     *   enable-auto-data-source-proxy: false
     *   # 服务配置项
     *   service:
     *     # 虚拟组和分组的映射
     *     vgroup-mapping:
     *       zksr-product-group: default
     *   config:
     *     type: nacos
     *     nacos:
     *       serverAddr: 127.0.0.1:8848
     *       username: nacos
     *       password: zksr9876
     *       group: SEATA_GROUP
     *       namespace: seata_server
     *   registry:
     *     type: nacos
     *     nacos:
     *       application: seata-server
     *       server-addr: 127.0.0.1:8848
     *       username: nacos
     *       password: zksr9876
     *       namespace: seata_server
     *
     * zksr-trade同理增加以上配置
     *
     * 6.测试
     *      6.1 分布式事务成功，模拟正常下单、扣库存
     *
     *    127.0.0.1:6105/order/placeOrder/commit
     *
     *      6.2 分布式事务失败，模拟下单成功、扣库存失败，最终同时回滚
     *
     *    127.0.0.1:6105/order/placeOrder/rollback
     */

    @Resource
    private OrderService orderService;
    @Resource
    private StockFeignClient stockFeignClient;

    /**
     * 下单：插入订单表、扣减库存，模拟回滚
     *
     * @return
     */
    @RequestMapping("/placeOrder/commit")
    public CommonResult<Boolean> placeOrderCommit() {

        orderService.placeOrder("1", "product-1", 1);
        return success(true);
    }

    /**
     * 下单：插入订单表、扣减库存，模拟回滚
     *
     * @return
     */
    @RequestMapping("/placeOrder/rollback")
    public CommonResult<Boolean> placeOrderRollback() {
        // product-2 扣库存时模拟了一个业务异常,
        orderService.placeOrder("1", "product-2", 1);
        return success(true);
    }

    @RequestMapping("/placeOrder")
    public Boolean placeOrder(String userId, String commodityCode, Integer count) {
        orderService.placeOrder(userId, commodityCode, count);
        return true;
    }
}
