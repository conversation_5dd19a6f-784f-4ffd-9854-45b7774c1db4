package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 司机档案对象 trd_driver
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@TableName(value = "trd_driver")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdDriver extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 司机id */
    @TableId
    private Long driverId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 司机名 */
    @Excel(name = "司机名")
    private String driverName;

    /** 司机手机号 */
    @Excel(name = "司机手机号")
    private String driverPhone;

}
