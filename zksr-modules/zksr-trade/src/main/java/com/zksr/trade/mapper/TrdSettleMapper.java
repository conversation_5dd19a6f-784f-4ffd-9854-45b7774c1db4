package com.zksr.trade.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.trade.api.order.dto.TrdSettleDTO;
import com.zksr.trade.api.orderSettle.dto.*;
import com.zksr.trade.api.orderSettle.vo.OrderSettlePageVO;
import com.zksr.trade.api.orderSettle.vo.OrderSupplierSettlePageVo;
import com.zksr.trade.controller.settle.vo.TrdSettlePageReqVO;
import com.zksr.trade.controller.statementOfAccount.vo.OrderCommissionStatementReqVO;
import com.zksr.trade.controller.statementOfAccount.vo.OrderCommissionStatementRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdSettle;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


/**
 * 订单结算Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Mapper
public interface TrdSettleMapper extends BaseMapperX<TrdSettle> {
    default PageResult<TrdSettle> selectPage(TrdSettlePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdSettle>()
                    .eqIfPresent(TrdSettle::getSettleId, reqVO.getSettleId())
                    .eqIfPresent(TrdSettle::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdSettle::getOrderNo, reqVO.getOrderNo())
                    .eqIfPresent(TrdSettle::getOrderId, reqVO.getOrderId())
                    .eqIfPresent(TrdSettle::getSupplierOrderNo, reqVO.getSupplierOrderNo())
                    .eqIfPresent(TrdSettle::getSupplierOrderId, reqVO.getSupplierOrderId())
                    .eqIfPresent(TrdSettle::getSupplierOrderDtlNo, reqVO.getSupplierOrderDtlNo())
                    .eqIfPresent(TrdSettle::getSupplierOrderDtlId, reqVO.getSupplierOrderDtlId())
                    .eqIfPresent(TrdSettle::getAfterNo, reqVO.getAfterNo())
                    .eqIfPresent(TrdSettle::getAfterId, reqVO.getAfterId())
                    .eqIfPresent(TrdSettle::getSettleTime, reqVO.getSettleTime())
                    .eqIfPresent(TrdSettle::getState, reqVO.getState())
                    .eqIfPresent(TrdSettle::getSettleAmt, reqVO.getSettleAmt())
                    .eqIfPresent(TrdSettle::getMerchantType, reqVO.getMerchantType())
                    .eqIfPresent(TrdSettle::getMerchantId, reqVO.getMerchantId())
                    .eqIfPresent(TrdSettle::getSupplierId, reqVO.getSupplierId())
                    .eqIfPresent(TrdSettle::getPlatform, reqVO.getPlatform())
                .orderByDesc(TrdSettle::getSettleId));
    }

    /**
     * 根据订单明细Id获取未结算的订单结算数据
     * @param supplierDtlIds
     * @return
     */
    default List<TrdSettle> selectSettleBySupplierDtlIds(List<Long> supplierDtlIds) {
        LambdaQueryWrapperX<TrdSettle> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(TrdSettle::getSupplierOrderDtlId, supplierDtlIds);
        queryWrapperX.eq(TrdSettle::getState, StatusConstants.SETTLE_STATE_0);
        return selectList(queryWrapperX);
    }


    Page<OrderSettleResDTO> getOrderSettleInfoPage(@Param("pageVO") OrderSettlePageVO pageVO, @Param("page") Page<OrderSettlePageVO> page);


    /**
     * 查询账户订单结算数据(入驻商小程序)
     * @param pageVO
     * @return
     */
    List<OrderSupplierSettleResDTO> getSupperOrderSettleByDate(OrderSupplierSettlePageVo pageVO);

    /**
     * 查询账户订单结算数据明细(入驻商小程序)
     * @param pageVO
     * @return
     */
    List<OrderSupplierSettleResDTO> getSupperOrderSettleDtlByDateTime(OrderSupplierSettlePageVo pageVO);


    /**
     * 查询账户订单结算流水数据订单(业务员) APP
     * @param pageVO
     * @return
     */
    Page<OrderSettleColonelResDTO> getColonelOrderSettleInfoPage(@Param("pageVO") OrderSettlePageVO pageVO, @Param("page") Page<OrderSettlePageVO> page);

    /**
     * 查询账户订单结算流水数据明细(业务员) APP
     * @param pageVO
     * @return
     */
    List<OrderDtlSettleResDTO> getColonelOrderSettleDtlInfo(@Param("pageVO") OrderSettlePageVO pageVO);

    /**
     * 根据订单ID查询订单结算数据
     * @param orderId
     * @return
     */
    List<TrdSettle> getSettleListByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据入驻商订单明细ID查询订单结算数据
     * @param orderDtlIds
     * @return
     */
    List<TrdSettle> getSettleListBySupplierOrderDtlIds(@Param("orderDtlIds") List<Long> orderDtlIds);

    /**
     * 根据订单（销售、售后）明细Id集合获取未结算的订单结算数据
     * @param dtlIdList 明细Id集合
     * @return
     */
    default List<TrdSettle> selectSettleBydtlIdList(Set<Long> dtlIdList) {
        LambdaQueryWrapperX<TrdSettle> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.in(TrdSettle::getSupplierOrderDtlId, dtlIdList);
        queryWrapperX.eq(TrdSettle::getState, StatusConstants.SETTLE_STATE_0);
        return selectList(queryWrapperX);
    }

    /**
     * 获取业务员结算统计
     * @param colonelId
     * @return
     */
    ColonelFixSettleTotalRespVO selectColonelSettleTotal(@Param("colonelId") Long colonelId);

    /**
     * 获取业务员指定范围控制查询, 结算数据
     * @param reqVO
     * @return
     */
    ColonelFloatSettleTotalRespVO selectColonelSettleTotalRange(ColonelFloatSettleTotalReqVO reqVO);

    /**
     * 获取业务员指定日期控制查询订单金额、售后金额、结算金额（按天汇总）
     * @param reqVO
     * @return
     */
    List<ColonelCommissionDayTotalRespDTO> selectColonelSettleDayTotalByDateAndColonel(ColonelFloatSettleTotalReqVO reqVO);

    /**
     * 根据入驻商明细订单ID 查询出 当前的结算数据和对应退货订单的结算数据
     * @param
     * @return
     */
    List<TrdSettle> getSettleBySupplierOrderDtlId(@Param("dtlIds") List<Long> dtlIds);

    /**
     * 根据入驻商明细订单ID 、 账户id 、账户名称 查询出 当前的结算数据和对应退货订单的结算数据（用于计算转账解冻金额）
     * @param dtlId
     * @return
     */
    List<TrdSettle> getSettleBySupplierOrderDtlIdAndMerchantId(@Param("dtlId") Long dtlId, @Param("merchantId") Long merchantId, @Param("merchantType") String merchantType);

    /**
     * 根据结算状态查询结算数据
     * @param settleState
     * @return
     */
    default List<TrdSettle> selectSettleListBySettleState(Integer settleState, Long sysCode, Long orderId) {
        LambdaQueryWrapperX<TrdSettle> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(TrdSettle::getState,settleState)
                .eq(TrdSettle::getSysCode, sysCode)
                .eqIfPresent(TrdSettle::getOrderId, orderId)
        ;

        return selectList(queryWrapperX);
    }

    /**
     * 订单分佣对账单
     * @param pageVO
     * @return
     */
    Page<OrderCommissionStatementRespVO> getStatementOfAccountPage(@Param("pageVO") OrderCommissionStatementReqVO pageVO, @Param("page") Page<OrderCommissionStatementReqVO> page);

    /**
     * 根据主订单ID、分佣账户类型、分佣账户 获得订单结算信息
     * @param orderId
     * @param merchantType
     * @param merchantId
     * @return
     */
    List<TrdSettle> getTrdSettleListByOrderIdAndMerchant(@Param("orderId") Long orderId, @Param("merchantType") String merchantType, @Param("merchantId") Long merchantId);

    /**
     * 查询O2O待处理结算的订单信息
     *
     * @param settleState 结算状态
     * @param sysCode 系统编码
     * @param orderNos 订单NO列表
     * @param signAfterMinutes 签收后多少分钟可以结算（默认7天 = 10080分钟）
     * @return 结算信息列表
     */
    List<TrdSettleDTO> selectO2OSettleListBySettleState(
        @Param("settleState") Integer settleState,
        @Param("sysCode") Long sysCode,
        @Param("orderNos")  List<String> orderNos,
        @Param("supplierOrderNos")  List<String> supplierOrderNos,
        @Param("signAfterMinutes") Integer signAfterMinutes,
        @Param("merchantType") String merchantType);


    /**
     * 查询O2O待处理结算的订单信息
     *
     */
    List<TrdSettleDTO> selectO2OSettleListBySupplierOrderNo(
        @Param("settleState") Integer settleState,
        @Param("sysCode") Long sysCode,
        @Param("supplierOrderNo") String supplierOrderNo,
        @Param("merchantType") String merchantType
        );
}
