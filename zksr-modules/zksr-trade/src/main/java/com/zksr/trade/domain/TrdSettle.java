package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 订单结算对象 trd_settle
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@TableName(value = "trd_settle")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdSettle extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 订单结算id */
    @TableId
    private Long settleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 订单id */
    @Excel(name = "订单id")
    private Long orderId;

    /** 全国商品订单编号 */
    @Excel(name = "全国商品订单编号")
    private String supplierOrderNo;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    private Long supplierOrderId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 售后编号 */
    @Excel(name = "售后编号")
    private String afterNo;

    /** 售后单id */
    @Excel(name = "售后单id")
    private Long afterId;

    /** 结算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结算时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date settleTime;

    /** 结算状态 0=未结算，1=已结算 */
    @Excel(name = "结算状态 0=未结算，1=已结算")
    private Integer state;

    /** 结算金额 */
    @Excel(name = "结算金额")
    private BigDecimal settleAmt;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    private Long merchantId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 支付平台(数据字典);从订单表来 */
    @Excel(name = "支付平台(数据字典);从订单表来")
    private String platform;

    /** 结算比例（实际） */
    @Excel(name = "结算比例")
    private BigDecimal settleRate;

}
