package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 购物车对象 trd_car
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@TableName(value = "trd_car")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TrdCar extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 购物车id */
    @TableId(type = IdType.AUTO)
    private Long carId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createMemberId;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updateMemberId;

    /** 门店id */
    @Excel(name = "门店id")
    private Long branchId;

    @Excel(name = "入驻商ID")
    private Long supplierId;

    /** 入驻商上架商品id */
    @Excel(name = "入驻商上架商品id")
    private Long supplierItemId;

    /** 城市上架商品id */
    @Excel(name = "城市上架商品id")
    private Long areaItemId;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    private Long spuId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    private Long skuId;

    /** 数量 */
    @Excel(name = "数量")
    private Long qty;

    /** 是否选中 1-是 0-否 */
    @Excel(name = "是否选中 1-是 0-否")
    private Integer selected;

    /** 是否业务员推荐（1-是 0-否） */
    @Excel(name = "是否业务员推荐", readConverterExp = "1=-是,0=-否")
    private Integer recommendFlag;

    /** 规格尺寸 */
    @Excel(name = "规格尺寸")
    private Integer unitSize;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;
}
