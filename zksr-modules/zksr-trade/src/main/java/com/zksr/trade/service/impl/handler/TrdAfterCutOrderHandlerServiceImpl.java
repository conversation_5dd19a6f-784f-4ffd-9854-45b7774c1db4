package com.zksr.trade.service.impl.handler;

import com.alicp.jetcache.Cache;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.system.api.partnerPolicy.dto.SupplierOtherSettingPolicyDTO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.dto.OrderCutAmtDTO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.domain.TrdAfterDiscountDtl;
import com.zksr.trade.domain.TrdOrderDiscountDtl;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.ITrdAfterHandlerService;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:    公众号/小程序订阅消息, 订单流程参数数据
 * @date 2024/6/13 16:46
 */
@Slf4j
@Service
@Order(ITrdOrderHandlerService.ORDER_SUBSCRIBE)
public class TrdAfterCutOrderHandlerServiceImpl implements ITrdAfterHandlerService {

    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private Cache<String, OrderCutAmtDTO> orderCutAmtCache;

    @Override
    public void afterOrderAfterReturnActivity(TrdOrder order, List<TrdSupplierOrder> tsoList, List<TrdSupplierOrderDtl> tsodList, List<TrdOrderDiscountDtl> toddList, TrdAfter after, List<TrdSupplierAfterDtl> afterDtlList, List<TrdAfterDiscountDtl> afterDiscountDtlList) {
        log.info("收到售后审核回调, 准备重置门店截单金额计算, orderNo={}", order.getOrderNo());
        afterDtlList.stream().map(TrdSupplierAfterDtl::getSupplierId).filter(Objects::nonNull).forEach(supplierId -> {
            // 如果没有设置截团时间, 则没有补单机制, 需要每次都验证起送金额
            SupplierOtherSettingPolicyDTO settingPolicy = trdCacheService.getPartnerSupplierOtherSettingPolicy(supplierId);
            // 入驻商没有设置, 或者关闭加单机制, 都不用管已下单金额
            if (Objects.isNull(settingPolicy) || StringUtils.isEmpty(settingPolicy.getBetOrder()) || StringPool.ZERO.equals(settingPolicy.getBetOrder())) {
                return;
            }
            OrderCutAmtDTO.CacheKey cacheKey = new OrderCutAmtDTO.CacheKey();
            // 计算截单时间
            cacheKey.setCutTime(settingPolicy.getCutTime());
            cacheKey.setSupplierId(supplierId);
            cacheKey.setBranchId(order.getBranchId());
            cacheKey.setDate(DateUtils.getCutDate(cacheKey.getCutTime(), order.getCreateTime()));
            try {
                // 移除本地计算
                cacheKey.setProductType(ProductType.LOCAL.getType());
                orderCutAmtCache.remove(cacheKey.toString());
                // 移除全国计算
                cacheKey.setProductType(ProductType.GLOBAL.getType());
                orderCutAmtCache.remove(cacheKey.toString());
            } catch (Exception e) {
                log.error("重置订单截团金额计算异常 " + cacheKey, e);
            }
        });
    }
}
