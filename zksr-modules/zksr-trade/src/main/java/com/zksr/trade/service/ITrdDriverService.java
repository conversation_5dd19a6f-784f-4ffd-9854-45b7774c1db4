package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import com.zksr.trade.api.driver.excel.TrdDriverImportExcel;
import com.zksr.trade.domain.TrdDriver;
import com.zksr.trade.controller.driver.vo.TrdDriverPageReqVO;
import com.zksr.trade.controller.driver.vo.TrdDriverSaveReqVO;

import java.util.List;

/**
 * 司机档案Service接口
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
public interface ITrdDriverService {

    /**
     * 新增司机档案
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdDriver(@Valid TrdDriverSaveReqVO createReqVO);

    /**
     * 修改司机档案
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdDriver(@Valid TrdDriverSaveReqVO updateReqVO);

    /**
     * 删除司机档案
     *
     * @param driverId 司机id
     */
    public void deleteTrdDriver(Long driverId);

    /**
     * 批量删除司机档案
     *
     * @param driverIds 需要删除的司机档案主键集合
     * @return 结果
     */
    public void deleteTrdDriverByDriverIds(Long[] driverIds);

    /**
     * 获得司机档案
     *
     * @param driverId 司机id
     * @return 司机档案
     */
    public TrdDriver getTrdDriver(Long driverId);

    /**
     * 获得司机档案分页
     *
     * @param pageReqVO 分页查询
     * @return 司机档案分页
     */
    PageResult<TrdDriver> getTrdDriverPage(TrdDriverPageReqVO pageReqVO);

     /**
     * 导入司机信息
     *
     */
     String impordData(List<TrdDriverImportExcel> driverList);

    FileImportHandlerVo impordDataEvent(List<TrdDriverImportExcel> driverList, Long dcId, Long sysCode, Long fileImportId,Integer seq);

}
