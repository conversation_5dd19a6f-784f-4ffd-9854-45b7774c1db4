package com.zksr.trade.service.impl;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.controller.status.vo.TrdExpressStatusPageReqVO;
import com.zksr.trade.controller.status.vo.TrdExpressStatusSaveReqVO;
import com.zksr.trade.convert.status.TrdExpressStatusConvert;
import com.zksr.trade.domain.TrdExpressStatus;
import com.zksr.trade.mapper.TrdExpressStatusMapper;
import com.zksr.trade.service.ITrdExpressStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 * 物流状态（ERP->B2B）Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@Service
public class TrdExpressStatusServiceImpl implements ITrdExpressStatusService {
    @Autowired
    private TrdExpressStatusMapper trdExpressStatusMapper;

    /**
     * 新增物流状态（ERP->B2B）
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdExpressStatus(TrdExpressStatusSaveReqVO createReqVO) {
        // 插入
        TrdExpressStatus trdExpressStatus = TrdExpressStatusConvert.INSTANCE.convert(createReqVO);
        trdExpressStatusMapper.insert(trdExpressStatus);
        // 返回
        return trdExpressStatus.getId();
    }

    /**
     * 修改物流状态（ERP->B2B）
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdExpressStatus(TrdExpressStatusSaveReqVO updateReqVO) {
        trdExpressStatusMapper.updateById(TrdExpressStatusConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除物流状态（ERP->B2B）
     *
     * @param id 主键ID
     */
    @Override
    public void deleteTrdExpressStatus(Long id) {
        // 删除
        trdExpressStatusMapper.deleteById(id);
    }

    /**
     * 批量删除物流状态（ERP->B2B）
     *
     * @param ids 需要删除的物流状态（ERP->B2B）主键
     * @return 结果
     */
    @Override
    public void deleteTrdExpressStatusByIds(Long[] ids) {
        for(Long id : ids){
            this.deleteTrdExpressStatus(id);
        }
    }

    /**
     * 获得物流状态（ERP->B2B）
     *
     * @param id 主键ID
     * @return 物流状态（ERP->B2B）
     */
    @Override
    public TrdExpressStatus getTrdExpressStatus(Long id) {
        return trdExpressStatusMapper.selectById(id);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdExpressStatus> getTrdExpressStatusPage(TrdExpressStatusPageReqVO pageReqVO) {
        return trdExpressStatusMapper.selectPage(pageReqVO);
    }

    private void validateTrdExpressStatusExists(Long id) {
        if (trdExpressStatusMapper.selectById(id) == null) {
            //throw exception(TRD_EXPRESS_STATUS_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.system.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 物流状态（ERP->B2B） TODO 补充编号 ==========
    // ErrorCode TRD_EXPRESS_STATUS_NOT_EXISTS = new ErrorCode(TODO 补充编号, "物流状态（ERP->B2B）不存在");


}
