package com.zksr.trade.controller.invoice;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdInvoiceRecord;
import com.zksr.trade.service.ITrdInvoiceRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.invoice.vo.TrdInvoiceRecordPageReqVO;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceRecordSaveReqVO;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceRecordRespVO;
import com.zksr.trade.convert.record.TrdInvoiceRecordConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 发票操作历史（含开票信息冗余）Controller
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Api(tags = "管理后台 - 发票操作历史（含开票信息冗余）接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/record")
public class TrdInvoiceRecordController {
    @Autowired
    private ITrdInvoiceRecordService trdInvoiceRecordService;

    /**
     * 新增发票操作历史（含开票信息冗余）
     */
    @ApiOperation(value = "新增发票操作历史（含开票信息冗余）", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "发票操作历史（含开票信息冗余）", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdInvoiceRecordSaveReqVO createReqVO) {
        return success(trdInvoiceRecordService.insertTrdInvoiceRecord(createReqVO));
    }

    /**
     * 修改发票操作历史（含开票信息冗余）
     */
    @ApiOperation(value = "修改发票操作历史（含开票信息冗余）", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "发票操作历史（含开票信息冗余）", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdInvoiceRecordSaveReqVO updateReqVO) {
            trdInvoiceRecordService.updateTrdInvoiceRecord(updateReqVO);
        return success(true);
    }

    /**
     * 删除发票操作历史（含开票信息冗余）
     */
    @ApiOperation(value = "删除发票操作历史（含开票信息冗余）", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "发票操作历史（含开票信息冗余）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public CommonResult<Boolean> remove(@PathVariable Long[] ids) {
        trdInvoiceRecordService.deleteTrdInvoiceRecordByIds(ids);
        return success(true);
    }

    /**
     * 获取发票操作历史（含开票信息冗余）详细信息
     */
    @ApiOperation(value = "获得发票操作历史（含开票信息冗余）详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{id}")
    public CommonResult<TrdInvoiceRecordRespVO> getInfo(@PathVariable("id") Long id) {
        TrdInvoiceRecord trdInvoiceRecord = trdInvoiceRecordService.getTrdInvoiceRecord(id);
        return success(TrdInvoiceRecordConvert.INSTANCE.convert(trdInvoiceRecord));
    }

    /**
     * 分页查询发票操作历史（含开票信息冗余）
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得发票操作历史（含开票信息冗余）分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdInvoiceRecordRespVO>> getPage(@Valid TrdInvoiceRecordPageReqVO pageReqVO) {
        PageResult<TrdInvoiceRecord> pageResult = trdInvoiceRecordService.getTrdInvoiceRecordPage(pageReqVO);
        return success(TrdInvoiceRecordConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:record:add";
        /** 编辑 */
        public static final String EDIT = "trade:record:edit";
        /** 删除 */
        public static final String DELETE = "trade:record:remove";
        /** 列表 */
        public static final String LIST = "trade:record:list";
        /** 查询 */
        public static final String GET = "trade:record:query";
        /** 停用 */
        public static final String DISABLE = "trade:record:disable";
        /** 启用 */
        public static final String ENABLE = "trade:record:enable";
    }
}
