package com.zksr.trade.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdSupplierOrderSettle;
import com.zksr.trade.controller.orderSettle.vo.TrdSupplierOrderSettlePageReqVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


/**
 * 入驻商订单结算信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Mapper
public interface TrdSupplierOrderSettleMapper extends BaseMapperX<TrdSupplierOrderSettle> {
    default PageResult<TrdSupplierOrderSettle> selectPage(TrdSupplierOrderSettlePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdSupplierOrderSettle>()
                    .eqIfPresent(TrdSupplierOrderSettle::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdSupplierOrderSettle::getOrderNo, reqVO.getOrderNo())
                    .eqIfPresent(TrdSupplierOrderSettle::getOrderId, reqVO.getOrderId())
                    .eqIfPresent(TrdSupplierOrderSettle::getSupplierOrderNo, reqVO.getSupplierOrderNo())
                    .eqIfPresent(TrdSupplierOrderSettle::getSupplierOrderId, reqVO.getSupplierOrderId())
                    .eqIfPresent(TrdSupplierOrderSettle::getSupplierOrderDtlNo, reqVO.getSupplierOrderDtlNo())
                    .eqIfPresent(TrdSupplierOrderSettle::getSupplierOrderDtlId, reqVO.getSupplierOrderDtlId())
                    .eqIfPresent(TrdSupplierOrderSettle::getItemQty, reqVO.getItemQty())
                    .eqIfPresent(TrdSupplierOrderSettle::getItemPrice, reqVO.getItemPrice())
                    .eqIfPresent(TrdSupplierOrderSettle::getItemAmt, reqVO.getItemAmt())
                    .eqIfPresent(TrdSupplierOrderSettle::getTransAmt, reqVO.getTransAmt())
                    .eqIfPresent(TrdSupplierOrderSettle::getSupplierAmt, reqVO.getSupplierAmt())
                    .eqIfPresent(TrdSupplierOrderSettle::getSoftwareAmt, reqVO.getSoftwareAmt())
                    .eqIfPresent(TrdSupplierOrderSettle::getProfit, reqVO.getProfit())
                    .eqIfPresent(TrdSupplierOrderSettle::getCostPrice, reqVO.getCostPrice())
                    .eqIfPresent(TrdSupplierOrderSettle::getDcRate, reqVO.getDcRate())
                    .eqIfPresent(TrdSupplierOrderSettle::getDcAmt, reqVO.getDcAmt())
                    .eqIfPresent(TrdSupplierOrderSettle::getPartnerRate, reqVO.getPartnerRate())
                    .eqIfPresent(TrdSupplierOrderSettle::getPartnerAmt, reqVO.getPartnerAmt())
                    .eqIfPresent(TrdSupplierOrderSettle::getColonel1Rate, reqVO.getColonel1Rate())
                    .eqIfPresent(TrdSupplierOrderSettle::getColonel1Amt, reqVO.getColonel1Amt())
                    .eqIfPresent(TrdSupplierOrderSettle::getColonel2Rate, reqVO.getColonel2Rate())
                    .eqIfPresent(TrdSupplierOrderSettle::getColonel2Amt, reqVO.getColonel2Amt())
                    .eqIfPresent(TrdSupplierOrderSettle::getMemo, reqVO.getMemo())
                    .eqIfPresent(TrdSupplierOrderSettle::getDelFlag, reqVO.getDelFlag())
                .orderByDesc(TrdSupplierOrderSettle::getSupplierOrderDtlId));
    }

    /**
     * 根据主单号获取入驻商订单明细结算分润信息
     * @param reqVO
     * @return
     */
    default List<TrdSupplierOrderSettle> selectList (TrdSupplierOrderSettlePageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierOrderSettle>()
                .eqIfPresent(TrdSupplierOrderSettle :: getOrderNo, reqVO.getOrderNo()));
    }

    /**
     * 根据主单ID 获取入驻商订单结算信息
     * @param orderId 主单ID
     * @return
     */
    default List<TrdSupplierOrderSettle> selectListByOrderId(Long orderId) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierOrderSettle>()
                .eqIfPresent(TrdSupplierOrderSettle::getOrderId, orderId));
    }

    /**
     * 根据入驻商订单明细ID 获取订单结算信息
     * @param orderDtlId 明细ID
     * @return
     */
    default TrdSupplierOrderSettle selectSettleByOrderDtlId(Long orderDtlId) {
        return selectOne(new LambdaQueryWrapperX<TrdSupplierOrderSettle>()
                .eqIfPresent(TrdSupplierOrderSettle::getSupplierOrderDtlId, orderDtlId));
    }

    /**
     * 根据入驻商订单明细ID 获取订单结算信息
     * @param orderDtlIds 明细ID集合
     * @return
     */
    default List<TrdSupplierOrderSettle> selectSettleByOrderDtlIds(List<Long> orderDtlIds) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierOrderSettle>()
                .inIfPresent(TrdSupplierOrderSettle::getSupplierOrderDtlId, orderDtlIds));
    }

    /**
     * 更新入驻商订单打印数量
     *
     * @param supplierOrderNos
     */
    public  void editOrderPrintQtyBySupplierOrderNos(@Param("supplierOrderNos") String[] supplierOrderNos);

    /**
     * 根据业务员ID获取业务员APP首页的提成金额（当天）
     *
     * @param colonelId
     * @return
     */
    BigDecimal getColonelAppPercentageAmt(Long colonelId);
}
