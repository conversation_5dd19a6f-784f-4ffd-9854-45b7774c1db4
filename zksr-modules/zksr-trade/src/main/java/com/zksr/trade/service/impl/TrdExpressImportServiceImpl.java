package com.zksr.trade.service.impl;

import com.zksr.common.core.web.pojo.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdExpressImportMapper;
import com.zksr.trade.convert.orderExpressImport.TrdExpressImportConvert;
import com.zksr.trade.domain.TrdExpressImport;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportPageReqVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportSaveReqVO;
import com.zksr.trade.service.ITrdExpressImportService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 快递导入记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Service
@Slf4j
public class TrdExpressImportServiceImpl implements ITrdExpressImportService {
    @Autowired
    private TrdExpressImportMapper trdExpressImportMapper;

    /**
     * 新增快递导入记录
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public TrdExpressImport insertTrdExpressImport(TrdExpressImportSaveReqVO createReqVO) {

        // 插入
        TrdExpressImport trdExpressImport = TrdExpressImportConvert.INSTANCE.convert(createReqVO);
        trdExpressImportMapper.insert(trdExpressImport);
        // 返回
        return trdExpressImport;
    }

    /**
     * 修改快递导入记录
     *
     * @param trdExpressImport 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdExpressImport(TrdExpressImport trdExpressImport) {
        trdExpressImportMapper.updateById(trdExpressImport);
    }

    /**
     * 删除快递导入记录
     *
     * @param expressImportId 快递导入记录id
     */
    @Override
    public void deleteTrdExpressImport(Long expressImportId) {
        // 删除
        trdExpressImportMapper.deleteById(expressImportId);
    }

    /**
     * 批量删除快递导入记录
     *
     * @param expressImportIds 需要删除的快递导入记录主键
     * @return 结果
     */
    @Override
    public void deleteTrdExpressImportByExpressImportIds(Long[] expressImportIds) {
        for(Long expressImportId : expressImportIds){
            this.deleteTrdExpressImport(expressImportId);
        }
    }

    /**
     * 获得快递导入记录
     *
     * @param expressImportId 快递导入记录id
     * @return 快递导入记录
     */
    @Override
    public TrdExpressImport getTrdExpressImport(Long expressImportId) {
        return trdExpressImportMapper.selectById(expressImportId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdExpressImport> getTrdExpressImportPage(TrdExpressImportPageReqVO pageReqVO) {
        return trdExpressImportMapper.selectPage(pageReqVO);
    }

    private void validateTrdExpressImportExists(Long expressImportId) {
        if (trdExpressImportMapper.selectById(expressImportId) == null) {
            throw exception(TRD_EXPRESS_IMPORT_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 快递导入记录 TODO 补充编号 ==========
    // ErrorCode TRD_EXPRESS_IMPORT_NOT_EXISTS = new ErrorCode(TODO 补充编号, "快递导入记录不存在");


}
