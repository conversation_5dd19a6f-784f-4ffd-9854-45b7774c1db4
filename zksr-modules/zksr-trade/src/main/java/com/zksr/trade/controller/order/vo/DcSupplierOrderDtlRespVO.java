package com.zksr.trade.controller.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *  @description: PC端订单明细查询页面返回实体
 */
@ApiModel("PC端订单明细查询页面返回实体")
@Data
public class DcSupplierOrderDtlRespVO {

    @ApiModelProperty("入驻商订单明细Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierOrderDtlId;

    @ApiModelProperty("入驻商订单明细编号")
    private String supplierOrderDtlNo;

    @ApiModelProperty("产品编号")
    private String spuNo;

    @ApiModelProperty("产品名称")
    private String spuName;

    @ApiModelProperty("条形码")
    private String barcode;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("要货数量(销售数量)")
    private Long demandNum;

    @ApiModelProperty("要货金额(销售金额)")
    private BigDecimal demandAmt;

    @ApiModelProperty("发货数量(实际发货数量)")
    private Long totalNum;

    @ApiModelProperty("金额（订单发货金额）")
    private BigDecimal totalAmt;

    @ApiModelProperty("差异数量")
    private Long shortageNum;

    @ApiModelProperty("差异金额")
    private BigDecimal discrepancyAmt;

    @ApiModelProperty("spuId")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long spuId;

    @ApiModelProperty("skuId")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;

    @ApiModelProperty("sku单位")
    private Long skuUnit;

    @ApiModelProperty("sku属性")
    private String skuProperties;

    @ApiModelProperty("合计金额")
    private BigDecimal saleTotalAmt;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmt;

    @ApiModelProperty("图片地址")
    private String thumb;

    @ApiModelProperty("是否是赠品 1-是  0-否")
    private Integer giftFlag;

    @ApiModelProperty("售后提示信息")
    private String afterPromptInfo;

    @ApiModelProperty("发货前已售后金额")
    private BigDecimal afterReturnAmt;

    @ApiModelProperty("销售原价")
    private BigDecimal originalPrice;

    /** 最旧生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最旧生产日期")
    private Date oldestDate;

    /** 最新生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最新生产日期")
    private Date latestDate;

    @ApiModelProperty("发货后已售后金额")
    private BigDecimal afterReturnAmtAfter = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否可售后 (数据字典 sys_status_type)", hidden = true)
    private Integer isAfterSales = NumberPool.INT_ZERO;

    @ApiModelProperty(value = "可售后时间（存储类型为分钟）", hidden = true)
    private Long afterSalesTime;

    @ApiModelProperty("快递信息集合")
    private List<TrdOrderExpressRespVO> expressesList;

    @ApiModelProperty("发货后售后信息集合")
    private List<DcSupplierOrderAfterDtlRespVO> afterDtlRespVOList;

    @ApiModelProperty("发货前售后信息集合")
    private List<DcSupplierOrderAfterDtlRespVO> beforeAfterDtlRespVOList;

    @ApiModelProperty("商品优惠信息集合")
    private List<DcSupplierOrderDtlDiscountRespVO> dcSupplierOrderDtlDiscountRespVOList;
}
