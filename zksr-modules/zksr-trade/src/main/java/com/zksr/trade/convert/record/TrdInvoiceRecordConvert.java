package com.zksr.trade.convert.record;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdInvoiceRecord;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceRecordRespVO;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceRecordSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 发票操作历史（含开票信息冗余） 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-07-19
*/
@Mapper
public interface TrdInvoiceRecordConvert {

    TrdInvoiceRecordConvert INSTANCE = Mappers.getMapper(TrdInvoiceRecordConvert.class);

    TrdInvoiceRecordRespVO convert(TrdInvoiceRecord trdInvoiceRecord);

    TrdInvoiceRecord convert(TrdInvoiceRecordSaveReqVO trdInvoiceRecordSaveReq);

    PageResult<TrdInvoiceRecordRespVO> convertPage(PageResult<TrdInvoiceRecord> trdInvoiceRecordPage);
}