package com.zksr.trade.convert.afterDiscount;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdAfterDiscountDtl;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.trade.domain.TrdOrderDiscountDtl;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 售后优惠明细 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-17
*/
@Mapper
public interface TrdAfterDiscountDtlConvert {

    TrdAfterDiscountDtlConvert INSTANCE = Mappers.getMapper(TrdAfterDiscountDtlConvert.class);

    List<TrdAfterDiscountDtl> convertOrderDiscountList(List<TrdOrderDiscountDtl> trdOrderDiscountDtls);

    TrdAfterDiscountDtl convertOrderDiscount(TrdOrderDiscountDtl trdOrderDiscountDtl);

}