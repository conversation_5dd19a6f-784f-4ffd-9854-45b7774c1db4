package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 入驻商订单结算信息对象 trd_supplier_order_settle
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@TableName(value = "trd_supplier_order_settle")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdSupplierOrderSettle extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 订单id */
    @Excel(name = "订单id")
    private Long orderId;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    private String supplierOrderNo;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    private Long supplierOrderId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 入驻商订单明细id */
    @TableId
    private Long supplierOrderDtlId;

    /** 商品数量 */
    @Excel(name = "商品数量")
    private BigDecimal itemQty;

    /** 商品销售价 */
    @Excel(name = "商品销售价")
    private BigDecimal itemPrice;

    /** 商品金额 */
    @Excel(name = "商品金额")
    private BigDecimal itemAmt;

    /** 运费 */
    @Excel(name = "运费")
    private BigDecimal transAmt;

    /** 入驻商结算金额 */
    @Excel(name = "入驻商结算金额")
    private BigDecimal supplierAmt;

    /** 利润 */
    @Excel(name = "利润")
    private BigDecimal profit;

    /** 入驻商成本价 */
    @Excel(name = "入驻商成本价")
    private BigDecimal costPrice;

    /**
     * 商品销售利润比例 占比, 最大0.29 = 29%
     */
    private BigDecimal saleTotalRate;

    /**
     * 利润模式 0=(售价*比例=利润), 1=(售价-进货价=利润)
     */
    private String profitModel;

    /** 软件商分润比例 */
    private BigDecimal softwareRate;

    /** 软件商结算金额 */
    private BigDecimal softwareAmt;

    /** 平台商分润比例 */
    @Excel(name = "平台商分润比例")
    private BigDecimal partnerRate;

    /** 平台商结算金额 */
    @Excel(name = "平台商结算金额")
    private BigDecimal partnerAmt;

    /** 运营商分润比例 */
    @Excel(name = "运营商分润比例")
    private BigDecimal dcRate;

    /** 运营商结算金额 */
    @Excel(name = "运营商结算金额")
    private BigDecimal dcAmt;

    /** 业务员负责人分润比例 */
    @Excel(name = "业务员负责人分润比例")
    private BigDecimal colonel1Rate;

    /** 业务员结算金额 */
    @Excel(name = "业务员结算金额")
    private BigDecimal colonel1Amt;

    /** 业务员分润比例 */
    @Excel(name = "业务员分润比例")
    private BigDecimal colonel2Rate;

    /** 业务员结算金额 */
    @Excel(name = "业务员结算金额")
    private BigDecimal colonel2Amt;
    
    /** 门店分润比例 */
    @Excel(name = "门店分润比例")
    private BigDecimal branchRate;
    
    /** 门店计算金额 */
    @Excel(name = "门店结算金额")
    private BigDecimal branchAmt;
    

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 删除状态(0:正常，2：删除) */
    private Integer delFlag;

    /** 支付金额 */
    @Excel(name = "商品支付金额")
    private BigDecimal payAmt;

    /** 支付公司收取的支付费率 */
    @Excel(name = "支付公司收取的支付费率")
    private BigDecimal payRate;

    /** 支付平台手续费;(pay_amt*pay_rate) 四舍五入 */
    @Excel(name = "支付平台手续费")
    private BigDecimal payFee;

    /** 入驻商分钱;pay_amt - pay_fee */
    @Excel(name = "入驻商分钱金额")
    private BigDecimal supplierDivideAmt;

    /**
     * 总运营商分润比例
     */
    @Excel(name = "总运营商分润比例")
    private BigDecimal allDcRate;

    /**
     * 运营商设置的运营商分润比例
     */
    @Excel(name = "运营商设置的运营商分润比例")
    private BigDecimal settingDcRate;

    /**
     * 运营商设置的业务员管理员分润比例
     */
    @Excel(name = "运营商设置的业务员管理员分润比例")
    private BigDecimal settingColonel1Rate;

    /**
     * 业务员管理员提成系数
     */
    @Excel(name = "业务员管理员提成系数")
    private BigDecimal colonel1Percentage;

    /**
     * 满额业务员管理员分润金额
     */
    @Excel(name = "满额业务员管理员分润金额")
    private BigDecimal allColonel1Amt;


    /**
     * 运营商设置的业务员分润比例
     */
    @Excel(name = "运营商设置的业务员分润比例")
    private BigDecimal settingColonel2Rate;

    /**
     * 业务员提成系数
     */
    @Excel(name = "业务员提成系数")
    private BigDecimal colonel2Percentage;

    /**
     * 满额业务员分润金额
     */
    @Excel(name = "满额业务员分润金额")
    private BigDecimal allColonel2Amt;

    /**
     * 已退款支付金额（售后总金额）
     */
    @Excel(name = "已退款支付金额(商品)")
    private BigDecimal refundPayAmt;

    /**
     * 支付公司已退款手续费金额
     */
    @Excel(name = "支付公司已退款手续费金额(商品)")
    private BigDecimal refundPayFee;

    /**
     * 储值本金比率
     */
    @Excel(name = "储值本金比率")
    private BigDecimal czPrincipalRate = BigDecimal.ZERO;

    /**
     * 储值本金支付金额
     */
    @Excel(name = "储值本金支付金额")
    private BigDecimal czPrincipalPayAmt = BigDecimal.ZERO;

    /**
     * 储值赠金支付金额
     */
    @Excel(name = "储值赠金支付金额")
    private BigDecimal czGivePayAmt = BigDecimal.ZERO;

    /**
     * 售后储值本金退款金额
     */
    @Excel(name = "售后储值本金退款金额")
    private BigDecimal returnCzPrincipalPayAmt = BigDecimal.ZERO;

    /**
     * 售后储值赠金退款金额
     */
    @Excel(name = "售后储值赠金退款金额")
    private BigDecimal returnCzGivePayAmt = BigDecimal.ZERO;

}
