package com.zksr.trade.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdHdfkPayDtlMapper;
import com.zksr.trade.convert.hdfk.TrdHdfkPayDtlConvert;
import com.zksr.trade.domain.TrdHdfkPayDtl;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayDtlPageReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayDtlSaveReqVO;
import com.zksr.trade.service.ITrdHdfkPayDtlService;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 货到付款付款单明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Service
public class TrdHdfkPayDtlServiceImpl implements ITrdHdfkPayDtlService {
    @Autowired
    private TrdHdfkPayDtlMapper trdHdfkPayDtlMapper;

    /**
     * 新增货到付款付款单明细
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdHdfkPayDtl(TrdHdfkPayDtlSaveReqVO createReqVO) {
        // 插入
        TrdHdfkPayDtl trdHdfkPayDtl = TrdHdfkPayDtlConvert.INSTANCE.convert(createReqVO);
        trdHdfkPayDtlMapper.insert(trdHdfkPayDtl);
        // 返回
        return trdHdfkPayDtl.getHdfkPayDtlId();
    }

    /**
     * 修改货到付款付款单明细
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdHdfkPayDtl(TrdHdfkPayDtlSaveReqVO updateReqVO) {
        trdHdfkPayDtlMapper.updateById(TrdHdfkPayDtlConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除货到付款付款单明细
     *
     * @param hdfkPayDtlId 货到付款付款单id
     */
    @Override
    public void deleteTrdHdfkPayDtl(Long hdfkPayDtlId) {
        // 删除
        trdHdfkPayDtlMapper.deleteById(hdfkPayDtlId);
    }

    /**
     * 批量删除货到付款付款单明细
     *
     * @param hdfkPayDtlIds 需要删除的货到付款付款单明细主键
     * @return 结果
     */
    @Override
    public void deleteTrdHdfkPayDtlByHdfkPayDtlIds(Long[] hdfkPayDtlIds) {
        for(Long hdfkPayDtlId : hdfkPayDtlIds){
            this.deleteTrdHdfkPayDtl(hdfkPayDtlId);
        }
    }

    /**
     * 获得货到付款付款单明细
     *
     * @param hdfkPayDtlId 货到付款付款单id
     * @return 货到付款付款单明细
     */
    @Override
    public TrdHdfkPayDtl getTrdHdfkPayDtl(Long hdfkPayDtlId) {
        return trdHdfkPayDtlMapper.selectById(hdfkPayDtlId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdHdfkPayDtl> getTrdHdfkPayDtlPage(TrdHdfkPayDtlPageReqVO pageReqVO) {
        return trdHdfkPayDtlMapper.selectPage(pageReqVO);
    }

}
