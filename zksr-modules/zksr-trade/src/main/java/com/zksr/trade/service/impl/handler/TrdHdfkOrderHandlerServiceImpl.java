package com.zksr.trade.service.impl.handler;

import com.zksr.account.api.transfer.TransferApi;
import com.zksr.account.api.transfer.dto.TransferSaveDTO;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.OrderPayWayEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayStateEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.service.RedisService;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdPayOrderPageVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSaveReqVO;
import com.zksr.trade.controller.order.vo.TrdOrderPageReqVO;
import com.zksr.trade.convert.hdfk.TrdHdfkSettleConvert;
import com.zksr.trade.domain.TrdSettle;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.mapper.TrdOrderMapper;
import com.zksr.trade.mapper.TrdSettleMapper;
import com.zksr.trade.mapper.TrdSupplierOrderDtlMapper;
import com.zksr.trade.mapper.TrdSupplierOrderMapper;
import com.zksr.trade.service.ITrdHdfkSettleService;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年05月30日 14:12
 * @description: TrdHdfkOrderHandlerServiceImpl
 */
@Service
@Slf4j
@Order(ITrdOrderHandlerService.ORDER_HDFK)
public class TrdHdfkOrderHandlerServiceImpl implements ITrdOrderHandlerService {
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;
    @Autowired
    private TrdOrderMapper trdOrderMapper;
    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;
    @Autowired
    private TrdSettleMapper trdSettleMapper;

    @Autowired
    private ITrdHdfkSettleService trdHdfkSettleService;
    @Autowired
    private RedisService redisService;

    @Autowired
    private TransferApi transferApi;

    @Override
    public void orderPay(TrdPayOrderPageVO pageVo, TrdOrder order) {
        // 非货到付款订单直接返回
        if (!OrderPayWayEnum.HDFK.getPayWay().equals(order.getPayWay())) {
            return;
        }

       List<TrdSupplierOrderDtl> dtlList =  trdSupplierOrderDtlMapper.selectListByOrderId(order.getOrderId());
        // 新增货到付款订单结算信息
        List<TrdHdfkSettleSaveReqVO> createReqVO = dtlList.stream().map(supplierOrderDtl -> {
            TrdHdfkSettleSaveReqVO trdHdfkSettleVo = TrdHdfkSettleConvert.INSTANCE.convertHdfkSettleVO(supplierOrderDtl);
            trdHdfkSettleVo.setBranchId(order.getBranchId());
            trdHdfkSettleVo.setOrderNo(order.getOrderNo());
            trdHdfkSettleVo.setSettleType(NumberPool.INT_ZERO);
            trdHdfkSettleVo.setSettleAmt(supplierOrderDtl.getTotalAmt());
            return trdHdfkSettleVo;
        }).collect(Collectors.toList());
        trdHdfkSettleService.insertTrdHdfkSettle(createReqVO);
    }

    @Override
    public void hdfkOrderDtlPaySuccessUpdateStatus(TrdSupplierOrderDtl orderDtl) {
        orderDtl.setPayState(PayStateEnum.PAY_ALREADY_HDFK.getCode());
        trdSupplierOrderDtlMapper.updateById(orderDtl);
    }

    @Override
    public void hdfkOrderPaySuccessUpdateStatus(List<TrdSupplierOrderDtl> orderDtlList) {
        // 更新主订单支付状态
        boolean boo = checkHdfkOrderPaySuccess(orderDtlList);
        if (boo) {
            TrdOrder order = trdOrderMapper.selectById(orderDtlList.get(0).getOrderId());
            order.setPayState(PayStateEnum.PAY_ALREADY_HDFK.getCode());
            trdOrderMapper.updateById(order);
        }

        // 更新入驻商订单支付状态
        Map<Long, List<TrdSupplierOrderDtl>> supplierOrderIdMap = orderDtlList.stream().collect(Collectors.groupingBy(TrdSupplierOrderDtl::getSupplierOrderId));
        supplierOrderIdMap.forEach((key, value) -> {
            boolean supplierStatus = checkHdfkOrderPaySuccess(value);
            if (supplierStatus) {
                TrdSupplierOrder supplierOrder = trdSupplierOrderMapper.selectById(key);
                supplierOrder.setPayState(PayStateEnum.PAY_ALREADY_HDFK.getCode());
                trdSupplierOrderMapper.updateById(supplierOrder);
            }
        });
    }

    /**
     * 校验是否所有订单明细已支付
     * @param orderDtlList
     * @return
     */
    private boolean checkHdfkOrderPaySuccess(List<TrdSupplierOrderDtl> orderDtlList) {
        return orderDtlList.stream().allMatch(orderDtl -> Objects.equals(orderDtl.getPayState(), PayStateEnum.PAY_ALREADY_HDFK.getCode()));
    }
}
