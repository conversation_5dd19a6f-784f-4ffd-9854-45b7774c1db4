package com.zksr.trade.service.impl.handler.payWay;

import com.alibaba.fastjson2.JSON;
import com.zksr.account.api.pay.PayApi;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.account.model.pay.vo.PayWxB2bCreateDivideReqVO;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.OrderPayWayEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.dto.OrderPayInfoRespDTO;
import com.zksr.trade.api.order.dto.TrdSupplierResDto;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdSupplierPageVO;
import com.zksr.trade.domain.TrdSettle;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.mapper.TrdSettleMapper;
import com.zksr.trade.mapper.TrdSupplierOrderDtlMapper;
import com.zksr.trade.service.handler.payWay.ITrdOrderPayWayHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 微信B2B支付方式处理器 - 在线支付
 */
@Service
@Slf4j
@Order(ITrdOrderPayWayHandlerService.ORDER_B2BWX_ONLINE)
public class TrdOrderB2bWxPayWayHandlerServiceimpl implements ITrdOrderPayWayHandlerService {

    @Autowired
    private TrdSettleMapper trdSettleMapper;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;


    @Autowired
    private PayApi payApi;

    @Override
    public Boolean isPlatform(String platform, String payWay) {
        return Objects.equals(PayChannelEnum.WX_B2B_PAY.getCode(), platform) && Objects.equals(OrderPayWayEnum.ONLINE.getPayWay(), payWay);
    }

    @Override
    public Boolean isPlatform(String platform) {
        return Objects.equals(PayChannelEnum.WX_B2B_PAY.getCode(), platform);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderCompleteCreateSettleTransfer(TrdOrder trdOrder, List<TrdSupplierOrder> tsoList, List<TrdSupplierOrderDtl> tsodList, List<TrdSettle> settleLists) {

        boolean isAllComplete = tsodList.stream()
                .filter(dtl -> !Objects.equals(dtl.getDeliveryState(), DeliveryStatusEnum.CANCEL.getCode()))
                .allMatch(dtl -> Objects.equals(dtl.getDeliveryState(), DeliveryStatusEnum.COMPLETE.getCode()));
        if (isAllComplete){ // 全部商品已全部完成

            settleLists.stream().collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
                    .forEach((merchantId, settleList) -> {
//                        BigDecimal settleAmt = settleList.stream().map(TrdSettle::getSettleAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                        PayWxB2bCreateDivideReqVO reqVO =
                                new PayWxB2bCreateDivideReqVO(trdOrder.getOrderNo(), settleList.get(NumberPool.INT_ZERO).getMerchantId(), settleList.get(NumberPool.INT_ZERO).getMerchantType());
                        // 发送分润账户分账请求
                        log.info("发送订单【{}】，发送B2B微信在线分润账户分账请求:{}", trdOrder.getOrderNo(), JSON.toJSONString(reqVO));
                        CreateDivideRespVO respVO = payApi.divide(reqVO).getCheckedData();
                        if (respVO.isSuccess()) {
                            settleList.forEach(settle -> {
                                settle.setState(StatusConstants.SETTLE_STATE_2); // 更新结算状态未结算中
                            });
                        }
                        log.info("发送订单【{}】，发送B2B微信在线分润账户分账请求返回结果:{}", trdOrder.getOrderNo(), JSON.toJSONString(respVO));
                        trdSettleMapper.updateBatch(settleList);
                    });
        }
    }


    @Override
    public void orderSettleAccountInfo(List<TrdSupplierResDto> payInfoList, Map<Long, List<TrdSupplierOrderDtl>> supplierItemMap,
                                                          TrdSupplierPageVO pageVo, List<TrdSettle> settles, OrderPayInfoRespDTO respDTO) {
        //payInfoList 子单信息
        payInfoList.stream().collect(Collectors.toMap(TrdSupplierResDto::getSupplierOrderId, item -> item)).forEach((supplierOrderId, supplierResDTO) -> {
            if (supplierItemMap.containsKey(supplierOrderId)) {
                // 入驻商分账方信息
                List<String> itemList = supplierItemMap.get(supplierOrderId).stream()
                        .map(item ->
                                StringUtils.format("{}", item.getSpuName().trim())
                        ).collect(Collectors.toList());
                supplierResDTO.setItemInfo(StringUtils.join(itemList, StringPool.COMMA))
                        .setMerchantId(supplierResDTO.getSupplierId())
                        .setMerchantType(MerchantTypeEnum.SUPPLIER.getType());


                // 软件、平台、运营、业务分账方信息
                settles.stream()
                        .filter(settle -> Objects.equals(supplierResDTO.getSupplierId(), settle.getSupplierId()))
                        .collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
                        .forEach((merchantId, settleList) -> {
                            TrdSupplierResDto supplierResDto = TrdSupplierResDto.builder()
                                    //TODO 2025年7月13日11:10:52 永培： 这里subOrderNo是不是设置错了。。。。。 还是先不改别人的
                                    .subOrderNo(pageVo.getOrderNo())
                                    .subPayFee(BigDecimal.ZERO)
                                    .platform(pageVo.getStoreOrderPayPlatform())
                                    .merchantId(settleList.get(NumberPool.INT_ZERO).getMerchantId())
                                    .merchantType(settleList.get(NumberPool.INT_ZERO).getMerchantType())
                                    .build();
                            BigDecimal settleAmt = settleList.stream().map(TrdSettle::getSettleAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                            supplierResDto.setSubOrderAmt(settleAmt);
                            if (settleAmt.compareTo(BigDecimal.ZERO) > NumberPool.INT_ZERO) {
                                respDTO.getSupplierList().add(supplierResDto);
                                // 入驻商分账金额 - 分账方金额
                                supplierResDTO.setSubOrderAmt(supplierResDTO.getSubOrderAmt().subtract(settleAmt));
                            }
                        });
                respDTO.getSupplierList().add(supplierResDTO);
            }
        });
    }

}
