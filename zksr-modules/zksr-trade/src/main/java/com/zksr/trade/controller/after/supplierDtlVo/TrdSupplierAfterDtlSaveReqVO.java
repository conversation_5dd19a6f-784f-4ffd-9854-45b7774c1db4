package com.zksr.trade.controller.after.supplierDtlVo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 售后单明细对象 trd_supplier_after_dtl
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Data
@ApiModel("售后单明细 - trd_supplier_after_dtl分页 Request VO")
public class TrdSupplierAfterDtlSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 售后单明细id */
    @ApiModelProperty(value = "备注")
    private Long supplierAfterDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 售后单编号 */
    @Excel(name = "售后单编号")
    @ApiModelProperty(value = "售后单编号", required = true)
    private String afterNo;

    /** 售后单id */
    @Excel(name = "售后单id")
    @ApiModelProperty(value = "售后单id", required = true)
    private Long afterId;

    /** 售后单明细编号 */
    @Excel(name = "售后单明细编号")
    @ApiModelProperty(value = "售后单明细编号")
    private String supplierAfterDtlNo;

    /** 入驻商售后单id */
    @Excel(name = "入驻商售后单id")
    @ApiModelProperty(value = "入驻商售后单id", required = true)
    private Long supplierAfterId;

    /** 入驻商售后单编号 */
    @Excel(name = "入驻商售后单编号")
    @ApiModelProperty(value = "入驻商售后单编号", required = true)
    private String supplierAfterNo;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    @ApiModelProperty(value = "入驻商订单明细id", required = true)
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    @ApiModelProperty(value = "入驻商订单明细编号", required = true)
    private String supplierOrderDtlNo;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 0-本地配送商品 1-全国一件代发商品 */
    @Excel(name = "0-本地配送商品 1-全国一件代发商品")
    @ApiModelProperty(value = "0-本地配送商品 1-全国一件代发商品")
    private Long itemType;

    /** 上架商品id */
    @Excel(name = "上架商品id")
    @ApiModelProperty(value = "上架商品id")
    private Long itemId;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    @ApiModelProperty(value = "商品SPU id")
    private Long spuId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    @ApiModelProperty(value = "商品sku id")
    private Long skuId;

    /** 退货原因 */
    @Excel(name = "退货原因")
    @ApiModelProperty(value = "退货原因")
    private String reason;

    /** 退货数量 */
    @Excel(name = "退货数量")
    @ApiModelProperty(value = "退货数量")
    private Long returnQty;

    /** 退货价 */
    @Excel(name = "退货价")
    @ApiModelProperty(value = "退货价")
    private BigDecimal returnPrice;

    /** 退货金额 */
    @Excel(name = "退货金额")
    @ApiModelProperty(value = "退货金额")
    private BigDecimal returnAmt;

    /** 退款金额 */
    @Excel(name = "退款金额")
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmt;

    /** 退货说明 */
    @Excel(name = "退货说明")
    @ApiModelProperty(value = "退货说明")
    private String descr;

    /** 待定：售后类型(数据字典);待定：1仅退款 2退货退款 3换货 4补发 */
    @Excel(name = "待定：售后类型(数据字典);待定：1仅退款 2退货退款 3换货 4补发")
    @ApiModelProperty(value = "待定：售后类型(数据字典);待定：1仅退款 2退货退款 3换货 4补发")
    private Long afterType;

    /** 售后阶段(数据字典);1发货前退款 2发货后退款 */
    @Excel(name = "售后阶段(数据字典);1发货前退款 2发货后退款")
    @ApiModelProperty(value = "售后阶段(数据字典);1发货前退款 2发货后退款")
    private Long afterPhase;

    /** 退款类型(数据字典);1退全款 2退部分款  3不退款 */
    @Excel(name = "退款类型(数据字典);1退全款 2退部分款  3不退款")
    @ApiModelProperty(value = "退款类型(数据字典);1退全款 2退部分款  3不退款")
    private Long refundType;

    /** 审核状态(数据字典);0待审核 1同意 2拒绝 */
    @Excel(name = "审核状态(数据字典);0待审核 1同意 2拒绝")
    @ApiModelProperty(value = "审核状态(数据字典);0待审核 1同意 2拒绝")
    private Long approveState;

    /** 退货状态(数据字典);0-未退货 1-退货中 2-退货完成  3-无需退货 */
    @Excel(name = "退货状态(数据字典);0-未退货 1-退货中 2-退货完成  3-无需退货")
    @ApiModelProperty(value = "退货状态(数据字典);0-未退货 1-退货中 2-退货完成  3-无需退货")
    private Long returnState;

    /** 退款状态(数据字典);0-未退款 1-退款中 2-退款完成  3-退款失败 */
    @Excel(name = "退款状态(数据字典);0-未退款 1-退款中 2-退款完成  3-退款失败")
    @ApiModelProperty(value = "退款状态(数据字典);0-未退款 1-退款中 2-退款完成  3-退款失败")
    private Long refundState;

    /** 退款总金额;包含本次售后单和之前未取消的售后单的退款金额之和 */
    @Excel(name = "退款总金额;包含本次售后单和之前未取消的售后单的退款金额之和")
    @ApiModelProperty(value = "退款总金额;包含本次售后单和之前未取消的售后单的退款金额之和")
    private BigDecimal orderdtlRefundAmt;

    /** 退款方式（数据字典）;0-在线支付 1-储值支付 2-货到付款 */
    @Excel(name = "退款方式", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "退款方式")
    private String payway;

    /** 支付平台(数据字典);从订单表 */
    @Excel(name = "支付平台(数据字典);从订单表")
    @ApiModelProperty(value = "支付平台(数据字典);从订单表")
    private String platform;

    /** 订单支付时间;从订单表 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "订单支付时间;从订单表", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "订单支付时间;从订单表")
    private Date payTime;

    /** 订单完成时间;从订单表 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "订单完成时间;从订单表", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "订单完成时间;从订单表")
    private Date finishTime;

    /** 退货完成时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "退货完成时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "退货完成时间")
    private Date returnTime;

    /** 退款完成时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "退款完成时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "退款完成时间")
    private Date refundTime;

    /** 是否已取消 0未取消 1已取消 */
    @Excel(name = "是否已取消 0未取消 1已取消")
    @ApiModelProperty(value = "是否已取消 0未取消 1已取消")
    private Long isCancel;

    /** 来源;1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序 */
    @Excel(name = "来源;1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序")
    @ApiModelProperty(value = "来源;1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序")
    private Long source;

    /** 是否已经同步 1-是 0-否 */
    @Excel(name = "是否已经同步 1-是 0-否")
    @ApiModelProperty(value = "是否已经同步 1-是 0-否")
    private Integer syncFlag;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

}
