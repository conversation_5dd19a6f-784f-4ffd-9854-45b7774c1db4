package com.zksr.trade.controller.driver.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 司机档案对象 trd_driver
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@Data
@ApiModel("司机档案 - trd_driver Response VO")
public class TrdDriverRespVO {
    private static final long serialVersionUID = 1L;

    /** 司机id */
    @ApiModelProperty(value = "司机ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long driverId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 司机名 */
    @Excel(name = "司机名")
    @ApiModelProperty(value = "司机名")
    private String driverName;

    /** 司机手机号 */
    @Excel(name = "司机手机号")
    @ApiModelProperty(value = "司机手机号")
    private String driverPhone;

}
