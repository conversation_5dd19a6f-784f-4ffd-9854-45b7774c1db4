package com.zksr.trade.convert.order;

import com.zksr.trade.api.order.dto.TrdSupplierOrderDtlDTO;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/12/8
 * @desc
 */
@Mapper
public interface TrdSupplierOrderDtlConvert {

    public static TrdSupplierOrderDtlConvert INSTANCE = Mappers.getMapper(TrdSupplierOrderDtlConvert.class);

    TrdSupplierOrderDtlDTO convertDTO(TrdSupplierOrderDtl trdSupplierOrderDtl);
}
