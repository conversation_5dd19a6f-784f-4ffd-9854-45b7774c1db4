package com.zksr.trade.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdHdfkPayDtl;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayDtlPageReqVO;

import java.util.List;


/**
 * 货到付款付款单明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Mapper
public interface TrdHdfkPayDtlMapper extends BaseMapperX<TrdHdfkPayDtl> {
    default PageResult<TrdHdfkPayDtl> selectPage(TrdHdfkPayDtlPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdHdfkPayDtl>()
                    .eqIfPresent(TrdHdfkPayDtl::getHdfkPayDtlId, reqVO.getHdfkPayDtlId())
                    .eqIfPresent(TrdHdfkPayDtl::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdHdfkPayDtl::getPayNo, reqVO.getPayNo())
                    .eqIfPresent(TrdHdfkPayDtl::getHdfkPayId, reqVO.getHdfkPayId())
                    .eqIfPresent(TrdHdfkPayDtl::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(TrdHdfkPayDtl::getSupplierId, reqVO.getSupplierId())
                    .eqIfPresent(TrdHdfkPayDtl::getPayAmt, reqVO.getPayAmt())
                    .eqIfPresent(TrdHdfkPayDtl::getSettleAmt, reqVO.getSettleAmt())
                    .eqIfPresent(TrdHdfkPayDtl::getPayRate, reqVO.getPayRate())
                    .eqIfPresent(TrdHdfkPayDtl::getPayFee, reqVO.getPayFee())
                    .eqIfPresent(TrdHdfkPayDtl::getDivideAmt, reqVO.getDivideAmt())
                .orderByDesc(TrdHdfkPayDtl::getHdfkPayDtlId));
    }

    default List<TrdHdfkPayDtl> selectByPayId(Long hdfkPayId) {
        return selectList(new LambdaQueryWrapperX<TrdHdfkPayDtl>()
                .eqIfPresent(TrdHdfkPayDtl::getHdfkPayId, hdfkPayId)
        );
    }
}
