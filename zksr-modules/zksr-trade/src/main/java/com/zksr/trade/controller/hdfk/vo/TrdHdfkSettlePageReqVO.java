package com.zksr.trade.controller.hdfk.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 货到付款结算对象 trd_hdfk_settle
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@ApiModel("货到付款结算 - trd_hdfk_settle分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdHdfkSettlePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("入驻商订单号")
    private String supplierOrderNo;

    @ApiModelProperty("门店ID")
    private Long branchId;

    @ApiModelProperty("订单状态")
    private Integer deliveryState;

    @ApiModelProperty("处理状态, 0-未处理, 1-已处理")
    private Integer processState;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date endTime;

    @ApiModelProperty("处理开始时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date startProcessTime;

    @ApiModelProperty("处理结束时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date endProcessTime;

    @ApiModelProperty("处理人")
    private String processUser;

    @ApiModelProperty("货到付款单据号")
    private String hdfkPayNo;

    @ApiModelProperty("商品名称")
    private String spuName;

    @ApiModelProperty("货到付款单ID")
    private Long hdfkPayId;
}
