package com.zksr.trade.api.order;

import cn.hutool.core.collection.ListUtil;
import com.github.pagehelper.Page;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncAfterOrderCallDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncOrderCallDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncOrderPageReqDTO;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.enums.RefundStateEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.domain.vo.openapi.CustomApiDetail;
import com.zksr.common.core.domain.vo.openapi.CustomApiMaster;
import com.zksr.report.api.homePages.dto.HomePagesCurrentSalesDataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesOrderAfterDataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesOrderSalesDataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.system.api.supplier.vo.SupplierOrderVO;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.api.order.dto.ColonelOrderDaySettleDTO;
import com.zksr.trade.api.order.vo.ColonelOrderDaySettleVO;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.dto.*;
import com.zksr.trade.api.order.vo.*;
import com.zksr.trade.api.supplierOrder.dto.DebtOrderExportVO;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderDtlVO;
import com.zksr.trade.controller.order.vo.DcOrderPageReqVO;
import com.zksr.trade.controller.order.vo.DcSupplierOrderPageRespVO;
import com.zksr.trade.controller.order.vo.TrdOrderOperVO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.convert.order.TrdSupplierOrderDtlConvert;
import com.zksr.trade.convert.orderExpress.TrdOrderExpressConvert;
import com.zksr.trade.domain.TrdSettle;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.mapper.TrdSupplierOrderMapper;
import com.zksr.trade.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zksr.common.core.enums.PayStateEnum.*;
import static com.zksr.common.core.web.pojo.CommonResult.success;

import java.util.Map;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
@Slf4j
public class OrderApiImpl implements OrderApi{

    @Autowired
    private ITrdOrderService trdOrderServiceImpl;

    @Autowired
    private ITrdSettleService trdSettleService;

    @Autowired
    private ITrdSupplierOrderService trdSupplierOrderService;

    @Autowired
    private ITrdSupplierAfterService trdSupplierAfterService;

    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;

    @Autowired
    private ITrdSupplierOrderDtlService trdSupplierOrderDtlService;

    @Autowired
    private ITrdOrderShareService orderShareService;

    @Autowired
    private ITrdOrderExpressService expressService;

    @Autowired
    private ITrdAfterService trdAfterService;


    @Override
    public CommonResult<TrdOrderResDto> saveOrder(RemoteSaveOrderVO trdOrderSaveReqVo) {
        return success(trdOrderServiceImpl.saveOrder(trdOrderSaveReqVo));
    }

    @Override
    public CommonResult<OrderPayInfoRespDTO> getSupplierPayInfo(TrdSupplierPageVO trdSupplierPageVo) {
        return success(trdOrderServiceImpl.getSupplierPayInfo(trdSupplierPageVo));
    }

    @Override
    public CommonResult<Boolean> orderPaySuccessCallback(TrdPayOrderPageVO pageVo) {
        trdOrderServiceImpl.orderPaySuccessCallback(pageVo);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> orderRefundCallback(TrdPayOrderPageVO pageVo) {
        return success(true);
    }

    /**
    * @Description: 分页获取订单数据
    * @Author: liuxingyu
    * @Date: 2024/3/30 10:28
    */
    @Override
    public PageResult<TrdOrderRespDTO> pageOrderList(TrdOrderPageReqDTO orderPageReqVO) {
//        return trdOrderServiceImpl.pageOrderList(orderPageReqVO);
        return trdOrderServiceImpl.pageOrderListNew(orderPageReqVO);
    }

    @Override
    public CommonResult<PageResult<TrdOrderMiniHeadRespDTO>> miniPageOrderList(TrdOrderPageReqDTO orderPageReqVO) {
//        Page<TrdOrderMiniHeadRespDTO> page = PageUtils.startPage(orderPageReqVO);
////        return success(trdOrderServiceImpl.pageOrderList(orderPageReqVO));
//        return success(new PageResult<>(trdOrderServiceImpl.pageOrderList(orderPageReqVO), page.getTotal()));
        return trdOrderServiceImpl.pageOrderList(orderPageReqVO);
    }

    @Override
    public CommonResult<TrdOrderRespDTO> getOrderInfo(TrdOrderPageReqDTO orderPageReqVO) {
        return success(trdOrderServiceImpl.getOrderInfo(orderPageReqVO));
    }

    @Override
    public CommonResult<Boolean> orderCancel(String supplierOrderNo) {
        log.info("订单取消supplierOrderNo{}", supplierOrderNo);
        trdOrderServiceImpl.orderCancel(supplierOrderNo);
        return success(true);

    }

    @Override
    public CommonResult<Boolean> orderTakeDelivery(TrdOrderTakeDeliveryVO takeDeliveryVO) {
        trdOrderServiceImpl.orderTakeDeliveryJob(takeDeliveryVO);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> orderComplete(TrdOrderTakeDeliveryVO takeDeliveryVO) {
        trdOrderServiceImpl.orderComplete(takeDeliveryVO);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> orderCreateSettleTransfer(Long sysCode, Long orderId) {
        trdOrderServiceImpl.orderCreateSettleTransfer(sysCode, orderId);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> updateOrderSettleState(List<Long> settleIds) {
        trdSettleService.updateOrderSettleState(settleIds);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> updateOrderDivideSettleState(String orderNo) {
        log.info("收到订单{}分账回调！", orderNo);
        // 创建单线程的定时任务调度器
        ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);
        // // 延时 3 秒后执行任务
        executorService.schedule(() -> {
            List<Long> supplierOrderDtlIdList = trdOrderServiceImpl.getOrderAndAfterSettleIdBySheetNo(orderNo);
            if (ToolUtil.isEmpty(supplierOrderDtlIdList) || supplierOrderDtlIdList.isEmpty()) {
                throw new ServiceException(StringUtils.format("支付单号【{}】为查询出订单明细数据", orderNo));
            }

            List<TrdSettle> settles = trdSettleService.getSettleBySupplierOrderDtlId(supplierOrderDtlIdList);
            if (ToolUtil.isEmpty(settles) || settles.isEmpty()) {
                throw new ServiceException(StringUtils.format("支付单号【{}】不存在可确认结算的结算流水数据", orderNo));
            }
            // 更新结算流水 为已结算
            trdSettleService.updateOrderSettleState(settles.stream().map(TrdSettle::getSettleId).collect(Collectors.toList()));
        }, 3, TimeUnit.SECONDS);
        // 关闭调度器
        executorService.shutdown();
        return success(true);
    }

    @Override
    public CommonResult<Boolean> nationwideProductConfirmReceive(Long orderId) {
        trdOrderServiceImpl.orderTakeDelivery(orderId);
        return success(Boolean.TRUE);
    }

    @Override
    public OrderStatusVO getOrderStatus(OrderStatusReqVO reqVO) {
        OrderStatusVO orderStatusVO = trdOrderServiceImpl.getOrderStatus(reqVO);
        return orderStatusVO;
    }

    /**
     * 获取订单金额统计
     *
     * @param branchId 门店ID
     * @return
     */
    @Override
    public OrderAmountStatisticsVO getOrderAmountStatisticsVO(Long branchId) {
        return trdOrderServiceImpl.getOrderAmountStatisticsVO(branchId);
    }

    @Override
    public CommonResult<ColonelAppOrderListTotalDTO> coloneAppPageOrderList(TrdColonelAppOrderListPageReqVO orderPageReqVO) {
        ColonelAppOrderListTotalDTO result = trdOrderServiceImpl.selectMemColoneAppOrderListTotal(orderPageReqVO);

        if (ToolUtil.isNotEmpty(result) && result.getNumTotal() > 0) {
            orderPageReqVO.setPageNo((orderPageReqVO.getPageNo() - 1) * orderPageReqVO.getPageSize());
            List<TrdOrderRespDTO> list = trdOrderServiceImpl.selectMemColoneAppOrderNew(orderPageReqVO);
            result.setList(list);
        } else {
            result.setList(new ArrayList<>());
            result.setAmtTotal(BigDecimal.ZERO);
        }
        result.setTotal(result.getNumTotal());
        return success(result);
    }

    @Override
    public List<TrdColonelAppOrderDetailRespVO> getMemColonelAppOrderDetail(Long orderId){
        return trdOrderServiceImpl.getMemColoneAppOrderDetail(orderId);
    }

    @Override
    public BigDecimal getSaleAmount(TrdColonelAppOrderListPageReqVO reqVO) {
        return trdOrderServiceImpl.getSaleAmount(reqVO);
    }

    @Override
    public CustomApiMaster getCustomApiMaster(CustomApiMaster customApiMaster) {
        return trdOrderServiceImpl.getCustomApiMaster(customApiMaster);
    }

    @Override
    public List<CustomApiDetail> getCustomApiDetail(String apiNo) {
        return trdOrderServiceImpl.getCustomApiDetail(apiNo);
    }

    @Override
    public CommonResult<Boolean> orderOutbound(TrdOrderOperDTO trdOrderOperDTO) {
        TrdOrderOperVO bean = HutoolBeanUtils.toBean(trdOrderOperDTO, TrdOrderOperVO.class);
        trdOrderServiceImpl.orderOutbound(bean);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> orderTakeDelivery(TrdOrderOperDTO trdOrderOperDTO) {
        TrdOrderOperVO bean = HutoolBeanUtils.toBean(trdOrderOperDTO, TrdOrderOperVO.class);
        trdOrderServiceImpl.orderTakeDelivery(bean);
        return success(true);
    }

    @Override
    public TrdOrder getOrderByOrderId(Long orderId) {
        return trdOrderServiceImpl.getTrdOrder(orderId);
    }

    /**
    * @Description: 获取常购商品列表
    * @Author: liuxingyu
    * @Date: 2024/5/7 14:42
    */
    @Override
    public PageResult<StoreProductRespVO> getEsStoreProductList(StoreProductRequest storeProductRequest) {
        return trdOrderServiceImpl.getEsStoreProductList(storeProductRequest);
    }

    /**
     * 获取优惠券拓展统计数据
     * @param couponTemplateIdList  优惠券模版ID集合
     * @return  订单使用优惠券的订单金额, 订单实际使用优惠金额
     */
    @Override
    public CommonResult<List<CouponExtendTotalVO>> getCouponExtendTotal(List<Long> couponTemplateIdList) {
        return success(trdOrderServiceImpl.getCouponExtendTotal(couponTemplateIdList));
    }

    @Override
    public CommonResult<List<CouponCustomerOrderUseTotalDTO>> getCouponCustomerUseTotal(Long couponTemplateId, List<Long> customerIdList) {
        return success(trdOrderServiceImpl.getCouponCustomerUseTotal(couponTemplateId, customerIdList));
    }

    @Override
    public CommonResult<List<ActivityOrderTotalDTO>> getPrmActivityByActivityIds(List<Long> activityIds) {
        return success(trdOrderServiceImpl.getPrmActivityByActivityIds(activityIds));
    }

    @Override
    public TrdSupplierOrder selectOrderBySupplierOrderNo(String supplierOrderNo) {
        return trdSupplierOrderMapper.selectOrderBySupplierOrderNo(supplierOrderNo);
    }

    @Override
    public CommonResult<TrdSupplierOrder> getOrderBySupplierOrderId(Long supplierOrderId) {
        return success(trdSupplierOrderMapper.selectById(supplierOrderId));
    }

    @Override
    public CommonResult<TrdOrder> getOrderByOrderNo(String orderNo) {
        return success(trdOrderServiceImpl.getTrdOrderByOrderNo(orderNo));
    }

    @Override
    public CommonResult<List<ColonelOrderDaySettleDTO>> getColonelOrderBusinessSettle(ColonelOrderDaySettleVO reqVO) {
        return success(trdOrderServiceImpl.getColonelOrderBusinessSettle(reqVO));
    }

    @Override
    public CommonResult<List<EsBranchOrderSalesRespDTO>> getBranchOrderSales(EsBranchOrderSalesReqVO reqVO) {
        return success(trdOrderServiceImpl.getBranchOrderSales(reqVO));
    }

    @Override
    public CommonResult<PageResult<SyncOrderCallDTO>> getOrdersWithDelay(SyncOrderPageReqDTO reqVO) {
        return success(trdOrderServiceImpl.getOrdersWithDelay(reqVO));
    }
    @Override
    public CommonResult<PageResult<SyncAfterOrderCallDTO>> getAfterOrdersWithDelay(SyncOrderPageReqDTO reqVO) {
        return success(trdOrderServiceImpl.getAfterOrdersWithDelay(reqVO));
    }


    @Override
    public CommonResult<List<OrderReceiptRespDTO>> getOrderReceiptInfoBySupplierOrderNo(SyncReceiptSendDTO reqVo) {
        if (ObjectUtil.equals(reqVo.getSheetType(), SheetTypeConstants.SHS)) { // 售后
            return success(trdSupplierAfterService.getOrderReceiptInfoBySupplierAfterNo(reqVo));
        }else  if (ObjectUtil.equals(reqVo.getSheetType(), SheetTypeConstants.XSS)){ // 销售
            return success(trdSupplierOrderService.getOrderReceiptInfoBySupplierOrderNo(reqVo));
        } else {
            throw new ServiceException("单据类型{" + reqVo.getSheetType() + "}匹配失败！");
        }
    }

    @Override
    public CommonResult<List<OrderReceiptRespDTO>> getReceiptBySupplierOrderNo(SyncReceiptSendDTO reqVo) {
        //销售订单收款单
        if (ObjectUtil.equals(reqVo.getSheetType(), SheetTypeConstants.XSS)){ // 销售
            List<TrdSupplierOrderDtl> listBySupplierOrderNo = trdSupplierOrderDtlService.getListBySupplierOrderNo(reqVo.getSupplierSheetNo());
            if(ToolUtil.isEmpty(listBySupplierOrderNo)){
                throw new ServiceException("订单未查询到!");
            }
            for (TrdSupplierOrderDtl trdSupplierOrderDtl : listBySupplierOrderNo) {
                if(!PAY_ALREADY_ONLINE.getCode().equals(trdSupplierOrderDtl.getPayState()) && !PAY_ALREADY_HDFK.getCode().equals(trdSupplierOrderDtl.getPayState())){
                    throw new ServiceException("销售订单没有全部收款,无法获取收款单!");
                }
            }
            BigDecimal sumReceiptAmt = new BigDecimal(BigInteger.ZERO);
            BigDecimal sumReceiptExactAmt = new BigDecimal(BigInteger.ZERO);
            //获取销售订单收款单信息
            List<OrderReceiptRespDTO> orderAfterReceipt = trdSupplierOrderService.getOrderAfterReceiptInfoBySupplierOrderNo(reqVo);
            for (OrderReceiptRespDTO orderReceiptRespDTO : orderAfterReceipt) {
                sumReceiptAmt = sumReceiptAmt.add(orderReceiptRespDTO.getReceiptAmt());
                sumReceiptExactAmt = sumReceiptExactAmt.add(orderReceiptRespDTO.getReceiptExactAmt());
            }
            //组装 收款单
            OrderReceiptRespDTO newOrderReceiptRespDTO = new OrderReceiptRespDTO();
            newOrderReceiptRespDTO.setSupplierSheetNo("XXS");
            newOrderReceiptRespDTO.setReceiptAmt(sumReceiptAmt);
            newOrderReceiptRespDTO.setReceiptExactAmt(sumReceiptExactAmt);
            newOrderReceiptRespDTO.setSheetType("XS");  // 设置订单类型
            newOrderReceiptRespDTO.setSupplierSheetType("XXSS");  // 设置单据类型
            newOrderReceiptRespDTO.setSheetPayWay(orderAfterReceipt.get(0).getSheetPayWay());
            orderAfterReceipt.add(newOrderReceiptRespDTO);
            return success(orderAfterReceipt);
        }else if (ObjectUtil.equals(reqVo.getSheetType(), SheetTypeConstants.SHS)) {
            //所有售后单全部退完款之后才能查到收款单
            List<TrdSupplierAfterDtl> supplierAfterList = trdSupplierAfterService.getSupplierAfterListBySupplierAfterNo(reqVo.getSupplierSheetNo());
            for (TrdSupplierAfterDtl trdSupplierAfterDtl : supplierAfterList) {
                if (!RefundStateEnum.REFUND_TKWC.getState().equals(trdSupplierAfterDtl.getRefundState())) {
                    throw new ServiceException("售后单没有全部退款完成，无法获取到收款单!");
                }
            }
            return success(trdSupplierAfterService.getOrderReceiptInfoBySupplierAfterNo(reqVo));
        }else{
            throw new ServiceException("单据类型{" + reqVo.getSheetType() + "}匹配失败！");
        }
    }

    @Override
    public CommonResult<String> shareOrder(TrdOrderShareSaveReqVO saveReqVO) {
        return success(orderShareService.insertTrdOrderShare(saveReqVO));
    }

    @Override
    public CommonResult<TrdOrderShareRespVO> getShareOrder(String shareKey) {
        return success(orderShareService.getTrdOrderShare(shareKey));
    }

    @Override
    public CommonResult<TrdSupplierOrderDtlDTO> getSupplierOrderDtl(Long supplierOrderDtlId) {
        TrdSupplierOrderDtlDTO supplierOrderDtlDTO = TrdSupplierOrderDtlConvert.INSTANCE.convertDTO(trdSupplierOrderDtlService.getTrdSupplierOrderDtl(supplierOrderDtlId));
        List<TrdOrderExpress> expressList = expressService.getOrderExpressInfoByOrderDtlIds(ListUtil.toList(supplierOrderDtlId));
        supplierOrderDtlDTO.setOrderExpressResDTOList(TrdOrderExpressConvert.INSTANCE.convertList(expressList));
        return success(supplierOrderDtlDTO);
    }

    @Override
    public CommonResult<List<TrdSupplierOrderDtlDTO>> getSupplierOrderDtlBatch(List<Long> supplierOrderDtlIdList) {
        List<TrdSupplierOrderDtlDTO> orderDtlDTOS = new ArrayList<>();
        for (Long supplierOrderDtlId : supplierOrderDtlIdList) {
            TrdSupplierOrderDtlDTO supplierOrderDtlDTO = TrdSupplierOrderDtlConvert.INSTANCE.convertDTO(trdSupplierOrderDtlService.getTrdSupplierOrderDtl(supplierOrderDtlId));
            List<TrdOrderExpress> expressList = expressService.getOrderExpressInfoByOrderDtlIds(ListUtil.toList(supplierOrderDtlId));
            supplierOrderDtlDTO.setOrderExpressResDTOList(TrdOrderExpressConvert.INSTANCE.convertList(expressList));
            orderDtlDTOS.add(supplierOrderDtlDTO);
        }
        return success(orderDtlDTOS);
    }

    @Override
    public CommonResult<Long> getBranchTransitQty(BranchTransitQtyReqVO branchTransitQtyReqVO) {
        return success(trdSupplierOrderMapper.selectBranchTransitQty(branchTransitQtyReqVO));
    }

    @Override
    public CommonResult<List<HomePagesCurrentSalesDataRespDTO>> getHomePagesCurrentSalesData(HomePagesReqVO reqVO) {
        return success(trdOrderServiceImpl.getHomePagesCurrentSalesData(reqVO));
    }

    @Override
    public CommonResult<List<HomePagesOrderSalesDataRespDTO>> getHomePagesOrderSalesData(HomePagesReqVO reqVO) {
        return success(trdOrderServiceImpl.getHomePagesOrderSalesData(reqVO));
    }

    @Override
    public CommonResult<List<HomePagesOrderAfterDataRespDTO>> getHomePagesOrderAfterData(HomePagesReqVO reqVO) {
        return success(trdAfterService.getHomePagesOrderAfterData(reqVO));
    }

    @Override
    public CommonResult<List<HomePagesSalesTop10DataRespDTO>> getHomePagesSalesTop10Data(HomePagesReqVO reqVO) {
        return success(trdOrderServiceImpl.getHomePagesSalesTop10Data(reqVO));
    }

    @Override
    public CommonResult<List<SupplierOrderDtlInfoExportVO>> getSupplierOrderDtlInfoExport(DcOrderPageReqApiVO dcOrderPageReqApiVO) {
        return success(trdOrderServiceImpl.getSupplierOrderDtlInfoExport(dcOrderPageReqApiVO));
    }

    @Override
    public CommonResult<List<Long>> checkMemberOrBranchExistsOrderSaleInfo(String type,Long sysCode, List<Long> ids) {
        return success(trdOrderServiceImpl.checkMemberOrBranchExistsOrderSaleInfo(type, sysCode, ids));
    }

    @Override
    public CommonResult<List<ColonelAppBranchOrderDTO>> getColonelAppBranchOrder(Long branchId) {
        return success(trdOrderServiceImpl.getColonelAppBranchOrder(branchId));
    }

    @Override
    public CommonResult<List<ColonelAppPageOrderDTO>> getColonelAppPageOrder(Long colonelId) {
        return success(trdOrderServiceImpl.getColonelAppPageOrder(colonelId));
    }

    @Override
    public CommonResult<List<SupplierOrderDtlInfoExportVO>> getLastOrderTime(List<Long> branchIds) {
        return success(trdSupplierOrderMapper.selectLastOrderTime(branchIds));
    }
    @Override
    public CommonResult<List<DebtSupplierOrderDtlInfoExportVO>> getTrdDebtOrderExportListGroupByOrder(DcOrderPageReqApiVO dcOrderPageReqApiVO) {
        List<SupplierOrderDtlInfoExportVO> supplierOrderDtlInfoExport = trdOrderServiceImpl.getDebtSupplierOrderDtlInfoExport(HutoolBeanUtils.toBean(dcOrderPageReqApiVO, DcOrderPageReqApiVO.class));
        return success(HutoolBeanUtils.toBean(supplierOrderDtlInfoExport, DebtSupplierOrderDtlInfoExportVO.class));
    }

    @Override
    public CommonResult<Boolean> getAreaIdExistOrder(Long areaId, Long sysCode) {
        Boolean areaIdExistOrder = trdOrderServiceImpl.getAreaIdExistOrder(areaId, sysCode);
        return success(areaIdExistOrder);
    }

    @Override
    public CommonResult<Boolean> getBranchIdExistOrder(Long branchId) {
        Boolean branchExistOrder = trdOrderServiceImpl.getBranchIdExistOrder(branchId);
        return success(branchExistOrder);
    }

    public CommonResult<List<TrdSupplierOrderDtlVO>> getSupplierOrderDtlByOrderNo(String orderNo){
        return success(trdOrderServiceImpl.getSupplierOrderDtlByOrderNo(orderNo));
    }

    public CommonResult<List<TrdSupplierOrderDtlVO>> getSupplierOrderDtlByOrderNos(List<String> orderNos) {
        if(CollectionUtils.isEmpty(orderNos)){
            return success(new ArrayList<>());
        }
        List<TrdSupplierOrderDtlVO> allDtls = trdOrderServiceImpl.getSupplierOrderDtlByOrderNos(orderNos);
//        Map<String, List<TrdSupplierOrderDtlVO>> result = allDtls.stream().collect(java.util.stream.Collectors.groupingBy(TrdSupplierOrderDtlVO::getOrderNo));
        return success(allDtls);
    }

    @Override
    public CommonResult<OrderCutAmtDTO> getOrderCntAmt(OrderCutAmtDTO.CacheKey cacheKey) {
        return success(trdOrderServiceImpl.getOrderCntAmt(cacheKey));
    }

    @Override
    public CommonResult<List<TrdOrder>> getBranchLatestOrderByBranchIdList(Long sysCode) {
        return success(trdOrderServiceImpl.getBranchLatestOrderByBranchIdList(sysCode));
    }

    @Override
    public CommonResult<TrdOrder> getBranchLatestOrderByBranchId(Long branchId) {
        return success(trdOrderServiceImpl.getBranchLatestOrderByBranchId(branchId));
    }

    public CommonResult<List<SupplierOrderExportVO>> selectSupplierOrder(OrderPageReqVO param){
        Page<SupplierOrderExportVO> page = PageUtils.startPage(param);
        List<SupplierOrderExportVO> supplierOrderVOS = trdOrderServiceImpl.selectSupplierOrderExport(BeanUtils.toBean(param, DcOrderPageReqVO.class));
        return success(supplierOrderVOS);
    }

    @Override
    public CommonResult<Boolean> deleteOrder(Long orderId) {
        trdOrderServiceImpl.deleteOrder(orderId);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> sendOrderHdfkSuccess(RemoteSaveOrderVO orderVo) {
        trdOrderServiceImpl.sendOrderHdfkSuccess(orderVo);
        return success(true);
    }

    @Override
    public CommonResult<Long> getOrderLastTimeDescriptionByBranchId(Long branchId) {
        return success(trdOrderServiceImpl.getOrderLastTimeDescriptionByBranchId(branchId));
    }
}
