package com.zksr.trade.controller.order.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/01/23 16:53
 * 订单明细商品优惠数据返回实体
 */
@ApiModel("订单明细商品优惠数据返回实体")
@Data
@Builder
public class DcSupplierOrderDtlDiscountRespVO {

    @ApiModelProperty("优惠类型")
    private String discountType;

    @ApiModelProperty("优惠类型名称")
    private String discountTypeName;

    @ApiModelProperty("促销名称")
    private String discountName;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmt;
}
