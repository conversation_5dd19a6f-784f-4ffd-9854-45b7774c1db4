package com.zksr.trade.api.supplierOrder;

import com.zksr.common.core.domain.vo.openapi.OrderDetailOpenDTO;
import com.zksr.common.core.domain.vo.openapi.OrderOpenDTO;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.domain.vo.openapi.receive.*;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.trade.api.supplierOrder.dto.OrderDiscountDtlDTO;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderInvoicePageReqDTO;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderInvoiceRespDTO;
import com.zksr.trade.controller.invoice.vo.TrdSupplierOrderInvoicePageReqVO;
import com.zksr.trade.controller.invoice.vo.TrdSupplierOrderInvoiceRespVO;
import com.zksr.trade.controller.invoice.vo.TrdSupplierOrderInvoiceSaveReqVO;
import com.zksr.trade.convert.invoice.TrdSupplierOrderInvoiceConvert;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.service.ITrdOrderDiscountDtlService;
import com.zksr.trade.service.ITrdSupplierOrderDtlService;
import com.zksr.trade.service.ITrdSupplierOrderInvoiceService;
import com.zksr.trade.service.ITrdSupplierOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@ApiIgnore
@RestController // 提供 RESTful API 接口，给 Feign 调用
public class SupplierOrderApiImpl  implements SupplierOrderApi{

    @Autowired
    private ITrdSupplierOrderService trdSupplierOrderService;

    @Autowired
    private ITrdSupplierOrderDtlService trdSupplierOrderDtlService;

    @Autowired
    private ITrdOrderDiscountDtlService trdOrderDiscountDtlService;

    @Resource
    private ITrdSupplierOrderInvoiceService trdSupplierOrderInvoiceService;


    @Override
    public CommonResult<Boolean> receiveOrderOutboundReturn(Long sysCode,
                                                            Long opensourceId,
                                                            @RequestBody OrderOutboundReturnVO vo) {
        trdSupplierOrderService.receiveOrderOutboundReturn(vo);
        return success(true);
    }
    
    @Override
    public CommonResult<Boolean> receivePackage(Long sysCode,
                                                            Long opensourceId,
                                                            @RequestBody OrderPackageReturnVO vo) {
        trdSupplierOrderService.receivePackage(vo);
        return success(true);
    }

    @Override
    public CommonResult<List<OrderOpenDTO>> getSupplierOrderListByOrderNo(String orderNo) {
        return success(HutoolBeanUtils.toBean(trdSupplierOrderService.getSupplierOrderListByOrderNo(orderNo),OrderOpenDTO.class));
    }

    @Override
    public CommonResult<OrderOpenDTO> getSupplierOrderBySupplierOrderNo(String supplierOrderNo) {
        return success(HutoolBeanUtils.toBean(trdSupplierOrderService.getSupplierOrderBySupplierOrderNo(supplierOrderNo),OrderOpenDTO.class));
    }


    @Override
    public CommonResult<List<OrderDetailOpenDTO>> getSupplierOrderDtlBySupplierOrderNo(String supplierOrderNo) {
        return success(HutoolBeanUtils.toBean(trdSupplierOrderDtlService.getListBySupplierOrderNo(supplierOrderNo),OrderDetailOpenDTO.class));
    }

    @Override
    public CommonResult<Boolean> updateByPushStatus(String supplierOrderNo, Integer pushStatus) {
        trdSupplierOrderService.updateByPushStatus(supplierOrderNo, pushStatus);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> syncOrderResponseSuccess(SyncDataDTO data) {
        trdSupplierOrderService.syncOrderResponseSuccess(data);
        return success(true);
    }

    @Override
    public CommonResult<List<OrderDetailOpenDTO>> getSupplierOrderDtlListByIds(List<Long> ids) {
        return success(HutoolBeanUtils.toBean(trdSupplierOrderDtlService.getListByIds(ids),OrderDetailOpenDTO.class));
    }

    @Override
    public CommonResult<Boolean> orderReceiveCallback(Long sysCode, Long opensourceId, OrderReceiveCallbackVO vo) {
        trdSupplierOrderService.orderReceiveCallback(vo);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> orderCancelReceiveCallback(Long sysCode, Long opensourceId, OrderCancelReceiveCallbackVO vo) {
        trdSupplierOrderService.orderCancelReceiveCallback(vo);
        return success(true);
    }

    @Override
    public CommonResult<List<OrderDiscountDtlDTO>> getOrderDiscountDtlListBySupplierOrderId(Long supplierOrderId) {
        return success(trdOrderDiscountDtlService.getListBySupplierOrderId(supplierOrderId));
    }

    @Override
    public CommonResult<Boolean> orderCancel(Long sysCode, Long opensourceId, OrderCancelVO vo) {
        trdSupplierOrderService.orderCancel(vo);
        return success(true);
    }

    @Override
    public CommonResult<List<Long>> deleteSpuCheckSupplierOrderDtl(Long[] spuIds) {
        return success(trdSupplierOrderService.deleteSpuCheckSupplierOrderDtl(spuIds));
    }

    @Override
    public CommonResult<List<OrderDetailOpenDTO> > getCategoryExistOrder(Long categoryId, Long sysCode) {
        return success(BeanUtils.toBean(trdSupplierOrderDtlService.getCategoryExistOrder(categoryId,sysCode),OrderDetailOpenDTO.class));
    }

    @Override
    public CommonResult<TrdSupplierOrderInvoiceRespDTO> getSupplierOrderInvoice(TrdSupplierOrderInvoicePageReqDTO reqDTO) {
        TrdSupplierOrderInvoicePageReqVO req = TrdSupplierOrderInvoiceConvert.INSTANCE.convert2TrdSupplierOrderInvoicePageReqVO(reqDTO);
        TrdSupplierOrderInvoiceRespVO resp = trdSupplierOrderInvoiceService.getSupplierOrderInvoice(req);
        return success(TrdSupplierOrderInvoiceConvert.INSTANCE.convert2TrdSupplierOrderInvoiceRespDTO(resp));
    }

    @Override
    public CommonResult<Boolean> receiveOrderInvoice(SupplierOrderInvoiceOpenDTO reqDTO) {
        TrdSupplierOrderInvoiceSaveReqVO reqVO = TrdSupplierOrderInvoiceConvert.INSTANCE.convert2TrdSupplierOrderInvoiceSaveReqVO(reqDTO);
        trdSupplierOrderInvoiceService.receiveOrderInvoice(reqVO);
        return success(Boolean.TRUE);

    }

}
