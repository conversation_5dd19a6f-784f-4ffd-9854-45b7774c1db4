package com.zksr.trade.service.impl.price;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.domain.TrdSupplierAfterSettle;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.mapper.TrdOrderMapper;
import com.zksr.trade.mapper.TrdSupplierAfterSettleMapper;
import com.zksr.trade.mapper.TrdSupplierOrderDtlMapper;
import com.zksr.trade.mapper.TrdSupplierOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 新余额退款计算服务
 * @Date: 2025/07/12
 */
@Service
@Slf4j
public class BalanceRefundCalculationService {

    @Autowired
    private TrdOrderMapper trdOrderMapper;

    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;

    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;

    @Autowired
    private TrdSupplierAfterSettleMapper trdSupplierAfterSettleMapper;

    /**
     * 计算售后单的余额退款金额
     * @param after 售后单
     */
    public void calculateBalanceRefundAmount(TrdAfter after, List<TrdSupplierAfterSettle> afterSettleList) {
        log.info("开始计算售后单余额退款金额, afterNo: {}", after.getAfterNo());

        // 获取原订单信息
        TrdOrder originalOrder = trdOrderMapper.selectById(after.getOrderId());
        if (originalOrder == null) {
            log.warn("未找到原订单信息, orderId: {}", after.getOrderId());
            return;
        }

        // 获取原订单的子单信息
        List<TrdSupplierOrder> supplierOrders = trdSupplierOrderMapper.selectListByOrderId(after.getOrderId());
        Map<String, TrdSupplierOrder> supplierOrderMap = supplierOrders.stream()
                .collect(Collectors.toMap(TrdSupplierOrder::getSupplierOrderNo, order -> order));

        // 获取原订单的子单明细信息
        List<TrdSupplierOrderDtl> supplierOrderDtls = trdSupplierOrderDtlMapper.selectListByOrderId(after.getOrderId());
        Map<Long, TrdSupplierOrderDtl> supplierOrderDtlMap = supplierOrderDtls.stream()
                .collect(Collectors.toMap(TrdSupplierOrderDtl::getSupplierOrderDtlId, dtl -> dtl));
        // 赋值
        afterSettleList.forEach(t -> {
            TrdSupplierOrder supplierOrder = findSupplierOrderByAfterSettle(t, supplierOrderMap, supplierOrderDtlMap);
            if (Objects.nonNull(supplierOrder)) {
                t.setSupplierOrderNo(supplierOrder.getSupplierOrderNo());
            }
        });
        Map<String, List<TrdSupplierAfterSettle>> groupMap = afterSettleList.stream().filter(t -> StringUtils.isNotBlank(t.getSupplierOrderNo())).collect(Collectors.groupingBy(TrdSupplierAfterSettle::getSupplierOrderNo));
        for (Map.Entry<String, List<TrdSupplierAfterSettle>> entry : groupMap.entrySet()) {
            String supplierOrderNo = entry.getKey();
            TrdSupplierOrder supplierOrder = supplierOrderMap.get(supplierOrderNo);
            List<TrdSupplierAfterSettle> settleList = entry.getValue();
            // 入驻商订单余额支付金额
            BigDecimal subPayBalanceAmt = supplierOrder.getSubPayBalanceAmt();
            if (Objects.isNull(subPayBalanceAmt) || subPayBalanceAmt.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            for (int i = 0; i < settleList.size(); i++) {
                TrdSupplierAfterSettle afterSettle = settleList.get(i);
                BigDecimal balanceRefundAmt;
                // 按比例分配,计算该明细的余额退款金额
                balanceRefundAmt = afterSettle.getRefundAmt().divide(supplierOrder.getTotalSubPayAmt(), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP)
                            .multiply(supplierOrder.getSubPayBalanceAmt()).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                subPayBalanceAmt = subPayBalanceAmt.subtract(balanceRefundAmt);
                afterSettle.setRefundBalanceAmt(balanceRefundAmt);
                afterSettle.setRefundAmt(afterSettle.getRefundAmt().subtract(balanceRefundAmt));
            }
        }
    }

    /**
     * 根据售后结算信息查找对应的子单
     */
    private TrdSupplierOrder findSupplierOrderByAfterSettle(TrdSupplierAfterSettle afterSettle, 
                                                            Map<String, TrdSupplierOrder> supplierOrderMap,
                                                            Map<Long, TrdSupplierOrderDtl> supplierOrderDtlMap) {
        return Optional.ofNullable(supplierOrderDtlMap.get(afterSettle.getSupplierOrderDtlId()))
                .map(TrdSupplierOrderDtl::getSupplierOrderNo)
                .map(supplierOrderMap::get)
                .orElse(null);
    }


}
