package com.zksr.trade.controller.hdfk.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;


/**
 * 货到付款结算对象 trd_hdfk_settle
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@ApiModel("货到付款结算 - trd_hdfk_settle Response VO")
public class TrdHdfkSettleSkuItemRespVO {

    @ApiModelProperty("入驻商订单商品ID")
    @Excel(name = "入驻商订单商品ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierOrderDtlId;

    @ApiModelProperty("门店ID")
    @Excel(name = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty("入驻商ID")
    @Excel(name = "入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty("门店名称")
    @Excel(name = "门店名称")
    private String branchName;

    @ApiModelProperty("入驻商名称")
    @Excel(name = "入驻商名称")
    private String supplierName;

    @ApiModelProperty("门店联系人")
    @Excel(name = "门店联系人")
    private String branchContactName;

    @ApiModelProperty("门店地址")
    @Excel(name = "门店地址")
    private String branchAddr;

    @ApiModelProperty("订单状态 0-待配货,3-待发货,4-待收货,5-已收货,6-已完成,7-备货中,40-待装车")
    @Excel(name = "订单状态 0-待配货,3-待发货,4-待收货,5-已收货,6-已完成,7-备货中,40-待装车")
    private Integer deliveryState;

    @ApiModelProperty("订单号")
    @Excel(name = "订单号")
    private String orderNo;

    @ApiModelProperty("入驻商订单号")
    @Excel(name = "入驻商订单号")
    private String supplierOrderNo;

    @ApiModelProperty("处理人")
    @Excel(name = "处理人")
    private String processUser;

    @ApiModelProperty("订单创建时间")
    @Excel(name = "订单创建时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date createTime;

    @ApiModelProperty("处理时间")
    @Excel(name = "处理时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date processTime;

    @ApiModelProperty("1-已支付,2-已付款取消,3-未支付")
    @Excel(name = "1-已支付,2-已付款取消,3-未支付")
    private Integer payState;

    @ApiModelProperty("订单金额")
    @Excel(name = "订单金额")
    private BigDecimal totalAmt;

    @ApiModelProperty("结算金额")
    @Excel(name = "结算金额")
    private BigDecimal settleAmt;

    @ApiModelProperty("spu名称")
    @Excel(name = "spu名称")
    private String spuName;

    @ApiModelProperty("规格名称")
    @Excel(name = "规格名称")
    private String specName;

    @ApiModelProperty("规格名称")
    @Excel(name = "规格名称")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    @ApiModelProperty("单位名称")
    @Excel(name = "单位名称")
    private String unitName;

    @ApiModelProperty("购买具体单位字典key")
    @Excel(name = "购买具体单位字典key")
    private Integer orderUnit;

    @ApiModelProperty("1-小单位, 2-中单位, 3-大单位")
    @Excel(name = "1-小单位, 2-中单位, 3-大单位")
    private String orderUnitType;

    @ApiModelProperty("货到付款单据号")
    @Excel(name = "货到付款单据号")
    private String hdfkPayNo;

    @ApiModelProperty("货到付款单ID")
    @Excel(name = "货到付款单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long hdfkPayId;
}
