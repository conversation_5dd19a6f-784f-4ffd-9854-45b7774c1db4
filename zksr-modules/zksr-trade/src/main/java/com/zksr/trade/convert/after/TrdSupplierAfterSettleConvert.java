package com.zksr.trade.convert.after;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdSupplierAfterSettle;
import com.zksr.trade.controller.after.settleVo.TrdSupplierAfterSettleRespVO;
import com.zksr.trade.controller.after.settleVo.TrdSupplierAfterSettleSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 售后结算信息 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-20
*/
@Mapper
public interface TrdSupplierAfterSettleConvert {

    TrdSupplierAfterSettleConvert INSTANCE = Mappers.getMapper(TrdSupplierAfterSettleConvert.class);

    TrdSupplierAfterSettleRespVO convert(TrdSupplierAfterSettle trdSupplierAfterSettle);

    TrdSupplierAfterSettle convert(TrdSupplierAfterSettleSaveReqVO trdSupplierAfterSettleSaveReq);

    PageResult<TrdSupplierAfterSettleRespVO> convertPage(PageResult<TrdSupplierAfterSettle> trdSupplierAfterSettlePage);
}