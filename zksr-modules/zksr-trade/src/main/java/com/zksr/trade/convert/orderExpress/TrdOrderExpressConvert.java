package com.zksr.trade.convert.orderExpress;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.express.dto.OrderExpressResDTO;
import com.zksr.trade.domain.TrdExpressImportDtl;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressRespVO;
import com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 订单快递 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-17
*/
@Mapper
public interface TrdOrderExpressConvert {

    TrdOrderExpressConvert INSTANCE = Mappers.getMapper(TrdOrderExpressConvert.class);

    TrdOrderExpressRespVO convert(TrdOrderExpress trdOrderExpress);

    TrdOrderExpress convert(TrdOrderExpressSaveReqVO trdOrderExpressSaveReq);

    PageResult<TrdOrderExpressRespVO> convertPage(PageResult<TrdOrderExpress> trdOrderExpressPage);

    TrdOrderExpress convert(TrdExpressImportDtl trdExpressImportDtl, String receiveMan, String receivePhone, String address);

    List<OrderExpressResDTO> convertList(List<TrdOrderExpress> trdOrderExpress);
}
