package com.zksr.trade.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdAfterLogMapper;
import com.zksr.trade.convert.after.TrdAfterLogConvert;
import com.zksr.trade.domain.TrdAfterLog;
import com.zksr.trade.controller.after.logVo.TrdAfterLogPageReqVO;
import com.zksr.trade.controller.after.logVo.TrdAfterLogSaveReqVO;
import com.zksr.trade.service.ITrdAfterLogService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 售后日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Service
public class TrdAfterLogServiceImpl implements ITrdAfterLogService {
    @Autowired
    private TrdAfterLogMapper trdAfterLogMapper;

    /**
     * 新增售后日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdAfterLog(TrdAfterLogSaveReqVO createReqVO) {
        // 插入
        TrdAfterLog trdAfterLog = TrdAfterLogConvert.INSTANCE.convert(createReqVO);
        trdAfterLogMapper.insert(trdAfterLog);
        // 返回
        return trdAfterLog.getAfterLogId();
    }

    /**
     * 修改售后日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdAfterLog(TrdAfterLogSaveReqVO updateReqVO) {
        trdAfterLogMapper.updateById(TrdAfterLogConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除售后日志
     *
     * @param afterLogId 售后日志
     */
    @Override
    public void deleteTrdAfterLog(Long afterLogId) {
        // 删除
        trdAfterLogMapper.deleteById(afterLogId);
    }

    /**
     * 批量删除售后日志
     *
     * @param afterLogIds 需要删除的售后日志主键
     * @return 结果
     */
    @Override
    public void deleteTrdAfterLogByAfterLogIds(Long[] afterLogIds) {
        for(Long afterLogId : afterLogIds){
            this.deleteTrdAfterLog(afterLogId);
        }
    }

    /**
     * 获得售后日志
     *
     * @param afterLogId 售后日志
     * @return 售后日志
     */
    @Override
    public TrdAfterLog getTrdAfterLog(Long afterLogId) {
        return trdAfterLogMapper.selectById(afterLogId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdAfterLog> getTrdAfterLogPage(TrdAfterLogPageReqVO pageReqVO) {
        return trdAfterLogMapper.selectPage(pageReqVO);
    }

    private void validateTrdAfterLogExists(Long afterLogId) {
        if (trdAfterLogMapper.selectById(afterLogId) == null) {
            throw exception(TRD_AFTER_LOG_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 售后日志 TODO 补充编号 ==========
    // ErrorCode TRD_AFTER_LOG_NOT_EXISTS = new ErrorCode(TODO 补充编号, "售后日志不存在");


}
