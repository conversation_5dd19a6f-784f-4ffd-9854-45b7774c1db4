package com.zksr.trade.controller.orderSupplier.vo;

import java.math.BigDecimal;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 入驻商订单对象 trd_supplier_order
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("入驻商订单 - trd_supplier_order分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class TrdSupplierOrderPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 入驻商订单id */
    @ApiModelProperty(value = "入驻商订单id")
    private Long supplierOrderId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    /** 订单id */
    @Excel(name = "订单id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 配送费 */
    @Excel(name = "配送费")
    @ApiModelProperty(value = "配送费")
    private BigDecimal transAmt;


    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 删除状态(0:正常，2：删除) */
    @ApiModelProperty(value = "备注")
    private Integer delFlag;

    public TrdSupplierOrderPageReqVO(String orderNo) {
        this.orderNo = orderNo;
    }
}
