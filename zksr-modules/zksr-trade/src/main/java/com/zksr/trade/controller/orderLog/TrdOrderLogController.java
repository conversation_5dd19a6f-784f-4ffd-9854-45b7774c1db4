package com.zksr.trade.controller.orderLog;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdOrderLog;
import com.zksr.trade.service.ITrdOrderLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.orderLog.vo.TrdOrderLogPageReqVO;
import com.zksr.trade.controller.orderLog.vo.TrdOrderLogSaveReqVO;
import com.zksr.trade.controller.orderLog.vo.TrdOrderLogRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 订单日志Controller
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@Api(tags = "管理后台 - 订单日志接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/log")
public class TrdOrderLogController {
    @Autowired
    private ITrdOrderLogService trdOrderLogService;

    /**
     * 新增订单日志
     */
    @ApiOperation(value = "新增订单日志", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "订单日志", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdOrderLogSaveReqVO createReqVO) {
        return success(trdOrderLogService.insertTrdOrderLog(createReqVO));
    }

    /**
     * 修改订单日志
     */
    @ApiOperation(value = "修改订单日志", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "订单日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdOrderLogSaveReqVO updateReqVO) {
            trdOrderLogService.updateTrdOrderLog(updateReqVO);
        return success(true);
    }

    /**
     * 删除订单日志
     */
    @ApiOperation(value = "删除订单日志", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "订单日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sysCodes}")
    public CommonResult<Boolean> remove(@PathVariable Long[] sysCodes) {
        trdOrderLogService.deleteTrdOrderLogBySysCodes(sysCodes);
        return success(true);
    }

    /**
     * 获取订单日志详细信息
     */
    @ApiOperation(value = "获得订单日志详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{sysCode}")
    public CommonResult<TrdOrderLogRespVO> getInfo(@PathVariable("sysCode") Long sysCode) {
        TrdOrderLog trdOrderLog = trdOrderLogService.getTrdOrderLog(sysCode);
        return success(HutoolBeanUtils.toBean(trdOrderLog, TrdOrderLogRespVO.class));
    }

    /**
     * 分页查询订单日志
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得订单日志分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdOrderLogRespVO>> getPage(@Valid TrdOrderLogPageReqVO pageReqVO) {
        PageResult<TrdOrderLog> pageResult = trdOrderLogService.getTrdOrderLogPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, TrdOrderLogRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:log:add";
        /** 编辑 */
        public static final String EDIT = "trade:log:edit";
        /** 删除 */
        public static final String DELETE = "trade:log:remove";
        /** 列表 */
        public static final String LIST = "trade:log:list";
        /** 查询 */
        public static final String GET = "trade:log:query";
        /** 停用 */
        public static final String DISABLE = "trade:log:disable";
        /** 启用 */
        public static final String ENABLE = "trade:log:enable";
    }
}
