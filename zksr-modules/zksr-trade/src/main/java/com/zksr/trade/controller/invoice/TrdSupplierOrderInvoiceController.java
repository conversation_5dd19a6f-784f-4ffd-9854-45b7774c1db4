package com.zksr.trade.controller.invoice;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdSupplierOrderInvoice;
import com.zksr.trade.service.ITrdSupplierOrderInvoiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.invoice.vo.TrdSupplierOrderInvoicePageReqVO;
import com.zksr.trade.controller.invoice.vo.TrdSupplierOrderInvoiceSaveReqVO;
import com.zksr.trade.controller.invoice.vo.TrdSupplierOrderInvoiceRespVO;
import com.zksr.trade.convert.invoice.TrdSupplierOrderInvoiceConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 用户发票Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Api(tags = "管理后台 - 用户发票接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/invoice")
public class TrdSupplierOrderInvoiceController {
    @Autowired
    private ITrdSupplierOrderInvoiceService trdSupplierOrderInvoiceService;

    /**
     * 新增用户发票
     */
    @ApiOperation(value = "新增用户发票", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "用户发票", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdSupplierOrderInvoiceSaveReqVO createReqVO) {
        return success(trdSupplierOrderInvoiceService.insertTrdSupplierOrderInvoice(createReqVO));
    }

    /**
     * 修改用户发票
     */
    @ApiOperation(value = "修改用户发票", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "用户发票", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdSupplierOrderInvoiceSaveReqVO updateReqVO) {
            trdSupplierOrderInvoiceService.updateTrdSupplierOrderInvoice(updateReqVO);
        return success(true);
    }

    /**
     * 删除用户发票
     */
    @ApiOperation(value = "删除用户发票", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "用户发票", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public CommonResult<Boolean> remove(@PathVariable Long[] ids) {
        trdSupplierOrderInvoiceService.deleteTrdSupplierOrderInvoiceByIds(ids);
        return success(true);
    }

    /**
     * 获取用户发票详细信息
     */
    @ApiOperation(value = "获得用户发票详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{id}")
    public CommonResult<TrdSupplierOrderInvoiceRespVO> getInfo(@PathVariable("id") Long id) {
        TrdSupplierOrderInvoice trdSupplierOrderInvoice = trdSupplierOrderInvoiceService.getTrdSupplierOrderInvoice(id);
        return success(TrdSupplierOrderInvoiceConvert.INSTANCE.convert(trdSupplierOrderInvoice));
    }

    /**
     * 分页查询用户发票
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得用户发票分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdSupplierOrderInvoiceRespVO>> getPage(@Valid TrdSupplierOrderInvoicePageReqVO pageReqVO) {
        PageResult<TrdSupplierOrderInvoice> pageResult = trdSupplierOrderInvoiceService.getTrdSupplierOrderInvoicePage(pageReqVO);
        return success(TrdSupplierOrderInvoiceConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:invoice:add";
        /** 编辑 */
        public static final String EDIT = "trade:invoice:edit";
        /** 删除 */
        public static final String DELETE = "trade:invoice:remove";
        /** 列表 */
        public static final String LIST = "trade:invoice:list";
        /** 查询 */
        public static final String GET = "trade:invoice:query";
        /** 停用 */
        public static final String DISABLE = "trade:invoice:disable";
        /** 启用 */
        public static final String ENABLE = "trade:invoice:enable";
    }
}
