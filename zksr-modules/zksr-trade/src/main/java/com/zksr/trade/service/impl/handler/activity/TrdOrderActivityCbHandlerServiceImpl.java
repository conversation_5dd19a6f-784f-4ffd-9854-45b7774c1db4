package com.zksr.trade.service.impl.handler.activity;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.combine.SpuCombineDtlDTO;
import com.zksr.promotion.api.activity.ActivityApi;
import com.zksr.promotion.api.activity.dto.CbRuleDTO;
import com.zksr.promotion.api.activity.dto.PrmActivityDTO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.domain.TrdAfterDiscountDtl;
import com.zksr.trade.domain.TrdOrderDiscountDtl;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.activity.ITrdOrderActivityHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *  订单优惠活动处理器 ———— 组合促销
 */
@Service
@Slf4j
public class TrdOrderActivityCbHandlerServiceImpl implements ITrdOrderActivityHandlerService {
    @Resource
    private ActivityApi activityApi;
    @Autowired
    private TrdCacheService trdCacheService;
    @Autowired
    private RedisActivityService redisActivityService;
    @Autowired
    private RedisService redisService;

    @Override
    public void orderCancelReturnActivity(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {
        // 返还组合商品活动库存
        toddList.stream()
                .filter(orderDiscount -> Objects.equals(TrdDiscountTypeEnum.CB.getType(), orderDiscount.getDiscountType()))
                .collect(Collectors.groupingBy(discount -> StringUtils.format("{}_{}", discount.getDiscountId(), discount.getDiscountRuleId())))
                .forEach((key, value) -> {
                    TrdOrderDiscountDtl  discountDtl = value.get(NumberPool.INT_ZERO);
                    // 获取组合商品活动信息
                    PrmActivityDTO activity = trdCacheService.getActivityDTO(discountDtl.getDiscountId());

                    // 获取当前组合促销活动规则
                    List<CbRuleDTO> cbRuleDTOList = activityApi.getActivityCbRule(discountDtl.getDiscountId()).getCheckedData()
                            .stream().filter(item -> Objects.equals(item.getCbRuleId(), discountDtl.getDiscountRuleId()))
                            .collect(Collectors.toList());

                    if (ToolUtil.isNotEmpty(cbRuleDTOList) && !cbRuleDTOList.isEmpty()) {

                        // 获取当前组合促销活动规则关联的商品信息
                        List<TrdSupplierOrderDtl> supplierOrderDtlList = value.stream().map(dtl -> {
                            return tsodMap.get(dtl.getSupplierOrderDtlId());
                        }).filter(Objects::nonNull).collect(Collectors.toList());


                        SpuCombineDTO spuCombineDTO = trdCacheService.getSpuCombineDTO(cbRuleDTOList.get(NumberPool.INT_ZERO).getSpuCombineId());
                        Map<String, SpuCombineDtlDTO> stringListMap =  CollectionUtils.convertMap(spuCombineDTO.getCombineDtls(), combineDtl -> StringUtils.format("{}_{}", combineDtl.getSkuId(), combineDtl.getSkuUnitType()));

                        long cancelQty = supplierOrderDtlList.stream().map(tsod -> {
                            String mapKey = StringUtils.format("{}_{}", tsod.getSkuId(), tsod.getOrderUnitType());
                            if (!stringListMap.containsKey(mapKey)) {
                                return NumberPool.LONG_ZERO;
                            }
                            // 订单最小单位数量 / 订单转换数量 / 组合商品商品单位数量
                            return  tsod.getTotalNum().divide(tsod.getOrderUnitSize(), StatusConstants.PRICE_RESERVE_3, RoundingMode.DOWN)
                                    .divide(BigDecimal.valueOf(stringListMap.get(mapKey).getQty()), StatusConstants.PRICE_RESERVE_3, RoundingMode.DOWN).longValue();
                        }).min(Long::compare).orElse(NumberPool.LONG_ZERO);

                        // 返回组合商品活动库存
                        redisActivityService.setCbSaleNum(discountDtl.getDiscountId(), discountDtl.getDiscountRuleId(), BigDecimal.valueOf(cancelQty).multiply(NumberPool.BIGDECIMAL_GROUND), null, null);

                        if (Objects.nonNull(activity.getTimesRule())) {
                            // 删除 标记活动已经达到限制 key
                            redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(discountDtl.getDiscountId(), tor.getBranchId()));
                        }
                    }
                });



    }


    @Override
    public void afterCancelReturnActivity(TrdOrder tor, List<TrdSupplierOrderDtl> tsodList, List<TrdOrderDiscountDtl> toddList,
                                          TrdAfter tar, List<TrdSupplierAfterDtl> tsadList, List<TrdAfterDiscountDtl> taddList) {

        taddList.stream()
                .filter(tadd -> Objects.equals(TrdDiscountTypeEnum.CB.getType(), tadd.getDiscountType()))
                .collect(Collectors.groupingBy(tadd -> StringUtils.format("{}_{}", tadd.getDiscountId(), tadd.getDiscountRuleId())))
                .forEach((key, value) -> {
                    // 活动Id
                    Long discountId = value.get(NumberPool.INT_ZERO).getDiscountId();

                    // 活动规则Id
                    Long discountRuleId = value.get(NumberPool.INT_ZERO).getDiscountRuleId();

                    // 获取组合商品活动信息
                    PrmActivityDTO activity = trdCacheService.getActivityDTO(discountId);


                    // 获取活动规则数据
                    List<CbRuleDTO> cbRuleDTOList = activityApi.getActivityCbRule(discountId).getCheckedData()
                            .stream().filter(item -> Objects.equals(item.getCbRuleId(), discountRuleId))
                            .collect(Collectors.toList());

                    if (ToolUtil.isNotEmpty(cbRuleDTOList)) {
                        // 组合商品ID
                        Long spuCombineId = cbRuleDTOList.get(NumberPool.INT_ZERO).getSpuCombineId();
                        // 获取组合商品数据
                        SpuCombineDTO spuCombineDTO = trdCacheService.getSpuCombineDTO(spuCombineId);
                        if (ToolUtil.isNotEmpty(spuCombineDTO)) {
                            // 组合商品数据转换map
                            Map<String, SpuCombineDtlDTO> stringListMap = CollectionUtils.convertMap(spuCombineDTO.getCombineDtls(), combineDtl -> StringUtils.format("{}_{}", combineDtl.getSkuId(), combineDtl.getSkuUnitType()));

                            // 获取当前售后商品所对应的促销单据 对应的所有订单明细数据
                            List<Long> toddDtlIds = toddList.stream()
                                    .filter(todd -> Objects.equals(todd.getDiscountId(), discountId) && Objects.equals(todd.getDiscountRuleId(), discountRuleId))
                                    .map(TrdOrderDiscountDtl::getSupplierOrderDtlId).collect(Collectors.toList());


                            // 过滤促销数据对应的商品数据
                            List<TrdSupplierOrderDtl> supplierOrderDtlList = tsodList.stream().filter(tsod -> toddDtlIds.contains(tsod.getSupplierOrderDtlId())).collect(Collectors.toList());


                            // 过滤出售后商品最小单位数量 可以返还促销的最小单位数量
                            BigDecimal cancelQty = supplierOrderDtlList.stream().map(tsod -> {
                                String mapKey = StringUtils.format("{}_{}", tsod.getSkuId(), tsod.getOrderUnitType());
                                if (!stringListMap.containsKey(mapKey)) {
                                    return BigDecimal.ZERO;
                                }
                                return tsod.getCancelQty().divide(tsod.getOrderUnitSize(), 0, RoundingMode.DOWN).divide(BigDecimal.valueOf(stringListMap.get(mapKey).getQty()), 0, RoundingMode.DOWN);
                            }).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);

                            if (cancelQty.compareTo(BigDecimal.ZERO) > 0) {
                                redisActivityService.setCbSaleNum(discountId, discountRuleId, cancelQty.multiply(NumberPool.BIGDECIMAL_GROUND), null, null);
                            }

                            // 获取活动参与商品列表是否已全部售后
                            boolean boo = supplierOrderDtlList.stream().allMatch(tsod -> tsod.getTotalNum().compareTo(tsod.getCancelQty()) == 0);

                            if (Objects.nonNull(activity.getTimesRule()) && boo) {
                                // 删除 标记活动已经达到限制 key
                                redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(discountId, tor.getBranchId()));
                            }
                        }
                    }
                });




    }


    @Override
    public void restoreCancelledOrderDiscounts(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {
        // 恢复已取消订单的组合商品活动库存
        toddList.stream()
                .filter(orderDiscount -> Objects.equals(TrdDiscountTypeEnum.CB.getType(), orderDiscount.getDiscountType()))
                .collect(Collectors.groupingBy(discount -> StringUtils.format("{}_{}", discount.getDiscountId(), discount.getDiscountRuleId())))
                .forEach((key, value) -> {
                    TrdOrderDiscountDtl  discountDtl = value.get(NumberPool.INT_ZERO);
                    // 获取组合商品活动信息
                    PrmActivityDTO activity = trdCacheService.getActivityDTO(discountDtl.getDiscountId());

                    // 获取当前组合促销活动规则
                    List<CbRuleDTO> cbRuleDTOList = activityApi.getActivityCbRule(discountDtl.getDiscountId()).getCheckedData()
                            .stream().filter(item -> Objects.equals(item.getCbRuleId(), discountDtl.getDiscountRuleId()))
                            .collect(Collectors.toList());

                    if (ToolUtil.isNotEmpty(cbRuleDTOList) && !cbRuleDTOList.isEmpty()) {

                        // 获取当前组合促销活动规则关联的商品信息
                        List<TrdSupplierOrderDtl> supplierOrderDtlList = value.stream().map(dtl -> {
                            return tsodMap.get(dtl.getSupplierOrderDtlId());
                        }).collect(Collectors.toList());


                        SpuCombineDTO spuCombineDTO = trdCacheService.getSpuCombineDTO(cbRuleDTOList.get(NumberPool.INT_ZERO).getSpuCombineId());
                        Map<String, SpuCombineDtlDTO> stringListMap =  CollectionUtils.convertMap(spuCombineDTO.getCombineDtls(), combineDtl -> StringUtils.format("{}_{}", combineDtl.getSkuId(), combineDtl.getSkuUnitType()));

                        long cancelQty = supplierOrderDtlList.stream().map(tsod -> {
                            String mapKey = StringUtils.format("{}_{}", tsod.getSkuId(), tsod.getOrderUnitType());
                            if (!stringListMap.containsKey(mapKey)) {
                                return NumberPool.LONG_ZERO;
                            }
                            // 订单最小单位数量 / 订单转换数量 / 组合商品商品单位数量
                            return  tsod.getTotalNum().divide(tsod.getOrderUnitSize(), StatusConstants.PRICE_RESERVE_3, RoundingMode.DOWN)
                                    .divide(BigDecimal.valueOf(stringListMap.get(mapKey).getQty()), StatusConstants.PRICE_RESERVE_3, RoundingMode.DOWN).longValue();
                        }).min(Long::compare).orElse(NumberPool.LONG_ZERO);

                        // 返回组合商品活动库存
                        redisActivityService.setCbSaleNum(discountDtl.getDiscountId(), discountDtl.getDiscountRuleId(), BigDecimal.valueOf(cancelQty), null, null);

                        // 参与活动次数限制规则
                        if (Objects.nonNull(activity.getTimesRule())) {
                            Long ruleTime = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());; //默认时间到活动截止时间到期  仅一次
                            if (activity.getTimesRule() == NumberPool.INT_ZERO) { // 每日一次
                                ruleTime = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate());
                            }
                            // 标记活动已经达到限制
                            redisService.setCacheObject(RedisActivityConstants.getTimesRuleKey(activity.getActivityId(), tor.getBranchId()), StringPool.ONE, ruleTime, TimeUnit.SECONDS);
                        }
                    }
                });
    }
}
