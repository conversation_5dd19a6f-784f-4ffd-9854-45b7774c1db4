package com.zksr.trade.controller.trdPackage.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 包裹对象 trd_package
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@ApiModel("包裹 - trd_package Response VO")
public class TrdPackageRespVO {
    private static final long serialVersionUID = 1L;

    /** 包裹ID */
    @ApiModelProperty(value = "幂等键(防止重复提交)")
    private Long packageId;

    /** 平台商ID */
    @Excel(name = "平台商ID")
    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号")
    private Long version;

    /** 包裹编号，外部系统给，且唯一 */
    @Excel(name = "包裹编号，外部系统给，且唯一")
    @ApiModelProperty(value = "包裹编号，外部系统给，且唯一")
    private String packageNo;

    /** 供应商ID */
    @Excel(name = "供应商ID")
    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;

    /** 供应商订单ID */
    @Excel(name = "供应商订单ID")
    @ApiModelProperty(value = "供应商订单ID")
    private Long supplierOrderId;

    /** 供应商订单编号 */
    @Excel(name = "供应商订单编号")
    @ApiModelProperty(value = "供应商订单编号")
    private String supplierOrderNo;

    /** 快递单号 */
    @Excel(name = "快递单号")
    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    /** 发货状态(0-待发货 1-已发货 2-已签收) */
    @Excel(name = "发货状态(0-待发货 1-已发货 2-已签收)")
    @ApiModelProperty(value = "发货状态(0-待发货 1-已发货 2-已签收)")
    private Integer deliveryState;

    /** 快递公司 */
    @Excel(name = "快递公司")
    @ApiModelProperty(value = "快递公司")
    private String expressCom;

    /** 快递公司编号 */
    @Excel(name = "快递公司编号")
    @ApiModelProperty(value = "快递公司编号")
    private String expressComNo;

    /** 发货时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "发货时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "发货时间")
    private Date deliveryTime;

    /** 预计送达时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "预计送达时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "预计送达时间")
    private Date planReceiveTime;

    /** 实际送达时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "实际送达时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "实际送达时间")
    private Date actReceiveTime;

    /** 包裹商品总数量 */
    @Excel(name = "包裹商品总数量")
    @ApiModelProperty(value = "包裹商品总数量")
    private Long totalNum;

    /** 包裹商品总金额 */
    @Excel(name = "包裹商品总金额")
    @ApiModelProperty(value = "包裹商品总金额")
    private BigDecimal totalAmt;

    /** 包裹重量(kg) */
    @Excel(name = "包裹重量(kg)")
    @ApiModelProperty(value = "包裹重量(kg)")
    private BigDecimal weight;

    /** 包裹体积(m³) */
    @Excel(name = "包裹体积(m³)")
    @ApiModelProperty(value = "包裹体积(m³)")
    private BigDecimal volume;

    /** 包裹长度(cm) */
    @Excel(name = "包裹长度(cm)")
    @ApiModelProperty(value = "包裹长度(cm)")
    private BigDecimal length;

    /** 包裹宽度(cm) */
    @Excel(name = "包裹宽度(cm)")
    @ApiModelProperty(value = "包裹宽度(cm)")
    private BigDecimal width;

    /** 包裹高度(cm) */
    @Excel(name = "包裹高度(cm)")
    @ApiModelProperty(value = "包裹高度(cm)")
    private BigDecimal height;

    /** 幂等键(防止重复提交) */
    @Excel(name = "幂等键(防止重复提交)")
    @ApiModelProperty(value = "幂等键(防止重复提交)")
    private String idempotentKey;

}
