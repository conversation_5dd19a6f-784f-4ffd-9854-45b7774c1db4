package com.zksr.trade.controller.order.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单PC打印列表数据信息
 * @date 2024/9/12 15:26
 */
@Data
@ApiModel(description = "订单PC打印列表数据信息")
public class PcOrderPrintDetailVO {

    @ApiModelProperty("商品名称")
    private String spuName;

    @ApiModelProperty("数量")
    private Integer num;

    @ApiModelProperty("订单规格")
    private String orderUnit;

    @ApiModelProperty("货号")
    private String itemNo;

    @ApiModelProperty("sku条码")
    private String barcode;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("总价")
    private BigDecimal totalAmt;

    @JsonIgnore
    private Long skuId;

    @JsonIgnore
    private Long spuId;

    @JsonIgnore
    private Long orderId;

    @JsonIgnore
    private Long supplierOrderId;

    @JsonIgnore
    private Long branchId;

    @JsonIgnore
    private Long supplierId;

    @JsonIgnore
    private Date createTime;

    @JsonIgnore
    @ApiModelProperty("订单号")
    private String orderNo;

    @JsonIgnore
    @ApiModelProperty("子订单编号")
    private String supplierOrderNo;

    @JsonIgnore
    @ApiModelProperty("收单方式")
    private String payWay;

    @JsonIgnore
    private Long memberId;

    @ApiModelProperty("备注")
    private String memo;

    @ApiModelProperty("打印次数")
    private Long printQty;
}
