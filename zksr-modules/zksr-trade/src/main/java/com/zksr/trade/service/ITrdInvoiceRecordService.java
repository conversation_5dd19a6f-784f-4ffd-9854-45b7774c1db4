package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.domain.TrdInvoiceRecord;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceRecordPageReqVO;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceRecordSaveReqVO;

/**
 * 发票操作历史（含开票信息冗余）Service接口
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface ITrdInvoiceRecordService {

    /**
     * 新增发票操作历史（含开票信息冗余）
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdInvoiceRecord(@Valid TrdInvoiceRecordSaveReqVO createReqVO);

    /**
     * 修改发票操作历史（含开票信息冗余）
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdInvoiceRecord(@Valid TrdInvoiceRecordSaveReqVO updateReqVO);

    /**
     * 删除发票操作历史（含开票信息冗余）
     *
     * @param id 主键ID
     */
    public void deleteTrdInvoiceRecord(Long id);

    /**
     * 批量删除发票操作历史（含开票信息冗余）
     *
     * @param ids 需要删除的发票操作历史（含开票信息冗余）主键集合
     * @return 结果
     */
    public void deleteTrdInvoiceRecordByIds(Long[] ids);

    /**
     * 获得发票操作历史（含开票信息冗余）
     *
     * @param id 主键ID
     * @return 发票操作历史（含开票信息冗余）
     */
    public TrdInvoiceRecord getTrdInvoiceRecord(Long id);

    /**
     * 获得发票操作历史（含开票信息冗余）分页
     *
     * @param pageReqVO 分页查询
     * @return 发票操作历史（含开票信息冗余）分页
     */
    PageResult<TrdInvoiceRecord> getTrdInvoiceRecordPage(TrdInvoiceRecordPageReqVO pageReqVO);

}
