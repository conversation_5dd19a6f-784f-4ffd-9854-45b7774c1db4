package com.zksr.trade.controller.orderSettle.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 入驻商订单结算信息对象 trd_supplier_order_settle
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Data
@ApiModel("入驻商订单结算信息 - trd_supplier_order_settle Response VO")
public class TrdSupplierOrderSettleRespVO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 订单id */
    @Excel(name = "订单id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    @ApiModelProperty(value = "入驻商订单id")
    private Long supplierOrderId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    @ApiModelProperty(value = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 入驻商订单明细id */
    @ApiModelProperty(value = "入驻商订单明细编号")
    private Long supplierOrderDtlId;

    /** 商品数量 */
    @Excel(name = "商品数量")
    @ApiModelProperty(value = "商品数量")
    private String itemQty;

    /** 商品销售价 */
    @Excel(name = "商品销售价")
    @ApiModelProperty(value = "商品销售价")
    private String itemPrice;

    /** 商品金额 */
    @Excel(name = "商品金额")
    @ApiModelProperty(value = "商品金额")
    private String itemAmt;

    /** 运费 */
    @Excel(name = "运费")
    @ApiModelProperty(value = "运费")
    private String transAmt;

    /** 入驻商结算金额 */
    @Excel(name = "入驻商结算金额")
    @ApiModelProperty(value = "入驻商结算金额")
    private BigDecimal supplierAmt;

    /** 利润 */
    @Excel(name = "利润")
    @ApiModelProperty(value = "利润")
    private BigDecimal profit;

    /** 入驻商成本价 */
    @Excel(name = "入驻商成本价")
    @ApiModelProperty(value = "入驻商成本价")
    private BigDecimal costPrice;

    /** 软件商分润比例 */
    @ApiModelProperty(value = "软件商分润比例")
    private BigDecimal softwareRate;

    /** 软件商结算金额 */
    @ApiModelProperty(value = "软件商结算金额")
    private BigDecimal softwareAmt;

    /** 运营商分润比例 */
    @Excel(name = "运营商分润比例")
    @ApiModelProperty(value = "运营商分润比例")
    private BigDecimal dcRate;

    /** 运营商结算金额 */
    @Excel(name = "运营商结算金额")
    @ApiModelProperty(value = "运营商结算金额")
    private BigDecimal dcAmt;

    /** 平台商分润比例 */
    @Excel(name = "平台商分润比例")
    @ApiModelProperty(value = "平台商分润比例")
    private BigDecimal partnerRate;

    /** 平台商结算金额 */
    @Excel(name = "平台商结算金额")
    @ApiModelProperty(value = "平台商结算金额")
    private BigDecimal partnerAmt;

    /** 业务员负责人分润比例 */
    @Excel(name = "业务员负责人分润比例")
    @ApiModelProperty(value = "业务员负责人分润比例")
    private BigDecimal colonel1Rate;

    /** 业务员结算金额 */
    @Excel(name = "业务员结算金额")
    @ApiModelProperty(value = "业务员结算金额")
    private BigDecimal colonel1Amt;

    /** 业务员分润比例 */
    @Excel(name = "业务员分润比例")
    @ApiModelProperty(value = "业务员分润比例")
    private BigDecimal colonel2Rate;

    /** 业务员结算金额 */
    @Excel(name = "业务员结算金额")
    @ApiModelProperty(value = "业务员结算金额")
    private BigDecimal colonel2Amt;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

}
