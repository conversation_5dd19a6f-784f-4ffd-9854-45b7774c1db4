package com.zksr.trade.controller.after.supplierVo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;


/**
 * 入驻商售后单对象 trd_supplier_after
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Data
@ApiModel("入驻商售后单 - trd_supplier_after Response VO")
public class TrdSupplierAfterRespVO {
    private static final long serialVersionUID = 1L;

    /** 入驻商订单id */
    @ApiModelProperty(value = "退款金额;实际退款金额")
    private Long supplierAfterId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 入驻商售后单编号 */
    @Excel(name = "入驻商售后单编号")
    @ApiModelProperty(value = "入驻商售后单编号")
    private String supplierAfterNo;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    /** 订单id */
    @Excel(name = "订单id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 售后单id */
    @Excel(name = "售后单id")
    @ApiModelProperty(value = "售后单id")
    private Long afterId;

    /** 售后单编号 */
    @Excel(name = "售后单编号")
    @ApiModelProperty(value = "售后单编号")
    private String afterNo;

    /** 配送费 */
    @Excel(name = "配送费")
    @ApiModelProperty(value = "配送费")
    private BigDecimal transAmt;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal subDiscountAmt;

    /** 退款金额;实际退款金额 */
    @Excel(name = "退款金额;实际退款金额")
    @ApiModelProperty(value = "退款金额;实际退款金额")
    private BigDecimal subRefundAmt;

}
