package com.zksr.trade.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "midea-invoice.branch")
public class MideaInvoiceConfig {


    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品单位
     */
    private String goodsUnit;


    /**
     * 商品数量
     */
    private Integer goodsCount;


    /**
     * 税收分类编码
     */
    private String texCode;


}
