package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 售后优惠明细对象 trd_after_discount_dtl
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
@TableName(value = "trd_after_discount_dtl")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdAfterDiscountDtl extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 售后优惠明细 */
    @TableId
    private Long afterDiscountDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 订单id */
    @Excel(name = "订单id")
    private Long orderId;

    /** 门店id */
    @Excel(name = "门店id")
    private Long branchId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    private Long skuId;

    /** 售后单id; */
    @Excel(name = "售后单id;")
    private Long afterId;

    /** 售后编号; */
    @Excel(name = "售后编号;")
    private String afterNo;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 售后单明细id */
    @Excel(name = "售后单明细id")
    private Long supplierAfterDtlId;

    /** 售后单明细编号 */
    @Excel(name = "售后单明细编号")
    private String supplierAfterDtlNo;

    /** 优惠类型（数据字典） */
    @Excel(name = "优惠类型", readConverterExp = "数=据字典")
    private String discountType;

    /** 优惠id */
    @Excel(name = "优惠id")
    private Long discountId;

    /** 活动优惠金额(分摊的)(new) */
    @Excel(name = "活动优惠金额(分摊的)(new)")
    private BigDecimal activityDiscountAmt;

    /** 优惠金额(分摊的)(new) */
    @Excel(name = "优惠金额(分摊的)(new)")
    private BigDecimal couponDiscountAmt;

    /** 优惠金额(不分摊的)(new) */
    @Excel(name = "优惠金额(不分摊的)(new)")
    private BigDecimal couponDiscountAmt2;

    /** 赠品类型;0-商品 1-优惠券 */
    @Excel(name = "赠品类型;0-商品 1-优惠券")
    private Long giftType;

    /** 赠品sku;gift_type=0 则记录;gift_type=1 则记录 */
    @Excel(name = "赠品sku;gift_type=0 则记录;gift_type=1 则记录")
    private Long giftSkuId;

    /** 赠品sku优惠券模板 */
    @Excel(name = "赠品sku优惠券模板")
    private Long giftCouponTemplateId;

    /** 赠品数量
     * SAASB-507 【成都佰润】散装称重商品需求
     * */
    @Excel(name = "赠品数量")
    private BigDecimal giftQty;

    /**
     * 活动优惠规则ID
     */
    private Long discountRuleId;

}
