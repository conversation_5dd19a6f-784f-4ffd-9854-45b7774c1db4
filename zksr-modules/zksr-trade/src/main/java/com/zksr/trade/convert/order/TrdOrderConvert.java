package com.zksr.trade.convert.order;

import com.zksr.member.api.member.dto.MemMemberInvoiceRespDTO;
import com.zksr.system.api.partnerPolicy.dto.OrderSettingPolicyDTO;
import com.zksr.trade.api.order.dto.TrdOrderMiniHeadRespDTO;
import com.zksr.trade.api.order.dto.TrdOrderMiniRespDTO;
import com.zksr.trade.api.order.dto.TrdOrderRespDTO;
import com.zksr.trade.controller.order.vo.DcSupplierOrderPageRespVO;
import com.zksr.trade.controller.order.vo.HomePageRespVO;
import com.zksr.trade.domain.TrdSupplierOrderInvoice;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/6/25 15:13
 */
@Mapper
public interface TrdOrderConvert {

    public static TrdOrderConvert INSTANCE = Mappers.getMapper(TrdOrderConvert.class);


    @Mappings({
            @Mapping(source = "result2.todayOrder", target = "todayOrder"),
            @Mapping(source = "result2.todayAmount", target = "todayAmount"),
    })
    @BeanMapping(ignoreByDefault = true)
    public void buildSetSupplierPageHomeResp(@MappingTarget HomePageRespVO result, HomePageRespVO result2);

    // 配置 -> 订单设置
    @Mappings({
            @Mapping(source = "settingPolicyInfo.showAfterHour", target = "showAfterHour")
    })
    @BeanMapping(ignoreByDefault = true)
    void buildSetOrderSetting(@MappingTarget TrdOrderRespDTO item, OrderSettingPolicyDTO settingPolicyInfo);

    // 配置 -> 订单设置
    @Mappings({
            @Mapping(source = "settingPolicyInfo.showAfterHour", target = "showAfterHour")
    })
    @BeanMapping(ignoreByDefault = true)
    void buildSetOrderSetting(@MappingTarget DcSupplierOrderPageRespVO item, OrderSettingPolicyDTO settingPolicyInfo);

    TrdOrderMiniHeadRespDTO convert2TrdOrderMiniHeadRespDTO(TrdOrderMiniRespDTO respDto);

    @Mappings({
            @Mapping(source = "memMemberInvoiceRespDTO.id", target = "memberInvoiceId")
    })
    TrdSupplierOrderInvoice convert2TrdSupplierOrderInvoice(MemMemberInvoiceRespDTO memMemberInvoiceRespDTO);
}
