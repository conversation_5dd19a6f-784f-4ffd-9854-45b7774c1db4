package com.zksr.trade.controller.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.api.order.dto.TrdOrderDeliveryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * @description: PC端订单查询页面返回实体
 */
@ApiModel("PC端订单查询页面返回实体")
@Data
@Accessors(chain = true)
public class DcSupplierOrderPageRespVO {

    @ApiModelProperty("订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("平台编号")
    private Long sysCode;

    @ApiModelProperty("订单状态（数据字典）")
    private Long deliveryState;

    @ApiModelProperty("运营商id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dcId;

    @ApiModelProperty("门店id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long branchId;

    @ApiModelProperty("门店名称")
    private String branchName;

    @ApiModelProperty("门店地址")
    private String branchAddr;

    @ApiModelProperty("门店门头照片")
    private String branchImages;

    @ApiModelProperty("业务员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long colonelId;

    @ApiModelProperty("业务员名称")
    private String colonelName;

    @ApiModelProperty("业务员联系电话")
    private String colonelPhone;

    @ApiModelProperty("上级业务员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pcolonelId;

    @ApiModelProperty("上级业务员名称")
    private String pcolonelName;

    @ApiModelProperty("上级业务员联系电话")
    private String pcolonelPhone;

    @ApiModelProperty("入驻商订单Id（子订单Id）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierOrderId;

    @ApiModelProperty("入驻商订单号（子订单号）")
    private String supplierOrderNo;

    @ApiModelProperty("入驻商id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierId;

    @ApiModelProperty("入驻商名称")
    private String supplierName;

    @ApiModelProperty("入驻商联系电话")
    private String contactPhone;

    @ApiModelProperty("支付方式")
    private String payWay;

    @ApiModelProperty("支付状态 数据字典：sys_pay_status")
    private String payState;

    @ApiModelProperty("订单支付时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    private Date payTime;

    @ApiModelProperty("订单类型 0：全国商品 1：本地商品")
    private Integer orderType;

    @ApiModelProperty("订单创建时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    private Date createTime;

    @ApiModelProperty("门店联系人名称")
    private String branchContactName;

    @ApiModelProperty("门店联系人电话")
    private String branchContactPhone;

    @ApiModelProperty("合计金额")
    private BigDecimal saleTotalAmt;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmt;

    @ApiModelProperty("实际支付金额")
    private BigDecimal payTotalAmt;

    @ApiModelProperty("要货总数量")
    private Long demandTotalNum;

    @ApiModelProperty("订单备注")
    private String memo;

    @ApiModelProperty("入驻商订单备注")
    private String supplierMemo;

    /**
     * 推送状态 0未推送 1已推送 2已接收
     */
    @Excel(name = "推送状态")
    @ApiModelProperty("推送状态 0未推送 1已推送 2已接收")
    private Integer pushStatus;

    @ApiModelProperty(value = "入驻商订单外部订单号")
    private String sourceOrderNo;

    @ApiModelProperty("订单已售后总金额")
    private BigDecimal afterReturnTotalAmt;

    /**
     * 是否展示订单售后时间
     */
    @ApiModelProperty("是否展示订单售后时间(默认不显示). 0-不显示, 1-显示")
    private String showAfterHour;

    @ApiModelProperty("入驻商订单明细集合")
    private List<DcSupplierOrderDtlRespVO> supplierOrderDtlRespVOList;

    @ApiModelProperty("入驻商订单操作信息集合")
    private List<TrdOrderDeliveryDTO> orderDeliveryDTOList;

    @ApiModelProperty("订单优惠信息集合")
    private List<DcSupplierOrderDtlDiscountRespVO> dcSupplierOrderRespVOList;

    @ApiModelProperty(value = "异常状态")
    private Integer errorState = 0;

    @ApiModelProperty(value = "异常状态描述")
    private String errorMemo;

    @ApiModelProperty(name = "司机ID")
    private Long driverId;

    @ApiModelProperty(name = "司机评价状态")
    private Integer driverRatingFlag;

    @ApiModelProperty(name = "司机评价ID")
    private Long driverRatingId;

    @ApiModelProperty("司机评论时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    private Date driverRatingTime;

    @ApiModelProperty(name = "打印状态（0：未打印，1：已打印）")
    private Integer printState;

    @ApiModelProperty("打印次数")
    private Long printQty;

    @ApiModelProperty("支付流水号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long payFlowId;

//    @Excel(name = "入驻商订单状态")
//    @ApiModelProperty(value = "入驻商订单状态")
//    private String deliveryState;
}
