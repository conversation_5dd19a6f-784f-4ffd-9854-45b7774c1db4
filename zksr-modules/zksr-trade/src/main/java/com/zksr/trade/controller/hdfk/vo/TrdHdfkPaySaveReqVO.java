package com.zksr.trade.controller.hdfk.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 货到付款付款单对象 trd_hdfk_pay
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@ApiModel("货到付款付款单 - trd_hdfk_pay分页 Request VO")
public class TrdHdfkPaySaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 货到付款付款单id */
    @ApiModelProperty(value = "付款单来源(数据字典)")
    private Long hdfkPayId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 付款单号 */
    @Excel(name = "付款单号")
    @ApiModelProperty(value = "付款单号")
    private String hdfkPayNo;

    /** 门店id */
    @Excel(name = "门店id")
    @ApiModelProperty(value = "门店id")
    private String branchId;

    /** 支付状态（数据字典sys_pay_state）；0-未支付 1-已支付 */
    @Excel(name = "支付状态", readConverterExp = "数=据字典sys_pay_state")
    @ApiModelProperty(value = "支付状态")
    private Integer payState;

    /** 支付方式(数据字典sys_hdfk_pay_way));0-在线支付 */
    @Excel(name = "支付方式(数据字典sys_hdfk_pay_way));0-在线支付")
    @ApiModelProperty(value = "支付方式(数据字典sys_hdfk_pay_way));0-在线支付")
    private String payWay;

    /** 支付时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "支付时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    /** 支付金额；实际支付金额 */
    @Excel(name = "支付金额；实际支付金额")
    @ApiModelProperty(value = "支付金额；实际支付金额")
    private BigDecimal payAmt;

    /** 订单金额；未减去优惠的订单金额 */
    @Excel(name = "订单金额；未减去优惠的订单金额")
    @ApiModelProperty(value = "订单金额；未减去优惠的订单金额")
    private BigDecimal orderAmt;

    /** 支付公司收取的支付费率 */
    @Excel(name = "支付公司收取的支付费率")
    @ApiModelProperty(value = "支付公司收取的支付费率")
    private BigDecimal payRate;

    /** 支付平台手续费；(pay_amt*pay_rate) 四舍五入 */
    @Excel(name = "支付平台手续费；(pay_amt*pay_rate) 四舍五入")
    @ApiModelProperty(value = "支付平台手续费；(pay_amt*pay_rate) 四舍五入")
    private BigDecimal payFee;

    /** 支付平台(数据字典);从sys_partner表字段jy_platform获取 */
    @Excel(name = "支付平台(数据字典);从sys_partner表字段jy_platform获取")
    @ApiModelProperty(value = "支付平台(数据字典);从sys_partner表字段jy_platform获取")
    private String platform;

    /** 付款单来源(数据字典) */
    @Excel(name = "付款单来源(数据字典)")
    @ApiModelProperty(value = "付款单来源(数据字典)")
    private Integer paySource;

}
