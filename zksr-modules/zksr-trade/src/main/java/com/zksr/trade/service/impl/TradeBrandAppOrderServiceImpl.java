package com.zksr.trade.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.RemoteUserService;
import com.zksr.system.api.brand.BrandMerchantApi;
import com.zksr.system.api.brand.vo.SysBrandMerchantRespVO;
import com.zksr.system.api.domain.SysUser;
import com.zksr.trade.controller.app.dto.BrandMerchantDataScopeDTO;
import com.zksr.trade.controller.app.vo.BrandHomePageRespVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListInfoRespVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListReqVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListRespVO;
import com.zksr.trade.mapper.TrdAfterMapper;
import com.zksr.trade.mapper.TrdOrderMapper;
import com.zksr.trade.service.TradeBrandAppOrderService;
import com.zksr.trade.service.TrdCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:    品牌商app查询
 * @date 2024/8/6 14:05
 */
@Service
public class TradeBrandAppOrderServiceImpl implements TradeBrandAppOrderService {

    @Autowired
    private TrdOrderMapper trdOrderMapper;

    @Autowired
    private TrdAfterMapper trdAfterMapper;

    @Resource
    private BrandMerchantApi brandMerchantApi;

    @Autowired
    private TrdCacheService trdCacheService;

    @Resource
    private RemoteUserService remoteUserService;

    @Override
    public BrandHomePageRespVO getBrandHomePage() {
        BrandMerchantDataScopeDTO scopeDTO = getDataScopeDTO();
        if (Objects.isNull(scopeDTO)) {
            return null;
        }
        BrandHomePageRespVO saleData = trdOrderMapper.selectByBrandHomeSaleData(scopeDTO.getBrandIdList(), scopeDTO.getAreaIdList());
        BrandHomePageRespVO afterData = trdAfterMapper.selectByBrandHomeAfterData(scopeDTO.getBrandIdList(), scopeDTO.getAreaIdList());
        saleData.setTodayAfter(afterData.getTodayAfter());
        saleData.setTodayAfterAmt(afterData.getTodayAfterAmt());
        return saleData;
    }

    @Override
    public List<BrandHomeSaleListRespVO> getHomeSaleList(BrandHomeSaleListReqVO reqVO) {
        BrandMerchantDataScopeDTO scopeDTO = getDataScopeDTO();
        if (Objects.isNull(scopeDTO)) {
            return null;
        }
        reqVO.setBrandList(scopeDTO.getBrandIdList());
        reqVO.setAreaList(scopeDTO.getAreaIdList());
        // 查询正向数据
        List<BrandHomeSaleListRespVO> saleList = trdOrderMapper.selectByBrandHomeSaleList(reqVO);
        // 查询退单数据
        List<BrandHomeSaleListRespVO> afterList = trdAfterMapper.selectByBrandHomeSaleList(reqVO);
        // 合并数据
        Map<Long, BrandHomeSaleListRespVO> saleListRespVOMap = saleList.stream().collect(Collectors.toMap(BrandHomeSaleListRespVO::getBrandId, item -> item));
        afterList.forEach(item -> {
            if (saleListRespVOMap.containsKey(item.getBrandId())) {
                BrandHomeSaleListRespVO respVO = saleListRespVOMap.get(item.getBrandId());
                respVO.setAfterAmt(item.getAfterAmt());
                respVO.setAfterOrder(item.getAfterOrder());
            } else {
                saleList.add(item);
            }
        });
        // 渲染品牌名称
        for (BrandHomeSaleListRespVO respVO : saleList) {
            BrandDTO brandDTO = trdCacheService.getBrandDTO(respVO.getBrandId());
            if (Objects.nonNull(brandDTO)) {
                respVO.setBrandName(brandDTO.getBrandName());
            }
        }
        return saleList;
    }

    @Override
    public List<BrandHomeSaleListInfoRespVO> getHomeSaleListInfo(BrandHomeSaleListReqVO reqVO) {
        BrandMerchantDataScopeDTO scopeDTO = getDataScopeDTO();
        if (Objects.isNull(scopeDTO)) {
            return null;
        }
        reqVO.setBrandList(ListUtil.toList(reqVO.getBrandId()));
        reqVO.setAreaList(scopeDTO.getAreaIdList());
        // 查询正向数据
        List<BrandHomeSaleListInfoRespVO> saleList = trdOrderMapper.selectByBrandHomeSaleInfoList(reqVO);
        // 查询退单数据
        List<BrandHomeSaleListInfoRespVO> afterList = trdAfterMapper.selectByBrandHomeSaleInfoList(reqVO);
        // 合并数据
        Map<Long, BrandHomeSaleListInfoRespVO> saleListRespVOMap = saleList.stream().collect(Collectors.toMap(BrandHomeSaleListInfoRespVO::getSpuId, item -> item));
        afterList.forEach(item -> {
            if (saleListRespVOMap.containsKey(item.getSpuId())) {
                BrandHomeSaleListInfoRespVO respVO = saleListRespVOMap.get(item.getSpuId());
                respVO.setAfterAmt(item.getAfterAmt());
                respVO.setAfterOrder(item.getAfterOrder());
            } else {
                saleList.add(item);
            }
        });
        // 渲染商品名称
        for (BrandHomeSaleListInfoRespVO respVO : saleList) {
            SpuDTO spuDTO = trdCacheService.getSpuDTO(respVO.getSpuId());
            if (Objects.nonNull(spuDTO)) {
                respVO.setSpuName(spuDTO.getSpuName());
            }
        }
        return saleList;
    }


    // 获取品牌商
    private BrandMerchantDataScopeDTO getDataScopeDTO() {
        SysUser sysUser = remoteUserService.getSysUser(SecurityUtils.getUserId()).getCheckedData();
        SysBrandMerchantRespVO merchantRespVO = brandMerchantApi.getByUser(SecurityUtils.getUserId()).getCheckedData();
        if (Objects.isNull(sysUser) || Objects.isNull(merchantRespVO)) {
            return null;
        }
        if (StringUtils.isEmpty(sysUser.getBrandId())) {
            return null;
        }
        List<Long> brandList = Arrays.stream(sysUser.getBrandId().split(StringPool.COMMA))
                .map(Long::parseLong)
                .collect(Collectors.toList());

        List<Long> areaList = null;
        if (StringUtils.isNotEmpty(sysUser.getAreaId())) {
            areaList = Arrays.stream(sysUser.getAreaId().split(StringPool.COMMA))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }
        return new BrandMerchantDataScopeDTO(brandList, areaList);
    }
}
