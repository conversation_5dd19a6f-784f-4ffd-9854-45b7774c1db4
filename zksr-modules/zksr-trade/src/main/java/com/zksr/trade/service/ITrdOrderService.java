package com.zksr.trade.service;

import com.zksr.common.core.domain.vo.openapi.syncCall.SyncAfterOrderCallDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncOrderCallDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncOrderPageReqDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsStoreProduct;
import com.zksr.common.core.domain.vo.openapi.CustomApiDetail;
import com.zksr.common.core.domain.vo.openapi.CustomApiMaster;
import com.zksr.report.api.homePages.dto.HomePagesCurrentSalesDataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesOrderSalesDataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.vo.SupplierOrderCountPageReqVO;
import com.zksr.trade.api.order.vo.SupplierOrderCountRespVO;
import com.zksr.system.api.supplier.vo.SupplierOrderVO;
import com.zksr.trade.api.after.dto.BranchSkuMergeAfterResDTO;
import com.zksr.trade.api.after.dto.OrderMergeAfterResDTO;
import com.zksr.trade.api.after.vo.BranchSkuMergeAfterReqVO;
import com.zksr.trade.api.order.dto.ColonelOrderDaySettleDTO;
import com.zksr.trade.api.order.vo.ColonelOrderDaySettleVO;
import com.zksr.trade.api.after.dto.OrderAfterResDTO;
import com.zksr.trade.api.after.vo.OrderAfterRequest;
import com.zksr.trade.api.order.dto.*;
import com.zksr.trade.api.order.vo.*;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderDtlVO;
import com.zksr.trade.controller.order.vo.*;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.print.vo.PrintSupplierOrderVO;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 订单Service接口
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
public interface ITrdOrderService {

    /**
     * 新增订单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdOrder(@Valid TrdOrderSaveReqVO createReqVO);

    /**
     * 修改订单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdOrder(@Valid TrdOrderSaveReqVO updateReqVO);

    /**
     * 删除订单
     *
     * @param orderId 订单id
     */
    public void deleteTrdOrder(Long orderId);

    /**
     * 批量删除订单
     *
     * @param orderIds 需要删除的订单主键集合
     * @return 结果
     */
    public void deleteTrdOrderByOrderIds(Long[] orderIds);

    /**
     * 获得订单
     *
     * @param orderId 订单id
     * @return 订单
     */
    public TrdOrder getTrdOrder(Long orderId);

    /**
     * 获得订单分页
     *
     * @param pageReqVO 分页查询
     * @return 订单分页
     */
    PageResult<TrdOrder> getTrdOrderPage(TrdOrderPageReqVO pageReqVO);


    /**
     * 订单保存
     * @param trdOrderSaveVo
     * @return
     */
    TrdOrderResDto saveOrder(RemoteSaveOrderVO trdOrderSaveVo);

    /**
     * 获取订单入驻商 订单和进件信息
     * @param pageVo
     * @return
     */
    OrderPayInfoRespDTO getSupplierPayInfo(TrdSupplierPageVO pageVo);

    /**
     * 支付成功订单回调
     * @param pageVo
     * @return
     */
    void orderPaySuccessCallback(TrdPayOrderPageVO pageVo);

    /**
     * 支付成功订单回调,入驻商维度
     * @param supplierOrder
     * @param pageVo
     */
    void orderPaySuccessCallbackBySupplierOrder(TrdSupplierOrder supplierOrder, TrdPayOrderPageVO pageVo);

    /**
     * 处理订单更新成功之后（用于处理订单支付完成后的业务逻辑）
     * @param trdOrder
     * @return
     */
    void afterOrderPayUpdateSuccess(TrdOrder trdOrder);



    /**
    * @Description: 分页获取订单数据 （小程序使用）
    * @Author: chenmingqing
    * @Date: 2025/3/26 10:29
    */
    CommonResult<PageResult<TrdOrderMiniHeadRespDTO>> pageOrderList(TrdOrderPageReqDTO orderPageReqVO);

    /**
     * @Description: 分页获取订单数据 新（商城小程序、商城APP、业务员小程序我的代办）
     * @Author: chenmingqing
     * @Date: 2024/7/29 10:29
     */
    PageResult<TrdOrderRespDTO> pageOrderListNew(TrdOrderPageReqDTO orderPageReqVO);

    /**
     * @Description: 获取订单详情数据 新
     * @Author: chenmingqing
     * @Date: 2024/7/31 10:29
     */
    TrdOrderRespDTO getOrderInfo(TrdOrderPageReqDTO orderPageReqVO);

    /**
    * @Description: 获取入驻商小程序首页统计数据
    * @Author: liuxingyu
    * @Date: 2024/4/3 9:10
    */
    HomePageRespVO getHomePage(HomePageReqVO homePageReqVo);

    /**
    * @Description: 获取入驻商小程序订单分页列表
    * @Author: liuxingyu
    * @Date: 2024/4/8 11:17
    */
    PageResult<MiniProgramRespVO> getMerchantMiniProgramOrderPageList(TrdOrderPageReqDTO trdOrderPageReqDTO);

    /**
     *  单据发货出库
     * @param trdOrderOperVO
     */
    void orderOutbound(TrdOrderOperVO trdOrderOperVO);

    /**
     *  单据装车
     * @param trdOrderOperVO
     */
    void orderEntrucking(TrdOrderOperVO trdOrderOperVO);

    /**
     *  单据取消装车
     * @param trdOrderOperVO
     */
    void orderCancelEntrucking(TrdOrderOperVO trdOrderOperVO);


    /**
     *  单据配送完成（收货 本地商品）
     * @param trdOrderOperVO
     */
    void orderTakeDelivery(TrdOrderOperVO trdOrderOperVO);

    /**
    * @Description: 获取运营商订单管理
    * @Author: liuxingyu
    * @Date: 2024/4/9 10:11
    */
    PageResult<DcOrderPageRespVO> getOperatorOrderPageList(DcOrderPageReqVO dcOrderPageReqVO);

    /**
     * 获取运营商订单管理(新)
     * @param dcOrderPageReqVO
     * @return
     */
    PageResult<DcSupplierOrderPageRespVO> getOperatorOrderPageListNew(DcOrderPageReqVO dcOrderPageReqVO);

    /**
     * 获取订单打印数据
     * @param dcOrderPageReqVO
     * @return
     */
    List<PcOrderPrintMasterVO> getOperatorOrderPageListNewPrint(DcOrderPageReqVO dcOrderPageReqVO);

    /**
     * 获取运营商订单详情(入驻商订单维度)
     * @param supplierOrderId
     * @return
     */
    DcSupplierOrderPageRespVO getOperatorOrderInfoDetail(Long supplierOrderId, Integer deliveryState);

    /**
     * 订单取消
     * @param supplierOrderNo
     */
    void orderCancel(String supplierOrderNo);

    /**
     * 订单确认收货（定时任务  全国商品）
     * @return
     */
    void orderTakeDeliveryJob(TrdOrderTakeDeliveryVO takeDeliveryVO);


    /**
     * 订单完成
     * @return
     */
    void orderComplete(TrdOrderTakeDeliveryVO takeDeliveryVO);

    /**
     * 订单创建转账单或 申请分账提交
     * @param takeDeliveryVO
     */
    void orderCreateSettleTransfer(Long sysCode, Long orderId);

    /**
     * 订单确认收货（全国商品）
     * @return
     */
    void orderTakeDelivery(Long supplierOrderDtlId);


    /**
     * 根据条件获取可售后的订单数据
     * @param request
     */
    OrderAfterResDTO getOrderAfterInfoByOrderInfo(OrderAfterRequest request);

    /**
     * 根据条件获取门店合单售后商品列表
     * @param reqVo
     * @return
     */
    List<BranchSkuMergeAfterResDTO> branchSkuMergeAfterList(BranchSkuMergeAfterReqVO reqVo);

    /**
     * 根据条件获取门店合单售后商品信息- 提交页
     * @param reqVo
     * @return
     */
    OrderMergeAfterResDTO orderMergeAfter(BranchSkuMergeAfterReqVO reqVo);

    /**
     * 根据订单id返回订单所有数据，用于写入售后单数据
     * @param orderId
     */
   RemoteSaveOrderVO getOrderInfoByOrderId(Long orderId);

    /**
    * @Description: 打印获取订单数据
    * @Author: liuxingyu
    * @Date: 2024/4/19 15:48
    */
    List<PrintSupplierOrderVO> printGetByOrderId(Long orderId, Long sysCode, Long supplierOrderId);

    /**
     * 获取订单状态的数量（角标）
     * @return
     */
    OrderStatusVO getOrderStatus(OrderStatusReqVO reqVO);

    /**
     * 当月订单金额统计
     * @param branchId 门店ID
     * @return
     */
    OrderAmountStatisticsVO getOrderAmountStatisticsVO(Long branchId);

    /**
     * @Description: 获取业务员App订单
     * @Author: liyi
     * @Date: 2024/4/26 10:29
     */
    ColonelAppOrderListTotalDTO selectMemColoneAppOrderListTotal(TrdColonelAppOrderListPageReqVO orderPageReqVO);


    /**
     * @Description: 获取业务员App订单
     * @Author: liyi
     * @Date: 2024/4/26 10:29
     */
    PageResult<TrdColonelAppOrderListRespVO> selectMemColoneAppOrder(TrdColonelAppOrderListPageReqVO orderPageReqVO);

    /**
     * @Description: 获取业务员App订单 (新)
     * @Author: 陈明清
     * @Date: 2025/1/9 18:18
     */
    List<TrdOrderRespDTO> selectMemColoneAppOrderNew(TrdColonelAppOrderListPageReqVO orderPageReqVO);

    /**
     * @Description: 获取业务员App订单详情
     * @Author: liyi
     * @Date: 2024/4/26 10:29
     */
    public List<TrdColonelAppOrderDetailRespVO> getMemColoneAppOrderDetail(Long orderId);

    /**
    * @Description: 根据门店获取常购信息
    * @Author: liuxingyu
    * @Date: 2024/4/29 10:53
    */
    List<EsStoreProduct> getListByBranch(List<EsStoreProduct> esStoreProductList);

    /**
    * @Description: 获取常购商品列表
    * @Author: liuxingyu
    * @Date: 2024/5/7 14:42
    */
    PageResult<StoreProductRespVO> getEsStoreProductList(StoreProductRequest storeProductRequest);

    /**
    * @Description: ES根据订单ID获取订单明细列表
    * @Author: liuxingyu
    * @Date: 2024/5/7 17:34
    */
    List<EsStoreProduct> getEsListByOrderId(Long orderId);

    /**
     * @Description: 根据条件获取某段时间的订单销售金额
     * @Author: liyi
     * @Date: 2024/5/14 15:15
     */
    public BigDecimal getSaleAmount(TrdColonelAppOrderListPageReqVO reqVO);


    /**
     * 获取优惠券拓展统计数据
     * @param couponTemplateIdList  优惠券模版ID集合
     * @return  订单使用优惠券的订单金额, 订单实际使用优惠金额
     */
    List<CouponExtendTotalVO> getCouponExtendTotal(List<Long> couponTemplateIdList);


    /**
     * 货到付款单支付成功回调更新订单明细支付状态
     * @param supplierOrderDtlIdList
     */
    void hdfkOrderPaySuccessUpdateStatus(Set<Long> supplierOrderDtlIdList, String payPlatform, Long hdfkPayId, String hdfkPayWay);

    CustomApiMaster getCustomApiMaster(CustomApiMaster customApiMaster);

    List<CustomApiDetail> getCustomApiDetail(String apiNo);


    /**
     * 获取该优惠劵模板 和 参与客户的 使用优惠劵订单商品汇总数据
     * @param couponTemplateId 优惠券模版ID
     * @param customerIdList 使用优惠劵的客户ID集合
     * @return
     */
    List<CouponCustomerOrderUseTotalDTO> getCouponCustomerUseTotal(Long couponTemplateId, List<Long> customerIdList);

    /**
     * 获取该优惠劵模板 和 参与客户的 使用优惠劵订单商品数据(按订单区分)
     * @param pageReqVO
     * @return
     */
    PageResult<CouponCustomerOrderUseTotalDTO> getCouponCustomerOrderUseDetailTotal(TrdOrderPromitionReportPageReqVO pageReqVO);

    /**
     * 获取该优惠劵模板 和 参与客户的 使用优惠劵订单商品数据(按订单区分)
     * @param pageReqVO
     * @return
     */
    PageResult<CouponCustomerOrderUseDetilDTO> getCouponCustomerOrderUseDetail(TrdOrderPromitionReportPageReqVO pageReqVO);

    /**
     * 查询活动订单汇总数据
     * @param activityIds
     * @return
     */
    List<ActivityOrderTotalDTO> getPrmActivityByActivityIds(List<Long> activityIds);

    /**
     * 获取参与该活动的客户订单汇总数据(按客户区分)
     * @param pageReqVO
     * @return
     */
    PageResult<ActivityCustomerOrderUseTotalDTO> getActivityCustomerTotal(TrdOrderPromitionReportPageReqVO pageReqVO);

    /**
     * 获取使用该活动 指定客户的订单报表（按客户订单汇总）
     * @param pageReqVO
     * @return
     */
    PageResult<ActivityCustomerOrderUseTotalDTO> getActivityCustomerOrderTotal(TrdOrderPromitionReportPageReqVO pageReqVO);

    /**
     * 获取使用该活动 指定客户、指定订单的明细数据
     * @param pageReqVO
     * @return
     */
    PageResult<ActivityCustomerOrderUseDetailDTO> getActivityCustomerOrderDetail(TrdOrderPromitionReportPageReqVO pageReqVO);

    /**
     * 根据订单编号获得订单
     *
     * @param orderNo 订单id
     * @return 订单
     */
    public TrdOrder getTrdOrderByOrderNo(String orderNo);

    /**
     *  查询业务员 结算订单销售数据
     * @param reqVO
     * @return
     */
    List<ColonelOrderDaySettleDTO> getColonelOrderBusinessSettle(ColonelOrderDaySettleVO reqVO);

    /**
     * 根据门店、日期区间 查询门店订单销售数据汇总 (按照年月进行拆分)
     * @param reqVO
     * @return
     */
    List<EsBranchOrderSalesRespDTO> getBranchOrderSales(EsBranchOrderSalesReqVO reqVO);

    PageResult<SyncOrderCallDTO> getOrdersWithDelay(SyncOrderPageReqDTO reqVO);

    PageResult<SyncAfterOrderCallDTO> getAfterOrdersWithDelay(SyncOrderPageReqDTO reqVO);

    /**
     * 根据 实际支付单号（订单单号或货到付款单号） 获取订单明细Id集合
     * @param sheetNo
     * @return
     */
    List<Long> getOrderAndAfterSettleIdBySheetNo(String sheetNo);

    /**
     * 根据查询条件获取当前订单明细信息导出数据
     * @param dcOrderPageReqVO
     * @return
     */
    List<SupplierOrderDtlInfoExportVO> getSupplierOrderDtlInfoExport(DcOrderPageReqApiVO dcOrderPageReqVO);

    /**
     * 驻商单据订单打印次数调整 （默认 + 1）
     * @return
     */
    void editOrderPrintQty(String[] supplierOrderNos);

    /**
     * 获取首页当前销售、欠款、退单实时数据
     * @param reqVO
     * @return
     */
    List<HomePagesCurrentSalesDataRespDTO> getHomePagesCurrentSalesData(HomePagesReqVO reqVO);

    /**
     * 获取订单销售数据
     * @param reqVO
     * @return
     */
    List<HomePagesOrderSalesDataRespDTO> getHomePagesOrderSalesData(HomePagesReqVO reqVO);

    /**
     * 根据类型查询销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 根据 用户账号或门店账号查询出是否下过订单
     * @param type member：用户，branch：门店
     * @param ids 用户或门店账号ID
     * @return 返回下过单的用户或门店ID
     */
    List<Long> checkMemberOrBranchExistsOrderSaleInfo(String type, Long sysCode, List<Long> ids);

    /**
     * 更新常购订单数据
     * @param trdOrder  常购订单
     */
    void updateFrequentByOrder(TrdOrder trdOrder);

    /**
     * 获取欠款金额
     * @param dcOrderPageReqVO
     * @return
     */
    PageResult<DcSupplierOrderPageRespVO> getDebtOrderPageList(DcOrderPageReqVO dcOrderPageReqVO);

    BigDecimal getDebtOrderAmt(DcOrderPageReqVO dcOrderPageReqVO);

    List<SupplierOrderDtlInfoExportVO> getDebtSupplierOrderDtlInfoExport(DcOrderPageReqApiVO bean);

    /**
     * 根据门店ID获取业务员APP客户信息中的订单信息（当月）
     * @param branchId
     * @return
     */
    List<ColonelAppBranchOrderDTO> getColonelAppBranchOrder(Long branchId);

    /**
     * 根据业务员ID获取业务员APP首页的订单信息（当天）
     * @param colonelId
     * @return
     */
    List<ColonelAppPageOrderDTO> getColonelAppPageOrder(Long colonelId);

    Boolean getAreaIdExistOrder(Long areaId, Long sysCode);

    Boolean getBranchIdExistOrder(Long branchId);

    List<TrdSupplierOrderDtlVO> getSupplierOrderDtlByOrderNo(String orderNo);

    /**
     * 根据订单号查询明细
     * @param orderNos
     * @return
     */
    List<TrdSupplierOrderDtlVO> getSupplierOrderDtlByOrderNos(List<String> orderNos);

    /**
     * 获取门店入驻商截团订单金额
     */
    OrderCutAmtDTO getOrderCntAmt(OrderCutAmtDTO.CacheKey cacheKey);

    /**
     * 根平台商ID 查询 近一年 这些门店最后一次下单的订单信息
     *
     * @param sysCode
     * @return
     */
    List<TrdOrder> getBranchLatestOrderByBranchIdList(Long sysCode);

    /**
     * 根据门店ID 查询 该门店最后一次下单的订单信息
     * @param branchId
     * @return
     */
    TrdOrder getBranchLatestOrderByBranchId(Long branchId);

    List<SupplierOrderVO> selectSupplierOrder(DcOrderPageReqVO param);

    List<SupplierOrderExportVO> selectSupplierOrderExport(DcOrderPageReqVO param);

    /**
     * 统计订单数量
     * @param pageReqVO
     * @return
     */
    List<SupplierOrderCountRespVO> countSupplierOrder(SupplierOrderCountPageReqVO pageReqVO);

    /**
     * 按平台商统计总单量
     * @param pageReqVO
     * @return
     */
    List<SupplierOrderCountAllRespVO> countSupplierOrderAll(SupplierOrderCountAllPageReqVO pageReqVO);

    void deleteOrder(Long orderId);

    void sendOrderHdfkOrPaySuccess(RemoteSaveOrderVO orderVo);

    /**
     * 实时查询当日销售额
     * @param reqVO
     * @return
     */
    HomePagesCurrentSalesDataRespVO getHomePagesCurrentDaySales(HomePagesCurrentSalesDataReqVO reqVO);


    /**
     * 获取门店距上次下单的描述信息
     *
     * @param branchId 门店ID
     * @return 如果有记录返回具体数据，否则返回 "从未下单"
     */
    Long getOrderLastTimeDescriptionByBranchId(Long branchId);

    Map<String, Object> countOrderStatsByBranchId(Long branchId);

}
