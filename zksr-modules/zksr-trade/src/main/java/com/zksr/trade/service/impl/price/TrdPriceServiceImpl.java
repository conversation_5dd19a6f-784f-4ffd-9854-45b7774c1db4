package com.zksr.trade.service.impl.price;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.vo.BranchBalanceRespVO;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.OrderPayWayEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.sysconfig.GlobalPayConfigDTO;
import com.zksr.trade.api.after.vo.OrderAfterResp;
import com.zksr.trade.api.after.vo.OrderAfterSaveRequest;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdSupplierPageVO;
import com.zksr.trade.domain.*;
import com.zksr.trade.mapper.*;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.ITrdAfterHandlerService;
import com.zksr.trade.service.impl.handler.TrdAfterHandlerServiceImpl;
import com.zksr.trade.service.impl.handler.TrdAfterHdfkHandlerServiceImpl;
import com.zksr.trade.service.price.ITrdPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.PRICE_RESERVE_2;
import static com.zksr.common.core.constant.StatusConstants.PRICE_RESERVE_6;
import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;

/**
 * <AUTHOR>
 * @date 2024年04月20日 18:23
 * @description: TrdPriceServiceImpl
 */
@Service
@Slf4j
public class TrdPriceServiceImpl implements ITrdPriceService {
    @Autowired
    private TrdSupplierOrderSettleMapper trdSupplierOrderSettleMapper;
    @Autowired
    private TrdSupplierAfterSettleMapper trdSupplierAfterSettleMapper;
    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;
    @Autowired
    private TrdSupplierAfterMapper trdSupplierAfterMapper;
    @Autowired
    private TrdSupplierAfterDtlMapper trdSupplierAfterDtlMapper;
    @Autowired
    private TrdAfterMapper trdAfterMapper;
    @Autowired
    private TrdAfterDiscountDtlMapper trdAfterDiscountDtlMapper;
    @Autowired
    private TrdOrderMapper trdOrderMapper;
    @Autowired
    private TrdHdfkPayMapper trdHdfkPayMapper;

    @Autowired
    private AccountApi accountApi;



    @Override
    public OrderAfterResp calculateAfterOrderPrice(OrderAfterSaveRequest request) {
        OrderAfterResp resp = TrdPriceCalculatorHelper.buildAfterPriceCalculateResp(request);

        resp.getSupplierOrders().stream().flatMap(supplierOrder -> supplierOrder.getSupplierOrderDtls().stream()).forEach(dtl -> {
            try {
                Assert.isTrue( dtl.getTotalNum().compareTo(dtl.getRefundQty()) >= 0,
                        "商品({}) 取消数量大于可退数量", dtl.getSupplierOrderDtlId());
            } catch (IllegalArgumentException e) {
                throw new ServiceException(e.getMessage());
            }
        });
        return resp;
    }

    @Override
    public void calculateAfterRateAmt(TrdSupplierAfterSettle trdSupplierAfterSettle) {
        // 入驻商成本金额
        trdSupplierAfterSettle.setSupplierAmt(trdSupplierAfterSettle.getCostPrice().multiply(new BigDecimal(trdSupplierAfterSettle.getReturnQty() +"")).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));
        // 退货利润
        trdSupplierAfterSettle.setProfit(trdSupplierAfterSettle.getRefundAmt().subtract(trdSupplierAfterSettle.getSupplierAmt()));
        if (ToolUtil.isNotEmpty(trdSupplierAfterSettle.getProfitModel()) && trdSupplierAfterSettle.getProfitModel().equals(StringPool.ZERO)) {
            trdSupplierAfterSettle.setProfit(
                    trdSupplierAfterSettle.getRefundAmt().multiply(trdSupplierAfterSettle.getSaleTotalRate()).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.DOWN)
            );
        }

        // 根据订单明细ID获取订单明细结算数据
        TrdSupplierOrderSettle orderSettle = trdSupplierOrderSettleMapper.selectSettleByOrderDtlId(trdSupplierAfterSettle.getSupplierOrderDtlId());

        // 根据订单明细ID获取售后订单明细结算数据
        List<TrdSupplierAfterSettle> afterSettles = trdSupplierAfterSettleMapper.selectSettleByOrderDtlId(trdSupplierAfterSettle.getSupplierOrderDtlId());
        // 已售后的软件商的分润总金额
        BigDecimal softwareAmt = BigDecimal.ZERO;
        // 已售后的平台商的分润总金额
        BigDecimal partnerAmt = BigDecimal.ZERO;
        // 已售后的运营商的分润总金额
        BigDecimal dcAmt = BigDecimal.ZERO;
        // 已售后的业务员管理员的分润总金额
        BigDecimal colonel1Amt = BigDecimal.ZERO;
        // 已售后的业务员的分润总金额
        BigDecimal colonel2Amt = BigDecimal.ZERO;
        // 已售后金额
        BigDecimal returnItemQty = BigDecimal.ZERO;
        // 已售后利润
        BigDecimal returnProfit = BigDecimal.ZERO;
        if (ToolUtil.isNotEmpty(afterSettles) && !afterSettles.isEmpty()){
            softwareAmt = afterSettles.stream().map((TrdSupplierAfterSettle::getSoftwareAmt)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            partnerAmt = afterSettles.stream().map((TrdSupplierAfterSettle::getPartnerAmt)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            dcAmt = afterSettles.stream().map((TrdSupplierAfterSettle::getDcAmt)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            colonel1Amt = afterSettles.stream().map((TrdSupplierAfterSettle::getColonel1Amt)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            colonel2Amt = afterSettles.stream().map((TrdSupplierAfterSettle::getColonel2Amt)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            returnItemQty = afterSettles.stream().map((TrdSupplierAfterSettle::getReturnQty)).reduce(BigDecimal.ZERO, BigDecimal::add);
            returnProfit = afterSettles.stream().map((TrdSupplierAfterSettle::getProfit)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        }

        BigDecimal orderItemQty = orderSettle.getItemQty();
        // 本次退货数量 + 已售后数量 == 订单下单数据 时 ， 本次的退货利润 = 订单明细总利润金额 - 之前已售后的利润金额
        if ((trdSupplierAfterSettle.getReturnQty().add(returnItemQty)).compareTo(orderItemQty) == 0) {
            trdSupplierAfterSettle.setProfit(orderSettle.getProfit().subtract(returnProfit));
        }

        // 如果商品是负毛利销售，则分成利润全都为0
        BigDecimal profit = NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, trdSupplierAfterSettle.getProfit()) ? BigDecimal.ZERO : trdSupplierAfterSettle.getProfit(); //利润总金额

        trdSupplierAfterSettle.setAllDcRate(orderSettle.getAllDcRate()); // 总运营商分润比例
        trdSupplierAfterSettle.setSettingDcRate(orderSettle.getSettingDcRate()); // 运营商设置的运营商分润比例
        trdSupplierAfterSettle.setSettingColonel1Rate(orderSettle.getSettingColonel1Rate()); // 运营商设置的业务员管理员分润比例
        trdSupplierAfterSettle.setColonel1Percentage(orderSettle.getColonel1Percentage());  // 业务员管理员提成系数
        trdSupplierAfterSettle.setSettingColonel2Rate(orderSettle.getSettingColonel2Rate()); // 运营商设置的业务员分润比例
        trdSupplierAfterSettle.setColonel2Percentage(orderSettle.getColonel2Percentage());  // 业务员提成系数




        /**
         * 计算退款的分润金额
         */
        // 软件商分润金额
        trdSupplierAfterSettle.setSoftwareAmt(trdSupplierAfterSettle.getSoftwareRate().multiply(profit).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.DOWN));
        if (NumberUtil.isGreater(trdSupplierAfterSettle.getSoftwareAmt().add(softwareAmt), orderSettle.getSoftwareAmt())
                || (trdSupplierAfterSettle.getReturnQty().add(returnItemQty)).compareTo(orderItemQty) == 0) // 当的计算的分润金额+之前已售后的分润金额 > 订单的分润总金额 时， 本次的分润金额 = 订单的分润总金额 - 之前已售后的分润金额
            trdSupplierAfterSettle.setSoftwareAmt(orderSettle.getSoftwareAmt().subtract(softwareAmt));

        // 平台商分润金额
        trdSupplierAfterSettle.setPartnerAmt(trdSupplierAfterSettle.getPartnerRate().multiply(profit).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.DOWN));
        if (NumberUtil.isGreater(trdSupplierAfterSettle.getPartnerAmt().add(partnerAmt), orderSettle.getPartnerAmt())
                || (trdSupplierAfterSettle.getReturnQty().add(returnItemQty)).compareTo(orderItemQty) == 0) // 当的计算的分润金额+之前已售后的分润金额 > 订单的分润总金额 时， 本次的分润金额 = 订单的分润总金额 - 之前已售后的分润金额
            trdSupplierAfterSettle.setPartnerAmt(orderSettle.getPartnerAmt().subtract(partnerAmt));

        //设置剩余分润金额：  剩余分润金额 =  分润总金额 -  平台商分润金额 - 软件商分润金额
        profit = profit.subtract(trdSupplierAfterSettle.getPartnerAmt()).subtract(trdSupplierAfterSettle.getSoftwareAmt());

        // 业务员管理员分润金额
        trdSupplierAfterSettle.setAllColonel1Amt(profit.multiply(trdSupplierAfterSettle.getSettingColonel1Rate())); // 满额业务员管理员分润金额
        trdSupplierAfterSettle.setColonel1Amt(trdSupplierAfterSettle.getAllColonel1Amt().multiply(trdSupplierAfterSettle.getColonel1Percentage()).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.DOWN));
        // 当前计算的分润金额+之前已售后的分润金额 > 订单的分润总金额  或者 本次退货数量 + 已售后数量 == 订单下单数据 时 ， 本次的分润金额 = 订单的分润总金额 - 之前已售后的分润金额
        if (NumberUtil.isGreater(trdSupplierAfterSettle.getColonel1Amt().add(colonel1Amt), orderSettle.getColonel1Amt())
                || (trdSupplierAfterSettle.getReturnQty().add(returnItemQty)).compareTo(orderItemQty) == 0 )
            trdSupplierAfterSettle.setColonel1Amt(orderSettle.getColonel1Amt().subtract(colonel1Amt));

        // 业务员分润金额
        trdSupplierAfterSettle.setAllColonel2Amt(profit.multiply(trdSupplierAfterSettle.getColonel2Rate())); // 满额业务员分润金额
        trdSupplierAfterSettle.setColonel2Amt(trdSupplierAfterSettle.getAllColonel2Amt().multiply(trdSupplierAfterSettle.getColonel2Percentage()).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.DOWN));
        // 当的计算的分润金额+之前已售后的分润金额 > 订单的分润总金额 或者 本次退货数量 + 已售后数量 == 订单下单数据 时， 本次的分润金额 = 订单的分润总金额 - 之前已售后的分润金额
        if (NumberUtil.isGreater(trdSupplierAfterSettle.getColonel2Amt().add(colonel2Amt), orderSettle.getColonel2Amt())
                || (trdSupplierAfterSettle.getReturnQty().add(returnItemQty)).compareTo(orderItemQty) == 0 )
            trdSupplierAfterSettle.setColonel2Amt(orderSettle.getColonel2Amt().subtract(colonel2Amt));

        profit = profit.subtract(trdSupplierAfterSettle.getColonel1Amt()).subtract(trdSupplierAfterSettle.getColonel2Amt());  // 剩余利润金额 = 平台商分润 - 业务员管理员分润 - 业务员分润
        // 运营商分润金额
        trdSupplierAfterSettle.setDcAmt(profit);
        // 当的计算的分润金额+之前已售后的分润金额 > 订单的分润总金额 或者 本次退货数量 + 已售后数量 == 订单下单数据， 本次的分润金额 = 订单的分润总金额 - 之前已售后的分润金额
        if (NumberUtil.isGreater(trdSupplierAfterSettle.getDcAmt().add(dcAmt), orderSettle.getDcAmt())
                || (trdSupplierAfterSettle.getReturnQty().add(returnItemQty)).compareTo(orderItemQty) == 0 )
            trdSupplierAfterSettle.setDcAmt(orderSettle.getDcAmt().subtract(dcAmt));
    }


    @Override
    public void recalculateOrderAmt(TrdOrder tor, List<TrdSupplierOrder> tsoList, List<TrdSupplierOrderDtl> tsodList) {
        tsoList.forEach(supplier -> {
            supplier.setSubCancelQty(BigDecimal.ZERO);
            supplier.setSubCancelAmt(BigDecimal.ZERO);
            tsodList.stream().filter((supplierDtl -> supplierDtl.getSupplierOrderId().equals(supplier.getSupplierOrderId())))
                    .forEach(supplierDtl -> {
//                        supplierDtl.setCancelAmt(supplierDtl.getPrice().multiply(new BigDecimal(supplierDtl.getCancelQty()+"")));
                        supplier.setSubCancelQty(supplier.getSubCancelQty().add(supplierDtl.getCancelQty()));
                        supplier.setSubCancelAmt(supplier.getSubCancelAmt().add(supplierDtl.getCancelAmt()));
                    });
        });
    }



    @Override
    public void recalculateAfterAmt(TrdAfter trdAfter) {
        log.error("----------------更新订单数量数据"+trdAfter.getAfterNo());
        // 查询入驻商售后订单数据
        List<TrdSupplierAfter> supplierAfters = trdSupplierAfterMapper.selectListByOrderNo(trdAfter.getAfterNo());
        // 查询入驻商售后订单明细数据
        List<TrdSupplierAfterDtl> supplierAfterDtls = trdSupplierAfterDtlMapper.selectListByAfterId(trdAfter.getAfterId());

        // 查询入驻商售后订单明细结算数据
        List<TrdSupplierAfterSettle> supplierAfterSettles = trdSupplierAfterSettleMapper.selectSettleByAfterId(trdAfter.getAfterId());
//        Map<Long, TrdSupplierAfterSettle> supplierAfterSettleMap = convertMap(supplierAfterSettles, TrdSupplierAfterSettle::getSupplierAfterDtlId);

        // 查询入驻商售后订单优惠信息
        List<TrdAfterDiscountDtl> supplierAfterDiscounts = trdAfterDiscountDtlMapper.selectListByAfterId(trdAfter.getAfterId());

        // 查询入驻商订单明细表数据
        List<TrdSupplierOrderDtl> supplierOrderDtls = trdSupplierOrderDtlMapper.selectListByOrderId(trdAfter.getOrderId());
        Map<Long, TrdSupplierOrderDtl> supplierOrderDtlMap = convertMap(supplierOrderDtls, TrdSupplierOrderDtl::getSupplierOrderDtlId);


        trdAfter.setRefundAmt(BigDecimal.ZERO);
        supplierAfters.forEach(supplier -> {
            supplier.setSubRefundAmt(BigDecimal.ZERO);
            supplier.setReturnSubOrderAmt(BigDecimal.ZERO);
            supplier.setSubDiscountAmt(BigDecimal.ZERO);

            supplierAfterDtls.stream()
                    .filter((supplierDtl -> supplierDtl.getSupplierAfterId().equals(supplier.getSupplierAfterId())))
                    .forEach(supplierAfterDtl -> {
                        /**
                         * 计算退款金额
                         */
                        supplierAfterDtl.setExactReturnAmt(supplierAfterDtl.getReturnSalesUnitPrice().multiply(new BigDecimal(supplierAfterDtl.getReturnUnitQty()+"")));   // 商品售后金额 = 售后单位单价 * 售后单位数量
                        // 退款总金额(包括之前售后的售后总金额) = 原退款总金额 - 原售后订单金额 + 本次重新计算售后订单金额
                        supplierAfterDtl.setOrderdtlRefundAmt(
                                supplierAfterDtl.getOrderdtlRefundAmt().subtract(supplierAfterDtl.getReturnAmt()).add(supplierAfterDtl.getExactReturnAmt().setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP))
                        );
//                        supplierAfterDtl.setExactReturnAmt(supplierAfterDtl.getExactReturnPrice().multiply(new BigDecimal(supplierAfterDtl.getReturnQty()+"")));
                        supplierAfterDtl.setReturnAmt(supplierAfterDtl.getExactReturnAmt().setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));
                        supplierAfterDtl.setRefundAmt(supplierAfterDtl.getReturnAmt());

                        /**
                         * 计算本地售后优惠金额信息
                         */
                        TrdSupplierOrderDtl orderDtl = supplierOrderDtlMap.get(supplierAfterDtl.getSupplierOrderDtlId());
                        // 优惠劵优惠金额 （分摊）
                        BigDecimal singleCouponDiscountAmt = orderDtl.getCouponDiscountAmt().divide(new BigDecimal(orderDtl.getTotalNum()+""), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);
                        supplierAfterDtl.setReturnCouponDiscountAmt(singleCouponDiscountAmt.multiply(new BigDecimal(supplierAfterDtl.getReturnQty()+"")).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));

                        // 优惠劵优惠金额 （不分摊）
                        BigDecimal singleCouponDiscountAmt2 = orderDtl.getCouponDiscountAmt2().divide(new BigDecimal(orderDtl.getTotalNum()+""), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);
                        supplierAfterDtl.setReturnCouponDiscountAmt2(singleCouponDiscountAmt2.multiply(new BigDecimal(supplierAfterDtl.getReturnQty()+"")).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));

                        // 活动优惠金额 （分摊）
                        BigDecimal singleActivityDiscountAmt = orderDtl.getActivityDiscountAmt().divide(new BigDecimal(orderDtl.getTotalNum()+""), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);
                        supplierAfterDtl.setReturnActivityDiscountAmt(singleActivityDiscountAmt.multiply(new BigDecimal(supplierAfterDtl.getReturnQty()+"")).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));

                        // 计算售后订单结算信息
                        supplierAfterSettles.stream().filter((afterSettle -> afterSettle.getSupplierAfterDtlId().equals(supplierAfterDtl.getSupplierAfterDtlId())))
                                        .forEach(trdSupplierAfterSettle -> {
                                            trdSupplierAfterSettle.setReturnQty(supplierAfterDtl.getReturnQty());
                                            trdSupplierAfterSettle.setReturnAmt(supplierAfterDtl.getReturnAmt());
                                            trdSupplierAfterSettle.setRefundAmt(supplierAfterDtl.getReturnAmt());
                                            calculateAfterRateAmt(trdSupplierAfterSettle);
                                        });

                        // 计算售后订单优惠信息
                        supplierAfterDiscounts.stream().filter((afterDiscount -> afterDiscount.getSupplierAfterDtlId().equals(supplierAfterDtl.getSupplierAfterDtlId())))
                                .forEach(afterDiscount -> {
                                    afterDiscount.setCouponDiscountAmt(supplierAfterDtl.getReturnCouponDiscountAmt());
                                    afterDiscount.setCouponDiscountAmt2(supplierAfterDtl.getReturnCouponDiscountAmt2());
                                    afterDiscount.setActivityDiscountAmt(supplierAfterDtl.getReturnActivityDiscountAmt());
                                });

                        supplier.setSubRefundAmt(supplier.getSubRefundAmt().add(supplierAfterDtl.getRefundAmt()));
                        supplier.setSubDiscountAmt(supplier.getSubDiscountAmt()
                                .add(supplierAfterDtl.getReturnActivityDiscountAmt())
                                .add(supplierAfterDtl.getReturnCouponDiscountAmt())
                                .add(supplierAfterDtl.getReturnCouponDiscountAmt2())
                        );
                        supplier.setReturnSubOrderAmt(supplier.getReturnSubOrderAmt().add(supplier.getSubRefundAmt()).add(supplier.getSubDiscountAmt()));
                    });
            trdAfter.setRefundAmt(trdAfter.getRefundAmt().add(supplier.getSubRefundAmt()));
            trdAfter.setReturnOrderAmt(trdAfter.getReturnOrderAmt().add(supplier.getReturnSubOrderAmt()));
            trdAfter.setReturnDiscountAmt(trdAfter.getReturnDiscountAmt().add(supplier.getSubDiscountAmt()));
        });

        // 获取订单信息
        TrdOrder tor = trdOrderMapper.selectById(trdAfter.getOrderId());
        // 获取入驻商订单列表
        List<TrdSupplierOrder> tsoList = trdSupplierOrderMapper.selectListByOrderId(trdAfter.getOrderId());


        // 重新计算退款手续费之前重置订单的已退款手续费金额
        resetOrderRefundFee(trdAfter, supplierAfters, tor, tsoList);
        // 计算售后手续费
        recalculateAfterPayFee(trdAfter, supplierAfters, supplierAfterSettles, tor, tsoList);

        trdAfterMapper.updateById(trdAfter); // 更新售后主订单
        trdSupplierAfterMapper.updateBatch(supplierAfters); // 更新售后入驻商订单
        trdSupplierAfterDtlMapper.updateBatch(supplierAfterDtls); // 更新售后入驻商订单明细
        trdSupplierAfterSettleMapper.updateBatch(supplierAfterSettles); // 更新售后入驻商订单明细结算信息
        if (ToolUtil.isNotEmpty(supplierAfterDiscounts)) {
            trdAfterDiscountDtlMapper.updateBatch(supplierAfterDiscounts); // 更新售后入驻商订单明细优惠信息
        }

        // 更新订单已退款手续费金额
        updateOrderInfo(tor, tsoList, trdAfter, supplierAfters, StringPool.PLUS);
    }


    @Override
    public void recalculateAfterPayFee(TrdAfter trdAfter, List<TrdSupplierAfter> trdSupplierAfters, List<TrdSupplierAfterSettle> trdSupplierAfterSettles, TrdOrder trdOrder, List<TrdSupplierOrder> tsoList){

        /**
         * 计算总订单的支付手续费  手续费率取源订单中的手续费率
         */
        BigDecimal payRate = trdAfter.getPayRate();
        trdAfter.setPayFee(BigDecimal.ZERO);

        // 入驻商销售订单转换MAP
        Map<String, TrdSupplierOrder> tsoMap = convertMap(tsoList, TrdSupplierOrder::getSupplierOrderNo);
        // 入驻商售后订单结算转换MAP
        Map<String, List<TrdSupplierAfterSettle>> afterSettleS = trdSupplierAfterSettles.stream().collect(Collectors.groupingBy(TrdSupplierAfterSettle::getSupplierAfterNo));

        /**
         * 计算入驻商订单的手续费
         */
        List<TrdSupplierAfter> supplierAfters = trdSupplierAfters.stream()
                .sorted(Comparator.comparing(TrdSupplierAfter::getSubRefundAmt, Comparator.nullsFirst(BigDecimal::compareTo)))
                .collect(Collectors.toList());
        supplierAfters.forEach(supplierAfter -> {
            TrdSupplierOrder tso = tsoMap.get(supplierAfter.getSupplierOrderNo());

            // 计算当前入驻商订单的退款手续费  手续费率取源订单中的手续费率
            BigDecimal subPayFee = supplierAfter.getSubRefundAmt().multiply(payRate).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);


//             1、当入驻商的已退款金额 + 当前退款金额 >= 入驻商订单金额时，
//             2、当入驻商已退款手续费金额 + 当前退款手续费金额 >= 入驻商订单手续费金额时，
//             退款手续费金额 = 入驻商订单手续费金额 - 已退款手续费金额
            if (NumberUtil.isGreaterOrEqual(supplierAfter.getSubRefundAmt().add(tso.getSubRefundAmt()), tso.getSubPayAmt())
                    || NumberUtil.isGreaterOrEqual(subPayFee.add(tso.getSubRefundFee()), tso.getSubPayFee())
            ) {
                subPayFee = tso.getSubPayFee().subtract(tso.getSubRefundFee());
            }

            // 更新入驻商售后订单退款手续费 、 退款金额
            supplierAfter.setPayRate(payRate);
            supplierAfter.setSubRefundFee(subPayFee);
            // 更新当前售后订单退款手续费
            trdAfter.setPayFee(trdAfter.getPayFee().add(supplierAfter.getSubRefundFee()));

            /**
             * 计算订单结算数据手续费
             */
            final BigDecimal[] allocationSupplierAmtCount = {BigDecimal.ZERO};  // 入驻商已扣减总金额
            AtomicInteger k = new AtomicInteger();
            if (ToolUtil.isNotEmpty(afterSettleS.get(supplierAfter.getSupplierAfterNo()))) { // 当赠品单独推时，不计算手续费金额
                afterSettleS.get(supplierAfter.getSupplierAfterNo()).forEach(settle -> {
                    k.getAndIncrement();
                    BigDecimal subDtlPayFee = BigDecimal.ZERO;  // 入驻商商品手续费金额
                    if (k.get() == afterSettleS.get(supplierAfter.getSupplierAfterNo()).size()) {
                        subDtlPayFee = supplierAfter.getSubRefundFee().subtract(allocationSupplierAmtCount[0]);   // 最后一个商品得到剩下的入驻商手续金额
                    } else {
                        subDtlPayFee = settle.getRefundAmt().multiply(payRate).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                    }
                    allocationSupplierAmtCount[0] = allocationSupplierAmtCount[0].add(subDtlPayFee);
                    settle.setPayRate(payRate);
                    settle.setRefundFee(subDtlPayFee);
                    settle.setSupplierRefundDivideAmt(settle.getRefundAmt().subtract(settle.getRefundFee()));
                });
            }
        });
    }


    @Override
    public void recalculateHdfkOrderReceiveSuccessPayFee(Long hdfkPayId, List<TrdSupplierOrderDtl> trdSupplierOrderDtlList) {
        // 货到付款单收款需根据本次收款的商品 重新计算下手续费金额
        TrdHdfkPay trdHdfkPay = trdHdfkPayMapper.selectById(hdfkPayId);
        BigDecimal payFee = trdHdfkPay.getPayFee();
        BigDecimal payRate = trdHdfkPay.getPayRate();

        /**
         * 计算订单结算数据手续费
         */
        final BigDecimal[] allocationSupplierAmtCount = {BigDecimal.ZERO};  // 入驻商已扣减总金额
        AtomicInteger k = new AtomicInteger();
        List<TrdSupplierOrderSettle> orderSettleList = trdSupplierOrderSettleMapper.selectSettleByOrderDtlIds(trdSupplierOrderDtlList.stream().map(TrdSupplierOrderDtl::getSupplierOrderDtlId).collect(Collectors.toList()));

        orderSettleList.forEach(settle -> {
                    k.getAndIncrement();
                    BigDecimal subDtlPayFee = BigDecimal.ZERO;  // 入驻商商品手续费金额
                    if (k.get() == orderSettleList.size()) {
                        subDtlPayFee = payFee.subtract(allocationSupplierAmtCount[0]);   // 最后一个商品得到剩下的入驻商手续金额
                    } else {
                        subDtlPayFee = settle.getPayAmt().multiply(payRate).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                    }
                    allocationSupplierAmtCount[0] = allocationSupplierAmtCount[0].add(subDtlPayFee);
                    settle.setPayRate(payRate);
                    settle.setPayFee(subDtlPayFee);
                    settle.setSupplierDivideAmt(settle.getPayAmt().subtract(settle.getPayFee()));
                });
        trdSupplierOrderSettleMapper.updateBatch(orderSettleList);
    }

    @Override
    public void calculatorAfterCzPayWayGiveAmt(TrdAfter after) {
        if (!Objects.equals(after.getPayWay(), OrderPayWayEnum.WALLET.getPayWay())) {
            return;
        }

        // 获取当前售后订单明细结算数据
        List<TrdSupplierAfterSettle> tsasList = trdSupplierAfterSettleMapper.selectSettleByAfterId(after.getAfterId());

        // 获取售后订单对应订单数据
        TrdOrder tor = trdOrderMapper.selectById(after.getOrderId());
        // 获取售后订单对应订单明细结算数据
        List<TrdSupplierOrderSettle> tsosList = trdSupplierOrderSettleMapper.selectListByOrderId(after.getOrderId());
        // 转换map
        Map<Long, TrdSupplierOrderSettle> supplierOrderSettleMap = convertMap(tsosList, TrdSupplierOrderSettle::getSupplierOrderDtlId);

        // 储值本金比例
        after.setReturnCzPrincipalRate(tor.getCzPrincipalRate());
        after.setReturnCzPrincipalPayAmt(BigDecimal.ZERO);
        after.setReturnCzGivePayAmt(BigDecimal.ZERO);

        // 计算商品明细 储值支付本金支付金额、储值赠金支付金额
        /**
         * 当订单储值赠金金额 小于等于 订单储值赠金已退款金额 + 当前售后订单储值赠金退款金额时，
         * ture : 当前售后订单储值赠金退款金额 = 订单储值赠金金额 - 订单储值赠金已退款金额
         * false: 当前售后订单储值赠金退款金额 = 订单储值赠金金额 - 订单储值赠金已退款金额 - 当前售后订单储值赠金退款金额
         */
        for (TrdSupplierAfterSettle tsas : tsasList) {
            // 原储值退款本金金额
            BigDecimal originalReturnCzPrincipalPayAmt = tsas.getReturnCzPrincipalPayAmt();
            // 原储值退款赠金金额
            BigDecimal originalReturnCzGivePayAmt = tsas.getReturnCzGivePayAmt();

            // 订单商品明细结算表
            TrdSupplierOrderSettle tsos = supplierOrderSettleMap.get(tsas.getSupplierOrderDtlId());

            // 储值赠金退款金额(保留两位小数) = 储值本金比例 * 售后订单商品退款金额
            BigDecimal returnCzPrincipalPayAmt = tor.getCzPrincipalRate().divide(BigDecimal.valueOf(100), StatusConstants.PRICE_RESERVE_6, RoundingMode.DOWN)
                    .multiply(tsas.getReturnAmt())
                    .setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.DOWN);
            // 储值赠金退款金额(保留两位小数)
            BigDecimal returnCzGivePayAmt = tsas.getReturnAmt().subtract(returnCzPrincipalPayAmt);
            /**
             * 当订单储值本金金额 小于等于 订单储值本金已退款金额 + 当前售后订单储值本金退款金额时，
             * ture : 当前售后订单储值本金退款金额 = 订单储值本金金额 - 订单储值本金已退款金额
             * false: 当前售后订单储值本金退款金额 = 订单储值本金比例 / 当前售后订单退款金额
             *
             * 当订单储值赠金金额 小于等于 订单储值赠金已退款金额 + 当前售后订单储值赠金退款金额时，
             * ture :
             *      当前售后订单储值赠金退款金额 = 订单储值赠金金额 - 订单储值本金赠退款金额
             *      当前售后订单储值本金退款金额 = 当前售后订单退款金额 - 当前售后订单储值赠金退款金额
             * false: 当前售后订单储值赠金退款金额 = 当前售后订单退款金额 - 当前售后订单储值本金退款金额
             */
            if (tsos.getCzPrincipalPayAmt().compareTo(tsos.getReturnCzPrincipalPayAmt().add(returnCzPrincipalPayAmt)) <= 0) {
                returnCzPrincipalPayAmt = tsos.getCzPrincipalPayAmt().subtract(tsos.getReturnCzPrincipalPayAmt());
            } else if (tsos.getCzGivePayAmt().compareTo(tsos.getReturnCzGivePayAmt().add(returnCzGivePayAmt)) <= 0) {
                returnCzGivePayAmt = tsos.getCzGivePayAmt().subtract(tsos.getReturnCzGivePayAmt());
                returnCzPrincipalPayAmt = tsas.getReturnAmt().subtract(returnCzGivePayAmt);

            }

            //  储值本金比率
            tsas.setReturnCzPrincipalRate(tor.getCzPrincipalRate())
                    //  订单商品储值本金退款金额 = 订单商品退款金额 - 订单商品储值赠金退款金额
                    .setReturnCzPrincipalPayAmt(returnCzPrincipalPayAmt)
                    // 储值赠金退款金额(保留两位小数)
                    .setReturnCzGivePayAmt(returnCzGivePayAmt)
            ;


            // 售后总订单储值赠金退款金额(保留两位小数)
            after.setReturnCzGivePayAmt(after.getReturnCzGivePayAmt().add(tsas.getReturnCzGivePayAmt()));
            // 售后总订单储值本金退款金额(保留两位小数)
            after.setReturnCzPrincipalPayAmt(after.getReturnCzPrincipalPayAmt().add(tsas.getReturnCzPrincipalPayAmt()));

            // 订单明细数据储值赠金退款金额更新
            // 订单明细数据储值本金退款金额 = 订单明细数据储值本金退款金额 + 售后订单商品储值本金退款金额 - 售后订单原明细数据储值本金退款金额
            tsos.setReturnCzPrincipalPayAmt(tsos.getReturnCzPrincipalPayAmt().add(tsas.getReturnCzPrincipalPayAmt()).subtract(originalReturnCzPrincipalPayAmt));
            // 订单明细数据储值赠金退款金额 = 订单明细数据储值赠金退款金额 + 售后订单商品储值赠金退款金额 - 售后订单原明细数据储值赠金退款金额
            tsos.setReturnCzGivePayAmt(tsos.getReturnCzGivePayAmt().add(tsas.getReturnCzGivePayAmt()).subtract(originalReturnCzGivePayAmt));
        }

        // 更新售后订单数据
        trdAfterMapper.updateById(after);
        trdSupplierAfterSettleMapper.updateBatch(tsasList);

        // 更新销售订单数据
        trdSupplierOrderSettleMapper.updateBatch(tsosList);
    }

    /**
     *  储值支付计算 本金支付金额、储值赠金支付金额
     * @param tor
     */
    @Override
    public void calculatorCzPayWayGiveAmt(TrdOrder tor, TrdSupplierPageVO pageVo) {
        if (!Objects.equals(PayChannelEnum.WALLET.getCode(), pageVo.getStoreOrderPayPlatform())) {
            return;
        }
        // 获取订单明细结算数据
        List<TrdSupplierOrderSettle> tsosList = trdSupplierOrderSettleMapper.selectListByOrderId(tor.getOrderId());
        // 获取门店储值账户余额
        BranchBalanceRespVO branchBalanceRespVO = accountApi.getBranchBalance(tor.getBranchId()).getCheckedData();

        // 计算总订单 储值支付本金支付金额、储值赠金支付金额
        // 订单使用储值本金比例
        tor.setCzPrincipalRate(branchBalanceRespVO.getBalanceRate());
        // 储值本金支付金额(保留两位小数) = 储值本金比例 * 订单支付金额
        tor.setCzPrincipalPayAmt(
                branchBalanceRespVO.getBalanceRate().divide(BigDecimal.valueOf(100), StatusConstants.PRICE_RESERVE_6, RoundingMode.DOWN)
                        .multiply(tor.getPayAmt())
                        .setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.DOWN)
        );
        // 储值赠金支付金额(保留两位小数) = 订单支付金额 - 储值本金支付金额
        tor.setCzGivePayAmt(tor.getPayAmt().subtract(tor.getCzPrincipalPayAmt()));

        BigDecimal shareDectedCzGiveAmt = BigDecimal.ZERO;
        // 计算商品明细 储值支付本金支付金额、储值赠金支付金额
        for (int i = 0; i < tsosList.size(); i++) {
            TrdSupplierOrderSettle tsos = tsosList.get(i);
            //  储值本金比率
            tsos.setCzPrincipalRate(tor.getCzPrincipalRate());
            //  明细储值本金支付金额 = 订单明细商品金额 / 订单金额 * 总订单储值本金支付金额
            tsos.setCzPrincipalPayAmt(
                    tsos.getItemAmt().divide(tor.getPayAmt(), PRICE_RESERVE_6, RoundingMode.DOWN)
                            .multiply(tor.getCzPrincipalPayAmt())
                            .setScale(PRICE_RESERVE_2, RoundingMode.DOWN)
            );
            // 储值赠金支付金额
            tsos.setCzGivePayAmt(tsos.getItemAmt().subtract(tsos.getCzPrincipalPayAmt()));


            /**
             * 校验总储值赠金支付金额是否 小于等于 储值赠金累计已分摊金额 + 当前商品储值赠金支付金额
             */
            boolean isTrue = shareDectedCzGiveAmt.add(tsos.getCzGivePayAmt()).compareTo(tor.getCzGivePayAmt()) >= 0;

            // 当 isTrue 为 true 或者 当前订单明细为最后一个时， 当前商品储值赠金支付金额 = 储值赠金累计已分摊金额 + 当前商品储值赠金支付金额 - 总储值赠金支付金额
            if (isTrue || i == tsosList.size() - 1) {
                // 当前商品储值赠金支付金额 = 总储值赠金支付金额 - 储值赠金累计已分摊金额
                tsos.setCzGivePayAmt(tor.getCzGivePayAmt().subtract(shareDectedCzGiveAmt));
                // 储值本金支付金额 = 商品金额 - 储值赠金支付金额
                tsos.setCzPrincipalPayAmt(tsos.getItemAmt().subtract(tsos.getCzGivePayAmt()));
            }
            shareDectedCzGiveAmt = shareDectedCzGiveAmt.add(tsos.getCzGivePayAmt());
        }
        // 更新数据
        trdOrderMapper.updateById(tor);
        trdSupplierOrderSettleMapper.updateBatch(tsosList);
    }


    @Override
    public void updateOrderInfo(TrdOrder tor, List<TrdSupplierOrder> tsoList, TrdAfter tar, List<TrdSupplierAfter> tsaList, String operateType) {
        // 操作类型 返还值为 1 或者 -1
        BigDecimal bigDecimalGround = Objects.equals(StringPool.PLUS, operateType) ? BigDecimal.ONE : BigDecimal.ONE.negate();

        // 更新订单退款金额
        tor.setRefundPayFee(tor.getRefundPayFee().add(tar.getPayFee().multiply(bigDecimalGround)));
        tor.setRefundPayAmt(tor.getRefundPayAmt().add(tar.getRefundAmt().multiply(bigDecimalGround)));

        Map<String, TrdSupplierAfter> tsaMap = convertMap(tsaList, TrdSupplierAfter::getSupplierOrderNo);
        // 更新入驻商订单手续费
        tsoList.stream()
                .filter(tso -> tsaMap.containsKey(tso.getSupplierOrderNo()))
                .forEach(tso -> {
            TrdSupplierAfter tsa = tsaMap.get(tso.getSupplierOrderNo());
            tso.setSubRefundAmt(tso.getSubRefundAmt().add(tsa.getSubRefundAmt().multiply(bigDecimalGround)));
            tso.setSubRefundFee(tso.getSubRefundFee().add(tsa.getSubRefundFee().multiply(bigDecimalGround)));
        });
        trdOrderMapper.updateById(tor);
        trdSupplierOrderMapper.updateBatch(tsoList);
    }



    // 重新计算退款手续费之前重置订单的已退款手续费金额
    private void resetOrderRefundFee (TrdAfter tar, List<TrdSupplierAfter> tsaList, TrdOrder tor, List<TrdSupplierOrder> tsoList) {

        // 订单退款手续费 = 订单退款手续费 - 售后退款手续费
        tor.setRefundPayFee(tor.getRefundPayFee().subtract(tar.getPayFee()));
        // 订单退款金额 = 订单退款金额 - 售后退款金额
        tor.setRefundPayAmt(tor.getRefundPayAmt().subtract(tar.getRefundAmt()));

        // 入驻商销售订单转换MAP
        Map<String, TrdSupplierOrder> tsoMap = convertMap(tsoList, TrdSupplierOrder::getSupplierOrderNo);
        tsaList.forEach(tsa -> {
            TrdSupplierOrder tso = tsoMap.get(tsa.getSupplierOrderNo());

            // 入驻商订单退款手续费 = 入驻商订单退款手续费 - 售后退款手续费
            tso.setSubRefundFee(tso.getSubRefundFee().subtract(tsa.getSubRefundFee()));
            // 入驻商订单退款金额 = 入驻商订单退款金额 - 售后退款金额
            tso.setSubRefundAmt(tso.getSubRefundAmt().subtract(tsa.getSubRefundAmt()));
        });
    }
}
