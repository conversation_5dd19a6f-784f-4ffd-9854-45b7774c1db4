package com.zksr.trade.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdOrderShareRespVO;
import com.zksr.trade.mapper.TrdOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdOrderShareMapper;
import com.zksr.trade.convert.order.TrdOrderShareConvert;
import com.zksr.trade.domain.TrdOrderShare;
import com.zksr.trade.controller.order.vo.TrdOrderSharePageReqVO;
import com.zksr.trade.api.order.vo.TrdOrderShareSaveReqVO;
import com.zksr.trade.service.ITrdOrderShareService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.TRD_ORDER_SHARE_MAX;
import static com.zksr.trade.enums.ErrorCodeConstants.TRD_ORDER_SHARE_ROLE;

/**
 * 订单分享Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class TrdOrderShareServiceImpl implements ITrdOrderShareService {

    @Autowired
    private TrdOrderShareMapper trdOrderShareMapper;

    @Autowired
    private TrdOrderMapper orderMapper;

    /**
     * 新增订单分享
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public String insertTrdOrderShare(TrdOrderShareSaveReqVO createReqVO) {
        // 验证订单
        TrdOrder trdOrder = orderMapper.selectById(createReqVO.getOrderId());
        if (!trdOrder.getBranchId().equals(createReqVO.getBranchId())) {
            throw exception(TRD_ORDER_SHARE_ROLE);
        }
        Long cnt = trdOrderShareMapper.selectValidCnt(createReqVO.getOrderId());
        if (cnt > 20L) {
            throw exception(TRD_ORDER_SHARE_MAX);
        }

        // 插入
        TrdOrderShare trdOrderShare = TrdOrderShareConvert.INSTANCE.convert(createReqVO);
        // 有效期20天, 20天后过期
        trdOrderShare.setExpirationTime(DateUtil.offsetDay(DateUtil.date(), 15));
        trdOrderShare.setShareKey(RandomUtil.randomString(48));
        trdOrderShare.setSysCode(trdOrder.getSysCode());
        trdOrderShareMapper.insert(trdOrderShare);
        // 返回
        return trdOrderShare.getShareKey();
    }

    /**
     * 修改订单分享
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdOrderShare(TrdOrderShareSaveReqVO updateReqVO) {
        trdOrderShareMapper.updateById(TrdOrderShareConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除订单分享
     *
     * @param shareOrderId ${pkColumn.columnComment}
     */
    @Override
    public void deleteTrdOrderShare(Long shareOrderId) {
        // 删除
        trdOrderShareMapper.deleteById(shareOrderId);
    }

    /**
     * 批量删除订单分享
     *
     * @param shareOrderIds 需要删除的订单分享主键
     * @return 结果
     */
    @Override
    public void deleteTrdOrderShareByShareOrderIds(Long[] shareOrderIds) {
        for(Long shareOrderId : shareOrderIds){
            this.deleteTrdOrderShare(shareOrderId);
        }
    }

    /**
     * 获得订单分享
     *
     * @param shareOrderId ${pkColumn.columnComment}
     * @return 订单分享
     */
    @Override
    public TrdOrderShare getTrdOrderShare(Long shareOrderId) {
        return trdOrderShareMapper.selectById(shareOrderId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdOrderShare> getTrdOrderSharePage(TrdOrderSharePageReqVO pageReqVO) {
        return trdOrderShareMapper.selectPage(pageReqVO);
    }

    @Override
    public TrdOrderShareRespVO getTrdOrderShare(String shareKey) {
        return TrdOrderShareConvert.INSTANCE.convert(trdOrderShareMapper.selectByShareKey(shareKey));
    }

}
