package com.zksr.trade.service.impl.handler.activity;

import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.domain.TrdAfterDiscountDtl;
import com.zksr.trade.domain.TrdOrderDiscountDtl;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.service.handler.activity.ITrdOrderActivityHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *  订单优惠活动处理器 ———— 买赠促销
 */
@Service
@Slf4j
public class TrdOrderActivityBgHandlerServiceImpl implements ITrdOrderActivityHandlerService {
    @Autowired
    private RedisActivityService redisActivityService;
    @Override
    public void orderCancelReturnActivity(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {
        // 返还买赠活动库存
        toddList.stream()
                .filter(orderDiscount -> Objects.equals(TrdDiscountTypeEnum.BG.getType(), orderDiscount.getDiscountType()))
                .forEach(orderDiscount -> {
                    // 返回买赠活动库存
                    BigDecimal saleQty = BigDecimal.valueOf(orderDiscount.getGiftQty()).multiply(NumberPool.BIGDECIMAL_GROUND);
                    redisActivityService.setBgSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), saleQty, null, null);
                });
    }

    @Override
    public void afterCancelReturnActivity(TrdOrder tor, List<TrdSupplierOrderDtl> tsodList, List<TrdOrderDiscountDtl> toddList,
                                          TrdAfter tar, List<TrdSupplierAfterDtl> tsadList, List<TrdAfterDiscountDtl> taddList) {
        taddList.stream()
                .filter(tadd -> Objects.equals(TrdDiscountTypeEnum.BG.getType(), tadd.getDiscountType()))
                .forEach(tadd -> {
                    // 返回买赠活动库存
                    BigDecimal returnQty = tadd.getGiftQty().multiply(NumberPool.BIGDECIMAL_GROUND);

                    redisActivityService.setBgSaleNum(tadd.getDiscountId(), tadd.getDiscountRuleId(), returnQty, null, null);
                });
    }

    @Override
    public void restoreCancelledOrderDiscounts(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {
        // 恢复已取消订单的买赠活动库存
        toddList.stream()
                .filter(orderDiscount -> Objects.equals(TrdDiscountTypeEnum.BG.getType(), orderDiscount.getDiscountType()))
                .forEach(orderDiscount -> {
                    BigDecimal saleQty = BigDecimal.valueOf(orderDiscount.getGiftQty());
                    redisActivityService.setBgSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), saleQty, null, null);
                });
    }
}
