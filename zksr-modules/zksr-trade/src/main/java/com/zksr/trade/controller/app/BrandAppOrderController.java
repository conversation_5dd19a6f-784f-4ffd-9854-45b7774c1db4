package com.zksr.trade.controller.app;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.controller.app.vo.BrandHomePageRespVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListInfoRespVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListReqVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListRespVO;
import com.zksr.trade.service.TradeBrandAppOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 品牌商app订单查询
 * @date 2024/8/6 11:21
 */
@Api(tags = "品牌商APP - 订单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/brandAppOrder")
public class BrandAppOrderController {

    @Autowired
    private TradeBrandAppOrderService brandAppOrderService;

    /**
     * 获取品牌商首页统计数据
     * @return
     */
    @ApiOperation(value = "获取首页数据", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER)
    @RequiresPermissions(Permissions.ORDER)
    @GetMapping("/getHomePage")
    public CommonResult<BrandHomePageRespVO> getHomePage() {
        return success(brandAppOrderService.getBrandHomePage());
    }

    /**
     * 获取首页销售数据列表
     * @return
     */
    @ApiOperation(value = "获取首页销售数据列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER)
    @RequiresPermissions(Permissions.ORDER)
    @GetMapping("/getHomeSaleList")
    public CommonResult<List<BrandHomeSaleListRespVO>> getHomeSaleList(@Valid BrandHomeSaleListReqVO reqVO) {
        return success(brandAppOrderService.getHomeSaleList(reqVO));
    }

    /**
     * 获取某个品牌销售详情
     * @return
     */
    @ApiOperation(value = "获取某个品牌销售详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ORDER)
    @RequiresPermissions(Permissions.ORDER)
    @GetMapping("/getHomeSaleListInfo")
    public CommonResult<List<BrandHomeSaleListInfoRespVO>> getHomeSaleListInfo(@Valid BrandHomeSaleListReqVO reqVO) {
        if (Objects.isNull(reqVO.getBrandId())) {
            return success(null);
        }
        return success(brandAppOrderService.getHomeSaleListInfo(reqVO));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ORDER = "trade:brandAppOrder:data";
    }
}
