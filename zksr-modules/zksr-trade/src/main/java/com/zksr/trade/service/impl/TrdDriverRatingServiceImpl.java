package com.zksr.trade.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.member.MemberApi;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingRespVO;
import com.zksr.trade.service.ITrdSupplierOrderService;
import com.zksr.trade.service.TrdCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdDriverRatingMapper;
import com.zksr.trade.convert.rating.TrdDriverRatingConvert;
import com.zksr.trade.domain.TrdDriverRating;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingPageReqVO;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingSaveReqVO;
import com.zksr.trade.service.ITrdDriverRatingService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 司机评分Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@Service
public class TrdDriverRatingServiceImpl implements ITrdDriverRatingService {

    @Autowired
    private TrdDriverRatingMapper trdDriverRatingMapper;

    @Autowired
    private TrdCacheService trdCacheService;



    /**
     * 新增司机评分
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdDriverRating(TrdDriverRatingSaveReqVO createReqVO) {
        // 插入
        TrdDriverRating trdDriverRating = TrdDriverRatingConvert.INSTANCE.convert(createReqVO);
        trdDriverRatingMapper.insert(trdDriverRating);
        // 返回
        return trdDriverRating.getDriverRatingId();
    }

    /**
     * 修改司机评分
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdDriverRating(TrdDriverRatingSaveReqVO updateReqVO) {
        trdDriverRatingMapper.updateById(TrdDriverRatingConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除司机评分
     *
     * @param driverRatingId 司机评分
     */
    @Override
    public void deleteTrdDriverRating(Long driverRatingId) {
        // 删除
        trdDriverRatingMapper.deleteById(driverRatingId);
    }

    /**
     * 批量删除司机评分
     *
     * @param driverRatingIds 需要删除的司机评分主键
     * @return 结果
     */
    @Override
    public void deleteTrdDriverRatingByDriverRatingIds(Long[] driverRatingIds) {
        for (Long driverRatingId : driverRatingIds) {
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deleteTrdDriverRating(driverRatingId);
        }
    }

    /**
     * 获得司机评分
     *
     * @param driverRatingId 司机评分
     * @return 司机评分
     */
    @Override
    public TrdDriverRatingRespVO getTrdDriverRating(Long driverRatingId) {
        return trdDriverRatingMapper.selectDriverRatingById(driverRatingId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<TrdDriverRatingRespVO> getTrdDriverRatingPage(TrdDriverRatingPageReqVO pageReqVO) {
        Page<TrdDriverRatingPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<TrdDriverRatingRespVO> list = trdDriverRatingMapper.selectDriverRatingList(pageReqVO, page);

        list.getRecords().forEach(item -> {

            //填充评价人名称
            if (ToolUtil.isNotEmpty(trdCacheService.getMemberDTO(item.getMemberId()))) {
                item.setMemberName(trdCacheService.getMemberDTO(item.getMemberId()).getMemberName());
            }

            //填充门店名称
            if (ToolUtil.isNotEmpty(trdCacheService.getBranchDTO(item.getBranchId()))) {
                item.setBranchName(trdCacheService.getBranchDTO(item.getBranchId()).getBranchName());
            }
            // 计算司机综合评分，并保留两位小数
            BigDecimal slot1Score = BigDecimal.valueOf(item.getSlot1ScoreCode());
            BigDecimal slot2Score = BigDecimal.valueOf(item.getSlot2Score());
            BigDecimal slot3Score = BigDecimal.valueOf(item.getSlot3Score());

            BigDecimal totalScore = slot1Score.add(slot2Score).add(slot3Score)
                    .divide(BigDecimal.valueOf(3), 2, RoundingMode.HALF_UP);

            item.setTotalScore(totalScore);

        });
        return new PageResult<>(list.getRecords(), list.getTotal());
    }

/*    private void validateTrdDriverRatingExists(Long driverRatingId) {
        if (trdDriverRatingMapper.selectById(driverRatingId) == null) {
            throw exception(TRD_DRIVER_RATING_NOT_EXISTS);
        }
    }*/

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 司机评分 TODO 补充编号 ==========
    // ErrorCode TRD_DRIVER_RATING_NOT_EXISTS = new ErrorCode(TODO 补充编号, "司机评分不存在");


}
