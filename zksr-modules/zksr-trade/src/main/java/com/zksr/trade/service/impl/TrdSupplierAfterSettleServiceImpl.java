package com.zksr.trade.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdSupplierAfterSettleMapper;
import com.zksr.trade.convert.after.TrdSupplierAfterSettleConvert;
import com.zksr.trade.domain.TrdSupplierAfterSettle;
import com.zksr.trade.controller.after.settleVo.TrdSupplierAfterSettlePageReqVO;
import com.zksr.trade.controller.after.settleVo.TrdSupplierAfterSettleSaveReqVO;
import com.zksr.trade.service.ITrdSupplierAfterSettleService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 售后结算信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Service
public class TrdSupplierAfterSettleServiceImpl implements ITrdSupplierAfterSettleService {
    @Autowired
    private TrdSupplierAfterSettleMapper trdSupplierAfterSettleMapper;

    /**
     * 新增售后结算信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdSupplierAfterSettle(TrdSupplierAfterSettleSaveReqVO createReqVO) {
        // 插入
        TrdSupplierAfterSettle trdSupplierAfterSettle = TrdSupplierAfterSettleConvert.INSTANCE.convert(createReqVO);
        trdSupplierAfterSettleMapper.insert(trdSupplierAfterSettle);
        // 返回
        return trdSupplierAfterSettle.getAfterSettleId();
    }

    /**
     * 修改售后结算信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdSupplierAfterSettle(TrdSupplierAfterSettleSaveReqVO updateReqVO) {
        trdSupplierAfterSettleMapper.updateById(TrdSupplierAfterSettleConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除售后结算信息
     *
     * @param afterSettleId 售后结算信息id;售后结算信息id
     */
    @Override
    public void deleteTrdSupplierAfterSettle(Long afterSettleId) {
        // 删除
        trdSupplierAfterSettleMapper.deleteById(afterSettleId);
    }

    /**
     * 批量删除售后结算信息
     *
     * @param afterSettleIds 需要删除的售后结算信息主键
     * @return 结果
     */
    @Override
    public void deleteTrdSupplierAfterSettleByAfterSettleIds(Long[] afterSettleIds) {
        for(Long afterSettleId : afterSettleIds){
            this.deleteTrdSupplierAfterSettle(afterSettleId);
        }
    }

    /**
     * 获得售后结算信息
     *
     * @param afterSettleId 售后结算信息id;售后结算信息id
     * @return 售后结算信息
     */
    @Override
    public TrdSupplierAfterSettle getTrdSupplierAfterSettle(Long afterSettleId) {
        return trdSupplierAfterSettleMapper.selectById(afterSettleId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdSupplierAfterSettle> getTrdSupplierAfterSettlePage(TrdSupplierAfterSettlePageReqVO pageReqVO) {
        return trdSupplierAfterSettleMapper.selectPage(pageReqVO);
    }

    private void validateTrdSupplierAfterSettleExists(Long afterSettleId) {
        if (trdSupplierAfterSettleMapper.selectById(afterSettleId) == null) {
            throw exception(TRD_SUPPLIER_AFTER_SETTLE_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 售后结算信息 TODO 补充编号 ==========
    // ErrorCode TRD_SUPPLIER_AFTER_SETTLE_NOT_EXISTS = new ErrorCode(TODO 补充编号, "售后结算信息不存在");


}
