package com.zksr.trade.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.trade.api.after.dto.AfterSupplierRespDTO;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.dto.OrderReceiptRespDTO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdSupplierAfter;
import com.zksr.trade.controller.after.supplierVo.TrdSupplierAfterPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 入驻商售后单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Mapper
public interface TrdSupplierAfterMapper extends BaseMapperX<TrdSupplierAfter> {
    default PageResult<TrdSupplierAfter> selectPage(TrdSupplierAfterPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdSupplierAfter>()
                    .eqIfPresent(TrdSupplierAfter::getSupplierAfterId, reqVO.getSupplierAfterId())
                    .eqIfPresent(TrdSupplierAfter::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdSupplierAfter::getSupplierAfterNo, reqVO.getSupplierAfterNo())
                    .eqIfPresent(TrdSupplierAfter::getSupplierOrderNo, reqVO.getSupplierOrderNo())
                    .eqIfPresent(TrdSupplierAfter::getOrderId, reqVO.getOrderId())
                    .eqIfPresent(TrdSupplierAfter::getOrderNo, reqVO.getOrderNo())
                    .eqIfPresent(TrdSupplierAfter::getAfterId, reqVO.getAfterId())
                    .eqIfPresent(TrdSupplierAfter::getAfterNo, reqVO.getAfterNo())
                    .eqIfPresent(TrdSupplierAfter::getTransAmt, reqVO.getTransAmt())
                    .eqIfPresent(TrdSupplierAfter::getSupplierId, reqVO.getSupplierId())
                    .eqIfPresent(TrdSupplierAfter::getSubDiscountAmt, reqVO.getSubDiscountAmt())
                    .eqIfPresent(TrdSupplierAfter::getSubRefundAmt, reqVO.getSubRefundAmt())
                .orderByDesc(TrdSupplierAfter::getSupplierAfterId));
    }

    /**
     * 根据售后订单ID查询售后入驻商订单和明细信息
     * @param afterIds
     * @return
     */
    List<AfterSupplierRespDTO> getSupplierAfterByAfterId (@Param("afterIds") List<Long> afterIds);

    /**
     * 根据售后订单ID 和 入驻商Id 获取售后入驻商订单信息
     * @param afterId 售后订单ID
     * @param supplierId 入驻商Id
     * @return
     */
    default TrdSupplierAfter selectByAfterIdAndSupplierId(Long afterId, Long supplierId) {
        return selectOne(new LambdaQueryWrapperX<TrdSupplierAfter>()
                .eqIfPresent(TrdSupplierAfter::getAfterId, afterId)
                .eqIfPresent(TrdSupplierAfter::getSupplierId, supplierId));
    }

    /**
     * 根据入驻商售后订单号 获取入驻商售后订单信息
     * @param supplierAfterNo
     * @return
     */
    default TrdSupplierAfter selectAfterBySupplierAfterNo(String supplierAfterNo){
        return selectOne(new LambdaQueryWrapper<TrdSupplierAfter>().eq(TrdSupplierAfter::getSupplierAfterNo,supplierAfterNo));
    }

    default  List<TrdSupplierAfter> selectList(TrdSupplierAfterPageReqVO reqVO){
        return selectList(new LambdaQueryWrapperX<TrdSupplierAfter>()
                .eqIfPresent(TrdSupplierAfter::getSupplierAfterNo,reqVO.getOrderNo()));
    }

    /**
     * 根据入驻商订单编号和 入驻商Id 订单类型  获取售后入驻商订单信息
     *
     * @param supplierOrderNo 入驻商订单编号
     * @param supplierId      入驻商Id
     * @param transNos
     * @return
     */
    default TrdSupplierAfter selectAfterBySupplierOrderNoAndSupplierIdAndTransNo(String supplierOrderNo,Long supplierId,List<String> transNos) {
        return selectOne(new LambdaQueryWrapperX<TrdSupplierAfter>()
                .eqIfPresent(TrdSupplierAfter::getSupplierOrderNo, supplierOrderNo)
                .eqIfPresent(TrdSupplierAfter::getSupplierId, supplierId)
                .in(TrdSupplierAfter::getTransNo, transNos));
    }

    default List<TrdSupplierAfter> selectListByOrderNo(String AfterNo){
        return selectList(new LambdaQueryWrapperX<TrdSupplierAfter>()
                .eqIfPresent(TrdSupplierAfter::getAfterNo,AfterNo));
    }

    default TrdSupplierAfter selectOneBySupAfterNo(String supplierAfterNo){
        return selectOne(new LambdaQueryWrapper<TrdSupplierAfter>().eq(TrdSupplierAfter::getSupplierAfterNo,supplierAfterNo));
    }


    /**
     * 根据供应商售后订单号查询退款信息
     * @param reqVo
     * @return
     */
    List<OrderReceiptRespDTO> getOrderReceiptInfoBySupplierAfterNo(SyncReceiptSendDTO reqVo);

    /**
     * 更新售后订单同步状态
     * @param supplierAfterNo
     * @param pushStatus
     */
    default void updateByPushStatus(String supplierAfterNo, Integer pushStatus){
        update(null,new LambdaUpdateWrapper<TrdSupplierAfter>().eq(TrdSupplierAfter::getSupplierAfterNo, supplierAfterNo)
                .set(TrdSupplierAfter::getPushStatus,pushStatus));//1代表已经推送订单到其他系统
    }
}
