package com.zksr.trade.service.impl.handler.payWay;

import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.domain.TrdSettle;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierAfterSettle;
import com.zksr.trade.service.handler.payWay.ITrdAfterPayWayHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 通用支付方式处理实现
 */
@Service
@Slf4j
@Order(ITrdAfterPayWayHandlerService.ORDER_NONE_ONLINE)
public class TrdAfterPayWayHandlerServiceimpl implements ITrdAfterPayWayHandlerService {

    @Override
    public Boolean isPlatform(String platform, String payWay) {
        return Objects.equals(PayChannelEnum.NONE.getCode(), platform) && Objects.equals("100", payWay);
    }

    @Override
    public Boolean isPlatform(String platform) {
        return Objects.equals(PayChannelEnum.NONE.getCode(), platform);
    }

    @Override
    public void afterMerchantSettlement(Long supplierId,
                                        TrdAfter after,
                                        PlatformMerchantDTO merchantDTO,
                                        Map<Long, TrdSupplierAfterSettle> supplierAfterSettleMap,
                                        List<TrdSettle> afterSettleList,
                                        List<TrdSupplierAfterDtl> supplierAfterDtlList,
                                        PayRefundOrderSubmitReqVO refundReqVo) {
        String supplierAccountNo = supplierId + "";
        // 分账信息  获取当前支付方式 属性【是否支持在线分账】值
        if (PayChannelEnum.getPayOnlineSupportDivide(after.getPlatform())) {
            supplierAccountNo = merchantDTO.getAltMchNo();
        }

        OrderSettlementDTO orderSettlementDTO = OrderSettlementDTO.builder()
                .accountNo(supplierAccountNo)
                .merchantId(supplierId)
                .merchantType(MerchantTypeEnum.SUPPLIER.getType())
                .amt(BigDecimal.ZERO)
                .build();

        supplierAfterDtlList.forEach(afterDtl -> {
            TrdSupplierAfterSettle afterSettle = supplierAfterSettleMap.get(afterDtl.getSupplierAfterDtlId());
            orderSettlementDTO.setAmt(orderSettlementDTO.getAmt().add(afterSettle.getSupplierRefundDivideAmt()));
            refundReqVo.setRefundAmt(afterSettle.getRefundAmt().add(refundReqVo.getRefundAmt()));
        });
        refundReqVo.getSettlements().add(orderSettlementDTO);
    }




}
