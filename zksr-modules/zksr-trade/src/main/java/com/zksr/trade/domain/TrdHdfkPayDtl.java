package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 货到付款付款单明细对象 trd_hdfk_pay_dtl
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@TableName(value = "trd_hdfk_pay_dtl")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdHdfkPayDtl extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 货到付款付款单id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long hdfkPayDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 付款单号 */
    @Excel(name = "付款单号")
    private String payNo;

    /** 付款单id */
    @Excel(name = "付款单id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long hdfkPayId;

    /** 门店id */
    @Excel(name = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 支付金额；实际支付金额 */
    @Excel(name = "支付金额；实际支付金额")
    private BigDecimal payAmt;

    /** 结算金额 */
    @Excel(name = "结算金额")
    private BigDecimal settleAmt;

    /** 支付公司收取的支付费率 */
    @Excel(name = "支付公司收取的支付费率")
    private BigDecimal payRate;

    /** 支付平台手续费；(pay_amt*pay_rate) 四舍五入 */
    @Excel(name = "支付平台手续费；(pay_amt*pay_rate) 四舍五入")
    private BigDecimal payFee;

    /** 分账金额 */
    @Excel(name = "分账金额")
    private BigDecimal divideAmt;

    /** 货到付款结算ID */
    @Excel(name = "货到付款结算ID")
    private Long hdfkSettleId;

}
