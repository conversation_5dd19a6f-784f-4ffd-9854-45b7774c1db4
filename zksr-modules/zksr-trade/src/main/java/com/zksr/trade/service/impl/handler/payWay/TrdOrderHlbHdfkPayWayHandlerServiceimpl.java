package com.zksr.trade.service.impl.handler.payWay;

import com.alibaba.fastjson2.JSON;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.pay.PayApi;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.account.model.pay.vo.PayWxB2bCreateDivideReqVO;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.OrderPayWayEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveRespVO;
import com.zksr.trade.api.order.dto.OrderPayInfoRespDTO;
import com.zksr.trade.api.order.dto.TrdSupplierResDto;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdSupplierPageVO;
import com.zksr.trade.domain.TrdHdfkPay;
import com.zksr.trade.domain.TrdSettle;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.mapper.TrdHdfkPayMapper;
import com.zksr.trade.mapper.TrdSettleMapper;
import com.zksr.trade.mapper.TrdSupplierOrderDtlMapper;
import com.zksr.trade.service.ITrdSettleService;
import com.zksr.trade.service.handler.payWay.ITrdOrderPayWayHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 合利宝支付方式处理器 - 货到付款
 */
@Service
@Slf4j
@Order(ITrdOrderPayWayHandlerService.ORDER_HLB_HDFK)
public class TrdOrderHlbHdfkPayWayHandlerServiceimpl implements ITrdOrderPayWayHandlerService {

    @Autowired
    private TrdSettleMapper trdSettleMapper;
    @Autowired
    private TrdHdfkPayMapper trdHdfkPayMapper;

    @Autowired
    private ITrdSettleService trdSettleService;

    @Autowired
    private PayApi payApi;
    @Resource
    private PlatformMerchantApi platformMerchantApi;

    @Override
    public Boolean isPlatform(String platform, String payWay) {
        return Objects.equals(PayChannelEnum.HLB.getCode(), platform) && Objects.equals(OrderPayWayEnum.ONLINE.getPayWay(), payWay);
    }

    @Override
    public Boolean isPlatform(String platform) {
        return Objects.equals(PayChannelEnum.HLB.getCode(), platform);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderCompleteCreateSettleTransfer(TrdOrder tor, List<TrdSupplierOrder> tsoList, List<TrdSupplierOrderDtl> tsodList, List<TrdSettle> settleList) {
        // 商品明细按照货到付款单id进行分组
        tsodList.stream().collect(Collectors.groupingBy(orderDtl -> ToolUtil.isEmptyReturn(orderDtl.getHdfkPayId(), NumberPool.LOWER_GROUND_LONG)))
                .forEach((hdfkPayId, list) -> {
                    // 过滤掉没有货到收款单的订单商品数据
                    if (hdfkPayId > NumberPool.LONG_ZERO) {
                        // 验证 货到付款单 是否已全部收货
                        boolean isAllComplete = list.stream().allMatch(dtl -> Objects.equals(dtl.getDeliveryState(), DeliveryStatusEnum.COMPLETE.getCode()));
                        if (isAllComplete) { // 货到付款单 全部商品已收货

                            TrdHdfkPay hdfkPay = trdHdfkPayMapper.selectById(hdfkPayId);
                            if (ToolUtil.isEmpty(hdfkPay) || hdfkPay.getPayState() == NumberPool.INT_ZERO) {
                                log.info("发送货到付款订单【{}】，货到收款ID【{}】 未查询到货到收款单或该收款单为支付！", tor.getOrderNo(), hdfkPayId);
                                return;
                            }

                            // 查询订单结算信息
                            List<TrdSettle> trdSettles = trdSettleMapper.getSettleBySupplierOrderDtlId(list.stream().map(TrdSupplierOrderDtl::getSupplierOrderDtlId).collect(Collectors.toList()));
                            trdSettles.stream().collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
                                    .forEach((merchantId, settles) -> {
                                        boolean isComplete = settleList.stream()
                                                .anyMatch(dtl -> Objects.equals(dtl.getState(), StatusConstants.SETTLE_STATE_2));
                                        if (isComplete) {
                                            log.info("货到付款订单【{}】，B2B微信分润账户【{}】已请求分账，无需重复处理！", tor.getOrderNo(), merchantId);
                                            return;
                                        }

                                        PayWxB2bCreateDivideReqVO reqVO =
                                                new PayWxB2bCreateDivideReqVO(hdfkPay.getHdfkPayNo(), settleList.get(NumberPool.INT_ZERO).getMerchantId(), settleList.get(NumberPool.INT_ZERO).getMerchantType());
                                        // 发送分润账户分账请求
                                        log.info("发送货到付款订单【{}】，货到收款单号【{}】，发送B2B微信分润账户分账请求:{}", tor.getOrderNo(), hdfkPay.getHdfkPayNo(), JSON.toJSONString(reqVO));
                                        CreateDivideRespVO respVO = payApi.divide(reqVO).getCheckedData();
                                        if (respVO.isSuccess()) {
                                            settleList.forEach(settle -> {
                                                settle.setState(StatusConstants.SETTLE_STATE_2); // 更新结算状态未结算中
                                            });
                                        }
                                        log.info("发送货到付款订单【{}】，货到收款单号【{}】，发送B2B微信分润账户分账请求返回结果:{}", hdfkPay.getHdfkPayNo(), hdfkPay.getHdfkPayNo(), JSON.toJSONString(respVO));

                                        trdSettleMapper.updateBatch(settleList);
                                    });
                        }
                    }
                });
    }

    @Override
    public void orderHdfkSettleAccountInfo(AccAccountDTO accountDTO, Long supplierId, BigDecimal supplierAmt, BigDecimal itemFree, List<TrdSettle> settles, PayConfigDTO payConfigDTO, List<HdfkPaySaveRespVO.OrderSettlementDTO> settlementDTOS) {
        HdfkPaySaveRespVO.OrderSettlementDTO orderSttlementDTO =new HdfkPaySaveRespVO.OrderSettlementDTO(
                accountDTO.getPlatformAccount().getAltMchNo(),
                supplierAmt.subtract(itemFree),
                supplierId,
                MerchantTypeEnum.SUPPLIER.getType()
        );
        settlementDTOS.add(orderSttlementDTO);

        // 兼容合并订单结算分账信息 过滤掉结算为0 的数据
        if (Objects.nonNull(payConfigDTO)) {
            settles.stream()
                    .filter(settle -> Objects.equals(supplierId, settle.getSupplierId()))
                    .collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
                    .forEach((merchant, merchantSettleList) -> {
                        // 商户Id
                        Long merchantId = merchantSettleList.get(NumberPool.INT_ZERO).getMerchantId();
                        // 商户类型
                        String merchantType = merchantSettleList.get(NumberPool.INT_ZERO).getMerchantType();

                        // 获取支付配置
                        PlatformMerchantDTO merchantDTO = platformMerchantApi.getPlatformMerchant(merchantType, merchantId, PayChannelEnum.HLB.getCode()).getCheckedData();

                        BigDecimal settleAmt = merchantSettleList.stream().map(TrdSettle::getSettleAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        HdfkPaySaveRespVO.OrderSettlementDTO orderSttlement =new HdfkPaySaveRespVO.OrderSettlementDTO(
                                ToolUtil.isEmpty(merchantDTO) ? null : merchantDTO.getAltMchNo(), // 这里第三方商户号未查询到默认为null
                                settleAmt,
                                merchantId,
                                merchantType
                        );
                        if (settleAmt.compareTo(BigDecimal.ZERO) > NumberPool.INT_ZERO) {
                            settlementDTOS.add(orderSttlement);
                            // 入驻商分账金额 - 分账方金额
                            orderSttlementDTO.setAmt(orderSttlementDTO.getAmt().subtract(settleAmt));
                        }
                    });
        }
    }
}
