package com.zksr.trade.service.handler;

import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.vo.TrdPayOrderPageVO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.domain.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024年04月2日 19:54
 * @description: 订单活动特殊逻辑处理器 handler 接口
 * 提供订单生命周期钩子接口；订单支付后、订单取消、订单发货、订单收货、订单完成
 */
@Component
public interface ITrdOrderHandlerService {

    /**
     * 线上订单（在线支付）
     */
    int ORDER_ONLINE = 1;
    /**
     * 线上订单（货到付款）
     */
    int ORDER_HDFK = 2;
    /**
     * 线上订单 发送消息通知
     */
    int ORDER_SUBSCRIBE = 3;

    /**
     * 订单支付方式：微信B2B支付
     */
    int ORDER_B2B_WX_PLATFORM = 4;
    /**
     * 订单支付方式：货到付款 微信B2B支付
     */
    int ORDER_HDFK_B2B_WX_PLATFORM = 5;


    /**
     * 订单支付回调
     *
     * @param pageVo 订单
     */
    default void orderPay(TrdPayOrderPageVO pageVo, TrdOrder order) {}

    /**
     * 订单支付回调之后操作
     *
     * @param order 订单
     */
    default void afterOrderPay(TrdOrder order) {}



    /**
     * 订单取消
     *
     * @param supplierOrder
     */
    default void orderCancel(TrdSupplierOrder supplierOrder) {}


    /**
     * 订单发货（本地商品）
     *
     * @param orderVo
     */
    default void orderOutbound(TrdOrder orderVo, Long supplierId) {}

    /**
     * 订单装车
     *
     * @param orderVo
     */
    default void orderEntrucking(TrdOrder orderVo, Long supplierId) {}

    /**
     * 订单取消装车
     *
     * @param orderVo
     */
    default void orderCancelEntrucking(TrdOrder orderVo, Long supplierId) {}

    /**
     * 订单收货
     *
     * @param orderDtl
     */
    default void orderTakeDelivery(TrdSupplierOrderDtl orderDtl) {}

    /**
     * 订单收货完成, 按单, 最大范围一个trd_order, 同一个trd_order的订单详情
     * @param orderDtls 订单详情
     */
    default void orderTakeDeliveryFinish(List<TrdSupplierOrderDtl> orderDtls) {};

    /**
     * 订单完成
     *
     * @param orderDtl
     */
    default void orderComplete(TrdSupplierOrderDtl orderDtl) {}

    /**
     * 订单备货
     *
     * @param orderDtl
     */
    default void orderPreareGoods(TrdSupplierOrderDtl orderDtl) {}

    /**
     * 订单发货（全国商品）
     *
     * @param orderDtl
     */
    default void orderOutbound(TrdSupplierOrderDtl orderDtl) {}

    /**
     * 货到付款支付成功更新订单明细支付状态
     *
     * @param orderDtl
     */
    default void hdfkOrderDtlPaySuccessUpdateStatus(TrdSupplierOrderDtl orderDtl) {}

    /**
     * 货到付款支付成功验证订单是否已经全部付款，若已全部付款，则将订单状态更改成 货到付款已付款
     *
     * @param orderDtlList
     */
    default void hdfkOrderPaySuccessUpdateStatus(List<TrdSupplierOrderDtl> orderDtlList) {}


    /**
     *  订单售后 使用活动已购数量返还 (发货前取消)
     * @param disoucntDtl 售后订单优惠信息
     * @param tar 售后主订单
     * @param supplierAfterDtlMap 售后订单商品明细详情
     * @param orderDiscountDtlList 售后订单对应的入驻商销售订单优惠数据详情
     */
    default void returnAfterActivitySaleQty(List<TrdAfterDiscountDtl> disoucntDtl, TrdAfter tar, Map<Long, TrdSupplierAfterDtl> supplierAfterDtlMap, List<TrdOrderDiscountDtl> orderDiscountDtlList){}


    /**
     * 订单收货发送优惠劵
     * @param orderId
     * @param supplierOrderIds
     */
    default void orderTakeDeliveryReceiveCoupon(Long orderId, Set<Long> supplierOrderIds){}

    /**
     * 处理已取消订单的支付成功回调逻辑
     * 1、处理已回滚库存的 增加已售库存操作
     * 2、处理已回滚优惠信息库存的 增加活动已售库存操作
     * @param tor
     */
    default void handleCancelledOrderPaymentSuccess(TrdOrder tor) {}
}
