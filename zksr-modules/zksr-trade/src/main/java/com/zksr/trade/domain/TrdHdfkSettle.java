package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 货到付款结算对象 trd_hdfk_settle
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@TableName(value = "trd_hdfk_settle")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdHdfkSettle extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 订单结算id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long hdfkSettleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 门店id */
    @Excel(name = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 订单id */
    @Excel(name = "订单id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long orderId;

    /** 全国商品订单编号 */
    @Excel(name = "全国商品订单编号")
    private String supplierOrderNo;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierOrderId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierOrderDtlId;

    /** 售后编号;仅售后写 */
    @Excel(name = "售后编号;仅售后写")
    private String supplierAfterDtlNo;

    /** 售后单id;仅售后写 */
    @Excel(name = "售后单id;仅售后写")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierAfterDtlId;

    /** 0-订单  1-售后 */
    @Excel(name = "0-订单  1-售后")
    private Integer settleType;

    /** 结算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结算时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date settleTime;

    /** 结算状态 0=未结算，1=已结算 */
    @Excel(name = "结算状态 0=未结算，1=已结算")
    private Integer state;

    /** 应结算金额 */
    @Excel(name = "应结算金额")
    private BigDecimal settleAmt;

    /** 货到付款付款单号;生成付款单后反写 */
    @Excel(name = "货到付款付款单号;生成付款单后反写")
    private String hdfkPayNo;

    /** 货到付款付款单明细;生成付款单后反写 */
    @Excel(name = "货到付款付款单明细;生成付款单后反写")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long fdfkPayDtlId;

}
