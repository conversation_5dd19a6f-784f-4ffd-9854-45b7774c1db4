package com.zksr.trade.controller.after.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @date 2024年04月22日 15:00
 * @description: AfterOrderPageRespDTO
 */
@ApiModel("售后单 - trd_after分页 Response DTO")
@Data
public class AfterPageRespDTO {

    @ApiModelProperty(value = "售后单ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private  Long afterId;

    @ApiModelProperty(value = "售后单号")
    private  String afterNo;

    @ApiModelProperty(value = "入驻商售后单号")
    private  String supplierAfterNo;

    @ApiModelProperty(value = "订单单号")
    private  String orderNo;

    @ApiModelProperty(value = "商品类型")
    private  Long productType;

    @ApiModelProperty(value = "售后类型")
    private  Long afterType;

    @ApiModelProperty(value = "售后阶段")
    private  Long afterPhase;

    @ApiModelProperty(value = "审核状态")
    private  Long approveState;

    @ApiModelProperty(value = "退货状态")
    private  Long returnState;

    @ApiModelProperty(value = "退款状态")
    private  Long refundState;

    @ApiModelProperty(value = "处理状态处理中状态{0：(处理中状态全部)，1：待处理， 2：未退货，3：退货中，4：已退货，5：待退款，6：退款中，7：退款失败, 8:已处理（完成），9：已拒绝}")
    private  Long handleState;

    @ApiModelProperty(value = "售后单申请时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date afterCreateTime;

    @ApiModelProperty(value = "订单创建时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private  Date orderCreateTime;

    @ApiModelProperty(value = "订单支付时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date orderPayTime;

    @ApiModelProperty(value = "付款方式（数据字典KEY：sys_pay_way）")
    private  Long payWay;

    @ApiModelProperty(value = "退货数量")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "退货金额")
    private  BigDecimal returnAmt;

    @ApiModelProperty(value = "门店Id")
    private Long branchId;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店地址")
    private String branchAddress;

    @ApiModelProperty(value = "入驻商售后订单外部订单号")
    private String sourceOrderNo;

    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "门店手机号")
    private String branchPhone;



}
