package com.zksr.trade.controller.orderLog.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 订单日志对象 trd_order_log
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@ApiModel("订单日志 - trd_order_log分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdOrderLogPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    @ApiModelProperty(value = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    @ApiModelProperty(value = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 操作前状态 */
    @Excel(name = "操作前状态")
    @ApiModelProperty(value = "操作前状态")
    private Long beforeState;

    /** 操作后状态 */
    @Excel(name = "操作后状态")
    @ApiModelProperty(value = "操作后状态")
    private Long afterState;

    /** 操作类型(数据字典) */
    @Excel(name = "操作类型(数据字典)")
    @ApiModelProperty(value = "操作类型(数据字典)")
    private Long operateType;

    /** 订单日志信息 */
    @Excel(name = "订单日志信息")
    @ApiModelProperty(value = "订单日志信息")
    private String content;


}
