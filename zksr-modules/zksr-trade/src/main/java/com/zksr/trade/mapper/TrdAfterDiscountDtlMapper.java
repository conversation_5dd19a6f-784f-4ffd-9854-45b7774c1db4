package com.zksr.trade.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.trade.domain.TrdAfterDiscountDtl;

import java.util.List;


/**
 * 售后优惠明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
@Mapper
public interface TrdAfterDiscountDtlMapper extends BaseMapperX<TrdAfterDiscountDtl> {

    /**
     *  根据售后订单ID查询售后结算信息
     * @param afterId
     * @return
     */
    default List<TrdAfterDiscountDtl> selectListByAfterId(Long afterId) {
        return selectList(new LambdaQueryWrapperX<TrdAfterDiscountDtl>()
                .inIfPresent(TrdAfterDiscountDtl::getAfterId, afterId));
    }

}
