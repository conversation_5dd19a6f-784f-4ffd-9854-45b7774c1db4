package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.domain.TrdSupplierAfterSettle;
import com.zksr.trade.controller.after.settleVo.TrdSupplierAfterSettlePageReqVO;
import com.zksr.trade.controller.after.settleVo.TrdSupplierAfterSettleSaveReqVO;

/**
 * 售后结算信息Service接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
public interface ITrdSupplierAfterSettleService {

    /**
     * 新增售后结算信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdSupplierAfterSettle(@Valid TrdSupplierAfterSettleSaveReqVO createReqVO);

    /**
     * 修改售后结算信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdSupplierAfterSettle(@Valid TrdSupplierAfterSettleSaveReqVO updateReqVO);

    /**
     * 删除售后结算信息
     *
     * @param afterSettleId 售后结算信息id;售后结算信息id
     */
    public void deleteTrdSupplierAfterSettle(Long afterSettleId);

    /**
     * 批量删除售后结算信息
     *
     * @param afterSettleIds 需要删除的售后结算信息主键集合
     * @return 结果
     */
    public void deleteTrdSupplierAfterSettleByAfterSettleIds(Long[] afterSettleIds);

    /**
     * 获得售后结算信息
     *
     * @param afterSettleId 售后结算信息id;售后结算信息id
     * @return 售后结算信息
     */
    public TrdSupplierAfterSettle getTrdSupplierAfterSettle(Long afterSettleId);

    /**
     * 获得售后结算信息分页
     *
     * @param pageReqVO 分页查询
     * @return 售后结算信息分页
     */
    PageResult<TrdSupplierAfterSettle> getTrdSupplierAfterSettlePage(TrdSupplierAfterSettlePageReqVO pageReqVO);

}
