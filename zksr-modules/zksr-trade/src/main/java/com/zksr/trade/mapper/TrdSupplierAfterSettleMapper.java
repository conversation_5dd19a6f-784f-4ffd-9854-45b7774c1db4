package com.zksr.trade.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.trade.domain.TrdSupplierOrderSettle;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdSupplierAfterSettle;
import com.zksr.trade.controller.after.settleVo.TrdSupplierAfterSettlePageReqVO;

import java.util.List;


/**
 * 售后结算信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Mapper
public interface TrdSupplierAfterSettleMapper extends BaseMapperX<TrdSupplierAfterSettle> {
    default PageResult<TrdSupplierAfterSettle> selectPage(TrdSupplierAfterSettlePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdSupplierAfterSettle>()
                    .eqIfPresent(TrdSupplierAfterSettle::getAfterSettleId, reqVO.getAfterSettleId())
                    .eqIfPresent(TrdSupplierAfterSettle::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdSupplierAfterSettle::getAfterId, reqVO.getAfterId())
                    .eqIfPresent(TrdSupplierAfterSettle::getAfterNo, reqVO.getAfterNo())
                    .eqIfPresent(TrdSupplierAfterSettle::getSupplierOrderDtlNo, reqVO.getSupplierOrderDtlNo())
                    .eqIfPresent(TrdSupplierAfterSettle::getSupplierOrderDtlId, reqVO.getSupplierOrderDtlId())
                    .eqIfPresent(TrdSupplierAfterSettle::getReturnQty, reqVO.getReturnQty())
                    .eqIfPresent(TrdSupplierAfterSettle::getReturnPrice, reqVO.getReturnPrice())
                    .eqIfPresent(TrdSupplierAfterSettle::getReturnAmt, reqVO.getReturnAmt())
                    .eqIfPresent(TrdSupplierAfterSettle::getTransAmt, reqVO.getTransAmt())
                    .eqIfPresent(TrdSupplierAfterSettle::getSupplierAmt, reqVO.getSupplierAmt())
                    .eqIfPresent(TrdSupplierAfterSettle::getSoftwareAmt, reqVO.getSoftwareAmt())
                    .eqIfPresent(TrdSupplierAfterSettle::getProfit, reqVO.getProfit())
                    .eqIfPresent(TrdSupplierAfterSettle::getCostPrice, reqVO.getCostPrice())
                    .eqIfPresent(TrdSupplierAfterSettle::getDcRate, reqVO.getDcRate())
                    .eqIfPresent(TrdSupplierAfterSettle::getDcAmt, reqVO.getDcAmt())
                    .eqIfPresent(TrdSupplierAfterSettle::getPartnerRate, reqVO.getPartnerRate())
                    .eqIfPresent(TrdSupplierAfterSettle::getPartnerAmt, reqVO.getPartnerAmt())
                    .eqIfPresent(TrdSupplierAfterSettle::getColonel1Rate, reqVO.getColonel1Rate())
                    .eqIfPresent(TrdSupplierAfterSettle::getColonel1Amt, reqVO.getColonel1Amt())
                    .eqIfPresent(TrdSupplierAfterSettle::getColonel2Rate, reqVO.getColonel2Rate())
                    .eqIfPresent(TrdSupplierAfterSettle::getColonel2Amt, reqVO.getColonel2Amt())
                    .eqIfPresent(TrdSupplierAfterSettle::getRefundAmt, reqVO.getRefundAmt())
                    .eqIfPresent(TrdSupplierAfterSettle::getPayRate, reqVO.getPayRate())
                    .eqIfPresent(TrdSupplierAfterSettle::getRefundFee, reqVO.getRefundFee())
                    .eqIfPresent(TrdSupplierAfterSettle::getSupplierRefundDivideAmt, reqVO.getSupplierRefundDivideAmt())
                .orderByDesc(TrdSupplierAfterSettle::getAfterSettleId));
    }

    /**
     * 根据入驻商订单明细ID 获取订单售后结算信息
     * @param orderDtlId 明细ID
     * @return
     */
    default List<TrdSupplierAfterSettle> selectSettleByOrderDtlId(Long orderDtlId) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierAfterSettle>()
                .eqIfPresent(TrdSupplierAfterSettle::getSupplierOrderDtlId, orderDtlId));
    }

    /**
     * 根据入驻商售后订单明细ID 获取订单售后结算信息
     * @param afterDtlId 明细ID
     * @return
     */
    default List<TrdSupplierAfterSettle> selectSettleByAfterDtlId(List<Long> afterDtlId) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierAfterSettle>()
                .inIfPresent(TrdSupplierAfterSettle::getSupplierAfterDtlId, afterDtlId));
    }

    /**
     *  根据售后订单ID查询售后结算信息
     * @param afterId
     * @return
     */
    default List<TrdSupplierAfterSettle> selectSettleByAfterId(Long afterId) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierAfterSettle>()
                .inIfPresent(TrdSupplierAfterSettle::getAfterId, afterId));
    }

    /**
     * 根据入驻商售后订单明细ID 获取订单售后结算信息
     * @param afterDtlId 明细ID
     * @return
     */
    default TrdSupplierAfterSettle selectByAfterDtlId(Long afterDtlId) {
        return selectOne(new LambdaQueryWrapperX<TrdSupplierAfterSettle>()
                .inIfPresent(TrdSupplierAfterSettle::getSupplierAfterDtlId, afterDtlId));
    }
}
