package com.zksr.trade.controller.hdfk;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdHdfkPayDtl;
import com.zksr.trade.service.ITrdHdfkPayDtlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayDtlPageReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayDtlSaveReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkPayDtlRespVO;
import com.zksr.trade.convert.hdfk.TrdHdfkPayDtlConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 货到付款付款单明细Controller
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Api(tags = "管理后台 - 货到付款付款单明细接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/hdfkPayDtl")
public class TrdHdfkPayDtlController {
    @Autowired
    private ITrdHdfkPayDtlService trdHdfkPayDtlService;

    /**
     * 新增货到付款付款单明细
     */
    @ApiOperation(value = "新增货到付款付款单明细", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "货到付款付款单明细", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdHdfkPayDtlSaveReqVO createReqVO) {
        return success(trdHdfkPayDtlService.insertTrdHdfkPayDtl(createReqVO));
    }

    /**
     * 修改货到付款付款单明细
     */
    @ApiOperation(value = "修改货到付款付款单明细", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "货到付款付款单明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdHdfkPayDtlSaveReqVO updateReqVO) {
            trdHdfkPayDtlService.updateTrdHdfkPayDtl(updateReqVO);
        return success(true);
    }

    /**
     * 删除货到付款付款单明细
     */
    @ApiOperation(value = "删除货到付款付款单明细", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "货到付款付款单明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{hdfkPayDtlIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] hdfkPayDtlIds) {
        trdHdfkPayDtlService.deleteTrdHdfkPayDtlByHdfkPayDtlIds(hdfkPayDtlIds);
        return success(true);
    }

    /**
     * 获取货到付款付款单明细详细信息
     */
    @ApiOperation(value = "获得货到付款付款单明细详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{hdfkPayDtlId}")
    public CommonResult<TrdHdfkPayDtlRespVO> getInfo(@PathVariable("hdfkPayDtlId") Long hdfkPayDtlId) {
        TrdHdfkPayDtl trdHdfkPayDtl = trdHdfkPayDtlService.getTrdHdfkPayDtl(hdfkPayDtlId);
        return success(TrdHdfkPayDtlConvert.INSTANCE.convert(trdHdfkPayDtl));
    }

    /**
     * 分页查询货到付款付款单明细
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得货到付款付款单明细分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdHdfkPayDtlRespVO>> getPage(@Valid TrdHdfkPayDtlPageReqVO pageReqVO) {
        PageResult<TrdHdfkPayDtl> pageResult = trdHdfkPayDtlService.getTrdHdfkPayDtlPage(pageReqVO);
        return success(TrdHdfkPayDtlConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:hdfk-pay-dtl:add";
        /** 编辑 */
        public static final String EDIT = "trade:hdfk-pay-dtl:edit";
        /** 删除 */
        public static final String DELETE = "trade:hdfk-pay-dtl:remove";
        /** 列表 */
        public static final String LIST = "trade:hdfk-pay-dtl:list";
        /** 查询 */
        public static final String GET = "trade:hdfk-pay-dtl:query";
        /** 停用 */
        public static final String DISABLE = "trade:hdfk-pay-dtl:disable";
        /** 启用 */
        public static final String ENABLE = "trade:hdfk-pay-dtl:enable";
    }
}
