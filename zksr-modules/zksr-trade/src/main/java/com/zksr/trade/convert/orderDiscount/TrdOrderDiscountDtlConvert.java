package com.zksr.trade.convert.orderDiscount;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.trade.api.order.dto.TrdOrderDiscountDTO;
import com.zksr.trade.api.order.vo.TrdOrderDiscountDtlSaveVO;
import com.zksr.trade.domain.TrdOrderDiscountDtl;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 订单优惠明细 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-14
*/
@Mapper
public interface TrdOrderDiscountDtlConvert {

    TrdOrderDiscountDtlConvert INSTANCE = Mappers.getMapper(TrdOrderDiscountDtlConvert.class);

    TrdOrderDiscountDtl convert(TrdOrderDiscountDtlSaveVO trdOrderDiscountDtlSaveVO);

    TrdOrderDiscountDTO convert1(CouponTemplateDTO couponTemplateDTO);
}