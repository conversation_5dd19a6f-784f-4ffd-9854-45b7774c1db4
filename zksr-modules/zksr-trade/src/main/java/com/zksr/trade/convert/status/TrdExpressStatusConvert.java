package com.zksr.trade.convert.status;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.controller.status.vo.TrdExpressStatusRespVO;
import com.zksr.trade.controller.status.vo.TrdExpressStatusSaveReqVO;
import com.zksr.trade.domain.TrdExpressStatus;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

;

/**
* 物流状态（ERP-&gt;B2B） 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-06-04
*/
@Mapper
public interface TrdExpressStatusConvert {

    TrdExpressStatusConvert INSTANCE = Mappers.getMapper(TrdExpressStatusConvert.class);

    TrdExpressStatusRespVO convert(TrdExpressStatus trdExpressStatus);

    TrdExpressStatus convert(TrdExpressStatusSaveReqVO trdExpressStatusSaveReq);

    PageResult<TrdExpressStatusRespVO> convertPage(PageResult<TrdExpressStatus> trdExpressStatusPage);
}