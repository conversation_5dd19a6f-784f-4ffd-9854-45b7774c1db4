package com.zksr.trade.controller.orderSupplier.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 入驻商订单对象 trd_supplier_order
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Data
@ApiModel("入驻商订单 - trd_supplier_order分页 Request VO")
public class TrdSupplierOrderSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 入驻商订单id */
    @ApiModelProperty(value = "入驻商订单id")
    private Long supplierOrderId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    /** 订单id */
    @Excel(name = "订单id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 配送费 */
    @Excel(name = "配送费")
    @ApiModelProperty(value = "配送费")
    private BigDecimal transAmt;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消 3-货到付款未收款 4-货到付款已收款*/
    @Excel(name = "支付状态", readConverterExp = "数=据字典sys_pay_state")
    private Integer payState;

    /** 司机ID */
    @Excel(name = "司机ID")
    private Long driverId;

    /** 司机评价状态 (0-未评价, 1-已评价) */
    @Excel(name = "司机评价状态")
    private Integer driverRatingFlag;

    /** 司机评价ID */
    @Excel(name = "司机评价ID")
    private Long driverRatingId;

    /**
     * 入驻商单据明细集合
     */
    private List<TrdSupplierOrderDtlSaveReqVO> supplierOrderDtlSaveReqVoS;

}
