package com.zksr.trade.controller.invoice.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 发票主对象 trd_invoice_info
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@ApiModel("发票主 - trd_invoice_info分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdInvoiceInfoPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "是否删除：0-否，1-是")
    private Long id;

    /** 上游传入唯一流水号 */
    @Excel(name = "上游传入唯一流水号")
    @ApiModelProperty(value = "上游传入唯一流水号", required = true)
    private String bizId;

    /** 主业务单号 */
    @Excel(name = "主业务单号")
    @ApiModelProperty(value = "主业务单号")
    private String businessNo;

    /** 交易类型：1=蓝票，2=红票 */
    @Excel(name = "交易类型：1=蓝票，2=红票")
    @ApiModelProperty(value = "交易类型：1=蓝票，2=红票", required = true)
    private Long transType;

    /** 发票类型：1-电子普票 2-电子专票 3-纸质普票 4-纸质专票 */
    @Excel(name = "发票类型：1-电子普票 2-电子专票 3-纸质普票 4-纸质专票")
    @ApiModelProperty(value = "发票类型：1-电子普票 2-电子专票 3-纸质普票 4-纸质专票", required = true)
    private Long invoiceType;

    /** 发票代码 */
    @Excel(name = "发票代码")
    @ApiModelProperty(value = "发票代码")
    private String invoiceCode;

    /** 发票号码 */
    @Excel(name = "发票号码")
    @ApiModelProperty(value = "发票号码")
    private String invoiceNo;

    /** 开票日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "开票日期", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "开票日期")
    private Date invoiceDate;

    /** 发票校验码 */
    @Excel(name = "发票校验码")
    @ApiModelProperty(value = "发票校验码")
    private String checkCode;

    /** 发票抬头：1=个人，2=企业 */
    @Excel(name = "发票抬头：1=个人，2=企业")
    @ApiModelProperty(value = "发票抬头：1=个人，2=企业")
    private Long invoiceHead;

    /** 发票含税金额 */
    @Excel(name = "发票含税金额")
    @ApiModelProperty(value = "发票含税金额")
    private BigDecimal amtContainTax;

    /** 发票不含税金额 */
    @Excel(name = "发票不含税金额")
    @ApiModelProperty(value = "发票不含税金额")
    private BigDecimal amtNotContainTax;

    /** 蓝票发票号码（红票关联的蓝票号） */
    @Excel(name = "蓝票发票号码", readConverterExp = "红=票关联的蓝票号")
    @ApiModelProperty(value = "蓝票发票号码")
    private String blueInvoiceNo;

    /** 开票渠道 */
    @Excel(name = "开票渠道")
    @ApiModelProperty(value = "开票渠道")
    private String requestChannel;

    /** 开票状态：3=开票成功，5=开票失败，8=已作废，10=二次附件回调 */
    @Excel(name = "开票状态：3=开票成功，5=开票失败，8=已作废，10=二次附件回调")
    @ApiModelProperty(value = "开票状态：3=开票成功，5=开票失败，8=已作废，10=二次附件回调", required = true)
    private Long status;

    /** 开票失败原因 */
    @Excel(name = "开票失败原因")
    @ApiModelProperty(value = "开票失败原因")
    private String failReason;

    /** 业务系统在智汇票的标识 */
    @Excel(name = "业务系统在智汇票的标识")
    @ApiModelProperty(value = "业务系统在智汇票的标识")
    private Long sysSource;

    /** 发票PDF下载URL */
    @Excel(name = "发票PDF下载URL")
    @ApiModelProperty(value = "发票PDF下载URL")
    private String pdfUrl;

    /** 作废发票的发票号码（JSON数组格式） */
    @Excel(name = "作废发票的发票号码", readConverterExp = "J=SON数组格式")
    @ApiModelProperty(value = "作废发票的发票号码")
    private String voidInvoiceNos;

    /** 作废红票对应的蓝票发票号码（JSON数组格式） */
    @Excel(name = "作废红票对应的蓝票发票号码", readConverterExp = "J=SON数组格式")
    @ApiModelProperty(value = "作废红票对应的蓝票发票号码")
    private String voidBlueInvoiceNos;

    /** 回调时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "回调时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "回调时间")
    private Date callbackTime;

    /** 附件二次回调时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "附件二次回调时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "附件二次回调时间")
    private Date attachmentCallbackTime;

    /** 是否删除：0-否，1-是 */
    @Excel(name = "是否删除：0-否，1-是")
    @ApiModelProperty(value = "是否删除：0-否，1-是", required = true)
    private Integer isDeleted;


}
