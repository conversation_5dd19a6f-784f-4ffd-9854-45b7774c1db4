package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 物流状态（ERP-&gt;B2B）对象 trd_express_status
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@TableName(value = "trd_express_status")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdExpressStatus extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 订单号(入驻商订单编号) */
    @Excel(name = "订单号")
    @ApiModelProperty(value = "订单号")
    private String supplierOrderNo;

    /** 物流状态（1待出库、2待配送、3配送中、4已配送（暂弃用）、5已收货）  */
    @Excel(name = "物流状态", readConverterExp = "订单物流状态（1待出库、2待配送、3配送中、4已配送（暂弃用）、5已收货） 售后物流状态（1、门店发起售后  2、商家已同意 11、待确认收货 12、已确认收货 13、已入库 21、商家同意退款 22、退款成功）） ")
    @ApiModelProperty(value = "物流状态")
    private Integer logisticsStatus;

    /** 顺序标识字段 */
    @Excel(name = "顺序标识字段")
    private Long sort;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注（司机+ 电话）")
    private String memo;

    /** 售后物流状态 */
    @Excel(name = "物流状态信息")
    @ApiModelProperty(value = "物流状态信息")
    private String logisticsStatusInfo;

    /** 外部订单号 */
    @Excel(name = "外部(ERP)单号(非必填)")
    @ApiModelProperty(value = "外部(ERP)单号(非必填)")
    private String sourceOrderNo;

    /** 开始时间 */
    @Excel(name = "开始时间")
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date startTime;



    public TrdExpressStatus(String supplierOrderNo, Integer logisticsStatus) {
        this.supplierOrderNo = supplierOrderNo;
        this.logisticsStatus = logisticsStatus;
    }

    public TrdExpressStatus(String supplierOrderNo, Integer logisticsStatus, Long sysCode) {
        this.supplierOrderNo = supplierOrderNo;
        this.logisticsStatus = logisticsStatus;
        this.sysCode = sysCode;
    }
}
