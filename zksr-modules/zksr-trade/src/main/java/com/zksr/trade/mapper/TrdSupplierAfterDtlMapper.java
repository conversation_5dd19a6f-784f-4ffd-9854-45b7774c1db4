package com.zksr.trade.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.domain.erp.dto.AfterDetailOpenDto;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.trade.api.after.dto.OrderAfterDtlResDTO;
import com.zksr.trade.controller.order.vo.DcSupplierOrderAfterDtlRespVO;
import com.zksr.trade.domain.TrdSupplierAfterSettle;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.controller.after.supplierDtlVo.TrdSupplierAfterDtlPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 售后单明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Mapper
public interface TrdSupplierAfterDtlMapper extends BaseMapperX<TrdSupplierAfterDtl> {
    default PageResult<TrdSupplierAfterDtl> selectPage(TrdSupplierAfterDtlPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdSupplierAfterDtl>()
                    .eqIfPresent(TrdSupplierAfterDtl::getSupplierAfterDtlId, reqVO.getSupplierAfterDtlId())
                    .eqIfPresent(TrdSupplierAfterDtl::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdSupplierAfterDtl::getAfterNo, reqVO.getAfterNo())
                    .eqIfPresent(TrdSupplierAfterDtl::getAfterId, reqVO.getAfterId())
                    .eqIfPresent(TrdSupplierAfterDtl::getSupplierAfterDtlNo, reqVO.getSupplierAfterDtlNo())
                    .eqIfPresent(TrdSupplierAfterDtl::getSupplierAfterId, reqVO.getSupplierAfterId())
                    .eqIfPresent(TrdSupplierAfterDtl::getSupplierAfterNo, reqVO.getSupplierAfterNo())
                    .eqIfPresent(TrdSupplierAfterDtl::getSupplierOrderDtlId, reqVO.getSupplierOrderDtlId())
                    .eqIfPresent(TrdSupplierAfterDtl::getSupplierOrderDtlNo, reqVO.getSupplierOrderDtlNo())
                    .eqIfPresent(TrdSupplierAfterDtl::getSupplierId, reqVO.getSupplierId())
                    .eqIfPresent(TrdSupplierAfterDtl::getItemType, reqVO.getItemType())
                    .eqIfPresent(TrdSupplierAfterDtl::getItemId, reqVO.getItemId())
                    .eqIfPresent(TrdSupplierAfterDtl::getSpuId, reqVO.getSpuId())
                    .eqIfPresent(TrdSupplierAfterDtl::getSkuId, reqVO.getSkuId())
                    .eqIfPresent(TrdSupplierAfterDtl::getReason, reqVO.getReason())
                    .eqIfPresent(TrdSupplierAfterDtl::getReturnQty, reqVO.getReturnQty())
                    .eqIfPresent(TrdSupplierAfterDtl::getReturnPrice, reqVO.getReturnPrice())
                    .eqIfPresent(TrdSupplierAfterDtl::getReturnAmt, reqVO.getReturnAmt())
                    .eqIfPresent(TrdSupplierAfterDtl::getRefundAmt, reqVO.getRefundAmt())
                    .eqIfPresent(TrdSupplierAfterDtl::getDescr, reqVO.getDescr())
                    .eqIfPresent(TrdSupplierAfterDtl::getAfterType, reqVO.getAfterType())
                    .eqIfPresent(TrdSupplierAfterDtl::getAfterPhase, reqVO.getAfterPhase())
                    .eqIfPresent(TrdSupplierAfterDtl::getRefundType, reqVO.getRefundType())
                    .eqIfPresent(TrdSupplierAfterDtl::getApproveState, reqVO.getApproveState())
                    .eqIfPresent(TrdSupplierAfterDtl::getReturnState, reqVO.getReturnState())
                    .eqIfPresent(TrdSupplierAfterDtl::getRefundState, reqVO.getRefundState())
                    .eqIfPresent(TrdSupplierAfterDtl::getOrderdtlRefundAmt, reqVO.getOrderdtlRefundAmt())
                    .eqIfPresent(TrdSupplierAfterDtl::getPayway, reqVO.getPayway())
                    .eqIfPresent(TrdSupplierAfterDtl::getPlatform, reqVO.getPlatform())
                    .eqIfPresent(TrdSupplierAfterDtl::getPayTime, reqVO.getPayTime())
                    .eqIfPresent(TrdSupplierAfterDtl::getFinishTime, reqVO.getFinishTime())
                    .eqIfPresent(TrdSupplierAfterDtl::getReturnTime, reqVO.getReturnTime())
                    .eqIfPresent(TrdSupplierAfterDtl::getRefundTime, reqVO.getRefundTime())
                    .eqIfPresent(TrdSupplierAfterDtl::getIsCancel, reqVO.getIsCancel())
                    .eqIfPresent(TrdSupplierAfterDtl::getSource, reqVO.getSource())
                    .eqIfPresent(TrdSupplierAfterDtl::getSyncFlag, reqVO.getSyncFlag())
                    .eqIfPresent(TrdSupplierAfterDtl::getMemo, reqVO.getMemo())
                .orderByDesc(TrdSupplierAfterDtl::getSupplierAfterDtlId));
    }


    /**
     * 根据入驻商售后订单退款单号 获取订单售后明细信息
     * @param refundNo  退款单号
     * @return
     */
    default List<TrdSupplierAfterDtl> selectAfterDtlByAfterDtlNo(String refundNo) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierAfterDtl>()
                .eqIfPresent(TrdSupplierAfterDtl::getRefundNo, refundNo));
    }

    /**
     * 根据主单ID 获取入驻商订单明细信息
     * @param orderId 主单ID
     * @return
     */
    default List<TrdSupplierAfterDtl> selectListByAfterId(Long afterId) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierAfterDtl>()
                .eqIfPresent(TrdSupplierAfterDtl::getAfterId, afterId)
                .orderByAsc(TrdSupplierAfterDtl::getSupplierId));
    }

    /**
     * 根据入驻商订单明细ID 获取已售后总数量和总金额
     * @param supplierDtlIds  订单明细ID集合
     * @return
     */
    List<OrderAfterDtlResDTO> getAfterDtlNumBySupplierDtlId(@Param("supplierDtlIds") List<Long> supplierDtlIds);

    List<AfterDetailOpenDto> getTrdSupplierAfterDtlBySplNo(@Param("aLong") Long aLong);

    /**
     * 根据入驻商售后订单主表编号 获取入驻商售后订单明细信息
     * @param supplierAfterNo 入驻商订单售后主表编号
     * @return
     */
    default List<TrdSupplierAfterDtl> selectListBySupplierAfterNo(String supplierAfterNo) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierAfterDtl>()
                .eqIfPresent(TrdSupplierAfterDtl::getSupplierAfterNo, supplierAfterNo));
    }

    /**
     * 根据入驻商售后订单明细编号集合 获取入驻商售后订单明细信息
     * @param dtlIdList 入驻商订单售后明细编号集合
     * @return
     */
    default List<TrdSupplierAfterDtl> selectListByDtlIdList(List<Long> dtlIdList) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierAfterDtl>()
                .inIfPresent(TrdSupplierAfterDtl::getSupplierAfterDtlId, dtlIdList));
    }

    /**
     * 根据入驻商订单明细编号集合 获取入驻商售后订单明细信息
     * @param dtlIdList 入驻商订单明细编号集合
     * @return
     */
    default List<TrdSupplierAfterDtl> selectListBySupplierDtlIdList(List<Long> dtlIdList) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierAfterDtl>()
                .inIfPresent(TrdSupplierAfterDtl::getSupplierOrderDtlId, dtlIdList));
    }

    default List<TrdSupplierAfterDtl> getSupplierAfterListBySupplierAfterNo(String supplierAfterNo){
        return selectList(new LambdaQueryWrapperX<TrdSupplierAfterDtl>()
                .eqIfPresent(TrdSupplierAfterDtl::getSupplierAfterNo,supplierAfterNo));
    }
    default List<TrdSupplierAfterDtl> selectListBySupplierAfterId(Long supplierAfterId){
        return selectList(new LambdaQueryWrapperX<TrdSupplierAfterDtl>()
                .eqIfPresent(TrdSupplierAfterDtl::getSupplierAfterId,supplierAfterId));
    }
    /**
     * 根据入驻商订单ID 查询发货后退款的已审核订单的退款金额和退款单位
     * @param supplierOrderDtlId 入驻商订单明细ID
     * @return
     */
    List<DcSupplierOrderAfterDtlRespVO> selectAfterDtlBySupplierOrderDtlId(@Param("supplierOrderDtlId")Long supplierOrderDtlId,@Param("afterType")Integer afterType);

}
