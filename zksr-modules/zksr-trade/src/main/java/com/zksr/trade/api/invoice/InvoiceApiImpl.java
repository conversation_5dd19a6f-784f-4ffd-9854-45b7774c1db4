package com.zksr.trade.api.invoice;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.invoice.dto.InvoiceCallbackDTO;
import com.zksr.trade.api.invoice.dto.InvoiceProcessDTO;
import com.zksr.trade.service.ITrdInvoiceInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
@Slf4j
public class InvoiceApiImpl implements InvoiceApi {

    @Autowired
    private ITrdInvoiceInfoService trdInvoiceInfoService;

    @Override
    public CommonResult<Boolean> blueInvoiceProcess(InvoiceProcessDTO invoiceProcessDTO) {
        return CommonResult.success(trdInvoiceInfoService.blueInvoiceProcess(invoiceProcessDTO));
    }

    @Override
    public CommonResult<Boolean> invoiceCallback(InvoiceCallbackDTO invoiceCallbackDTO) {
        return CommonResult.success(trdInvoiceInfoService.invoiceCallback(invoiceCallbackDTO));
    }
}
