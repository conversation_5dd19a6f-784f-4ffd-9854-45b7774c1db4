package com.zksr.trade.controller.after.vo;

import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.domain.*;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月21日 10:12
 * @description: OrderAfterSaveVO
 */
@Data
@ApiModel("订单信息 - 保存售后订单Request VO")
public class OrderAfterSaveVO {
    /**
     * 售后主订单
     */
    TrdAfter afterVO;
    /**
     * 售后入驻商订单
     */
    List<TrdSupplierAfter> supplierAfterVOs;
    /**
     * 售后入驻商订单明细
     */
    List<TrdSupplierAfterDtl> supplierAfterDtlVOs;
    /**
     * 售后入驻商订单明细结算信息
     */
    List<TrdSupplierAfterSettle> supplierAfterSettleVOs;
    /**
     * 售后入驻商订单明细日志
     */
    List<TrdAfterLog> afterLogVOs;
    /**
     * 售后订单明细优惠记录表
     */
    List<TrdAfterDiscountDtl> afterDiscountDtlVOs;

}
