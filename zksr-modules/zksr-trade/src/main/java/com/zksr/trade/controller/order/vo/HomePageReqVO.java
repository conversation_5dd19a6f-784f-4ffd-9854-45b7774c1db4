package com.zksr.trade.controller.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("入驻商首页返回实体类")
public class HomePageReqVO {

    @JsonFormat(pattern = YYYY_MM_DD)
    @DateTimeFormat(pattern = YYYY_MM_DD)
    @ApiModelProperty(value = "统计开始时间")
    private Date countStartDate;

    @JsonFormat(pattern = YYYY_MM_DD)
    @DateTimeFormat(pattern = YYYY_MM_DD)
    @ApiModelProperty(value = "统计结束时间")
    private Date countEndDate;
}
