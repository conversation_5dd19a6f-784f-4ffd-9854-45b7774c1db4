package com.zksr.trade.service;

import javax.validation.Valid;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.controller.trdPackage.vo.TrdPackagePageReqVO;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageSaveReqVO;
import com.zksr.trade.domain.TrdPackage;

import java.util.List;

/**
 * 包裹Service接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface ITrdPackageService {

    /**
     * 新增包裹
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdPackage(@Valid TrdPackageSaveReqVO createReqVO);

    /**
     * 修改包裹
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdPackage(@Valid TrdPackageSaveReqVO updateReqVO);

    /**
     * 删除包裹
     *
     * @param packageId 包裹ID
     */
    public void deleteTrdPackage(Long packageId);

    /**
     * 批量删除包裹
     *
     * @param packageIds 需要删除的包裹主键集合
     * @return 结果
     */
    public void deleteTrdPackageByPackageIds(Long[] packageIds);

    /**
     * 获得包裹
     *
     * @param packageId 包裹ID
     * @return 包裹
     */
    public TrdPackage getTrdPackage(Long packageId);

    /**
     * 获得包裹分页
     *
     * @param pageReqVO 分页查询
     * @return 包裹分页
     */
    PageResult<TrdPackage> getTrdPackagePage(TrdPackagePageReqVO pageReqVO);

    /**
     * 根据入驻商订单号查询包裹列表
     * @param supplierOrderNo 入驻商订单号
     * @return 包裹列表
     */
    List<TrdPackage> getPackageListBySupplierOrderNo(String supplierOrderNo);
    
    /**
     * 根据入驻商订单号+包裹查询包裹列表
     * @return 包裹列表
     */
    TrdPackage getPackageByOrderIdAndPackageNo(Long supplierOrderId, String packageNo);
}
