package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.api.order.vo.TrdOrderShareRespVO;
import com.zksr.trade.domain.TrdOrderShare;
import com.zksr.trade.controller.order.vo.TrdOrderSharePageReqVO;
import com.zksr.trade.api.order.vo.TrdOrderShareSaveReqVO;

/**
 * 订单分享Service接口
 *
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface ITrdOrderShareService {

    /**
     * 新增订单分享
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public String insertTrdOrderShare(@Valid TrdOrderShareSaveReqVO createReqVO);

    /**
     * 修改订单分享
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdOrderShare(@Valid TrdOrderShareSaveReqVO updateReqVO);

    /**
     * 删除订单分享
     *
     * @param shareOrderId ${pkColumn.columnComment}
     */
    public void deleteTrdOrderShare(Long shareOrderId);

    /**
     * 批量删除订单分享
     *
     * @param shareOrderIds 需要删除的订单分享主键集合
     * @return 结果
     */
    public void deleteTrdOrderShareByShareOrderIds(Long[] shareOrderIds);

    /**
     * 获得订单分享
     *
     * @param shareOrderId ${pkColumn.columnComment}
     * @return 订单分享
     */
    public TrdOrderShare getTrdOrderShare(Long shareOrderId);

    /**
     * 获得订单分享分页
     *
     * @param pageReqVO 分页查询
     * @return 订单分享分页
     */
    PageResult<TrdOrderShare> getTrdOrderSharePage(TrdOrderSharePageReqVO pageReqVO);

    /**
     * 根据分享key获取分享订单
     * @param shareKey
     * @return
     */
    TrdOrderShareRespVO getTrdOrderShare(String shareKey);
}
