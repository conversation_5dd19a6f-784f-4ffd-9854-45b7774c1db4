package com.zksr.trade.service.impl.handler.activity;

import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.domain.TrdAfterDiscountDtl;
import com.zksr.trade.domain.TrdOrderDiscountDtl;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.service.handler.activity.ITrdOrderActivityHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;

/**
 *  订单优惠活动处理器 ———— 秒杀促销
 */
@Service
@Slf4j
public class TrdOrderActivitySkHandlerServiceImpl implements ITrdOrderActivityHandlerService {

    @Autowired
    private RedisActivityService redisActivityService;
    @Override
    public void orderCancelReturnActivity(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {
        // 返还秒杀活动库存
        toddList.stream()
                .filter(orderDiscount -> Objects.equals(TrdDiscountTypeEnum.SK.getType(), orderDiscount.getDiscountType()))
                .forEach(orderDiscount -> {
                    TrdSupplierOrderDtl supplierOrderDtl = tsodMap.get(orderDiscount.getSupplierOrderDtlId());
                    if (supplierOrderDtl == null) {
                        return;
                    }

                    // set秒杀活动商品门店已购数量 返还
                    BigDecimal orderUnitQtyNegate = BigDecimal.valueOf(supplierOrderDtl.getOrderUnitQty()).negate();
                    if (UnitTypeEnum.UNIT_SMALL.getType().equals(supplierOrderDtl.getOrderUnitType())) {
                        // set秒杀活动商品门店已购数量 返还（小单位）
                        redisActivityService.setSkSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQtyNegate, null, null);
                        // set秒杀活动商品已购总数量 返还（小单位）
                        redisActivityService.setSkSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQtyNegate, null, null);
                    } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(supplierOrderDtl.getOrderUnitType())) {
                        // set秒杀活动商品门店已购数量 返还（中单位）
                        redisActivityService.setSkMidSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQtyNegate, null, null);
                        // set秒杀活动商品已购总数量 返还（中单位）
                        redisActivityService.setSkMidSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQtyNegate, null, null);
                    } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(supplierOrderDtl.getOrderUnitType())) {
                        // set秒杀活动商品门店已购数量 返还（大单位）
                        redisActivityService.setSkLargeSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQtyNegate, null, null);
                        // set秒杀活动商品已购总数量 返还（大单位）
                        redisActivityService.setSkLargeSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQtyNegate, null, null);
                    }
                });
    }

    @Override
    public void afterCancelReturnActivity(TrdOrder tor, List<TrdSupplierOrderDtl> tsodList, List<TrdOrderDiscountDtl> toddList, TrdAfter tar, List<TrdSupplierAfterDtl> tsadList, List<TrdAfterDiscountDtl> taddList) {

        // 构建售后详情映射
        Map<Long, TrdSupplierAfterDtl> afterDtlMap = convertMap(tsadList, TrdSupplierAfterDtl::getSupplierAfterDtlId);
        taddList.stream()
                .filter(tadd -> Objects.equals(TrdDiscountTypeEnum.SK.getType(), tadd.getDiscountType()))
                .forEach(orderDiscount -> {

                    TrdSupplierAfterDtl tsad = afterDtlMap.get(orderDiscount.getSupplierAfterDtlId());

                    BigDecimal returnUnitQtyNegate = tsad.getReturnUnitQty().negate();
                    if (UnitTypeEnum.UNIT_SMALL.getType().equals(tsad.getReturnUnitType())) {
                        // set秒杀活动商品门店已购数量 返还（小单位）
                        redisActivityService.setSkSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                        // set秒杀活动商品已购总数量 返还（小单位）
                        redisActivityService.setSkSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                    } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(tsad.getReturnUnitType())) {
                        // set秒杀活动商品门店已购数量 返还（中单位）
                        redisActivityService.setSkMidSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                        // set秒杀活动商品已购总数量 返还（中单位）
                        redisActivityService.setSkMidSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                    } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(tsad.getReturnUnitType())) {
                        // set秒杀活动商品门店已购数量 返还（大单位）
                        redisActivityService.setSkLargeSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                        // set秒杀活动商品已购总数量 返还（大单位）
                        redisActivityService.setSkLargeSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), returnUnitQtyNegate, null, null);
                    }
                });
    }

    @Override
    public void restoreCancelledOrderDiscounts(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {
        // 恢复已取消订单的 秒杀活动库存
        toddList.stream()
                .filter(orderDiscount -> Objects.equals(TrdDiscountTypeEnum.SK.getType(), orderDiscount.getDiscountType()))
                .forEach(orderDiscount -> {
                    TrdSupplierOrderDtl supplierOrderDtl = tsodMap.get(orderDiscount.getSupplierOrderDtlId());

                    BigDecimal orderUnitQty = BigDecimal.valueOf(supplierOrderDtl.getOrderUnitQty());
                    if (UnitTypeEnum.UNIT_SMALL.getType().equals(supplierOrderDtl.getOrderUnitType())) {
                        // 恢复已取消订单 秒杀活动商品门店已购数量（小单位）
                        redisActivityService.setSkSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQty, null, null);
                        // 恢复已取消订单 秒杀活动商品已购总数量（小单位）
                        redisActivityService.setSkSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQty, null, null);
                    } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(supplierOrderDtl.getOrderUnitType())) {
                        // 恢复已取消订单 秒杀活动商品门店已购数量（中单位）
                        redisActivityService.setSkMidSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQty, null, null);
                        // 恢复已取消订单 秒杀活动商品已购总数量（中单位）
                        redisActivityService.setSkMidSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQty, null, null);
                    } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(supplierOrderDtl.getOrderUnitType())) {
                        // 恢复已取消订单 秒杀活动商品门店已购数量（大单位）
                        redisActivityService.setSkLargeSaleNum(tor.getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQty, null, null);
                        // 恢复已取消订单 秒杀活动商品已购总数量（大单位）
                        redisActivityService.setSkLargeSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderUnitQty, null, null);
                    }
                });
    }
}
