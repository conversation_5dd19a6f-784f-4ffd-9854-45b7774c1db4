package com.zksr.trade.service.impl;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import com.zksr.trade.api.driver.excel.TrdDriverImportExcel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdDriverMapper;
import com.zksr.trade.convert.driver.TrdDriverConvert;
import com.zksr.trade.domain.TrdDriver;
import com.zksr.trade.controller.driver.vo.TrdDriverPageReqVO;
import com.zksr.trade.controller.driver.vo.TrdDriverSaveReqVO;
import com.zksr.trade.service.ITrdDriverService;

import java.util.*;

import static com.zksr.common.core.constant.StatusConstants.STATUS_FAIL;
import static com.zksr.common.core.constant.StatusConstants.STATUS_SUCCESS;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 司机档案Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@Service
public class TrdDriverServiceImpl implements ITrdDriverService {
    @Autowired
    private TrdDriverMapper trdDriverMapper;

    /**
     * 新增司机档案
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdDriver(TrdDriverSaveReqVO createReqVO) {
        // 插入
        TrdDriver trdDriver = TrdDriverConvert.INSTANCE.convert(createReqVO);

        //校验司机手机号的唯一性
        if (ToolUtil.isNotEmpty(trdDriverMapper.checkDriverPhoneUnique(createReqVO.getDriverPhone()))) {
            throw exception(TRD_DRIVER_PHONE_EXIST);
        }

        trdDriverMapper.insert(trdDriver);
        // 返回
        return trdDriver.getDriverId();
    }

    /**
     * 修改司机档案
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdDriver(TrdDriverSaveReqVO updateReqVO) {
        trdDriverMapper.updateById(TrdDriverConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除司机档案
     *
     * @param driverId 司机id
     */
    @Override
    public void deleteTrdDriver(Long driverId) {
        // 删除
        trdDriverMapper.deleteById(driverId);
    }

    /**
     * 批量删除司机档案
     *
     * @param driverIds 需要删除的司机档案主键
     * @return 结果
     */
    @Override
    public void deleteTrdDriverByDriverIds(Long[] driverIds) {
        for (Long driverId : driverIds) {
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deleteTrdDriver(driverId);
        }
    }

    /**
     * 获得司机档案
     *
     * @param driverId 司机id
     * @return 司机档案
     */
    @Override
    public TrdDriver getTrdDriver(Long driverId) {
        return trdDriverMapper.selectById(driverId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<TrdDriver> getTrdDriverPage(TrdDriverPageReqVO pageReqVO) {
        return trdDriverMapper.selectPage(pageReqVO);
    }

    public String impordData(List<TrdDriverImportExcel> driverList) {
        return impordDataEvent(driverList,SecurityUtils.getDcId(),SecurityUtils.getLoginUser().getSysCode(),null,0).getMsg();
    }

    @Override
    public FileImportHandlerVo impordDataEvent(List<TrdDriverImportExcel> driverList,Long dcId,Long sysCode,Long fileImportId,Integer seq) {
        FileImportHandlerVo fileImportHandlerVo = new FileImportHandlerVo();
        List<SysFileImportDtl> sysFileImportDtls = new ArrayList<>();
        int successNum = driverList.size();
        int failureNum = 0;
        int totalNum = driverList.size();
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        fileImportHandlerVo.setSuccessNum(successNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setTotalNum(totalNum);

        if (driverList.isEmpty()) {
            // 如果导入数据为空，则不进行数据导入
            fileImportHandlerVo.setFailureNum(driverList.size());
            fileImportHandlerVo.setTotalNum(driverList.size());
            fileImportHandlerVo.setMsg("导入数据为空");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
        }
//        Long dcId = SecurityUtils.getDcId();
        if (Objects.isNull(dcId)) {
            fileImportHandlerVo.setFailureNum(driverList.size());
            fileImportHandlerVo.setTotalNum(driverList.size());
            fileImportHandlerVo.setMsg("非运营商角色");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            return fileImportHandlerVo;
//            throw new ServiceException("非运营商角色");
        }

        Map<String, TrdDriver> driverCache = new LinkedHashMap<>();
//        int successNum = driverList.size();
//        int failureNum = 0;
//        StringBuilder successMsg = new StringBuilder();
//        StringBuilder failureMsg = new StringBuilder();
        for (int line = 0; line < driverList.size(); line++) {
            //导入明细
            SysFileImportDtl sysFileImportDtl = new SysFileImportDtl();
            sysFileImportDtl.setSysCode(sysCode);
            sysFileImportDtl.setCreateBy(sysFileImportDtl.getCreateBy());
            sysFileImportDtl.setCreateTime(new Date());
            sysFileImportDtl.setFileImportId(fileImportId);
            if (failureMsg.length() > 2000) {
                break;
            }
            int cellNumber = line + 3+seq;
            TrdDriverImportExcel data = driverList.get(line);
            sysFileImportDtl.setDtlJson(JsonUtils.toJsonString(data));

            if (StringUtils.isEmpty(data.getDriverName())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>司机名称不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据司机名称不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (StringUtils.isEmpty(data.getDriverPhone())) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>司机电话不能为空。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据司机电话不能为空。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (data.getDriverPhone().length() > 11) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>司机电话字符长度超长11。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据司机电话字符长度超长11。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            if (ToolUtil.isNotEmpty(trdDriverMapper.checkDriverPhoneUnique(data.getDriverPhone()))) {
                failureNum++;
                failureMsg.append(StringUtils.format("<br/>第{}行数据<span style='color:red'>司机电话已存在，不可导入重复的手机号。</span>", cellNumber));
                sysFileImportDtl.setStatus(StatusConstants.STATUS_FAIL);
                sysFileImportDtl.setFailReason(StringUtils.format("第{}行数据司机电话已存在，不可导入重复的手机号。", cellNumber));
                sysFileImportDtls.add(sysFileImportDtl);
                continue;
            }
            // 转换导入实体 司机信息
            TrdDriver trdDriver = new TrdDriver();
            TrdDriverConvert.INSTANCE.convertImport(trdDriver, data);
            driverCache.put(data.getDriverPhone(), trdDriver);
            sysFileImportDtl.setStatus(STATUS_SUCCESS);
            sysFileImportDtls.add(sysFileImportDtl);
        }

        fileImportHandlerVo.setTotalNum(totalNum);
        fileImportHandlerVo.setSuccessNum(totalNum-failureNum);
        fileImportHandlerVo.setFailureNum(failureNum);
        fileImportHandlerVo.setList(sysFileImportDtls);
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            fileImportHandlerVo.setStatus(STATUS_FAIL);
            fileImportHandlerVo.setMsg(failureMsg.toString());
            return fileImportHandlerVo;
//            throw new ServiceException(failureMsg.toString());
        } else {
            driverCache.forEach((phone, trdDriver) -> trdDriverMapper.insert(trdDriver));
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
            fileImportHandlerVo.setStatus(STATUS_SUCCESS);
            fileImportHandlerVo.setMsg(successMsg.toString());
        }
        return fileImportHandlerVo;
    }

/*    private void validateTrdDriverExists(Long driverId) {
        if (trdDriverMapper.selectById(driverId) == null) {
            throw exception(TRD_DRIVER_NOT_EXISTS);
        }
    }*/

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 司机档案 TODO 补充编号 ==========
    // ErrorCode TRD_DRIVER_NOT_EXISTS = new ErrorCode(TODO 补充编号, "司机档案不存在");


}
