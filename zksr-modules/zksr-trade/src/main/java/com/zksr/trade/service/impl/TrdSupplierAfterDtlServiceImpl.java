package com.zksr.trade.service.impl;

import com.zksr.common.core.domain.erp.dto.AfterDetailOpenDto;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.trade.mapper.TrdSupplierAfterDtlMapper;
import com.zksr.trade.convert.after.TrdSupplierAfterDtlConvert;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.controller.after.supplierDtlVo.TrdSupplierAfterDtlPageReqVO;
import com.zksr.trade.controller.after.supplierDtlVo.TrdSupplierAfterDtlSaveReqVO;
import com.zksr.trade.service.ITrdSupplierAfterDtlService;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 售后单明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Service
public class TrdSupplierAfterDtlServiceImpl implements ITrdSupplierAfterDtlService {
    @Autowired
    private TrdSupplierAfterDtlMapper trdSupplierAfterDtlMapper;

    /**
     * 新增售后单明细
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdSupplierAfterDtl(TrdSupplierAfterDtlSaveReqVO createReqVO) {
        // 插入
        TrdSupplierAfterDtl trdSupplierAfterDtl = TrdSupplierAfterDtlConvert.INSTANCE.convert(createReqVO);
        trdSupplierAfterDtlMapper.insert(trdSupplierAfterDtl);
        // 返回
        return trdSupplierAfterDtl.getSupplierAfterDtlId();
    }

    /**
     * 修改售后单明细
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdSupplierAfterDtl(TrdSupplierAfterDtlSaveReqVO updateReqVO) {
        trdSupplierAfterDtlMapper.updateById(TrdSupplierAfterDtlConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除售后单明细
     *
     * @param supplierAfterDtlId 售后单明细id
     */
    @Override
    public void deleteTrdSupplierAfterDtl(Long supplierAfterDtlId) {
        // 删除
        trdSupplierAfterDtlMapper.deleteById(supplierAfterDtlId);
    }

    /**
     * 批量删除售后单明细
     *
     * @param supplierAfterDtlIds 需要删除的售后单明细主键
     * @return 结果
     */
    @Override
    public void deleteTrdSupplierAfterDtlBySupplierAfterDtlIds(Long[] supplierAfterDtlIds) {
        for(Long supplierAfterDtlId : supplierAfterDtlIds){
            this.deleteTrdSupplierAfterDtl(supplierAfterDtlId);
        }
    }

    /**
     * 获得售后单明细
     *
     * @param supplierAfterDtlId 售后单明细id
     * @return 售后单明细
     */
    @Override
    public TrdSupplierAfterDtl getTrdSupplierAfterDtl(Long supplierAfterDtlId) {
        return trdSupplierAfterDtlMapper.selectById(supplierAfterDtlId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<TrdSupplierAfterDtl> getTrdSupplierAfterDtlPage(TrdSupplierAfterDtlPageReqVO pageReqVO) {
        return trdSupplierAfterDtlMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AfterDetailOpenDto> getTrdSupplierAfterDtlBySplNo(Long aLong) {
        return trdSupplierAfterDtlMapper.getTrdSupplierAfterDtlBySplNo(aLong);
    }

    @Override
    public List<TrdSupplierAfterDtl> getTrdSupplierAfterDtlBatch(List<Long> supplierAfterDtlIdList) {
        return trdSupplierAfterDtlMapper.selectBatchIds(supplierAfterDtlIdList);
    }

    @Override
    public List<TrdSupplierAfterDtl> getAfterDtlListBySupplierAfterId(Long supplierAfterId) {
        return trdSupplierAfterDtlMapper.selectListBySupplierAfterId(supplierAfterId);
    }

    private void validateTrdSupplierAfterDtlExists(Long supplierAfterDtlId) {
        if (trdSupplierAfterDtlMapper.selectById(supplierAfterDtlId) == null) {
            throw exception(TRD_SUPPLIER_AFTER_DTL_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 售后单明细 TODO 补充编号 ==========
    // ErrorCode TRD_SUPPLIER_AFTER_DTL_NOT_EXISTS = new ErrorCode(TODO 补充编号, "售后单明细不存在");


}
