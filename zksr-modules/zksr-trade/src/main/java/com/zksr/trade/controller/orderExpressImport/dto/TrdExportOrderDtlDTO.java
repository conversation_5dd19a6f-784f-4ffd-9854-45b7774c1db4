package com.zksr.trade.controller.orderExpressImport.dto;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年04月16日 14:35
 * @description: TrdExportOrderDtlDTO
 */
@Data
@ApiModel("入驻商全国订单商品导出实体")
public class TrdExportOrderDtlDTO {

    @Excel(name = "订单详情编号")
    @ApiModelProperty(value = "订单详情编号")
    private String supplierOrderDtlNo;

    @Excel(name = "商品名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    @Excel(name = "规格名称")
    @ApiModelProperty(value = "商品SPU规格名称")
    private String specName;

    @Excel(name = "商品条码（小单位）")
    @ApiModelProperty(value = "商品条码（小单位）")
    private String barcode;

    @Excel(name = "数量")
    @ApiModelProperty(value = "商品数量")
    private Long totalNum;

    @Excel(name = "商品销售价")
    @ApiModelProperty(value = "商品销售价")
    private BigDecimal price;

    @Excel(name = "供应商名称")
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @Excel(name = "收货人")
    @ApiModelProperty(value = "门店收货人")
    private String branchReceiveBy;

    @Excel(name = "手机号")
    @ApiModelProperty(value = "门店手机号")
    private String branchIphone;

    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddress;

    @Excel(name = "寄件人")
    @ApiModelProperty(value = "寄件人")
    private String deliveryBy;

    @Excel(name = "寄件人电话")
    @ApiModelProperty(value = "寄件人电话")
    private String deliveryIphone;

    @Excel(name = "快递公司")
    @ApiModelProperty(value = "快递公司")
    private String expressCom;

    @Excel(name = "快递单号")
    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @Excel(name = "快递公司英文编码")
    @ApiModelProperty(value = "快递公司英文编码")
    private String expressComNo;

    @Excel(name = "支付时间")
    @ApiModelProperty(value = "支付时间")
    private String payTime;


    //==================== 不导出显示数据=============================
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;
    @ApiModelProperty(value = "门店id")
    private Long branchId;
    @ApiModelProperty(value = "skuid")
    private Long skuId;
    @ApiModelProperty(value = "spuid")
    private Long spuId;
    @ApiModelProperty(value = "orderId")
    private Long orderId;
    @ApiModelProperty(value = "订单详情Id")
    private Long supplierOrderDtlId;


}
