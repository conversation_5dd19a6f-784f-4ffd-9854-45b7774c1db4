package com.zksr.trade.controller.after.logVo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 售后日志对象 trd_after_log
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@ApiModel("售后日志 - trd_after_log分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdAfterLogPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 售后日志 */
    @ApiModelProperty(value = "订单日志信息")
    private Long afterLogId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 入驻商售后单明细id */
    @Excel(name = "入驻商售后单明细id")
    @ApiModelProperty(value = "入驻商售后单明细id", required = true)
    private Long supplierAfterDtlId;

    /** 入驻商售后单明细编号 */
    @Excel(name = "入驻商售后单明细编号")
    @ApiModelProperty(value = "入驻商售后单明细编号", required = true)
    private String supplierAfterDtlNo;

    /** 操作前状态 */
    @Excel(name = "操作前状态")
    @ApiModelProperty(value = "操作前状态", required = true)
    private Long beforeState;

    /** 操作后状态 */
    @Excel(name = "操作后状态")
    @ApiModelProperty(value = "操作后状态", required = true)
    private Long afterState;

    /** 操作类型(数据字典) */
    @Excel(name = "操作类型(数据字典)")
    @ApiModelProperty(value = "操作类型(数据字典)", required = true)
    private Long operateType;

    /** 订单日志信息 */
    @Excel(name = "订单日志信息")
    @ApiModelProperty(value = "订单日志信息")
    private String content;


}
