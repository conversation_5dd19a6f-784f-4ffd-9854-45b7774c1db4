package com.zksr.trade.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LFUCache;
import cn.hutool.core.util.NumberUtil;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.divide.DivideFlowApi;
import com.zksr.account.api.divide.dto.AccDivideDtlDTO;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.enums.DistributionModeEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.software.SoftwareApi;
import com.zksr.system.api.software.dto.SoftwareDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.dto.TrdSettleDTO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.trade.api.orderSettle.dto.*;
import com.zksr.trade.api.orderSettle.vo.OrderSettlePageVO;
import com.zksr.trade.api.orderSettle.vo.OrderSupplierSettlePageVo;
import com.zksr.trade.controller.orderSettle.vo.TrdSupplierOrderSettlePageReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderPageReqVO;
import com.zksr.trade.controller.settle.vo.TrdSettlePageReqVO;
import com.zksr.trade.controller.settle.vo.TrdSettleSaveReqVO;
import com.zksr.trade.controller.statementOfAccount.vo.OrderCommissionStatementReqVO;
import com.zksr.trade.controller.statementOfAccount.vo.OrderCommissionStatementRespVO;
import com.zksr.trade.domain.*;
import com.zksr.trade.mapper.*;
import com.zksr.trade.service.ITrdOrderService;
import com.zksr.trade.service.TrdCacheService;
import lombok.extern.slf4j.Slf4j;

import org.checkerframework.checker.units.qual.N;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.zksr.trade.service.ITrdSettleService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;
import static com.zksr.trade.enums.ErrorCodeConstants.TRD_SETTLE_NOT_EXISTS;

/**
 * 订单结算Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Service
@Slf4j
public class TrdSettleServiceImpl implements ITrdSettleService {
    @Autowired
    private TrdSettleMapper trdSettleMapper;

    @Autowired
    private TrdSupplierOrderSettleMapper trdSupplierOrderSettleMapper;

    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;

    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private TrdSupplierAfterSettleMapper trdSupplierAfterSettleMapper;

    @Autowired
    private TrdSupplierAfterMapper trdSupplierAfterMapper;

    @Autowired
    private TrdAfterMapper trdAfterMapper;

    @Autowired
    private TrdOrderMapper orderMapper;

    @Autowired
    private AccountApi accountApi;

    @Autowired
    private TrdSupplierOrderDtlMapper supplierOrderDtlMapper;

    @Autowired
    private TrdSupplierAfterDtlMapper supplierAfterDtlMapper;

    @Autowired
    private DcApi dcApi;

    @Autowired
    private PartnerApi partnerApi;

    @Autowired
    private SoftwareApi softwareApi;
    
    @Autowired
    private DivideFlowApi divideFlowApi;
    
    @Lazy
    @Autowired
    private ITrdOrderService orderService;
    


    /**
     * 新增订单结算
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdSettle(TrdSettleSaveReqVO createReqVO) {
        // 插入
        TrdSettle trdSettle = HutoolBeanUtils.toBean(createReqVO, TrdSettle.class);
        trdSettleMapper.insert(trdSettle);
        // 返回
        return trdSettle.getSettleId();
    }

    /**
     * 修改订单结算
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdSettle(TrdSettleSaveReqVO updateReqVO) {
        trdSettleMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, TrdSettle.class));
    }

    /**
     * 删除订单结算
     *
     * @param settleId 订单结算id
     */
    @Override
    public void deleteTrdSettle(Long settleId) {
        // 删除
        trdSettleMapper.deleteById(settleId);
    }

    /**
     * 批量删除订单结算
     *
     * @param settleIds 需要删除的订单结算主键
     * @return 结果
     */
    @Override
    public void deleteTrdSettleBySettleIds(Long[] settleIds) {
        for (Long settleId : settleIds) {
            this.deleteTrdSettle(settleId);
        }
    }

    /**
     * 获得订单结算
     *
     * @param settleId 订单结算id
     * @return 订单结算
     */
    @Override
    public TrdSettle getTrdSettle(Long settleId) {
        return trdSettleMapper.selectById(settleId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<TrdSettle> getTrdSettlePage(TrdSettlePageReqVO pageReqVO) {
        return trdSettleMapper.selectPage(pageReqVO);
    }
    
    //!@支付 - 提交支付订单 - 2、校验订单 - 1、根据（trd_supplier_order_settle）【模拟生成】订单结算表(trd_settle) , 【入驻商】为【平台、软件商、运营商、业务员、业务员负责人】结算金额,所以一条商品总结算记录<1:N>多个结算明细
    //!@回调支付 - 7、更新订单之后其他操作 - 1、生成订单结算表(trd_settle), 【入驻商】为【平台、软件商、运营商、业务员、业务员负责人】结算金额,所以一条商品总结算记录<1:N>多个结算明细
    @Override
    public List<TrdSettle> createBatchSettle(TrdOrder order) {
        /**
         * 获取入驻商订单明细利润分成信息
         *
         */
        TrdSupplierOrderSettlePageReqVO settlePageVo = new TrdSupplierOrderSettlePageReqVO();
        settlePageVo.setOrderNo(order.getOrderNo());
        List<TrdSupplierOrderSettle> supplierOrderSettles = trdSupplierOrderSettleMapper.selectList(settlePageVo);


        /**
         * 获取入驻商订单信息
         */
        TrdSupplierOrderPageReqVO subPageVo = new TrdSupplierOrderPageReqVO();
        subPageVo.setOrderNo(order.getOrderNo());
        List<TrdSupplierOrder> subOrderList = trdSupplierOrderMapper.selectList(subPageVo);
        Map<Long, TrdSupplierOrder> supplierVoMap = subOrderList.stream().collect(Collectors.toMap(TrdSupplierOrder::getSupplierOrderId, TrdSupplierOrder -> TrdSupplierOrder));
       

        //平台商信息 获取关联软件商ID
        PartnerDto partnerDto = trdCacheService.getPartnerDto(order.getSysCode() + "");
        
        // 判断分销模式是否为O2O
        if (DistributionModeEnum.O2O.getCode().equals(order.getDistributionMode())) {
            return getO2OTrdSettles(order, supplierOrderSettles, supplierVoMap, partnerDto);
        } else {
            return getTrdSettles(order, supplierOrderSettles, supplierVoMap, partnerDto);
        }
    }
    
    private  List<TrdSettle> getTrdSettles(TrdOrder order, List<TrdSupplierOrderSettle> supplierOrderSettles, Map<Long, TrdSupplierOrder> supplierVoMap, PartnerDto partnerDto) {
        /**
         *
         */
        Map<Long, Map<String, BigDecimal>> settleMap = new HashMap<>();
        supplierOrderSettles.forEach(settle -> {
            Map<String, BigDecimal> map = new HashMap<>();
            //设置软件商结算金额
            map.put(MerchantTypeEnum.SOFTWARE.getType(), ToolUtil.isEmpty(settle.getSoftwareAmt()) ? BigDecimal.ZERO : settle.getSoftwareAmt());
            map.put(MerchantTypeEnum.PARTNER.getType(), settle.getPartnerAmt());
            map.put(MerchantTypeEnum.DC.getType(), settle.getDcAmt());
            if (ToolUtil.isNotEmpty(order.getColonelId()))
                map.put(MerchantTypeEnum.COLONEL.getType() + "2", settle.getColonel2Amt());
            if (ToolUtil.isNotEmpty(order.getPcolonelId())) {
                if (Objects.equals(order.getPcolonelId(), order.getColonelId())) {
                    map.put(MerchantTypeEnum.COLONEL.getType() + "2", settle.getColonel1Amt().add(settle.getColonel2Amt()));
                } else {
                    map.put(MerchantTypeEnum.COLONEL.getType() + "1", settle.getColonel1Amt());
                }
            }
            settleMap.put(settle.getSupplierOrderDtlId(), map);
        });
        
        log.info("保存订单付款后结算信息:" + order.toString());
        
        /**
         * 遍历子单结算明细，该子单下平台、软件商、运营商、业务员、业务员负责人结算金额
         * 所以子单<1:N>多个计算明细
         */
        List<TrdSettle> settles = new ArrayList<>();
        supplierOrderSettles.forEach(dtlSettle -> {
            Map<String, BigDecimal> map = settleMap.get(dtlSettle.getSupplierOrderDtlId());
            TrdSupplierOrder supplierOrder = supplierVoMap.get(dtlSettle.getSupplierOrderId());
            map.forEach((k, v) -> {
                TrdSettle trdSettle = new TrdSettle();
                trdSettle.setSysCode(order.getSysCode());
                trdSettle.setOrderId(dtlSettle.getOrderId());
                trdSettle.setOrderNo(dtlSettle.getOrderNo());
                trdSettle.setSupplierOrderId(dtlSettle.getSupplierOrderId());
                trdSettle.setSupplierOrderNo(dtlSettle.getSupplierOrderNo());
                trdSettle.setSupplierOrderDtlId(dtlSettle.getSupplierOrderDtlId());
                trdSettle.setSupplierOrderDtlNo(dtlSettle.getSupplierOrderDtlNo());
                trdSettle.setState(StatusConstants.SETTLE_STATE_0);
                if (k.contains(MerchantTypeEnum.SOFTWARE.getType())) { // 软件商
                    trdSettle.setMerchantType(MerchantTypeEnum.SOFTWARE.getType());
                    trdSettle.setMerchantId(partnerDto.getSoftwareId());
                }
                if (k.contains(MerchantTypeEnum.PARTNER.getType())) { // 平台商
                    trdSettle.setMerchantType(MerchantTypeEnum.PARTNER.getType());
                    trdSettle.setMerchantId(order.getSysCode());
                }
                if (k.contains(MerchantTypeEnum.DC.getType())) { // 运营商
                    trdSettle.setMerchantType(MerchantTypeEnum.DC.getType());
                    trdSettle.setMerchantId(order.getDcId());
                }
                if (k.contains(MerchantTypeEnum.COLONEL.getType() + "1") && ToolUtil.isNotEmpty(order.getPcolonelId())) { // 业务员负责人
                    trdSettle.setMerchantType(MerchantTypeEnum.COLONEL.getType());
                    trdSettle.setMerchantId(order.getPcolonelId());
                }
                if (k.contains(MerchantTypeEnum.COLONEL.getType() + "2") && ToolUtil.isNotEmpty(order.getColonelId())) { // 业务员
                    trdSettle.setMerchantType(MerchantTypeEnum.COLONEL.getType());
                    trdSettle.setMerchantId(order.getColonelId());
                }
                trdSettle.setSettleAmt(v);
                trdSettle.setSupplierId(supplierOrder.getSupplierId());
                trdSettle.setPlatform(order.getPlatform());
                if (NumberUtil.isGreater(dtlSettle.getProfit(), BigDecimal.ZERO)) {
                    trdSettle.setSettleRate(v.divide(dtlSettle.getProfit(), 4, BigDecimal.ROUND_HALF_UP)); // 实际结算比例
                } else {
                    trdSettle.setSettleRate(BigDecimal.ZERO); // 实际结算比例
                }

                settles.add(trdSettle);
            });
        });
        return settles;
    }
    
    private  List<TrdSettle> getO2OTrdSettles(TrdOrder order, List<TrdSupplierOrderSettle> supplierOrderSettles,  Map<Long, TrdSupplierOrder> supplierVoMap, PartnerDto partnerDto) {
        
        Map<Long, Map<String, BigDecimal>> settleMap = new HashMap<>();
        supplierOrderSettles.forEach(settle -> {
            Map<String, BigDecimal> map = new HashMap<>();
            map.put(MerchantTypeEnum.BRANCH_PROFIT.getType(), settle.getBranchAmt());
            map.put(MerchantTypeEnum.SUPPLIER.getType(), settle.getSupplierAmt());
            settleMap.put(settle.getSupplierOrderDtlId(), map);
        });
        
        
        List<TrdSettle> settles = new ArrayList<>();
        supplierOrderSettles.forEach(dtlSettle -> {
            Map<String, BigDecimal> map = settleMap.get(dtlSettle.getSupplierOrderDtlId());
            TrdSupplierOrder supplierOrder = supplierVoMap.get(dtlSettle.getSupplierOrderId());
            map.forEach((k, v) -> {
                TrdSettle trdSettle = new TrdSettle();
                trdSettle.setSysCode(order.getSysCode());
                trdSettle.setOrderId(dtlSettle.getOrderId());
                trdSettle.setOrderNo(dtlSettle.getOrderNo());
                trdSettle.setSupplierOrderId(dtlSettle.getSupplierOrderId());
                trdSettle.setSupplierOrderNo(dtlSettle.getSupplierOrderNo());
                trdSettle.setSupplierOrderDtlId(dtlSettle.getSupplierOrderDtlId());
                trdSettle.setSupplierOrderDtlNo(dtlSettle.getSupplierOrderDtlNo());
                trdSettle.setState(StatusConstants.SETTLE_STATE_0);
                if (k.contains(MerchantTypeEnum.BRANCH_PROFIT.getType())) { // 门店
                    trdSettle.setMerchantType(MerchantTypeEnum.BRANCH_PROFIT.getType());
                    trdSettle.setMerchantId(order.getBranchId());
                }
                if (k.contains(MerchantTypeEnum.SUPPLIER.getType())) { // 入驻商
                    trdSettle.setMerchantType(MerchantTypeEnum.SUPPLIER.getType());
                    trdSettle.setMerchantId(supplierOrder.getSupplierId());
                }
                trdSettle.setSettleAmt(v);
                trdSettle.setSupplierId(supplierOrder.getSupplierId());
                trdSettle.setPlatform(order.getPlatform());
                if (NumberUtil.isGreater(dtlSettle.getProfit(), BigDecimal.ZERO)) {
                    trdSettle.setSettleRate(v.divide(dtlSettle.getPayAmt(), 4, BigDecimal.ROUND_HALF_UP)); // 实际结算比例
                } else {
                    trdSettle.setSettleRate(BigDecimal.ZERO); // 实际结算比例
                }

                settles.add(trdSettle);
            });
        });
        log.info("保存O2O订单付款后结算信息:" + JSON.toJSONString(settles));
        return settles;
    }
    
    @Override
    public List<TrdSettle> createBatchAfterSettle(List<TrdSupplierAfterDtl> afterDtls) {

        /**
         *  查询售后主订单
         */
        TrdAfter trdAfter = trdAfterMapper.selectById(afterDtls.get(0).getAfterId());

        // 售后单订单
        TrdOrder trdOrder = orderMapper.selectById(trdAfter.getOrderId());

        /**
         * 获取售后入驻商订单明细利润分成信息
         */
        List<Long> afterIds = afterDtls.stream().map(TrdSupplierAfterDtl::getSupplierAfterDtlId).collect(Collectors.toList());
        List<TrdSupplierAfterSettle> supplierAfterSettleList = trdSupplierAfterSettleMapper.selectSettleByAfterDtlId(afterIds);


        Map<Long, TrdSupplierAfterDtl> supplierAfterDtlMap = convertMap(afterDtls, TrdSupplierAfterDtl::getSupplierAfterDtlId);

        /**
         *
         */
        Map<Long, Map<String, BigDecimal>> settleMap = new HashMap<>();
        supplierAfterSettleList.forEach(settle -> {
            Map<String, BigDecimal> map = new HashMap<>();
            //设置软件商结算金额
            map.put(MerchantTypeEnum.SOFTWARE.getType(), ToolUtil.isEmpty(settle.getSoftwareAmt()) ? BigDecimal.ZERO : settle.getSoftwareAmt());
            map.put(MerchantTypeEnum.PARTNER.getType(), settle.getPartnerAmt());
            map.put(MerchantTypeEnum.DC.getType(), settle.getDcAmt());
            if (ToolUtil.isNotEmpty(trdAfter.getColonelId()))
                map.put(MerchantTypeEnum.COLONEL.getType() + "2", settle.getColonel2Amt());

            if (ToolUtil.isNotEmpty(trdAfter.getPcolonelId())) {
                if (Objects.equals(trdAfter.getPcolonelId(), trdAfter.getColonelId())) {
                    map.put(MerchantTypeEnum.COLONEL.getType() + "2", settle.getColonel1Amt().add(settle.getColonel2Amt()));
                } else {
                    map.put(MerchantTypeEnum.COLONEL.getType() + "1", settle.getColonel1Amt());
                }
            }
            settleMap.put(settle.getSupplierOrderDtlId(), map);
        });

        log.info("保存订单付款后结算信息:" + trdAfter.toString());

        //平台商信息 获取关联软件商ID
        PartnerDto partnerDto = trdCacheService.getPartnerDto(trdAfter.getSysCode() + "");


        List<TrdSettle> settles = new ArrayList<>();
        supplierAfterSettleList.forEach(dtlSettle -> {
            Map<String, BigDecimal> map = settleMap.get(dtlSettle.getSupplierOrderDtlId());
            TrdSupplierAfterDtl afterDtl = supplierAfterDtlMap.get(dtlSettle.getSupplierAfterDtlId());
            map.forEach((k, v) -> {
                TrdSettle trdSettle = new TrdSettle();
                trdSettle.setSysCode(trdAfter.getSysCode());
                trdSettle.setOrderId(trdAfter.getOrderId());
                trdSettle.setOrderNo(trdOrder.getOrderNo());
                trdSettle.setAfterId(trdAfter.getAfterId());
                trdSettle.setAfterNo(trdAfter.getAfterNo());

                trdSettle.setSupplierOrderId(dtlSettle.getSupplierAfterId());
                trdSettle.setSupplierOrderNo(dtlSettle.getSupplierAfterNo());
                trdSettle.setSupplierOrderDtlId(dtlSettle.getSupplierAfterDtlId());
                trdSettle.setSupplierOrderDtlNo(dtlSettle.getSupplierAfterDtlNo());
                trdSettle.setState(StatusConstants.SETTLE_STATE_0);
                if (k.contains(MerchantTypeEnum.SOFTWARE.getType())) { // 软件商
                    trdSettle.setMerchantType(MerchantTypeEnum.SOFTWARE.getType());
                    trdSettle.setMerchantId(partnerDto.getSoftwareId());
                }
                if (k.contains(MerchantTypeEnum.PARTNER.getType())) { // 平台商
                    trdSettle.setMerchantType(MerchantTypeEnum.PARTNER.getType());
                    trdSettle.setMerchantId(trdAfter.getSysCode());
                }
                if (k.contains(MerchantTypeEnum.DC.getType())) { // 运营商
                    trdSettle.setMerchantType(MerchantTypeEnum.DC.getType());
                    trdSettle.setMerchantId(trdAfter.getDcId());
                }
                if (k.contains(MerchantTypeEnum.COLONEL.getType() + "1") && ToolUtil.isNotEmpty(trdAfter.getPcolonelId())) { // 业务员负责人
                    trdSettle.setMerchantType(MerchantTypeEnum.COLONEL.getType());
                    trdSettle.setMerchantId(trdAfter.getPcolonelId());
                }
                if (k.contains(MerchantTypeEnum.COLONEL.getType() + "2") && ToolUtil.isNotEmpty(trdAfter.getColonelId())) { // 业务员
                    trdSettle.setMerchantType(MerchantTypeEnum.COLONEL.getType());
                    trdSettle.setMerchantId(trdAfter.getColonelId());
                }
                trdSettle.setSettleAmt(v.negate());
                trdSettle.setSupplierId(afterDtl.getSupplierId());
                trdSettle.setPlatform(trdAfter.getPlatform());
                if (NumberUtil.isGreater(dtlSettle.getProfit(), BigDecimal.ZERO)) {
                    trdSettle.setSettleRate(v.divide(dtlSettle.getProfit(), 4, BigDecimal.ROUND_HALF_UP)); // 实际结算比例
                } else {
                    trdSettle.setSettleRate(BigDecimal.ZERO); // 实际结算比例
                }
                settles.add(trdSettle);
            });
        });
        return settles;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderSettleState(List<Long> settleIds) {
        settleIds.stream().forEach(id -> {
            TrdSettle settle = trdSettleMapper.selectById(id);
            if (ToolUtil.isEmpty(settle)) throw exception(TRD_SETTLE_NOT_EXISTS);
            settle.setState(StatusConstants.SETTLE_STATE_1);
            settle.setSettleTime(DateUtils.getNowDate());
            log.info("订单{}结算成功！", settle);
            trdSettleMapper.updateById(settle);
        });
    }

    @Override
    public PageResult<OrderSettleResDTO> getOrderSettleInfoPage(OrderSettlePageVO pageVO) {
        Page<OrderSettlePageVO> page = new Page<>(pageVO.getPageNo(), pageVO.getPageSize(), pageVO.isCount());
        Page<OrderSettleResDTO> resDto = trdSettleMapper.getOrderSettleInfoPage(pageVO, page);
        resDto.getRecords().forEach(settle -> {
            SpuDTO spuDTO = trdCacheService.getSpuDTO(settle.getSpuId());
            if (ToolUtil.isEmpty(spuDTO)) return;
            settle.setItemNo(spuDTO.getSpuNo());
            settle.setItemName(spuDTO.getSpuName());

            // 入驻商信息
            SupplierDTO supplier = trdCacheService.getSupplierDTO(settle.getSupplierId());
            if (ToolUtil.isNotEmpty(supplier)) {
                settle.setSupplierName(supplier.getSupplierName());
            }
        });
        return new PageResult<>(resDto.getRecords(), resDto.getTotal());
    }

    @Override
    public List<OrderSupplierSettleResDTO> getSupperOrderSettleByDate(OrderSupplierSettlePageVo pageVO) {
        return trdSettleMapper.getSupperOrderSettleByDate(pageVO);
    }

    @Override
    public List<OrderSupplierSettleResDTO> getSupperOrderSettleDtlByDateTime(OrderSupplierSettlePageVo pageVO) {
        return trdSettleMapper.getSupperOrderSettleDtlByDateTime(pageVO);
    }

    @Override
    public PageResult<OrderSettleColonelResDTO> getColonelOrderSettleInfoPage(OrderSettlePageVO pageVO) {
        Page<OrderSettlePageVO> page = new Page<>(pageVO.getPageNo(), pageVO.getPageSize());
        Page<OrderSettleColonelResDTO> resDto = trdSettleMapper.getColonelOrderSettleInfoPage(pageVO, page);
        List<OrderDtlSettleResDTO> orderDtlSettleResDTOS = trdSettleMapper.getColonelOrderSettleDtlInfo(pageVO);

        Map<Long, List<OrderDtlSettleResDTO>> orderDtlSettleMap = orderDtlSettleResDTOS.stream().collect(Collectors.groupingBy(OrderDtlSettleResDTO::getOrderId));
        resDto.getRecords().forEach(orderSettle -> {
            BranchDTO branch = trdCacheService.getBranchDTO(orderSettle.getBranchId());
            if (ToolUtil.isNotEmpty(branch)) {
                orderSettle.setBranchName(branch.getBranchName());
            }
            orderSettle.setOrderDtlSettleResDTOS(orderDtlSettleMap.get(orderSettle.getOrderId()))
                    .setSkuNum(orderSettle.getOrderDtlSettleResDTOS().size())
                    .setPayAmtTotal(BigDecimal.ZERO)
                    .setSettleTotal(BigDecimal.ZERO)
            ;

            orderSettle.getOrderDtlSettleResDTOS().forEach(dtl -> {
                SpuDTO spuDTO = trdCacheService.getSpuDTO(dtl.getSpuId());
                if (ToolUtil.isNotEmpty(spuDTO)) {
                    dtl.setSpuName(spuDTO.getSpuName());
                }
                orderSettle.setPayAmtTotal(orderSettle.getPayAmtTotal().add(dtl.getItemAmt()))
                        .setSettleTotal(orderSettle.getSettleTotal().add(dtl.getSettleAmt()))
                ;
            });
        });
        return new PageResult<>(resDto.getRecords(), resDto.getTotal());
    }

    @Override
    public void cancelSupplierFreezeFlow(List<TrdSettle> settles, TrdSupplierAfterDtl afterDtl) {
        TrdAfter trdAfter = trdAfterMapper.selectById(afterDtl.getAfterId());
        List<AccAccountFlowDTO> flowDTOS = createAccountFreezeFlow(trdAfter, settles);

        if (ToolUtil.isEmpty(flowDTOS) || flowDTOS.isEmpty() || PayChannelEnum.isB2b(trdAfter.getPlatform())) {
            log.info("售后订单【" + trdAfter.getAfterNo() + "】、支付方式【" + trdAfter.getPlatform() + "】无入驻商解冻流水！");
            return;
        }
        // 取消入驻商账户冻结流水
        accountApi.saveAccountFlowAndProcess(flowDTOS).checkError();

    }

    @Override
    public ColonelFixSettleTotalRespVO getColonelSettleTotal(Long colonelId) {
        return trdSettleMapper.selectColonelSettleTotal(colonelId);
    }

    @Override
    public ColonelFloatSettleTotalRespVO getColonelSettleTotalRange(ColonelFloatSettleTotalReqVO reqVO) {
        ColonelFloatSettleTotalRespVO colonelFloatSettleTotalRespVO = trdSettleMapper.selectColonelSettleTotalRange(reqVO);
        colonelFloatSettleTotalRespVO.setDayTotalList(trdSettleMapper.selectColonelSettleDayTotalByDateAndColonel(reqVO));
        return colonelFloatSettleTotalRespVO;
    }


    @Override
    public List<TrdSettle> getTrdSettleListByOrderId(Long orderId) {
        return trdSettleMapper.getSettleListByOrderId(orderId);
    }

    /**
     * 创建入驻商账户取消冻结流水
     *
     * @param trdAfter
     * @param settles
     * @return
     */
    private List<AccAccountFlowDTO> createAccountFreezeFlow(TrdAfter trdAfter, List<TrdSettle> settles) {
        List<AccAccountFlowDTO> flowDTOS = new ArrayList<>();
        // 根据售后入驻商ID进行分组，区分解冻入驻商信息

        Map<Long, List<TrdSettle>> settleMap = settles.stream().collect(Collectors.groupingBy(TrdSettle::getSupplierId));
//        // 入驻商本次取消冻结总金额
        Map<Long, BigDecimal> profitAmtMap = settles.stream().collect(Collectors.groupingBy(TrdSettle::getSupplierId,
                Collectors.reducing(
                        BigDecimal.ZERO,
                        TrdSettle::getSettleAmt,
                        BigDecimal::add
                )));
        PayConfigDTO payConfig = trdCacheService.getPayConfigDTO(trdAfter.getSysCode());
        settleMap.forEach((key, value) -> {

            AccAccountFlowDTO flowDTO = new AccAccountFlowDTO();
            flowDTO.setSysCode(trdAfter.getSysCode());
            flowDTO.setBusiType(AccountBusiType.SUPPLIER_CREATE_ORDER_CANCEL.getType());
            flowDTO.setBusiId(trdAfter.getOrderId());
            flowDTO.setBusiFields(AccountBusiTypeField.FROZEN_AMT.getField());
            flowDTO.setPlatform(payConfig.getStoreOrderPayPlatform());
            flowDTO.setMerchantType(MerchantTypeEnum.SUPPLIER.getType());
            flowDTO.setMerchantId(key);
            flowDTO.setBusiFrozenAmt(profitAmtMap.get(key));

            flowDTOS.add(flowDTO);
        });
        return flowDTOS;
    }


    /**
     * 更新订单结算流水状态 订单销售数量 == 订单发货前取消数量
     *
     * @param afterDtls
     */
    @Override
    public void updateSettle(List<TrdSupplierAfterDtl> afterDtls) {
        // 根据售后订单明细 查询出对应的订单明细 数据  订单销售数量 == 订单发货前取消数量
        List<TrdSupplierOrderDtl> orderDtlList = supplierOrderDtlMapper.selectListBySupplierOrderDtlId(afterDtls.stream().map(TrdSupplierAfterDtl::getSupplierOrderDtlId).collect(Collectors.toList()));
        if (ToolUtil.isEmpty(orderDtlList)) return;

        // 根据订单明细ID 查询 这个订单明细的所有售后信息
        List<TrdSupplierAfterDtl> afterDtlList = supplierAfterDtlMapper.selectListBySupplierDtlIdList(orderDtlList.stream().map(TrdSupplierOrderDtl::getSupplierOrderDtlId).collect(Collectors.toList()));
        if (ToolUtil.isEmpty(afterDtlList)) return;

        Set<Long> dtlList = new HashSet<>();
        dtlList.addAll(orderDtlList.stream().map(TrdSupplierOrderDtl::getSupplierOrderDtlId).collect(Collectors.toSet()));
        dtlList.addAll(afterDtlList.stream().map(TrdSupplierAfterDtl::getSupplierAfterDtlId).collect(Collectors.toSet()));

        // 查询出需结算信息数据
        List<TrdSettle> settleList = trdSettleMapper.selectSettleBydtlIdList(dtlList);
        if (ToolUtil.isEmpty(settleList)) return;

        // 更新结算状态
        updateOrderSettleState(settleList.stream().map(TrdSettle::getSettleId).collect(Collectors.toList()));
    }

    @Override
    public List<TrdSettle> getSettleBySupplierOrderDtlId(List<Long> supplierOrderDtlIdList) {
        return trdSettleMapper.getSettleBySupplierOrderDtlId(supplierOrderDtlIdList);
    }

    @Override
    public PageResult<OrderCommissionStatementRespVO> getStatementOfAccountPage(OrderCommissionStatementReqVO pageVO) {
        Page<OrderCommissionStatementReqVO> page = new Page<>(pageVO.getPageNo(), pageVO.getPageSize(), pageVO.isCount());
        //判断是否是导出
        LoginUser loginUser = pageVO.getIsExport() == null ? SecurityUtils.getLoginUser() : pageVO.getLoginUser();
        Long loginSupplierId = pageVO.getIsExport() == null ? SecurityUtils.getSupplierId() : pageVO.getLoginSupplierId();
        Long loginDcId = pageVO.getIsExport() == null ? SecurityUtils.getDcId() : pageVO.getLoginDcId();
        if (ToolUtil.isEmpty(pageVO.getPlatform())) {
            pageVO.setPlatform("wxb2b");
        }
        if (ToolUtil.isNotEmpty(loginUser.getSysCode()) && ToolUtil.isEmpty(pageVO.getSysCode())) {
            pageVO.setSysCode(loginUser.getSysCode());
        }
        if (ToolUtil.isNotEmpty(loginUser.getSysCodeList())) {
            pageVO.setSysCodes(loginUser.getSysCodeList());
        }
        if (ToolUtil.isNotEmpty(loginSupplierId)) {
            pageVO.setSupplierId(loginSupplierId);
        }
        if (ToolUtil.isNotEmpty(loginDcId)) {
            List<Long> dcSupplierList = dcApi.getDcSupplierList(loginDcId).getCheckedData();
            if (ToolUtil.isNotEmpty(dcSupplierList)) {
                pageVO.setSupplierIds(dcSupplierList);
            }
        }
        if (ToolUtil.isNotEmpty(pageVO.getSettleStatus())) {
            pageVO.setSettleStatus(pageVO.getSettleStatus().equals("0") ? "未结算" : "已结算");
        }
        // 初始化缓存
        LFUCache<Long, DcDTO> dcCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, PartnerDto> partnerCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, SoftwareDTO> softwareCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, SupplierDTO> supplierCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, ColonelDTO> colonelCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, BranchDTO> branchCache = CacheUtil.newLFUCache(0);


        Page<OrderCommissionStatementRespVO> resDto = trdSettleMapper.getStatementOfAccountPage(pageVO, page);
        resDto.getRecords().forEach(settle -> {
            // 使用缓存获取 Supplier 信息
            SupplierDTO supplier = supplierCache.get(settle.getSupplierId(),
                    () -> trdCacheService.getSupplierDTO(settle.getSupplierId()));
            settle.setSupplierName(supplier != null ? supplier.getSupplierName() : null);

            // 使用缓存获取 Colonel 信息
            Long colonelId = settle.getColonelId();
            if (colonelId != null) {
                ColonelDTO colonel = colonelCache.get(colonelId,
                        () -> trdCacheService.getColonelDTO(colonelId));
                settle.setColonelName(colonel != null ? colonel.getColonelName() : null);
            } else {
                settle.setColonelName(null);  // 或者设置为默认值
            }

            // 使用缓存获取 DC 信息
            DcDTO dc = dcCache.get(settle.getDcId() != null ? settle.getDcId() : null,
                    () -> trdCacheService.getDcDTO(settle.getDcId()));
            settle.setDcName(dc != null ? dc.getDcName() : null);

            // 使用缓存获取 Partner 信息
            PartnerDto partner = partnerCache.get(settle.getSysCode(),
                    () -> trdCacheService.getPartnerDto(settle.getSysCode() + ""));
            settle.setPartnerName(partner != null ? partner.getPartnerName() : null);

            // 使用缓存获取 Software 信息
            SoftwareDTO software = softwareCache.get(partner != null ? partner.getSoftwareId() : null,
                    () -> partner != null ? softwareApi.getById(partner.getSoftwareId()).getCheckedData() : null);
            settle.setSoftwareName(software != null ? software.getSoftwareName() : null);

            if (ToolUtil.isNotEmpty(settle.getBranchId())) {
                BranchDTO branch = branchCache.get(settle.getBranchId(),
                        () -> trdCacheService.getBranchDTO(settle.getBranchId()));
                settle.setBranchName(branch != null ? branch.getBranchName() : null);
            } else {
                settle.setBranchName(null);  // 或者设置为默认值
            }
            settle.setDeliveryStateName(DeliveryStatusEnum.getDeliveryStatus(settle.getDeliveryState()).getName());


            // 判断是否为售后单
            boolean isAfterSale = ToolUtil.isNotEmpty(settle.getAfterNo());

            // 计算总得分润金额
            BigDecimal totalSettleAmt = "已结算".equals(settle.getSettleStatus()) ? settle.getSettledAmt() : settle.getSettleAmt();
            if (isAfterSale) {
                totalSettleAmt = totalSettleAmt.negate();  // 售后单总得分润金额为负数
            }

            // 计算各个角色的分润金额
            BigDecimal softwareSettleAmt = "已结算".equals(settle.getSettleStatus()) ? settle.getSoftwareSettledAmt() == null ? BigDecimal.ZERO : settle.getSoftwareSettledAmt() : settle.getSoftwareSettleAmt() == null ? BigDecimal.ZERO : settle.getPartnerSettleAmt();
            BigDecimal partnerSettleAmt = "已结算".equals(settle.getSettleStatus()) ? settle.getPartnerSettledAmt() == null ? BigDecimal.ZERO : settle.getPartnerSettledAmt() : settle.getPartnerSettleAmt() == null ? BigDecimal.ZERO : settle.getPartnerSettleAmt();
            BigDecimal dcSettleAmt = "已结算".equals(settle.getSettleStatus()) ? settle.getDcSettledAmt() == null ? BigDecimal.ZERO : settle.getDcSettledAmt() : settle.getDcSettleAmt() == null ? BigDecimal.ZERO : settle.getDcSettleAmt();
            BigDecimal colonelSettleAmt = "已结算".equals(settle.getSettleStatus()) ? settle.getColonelSettledAmt() == null ? BigDecimal.ZERO : settle.getColonelSettledAmt() : settle.getColonelSettleAmt() == null ? BigDecimal.ZERO : settle.getColonelSettleAmt();

            if (isAfterSale) {
                softwareSettleAmt = softwareSettleAmt == null ? BigDecimal.ZERO : softwareSettleAmt.negate();  // 售后单各角色分润金额为负数
                partnerSettleAmt = partnerSettleAmt == null ? BigDecimal.ZERO : partnerSettleAmt.negate();
                dcSettleAmt = dcSettleAmt == null ? BigDecimal.ZERO : dcSettleAmt.negate();
                colonelSettleAmt = colonelSettleAmt == null ? BigDecimal.ZERO : colonelSettleAmt.negate();
            }

            // 计算分润比例（忽略金额符号）
            BigDecimal absTotalSettleAmt = totalSettleAmt.abs();
            if (absTotalSettleAmt.compareTo(BigDecimal.ZERO) > 0) {
                settle.setSoftwareSettleRatio(softwareSettleAmt.abs().divide(absTotalSettleAmt, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                settle.setPartnerSettleRatio(partnerSettleAmt.abs().divide(absTotalSettleAmt, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                settle.setDcSettleRatio(dcSettleAmt.abs().divide(absTotalSettleAmt, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                settle.setColonelSettleRatio(colonelSettleAmt.abs().divide(absTotalSettleAmt, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
            } else {
                settle.setSoftwareSettleRatio(BigDecimal.ZERO);
                settle.setPartnerSettleRatio(BigDecimal.ZERO);
                settle.setDcSettleRatio(BigDecimal.ZERO);
                settle.setColonelSettleRatio(BigDecimal.ZERO);
            }

        });
        return new PageResult<>(resDto.getRecords(), resDto.getTotal());
    }

    @Override
    public List<TrdSettle> getTrdSettleListByOrderIdAndMerchant(Long orderId, String merchantType, Long merchantId) {
        return trdSettleMapper.getTrdSettleListByOrderIdAndMerchant(orderId, merchantType, merchantId);
    }

    @Override
    public List<TrdSettle> getSettlingBySupplierOrderNo(String supplierOrderNo, String merchantType) {
        log.info("查询订单{}未结算记录，商户类型：{}！", supplierOrderNo, merchantType);
        LambdaQueryWrapperX<TrdSettle> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(TrdSettle::getSupplierOrderNo, supplierOrderNo)
                    .eq(TrdSettle::getMerchantType, merchantType)
                    .eq(TrdSettle::getState, StatusConstants.SETTLE_STATE_2) // 结算中状态
                    .isNull(TrdSettle::getAfterId); // afterId为空，表示非售后订单
        
        return trdSettleMapper.selectList(queryWrapper);
    }

    private void validateTrdSettleExists(Long settleId) {
        if (trdSettleMapper.selectById(settleId) == null) {
            throw exception(TRD_SETTLE_NOT_EXISTS);
        }
    }
    
    /**
     * 异步O2O门店分账处理
     *
     * @param bizId 业务ID
     */
    @Override
    public void asynO2OBranchDivide(String bizId) {
        CompletableFuture.runAsync(() -> {
			Long divideDtlId = null;
			try {
                divideDtlId = Long.parseLong(bizId);
			} catch (NumberFormatException e) {
                log.info("异步O2O门店分账处理，业务ID：{}，解析异常！e：{}", bizId,e);
				return ;
			}
			// 1. 根据 bizId 查询分账详情
            AccDivideDtlDTO dto = AccDivideDtlDTO.builder()
              .divideDtlId(Long.parseLong(bizId))
              .build();
            AccDivideDtlDTO divideDtl = divideFlowApi.getAccDivideDtlById(dto).getCheckedData();
            if (divideDtl == null) {
                log.info("获取分账详情失败, bizId:{}", bizId);
                return;
            }
            
            String merchantType = divideDtl.getMerchantType();
            if (!MerchantTypeEnum.BRANCH_PROFIT.getType().equals(merchantType)) {
                log.info("分账详情merchantType不是门店分账类型, bizId:{}", bizId);
                return;
            }
            
            // 2. 根据 tradeNo 查询入驻商订单信息
            String supplierOrderNo = divideDtl.getTradeNo();
            if (ToolUtil.isEmpty(supplierOrderNo)) {
                log.info("分账详情中supplierOrderNo为空, bizId:{}", bizId);
                return;
            }
            
            // 3.根据 supplierOrderNo + 待结算 + 门店分账类型
            List<TrdSettleDTO> trdSettleDTOS = trdSettleMapper.selectO2OSettleListBySupplierOrderNo(
                    StatusConstants.SETTLE_STATE_3, divideDtl.getSysCode(), supplierOrderNo,
                    MerchantTypeEnum.BRANCH_PROFIT.getType());
            
            // 4、执行O2O分账
            orderService.orderO2OSignAfterSettle(trdSettleDTOS);
        });
        
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 订单结算 TODO 补充编号 ==========
    // ErrorCode TRD_SETTLE_NOT_EXISTS = new ErrorCode(TODO 补充编号, "订单结算不存在");


}
