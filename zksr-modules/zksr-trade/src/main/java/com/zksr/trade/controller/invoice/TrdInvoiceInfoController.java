package com.zksr.trade.controller.invoice;

import javax.validation.Valid;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.system.api.invoice.dto.InvoiceCallbackDTO;
import com.zksr.trade.api.invoice.InvoiceApi;
import com.zksr.trade.api.invoice.dto.InvoiceProcessDTO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdInvoiceInfo;
import com.zksr.trade.service.ITrdInvoiceInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.invoice.vo.TrdInvoiceInfoPageReqVO;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceInfoSaveReqVO;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceInfoRespVO;
import com.zksr.trade.convert.info.TrdInvoiceInfoConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 发票主Controller
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Api(tags = "管理后台 - 发票主接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/invoiceInfo")
public class TrdInvoiceInfoController {
    @Autowired
    private ITrdInvoiceInfoService trdInvoiceInfoService;

    @Autowired
    private InvoiceApi invoiceApi;

    /**
     * 新增发票主
     */
    @ApiOperation(value = "新增发票主", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "发票主", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdInvoiceInfoSaveReqVO createReqVO) {
        return success(trdInvoiceInfoService.insertTrdInvoiceInfo(createReqVO));
    }

    /**
     * 修改发票主
     */
    @ApiOperation(value = "修改发票主", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "发票主", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdInvoiceInfoSaveReqVO updateReqVO) {
        trdInvoiceInfoService.updateTrdInvoiceInfo(updateReqVO);
        return success(true);
    }

    /**
     * 删除发票主
     */
    @ApiOperation(value = "删除发票主", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "发票主", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public CommonResult<Boolean> remove(@PathVariable Long[] ids) {
        trdInvoiceInfoService.deleteTrdInvoiceInfoByIds(ids);
        return success(true);
    }

    /**
     * 获取发票主详细信息
     */
    @ApiOperation(value = "获得发票主详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{id}")
    public CommonResult<TrdInvoiceInfoRespVO> getInfo(@PathVariable("id") Long id) {
        TrdInvoiceInfo trdInvoiceInfo = trdInvoiceInfoService.getTrdInvoiceInfo(id);
        return success(TrdInvoiceInfoConvert.INSTANCE.convert(trdInvoiceInfo));
    }

    /**
     * 分页查询发票主
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得发票主分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdInvoiceInfoRespVO>> getPage(@Valid TrdInvoiceInfoPageReqVO pageReqVO) {
        PageResult<TrdInvoiceInfo> pageResult = trdInvoiceInfoService.getTrdInvoiceInfoPage(pageReqVO);
        return success(TrdInvoiceInfoConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 开票接口
     *
     * @param invoiceProcessDTO
     * @return
     */
    @PostMapping(value = "/blueInvoiceProcess")
    public CommonResult<Boolean> blueInvoiceProcess(@RequestBody InvoiceProcessDTO invoiceProcessDTO) {

        return invoiceApi.blueInvoiceProcess(invoiceProcessDTO);
    }


    /**
     * 开票回调
     *
     * @param invoiceCallbackDTO
     * @return
     */

    @PostMapping(value = "/invoiceCallback")
    public CommonResult<Boolean> invoiceCallback(@RequestBody InvoiceCallbackDTO invoiceCallbackDTO) {

        return invoiceApi.invoiceCallback(invoiceCallbackDTO);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "trade:info:add";
        /**
         * 编辑
         */
        public static final String EDIT = "trade:info:edit";
        /**
         * 删除
         */
        public static final String DELETE = "trade:info:remove";
        /**
         * 列表
         */
        public static final String LIST = "trade:info:list";
        /**
         * 查询
         */
        public static final String GET = "trade:info:query";
        /**
         * 停用
         */
        public static final String DISABLE = "trade:info:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "trade:info:enable";
    }
}
