/*
 *  Copyright 1999-2021 Seata.io Group.
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.zksr.trade.service;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.uuid.UUID;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.rocketmq.tx.AbstractTxMsgSender;
import com.zksr.product.api.seata.StockFeignClient;
import com.zksr.trade.api.order.dto.TestOrderDto;
import com.zksr.trade.domain.Order;
import com.zksr.trade.mapper.OrderDAO;
import com.zksr.trade.mq.TradeMqProducer;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.LocalTransactionState;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * Program Name: springcloud-nacos-seata
 * <p>
 * Description:
 * <p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/8/28 4:05 PM
 */
@Service
@Slf4j
public class OrderService {

//    @Resource
//    private AccountFeignClient accountFeignClient;
//    @Resource
//    private StockFeignClient stockFeignClient;
    @Resource
    private OrderDAO orderDAO;

    @Autowired
    private TradeMqProducer tradeMqProducer;

    @Autowired
    private AbstractTxMsgSender testOrderTxMsgSender;

    @Autowired
    private AbstractTxMsgSender demoTxMsgSender;

    /**
     * 下单：创建订单、减库存，涉及到两个服务
     *
     * @param userId
     * @param commodityCode
     * @param count
     */
//    @GlobalTransactional
//    @Transactional(rollbackFor = Exception.class)
    public void placeOrder(String userId, String commodityCode, Integer count) {
        BigDecimal orderMoney = new BigDecimal(count).multiply(new BigDecimal(5));
        TestOrderDto order = new TestOrderDto().setUserId(userId).setCommodityCode(commodityCode).setCount(count).setMoney(
            orderMoney).setOrderNo(UUID.fastUUID().toString());

//        tradeMqProducer.sendTestTransacitonMsg(order);
        testOrderTxMsgSender.sendMsg(order);

//        demoTxMsgSender.sendMsg(order.getOrderNo());
//        Order o = orderDAO.selectOne(new LambdaQueryWrapperX<Order>()
//                .eqIfPresent(Order::getOrderNo, order.getOrderNo()));
//        if(ToolUtil.isNotEmpty(o)){
//            log.info("placeOrder 事务确认已提交");
//        }
//        if(ToolUtil.isEmpty(o)){
//            log.info("placeOrder 事务未正确提交");
//        }

//        Order order = new Order().setUserId(userId).setCommodityCode(commodityCode).setCount(count).setMoney(
//            orderMoney).setOrderNo(UUID.fastUUID().toString());
//
//        orderDAO.insert(order);
//        stockFeignClient.deduct(commodityCode, count);

    }

//    @Transactional(rollbackFor = Exception.class)
//    public void create(String userId, String commodityCode, Integer count) {
//
//        BigDecimal orderMoney = new BigDecimal(count).multiply(new BigDecimal(5));
//
//        Order order = new Order().setUserId(userId).setCommodityCode(commodityCode).setCount(count).setMoney(
//            orderMoney);
//        orderDAO.insert(order);
//
//        accountFeignClient.reduce(userId, orderMoney);
//
//    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrder(Order order){
        orderDAO.insert(order);
        int res = 10/0;
    }

}
