package com.zksr.trade.service;

import javax.validation.*;

import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.domain.vo.openapi.receive.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.dto.OrderReceiptRespDTO;
import com.zksr.trade.controller.order.vo.SupplierMemoEditReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderPageReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderSaveReqVO;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

/**
 * 入驻商订单Service接口
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
public interface ITrdSupplierOrderService {

    /**
     * 新增入驻商订单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdSupplierOrder(@Valid TrdSupplierOrderSaveReqVO createReqVO);

    /**
     * 修改入驻商订单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdSupplierOrder(@Valid TrdSupplierOrderSaveReqVO updateReqVO);

    /**
     * 删除入驻商订单
     *
     * @param supplierOrderId 入驻商订单id
     */
    public void deleteTrdSupplierOrder(Long supplierOrderId);

    /**
     * 批量删除入驻商订单
     *
     * @param supplierOrderIds 需要删除的入驻商订单主键集合
     * @return 结果
     */
    public void deleteTrdSupplierOrderBySupplierOrderIds(Long[] supplierOrderIds);

    /**
     * 获得入驻商订单
     *
     * @param supplierOrderId 入驻商订单id
     * @return 入驻商订单
     */
    public TrdSupplierOrder getTrdSupplierOrder(Long supplierOrderId);

    /**
     * 获得入驻商订单分页
     *
     * @param pageReqVO 分页查询
     * @return 入驻商订单分页
     */
    PageResult<TrdSupplierOrder> getTrdSupplierOrderPage(TrdSupplierOrderPageReqVO pageReqVO);

    /**
     * OpenApi销售订单出库回传
     * @param vo
     */
    public void receiveOrderOutboundReturn (OrderOutboundReturnVO vo);
    
    /**
     * 包裹接收
     * @param vo
     */
    public void receivePackage(OrderPackageReturnVO vo);
    
    /**
     * 根据订单编号查询销售订单
     *
     * @param orderNo
     * @return
     */
    List<TrdSupplierOrder> getSupplierOrderListByOrderNo(String orderNo);

    /**
     * 根据入驻商订单编号查询入驻商销售订单
     * @param supplierOrderNo
     * @return
     */
    TrdSupplierOrder getSupplierOrderBySupplierOrderNo(String supplierOrderNo);

    /**
     * 更新订单推送标志
     *
     * @param supplierOrderNo
     * @param pushStatus
     */
    public void updateByPushStatus(String supplierOrderNo,Integer pushStatus);

    /**
     * 批量推送入驻商订单
     * @param supplierOrderNoList
     */
    public String batchSyncSupplierOrder(List<String> supplierOrderNoList);

    /**
     * 校验订单是否是手动推送第三方
     * @param
     */
    public Boolean checkOrderSync();

    /**
     * 订单推送成功后处理
     * @param data
     */
    void syncOrderResponseSuccess(SyncDataDTO data);

    /**
     * 更新入驻商订单备注
     * @param reqVO
     */
    void supplierOrderMemoEdit(SupplierMemoEditReqVO reqVO);

    /**
     * 订单回调更新同步库存信息
     * @param vo
     */
    void orderReceiveCallback(OrderReceiveCallbackVO vo);

    /**
     * 根据供应商订单号查询供应商订单收款信息
     * @param reqVo
     * @return
     */
    List<OrderReceiptRespDTO> getOrderReceiptInfoBySupplierOrderNo(SyncReceiptSendDTO reqVo);


    /**
     * 根据供应商订单号查询供应商订单收款信息(销售订单、差异出库、拒收)
     * @param reqVo
     * @return
     */
    List<OrderReceiptRespDTO> getOrderAfterReceiptInfoBySupplierOrderNo(SyncReceiptSendDTO reqVo);

    /**
     * 订单发货前取消
     * @param vo
     */
    void orderCancel(OrderCancelVO vo);

    /**
     * 删除商品信息 前置校验 需要删除的商品是否下单
     *
     * @param spuIds 需要校验的SPUID集合
     * @return 已经下过单的SPUID集合
     */
    List<Long> deleteSpuCheckSupplierOrderDtl(Long[] spuIds);

    /**
     * 订单取消回调
     * @param vo
     */
    void orderCancelReceiveCallback(OrderCancelReceiveCallbackVO vo);
}
