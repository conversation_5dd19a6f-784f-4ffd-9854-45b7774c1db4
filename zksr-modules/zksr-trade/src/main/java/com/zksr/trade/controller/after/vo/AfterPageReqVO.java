package com.zksr.trade.controller.after.vo;

import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年04月22日 12:32
 * @description: AfterOrderPageReqVO
 */
@ApiModel("售后单 - trd_after分页 Request VO")
@Data
public class AfterPageReqVO extends PageParam {

    @ApiModelProperty(value = "售后开始时间")
    private String startDate;

    @ApiModelProperty(value = "售后结束时间")
    private String endDate;

    @ApiModelProperty(value = "售后单号")
    private String afterNo;

    @ApiModelProperty(value = "入驻商售后单号")
    private String supplierAfterNo;

    @ApiModelProperty(value = "订单单号")
    private String orderNo;

    @ApiModelProperty(value = "单据类型")
    private Long productType;

//    @ApiModelProperty(value = "单据状态{0：全部，1：待处理，2：处理中，3：已处理（完成）}")
//    private Long approveState;

    @ApiModelProperty(value = "处理中状态{0：(处理中状态全部)，1：待处理， 2：未退货，3：退货中，4：已退货，5：待退款，6：退款中，7：退款失败, 8:已处理（完成），99：可确认退款， 100：发货前售后单据}")
    private Long handleState;

    @ApiModelProperty(value = "入驻商ID", hidden = true)
    private Long supplierId;

    @ApiModelProperty(value = "售后订单单号")
    private Long afterId;

    @ApiModelProperty(value = "入驻商售后订单外部订单号")
    private String sourceOrderNo;

    @ApiModelProperty(value = "门店Id")
    private Long branchId;

}
