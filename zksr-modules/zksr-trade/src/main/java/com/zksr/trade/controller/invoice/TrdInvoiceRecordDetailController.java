package com.zksr.trade.controller.invoice;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdInvoiceRecordDetail;
import com.zksr.trade.service.ITrdInvoiceRecordDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.invoice.vo.TrdInvoiceRecordDetailPageReqVO;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceRecordDetailSaveReqVO;
import com.zksr.trade.controller.invoice.vo.TrdInvoiceRecordDetailRespVO;
import com.zksr.trade.convert.detail.TrdInvoiceRecordDetailConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 发票明细Controller
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Api(tags = "管理后台 - 发票明细接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/detail")
public class TrdInvoiceRecordDetailController {
    @Autowired
    private ITrdInvoiceRecordDetailService trdInvoiceRecordDetailService;

    /**
     * 新增发票明细
     */
    @ApiOperation(value = "新增发票明细", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "发票明细", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdInvoiceRecordDetailSaveReqVO createReqVO) {
        return success(trdInvoiceRecordDetailService.insertTrdInvoiceRecordDetail(createReqVO));
    }

    /**
     * 修改发票明细
     */
    @ApiOperation(value = "修改发票明细", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "发票明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdInvoiceRecordDetailSaveReqVO updateReqVO) {
            trdInvoiceRecordDetailService.updateTrdInvoiceRecordDetail(updateReqVO);
        return success(true);
    }

    /**
     * 删除发票明细
     */
    @ApiOperation(value = "删除发票明细", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "发票明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public CommonResult<Boolean> remove(@PathVariable Long[] ids) {
        trdInvoiceRecordDetailService.deleteTrdInvoiceRecordDetailByIds(ids);
        return success(true);
    }

    /**
     * 获取发票明细详细信息
     */
    @ApiOperation(value = "获得发票明细详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{id}")
    public CommonResult<TrdInvoiceRecordDetailRespVO> getInfo(@PathVariable("id") Long id) {
        TrdInvoiceRecordDetail trdInvoiceRecordDetail = trdInvoiceRecordDetailService.getTrdInvoiceRecordDetail(id);
        return success(TrdInvoiceRecordDetailConvert.INSTANCE.convert(trdInvoiceRecordDetail));
    }

    /**
     * 分页查询发票明细
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得发票明细分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdInvoiceRecordDetailRespVO>> getPage(@Valid TrdInvoiceRecordDetailPageReqVO pageReqVO) {
        PageResult<TrdInvoiceRecordDetail> pageResult = trdInvoiceRecordDetailService.getTrdInvoiceRecordDetailPage(pageReqVO);
        return success(TrdInvoiceRecordDetailConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:detail:add";
        /** 编辑 */
        public static final String EDIT = "trade:detail:edit";
        /** 删除 */
        public static final String DELETE = "trade:detail:remove";
        /** 列表 */
        public static final String LIST = "trade:detail:list";
        /** 查询 */
        public static final String GET = "trade:detail:query";
        /** 停用 */
        public static final String DISABLE = "trade:detail:disable";
        /** 启用 */
        public static final String ENABLE = "trade:detail:enable";
    }
}
