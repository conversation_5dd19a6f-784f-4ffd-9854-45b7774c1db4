package com.zksr.trade.service;

import javax.validation.*;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.api.after.vo.OrderMergeAfterRequest;
import com.zksr.trade.api.supplierOrder.dto.OrderDiscountDtlDTO;
import com.zksr.trade.domain.TrdOrderDiscountDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;

import java.util.List;

/**
 * 订单优惠明细Service接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
public interface ITrdOrderDiscountDtlService {

    /**
     * 获取入驻商订单的优惠信息
     * @param supplierOrderId
     * @return
     */
   public List<OrderDiscountDtlDTO> getListBySupplierOrderId(Long supplierOrderId);


}
