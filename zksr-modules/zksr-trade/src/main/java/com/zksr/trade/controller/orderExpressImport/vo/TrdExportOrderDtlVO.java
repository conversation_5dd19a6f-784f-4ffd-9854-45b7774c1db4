package com.zksr.trade.controller.orderExpressImport.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月16日 15:21
 * @description: TrdExportOrderDtlVO
 */
@Data
@ApiModel("入驻商全国订单商品请求实体")
public class TrdExportOrderDtlVO {
    @ApiModelProperty(value = "操作类型  0：全部 ，1：按日期", required = false)
    private Integer operType;

    @ApiModelProperty(value = "日期集合")
    private List<String> dates;

    @ApiModelProperty(value = "入驻商编号" , hidden = true)
    private Long supplierId;
}
