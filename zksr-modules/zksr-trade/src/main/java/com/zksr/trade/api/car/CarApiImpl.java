package com.zksr.trade.api.car;

import com.zksr.common.core.domain.vo.car.AppCarPageReqVO;
import com.zksr.common.core.domain.vo.car.AppCarPageRespVO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.redis.service.RedisCarService;
import com.zksr.trade.api.car.dto.AppCarEventDTO;
import com.zksr.trade.api.car.dto.AppCarInitDTO;
import com.zksr.trade.api.car.vo.TrdCarApiRespVO;
import com.zksr.trade.convert.car.TrdCarConvert;
import com.zksr.trade.mq.TradeMqProducer;
import com.zksr.trade.service.ITrdCarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2024/3/27
 * @desc
 */
@RestController
@ApiIgnore
public class CarApiImpl implements CarApi {

    @Autowired
    private TradeMqProducer productCarMqChannel;

    @Autowired
    private ITrdCarService trdCarService;

    @Autowired
    private RedisCarService redisCarService;

    /**
     * 发送购物车事件
     * @param carEvent 事件
     * @return
     */
    @Override
    public CommonResult<Boolean> carEvent(AppCarEventDTO carEvent) {
        productCarMqChannel.sendProductCarEvent(carEvent);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<List<AppCarInitDTO>> getInitData(Long branchId, Long minId) {
        return CommonResult.success(trdCarService.getInitData(branchId, minId));
    }

    @Override
    public List<TrdCarApiRespVO> getTrdCarList(Long branchId) {
        //return trdCarService.getTrdCarList(branchId);

        AppCarPageReqVO vo = new AppCarPageReqVO();
        vo.setPageNo(1);
        vo.setPageSize(999);
        vo.setBranchId(branchId);
        AppCarPageRespVO checkedData = getTrdCacheCarItemPageList(vo).getCheckedData();
        List<TrdCarApiRespVO> convert = TrdCarConvert.INSTANCE.convert(checkedData.getList());
        return Objects.isNull(convert) ? new ArrayList<>() : convert;
    }

    @Override
    public CommonResult<AppCarPageRespVO> getTrdCacheCarItemPageList(AppCarPageReqVO appCarPageReqVO) {
        return CommonResult.success(redisCarService.getAppCarItemListPage(appCarPageReqVO));
    }

    @Override
    public CommonResult<Boolean> saveRedisCar(Long branchId) {
        trdCarService.saveRedisCar(branchId);
        return CommonResult.success(Boolean.TRUE);
    }
}
