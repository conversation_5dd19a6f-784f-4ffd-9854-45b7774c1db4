package com.zksr.trade.service.handler.payWay;

import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveRespVO;
import com.zksr.trade.api.order.dto.OrderPayInfoRespDTO;
import com.zksr.trade.api.order.dto.TrdSupplierResDto;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdSupplierPageVO;
import com.zksr.trade.domain.*;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年02月24日 19:54
 * @description: 订单支付方式 处理器 handler 接口
 *
 */
@Component
public interface ITrdOrderPayWayHandlerService {
    /**
     * 通用处理支付方式
     */
    int ORDER_NONE_ONLINE = 0;
    /**
     * B2B 微信支付（在线支付）
     */
    int ORDER_B2BWX_ONLINE = 1;

    /**
     * B2B 微信支付（货到付款）
     */
    int ORDER_B2BWX_HDFK = 2;

    /**
     * 储值支付（在线支付）
     */
    int ORDER_CZ_ONLINE = 3;

    /**
     * 合利宝支付（在线支付）
     */
    int ORDER_HLB_ONLINE = 4;

    /**
     * 合利宝支付（货到付款）
     */
    int ORDER_HLB_HDFK = 5;

    /**
     * 获取当前支付方式
     * @param platform 支付平台
     * @param payWay 支付方式
     * @return true/false
     */
    public Boolean isPlatform(String platform, String payWay);

    /**
     * 获取当前支付方式
     * @param platform 支付平台
     * @return true/false
     */
    public Boolean isPlatform(String platform);


    /**
     * 订单完成生成转账单
     * @param trdOrder
     */
    default void orderCompleteCreateSettleTransfer(TrdOrder tor, List<TrdSupplierOrder> tsoList, List<TrdSupplierOrderDtl> tsodList, List<TrdSettle> settleList) {}


    /**
     * 支付获取订单结算分账数据- 订单支付结算分账数据处理（在线支付）
     * @param payInfoList 订单支付信息
     * @param supplierItemMap 入驻商商品信息集合
     * @param pageVo
     * @param settles
     * @param respDTO
     */
    default void orderSettleAccountInfo(List<TrdSupplierResDto> payInfoList, Map<Long, List<TrdSupplierOrderDtl>> supplierItemMap,
                                        TrdSupplierPageVO pageVo, List<TrdSettle> settles, OrderPayInfoRespDTO respDTO) {}

    /**
     * 支付获取订单结算分账数据- 订单支付结算分账数据处理（货到付款）
     */
    default void orderHdfkSettleAccountInfo(AccAccountDTO accountDTO, Long supplierId, BigDecimal supplierAmt, BigDecimal itemFree,
                                            List<TrdSettle> settles, PayConfigDTO payConfigDTO, List<HdfkPaySaveRespVO.OrderSettlementDTO> settlementDTOS) {}
}
