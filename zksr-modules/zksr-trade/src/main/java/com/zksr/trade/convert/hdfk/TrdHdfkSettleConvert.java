package com.zksr.trade.convert.hdfk;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkOrderRespVO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkOrderItemVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSkuItemRespVO;
import com.zksr.trade.domain.TrdHdfkSettle;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleRespVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSaveReqVO;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 货到付款结算 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-28
*/
@Mapper
public interface TrdHdfkSettleConvert {

    TrdHdfkSettleConvert INSTANCE = Mappers.getMapper(TrdHdfkSettleConvert.class);

    TrdHdfkSettleRespVO convert(TrdHdfkSettle trdHdfkSettle);

    TrdHdfkSettle convert(TrdHdfkSettleSaveReqVO trdHdfkSettleSaveReq);

    PageResult<TrdHdfkSettleRespVO> convertPage(PageResult<TrdHdfkSettle> trdHdfkSettlePage);

    List<BranchHdfkOrderRespVO.BranchHdfkOrderItem> convertHdfkOrderItemList(List<TrdHdfkOrderItemVO> hdfkOrderItemVOS);

    TrdHdfkSettleSaveReqVO convertHdfkSettleVO(TrdSupplierOrderDtl supplierOrderDtl);

    @Mappings({
            @Mapping(source = "supplierAfterDtl.supplierId", target = "supplierId"),
            @Mapping(source = "trdOrder.branchId", target = "branchId"),
            @Mapping(source = "trdOrder.orderNo", target = "orderNo"),
            @Mapping(source = "trdOrder.orderId", target = "orderId"),
            @Mapping(source = "supplierOrderDtl.supplierOrderNo", target = "supplierOrderNo"),
            @Mapping(source = "supplierOrderDtl.supplierOrderId", target = "supplierOrderId"),
            @Mapping(source = "supplierAfterDtl.supplierOrderDtlId", target = "supplierOrderDtlId"),
            @Mapping(source = "supplierAfterDtl.supplierOrderDtlNo", target = "supplierOrderDtlNo"),
            @Mapping(source = "supplierAfterDtl.supplierAfterDtlNo", target = "supplierAfterDtlNo"),
            @Mapping(source = "supplierAfterDtl.supplierAfterDtlId", target = "supplierAfterDtlId"),
            @Mapping(source = "supplierAfterDtl.sysCode", target = "sysCode")
    })
    TrdHdfkSettleSaveReqVO convertHdfkSettleVO(TrdSupplierAfterDtl supplierAfterDtl, TrdOrder trdOrder, TrdSupplierOrderDtl supplierOrderDtl);

    @Mappings({
            @Mapping(source = "supplierDTO.supplierName", target = "supplierName"),
            @Mapping(source = "branchDTO.branchName", target = "branchName"),
            @Mapping(source = "branchDTO.contactName", target = "branchContactName"),
            @Mapping(source = "branchDTO.branchAddr", target = "branchAddr"),
            @Mapping(source = "specName", target = "specName"),
    })
    @BeanMapping(ignoreByDefault = true)
    void buidSetSkuItemRespVO(@MappingTarget TrdHdfkSettleSkuItemRespVO item,
                              SupplierDTO supplierDTO,
                              BranchDTO branchDTO,
                              String specName
    );
}