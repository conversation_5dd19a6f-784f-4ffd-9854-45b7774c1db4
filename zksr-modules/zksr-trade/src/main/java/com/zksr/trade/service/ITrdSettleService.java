package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.api.orderSettle.dto.*;
import com.zksr.trade.api.orderSettle.vo.OrderSettlePageVO;
import com.zksr.trade.api.orderSettle.vo.OrderSupplierSettlePageVo;
import com.zksr.trade.controller.settle.vo.TrdSettlePageReqVO;
import com.zksr.trade.controller.settle.vo.TrdSettleSaveReqVO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.controller.statementOfAccount.vo.OrderCommissionStatementReqVO;
import com.zksr.trade.controller.statementOfAccount.vo.OrderCommissionStatementRespVO;
import com.zksr.trade.domain.TrdSettle;
import com.zksr.trade.domain.TrdSupplierAfterDtl;

import java.util.List;

/**
 * 订单结算Service接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface ITrdSettleService {

    /**
     * 新增订单结算
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdSettle(@Valid TrdSettleSaveReqVO createReqVO);

    /**
     * 修改订单结算
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdSettle(@Valid TrdSettleSaveReqVO updateReqVO);

    /**
     * 删除订单结算
     *
     * @param settleId 订单结算id
     */
    public void deleteTrdSettle(Long settleId);

    /**
     * 批量删除订单结算
     *
     * @param settleIds 需要删除的订单结算主键集合
     * @return 结果
     */
    public void deleteTrdSettleBySettleIds(Long[] settleIds);

    /**
     * 获得订单结算
     *
     * @param settleId 订单结算id
     * @return 订单结算
     */
    public TrdSettle getTrdSettle(Long settleId);

    /**
     * 获得订单结算分页
     *
     * @param pageReqVO 分页查询
     * @return 订单结算分页
     */
    PageResult<TrdSettle> getTrdSettlePage(TrdSettlePageReqVO pageReqVO);


    /**
     * 保存订单付款后结算信息
     */
    public List<TrdSettle> createBatchSettle(TrdOrder order);


    /**
     * 保存售后订单退款结算信息
     */
    public List<TrdSettle> createBatchAfterSettle(List<TrdSupplierAfterDtl> afterDtls);

    /**
     * 更加结算id修改结算信息状态为已结算
     * @param settleIds
     */
    public void updateOrderSettleState(List<Long> settleIds);


    /**
     * 查询账户订单结算流水数据 （平台商、运营商、业务员）
     * @param pageVO
     * @return
     */
    public PageResult<OrderSettleResDTO> getOrderSettleInfoPage(OrderSettlePageVO pageVO);


    /**
     * 查询账户订单结算数据 (入驻商)
     * @param pageVO
     * @return
     */
    public List<OrderSupplierSettleResDTO> getSupperOrderSettleByDate(OrderSupplierSettlePageVo pageVO);

    /**
     * 查询账户订单结算数据明细 (入驻商)
     * @param pageVO
     * @return
     */
    public List<OrderSupplierSettleResDTO> getSupperOrderSettleDtlByDateTime(OrderSupplierSettlePageVo pageVO);

    /**
     * 查询账户订单结算数据明细 (业务员APP)
     * @param pageVO
     * @return
     */
    PageResult<OrderSettleColonelResDTO> getColonelOrderSettleInfoPage(OrderSettlePageVO pageVO);

    /**
     * 取消入驻商冻结流水   TODO 应入驻商充值方案弃用，故此代码弃用
     * @param settles
     * @param afterDtl
     */
    public void cancelSupplierFreezeFlow(List<TrdSettle> settles, TrdSupplierAfterDtl afterDtl);

    /**
     * 获取业务员, 日, 月, 上月, 结算数据统计
     * @param colonelId 业务员ID
     * @return  统计数据
     */
    ColonelFixSettleTotalRespVO getColonelSettleTotal(Long colonelId);

    /**
     * 获取指定时间范围内的查询数据
     * @param reqVO
     * @return
     */
    ColonelFloatSettleTotalRespVO getColonelSettleTotalRange(ColonelFloatSettleTotalReqVO reqVO);


    /**
     * 根据主订单ID获得订单结算信息
     *
     * @param orderId 订单编号
     * @return 订单结算
     */
    public List<TrdSettle> getTrdSettleListByOrderId(Long orderId);

    /**
     * 更新订单结算流水状态 订单销售数量 == 订单发货前取消数量
     * @param afterDtls
     */
    public void updateSettle(List<TrdSupplierAfterDtl> afterDtls);

    /**
     * 根据供应商订单明细ID获得结算流水ID (包括订单明细所产生对应售后单)
     * @param supplierOrderDtlIdList
     * @return
     */
    public List<TrdSettle> getSettleBySupplierOrderDtlId(List<Long> supplierOrderDtlIdList);


    /**
     * 订单分佣对账单
     * @param pageVO
     * @return
     */
    public PageResult<OrderCommissionStatementRespVO> getStatementOfAccountPage(OrderCommissionStatementReqVO pageVO);

    /**
     * 根据主订单ID、分佣账户类型、分佣账户 获得订单结算信息
     *
     * @param orderId 订单编号
     * @return 订单结算
     */
    public List<TrdSettle> getTrdSettleListByOrderIdAndMerchant(Long orderId, String merchantType, Long merchantId);

}
