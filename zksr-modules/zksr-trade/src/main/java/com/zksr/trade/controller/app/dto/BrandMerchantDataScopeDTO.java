package com.zksr.trade.controller.app.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/8/7 14:22
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BrandMerchantDataScopeDTO {

    @ApiModelProperty("品牌集合")
    private List<Long> brandIdList;

    @ApiModelProperty("区域集合")
    private List<Long> areaIdList;
}
