package com.zksr.trade.controller.order.export;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

@Data
@Accessors(chain = true)
@ApiModel("入驻商订单商品明细导出实体")
public class SupplierOrderDtlInfoExport {

    @Excel(name = "入驻商订单编号", needMerge = true)
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    @Excel(name = "商品名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    @Excel(name = "规格名称")
    @ApiModelProperty(value = "商品SPU规格名称")
    private String specName;

    @Excel(name = "商品条码")
    @ApiModelProperty(value = "商品条码")
    private String barcode;

    @Excel(name = "单位")
    @ApiModelProperty(value = "下单单位")
    private String saleUnit;

    @Excel(name = "是否赠品", readConverterExp = "0=否,1=是")
    @ApiModelProperty(value = "是否赠品（1：是 ，0：否）")
    private Integer giftFlag;


    @Excel(name = "数量")
    @ApiModelProperty(value = "商品数量（下单单位数量）")
    private Long totalNum;

    @Excel(name = "单价")
    @ApiModelProperty(value = "商品销售价（优惠后平均单价）")
    private BigDecimal price;

    @Excel(name = "总价")
    @ApiModelProperty(value = "商品销售总价（优惠后价格）")
    private BigDecimal totalAmt;

    @Excel(name = "订单日期",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订单日期")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    @Excel(name = "订单年")
    @ApiModelProperty(value = "订单年")
    private String createTimeYear;

    @Excel(name = "订单月")
    @ApiModelProperty(value = "订单月")
    private String createTimeMonth;

    @Excel(name = "订单日")
    @ApiModelProperty(value = "订单日")
    private String createTimeDay;

    @Excel(name = "订单所属的省份")
    @ApiModelProperty(value = "订单所属的省份")
    private String province;

    @Excel(name = "订单所属的城市")
    @ApiModelProperty(value = "订单所属的城市")
    private String city;

    @Excel(name = "订单所属的区县")
    @ApiModelProperty(value = "订单所属的区县")
    private String county;

    @Excel(name = "订单所属的入驻商")
    @ApiModelProperty(value = "订单所属的入驻商")
    private String supplierName;

    @Excel(name = "订单所属的运营商")
    @ApiModelProperty(value = "订单所属的运营商")
    private String dcName;

    @Excel(name = "订单所属的下单门店")
    @ApiModelProperty(value = "订单所属的下单门店")
    private String branchName;

    @Excel(name = "订单所属的下单门店渠道")
    @ApiModelProperty(value = "订单所属的下单门店渠道")
    private String channelName;

    //订单所属的销售经理*****************************************************************************

    @ApiModelProperty("订单所属的业务员")
    @Excel(name = "订单所属的业务员")
    private String colonelName;

    @ApiModelProperty("订单所属的下单门店地址")
    @Excel(name = "订单所属的下单门店地址")
    private String branchAddr;

    @ApiModelProperty("订单所属的下单门店经纬度")
    @Excel(name = "订单所属的下单门店经纬度")
    private String longitudeLatitude;

    @ApiModelProperty("订单的支付方式")
    @Excel(name = "订单的支付方式")
    private String payWay;

    @ApiModelProperty("订单商品的辅助商品编号")
    @Excel(name = "订单商品的辅助商品编号")
    private String spuNo;

    @ApiModelProperty(value = "订单商品的品牌")
    @Excel(name = "订单商品的品牌")
    private String brandName;

    @ApiModelProperty(value = "订单商品所属的一级类目")
    @Excel(name = "订单商品所属的一级类目")
    private String oneCategory;

    @ApiModelProperty(value = "订单商品所属的二级类目")
    @Excel(name = "订单商品所属的二级类目")
    private String twoCategory;

    @ApiModelProperty(value = "订单商品所属的三级类目")
    @Excel(name = "订单商品所属的三级类目")
    private String threeCategory;

    @ApiModelProperty(value = "订单商品促销类型")
    @Excel(name = "订单商品促销类型")
    private String discountType;

    @ApiModelProperty(value = "订单商品使用的优惠劵")
    @Excel(name = "订单商品使用的优惠劵")
    private String discountRoll;


    @ApiModelProperty(value = "订单商品状态")
    @Excel(name = "订单商品状态")
    private String delivery;

    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;



    //==================== 不导出显示数据=============================
    @ApiModelProperty(value = "skuid")
    private Long skuId;
    @ApiModelProperty(value = "spuid")
    private Long spuId;
    @ApiModelProperty(value = "下单单位大小")
    private Integer orderUnitType;
    @ApiModelProperty(value = "下单单位")
    private Integer orderUnit;

    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "运营商id")
    private Long dcId;

    @ApiModelProperty(value = "城市ID")
    private Long areaId;

    @ApiModelProperty("业务员id")
    private Long colonelId;

    private Long orderId;



}
