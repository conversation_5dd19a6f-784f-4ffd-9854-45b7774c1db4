package com.zksr.trade.service.impl.handler.payWay;

import com.alibaba.fastjson2.JSON;
import com.zksr.account.api.pay.PayApi;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.account.model.pay.vo.PayWxB2bCreateDivideReqVO;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.OrderPayWayEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.dto.OrderPayInfoRespDTO;
import com.zksr.trade.api.order.dto.TrdSupplierResDto;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdSupplierPageVO;
import com.zksr.trade.domain.TrdSettle;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.mapper.TrdSettleMapper;
import com.zksr.trade.mapper.TrdSupplierOrderDtlMapper;
import com.zksr.trade.service.handler.payWay.ITrdOrderPayWayHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 合利宝支付方式处理器 - 在线支付
 */
@Service
@Slf4j
@Order(ITrdOrderPayWayHandlerService.ORDER_HLB_ONLINE)
public class TrdOrderHlbPayWayHandlerServiceimpl implements ITrdOrderPayWayHandlerService {

    @Autowired
    private TrdSettleMapper trdSettleMapper;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;

    @Autowired
    private PayApi payApi;

    @Override
    public Boolean isPlatform(String platform, String payWay) {
        return Objects.equals(PayChannelEnum.HLB.getCode(), platform) && Objects.equals(OrderPayWayEnum.ONLINE.getPayWay(), payWay);
    }

    @Override
    public Boolean isPlatform(String platform) {
        return Objects.equals(PayChannelEnum.HLB.getCode(), platform);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderCompleteCreateSettleTransfer(TrdOrder tor, List<TrdSupplierOrder> tsoList, List<TrdSupplierOrderDtl> tsodList, List<TrdSettle> settleList) {
        boolean isAllComplete = tsodList.stream()
                .filter(dtl -> !Objects.equals(dtl.getDeliveryState(), DeliveryStatusEnum.CANCEL.getCode()))
                .allMatch(dtl -> Objects.equals(dtl.getDeliveryState(), DeliveryStatusEnum.COMPLETE.getCode()));
        if (isAllComplete){ // 全部商品已全部完成
            // 入驻商分账请求  先行处理，不然后续分账无法处理
            tsoList.forEach(tso -> {
                PayWxB2bCreateDivideReqVO reqVO = new PayWxB2bCreateDivideReqVO(tor.getOrderNo(), tso.getSupplierId(), MerchantTypeEnum.SUPPLIER.getType());
                // 发送分润账户分账请求
                CreateDivideRespVO respVO = payApi.divide(reqVO).getCheckedData();
                log.info("发送订单【{}】，发送合利宝在线分润账户分账请求：{}, 返回结果:{}", tor.getOrderNo(), JSON.toJSONString(reqVO), JSON.toJSONString(respVO));
            });

            // 软件商、平台商、运营商、业务员 分账请求
            settleList.stream().collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
                    .forEach((merchantId, settles) -> {

                        PayWxB2bCreateDivideReqVO reqVO =
                                new PayWxB2bCreateDivideReqVO(tor.getOrderNo(), settles.get(NumberPool.INT_ZERO).getMerchantId(), settles.get(NumberPool.INT_ZERO).getMerchantType());
                        // 发送分润账户分账请求
                        log.info("发送订单【{}】，发送合利宝在线分润账户分账请求:{}", tor.getOrderNo(), JSON.toJSONString(reqVO));
                        CreateDivideRespVO respVO = payApi.divide(reqVO).getCheckedData();
                        if (respVO.isSuccess()) {
                            settles.forEach(settle -> {
                                settle.setState(StatusConstants.SETTLE_STATE_2); // 更新结算状态未结算中
                            });
                        }
                        log.info("发送订单【{}】，发送合利宝在线分润账户分账请求返回结果:{}", tor.getOrderNo(), JSON.toJSONString(respVO));
                        trdSettleMapper.updateBatch(settles);
                    });
        }
    }

    @Override
    public void orderSettleAccountInfo(List<TrdSupplierResDto> payInfoList, Map<Long, List<TrdSupplierOrderDtl>> supplierItemMap, TrdSupplierPageVO pageVo, List<TrdSettle> settles, OrderPayInfoRespDTO respDTO) {

        payInfoList.stream().collect(Collectors.toMap(TrdSupplierResDto::getSupplierOrderId, item -> item)).forEach((supplierOrderId, supplierResDTO) -> {
            // 入驻商分账方信息
            supplierResDTO.setItemInfo("")
                    .setMerchantId(supplierResDTO.getSupplierId())
                    .setMerchantType(MerchantTypeEnum.SUPPLIER.getType());

            if (supplierItemMap.containsKey(supplierOrderId)) {
                // 入驻商分账方信息
                List<String> itemList = supplierItemMap.get(supplierOrderId).stream()
                        .map(item ->
                                StringUtils.format("{}", item.getSpuName().trim())
                        ).collect(Collectors.toList());

                supplierResDTO.setItemInfo(StringUtils.join(itemList, StringPool.COMMA));
            }

            // 软件商、平台商、运营商、业务员 分账方信息
            settles.stream()
                    .filter(settle -> Objects.equals(supplierResDTO.getSupplierId(), settle.getSupplierId()))
                    .collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
                    .forEach((merchantId, settleList) -> {
                        TrdSupplierResDto supplierResDto = TrdSupplierResDto.builder()
                                .subOrderNo(pageVo.getOrderNo())
                                .subPayFee(BigDecimal.ZERO)
                                .platform(pageVo.getStoreOrderPayPlatform())
                                .merchantId(settleList.get(NumberPool.INT_ZERO).getMerchantId())
                                .merchantType(settleList.get(NumberPool.INT_ZERO).getMerchantType())
                                .build();
                        BigDecimal settleAmt = settleList.stream().map(TrdSettle::getSettleAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        supplierResDto.setSubOrderAmt(settleAmt);
                        if (settleAmt.compareTo(BigDecimal.ZERO) > NumberPool.INT_ZERO) {
                            respDTO.getSupplierList().add(supplierResDto);
                            // 入驻商分账金额 - 分账方金额
                            supplierResDTO.setSubOrderAmt(supplierResDTO.getSubOrderAmt().subtract(settleAmt));
                        }
                    });
            respDTO.getSupplierList().add(supplierResDTO);
        });
    }
}
