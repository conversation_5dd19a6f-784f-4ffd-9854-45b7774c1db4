package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.api.car.dto.AppCarEventDTO;
import com.zksr.trade.api.car.dto.AppCarInitDTO;
import com.zksr.trade.api.car.vo.TrdCarApiRespVO;
import com.zksr.trade.domain.TrdCar;
import com.zksr.trade.controller.car.vo.*;

import java.util.List;

/**
 * 购物车Service接口
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
public interface ITrdCarService {

    /**
     * 新增购物车
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdCar(@Valid TrdCarSaveReqVO createReqVO);

    /**
     * 修改购物车
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdCar(@Valid TrdCarSaveReqVO updateReqVO);

    /**
     * 删除购物车
     *
     * @param carId 购物车id
     */
    public void deleteTrdCar(Long carId);

    /**
     * 批量删除购物车
     *
     * @param carIds 需要删除的购物车主键集合
     * @return 结果
     */
    public void deleteTrdCarByCarIds(Long[] carIds);

    /**
     * 获得购物车
     *
     * @param carId 购物车id
     * @return 购物车
     */
    public TrdCar getTrdCar(Long carId);

    /**
     * 获得购物车分页
     *
     * @param pageReqVO 分页查询
     * @return 购物车分页
     */
    PageResult<TrdCar> getTrdCarPage(TrdCarPageReqVO pageReqVO);

    /**
     * 处理购物车数据变动
     * @param carEvent 购物车事件
     */
    void processCarEvent(AppCarEventDTO carEvent);

    /**
     * 获取初始化数据
     * @param branchId
     * @param minId         最小键ID
     * @return 每次500条
     */
    List<AppCarInitDTO> getInitData(Long branchId, Long minId);

    /**
     * 根据门店获得购物车
     *
     * @param branchId
     * @return 购物车
     */
    List<TrdCarApiRespVO> getTrdCarList(Long branchId);

    void saveRedisCar(Long branchId);
}
