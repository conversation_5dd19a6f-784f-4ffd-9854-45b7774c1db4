package com.zksr.trade.controller.after.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年02月20日 09:13
 * @description: AfterDeliveryDTO
 */
@ApiModel("入驻商售后订单操作信息返回对象")
@Data
@Accessors(chain = true)
@Builder
public class AfterDeliveryDTO {

    /** 操作时间 */
    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;

    /** 当前状态 */
    @ApiModelProperty("当前状态")
    private Integer state;

    /** 状态说明 */
    @ApiModelProperty("状态名称")
    private String statusName;

    /** 外部单号 */
    @ApiModelProperty("外部单号")
    private String sourceOrderNo;

    /** 具体描述 */
    @ApiModelProperty("具体内容")
    private String describe;

    /** 排序号 */
    @ApiModelProperty("排序号")
    private Integer sort;

}
