package com.zksr.trade.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.domain.vo.openapi.TrdSupAfterDtlRequest;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.report.api.homePages.dto.HomePagesOrderAfterDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.trade.api.after.dto.AfterOrderPageRespDTO;
import com.zksr.trade.api.after.vo.AfterOrderPageReqVO;
import com.zksr.trade.api.order.dto.ColonelAppOrderListTotalDTO;
import com.zksr.trade.api.order.vo.*;
import com.zksr.trade.controller.after.dto.AfterDtlListDTO;
import com.zksr.trade.controller.after.dto.AfterPageRespDTO;
import com.zksr.trade.controller.after.vo.AfterPageReqVO;
import com.zksr.trade.controller.app.vo.BrandHomePageRespVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListInfoRespVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListReqVO;
import com.zksr.trade.controller.app.vo.BrandHomeSaleListRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.trade.api.after.vo.TrdAfter;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 售后单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Mapper
public interface TrdAfterMapper extends BaseMapperX<TrdAfter> {
//    default PageResult<TrdAfter> selectPage(TrdAfterPageReqVO reqVO) {
//        return selectPage(reqVO, new LambdaQueryWrapperX<TrdAfter>()
//                    .eqIfPresent(TrdAfter::getAfterId, reqVO.getAfterId())
//                    .eqIfPresent(TrdAfter::getSysCode, reqVO.getSysCode())
//                    .eqIfPresent(TrdAfter::getAfterNo, reqVO.getAfterNo())
//                    .eqIfPresent(TrdAfter::getDcId, reqVO.getDcId())
//                    .eqIfPresent(TrdAfter::getAreaId, reqVO.getAreaId())
//                    .eqIfPresent(TrdAfter::getMemberId, reqVO.getMemberId())
//                    .eqIfPresent(TrdAfter::getColonelId, reqVO.getColonelId())
//                    .eqIfPresent(TrdAfter::getColonelLevel, reqVO.getColonelLevel())
//                    .eqIfPresent(TrdAfter::getPcolonelId, reqVO.getPcolonelId())
//                    .eqIfPresent(TrdAfter::getPcolonelLevel, reqVO.getPcolonelLevel())
//                    .eqIfPresent(TrdAfter::getBranchId, reqVO.getBranchId())
//                    .eqIfPresent(TrdAfter::getLongitude, reqVO.getLongitude())
//                    .eqIfPresent(TrdAfter::getLatitude, reqVO.getLatitude())
//                    .eqIfPresent(TrdAfter::getBranchAddr, reqVO.getBranchAddr())
//                    .eqIfPresent(TrdAfter::getReason, reqVO.getReason())
//                    .eqIfPresent(TrdAfter::getRefundAmt, reqVO.getRefundAmt())
//                    .eqIfPresent(TrdAfter::getDescr, reqVO.getDescr())
//                    .eqIfPresent(TrdAfter::getOrderRefundAmt, reqVO.getOrderRefundAmt())
//                    .eqIfPresent(TrdAfter::getPayAmt, reqVO.getPayAmt())
//                    .eqIfPresent(TrdAfter::getPayWay, reqVO.getPayway())
//                    .eqIfPresent(TrdAfter::getPlatform, reqVO.getPlatform())
//                    .eqIfPresent(TrdAfter::getPayTime, reqVO.getPayTime())
//                    .eqIfPresent(TrdAfter::getReturnTime, reqVO.getReturnTime())
//                    .eqIfPresent(TrdAfter::getRefundTime, reqVO.getRefundTime())
//                    .eqIfPresent(TrdAfter::getIsCancel, reqVO.getIsCancel())
//                    .eqIfPresent(TrdAfter::getSource, reqVO.getSource())
//                    .eqIfPresent(TrdAfter::getMemo, reqVO.getMemo())
//                    .eqIfPresent(TrdAfter::getApplyImgs, reqVO.getApplyImgs())
//                    .eqIfPresent(TrdAfter::getExpressImgs, reqVO.getExpressImgs())
//                    .eqIfPresent(TrdAfter::getPayRate, reqVO.getPayRate())
//                    .eqIfPresent(TrdAfter::getPayFee, reqVO.getPayFee())
//                .orderByDesc(TrdAfter::getAfterId));
//    }

    /**
     * @Description: 分页获取售后订单数据(入驻商 PC)
     * @Author: chenmingqing
     * @Date: 2024/4/22 12:51
     */
    Page<AfterPageRespDTO> selectPage(@Param("pageReqVO") AfterPageReqVO pageReqVO, @Param("page") Page<AfterPageReqVO> page);

    /**
     * @Description: 获取售后订单详情数据(入驻商)
     * @Author: chenmingqing
     * @Date: 2024/4/22 12:51
     */
    List<AfterDtlListDTO> selectAfterDtlList(AfterDtlReqVO reqVO);


    /**
     * @Description: 分页获取售后订单数据(门店  小程序)
     * @Author: chenmingqing
     * @Date: 2024/4/22 12:51
     */
    Page<AfterOrderPageRespDTO> pageAfter(@Param("pageReqVO") AfterOrderPageReqVO reqVO, @Param("page") Page<AfterOrderPageReqVO> page);

    /**
     * 业务员app售后列表 汇总数据
     * @param memColonelAppOrderListPageReqVO
     * @return
     */
    ColonelAppOrderListTotalDTO selectMemColoneAppAfterOrderListTotal(@Param("reqVO") TrdColonelAppOrderListPageReqVO memColonelAppOrderListPageReqVO);

    /**
     * @Description: 业务员app售后列表
     * @Author: liuxingyu
     * @Date: 2024/4/26 15:00
     */
    Page<TrdColonelAppOrderListRespVO> selectMemColoneAppAfterOrder(@Param("reqVO") TrdColonelAppOrderListPageReqVO memColonelAppOrderListPageReqVO, @Param("page") Page<TrdColonelAppOrderListPageReqVO> page, @Param("colonelIds") List<Long> colonelIds);

    /**
     * @Description: 业务员app售后列表 (新)
     * @Author: chenmingqing
     * @Date: 2025/1/11 9:00
     */
    List<AfterOrderPageRespDTO> selectMemColoneAppAfterOrderNew(TrdColonelAppOrderListPageReqVO reqVO);


    List<AfterDtl> getAfterDtlByDtlId(@Param("idS") List<Long> ids,@Param("request") TrdSupAfterDtlRequest request);

    AfterApproveDtlEditVO getAfterDtlByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据售后订单Id查询信息
     * @param afterId 售后订单Id
     * @return
     */
    default TrdAfter selectByAfterId(Long afterId) {
        return selectOne(new LambdaQueryWrapperX<TrdAfter>()
                .eq(TrdAfter::getAfterId, afterId));
    }

    default TrdAfter getTrdAfterByAfterNo(String afterNo){
        return selectOne(new LambdaQueryWrapper<TrdAfter>()
                .eq(TrdAfter::getAfterNo,afterNo));
    }

    /**
     * 获取品牌商首页统计数据
     * @param brandList 品牌列表
     * @param areaList  城市列表
     * @return
     */
    BrandHomePageRespVO selectByBrandHomeAfterData(@Param("brandList") List<Long> brandList, @Param("areaList") List<Long> areaList);

    /**
     * 查询品牌商首页列表数据
     * @param reqVO
     * @return
     */
    List<BrandHomeSaleListRespVO> selectByBrandHomeSaleList(@Param("reqVO") BrandHomeSaleListReqVO reqVO);

    /**
     * 获取品牌商品牌商品销售数据
     * @param reqVO
     * @return
     */
    List<BrandHomeSaleListInfoRespVO> selectByBrandHomeSaleInfoList(@Param("reqVO") BrandHomeSaleListReqVO reqVO);

    /**
     * 根据订单Id和入驻商id查询未完成的售后订单
     * @param orderId
     * @return
     */
    Integer selectUnfinishedAfterByOrderId(@Param("orderId") Long orderId);


    List<Map<String, Object>> getPendingRefundAmount(@Param("currentMonthId") String currentMonthId,@Param("branchIds") List<String> branchIds);

    List<Map<String, Object>> getColonelPendingRefundAmount(@Param("currentMonthId") String currentMonthId,@Param("colonelIds") List<String> colonelIds);

    /**
     *  获取订单退货销售数据
     * @param reqVO
     * @return
     */
    List<HomePagesOrderAfterDataRespDTO> getHomePagesOrderAfterData(HomePagesReqVO reqVO);

    /**
     * 根据业务员ID获取业务员APP首页的售后金额（当天）
     *
     * @param colonelId
     * @return
     */
    BigDecimal getColonelAppPageAfterAmt(Long colonelId);
}

