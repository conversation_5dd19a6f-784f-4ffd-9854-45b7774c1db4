package com.zksr.trade.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdPackageDtl;
import com.zksr.trade.controller.trdPackage.vo.TrdPackageDtlPageReqVO;

import java.util.List;


/**
 * 包裹明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Mapper
public interface TrdPackageDtlMapper extends BaseMapperX<TrdPackageDtl> {
    default PageResult<TrdPackageDtl> selectPage(TrdPackageDtlPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdPackageDtl>()
                    .eqIfPresent(TrdPackageDtl::getPackageDtlId, reqVO.getPackageDtlId())
                    .eqIfPresent(TrdPackageDtl::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdPackageDtl::getVersion, reqVO.getVersion())
                    .eqIfPresent(TrdPackageDtl::getPackageId, reqVO.getPackageId())
                    .eqIfPresent(TrdPackageDtl::getExpressNo, reqVO.getExpressNo())
                    .eqIfPresent(TrdPackageDtl::getProductCode, reqVO.getProductCode())
                    .eqIfPresent(TrdPackageDtl::getProductName, reqVO.getProductName())
                    .eqIfPresent(TrdPackageDtl::getQuantity, reqVO.getQuantity())
                    .eqIfPresent(TrdPackageDtl::getOrderLineNo, reqVO.getOrderLineNo())
                    .eqIfPresent(TrdPackageDtl::getBomFlag, reqVO.getBomFlag())
                    .eqIfPresent(TrdPackageDtl::getSubItemName, reqVO.getSubItemName())
                    .eqIfPresent(TrdPackageDtl::getSubItemNo, reqVO.getSubItemNo())
                    .eqIfPresent(TrdPackageDtl::getSubQtyBom, reqVO.getSubQtyBom())
                .orderByDesc(TrdPackageDtl::getPackageDtlId));
    }

    /**
     * 根据包裹ID查询包裹明细列表
     * @param packageId 包裹ID
     * @return 包裹明细列表
     */
    default List<TrdPackageDtl> selectListByPackageId(Long packageId) {
        return selectList(new LambdaQueryWrapperX<TrdPackageDtl>()
                .eq(TrdPackageDtl::getPackageId, packageId));
    }
}
