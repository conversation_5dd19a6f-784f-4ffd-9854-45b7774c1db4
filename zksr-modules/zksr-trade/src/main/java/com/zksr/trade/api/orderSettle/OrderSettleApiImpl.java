package com.zksr.trade.api.orderSettle;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.orderSettle.dto.*;
import com.zksr.trade.api.orderSettle.vo.OrderCommissionStatementPageVo;
import com.zksr.trade.api.orderSettle.vo.OrderSettlePageVO;
import com.zksr.trade.controller.statementOfAccount.vo.OrderCommissionStatementReqVO;
import com.zksr.trade.service.ITrdSettleService;
import com.zksr.trade.service.ITrdSupplierOrderSettleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2024年05月07日 11:01
 * @description: OrderSettleApiImpl
 */
@RestController
@ApiIgnore
public class OrderSettleApiImpl  implements OrderSettleApi{
    @Autowired
    private ITrdSettleService trdSettleService;

    @Autowired
    private ITrdSupplierOrderSettleService trdSupplierOrderSettleService;

    @Override
    public PageResult<OrderSettleColonelResDTO> getColonelOrderSettleInfoPage(OrderSettlePageVO pageVO) {
        return trdSettleService.getColonelOrderSettleInfoPage(pageVO);
    }

    @Override
    public CommonResult<ColonelFixSettleTotalRespVO> getColonelSettleTotal(Long colonelId) {
        return success(trdSettleService.getColonelSettleTotal(colonelId));
    }

    @Override
    public CommonResult<ColonelFloatSettleTotalRespVO> getColonelSettleTotalRange(ColonelFloatSettleTotalReqVO reqVO) {
        return success(trdSettleService.getColonelSettleTotalRange(reqVO));
    }

    @Override
    public CommonResult<List<OrderSettleResDTO>> getAccountOrderSettleFlowPageList(OrderSettlePageVO pageVO) {
        return success(trdSettleService.getOrderSettleInfoPage(pageVO).getList());
    }

    @Override
    public CommonResult<List<OrderCommissionStatementResDTO>> getStatementOfAccountPage(OrderCommissionStatementPageVo pageVO) {
        OrderCommissionStatementReqVO pageVO1 = HutoolBeanUtils.toBean( pageVO, OrderCommissionStatementReqVO.class);
        return success(HutoolBeanUtils.toBean(trdSettleService.getStatementOfAccountPage(pageVO1).getList(), OrderCommissionStatementResDTO.class));
    }

    @Override
    public CommonResult<BigDecimal> getColonelAppPercentageAmt(Long colonelId) {
        return success(trdSupplierOrderSettleService.getColonelAppPercentageAmt(colonelId));
    }
}
