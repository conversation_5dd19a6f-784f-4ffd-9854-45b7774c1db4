package com.zksr.trade.controller.order.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.trade.api.express.TrdOrderExpress;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DcOrderPageRespVO {

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 订单状态（数据字典）
     */
    @ApiModelProperty("订单状态")
    private Long deliveryState;

    /**
     * 门店id
     */
    @ApiModelProperty("门店id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long branchId;

    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    private String branchName;

    /**
     * 门店地址
     */
    @ApiModelProperty("门店地址")
    private String branchAddr;

    /**
     * 业务员id
     */
    @ApiModelProperty("业务员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long colonelId;


    /**
     * 业务员名称
     */
    @ApiModelProperty("业务员名称")
    private String colonelName;

    /**
     * 入驻商id
     */
    @ApiModelProperty("入驻商id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierId;

    /**
     * 入驻商名称
     */
    @ApiModelProperty("入驻商名称")
    private String supplierName;

    @ApiModelProperty("入驻商订单Id（子订单Id）")
    private Long supplierOrderId;

    @ApiModelProperty("入驻商订单号（子订单号）")
    private String supplierOrderNo;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String contactPhone;

    /**
     * 产品编号
     */
    @ApiModelProperty("产品编号")
    private String spuNo;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String spuName;

    /**
     * 条形码
     */
    @ApiModelProperty("条形码")
    private String barcode;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private BigDecimal price;

    /**
     * 要货数量(销售数量)
     */
    @ApiModelProperty("要货数量")
    private Long demandNum;

    /**
     * 发货数量(销售数量)
     */
    @ApiModelProperty("发货数量")
    private Long totalNum;

    /**
     * 差异数量
     */
    @ApiModelProperty("差异数量")
    private Long shortageNum;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal totalAmt;

    /**
     * 差异金额
     */
    @ApiModelProperty("差异金额")
    private BigDecimal discrepancyAmt;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private String payWay;

    /**
     * spuId
     */
    @ApiModelProperty("spuId")
    private Long spuId;

    /**
     * skuId
     */
    @ApiModelProperty("skuId")
    private Long skuId;

    /**
     * 商品类型 0：全国商品 1：本地商品
     */
    @ApiModelProperty("商品类型 0：全国商品 1：本地商品")
    private Integer itemType;

    /**
     * 快递信息集合
     */
    @ApiModelProperty("快递信息集合")
    private List<TrdOrderExpress> expressesList;
}
