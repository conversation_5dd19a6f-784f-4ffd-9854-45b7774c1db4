package com.zksr.trade.api.supplierAfter;

import com.zksr.common.core.domain.vo.openapi.AfterDetailPushDTO;
import com.zksr.common.core.domain.vo.openapi.AfterPushDTO;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.domain.vo.openapi.receive.*;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.trade.service.ITrdSupplierAfterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@ApiIgnore
@RestController // 提供 RESTful API 接口，给 Feign 调用
public class SupplierAfterApiImpl implements SupplierAfterApi {

    @Autowired
    private ITrdSupplierAfterService trdSupplierAfterService;

    @Override
    public CommonResult<Boolean> receiveSalesReturn(Long sysCode, Long opensourceId, AfterSalesReturnVO vo) {
        trdSupplierAfterService.receiveSalesReturn(vo);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> receiveOrderTakeDelivery(Long sysCode, Long opensourceId, ReceiveOrderTakeDeliveryVO vo) {
        return success(trdSupplierAfterService.receiveOrderTakeDelivery(vo));
    }

    @Override
    public CommonResult<List<AfterPushDTO>> getSupplierAfterListByAfterNo(String AfterNo) {
        return CommonResult.success(HutoolBeanUtils.toBean(trdSupplierAfterService.getSupAfterByOrderNo(AfterNo), AfterPushDTO.class));
    }

    @Override
    public CommonResult<List<AfterDetailPushDTO>> getSupplierAfterListBySupplierAfterNo(String supplierAfterNo) {
        return CommonResult.success(HutoolBeanUtils.toBean(trdSupplierAfterService.getSupplierAfterListBySupplierAfterNo(supplierAfterNo), AfterDetailPushDTO.class));
    }

    @Override
    public CommonResult<List<AfterDetailPushDTO>> getSupplierAfterDtlListByIds(List<Long> ids) {
        return CommonResult.success(HutoolBeanUtils.toBean(trdSupplierAfterService.getListByIds(ids), AfterDetailPushDTO.class));
    }

    @Override
    public CommonResult<AfterPushDTO> getSupAfterBySupAfterNo(String supplierAfterNo) {
        return CommonResult.success(HutoolBeanUtils.toBean(trdSupplierAfterService.getSupAfterBySupAfterNo(supplierAfterNo), AfterPushDTO.class));
    }

    @Override
    public CommonResult<Boolean> afterOrderReceiveCallback(Long sysCode, Long opensourceId, AfterOrderReceiveCallbackVO vo) {
        trdSupplierAfterService.afterOrderReceiveCallback(vo);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> syncAfterResponseSuccess(SyncDataDTO data) {
        trdSupplierAfterService.syncAfterResponseSuccess(data);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> afterCancel(Long sysCode, Long opensourceId, AfterCancelVO vo) {
        trdSupplierAfterService.afterCancel(vo);
        return CommonResult.success(true);
    }
}
