package com.zksr.trade.controller.trdPackage.vo;

import java.math.BigDecimal;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

/**
 * 包裹明细对象 trd_package_dtl
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@ApiModel("包裹明细 - trd_package_dtl分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdPackageDtlPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 包裹明细ID */
    @ApiModelProperty(value = "商品订单行号")
    private Long packageDtlId;

    /** 平台商ID */
    @Excel(name = "平台商ID")
    @ApiModelProperty(value = "平台商ID", required = true)
    private Long sysCode;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号", required = true)
    private Long version;

    /** 包裹ID */
    @Excel(name = "包裹ID")
    @ApiModelProperty(value = "包裹ID", required = true)
    private Long packageId;

    /** 运单号 */
    @Excel(name = "运单号")
    @ApiModelProperty(value = "运单号")
    private String expressNo;

    /** 商品编码 */
    @Excel(name = "商品编码")
    @ApiModelProperty(value = "商品编码")
    private String productCode;

    /** 商品名称 */
    @Excel(name = "商品名称")
    @ApiModelProperty(value = "商品名称")
    private String productName;

    /** 商品数量 */
    @Excel(name = "商品数量")
    @ApiModelProperty(value = "商品数量")
    private BigDecimal quantity;

    /** 商品订单行号 */
    @Excel(name = "商品订单行号")
    @ApiModelProperty(value = "商品订单行号")
    private String orderLineNo;

    /** 套件标识 0散件 1套件 */
    @Excel(name = "套件标识")
    @ApiModelProperty(value = "套件标识 0散件 1套件")
    private Integer bomFlag;

    /** 子件商品名称 */
    @Excel(name = "子件商品名称")
    @ApiModelProperty(value = "子件商品名称，bomFlag为0时，需要传")
    private String subItemName;

    /** 子件商品编码 */
    @Excel(name = "子件商品编码")
    @ApiModelProperty(value = "子件商品编码，bomFlag为0时，需要传")
    private String subItemNo;

    /** 单位用量 */
    @Excel(name = "单位用量")
    @ApiModelProperty(value = "单位用量，一个bom中子件的数量")
    private BigDecimal subQtyBom;
}
