package com.zksr.trade.controller.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 品牌商首页销售数据列表请求 - request vo
 * @date 2024/8/6 14:17
 */
@Data
@ApiModel(description = "品牌商首页销售数据列表请求")
public class BrandHomeSaleListReqVO {

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @NotNull(message = "查询开始时间不能为空")
    private Date startTime;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @NotNull(message = "查询结束时间不能为空")
    private Date endTime;

    @ApiModelProperty("品牌ID")
    private Long brandId;

    @ApiModelProperty(value = "品牌列表", hidden = true)
    private List<Long> brandList;

    @ApiModelProperty(value = "城市列表", hidden = true)
    private List<Long> areaList;
}
