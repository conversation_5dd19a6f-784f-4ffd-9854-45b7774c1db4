package com.zksr.trade.service;

import javax.validation.*;

import com.zksr.common.core.domain.erp.dto.AfterDetailOpenDto;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.controller.after.supplierDtlVo.TrdSupplierAfterDtlPageReqVO;
import com.zksr.trade.controller.after.supplierDtlVo.TrdSupplierAfterDtlSaveReqVO;

import java.util.List;

/**
 * 售后单明细Service接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
public interface ITrdSupplierAfterDtlService {

    /**
     * 新增售后单明细
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdSupplierAfterDtl(@Valid TrdSupplierAfterDtlSaveReqVO createReqVO);

    /**
     * 修改售后单明细
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdSupplierAfterDtl(@Valid TrdSupplierAfterDtlSaveReqVO updateReqVO);

    /**
     * 删除售后单明细
     *
     * @param supplierAfterDtlId 售后单明细id
     */
    public void deleteTrdSupplierAfterDtl(Long supplierAfterDtlId);

    /**
     * 批量删除售后单明细
     *
     * @param supplierAfterDtlIds 需要删除的售后单明细主键集合
     * @return 结果
     */
    public void deleteTrdSupplierAfterDtlBySupplierAfterDtlIds(Long[] supplierAfterDtlIds);

    /**
     * 获得售后单明细
     *
     * @param supplierAfterDtlId 售后单明细id
     * @return 售后单明细
     */
    public TrdSupplierAfterDtl getTrdSupplierAfterDtl(Long supplierAfterDtlId);

    /**
     * 获得售后单明细分页
     *
     * @param pageReqVO 分页查询
     * @return 售后单明细分页
     */
    PageResult<TrdSupplierAfterDtl> getTrdSupplierAfterDtlPage(TrdSupplierAfterDtlPageReqVO pageReqVO);

    List<AfterDetailOpenDto> getTrdSupplierAfterDtlBySplNo(Long aLong);

    /**
     * 批量获取入驻商售后详情
     * @param supplierAfterDtlIdList    入驻商售后详情
     * @return  入驻商售后
     */
    List<TrdSupplierAfterDtl> getTrdSupplierAfterDtlBatch(List<Long> supplierAfterDtlIdList);

    /**
     * 根据入驻商售后订单ID获取入驻商售后信息
     * @param supplierAfterId    supplierAfterId
     * @return  入驻商售后
     */
    List<TrdSupplierAfterDtl> getAfterDtlListBySupplierAfterId(Long supplierAfterId);
}
