package com.zksr.trade.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdExpressImportDtl;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlPageReqVO;

import java.util.List;


/**
 * 快递导入明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Mapper
public interface TrdExpressImportDtlMapper extends BaseMapperX<TrdExpressImportDtl> {
    default PageResult<TrdExpressImportDtl> selectPage(TrdExpressImportDtlPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdExpressImportDtl>()
                    .eqIfPresent(TrdExpressImportDtl::getExpressImportDtlId, reqVO.getExpressImportDtlId())
                    .eqIfPresent(TrdExpressImportDtl::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdExpressImportDtl::getStatus, reqVO.getStatus())
                    .eqIfPresent(TrdExpressImportDtl::getOrderId, reqVO.getOrderId())
                    .eqIfPresent(TrdExpressImportDtl::getOrderNo, reqVO.getOrderNo())
                    .eqIfPresent(TrdExpressImportDtl::getSupplierOrderDtlId, reqVO.getSupplierOrderDtlId())
                    .eqIfPresent(TrdExpressImportDtl::getSupplierOrderDtlNo, reqVO.getSupplierOrderDtlNo())
                    .eqIfPresent(TrdExpressImportDtl::getExpressNo, reqVO.getExpressNo())
                    .eqIfPresent(TrdExpressImportDtl::getExpressCom, reqVO.getExpressCom())
                    .eqIfPresent(TrdExpressImportDtl::getExpressComNo, reqVO.getExpressComNo())
                    .eqIfPresent(TrdExpressImportDtl::getFailReason, reqVO.getFailReason())
                    .eqIfPresent(TrdExpressImportDtl::getExpressImportId, reqVO.getExpressImportId())
                .orderByDesc(TrdExpressImportDtl::getExpressImportDtlId));
    }

    default List<TrdExpressImportDtl> getExpressImportInfo(Long expressImportId, Integer status){
        return selectList(new LambdaQueryWrapperX<TrdExpressImportDtl>()
        .eqIfPresent(TrdExpressImportDtl::getExpressImportId, expressImportId)
        .eqIfPresent(TrdExpressImportDtl::getStatus, status));
    }
}
