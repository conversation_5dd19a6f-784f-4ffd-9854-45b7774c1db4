package com.zksr.trade.controller.orderExpress.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;


/**
 * 订单快递对象 trd_order_express
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Data
@ApiModel("订单快递 - trd_order_express Response VO")
public class TrdOrderExpressRespVO {
    private static final long serialVersionUID = 1L;

    /** 订单快递id */
    @ApiModelProperty(value = "物流状态(数据字典);0-暂无轨迹信息 1-已揽收 2-在途中 3-签收 4-问题件")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderExpressId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sysCode;

    /** 订单id;订单id */
    @Excel(name = "订单id;订单id")
    @ApiModelProperty(value = "订单id;订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /** 订单编号;订单编号 */
    @Excel(name = "订单编号;订单编号")
    @ApiModelProperty(value = "订单编号;订单编号")
    private String orderNo;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    @ApiModelProperty(value = "入驻商订单明细id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    @ApiModelProperty(value = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 快递单号 */
    @Excel(name = "快递单号")
    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    /** 快递公司 */
    @Excel(name = "快递公司")
    @ApiModelProperty(value = "快递公司")
    private String expressCom;

    /** 快递公司编码 */
    @Excel(name = "快递公司编码")
    @ApiModelProperty(value = "快递公司编码")
    private String expressComNo;

    /** 收货人 */
    @Excel(name = "收货人")
    @ApiModelProperty(value = "收货人")
    private String receiveMan;

    /** 收货人电话 */
    @Excel(name = "收货人电话")
    @ApiModelProperty(value = "收货人电话")
    private String receivePhone;

    /** 收货地址 */
    @Excel(name = "收货地址")
    @ApiModelProperty(value = "收货地址")
    private String address;

    /** 最新物流轨迹 */
    @Excel(name = "最新物流轨迹")
    @ApiModelProperty(value = "最新物流轨迹")
    private String latestInfo;

    /** 快递导入明细id */
    @Excel(name = "快递导入明细id")
    @ApiModelProperty(value = "快递导入明细id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long expressImportDtlId;

    /** 真实发货时间(快递开始运输时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "真实发货时间(快递开始运输时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "真实发货时间(快递开始运输时间")
    private Date realInTransitTime;

    /** 物流状态(数据字典);0-暂无轨迹信息 1-已揽收 2-在途中 3-签收 4-问题件 */
    @Excel(name = "物流状态(数据字典);0-暂无轨迹信息 1-已揽收 2-在途中 3-签收 4-问题件")
    @ApiModelProperty(value = "物流状态(数据字典);0-暂无轨迹信息 1-已揽收 2-在途中 3-签收 4-问题件")
    private Long state;

}
