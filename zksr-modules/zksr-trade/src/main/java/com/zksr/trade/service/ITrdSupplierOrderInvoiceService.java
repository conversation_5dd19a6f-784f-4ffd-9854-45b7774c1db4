package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.api.supplierOrder.TrdSupplierOrderInvoiceSaveReqDTO;
import com.zksr.trade.controller.invoice.vo.TrdSupplierOrderInvoiceRespVO;
import com.zksr.trade.domain.TrdSupplierOrderInvoice;
import com.zksr.trade.controller.invoice.vo.TrdSupplierOrderInvoicePageReqVO;
import com.zksr.trade.controller.invoice.vo.TrdSupplierOrderInvoiceSaveReqVO;

/**
 * 用户发票Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface ITrdSupplierOrderInvoiceService {

    /**
     * 新增用户发票
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdSupplierOrderInvoice(@Valid TrdSupplierOrderInvoiceSaveReqVO createReqVO);

    /**
     * 修改用户发票
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdSupplierOrderInvoice(@Valid TrdSupplierOrderInvoiceSaveReqVO updateReqVO);

    /**
     * 删除用户发票
     *
     * @param id ID主键
     */
    public void deleteTrdSupplierOrderInvoice(Long id);

    /**
     * 批量删除用户发票
     *
     * @param ids 需要删除的用户发票主键集合
     * @return 结果
     */
    public void deleteTrdSupplierOrderInvoiceByIds(Long[] ids);

    /**
     * 获得用户发票
     *
     * @param id ID主键
     * @return 用户发票
     */
    public TrdSupplierOrderInvoice getTrdSupplierOrderInvoice(Long id);

    /**
     * 获得用户发票分页
     *
     * @param pageReqVO 分页查询
     * @return 用户发票分页
     */
    PageResult<TrdSupplierOrderInvoice> getTrdSupplierOrderInvoicePage(TrdSupplierOrderInvoicePageReqVO pageReqVO);

    /**
     * 查询入驻商订单发票
     * @param req
     * @return
     */
    TrdSupplierOrderInvoiceRespVO getSupplierOrderInvoice(TrdSupplierOrderInvoicePageReqVO req);

    /**
     * 根据入驻商订单编号查询入驻商订单发票信息
     * @param supplierOrderNo
     * @return
     */
    TrdSupplierOrderInvoice getInvoiceBySupplierOrderNo(String supplierOrderNo);

    /**
     * 订单发票接收
     * @param reqVO
     */
    void receiveOrderInvoice(TrdSupplierOrderInvoiceSaveReqVO reqVO);

    /**
     * 更新海信订单发票信息
     * @param supplierOrderInvoice 入驻商订单发票信息
     */
    void updateInvoiceHisense(TrdSupplierOrderInvoice supplierOrderInvoice);

    /**
     * 修改入驻商订单发票信息
     * @param reqDTO
     * @return
     */
    Boolean updateSupplierOrderInvoice(TrdSupplierOrderInvoiceSaveReqDTO reqDTO);
}
