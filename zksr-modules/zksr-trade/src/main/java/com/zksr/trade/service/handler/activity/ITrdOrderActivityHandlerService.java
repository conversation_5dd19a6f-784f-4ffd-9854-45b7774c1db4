package com.zksr.trade.service.handler.activity;

import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.domain.*;

import java.util.List;
import java.util.Map;

public interface ITrdOrderActivityHandlerService {




    /**
     * 订单未支付取消 返还优惠数据信息
     * @param toddList 订单优惠信息数据
     * @param tor 总订单数据
     * @param tsodMap 订单明细数据
     */
    default void orderCancelReturnActivity(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {}


    /**
     * 售后取消 返还 优惠数据信息
     * @param tor 总订单数据
     * @param tsodList 订单明细数据
     * @param toddList 订单优惠信息数据
     * @param tar 总售后订单数据
     * @param tsadList 售后订单明细数据
     * @param taddList 售后订单优惠信息数据
     */
    default void afterCancelReturnActivity(TrdOrder tor,
                                           List<TrdSupplierOrderDtl> tsodList,
                                           List<TrdOrderDiscountDtl> toddList,
                                           TrdAfter tar,
                                           List<TrdSupplierAfterDtl> tsadList,
                                           List<TrdAfterDiscountDtl> taddList)
    {}

    /**
     * 恢复已取消订单折扣信息
     * 已取消订单收到支付成功回调 还原返还的优惠数据信息
     * @param toddList 订单优惠信息数据
     * @param tor 总订单数据
     * @param tsodMap 订单明细数据
     */
    default void restoreCancelledOrderDiscounts(List<TrdOrderDiscountDtl> toddList, TrdOrder tor, Map<Long, TrdSupplierOrderDtl> tsodMap) {}
}
