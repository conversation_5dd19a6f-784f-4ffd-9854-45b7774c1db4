package com.zksr.trade.convert.orderExpressImport;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdExpressImportDtl;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlRespVO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExpressImportDtlSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 快递导入明细 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-15
*/
@Mapper
public interface TrdExpressImportDtlConvert {

    TrdExpressImportDtlConvert INSTANCE = Mappers.getMapper(TrdExpressImportDtlConvert.class);

    TrdExpressImportDtlRespVO convert(TrdExpressImportDtl trdExpressImportDtl);

    TrdExpressImportDtl convert(TrdExpressImportDtlSaveReqVO trdExpressImportDtlSaveReq);

    PageResult<TrdExpressImportDtlRespVO> convertPage(PageResult<TrdExpressImportDtl> trdExpressImportDtlPage);

    List<TrdExpressImportDtlRespVO> convertList(List<TrdExpressImportDtl> trdExpressImportDtls);
}