package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.domain.TrdOrderLog;
import com.zksr.trade.controller.orderLog.vo.TrdOrderLogPageReqVO;
import com.zksr.trade.controller.orderLog.vo.TrdOrderLogSaveReqVO;

/**
 * 订单日志Service接口
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
public interface ITrdOrderLogService {

    /**
     * 新增订单日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdOrderLog(@Valid TrdOrderLogSaveReqVO createReqVO);

    /**
     * 修改订单日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdOrderLog(@Valid TrdOrderLogSaveReqVO updateReqVO);

    /**
     * 删除订单日志
     *
     * @param sysCode 平台商id
     */
    public void deleteTrdOrderLog(Long sysCode);

    /**
     * 批量删除订单日志
     *
     * @param sysCodes 需要删除的订单日志主键集合
     * @return 结果
     */
    public void deleteTrdOrderLogBySysCodes(Long[] sysCodes);

    /**
     * 获得订单日志
     *
     * @param sysCode 平台商id
     * @return 订单日志
     */
    public TrdOrderLog getTrdOrderLog(Long sysCode);

    /**
     * 获得订单日志分页
     *
     * @param pageReqVO 分页查询
     * @return 订单日志分页
     */
    PageResult<TrdOrderLog> getTrdOrderLogPage(TrdOrderLogPageReqVO pageReqVO);

}
