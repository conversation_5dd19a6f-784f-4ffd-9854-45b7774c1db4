package com.zksr.trade.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 订单日志对象 trd_order_log
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@TableName(value = "trd_order_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdOrderLog extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 操作前状态 */
    @Excel(name = "操作前状态")
    private Long beforeState;

    /** 操作后状态 */
    @Excel(name = "操作后状态")
    private Long afterState;

    /** 操作类型(数据字典) */
    @Excel(name = "操作类型(数据字典)")
    private Long operateType;

    /** 订单日志信息 */
    @Excel(name = "订单日志信息")
    private String content;

}
