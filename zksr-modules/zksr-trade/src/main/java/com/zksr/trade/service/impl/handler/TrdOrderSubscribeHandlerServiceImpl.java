package com.zksr.trade.service.impl.handler;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.system.api.commonMessage.SubscribeApi;
import com.zksr.system.api.commonMessage.vo.SubscribeOrderReceiveVO;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdPayOrderPageVO;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 公众号/小程序订阅消息, 订单流程参数数据
 * @date 2024/6/13 16:46
 */
@Slf4j
@Service
@Order(ITrdOrderHandlerService.ORDER_SUBSCRIBE)
public class TrdOrderSubscribeHandlerServiceImpl implements ITrdOrderHandlerService {

    @Resource
    private SubscribeApi subscribeApi;

    /**
     * 订单未付款取消
     */
    @Override
    public void orderCancel(TrdSupplierOrder orderVo) {

    }

    /**
     * 订支付回调
     *
     * @param order 订单
     */
    @Override
    public void orderPay(TrdPayOrderPageVO pageVo, TrdOrder order) {
        try {
            ThreadUtil.execute(() -> {
                subscribeApi.sendNewOrder(order.getOrderId(), order.getSysCode());
            });
        } catch (Exception e) {
            log.error("发送新订单下单通知失败", e);
        }
    }

    /**
     * 本地商品发货
     */
    @Override
    public void orderOutbound(TrdOrder orderVo, Long supplierId) {
        try {
            ThreadUtil.execute(() -> {
                // 发送消息处理
                //subscribeApi.sendDeliveryOrder(orderVo.getOrderId(), supplierId, orderVo.getSysCode());
                subscribeApi.sendWxMerchantDeliveryOrder(orderVo.getOrderId(), supplierId, orderVo.getSysCode());
            });
        } catch (Exception e) {
            log.error("本地商品发货消息订阅", e);
        }
    }


    /**
     * 全国订单发货
     */
    @Override
    public void orderOutbound(TrdSupplierOrderDtl orderDtl) {
        try {
            ThreadUtil.execute(() -> {
                // 发送消息处理
                subscribeApi.globalOrderDelivery(orderDtl.getSupplierOrderDtlId(), orderDtl.getSysCode());
            });
        } catch (Exception e) {
            log.error("全国订单发货消息订阅", e);
        }
    }

    /**
     * 订单收货
     */
    @Override
    public void orderTakeDeliveryFinish(List<TrdSupplierOrderDtl> orderDtlList) {
        try {
            if (ObjectUtil.isEmpty(orderDtlList)) {
                return;
            }
            SubscribeOrderReceiveVO receiveVO = SubscribeOrderReceiveVO
                    .builder()
                    .sysCode(orderDtlList.get(0).getSysCode())
                    .supplierOrderDtlIdList(orderDtlList.stream().map(TrdSupplierOrderDtl::getSupplierOrderDtlId).collect(Collectors.toList()))
                    .build();
            ThreadUtil.execute(() -> {
                // 发送消息处理
                subscribeApi.orderReceive(receiveVO);
            });
        } catch (Exception e) {
            log.error("订单收货订阅", e);
        }
    }
}
