package com.zksr.trade.service.impl.handler;

import cn.hutool.core.thread.ThreadUtil;
import com.zksr.common.core.enums.AfterPhaseEnum;
import com.zksr.common.core.enums.OrderPayWayEnum;
import com.zksr.common.core.enums.PayStateEnum;
import com.zksr.system.api.commonMessage.SubscribeApi;
import com.zksr.system.api.commonMessage.vo.SubscribeOrderAfterFinishVO;
import com.zksr.system.api.commonMessage.vo.SubscribeOrderAfterCreateVO;
import com.zksr.system.api.commonMessage.vo.SubscribeOrderCancelVO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.controller.after.vo.OrderAfterSaveVO;
import com.zksr.trade.domain.TrdSupplierAfter;
import com.zksr.trade.domain.TrdSupplierAfterDtl;
import com.zksr.trade.service.handler.ITrdAfterHandlerService;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 公众号/小程序订阅消息, 订单流程参数数据
 * @date 2024/6/13 16:46
 */
@Slf4j
@Service
@Order(ITrdOrderHandlerService.ORDER_SUBSCRIBE)
public class TrdAfterSubscribeHandlerServiceImpl implements ITrdAfterHandlerService {

    @Resource
    private SubscribeApi subscribeApi;

    /**
     * 发货前取消
     */
    @Override
    public void beforeAfterCreate(OrderAfterSaveVO afterSaveVO, TrdOrder trdOrder, List<TrdSupplierOrder> tsoList) {
        try {
            Map<Long, List<TrdSupplierAfterDtl>> afterPhase = afterSaveVO.getSupplierAfterDtlVOs()
                    .stream()
                    .collect(
                            Collectors.groupingBy(TrdSupplierAfterDtl::getAfterPhase)
                    );
            // 取消
            if (afterPhase.containsKey(AfterPhaseEnum.REDUND_BEFORE_SHIPMENT.getState())) {
                SubscribeOrderCancelVO cancelVO = SubscribeOrderCancelVO.builder()
                        .sysCode(afterSaveVO.getAfterVO().getSysCode())
                        .orderId(trdOrder.getOrderId())
                        .supplierAfterDtlIdList(
                                afterPhase.get(AfterPhaseEnum.REDUND_BEFORE_SHIPMENT.getState()).stream().map(TrdSupplierAfterDtl::getSupplierOrderDtlId).collect(Collectors.toList())
                        ).build();
                ThreadUtil.execute(() -> {
                    subscribeApi.orderCancel(cancelVO);
                });
            }
            // 售后
            if (afterPhase.containsKey(AfterPhaseEnum.REDUND_AFTER_SHIPMENT.getState())) {
                SubscribeOrderAfterCreateVO afterVO = SubscribeOrderAfterCreateVO.builder()
                        .sysCode(afterSaveVO.getAfterVO().getSysCode())
                        .orderId(trdOrder.getOrderId())
                        .afterNo(afterSaveVO.getAfterVO().getAfterNo())
                        .memo(afterSaveVO.getAfterVO().getMemo())
                        .supplierAfterDtlIdList(
                                afterPhase.get(AfterPhaseEnum.REDUND_AFTER_SHIPMENT.getState()).stream().map(TrdSupplierAfterDtl::getSupplierOrderDtlId).collect(Collectors.toList())
                        ).build();
                ThreadUtil.execute(() -> {
                    subscribeApi.afterCreate(afterVO);
                });
            }
        } catch (Exception e) {
            log.error("订阅发货前订单取消通知异常", e);
        }
    }

    /**
     * 入驻商订单审核
     */
    @Override
    public void approveSupplierAfter(TrdSupplierAfter supplierAfter) {
        try {
            ThreadUtil.execute(() -> {
                subscribeApi.afterAudit(supplierAfter.getSupplierAfterId(), supplierAfter.getSysCode());
            });
        } catch (Exception e) {
            log.error("订阅售后审核结果通知异常", e);
        }
    }

    /**
     * 货到付款单子无退货申请，这里处理结算信息
     *
     * @param after
     * @param afterDtls
     */
    @Override
    public void createPayRefundOrder(String orderNo, String trdSupplierNo, TrdAfter after, List<TrdSupplierAfterDtl> afterDtls) {
        try {
            // 当前订单为货到付款的单子才进入
            if (!OrderPayWayEnum.HDFK.getPayWay().equals(after.getPayWay())) {
                return;
            }
            // 过滤出货到付款的, 无需退款的, 直接通知售后完成, 需要退款的等待退款完成
            List<TrdSupplierAfterDtl> hdfkAfterFinishList = afterDtls.stream().filter(item -> Objects.equals(item.getPayState(), PayStateEnum.PAY_NOT_HDFK.getCode())).collect(Collectors.toList());
            if (!hdfkAfterFinishList.isEmpty()) {
                SubscribeOrderAfterFinishVO afterVO = SubscribeOrderAfterFinishVO.builder()
                        .sysCode(after.getSysCode())
                        .supplierAfterDtlIdList(
                                hdfkAfterFinishList.stream().map(TrdSupplierAfterDtl::getSupplierOrderDtlId).collect(Collectors.toList())
                        ).build();
                ThreadUtil.execute(() -> {
                    subscribeApi.afterFinish(afterVO);
                });
            }
        } catch (Exception e) {
            log.error("订阅售后退款完成通知异常", e);
        }
    }

    /**
     * 退款完成事件
     */
    @Override
    public void payRefundSuccess(List<TrdSupplierAfterDtl> afterDtlList) {
        try {
            if (afterDtlList.isEmpty()) {
                return;
            }
            SubscribeOrderAfterFinishVO afterVO = SubscribeOrderAfterFinishVO.builder()
                    .sysCode(afterDtlList.get(0).getSysCode())
                    .supplierAfterDtlIdList(
                            afterDtlList.stream().map(TrdSupplierAfterDtl::getSupplierOrderDtlId).collect(Collectors.toList())
                    ).build();
            ThreadUtil.execute(() -> {
                subscribeApi.afterFinish(afterVO);
            });
        } catch (Exception e) {
            log.error("订阅售后退款完成通知异常", e);
        }
    }
}
