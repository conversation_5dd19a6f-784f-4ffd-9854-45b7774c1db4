package com.zksr.trade.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdDriverRating;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 司机评分Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@Mapper
public interface TrdDriverRatingMapper extends BaseMapperX<TrdDriverRating> {
    default PageResult<TrdDriverRating> selectPage(TrdDriverRatingPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdDriverRating>()
                    .eqIfPresent(TrdDriverRating::getDriverRatingId, reqVO.getDriverRatingId())
                    .eqIfPresent(TrdDriverRating::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdDriverRating::getMemberId, reqVO.getMemberId())
                    .eqIfPresent(TrdDriverRating::getSupplierOrderId, reqVO.getSupplierOrderId())
                    .eqIfPresent(TrdDriverRating::getSlot1Code, reqVO.getSlot1Code())
                    .eqIfPresent(TrdDriverRating::getSlot1Val, reqVO.getSlot1Val())
                    .eqIfPresent(TrdDriverRating::getSlot1ScoreCode, reqVO.getSlot1ScoreCode())
                    .eqIfPresent(TrdDriverRating::getSlot1ScoreCodeVal, reqVO.getSlot1ScoreCodeVal())
                    .eqIfPresent(TrdDriverRating::getSlot2Code, reqVO.getSlot2Code())
                    .eqIfPresent(TrdDriverRating::getSlot2Val, reqVO.getSlot2Val())
                    .eqIfPresent(TrdDriverRating::getSlot2Score, reqVO.getSlot2Score())
                    .eqIfPresent(TrdDriverRating::getSlot2ScoreCodeVal, reqVO.getSlot2ScoreCodeVal())
                    .eqIfPresent(TrdDriverRating::getSlot3Code, reqVO.getSlot3Code())
                    .eqIfPresent(TrdDriverRating::getSlot3Val, reqVO.getSlot3Val())
                    .eqIfPresent(TrdDriverRating::getSlot3Score, reqVO.getSlot3Score())
                    .eqIfPresent(TrdDriverRating::getScoreCodeVal, reqVO.getScoreCodeVal())
                    .eqIfPresent(TrdDriverRating::getReasonCode, reqVO.getReasonCode())
                    .eqIfPresent(TrdDriverRating::getReasonVal, reqVO.getReasonVal())
                    .eqIfPresent(TrdDriverRating::getFedbackMsg, reqVO.getFedbackMsg())
                    .eqIfPresent(TrdDriverRating::getFedbackPics, reqVO.getFedbackPics())
                .orderByDesc(TrdDriverRating::getDriverRatingId));
    }

    Page<TrdDriverRatingRespVO> selectDriverRatingList(@Param("reqVO") TrdDriverRatingPageReqVO reqVO,@Param("page") Page<TrdDriverRatingPageReqVO> page);

    TrdDriverRatingRespVO selectDriverRatingById(@Param("driverRatingId")Long driverRatingId);
}
