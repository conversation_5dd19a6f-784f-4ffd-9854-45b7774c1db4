package com.zksr.trade.controller.driver;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.trade.api.driver.excel.TrdDriverImportExcel;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdDriver;
import com.zksr.trade.service.ITrdDriverService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.driver.vo.TrdDriverPageReqVO;
import com.zksr.trade.controller.driver.vo.TrdDriverSaveReqVO;
import com.zksr.trade.controller.driver.vo.TrdDriverRespVO;
import com.zksr.trade.convert.driver.TrdDriverConvert;
import com.zksr.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 司机档案Controller
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@Api(tags = "管理后台 - 司机档案接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/driver")
public class TrdDriverController {
    @Autowired
    private ITrdDriverService trdDriverService;

    /**
     * 新增司机档案
     */
    @ApiOperation(value = "新增司机档案", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "司机档案", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdDriverSaveReqVO createReqVO) {
        return success(trdDriverService.insertTrdDriver(createReqVO));
    }

    /**
     * 修改司机档案
     */
    @ApiOperation(value = "修改司机档案", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "司机档案", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdDriverSaveReqVO updateReqVO) {
        trdDriverService.updateTrdDriver(updateReqVO);
        return success(true);
    }

    /**
     * 删除司机档案
     */
    @ApiOperation(value = "删除司机档案", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "司机档案", businessType = BusinessType.DELETE)
    @DeleteMapping("/{driverIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] driverIds) {
        trdDriverService.deleteTrdDriverByDriverIds(driverIds);
        return success(true);
    }

    /**
     * 获取司机档案详细信息
     */
    @ApiOperation(value = "获得司机档案详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{driverId}")
    public CommonResult<TrdDriverRespVO> getInfo(@PathVariable("driverId") Long driverId) {
        TrdDriver trdDriver = trdDriverService.getTrdDriver(driverId);
        return success(TrdDriverConvert.INSTANCE.convert(trdDriver));
    }

    /**
     * 分页查询司机档案
     */
    @PostMapping("/list")
    @ApiOperation(value = "获得司机档案分页列表", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdDriverRespVO>> getPage(@Valid TrdDriverPageReqVO pageReqVO) {
        PageResult<TrdDriver> pageResult = trdDriverService.getTrdDriverPage(pageReqVO);
        return success(TrdDriverConvert.INSTANCE.convertPage(pageResult));
    }

    @ApiOperation(value = "导入司机信息", httpMethod = HttpMethod.POST)
    @Log(title = "导入司机信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/importData")
    public CommonResult<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<TrdDriverImportExcel> util = new ExcelUtil<>(TrdDriverImportExcel.class);
        List<TrdDriverImportExcel> driverList = util.importExcel(file.getInputStream(), 1);
        String message = trdDriverService.impordData(driverList);
        return success(message);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "获取司机信息导入模版", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response){
        ExcelUtil<TrdDriverImportExcel> util = new ExcelUtil<TrdDriverImportExcel>(TrdDriverImportExcel.class);
        String instructions = "填表说明：\n" +
                "2、司机名称：必填项，最多20个字符，超出导入不成功；\n" +
                "4、司机电话：必填项，仅限11位数字，超出导入不成功；";

        util.importTemplateExcel(response, "司机信息导入", StringUtils.EMPTY, instructions);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "trade:driver:add";
        /**
         * 编辑
         */
        public static final String EDIT = "trade:driver:edit";
        /**
         * 删除
         */
        public static final String DELETE = "trade:driver:remove";
        /**
         * 列表
         */
        public static final String LIST = "trade:driver:list";
        /**
         * 查询
         */
        public static final String GET = "trade:driver:query";
        /**
         * 停用
         */
        public static final String DISABLE = "trade:driver:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "trade:driver:enable";

        /**
         * 导入
         */
        public static final String IMPORT = "trade:driver:import";
        /**
         * 导出
         */
        public static final String EXPORT = "trade:driver:export";
    }
}
