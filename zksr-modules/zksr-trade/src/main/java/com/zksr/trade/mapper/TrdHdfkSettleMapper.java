package com.zksr.trade.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.trade.api.hdfk.dto.HdfkNoPaymentTotalDTO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkReqVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkOrderItemVO;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSkuItemRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdHdfkSettle;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettlePageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 货到付款结算Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Mapper
public interface TrdHdfkSettleMapper extends BaseMapperX<TrdHdfkSettle> {
    default PageResult<TrdHdfkSettle> selectPage(TrdHdfkSettlePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdHdfkSettle>()
                .orderByDesc(TrdHdfkSettle::getHdfkSettleId));
    }

    List<TrdHdfkOrderItemVO> selectHdfkOrderItemList(BranchHdfkReqVO branchHdfkReqVO);

    HdfkNoPaymentTotalDTO selectBranchHdfkNoPaymentTotal(BranchHdfkReqVO branchHdfkReqVO);

    default List<TrdHdfkSettle> selectByOrderDtlId(List<Long> supplierOrderDtlIdList) {
        return selectList(
                new LambdaQueryWrapperX<TrdHdfkSettle>()
                        .in(TrdHdfkSettle::getSupplierOrderDtlId, supplierOrderDtlIdList)
                        .isNull(TrdHdfkSettle::getFdfkPayDtlId)
        );
    }

    List<TrdHdfkSettleSkuItemRespVO> selectHdfkSkuItemList(TrdHdfkSettlePageReqVO pageReqVO);

    /**
     * 根据订单明细id查询货到付款待结算信息
     * @param supplierOrderDtlIdList
     * @return
     */
    default List<TrdHdfkSettle> selectListByOrderDtlId(List<Long> supplierOrderDtlIdList) {
        return selectList(
                new LambdaQueryWrapperX<TrdHdfkSettle>()
                        .in(TrdHdfkSettle::getSupplierOrderDtlId, supplierOrderDtlIdList)
                        .isNotNull(TrdHdfkSettle::getFdfkPayDtlId)
        );
    }

    List<TrdHdfkOrderItemVO> selectHdfkOrderItemListByOrderDtlIds(@Param("supplierOrderDtlIdList") List<Long> supplierOrderDtlIdList);

    /**
     * 获取支付结算信息
     * @param supplierOrderDtlIdList
     * @return
     */
    List<TrdHdfkSettle> selectByPayOrderDtlId(@Param("supplierOrderDtlIdList") List<Long> supplierOrderDtlIdList);

    /**
     * 根据入驻商订单编号获得货到付款结算
     * @param supplierOrderNo
     * @return
     */
    default List<TrdHdfkSettle> getTrdHdfkSettleList(String supplierOrderNo){
        return selectList(
                new LambdaQueryWrapperX<TrdHdfkSettle>()
                        .eq(TrdHdfkSettle::getSupplierOrderNo, supplierOrderNo)
        );
    }
}
