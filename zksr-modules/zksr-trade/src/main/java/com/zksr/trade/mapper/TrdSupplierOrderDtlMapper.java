package com.zksr.trade.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.enums.PayStateEnum;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.api.spu.dto.SpuDeleteCheckDTO;
import com.zksr.trade.api.order.dto.*;
import com.zksr.trade.api.order.vo.CouponExtendTotalVO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdOrderTakeDeliveryVO;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderDtlVO;
import com.zksr.trade.controller.order.vo.TrdOrderPromitionReportPageReqVO;
import com.zksr.trade.controller.order.vo.TrdOrderSupplierOperVo;
import com.zksr.trade.controller.orderExpressImport.dto.TrdExportOrderDtlDTO;
import com.zksr.trade.controller.orderExpressImport.vo.TrdExportOrderDtlVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderDtlPageReqVO;
import com.zksr.trade.domain.TrdSupplierOrderDtl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 入驻商订单明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Mapper
public interface TrdSupplierOrderDtlMapper extends BaseMapperX<TrdSupplierOrderDtl> {
    default PageResult<TrdSupplierOrderDtl> selectPage(TrdSupplierOrderDtlPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdSupplierOrderDtl>()
                .eqIfPresent(TrdSupplierOrderDtl::getSupplierOrderDtlId, reqVO.getSupplierOrderDtlId())
                .eqIfPresent(TrdSupplierOrderDtl::getSysCode, reqVO.getSysCode())
                .eqIfPresent(TrdSupplierOrderDtl::getSupplierOrderDtlNo, reqVO.getSupplierOrderDtlNo())
                .eqIfPresent(TrdSupplierOrderDtl::getSupplierOrderId, reqVO.getSupplierOrderId())
                .eqIfPresent(TrdSupplierOrderDtl::getSupplierOrderNo, reqVO.getSupplierOrderNo())
                .eqIfPresent(TrdSupplierOrderDtl::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(TrdSupplierOrderDtl::getSpuId, reqVO.getSpuId())
                .eqIfPresent(TrdSupplierOrderDtl::getSkuId, reqVO.getSkuId())
                .eqIfPresent(TrdSupplierOrderDtl::getThumb, reqVO.getThumb())
                .eqIfPresent(TrdSupplierOrderDtl::getThumbVideo, reqVO.getThumbVideo())
                .eqIfPresent(TrdSupplierOrderDtl::getImages, reqVO.getImages())
                .eqIfPresent(TrdSupplierOrderDtl::getDetails, reqVO.getDetails())
                .eqIfPresent(TrdSupplierOrderDtl::getTotalNum, reqVO.getTotalNum())
                .eqIfPresent(TrdSupplierOrderDtl::getTotalAmt, reqVO.getTotalAmt())
                .eqIfPresent(TrdSupplierOrderDtl::getPrice, reqVO.getPrice())
                .eqIfPresent(TrdSupplierOrderDtl::getSalePrice, reqVO.getSalePrice())
                .eqIfPresent(TrdSupplierOrderDtl::getDeliveryState, reqVO.getDeliveryState())
                .eqIfPresent(TrdSupplierOrderDtl::getGiftFlag, reqVO.getGiftFlag())
                .eqIfPresent(TrdSupplierOrderDtl::getMemo, reqVO.getMemo())
                .eqIfPresent(TrdSupplierOrderDtl::getSyncFlag, reqVO.getSyncFlag())
                .eqIfPresent(TrdSupplierOrderDtl::getDelFlag, reqVO.getDelFlag())
                .orderByDesc(TrdSupplierOrderDtl::getSupplierOrderDtlId));
    }

    /**
     * 根据单据ID更改商品配送状态
     *
     * @param operVo
     */
    public  void updateDeliveryByOrderId(TrdOrderSupplierOperVo operVo);

    /**
     * 查询订单明细
     * @param orderDtl 订单明细
     * @return
     */
    default List<TrdSupplierOrderDtl> getSupplierOrdeDtl(TrdSupplierOrderDtl orderDtl){
        return selectList(new LambdaQueryWrapperX<TrdSupplierOrderDtl>()
                .eqIfPresent(TrdSupplierOrderDtl::getOrderId, orderDtl.getOrderId())
                .eqIfPresent(TrdSupplierOrderDtl::getSupplierId, orderDtl.getSupplierId())
                .eqIfPresent(TrdSupplierOrderDtl::getItemType, orderDtl.getItemType()));
    }

    /**
     * @Description: 获取入驻商小程序订单详情
     * @Author: liuxingyu
     * @Date: 2024/4/11 9:54
     */
    default List<TrdSupplierOrderDtl> getSupplierOrderDtl(Long orderId, Long supplierId) {
        return selectList(new LambdaQueryWrapper<TrdSupplierOrderDtl>()
                .eq(TrdSupplierOrderDtl::getOrderId, orderId)
                .eq(TrdSupplierOrderDtl::getSupplierId, supplierId).eq(TrdSupplierOrderDtl::getItemType,"1")
                .last("and total_num != cancel_qty"));
    }

    /**
     * 根据条件查询出已装车订单且超出自动收货时间的订单明细
     * @param takeDeliveryVO
     */
    public List<TrdSupplierOrderDtl> getEntruckingOrderDtlList(TrdOrderTakeDeliveryVO takeDeliveryVO);


    /**
     * 根据条件查询出已收货订单且超出自动完成时间的订单明细
     * @param takeDeliveryVO
     */
    public List<TrdSupplierOrderDtl> getCompleteOrderDtlList(TrdOrderTakeDeliveryVO takeDeliveryVO);

    /**
     * 获取供应商下全国订单未发货的单据日期
     * @param supplierId
     * @return
     */
    public List<String> getNotDeliveryNationwideOrderDate(@Param("supplierId") Long supplierId);

    /**
     * 根据入驻商信息获取供应商下全国订单未发货的单据明细
     * @param expressVo
     * @return
     */
    public List<TrdExportOrderDtlDTO> getNotDeliveryNationwideOrderDtl(TrdExportOrderDtlVO expressVo);

    /**
     * 根据订单明细单号查询订单明细
     * @param supplierOrderDtlNo
     * @return
     */
    default TrdSupplierOrderDtl getSupplierOrderDtlBySupplierOrderDtlNo(String supplierOrderDtlNo) {
        return selectOne(new LambdaQueryWrapper<TrdSupplierOrderDtl>()
                .eq(TrdSupplierOrderDtl::getSupplierOrderDtlNo, supplierOrderDtlNo));
    }

    /**
     * 根据主单ID 获取入驻商订单明细信息
     * @param orderId 主单ID
     * @return
     */
    default List<TrdSupplierOrderDtl> selectListByOrderId(Long orderId) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierOrderDtl>()
                .eqIfPresent(TrdSupplierOrderDtl::getOrderId, orderId)
                .orderByAsc(TrdSupplierOrderDtl::getSupplierId));
    }

    /**
     * 根据入驻商订单ID 获取入驻商订单明细信息
     * @param supplierOrderId 入驻商订单ID
     * @return
     */
    default List<TrdSupplierOrderDtl> selectListBySupplierOrderId(Long supplierOrderId) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierOrderDtl>()
                .eqIfPresent(TrdSupplierOrderDtl::getSupplierOrderId, supplierOrderId));
    }


    List<CouponExtendTotalVO> selectCouponExtendTotal(@Param("couponTemplateIdList") List<Long> couponTemplateIdList);

    /**
     * 获取该优惠劵模板 和 参与客户的 使用优惠劵订单商品汇总数据
     * @param couponTemplateId 优惠券模版ID
     * @param customerIdList 使用优惠劵的客户ID集合
     * @return
     */
    List<CouponCustomerOrderUseTotalDTO> getCouponCustomerOrderUseTotal(@Param("couponTemplateId") Long couponTemplateId, @Param("customerIdList") List<Long> customerIdList);


    /**
     * 获取该优惠劵模板 和 参与客户的 使用优惠劵订单商品数据
     * @param pageReqVO
     * @return
     */
    List<CouponCustomerOrderUseTotalDTO> getCouponCustomerOrderUseDetailTotal(TrdOrderPromitionReportPageReqVO pageReqVO);


    /**
     * 获取该优惠劵模板对应订单商品数据详情
     * @param pageReqVO
     * @return
     */
    List<CouponCustomerOrderUseDetilDTO> getCouponCustomerOrderUseDetail(@Param("pageReqVO")TrdOrderPromitionReportPageReqVO pageReqVO);

    /**
     * 查询活动订单汇总数据
     * @param activityIds
     * @return
     */
    List<ActivityOrderTotalDTO> getPrmActivityByActivityIds(@Param("activityIds") List<Long> activityIds);


    /**
     * 获取参与该活动的客户订单汇总数据(按客户区分)
     * @param pageReqVO
     * @return
     */
    List<ActivityCustomerOrderUseTotalDTO> getActivityCustomerTotal(TrdOrderPromitionReportPageReqVO pageReqVO);


    /**
     * 获取使用该活动 指定客户的订单报表（按客户订单汇总）
     * @param pageReqVO
     * @return
     */
    List<ActivityCustomerOrderUseTotalDTO> getActivityCustomerOrderTotal(TrdOrderPromitionReportPageReqVO pageReqVO);



    List<ActivityCustomerOrderUseDetailDTO> getActivityCustomerOrderDetail(TrdOrderPromitionReportPageReqVO pageReqVO);


    /**
     * 根据入驻商订单主表编号 获取入驻商订单明细信息
     * @param supplierOrderNo 入驻商订单主表编号
     * @return
     */
    default List<TrdSupplierOrderDtl> selectListBySupplierOrderNo(String supplierOrderNo) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierOrderDtl>()
                .eqIfPresent(TrdSupplierOrderDtl::getSupplierOrderNo, supplierOrderNo));
    }

    /**
     * 根据入驻商订单明细编号集合查询信息
     * @param dtlIdList 入驻商订单明细表编号集合
     * @return
     */
    default List<TrdSupplierOrderDtl> selectListByDtlIdList(List<Long> dtlIdList) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierOrderDtl>()
                .inIfPresent(TrdSupplierOrderDtl::getSupplierOrderDtlId, dtlIdList));
    }

    /**
     * 批量修改入驻商订单详情状态
     * @param supplierOrderNo  订单号
     * @param code  修改的状态
     * @param conditionCode  条件中的状态
     */
    default void updateBatchDeliveryStateBySupplierOrderNo(String supplierOrderNo,Long code,Long conditionCode){
        update(null,new LambdaUpdateWrapper<TrdSupplierOrderDtl>()
                .set(TrdSupplierOrderDtl::getDeliveryState, code)
                .eq(TrdSupplierOrderDtl::getSupplierOrderNo,supplierOrderNo)
                .eq(TrdSupplierOrderDtl::getDeliveryState,conditionCode)
        );
    }

    /**
     * 根据入驻商订单明细Id集合查询信息
     * @param supplierOrderDtlIdList  入驻商订单明细Id集合
     */
    default List<TrdSupplierOrderDtl> selectListBySupplierOrderDtlId(List<Long> supplierOrderDtlIdList){
        return selectList(new LambdaUpdateWrapper<TrdSupplierOrderDtl>()
                .in(TrdSupplierOrderDtl::getSupplierOrderDtlId, supplierOrderDtlIdList)
                .last("AND total_num = cancel_qty")
        );
    }

    /**
     * 查询订单销售数据 商品名称*数量
     * @param supplierOrderIds
     * @return
     */
    default List<TrdSupplierOrderDtl> selectBySpuNameAndTotalNum(List<Long> supplierOrderIds) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierOrderDtl>()
                .in(TrdSupplierOrderDtl::getSupplierOrderId, supplierOrderIds)
                .select(TrdSupplierOrderDtl::getSpuName, TrdSupplierOrderDtl::getTotalNum, TrdSupplierOrderDtl::getSupplierOrderId)
        );
    }

    /**
     * 删除商品信息 前置校验 需要删除的商品是否下单
     * @param spuIds 需要校验的SPUID集合
     * @return 已经下过单的SPUID集合
     */
    List<Long> deleteSpuCheckSupplierOrderDtl(@Param("spuIds") Long[] spuIds);

    default List<TrdSupplierOrderDtl> getCategoryExistOrder(Long categoryId, Long sysCode){
        return selectList(new LambdaQueryWrapper<TrdSupplierOrderDtl>()
                .eq(TrdSupplierOrderDtl::getCategoryId, categoryId)
                .eq(TrdSupplierOrderDtl::getSysCode, sysCode)
                .last("LIMIT 1")
        );
    }

    /**
     * 查询该订单下的所有入驻商订单详情是否全部取消
     * @param orderId
     * @return
     */
    default Long checkOrderCancel(Long orderId){
        return selectCount(new LambdaQueryWrapper<TrdSupplierOrderDtl>()
                .eq(TrdSupplierOrderDtl::getOrderId, orderId)
                .ne(TrdSupplierOrderDtl::getDeliveryState, DeliveryStatusEnum.CANCEL.getCode()));
    }

    Integer checkOrderDeliveryStatusCountByBranchId(@Param("branchId")Long branchId,@Param("orderId")Long orderId);

    /**
     * 根据订单号查询入住商订单详情
     * @param orderNo
     * @return
     */
    List<TrdSupplierOrderDtlVO> getSupplierOrderDtlByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据多个订单号查询入住商订单详情
     * @param orderNos
     * @return
     */
    List<TrdSupplierOrderDtlVO> getSupplierOrderDtlByOrderNos(@Param("orderNos") List<String> orderNos);
}
