package com.zksr.trade.controller.car;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdCar;
import com.zksr.trade.service.ITrdCarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.car.vo.TrdCarPageReqVO;
import com.zksr.trade.controller.car.vo.TrdCarSaveReqVO;
import com.zksr.trade.controller.car.vo.TrdCarRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 购物车Controller
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Api(tags = "管理后台 - 购物车接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/car")
public class TrdCarController {
    @Autowired
    private ITrdCarService trdCarService;

    /**
     * 新增购物车
     */
    @ApiOperation(value = "新增购物车", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "购物车", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdCarSaveReqVO createReqVO) {
        return success(trdCarService.insertTrdCar(createReqVO));
    }

    /**
     * 修改购物车
     */
    @ApiOperation(value = "修改购物车", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "购物车", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdCarSaveReqVO updateReqVO) {
            trdCarService.updateTrdCar(updateReqVO);
        return success(true);
    }

    /**
     * 删除购物车
     */
    @ApiOperation(value = "删除购物车", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "购物车", businessType = BusinessType.DELETE)
    @DeleteMapping("/{carIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] carIds) {
        trdCarService.deleteTrdCarByCarIds(carIds);
        return success(true);
    }

    /**
     * 获取购物车详细信息
     */
    @ApiOperation(value = "获得购物车详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{carId}")
    public CommonResult<TrdCarRespVO> getInfo(@PathVariable("carId") Long carId) {
        TrdCar trdCar = trdCarService.getTrdCar(carId);
        return success(HutoolBeanUtils.toBean(trdCar, TrdCarRespVO.class));
    }

    /**
     * 分页查询购物车
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得购物车分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdCarRespVO>> getPage(@Valid TrdCarPageReqVO pageReqVO) {
        PageResult<TrdCar> pageResult = trdCarService.getTrdCarPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, TrdCarRespVO.class));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:car:add";
        /** 编辑 */
        public static final String EDIT = "trade:car:edit";
        /** 删除 */
        public static final String DELETE = "trade:car:remove";
        /** 列表 */
        public static final String LIST = "trade:car:list";
        /** 查询 */
        public static final String GET = "trade:car:query";
        /** 停用 */
        public static final String DISABLE = "trade:car:disable";
        /** 启用 */
        public static final String ENABLE = "trade:car:enable";
    }
}
