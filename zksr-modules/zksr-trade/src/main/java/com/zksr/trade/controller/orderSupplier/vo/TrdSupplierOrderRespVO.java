package com.zksr.trade.controller.orderSupplier.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 入驻商订单对象 trd_supplier_order
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Data
@ApiModel("入驻商订单 - trd_supplier_order Response VO")
public class TrdSupplierOrderRespVO {
    private static final long serialVersionUID = 1L;

    /** 入驻商订单id */
    @ApiModelProperty(value = "入驻商订单id")
    private Long supplierOrderId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    /** 订单id */
    @Excel(name = "订单id")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 配送费 */
    @Excel(name = "配送费")
    @ApiModelProperty(value = "配送费")
    private BigDecimal transAmt;


    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "spuId")
    private Long spuId;

    @ApiModelProperty(value = "skuId")
    private Long skuId;

    @ApiModelProperty(value = "spuNo")
    private String spuNo;

    @ApiModelProperty(value = "封面图")
    private String thumb;

    @ApiModelProperty("sup名称")
    private String spuName;

    @ApiModelProperty("国际条码")
    private String barcode;

    @Excel(name = "属性", cellType = Excel.ColumnType.STRING)
    @ApiModelProperty(value = "属性")
    private String properties;

    @ApiModelProperty("商品规格")
    private String specName;

    /** 最旧生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最旧生产日期")
    private Date oldestDate;

    /** 最新生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最新生产日期")
    private Date latestDate;

    @ApiModelProperty("商品数量")
    private Long totalNum;

    @ApiModelProperty("价格")
    private BigDecimal price;

    @ApiModelProperty("发货前取消数量")
    private Long cancelQty;

    @ApiModelProperty("发货前取消金额")
    private BigDecimal cancelAmt;

    @ApiModelProperty("商品单位")
    private String orderUnit;

    @ApiModelProperty("商品单位名称")
    private String unit;

    @ApiModelProperty("是否是赠品 1-是  0-否")
    private String giftFlag;

    @ApiModelProperty("订单购买单位价格")
    private BigDecimal orderUnitPrice;


}
