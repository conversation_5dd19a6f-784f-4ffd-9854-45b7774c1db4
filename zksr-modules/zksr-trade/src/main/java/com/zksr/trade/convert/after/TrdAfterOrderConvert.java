package com.zksr.trade.convert.after;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.trade.api.after.dto.OrderAfterDtlResDTO;
import com.zksr.trade.api.after.vo.OrderAfterResp;
import com.zksr.trade.api.after.vo.OrderMergeAfterRequest;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.domain.*;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024年04月21日 09:34
 * @description: TrdAfterOrderConvert
 */
@Mapper
public interface TrdAfterOrderConvert {

    TrdAfterOrderConvert INSTANCE = Mappers.getMapper(TrdAfterOrderConvert.class);

    @Mappings({
            @Mapping(source = "orderSaveVO.orderId", target = "orderId"),
            @Mapping(source = "resp.source", target = "source")
    })
    TrdAfter convertAfter(TrdOrder orderSaveVO, OrderAfterResp resp);

    TrdSupplierAfter convertSupplierAfter(TrdSupplierOrder supplierOrder);

    @Mappings({
            @Mapping(source = "supplierOrderDtl.supplierOrderDtlId", target = "supplierOrderDtlId"),
            @Mapping(source = "supplierAfterDtl.refundQty", target = "returnUnitQty"),
            @Mapping(source = "supplierAfterDtl.refundPrice", target = "returnPrice"),
            @Mapping(source = "supplierAfterDtl.refundAmt", target = "returnAmt"),
            @Mapping(source = "supplierAfterDtl.refundAmt", target = "refundAmt"),
            @Mapping(source = "after.memo", target = "memo"),
            @Mapping(source = "supplierOrderDtl.resPrice", target = "exactReturnPrice"),
            @Mapping(source = "supplierOrderDtl.sysCode", target = "sysCode"),
            @Mapping(source = "supplierAfterDtl.unitType", target = "returnUnitType"),
            @Mapping(source = "supplierOrderDtl.brandId", target = "brandId"),
            @Mapping(source = "supplierOrderDtl.platform", target = "platform"),
            @Mapping(source = "supplierOrderDtl.payState", target = "payState"),
            @Mapping(source = "supplierOrderDtl.categoryId", target = "categoryId"),
            @Mapping(source = "supplierAfterDtl.refundPrice", target = "returnSalesUnitPrice"),
            @Mapping(source = "supplierOrderDtl.oldestDate", target = "oldestDate"),
            @Mapping(source = "supplierOrderDtl.latestDate", target = "latestDate"),
            @Mapping(source = "supplierOrderDtl.salePrice", target = "returnOriginalPrice"),
            @Mapping(source = "supplierOrderDtl.orderUnitPrice", target = "returnOriginalUnitPrice"),
            @Mapping(source = "supplierAfterDtl.afterPhase", target = "afterPhase")
    })
    TrdSupplierAfterDtl convertSupplierAfterDtl(TrdSupplierOrderDtl supplierOrderDtl, OrderAfterResp.SupplierOrder.SupplierOrderDtl supplierAfterDtl, TrdAfter after);

    @Mappings({
            @Mapping(source = "supplierOrderSettle.supplierOrderDtlId", target = "supplierOrderDtlId"),
            @Mapping(source = "supplierAfterDtl.refundQty", target = "returnQty"),
            @Mapping(source = "supplierAfterDtl.refundPrice", target = "returnPrice"),
            @Mapping(source = "supplierAfterDtl.refundAmt", target = "returnAmt")
    })
    TrdSupplierAfterSettle convertSupplierAfterSettle(TrdSupplierOrderSettle supplierOrderSettle, OrderAfterResp.SupplierOrder.SupplierOrderDtl supplierAfterDtl);


    /**
     *  售后单明细数据构建
     * @param orderDtl
     * @param afterDtl
     * @param after
     * @return
     */
    default TrdSupplierAfterDtl convertSupplierAfterDtlCreate(TrdSupplierOrderDtl orderDtl, OrderAfterResp.SupplierOrder.SupplierOrderDtl afterDtl, TrdAfter after, OrderAfterDtlResDTO orderAfterDtlResDTO, SpuDTO spu){
        TrdSupplierAfterDtl supplierAfterDtl = convertSupplierAfterDtl(orderDtl, afterDtl, after);

        // 单位转换数量
        BigDecimal unitSizeQty = orderDtl.getOrderUnitSize();
        // 单位
        String unit = orderDtl.getOrderUnit();
        if (Objects.equals(UnitTypeEnum.UNIT_SMALL.getType(), afterDtl.getUnitType())) {
            unitSizeQty = BigDecimal.ONE;
            unit = spu.getUnit(afterDtl.getUnitType());
        }

        supplierAfterDtl.setReturnUnit(unit);
        supplierAfterDtl.setReturnUnitType(afterDtl.getUnitType());
        supplierAfterDtl.setReturnUnitSize(unitSizeQty);

        supplierAfterDtl.setReturnQty(supplierAfterDtl.getReturnUnitQty().multiply(supplierAfterDtl.getReturnUnitSize()));

        if (orderDtl.getItemType() == ProductType.GLOBAL.getCode()) {
            supplierAfterDtl.setItemId(orderDtl.getSupplierItemId());
        }
        if (orderDtl.getItemType() == ProductType.LOCAL.getCode()) {
            supplierAfterDtl.setItemId(orderDtl.getAreaItemId());
        }

        // 售后阶段
        if (supplierAfterDtl.getAfterPhase() == null) {
            if (orderDtl.getDeliveryState().compareTo(DeliveryStatusEnum.WAIT_PH.getCode()) == 0
                    || orderDtl.getDeliveryState().compareTo(DeliveryStatusEnum.WAIT_FH.getCode()) == 0
                    || orderDtl.getDeliveryState().compareTo(DeliveryStatusEnum.PREAREGOODS.getCode()) == 0) {
                supplierAfterDtl.setAfterPhase(StatusConstants.AFTER_PHASE_1);
            } else {
                supplierAfterDtl.setAfterPhase(StatusConstants.AFTER_PHASE_2);
            }
        }

        // 订单总下单最小单位数量
        BigDecimal totalNum = orderDtl.getTotalNum();
        // 退货类型
        if (totalNum.compareTo(supplierAfterDtl.getReturnQty()) == 0) {
            supplierAfterDtl.setRefundType(StatusConstants.REFUND_TYPE_1);
        } else {
            supplierAfterDtl.setRefundType(StatusConstants.REFUND_TYPE_2);
        }
        supplierAfterDtl.setApproveState(0l); // 审核状态
        supplierAfterDtl.setReturnState(0L); // 退货状态
        supplierAfterDtl.setRefundState(0L); // 退款状态

        /**
         * 计算退款金额
         */
        supplierAfterDtl.setExactReturnAmt(supplierAfterDtl.getExactReturnPrice().multiply(new BigDecimal(supplierAfterDtl.getReturnQty()+"")));   // 商品售后金额 = 售后小单位单价 * 售后小单位数量
        supplierAfterDtl.setReturnPrice(supplierAfterDtl.getExactReturnPrice().setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)); // 退货单价
        supplierAfterDtl.setReturnAmt(supplierAfterDtl.getExactReturnAmt().setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)); // 退货金额



        // 当本次退货数量+之前的退货数量 等于 订单数量时，本次退款金额 = 订单金额 - 已售后金额
        if (ToolUtil.isNotEmpty(orderAfterDtlResDTO)
                && totalNum.compareTo(orderAfterDtlResDTO.getSumReturnNum().add(supplierAfterDtl.getReturnQty())) <= 0){
            supplierAfterDtl.setReturnAmt(orderDtl.getResAmt().subtract(orderAfterDtlResDTO.getSumReturnAmt()));
        }
        // 当前退款金额 = （（当前退货数量+之前退货数量）* 退货单价） - 之前退款金额
        else if (ToolUtil.isNotEmpty(orderAfterDtlResDTO)) {
            BigDecimal returnAmt = orderDtl.getResPrice().multiply(supplierAfterDtl.getReturnQty().add(orderAfterDtlResDTO.getSumReturnNum()))
                    .setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
            supplierAfterDtl.setReturnAmt(returnAmt.subtract(orderAfterDtlResDTO.getSumReturnAmt())
                    .setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));
        }
        // 当当前商品全部一起退时，退款金额 = 订单金额
        else if (totalNum.compareTo(supplierAfterDtl.getReturnQty()) <= 0) {
            supplierAfterDtl.setReturnAmt(orderDtl.getResAmt());
        }
        supplierAfterDtl.setOriginalReturnQty(supplierAfterDtl.getReturnQty());
        supplierAfterDtl.setOriginalReturnAmt(supplierAfterDtl.getReturnAmt());
        // 当本次退货数量+之前的退货数量 大于 订单数量时，则抛出异常
        if (ToolUtil.isNotEmpty(orderAfterDtlResDTO)
                && totalNum.compareTo(orderAfterDtlResDTO.getSumReturnNum().add(afterDtl.getRefundQty())) < 0){
            throw new ServiceException(
                    StringUtils.format("订单【{}】中商品【{}】下单总数量【{}】，已退货数量【{}】，本次退货数量【{}】，超出下单数量，请刷新页面数据后重新操作！",
                            orderDtl.getOrderId(),
                            orderDtl.getSpuName(),
                            totalNum,
                            orderAfterDtlResDTO.getSumReturnNum(),
                            afterDtl.getRefundQty())
            );
        }
        else if (totalNum.compareTo(afterDtl.getRefundQty()) < 0) {
            throw new ServiceException(
                    StringUtils.format(
                            "订单【{}】中商品【{}】下单总数量【{}】，本次退货数量【{}】，超出下单数量，请刷新页面数据后重新操作！",
                            orderDtl.getOrderId(),
                            orderDtl.getSpuName(),
                            totalNum,
                            afterDtl.getRefundQty()
                    )
            );
        }
        supplierAfterDtl.setRefundAmt(supplierAfterDtl.getReturnAmt()); // 退款金额
        supplierAfterDtl.setOrderdtlRefundAmt(supplierAfterDtl.getReturnAmt().add(ToolUtil.isEmpty(orderAfterDtlResDTO) ? BigDecimal.ZERO : orderAfterDtlResDTO.getSumReturnAmt())); // 退款总金额(包括之前售后的售后总金额)

        /**
         * 计算本地售后优惠金额信息
         */
        // 优惠劵优惠金额 （分摊）
        BigDecimal singleCouponDiscountAmt = orderDtl.getCouponDiscountAmt().divide(totalNum, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);
        supplierAfterDtl.setReturnCouponDiscountAmt(singleCouponDiscountAmt.multiply(supplierAfterDtl.getReturnQty()).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));

        // 优惠劵优惠金额 （不分摊）
        BigDecimal singleCouponDiscountAmt2 = orderDtl.getCouponDiscountAmt2().divide(totalNum, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);
        supplierAfterDtl.setReturnCouponDiscountAmt2(singleCouponDiscountAmt2.multiply(supplierAfterDtl.getReturnQty()).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));

        // 活动优惠金额 （分摊）
        BigDecimal singleActivityDiscountAmt = orderDtl.getActivityDiscountAmt().divide(totalNum, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);
        supplierAfterDtl.setReturnActivityDiscountAmt(singleActivityDiscountAmt.multiply(supplierAfterDtl.getReturnQty()).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP));

        supplierAfterDtl.setIsCancel(StatusConstants.ISCANCEL_0);
        supplierAfterDtl.setSyncFlag(StatusConstants.FLAG_FALSE);
        return supplierAfterDtl;
    }


    @Mappings({
            @Mapping(source = "request.branchId", target = "branchId"),
            @Mapping(source = "request.reason", target = "reason"),
            @Mapping(source = "request.applyImgs", target = "applyImgs"),
            @Mapping(source = "request.source", target = "source")
    })
    @BeanMapping(ignoreByDefault = true)
    TrdAfter convertAfter1(OrderMergeAfterRequest request);

    @Mappings({
            @Mapping(source = "request.supplierId", target = "supplierId"),
            @Mapping(source = "request.returnPhone", target = "returnPhone"),
            @Mapping(source = "request.returnAddr", target = "returnAddr")
    })
    @BeanMapping(ignoreByDefault = true)
    TrdSupplierAfter convertSupplierAfter(OrderMergeAfterRequest request);

}
