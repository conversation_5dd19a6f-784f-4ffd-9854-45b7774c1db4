package com.zksr.trade.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.trade.api.car.dto.AppCarInitDTO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.domain.TrdCar;
import com.zksr.trade.controller.car.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 购物车Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Mapper
public interface TrdCarMapper extends BaseMapperX<TrdCar> {
    default PageResult<TrdCar> selectPage(TrdCarPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdCar>()
                    .eqIfPresent(TrdCar::getCarId, reqVO.getCarId())
                    .eqIfPresent(TrdCar::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(TrdCar::getCreateMemberId, reqVO.getCreateMemberId())
                    .eqIfPresent(TrdCar::getUpdateMemberId, reqVO.getUpdateMemberId())
                    .eqIfPresent(TrdCar::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(TrdCar::getSupplierItemId, reqVO.getSupplierItemId())
                    .eqIfPresent(TrdCar::getAreaItemId, reqVO.getAreaItemId())
                    .eqIfPresent(TrdCar::getSpuId, reqVO.getSpuId())
                    .eqIfPresent(TrdCar::getSkuId, reqVO.getSkuId())
                    .eqIfPresent(TrdCar::getQty, reqVO.getQty())
                    .eqIfPresent(TrdCar::getSelected, reqVO.getSelected())
                    .eqIfPresent(TrdCar::getRecommendFlag, reqVO.getRecommendFlag())
                .orderByDesc(TrdCar::getCarId));
    }

    /**
     * 通过门店ID 和 skuId 获取购物车数据唯一
     * @param branchId  门店ID
     * @param skuId     skuId
     * @return
     */
    default TrdCar selectByBranchIdAndSkuId(Long branchId, Long skuId) {
        return selectOne(
                Wrappers.lambdaQuery(TrdCar.class)
                        .eq(TrdCar::getBranchId, branchId)
                        .eq(TrdCar::getSkuId, skuId)
                        .last(StringPool.LIMIT_ONE)
        );
    }

    /**
     * 获取初始化数据
     * @param branchId  门店ID
     * @param minId     最小键ID
     * @return
     */
    List<AppCarInitDTO> selectInitData(@Param("branchId") Long branchId, @Param("minId") Long minId);

    default void deleteByBranchId(@Param("branchId") Long branchId) {
        delete(
                Wrappers.lambdaQuery(TrdCar.class)
                        .eq(TrdCar::getBranchId, branchId)
        );
    }
}
