package com.zksr.trade.controller.after;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.domain.TrdAfterLog;
import com.zksr.trade.service.ITrdAfterLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.after.logVo.TrdAfterLogPageReqVO;
import com.zksr.trade.controller.after.logVo.TrdAfterLogSaveReqVO;
import com.zksr.trade.controller.after.logVo.TrdAfterLogRespVO;
import com.zksr.trade.convert.after.TrdAfterLogConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 售后日志Controller
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Api(tags = "管理后台 - 售后日志接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/afterLog")
public class TrdAfterLogController {
    @Autowired
    private ITrdAfterLogService trdAfterLogService;

    /**
     * 新增售后日志
     */
    @ApiOperation(value = "新增售后日志", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "售后日志", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdAfterLogSaveReqVO createReqVO) {
        return success(trdAfterLogService.insertTrdAfterLog(createReqVO));
    }

    /**
     * 修改售后日志
     */
    @ApiOperation(value = "修改售后日志", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "售后日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdAfterLogSaveReqVO updateReqVO) {
            trdAfterLogService.updateTrdAfterLog(updateReqVO);
        return success(true);
    }

    /**
     * 删除售后日志
     */
    @ApiOperation(value = "删除售后日志", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "售后日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{afterLogIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] afterLogIds) {
        trdAfterLogService.deleteTrdAfterLogByAfterLogIds(afterLogIds);
        return success(true);
    }

    /**
     * 获取售后日志详细信息
     */
    @ApiOperation(value = "获得售后日志详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{afterLogId}")
    public CommonResult<TrdAfterLogRespVO> getInfo(@PathVariable("afterLogId") Long afterLogId) {
        TrdAfterLog trdAfterLog = trdAfterLogService.getTrdAfterLog(afterLogId);
        return success(TrdAfterLogConvert.INSTANCE.convert(trdAfterLog));
    }

    /**
     * 分页查询售后日志
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得售后日志分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdAfterLogRespVO>> getPage(@Valid TrdAfterLogPageReqVO pageReqVO) {
        PageResult<TrdAfterLog> pageResult = trdAfterLogService.getTrdAfterLogPage(pageReqVO);
        return success(TrdAfterLogConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:log:add";
        /** 编辑 */
        public static final String EDIT = "trade:log:edit";
        /** 删除 */
        public static final String DELETE = "trade:log:remove";
        /** 列表 */
        public static final String LIST = "trade:log:list";
        /** 查询 */
        public static final String GET = "trade:log:query";
        /** 停用 */
        public static final String DISABLE = "trade:log:disable";
        /** 启用 */
        public static final String ENABLE = "trade:log:enable";
    }
}
