package com.zksr.trade.controller.status;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.controller.status.vo.TrdExpressStatusPageReqVO;
import com.zksr.trade.controller.status.vo.TrdExpressStatusRespVO;
import com.zksr.trade.controller.status.vo.TrdExpressStatusSaveReqVO;
import com.zksr.trade.convert.status.TrdExpressStatusConvert;
import com.zksr.trade.domain.TrdExpressStatus;
import com.zksr.trade.service.ITrdExpressStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 物流状态（ERP->B2B）Controller
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@Api(tags = "管理后台 - 物流状态（ERP->B2B）接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/status")
public class TrdExpressStatusController {
    @Autowired
    private ITrdExpressStatusService trdExpressStatusService;

    /**
     * 新增物流状态（ERP->B2B）
     */
    @ApiOperation(value = "新增物流状态（ERP->B2B）", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "物流状态（ERP->B2B）", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdExpressStatusSaveReqVO createReqVO) {
        return success(trdExpressStatusService.insertTrdExpressStatus(createReqVO));
    }

    /**
     * 修改物流状态（ERP->B2B）
     */
    @ApiOperation(value = "修改物流状态（ERP->B2B）", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "物流状态（ERP->B2B）", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdExpressStatusSaveReqVO updateReqVO) {
            trdExpressStatusService.updateTrdExpressStatus(updateReqVO);
        return success(true);
    }

    /**
     * 删除物流状态（ERP->B2B）
     */
    @ApiOperation(value = "删除物流状态（ERP->B2B）", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "物流状态（ERP->B2B）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public CommonResult<Boolean> remove(@PathVariable Long[] ids) {
        trdExpressStatusService.deleteTrdExpressStatusByIds(ids);
        return success(true);
    }

    /**
     * 获取物流状态（ERP->B2B）详细信息
     */
    @ApiOperation(value = "获得物流状态（ERP->B2B）详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{id}")
    public CommonResult<TrdExpressStatusRespVO> getInfo(@PathVariable("id") Long id) {
        TrdExpressStatus trdExpressStatus = trdExpressStatusService.getTrdExpressStatus(id);
        return success(TrdExpressStatusConvert.INSTANCE.convert(trdExpressStatus));
    }

    /**
     * 分页查询物流状态（ERP->B2B）
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得物流状态（ERP->B2B）分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdExpressStatusRespVO>> getPage(@Valid TrdExpressStatusPageReqVO pageReqVO) {
        PageResult<TrdExpressStatus> pageResult = trdExpressStatusService.getTrdExpressStatusPage(pageReqVO);
        return success(TrdExpressStatusConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:status:add";
        /** 编辑 */
        public static final String EDIT = "system:status:edit";
        /** 删除 */
        public static final String DELETE = "system:status:remove";
        /** 列表 */
        public static final String LIST = "system:status:list";
        /** 查询 */
        public static final String GET = "system:status:query";
        /** 停用 */
        public static final String DISABLE = "system:status:disable";
        /** 启用 */
        public static final String ENABLE = "system:status:enable";
    }
}
