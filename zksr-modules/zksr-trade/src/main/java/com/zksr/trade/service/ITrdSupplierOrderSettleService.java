package com.zksr.trade.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.trade.domain.TrdSupplierOrderSettle;
import com.zksr.trade.controller.orderSettle.vo.TrdSupplierOrderSettlePageReqVO;
import com.zksr.trade.controller.orderSettle.vo.TrdSupplierOrderSettleSaveReqVO;

import java.math.BigDecimal;

/**
 * 入驻商订单结算信息Service接口
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
public interface ITrdSupplierOrderSettleService {

    /**
     * 新增入驻商订单结算信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertTrdSupplierOrderSettle(@Valid TrdSupplierOrderSettleSaveReqVO createReqVO);

    /**
     * 修改入驻商订单结算信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateTrdSupplierOrderSettle(@Valid TrdSupplierOrderSettleSaveReqVO updateReqVO);

    /**
     * 删除入驻商订单结算信息
     *
     * @param supplierOrderDtlId 入驻商订单明细id
     */
    public void deleteTrdSupplierOrderSettle(Long supplierOrderDtlId);

    /**
     * 批量删除入驻商订单结算信息
     *
     * @param supplierOrderDtlIds 需要删除的入驻商订单结算信息主键集合
     * @return 结果
     */
    public void deleteTrdSupplierOrderSettleBySupplierOrderDtlIds(Long[] supplierOrderDtlIds);

    /**
     * 获得入驻商订单结算信息
     *
     * @param supplierOrderDtlId 入驻商订单明细id
     * @return 入驻商订单结算信息
     */
    public TrdSupplierOrderSettle getTrdSupplierOrderSettle(Long supplierOrderDtlId);

    /**
     * 获得入驻商订单结算信息分页
     *
     * @param pageReqVO 分页查询
     * @return 入驻商订单结算信息分页
     */
    PageResult<TrdSupplierOrderSettle> getTrdSupplierOrderSettlePage(TrdSupplierOrderSettlePageReqVO pageReqVO);

    /**
     * 根据业务员ID获取业务员APP首页的提成金额（当天）
     *
     * @param colonelId
     * @return
     */
    BigDecimal getColonelAppPercentageAmt(Long colonelId);

}
