package com.zksr.trade.service.impl.handler;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.balance.BalanceApi;
import com.zksr.account.api.balance.dto.MemBranchBalanceDTO;
import com.zksr.account.api.pay.PayApi;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.redis.service.RedisService;
import com.zksr.member.api.colonelApp.dto.PageDataReqDTO;
import com.zksr.trade.api.after.dto.OrderAfterDtlResDTO;
import com.zksr.trade.api.after.vo.PayRefundVO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSaveReqVO;
import com.zksr.trade.convert.hdfk.TrdHdfkSettleConvert;
import com.zksr.trade.domain.*;
import com.zksr.trade.mapper.*;
import com.zksr.trade.mq.TradeMqProducer;
import com.zksr.trade.service.ITrdHdfkSettleService;
import com.zksr.trade.service.ITrdSettleService;
import com.zksr.trade.service.TrdCacheService;
import com.zksr.trade.service.handler.ITrdAfterHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.OpenApiConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;
import static com.zksr.member.constant.MemberConstant.COLONEL_APP_PAGE_DATA_TYPE_5;
import static com.zksr.member.constant.MemberConstant.COLONEL_APP_Page_DATA_TYPE_4;
import static com.zksr.trade.enums.ErrorCodeConstants.TRD_HDFK_PAY_NOT_FOUND;

/**
 * <AUTHOR>
 * @date 2024年05月10日 14:38
 * @description: TrdAfterHdfkHandlerServiceImpl
 */
@Service
@Slf4j
@Order(ITrdAfterHandlerService.ORDER_AFTER_HDFK)
public class TrdAfterHdfkHandlerServiceImpl implements ITrdAfterHandlerService {

    @Autowired
    private TrdSupplierAfterDtlMapper trdSupplierAfterDtlMapper;
    @Autowired
    private TrdAfterLogMapper afterLogMapper;
    @Autowired
    private ITrdSettleService trdSettleService;
    @Autowired
    private TradeMqProducer tradeMqProducer;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;
    @Autowired
    private ITrdHdfkSettleService trdHdfkSettleService;
    @Autowired
    private TrdOrderMapper trdOrderMapper;
    @Autowired
    private TrdHdfkSettleMapper trdHdfkSettleMapper;
    @Autowired
    private TrdSettleMapper trdSettleMapper;
    @Autowired
    private AccountApi accountApi;
    @Autowired
    private PlatformMerchantApi platformMerchantApi;
    @Autowired
    private TrdSupplierAfterSettleMapper trdSupplierAfterSettleMapper;
    @Autowired
    private TrdSupplierOrderSettleMapper trdSupplierOrderSettleMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private PayApi payApi;
    @Autowired
    private TrdCacheService trdCacheService;

    @Autowired
    private TrdSupplierAfterMapper trdSupplierAfterMapper;

    @Autowired
    private BalanceApi balanceApi;

    /**
     * 货到付款单子无退货申请，这里处理结算信息
     *
     * @param after
     * @param afterDtls
     */
    @Override
    public void createPayRefundOrder(String orderNo, String trdSupplierNo, TrdAfter after, List<TrdSupplierAfterDtl> afterDtls) {
        // 当前订单为货到付款的单子才进入
        if (!OrderPayWayEnum.HDFK.getPayWay().equals(after.getPayWay())) {
            return;
        }
        Map<Integer, List<TrdSupplierAfterDtl>> payStateAfterDtlMap = afterDtls.stream().collect(Collectors.groupingBy(TrdSupplierAfterDtl::getPayState));

        // 根据售后订单明细ID 查询 售后订单明细结算流水，用于生成门店余额退款
        List<TrdSupplierAfterSettle> afterSettles = trdSupplierAfterSettleMapper.selectSettleByAfterDtlId(
                afterDtls.stream()
                        .map(TrdSupplierAfterDtl::getSupplierAfterDtlId)
                        .collect(Collectors.toList())
        );
        Map<Long, TrdSupplierAfterSettle> supplierAfterSettleMap = convertMap(afterSettles, TrdSupplierAfterSettle::getSupplierAfterDtlId);
        BigDecimal refundBalanceAmt = BigDecimal.ZERO;
        for (Map.Entry<Integer, List<TrdSupplierAfterDtl>> entry : payStateAfterDtlMap.entrySet()) {
            Integer payState = entry.getKey();
            List<TrdSupplierAfterDtl> afterDtlList = entry.getValue();
            if (Objects.equals(payState, PayStateEnum.PAY_NOT_HDFK.getCode())) {
                TrdSupplierOrderDtl supplierOrderDtl = trdSupplierOrderDtlMapper.selectById(afterDtlList.get(0).getSupplierOrderDtlId());
                // 货到付款订单售后未支付
                orderAfterNotPay(after, afterDtlList, supplierOrderDtl);
            } else if (Objects.equals(payState, PayStateEnum.PAY_ALREADY_HDFK.getCode())) {
                // 货到付款订单售后已支付
                orderAfterAlreayPay(after, afterDtlList);
            }
            // 计算余额退款金额
            refundBalanceAmt = refundBalanceAmt.add(calculateRefundBalanceAmounts(supplierAfterSettleMap, afterDtlList));
        }
        // 退还门店余额
        refundBranchBalance(after, refundBalanceAmt, orderNo);
    }

    /**
     * 计算余额退款金额
     */
    private BigDecimal calculateRefundBalanceAmounts(Map<Long, TrdSupplierAfterSettle> supplierAfterSettleMap, List<TrdSupplierAfterDtl> supplierAfterDtlList) {
        BigDecimal refundBalanceAmt = BigDecimal.ZERO;
        for (TrdSupplierAfterDtl afterDtl : supplierAfterDtlList) {
            TrdSupplierAfterSettle afterSettle = supplierAfterSettleMap.get(afterDtl.getSupplierAfterDtlId());
            refundBalanceAmt = refundBalanceAmt.add(afterSettle.getRefundBalanceAmt());
        }
        return refundBalanceAmt;
    }

    /**
     * 退还门店余额
     */
    private void refundBranchBalance(TrdAfter after, BigDecimal refundBalanceAmt, String orderNo) {
        if (Objects.isNull(refundBalanceAmt) || refundBalanceAmt.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        // 组建余额支付订单取消参数
        MemBranchBalanceDTO balanceDTO = new MemBranchBalanceDTO();
        balanceDTO.setBranchId(after.getBranchId());
        balanceDTO.setSysCode(after.getSysCode());
        balanceDTO.setTradeNo(orderNo);
        balanceDTO.setOpAmt(refundBalanceAmt);
        balanceDTO.setOperUserName(SecurityContextHolder.getUserName());
        CommonResult<String> operResult = balanceApi.OrderPayCancel(balanceDTO);
        log.info("===================取消订单：{}，回退支付金额流水记录：{}", orderNo, JSON.toJSONString(operResult));
        if (operResult.isError()) {
            throw new ServiceException("门店余额支付退款失败");
        }
    }

    // 订单售后未支付
    private void orderAfterNotPay(TrdAfter after, List<TrdSupplierAfterDtl> afterDtls, TrdSupplierOrderDtl supplierOrderDtl) {
        // 更新状态为已完成
        afterDtls.forEach(afterDtl -> {
            refundSuccess(afterDtl);
        });

        //统计退货金额
        BigDecimal returnAmt = afterDtls.stream().map(TrdSupplierAfterDtl::getReturnAmt).reduce(BigDecimal.ZERO, BigDecimal::add);

        /**
         * 2、写入订单结算数据
         */
        List<TrdSettle> settles = trdSettleService.createBatchAfterSettle(afterDtls);
        trdSettleMapper.insertBatch(settles);
        // 更新结算状态
        trdSettleService.updateSettle(afterDtls);

        /**
         * 3、取消冻结入驻商分成利润金额流水 TODO 应入驻商充值方案弃用，故此代码注释
         */
//        trdSettleService.cancelSupplierFreezeFlow(settles, afterDtls.get(0));

        //发送业务员APP首页信息 客户下单订单MQ
        //查询售后订单信息
        if (ToolUtil.isNotEmpty(after) && ToolUtil.isNotEmpty(after.getColonelId())) {
            // 当前业务员提成售后金额
            BigDecimal returnColonel2Amt =
                    settles.stream().filter(settle -> settle.getMerchantType().equalsIgnoreCase(MerchantTypeEnum.COLONEL.getType()) && Objects.equals(settle.getMerchantId(), after.getColonelId()))
                            .map(TrdSettle::getSettleAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 当前业务员的上级业务员提成售后金额
            BigDecimal returnColonel1Amt =
                    settles.stream().filter(settle -> settle.getMerchantType().equalsIgnoreCase(MerchantTypeEnum.COLONEL.getType()) && Objects.equals(settle.getMerchantId(), after.getPcolonelId()))
                            .map(TrdSettle::getSettleAmt).reduce(BigDecimal.ZERO, BigDecimal::add);

            tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(after.getColonelId(), after.getSysCode(), after.getBranchId(), returnAmt, returnColonel2Amt, COLONEL_APP_Page_DATA_TYPE_4));
            if (NumberUtil.isGreater(returnColonel1Amt, BigDecimal.ZERO)) {
                tradeMqProducer.sendColoenlAppPageDataEvent(new PageDataReqDTO(after.getPcolonelId(), after.getSysCode(), after.getBranchId(), returnAmt, returnColonel1Amt, COLONEL_APP_PAGE_DATA_TYPE_5));
            }
        }

        /**
         * 这里需要区分货到付款售后订单 所对应的原订单是否已经付款
         * 已付款的货到付款售后流程需要进行退款（根据货到付款结算流水中的付款单号进行退款），不需要写货到付款售后流水
         * 未付款的货到付款售后流程不需要进行退款，需要写货到付款售后流水且金额是负数
         */

        TrdOrder trdOrder = trdOrderMapper.selectById(after.getOrderId());
        // 货到付款未收款

        // 新增货到付款订单售后结算信息
        List<TrdHdfkSettleSaveReqVO> createReqVO = afterDtls.stream().map(afterDtl -> {
            TrdHdfkSettleSaveReqVO trdHdfkSettleVo = TrdHdfkSettleConvert.INSTANCE.convertHdfkSettleVO(afterDtl, trdOrder, supplierOrderDtl);
            trdHdfkSettleVo.setSettleType(NumberPool.INT_ONE);
            trdHdfkSettleVo.setSettleAmt(afterDtl.getReturnAmt().negate()); // 售后写入金额字段为负数
            return trdHdfkSettleVo;
        }).collect(Collectors.toList());
        trdHdfkSettleService.insertTrdHdfkSettle(createReqVO);


        /**
         * 4、B2B发送  货到付款未收款的售后订单信息 推送ERP （货到付款未付款的差异和拒收不推）
         */
        //获取入驻商售后订单信息 校验
        TrdSupplierAfter trdSupplierAfter = trdSupplierAfterMapper.selectByAfterIdAndSupplierId(after.getAfterId(), afterDtls.get(NumberPool.INT_ZERO).getSupplierId());

        if ((Objects.equals(trdSupplierAfter.getTransNo(), SheetTypeConstants.SHS) || Objects.equals(trdSupplierAfter.getTransNo(), "SHS")) &&
                (ORDER_SYNC_FLAG_1.equals(trdSupplierAfter.getPushStatus()) || ORDER_SYNC_FLAG_2.equals(trdSupplierAfter.getPushStatus()))) {

            tradeMqProducer.sendSyncDataReceiptEvent(
                    new SyncReceiptSendDTO()
                            .setSheetType(SheetTypeConstants.SHS)
                            .setSupplierSheetNo(trdSupplierAfter.getSupplierAfterNo())
                            .setIsProceeds(OPEN_FLAG_0)
//                        .setSupplierDtlIdList(afterDtl.stream().map(TrdSupplierAfterDtl::getSupplierAfterDtlId).collect(Collectors.toList()))
            );
        }
    }

    // 订单售后已支付
    private void orderAfterAlreayPay(TrdAfter after, List<TrdSupplierAfterDtl> afterDtls) {

        // 这里做退款操作
        List<Long> supplierOrderDtlIdList = afterDtls.stream().map(TrdSupplierAfterDtl::getSupplierOrderDtlId).collect(Collectors.toList());
        List<TrdHdfkSettle> settleList = trdHdfkSettleMapper.selectListByOrderDtlId(supplierOrderDtlIdList);

        Map<Long, List<TrdHdfkSettle>> settleMap = settleList.stream().collect(Collectors.groupingBy(TrdHdfkSettle::getSupplierOrderDtlId));
        Map<String, List<TrdSupplierAfterDtl>> afterDtlMap = new HashMap<>();
        afterDtls.stream().filter(afterDtl -> settleMap.containsKey(afterDtl.getSupplierOrderDtlId()))
                .forEach(afterDtl -> {
                    TrdHdfkSettle hdfkSettle = settleMap.get(afterDtl.getSupplierOrderDtlId()).get(0);
                    List<TrdSupplierAfterDtl> afterDtlList = new ArrayList<>();
                    if (afterDtlMap.containsKey(hdfkSettle.getHdfkPayNo())) {
                        afterDtlList = afterDtlMap.get(hdfkSettle.getHdfkPayNo());
                    }
                    afterDtlList.add(afterDtl);
                    afterDtlMap.put(hdfkSettle.getHdfkPayNo(), afterDtlList);
                });

        // 一个货到付款售后订单可能会存在多个货到付款支付单号，退款需要根据付款支付单号退。
        afterDtlMap.forEach((key, value) -> {
            hdfkSubmitPayOrder(after, value, key);
        });

    }


    /**
     * 货到付款订单退款完成
     *
     * @param afterDtl
     */
    private void refundSuccess(TrdSupplierAfterDtl afterDtl) {
        /**
         * 1、更新售后订单商品状态为退款完成 {@link com.zksr.common.core.enums.RefundStateEnum}
         */
        afterDtl.setRefundState(RefundStateEnum.REFUND_TKWC.getState());
        afterDtl.setRefundTime(DateUtils.getNowDate());
        trdSupplierAfterDtlMapper.updateById(afterDtl);

        // 添加订单操作日志
        saveOrderDtlLog(afterDtl, AfterHandleStateEnum.TKZ.getCode(), AfterHandleStateEnum.TKWC.getCode(), StatusConstants.OPER_TYPE_UPDATE, AfterHandleStateEnum.TKWC.getContent());
    }

    /**
     * 新增订单明细操作日志
     *
     * @param dtl         订单明细
     * @param beforeState 更新前状态
     * @param afterState  更新后状态
     * @param operType    操作类型
     * @param content     信息
     */
    private void saveOrderDtlLog(TrdSupplierAfterDtl dtl, Long beforeState, Long afterState, Long operType, String content) {
        TrdAfterLog log = new TrdAfterLog();
        log.setSupplierAfterDtlId(dtl.getSupplierAfterDtlId());
        log.setSupplierAfterDtlNo(dtl.getSupplierAfterDtlNo());
        log.setBeforeState(beforeState);
        log.setAfterState(afterState);
        log.setOperateType(operType);
        log.setContent(content);
        log.setSysCode(dtl.getSysCode());
        afterLogMapper.insert(log);
    }

    @Autowired
    private TrdHdfkPayDtlMapper trdHdfkPayDtlMapper;

    @Autowired
    private TrdHdfkPayMapper trdHdfkPayMapper;

    private void hdfkSubmitPayOrder(TrdAfter after, List<TrdSupplierAfterDtl> afterDtls, String orderNo) {
        // 根据售后单的订单 ID 或其他字段找到原始的 hdfk_pay 付款单记录
        TrdHdfkPay hdfkPay = trdHdfkPayMapper.selectByPayNo(orderNo);
        //如果是线下支付
        if ("6".equals(hdfkPay.getPayWay())) {
            Long hdfkPayId = hdfkPay.getHdfkPayId();
            if (ToolUtil.isEmpty(hdfkPay)) {
                throw exception(TRD_HDFK_PAY_NOT_FOUND);  // 如果找不到货到付款付款单，抛出异常
            }
            //创建货到付款结算
            List<TrdHdfkSettleSaveReqVO> createReqVO = afterDtls.stream().map(afterDtl -> {
                TrdOrder trdOrder = trdOrderMapper.selectById(after.getOrderId());
                TrdSupplierOrderDtl trdSupplierOrderDtl = trdSupplierOrderDtlMapper.selectById(afterDtl.getSupplierOrderDtlId());
                TrdHdfkSettleSaveReqVO trdHdfkSettleVo = TrdHdfkSettleConvert.INSTANCE.convertHdfkSettleVO(afterDtl, trdOrder, trdSupplierOrderDtl);
                trdHdfkSettleVo.setState(NumberPool.INT_ONE);
                trdHdfkSettleVo.setSettleType(NumberPool.INT_ONE);
                trdHdfkSettleVo.setHdfkPayNo(hdfkPay.getHdfkPayNo());
                trdHdfkSettleVo.setSettleTime(new Date());
                trdHdfkSettleVo.setSettleAmt(afterDtl.getReturnAmt().negate());
                return trdHdfkSettleVo;
            }).collect(Collectors.toList());
            List<TrdHdfkSettle> hdfkSettlesList = new ArrayList<>();
            for (TrdHdfkSettleSaveReqVO trdHdfkSettleSaveReqVO : createReqVO) {
                hdfkSettlesList.add(TrdHdfkSettleConvert.INSTANCE.convert(trdHdfkSettleSaveReqVO));
            }
            trdHdfkSettleMapper.insertBatch(hdfkSettlesList);


            //创建货到付款付款单明细
            List<TrdHdfkPayDtl> trdHdfkPayDtls = hdfkSettlesList.stream().map(item ->
                    TrdHdfkPayDtl.builder()
                            .hdfkPayId(hdfkPayId)
                            .hdfkSettleId(item.getHdfkSettleId())
                            .settleAmt(item.getSettleAmt())
                            .build()
            ).collect(Collectors.toList());
            trdHdfkPayDtlMapper.insertBatch(trdHdfkPayDtls);

            // 创建映射关系，将 HdfkSettleId 与 HdfkPayDtlId 进行对应存储在 Map 中
            Map<Long, Long> settleIdToPayDtlIdMap = trdHdfkPayDtls.stream()
                    .collect(Collectors.toMap(TrdHdfkPayDtl::getHdfkSettleId, TrdHdfkPayDtl::getHdfkPayDtlId));

            // 遍历 hdfkSettlesList 并根据映射关系更新 FdfkPayDtlId
            for (TrdHdfkSettle trdHdfkSettle : hdfkSettlesList) {
                Long payDtlId = settleIdToPayDtlIdMap.get(trdHdfkSettle.getHdfkSettleId());
                if (payDtlId != null) {
                    trdHdfkSettle.setFdfkPayDtlId(payDtlId);
                    trdHdfkSettleMapper.updateById(trdHdfkSettle);
                }
            }

            // 计算新的付款总金额和手续费（负值表示售后减少金额）
            BigDecimal updatedPayAmt = hdfkPay.getPayAmt().add(
                    trdHdfkPayDtls.stream().map(TrdHdfkPayDtl::getSettleAmt).reduce(BigDecimal.ZERO, BigDecimal::add)
            );

            // 更新付款单的金额和状态
            hdfkPay.setPayAmt(updatedPayAmt);  // 更新付款金额，累加负值
            hdfkPay.setPayState(NumberPool.INT_TWO);
            // 更新付款单
            trdHdfkPayMapper.updateById(hdfkPay);
            //生成一个退款单号
            String refundNo = SheetTypeConstants.HDFKTK + redisService.getUniqueNumber();
            for (TrdSupplierAfterDtl afterDtl : afterDtls) {
                afterDtl.setRefundNo(refundNo);
            }
            //更新明细退款单号
            trdSupplierAfterDtlMapper.updateBatch(afterDtls);
            //售后订单退款成功回调
            tradeMqProducer.sendRefundNotification(new PayRefundVO().setRefundNo(refundNo).setStatus(PayRefundStatusEnum.SUCCESS.getStatus()));
        } else {
            PayRefundOrderSubmitReqVO refundReqVo = new PayRefundOrderSubmitReqVO();
            refundReqVo.setBusiId(after.getAfterId())
                    .setOrderNo(orderNo)
                    .setRefundAmt(BigDecimal.ZERO)
                    .setSysCode(after.getSysCode())
                    .setOrderType(OrderTypeConstants.MALL)
                    .setRefundNo(SheetTypeConstants.TK + redisService.getUniqueNumber())
                    .setSettlements(new ArrayList<>());


            // 创建售后订单明细生成流水， 目前 用于B2B微信支付退款
            List<TrdSettle> afterSettleList = trdSettleService.createBatchAfterSettle(afterDtls);

            Map<Long, List<TrdSupplierAfterDtl>> supplierAfterDtlMap = afterDtls.stream().collect(Collectors.groupingBy(TrdSupplierAfterDtl::getSupplierId));
            supplierAfterDtlMap.forEach((key, value) -> {
                String platform = value.get(0).getPlatform(); // 订单支付平台
                Long supplierId = value.get(0).getSupplierId(); // 入驻商ID
                // 获取当前入驻商的第三方账户信息，用于退款
                PlatformMerchantDTO merchantDTO = platformMerchantApi.getPlatformMerchant(MerchantTypeEnum.SUPPLIER.getType(), key, platform).getCheckedData();
//                AccAccountDTO account = accountApi.getSupplierAccount(key).getCheckedData();
                if (PayChannelEnum.getPayOnlineSupportDivide(platform)
                        && (ToolUtil.isEmpty(merchantDTO) || ToolUtil.isEmpty(merchantDTO.getAltMchNo()))) {
                    throw new ServiceException("入驻商[" + key + "]无第三方账户信息！");
                }

                OrderSettlementDTO orderSettlementDTO = new OrderSettlementDTO();
                // 分账信息  获取当前支付方式 属性【是否支持在线分账】值
                if (PayChannelEnum.getPayOnlineSupportDivide(platform)) {
                    orderSettlementDTO.setAccountNo(merchantDTO.getAltMchNo());
                } else {
                    orderSettlementDTO.setAccountNo(supplierId + "");
                }
                orderSettlementDTO.setAmt(BigDecimal.ZERO);
                orderSettlementDTO.setMerchantId(supplierId);
                orderSettlementDTO.setMerchantType(MerchantTypeEnum.SUPPLIER.getType());
                // 计算退款金额和手续费
                refundAmtFeeCalculate(value, orderSettlementDTO, refundReqVo);
                // 兼容合并订单结算分账信息，用于B2B微信支付, 过滤掉结算为0 的数据
                if (PayChannelEnum.isB2b(after.getPlatform()) || PayChannelEnum.isHlb(after.getPlatform())) {
                    createMerchantSettle(afterSettleList, key, platform, orderSettlementDTO, refundReqVo);
                }
                refundReqVo.getSettlements().add(orderSettlementDTO);
            });
            log.info("发起货到付款退款请求,req={}", JSON.toJSONString(refundReqVo));
            // 发起退款接口
            payApi.submitPayOrder(refundReqVo).getCheckedData();
        }
    }

    // 计算退款金额和手续费
    private void refundAmtFeeCalculate(List<TrdSupplierAfterDtl> afterDtls, OrderSettlementDTO orderSettlementDTO, PayRefundOrderSubmitReqVO refundReqVo) {
        // 根据售后订单明细ID 查询 售后订单明细结算流水，用于生成退款申请单
        List<Long> afterIds = afterDtls.stream().map(TrdSupplierAfterDtl::getSupplierAfterDtlId).collect(Collectors.toList());
        List<TrdSupplierAfterSettle> afterSettles = trdSupplierAfterSettleMapper.selectSettleByAfterDtlId(afterIds);
        Map<Long, TrdSupplierAfterSettle> supplierAfterSettleMap = convertMap(afterSettles, TrdSupplierAfterSettle::getSupplierAfterDtlId);

        List<Long> orderDtls = afterDtls.stream().map(TrdSupplierAfterDtl::getSupplierOrderDtlId).collect(Collectors.toList());
        // 订单明细数据
        List<TrdSupplierOrderDtl> orderDtlList = trdSupplierOrderDtlMapper.selectListByDtlIdList(orderDtls);
        Map<Long, TrdSupplierOrderDtl> orderDtlMap = convertMap(orderDtlList, TrdSupplierOrderDtl::getSupplierOrderDtlId);

        // 查询订单明细结算信息
        List<TrdSupplierOrderSettle> orderSettleList = trdSupplierOrderSettleMapper.selectSettleByOrderDtlIds(orderDtls);
        Map<Long, TrdSupplierOrderSettle> supplierOrderSettleMap = convertMap(orderSettleList, TrdSupplierOrderSettle::getSupplierOrderDtlId);

        // 订单明细已售后总数量
        List<OrderAfterDtlResDTO> orderAfterDtlResDTOS = trdSupplierAfterDtlMapper.getAfterDtlNumBySupplierDtlId(orderDtls);
        Map<Long, OrderAfterDtlResDTO> orderAfterDtlResDTOMap = convertMap(orderAfterDtlResDTOS, OrderAfterDtlResDTO::getSupplierOrderDtlId);


        afterDtls.stream()
                .filter(afterDtl -> afterDtl.getGiftFlag().equals(NumberPool.INT_ZERO)) // 非赠品才参与计算手续费
                .forEach(afterDtl -> {
                    // 订单明细信息
                    TrdSupplierOrderDtl orderDtl = orderDtlMap.get(afterDtl.getSupplierOrderDtlId());
                    // 订单明细结算信息
                    TrdSupplierOrderSettle orderSettle = supplierOrderSettleMap.get(afterDtl.getSupplierOrderDtlId());
                    // 订单明细已售后数量
                    OrderAfterDtlResDTO orderAfterDtlDTO = orderAfterDtlResDTOMap.get(afterDtl.getSupplierOrderDtlId());
                    // 售后订单明细结算信息
                    TrdSupplierAfterSettle afterSettle = supplierAfterSettleMap.get(afterDtl.getSupplierAfterDtlId());

                    // 订单明细货到付款支付平台 售后手续费金额
                    BigDecimal refundPayFee = afterSettle.getRefundAmt().multiply(orderSettle.getPayRate()).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                    if (orderAfterDtlDTO.getSumReturnNum().compareTo(orderDtl.getTotalNum()) >= 0) {
                        refundPayFee = orderSettle.getPayFee().subtract(orderSettle.getRefundPayFee());

                    }

                    // 更新货到付款订单明细结算 已退款金额和手续费 （已迁移到退款成功回调中处理）
//            orderSettle.setRefundPayAmt(orderSettle.getRefundPayAmt().add(afterSettle.getRefundAmt()));
//            orderSettle.setRefundPayFee(orderSettle.getRefundPayFee().add(refundPayFee));
//            trdSupplierOrderSettleMapper.updateById(orderSettle);

                    // 更新售后订单明细结算信息
                    afterSettle.setPayRate(orderSettle.getPayRate()); // 订单明细货到付款支付平台手续费率
                    afterSettle.setRefundFee(refundPayFee); // 本次售后手续费
                    afterSettle.setSupplierRefundDivideAmt(afterSettle.getRefundAmt().subtract(afterSettle.getRefundFee())); // 退款分账金额
                    trdSupplierAfterSettleMapper.updateById(afterSettle);

                    // 回写退款单号到售后订单明细中
                    afterDtl.setRefundNo(refundReqVo.getRefundNo());
                    trdSupplierAfterDtlMapper.updateById(afterDtl);

                    orderSettlementDTO.setAmt(orderSettlementDTO.getAmt().add(afterSettle.getSupplierRefundDivideAmt()));
                    refundReqVo.setRefundAmt(afterSettle.getRefundAmt().add(refundReqVo.getRefundAmt()));

                });
    }

    // B2B 微信支付合并订单结算分账信息
    private void createMerchantSettle(List<TrdSettle> afterSettleList, Long supplierId, String platform, OrderSettlementDTO orderSettlementDTO, PayRefundOrderSubmitReqVO refundReqVo) {
        afterSettleList.stream()
                .filter(settle -> Objects.equals(supplierId, settle.getSupplierId()))
                .collect(Collectors.groupingBy(settle -> StringUtils.format("{}_{}", settle.getMerchantId(), settle.getMerchantType())))
                .forEach((merchantKey, settleList) -> {
                    Long merchantId = Long.valueOf(merchantKey.split("_")[NumberPool.INT_ZERO]);
                    String merchantType = merchantKey.split("_")[NumberPool.INT_ONE];

                    // 获取当前结算的账户信息
                    PlatformMerchantDTO merchantDTO = platformMerchantApi.getPlatformMerchant(merchantType, merchantId, platform).getCheckedData();
//                    AccAccountDTO accountDTO = trdCacheService.getAccount(merchantId, merchantType, platform);
                    String accountNo = merchantId + "";
                    // 分账信息  获取当前支付方式 属性【是否支持在线分账】值
                    if (ToolUtil.isNotEmpty(merchantDTO) && PayChannelEnum.getPayOnlineSupportDivide(platform)) {
                        accountNo = merchantDTO.getAltMchNo();
                    }

                    OrderSettlementDTO orderMerchantSettlement = OrderSettlementDTO.builder()
                            .accountNo(accountNo)
                            .merchantId(merchantId)
                            .merchantType(merchantType)
                            .build();

                    // 因结算信息表中售后订单填写的金额为负数，所以需要取反
                    BigDecimal settleAmt = settleList.stream().map(TrdSettle::getSettleAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).multiply(new BigDecimal("-1"));
                    orderMerchantSettlement.setAmt(settleAmt);
                    if (settleAmt.compareTo(BigDecimal.ZERO) > NumberPool.INT_ZERO) {
                        refundReqVo.getSettlements().add(orderMerchantSettlement);
                        // 入驻商分账金额 - 分账方金额
                        orderSettlementDTO.setAmt(orderSettlementDTO.getAmt().subtract(settleAmt));
                    }
                });
    }
}
