package com.zksr.trade.service.handler;

import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.controller.after.vo.OrderAfterSaveVO;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.domain.*;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月21日 17:56
 * @description: 售后订单活动特殊逻辑处理器 handler 接口
 * * 提供订单生命周期钩子接口；保存订单前
 */
@Component
public interface ITrdAfterHandlerService {


    int ORDER_AFTER = 1;
    int ORDER_AFTER_HDFK = 2;

    /**
     * 订单创建前
     *
     * @param afterSaveVO 订单
     */
    default void beforeAfterCreate(OrderAfterSaveVO afterSaveVO, TrdOrder trdOrder, List<TrdSupplierOrder> tsoList) {
    }

    /**
     * 创建订单后(发货前售后)
     */
    default void afterOrderAfterCreate(TrdSupplierAfterDtl afterDtl, TrdOrder trdOrder, TrdSupplierOrderDtl tsod) {
    }

    /**
     * 创建订单后(发货前售后返还商品库存及促销库存操作)
     */
    default void afterOrderAfterReturnActivity(TrdOrder tor, List<TrdSupplierOrder> tsoList, List<TrdSupplierOrderDtl> tsodList, List<TrdOrderDiscountDtl> toddList,
                                               TrdAfter after, List<TrdSupplierAfterDtl> afterDtlList, List<TrdAfterDiscountDtl> afterDiscountDtlList) {
    }

    /**
     * 撤销售后订单
     */
    default void revokeAfterReturn(TrdOrder tor, List<TrdSupplierOrder> tsoList, TrdAfter after, List<TrdSupplierAfter> tsaList, List<TrdSupplierAfterDtl> dtlList) {
    }

    /**
     * 取消售后订单
     */
    default void cancelAfterReturn(TrdAfter after, List<TrdSupplierAfterDtl> dtlList) {
    }

    /**
     * 售后订单审核
     */
    default void approveAfterReturnOrderDtl(TrdSupplierAfterDtl afterDtl) {
    }

    /**
     * 入驻商售后订单审核
     */
    default void approveSupplierAfter(TrdSupplierAfter supplierAfter) {
    }

    /**
     * 售后订单更改状态为 退货中 {@link com.zksr.common.core.enums.ReturnStateEnum}
     */
    default void submitLogisticsAfterOrderDtl(TrdSupplierAfterDtl afterDtl) {
    }

    /**
     * 更改状态为退款中 （售后订单审核同意退款操作 或 售后订单同意退款重新发起操作）
     */
    default void approveAfterRefundOrderDtl(TrdSupplierAfterDtl afterDtl, TrdAfter trdAfter) {
    }

    /**
     * 退款成功
     */
    default void payRefundSuccess(List<TrdSupplierAfterDtl> afterDtl) {
    }

    /**
     * 退款失败
     */
    default void payRefundFail(TrdSupplierAfterDtl afterDtl) {
    }

    /**
     * 发起退货申请
     */
    default void createPayRefundOrder(String orderNo, String trdSupplierNo, TrdAfter after, List<TrdSupplierAfterDtl> afterDtls) {
    }

}
