package com.zksr.trade.controller.orderSupplier;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.service.ITrdSupplierOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderPageReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderSaveReqVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderRespVO;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 入驻商订单Controller
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Api(tags = "管理后台 - 入驻商订单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/orderSupplier")
public class TrdSupplierOrderController {
    @Autowired
    private ITrdSupplierOrderService trdSupplierOrderService;

    /**
     * 新增入驻商订单
     */
    @ApiOperation(value = "新增入驻商订单", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "入驻商订单", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody TrdSupplierOrderSaveReqVO createReqVO) {
        return success(trdSupplierOrderService.insertTrdSupplierOrder(createReqVO));
    }

    /**
     * 修改入驻商订单
     */
    @ApiOperation(value = "修改入驻商订单", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "入驻商订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody TrdSupplierOrderSaveReqVO updateReqVO) {
            trdSupplierOrderService.updateTrdSupplierOrder(updateReqVO);
        return success(true);
    }

    /**
     * 删除入驻商订单
     */
    @ApiOperation(value = "删除入驻商订单", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "入驻商订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{supplierOrderIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] supplierOrderIds) {
        trdSupplierOrderService.deleteTrdSupplierOrderBySupplierOrderIds(supplierOrderIds);
        return success(true);
    }

    /**
     * 获取入驻商订单详细信息
     */
    @ApiOperation(value = "获得入驻商订单详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{supplierOrderId}")
    public CommonResult<TrdSupplierOrderRespVO> getInfo(@PathVariable("supplierOrderId") Long supplierOrderId) {
        TrdSupplierOrder trdSupplierOrder = trdSupplierOrderService.getTrdSupplierOrder(supplierOrderId);
        return success(HutoolBeanUtils.toBean(trdSupplierOrder, TrdSupplierOrderRespVO.class));
    }

    /**
     * 分页查询入驻商订单
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得入驻商订单分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<TrdSupplierOrderRespVO>> getPage(@Valid TrdSupplierOrderPageReqVO pageReqVO) {
        PageResult<TrdSupplierOrder> pageResult = trdSupplierOrderService.getTrdSupplierOrderPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, TrdSupplierOrderRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "trade:order:add";
        /** 编辑 */
        public static final String EDIT = "trade:order:edit";
        /** 删除 */
        public static final String DELETE = "trade:order:remove";
        /** 列表 */
        public static final String LIST = "trade:order:list";
        /** 查询 */
        public static final String GET = "trade:order:query";
        /** 停用 */
        public static final String DISABLE = "trade:order:disable";
        /** 启用 */
        public static final String ENABLE = "trade:order:enable";
    }
}
