package com.zksr.trade.convert.invoice;

import com.zksr.common.core.domain.vo.openapi.receive.SupplierOrderInvoiceOpenDTO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.supplierOrder.TrdSupplierOrderInvoiceSaveReqDTO;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderInvoicePageReqDTO;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderInvoiceRespDTO;
import com.zksr.trade.controller.invoice.vo.TrdSupplierOrderInvoicePageReqVO;
import com.zksr.trade.domain.TrdSupplierOrderInvoice;
import com.zksr.trade.controller.invoice.vo.TrdSupplierOrderInvoiceRespVO;
import com.zksr.trade.controller.invoice.vo.TrdSupplierOrderInvoiceSaveReqVO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
* 用户发票 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2025-07-04
*/
@Mapper
public interface TrdSupplierOrderInvoiceConvert {

    TrdSupplierOrderInvoiceConvert INSTANCE = Mappers.getMapper(TrdSupplierOrderInvoiceConvert.class);

    TrdSupplierOrderInvoiceRespVO convert(TrdSupplierOrderInvoice trdSupplierOrderInvoice);

    TrdSupplierOrderInvoice convert(TrdSupplierOrderInvoiceSaveReqVO trdSupplierOrderInvoiceSaveReq);

    PageResult<TrdSupplierOrderInvoiceRespVO> convertPage(PageResult<TrdSupplierOrderInvoice> trdSupplierOrderInvoicePage);

    PageResult<TrdSupplierOrderInvoiceRespDTO> convert2TrdSupplierOrderInvoiceRespDTO(PageResult<TrdSupplierOrderInvoiceRespVO> resp);

    TrdSupplierOrderInvoiceRespDTO convert2TrdSupplierOrderInvoiceRespDTO(TrdSupplierOrderInvoiceRespVO resp);

    TrdSupplierOrderInvoicePageReqVO convert2TrdSupplierOrderInvoicePageReqVO(TrdSupplierOrderInvoicePageReqDTO req);

    TrdSupplierOrderInvoiceSaveReqVO convert2TrdSupplierOrderInvoiceSaveReqVO(SupplierOrderInvoiceOpenDTO reqDTO);


    @Mappings({
            @Mapping(target = "updateTime", expression = "java(com.zksr.common.core.utils.DateUtils.getNowDate())"),
            @Mapping(target = "id", ignore = true)
    })
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void convert(TrdSupplierOrderInvoiceSaveReqDTO reqDTO, @MappingTarget TrdSupplierOrderInvoice trdSupplierOrderInvoice);
}