package com.zksr.trade.controller.root;

import javax.validation.Valid;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.controller.orderSupplier.TrdSupplierOrderController;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderSaveReqVO;
import com.zksr.trade.service.ITrdOrderService;
import com.zksr.trade.service.ITrdRootService;
import com.zksr.trade.service.ITrdSupplierAfterService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 交易模块超管数据刷新后门
 *
 * <AUTHOR>
 * @date 2025年2月7日
 */
@Api(tags = "管理后台 - 交易模块超管数据刷新后门", produces = "application/json")
@Validated
@RestController
@RequestMapping("/tradeRoot")
public class TradeRootController {

    @Autowired
    private ITrdRootService trdRootService;
    @Autowired
    private ITrdOrderService trdOrderService;
    @Autowired
    private ITrdSupplierAfterService trdSupplierAfterService;

    /**
     * 初始化三个月内常购数据
     */
    @ApiOperation(value = "初始化三个月内常购数据", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ROOT)
    @GetMapping(value = "/refreshStoreProduct")
    @RequiresPermissions(Permissions.ROOT)
    public CommonResult<Boolean> refreshStoreProduct() {
        trdRootService.refreshStoreProduct();
        return success(true);
    }
    
    
    /**
     * 美的付 - O2O分账
     */
    @ApiOperation(value = "美的付 - O2O分账", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ROOT)
    @PostMapping(value = "/mideaPayO2OSettle")
    public CommonResult<Boolean> mideaPayO2OSettle(@RequestBody TrdSupplierOrderSaveReqVO createReqVO) {
        trdOrderService.mideaPayO2OSettle(createReqVO.getSysCode(), createReqVO.getOrderNo());
        return success(true);
    }
    
    /**
     * 入驻商订单生成O2O结算和分账记录
     */
    @ApiOperation(value = "入驻商订单生成O2O结算和分账记录", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + TrdSupplierOrderController.Permissions.ADD)
    @PostMapping(value = "/createO2OSettleAndDivide")
    public CommonResult<Boolean> createO2OSettleAndDivide(@Valid @RequestBody TrdSupplierOrderSaveReqVO createReqVO) {
        trdSupplierAfterService.createO2OSettleAndDivide(createReqVO);
        return success(Boolean.TRUE);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 只允许超级管理员操作 */
        public static final String ROOT = "trd:trade-context:root";
    }
}