package com.zksr.trade.controller.root;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.service.ITrdRootService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 交易模块超管数据刷新后门
 *
 * <AUTHOR>
 * @date 2025年2月7日
 */
@Api(tags = "管理后台 - 交易模块超管数据刷新后门", produces = "application/json")
@Validated
@RestController
@RequestMapping("/tradeRoot")
public class TradeRootController {

    @Autowired
    private ITrdRootService trdRootService;

    /**
     * 初始化三个月内常购数据
     */
    @ApiOperation(value = "初始化三个月内常购数据", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ROOT)
    @GetMapping(value = "/refreshStoreProduct")
    @RequiresPermissions(Permissions.ROOT)
    public CommonResult<Boolean> refreshStoreProduct() {
        trdRootService.refreshStoreProduct();
        return success(true);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 只允许超级管理员操作 */
        public static final String ROOT = "trd:trade-context:root";
    }
}