<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdSupplierOrderDtlMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="updateDeliveryByOrderId" parameterType="com.zksr.trade.controller.order.vo.TrdOrderSupplierOperVo">
        UPDATE
            trd_supplier_order_dtl
        SET
            delivery_state = #{deliveryStatus}
            , update_time = now()
            <if test=" null != deliveryTime">
                , delivery_time = #{deliveryTime}
            </if>
            <if test=" null != receiveTime">
                , receive_time = #{receiveTime}
            </if>
            <if test=" null != completeTime">
                , complete_time = #{completeTime}
            </if>
            <if test="null != deliveryStatus and (deliveryStatus == 4 or deliveryStatus == 41)">
                , send_qty = (total_num - cancel_qty)  <!-- 发货数量 = 商品数量 - 发货前取消数量 -->
                , send_unit = order_unit <!-- 发货单位 = 订单购买单位 -->
                , send_unit_type = order_unit_type <!-- 发货单位大小 = 订单购买单位大小 -->
                , send_unit_size = order_unit_size <!-- 发货单位换算数量 = 订单购买单位换算数量 -->
                , send_unit_qty = (total_num - cancel_qty) / order_unit_size <!-- 发货单位数量 = (商品数量 - 发货前取消数量) / 发货单位换算数量  -->
            </if>
            <if test="null != platform and platform != ''">
                , platform = #{platform}
            </if>
            <if test="null != payState">
                , pay_state = #{payState}
            </if>
        WHERE
            order_id = #{orderId}
            <if test="null == isFilterCancelStatus or isFilterCancelStatus == 1">
                AND delivery_state != 2
            </if>
            <if test=" null != supplierId">
                AND supplier_id = #{supplierId}
            </if>
            <if test="null != itemType">
                AND item_type = #{itemType}
            </if>
            <if test="null != supplierOrderDtlId">
                AND supplier_order_dtl_id = #{supplierOrderDtlId}
            </if>
    </update>


    <select id ="getEntruckingOrderDtlList" parameterType="com.zksr.trade.api.order.vo.TrdOrderTakeDeliveryVO"
    resultType="com.zksr.trade.domain.TrdSupplierOrderDtl">
        SELECT
            orderDtl.*
--             orderDtl.supplier_order_dtl_id
--             ,orderDtl.supplier_order_dtl_no
--             ,orderDtl.delivery_state
--             ,orderDtl.supplier_id
        FROM trd_supplier_order_dtl orderDtl
        LEFT JOIN trd_order tOrder ON orderDtl.order_id = tOrder.order_id
        <where>
            orderDtl.delivery_state = #{deliveryState}
            <if test="null != dcId">
                AND tOrder.dc_id = #{dcId}
            </if>
            <if test="null != sysCode">
                AND orderDtl.sys_code = #{sysCode}
            </if>
            <if test="null != operDate">
                <choose>
                    <when test="null != operDateType and operDateType != '0'.toString()">
                        AND  DATE_ADD(orderDtl.delivery_time, INTERVAL ${operDate} MINUTE) &lt;= #{nowDate}
                    </when>
                    <otherwise>
                        AND  DATE_ADD(orderDtl.delivery_time, INTERVAL ${operDate} HOUR) &lt;= #{nowDate}
                    </otherwise>
                </choose>

            </if>
            <if test="null != itemType">
                AND orderDtl.item_type = #{itemType}
            </if>
        </where>
    </select>

    <select id ="getCompleteOrderDtlList" parameterType="com.zksr.trade.api.order.vo.TrdOrderTakeDeliveryVO"
            resultType="com.zksr.trade.domain.TrdSupplierOrderDtl">
        SELECT
            orderDtl.supplier_order_dtl_id
            ,orderDtl.supplier_order_dtl_no
            ,orderDtl.delivery_state
            ,orderDtl.supplier_id
            ,orderDtl.order_id
            ,orderDtl.sys_code
        FROM trd_supplier_order_dtl orderDtl
        LEFT JOIN trd_order tOrder ON orderDtl.order_id = tOrder.order_id
        <where>
            orderDtl.delivery_state = #{deliveryState}
            AND NOT EXISTS ( SELECT 1 FROM trd_supplier_after_dtl afterDtl WHERE afterDtl.supplier_order_dtl_id = orderDtl.supplier_order_dtl_id AND afterDtl.refund_state != 2 and is_cancel = 0  )
            <if test="null != dcId">
                AND tOrder.dc_id = #{dcId}
            </if>
            <if test="null != sysCode">
                AND orderDtl.sys_code = #{sysCode}
            </if>
            <if test="null != operDate">
                <choose>
                    <when test="null != operDateType and operDateType != '0'.toString()">
                        AND  DATE_ADD(orderDtl.receive_time, INTERVAL ${operDate} MINUTE) &lt;= #{nowDate}
                    </when>
                    <otherwise>
                        AND  DATE_ADD(orderDtl.receive_time, INTERVAL ${operDate} HOUR) &lt;= #{nowDate}
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <!-- 获取供应商下全国订单未发货的单据日期 -->
    <select id="getNotDeliveryNationwideOrderDate" resultType="String">
        SELECT
            DATE_FORMAT(orderDtl.create_time,'%Y-%m-%d') crateTime
        FROM trd_supplier_order_dtl orderDtl
		LEFT JOIN trd_order tOrder ON orderDtl.order_id = tOrder.order_id
		WHERE
		    orderDtl.supplier_id = #{supplierId}
			AND tOrder.pay_state = 1
			AND orderDtl.item_type = 0
			AND (orderDtl.delivery_state = 3 OR orderDtl.delivery_state = 7)
			AND (orderDtl.total_num - orderDtl.cancel_qty) > 0
			GROUP BY DATE_FORMAT(orderDtl.create_time,'%Y-%m-%d')
			ORDER BY DATE_FORMAT(orderDtl.create_time,'%Y-%m-%d') DESC
    </select>

    <!-- 根据入驻商信息获取供应商下全国订单未发货的单据明细 -->
    <select id="getNotDeliveryNationwideOrderDtl"
            parameterType="com.zksr.trade.controller.orderExpressImport.vo.TrdExportOrderDtlVO"
            resultType="com.zksr.trade.controller.orderExpressImport.dto.TrdExportOrderDtlDTO">
        SELECT
            orderDtl.supplier_order_dtl_id
            ,orderDtl.supplier_order_dtl_no
			,orderDtl.sku_id
			,orderDtl.spu_id
			,(orderDtl.total_num - orderDtl.cancel_qty) AS totalNum
			,orderDtl.price
			,orderDtl.supplier_id
			,tOrder.branch_id
			,tOrder.pay_time
			,tOrder.order_id
        FROM trd_supplier_order_dtl orderDtl
		LEFT JOIN trd_order tOrder ON orderDtl.order_id = tOrder.order_id
		WHERE
		    orderDtl.supplier_id = #{supplierId}
		    AND tOrder.pay_state = 1
			AND orderDtl.item_type = 0
            AND (orderDtl.delivery_state = 3 or orderDtl.delivery_state = 7)
			AND (orderDtl.total_num - orderDtl.cancel_qty) > 0
			<if test="null != dates">
                AND DATE_FORMAT(orderDtl.create_time,'%Y-%m-%d')  IN
                <foreach collection="dates" item ="date" open="(" separator="," close=")">
                    #{date}
                </foreach>
            </if>
    </select>
    <select id="selectCouponExtendTotal" resultType="com.zksr.trade.api.order.vo.CouponExtendTotalVO">
        SELECT
            t1.coupon_template_id,
            IFNULL(t2.total_sale_amt, 0) AS total_sale_amt,
            IFNULL(t1.total_coupon_amt, 0) AS total_coupon_amt
        FROM (
             SELECT
                 SUM( todd.coupon_discount_amt + todd.coupon_discount_amt2 ) total_coupon_amt,
                 todd.coupon_template_id
             FROM
                 trd_order_discount_dtl todd
             WHERE
                 todd.coupon_template_id IN
                 <foreach collection="couponTemplateIdList" item ="couponTemplateId" open="(" separator="," close=")">
                     #{couponTemplateId}
                 </foreach>
             GROUP BY
                 todd.coupon_template_id
         ) t1
         LEFT JOIN (
            SELECT
                SUM( tsod.total_amt - tsod.cancel_amt ) total_sale_amt,
                todd.coupon_template_id
            FROM
                trd_order_discount_dtl todd
                LEFT JOIN trd_supplier_order_dtl tsod ON tsod.supplier_order_dtl_id = todd.supplier_order_dtl_id
            WHERE
                todd.coupon_template_id IN
                <foreach collection="couponTemplateIdList" item ="couponTemplateId" open="(" separator="," close=")">
                    #{couponTemplateId}
                </foreach>
            GROUP BY
                todd.coupon_template_id
        ) t2 ON t1.coupon_template_id = t2.coupon_template_id
    </select>

    <!-- 获取该优惠劵模板 和 参与客户的 使用优惠劵订单商品汇总数据 -->
    <select id="getCouponCustomerOrderUseTotal" resultType="com.zksr.trade.api.order.dto.CouponCustomerOrderUseTotalDTO">
        SELECT
            tor.branch_id AS customerId
            ,todd.coupon_template_id
            ,SUM(tsod.order_unit_qty) AS orderNumSum
            ,SUM(tsod.exact_total_amt) AS orderAmtSum
        FROM
            trd_order_discount_dtl todd
            LEFT JOIN trd_supplier_order_dtl tsod ON todd.supplier_order_dtl_id = tsod.supplier_order_dtl_id
            LEFT JOIN trd_order tor ON tor.order_id = tsod.order_id
        WHERE
            todd.coupon_template_id = #{couponTemplateId}
            AND tor.branch_id IN
            <foreach collection="customerIdList" item ="customerId" open="(" separator="," close=")">
                #{customerId}
            </foreach>
        GROUP BY
            tor.branch_id
            ,todd.coupon_template_id
    </select>

    <!-- 获取该优惠劵模板 和 参与客户的 使用优惠劵订单商品汇总数据 -->
    <select id="getCouponCustomerOrderUseDetailTotal"
            resultType="com.zksr.trade.api.order.dto.CouponCustomerOrderUseTotalDTO"
            parameterType="com.zksr.trade.controller.order.vo.TrdOrderPromitionReportPageReqVO">
        SELECT
            tor.branch_id AS customerId
            ,tor.order_no AS orderNo
            ,MAX(tor.create_time) AS orderCreateTime
            ,SUM(tsod.order_unit_qty) orderNumSum
            ,SUM(tsod.total_amt) orderAmtSum
        FROM
            trd_order_discount_dtl todd
            LEFT JOIN trd_supplier_order_dtl tsod ON todd.supplier_order_dtl_id = tsod.supplier_order_dtl_id
            LEFT JOIN trd_order tor ON tor.order_id = tsod.order_id
        WHERE
            todd.coupon_template_id = #{couponTemplateId}
            <if test="null != customerId">
                AND tor.branch_id = #{customerId}
            </if>
            <if test="null != startDate and startDate != '' and null != endDate and endDate != ''">
                AND tor.create_time BETWEEN concat(#{startDate}, ' 00:00:00.000') AND concat(#{endDate}, ' 23:59:59.999')
            </if>
            <if test="null != customerIds">
                AND tor.branch_id IN
                <foreach collection="customerIds" item ="customerId" open="(" separator="," close=")">
                    #{customerId}
                </foreach>
            </if>

        GROUP BY
            tor.branch_id
            ,tor.order_no
    </select>

    <!-- 获取该优惠劵模板对应订单商品数据详情 -->
    <select id="getCouponCustomerOrderUseDetail"
            resultType="com.zksr.trade.api.order.dto.CouponCustomerOrderUseDetilDTO"
            parameterType="com.zksr.trade.controller.order.vo.TrdOrderPromitionReportPageReqVO">
        SELECT
            tso.supplier_id
            ,tso.supplier_name
            ,tsod.sku_id
            ,tsod.spu_id
            ,tsod.memo
            ,tsod.order_unit AS orderUnit
            ,tsod.order_unit_qty AS orderUnitQty
            ,tsod.total_amt AS orderUnitAmt
        FROM
            trd_order_discount_dtl todd
            LEFT JOIN trd_supplier_order_dtl tsod ON todd.supplier_order_dtl_id = tsod.supplier_order_dtl_id
            LEFt JOIn trd_supplier_order tso ON tsod.supplier_order_id = tso.supplier_order_id
            LEFT JOIN trd_order tor ON tor.order_id = tsod.order_id
        WHERE
            todd.coupon_template_id = #{pageReqVO.couponTemplateId}
            AND tor.order_no = #{pageReqVO.orderNo}
            <if test="null != pageReqVO.customerId">
                AND tor.branch_id = #{pageReqVO.customerId}
            </if>
            <if test="null != pageReqVO.startDate and pageReqVO.startDate != '' and null != pageReqVO.endDate and pageReqVO.endDate != ''">
                AND tor.create_time BETWEEN concat(#{pageReqVO.startDate}, ' 00:00:00.000') AND concat(#{pageReqVO.endDate}, ' 23:59:59.999')
            </if>
    </select>

    <!-- 查询活动订单汇总数据 -->
    <select id="getPrmActivityByActivityIds" resultType="com.zksr.trade.api.order.dto.ActivityOrderTotalDTO">
        SELECT
            t1.discount_id AS activityId
            ,COUNT(distinct tor.branch_id) AS activityCustomerCount
            ,SUM(tso.sub_pay_amt) activityOrderAmtSum
        FROM
            (
                SELECT
                    supplier_order_id
                    ,discount_id
                FROM
                    trd_order_discount_dtl
                WHERE
                    discount_id IN
                    <foreach collection="activityIds" item ="activityId" open="(" separator="," close=")">
                        #{activityId}
                    </foreach>
                GROUP BY
                    supplier_order_id
                    ,discount_id
            ) t1
            LEFT JOIN trd_supplier_order tso ON t1.supplier_order_id = tso.supplier_order_id
            LEFT JOIN trd_order tor ON tso.order_id = tor.order_id
        GROUP BY
            t1.discount_id
    </select>


    <!-- 获取参与该活动的客户订单汇总数据(按客户区分) -->
    <select id="getActivityCustomerTotal"
            resultType="com.zksr.trade.api.order.dto.ActivityCustomerOrderUseTotalDTO"
            parameterType="com.zksr.trade.controller.order.vo.TrdOrderPromitionReportPageReqVO">
        SELECT
            tor.branch_id AS customerId
            ,COUNT(distinct tor.order_id) AS activityOrderCount
            ,SUM(tso.sub_pay_amt) activityOrderAmtSum
        FROM
            (
                SELECT
                    supplier_order_id
                FROM
                    trd_order_discount_dtl
                WHERE
                    discount_id = #{activityId}
                GROUP BY
                    supplier_order_id
            ) t1
                LEFT JOIN trd_supplier_order tso ON t1.supplier_order_id = tso.supplier_order_id
                LEFT JOIN trd_order tor ON tso.order_id = tor.order_id
        <where>
            <if test="null != customerId">
                AND tor.branch_id = #{customerId}
            </if>
        </where>
        GROUP BY
            tor.branch_id
    </select>

    <!-- 获取使用该活动 指定客户的订单报表（按客户订单汇总） -->
    <select id="getActivityCustomerOrderTotal"
            resultType="com.zksr.trade.api.order.dto.ActivityCustomerOrderUseTotalDTO"
            parameterType="com.zksr.trade.controller.order.vo.TrdOrderPromitionReportPageReqVO">
        SELECT
            MAX(tor.branch_id) AS customerId
            ,tor.order_no AS orderNo
            ,MAX(tor.create_time) AS orderCreateTime
            ,SUM(tso.sub_order_num) AS activityOrderCount
            ,SUM(tso.sub_pay_amt) activityOrderAmtSum
        FROM
            (
                SELECT
                    supplier_order_id
                FROM
                    trd_order_discount_dtl
                WHERE
                    discount_id = #{activityId}
                GROUP BY
                    supplier_order_id
            ) t1
            LEFT JOIN trd_supplier_order tso ON t1.supplier_order_id = tso.supplier_order_id
            LEFT JOIN trd_order tor ON tso.order_id = tor.order_id
        <where>
            <if test="null != customerId">
                AND tor.branch_id = #{customerId}
            </if>
        </where>
        GROUP BY
            tor.order_no
    </select>

    <!-- 获取使用该活动 指定客户的订单报表（按客户订单明细）-->
    <select id="getActivityCustomerOrderDetail"
            resultType="com.zksr.trade.api.order.dto.ActivityCustomerOrderUseDetailDTO"
            parameterType="com.zksr.trade.controller.order.vo.TrdOrderPromitionReportPageReqVO">
        SELECT
            tso.supplier_id
            ,tso.supplier_name
            ,tsod.sku_id
            ,tsod.spu_id
            ,tsod.memo
            ,tsod.order_unit AS orderUnit
            ,tsod.order_unit_qty AS orderUnitQty
            ,tsod.total_amt AS orderUnitAmt
        FROM
            (
                SELECT
                    supplier_order_id
                FROM
                    trd_order_discount_dtl
                WHERE
                    discount_id = #{activityId}
                GROUP BY
                    supplier_order_id
            ) t1
            LEFT JOIN trd_supplier_order_dtl tsod ON tsod.supplier_order_id = t1.supplier_order_id
            LEFT JOIN trd_supplier_order tso ON t1.supplier_order_id = tso.supplier_order_id
            LEFT JOIN trd_order tor ON tso.order_id = tor.order_id
        <where>
            <if test="null != customerId">
                AND tor.branch_id = #{customerId}
            </if>
            <if test="null != orderNo and orderNo != ''">
                AND tor.order_no = #{orderNo}
            </if>
        </where>
    </select>

    <select id="deleteSpuCheckSupplierOrderDtl"
            resultType="java.lang.Long">
        SELECT
            spu_id
        FROM
            trd_supplier_order_dtl
        WHERE
            spu_id IN
            <foreach collection="spuIds" item ="spuId" open="(" separator="," close=")">
                #{spuId}
            </foreach>
        GROUP BY spu_id
    </select>

    <select id="checkOrderDeliveryStatusCountByBranchId" resultType="java.lang.Integer">
        SELECT
            count( 1 )
        FROM
            trd_supplier_order_dtl tsod
            LEFT JOIN trd_order tor ON tsod.order_id = tor.order_id
        WHERE
            tor.branch_id = #{branchId}
          AND tor.order_id != #{orderId}
          AND tsod.delivery_state NOT IN ( 0, 2 )
    </select>
    <select id="getSupplierOrderDtlByOrderNo"
            resultType="com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderDtlVO">
        SELECT
            tor.order_id,
            tor.order_no,
            tor.branch_id,
            tor.pay_way,
            tor.order_type,
            tor.sys_code,
            tsord.supplier_order_id,
            tsord.supplier_order_no,
            tsord.supplier_id,
            tsord.supplier_order_dtl_id,
            tsord.supplier_order_dtl_no,
            tsord.spu_id,
            tsord.sku_id,
            tsord.latest_date,
            tsord.oldest_date,
            tsord.gift_flag,
            IFNULL(tsord.sub_order_amt, 0) AS saleTotalAmt,
            IFNULL((tsord.coupon_discount_amt + tsord.coupon_discount_amt2 + tsord.activity_discount_amt), 0) AS discountAmt,
            IFNULL(tsord.coupon_discount_amt,0)+IFNULL(tsord.coupon_discount_amt2,0) AS coupon_amt,
            IFNULL(tsord.sub_order_amt, 0)-IFNULL(tsord.coupon_discount_amt,0)-IFNULL(tsord.coupon_discount_amt2,0) AS useCouponOrderMoney

        FROM
            trd_order tor
            INNER JOIN trd_supplier_order_dtl tsord ON tor.order_id = tsord.order_id
        WHERE tor.order_no = #{orderNo}
    </select>
    <select id="getSupplierOrderDtlByOrderNos"
            resultType="com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderDtlVO">
        SELECT
            tor.order_id,
            tor.order_no,
            tor.branch_id,
            tor.pay_way,
            tor.order_type,
            tor.sys_code,
            tsord.supplier_order_id,
            tsord.supplier_order_no,
            tsord.supplier_id,
            tsord.supplier_order_dtl_id,
            tsord.supplier_order_dtl_no,
            tsord.spu_id,
            tsord.sku_id,
            tsord.latest_date,
            tsord.oldest_date,
            tsord.gift_flag,
            IFNULL(tsord.sub_order_amt, 0) AS saleTotalAmt,
            IFNULL((tsord.coupon_discount_amt + tsord.coupon_discount_amt2 + tsord.activity_discount_amt), 0) AS discountAmt,
            IFNULL(tsord.coupon_discount_amt,0)+IFNULL(tsord.coupon_discount_amt2,0) AS coupon_amt,
            IFNULL(tsord.sub_order_amt, 0)-IFNULL(tsord.coupon_discount_amt,0)-IFNULL(tsord.coupon_discount_amt2,0) AS useCouponOrderMoney
        FROM
            trd_order tor
            INNER JOIN trd_supplier_order_dtl tsord ON tor.order_id = tsord.order_id
        WHERE tor.order_no in
        <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
    </select>
</mapper>