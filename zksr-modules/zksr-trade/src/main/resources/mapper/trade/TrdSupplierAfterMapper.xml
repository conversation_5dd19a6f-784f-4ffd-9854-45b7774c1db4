<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdSupplierAfterMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <resultMap id="querySupplierAfter" type="com.zksr.trade.api.after.dto.AfterSupplierRespDTO">
        <id property="supplierAfterId" column="supplier_after_id"/>
        <result property="supplierAfterNo" column="supplier_after_no"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="afterId" column="after_id"/>
        <result property="supplierAddress" column="return_addr"/>
        <result property="supplierPhone" column="return_phone"/>
        <collection property="afterSupplierDtlRespDTOList" javaType="list" ofType="com.zksr.trade.api.after.dto.AfterSupplierDtlRespDTO"
                    column="supplierAfterId">
            <id property="supplierAfterDtlId" column="supplier_after_dtl_id"/>
            <result property="supplierAfterDtlNo" column="supplier_after_dtl_no"/>
<!--            <result property="supplierAfterNo" column="supplier_after_id"/>-->
            <result property="skuId" column="sku_id"/>
            <result property="spuId" column="spu_id"/>
            <result property="returnQty" column="return_qty"/>
            <result property="returnAmt" column="return_amt"/>
            <result property="returnPrice" column="return_price"/>
            <result property="itemType" column="item_type"/>
            <result property="refundNo" column="refund_no"/>
        </collection>
    </resultMap>

    <select id="getSupplierAfterByAfterId" resultMap="querySupplierAfter">
        SELECT
            sAfter.supplier_after_id
            ,sAfter.supplier_after_no
            ,sAfter.supplier_id
            ,sAfter.after_id
            ,afterDtl.supplier_after_dtl_id
            ,afterDtl.supplier_after_dtl_no
            ,afterDtl.supplier_after_dtl_no
            ,afterDtl.sku_id
            ,afterDtl.spu_id
            ,afterDtl.return_qty
            ,afterDtl.return_amt
            ,afterDtl.return_price
            ,afterDtl.item_type
            ,sAfter.return_addr
            ,sAfter.return_phone
            ,afterDtl.refund_no
<!--        	,case-->
<!--        		when afterDtl.approve_state = 0 then 1 &lt;!&ndash; 待处理 &ndash;&gt;-->
<!--        		when afterDtl.approve_state = 1 and afterDtl.return_state = 0 then 2  &lt;!&ndash; 待退货 &ndash;&gt;-->
<!--        		when afterDtl.approve_state = 1 and afterDtl.return_state = 1 then 3 &lt;!&ndash; 退货中 &ndash;&gt;-->
<!--        		when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 0 then 5  &lt;!&ndash; 待退款 &ndash;&gt;-->
<!--        		when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 1 then 6  &lt;!&ndash; 退款中 &ndash;&gt;-->
<!--        		when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 3 then 7  &lt;!&ndash; 退款失败 &ndash;&gt;-->
<!--        		when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 2 then 8  &lt;!&ndash; 处理完成 &ndash;&gt;-->
<!--        		when afterDtl.approve_state = 2 then 9  &lt;!&ndash; 已拒绝 &ndash;&gt;-->
<!--        		else 0 &lt;!&ndash; 问题状态 &ndash;&gt;-->
<!--        	end handleState-->
        FROM trd_supplier_after sAfter
        INNER JOIN trd_supplier_after_dtl afterDtl ON sAfter.supplier_after_id = afterDtl.supplier_after_id
        where sAfter.after_id in
        <foreach collection="afterIds" item="afterId" open="(" separator="," close=")">
            #{afterId}
        </foreach>

    </select>
    <select id="getOrderReceiptInfoBySupplierAfterNo"
            resultType="com.zksr.trade.api.order.dto.OrderReceiptRespDTO"
            parameterType="com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO">
        SELECT
            tsa.supplier_after_no AS supplierSheetNo
            , -tsa.sub_refund_amt AS receiptAmt
            , (select -SUM(tsad.exact_return_amt) from trd_supplier_after_dtl tsad where tsad.supplier_after_id = tsa.supplier_after_id )  AS receiptExactAmt
            , 'SH' AS sheetType
            , IFNULL(tsa.trans_no, 'SHS') AS supplierSheetType
            , tar.pay_way AS sheetPayWay
        FROM
            trd_supplier_after tsa
            LEFT JOIN trd_after tar ON tsa.after_id = tar.after_id
        WHERE
            tsa.supplier_after_no = #{supplierSheetNo}
    </select>
</mapper>