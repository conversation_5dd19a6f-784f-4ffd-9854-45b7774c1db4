<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdSupplierAfterDtlMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- 根据入驻商订单明细ID 获取已售后总数量 -->
    <select id="getAfterDtlNumBySupplierDtlId" resultType="com.zksr.trade.api.after.dto.OrderAfterDtlResDTO">
        SELECT
            SUM(return_qty) as sumReturnNum
            ,SUM(return_Amt) AS sumReturnAmt
            ,supplier_order_dtl_id
        FROM trd_supplier_after_dtl
        WHERE
            is_cancel = 0
            AND supplier_order_dtl_id IN
                <foreach collection="supplierDtlIds" item="supplierDtlId" open="(" separator="," close=")">
                    #{supplierDtlId}
                </foreach>
            GROUP BY supplier_order_dtl_id
    </select>
    <select id="getTrdSupplierAfterDtlBySplNo"
            resultType="com.zksr.common.core.domain.erp.dto.AfterDetailOpenDto">
        select
            tsad.sku_id as itemNo,
            tsad.return_qty as subQty,
            tsad.memo as remark,
            '1' as externalLineNo
        from trd_supplier_after_dtl as tsad
        where tsad.supplier_after_no =#{aLong}
    </select>

    <!-- 根据入驻商订单ID 查询发货后退款的已审核订单的退款金额和退款单位 -->
    <select id="selectAfterDtlBySupplierOrderDtlId"
            resultType="com.zksr.trade.controller.order.vo.DcSupplierOrderAfterDtlRespVO">
        SELECT
            dtl.return_amt afterAmt,
            dtl.return_unit afterUnit,
            dtl.return_unit_qty afterNum,
            tsa.trans_no afterOrderType
        FROM
            trd_supplier_after_dtl dtl
                LEFT JOIN trd_supplier_after tsa ON tsa.after_id = dtl.after_id
        WHERE
            dtl.supplier_order_dtl_id = #{supplierOrderDtlId}
          AND dtl.after_phase = #{afterType}
          AND dtl.approve_state = 1
    </select>
</mapper>