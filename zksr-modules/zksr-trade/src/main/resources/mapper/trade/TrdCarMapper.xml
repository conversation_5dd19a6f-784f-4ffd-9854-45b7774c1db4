<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdCarMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectInitData" resultType="com.zksr.trade.api.car.dto.AppCarInitDTO">
        SELECT
            car_id AS trdCarId,
            branch_id AS branchId,
            supplier_id AS supplierId,
            supplier_item_id AS supplierItemId,
            area_item_id AS areaItemId,
            sku_id AS skuId,
            qty AS productNum,
            selected AS selected,
            recommend_flag AS recommendFlag
        FROM
            trd_car
        WHERE
            qty > 0
            <if test="minId != null">
                AND car_id > #{minId}
            </if>
            <if test="branchId != null">
                AND branch_id = #{branchId}
            </if>
        ORDER BY
            car_id ASC
        LIMIT 500
    </select>
</mapper>