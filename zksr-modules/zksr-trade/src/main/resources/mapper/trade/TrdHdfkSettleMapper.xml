<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdHdfkSettleMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectHdfkOrderItemList" resultType="com.zksr.trade.controller.hdfk.vo.TrdHdfkOrderItemVO">
        SELECT
            tso.order_id,
            tso.order_no,
            tso.supplier_order_no,
            tor.create_time,
            tso.supplier_order_id,
            tso.supplier_id,
            tsod.thumb,
            tsod.sku_id,
            tsod.spu_name,
            tsod.supplier_order_dtl_id,
            tsod.delivery_state,
            tsod.total_num - tsod.cancel_qty AS total_num,
            <!-- 应结算金额 -->
            (SELECT SUM(ths.settle_amt) FROM trd_hdfk_settle ths WHERE ths.supplier_order_dtl_id = tsod.supplier_order_dtl_id) settleAmt,
            IFNULL((SELECT SUM(tsad.return_qty) FROM trd_supplier_after_dtl tsad WHERE tsad.supplier_order_dtl_id = tsod.supplier_order_dtl_id AND tsad.after_phase = 2 AND tsad.is_cancel = 0), 0) AS afterTotalNum,
            tsod.exact_price

        FROM
            trd_supplier_order tso
            INNER JOIN (
                SELECT
                    tso.supplier_order_id,
                    tsod.delivery_state
                FROM
                    trd_supplier_order tso
                    INNER JOIN trd_order tor ON tso.order_id = tor.order_id
                    INNER JOIN trd_supplier_order_dtl tsod ON tso.supplier_order_id = tsod.supplier_order_id
                WHERE
                    tsod.pay_state = 3
                    <!-- 门店ID -->
                    <if test="branchId != null">
                        AND tor.branch_id = #{branchId}
                    </if>
                    <!-- 用户ID -->
                    <if test="memberId != null">
                        AND tor.member_id = #{memberId}
                    </if>
                    <!-- 0-待配货,3-待发货,4-待收货,5-已收货,6-已完成,7-备货中,40-待装车 -->
                    AND tsod.delivery_state IN (0, 3, 4, 5, 6, 7, 40)
                    <if test='condition != null and condition != ""'>
                        AND tsod.spu_name LIKE CONCAT('%', #{condition}, '%')
                    </if>
                    <choose>
                        <!-- 如果指定订单号搜索, 则不限制查询数据范围 -->
                        <when test='orderNo != null and orderNo != ""'>
                            AND (tso.supplier_order_no = #{orderNo} OR tor.order_no = #{orderNo})
                        </when>
                        <!-- 没有订单号搜索, 默认查询60天以内的数据 -->
                        <otherwise>
                            AND tor.create_time > DATE_SUB(NOW(), INTERVAL 60 DAY)
                        </otherwise>
                    </choose>
                GROUP BY
                    tso.supplier_order_id,
                    tsod.delivery_state
                ORDER BY
                    tso.supplier_order_id DESC
                LIMIT #{pageNo}, #{pageSize}
            ) page_table ON page_table.supplier_order_id = tso.supplier_order_id
            INNER JOIN trd_supplier_order_dtl tsod ON page_table.supplier_order_id = tsod.supplier_order_id AND tsod.delivery_state = page_table.delivery_state AND tsod.pay_state = 3
            INNER JOIN trd_order tor ON tso.order_id = tor.order_id
    </select>
    <select id="selectBranchHdfkNoPaymentTotal" resultType="com.zksr.trade.api.hdfk.dto.HdfkNoPaymentTotalDTO">
        SELECT
            SUM(ths.settle_amt) totalAmt
        FROM
            trd_supplier_order tso
            LEFT JOIN trd_supplier_order_dtl tsod ON tso.supplier_order_id = tsod.supplier_order_id
            LEFT JOIN trd_order tor ON tso.order_id = tor.order_id
            LEFT JOIN trd_hdfk_settle ths ON ths.supplier_order_dtl_id = tsod.supplier_order_dtl_id
        WHERE
            tor.create_time > DATE_SUB(NOW(), INTERVAL 60 DAY)
            AND tso.pay_state = '3'
            <!-- 门店ID -->
            <if test="branchId != null">
                AND tor.branch_id = #{branchId}
            </if>
            <!-- 用户ID -->
            <if test="memberId != null">
                AND tor.member_id = #{memberId}
            </if>
    </select>
    <select id="selectHdfkSkuItemList"
            resultType="com.zksr.trade.controller.hdfk.vo.TrdHdfkSettleSkuItemRespVO">
        SELECT
            tsod.supplier_order_dtl_id,
            tor.branch_id,
            tsod.supplier_id,
            tso.order_no,
            tso.supplier_order_no,
            tso.create_time,
            tsod.pay_state,
            tsod.total_amt,
            tsod.delivery_state,
            (SELECT SUM(ths.settle_amt) FROM trd_hdfk_settle ths WHERE ths.supplier_order_dtl_id = tsod.supplier_order_dtl_id) settleAmt,
            tsod.spu_name,
            tsod.sku_id,
            tsod.order_unit,
            tsod.order_unit_type,
            hp.create_time processTime,
            hp.create_by processUser,
            hp.hdfk_pay_no,
            hp.hdfk_pay_id
        FROM
            trd_supplier_order tso
            INNER JOIN trd_order tor ON tso.order_id = tor.order_id
            INNER JOIN trd_supplier_order_dtl tsod ON tso.supplier_order_id = tsod.supplier_order_id
            LEFT  JOIN trd_hdfk_pay hp ON hp.hdfk_pay_id = tsod.hdfk_pay_id
        WHERE
            tsod.pay_state IN (3, 4)
            <!-- 0-待配货,3-待发货,4-待收货,5-已收货,6-已完成,7-备货中,40-待装车 -->
            AND tso.delivery_state in (21,40)
            <if test="orderNo != null">
                AND tso.order_no = #{orderNo}
            </if>
            <if test="supplierOrderNo != null">
                AND tso.supplier_order_no = #{supplierOrderNo}
            </if>
            <if test="branchId != null">
                AND tor.branch_id = #{branchId}
            </if>
            <if test="startTime != null">
                AND tor.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="startProcessTime != null">
                AND hp.create_time BETWEEN #{startProcessTime} AND #{endProcessTime}
            </if>
            <if test="hdfkPayNo != null">
                AND hp.hdfk_pay_no = #{hdfkPayNo}
            </if>
            <if test="spuName != null">
                AND tsod.spu_name LIKE CONCAT('%', #{spuName}, '%')
            </if>
            <if test="processState != null and processState == 0">
                AND hp.hdfk_pay_id IS NULL
            </if>
            <if test="processState != null and processState == 1">
                AND hp.hdfk_pay_id IS NOT NULL
            </if>
            <if test="hdfkPayId != null">
                AND hp.hdfk_pay_id = #{hdfkPayId}
            </if>
            <!-- 数据范围隔离 -->
            ${params.dataScope}
        ORDER BY
            tso.order_id DESC
    </select>

    <select id="selectHdfkOrderItemListByOrderDtlIds"
            resultType="com.zksr.trade.controller.hdfk.vo.TrdHdfkOrderItemVO">
        SELECT
            tsod.supplier_order_dtl_id,
            tsod.total_num - tsod.cancel_qty AS total_num,
            IFNULL((SELECT SUM(tsad.return_qty) FROM trd_supplier_after_dtl tsad WHERE tsad.supplier_order_dtl_id = tsod.supplier_order_dtl_id AND tsad.after_phase = 2 AND tsad.is_cancel = 0), 0) AS afterTotalNum,
            tsod.exact_price
        FROM
            trd_supplier_order_dtl tsod
        WHERE
            tsod.supplier_order_dtl_id IN
                <foreach collection="supplierOrderDtlIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
    </select>
    <select id="selectByPayOrderDtlId" resultType="com.zksr.trade.domain.TrdHdfkSettle">
        SELECT
            hs.*
        FROM
            trd_hdfk_settle hs
            LEFT JOIN trd_supplier_order_dtl tsod ON hs.supplier_order_dtl_id = tsod.supplier_order_dtl_id
        WHERE
            hs.supplier_order_dtl_id IN
            <foreach collection="supplierOrderDtlIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND tsod.hdfk_pay_id IS NULL
    </select>
</mapper>