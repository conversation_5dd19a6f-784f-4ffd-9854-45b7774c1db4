<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdSupplierOrderSettleMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- 更新入驻商订单打印数量 -->
    <update id="editOrderPrintQtyBySupplierOrderNos">
        UPDATE
            trd_supplier_order
        SET
            print_qty = print_qty + 1,
            print_state = 1
        WHERE
            supplier_order_no IN
            <foreach collection="supplierOrderNos" item="supplierOrderNo" open="(" separator="," close=")">
                #{supplierOrderNo}
            </foreach>
    </update>

    <select id="getColonelAppPercentageAmt" resultType="java.math.BigDecimal">
        -- 选择总的比例金额并命名为total_percentage
        SELECT SUM(cast(percentageAmt as decimal(18,2)))  AS total_percentage
        FROM (
                 -- 计算每个记录的比例金额，根据source_type决定是正数还是负数
                 SELECT
                         COALESCE(colonel_amt, 0) * CASE WHEN source_type = 'order' THEN 1 ELSE -1 END AS percentageAmt
                 FROM (
                          -- 从订单数据中选择和计算colonel_amt
                          SELECT
                              'order' AS source_type,
                              CASE
                                  WHEN tor.colonel_id = #{colonelId} and tor.pcolonel_id = #{colonelId} then colonel2_amt + colonel1_amt
                                  WHEN tor.colonel_id = #{colonelId} THEN colonel2_amt
                                  WHEN tor.pcolonel_id = #{colonelId} THEN colonel1_amt
                                  END AS colonel_amt,
                              tor.colonel_id,
                              tor.create_time
                          FROM trd_supplier_order_settle settle
                                   LEFT JOIN trd_order tor ON settle.order_id = tor.order_id
                          WHERE (tor.colonel_id = #{colonelId} OR tor.pcolonel_id = #{colonelId})
                            AND DATE(tor.create_time) = CURDATE()
                            -- AND tor.create_time BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 DAY) - INTERVAL 1 SECOND

                          UNION ALL

                          -- 从售后数据中选择和计算colonel_amt
                          SELECT
                              'after' AS source_type,
                              CASE
                              WHEN tar.colonel_id = #{colonelId} and tar.pcolonel_id = #{colonelId} then colonel2_amt + colonel1_amt
                              WHEN tar.colonel_id = #{colonelId} THEN colonel2_amt
                                  WHEN tar.pcolonel_id = #{colonelId} THEN colonel1_amt
                                  END AS colonel_amt,
                              tar.colonel_id,
                              tar.create_time
                          FROM trd_supplier_after_settle settle
                              LEFT JOIN trd_after tar ON settle.after_id = tar.after_id
                          WHERE (tar.colonel_id = #{colonelId} OR tar.pcolonel_id = #{colonelId})
                            AND DATE(tar.create_time) = CURDATE()
                            -- AND tar.create_time BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 DAY) - INTERVAL 1 SECOND
                      ) subquery
             ) collected_data;

    </select>
</mapper>