<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdDriverRatingMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <!--司机评价列表 -->
    <select id="selectDriverRatingList" resultType="com.zksr.trade.controller.rating.vo.TrdDriverRatingRespVO">
        SELECT o.branch_id AS branchId,
        ra.driver_rating_id AS driverRatingId,
        ra.create_time AS createTime,
        ra.member_id AS memberId,
        d.driver_name AS driverName,
        d.driver_phone AS driverPhone,
        ra.slot1_code AS slot1Code,
        ra.slot1_val AS slot1Val,
        ra.slot2_code AS slot2Code,
        ra.slot2_val AS slot2Val,
        ra.slot3_code AS slot3Code,
        ra.slot3_val AS slot3Val,
        ra.slot1_score_code AS slot1ScoreCode,
        ra.slot1_score_code_val AS slot1ScoreCodeVal,
        ra.slot2_score AS slot2Score,
        ra.slot2_score_code_val AS slot2ScoreCodeVal,
        ra.slot3_score AS slot3Score,
        ra.score_code_val AS scoreCodeVal,
        ra.reason_code AS reasonCode,
        ra.reason_val AS reasonVal,
        ra.fedback_msg AS fedbackMsg,
        ra.fedback_pics AS fedbackPics,
        tso.supplier_order_no AS supplierOrderNo,
        ra.driver_id AS driverId,
        tso.order_no AS orderNo,
        o.order_type AS orderType
        FROM trd_driver_rating ra
        LEFT JOIN trd_supplier_order tso ON ra.driver_rating_id = tso.driver_rating_id
        LEFT JOIN trd_order o ON tso.order_id = o.order_id
        LEFT JOIN trd_driver d ON d.driver_id = tso.driver_id
        <where>
            <if test="reqVO.branchId != null and '' != reqVO.branchId">
                AND o.branch_id = #{reqVO.branchId}
            </if>
            <if test="reqVO.supplierOrderNo != null and '' != reqVO.supplierOrderNo">
                AND tso.supplier_order_no = #{reqVO.supplierOrderNo}
            </if>
            <!--评价时间 -->
            <if test="reqVO.createTimeBegin != null and reqVO.createTimeEnd != null and '' != reqVO.createTimeBegin and '' != reqVO.createTimeEnd">
                AND ra.create_time BETWEEN #{reqVO.createTimeBegin} AND #{reqVO.createTimeEnd}
            </if>
            <!--单据类型 -->
            <if test="reqVO.orderType != null and '' !=reqVO.orderType">
                AND o.order_type = #{reqVO.orderType}
            </if>
            <!--司机ID -->
            <if test="reqVO.driverId != null and '' !=reqVO.driverId">
                AND ra.driver_id = #{reqVO.driverId}
            </if>
            <!--司机态度 -->
            <if test="reqVO.slot1ScoreCodeVal != null and '' != reqVO.slot1ScoreCodeVal">
                AND ra.slot1_score_code_val = #{reqVO.slot1ScoreCodeVal}
            </if>
            <if test="reqVO.slot1ScoreCode != null and '' !=reqVO.slot1ScoreCode">
                AND ra.slot1_score_code = #{reqVO.slot1ScoreCode}
            </if>
            <if test="reqVO.slot2ScoreCodeVal != null and '' !=reqVO.slot2ScoreCodeVal">
                AND ra.slot2_score_code_val = #{reqVO.slot2ScoreCodeVal}
            </if>
            <if test="reqVO.slot2Score != null and '' !=reqVO.slot2Score">
                AND ra.slot2_score = #{reqVO.slot2Score}
            </if>
            <if test="reqVO.scoreCodeVal != null and '' !=reqVO.scoreCodeVal">
                AND ra.score_code_val = #{reqVO.scoreCodeVal}
            </if>
            <if test="reqVO.slot3Score != null and '' !=reqVO.slot3Score">
                AND ra.slot3_score = #{reqVO.slot3Score}
            </if>
        </where>
    </select>
    <select id="selectDriverRatingById" resultType="com.zksr.trade.controller.rating.vo.TrdDriverRatingRespVO">
        SELECT o.branch_id             AS branchId,
               ra.driver_rating_id     AS driverRatingId,
               ra.member_id            AS memberId,
               ra.create_time          AS createTime,
               d.driver_name           AS driverName,
               d.driver_phone          AS driverPhone,
               ra.slot1_code           AS slot1Code,
               ra.slot1_val            AS slot1Val,
               ra.slot2_code           AS slot2Code,
               ra.slot2_val            AS slot2Val,
               ra.slot3_code           AS slot3Code,
               ra.slot3_val            AS slot3Val,
               ra.slot1_score_code     AS slot1ScoreCode,
               ra.slot1_score_code_val AS slot1ScoreCodeVal,
               ra.slot2_score          AS slot2Score,
               ra.slot2_score_code_val AS slot2ScoreCodeVal,
               ra.slot3_score          AS slot3Score,
               ra.score_code_val       AS scoreCodeVal,
               ra.reason_code          AS reasonCode,
               ra.reason_val           AS reasonVal,
               ra.fedback_msg          AS fedbackMsg,
               ra.fedback_pics         AS fedbackPics,
               ra.driver_id            AS driverId,
               tso.supplier_order_no   AS supplierOrderNo,
               tso.order_no            AS orderNo
        FROM trd_driver_rating ra
                 LEFT JOIN trd_supplier_order tso ON ra.driver_rating_id = tso.driver_rating_id
                 LEFT JOIN trd_order o ON tso.order_id = o.order_id
                 LEFT JOIN trd_driver d ON d.driver_id = tso.driver_id
        WHERE ra.driver_rating_id = #{driverRatingId}
    </select>

</mapper>