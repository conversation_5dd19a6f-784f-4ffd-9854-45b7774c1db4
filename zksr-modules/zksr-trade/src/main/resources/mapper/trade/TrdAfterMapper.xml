<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdAfterMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

	<!-- 订单分页查询返回Map 新-->
	<resultMap id="afterListNew" type="com.zksr.trade.api.after.dto.AfterOrderPageRespDTO">
		<id property="afterId" column="after_id"/>
		<result property="afterNo" column="after_no"/>
		<result property="branchId" column="branch_id"/>
		<result property="createTime" column="create_time"/>
		<result property="handleState" column="handleState"/>
		<result property="orderType" column="order_type"/>
		<result property="afterType" column="after_type"/>
		<result property="reason" column="reason"/>
		<result property="returnOrderAmt" column="return_order_amt"/>
		<result property="returnDiscountAmt" column="return_discount_amt"/>


		<collection property="afterSupplierRespDTOS" ofType="com.zksr.trade.api.after.dto.AfterSupplierRespDTO">
			<id property="supplierAfterId" column="supplier_after_id"/>
			<result property="supplierId" column="supplier_id"/>
			<result property="supplierName" column="supplier_name"/>
			<result property="supplierAfterNo" column="supplier_after_no"/>

			<collection property="afterSupplierDtlRespDTOList" ofType="com.zksr.trade.api.after.dto.AfterSupplierDtlRespDTO">
				<id property="supplierAfterDtlId" column="supplier_after_dtl_id"/>
				<result property="spuId" column="spu_id"/>
				<result property="skuId" column="sku_id"/>
				<result property="returnQty" column="return_unit_qty"/>
				<result property="returnPrice" column="return_sales_unit_price"/>
				<result property="returnAmt" column="return_amt"/>
				<result property="itemType" column="item_type"/>
				<result property="returnUnit" column="return_unit"/>
				<result property="returnUnitType" column="return_unit_type"/>
			</collection>
		</collection>
	</resultMap>



    <select id="selectPage" resultType="com.zksr.trade.controller.after.dto.AfterPageRespDTO">
        SELECT
	        tAfter.after_id
	        ,afterDtl.item_type AS productType
	        ,afterDtl.after_type
	        ,afterDtl.after_phase
	        ,afterDtl.approve_state
	        ,afterDtl.return_state
	        ,afterDtl.refund_state
            ,afterDtl.supplier_after_no
		    ,supAfter.source_order_no
	        ,case
				when afterDtl.is_cancel = 1 and afterDtl.approve_state = 0 then 10 <!-- 已取消 -->
		        when afterDtl.approve_state = 0 then 1 <!-- 待处理 -->
		        when afterDtl.approve_state = 1 and afterDtl.return_state = 0 then 2  <!-- 待退货 -->
		        when afterDtl.approve_state = 1 and afterDtl.return_state = 1 then 3 <!-- 退货中 -->
				when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 0 then 4  <!-- 已退货 -->
		        <!-- when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 0 then 5   待退款 -->
		        when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 1 then 6  <!-- 退款中 -->
		        when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 3 then 7  <!-- 退款失败 -->
		        when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 2 then 8  <!-- 处理完成 -->
		        when afterDtl.approve_state = 2 then 9  <!-- 已拒绝 -->
				when afterDtl.approve_state = 3 then 11  <!-- 已撤销 -->
		        else 0 <!-- 问题状态 -->
	        end handleState
            ,MAX(tAfter.after_no) afterNo
	        ,MAX(tOrder.order_no) orderNo
	        ,MAX(tAfter.create_time) afterCreateTime
            ,MAX(tOrder.create_time) orderCreateTime
	        ,MAX(tOrder.pay_time) orderPayTime
	        ,MAX(tOrder.pay_way) payWay
            ,sum(return_qty) returnQty
	        ,sum(return_amt) returnAmt
        	,MAX(tAfter.branch_id) AS branchId
        	,MAX(tAfter.colonel_id) AS colonelId
        	,MAX(tAfter.create_by) AS createBy
        FROM  trd_after tAfter
        LEFT JOIN trd_order tOrder ON tAfter.order_id = tOrder.order_id
		LEFT JOIN trd_supplier_after supAfter ON supAfter.after_id = tAfter.after_id
        LEFT JOIN trd_supplier_after_dtl afterDtl ON supAfter.supplier_after_id = afterDtl.supplier_after_id
        <where>
			<if test="null != pageReqVO.branchId">
				AND tOrder.branch_id = #{pageReqVO.branchId}
			</if>
			<if test="null != pageReqVO.supplierId">
				AND afterDtl.supplier_id = #{pageReqVO.supplierId}
			</if>
            <if test="null != pageReqVO.startDate and null != pageReqVO.endDate ">
				AND tAfter.create_time BETWEEN CONCAT(#{pageReqVO.startDate},' 00:00:00') and CONCAT(#{pageReqVO.endDate},' 23:59:59')
			</if>
			<if test="null != pageReqVO.afterNo">
				AND tAfter.after_no like concat('%', #{pageReqVO.afterNo}, '%')
			</if>
			<if test="null != pageReqVO.supplierAfterNo">
				AND afterDtl.supplier_after_no like concat('%', #{pageReqVO.supplierAfterNo}, '%')
			</if>
			<if test="null != pageReqVO.orderNo">
				AND tOrder.order_no like concat('%', #{pageReqVO.orderNo}, '%')
			</if>
			<if test="null != pageReqVO.productType">
				AND afterDtl.item_type = #{pageReqVO.productType}
			</if>
			<if test="null != pageReqVO.handleState">
				<choose>
					<when test="pageReqVO.handleState == 1"> <!-- 待处理 已取消的不查询-->
						AND afterDtl.approve_state = 0
                        AND afterDtl.is_cancel = 0
					</when>
					<when test="pageReqVO.handleState == 0"> <!-- 处理中查询全部 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.is_cancel = 0
						AND afterDtl.return_state in (0, 1, 2, 3)
                        AND afterDtl.refund_state in (0, 1, 3)
					</when>
					<when test="pageReqVO.handleState == 2"> <!-- 待退货 -->
					 	AND afterDtl.approve_state = 1
					 	AND afterDtl.return_state = 0
					</when>
					<when test="pageReqVO.handleState == 3"> <!-- 退货中 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state = 1
					</when>
					<when test="pageReqVO.handleState == 4"> <!-- 已退货 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state in (2,3)
						AND afterDtl.refund_state = 0
					</when>
					<!-- <when test="pageReqVO.handleState == 5">  待退款
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state in (2,3)
						AND afterDtl.refund_state = 0
					</when>  -->
					<when test="pageReqVO.handleState == 6"> <!-- 退款中 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state in (2,3)
						AND afterDtl.refund_state = 1
					</when>
					<when test="pageReqVO.handleState == 7"> <!-- 退款失败 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state in (2,3)
						AND afterDtl.refund_state = 3
					</when>
					<when test="pageReqVO.handleState == 8"> <!-- 处理完成 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state in (2,3)
						AND afterDtl.refund_state = 2
					</when>
					<when test="pageReqVO.handleState == 9"> <!-- 已拒绝 -->
						AND afterDtl.approve_state = 2
						AND afterDtl.is_cancel = 1
					</when>
					<when test="pageReqVO.handleState == 10"> <!-- 已取消 -->
						AND afterDtl.approve_state = 0
						AND afterDtl.is_cancel = 1
					</when>
					<when test="pageReqVO.handleState == 11"> <!-- 已撤销 -->
						AND afterDtl.approve_state = 3
						AND afterDtl.is_cancel = 1
					</when>
					<when test="pageReqVO.handleState == 99"> <!-- 可确认退款数据， 查询退货中和已退货的 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state IN (1, 2, 3)
						AND afterDtl.refund_state IN (0, 3)
					</when>
					<when test="pageReqVO.handleState == 100"> <!-- 发货前售后单据, 只查询发货前售后的 -->
						AND afterDtl.after_phase = 1
					</when>
				</choose>
			</if>
			<if test="null != pageReqVO.sourceOrderNo and '' != pageReqVO.sourceOrderNo">
				AND supAfter.source_order_no LIKE concat('%', #{pageReqVO.sourceOrderNo}, '%')
			</if>
			${pageReqVO.params.dataScope}
        </where>
		GROUP BY
			tAfter.after_id
			,afterDtl.item_type
			,afterDtl.after_type
			,afterDtl.after_phase
			,afterDtl.approve_state
			,afterDtl.return_state
			,afterDtl.refund_state
		    ,afterDtl.is_cancel
			,afterDtl.supplier_after_no
		    ,supAfter.source_order_no
		ORDER BY afterCreateTime DESC
    </select>


	<select id="selectAfterDtlList" resultType="com.zksr.trade.controller.after.dto.AfterDtlListDTO"
			parameterType="com.zksr.trade.api.order.vo.AfterDtlReqVO">
		SELECT
			tAfter.create_time AS afterCreateTime,
			afterDtl.supplier_after_dtl_id,
			afterDtl.supplier_after_dtl_no,
			afterDtl.sku_id,
			afterDtl.spu_id,
			tOrder.member_id,
			orderDtl.pay_state AS orderPayState,
			tOrder.create_time AS orderCreateTime,
			tOrder.pay_time AS orderPayTime,
			tOrder.pay_way,
			afterDtl.return_price,
			orderDtl.total_num AS orderTotalNum,
			orderDtl.total_amt AS orderTotalAmt,
			afterDtl.return_qty,
			afterDtl.return_amt,
			afterDtl.after_type,
			tAfter.reason,
			tAfter.apply_imgs,
			sAfter.supplier_id,
			tAfter.after_no,
			afterDtl.item_type,
			tOrder.order_no,
			afterDtl.refund_type,
			sAfter.return_addr,
			sAfter.return_phone,
			tAfter.after_id,
			tAfter.branch_id AS branchId,
			afterDtl.refund_fail_reason AS refundFailReason,
			afterDtl.return_unit,
			afterDtl.return_unit_qty AS returnUnitQty,
			afterDtl.return_unit_size AS returnUnitSize,
			afterDtl.return_sales_unit_price AS returnSalesUnitPrice,
			afterDtl.order_unit AS orderUnit,
			afterDtl.order_unit_type AS orderUnitType,
			afterDtl.order_unit_qty AS orderUnitQty,
			afterDtl.order_unit_size AS orderUnitSize,
			afterDtl.sku_id AS skuId,
			(case
			when afterDtl.is_cancel = 1 and afterDtl.approve_state = 0 then 10 <!-- 已取消 -->
			when afterDtl.approve_state = 0 then 1 <!-- 待处理 -->
			when afterDtl.approve_state = 1 and afterDtl.return_state = 0 then 2  <!-- 待退货 -->
			when afterDtl.approve_state = 1 and afterDtl.return_state = 1 then 3 <!-- 退货中 -->
			when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 0 then 4  <!-- 已退货 -->
			<!-- when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 0 then 5   待退款 -->
			when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 1 then 6  <!-- 退款中 -->
			when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 3 then 7  <!-- 退款失败 -->
			when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 2 then 8  <!-- 处理完成 -->
			when afterDtl.approve_state = 2 then 9  <!-- 已拒绝 -->
			when afterDtl.approve_state = 3 then 11  <!-- 已撤销 -->
			else 0 <!-- 问题状态 -->
			end) handleState,
			sAfter.supplier_after_no,
			sAfter.supplier_order_no,
			tso.sub_pay_amt,
			tso.sub_order_amt,
			tso.sub_discount_amt,
		    tso.sub_pay_balance_amt as subPayBalanceAmt,
			afterDtl.original_return_qty,
			afterDtl.original_return_amt,
			(orderDtl.total_num - (
				SELECT
				    IFNULL(SUM(tsad.return_qty), 0)
				FROM
				    trd_supplier_after_dtl tsad
				WHERE
					tsad.supplier_order_dtl_id = afterDtl.supplier_order_dtl_id AND afterDtl.is_cancel = 0
			)) AS waitReturnQty,
			(orderDtl.res_amt - (
				SELECT
					IFNULL(SUM(tsad.return_amt), 0)
				FROM
					trd_supplier_after_dtl tsad
				WHERE
					tsad.supplier_order_dtl_id = afterDtl.supplier_order_dtl_id AND afterDtl.is_cancel = 0
			)) AS waitReturnAmt
		FROM
		    trd_after tAfter
			LEFT JOIN trd_supplier_after sAfter ON tAfter.after_id = sAfter.after_id
		    LEFT JOIn trd_supplier_order tso ON sAfter.supplier_order_no = tso.supplier_order_no
			LEFT JOIN trd_order tOrder ON tAfter.order_id = tOrder.order_id
			LEFT JOIN trd_supplier_after_dtl afterDtl ON tAfter.after_id = afterDtl.after_id
			LEFT JOIN trd_supplier_order_dtl orderDtl ON afterDtl.supplier_order_dtl_id = orderDtl.supplier_order_dtl_id
		<where>
			<if test="null != supplierId">
				AND afterDtl.supplier_id = #{supplierId}
			</if>
			<if test="null != afterId">
				AND tAfter.after_id = #{afterId}
			</if>
			<if test="null != productType">
				AND afterDtl.item_type = #{productType}
			</if>
			<if test="null != handleState">
				<choose>
					<when test="handleState == 1"> <!-- 待处理 -->
						AND afterDtl.approve_state = 0
					</when>
					<when test="handleState == 0"> <!-- 处理中查询全部 -->
						AND afterDtl.approve_state = 0
						AND (afterDtl.return_state in (0, 1, 2, 3) OR afterDtl.refund_state in (0, 1, 2, 3))
					</when>
					<when test="handleState == 2"> <!-- 待退货 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state = 0
					</when>
					<when test="handleState == 3"> <!-- 退货中 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state = 1
					</when>
					<when test="handleState == 5"> <!-- 待退款 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state in (2,3)
						AND afterDtl.refund_state = 0
					</when>
					<when test="handleState == 6"> <!-- 退款中 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state in (2,3)
						AND afterDtl.refund_state = 1
					</when>
					<when test="handleState == 7"> <!-- 退款失败 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state in (2,3)
						AND afterDtl.refund_state = 3
					</when>
					<when test="handleState == 8"> <!-- 处理完成 -->
						AND afterDtl.approve_state = 1
						AND afterDtl.return_state in (2,3)
						AND afterDtl.refund_state = 2
					</when>
					<when test="handleState == 9"> <!-- 已拒绝 -->
						AND afterDtl.approve_state = 2
						AND afterDtl.is_cancel = 1
					</when>
					<when test="handleState == 10"> <!-- 已取消 -->
			            AND afterDtl.approve_state = 0
						AND afterDtl.is_cancel = 1
					</when>
					<when test="handleState == 11"> <!-- 已撤销 -->
						AND afterDtl.approve_state = 3
						AND afterDtl.is_cancel = 1
					</when>
				</choose>
			</if>
		</where>
		GROUP BY afterDtl.supplier_after_dtl_id
		ORDER BY afterDtl.supplier_after_dtl_id DESC
	</select>


	<!-- 售后订单小程序分页查询-->
	<select id="pageAfter" resultType="com.zksr.trade.api.after.dto.AfterOrderPageRespDTO">
		SELECT
			tAfter.after_id
			,tAfter.after_no
			,tAfter.create_time
			,tAfter.branch_id
			,case
			    when afterDtl.is_cancel = 1 and afterDtl.approve_state = 0 then 10 <!-- 已取消 -->
				when afterDtl.approve_state = 0 then 1 <!-- 待处理 -->
				when afterDtl.approve_state = 1 and afterDtl.return_state = 0 then 2  <!-- 待退货 -->
				when afterDtl.approve_state = 1 and afterDtl.return_state = 1 then 3 <!-- 退货中 -->
				when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 0 then 5  <!-- 待退款 -->
				when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 1 then 6  <!-- 退款中 -->
				when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 3 then 7  <!-- 退款失败 -->
				when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 2 then 8  <!-- 处理完成 -->
				when afterDtl.approve_state = 2 then 9  <!-- 已拒绝 -->
				else 0 <!-- 问题状态 -->
			end handleState
			,SUM(afterDtl.return_qty) AS afterOrderQty
			,SUM(afterDtl.return_amt) AS afterOrderAmt
			,MAX(tAfter.reason) AS reason
			,MAX(tAfter.order_type) AS orderType
			,MAX(afterDtl.after_type) AS afterType
			,MAx(afterDtl.refund_time) AS refundTime
		FROM trd_after tAfter
		LEFT JOIN trd_supplier_after_dtl afterDtl ON tAfter.after_id = afterDtl.after_id
		<where>
			<if test="null != pageReqVO.memberId">
				AND tAfter.member_id = #{pageReqVO.memberId}
			</if>
			<if test="null != pageReqVO.branchId">
				AND tAfter.branch_id = #{pageReqVO.branchId}
			</if>
			<if test="null != pageReqVO.keyWords and pageReqVO.keyWords != '' ">
				AND (tAfter.after_no like CONCAT ('%',#{pageReqVO.keyWords},'%') OR afterDtl.spu_name like CONCAT ('%',#{pageReqVO.keyWords},'%'))
			</if>
			<if test="null != pageReqVO.startDate and pageReqVO.startDate != '' and null != pageReqVO.endDate and pageReqVO.endDate != ''">
				AND tAfter.create_time BETWEEN CONCAT(#{pageReqVO.startDate},' 00:00:00') and CONCAT(#{pageReqVO.endDate},' 23:59:59')
			</if>
			<if test="null != pageReqVO.afterId">
				AND tAfter.after_id = #{pageReqVO.afterId}
			</if>
			<if test="null != pageReqVO.orderType">
				AND tAfter.order_type = #{pageReqVO.orderType}
			</if>
			<if test="null != pageReqVO.colonelId">
		        AND tAfter.colonel_id = #{pageReqVO.colonelId}
				<!-- 售后中 -->
				AND tAfter.is_cancel = 0
				AND (
					<!-- 售后未审核 -->
					(afterDtl.approve_state = 0)
					OR
					<!-- 售后已审核但是还没退款-->
					(afterDtl.approve_state = 1 AND afterDtl.refund_state = 0)
				)
			</if>
			<if test="null != pageReqVO.refundNo and pageReqVO.refundNo != ''">
				AND afterDtl.refund_no = #{pageReqVO.refundNo}
			</if>
		</where>
		GROUP BY
			tAfter.after_id
			,tAfter.after_no
			,tAfter.create_time
			,tAfter.branch_id
			,afterDtl.after_type
			,afterDtl.after_phase
			,afterDtl.approve_state
			,afterDtl.return_state
			,afterDtl.refund_state
			,afterDtl.is_cancel
		ORDER BY tAfter.create_time DESC
	</select>

	<select id="selectMemColoneAppAfterOrderListTotal"
			resultType="com.zksr.trade.api.order.dto.ColonelAppOrderListTotalDTO">
		SELECT
			count(1) AS numTotal
			,SUM(tAfter.refund_amt) AS amtTotal
		FROM
		    trd_after tAfter
		<where>
			<if test="null != reqVO.colonelIds and reqVO.colonelIds.size > 0 ">
				AND tAfter.colonel_id IN
				<foreach collection="reqVO.colonelIds" item="colonelId" open="(" separator="," close=")">
					#{colonelId}
				</foreach>
			</if>
			<if test="null != reqVO.orderNo">
				AND tAfter.after_no LIKE concat ('%', #{reqVO.orderNo} ,'%')
			</if>
			<if test="null != reqVO.startTime and null != reqVO.endTime">
				AND tAfter.create_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
			</if>
			<if test="null != reqVO.branchIds and reqVO.branchIds.size > 0 ">
				AND tAfter.branch_id IN
				<foreach collection="reqVO.branchIds" item="branchId" open="(" separator="," close=")">
					#{branchId}
				</foreach>
			</if>
		</where>

	</select>

	<select id="selectMemColoneAppAfterOrder" resultType="com.zksr.trade.api.order.vo.TrdColonelAppOrderListRespVO">
		SELECT

			tAfter.after_id AS orderId
			,tAfter.after_no AS orderNo
			,tAfter.branch_id
			,tAfter.create_time
			,tAfter.refund_amt
		    ,tAfter.return_order_amt AS returnAmt
		    ,tAfter.return_discount_amt
			,tAfter.reason AS refundReason
		FROM
		    trd_after tAfter
-- 		LEFT JOIN trd_supplier_after_dtl afterDtl ON tAfter.after_id = afterDtl.after_id
		<where> 1=1
			<if test="null != colonelIds and colonelIds.size > 0 ">
				and tAfter.colonel_id in
				<foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
					#{colonelId}
				</foreach>
			</if>
			<if test="null != reqVO.orderNo">
				and tAfter.after_no like concat ('%', #{reqVO.orderNo} ,'%')
			</if>
			<if test="null != reqVO.startTime and null != reqVO.endTime">
				and tAfter.create_time BETWEEN #{reqVO.startTime} and #{reqVO.endTime}
			</if>
			<if test="null != reqVO.branchIds and reqVO.branchIds.size > 0 ">
				and tAfter.branch_id in
				<foreach collection="reqVO.branchIds" item="branchId" open="(" separator="," close=")">
					#{branchId}
				</foreach>
			</if>
		</where>
		ORDER BY tAfter.create_time DESC
	</select>
	<select id="getAfterDtlByDtlId" resultType="com.zksr.trade.api.order.vo.AfterDtl">
		select
		    tsad.supplier_after_dtl_id as supplierAfterDtlId,
		    tsad.supplier_after_dtl_no as supplierAfterDtlNo,
		    tsad.return_qty as returnQty,
		    tsad.return_amt as returnAmt,
			tsad.return_qty as realityReturnQty,
			tsad.return_amt as realityReturnAmt
		from trd_supplier_after_dtl tsad
		where
			tsad.after_phase = 2
			and tsad.supplier_after_id =#{request.orderNo}
			and tsad.supplier_after_dtl_id in
			<foreach collection="ids" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
	</select>
	<select id="getAfterDtlByOrderNo" resultType="com.zksr.trade.api.order.vo.AfterApproveDtlEditVO">
		SELECT
		    tsa.return_phone as returnPhone,
		    tsa.return_addr as returnAddr,
		    0 as handleState,
		    tsa.supplier_id as supplierId,
		    tsa.after_id as afterId
		FROM
		    trd_supplier_after tsa
		WHERE
		    tsa.supplier_after_no = #{orderNo}
	</select>
    <select id="selectByBrandHomeAfterData" resultType="com.zksr.trade.controller.app.vo.BrandHomePageRespVO">
		SELECT
			IFNULL(SUM(tsad.return_amt), 0) AS todayAfterAmt,
			IFNULL(COUNT(DISTINCT tsad.after_id), 0) AS todayAfter
		FROM
			trd_supplier_after_dtl tsad
			INNER JOIN trd_after tof ON tof.after_id = tsad.after_id
		WHERE
		    <!-- 是否已取消 0未取消 1已取消 -->
			tsad.is_cancel = 0
		  	<!-- 审核状态(数据字典);0待审核 1同意 2拒绝 -->
		  	AND tsad.approve_state IN (0, 1)
			AND tsad.create_time BETWEEN DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00') AND NOW()
			AND tsad.brand_id IN
			<foreach collection="brandList" item="brandId" open="(" separator="," close=")">
				#{brandId}
			</foreach>
			<if test="areaList != null">
				AND tof.area_id IN
				<foreach collection="areaList" item="areaId" open="(" separator="," close=")">
					#{areaId}
				</foreach>
			</if>
	</select>
	<select id="selectByBrandHomeSaleList"
			resultType="com.zksr.trade.controller.app.vo.BrandHomeSaleListRespVO">
		SELECT
			tsad.brand_id,
			IFNULL(SUM(tsad.return_amt), 0) AS afterAmt,
			IFNULL(COUNT(DISTINCT tsad.after_id), 0) AS afterOrder
		FROM
			trd_supplier_after_dtl tsad
			INNER JOIN trd_after tof ON tof.after_id = tsad.after_id
		WHERE
			<!-- 是否已取消 0未取消 1已取消 -->
			tsad.is_cancel = 0
			<!-- 审核状态(数据字典);0待审核 1同意 2拒绝 -->
			AND tsad.approve_state IN (0, 1)
			AND tsad.create_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
			AND tsad.brand_id IN
			<foreach collection="reqVO.brandList" item="brandId" open="(" separator="," close=")">
				#{brandId}
			</foreach>
			<if test="reqVO.areaList != null">
				AND tof.area_id IN
				<foreach collection="reqVO.areaList" item="areaId" open="(" separator="," close=")">
					#{areaId}
				</foreach>
			</if>
		GROUP BY
		    tsad.brand_id
	</select>
	<select id="selectByBrandHomeSaleInfoList"
			resultType="com.zksr.trade.controller.app.vo.BrandHomeSaleListInfoRespVO">
		SELECT
			tsad.spu_id,
			IFNULL(SUM(tsad.return_amt), 0) AS afterAmt,
			IFNULL(COUNT(DISTINCT tsad.after_id), 0) AS afterOrder
		FROM
			trd_supplier_after_dtl tsad
			INNER JOIN trd_after tof ON tof.after_id = tsad.after_id
		WHERE
			<!-- 是否已取消 0未取消 1已取消 -->
			tsad.is_cancel = 0
			<!-- 审核状态(数据字典);0待审核 1同意 2拒绝 -->
			AND tsad.approve_state IN (0, 1)
			AND tsad.create_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
			AND tsad.brand_id IN
			<foreach collection="reqVO.brandList" item="brandId" open="(" separator="," close=")">
				#{brandId}
			</foreach>
			<if test="reqVO.areaList != null">
				AND tof.area_id IN
				<foreach collection="reqVO.areaList" item="areaId" open="(" separator="," close=")">
					#{areaId}
				</foreach>
			</if>
		GROUP BY
			tsad.spu_id
	</select>

	<!-- 根据订单Id查询未完成的售后订单 -->
	<select id="selectUnfinishedAfterByOrderId" resultType="java.lang.Integer">
		SELECT
			count(1)
		FROM
			trd_after ta
			LEFT JOIN trd_supplier_after_dtl tsad ON ta.after_id = tsad.after_id
		WHERE
			ta.order_id = #{orderId}
		  	AND ta.is_cancel = 0
			AND tsad.refund_state != 2
	</select>
    <select id="getPendingRefundAmount" resultType="java.util.Map">
		SELECT
			branch_id AS branchId,
			SUM(refund_amt) AS refundAmount
		FROM
			trd_after
		WHERE
			is_cancel = 0
			AND refund_time IS NULL
			AND DATE_FORMAT(pay_time, '%Y%m') = #{currentMonthId}
			AND branch_id IN
			<foreach collection="branchIds" item="branchId" open="(" separator="," close=")">
				#{branchId}
			</foreach>
		GROUP BY
			branch_id
	</select>
	<select id="getColonelPendingRefundAmount" resultType="java.util.Map">
		SELECT
			colonel_id AS colonelId,
			SUM(refund_amt) AS refundAmount
		FROM
			trd_after
		WHERE
			is_cancel = 0
			AND refund_time IS NULL
			AND DATE_FORMAT(pay_time, '%Y%m') = #{currentMonthId}
			AND colonel_id IN
			<foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
				#{colonelId}
			</foreach>
		GROUP BY
			colonel_id
	</select>
	<select id="getHomePagesOrderAfterData"
			resultType="com.zksr.report.api.homePages.dto.HomePagesOrderAfterDataRespDTO">
		SELECT
			afterTable.sys_code,
			<if test="null != isDc and isDc != 0">
				afterTable.dc_id,
			</if>
			<if test="null != isSupplier and isSupplier != 0">
				afterTable.supplier_id,
			</if>
			SUM( afterTable.returnAmt ) AS returnAmt,
			SUM( afterTable.returnQty ) AS returnQty,
			SUM( afterTable.returnBranchQty ) AS returnBranchQty,
			SUM( afterTable.returnSkuQty ) AS returnSkuQty,
			SUM( afterTable.pendingReturnAmt ) AS pendingReturnAmt,
			SUM( afterTable.pendingReturnQty ) AS pendingReturnQty,
			SUM( afterTable.pendingeturnBranchQty ) AS pendingeturnBranchQty,
			SUM( afterTable.beforeReturnAmt ) AS beforeReturnAmt,
			SUM( afterTable.beforeReturnQty ) AS beforeReturnQty,
			SUM( afterTable.beforeReturnBranchQty ) AS beforeReturnBranchQty,
			SUM( afterTable.beforeReturnSkuQty ) AS beforeReturnSkuQty
		FROM (
			SELECT
				tar.sys_code,
				<if test="null != isDc and isDc != 0">
					tar.dc_id,
				</if>
				<if test="null != isSupplier and isSupplier != 0">
					tsad.supplier_id,
				</if>
				SUM( tsad.refund_amt ) AS returnAmt,
				COUNT( DISTINCT tar.after_id ) AS returnQty,
				COUNT( DISTINCT tar.branch_id ) AS returnBranchQty,
				COUNT( DISTINCT tsad.sku_id ) AS returnSkuQty,
				0 AS pendingReturnAmt,
				0 AS pendingReturnQty,
				0 AS pendingeturnBranchQty,
				0 AS beforeReturnAmt,
				0 AS beforeReturnQty,
				0 AS beforeReturnBranchQty,
				0 AS beforeReturnSkuQty
			FROM
				trd_after tar
				LEFT JOIN trd_supplier_after_dtl tsad ON tar.after_id = tsad.after_id
			WHERE
				tar.sys_code = #{sysCode}
				AND tsad.after_phase = 2
				AND tar.create_time BETWEEN CONCAT(#{startDate}, ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
			GROUP BY
				tar.sys_code
				<if test="null != isDc and isDc != 0">
					, tar.dc_id
				</if>
				<if test="null != isSupplier and isSupplier != 0">
					, tsad.supplier_id
				</if>

			UNION ALL

			SELECT
				tar.sys_code,
				<if test="null != isDc and isDc != 0">
					tar.dc_id,
				</if>
				<if test="null != isSupplier and isSupplier != 0">
					tsad.supplier_id,
				</if>
				0 AS returnAmt,
				0 AS returnQty,
				0 AS returnBranchQty,
				0 AS returnSkuQty,
				SUM( tsad.refund_amt ) AS pendingReturnAmt,
				COUNT( DISTINCT tar.after_id ) AS pendingReturnQty,
				COUNT( DISTINCT tar.branch_id ) AS pendingeturnBranchQty,
				0 AS beforeReturnAmt,
				0 AS beforeReturnQty,
				0 AS beforeReturnBranchQty,
				0 AS beforeReturnSkuQty
			FROM
				trd_after tar
				LEFT JOIN trd_supplier_after_dtl tsad ON tar.after_id = tsad.after_id
			WHERE
				tar.sys_code = #{sysCode}
				AND tsad.after_phase = 2
				AND tsad.approve_state != 2
				AND tsad.refund_state != 2
				AND tar.create_time BETWEEN CONCAT(#{startDate}, ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
			GROUP BY
				tar.sys_code
				<if test="null != isDc and isDc != 0">
					, tar.dc_id
				</if>
				<if test="null != isSupplier and isSupplier != 0">
					, tsad.supplier_id
				</if>

			UNION ALL

			SELECT
				tar.sys_code,
				<if test="null != isDc and isDc != 0">
					tar.dc_id,
				</if>
				<if test="null != isSupplier and isSupplier != 0">
					tsad.supplier_id,
				</if>
				0 AS returnAmt,
				0 AS returnQty,
				0 AS returnBranchQty,
				0 AS returnSkuQty,
				0 AS pendingReturnAmt,
				0 AS pendingReturnQty,
				0 AS pendingeturnBranchQty,
				SUM( tsad.refund_amt ) AS beforeReturnAmt,
				COUNT( DISTINCT tar.after_id ) AS beforeReturnQty,
				COUNT( DISTINCT tar.branch_id ) AS beforeReturnBranchQty,
				COUNT( DISTINCT tsad.sku_id ) AS beforeReturnSkuQty
			FROM
				trd_after tar
				LEFT JOIN trd_supplier_after_dtl tsad ON tar.after_id = tsad.after_id
			WHERE
				tar.sys_code = #{sysCode}
				AND tsad.after_phase = 2
				AND tar.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), ' 00:00:00.000') AND CONCAT(DATE_SUB(#{endDate}, INTERVAL 1 DAY), ' 23:59:59.999')
			GROUP BY
				tar.sys_code
				<if test="null != isDc and isDc != 0">
					, tar.dc_id
				</if>
				<if test="null != isSupplier and isSupplier != 0">
					, tsad.supplier_id
				</if>
		) afterTable
		GROUP BY
			afterTable.sys_code
			<if test="null != isDc and isDc != 0">
				, afterTable.dc_id
			</if>
			<if test="null != isSupplier and isSupplier != 0">
				, afterTable.supplier_id
			</if>
	</select>

	<!-- 业务员app售后列表 (新) -->
	<select id="selectMemColoneAppAfterOrderNew" resultMap="afterListNew" parameterType="com.zksr.trade.api.order.vo.TrdColonelAppOrderListPageReqVO">
		SELECT
			/*-- 售后主订单信息*/
			tar.after_id,
			tar.after_no,
			tar.branch_id,
			tar.create_time,
			tar.order_type,
			tar.reason,
			tar.return_order_amt,
			tar.return_discount_amt,

			/*-- 售后入驻商订单信息*/
			tsa.supplier_id,
			tsa.supplier_after_id,
			tsa.supplier_after_no,

			/*-- 售后入驻商订单明细信息*/
			tsad.supplier_after_dtl_id,
			tsad.spu_id,
			tsad.sku_id,
			tsad.return_unit_qty,
			tsad.return_unit,
			tsad.return_unit_type,
			tsad.return_sales_unit_price,
			tsad.return_amt,
			tsad.after_type,
			tsad.item_type,
			CASE
				WHEN tsad.is_cancel = 1 AND tsad.approve_state = 0 THEN 10 <!-- 已取消 -->
				WHEN tsad.approve_state = 0 then 1 <!-- 待处理 -->
				WHEN tsad.approve_state = 1 AND tsad.return_state = 0 THEN 2  <!-- 待退货 -->
				WHEN tsad.approve_state = 1 AND tsad.return_state = 1 THEN 3 <!-- 退货中 -->
				WHEN tsad.approve_state = 1 AND tsad.return_state in (2,3) AND tsad.refund_state = 0 THEN 4  <!-- 已退货 -->
				<!-- when afterDtl.approve_state = 1 and afterDtl.return_state in (2,3) and afterDtl.refund_state = 0 then 5   待退款 -->
				WHEN tsad.approve_state = 1 AND tsad.return_state in (2,3) AND tsad.refund_state = 1 THEN 6  <!-- 退款中 -->
				WHEN tsad.approve_state = 1 AND tsad.return_state in (2,3) AND tsad.refund_state = 3 THEN 7  <!-- 退款失败 -->
				WHEN tsad.approve_state = 1 AND tsad.return_state in (2,3) AND tsad.refund_state = 2 THEN 8  <!-- 处理完成 -->
				WHEN tsad.approve_state = 2 THEN 9  <!-- 已拒绝 -->
				ELSE 0 <!-- 问题状态 -->
			  END handleState
		FROM
		    trd_after tar
			INNER JOIN (
				SELECT
					tar.after_id
				FROM
					trd_after tar
				<where>
					<!-- 业务员 -->
					<if test="null != colonelIds and colonelIds.size > 0 ">
						AND tar.colonel_id IN
						<foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
							#{colonelId}
						</foreach>
					</if>
					<!-- 售后编号 -->
					<if test="null != orderNo">
						AND tar.after_no LIKE concat ('%', #{orderNo} ,'%')
					</if>
					<!-- 创建时间 -->
					<if test="null != startTime and null != endTime">
						AND tar.create_time BETWEEN #{startTime} AND #{endTime}
					</if>
					<!-- 门店编号 -->
					<if test="null != branchIds and branchIds.size > 0 ">
						AND tar.branch_id IN
						<foreach collection="branchIds" item="branchId" open="(" separator="," close=")">
							#{branchId}
						</foreach>
					</if>
					<!-- 售后订单类型 -->
					<if test="null != orderProductType">
						AND tar.order_type = #{orderProductType}
					</if>
				</where>
				ORDER BY
					tar.create_time DESC
				LIMIT #{pageNo}, #{pageSize}
			) pageAfter ON pageAfter.after_id = tar.after_id
			LEFT JOIN trd_supplier_after tsa ON tsa.after_id = tar.after_id
			LEFT JOIN trd_supplier_after_dtl tsad ON tsad.after_id = tar.after_id

	</select>

	<select id="getColonelAppPageAfterAmt" resultType="java.math.BigDecimal">
		SELECT
			SUM(tsad.return_amt) AS realSaleAmt  -- 计算实际售后金额
		FROM
			trd_after tar
				LEFT JOIN trd_supplier_after_dtl tsad ON tar.after_id = tsad.after_id
		WHERE
			tar.colonel_id = #{colonelId}
		  AND tsad.approve_state = 1
		  AND DATE(tsad.return_approve_time) = CURDATE()
		  -- AND tsad.return_approve_time BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND
	</select>

</mapper>
