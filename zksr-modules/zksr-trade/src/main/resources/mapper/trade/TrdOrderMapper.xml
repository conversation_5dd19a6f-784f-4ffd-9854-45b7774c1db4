<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdOrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- 用户小程序查询订单start -->
    <!-- 订单列表 -->
    <resultMap id="queryOrderAll" type="com.zksr.trade.api.order.dto.TrdOrderRespDTO">
        <id property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="orderType" column="orderType"/>
        <collection property="supplierOrderList" select="querySupplierOrderList"
                    column="{orderId=order_id,deliveryState=deliveryState,keyWords = keyWords,supplierOrderId = supplierOrderId}"/>
    </resultMap>

    <!-- 入驻商订单列表 -->
    <resultMap id="supplierOrderList" type="com.zksr.trade.api.order.dto.TrdSupplierOrderDTO">
        <id property="supplierOrderId" column="supplier_order_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="subAmt" column="subAmt"/>
        <result property="subNum" column="subNum"/>
        <result property="deliveryState" column="sDeliveryState"/>
        <collection property="supplierOrderDtlDTOList"
                    select="querySupplierOrderDtlList"
                    column="{supplierOrderId=supplier_order_id ,deliveryState=sDeliveryState ,keyWords = keyWords}"/>
    </resultMap>

    <resultMap id="orderList" type="com.zksr.trade.controller.order.vo.DcOrderPageRespVO">
        <id property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="deliveryState" column="delivery_state"/>
        <result property="payWay" column="pay_way"/>
        <result property="branchId" column="branch_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="spuId" column="spu_id"/>
        <result property="totalNum" column="total_num"/>
        <result property="price" column="price"/>
        <result property="totalAmt" column="total_amt"/>
        <result property="totalNum" column="send_qty"/>
        <result property="shortageNum" column="cancel_qty"/>
        <result property="discrepancyAmt" column="cancel_amt"/>
        <result property="itemType" column="item_type"/>
        <result property="supplierOrderId" column="supplier_order_id"/>
        <collection property="expressesList" select="getExpressesList" column="supplier_order_dtl_id">
            <id property="orderExpressId" column="order_express_id"/>
            <result property="expressNo" column="express_no"/>
            <result property="expressCom" column="express_com"/>
            <result property="expressComNo" column="express_com_no"/>
            <result property="receivePhone" column="receive_phone"/>
        </collection>
    </resultMap>

    <resultMap id="printOrder" type="com.zksr.trade.print.vo.PrintSupplierOrderVO">
        <id property="orderId" column="order_id"/>
        <result property="createTime" column="create_time"/>
        <result property="colonelId" column="colonel_id"/>
        <result property="branchId" column="branch_id"/>
        <result property="payWay" column="pay_way"/>
        <result property="orderNo" column="order_no"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierOrderId" column="supplier_order_id"/>
        <result property="supplierOrderNo" column="supplier_order_no"/>
        <result property="subDiscountAmt" column="sub_discount_amt"/>
        <result property="subPayAmt" column="sub_pay_amt"/>
        <result property="subOrderAmt" column="sub_order_amt"/>
        <result property="subOrderNum" column="sub_order_num"/>
        <collection property="supplierOrderDtlVOList" ofType="com.zksr.trade.print.vo.PrintSupplierOrderDtlVO">
            <id property="supplierOrderDtlId" column="supplier_order_dtl_id"/>
            <result property="spuId" column="spu_id"/>
            <result property="skuId" column="sku_id"/>
            <result property="totalAmt" column="total_amt"/>
            <result property="salePrice" column="sale_price"/>
            <result property="totalNum" column="total_num"/>
            <result property="price" column="price"/>
            <result property="memo" column="memo"/>
            <result property="orderUnit" column="order_unit"/>
            <result property="orderUnitQty" column="order_unit_qty"/>
            <result property="orderUnitSize" column="order_unit_size"/>
            <result property="orderUnitPrice" column="order_unit_price"/>
            <result property="cancelAmt" column="cancel_amt"/>
            <result property="cancelQty" column="cancel_qty"/>
        </collection>
    </resultMap>

    <!-- 订单分页查询返回Map 新-->
    <resultMap id="orderListNew" type="com.zksr.trade.api.order.dto.TrdOrderRespDTO">
        <id property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="memberId" column="member_id"/>
        <result property="branchId" column="branch_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="payState" column="pay_state"/>
        <result property="payTime" column="pay_time"/>
        <result property="payWay" column="pay_way"/>
        <result property="dcId" column="dc_id"/>
        <result property="orderType" column="order_type"/>
        <result property="colonelFlag" column="colonel_flag"/>
        <result property="colonelId" column="colonel_id"/>
        <result property="driverId" column="driver_id"/>
        <result property="driverRatingFlag" column="driver_rating_flag"/>
        <result property="driverRatingId" column="driver_rating_id"/>
        <result property="memo" column="memo"/>
        <result property="distributionMode" column="distribution_mode"/>
        <!--        <result property="payAmt" column="pay_amt"/>-->

        <!--        <result property="deliveryState" column="delivery_state"/>-->
        <collection property="supplierOrderList" ofType="com.zksr.trade.api.order.dto.TrdSupplierOrderDTO">
            <id property="supplierOrderId" column="supplier_order_id"/>
            <id property="deliveryState" column="deliveryState"/>
            <result property="supplierId" column="supplier_id"/>
            <result property="supplierName" column="supplier_name"/>
            <result property="supplierOrderNo" column="supplier_order_no"/>
            <result property="memo" column="supplier_memo"/>
            <result property="subPayBalanceAmt" column="sub_pay_balance_amt"/>
<!--            <result property="deliveryState" column="deliveryState"/>-->

            <collection property="supplierOrderDtlDTOList" ofType="com.zksr.trade.api.order.dto.TrdSupplierOrderDtlDTO">
                <id property="supplierOrderDtlId" column="supplier_order_dtl_id"/>
                <result property="spuId" column="spu_id"/>
                <result property="skuId" column="sku_id"/>
                <result property="thumbVideo" column="thumb_video"/>
                <result property="thumb" column="thumb"/>
                <result property="totalNum" column="totalNum"/>
                <result property="minTotalNum" column="minTotalNum"/>
                <result property="totalAmt" column="totalAmt"/>
                <result property="price" column="price"/>
                <result property="giftFlag" column="gift_flag"/>
                <result property="spuName" column="spu_name"/>
                <result property="itemType" column="item_type"/>
                <result property="supplierOrderDtlId" column="supplier_order_dtl_id"/>
                <result property="supplierItemId" column="supplier_item_id"/>
                <result property="areaItemId" column="area_item_id"/>
                <result property="orderUnit" column="order_unit"/>
                <result property="orderUnitType" column="order_unit_type"/>
                <result property="saleAmt" column="saleAmt"/>
                <result property="discountAmt" column="discountAmt"/>
                <result property="oldestDate" column="oldest_date"/>
                <result property="latestDate" column="latest_date"/>
                <result property="originalPrice" column="order_unit_price"/>
                <result property="isAfterSales" column="is_after_sales"/>
                <result property="afterSalesTime" column="after_sales_time"/>
            </collection>
        </collection>
    </resultMap>

    <!-- 订单分页查询返回Map-->
    <resultMap id="selectPageAllMap" type="com.zksr.trade.api.order.dto.TrdOrderMiniRespDTO">
        <id property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="memberId" column="member_id"/>
        <result property="branchId" column="branch_id"/>
        <result property="createTime" column="create_time"/>
        <result property="payState" column="pay_state"/>
        <result property="payTime" column="pay_time"/>
        <result property="payWay" column="pay_way"/>
        <result property="dcId" column="dc_id"/>
        <result property="orderType" column="order_type"/>
        <result property="colonelFlag" column="colonel_flag"/>
        <result property="colonelId" column="colonel_id"/>
        <result property="deliveryState" column="deliveryState"/>

        <collection property="supplierOrderList" ofType="com.zksr.trade.api.order.dto.TrdOrderMiniRespDTO$SupplierOrderDTO">
            <id property="supplierOrderId" column="supplier_order_id"/>
<!--            <result property="deliveryState" column="deliveryState"/>-->
            <result property="supplierId" column="supplier_id"/>
            <result property="supplierName" column="supplier_name"/>
            <result property="supplierOrderNo" column="supplier_order_no"/>
            <result property="driverId" column="driver_id"/>
            <result property="driverRatingFlag" column="driver_rating_flag"/>
            <result property="driverRatingId" column="driver_rating_id"/>
            <result property="payState" column="supplier_pay_state"/>
            <result property="deliveryState" column="supplier_delivery_State"/>

            <collection property="supplierOrderDtlDTOList" ofType="com.zksr.trade.api.order.dto.TrdOrderMiniRespDTO$SupplierOrderDtlDTO">
                <id property="supplierOrderDtlId" column="supplier_order_dtl_id"/>
                <result property="thumb" column="thumb"/>
                <result property="totalNum" column="totalNum"/>
                <result property="totalAmt" column="totalAmt"/>
                <result property="saleAmt" column="saleAmt"/>
                <result property="discountAmt" column="discountAmt"/>
            </collection>
        </collection>
    </resultMap>


    <resultMap id="getOperatorOrderPageListNewMap" type="com.zksr.trade.controller.order.vo.DcSupplierOrderPageRespVO">
        <id property="supplierOrderId" column="supplier_order_id"/>
        <id property="deliveryState" column="deliveryState"/>
        <result property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="branchId" column="branch_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierOrderNo" column="supplier_order_no"/>
        <result property="payWay" column="pay_way"/>
        <result property="orderType" column="order_type"/>
        <result property="sysCode" column="sys_code"/>
        <result property="createTime" column="create_time"/>
        <result property="colonelId" column="colonel_id"/>
        <result property="memo" column="memo"/>
        <result property="dcId" column="dc_id"/>
        <result property="supplierMemo" column="supplierMemo"/>
        <result property="pcolonelId" column="pcolonel_id"/>
        <result property="payState" column="pay_state"/>
        <result property="payTime" column="pay_time"/>
        <result property="pushStatus" column="push_status"/>
        <result property="sourceOrderNo" column="source_order_no"/>
        <result property="driverId" column="driver_id"/>
        <result property="driverRatingFlag" column="driver_rating_flag"/>
        <result property="driverRatingId" column="driver_rating_id"/>
        <result property="printState" column="print_state"/>
        <result property="printQty" column="print_qty"/>
        <result property="subPayBalanceAmt" column="sub_pay_balance_amt"/>

        <collection property="supplierOrderDtlRespVOList" ofType="com.zksr.trade.controller.order.vo.DcSupplierOrderDtlRespVO">
            <id property="supplierOrderDtlId" column="supplier_order_dtl_id"/>
            <result property="supplierOrderDtlNo" column="supplier_order_dtl_no"/>
            <result property="skuId" column="sku_id"/>
            <result property="spuId" column="spu_id"/>
            <result property="price" column="price"/>
            <result property="demandNum" column="demandNum"/>
            <result property="demandAmt" column="demandAmt"/>
            <result property="totalNum" column="totalNum"/>
            <result property="totalAmt" column="totalAmt"/>
            <result property="shortageNum" column="shortageNum"/>
            <result property="discrepancyAmt" column="discrepancyAmt"/>
            <result property="skuUnit" column="order_unit"/>
            <result property="saleTotalAmt" column="saleTotalAmt"/>
            <result property="discountAmt" column="discountAmt"/>
            <result property="giftFlag" column="gift_flag"/>
            <result property="afterReturnAmt" column="afterReturnAmt"/>
            <result property="latestDate" column="latest_date"/>
            <result property="oldestDate" column="oldest_date"/>
            <result property="originalPrice" column="order_unit_price"/>
            <result property="isAfterSales" column="is_after_sales"/>
            <result property="afterSalesTime" column="after_sales_time"/>


            <collection property="expressesList" ofType="com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressRespVO">
                <id property="orderExpressId" column="order_express_id"/>
                <result property="expressNo" column="express_no"/>
                <result property="expressCom" column="express_com"/>
                <result property="expressComNo" column="express_com_no"/>
                <result property="receivePhone" column="receive_phone"/>
                <result property="orderId" column="expressOrderId"/>
                <result property="orderNo" column="expressOrderNo"/>
                <result property="supplierOrderDtlNo" column="expressSupplierOrderDtlNo"/>
                <result property="receiveMan" column="receive_man"/>
                <result property="address" column="address"/>

            </collection>
        </collection>
    </resultMap>

    <resultMap id="OrderWithDetailsResultMap" type="com.zksr.common.core.domain.vo.openapi.syncCall.SyncOrderCallDTO">
        <id property="supplierOrderId" column="supplier_order_id" />
        <result property="supplierOrderNo" column="supplier_order_no" />
        <result property="orderId" column="order_id" />
        <result property="orderNo" column="order_no" />
        <result property="branchId" column="branch_id" />
        <result property="transAmt" column="trans_amt" />
        <result property="supplierId" column="supplier_id" />
        <result property="supplierName" column="supplier_name" />
        <result property="memo" column="memo" />
        <result property="subDiscountAmt" column="sub_discount_amt" />
        <result property="subPayAmt" column="sub_pay_amt" />
        <result property="subOrderAmt" column="sub_order_amt" />
        <result property="subOrderNum" column="sub_order_num" />
        <result property="sourceOrderNo" column="source_order_no" />
        <result property="payTime" column="pay_time" />
        <result property="payWay" column="pay_way" />
        <result property="orderType" column="order_type" />
        <result property="colonelId" column="colonel_id" />
        <result property="colonelFlag" column="colonel_flag" />
        <result property="payState" column="pay_state" />
        <result property="pushStatus" column="push_status" />
        <collection property="detailList" ofType="com.zksr.common.core.domain.vo.openapi.syncCall.SyncOrderDetailCallDTO">
            <id property="supplierOrderDtlId" column="supplier_order_dtl_id" />
            <result property="supplierOrderDtlNo" column="supplier_order_dtl_no" />
            <result property="skuId" column="sku_id" />
            <result property="spuName" column="spu_name" />
            <result property="thumb" column="thumb" />
            <result property="thumbVideo" column="thumb_video" />
            <result property="details" column="details" />
            <result property="deliveryState" column="deliveryState" />
            <result property="totalNum" column="totalNum" />
            <result property="totalAmt" column="totalAmt" />
            <result property="price" column="price" />
            <result property="giftFlag" column="gift_flag" />
            <result property="memo" column="memo" />
            <result property="specName" column="spec_name" />
            <result property="itemType" column="item_type" />
            <result property="lineNum" column="line_num" />
            <result property="supplierItemId" column="supplier_item_id" />
            <result property="areaItemId" column="area_item_id" />
            <result property="orderUnit" column="order_unit" />
            <result property="orderUnitType" column="order_unit_type" />
            <result property="orderUnitQty" column="order_unit_qty" />
            <result property="orderUnitSize" column="order_unit_size" />
            <result property="orderUnitPrice" column="order_unit_price" />
            <result property="couponDiscountAmt" column="coupon_discount_amt" />
            <result property="couponDiscountAmt2" column="coupon_discount_amt2" />
            <result property="activityDiscountAmt" column="activity_discount_amt" />
            <result property="brandId" column="brand_id" />
            <result property="isProceeds" column="is_proceeds" />
            <result property="hdfkPayId" column="hdfk_pay_id" />
            <result property="syncStock" column="sync_stock" />
            <result property="categoryId" column="category_id" />
            <result property="exactPrice" column="exact_price" />
            <result property="exactTotalAmt" column="exact_total_amt" />
            <result property="salePrice" column="sale_price" />
        </collection>
    </resultMap>


    <resultMap id="AfterOrderWithDetailsResultMap" type="com.zksr.common.core.domain.vo.openapi.syncCall.SyncAfterOrderCallDTO">
        <id property="supplierAfterId" column="supplier_after_id" />
        <result property="sysCode" column="sys_code" />
        <result property="supplierAfterNo" column="supplier_after_no" />
        <result property="supplierOrderNo" column="supplier_order_no" />
        <result property="orderId" column="order_id" />
        <result property="orderNo" column="order_no" />
        <result property="afterId" column="after_id" />
        <result property="afterNo" column="after_no" />
        <result property="transAmt" column="trans_amt" />
        <result property="supplierId" column="supplier_id" />
        <result property="subDiscountAmt" column="sub_discount_amt" />
        <result property="subRefundAmt" column="sub_refund_amt" />
        <result property="payRate" column="pay_rate" />
        <result property="subRefundFee" column="sub_refund_fee" />
        <result property="returnPhone" column="return_phone" />
        <result property="returnAddr" column="return_addr" />
        <result property="sourceOrderNo" column="source_order_no" />
        <result property="transNo" column="trans_no" />
        <result property="branchId" column="branch_id" />
        <result property="colonelId" column="colonel_id" />
        <result property="createTime" column="create_time" />


        <collection property="detailList" ofType="com.zksr.common.core.domain.vo.openapi.syncCall.SyncAfterOrderDetailCallDTO">
            <id property="supplierAfterDtlId" column="supplier_after_dtl_id" />
            <result property="sysCode" column="sys_code" />
            <result property="afterNo" column="after_no" />
            <result property="afterId" column="after_id" />
            <result property="supplierAfterDtlNo" column="supplier_after_dtl_no" />
            <result property="supplierAfterId" column="supplier_after_id" />
            <result property="supplierAfterNo" column="supplier_after_no" />
            <result property="supplierOrderDtlId" column="supplier_order_dtl_id" />
            <result property="supplierOrderDtlNo" column="supplier_order_dtl_no" />
            <result property="supplierId" column="supplier_id" />
            <result property="itemType" column="item_type" />
            <result property="itemId" column="item_id" />
            <result property="skuId" column="sku_id" />
            <result property="reason" column="reason" />
            <result property="returnQty" column="return_qty" />
            <result property="returnPrice" column="return_price" />
            <result property="returnAmt" column="return_amt" />
            <result property="refundAmt" column="refund_amt" />
            <result property="descr" column="descr" />
            <result property="afterType" column="after_type" />
            <result property="afterPhase" column="after_phase" />
            <result property="refundType" column="refund_type" />
            <result property="approveState" column="approve_state" />
            <result property="returnState" column="return_state" />
            <result property="refundState" column="refund_state" />
            <result property="refundFailReason" column="refund_fail_reason" />
            <result property="orderdtlRefundAmt" column="orderdtl_refund_amt" />
            <result property="payway" column="payway" />
            <result property="platform" column="platform" />
            <result property="payTime" column="pay_time" />
            <result property="finishTime" column="finish_time" />
            <result property="payState" column="pay_state" />
            <result property="returnTime" column="return_time" />
            <result property="refundTime" column="refund_time" />
            <result property="isCancel" column="is_cancel" />
            <result property="source" column="source" />
            <result property="syncFlag" column="sync_flag" />
            <result property="memo" column="memo" />
            <result property="refundNo" column="refund_no" />
            <result property="exactReturnPrice" column="exact_return_price" />
            <result property="exactReturnAmt" column="exact_return_amt" />
            <result property="returnCouponDiscountAmt" column="return_coupon_discount_amt" />
            <result property="returnCouponDiscountAmt2" column="return_coupon_discount_amt2" />
            <result property="returnActivityDiscountAmt" column="return_activity_discount_amt" />
            <result property="giftFlag" column="gift_flag" />
            <result property="lineNum" column="line_num" />
            <result property="orderUnit" column="order_unit" />
            <result property="orderUnitType" column="order_unit_type" />
            <result property="orderUnitQty" column="order_unit_qty" />
            <result property="orderUnitSize" column="order_unit_size" />
            <result property="returnUnit" column="return_unit" />
            <result property="returnUnitType" column="return_unit_type" />
            <result property="returnUnitQty" column="return_unit_qty" />
            <result property="returnUnitSize" column="return_unit_size" />
        </collection>
    </resultMap>


    <!-- 主订单 -->
    <select id="selectPageAll" resultMap="selectPageAllMap">
        SELECT
            /* -- 主订单数据*/
            tor.order_id
            ,tor.order_no
            ,tor.member_id
            ,tor.branch_id
            ,tor.create_time
            ,tor.pay_state
            ,tor.pay_time
            ,tor.pay_way
            ,tor.dc_id
            ,tor.order_type
            ,tor.colonel_flag
            ,tor.colonel_id
            ,CASE tsod.delivery_state
                WHEN '40' THEN 4
                WHEN '41' THEN 4
                WHEN '7'  THEN 3
                ELSE tsod.delivery_state
            END AS deliveryState

            /*-- 入驻商订单数据*/
            ,tso.driver_id
            ,tso.driver_rating_flag
            ,tso.driver_rating_id
            ,tso.supplier_id
            ,tso.supplier_name
            ,tso.supplier_order_id
            ,tso.supplier_order_no

            /*-- 入驻商订单明细信息*/
            ,tsod.supplier_order_dtl_id
            ,tsod.thumb
            ,CASE tsod.delivery_state
                WHEN 2 THEN IFNULL((tsod.total_num / tsod.order_unit_size), 0)
                ELSE  IFNULL(((tsod.total_num - tsod.cancel_qty) / tsod.order_unit_size), 0)
            END AS totalNum
            ,CASE tsod.delivery_state
                WHEN 2 THEN tsod.total_amt
                ELSE tsod.total_amt - tsod.cancel_amt
            END AS totalAmt
            ,IFNULL(tsod.sub_order_amt, 0) AS saleAmt
            ,IFNULL((tsod.coupon_discount_amt + tsod.coupon_discount_amt2 + tsod.activity_discount_amt), 0) AS discountAmt
        FROM
            trd_order tor
            INNER JOIN (
                SELECT
                    tor.order_id
                    ,tsod.delivery_state
                FROM
                    trd_order tor
                    LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
                    <where>
                        <if test="null != orderPageReqVO.colonelId">
                            AND tor.colonel_id = #{orderPageReqVO.colonelId}
                        </if>
                        <if test="null != orderPageReqVO.branchId">
                            AND tor.branch_id = #{orderPageReqVO.branchId}
                        </if>
                        <if test="null != orderPageReqVO.memberId">
                            AND tor.member_id = #{orderPageReqVO.memberId}
                        </if>
                        <if test=" null != orderPageReqVO.deliveryState and 100 != orderPageReqVO.deliveryState">
                            <choose>
                                <when test="0 != orderPageReqVO.deliveryState and 1 != orderPageReqVO.deliveryState and 2 != orderPageReqVO.deliveryState and 4 != orderPageReqVO.deliveryState and 3 != orderPageReqVO.deliveryState">
                                    AND tsod.delivery_state = #{orderPageReqVO.deliveryState}
                                </when>
                                <when test="4 == orderPageReqVO.deliveryState"> <!-- 待收货包含（待装车、已装车状态），故需要单独处理 -->
                                    AND tsod.delivery_state in (#{orderPageReqVO.deliveryState},'40','41')
                                </when>
                                <when test="3 == orderPageReqVO.deliveryState and null == orderPageReqVO.orderDeliveryOvertimeFlag"> <!-- 待发货包含（备货中状态），故需要单独处理 -->
                                    AND tsod.delivery_state in (#{orderPageReqVO.deliveryState},'7')
                                </when>
                                <when test="2 == orderPageReqVO.deliveryState"> <!-- 取消（包含未付款取消） 故需要单独处理 -->
                                    AND (tsod.delivery_state = #{orderPageReqVO.deliveryState} or tor.pay_state = #{orderPageReqVO.deliveryState})
                                </when>
                                <when test="3 == orderPageReqVO.deliveryState and 1 == orderPageReqVO.orderDeliveryOvertimeFlag"> <!-- 超时未发货订单标志 默认超过三天未发货为异常订单-->
                                    AND tsod.delivery_state in (#{orderPageReqVO.deliveryState},'7') AND tor.pay_time &lt;= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
                                </when>
                                <otherwise>
                                    AND tor.pay_state = #{orderPageReqVO.deliveryState}
                                </otherwise>
                            </choose>
                        </if>
                        <if test=" null != orderPageReqVO.keyWords and '' != orderPageReqVO.keyWords">
                            AND (tor.order_no LIKE CONCAT ('%',#{orderPageReqVO.keyWords},'%') OR tsod.spu_name LIKE CONCAT ('%',#{orderPageReqVO.keyWords},'%') )
                        </if>
                        <if test=" null != orderPageReqVO.orderId">  <!-- TODO 页面点击主单号查询详情使用，后期可能需要根据需求业务来对列表和明细进行SQL拆分 -->
                            AND tor.order_id = #{orderPageReqVO.orderId}
                        </if>
                        <if test=" null != orderPageReqVO.supplierOrderId">  <!-- 入驻商订单ID -->
                            AND tsod.supplier_order_id = #{orderPageReqVO.supplierOrderId}
                        </if>
                        <if test=" null != orderPageReqVO.supplierOrderNo">  <!-- 入驻商订单编号 -->
                            AND tsod.supplier_order_no = #{orderPageReqVO.supplierOrderNo}
                        </if>
                        <if test=" null != orderPageReqVO.orderType">  <!-- 订单类型 订单类型：0 全国订单 1：本地订单 -->
                            AND tor.order_type = #{orderPageReqVO.orderType}
                        </if>
                        <if test="null != orderPageReqVO.startTime and null != orderPageReqVO.endTime">
                            AND tor.create_time BETWEEN #{orderPageReqVO.startTime} and #{orderPageReqVO.endTime}
                        </if>
                    </where>
                GROUP BY
                    tor.order_id
                    ,tsod.delivery_state
                ORDER BY MAX(tor.create_time) DESC
                LIMIT #{orderPageReqVO.pageNo}, #{orderPageReqVO.pageSize}
            ) pageOrder ON pageOrder.order_id = tor.order_id
            INNER JOIN trd_supplier_order tso ON tor.order_id = tso.order_id
            <if test=" null != orderPageReqVO.supplierOrderId">  <!-- 入驻商订单ID -->
                AND tso.supplier_order_id = #{orderPageReqVO.supplierOrderId}
            </if>
            <if test=" null != orderPageReqVO.supplierOrderNo">  <!-- 入驻商订单编号 -->
                AND tso.supplier_order_no = #{orderPageReqVO.supplierOrderNo}
            </if>
            INNER JOIN trd_supplier_order_dtl tsod ON tsod.supplier_order_id = tso.supplier_order_id AND pageOrder.delivery_state = tsod.delivery_state
        ORDER BY tor.create_time DESC
    </select>

    <select id="selectPageAllNew2" resultMap="selectPageAllMap">
        SELECT
        /* -- 主订单数据*/
        tor.order_id
        ,tor.order_no
        ,tor.member_id
        ,tor.branch_id
        ,tor.create_time
        ,tor.pay_state
        ,tor.pay_time
        ,tor.pay_way
        ,tor.dc_id
        ,tor.order_type
        ,tor.colonel_flag
        ,tor.colonel_id
        ,tso.delivery_state AS deliveryState

        /*-- 入驻商订单数据*/
        ,tso.driver_id
        ,tso.driver_rating_flag
        ,tso.driver_rating_id
        ,tso.supplier_id
        ,tso.supplier_name
        ,tso.supplier_order_id
        ,tso.supplier_order_no
        ,tso.pay_state as supplier_pay_state
        ,tso.delivery_state AS supplier_delivery_State
        /*-- 入驻商订单明细信息*/
        ,tsod.supplier_order_dtl_id
        ,tsod.thumb
        ,CASE tso.delivery_state
        WHEN 50 THEN IFNULL((tsod.total_num / tsod.order_unit_size), 0)
        ELSE  IFNULL(((tsod.total_num - tsod.cancel_qty) / tsod.order_unit_size), 0)
        END AS totalNum
        ,CASE tso.delivery_state
        WHEN 50 THEN tsod.total_amt
        ELSE tsod.total_amt - tsod.cancel_amt
        END AS totalAmt
        ,IFNULL(tsod.sub_order_amt, 0) AS saleAmt
        ,IFNULL((tsod.coupon_discount_amt + tsod.coupon_discount_amt2 + tsod.activity_discount_amt), 0) AS discountAmt
        FROM
        trd_order tor

        INNER JOIN trd_supplier_order tso ON tor.order_id = tso.order_id
        INNER JOIN trd_supplier_order_dtl tsod ON tsod.supplier_order_id = tso.supplier_order_id
        <where>
            <if test="null != orderPageReqVO.colonelId">
                AND tor.colonel_id = #{orderPageReqVO.colonelId}
            </if>
            <if test="null != orderPageReqVO.branchId">
                AND tor.branch_id = #{orderPageReqVO.branchId}
            </if>
            <if test="null != orderPageReqVO.memberId">
                AND tor.member_id = #{orderPageReqVO.memberId}
            </if>
            <if test=" null != orderPageReqVO.deliveryState and 100 != orderPageReqVO.deliveryState">
                AND tso.delivery_state = #{orderPageReqVO.deliveryState}
            </if>
            <if test=" null != orderPageReqVO.keyWords and '' != orderPageReqVO.keyWords">
                AND (tor.order_no LIKE CONCAT ('%',#{orderPageReqVO.keyWords},'%') )
            </if>
            <if test=" null != orderPageReqVO.orderId">  <!-- TODO 页面点击主单号查询详情使用，后期可能需要根据需求业务来对列表和明细进行SQL拆分 -->
                AND tor.order_id = #{orderPageReqVO.orderId}
            </if>
            <if test=" null != orderPageReqVO.supplierOrderId">  <!-- 入驻商订单ID -->
                AND tso.supplier_order_id = #{orderPageReqVO.supplierOrderId}
            </if>
            <if test=" null != orderPageReqVO.supplierOrderNo">  <!-- 入驻商订单编号 -->
                AND tso.supplier_order_no = #{orderPageReqVO.supplierOrderNo}
            </if>
            <if test=" null != orderPageReqVO.orderType">  <!-- 订单类型 订单类型：0 全国订单 1：本地订单 -->
                AND tor.order_type = #{orderPageReqVO.orderType}
            </if>
            <if test="null != orderPageReqVO.startTime and null != orderPageReqVO.endTime">
                AND tor.create_time BETWEEN #{orderPageReqVO.startTime} and #{orderPageReqVO.endTime}
            </if>
        </where>

        ORDER BY tor.create_time DESC
    </select>

    <select id="selectPageAllNew3" resultType="com.zksr.trade.api.order.dto.TrdOrderMiniHeadRespDTO">
        SELECT
        /* -- 主订单数据*/
        tor.order_id,
        tor.order_no,
        tor.member_id,
        tor.branch_id,
        tor.create_time,
        tor.pay_state,
        tor.pay_time,
        tor.pay_way,
        tor.dc_id,
        tor.order_type,
        tor.colonel_flag,
        tor.colonel_id
        FROM
        trd_order tor
        <where>
            <if test="null != orderPageReqVO.colonelId">
                AND tor.colonel_id = #{orderPageReqVO.colonelId}
            </if>
            <if test="null != orderPageReqVO.branchId">
                AND tor.branch_id = #{orderPageReqVO.branchId}
            </if>
            <if test="null != orderPageReqVO.memberId">
                AND tor.member_id = #{orderPageReqVO.memberId}
            </if>
            <if test="null != orderPageReqVO.deliveryState and 100 != orderPageReqVO.deliveryState">
                AND EXISTS (
                SELECT 1 FROM trd_supplier_order tso
                WHERE tor.order_id = tso.order_id
                AND tso.delivery_state = #{orderPageReqVO.deliveryState}
                )
            </if>
            <if test="null != orderPageReqVO.keyWords and '' != orderPageReqVO.keyWords">
                AND (tor.order_no LIKE CONCAT ('%',#{orderPageReqVO.keyWords},'%')
                OR EXISTS (
                SELECT 1 FROM trd_supplier_order_dtl tsod
                WHERE tor.order_id = tsod.order_id
                AND tsod.spu_name LIKE CONCAT ('%',#{orderPageReqVO.keyWords},'%')
                )
                )
            </if>
            <if test="null != orderPageReqVO.orderId">
                AND tor.order_id = #{orderPageReqVO.orderId}
            </if>
            <if test="null != orderPageReqVO.supplierOrderId">
                AND EXISTS (
                SELECT 1 FROM trd_supplier_order tso
                WHERE tor.order_id = tso.order_id
                AND tso.supplier_order_id = #{orderPageReqVO.supplierOrderId}
                )
            </if>
            <if test="null != orderPageReqVO.supplierOrderNo">
                AND EXISTS (
                SELECT 1 FROM trd_supplier_order tso
                WHERE tor.order_id = tso.order_id
                AND tso.supplier_order_no = #{orderPageReqVO.supplierOrderNo}
                )
            </if>
            <if test="null != orderPageReqVO.orderType">
                AND tor.order_type = #{orderPageReqVO.orderType}
            </if>
            <if test="null != orderPageReqVO.startTime and null != orderPageReqVO.endTime">
                AND tor.create_time BETWEEN #{orderPageReqVO.startTime} and #{orderPageReqVO.endTime}
            </if>
        </where>
        ORDER BY tor.create_time DESC
    </select>

    <!-- 入驻商订单 -->
    <select id="querySupplierOrderList" resultMap="supplierOrderList">
        SELECT
            a.supplier_id
            ,a.supplier_name
            ,a.supplier_order_id
            ,a.supplier_order_no
            ,a.sDeliveryState
            ,CASE a.sDeliveryState
                WHEN 2 THEN a.totalNum
                ELSE a.totalNum - a.cancelQty
            END AS subNum
            ,CASE a.sDeliveryState
                WHEN 2 THEN a.totalAmt
                ELSE a.totalAmt - a.cancelAmt
            END AS subAmt
            ,CASE
                WHEN '${keyWords}' IS NULL then NULL
                ELSE '${keyWords}'
            END AS keyWords
        FROM (
            SELECT
                sOrder.supplier_order_id
                ,sOrder.supplier_order_no
                ,sOrder.supplier_id
                ,sOrder.supplier_name
                ,CASE sOrderDtl.delivery_state
                    WHEN '40' THEN 4
                    WHEN '41' THEN 4
                    WHEN '7'  THEN 3
                    ELSE sOrderDtl.delivery_state
                END AS sDeliveryState
                ,SUM(sOrderDtl.total_num / sOrderDtl.order_unit_size) AS totalNum
                ,SUM(sOrderDtl.cancel_qty / sOrderDtl.order_unit_size) AS cancelQty
                ,SUM(sOrderDtl.total_amt) AS totalAmt
                ,SUM(sOrderDtl.cancel_amt) AS cancelAmt
            FROM
                trd_supplier_order sOrder
            LEFT JOIN trd_supplier_order_dtl sOrderDtl ON sOrder.supplier_order_id = sOrderDtl.supplier_order_id
            WHERE
                sOrder.order_id = #{orderId}
                <if test=" null != deliveryState and 100 != deliveryState and '' != deliveryState">
                    <choose>
                        <when test="4 == deliveryState">
                            AND sOrderDtl.delivery_state in (#{deliveryState},'40','41')
                        </when>
                        <when test="3 == deliveryState">
                            AND sOrderDtl.delivery_state in (#{deliveryState},'7')
                        </when>
                        <otherwise>
                            AND sOrderDtl.delivery_state = #{deliveryState}
                        </otherwise>
                    </choose>
                </if>
                <if test=" null != keyWords and '' != keyWords">
                    AND (sOrder.order_no LIKE CONCAT ('%',#{keyWords},'%') OR sOrderDtl.spu_name LIKE CONCAT ('%',#{keyWords},'%') )
                </if>
                <if test=" null != supplierOrderId and '' != supplierOrderId">  <!-- 入驻商订单ID -->
                    AND sOrderDtl.supplier_order_id = #{supplierOrderId}
                </if>
            GROUP BY sOrder.supplier_order_id,sOrder.supplier_order_no,sOrder.supplier_id,sOrder.supplier_name,sDeliveryState
        ) a
    </select>

    <!-- 入驻商订单明细 -->
    <select id = "querySupplierOrderDtlList" resultType="com.zksr.trade.api.order.dto.TrdSupplierOrderDtlDTO">
        SELECT
            sOrderDtl.spu_id
            ,sOrderDtl.sku_id
            ,sOrderDtl.thumb_video
            ,sOrderDtl.thumb
            ,CASE sOrderDtl.delivery_state
                WHEN 2 THEN sOrderDtl.total_num / sOrderDtl.order_unit_size
                ELSE (sOrderDtl.total_num - sOrderDtl.cancel_qty) / sOrderDtl.order_unit_size
             END AS totalNum
            ,CASE sOrderDtl.delivery_state
                WHEN 2 THEN sOrderDtl.total_amt
                ELSE (sOrderDtl.total_amt - sOrderDtl.cancel_amt)
             END AS totalAmt
            ,(sOrderDtl.price * sOrderDtl.order_unit_size) AS price
            ,sOrderDtl.discount_amt
            ,sOrderDtl.gift_flag
            ,sOrderDtl.discount_amt2
            ,sOrderDtl.spu_name
            ,sOrderDtl.spec_name
            ,sOrderDtl.item_type
            ,sOrderDtl.supplier_order_dtl_id
            ,sOrderDtl.supplier_item_id
            ,sOrderDtl.area_item_id
            ,sOrderDtl.order_unit
            ,sOrderDtl.order_unit_type
        FROM trd_supplier_order_dtl sOrderDtl
        LEFT JOIN trd_order torder ON sOrderDtl.order_id = torder.order_id
        WHERE
            sOrderDtl.supplier_order_id = #{supplierOrderId}
            <choose>
                <when test="4 == deliveryState">
                    AND sOrderDtl.delivery_state in (#{deliveryState},'40','41')
                </when>
                <when test="3 == deliveryState">
                    AND sOrderDtl.delivery_state in (#{deliveryState},'7')
                </when>
                <otherwise>
                    AND sOrderDtl.delivery_state = #{deliveryState}
                </otherwise>
            </choose>
<!--            <if test=" null != keyWords and '' != keyWords">-->
<!--                AND (torder.order_no LIKE CONCAT ('%',#{keyWords},'%') OR sOrderDtl.spu_name LIKE CONCAT ('%',#{keyWords},'%') )-->
<!--            </if>-->
    </select>


    <select id="getMerchantMiniProgramOrderPageList"
            resultType="com.zksr.trade.controller.order.vo.MiniProgramRespVO">
        SELECT
            tor.order_id,
            tor.branch_id,
            tor.branch_addr,
            SUM(tsord.total_amt - tsord.cancel_amt) + SUM(tsord.activity_discount_amt+tsord.coupon_discount_amt+tsord.coupon_discount_amt2) as totalAmt,
            SUM((tsord.total_num - tsord.cancel_qty)/tsord.order_unit_size)as totalNum,
            SUM(tsord.activity_discount_amt+tsord.coupon_discount_amt+tsord.coupon_discount_amt2) as discountAmt,
            tor.pay_state,
            SUM(tsord.total_amt - tsord.cancel_amt) as payAmt,
            tor.create_time,
            tor.order_no,
        tor.memo
        FROM
            trd_order tor
            INNER JOIN trd_supplier_order_dtl tsord ON tor.order_id = tsord.order_id
        WHERE
            tsord.supplier_id = #{supplierId}
            AND tsord.item_type = '1'
            <if test="null != orderPageReqVO.orderNo">
                and tor.order_no like concat ('%', #{orderPageReqVO.orderNo} ,'%')
            </if>
            <if test="null != orderPageReqVO.deliveryState and orderPageReqVO.deliveryState != 5 ">
                and tsord.delivery_state = #{orderPageReqVO.deliveryState}
            </if>
            <if test="null != orderPageReqVO.deliveryState and orderPageReqVO.deliveryState == 5 ">
                and ( tsord.delivery_state = #{orderPageReqVO.deliveryState} or tsord.delivery_state = 6 )
            </if>
            <if test="null != orderPageReqVO.startTime and null != orderPageReqVO.endTime">
                and tor.create_time BETWEEN #{orderPageReqVO.startTime} and #{orderPageReqVO.endTime}
            </if>
        GROUP BY
            tor.order_id,
            tor.branch_id,
            tor.branch_addr,
            tor.pay_state,
            tor.create_time,
            tor.order_no,
            tor.memo
        order by tor.create_time desc
    </select>
    <!-- 用户小程序查询订单END -->

    <select id="getOperatorOrderPageList" resultMap="orderList">
        SELECT
            tor.order_id,
            tor.order_no,
            tsord.delivery_state,
            tor.pay_way,
            tor.branch_id,
            tsord.supplier_order_dtl_id,
            tsord.supplier_id,
            tsord.spu_id,
            tsord.sku_id,
            tsord.price,
            tsord.total_num,
            tsord.total_amt,
            tsord.send_qty,
            tsord.cancel_qty,
            tsord.cancel_amt,
            tsord.item_type,
            tsord.supplier_order_no,
            tsord.supplier_order_id
        FROM
            trd_order tor
                INNER JOIN trd_supplier_order_dtl tsord ON tor.order_id = tsord.order_id
        <where>
            <if test="null != param.itemType">
                and tsord.item_type = #{param.itemType}
            </if>

            <if test="null != supplierIds and supplierIds.size > 0 ">
                and tsord.supplier_id in
                <foreach collection="supplierIds" item="supplierId" open="(" separator="," close=")">
                    #{supplierId}
                </foreach>
            </if>

            <if test="null != spuIds and spuIds.size > 0 ">
                and tsord.spu_id in
                <foreach collection="spuIds" item="spuId" open="(" separator="," close=")">
                    #{spuId}
                </foreach>
            </if>

            <if test="null != branchIds and branchIds.size > 0 ">
                and tor.branch_id in
                <foreach collection="branchIds" item="branchId" open="(" separator="," close=")">
                    #{branchId}
                </foreach>
            </if>

            <if test="null != skuIds and skuIds.size > 0 ">
                and tsord.sku_id in
                <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>

            <if test="null != colonelIds and colonelIds.size > 0 ">
                and tor.colonel_id in
                <foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>

            <if test="null != param.orderNo">
                and tor.order_no like concat('%', #{param.orderNo}, '%')
            </if>
            <if test="null != param.subOrderNo">
                and tsord.supplier_order_no like concat('%', #{param.subOrderNo}, '%')
            </if>
            <if test="null != param.startTime and null != param.endTime ">
                <if test="null != param.dateType and param.dateType == 1">
                    and tor.create_time
                </if>
                <if test="null != param.dateType and param.dateType == 2">
                    and tor.pay_time
                </if>
                BETWEEN #{param.startTime} and #{param.endTime}
            </if>
            <if test="null != param.orderType and param.orderType != 100">
                <if test="param.orderType == 0 or param.orderType == 2"> <!-- 待付款、订单取消 -->
                    and tor.pay_state = #{param.orderType}
                </if>
                <if test="param.orderType == 3"> <!-- 代发货包含（备货中 7）-->
                    and tsord.delivery_state in(#{param.orderType}, '7')
                </if>
                <if test="param.orderType == 4"> <!-- 已发货包含（待装车 40、已装车 41） -->
                    and (tsord.delivery_state = #{param.orderType} or tsord.delivery_state = 40 or tsord.delivery_state = 41)
                </if>
                <if test="param.orderType == 5"> <!-- 交易完成包含（订单收货 5 ，订单已完成 6 ） -->
                    and tsord.delivery_state in (#{param.orderType}, '6')
                </if>
            </if>
        </where>
        order by tsord.create_time desc
    </select>


    <!-- 获取运营商订单管理列表总数（新） -->
    <select id="selectOrderPageListNewCount" resultType="java.lang.Long">
        SELECT
            COUNT(1) AS total
        FROM (
            SELECT
                tsord.supplier_order_id
                ,tsord.delivery_state
            FROM
                trd_order tor
                INNER JOIN trd_supplier_order_dtl tsord ON tor.order_id = tsord.order_id
                INNER JOIN trd_supplier_order tso ON tso.order_id = tor.order_id
            <where>
                <!-- 单据类型 -->
                <if test="null != param.itemType">
                    AND tor.order_type = #{param.itemType}
                </if>
                <!-- 入驻商 -->
                <if test="null != param.supplierIdList and param.supplierIdList.size > 0 ">
                    AND tsord.supplier_id IN
                    <foreach collection="param.supplierIdList" item="supplierId" open="(" separator="," close=")">
                        #{supplierId}
                    </foreach>
                </if>
                <!-- 商品SPU -->
                <if test="null != spuIds and spuIds.size > 0 ">
                    AND tsord.spu_id IN
                    <foreach collection="spuIds" item="spuId" open="(" separator="," close=")">
                        #{spuId}
                    </foreach>
                </if>
                <!-- 门店 -->
                <if test="null != param.branchIds and param.branchIds.size > 0 ">
                    AND tor.branch_id IN
                    <foreach collection="param.branchIds" item="branchId" open="(" separator="," close=")">
                        #{branchId}
                    </foreach>
                </if>
                <!-- 商品SKU -->
                <if test="null != skuIds and skuIds.size > 0 ">
                    AND tsord.sku_id IN
                    <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                        #{skuId}
                    </foreach>
                </if>
                <!-- 业务员 -->
                <if test="null != param.colonelIds and param.colonelIds.size > 0 ">
                    AND tor.colonel_id IN
                    <foreach collection="param.colonelIds" item="colonelId" open="(" separator="," close=")">
                        #{colonelId}
                    </foreach>
                </if>
                <!-- 订单号 -->
                <if test="null != param.orderNo and param.orderNo != '' ">
                    AND tor.order_no LIKE concat('%', #{param.orderNo}, '%')
                </if>
                <!-- 入驻商订单号 -->
                <if test="null != param.subOrderNo and param.subOrderNo != '' ">
                    AND tsord.supplier_order_no LIKE concat('%', #{param.subOrderNo}, '%')
                </if>
                <!-- 查询时间  创建时间或支付时间 -->
                <if test="null != param.startTime and null != param.endTime ">
                    AND tor.create_time
                    BETWEEN #{param.startTime} AND #{param.endTime}
                </if>
                <if test="null != param.payStartTime and null != param.payEndTime ">
                    AND tor.pay_time
                    BETWEEN #{param.payStartTime} AND #{param.payEndTime}
                </if>
                <!-- 订单状态 -->
<!--                <if test="null != param.orderType and param.orderType != 100">-->
<!--                    <if test="param.orderType == 0"> &lt;!&ndash; 待付款&ndash;&gt;-->
<!--                        AND tor.pay_state = #{param.orderType}-->
<!--                    </if>-->
<!--                    <if test="param.orderType == 2"> &lt;!&ndash; 订单取消 &ndash;&gt;-->
<!--                        AND (tor.pay_state = #{param.orderType} OR tsord.delivery_state = #{param.orderType})-->
<!--                    </if>-->
<!--                    <if test="param.orderType == 3"> &lt;!&ndash; 代发货包含（备货中 7）&ndash;&gt;-->
<!--                        AND tsord.delivery_state IN (#{param.orderType}, '7')-->
<!--                    </if>-->
<!--                    <if test="param.orderType == 4"> &lt;!&ndash; 已发货包含（待装车 40、已装车 41） &ndash;&gt;-->
<!--                        AND tsord.delivery_state IN (#{param.orderType} , '40', '41')-->
<!--                    </if>-->
<!--                    <if test="param.orderType == 5"> &lt;!&ndash; 订单收货 5 &ndash;&gt;-->
<!--                        AND tsord.delivery_state = #{param.orderType}-->
<!--                    </if>-->
<!--                    <if test="param.orderType == 6"> &lt;!&ndash; 订单已完成 6  &ndash;&gt;-->
<!--                        AND tsord.delivery_state = #{param.orderType}-->
<!--                    </if>-->
<!--                    <if test="param.orderType == 99"> &lt;!&ndash; 订单发货超时（3，7）&ndash;&gt;-->
<!--                        AND tsord.delivery_state IN ('3', '7')  AND tor.pay_time &lt;= DATE_ADD(CURDATE(), INTERVAL #{param.orderDeliveryOvertimeDay} DAY)-->
<!--                    </if>-->
<!--                </if>-->
                <!-- 外部单号 -->
                <if test="null != param.sourceOrderNo and '' != param.sourceOrderNo">
                    AND tso.source_order_no LIKE concat('%', #{param.sourceOrderNo}, '%')
                </if>
                <if test="null != param.printState">
                    AND print_state = #{param.printState}
                </if>
                <if test="null != param.pushStatus">
                    AND tso.push_status = #{param.pushStatus}
                </if>
                <if test="null != param.areaId">
                    AND tor.area_id = #{param.areaId}
                </if>
                <if test="null != param.payState">
                    AND tor.pay_state = #{param.payState}
                </if>
                <if test="null != param.payWay">
                    AND tor.pay_way = #{param.payWay}
                </if>
                <!-- 订单状态 -->
                <if test="null != param.deliveryStates and param.deliveryStates.size > 0 ">
                    AND tso.delivery_state IN
                    <foreach collection="param.deliveryStates" item="deliveryState" open="(" separator="," close=")">
                        #{deliveryState}
                    </foreach>
                </if>
            </where>
            ${param.params.dataScope}
            GROUP BY
                tsord.supplier_order_id
                ,tsord.delivery_state
        ) table_count
    </select>

    <select id="getOperatorOrderPageListNew" resultMap="getOperatorOrderPageListNewMap">
        SELECT * FROM (
            SELECT
                <!-- 主表信息 -->
                tor.order_id,
                tor.order_no,
                tor.branch_id,
                tor.pay_way,
                tor.order_type,
                tor.sys_code,
                tsord.supplier_order_id,
                tsord.supplier_order_no,
                tsord.supplier_id,
                tso.delivery_state as deliveryState,
    <!--            CASE tsord.delivery_state-->
    <!--                WHEN '40' THEN 4-->
    <!--                WHEN '41' THEN 4-->
    <!--                WHEN '7'  THEN 3-->
    <!--                ELSE tsord.delivery_state-->
    <!--            END AS deliveryState,-->
                tor.create_time,
                tor.memo, <!-- 订单备注 -->
                tor.colonel_id,
                tor.dc_id,
                tso.memo AS supplierMemo, <!-- 入驻商订单备注 -->
                tor.pcolonel_id,
                tor.pay_state,
                tso.push_status,
                tso.sub_pay_balance_amt,
                tso.source_order_no,
                tor.pay_time,
                tso.driver_id,
                tso.driver_rating_flag,
                tso.driver_rating_id,
                tso.print_state,
                tso.print_qty,
                <!-- 明细表信息 -->
                tsord.supplier_order_dtl_id,
                tsord.supplier_order_dtl_no,
                tsord.spu_id,
                tsord.sku_id,
                tsord.latest_date,
                tsord.oldest_date,
                tsord.gift_flag,
                <!-- 商品原金额 -->
                tsord.order_unit_price,
                <!-- 单位单价、 下单数量、下单金额-->
                ROUND(IFNULL(tsord.order_sales_unit_price, tsord.exact_price * tsord.order_unit_size), 2) AS price,
                IFNULL((tsord.total_num / tsord.order_unit_size), 0) AS demandNum,
                tsord.total_amt AS demandAmt,
                <!-- 发货数量、 发货金额-->
                IFNULL((tsord.send_qty / tsord.order_unit_size), 0) AS totalNum,
                IFNULL((tsord.send_qty * tsord.price), 0) AS totalAmt,
                <!-- 差异数量、 差异金额-->
                IFNULL((tsord.cancel_qty / tsord.order_unit_size), 0) AS shortageNum,
                IFNULL(((tsord.cancel_qty / tsord.order_unit_size) * tsord.order_sales_unit_price), 0) AS discrepancyAmt,
                tsord.order_unit,

                IFNULL(tsord.sub_order_amt, 0) AS saleTotalAmt,
                IFNULL((tsord.coupon_discount_amt + tsord.coupon_discount_amt2 + tsord.activity_discount_amt), 0) AS discountAmt,

                <!-- 售后信息  根据订单明细查询未取消 、 已审核的对应的售后订单详情金额汇总 -->
                IFNULL((SELECT SUM(return_amt) FROM trd_supplier_after_dtl tsad WHERE tsad.is_cancel = 0 AND tsad.approve_state = 1 AND tsord.supplier_order_dtl_id = tsad.supplier_order_dtl_id), 0) AS afterReturnAmt,

                <!-- 商品是否可售后信息  -->
                tsord.is_after_sales,
                tsord.after_sales_time,

                <!-- 商品明细物流信息-->
                toe.order_express_id,
                toe.express_no,
                toe.express_com,
                toe.express_com_no,
                toe.receive_phone,
                toe.receive_man,
                toe.address,
                toe.order_id AS expressOrderId,
                toe.order_no AS expressOrderNo,
                toe.supplier_order_dtl_no AS expressSupplierOrderDtlNo

            FROM
                trd_order tor
                INNER JOIN trd_supplier_order_dtl tsord ON tor.order_id = tsord.order_id
                INNER JOIN trd_supplier_order tso ON tso.order_id = tor.order_id AND tso.supplier_order_id = tsord.supplier_order_id
                LEFT JOIN trd_order_express toe ON tsord.supplier_order_dtl_id = toe.supplier_order_dtl_id
            <where>
                <!-- 单据类型 -->
                <if test="null != param.itemType">
                    AND tor.order_type = #{param.itemType}
                </if>
                <!-- 入驻商 -->
                <if test="null != param.supplierIdList and param.supplierIdList.size > 0 ">
                    AND tsord.supplier_id IN
                    <foreach collection="param.supplierIdList" item="supplierId" open="(" separator="," close=")">
                        #{supplierId}
                    </foreach>
                </if>
                <!-- 商品SPU -->
                <if test="null != spuIds and spuIds.size > 0 ">
                    AND tsord.spu_id IN
                    <foreach collection="spuIds" item="spuId" open="(" separator="," close=")">
                        #{spuId}
                    </foreach>
                </if>
                <!-- 门店 -->
                <if test="null != param.branchIds and param.branchIds.size > 0 ">
                    AND tor.branch_id IN
                    <foreach collection="param.branchIds" item="branchId" open="(" separator="," close=")">
                        #{branchId}
                    </foreach>
                </if>
                <!-- 商品SKU -->
                <if test="null != skuIds and skuIds.size > 0 ">
                    AND tsord.sku_id IN
                    <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                        #{skuId}
                    </foreach>
                </if>
                <!-- 业务员 -->
                <if test="null != param.colonelIds and param.colonelIds.size > 0 ">
                    AND tor.colonel_id IN
                    <foreach collection="param.colonelIds" item="colonelId" open="(" separator="," close=")">
                        #{colonelId}
                    </foreach>
                </if>
                <!-- 订单号 -->
                <if test="null != param.orderNo and param.orderNo != ''">
                    AND tor.order_no LIKE concat('%', #{param.orderNo}, '%')
                </if>
                <!-- 入驻商订单号 -->
                <if test="null != param.subOrderNo and param.subOrderNo != ''">
                    AND tsord.supplier_order_no LIKE concat('%', #{param.subOrderNo}, '%')
                </if>
                <!-- 查询时间  创建时间或支付时间 -->
                <if test="null != param.startTime and null != param.endTime ">
                    AND tor.create_time
                    BETWEEN #{param.startTime} AND #{param.endTime}
                </if>
                <if test="null != param.payStartTime and null != param.payEndTime ">
                    AND tor.pay_time
                    BETWEEN #{param.payStartTime} AND #{param.payEndTime}
                </if>
                    <!-- 订单状态 -->
    <!--                    <if test="null != param.orderType and param.orderType != 100">-->
    <!--                        <if test="param.orderType == 0"> &lt;!&ndash; 待付款&ndash;&gt;-->
    <!--                            AND tor.pay_state = #{param.orderType}-->
    <!--                        </if>-->
    <!--                        <if test="param.orderType == 2"> &lt;!&ndash; 订单取消 &ndash;&gt;-->
    <!--                            AND (tor.pay_state = #{param.orderType} OR tsord.delivery_state = #{param.orderType})-->
    <!--                        </if>-->
    <!--                        <if test="param.orderType == 3"> &lt;!&ndash; 代发货包含（备货中 7）&ndash;&gt;-->
    <!--                            AND tsord.delivery_state IN (#{param.orderType}, '7')-->
    <!--                        </if>-->
    <!--                        <if test="param.orderType == 4"> &lt;!&ndash; 已发货包含（待装车 40、已装车 41） &ndash;&gt;-->
    <!--                            AND tsord.delivery_state IN (#{param.orderType} , '40', '41')-->
    <!--                        </if>-->
    <!--                        <if test="param.orderType == 5"> &lt;!&ndash; 订单收货 5 &ndash;&gt;-->
    <!--                            AND tsord.delivery_state = #{param.orderType}-->
    <!--                        </if>-->
    <!--                        <if test="param.orderType == 6"> &lt;!&ndash; 订单已完成 6  &ndash;&gt;-->
    <!--                            AND tsord.delivery_state = #{param.orderType}-->
    <!--                        </if>-->
    <!--                        <if test="param.orderType == 99"> &lt;!&ndash; 订单发货超时（3，7）&ndash;&gt;-->
    <!--                            AND tsord.delivery_state IN ('3', '7')  AND tor.pay_time &lt;= DATE_ADD(CURDATE(), INTERVAL #{param.orderDeliveryOvertimeDay} DAY)-->
    <!--                        </if>-->
    <!--                    </if>-->

                    <!-- 订单状态 -->
                    <if test="null != param.deliveryStates and param.deliveryStates.size > 0 ">
                        AND tso.delivery_state IN
                        <foreach collection="param.deliveryStates" item="deliveryState" open="(" separator="," close=")">
                            #{deliveryState}
                        </foreach>
                    </if>

                    <if test="null != param.subOrderId">
                        AND tsord.supplier_order_id = #{param.subOrderId}
                    </if>
                    <!-- 外部单号 -->
                    <if test="null != param.sourceOrderNo and '' != param.sourceOrderNo">
                        AND tso.source_order_no LIKE concat('%', #{param.sourceOrderNo}, '%')
                    </if>
                    <if test="null != param.printState">
                        AND tso.print_state = #{param.printState}
                    </if>
                    <if test="null != param.pushStatus">
                        AND tso.push_status = #{param.pushStatus}
                    </if>
                    <if test="null != param.payState">
                        AND tor.pay_state = #{param.payState}
                    </if>
                    <if test="null != param.payWay">
                        AND tor.pay_way = #{param.payWay}
                    </if>
                    <if test="null != param.areaId">
                        AND tor.area_id = #{param.areaId}
                    </if>
                </where>
                ${param.params.dataScope}
                ORDER BY tor.create_time DESC
        ) result_table
        LIMIT #{param.pageNo}, #{param.pageSize}
    </select>

    <select id="getExpressesList" resultType="com.zksr.trade.controller.orderExpress.vo.TrdOrderExpressRespVO">
        select
            toe.order_express_id,
            toe.express_no,
            toe.express_com,
            toe.express_com_no,
            toe.receive_phone
        from
        trd_order_express toe
        where
        toe.supplier_order_dtl_id = #{supplier_order_dtl_id}
    </select>

    <!--  根据条件获取可售后的订单数据 -->
    <select id="getOrderAfterInfo" parameterType="com.zksr.trade.api.after.vo.OrderAfterRequest" resultType="com.zksr.trade.api.after.dto.OrderAfterDtlDTO">

        SELECT
            tOrder.*
            , TRUNCATE(tOrder.minTotalNum / tOrder.order_unit_size, 0) AS totalNum
            , ROUND(IFNULL(tOrder.res_unit_price, tOrder.res_price * tOrder.order_unit_size), 2) AS price
            ,ROUND(tOrder.res_price * tOrder.minTotalNum, 2) AS totalAmt
        FROM (
            SELECT
                tOrder.order_id
                ,tOrder.order_no
                ,tOrder.branch_id
                ,sOrder.supplier_order_id
                ,sOrder.supplier_id
                ,sOrder.supplier_name
                ,sOrderDtl.supplier_order_dtl_id
                ,sOrderDtl.sku_id
                ,sOrderDtl.spu_id
                ,CASE
                    WHEN sOrderDtl.delivery_state = 5 THEN (sOrderDtl.total_num - (select ifnull(sum(afterDtl.return_qty), 0) returnQty from trd_supplier_after_dtl afterDtl where afterDtl.supplier_order_dtl_id =
                        sOrderDtl.supplier_order_dtl_id and afterDtl.is_cancel = 0))
                    ELSE (sOrderDtl.total_num - ifnull(sOrderDtl.cancel_qty, 0))
                END AS minTotalNum
                ,ROUND(sOrderDtl.res_price, 2) AS minPrice
                ,sOrderDtl.res_price
                ,sOrderDtl.gift_flag
                ,sOrderDtl.order_unit_type AS unitType
                ,sOrderDtl.order_unit_size
                ,sOrderDtl.order_unit AS orderUnit
                ,sOrderDtl.order_sales_unit_price AS orderSalesUnitPrice
                ,sOrderDtl.res_unit_price
                ,sOrder.push_status AS pushStatus
                ,sOrderDtl.latest_date
                ,sOrderDtl.oldest_date
                ,(sOrderDtl.total_num - ifnull(sOrderDtl.cancel_qty, 0)) / sOrderDtl.order_unit_size AS orderTotalNum
            FROM trd_order tOrder
            LEFT JOIN trd_supplier_order sOrder ON tOrder.order_id = sOrder.order_id
            LEFT JOIN trd_supplier_order_dtl sOrderDtl ON sOrder.supplier_order_id = sOrderDtl.supplier_order_id
            WHERE
                tOrder.pay_state in (1, 3, 4)
                AND tOrder.order_id = #{orderId}
                <choose>
                    <when test="null != deliveryState and deliveryState == 10">
                        AND sOrder.delivery_state IN (#{deliveryState})
                    </when>
                    <otherwise>
                        AND sOrder.delivery_state = (#{deliveryState})
                        AND sOrderDtl.is_after_sales = 1
                        AND DATE_ADD(sOrderDtl.receive_time, INTERVAL IFNULL(sOrderDtl.after_sales_time, 0) MINUTE) &gt;= now()
                    </otherwise>
                </choose>

            <if test="null != supplierOrders and supplierOrders.size > 0 ">
                AND sOrder.supplier_order_id in
                    <foreach collection="supplierOrders" item="suppleir" open="(" separator="," close=")">
                        #{suppleir.supplierOrderId}
                    </foreach>

            <!-- TODO 支持商品级售后 暂不开放-->
<!--            <foreach collection="supplierOrders" item="suppleir">-->
<!--                <if test="null != suppleir.supplierOrderDtls and suppleir.supplierOrderDtls.size > 0">-->
<!--                    OR sOrderDtl.supplier_order_dtl_id in-->
<!--                    <foreach collection="suppleir.supplierOrderDtls" item="suppleirItem" open="(" separator="," close=")">-->
<!--                        #{suppleirItem.supplierOrderDtlId}-->
<!--                    </foreach>-->
<!--                </if>-->
<!--            </foreach>-->

            </if>
        ) tOrder
        WHERE tOrder.minTotalNum > 0

    </select>


    <select id="printGetByOrderId" resultMap="printOrder">
        SELECT
            tor.order_id,
            tor.order_no,
            tor.branch_id,
            tor.pay_way,
            tor.create_time,
            tor.colonel_id,
            tsor.supplier_order_id,
            tsor.supplier_order_no,
            tsor.supplier_id,
            tsor.sub_discount_amt,
            tsor.sub_pay_amt,
            tsor.sub_order_amt,
            tsor.sub_order_num,
            tsord.supplier_order_dtl_id,
            tsord.spu_id,
            tsord.sku_id,
            tsord.total_amt,
            tsord.total_num,
            tsord.price,
            tsord.sale_price,
            tsord.memo,
            tsord.order_unit,
            tsord.order_unit_qty,
            tsord.order_unit_size,
            tsord.order_unit_price,
            tsord.cancel_amt,
            tsord.cancel_qty
        FROM
            trd_order tor
                INNER JOIN trd_supplier_order tsor ON tor.order_id = tsor.order_id
                INNER JOIN trd_supplier_order_dtl tsord ON tsord.supplier_order_id = tsor.supplier_order_id
        WHERE
            tor.order_id = #{orderId}
            AND tor.sys_code = #{sysCode}
            <if test="null != supplierOrderId">
                AND tsor.supplier_order_id = #{supplierOrderId}
            </if>

    </select>

    <!-- 获取门店待付款、待发货、待收货、已收货 状态订单数量  -->
    <select id="getOrderStatus" resultType="com.zksr.trade.api.order.vo.OrderStatusVO" parameterType="com.zksr.trade.api.order.vo.OrderStatusReqVO">
        SELECT
            <!-- 待付款订单 (在线支付未付款) -->
            IFNULL((
                SELECT
                    COUNT( 1 )
                FROM
                    trd_order o
                WHERE
                    <choose>
                        <!-- 业务员 -->
                        <when test="2 == reqType">
                            o.colonel_id = #{reqId}
                        </when>
                        <!-- 门店 -->
                        <otherwise>
                            o.branch_id = #{reqId}
                        </otherwise>
                    </choose>
                   <!-- AND o.create_time &gt; DATE_SUB(NOW(), INTERVAL 3 HOUR)-->
                    AND o.pay_state = 0
            ), 0) pendingPayment,
            <!-- 待发货订单 (未发货) -->
            IFNULL((
                SELECT
                    COUNT( DISTINCT o.order_id )
                FROM
                    trd_order o
                    INNER JOIN trd_supplier_order_dtl d ON o.order_id = d.order_id
                WHERE
                    <choose>
                        <!-- 业务员 -->
                        <when test="2 == reqType">
                            o.colonel_id = #{reqId}
                        </when>
                        <!-- 门店 -->
                        <otherwise>
                            o.branch_id = #{reqId}
                        </otherwise>
                    </choose>
                    <!--AND o.create_time &gt; DATE_SUB(NOW(), INTERVAL 90 DAY)-->
                    AND d.delivery_state IN (3, 7)
            ), 0) toBeDelivered,
            <!-- 待收货订单 (已发货) -->
            IFNULL((
                SELECT
                    COUNT( DISTINCT o.order_id )
                FROM
                    trd_order o
                    INNER JOIN trd_supplier_order_dtl d ON o.order_id = d.order_id
                WHERE
                    <choose>
                        <!-- 业务员 -->
                        <when test="2 == reqType">
                            o.colonel_id = #{reqId}
                        </when>
                        <!-- 门店 -->
                        <otherwise>
                            o.branch_id = #{reqId}
                        </otherwise>
                    </choose>
                    <!--AND o.create_time &gt; DATE_SUB(NOW(), INTERVAL 90 DAY)-->
                    AND d.delivery_state IN ( 4, 40, 41 )
            ), 0) awaitingReceipt
    </select>


    <!-- 门店销售总额 -->
    <select id="getBranchMonthOrderAmount" resultType="com.zksr.trade.api.order.vo.OrderAmountStatisticsVO" >
        SELECT
            <!-- 已收货金额 -->
            IFNULL((
                SELECT
                    SUM( dtl.sub_pay_amt )
                FROM
                    trd_supplier_order AS dtl
                    INNER JOIN trd_order AS tror ON dtl.order_id = tror.order_id
                WHERE
                    tror.branch_id = #{branchId}
                    AND tror.create_time BETWEEN #{beginTime} AND #{endTime}
                    AND dtl.delivery_state = 21
            ), 0) arrivalAmount,
            <!-- 在途金额 -->
            IFNULL((
                SELECT
                    SUM( dtl.sub_pay_amt )
                FROM
                    trd_supplier_order AS dtl
                    INNER JOIN trd_order AS tdor ON dtl.order_id = tdor.order_id
                WHERE
                    tdor.branch_id = #{branchId}
                    AND tdor.create_time BETWEEN #{beginTime} AND #{endTime}
                    AND dtl.delivery_state = 20
            ), 0) outstandingAmount,
            <!-- 退货金额 -->
            IFNULL((
                SELECT
                    SUM(refund_amt)
                FROM
                    trd_after
                WHERE
                    create_time BETWEEN #{beginTime} AND #{endTime}
                    AND branch_id = #{branchId}
                    AND is_cancel = 0
            ), 0) returnAmount
    </select>


    <select id="getSaleAmount" resultType="java.math.BigDecimal" >
        SELECT
            SUM( dtl.total_amt )
        FROM
            trd_supplier_order_dtl dtl
                INNER JOIN trd_order tror ON dtl.order_id = tror.order_id
        WHERE 1=1
            <if test="null != reqVO.colonelId">
                AND tror.colonel_id = #{reqVO.colonelId}
            </if>
            <if test="null != reqVO.branchId">
                AND tror.branch_id = #{reqVO.branchId}
            </if>
          AND tror.create_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
          AND tror.pay_state = 1

    </select>

<!--    查询订单售后数量-->
    <select id="getSaleAfterNum" resultType="java.lang.Integer" parameterType="com.zksr.trade.api.order.vo.OrderStatusReqVO">
        SELECT
            COUNT(DISTINCT tdar.after_id) AS total_after_sales_count
        FROM
            trd_after tdar
            INNER JOIN trd_supplier_after_dtl dtl ON dtl.after_id = tdar.after_id
        WHERE
            <choose>
                <!-- 业务员 -->
                <when test="2 == reqType">
                    tdar.colonel_id = #{reqId}
                </when>
                <!-- 门店 -->
                <otherwise>
                    tdar.branch_id = #{reqId}
                </otherwise>
            </choose>
            AND tdar.is_cancel = 0
            <!-- 只查询20天内的数据 -->
           <!-- AND tdar.create_time &gt; DATE_SUB(NOW(), INTERVAL 90 DAY)-->
            AND (
                <!-- 售后未审核 -->
                (dtl.approve_state = 0) OR
                <!-- 售后已审核但是还没退款-->
                (dtl.approve_state = 1 AND dtl.refund_state = 0)
            )
    </select>

    <select id="getListByBranch" resultType="com.zksr.common.elasticsearch.domain.EsStoreProduct">
        SELECT
            tsod.sku_id,
            IFNULL(tsod.order_unit_type, 1) AS unitSize,
            tor.branch_id,
            tsod.item_type,
            tsod.supplier_id,
            tsod.area_item_id,
            tsod.supplier_item_id,
            MIN(tsod.create_time) firstPurchasedDate,
            MAX(tsod.create_time) recentlyPurchasedDate,
            COUNT(*) AS purchasedFrequencyTotal,
            SUM( IFNULL(tsod.total_num,0) ) AS purchasedNumberTotal,
            SUM( CASE WHEN tsod.create_time >= (CURDATE() - INTERVAL 90 DAY) THEN 1 ELSE 0 END ) AS withinPurchasedFrequencyTotal,
            SUM( CASE WHEN tsod.create_time >= (CURDATE() - INTERVAL 90 DAY) THEN tsod.total_num ELSE 0 END ) AS withinPurchasedNumberTotal
        FROM
            trd_supplier_order_dtl tsod
            INNER JOIN trd_order tor ON tsod.order_id = tor.order_id
        WHERE
            tor.branch_id = #{branchId}
            <!-- 90天内的订单 -->
            AND tor.create_time &gt; DATE_SUB(NOW(), INTERVAL 3 MONTH)
            AND (tsod.gift_flag IS NULL OR tsod.gift_flag = '0')
            <if test="null != skuIdList and skuIdList.size > 0 ">
                and tsod.sku_id in
                <foreach collection="skuIdList" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
            AND tsod.order_unit_type = #{unitSize}
            AND tsod.item_type IS NOT NULL
        GROUP BY
            tsod.sku_id,
            tor.branch_id,
            tsod.item_type,
            tsod.supplier_id,
            tsod.area_item_id,
            tsod.supplier_item_id,
            unitSize
        ORDER BY MIN(tsod.create_time) DESC
    </select>

    <select id="selectMemColoneAppOrderListTotal"
            resultType="com.zksr.trade.api.order.dto.ColonelAppOrderListTotalDTO">
        SELECT
            COUNT(1) AS numTotal,
            SUM(orderTable.amtTotal) AS amtTotal
        FROM (
            SELECT
                torder.order_id,
                SUM(orderDtl.total_amt - orderDtl.cancel_amt) AS amtTotal
            FROM
                trd_order torder
                LEFT JOIN trd_supplier_order_dtl orderDtl ON orderDtl.order_id = torder.order_id
            WHERE
                torder.pay_state IN (1, 3, 4) <!-- 这里只查询已付款或货到付款的单据 且 不显示已取消的订单数据-->
                AND orderDtl.delivery_state != 2
                <!-- 业务员 -->
                <if test="null != reqVO.colonelIds and reqVO.colonelIds.size > 0 ">
                    AND torder.colonel_id IN
                    <foreach collection="reqVO.colonelIds" item="colonelId" open="(" separator="," close=")">
                        #{colonelId}
                    </foreach>
                </if>
                <!-- 订单编号 -->
                <if test="null != reqVO.orderNo">
                    AND torder.order_no LIKE concat ('%', #{reqVO.orderNo} ,'%')
                </if>
                <!-- 创建时间 -->
                <if test="null != reqVO.startTime and null != reqVO.endTime">
                    AND torder.create_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </if>
                <!-- 门店编号 -->
                <if test="null != reqVO.branchIds and reqVO.branchIds.size > 0 ">
                    AND torder.branch_id IN
                    <foreach collection="reqVO.branchIds" item="branchId" open="(" separator="," close=")">
                        #{branchId}
                    </foreach>
                </if>
                <!-- 订单类型 -->
                <if test="null != reqVO.orderProductType">
                    AND torder.order_type = #{reqVO.orderProductType}
                </if>
                <!-- 订单状态 -->
                <if test="null != reqVO.orderStatus and reqVO.orderStatus != 100">
                    <if test="reqVO.orderStatus == 3"> <!-- 代发货包含（备货中 7）-->
                        AND orderDtl.delivery_state IN (#{reqVO.orderStatus}, '7')
                    </if>
                    <if test="reqVO.orderStatus == 4"> <!-- 待收货包含（待装车 40、已装车 41） -->
                        AND orderDtl.delivery_state IN (#{reqVO.orderStatus} , '40', '41')
                    </if>
                    <if test="reqVO.orderStatus == 5"> <!-- 订单收货 5 -->
                        AND orderDtl.delivery_state = #{reqVO.orderStatus}
                    </if>
                    <if test="reqVO.orderStatus == 6"> <!-- 订单已完成 6  -->
                        AND orderDtl.delivery_state = #{reqVO.orderStatus}
                    </if>
                </if>
            GROUP BY
                torder.order_id
        ) orderTable
    </select>

    <select id="selectMemColoneAppOrder" resultType="com.zksr.trade.api.order.vo.TrdColonelAppOrderListRespVO">
        SELECT
            torder.order_id
            ,torder.order_no
            ,torder.member_id
            ,torder.branch_id
            ,torder.create_time
            ,torder.pay_state
            ,torder.pay_time
            ,torder.pay_amt AS orderPayAmt
            ,torder.order_amt AS orderAmt
            ,torder.discount_amt AS orderDiscountAmt
            ,torder.pay_way payWay
            ,torder.colonel_flag AS colonelFlag
            ,torder.colonel_id AS colonelId
        FROM trd_order torder
        <where>
            AND torder.pay_state IN (1, 3, 4) <!-- 这里只查询已付款或货到付款的单据-->
            <if test="null != colonelIds and colonelIds.size > 0 ">
                and torder.colonel_id in
                <foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
            <if test="null != reqVO.orderNo">
                and torder.order_no like concat ('%', #{reqVO.orderNo} ,'%')
            </if>
            <if test="null != reqVO.startTime and null != reqVO.endTime">
                and torder.create_time BETWEEN #{reqVO.startTime} and #{reqVO.endTime}
            </if>
            <if test="null != reqVO.branchIds and reqVO.branchIds.size > 0 ">
                and torder.branch_id in
                <foreach collection="reqVO.branchIds" item="branchId" open="(" separator="," close=")">
                    #{branchId}
                </foreach>
            </if>
        </where>
        ORDER BY
            torder.create_time
            DESC
    </select>

    <select id="getEsListByOrderId" resultType="com.zksr.common.elasticsearch.domain.EsStoreProduct">
        SELECT
            tor.branch_id branchId,
            tsod.sku_id skuId,
            tsod.spu_id spuId,
            tsod.sys_code sysCode,
            tsod.total_num recentlyPurchasedNumber,
            tsod.item_type itemType,
            tsod.order_unit unit,
            tsod.area_item_id areaItemId,
            tsod.supplier_item_id supplierItemId,
            IFNULL(tsod.order_unit_type, 1) unitSize
        FROM
            trd_order tor
            INNER JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
        WHERE
            tor.order_id = #{orderId}
            AND (tsod.gift_flag IS NULL OR tsod.gift_flag = '0')
    </select>
    <select id="getTrdSupplierOrderListByOrderId" resultType="com.zksr.common.core.domain.vo.openapi.TradeOutErpRequest">
        select
            tso.supplier_order_no as orderNo,
            tor.branch_id as branchNo,
            tso.supplier_id as dcBranchNo,
            tso.sub_order_amt as totalAmt,
            tso.sub_pay_amt as realAmt,
            tso.memo as memo,
            "1" as distributionType
        from
            trd_supplier_order tso
                left join trd_order tor on tso.order_id = tor.order_id
        where
            tso.order_id = #{orderId}
    </select>
    <select id="getTrdSupplierOrderDtlListBySupOrderNo" resultType="com.zksr.common.core.domain.vo.openapi.TradeDetail">
        select
            tsod.sku_id as itemNo,
            tsod.total_num as saleQty,
            tsod.sale_price as itemAmt,
            tsod.price as itemRealAmt,
            tsod.gift_flag as giftFlag
        from
            trd_supplier_order_dtl tsod
        where
            tsod.supplier_order_no = #{supplierOrderNo}
    </select>
    <select id="getCustomApiMaster" resultType="com.zksr.common.core.domain.vo.openapi.CustomApiMaster">
        select
            cam.*
        from
            custom_api_master cam
        <where>
            1=1
            <if test="customApiMaster.modelType != null and customApiMaster.modelType != '' ">
                and cam.model_type =#{customApiMaster.modelType}
            </if>
            <if test="customApiMaster.apiStatus != null and customApiMaster.apiStatus != '' ">
                and cam.api_status =#{customApiMaster.apiStatus}
            </if>
            <if test="customApiMaster.systemType != null and customApiMaster.systemType != '' ">
                and cam.system_type =#{customApiMaster.systemType}
            </if>
        </where>

    </select>
    <select id="getCustomApiDetail" resultType="com.zksr.common.core.domain.vo.openapi.CustomApiDetail">
        select
            cad.*
        from
            sys_custom_api_detail cad
        where
            cad.api_no =#{apiNo}
    </select>


    <!-- 订单分页查询总条数 新 2024-07-29 -->
    <select id ="selectPageAllNewCount" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT tor.order_id) AS total
        FROM
            trd_order tor
            LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
        <where>
            <if test="null != orderPageReqVO.colonelId">
                AND tor.colonel_id = #{orderPageReqVO.colonelId}
            </if>
            <if test="null != orderPageReqVO.branchId">
                AND tor.branch_id = #{orderPageReqVO.branchId}
            </if>
            <if test="null != orderPageReqVO.memberId">
                AND tor.member_id = #{orderPageReqVO.memberId}
            </if>
            <if test=" null != orderPageReqVO.deliveryState and 100 != orderPageReqVO.deliveryState">
                <choose>
                    <when test="0 != orderPageReqVO.deliveryState and 1 != orderPageReqVO.deliveryState and 2 != orderPageReqVO.deliveryState and 4 != orderPageReqVO.deliveryState and 3 != orderPageReqVO.deliveryState">
                        AND tsod.delivery_state = #{orderPageReqVO.deliveryState}
                    </when>
                    <when test="4 == orderPageReqVO.deliveryState"> <!-- 待收货包含（待装车、已装车状态），故需要单独处理 -->
                        AND tsod.delivery_state in (#{orderPageReqVO.deliveryState},'40','41')
                    </when>
                    <when test="3 == orderPageReqVO.deliveryState and null == orderPageReqVO.orderDeliveryOvertimeFlag"> <!-- 待发货包含（备货中状态），故需要单独处理 -->
                        AND tsod.delivery_state in (#{orderPageReqVO.deliveryState},'7')
                    </when>
                    <when test="2 == orderPageReqVO.deliveryState"> <!-- 取消（包含未付款取消） 故需要单独处理 -->
                        AND (tsod.delivery_state = #{orderPageReqVO.deliveryState} or tor.pay_state = #{orderPageReqVO.deliveryState})
                    </when>
                    <when test="3 == orderPageReqVO.deliveryState and 1 == orderPageReqVO.orderDeliveryOvertimeFlag"> <!-- 超时未发货订单标志 默认超过三天未发货为异常订单-->
                        AND tsod.delivery_state in (#{orderPageReqVO.deliveryState},'7') AND tor.pay_time &lt;= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
                    </when>
                    <otherwise>
                        AND tor.pay_state = #{orderPageReqVO.deliveryState}
                    </otherwise>
                </choose>
            </if>
            <if test=" null != orderPageReqVO.keyWords and '' != orderPageReqVO.keyWords">
                AND (tor.order_no LIKE CONCAT ('%',#{orderPageReqVO.keyWords},'%') OR tsod.spu_name LIKE CONCAT ('%',#{orderPageReqVO.keyWords},'%') )
            </if>
            <if test=" null != orderPageReqVO.orderId">  <!-- TODO 页面点击主单号查询详情使用，后期可能需要根据需求业务来对列表和明细进行SQL拆分 -->
                AND tor.order_id = #{orderPageReqVO.orderId}
            </if>
            <if test=" null != orderPageReqVO.supplierOrderId">  <!-- 入驻商订单ID -->
                AND tsod.supplier_order_id = #{orderPageReqVO.supplierOrderId}
            </if>
            <if test=" null != orderPageReqVO.orderType">  <!-- 订单类型 订单类型：0 全国订单 1：本地订单 -->
                AND tor.order_type = #{orderPageReqVO.orderType}
            </if>
            <if test="null != orderPageReqVO.startTime and null != orderPageReqVO.endTime">
                AND tor.create_time BETWEEN #{orderPageReqVO.startTime} and #{orderPageReqVO.endTime}
            </if>
        </where>
    </select>

    <!-- 订单分页查询 新 2024-07-29-->
    <!--<select id="selectPageAllNew" resultType="com.zksr.trade.api.order.dto.OrderResultDTO">-->
    <select id="selectPageAllNew" resultMap="orderListNew">
        SELECT
           /* -- 主订单数据*/
            tor.order_id
            ,tor.order_no
            ,tor.member_id
            ,tor.branch_id
            ,tor.create_by
            ,tor.create_time
            ,tor.pay_state
            ,tor.pay_time
            ,tor.pay_way
            ,tor.dc_id
            ,tor.order_type
            ,tor.colonel_flag
            ,tor.colonel_id
            ,tor.memo
             <!--增加销售模式-->
            ,tor.distribution_mode
            ,tso.driver_id
            ,tso.driver_rating_flag
            ,tso.driver_rating_id
            /*-- 入驻商订单数据*/
            ,tso.supplier_id
            ,tso.supplier_name
            ,tso.supplier_order_id
            ,tso.supplier_order_no
            ,tso.memo supplier_memo
            ,tso.sub_pay_balance_amt
            ,tso.delivery_state as deliveryState
<!--            ,CASE tsod.delivery_state-->
<!--                WHEN '40' THEN 4-->
<!--                WHEN '41' THEN 4-->
<!--                WHEN '7'  THEN 3-->
<!--                ELSE tsod.delivery_state-->
<!--            END AS deliveryState-->

            /*-- 入驻商订单明细信息*/
            ,tsod.spu_id
            ,tsod.sku_id
            ,tsod.thumb_video
            ,tsod.thumb
            ,CASE tsod.delivery_state
                 WHEN 2 THEN IFNULL((tsod.total_num / tsod.order_unit_size), 0)
                 ELSE  IFNULL(((tsod.total_num - tsod.cancel_qty) / tsod.order_unit_size), 0) END AS totalNum
            ,CASE tsod.delivery_state
                 WHEN 2 THEN tsod.total_amt
                 ELSE tsod.total_amt - tsod.cancel_amt END AS totalAmt
            ,CASE tsod.delivery_state
                WHEN 2 THEN IFNULL(tsod.total_num, 0)
                ELSE  IFNULL((tsod.total_num - tsod.cancel_qty), 0)
            END AS minTotalNum
            ,ROUND(IFNULL(tsod.order_sales_unit_price, tsod.exact_price * tsod.order_unit_size), 2) AS price
            ,tsod.discount_amt
            ,tsod.gift_flag
            ,tsod.discount_amt2
            ,tsod.spu_name
            ,tsod.spec_name
            ,tsod.item_type
            ,tsod.supplier_order_dtl_id
            ,tsod.supplier_item_id
            ,tsod.area_item_id
            ,tsod.order_unit
            ,tsod.order_unit_type
            ,IFNULL(tsod.sub_order_amt, 0) AS saleAmt
            ,IFNULL((tsod.coupon_discount_amt + tsod.coupon_discount_amt2 + tsod.activity_discount_amt), 0) AS discountAmt
            ,tsod.latest_date
            ,tsod.oldest_date
            ,tsod.order_unit_price
            ,tsod.is_after_sales
            ,tsod.after_sales_time
        FROM
            trd_order tor
            INNER JOIN (
                SELECT
                    tor.order_id
                    ,tsod.delivery_state
                FROM
                    trd_order tor
                    LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
                <where>
                    <if test="null != orderPageReqVO.colonelId">
                        AND tor.colonel_id = #{orderPageReqVO.colonelId}
                    </if>
                    <if test="null != orderPageReqVO.branchId">
                        AND tor.branch_id = #{orderPageReqVO.branchId}
                    </if>
                    <if test="null != orderPageReqVO.memberId">
                        AND tor.member_id = #{orderPageReqVO.memberId}
                    </if>
                    <if test=" null != orderPageReqVO.deliveryState and 100 != orderPageReqVO.deliveryState">
                        <choose>
                            <when test="0 != orderPageReqVO.deliveryState and 1 != orderPageReqVO.deliveryState and 2 != orderPageReqVO.deliveryState and 4 != orderPageReqVO.deliveryState and 3 != orderPageReqVO.deliveryState">
                                AND tsod.delivery_state = #{orderPageReqVO.deliveryState}
                            </when>
                            <when test="4 == orderPageReqVO.deliveryState"> <!-- 待收货包含（待装车、已装车状态），故需要单独处理 -->
                                AND tsod.delivery_state in (#{orderPageReqVO.deliveryState},'40','41')
                            </when>
                            <when test="3 == orderPageReqVO.deliveryState and null == orderPageReqVO.orderDeliveryOvertimeFlag"> <!-- 待发货包含（备货中状态），故需要单独处理 -->
                                AND tsod.delivery_state in (#{orderPageReqVO.deliveryState},'7')
                            </when>
                            <when test="2 == orderPageReqVO.deliveryState"> <!-- 取消（包含未付款取消） 故需要单独处理 -->
                                AND (tsod.delivery_state = #{orderPageReqVO.deliveryState} or tor.pay_state = #{orderPageReqVO.deliveryState})
                            </when>
                            <when test="3 == orderPageReqVO.deliveryState and 1 == orderPageReqVO.orderDeliveryOvertimeFlag"> <!-- 超时未发货订单标志 默认超过三天未发货为异常订单-->
                                AND tsod.delivery_state in (#{orderPageReqVO.deliveryState},'7') AND tor.pay_time &lt;= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
                            </when>
                            <otherwise>
                                AND tor.pay_state = #{orderPageReqVO.deliveryState}
                            </otherwise>
                        </choose>
                    </if>
                    <if test=" null != orderPageReqVO.keyWords and '' != orderPageReqVO.keyWords">
                        AND (tor.order_no LIKE CONCAT ('%',#{orderPageReqVO.keyWords},'%') OR tsod.spu_name LIKE CONCAT ('%',#{orderPageReqVO.keyWords},'%') )
                    </if>
                    <if test=" null != orderPageReqVO.orderId">  <!-- TODO 页面点击主单号查询详情使用，后期可能需要根据需求业务来对列表和明细进行SQL拆分 -->
                        AND tor.order_id = #{orderPageReqVO.orderId}
                    </if>
                    <if test=" null != orderPageReqVO.supplierOrderId">  <!-- 入驻商订单ID -->
                        AND tsod.supplier_order_id = #{orderPageReqVO.supplierOrderId}
                    </if>
                    <if test=" null != orderPageReqVO.supplierOrderNo">  <!-- 入驻商订单编号 -->
                        AND tsod.supplier_order_no = #{orderPageReqVO.supplierOrderNo}
                    </if>
                    <if test=" null != orderPageReqVO.orderType">  <!-- 订单类型 订单类型：0 全国订单 1：本地订单 -->
                        AND tor.order_type = #{orderPageReqVO.orderType}
                    </if>
                    <if test="null != orderPageReqVO.startTime and null != orderPageReqVO.endTime">
                        AND tor.create_time BETWEEN #{orderPageReqVO.startTime} and #{orderPageReqVO.endTime}
                    </if>
                </where>
                GROUP BY
                    tor.order_id
                    ,tsod.delivery_state
                ORDER BY MAX(tor.create_time) DESC
                LIMIT #{orderPageReqVO.pageNo}, #{orderPageReqVO.pageSize}
            ) pageOrder ON pageOrder.order_id = tor.order_id
            INNER JOIN trd_supplier_order tso ON tor.order_id = tso.order_id
            <if test=" null != orderPageReqVO.supplierOrderId">  <!-- 入驻商订单ID -->
                AND tso.supplier_order_id = #{orderPageReqVO.supplierOrderId}
            </if>
            <if test=" null != orderPageReqVO.supplierOrderNo">  <!-- 入驻商订单编号 -->
                AND tso.supplier_order_no = #{orderPageReqVO.supplierOrderNo}
            </if>
            INNER JOIN trd_supplier_order_dtl tsod ON tsod.supplier_order_id = tso.supplier_order_id AND pageOrder.delivery_state = tsod.delivery_state
        ORDER BY tor.create_time DESC
    </select>
    <select id="selectByBrandHomeSaleData" resultType="com.zksr.trade.controller.app.vo.BrandHomePageRespVO">
        SELECT
            IFNULL(SUM(tsod.total_amt), 0) AS todayTotalAmt,
            IFNULL(COUNT(DISTINCT tsod.order_id), 0) AS todayOrder
        FROM
            trd_supplier_order_dtl tsod
            INNER JOIN trd_order tor ON tor.order_id = tsod.order_id
        WHERE
            tsod.pay_state IN ('1','3')
            AND tsod.create_time BETWEEN DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00') AND NOW()
            AND tsod.brand_id IN
            <foreach collection="brandList" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
            <if test="areaList != null">
                AND tor.area_id IN
                <foreach collection="areaList" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
    </select>
    <select id="selectByBrandHomeSaleList"
            resultType="com.zksr.trade.controller.app.vo.BrandHomeSaleListRespVO">
        SELECT
            tsod.brand_id,
            IFNULL(SUM(tsod.total_amt), 0) AS saleAmt,
            IFNULL(COUNT(DISTINCT tsod.order_id), 0) AS saleOrder
        FROM
            trd_supplier_order_dtl tsod
            INNER JOIN trd_order tor ON tor.order_id = tsod.order_id
        WHERE
            tsod.pay_state IN ('1','3')
            AND tsod.create_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
            AND tsod.brand_id IN
            <foreach collection="reqVO.brandList" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
            <if test="reqVO.areaList != null">
                AND tor.area_id IN
                <foreach collection="reqVO.areaList" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
        GROUP BY
            tsod.brand_id
    </select>
    <select id="selectByBrandHomeSaleInfoList"
            resultType="com.zksr.trade.controller.app.vo.BrandHomeSaleListInfoRespVO">
        SELECT
            tsod.spu_id,
            IFNULL(SUM(tsod.total_amt), 0) AS saleAmt,
            IFNULL(COUNT(DISTINCT tsod.order_id), 0) AS saleOrder
        FROM
            trd_supplier_order_dtl tsod
            INNER JOIN trd_order tor ON tor.order_id = tsod.order_id
        WHERE
            tsod.pay_state IN ('1','3')
            AND tsod.create_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
            AND tsod.brand_id IN
            <foreach collection="reqVO.brandList" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
            <if test="reqVO.areaList != null">
                AND tor.area_id IN
                <foreach collection="reqVO.areaList" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
        GROUP BY
            tsod.spu_id
    </select>


    <!-- 获取业务员订单业务结算信息 (需日结数据)-->
    <select id="getColonelOrderBusinessSettle" resultType="com.zksr.trade.api.order.dto.ColonelOrderDaySettleDTO"
        parameterType="com.zksr.trade.api.order.vo.ColonelOrderDaySettleVO">
        SELECT
            table_order.*
            ,table_settle.percentageAmt
        FROM (
            SELECT
                table_order.colonel_id
                ,table_order.settleDate AS settleCreateDate
                ,SUM(table_order.orderQty) AS orderQty
                ,SUM(table_order.branchOrderAmt) AS branchOrderAmt
                ,SUM(table_order.businessOrderQty) AS businessOrderQty
                ,SUM(table_order.businessOrderAmt) AS businessOrderAmt
                ,SUM(table_order.saleBranchQty) AS saleBranchQty
                ,SUM(table_order.branchRefundAmt) AS branchRefundAmt
            FROM (
                <!-- 业务员销售订单 门店下单金额和数量-->
                SELECT
                    tor.colonel_id
                    ,DATE_FORMAT(tor.create_time, '%Y-%m-%d') AS settleDate
                    ,COUNT(tor.order_id) AS orderQty
                    ,SUM(tor.pay_amt) AS branchOrderAmt
                    ,0 AS businessOrderQty
                    ,0 AS businessOrderAmt
                    ,COUNT(DISTINCT tor.branch_id) AS saleBranchQty
                    ,0 AS branchRefundAmt
                FROM
                    trd_order tor
                WHERE
                    tor.sys_code = #{sysCode}
                    AND (tor.colonel_flag = 0 OR tor.colonel_flag IS NULL)
                    AND tor.colonel_id IN
                        <foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
                            #{colonelId}
                        </foreach>
                    AND tor.create_time BETWEEN CONCAT(#{startDate},' 00:00:00.000') AND CONCAT(#{endDate},' 23:59:59.999')
                GROUP BY
                    tor.colonel_id,
                    DATE_FORMAT(tor.create_time, '%Y-%m-%d')

                UNION ALL
                <!-- 业务员销售订单业务（代客订单）下单金额和数量-->
                SELECT
                    tor.colonel_id
                    ,DATE_FORMAT(tor.create_time, '%Y-%m-%d') AS settleDate
                    ,0 AS orderQty
                    ,0 AS branchOrderAmt
                    ,COUNT(tor.order_id) AS businessOrderQty
                    ,SUM(tor.pay_amt) AS businessOrderAmt
                    ,0 AS saleBranchQty
                    ,0 AS branchRefundAmt
                FROM
                    trd_order tor
                WHERE
                    tor.sys_code = #{sysCode}
                    AND tor.colonel_flag = 1
                    AND tor.colonel_id IN
                        <foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
                            #{colonelId}
                        </foreach>
                    AND tor.create_time BETWEEN CONCAT(#{startDate},' 00:00:00.000') AND CONCAT(#{endDate},' 23:59:59.999')
                GROUP BY
                    tor.colonel_id,
                    DATE_FORMAT(tor.create_time, '%Y-%m-%d')


                UNION ALL
                <!-- 业务员售后订单售后金额-->
                SELECT
                    tar.colonel_id
                    ,DATE_FORMAT(tar.create_time, '%Y-%m-%d') AS settleDate
                    ,0 AS orderQty
                    ,0 AS branchOrderAmt
                    ,0 AS businessOrderQty
                    ,0 AS businessOrderAmt
                    ,0 AS saleBranchQty
                    ,SUM(tar.refund_amt) AS branchRefundAmt
                FROM
                    trd_after tar
                WHERE
                    tar.sys_code = #{sysCode}
                    AND tar.colonel_id IN
                        <foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
                            #{colonelId}
                        </foreach>
                    AND tar.create_time BETWEEN CONCAT(#{startDate},' 00:00:00.000') AND CONCAT(#{endDate},' 23:59:59.999')
                GROUP BY
                    tar.colonel_id,
                    DATE_FORMAT(tar.create_time, '%Y-%m-%d')
            ) table_order
            GROUP BY
                table_order.colonel_id,
                table_order.settleDate

        ) table_order
        LEFT JOIN (
            <!-- 业务员订单提成结算金额-->
            SELECT
                table_settle.colonel_id
                ,table_settle.settleDate
                ,ROUND(SUM(table_settle.percentageAmt), 2) AS percentageAmt
            FROM (
                SELECT
                    tor.colonel_id
                    ,DATE_FORMAT(tor.create_time, '%Y-%m-%d') AS settleDate
                    ,IFNULL(SUM(tsos.colonel2_amt), 0) AS percentageAmt
                FROM
                    trd_order tor
                    LEFT JOIN trd_supplier_order_settle tsos ON tor.order_id = tsos.order_id
                WHERE
                    tor.sys_code = #{sysCode}
                    AND tor.colonel_id IN
                        <foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
                            #{colonelId}
                        </foreach>
                    AND tor.create_time BETWEEN CONCAT(#{startDate},' 00:00:00.000') AND CONCAT(#{endDate},' 23:59:59.999')
                    AND tsos.colonel2_amt > 0
                GROUP BY
                    tor.colonel_id,
                    DATE_FORMAT(tor.create_time, '%Y-%m-%d')

                UNION ALL
                <!-- 上级业务员订单提成结算金额-->
                SELECT
                    tor.pcolonel_id AS colonel_id
                    ,DATE_FORMAT(tor.create_time, '%Y-%m-%d') AS settleDate
                    ,IFNULL(SUM(tsos.colonel1_amt), 0) AS percentageAmt
                FROM
                    trd_order tor
                    LEFT JOIN trd_supplier_order_settle tsos ON tor.order_id = tsos.order_id
                WHERE
                    tor.sys_code = #{sysCode}
                    AND tor.pcolonel_id IN
                        <foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
                            #{colonelId}
                        </foreach>
                    AND tor.create_time BETWEEN CONCAT(#{startDate},' 00:00:00.000') AND CONCAT(#{endDate},' 23:59:59.999')
                    AND tsos.colonel1_amt > 0
                GROUP BY
                    tor.pcolonel_id,
                    DATE_FORMAT(tor.create_time, '%Y-%m-%d')

                UNION ALL
                <!-- 业务员订单售后提成结算金额-->
                SELECT
                    tar.colonel_id
                    ,DATE_FORMAT(tar.create_time, '%Y-%m-%d') AS settleDate
                    ,IFNULL(SUM(tsas.colonel2_amt), 0) * -1 AS percentageAmt
                FROM
                    trd_after tar
                    LEFT JOIN trd_supplier_after_settle tsas ON tar.after_id = tsas.after_id
                WHERE
                    tar.sys_code = #{sysCode}
                    AND tar.colonel_id IN
                        <foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
                            #{colonelId}
                        </foreach>
                    AND tar.create_time BETWEEN CONCAT(#{startDate},' 00:00:00.000') AND CONCAT(#{endDate},' 23:59:59.999')
                    AND tsas.colonel2_amt > 0
                GROUP BY
                    tar.colonel_id,
                    DATE_FORMAT(tar.create_time, '%Y-%m-%d')
                UNION ALL
                <!-- 上级业务员订单售后提成结算金额-->
                SELECT
                    tar.pcolonel_id AS colonel_id
                    ,DATE_FORMAT(tar.create_time, '%Y-%m-%d') AS settleDate
                    ,IFNULL(SUM(tsas.colonel1_amt), 0) * -1 AS percentageAmt
                FROM
                    trd_after tar
                    LEFT JOIN trd_supplier_after_settle tsas ON tar.after_id = tsas.after_id
                WHERE
                    tar.sys_code = #{sysCode}
                    AND tar.pcolonel_id IN
                    <foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
                        #{colonelId}
                    </foreach>
                    AND tar.create_time BETWEEN CONCAT(#{startDate},' 00:00:00.000') AND CONCAT(#{endDate},' 23:59:59.999')
                    AND tsas.colonel1_amt > 0
                GROUP BY
                    tar.pcolonel_id,
                    DATE_FORMAT(tar.create_time, '%Y-%m-%d')
            ) table_settle
            GROUP BY
                table_settle.colonel_id,
                table_settle.settleDate
        ) table_settle ON table_order.colonel_id = table_settle.colonel_id AND table_order.settleCreateDate = table_settle.settleDate
    </select>

    <select id="getBranchOrderSalesByParams"
            resultType="com.zksr.trade.api.order.dto.EsBranchOrderSalesRespDTO"
            parameterType="com.zksr.trade.api.order.vo.EsBranchOrderSalesReqVO">
        SELECT

            table_esBranch.branch_id
            , table_esBranch.sys_code
            , table_esBranch.createYearMonth
            , SUM(table_esBranch.payAmtSum) AS orderAmtSum
            , SUM(table_esBranch.orderCount) AS orderQtyCount
            , MAX(table_esBranch.lastBuyTime) lastBuyTime
            , MAX(table_esBranch.lastBuyAmt) lastBuyAmt
            , MAX(table_esBranch.lastBuyTime) lastAccessSystemTime
            , MAX(table_esBranch.lastBuyTime) lastLoginTime
        FROM (
            SELECT
                tor.branch_id
                , tor.sys_code
                , DATE_FORMAT(tor.create_time, '%Y-%m') AS createYearMonth
                , SUM(tor.pay_amt) AS payAmtSum
                , COUNT(tor.order_id) AS orderCount
                , MAX(tor.create_time) lastBuyTime
                , MAX(tor.pay_amt) as lastBuyAmt
            FROM
                trd_order tor
            WHERE
                tor.branch_id = #{branchId}
                AND tor.sys_code = #{sysCode}
                AND tor.pay_state IN (1, 3, 4)
	            AND tor.create_time BETWEEN CONCAT(#{startDate},' 00:00:00.000') AND CONCAT(#{endDate},' 23:59:59.999')
            GROUP BY
                tor.branch_id
                , tor.sys_code
                , DATE_FORMAT(tor.create_time, '%Y-%m')
            UNION ALL
            SELECT
                tar.branch_id
                , tar.sys_code
                , DATE_FORMAT(tar.create_time, '%Y-%m') AS createYearMonth
                , -SUM(tar.refund_amt) AS payAmtSum
                , 0 AS orderCount
                , NULL AS lastBuyTime
                , 0 AS lastBuyAmt
            FROM
                trd_after tar
            WHERE
                tar.branch_id = #{branchId}
                AND tar.sys_code = #{sysCode}
	            AND tar.create_time BETWEEN CONCAT(#{startDate},' 00:00:00.000') AND CONCAT(#{endDate},' 23:59:59.999')
            GROUP BY
                tar.branch_id
                , tar.sys_code
                , DATE_FORMAT(tar.create_time, '%Y-%m')
        ) table_esBranch
        GROUP BY
            table_esBranch.branch_id
            , table_esBranch.sys_code
            , table_esBranch.createYearMonth
        ORDER BY
            table_esBranch.branch_id
            , table_esBranch.createYearMonth ASC
    </select>
    <select id="getOperatorOrderPageListNewPrint"
            resultType="com.zksr.trade.controller.order.vo.PcOrderPrintDetailVO">
        SELECT
            tso.create_time,
            tso.order_no,
            tso.supplier_order_no,
            tso.order_id,
            tor.member_id,
            tso.supplier_order_id,
            tso.supplier_id,
            tsod.sku_id,
            MAX(tsod.spu_id) spuId,
            tor.branch_id,
            tor.pay_way,
            MAX(tsod.order_unit) orderUnit,
            SUM(IFNULL((tsod.price * tsod.order_unit_size), 0)) AS price,
            SUM(tsod.total_num - tsod.cancel_qty) AS num,
            SUM(tsod.total_amt - tsod.cancel_amt) AS totalAmt,
            tor.memo,
            tso.print_qty
        FROM
            trd_supplier_order tso
            LEFT JOIN trd_supplier_order_dtl tsod ON tso.supplier_order_id = tsod.supplier_order_id
            LEFT JOIN trd_order tor ON tor.order_id = tso.order_id
        WHERE
            tso.order_no IN
            <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
                #{orderNo}
            </foreach>
            AND tor.pay_state IN (1, 3, 4)
        GROUP BY
            tso.supplier_order_id,
            tsod.sku_id
        HAVING
            num > 0
    </select>
    <select id="getSupplierOrderOpenDTOList"
            resultMap="OrderWithDetailsResultMap">
        SELECT
            /* -- 主订单数据*/
            tso.supplier_order_id
            ,tso.supplier_order_no
            ,tor.order_id
            ,tor.order_no
            ,tor.branch_id
            ,tso.trans_amt
            ,tor.create_by
            ,tor.create_time
            ,tso.supplier_id
            ,tso.supplier_name
            ,tor.memo
            ,tso.sub_discount_amt
            ,tso.sub_pay_amt
            ,tso.sub_order_amt
            ,tso.sub_order_num
            ,tso.source_order_no
            ,tso.pay_state
            ,tor.pay_time
            ,tor.pay_way
            ,tor.order_type
            ,tor.colonel_flag
            ,tor.colonel_id
            ,tso.push_status

            /*-- 入驻商订单明细信息*/
            ,tsod.supplier_order_dtl_no
            ,tsod.supplier_order_dtl_id
            ,tsod.sku_id
            ,tsod.spu_name
            ,tsod.thumb_video
            ,tsod.thumb
            ,tsod.details
            ,IFNULL((tsod.total_num - tsod.cancel_qty), 0) AS totalNum
            ,CASE tsod.delivery_state
            WHEN 2 THEN tsod.total_amt
            ELSE tsod.total_amt - tsod.cancel_amt END AS totalAmt
            ,(tsod.price * tsod.order_unit_size) AS price
            ,CASE tsod.delivery_state
            WHEN '40' THEN 4
            WHEN '41' THEN 4
            WHEN '7'  THEN 3
            ELSE tsod.delivery_state
            END AS deliveryState
            ,tsod.gift_flag
            ,tsod.memo
            ,tsod.spec_name
            ,tsod.item_type
            ,tsod.line_num
            ,tsod.order_unit
            ,tsod.order_unit_type
            ,tsod.order_unit_qty
            ,tsod.order_unit_size
            ,tsod.order_unit_price
            ,tsod.coupon_discount_amt
            ,tsod.coupon_discount_amt2
            ,tsod.activity_discount_amt
            ,tsod.brand_id
            ,tsod.is_proceeds
            ,tsod.hdfk_pay_id
            ,tsod.sync_stock
            ,tsod.category_id
            ,tsod.supplier_item_id
            ,tsod.area_item_id
            ,tsod.exact_price
            ,tsod.exact_total_amt
            ,ROUND(tsod.order_unit_price / tsod.order_unit_size, 6) AS sale_price
        FROM
            trd_order tor
            INNER JOIN (
                SELECT
                    tor.order_id
                FROM trd_supplier_order tso
                    LEFT JOIN trd_order tor ON tso.order_id = tor.order_id
                WHERE
                    tso.supplier_id = #{supplierId}
                    AND tso.push_status = 0
                    AND (
                        (tor.pay_state = 1 AND tor.pay_way = 0 AND DATE_ADD(tor.pay_time, INTERVAL #{orderDelayPushTime} SECOND) &lt;  NOW())
                        OR
                        (tor.pay_way = 2 AND DATE_ADD(tor.create_time, INTERVAL #{orderDelayPushTime} SECOND) &lt;  NOW())
                        )
                    <if test="reqVO.startTime != null and reqVO.startTime != ''">
                        AND ((tor.pay_way = 0 AND tor.pay_time >= #{reqVO.startTime}) OR
                        (tor.pay_way = 2 AND tor.create_time >= #{reqVO.startTime}))
                    </if>
                    <if test="reqVO.endTime != null and reqVO.endTime != ''">
                        AND ((tor.pay_way = 0 AND tor.pay_time &lt;= #{reqVO.endTime}) OR
                        (tor.pay_way = 2 AND tor.create_time &lt;= #{reqVO.endTime}))
                    </if>
                        ORDER BY tor.create_time DESC
                        LIMIT #{reqVO.pageNo}, #{reqVO.pageSize}
            ) pageOrder ON pageOrder.order_id = tor.order_id
            INNER JOIN trd_supplier_order tso ON tor.order_id = tso.order_id
            INNER JOIN trd_supplier_order_dtl tsod ON tsod.supplier_order_id = tso.supplier_order_id
            WHERE tsod.delivery_state != 2
            ORDER BY tor.create_time DESC
    </select>
    <select id="getSupplierOrderOpenDTOListCount" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
            trd_order tor
        INNER JOIN (
            SELECT
                tor.order_id
            FROM trd_supplier_order tso
                LEFT JOIN trd_order tor ON tso.order_id = tor.order_id
            WHERE
                tso.supplier_id = #{supplierId}
                AND tso.push_status = 0
                AND (
                (tor.pay_state = 1 AND tor.pay_way = 0 AND DATE_ADD(tor.pay_time, INTERVAL #{orderDelayPushTime} SECOND) &lt;  NOW())
                    OR
                (tor.pay_way = 2 AND DATE_ADD(tor.create_time, INTERVAL #{orderDelayPushTime} SECOND) &lt;  NOW())
                )
                <if test="reqVO.startTime != null and reqVO.startTime != ''">
                    AND ((tor.pay_way = 0 AND tor.pay_time >= #{reqVO.startTime}) OR
                    (tor.pay_way = 2 AND tor.create_time >= #{reqVO.startTime}))
                </if>
                <if test="reqVO.endTime != null and reqVO.endTime != ''">
                    AND ((tor.pay_way = 0 AND tor.pay_time &lt;= #{reqVO.endTime}) OR
                    (tor.pay_way = 2 AND tor.create_time &lt;= #{reqVO.endTime}))
                </if>
            ORDER BY tor.create_time DESC
        ) pageOrder ON pageOrder.order_id = tor.order_id
        INNER JOIN trd_supplier_order tso ON tor.order_id = tso.order_id
        INNER JOIN trd_supplier_order_dtl tsod ON tsod.supplier_order_id = tso.supplier_order_id
        WHERE tsod.delivery_state != 2
        ORDER BY tor.create_time DESC
    </select>
    <select id="getSupplierAfterOrderOpenDTOList"
            resultMap="AfterOrderWithDetailsResultMap">
        SELECT
            tsa.*,
            afterDtl.*,
            ta.branch_id,
            ta.colonel_id
        FROM
            (
                SELECT DISTINCT
                    tsa.after_id
                FROM
                    trd_supplier_after tsa
                    LEFT JOIN trd_supplier_after_dtl afterDtl ON tsa.after_id = afterDtl.after_id
                WHERE
                    tsa.supplier_id = #{supplierId}
                  AND tsa.push_status = 0
                  AND afterDtl.return_state = 1
                  AND afterDtl.after_phase = 2
                  <if test="reqVO.startTime != null and reqVO.startTime != '' and reqVO.endTime != null and reqVO.endTime != ''">
                     AND afterDtl.return_approve_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                  </if>
                LIMIT #{reqVO.pageNo}, #{reqVO.pageSize}
            ) AS paginated_after
                LEFT JOIN trd_supplier_after tsa ON paginated_after.after_id = tsa.after_id
                LEFT JOIN trd_supplier_after_dtl afterDtl ON tsa.after_id = afterDtl.after_id
                LEFT JOIN trd_after ta ON paginated_after.after_id = ta.after_id
        ORDER BY
            tsa.create_time DESC;
    </select>
    <select id="getSupplierAfterOrderOpenDTOListCount" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT tsa.after_id)
        FROM
            trd_supplier_after tsa
            LEFT JOIN trd_supplier_after_dtl afterDtl ON tsa.after_id = afterDtl.after_id
        WHERE
            tsa.supplier_id = #{supplierId}
            AND tsa.push_status = 0
            AND afterDtl.return_state = 1
            AND afterDtl.after_phase = 2
            <if test="reqVO.startTime != null and reqVO.startTime != '' and reqVO.endTime != null and reqVO.endTime != ''">
                AND afterDtl.return_approve_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
            </if>
    </select>

    <select id="getOrderAndAfterSettleIdBySheetNo" resultType="java.lang.Long">
        SELECT
            tsod.supplier_order_dtl_id
        FROM
            trd_supplier_order_dtl tsod
            INNER JOIN trd_order tor ON tsod.order_id = tor.order_id
        WHERE
            tor.order_no = #{sheetNo}

        UNION ALL

        SELECT
            tsod.supplier_order_dtl_id
        FROM
            trd_supplier_order_dtl tsod
        WHERE
            tsod.hdfk_pay_id = (
                SELECT
                    thp.hdfk_pay_id
                FROM
                    trd_hdfk_pay thp
                WHERE
                    thp.hdfk_pay_no = #{sheetNo}
            )
    </select>
    <select id="getSupplierOrderDtlInfoExport"
            resultType="com.zksr.trade.api.order.vo.SupplierOrderDtlInfoExportVO"
            parameterType="com.zksr.trade.controller.order.vo.DcOrderPageReqVO">
        SELECT
            tso.supplier_order_no AS supplierOrderNo <!-- 入驻商订单号 -->
            , tsord.spu_id
            , tsord.sku_id
            , tsord.gift_flag <!-- 是否是赠品 -->
            , tsord.order_unit_type <!-- 下单单位大小 -->
            , tsord.order_unit <!-- 下单单位 -->
            , IFNULL((tsord.price * tsord.order_unit_size), 0) AS price <!-- 下单单位单价 -->
            , IFNULL((tsord.total_num / tsord.order_unit_size), 0) AS totalNum <!-- 下单单位数量 -->
            , tsord.total_amt		 <!-- 下单单位金额 -->
            ,tso.memo <!-- 入驻商订单备注 -->
            ,tor.create_time createDate
            ,tor.create_time
            ,YEAR(tor.create_time) createTimeYear
            ,MONTH(tor.create_time) createTimeMonth
            ,DAY(tor.create_time) createTimeDay
            ,tor.branch_id
            ,tor.dc_id
            ,tor.area_id
            ,tor.colonel_id
            ,tor.pcolonel_id
            ,tor.pay_way
            ,CASE tsord.delivery_state
            WHEN '1' THEN '待付款'
            WHEN '2' THEN '订单取消'
            WHEN '3' THEN '待发货'
            WHEN '7' THEN '待发货'
            WHEN '4' THEN  '待收货'
            WHEN '40' THEN '待收货'
            WHEN '41' THEN '待收货'
            WHEN '5' THEN   '已收货'
            WHEN '6' THEN  '已完成'
            WHEN '21' THEN '售后中'
            WHEN '待收货' THEN '已完成'
            ELSE tsord.delivery_state END AS delivery
            ,tso.delivery_state
            ,tor.order_id
            ,tor.order_no
            ,tso.supplier_id
            ,tor.pcolonel_id
            ,tor.order_type
            ,IFNULL((tsord.total_num / tsord.order_unit_size), 0) AS demandNum
            ,IFNULL(tsord.sub_order_amt, 0) AS saleOrderAmt
            ,tsord.supplier_order_id
            ,tsord.area_item_id
            ,tsord.supplier_item_id
            ,tso.source_order_no
            ,tor.pay_time
            ,tsord.order_unit
            ,tsord.total_amt AS demandAmt
            ,IFNULL(tsord.sub_order_amt, 0) AS saleTotalAmt
            ,IFNULL((tsord.coupon_discount_amt + tsord.coupon_discount_amt2 + tsord.activity_discount_amt), 0) AS discountAmt
            ,tso.print_state
            ,tor.pay_state
            ,tso.delivery_state as deliveryState
            ,tso.push_status
        FROM
            trd_order tor
        INNER JOIN trd_supplier_order tso ON tor.order_id = tso.order_id
        INNER JOIN trd_supplier_order_dtl tsord ON tso.supplier_order_id = tsord.supplier_order_id
        <where>
            <!-- 单据类型 -->
            <if test="null != param.itemType">
                AND tor.order_type = #{param.itemType}
            </if>
            <!-- 入驻商 -->
            <if test="null != param.supplierIdList and param.supplierIdList.size > 0 ">
                AND tso.supplier_id IN
                <foreach collection="param.supplierIdList" item="supplierId" open="(" separator="," close=")">
                    #{supplierId}
                </foreach>
            </if>
            <if test="null != param.payState">
                AND tor.pay_state = #{param.payState}
            </if>
            <if test="null != param.payWay">
                AND tor.pay_way = #{param.payWay}
            </if>
            <!-- 订单状态 -->
            <if test="null != param.deliveryStates and param.deliveryStates.size > 0 ">
                AND tso.delivery_state IN
                <foreach collection="param.deliveryStates" item="deliveryState" open="(" separator="," close=")">
                    #{deliveryState}
                </foreach>
            </if>
            <!-- 商品SPU -->
            <if test="null != spuIds and spuIds.size > 0 ">
                AND tsord.spu_id IN
                <foreach collection="spuIds" item="spuId" open="(" separator="," close=")">
                    #{spuId}
                </foreach>
            </if>
            <!-- 门店 -->
            <if test="null != param.branchIds and param.branchIds.size > 0 ">
                AND tor.branch_id IN
                <foreach collection="param.branchIds" item="branchId" open="(" separator="," close=")">
                    #{branchId}
                </foreach>
            </if>

            <!-- 商品SKU -->
            <if test="null != skuIds and skuIds.size > 0 ">
                AND tsord.sku_id IN
                <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
            <!-- 业务员 -->
            <if test="null != param.colonelIds and param.colonelIds.size > 0 ">
                AND tor.colonel_id IN
                <foreach collection="param.colonelIds" item="colonelId" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
            <!-- 订单号 -->
            <if test="null != param.orderNo and param.orderNo != ''">
                AND tor.order_no LIKE concat('%', #{param.orderNo}, '%')
            </if>
            <!-- 入驻商订单号 -->
            <if test="null != param.subOrderNo and param.subOrderNo != ''">
                AND tsord.supplier_order_no LIKE concat('%', #{param.subOrderNo}, '%')
            </if>
            <!-- 查询时间  创建时间或支付时间 -->
            <if test="null != param.startTime and null != param.endTime ">
                AND tor.create_time
                BETWEEN #{param.startTime} AND #{param.endTime}
            </if>
            <if test="null != param.payStartTime and null != param.payEndTime ">
                AND tor.pay_time
                BETWEEN #{param.payStartTime} AND #{param.payEndTime}
            </if>
            <!-- 订单状态 -->
<!--            <if test="null != param.orderType and param.orderType != 100">-->
<!--                <if test="param.orderType == 0"> &lt;!&ndash; 待付款&ndash;&gt;-->
<!--                    AND tor.pay_state = #{param.orderType}-->
<!--                </if>-->
<!--                <if test="param.orderType == 2"> &lt;!&ndash; 订单取消 &ndash;&gt;-->
<!--                    AND (tor.pay_state = #{param.orderType} OR tsord.delivery_state = #{param.orderType})-->
<!--                </if>-->
<!--                <if test="param.orderType == 3"> &lt;!&ndash; 代发货包含（备货中 7）&ndash;&gt;-->
<!--                    AND tsord.delivery_state IN (#{param.orderType}, '7')-->
<!--                </if>-->
<!--                <if test="param.orderType == 4"> &lt;!&ndash; 已发货包含（待装车 40、已装车 41） &ndash;&gt;-->
<!--                    AND tsord.delivery_state IN (#{param.orderType} , '40', '41')-->
<!--                </if>-->
<!--                <if test="param.orderType == 5"> &lt;!&ndash; 交易完成包含（订单收货 5 ，订单已完成 6 ） &ndash;&gt;-->
<!--                    AND tsord.delivery_state IN (#{param.orderType}, '6')-->
<!--                </if>-->
<!--            </if>-->
            <if test="null != param.subOrderId">
                AND tsord.supplier_order_id = #{param.subOrderId}
            </if>
            <!-- 外部单号 -->
            <if test="null != param.sourceOrderNo and '' != param.sourceOrderNo">
                AND tso.source_order_no LIKE concat('%', #{param.sourceOrderNo}, '%')
            </if>
            <if test="param.dcId != null">
                and tor.dc_id = #{param.dcId}
            </if>
            <if test="param.supplierId != null">
                and tso.supplier_id = #{param.supplierId}
            </if>
            <if test="null != param.orderNoList and param.orderNoList.size > 0">
                tso.order_no IN
                <foreach collection="param.orderNoList" item="orderNo" open="(" separator="," close=")">
                    #{orderNo}
                </foreach>
            </if>
            <if test="null != param.isDebtOrder and 1 == param.isDebtOrder">
                AND tsord.pay_state = '3'
                AND tsord.delivery_state != 2
                <if test="null != param.dcId">
                    AND tor.dc_id = #{param.dcId}
                </if>
                <if test="null != param.supplierId">
                    AND tsord.supplier_id = #{param.supplierId}
                </if>
            </if>
        </where>
        ${param.params.dataScope}
        ORDER BY tor.create_time DESC
        <if test="null != param.pageNo and null != param.pageSize">
            LIMIT #{param.pageNo}, #{param.pageSize}
        </if>
    </select>

    <select id="getHomePagesCurrentSalesData"
            resultType="com.zksr.report.api.homePages.dto.HomePagesCurrentSalesDataRespDTO">
        SELECT
            dataTable.sys_code,
            <if test="null != isDc and isDc != 0">
                dataTable.dc_id,
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                dataTable.supplier_id,
            </if>
            SUM(dataTable.todayOrderTotalAmt) AS todayOrderTotalAmt,
            SUM(dataTable.yesterdayOrderTotalAmt) AS yesterdayOrderTotalAmt,
            SUM(dataTable.debtTotalAmt) AS debtTotalAmt,
            SUM(dataTable.debtOrderTotalQty) AS debtOrderTotalQty,
            SUM(dataTable.debtBranchTotalQty) AS debtBranchTotalQty,
            SUM(dataTable.afterTotalAmt) AS afterTotalAmt,
            SUM(dataTable.afterTotalQty) AS afterTotalQty,
            SUM(dataTable.afterBranchTotalQty) AS afterBranchTotalQty
        FROM (
            SELECT
                orderTable.sys_code,
                <if test="null != isDc and isDc != 0">
                    orderTable.dc_id,
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    orderTable.supplier_id,
                </if>
                SUM(IFNULL(orderTable.todayOrderTotalAmt, 0)) AS todayOrderTotalAmt,
                CASE
                    WHEN SUM(IFNULL( orderTable.yesterdayOrderTotalAmt, 0 )) > 0 THEN SUM(IFNULL( orderTable.yesterdayOrderTotalAmt, 0 ))
                    ELSE 1
                END AS yesterdayOrderTotalAmt,
                0 AS debtTotalAmt,
                0 AS debtOrderTotalQty,
                0 AS debtBranchTotalQty,
                0 AS afterTotalAmt,
                0 AS afterTotalQty,
                0 AS afterBranchTotalQty
            FROM (
                SELECT
                    tsod.sys_code,
                    <if test="null != isDc and isDc != 0">
                        tor.dc_id,
                    </if>
                    <if test="null != isSupplier and isSupplier != 0">
                        tsod.supplier_id,
                    </if>
                    SUM(tsod.res_amt - tsod.cancel_amt) AS todayOrderTotalAmt,
                    0 AS yesterdayOrderTotalAmt
                FROM
                    trd_order tor
                    LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
                WHERE
                    tor.pay_state IN (1, 3, 4)
                    AND tor.create_time BETWEEN  CONCAT(#{startDate}, ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                    AND tsod.delivery_state != 2
                    AND tor.sys_code = #{sysCode}
                GROUP BY
                    tsod.sys_code
                    <if test="null != isDc and isDc != 0">
                        , tor.dc_id
                    </if>
                    <if test="null != isSupplier and isSupplier != 0">
                        , tsod.supplier_id
                    </if>

                UNION ALL

                SELECT
                    tsod.sys_code,
                    <if test="null != isDc and isDc != 0">
                        tor.dc_id,
                    </if>
                    <if test="null != isSupplier and isSupplier != 0">
                        tsod.supplier_id,
                    </if>
                    0 AS todayOrderTotalAmt,
                    SUM(tsod.res_amt - tsod.cancel_amt) AS yesterdayOrderTotalAmt
                FROM
                    trd_order tor
                    LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
                WHERE
                    tor.pay_state IN (1, 3, 4)
                    AND tor.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), ' 00:00:00.000') AND CONCAT(DATE_SUB(#{endDate}, INTERVAL 1 DAY), ' 23:59:59.999')
                    AND tsod.delivery_state != 2
                    AND tor.sys_code = #{sysCode}
                GROUP BY
                    tsod.sys_code
                    <if test="null != isDc and isDc != 0">
                        , tor.dc_id
                    </if>
                    <if test="null != isSupplier and isSupplier != 0">
                        , tsod.supplier_id
                    </if>
            ) orderTable
            GROUP BY
                orderTable.sys_code
                <if test="null != isDc and isDc != 0">
                    , orderTable.dc_id
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    , orderTable.supplier_id
                </if>

            UNION ALL

            SELECT
                tor.sys_code,
                <if test="null != isDc and isDc != 0">
                    tor.dc_id,
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    tsod.supplier_id,
                </if>
                0 AS todayOrderTotalAmt,
                0 AS yesterdayOrderTotalAmt,
                SUM(tsod.total_amt) AS debtTotalAmt,
                COUNT(DISTINCT tsod.supplier_order_id) AS debtOrderTotalQty,
                COUNT(DISTINCT tor.branch_id) AS debtBranchTotalQty,
                0 AS afterTotalAmt,
                0 AS afterTotalQty,
                0 AS afterBranchTotalQty
            FROM
                trd_order tor
                LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
            WHERE
                tsod.pay_state = 3
                AND tor.create_time &lt;= CONCAT(#{endDate}, ' 23:59:59.999')
                AND tsod.delivery_state != 2
                AND tor.sys_code = #{sysCode}
            GROUP BY
                tor.sys_code
                <if test="null != isDc and isDc != 0">
                    , tor.dc_id
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    , tsod.supplier_id
                </if>

            UNION ALL

            SELECT
                tsad.sys_code,
                <if test="null != isDc and isDc != 0">
                    tar.dc_id,
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    tsad.supplier_id,
                </if>
                0 AS todayOrderTotalAmt,
                0 AS yesterdayOrderTotalAmt,
                0 AS debtTotalAmt,
                0 AS debtOrderTotalQty,
                0 AS debtBranchTotalQty,
                SUM(tsad.return_amt) AS afterTotalAmt,
                COUNT(DISTINCT tar.after_id) AS afterTotalQty,
                COUNT(DISTINCT tar.branch_id) AS afterBranchTotalQty
            FROM
                trd_supplier_after_dtl tsad
                LEFT JOIN trd_after tar ON tsad.after_id = tar.after_id
            WHERE
                tsad.after_phase = 2
                AND tsad.approve_state != 2
                AND tsad.refund_state != 2
                AND tsad.sys_code = #{sysCode}
            GROUP BY
                tsad.sys_code
                <if test="null != isDc and isDc != 0">
                    , tar.dc_id
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    , tsad.supplier_id
                </if>
        ) dataTable
        GROUP BY
            dataTable.sys_code
            <if test="null != isDc and isDc != 0">
                , dataTable.dc_id
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                , dataTable.supplier_id
            </if>
    </select>

    <select id="getHomePagesOrderSalesData"
            resultType="com.zksr.report.api.homePages.dto.HomePagesOrderSalesDataRespDTO">
        SELECT
            orderTable.sys_code,
            <if test="null != isDc and isDc != 0">
                orderTable.dc_id,
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                orderTable.supplier_id,
            </if>
            SUM(orderTable.orderAmt) AS orderAmt,
            SUM(orderTable.orderQty) AS orderQty,
            SUM(orderTable.orderBranchQty) AS orderBranchQty,
            SUM(orderTable.orderSkuQty) AS orderSkuQty,
            SUM(orderTable.orderBranchAvgAmt) AS orderBranchAvgAmt,
            SUM(orderTable.debtOrderAmt) AS debtOrderAmt,
            SUM(orderTable.debtOrderQty) AS debtOrderQty,
            SUM(orderTable.debtOrderBranchQty) AS debtOrderBranchQty,
            SUM(orderTable.beforeOrderAmt) AS beforeOrderAmt,
            SUM(orderTable.beforeOrderQty) AS beforeOrderQty,
            SUM(orderTable.beforeOrderBranchQty) AS beforeOrderBranchQty,
            SUM(orderTable.beforeOrderSkuQty) AS beforeOrderSkuQty,
            SUM(orderTable.beforeOrderBranchAvgAmt) AS beforeOrderBranchAvgAmt,
            ROUND(((SUM(orderTable.orderQty) - SUM(orderTable.beforeOrderQty)) / SUM(orderTable.beforeOrderQty)) * 100, 2) AS orderQtyRate,
            ROUND(((SUM(orderTable.orderBranchQty) - SUM(orderTable.beforeOrderBranchQty)) / SUM(orderTable.beforeOrderBranchQty)) * 100, 2) AS orderBranchQtyRate,
            ROUND(((SUM(orderTable.orderBranchAvgAmt) - SUM(orderTable.beforeOrderBranchAvgAmt))/ SUM(orderTable.beforeOrderBranchAvgAmt)) * 100, 2) AS orderBranchAmtAvgRate
        FROM (
            SELECT
                tor.sys_code,
                <if test="null != isDc and isDc != 0">
                    tor.dc_id,
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    tsod.supplier_id,
                </if>
                SUM(tsod.res_amt - tsod.cancel_amt) AS orderAmt,
                COUNT(DISTINCT tor.order_id) AS orderQty,
                COUNT(DISTINCT tor.branch_id) AS orderBranchQty,
                COUNT( DISTINCT tsod.sku_id ) AS orderSkuQty,
                ROUND(SUM(tsod.res_amt - tsod.cancel_amt) / COUNT(DISTINCT tor.branch_id), 2) AS orderBranchAvgAmt,
                SUM(CASE WHEN tsod.pay_state = 3 THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS debtOrderAmt,
                COUNT(DISTINCT CASE WHEN tsod.pay_state = 3 THEN tor.order_id END) AS debtOrderQty,
                COUNT(DISTINCT CASE WHEN tsod.pay_state = 3 THEN tor.branch_id END) AS debtOrderBranchQty,
                0 AS beforeOrderAmt,
                0 AS beforeOrderQty,
                0 AS beforeOrderBranchQty,
                0 AS beforeOrderSkuQty,
                0 AS beforeOrderBranchAvgAmt
            FROM
                trd_order tor
                LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
            WHERE
                tor.pay_state IN (1, 3, 4)
                AND tor.sys_code = #{sysCode}
                AND tor.create_time BETWEEN CONCAT(#{startDate}, ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                AND tsod.delivery_state != 2
            GROUP BY
                tor.sys_code
                <if test="null != isDc and isDc != 0">
                    , tor.dc_id
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    , tsod.supplier_id
                </if>

            UNION ALL

            SELECT
                tor.sys_code,
                <if test="null != isDc and isDc != 0">
                    tor.dc_id,
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    tsod.supplier_id,
                </if>
                0 AS orderAmt,
                0 AS orderQty,
                0 AS orderBranchQty,
                0 AS orderSkuQty,
                0 AS orderBranchAvgAmt,
                0 AS debtOrderAmt,
                0 AS debtOrderQty,
                0 AS debtOrderBranchQty,
                SUM(tsod.res_amt - tsod.cancel_amt) AS beforeOrderAmt,
                COUNT(DISTINCT tor.order_id) AS beforeOrderQty,
                COUNT(DISTINCT tor.branch_id) AS beforeOrderBranchQty,
                COUNT( DISTINCT tsod.sku_id ) AS beforeOrderSkuQty,
                ROUND(SUM(tsod.res_amt - tsod.cancel_amt) / COUNT(DISTINCT tor.branch_id), 2) AS beforeOrderBranchAvgAmt
            FROM
                trd_order tor
                LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
            WHERE
                tor.pay_state IN (1, 3, 4)
                AND tor.sys_code = #{sysCode}
                AND tor.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), ' 00:00:00.000') AND CONCAT(DATE_SUB(#{endDate}, INTERVAL 1 DAY), ' 23:59:59.999')
                AND tsod.delivery_state != 2
            GROUP BY
                tor.sys_code
            <if test="null != isDc and isDc != 0">
                , tor.dc_id
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                , tsod.supplier_id
            </if>
        ) AS orderTable
        GROUP BY
            orderTable.sys_code
            <if test="null != isDc and isDc != 0">
                , orderTable.dc_id
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                , orderTable.supplier_id
            </if>

    </select>


    <select id="getHomePagesAreaSalesTop10Data" resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            tor.sys_code,
            tor.area_id AS salesTypeId,
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(#{endDate}, '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 1 DAY), '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            trd_order tor
                LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
        WHERE
            tor.pay_state IN (1, 3, 4)
          AND tor.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
          AND tsod.delivery_state != 2
                AND tor.sys_code = #{sysCode}
        GROUP BY
            tor.sys_code,
            tor.area_id
    </select>
    <select id="getHomePagesDcSalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            tor.sys_code,
            tor.dc_id AS salesTypeId,
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(#{endDate}, '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 1 DAY), '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            trd_order tor
                LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
        WHERE
            tor.pay_state IN (1, 3, 4)
          AND tor.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
          AND tsod.delivery_state != 2
                AND tor.sys_code = #{sysCode}
        GROUP BY
            tor.sys_code,
            tor.dc_id
    </select>
    <select id="getHomePagesItemSalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            tor.sys_code,
            tsod.spu_id AS salesTypeId,
            <if test="null != isDc and isDc != 0">
                tor.dc_id,
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                tsod.supplier_id,
            </if>
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(#{endDate}, '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 1 DAY), '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            trd_order tor
                LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
        WHERE
            tor.pay_state IN (1, 3, 4)
          AND tor.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
          AND tsod.delivery_state != 2
                AND tor.sys_code = #{sysCode}
        GROUP BY
            tor.sys_code,
            tsod.spu_id
            <if test="null != isDc and isDc != 0">
                , tor.dc_id
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                , tsod.supplier_id
            </if>
    </select>
    <select id="getHomePagesCategorySalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            tor.sys_code,
            tsod.category_id AS salesTypeId,
            <!-- 这里是用于 查询一级管理分类动销数据-->
            <if test="null != isDc and isDc != 0">
                tor.dc_id,
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                tsod.supplier_id,
            </if>
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(#{endDate}, '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 1 DAY), '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            trd_order tor
            LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
        WHERE
            tor.pay_state IN (1, 3, 4)
            AND tor.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            AND tsod.delivery_state != 2
            AND tor.sys_code = #{sysCode}
            <if test="null != categoryIds and categoryIds.size > 0">
                AND tsod.category_id IN
                <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
        GROUP BY
            tor.sys_code,
            tsod.category_id
            <if test="null != isDc and isDc != 0">
                , tor.dc_id
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                , tsod.supplier_id
            </if>
    </select>

    <select id="getHomePagesSupplierSalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            tor.sys_code,
            tor.dc_id,
            tsod.supplier_id AS salesTypeId,
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(#{endDate}, '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 1 DAY), '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            trd_order tor
            LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
        WHERE
            tor.pay_state IN (1, 3, 4)
            AND tor.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            AND tsod.delivery_state != 2
            AND tor.sys_code = #{sysCode}
        GROUP BY
            tor.sys_code,
            tor.dc_id,
            tsod.supplier_id
    </select>
    <select id="getHomePagesColonelSalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            tor.sys_code,
            tor.dc_id,
            tor.colonel_id AS salesTypeId,
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(#{endDate}, '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 1 DAY), '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            trd_order tor
            LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
        WHERE
            tor.pay_state IN (1, 3, 4)
            AND tor.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            AND tsod.delivery_state != 2
            AND tor.colonel_id IS NOT NULL
            AND tor.sys_code = #{sysCode}
        GROUP BY
            tor.sys_code,
            tor.dc_id,
            tor.colonel_id
    </select>
    <select id="getHomePagesBranchSalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            tor.sys_code,
            tor.branch_id AS salesTypeId,
            <if test="null != isDc and isDc != 0">
                tor.dc_id,
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                tsod.supplier_id,
            </if>
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(#{endDate}, '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(tor.create_time, '%Y%m%d') = DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 1 DAY), '%Y%m%d') THEN tsod.res_amt - tsod.cancel_amt ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            trd_order tor
            LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
        WHERE
            tor.pay_state IN (1, 3, 4)
            AND tor.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 DAY), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            AND tsod.delivery_state != 2
            AND tor.sys_code = #{sysCode}
        GROUP BY
            tor.sys_code,
            tor.branch_id
            <if test="null != isDc and isDc != 0">
                , tor.dc_id
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                , tsod.supplier_id
            </if>
    </select>

    <!-- 根据 用户账号或门店账号查询出是否下过订单 -->
    <select id="checkMemberOrBranchExistsOrderSaleInfo" resultType="java.lang.Long">
        SELECT
            <if test="type == 'member' ">
                member_id
            </if>
            <if test="type == 'branch' ">
                branch_id
            </if>
        FROM
            trd_order
        WHERE
            sys_code = #{sysCode}
            <if test="type == 'member' ">
                AND member_id IN
            </if>
            <if test="type == 'branch' ">
                AND branch_id IN
            </if>
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        GROUP BY
            <if test="type == 'member' ">
                member_id
            </if>
            <if test="type == 'branch' ">
                branch_id
            </if>
    </select>

    <!-- 获取业务员App订单 (新) -->
    <select id="selectMemColoneAppOrderNew" resultMap="orderListNew" parameterType="com.zksr.trade.api.order.vo.TrdColonelAppOrderListPageReqVO">
        SELECT
            /* -- 主订单数据*/
            tor.order_id
            ,tor.order_no
            ,tor.member_id
            ,tor.branch_id
            ,tor.create_time
            ,tor.pay_state
            ,tor.pay_way
            ,tor.order_type

            /*-- 入驻商订单数据*/
            ,tso.supplier_id
            ,tso.supplier_name
            ,tso.supplier_order_id
            ,tso.supplier_order_no
            ,CASE tsod.delivery_state
                WHEN '40' THEN 4
                WHEN '41' THEN 4
                WHEN '7'  THEN 3
                ELSE tsod.delivery_state
            END AS deliveryState

            /*-- 入驻商订单明细信息*/
            ,tsod.supplier_order_dtl_id
            ,tsod.spu_id
            ,tsod.sku_id
            ,CASE tsod.delivery_state
                WHEN 2 THEN IFNULL((tsod.total_num / tsod.order_unit_size), 0)
                ELSE  IFNULL(((tsod.total_num - tsod.cancel_qty) / tsod.order_unit_size), 0)
            END AS totalNum
            ,CASE tsod.delivery_state
                WHEN 2 THEN tsod.total_amt
                ELSE tsod.total_amt - tsod.cancel_amt
            END AS totalAmt
            ,ROUND(IFNULL(tsod.order_sales_unit_price, tsod.exact_price * tsod.order_unit_size), 2) AS price
            ,IFNULL((tsod.coupon_discount_amt + tsod.coupon_discount_amt2 + tsod.activity_discount_amt), 0) AS discountAmt
            ,tsod.item_type
        FROM
            trd_order tor
            INNER JOIN (
                SELECT
                    tor.order_id
                    ,tsod.delivery_state
                FROM
                    trd_order tor
                    LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
                WHERE
                    tor.pay_state IN (1, 3, 4) <!-- 这里只查询已付款或货到付款的单据 且 不显示已取消的订单数据-->
                    AND tsod.delivery_state != 2
                    <!-- 业务员 -->
                    <if test="null != colonelIds and colonelIds.size > 0 ">
                        AND tor.colonel_id IN
                        <foreach collection="colonelIds" item="colonelId" open="(" separator="," close=")">
                            #{colonelId}
                        </foreach>
                    </if>
                    <!-- 订单编号 -->
                    <if test="null != orderNo">
                        AND tor.order_no LIKE concat ('%', #{orderNo} ,'%')
                    </if>
                    <!-- 创建时间 -->
                    <if test="null != startTime and null != endTime">
                        AND tor.create_time BETWEEN #{startTime} AND #{endTime}
                    </if>
                    <!-- 门店编号 -->
                    <if test="null != branchIds and branchIds.size > 0 ">
                        AND tor.branch_id IN
                        <foreach collection="branchIds" item="branchId" open="(" separator="," close=")">
                            #{branchId}
                        </foreach>
                    </if>
                    <!-- 订单类型 -->
                    <if test="null != orderProductType">
                        AND tor.order_type = #{orderProductType}
                    </if>
                    <!-- 订单状态 -->
                    <if test="null != orderStatus and orderStatus != 100">
                        <if test="orderStatus == 3"> <!-- 代发货包含（备货中 7）-->
                            AND tsod.delivery_state IN (#{orderStatus}, '7')
                        </if>
                        <if test="orderStatus == 4"> <!-- 待收货包含（待装车 40、已装车 41） -->
                            AND tsod.delivery_state IN (#{orderStatus} , '40', '41')
                        </if>
                        <if test="orderStatus == 5"> <!-- 订单收货 5 -->
                            AND tsod.delivery_state = #{orderStatus}
                        </if>
                        <if test="orderStatus == 6"> <!-- 订单已完成 6  -->
                            AND tsod.delivery_state = #{orderStatus}
                        </if>
                    </if>
                GROUP BY
                    tor.order_id
                    ,tsod.delivery_state
                ORDER BY
                    MAX(tor.create_time) DESC
                LIMIT #{pageNo}, #{pageSize}
            ) pageOrder ON pageOrder.order_id = tor.order_id
            INNER JOIN trd_supplier_order tso ON tor.order_id = tso.order_id
<!--            <if test=" null != orderPageReqVO.supplierOrderId">  &lt;!&ndash; 入驻商订单ID &ndash;&gt;-->
<!--                AND tso.supplier_order_id = #{orderPageReqVO.supplierOrderId}-->
<!--            </if>-->
<!--            <if test=" null != orderPageReqVO.supplierOrderNo">  &lt;!&ndash; 入驻商订单编号 &ndash;&gt;-->
<!--                AND tso.supplier_order_no = #{orderPageReqVO.supplierOrderNo}-->
<!--            </if>-->
            INNER JOIN trd_supplier_order_dtl tsod ON tsod.supplier_order_id = tso.supplier_order_id AND pageOrder.delivery_state = tsod.delivery_state
        ORDER BY tor.create_time DESC

    </select>

    <!-- 根据条件获取门店合单售后商品列表 -->
    <select id="branchSkuMergeAfterList" resultType="com.zksr.trade.api.after.dto.BranchSkuMergeAfterResDTO" parameterType="com.zksr.trade.api.after.vo.BranchSkuMergeAfterReqVO">
        SELECT
            resOrder.*,
            IFNULL(resOrder.minTotalNum / resOrder.order_unit_size, 0) AS totalNum,
            ROUND(resOrder.res_price * resOrder.minTotalNum, 2) AS totalAmt
        FROM (
            SELECT
                tor.order_id,
                tor.order_no,
                tor.create_time,
                tor.branch_id,
                tor.order_type,
                tsod.supplier_order_id,
                tsod.supplier_id,
                tsod.supplier_order_dtl_id,
                tsod.spu_id,
                tsod.sku_id,
                tsod.oldest_date,
                tsod.latest_date,
                CASE tsod.delivery_state
                    WHEN '40' THEN 4
                    WHEN '41' THEN 4
                    WHEN '7'  THEN 3
                    ELSE tsod.delivery_state
                END AS deliveryState,
                tsod.gift_flag,
                tsod.order_unit,
                tsod.order_unit_type,
                tsod.order_unit_size,
                CASE
                    WHEN tsod.delivery_state = 5 THEN (tsod.total_num - (select ifnull(sum(afterDtl.return_qty), 0) returnQty from trd_supplier_after_dtl afterDtl where afterDtl.supplier_order_dtl_id = tsod.supplier_order_dtl_id and afterDtl.is_cancel = 0))
                    ELSE (tsod.total_num - ifnull(tsod.cancel_qty, 0))
                END AS minTotalNum,
                ROUND(tsod.res_price, 2) AS minPrice,
                tsod.res_price,
                ROUND(IFNULL(tsod.res_unit_price, tsod.res_price * tsod.order_unit_size), 2) AS price
            FROM
                trd_order tor
                LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
            WHERE
                tor.branch_id = #{branchId}
                AND tor.pay_state IN (1, 3, 4)
                <if test="null != searchKey">
                    AND tsod.spu_name LIKE CONCAT('%', #{searchKey}, '%')
                </if>
                <choose>
                    <when test="null != deliveryState and 3 == deliveryState">
                        AND tsod.delivery_state IN (#{deliveryState}, 7) <!-- 待发货包括（备货中状态，故需要单独处理）-->
                    </when>
                    <when test="null != deliveryState and 5 == deliveryState">
                        AND tsod.delivery_state = #{deliveryState}
                    </when>
                    <otherwise>
                        AND tsod.delivery_state IN (3, 5, 7) <!-- 这里默认查询订单明细状态为 3（待发货）, 5（已收货）, 7（备货中） 的数据-->
                    </otherwise>
                </choose>
                <!-- SKUID -->
                <if test="null != skuId">
                    AND tsod.sku_id = #{skuId}
                </if>
                <!-- 入驻商ID -->
                <if test="null != supplierId">
                    AND tsod.supplier_id = #{supplierId}
                </if>
        ) resOrder
        WHERE
            resOrder.minTotalNum > 0
    </select>
    <select id="getDebtOrderPageList" resultMap="getOperatorOrderPageListNewMap">
        SELECT
        <!-- 主表信息 -->
        tor.order_id,
        tor.order_no,
        tor.branch_id,
        tor.pay_way,
        tor.order_type,
        tor.sys_code,
        tsord.supplier_order_id,
        tsord.supplier_order_no,
        tsord.supplier_id,
        tor.create_time,
        tor.dc_id,
        tor.pay_state,
        tor.colonel_id,
        tor.pcolonel_id,
        tso.source_order_no,
        <!-- 明细表信息 -->
        tsord.supplier_order_dtl_id,
        tsord.supplier_order_dtl_no,
        tsord.spu_id,
        tsord.sku_id,
        tsord.latest_date,
        tsord.oldest_date,
        tsord.gift_flag,
        <!-- 商品原金额 -->
        tsord.order_unit_price,
        <!-- 单位单价、 下单数量、下单金额-->
        ROUND(IFNULL(tsord.order_sales_unit_price, tsord.exact_price * tsord.order_unit_size), 2) AS price,
        IFNULL((tsord.total_num / tsord.order_unit_size), 0) AS demandNum,
        tsord.total_amt AS demandAmt,
        <!-- 发货数量、 发货金额-->
        IFNULL((tsord.send_qty / tsord.order_unit_size), 0) AS totalNum,
        IFNULL((tsord.send_qty * tsord.price), 0) AS totalAmt,
        <!-- 差异数量、 差异金额-->
        IFNULL((tsord.cancel_qty / tsord.order_unit_size), 0) AS shortageNum,
        IFNULL(((tsord.cancel_qty / tsord.order_unit_size) * tsord.order_sales_unit_price), 0) AS discrepancyAmt,
        tsord.order_unit,
        IFNULL(tsord.sub_order_amt, 0) AS saleTotalAmt,
        IFNULL((tsord.coupon_discount_amt + tsord.coupon_discount_amt2 + tsord.activity_discount_amt), 0) AS discountAmt,
        <!-- 售后信息  根据订单明细查询未取消 、 已审核的对应的售后订单详情金额汇总 -->
        0 AS afterReturnAmt,
        tsord.total_amt AS demandAmt
        FROM
        trd_order tor
        INNER JOIN trd_supplier_order_dtl tsord ON tor.order_id = tsord.order_id
        INNER JOIN (
        SELECT
        distinct tsord.supplier_order_id
        FROM
        trd_order tor
        INNER JOIN trd_supplier_order_dtl tsord ON tor.order_id = tsord.order_id
        INNER JOIN trd_supplier_order tso ON tso.order_id = tsord.order_id
        <where>
            AND tsord.pay_state = '3'
            AND tsord.delivery_state != 2
            <if test="null != param.sysCode">
                AND tor.sys_code = #{param.sysCode}
            </if>
            <if test="null != param.dcId">
                AND tor.dc_id = #{param.dcId}
            </if>
            <!-- 入驻商 -->
            <if test="null != param.supplierId">
                AND tsord.supplier_id = #{param.supplierId}
            </if>
<!--            &lt;!&ndash; 入驻商 &ndash;&gt;-->
<!--            <if test="null != param.supplierIdList and param.supplierIdList.size > 0 ">-->
<!--                AND tsord.supplier_id IN-->
<!--                <foreach collection="param.supplierIdList" item="supplierId" open="(" separator="," close=")">-->
<!--                    #{supplierId}-->
<!--                </foreach>-->
<!--            </if>-->
            <!-- 单据类型 -->
            <if test="null != param.itemType">
                AND tor.order_type = #{param.itemType}
            </if>
            <!-- 门店 -->
            <if test="null != param.branchId">
                AND tor.branch_id = #{param.branchId}
            </if>
            <!-- 区域 -->
            <if test="null != param.areaId and param.areaId != ''">
                AND tor.area_id = #{param.areaId}
            </if>
            <!-- 订单号 -->
            <if test="null != param.orderNo and param.orderNo != ''">
                AND tor.order_no LIKE concat('%', #{param.orderNo}, '%')
            </if>
            <!-- 入驻商订单号 -->
            <if test="null != param.subOrderNo and param.subOrderNo != ''">
                AND tsord.supplier_order_no LIKE concat('%', #{param.subOrderNo}, '%')
            </if>
            <!-- 查询时间  创建时间或支付时间 -->
            <if test="null != param.startTime and null != param.endTime ">
                 AND tor.create_time BETWEEN #{param.startTime} AND#{param.endTime}
            </if>
            <if test="null != param.subOrderId">
                AND tsord.supplier_order_id = #{param.subOrderId}
            </if>
            <!-- 外部单号 -->
            <if test="null != param.sourceOrderNo and '' != param.sourceOrderNo">
                AND tso.source_order_no LIKE concat('%', #{param.sourceOrderNo}, '%')
            </if>
        </where>
        ${param.params.dataScope}
        GROUP BY
        tsord.supplier_order_id
        ORDER BY
        MAX(tor.create_time) DESC
        LIMIT #{param.pageNo}, #{param.pageSize}
        ) table_supplierOrder ON tsord.supplier_order_id = table_supplierOrder.supplier_order_id
        LEFT JOIN trd_supplier_order tso ON table_supplierOrder.supplier_order_id = tso.supplier_order_id
        ORDER BY tor.create_time DESC
    </select>

    <select id="getDebtOrderPageListCount" resultType="com.zksr.report.api.homePages.dto.HomePagesCurrentSalesDataRespDTO">
        SELECT
        IFNULL(SUM(dataTable.debtTotalAmt), 0) AS debtTotalAmt,
        IFNULL(SUM(dataTable.debtOrderTotalQty), 0) AS debtOrderTotalQty
        FROM
        (
        SELECT
        tor.sys_code,
        SUM(IFNULL(tsord.total_amt, 0)) AS debtTotalAmt,
        COUNT(DISTINCT tsord.supplier_order_id) AS debtOrderTotalQty
        FROM
        trd_order tor
        LEFT JOIN trd_supplier_order_dtl tsord ON tor.order_id = tsord.order_id
        <!-- 外部单号 -->
        <if test="null != param.sourceOrderNo and '' != param.sourceOrderNo">
            INNER JOIN trd_supplier_order tso ON tso.order_id = tsord.order_id
        </if>
        WHERE
        tsord.pay_state = 3
        AND tsord.delivery_state != 2

        <if test="null != param.sysCode">
            AND tor.sys_code = #{param.sysCode}
        </if>
        <if test="null != param.dcId">
            AND tor.dc_id = #{param.dcId}
        </if>
        <if test="null != param.supplierId">
            AND tsord.supplier_id = #{param.supplierId}
        </if>
        <if test="null != param.startTime and null != param.endTime ">
            AND tor.create_time BETWEEN  #{param.startTime}  AND  #{param.endTime}
        </if>
        <!-- 单据类型 -->
        <if test="null != param.itemType">
            AND tor.order_type = #{param.itemType}
        </if>
        <!-- 订单号 -->
        <if test="null != param.orderNo and param.orderNo != ''">
            AND tor.order_no LIKE concat('%', #{param.orderNo}, '%')
        </if>
        <!-- 区域 -->
        <if test="null != param.areaId and param.areaId != ''">
            AND tor.area_id = #{param.areaId}
        </if>
        <!-- 入驻商订单号 -->
        <if test="null != param.subOrderNo and param.subOrderNo != ''">
            AND tsord.supplier_order_no LIKE concat('%', #{param.subOrderNo}, '%')
        </if>
        <!-- 外部单号 -->
        <if test="null != param.sourceOrderNo and '' != param.sourceOrderNo">
            AND tso.source_order_no LIKE concat('%', #{param.sourceOrderNo}, '%')
        </if>
        <!-- 门店 -->
        <if test="null != param.branchId">
            AND tor.branch_id = #{param.branchId}
        </if>
<!--        &lt;!&ndash; 入驻商 &ndash;&gt;-->
<!--        <if test="null != param.supplierIdList and param.supplierIdList.size > 0 ">-->
<!--            AND tsord.supplier_id IN-->
<!--            <foreach collection="param.supplierIdList" item="supplierId" open="(" separator="," close=")">-->
<!--                #{supplierId}-->
<!--            </foreach>-->
<!--        </if>-->
        GROUP BY
        tor.sys_code
        ) AS dataTable;
    </select>

    <select id="getColonelAppBranchOrder" resultType="com.zksr.trade.api.order.dto.ColonelAppBranchOrderDTO">
        SELECT
        tor.order_id,  -- 订单ID
        tor.create_time,  -- 订单创建时间
        tor.order_amt,  -- 订单金额
        SUM(COALESCE(tsod.total_amt, 0) - COALESCE(tsod.cancel_amt, 0)) realSaleAmt  -- 计算实际销售金额，考虑取消的金额
        FROM
        trd_order tor
        LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
        where
        tor.branch_id = #{branchId}  -- 指定门店ID
        and tsod.pay_state not in (0,2)  -- 排除已取消或者未支付的线上支付订单
        and tsod.delivery_state != 2 -- 排查掉订单状态为取消的
        AND tor.create_time &gt;= DATE_FORMAT(NOW() ,'%Y-%m-01')
        AND tor.create_time &lt;= DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 MONTH), '%Y-%m-01')  -- 查询当月的订单
        group by tor.order_id,tor.create_time,tor.order_amt
        order by tor.create_time DESC
    </select>

    <select id="getColonelAppPageOrder" resultType="com.zksr.trade.api.order.dto.ColonelAppPageOrderDTO">
        SELECT
            tor.order_id,  -- 订单ID
            tor.colonel_flag,  -- 下单用户是否本身是业务员(0-否（默认）  1-是)
            tor.branch_id,  -- 门店ID
            SUM(COALESCE(tsod.total_amt, 0) - COALESCE(tsod.cancel_amt, 0)) AS realSaleAmt  -- 计算实际销售金额，考虑取消的金额
        FROM
            trd_order tor
                LEFT JOIN trd_supplier_order_dtl tsod ON tor.order_id = tsod.order_id
        WHERE
            tor.colonel_id = #{colonelId}
          AND tsod.pay_state NOT IN (0, 2)  -- 排除已取消或者未支付的线上支付订单
          AND tsod.delivery_state != 2  -- 排查掉订单状态为取消的
          AND DATE(tor.create_time) = CURDATE()
        -- AND tor.create_time BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND
        GROUP BY
            tor.order_id,
            tor.colonel_flag,
            tor.branch_id
    </select>
    <select id="selectOrderCntAmt" resultType="com.zksr.trade.api.order.dto.OrderCutAmtDTO">
        SELECT
            IFNULL(mst.sub_pay_amt, 0) - IFNULL(rfd.refund_amt, 0) AS orderAmt
        FROM
            (
                SELECT
                    tor.branch_id,
                    SUM(tsod.total_amt) sub_pay_amt
                FROM
                    trd_order tor
                    INNER JOIN trd_supplier_order tso ON tor.order_id = tso.order_id
                    INNER JOIN trd_supplier_order_dtl tsod ON tsod.supplier_order_id = tso.supplier_order_id
                WHERE
                    tor.branch_id = #{cacheKey.branchId}
                    AND tor.pay_state in (1,2,3,4)
                    AND tor.create_time BETWEEN #{startDate} AND #{endDate}
                    AND tso.supplier_id = #{cacheKey.supplierId}
                    <if test='cacheKey.productType == "global"'>
                        <!-- 全国 -->
                        AND tsod.item_type = 0
                    </if>
                    <if test='cacheKey.productType == "local"'>
                        <!-- 本地 -->
                        AND tsod.item_type = 1
                    </if>
                GROUP BY
                    tor.branch_id
            ) mst
            LEFT JOIN (
                SELECT
                    tor.branch_id,
                    SUM(tsal.refund_amt) refund_amt
                FROM
                    trd_order tor
                    INNER JOIN trd_after aft ON aft.order_id = tor.order_id
                    INNER JOIN trd_supplier_after tsa ON tsa.after_id = aft.after_id
                    INNER JOIN trd_supplier_after_dtl tsal ON tsal.supplier_after_id = tsa.supplier_after_id
                WHERE
                    tor.branch_id = #{cacheKey.branchId}
                    AND tsal.approve_state in (0, 1)
                    AND tor.create_time BETWEEN #{startDate} AND #{endDate}
                    AND tsa.supplier_id = #{cacheKey.supplierId}
                    <if test='cacheKey.productType == "global"'>
                        <!-- 全国 -->
                        AND tsal.item_type = 0
                    </if>
                    <if test='cacheKey.productType == "local"'>
                        <!-- 本地 -->
                        AND tsal.item_type = 1
                    </if>
                GROUP BY
                    tor.branch_id
            ) rfd ON rfd.branch_id = mst.branch_id
    </select>

    <select id="getBranchLatestOrderByBranchIdList" resultType="com.zksr.trade.api.order.vo.TrdOrder">
        SELECT
            tor.create_time,
            tor.branch_id,
            tor.colonel_id,
            tor.order_no,
            tor.order_amt
        FROM
            trd_order tor
                INNER JOIN (
                SELECT
                    tor2.branch_id,
                    max( tor2.order_id ) maxOrderId
                FROM
                    trd_order tor2
                        LEFT JOIN trd_supplier_order_dtl tsod ON tsod.order_id = tor2.order_id
                WHERE
                  tor2.sys_code = #{sysCode}
                  AND ( tor2.del_flag = 0 OR tor2.del_flag IS NULL )
                  AND ( tsod.delivery_state != 0 AND tsod.delivery_state != 2 )
                  AND tor2.create_time >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
                GROUP BY
                    tor2.branch_id
            ) maxTor ON tor.order_id = maxTor.maxOrderId
        WHERE
            tor.sys_code = #{sysCode}

    </select>

    <select id="getBranchLatestOrderByBranchId" resultType="com.zksr.trade.api.order.vo.TrdOrder">
        SELECT
            tor.create_time,
            tor.branch_id,
            tor.colonel_id,
            tor.order_no,
            tor.order_amt
        FROM
            trd_order tor
                INNER JOIN (
                SELECT
                    tor2.branch_id,
                    max( tor2.order_id ) maxOrderId
                FROM
                    trd_order tor2
                        LEFT JOIN trd_supplier_order_dtl tsod ON tsod.order_id = tor2.order_id
                WHERE
                    tor2.branch_id = #{branchId}
                  AND ( tor2.del_flag = 0 OR tor2.del_flag IS NULL )
                  AND ( tsod.delivery_state != 0 AND tsod.delivery_state != 2 )
                  AND tor2.create_time >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
                GROUP BY
                    tor2.branch_id
            ) maxTor ON tor.order_id = maxTor.maxOrderId
        WHERE
            tor.branch_id = #{branchId}

    </select>
    <select id="selectSupplierOrder" resultType="com.zksr.system.api.supplier.vo.SupplierOrderVO">
        SELECT
            tor.order_id,
            tor.order_no,
            tor.branch_id,
            tor.pay_way,
            tor.order_type,
            tor.sys_code,
            tso.supplier_order_id,
            tso.supplier_order_no,
            tso.supplier_id,
            tor.create_time,
            tor.create_time createDate,
            tor.memo,
            tor.colonel_id,
            tor.dc_id,
            tso.memo AS supplierMemo,
            tor.pcolonel_id,
            tor.pay_state,
            tso.push_status,
            tso.source_order_no,
            tor.pay_time,
            tso.driver_id,
            tso.driver_rating_flag,
            tso.driver_rating_id,
            tso.print_state,
            tso.print_qty,
            tso.sub_discount_amt,
            tso.sub_pay_amt,
            tso.sub_order_amt,
            tso.sub_order_num
        FROM
            trd_order tor
                INNER JOIN trd_supplier_order tso ON tso.order_id = tor.order_id
        <where>
            <if test="null != param.itemType">
                AND tor.order_type = #{param.itemType}
            </if>
            <!-- 入驻商 -->
            <if test="null != param.supplierIdList and param.supplierIdList.size > 0 ">
                AND tso.supplier_id IN
                <foreach collection="param.supplierIdList" item="supplierId" open="(" separator="," close=")">
                    #{supplierId}
                </foreach>
            </if>
            <!-- 门店 -->
            <if test="null != param.branchIds and param.branchIds.size > 0 ">
                AND tor.branch_id IN
                <foreach collection="param.branchIds" item="branchId" open="(" separator="," close=")">
                    #{branchId}
                </foreach>
            </if>
            <!-- 业务员 -->
            <if test="null != param.colonelIds and param.colonelIds.size > 0 ">
                AND tor.colonel_id IN
                <foreach collection="param.colonelIds" item="colonelId" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
            <!-- 订单号 -->
            <if test="null != param.orderNo and param.orderNo != ''">
                AND tor.order_no LIKE concat('%', #{param.orderNo}, '%')
            </if>
            <!-- 入驻商订单号 -->
            <if test="null != param.subOrderNo and param.subOrderNo != ''">
                AND tso.supplier_order_no LIKE concat('%', #{param.subOrderNo}, '%')
            </if>
            <!-- 查询时间  创建时间或支付时间 -->
            <if test="null != param.startTime and null != param.endTime ">
                <if test="null != param.dateType and param.dateType == 1">
                    AND tor.create_time
                </if>
                <if test="null != param.dateType and param.dateType == 2">
                    AND tor.pay_time
                </if>
                BETWEEN #{param.startTime} AND #{param.endTime}
            </if>
            <if test="null != param.subOrderId">
                AND tso.supplier_order_id = #{param.subOrderId}
            </if>
            <!-- 外部单号 -->
            <if test="null != param.sourceOrderNo and '' != param.sourceOrderNo">
                AND tso.source_order_no LIKE concat('%', #{param.sourceOrderNo}, '%')
            </if>
            <if test="null != param.printState">
                AND print_state = #{param.printState}
            </if>
            <if test="null != param.pushStatus">
                AND tso.push_status = #{param.pushStatus}
            </if>
            <if test="null != param.areaId">
                AND tor.area_id = #{param.areaId}
            </if>
            <if test="null != param.orderType and param.orderType != 100">
                AND tor.pay_state = #{param.orderType}
            </if>
        </where>
    </select>
    <select id="selectSupplierOrderExport" resultType="com.zksr.trade.api.order.vo.SupplierOrderExportVO">
        SELECT
        tor.order_id,
        tor.order_no,
        tor.branch_id,
        tor.pay_way,
        tor.order_type,
        tor.sys_code,
        tso.supplier_order_id,
        tso.supplier_order_no,
        tso.supplier_id,
        tor.create_time,
        DATE_FORMAT(tor.create_time, '%Y-%m-%d %H:%i:%s') AS createDate,
        tor.memo,
        tor.colonel_id,
        tor.dc_id,
        tso.memo AS supplierMemo,
        tor.pcolonel_id,
        tor.pay_state,
        tso.push_status,
        tso.source_order_no,
        tor.pay_time,
        tso.driver_id,
        tso.driver_rating_flag,
        tso.driver_rating_id,
        tso.print_state,
        tso.print_qty,
        tso.sub_discount_amt,
        tso.sub_pay_amt,
        tso.sub_order_amt,
        tso.sub_order_num,
        YEAR(tor.create_time) createTimeYear,
        MONTH(tor.create_time) createTimeMonth,
        DAY(tor.create_time) createTimeDay,
        tor.area_id,
        tso.delivery_state
        FROM
        trd_order tor
        INNER JOIN trd_supplier_order tso ON tso.order_id = tor.order_id
        <where>
            <if test="null != param.itemType">
                AND tor.order_type = #{param.itemType}
            </if>
            <!-- 入驻商 -->
            <if test="null != param.supplierIdList and param.supplierIdList.size > 0 ">
                AND tso.supplier_id IN
                <foreach collection="param.supplierIdList" item="supplierId" open="(" separator="," close=")">
                    #{supplierId}
                </foreach>
            </if>
            <!-- 门店 -->
            <if test="null != param.branchIds and param.branchIds.size > 0 ">
                AND tor.branch_id IN
                <foreach collection="param.branchIds" item="branchId" open="(" separator="," close=")">
                    #{branchId}
                </foreach>
            </if>
            <!-- 订单状态 -->
            <if test="null != param.deliveryStates and param.deliveryStates.size > 0 ">
                AND tso.delivery_state IN
                <foreach collection="param.deliveryStates" item="deliveryState" open="(" separator="," close=")">
                    #{deliveryState}
                </foreach>
            </if>
            <!-- 业务员 -->
            <if test="null != param.colonelIds and param.colonelIds.size > 0 ">
                AND tor.colonel_id IN
                <foreach collection="param.colonelIds" item="colonelId" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
            <!-- 订单号 -->
            <if test="null != param.orderNo and param.orderNo != ''">
                AND tor.order_no LIKE concat('%', #{param.orderNo}, '%')
            </if>
            <!-- 入驻商订单号 -->
            <if test="null != param.subOrderNo and param.subOrderNo != ''">
                AND tso.supplier_order_no LIKE concat('%', #{param.subOrderNo}, '%')
            </if>
            <!-- 查询时间  创建时间或支付时间 -->
            <if test="null != param.startTime and null != param.endTime ">
                AND tor.create_time
                BETWEEN #{param.startTime} AND #{param.endTime}
            </if>
            <if test="null != param.payStartTime and null != param.payEndTime ">

                AND tor.pay_time
                BETWEEN #{param.payStartTime} AND #{param.payEndTime}
            </if>
            <if test="null != param.subOrderId">
                AND tso.supplier_order_id = #{param.subOrderId}
            </if>
            <!-- 外部单号 -->
            <if test="null != param.sourceOrderNo and '' != param.sourceOrderNo">
                AND tso.source_order_no LIKE concat('%', #{param.sourceOrderNo}, '%')
            </if>
            <if test="null != param.printState">
                AND print_state = #{param.printState}
            </if>
            <if test="null != param.pushStatus">
                AND tso.push_status = #{param.pushStatus}
            </if>
            <if test="null != param.areaId">
                AND tor.area_id = #{param.areaId}
            </if>
            <if test="null != param.payState">
                AND tor.pay_state = #{param.payState}
            </if>
            <if test="null != param.payWay">
                AND tor.pay_way = #{param.payWay}
            </if>
        </where>
    </select>

    <select id="countSupplierOrder" resultType="com.zksr.trade.api.order.vo.SupplierOrderCountRespVO">
        select
        sys_code,
        supplier_id,
        supplier_name,
        create_date,
        order_type,
        sum(qty) qty
        from
        (
        select
        sys_code,
        supplier_id,
        supplier_name,
        DATE_FORMAT(o.create_time, '%Y-%m-%d') as create_date,
        count(1) qty,
        3 order_type
        from
        trd_supplier_order o
        <where>
            1 = 1
            <if test="null != param.sysCode">
                and sys_code = #{param.sysCode}
            </if>
            <if test="null != param.startDate and null != param.endDate ">
                and create_time BETWEEN #{param.startDate} AND #{param.endDate}
            </if>
            <if test="null != param.supplierName">
                and supplier_name = #{param.supplierName}
            </if>
        </where>
        group by
        o.sys_code,
        o.supplier_id,
        o.supplier_name,
        DATE_FORMAT(o.create_time, '%Y-%m-%d'),
        order_type
        union all
        select
        a.sys_code,
        o.supplier_id,
        o.supplier_name,
        DATE_FORMAT(a.create_time, '%Y-%m-%d') as create_date,
        count(1) qty,
        4 order_type
        from
        trd_supplier_after a
        left join trd_supplier_order o on
        a.supplier_order_no = o.supplier_order_no
        and a.sys_code = o.sys_code
        <where>
            1 = 1
            <if test="null != param.sysCode">
                and a.sys_code = #{param.sysCode}
            </if>
            <if test="null != param.startDate and null != param.endDate ">
                and a.create_time BETWEEN #{param.startDate} AND #{param.endDate}
            </if>
            <if test="null != param.supplierName">
                and o.supplier_name = #{param.supplierName}
            </if>
        </where>
        group by
        a.sys_code,
        o.supplier_id,
        o.supplier_name,
        DATE_FORMAT(a.create_time, '%Y-%m-%d'),
        order_type

        ) t
        <where>
            1 = 1
            <if test="null != param.sysCode">
                and sys_code = #{param.sysCode}
            </if>
            <if test="null != param.startDate and null != param.endDate ">
                and create_date BETWEEN #{param.startDate} AND #{param.endDate}
            </if>
            <if test="null != param.supplierName">
                and supplier_name = #{param.supplierName}
            </if>
        </where>
        group by
        sys_code,
        supplier_id,
        supplier_name,
        create_date,
        order_type
        order by
        create_date desc,
        supplier_name asc,
        order_type asc
    </select>

    <select id="countSupplierOrderAll" resultType="com.zksr.trade.api.order.vo.SupplierOrderCountAllRespVO">
        select
        sys_code,
        create_date,
        order_type,
        sum(qty) qty,
        sum(sub_order_amt) sub_order_amt
        from
        (
        select
        sys_code,
        DATE_FORMAT(o.create_time, '%Y-%m-%d') as create_date,
        count(1) qty,
        sum(sub_order_amt) sub_order_amt,
        8 order_type
        from
        trd_supplier_order o
        <where>
            1 = 1
            <if test="null != param.sysCode">
                and sys_code = #{param.sysCode}
            </if>
            <if test="null != param.startDate and null != param.endDate ">
                and create_time BETWEEN #{param.startDate} AND #{param.endDate}
            </if>

        </where>
        group by
        o.sys_code,
        DATE_FORMAT(o.create_time, '%Y-%m-%d'),
        order_type
        union all
        select
        a.sys_code,
        DATE_FORMAT(a.create_time, '%Y-%m-%d') as create_date,
        count(1) qty,
        sum(sub_refund_amt) sub_order_amt,
        9 order_type
        from
        trd_supplier_after a

        <where>
            1 = 1
            <if test="null != param.sysCode">
                and sys_code = #{param.sysCode}
            </if>
            <if test="null != param.startDate and null != param.endDate ">
                and create_time BETWEEN #{param.startDate} AND #{param.endDate}
            </if>

        </where>
        group by
        a.sys_code,
        DATE_FORMAT(a.create_time, '%Y-%m-%d'),
        order_type

        ) t
        <where>
            1 = 1
            <if test="null != param.sysCode">
                and sys_code = #{param.sysCode}
            </if>
            <if test="null != param.startDate and null != param.endDate ">
                and create_date BETWEEN #{param.startDate} AND #{param.endDate}
            </if>

        </where>
        group by
        sys_code,
        create_date,
        order_type
        order by
        create_date desc,
        order_type asc
    </select>

    <select id="getHomePagesCurrentDaySales" resultType="com.zksr.trade.api.order.vo.HomePagesCurrentSalesDataRespVO">
        select
        ifnull(sum(ifnull(l.sub_order_amt, 0) ), 0) as today_order_total_amt
        from
        trd_supplier_order l inner join trd_order h
        on h.order_id = l.order_id

        <where>

            l.pay_state not in (2)
            and l.create_time >= CURDATE()

            <if test="null != param.sysCode">
                and l.sys_code = #{param.sysCode}
            </if>

            <if test="null != param.dcId">
                and h.dc_id = #{param.dcId}
            </if>

            <if test="null != param.supplierId">
                and l.supplier_id = #{param.supplierId}
            </if>

        </where>
    </select>

    <!-- 根据branchId统计当前月和上月订单笔数和订单总金额 -->
    <select id="countOrderStatsByBranchId" resultType="map">
        SELECT
        DATE_FORMAT(create_time, '%Y-%m') AS month,
        COUNT(DISTINCT order_id) AS orderCount,
        SUM(pay_amt) AS totalPayAmt
        FROM
        trd_order
        WHERE
        branch_id = #{branchId}
        AND ( del_flag = 0 OR del_flag IS NULL )
        AND (
        DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') -- 当前月份
        OR DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m') -- 上个月份
        )
        GROUP BY
        DATE_FORMAT(create_time, '%Y-%m')
    </select>


</mapper>
