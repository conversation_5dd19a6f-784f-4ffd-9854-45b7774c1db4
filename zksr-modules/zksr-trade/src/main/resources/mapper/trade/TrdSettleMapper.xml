<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdSettleMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- 查询账户订单结算流水数据(平台商、运营商、业务员) PC -->
    <select id ="getOrderSettleInfoPage" resultType="com.zksr.trade.api.orderSettle.dto.OrderSettleResDTO">
        SELECT
            settle.settle_id,
            settle.create_time
            ,ifnull(dtlSettle.profit, aDtlSettle.profit) AS profit
            ,settle.order_id
            ,settle.order_no
            ,settle.after_id
            ,settle.after_no
            ,ifnull(orderDtl.sku_id, afterDtl.sku_id) AS skuId
            ,ifnull(orderDtl.spu_id, afterDtl.spu_id) AS spuId
            ,ifnull(dtlSettle.item_amt, afterDtl.return_amt) AS itemAmt
            ,settle.settle_amt
            ,settle.merchant_type
            ,settle.merchant_id
            ,settle.settle_rate AS merchantRate  <!-- 实际分润比例 -->
--             ,CASE settle.merchant_type
--                 WHEN 'partner' THEN ifnull(dtlSettle.partner_rate, aDtlSettle.partner_rate)
--                 WHEN 'dc' THEN ifnull(dtlSettle.dc_rate, aDtlSettle.dc_rate)
--                 ELSE 0
--             END merchantRate
            ,CASE WHEN settle.state = 2 OR settle.state = 3 THEN 2 ELSE settle.state END state
            ,ifnull(orderDtl.supplier_id, afterDtl.supplier_id) AS supplierId
        FROM trd_settle  settle
        LEFT JOIN trd_supplier_order_settle dtlSettle ON settle.supplier_order_dtl_id = dtlSettle.supplier_order_dtl_id
        LEFT JOIN trd_supplier_order_dtl orderDtl ON orderDtl.supplier_order_dtl_id = settle.supplier_order_dtl_id
        LEFT JOIN trd_supplier_after_settle aDtlSettle ON  settle.supplier_order_dtl_id = aDtlSettle.supplier_after_dtl_id
        LEFT JOIN trd_supplier_after_dtl afterDtl ON afterDtl.supplier_after_dtl_id = settle.supplier_order_dtl_id
        WHERE
            settle.merchant_id = #{pageVO.merchantId}
            AND settle.merchant_type = #{pageVO.merchantType}
            <choose>
                <when test="null != pageVO.state and pageVO.state == 2">
                    AND settle.state in (2, 3)
                </when>
                <otherwise>
                    AND settle.state = #{pageVO.state}
                </otherwise>
            </choose>
            <if test="null != pageVO.startTime and pageVO.startTime != '' and null != pageVO.endTime and pageVO.endTime !=''">
                AND settle.create_time between #{pageVO.startTime} and #{pageVO.endTime}
            </if>
            <if test="null != pageVO.orderId">
                AND settle.order_id = #{pageVO.orderId}
            </if>
            <if test='pageVO.orderNo != null and pageVO.orderNo != ""'>
                AND settle.order_no = #{pageVO.orderNo}
            </if>
            <if test='pageVO.itemName != null and pageVO.itemName != ""'>
                AND orderDtl.spu_name LIKE CONCAT('%', #{pageVO.itemName}, '%')
            </if>
            <if test='pageVO.settleIdList != null and pageVO.settleIdList.size > 0'>
                AND settle.settle_id IN
                <foreach collection="pageVO.settleIdLis" item="settleId" open="(" separator="," close=")">
                    #{settleId}
                </foreach>
            </if>
        ORDER BY settle.create_time DESC
    </select>


    <!-- 订单分佣对账单 -->
<select id="getStatementOfAccountPage" resultType="com.zksr.trade.controller.statementOfAccount.vo.OrderCommissionStatementRespVO">
    <!-- 原始查询 -->
    select
        tor.create_time createTime,
        tso.supplier_id supplierId,
        tso.sub_pay_amt subPayAmt,
        tso.sub_pay_amt - IFNULL(MAX(sa.sub_refund_amt), 0) AS subSettleAmt,
        CASE
        WHEN tor.pay_way = 0 THEN '在线支付'
        WHEN tor.pay_way = 2 THEN '货到付款'
        ELSE '储值支付'
        END orderType,
        tso.sub_discount_amt subDiscountAmt,
        tso.order_no orderNo,
        MAX(sas.supplier_after_no) afterNo,
        tor.platform platform,
        tso.supplier_order_no supplierOrderNo,
        SUM(CASE WHEN tse.state != 1 THEN tse.settle_amt ELSE 0 END) settleAmt,
        SUM(CASE WHEN tse.state = 1 THEN tse.settle_amt ELSE 0 END) settledAmt,
        IF(SUM(CASE WHEN tse.state = 1 THEN 1 ELSE 0 END) > 1, '已结算', '未结算') settleStatus,
        tse.sys_code sysCode,
        SUM(CASE WHEN tse.merchant_type = 'partner' AND tse.state != 1 THEN tse.settle_amt ELSE 0 END) partnerSettleAmt,
        SUM(CASE WHEN tse.merchant_type = 'partner' AND tse.state = 1 THEN tse.settle_amt ELSE 0 END) partnerSettledAmt,
        IF(SUM(CASE WHEN tse.merchant_type = 'partner' AND tse.state = 1 THEN 1 ELSE 0 END) >= 1, '已结算', '未结算') partnerSettleStatus,
        MAX(CASE WHEN tse.merchant_type = 'partner' AND tse.state = 1 THEN tse.settle_time END) partnerSettleTime,
        MAX(sos.partner_rate) partnerSettleRatio,
        SUM(CASE WHEN tse.merchant_type = 'software' AND tse.state != 1 THEN tse.settle_amt ELSE 0 END) softwareSettleAmt,
        SUM(CASE WHEN tse.merchant_type = 'software' AND tse.state = 1 THEN tse.settle_amt ELSE 0 END) softwareSettledAmt,
        IF(SUM(CASE WHEN tse.merchant_type = 'software' AND tse.state = 1 THEN 1 ELSE 0 END) >= 1, '已结算', '未结算') softwareSettleStatus,
        MAX(CASE WHEN tse.merchant_type = 'software' AND tse.state = 1 THEN tse.settle_time END) softwareSettleTime,
        MAX(sos.software_rate) softwareSettleRatio,
        tor.dc_id dcId,
        SUM(CASE WHEN tse.merchant_type = 'dc' AND tse.state != 1 THEN tse.settle_amt ELSE 0 END) dcSettleAmt,
        SUM(CASE WHEN tse.merchant_type = 'dc' AND tse.state = 1 THEN tse.settle_amt ELSE 0 END) dcSettledAmt,
        IF(SUM(CASE WHEN tse.merchant_type = 'dc' AND tse.state = 1 THEN 1 ELSE 0 END) >= 1, '已结算', '未结算') dcSettleStatus,
        MAX(CASE WHEN tse.merchant_type = 'dc' AND tse.state = 1 THEN tse.settle_time END) dcSettleTime,
        MAX(sos.dc_rate) dcSettleRatio,
        tor.colonel_id colonelId,
        SUM(CASE WHEN tse.merchant_type = 'colonel' AND tse.state != 1 THEN tse.settle_amt ELSE 0 END) colonelSettleAmt,
        SUM(CASE WHEN tse.merchant_type = 'colonel' AND tse.state = 1 THEN tse.settle_amt ELSE 0 END) colonelSettledAmt,
        IF(SUM(CASE WHEN tse.merchant_type = 'colonel' AND tse.state = 1 THEN 1 ELSE 0 END) >= 1, '已结算', '未结算') colonelSettleStatus,
        MAX(CASE WHEN tse.merchant_type = 'colonel' AND tse.state = 1 THEN tse.settle_time END) colonelSettleTime,
        MAX(sos.colonel1_rate) colonelSettleRatio,
        MAX(tse.settle_time) platformSettleTime,
        tor.branch_id branchId,
        tsd.delivery_state deliveryState
        FROM
        trd_settle tse
        LEFT JOIN trd_supplier_order tso ON tso.order_id = tse.order_id  AND tse.supplier_id = tso.supplier_id
        LEFT JOIN trd_supplier_order_settle sos ON sos.supplier_order_dtl_id = tse.supplier_order_dtl_id
        LEFT JOIN trd_supplier_after_settle sas ON sas.supplier_order_dtl_id = tse.supplier_order_dtl_id
        LEFT JOIN trd_supplier_after sa ON sa.after_id = sas.after_id AND tse.supplier_id = sa.supplier_id
        LEFT JOIN trd_supplier_order_dtl tsd ON tsd.supplier_order_dtl_id = tse.supplier_order_dtl_id
        LEFT JOIN trd_order tor ON tor.order_id = tso.order_id
        WHERE
        (tor.pay_state IN(1,2,3) or tor.pay_state IN(1,2,3))
        AND tse.after_no IS NULL
        <if test="pageVO.sysCode != null and pageVO.sysCode != ''">
            AND tse.sys_code = #{pageVO.sysCode}
        </if>
        <if test="pageVO.sysCodes != null and pageVO.sysCodes != ''">
            AND tse.sys_code IN
            <foreach collection="pageVO.sysCodes" item="sysCode" open="(" separator="," close=")">
                #{sysCode}
            </foreach>
        </if>
        <if test="null != pageVO.platform">
            AND tor.platform = #{pageVO.platform}
        </if>
        <if test="pageVO.supplierId != null and pageVO.supplierId != ''">
            AND tso.supplier_id = #{pageVO.supplierId}
        </if>
        <if test="pageVO.supplierIds != null and pageVO.supplierIds != ''">
            AND tso.supplier_id in
            <foreach collection="pageVO.supplierIds" item="supplierId" open="(" separator="," close=")">
                #{supplierId}
            </foreach>
        </if>
        <if test="pageVO.orderNo != null and pageVO.orderNo != ''">
            AND tso.order_no LIKE CONCAT ('%',#{pageVO.orderNo},'%')
        </if>
        <if test="pageVO.afterNo != null and pageVO.afterNo != ''">
            AND sas.supplier_after_no LIKE CONCAT ('%',#{pageVO.afterNo},'%')
        </if>
        <if test="pageVO.supplierOrderNo != null and pageVO.supplierOrderNo != ''">
            AND tso.supplier_order_no LIKE CONCAT ('%',#{pageVO.supplierOrderNo},'%')
        </if>
        <if test="pageVO.orderType != null and pageVO.orderType != ''">
            AND tor.pay_way = #{pageVO.orderType}
        </if>
        <if test="pageVO.orderSettleStartTime != null and pageVO.orderSettleStartTime != '' and pageVO.orderSettleEndTime != null and pageVO.orderSettleEndTime != ''">
            AND tse.settle_time BETWEEN #{pageVO.orderSettleStartTime} AND #{pageVO.orderSettleEndTime}
        </if>
        <if test="pageVO.orderCreateStartTime != null and pageVO.orderCreateStartTime != '' and pageVO.orderCreateEndTime != null and pageVO.orderCreateEndTime != ''">
            AND tor.create_time BETWEEN #{pageVO.orderCreateStartTime} AND #{pageVO.orderCreateEndTime}
        </if>
        <if test="pageVO.deliveryState != null and pageVO.deliveryState != ''">
            AND tsd.delivery_state = #{pageVO.deliveryState}
        </if>
        <if test="pageVO.branchId != null and pageVO.branchId != ''">
            AND tor.branch_id = #{pageVO.branchId}
        </if>
        GROUP BY
        tso.supplier_order_id,tse.sys_code,tsd.delivery_state
        <if test="pageVO.settleStatus != null and pageVO.settleStatus != ''">
            HAVING IF(SUM(CASE WHEN tse.state = 1 THEN 1 ELSE 0 END) > 1, '已结算', '未结算') =  #{pageVO.settleStatus}
        </if>

    UNION ALL
    <!-- 新增查询，处理 afterNo 不为空的数据 -->
    select
        tor.create_time createTime,
        tso.supplier_id supplierId,
        -MAX(sa.sub_refund_amt) subPayAmt,
        tso.sub_pay_amt - IFNULL(MAX(sa.sub_refund_amt), 0) AS subSettleAmt,
        CASE
        WHEN tor.pay_way = 0 THEN '在线支付'
        WHEN tor.pay_way = 2 THEN '货到付款'
        ELSE '储值支付'
        END orderType,
        tso.sub_discount_amt subDiscountAmt,
        tso.order_no orderNo,
        MAX(sas.supplier_after_no) afterNo,
        tor.platform platform,
        tso.supplier_order_no supplierOrderNo,
        SUM(CASE WHEN tse.state != 1 THEN -tse.settle_amt ELSE 0 END) settleAmt,  <!-- 注意这里取反 -->
        SUM(CASE WHEN tse.state = 1 THEN -tse.settle_amt ELSE 0 END) settledAmt,
        IF(SUM(CASE WHEN tse.state = 1 THEN 1 ELSE 0 END) > 1, '已结算', '未结算') settleStatus,
        tse.sys_code sysCode,
        SUM(CASE WHEN tse.merchant_type = 'partner' AND tse.state != 1 THEN -tse.settle_amt ELSE 0 END) partnerSettleAmt,  <!-- 注意这里取反 -->
        SUM(CASE WHEN tse.merchant_type = 'partner' AND tse.state = 1 THEN -tse.settle_amt ELSE 0 END) partnerSettledAmt,  <!-- 注意这里取反 -->
        IF(SUM(CASE WHEN tse.merchant_type = 'partner' AND tse.state = 1 THEN 1 ELSE 0 END) >= 1, '已结算', '未结算') partnerSettleStatus,
        MAX(CASE WHEN tse.merchant_type = 'partner' AND tse.state = 1 THEN tse.settle_time END) partnerSettleTime,
        MAX(sos.partner_rate) partnerSettleRatio,
        SUM(CASE WHEN tse.merchant_type = 'software' AND tse.state != 1 THEN -tse.settle_amt ELSE 0 END) softwareSettleAmt,  <!-- 注意这里取反 -->
        SUM(CASE WHEN tse.merchant_type = 'software' AND tse.state = 1 THEN -tse.settle_amt ELSE 0 END) softwareSettledAmt,  <!-- 注意这里取反 -->
        IF(SUM(CASE WHEN tse.merchant_type = 'software' AND tse.state = 1 THEN 1 ELSE 0 END) >= 1, '已结算', '未结算') softwareSettleStatus,
        MAX(CASE WHEN tse.merchant_type = 'software' AND tse.state = 1 THEN tse.settle_time END) softwareSettleTime,
        MAX(sos.software_rate) softwareSettleRatio,
        tor.dc_id dcId,
        SUM(CASE WHEN tse.merchant_type = 'dc' AND tse.state != 1 THEN -tse.settle_amt ELSE 0 END) dcSettleAmt,  <!-- 注意这里取反 -->
        SUM(CASE WHEN tse.merchant_type = 'dc' AND tse.state = 1 THEN -tse.settle_amt ELSE 0 END) dcSettledAmt,  <!-- 注意这里取反 -->
        IF(SUM(CASE WHEN tse.merchant_type = 'dc' AND tse.state = 1 THEN 1 ELSE 0 END) >= 1, '已结算', '未结算') dcSettleStatus,
        MAX(CASE WHEN tse.merchant_type = 'dc' AND tse.state = 1 THEN tse.settle_time END) dcSettleTime,
        MAX(sos.dc_rate) dcSettleRatio,
        tor.colonel_id colonelId,
        SUM(CASE WHEN tse.merchant_type = 'colonel' AND tse.state != 1 THEN -tse.settle_amt ELSE 0 END) colonelSettleAmt,  <!-- 注意这里取反 -->
        SUM(CASE WHEN tse.merchant_type = 'colonel' AND tse.state = 1 THEN -tse.settle_amt ELSE 0 END) colonelSettledAmt,  <!-- 注意这里取反 -->
        IF(SUM(CASE WHEN tse.merchant_type = 'colonel' AND tse.state = 1 THEN 1 ELSE 0 END) >= 1, '已结算', '未结算') colonelSettleStatus,
        MAX(CASE WHEN tse.merchant_type = 'colonel' AND tse.state = 1 THEN tse.settle_time END) colonelSettleTime,
        MAX(sos.colonel1_rate) colonelSettleRatio,
        MAX(tse.settle_time) platformSettleTime,
        tor.branch_id branchId,
        tsd.delivery_state deliveryState
        FROM
        trd_settle tse
        LEFT JOIN trd_supplier_order tso ON tso.order_id = tse.order_id  AND tse.supplier_id = tso.supplier_id
        LEFT JOIN trd_supplier_order_settle sos ON sos.supplier_order_dtl_id = tse.supplier_order_dtl_id
        LEFT JOIN trd_supplier_after_settle sas ON sas.supplier_order_dtl_id = tse.supplier_order_dtl_id
        LEFT JOIN trd_supplier_after sa ON sa.after_id = sas.after_id AND tse.supplier_id = sa.supplier_id
        LEFT JOIN trd_supplier_order_dtl tsd ON tsd.supplier_order_dtl_id = tse.supplier_order_dtl_id
        LEFT JOIN trd_order tor ON tor.order_id = tso.order_id
        WHERE
        (tor.pay_state IN(1,2,3) or tor.pay_state IN(1,2,3))
        AND sas.supplier_after_no IS NOT NULL  <!-- 添加条件：afterNo 不为空 -->
        <if test="pageVO.sysCode != null and pageVO.sysCode != ''">
            AND tse.sys_code = #{pageVO.sysCode}
        </if>
        <if test="pageVO.sysCodes != null and pageVO.sysCodes != ''">
            AND tse.sys_code IN
            <foreach collection="pageVO.sysCodes" item="sysCode" open="(" separator="," close=")">
                #{sysCode}
            </foreach>
        </if>
        <if test="null != pageVO.platform">
            AND tor.platform = #{pageVO.platform}
        </if>
        <if test="pageVO.supplierId != null and pageVO.supplierId != ''">
            AND tso.supplier_id = #{pageVO.supplierId}
        </if>
        <if test="pageVO.supplierIds != null and pageVO.supplierIds != ''">
            AND tso.supplier_id in
            <foreach collection="pageVO.supplierIds" item="supplierId" open="(" separator="," close=")">
                #{supplierId}
            </foreach>
        </if>
        <if test="pageVO.orderNo != null and pageVO.orderNo != ''">
            AND tso.order_no LIKE CONCAT ('%',#{pageVO.orderNo},'%')
        </if>
        <if test="pageVO.afterNo != null and pageVO.afterNo != ''">
            AND sas.supplier_after_no LIKE CONCAT ('%',#{pageVO.afterNo},'%')
        </if>
        <if test="pageVO.supplierOrderNo != null and pageVO.supplierOrderNo != ''">
            AND tso.supplier_order_no LIKE CONCAT ('%',#{pageVO.supplierOrderNo},'%')
        </if>
        <if test="pageVO.orderType != null and pageVO.orderType != ''">
            AND tor.pay_way = #{pageVO.orderType}
        </if>
        <if test="pageVO.orderSettleStartTime != null and pageVO.orderSettleStartTime != '' and pageVO.orderSettleEndTime != null and pageVO.orderSettleEndTime != ''">
            AND tse.settle_time BETWEEN #{pageVO.orderSettleStartTime} AND #{pageVO.orderSettleEndTime}
        </if>
        <if test="pageVO.orderCreateStartTime != null and pageVO.orderCreateStartTime != '' and pageVO.orderCreateEndTime != null and pageVO.orderCreateEndTime != ''">
            AND tor.create_time BETWEEN #{pageVO.orderCreateStartTime} AND #{pageVO.orderCreateEndTime}
        </if>
        <if test="pageVO.orderType != null and pageVO.orderType != ''">
            AND tor.pay_way = #{pageVO.orderType}
        </if>
        <if test="pageVO.deliveryState != null and pageVO.deliveryState != ''">
            AND tsd.delivery_state = #{pageVO.deliveryState}
        </if>
        <if test="pageVO.branchId != null and pageVO.branchId != ''">
            AND tor.branch_id = #{pageVO.branchId}
        </if>
        GROUP BY
        tso.supplier_order_id,tse.sys_code,tsd.delivery_state
        <if test="pageVO.settleStatus != null and pageVO.settleStatus != ''">
            HAVING IF(SUM(CASE WHEN tse.state = 1 THEN 1 ELSE 0 END) > 1, '已结算', '未结算') =  #{pageVO.settleStatus}
        </if>
</select>


    <!-- 查询账户订单结算数据(入驻商小程序) -->
    <select id ="getSupperOrderSettleByDate" parameterType="com.zksr.trade.api.orderSettle.vo.OrderSupplierSettlePageVo" resultType="com.zksr.trade.api.orderSettle.dto.OrderSupplierSettleResDTO">
        SELECT
            DATE_FORMAT(settle.create_time,'%Y-%m-%d') AS createDate
			,(sum(settle.settle_amt) * -1) AS settleAmt
			,count(DISTINCT settle.order_id) AS orderCount
        FROM trd_settle  settle
        WHERE
            settle.supplier_id = #{supplierId}
            AND DATE_FORMAT(settle.create_time,'%Y-%m') = #{startDate}
        GROUP BY DATE_FORMAT(settle.create_time,'%Y-%m-%d')
        ORDER BY DATE_FORMAT(settle.create_time,'%Y-%m-%d') DESC
    </select>

    <!-- 查询账户订单结算数据明细(入驻商小程序) -->
    <select id="getSupperOrderSettleDtlByDateTime" parameterType="com.zksr.trade.api.orderSettle.vo.OrderSupplierSettlePageVo" resultType="com.zksr.trade.api.orderSettle.dto.OrderSupplierSettleResDTO">
        SELECT
		    sOrder.create_time
			,sOrder.supplier_order_no
			,sOrder.supplier_name
			,sOrder.sub_pay_amt
			,((select sum(settle.settle_amt) from trd_settle settle WHERE settle.supplier_order_id = sOrder.supplier_order_id) * -1) AS settleAmt
		FROM trd_supplier_order  sOrder
		LEFT JOIN trd_order tOrder ON sOrder.order_id = tOrder.order_id
		WHERE
		    sOrder.supplier_id = #{supplierId}
		    AND tOrder.pay_state = 1
			AND  sOrder.create_time BETWEEN CONCAT(#{startDate},' 00:00:00') and CONCAT(#{startDate},' 23:59:59')
    </select>

    <!--  查询账户订单结算流水数据(业务员) APP -->
    <select id="getColonelOrderSettleInfoPage" resultType="com.zksr.trade.api.orderSettle.dto.OrderSettleColonelResDTO">
        SELECT
            DISTINCT
            IFNULL(tAfter.create_time, tOrder.create_time) AS createTime  <!-- 创建时间 -->
            ,IFNULL(settle.after_id, settle.order_id) AS orderId      <!-- id -->
            ,IFNULL(settle.after_no, settle.order_no) AS orderNo      <!-- 编号 -->
            ,CASE
                WHEN settle.after_id IS NOT NULL THEN '售后订单'
                WHEN settle.order_id IS NOT NULL THEN '销售订单'
                ELSE '订单'
            END AS orderStateType
            ,IFNULL(tAfter.branch_id, tOrder.branch_id) AS branchId
        FROM trd_settle  settle
        LEFT JOIN trd_order tOrder ON tOrder.order_id = settle.order_id
        LEFT JOIN trd_after tAfter ON tAfter.after_id = settle.after_id

        <where>
            settle.merchant_id = #{pageVO.merchantId}
            AND settle.merchant_type = #{pageVO.merchantType}
            <if test="null != pageVO.state">
                AND settle.state = #{pageVO.state}
            </if>
            <if test="null != pageVO.startTime and pageVO.startTime != '' and null != pageVO.endTime and pageVO.endTime !=''">
                AND (
                        tOrder.create_time BETWEEN CONCAT(#{pageVO.startTime},' 00:00:00')  AND CONCAT(#{pageVO.endTime},' 23:59:59')
                        OR tAfter.create_time BETWEEN CONCAT(#{pageVO.startTime},' 00:00:00')  AND CONCAT(#{pageVO.endTime},' 23:59:59')
                    )
            </if>
            <if test=" null != pageVO.orderNo">
                AND (settle.order_no LIKE CONCAT ('%',#{pageVO.orderNo},'%') OR settle.after_no LIKE CONCAT ('%',#{pageVO.orderNo},'%') )
            </if>
            <if test="null != pageVO.branchId">
                AND (tOrder.branch_id = #{pageVO.branchId} OR tAfter.branch_id = #{pageVO.branchId} )
            </if>
        </where>
        ORDER BY createTime DESC
    </select>


    <!--  查询账户订单结算流水数据明细(业务员) APP -->
    <select id="getColonelOrderSettleDtlInfo" resultType="com.zksr.trade.api.orderSettle.dto.OrderDtlSettleResDTO">
        SELECT
            IFNULL(dtlSettle.profit, aDtlSettle.profit) AS profit      <!-- 利润 -->
            ,IFNULL(settle.after_id, settle.order_id) AS orderId      <!-- id -->
            ,IFNULL(settle.after_no, settle.order_no) AS orderNo      <!-- 编号 -->
            ,ifnull(orderDtl.sku_id, afterDtl.sku_id) AS skuId
            ,ifnull(orderDtl.spu_id, afterDtl.spu_id) AS spuId
            ,ifnull(dtlSettle.item_amt, afterDtl.return_amt) AS itemAmt
            ,ifnull(dtlSettle.item_qty, afterDtl.return_qty) AS itemQty
            ,CASE
                WHEN settle.after_id IS NOT NULL THEN afterDtl.return_unit
                ELSE orderDtl.order_unit
            END AS orderUnit
            ,settle.settle_amt
            ,settle.merchant_type
            ,settle.merchant_id
            ,settle.settle_rate  <!-- 实际分润比例 -->
            ,settle.state
        FROM trd_settle  settle
        LEFT JOIN trd_supplier_order_settle dtlSettle ON settle.supplier_order_dtl_id = dtlSettle.supplier_order_dtl_id
        LEFT JOIN trd_supplier_order_dtl orderDtl ON orderDtl.supplier_order_dtl_id = settle.supplier_order_dtl_id
        LEFT JOIN trd_order tOrder ON tOrder.order_id = settle.order_id

        LEFT JOIN trd_supplier_after_settle aDtlSettle ON  settle.supplier_order_dtl_id = aDtlSettle.supplier_after_dtl_id
        LEFT JOIN trd_supplier_after_dtl afterDtl ON afterDtl.supplier_after_dtl_id = settle.supplier_order_dtl_id
        LEFT JOIN trd_after tAfter ON tAfter.after_id = settle.after_id

        <where>
            settle.merchant_id = #{pageVO.merchantId}
            AND settle.merchant_type = #{pageVO.merchantType}
            <if test="null != pageVO.state">
                AND settle.state = #{pageVO.state}
            </if>
            <if test="null != pageVO.startTime and pageVO.startTime != '' and null != pageVO.endTime and pageVO.endTime !=''">
                AND (
                        tOrder.create_time BETWEEN CONCAT(#{pageVO.startTime},' 00:00:00')  AND CONCAT(#{pageVO.endTime},' 23:59:59')
                        OR tAfter.create_time BETWEEN CONCAT(#{pageVO.startTime},' 00:00:00')  AND CONCAT(#{pageVO.endTime},' 23:59:59')
                    )
            </if>
            <if test=" null != pageVO.orderNo">
                AND (settle.order_no LIKE CONCAT ('%',#{pageVO.orderNo},'%') OR settle.after_no LIKE CONCAT ('%',#{pageVO.orderNo},'%') )
            </if>
            <if test="null != pageVO.branchId">
                AND (tOrder.branch_id = #{pageVO.branchId} OR tAfter.branch_id = #{pageVO.branchId} )
            </if>
        </where>
    </select>

    <!-- 根据订单ID查询订单结算数据 -->
    <select id="getSettleListByOrderId" resultType="com.zksr.trade.domain.TrdSettle">
        SELECT
            *
        FROM trd_settle
        WHERE
            order_id = #{orderId}
    </select>
    <select id="selectColonelSettleTotal"
            resultType="com.zksr.trade.api.orderSettle.dto.ColonelFixSettleTotalRespVO">
        SELECT
            <!-- 日结算 -->
            IFNULL((SELECT SUM(settle_amt) FROM trd_settle WHERE merchant_id = #{colonelId} AND merchant_type = 'colonel' AND create_time > DATE_FORMAT(now(), '%Y-%m-%d')), 0) AS todaySettleAmt,
            <!-- 本月结算 -->
            IFNULL((SELECT SUM(settle_amt) FROM trd_settle WHERE merchant_id = #{colonelId} AND merchant_type = 'colonel' AND create_time > DATE_FORMAT(now(), '%Y-%m')), 0) AS monthSettleAmt,
            <!-- 上月结算 -->
            IFNULL((SELECT SUM(settle_amt) FROM trd_settle WHERE merchant_id = #{colonelId} AND merchant_type = 'colonel' AND create_time BETWEEN CONCAT(DATE_FORMAT(DATE_SUB(now(),INTERVAL 1 MONTH), '%Y-%m'), '-01') AND CONCAT(DATE_FORMAT(now(), '%Y-%m'), '-01')), 0) AS beforeMonthSettleAmt
    </select>
    <select id="selectColonelSettleTotalRange"
            resultType="com.zksr.trade.api.orderSettle.dto.ColonelFloatSettleTotalRespVO">
        SELECT
            IFNULL(SUM(CASE WHEN state = 1 THEN settle_amt ELSE 0 END), 0) settleAmt,
            IFNULL(SUM(CASE WHEN state IN (0, 2, 3) THEN settle_amt ELSE 0 END), 0) noSettleAmt
        FROM
            trd_settle
        WHERE
            merchant_id = #{colonelId}
            AND merchant_type = 'colonel'
            AND create_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="selectColonelSettleDayTotalByDateAndColonel"
            resultType="com.zksr.trade.api.orderSettle.dto.ColonelCommissionDayTotalRespDTO">
        SELECT
            a.operateDate
            , SUM(a.orderAmt) AS orderAmt
            , -SUM(a.afterAmt) AS afterAmt
            , SUM(a.settleAmt) AS settleAmt
        FROM (
            SELECT
                DATE_FORMAT(tor.create_time, '%Y-%m-%d') AS operateDate
                , SUM(tor.pay_amt) AS orderAmt
                , 0 AS afterAmt
                , 0 AS settleAmt
            FROM
                trd_order tor
            WHERE
                colonel_id = #{colonelId}
                AND pay_state IN (1, 3, 4)
                AND create_time BETWEEN #{startTime} AND #{endTime}
            GROUP BY
                DATE_FORMAT(tor.create_time, '%Y-%m-%d')

            UNION ALL

            SELECT
                DATE_FORMAT(tsad.refund_time, '%Y-%m-%d') AS operateDate
                , 0 AS orderAmt
                , SUM(tsad.refund_amt) AS afterAmt
                , 0 AS settleAmt
            FROM
                trd_supplier_after_dtl tsad
                LEFT JOIN trd_after tar ON tsad.after_id = tar.after_id
            WHERE
                tar.colonel_id = #{colonelId}
                AND tsad.refund_state = 2
                AND tsad.refund_time BETWEEN #{startTime} AND #{endTime}
            GROUP BY
                DATE_FORMAT(tsad.refund_time, '%Y-%m-%d')

            UNION ALL

            SELECT
                DATE_FORMAT(ts.create_time, '%Y-%m-%d') AS operateDate
                , 0 AS orderAmt
                , 0 AS afterAmt
                , SUM(settle_amt) AS settleAmt
            FROM
                trd_settle ts
            WHERE
                ts.merchant_id = #{colonelId}
                AND merchant_type = 'colonel'
                AND create_time BETWEEN #{startTime} AND #{endTime}
            GROUP BY
                DATE_FORMAT(ts.create_time, '%Y-%m-%d')

        )  a
        GROUP BY
            a.operateDate
        ORDER BY
            a.operateDate DESC
    </select>

    <select id="getSettleListBySupplierOrderDtlIds" resultType="com.zksr.trade.domain.TrdSettle">
        SELECT
            ts.*
        FROM
            trd_settle ts
        WHERE
            supplier_order_dtl_id IN
            <foreach collection="orderDtlIds" item="orderDtlId" open="(" separator="," close=")">
                #{orderDtlId}
            </foreach>

        UNION ALL

        SELECT
            ts.*
        FROM
            trd_settle ts
        WHERE
            supplier_order_dtl_id IN (
                SELECT
                    supplier_after_dtl_id
                FROM
                    trd_supplier_after_dtl
                WHERE
                    supplier_order_dtl_id  IN
                    <foreach collection="orderDtlIds" item="orderDtlId" open="(" separator="," close=")">
                        #{orderDtlId}
                    </foreach>
            )
    </select>

    <select id="getSettleBySupplierOrderDtlId" resultType="com.zksr.trade.domain.TrdSettle">
        SELECT
            ts.*
        FROM
            trd_settle ts
        WHERE
            ts.supplier_order_dtl_id IN
            <foreach collection="dtlIds" item="dtlId" open="(" separator="," close=")">
                #{dtlId}
            </foreach>
        UNION ALL
        SELECT
            ts.*
        FROM
            trd_settle ts
        WHERE
            ts.supplier_order_dtl_id IN (
                SELECT
                    supplier_after_dtl_id
                FROM
                    trd_supplier_after_dtl
                WHERE
                    supplier_order_dtl_id IN
                    <foreach collection="dtlIds" item="dtlId" open="(" separator="," close=")">
                        #{dtlId}
                    </foreach>
            )
    </select>
    <select id="getSettleBySupplierOrderDtlIdAndMerchantId" resultType="com.zksr.trade.domain.TrdSettle">
        SELECT
            ts.*
        FROM
            trd_settle ts
        WHERE
            ts.merchant_id = #{merchantId}
            AND ts.merchant_type = #{merchantType}
            AND ts.supplier_order_dtl_id  = #{dtlId}

        UNION ALL

        SELECT
            ts.*
        FROM
            trd_settle ts
        WHERE
            ts.merchant_id = #{merchantId}
            AND ts.merchant_type = #{merchantType}
            AND ts.supplier_order_dtl_id IN (
                SELECT
                    supplier_after_dtl_id
                FROM
                    trd_supplier_after_dtl
                WHERE
                    supplier_order_dtl_id = #{dtlId}
            )
    </select>

    <!-- 根据主订单ID、分佣账户类型、分佣账户 获得订单结算信息 -->
    <select id="getTrdSettleListByOrderIdAndMerchant" resultType="com.zksr.trade.domain.TrdSettle">
        SELECT
            supplier_order_dtl_id,
            SUM(settle_amt) AS settleAmt
        FROM
            trd_settle
        WHERE
            order_id = #{orderId}
            AND merchant_id = #{merchantId}
            AND merchant_type = #{merchantType}
            AND after_id is null
        GROUP By
            supplier_order_dtl_id
    </select>

</mapper>