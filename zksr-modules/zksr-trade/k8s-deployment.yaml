apiVersion: apps/v1
kind: Deployment
metadata:
  name: zksr-trade
  namespace: haixin
  labels:
    app: zksr-trade
spec:
  replicas: 4
  selector:
    matchLabels:
      app: zksr-trade
  template:
    metadata:
      labels:
        app: zksr-trade
    spec:
      containers:
        - name: zksr-trade
          image: haixin.tencentcloudcr.com/hisense/u-biz/prod/b2b/trade:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 6105
          env:
            - name: TZ
              value: "Asia/Shanghai"
            - name: SPRING_PROFILES_ACTIVE
              value: "dev"
          resources:
            requests:
              memory: "6Gi"
              cpu: "1000m"
            limits:
              memory: "8Gi"
              cpu: "4000m"
      imagePullSecrets:
        - name: hisense