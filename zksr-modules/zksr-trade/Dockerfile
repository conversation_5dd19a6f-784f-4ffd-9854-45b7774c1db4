# Name:     zksr-mall-cloud-product
# Time:     2024-01-19
## AdoptOpenJDK 停止发布 OpenJDK 二进制，而 Eclipse Temurin 是它的延伸，提供更好的稳定性
FROM image.midea.com/annto-public/openjdk:8

RUN mkdir /app

WORKDIR /app

COPY ./target/zksr-modules-trade.jar /app

# 设置 JVM 参数
ENV JAVA_OPTS="-Xms4g -Xmx4g \
    -XX:+UseG1GC \
    -XX:MaxGCPauseMillis=200 \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/tmp/heapdump.hprof \
    -Djava.security.egd=file:/dev/./urandom"

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "/app/zksr-modules-trade.jar"]

EXPOSE 6105

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
