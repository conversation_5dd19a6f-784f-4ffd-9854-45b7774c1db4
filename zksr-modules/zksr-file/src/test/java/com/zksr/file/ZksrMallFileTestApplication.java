package com.zksr.file;

import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.domain.R;
import com.zksr.file.job.exe.ExportJob;
import com.zksr.file.job.handler.impl.TrdOrderExportHandler;
import com.zksr.system.api.RemoteExportService;
import com.zksr.system.api.export.vo.SysExportJob;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallFileApplication.class)
@Slf4j
public class ZksrMallFileTestApplication {

    /*@Autowired
    private TrdOrderExportHandler trdOrderExportHandler;

    // system调用服务
    @Autowired
    private RemoteExportService remoteExportService;

    @Test
    public void testRedisService() throws InterruptedException {

        R<SysExportJob> serviceJob = remoteExportService.getJob(214, SecurityConstants.INNER);
        new ExportJob(serviceJob.getData(), trdOrderExportHandler).run();
    }*/
}
