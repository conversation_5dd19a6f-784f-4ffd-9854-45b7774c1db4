package com.zksr.file.job.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.saleClass.SaleClassApi;
import com.zksr.product.api.saleClass.dto.SaleClassExportVo;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.zksr.product.constant.ProductConstant.PRDT_ITEM_TYPE_0;
import static com.zksr.product.constant.ProductConstant.PRDT_ITEM_TYPE_1;

/**
 * 城市展示分类导出
 */
@ExportHandler(value = ExportType.PRDT_SALE_CLASS_EXPORT, defaultPage = false)
public class PrdtSaleClassExportHandler extends ReportAbstractContentHandler<SaleClassExportVo> {

    @Resource
    private SaleClassApi saleClassApi;
    @Override
    public List getList(ExportJobContext jobContext) {
        SaleClassExportVo pageVo = jobContext.getQueryData(SaleClassExportVo.class);
        pageVo.setPage(jobContext.page());
        ArrayList<SaleClassExportVo> list = new ArrayList<>();
        int i = 1;
        for (; ; ) {
            pageVo.setPageSize(30);
            pageVo.setPageNo((i));
            List<SaleClassExportVo> checkedData = saleClassApi.getSaleClassExportList(pageVo).getCheckedData();
            if (ToolUtil.isEmpty(checkedData)) {
                break;
            }
            list.addAll(checkedData);
            i++;
        }
        return list;
    }
}
