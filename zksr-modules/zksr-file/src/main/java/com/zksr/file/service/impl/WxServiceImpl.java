package com.zksr.file.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.account.api.pay.PayApi;
import com.zksr.account.api.pay.PayFlowApi;
import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayOrderStatusRespEnum;
import com.zksr.common.core.enums.WxReceivePayStatusEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.html.EscapeUtil;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.common.redis.utils.RedisWxTokenUtil;
import com.zksr.common.third.wx.AesException;
import com.zksr.common.third.wx.WXBizMsgCrypt;
import com.zksr.common.third.wx.WxUtils;
import com.zksr.file.api.file.vo.MchProfitRateReqVO;
import com.zksr.file.api.file.vo.MchProfitRateRespVO;
import com.zksr.file.service.IFileCacheService;
import com.zksr.file.service.WxService;
import com.zksr.system.api.partnerConfig.dto.WxB2bPayConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_PARTNER_CONFIG_COMPONENT_PRE_AUTH_CODE_ERR;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信服务
 * @date 2024/8/12 18:49
 */
@Service
@Slf4j
public class WxServiceImpl implements WxService {

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private RedisService redisService;

    @Resource
    private PayApi payApi;

    @Resource
    private PayFlowApi payFlowApi;

    @Autowired
    private IFileCacheService fileCacheService;

    private final static String VALIDATE_FIELD = "out_trade_no";

    @Override
    public void receiveMerchantAuth(String body) {

        // 获取服务商appid
        String appId = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_ID);
        if (StringUtils.isEmpty(appId)) {
            log.info("接受到服务器推送的ticket但是没有配置服务商appid信息, encryption={}", body);
            return;
        }

        // 获取服务器aesKey
        String aesKey = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_AES);
        if (StringUtils.isEmpty(aesKey)) {
            log.info("接受到服务器推送的ticket但是没有配置服务商aesKey信息, encryption={}", body);
            return;
        }
        // xml 转map
        Map<String, Object> xmledToMap = XmlUtil.xmlToMap(body);
        if (!xmledToMap.containsKey("Encrypt")) {
            log.info("没有查询到加密信息体");
            return;
        }
        WXBizMsgCrypt crypt = new WXBizMsgCrypt(aesKey, appId);
        try {
            // 解密数据
            String decryptStr = crypt.decrypt02(String.valueOf(xmledToMap.get("Encrypt")));
            // 转换json
            Map<String, Object> decryptData = XmlUtil.xmlToMap(decryptStr);
            if (!decryptData.containsKey("ComponentVerifyTicket")) {
                log.info("没有有效ticket, decryptData={}", decryptData);
                return;
            }
            String componentVerifyTicket = String.valueOf(decryptData.get("ComponentVerifyTicket"));
            // 设置ticket
            // redisService.setCacheObject(StringUtils.format("{}{}", RedisConstants.ACCESS_TICKET, appId), componentVerifyTicket);
            String componentAccessTokenKey = RedisConstants.COMPONENT_ACCESS_TOKEN + appId;
            // 获取 componentAccessToken
            String componentAccessToken = redisService.getCacheObject(componentAccessTokenKey);
            if (StringUtils.isEmpty(componentAccessToken)) {
                // 如果没有就直接更新获取
                HashMap<String, String> req = new HashMap<>();
                req.put("component_appid", appId);
                req.put("component_appsecret", redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_KEY));
                req.put("component_verify_ticket", componentVerifyTicket);
                String result = HttpUtil.post("https://api.weixin.qq.com/cgi-bin/component/api_component_token", JSON.toJSONString(req));
                log.info("请求获取componentAccessToken结果, result={}", result);
                JSONObject jsonObject = JSON.parseObject(result);
                if (jsonObject.containsKey("component_access_token")) {
                    // 设置有效期为1小时20分钟
                    redisService.setCacheObject(
                            componentAccessTokenKey,
                            jsonObject.getString("component_access_token"),
                            80L,
                            TimeUnit.MINUTES
                    );
                }
            }
        } catch (AesException e) {
            log.error("解密数据异常", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getComponentAccessToken() {
        // 获取服务商appid
        String appId = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_ID);
        if (StringUtils.isEmpty(appId)) {
            log.info("获取componentAccessToken, 但是没有配置服务商appId");
            return null;
        }
        String componentAccessTokenKey = RedisConstants.COMPONENT_ACCESS_TOKEN + appId;
        return redisService.getCacheObject(componentAccessTokenKey);
    }

    @Override
    public String getPreAuthCode() {
        // 获取服务商appid
        String appId = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_ID);
        if (StringUtils.isEmpty(appId)) {
            log.info("获取componentAccessToken, 但是没有配置服务商appId");
            return null;
        }
        String preAuthCodeUrl = StringUtils.format("https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode?component_access_token={}", getComponentAccessToken());
        log.info("preAuthCodeUrl={}", preAuthCodeUrl);
        String preAuthRespStr = HttpUtil.post(preAuthCodeUrl, JSON.toJSONString(MapUtil.of("component_appid", appId)));
        JSONObject preAuthResp = JSON.parseObject(preAuthRespStr);
        if (!preAuthResp.containsKey("pre_auth_code")) {
            throw exception(SYS_PARTNER_CONFIG_COMPONENT_PRE_AUTH_CODE_ERR);
        }
        return preAuthResp.getString("pre_auth_code");
    }

    @Override
    public void updateAuthCode(String appid, String authCode) {
        // 获取服务商appid
        String providerId = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_ID);
        if (StringUtils.isEmpty(providerId)) {
            log.info("更新authorizationCode, 但是没有配置服务商appId, 授权小程序appid={}, authCode={}", appid, authCode);
            return;
        }

        // 1, 获取 authorizerRefreshToken
        String authorizerRefreshToken = null;
        {
            HashMap<String, String> request = new HashMap<>();
            request.put("component_appid", providerId);
            request.put("authorization_code", authCode);
            String requestData = JSON.toJSONString(request);
            log.info("/cgi-bin/component/api_query_auth, req={}", requestData);
            String url = StringUtils.format("https://api.weixin.qq.com/cgi-bin/component/api_query_auth?component_access_token={}", getComponentAccessToken());
            String res = HttpUtil.post(url, requestData);
            log.info("/cgi-bin/component/api_query_auth, resp={}", res);

            JSONObject resObj = JSON.parseObject(res);
            if (resObj.containsKey("authorization_info")) {
                authorizerRefreshToken = resObj.getJSONObject("authorization_info").getString("authorizer_refresh_token");
            }
        }

        // 2, 通过 authorizerRefreshToken 获取 authorizationCode
        if (StringUtils.isNotEmpty(authorizerRefreshToken)) {
            HashMap<String, String> request = new HashMap<>();
            request.put("component_appid", providerId);
            request.put("authorizer_appid", appid);
            request.put("authorizer_refresh_token", authorizerRefreshToken);
            String requestData = JSON.toJSONString(request);
            log.info("/cgi-bin/component/api_authorizer_token, req={}", requestData);
            String url = StringUtils.format("https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token={}", getComponentAccessToken());
            String res = HttpUtil.post(url, requestData);
            log.info("/cgi-bin/component/api_authorizer_token, resp={}", res);

            JSONObject resObj = JSON.parseObject(res);
            if (resObj.containsKey("authorizer_access_token")) {
                {
                    String cacheKey = RedisConstants.AUTHORIZER_ACCESS_TOKEN + appid;
                    // 设置有效期为1小时20分钟
                    redisService.setCacheObject(
                            cacheKey,
                            resObj.getString("authorizer_access_token"),
                            80L,
                            TimeUnit.MINUTES
                    );
                }
                // 刷新令牌, 缓存维护
                {
                    String cacheKey = RedisConstants.AUTHORIZER_REFRESH_TOKEN + appid;
                    redisService.setCacheObject(
                            cacheKey,
                            resObj.getString("authorizer_refresh_token")
                    );
                }
            }
        }
    }

    @Override
    public String getAuthorizerAccessToken(String appid) {
        String authorizerAccessToken = redisService.getCacheObject(RedisConstants.AUTHORIZER_ACCESS_TOKEN + appid);
        if (StringUtils.isNotEmpty(authorizerAccessToken)) {
            return authorizerAccessToken;
        }
        // 获取服务商appid
        String providerId = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_ID);
        if (StringUtils.isEmpty(providerId)) {
            log.info("更新authorizerAccessToken, 但是没有配置服务商appId, 授权小程序appid={}", appid);
            return null;
        }
        String refreshTokenKey = RedisConstants.AUTHORIZER_REFRESH_TOKEN + appid;
        String refreshToken = redisSysConfigService.get(refreshTokenKey);
        if (StringUtils.isEmpty(refreshToken)) {
            log.info("更新authorizerAccessToken, 未发现refreshToken, 授权小程序appid={}", appid);
            return null;
        }

        HashMap<String, String> request = new HashMap<>();
        request.put("component_appid", providerId);
        request.put("authorizer_appid", appid);
        request.put("authorizer_refresh_token", refreshToken);
        String requestData = JSON.toJSONString(request);
        log.info("/cgi-bin/component/api_authorizer_token, req={}", requestData);
        String url = StringUtils.format("https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token={}", getComponentAccessToken());
        String res = HttpUtil.post(url, requestData);
        log.info("/cgi-bin/component/api_authorizer_token, resp={}", res);

        JSONObject resObj = JSON.parseObject(res);
        if (resObj.containsKey("authorizer_access_token")) {
            authorizerAccessToken = resObj.getString("authorizer_access_token");
            // authorizerAccessToken, 缓存维护
            {
                String cacheKey = RedisConstants.AUTHORIZER_ACCESS_TOKEN + appid;
                // 设置有效期为1小时20分钟
                redisService.setCacheObject(
                        cacheKey,
                        authorizerAccessToken,
                        80L,
                        TimeUnit.MINUTES
                );
            }
            // 刷新令牌, 缓存维护
            {
                String cacheKey = RedisConstants.AUTHORIZER_REFRESH_TOKEN + appid;
                redisService.setCacheObject(
                        cacheKey,
                        resObj.getString("authorizer_refresh_token")
                );
            }
            return authorizerAccessToken;
        }
        return null;
    }

    @Override
    public void refreshAuthorizerAccessToken() {
        // 筛选凭证
    }

    @Override
    public void processWxReceiveMsg(Map<String, String> params, String body) {

        /**
         * params 案例
         * {
         *   "signature": "e8c1c023b63f46cdeadf930c9630b6e4d1eca271",
         *   "timestamp": "1723620477",
         *   "nonce": "1734470737",
         *   "openid": "o1Ska45hzwHWbRWI8fBNjMeTp_3E",
         *   "encrypt_type": "aes",
         *   "msg_signature": "daf93be867c87c53522832367ca51f2972adf874"
         * }
         */

        /**
         * body 案例
         * {
         *   "ToUserName": "gh_29bad549f1a6",
         *   "FromUserName": "o1Ska45hzwHWbRWI8fBNjMeTp_3E",
         *   "CreateTime": 1723620477,
         *   "MsgType": "event",
         *   "Event": "retail_pay_notify",
         *   "appid": "wx1f8f0a0b87603148",
         *   "mchid": "1682898180",
         *   "out_trade_no": "XS240814800000273615",
         *   "order_id": "o20240814152648597822066",
         *   "pay_status": "ORDER_PAY_SUCC",
         *   "pay_time": "2024-08-14 15:27:56",
         *   "attach": "1",
         *   "payer_openid": "o1Ska45hzwHWbRWI8fBNjMeTp_3E",
         *   "amount": {
         *     "order_amount": 4,
         *     "payer_amount": 4,
         *     "currency": "CNY"
         *   },
         *   "wxpay_transaction_id": "4200002360202408147614523011",
         *   "env": 0,
         *   "Encrypt": "ATkd7P51hGRMMthZGVQvvE48qxaJH0eBS4Jer1fsoDYFiJeAhYeHaZQZQi5KQHcmyg+FCyi7yI1BbhJmZQlCJO2KAikJfnrhY8RKgbW3Isr2k1SAK2QhB3NErnZxAAVmWoHMW3R5rqQWdftzYFy6NGNtc2ujL4TitHCNCezRGbj0x9v3iUSvpkw62ci+diT8cuic64/rOG0Y1a++yHFK27QAmFCCI/4QlzAIPUj+Pm0I8TOXei1J5rVfv1Jmw56VPD5P+PKNfkEQfx899HUDn4RUgc5D97km3/pWNmJgOURnaDocFFR8wTYm4XPCgnLObCiR8ZIrsAvU7686FnM1itvtY5nV9ak0MpcIsTWFFNTZipF4dQZm3/h3B2JpdJz43Oaq7A4ZUTv7Uz6JHivYKFsSTlCd5BZjpaEtp0GLQfzm/1kugXkvm2enNliG4jBrhHtL5rseEvsteGqK8BdQeVWTq9ne2wUESfZzi8PK+oYbTay+Z7s38MXV6CGhn455zIr+uQisyYgIjVjAG2wybcdwYdvlcXfKZ5XvBazQ0ZyqO7rj48szGasodcp6AraYE5YscJm1C4ZD9niS8zI4FafpMJcLjnLMsvphcNHOTIcgxdUNFzVxiqF79mvixfjxTLPAXCymz4qgYm0krHF8xZmtV7OClEYxS2PvdmY1mMUDqhoOXHLpYEbyUmFi8RK05BRPcxZIT0Z/yWUUf0SihHL8KxTXvTfha1SCGYUtvRUCCZh0exKH9IPZTLs3mTp0"
         * }
         */

        if (StringUtils.isNotEmpty(body)) {
            JSONObject bodyObj = JSON.parseObject(body);
            if (bodyObj.containsKey("Event")) {
                String event = bodyObj.getString("Event");
                switch (event) {
                    case "retail_pay_notify":
                        processPayCallBack(bodyObj);
                        break;
                }
            }
        }
    }

    @Override
    public MchProfitRateRespVO setMchProfitRate(MchProfitRateReqVO mchProfitRateReqVO) {
        String authorizerAccessToken = getAuthorizerAccessToken(mchProfitRateReqVO.getAppid());

        HashMap<String, Object> request = new HashMap<>();
        request.put("sub_mchid", mchProfitRateReqVO.getSubMchid());
        request.put("profit_rate", mchProfitRateReqVO.getProfitRate().multiply(new BigDecimal("10000")).intValue());
        String requestData = JSON.toJSONString(request);
        log.info("/retail/B2b/setmchprofitrate, req={}", requestData);
        String url = StringUtils.format("https://api.weixin.qq.com/retail/B2b/setmchprofitrate?access_token={}", authorizerAccessToken);
        String res = HttpUtil.post(url, requestData);
        log.info("/retail/B2b/setmchprofitrate, resp={}", res);

        JSONObject resObj = JSONObject.parseObject(res);
        MchProfitRateRespVO respVO = new MchProfitRateRespVO();
        if (resObj.getInteger("errcode") != 0) {
            respVO.setSuccess(false);
            respVO.setMsg(resObj.getString("errmsg"));
        }
        respVO.setSuccess(true);
        return respVO;
    }

    @Override
    public String getAccessToken(String appid) {
        return RedisWxTokenUtil.getAppletAccessToken(appid);
    }

    @Override
    public String getOpenToken() {
        // 获取服务商appid
        String wechatServiceCenterServerToken = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_CENTER_SERVER_TOKEN);
        if (StringUtils.isEmpty(wechatServiceCenterServerToken)) {
            log.info("未配置 CacheConstants.WECHAT_SERVICE_CENTER_SERVER_TOKEN, 对外部验证失败");
            return null;
        }
        return wechatServiceCenterServerToken;
    }

    @Override
    public String getAppWxB2bAuthUrl(String key, String appid) {
        // 获取第三方应用, 服务商componentAccessToken
        // 如果系统配置了调用连接则使用调用连接获取
        // 获取服务商appid
        String providerId = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_ID);
        if (StringUtils.isEmpty(providerId)) {
            log.info("更新authorizerAccessToken, 但是没有配置服务商appId, 授权小程序appid={}", appid);
            return null;
        }
        // 获取preAuthCode
        String preAuthCode = getPreAuthCode();
        if (StringUtils.isEmpty(preAuthCode)) {
            throw exception(SYS_PARTNER_CONFIG_COMPONENT_PRE_AUTH_CODE_ERR);
        }
        // 组装授权链接
        String authUrl = URLUtil.encodeAll(StringUtils.format(redisService.getCacheObject(CacheConstants.WECHAT_SERVICE_AUTH_URL), appid));
        StringBuilder param = new StringBuilder();
        param.append("component_appid=").append(providerId).append(StringPool.AMPERSAND);
        param.append("pre_auth_code=").append(preAuthCode).append(StringPool.AMPERSAND);
        param.append("redirect_uri=").append(authUrl).append(StringPool.AMPERSAND);
        param.append("auth_type=").append("2").append(StringPool.AMPERSAND);
        param.append("biz_appid=").append(appid);
        return StringUtils.format("https://open.weixin.qq.com/wxaopen/safe/bindcomponent?action=bindcomponent&no_scan=1&{}", param.toString());
    }

    /**
     * !@微信 - 2、支付回调
     * 处理微信支付消息
     * @param bodyObj
     */
    private void processPayCallBack(JSONObject bodyObj) {

        String attach = bodyObj.getString("attach");
        Map<String, String> extMap = EscapeUtil.parseQueryString(attach);
        String orderType = extMap.get("orderType");
        String orderNo = extMap.get("orderNo");
        Long payFlowId = bodyObj.getLong("out_trade_no");
        // 验证支付订单号 out_trade_no, 是否被篡改
        PayFlowDTO payFlowDTO = payFlowApi.getAccPayFlowOrder(payFlowId).getCheckedData();
        if (Objects.isNull(payFlowDTO)) {
            log.error("微信B2B支付回调, 找不到发起记录 {}", attach);
            return;
        }
        WxB2bPayConfigDTO wxB2BPayConfigDTO = fileCacheService.getWxB2BPayConfigDTO(payFlowDTO.getSysCode());
        if (Objects.nonNull(wxB2BPayConfigDTO) && StringUtils.isNotEmpty(wxB2BPayConfigDTO.getEncodingAESKey())) {
            try {
                String decrypt = WxUtils.decrypt(bodyObj.getString("Encrypt"), wxB2BPayConfigDTO.getEncodingAESKey());
                JSONObject decryptObj = JSON.parseObject(decrypt);
                if (!decryptObj.getString(VALIDATE_FIELD).equals(bodyObj.getString(VALIDATE_FIELD))) {
                    log.warn("解密微信支付支付单号解密不一致明文单号:{}, 密文单号:{}", bodyObj.getString(VALIDATE_FIELD), decryptObj.getString(VALIDATE_FIELD));
                    return;
                }
                // 通过解密参数重新获取订单参数
                extMap = EscapeUtil.parseQueryString(decryptObj.getString("attach"));
                orderType = extMap.get("orderType");
                orderNo = extMap.get("orderNo");
            } catch (Exception e) {
                log.error("解密微信支付消息异常", e);
                return;
            }
        }
        if (StringUtils.isEmpty(orderNo)) {
            log.error("微信B2B支付回调, 订单号无效 {}", payFlowId);
            return;
        }
        String payStatus = bodyObj.getString("pay_status");
        if (!WxReceivePayStatusEnum.ORDER_PAY_SUCC.getCode().equals(payStatus)) {
            log.warn("微信B2B支付回调, 支付状态 {}", payStatus);
            return;
        }
        // 获取参数
        PayOrderRespDTO orderRespDTO = new PayOrderRespDTO();
        orderRespDTO.setFlowId(payFlowId)
                .setOrderNo(orderNo)
                .setOutTradeNo(bodyObj.getString("wxpay_transaction_id"))
                .setStatus(PayOrderStatusRespEnum.FINISH.getStatus())
                .setPayPlatform(PayChannelEnum.WX_B2B_PAY.getCode())
                .setOrderType(Integer.valueOf(orderType))
        ;
        payApi.payCallBack(orderRespDTO);
    }
}
