package com.zksr.file.importJob.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.file.api.constant.ImportTypeEnum;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.file.importJob.handler.IImportContentHandler;
import com.zksr.member.api.branch.excel.MemBranchImportExcel;
import com.zksr.product.api.brand.BrandApi;
import com.zksr.product.api.brand.excel.BrandImportExcel;
import com.zksr.product.api.brand.form.BrandImportForm;
import com.zksr.product.api.catgory.CatgoryApi;
import com.zksr.product.api.catgory.excel.CategoryImportExcel;
import com.zksr.product.api.catgory.form.CategoryImportForm;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.FileImportApi;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class CategoryImportHandler extends ImportContentHandlerEvent<CategoryImportExcel> implements IImportContentHandler<CategoryImportExcel> {

    @Resource
    private CatgoryApi catgoryApi;
    @Resource
    private FileImportApi fileImportApi;


    @Override
    public String getImportType() {
        return ImportTypeEnum.CATEGORY_IMPORT.getCode();
    }

    public void importData(SysFileImportVO sysFileImport) {
        List<List<CategoryImportExcel>> lists = excelData(ImportTypeEnum.CATEGORY_IMPORT.getName(), 1, 1, CategoryImportExcel.class, sysFileImport.getFileUrl());
        SysFileImport fileImport = getFileImport(sysFileImport.getFileImportId());

        CategoryImportForm categoryImportForm = new CategoryImportForm();
        categoryImportForm.setFileImportId(sysFileImport.getFileImportId());
        categoryImportForm.setSysCode(sysFileImport.getSysCode());

        FileImportHandlerVo vo = new FileImportHandlerVo();
        List<SysFileImportDtl> list = new ArrayList<>();
        for (List<CategoryImportExcel> excels : lists) {
            log.info("平台管理类别导第{}数据导入开始",vo.getTotalNum()+1);
            categoryImportForm.setList(excels);
            categoryImportForm.setSeq(vo.getTotalNum());
            CommonResult<String> stringCommonResult = catgoryApi.importBaseCategoryEvent(categoryImportForm);
            importResult(stringCommonResult,list,vo,excels.size(),ImportTypeEnum.CATEGORY_IMPORT.getName());
        }
        renewalFileImport(fileImport,list,vo,ImportTypeEnum.CATEGORY_IMPORT.getName());

    }

}
