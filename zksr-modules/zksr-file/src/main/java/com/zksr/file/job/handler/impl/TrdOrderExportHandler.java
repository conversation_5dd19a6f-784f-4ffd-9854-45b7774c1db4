package com.zksr.file.job.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.DcOrderPageReqApiVO;
import com.zksr.trade.api.order.vo.SupplierOrderDtlInfoExportVO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: SKU价格导出
 * @date 2024/11/2 15:41
 */
@ExportHandler(value = ExportType.TRD_ORDER_EXPORT, defaultPage = false)
public class TrdOrderExportHandler extends ReportAbstractContentHandler<SupplierOrderDtlInfoExportVO> {
    @Resource
    private OrderApi orderApi;
    @Override
    public List<SupplierOrderDtlInfoExportVO> getList(ExportJobContext jobContext) {
        DcOrderPageReqApiVO pageVO = jobContext.getQueryData(DcOrderPageReqApiVO.class);
        pageVO.setPage(jobContext.page());
        List<SupplierOrderDtlInfoExportVO> list = new ArrayList<>();

        Long dcId = jobContext.getLoginUser().getSysUser().getDcId();
        Long supplierId = jobContext.getLoginUser().getSysUser().getSupplierId();
        if (ObjectUtil.isNotNull(dcId)){
            pageVO.setDcId(dcId);
        }
        if (ObjectUtil.isNotNull(supplierId)){
            pageVO.setSupplierId(supplierId);
        }

//        BigExcelWriter writer = jobContext.getWriter();
        int i = 1;
        for (;;) {
            // 这里每次分页查询50条，因返回数据查询速度过慢，超出50条会导致接口超时，后续查询优化可将分页条数进行调整
            // 设置为20，太多会超时
            pageVO.setPageSize(20);
            pageVO.setPageNo((i - 1) * pageVO.getPageSize());
            List<SupplierOrderDtlInfoExportVO> list1 = orderApi.getSupplierOrderDtlInfoExport(pageVO).getCheckedData();
            if (ToolUtil.isEmpty(list1) || list1.isEmpty()) {
                break;
            }
            list.addAll(list1);
            i++;
//            writer.write(list);
        }
        return list;
    }
}
