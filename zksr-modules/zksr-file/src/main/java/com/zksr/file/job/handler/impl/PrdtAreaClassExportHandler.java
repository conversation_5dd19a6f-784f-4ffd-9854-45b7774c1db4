package com.zksr.file.job.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.saleClass.SaleClassApi;
import com.zksr.product.api.saleClass.dto.SaleClassExportVo;
import com.zksr.product.api.spu.dto.SpuExportDTO;
import com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRespDTO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.zksr.product.constant.ProductConstant.*;

/**
 * 城市展示分类导出
 */
@ExportHandler(value = ExportType.PRDT_AREA_CLASS_EXPORT, defaultPage = false)
public class PrdtAreaClassExportHandler extends ReportAbstractContentHandler<PrdtAreaClassExportVo> {

    @Resource
    private AreaClassApi areaClassApi;
    @Resource
    private SaleClassApi saleClassApi;

    @Override
    public List getList(ExportJobContext jobContext) {

        //校验登陆账号是平台商还是运营商
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        Integer itemType = ObjectUtil.isNull(dcId) ? PRDT_ITEM_TYPE_0 : PRDT_ITEM_TYPE_1;

        //查询分页列表
        if (PRDT_ITEM_TYPE_1.equals(itemType)) {
            PrdtAreaClassExportVo pageVo = jobContext.getQueryData(PrdtAreaClassExportVo.class);
            pageVo.setPage(jobContext.page());
            ArrayList<PrdtAreaClassExportVo> list = new ArrayList<>();
            int i = 1;
            for (; ; ) {
                pageVo.setPageSize(30);
                pageVo.setPageNo((i));
                List<PrdtAreaClassExportVo> checkedData = areaClassApi.getPrdtAreaClassExportList(pageVo).getCheckedData();
                if (ToolUtil.isEmpty(checkedData)) {
                    break;
                }
                list.addAll(checkedData);
                i++;
            }
            return list;
        } else {
            SaleClassExportVo pageVo = jobContext.getQueryData(SaleClassExportVo.class);
            pageVo.setPage(jobContext.page());
            ArrayList<SaleClassExportVo> list = new ArrayList<>();
            int i = 1;
            for (; ; ) {
                pageVo.setPageSize(30);
                pageVo.setPageNo((i));
                List<SaleClassExportVo> checkedData = saleClassApi.getSaleClassExportList(pageVo).getCheckedData();
                if (ToolUtil.isEmpty(checkedData)) {
                    break;
                }
                list.addAll(checkedData);
                i++;
            }
            return list;
        }
    }
}
