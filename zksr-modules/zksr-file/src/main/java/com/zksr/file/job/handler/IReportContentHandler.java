package com.zksr.file.job.handler;

import com.zksr.common.core.web.domain.BaseEntity;
import com.zksr.file.job.exe.ExportJobContext;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/1/15
 * @desc
 */
public interface IReportContentHandler<T extends BaseEntity> {

    List<T> getList(ExportJobContext jobContext);

    Class<T> getClassType();

    boolean defaultPage();

    boolean permissions();

}
