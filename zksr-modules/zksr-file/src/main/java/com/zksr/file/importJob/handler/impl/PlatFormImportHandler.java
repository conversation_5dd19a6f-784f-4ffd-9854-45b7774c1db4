package com.zksr.file.importJob.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.file.api.constant.ImportTypeEnum;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.file.importJob.handler.IImportContentHandler;
import com.zksr.member.api.colonel.excel.MemColonelImportExcel;
import com.zksr.member.api.colonel.form.MemColonelImportForm;
import com.zksr.product.api.platform.PlatFormApi;
import com.zksr.product.api.platform.excel.ProductPlarformImportExcel;
import com.zksr.product.api.platform.form.PlatFormImportForm;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.FileImportApi;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import com.zksr.trade.api.driver.DriverApi;
import com.zksr.trade.api.driver.excel.TrdDriverImportExcel;
import com.zksr.trade.api.driver.form.TrdDriverImportForm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class PlatFormImportHandler extends ImportContentHandlerEvent<ProductPlarformImportExcel> implements IImportContentHandler<ProductPlarformImportExcel> {

    @Resource
    private PlatFormApi platFormApi;

    @Override
    public String getImportType() {
        return ImportTypeEnum.PRODUCT_PLAT_FORM.getCode();
    }

    public void importData(SysFileImportVO sysFileImport) {
        List<List<ProductPlarformImportExcel>> lists = excelData(ImportTypeEnum.PRODUCT_PLAT_FORM.getName(), 0, 1, ProductPlarformImportExcel.class, sysFileImport.getFileUrl());
        SysFileImport fileImport = getFileImport(sysFileImport.getFileImportId());

        PlatFormImportForm form = new PlatFormImportForm();
        form.setFileImportId(sysFileImport.getFileImportId());
        form.setSysCode(sysFileImport.getSysCode());
        form.setDcId(sysFileImport.getDcId());

        FileImportHandlerVo vo = new FileImportHandlerVo();
        List<SysFileImportDtl> list = new ArrayList<>();
        for (List<ProductPlarformImportExcel> excels : lists) {
            log.info("平台商品库信息第{}数据导入开始",vo.getTotalNum()+1);
            form.setList(excels);
            form.setSeq(vo.getTotalNum());
            CommonResult<String> stringCommonResult = platFormApi.importDataEvent(form);
            importResult(stringCommonResult,list,vo,excels.size(),ImportTypeEnum.PRODUCT_PLAT_FORM.getName());
        }
        renewalFileImport(fileImport,list,vo,ImportTypeEnum.PRODUCT_PLAT_FORM.getName());

    }

}
