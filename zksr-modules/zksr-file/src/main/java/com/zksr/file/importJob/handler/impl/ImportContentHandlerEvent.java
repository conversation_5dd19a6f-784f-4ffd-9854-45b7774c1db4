package com.zksr.file.importJob.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.member.api.branch.form.BranchImportForm;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.FileImportApi;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
public class ImportContentHandlerEvent <T>{

    @Resource
    private FileImportApi fileImportApi;


    /**
     * 解析excel
     * @param importName 导入类型名称
     * @param titleNum 行数
     * @param pageSize 分页
     * @param tClass
     * @param fileUrl
     * @return
     * @param <T>
     */
    public <T> List<List<T>> excelData(String importName,int titleNum,int pageSize, Class<T> tClass, String fileUrl) {
        log.info(importName + "导入开始");
        ExcelUtil<T> util = new ExcelUtil<>(tClass);
        List<T> orderExportList = util.getImportExcelData(fileUrl, titleNum);
        log.info(importName + "导入开始数据{}", orderExportList);
        List<List<T>> partition = Lists.partition(orderExportList, pageSize);//数据分页
        return partition;
    }

    /**
     * 导入结果
     * @param stringCommonResult 导入结果
     * @param list
     * @param vo
     * @param thisNum
     * @param importName
     * @return
     */
    public Boolean importResult(CommonResult<String> stringCommonResult, List<SysFileImportDtl> list,FileImportHandlerVo vo, Integer thisNum,String importName){
        if (stringCommonResult.isError()|| StringUtils.isEmpty(stringCommonResult.getData())){
            vo.setStringBuffer(vo.getStringBuffer().append(StringUtils.format("第{}-{}数据{}"+"\n",vo.getTotalNum()+1,vo.getTotalNum()+thisNum,stringCommonResult.getMsg())));
            vo.setFailureNum(vo.getFailureNum()+thisNum);
            vo.setTotalNum(vo.getTotalNum()+thisNum);
            return false;
        }
        FileImportHandlerVo fileImportHandlerVo = JsonUtils.toJavaClass(stringCommonResult.getData(), FileImportHandlerVo.class);
        vo.setSuccessNum(vo.getSuccessNum()+fileImportHandlerVo.getSuccessNum());
        vo.setFailureNum(vo.getFailureNum()+fileImportHandlerVo.getFailureNum());
        vo.setTotalNum(vo.getTotalNum()+fileImportHandlerVo.getTotalNum());

        if (fileImportHandlerVo.getFailureNum()>0){
            vo.setStringBuffer(vo.getStringBuffer().append(fileImportHandlerVo.getMsg()+"\n"));
        }
        if (StringUtils.isNotEmpty(fileImportHandlerVo.getList())){
            list.addAll(fileImportHandlerVo.getList());
        }
        log.info("{}信息第{}-{}数据导入成功",importName,vo.getTotalNum()+1,vo.getTotalNum()+thisNum);
        return true;
    }

    public SysFileImport getFileImport(Long fileImportId){
        CommonResult<SysFileImport> commonResult = fileImportApi.getFileImportById(fileImportId);
        if (commonResult.isError()|| ObjectUtil.isNull(commonResult.getData())){
            throw new RuntimeException("导入记录数据查询失败");
        }
        SysFileImport data = commonResult.getData();
        return data;
    }

    public void renewalFileImport(SysFileImport data,List<SysFileImportDtl> list,FileImportHandlerVo vo,String importName){
        data.setSuccessNum(vo.getSuccessNum());
        data.setFailNum(vo.getFailureNum());
        data.setTotalNum(vo.getTotalNum());
        data.setRemark(vo.getStringBuffer().toString());
        if (vo.getFailureNum()>0){
            data.setImportStatus(StatusConstants.STATUS_FAIL);
        }else {
            data.setImportStatus(StatusConstants.STATUS_SUCCESS);
        }
        log.info("{}导入结果:{}",importName,JsonUtils.toJsonString(data));
        fileImportApi.updateFileImport(data);
        if (StringUtils.isNotEmpty(list)){
            fileImportApi.batchAddFileImportDtl(list);
        }
    }


}
