package com.zksr.file.job.handler.impl;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.system.api.model.LoginUser;
import com.zksr.trade.api.orderSettle.OrderSettleApi;
import com.zksr.trade.api.orderSettle.dto.OrderCommissionStatementResDTO;
import com.zksr.trade.api.orderSettle.vo.OrderCommissionStatementPageVo;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/5 9:20
 * @订单分佣对账单导出
 */
@ExportHandler(value = ExportType.ORDER_STATEMENT_OF_ACCOUNT_EXPORT, defaultPage = false)
public class OrderStatementOfAccountExportHandler extends ReportAbstractContentHandler<OrderCommissionStatementResDTO> {

    @Resource
    private OrderSettleApi orderSettleApi;

    @Override
    public List<OrderCommissionStatementResDTO> getList(ExportJobContext jobContext) {
        OrderCommissionStatementPageVo pageVO = jobContext.getQueryData(OrderCommissionStatementPageVo.class);
        pageVO.setPage(jobContext.page());
        pageVO.setLoginUser(SecurityUtils.getLoginUser());
        pageVO.setLoginSupplierId(SecurityUtils.getSupplierId());
        pageVO.setLoginDcId(SecurityUtils.getDcId());
        pageVO.setIsExport(1);
        return orderSettleApi.getStatementOfAccountPage(pageVO).getCheckedData();
    }
}
