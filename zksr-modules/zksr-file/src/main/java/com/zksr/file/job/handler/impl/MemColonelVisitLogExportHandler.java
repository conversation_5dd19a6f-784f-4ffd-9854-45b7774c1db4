package com.zksr.file.job.handler.impl;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.dto.MemColonelVisitLogRespDTO;
import com.zksr.member.api.colonel.vo.MemColonelVisitLogVO;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/17 16:59
 * @业务员拜访明细导出
 */
@ExportHandler(value = ExportType.MEM_COLONEL_VISIT_LOG_EXPORT, defaultPage = false)
public class MemColonelVisitLogExportHandler extends ReportAbstractContentHandler<MemColonelVisitLogRespDTO> {

    @Resource
    private Colonel<PERSON>pi Colonel<PERSON>pi;

    @Override
    public List<MemColonelVisitLogRespDTO> getList(ExportJobContext jobContext) {
        MemColonelVisitLogVO pageVO = jobContext.getQueryData(MemColonelVisitLogVO.class);
        pageVO.setPage(jobContext.page());
        pageVO.setSysCode(SecurityUtils.getLoginUser().getSysCode());
        return ColonelApi.getColonelVisitLog(pageVO).getCheckedData();
    }
}
