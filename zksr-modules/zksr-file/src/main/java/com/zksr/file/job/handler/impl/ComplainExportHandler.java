package com.zksr.file.job.handler.impl;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.enums.ComplainTypeEnum;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.member.api.complain.ComplainApi;
import com.zksr.member.api.complain.MemComplainVO;
import com.zksr.member.api.complain.excel.MemComplainExcel;
import com.zksr.product.api.skuPrice.SkuPriceApi;
import com.zksr.product.api.skuPrice.dto.PrdtSkPageReqApiVO;
import com.zksr.product.api.skuPrice.dto.PrdtSkuPriceInfoExportVO;
import com.zksr.system.api.domain.SysDictData;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 投诉管理导出
 */
@ExportHandler(value = ExportType.COMPLAIN_EXPORT, defaultPage = false)
public class ComplainExportHandler extends ReportAbstractContentHandler<MemComplainExcel> {

    @Resource
    private ComplainApi complainApi;
    @Override
    public List<MemComplainExcel> getList(ExportJobContext jobContext) {

        MemComplainVO pageVO = jobContext.getQueryData(MemComplainVO.class);
        pageVO.setPage(jobContext.page());
        List<MemComplainExcel> list = new ArrayList<>();

        int i = 1;
        for (;;) {
            pageVO.setPageSize(50);
            pageVO.setPageNo(i);

            List<MemComplainExcel> list1 = complainApi.getComplainExcel(pageVO).getCheckedData();

            list1.forEach((item)->{
                item.setComplainType(ComplainTypeEnum.matchingName(item.getComplainType()));
                if ("0".equals(item.getStatus())){
                    item.setStatus("待处理");
                } else if ("1".equals(item.getStatus())) {
                    item.setStatus("已回复");
                }
            });

            if (ToolUtil.isEmpty(list1) || list1.isEmpty()) {
                break;
            }
            list.addAll(list1);
            i++;
        }
        return list;
    }
}
