package com.zksr.file.importJob.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.file.api.constant.ImportTypeEnum;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.file.importJob.handler.IImportContentHandler;
import com.zksr.member.api.branch.excel.MemBranchImportExcel;
import com.zksr.member.api.branch.form.BranchImportForm;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.areaClass.excel.ProductAreaClassImportExcel;
import com.zksr.product.api.areaClass.form.AreaClassImportForm;
import com.zksr.product.api.catgory.CatgoryApi;
import com.zksr.product.api.catgory.excel.CategoryImportExcel;
import com.zksr.product.api.catgory.form.CategoryImportForm;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.FileImportApi;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class AreaClassImportHandler extends ImportContentHandlerEvent<ProductAreaClassImportExcel> implements IImportContentHandler<ProductAreaClassImportExcel> {

    @Resource
    private AreaClassApi areaClassApi;


    @Override
    public String getImportType() {
        return ImportTypeEnum.AREA_CLASS_IMPORT.getCode();
    }

    public void importData(SysFileImportVO sysFileImport) {
        List<List<ProductAreaClassImportExcel>> lists = excelData(ImportTypeEnum.AREA_CLASS_IMPORT.getName(), 1, 1, ProductAreaClassImportExcel.class, sysFileImport.getFileUrl());
        SysFileImport fileImport = getFileImport(sysFileImport.getFileImportId());

        AreaClassImportForm areaClassImportForm = new AreaClassImportForm();
        areaClassImportForm.setFileImportId(sysFileImport.getFileImportId());
        areaClassImportForm.setSysCode(sysFileImport.getSysCode());
        areaClassImportForm.setDcId(sysFileImport.getDcId());

        FileImportHandlerVo vo = new FileImportHandlerVo();
        List<SysFileImportDtl> list = new ArrayList<>();
        for (List<ProductAreaClassImportExcel> excels : lists) {
            log.info("城市展示分类信息第{}数据导入开始",vo.getTotalNum()+1);
            areaClassImportForm.setList(excels);
            areaClassImportForm.setSeq(vo.getTotalNum());
            CommonResult<String> stringCommonResult = areaClassApi.importAreaClassDataEvent(areaClassImportForm);
            importResult(stringCommonResult,list,vo,excels.size(),ImportTypeEnum.AREA_CLASS_IMPORT.getName());
        }
        renewalFileImport(fileImport,list,vo,ImportTypeEnum.AREA_CLASS_IMPORT.getName());

    }
}
