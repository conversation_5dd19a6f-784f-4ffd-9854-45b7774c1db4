package com.zksr.file.importJob.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.file.api.constant.ImportTypeEnum;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.file.importJob.handler.IImportContentHandler;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.excel.MemBranchImportExcel;
import com.zksr.member.api.branch.form.BranchImportForm;
import com.zksr.product.api.brand.excel.BrandImportExcel;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.FileImportApi;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class MemBranchImportHandler extends ImportContentHandlerEvent<MemBranchImportExcel> implements IImportContentHandler<MemBranchImportExcel>{

    @Resource
    private BranchApi branchApi;

    @Override
    public String getImportType() {
        return ImportTypeEnum.MEM_BRANCH_IMPORT.getCode();
    }

    public void importData(SysFileImportVO sysFileImport) {
        List<List<MemBranchImportExcel>> lists = excelData(ImportTypeEnum.MEM_BRANCH_IMPORT.getName(), 1, 1, MemBranchImportExcel.class, sysFileImport.getFileUrl());
        SysFileImport fileImport = getFileImport(sysFileImport.getFileImportId());

        BranchImportForm branchImportForm = new BranchImportForm();
        branchImportForm.setFileImportId(sysFileImport.getFileImportId());
        branchImportForm.setDcId(sysFileImport.getDcId());
        branchImportForm.setSysCode(sysFileImport.getSysCode());
        FileImportHandlerVo vo = new FileImportHandlerVo();
        List<SysFileImportDtl> list = new ArrayList<>();
        for (List<MemBranchImportExcel> memBranchImportExcels : lists) {
            log.info("门店信息第{}数据导入开始",vo.getTotalNum()+1);
            branchImportForm.setBranchList(memBranchImportExcels);
            branchImportForm.setSeq(vo.getTotalNum());
            CommonResult<String> stringCommonResult = branchApi.importDataEvent(branchImportForm);
            importResult(stringCommonResult,list,vo,memBranchImportExcels.size(),ImportTypeEnum.MEM_BRANCH_IMPORT.getName());
        }
        renewalFileImport(fileImport,list,vo,ImportTypeEnum.MEM_BRANCH_IMPORT.getName());

    }

}
