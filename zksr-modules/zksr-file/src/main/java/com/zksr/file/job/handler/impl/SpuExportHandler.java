package com.zksr.file.job.handler.impl;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuExportDTO;
import com.zksr.product.api.spu.vo.PrdtSpuPageReqVO;

import javax.annotation.Resource;
import java.util.List;

/**
* 商品SPU导出
* @date 2024/11/11 10:55
* <AUTHOR>
*/
@ExportHandler(value = ExportType.SPU_EXPORT, defaultPage = false)
public class SpuExportHandler extends ReportAbstractContentHandler<SpuExportDTO> {
    @Resource
    private SpuApi spuApi;
    @Override
    public List<SpuExportDTO> getList(ExportJobContext jobContext) {
        PrdtSpuPageReqVO pageVO = jobContext.getQueryData(PrdtSpuPageReqVO.class);
        pageVO.setPage(jobContext.page());
        return spuApi.getSpuExportList(pageVO).getCheckedData();
    }
}
