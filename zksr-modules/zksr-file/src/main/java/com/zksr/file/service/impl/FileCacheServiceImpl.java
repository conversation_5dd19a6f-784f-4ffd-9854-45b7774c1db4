package com.zksr.file.service.impl;

import com.alicp.jetcache.Cache;
import com.zksr.file.service.IFileCacheService;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.WxB2bPayConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/20 16:19
 */
@Service
public class FileCacheServiceImpl implements IFileCacheService {

    @Autowired
    private Cache<Long, WxB2bPayConfigDTO> wxB2bPayConfigDTOCache;

    @Resource
    private PartnerConfigApi partnerConfigApi;

    @PostConstruct
    public void init() {
        wxB2bPayConfigDTOCache.config().setLoader(this::loadWxB2bPayConfigCache);
    }

    private WxB2bPayConfigDTO loadWxB2bPayConfigCache(Long sysCode) {
        return partnerConfigApi.getWxB2bPayConfig(sysCode).getCheckedData();
    }

    @Override
    public WxB2bPayConfigDTO getWxB2BPayConfigDTO(Long sysCode) {
        return wxB2bPayConfigDTOCache.get(sysCode);
    }
}
