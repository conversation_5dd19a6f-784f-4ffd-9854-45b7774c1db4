package com.zksr.file.service;

import com.zksr.file.api.file.vo.ImageInfoReqVO;
import com.zksr.system.api.domain.JSONFile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 文件上传接口
 *
 * <AUTHOR>
 */
public interface ISysFileService
{

    public String platform();

    /**
     * 文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    public String uploadFile(MultipartFile file) throws Exception;

    /**
     * json上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    public String uploadJsonFile(JSONFile file) throws Exception;

    /**
    * @Description: 上传合利宝公钥文件解析内容返回
    * @Author: liuxingyu
    * @Date: 2024/3/23 16:45
    */
    String  uploadHeLiBaoPublicKey(MultipartFile file) throws IOException;

    /**
     * @Description: 上传合利宝文件私钥解析内容返回
     * @Author: liuxingyu
     * @Date: 2024/3/23 16:45
     */
    String uploadHeLiBaoPrivateKey(MultipartFile file) throws IOException;

    /**
     * 下载选中的图片 打包成zip
     * @param images
     * @return
     */
    byte[] downloadImages(List<ImageInfoReqVO> images) throws IOException;

    /**
     * 下载base64图片
     * @param imgUrl
     * @return
     */
    String downloadBase64(String imgUrl);
}
