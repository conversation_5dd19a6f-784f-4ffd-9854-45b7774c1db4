package com.zksr.file.job.handler.impl;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponExportRespDTO;
import com.zksr.promotion.api.coupon.vo.CouponExportVO;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/17 16:59
 * @优惠券领取记录导出
 */
@ExportHandler(value = ExportType.PRM_COUPON_RECEIVE_EXPORT, defaultPage = false)
public class PrmCouponExportHandler extends ReportAbstractContentHandler<CouponExportRespDTO> {

    @Resource
    private CouponApi couponApi;

    @Override
    public List<CouponExportRespDTO> getList(ExportJobContext jobContext) {
        CouponExportVO pageVO = jobContext.getQueryData(CouponExportVO.class);
        pageVO.setPage(jobContext.page());
        pageVO.setSysCode(SecurityUtils.getLoginUser().getSysCode());
        pageVO.setDcId(SecurityUtils.getDcId());
        return couponApi.getPrmCouponPage(pageVO).getCheckedData();
    }
}
