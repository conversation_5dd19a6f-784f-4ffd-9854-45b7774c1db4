package com.zksr.file.job.handler.impl;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.trade.api.order.vo.SupplierOrderDtlInfoExportVO;
import com.zksr.trade.api.orderSettle.dto.BranchStatementResDTO;
import com.zksr.trade.api.orderSettle.vo.BranchStatementExportVo;
import com.zksr.trade.api.orderSettle.vo.BranchStatementPageVo;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/17
 * @导出门店信息
 */
@ExportHandler(value = ExportType.OWN_BRANCH_STATEMENT_EXPORT, defaultPage = false)
public class OwnBranchStatementExportHandler extends ReportAbstractContentHandler<BranchStatementExportVo> {

    @Resource
    private BranchApi branchApi;  // 假设有一个与门店（Branch）相关的 API

    @Override
    public List<BranchStatementExportVo> getList(ExportJobContext jobContext) {
        // 获取查询参数（这里假设你有类似的Page对象）
        BranchStatementPageVo pageVO = jobContext.getQueryData(BranchStatementPageVo.class);
        // 设置分页信息
        pageVO.setPage(jobContext.page());
        pageVO.setLoginDcId(SecurityUtils.getDcId());
        // 设置只导出标志
        pageVO.setPage(jobContext.page());
        List<BranchStatementExportVo> list = new ArrayList<>();
        int i = 1;
        for (;;) {
            // 这里每次分页查询50条，因返回数据查询速度过慢，超出50条会导致接口超时，后续查询优化可将分页条数进行调整
            pageVO.setPageSize(30);
            pageVO.setPageNo((i));
            List<BranchStatementExportVo> list1 = branchApi.getBranchStatementPage(pageVO).getCheckedData();
            if (ToolUtil.isEmpty(list1) || list1.isEmpty()) {
                break;
            }
            list.addAll(list1);
            i++;

        }
//        // 调用门店（Branch）相关 API 获取数据
        return list;
    }
}