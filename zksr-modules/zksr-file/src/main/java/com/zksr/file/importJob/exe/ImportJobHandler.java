package com.zksr.file.importJob.exe;

import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.file.importJob.handler.IImportContentHandler;
import com.zksr.system.api.domain.SysFileImport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@Component
public class ImportJobHandler {

    @Autowired
    private List<IImportContentHandler> importContentHandlers;


    public void importJobExecute(SysFileImportVO sysFileImport){
        for (IImportContentHandler importContentHandler : importContentHandlers) {
            if (importContentHandler.getImportType().equals(sysFileImport.getImportType())){
                importContentHandler.importData(sysFileImport);
            }
        }
    }

}
