package com.zksr.file.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 华为云OBS
 * @date 2024/2/29 15:06
 */

@Configuration
@ConfigurationProperties(prefix = "cos")
@Data
public class CosConfig {
    private String endpoint;
    private String accessKeyId;
    private String accessKeySelect;
    private String bucketName;
    private String objectName;
    private String cdnUrl;
}
