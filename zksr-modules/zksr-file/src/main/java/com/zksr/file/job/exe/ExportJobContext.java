
package com.zksr.file.job.exe;

import cn.hutool.poi.excel.BigExcelWriter;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.github.pagehelper.Page;
import com.zksr.system.api.export.vo.SysExportJob;
import com.zksr.system.api.model.LoginUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 导出任务上下文
 * @date 2024/1/15 16:05
 */
@Data
@Slf4j
@NoArgsConstructor
@SuppressWarnings("all")
public class ExportJobContext {

    // 执行任务
    private SysExportJob wmsReportJobPo;

    /**
     * 自定义导出
     */
    private BigExcelWriter writer;

    /**
     * 导出用户
     */
    private LoginUser loginUser;

    @ApiModelProperty(value = "页码，从 1 开始", required = true, example = "1")
    private Integer pageNo;

    @ApiModelProperty(value = "每页条数，最大值为 100, -1 为查询全部数据", required = true, example = "10")
    private Integer pageSize;

    public ExportJobContext(SysExportJob wmsReportJobPo, BigExcelWriter writer) {
        this.wmsReportJobPo = wmsReportJobPo;
        this.writer = writer;
    }

    public <T> ExportJobContext(SysExportJob wmsReportJobPo, Page<T> localPage) {
        this.wmsReportJobPo = wmsReportJobPo;
        this.pageNo = localPage.getPageNum();
        this.pageSize = localPage.getPageSize();
    }

    public <T> ExportJobContext(SysExportJob wmsReportJobPo) {
        this.wmsReportJobPo = wmsReportJobPo;
        this.loginUser = JSON.parseObject(wmsReportJobPo.getUserInfo(), LoginUser.class);
    }

    public <T> ExportJobContext(SysExportJob wmsReportJobPo, BigExcelWriter writer, Page<T> localPage) {
        this.wmsReportJobPo = wmsReportJobPo;
        this.writer = writer;
        this.pageNo = localPage.getPageNum();
        this.pageSize = localPage.getPageSize();
    }

    public <T> T getQueryData(Class<T> tClass) {
        return wmsReportJobPo.getQueryData(tClass);
    }

    public SysExportJob getWmsReportJobPo() {
        return wmsReportJobPo;
    }

    /**
     * 仅是返回一个page, 不具备分页功能
     * @return
     */
    public Page<?> page() {
        return new Page<>(this.pageNo, pageSize);
    }

    /**
     * 仅是返回一个page, 不具备分页功能
     * @return
     */
    public void setPage(Page<?> localPage) {
        this.pageNo = localPage.getPageNum();
        this.pageSize = localPage.getPageSize();
    }
}
