package com.zksr.file.job.handler.impl;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.after.vo.AfterOrderExportVO;
import com.zksr.trade.api.after.vo.AfterOrderPageReqVO;
import com.zksr.trade.api.orderSettle.vo.BranchStatementExportVo;
import com.zksr.trade.api.orderSettle.vo.BranchStatementPageVo;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @ 售后管理导出
 */
@ExportHandler(value = ExportType.AFTER_ORDER_EXPORT, defaultPage = false)
public class AfterExportHandler  extends ReportAbstractContentHandler<AfterOrderExportVO> {

    @Resource
    private AfterApi afterApi;

    @Override
    public List<AfterOrderExportVO> getList(ExportJobContext jobContext) {
        // 获取查询参数（这里假设你有类似的Page对象）
        AfterOrderPageReqVO pageVO = jobContext.getQueryData(AfterOrderPageReqVO.class);
        // 设置分页信息
        pageVO.setPage(jobContext.page());
        // 设置只导出标志
        List<AfterOrderExportVO> list = new ArrayList<>();
        int i = 1;
        for (;;) {
            // 这里每次分页查询50条，因返回数据查询速度过慢，超出50条会导致接口超时，后续查询优化可将分页条数进行调整
            pageVO.setPageSize(30);
            pageVO.setPageNo((i));
            List<AfterOrderExportVO> list1 = afterApi.getAfterExportPage(pageVO).getCheckedData();
            if (ToolUtil.isEmpty(list1) || list1.isEmpty()) {
                break;
            }
            list.addAll(list1);
            i++;
        }
//        // 调用门店（Branch）相关 API 获取数据
        return list;
    }

}
