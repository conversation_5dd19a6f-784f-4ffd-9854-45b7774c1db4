package com.zksr.file.job.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.DcOrderPageReqApiVO;
import com.zksr.trade.api.order.vo.DebtSupplierOrderDtlInfoExportVO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 欠款订单导出
 */
@ExportHandler(value = ExportType.DEBT_ORDER_EXPORT, defaultPage = false)
public class TrdDebtOrderExportHandler extends ReportAbstractContentHandler<DebtSupplierOrderDtlInfoExportVO> {
    @Resource
    private OrderApi orderApi;
    @Override
    public List<DebtSupplierOrderDtlInfoExportVO> getList(ExportJobContext jobContext) {
        DcOrderPageReqApiVO pageVO = jobContext.getQueryData(DcOrderPageReqApiVO.class);
        pageVO.setPage(jobContext.page());
        List<DebtSupplierOrderDtlInfoExportVO> list = new ArrayList<>();

        if (ObjectUtil.isNull(pageVO.getDcId())){
            Long dcId = jobContext.getLoginUser().getSysUser().getDcId();
            pageVO.setDcId(dcId);
        }

        if (ObjectUtil.isNull(pageVO.getSupplierId())){
            Long supplierId = jobContext.getLoginUser().getSysUser().getSupplierId();
            pageVO.setSupplierId(supplierId);
        }

        int i = 1;
        for (;;) {
            pageVO.setPageSize(50);
            pageVO.setPageNo((i - 1) * pageVO.getPageSize());
            List<DebtSupplierOrderDtlInfoExportVO> list1 = orderApi.getTrdDebtOrderExportListGroupByOrder(pageVO).getCheckedData();
            if (ToolUtil.isEmpty(list1) || list1.isEmpty()) {
                break;
            }
            list.addAll(list1);
            i++;
        }
        return list;
    }
}
