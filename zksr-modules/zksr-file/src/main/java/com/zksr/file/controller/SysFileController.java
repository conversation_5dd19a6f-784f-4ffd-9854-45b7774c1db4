package com.zksr.file.controller;

import cn.hutool.core.io.FileUtil;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.utils.file.FileUtils;
import com.zksr.file.config.PlatformConfig;
import com.zksr.file.service.ISysFileService;
import com.zksr.file.service.PlatformFileService;
import com.zksr.system.api.domain.JSONFile;
import com.zksr.system.api.domain.SysFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

/**
 * 文件请求处理
 *
 * <AUTHOR>
 */
@RestController
public class SysFileController
{
    private static final Logger log = LoggerFactory.getLogger(SysFileController.class);

    @Autowired
    private List<ISysFileService> sysFileServices;

    @Autowired
    private PlatformConfig platformConfig;

    @Autowired
    private PlatformFileService platformFileService;

    /**
     * 文件上传请求
     */
    @PostMapping("upload")
    public R<SysFile> upload(MultipartFile file)
    {
        log.info("文件上传 {}", file.getName());
        try {
            // 上传并返回访问地址
            String url = platformFileService.getFileService().uploadFile(file);
            SysFile sysFile = new SysFile();
            sysFile.setName(FileUtils.getName(url));
            sysFile.setUrl(url);
            return R.ok(sysFile);
        }
        catch (Exception e)
        {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 大JSON上传请求
     */
    @PostMapping("uploadJson")
    public R<SysFile> uploadJson(@RequestBody JSONFile jsonFile)
    {
        try {
            // 上传并返回访问地址
            String url = platformFileService.getFileService().uploadJsonFile(jsonFile);
            SysFile sysFile = new SysFile();
            sysFile.setName(FileUtils.getName(url));
            sysFile.setUrl(url);
            return R.ok(sysFile);
        }
        catch (Exception e)
        {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
    * @Description: 上传合利宝公钥文件解析内容返回
    * @Param: MultipartFile file
    * @return: R<String>
    * @Author: liuxingyu
    * @Date: 2024/3/23 16:44
    */
    @PostMapping("uploadHeLiBaoPublicKey")
    public R<String> uploadHeLiBaoPublicKey(MultipartFile file){
        try {
            // 上传并返回访问地址
            return R.ok(platformFileService.getFileService().uploadHeLiBaoPublicKey(file));
        }
        catch (Exception e)
        {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * @Description: 上传合利宝文件私钥解析内容返回
     * @Param: MultipartFile file
     * @return: R<String>
     * @Author: liuxingyu
     * @Date: 2024/3/23 16:44
     */
    @PostMapping("uploadHeLiBaoPrivateKey")
    public R<String> uploadHeLiBaoPrivateKey(MultipartFile file){
        try {
            // 上传并返回访问地址
            return R.ok(platformFileService.getFileService().uploadHeLiBaoPrivateKey(file));
        }
        catch (Exception e)
        {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 下载static文件,
     * @param fileName  文件名称
     * @throws UnsupportedEncodingException
     */
    @RequestMapping("/staticFile/{fileName}")
    public void getStaticFile(@PathVariable String fileName, HttpServletResponse response) throws UnsupportedEncodingException {
        //注意getResource("")里面是空字符串
        String path = this.getClass().getClassLoader().getResource("static/" + fileName).getPath();
        //如果路径中带有中文会被URLEncoder,因此这里需要解码
        String filePath = URLDecoder.decode(path, "UTF-8");
        try (OutputStream outputStream = response.getOutputStream()) {
            // 将文件数据通过输出流写入响应
            outputStream.write(FileUtil.readBytes(filePath));
            outputStream.flush();
        } catch (IOException e) {
            log.error("静态文件下载失败", e);
            // 处理异常
        }
    }
}
