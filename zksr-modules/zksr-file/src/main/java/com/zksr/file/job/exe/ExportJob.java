package com.zksr.file.job.exe;

import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageHelper;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.BigExcelUtil;
import com.zksr.common.core.utils.poi.ExportSupplier;
import com.zksr.common.redis.service.RedisService;
import com.zksr.file.api.constant.ReportState;
import com.zksr.file.job.handler.IReportContentHandler;
import com.zksr.file.service.PlatformFileService;
import com.zksr.system.api.RemoteExportService;
import com.zksr.system.api.export.vo.SysExportJob;
import com.zksr.system.api.exportJob.dto.ExportUserColumnDTO;
import com.zksr.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.security.auth.message.AuthException;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 具体导出任务
 * @date 2024/1/15 16:05
 */
@Slf4j
@SuppressWarnings("all")
public class ExportJob implements Runnable{

    // 执行任务
    private SysExportJob wmsReportJobPo;

    // 具体数据提供
    private IReportContentHandler<?> reportContentHandler;

    public ExportJob(SysExportJob wmsReportJobPo, IReportContentHandler<?> reportContentHandler) {
        this.wmsReportJobPo = wmsReportJobPo;
        this.reportContentHandler = reportContentHandler;
    }

    @Autowired
    private RedisService redisService;

    @Override
    public void run() {
        try {
            if (!recheckState()) {
                return;
            }
            // 载入线程用户信息
            initUser();
            // 执行任务
            String path = execute();
            // 完成任务
            success(new File(path));
        } catch (Exception e) {
            log.error("任务执行异常", e);
            // 处理异常过长
            wmsReportJobPo.setRemark(e.getMessage());
            wmsReportJobPo.setState(ReportState.FAIL.getState());
            wmsReportJobPo.setFinishTime(new Date());
            updateJob();
        } finally {
            SecurityContextHolder.remove();
            ExportJobExecutor.jobRun.remove(wmsReportJobPo.getJobKey());
        }
    }

    private void success(File file) {
        try {
            // 上传文件
            wmsReportJobPo.setFile(SpringUtils.getBean(PlatformFileService.class).getFileService().uploadFile(getMultipartFile(file)));
        } catch (Exception e) {
            log.error("文件上传失败",e);
            throw new ServiceException("文件上传失败");
        }
        wmsReportJobPo.setFinishTime(new Date());
        wmsReportJobPo.setState(ReportState.SUCCESS.getState());
        updateJob();
    }

    private String execute() throws AuthException {

        // 创建上下文
        ExportJobContext jobContext = new ExportJobContext(wmsReportJobPo);

        // 验证权限
        if (!reportContentHandler.permissions()) {
            throw new AuthException("无导出权限");
        }

        BigExcelUtil<?> util = new BigExcelUtil<>(reportContentHandler.getClassType());
        String path = "/tmp/" + wmsReportJobPo.getName() + "_" + wmsReportJobPo.getJobKey() + ".xlsx";

        // 导出数据
        ExportSupplier exportSupplier = new ExportSupplier<List>() {
            @Override
            public List<?> get(BigExcelWriter writer) {
                // 设置导出writer
                jobContext.setWriter(writer);
                jobContext.setPage(PageHelper.getLocalPage());

                // 如果不需要分页
                if (!reportContentHandler.defaultPage()) {
                    // 自定义分页, 不需要定时任务控制
                    PageHelper.clearPage();
                }

                // 获取列表
                List list = reportContentHandler.getList(jobContext);
                return list;
            }
        };

        // 导出列配置
        if (StringUtils.isNotEmpty(wmsReportJobPo.getColConfig())) {
            // 自定义导出列
            BigExcelWriter writer = ExcelUtil.getBigWriter(path);
            writer.setOnlyAlias(true);
            // 自定义导出列
            List<ExportUserColumnDTO> columnDTOS = JSON.parseArray(wmsReportJobPo.getColConfig(), ExportUserColumnDTO.class);
            // 处理排序号为空的
            columnDTOS.stream().filter(item -> Objects.isNull(item.getSort())).forEach(item -> item.setSort(Integer.MAX_VALUE));
            // 字段排序
            columnDTOS.sort(Comparator.comparing(ExportUserColumnDTO::getSort));
            for (ExportUserColumnDTO columnDTO : columnDTOS) {
                writer.addHeaderAlias(columnDTO.getFiledKey(), columnDTO.getFiledName());
            }
            util.exportExcelFunLimit(writer, path, this.wmsReportJobPo.getName(), exportSupplier);
        } else {
            // 常规注解导出
            util.exportExcelFunLimit(path, this.wmsReportJobPo.getName(), exportSupplier);
        }
        return path;
    }

    private void initUser() {
        LoginUser loginUser = JSON.parseObject(wmsReportJobPo.getUserInfo(), LoginUser.class);
        if (Objects.nonNull(loginUser)) {
            SecurityContextHolder.set(SecurityConstants.DETAILS_USER_ID, loginUser.getUserid());
            SecurityContextHolder.set(SecurityConstants.DETAILS_USERNAME, loginUser.getUsername());
            SecurityContextHolder.set(SecurityConstants.USER_KEY, wmsReportJobPo.getUserKey());
            SecurityContextHolder.set(SecurityConstants.SYS_CODE, loginUser.getSysCode());
            SecurityContextHolder.set(SecurityConstants.ROLE_PERMISSION, loginUser.getPermissions());
            SecurityContextHolder.set(SecurityConstants.DC_ID, loginUser.getDcId());
            SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
            SecurityContextHolder.set(SecurityConstants.SYS_CODES, StringUtils.join(loginUser.getSysCodeList(), StringPool.COMMA));
        }
        SecurityContextHolder.set(SecurityConstants.FROM_SOURCE, SecurityConstants.EXPORT);
        // 开始执行时间标记
        wmsReportJobPo.setExecuteTime(new Date());
        wmsReportJobPo.setState(ReportState.PROCESS.getState());
        updateJob();
    }

    // 更新数据
    protected void updateJob() {
        RemoteExportService bean = SpringUtils.getBean(RemoteExportService.class);
        R<Boolean> booleanR = bean.updateJob(wmsReportJobPo, SecurityConstants.INNER);
        if (!booleanR.isSuccess()) {
            throw new ServiceException("更新导出任务失败");
        }
    };

    // 更新数据
    protected boolean recheckState() {
        return SpringUtils.getBean(RedisService.class).getRedisTemplate().opsForValue().setIfAbsent("JOB_KEY:" + this.wmsReportJobPo.getJobKey(), "1", 1000, TimeUnit.SECONDS);
    }

    public static MultipartFile getMultipartFile(File file) {
        DiskFileItem item = new DiskFileItem("file"
                , MediaType.MULTIPART_FORM_DATA_VALUE
                , true
                , file.getName()
                , (int)file.length()
                , file.getParentFile());
        try {
            OutputStream os = item.getOutputStream();
            os.write(FileUtils.readFileToByteArray(file));
        } catch (IOException e) {
            log.error(" ExportJob.getMultipartFile 异常, ", e);
        }
        return new CommonsMultipartFile(item);
    }

}
