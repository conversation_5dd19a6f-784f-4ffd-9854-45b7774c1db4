package com.zksr.file.job.handler.impl;

import com.zksr.account.api.transfer.TransferApi;
import com.zksr.account.api.transfer.dto.AccTransferBillRespDTO;
import com.zksr.account.api.transfer.vo.AccTransferBillPageVO;
import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/1/22 10:00
 * @注释
 */
@ExportHandler(value = ExportType.ACC_TRANSFER_BILL_EXPORT, defaultPage = false)
public class AccTransferBillExportHandler extends ReportAbstractContentHandler<AccTransferBillRespDTO> {

    @Resource
    private TransferApi transferApi;

    @Override
    public List<AccTransferBillRespDTO> getList(ExportJobContext jobContext) {
        AccTransferBillPageVO pageVO = jobContext.getQueryData(AccTransferBillPageVO.class);
        pageVO.setPage(jobContext.page());
        pageVO.setSysCode(SecurityUtils.getLoginUser().getSysCode());
        return transferApi.getTransferBill(pageVO).getCheckedData();
    }
}
