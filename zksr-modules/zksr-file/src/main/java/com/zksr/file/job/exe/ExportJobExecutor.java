package com.zksr.file.job.exe;

import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.service.RedisService;
import com.zksr.file.job.handler.IReportContentHandler;
import com.zksr.system.api.RemoteExportService;
import com.zksr.system.api.export.vo.SysExportJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 导出执行中心
 * @date 2024/1/15 10:18
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class ExportJobExecutor {

    // system调用服务
    @Autowired
    private RemoteExportService remoteExportService;

    // 防止线程重入
    public static Map<String, Object> jobRun = new ConcurrentHashMap<String, Object>();

    // 执行线程池
    private final static ExecutorService threadPool = new ThreadPoolExecutor(2, 2,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(1),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.AbortPolicy());

    @PostConstruct
    public void init() {
        // 主线程 (监听)
        new Thread(() -> {
            // 休眠20秒让程序启动
            initSleep(20);
            for (; ; ) {
                // 执行线程
                // 循环执行
                try {
                    R<List<SysExportJob>> rWaitList = remoteExportService.waitJobList(SecurityConstants.INNER);
                    if (rWaitList.isSuccess()) {
                        rWaitList.getData().forEach(this::processing);
                    }
                } catch (Exception e) {
                    log.error("从system查询获取导出任务失败", e);
                }
                initSleep(10);
            }
        }).start();
    }

    // 执行处理任务
    public void processing(SysExportJob wmsReportJobPo) {
        try {
            IReportContentHandler handler = getHandler(wmsReportJobPo.getExportType());
            if (Objects.isNull(handler)) {
                return;
            }
            // 前置验证. 如果已经在队列了, 就跳过, 不要再进入队列了
            if (recheckState(wmsReportJobPo)) {
                return;
            }
            // 标记已进入队列
            ExportJobExecutor.jobRun.put(wmsReportJobPo.getJobKey(), wmsReportJobPo);
            // 加入执行队列
            threadPool.execute(new ExportJob(wmsReportJobPo, handler));
        } catch (Exception e) {
            log.error(StringUtils.format("当前线程池存在执行任务, 拒绝任务等待下次执行, {}", wmsReportJobPo.getJobKey()), e);

        }
    }

    // 获取实现导出具体类
    private IReportContentHandler getHandler(String exportType) {
        return SpringUtils.getBean(exportType);
    }

    private void initSleep(long sleepTime) {
        try {
            // 休眠20秒让程序启动
            Thread.sleep(sleepTime * 1000L);
        } catch (InterruptedException e) {
            log.error(" ExportJobExecutor.initSleep 异常, ", e);
        }
    }

    // 更新数据
    protected boolean recheckState(SysExportJob wmsReportJobPo) {
        return ExportJobExecutor.jobRun.containsKey(wmsReportJobPo.getJobKey());
    }
}
