package com.zksr.file.importJob.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.file.api.constant.ImportTypeEnum;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.file.importJob.handler.IImportContentHandler;
import com.zksr.member.api.branch.excel.MemBranchImportExcel;
import com.zksr.member.api.branch.form.BranchImportForm;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.excel.MemColonelImportExcel;
import com.zksr.member.api.colonel.form.MemColonelImportForm;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.areaClass.excel.ProductAreaClassImportExcel;
import com.zksr.product.api.areaClass.form.AreaClassImportForm;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.FileImportApi;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class MemColonelImportHandler extends ImportContentHandlerEvent<MemColonelImportExcel> implements IImportContentHandler<MemColonelImportExcel> {

    @Resource
    private ColonelApi colonelApi;

    @Override
    public String getImportType() {
        return ImportTypeEnum.MEM_COLONEL_IMPORT.getCode();
    }

    public void importData(SysFileImportVO sysFileImport) {
        List<List<MemColonelImportExcel>> lists = excelData(ImportTypeEnum.MEM_COLONEL_IMPORT.getName(), 1, 1, MemColonelImportExcel.class, sysFileImport.getFileUrl());
        SysFileImport fileImport = getFileImport(sysFileImport.getFileImportId());

        MemColonelImportForm memColonelImportForm = new MemColonelImportForm();
        memColonelImportForm.setFileImportId(sysFileImport.getFileImportId());
        memColonelImportForm.setSysCode(sysFileImport.getSysCode());
        memColonelImportForm.setDcId(sysFileImport.getDcId());
        memColonelImportForm.setFuncScop(sysFileImport.getFuncScop());

        FileImportHandlerVo vo = new FileImportHandlerVo();
        List<SysFileImportDtl> list = new ArrayList<>();
        for (List<MemColonelImportExcel> excels : lists) {
            log.info("业务员信息第{}数据导入开始",vo.getTotalNum()+1);
            memColonelImportForm.setList(excels);
            memColonelImportForm.setSeq(vo.getTotalNum());
            CommonResult<String> stringCommonResult = colonelApi.importDataEvent(memColonelImportForm);
            importResult(stringCommonResult,list,vo,excels.size(),ImportTypeEnum.MEM_COLONEL_IMPORT.getName());
        }
        renewalFileImport(fileImport,list,vo,ImportTypeEnum.MEM_COLONEL_IMPORT.getName());

    }

}
