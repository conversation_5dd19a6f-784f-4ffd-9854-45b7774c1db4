package com.zksr.file.controller;

import com.alibaba.fastjson.JSON;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ip.IpUtils;
import com.zksr.file.api.file.vo.MchProfitRateReqVO;
import com.zksr.file.api.file.vo.MchProfitRateRespVO;
import com.zksr.file.service.WxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 开放消息微信
 * @date 2024/8/9 16:28
 */
@Slf4j
@Api(tags = "消息处理 - 微信消息")
@RestController
@RequestMapping("/openWxMsg")
@ApiIgnore
public class SysOpenWxMsgController {

    private static String token = "zksr9876"; // 定义Token 务必与服务器保持一致

    //private static String key = "zBVuCma2hBQh50m90mc3IuoeiM5NQI6h";

    @Autowired
    private WxService wxService;

    /**
     * 参考小程序消息推送配置
     * {url}/file/openWxMsg/receive/zBVuCma2hBQh50m90mc3IuoeiM5NQI6h/{sysSource}
     *
     * // 支付通知
     * https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/business-capabilities/ministore/wxafunds/callback/create_order.html
     */
    @ApiOperation(value = "接受微信消息推送")
    @RequestMapping("/receive/{key}/{sysSource}")
    public String receive(
            @PathVariable String key,
            @PathVariable String sysSource,
            @RequestParam(required = false) Map<String, String> params,
            @RequestBody(required = false) String body) {
        if (!wxService.getOpenToken().equals(key)) {
            log.info("微信消息推送接收失败,key={}, receive,params={}, body={}", key,JSON.toJSONString(params), body);
            return "fail";
        }
        log.info("微信服务器IP: {}", IpUtils.getIpAddr());

        // 支付消息例子
        // params={"signature":"e8c1c023b63f46cdeadf930c9630b6e4d1eca271","timestamp":"1723620477","nonce":"1734470737","openid":"o1Ska45hzwHWbRWI8fBNjMeTp_3E","encrypt_type":"aes","msg_signature":"daf93be867c87c53522832367ca51f2972adf874"},
        // body={"ToUserName":"gh_29bad549f1a6","FromUserName":"o1Ska45hzwHWbRWI8fBNjMeTp_3E","CreateTime":1723620477,"MsgType":"event","Event":"retail_pay_notify","appid":"wx1f8f0a0b87603148","mchid":"1682898180","out_trade_no":"XS240814800000273615","order_id":"o20240814152648597822066","pay_status":"ORDER_PAY_SUCC","pay_time":"2024-08-14 15:27:56","attach":"1","payer_openid":"o1Ska45hzwHWbRWI8fBNjMeTp_3E","amount":{"order_amount":4,"payer_amount":4,"currency":"CNY"},"wxpay_transaction_id":"4200002360202408147614523011","env":0,"Encrypt":"ATkd7P51hGRMMthZGVQvvE48qxaJH0eBS4Jer1fsoDYFiJeAhYeHaZQZQi5KQHcmyg+FCyi7yI1BbhJmZQlCJO2KAikJfnrhY8RKgbW3Isr2k1SAK2QhB3NErnZxAAVmWoHMW3R5rqQWdftzYFy6NGNtc2ujL4TitHCNCezRGbj0x9v3iUSvpkw62ci+diT8cuic64/rOG0Y1a++yHFK27QAmFCCI/4QlzAIPUj+Pm0I8TOXei1J5rVfv1Jmw56VPD5P+PKNfkEQfx899HUDn4RUgc5D97km3/pWNmJgOURnaDocFFR8wTYm4XPCgnLObCiR8ZIrsAvU7686FnM1itvtY5nV9ak0MpcIsTWFFNTZipF4dQZm3/h3B2JpdJz43Oaq7A4ZUTv7Uz6JHivYKFsSTlCd5BZjpaEtp0GLQfzm/1kugXkvm2enNliG4jBrhHtL5rseEvsteGqK8BdQeVWTq9ne2wUESfZzi8PK+oYbTay+Z7s38MXV6CGhn455zIr+uQisyYgIjVjAG2wybcdwYdvlcXfKZ5XvBazQ0ZyqO7rj48szGasodcp6AraYE5YscJm1C4ZD9niS8zI4FafpMJcLjnLMsvphcNHOTIcgxdUNFzVxiqF79mvixfjxTLPAXCymz4qgYm0krHF8xZmtV7OClEYxS2PvdmY1mMUDqhoOXHLpYEbyUmFi8RK05BRPcxZIT0Z/yWUUf0SihHL8KxTXvTfha1SCGYUtvRUCCZh0exKH9IPZTLs3mTp0"}
        log.info("微信消息推送, receive,params={}, body={}", JSON.toJSONString(params), body);
        wxService.processWxReceiveMsg(params, body);
        return params.get("echostr");
    }

    /**
     * 参考小程序消息推送配置
     */
    @ApiOperation(value = "接受第三方服务商微信认证消息推送")
    @RequestMapping("/receiveMerchantAuth/{key}/{appid}")
    public String receiveMerchantAuth(
            @PathVariable String key,
            @PathVariable String appid,
            @RequestParam(required = false) Map<String, String> params,
            @RequestBody(required = false) String body) {
        if (!wxService.getOpenToken().equals(key)) {
            return "fail";
        }
        log.info("服务商认证消息推送, receiveMerchant,params={}, body={}", JSON.toJSONString(params), body);
        wxService.receiveMerchantAuth(body);
        return "success";
    }

    /**
     * 参考小程序消息推送配置
     */
    @ApiOperation(value = "接受第三方服务商微信消息推送")
    @RequestMapping("/receiveMerchant/{key}/{appid}")
    public String receiveMerchant(
            @PathVariable String key,
            @PathVariable String appid,
            @RequestParam(required = false) Map<String, String> params,
            @RequestBody(required = false) String body) {
        if (!wxService.getOpenToken().equals(key)) {
            return "fail";
        }
        log.info("服务商消息推送, receiveMerchant,params={}, body={}", JSON.toJSONString(params), body);
        return "success";
    }


    /**
     * 获取第三方平台, 微信服务商ComponentAccessToken
     */
    @ApiOperation(value = "获取第三方平台, 微信服务商ComponentAccessToken", httpMethod = HttpMethod.GET)
    @GetMapping("/getComponentAccessToken/{key}")
    public String getComponentAccessToken(@PathVariable String key) {
        if (!wxService.getOpenToken().equals(key)) {
            return "fail";
        }
        return wxService.getComponentAccessToken();
    }


    /**
     * 获取第三方平台, 微信服务商preAuthCode
     */
    @ApiOperation(value = "获取第三方平台, 微信服务商preAuthCode", httpMethod = HttpMethod.GET)
    @GetMapping("/getPreAuthCode/{key}")
    public String getPreAuthCode(@PathVariable String key) {
        if (!wxService.getOpenToken().equals(key)) {
            return "fail";
        }
        return wxService.getPreAuthCode();
    }

    /**
     * 获取第三方平台, 小程序授权authCode
     */
    @ApiOperation(value = "获取第三方平台, 微信服务商代理小程序authorizerAccessToken", httpMethod = HttpMethod.GET)
    @GetMapping("/updateAuthCode/{key}/{appid}")
    public String updateAuthCode(
            @PathVariable String key,
            @PathVariable String appid,
            @ApiParam(name = "auth_code", value = "授权code")
            @RequestParam("auth_code") String authCode
    ) {
        if (!wxService.getOpenToken().equals(key)) {
            return "fail";
        }
        log.info("updateAuthCode, appid={}, auth_code={}", appid, authCode);
        // 第三方平台操作小程序之前都需要获得小程序认证授权
        wxService.updateAuthCode(appid, authCode);
        return "<!DOCTYPE html><html lang=\"zh-CN\"><head><meta charset=\"UTF-8\"><meta name=\"viewport\"content=\"width=device-width, initial-scale=1.0\"><title>授权成功</title><style>body,html{height:100%;margin:0;display:flex;align-items:center;justify-content:center;font-family:Arial,sans-serif;background-color:#f0f0f0}.message{background-color:#ffffff;padding:20px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.1);text-align:center;max-width:80%}h1{color:#333333;font-size:2em}@media(max-width:600px){h1{font-size:1.5em}}</style></head><body><div class=\"message\"><h1>授权成功</h1></div></body></html>";
    }

    /**
     * 获取第三方平台, 微信服务商代理小程序authorizerAccessToken
     */
    @ApiOperation(value = "获取第三方平台, 微信服务商代理小程序authorizerAccessToken", httpMethod = HttpMethod.GET)
    @GetMapping("/getAuthorizerAccessToken/{key}")
    public String getAuthorizerAccessToken(@PathVariable String key, @ApiParam(name = "appId", value = "授权的小程序appid") String appid) {
        if (!wxService.getOpenToken().equals(key)) {
            return "fail";
        }
        return wxService.getAuthorizerAccessToken(appid);
    }

    /**
     * 获微信服务商代报名技术服务费比例
     */
    @ApiOperation(value = "微信服务商代报名技术服务费比例", httpMethod = HttpMethod.POST)
    @PostMapping("/setMchProfitRate/{key}")
    public MchProfitRateRespVO setMchProfitRate(@PathVariable String key, @Valid @RequestBody MchProfitRateReqVO mchProfitRateReqVO) {
        if (!wxService.getOpenToken().equals(key)) {
            return new MchProfitRateRespVO();
        }
        return wxService.setMchProfitRate(mchProfitRateReqVO);
    }

    /**
     * 获取中心服务器配置小程序accessToken, 解决处理多个服务器使用同一个小程序问题
     */
    @ApiOperation(value = "获取中心服务器配置小程序accessToken", httpMethod = HttpMethod.GET)
    @GetMapping("/getAccessToken/{key}")
    public String getAccessToken(@PathVariable String key, @ApiParam(name = "appId", value = "授权的小程序appid") String appid) {
        if (!wxService.getOpenToken().equals(key)) {
            return StringPool.EMPTY;
        }
        return wxService.getAccessToken(appid);
    }

    @ApiOperation(value = "获取小程序认证链接", httpMethod = HttpMethod.GET)
    @GetMapping("/getAppWxB2bAuthUrl/{key}/{appid}")
    public String getAppWxB2bAuthUrl(@PathVariable String key, @PathVariable String appid) {
        if (!wxService.getOpenToken().equals(key)) {
            return StringPool.EMPTY;
        }
        return wxService.getAppWxB2bAuthUrl(key, appid);
    }
}
