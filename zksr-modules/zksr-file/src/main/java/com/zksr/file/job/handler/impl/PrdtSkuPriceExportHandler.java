package com.zksr.file.job.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.product.api.skuPrice.SkuPriceApi;
import com.zksr.product.api.skuPrice.dto.PrdtSkPageReqApiVO;
import com.zksr.product.api.skuPrice.dto.PrdtSkuPriceInfoExportVO;
import com.zksr.product.api.skuPrice.dto.SkuPriceDTO;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.trade.api.order.vo.DcOrderPageReqApiVO;
import com.zksr.trade.api.order.vo.DebtSupplierOrderDtlInfoExportVO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 城市价格导出
 */
@ExportHandler(value = ExportType.SKU_PRICE_EXPORT, defaultPage = false)
public class PrdtSkuPriceExportHandler extends ReportAbstractContentHandler<PrdtSkuPriceInfoExportVO> {

    @Resource
    private SkuPriceApi skuPriceApi;
    @Override
    public List<PrdtSkuPriceInfoExportVO> getList(ExportJobContext jobContext) {

        PrdtSkPageReqApiVO pageVO = jobContext.getQueryData(PrdtSkPageReqApiVO.class);
        pageVO.setPage(jobContext.page());
        List<PrdtSkuPriceInfoExportVO> list = new ArrayList<>();

        //获取单位字典信息
        List<SysDictData> sysDictDataList = DictUtils.getDictCache("sys_prdt_unit");
        Map<String, String> sysDictDataMap = sysDictDataList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        int i = 1;
        for (;;) {
            pageVO.setPageSize(50);
            pageVO.setPageNo(i);

            List<PrdtSkuPriceInfoExportVO> list1 = skuPriceApi.getSkuPriceExportList(BeanUtils.toBean(pageVO,PrdtSkPageReqApiVO.class)).getCheckedData();

            list1.forEach((item)->{
                item.setMinUnitName(sysDictDataMap.get(String.valueOf(item.getMinUnit())));
                item.setMidUnitName(sysDictDataMap.get(String.valueOf(item.getMidUnit())));
                item.setLargeUnitName(sysDictDataMap.get(String.valueOf(item.getLargeUnit())));
            });

            if (ToolUtil.isEmpty(list1) || list1.isEmpty()) {
                break;
            }
            list.addAll(list1);
            i++;
        }
        return list;
    }
}
