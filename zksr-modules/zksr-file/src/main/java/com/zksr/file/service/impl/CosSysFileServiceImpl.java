package com.zksr.file.service.impl;

import com.obs.services.ObsClient;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.file.FileUtils;
import com.zksr.common.core.utils.uuid.Seq;
import com.zksr.common.security.config.AnntoProxyConfig;
import com.zksr.file.api.file.vo.ImageInfoReqVO;
import com.zksr.file.config.CosConfig;
import com.zksr.file.config.ObsConfig;
import com.zksr.file.service.ISysFileService;
import com.zksr.file.utils.FileUploadUtils;
import com.zksr.system.api.domain.JSONFile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileVisitOption;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 腾讯云文件上传
 * @date 2024/2/29 15:09
 */
@Service
@Primary
@Slf4j
public class CosSysFileServiceImpl implements ISysFileService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    @Autowired
    private CosConfig cosConfig;
    @Resource
    private AnntoProxyConfig anntoProxyConfig;

    @Override
    public String platform() {
        return "cos";
    }

    @Override
    public String uploadFile(MultipartFile file) throws Exception {
        String extractFilename = FileUploadUtils.extractFilename(file);
        COSClient client = null;
        try {
            COSCredentials pass = new BasicCOSCredentials(cosConfig.getAccessKeyId(), cosConfig.getAccessKeySelect());
            Region vo = new Region(cosConfig.getEndpoint());
            ClientConfig clientConfig = new ClientConfig(vo);
            //是否使用代理
            if(anntoProxyConfig.isEnable() && StringUtils.isNotEmpty(anntoProxyConfig.getHost())) {
                log.warn(" 使用代理上传文件...");
                //代理ip或域名
                clientConfig.setHttpProxyIp(anntoProxyConfig.getHost());
                //代理端口
                clientConfig.setHttpProxyPort(anntoProxyConfig.getPort());
            }

            client = new COSClient(pass, clientConfig);
            ObjectMetadata metadata = new ObjectMetadata();
            PutObjectResult result = client.putObject(cosConfig.getBucketName(), extractFilename, file.getInputStream(), metadata);
            // 设置成公共读
            // client.setObjectAcl(cosConfig.getBucketName(), extractFilename, CannedAccessControlList.PublicRead);
            // 获取url
            // URL url = client.getObjectUrl(cosConfig.getBucketName(), extractFilename);
            // 获取有效期内的秘钥
            // URL url = client.generatePresignedUrl(cosConfig.getBucketName(), extractFilename, new Date(System.currentTimeMillis() + 10 * 60 * 1000L));
            // System.out.println(url);
            if (StringUtils.isNotEmpty(cosConfig.getCdnUrl())) {
                return cosConfig.getCdnUrl() + "/" + extractFilename;
            }
            return client.getObjectUrl(cosConfig.getBucketName(), extractFilename).toString();
        } catch (CosClientException e) {
            log.error(" CosSysFileServiceImpl.uploadFile 异常, ", e);
        } finally {
            client.shutdown();
        }
        return null;
    }

    @Override
    public String uploadJsonFile(JSONFile file) throws Exception {
        String extractFilename = StringUtils.format("{}/{}_{}.{}", DateUtils.datePath(), FilenameUtils.getBaseName(file.getName()), Seq.getId(Seq.uploadSeqType), "json");
        COSClient client = null;
        try {
            COSCredentials pass = new BasicCOSCredentials(cosConfig.getAccessKeyId(), cosConfig.getAccessKeySelect());
            Region vo = new Region(cosConfig.getEndpoint());
            ClientConfig clientConfig = new ClientConfig(vo);
            //是否使用代理
            if(anntoProxyConfig.isEnable() && StringUtils.isNotEmpty(anntoProxyConfig.getHost())) {
                log.warn(" 使用代理上传文件...");
                //代理ip或域名
                clientConfig.setHttpProxyIp(anntoProxyConfig.getHost());
                //代理端口
                clientConfig.setHttpProxyPort(anntoProxyConfig.getPort());
            }

            client = new COSClient(pass, clientConfig);

            ObjectMetadata metadata = new ObjectMetadata();
            byte[] jsonBytes = file.getJson().getBytes();
            metadata.setContentLength(jsonBytes.length); // 设置 Content-Length
            client.putObject(cosConfig.getBucketName(), extractFilename, new ByteArrayInputStream(jsonBytes), metadata);
            // 设置成公共读
            client.setObjectAcl(cosConfig.getBucketName(), extractFilename, CannedAccessControlList.PublicRead);
            if (StringUtils.isNotEmpty(cosConfig.getCdnUrl())) {
                return cosConfig.getCdnUrl() + "/" + extractFilename;
            }
            return client.getObjectUrl(cosConfig.getBucketName(), extractFilename).toString();
        } catch (CosClientException e) {
            log.error(" CosSysFileServiceImpl.uploadJsonFile 异常, ", e);
        } finally {
            if (client != null) {
                client.shutdown();
            }
        }
        return null;
    }

    /**
    * @Description: 上传合利宝公钥文件解析内容返回
    * @Author: liuxingyu
    * @Date: 2024/3/23 17:03
    */
    @Override
    public String uploadHeLiBaoPublicKey(MultipartFile file) throws IOException {
        byte[] readAllBytes = file.getBytes();
        String fileContent = new String(readAllBytes);
        if ("-----BEGIN CERTIFICATE-----".indexOf(fileContent) < 0) {
            fileContent = "-----BEGIN CERTIFICATE-----\n" + fileContent +
                    "\n-----END CERTIFICATE-----";
        }
        return fileContent;
    }

    /**
    * @Description: 上传合利宝文件私钥解析内容返回
    * @Author: liuxingyu
    * @Date: 2024/3/30 15:57
    */
    @Override
    public String uploadHeLiBaoPrivateKey(MultipartFile file) throws IOException {
        return new String(Base64.encodeBase64(file.getBytes()), StandardCharsets.UTF_8);
    }

    @Override
    public byte[] downloadImages(List<ImageInfoReqVO> images) throws IOException {
        Map<LocalDate, List<ImageInfoReqVO>> imagesByDate = groupImagesByDate(images);

        // 创建临时目录
        Path tempDir = Files.createTempDirectory("image");
        try {
            // 创建ZIP文件
            ByteArrayOutputStream bao = new ByteArrayOutputStream();
            try (ZipOutputStream zipOut = new ZipOutputStream(bao)) {
                for (Map.Entry<LocalDate, List<ImageInfoReqVO>> entry : imagesByDate.entrySet()) {
                    LocalDate date = entry.getKey();
                    List<ImageInfoReqVO> imagesForDate = entry.getValue();

                    // 创建日期子目录
                    Path dateSubdir = tempDir.resolve(date.format(DATE_FORMATTER));
                    Files.createDirectories(dateSubdir);

                    // 下载图片并添加到ZIP
                    for (ImageInfoReqVO image : imagesForDate) {
                        Path imageFile = dateSubdir.resolve(image.getName());
                        downloadImage(image.getUrl(), imageFile);
                        addFileToZip(imageFile, zipOut, tempDir);
                    }
                }
            }

            return bao.toByteArray();
        } finally {
            // 删除临时目录及其内容
            Files.walk(tempDir, FileVisitOption.FOLLOW_LINKS)
                    .sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(File::delete);
        }
    }

    @Override
    public String downloadBase64(String imgUrl) {
        COSClient client = null;
        try {
            String name = FileUtils.getName(imgUrl);
            COSCredentials pass = new BasicCOSCredentials(cosConfig.getAccessKeyId(), cosConfig.getAccessKeySelect());
            Region vo = new Region(cosConfig.getEndpoint());
            client = new COSClient(pass, new ClientConfig(vo));
            GetObjectRequest request = new GetObjectRequest(cosConfig.getBucketName(), name);
            // 设置图片处理参数，对图片依次进行缩放、旋转
            COSObjectInputStream objectContent = client.getObject(request).getObjectContent();
            byte[] buffer = new byte[1024];
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int bytesRead;
            try {
                while ((bytesRead = objectContent.read(buffer, 0, 1024)) != -1) {
                    baos.write(buffer, 0, bytesRead);
                }
                baos.close();
            } catch (IOException e) {
                throw new RuntimeException("Failed to read from COSObjectInputStream", e);
            }
            byte[] bytes = baos.toByteArray();
            return cn.hutool.core.codec.Base64.encode(bytes);
        } catch (CosClientException e) {
            log.error(" CosSysFileServiceImpl.downloadBase64 异常, ", e);
        } finally {
            client.shutdown();
        }
        return null;
    }

    private Map<LocalDate, List<ImageInfoReqVO>> groupImagesByDate(List<ImageInfoReqVO> images) {
        Map<LocalDate, List<ImageInfoReqVO>> imagesByDate = new TreeMap<>();
        for (ImageInfoReqVO image : images) {
            LocalDate date = LocalDate.parse(image.getDate(), DATE_FORMATTER);
            imagesByDate.computeIfAbsent(date, k -> new ArrayList<>()).add(image);
        }
        return imagesByDate;
    }

    private void downloadImage(String imageUrl, Path targetFile) throws IOException {
        URL url = new URL(imageUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 设置请求方法为GET
        connection.setRequestMethod("GET");

        // 设置连接超时和读取超时
        connection.setConnectTimeout(10000); // 10秒
        connection.setReadTimeout(10000); // 10秒

        // 检查响应码是否成功
        int responseCode = connection.getResponseCode();
        if (responseCode != HttpURLConnection.HTTP_OK) {
            throw new IOException("Failed to download image, response code: " + responseCode);
        }

        try (InputStream inputStream = connection.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(targetFile.toFile())) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } finally {
            connection.disconnect();
        }
    }


    private void addFileToZip(Path file, ZipOutputStream zipOut, Path tempDir) throws IOException {
        // 计算文件相对于临时目录的相对路径
        Path relativePath = tempDir.relativize(file);

        // 创建 ZipEntry 使用相对路径作为名称
        ZipEntry entry = new ZipEntry(relativePath.toString());

        try (InputStream in = Files.newInputStream(file)) {
            zipOut.putNextEntry(entry);
            IOUtils.copy(in, zipOut);
            zipOut.closeEntry();
        }
    }
}
