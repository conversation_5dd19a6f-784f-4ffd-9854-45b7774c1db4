package com.zksr.file.job.handler;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.domain.BaseEntity;
import com.zksr.common.security.utils.SecurityUtils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 抽象导出调度处理器公共类
 * @date 2024/1/15 15:34
 */
public abstract class ReportAbstractContentHandler<T extends BaseEntity> implements IReportContentHandler<T>{

    @Override
    public Class<T> getClassType() {
        // 获取当前类的 Class 对象
        Class<?> clazz = this.getClass();
        // 获取泛型超类
        Type genericSuperclass = clazz.getGenericSuperclass();
        // 将泛型超类转换为 ParameterizedType
        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            // 从 ParameterizedType 中获取实际类型参数
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length > 0) {
                return (Class<T>) actualTypeArguments[0];
            }
        }
        return null;
    }

    /**
     * 是否需要默认开启分页
     * @return
     */
    @Override
    public boolean defaultPage() {
        ExportHandler exportHandler = this.getClass().getAnnotation(ExportHandler.class);
        if (Objects.nonNull(exportHandler)) {
            return exportHandler.defaultPage();
        }
        return true;
    }

    @Override
    public boolean permissions() {
        ExportHandler exportHandler = this.getClass().getAnnotation(ExportHandler.class);
        if (Objects.nonNull(exportHandler)) {
            String permissions = exportHandler.permissions();
            if (StringUtils.isEmpty(permissions)) {
                return true;
            }
            if (Objects.isNull(SecurityUtils.getLoginUser().getPermissions())) {
                return true;
            }
            return SecurityUtils.getLoginUser().getPermissions().contains(permissions);
        }
        return true;
    }
}
