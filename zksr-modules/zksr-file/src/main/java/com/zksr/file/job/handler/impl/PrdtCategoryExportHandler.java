package com.zksr.file.job.handler.impl;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.product.api.catgory.CatgoryApi;
import com.zksr.product.api.catgory.excel.PrdtCatgoryExcel;
import com.zksr.product.api.catgory.vo.PrdtCatgoryPageVO;
import com.zksr.product.api.skuPrice.SkuPriceApi;
import com.zksr.product.api.skuPrice.dto.PrdtSkPageReqApiVO;
import com.zksr.product.api.skuPrice.dto.PrdtSkuPriceInfoExportVO;
import com.zksr.system.api.domain.SysDictData;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 城市价格导出
 */
@ExportHandler(value = ExportType.CATEGORY_EXPORT, defaultPage = false)
public class PrdtCategoryExportHandler extends ReportAbstractContentHandler<PrdtCatgoryExcel> {

    @Resource
    private CatgoryApi catgoryApi;
    @Override
    public List<PrdtCatgoryExcel> getList(ExportJobContext jobContext) {

        PrdtCatgoryPageVO pageVO = jobContext.getQueryData(PrdtCatgoryPageVO.class);
        pageVO.setPage(jobContext.page());
        List<PrdtCatgoryExcel> list = new ArrayList<>();
        Long sysSupplierId = SecurityUtils.getSupplierId();
        if (Objects.nonNull(sysSupplierId)) {
            pageVO.setSupplierId(sysSupplierId);
        }

        int i = 1;
        for (;;) {
            pageVO.setPageSize(50);
            pageVO.setPageNo(i);

            List<PrdtCatgoryExcel> list1 = catgoryApi.getPrdtCatgoryExcel(pageVO).getCheckedData();

            list1.forEach((item)->{
                if ("0".equals(item.getStatus())){
                    item.setStatus("停用");
                }else if ("1".equals(item.getStatus())){
                    item.setStatus("启用");
                }
            });

            if (ToolUtil.isEmpty(list1) || list1.isEmpty()) {
                break;
            }
            list.addAll(list1);
            i++;
        }
        return list;
    }
}
