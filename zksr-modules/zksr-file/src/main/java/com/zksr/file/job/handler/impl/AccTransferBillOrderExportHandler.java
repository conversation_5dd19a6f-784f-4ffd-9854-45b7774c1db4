package com.zksr.file.job.handler.impl;

import com.zksr.account.api.transfer.TransferApi;
import com.zksr.account.api.transfer.dto.AccTransferBillOrderRespDTO;
import com.zksr.account.api.transfer.vo.AccTransferBillOrderExportPageVO;
import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 交易对账单明细导出
 * @date 2025/01/22 15:41
 */
@ExportHandler(value = ExportType.ACC_TRANSFER_BILL_ORDER_EXPORT, defaultPage = false)
public class AccTransferBillOrderExportHandler extends ReportAbstractContentHandler<AccTransferBillOrderRespDTO> {
    @Resource
    private TransferApi transferApi;
    @Override
    public List<AccTransferBillOrderRespDTO> getList(ExportJobContext jobContext) {
        AccTransferBillOrderExportPageVO pageVO = jobContext.getQueryData(AccTransferBillOrderExportPageVO.class);
        pageVO.setSysCode(jobContext.getLoginUser().getSysCode());
        List<AccTransferBillOrderRespDTO> resultList = new ArrayList<>();
        int i = 1;
        for (;;) {
            // 这里每次分页查询50条，因返回数据查询速度过慢，超出50条会导致接口超时，后续查询优化可将分页条数进行调整
            pageVO.setPageSize(50);
            pageVO.setPageNo(i);
            List<AccTransferBillOrderRespDTO> list = transferApi.getAccTransferBillOrderDataExport(pageVO).getCheckedData();
            if (ToolUtil.isEmpty(list) || list.isEmpty()) {
                break;
            }
            i++;
            resultList.addAll(list);
        }
        return resultList;
    }
}
