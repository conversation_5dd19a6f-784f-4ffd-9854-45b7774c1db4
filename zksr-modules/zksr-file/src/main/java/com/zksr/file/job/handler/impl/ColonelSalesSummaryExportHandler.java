package com.zksr.file.job.handler.impl;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.utils.DcUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.report.api.export.RptExportApi;
import com.zksr.report.api.export.dto.ColonelSalesSummaryExportDTO;
import com.zksr.report.api.export.vo.ColonelSalesSummaryExportPageVO;
import com.zksr.system.api.model.LoginUser;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 业务员销售月报表导出
 * @date 2025/01/22 15:41
 */
@ExportHandler(value = ExportType.COLONEL_SALES_MONTH_EXPORT, defaultPage = false)
public class ColonelSalesSummaryExportHandler extends ReportAbstractContentHandler<ColonelSalesSummaryExportDTO> {
    @Resource
    private RptExportApi rptExportApi;
    @Override
    public List<ColonelSalesSummaryExportDTO> getList(ExportJobContext jobContext) {
        ColonelSalesSummaryExportPageVO pageVO = jobContext.getQueryData(ColonelSalesSummaryExportPageVO.class);
        pageVO.setSysCode(jobContext.getLoginUser().getSysUser().getSysCode());

        Map<String, Object> params = new HashMap<>();
       LoginUser loginUser = jobContext.getLoginUser();
        if (ToolUtil.isNotEmpty(loginUser.getDcId())) {
            StringBuilder sqlString = new StringBuilder();
            List<Long> areaIdList = DcUtils.getAreaList(jobContext.getLoginUser().getSysUser().getDcId());
            if (Objects.nonNull(areaIdList)) {
                sqlString.append(StringUtils.format(" AND t1.area_id IN ({}) ", StringUtils.join(areaIdList, StringPool.COMMA)));
            } else {
                sqlString.append(StringUtils.format(" AND t1.area_id IN ({}) ", -1));
            }
            params.put("dataScope", sqlString);

        }
        pageVO.setParams(params);

        List<ColonelSalesSummaryExportDTO> resultList = new ArrayList<>();
        int i = 1;
        for (;;) {
            // 这里每次分页查询50条，因返回数据查询速度过慢，超出50条会导致接口超时，后续查询优化可将分页条数进行调整
            pageVO.setPageSize(50);
            pageVO.setPageNo((i - 1) * pageVO.getPageSize());
            List<ColonelSalesSummaryExportDTO> list = rptExportApi.getMonthColonelSalesSummaryExport(pageVO).getCheckedData();
            if (ToolUtil.isEmpty(list) || list.isEmpty()) {
                break;
            }
            i++;
            resultList.addAll(list);
        }
        return resultList;
    }
}
