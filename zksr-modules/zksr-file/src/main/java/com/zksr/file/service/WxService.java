package com.zksr.file.service;

import com.zksr.file.api.file.vo.MchProfitRateReqVO;
import com.zksr.file.api.file.vo.MchProfitRateRespVO;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信服务
 * @date 2024/8/12 18:48
 */
public interface WxService {

    /**
     * 处理微信第三方平台服务商推送ticket
     * @param body  加密xml
     */
    void receiveMerchantAuth(String body);

    /**
     * 获取第三方平台, 微信服务商ComponentAccessToken
     * @return
     */
    String getComponentAccessToken();

    /**
     * 获取预授权code
     * @return
     */
    String getPreAuthCode();

    /**
     * 更新小程序授权
     * @param appid     小程序ID
     * @param authCode  授权code
     */
    void updateAuthCode(String appid, String authCode);

    /**
     * 获取小程序代理 accesstoken
     * @param appid 授权小程序appid
     * @return authorizerAccessToken
     */
    String getAuthorizerAccessToken(String appid);

    /**
     * 刷新 AuthorizerAccessToken
     */
    void refreshAuthorizerAccessToken();

    /**
     * 处理微信小程序支付回调
     * @param params    表单消息
     * @param body      body消息
     */
    void processWxReceiveMsg(Map<String, String> params, String body);

    /**
     * 服务商设置技术手续费比例
     * @param mchProfitRateReqVO    请求
     * @return
     */
    MchProfitRateRespVO setMchProfitRate(MchProfitRateReqVO mchProfitRateReqVO);

    /**
     *
     * @param appid
     * @return
     */
    String getAccessToken(String appid);

    /**
     * 获取开放路径token
     * @return
     */
    String getOpenToken();

    /**
     * 获取小程序认证链接
     * @param key
     * @param appid
     * @return
     */
    String getAppWxB2bAuthUrl(String key, String appid);

}
