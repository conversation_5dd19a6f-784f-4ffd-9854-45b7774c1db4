package com.zksr.file.api;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.file.api.file.FileApi;
import com.zksr.file.api.file.vo.ImageInfoReqVO;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.file.config.PlatformConfig;
import com.zksr.file.importJob.exe.ImportJobHandler;
import com.zksr.file.service.ISysFileService;
import com.zksr.file.service.WxService;
import com.zksr.system.api.domain.SysFileImport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Slf4j
@ApiIgnore
public class FileApiImpl  implements FileApi {

    @Autowired
    private PlatformConfig platformConfig;

    @Autowired
    private WxService wxService;

    @Autowired
    private List<ISysFileService> sysFileServices;
    @Autowired
    private ImportJobHandler importJobHandler;

    @Override
    public CommonResult<String> uploadFileByMail(MultipartFile file) throws Exception {
        return success(getFileService().uploadFile(file));
    }

    @Override
    public byte[] downloadImages(List<ImageInfoReqVO> images) throws IOException {
        return getFileService().downloadImages(images);
    }

    @Override
    public CommonResult<String> downloadByBase64(String imgUrl) {
        return CommonResult.success(getFileService().downloadBase64(imgUrl));
    }

    @Override
    public CommonResult<Boolean> refreshWxToken() {
        wxService.refreshAuthorizerAccessToken();
        return success(Boolean.TRUE);
    }

    public CommonResult<Boolean> importJobExecute(SysFileImportVO sysFileImport){
        importJobHandler.importJobExecute(sysFileImport);
        return success(Boolean.TRUE);
    }


    ISysFileService getFileService() {
        for (ISysFileService fileService : sysFileServices) {
            if (fileService.platform().equals(platformConfig.getPlatform())) {
                return fileService;
            }
        }
        return null;
    }
}
