package com.zksr.file.importJob.handler.impl;

import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.file.api.constant.ImportTypeEnum;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.file.importJob.handler.IImportContentHandler;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.areaClass.excel.ProductAreaClassImportExcel;
import com.zksr.product.api.areaClass.form.AreaClassImportForm;
import com.zksr.product.api.areaItem.AreaItemApi;
import com.zksr.product.api.areaItem.excel.PrdtAreaItemImportExcel;
import com.zksr.product.api.areaItem.form.PrdAreaItemImportForm;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class PrdAreaItemImportHandler implements IImportContentHandler<PrdtAreaItemImportExcel> {

    @Resource
    private AreaItemApi areaItemApi;

    @Override
    public String getImportType() {
        return ImportTypeEnum.PRD_AREA_ITEM_IMPORT.getCode();
    }


    @Override
    public void importData(SysFileImportVO sysFileImport) {
        ExcelUtil<PrdtAreaItemImportExcel> util = new ExcelUtil<>(PrdtAreaItemImportExcel.class);
        List<PrdtAreaItemImportExcel> orderExportList = util.getImportExcelData(sysFileImport.getFileUrl(),1);

        PrdAreaItemImportForm areaItemImportForm = new PrdAreaItemImportForm();
        areaItemImportForm.setList(orderExportList);
        areaItemImportForm.setFileImportId(sysFileImport.getFileImportId());
        areaItemImportForm.setSysCode(sysFileImport.getSysCode());
        areaItemImportForm.setDcId(sysFileImport.getDcId());

        areaItemApi.importDataDcEvent(areaItemImportForm);

    }
}
