
package com.zksr.file.job.handler.impl;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.trade.api.orderSettle.OrderSettleApi;
import com.zksr.trade.api.orderSettle.dto.OrderSettleResDTO;
import com.zksr.trade.api.orderSettle.vo.OrderSettlePageVO;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户结算明细
 * @date 2024/1/15 15:41
 */
@ExportHandler(value = ExportType.MERCHANT_SETTLE_EXPORT, defaultPage = false)
public class MerchantSettleExportHandler extends ReportAbstractContentHandler<OrderSettleResDTO> {

    @Resource
    private OrderSettleApi orderSettleApi;

    @Override
    public List<OrderSettleResDTO> getList(ExportJobContext jobContext) {
        OrderSettlePageVO pageVO = jobContext.getQueryData(OrderSettlePageVO.class);
        pageVO.setPage(jobContext.page());
        return orderSettleApi.getAccountOrderSettleFlowPageList(pageVO).getCheckedData();
    }
}
