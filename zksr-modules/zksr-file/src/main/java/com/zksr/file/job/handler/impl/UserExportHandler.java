package com.zksr.file.job.handler.impl;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.domain.R;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.api.model.UserExportPo;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.system.api.RemoteUserService;
import com.zksr.system.api.export.vo.SysExportJob;
import com.zksr.system.api.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义导出测试用户导出
 * @date 2024/1/15 15:41
 */
@ExportHandler(value = ExportType.USER_EXPORT)
public class UserExportHandler extends ReportAbstractContentHandler<UserExportPo> {

    @Autowired
    private RemoteUserService remoteUserService;

    @Override
    public List<UserExportPo> getList(ExportJobContext jobContext) {
        // 可不用管分页什么的, 直接自定义导出, 返回一个空的数据就行了
        // BigExcelWriter writer = wmsReportJobPo.getWriter();
        // return new ArrayList();
        // 跨RPC 分页还未实现
        R<List<SysUser>> listR = remoteUserService.exportTest();
        ArrayList<UserExportPo> exportPos = new ArrayList<>();
        if (listR.isSuccess()) {
            for (SysUser datum : listR.getData()) {
                UserExportPo item = new UserExportPo();
                item.setUserName(datum.getUserName());
                item.setPhonenumber(datum.getPhonenumber());
                exportPos.add(item);
            }
        }
        return exportPos;
    }
}
