package com.zksr.file.job.handler.impl;

import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuPricesRespDTO;
import com.zksr.product.api.sku.vo.SkuPricesPageReqVO;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description: SKU价格导出
 * @date 2024/11/2 15:41
 */
//@ExportHandler(value = ExportType.SKU_PRICES_EXPORT, defaultPage = false)   这个重复了  你是会复制的!!!
public class AdjustPricesExportHandler extends ReportAbstractContentHandler<SkuPricesRespDTO> {
    @Resource
    private SkuApi skuApi;
    @Override
    public List<SkuPricesRespDTO> getList(ExportJobContext jobContext) {
        SkuPricesPageReqVO pageVO = jobContext.getQueryData(SkuPricesPageReqVO.class);
        pageVO.setPage(jobContext.page());
        return skuApi.getSkuPricesList(pageVO).getCheckedData();
    }
}
