package com.zksr.file.importJob.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.file.api.constant.ImportTypeEnum;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.file.importJob.handler.IImportContentHandler;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.excel.MemBranchImportExcel;
import com.zksr.member.api.branch.form.BranchImportForm;
import com.zksr.product.api.areaClass.excel.ProductAreaClassImportExcel;
import com.zksr.product.api.areaClass.form.AreaClassImportForm;
import com.zksr.product.api.brand.BrandApi;
import com.zksr.product.api.brand.excel.BrandImportExcel;
import com.zksr.product.api.brand.form.BrandImportForm;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.fileImport.FileImportApi;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class BrandImportHandler extends ImportContentHandlerEvent<BrandImportExcel> implements IImportContentHandler<BrandImportExcel> {

    @Resource
    private BrandApi brandApi;

    @Override
    public String getImportType() {
        return ImportTypeEnum.BRAND_IMPORT.getCode();
    }

    public void importData(SysFileImportVO sysFileImport) {
        List<List<BrandImportExcel>> lists = excelData(ImportTypeEnum.BRAND_IMPORT.getName(), 1, 1, BrandImportExcel.class, sysFileImport.getFileUrl());
        SysFileImport fileImport = getFileImport(sysFileImport.getFileImportId());

        BrandImportForm brandImportForm = new BrandImportForm();
        brandImportForm.setFileImportId(sysFileImport.getFileImportId());
        brandImportForm.setSysCode(sysFileImport.getSysCode());

        FileImportHandlerVo vo = new FileImportHandlerVo();
        List<SysFileImportDtl> list = new ArrayList<>();
        for (List<BrandImportExcel> excels : lists) {
            log.info("品牌信息导第{}数据导入开始",vo.getTotalNum()+1);
            brandImportForm.setList(excels);
            brandImportForm.setSeq(vo.getTotalNum());
            CommonResult<String> stringCommonResult = brandApi.importBrandData(brandImportForm);
            importResult(stringCommonResult,list,vo,excels.size(),ImportTypeEnum.BRAND_IMPORT.getName());
        }
        renewalFileImport(fileImport,list,vo,ImportTypeEnum.BRAND_IMPORT.getName());

    }

}
