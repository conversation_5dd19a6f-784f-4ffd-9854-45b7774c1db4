package com.zksr.file.service;

import com.zksr.file.config.PlatformConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/10/24 18:53
 */
@Service
public class PlatformFileService {

    @Autowired
    private List<ISysFileService> sysFileServices;

    @Autowired
    private PlatformConfig platformConfig;

    public ISysFileService getFileService() {
        for (ISysFileService fileService : sysFileServices) {
            if (fileService.platform().equals(platformConfig.getPlatform())) {
                return fileService;
            }
        }
        return null;
    }
}
