package com.zksr.file.job.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.annotation.ExportHandler;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.file.job.exe.ExportJobContext;
import com.zksr.file.job.handler.ReportAbstractContentHandler;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.DcOrderPageReqApiVO;
import com.zksr.trade.api.order.vo.OrderPageReqVO;
import com.zksr.trade.api.order.vo.SupplierOrderDtlInfoExportVO;
import com.zksr.trade.api.order.vo.SupplierOrderExportVO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *入驻商订单导出
 */
@ExportHandler(value = ExportType.TRD_SUPPLIER_ORDER_EXPORT, defaultPage = false)
public class TrdSupplierOrderExportHandler extends ReportAbstractContentHandler<SupplierOrderExportVO> {
    @Resource
    private OrderApi orderApi;
    @Override
    public List<SupplierOrderExportVO> getList(ExportJobContext jobContext) {
        OrderPageReqVO pageVO = jobContext.getQueryData(OrderPageReqVO.class);
        pageVO.setPage(jobContext.page());
        List<SupplierOrderExportVO> list = new ArrayList<>();

        Long dcId = jobContext.getLoginUser().getSysUser().getDcId();
        Long supplierId = jobContext.getLoginUser().getSysUser().getSupplierId();
        if (ObjectUtil.isNotNull(dcId)){
            pageVO.setDcId(dcId);
        }
        if (ObjectUtil.isNotNull(supplierId)){
            pageVO.setSupplierId(supplierId);
        }

        int i = 1;
        for (;;) {
            pageVO.setPageSize(50);
            pageVO.setPageNo(i);
            List<SupplierOrderExportVO> list1 = orderApi.selectSupplierOrder(pageVO).getCheckedData();

            for (SupplierOrderExportVO supplierOrderExportVO : list1) {
                if (StringUtils.isNotEmpty(supplierOrderExportVO.getPrintState())){
                    if ("1".equals(supplierOrderExportVO.getPrintState())){
                        supplierOrderExportVO.setPrintState("已打印");
                    }else {
                        supplierOrderExportVO.setPrintState("未打印");
                    }
                }
            }
            if (ToolUtil.isEmpty(list1) || list1.isEmpty()) {
                break;
            }
            list.addAll(list1);
            i++;
        }
        return list;
    }
}
