package com.zksr.report;

import com.zksr.report.controller.schedule.vo.ComputeBranchTagReqVO;
import com.zksr.report.service.IScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 报表模块
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallReportApplication.class)
@Slf4j
public class ZksrMallReportTestApplication {

    /*@Autowired
    private IScheduleService scheduleService;

    @Test
    public void test01() {
        ComputeBranchTagReqVO req = new ComputeBranchTagReqVO();
        req.setBranchId(466979106766028801L);
        req.setMonthDate("202411");
        scheduleService.computeBranchTag(req);
    }*/
}
