-- =======================================================城市门店统计月表（ads_area_branch_stats_stats）==========================
replace into ads_area_branch_stats_stats(
    month_id
    , sys_code
    , area_id
    , branch_qty
    , active_branch_qty
    , a_level_branch_qty
    , b_level_branch_qty
    , c_level_branch_qty
    , d_level_branch_qty
    , e_level_branch_qty
    , a_level_branch_sales_amt
    , b_level_branch_sales_amt
    , c_level_branch_sales_amt
    , d_level_branch_sales_amt
    , e_level_branch_sales_amt
    , area_colonel_profit
    , profit
	, sales_amt
	, profit_rate
    , insert_date_id
)
SELECT
    mt.month_id,
    IFNULL(area.sys_code, -1) AS sys_code,
    mt.area_id,
    mt.branch_qty,
    mt.active_branch_qty,
    mt.a_level_branch_qty,
    mt.b_level_branch_qty,
    mt.c_level_branch_qty,
    mt.d_level_branch_qty,
    mt.e_level_branch_qty,
    mt.a_level_branch_sales_amt,
    mt.b_level_branch_sales_amt,
    mt.c_level_branch_sales_amt,
    mt.d_level_branch_sales_amt,
    mt.e_level_branch_sales_amt,
    et.area_colonel_profit,
    et.profit,
    et.sales_amt,
    ROUND(et.profit / et.sales_amt, 6) profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            btm.month_id,
            btm.area_id,
            COUNT(1) branch_qty,
            SUM(CASE WHEN btm.active_tag_type IS NOT NULL THEN 1 ELSE 0 END) active_branch_qty,
            SUM(CASE WHEN btm.level_tag_type = 'level_a' THEN 1 ELSE 0 END) a_level_branch_qty,
            SUM(CASE WHEN btm.level_tag_type = 'level_b' THEN 1 ELSE 0 END) b_level_branch_qty,
            SUM(CASE WHEN btm.level_tag_type = 'level_c' THEN 1 ELSE 0 END) c_level_branch_qty,
            SUM(CASE WHEN btm.level_tag_type = 'level_d' THEN 1 ELSE 0 END) d_level_branch_qty,
            SUM(CASE WHEN btm.level_tag_type = 'level_e' THEN 1 ELSE 0 END) e_level_branch_qty,
            SUM(CASE WHEN btm.level_tag_type = 'level_a' THEN REPLACE ( btm.`level_tag_val`, '元', '' ) ELSE 0 END) a_level_branch_sales_amt,
            SUM(CASE WHEN btm.level_tag_type = 'level_b' THEN REPLACE ( btm.`level_tag_val`, '元', '' ) ELSE 0 END) b_level_branch_sales_amt,
            SUM(CASE WHEN btm.level_tag_type = 'level_c' THEN REPLACE ( btm.`level_tag_val`, '元', '' ) ELSE 0 END) c_level_branch_sales_amt,
            SUM(CASE WHEN btm.level_tag_type = 'level_d' THEN REPLACE ( btm.`level_tag_val`, '元', '' ) ELSE 0 END) d_level_branch_sales_amt,
            SUM(CASE WHEN btm.level_tag_type = 'level_e' THEN REPLACE ( btm.`level_tag_val`, '元', '' ) ELSE 0 END) e_level_branch_sales_amt
        FROM
            ads_branch_tag_month btm
        WHERE
            btm.month_id IN (
                SELECT month_id FROM ads_branch_tag_month WHERE DATE_FORMAT(insert_date, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d')
            )
        GROUP BY
            btm.month_id,
            btm.area_id
    ) mt
    LEFT JOIN (
        SELECT
            tasm.month_id,
            tasm.area_id,
            SUM(tasm.profit_colonel_amt - tasm.return_profit_colonel_amt) area_colonel_profit,
            SUM(tasm.profit_amt - tasm.return_profit_amt) profit,
            SUM(tasm.order_amt - tasm.return_amt) sales_amt
        FROM
            dws_trd_area_sales_month tasm
        GROUP BY
            tasm.month_id,
            tasm.area_id
    ) et ON mt.month_id = et.month_id AND mt.area_id = et.area_id
    LEFT JOIN dim_area area ON area.area_id = mt.area_id

-- =========================================================门店一级类别销售金额统计月表（ads_branch_cat1_orderamt_stats_month）========================================
replace into ads_branch_cat1_orderamt_stats_month (
    month_id
    , sys_code
    , area_id
    , branch_id
    , cat1_id
    , branch_order_amt
    , cat1_order_amt
    , proportion
    , insert_date_id
)
SELECT
    bcosm.*,
    cat1Area.cat1_order_amt,
    CASE
        WHEN cat1Area.cat1_order_amt <= 0 THEN 0
        ELSE ROUND((bcosm.branch_order_amt / cat1Area.cat1_order_amt) * 100, 6)
    END AS proportion,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            dtbcsm.month_id,
            dtbcsm.sys_code,
            dtbcsm.area_id,
            dtbcsm.branch_id,
            dtbcsm.catgory1_id AS cat1_id,
            SUM( dtbcsm.order_amt ) AS branch_order_amt
        FROM
            dws_trd_branch_category_sales_month dtbcsm
        WHERE
            dtbcsm.month_id IN (
                SELECT month_id FROM dws_trd_branch_category_sales_month WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY month_id
            )
        GROUP BY
            month_id,
            sys_code,
            area_id,
            catgory1_id,
            branch_id
    ) bcosm
    LEFT JOIN (
        SELECT
            month_id,
            sys_code,
            area_id,
            catgory1_id AS cat1_id,
            SUM( order_amt ) AS cat1_order_amt
        FROM
            dws_trd_branch_category_sales_month
        WHERE
            month_id IN (
                SELECT month_id FROM dws_trd_branch_category_sales_month WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY month_id
            )
        GROUP BY
            month_id,
            sys_code,
            area_id,
            catgory1_id
    ) cat1Area ON bcosm.month_id = cat1Area.month_id AND bcosm.sys_code = cat1Area.sys_code AND bcosm.area_id = cat1Area.area_id AND bcosm.cat1_id = cat1Area.cat1_id

--===================================================================城市一级类别销售金额统计月表（ads_area_cat1_stats_month）===================
replace into ads_area_cat1_stats_month (
    month_id
    , sys_code
    , area_id
    , cat1_id
    , area_order_amt
    , cat1_order_amt
    , proportion
    , insert_date_id
)
SELECT
    bcosm.*,
    cat1Area.cat1_order_amt,
    CASE
        WHEN cat1Area.cat1_order_amt <= 0 THEN 0
        ELSE ROUND((bcosm.area_order_amt / cat1Area.cat1_order_amt) * 100, 6)
    END AS proportion,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            dtbcsm.month_id,
            dtbcsm.sys_code,
            dtbcsm.area_id,
            dtbcsm.catgory1_id AS cat1_id,
            SUM( dtbcsm.order_amt ) AS area_order_amt
        FROM
            dws_trd_branch_category_sales_month dtbcsm
        WHERE
            dtbcsm.month_id IN (
                SELECT month_id FROM dws_trd_branch_category_sales_month WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY month_id
            )
        GROUP BY
            month_id,
            sys_code,
            area_id,
            catgory1_id
    ) bcosm
    LEFT JOIN (
        SELECT
            month_id,
            sys_code,
            catgory1_id AS cat1_id,
            SUM( order_amt ) AS cat1_order_amt
        FROM
            dws_trd_branch_category_sales_month
        WHERE
            month_id IN (
                SELECT month_id FROM dws_trd_branch_category_sales_month WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY month_id
            )
        GROUP BY
            month_id,
            sys_code,
            catgory1_id
    ) cat1Area ON bcosm.month_id = cat1Area.month_id AND bcosm.sys_code = cat1Area.sys_code AND bcosm.cat1_id = cat1Area.cat1_id


-- ===========================================================================================门店周下单分布月表 (ads_branch_trade_weekday_distribution)==============
replace into ads_branch_trade_weekday_distribution (
    month_id
    , sys_code
    , area_id
    , branch_id
    , monday_dx_branch_qty
    , monday_order_qty
    , monday_order_amt
    , tuesday_dx_branch_qty
    , tuesday_order_qty
    , tuesday_order_amt
    , wednesday_dx_branch_qty
    , wednesday_order_qty
    , wednesday_order_amt
    , thursday_dx_branch_qty
    , thursday_order_qty
    , thursday_order_amt
    , friday_dx_branch_qty
    , friday_order_qty
    , friday_order_amt
    , saturday_dx_branch_qty
    , saturday_order_qty
    , saturday_order_amt
    , sunday_dx_branch_qty
    , sunday_order_qty
    , sunday_order_amt
    , insert_date_id
)
SELECT
    SUBSTRING( dtbsd.date_id , 1, 6 ) AS month_id,
    dtbsd.sys_code,
    dtbsd.area_id,
    dtbsd.branch_id,
    -- 周一
    SUM(CASE WHEN dDate.day_of_week = 0 THEN dtbsd.dx_branch_qty ELSE 0 END) AS monday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 0 THEN dtbsd.order_qty ELSE 0 END) AS monday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 0 THEN dtbsd.order_amt ELSE 0 END) AS monday_order_amt,
    -- 周二
    SUM(CASE WHEN dDate.day_of_week = 1 THEN dtbsd.dx_branch_qty ELSE 0 END) AS tuesday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 1 THEN dtbsd.order_qty ELSE 0 END) AS tuesday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 1 THEN dtbsd.order_amt ELSE 0 END) AS tuesday_order_amt,
    -- 周三
    SUM(CASE WHEN dDate.day_of_week = 2 THEN dtbsd.dx_branch_qty ELSE 0 END) AS wednesday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 2 THEN dtbsd.order_qty ELSE 0 END) AS wednesday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 2 THEN dtbsd.order_amt ELSE 0 END) AS wednesday_order_amt,
    -- 周四
    SUM(CASE WHEN dDate.day_of_week = 3 THEN dtbsd.dx_branch_qty ELSE 0 END) AS thursday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 3 THEN dtbsd.order_qty ELSE 0 END) AS thursday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 3 THEN dtbsd.order_amt ELSE 0 END) AS thursday_order_amt,
    -- 周五
    SUM(CASE WHEN dDate.day_of_week = 4 THEN dtbsd.dx_branch_qty ELSE 0 END) AS friday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 4 THEN dtbsd.order_qty ELSE 0 END) AS friday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 4 THEN dtbsd.order_amt ELSE 0 END) AS friday_order_amt,
    -- 周六
    SUM(CASE WHEN dDate.day_of_week = 5 THEN dtbsd.dx_branch_qty ELSE 0 END) AS saturday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 5 THEN dtbsd.order_qty ELSE 0 END) AS saturday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 5 THEN dtbsd.order_amt ELSE 0 END) AS saturday_order_amt,
    -- 周日
    SUM(CASE WHEN dDate.day_of_week = 6 THEN dtbsd.dx_branch_qty ELSE 0 END) AS sunday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 6 THEN dtbsd.order_qty ELSE 0 END) AS sunday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 6 THEN dtbsd.order_amt ELSE 0 END) AS sunday_order_amt,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    dws_trd_branch_sales_day dtbsd
    LEFT JOIN dim_date dDate ON dtbsd.date_id = dDate.date_id
WHERE
    SUBSTRING( dtbsd.date_id , 1, 6 ) IN (
        SELECT SUBSTRING( date_id , 1, 6 ) FROM dws_trd_branch_sales_day WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id , 1, 6 )
    )
GROUP BY
    SUBSTRING( dtbsd.date_id , 1, 6 ),
    dtbsd.sys_code,
    dtbsd.area_id,
    dtbsd.branch_id
-- =============================================================================城市周下单分布月表 （ads_area_trade_weekday_distribution）========================
replace into ads_area_trade_weekday_distribution
    (
    month_id
    , sys_code
    , area_id
    , monday_dx_branch_qty
    , monday_order_qty
    , monday_order_amt
    , tuesday_dx_branch_qty
    , tuesday_order_qty
    , tuesday_order_amt
    , wednesday_dx_branch_qty
    , wednesday_order_qty
    , wednesday_order_amt
    , thursday_dx_branch_qty
    , thursday_order_qty
    , thursday_order_amt
    , friday_dx_branch_qty
    , friday_order_qty
    , friday_order_amt
    , saturday_dx_branch_qty
    , saturday_order_qty
    , saturday_order_amt
    , sunday_dx_branch_qty
    , sunday_order_qty
    , sunday_order_amt
    , insert_date_id
)
SELECT
    SUBSTRING( dtbsd.date_id , 1, 6 ) AS month_id,
    dtbsd.sys_code,
    dtbsd.area_id,
    -- 周一
    SUM(CASE WHEN dDate.day_of_week = 0 THEN dtbsd.dx_branch_qty ELSE 0 END) AS monday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 0 THEN dtbsd.order_qty ELSE 0 END) AS monday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 0 THEN dtbsd.order_amt ELSE 0 END) AS monday_order_amt,
    -- 周二
    SUM(CASE WHEN dDate.day_of_week = 1 THEN dtbsd.dx_branch_qty ELSE 0 END) AS tuesday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 1 THEN dtbsd.order_qty ELSE 0 END) AS tuesday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 1 THEN dtbsd.order_amt ELSE 0 END) AS tuesday_order_amt,
    -- 周三
    SUM(CASE WHEN dDate.day_of_week = 2 THEN dtbsd.dx_branch_qty ELSE 0 END) AS wednesday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 2 THEN dtbsd.order_qty ELSE 0 END) AS wednesday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 2 THEN dtbsd.order_amt ELSE 0 END) AS wednesday_order_amt,
    -- 周四
    SUM(CASE WHEN dDate.day_of_week = 3 THEN dtbsd.dx_branch_qty ELSE 0 END) AS thursday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 3 THEN dtbsd.order_qty ELSE 0 END) AS thursday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 3 THEN dtbsd.order_amt ELSE 0 END) AS thursday_order_amt,
    -- 周五
    SUM(CASE WHEN dDate.day_of_week = 4 THEN dtbsd.dx_branch_qty ELSE 0 END) AS friday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 4 THEN dtbsd.order_qty ELSE 0 END) AS friday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 4 THEN dtbsd.order_amt ELSE 0 END) AS friday_order_amt,
    -- 周六
    SUM(CASE WHEN dDate.day_of_week = 5 THEN dtbsd.dx_branch_qty ELSE 0 END) AS saturday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 5 THEN dtbsd.order_qty ELSE 0 END) AS saturday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 5 THEN dtbsd.order_amt ELSE 0 END) AS saturday_order_amt,
    -- 周日
    SUM(CASE WHEN dDate.day_of_week = 6 THEN dtbsd.dx_branch_qty ELSE 0 END) AS sunday_dx_branch_qty,
    SUM(CASE WHEN dDate.day_of_week = 6 THEN dtbsd.order_qty ELSE 0 END) AS sunday_order_qty,
    SUM(CASE WHEN dDate.day_of_week = 6 THEN dtbsd.order_amt ELSE 0 END) AS sunday_order_amt,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    dws_trd_branch_sales_day dtbsd
    LEFT JOIN dim_date dDate ON dtbsd.date_id = dDate.date_id
WHERE
    SUBSTRING( dtbsd.date_id , 1, 6 ) IN (
        SELECT SUBSTRING( date_id , 1, 6 ) FROM dws_trd_branch_sales_day WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id , 1, 6 )
    )
GROUP BY
    SUBSTRING( dtbsd.date_id , 1, 6 ),
    dtbsd.sys_code,
    dtbsd.area_id