DROP TABLE IF EXISTS ads_branch_tag_month;
CREATE TABLE `ads_branch_tag_month` (
                                        `month_id` bigint(20) NOT NULL COMMENT '月份ID',
                                        `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                        `branch_id` bigint(20) NOT NULL COMMENT '门店id',
                                        `area_id` bigint(20) NOT NULL COMMENT '区域id',
                                        `level_tag_type` varchar(255) DEFAULT NULL COMMENT '客户等级标签类型;例：客户等级',
                                        `level_tag_def_id` bigint(20) DEFAULT NULL COMMENT '客户等级标签定义id;例：id',
                                        `level_tag_rule` varchar(255) DEFAULT NULL COMMENT '客户等级规则;例：自然月销售金额 >= 2000 元',
                                        `level_tag_name` varchar(255) DEFAULT NULL COMMENT '客户等级标签名;例：A级',
                                        `level_tag_val` varchar(255) DEFAULT NULL COMMENT '客户等级标签指标值;例：2100元',
                                        `active_tag_type` varchar(255) DEFAULT NULL COMMENT '活跃客户标签类型;例：是否活跃客户',
                                        `active_tag_def_id` bigint(20) DEFAULT NULL COMMENT '活跃客户标签定义id;例：id',
                                        `active_tag_rule` varchar(255) DEFAULT NULL COMMENT '活跃客户规则;例：自然月销售金额 >= 2000元',
                                        `active_tag_name` varchar(255) DEFAULT NULL COMMENT '活跃客户标签名;例：是',
                                        `active_tag_val` varchar(255) DEFAULT NULL COMMENT '活跃客户标签指标值;例：2100元',
                                        `lz_level_tag_type` varchar(255) DEFAULT NULL COMMENT '类占等级标签类型;例：类占等级',
                                        `lz_level_tag_def_id` bigint(20) DEFAULT NULL COMMENT '类占等级标签定义id;例：id',
                                        `lz_level_tag_rule` varchar(255) DEFAULT NULL COMMENT '类占等级规则;例：高类占比率 > 50%',
                                        `lz_level_tag_name` varchar(255) DEFAULT NULL COMMENT '类占等级标签名;例：高类占',
                                        `lz_level_tag_val` varchar(255) DEFAULT NULL COMMENT '类占等级标签指标值;例：60%',
                                        `profit_level_tag_type` varchar(255) DEFAULT NULL COMMENT '毛利等级标签类型;例：毛利等级',
                                        `profit_level_tag_def_id` bigint(20) DEFAULT NULL COMMENT '毛利等级标签定义id;例：id',
                                        `profit_level_tag_rule` varchar(255) DEFAULT NULL COMMENT '毛利等级规则;例：毛利率 > 10%',
                                        `profit_level_tag_name` varchar(255) DEFAULT NULL COMMENT '毛利等级标签名;例：高毛利',
                                        `profit_level_tag_val` varchar(255) DEFAULT NULL COMMENT '毛利等级标签指标值;例：10.88%',
                                        `freq_level_tag_type` varchar(255) DEFAULT NULL COMMENT '频次等级标签类型;例：频次等级',
                                        `freq_level_tag_def_id` bigint(20) DEFAULT NULL COMMENT '频次等级标签定义id;例：id',
                                        `freq_level_tag_rule` varchar(255) DEFAULT NULL COMMENT '频次等级规则;例：月订货天数 > 10天',
                                        `freq_level_tag_name` varchar(255) DEFAULT NULL COMMENT '频次等级标签名;例：高频次',
                                        `freq_level_tag_val` varchar(255) DEFAULT NULL COMMENT '频次等级标签指标值;例：11',
                                        `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                        `insert_date` TIMESTAMP	DEFAULT CURRENT_TIMESTAMP   COMMENT '插入时间' ,
                                        PRIMARY KEY (`month_id`,`sys_code`,`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店标签月表';


DROP TABLE IF EXISTS ads_area_branch_stats_stats;
CREATE TABLE ads_area_branch_stats_stats(
                                            `month_id` BIGINT(20) NOT NULL  COMMENT '日期ID' ,
                                            `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
                                            `area_id` BIGINT(20) NOT NULL  COMMENT '区域城市id' ,
                                            `branch_qty` INT(8)   COMMENT '门店总数' ,
                                            `active_branch_qty` INT(8)   COMMENT '活跃门店数' ,
                                            `a_level_branch_qty` INT(8)   COMMENT 'A级门店客户数' ,
                                            `b_level_branch_qty` INT(8)   COMMENT 'B级门店客户数' ,
                                            `c_level_branch_qty` INT(8)   COMMENT 'C级门店客户数' ,
                                            `d_level_branch_qty` INT(8)   COMMENT 'D级门店客户数' ,
                                            `e_level_branch_qty` INT(8)   COMMENT 'E级门店客户数' ,
                                            `a_level_branch_sales_amt` DECIMAL(12,2)   COMMENT 'A级门店销售额' ,
                                            `b_level_branch_sales_amt` DECIMAL(12,2)   COMMENT 'A级门店销售额' ,
                                            `c_level_branch_sales_amt` DECIMAL(12,2)   COMMENT 'A级门店销售额' ,
                                            `d_level_branch_sales_amt` DECIMAL(12,2)   COMMENT 'A级门店销售额' ,
                                            `e_level_branch_sales_amt` DECIMAL(12,2)   COMMENT 'A级门店销售额' ,
                                            `area_colonel_profit` DECIMAL(12,2)   COMMENT '业务员提成' ,
                                            `profit` DECIMAL(12,2)   COMMENT '总提成' ,
                                            `sales_amt` DECIMAL(12,2)   COMMENT '销售额' ,
                                            `profit_rate` DECIMAL(8,6)   COMMENT '毛利率' ,
                                            `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
                                            PRIMARY KEY (month_id,sys_code,area_id)
)  COMMENT = '城市门店统计月表';


DROP TABLE IF EXISTS ads_branch_trade_weekday_distribution;
CREATE TABLE ads_branch_trade_weekday_distribution(
                                                      `month_id` BIGINT(20) NOT NULL  COMMENT '月份ID' ,
                                                      `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
                                                      `area_id` BIGINT(20) NOT NULL  COMMENT '区域城市id' ,
                                                      `branch_id` BIGINT(20) NOT NULL  COMMENT '门店id' ,
                                                      `monday_dx_branch_qty` INT(8)   COMMENT '周一动销门店数' ,
                                                      `monday_order_qty` INT(8)   COMMENT '周一订单数量' ,
                                                      `monday_order_amt` DECIMAL(12,2)   COMMENT '周一订单金额' ,
                                                      `tuesday_dx_branch_qty` INT(8)   COMMENT '周二动销门店数' ,
                                                      `tuesday_order_qty` INT(8)   COMMENT '周二订单数量' ,
                                                      `tuesday_order_amt` DECIMAL(12,2)   COMMENT '周二订单金额' ,
                                                      `wednesday_dx_branch_qty` INT(8)   COMMENT '周三动销门店数' ,
                                                      `wednesday_order_qty` INT(8)   COMMENT '周三订单数量' ,
                                                      `wednesday_order_amt` DECIMAL(12,2)   COMMENT '周三订单金额' ,
                                                      `thursday_dx_branch_qty` INT(8)   COMMENT '周四动销门店数' ,
                                                      `thursday_order_qty` INT(8)   COMMENT '周四订单数量' ,
                                                      `thursday_order_amt` DECIMAL(12,2)   COMMENT '周四订单金额' ,
                                                      `friday_dx_branch_qty` INT(8)   COMMENT '周五动销门店数' ,
                                                      `friday_order_qty` INT(8)   COMMENT '周五订单数量' ,
                                                      `friday_order_amt` DECIMAL(12,2)   COMMENT '周五订单金额' ,
                                                      `saturday_dx_branch_qty` INT(8)   COMMENT '周六动销门店数' ,
                                                      `saturday_order_qty` INT(8)   COMMENT '周六订单数量' ,
                                                      `saturday_order_amt` DECIMAL(12,2)   COMMENT '周六订单金额' ,
                                                      `sunday_dx_branch_qty` INT(8)   COMMENT '周日动销门店数' ,
                                                      `sunday_order_qty` INT(8)   COMMENT '周日订单数量' ,
                                                      `sunday_order_amt` DECIMAL(12,2)   COMMENT '周日订单金额' ,
                                                      `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
                                                      PRIMARY KEY (month_id,sys_code,area_id,branch_id)
)  COMMENT = '门店周下单分布月表';


DROP TABLE IF EXISTS ads_area_trade_weekday_distribution;
CREATE TABLE ads_area_trade_weekday_distribution(
                                                    `month_id` BIGINT(20) NOT NULL  COMMENT '月份ID' ,
                                                    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
                                                    `area_id` BIGINT(20) NOT NULL  COMMENT '区域城市id' ,
                                                    `monday_dx_branch_qty` INT(8)   COMMENT '周一动销门店数' ,
                                                    `monday_order_qty` INT(8)   COMMENT '周一订单数量' ,
                                                    `monday_order_amt` DECIMAL(12,2)   COMMENT '周一订单金额' ,
                                                    `tuesday_dx_branch_qty` INT(8)   COMMENT '周二动销门店数' ,
                                                    `tuesday_order_qty` INT(8)   COMMENT '周二订单数量' ,
                                                    `tuesday_order_amt` DECIMAL(12,2)   COMMENT '周二订单金额' ,
                                                    `wednesday_dx_branch_qty` INT(8)   COMMENT '周三动销门店数' ,
                                                    `wednesday_order_qty` INT(8)   COMMENT '周三订单数量' ,
                                                    `wednesday_order_amt` DECIMAL(12,2)   COMMENT '周三订单金额' ,
                                                    `thursday_dx_branch_qty` INT(8)   COMMENT '周四动销门店数' ,
                                                    `thursday_order_qty` INT(8)   COMMENT '周四订单数量' ,
                                                    `thursday_order_amt` DECIMAL(12,2)   COMMENT '周四订单金额' ,
                                                    `friday_dx_branch_qty` INT(8)   COMMENT '周五动销门店数' ,
                                                    `friday_order_qty` INT(8)   COMMENT '周五订单数量' ,
                                                    `friday_order_amt` DECIMAL(12,2)   COMMENT '周五订单金额' ,
                                                    `saturday_dx_branch_qty` INT(8)   COMMENT '周六动销门店数' ,
                                                    `saturday_order_qty` INT(8)   COMMENT '周六订单数量' ,
                                                    `saturday_order_amt` DECIMAL(12,2)   COMMENT '周六订单金额' ,
                                                    `sunday_dx_branch_qty` INT(8)   COMMENT '周日动销门店数' ,
                                                    `sunday_order_qty` INT(8)   COMMENT '周日订单数量' ,
                                                    `sunday_order_amt` DECIMAL(12,2)   COMMENT '周日订单金额' ,
                                                    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
                                                    PRIMARY KEY (month_id,sys_code,area_id)
)  COMMENT = '城市周下单分布月表';


DROP TABLE IF EXISTS ads_branch_cat1_orderamt_stats_month;
CREATE TABLE ads_branch_cat1_orderamt_stats_month(
                                                     `month_id` BIGINT(20) NOT NULL  COMMENT '月份ID' ,
                                                     `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
                                                     `area_id` BIGINT(20) NOT NULL  COMMENT '区域城市id' ,
                                                     `branch_id` BIGINT(20) NOT NULL  COMMENT '门店id' ,
                                                     `cat1_id` BIGINT(20) NOT NULL  COMMENT '一级类别id' ,
                                                     `cat1_order_amt` DECIMAL(12,2)   COMMENT '一级类别id' ,
                                                     `branch_order_amt` DECIMAL(12,2)   COMMENT '门店下单金额' ,
                                                     `proportion` DECIMAL(18,6)   COMMENT '占比' ,
                                                     `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
                                                     PRIMARY KEY (month_id,sys_code,area_id,branch_id,cat1_id)
)  COMMENT = '门店一级类别销售金额统计月表';


DROP TABLE IF EXISTS ads_area_cat1_stats_month;
CREATE TABLE ads_area_cat1_stats_month(
                                          `month_id` BIGINT(20) NOT NULL  COMMENT '月份ID' ,
                                          `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
                                          `area_id` BIGINT(20) NOT NULL  COMMENT '区域城市id' ,
                                          `cat1_id` BIGINT(20) NOT NULL  COMMENT '一级类别id' ,
                                          `cat1_order_amt` DECIMAL(12,2)   COMMENT '一级类别id' ,
                                          `area_order_amt` DECIMAL(12,2)   COMMENT '区域城市下单金额' ,
                                          `proportion` DECIMAL(18,6)   COMMENT '占比' ,
                                          `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
                                          PRIMARY KEY (month_id,sys_code,area_id,cat1_id)
)  COMMENT = '城市一级类别销售统计月表';


DROP TABLE IF EXISTS ads_branch_stats_month;
CREATE TABLE ads_branch_stats_month(
                                       `month_id` BIGINT(20) NOT NULL  COMMENT '月份ID' ,
                                       `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
                                       `branch_id` BIGINT(20) NOT NULL  COMMENT '门店id' ,
                                       `branch_name` VARCHAR(255)   COMMENT '门店名称' ,
                                       `contact_name` VARCHAR(255)   COMMENT '联系人' ,
                                       `branch_addr` VARCHAR(255)   COMMENT '门店地址' ,
                                       `location` VARCHAR(255)   COMMENT '门店经纬度' ,
                                       `now_month_order_qty` INT(8)   COMMENT '本月订单笔数' ,
                                       `now_month_order_amt` DECIMAL(12,2)   COMMENT '本月订单金额' ,
                                       `last_month_order_qty` INT(8)   COMMENT '上月订单笔数' ,
                                       `last_month_order_amt` DECIMAL(12,2)   COMMENT '上月订单金额' ,
                                       `last_buy_time` DECIMAL(12,2)   COMMENT '最近一次订货金额' ,
                                       `last_access_system_time` DATETIME   COMMENT '上次访问系统时间' ,
                                       `last_login_time` DATETIME   COMMENT '最近登陆时间' ,
                                       `last_visit_time` DATETIME   COMMENT '最近一次拜访时间' ,
                                       `keyword` VARCHAR(255)   COMMENT '门店名称，联系人，门店地址' ,
                                       `month_sales_target` VARCHAR(255)   COMMENT '月销售任务' ,
                                       `level_tag_name` VARCHAR(255)   COMMENT '上月客户级别' ,
                                       `profit_level_tag_name` VARCHAR(255)   COMMENT '上月毛利级别' ,
                                       `lz_level_tag_name` VARCHAR(255)   COMMENT '上月类占级别' ,
                                       `freq_level_tag_name` VARCHAR(255)   COMMENT '上月频次级别' ,
                                       PRIMARY KEY (month_id,sys_code,branch_id)
)  COMMENT = '门店统计月表';
