DROP TABLE IF EXISTS dws_trd_supplier_sales_day;
CREATE TABLE dws_trd_supplier_sales_day(
    `date_id` BIGINT(20) NOT NULL  COMMENT '日期ID' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域ID' ,
    `order_amt` DECIMAL(12,2)   COMMENT '下单金额;订单成交金额（有效订单）' ,
    `order_qty` INT(8)   COMMENT '订单数量' ,
    `dx_sku_qty` INT(8)   COMMENT '动销SKU数;有效订单金额>0 的sku数量' ,
    `dx_spu_qty` INT(8)   COMMENT '动销SPU数;有效订单金额>0 的spu数量' ,
    `dx_branch_qty` INT(8)   COMMENT '动销门店数;有效订单金额>0 的门店数量' ,
    `supplier_cost_amt` DECIMAL(12,2)   COMMENT '入驻商总成本金额;有效订单的成本金额' ,
    `profit_amt` DECIMAL(12,2)   COMMENT '总分润金额;有效订单的分润金额' ,
    `profit_partner_amt` DECIMAL(12,2)   COMMENT '平台商分润;有效订单的平台商分润金额' ,
    `profit_dc_amt` DECIMAL(12,2)   COMMENT '运营商分润;有效订单的运营商分润金额' ,
    `profit_colonel_amt` DECIMAL(12,2)   COMMENT '业务员分润;有效订单的业务员分润金额' ,
    `profit_colonel1_amt` DECIMAL(12,2)   COMMENT '一级业务员分润;有效订单的一级业务员分润' ,
    `profit_colonel2_amt` DECIMAL(12,2)   COMMENT '二级业务员分润;有效订单的二级业务员分润' ,
    `pre_order_amt` DECIMAL(12,2)   COMMENT '原订单金额;pre_order_amt = order_amt + discount_amt' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)（有效订单）' ,
    `discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;有效订单的订单优惠金额' ,
    `profit_rate` DECIMAL(18,6)   COMMENT '利润率;profit/order_amt' ,
    `return_amt` DECIMAL(12,2)   COMMENT '收货后售后金额（收货后售后订单）' ,
    `return_qty` INT(8)   COMMENT '退单数量;收货后售后' ,
    `return_sku_qty` INT(8)   COMMENT '售后SKU数;收货后售后 的sku数量' ,
    `return_spu_qty` INT(8)   COMMENT '售后SPU数;收货后售后 的spu数量' ,
    `return_branch_qty` INT(8)   COMMENT '售后门店数;收货后售后 的门店数量' ,
    `return_supplier_cost_amt` DECIMAL(12,2)   COMMENT '售后入驻商总成本金额;收货后售后订单的成本金额' ,
    `return_profit_amt` DECIMAL(12,2)   COMMENT '售后总分润金额; 收货后售后' ,
    `return_profit_partner_amt` DECIMAL(12,2)   COMMENT '售后平台商分润;收货后售后' ,
    `return_profit_dc_amt` DECIMAL(12,2)   COMMENT '售后运营商分润;收货后售后' ,
    `return_profit_colonel_amt` DECIMAL(12,2)   COMMENT '售后业务员分润;收货后售后' ,
    `return_profit_colonel1_amt` DECIMAL(12,2)   COMMENT '售后一级业务员分润;收货后售后' ,
    `return_profit_colonel2_amt` DECIMAL(12,2)   COMMENT '售后二级业务员分润;收货后售后' ,
    `pre_return_amt` DECIMAL(12,2)   COMMENT '原售后金额;pre_return_amt = return_amt + discount_amt' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)收货后售后' ,
    `return_discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;收货后售后' ,
    `return_profit_rate` DECIMAL(18,6)   COMMENT '利润率;return_profit/return_amt' ,
    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (date_id,sys_code,supplier_id,area_id)
)  COMMENT = '交易域入驻商粒度下单日汇总表';

DROP TABLE IF EXISTS dws_trd_supplier_sales_month;
CREATE TABLE dws_trd_supplier_sales_month(
    `month_id` BIGINT(20) NOT NULL  COMMENT '日期ID' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域ID' ,
    `order_amt` DECIMAL(12,2)   COMMENT '下单金额;订单成交金额（有效订单）' ,
    `order_qty` INT(8)   COMMENT '订单数量' ,
    `dx_sku_qty` INT(8)   COMMENT '动销SKU数;有效订单金额>0 的sku数量' ,
    `dx_spu_qty` INT(8)   COMMENT '动销SPU数;有效订单金额>0 的spu数量' ,
    `dx_branch_qty` INT(8)   COMMENT '动销门店数;有效订单金额>0 的门店数量' ,
    `supplier_cost_amt` DECIMAL(12,2) COMMENT '入驻商总成本金额;有效订单的成本金额',
    `profit_amt` DECIMAL(12,2)   COMMENT '总分润金额;有效订单的分润金额' ,
    `profit_partner_amt` DECIMAL(12,2)   COMMENT '平台商分润;有效订单的平台商分润金额' ,
    `profit_dc_amt` DECIMAL(12,2)   COMMENT '运营商分润;有效订单的运营商分润金额' ,
    `profit_colonel_amt` DECIMAL(12,2)   COMMENT '业务员分润;有效订单的业务员分润金额' ,
    `profit_colonel1_amt` DECIMAL(12,2)   COMMENT '一级业务员分润;有效订单的一级业务员分润' ,
    `profit_colonel2_amt` DECIMAL(12,2)   COMMENT '二级业务员分润;有效订单的二级业务员分润' ,
    `pre_order_amt` DECIMAL(12,2)   COMMENT '原订单金额;pre_order_amt = order_amt + discount_amt' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)（有效订单）' ,
    `discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;有效订单的订单优惠金额' ,
    `profit_rate` DECIMAL(18,6)   COMMENT '销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100' ,
    `return_amt` DECIMAL(12,2)   COMMENT '收货后售后金额（收货后售后订单）' ,
    `return_qty` INT(8)   COMMENT '退单数量;收货后售后' ,
    `return_sku_qty` INT(8) DEFAULT NULL COMMENT '售后SKU数;收货后售后 的sku数量',
    `return_spu_qty` INT(8) DEFAULT NULL COMMENT '售后SPU数;收货后售后 的spu数量',
    `return_branch_qty` INT(8) DEFAULT NULL COMMENT '售后门店数;收货后售后 的门店数量',
    `return_supplier_cost_amt` DECIMAL(12,2) DEFAULT NULL COMMENT '售后入驻商总成本金额;收货后售后订单的成本金额',
    `return_profit_amt` DECIMAL(12,2)   COMMENT '售后总分润金额; 收货后售后' ,
    `return_profit_partner_amt` DECIMAL(12,2)   COMMENT '售后平台商分润;收货后售后' ,
    `return_profit_dc_amt` DECIMAL(12,2)   COMMENT '售后运营商分润;收货后售后' ,
    `return_profit_colonel_amt` DECIMAL(12,2)   COMMENT '售后业务员分润;收货后售后' ,
    `return_profit_colonel1_amt` DECIMAL(12,2)   COMMENT '售后一级业务员分润;收货后售后' ,
    `return_profit_colonel2_amt` DECIMAL(12,2)   COMMENT '售后二级业务员分润;收货后售后' ,
    `pre_return_amt` DECIMAL(12,2)   COMMENT '原售后金额;pre_return_amt = return_amt + discount_amt' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)收货后售后' ,
    `return_discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;收货后售后' ,
    `return_profit_rate` DECIMAL(18,6)   COMMENT '售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100',
    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (month_id,sys_code,supplier_id,area_id)
)  COMMENT = '交易域入驻商粒度下单月汇总表';

DROP TABLE IF EXISTS dws_trd_area_sales_day;
CREATE TABLE dws_trd_area_sales_day(
    `date_id` BIGINT(20) NOT NULL  COMMENT '日期ID' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域ID' ,
    `order_amt` DECIMAL(12,2)   COMMENT '下单金额;订单成交金额（有效订单）' ,
    `order_qty` INT(8)   COMMENT '订单数量' ,
    `dx_sku_qty` INT(8)   COMMENT '动销SKU数;有效订单金额>0 的sku数量' ,
    `dx_spu_qty` INT(8)   COMMENT '动销SPU数;有效订单金额>0 的spu数量' ,
    `dx_branch_qty` INT(8)   COMMENT '动销门店数;有效订单金额>0 的门店数量' ,
    `supplier_cost_amt` DECIMAL(12,2) COMMENT '入驻商总成本金额;有效订单的成本金额',
    `profit_amt` DECIMAL(12,2)   COMMENT '总分润金额;有效订单的分润金额' ,
    `profit_partner_amt` DECIMAL(12,2)   COMMENT '平台商分润;有效订单的平台商分润金额' ,
    `profit_dc_amt` DECIMAL(12,2)   COMMENT '运营商分润;有效订单的运营商分润金额' ,
    `profit_colonel_amt` DECIMAL(12,2)   COMMENT '业务员分润;有效订单的业务员分润金额' ,
    `profit_colonel1_amt` DECIMAL(12,2)   COMMENT '一级业务员分润;有效订单的一级业务员分润' ,
    `profit_colonel2_amt` DECIMAL(12,2)   COMMENT '二级业务员分润;有效订单的二级业务员分润' ,
    `pre_order_amt` DECIMAL(12,2)   COMMENT '原订单金额;pre_order_amt = order_amt + discount_amt' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)（有效订单）' ,
    `discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;有效订单的订单优惠金额' ,
    `profit_rate` DECIMAL(18,6)   COMMENT '销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100' ,
    `return_amt` DECIMAL(12,2)   COMMENT '收货后售后金额（收货后售后订单）' ,
    `return_qty` INT(8)   COMMENT '退单数量;收货后售后' ,
    `return_sku_qty` INT(8) DEFAULT NULL COMMENT '售后SKU数;收货后售后 的sku数量',
    `return_spu_qty` INT(8) DEFAULT NULL COMMENT '售后SPU数;收货后售后 的spu数量',
    `return_branch_qty` INT(8) DEFAULT NULL COMMENT '售后门店数;收货后售后 的门店数量',
    `return_supplier_cost_amt` DECIMAL(12,2) DEFAULT NULL COMMENT '售后入驻商总成本金额;收货后售后订单的成本金额',
    `return_profit_amt` DECIMAL(12,2)   COMMENT '售后总分润金额; 收货后售后' ,
    `return_profit_partner_amt` DECIMAL(12,2)   COMMENT '售后平台商分润;收货后售后' ,
    `return_profit_dc_amt` DECIMAL(12,2)   COMMENT '售后运营商分润;收货后售后' ,
    `return_profit_colonel_amt` DECIMAL(12,2)   COMMENT '售后业务员分润;收货后售后' ,
    `return_profit_colonel1_amt` DECIMAL(12,2)   COMMENT '售后一级业务员分润;收货后售后' ,
    `return_profit_colonel2_amt` DECIMAL(12,2)   COMMENT '售后二级业务员分润;收货后售后' ,
    `pre_return_amt` DECIMAL(12,2)   COMMENT '原售后金额;pre_return_amt = return_amt + discount_amt' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)收货后售后' ,
    `return_discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;收货后售后' ,
    `return_profit_rate` DECIMAL(18,6)   COMMENT '售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100',
    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (date_id,sys_code,area_id)
)  COMMENT = '交易域区域城市粒度下单日汇总表';

DROP TABLE IF EXISTS dws_trd_area_sales_month;
CREATE TABLE dws_trd_area_sales_month(
    `month_id` BIGINT(20) NOT NULL  COMMENT '日期ID' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域ID' ,
    `order_amt` DECIMAL(12,2)   COMMENT '下单金额;订单成交金额（有效订单）' ,
    `order_qty` INT(8)   COMMENT '订单数量' ,
    `dx_sku_qty` INT(8)   COMMENT '动销SKU数;有效订单金额>0 的sku数量' ,
    `dx_spu_qty` INT(8)   COMMENT '动销SPU数;有效订单金额>0 的spu数量' ,
    `dx_branch_qty` INT(8)   COMMENT '动销门店数;有效订单金额>0 的门店数量' ,
    `supplier_cost_amt` DECIMAL(12,2) COMMENT '入驻商总成本金额;有效订单的成本金额',
    `profit_amt` DECIMAL(12,2)   COMMENT '总分润金额;有效订单的分润金额' ,
    `profit_partner_amt` DECIMAL(12,2)   COMMENT '平台商分润;有效订单的平台商分润金额' ,
    `profit_dc_amt` DECIMAL(12,2)   COMMENT '运营商分润;有效订单的运营商分润金额' ,
    `profit_colonel_amt` DECIMAL(12,2)   COMMENT '业务员分润;有效订单的业务员分润金额' ,
    `profit_colonel1_amt` DECIMAL(12,2)   COMMENT '一级业务员分润;有效订单的一级业务员分润' ,
    `profit_colonel2_amt` DECIMAL(12,2)   COMMENT '二级业务员分润;有效订单的二级业务员分润' ,
    `pre_order_amt` DECIMAL(12,2)   COMMENT '原订单金额;pre_order_amt = order_amt + discount_amt' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)（有效订单）' ,
    `discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;有效订单的订单优惠金额' ,
    `profit_rate` DECIMAL(18,6)   COMMENT '销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100' ,
    `return_amt` DECIMAL(12,2)   COMMENT '收货后售后金额（收货后售后订单）' ,
    `return_qty` INT(8)   COMMENT '退单数量;收货后售后' ,
    `return_sku_qty` INT(8) DEFAULT NULL COMMENT '售后SKU数;收货后售后 的sku数量',
    `return_spu_qty` INT(8) DEFAULT NULL COMMENT '售后SPU数;收货后售后 的spu数量',
    `return_branch_qty` INT(8) DEFAULT NULL COMMENT '售后门店数;收货后售后 的门店数量',
    `return_supplier_cost_amt` DECIMAL(12,2) DEFAULT NULL COMMENT '售后入驻商总成本金额;收货后售后订单的成本金额',
    `return_profit_amt` DECIMAL(12,2)   COMMENT '售后总分润金额; 收货后售后' ,
    `return_profit_partner_amt` DECIMAL(12,2)   COMMENT '售后平台商分润;收货后售后' ,
    `return_profit_dc_amt` DECIMAL(12,2)   COMMENT '售后运营商分润;收货后售后' ,
    `return_profit_colonel_amt` DECIMAL(12,2)   COMMENT '售后业务员分润;收货后售后' ,
    `return_profit_colonel1_amt` DECIMAL(12,2)   COMMENT '售后一级业务员分润;收货后售后' ,
    `return_profit_colonel2_amt` DECIMAL(12,2)   COMMENT '售后二级业务员分润;收货后售后' ,
    `pre_return_amt` DECIMAL(12,2)   COMMENT '原售后金额;pre_return_amt = return_amt + discount_amt' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)收货后售后' ,
    `return_discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;收货后售后' ,
    `return_profit_rate` DECIMAL(18,6)   COMMENT '售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100',
    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (month_id,sys_code,area_id)
)  COMMENT = '交易域区域城市粒度下单月汇总表';


DROP TABLE IF EXISTS dws_trd_area_category_sales_month;
CREATE TABLE dws_trd_area_category_sales_month(
    `month_id` BIGINT(20) NOT NULL  COMMENT '日期ID' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域ID' ,
    `catgory1_id` BIGINT(20) NOT NULL  COMMENT '一级管理分类id' ,
    `catgory2_id` BIGINT(20) NOT NULL  COMMENT '二级管理分类id' ,
    `catgory3_id` BIGINT(20) NOT NULL  COMMENT '三级管理分类id' ,
    `order_amt` DECIMAL(12,2)   COMMENT '下单金额;订单成交金额（有效订单）' ,
    `order_qty` INT(8)   COMMENT '订单数量' ,
    `sales_qty` INT(8)   COMMENT '销量；有效订单金额>0 下单商品小单位总数量' ,
    `dx_sku_qty` INT(8)   COMMENT '动销SKU数;有效订单金额>0 的sku数量' ,
    `dx_spu_qty` INT(8)   COMMENT '动销SPU数;有效订单金额>0 的spu数量' ,
    `dx_branch_qty` INT(8)   COMMENT '动销门店数;有效订单金额>0 的门店数量' ,
    `supplier_cost_amt` DECIMAL(12,2) COMMENT '入驻商总成本金额;有效订单的成本金额',
    `profit_amt` DECIMAL(12,2)   COMMENT '总分润金额;有效订单的分润金额' ,
    `profit_partner_amt` DECIMAL(12,2)   COMMENT '平台商分润;有效订单的平台商分润金额' ,
    `profit_dc_amt` DECIMAL(12,2)   COMMENT '运营商分润;有效订单的运营商分润金额' ,
    `profit_colonel_amt` DECIMAL(12,2)   COMMENT '业务员分润;有效订单的业务员分润金额' ,
    `profit_colonel1_amt` DECIMAL(12,2)   COMMENT '一级业务员分润;有效订单的一级业务员分润' ,
    `profit_colonel2_amt` DECIMAL(12,2)   COMMENT '二级业务员分润;有效订单的二级业务员分润' ,
    `pre_order_amt` DECIMAL(12,2)   COMMENT '原订单金额;pre_order_amt = order_amt + discount_amt' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)（有效订单）' ,
    `discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;有效订单的订单优惠金额' ,
    `profit_rate` DECIMAL(18,6)   COMMENT '销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100' ,
    `return_amt` DECIMAL(12,2)   COMMENT '收货后售后金额（收货后售后订单）' ,
    `return_qty` INT(8)   COMMENT '退单数量;收货后售后' ,
    `return_sales_qty` INT(8)   COMMENT '售后数量；收货后售后 下单商品小单位总数量' ,
    `return_sku_qty` INT(8) DEFAULT NULL COMMENT '售后SKU数;收货后售后 的sku数量',
    `return_spu_qty` INT(8) DEFAULT NULL COMMENT '售后SPU数;收货后售后 的spu数量',
    `return_branch_qty` INT(8) DEFAULT NULL COMMENT '售后门店数;收货后售后 的门店数量',
    `return_supplier_cost_amt` DECIMAL(12,2) DEFAULT NULL COMMENT '售后入驻商总成本金额;收货后售后订单的成本金额',
    `return_profit_amt` DECIMAL(12,2)   COMMENT '售后总分润金额; 收货后售后' ,
    `return_profit_partner_amt` DECIMAL(12,2)   COMMENT '售后平台商分润;收货后售后' ,
    `return_profit_dc_amt` DECIMAL(12,2)   COMMENT '售后运营商分润;收货后售后' ,
    `return_profit_colonel_amt` DECIMAL(12,2)   COMMENT '售后业务员分润;收货后售后' ,
    `return_profit_colonel1_amt` DECIMAL(12,2)   COMMENT '售后一级业务员分润;收货后售后' ,
    `return_profit_colonel2_amt` DECIMAL(12,2)   COMMENT '售后二级业务员分润;收货后售后' ,
    `pre_return_amt` DECIMAL(12,2)   COMMENT '原售后金额;pre_return_amt = return_amt + discount_amt' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)收货后售后' ,
    `return_discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;收货后售后' ,
    `return_profit_rate` DECIMAL(18,6)   COMMENT '售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100',
    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (month_id,sys_code,area_id,catgory1_id,catgory2_id,catgory3_id)
)  COMMENT = '交易域区域城市管理类别粒度下单月汇总表';


DROP TABLE IF EXISTS dws_trd_branch_category_sales_month;
CREATE TABLE dws_trd_branch_category_sales_month(
    `month_id` BIGINT(20) NOT NULL  COMMENT '日期ID' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域ID' ,
    `branch_id` BIGINT(20) NOT NULL  COMMENT '门店ID' ,
    `catgory1_id` BIGINT(20) NOT NULL  COMMENT '一级管理分类id' ,
    `catgory2_id` BIGINT(20) NOT NULL  COMMENT '二级管理分类id' ,
    `catgory3_id` BIGINT(20) NOT NULL  COMMENT '三级管理分类id' ,
    `order_amt` DECIMAL(12,2)   COMMENT '下单金额;订单成交金额（有效订单）' ,
    `order_qty` INT(8)   COMMENT '订单数量' ,
    `sales_qty` INT(8)   COMMENT '销量；有效订单金额>0 下单商品小单位总数量' ,
    `dx_sku_qty` INT(8)   COMMENT '动销SKU数;有效订单金额>0 的sku数量' ,
    `dx_spu_qty` INT(8)   COMMENT '动销SPU数;有效订单金额>0 的spu数量' ,
    `dx_branch_qty` INT(8)   COMMENT '动销门店数;有效订单金额>0 的门店数量' ,
    `supplier_cost_amt` DECIMAL(12,2) COMMENT '入驻商总成本金额;有效订单的成本金额',
    `profit_amt` DECIMAL(12,2)   COMMENT '总分润金额;有效订单的分润金额' ,
    `profit_partner_amt` DECIMAL(12,2)   COMMENT '平台商分润;有效订单的平台商分润金额' ,
    `profit_dc_amt` DECIMAL(12,2)   COMMENT '运营商分润;有效订单的运营商分润金额' ,
    `profit_colonel_amt` DECIMAL(12,2)   COMMENT '业务员分润;有效订单的业务员分润金额' ,
    `profit_colonel1_amt` DECIMAL(12,2)   COMMENT '一级业务员分润;有效订单的一级业务员分润' ,
    `profit_colonel2_amt` DECIMAL(12,2)   COMMENT '二级业务员分润;有效订单的二级业务员分润' ,
    `pre_order_amt` DECIMAL(12,2)   COMMENT '原订单金额;pre_order_amt = order_amt + discount_amt' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)（有效订单）' ,
    `discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;有效订单的订单优惠金额' ,
    `profit_rate` DECIMAL(18,6)   COMMENT '销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100' ,
    `return_amt` DECIMAL(12,2)   COMMENT '收货后售后金额（收货后售后订单）' ,
    `return_qty` INT(8)   COMMENT '退单数量;收货后售后' ,
    `return_sales_qty` INT(8)   COMMENT '售后数量；收货后售后 下单商品小单位总数量' ,
    `return_sku_qty` INT(8) DEFAULT NULL COMMENT '售后SKU数;收货后售后 的sku数量',
    `return_spu_qty` INT(8) DEFAULT NULL COMMENT '售后SPU数;收货后售后 的spu数量',
    `return_branch_qty` INT(8) DEFAULT NULL COMMENT '售后门店数;收货后售后 的门店数量',
    `return_supplier_cost_amt` DECIMAL(12,2) DEFAULT NULL COMMENT '售后入驻商总成本金额;收货后售后订单的成本金额',
    `return_profit_amt` DECIMAL(12,2)   COMMENT '售后总分润金额; 收货后售后' ,
    `return_profit_partner_amt` DECIMAL(12,2)   COMMENT '售后平台商分润;收货后售后' ,
    `return_profit_dc_amt` DECIMAL(12,2)   COMMENT '售后运营商分润;收货后售后' ,
    `return_profit_colonel_amt` DECIMAL(12,2)   COMMENT '售后业务员分润;收货后售后' ,
    `return_profit_colonel1_amt` DECIMAL(12,2)   COMMENT '售后一级业务员分润;收货后售后' ,
    `return_profit_colonel2_amt` DECIMAL(12,2)   COMMENT '售后二级业务员分润;收货后售后' ,
    `pre_return_amt` DECIMAL(12,2)   COMMENT '原售后金额;pre_return_amt = return_amt + discount_amt' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)收货后售后' ,
    `return_discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;收货后售后' ,
    `return_profit_rate` DECIMAL(18,6)   COMMENT '售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100',
    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (month_id,sys_code,area_id,branch_id,catgory1_id,catgory2_id,catgory3_id)
)  COMMENT = '交易域门店管理类别粒度下单月汇总表';


DROP TABLE IF EXISTS dws_trd_branch_sales_day;
CREATE TABLE dws_trd_branch_sales_day(
    `date_id` BIGINT(20) NOT NULL  COMMENT '日期ID' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域ID' ,
    `branch_id` BIGINT(20) NOT NULL  COMMENT '门店ID' ,
    `order_amt` DECIMAL(12,2)   COMMENT '下单金额;订单成交金额（有效订单）' ,
    `order_qty` INT(8)   COMMENT '订单数量' ,
    `dx_sku_qty` INT(8)   COMMENT '动销SKU数;有效订单金额>0 的sku数量' ,
    `dx_spu_qty` INT(8)   COMMENT '动销SPU数;有效订单金额>0 的spu数量' ,
    `dx_branch_qty` INT(8)   COMMENT '动销门店数;有效订单金额>0 的门店数量' ,
    `supplier_cost_amt` DECIMAL(12,2) COMMENT '入驻商总成本金额;有效订单的成本金额',
    `profit_amt` DECIMAL(12,2)   COMMENT '总分润金额;有效订单的分润金额' ,
    `profit_partner_amt` DECIMAL(12,2)   COMMENT '平台商分润;有效订单的平台商分润金额' ,
    `profit_dc_amt` DECIMAL(12,2)   COMMENT '运营商分润;有效订单的运营商分润金额' ,
    `profit_colonel_amt` DECIMAL(12,2)   COMMENT '业务员分润;有效订单的业务员分润金额' ,
    `profit_colonel1_amt` DECIMAL(12,2)   COMMENT '一级业务员分润;有效订单的一级业务员分润' ,
    `profit_colonel2_amt` DECIMAL(12,2)   COMMENT '二级业务员分润;有效订单的二级业务员分润' ,
    `pre_order_amt` DECIMAL(12,2)   COMMENT '原订单金额;pre_order_amt = order_amt + discount_amt' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)（有效订单）' ,
    `discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;有效订单的订单优惠金额' ,
    `profit_rate` DECIMAL(18,6)   COMMENT '销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100' ,
    `return_amt` DECIMAL(12,2)   COMMENT '收货后售后金额（收货后售后订单）' ,
    `return_qty` INT(8)   COMMENT '退单数量;收货后售后' ,
    `return_sku_qty` INT(8) DEFAULT NULL COMMENT '售后SKU数;收货后售后 的sku数量',
    `return_spu_qty` INT(8) DEFAULT NULL COMMENT '售后SPU数;收货后售后 的spu数量',
    `return_branch_qty` INT(8) DEFAULT NULL COMMENT '售后门店数;收货后售后 的门店数量',
    `return_supplier_cost_amt` DECIMAL(12,2) DEFAULT NULL COMMENT '售后入驻商总成本金额;收货后售后订单的成本金额',
    `return_profit_amt` DECIMAL(12,2)   COMMENT '售后总分润金额; 收货后售后' ,
    `return_profit_partner_amt` DECIMAL(12,2)   COMMENT '售后平台商分润;收货后售后' ,
    `return_profit_dc_amt` DECIMAL(12,2)   COMMENT '售后运营商分润;收货后售后' ,
    `return_profit_colonel_amt` DECIMAL(12,2)   COMMENT '售后业务员分润;收货后售后' ,
    `return_profit_colonel1_amt` DECIMAL(12,2)   COMMENT '售后一级业务员分润;收货后售后' ,
    `return_profit_colonel2_amt` DECIMAL(12,2)   COMMENT '售后二级业务员分润;收货后售后' ,
    `pre_return_amt` DECIMAL(12,2)   COMMENT '原售后金额;pre_return_amt = return_amt + discount_amt' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)收货后售后' ,
    `return_discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;收货后售后' ,
    `return_profit_rate` DECIMAL(18,6)   COMMENT '售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100',
    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (date_id,sys_code,area_id,branch_id)
)  COMMENT = '交易域门店粒度下单日汇总表';

DROP TABLE IF EXISTS dws_trd_branch_sales_month;
CREATE TABLE dws_trd_branch_sales_month(
    `month_id` BIGINT(20) NOT NULL  COMMENT '日期ID' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域ID' ,
    `branch_id` BIGINT(20) NOT NULL  COMMENT '门店ID' ,
    `order_amt` DECIMAL(12,2)   COMMENT '下单金额;订单成交金额（有效订单）' ,
    `order_qty` INT(8)   COMMENT '订单数量' ,
    `dx_sku_qty` INT(8)   COMMENT '动销SKU数;有效订单金额>0 的sku数量' ,
    `dx_spu_qty` INT(8)   COMMENT '动销SPU数;有效订单金额>0 的spu数量' ,
    `dx_branch_qty` INT(8)   COMMENT '动销门店数;有效订单金额>0 的门店数量' ,
    `supplier_cost_amt` DECIMAL(12,2) COMMENT '入驻商总成本金额;有效订单的成本金额',
    `profit_amt` DECIMAL(12,2)   COMMENT '总分润金额;有效订单的分润金额' ,
    `profit_partner_amt` DECIMAL(12,2)   COMMENT '平台商分润;有效订单的平台商分润金额' ,
    `profit_dc_amt` DECIMAL(12,2)   COMMENT '运营商分润;有效订单的运营商分润金额' ,
    `profit_colonel_amt` DECIMAL(12,2)   COMMENT '业务员分润;有效订单的业务员分润金额' ,
    `profit_colonel1_amt` DECIMAL(12,2)   COMMENT '一级业务员分润;有效订单的一级业务员分润' ,
    `profit_colonel2_amt` DECIMAL(12,2)   COMMENT '二级业务员分润;有效订单的二级业务员分润' ,
    `pre_order_amt` DECIMAL(12,2)   COMMENT '原订单金额;pre_order_amt = order_amt + discount_amt' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)（有效订单）' ,
    `discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;有效订单的订单优惠金额' ,
    `profit_rate` DECIMAL(18,6)   COMMENT '销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100' ,
    `dx_days` INT(8)   COMMENT '动销天数' ,
    `return_amt` DECIMAL(12,2)   COMMENT '收货后售后金额（收货后售后订单）' ,
    `return_qty` INT(8)   COMMENT '退单数量;收货后售后' ,
    `return_sku_qty` INT(8) DEFAULT NULL COMMENT '售后SKU数;收货后售后 的sku数量',
    `return_spu_qty` INT(8) DEFAULT NULL COMMENT '售后SPU数;收货后售后 的spu数量',
    `return_branch_qty` INT(8) DEFAULT NULL COMMENT '售后门店数;收货后售后 的门店数量',
    `return_supplier_cost_amt` DECIMAL(12,2) DEFAULT NULL COMMENT '售后入驻商总成本金额;收货后售后订单的成本金额',
    `return_profit_amt` DECIMAL(12,2)   COMMENT '售后总分润金额; 收货后售后' ,
    `return_profit_partner_amt` DECIMAL(12,2)   COMMENT '售后平台商分润;收货后售后' ,
    `return_profit_dc_amt` DECIMAL(12,2)   COMMENT '售后运营商分润;收货后售后' ,
    `return_profit_colonel_amt` DECIMAL(12,2)   COMMENT '售后业务员分润;收货后售后' ,
    `return_profit_colonel1_amt` DECIMAL(12,2)   COMMENT '售后一级业务员分润;收货后售后' ,
    `return_profit_colonel2_amt` DECIMAL(12,2)   COMMENT '售后二级业务员分润;收货后售后' ,
    `pre_return_amt` DECIMAL(12,2)   COMMENT '原售后金额;pre_return_amt = return_amt + discount_amt' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)收货后售后' ,
    `return_discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;收货后售后' ,
    `return_profit_rate` DECIMAL(18,6)   COMMENT '售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100',
    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (month_id,sys_code,area_id,branch_id)
)  COMMENT = '交易域门店粒度下单月汇总表';


DROP TABLE IF EXISTS dws_trd_area_hours_sales_month;
CREATE TABLE dws_trd_area_hours_sales_month(
`month_id` BIGINT(20) NOT NULL  COMMENT '月份ID' ,
`sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
`area_id` BIGINT(20) NOT NULL  COMMENT '区域城市id' ,
`hour` VARCHAR(12) NOT NULL  COMMENT '时段;格式为HH   如： 00 对应的区间为[00:00:00,  01:00:00)' ,
`order_amt` DECIMAL(12, 2)   COMMENT '下单金额' ,
`order_qty` INT(8)   COMMENT '订单数量' ,
`dx_sku_qty` INT(8)   COMMENT '动销SKU数' ,
`dx_spu_qty` INT(8)   COMMENT '动销SPU数' ,
`dx_branch_qty` INT(8)   COMMENT '动销门店数' ,
`return_amt` DECIMAL(12, 2)   COMMENT '收货后售后金额' ,
`return_qty` INT(8)   COMMENT '退单数量' ,
`return_sku_qty` INT(8)   COMMENT '售后SKU数' ,
`return_spu_qty` INT(8)   COMMENT '售后SPU数' ,
`return_branch_qty` INT(8)   COMMENT '售后门店数' ,
`insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
PRIMARY KEY (month_id,sys_code,area_id,hour)
)  COMMENT = '交易域区域城市粒度时段下单月汇总表';



DROP TABLE IF EXISTS dws_trd_branch_hours_sales_month;
CREATE TABLE dws_trd_branch_hours_sales_month(
`month_id` BIGINT(20) NOT NULL  COMMENT '月份ID' ,
`sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
`branch_id` BIGINT(20) NOT NULL  COMMENT '门店id' ,
`hour` VARCHAR(12) NOT NULL  COMMENT '时段;格式为HH   如： 00 对应的区间为[00:00:00,  01:00:00)' ,
`order_amt` DECIMAL(12, 2)   COMMENT '下单金额' ,
`order_qty` INT(8)   COMMENT '订单数量' ,
`dx_sku_qty` INT(8)   COMMENT '动销SKU数' ,
`dx_spu_qty` INT(8)   COMMENT '动销SPU数' ,
`return_amt` DECIMAL(12, 2)   COMMENT '收货后售后金额' ,
`return_qty` INT(8)   COMMENT '退单数量' ,
`return_sku_qty` INT(8)   COMMENT '售后SKU数' ,
`return_spu_qty` INT(8)   COMMENT '售后SPU数' ,
`insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
PRIMARY KEY (month_id,sys_code,branch_id,hour)
)  COMMENT = '交易域门店粒度时段下单月汇总表';


DROP TABLE IF EXISTS dws_trd_sku_sales_day;
CREATE TABLE dws_trd_sku_sales_day(
    `date_id` BIGINT(20) NOT NULL  COMMENT '日期ID' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域城市id' ,
    `sku_id` BIGINT(20) NOT NULL  COMMENT '商品sku id' ,
    `spu_id` BIGINT(20) NOT NULL  COMMENT '商品SPU id' ,
    `supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
    `order_amt` DECIMAL(12,2)   COMMENT '下单金额;订单成交金额（有效订单）' ,
    `order_qty` INT(8)   COMMENT '订单数量' ,
    `sku_sales_qty` INT(8)   COMMENT 'SKU最小单位销量' ,
    `dx_branch_qty` INT(8)   COMMENT '动销门店数;有效订单金额>0 的门店数量' ,
    `supplier_cost_amt` DECIMAL(12,2) COMMENT '入驻商总成本金额;有效订单的成本金额',
    `profit_amt` DECIMAL(12,2)   COMMENT '总分润金额;有效订单的分润金额' ,
    `profit_partner_amt` DECIMAL(12,2)   COMMENT '平台商分润;有效订单的平台商分润金额' ,
    `profit_dc_amt` DECIMAL(12,2)   COMMENT '运营商分润;有效订单的运营商分润金额' ,
    `profit_colonel_amt` DECIMAL(12,2)   COMMENT '业务员分润;有效订单的业务员分润金额' ,
    `profit_colonel1_amt` DECIMAL(12,2)   COMMENT '一级业务员分润;有效订单的一级业务员分润' ,
    `profit_colonel2_amt` DECIMAL(12,2)   COMMENT '二级业务员分润;有效订单的二级业务员分润' ,
    `pre_order_amt` DECIMAL(12,2)   COMMENT '原订单金额;pre_order_amt = order_amt + discount_amt' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)（有效订单）' ,
    `discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;有效订单的订单优惠金额' ,
    `profit_rate` DECIMAL(18,6)   COMMENT '销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100' ,
    `return_amt` DECIMAL(12,2)   COMMENT '收货后售后金额（收货后售后订单）' ,
    `return_qty` INT(8)   COMMENT '退单数量;收货后售后' ,
    `sku_return_qty` INT(8)   COMMENT '最小单位售后数量;收货后售后' ,
    `return_branch_qty` INT(8) DEFAULT NULL COMMENT '售后门店数;收货后售后 的门店数量',
    `return_supplier_cost_amt` DECIMAL(12,2) DEFAULT NULL COMMENT '售后入驻商总成本金额;收货后售后订单的成本金额',
    `return_profit_amt` DECIMAL(12,2)   COMMENT '售后总分润金额; 收货后售后' ,
    `return_profit_partner_amt` DECIMAL(12,2)   COMMENT '售后平台商分润;收货后售后' ,
    `return_profit_dc_amt` DECIMAL(12,2)   COMMENT '售后运营商分润;收货后售后' ,
    `return_profit_colonel_amt` DECIMAL(12,2)   COMMENT '售后业务员分润;收货后售后' ,
    `return_profit_colonel1_amt` DECIMAL(12,2)   COMMENT '售后一级业务员分润;收货后售后' ,
    `return_profit_colonel2_amt` DECIMAL(12,2)   COMMENT '售后二级业务员分润;收货后售后' ,
    `pre_return_amt` DECIMAL(12,2)   COMMENT '原售后金额;pre_return_amt = return_amt + discount_amt' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)收货后售后' ,
    `return_discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;收货后售后' ,
    `return_profit_rate` DECIMAL(18,6)   COMMENT '售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100',
    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (date_id,sys_code,area_id,sku_id,spu_id,supplier_id)
)  COMMENT = '交易域SKU粒度下单日汇总表';



DROP TABLE IF EXISTS dws_trd_sku_sales_month;
CREATE TABLE dws_trd_sku_sales_month(
    `month_id` BIGINT(20) NOT NULL  COMMENT '日期ID' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域城市id' ,
    `sku_id` BIGINT(20) NOT NULL  COMMENT '商品sku id' ,
    `spu_id` BIGINT(20) NOT NULL  COMMENT '商品SPU id' ,
    `supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
    `order_amt` DECIMAL(12,2)   COMMENT '下单金额;订单成交金额（有效订单）' ,
    `order_qty` INT(8)   COMMENT '订单数量' ,
    `sku_sales_qty` INT(8)   COMMENT 'SKU最小单位销量' ,
    `dx_branch_qty` INT(8)   COMMENT '动销门店数;有效订单金额>0 的门店数量' ,
    `supplier_cost_amt` DECIMAL(12,2) COMMENT '入驻商总成本金额;有效订单的成本金额',
    `profit_amt` DECIMAL(12,2)   COMMENT '总分润金额;有效订单的分润金额' ,
    `profit_partner_amt` DECIMAL(12,2)   COMMENT '平台商分润;有效订单的平台商分润金额' ,
    `profit_dc_amt` DECIMAL(12,2)   COMMENT '运营商分润;有效订单的运营商分润金额' ,
    `profit_colonel_amt` DECIMAL(12,2)   COMMENT '业务员分润;有效订单的业务员分润金额' ,
    `profit_colonel1_amt` DECIMAL(12,2)   COMMENT '一级业务员分润;有效订单的一级业务员分润' ,
    `profit_colonel2_amt` DECIMAL(12,2)   COMMENT '二级业务员分润;有效订单的二级业务员分润' ,
    `pre_order_amt` DECIMAL(12,2)   COMMENT '原订单金额;pre_order_amt = order_amt + discount_amt' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)（有效订单）' ,
    `discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;有效订单的订单优惠金额' ,
    `profit_rate` DECIMAL(18,6)   COMMENT '销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100' ,
    `return_amt` DECIMAL(12,2)   COMMENT '收货后售后金额（收货后售后订单）' ,
    `return_qty` INT(8)   COMMENT '退单数量;收货后售后' ,
    `sku_return_qty` INT(8)   COMMENT '最小单位售后数量;收货后售后' ,
    `return_branch_qty` INT(8) DEFAULT NULL COMMENT '售后门店数;收货后售后 的门店数量',
    `return_supplier_cost_amt` DECIMAL(12,2) DEFAULT NULL COMMENT '售后入驻商总成本金额;收货后售后订单的成本金额',
    `return_profit_amt` DECIMAL(12,2)   COMMENT '售后总分润金额; 收货后售后' ,
    `return_profit_partner_amt` DECIMAL(12,2)   COMMENT '售后平台商分润;收货后售后' ,
    `return_profit_dc_amt` DECIMAL(12,2)   COMMENT '售后运营商分润;收货后售后' ,
    `return_profit_colonel_amt` DECIMAL(12,2)   COMMENT '售后业务员分润;收货后售后' ,
    `return_profit_colonel1_amt` DECIMAL(12,2)   COMMENT '售后一级业务员分润;收货后售后' ,
    `return_profit_colonel2_amt` DECIMAL(12,2)   COMMENT '售后二级业务员分润;收货后售后' ,
    `pre_return_amt` DECIMAL(12,2)   COMMENT '原售后金额;pre_return_amt = return_amt + discount_amt' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)收货后售后' ,
    `return_discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;收货后售后' ,
    `return_profit_rate` DECIMAL(18,6)   COMMENT '售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100',
    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (month_id,sys_code,area_id,sku_id,spu_id,supplier_id)
)  COMMENT = '交易域SKU粒度下单月汇总表';

DROP TABLE IF EXISTS dws_trd_colonel_sales_day;
CREATE TABLE dws_trd_colonel_sales_day(
    `date_id` BIGINT(20) NOT NULL  COMMENT '日期ID' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域ID' ,
    `colonel_id` BIGINT(20) NOT NULL  COMMENT '业务员ID' ,
    `order_amt` DECIMAL(12,2)   COMMENT '下单金额;订单成交金额（有效订单）' ,
    `colonel_order_amt` DECIMAL(12,2)   COMMENT '业务员代客下单金额;订单成交金额（有效订单）' ,
    `branch_order_amt` DECIMAL(12,2)   COMMENT '门店自主下单金额;订单成交金额（有效订单）' ,
    `order_qty` INT(8)   COMMENT '订单数量' ,
    `colonel_order_qty` INT(8)   COMMENT '业务员代客下单订单数量' ,
    `branch_order_qty` INT(8)   COMMENT '门店自主下单订单数量' ,
    `dx_sku_qty` INT(8)   COMMENT '动销SKU数;有效订单金额>0 的sku数量' ,
    `dx_spu_qty` INT(8)   COMMENT '动销SPU数;有效订单金额>0 的spu数量' ,
    `dx_branch_qty` INT(8)   COMMENT '动销门店数;有效订单金额>0 的门店数量' ,
    `supplier_cost_amt` DECIMAL(12,2) COMMENT '入驻商总成本金额;有效订单的成本金额',
    `profit_amt` DECIMAL(12,2)   COMMENT '总分润金额;有效订单的分润金额' ,
    `profit_partner_amt` DECIMAL(12,2)   COMMENT '平台商分润;有效订单的平台商分润金额' ,
    `profit_dc_amt` DECIMAL(12,2)   COMMENT '运营商分润;有效订单的运营商分润金额' ,
    `profit_colonel_amt` DECIMAL(12,2)   COMMENT '业务员分润;有效订单的业务员分润金额' ,
    `profit_colonel1_amt` DECIMAL(12,2)   COMMENT '一级业务员分润;有效订单的一级业务员分润' ,
    `profit_colonel2_amt` DECIMAL(12,2)   COMMENT '二级业务员分润;有效订单的二级业务员分润' ,
    `pre_order_amt` DECIMAL(12,2)   COMMENT '原订单金额;pre_order_amt = order_amt + discount_amt' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)（有效订单）' ,
    `discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;有效订单的订单优惠金额' ,
    `profit_rate` DECIMAL(18,6)   COMMENT '销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100' ,
    `return_amt` DECIMAL(12,2)   COMMENT '收货后售后金额（收货后售后订单）' ,
    `return_qty` INT(8)   COMMENT '退单数量;收货后售后' ,
    `return_sku_qty` INT(8) DEFAULT NULL COMMENT '售后SKU数;收货后售后 的sku数量',
    `return_spu_qty` INT(8) DEFAULT NULL COMMENT '售后SPU数;收货后售后 的spu数量',
    `return_branch_qty` INT(8) DEFAULT NULL COMMENT '售后门店数;收货后售后 的门店数量',
    `return_supplier_cost_amt` DECIMAL(12,2) DEFAULT NULL COMMENT '售后入驻商总成本金额;收货后售后订单的成本金额',
    `return_profit_amt` DECIMAL(12,2)   COMMENT '售后总分润金额; 收货后售后' ,
    `return_profit_partner_amt` DECIMAL(12,2)   COMMENT '售后平台商分润;收货后售后' ,
    `return_profit_dc_amt` DECIMAL(12,2)   COMMENT '售后运营商分润;收货后售后' ,
    `return_profit_colonel_amt` DECIMAL(12,2)   COMMENT '售后业务员分润;收货后售后' ,
    `return_profit_colonel1_amt` DECIMAL(12,2)   COMMENT '售后一级业务员分润;收货后售后' ,
    `return_profit_colonel2_amt` DECIMAL(12,2)   COMMENT '售后二级业务员分润;收货后售后' ,
    `pre_return_amt` DECIMAL(12,2)   COMMENT '原售后金额;pre_return_amt = return_amt + discount_amt' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)收货后售后' ,
    `return_discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;收货后售后' ,
    `return_profit_rate` DECIMAL(18,6)   COMMENT '售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100',
    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (date_id,sys_code,area_id,colonel_id)
)  COMMENT = '交易域业务员粒度下单日汇总表';

DROP TABLE IF EXISTS dws_trd_colonel_sales_month;
CREATE TABLE dws_trd_colonel_sales_month(
    `month_id` BIGINT(20) NOT NULL  COMMENT '年月ID' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '区域ID' ,
    `colonel_id` BIGINT(20) NOT NULL  COMMENT '业务员ID' ,
    `order_amt` DECIMAL(12,2)   COMMENT '下单金额;订单成交金额（有效订单）' ,
    `colonel_order_amt` DECIMAL(12,2)   COMMENT '业务员代客下单金额;订单成交金额（有效订单）' ,
    `branch_order_amt` DECIMAL(12,2)   COMMENT '门店自主下单金额;订单成交金额（有效订单）' ,
    `order_qty` INT(8)   COMMENT '订单数量' ,
    `colonel_order_qty` INT(8)   COMMENT '业务员代客下单订单数量' ,
    `branch_order_qty` INT(8)   COMMENT '门店自主下单订单数量' ,
    `dx_sku_qty` INT(8)   COMMENT '动销SKU数;有效订单金额>0 的sku数量' ,
    `dx_spu_qty` INT(8)   COMMENT '动销SPU数;有效订单金额>0 的spu数量' ,
    `dx_branch_qty` INT(8)   COMMENT '动销门店数;有效订单金额>0 的门店数量' ,
    `supplier_cost_amt` DECIMAL(12,2) COMMENT '入驻商总成本金额;有效订单的成本金额',
    `profit_amt` DECIMAL(12,2)   COMMENT '总分润金额;有效订单的分润金额' ,
    `profit_partner_amt` DECIMAL(12,2)   COMMENT '平台商分润;有效订单的平台商分润金额' ,
    `profit_dc_amt` DECIMAL(12,2)   COMMENT '运营商分润;有效订单的运营商分润金额' ,
    `profit_colonel_amt` DECIMAL(12,2)   COMMENT '业务员分润;有效订单的业务员分润金额' ,
    `profit_colonel1_amt` DECIMAL(12,2)   COMMENT '一级业务员分润;有效订单的一级业务员分润' ,
    `profit_colonel2_amt` DECIMAL(12,2)   COMMENT '二级业务员分润;有效订单的二级业务员分润' ,
    `pre_order_amt` DECIMAL(12,2)   COMMENT '原订单金额;pre_order_amt = order_amt + discount_amt' ,
    `activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)（有效订单）' ,
    `coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)（有效订单）' ,
    `discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;有效订单的订单优惠金额' ,
    `profit_rate` DECIMAL(18,6)   COMMENT '销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100' ,
    `return_amt` DECIMAL(12,2)   COMMENT '收货后售后金额（收货后售后订单）' ,
    `return_qty` INT(8)   COMMENT '退单数量;收货后售后' ,
    `return_sku_qty` INT(8) DEFAULT NULL COMMENT '售后SKU数;收货后售后 的sku数量',
    `return_spu_qty` INT(8) DEFAULT NULL COMMENT '售后SPU数;收货后售后 的spu数量',
    `return_branch_qty` INT(8) DEFAULT NULL COMMENT '售后门店数;收货后售后 的门店数量',
    `return_supplier_cost_amt` DECIMAL(12,2) DEFAULT NULL COMMENT '售后入驻商总成本金额;收货后售后订单的成本金额',
    `return_profit_amt` DECIMAL(12,2)   COMMENT '售后总分润金额; 收货后售后' ,
    `return_profit_partner_amt` DECIMAL(12,2)   COMMENT '售后平台商分润;收货后售后' ,
    `return_profit_dc_amt` DECIMAL(12,2)   COMMENT '售后运营商分润;收货后售后' ,
    `return_profit_colonel_amt` DECIMAL(12,2)   COMMENT '售后业务员分润;收货后售后' ,
    `return_profit_colonel1_amt` DECIMAL(12,2)   COMMENT '售后一级业务员分润;收货后售后' ,
    `return_profit_colonel2_amt` DECIMAL(12,2)   COMMENT '售后二级业务员分润;收货后售后' ,
    `pre_return_amt` DECIMAL(12,2)   COMMENT '原售后金额;pre_return_amt = return_amt + discount_amt' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)收货后售后' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)收货后售后' ,
    `return_discount_amt` DECIMAL(12,2)   COMMENT '订单优惠金额;收货后售后' ,
    `return_profit_rate` DECIMAL(18,6)   COMMENT '售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100',
    `insert_date_id` BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (month_id,sys_code,area_id,colonel_id)
)  COMMENT = '交易域业务员粒度下单月汇总表';