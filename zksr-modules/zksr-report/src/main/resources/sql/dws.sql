-- replace into dws_trd_supplier_sales_day (
--     date_id,
--     sys_code,
--     supplier_id,
--     order_amt,
--     return_amt,
--     order_qty,
--     return_qty,
--     dx_sku_qty,
--     dx_spu_qty,
--     dx_branch_qty,
--     profit,
--     profit_partner,
--     profit_dc,
--     profit_colonel,
--     profit_colonel1,
--     profit_colonel2,
--     pre_order_amt,
--     discount_amt,
--     profit_rate
-- )
-- SELECT
--     dtodi.date_id as date_id,-- date_id,
--     dtodi.sys_code as sys_code,-- sys_code,
--     dtodi.supplier_id as supplier_id,-- supplier_id,
--     sum( dtodi.total_amt - ifnull(dtci.refund_amt,0)) as order_amt, --  order_amt(下单金额)
--     null as return_amt,-- return_amt (收货后售后金额)
--     count(dtodi.supplier_order_dtl_id) as order_qty,-- order_qty (订单数量)
--     null as return_qty,-- return_qty (退单数量)
--     count(DISTINCT dtodi.sku_id) as dx_sku_qty, -- dx_sku_qty (动销SKU数)
--     count(DISTINCT dtodi.spu_id) as dx_spu_qty, -- dx_spu_qty (动销SPU数)
--     count(DISTINCT dtodi.branch_id) as dx_branch_qty,-- dx_branch_qty (动销门店数)
--     sum(dtodi.profit - ifnull(profit,0)) as profit, -- profit (总分润金额)
--     sum(dtodi.partner_amt - ifnull(partner_amt,0)) as profit_partner,-- profit_partner (平台商分润)
--     sum(dtodi.dc_amt - ifnull(dtci.dc_amt,0)) as profit_dc,-- profit_dc (运营商分润)
--     sum(dtodi.colonel1_amt + dtodi.colonel2_amt - ifnull(dtci.colonel1_amt,0) - ifnull(dtci.colonel2_amt,0)) as profit_colonel,-- profit_colonel (业务员分润)
--     sum(dtodi.colonel1_amt - ifnull(dtci.colonel1_amt,0)) as profit_colone1,-- profit_colonel1 (一级业务员分润)
--     sum(dtodi.colonel2_amt - ifnull(dtci.colonel2_amt,0)) as profit_colone2,-- profit_colonel2 (二级业务员分润)
--     sum(dtodi.sub_order_amt - ifnull(dtci.return_amt,0)) as pre_order_amt,-- pre_order_amt (原订单金额)
--     sum(dtodi.activity_discount_amt + dtodi.coupon_discount_amt + dtodi.coupon_discount_amt2 - ifnull(dtci.activity_discount_amt,0) - ifnull(dtci.coupon_discount_amt,0) - ifnull(dtci.coupon_discount_amt2,0) ) as discount_amt,-- discount_amt (订单优惠金额)
--     sum(dtodi.profit - ifnull(profit,0))/sum( dtodi.total_amt - ifnull(dtci.refund_amt,0)) as profit_rate-- profit_rate (利润率)
-- FROM
--     dwd_trd_order_dtl_inc dtodi
--     LEFT JOIN (
--         SELECT
--             supplier_order_dtl_id supplier_order_dtl_id,
--             sum( return_amt ) return_amt,
--             sum( refund_amt ) refund_amt,
--             sum( cancel_qty ) cancel_qty,
--             sum( dc_amt ) dc_amt,
--             sum( colonel1_amt ) colonel1_amt,
--             sum( colonel2_amt ) colonel2_amt,
--             sum( activity_discount_amt ) activity_discount_amt,
--             sum( coupon_discount_amt ) coupon_discount_amt,
--             sum( coupon_discount_amt2 ) coupon_discount_amt2
--         FROM
--             dwd_trd_cancel_inc
--         WHERE
--             order_date_id in (
--                 SELECT
--                     orderdate.order_date_id as order_date_id
--                 FROM
--                     (
--                         SELECT date_id as order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY date_id
--                         union
--                         SELECT order_date_id as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
--                     ) orderdate
--             )
--         GROUP BY
--             supplier_order_dtl_id
--         ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
-- WHERE
--     dtodi.date_id in (
--         SELECT
--             orderdate.order_date_id as order_date_id
--         FROM
--             (
--                 SELECT date_id as order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY date_id
--                 union
--                 SELECT order_date_id as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
--             ) orderdate
--     )
--     and  (dtodi.total_num - dtci.cancel_qty) > 0
-- GROUP BY
--     dtodi.date_id,
--     dtodi.supplier_id,
--     dtodi.sys_code;
--
--
-- replace into dws_trd_supplier_sales_month (
--     month_id,
--     sys_code,
--     supplier_id,
--     order_amt,
--     return_amt,
--     order_qty,
--     return_qty,
--     dx_sku_qty,
--     dx_spu_qty,
--     dx_branch_qty,
--     profit,
--     profit_partner,
--     profit_dc,
--     profit_colonel,
--     profit_colonel1,
--     profit_colonel2,
--     pre_order_amt,
--     discount_amt,
--     profit_rate
-- )
-- SELECT
--     SUBSTRING( dtodi.date_id, 1, 6 ) as month_id,-- date_id,
--     dtodi.sys_code as sys_code,-- sys_code,
--     dtodi.supplier_id as supplier_id,-- supplier_id,
--     sum( dtodi.total_amt - ifnull(dtci.refund_amt,0)) as order_amt, --  order_amt(下单金额)
--     null as return_amt,-- return_amt (收货后售后金额)
--     count(dtodi.supplier_order_dtl_id) as order_qty,-- order_qty (订单数量)
--     null as return_qty,-- return_qty (退单数量)
--     count(DISTINCT dtodi.sku_id) as dx_sku_qty, -- dx_sku_qty (动销SKU数)
--     count(DISTINCT dtodi.spu_id) as dx_spu_qty, -- dx_spu_qty (动销SPU数)
--     count(DISTINCT dtodi.branch_id) as dx_branch_qty,-- dx_branch_qty (动销门店数)
--     sum(dtodi.profit - ifnull(profit,0)) as profit, -- profit (总分润金额)
--     sum(dtodi.partner_amt - ifnull(partner_amt,0)) as profit_partner,-- profit_partner (平台商分润)
--     sum(dtodi.dc_amt - ifnull(dtci.dc_amt,0)) as profit_dc,-- profit_dc (运营商分润)
--     sum(dtodi.colonel1_amt + dtodi.colonel2_amt - ifnull(dtci.colonel1_amt,0) - ifnull(dtci.colonel2_amt,0)) as profit_colonel,-- profit_colonel (业务员分润)
--     sum(dtodi.colonel1_amt - ifnull(dtci.colonel1_amt,0)) as profit_colone1,-- profit_colonel1 (一级业务员分润)
--     sum(dtodi.colonel2_amt - ifnull(dtci.colonel2_amt,0)) as profit_colone2,-- profit_colonel2 (二级业务员分润)
--     sum(dtodi.sub_order_amt - ifnull(dtci.return_amt,0)) as pre_order_amt,-- pre_order_amt (原订单金额)
--     sum(dtodi.activity_discount_amt + dtodi.coupon_discount_amt + dtodi.coupon_discount_amt2 - ifnull(dtci.activity_discount_amt,0) - ifnull(dtci.coupon_discount_amt,0) - ifnull(dtci.coupon_discount_amt2,0) ) as discount_amt,-- discount_amt (订单优惠金额)
--     sum(dtodi.profit - ifnull(profit,0))/sum( dtodi.total_amt - ifnull(dtci.refund_amt,0)) as profit_rate-- profit_rate (利润率)
-- FROM
--     dwd_trd_order_dtl_inc dtodi
--         LEFT JOIN (
--         SELECT
--             supplier_order_dtl_id supplier_order_dtl_id,
--             sum( return_amt ) return_amt,
--             sum( refund_amt ) refund_amt,
--             sum( cancel_qty ) cancel_qty,
--             sum( dc_amt ) dc_amt,
--             sum( colonel1_amt ) colonel1_amt,
--             sum( colonel2_amt ) colonel2_amt,
--             sum( activity_discount_amt ) activity_discount_amt,
--             sum( coupon_discount_amt ) coupon_discount_amt,
--             sum( coupon_discount_amt2 ) coupon_discount_amt2
--         FROM
--             dwd_trd_cancel_inc
--         WHERE
--             SUBSTRING( order_date_id, 1, 6 ) in (
--                 SELECT
--                     ordermonth.order_month_id as order_month_id
--                 FROM
--                     (
--                         SELECT SUBSTRING( date_id, 1, 6 ) as order_month_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY date_id
--                         union
--                         SELECT SUBSTRING( order_date_id, 1, 6 ) as order_month_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
--                     )ordermonth
--             )
--         GROUP BY
--             supplier_order_dtl_id
--     ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
-- WHERE
--     SUBSTRING( dtodi.date_id, 1, 6 ) in (
--         SELECT
--             ordermonth.order_month_id as order_month_id
--         FROM
--             (
--                 SELECT SUBSTRING( date_id, 1, 6 ) as order_month_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY date_id
--                 union
--                 SELECT SUBSTRING( order_date_id, 1, 6 ) as order_month_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
--             ) ordermonth
--     )
--   and  (dtodi.total_num - dtci.cancel_qty) > 0
-- GROUP BY
--     SUBSTRING( dtodi.date_id, 1, 6 ),
--     dtodi.supplier_id,
--     dtodi.sys_code;



--===============================================================dws_trd_area_sales_day（区域销售日结）===================================================================================
replace into dws_trd_area_sales_day (
    date_id
    , sys_code
    , area_id
    , order_amt
    , order_qty
    , dx_sku_qty
    , dx_spu_qty
    , dx_branch_qty
    , supplier_cost_amt
    , profit_amt
    , profit_partner_amt
    , profit_dc_amt
    , profit_colonel_amt
    , profit_colonel1_amt
    , profit_colonel2_amt
    , pre_order_amt
    , activity_discount_amt
    , coupon_discount_amt
    , coupon_discount_amt2
    , discount_amt
    , profit_rate
    , return_amt
    , return_qty
    , return_sku_qty
    , return_spu_qty
    , return_branch_qty
    , return_supplier_cost_amt
    , return_profit_amt
    , return_profit_partner_amt
    , return_profit_dc_amt
    , return_profit_colonel_amt
    , return_profit_colonel1_amt
    , return_profit_colonel2_amt
    , pre_return_amt
    , return_activity_discount_amt
    , return_coupon_discount_amt
    , return_coupon_discount_amt2
    , return_discount_amt
    , return_profit_rate
    , insert_date_id
)
SELECT
    updateTable.date_id,
    updateTable.sys_code,
    updateTable.area_id,
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.sku_num) AS dx_sku_qty,
    SUM(updateTable.spu_num) AS dx_spu_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.supplier_amt) AS supplier_cost_amt,
    SUM(updateTable.profit_amt) AS profit_amt,
    SUM(updateTable.partner_amt) AS profit_partner_amt,
    SUM(updateTable.dc_amt) AS profit_dc_amt,
    SUM(updateTable.colonel1_amt + updateTable.colonel2_amt) AS profit_colonel_amt,
    SUM(updateTable.colonel1_amt) AS profit_colonel1_amt,
    SUM(updateTable.colonel2_amt) AS profit_colonel2_amt,
    SUM(updateTable.pre_order_amt) AS pre_order_amt,
    SUM(updateTable.activity_discount_amt) AS activity_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt2,
    SUM(updateTable.activity_discount_amt + updateTable.coupon_discount_amt + updateTable.coupon_discount_amt2) AS discount_amt,
    CASE
        WHEN SUM(updateTable.order_amt) <= 0 THEN 0
        ELSE  ROUND(IFNULL((SUM(updateTable.order_amt) - SUM(updateTable.supplier_amt)) / SUM(updateTable.order_amt) * 100, 0), 6)
    END AS profit_rate,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.after_num) AS return_qty,
    SUM(updateTable.return_sku_num) AS return_sku_qty,
    SUM(updateTable.return_spu_num) AS return_spu_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    SUM(updateTable.return_supplier_amt) AS return_supplier_cost_amt,
    SUM(updateTable.return_profit) AS return_profit,
    SUM(updateTable.return_profit_partner) AS return_profit_partner_amt,
    SUM(updateTable.return_profit_dc) AS return_profit_dc_amt,
    SUM(updateTable.return_profit_colonel1 + updateTable.return_profit_colonel2) AS return_profit_colonel_amt,
    SUM(updateTable.return_profit_colonel1) AS return_profit_colonel1_amt,
    SUM(updateTable.return_profit_colonel2) AS return_profit_colonel2_amt,
    SUM(updateTable.pre_return_amt) AS pre_return_amt,
    SUM(updateTable.return_activity_discount_amt) AS return_activity_discount_amt,
    SUM(updateTable.return_coupon_discount_amt) AS return_coupon_discount_amt,
    SUM(updateTable.return_coupon_discount_amt2) AS return_coupon_discount_amt2,
    SUM(updateTable.return_activity_discount_amt + updateTable.return_coupon_discount_amt + updateTable.return_coupon_discount_amt2) AS return_discount_amt,
    CASE
        WHEN SUM(updateTable.return_amt) <= 0 THEN 0
        ELSE ROUND(IFNULL((SUM(updateTable.return_amt) - SUM(updateTable.return_supplier_amt)) / SUM(updateTable.return_amt) * 100, 0), 6)
    END AS return_profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            dtodi.date_id, -- 日期ID
            dtodi.sys_code, -- 平台商编号
            dtodi.area_id, -- 区域ID
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            COUNT(DISTINCT dtodi.sku_id) AS sku_num, -- SKU数量
            COUNT(DISTINCT dtodi.spu_id) AS spu_num, -- SPU数量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            IFNULL(SUM(dtodi.supplier_amt) - SUM(IFNULL(dtci.supplier_amt, 0)), 0) AS supplier_amt, -- 入驻商成本金额
            IFNULL(SUM(dtodi.profit) - SUM(IFNULL(dtci.profit, 0)), 0) AS profit_amt, -- 利润金额（减去发货前售后）
            IFNULL(SUM(dtodi.partner_amt) - SUM(IFNULL(dtci.partner_amt, 0)), 0) AS partner_amt, -- 平台商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.dc_amt) - SUM(IFNULL(dtci.dc_amt, 0)), 0) AS dc_amt, -- 运营商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel1_amt) - SUM(IFNULL(dtci.colonel1_amt, 0)), 0) AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel2_amt) - SUM(IFNULL(dtci.colonel2_amt, 0)), 0) AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.order_amt) - SUM(IFNULL(dtci.cancel_amt, 0)), 0) AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            IFNULL(SUM(dtodi.activity_discount_amt) - SUM(IFNULL(dtci.cancel_activity_discount_amt, 0)), 0) AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt) - SUM(IFNULL(dtci.cancel_coupon_discount_amt, 0)), 0) AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt2) - SUM(IFNULL(dtci.cancel_coupon_discount_amt2, 0)), 0) AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS after_num, -- 售后单数量（收货后）
            0 AS return_sku_num, -- 售后SKU数量（收货后）
            0 AS return_spu_num, -- 售后SPU数量（收货后）
            0 AS return_branch_num, -- 售后门店数量（收货后）
            0 AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            0 AS return_profit, -- 售后利润（收货后）
            0 AS return_profit_partner, -- 平台商售后分润金额（收货后）
            0 AS return_profit_dc, -- 运营商售后分润金额（收货后）
            0 AS return_profit_colonel1, -- 业务员管理员售后分润金额（收货后）
            0 AS return_profit_colonel2, -- 业务员售后分润金额（收货后）
            0 AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            0 AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            0 AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            0 AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    sum( return_amt ) return_amt,
                    sum( refund_amt ) refund_amt,
                    sum( cancel_amt ) cancel_amt,
                    sum( cancel_qty ) cancel_qty,
                    sum( supplier_amt ) supplier_amt,
                    sum( profit ) profit,
                    sum( partner_amt ) partner_amt,
                    sum( dc_amt ) dc_amt,
                    sum( colonel1_amt ) colonel1_amt,
                    sum( colonel2_amt ) colonel2_amt,
                    sum( cancel_activity_discount_amt ) cancel_activity_discount_amt,
                    sum( cancel_coupon_discount_amt ) cancel_coupon_discount_amt,
                    sum( cancel_coupon_discount_amt2 ) cancel_coupon_discount_amt2
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    order_date_id in (
                        SELECT order_date_id as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    )
                GROUP BY
                    supplier_order_dtl_id

            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            dtodi.date_id in (
                SELECT
                    orderdate.order_date_id as order_date_id
                FROM
                    (
                        SELECT date_id as order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY date_id
                        union
                        SELECT order_date_id as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            dtodi.date_id,
            dtodi.sys_code,
            dtodi.area_id

        UNION ALL

        SELECT
            dtri.date_id, -- 日期ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS order_num, -- 订单数量
            0 AS sku_num, -- SKU数量
            0 AS spu_num, -- SPU数量
            0 AS branch_num, -- 门店数量
            0 AS supplier_amt, -- 入驻商成本金额
            0 AS profit_amt, -- 利润金额（减去发货前售后）
            0 AS partner_amt, -- 平台上分润金额（减去发货前售后）
            0 AS dc_amt, -- 运营上分润金额（减去发货前售后）
            0 AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            0 AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            0 AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            0 AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            0 AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            0 AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS after_num, -- 退单数量（收货后）
            COUNT(DISTINCT dtri.sku_id) AS return_sku_num, -- 售后SKU数量（收货后）
            COUNT(DISTINCT dtri.spu_id) AS return_spu_num, -- 售后SPU数量（收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num, -- 售后门店数量（收货后）
            IFNULL(SUM(dtri.supplier_amt), 0)AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            IFNULL(SUM(dtri.profit), 0) AS return_profit, -- 售后利润（收货后）
            IFNULL(SUM(dtri.partner_amt), 0) AS return_profit_partner_amt, -- 平台商售后分润金额（收货后）
            IFNULL(SUM(dtri.dc_amt), 0) AS return_profit_dc_amt, -- 运营商售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel1_amt), 0) AS return_profit_colonel1_amt, -- 业务员管理员售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel2_amt), 0) AS return_profit_colonel2_amt, -- 业务员售后分润金额（收货后）
            SUM(dtri.return_order_amt) AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            IFNULL(SUM(dtri.return_activity_discount_amt), 0) AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt), 0) AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt2), 0) AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_return_inc dtri
        WHERE
            order_date_id in (
                SELECT order_date_id FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
            )
        GROUP BY
            dtri.date_id, -- 日期ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id -- 区域ID

    ) updateTable
GROUP BY
    updateTable.date_id,
    updateTable.sys_code,
    updateTable.area_id

-- ===================================================dws_trd_area_sales_month（区域销售月结）=================================================================
replace into dws_trd_area_sales_month (
    month_id
    , sys_code
    , area_id
    , order_amt
    , order_qty
    , dx_sku_qty
    , dx_spu_qty
    , dx_branch_qty
    , supplier_cost_amt
    , profit_amt
    , profit_partner_amt
    , profit_dc_amt
    , profit_colonel_amt
    , profit_colonel1_amt
    , profit_colonel2_amt
    , pre_order_amt
    , activity_discount_amt
    , coupon_discount_amt
    , coupon_discount_amt2
    , discount_amt
    , profit_rate
    , return_amt
    , return_qty
    , return_sku_qty
    , return_spu_qty
    , return_branch_qty
    , return_supplier_cost_amt
    , return_profit_amt
    , return_profit_partner_amt
    , return_profit_dc_amt
    , return_profit_colonel_amt
    , return_profit_colonel1_amt
    , return_profit_colonel2_amt
    , pre_return_amt
    , return_activity_discount_amt
    , return_coupon_discount_amt
    , return_coupon_discount_amt2
    , return_discount_amt
    , return_profit_rate
    , insert_date_id
)
SELECT
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.sku_num) AS dx_sku_qty,
    SUM(updateTable.spu_num) AS dx_spu_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.supplier_amt) AS supplier_cost_amt,
    SUM(updateTable.profit_amt) AS profit_amt,
    SUM(updateTable.partner_amt) AS profit_partner_amt,
    SUM(updateTable.dc_amt) AS profit_dc_amt,
    SUM(updateTable.colonel1_amt + updateTable.colonel2_amt) AS profit_colonel_amt,
    SUM(updateTable.colonel1_amt) AS profit_colonel1_amt,
    SUM(updateTable.colonel2_amt) AS profit_colonel2_amt,
    SUM(updateTable.pre_order_amt) AS pre_order_amt,
    SUM(updateTable.activity_discount_amt) AS activity_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt2,
    SUM(updateTable.activity_discount_amt + updateTable.coupon_discount_amt + updateTable.coupon_discount_amt2) AS discount_amt,
    CASE
        WHEN SUM(updateTable.order_amt) <= 0 THEN 0
        ELSE  ROUND(IFNULL((SUM(updateTable.order_amt) - SUM(updateTable.supplier_amt)) / SUM(updateTable.order_amt) * 100, 0), 6)
    END AS profit_rate,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.after_num) AS return_qty,
    SUM(updateTable.return_sku_num) AS return_sku_qty,
    SUM(updateTable.return_spu_num) AS return_spu_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    SUM(updateTable.return_supplier_amt) AS return_supplier_cost_amt,
    SUM(updateTable.return_profit) AS return_profit,
    SUM(updateTable.return_profit_partner) AS return_profit_partner_amt,
    SUM(updateTable.return_profit_dc) AS return_profit_dc_amt,
    SUM(updateTable.return_profit_colonel1 + updateTable.return_profit_colonel2) AS return_profit_colonel_amt,
    SUM(updateTable.return_profit_colonel1) AS return_profit_colonel1_amt,
    SUM(updateTable.return_profit_colonel2) AS return_profit_colonel2_amt,
    SUM(updateTable.pre_return_amt) AS pre_return_amt,
    SUM(updateTable.return_activity_discount_amt) AS return_activity_discount_amt,
    SUM(updateTable.return_coupon_discount_amt) AS return_coupon_discount_amt,
    SUM(updateTable.return_coupon_discount_amt2) AS return_coupon_discount_amt2,
    SUM(updateTable.return_activity_discount_amt + updateTable.return_coupon_discount_amt + updateTable.return_coupon_discount_amt2) AS return_discount_amt,
    CASE
        WHEN SUM(updateTable.return_amt) <= 0 THEN 0
        ELSE ROUND(IFNULL((SUM(updateTable.return_amt) - SUM(updateTable.return_supplier_amt)) / SUM(updateTable.return_amt) * 100, 0), 6)
    END AS return_profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            SUBSTRING( dtodi.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtodi.sys_code, -- 平台商编号
            dtodi.area_id, -- 区域ID
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            COUNT(DISTINCT dtodi.sku_id) AS sku_num, -- SKU数量
            COUNT(DISTINCT dtodi.spu_id) AS spu_num, -- SPU数量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            IFNULL(SUM(dtodi.supplier_amt) - SUM(IFNULL(dtci.supplier_amt, 0)), 0) AS supplier_amt, -- 入驻商成本金额
            IFNULL(SUM(dtodi.profit) - SUM(IFNULL(dtci.profit, 0)), 0) AS profit_amt, -- 利润金额（减去发货前售后）
            IFNULL(SUM(dtodi.partner_amt) - SUM(IFNULL(dtci.partner_amt, 0)), 0) AS partner_amt, -- 平台商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.dc_amt) - SUM(IFNULL(dtci.dc_amt, 0)), 0) AS dc_amt, -- 运营商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel1_amt) - SUM(IFNULL(dtci.colonel1_amt, 0)), 0) AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel2_amt) - SUM(IFNULL(dtci.colonel2_amt, 0)), 0) AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.order_amt) - SUM(IFNULL(dtci.cancel_amt, 0)), 0) AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            IFNULL(SUM(dtodi.activity_discount_amt) - SUM(IFNULL(dtci.cancel_activity_discount_amt, 0)), 0) AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt) - SUM(IFNULL(dtci.cancel_coupon_discount_amt, 0)), 0) AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt2) - SUM(IFNULL(dtci.cancel_coupon_discount_amt2, 0)), 0) AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS after_num, -- 售后单数量（收货后）
            0 AS return_sku_num, -- 售后SKU数量（收货后）
            0 AS return_spu_num, -- 售后SPU数量（收货后）
            0 AS return_branch_num, -- 售后门店数量（收货后）
            0 AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            0 AS return_profit, -- 售后利润（收货后）
            0 AS return_profit_partner, -- 平台商售后分润金额（收货后）
            0 AS return_profit_dc, -- 运营商售后分润金额（收货后）
            0 AS return_profit_colonel1, -- 业务员管理员售后分润金额（收货后）
            0 AS return_profit_colonel2, -- 业务员售后分润金额（收货后）
            0 AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            0 AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            0 AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            0 AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    SUM( return_amt ) return_amt,
                    SUM( refund_amt ) refund_amt,
                    SUM( cancel_amt ) cancel_amt,
                    SUM( cancel_qty ) cancel_qty,
                    SUM( supplier_amt ) supplier_amt,
                    SUM( profit ) profit,
                    SUM( partner_amt ) partner_amt,
                    SUM( dc_amt ) dc_amt,
                    SUM( colonel1_amt ) colonel1_amt,
                    SUM( colonel2_amt ) colonel2_amt,
                    SUM( cancel_activity_discount_amt ) cancel_activity_discount_amt,
                    SUM( cancel_coupon_discount_amt ) cancel_coupon_discount_amt,
                    SUM( cancel_coupon_discount_amt2 ) cancel_coupon_discount_amt2
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    SUBSTRING( order_date_id , 1, 6 ) in (
                        SELECT SUBSTRING( order_date_id , 1, 6 ) FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            SUBSTRING( dtodi.date_id , 1, 6 ) in (
                SELECT
                    orderdate.order_date_id
                FROM
                    (
                        SELECT SUBSTRING( date_id , 1, 6 )  AS order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id , 1, 6 )
                        UNION
                        SELECT SUBSTRING( order_date_id , 1, 6 )  AS order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            SUBSTRING( dtodi.date_id , 1, 6 ), -- 年月ID
            dtodi.sys_code,
            dtodi.area_id

        UNION ALL

        SELECT
            SUBSTRING( dtri.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS order_num, -- 订单数量
            0 AS sku_num, -- SKU数量
            0 AS spu_num, -- SPU数量
            0 AS branch_num, -- 门店数量
            0 AS supplier_amt, -- 入驻商成本金额
            0 AS profit_amt, -- 利润金额（减去发货前售后）
            0 AS partner_amt, -- 平台上分润金额（减去发货前售后）
            0 AS dc_amt, -- 运营上分润金额（减去发货前售后）
            0 AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            0 AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            0 AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            0 AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            0 AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            0 AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS after_num, -- 退单数量（收货后）
            COUNT(DISTINCT dtri.sku_id) AS return_sku_num, -- 售后SKU数量（收货后）
            COUNT(DISTINCT dtri.spu_id) AS return_spu_num, -- 售后SPU数量（收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num, -- 售后门店数量（收货后）
            IFNULL(SUM(dtri.supplier_amt), 0)AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            IFNULL(SUM(dtri.profit), 0) AS return_profit, -- 售后利润（收货后）
            IFNULL(SUM(dtri.partner_amt), 0) AS return_profit_partner_amt, -- 平台商售后分润金额（收货后）
            IFNULL(SUM(dtri.dc_amt), 0) AS return_profit_dc_amt, -- 运营商售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel1_amt), 0) AS return_profit_colonel1_amt, -- 业务员管理员售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel2_amt), 0) AS return_profit_colonel2_amt, -- 业务员售后分润金额（收货后）
            SUM(dtri.return_order_amt) AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            IFNULL(SUM(dtri.return_activity_discount_amt), 0) AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt), 0) AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt2), 0) AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_return_inc dtri
        WHERE
            SUBSTRING( date_id, 1, 6 ) in (
                SELECT SUBSTRING( date_id, 1, 6 )  FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id, 1, 6 )
            )
        GROUP BY
            SUBSTRING( dtri.date_id , 1, 6 ), -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id -- 区域ID
    ) updateTable
GROUP BY
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id

-- ======================================================dws_trd_supplier_sales_day（入驻商销售日结）===============================================
replace into dws_trd_supplier_sales_day (
    date_id
    , sys_code
    , supplier_id
    , area_id
    , order_amt
    , order_qty
    , dx_sku_qty
    , dx_spu_qty
    , dx_branch_qty
    , supplier_cost_amt
    , profit_amt
    , profit_partner_amt
    , profit_dc_amt
    , profit_colonel_amt
    , profit_colonel1_amt
    , profit_colonel2_amt
    , pre_order_amt
    , activity_discount_amt
    , coupon_discount_amt
    , coupon_discount_amt2
    , discount_amt
    , profit_rate
    , return_amt
    , return_qty
    , return_sku_qty
    , return_spu_qty
    , return_branch_qty
    , return_supplier_cost_amt
    , return_profit_amt
    , return_profit_partner_amt
    , return_profit_dc_amt
    , return_profit_colonel_amt
    , return_profit_colonel1_amt
    , return_profit_colonel2_amt
    , pre_return_amt
    , return_activity_discount_amt
    , return_coupon_discount_amt
    , return_coupon_discount_amt2
    , return_discount_amt
    , return_profit_rate
    , insert_date_id
)
SELECT
    updateTable.date_id,
    updateTable.sys_code,
    updateTable.supplier_id,
    updateTable.area_id,
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.sku_num) AS dx_sku_qty,
    SUM(updateTable.spu_num) AS dx_spu_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.supplier_amt) AS supplier_cost_amt,
    SUM(updateTable.profit_amt) AS profit_amt,
    SUM(updateTable.partner_amt) AS profit_partner_amt,
    SUM(updateTable.dc_amt) AS profit_dc_amt,
    SUM(updateTable.colonel1_amt + updateTable.colonel2_amt) AS profit_colonel_amt,
    SUM(updateTable.colonel1_amt) AS profit_colonel1_amt,
    SUM(updateTable.colonel2_amt) AS profit_colonel2_amt,
    SUM(updateTable.pre_order_amt) AS pre_order_amt,
    SUM(updateTable.activity_discount_amt) AS activity_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt2,
    SUM(updateTable.activity_discount_amt + updateTable.coupon_discount_amt + updateTable.coupon_discount_amt2) AS discount_amt,
    CASE
        WHEN SUM(updateTable.order_amt) <= 0 THEN 0
        ELSE ROUND(SUM(updateTable.profit_amt) / SUM(updateTable.order_amt), 6)
    END AS profit_rate,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.after_num) AS return_qty,
    SUM(updateTable.return_sku_num) AS return_sku_qty,
    SUM(updateTable.return_spu_num) AS return_spu_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    SUM(updateTable.return_supplier_amt) AS return_supplier_cost_amt,
    SUM(updateTable.return_profit) AS return_profit,
    SUM(updateTable.return_profit_partner) AS return_profit_partner_amt,
    SUM(updateTable.return_profit_dc) AS return_profit_dc_amt,
    SUM(updateTable.return_profit_colonel1 + updateTable.return_profit_colonel2) AS return_profit_colonel_amt,
    SUM(updateTable.return_profit_colonel1) AS return_profit_colonel1_amt,
    SUM(updateTable.return_profit_colonel2) AS return_profit_colonel2_amt,
    SUM(updateTable.pre_return_amt) AS pre_return_amt,
    SUM(updateTable.return_activity_discount_amt) AS return_activity_discount_amt,
    SUM(updateTable.return_coupon_discount_amt) AS return_coupon_discount_amt,
    SUM(updateTable.return_coupon_discount_amt2) AS return_coupon_discount_amt2,
    SUM(updateTable.return_activity_discount_amt + updateTable.return_coupon_discount_amt + updateTable.return_coupon_discount_amt2) AS return_discount_amt,
    CASE
        WHEN SUM(updateTable.return_amt) <= 0 THEN 0
        ELSE ROUND(SUM(updateTable.return_profit) / SUM(updateTable.return_amt), 6)
    END AS return_profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            dtodi.date_id, -- 日期ID
            dtodi.sys_code, -- 平台商编号
            dtodi.supplier_id, -- 入驻商ID
            dtodi.area_id, -- 区域ID
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt,0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            COUNT(DISTINCT dtodi.sku_id) AS sku_num, -- SKU数量
            COUNT(DISTINCT dtodi.spu_id) AS spu_num, -- SPU数量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            IFNULL(SUM(dtodi.supplier_amt) - SUM(IFNULL(dtci.supplier_amt, 0)), 0) AS supplier_amt, -- 入驻商成本金额
            IFNULL(SUM(dtodi.profit) - SUM(IFNULL(dtci.profit, 0)), 0) AS profit_amt, -- 利润金额（减去发货前售后）
            IFNULL(SUM(dtodi.partner_amt) - SUM(IFNULL(dtci.partner_amt, 0)), 0) AS partner_amt, -- 平台商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.dc_amt) - SUM(IFNULL(dtci.dc_amt, 0)), 0) AS dc_amt, -- 运营商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel1_amt) - SUM(IFNULL(dtci.colonel1_amt, 0)), 0) AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel2_amt) - SUM(IFNULL(dtci.colonel2_amt, 0)), 0) AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.order_amt) - SUM(IFNULL(dtci.cancel_amt, 0)), 0) AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            IFNULL(SUM(dtodi.activity_discount_amt) - SUM(IFNULL(dtci.cancel_activity_discount_amt, 0)), 0) AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt) - SUM(IFNULL(dtci.cancel_coupon_discount_amt, 0)), 0) AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt2) - SUM(IFNULL(dtci.cancel_coupon_discount_amt2, 0)), 0) AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS after_num, -- 售后单数量（收货后）
            0 AS return_sku_num, -- 售后SKU数量（收货后）
            0 AS return_spu_num, -- 售后SPU数量（收货后）
            0 AS return_branch_num, -- 售后门店数量（收货后）
            0 AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            0 AS return_profit, -- 售后利润（收货后）
            0 AS return_profit_partner, -- 平台商售后分润金额（收货后）
            0 AS return_profit_dc, -- 运营商售后分润金额（收货后）
            0 AS return_profit_colonel1, -- 业务员管理员售后分润金额（收货后）
            0 AS return_profit_colonel2, -- 业务员售后分润金额（收货后）
            0 AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            0 AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            0 AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            0 AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    sum( return_amt ) return_amt,
                    sum( refund_amt ) refund_amt,
                    sum( cancel_amt ) cancel_amt,
                    sum( cancel_qty ) cancel_qty,
                    sum( supplier_amt ) supplier_amt,
                    sum( profit ) profit,
                    sum( partner_amt ) partner_amt,
                    sum( dc_amt ) dc_amt,
                    sum( colonel1_amt ) colonel1_amt,
                    sum( colonel2_amt ) colonel2_amt,
                    sum( cancel_activity_discount_amt ) cancel_activity_discount_amt,
                    sum( cancel_coupon_discount_amt ) cancel_coupon_discount_amt,
                    sum( cancel_coupon_discount_amt2 ) cancel_coupon_discount_amt2
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    order_date_id in (
                        SELECT order_date_id as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            dtodi.date_id in (
                SELECT
                    orderdate.order_date_id as order_date_id
                FROM
                    (
                        SELECT date_id as order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY date_id
                        union
                        SELECT order_date_id as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            dtodi.date_id,
            dtodi.sys_code,
            dtodi.supplier_id,
            dtodi.area_id

        UNION ALL

        SELECT
            dtri.date_id, -- 日期ID
            dtri.sys_code, -- 平台商编号
            dtri.supplier_id, -- 入驻商ID
            dtri.area_id, -- 区域ID
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS order_num, -- 订单数量
            0 AS sku_num, -- SKU数量
            0 AS spu_num, -- SPU数量
            0 AS branch_num, -- 门店数量
            0 AS supplier_amt, -- 入驻商成本金额
            0 AS profit_amt, -- 利润金额（减去发货前售后）
            0 AS partner_amt, -- 平台上分润金额（减去发货前售后）
            0 AS dc_amt, -- 运营上分润金额（减去发货前售后）
            0 AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            0 AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            0 AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            0 AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            0 AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            0 AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS after_num, -- 退单数量（收货后）
            COUNT(DISTINCT dtri.sku_id) AS return_sku_num, -- 售后SKU数量（收货后）
            COUNT(DISTINCT dtri.spu_id) AS return_spu_num, -- 售后SPU数量（收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num, -- 售后门店数量（收货后）
            IFNULL(SUM(dtri.supplier_amt), 0)AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            IFNULL(SUM(dtri.profit), 0) AS return_profit, -- 售后利润（收货后）
            IFNULL(SUM(dtri.partner_amt), 0) AS return_profit_partner_amt, -- 平台商售后分润金额（收货后）
            IFNULL(SUM(dtri.dc_amt), 0) AS return_profit_dc_amt, -- 运营商售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel1_amt), 0) AS return_profit_colonel1_amt, -- 业务员管理员售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel2_amt), 0) AS return_profit_colonel2_amt, -- 业务员售后分润金额（收货后）
            SUM(dtri.return_order_amt) AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            IFNULL(SUM(dtri.return_activity_discount_amt), 0) AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt), 0) AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt2), 0) AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_return_inc dtri
        WHERE
            order_date_id in (
                SELECT order_date_id FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
            )
        GROUP BY
            dtri.date_id, -- 日期ID
            dtri.sys_code, -- 平台商编号
            dtri.supplier_id, -- 入住商ID
            dtri.area_id -- 区域ID
    ) updateTable
GROUP BY
    updateTable.date_id,
    updateTable.sys_code,
    updateTable.supplier_id,
    updateTable.area_id

-- ======================================================dws_trd_supplier_sales_month（入驻商销售月结）===============================================
replace into dws_trd_supplier_sales_month (
    month_id
    , sys_code
    , supplier_id
    , area_id
    , order_amt
    , order_qty
    , dx_sku_qty
    , dx_spu_qty
    , dx_branch_qty
    , supplier_cost_amt
    , profit_amt
    , profit_partner_amt
    , profit_dc_amt
    , profit_colonel_amt
    , profit_colonel1_amt
    , profit_colonel2_amt
    , pre_order_amt
    , activity_discount_amt
    , coupon_discount_amt
    , coupon_discount_amt2
    , discount_amt
    , profit_rate
    , return_amt
    , return_qty
    , return_sku_qty
    , return_spu_qty
    , return_branch_qty
    , return_supplier_cost_amt
    , return_profit_amt
    , return_profit_partner_amt
    , return_profit_dc_amt
    , return_profit_colonel_amt
    , return_profit_colonel1_amt
    , return_profit_colonel2_amt
    , pre_return_amt
    , return_activity_discount_amt
    , return_coupon_discount_amt
    , return_coupon_discount_amt2
    , return_discount_amt
    , return_profit_rate
    , insert_date_id
)
SELECT
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.supplier_id,
    updateTable.area_id,
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.sku_num) AS dx_sku_qty,
    SUM(updateTable.spu_num) AS dx_spu_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.supplier_amt) AS supplier_cost_amt,
    SUM(updateTable.profit_amt) AS profit_amt,
    SUM(updateTable.partner_amt) AS profit_partner_amt,
    SUM(updateTable.dc_amt) AS profit_dc_amt,
    SUM(updateTable.colonel1_amt + updateTable.colonel2_amt) AS profit_colonel_amt,
    SUM(updateTable.colonel1_amt) AS profit_colonel1_amt,
    SUM(updateTable.colonel2_amt) AS profit_colonel2_amt,
    SUM(updateTable.pre_order_amt) AS pre_order_amt,
    SUM(updateTable.activity_discount_amt) AS activity_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt2,
    SUM(updateTable.activity_discount_amt + updateTable.coupon_discount_amt + updateTable.coupon_discount_amt2) AS discount_amt,
    CASE
        WHEN SUM(updateTable.order_amt) <= 0 THEN 0
        ELSE  ROUND(IFNULL((SUM(updateTable.order_amt) - SUM(updateTable.supplier_amt)) / SUM(updateTable.order_amt) * 100, 0), 6)
    END AS profit_rate,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.after_num) AS return_qty,
    SUM(updateTable.return_sku_num) AS return_sku_qty,
    SUM(updateTable.return_spu_num) AS return_spu_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    SUM(updateTable.return_supplier_amt) AS return_supplier_cost_amt,
    SUM(updateTable.return_profit) AS return_profit,
    SUM(updateTable.return_profit_partner) AS return_profit_partner_amt,
    SUM(updateTable.return_profit_dc) AS return_profit_dc_amt,
    SUM(updateTable.return_profit_colonel1 + updateTable.return_profit_colonel2) AS return_profit_colonel_amt,
    SUM(updateTable.return_profit_colonel1) AS return_profit_colonel1_amt,
    SUM(updateTable.return_profit_colonel2) AS return_profit_colonel2_amt,
    SUM(updateTable.pre_return_amt) AS pre_return_amt,
    SUM(updateTable.return_activity_discount_amt) AS return_activity_discount_amt,
    SUM(updateTable.return_coupon_discount_amt) AS return_coupon_discount_amt,
    SUM(updateTable.return_coupon_discount_amt2) AS return_coupon_discount_amt2,
    SUM(updateTable.return_activity_discount_amt + updateTable.return_coupon_discount_amt + updateTable.return_coupon_discount_amt2) AS return_discount_amt,
    CASE
        WHEN SUM(updateTable.return_amt) <= 0 THEN 0
        ELSE ROUND(IFNULL((SUM(updateTable.return_amt) - SUM(updateTable.return_supplier_amt)) / SUM(updateTable.return_amt) * 100, 0), 6)
    END AS return_profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            SUBSTRING( dtodi.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtodi.sys_code, -- 平台商编号
            dtodi.supplier_id, -- 入驻商id
            dtodi.area_id, -- 区域ID
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            COUNT(DISTINCT dtodi.sku_id) AS sku_num, -- SKU数量
            COUNT(DISTINCT dtodi.spu_id) AS spu_num, -- SPU数量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            IFNULL(SUM(dtodi.supplier_amt) - SUM(IFNULL(dtci.supplier_amt, 0)), 0) AS supplier_amt, -- 入驻商成本金额
            IFNULL(SUM(dtodi.profit) - SUM(IFNULL(dtci.profit, 0)), 0) AS profit_amt, -- 利润金额（减去发货前售后）
            IFNULL(SUM(dtodi.partner_amt) - SUM(IFNULL(dtci.partner_amt, 0)), 0) AS partner_amt, -- 平台商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.dc_amt) - SUM(IFNULL(dtci.dc_amt, 0)), 0) AS dc_amt, -- 运营商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel1_amt) - SUM(IFNULL(dtci.colonel1_amt, 0)), 0) AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel2_amt) - SUM(IFNULL(dtci.colonel2_amt, 0)), 0) AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.order_amt) - SUM(IFNULL(dtci.cancel_amt, 0)), 0) AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            IFNULL(SUM(dtodi.activity_discount_amt) - SUM(IFNULL(dtci.cancel_activity_discount_amt, 0)), 0) AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt) - SUM(IFNULL(dtci.cancel_coupon_discount_amt, 0)), 0) AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt2) - SUM(IFNULL(dtci.cancel_coupon_discount_amt2, 0)), 0) AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS after_num, -- 售后单数量（收货后）
            0 AS return_sku_num, -- 售后SKU数量（收货后）
            0 AS return_spu_num, -- 售后SPU数量（收货后）
            0 AS return_branch_num, -- 售后门店数量（收货后）
            0 AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            0 AS return_profit, -- 售后利润（收货后）
            0 AS return_profit_partner, -- 平台商售后分润金额（收货后）
            0 AS return_profit_dc, -- 运营商售后分润金额（收货后）
            0 AS return_profit_colonel1, -- 业务员管理员售后分润金额（收货后）
            0 AS return_profit_colonel2, -- 业务员售后分润金额（收货后）
            0 AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            0 AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            0 AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            0 AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    SUM( return_amt ) return_amt,
                    SUM( refund_amt ) refund_amt,
                    SUM( cancel_amt ) cancel_amt,
                    SUM( cancel_qty ) cancel_qty,
                    SUM( supplier_amt ) supplier_amt,
                    SUM( profit ) profit,
                    SUM( partner_amt ) partner_amt,
                    SUM( dc_amt ) dc_amt,
                    SUM( colonel1_amt ) colonel1_amt,
                    SUM( colonel2_amt ) colonel2_amt,
                    SUM( cancel_activity_discount_amt ) cancel_activity_discount_amt,
                    SUM( cancel_coupon_discount_amt ) cancel_coupon_discount_amt,
                    SUM( cancel_coupon_discount_amt2 ) cancel_coupon_discount_amt2
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    SUBSTRING( order_date_id , 1, 6 ) in (
                        SELECT SUBSTRING( order_date_id , 1, 6 ) FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            SUBSTRING( dtodi.date_id , 1, 6 ) in (
                SELECT
                    orderdate.order_date_id
                FROM
                    (
                        SELECT SUBSTRING( date_id , 1, 6 )  AS order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id , 1, 6 )
                        UNION
                        SELECT SUBSTRING( order_date_id , 1, 6 )  AS order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            SUBSTRING( dtodi.date_id , 1, 6 ), -- 年月ID
            dtodi.supplier_id, -- 入驻商id
            dtodi.sys_code,
            dtodi.area_id

        UNION ALL

        SELECT
            SUBSTRING( dtri.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.supplier_id, -- 入驻商id
            dtri.area_id, -- 区域ID
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS order_num, -- 订单数量
            0 AS sku_num, -- SKU数量
            0 AS spu_num, -- SPU数量
            0 AS branch_num, -- 门店数量
            0 AS supplier_amt, -- 入驻商成本金额
            0 AS profit_amt, -- 利润金额（减去发货前售后）
            0 AS partner_amt, -- 平台上分润金额（减去发货前售后）
            0 AS dc_amt, -- 运营上分润金额（减去发货前售后）
            0 AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            0 AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            0 AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            0 AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            0 AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            0 AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS after_num, -- 退单数量（收货后）
            COUNT(DISTINCT dtri.sku_id) AS return_sku_num, -- 售后SKU数量（收货后）
            COUNT(DISTINCT dtri.spu_id) AS return_spu_num, -- 售后SPU数量（收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num, -- 售后门店数量（收货后）
            IFNULL(SUM(dtri.supplier_amt), 0)AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            IFNULL(SUM(dtri.profit), 0) AS return_profit, -- 售后利润（收货后）
            IFNULL(SUM(dtri.partner_amt), 0) AS return_profit_partner_amt, -- 平台商售后分润金额（收货后）
            IFNULL(SUM(dtri.dc_amt), 0) AS return_profit_dc_amt, -- 运营商售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel1_amt), 0) AS return_profit_colonel1_amt, -- 业务员管理员售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel2_amt), 0) AS return_profit_colonel2_amt, -- 业务员售后分润金额（收货后）
            SUM(dtri.return_order_amt) AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            IFNULL(SUM(dtri.return_activity_discount_amt), 0) AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt), 0) AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt2), 0) AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_return_inc dtri
        WHERE
            SUBSTRING( date_id, 1, 6 ) in (
                SELECT SUBSTRING( date_id, 1, 6 )  FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id, 1, 6 )
            )
        GROUP BY
            SUBSTRING( dtri.date_id , 1, 6 ), -- 年月ID
            dtri.supplier_id, -- 入驻商id
            dtri.sys_code, -- 平台商编号
            dtri.area_id -- 区域ID
    ) updateTable
GROUP BY
    updateTable.month_id,
    updateTable.supplier_id, -- 入驻商id
    updateTable.sys_code,
    updateTable.area_id


-- =======================================================dws_trd_area_category_sales_month(交易域区域城市管理类别粒度下单月结)================================================================
replace into dws_trd_area_category_sales_month (
    month_id
    , sys_code
    , area_id
    , catgory1_id
    , catgory2_id
    , catgory3_id
    , order_amt
    , order_qty
    , sales_qty
    , dx_sku_qty
    , dx_spu_qty
    , dx_branch_qty
    , supplier_cost_amt
    , profit_amt
    , profit_partner_amt
    , profit_dc_amt
    , profit_colonel_amt
    , profit_colonel1_amt
    , profit_colonel2_amt
    , pre_order_amt
    , activity_discount_amt
    , coupon_discount_amt
    , coupon_discount_amt2
    , discount_amt
    , profit_rate
    , return_amt
    , return_qty
    , return_sales_qty
    , return_sku_qty
    , return_spu_qty
    , return_branch_qty
    , return_supplier_cost_amt
    , return_profit_amt
    , return_profit_partner_amt
    , return_profit_dc_amt
    , return_profit_colonel_amt
    , return_profit_colonel1_amt
    , return_profit_colonel2_amt
    , pre_return_amt
    , return_activity_discount_amt
    , return_coupon_discount_amt
    , return_coupon_discount_amt2
    , return_discount_amt
    , return_profit_rate
    , insert_date_id
)
SELECT
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.catgory1_id, -- 一级管理分类ID
    updateTable.catgory2_id, -- 二级管理分类ID
    updateTable.catgory3_id, -- 三级管理分类ID
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.sales_num) AS sales_qty,
    SUM(updateTable.sku_num) AS dx_sku_qty,
    SUM(updateTable.spu_num) AS dx_spu_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.supplier_amt) AS supplier_cost_amt,
    SUM(updateTable.profit_amt) AS profit_amt,
    SUM(updateTable.partner_amt) AS profit_partner_amt,
    SUM(updateTable.dc_amt) AS profit_dc_amt,
    SUM(updateTable.colonel1_amt + updateTable.colonel2_amt) AS profit_colonel_amt,
    SUM(updateTable.colonel1_amt) AS profit_colonel1_amt,
    SUM(updateTable.colonel2_amt) AS profit_colonel2_amt,
    SUM(updateTable.pre_order_amt) AS pre_order_amt,
    SUM(updateTable.activity_discount_amt) AS activity_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt2,
    SUM(updateTable.activity_discount_amt + updateTable.coupon_discount_amt + updateTable.coupon_discount_amt2) AS discount_amt,
    CASE
        WHEN SUM(updateTable.order_amt) <= 0 THEN 0
        ELSE  ROUND(IFNULL((SUM(updateTable.order_amt) - SUM(updateTable.supplier_amt)) / SUM(updateTable.order_amt) * 100, 0), 6)
    END AS profit_rate,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.after_num) AS return_qty,
    SUM(updateTable.return_sales_num) AS return_sales_qty, -- 售后数量（收货后）
    SUM(updateTable.return_sku_num) AS return_sku_qty,
    SUM(updateTable.return_spu_num) AS return_spu_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    SUM(updateTable.return_supplier_amt) AS return_supplier_cost_amt,
    SUM(updateTable.return_profit) AS return_profit,
    SUM(updateTable.return_profit_partner) AS return_profit_partner_amt,
    SUM(updateTable.return_profit_dc) AS return_profit_dc_amt,
    SUM(updateTable.return_profit_colonel1 + updateTable.return_profit_colonel2) AS return_profit_colonel_amt,
    SUM(updateTable.return_profit_colonel1) AS return_profit_colonel1_amt,
    SUM(updateTable.return_profit_colonel2) AS return_profit_colonel2_amt,
    SUM(updateTable.pre_return_amt) AS pre_return_amt,
    SUM(updateTable.return_activity_discount_amt) AS return_activity_discount_amt,
    SUM(updateTable.return_coupon_discount_amt) AS return_coupon_discount_amt,
    SUM(updateTable.return_coupon_discount_amt2) AS return_coupon_discount_amt2,
    SUM(updateTable.return_activity_discount_amt + updateTable.return_coupon_discount_amt + updateTable.return_coupon_discount_amt2) AS return_discount_amt,
    CASE
        WHEN SUM(updateTable.return_amt) <= 0 THEN 0
        ELSE ROUND(IFNULL((SUM(updateTable.return_amt) - SUM(updateTable.return_supplier_amt)) / SUM(updateTable.return_amt) * 100, 0), 6)
    END AS return_profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            SUBSTRING( dtodi.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtodi.sys_code, -- 平台商ID
            dtodi.area_id, -- 区域ID
            ds.catgory1_id,
            ds.catgory2_id,
            ds.catgory3_id,
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            IFNULL(SUM(dtodi.total_num) - SUM(IFNULL(dtci.cancel_qty, 0)), 0) AS sales_num, -- 销量;小单位
            COUNT(DISTINCT dtodi.sku_id) AS sku_num, -- SKU数量
            COUNT(DISTINCT dtodi.spu_id) AS spu_num, -- SPU数量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            IFNULL(SUM(dtodi.supplier_amt) - SUM(IFNULL(dtci.supplier_amt, 0)), 0) AS supplier_amt, -- 入驻商成本金额
            IFNULL(SUM(dtodi.profit) - SUM(IFNULL(dtci.profit, 0)), 0) AS profit_amt, -- 利润金额（减去发货前售后）
            IFNULL(SUM(dtodi.partner_amt) - SUM(IFNULL(dtci.partner_amt, 0)), 0) AS partner_amt, -- 平台商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.dc_amt) - SUM(IFNULL(dtci.dc_amt, 0)), 0) AS dc_amt, -- 运营商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel1_amt) - SUM(IFNULL(dtci.colonel1_amt, 0)), 0) AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel2_amt) - SUM(IFNULL(dtci.colonel2_amt, 0)), 0) AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.order_amt) - SUM(IFNULL(dtci.cancel_amt, 0)), 0) AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            IFNULL(SUM(dtodi.activity_discount_amt) - SUM(IFNULL(dtci.cancel_activity_discount_amt, 0)), 0) AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt) - SUM(IFNULL(dtci.cancel_coupon_discount_amt, 0)), 0) AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt2) - SUM(IFNULL(dtci.cancel_coupon_discount_amt2, 0)), 0) AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS after_num, -- 售后单数量（收货后）
            0 AS return_sales_num, -- 售后数量（收货后）
            0 AS return_sku_num, -- 售后SKU数量（收货后）
            0 AS return_spu_num, -- 售后SPU数量（收货后）
            0 AS return_branch_num, -- 售后门店数量（收货后）
            0 AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            0 AS return_profit, -- 售后利润（收货后）
            0 AS return_profit_partner, -- 平台商售后分润金额（收货后）
            0 AS return_profit_dc, -- 运营商售后分润金额（收货后）
            0 AS return_profit_colonel1, -- 业务员管理员售后分润金额（收货后）
            0 AS return_profit_colonel2, -- 业务员售后分润金额（收货后）
            0 AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            0 AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            0 AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            0 AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    SUM( return_amt ) return_amt,
                    SUM( refund_amt ) refund_amt,
                    SUM( cancel_amt ) cancel_amt,
                    SUM( cancel_qty ) cancel_qty,
                    SUM( supplier_amt ) supplier_amt,
                    SUM( profit ) profit,
                    SUM( partner_amt ) partner_amt,
                    SUM( dc_amt ) dc_amt,
                    SUM( colonel1_amt ) colonel1_amt,
                    SUM( colonel2_amt ) colonel2_amt,
                    SUM( cancel_activity_discount_amt ) cancel_activity_discount_amt,
                    SUM( cancel_coupon_discount_amt ) cancel_coupon_discount_amt,
                    SUM( cancel_coupon_discount_amt2 ) cancel_coupon_discount_amt2
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    SUBSTRING( order_date_id , 1, 6 ) in (
                        SELECT SUBSTRING( order_date_id , 1, 6 ) FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
            LEFT JOIN dim_sku ds ON dtodi.sku_id = ds.sku_id
        WHERE
            SUBSTRING( dtodi.date_id , 1, 6 ) in (
                SELECT
                    orderdate.order_date_id
                FROM
                    (
                        SELECT SUBSTRING( date_id , 1, 6 )  AS order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id , 1, 6 )
                        UNION
                        SELECT SUBSTRING( order_date_id , 1, 6 )  AS order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
            AND ds.catgory1_id IS NOT NULL
            AND ds.catgory2_id IS NOT NULL
            AND ds.catgory3_id IS NOT NULL
        GROUP BY
            SUBSTRING( dtodi.date_id , 1, 6 ), -- 年月ID
            dtodi.sys_code, -- 平台商ID
            dtodi.area_id, -- 区域ID
            ds.catgory1_id, -- 一级管理分类ID
            ds.catgory2_id, -- 二级管理分类ID
            ds.catgory3_id -- 三级管理分类ID

        UNION ALL

        SELECT
            SUBSTRING( dtri.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            ds.catgory1_id, -- 一级管理分类ID
            ds.catgory2_id, -- 二级管理分类ID
            ds.catgory3_id, -- 三级管理分类ID
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS order_num, -- 订单数量
            0 AS sales_num, -- 销量;小单位
            0 AS sku_num, -- SKU数量
            0 AS spu_num, -- SPU数量
            0 AS branch_num, -- 门店数量
            0 AS supplier_amt, -- 入驻商成本金额
            0 AS profit_amt, -- 利润金额（减去发货前售后）
            0 AS partner_amt, -- 平台上分润金额（减去发货前售后）
            0 AS dc_amt, -- 运营上分润金额（减去发货前售后）
            0 AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            0 AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            0 AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            0 AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            0 AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            0 AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS after_num, -- 退单数量（收货后）
            IFNULL(SUM(dtri.return_qty), 0) AS return_sales_num, -- 售后数量（收货后）
            COUNT(DISTINCT dtri.sku_id) AS return_sku_num, -- 售后SKU数量（收货后）
            COUNT(DISTINCT dtri.spu_id) AS return_spu_num, -- 售后SPU数量（收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num, -- 售后门店数量（收货后）
            IFNULL(SUM(dtri.supplier_amt), 0)AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            IFNULL(SUM(dtri.profit), 0) AS return_profit, -- 售后利润（收货后）
            IFNULL(SUM(dtri.partner_amt), 0) AS return_profit_partner_amt, -- 平台商售后分润金额（收货后）
            IFNULL(SUM(dtri.dc_amt), 0) AS return_profit_dc_amt, -- 运营商售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel1_amt), 0) AS return_profit_colonel1_amt, -- 业务员管理员售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel2_amt), 0) AS return_profit_colonel2_amt, -- 业务员售后分润金额（收货后）
            SUM(dtri.return_order_amt) AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            IFNULL(SUM(dtri.return_activity_discount_amt), 0) AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt), 0) AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt2), 0) AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_return_inc dtri
            LEFT JOIN dim_sku ds ON dtri.sku_id = ds.sku_id
        WHERE
            SUBSTRING( date_id, 1, 6 ) in (
                SELECT SUBSTRING( date_id, 1, 6 )  FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id, 1, 6 )
            )
            AND ds.catgory1_id IS NOT NULL
            AND ds.catgory2_id IS NOT NULL
            AND ds.catgory3_id IS NOT NULL
        GROUP BY
            SUBSTRING( dtri.date_id , 1, 6 ), -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            ds.catgory1_id, -- 一级管理分类ID
            ds.catgory2_id, -- 二级管理分类ID
            ds.catgory3_id -- 三级管理分类ID
    ) updateTable
GROUP BY
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.catgory1_id, -- 一级管理分类ID
    updateTable.catgory2_id, -- 二级管理分类ID
    updateTable.catgory3_id -- 三级管理分类ID

-- ===================================================================dws_trd_branch_category_sales_month（交易域门店管理类别粒度下单月结）=======================================
replace into dws_trd_branch_category_sales_month
    (
    month_id
    , sys_code
    , area_id
    , branch_id
    , catgory1_id
    , catgory2_id
    , catgory3_id
    , order_amt
    , order_qty
    , sales_qty
    , dx_sku_qty
    , dx_spu_qty
    , dx_branch_qty
    , supplier_cost_amt
    , profit_amt
    , profit_partner_amt
    , profit_dc_amt
    , profit_colonel_amt
    , profit_colonel1_amt
    , profit_colonel2_amt
    , pre_order_amt
    , activity_discount_amt
    , coupon_discount_amt
    , coupon_discount_amt2
    , discount_amt
    , profit_rate
    , return_amt
    , return_qty
    , return_sales_qty
    , return_sku_qty
    , return_spu_qty
    , return_branch_qty
    , return_supplier_cost_amt
    , return_profit_amt
    , return_profit_partner_amt
    , return_profit_dc_amt
    , return_profit_colonel_amt
    , return_profit_colonel1_amt
    , return_profit_colonel2_amt
    , pre_return_amt
    , return_activity_discount_amt
    , return_coupon_discount_amt
    , return_coupon_discount_amt2
    , return_discount_amt
    , return_profit_rate
    , insert_date_id
)
SELECT
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.branch_id,
    updateTable.catgory1_id, -- 一级管理分类ID
    updateTable.catgory2_id, -- 二级管理分类ID
    updateTable.catgory3_id, -- 三级管理分类ID
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.sales_num) AS sales_qty,
    SUM(updateTable.sku_num) AS dx_sku_qty,
    SUM(updateTable.spu_num) AS dx_spu_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.supplier_amt) AS supplier_cost_amt,
    SUM(updateTable.profit_amt) AS profit_amt,
    SUM(updateTable.partner_amt) AS profit_partner_amt,
    SUM(updateTable.dc_amt) AS profit_dc_amt,
    SUM(updateTable.colonel1_amt + updateTable.colonel2_amt) AS profit_colonel_amt,
    SUM(updateTable.colonel1_amt) AS profit_colonel1_amt,
    SUM(updateTable.colonel2_amt) AS profit_colonel2_amt,
    SUM(updateTable.pre_order_amt) AS pre_order_amt,
    SUM(updateTable.activity_discount_amt) AS activity_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt2,
    SUM(updateTable.activity_discount_amt + updateTable.coupon_discount_amt + updateTable.coupon_discount_amt2) AS discount_amt,
    CASE
        WHEN SUM(updateTable.order_amt) <= 0 THEN 0
        ELSE  ROUND(IFNULL((SUM(updateTable.order_amt) - SUM(updateTable.supplier_amt)) / SUM(updateTable.order_amt) * 100, 0), 6)
    END AS profit_rate,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.after_num) AS return_qty,
    SUM(updateTable.return_sales_num) AS return_sales_qty, -- 售后数量（收货后）
    SUM(updateTable.return_sku_num) AS return_sku_qty,
    SUM(updateTable.return_spu_num) AS return_spu_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    SUM(updateTable.return_supplier_amt) AS return_supplier_cost_amt,
    SUM(updateTable.return_profit) AS return_profit,
    SUM(updateTable.return_profit_partner) AS return_profit_partner_amt,
    SUM(updateTable.return_profit_dc) AS return_profit_dc_amt,
    SUM(updateTable.return_profit_colonel1 + updateTable.return_profit_colonel2) AS return_profit_colonel_amt,
    SUM(updateTable.return_profit_colonel1) AS return_profit_colonel1_amt,
    SUM(updateTable.return_profit_colonel2) AS return_profit_colonel2_amt,
    SUM(updateTable.pre_return_amt) AS pre_return_amt,
    SUM(updateTable.return_activity_discount_amt) AS return_activity_discount_amt,
    SUM(updateTable.return_coupon_discount_amt) AS return_coupon_discount_amt,
    SUM(updateTable.return_coupon_discount_amt2) AS return_coupon_discount_amt2,
    SUM(updateTable.return_activity_discount_amt + updateTable.return_coupon_discount_amt + updateTable.return_coupon_discount_amt2) AS return_discount_amt,
    CASE
        WHEN SUM(updateTable.return_amt) <= 0 THEN 0
        ELSE ROUND(IFNULL((SUM(updateTable.return_amt) - SUM(updateTable.return_supplier_amt)) / SUM(updateTable.return_amt) * 100, 0), 6)
    END AS return_profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            SUBSTRING( dtodi.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtodi.sys_code, -- 平台商ID
            dtodi.area_id, -- 区域ID
            dtodi.branch_id, -- 门店ID
            ds.catgory1_id,
            ds.catgory2_id,
            ds.catgory3_id,
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            IFNULL(SUM(dtodi.total_num) - SUM(IFNULL(dtci.cancel_qty, 0)), 0) AS sales_num, -- 销量;小单位
            COUNT(DISTINCT dtodi.sku_id) AS sku_num, -- SKU数量
            COUNT(DISTINCT dtodi.spu_id) AS spu_num, -- SPU数量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            IFNULL(SUM(dtodi.supplier_amt) - SUM(IFNULL(dtci.supplier_amt, 0)), 0) AS supplier_amt, -- 入驻商成本金额
            IFNULL(SUM(dtodi.profit) - SUM(IFNULL(dtci.profit, 0)), 0) AS profit_amt, -- 利润金额（减去发货前售后）
            IFNULL(SUM(dtodi.partner_amt) - SUM(IFNULL(dtci.partner_amt, 0)), 0) AS partner_amt, -- 平台商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.dc_amt) - SUM(IFNULL(dtci.dc_amt, 0)), 0) AS dc_amt, -- 运营商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel1_amt) - SUM(IFNULL(dtci.colonel1_amt, 0)), 0) AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel2_amt) - SUM(IFNULL(dtci.colonel2_amt, 0)), 0) AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.order_amt) - SUM(IFNULL(dtci.cancel_amt, 0)), 0) AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            IFNULL(SUM(dtodi.activity_discount_amt) - SUM(IFNULL(dtci.cancel_activity_discount_amt, 0)), 0) AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt) - SUM(IFNULL(dtci.cancel_coupon_discount_amt, 0)), 0) AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt2) - SUM(IFNULL(dtci.cancel_coupon_discount_amt2, 0)), 0) AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS after_num, -- 售后单数量（收货后）
            0 AS return_sales_num, -- 售后数量（收货后）
            0 AS return_sku_num, -- 售后SKU数量（收货后）
            0 AS return_spu_num, -- 售后SPU数量（收货后）
            0 AS return_branch_num, -- 售后门店数量（收货后）
            0 AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            0 AS return_profit, -- 售后利润（收货后）
            0 AS return_profit_partner, -- 平台商售后分润金额（收货后）
            0 AS return_profit_dc, -- 运营商售后分润金额（收货后）
            0 AS return_profit_colonel1, -- 业务员管理员售后分润金额（收货后）
            0 AS return_profit_colonel2, -- 业务员售后分润金额（收货后）
            0 AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            0 AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            0 AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            0 AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    SUM( return_amt ) return_amt,
                    SUM( refund_amt ) refund_amt,
                    SUM( cancel_amt ) cancel_amt,
                    SUM( cancel_qty ) cancel_qty,
                    SUM( supplier_amt ) supplier_amt,
                    SUM( profit ) profit,
                    SUM( partner_amt ) partner_amt,
                    SUM( dc_amt ) dc_amt,
                    SUM( colonel1_amt ) colonel1_amt,
                    SUM( colonel2_amt ) colonel2_amt,
                    SUM( cancel_activity_discount_amt ) cancel_activity_discount_amt,
                    SUM( cancel_coupon_discount_amt ) cancel_coupon_discount_amt,
                    SUM( cancel_coupon_discount_amt2 ) cancel_coupon_discount_amt2
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    SUBSTRING( order_date_id , 1, 6 ) in (
                        SELECT SUBSTRING( order_date_id , 1, 6 ) FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
            LEFT JOIN dim_sku ds ON dtodi.sku_id = ds.sku_id
        WHERE
            SUBSTRING( dtodi.date_id , 1, 6 ) in (
                SELECT
                    orderdate.order_date_id
                FROM
                    (
                        SELECT SUBSTRING( date_id , 1, 6 )  AS order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id , 1, 6 )
                        UNION
                        SELECT SUBSTRING( order_date_id , 1, 6 )  AS order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
            AND ds.catgory1_id IS NOT NULL
            AND ds.catgory2_id IS NOT NULL
            AND ds.catgory3_id IS NOT NULL
        GROUP BY
            SUBSTRING( dtodi.date_id , 1, 6 ), -- 年月ID
            dtodi.sys_code, -- 平台商ID
            dtodi.area_id, -- 区域ID
            dtodi.branch_id, -- 门店ID
            ds.catgory1_id, -- 一级管理分类ID
            ds.catgory2_id, -- 二级管理分类ID
            ds.catgory3_id -- 三级管理分类ID

        UNION ALL

        SELECT
            SUBSTRING( dtri.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.branch_id, -- 门店ID
            ds.catgory1_id, -- 一级管理分类ID
            ds.catgory2_id, -- 二级管理分类ID
            ds.catgory3_id, -- 三级管理分类ID
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS order_num, -- 订单数量
            0 AS sales_num, -- 销量;小单位
            0 AS sku_num, -- SKU数量
            0 AS spu_num, -- SPU数量
            0 AS branch_num, -- 门店数量
            0 AS supplier_amt, -- 入驻商成本金额
            0 AS profit_amt, -- 利润金额（减去发货前售后）
            0 AS partner_amt, -- 平台上分润金额（减去发货前售后）
            0 AS dc_amt, -- 运营上分润金额（减去发货前售后）
            0 AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            0 AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            0 AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            0 AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            0 AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            0 AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS after_num, -- 退单数量（收货后）
            IFNULL(SUM(dtri.return_qty), 0) AS return_sales_num, -- 售后数量（收货后）
            COUNT(DISTINCT dtri.sku_id) AS return_sku_num, -- 售后SKU数量（收货后）
            COUNT(DISTINCT dtri.spu_id) AS return_spu_num, -- 售后SPU数量（收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num, -- 售后门店数量（收货后）
            IFNULL(SUM(dtri.supplier_amt), 0)AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            IFNULL(SUM(dtri.profit), 0) AS return_profit, -- 售后利润（收货后）
            IFNULL(SUM(dtri.partner_amt), 0) AS return_profit_partner_amt, -- 平台商售后分润金额（收货后）
            IFNULL(SUM(dtri.dc_amt), 0) AS return_profit_dc_amt, -- 运营商售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel1_amt), 0) AS return_profit_colonel1_amt, -- 业务员管理员售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel2_amt), 0) AS return_profit_colonel2_amt, -- 业务员售后分润金额（收货后）
            SUM(dtri.return_order_amt) AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            IFNULL(SUM(dtri.return_activity_discount_amt), 0) AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt), 0) AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt2), 0) AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_return_inc dtri
            LEFT JOIN dim_sku ds ON dtri.sku_id = ds.sku_id
        WHERE
            SUBSTRING( date_id, 1, 6 ) in (
                SELECT SUBSTRING( date_id, 1, 6 )  FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id, 1, 6 )
            )
            AND ds.catgory1_id IS NOT NULL
            AND ds.catgory2_id IS NOT NULL
            AND ds.catgory3_id IS NOT NULL
        GROUP BY
            SUBSTRING( dtri.date_id , 1, 6 ), -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.branch_id, -- 门店ID
            ds.catgory1_id, -- 一级管理分类ID
            ds.catgory2_id, -- 二级管理分类ID
            ds.catgory3_id -- 三级管理分类ID
    ) updateTable
GROUP BY
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.branch_id,
    updateTable.catgory1_id, -- 一级管理分类ID
    updateTable.catgory2_id, -- 二级管理分类ID
    updateTable.catgory3_id -- 三级管理分类ID

-- ===============================================================交易域门店粒度下单日汇总表（dws_trd_branch_sales_day）================
replace into dws_trd_branch_sales_day (
    date_id
    , sys_code
    , area_id
    , branch_id
    , order_amt
    , order_qty
    , dx_sku_qty
    , dx_spu_qty
    , dx_branch_qty
    , supplier_cost_amt
    , profit_amt
    , profit_partner_amt
    , profit_dc_amt
    , profit_colonel_amt
    , profit_colonel1_amt
    , profit_colonel2_amt
    , pre_order_amt
    , activity_discount_amt
    , coupon_discount_amt
    , coupon_discount_amt2
    , discount_amt
    , profit_rate
    , return_amt
    , return_qty
    , return_sku_qty
    , return_spu_qty
    , return_branch_qty
    , return_supplier_cost_amt
    , return_profit_amt
    , return_profit_partner_amt
    , return_profit_dc_amt
    , return_profit_colonel_amt
    , return_profit_colonel1_amt
    , return_profit_colonel2_amt
    , pre_return_amt
    , return_activity_discount_amt
    , return_coupon_discount_amt
    , return_coupon_discount_amt2
    , return_discount_amt
    , return_profit_rate
    , insert_date_id
)
SELECT
    updateTable.date_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.branch_id,
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.sku_num) AS dx_sku_qty,
    SUM(updateTable.spu_num) AS dx_spu_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.supplier_amt) AS supplier_cost_amt,
    SUM(updateTable.profit_amt) AS profit_amt,
    SUM(updateTable.partner_amt) AS profit_partner_amt,
    SUM(updateTable.dc_amt) AS profit_dc_amt,
    SUM(updateTable.colonel1_amt + updateTable.colonel2_amt) AS profit_colonel_amt,
    SUM(updateTable.colonel1_amt) AS profit_colonel1_amt,
    SUM(updateTable.colonel2_amt) AS profit_colonel2_amt,
    SUM(updateTable.pre_order_amt) AS pre_order_amt,
    SUM(updateTable.activity_discount_amt) AS activity_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt2,
    SUM(updateTable.activity_discount_amt + updateTable.coupon_discount_amt + updateTable.coupon_discount_amt2) AS discount_amt,
    CASE
        WHEN SUM(updateTable.order_amt) <= 0 THEN 0
        ELSE  ROUND(IFNULL((SUM(updateTable.order_amt) - SUM(updateTable.supplier_amt)) / SUM(updateTable.order_amt) * 100, 0), 6)
    END AS profit_rate,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.after_num) AS return_qty,
    SUM(updateTable.return_sku_num) AS return_sku_qty,
    SUM(updateTable.return_spu_num) AS return_spu_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    SUM(updateTable.return_supplier_amt) AS return_supplier_cost_amt,
    SUM(updateTable.return_profit) AS return_profit,
    SUM(updateTable.return_profit_partner) AS return_profit_partner_amt,
    SUM(updateTable.return_profit_dc) AS return_profit_dc_amt,
    SUM(updateTable.return_profit_colonel1 + updateTable.return_profit_colonel2) AS return_profit_colonel_amt,
    SUM(updateTable.return_profit_colonel1) AS return_profit_colonel1_amt,
    SUM(updateTable.return_profit_colonel2) AS return_profit_colonel2_amt,
    SUM(updateTable.pre_return_amt) AS pre_return_amt,
    SUM(updateTable.return_activity_discount_amt) AS return_activity_discount_amt,
    SUM(updateTable.return_coupon_discount_amt) AS return_coupon_discount_amt,
    SUM(updateTable.return_coupon_discount_amt2) AS return_coupon_discount_amt2,
    SUM(updateTable.return_activity_discount_amt + updateTable.return_coupon_discount_amt + updateTable.return_coupon_discount_amt2) AS return_discount_amt,
    CASE
        WHEN SUM(updateTable.return_amt) <= 0 THEN 0
        ELSE ROUND(IFNULL((SUM(updateTable.return_amt) - SUM(updateTable.return_supplier_amt)) / SUM(updateTable.return_amt) * 100, 0), 6)
    END AS return_profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            dtodi.date_id, -- 日期ID
            dtodi.sys_code, -- 平台商编号
            dtodi.area_id, -- 区域ID
            dtodi.branch_id, -- 门店ID
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            COUNT(DISTINCT dtodi.sku_id) AS sku_num, -- SKU数量
            COUNT(DISTINCT dtodi.spu_id) AS spu_num, -- SPU数量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            IFNULL(SUM(dtodi.supplier_amt) - SUM(IFNULL(dtci.supplier_amt, 0)), 0) AS supplier_amt, -- 入驻商成本金额
            IFNULL(SUM(dtodi.profit) - SUM(IFNULL(dtci.profit, 0)), 0) AS profit_amt, -- 利润金额（减去发货前售后）
            IFNULL(SUM(dtodi.partner_amt) - SUM(IFNULL(dtci.partner_amt, 0)), 0) AS partner_amt, -- 平台商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.dc_amt) - SUM(IFNULL(dtci.dc_amt, 0)), 0) AS dc_amt, -- 运营商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel1_amt) - SUM(IFNULL(dtci.colonel1_amt, 0)), 0) AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel2_amt) - SUM(IFNULL(dtci.colonel2_amt, 0)), 0) AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.order_amt) - SUM(IFNULL(dtci.cancel_amt, 0)), 0) AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            IFNULL(SUM(dtodi.activity_discount_amt) - SUM(IFNULL(dtci.cancel_activity_discount_amt, 0)), 0) AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt) - SUM(IFNULL(dtci.cancel_coupon_discount_amt, 0)), 0) AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt2) - SUM(IFNULL(dtci.cancel_coupon_discount_amt2, 0)), 0) AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS after_num, -- 售后单数量（收货后）
            0 AS return_sku_num, -- 售后SKU数量（收货后）
            0 AS return_spu_num, -- 售后SPU数量（收货后）
            0 AS return_branch_num, -- 售后门店数量（收货后）
            0 AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            0 AS return_profit, -- 售后利润（收货后）
            0 AS return_profit_partner, -- 平台商售后分润金额（收货后）
            0 AS return_profit_dc, -- 运营商售后分润金额（收货后）
            0 AS return_profit_colonel1, -- 业务员管理员售后分润金额（收货后）
            0 AS return_profit_colonel2, -- 业务员售后分润金额（收货后）
            0 AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            0 AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            0 AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            0 AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    SUM( return_amt ) return_amt,
                    SUM( refund_amt ) refund_amt,
                    SUM( cancel_amt ) cancel_amt,
                    SUM( cancel_qty ) cancel_qty,
                    SUM( supplier_amt ) supplier_amt,
                    SUM( profit ) profit,
                    SUM( partner_amt ) partner_amt,
                    SUM( dc_amt ) dc_amt,
                    SUM( colonel1_amt ) colonel1_amt,
                    SUM( colonel2_amt ) colonel2_amt,
                    SUM( cancel_activity_discount_amt ) cancel_activity_discount_amt,
                    SUM( cancel_coupon_discount_amt ) cancel_coupon_discount_amt,
                    SUM( cancel_coupon_discount_amt2 ) cancel_coupon_discount_amt2
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    order_date_id in (
                        SELECT order_date_id as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            dtodi.date_id in (
                SELECT
                    orderdate.order_date_id as order_date_id
                FROM
                    (
                        SELECT date_id as order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY date_id
                        union
                        SELECT order_date_id as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            dtodi.date_id,
            dtodi.sys_code,
            dtodi.area_id,
            dtodi.branch_id -- 门店ID

        UNION ALL

        SELECT
            dtri.date_id, -- 日期ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.branch_id, -- 门店ID
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS order_num, -- 订单数量
            0 AS sku_num, -- SKU数量
            0 AS spu_num, -- SPU数量
            0 AS branch_num, -- 门店数量
            0 AS supplier_amt, -- 入驻商成本金额
            0 AS profit_amt, -- 利润金额（减去发货前售后）
            0 AS partner_amt, -- 平台上分润金额（减去发货前售后）
            0 AS dc_amt, -- 运营上分润金额（减去发货前售后）
            0 AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            0 AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            0 AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            0 AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            0 AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            0 AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS after_num, -- 退单数量（收货后）
            COUNT(DISTINCT dtri.sku_id) AS return_sku_num, -- 售后SKU数量（收货后）
            COUNT(DISTINCT dtri.spu_id) AS return_spu_num, -- 售后SPU数量（收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num, -- 售后门店数量（收货后）
            IFNULL(SUM(dtri.supplier_amt), 0)AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            IFNULL(SUM(dtri.profit), 0) AS return_profit, -- 售后利润（收货后）
            IFNULL(SUM(dtri.partner_amt), 0) AS return_profit_partner_amt, -- 平台商售后分润金额（收货后）
            IFNULL(SUM(dtri.dc_amt), 0) AS return_profit_dc_amt, -- 运营商售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel1_amt), 0) AS return_profit_colonel1_amt, -- 业务员管理员售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel2_amt), 0) AS return_profit_colonel2_amt, -- 业务员售后分润金额（收货后）
            SUM(dtri.return_order_amt) AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            IFNULL(SUM(dtri.return_activity_discount_amt), 0) AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt), 0) AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt2), 0) AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_return_inc dtri
        WHERE
            order_date_id in (
                SELECT order_date_id FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
            )
        GROUP BY
            dtri.date_id, -- 日期ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.branch_id -- 门店ID
    ) updateTable
GROUP BY
    updateTable.date_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.branch_id

-- ========================================================交易域门店粒度下单月汇总表（dws_trd_branch_sales_month）=================================
replace into dws_trd_branch_sales_month (
    month_id
    , sys_code
    , area_id
    , branch_id
    , order_amt
    , order_qty
    , dx_sku_qty
    , dx_spu_qty
    , dx_branch_qty
    , supplier_cost_amt
    , profit_amt
    , profit_partner_amt
    , profit_dc_amt
    , profit_colonel_amt
    , profit_colonel1_amt
    , profit_colonel2_amt
    , pre_order_amt
    , activity_discount_amt
    , coupon_discount_amt
    , coupon_discount_amt2
    , discount_amt
    , profit_rate
    , dx_days
    , return_amt
    , return_qty
    , return_sku_qty
    , return_spu_qty
    , return_branch_qty
    , return_supplier_cost_amt
    , return_profit_amt
    , return_profit_partner_amt
    , return_profit_dc_amt
    , return_profit_colonel_amt
    , return_profit_colonel1_amt
    , return_profit_colonel2_amt
    , pre_return_amt
    , return_activity_discount_amt
    , return_coupon_discount_amt
    , return_coupon_discount_amt2
    , return_discount_amt
    , return_profit_rate
    , insert_date_id
)
SELECT
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.branch_id,
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.sku_num) AS dx_sku_qty,
    SUM(updateTable.spu_num) AS dx_spu_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.supplier_amt) AS supplier_cost_amt,
    SUM(updateTable.profit_amt) AS profit_amt,
    SUM(updateTable.partner_amt) AS profit_partner_amt,
    SUM(updateTable.dc_amt) AS profit_dc_amt,
    SUM(updateTable.colonel1_amt + updateTable.colonel2_amt) AS profit_colonel_amt,
    SUM(updateTable.colonel1_amt) AS profit_colonel1_amt,
    SUM(updateTable.colonel2_amt) AS profit_colonel2_amt,
    SUM(updateTable.pre_order_amt) AS pre_order_amt,
    SUM(updateTable.activity_discount_amt) AS activity_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt2,
    SUM(updateTable.activity_discount_amt + updateTable.coupon_discount_amt + updateTable.coupon_discount_amt2) AS discount_amt,
    CASE
        WHEN SUM(updateTable.order_amt) <= 0 THEN 0
        ELSE  ROUND(IFNULL((SUM(updateTable.order_amt) - SUM(updateTable.supplier_amt)) / SUM(updateTable.order_amt) * 100, 0), 6)
    END AS profit_rate,
    COUNT(updateTable.dx_days) AS dx_days,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.after_num) AS return_qty,
    SUM(updateTable.return_sku_num) AS return_sku_qty,
    SUM(updateTable.return_spu_num) AS return_spu_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    SUM(updateTable.return_supplier_amt) AS return_supplier_cost_amt,
    SUM(updateTable.return_profit) AS return_profit,
    SUM(updateTable.return_profit_partner) AS return_profit_partner_amt,
    SUM(updateTable.return_profit_dc) AS return_profit_dc_amt,
    SUM(updateTable.return_profit_colonel1 + updateTable.return_profit_colonel2) AS return_profit_colonel_amt,
    SUM(updateTable.return_profit_colonel1) AS return_profit_colonel1_amt,
    SUM(updateTable.return_profit_colonel2) AS return_profit_colonel2_amt,
    SUM(updateTable.pre_return_amt) AS pre_return_amt,
    SUM(updateTable.return_activity_discount_amt) AS return_activity_discount_amt,
    SUM(updateTable.return_coupon_discount_amt) AS return_coupon_discount_amt,
    SUM(updateTable.return_coupon_discount_amt2) AS return_coupon_discount_amt2,
    SUM(updateTable.return_activity_discount_amt + updateTable.return_coupon_discount_amt + updateTable.return_coupon_discount_amt2) AS return_discount_amt,
    CASE
        WHEN SUM(updateTable.return_amt) <= 0 THEN 0
        ELSE ROUND(IFNULL((SUM(updateTable.return_amt) - SUM(updateTable.return_supplier_amt)) / SUM(updateTable.return_amt) * 100, 0), 6)
    END AS return_profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            SUBSTRING( dtodi.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtodi.sys_code, -- 平台商编号
            dtodi.area_id, -- 区域ID
            dtodi.branch_id,
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            COUNT(DISTINCT dtodi.sku_id) AS sku_num, -- SKU数量
            COUNT(DISTINCT dtodi.spu_id) AS spu_num, -- SPU数量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            IFNULL(SUM(dtodi.supplier_amt) - SUM(IFNULL(dtci.supplier_amt, 0)), 0) AS supplier_amt, -- 入驻商成本金额
            IFNULL(SUM(dtodi.profit) - SUM(IFNULL(dtci.profit, 0)), 0) AS profit_amt, -- 利润金额（减去发货前售后）
            IFNULL(SUM(dtodi.partner_amt) - SUM(IFNULL(dtci.partner_amt, 0)), 0) AS partner_amt, -- 平台商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.dc_amt) - SUM(IFNULL(dtci.dc_amt, 0)), 0) AS dc_amt, -- 运营商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel1_amt) - SUM(IFNULL(dtci.colonel1_amt, 0)), 0) AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel2_amt) - SUM(IFNULL(dtci.colonel2_amt, 0)), 0) AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.order_amt) - SUM(IFNULL(dtci.cancel_amt, 0)), 0) AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            IFNULL(SUM(dtodi.activity_discount_amt) - SUM(IFNULL(dtci.cancel_activity_discount_amt, 0)), 0) AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt) - SUM(IFNULL(dtci.cancel_coupon_discount_amt, 0)), 0) AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt2) - SUM(IFNULL(dtci.cancel_coupon_discount_amt2, 0)), 0) AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            COUNT(DISTINCT dtodi.date_id) AS dx_days, -- 门店月动销天数
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS after_num, -- 售后单数量（收货后）
            0 AS return_sku_num, -- 售后SKU数量（收货后）
            0 AS return_spu_num, -- 售后SPU数量（收货后）
            0 AS return_branch_num, -- 售后门店数量（收货后）
            0 AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            0 AS return_profit, -- 售后利润（收货后）
            0 AS return_profit_partner, -- 平台商售后分润金额（收货后）
            0 AS return_profit_dc, -- 运营商售后分润金额（收货后）
            0 AS return_profit_colonel1, -- 业务员管理员售后分润金额（收货后）
            0 AS return_profit_colonel2, -- 业务员售后分润金额（收货后）
            0 AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            0 AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            0 AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            0 AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    SUM( return_amt ) return_amt,
                    SUM( refund_amt ) refund_amt,
                    SUM( cancel_amt ) cancel_amt,
                    SUM( cancel_qty ) cancel_qty,
                    SUM( supplier_amt ) supplier_amt,
                    SUM( profit ) profit,
                    SUM( partner_amt ) partner_amt,
                    SUM( dc_amt ) dc_amt,
                    SUM( colonel1_amt ) colonel1_amt,
                    SUM( colonel2_amt ) colonel2_amt,
                    SUM( cancel_activity_discount_amt ) cancel_activity_discount_amt,
                    SUM( cancel_coupon_discount_amt ) cancel_coupon_discount_amt,
                    SUM( cancel_coupon_discount_amt2 ) cancel_coupon_discount_amt2
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    SUBSTRING( order_date_id , 1, 6 ) in (
                        SELECT SUBSTRING( order_date_id , 1, 6 ) FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            SUBSTRING( dtodi.date_id , 1, 6 ) in (
                SELECT
                    orderdate.order_date_id
                FROM
                    (
                        SELECT SUBSTRING( date_id , 1, 6 )  AS order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id , 1, 6 )
                        UNION
                        SELECT SUBSTRING( order_date_id , 1, 6 )  AS order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            SUBSTRING( dtodi.date_id , 1, 6 ), -- 年月ID
            dtodi.sys_code,
            dtodi.area_id,
            dtodi.branch_id

        UNION ALL

        SELECT
            SUBSTRING( dtri.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.branch_id,
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS order_num, -- 订单数量
            0 AS sku_num, -- SKU数量
            0 AS spu_num, -- SPU数量
            0 AS branch_num, -- 门店数量
            0 AS supplier_amt, -- 入驻商成本金额
            0 AS profit_amt, -- 利润金额（减去发货前售后）
            0 AS partner_amt, -- 平台上分润金额（减去发货前售后）
            0 AS dc_amt, -- 运营上分润金额（减去发货前售后）
            0 AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            0 AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            0 AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            0 AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            0 AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            0 AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            0 AS dx_days, -- 门店动销天数
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS after_num, -- 退单数量（收货后）
            COUNT(DISTINCT dtri.sku_id) AS return_sku_num, -- 售后SKU数量（收货后）
            COUNT(DISTINCT dtri.spu_id) AS return_spu_num, -- 售后SPU数量（收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num, -- 售后门店数量（收货后）
            IFNULL(SUM(dtri.supplier_amt), 0)AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            IFNULL(SUM(dtri.profit), 0) AS return_profit, -- 售后利润（收货后）
            IFNULL(SUM(dtri.partner_amt), 0) AS return_profit_partner_amt, -- 平台商售后分润金额（收货后）
            IFNULL(SUM(dtri.dc_amt), 0) AS return_profit_dc_amt, -- 运营商售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel1_amt), 0) AS return_profit_colonel1_amt, -- 业务员管理员售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel2_amt), 0) AS return_profit_colonel2_amt, -- 业务员售后分润金额（收货后）
            SUM(dtri.return_order_amt) AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            IFNULL(SUM(dtri.return_activity_discount_amt), 0) AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt), 0) AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt2), 0) AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_return_inc dtri
        WHERE
            SUBSTRING( date_id, 1, 6 ) in (
                SELECT SUBSTRING( date_id, 1, 6 )  FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id, 1, 6 )
            )
        GROUP BY
            SUBSTRING( dtri.date_id , 1, 6 ), -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.branch_id
    ) updateTable
GROUP BY
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.branch_id
-- =======================================================交易域城市粒度时段下单月汇总表 （dws_trd_area_hours_sales_month）==========================
replace into dws_trd_area_hours_sales_month (
    month_id
    , sys_code
    , area_id
    , hour
	, order_amt
	, order_qty
    , dx_sku_qty
    , dx_spu_qty
    , dx_branch_qty
	, return_amt
	, return_qty
	, return_sku_qty
    , return_spu_qty
    , return_branch_qty
	, insert_date_id
)
SELECT
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.month_hour,
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.sku_num) AS dx_sku_qty,
    SUM(updateTable.spu_num) AS dx_spu_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.after_num) AS return_qty,
    SUM(updateTable.return_sku_num) AS return_sku_qty,
    SUM(updateTable.return_spu_num) AS return_spu_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            SUBSTRING( dtodi.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtodi.sys_code, -- 平台商编号
            dtodi.area_id, -- 区域ID
			DATE_FORMAT(dtodi.order_time, '%H') AS month_hour,
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            COUNT(DISTINCT dtodi.sku_id) AS sku_num, -- SKU数量
            COUNT(DISTINCT dtodi.spu_id) AS spu_num, -- SPU数量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS after_num, -- 售后单数量（收货后）
            0 AS return_sku_num, -- 售后SKU数量（收货后）
            0 AS return_spu_num, -- 售后SPU数量（收货后）
            0 AS return_branch_num -- 售后门店数量（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    SUM( refund_amt ) refund_amt,
                    SUM( cancel_qty ) cancel_qty
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    SUBSTRING( order_date_id , 1, 6 ) in (
                        SELECT SUBSTRING( order_date_id , 1, 6 ) FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            SUBSTRING( dtodi.date_id , 1, 6 ) in (
                SELECT
                    orderdate.order_date_id
                FROM
                    (
                        SELECT SUBSTRING( date_id , 1, 6 )  AS order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id , 1, 6 )
                        UNION
                        SELECT SUBSTRING( order_date_id , 1, 6 )  AS order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            SUBSTRING( dtodi.date_id , 1, 6 ), -- 年月ID
            dtodi.sys_code,
            dtodi.area_id,
			DATE_FORMAT(dtodi.order_time, '%H') -- 时

        UNION ALL

        SELECT
            SUBSTRING( dtri.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            DATE_FORMAT(dtri.order_time, '%H') AS month_hour,
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS order_num, -- 订单数量
            0 AS sku_num, -- SKU数量
            0 AS spu_num, -- SPU数量
            0 AS branch_num, -- 门店数量
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS after_num, -- 退单数量（收货后）
            COUNT(DISTINCT dtri.sku_id) AS return_sku_num, -- 售后SKU数量（收货后）
            COUNT(DISTINCT dtri.spu_id) AS return_spu_num, -- 售后SPU数量（收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num -- 售后门店数量（收货后）
        FROM
            dwd_trd_return_inc dtri
        WHERE
            SUBSTRING( date_id, 1, 6 ) in (
                SELECT SUBSTRING( date_id, 1, 6 )  FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id, 1, 6 )
            )
        GROUP BY
            SUBSTRING( dtri.date_id , 1, 6 ), -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            DATE_FORMAT(dtri.order_time, '%H')
    ) updateTable
GROUP BY
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.month_hour

-- ============================================================交易域门店粒度时段下单月汇总表 （dws_trd_branch_hours_sales_month）===========================
replace into dws_trd_branch_hours_sales_month (
    month_id
    , sys_code
    , branch_id
    , hour
	, order_amt
	, order_qty
    , dx_sku_qty
    , dx_spu_qty
	, return_amt
	, return_qty
	, return_sku_qty
    , return_spu_qty
	, insert_date_id
)
SELECT
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.branch_id,
    updateTable.month_hour,
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.sku_num) AS dx_sku_qty,
    SUM(updateTable.spu_num) AS dx_spu_qty,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.after_num) AS return_qty,
    SUM(updateTable.return_sku_num) AS return_sku_qty,
    SUM(updateTable.return_spu_num) AS return_spu_qty,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            SUBSTRING( dtodi.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtodi.sys_code, -- 平台商编号
            dtodi.branch_id, -- 门店ID
			DATE_FORMAT(dtodi.order_time, '%H') AS month_hour,
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            COUNT(DISTINCT dtodi.sku_id) AS sku_num, -- SKU数量
            COUNT(DISTINCT dtodi.spu_id) AS spu_num, -- SPU数量
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS after_num, -- 售后单数量（收货后）
            0 AS return_sku_num, -- 售后SKU数量（收货后）
            0 AS return_spu_num -- 售后SPU数量（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    SUM( refund_amt ) refund_amt,
                    SUM( cancel_qty ) cancel_qty
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    SUBSTRING( order_date_id , 1, 6 ) in (
                        SELECT SUBSTRING( order_date_id , 1, 6 ) FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            SUBSTRING( dtodi.date_id , 1, 6 ) in (
                SELECT
                    orderdate.order_date_id
                FROM
                    (
                        SELECT SUBSTRING( date_id , 1, 6 )  AS order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id , 1, 6 )
                        UNION
                        SELECT SUBSTRING( order_date_id , 1, 6 )  AS order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( order_date_id , 1, 6 )
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            SUBSTRING( dtodi.date_id , 1, 6 ), -- 年月ID
            dtodi.sys_code,
            dtodi.branch_id,
			DATE_FORMAT(dtodi.order_time, '%H') -- 时

        UNION ALL

        SELECT
            SUBSTRING( dtri.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.branch_id, -- 门店ID
            DATE_FORMAT(dtri.order_time, '%H') AS month_hour,
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS order_num, -- 订单数量
            0 AS sku_num, -- SKU数量
            0 AS spu_num, -- SPU数量
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS after_num, -- 退单数量（收货后）
            COUNT(DISTINCT dtri.sku_id) AS return_sku_num, -- 售后SKU数量（收货后）
            COUNT(DISTINCT dtri.spu_id) AS return_spu_num -- 售后SPU数量（收货后）
        FROM
            dwd_trd_return_inc dtri
        WHERE
            SUBSTRING( date_id, 1, 6 ) in (
                SELECT SUBSTRING( date_id, 1, 6 )  FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY SUBSTRING( date_id, 1, 6 )
            )
        GROUP BY
            SUBSTRING( dtri.date_id , 1, 6 ), -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.branch_id, -- 门店ID
            DATE_FORMAT(dtri.order_time, '%H')
    ) updateTable
GROUP BY
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.branch_id,
    updateTable.month_hour


-- ============================================================交易域SKU粒度下单日汇总表(dws_trd_sku_sales_day)=================
replace into dws_trd_sku_sales_day (
    date_id
    , sys_code
    , area_id
    , sku_id
    , spu_id
    , supplier_id
    , order_amt
    , order_qty
    , sku_sales_qty
    , dx_branch_qty
    , supplier_cost_amt
    , profit_amt
    , profit_partner_amt
    , profit_dc_amt
    , profit_colonel_amt
    , profit_colonel1_amt
    , profit_colonel2_amt
    , pre_order_amt
    , activity_discount_amt
    , coupon_discount_amt
    , coupon_discount_amt2
    , discount_amt
    , profit_rate
    , return_amt
    , return_qty
    , sku_return_qty
    , return_branch_qty
    , return_supplier_cost_amt
    , return_profit_amt
    , return_profit_partner_amt
    , return_profit_dc_amt
    , return_profit_colonel_amt
    , return_profit_colonel1_amt
    , return_profit_colonel2_amt
    , pre_return_amt
    , return_activity_discount_amt
    , return_coupon_discount_amt
    , return_coupon_discount_amt2
    , return_discount_amt
    , return_profit_rate
    , insert_date_id
)
SELECT
    updateTable.date_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.sku_id,
    updateTable.spu_id,
    updateTable.supplier_id,
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.sku_sales_qty) AS sku_sales_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.supplier_amt) AS supplier_cost_amt,
    SUM(updateTable.profit_amt) AS profit_amt,
    SUM(updateTable.partner_amt) AS profit_partner_amt,
    SUM(updateTable.dc_amt) AS profit_dc_amt,
    SUM(updateTable.colonel1_amt + updateTable.colonel2_amt) AS profit_colonel_amt,
    SUM(updateTable.colonel1_amt) AS profit_colonel1_amt,
    SUM(updateTable.colonel2_amt) AS profit_colonel2_amt,
    SUM(updateTable.pre_order_amt) AS pre_order_amt,
    SUM(updateTable.activity_discount_amt) AS activity_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt2,
    SUM(updateTable.activity_discount_amt + updateTable.coupon_discount_amt + updateTable.coupon_discount_amt2) AS discount_amt,
    CASE
        WHEN SUM(updateTable.order_amt) <= 0 THEN 0
        ELSE  ROUND(IFNULL((SUM(updateTable.order_amt) - SUM(updateTable.supplier_amt)) / SUM(updateTable.order_amt) * 100, 0), 6)
    END AS profit_rate,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.return_num) AS return_qty,
    SUM(updateTable.sku_return_qty) AS sku_return_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    SUM(updateTable.return_supplier_amt) AS return_supplier_cost_amt,
    SUM(updateTable.return_profit) AS return_profit,
    SUM(updateTable.return_profit_partner) AS return_profit_partner_amt,
    SUM(updateTable.return_profit_dc) AS return_profit_dc_amt,
    SUM(updateTable.return_profit_colonel1 + updateTable.return_profit_colonel2) AS return_profit_colonel_amt,
    SUM(updateTable.return_profit_colonel1) AS return_profit_colonel1_amt,
    SUM(updateTable.return_profit_colonel2) AS return_profit_colonel2_amt,
    SUM(updateTable.pre_return_amt) AS pre_return_amt,
    SUM(updateTable.return_activity_discount_amt) AS return_activity_discount_amt,
    SUM(updateTable.return_coupon_discount_amt) AS return_coupon_discount_amt,
    SUM(updateTable.return_coupon_discount_amt2) AS return_coupon_discount_amt2,
    SUM(updateTable.return_activity_discount_amt + updateTable.return_coupon_discount_amt + updateTable.return_coupon_discount_amt2) AS return_discount_amt,
    CASE
        WHEN SUM(updateTable.return_amt) <= 0 THEN 0
        ELSE ROUND(IFNULL((SUM(updateTable.return_amt) - SUM(updateTable.return_supplier_amt)) / SUM(updateTable.return_amt) * 100, 0), 6)
    END AS return_profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            dtodi.date_id, -- 日期ID
            dtodi.sys_code, -- 平台商编号
            dtodi.area_id, -- 区域ID
            dtodi.sku_id,
            dtodi.spu_id,
            dtodi.supplier_id,
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            IFNULL(SUM(dtodi.total_num) - SUM(IFNULL(dtci.cancel_qty, 0)), 0) AS sku_sales_qty, -- 最小单位销量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            IFNULL(SUM(dtodi.supplier_amt) - SUM(IFNULL(dtci.supplier_amt, 0)), 0) AS supplier_amt, -- 入驻商成本金额
            IFNULL(SUM(dtodi.profit) - SUM(IFNULL(dtci.profit, 0)), 0) AS profit_amt, -- 利润金额（减去发货前售后）
            IFNULL(SUM(dtodi.partner_amt) - SUM(IFNULL(dtci.partner_amt, 0)), 0) AS partner_amt, -- 平台商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.dc_amt) - SUM(IFNULL(dtci.dc_amt, 0)), 0) AS dc_amt, -- 运营商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel1_amt) - SUM(IFNULL(dtci.colonel1_amt, 0)), 0) AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel2_amt) - SUM(IFNULL(dtci.colonel2_amt, 0)), 0) AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.order_amt) - SUM(IFNULL(dtci.cancel_amt, 0)), 0) AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            IFNULL(SUM(dtodi.activity_discount_amt) - SUM(IFNULL(dtci.cancel_activity_discount_amt, 0)), 0) AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt) - SUM(IFNULL(dtci.cancel_coupon_discount_amt, 0)), 0) AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt2) - SUM(IFNULL(dtci.cancel_coupon_discount_amt2, 0)), 0) AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS return_num, -- 售后单数量（收货后）
            0 AS sku_return_qty, -- 退货最小单位数量（收货后）
            0 AS return_branch_num, -- 售后门店数量（收货后）
            0 AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            0 AS return_profit, -- 售后利润（收货后）
            0 AS return_profit_partner, -- 平台商售后分润金额（收货后）
            0 AS return_profit_dc, -- 运营商售后分润金额（收货后）
            0 AS return_profit_colonel1, -- 业务员管理员售后分润金额（收货后）
            0 AS return_profit_colonel2, -- 业务员售后分润金额（收货后）
            0 AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            0 AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            0 AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            0 AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    sum( return_amt ) return_amt,
                    sum( refund_amt ) refund_amt,
                    sum( cancel_amt ) cancel_amt,
                    sum( cancel_qty ) cancel_qty,
                    sum( supplier_amt ) supplier_amt,
                    sum( profit ) profit,
                    sum( partner_amt ) partner_amt,
                    sum( dc_amt ) dc_amt,
                    sum( colonel1_amt ) colonel1_amt,
                    sum( colonel2_amt ) colonel2_amt,
                    sum( cancel_activity_discount_amt ) cancel_activity_discount_amt,
                    sum( cancel_coupon_discount_amt ) cancel_coupon_discount_amt,
                    sum( cancel_coupon_discount_amt2 ) cancel_coupon_discount_amt2
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    order_date_id in (
                        SELECT order_date_id as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
            LEFT JOIN dim_sku ds ON dtodi.sku_id = ds.sku_id
        WHERE
            dtodi.date_id in (
                SELECT
                    orderdate.order_date_id as order_date_id
                FROM
                    (
                        SELECT date_id as order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY date_id
                        UNION
                        SELECT order_date_id as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            dtodi.date_id, -- 日期ID
            dtodi.sys_code, -- 平台商编号
            dtodi.area_id, -- 区域ID
            dtodi.sku_id, -- sku编号
            dtodi.spu_id, -- spu编号
            dtodi.supplier_id -- 入驻商编号

        UNION ALL

        SELECT
            dtri.date_id, -- 日期ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.sku_id, -- sku编号
            dtri.spu_id, -- spu编号
            dtri.supplier_id, -- 入驻商编号
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS order_num, -- 订单数量
            0 AS sku_sales_qty, -- 最小单位销量
            0 AS branch_num, -- 门店数量
            0 AS supplier_amt, -- 入驻商成本金额
            0 AS profit_amt, -- 利润金额（减去发货前售后）
            0 AS partner_amt, -- 平台上分润金额（减去发货前售后）
            0 AS dc_amt, -- 运营上分润金额（减去发货前售后）
            0 AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            0 AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            0 AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            0 AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            0 AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            0 AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS return_num, -- 退单数量（收货后）
            IFNULL(SUM(dtri.return_qty), 0) AS sku_return_qty, -- 退货最小单位数量 （收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num, -- 售后门店数量（收货后）
            IFNULL(SUM(dtri.supplier_amt), 0)AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            IFNULL(SUM(dtri.profit), 0) AS return_profit, -- 售后利润（收货后）
            IFNULL(SUM(dtri.partner_amt), 0) AS return_profit_partner_amt, -- 平台商售后分润金额（收货后）
            IFNULL(SUM(dtri.dc_amt), 0) AS return_profit_dc_amt, -- 运营商售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel1_amt), 0) AS return_profit_colonel1_amt, -- 业务员管理员售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel2_amt), 0) AS return_profit_colonel2_amt, -- 业务员售后分润金额（收货后）
            SUM(dtri.return_order_amt) AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            IFNULL(SUM(dtri.return_activity_discount_amt), 0) AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt), 0) AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt2), 0) AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_return_inc dtri
            LEFT JOIN dim_sku ds ON dtri.sku_id = ds.sku_id
        WHERE
            dtri.date_id in (
                SELECT date_id FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
            )
        GROUP BY
            dtri.date_id, -- 日期ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.sku_id, -- sku编号
            dtri.spu_id, -- spu编号
            dtri.supplier_id -- 入驻商编号
    ) updateTable
GROUP BY
    updateTable.date_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.sku_id,
    updateTable.spu_id,
    updateTable.supplier_id


-- ============================================================交易域SKU粒度下单月汇总表(dws_trd_sku_sales_month)=================
replace into dws_trd_sku_sales_month (
    month_id
    , sys_code
    , area_id
    , sku_id
    , spu_id
    , supplier_id
    , order_amt
    , order_qty
    , sku_sales_qty
    , dx_branch_qty
    , supplier_cost_amt
    , profit_amt
    , profit_partner_amt
    , profit_dc_amt
    , profit_colonel_amt
    , profit_colonel1_amt
    , profit_colonel2_amt
    , pre_order_amt
    , activity_discount_amt
    , coupon_discount_amt
    , coupon_discount_amt2
    , discount_amt
    , profit_rate
    , return_amt
    , return_qty
    , sku_return_qty
    , return_branch_qty
    , return_supplier_cost_amt
    , return_profit_amt
    , return_profit_partner_amt
    , return_profit_dc_amt
    , return_profit_colonel_amt
    , return_profit_colonel1_amt
    , return_profit_colonel2_amt
    , pre_return_amt
    , return_activity_discount_amt
    , return_coupon_discount_amt
    , return_coupon_discount_amt2
    , return_discount_amt
    , return_profit_rate
    , insert_date_id
)
SELECT
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.sku_id,
    updateTable.spu_id,
    updateTable.supplier_id,
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.sku_sales_qty) AS sku_sales_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.supplier_amt) AS supplier_cost_amt,
    SUM(updateTable.profit_amt) AS profit_amt,
    SUM(updateTable.partner_amt) AS profit_partner_amt,
    SUM(updateTable.dc_amt) AS profit_dc_amt,
    SUM(updateTable.colonel1_amt + updateTable.colonel2_amt) AS profit_colonel_amt,
    SUM(updateTable.colonel1_amt) AS profit_colonel1_amt,
    SUM(updateTable.colonel2_amt) AS profit_colonel2_amt,
    SUM(updateTable.pre_order_amt) AS pre_order_amt,
    SUM(updateTable.activity_discount_amt) AS activity_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt2,
    SUM(updateTable.activity_discount_amt + updateTable.coupon_discount_amt + updateTable.coupon_discount_amt2) AS discount_amt,
    CASE
        WHEN SUM(updateTable.order_amt) <= 0 THEN 0
        ELSE  ROUND(IFNULL((SUM(updateTable.order_amt) - SUM(updateTable.supplier_amt)) / SUM(updateTable.order_amt) * 100, 0), 6)
    END AS profit_rate,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.return_num) AS return_qty,
    SUM(updateTable.sku_return_qty) AS sku_return_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    SUM(updateTable.return_supplier_amt) AS return_supplier_cost_amt,
    SUM(updateTable.return_profit) AS return_profit,
    SUM(updateTable.return_profit_partner) AS return_profit_partner_amt,
    SUM(updateTable.return_profit_dc) AS return_profit_dc_amt,
    SUM(updateTable.return_profit_colonel1 + updateTable.return_profit_colonel2) AS return_profit_colonel_amt,
    SUM(updateTable.return_profit_colonel1) AS return_profit_colonel1_amt,
    SUM(updateTable.return_profit_colonel2) AS return_profit_colonel2_amt,
    SUM(updateTable.pre_return_amt) AS pre_return_amt,
    SUM(updateTable.return_activity_discount_amt) AS return_activity_discount_amt,
    SUM(updateTable.return_coupon_discount_amt) AS return_coupon_discount_amt,
    SUM(updateTable.return_coupon_discount_amt2) AS return_coupon_discount_amt2,
    SUM(updateTable.return_activity_discount_amt + updateTable.return_coupon_discount_amt + updateTable.return_coupon_discount_amt2) AS return_discount_amt,
    CASE
        WHEN SUM(updateTable.return_amt) <= 0 THEN 0
        ELSE ROUND(IFNULL((SUM(updateTable.return_amt) - SUM(updateTable.return_supplier_amt)) / SUM(updateTable.return_amt) * 100, 0), 6)
    END AS return_profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            SUBSTRING( dtodi.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtodi.sys_code, -- 平台商编号
            dtodi.area_id, -- 区域ID
            dtodi.sku_id,
            dtodi.spu_id,
            dtodi.supplier_id,
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            IFNULL(SUM(dtodi.total_num) - SUM(IFNULL(dtci.cancel_qty, 0)), 0) AS sku_sales_qty, -- 最小单位销量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            IFNULL(SUM(dtodi.supplier_amt) - SUM(IFNULL(dtci.supplier_amt, 0)), 0) AS supplier_amt, -- 入驻商成本金额
            IFNULL(SUM(dtodi.profit) - SUM(IFNULL(dtci.profit, 0)), 0) AS profit_amt, -- 利润金额（减去发货前售后）
            IFNULL(SUM(dtodi.partner_amt) - SUM(IFNULL(dtci.partner_amt, 0)), 0) AS partner_amt, -- 平台商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.dc_amt) - SUM(IFNULL(dtci.dc_amt, 0)), 0) AS dc_amt, -- 运营商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel1_amt) - SUM(IFNULL(dtci.colonel1_amt, 0)), 0) AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel2_amt) - SUM(IFNULL(dtci.colonel2_amt, 0)), 0) AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.order_amt) - SUM(IFNULL(dtci.cancel_amt, 0)), 0) AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            IFNULL(SUM(dtodi.activity_discount_amt) - SUM(IFNULL(dtci.cancel_activity_discount_amt, 0)), 0) AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt) - SUM(IFNULL(dtci.cancel_coupon_discount_amt, 0)), 0) AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt2) - SUM(IFNULL(dtci.cancel_coupon_discount_amt2, 0)), 0) AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS return_num, -- 售后单数量（收货后）
            0 AS sku_return_qty, -- 退货最小单位数量（收货后）
            0 AS return_branch_num, -- 售后门店数量（收货后）
            0 AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            0 AS return_profit, -- 售后利润（收货后）
            0 AS return_profit_partner, -- 平台商售后分润金额（收货后）
            0 AS return_profit_dc, -- 运营商售后分润金额（收货后）
            0 AS return_profit_colonel1, -- 业务员管理员售后分润金额（收货后）
            0 AS return_profit_colonel2, -- 业务员售后分润金额（收货后）
            0 AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            0 AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            0 AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            0 AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    sum( return_amt ) return_amt,
                    sum( refund_amt ) refund_amt,
                    sum( cancel_amt ) cancel_amt,
                    sum( cancel_qty ) cancel_qty,
                    sum( supplier_amt ) supplier_amt,
                    sum( profit ) profit,
                    sum( partner_amt ) partner_amt,
                    sum( dc_amt ) dc_amt,
                    sum( colonel1_amt ) colonel1_amt,
                    sum( colonel2_amt ) colonel2_amt,
                    sum( cancel_activity_discount_amt ) cancel_activity_discount_amt,
                    sum( cancel_coupon_discount_amt ) cancel_coupon_discount_amt,
                    sum( cancel_coupon_discount_amt2 ) cancel_coupon_discount_amt2
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    SUBSTRING( order_date_id , 1, 6 ) in (
                        SELECT SUBSTRING( order_date_id , 1, 6 ) as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
            LEFT JOIN dim_sku ds ON dtodi.sku_id = ds.sku_id
        WHERE
            SUBSTRING( dtodi.date_id , 1, 6 ) in (
                SELECT
                    orderdate.order_date_id as order_date_id
                FROM
                    (
                        SELECT SUBSTRING( date_id , 1, 6 ) as order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY date_id
                        UNION
                        SELECT SUBSTRING( order_date_id , 1, 6 ) as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            SUBSTRING( dtodi.date_id , 1, 6 ), -- 年月ID
            dtodi.sys_code, -- 平台商编号
            dtodi.area_id, -- 区域ID
            dtodi.sku_id,
            dtodi.spu_id,
            dtodi.supplier_id

        UNION ALL

        SELECT
            SUBSTRING( dtri.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.sku_id,
            dtri.spu_id,
            dtri.supplier_id,
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS order_num, -- 订单数量
            0 AS sku_sales_qty, -- 最小单位销量
            0 AS branch_num, -- 门店数量
            0 AS supplier_amt, -- 入驻商成本金额
            0 AS profit_amt, -- 利润金额（减去发货前售后）
            0 AS partner_amt, -- 平台上分润金额（减去发货前售后）
            0 AS dc_amt, -- 运营上分润金额（减去发货前售后）
            0 AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            0 AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            0 AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            0 AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            0 AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            0 AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS return_num, -- 退单数量（收货后）
            IFNULL(SUM(dtri.return_qty), 0) AS sku_return_qty, -- 退货最小单位数量 （收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num, -- 售后门店数量（收货后）
            IFNULL(SUM(dtri.supplier_amt), 0)AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            IFNULL(SUM(dtri.profit), 0) AS return_profit, -- 售后利润（收货后）
            IFNULL(SUM(dtri.partner_amt), 0) AS return_profit_partner_amt, -- 平台商售后分润金额（收货后）
            IFNULL(SUM(dtri.dc_amt), 0) AS return_profit_dc_amt, -- 运营商售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel1_amt), 0) AS return_profit_colonel1_amt, -- 业务员管理员售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel2_amt), 0) AS return_profit_colonel2_amt, -- 业务员售后分润金额（收货后）
            SUM(dtri.return_order_amt) AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            IFNULL(SUM(dtri.return_activity_discount_amt), 0) AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt), 0) AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt2), 0) AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_return_inc dtri
            LEFT JOIN dim_sku ds ON dtri.sku_id = ds.sku_id
        WHERE
            SUBSTRING( dtri.date_id , 1, 6 ) in (
                SELECT date_id FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
            )
        GROUP BY
            SUBSTRING( dtri.date_id , 1, 6 ), -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.sku_id,
            dtri.spu_id,
            dtri.supplier_id
    ) updateTable
GROUP BY
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.sku_id,
    updateTable.spu_id,
    updateTable.supplier_id


-- ===================================================================交易域业务员粒度下单日汇总表（dws_trd_colonel_sales_day）===========================
replace into dws_trd_colonel_sales_day (
    date_id
    , sys_code
    , area_id
    , colonel_id
    , order_amt
    , colonel_order_amt
    , branch_order_amt
    , order_qty
    , colonel_order_qty
    , branch_order_qty
    , dx_sku_qty
    , dx_spu_qty
    , dx_branch_qty
    , supplier_cost_amt
    , profit_amt
    , profit_partner_amt
    , profit_dc_amt
    , profit_colonel_amt
    , profit_colonel1_amt
    , profit_colonel2_amt
    , pre_order_amt
    , activity_discount_amt
    , coupon_discount_amt
    , coupon_discount_amt2
    , discount_amt
    , profit_rate
    , return_amt
    , return_qty
    , return_sku_qty
    , return_spu_qty
    , return_branch_qty
    , return_supplier_cost_amt
    , return_profit_amt
    , return_profit_partner_amt
    , return_profit_dc_amt
    , return_profit_colonel_amt
    , return_profit_colonel1_amt
    , return_profit_colonel2_amt
    , pre_return_amt
    , return_activity_discount_amt
    , return_coupon_discount_amt
    , return_coupon_discount_amt2
    , return_discount_amt
    , return_profit_rate
    , insert_date_id
)
SELECT
    updateTable.date_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.colonel_id,
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.colonel_order_amt) AS colonel_order_amt,
    SUM(updateTable.branch_order_amt) AS branch_order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.colonel_order_num) AS colonel_order_qty,
    SUM(updateTable.branch_order_num) AS branch_order_qty,
    SUM(updateTable.sku_num) AS dx_sku_qty,
    SUM(updateTable.spu_num) AS dx_spu_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.supplier_amt) AS supplier_cost_amt,
    SUM(updateTable.profit_amt) AS profit_amt,
    SUM(updateTable.partner_amt) AS profit_partner_amt,
    SUM(updateTable.dc_amt) AS profit_dc_amt,
    SUM(updateTable.colonel1_amt + updateTable.colonel2_amt) AS profit_colonel_amt,
    SUM(updateTable.colonel1_amt) AS profit_colonel1_amt,
    SUM(updateTable.colonel2_amt) AS profit_colonel2_amt,
    SUM(updateTable.pre_order_amt) AS pre_order_amt,
    SUM(updateTable.activity_discount_amt) AS activity_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt2,
    SUM(updateTable.activity_discount_amt + updateTable.coupon_discount_amt + updateTable.coupon_discount_amt2) AS discount_amt,
    CASE
        WHEN SUM(updateTable.order_amt) <= 0 THEN 0
        ELSE  ROUND(IFNULL((SUM(updateTable.order_amt) - SUM(updateTable.supplier_amt)) / SUM(updateTable.order_amt) * 100, 0), 6)
    END AS profit_rate,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.after_num) AS return_qty,
    SUM(updateTable.return_sku_num) AS return_sku_qty,
    SUM(updateTable.return_spu_num) AS return_spu_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    SUM(updateTable.return_supplier_amt) AS return_supplier_cost_amt,
    SUM(updateTable.return_profit) AS return_profit,
    SUM(updateTable.return_profit_partner) AS return_profit_partner_amt,
    SUM(updateTable.return_profit_dc) AS return_profit_dc_amt,
    SUM(updateTable.return_profit_colonel1 + updateTable.return_profit_colonel2) AS return_profit_colonel_amt,
    SUM(updateTable.return_profit_colonel1) AS return_profit_colonel1_amt,
    SUM(updateTable.return_profit_colonel2) AS return_profit_colonel2_amt,
    SUM(updateTable.pre_return_amt) AS pre_return_amt,
    SUM(updateTable.return_activity_discount_amt) AS return_activity_discount_amt,
    SUM(updateTable.return_coupon_discount_amt) AS return_coupon_discount_amt,
    SUM(updateTable.return_coupon_discount_amt2) AS return_coupon_discount_amt2,
    SUM(updateTable.return_activity_discount_amt + updateTable.return_coupon_discount_amt + updateTable.return_coupon_discount_amt2) AS return_discount_amt,
    CASE
        WHEN SUM(updateTable.return_amt) <= 0 THEN 0
        ELSE ROUND(IFNULL((SUM(updateTable.return_amt) - SUM(updateTable.return_supplier_amt)) / SUM(updateTable.return_amt) * 100, 0), 6)
    END AS return_profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            dtodi.date_id, -- 日期ID
            dtodi.sys_code, -- 平台商编号
            dtodi.area_id, -- 区域ID
            dtodi.colonel_id, -- 业务员ID
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            SUM(CASE WHEN dtodi.colonel_flag = 1 THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS colonel_order_amt, -- 业务员代客下单金额
            SUM(CASE WHEN dtodi.colonel_flag = 0 THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS branch_order_amt, -- 门店自主下单金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            COUNT(DISTINCT CASE WHEN dtodi.colonel_flag = 1 THEN dtodi.order_id END) AS colonel_order_num, -- 业务员代客下单数量
            COUNT(DISTINCT CASE WHEN dtodi.colonel_flag = 0 THEN dtodi.order_id END) AS branch_order_num, -- 门店自主下单数量
            COUNT(DISTINCT dtodi.sku_id) AS sku_num, -- SKU数量
            COUNT(DISTINCT dtodi.spu_id) AS spu_num, -- SPU数量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            IFNULL(SUM(dtodi.supplier_amt) - SUM(IFNULL(dtci.supplier_amt, 0)), 0) AS supplier_amt, -- 入驻商成本金额
            IFNULL(SUM(dtodi.profit) - SUM(IFNULL(dtci.profit, 0)), 0) AS profit_amt, -- 利润金额（减去发货前售后）
            IFNULL(SUM(dtodi.partner_amt) - SUM(IFNULL(dtci.partner_amt, 0)), 0) AS partner_amt, -- 平台商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.dc_amt) - SUM(IFNULL(dtci.dc_amt, 0)), 0) AS dc_amt, -- 运营商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel1_amt) - SUM(IFNULL(dtci.colonel1_amt, 0)), 0) AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel2_amt) - SUM(IFNULL(dtci.colonel2_amt, 0)), 0) AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.order_amt) - SUM(IFNULL(dtci.cancel_amt, 0)), 0) AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            IFNULL(SUM(dtodi.activity_discount_amt) - SUM(IFNULL(dtci.cancel_activity_discount_amt, 0)), 0) AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt) - SUM(IFNULL(dtci.cancel_coupon_discount_amt, 0)), 0) AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt2) - SUM(IFNULL(dtci.cancel_coupon_discount_amt2, 0)), 0) AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS after_num, -- 售后单数量（收货后）
            0 AS return_sku_num, -- 售后SKU数量（收货后）
            0 AS return_spu_num, -- 售后SPU数量（收货后）
            0 AS return_branch_num, -- 售后门店数量（收货后）
            0 AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            0 AS return_profit, -- 售后利润（收货后）
            0 AS return_profit_partner, -- 平台商售后分润金额（收货后）
            0 AS return_profit_dc, -- 运营商售后分润金额（收货后）
            0 AS return_profit_colonel1, -- 业务员管理员售后分润金额（收货后）
            0 AS return_profit_colonel2, -- 业务员售后分润金额（收货后）
            0 AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            0 AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            0 AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            0 AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    SUM( return_amt ) return_amt,
                    SUM( refund_amt ) refund_amt,
                    SUM( cancel_amt ) cancel_amt,
                    SUM( cancel_qty ) cancel_qty,
                    SUM( supplier_amt ) supplier_amt,
                    SUM( profit ) profit,
                    SUM( partner_amt ) partner_amt,
                    SUM( dc_amt ) dc_amt,
                    SUM( colonel1_amt ) colonel1_amt,
                    SUM( colonel2_amt ) colonel2_amt,
                    SUM( cancel_activity_discount_amt ) cancel_activity_discount_amt,
                    SUM( cancel_coupon_discount_amt ) cancel_coupon_discount_amt,
                    SUM( cancel_coupon_discount_amt2 ) cancel_coupon_discount_amt2
                FROM
                    dwd_trd_cancel_inc
                WHERE
                        order_date_id in (
                        SELECT order_date_id as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            dtodi.date_id in (
                SELECT
                    orderdate.order_date_id as order_date_id
                FROM
                    (
                        SELECT date_id as order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY date_id
                        union
                        SELECT order_date_id as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
            AND dtodi.colonel_id is not null
        GROUP BY
            dtodi.date_id,
            dtodi.sys_code,
            dtodi.area_id,
            dtodi.colonel_id

        UNION ALL

        SELECT
            dtri.date_id, -- 日期ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.colonel_id, -- 业务员ID
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS colonel_order_amt, -- 业务员代客下单金额
            0 AS branch_order_amt, -- 门店自主下单金额
            0 AS order_num, -- 订单数量
            0 AS colonel_order_num, -- 业务员代客下单数量
            0 AS branch_order_num, -- 门店自主下单数量
            0 AS sku_num, -- SKU数量
            0 AS spu_num, -- SPU数量
            0 AS branch_num, -- 门店数量
            0 AS supplier_amt, -- 入驻商成本金额
            0 AS profit_amt, -- 利润金额（减去发货前售后）
            0 AS partner_amt, -- 平台上分润金额（减去发货前售后）
            0 AS dc_amt, -- 运营上分润金额（减去发货前售后）
            0 AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            0 AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            0 AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            0 AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            0 AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            0 AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS after_num, -- 退单数量（收货后）
            COUNT(DISTINCT dtri.sku_id) AS return_sku_num, -- 售后SKU数量（收货后）
            COUNT(DISTINCT dtri.spu_id) AS return_spu_num, -- 售后SPU数量（收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num, -- 售后门店数量（收货后）
            IFNULL(SUM(dtri.supplier_amt), 0)AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            IFNULL(SUM(dtri.profit), 0) AS return_profit, -- 售后利润（收货后）
            IFNULL(SUM(dtri.partner_amt), 0) AS return_profit_partner_amt, -- 平台商售后分润金额（收货后）
            IFNULL(SUM(dtri.dc_amt), 0) AS return_profit_dc_amt, -- 运营商售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel1_amt), 0) AS return_profit_colonel1_amt, -- 业务员管理员售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel2_amt), 0) AS return_profit_colonel2_amt, -- 业务员售后分润金额（收货后）
            SUM(dtri.return_order_amt) AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            IFNULL(SUM(dtri.return_activity_discount_amt), 0) AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt), 0) AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt2), 0) AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_return_inc dtri
        WHERE
            date_id in (
                SELECT date_id FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
            )
            AND dtri.colonel_id is not null
        GROUP BY
            dtri.date_id, -- 日期ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.colonel_id -- 业务员ID
    ) updateTable
GROUP BY
    updateTable.date_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.colonel_id

-- ========================================================================交易域业务员粒度下单月汇总表（dws_trd_colonel_sales_month）========================
replace into dws_trd_colonel_sales_month (
    month_id
    , sys_code
    , area_id
    , colonel_id
    , order_amt
    , colonel_order_amt
    , branch_order_amt
    , order_qty
    , colonel_order_qty
    , branch_order_qty
    , dx_sku_qty
    , dx_spu_qty
    , dx_branch_qty
    , supplier_cost_amt
    , profit_amt
    , profit_partner_amt
    , profit_dc_amt
    , profit_colonel_amt
    , profit_colonel1_amt
    , profit_colonel2_amt
    , pre_order_amt
    , activity_discount_amt
    , coupon_discount_amt
    , coupon_discount_amt2
    , discount_amt
    , profit_rate
    , return_amt
    , return_qty
    , return_sku_qty
    , return_spu_qty
    , return_branch_qty
    , return_supplier_cost_amt
    , return_profit_amt
    , return_profit_partner_amt
    , return_profit_dc_amt
    , return_profit_colonel_amt
    , return_profit_colonel1_amt
    , return_profit_colonel2_amt
    , pre_return_amt
    , return_activity_discount_amt
    , return_coupon_discount_amt
    , return_coupon_discount_amt2
    , return_discount_amt
    , return_profit_rate
    , insert_date_id
)
SELECT
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.colonel_id,
    SUM(updateTable.order_amt) AS order_amt,
    SUM(updateTable.colonel_order_amt) AS colonel_order_amt,
    SUM(updateTable.branch_order_amt) AS branch_order_amt,
    SUM(updateTable.order_num) AS order_qty,
    SUM(updateTable.colonel_order_num) AS colonel_order_qty,
    SUM(updateTable.branch_order_num) AS branch_order_qty,
    SUM(updateTable.sku_num) AS dx_sku_qty,
    SUM(updateTable.spu_num) AS dx_spu_qty,
    SUM(updateTable.branch_num) AS dx_branch_qty,
    SUM(updateTable.supplier_amt) AS supplier_cost_amt,
    SUM(updateTable.profit_amt) AS profit_amt,
    SUM(updateTable.partner_amt) AS profit_partner_amt,
    SUM(updateTable.dc_amt) AS profit_dc_amt,
    SUM(updateTable.colonel1_amt + updateTable.colonel2_amt) AS profit_colonel_amt,
    SUM(updateTable.colonel1_amt) AS profit_colonel1_amt,
    SUM(updateTable.colonel2_amt) AS profit_colonel2_amt,
    SUM(updateTable.pre_order_amt) AS pre_order_amt,
    SUM(updateTable.activity_discount_amt) AS activity_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt,
    SUM(updateTable.coupon_discount_amt) AS coupon_discount_amt2,
    SUM(updateTable.activity_discount_amt + updateTable.coupon_discount_amt + updateTable.coupon_discount_amt2) AS discount_amt,
    CASE
        WHEN SUM(updateTable.order_amt) <= 0 THEN 0
        ELSE  ROUND(IFNULL((SUM(updateTable.order_amt) - SUM(updateTable.supplier_amt)) / SUM(updateTable.order_amt) * 100, 0), 6)
    END AS profit_rate,
    SUM(updateTable.return_amt) AS return_amt,
    SUM(updateTable.after_num) AS return_qty,
    SUM(updateTable.return_sku_num) AS return_sku_qty,
    SUM(updateTable.return_spu_num) AS return_spu_qty,
    SUM(updateTable.return_branch_num) AS return_branch_qty,
    SUM(updateTable.return_supplier_amt) AS return_supplier_cost_amt,
    SUM(updateTable.return_profit) AS return_profit,
    SUM(updateTable.return_profit_partner) AS return_profit_partner_amt,
    SUM(updateTable.return_profit_dc) AS return_profit_dc_amt,
    SUM(updateTable.return_profit_colonel1 + updateTable.return_profit_colonel2) AS return_profit_colonel_amt,
    SUM(updateTable.return_profit_colonel1) AS return_profit_colonel1_amt,
    SUM(updateTable.return_profit_colonel2) AS return_profit_colonel2_amt,
    SUM(updateTable.pre_return_amt) AS pre_return_amt,
    SUM(updateTable.return_activity_discount_amt) AS return_activity_discount_amt,
    SUM(updateTable.return_coupon_discount_amt) AS return_coupon_discount_amt,
    SUM(updateTable.return_coupon_discount_amt2) AS return_coupon_discount_amt2,
    SUM(updateTable.return_activity_discount_amt + updateTable.return_coupon_discount_amt + updateTable.return_coupon_discount_amt2) AS return_discount_amt,
    CASE
        WHEN SUM(updateTable.return_amt) <= 0 THEN 0
        ELSE ROUND(IFNULL((SUM(updateTable.return_amt) - SUM(updateTable.return_supplier_amt)) / SUM(updateTable.return_amt) * 100, 0), 6)
    END AS return_profit_rate,
    DATE_FORMAT(now(), '%Y%m%d')
FROM
    (
        SELECT
            -- 订单
            SUBSTRING( dtodi.date_id , 1, 6 ) AS month_id, -- 年月ID
            dtodi.sys_code, -- 平台商编号
            dtodi.area_id, -- 区域ID
            dtodi.colonel_id, -- 业务员ID
            IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            SUM(CASE WHEN dtodi.colonel_flag = 1 THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS colonel_order_amt, -- 业务员代客下单金额
            SUM(CASE WHEN dtodi.colonel_flag = 0 THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS branch_order_amt, -- 门店自主下单金额
            COUNT(DISTINCT dtodi.order_id) AS order_num, -- 订单数量
            COUNT(DISTINCT CASE WHEN dtodi.colonel_flag = 1 THEN dtodi.order_id END) AS colonel_order_num, -- 业务员代客下单数量
            COUNT(DISTINCT CASE WHEN dtodi.colonel_flag = 0 THEN dtodi.order_id END) AS branch_order_num, -- 门店自主下单数量
            COUNT(DISTINCT dtodi.sku_id) AS sku_num, -- SKU数量
            COUNT(DISTINCT dtodi.spu_id) AS spu_num, -- SPU数量
            COUNT(DISTINCT dtodi.branch_id) AS branch_num, -- 门店数量
            IFNULL(SUM(dtodi.supplier_amt) - SUM(IFNULL(dtci.supplier_amt, 0)), 0) AS supplier_amt, -- 入驻商成本金额
            IFNULL(SUM(dtodi.profit) - SUM(IFNULL(dtci.profit, 0)), 0) AS profit_amt, -- 利润金额（减去发货前售后）
            IFNULL(SUM(dtodi.partner_amt) - SUM(IFNULL(dtci.partner_amt, 0)), 0) AS partner_amt, -- 平台商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.dc_amt) - SUM(IFNULL(dtci.dc_amt, 0)), 0) AS dc_amt, -- 运营商分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel1_amt) - SUM(IFNULL(dtci.colonel1_amt, 0)), 0) AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.colonel2_amt) - SUM(IFNULL(dtci.colonel2_amt, 0)), 0) AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            IFNULL(SUM(dtodi.order_amt) - SUM(IFNULL(dtci.cancel_amt, 0)), 0) AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            IFNULL(SUM(dtodi.activity_discount_amt) - SUM(IFNULL(dtci.cancel_activity_discount_amt, 0)), 0) AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt) - SUM(IFNULL(dtci.cancel_coupon_discount_amt, 0)), 0) AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            IFNULL(SUM(dtodi.coupon_discount_amt2) - SUM(IFNULL(dtci.cancel_coupon_discount_amt2, 0)), 0) AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            -- 售后
            0 AS return_amt, -- 售后金额（收货后）
            0 AS after_num, -- 售后单数量（收货后）
            0 AS return_sku_num, -- 售后SKU数量（收货后）
            0 AS return_spu_num, -- 售后SPU数量（收货后）
            0 AS return_branch_num, -- 售后门店数量（收货后）
            0 AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            0 AS return_profit, -- 售后利润（收货后）
            0 AS return_profit_partner, -- 平台商售后分润金额（收货后）
            0 AS return_profit_dc, -- 运营商售后分润金额（收货后）
            0 AS return_profit_colonel1, -- 业务员管理员售后分润金额（收货后）
            0 AS return_profit_colonel2, -- 业务员售后分润金额（收货后）
            0 AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            0 AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            0 AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            0 AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    SUM( return_amt ) return_amt,
                    SUM( refund_amt ) refund_amt,
                    SUM( cancel_amt ) cancel_amt,
                    SUM( cancel_qty ) cancel_qty,
                    SUM( supplier_amt ) supplier_amt,
                    SUM( profit ) profit,
                    SUM( partner_amt ) partner_amt,
                    SUM( dc_amt ) dc_amt,
                    SUM( colonel1_amt ) colonel1_amt,
                    SUM( colonel2_amt ) colonel2_amt,
                    SUM( cancel_activity_discount_amt ) cancel_activity_discount_amt,
                    SUM( cancel_coupon_discount_amt ) cancel_coupon_discount_amt,
                    SUM( cancel_coupon_discount_amt2 ) cancel_coupon_discount_amt2
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    SUBSTRING( order_date_id , 1, 6 ) in (
                        SELECT SUBSTRING( order_date_id , 1, 6 ) as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    )
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            SUBSTRING(dtodi.date_id, 1, 6 ) in (
                SELECT
                    orderdate.order_date_id as order_date_id
                FROM
                    (
                        SELECT SUBSTRING(date_id, 1, 6) as order_date_id FROM dwd_trd_order_dtl_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY date_id
                        UNION
                        SELECT SUBSTRING(order_date_id, 1, 6) as order_date_id FROM dwd_trd_cancel_inc WHERE insert_date_id=DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
                    ) orderdate
            )
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
            AND dtodi.colonel_id is not null
        GROUP BY
            SUBSTRING( dtodi.date_id , 1, 6 ), -- 年月ID
            dtodi.sys_code,
            dtodi.area_id,
            dtodi.colonel_id

        UNION ALL

        SELECT
            SUBSTRING(dtri.date_id, 1, 6) AS month_id, -- 年月ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.colonel_id, -- 业务员ID
            0 AS order_amt, -- 订单金额 = 实际支付金额 - 发货前取消金额
            0 AS colonel_order_amt, -- 业务员代客下单金额
            0 AS branch_order_amt, -- 门店自主下单金额
            0 AS order_num, -- 订单数量
            0 AS colonel_order_num, -- 业务员代客下单数量
            0 AS branch_order_num, -- 门店自主下单数量
            0 AS sku_num, -- SKU数量
            0 AS spu_num, -- SPU数量
            0 AS branch_num, -- 门店数量
            0 AS supplier_amt, -- 入驻商成本金额
            0 AS profit_amt, -- 利润金额（减去发货前售后）
            0 AS partner_amt, -- 平台上分润金额（减去发货前售后）
            0 AS dc_amt, -- 运营上分润金额（减去发货前售后）
            0 AS colonel1_amt, -- 业务员管理员分润金额（减去发货前售后）
            0 AS colonel2_amt, -- 业务员分润金额（减去发货前售后）
            0 AS pre_order_amt, -- 订单金额（不包含优惠）（减去发货前售后）
            0 AS activity_discount_amt, -- 活动优惠金额（减去发货前售后）
            0 AS coupon_discount_amt, -- 优惠劵金额；分摊（减去发货前售后）
            0 AS coupon_discount_amt2, -- 优惠劵金额；不分摊（减去发货前售后）
            IFNULL(SUM(dtri.return_amt), 0) AS return_amt, -- 售后金额（收货后）
            COUNT(DISTINCT dtri.after_no) AS after_num, -- 退单数量（收货后）
            COUNT(DISTINCT dtri.sku_id) AS return_sku_num, -- 售后SKU数量（收货后）
            COUNT(DISTINCT dtri.spu_id) AS return_spu_num, -- 售后SPU数量（收货后）
            COUNT(DISTINCT dtri.branch_id) AS return_branch_num, -- 售后门店数量（收货后）
            IFNULL(SUM(dtri.supplier_amt), 0)AS return_supplier_amt, -- 售后入驻商成本金额（收货后）
            IFNULL(SUM(dtri.profit), 0) AS return_profit, -- 售后利润（收货后）
            IFNULL(SUM(dtri.partner_amt), 0) AS return_profit_partner_amt, -- 平台商售后分润金额（收货后）
            IFNULL(SUM(dtri.dc_amt), 0) AS return_profit_dc_amt, -- 运营商售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel1_amt), 0) AS return_profit_colonel1_amt, -- 业务员管理员售后分润金额（收货后）
            IFNULL(SUM(dtri.colonel2_amt), 0) AS return_profit_colonel2_amt, -- 业务员售后分润金额（收货后）
            SUM(dtri.return_order_amt) AS pre_return_amt, -- 售后金额；不包含优惠（收货后）
            IFNULL(SUM(dtri.return_activity_discount_amt), 0) AS return_activity_discount_amt, -- 售后活动优惠金额（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt), 0) AS return_coupon_discount_amt, -- 售后优惠劵金额；分摊（收货后）
            IFNULL(SUM(dtri.return_coupon_discount_amt2), 0) AS return_coupon_discount_amt2 -- 售后优惠劵金额；不分摊（收货后）
        FROM
            dwd_trd_return_inc dtri
        WHERE
            SUBSTRING(date_id, 1, 6) in (
                SELECT SUBSTRING(date_id, 1, 6) FROM dwd_trd_return_inc WHERE insert_date_id = DATE_FORMAT(now(), '%Y%m%d') GROUP BY order_date_id
            )
            AND dtri.colonel_id is not null
        GROUP BY
            SUBSTRING(dtri.date_id, 1, 6), -- 日期ID
            dtri.sys_code, -- 平台商编号
            dtri.area_id, -- 区域ID
            dtri.colonel_id -- 业务员ID
    ) updateTable
GROUP BY
    updateTable.month_id,
    updateTable.sys_code,
    updateTable.area_id,
    updateTable.colonel_id


















