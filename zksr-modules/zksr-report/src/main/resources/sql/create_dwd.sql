DROP TABLE IF EXISTS dwd_trd_order_dtl_inc;
CREATE TABLE dwd_trd_order_dtl_inc(
`supplier_order_dtl_id`BIGINT(20) NOT NULL  COMMENT '入驻商订单明细id' ,
`order_id`BIGINT(20)   COMMENT '订单id' ,
`order_no` VARCHAR(20)   COMMENT '订单编号;订单编号' ,
`dc_id`BIGINT(20)   COMMENT '运营商id' ,
`area_id`BIGINT(20)   COMMENT '城市id' ,
`member_id`BIGINT(20)   COMMENT '用户id' ,
`colonel_id`BIGINT(20)   COMMENT '业务员id' ,
`colonel_level` INT(2)   COMMENT '业务员级别' ,
`pcolonel_id`BIGINT(20)   COMMENT '父业务员id' ,
`pcolonel_level` INT(2)   COMMENT '父业务员级别' ,
`branch_id`BIGINT(20)   COMMENT '门店id' ,
`order_amt` DECIMAL(12,2)   COMMENT '订单金额;未减去优惠的订单金额' ,
`colonel_flag` INT(1)   COMMENT '下单用户是否本身是业务员(0-否（默认）  1-是)' ,
`colonel_flag_val` VARCHAR(100)   COMMENT '是|否' ,
`source` VARCHAR(16)   COMMENT '订单来源（数据字典）;mini-小程序(默认) ywyApp-业务员APP' ,
`source_val` VARCHAR(100)   COMMENT '订单来源' ,
`sys_code`BIGINT(20)   COMMENT '平台商id' ,
`item_type` INT(1)   COMMENT '0-全国商品 1-本地配送' ,
`item_type_val` VARCHAR(100)   COMMENT '全国商品|本地配送' ,
`supplier_order_no` VARCHAR(25)   COMMENT '入驻商订单编号' ,
`supplier_order_dtl_no` VARCHAR(25)   COMMENT '入驻商订单明细编号' ,
`supplier_id`BIGINT(20)   COMMENT '入驻商id' ,
`sku_id`BIGINT(20)   COMMENT '商品sku id' ,
`spu_id`BIGINT(20)   COMMENT '商品SPU id' ,
`total_num` INT(8)   COMMENT '商品数量;最小单位，中单位需换算成最小单位，大单位同理' ,
`exact_total_amt` DECIMAL(18,6)   COMMENT '合计金额（exact_price*total_num）' ,
`total_amt` DECIMAL(12,2)   COMMENT 'exact_total_amt的四舍五入值（实际支付金额）' ,
`exact_price` DECIMAL(18,6)   COMMENT '精准成交价' ,
`price` DECIMAL(12,2)   COMMENT '成交价;最小单位的成交价' ,
`sale_price` DECIMAL(12,2)   COMMENT '原销售价;最小单位的原销售价' ,
`activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)(new)' ,
`coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)(new)' ,
`coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)(new)' ,
`gift_flag` INT(1)   COMMENT '是否是赠品 1-是  0-否' ,
`gift_flag_val` VARCHAR(100)   COMMENT '是|否' ,
`order_unit` VARCHAR(16)   COMMENT '订单购买单位;最小单位的单位，中单位的单位，大单位的单位' ,
`order_unit_val` VARCHAR(100)   COMMENT '订单购买单位' ,
`order_unit_type` INT(2)   COMMENT '购买:单位大小' ,
`order_unit_type_val` VARCHAR(255)   COMMENT '购买:单位大小' ,
`order_unit_qty` INT(8)   COMMENT '购买:订单购买单位数量;购买的是中单位，即为中单位数量。小单位、大单位同理' ,
`supplier_amt` DECIMAL(12,2)   COMMENT '入驻商结算金额' ,
`profit` DECIMAL(12,2)   COMMENT '利润' ,
`cost_price` DECIMAL(12,2)   COMMENT '入驻商成本价' ,
`all_dc_rate` DECIMAL(8,6)   COMMENT '总运营商分润比例;all_dc_rate = 1 - partner_rate' ,
`setting_dc_rate` DECIMAL(8,6)   COMMENT '运营商设置的运营商分润比例;如果运营商没有设置，则setting_dc_rate = 1- partner_rate' ,
`dc_rate` DECIMAL(8,6)   COMMENT '运营商分润比例;实际分润比例' ,
`dc_amt` DECIMAL(12,2)   COMMENT '运营商结算金额' ,
`partner_rate` DECIMAL(8,6)   COMMENT '平台商分润比例;从prdt_catgory' ,
`partner_amt` DECIMAL(12,2)   COMMENT '平台商结算金额' ,
`setting_colonel1_rate` DECIMAL(8,6)   COMMENT '运营商设置的业务员负责人分润比例;从prdt_catgory_rate' ,
`colonel1_rate` DECIMAL(8,6)   COMMENT '业务员负责人分润比例;实际分润比例 = all_dc_rate * setting_colonel1_rate' ,
`colonel1_percentage` DECIMAL(8,6)   COMMENT '业务员负责人提成系数' ,
`all_colonel1_amt` DECIMAL(12,2)   COMMENT '满额业务员分润金额;all_colonel2_amt = profit * colonel1_rate' ,
`colonel1_amt` DECIMAL(12,2)   COMMENT '业务员结算金额;colonel1_amt = all_colonel1_amt * percentage_rate1' ,
`setting_colonel2_rate` DECIMAL(8,6)   COMMENT '运营商设置的业务员分润比例;从prdt_catgory_rate' ,
`colonel2_rate` DECIMAL(8,6)   COMMENT '业务员分润比例;实际分润比例' ,
`colonel2_percentage` DECIMAL(8,6)   COMMENT '业务员提成系数' ,
`all_colonel2_amt` DECIMAL(12,2)   COMMENT '满额业务员分润金额;all_colonel2_amt = profit * colonel2_rate' ,
`colonel2_amt` DECIMAL(12,2)   COMMENT '业务员结算金额;colonel2_amt = all_colonel2_amt * percentage_rate2' ,
`order_time` DATETIME(3)   COMMENT '下单时间' ,
`date_id`BIGINT(20)   COMMENT '下单日期ID' ,
`insert_date_id`BIGINT(20)   COMMENT '插入时间id' ,
PRIMARY KEY (supplier_order_dtl_id)
)  COMMENT = '交易域下单事务事实表(dwd_trd_order_dtl_inc)';


DROP TABLE IF EXISTS dwd_trd_cancel_inc;
CREATE TABLE dwd_trd_cancel_inc(
`supplier_after_dtl_id`BIGINT(20) NOT NULL  COMMENT '售后单明细id' ,
`sys_code`BIGINT(20)   COMMENT '平台商id' ,
`dc_id`BIGINT(20)   COMMENT '运营商id' ,
`area_id`BIGINT(20)   COMMENT '城市id' ,
`member_id`BIGINT(20)   COMMENT '用户id' ,
`supplier_id`BIGINT(20)   COMMENT '入驻商id' ,
`colonel_id`BIGINT(20)   COMMENT '业务员id' ,
`colonel_level` INT(2)   COMMENT '业务员级别' ,
`pcolonel_id`BIGINT(20)   COMMENT '父业务员id' ,
`pcolonel_level` INT(2)   COMMENT '父业务员级别' ,
`branch_id`BIGINT(20)   COMMENT '门店id' ,
`order_unit` VARCHAR(16)   COMMENT '订单购买单位;从订单，最小单位的单位，中单位的单位，大单位的单位' ,
`order_unit_type` INT(2)   COMMENT '单位大小 1：小，2：中，3：大' ,
`order_unit_type_val` VARCHAR(30)   COMMENT '购买:单位大小 对应值' ,
`order_unit_qty` INT(8)   COMMENT '订单购买单位数量;从订单，购买的是中单位，即为中单位数量。小单位、大单位同理' ,
`order_unit_size` INT(8)   COMMENT '订单换算数量;从订单，小单位为1，中、大单位的换算数量' ,
`cancel_qty` INT(8)   COMMENT '发货前取消的数量;最小单位' ,
`cancel_amt` DECIMAL(12,2)   COMMENT '退货金额(不包含优惠)' ,
`cancel_unit` VARCHAR(16)   COMMENT '发货前取消单位;最小单位的单位，中单位的单位，大单位的单位' ,
`cancel_unit_type` INT(2)   COMMENT '发货前取消单位大小 1：小，2：中，3：大' ,
`cancel_unit_type_val` VARCHAR(30)   COMMENT '发货前取消单位大小 对应值' ,
`cancel_unit_qty` INT(8)   COMMENT '发货前取消单位数量;发货前取消的是中单位，即为中单位数量。小单位、大单位同理' ,
`cancel_unit_size` INT(8)   COMMENT '发货前取消单位换算数量' ,
`sku_id`BIGINT(20)   COMMENT '商品sku id' ,
`spu_id`BIGINT(20)   COMMENT '商品SPU id' ,
`after_no` VARCHAR(25)   COMMENT '售后单编号' ,
`supplier_after_no` VARCHAR(25)   COMMENT '入驻商售后单编号' ,
`supplier_order_dtl_id`BIGINT(20)   COMMENT '入驻商订单明细id' ,
`exact_return_price` DECIMAL(18,6)   COMMENT '精准成交价;从订单明细 （最小单位）' ,
`return_price` DECIMAL(12,2)   COMMENT '退货价;最小单位' ,
`exact_return_amt` DECIMAL(18,6)   COMMENT '精准退货金额;exact_return_price * return_qty' ,
`return_amt` DECIMAL(12,2)   COMMENT '退货金额(包含优惠)' ,
`refund_amt` DECIMAL(12,2)   COMMENT '退款金额(包含优惠)' ,
`cancel_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)(new)' ,
`cancel_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)(new)' ,
`cancel_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)(new)' ,
`cancel_sales_unit_price` decimal(18,6) DEFAULT NULL COMMENT '售后购买单位实际销售单价（包含优惠）',
`cancel_original_price` decimal(18,6) DEFAULT NULL COMMENT '售后最小单位原销售单价（不包括优惠）',
`cancel_original_unit_price` decimal(18,6) DEFAULT NULL COMMENT '售后单位原销售单价（不包括优惠）',
`gift_flag` INT(1)   COMMENT '是否是赠品 1-是  0-否' ,
`gift_flag_val` VARCHAR(100)   COMMENT '是|否' ,
`supplier_amt` DECIMAL(12,2)   COMMENT '入驻商结算金额' ,
`profit` DECIMAL(12,2)   COMMENT '利润' ,
`cost_price` DECIMAL(12,2)   COMMENT '入驻商成本价' ,
`all_dc_rate` DECIMAL(8,6)   COMMENT '总运营商分润比例;all_dc_rate = 1 - partner_rate' ,
`setting_dc_rate` DECIMAL(8,6)   COMMENT '运营商设置的运营商分润比例;如果运营商没有设置，则setting_dc_rate = 1- partner_rate' ,
`dc_rate` DECIMAL(8,6)   COMMENT '运营商分润比例;实际分润比例' ,
`dc_amt` DECIMAL(12,2)   COMMENT '运营商结算金额' ,
`partner_rate` DECIMAL(8,6)   COMMENT '平台商分润比例;从prdt_catgory' ,
`partner_amt` DECIMAL(12,2)   COMMENT '平台商结算金额' ,
`setting_colonel1_rate` DECIMAL(8,6)   COMMENT '运营商设置的业务员负责人分润比例;从prdt_catgory_rate' ,
`colonel1_rate` DECIMAL(8,6)   COMMENT '业务员负责人分润比例;实际分润比例 = all_dc_rate * setting_colonel1_rate' ,
`colonel1_percentage` DECIMAL(8,6)   COMMENT '业务员负责人提成系数' ,
`all_colonel1_amt` DECIMAL(12,2)   COMMENT '满额业务员分润金额;all_colonel2_amt = profit * colonel1_rate' ,
`colonel1_amt` DECIMAL(12,2)   COMMENT '业务员结算金额;colonel1_amt = all_colonel1_amt * percentage_rate1' ,
`setting_colonel2_rate` DECIMAL(8,6)   COMMENT '运营商设置的业务员分润比例;从prdt_catgory_rate' ,
`colonel2_rate` DECIMAL(8,6)   COMMENT '业务员分润比例;实际分润比例' ,
`colonel2_percentage` DECIMAL(8,6)   COMMENT '业务员提成系数' ,
`all_colonel2_amt` DECIMAL(12,2)   COMMENT '满额业务员分润金额;all_colonel2_amt = profit * colonel2_rate' ,
`colonel2_amt` DECIMAL(12,2)   COMMENT '业务员结算金额;colonel2_amt = all_colonel2_amt * percentage_rate2' ,
`cancel_time` DATETIME(3)   COMMENT '取消时间' ,
`order_time` DATETIME(3)   COMMENT '订单时间' ,
`order_date_id`BIGINT(20)   COMMENT '订单日期ID' ,
`date_id`BIGINT(20)   COMMENT '日期ID' ,
`insert_date_id`BIGINT(20)   COMMENT '插入时间id' ,
PRIMARY KEY (supplier_after_dtl_id)
)  COMMENT = '交易域发货前取消订单事务事实表(dwd_trd_cancel_inc)';

DROP TABLE IF EXISTS dwd_trd_return_inc;
CREATE TABLE dwd_trd_return_inc(
    `supplier_after_dtl_id`BIGINT(20) NOT NULL  COMMENT '售后单明细id' ,
    `sys_code`BIGINT(20)   COMMENT '平台商id' ,
    `dc_id`BIGINT(20)   COMMENT '运营商id' ,
    `area_id`BIGINT(20)   COMMENT '城市id' ,
    `member_id`BIGINT(20)   COMMENT '用户id' ,
    `supplier_id`BIGINT(20)   COMMENT '入驻商id' ,
    `colonel_id`BIGINT(20)   COMMENT '业务员id' ,
    `colonel_level` INT(2)   COMMENT '业务员级别' ,
    `pcolonel_id`BIGINT(20)   COMMENT '父业务员id' ,
    `pcolonel_level` INT(2)   COMMENT '父业务员级别' ,
    `branch_id`BIGINT(20)   COMMENT '门店id' ,
    `order_unit` VARCHAR(16)   COMMENT '订单购买单位;从订单，最小单位的单位，中单位的单位，大单位的单位' ,
    `order_unit_type` INT(2)   COMMENT '单位大小 1：小，2：中，3：大' ,
    `order_unit_type_val` VARCHAR(30)   COMMENT '购买:单位大小 对应值' ,
    `order_unit_qty` INT(8)   COMMENT '订单购买单位数量;从订单，购买的是中单位，即为中单位数量。小单位、大单位同理' ,
    `order_unit_size` INT(8)   COMMENT '订单换算数量;从订单，小单位为1，中、大单位的换算数量' ,
    `return_qty` INT(8)   COMMENT '售后数量;最小单位' ,
    `return_order_amt` DECIMAL(12,2)   COMMENT '售后金额(不包含优惠)' ,
    `return_unit` VARCHAR(16)   COMMENT '售后单位;最小单位的单位，中单位的单位，大单位的单位' ,
    `return_unit_type` INT(2)   COMMENT '售后单位大小 1：小，2：中，3：大' ,
    `return_unit_type_val` VARCHAR(30)   COMMENT '售后单位大小 对应值' ,
    `return_unit_qty` INT(8)   COMMENT '售后单位数量;售后的是中单位，即为中单位数量。小单位、大单位同理' ,
    `return_unit_size` INT(8)   COMMENT '售后单位换算数量' ,
    `sku_id`BIGINT(20)   COMMENT '商品sku id' ,
    `spu_id`BIGINT(20)   COMMENT '商品SPU id' ,
    `after_no` VARCHAR(25)   COMMENT '售后单编号' ,
    `supplier_after_no` VARCHAR(25)   COMMENT '入驻商售后单编号' ,
    `supplier_order_dtl_id`BIGINT(20)   COMMENT '入驻商订单明细id' ,
    `exact_return_price` DECIMAL(18,6)   COMMENT '精准成交价;从订单明细 （最小单位）' ,
    `return_price` DECIMAL(12,2)   COMMENT '退货价;最小单位' ,
    `exact_return_amt` DECIMAL(18,6)   COMMENT '精准退货金额;exact_return_price * return_qty' ,
    `return_amt` DECIMAL(12,2)   COMMENT '退货金额(包含优惠)' ,
    `refund_amt` DECIMAL(12,2)   COMMENT '退款金额(包含优惠)' ,
    `return_activity_discount_amt` DECIMAL(12,2)   COMMENT '活动优惠金额(分摊的)(new)' ,
    `return_coupon_discount_amt` DECIMAL(12,2)   COMMENT '优惠金额(分摊的)(new)' ,
    `return_coupon_discount_amt2` DECIMAL(12,2)   COMMENT '优惠金额(不分摊的)(new)' ,
    `return_sales_unit_price` decimal(18,6) DEFAULT NULL COMMENT '售后购买单位实际销售单价（包含优惠）',
    `return_original_price` decimal(18,6) DEFAULT NULL COMMENT '售后最小单位原销售单价（不包括优惠）',
    `return_original_unit_price` decimal(18,6) DEFAULT NULL COMMENT '售后单位原销售单价（不包括优惠）',
    `gift_flag` INT(1)   COMMENT '是否是赠品 1-是  0-否' ,
    `gift_flag_val` VARCHAR(100)   COMMENT '是|否' ,
    `supplier_amt` DECIMAL(12,2)   COMMENT '入驻商结算金额' ,
    `profit` DECIMAL(12,2)   COMMENT '利润' ,
    `cost_price` DECIMAL(12,2)   COMMENT '入驻商成本价' ,
    `all_dc_rate` DECIMAL(8,6)   COMMENT '总运营商分润比例;all_dc_rate = 1 - partner_rate' ,
    `setting_dc_rate` DECIMAL(8,6)   COMMENT '运营商设置的运营商分润比例;如果运营商没有设置，则setting_dc_rate = 1- partner_rate' ,
    `dc_rate` DECIMAL(8,6)   COMMENT '运营商分润比例;实际分润比例' ,
    `dc_amt` DECIMAL(12,2)   COMMENT '运营商结算金额' ,
    `partner_rate` DECIMAL(8,6)   COMMENT '平台商分润比例;从prdt_catgory' ,
    `partner_amt` DECIMAL(12,2)   COMMENT '平台商结算金额' ,
    `setting_colonel1_rate` DECIMAL(8,6)   COMMENT '运营商设置的业务员负责人分润比例;从prdt_catgory_rate' ,
    `colonel1_rate` DECIMAL(8,6)   COMMENT '业务员负责人分润比例;实际分润比例 = all_dc_rate * setting_colonel1_rate' ,
    `colonel1_percentage` DECIMAL(8,6)   COMMENT '业务员负责人提成系数' ,
    `all_colonel1_amt` DECIMAL(12,2)   COMMENT '满额业务员分润金额;all_colonel2_amt = profit * colonel1_rate' ,
    `colonel1_amt` DECIMAL(12,2)   COMMENT '业务员结算金额;colonel1_amt = all_colonel1_amt * percentage_rate1' ,
    `setting_colonel2_rate` DECIMAL(8,6)   COMMENT '运营商设置的业务员分润比例;从prdt_catgory_rate' ,
    `colonel2_rate` DECIMAL(8,6)   COMMENT '业务员分润比例;实际分润比例' ,
    `colonel2_percentage` DECIMAL(8,6)   COMMENT '业务员提成系数' ,
    `all_colonel2_amt` DECIMAL(12,2)   COMMENT '满额业务员分润金额;all_colonel2_amt = profit * colonel2_rate' ,
    `colonel2_amt` DECIMAL(12,2)   COMMENT '业务员结算金额;colonel2_amt = all_colonel2_amt * percentage_rate2' ,
    `return_time` DATETIME(3)   COMMENT '售后时间' ,
    `order_time` DATETIME(3)   COMMENT '订单时间' ,
    `order_date_id`BIGINT(20)   COMMENT '订单日期ID' ,
    `date_id`BIGINT(20)   COMMENT '日期ID' ,
    `insert_date_id`BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (supplier_after_dtl_id)
)  COMMENT = '交易域售后订单事务事实表(dwd_trd_return_inc)';


DROP TABLE IF EXISTS dwd_colonel_visit_inc;
CREATE TABLE dwd_colonel_visit_inc(
    `colonel_visit_log_id` BIGINT(20) NOT NULL  COMMENT '业务员拜访日志id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` VARCHAR(64)   COMMENT '更新人' ,
    `update_time` DATETIME(3)   COMMENT '更新时间' ,
    `sys_code` BIGINT(20)   COMMENT '平台商id' ,
    `area_id` BIGINT(20)   COMMENT '区域城市id' ,
    `colonel_id` BIGINT(20)   COMMENT '业务员ID' ,
    `branch_id` BIGINT(20)   COMMENT '门店ID' ,
    `sign_in_longitude` VARCHAR(30)   COMMENT '签到经度' ,
    `sign_in_latitude` VARCHAR(30)   COMMENT '签到纬度' ,
    `sign_in_address` VARCHAR(255)   COMMENT '签到地址' ,
    `sign_in_distance` VARCHAR(50)   COMMENT '签到距离' ,
    `sign_in_img_urls` TEXT(255)   COMMENT '签到图片链接：多个以英文，隔开' ,
    `sign_in_date` DATETIME(3)   COMMENT '签到时间' ,
    `sign_out_longitude` VARCHAR(30)   COMMENT '签退经度' ,
    `sign_out_latitude` VARCHAR(30)   COMMENT '签退纬度' ,
    `sign_out_address` VARCHAR(255)   COMMENT '签退地址' ,
    `sign_out_distance` VARCHAR(50)   COMMENT '签退距离' ,
    `sign_out_date` DATETIME(3)   COMMENT '签退时间' ,
    `visit_interval_time` VARCHAR(20)   COMMENT '拜访间隔时间' ,
    `visit_flag` int(11) DEFAULT NULL COMMENT '拜访状态 0-签到 1-签退 2-作废',
    `visit_flag_val` VARCHAR(20) DEFAULT NULL COMMENT '拜访状态 对应值',
    `memo` varchar(255) DEFAULT NULL COMMENT '备注',
    `date_id`BIGINT(20)   COMMENT '日期ID' ,
    `insert_date_id`BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (colonel_visit_log_id)
)  COMMENT = '业务员域拜访事务事实表';

DROP TABLE IF EXISTS dwd_login_his_inc;
CREATE TABLE dwd_login_his_inc(
    `login_his_id` BIGINT(20) NOT NULL  COMMENT '登录历史Id' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `sys_code` BIGINT(20)   COMMENT '平台商id' ,
    `date_id` INT(11) DEFAULT NULL COMMENT '日期;yyyyMMdd',
    `wx_openid` VARCHAR(100) DEFAULT NULL COMMENT '微信openid;后台登录信息获取',
    `member_phone` VARCHAR(16) DEFAULT NULL COMMENT '用户手机号;后台登录信息获取',
    `member_username` VARCHAR(255) DEFAULT NULL COMMENT '用户名;后台登录信息获取',
    `member_id` BIGINT(20) DEFAULT NULL COMMENT '用户id;后台登录信息获取',
    `branch_id` BIGINT(20) DEFAULT NULL COMMENT '门店id;后台登录信息获取',
    `area_id` BIGINT(20)   COMMENT '区域城市id' ,
    `ip` VARCHAR(32) DEFAULT NULL COMMENT 'ip地址;http_request',
    `district` VARCHAR(32) DEFAULT NULL COMMENT 'ip地址归属地;http_request',
    `tp` VARCHAR(8) DEFAULT NULL COMMENT '类型（数据字典）;0-登陆  1-访问',
    `device_id` VARCHAR(128) DEFAULT NULL COMMENT '设备id;前端传（HttpHeader）',
    `device_type` VARCHAR(32) DEFAULT NULL COMMENT '设备类型;前端传（HttpHeader）',
    `device_brand` VARCHAR(32) DEFAULT NULL COMMENT '设备品牌;前端传（HttpHeader）',
    `device_model` VARCHAR(32) DEFAULT NULL COMMENT '设备型号;前端传（HttpHeader）',
    `os_name` VARCHAR(32) DEFAULT NULL COMMENT '系统名称;前端传（HttpHeader）',
    `os_version` VARCHAR(32) DEFAULT NULL COMMENT '操作系统版本;前端传（HttpHeader）',
    `port` VARCHAR(32) DEFAULT NULL COMMENT 'pc app xcx',
    `insert_date_id`BIGINT(20)   COMMENT '插入时间id' ,
    PRIMARY KEY (login_his_id)
)  COMMENT = '门店登录域事务事实表';
