DROP TABLE IF EXISTS dim_date;
CREATE TABLE dim_date(
 `date_id` INT(8) NOT NULL  COMMENT '日期ID' ,
 `date` DATE   COMMENT '' ,
 `year` INT(4)   COMMENT '年份' ,
 `month` INT(2)   COMMENT '一年中的第几月' ,
 `day` INT(3)   COMMENT '每月的第几天' ,
 `quarter` INT(1)   COMMENT '一年中的第几季度' ,
 `week_id` VARCHAR(8)   COMMENT '周ID,一年中的第几周' ,
 `day_of_week` INT(1)   COMMENT '一周的第几天' ,
 `weekday_name` VARCHAR(16)   COMMENT '星期几名' ,
 `month_name` VARCHAR(16)   COMMENT '月份名' ,
PRIMARY KEY (date_id)
)  COMMENT = '日期维度表';

DROP TABLE IF EXISTS dim_month;
CREATE TABLE dim_month(
`month_id` INT(8) NOT NULL  COMMENT '月份ID' ,
`month_val` varchar(7)   COMMENT '月份' ,
`year` INT(4)   COMMENT '年' ,
`month` INT(2)   COMMENT '月' ,
`quarter` INT(1)   COMMENT '一年中的第几季度' ,
`date_start` date   COMMENT '月份开始日期' ,
`date_end` date   COMMENT '月份结束日期' ,
PRIMARY KEY (month_id)
)  COMMENT = '月份维度表';





DROP TABLE IF EXISTS dim_dict_data;
CREATE TABLE dim_dict_data(
`dict_code` BIGINT(20) NOT NULL  COMMENT '字典编码' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`update_by` VARCHAR(64)   COMMENT '更新者' ,
`update_time` DATETIME   COMMENT '更新时间' ,
`dict_label` VARCHAR(100)   COMMENT '字典标签' ,
`dict_value` VARCHAR(100)   COMMENT '字典键值' ,
`dict_type` VARCHAR(100)   COMMENT '字典类型' ,
`dict_type_name` VARCHAR(100)   COMMENT '字典类型名' ,
`dict_sort` INT(4)   COMMENT '字典排序' ,
`status` VARCHAR(1)   COMMENT '状态（0正常 1停用）' ,
PRIMARY KEY (dict_code)
)  COMMENT = '数据字典维度表';


DROP TABLE IF EXISTS dim_sku;
CREATE TABLE dim_sku(
`sku_id` BIGINT(20)   COMMENT '商品sku id' ,
`sys_code` BIGINT(20)   COMMENT '平台商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`update_by` VARCHAR(64)   COMMENT '更新人' ,
`update_time` DATETIME(3)   COMMENT '更新时间' ,
`spu_id` BIGINT(20)   COMMENT '商品SPU id' ,
`spu_name` VARCHAR(255)   COMMENT '商品SPU名称' ,
`spu_no` VARCHAR(32)   COMMENT '商品SPU编号' ,
`supplier_id` BIGINT(20)   COMMENT '入驻商id' ,
`catgory1_id` BIGINT(20)   COMMENT '一级管理分类id' ,
`catgory1_name` VARCHAR(255)   COMMENT '一级管理分类名' ,
`catgory2_id` BIGINT(20)   COMMENT '二级管理分类id' ,
`catgory2_name` VARCHAR(255)   COMMENT '二级管理分类名' ,
`catgory3_id` BIGINT(20)   COMMENT '三级管理分类id' ,
`catgory3_name` VARCHAR(255)   COMMENT '三级管理分类名' ,
`brand_id` BIGINT(20)   COMMENT '平台商品牌id' ,
`brand_name` VARCHAR(255)   COMMENT '品牌名称' ,
`unit` INT(3)   COMMENT '单位-数据字典（sys_prdt_unit）' ,
`unit_val` VARCHAR(100)   COMMENT '单位' ,
`barcode` VARCHAR(64)   COMMENT '国际条码' ,
`properties` VARCHAR(512)   COMMENT '属性数组，JSON 格式' ,
`thumb` VARCHAR(255)   COMMENT '封面图' ,
`stock` INT(10)   COMMENT '库存数量' ,
`mark_price` DECIMAL(12,2)   COMMENT '标准价' ,
`suggest_price` DECIMAL(12,2)   COMMENT '建议零售价' ,
`cost_price` DECIMAL(12,2)   COMMENT '成本价(供货价)' ,
`status` INT(1)   COMMENT '状态 1-启用 0-停用' ,
`is_delete` INT(1)   COMMENT '是否删除 1-是 0-否' ,
`mid_barcode` VARCHAR(64)   COMMENT '中单位-国际条码' ,
`mid_mark_price` DECIMAL(12,2)   COMMENT '中单位-标准价' ,
`mid_cost_price` DECIMAL(12,2)   COMMENT '中单位-成本价(供货价)' ,
`mid_suggest_price` DECIMAL(12,2)   COMMENT '中单位-建议零售价' ,
`large_barcode` VARCHAR(64)   COMMENT '大单位-国际条码' ,
`large_mark_price` DECIMAL(12,2)   COMMENT '大单位-标准价' ,
`large_cost_price` DECIMAL(12,2)   COMMENT '大单位-成本价(供货价)' ,
`large_suggest_price` DECIMAL(12,2)   COMMENT '大单位-建议零售价' ,
PRIMARY KEY (sku_id)
)  COMMENT = '商品维度表(dim_sku)';


DROP TABLE IF EXISTS dim_colonel;
CREATE TABLE dim_colonel(
    `colonel_id` BIGINT(20) NOT NULL  COMMENT '业务员id' ,
    `sys_code` BIGINT(20) NOT NULL   COMMENT '平台商id' ,
    `create_by` VARCHAR(64) DEFAULT NULL  COMMENT '创建人' ,
    `create_time` DATETIME(3) DEFAULT NULL   COMMENT '创建时间' ,
    `update_by` VARCHAR(64) DEFAULT NULL  COMMENT '更新人' ,
    `update_time` DATETIME(3) DEFAULT NULL   COMMENT '更新时间' ,
    `area_id` BIGINT(20)  DEFAULT NULL COMMENT '城市id' ,
    `colonel_phone` VARCHAR(16) DEFAULT NULL  COMMENT '业务员手机号' ,
    `colonel_name` VARCHAR(64) DEFAULT NULL  COMMENT '业务员名' ,
    `colonel_level` INT(2)  DEFAULT NULL COMMENT '业务员级别（职务）(数据字典) 1：业务员专员 2：业务经理' ,
    `pcolonel_id` BIGINT(20) DEFAULT NULL  COMMENT '上级业务员-父业务员id' ,
    `sex` INT(1)  DEFAULT NULL COMMENT '性别（数据字典）0：男 ，1：女' ,
    `sex_val` VARCHAR(10)  DEFAULT NULL COMMENT '性别 对应值' ,
    `status` INT(1) DEFAULT NULL  COMMENT '状态 0正常 1停用' ,
    `status_val` VARCHAR(10) DEFAULT NULL  COMMENT '状态 对应值' ,
    `birthday` DATETIME(3) DEFAULT NULL  COMMENT '出生日期' ,
    `birthplace` VARCHAR(16) DEFAULT NULL  COMMENT '籍贯' ,
    `entry_date` DATETIME(3) DEFAULT NULL  COMMENT '入职日期' ,
    `edu` VARCHAR(16) DEFAULT NULL  COMMENT '学历(数据字典)' ,
    `idcard` VARCHAR(32) DEFAULT NULL  COMMENT '身份证号' ,
    `percentage_rate` DECIMAL(19,6) DEFAULT NULL  COMMENT '提成系数' ,
    `contact_addr` VARCHAR(128) DEFAULT NULL  COMMENT '联系地址' ,
    `memo` VARCHAR(128) DEFAULT NULL  COMMENT '备注' ,
    `is_colonel_admin` varchar(1)  DEFAULT NULL COMMENT '是否是业务员管理员 Y-是 N-否;是业务员管理员，不需要选择上级业务员（pcolonel_id）' ,
    `is_colonel_admin_val` VARCHAR(10)  DEFAULT NULL COMMENT '是否是业务员管理员 对应值' ,
    `dept_id` BIGINT(20)  DEFAULT NULL COMMENT '部门id（数据字典）' ,
    `app_order_price_adjust` varchar(1) DEFAULT NULL COMMENT 'APP下单改价 字典（Y:是，N:否）',
    `app_order_price_adjust_val` varchar(10) DEFAULT NULL COMMENT 'APP下单改价 对应值',
    `app_after_price_adjust` varchar(1) DEFAULT NULL COMMENT 'APP退货改价 字典（Y:是，N:否）',
    `app_after_price_adjust_val` varchar(10) DEFAULT NULL COMMENT 'APP退货改价 对应值',
    `order_auto_approve` varchar(1) DEFAULT NULL COMMENT '下单自动审核 字典（Y:是，N:否）',
    `order_auto_approve_val` varchar(10) DEFAULT NULL COMMENT '下单自动审核 对应值',
    `del_flag` TINYINT(1) DEFAULT '0' COMMENT '删除状态(0:正常，2：删除)',
    `del_flag_val` VARCHAR(10) DEFAULT '正常' COMMENT '删除状态 对应值',
    `user_id` BIGINT(20) DEFAULT NULL COMMENT '用户ID',
    `source` VARCHAR(8) DEFAULT NULL COMMENT '来源(PC、APP)',
    `audit_state` TINYINT(1) DEFAULT '1' COMMENT '审核状态 （0待审核 1审核通过 2审核不通过）',
    `audit_state_val` VARCHAR(10) DEFAULT '审核通过' COMMENT '审核状态 对应值',
    `audit_by` VARCHAR(64) DEFAULT NULL COMMENT '审核人',
    `audit_time` DATETIME(3) DEFAULT NULL COMMENT '审核时间',
    `audit_memo` VARCHAR(255) DEFAULT NULL COMMENT '审核备注',
    `develop_people_id` BIGINT(20) DEFAULT NULL COMMENT '发展人id',
    `avatar_images` VARCHAR(255) DEFAULT NULL COMMENT '业务员头像',
    `avatar_update_time` DATETIME(3) DEFAULT NULL COMMENT '业务员头像修改时间',
    `three_area_city_id` BIGINT(20) DEFAULT NULL COMMENT '三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)',
    PRIMARY KEY (colonel_id)
)  COMMENT = '业务员维度表(dim_colonel)';

DROP TABLE IF EXISTS dim_member;
CREATE TABLE dim_member(
    `member_id` BIGINT(20) NOT NULL  COMMENT '用户id' ,
    `sys_code` BIGINT(20)   COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` VARCHAR(64)   COMMENT '更新人' ,
    `update_time` DATETIME(3)   COMMENT '更新时间' ,
    `member_phone` VARCHAR(16)   COMMENT '用户手机号;手机号平台商级唯一' ,
    `member_name` VARCHAR(255)   COMMENT '用户名' ,
    `wx_unionid` VARCHAR(100)   COMMENT '微信unionid' ,
    `avatar` VARCHAR(255)   COMMENT '头像' ,
    `status` TINYINT(1)   COMMENT '状态：1启用  0停用' ,
    `status_val` VARCHAR(10)   COMMENT '状态对应 字典值' ,
    `xcx_openid` VARCHAR(100)   COMMENT '小程序openid' ,
    `register_colonel_id` BIGINT(20)   COMMENT '注册业务员id' ,
    `memo` VARCHAR(255)   COMMENT '备注' ,
    `last_login_time` datetime(3) DEFAULT NULL COMMENT '最后一次登录时间',
    `expiration_date` datetime DEFAULT NULL COMMENT '过期时间',
    `user_name` VARCHAR(255) DEFAULT NULL COMMENT '用户账号',
    `is_shop_manager` TINYINT(1) DEFAULT NULL COMMENT '是否为店长用户 0否 1是',
    `is_shop_manager_val` VARCHAR(10) DEFAULT NULL COMMENT '是否为店长用户对应 字典值',
    `pid` bigint(20) DEFAULT NULL COMMENT '父ID',
    `publish_openid` VARCHAR(64) DEFAULT NULL COMMENT '公众号openid',
    `is_colonel` TINYINT(1) DEFAULT '0' COMMENT '是否业务员 1:是 0:否 (默认：否)；业务员代客下单身份',
    `is_colonel_val` VARCHAR(10)   COMMENT '是否业务员对应 字典值' ,
    `relate_colonel_id` bigint(20) DEFAULT NULL COMMENT '关联业务员ID',
    PRIMARY KEY (member_id)
)  COMMENT = '门店用户维度表(dim_member)';

DROP TABLE IF EXISTS dim_branch;
CREATE TABLE dim_branch(
    `branch_id` BIGINT(20) NOT NULL COMMENT '门店id',
    `branch_no` VARCHAR(30) DEFAULT NULL COMMENT '门店编号',
    `sys_code` BIGINT(20) NOT NULL COMMENT '平台商id',
    `create_by` VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    `create_time` DATETIME(3) DEFAULT NULL COMMENT '创建时间',
    `update_by` VARCHAR(64) DEFAULT NULL COMMENT '更新人',
    `update_time` DATETIME(3) DEFAULT NULL COMMENT '更新时间',
    `branch_name` VARCHAR(55) DEFAULT NULL COMMENT '门店名称',
    `area_id` BIGINT(20) DEFAULT NULL COMMENT '城市id',
    `colonel_id` BIGINT(20) DEFAULT NULL COMMENT '业务员id',
    `branch_addr` VARCHAR(255) DEFAULT NULL COMMENT '门店地址',
    `longitude` DECIMAL(12,8) DEFAULT NULL COMMENT '经度',
    `latitude` DECIMAL(12,8) DEFAULT NULL COMMENT '纬度',
    `channel_id` BIGINT(20) DEFAULT NULL COMMENT '渠道id',
    `group_id` BIGINT(20) DEFAULT NULL COMMENT '平台商城市分组id',
    `contact_name` VARCHAR(32) DEFAULT NULL COMMENT '联系人',
    `contact_phone` VARCHAR(16) DEFAULT NULL COMMENT '联系电话',
    `memo` VARCHAR(550) DEFAULT NULL COMMENT '备注',
    `status` TINYINT(1) DEFAULT NULL COMMENT '状态 1：启用，0：停用',
    `status_val` VARCHAR(10) DEFAULT NULL COMMENT '状态 对应值',
    `audit_state` TINYINT(1) DEFAULT NULL COMMENT '审核状态（数据字典：sys_audit_state 0：未审核，1：已审核，2：已作废）',
    `audit_state_val` VARCHAR(20) DEFAULT NULL COMMENT '审核状态 对应值 ',
    `audit_by` VARCHAR(64) DEFAULT NULL COMMENT '审核人',
    `audit_time` DATETIME(3) DEFAULT NULL COMMENT '审核时间',
    `del_flag` TINYINT(1) DEFAULT '0' COMMENT '删除状态(0:正常，2：删除)',
    `del_flag_val` VARCHAR(10) DEFAULT '正常' COMMENT '删除状态 对应值',
    `expiration_date` DATETIME DEFAULT NULL COMMENT '过期时间',
    `sale_price_code` TINYINT(2) DEFAULT NULL COMMENT '价格码-数据字典（1，2，3，4，5，6））',
    `sale_price_code_val` VARCHAR(20) DEFAULT NULL COMMENT '价格码 对应字典值',
    `hdfk_support` TINYINT(1) DEFAULT NULL COMMENT '是否支持货到付款(0,否 1,是)',
    `hdfk_support_val` VARCHAR(10) DEFAULT NULL COMMENT '是否支持货到付款 对应值',
    `branch_images` VARCHAR(255) DEFAULT NULL COMMENT '门头照',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后一次登陆时间',
    `hdfk_max_amt` DECIMAL(12,2) DEFAULT NULL COMMENT '货到付款最大可欠款金额',
    `three_area_city_id` BIGINT(20) DEFAULT NULL COMMENT '三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)',
    `wechat_merchant_auth_openid` VARCHAR(32) DEFAULT NULL COMMENT '微信商户认证openid',
    PRIMARY KEY (`branch_id`)
)  COMMENT = '门店维度表(dim_branch)';


DROP TABLE IF EXISTS dim_area;
CREATE TABLE dim_area(
`area_id` INT AUTO_INCREMENT COMMENT '区域城市id' ,
`sys_code` BIGINT(20)   COMMENT '平台商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`update_by` VARCHAR(64)   COMMENT '更新人' ,
`update_time` DATETIME(3)   COMMENT '更新时间' ,
`pid` BIGINT(20)   COMMENT '父id' ,
`area_name` VARCHAR(255)   COMMENT '区域城市名' ,
`status` INT(1)   COMMENT '状态' ,
`status_val` VARCHAR(10) DEFAULT NULL COMMENT '状态 对应值',
`memo` VARCHAR(255)   COMMENT '备注' ,
`dc_id` BIGINT(20)   COMMENT '运营商id' ,
`local_flag` VARCHAR(255)   COMMENT '是否开通本地配送业务' ,
`local_flag_val` VARCHAR(10) DEFAULT NULL COMMENT '是否开通本地配送业务 对应值',
`group_id` BIGINT(20)   COMMENT '平台商城市分组id' ,
`level` INT(11) DEFAULT NULL COMMENT '级别',
`three_area_city_id` BIGINT(20) DEFAULT NULL COMMENT '三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)',
`sort_num` INT(10) DEFAULT NULL COMMENT '城市排序号',
`pname` VARCHAR(255)   COMMENT '父区域城市名' ,
PRIMARY KEY (area_id)
)  COMMENT = '区域城市维度表(dim_area)';

DROP TABLE IF EXISTS dim_supplier;
CREATE TABLE dim_supplier(
    `supplier_id` bigint(20) AUTO_INCREMENT COMMENT '入驻商id' ,
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `create_by` VARCHAR(64) DEFAULT NULL  COMMENT '创建人' ,
    `create_time` DATETIME(3) DEFAULT NULL  COMMENT '创建时间' ,
    `update_by` VARCHAR(64) DEFAULT NULL  COMMENT '更新人' ,
    `update_time` DATETIME(3) DEFAULT NULL  COMMENT '更新时间' ,
    `supplier_code` VARCHAR(128) DEFAULT NULL COMMENT '入驻商编号',
    `supplier_name` VARCHAR(64) DEFAULT NULL  COMMENT '入驻商名称' ,
    `contact_name` VARCHAR(32)  DEFAULT NULL COMMENT '联系人' ,
    `contact_phone` VARCHAR(16) DEFAULT NULL  COMMENT '联系电话' ,
    `contact_address` VARCHAR(255) DEFAULT NULL  COMMENT '联系地址' ,
    `status` INT(1)   COMMENT '状态 1：启用 ，0：停用' ,
    `status_val` VARCHAR(10)   COMMENT '状态 对应值' ,
    `memo` VARCHAR(255)   COMMENT '备注' ,
    `dzwl_flag` INT(1)   COMMENT '是否是电子围栏入驻商' ,
    `dzwl_flag_val`  VARCHAR(10)   COMMENT '是否是电子围栏入驻商 对应值' ,
    `dzwl_info` text  COMMENT '电子围栏' ,
    `dc_id` BIGINT(20)   COMMENT '运营商id' ,
    `min_amt` DECIMAL(12,2)   COMMENT '起送费' ,
    `licence_url` VARCHAR(255)   COMMENT '营业执照图片地址' ,
    `user_id` BIGINT(20) DEFAULT NULL COMMENT '入驻商用户id',
    `global_min_amt` DECIMAL(12,2) DEFAULT '0.00' COMMENT '全国起送价',
    `min_settle_amt` DECIMAL(12,2) DEFAULT '0.00' COMMENT '支付商户最小保留金',
    `publish_openid` VARCHAR(64)  DEFAULT NULL COMMENT '公众号openid',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '入驻商头像地址',
    PRIMARY KEY (supplier_id)

)  COMMENT = '入驻商维度表(dim_supplier)';


DROP TABLE IF EXISTS dim_dc;
CREATE TABLE dim_dc(
    `dc_id` BIGINT(20) NOT NULL  COMMENT '运营商id' ,
    `sys_code` BIGINT(20) DEFAULT NULL  COMMENT '平台商id' ,
    `create_by` VARCHAR(64) DEFAULT NULL  COMMENT '创建人' ,
    `create_time` DATETIME(3) DEFAULT NULL  COMMENT '创建时间' ,
    `update_by` VARCHAR(64) DEFAULT NULL  COMMENT '更新人' ,
    `update_time` DATETIME(3) DEFAULT NULL  COMMENT '更新时间' ,
    `status` INT(1) DEFAULT NULL  COMMENT '状态 0 :启用 1：停用' ,
    `status_val` VARCHAR(10) DEFAULT NULL  COMMENT '状态 对应值' ,
    `del_flag` char(1) DEFAULT NULL COMMENT '删除状态  (0正常  2已删除)',
    `del_flag_val` VARCHAR(10) DEFAULT '0' COMMENT '删除状态  对应值',
    `memo` VARCHAR(255) DEFAULT NULL  COMMENT '备注' ,
    `address` VARCHAR(255) DEFAULT NULL COMMENT '运营商地址',
    `dc_code` VARCHAR(64) DEFAULT NULL COMMENT '运营商编号',
    `dc_name` VARCHAR(64) DEFAULT NULL COMMENT '运营商名称',
    `contract_name` VARCHAR(64) DEFAULT NULL COMMENT '联系人',
    `contract_phone` VARCHAR(64) DEFAULT NULL COMMENT '联系电话',
    `company_name` VARCHAR(64) DEFAULT NULL COMMENT '公司名称',
    `sys_area_id` BIGINT(20) DEFAULT NULL COMMENT '业务城市ID',
    `user_id` BIGINT(20) DEFAULT NULL COMMENT '运营商用户账号id',
    PRIMARY KEY (dc_id)
)  COMMENT = '运营商维度表(dim_dc)';


DROP TABLE IF EXISTS dim_partner;
CREATE TABLE dim_partner(
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` VARCHAR(64)   COMMENT '更新人' ,
    `update_time` DATETIME(3)   COMMENT '更新时间' ,
    `partner_user_id` INT(11) NOT NULL COMMENT '平台商用户账号id',
    `partner_name` VARCHAR(32)   COMMENT '平台商名' ,
    `contact_name` VARCHAR(32)   COMMENT '联系人' ,
    `contact_phone` VARCHAR(16)   COMMENT '联系电话' ,
    `contact_address` VARCHAR(255)   COMMENT '联系地址' ,
    `province_code` INT(11) DEFAULT NULL COMMENT '省',
    `city_code` INT(11) DEFAULT NULL COMMENT '市',
    `area_code` INT(11) DEFAULT NULL COMMENT '区',
    `logo_name` VARCHAR(20) DEFAULT NULL COMMENT '平台商公司简称',
    `logo_pic` VARCHAR(255) DEFAULT NULL COMMENT '平台商公司logo',
    `area_num` INT(10) DEFAULT NULL COMMENT '城市数量',
    `dc_num` INT(10) DEFAULT NULL COMMENT '运营商数量',
    `supplier_num` INT(10) DEFAULT NULL COMMENT '入驻商数量',
    `partner_code` VARCHAR(64) DEFAULT NULL COMMENT '平台商对外显示编号',
    `sid` BIGINT(20) DEFAULT NULL COMMENT '猎鹰服务ID',
    `status` INT(1)   COMMENT '状态 (1:未审核,2:启用,3:停用) ' ,
    `status_val` VARCHAR(10)   COMMENT '状态 对应值' ,
    `memo` VARCHAR(255)   COMMENT '备注' ,
    `source` VARCHAR(16)   COMMENT '来源' ,
    PRIMARY KEY (sys_code)
)  COMMENT = '平台商维度表(dim_partner)';


DROP TABLE IF EXISTS rpt_tag_def;
CREATE TABLE `rpt_tag_def` (
                               `tag_def_id` bigint(20) NOT NULL COMMENT '标签定义id',
                               `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                               `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                               `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                               `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                               `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                               `tag_name` varchar(32) DEFAULT NULL COMMENT '标签名',
                               `tag_rule_json` text COMMENT '标签规则json',
                               `enabled` int(1) DEFAULT NULL COMMENT '是否启用 1-是 0-否',
                               `tag_type` varchar(32) DEFAULT NULL COMMENT '标签类型（数据字典）',
                               `tag_category` varchar(32) DEFAULT NULL COMMENT '标签分类,(客户活跃, 客户等级, 类占比)',
                               PRIMARY KEY (`tag_def_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签定义表';


DROP TABLE IF EXISTS mem_colonel_branch_zip;
CREATE TABLE mem_colonel_branch_zip(
`colonel_branch_zip_id` bigint(20) NOT NULL COMMENT 'ID',
`sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`branch_id` BIGINT(20) NOT NULL  COMMENT '门店id' ,
`colonel_id` BIGINT(20) NOT NULL  COMMENT '业务员id' ,
`start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
`end_date` DATETIME NOT NULL  COMMENT '结束日期',
PRIMARY KEY (`colonel_branch_zip_id`)
)  COMMENT = '门店业务员关系拉链表';


DROP TABLE IF EXISTS mem_colonel_hierarchy_zip;
CREATE TABLE mem_colonel_hierarchy_zip(
`colonel_hierarchy_zip_id` bigint(20) NOT NULL COMMENT 'ID',
`sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`pcolonel_id` BIGINT(20) NOT NULL  COMMENT '上级业务员id' ,
`colonel_id` BIGINT(20) NOT NULL  COMMENT '业务员id' ,
`start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
`end_date` DATETIME NOT NULL  COMMENT '结束日期',
PRIMARY KEY (`colonel_hierarchy_zip_id`)
)  COMMENT = '业务员上下级关系拉链表';

DROP TABLE IF EXISTS sys_area_supplier_zip;
CREATE TABLE sys_area_supplier_zip(
`area_supplier_zip_id` bigint(20) NOT NULL COMMENT 'ID',
`sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`area_id` BIGINT(20) NOT NULL  COMMENT '区域城市id' ,
`supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
`start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
`end_date` DATETIME NOT NULL  COMMENT '结束日期',
PRIMARY KEY (`area_supplier_zip_id`)
)  COMMENT = '区域城市入驻商关系拉链表';

DROP TABLE IF EXISTS sys_dc_area_zip;
CREATE TABLE sys_dc_area_zip(
`dc_area_zip_id` bigint(20) NOT NULL COMMENT 'ID',
`sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`dc_id` BIGINT(20) NOT NULL  COMMENT '运营商id' ,
`area_id` BIGINT(20) NOT NULL  COMMENT '区域城市id' ,
`start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
`end_date` DATETIME NOT NULL  COMMENT '结束日期',
PRIMARY KEY (`dc_area_zip_id`)
)  COMMENT = '运营商区域城市拉链表';

DROP TABLE IF EXISTS prdt_area_item_zip;
CREATE TABLE prdt_area_item_zip(
    `area_item_zip_id` bigint(20) NOT NULL COMMENT 'ID',
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
    `spu_id` BIGINT(20) NOT NULL  COMMENT '商品SPU id' ,
    `sku_id` BIGINT(20) NOT NULL  COMMENT '商品sku id' ,
    `area_id` BIGINT(20) NOT NULL  COMMENT '城市id' ,
    `start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
    `end_date` DATETIME NOT NULL  COMMENT '结束日期',
    PRIMARY KEY (`area_item_zip_id`)
)  COMMENT = '城市上架商品拉链表';

DROP TABLE IF EXISTS prdt_supplier_item_zip;
CREATE TABLE prdt_supplier_item_zip(
    `supplier_item_zip_id` bigint(20) NOT NULL COMMENT 'ID',
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
    `spu_id` BIGINT(20) NOT NULL  COMMENT '商品SPU id' ,
    `sku_id` BIGINT(20) NOT NULL  COMMENT '商品sku id' ,
    `start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
    `end_date` DATETIME NOT NULL  COMMENT '结束日期',
    PRIMARY KEY (`supplier_item_zip_id`)
)  COMMENT = '全国上架商品拉链表';

DROP TABLE IF EXISTS dim_catgory;
CREATE TABLE dim_catgory(
    `catgory_id` BIGINT(20) NOT NULL COMMENT '平台商管理分类id',
    `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
    `create_by` VARCHAR(64)   COMMENT '创建人' ,
    `create_time` DATETIME(3)   COMMENT '创建时间' ,
    `update_by` VARCHAR(64) DEFAULT NULL COMMENT '更新人',
    `update_time` DATETIME(3) DEFAULT NULL COMMENT '更新时间',
    `pid` INT(11) DEFAULT NULL COMMENT '父id',
    `catgory_name` VARCHAR(255)  DEFAULT NULL COMMENT '分类名',
    `icon` VARCHAR(255)  DEFAULT NULL COMMENT '分类图标',
    `sort` INT(11) DEFAULT NULL COMMENT '排序',
    `status` VARCHAR(255) DEFAULT NULL COMMENT '状态 0-停用 1-启用',
    `partner_rate` DECIMAL(7,6) DEFAULT NULL COMMENT '平台商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01',
    `dc_rate` DECIMAL(7,6) DEFAULT NULL COMMENT '运营商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01',
    `colonel1_rate` DECIMAL(7,6) DEFAULT NULL COMMENT '业务员负责人分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01',
    `colonel2_rate` DECIMAL(7,6) DEFAULT NULL COMMENT '业务员分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01',
    `memo` VARCHAR(255) DEFAULT NULL COMMENT '备注',
    `level` INT(11) DEFAULT NULL COMMENT '级别',
    `catgory_no` VARCHAR(64) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '类别编号',
    `sale_total_rate` DECIMAL(10,4) DEFAULT NULL COMMENT '销售占比利润, 百分比,最高29% = 0.29, 只有一级可以设置',
    `software_rate` DECIMAL(7,6) DEFAULT NULL COMMENT '软件商分润比例 百分比的小数表现形式，1%表示为0.01',
    PRIMARY KEY (`catgory_id`)
)  COMMENT = '管理分类维度表(dim_catgory)';









