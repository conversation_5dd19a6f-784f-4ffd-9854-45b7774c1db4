INSERT INTO dim_date (
    `date_id`, `date`,`year`,`month`,`day`,`quarter`,`week_id`,`day_of_week`,`weekday_name`,`month_name`
)
SELECT
    DATE_FORMAT(date, '%Y%m%d') AS date_id,
    date,
    YEAR(date) AS year,
    MONTH(date) AS month,
    DAY(date) AS day,
    QUARTER(date) AS quarter,
    DATE_FORMAT(date, '%Y-%U') AS week_id,
    WEEKDAY(date) AS day_of_week,
    DAY<PERSON><PERSON>(date) AS day_name,
    MONTHNAME(date) AS month_name
FROM
    (
    SELECT
    '2024-01-01' + INTERVAL (a.a + (10 * b.a) + (100 * c.a)) DAY AS date
    FROM
    (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS a
    CROSS JOIN
    (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS b
    CROSS JOIN
    (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS c
    ) d
WHERE
    date BETWEEN '2024-01-01' AND '2099-12-31';



SELECT
    sdd.dict_code,
    sdd.create_by,
    sdd.create_time,
    sdd.update_by,
    sdd.update_time,
    sdd.dict_label,
    sdd.dict_value,
    sdd.dict_type,
    sdt.dict_name AS dict_type_name,
    sdd.dict_sort,
    sdd.`status`
FROM
    sys_dict_data sdd
        LEFT JOIN sys_dict_type sdt ON sdd.dict_type = sdt.dict_type;


SELECT
    sku.sku_id,
    sku.sys_code,
    sku.create_by,
    sku.create_time,
    sku.update_by,
    sku.update_time,
    sku.spu_id,
    spu.spu_name,
    spu.spu_no,
    spu.supplier_id,
    cat1.catgory_id catgory1_id,
    cat1.catgory_name catgory1_name,
    cat2.catgory_id catgory2_id,
    cat2.catgory_name catgory2_name,
    cat3.catgory_id catgory3_id,
    cat3.catgory_name catgory3_name,
    spu.brand_id,
    brand.brand_name,
    sku.unit,
    -- unit_val,
    sku.barcode,
    sku.properties,
    sku.thumb,
    sku.stock,
    sku.mark_price,
    sku.suggest_price,
    sku.cost_price,
    sku.`status`,
    sku.is_delete,
    sku.mid_barcode,
    sku.mid_mark_price,
    sku.mid_cost_price,
    sku.mid_suggest_price,
    sku.large_barcode,
    sku.large_mark_price,
    sku.large_cost_price,
    sku.large_suggest_price
FROM
    prdt_sku sku
    LEFT JOIN prdt_spu spu ON sku.spu_id = spu.spu_id
    LEFT JOIN prdt_catgory cat3 ON spu.catgory_id = cat3.catgory_id
    LEFT JOIN prdt_catgory cat2 ON cat3.pid = cat2.catgory_id
    LEFT JOIN prdt_catgory cat1 ON cat2.pid = cat1.catgory_id
    LEFT JOIN prdt_brand brand ON spu.brand_id = brand.brand_id;
