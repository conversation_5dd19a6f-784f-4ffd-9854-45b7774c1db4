{
    "job": {
        "setting": {
            "speed": {
                "channel": 1
            },
            "errorLimit": {
                "record": 0,
                "percentage": 0.02
            }
        },
        "content": [
            {
                "reader": {
                    "name": "mysqlreader",
                    "parameter": {
                        "username": "zksr_query_user",
                        "password": "zksr_query_user9876",
                        "connection": [
                            {
                                "querySql": [
                                    "
                                    SELECT
                                        tsad.supplier_after_dtl_id,
                                        tsad.sys_code,
                                        ta.dc_id,
                                        ta.area_id,
                                        ta.member_id,
                                        tsad.supplier_id,
                                        ta.colonel_id,
                                        ta.colonel_level,
                                        ta.pcolonel_id,
                                        ta.pcolonel_level,
                                        ta.branch_id,
                                        tsad.order_unit,
                                        tsad.order_unit_type,
                                        CASE
                                            WHEN tsad.order_unit_type = 1 THEN '小单位'
                                            WHEN tsad.order_unit_type = 2 THEN '中单位'
                                            WHEN tsad.order_unit_type = 3 THEN '大单位'
                                            ELSE NULL
                                        END AS order_unit_type_val,
                                        tsad.order_unit_qty,
                                        tsad.order_unit_size,
                                        tsad.return_qty,
                                        tsad.return_amt + IFNULL(return_activity_discount_amt,0) + IFNULL(return_coupon_discount_amt,0) + IFNULL(return_coupon_discount_amt2,0) AS return_order_amt,
                                        tsad.return_unit,
                                        tsad.return_unit_type,
                                        CASE
                                            WHEN tsad.return_unit_type = 1 THEN '小单位'
                                            WHEN tsad.return_unit_type = 2 THEN '中单位'
                                            WHEN tsad.return_unit_type = 3 THEN '大单位'
                                            ELSE NULL
                                        END AS return_unit_type_val,
                                        tsad.return_unit_qty,
                                        tsad.return_unit_size,
                                        tsad.sku_id,
                                        tsad.spu_id,
                                        tsad.after_no,
                                        tsad.supplier_after_no,
                                        tsad.supplier_order_dtl_id,
                                        tsad.exact_return_price,
                                        tsad.return_price,
                                        tsad.exact_return_amt,
                                        tsad.return_amt,
                                        tsad.refund_amt,
                                        tsad.return_activity_discount_amt,
                                        tsad.return_coupon_discount_amt,
                                        tsad.return_coupon_discount_amt2,
                                        tsad.return_sales_unit_price,
                                        tsad.return_original_price,
                                        tsad.return_original_unit_price,
                                        tsad.gift_flag,
                                        CASE
                                            WHEN tsad.gift_flag=1 THEN '是'
                                            WHEN tsad.gift_flag=0 THEN '否'
                                            ELSE null
                                        END AS gift_flag_val,
                                        tsas.supplier_amt,
                                        tsas.profit,
                                        tsas.cost_price,
                                        tsas.all_dc_rate,
                                        tsas.setting_dc_rate,
                                        tsas.dc_rate,
                                        tsas.dc_amt,
                                        tsas.partner_rate,
                                        tsas.partner_amt,
                                        tsas.setting_colonel1_rate,
                                        tsas.colonel1_rate,
                                        tsas.colonel1_percentage,
                                        tsas.all_colonel1_amt,
                                        tsas.colonel1_amt,
                                        tsas.setting_colonel2_rate,
                                        tsas.colonel2_rate,
                                        tsas.colonel2_percentage,
                                        tsas.all_colonel2_amt,
                                        tsas.colonel2_amt,
                                        tsad.refund_time AS return_time,
                                        tsod.create_time AS order_time,
                                        DATE_FORMAT(tsod.create_time, '%Y%m%d') AS order_date_id,
                                        DATE_FORMAT(tsad.create_time, '%Y%m%d') AS date_id,
                                        DATE_FORMAT(now(), '%Y%m%d') AS insert_date_id,
                                        tsad.approve_state,
                                        tsad.refund_state
                                    FROM
                                        trd_supplier_after_dtl tsad
                                        LEFT JOIN trd_after ta ON tsad.after_id = ta.after_id
                                        LEFT JOIN trd_supplier_after_settle tsas ON tsad.supplier_after_dtl_id = tsas.supplier_after_dtl_id
                                        LEFT JOIN trd_supplier_order_dtl tsod ON tsad.supplier_order_dtl_id = tsod.supplier_order_dtl_id
                                    WHERE
                                        tsad.after_phase = 2
                                        AND DATE(tsad.create_time) = '${dt}'
                                    ;"
                                ],
                                "jdbcUrl": [
                                    "*********************************************************************************************************************************************************"
                                ]
                            }
                        ]
                    }
                },
                "writer": {
                    "name": "mysqlwriter",
                    "parameter": {
                        "writeMode": "replace",
                        "username": "report_user",
                        "password": "report_user9876",
                        "column": [
                            "supplier_after_dtl_id",
                            "sys_code",
                            "dc_id",
                            "area_id",
                            "member_id",
                            "supplier_id",
                            "colonel_id",
                            "colonel_level",
                            "pcolonel_id",
                            "pcolonel_level",
                            "branch_id",
                            "order_unit",
                            "order_unit_type",
                            "order_unit_type_val",
                            "order_unit_qty",
                            "order_unit_size",
                            "return_qty",
                            "return_order_amt",
                            "return_unit",
                            "return_unit_type",
                            "return_unit_type_val",
                            "return_unit_qty",
                            "return_unit_size",
                            "sku_id",
                            "spu_id",
                            "after_no",
                            "supplier_after_no",
                            "supplier_order_dtl_id",
                            "exact_return_price",
                            "return_price",
                            "exact_return_amt",
                            "return_amt",
                            "refund_amt",
                            "return_activity_discount_amt",
                            "return_coupon_discount_amt",
                            "return_coupon_discount_amt2",
                            "return_sales_unit_price",
                            "return_original_price",
                            "return_original_unit_price",
                            "gift_flag",
                            "gift_flag_val",
                            "supplier_amt",
                            "profit",
                            "cost_price",
                            "all_dc_rate",
                            "setting_dc_rate",
                            "dc_rate",
                            "dc_amt",
                            "partner_rate",
                            "partner_amt",
                            "setting_colonel1_rate",
                            "colonel1_rate",
                            "colonel1_percentage",
                            "all_colonel1_amt",
                            "colonel1_amt",
                            "setting_colonel2_rate",
                            "colonel2_rate",
                            "colonel2_percentage",
                            "all_colonel2_amt",
                            "colonel2_amt",
                            "return_time",
                            "order_time",
                            "order_date_id",
                            "date_id",
                            "insert_date_id",
                            "approve_state",
                            "refund_state"
                        ],
                        "connection": [
                            {
                                "jdbcUrl": "**********************************************************************************************************************************************************",
                                "table": [
                                    "dwd_trd_return_inc"
                                ]
                            }
                        ]
                    }
                }

            }
        ]
    }
}
