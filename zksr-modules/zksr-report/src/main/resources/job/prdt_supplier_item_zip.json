{
    "job": {
        "setting": {
            "speed": {
                "channel": 1
            },
            "errorLimit": {
                "record": 0,
                "percentage": 0.02
            }
        },
        "content": [
            {
                "reader": {
                    "name": "mysqlreader",
                    "parameter": {
                        "username": "zksr_query_user",
                        "password": "zksr_query_user9876",
                        "connection": [
                            {
                                "querySql": [
                                    "SELECT
                                        supplier_item_zip_id,
                                        sys_code,
                                        create_by,
                                        create_time,
                                        supplier_id,
                                        spu_id,
                                        sku_id,
                                        start_date,
                                        end_date
                                    FROM
                                        prdt_supplier_item_zip
                                    WHERE
                                        create_time BETWEEN CONCAT( '${dt}' , ' 00:00:00.000') AND CONCAT('${dt}' , ' 23:59:59.999')
                                        OR
                                        update_time BETWEEN CONCAT( '${dt}' , ' 00:00:00.000') AND CONCAT('${dt}' , ' 23:59:59.999')"
                                ],
                                "jdbcUrl": [
                                    "***********************************************************************************************************************************************************"
                                ]
                            }
                        ]
                    }
                },
                "writer": {
                    "name": "mysqlwriter",
                    "parameter": {
                        "writeMode": "replace",
                        "username": "report_user",
                        "password": "report_user9876",
                        "column": [
                            "supplier_item_zip_id",
                            "sys_code",
                            "create_by",
                            "create_time",
                            "supplier_id",
                            "spu_id",
                            "sku_id",
                            "start_date",
                            "end_date"
                        ],
                        "connection": [
                            {
                                "jdbcUrl": "**********************************************************************************************************************************************************",
                                "table": [
                                    "prdt_supplier_item_zip"
                                ]
                            }
                        ]
                    }
                }

            }
        ]
    }
}
