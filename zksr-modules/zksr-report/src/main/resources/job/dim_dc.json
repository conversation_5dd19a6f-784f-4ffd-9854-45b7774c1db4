{
  "job": {
    "setting": {
      "speed": {
        "channel": 1
      },
      "errorLimit": {
        "record": 0,
        "percentage": 0.02
      }
    },
    "content": [
      {
        "reader": {
          "name": "mysqlreader",
          "parameter": {
            "username": "zksr_query_user",
            "password": "zksr_query_user9876",
            "connection": [
              {
                "querySql": [
                  "SELECT
                    dc_id,
                    sys_code,
                    create_by,
                    create_time,
                    update_by,
                    update_time,
                    status,
                    CASE
                      WHEN status = '0' THEN '启用'
                      WHEN status = '1' THEN '停用'
                      ELSE '其他'
                    END AS status_val,
                    del_flag,
                    CASE
                      WHEN del_flag = '0' THEN '正常'
                      ELSE '删除'
                    END AS del_flag_val,
                    memo,
                    address,
                    dc_code,
                    dc_name,
                    contract_name,
                    contract_phone,
                    company_name,
                    sys_area_id,
                    user_id
                  FROM
                    sys_dc;"
                ],
                "jdbcUrl": [
                  "jdbc:mysql://**************:3306/zksr-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8"
                ]
              }
            ]
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "writeMode": "replace",
            "username": "report_user",
            "password": "report_user9876",
            "column": [
              "dc_id",
              "sys_code",
              "create_by",
              "create_time",
              "update_by",
              "update_time",
              "status",
              "status_val",
              "del_flag",
              "del_flag_val",
              "memo",
              "address",
              "dc_code",
              "dc_name",
              "contract_name",
              "contract_phone",
              "company_name",
              "sys_area_id",
              "user_id"
            ],
            "connection": [
              {
                "jdbcUrl": "**********************************************************************************************************************************************************",
                "table": [
                  "dim_dc"
                ]
              }
            ]
          }
        }

      }
    ]
  }
}
