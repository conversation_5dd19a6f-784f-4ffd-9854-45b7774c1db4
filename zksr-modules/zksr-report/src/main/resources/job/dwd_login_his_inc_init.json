{
    "job": {
        "setting": {
            "speed": {
                 "channel": 1
            },
            "errorLimit": {
                "record": 0,
                "percentage": 0.02
            }
        },
        "content": [
            {
                "reader": {
                    "name": "mysqlreader",
                    "parameter": {
                        "username": "zksr_query_user",
                        "password": "zksr_query_user9876",
                        "connection": [
                            {
                                "querySql": [
                                    ",
                                    SELECT
                                        mlh.login_his_id
                                        ,mlh.create_time
                                        ,mlh.sys_code
                                        ,mlh.date_id
                                        ,mlh.wx_openid
                                        ,mlh.member_phone
                                        ,mlh.member_username
                                        ,mlh.branch_id
                                        ,mb.area_id
                                        ,mlh.ip
                                        ,mlh.tp
                                        ,mlh.device_id
                                        ,mlh.device_type
                                        ,mlh.device_brand
                                        ,mlh.device_model
                                        ,mlh.os_name
                                        ,mlh.os_version
                                        ,mlh.port
                                        ,DATE_FORMAT(now(), '%Y%m%d')
                                    FROM
                                        mem_login_his mlh
                                        LEFT JOIN mem_branch mb ON mlh.branch_id = mb.branch_id
                                    ;"
                                ],
                                "jdbcUrl": [
                                    "jdbc:mysql://**************:3306/zksr-member?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8"
                                ]
                            }
                        ]
                    }
                },
                "writer": {
                    "name": "mysqlwriter",
                    "parameter": {
                        "writeMode": "replace",
                        "username": "report_user",
                        "password": "report_user9876",
                        "column": [
                            "login_his_id",
                            "create_time",
                            "sys_code",
                            "date_id",
                            "wx_openid",
                            "member_phone",
                            "member_username",
                            "branch_id",
                            "area_id",
                            "ip",
                            "tp",
                            "device_id",
                            "device_type",
                            "device_brand",
                            "device_model",
                            "os_name",
                            "os_version",
                            "port",
                            "insert_date_id"
                        ],
                        "connection": [
                            {
                                "jdbcUrl": "jdbc:mysql://**************:3306/zksr-report?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8",
                                "table": [
                                    "dwd_login_his_inc"
                                ]
                            }
                        ]
                    }
                }

            }
        ]
    }
}
