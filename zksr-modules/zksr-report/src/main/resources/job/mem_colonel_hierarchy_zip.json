{
    "job": {
        "setting": {
            "speed": {
                "channel": 1
            },
            "errorLimit": {
                "record": 0,
                "percentage": 0.02
            }
        },
        "content": [
            {
                "reader": {
                    "name": "mysqlreader",
                    "parameter": {
                        "username": "zksr_query_user",
                        "password": "zksr_query_user9876",
                        "connection": [
                            {
                                "querySql": [
                                    "SELECT
                                        colonel_hierarchy_zip_id,
                                        sys_code,
                                        create_by,
                                        create_time,
                                        pcolonel_id,
                                        colonel_id,
                                        start_date,
                                        end_date
                                    FROM
                                        mem_colonel_hierarchy_zip
                                    WHERE
                                        create_time BETWEEN CONCAT( '${dt}' , ' 00:00:00.000') AND CONCAT('${dt}' , ' 23:59:59.999')
                                        OR
                                        update_time BETWEEN CONCAT( '${dt}' , ' 00:00:00.000') AND CONCAT('${dt}' , ' 23:59:59.999')"
                                ],
                                "jdbcUrl": [
                                    "jdbc:mysql://**************:3306/zksr-member?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8"
                                ]
                            }
                        ]
                    }
                },
                "writer": {
                    "name": "mysqlwriter",
                    "parameter": {
                        "writeMode": "replace",
                        "username": "report_user",
                        "password": "report_user9876",
                        "column": [
                            "colonel_hierarchy_zip_id",
                            "sys_code",
                            "create_by",
                            "create_time",
                            "pcolonel_id",
                            "colonel_id",
                            "start_date",
                            "end_date"
                        ],
                        "connection": [
                            {
                                "jdbcUrl": "**********************************************************************************************************************************************************",
                                "table": [
                                    "mem_colonel_hierarchy_zip"
                                ]
                            }
                        ]
                    }
                }

            }
        ]
    }
}
