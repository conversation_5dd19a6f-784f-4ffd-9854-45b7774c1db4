{
  "job": {
    "setting": {
      "speed": {
        "channel": 1
      },
      "errorLimit": {
        "record": 0,
        "percentage": 0.02
      }
    },
    "content": [
      {
        "reader": {
          "name": "mysqlreader",
          "parameter": {
            "username": "zksr_query_user",
            "password": "zksr_query_user9876",
            "connection": [
              {
                "querySql": [
                  "SELECT
                    area.area_id,
                    area.sys_code,
                    area.create_by,
                    area.create_time,
                    area.update_by,
                    area.update_time,
                    area.pid,
                    area.area_name,
                    area.status,
                    CASE
                      WHEN area.status = 0 THEN '停用'
                      WHEN area.status = 1 THEN '启用'
                      ELSE '其他'
                    END AS status_val,
                    area.memo,
                    area.dc_id,
                    area.local_flag,
                    CASE
                      WHEN area.local_flag = '1' THEN '是'
                      WHEN area.local_flag = '0' THEN '否'
                      ELSE '其他'
                    END AS local_flag_val,
                    area.group_id,
                    area.level,
                    area.three_area_city_id,
                    area.sort_num,
                    pArea.area_name AS pname
                  FROM
                    sys_area area
                    LEFT JOIN sys_area pArea ON area.pid = pArea.area_id;"
                ],
                "jdbcUrl": [
                  "*********************************************************************************************************************************************************"
                ]
              }
            ]
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "writeMode": "replace",
            "username": "report_user",
            "password": "report_user9876",
            "column": [
              "area_id",
              "sys_code",
              "create_by",
              "create_time",
              "update_by",
              "update_time",
              "pid",
              "area_name",
              "status",
              "status_val",
              "memo",
              "dc_id",
              "local_flag",
              "local_flag_val",
              "group_id",
              "level",
              "three_area_city_id",
              "sort_num",
              "pname"
            ],
            "connection": [
              {
                "jdbcUrl": "**********************************************************************************************************************************************************",
                "table": [
                  "dim_area"
                ]
              }
            ]
          }
        }

      }
    ]
  }
}
