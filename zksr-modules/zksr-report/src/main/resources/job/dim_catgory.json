{
  "job": {
    "setting": {
      "speed": {
        "channel": 1
      },
      "errorLimit": {
        "record": 0,
        "percentage": 0.02
      }
    },
    "content": [
      {
        "reader": {
          "name": "mysqlreader",
          "parameter": {
            "username": "zksr_query_user",
            "password": "zksr_query_user9876",
            "connection": [
              {
                "querySql": [
                  "SELECT
                    catgory_id
                    ,sys_code
                    ,create_by
                    ,create_time
                    ,update_by
                    ,update_time
                    ,pid
                    ,catgory_name
                    ,icon
                    ,sort
                    ,status
                    ,partner_rate
                    ,dc_rate
                    ,colonel1_rate
                    ,colonel2_rate
                    ,memo
                    ,level
                    ,catgory_no
                    ,sale_total_rate
                    ,software_rate
                  FROM
                    prdt_catgory
                  ;"
                ],
                "jdbcUrl": [
                  "***********************************************************************************************************************************************************"
                ]
              }
            ]
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "writeMode": "replace",
            "username": "report_user",
            "password": "report_user9876",
            "column": [
              "catgory_id",
              "sys_code",
              "create_by",
              "create_time",
              "update_by",
              "update_time",
              "pid",
              "catgory_name",
              "icon",
              "sort",
              "status",
              "partner_rate",
              "dc_rate",
              "colonel1_rate",
              "colonel2_rate",
              "memo",
              "level",
              "catgory_no",
              "sale_total_rate",
              "software_rate"
            ],
            "connection": [
              {
                "jdbcUrl": "**********************************************************************************************************************************************************",
                "table": [
                  "dim_catgory"
                ]
              }
            ]
          }
        }

      }
    ]
  }
}
