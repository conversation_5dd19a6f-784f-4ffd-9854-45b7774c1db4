{
  "job": {
    "setting": {
      "speed": {
        "channel": 1
      },
      "errorLimit": {
        "record": 0,
        "percentage": 0.02
      }
    },
    "content": [
      {
        "reader": {
          "name": "mysqlreader",
          "parameter": {
            "username": "zksr_query_user",
            "password": "zksr_query_user9876",
            "connection": [
              {
                "querySql": [
                  "SELECT
                    supplier_id,
                    sys_code,
                    create_by,
                    create_time,
                    update_by,
                    update_time,
                    supplier_code,
                    supplier_name,
                    contact_name,
                    contact_phone,
                    contact_address,
                    status,
                    CASE
                      WHEN status = 1 THEN '启用'
                      ELSE '否'
                    END AS status_val,
                    memo,
                    dzwl_flag,
                    CASE
                      WHEN dzwl_flag = 1 THEN '启用'
                      ELSE '否'
                    END AS dzwl_flag_val,
                    dzwl_info,
                    dc_id,
                    min_amt,
                    licence_url,
                    user_id,
                    global_min_amt,
                    min_settle_amt,
                    publish_openid,
                    avatar
                  FROM
                    sys_supplier;"
                ],
                "jdbcUrl": [
                  "jdbc:mysql://**************:3306/zksr-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8"
                ]
              }
            ]
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "writeMode": "replace",
            "username": "report_user",
            "password": "report_user9876",
            "column": [
              "supplier_id",
              "sys_code",
              "create_by",
              "create_time",
              "update_by",
              "update_time",
              "supplier_code",
              "supplier_name",
              "contact_name",
              "contact_phone",
              "contact_address",
              "status",
              "status_val",
              "memo",
              "dzwl_flag",
              "dzwl_flag_val",
              "dzwl_info",
              "dc_id",
              "min_amt",
              "licence_url",
              "user_id",
              "global_min_amt",
              "min_settle_amt",
              "publish_openid",
              "avatar"
            ],
            "connection": [
              {
                "jdbcUrl": "**********************************************************************************************************************************************************",
                "table": [
                  "dim_supplier"
                ]
              }
            ]
          }
        }

      }
    ]
  }
}
