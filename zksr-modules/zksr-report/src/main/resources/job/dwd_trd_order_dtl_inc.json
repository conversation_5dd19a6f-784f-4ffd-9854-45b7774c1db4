{
    "job": {
        "setting": {
            "speed": {
                 "channel": 1
            },
            "errorLimit": {
                "record": 0,
                "percentage": 0.02
            }
        },
        "content": [
            {
                "reader": {
                    "name": "mysqlreader",
                    "parameter": {
                        "username": "zksr_query_user",
                        "password": "zksr_query_user9876",
                        "connection": [
                            {
                                "querySql": [
                                    "
                                    SELECT
                                        tsod.supplier_order_dtl_id
                                        ,torder.order_id
                                        ,torder.order_no
                                        ,torder.dc_id
                                        ,torder.area_id
                                        ,torder.member_id
                                        ,torder.colonel_id
                                        ,torder.colonel_level
                                        ,torder.pcolonel_id
                                        ,torder.pcolonel_level
                                        ,torder.branch_id
                                        ,tsod.sub_order_amt
                                        ,torder.colonel_flag
                                        ,case when torder.colonel_flag=1 then '是'
                                            when torder.colonel_flag=0 then '否'
                                            else null end colonel_flag_val
                                        ,torder.source
                                        ,case when torder.source='mini' then '小程序'
                                            when torder.source='ywyApp' then '业务员APP'
                                            else null end source_val
                                        ,tsod.sys_code
                                        ,tsod.item_type
                                        ,case when tsod.item_type=1 then '本地商品'
                                            when tsod.item_type=0 then '全国商品'
                                            else null end item_type_val
                                        ,tsod.supplier_order_no
                                        ,tsod.supplier_order_dtl_no
                                        ,tsod.supplier_id
                                        ,tsod.sku_id
                                        ,tsod.spu_id
                                        ,tsod.total_num
                                        ,tsod.exact_total_amt
                                        ,tsod.total_amt
                                        ,tsod.exact_price
                                        ,tsod.price
                                        ,tsod.sale_price
                                        ,tsod.activity_discount_amt
                                        ,tsod.coupon_discount_amt
                                        ,tsod.coupon_discount_amt2
                                        ,tsod.gift_flag
                                        ,case when tsod.gift_flag=1 then '是'
                                            when tsod.gift_flag=0 then '否'
                                            else null end gift_flag_val
                                        ,tsod.order_unit
                                        ,null    -- tsod.order_unit_val
                                        ,tsod.order_unit_type
                                        ,CASE
                                            WHEN order_unit_type = 1 THEN '小单位'
                                            WHEN order_unit_type = 2 THEN '中单位'
                                            WHEN order_unit_type = 3 THEN '大单位'
                                            ELSE NULL
                                        END AS order_unit_type_val
                                        ,tsod.order_unit_qty
                                        ,tsos.supplier_amt
                                        ,tsos.profit
                                        ,tsos.cost_price
                                        ,tsos.all_dc_rate
                                        ,tsos.setting_dc_rate
                                        ,tsos.dc_rate
                                        ,tsos.dc_amt
                                        ,tsos.partner_rate
                                        ,tsos.partner_amt
                                        ,tsos.setting_colonel1_rate
                                        ,tsos.colonel1_rate
                                        ,tsos.colonel1_percentage
                                        ,tsos.all_colonel1_amt
                                        ,tsos.colonel1_amt
                                        ,tsos.setting_colonel2_rate
                                        ,tsos.colonel2_rate
                                        ,tsos.colonel2_percentage
                                        ,tsos.all_colonel2_amt
                                        ,tsos.colonel2_amt
                                        ,tsod.create_time order_time
                                        ,DATE_FORMAT(tsod.create_time, '%Y%m%d')
                                        ,DATE_FORMAT(now(), '%Y%m%d')
                                        ,tsod.pay_state
                                        ,torder.pay_way
                                        ,tsod.category_id
                                    FROM
                                        trd_supplier_order_dtl tsod
                                        LEFT JOIN trd_supplier_order_settle tsos on tsod.supplier_order_dtl_id = tsos.supplier_order_dtl_id
                                        LEFT JOIN trd_order torder on torder.order_id = tsod.order_id
                                    WHERE
                                        torder.pay_state in (1,3,4)
                                        AND DATE(tsod.create_time) = '${dt}'
                                    ;"
                                ],
                                "jdbcUrl": [
                                    "*********************************************************************************************************************************************************"
                                ]
                            }
                        ]
                    }
                },
                "writer": {
                    "name": "mysqlwriter",
                    "parameter": {
                        "writeMode": "replace",
                        "username": "report_user",
                        "password": "report_user9876",
                        "column": [
                            "supplier_order_dtl_id",
                            "order_id",
                            "order_no",
                            "dc_id",
                            "area_id",
                            "member_id",
                            "colonel_id",
                            "colonel_level",
                            "pcolonel_id",
                            "pcolonel_level",
                            "branch_id",
                            "order_amt",
                            "colonel_flag",
                            "colonel_flag_val",
                            "source",
                            "source_val",
                            "sys_code",
                            "item_type",
                            "item_type_val",
                            "supplier_order_no",
                            "supplier_order_dtl_no",
                            "supplier_id",
                            "sku_id",
                            "spu_id",
                            "total_num",
                            "exact_total_amt",
                            "total_amt",
                            "exact_price",
                            "price",
                            "sale_price",
                            "activity_discount_amt",
                            "coupon_discount_amt",
                            "coupon_discount_amt2",
                            "gift_flag",
                            "gift_flag_val",
                            "order_unit",
                            "order_unit_val",
                            "order_unit_type",
                            "order_unit_type_val",
                            "order_unit_qty",
                            "supplier_amt",
                            "profit",
                            "cost_price",
                            "all_dc_rate",
                            "setting_dc_rate",
                            "dc_rate",
                            "dc_amt",
                            "partner_rate",
                            "partner_amt",
                            "setting_colonel1_rate",
                            "colonel1_rate",
                            "colonel1_percentage",
                            "all_colonel1_amt",
                            "colonel1_amt",
                            "setting_colonel2_rate",
                            "colonel2_rate",
                            "colonel2_percentage",
                            "all_colonel2_amt",
                            "colonel2_amt",
                            "order_time",
                            "date_id",
                            "insert_date_id",
                            "pay_state",
                            "pay_way",
                            "category_id"
                        ],
                        "connection": [
                            {
                                "jdbcUrl": "**********************************************************************************************************************************************************",
                                "table": [
                                    "dwd_trd_order_dtl_inc"
                                ]
                            }
                        ]
                    }
                }

            }
        ]
    }
}
