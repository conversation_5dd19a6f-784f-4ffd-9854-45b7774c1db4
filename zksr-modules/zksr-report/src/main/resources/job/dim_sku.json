{
    "job": {
        "setting": {
            "speed": {
                 "channel": 1
            },
            "errorLimit": {
                "record": 0,
                "percentage": 0.02
            }
        },
        "content": [
            {
                "reader": {
                    "name": "mysqlreader",
                    "parameter": {
                        "username": "zksr_query_user",
                        "password": "zksr_query_user9876",
                        "connection": [
                            {
                                "querySql": [
                                    "SELECT
                                      sku.sku_id,
                                      sku.sys_code,
                                      sku.create_by,
                                      sku.create_time,
                                      sku.update_by,
                                      sku.update_time,
                                      sku.spu_id,
                                      spu.spu_name,
                                      spu.spu_no,
                                      spu.supplier_id,
                                      cat1.catgory_id catgory1_id,
                                      cat1.catgory_name catgory1_name,
                                      cat2.catgory_id catgory2_id,
                                      cat2.catgory_name catgory2_name,
                                      cat3.catgory_id catgory3_id,
                                      cat3.catgory_name catgory3_name,
                                      spu.brand_id,
                                      brand.brand_name,
                                      sku.unit,
                                      null AS unit_val,
                                      sku.barcode,
                                      sku.properties,
                                      sku.thumb,
                                      sku.stock,
                                      sku.mark_price,
                                      sku.suggest_price,
                                      sku.cost_price,
                                      sku.`status`,
                                      sku.is_delete,
                                      sku.mid_barcode,
                                      sku.mid_mark_price,
                                      sku.mid_cost_price,
                                      sku.mid_suggest_price,
                                      sku.large_barcode,
                                      sku.large_mark_price,
                                      sku.large_cost_price,
                                      sku.large_suggest_price
                                  FROM
                                      prdt_sku sku
                                      LEFT JOIN prdt_spu spu ON sku.spu_id = spu.spu_id
                                      LEFT JOIN prdt_catgory cat3 ON spu.catgory_id = cat3.catgory_id
                                      LEFT JOIN prdt_catgory cat2 ON cat3.pid = cat2.catgory_id
                                      LEFT JOIN prdt_catgory cat1 ON cat2.pid = cat1.catgory_id
                                      LEFT JOIN prdt_brand brand ON spu.brand_id = brand.brand_id;"
                                ],
                                "jdbcUrl": [
                                  "***********************************************************************************************************************************************************"
                                ]
                            }
                        ]
                    }
                },
                "writer": {
                    "name": "mysqlwriter",
                    "parameter": {
                        "writeMode": "replace",
                        "username": "report_user",
                        "password": "report_user9876",
                        "column": [
                          "sku_id",
                          "sys_code",
                          "create_by",
                          "create_time",
                          "update_by",
                          "update_time",
                          "spu_id",
                          "spu_name",
                          "spu_no",
                          "supplier_id",
                          "catgory1_id",
                          "catgory1_name",
                          "catgory2_id",
                          "catgory2_name",
                          "catgory3_id",
                          "catgory3_name",
                          "brand_id",
                          "brand_name",
                          "unit",
                          "unit_val",
                          "barcode",
                          "properties",
                          "thumb",
                          "stock",
                          "mark_price",
                          "suggest_price",
                          "cost_price",
                          "status",
                          "is_delete",
                          "mid_barcode",
                          "mid_mark_price",
                          "mid_cost_price",
                          "mid_suggest_price",
                          "large_barcode",
                          "large_mark_price",
                          "large_cost_price",
                          "large_suggest_price"
                        ],
                        "connection": [
                            {
                                "jdbcUrl": "**********************************************************************************************************************************************************",
                                "table": [
                                    "dim_sku"
                                ]
                            }
                        ]
                    }
                }

            }
        ]
    }
}
