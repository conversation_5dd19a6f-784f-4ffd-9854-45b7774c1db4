{
    "job": {
        "setting": {
            "speed": {
                 "channel": 1
            },
            "errorLimit": {
                "record": 0,
                "percentage": 0.02
            }
        },
        "content": [
            {
                "reader": {
                    "name": "mysqlreader",
                    "parameter": {
                        "username": "zksr_query_user",
                        "password": "zksr_query_user9876",
                        "connection": [
                            {
                                "querySql": [
                                    "SELECT
										sdd.dict_code,
										sdd.create_by,
										sdd.create_time,
										sdd.update_by,
										sdd.update_time,
										sdd.dict_label,
										sdd.dict_value,
										sdd.dict_type,
										sdt.dict_name AS dict_type_name,
										sdd.dict_sort,
										sdd.`status`
									FROM
										sys_dict_data sdd
										LEFT JOIN sys_dict_type sdt ON sdd.dict_type = sdt.dict_type"
                                ],
                                "jdbcUrl": [
     "*********************************************************************************************************************************************************"
                                ]
                            }
                        ]
                    }
                },
                "writer": {
                    "name": "mysqlwriter",
                    "parameter": {
                        "writeMode": "replace",
                        "username": "report_user",
                        "password": "report_user9876",
                        "column": [
                            "dict_code",
                            "create_by",
                            "create_time",
                            "update_by",
                            "update_time",
                            "dict_label",
                            "dict_value",
                            "dict_type",
							"dict_type_name",
							"dict_sort",
							"status"
                        ],
                        "connection": [
                            {
                                "jdbcUrl": "**********************************************************************************************************************************************************",
                                "table": [
                                    "dim_dict_data"
                                ]
                            }
                        ]
                    }
                }

            }
        ]
    }
}
