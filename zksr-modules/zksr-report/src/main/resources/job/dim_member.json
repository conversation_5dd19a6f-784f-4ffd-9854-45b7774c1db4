{
  "job": {
    "setting": {
      "speed": {
        "channel": 1
      },
      "errorLimit": {
        "record": 0,
        "percentage": 0.02
      }
    },
    "content": [
      {
        "reader": {
          "name": "mysqlreader",
          "parameter": {
            "username": "zksr_query_user",
            "password": "zksr_query_user9876",
            "connection": [
              {
                "querySql": [
                  "SELECT
                    member_id,
                    sys_code,
                    create_by,
                    create_time,
                    update_by,
                    update_time,
                    member_phone,
                    member_name,
                    wx_unionid,
                    avatar,
                    status,
                    CASE
                        WHEN status = 1 THEN '启用'
                        ELSE '停用'
                    END	AS status_val,
                    xcx_openid,
                    register_colonel_id,
                    memo,
                    last_login_time,
                    expiration_date,
                    user_name,
                    is_shop_manager,
                    CASE
                        WHEN is_shop_manager = 1 THEN '是'
                        ELSE '否'
                    END	AS is_shop_manager_val,
                    pid,
                    publish_openid,
                    is_colonel,
                    CASE
                        WHEN is_colonel = 1 THEN '是'
                        ELSE '否'
                    END	AS is_colonel_val,
                    relate_colonel_id
                  FROM
                    mem_member;"
                ],
                "jdbcUrl": [
                  "**********************************************************************************************************************************************************"
                ]
              }
            ]
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "writeMode": "replace",
            "username": "report_user",
            "password": "report_user9876",
            "column": [
              "member_id",
              "sys_code",
              "create_by",
              "create_time",
              "update_by",
              "update_time",
              "member_phone",
              "member_name",
              "wx_unionid",
              "avatar",
              "status",
              "status_val",
              "xcx_openid",
              "register_colonel_id",
              "memo",
              "last_login_time",
              "expiration_date",
              "user_name",
              "is_shop_manager",
              "is_shop_manager_val",
              "pid",
              "publish_openid",
              "is_colonel",
              "is_colonel_val",
              "relate_colonel_id"
            ],
            "connection": [
              {
                "jdbcUrl": "**********************************************************************************************************************************************************",
                "table": [
                  "dim_member"
                ]
              }
            ]
          }
        }

      }
    ]
  }
}
