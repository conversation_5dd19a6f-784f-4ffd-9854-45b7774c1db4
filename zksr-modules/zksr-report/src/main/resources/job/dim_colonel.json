{
  "job": {
    "setting": {
      "speed": {
        "channel": 1
      },
      "errorLimit": {
        "record": 0,
        "percentage": 0.02
      }
    },
    "content": [
      {
        "reader": {
          "name": "mysqlreader",
          "parameter": {
            "username": "zksr_query_user",
            "password": "zksr_query_user9876",
            "connection": [
              {
                "querySql": [
                  "SELECT
                    colonel_id,
                    sys_code,
                    create_by,
                    create_time,
                    update_by,
                    update_time,
                    area_id,
                    colonel_phone,
                    colonel_name,
                    colonel_level,
                    pcolonel_id,
                    sex,
                    CASE
                      WHEN sex = 0 THEN '男'
                      WHEN sex = 1 THEN '女'
                      ELSE '其他'
                    END AS sex_val,
                    birthday,
                    birthplace,
                    entry_date,
                    edu,
                    idcard,
                    percentage_rate,
                    contact_addr,
                    memo,
                    is_colonel_admin,
                    CASE
                      WHEN is_colonel_admin = 'Y' THEN '是'
                      ELSE '否'
                    END AS is_colonel_admin_val,
                    dept_id,
                    app_order_price_adjust,
                    CASE
                      WHEN app_order_price_adjust = 'Y' THEN '是'
                      ELSE '否'
                    END AS app_order_price_adjust_val,
                    app_after_price_adjust,
                    CASE
                      WHEN app_after_price_adjust = 'Y' THEN '是'
                      ELSE '否'
                    END AS app_after_price_adjust_val,
                    order_auto_approve,
                    CASE
                      WHEN order_auto_approve = 'Y' THEN '是'
                      ELSE '否'
                    END AS order_auto_approve_val,
                    del_flag,
                    CASE
                      WHEN del_flag = 0 THEN '正常'
                      ELSE '删除'
                    END AS del_flag_val,
                    user_id,
                    source,
                    audit_state,
                    CASE
                      WHEN audit_state = 0 THEN '待审核'
                      WHEN audit_state = 1 THEN '审核通过'
                      WHEN audit_state = 2 THEN '审核不通过'
                      ELSE '未知'
                    END AS audit_state_val,
                    audit_by,
                    audit_time,
                    audit_memo,
                    develop_people_id,
                    avatar_images,
                    avatar_update_time,
                    three_area_city_id
                  FROM
                    mem_colonel;"
                ],
                "jdbcUrl": [
                  "**********************************************************************************************************************************************************"
                ]
              }
            ]
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "writeMode": "replace",
            "username": "report_user",
            "password": "report_user9876",
            "column": [
              "colonel_id",
              "sys_code",
              "create_by",
              "create_time",
              "update_by",
              "update_time",
              "area_id",
              "colonel_phone",
              "colonel_name",
              "colonel_level",
              "pcolonel_id",
              "sex",
              "sex_val",
              "birthday",
              "birthplace",
              "entry_date",
              "edu",
              "idcard",
              "percentage_rate",
              "contact_addr",
              "memo",
              "is_colonel_admin",
              "is_colonel_admin_val",
              "dept_id",
              "app_order_price_adjust",
              "app_order_price_adjust_val",
              "app_after_price_adjust",
              "app_after_price_adjust_val",
              "order_auto_approve",
              "order_auto_approve_val",
              "del_flag",
              "del_flag_val",
              "user_id",
              "source",
              "audit_state",
              "audit_state_val",
              "audit_by",
              "audit_time",
              "audit_memo",
              "develop_people_id",
              "avatar_images",
              "avatar_update_time",
              "three_area_city_id"
            ],
            "connection": [
              {
                "jdbcUrl": "**********************************************************************************************************************************************************",
                "table": [
                  "dim_colonel"
                ]
              }
            ]
          }
        }

      }
    ]
  }
}
