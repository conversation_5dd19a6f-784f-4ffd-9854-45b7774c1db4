{
  "job": {
    "setting": {
      "speed": {
        "channel": 1
      },
      "errorLimit": {
        "record": 0,
        "percentage": 0.02
      }
    },
    "content": [
      {
        "reader": {
          "name": "mysqlreader",
          "parameter": {
            "username": "zksr_query_user",
            "password": "zksr_query_user9876",
            "connection": [
              {
                "querySql": [
                  "SELECT
                    branch_id,
                    branch_no,
                    sys_code,
                    create_time,
                    update_by,
                    update_time,
                    branch_name,
                    area_id,
                    colonel_id,
                    branch_addr,
                    longitude,
                    latitude,
                    channel_id,
                    group_id,
                    contact_name,
                    contact_phone,
                    memo,
                    status,
                    CASE
                      WHEN status = 1 THEN '启用'
                      ELSE '停用'
                    END AS status_val,
                    audit_state,
                    CASE
                      WHEN audit_state = 1 THEN '已审核'
                      WHEN audit_state = 2 THEN '已作废'
                      ELSE '未审核'
                    END AS audit_state_val,
                    audit_by,
                    audit_time,
                    del_flag,
                    CASE
                      WHEN del_flag = 0 THEN '正常'
                      ELSE '删除'
                    END AS del_flag_val,
                    expiration_date,
                    sale_price_code,
                    CASE
                      WHEN sale_price_code IS NOT NULL THEN  concat('配送价', sale_price_code)
                      ELSE NULL
                    END AS sale_price_code_val,
                    hdfk_support,
                    CASE
                      WHEN status = 1 THEN '是'
                      ELSE '否'
                    END AS hdfk_support_val,
                    branch_images,
                    last_login_time,
                    hdfk_max_amt,
                    three_area_city_id,
                    wechat_merchant_auth_openid
                  FROM
                    mem_branch;"
                ],
                "jdbcUrl": [
                  "**********************************************************************************************************************************************************"
                ]
              }
            ]
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "writeMode": "replace",
            "username": "report_user",
            "password": "report_user9876",
            "column": [
              "branch_id",
              "branch_no",
              "sys_code",
              "create_time",
              "update_by",
              "update_time",
              "branch_name",
              "area_id",
              "colonel_id",
              "branch_addr",
              "longitude",
              "latitude",
              "channel_id",
              "group_id",
              "contact_name",
              "contact_phone",
              "memo",
              "status",
              "status_val",
              "audit_state",
              "audit_state_val",
              "audit_by",
              "audit_time",
              "del_flag",
              "del_flag_val",
              "expiration_date",
              "sale_price_code",
              "sale_price_code_val",
              "hdfk_support",
              "hdfk_support_val",
              "branch_images",
              "last_login_time",
              "hdfk_max_amt",
              "three_area_city_id",
              "wechat_merchant_auth_openid"
            ],
            "connection": [
              {
                "jdbcUrl": "**********************************************************************************************************************************************************",
                "table": [
                  "dim_branch"
                ]
              }
            ]
          }
        }

      }
    ]
  }
}
