{
    "job": {
        "setting": {
            "speed": {
                 "channel": 1
            },
            "errorLimit": {
                "record": 0,
                "percentage": 0.02
            }
        },
        "content": [
            {
                "reader": {
                    "name": "mysqlreader",
                    "parameter": {
                        "username": "zksr_query_user",
                        "password": "zksr_query_user9876",
                        "connection": [
                            {
                                "querySql": [
                                    "
                                    SELECT
                                        mcvl.colonel_visit_log_id
                                        ,mcvl.create_by
                                        ,mcvl.create_time
                                        ,mcvl.update_by
                                        ,mcvl.update_time
                                        ,mcvl.sys_code
                                        ,mb.area_id
                                        ,mcvl.colonel_id
                                        ,mcvl.branch_id
                                        ,mcvl.sign_in_longitude
                                        ,mcvl.sign_in_latitude
                                        ,mcvl.sign_in_address
                                        ,mcvl.sign_in_distance
                                        ,mcvl.sign_in_img_urls
                                        ,mcvl.sign_in_date
                                        ,mcvl.sign_out_longitude
                                        ,mcvl.sign_out_latitude
                                        ,mcvl.sign_out_address
                                        ,mcvl.sign_out_distance
                                        ,mcvl.sign_out_date
                                        ,mcvl.visit_interval_time
                                        ,mcvl.visit_flag
                                        ,CASE
                                            WHEN mcvl.visit_flag = '0' then '签到'
                                            WHEN mcvl.visit_flag = '1' then '签退'
                                            WHEN mcvl.visit_flag = '2' then '作废'
                                            ELSE null
                                        END AS visit_flag_val
                                        ,DATE_FORMAT(mcvl.create_time, '%Y%m%d')
                                        ,DATE_FORMAT(now(), '%Y%m%d')
                                    FROM
                                        mem_colonel_visit_log mcvl
                                        LEFT JOIN mem_branch mb ON mcvl.branch_id = mb.branch_id
                                    WHERE
                                        DATE(mcvl.create_time), = '${dt}'
                                    ;"
                                ],
                                "jdbcUrl": [
                                    "**********************************************************************************************************************************************************"
                                ]
                            }
                        ]
                    }
                },
                "writer": {
                    "name": "mysqlwriter",
                    "parameter": {
                        "writeMode": "replace",
                        "username": "report_user",
                        "password": "report_user9876",
                        "column": [
                            "colonel_visit_log_id",
                            "create_by",
                            "create_time",
                            "update_by",
                            "update_time",
                            "sys_code",
                            "area_id",
                            "colonel_id",
                            "branch_id",
                            "sign_in_longitude",
                            "sign_in_latitude",
                            "sign_in_address",
                            "sign_in_distance",
                            "sign_in_img_urls",
                            "sign_in_date",
                            "sign_out_longitude",
                            "sign_out_latitude",
                            "sign_out_address",
                            "sign_out_distance",
                            "sign_out_date",
                            "visit_interval_time",
                            "visit_flag",
                            "visit_flag_val",
                            "date_id",
                            "insert_date_id"
                        ],
                        "connection": [
                            {
                                "jdbcUrl": "**********************************************************************************************************************************************************",
                                "table": [
                                    "dwd_colonel_visit_inc"
                                ]
                            }
                        ]
                    }
                }

            }
        ]
    }
}
