{
  "job": {
    "setting": {
      "speed": {
        "channel": 1
      },
      "errorLimit": {
        "record": 0,
        "percentage": 0.02
      }
    },
    "content": [
      {
        "reader": {
          "name": "mysqlreader",
          "parameter": {
            "username": "zksr_query_user",
            "password": "zksr_query_user9876",
            "connection": [
              {
                "querySql": [
                  "SELECT
                    sys_code,
                    create_by,
                    create_time,
                    update_by,
                    update_time,
                    partner_user_id,
                    partner_name,
                    contact_name,
                    contact_phone,
                    contact_address,
                    province_code,
                    city_code,
                    area_code,
                    logo_name,
                    logo_pic,
                    area_num,
                    dc_num,
                    supplier_num,
                    partner_code,
                    sid,
                    status,
                    CASE
                      WHEN status = 1 THEN '未审核'
                      WHEN status = 2 THEN '启用'
                      WHEN status = 3 THEN '停用'
                      ELSE '未知'
                    END AS status_val,
                    memo,
                    source
                  FROM
                    sys_partner;"
                ],
                "jdbcUrl": [
                  "jdbc:mysql://**************:3306/zksr-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8"
                ]
              }
            ]
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "writeMode": "replace",
            "username": "report_user",
            "password": "report_user9876",
            "column": [
              "sys_code",
              "create_by",
              "create_time",
              "update_by",
              "update_time",
              "partner_user_id",
              "partner_name",
              "contact_name",
              "contact_phone",
              "contact_address",
              "province_code",
              "city_code",
              "area_code",
              "logo_name",
              "logo_pic",
              "area_num",
              "dc_num",
              "supplier_num",
              "partner_code",
              "sid",
              "status",
              "status_val",
              "memo",
              "source"
            ],
            "connection": [
              {
                "jdbcUrl": "**********************************************************************************************************************************************************",
                "table": [
                  "dim_partner"
                ]
              }
            ]
          }
        }

      }
    ]
  }
}
