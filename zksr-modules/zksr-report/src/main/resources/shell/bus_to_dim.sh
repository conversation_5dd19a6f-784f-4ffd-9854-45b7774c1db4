#!/bin/bash

DATAX_HOME=/opt/soft/datax

#数据同步
import_data() {
    for tab in $@
    do
        datax_config=/opt/soft/datax/job/zksr_mall/import/${tab}.json

        python3 $DATAX_HOME/bin/datax.py $datax_config >$DATAX_HOME/zksr_mall_import.log_$do_date 2>&1

        if [[ $? -eq 1 ]]
        then
            echo "${tab}数据导入出错，日志如下: "
            cat $DATAX_HOME/zksr_mall_import_$do_date.log
        else
            echo "${tab}导入成功~"
        fi
    done
}

case ${table_scope} in
dim_dict_data | dim_sku | dim_member | dim_area | dim_branch | dim_colonel | dim_dc | dim_partner | dim_supplier | dim_catgory)
    import_data ${table_scope}
    ;;
all)
    import_data dim_dict_data dim_sku dim_member dim_area dim_branch dim_colonel dim_dc dim_partner dim_supplier dim_catgory
    ;;
esac

## 自定义参数 table_scope:in:varchar:all
