#!/bin/bash

DATAX_HOME=/opt/soft/datax

# 如果传入日期则do_date等于传入的日期，否则等于前一天日期
if [ -n "${dt}" ] ;then
    do_date=${dt}
else
    do_date=`date -d "-1 day" +%Y%m%d`
fi

#数据同步
import_data() {
    for tab in $@
    do
        # 使用 if 语句判断 do_date 是否等于 "init"
        if [ "$do_date" == "init" ]; then
            echo "do_date 等于 init"
            # 在这里添加你想要执行的命令
            datax_config=/opt/soft/datax/job/zksr_mall/import/${tab}_init.json
            python3 $DATAX_HOME/bin/datax.py $datax_config >$DATAX_HOME/zksr_mall_import_$do_date.log 2>&1
        else
            echo "do_date 不等于 init"
            # 在这里添加你想要在 do_date 不等于 init 时执行的命令
            datax_config=/opt/soft/datax/job/zksr_mall/import/${tab}.json
            python3 $DATAX_HOME/bin/datax.py -p"-Ddt=$do_date" $datax_config >$DATAX_HOME/zksr_mall_import_$do_date.log 2>&1
        fi

        if [[ $? -eq 1 ]]
        then
            echo "${tab}数据导入出错，日志如下: "
            cat $DATAX_HOME/zksr_mall_import_$do_date.log
        else
            echo "${tab}导入成功~$do_date"
        fi

    done
}

case ${table_scope} in
dwd_trd_order_dtl_inc | dwd_trd_cancel_inc | dwd_trd_return_inc | dwd_colonel_visit_inc | dwd_login_his_inc)
    import_data ${table_scope}
    ;;
all)
    import_data dwd_trd_order_dtl_inc dwd_trd_cancel_inc dwd_trd_return_inc dwd_colonel_visit_inc dwd_login_his_inc
    ;;
esac


## 自定义参数 table_scope:in:varchar:all
## 全局参数 dt=20240922
