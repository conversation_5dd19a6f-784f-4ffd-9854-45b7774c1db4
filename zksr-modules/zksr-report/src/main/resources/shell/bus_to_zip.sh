#!/bin/bash

DATAX_HOME=/opt/soft/datax

# 如果传入日期则do_date等于传入的日期，否则等于前一天日期
if [[ -n "${dt}" && "${dt}" -ne "init" ]] ;then
    do_date=${dt}
else
    do_date=`date -d "-1 day" +%Y-%m-%d`
fi

#数据同步
import_data() {
    for tab in $@
    do
        # 在这里添加你想要在 do_date 不等于 init 时执行的命令
        echo "日期：${do_date}定时执行获取业务库 ${tab} 拉链表数据: "
        datax_config=/opt/soft/datax/job/zksr_mall/import/${tab}.json
        python3 $DATAX_HOME/bin/datax.py -p"-Ddt=$do_date" $datax_config >$DATAX_HOME/zksr_mall_import_$do_date.log 2>&1

        if [[ $? -eq 1 ]]
        then
            echo "${tab}数据导入出错，日志如下: "
            cat $DATAX_HOME/zksr_mall_import_$do_date.log
        else
            echo "${tab}导入成功~$do_date"
        fi
    done
}

case ${table_scope} in
mem_colonel_branch_zip | mem_colonel_hierarchy_zip | prdt_area_item_zip | prdt_supplier_item_zip | sys_area_supplier_zip | sys_dc_area_zip )
    import_data ${table_scope}
    ;;
all)
    import_data mem_colonel_branch_zip mem_colonel_hierarchy_zip prdt_area_item_zip prdt_supplier_item_zip sys_area_supplier_zip sys_dc_area_zip
    ;;
esac


## 自定义参数 table_scope:in:varchar:all
## 全局参数 dt=20240922
