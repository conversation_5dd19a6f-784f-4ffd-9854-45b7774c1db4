<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.report.mapper.ColonelRptMapper">

    <select id="countByColonel" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM
            dim_branch dbr
        <where>
            <if test="colonelIdList != null and colonelIdList.size > 0">
                AND dbr.colonel_id IN
                <foreach item="colonelId" collection="colonelIdList" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="sumMonthSaleAmtByColonel" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(dtbsm.order_amt - dtbsm.return_amt), 0)
        FROM
            dim_branch dbr
            INNER JOIN dws_trd_branch_sales_month dtbsm ON dbr.branch_id = dtbsm.branch_id
        WHERE
            dtbsm.month_id = #{monthId}
            <if test="colonelIdList != null and colonelIdList.size > 0">
                AND dbr.colonel_id IN
                <foreach item="colonelId" collection="colonelIdList" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
    </select>
    <select id="countMonthActiveByColonel" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM
            dim_branch dbr
            INNER JOIN ads_branch_tag_month abtm ON dbr.branch_id = abtm.branch_id
        WHERE
            abtm.month_id = #{monthId}
            AND abtm.active_tag_def_id IS NOT NULL
            <if test="colonelIdList != null and colonelIdList.size > 0">
                AND dbr.colonel_id IN
                <foreach item="colonelId" collection="colonelIdList" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
    </select>
    <select id="sumMonthColonelProfitByColonel" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(dtbsm.profit_colonel_amt - dtbsm.return_profit_colonel_amt), 0)
        FROM
            dim_branch dbr
            INNER JOIN dws_trd_branch_sales_month dtbsm ON dbr.branch_id = dtbsm.branch_id
        WHERE
            dtbsm.month_id = #{monthId}
            <if test="colonelIdList != null and colonelIdList.size > 0">
                AND dbr.colonel_id IN
                <foreach item="colonelId" collection="colonelIdList" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
    </select>
    <select id="selectMonthBranchTagByColonel" resultType="com.zksr.report.domain.vo.BranchTagTotalVO">
        SELECT
            abtm.level_tag_type AS branchTag,
            SUM(REPLACE ( abtm.`level_tag_val`, '元', '' )) AS saleAmt ,
            COUNT(1) cnt
        FROM
            dim_branch dbr
            INNER JOIN ads_branch_tag_month abtm ON dbr.branch_id = abtm.branch_id
        WHERE
            abtm.month_id = #{monthId}
            AND abtm.level_tag_def_id IS NOT NULL
            <if test="colonelIdList != null and colonelIdList.size > 0">
                AND dbr.colonel_id IN
                <foreach item="colonelId" collection="colonelIdList" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
        GROUP BY
            abtm.level_tag_type
    </select>
    <select id="selectSumSaleWeek" resultType="com.zksr.report.domain.AdsBranchTradeWeekdayDistribution">
        SELECT
            SUM(abtwd.monday_dx_branch_qty) AS monday_dx_branch_qty,
            SUM(abtwd.monday_order_qty) AS monday_order_qty,
            SUM(abtwd.monday_order_amt) AS monday_order_amt,
            SUM(abtwd.tuesday_dx_branch_qty) AS tuesday_dx_branch_qty,
            SUM(abtwd.tuesday_order_qty) AS tuesday_order_qty,
            SUM(abtwd.tuesday_order_amt) AS tuesday_order_amt,
            SUM(abtwd.wednesday_dx_branch_qty) AS wednesday_dx_branch_qty,
            SUM(abtwd.wednesday_order_qty) AS wednesday_order_qty,
            SUM(abtwd.wednesday_order_amt) AS wednesday_order_amt,
            SUM(abtwd.thursday_dx_branch_qty) AS thursday_dx_branch_qty,
            SUM(abtwd.thursday_order_qty) AS thursday_order_qty,
            SUM(abtwd.thursday_order_amt) AS thursday_order_amt,
            SUM(abtwd.friday_dx_branch_qty) AS friday_dx_branch_qty,
            SUM(abtwd.friday_order_qty) AS friday_order_qty,
            SUM(abtwd.friday_order_amt) AS friday_order_amt,
            SUM(abtwd.saturday_dx_branch_qty) AS saturday_dx_branch_qty,
            SUM(abtwd.saturday_order_qty) AS saturday_order_qty,
            SUM(abtwd.saturday_order_amt) AS saturday_order_amt,
            SUM(abtwd.sunday_dx_branch_qty) AS sunday_dx_branch_qty,
            SUM(abtwd.sunday_order_qty) AS sunday_order_qty,
            SUM(abtwd.sunday_order_amt) AS sunday_order_amt
        FROM
            dim_branch dbr
            INNER JOIN ads_branch_trade_weekday_distribution abtwd ON dbr.branch_id = abtwd.branch_id
        WHERE
            abtwd.month_id = #{monthId}
            <if test="colonelIdList != null and colonelIdList.size > 0">
                AND dbr.colonel_id IN
                <foreach item="colonelId" collection="colonelIdList" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
        GROUP BY
            abtwd.sys_code
    </select>
    <select id="selectSaleCategoryList"
            resultType="com.zksr.report.domain.AdsBranchCat1OrderamtStatsMonth">
        SELECT
            abc1osm.cat1_id,
            SUM(abc1osm.branch_order_amt) AS branch_order_amt
        FROM
            dim_branch dbr
            INNER JOIN ads_branch_cat1_orderamt_stats_month abc1osm ON dbr.branch_id = abc1osm.branch_id
        WHERE
            abc1osm.month_id = #{monthId}
            <if test="colonelIdList != null and colonelIdList.size > 0">
                AND dbr.colonel_id IN
                <foreach item="colonelId" collection="colonelIdList" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
        GROUP BY
            abc1osm.cat1_id
    </select>
    <select id="selectBranchSaleCategoryList"
            resultType="com.zksr.report.domain.AdsBranchCat1OrderamtStatsMonth">
        SELECT
            abc1osm.cat1_id,
            SUM(abc1osm.branch_order_amt) AS branch_order_amt
        FROM
            dim_branch dbr
            INNER JOIN ads_branch_cat1_orderamt_stats_month abc1osm ON dbr.branch_id = abc1osm.branch_id
        WHERE
            abc1osm.month_id = #{monthId}
            AND abc1osm.branch_id = #{branchId}
        GROUP BY
            abc1osm.cat1_id
    </select>
    <select id="selectMonthBranchTagByColonelAndLevelTag"
            resultType="com.zksr.report.domain.vo.BranchTagTotalVO">
        SELECT
            dbr.colonel_id,
            COUNT(1) branchCnt
            <if test="levelTagType != null and levelTagType.contains('level')">
                , SUM(CASE WHEN abtm.level_tag_type = #{levelTagType} THEN 1 ELSE 0 END ) cnt
            </if>
            <if test="levelTagType != null and levelTagType.contains('active')">
                , SUM(CASE WHEN abtm.active_tag_type = #{levelTagType} THEN 1 ELSE 0 END ) cnt
            </if>
        FROM
            dim_colonel cl
            LEFT JOIN dim_branch dbr ON cl.colonel_id = dbr.colonel_id
            LEFT JOIN ads_branch_tag_month abtm ON abtm.branch_id = dbr.branch_id AND abtm.month_id = #{monthId}
        WHERE
            1 = 1
            <if test="colonelIdList != null and colonelIdList.size > 0">
                AND dbr.colonel_id IN
                <foreach item="colonelId" collection="colonelIdList" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
        GROUP BY
            dbr.colonel_id
        ORDER BY
            cnt DESC
    </select>
    <select id="countMonthBranchTagByColonelAndLevelTag" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM
            dim_colonel cl
            INNER JOIN dim_branch dbr ON cl.colonel_id = dbr.colonel_id
            INNER JOIN ads_branch_tag_month abtm ON abtm.branch_id = dbr.branch_id
        WHERE
            abtm.month_id = #{monthId}
            <if test="levelTagType != null and levelTagType.contains('level')">
                AND abtm.level_tag_type = #{levelTagType}
            </if>
            <if test="levelTagType != null and levelTagType.contains('active')">
                AND abtm.active_tag_type = #{levelTagType}
            </if>
            <if test="colonelIdList != null and colonelIdList.size > 0">
                AND dbr.colonel_id IN
                <foreach item="colonelId" collection="colonelIdList" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
    </select>
    <select id="selectTagBranchList" resultType="com.zksr.report.api.branch.vo.ColonelTagBranchRespVO">
        SELECT
            dbr.branch_id,
            dbr.branch_name,
            (SELECT order_time FROM dwd_trd_order_dtl_inc WHERE dbr.branch_id = branch_id ORDER BY order_id LIMIT 1) beforeOrderTime,
            IFNULL(dtbsm.order_amt - dtbsm.return_amt, 0) AS saleAmt,
            abtm.level_tag_type,
            abtm.active_tag_type,
            abtm.freq_level_tag_type,
            abtm.lz_level_tag_type,
            abtm.profit_level_tag_type
        FROM
            dim_branch dbr
            LEFT JOIN ads_branch_tag_month abtm ON abtm.branch_id = dbr.branch_id
            LEFT JOIN dws_trd_branch_sales_month dtbsm ON dtbsm.branch_id = dbr.branch_id AND dtbsm.month_id = abtm.month_id
        WHERE
            dbr.colonel_id = #{colonelId}
            AND abtm.month_id = #{monthId}
            <if test="branchTag.tag != null and branchTag.tag.contains('level')">
                AND abtm.level_tag_type = #{branchTag.tag}
            </if>
            <if test="branchTag.tag != null and branchTag.tag.contains('active')">
                AND abtm.active_tag_type = #{branchTag.tag}
            </if>
            <if test="saleAmtStart != null">
                AND dtbsm.order_amt - dtbsm.return_amt &gt;= #{saleAmtStart}
            </if>
            <if test="saleAmtEnd != null">
                AND dtbsm.order_amt - dtbsm.return_amt &lt;= #{saleAmtEnd}
            </if>
            <if test='branchName != null and branchName != ""'>
                AND dbr.branch_name LIKE CONCAT('%', #{branchName}, '%')
            </if>
            <if test="sortType != null and sortType == 1">
                ORDER BY
                    saleAmt DESC
            </if>
            <if test="sortType != null and sortType == 2">
                ORDER BY
                    saleAmt ASC
            </if>
    </select>
    <select id="selectBranchSaleByYear" resultType="com.zksr.report.api.branch.vo.BranchTargetProcessVO">
        SELECT
            CONCAT(dm.year, '-', (CASE WHEN dm.`month` &lt; 10 THEN CONCAT('0', dm.`month`) ELSE dm.`month` END)) AS monthDate,
            IFNULL(SUM(dtbsm.order_amt - dtbsm.return_amt), 0) AS saleAmt,
            IFNULL(SUM(dtbsm.discount_amt - dtbsm.return_discount_amt), 0) AS couponAmt
        FROM
            dim_month dm
            INNER JOIN dws_trd_branch_sales_month dtbsm ON dtbsm.month_id = dm.month_id
        WHERE
            dm.year = #{year}
            AND dtbsm.branch_id = #{branchId}
        GROUP BY
            dm.`month`
        ORDER BY
            monthDate DESC
    </select>
    <select id="selectColonelBranchHoursSaleList" resultType="com.zksr.report.api.branch.vo.HoursSaleVO">
        SELECT
            dtbhsm.hour,
            COUNT(DISTINCT dtbhsm.branch_id) branchNum,
            SUM(order_qty - return_qty) orderQty
        FROM
            dim_branch dbr
            INNER JOIN dws_trd_branch_hours_sales_month dtbhsm ON dtbhsm.branch_id = dbr.branch_id
        WHERE
            dtbhsm.month_id = #{monthId}
            <if test="colonelIdList != null and colonelIdList.size > 0">
                AND dbr.colonel_id IN
                <foreach item="colonelId" collection="colonelIdList" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
        GROUP BY
            dtbhsm.hour
    </select>
    <select id="selectBranchHoursSaleList" resultType="com.zksr.report.api.branch.vo.HoursSaleVO">
        SELECT
            dtbhsm.hour,
            COUNT(DISTINCT dtbhsm.branch_id) branchNum,
            SUM(order_qty - return_qty) orderQty
        FROM
            dws_trd_branch_hours_sales_month dtbhsm
        WHERE
            dtbhsm.month_id = #{monthId}
            AND dtbhsm.branch_id = #{branchId}
        GROUP BY
            dtbhsm.hour
    </select>
</mapper>
