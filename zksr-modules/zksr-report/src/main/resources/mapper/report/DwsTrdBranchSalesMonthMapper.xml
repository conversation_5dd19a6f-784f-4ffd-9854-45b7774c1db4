<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.report.mapper.DwsTrdBranchSalesMonthMapper">

    <select id="selectTotalColonelBranchSale" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(dtbsm.order_amt - dtbsm.return_amt), 0)
        FROM
            dim_branch dbr
            LEFT JOIN dws_trd_branch_sales_month dtbsm ON dbr.branch_id = dtbsm.branch_id
        WHERE
            dtbsm.month_id = #{reqVO.monthId}
            <if test="reqVO.colonelIdList != null and reqVO.colonelIdList.size > 0">
                AND dbr.colonel_id IN
                <foreach item="colonelId" collection="reqVO.colonelIdList" open="(" separator="," close=")">
                    #{colonelId}
                </foreach>
            </if>
    </select>
</mapper>
