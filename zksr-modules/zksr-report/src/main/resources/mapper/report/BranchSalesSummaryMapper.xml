<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.report.mapper.BranchSalesSummaryMapper">
    <select id="getMonthlyBranchSalesSummary" resultType="com.zksr.report.controller.branchSalesSummary.dto.BranchSalesSummaryDTO">
        SELECT
            t1.branch_id AS branchId,                                -- 门店编号
            dim.branch_name AS branchName,                          -- 门店名称
            SUM(t1.order_amt) AS totalSalesAmount,                   -- 销售总金额
            SUM(t1.order_qty) AS totalSalesOrderCount,               -- 销售订单数
            SUM(t1.dx_sku_qty) AS activeSkuCount,                    -- 动销SKU数
            SUM(t1.return_amt) AS totalReturnAmount,                 -- 退单金额
            SUM(t1.return_qty) AS totalReturnOrderCount,             -- 退单数量
            SUM(t1.return_sku_qty) AS returnSkuCount,                -- 退单SKU数
            SUM(t1.supplier_cost_amt) AS totalSalesCost,             -- 总销售成本金额
            SUM(t1.return_supplier_cost_amt) AS totalReturnCost,     -- 总退货成本金额
            SUM(t1.order_amt) - SUM(t1.supplier_cost_amt) AS grossProfit, -- 毛利金额
            CASE
            WHEN SUM(t1.order_amt) > 0 THEN
            ROUND((SUM(t1.order_amt) - SUM(t1.supplier_cost_amt)) / SUM(t1.order_amt) * 100, 2)
            ELSE
            0
            END AS grossProfitRate,                                  -- 毛利率(%)
            SUM(t1.discount_amt) AS totalDiscountAmount,             -- 优惠金额
            -- 上月数据
            COALESCE(prev_month_data.totalSalesAmount, 0) AS lastMonthSalesAmount, -- 上月销售金额
            CASE
            WHEN COALESCE(prev_month_data.totalSalesAmount, 0) > 0 THEN
            ROUND(SUM(t1.order_amt) / COALESCE(prev_month_data.totalSalesAmount, 1), 2)
            ELSE
            0
            END AS salesGrowthRate,                                        -- 销售金额成长比(%)
            COALESCE(prev_month_data.activeSkuCount, 0) AS lastMonthActiveSkuCount, -- 上月动销SKU数
            CASE
            WHEN COALESCE(prev_month_data.activeSkuCount, 0) > 0 THEN
            ROUND(SUM(t1.dx_sku_qty) / COALESCE(prev_month_data.activeSkuCount, 0), 2)
            ELSE
            0
            END AS activeSkuGrowthRate                               -- 动销SKU成长比(%)
        FROM
            dws_trd_branch_sales_month t1
            LEFT JOIN (
                -- 子查询：获取上月的数据
                SELECT
                    branch_id,
                    SUM(order_amt) AS totalSalesAmount,
                    SUM(dx_sku_qty) AS activeSkuCount
                FROM
                    dws_trd_branch_sales_month
                WHERE
                    month_id = #{previousMonthId} -- 动态替换为上月日期
                GROUP BY
                    branch_id
            ) prev_month_data
            ON t1.branch_id = prev_month_data.branch_id
            LEFT JOIN dim_branch dim
            ON t1.branch_id = dim.branch_id  -- 关联门店维度表
        WHERE
            t1.month_id = #{currentMonthId} -- 动态替换为查询的月份
            <if test="branchId != null and branchId != ''">
                AND dim.branch_id = #{branchId}
            </if>
            <if test="branchName != null and branchName != ''">
                AND dim.branch_name LIKE CONCAT('%', #{branchName}, '%') -- 增加门店名称模糊查询
            </if>
            <if test="sysCode != null">
                AND t1.sys_code = #{sysCode}
            </if>
            ${params.dataScope}
        GROUP BY
            t1.branch_id, dim.branch_name -- 增加对门店名称的分组
        ORDER BY
            totalSalesAmount DESC
            LIMIT #{pageNo}, #{pageSize};
    </select>

    <select id="countMonthlyBranchSalesSummary" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT t1.branch_id) -- 统计不同的 branch_id 数量
        FROM
            dws_trd_branch_sales_month t1
            LEFT JOIN (
                -- 子查询：获取上月的数据
                SELECT
                    branch_id
                FROM
                    dws_trd_branch_sales_month
                WHERE
                    month_id = #{previousMonthId} -- 上月月份
                GROUP BY
                    branch_id
            ) prev_month_data
            ON t1.branch_id = prev_month_data.branch_id
            LEFT JOIN dim_branch dim
            ON t1.branch_id = dim.branch_id  -- 关联门店维度表
        WHERE
            t1.month_id = #{currentMonthId} -- 当前月份
            <if test="branchId != null and branchId != ''">
                AND t1.branch_id = #{branchId}
            </if>
            <if test="branchName != null and branchName != ''">
                AND dim.branch_name LIKE CONCAT('%', #{branchName}, '%') -- 门店名称模糊查询
            </if>
    </select>
</mapper>
