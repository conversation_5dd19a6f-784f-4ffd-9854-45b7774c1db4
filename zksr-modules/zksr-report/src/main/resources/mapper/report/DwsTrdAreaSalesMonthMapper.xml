<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.report.mapper.DwsTrdAreaSalesMonthMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getAreaMonthSalesPage"
            resultType="com.zksr.report.controller.dwsAreaSales.dto.DwsTrdAreaSalesMonthRespDTO">
        SELECT
            dtasm.month_id
            , dtasm.sys_code
            , da2.dc_id
            , da1.area_name AS oneAreaName
            , da2.area_name AS twoAreaName
            , dtasm.pre_order_amt
            , dtasm.order_amt
            , dtasm.order_qty
            , 0 AS areaBranchTotalQty
            , dtasm.dx_branch_qty
            , dtasm.dx_sku_qty
            , dtasm.return_amt
            , dtasm.return_qty
            , dtasm.return_sku_qty
            , dd.dc_name
            , dtasm.supplier_cost_amt
            , dtasm.return_supplier_cost_amt
            , ROUND(dtasm.profit_rate, 2) AS profit_rate
            , dtasm.discount_amt
        FROM
            dws_trd_area_sales_month dtasm
            LEFT JOIN dim_area da2 ON dtasm.area_id = da2.area_id
            LEFT JOIN dim_area da1 ON da2.pid = da1.area_id
            LEFT JOIN dim_dc dd ON da2.dc_id = dd.dc_id
        WHERE
            dtasm.month_id = REPLACE(#{searchMonth}, '-', '')
            <if test="null != areaId">
                AND dtasm.area_id = #{areaId}
            </if>
            <if test="null != areaName and areaName != '' ">
                AND da2.area_name LIKE CONCAT('%', #{areaName}, '%')
            </if>
    </select>
</mapper>