<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.report.mapper.DwsTrdSupplierSalesDayMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getSupplierSummaryPage"
            resultType="com.zksr.report.controller.dwsSupplierSales.dto.DwsTrdSupplierSalesDayRespDTO">
        SELECT
            dtssd.supplier_id
            , ds.supplier_name
            , dtssd.area_id
            , da.area_name
            , dtssd.dx_sku_qty
            , dtssd.dx_branch_qty
            , dtssd.pre_order_amt
            , dtssd.order_amt
            , dtssd.order_qty
            , dtssd.supplier_cost_amt
            , dtssd.discount_amt
            , IFNULL(((dtssd.order_amt - dtssd.supplier_cost_amt) / dtssd.order_amt) * 100, 0) AS grossProfit
            , dtssd.return_amt
            , dtssd.return_qty
            , dtssd.return_sku_qty
            , dtssd.return_supplier_cost_amt
        FROM
            dws_trd_supplier_sales_day dtssd
            LEFT JOIN dim_supplier ds ON dtssd.supplier_id = ds.supplier_id
            LEFT JOIN dim_area da ON dtssd.area_id = da.area_id
        WHERE
            dtssd.date_id BETWEEN DATE_FORMAT(#{startDate}, '%Y%m%d') AND DATE_FORMAT(#{endDate}, '%Y%m%d')
            <if test="null != areaId">
                AND dtssd.area_id = #{areaId}
            </if>
            <if test="null != supplierId">
                AND dtssd.supplier_id = #{supplierId}
            </if>
            <if test="null != supplierName">
                AND ds.supplier_name LIKE CONCAT('%', #{supplierName}, '%')
            </if>
    </select>
</mapper>