<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.report.mapper.RptTagDefMapper">

    <select id="selectByMonthSale" resultType="com.zksr.report.domain.vo.BranchSaleVO">
        SELECT
            branch_id,
            SUM(profit_amt - return_profit_amt) profit,
            SUM(order_amt - return_amt) AS saleAmt
        FROM
            dws_trd_branch_sales_month
        WHERE
            month_id = #{monthId}
            AND branch_id IN
            <foreach item="branchId" collection="branchIds" open="(" separator="," close=")">
                #{branchId}
            </foreach>
        GROUP BY
            branch_id
    </select>
    <select id="selectByMonthClassSale" resultType="com.zksr.report.domain.vo.BranchSaleVO">
        SELECT
            branch_id,
            order_amt - return_amt AS saleAmt
        FROM
            dws_trd_branch_category_sales_month
        WHERE
            month_id = #{monthId}
            AND branch_id IN
            <foreach item="branchId" collection="branchIds" open="(" separator="," close=")">
                #{branchId}
            </foreach>
            AND catgory3_id IN
            <foreach item="categoryId" collection="categoryList" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
    </select>
    <select id="selectByMonthFreqSaleDay" resultType="com.zksr.report.domain.vo.BranchFreqVO">
        SELECT
            branch_id,
            COUNT(1) saleDayCnt
        FROM
            dws_trd_branch_sales_day
        WHERE
            date_id BETWEEN #{startDateId} AND #{endDateId}
            AND order_amt > 0
            AND branch_id IN
            <foreach item="branchId" collection="branchIds" open="(" separator="," close=")">
                #{branchId}
            </foreach>
        GROUP BY
            branch_id
    </select>
</mapper>
