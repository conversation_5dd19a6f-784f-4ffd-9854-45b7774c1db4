<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zksr.report.mapper.ColonelSalesSummaryMapper">

    <select id="getMonthlyColonelSalesSummary" resultType="com.zksr.report.controller.colonelSalesSummary.dto.ColonelSalesSummaryDTO">
        SELECT
            t1.colonel_id AS colonelId,                                -- 业务员编号
            dim.colonel_name AS colonelName,                          -- 业务员名称
            SUM(t1.order_amt) AS totalSalesAmount,                   -- 销售金额
            SUM(t1.order_qty) AS totalSalesOrderCount,               -- 销售数量
            SUM(t1.dx_branch_qty) AS activeBranchCount,              -- 动销门店数
            SUM(t1.dx_sku_qty) AS activeSkuCount,                    -- 动销SKU数
            SUM(t1.colonel_order_amt) AS agentOrderAmount,           -- 代客下单金额
            SUM(t1.colonel_order_qty) AS agentOrderSkuCount,         -- 代客下单SKU数量
            SUM(t1.branch_order_amt) AS selfOrderAmount,             -- 自主下单金额
            SUM(t1.branch_order_qty) AS selfOrderSkuCount,           -- 自主下单SKU数量
            SUM(t1.return_amt) AS totalReturnAmount,                 -- 退单金额
            SUM(t1.return_qty) AS totalReturnOrderCount,             -- 退单数量
            SUM(t1.supplier_cost_amt) AS totalSalesCost,             -- 总销售成本
            SUM(t1.return_supplier_cost_amt) AS totalReturnCost,     -- 总退货成本
            SUM(t1.order_amt) -  SUM(t1.supplier_cost_amt) AS grossProfitAmount,  -- 毛利金额
            CASE
            WHEN SUM(t1.order_amt) > 0 THEN
            ROUND((SUM(t1.order_amt) - SUM(t1.supplier_cost_amt)) / SUM(t1.order_amt) * 100, 2)
            ELSE
            0
            END AS grossProfitRate,                                  -- 毛利率(%)
            SUM(t1.discount_amt) AS totalDiscountAmount,             -- 优惠金额
            COALESCE(prev_month_data.activeBranchCount, 0) AS lastMonthActiveBranchCount, -- 上月动销门店
            CASE
            WHEN COALESCE(prev_month_data.activeBranchCount, 0) > 0 THEN
            ROUND(SUM(t1.dx_branch_qty) / COALESCE(prev_month_data.activeBranchCount, 1), 2)
            ELSE
            0
            END AS activeBranchGrowthRate,                           -- 动销门店成长比
            COALESCE(prev_month_data.totalSalesAmount, 0) AS lastMonthSalesAmount, -- 上月销售总金额
            CASE
            WHEN COALESCE(prev_month_data.totalSalesAmount, 0) > 0 THEN
            ROUND(SUM(t1.order_amt) / COALESCE(prev_month_data.totalSalesAmount, 1), 2)
            ELSE
            0
            END AS salesGrowthRate,                                    -- 销售金额成长比
            COALESCE(prev_month_data.activeSkuCount, 0) AS lastMonthActiveSkuCount, -- 上月动销SKU数
            CASE
            WHEN COALESCE(prev_month_data.activeSkuCount, 0) > 0 THEN
            ROUND(SUM(t1.dx_sku_qty) / COALESCE(prev_month_data.activeSkuCount, 0), 2)
            ELSE
            0
            END AS activeSkuGrowthRate                               -- 动销SKU成长比
        FROM
            dws_trd_colonel_sales_month t1
        LEFT JOIN (
            -- 子查询：获取上月的数据
            SELECT
                colonel_id,
                SUM(dx_branch_qty) AS activeBranchCount,
                SUM(dx_sku_qty) AS activeSkuCount,
                SUM(order_amt) AS totalSalesAmount
            FROM
                dws_trd_colonel_sales_month
            WHERE
                month_id = #{previousMonthId} -- 动态替换为上月日期
            GROUP BY
                colonel_id
            ) prev_month_data
        ON t1.colonel_id = prev_month_data.colonel_id
            LEFT JOIN dim_colonel dim
        ON t1.colonel_id = dim.colonel_id  -- 关联业务员维度表
        WHERE
            t1.month_id = #{currentMonthId} -- 动态替换为查询的月份
            <if test="colonelId != null and colonelId != ''">
                AND t1.colonel_id = #{colonelId}
            </if>
            <if test="colonelName != null and colonelName != ''">
                AND dim.colonel_name LIKE CONCAT('%', #{colonelName}, '%') -- 增加业务员名称模糊查询
            </if>
            <if test="sysCode != null">
                AND t1.sys_code = #{sysCode}
            </if>
            ${params.dataScope}
        GROUP BY
            t1.colonel_id, dim.colonel_name -- 增加对业务员名称的分组
        ORDER BY
            totalSalesAmount DESC
            LIMIT #{pageNo}, #{pageSize};
    </select>
    <select id="getBranchCountFromMemTable"
            resultType="com.zksr.member.api.branch.dto.ColonelBranchCountDTO">
        SELECT
            colonel_id AS colonelId,
            COUNT(DISTINCT branch_id) AS branchCount
        FROM
            mem_colonel_branch_zip
        WHERE
            colonel_id IN
            <foreach collection="colonelIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND DATE_FORMAT(start_date, '%Y%m') = #{currentMonthId}
        GROUP BY
            colonel_id
    </select>
    <select id="countMonthlyColonelSalesSummary" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT t1.colonel_id)
        FROM
            dws_trd_colonel_sales_month t1
            LEFT JOIN (
                -- 子查询：获取上月的数据
                SELECT
                    colonel_id,
                    SUM(dx_branch_qty) AS activeBranchCount,
                    SUM(dx_sku_qty) AS activeSkuCount,
                    SUM(order_amt) AS totalSalesAmount
                FROM
                    dws_trd_colonel_sales_month
                WHERE
                    month_id = #{previousMonthId} -- 动态替换为上月日期
                GROUP BY
                    colonel_id
                ) prev_month_data
            ON t1.colonel_id = prev_month_data.colonel_id
            LEFT JOIN dim_colonel dim
            ON t1.colonel_id = dim.colonel_id  -- 关联业务员维度表
        WHERE
            t1.month_id = #{currentMonthId} -- 动态替换为查询的月份
            <if test="colonelId != null and colonelId != ''">
                AND t1.colonel_id = #{colonelId}
            </if>
            <if test="colonelName != null and colonelName != ''">
                AND dim.colonel_name LIKE CONCAT('%', #{colonelName}, '%') -- 增加业务员名称模糊查询
            </if>
            ${params.dataScope}
    </select>
</mapper>
