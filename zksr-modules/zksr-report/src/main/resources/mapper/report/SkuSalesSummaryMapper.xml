<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zksr.report.mapper.SkuSalesSummaryMapper">
    <select id="getMonthlySkuSalesSummary"
            resultType="com.zksr.report.controller.skuSalesSummary.dto.SkuSalesSummaryDTO">
        SELECT
            ds.sku_id AS skuId,                                 -- 商品SKUID
            ds.spu_name AS skuName,                             -- 商品名称
            ds.spu_no AS skuNo,                                 -- 商品编号
            ds.barcode AS barCode,                              -- 产品条码
            ds.properties AS properties,                       -- 属性
            ds.unit AS unit,                                -- 单位
            ds.catgory1_name AS category1,                      -- 一级类别
            ds.catgory2_name AS category2,                      -- 二级类别
            ds.catgory3_name AS category3,                      -- 三级类别
            ds.brand_name AS brand,                             -- 品牌
            ds.supplier_id AS supplierId,                      -- 入驻商编号
            dsup.supplier_name AS supplierName,                 -- 入驻商名称
            SUM(ts.sku_sales_qty) AS salesQuantity,             -- 销售数量
            SUM(ts.order_amt) AS salesAmount,                   -- 销售金额
            SUM(ts.return_amt) AS refundAmount,                 -- 退单金额
            SUM(ts.sku_return_qty) AS refundQuantity,           -- 退单数量
            SUM(ts.supplier_cost_amt) AS totalCost,             -- 总销售成本金额
            SUM(ts.return_supplier_cost_amt) AS totalRefundCost,-- 总退货成本金额
            (SUM(ts.order_amt) - SUM(ts.supplier_cost_amt)) AS grossProfit, -- 毛利金额
            CASE
            WHEN SUM(ts.order_amt) > 0 THEN
            ROUND((SUM(ts.order_amt) - SUM(ts.supplier_cost_amt)) / SUM(ts.order_amt) * 100, 2)
            ELSE
            0
            END AS grossProfitRate,                                  -- 毛利率(%)
            SUM(ts.discount_amt) AS discountAmount,             -- 优惠金额
            SUM(ts.dx_branch_qty) AS activeBranchCount              -- 动销门店数
        FROM
            dws_trd_sku_sales_month ts
        LEFT JOIN
            dim_sku ds ON ts.sku_id = ds.sku_id
        LEFT JOIN
            dim_supplier dsup ON ts.supplier_id = dsup.supplier_id
        WHERE
            ts.month_id = #{currentMonthId} -- 按月份过滤
            <if test="skuNo != null and skuNo != ''">
                AND ds.spu_no = #{skuNo}            -- 按商品编号过滤
            </if>
            <if test="skuName != null and skuName != ''">
                AND ds.spu_name LIKE CONCAT('%', #{skuName}, '%') -- 按商品名称模糊查询
            </if>
            ${params.dataScope}
        GROUP BY
            ds.sku_id,
            ds.spu_name,
            ds.spu_no,
            ds.barcode,
            ds.properties,
            ds.unit,
            ds.catgory1_name,
            ds.catgory2_name,
            ds.catgory3_name,
            ds.brand_name,
            ds.supplier_id,
            dsup.supplier_name
        LIMIT #{pageNo}, #{pageSize}
    </select>
    <select id="countMonthlySkuSalesSummary" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT ts.sku_id)
        FROM
            dws_trd_sku_sales_month ts
         LEFT JOIN
            dim_sku ds ON ts.sku_id = ds.sku_id
        LEFT JOIN
            dim_supplier dsup ON ts.supplier_id = dsup.supplier_id
        WHERE
            ts.month_id = #{currentMonthId} -- 按月份过滤
            <if test="skuNo != null and skuNo != ''">
                AND ds.spu_no = #{skuNo}            -- 按商品编号过滤
            </if>
            <if test="skuName != null and skuName != ''">
                AND ds.spu_name LIKE CONCAT('%', #{skuName}, '%') -- 按商品名称模糊查询
            </if>
            ${params.dataScope}
    </select>
</mapper>