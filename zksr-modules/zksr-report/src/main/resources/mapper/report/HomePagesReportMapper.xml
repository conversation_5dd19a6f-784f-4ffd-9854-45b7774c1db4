<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.report.mapper.HomePagesReportMapper">

    <!-- 获取首页订单销售数据 -->
    <select id="getHomePagesOrderSalesData"
            resultType="com.zksr.report.api.homePages.dto.HomePagesOrderSalesDataRespDTO">
        SELECT
            orderTable.sys_code,
            <if test="null != isDc and isDc != 0">
                orderTable.dc_id,
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                orderTable.supplier_id,
            </if>
            SUM( orderTable.orderAmt ) AS orderAmt,
            SUM( orderTable.orderQty ) AS orderQty,
            SUM( orderTable.orderBranchQty ) AS orderBranchQty,
            SUM(orderTable.orderSkuQty) AS orderSkuQty,
            SUM( orderTable.orderBranchAvgAmt ) AS orderBranchAvgAmt,
            SUM( orderTable.debtOrderAmt ) AS debtOrderAmt,
            SUM( orderTable.debtOrderQty ) AS debtOrderQty,
            SUM( orderTable.debtOrderBranchQty ) AS debtOrderBranchQty,
            SUM( orderTable.beforeOrderAmt ) AS beforeOrderAmt,
            SUM( orderTable.beforeOrderQty ) AS beforeOrderQty,
            SUM( orderTable.beforeOrderBranchQty ) AS beforeOrderBranchQty,
            SUM(orderTable.beforeOrderSkuQty) AS beforeOrderSkuQty,
            SUM( orderTable.beforeOrderBranchAvgAmt ) AS beforeOrderBranchAvgAmt,
            ROUND((( SUM( orderTable.orderQty ) - SUM( orderTable.beforeOrderQty )) / SUM( orderTable.beforeOrderQty )) * 100, 2 ) AS orderQtyRate,
            ROUND((( SUM( orderTable.orderBranchQty ) - SUM( orderTable.beforeOrderBranchQty )) / SUM( orderTable.beforeOrderBranchQty )) * 100, 2 ) AS orderBranchQtyRate,
            ROUND((( SUM( orderTable.orderBranchAvgAmt ) - SUM( orderTable.beforeOrderBranchAvgAmt ))/ SUM( orderTable.beforeOrderBranchAvgAmt )) * 100, 2 ) AS orderBranchAmtAvgRate
        FROM (
            SELECT
                dtodi.sys_code,
                <if test="null != isDc and isDc != 0">
                    dtodi.dc_id,
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    dtodi.supplier_id,
                </if>
                IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS orderAmt,
                COUNT(DISTINCT dtodi.order_id) AS orderQty,
                COUNT(DISTINCT dtodi.branch_id) AS orderBranchQty,
                COUNT( DISTINCT dtodi.sku_id ) AS orderSkuQty,
                ROUND((SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0))) / COUNT( DISTINCT dtodi.branch_id ), 2 ) AS orderBranchAvgAmt,
                SUM( CASE WHEN dtodi.pay_state = 3 THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END ) AS debtOrderAmt,
                COUNT( DISTINCT CASE WHEN dtodi.pay_state = 3 THEN dtodi.order_id END ) AS debtOrderQty,
                COUNT( DISTINCT CASE WHEN dtodi.pay_state = 3 THEN dtodi.branch_id END ) AS debtOrderBranchQty,
                0 AS beforeOrderAmt,
                0 AS beforeOrderQty,
                0 AS beforeOrderBranchQty,
                0 AS beforeOrderSkuQty,
                0 AS beforeOrderBranchAvgAmt
            FROM
                dwd_trd_order_dtl_inc dtodi
                LEFT JOIN (
                    SELECT
                        supplier_order_dtl_id supplier_order_dtl_id,
                        sum( refund_amt ) refund_amt,
                        sum( cancel_qty ) cancel_qty
                    FROM
                        dwd_trd_cancel_inc
                    WHERE
                        order_time BETWEEN CONCAT(#{startDate}, ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                    GROUP BY
                        supplier_order_dtl_id
                ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
            WHERE
                dtodi.sys_code = #{sysCode}
                AND dtodi.order_time BETWEEN CONCAT(#{startDate}, ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                AND dtodi.pay_state IN (1, 3, 4)
                AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
            GROUP BY
                dtodi.sys_code
                <if test="null != isDc and isDc != 0">
                    , dtodi.dc_id
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    , dtodi.supplier_id
                </if>

            UNION ALL

            SELECT
                dtodi.sys_code,
                <if test="null != isDc and isDc != 0">
                    dtodi.dc_id,
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    dtodi.supplier_id,
                </if>
                0 AS orderAmt,
                0 AS orderQty,
                0 AS orderBranchQty,
                0 AS orderSkuQty,
                0 AS orderBranchAvgAmt,
                0 AS debtOrderAmt,
                0 AS debtOrderQty,
                0 AS debtOrderBranchQty,
                IFNULL(SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0)), 0) AS beforeOrderAmt,
                COUNT(DISTINCT dtodi.order_id) AS beforeOrderQty,
                COUNT(DISTINCT dtodi.branch_id) AS beforeOrderBranchQty,
                COUNT(DISTINCT dtodi.sku_id) AS beforeOrderSkuQty,
                ROUND((SUM(dtodi.total_amt) - SUM(IFNULL(dtci.refund_amt, 0))) / COUNT( DISTINCT dtodi.branch_id ), 2 ) AS beforeOrderBranchAvgAmt
            FROM
                dwd_trd_order_dtl_inc dtodi
                LEFT JOIN (
                    SELECT
                        supplier_order_dtl_id supplier_order_dtl_id,
                        sum( refund_amt ) refund_amt,
                        sum( cancel_qty ) cancel_qty
                    FROM
                        dwd_trd_cancel_inc
                    WHERE
                        order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(DATE_SUB(#{endDate}, INTERVAL 1 MONTH), ' 23:59:59.999')
                    GROUP BY
                        supplier_order_dtl_id
                ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
            WHERE
                dtodi.sys_code = #{sysCode}
                AND dtodi.order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(DATE_SUB(#{endDate}, INTERVAL 1 MONTH), ' 23:59:59.999')
                AND dtodi.pay_state IN (1, 3, 4)
                AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
            GROUP BY
                dtodi.sys_code
                <if test="null != isDc and isDc != 0">
                    , dtodi.dc_id
                </if>
                <if test="null != isSupplier and isSupplier != 0">
                    , dtodi.supplier_id
                </if>
        ) orderTable
        GROUP BY
            orderTable.sys_code
            <if test="null != isDc and isDc != 0">
                , orderTable.dc_id
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                , orderTable.supplier_id
            </if>
    </select>

    <!-- 获取PC首页门店数据 -->
    <select id="getHomePagesBranchData"
            resultType="com.zksr.report.api.homePages.dto.HomePagesBranchDataRespDTO">
        SELECT
            resTable.sys_code,
            <if test="null != isDc and isDc != 0">
                resTable.area_id,
            </if>
            SUM(resTable.branchAddQty) AS branchAddQty,
            SUM(resTable.beforeBranchAddQty) AS beforeBranchAddQty,
            SUM(resTable.branchTotalQty) AS branchTotalQty,
            SUM(resTable.beforeBranchTotalQty) AS beforeBranchTotalQty,
            SUM(resTable.visitBranchQty) AS visitBranchQty,
            SUM(resTable.beforeVisitBranchQty) AS beforeVisitBranchQty,
            SUM(resTable.colonelTotalQty) AS colonelTotalQty,
            SUM(resTable.loginBranchQty) AS loginBranchQty,
            SUM(resTable.beforeLoginBranchQty) AS beforeLoginBranchQty
        FROM (
            SELECT
                branch.sys_code,
                <if test="null != isDc and isDc != 0">
                    branch.area_id,
                </if>
                COUNT(DISTINCT CASE WHEN DATE_FORMAT(branch.create_time, '%Y%m') = DATE_FORMAT(#{startDate}, '%Y%m') THEN branch.branch_id END) AS branchAddQty,
                COUNT(DISTINCT CASE WHEN DATE_FORMAT(branch.create_time, '%Y%m') = DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), '%Y%m') THEN branch.branch_id END) AS beforeBranchAddQty,
                COUNT(DISTINCT branch.branch_id) AS branchTotalQty,
                COUNT(DISTINCT CASE WHEN DATE_FORMAT(branch.create_time, '%Y%m') &lt; DATE_FORMAT(#{startDate}, '%Y%m') THEN branch.branch_id END) AS beforeBranchTotalQty,
                0 AS visitBranchQty,
                0 AS beforeVisitBranchQty,
                0 AS colonelTotalQty,
                0 AS loginBranchQty,
                0 AS beforeLoginBranchQty
            FROM
                dim_branch	branch
            WHERE
                branch.sys_code = #{sysCode}
        		AND branch.create_time &lt;= CONCAT(#{endDate}, ' 23:59:59.999')
            GROUP BY
                branch.sys_code
                <if test="null != isDc and isDc != 0">
                    , branch.area_id
                </if>


            UNION ALL

            SELECT
                dcvl.sys_code,
                <if test="null != isDc and isDc != 0">
                    dcvl.area_id,
                </if>
                0 AS branchAddQty,
                0 AS beforeBranchAddQty,
                0 AS branchTotalQty,
                0 AS beforeBranchTotalQty,
                COUNT(DISTINCT CASE WHEN DATE_FORMAT(dcvl.create_time, '%Y%m') = DATE_FORMAT(#{startDate}, '%Y%m') THEN dcvl.branch_id END) AS visitBranchQty,
                COUNT(DISTINCT CASE WHEN DATE_FORMAT(dcvl.create_time, '%Y%m') = DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), '%Y%m') THEN dcvl.branch_id END) AS beforeVisitBranchQty,
                0 AS colonelTotalQty,
                0 AS loginBranchQty,
                0 AS beforeLoginBranchQty
            FROM
                dwd_colonel_visit_inc dcvl
            WHERE
                dcvl.sys_code = #{sysCode}
                AND dcvl.visit_flag = 1
        		AND dcvl.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            GROUP BY
                dcvl.sys_code
                <if test="null != isDc and isDc != 0">
                    , dcvl.area_id
                </if>


            UNION ALL

            SELECT
                colonel.sys_code,
                <if test="null != isDc and isDc != 0">
                    colonel.area_id,
                </if>
                0 AS branchAddQty,
                0 AS beforeBranchAddQty,
                0 AS branchTotalQty,
                0 AS beforeBranchTotalQty,
                0 AS visitBranchQty,
                0 AS beforeVisitBranchQty,
                COUNT(DISTINCT colonel.colonel_id) AS colonelTotalQty,
                0 AS loginBranchQty,
                0 AS beforeLoginBranchQty
            FROM
                dim_colonel colonel
            WHERE
                colonel.sys_code = #{sysCode}
        		AND colonel.create_time &lt;= CONCAT(#{endDate}, ' 23:59:59.999')
            GROUP BY
                colonel.sys_code
                <if test="null != isDc and isDc != 0">
                    , colonel.area_id
                </if>

            UNION ALL

            SELECT
                dlh.sys_code,
                <if test="null != isDc and isDc != 0">
                    dlh.area_id,
                </if>
                0 AS branchAddQty,
                0 AS beforeBranchAddQty,
                0 AS branchTotalQty,
                0 AS beforeBranchTotalQty,
                0 AS visitBranchQty,
                0 AS beforeVisitBranchQty,
                0 AS colonelTotalQty,
                COUNT(DISTINCT CASE WHEN DATE_FORMAT(dlh.create_time, '%Y%m') = DATE_FORMAT(#{startDate}, '%Y%m') THEN dlh.branch_id END) AS loginBranchQty,
                COUNT(DISTINCT CASE WHEN DATE_FORMAT(dlh.create_time, '%Y%m') = DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), '%Y%m') THEN dlh.branch_id END) AS beforeLoginBranchQty
            FROM
                dwd_login_his_inc dlh
            WHERE
                dlh.sys_code = #{sysCode}
                AND dlh.create_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            GROUP BY
                dlh.sys_code
                <if test="null != isDc and isDc != 0">
                    , dlh.area_id
                </if>

        ) resTable
        GROUP BY
            resTable.sys_code
            <if test="null != isDc and isDc != 0">
                , resTable.area_id
            </if>
    </select>

    <!-- 获取订单退货销售数据 -->
    <select id="getHomePagesOrderAfterData"
            resultType="com.zksr.report.api.homePages.dto.HomePagesOrderAfterDataRespDTO">
        SELECT
            afterTable.sys_code,
            <if test="null != isDc and isDc != 0">
                afterTable.dc_id,
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                afterTable.supplier_id,
            </if>
            SUM( afterTable.returnAmt ) AS returnAmt,
            SUM( afterTable.returnQty ) AS returnQty,
            SUM( afterTable.returnBranchQty ) AS returnBranchQty,
            SUM( afterTable.returnSkuQty ) AS returnSkuQty,
            SUM( afterTable.pendingReturnAmt ) AS pendingReturnAmt,
            SUM( afterTable.pendingReturnQty ) AS pendingReturnQty,
            SUM( afterTable.pendingeturnBranchQty ) AS pendingeturnBranchQty,
            SUM( afterTable.beforeReturnAmt ) AS beforeReturnAmt,
            SUM( afterTable.beforeReturnQty ) AS beforeReturnQty,
            SUM( afterTable.beforeReturnBranchQty ) AS beforeReturnBranchQty,
            SUM( afterTable.beforeReturnSkuQty ) AS beforeReturnSkuQty
        FROM
            (
                SELECT
                    dtri.sys_code,
                    <if test="null != isDc and isDc != 0">
                        dtri.dc_id,
                    </if>
                    <if test="null != isSupplier and isSupplier != 0">
                        dtri.supplier_id,
                    </if>
                    SUM( dtri.refund_amt ) AS returnAmt,
                    COUNT( DISTINCT dtri.after_no ) AS returnQty,
                    COUNT( DISTINCT dtri.branch_id ) AS returnBranchQty,
                    COUNT( DISTINCT dtri.sku_id ) AS returnSkuQty,
                    0 AS pendingReturnAmt,
                    0 AS pendingReturnQty,
                    0 AS pendingeturnBranchQty,
                    0 AS beforeReturnAmt,
                    0 AS beforeReturnQty,
                    0 AS beforeReturnBranchQty,
                    0 AS beforeReturnSkuQty
                FROM
                    dwd_trd_return_inc dtri
                WHERE
                    dtri.sys_code = 4
		            AND dtri.order_time BETWEEN CONCAT(#{startDate}, ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                GROUP BY
                    dtri.sys_code
                    <if test="null != isDc and isDc != 0">
                        , dtri.dc_id
                    </if>
                    <if test="null != isSupplier and isSupplier != 0">
                        , dtri.supplier_id
                    </if>

                UNION ALL

                SELECT
                    dtri.sys_code,
                    <if test="null != isDc and isDc != 0">
                        dtri.dc_id,
                    </if>
                    <if test="null != isSupplier and isSupplier != 0">
                        dtri.supplier_id,
                    </if>
                    0 AS returnAmt,
                    0 AS returnQty,
                    0 AS returnBranchQty,
                    0 AS returnSkuQty,
                    SUM( dtri.refund_amt ) AS pendingReturnAmt,
                    COUNT( DISTINCT dtri.after_no ) AS pendingReturnQty,
                    COUNT( DISTINCT dtri.branch_id ) AS pendingeturnBranchQty,
                    0 AS beforeReturnAmt,
                    0 AS beforeReturnQty,
                    0 AS beforeReturnBranchQty,
                    0 AS beforeReturnSkuQty
                FROM
                    dwd_trd_return_inc dtri
                WHERE
                    dtri.sys_code = 4
                    AND dtri.approve_state != 2
			        AND dtri.refund_state != 2
			        AND dtri.order_time BETWEEN CONCAT(#{startDate}, ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                GROUP BY
                    dtri.sys_code
                    <if test="null != isDc and isDc != 0">
                        , dtri.dc_id
                    </if>
                    <if test="null != isSupplier and isSupplier != 0">
                        , dtri.supplier_id
                    </if>

                UNION ALL

                SELECT
                    dtri.sys_code,
                    <if test="null != isDc and isDc != 0">
                        dtri.dc_id,
                    </if>
                    <if test="null != isSupplier and isSupplier != 0">
                        dtri.supplier_id,
                    </if>
                    0 AS returnAmt,
                    0 AS returnQty,
                    0 AS returnBranchQty,
                    0 AS returnSkuQty,
                    0 AS pendingReturnAmt,
                    0 AS pendingReturnQty,
                    0 AS pendingeturnBranchQty,
                    SUM( dtri.refund_amt ) AS beforeReturnAmt,
                    COUNT( DISTINCT dtri.after_no ) AS beforeReturnQty,
                    COUNT( DISTINCT dtri.branch_id ) AS beforeReturnBranchQty,
                    COUNT( DISTINCT dtri.sku_id ) AS beforeReturnSkuQty
                FROM
                    dwd_trd_return_inc dtri
                WHERE
                    dtri.sys_code = 4
				    AND dtri.order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(DATE_SUB(#{endDate}, INTERVAL 1 MONTH), ' 23:59:59.999')
                GROUP BY
                    dtri.sys_code
                    <if test="null != isDc and isDc != 0">
                        , dtri.dc_id
                    </if>
                    <if test="null != isSupplier and isSupplier != 0">
                        , dtri.supplier_id
                    </if>
            ) afterTable
        GROUP BY
            afterTable.sys_code
            <if test="null != isDc and isDc != 0">
                , afterTable.dc_id
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                , afterTable.supplier_id
            </if>
    </select>

    <!-- 获取PC首页SKU数据 -->
    <select id="getHomePagesSkuData" resultType="com.zksr.report.api.homePages.dto.HomePagesSkuDataRespDTO">
        SELECT
            itemShelf.sys_code,
            <if test="null != isSupplier and isSupplier != 0">
                itemShelf.supplier_id,
            </if>
            COUNT(DISTINCT CASE WHEN DATE_FORMAT(itemShelf.create_time, '%Y%m') &lt; DATE_FORMAT(#{startDate}, '%Y%m') THEN itemShelf.sku_id END) AS skuShelfQty,
            COUNT(DISTINCT CASE WHEN DATE_FORMAT(itemShelf.create_time, '%Y%m') &lt; DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), '%Y%m') THEN itemShelf.sku_id END) AS beforeSkuShelfQty,
            COUNT(DISTINCT CASE WHEN DATE_FORMAT(itemShelf.create_time, '%Y%m') = DATE_FORMAT(#{startDate}, '%Y%m') THEN itemShelf.sku_id END) AS skuNewShelfQty,
            COUNT(DISTINCT CASE WHEN DATE_FORMAT(itemShelf.create_time, '%Y%m') = DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), '%Y%m') THEN itemShelf.sku_id END) AS beforeSkuNewShelfQty,
            (
                SELECT
                    COUNT(1)
                FROM
                    dim_sku sku
                WHERE
                    sku.sys_code = #{sysCode}
            ) AS skuTotalQty
        FROM (
            SELECT
                sys_code,
                area_id,
                supplier_id,
                start_date AS create_time,
                sku_id,
                spu_id
            FROM
                prdt_area_item_zip
            WHERE
                sys_code = #{sysCode}
                AND start_date &lt;= CONCAT(#{endDate}, ' 23:59:59.999')
                <if test="null != isDc and isDc != 0 and null != areaIds and areaIds.size > 0">
                    AND area_id IN
                    <foreach collection="areaIds" item="areaId" open="(" separator="," close=")">
                        #{areaId}
                    </foreach>
                </if>

            <if test="null != isDc and isDc == 0">
                UNION ALL

                SELECT
                    sys_code,
                    null,
                    supplier_id,
                    start_date AS create_time,
                    sku_id,
                    spu_id
                FROM
                    prdt_supplier_item_zip
                WHERE
                    sys_code = #{sysCode}
                    AND start_date &lt;= CONCAT(#{endDate}, ' 23:59:59.999')
            </if>

        ) itemShelf
        LEFT JOIN dim_sku ds ON itemShelf.sku_id = ds.sku_id
        WHERE
            itemShelf.sys_code = #{sysCode}
        GROUP BY
            itemShelf.sys_code
            <if test="null != isSupplier and isSupplier != 0">
                , itemShelf.supplier_id
            </if>
    </select>

    <!-- 获取区域销售数据TOP10数据 -->
    <select id="getHomePagesAreaSalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            dtodi.sys_code,
            dtodi.area_id AS salesTypeId,
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(#{startDate}, '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    sum( refund_amt ) refund_amt,
                    sum( cancel_qty ) cancel_qty
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            dtodi.sys_code = #{sysCode}
            AND dtodi.order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            AND dtodi.pay_state IN (1, 3, 4)
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            dtodi.sys_code,
            dtodi.area_id
    </select>

    <!-- 获取运营商销售数据TOP10数据 -->
    <select id="getHomePagesDcSalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            dtodi.sys_code,
            dtodi.dc_id AS salesTypeId,
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(#{startDate}, '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    sum( refund_amt ) refund_amt,
                    sum( cancel_qty ) cancel_qty
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            dtodi.sys_code = #{sysCode}
            AND dtodi.order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            AND dtodi.pay_state IN (1, 3, 4)
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            dtodi.sys_code,
            dtodi.dc_id
    </select>

    <!-- 获取商品销售数据TOP10数据 -->
    <select id="getHomePagesItemSalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            dtodi.sys_code,
            dtodi.spu_id AS salesTypeId,
            <if test="null != isDc and isDc != 0">
                dtodi.dc_id,
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                dtodi.supplier_id,
            </if>
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(#{startDate}, '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    sum( refund_amt ) refund_amt,
                    sum( cancel_qty ) cancel_qty
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            dtodi.sys_code = #{sysCode}
            AND dtodi.order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            AND dtodi.pay_state IN (1, 3, 4)
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
        GROUP BY
            dtodi.sys_code,
            dtodi.spu_id
            <if test="null != isDc and isDc != 0">
                , dtodi.dc_id
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                , dtodi.supplier_id
            </if>
    </select>

    <!-- 获取一级品类销售数据TOP10数据 -->
    <select id="getHomePagesCategorySalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            dtodi.sys_code,
            catgory.catgory_id AS salesTypeId,
            <!-- 这里是用于 查询一级管理分类动销数据-->
            <if test="null != isDc and isDc != 0">
                dtodi.dc_id,
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                dtodi.supplier_id,
            </if>
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(#{startDate}, '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    sum( refund_amt ) refund_amt,
                    sum( cancel_qty ) cancel_qty
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
            LEFT JOIN dim_sku sku ON dtodi.sku_id = sku.sku_id
            LEFT JOIN dim_catgory catgory ON catgory.catgory_id = sku.catgory1_id
        WHERE
            dtodi.pay_state IN (1, 3, 4)
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
            AND dtodi.order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            AND dtodi.sys_code = #{sysCode}
            AND catgory.level = 1
        GROUP BY
            dtodi.sys_code,
            catgory.catgory_id
            <if test="null != isDc and isDc != 0">
                , dtodi.dc_id
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                , dtodi.supplier_id
            </if>
    </select>

    <!-- 获取入驻商销售数据TOP10数据 -->
    <select id="getHomePagesSupplierSalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            dtodi.sys_code,
            dtodi.dc_id,
            dtodi.supplier_id AS salesTypeId,
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(#{startDate}, '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    sum( refund_amt ) refund_amt,
                    sum( cancel_qty ) cancel_qty
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            dtodi.pay_state IN (1, 3, 4)
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
            AND dtodi.order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            AND dtodi.sys_code = #{sysCode}
        GROUP BY
            dtodi.sys_code,
            dtodi.dc_id,
            dtodi.supplier_id
    </select>

    <!-- 获取查询业务员销售数据TOP10数据 -->
    <select id="getHomePagesColonelSalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            dtodi.sys_code,
            dtodi.dc_id,
            dtodi.colonel_id AS salesTypeId,
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(#{startDate}, '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    sum( refund_amt ) refund_amt,
                    sum( cancel_qty ) cancel_qty
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            dtodi.pay_state IN (1, 3, 4)
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
            AND dtodi.order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            AND dtodi.sys_code = #{sysCode}
            AND dtodi.colonel_id IS NOT NULL
        GROUP BY
            dtodi.sys_code,
            dtodi.dc_id,
            dtodi.colonel_id
    </select>

    <!-- 获取门店销售数据TOP10数据 -->
    <select id="getHomePagesBranchSalesTop10Data"
            resultType="com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO">
        SELECT
            dtodi.sys_code,
            dtodi.branch_id AS salesTypeId,
            <if test="null != isDc and isDc != 0">
                dtodi.dc_id,
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                dtodi.supplier_id,
            </if>
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(#{startDate}, '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS orderSalesAmt,
            SUM(CASE WHEN DATE_FORMAT(dtodi.order_time, '%Y%m') = DATE_FORMAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), '%Y%m') THEN dtodi.total_amt - IFNULL(dtci.refund_amt, 0) ELSE 0 END) AS beforeOrderSalesAmt
        FROM
            dwd_trd_order_dtl_inc dtodi
            LEFT JOIN (
                SELECT
                    supplier_order_dtl_id supplier_order_dtl_id,
                    sum( refund_amt ) refund_amt,
                    sum( cancel_qty ) cancel_qty
                FROM
                    dwd_trd_cancel_inc
                WHERE
                    order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
                GROUP BY
                    supplier_order_dtl_id
            ) dtci ON dtodi.supplier_order_dtl_id = dtci.supplier_order_dtl_id
        WHERE
            dtodi.pay_state IN (1, 3, 4)
            AND (dtodi.total_num - IFNULL(dtci.cancel_qty, 0)) > 0
            AND dtodi.order_time BETWEEN CONCAT(DATE_SUB(#{startDate}, INTERVAL 1 MONTH), ' 00:00:00.000') AND CONCAT(#{endDate}, ' 23:59:59.999')
            AND dtodi.sys_code = #{sysCode}
        GROUP BY
            dtodi.sys_code,
            dtodi.branch_id
            <if test="null != isDc and isDc != 0">
                , dtodi.dc_id
            </if>
            <if test="null != isSupplier and isSupplier != 0">
                , dtodi.supplier_id
            </if>
    </select>

    <!-- 获取PC首页SKU数据 - 查询一级管理分类总数 -->
    <select id="getHomePagesCategoryData" resultType="com.zksr.report.api.homePages.dto.HomePagesSkuDataRespDTO">
        SELECT
            COUNT(DISTINCT CASE WHEN DATE_FORMAT(create_time, '%Y%m%d') &lt;= DATE_FORMAT(now(), '%Y%m%d') THEN catgory_id END) AS category1Qty,
            COUNT(DISTINCT CASE WHEN DATE_FORMAT(create_time, '%Y%m%d') &lt; DATE_FORMAT(now(), '%Y%m%d') THEN catgory_id END) AS beforeCategory1Qty
        FROM
            dim_catgory
        WHERE
            sys_code = #{sysCode}
          AND create_time &lt;= CONCAT(#{endDate}, ' 23:59:59.999')
          AND level = 1
    </select>
</mapper>
