<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.report.mapper.DwsTrdSupplierSalesMonthMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getSupplierMonthSalesPage"
            resultType="com.zksr.report.controller.dwsSupplierSales.dto.DwsTrdSupplierSalesMonthRespDTO">
        SELECT
            dtssm.supplier_id
            , ds.supplier_name
            , dtssm.area_id
            , da.area_name
            , dtssm.dx_sku_qty
            , dtssm.dx_branch_qty
            , dtssm.pre_order_amt
            , dtssm.order_amt
            , dtssm.order_qty
            , dtssm.supplier_cost_amt
            , dtssm.discount_amt
            , ROUND(dtssm.profit_rate, 2) AS profit_rate
            , dtssm.return_amt
            , dtssm.return_qty
            , dtssm.return_sku_qty
            , dtssm.return_supplier_cost_amt
            , (dtssm.order_amt - dtssm.supplier_cost_amt) AS profitAmt
        FROM
            dws_trd_supplier_sales_month dtssm
            LEFT JOIN dim_supplier ds ON dtssm.supplier_id = ds.supplier_id
            LEFT JOIN dim_area da ON dtssm.area_id = da.area_id
        WHERE
            dtssm.month_id = REPLACE(#{searchMonth}, '-', '')
            <if test="null != areaId">
                AND dtssm.area_id = #{areaId}
            </if>
            <if test="null != searchSupplier and searchSupplier != '' ">
                AND ( ds.supplier_name LIKE CONCAT('%', #{searchSupplier}, '%') OR dtssm.supplier_id LIKE CONCAT('%', #{searchSupplier}, '%') )
            </if>
    </select>
</mapper>