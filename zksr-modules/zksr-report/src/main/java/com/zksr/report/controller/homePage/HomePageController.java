package com.zksr.report.controller.homePage;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.elasticsearch.service.*;
import com.zksr.report.api.homePages.dto.*;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.report.service.IHomePageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * PC后台主页报表Controller
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Api(tags = "管理后台 - PC后台主页报表接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/homePageController")
public class HomePageController {
    @Autowired
    private IHomePageService homePageService;

    @Autowired
    private EsHomePagesDataService esHomePagesDataService;
    @Autowired
    private EsHomePagesOrderSalesDataService esHomePagesOrderSalesDataService;
    @Autowired
    private EsHomePagesOrderAfterDataService esHomePagesOrderAfterDataService;
    @Autowired
    private EsHomePagesBranchDataService esHomePagesBranchDataService;
    @Autowired
    private EsHomePagesSkuDataService esHomePagesSkuDataService;
    @Autowired
    private EsHomePagesSalesTop10DataService esHomePagesSalesTop10DataService;

    /**
     * 初始化ES首页数据
     */
    @ApiOperation(value = "初始化ES首页数据索引", httpMethod = HttpMethod.GET)
    @GetMapping(value = "/init4")
    public CommonResult<Boolean> init4() {
        esHomePagesDataService.initIndex();
        esHomePagesOrderSalesDataService.initIndex();
        esHomePagesOrderAfterDataService.initIndex();
        esHomePagesBranchDataService.initIndex();
        esHomePagesSkuDataService.initIndex();
        esHomePagesSalesTop10DataService.initIndex();
        return success(true);
    }

    /**
     *  获取PC首页看板当前销售、欠款、退单实时数据
     */
    @GetMapping("/getCurrentSalesData")
    @ApiOperation(value = "获取首页当前销售、欠款、退单实时数据", httpMethod = HttpMethod.GET)
    public CommonResult<HomePagesCurrentSalesDataRespDTO> getHomePagesCurrentSalesData(@Valid HomePagesReqVO reqVO) {
        return success(homePageService.getCurrentSalesData(reqVO));
    }

    /**
     *  获取PC首页看板订单销售数据
     */
    @GetMapping("/getHomePagesOrderSalesData")
    @ApiOperation(value = "获取订单销售数据", httpMethod = HttpMethod.GET)
    public CommonResult<HomePagesOrderSalesDataRespDTO> getHomePagesOrderSalesData(@Valid HomePagesReqVO reqVO) {
        return success(homePageService.getOrderSalesData(reqVO));
    }

    /**
     *  获取PC首页看板门店数据
     */
    @GetMapping("/getHomePagesBranchChangeData")
    @ApiOperation(value = "获取门店变动数据", httpMethod = HttpMethod.GET)
    public CommonResult<HomePagesBranchDataRespDTO> getHomePagesBranchChangeData(@Valid HomePagesReqVO reqVO) {
        return success(homePageService.getHomePagesBranchData(reqVO));
    }

    /**
     *  获取PC首页看板订单销售数据
     */
    @GetMapping("/getHomePagesOrderAfterData")
    @ApiOperation(value = "获取订单售后数据", httpMethod = HttpMethod.GET)
    public CommonResult<HomePagesOrderAfterDataRespDTO> getHomePagesOrderAfterData(@Valid HomePagesReqVO reqVO) {
        return success(homePageService.getOrderAfterData(reqVO));
    }

    /**
     *  获取SKU上架数据
     */
    @GetMapping("/getHomePagesSkuData")
    @ApiOperation(value = "获取SKU上架数据", httpMethod = HttpMethod.GET)
    public CommonResult<HomePagesSkuDataRespDTO> getHomePagesSkuData(@Valid HomePagesReqVO reqVO) {
        return success(homePageService.getHomePagesSkuData(reqVO));
    }

    /**
     *  获取区域销售TOP10数据
     */
    @GetMapping("/getHomePagesAreaSalesTop10Data")
    @ApiOperation(value = "获取区域销售TOP10数据", httpMethod = HttpMethod.GET)
    public CommonResult<List<HomePagesSalesTop10DataRespDTO>> getHomePagesAreaSalesTop10Data(@Valid HomePagesReqVO reqVO) {
        return success(homePageService.getHomePagesAreaSalesTop10Data(reqVO));
    }

    /**
     *  获取运营商销售TOP10数据
     */
    @GetMapping("/getHomePagesDcSalesTop10Data")
    @ApiOperation(value = "获取运营商销售TOP10数据", httpMethod = HttpMethod.GET)
    public CommonResult<List<HomePagesSalesTop10DataRespDTO>> getHomePagesDcSalesTop10Data(@Valid HomePagesReqVO reqVO) {
        return success(homePageService.getHomePagesDcSalesTop10Data(reqVO));
    }

    /**
     *  获取商品销售TOP10数据
     */
    @GetMapping("/getHomePagesItemSalesTop10Data")
    @ApiOperation(value = "获取商品销售TOP10数据", httpMethod = HttpMethod.GET)
    public CommonResult<List<HomePagesSalesTop10DataRespDTO>> getHomePagesItemSalesTop10Data(@Valid HomePagesReqVO reqVO) {
        return success(homePageService.getHomePagesItemSalesTop10Data(reqVO));
    }

    /**
     *  获取一级品类销售TOP10数据
     */
    @GetMapping("/getHomePagesCategroySalesTop10Data")
    @ApiOperation(value = "获取一级品类销售TOP10数据", httpMethod = HttpMethod.GET)
    public CommonResult<List<HomePagesSalesTop10DataRespDTO>> getHomePagesCategroySalesTop10Data(@Valid HomePagesReqVO reqVO) {
        return success(homePageService.getHomePagesCategroySalesTop10Data(reqVO));
    }

    /**
     *  获取入驻商销售TOP10数据
     */
    @GetMapping("/getHomePagesSupplierSalesTop10Data")
    @ApiOperation(value = "获取入驻商销售TOP10数据", httpMethod = HttpMethod.GET)
    public CommonResult<List<HomePagesSalesTop10DataRespDTO>> getHomePagesSupplierSalesTop10Data(@Valid HomePagesReqVO reqVO) {
        return success(homePageService.getHomePagesSupplierSalesTop10Data(reqVO));
    }

    /**
     *  获取业务员销售TOP10数据
     */
    @GetMapping("/getHomePagesColonelSalesTop10Data")
    @ApiOperation(value = "获取业务员销售TOP10数据", httpMethod = HttpMethod.GET)
    public CommonResult<List<HomePagesSalesTop10DataRespDTO>> getHomePagesColonelSalesTop10Data(@Valid HomePagesReqVO reqVO) {
        return success(homePageService.getHomePagesColonelSalesTop10Data(reqVO));
    }

    /**
     *  获取门店销售TOP10数据
     */
    @GetMapping("/getHomePagesBranchSalesTop10Data")
    @ApiOperation(value = "获取门店销售TOP10数据", httpMethod = HttpMethod.GET)
    public CommonResult<List<HomePagesSalesTop10DataRespDTO>> getHomePagesBranchSalesTop10Data(@Valid HomePagesReqVO reqVO) {
        return success(homePageService.getHomePagesBranchSalesTop10Data(reqVO));
    }

    /**
     *  获取PC首页看板订单销售数据
     */
    @GetMapping("/getHomePagesOrderAfterData1111111")
    @ApiOperation(value = "top 10 报表返回数据", httpMethod = HttpMethod.GET)
    public CommonResult<HomePagesSalesTop10DataRespDTO> getHomePagesOrderAfterData1111111(@Valid HomePagesReqVO reqVO) {
        return success(null);
    }
}
