package com.zksr.report.controller.colonelSalesSummary;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.report.controller.colonelSalesSummary.dto.ColonelSalesSummaryDTO;
import com.zksr.report.controller.colonelSalesSummary.dto.ColonelSalesSummaryQueryDTO;
import com.zksr.report.service.IColonelSalesSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "管理后台 - 业务员月销售汇总接口", produces = "application/json")
@RestController
@Validated
@RequestMapping("/colonelSalesSummary")
public class ColonelSalesSummaryController {

    @Autowired
    private IColonelSalesSummaryService colonelSalesSummaryService;

    /**
     * 获取业务员月销售汇总
     *
     * @param queryDTO 查询条件
     * @return 汇总数据列表
     */
    @PostMapping("/monthlyColonel")
    @ApiOperation("获取业务员月销售汇总")
    @DataScope(dcAlias = "t1")
    public CommonResult<PageResult<ColonelSalesSummaryDTO>> getMonthlyColonelSalesSummary(@RequestBody ColonelSalesSummaryQueryDTO queryDTO) {
        return CommonResult.success(colonelSalesSummaryService.getMonthlyColonelSalesSummary(queryDTO));
    }
}