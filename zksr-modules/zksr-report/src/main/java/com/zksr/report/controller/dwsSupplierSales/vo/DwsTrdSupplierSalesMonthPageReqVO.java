package com.zksr.report.controller.dwsSupplierSales.vo;

import java.math.BigDecimal;
import java.util.Date;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 交易域入驻商粒度下单月汇总对象 dws_trd_supplier_sales_month
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@ApiModel("交易域入驻商粒度下单月汇总 - dws_trd_supplier_sales_month分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DwsTrdSupplierSalesMonthPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "查询年月;字符串格式（yyyy-mm）")
    private String searchMonth;

    @ApiModelProperty(value = "区域ID")
    private Long areaId;

    @ApiModelProperty(value = "入驻商搜索（入驻商ID 或 入驻商名称）")
    private String searchSupplier;


}
