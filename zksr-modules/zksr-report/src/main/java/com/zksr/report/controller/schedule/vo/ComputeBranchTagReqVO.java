package com.zksr.report.controller.schedule.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 计算门店标签
 * @date 2024/11/13 14:16
 */
@Data
@NoArgsConstructor
@ApiModel(description = "计算门店标签")
public class ComputeBranchTagReqVO {

    @ApiModelProperty(value = "月份格式:yyyyMM", example = "202411")
    private String monthDate;

    @ApiModelProperty("门店ID")
    private Long branchId;

    public ComputeBranchTagReqVO(String monthDate) {
        this.monthDate = monthDate;
    }
}
