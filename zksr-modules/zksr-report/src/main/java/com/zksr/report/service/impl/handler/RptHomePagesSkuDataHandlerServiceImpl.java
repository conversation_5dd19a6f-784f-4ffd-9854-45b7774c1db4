package com.zksr.report.service.impl.handler;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.elasticsearch.domain.EsHomePagesOrderSalesData;
import com.zksr.common.elasticsearch.domain.EsHomePagesSkuData;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;
import com.zksr.common.elasticsearch.service.EsHomePagesOrderSalesDataService;
import com.zksr.common.elasticsearch.service.EsHomePagesSkuDataService;
import com.zksr.product.api.areaItem.AreaItemApi;
import com.zksr.product.api.catgory.CatgoryApi;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesSkuDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.report.convert.homeReport.HomeReportConvert;
import com.zksr.report.mapper.HomePagesReportMapper;
import com.zksr.report.service.handler.RptHomePagesDataHandlerService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年012月26日 10:33
 * @description: PC首页看板 SKU 数据
 */
@Component
@Order(RptHomePagesDataHandlerService.SKU_SHELF_DATA)
@Slf4j
@SuppressWarnings("all")
public class RptHomePagesSkuDataHandlerServiceImpl implements RptHomePagesDataHandlerService {
    @Autowired
    private AreaItemApi areaItemApi;
    @Autowired
    private DcApi dcApi;
    @Autowired
    private AreaApi areaApi;
    @Autowired
    private CatgoryApi catgoryApi;
    @Autowired
    private OrderApi orderApi;

    @Autowired
    private EsHomePagesSkuDataService esHomePagesSkuDataService;

    @Autowired
    private HomePagesReportMapper homePagesReportMapper;
    @Autowired
    private EsHomePagesOrderSalesDataService esHomePagesOrderSalesDataService;
    @Override
    public void dayDataStatistics(HomePagesReqVO reqVO) {
        // 查询平台商 SKU数据
        reqVO.setIsSupplier(NumberPool.INT_ZERO)
                .setIsDc(NumberPool.INT_ZERO);
        List<HomePagesSkuDataRespDTO> dataPartnerList = areaItemApi.getHomePagesSkuData(reqVO).getCheckedData();

        // 查询平台商、所有入驻商的数据
        reqVO.setIsSupplier(NumberPool.INT_ONE)
                .setIsDc(NumberPool.INT_ZERO);
        List<HomePagesSkuDataRespDTO> dataSupplierList = areaItemApi.getHomePagesSkuData(reqVO).getCheckedData();


        // 根据平台商编号获取该平台下所有得运营商
        List<DcDTO> dcList = dcApi.getDcBySysCode(reqVO.getSysCode()).getCheckedData();
        // 根据平台商编号获取该平台下所有得区域城市
        List<AreaDTO> areaList = areaApi.getListBySysCode(reqVO.getSysCode()).getCheckedData();
        // 根据运营商ID分组区域城市
        Map<Long, Set<Long>> dcAreaMap = areaList.stream()
                .filter(area -> ToolUtil.isNotEmpty(area.getDcId()))
                .collect(Collectors.groupingBy(AreaDTO::getDcId, Collectors.mapping(AreaDTO::getAreaId, Collectors.toSet())));

        // 根据查询 组装数据
        List<HomePagesSkuDataRespDTO> dataDcList = dcList.stream().map(dc -> {
            Set<Long> areaSet = dcAreaMap.get(dc.getDcId());
            reqVO.setIsSupplier(NumberPool.INT_ZERO)
                    .setIsDc(NumberPool.INT_ONE)
                    .setAreaIds(areaSet)
            ;
            List<HomePagesSkuDataRespDTO> dataList = areaItemApi.getHomePagesSkuData(reqVO).getCheckedData();
            HomePagesSkuDataRespDTO dataRespDTO = new HomePagesSkuDataRespDTO();
            dataRespDTO.setSysCode(reqVO.getSysCode())
                    .setDcId(dc.getDcId());
            if (ToolUtil.isNotEmpty(dataList) && !dataList.isEmpty()) {
                dataList.forEach(areaData -> {
                    dataRespDTO.setSkuShelfQty(dataRespDTO.getSkuShelfQty() + areaData.getSkuShelfQty())
                            .setBeforeSkuShelfQty(dataRespDTO.getBeforeSkuShelfQty() + areaData.getBeforeSkuShelfQty())
                            .setCategory1Qty(areaData.getCategory1Qty())
                            .setBeforeCategory1Qty(areaData.getBeforeCategory1Qty())
                            .setSkuNewShelfQty(dataRespDTO.getSkuNewShelfQty() + areaData.getSkuNewShelfQty())
                            .setBeforeSkuNewShelfQty(dataRespDTO.getBeforeSkuNewShelfQty() + areaData.getBeforeSkuNewShelfQty())
                    ;

                });
            }
            return dataRespDTO;
        }).collect(Collectors.toList());

        List<HomePagesSkuDataRespDTO> mergedList = new ArrayList<>();
        mergedList.addAll(dataPartnerList);
        mergedList.addAll(dataSupplierList);
        mergedList.addAll(dataDcList);

        // 兼容处理一级品类动销数量
        getCategorySalesData(reqVO, mergedList);

        saveBatch(reqVO, mergedList);
    }

    @Override
    public void monthDataStatistics(HomePagesReqVO reqVO) {
        // 查询平台商 SKU数据
        reqVO.setIsSupplier(NumberPool.INT_ZERO)
                .setIsDc(NumberPool.INT_ZERO);
        List<HomePagesSkuDataRespDTO> dataPartnerList = homePagesReportMapper.getHomePagesSkuData(reqVO);

        // 查询平台商、所有入驻商的数据
        reqVO.setIsSupplier(NumberPool.INT_ONE)
                .setIsDc(NumberPool.INT_ZERO);
        List<HomePagesSkuDataRespDTO> dataSupplierList = homePagesReportMapper.getHomePagesSkuData(reqVO);


        // 根据平台商编号获取该平台下所有得运营商
        List<DcDTO> dcList = dcApi.getDcBySysCode(reqVO.getSysCode()).getCheckedData();
        // 根据平台商编号获取该平台下所有得区域城市
        List<AreaDTO> areaList = areaApi.getListBySysCode(reqVO.getSysCode()).getCheckedData();
        // 根据运营商ID分组区域城市
        Map<Long, Set<Long>> dcAreaMap = areaList.stream()
                .filter(area -> ToolUtil.isNotEmpty(area.getDcId()))
                .collect(Collectors.groupingBy(AreaDTO::getDcId, Collectors.mapping(AreaDTO::getAreaId, Collectors.toSet())));

        // 根据查询 组装数据
        List<HomePagesSkuDataRespDTO> dataDcList = dcList.stream().map(dc -> {
            Set<Long> areaSet = dcAreaMap.get(dc.getDcId());
            reqVO.setIsSupplier(NumberPool.INT_ZERO)
                    .setIsDc(NumberPool.INT_ONE)
                    .setAreaIds(areaSet)
            ;
            List<HomePagesSkuDataRespDTO> dataList = homePagesReportMapper.getHomePagesSkuData(reqVO);
            HomePagesSkuDataRespDTO dataRespDTO = new HomePagesSkuDataRespDTO();
            dataRespDTO.setSysCode(reqVO.getSysCode())
                    .setDcId(dc.getDcId());
            if (ToolUtil.isNotEmpty(dataList) && !dataList.isEmpty()) {
                dataList.forEach(areaData -> {
                    dataRespDTO.setSkuShelfQty(dataRespDTO.getSkuShelfQty() + areaData.getSkuShelfQty())
                            .setBeforeSkuShelfQty(dataRespDTO.getBeforeSkuShelfQty() + areaData.getBeforeSkuShelfQty())
                            .setCategory1Qty(dataRespDTO.getCategory1Qty() + areaData.getCategory1Qty())
                            .setBeforeCategory1Qty(dataRespDTO.getBeforeCategory1Qty() + areaData.getBeforeCategory1Qty())
                            .setSkuNewShelfQty(dataRespDTO.getSkuNewShelfQty() + areaData.getSkuNewShelfQty())
                            .setBeforeSkuNewShelfQty(dataRespDTO.getBeforeSkuNewShelfQty() + areaData.getBeforeSkuNewShelfQty())
                    ;
                });
            }
            return dataRespDTO;
        }).collect(Collectors.toList());

        List<HomePagesSkuDataRespDTO> mergedList = new ArrayList<>();
        mergedList.addAll(dataPartnerList);
        mergedList.addAll(dataSupplierList);
        mergedList.addAll(dataDcList);

        // 平台一级品类数据总数
        HomePagesSkuDataRespDTO categoryData = homePagesReportMapper.getHomePagesCategoryData(reqVO);
        if (ToolUtil.isNotEmpty(categoryData)) {
            mergedList.forEach(merged -> {
                merged.setCategory1Qty(categoryData.getCategory1Qty())
                        .setBeforeCategory1Qty(categoryData.getBeforeCategory1Qty())
                ;
            });
        }


        Set<String> categorySalesSet = new HashSet<>();
        // 平台商一级管理分类动销数量
        List<HomePagesSalesTop10DataRespDTO> dataPartnerCategoryList = homePagesReportMapper.getHomePagesCategorySalesTop10Data(reqVO);
        Map<String, List<HomePagesSalesTop10DataRespDTO>> dataPartnerCategoryMap = dataPartnerCategoryList.stream().collect(Collectors.groupingBy(data -> StringUtils.format("{}", data.getSysCode())));
        dataPartnerList.forEach(partnerData -> {
            if (dataPartnerCategoryMap.containsKey(partnerData.getSysCode())) {
                partnerData.setCategory1SalesQty((long) dataPartnerCategoryMap.get(partnerData.getSysCode()).size());
            }
        });

        // 运营商一级管理分类动销数量
        reqVO.setIsDc(NumberPool.INT_ONE)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesSalesTop10DataRespDTO> dataDcCategoryList = homePagesReportMapper.getHomePagesCategorySalesTop10Data(reqVO);
        Map<String, List<HomePagesSalesTop10DataRespDTO>> dataDcCategoryMap = dataDcCategoryList.stream().collect(Collectors.groupingBy(data -> StringUtils.format("{}_{}", data.getSysCode(), data.getDcId())));
        dataDcList.forEach(dcData -> {
            String key = StringUtils.format("{}_{}", dcData.getSysCode(), dcData.getDcId());
            if (dataDcCategoryMap.containsKey(key)) {
                dcData.setCategory1SalesQty((long) dataDcCategoryMap.get(key).size());
            }
        });

        // 入驻商一级管理分类动销数量
        reqVO.setIsSupplier(NumberPool.INT_ONE)
                .setIsDc(NumberPool.INT_ZERO)
                ;
        List<HomePagesSalesTop10DataRespDTO> dataSupplierCategoryList = homePagesReportMapper.getHomePagesCategorySalesTop10Data(reqVO);
        Map<String, List<HomePagesSalesTop10DataRespDTO>> dataSupplierCategoryMap = dataSupplierCategoryList.stream().collect(Collectors.groupingBy(data -> StringUtils.format("{}_{}", data.getSysCode(), data.getSupplierId())));
        dataSupplierList.forEach(supplierData -> {
            String key = StringUtils.format("{}_{}", supplierData.getSysCode(), supplierData.getSupplierId());
            if (dataSupplierCategoryMap.containsKey(key)) {
                supplierData.setCategory1SalesQty((long) dataSupplierCategoryMap.get(key).size());
            }
        });

        saveBatch(reqVO, mergedList);
    }

    private void saveBatch(HomePagesReqVO reqVO,  List<HomePagesSkuDataRespDTO> mergedList) {
        List<EsHomePagesSkuData> esHomePagesData = HomeReportConvert.INSTANCE.convortEsHomePagesSkuData(mergedList);
        esHomePagesData.forEach(data -> {
            HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
            // idKEY: 时间_平台商_运营商_入驻商
            searchDTO.setId(StringUtils.format("{}_{}_{}_{}", reqVO.getDateId(), data.getSysCode(), ToolUtil.isEmptyReturn(data.getDcId(), NumberPool.LONG_ONE), ToolUtil.isEmptyReturn(data.getSupplierId(), NumberPool.LONG_ONE)));
            EsHomePagesOrderSalesData orderData = esHomePagesOrderSalesDataService.searchHomePagesOrderSalesData(searchDTO);
            if (ToolUtil.isNotEmpty(orderData)) {
                data.setSkuSalesQty(orderData.getOrderSkuQty())
                        .setBeforeSkuSalesQty(orderData.getBeforeOrderSkuQty())
                        ;
            }

            data.setDateId(reqVO.getDateId())
                    .setRefreshEsTime(DateUtils.getNowDate())
                    .setId(StringUtils.format("{}_{}_{}_{}", reqVO.getDateId(), reqVO.getSysCode(), ToolUtil.isEmptyReturn(data.getDcId(), 1L), ToolUtil.isEmptyReturn(data.getSupplierId(), 1L)))
                    // 上架SKU同比上升/下降率 = 上架sku数量 - 上次上架sku数量 / 上次上架sku数量 * 100
                    .setSkuShelfRate(
                            BigDecimal.valueOf(data.getSkuShelfQty() - data.getBeforeSkuShelfQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeSkuShelfQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // 动销SKU数量同比上升/下降率 = 动销SKU数量 - 上次动销SKU数量 / 上次动销SKU数量 * 100
                    .setSkuSalesRate(
                            BigDecimal.valueOf(data.getSkuSalesQty() - data.getBeforeSkuSalesQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeSkuSalesQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // SKU动销率 = 动销SKU数量 / 上架sku数量 * 100
                    .setSkuRate(
                            BigDecimal.valueOf(data.getSkuSalesQty()).divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getSkuTotalQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // 一级品类个数同比上升/下降率 = 一级品类数量 - 上次一级品类数量 / 上次一级品类数量 * 100
                    .setCategory1Rate(
                            BigDecimal.valueOf(data.getCategory1Qty() - data.getBeforeCategory1Qty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeCategory1Qty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // 一级品类动销率 = 一级品类动销数量 / 一级品类数量 * 100
                    .setCategory1SalesRate(
                            BigDecimal.valueOf(data.getCategory1SalesQty()).divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getCategory1Qty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // 新上架SKU数量同比上升/下降率 = 新上架SKU数量 - 上次新上架SKU数量 / 上次新上架SKU数量 * 100
                    .setSkuNewShelfRate(
                            BigDecimal.valueOf(data.getSkuNewShelfQty() - data.getBeforeSkuNewShelfQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeSkuNewShelfQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
            ;
       });
        // ES批量保存
        esHomePagesSkuDataService.saveBatchHomePagesSkuData(esHomePagesData);
    }

    /**
     * 获取管理分类销售销售（平台商、运营商、入驻商）
      * @param reqVO
     * @return
     */
    private void getCategorySalesData(HomePagesReqVO reqVO,  List<HomePagesSkuDataRespDTO> mergedList) {
        reqVO.setIsDc(NumberPool.INT_ONE)
                .setIsSupplier(NumberPool.INT_ONE);
        // 根据平台商查询出所有的管理分类
        List<CatgoryDTO> catgoryList = catgoryApi.getListBySysCode(reqVO.getSysCode()).getCheckedData();

        // 过滤出二、三级管理分类，并且按照上级管理分类ID进行分组
        Map<Long, List<CatgoryDTO>> catgoryMap = catgoryList.stream()
                .filter(category -> Objects.equals(category.getLevel(), NumberPool.INT_TWO) || Objects.equals(category.getLevel(), NumberPool.INT_THREE))
                .collect(Collectors.groupingBy(CatgoryDTO::getPid));

        Set<String> categorySalesSet = new HashSet<>();
        catgoryList.stream().filter(category -> Objects.equals(category.getLevel(), NumberPool.INT_ONE))
                .forEach(category -> {
                    HomePagesSalesTop10DataRespDTO categoryTop10Data = new HomePagesSalesTop10DataRespDTO();
                    categoryTop10Data.setSalesTypeId(category.getCatgoryId())
                            .setSysCode(reqVO.getSysCode())
                    ;

                    reqVO.setCategoryIds(new HashSet<>());

                    List<CatgoryDTO> catgory2List = catgoryMap.get(category.getCatgoryId());
                    if (ToolUtil.isNotEmpty(catgory2List) && !catgory2List.isEmpty()) {
                        catgory2List.forEach(category2 -> {
                            List<CatgoryDTO> catgory3List = catgoryMap.get(category2.getCatgoryId());
                            if (ToolUtil.isNotEmpty(catgory3List) && !catgory3List.isEmpty()) {
                                reqVO.getCategoryIds().addAll(
                                        catgory3List.stream().map(CatgoryDTO::getCatgoryId).collect(Collectors.toSet())
                                );
                            }
                        });
                    }
                    if (!reqVO.getCategoryIds().isEmpty()) {
                        reqVO.setSalesType("category");
                        List<HomePagesSalesTop10DataRespDTO> dataList = orderApi.getHomePagesSalesTop10Data(reqVO).getCheckedData();
                        if (ToolUtil.isNotEmpty(dataList) && !dataList.isEmpty()) {
                            categorySalesSet.add(StringUtils.format("{}_{}_{}_{}", reqVO.getSysCode(), 1L, 1L, category.getCatgoryId()));
                            dataList.forEach(data -> {
                                categorySalesSet.add(StringUtils.format("{}_{}_{}_{}", data.getSysCode(), data.getDcId(), 1L, category.getCatgoryId()));
                                categorySalesSet.add(StringUtils.format("{}_{}_{}_{}", data.getSysCode(), 1L, data.getSupplierId(), category.getCatgoryId()));
                            });
                        }
                    }
                });

        mergedList.forEach(merged -> {
            catgoryList.forEach(category -> {
                String key = StringUtils.format("{}_{}_{}_{}", merged.getSysCode(), ToolUtil.isEmptyReturn(merged.getDcId(), 1L), ToolUtil.isEmptyReturn(merged.getSupplierId(), 1L), category.getCatgoryId());
                if (categorySalesSet.contains(key)) {
                    merged.setCategory1SalesQty(merged.getCategory1SalesQty() + 1L);
                }
            });
        });
    }
}
