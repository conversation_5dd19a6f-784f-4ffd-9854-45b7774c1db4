package com.zksr.report.controller.dwsSupplierSales.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * 交易域入驻商粒度下单月汇总对象 dws_trd_supplier_sales_month
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Data
@ApiModel("交易域入驻商粒度下单月汇总 - dws_trd_supplier_sales_month Response VO")
public class DwsTrdSupplierSalesMonthRespDTO {
    private static final long serialVersionUID = 1L;

    /** 年月ID */
    @ApiModelProperty(value = "年月ID")
    private Long monthId;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 入驻商id */
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 入驻商名称 */
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    /** 区域ID */
    @ApiModelProperty(value = "区域ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 区域名称 */
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    /** 上架商品SKU数量 */
    @ApiModelProperty(value = "上架商品SKU数量")
    private Long sjSkuQty;

    /** 下单金额;（有效订单） */
    @ApiModelProperty(value = "原订单金额")
    private BigDecimal preOrderAmt;

    /** 实付金额;（有效订单） */
    @ApiModelProperty(value = "订单实收金额")
    private BigDecimal orderAmt;

    /** 订单数量 */
    @ApiModelProperty(value = "订单数量")
    private Long orderQty;

    /** 动销SKU数;有效订单金额>0 的sku数量 */
    @ApiModelProperty(value = "动销SKU数")
    private Long dxSkuQty;

    /** 动销SPU数;有效订单金额>0 的spu数量 */
    @ApiModelProperty(value = "动销SPU数")
    private Long dxSpuQty;

    /** 动销门店数;有效订单金额>0 的门店数量 */
    @ApiModelProperty(value = "动销门店数")
    private Long dxBranchQty;

    /** 入驻商总成本金额;有效订单的成本金额 */
    @ApiModelProperty(value = "入驻商总成本金额")
    private BigDecimal supplierCostAmt;

    /** 订单优惠金额;有效订单的订单优惠金额 */
    @ApiModelProperty(value = "订单优惠金额")
    private BigDecimal discountAmt;


    /** 收货后售后金额（收货后售后订单） */
    @ApiModelProperty(value = "售后金额")
    private BigDecimal returnAmt;

    /** 退单数量;收货后售后 */
    @ApiModelProperty(value = "售后退单数量")
    private Long returnQty;

    /** 售后SKU数;收货后售后 的sku数量 */
    @ApiModelProperty(value = "售后SKU数")
    private Long returnSkuQty;

    /** 售后SPU数;收货后售后 的spu数量 */
    @ApiModelProperty(value = "售后SPU数")
    private Long returnSpuQty;

    /** 售后门店数;收货后售后 的门店数量 */
    @ApiModelProperty(value = "售后门店数")
    private Long returnBranchQty;

    /** 售后入驻商总成本金额;收货后售后订单的成本金额 */
    @ApiModelProperty(value = "售后入驻商总成本金额")
    private BigDecimal returnSupplierCostAmt;

    /** 毛利率 （入驻商订单实收金额 - 入驻商成本金额） / 入驻商订单实收金额 * 100 */
    @ApiModelProperty(value = "利润率")
    private BigDecimal profitRate;

    /** 毛利率 （入驻商订单实收金额 - 入驻商成本金额） */
    @ApiModelProperty(value = "毛利金额")
    private BigDecimal profitAmt;

}
