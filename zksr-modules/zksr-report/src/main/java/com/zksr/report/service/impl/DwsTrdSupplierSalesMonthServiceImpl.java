package com.zksr.report.service.impl;

import com.github.pagehelper.Page;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.controller.dwsSupplierSales.dto.DwsTrdSupplierSalesDayRespDTO;
import com.zksr.report.controller.dwsSupplierSales.dto.DwsTrdSupplierSalesMonthRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.report.mapper.DwsTrdSupplierSalesMonthMapper;
import com.zksr.report.domain.DwsTrdSupplierSalesMonth;
import com.zksr.report.controller.dwsSupplierSales.vo.DwsTrdSupplierSalesMonthPageReqVO;
import com.zksr.report.service.IDwsTrdSupplierSalesMonthService;


/**
 * 交易域入驻商粒度下单月汇总Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Service
public class DwsTrdSupplierSalesMonthServiceImpl implements IDwsTrdSupplierSalesMonthService {
    @Autowired
    private DwsTrdSupplierSalesMonthMapper dwsTrdSupplierSalesMonthMapper;

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<DwsTrdSupplierSalesMonthRespDTO> getDwsTrdSupplierSalesMonthPage(DwsTrdSupplierSalesMonthPageReqVO pageReqVO) {
        Page<DwsTrdSupplierSalesMonthRespDTO> page = PageUtils.startPage(pageReqVO);
        return new PageResult<>(dwsTrdSupplierSalesMonthMapper.getSupplierMonthSalesPage(pageReqVO), page.getTotal());
    }


    // TODO 待办：请将下面的错误码复制到 com.zksr.report.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 交易域入驻商粒度下单月汇总 TODO 补充编号 ==========
    // ErrorCode DWS_TRD_SUPPLIER_SALES_MONTH_NOT_EXISTS = new ErrorCode(TODO 补充编号, "交易域入驻商粒度下单月汇总不存在");


}
