package com.zksr.report.controller.tagDefinit.vo;

import com.zksr.report.api.branch.dto.BranchTagConfigDTO;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 标签定义对象 rpt_tag_def
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Data
@ApiModel("标签定义 - rpt_tag_def分页 Request VO")
public class RptTagDefSaveReqVO extends BranchTagConfigDTO {

}
