package com.zksr.report.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.report.mapper.DwsTrdBranchSalesDayMapper;
import com.zksr.report.convert.branchSaleDay.DwsTrdBranchSalesDayConvert;
import com.zksr.report.domain.DwsTrdBranchSalesDay;
import com.zksr.report.controller.branchSaleDay.vo.DwsTrdBranchSalesDayPageReqVO;
import com.zksr.report.controller.branchSaleDay.vo.DwsTrdBranchSalesDaySaveReqVO;
import com.zksr.report.service.IDwsTrdBranchSalesDayService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.report.enums.ErrorCodeConstants.*;

/**
 * 交易域门店粒度下单日汇总Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Service
public class DwsTrdBranchSalesDayServiceImpl implements IDwsTrdBranchSalesDayService {
    @Autowired
    private DwsTrdBranchSalesDayMapper dwsTrdBranchSalesDayMapper;

    /**
     * 新增交易域门店粒度下单日汇总
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertDwsTrdBranchSalesDay(DwsTrdBranchSalesDaySaveReqVO createReqVO) {
        // 插入
        DwsTrdBranchSalesDay dwsTrdBranchSalesDay = DwsTrdBranchSalesDayConvert.INSTANCE.convert(createReqVO);
        dwsTrdBranchSalesDayMapper.insert(dwsTrdBranchSalesDay);
        // 返回
        return dwsTrdBranchSalesDay.getDateId();
    }

    /**
     * 修改交易域门店粒度下单日汇总
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateDwsTrdBranchSalesDay(DwsTrdBranchSalesDaySaveReqVO updateReqVO) {
        dwsTrdBranchSalesDayMapper.updateById(DwsTrdBranchSalesDayConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除交易域门店粒度下单日汇总
     *
     * @param dateId 日期ID
     */
    @Override
    public void deleteDwsTrdBranchSalesDay(Long dateId) {
        // 删除
        dwsTrdBranchSalesDayMapper.deleteById(dateId);
    }

    /**
     * 批量删除交易域门店粒度下单日汇总
     *
     * @param dateIds 需要删除的交易域门店粒度下单日汇总主键
     * @return 结果
     */
    @Override
    public void deleteDwsTrdBranchSalesDayByDateIds(Long[] dateIds) {
        for(Long dateId : dateIds){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deleteDwsTrdBranchSalesDay(dateId);
        }
    }

    /**
     * 获得交易域门店粒度下单日汇总
     *
     * @param dateId 日期ID
     * @return 交易域门店粒度下单日汇总
     */
    @Override
    public DwsTrdBranchSalesDay getDwsTrdBranchSalesDay(Long dateId) {
        return dwsTrdBranchSalesDayMapper.selectById(dateId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<DwsTrdBranchSalesDay> getDwsTrdBranchSalesDayPage(DwsTrdBranchSalesDayPageReqVO pageReqVO) {
        return dwsTrdBranchSalesDayMapper.selectPage(pageReqVO);
    }


}
