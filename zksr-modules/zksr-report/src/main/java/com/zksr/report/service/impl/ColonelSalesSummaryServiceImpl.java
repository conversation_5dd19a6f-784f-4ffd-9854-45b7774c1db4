package com.zksr.report.service.impl;

import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.ColonelBranchCountDTO;
import com.zksr.report.api.export.dto.ColonelSalesSummaryExportDTO;
import com.zksr.report.api.export.vo.ColonelSalesSummaryExportPageVO;
import com.zksr.report.controller.colonelSalesSummary.dto.ColonelSalesSummaryDTO;
import com.zksr.report.controller.colonelSalesSummary.dto.ColonelSalesSummaryQueryDTO;
import com.zksr.report.convert.rptExport.RptExportConvert;
import com.zksr.report.mapper.ColonelSalesSummaryMapper;
import com.zksr.report.service.IColonelSalesSummaryService;
import com.zksr.trade.api.after.AfterApi;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ColonelSalesSummaryServiceImpl implements IColonelSalesSummaryService {

    @Autowired
    private ColonelSalesSummaryMapper colonelSalesSummaryMapper;

    @Resource
    private AfterApi afterApi;

    @Resource
    private BranchApi branchApi;

    @Override
    public PageResult<ColonelSalesSummaryDTO> getMonthlyColonelSalesSummary(ColonelSalesSummaryQueryDTO queryDTO) {
        PageResult<ColonelSalesSummaryDTO> result = new PageResult<>();

        // 分页参数
        int pageNo = queryDTO.getPageNo();
        int pageSize = queryDTO.getPageSize();
        int offset = (pageNo - 1) * pageSize;

        queryDTO.setPageNo(offset);
        queryDTO.setPageSize(pageSize);

        // 日期处理
        Date queryMonth = queryDTO.getMonthId();
        Calendar calendar = Calendar.getInstance();

        if (queryMonth == null) {
            throw new ServiceException("选择日期查询时，查询月份不能为空");
        }
        String currentMonthId = DateFormatUtils.format(queryMonth, "yyyyMM");
        // 获取当前月份
        String currentSystemMonthId = DateFormatUtils.format(calendar.getTime(), "yyyyMM");
        // 判断 queryMonth 是否是当前月份
        boolean isCurrentMonth = currentMonthId.equals(currentSystemMonthId);
        calendar.setTime(queryMonth);
        calendar.add(Calendar.MONTH, -1);
        String previousMonthId = DateFormatUtils.format(calendar.getTime(), "yyyyMM");

        queryDTO.setCurrentMonthId(currentMonthId);
        queryDTO.setPreviousMonthId(previousMonthId);

        // 查询总记录数
        Long total = colonelSalesSummaryMapper.countMonthlyColonelSalesSummary(queryDTO);
        if (total == 0L) {
            result.setList(Collections.emptyList());
            result.setTotal(0L);
            return result;
        }

        // 查询数据
        List<ColonelSalesSummaryDTO> data = colonelSalesSummaryMapper.getMonthlyColonelSalesSummary(queryDTO);
        coloenlSalesDataAssembly(data, currentMonthId, currentSystemMonthId);

        result.setList(data);
        result.setTotal(total);

        return result;
    }

    @Override
    public List<ColonelSalesSummaryExportDTO> monthColonelSalesSummaryExport(ColonelSalesSummaryExportPageVO reqPageVo) {

        ColonelSalesSummaryQueryDTO queryDTO = RptExportConvert.INSTANCE.convort1(reqPageVo);
        Calendar calendar = Calendar.getInstance();

        if (queryDTO.getMonthId() == null) {
            throw new ServiceException("选择日期查询时，查询月份不能为空");
        }
        String currentMonthId = DateFormatUtils.format(queryDTO.getMonthId(), "yyyyMM");
        // 获取当前月份
        String currentSystemMonthId = DateFormatUtils.format(calendar.getTime(), "yyyyMM");

        calendar.setTime(queryDTO.getMonthId());
        calendar.add(Calendar.MONTH, -1);
        String previousMonthId = DateFormatUtils.format(calendar.getTime(), "yyyyMM");

        queryDTO.setCurrentMonthId(currentMonthId);
        queryDTO.setPreviousMonthId(previousMonthId);

        // 查询数据
        List<ColonelSalesSummaryDTO> data = colonelSalesSummaryMapper.getMonthlyColonelSalesSummary(queryDTO);
        if (ToolUtil.isEmpty(data) || data.isEmpty()) {
            return new ArrayList<>();
        }

        coloenlSalesDataAssembly(data, currentMonthId, currentSystemMonthId);

        return RptExportConvert.INSTANCE.convort2(data);
    }

    /**
     * 业务员销售组装数据
     * @param data
     */
    private void coloenlSalesDataAssembly(List<ColonelSalesSummaryDTO> data, String currentMonthId, String currentSystemMonthId) {
        // 判断 queryMonth 是否是当前月份
        boolean isCurrentMonth = currentMonthId.equals(currentSystemMonthId);
        // 提取所有 colonelId
        List<String> colonelIds = data.stream()
                .map(ColonelSalesSummaryDTO::getColonelId)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询退款金额
        Map<Long, BigDecimal> refundAmountMap = afterApi.getColonelPendingRefundAmount(currentMonthId, colonelIds).getCheckedData().stream()
                .collect(Collectors.toMap(
                        map -> (Long) map.get("colonelId"),
                        map -> {
                            Double sales = (Double) map.get("refundAmount");
                            return BigDecimal.valueOf(sales);
                        },
                        BigDecimal::add
                ));
        if (ToolUtil.isNotEmpty(refundAmountMap)) {
            // 将退款金额设置到 data 对象中
            data.forEach(dto -> dto.setPendingRefundAmount(refundAmountMap.getOrDefault(Long.valueOf(dto.getColonelId()), BigDecimal.ZERO)));
        }

        // 处理管理门店数量的获取
        if (isCurrentMonth) {
            // 如果是当前月份，调用外部库或 API 获取门店数量
            List<ColonelBranchCountDTO> currentMonthBranchCountList = branchApi.getBranchCountFromOtherSource(colonelIds, currentMonthId).getCheckedData();
            Map<String, Integer> currentMonthBranchCountMap = currentMonthBranchCountList.stream()
                    .collect(Collectors.toMap(
                            ColonelBranchCountDTO::getColonelId,
                            ColonelBranchCountDTO::getBranchCount
                    ));
            // 遍历数据对象并设置门店数量及动销门店率
            data.forEach(dto -> {
                // 获取管理门店数量，默认值为 0
                Integer managedBranchCount = currentMonthBranchCountMap.getOrDefault(dto.getColonelId(), 0);
                dto.setManagedBranchCount(managedBranchCount);

                // 计算并设置动销门店率
                BigDecimal activeBranchRate = calculateActiveBranchRate(dto.getActiveBranchCount(), managedBranchCount);
                dto.setActiveBranchRate(activeBranchRate);
            });
        } else {
            // 如果是历史月份，查询 mem_colonel_branch_zip 表并进行连表查询
           List<ColonelBranchCountDTO> historicalBranchCountList = colonelSalesSummaryMapper.getBranchCountFromMemTable(colonelIds, currentMonthId);
           Map<String, Integer> historicalBranchCountMap = historicalBranchCountList.stream()
                    .collect(Collectors.toMap(
                            ColonelBranchCountDTO::getColonelId,
                            ColonelBranchCountDTO::getBranchCount
                    ));
            // 将门店数量设置到数据对象中
            data.forEach(dto -> {
                if(ToolUtil.isEmpty(historicalBranchCountMap)){
                    dto.setManagedBranchCount(0);
                    dto.setActiveBranchRate(BigDecimal.ZERO);
                }
                Integer managedBranchCount = historicalBranchCountMap.getOrDefault(dto.getColonelId(), 0);
                dto.setManagedBranchCount(managedBranchCount);
                // 计算并设置动销门店率
                BigDecimal activeBranchRate = calculateActiveBranchRate(dto.getActiveBranchCount(), managedBranchCount);
                dto.setActiveBranchRate(activeBranchRate);
            });
        }
    }

    /**
     * 计算动销门店率 (动销门店数 / 总管理门店数)，向下取整并保留四位小数
     *
     * @param activeBranchCount 动销门店数
     * @param managedBranchCount 总管理门店数
     * @return 动销门店率
     */
    private BigDecimal calculateActiveBranchRate(Integer activeBranchCount, Integer managedBranchCount) {
        if (managedBranchCount == null || managedBranchCount == 0 || activeBranchCount == null || activeBranchCount <= 0) {
            return BigDecimal.ZERO; // 没有管理门店或动销门店时，返回 0
        }
        BigDecimal activeBranchBD = BigDecimal.valueOf(activeBranchCount);
        BigDecimal managedBranchBD = BigDecimal.valueOf(managedBranchCount);

        // 保留2位小数并向下取整
        return activeBranchBD.divide(managedBranchBD, 2, RoundingMode.DOWN);
    }
}