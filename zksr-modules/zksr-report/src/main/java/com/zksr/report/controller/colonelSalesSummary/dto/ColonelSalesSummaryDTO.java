package com.zksr.report.controller.colonelSalesSummary.dto;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
/**
 * 业务员月销售汇总
 * <AUTHOR>
 * @date 2024-12-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("业务员月销售汇总 -  Response VO")
public class ColonelSalesSummaryDTO {
    @ApiModelProperty(value = "业务员编号")
    @Excel(name = "业务员编号")
    private String colonelId; // 业务员编号

    @ApiModelProperty(value = "业务员名称")
    @Excel(name = "业务员名称")
    private String colonelName; // 业务员名称

    @ApiModelProperty(value = "销售金额")
    @Excel(name = "销售金额")
    private BigDecimal totalSalesAmount; // 销售金额

    @ApiModelProperty(value = "销售数量")
    @Excel(name = "销售数量")
    private Integer totalSalesOrderCount; // 销售数量

    @ApiModelProperty(value = "管理门店")
    @Excel(name = "管理门店")
    private Integer managedBranchCount; // 管理门店

    @ApiModelProperty(value = "动销门店数")
    @Excel(name = "动销门店数")
    private Integer activeBranchCount; // 动销门店数

    @ApiModelProperty(value = "动销门店率%")
    @Excel(name = "动销门店率%")
    private BigDecimal activeBranchRate; // 动销门店率%

    @ApiModelProperty(value = "动销SKU数")
    @Excel(name = "动销SKU数")
    private Integer activeSkuCount; // 动销SKU数

    @ApiModelProperty(value = "代客下单金额")
    @Excel(name = "代客下单金额")
    private BigDecimal agentOrderAmount; // 代客下单金额

    @ApiModelProperty(value = "代客下单SKU数量")
    @Excel(name = "代客下单SKU数量")
    private Integer agentOrderSkuCount; // 代客下单SKU数量

    @ApiModelProperty(value = "自主下单金额")
    @Excel(name = "自主下单金额")
    private BigDecimal selfOrderAmount; // 自主下单金额

    @ApiModelProperty(value = "自主下单SKU数量")
    @Excel(name = "自主下单SKU数量")
    private Integer selfOrderSkuCount; // 自主下单SKU数量

    @ApiModelProperty(value = "退单金额")
    @Excel(name = "退单金额")
    private BigDecimal totalReturnAmount; // 退单金额
    @ApiModelProperty(value = "退单数量")
    @Excel(name = "退单数量")
    private Integer totalReturnOrderCount; // 退单数量

    @ApiModelProperty(value = "总销售成本")
    @Excel(name = "总销售成本")
    private BigDecimal totalSalesCost; // 总销售成本

    @ApiModelProperty(value = "总退货成本")
    @Excel(name = "总退货成本")
    private BigDecimal totalReturnCost; // 总退货成本

    @ApiModelProperty(value = "毛利金额")
    @Excel(name = "毛利金额")
    private BigDecimal grossProfitAmount; // 毛利金额

    @ApiModelProperty(value = "毛利率%")
    @Excel(name = "毛利率%")
    private BigDecimal grossProfitRate; // 毛利率%

    @ApiModelProperty(value = "优惠金额")
    @Excel(name = "优惠金额")
    private BigDecimal totalDiscountAmount; // 优惠金额

    @ApiModelProperty(value = "上月动销门店")
    @Excel(name = "上月动销门店")
    private Integer lastMonthActiveBranchCount; // 上月动销门店

    @ApiModelProperty(value = "动销门店成长比")
    @Excel(name = "动销门店成长比")
    private BigDecimal activeBranchGrowthRate; // 动销门店成长比

    @ApiModelProperty(value = "上月销售总金额")
    @Excel(name = "上月销售总金额")
    private BigDecimal lastMonthSalesAmount; // 上月销售总金额

    @ApiModelProperty(value = "销售金额成长比")
    @Excel(name = "销售金额成长比")
    private BigDecimal salesGrowthRate; // 销售金额成长比

    @ApiModelProperty(value = "上月动销SKU数")
    @Excel(name = "上月动销SKU数")
    private Integer lastMonthActiveSkuCount; // 上月动销SKU数

    @ApiModelProperty(value = "动销SKU成长比")
    @Excel(name = "动销SKU成长比")
    private BigDecimal activeSkuGrowthRate; // 动销SKU成长比

    @ApiModelProperty(value = "待处理退单金额")
    @Excel(name = "待处理退单金额")
    private BigDecimal pendingRefundAmount = BigDecimal.ZERO; // 待处理退单金额
}