package com.zksr.report.controller.branchTagMonth.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

/**
 * 门店标签月对象 ads_branch_tag_month
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Data
@ApiModel("门店标签月 - ads_branch_tag_month分页 Request VO")
public class AdsBranchTagMonthSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 月份ID */
    @ApiModelProperty(value = "频次等级标签指标值;例：11")
    private Long monthId;

    /** 平台商id */
    @ApiModelProperty(value = "频次等级标签指标值;例：11")
    private Long sysCode;

    /** 门店id */
    @ApiModelProperty(value = "频次等级标签指标值;例：11")
    private Long branchId;

    /** 客户等级标签类型;例：客户等级 */
    @Excel(name = "客户等级标签类型;例：客户等级")
    @ApiModelProperty(value = "客户等级标签类型;例：客户等级")
    private String levelTagType;

    /** 客户等级标签定义id;例：id */
    @Excel(name = "客户等级标签定义id;例：id")
    @ApiModelProperty(value = "客户等级标签定义id;例：id")
    private Long levelTagDefId;

    /** 客户等级规则;例：自然月销售金额 &gt;= 2000 元 */
    @Excel(name = "客户等级规则;例：自然月销售金额 &gt;= 2000 元")
    @ApiModelProperty(value = "客户等级规则;例：自然月销售金额 &gt;= 2000 元")
    private String levelTagRule;

    /** 客户等级标签名;例：A级 */
    @Excel(name = "客户等级标签名;例：A级")
    @ApiModelProperty(value = "客户等级标签名;例：A级")
    private String levelTagName;

    /** 客户等级标签指标值;例：2100元 */
    @Excel(name = "客户等级标签指标值;例：2100元")
    @ApiModelProperty(value = "客户等级标签指标值;例：2100元")
    private String levelTagVal;

    /** 活跃客户标签类型;例：是否活跃客户 */
    @Excel(name = "活跃客户标签类型;例：是否活跃客户")
    @ApiModelProperty(value = "活跃客户标签类型;例：是否活跃客户")
    private String activeTagType;

    /** 活跃客户标签定义id;例：id */
    @Excel(name = "活跃客户标签定义id;例：id")
    @ApiModelProperty(value = "活跃客户标签定义id;例：id")
    private Long activeTagDefId;

    /** 活跃客户规则;例：自然月销售金额 &gt;= 2000元 */
    @Excel(name = "活跃客户规则;例：自然月销售金额 &gt;= 2000元")
    @ApiModelProperty(value = "活跃客户规则;例：自然月销售金额 &gt;= 2000元")
    private String activeTagRule;

    /** 活跃客户标签名;例：是 */
    @Excel(name = "活跃客户标签名;例：是")
    @ApiModelProperty(value = "活跃客户标签名;例：是")
    private String activeTagName;

    /** 活跃客户标签指标值;例：2100元 */
    @Excel(name = "活跃客户标签指标值;例：2100元")
    @ApiModelProperty(value = "活跃客户标签指标值;例：2100元")
    private String activeTagVal;

    /** 类占等级标签类型;例：类占等级 */
    @Excel(name = "类占等级标签类型;例：类占等级")
    @ApiModelProperty(value = "类占等级标签类型;例：类占等级")
    private String lzLevelTagType;

    /** 类占等级标签定义id;例：id */
    @Excel(name = "类占等级标签定义id;例：id")
    @ApiModelProperty(value = "类占等级标签定义id;例：id")
    private Long lzLevelTagDefId;

    /** 类占等级规则;例：高类占比率 &gt; 50% */
    @Excel(name = "类占等级规则;例：高类占比率 &gt; 50%")
    @ApiModelProperty(value = "类占等级规则;例：高类占比率 &gt; 50%")
    private String lzLevelTagRule;

    /** 类占等级标签名;例：高类占 */
    @Excel(name = "类占等级标签名;例：高类占")
    @ApiModelProperty(value = "类占等级标签名;例：高类占")
    private String lzLevelTagName;

    /** 类占等级标签指标值;例：60% */
    @Excel(name = "类占等级标签指标值;例：60%")
    @ApiModelProperty(value = "类占等级标签指标值;例：60%")
    private String lzLevelTagVal;

    /** 毛利等级标签类型;例：毛利等级 */
    @Excel(name = "毛利等级标签类型;例：毛利等级")
    @ApiModelProperty(value = "毛利等级标签类型;例：毛利等级")
    private String profitLevelTagType;

    /** 毛利等级标签定义id;例：id */
    @Excel(name = "毛利等级标签定义id;例：id")
    @ApiModelProperty(value = "毛利等级标签定义id;例：id")
    private Long profitLevelTagDefId;

    /** 毛利等级规则;例：毛利率 &gt; 10% */
    @Excel(name = "毛利等级规则;例：毛利率 &gt; 10%")
    @ApiModelProperty(value = "毛利等级规则;例：毛利率 &gt; 10%")
    private String profitLevelTagRule;

    /** 毛利等级标签名;例：高毛利 */
    @Excel(name = "毛利等级标签名;例：高毛利")
    @ApiModelProperty(value = "毛利等级标签名;例：高毛利")
    private String profitLevelTagName;

    /** 毛利等级标签指标值;例：10.88% */
    @Excel(name = "毛利等级标签指标值;例：10.88%")
    @ApiModelProperty(value = "毛利等级标签指标值;例：10.88%")
    private String profitLevelTagVal;

    /** 频次等级标签类型;例：频次等级 */
    @Excel(name = "频次等级标签类型;例：频次等级")
    @ApiModelProperty(value = "频次等级标签类型;例：频次等级")
    private String freqLevelTagType;

    /** 频次等级标签定义id;例：id */
    @Excel(name = "频次等级标签定义id;例：id")
    @ApiModelProperty(value = "频次等级标签定义id;例：id")
    private Long freqLevelTagDefId;

    /** 频次等级规则;例：月订货天数 &gt; 10天 */
    @Excel(name = "频次等级规则;例：月订货天数 &gt; 10天")
    @ApiModelProperty(value = "频次等级规则;例：月订货天数 &gt; 10天")
    private String freqLevelTagRule;

    /** 频次等级标签名;例：高频次 */
    @Excel(name = "频次等级标签名;例：高频次")
    @ApiModelProperty(value = "频次等级标签名;例：高频次")
    private String freqLevelTagName;

    /** 频次等级标签指标值;例：11 */
    @Excel(name = "频次等级标签指标值;例：11")
    @ApiModelProperty(value = "频次等级标签指标值;例：11")
    private String freqLevelTagVal;

    /** 删除标志（0代表存在 2代表删除） */
    @ApiModelProperty(value = "频次等级标签指标值;例：11")
    private String delFlag;

}
