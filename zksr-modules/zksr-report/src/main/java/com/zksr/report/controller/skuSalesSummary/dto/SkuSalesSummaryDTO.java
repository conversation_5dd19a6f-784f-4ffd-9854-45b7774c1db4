package com.zksr.report.controller.skuSalesSummary.dto;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 商品月销售汇总
 * <AUTHOR>
 * @date 2024-12-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("商品月销售汇总 -  Response VO")
public class SkuSalesSummaryDTO extends PageParam{
    @ApiModelProperty(value = "商品SKUID")
    @Excel(name = "商品SKUID")
    private String skuId;               // 商品SKUID

    @ApiModelProperty(value = "商品名称")
    @Excel(name = "商品名称")
    private String skuName;           // 商品名称

    @ApiModelProperty(value = "商品编号")
    @Excel(name = "商品编号")
    private String skuNo;           // 商品编号

    @ApiModelProperty(value = "产品条码")
    @Excel(name = "产品条码")
    private String barCode;           // 产品条码

    @ApiModelProperty(value = "属性")
    @Excel(name = "属性")
    private String properties;        // 属性

    @ApiModelProperty(value = "单位")
    @Excel(name = "单位")
    private String unit;              // 单位

    @ApiModelProperty(value = "一级类别")
    @Excel(name = "一级类别")
    private String category1;          // 一级类别

    @ApiModelProperty(value = "二级类别")
    @Excel(name = "二级类别")
    private String category2;          // 二级类别

    @ApiModelProperty(value = "三级类别")
    @Excel(name = "三级类别")
    private String category3;          // 三级类别

    @ApiModelProperty(value = "品牌")
    @Excel(name = "品牌")
    private String brand;             // 品牌

    @ApiModelProperty(value = "入驻商编号")
    @Excel(name = "入驻商编号")
    private String supplierId;            // 入驻商编号

    @ApiModelProperty(value = "所属入驻商名称")
    @Excel(name = "所属入驻商")
    private String supplierName;        // 所属入驻商

    @ApiModelProperty(value = "销售数量")
    @Excel(name = "销售数量")
    private Long salesQuantity;       // 销售数量

    @ApiModelProperty(value = "销售金额")
    @Excel(name = "销售金额")
    private BigDecimal salesAmount;   // 销售金额

    @ApiModelProperty(value = "退单金额")
    @Excel(name = "退单金额")
    private BigDecimal refundAmount;  // 退单金额

    @ApiModelProperty(value = "退单数量")
    @Excel(name = "退单数量")
    private Long refundQuantity;      // 退单数量

    @ApiModelProperty(value = "总销售成本金额")
    @Excel(name = "总销售成本金额")
    private BigDecimal totalCost;     // 总销售成本金额

    @ApiModelProperty(value = "总退货成本金额")
    @Excel(name = "总退货成本金额")
    private BigDecimal totalRefundCost;// 总退货成本金额

    @ApiModelProperty(value = "毛利金额")
    @Excel(name = "毛利金额")
    private BigDecimal grossProfit;   // 毛利金额

    @ApiModelProperty(value = "毛利率%")
    @Excel(name = "毛利率%")
    private BigDecimal grossProfitRate;// 毛利率%

    @ApiModelProperty(value = "动销门店数")
    @Excel(name = "动销门店数")
    private Long activeBranchCount;    // 动销门店数

    @ApiModelProperty(value = "优惠金额")
    @Excel(name = "优惠金额")
    private BigDecimal discountAmount;// 优惠金额
}