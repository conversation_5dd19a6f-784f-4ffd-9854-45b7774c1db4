package com.zksr.report.api.homePages;

import com.zksr.report.service.IHomePageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class RptHomePagesApiImpl implements  RptHomePagesApi{

    @Autowired
    private IHomePageService homePageService;
    @Override
    public void refreshEsHomePagesDataJob(Long sysCode) {
        homePageService.refreshEsHomePagesDataJob(sysCode);
    }

    @Override
    public void refreshEsHomePagesMonthDataJob(Long sysCode, String startDate, String endDate) {
        homePageService.refreshEsHomePagesMonthDataJob(sysCode, startDate, endDate);
    }
}
