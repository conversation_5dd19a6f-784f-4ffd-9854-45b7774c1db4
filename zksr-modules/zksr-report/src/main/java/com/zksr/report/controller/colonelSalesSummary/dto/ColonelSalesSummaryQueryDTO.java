package com.zksr.report.controller.colonelSalesSummary.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
@ApiModel("业务员月销售汇总 -  Request VO")
public class ColonelSalesSummaryQueryDTO extends PageParam {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "选择查询月份，格式为 yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM")
    private Date monthId; // 查询月份，格式为 yyyy-MM

    @ApiModelProperty(value = "业务员编号")
    private String colonelId; // 业务员编号

    @ApiModelProperty(value = "业务员名称")
    private String colonelName; // 业务员名称

    private String currentMonthId; // 当前月份ID

    private String previousMonthId; // 上月月份ID

    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;
}