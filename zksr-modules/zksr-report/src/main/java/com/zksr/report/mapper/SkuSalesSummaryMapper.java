package com.zksr.report.mapper;

import com.zksr.report.controller.skuSalesSummary.dto.SkuSalesSummaryDTO;
import com.zksr.report.controller.skuSalesSummary.dto.SkuSalesSummaryQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SkuSalesSummaryMapper {
    List<SkuSalesSummaryDTO> getMonthlySkuSalesSummary(SkuSalesSummaryQueryDTO queryDTO);

    Long countMonthlySkuSalesSummary(SkuSalesSummaryQueryDTO queryDTO);
}