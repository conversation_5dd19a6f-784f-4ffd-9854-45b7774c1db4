package com.zksr.report.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.domain.AdsBranchTradeWeekdayDistribution;
import com.zksr.report.controller.branchTradeWeekday.vo.AdsBranchTradeWeekdayDistributionPageReqVO;


/**
 * 门店周下单分布月Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Mapper
public interface AdsBranchTradeWeekdayDistributionMapper extends BaseMapperX<AdsBranchTradeWeekdayDistribution> {
    default PageResult<AdsBranchTradeWeekdayDistribution> selectPage(AdsBranchTradeWeekdayDistributionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AdsBranchTradeWeekdayDistribution>()
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getMonthId, reqVO.getMonthId())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getAreaId, reqVO.getAreaId())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getMondayDxBranchQty, reqVO.getMondayDxBranchQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getMondayOrderQty, reqVO.getMondayOrderQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getMondayOrderAmt, reqVO.getMondayOrderAmt())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getTuesdayDxBranchQty, reqVO.getTuesdayDxBranchQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getTuesdayOrderQty, reqVO.getTuesdayOrderQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getTuesdayOrderAmt, reqVO.getTuesdayOrderAmt())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getWednesdayDxBranchQty, reqVO.getWednesdayDxBranchQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getWednesdayOrderQty, reqVO.getWednesdayOrderQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getWednesdayOrderAmt, reqVO.getWednesdayOrderAmt())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getThursdayDxBranchQty, reqVO.getThursdayDxBranchQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getThursdayOrderQty, reqVO.getThursdayOrderQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getThursdayOrderAmt, reqVO.getThursdayOrderAmt())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getFridayDxBranchQty, reqVO.getFridayDxBranchQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getFridayOrderQty, reqVO.getFridayOrderQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getFridayOrderAmt, reqVO.getFridayOrderAmt())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getSaturdayDxBranchQty, reqVO.getSaturdayDxBranchQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getSaturdayOrderQty, reqVO.getSaturdayOrderQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getSaturdayOrderAmt, reqVO.getSaturdayOrderAmt())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getSundayDxBranchQty, reqVO.getSundayDxBranchQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getSundayOrderQty, reqVO.getSundayOrderQty())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getSundayOrderAmt, reqVO.getSundayOrderAmt())
                    .eqIfPresent(AdsBranchTradeWeekdayDistribution::getInsertDateId, reqVO.getInsertDateId())
                .orderByDesc(AdsBranchTradeWeekdayDistribution::getMonthId));
    }

    default AdsBranchTradeWeekdayDistribution selectBranchByMonthId(Long branchId, Integer monthId) {
        return selectOne(new LambdaQueryWrapperX<AdsBranchTradeWeekdayDistribution>()
                .eqIfPresent(AdsBranchTradeWeekdayDistribution::getMonthId, monthId)
                .eqIfPresent(AdsBranchTradeWeekdayDistribution::getBranchId, branchId)
        );
    }
}
