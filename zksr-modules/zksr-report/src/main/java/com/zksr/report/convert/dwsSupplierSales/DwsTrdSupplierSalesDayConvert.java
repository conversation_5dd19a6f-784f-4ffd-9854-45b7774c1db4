package com.zksr.report.convert.dwsSupplierSales;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.domain.DwsTrdSupplierSalesDay;
import com.zksr.report.controller.dwsSupplierSales.dto.DwsTrdSupplierSalesDayRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 交易域入驻商粒度下单日汇总 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-19
*/
@Mapper
public interface DwsTrdSupplierSalesDayConvert {

    DwsTrdSupplierSalesDayConvert INSTANCE = Mappers.getMapper(DwsTrdSupplierSalesDayConvert.class);

    DwsTrdSupplierSalesDayRespDTO convert(DwsTrdSupplierSalesDay dwsTrdSupplierSalesDay);


    PageResult<DwsTrdSupplierSalesDayRespDTO> convertPage(PageResult<DwsTrdSupplierSalesDay> dwsTrdSupplierSalesDayPage);
}