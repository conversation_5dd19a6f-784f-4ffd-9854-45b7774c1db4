package com.zksr.report.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.report.api.branch.vo.RptColonelBranchReqVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.domain.DwsTrdBranchSalesMonth;
import com.zksr.report.controller.branchSaleMonth.vo.DwsTrdBranchSalesMonthPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;


/**
 * 交易域门店粒度下单月汇总Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Mapper
public interface DwsTrdBranchSalesMonthMapper extends BaseMapperX<DwsTrdBranchSalesMonth> {
    default PageResult<DwsTrdBranchSalesMonth> selectPage(DwsTrdBranchSalesMonthPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DwsTrdBranchSalesMonth>()
                    .eqIfPresent(DwsTrdBranchSalesMonth::getMonthId, reqVO.getMonthId())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getAreaId, reqVO.getAreaId())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getOrderAmt, reqVO.getOrderAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getOrderQty, reqVO.getOrderQty())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getDxSkuQty, reqVO.getDxSkuQty())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getDxSpuQty, reqVO.getDxSpuQty())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getDxBranchQty, reqVO.getDxBranchQty())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getSupplierCostAmt, reqVO.getSupplierCostAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getProfitAmt, reqVO.getProfitAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getProfitPartnerAmt, reqVO.getProfitPartnerAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getProfitDcAmt, reqVO.getProfitDcAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getProfitColonelAmt, reqVO.getProfitColonelAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getProfitColonel1Amt, reqVO.getProfitColonel1Amt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getProfitColonel2Amt, reqVO.getProfitColonel2Amt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getPreOrderAmt, reqVO.getPreOrderAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getActivityDiscountAmt, reqVO.getActivityDiscountAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getCouponDiscountAmt, reqVO.getCouponDiscountAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getCouponDiscountAmt2, reqVO.getCouponDiscountAmt2())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getDiscountAmt, reqVO.getDiscountAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getProfitRate, reqVO.getProfitRate())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getDxDays, reqVO.getDxDays())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnAmt, reqVO.getReturnAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnQty, reqVO.getReturnQty())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnSkuQty, reqVO.getReturnSkuQty())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnSpuQty, reqVO.getReturnSpuQty())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnBranchQty, reqVO.getReturnBranchQty())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnSupplierCostAmt, reqVO.getReturnSupplierCostAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnProfitAmt, reqVO.getReturnProfitAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnProfitPartnerAmt, reqVO.getReturnProfitPartnerAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnProfitDcAmt, reqVO.getReturnProfitDcAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnProfitColonelAmt, reqVO.getReturnProfitColonelAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnProfitColonel1Amt, reqVO.getReturnProfitColonel1Amt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnProfitColonel2Amt, reqVO.getReturnProfitColonel2Amt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getPreReturnAmt, reqVO.getPreReturnAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnActivityDiscountAmt, reqVO.getReturnActivityDiscountAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnCouponDiscountAmt, reqVO.getReturnCouponDiscountAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnCouponDiscountAmt2, reqVO.getReturnCouponDiscountAmt2())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnDiscountAmt, reqVO.getReturnDiscountAmt())
                    .eqIfPresent(DwsTrdBranchSalesMonth::getReturnProfitRate, reqVO.getReturnProfitRate())
                .orderByDesc(DwsTrdBranchSalesMonth::getMonthId));
    }

    BigDecimal selectTotalColonelBranchSale(@Param("reqVO") RptColonelBranchReqVO reqVO);
}
