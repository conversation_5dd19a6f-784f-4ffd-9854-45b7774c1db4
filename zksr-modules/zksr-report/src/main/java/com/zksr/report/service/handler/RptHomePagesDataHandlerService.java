package com.zksr.report.service.handler;

import com.zksr.report.api.homePages.vo.HomePagesReqVO;

/**
 * <AUTHOR>
 * @date 2024年12月26日 10:40
 * @description: PC 首页报表统计Service
 */
public interface RptHomePagesDataHandlerService {

    /**
     * PC首页看板当前销售、欠款、退单实时数据
     */
    int CURRENT_SALES_DATA = 1;

    /**
     * 订单销售数据
     */
    int ORDER_SALES_DATA = 2;
    /**
     * 门店变动数据
     */
    int BRANCH_CHANGE_DATA = 3;

    /**
     * 订单退货数据
     */
    int AFTER_SALES_DATA = 4;

    /**
     * sku上架数据
     */
    int SKU_SHELF_DATA = 5;
    /**
     * 区域销售TOP10数据
     */
    int AREA_SALES_TOP10_DATA = 6;
    /**
     * 一级品类销售TOP10数据
     */
    int CATEGORY_SALES_TOP10_DATA = 7;

    /**
     * 运营商销售TOP10数据
     */
    int DC_SALES_TOP10_DATA = 8;

    /**
     * 商品销售TOP10数据
     */
    int ITEM_SALES_TOP10_DATA = 9;

    /**
     * 入驻商销售TOP10数据
     */
    int SUPPLIER_SALES_TOP10_DATA = 10;

    /**
     * 业务员销售TOP10数据
     */
    int COLONEL_SALES_TOP10_DATA = 11;

    /**
     * 门店销售TOP10数据
     */
    int BRANCH_SALES_TOP10_DATA = 12;



    default void dayDataStatistics(HomePagesReqVO reqVO) {}

    default void monthDataStatistics(HomePagesReqVO reqVO){}
}
