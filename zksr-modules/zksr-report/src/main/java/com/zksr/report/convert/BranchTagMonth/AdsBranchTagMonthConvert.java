package com.zksr.report.convert.BranchTagMonth;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.api.branch.vo.ColonelLevelTotalRespVO;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.api.branch.vo.AdsBranchTagMonthRespVO;
import com.zksr.report.controller.branchTagMonth.vo.AdsBranchTagMonthSaveReqVO;
import com.zksr.report.domain.vo.BranchTagTotalVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
* 门店标签月 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-13
*/
@Mapper
public interface AdsBranchTagMonthConvert {

    AdsBranchTagMonthConvert INSTANCE = Mappers.getMapper(AdsBranchTagMonthConvert.class);

    AdsBranchTagMonthRespVO convert(AdsBranchTagMonth adsBranchTagMonth);

    AdsBranchTagMonth convert(AdsBranchTagMonthSaveReqVO adsBranchTagMonthSaveReq);

    PageResult<AdsBranchTagMonthRespVO> convertPage(PageResult<AdsBranchTagMonth> adsBranchTagMonthPage);

    @Mappings({
            @Mapping(source = "cnt", target = "tagTotal"),
            @Mapping(source = "branchCnt", target = "branchNum")
    })
    ColonelLevelTotalRespVO.ColonelTotal convertColonelTotal(BranchTagTotalVO tagTotalVO);
}