package com.zksr.report.slot;

import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.report.api.branch.dto.BranchTagConfigDTO;
import com.zksr.report.domain.AdsBranchTagMonth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/13 14:22
 */
@Data
@ApiModel(description = "门店标签计算上下文")
public class BranchTagContext {

    @ApiModelProperty("计算月份,yyyyMM")
    private Long monthId;

    @ApiModelProperty("标签配置")
    private BranchTagConfigDTO tagConfig;

    @ApiModelProperty("一组门店")
    private List<BranchDTO> branchList;

    @ApiModelProperty("门店IDmap")
    private Map<Long, BranchDTO> branchMap = new HashMap<>();

    @ApiModelProperty("门店月度标签")
    private Map<Long, AdsBranchTagMonth> tagMonthMap = new HashMap<>();

    public void setBranchList(List<BranchDTO> branchList, Long monthId) {
        this.branchList = branchList;
        for (BranchDTO branchDTO : branchList) {
            tagMonthMap.put(
                    branchDTO.getBranchId(),
                    AdsBranchTagMonth.builder()
                            .monthId(monthId)
                            .sysCode(branchDTO.getSysCode())
                            .areaId(branchDTO.getAreaId())
                            .branchId(branchDTO.getBranchId())
                            .build()
                    );
            branchMap.put(branchDTO.getBranchId(), branchDTO);
        }
    }
}
