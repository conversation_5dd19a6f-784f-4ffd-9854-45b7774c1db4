package com.zksr.report.convert.homeReport;

import com.zksr.common.elasticsearch.domain.*;
import com.zksr.report.api.homePages.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface HomeReportConvert {
    HomeReportConvert INSTANCE =  Mappers.getMapper(HomeReportConvert.class);

    List<EsHomePagesData> convortEsHomePagesData(List<HomePagesCurrentSalesDataRespDTO> dataList);

    HomePagesCurrentSalesDataRespDTO convortHomePagesCurrentSalesData(EsHomePagesData data);

    List<EsHomePagesOrderSalesData> convortEsHomePagesOrderSalesData(List<HomePagesOrderSalesDataRespDTO> dataList);

    HomePagesOrderSalesDataRespDTO convortHomePagesOrderSalesData0(EsHomePagesOrderSalesData data);

    List<EsHomePagesOrderAfterData> convortEsHomePagesOrderAfterData(List<HomePagesOrderAfterDataRespDTO> dataList);

    HomePagesOrderAfterDataRespDTO convortHomePagesOrderAfterData0(EsHomePagesOrderAfterData data);

    List<EsHomePagesBranchData> convortEsHomePagesBranchData(List<HomePagesBranchDataRespDTO> dataList);

    HomePagesBranchDataRespDTO convortHomePagesBranchData0(EsHomePagesBranchData data);

    List<EsHomePagesSkuData> convortEsHomePagesSkuData(List<HomePagesSkuDataRespDTO> dataList);

    HomePagesSkuDataRespDTO convortHomePagesSkuData0(EsHomePagesSkuData data);

    List<EsHomePagesSalesTop10Data> convortEsHomePagesSalesTop10Data(List<HomePagesSalesTop10DataRespDTO> dataList);

    List<HomePagesSalesTop10DataRespDTO> convortHomePagesSalesTop10Data0(List<EsHomePagesSalesTop10Data> dataList);

}
