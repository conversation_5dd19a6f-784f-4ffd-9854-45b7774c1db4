package com.zksr.report.service.impl.handler;

import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.elasticsearch.domain.EsHomePagesData;
import com.zksr.common.elasticsearch.domain.EsHomePagesOrderSalesData;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;
import com.zksr.common.elasticsearch.service.EsHomePagesDataService;
import com.zksr.common.elasticsearch.service.EsHomePagesOrderSalesDataService;
import com.zksr.report.api.homePages.dto.HomePagesCurrentSalesDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.report.convert.homeReport.HomeReportConvert;
import com.zksr.report.service.handler.RptHomePagesDataHandlerService;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年012月26日 10:33
 * @description: PC首页看板当前销售、欠款、退单实时数据
 */
@Component
@Order(RptHomePagesDataHandlerService.CURRENT_SALES_DATA)
@Slf4j
@SuppressWarnings("all")
public class RptHomePagesCurrentSalesDataHandlerServiceImpl implements RptHomePagesDataHandlerService {
    @Autowired
    private OrderApi orderApi;

    @Autowired
    private EsHomePagesDataService esHomePagesDataService;
    @Autowired
    private EsHomePagesOrderSalesDataService esHomePagesOrderSalesDataService;
    @Override
    public void dayDataStatistics(HomePagesReqVO reqVO) {
        // 查询平台商的销售数据
        reqVO.setIsSupplier(NumberPool.INT_ZERO)
                .setIsDc(NumberPool.INT_ZERO);
        List<HomePagesCurrentSalesDataRespDTO> dataPartnerList = orderApi.getHomePagesCurrentSalesData(reqVO).getCheckedData();

        // 查询平台商、运营商的销售数据
        reqVO.setIsDc(NumberPool.INT_ONE)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesCurrentSalesDataRespDTO> dataDcList = orderApi.getHomePagesCurrentSalesData(reqVO).getCheckedData();

        // 查询平台商、入驻商的销售数据
        reqVO.setIsSupplier(NumberPool.INT_ONE)
                .setIsDc(NumberPool.INT_ZERO);
        List<HomePagesCurrentSalesDataRespDTO> dataSupplierList = orderApi.getHomePagesCurrentSalesData(reqVO).getCheckedData();

        List<HomePagesCurrentSalesDataRespDTO> mergedList = new ArrayList<>();
        mergedList.addAll(dataPartnerList);
        mergedList.addAll(dataDcList);
        mergedList.addAll(dataSupplierList);

        List<EsHomePagesData> esHomePagesData = HomeReportConvert.INSTANCE.convortEsHomePagesData(mergedList);
        esHomePagesData.forEach(data -> {
            data.setDateId(reqVO.getDateId())
                    .setRefreshEsTime(DateUtils.getNowDate())
                    .setId(StringUtils.format("{}_{}_{}_{}", reqVO.getDateId(), reqVO.getSysCode(), ToolUtil.isEmptyReturn(data.getDcId(), 1L), ToolUtil.isEmptyReturn(data.getSupplierId(), 1L)))
                    .setOrderAmtRate(data.getTodayOrderTotalAmt().subtract(data.getYesterdayOrderTotalAmt())
                            .divide(ToolUtil.isEmptyOrZeroReturn(data.getYesterdayOrderTotalAmt(), BigDecimal.ONE), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100))
                    )
            ;

//             获取当前月的销售数据 这里先注释掉，前端页面需要渲染
//            HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
//            searchDTO.setId(StringUtils.format("{}_{}_{}_{}", reqVO.getDateId().substring(0, 6), reqVO.getSysCode(), ToolUtil.isEmptyReturn(data.getDcId(), 1L), ToolUtil.isEmptyReturn(data.getSupplierId(), 1L)));
//            EsHomePagesOrderSalesData orderData = esHomePagesOrderSalesDataService.searchHomePagesOrderSalesData(searchDTO);
//            if (ToolUtil.isNotEmpty(orderData)) {
//                data.setMonthOrderAmt(orderData.getOrderAmt())
//                        .setBeforeMonthOrderAmt(orderData.getBeforeOrderAmt())
//                        .setMonthOrderRate(
//                                orderData.getOrderAmt().subtract(orderData.getBeforeOrderAmt())
//                                        .divide(ToolUtil.isEmptyOrZeroReturn(orderData.getBeforeOrderAmt(), BigDecimal.ONE), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
//                        )
//                ;
//            }
        });
        // ES批量保存
        esHomePagesDataService.saveBatchHomePagesData(esHomePagesData);
    }
}
