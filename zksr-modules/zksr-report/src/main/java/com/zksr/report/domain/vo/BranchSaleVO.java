package com.zksr.report.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店销售统计
 * @date 2024/11/14 9:20
 */
@Data
public class BranchSaleVO {

    @ApiModelProperty("门店ID")
    private Long branchId;

    @ApiModelProperty("利润")
    private BigDecimal profit;

    @ApiModelProperty("销售额")
    private BigDecimal saleAmt;
}
