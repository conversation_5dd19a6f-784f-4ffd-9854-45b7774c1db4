package com.zksr.report.service;

import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.report.api.branch.dto.BranchTagConfigDTO;
import com.zksr.report.controller.schedule.vo.ComputeBranchTagReqVO;
import com.zksr.report.slot.BranchTagContext;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/11/13
 * @desc
 */
public interface IScheduleService {

    /**
     * 计算门店标签
     * @param computeBranchTagReqVO 计算请求
     */
    void computeBranchTag(ComputeBranchTagReqVO computeBranchTagReqVO);

    /**
     * 执行标签计算
     * @param monthId       月份yyyyMM
     * @param branchDTO     门店
     */
    void executeTagProcess(Long monthId, BranchDTO branchDTO);

    /**
     * 执行标签计算
     * @param monthId       月份yyyyMM
     * @param branchDTOList 门店列表
     * @param config        标签配置
     */
    void executeTagProcess(Long monthId, List<BranchDTO> branchDTOList, BranchTagConfigDTO config);

    /**
     * 执行标签计算, 通过上下文
     * @param context   上下文
     */
    void executeTagProcess(BranchTagContext context);
}
