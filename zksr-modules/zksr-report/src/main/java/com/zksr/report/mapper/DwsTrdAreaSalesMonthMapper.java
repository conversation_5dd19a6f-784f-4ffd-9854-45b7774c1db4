package com.zksr.report.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.report.controller.dwsAreaSales.dto.DwsTrdAreaSalesMonthRespDTO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.domain.DwsTrdAreaSalesMonth;
import com.zksr.report.controller.dwsAreaSales.vo.DwsTrdAreaSalesMonthPageReqVO;

import java.util.List;


/**
 * 交易域区域城市粒度下单月汇总Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Mapper
public interface DwsTrdAreaSalesMonthMapper extends BaseMapperX<DwsTrdAreaSalesMonth> {

    /**
     * 交易域区域粒度月销售分页列表查询
     * @param pageReqVO
     * @return
     */
    List<DwsTrdAreaSalesMonthRespDTO> getAreaMonthSalesPage(DwsTrdAreaSalesMonthPageReqVO pageReqVO);
}
