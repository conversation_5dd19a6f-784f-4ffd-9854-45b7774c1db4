package com.zksr.report.controller.dwsSupplierSales;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.report.service.IDwsTrdSupplierSalesMonthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.report.controller.dwsSupplierSales.vo.DwsTrdSupplierSalesMonthPageReqVO;
import com.zksr.report.controller.dwsSupplierSales.dto.DwsTrdSupplierSalesMonthRespDTO;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 交易域入驻商粒度下单月汇总Controller
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Api(tags = "管理后台 - 交易域入驻商粒度下单月汇总接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/dwsSupplierSalesMonth")
public class DwsTrdSupplierSalesMonthController {
    @Autowired
    private IDwsTrdSupplierSalesMonthService dwsTrdSupplierSalesMonthService;


    /**
     * 分页查询交易域入驻商粒度下单月汇总
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得交易域入驻商粒度下单月汇总分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<DwsTrdSupplierSalesMonthRespDTO>> getPage(@Valid DwsTrdSupplierSalesMonthPageReqVO pageReqVO) {
        return success(dwsTrdSupplierSalesMonthService.getDwsTrdSupplierSalesMonthPage(pageReqVO));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "report:month:add";
        /** 编辑 */
        public static final String EDIT = "report:month:edit";
        /** 删除 */
        public static final String DELETE = "report:month:remove";
        /** 列表 */
        public static final String LIST = "report:month:list";
        /** 查询 */
        public static final String GET = "report:month:query";
        /** 停用 */
        public static final String DISABLE = "report:month:disable";
        /** 启用 */
        public static final String ENABLE = "report:month:enable";
    }
}
