package com.zksr.report.component.branchTag;

import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.report.api.branch.dto.BranchLevelConfigDTO;
import com.zksr.report.api.branch.dto.BranchTagConfigDTO;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.domain.vo.BranchSaleVO;
import com.zksr.report.domain.vo.TagNodeComponent;
import com.zksr.report.slot.BranchTagContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店类占比
 * @date 2024/11/14 9:05
 */
@Slf4j
@Component("branchTagClassCmp")
public class BranchTagClassCmp extends TagNodeComponent {

    @Override
    public void process() throws Exception {
        BranchTagContext context = this.getContext();
        // 标签配置
        BranchTagConfigDTO tagConfig = context.getTagConfig();
        if (Objects.isNull(tagConfig.getSaleClassConfig())) {
            return;
        }

        // 获取门店ID集合
        List<Long> branchIds = context.getBranchList().stream().map(BranchDTO::getBranchId).collect(Collectors.toList());

        // 获取核算分类
        List<String> catgoryList = tagConfig.getSaleClassConfig().getCatgoryList();
        if (Objects.isNull(catgoryList) || catgoryList.isEmpty()) {
            log.info("计算类占比标签, 但是没有配置分类");
            return;
        }
        // 查询销售数据
        List<BranchSaleVO> saleList = rptTagDefMapper.selectByMonthSale(context.getMonthId(), branchIds);
        Map<Long, BranchSaleVO> saleMap = saleList.stream().collect(Collectors.toMap(BranchSaleVO::getBranchId, item -> item));
        // 关联分类销售数据
        List<BranchSaleVO> cateSaleList = rptTagDefMapper.selectByMonthClassSale(context.getMonthId(), branchIds, catgoryList);
        Map<Long, BranchSaleVO> cateSaleMap = cateSaleList.stream().collect(Collectors.toMap(BranchSaleVO::getBranchId, item -> item));

        // 排序, 从顶级依次向下处理
        tagConfig.getSaleClassConfig().getRuleList().sort(Comparator.comparing(BranchLevelConfigDTO::getSort));

        for (Long branchId : branchIds) {
            BigDecimal odd = BigDecimal.ZERO;
            if (saleMap.containsKey(branchId) && cateSaleMap.containsKey(branchId)) {
                BigDecimal saleAmt = saleMap.get(branchId).getSaleAmt();
                BigDecimal cateSaleAmt = cateSaleMap.get(branchId).getSaleAmt();
                if (NumberUtil.isGreater(saleAmt, BigDecimal.ZERO) && NumberUtil.isGreater(cateSaleAmt, BigDecimal.ZERO)) {
                    // 计算出比例
                    // 管理分类销售金额 在总销售金额中的占比
                    odd = cateSaleAmt.divide(saleAmt, 2, RoundingMode.HALF_UP);
                }
            }

            // 已经获取到了销售额, 开始定级
            for (BranchLevelConfigDTO configDTO : tagConfig.getSaleClassConfig().getRuleList()) {
                // 验证规则
                if (this.condition(configDTO, odd)) {
                    AdsBranchTagMonth adsBranchTagMonth = context.getTagMonthMap().get(branchId);
                    BranchTagEnum branchTag = configDTO.getBranchTag();
                    // 类占等级标签类型;例：类占等级
                    adsBranchTagMonth.setLzLevelTagType(branchTag.getTag());
                    // 类占等级标签定义id;例：id
                    adsBranchTagMonth.setLzLevelTagDefId(configDTO.getTagDefId());
                    // 类占等级规则;例：高类占比率 > 50%
                    if (configDTO.getCondition().isOne()) {
                        adsBranchTagMonth.setLzLevelTagRule(
                                StringUtils.format("类占比{}{}%",
                                    configDTO.getCondition().getCondition(),
                                    configDTO.getValA().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP)
                                )
                        );
                    } else {
                        adsBranchTagMonth.setLzLevelTagRule(
                                StringUtils.format("类占比{}",
                                configDTO.getCondition().getCondition()
                                        .replace("A", configDTO.getValA().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).toString() + "%")
                                        //.replace("X", odd.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).toString())
                                        .replace("B", configDTO.getValB().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).toString()) + "%")
                        );
                    }
                    // 类占等级标签名;例：高类占
                    adsBranchTagMonth.setLzLevelTagName(branchTag.getName());
                    // 类占等级标签指标值;例：60%
                    adsBranchTagMonth.setLzLevelTagVal(StringUtils.format("{}%", odd.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).toString()));
                    // 跳到下一个门店
                    break;
                }
            }
        }
    }
}
