package com.zksr.report.service.impl.handler;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.elasticsearch.domain.EsHomePagesOrderSalesData;
import com.zksr.common.elasticsearch.service.EsHomePagesOrderSalesDataService;
import com.zksr.report.api.homePages.dto.HomePagesOrderSalesDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.report.convert.homeReport.HomeReportConvert;
import com.zksr.report.mapper.HomePagesReportMapper;
import com.zksr.report.service.handler.RptHomePagesDataHandlerService;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月26日 10:33
 * @description: PC首页 PC首页订单销售数据
 */
@Component
@Order(RptHomePagesDataHandlerService.ORDER_SALES_DATA)
@Slf4j
@SuppressWarnings("all")
public class RptHomePagesOrderDataHandlerServiceImpl implements RptHomePagesDataHandlerService {
    @Autowired
    private OrderApi orderApi;
    @Autowired
    private HomePagesReportMapper homePagesReportMapper;

    @Autowired
    private EsHomePagesOrderSalesDataService esHomePagesOrderSalesDataService;
    @Override
    public void dayDataStatistics(HomePagesReqVO reqVO) {
        // 查询平台商数据
        reqVO.setIsDc(NumberPool.INT_ZERO)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesOrderSalesDataRespDTO> dataPartnerList = orderApi.getHomePagesOrderSalesData(reqVO).getCheckedData();

        // 查询平台商、所有运营商的数据
        reqVO.setIsDc(NumberPool.INT_ONE)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesOrderSalesDataRespDTO> dataDcList = orderApi.getHomePagesOrderSalesData(reqVO).getCheckedData();

        // 查询平台商、所有入驻商的数据
        reqVO.setIsSupplier(NumberPool.INT_ONE)
                .setIsDc(NumberPool.INT_ZERO);
        List<HomePagesOrderSalesDataRespDTO> dataSupplierList = orderApi.getHomePagesOrderSalesData(reqVO).getCheckedData();


        List<HomePagesOrderSalesDataRespDTO> mergedList = new ArrayList<>();
        mergedList.addAll(dataPartnerList);
        mergedList.addAll(dataDcList);
        mergedList.addAll(dataSupplierList);

        saveBatch(reqVO, mergedList);
    }

    @Override
    public void monthDataStatistics(HomePagesReqVO reqVO) {
        // 查询平台商数据
        reqVO.setIsDc(NumberPool.INT_ZERO)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesOrderSalesDataRespDTO> dataPartnerList = homePagesReportMapper.getHomePagesOrderSalesData(reqVO);

        // 查询平台商、所有运营商的数据
        reqVO.setIsDc(NumberPool.INT_ONE)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesOrderSalesDataRespDTO> dataDcList = homePagesReportMapper.getHomePagesOrderSalesData(reqVO);

        // 查询平台商、所有入驻商的数据
        reqVO.setIsSupplier(NumberPool.INT_ONE)
                .setIsDc(NumberPool.INT_ZERO);
        List<HomePagesOrderSalesDataRespDTO> dataSupplierList = homePagesReportMapper.getHomePagesOrderSalesData(reqVO);


        List<HomePagesOrderSalesDataRespDTO> mergedList = new ArrayList<>();
        mergedList.addAll(dataPartnerList);
        mergedList.addAll(dataDcList);
        mergedList.addAll(dataSupplierList);



        saveBatch(reqVO, mergedList);
    }

    private void saveBatch(HomePagesReqVO reqVO, List<HomePagesOrderSalesDataRespDTO> mergedList) {
        List<EsHomePagesOrderSalesData> esHomePagesData = HomeReportConvert.INSTANCE.convortEsHomePagesOrderSalesData(mergedList);
        esHomePagesData.forEach(data -> {
            data.setDateId(reqVO.getDateId())
                    .setRefreshEsTime(DateUtils.getNowDate())
                    .setId(StringUtils.format("{}_{}_{}_{}", reqVO.getDateId(), reqVO.getSysCode(), ToolUtil.isEmptyReturn(data.getDcId(), 1L), ToolUtil.isEmptyReturn(data.getSupplierId(), 1L)))
                    // 下单金额环比 = 下单金额 - 上次下单金额 / 上次下单金额 * 100
                    .setOrderAmtRate(
                            data.getOrderAmt().subtract(data.getBeforeOrderAmt())
                                    .divide(ToolUtil.isEmptyOrZeroReturn(data.getBeforeOrderAmt(), BigDecimal.ONE), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // 下单数量环比 = 下单数量 - 上次下单数量 / 上次下单数量 * 100
                    .setOrderQtyRate(
                            BigDecimal.valueOf(data.getOrderQty() - data.getBeforeOrderQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeOrderQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))

                    )
                    // 下单门店数量环比 = 下单门店数量 - 上次下单门店数量 / 上次下单门店数量 * 100
                    .setOrderBranchQtyRate(
                            BigDecimal.valueOf(data.getOrderBranchQty() - data.getBeforeOrderBranchQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeOrderBranchQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // 下单门店均客价格环比 = 下单门店均客价格 - 上次下单门店均客价格 / 上次下单门店均客价格 * 100
                    .setOrderBranchAmtAvgRate(
                            data.getOrderBranchAvgAmt().subtract(data.getBeforeOrderBranchAvgAmt())
                                    .divide(ToolUtil.isEmptyOrZeroReturn(data.getBeforeOrderBranchAvgAmt(), BigDecimal.ONE), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
            ;
        });
        // ES批量保存
        esHomePagesOrderSalesDataService.saveBatchHomePagesOrderSalesData(esHomePagesData);
    }
}
