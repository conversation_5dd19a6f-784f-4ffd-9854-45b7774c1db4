package com.zksr.report.controller.dwsAreaSales.vo;

import java.math.BigDecimal;
import java.util.Date;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 交易域区域城市粒度下单月汇总对象 dws_trd_area_sales_month
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@ApiModel("交易域区域城市粒度下单月汇总 - dws_trd_area_sales_month分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DwsTrdAreaSalesMonthPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "查询年月;字符串格式（yyyy-mm）")
    private String searchMonth;

    @ApiModelProperty(value = "区域ID")
    private Long areaId;

    @ApiModelProperty(value = "区域名称")
    private String areaName;
}
