package com.zksr.report.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.report.domain.DwsTrdBranchSalesDay;
import com.zksr.report.controller.branchSaleDay.vo.DwsTrdBranchSalesDayPageReqVO;
import com.zksr.report.controller.branchSaleDay.vo.DwsTrdBranchSalesDaySaveReqVO;

/**
 * 交易域门店粒度下单日汇总Service接口
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
public interface IDwsTrdBranchSalesDayService {

    /**
     * 新增交易域门店粒度下单日汇总
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertDwsTrdBranchSalesDay(@Valid DwsTrdBranchSalesDaySaveReqVO createReqVO);

    /**
     * 修改交易域门店粒度下单日汇总
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateDwsTrdBranchSalesDay(@Valid DwsTrdBranchSalesDaySaveReqVO updateReqVO);

    /**
     * 删除交易域门店粒度下单日汇总
     *
     * @param dateId 日期ID
     */
    public void deleteDwsTrdBranchSalesDay(Long dateId);

    /**
     * 批量删除交易域门店粒度下单日汇总
     *
     * @param dateIds 需要删除的交易域门店粒度下单日汇总主键集合
     * @return 结果
     */
    public void deleteDwsTrdBranchSalesDayByDateIds(Long[] dateIds);

    /**
     * 获得交易域门店粒度下单日汇总
     *
     * @param dateId 日期ID
     * @return 交易域门店粒度下单日汇总
     */
    public DwsTrdBranchSalesDay getDwsTrdBranchSalesDay(Long dateId);

    /**
     * 获得交易域门店粒度下单日汇总分页
     *
     * @param pageReqVO 分页查询
     * @return 交易域门店粒度下单日汇总分页
     */
    PageResult<DwsTrdBranchSalesDay> getDwsTrdBranchSalesDayPage(DwsTrdBranchSalesDayPageReqVO pageReqVO);

}
