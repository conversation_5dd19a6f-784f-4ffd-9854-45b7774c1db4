package com.zksr.report.service.impl.handler;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.elasticsearch.domain.EsHomePagesOrderAfterData;
import com.zksr.common.elasticsearch.service.EsHomePagesOrderAfterDataService;
import com.zksr.report.api.homePages.dto.HomePagesOrderAfterDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.report.convert.homeReport.HomeReportConvert;
import com.zksr.report.mapper.HomePagesReportMapper;
import com.zksr.report.service.handler.RptHomePagesDataHandlerService;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月26日 10:33
 * @description: PC首页 PC首页订单退货数据
 */
@Component
@Order(RptHomePagesDataHandlerService.AFTER_SALES_DATA)
@Slf4j
@SuppressWarnings("all")
public class RptHomePagesAfterDataHandlerServiceImpl implements RptHomePagesDataHandlerService {
    @Autowired
    private OrderApi orderApi;
    @Autowired
    private HomePagesReportMapper homePagesReportMapper;

    @Autowired
    private EsHomePagesOrderAfterDataService esHomePagesOrderAfterDataService;
    @Override
    public void dayDataStatistics(HomePagesReqVO reqVO) {
        // 查询平台商数据
        reqVO.setIsDc(NumberPool.INT_ZERO)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesOrderAfterDataRespDTO> dataPartnerList = orderApi.getHomePagesOrderAfterData(reqVO).getCheckedData();

        // 查询平台商、所有运营商的数据
        reqVO.setIsDc(NumberPool.INT_ONE)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesOrderAfterDataRespDTO> dataDcList = orderApi.getHomePagesOrderAfterData(reqVO).getCheckedData();

        // 查询平台商、所有入驻商的数据
        reqVO.setIsSupplier(NumberPool.INT_ONE)
                .setIsDc(NumberPool.INT_ZERO);
        List<HomePagesOrderAfterDataRespDTO> dataSupplierList = orderApi.getHomePagesOrderAfterData(reqVO).getCheckedData();


        List<HomePagesOrderAfterDataRespDTO> mergedList = new ArrayList<>();
        mergedList.addAll(dataPartnerList);
        mergedList.addAll(dataDcList);
        mergedList.addAll(dataSupplierList);

        saveBatch(reqVO, mergedList);
    }

    @Override
    public void monthDataStatistics(HomePagesReqVO reqVO) {
        // 查询平台商数据
        reqVO.setIsDc(NumberPool.INT_ZERO)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesOrderAfterDataRespDTO> dataPartnerList = homePagesReportMapper.getHomePagesOrderAfterData(reqVO);

        // 查询平台商、所有运营商的数据
        reqVO.setIsDc(NumberPool.INT_ONE)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesOrderAfterDataRespDTO> dataDcList = homePagesReportMapper.getHomePagesOrderAfterData(reqVO);

        // 查询平台商、所有入驻商的数据
        reqVO.setIsSupplier(NumberPool.INT_ONE)
                .setIsDc(NumberPool.INT_ZERO);
        List<HomePagesOrderAfterDataRespDTO> dataSupplierList = homePagesReportMapper.getHomePagesOrderAfterData(reqVO);

        List<HomePagesOrderAfterDataRespDTO> mergedList = new ArrayList<>();
        mergedList.addAll(dataPartnerList);
        mergedList.addAll(dataDcList);
        mergedList.addAll(dataSupplierList);

        saveBatch(reqVO, mergedList);
    }

    private void saveBatch(HomePagesReqVO reqVO, List<HomePagesOrderAfterDataRespDTO> mergedList) {
        List<EsHomePagesOrderAfterData> esHomePagesData = HomeReportConvert.INSTANCE.convortEsHomePagesOrderAfterData(mergedList);
        esHomePagesData.forEach(data -> {
            data.setDateId(reqVO.getDateId())
                    .setRefreshEsTime(DateUtils.getNowDate())
                    .setId(StringUtils.format("{}_{}_{}_{}", reqVO.getDateId(), reqVO.getSysCode(), ToolUtil.isEmptyReturn(data.getDcId(), 1L), ToolUtil.isEmptyReturn(data.getSupplierId(), 1L)))
                    // 退单金额同比上升/下降率 = 退单金额 - 上次退单金额 / 上次退单金额 * 100
                    .setReturnAmtRate(
                            data.getReturnAmt().subtract(data.getBeforeReturnAmt())
                                    .divide(ToolUtil.isEmptyOrZeroReturn(data.getBeforeReturnAmt(), BigDecimal.ONE), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // 退单数量同比上升/下降率 = 退单数量 - 上次退单数量 / 上次退单数量 * 100
                    .setReturnQtyRate(
                            BigDecimal.valueOf(data.getReturnQty() - data.getBeforeReturnQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeReturnQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // 退单门店数量同比上升/下降率 = 退单门店数量 - 上次退单门店数量 / 上次退单门店数量 * 100
                    .setReturnBranchQtyRate(
                            BigDecimal.valueOf(data.getReturnBranchQty() - data.getBeforeReturnBranchQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeReturnBranchQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // 退单Sku数量同比上升/下降率 = 退单退单Sku数量 - 上次退单Sku数量 / 上次退单Sku数量 * 100
                    .setReturnSkuQtyRate(
                            BigDecimal.valueOf(data.getReturnSkuQty() - data.getBeforeReturnSkuQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeReturnSkuQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
            ;
        });
        // ES批量保存
        esHomePagesOrderAfterDataService.saveBatchHomePagesOrderAfterData(esHomePagesData);
    }


}
