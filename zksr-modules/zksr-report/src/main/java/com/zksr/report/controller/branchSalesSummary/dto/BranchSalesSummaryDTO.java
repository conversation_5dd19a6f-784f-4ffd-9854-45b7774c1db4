package com.zksr.report.controller.branchSalesSummary.dto;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 门店月销售汇总
 * <AUTHOR>
 * @date 2024-11-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("门店月销售汇总 -  Response VO")
public class BranchSalesSummaryDTO {
    @ApiModelProperty(value = "门店编号")
    @Excel(name = "门店编号")
    private String branchId; // 门店编号

    @ApiModelProperty(value = "门店名称")
    @Excel(name = "门店名称")
    private String branchName; // 门店名称

    @ApiModelProperty(value = "销售总金额")
    @Excel(name = "销售总金额")
    private BigDecimal totalSalesAmount; // 销售总金额

    @ApiModelProperty(value = "销售订单数")
    @Excel(name = "销售订单数")
    private int totalSalesOrderCount; // 销售订单数

    @ApiModelProperty(value = "动销SKU数")
    @Excel(name = "动销SKU数")
    private int activeSkuCount; // 动销SKU数

    @ApiModelProperty(value = "退单金额")
    @Excel(name = "退单金额")
    private BigDecimal totalReturnAmount; // 退单金额

    @ApiModelProperty(value = "退单数量")
    @Excel(name = "退单数量")
    private int totalReturnOrderCount; // 退单数量

    @ApiModelProperty(value = "退单SKU数")
    @Excel(name = "退单SKU数")
    private int returnSkuCount; // 退单SKU数

    @ApiModelProperty(value = "总销售成本金额")
    @Excel(name = "总销售成本金额")
    private BigDecimal totalSalesCost; // 总销售成本金额

    @ApiModelProperty(value = "总退货成本金额")
    @Excel(name = "总退货成本金额")
    private BigDecimal totalReturnCost; // 总退货成本金额

    @ApiModelProperty(value = "毛利金额")
    @Excel(name = "毛利金额")
    private BigDecimal grossProfit; // 毛利金额

    @ApiModelProperty(value = "毛利率")
    @Excel(name = "毛利率")
    private BigDecimal grossProfitRate; // 毛利率

    @ApiModelProperty(value = "优惠金额")
    @Excel(name = "优惠金额")
    private BigDecimal totalDiscountAmount; // 优惠金额

    @ApiModelProperty(value = "上月销售金额")
    @Excel(name = "上月销售金额")
    private BigDecimal lastMonthSalesAmount; // 上月销售金额

    @ApiModelProperty(value = "销售金额成长比")
    @Excel(name = "销售金额成长比")
    private BigDecimal salesGrowthRate; // 销售金额成长比

    @ApiModelProperty(value = "上月动销SKU数")
    @Excel(name = "上月动销SKU数")
    private int lastMonthActiveSkuCount; // 上月动销SKU数

    @ApiModelProperty(value = "动销SKU成长比")
    @Excel(name = "动销SKU成长比")
    private BigDecimal activeSkuGrowthRate; // 动销SKU成长比

    @ApiModelProperty(value = "待处理退单金额")
    @Excel(name = "待处理退单金额")
    private BigDecimal pendingRefundAmount; // 待处理退单金额
}