package com.zksr.report.mapper;

import com.zksr.report.controller.branchSalesSummary.dto.BranchSalesSummaryDTO;
import com.zksr.report.controller.branchSalesSummary.dto.BranchSalesSummaryQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/11/28
 * @desc
 */

@Mapper
public interface BranchSalesSummaryMapper {
    List<BranchSalesSummaryDTO> getMonthlyBranchSalesSummary(BranchSalesSummaryQueryDTO query);

    Long countMonthlyBranchSalesSummary(BranchSalesSummaryQueryDTO query);
}
