package com.zksr.report.convert.tagDefinit;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.api.branch.vo.ColonelIndexBranchLevelTotalRespVO;
import com.zksr.report.domain.RptTagDef;
import com.zksr.report.controller.tagDefinit.vo.RptTagDefRespVO;
import com.zksr.report.controller.tagDefinit.vo.RptTagDefSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.report.domain.vo.BranchTagTotalVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 标签定义 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-13
*/
@Mapper
public interface RptTagDefConvert {

    RptTagDefConvert INSTANCE = Mappers.getMapper(RptTagDefConvert.class);

    RptTagDefRespVO convert(RptTagDef rptTagDef);

    RptTagDef convert(RptTagDefSaveReqVO rptTagDefSaveReq);

    PageResult<RptTagDefRespVO> convertPage(PageResult<RptTagDef> rptTagDefPage);
}