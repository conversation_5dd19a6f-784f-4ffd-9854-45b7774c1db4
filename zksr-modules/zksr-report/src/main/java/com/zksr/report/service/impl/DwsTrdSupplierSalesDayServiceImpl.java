package com.zksr.report.service.impl;

import com.github.pagehelper.Page;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.controller.dwsSupplierSales.dto.DwsTrdSupplierSalesDayRespDTO;
import com.zksr.report.controller.dwsSupplierSales.vo.DwsTrdSupplierSalesDayPageReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.report.mapper.DwsTrdSupplierSalesDayMapper;
import com.zksr.report.service.IDwsTrdSupplierSalesDayService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 交易域入驻商粒度下单日汇总Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Service
public class DwsTrdSupplierSalesDayServiceImpl implements IDwsTrdSupplierSalesDayService {
    @Autowired
    private DwsTrdSupplierSalesDayMapper dwsTrdSupplierSalesDayMapper;


    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<DwsTrdSupplierSalesDayRespDTO> getDwsTrdSupplierSalesDayPage(DwsTrdSupplierSalesDayPageReqVO pageReqVO) {
        Page<DwsTrdSupplierSalesDayRespDTO> page = PageUtils.startPage(pageReqVO);
        return new PageResult<>(dwsTrdSupplierSalesDayMapper.getSupplierSummaryPage(pageReqVO), page.getTotal());
    }


    // TODO 待办：请将下面的错误码复制到 com.zksr.report.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 交易域入驻商粒度下单日汇总 TODO 补充编号 ==========
    // ErrorCode DWS_TRD_SUPPLIER_SALES_DAY_NOT_EXISTS = new ErrorCode(TODO 补充编号, "交易域入驻商粒度下单日汇总不存在");


}
