package com.zksr.report.controller.schedule;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.report.controller.schedule.vo.ComputeBranchTagReqVO;
import com.zksr.report.service.IScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 外部定时任务调度
 * @date 2024/11/13 14:05
 */
@Api(tags = "管理后台 - 外部定时任务调度", produces = "application/json")
@Validated
@RestController
@RequestMapping("/schedule")
public class ScheduleController {

    @Autowired
    private IScheduleService scheduleService;

    @GetMapping("/computeBranchTag")
    @ApiOperation(value = "计算门店标签", httpMethod = HttpMethod.GET)
    public CommonResult<Boolean> computeBranchTag(ComputeBranchTagReqVO computeBranchTagReqVO) {
        scheduleService.computeBranchTag(computeBranchTagReqVO);
        /**
         * 任何时候, 都计算当前月份标签
         */
        scheduleService.computeBranchTag(new ComputeBranchTagReqVO(DateUtil.format(DateUtil.date(), DatePattern.SIMPLE_MONTH_PATTERN)));
        return CommonResult.success(Boolean.TRUE);
    }
}
