package com.zksr.report.convert.rptExport;

import com.zksr.report.api.export.dto.BranchSalesSummaryExportDTO;
import com.zksr.report.api.export.dto.ColonelSalesSummaryExportDTO;
import com.zksr.report.api.export.vo.BranchSalesSummaryExportPageVO;
import com.zksr.report.api.export.vo.ColonelSalesSummaryExportPageVO;
import com.zksr.report.controller.branchSalesSummary.dto.BranchSalesSummaryDTO;
import com.zksr.report.controller.branchSalesSummary.dto.BranchSalesSummaryQueryDTO;
import com.zksr.report.controller.colonelSalesSummary.dto.ColonelSalesSummaryDTO;
import com.zksr.report.controller.colonelSalesSummary.dto.ColonelSalesSummaryQueryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

import java.lang.annotation.Target;
import java.util.List;

@Mapper
public interface RptExportConvert {
    RptExportConvert INSTANCE = org.mapstruct.factory.Mappers.getMapper(RptExportConvert.class);

    BranchSalesSummaryQueryDTO convort( BranchSalesSummaryExportPageVO reqPageVo);

    List<BranchSalesSummaryExportDTO> convort0(List<BranchSalesSummaryDTO> resDtoList);

    ColonelSalesSummaryQueryDTO convort1(ColonelSalesSummaryExportPageVO reqPageVo);

    List<ColonelSalesSummaryExportDTO> convort2(List<ColonelSalesSummaryDTO> resDtoList);
}
