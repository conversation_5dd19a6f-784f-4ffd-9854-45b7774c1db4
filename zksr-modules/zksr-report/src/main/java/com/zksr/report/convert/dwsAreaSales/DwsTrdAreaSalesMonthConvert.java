package com.zksr.report.convert.dwsAreaSales;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.controller.dwsAreaSales.dto.DwsTrdAreaSalesMonthRespDTO;
import com.zksr.report.controller.dwsAreaSales.vo.DwsTrdAreaSalesMonthSaveReqVO;
import com.zksr.report.domain.DwsTrdAreaSalesMonth;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 交易域区域城市粒度下单月汇总 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-22
*/
@Mapper
public interface DwsTrdAreaSalesMonthConvert {

    DwsTrdAreaSalesMonthConvert INSTANCE = Mappers.getMapper(DwsTrdAreaSalesMonthConvert.class);

    DwsTrdAreaSalesMonthRespDTO convert(DwsTrdAreaSalesMonth dwsTrdAreaSalesMonth);

    DwsTrdAreaSalesMonth convert(DwsTrdAreaSalesMonthSaveReqVO dwsTrdAreaSalesMonthSaveReq);

    PageResult<DwsTrdAreaSalesMonthRespDTO> convertPage(PageResult<DwsTrdAreaSalesMonth> dwsTrdAreaSalesMonthPage);
}