package com.zksr.report.controller.branchCategoryOrderAmt.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 门店一级类别销售金额统计月对象 ads_branch_cat1_orderamt_stats_month
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Data
@ApiModel("门店一级类别销售金额统计月 - ads_branch_cat1_orderamt_stats_month分页 Request VO")
public class AdsBranchCat1OrderamtStatsMonthSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 月份ID */
    @ApiModelProperty(value = "插入时间id")
    private Long monthId;

    /** 平台商id */
    @ApiModelProperty(value = "插入时间id")
    private Long sysCode;

    /** 区域城市id */
    @ApiModelProperty(value = "插入时间id")
    private Long areaId;

    /** 门店id */
    @ApiModelProperty(value = "插入时间id")
    private Long branchId;

    /** 一级类别id */
    @ApiModelProperty(value = "插入时间id")
    private Long cat1Id;

    /** 一级类别id */
    @Excel(name = "一级类别id")
    @ApiModelProperty(value = "一级类别id")
    private BigDecimal cat1OrderAmt;

    /** 门店下单金额 */
    @Excel(name = "门店下单金额")
    @ApiModelProperty(value = "门店下单金额")
    private BigDecimal branchOrderAmt;

    /** 占比 */
    @Excel(name = "占比")
    @ApiModelProperty(value = "占比")
    private BigDecimal proportion;

    /** 插入时间id */
    @Excel(name = "插入时间id")
    @ApiModelProperty(value = "插入时间id")
    private Long insertDateId;

}
