package com.zksr.report.service.impl;

import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.report.mapper.DwsTrdBranchSalesMonthMapper;
import com.zksr.report.convert.BranchSaleMonth.DwsTrdBranchSalesMonthConvert;
import com.zksr.report.domain.DwsTrdBranchSalesMonth;
import com.zksr.report.controller.branchSaleMonth.vo.DwsTrdBranchSalesMonthPageReqVO;
import com.zksr.report.controller.branchSaleMonth.vo.DwsTrdBranchSalesMonthSaveReqVO;
import com.zksr.report.service.IDwsTrdBranchSalesMonthService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 交易域门店粒度下单月汇总Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Service
public class DwsTrdBranchSalesMonthServiceImpl implements IDwsTrdBranchSalesMonthService {
    @Autowired
    private DwsTrdBranchSalesMonthMapper dwsTrdBranchSalesMonthMapper;

    /**
     * 新增交易域门店粒度下单月汇总
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertDwsTrdBranchSalesMonth(DwsTrdBranchSalesMonthSaveReqVO createReqVO) {
        // 插入
        DwsTrdBranchSalesMonth dwsTrdBranchSalesMonth = DwsTrdBranchSalesMonthConvert.INSTANCE.convert(createReqVO);
        dwsTrdBranchSalesMonthMapper.insert(dwsTrdBranchSalesMonth);
        // 返回
        return dwsTrdBranchSalesMonth.getMonthId();
    }

    /**
     * 修改交易域门店粒度下单月汇总
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateDwsTrdBranchSalesMonth(DwsTrdBranchSalesMonthSaveReqVO updateReqVO) {
        dwsTrdBranchSalesMonthMapper.updateById(DwsTrdBranchSalesMonthConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除交易域门店粒度下单月汇总
     *
     * @param monthId 日期ID
     */
    @Override
    public void deleteDwsTrdBranchSalesMonth(Long monthId) {
        // 删除
        dwsTrdBranchSalesMonthMapper.deleteById(monthId);
    }

    /**
     * 批量删除交易域门店粒度下单月汇总
     *
     * @param monthIds 需要删除的交易域门店粒度下单月汇总主键
     * @return 结果
     */
    @Override
    public void deleteDwsTrdBranchSalesMonthByMonthIds(Long[] monthIds) {
        for(Long monthId : monthIds){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deleteDwsTrdBranchSalesMonth(monthId);
        }
    }

    /**
     * 获得交易域门店粒度下单月汇总
     *
     * @param monthId 日期ID
     * @return 交易域门店粒度下单月汇总
     */
    @Override
    public DwsTrdBranchSalesMonth getDwsTrdBranchSalesMonth(Long monthId) {
        return dwsTrdBranchSalesMonthMapper.selectById(monthId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<DwsTrdBranchSalesMonth> getDwsTrdBranchSalesMonthPage(DwsTrdBranchSalesMonthPageReqVO pageReqVO) {
        return dwsTrdBranchSalesMonthMapper.selectPage(pageReqVO);
    }


}
