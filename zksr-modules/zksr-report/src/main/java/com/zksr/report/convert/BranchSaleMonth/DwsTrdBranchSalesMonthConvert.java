package com.zksr.report.convert.BranchSaleMonth;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.domain.DwsTrdBranchSalesMonth;
import com.zksr.report.controller.branchSaleMonth.vo.DwsTrdBranchSalesMonthRespVO;
import com.zksr.report.controller.branchSaleMonth.vo.DwsTrdBranchSalesMonthSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 交易域门店粒度下单月汇总 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-25
*/
@Mapper
public interface DwsTrdBranchSalesMonthConvert {

    DwsTrdBranchSalesMonthConvert INSTANCE = Mappers.getMapper(DwsTrdBranchSalesMonthConvert.class);

    DwsTrdBranchSalesMonthRespVO convert(DwsTrdBranchSalesMonth dwsTrdBranchSalesMonth);

    DwsTrdBranchSalesMonth convert(DwsTrdBranchSalesMonthSaveReqVO dwsTrdBranchSalesMonthSaveReq);

    PageResult<DwsTrdBranchSalesMonthRespVO> convertPage(PageResult<DwsTrdBranchSalesMonth> dwsTrdBranchSalesMonthPage);
}