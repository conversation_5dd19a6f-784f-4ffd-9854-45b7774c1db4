package com.zksr.report.mapper;

import com.zksr.report.api.branch.vo.*;
import com.zksr.report.domain.AdsBranchCat1OrderamtStatsMonth;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.domain.AdsBranchTradeWeekdayDistribution;
import com.zksr.report.domain.vo.BranchTagTotalVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/11/25
 * @desc
 */

@Mapper
public interface ColonelRptMapper {
    /**
     * 门店技术
     */
    Long countByColonel(@Param("colonelIdList") List<Long> colonelIdList);

    /**
     * 业务员门店销售额
     */
    BigDecimal sumMonthSaleAmtByColonel(@Param("colonelIdList")List<Long> colonelIdList, @Param("monthId") Integer monthId);

    /**
     * 业务员月活跃用户
     */
    Long countMonthActiveByColonel(@Param("colonelIdList")List<Long> colonelIdList, @Param("monthId") Integer monthId);

    /**
     * 业务员月活跃用户
     */
    BigDecimal sumMonthColonelProfitByColonel(@Param("colonelIdList")List<Long> colonelIdList, @Param("monthId") Integer monthId);

    /**
     * 获取业务员门店月标签
     */
    List<BranchTagTotalVO> selectMonthBranchTagByColonel(@Param("colonelIdList")List<Long> colonelIdList, @Param("monthId") Integer monthId);

    /**
     * 业务员门店月周销售chat图
     */
    AdsBranchTradeWeekdayDistribution selectSumSaleWeek(@Param("colonelIdList")List<Long> colonelIdList, @Param("monthId") Integer monthId);

    /**
     * 月订货类别占比
     */
    List<AdsBranchCat1OrderamtStatsMonth> selectSaleCategoryList(@Param("colonelIdList")List<Long> colonelIdList, @Param("monthId") Integer monthId);

    /**
     * 门店月订货类别占比
     */
    List<AdsBranchCat1OrderamtStatsMonth> selectBranchSaleCategoryList(@Param("branchId") Long branchId, @Param("monthId")  Integer monthId);

    /**
     * 查询业务员, 门店销售等级统计
     */
    List<BranchTagTotalVO> selectMonthBranchTagByColonelAndLevelTag(@Param("colonelIdList")List<Long> colonelIdList, @Param("monthId") Integer monthId, @Param("levelTagType") String levelTagType);

    /**
     * 查询业务员, 门店销售等级标签客户数
     */
    Long countMonthBranchTagByColonelAndLevelTag(@Param("colonelIdList")List<Long> colonelIdList, @Param("monthId") Integer monthId, @Param("levelTagType") String levelTagType);

    /**
     * 查询标签门店列表
     */
    List<ColonelTagBranchRespVO> selectTagBranchList(TagBranchListReqVO reqVO);

    /**
     * 获取门店一年的销售数据, 按月区分
     */
    List<BranchTargetProcessVO> selectBranchSaleByYear(@Param("branchId") Long branchId, @Param("year") int year);

    /**
     * 获取业务员客户 24小时下单分布
     */
    List<HoursSaleVO> selectColonelBranchHoursSaleList(@Param("colonelIdList")List<Long> colonelIdList, @Param("monthId") Integer monthId);

    /**
     * 门店 24小时下单分布
     */
    List<HoursSaleVO> selectBranchHoursSaleList(@Param("branchId") Long branchId, @Param("monthId") Integer monthId);
}
