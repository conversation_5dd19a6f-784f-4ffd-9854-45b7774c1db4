package com.zksr.report.controller.tagDefinit;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.model.LoginUser;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.report.domain.RptTagDef;
import com.zksr.report.service.IRptTagDefService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.report.controller.tagDefinit.vo.RptTagDefPageReqVO;
import com.zksr.report.controller.tagDefinit.vo.RptTagDefSaveReqVO;
import com.zksr.report.controller.tagDefinit.vo.RptTagDefRespVO;
import com.zksr.report.convert.tagDefinit.RptTagDefConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 标签定义Controller
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Api(tags = "管理后台 - 标签定义接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/tagDefinit")
public class RptTagDefController {

    @Autowired
    private IRptTagDefService rptTagDefService;

    /**
     * 新增标签定义
     */
    /*@ApiOperation(value = "新增标签定义", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "标签定义", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody RptTagDefSaveReqVO createReqVO) {
        return success(rptTagDefService.insertRptTagDef(createReqVO));
    }*/

    /**
     * 删除标签定义
     */
    /*@ApiOperation(value = "删除标签定义", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "标签定义", businessType = BusinessType.DELETE)
    @DeleteMapping("/{tagDefIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] tagDefIds) {
        rptTagDefService.deleteRptTagDefByTagDefIds(tagDefIds);
        return success(true);
    }*/

    /**
     * 修改标签定义
     */
    @ApiOperation(value = "修改标签定义", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "标签定义", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody RptTagDefSaveReqVO updateReqVO) {
        rptTagDefService.updateRptTagDef(updateReqVO);
        return success(true);
    }

    /**
     * 获取标签定义详细信息
     */
    @ApiOperation(value = "获得标签定义详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/getInfo")
    public CommonResult<RptTagDefRespVO> getInfo() {
        LoginUser user = SecurityUtils.getLoginUser();
        return success(rptTagDefService.getRptTagDefSysCode(user.getSysCode()));
    }

    /**
     * 分页查询标签定义
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得标签定义分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<RptTagDefRespVO>> getPage(@Valid RptTagDefPageReqVO pageReqVO) {
        PageResult<RptTagDef> pageResult = rptTagDefService.getRptTagDefPage(pageReqVO);
        return success(RptTagDefConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "report:tagDefinit:add";
        /** 编辑 */
        public static final String EDIT = "report:tagDefinit:edit";
        /** 删除 */
        public static final String DELETE = "report:tagDefinit:remove";
        /** 列表 */
        public static final String LIST = "report:tagDefinit:list";
        /** 查询 */
        public static final String GET = "report:tagDefinit:query";
        /** 停用 */
        public static final String DISABLE = "report:tagDefinit:disable";
        /** 启用 */
        public static final String ENABLE = "report:tagDefinit:enable";
    }
}
