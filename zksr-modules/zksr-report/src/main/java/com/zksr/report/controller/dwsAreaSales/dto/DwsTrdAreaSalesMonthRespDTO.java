package com.zksr.report.controller.dwsAreaSales.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;


/**
 * 交易域区域城市粒度下单月汇总对象 dws_trd_area_sales_month
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
@ApiModel("交易域区域城市粒度下单月汇总 - dws_trd_area_sales_month Response VO")
public class DwsTrdAreaSalesMonthRespDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日期ID")
    private Long monthId;

    @ApiModelProperty(value = "平台商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    @ApiModelProperty(value = "运营商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    @ApiModelProperty(value = "运营商名称")
    private String dcName;

    @ApiModelProperty(value = "一级区域城市名称")
    private String oneAreaName;

    @ApiModelProperty(value = "二级区域城市名称")
    private String twoAreaName;

    @ApiModelProperty(value = "原订单金额")
    private BigDecimal preOrderAmt;

    @ApiModelProperty(value = "订单实付金额")
    private BigDecimal orderAmt;

    @ApiModelProperty(value = "区域总门店数")
    private Long areaBranchTotalQty;

    @ApiModelProperty(value = "订单数量")
    private Long orderQty;

    @ApiModelProperty(value = "动销门店数")
    private Long dxBranchQty;

    @ApiModelProperty(value = "动销SKU数")
    private Long dxSkuQty;

    @ApiModelProperty(value = "销售总成本金额")
    private BigDecimal supplierCostAmt;

    @ApiModelProperty(value = "订单优惠金额")
    private BigDecimal discountAmt;

    @ApiModelProperty(value = "销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100")
    private BigDecimal profitRate;


    @ApiModelProperty(value = "售后金额")
    private BigDecimal returnAmt;

    @ApiModelProperty(value = "售后单数量")
    private Long returnQty;

    @ApiModelProperty(value = "售后SKU数")
    private Long returnSkuQty;

    @ApiModelProperty(value = "售后入驻商总成本金额")
    private BigDecimal returnSupplierCostAmt;


}
