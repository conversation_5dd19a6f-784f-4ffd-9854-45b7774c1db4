package com.zksr.report.component.branchTag;

import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.report.api.branch.dto.BranchActiveConfigDTO;
import com.zksr.report.api.branch.dto.BranchTagConfigDTO;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.domain.vo.BranchSaleVO;
import com.zksr.report.domain.vo.TagNodeComponent;
import com.zksr.report.slot.BranchTagContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 活跃用户定义
 * @date 2024/11/14 9:05
 */
@Slf4j
@Component("branchTagActiveCmp")
public class BranchTagActiveCmp extends TagNodeComponent {

    @Override
    public void process() throws Exception {
        BranchTagContext context = this.getContext();
        // 标签配置
        BranchTagConfigDTO tagConfig = context.getTagConfig();

        BranchActiveConfigDTO active = tagConfig.getBranchActive();
        if (Objects.isNull(active)) {
            // 不计算
            return;
        }

        // 获取门店ID集合
        List<Long> branchIds = context.getBranchList().stream().map(BranchDTO::getBranchId).collect(Collectors.toList());

        // 查询销售数据
        List<BranchSaleVO> saleVOList;
        Map<Long, BranchSaleVO> saleMap;
        if ("A".equals(active.getPlan())) {
            // A-计划按照自然月
            saleVOList = rptTagDefMapper.selectByMonthSale(context.getMonthId(), branchIds);
            saleMap = saleVOList.stream().collect(Collectors.toMap(BranchSaleVO::getBranchId, item -> item));
        } else {
            // B-计划按照指定分类自然月
            // 关联分类销售数据
            // 获取核算分类
            List<String> catgoryList = tagConfig.getBranchActive().getCatgoryList();
            if (Objects.isNull(catgoryList) || catgoryList.isEmpty()) {
                log.info("计算类占比标签, 但是没有配置分类");
                return;
            }
            saleVOList = rptTagDefMapper.selectByMonthClassSale(context.getMonthId(), branchIds, catgoryList);
            saleMap = saleVOList.stream().collect(Collectors.toMap(BranchSaleVO::getBranchId, item -> item));
        }

        for (Long branchId : branchIds) {
            BigDecimal saleAmt = BigDecimal.ZERO;
            if (saleMap.containsKey(branchId)) {
                saleAmt = saleMap.get(branchId).getSaleAmt();
            }
            // 是否达到活跃标准
            if (NumberUtil.isGreaterOrEqual(saleAmt, active.getTargetAmt())) {
                AdsBranchTagMonth adsBranchTagMonth = context.getTagMonthMap().get(branchId);
                BranchTagEnum branchTag = active.getBranchTag();
                // 活跃客户标签类型;例：是否活跃客户
                adsBranchTagMonth.setActiveTagType(branchTag.getTag());
                // 活跃客户标签定义id;例：id
                adsBranchTagMonth.setActiveTagDefId(active.getTagDefId());
                // 活跃客户规则;例：自然月销售金额 >= 2000元
                if ("A".equals(active.getPlan())) {
                    // A-计划按照自然月
                    adsBranchTagMonth.setActiveTagRule(
                            StringUtils.format("自然月销售额>={}元", active.getTargetAmt())
                    );
                } else {
                    // B-计划按照指定分类自然月
                    adsBranchTagMonth.setActiveTagRule(
                            StringUtils.format("指定分类自然月销售额>={}元", active.getTargetAmt())
                    );
                }
                // 活跃客户标签名;例：是
                adsBranchTagMonth.setActiveTagName(branchTag.getName());
                // 活跃客户标签指标值;例：2100元
                adsBranchTagMonth.setActiveTagVal(StringUtils.format("{}元", saleAmt.toString()));
            }
        }
    }
}
