package com.zksr.report.controller.branchCategoryOrderAmt;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.report.domain.AdsBranchCat1OrderamtStatsMonth;
import com.zksr.report.service.IAdsBranchCat1OrderamtStatsMonthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.report.controller.branchCategoryOrderAmt.vo.AdsBranchCat1OrderamtStatsMonthPageReqVO;
import com.zksr.report.controller.branchCategoryOrderAmt.vo.AdsBranchCat1OrderamtStatsMonthSaveReqVO;
import com.zksr.report.controller.branchCategoryOrderAmt.vo.AdsBranchCat1OrderamtStatsMonthRespVO;
import com.zksr.report.convert.branchCategoryOrderAmt.AdsBranchCat1OrderamtStatsMonthConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 门店一级类别销售金额统计月Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Api(tags = "管理后台 - 门店一级类别销售金额统计月接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/branchCategoryOrderAmt")
public class AdsBranchCat1OrderamtStatsMonthController {
    @Autowired
    private IAdsBranchCat1OrderamtStatsMonthService adsBranchCat1OrderamtStatsMonthService;

    /**
     * 新增门店一级类别销售金额统计月
     */
    @ApiOperation(value = "新增门店一级类别销售金额统计月", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "门店一级类别销售金额统计月", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AdsBranchCat1OrderamtStatsMonthSaveReqVO createReqVO) {
        return success(adsBranchCat1OrderamtStatsMonthService.insertAdsBranchCat1OrderamtStatsMonth(createReqVO));
    }

    /**
     * 修改门店一级类别销售金额统计月
     */
    @ApiOperation(value = "修改门店一级类别销售金额统计月", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "门店一级类别销售金额统计月", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody AdsBranchCat1OrderamtStatsMonthSaveReqVO updateReqVO) {
            adsBranchCat1OrderamtStatsMonthService.updateAdsBranchCat1OrderamtStatsMonth(updateReqVO);
        return success(true);
    }

    /**
     * 删除门店一级类别销售金额统计月
     */
    @ApiOperation(value = "删除门店一级类别销售金额统计月", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "门店一级类别销售金额统计月", businessType = BusinessType.DELETE)
    @DeleteMapping("/{monthIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] monthIds) {
        adsBranchCat1OrderamtStatsMonthService.deleteAdsBranchCat1OrderamtStatsMonthByMonthIds(monthIds);
        return success(true);
    }

    /**
     * 获取门店一级类别销售金额统计月详细信息
     */
    @ApiOperation(value = "获得门店一级类别销售金额统计月详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{monthId}")
    public CommonResult<AdsBranchCat1OrderamtStatsMonthRespVO> getInfo(@PathVariable("monthId") Long monthId) {
        AdsBranchCat1OrderamtStatsMonth adsBranchCat1OrderamtStatsMonth = adsBranchCat1OrderamtStatsMonthService.getAdsBranchCat1OrderamtStatsMonth(monthId);
        return success(AdsBranchCat1OrderamtStatsMonthConvert.INSTANCE.convert(adsBranchCat1OrderamtStatsMonth));
    }

    /**
     * 分页查询门店一级类别销售金额统计月
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得门店一级类别销售金额统计月分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AdsBranchCat1OrderamtStatsMonthRespVO>> getPage(@Valid AdsBranchCat1OrderamtStatsMonthPageReqVO pageReqVO) {
        PageResult<AdsBranchCat1OrderamtStatsMonth> pageResult = adsBranchCat1OrderamtStatsMonthService.getAdsBranchCat1OrderamtStatsMonthPage(pageReqVO);
        return success(AdsBranchCat1OrderamtStatsMonthConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "report:branchCategoryOrderAmt:add";
        /** 编辑 */
        public static final String EDIT = "report:branchCategoryOrderAmt:edit";
        /** 删除 */
        public static final String DELETE = "report:branchCategoryOrderAmt:remove";
        /** 列表 */
        public static final String LIST = "report:branchCategoryOrderAmt:list";
        /** 查询 */
        public static final String GET = "report:branchCategoryOrderAmt:query";
        /** 停用 */
        public static final String DISABLE = "report:branchCategoryOrderAmt:disable";
        /** 启用 */
        public static final String ENABLE = "report:branchCategoryOrderAmt:enable";
    }
}
