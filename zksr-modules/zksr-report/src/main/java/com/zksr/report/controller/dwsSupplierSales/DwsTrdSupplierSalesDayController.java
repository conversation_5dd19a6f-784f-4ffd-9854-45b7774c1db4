package com.zksr.report.controller.dwsSupplierSales;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.report.domain.DwsTrdSupplierSalesDay;
import com.zksr.report.service.IDwsTrdSupplierSalesDayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.report.controller.dwsSupplierSales.vo.DwsTrdSupplierSalesDayPageReqVO;
import com.zksr.report.controller.dwsSupplierSales.dto.DwsTrdSupplierSalesDayRespDTO;
import com.zksr.report.convert.dwsSupplierSales.DwsTrdSupplierSalesDayConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 交易域入驻商粒度下单日汇总Controller
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Api(tags = "管理后台 - 交易域入驻商粒度下单日汇总接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/day")
public class DwsTrdSupplierSalesDayController {
    @Autowired
    private IDwsTrdSupplierSalesDayService dwsTrdSupplierSalesDayService;

    /**
     * 分页查询交易域入驻商粒度下单日汇总
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得交易域入驻商粒度下单日汇总分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<DwsTrdSupplierSalesDayRespDTO>> getPage(@Valid DwsTrdSupplierSalesDayPageReqVO pageReqVO) {
        return success(dwsTrdSupplierSalesDayService.getDwsTrdSupplierSalesDayPage(pageReqVO));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "report:day:add";
        /** 编辑 */
        public static final String EDIT = "report:day:edit";
        /** 删除 */
        public static final String DELETE = "report:day:remove";
        /** 列表 */
        public static final String LIST = "report:day:list";
        /** 查询 */
        public static final String GET = "report:day:query";
        /** 停用 */
        public static final String DISABLE = "report:day:disable";
        /** 启用 */
        public static final String ENABLE = "report:day:enable";
    }
}
