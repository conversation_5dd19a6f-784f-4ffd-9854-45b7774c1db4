package com.zksr.report.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.api.export.dto.BranchSalesSummaryExportDTO;
import com.zksr.report.api.export.vo.BranchSalesSummaryExportPageVO;
import com.zksr.report.controller.branchSalesSummary.dto.BranchSalesSummaryDTO;
import com.zksr.report.controller.branchSalesSummary.dto.BranchSalesSummaryQueryDTO;

import java.util.List;

public interface IBranchSalesSummaryService {
    /**
     * 查询门店月销售汇总报表
     *
     * @param queryDTO 查询条件
     * @return 汇总结果列表
     */
    PageResult<BranchSalesSummaryDTO> getMonthlyBranchSalesSummary(BranchSalesSummaryQueryDTO queryDTO);


    /**
     * 门店月销售导出
     *
     * @param queryDTO 查询条件
     * @return 汇总结果列表
     */
    List<BranchSalesSummaryExportDTO> branchSalesMonthExport(BranchSalesSummaryExportPageVO pageVO);

}