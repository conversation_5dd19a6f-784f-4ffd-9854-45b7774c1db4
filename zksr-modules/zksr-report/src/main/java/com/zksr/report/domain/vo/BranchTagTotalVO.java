package com.zksr.report.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/25 14:23
 */
@Data
public class BranchTagTotalVO {

    @ApiModelProperty("业务员ID")
    private Long colonelId;

    /**
     * {@link com.zksr.common.core.enums.branch.BranchTagEnum}
     */
    @ApiModelProperty("等级标签")
    private String branchTag;

    @ApiModelProperty("符合标签门店数")
    private Long cnt;

    @ApiModelProperty("门店数")
    private Long branchCnt;

    @ApiModelProperty("销售金额")
    private BigDecimal saleAmt;
}
