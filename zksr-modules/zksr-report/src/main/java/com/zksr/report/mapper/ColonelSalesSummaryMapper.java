package com.zksr.report.mapper;

import com.zksr.member.api.branch.dto.ColonelBranchCountDTO;
import com.zksr.report.controller.colonelSalesSummary.dto.ColonelSalesSummaryDTO;
import com.zksr.report.controller.colonelSalesSummary.dto.ColonelSalesSummaryQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ColonelSalesSummaryMapper {

    List<ColonelSalesSummaryDTO> getMonthlyColonelSalesSummary(ColonelSalesSummaryQueryDTO queryDTO);

    List<ColonelBranchCountDTO> getBranchCountFromMemTable(@Param("colonelIds") List<String> colonelIds, @Param("currentMonthId") String currentMonthId);

    Long countMonthlyColonelSalesSummary(ColonelSalesSummaryQueryDTO queryDTO);
}