package com.zksr.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.report.domain.vo.BranchFreqVO;
import com.zksr.report.domain.vo.BranchSaleVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.domain.RptTagDef;
import com.zksr.report.controller.tagDefinit.vo.RptTagDefPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 标签定义Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Mapper
public interface RptTagDefMapper extends BaseMapperX<RptTagDef> {
    default PageResult<RptTagDef> selectPage(RptTagDefPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RptTagDef>()
                    .eqIfPresent(RptTagDef::getTagDefId, reqVO.getTagDefId())
                    .eqIfPresent(RptTagDef::getSysCode, reqVO.getSysCode())
                    .likeIfPresent(RptTagDef::getTagName, reqVO.getTagName())
                    .eqIfPresent(RptTagDef::getTagRuleJson, reqVO.getTagRuleJson())
                    .eqIfPresent(RptTagDef::getEnabled, reqVO.getEnabled())
                    .eqIfPresent(RptTagDef::getTagType, reqVO.getTagType())
                .orderByDesc(RptTagDef::getTagDefId));
    }

    default void updateDisable(Long sysCode) {
        RptTagDef update = new RptTagDef();
        update.setEnabled(NumberPool.INT_ZERO);
        LambdaQueryWrapperX<RptTagDef> uw = new LambdaQueryWrapperX<>();
        uw.eq(RptTagDef::getSysCode, sysCode);
        update(update, uw);
    }

    default List<RptTagDef> selectEnable(Long sysCode) {
        return selectList(
                new LambdaQueryWrapperX<RptTagDef>()
                        .eq(RptTagDef::getSysCode, sysCode)
                        .eq(RptTagDef::getEnabled, NumberPool.INT_ONE)
        );
    }

    List<BranchSaleVO> selectByMonthSale(@Param("monthId") Long monthId, @Param("branchIds") List<Long> branchIds);

    List<BranchSaleVO> selectByMonthClassSale(@Param("monthId") Long monthId, @Param("branchIds") List<Long> branchIds, @Param("categoryList") List<String> categoryList);

    List<BranchFreqVO> selectByMonthFreqSaleDay(@Param("startDateId") Integer startDateId, @Param("endDateId") Integer endDateId,  @Param("branchIds") List<Long> branchIds);
}
