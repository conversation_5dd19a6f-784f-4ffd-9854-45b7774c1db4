package com.zksr.report.service.impl;

import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.api.export.dto.BranchSalesSummaryExportDTO;
import com.zksr.report.api.export.vo.BranchSalesSummaryExportPageVO;
import com.zksr.report.controller.branchSalesSummary.dto.BranchSalesSummaryDTO;
import com.zksr.report.controller.branchSalesSummary.dto.BranchSalesSummaryQueryDTO;
import com.zksr.report.convert.rptExport.RptExportConvert;
import com.zksr.report.mapper.BranchSalesSummaryMapper;
import com.zksr.report.service.IBranchSalesSummaryService;
import com.zksr.trade.api.after.AfterApi;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BranchSalesSummaryServiceImpl implements IBranchSalesSummaryService {
    @Autowired
    private BranchSalesSummaryMapper branchSalesSummaryMapper;

    @Resource
    private AfterApi afterApi;

    @Override
    public PageResult<BranchSalesSummaryDTO> getMonthlyBranchSalesSummary(BranchSalesSummaryQueryDTO queryDTO) {
        PageResult<BranchSalesSummaryDTO> result = new PageResult<>();

        // 分页参数
        int pageNo = queryDTO.getPageNo();
        int pageSize = queryDTO.getPageSize();
        int offset = (pageNo - 1) * pageSize;

        queryDTO.setPageNo(offset);
        queryDTO.setPageSize(pageSize);

        // 日期处理
        Date queryMonth = queryDTO.getMonthId();
        Calendar calendar = Calendar.getInstance();

        if (queryMonth == null) {
            throw new ServiceException("选择日期查询时，查询月份不能为空");
        }
        String currentMonthId = DateFormatUtils.format(queryMonth, "yyyyMM");
        calendar.setTime(queryMonth);
        calendar.add(Calendar.MONTH, -1);
        String previousMonthId = DateFormatUtils.format(calendar.getTime(), "yyyyMM");

        queryDTO.setCurrentMonthId(currentMonthId);
        queryDTO.setPreviousMonthId(previousMonthId);

        // 查询总记录数
        Long total = branchSalesSummaryMapper.countMonthlyBranchSalesSummary(queryDTO);
        if (total == 0L) {
            result.setList(Collections.emptyList());
            result.setTotal(0L);
            return result;
        }

        // 查询数据
        List<BranchSalesSummaryDTO> data = branchSalesSummaryMapper.getMonthlyBranchSalesSummary(queryDTO);
        // 门店返回数据组装
        branchSalesDataAssembly(data, currentMonthId);
        result.setList(data);
        result.setTotal(total);

        return result;
    }

    @Override
    public List<BranchSalesSummaryExportDTO> branchSalesMonthExport(BranchSalesSummaryExportPageVO pageVO) {
        BranchSalesSummaryQueryDTO queryDTO = RptExportConvert.INSTANCE.convort(pageVO);
        // 日期处理
        Date queryMonth = pageVO.getMonthId();
        Calendar calendar = Calendar.getInstance();

        if (queryMonth == null) {
            throw new ServiceException("选择日期查询时，查询月份不能为空");
        }
        String currentMonthId = DateFormatUtils.format(queryMonth, "yyyyMM");
        calendar.setTime(queryMonth);
        calendar.add(Calendar.MONTH, -1);
        String previousMonthId = DateFormatUtils.format(calendar.getTime(), "yyyyMM");

        queryDTO.setCurrentMonthId(currentMonthId);
        queryDTO.setPreviousMonthId(previousMonthId);
        List<BranchSalesSummaryDTO> data = branchSalesSummaryMapper.getMonthlyBranchSalesSummary(queryDTO);
        if (ToolUtil.isEmpty(data) || data.isEmpty()) {
            return new ArrayList<>();
        }
        // 门店返回数据组装
        branchSalesDataAssembly(data, currentMonthId);

        return RptExportConvert.INSTANCE.convort0(data);
    }

    /**
     * 门店销售组装数据
     * @param data
     */
    private void branchSalesDataAssembly(List<BranchSalesSummaryDTO> data, String currentMonthId) {
        if(ToolUtil.isNotEmpty(data)){
            // 提取所有 branchId
            List<String> branchIds = data.stream()
                    .map(BranchSalesSummaryDTO::getBranchId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询退款金额
            Map<Long, BigDecimal> refundAmountMap = afterApi.getPendingRefundAmount(currentMonthId, branchIds).getCheckedData().stream()
                    .collect(Collectors.toMap(
                            map -> (Long) map.get("branchId"),
                            map -> {
                                Double sales = (Double) map.get("refundAmount");
                                return BigDecimal.valueOf(sales);
                            },
                            BigDecimal::add
                    ));
            if(ToolUtil.isNotEmpty(refundAmountMap)){
                // 将退款金额设置到 data 对象中
                data.forEach(dto -> dto.setPendingRefundAmount(refundAmountMap.getOrDefault(Long.valueOf(dto.getBranchId()), BigDecimal.ZERO)));
            }
        }
    }
}