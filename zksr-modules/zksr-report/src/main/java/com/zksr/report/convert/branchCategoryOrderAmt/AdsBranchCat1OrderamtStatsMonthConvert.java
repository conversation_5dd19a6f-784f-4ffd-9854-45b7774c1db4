package com.zksr.report.convert.branchCategoryOrderAmt;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.domain.AdsBranchCat1OrderamtStatsMonth;
import com.zksr.report.controller.branchCategoryOrderAmt.vo.AdsBranchCat1OrderamtStatsMonthRespVO;
import com.zksr.report.controller.branchCategoryOrderAmt.vo.AdsBranchCat1OrderamtStatsMonthSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 门店一级类别销售金额统计月 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-26
*/
@Mapper
public interface AdsBranchCat1OrderamtStatsMonthConvert {

    AdsBranchCat1OrderamtStatsMonthConvert INSTANCE = Mappers.getMapper(AdsBranchCat1OrderamtStatsMonthConvert.class);

    AdsBranchCat1OrderamtStatsMonthRespVO convert(AdsBranchCat1OrderamtStatsMonth adsBranchCat1OrderamtStatsMonth);

    AdsBranchCat1OrderamtStatsMonth convert(AdsBranchCat1OrderamtStatsMonthSaveReqVO adsBranchCat1OrderamtStatsMonthSaveReq);

    PageResult<AdsBranchCat1OrderamtStatsMonthRespVO> convertPage(PageResult<AdsBranchCat1OrderamtStatsMonth> adsBranchCat1OrderamtStatsMonthPage);
}