package com.zksr.report.controller.branchSaleDay;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.report.domain.DwsTrdBranchSalesDay;
import com.zksr.report.service.IDwsTrdBranchSalesDayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.report.controller.branchSaleDay.vo.DwsTrdBranchSalesDayPageReqVO;
import com.zksr.report.controller.branchSaleDay.vo.DwsTrdBranchSalesDaySaveReqVO;
import com.zksr.report.controller.branchSaleDay.vo.DwsTrdBranchSalesDayRespVO;
import com.zksr.report.convert.branchSaleDay.DwsTrdBranchSalesDayConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 交易域门店粒度下单日汇总Controller
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Api(tags = "管理后台 - 交易域门店粒度下单日汇总接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/branchSaleDay")
public class DwsTrdBranchSalesDayController {
    @Autowired
    private IDwsTrdBranchSalesDayService dwsTrdBranchSalesDayService;

    /**
     * 新增交易域门店粒度下单日汇总
     */
    @ApiOperation(value = "新增交易域门店粒度下单日汇总", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "交易域门店粒度下单日汇总", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody DwsTrdBranchSalesDaySaveReqVO createReqVO) {
        return success(dwsTrdBranchSalesDayService.insertDwsTrdBranchSalesDay(createReqVO));
    }

    /**
     * 修改交易域门店粒度下单日汇总
     */
    @ApiOperation(value = "修改交易域门店粒度下单日汇总", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "交易域门店粒度下单日汇总", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody DwsTrdBranchSalesDaySaveReqVO updateReqVO) {
            dwsTrdBranchSalesDayService.updateDwsTrdBranchSalesDay(updateReqVO);
        return success(true);
    }

    /**
     * 删除交易域门店粒度下单日汇总
     */
    @ApiOperation(value = "删除交易域门店粒度下单日汇总", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "交易域门店粒度下单日汇总", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dateIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] dateIds) {
        dwsTrdBranchSalesDayService.deleteDwsTrdBranchSalesDayByDateIds(dateIds);
        return success(true);
    }

    /**
     * 获取交易域门店粒度下单日汇总详细信息
     */
    @ApiOperation(value = "获得交易域门店粒度下单日汇总详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{dateId}")
    public CommonResult<DwsTrdBranchSalesDayRespVO> getInfo(@PathVariable("dateId") Long dateId) {
        DwsTrdBranchSalesDay dwsTrdBranchSalesDay = dwsTrdBranchSalesDayService.getDwsTrdBranchSalesDay(dateId);
        return success(DwsTrdBranchSalesDayConvert.INSTANCE.convert(dwsTrdBranchSalesDay));
    }

    /**
     * 分页查询交易域门店粒度下单日汇总
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得交易域门店粒度下单日汇总分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<DwsTrdBranchSalesDayRespVO>> getPage(@Valid DwsTrdBranchSalesDayPageReqVO pageReqVO) {
        PageResult<DwsTrdBranchSalesDay> pageResult = dwsTrdBranchSalesDayService.getDwsTrdBranchSalesDayPage(pageReqVO);
        return success(DwsTrdBranchSalesDayConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "report:branchSaleDay:add";
        /** 编辑 */
        public static final String EDIT = "report:branchSaleDay:edit";
        /** 删除 */
        public static final String DELETE = "report:branchSaleDay:remove";
        /** 列表 */
        public static final String LIST = "report:branchSaleDay:list";
        /** 查询 */
        public static final String GET = "report:branchSaleDay:query";
        /** 停用 */
        public static final String DISABLE = "report:branchSaleDay:disable";
        /** 启用 */
        public static final String ENABLE = "report:branchSaleDay:enable";
    }
}
