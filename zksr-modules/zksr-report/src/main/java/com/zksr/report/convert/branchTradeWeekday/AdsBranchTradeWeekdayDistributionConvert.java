package com.zksr.report.convert.branchTradeWeekday;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.domain.AdsBranchTradeWeekdayDistribution;
import com.zksr.report.api.branch.vo.AdsBranchTradeWeekdayDistributionRespVO;
import com.zksr.report.controller.branchTradeWeekday.vo.AdsBranchTradeWeekdayDistributionSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 门店周下单分布月 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-25
*/
@Mapper
public interface AdsBranchTradeWeekdayDistributionConvert {

    AdsBranchTradeWeekdayDistributionConvert INSTANCE = Mappers.getMapper(AdsBranchTradeWeekdayDistributionConvert.class);

    AdsBranchTradeWeekdayDistributionRespVO convert(AdsBranchTradeWeekdayDistribution adsBranchTradeWeekdayDistribution);

    AdsBranchTradeWeekdayDistribution convert(AdsBranchTradeWeekdayDistributionSaveReqVO adsBranchTradeWeekdayDistributionSaveReq);

    PageResult<AdsBranchTradeWeekdayDistributionRespVO> convertPage(PageResult<AdsBranchTradeWeekdayDistribution> adsBranchTradeWeekdayDistributionPage);
}