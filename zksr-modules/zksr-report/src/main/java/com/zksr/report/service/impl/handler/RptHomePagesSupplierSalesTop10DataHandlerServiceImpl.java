package com.zksr.report.service.impl.handler;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.elasticsearch.domain.EsHomePagesSalesTop10Data;
import com.zksr.common.elasticsearch.service.EsHomePagesSalesTop10DataService;
import com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.report.convert.homeReport.HomeReportConvert;
import com.zksr.report.mapper.HomePagesReportMapper;
import com.zksr.report.service.IReportCacheService;
import com.zksr.report.service.handler.RptHomePagesDataHandlerService;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月26日 10:33
 * @description: PC首页 入驻商销售TOP10数据
 */
@Component
@Order(RptHomePagesDataHandlerService.SUPPLIER_SALES_TOP10_DATA)
@Slf4j
@SuppressWarnings("all")
public class RptHomePagesSupplierSalesTop10DataHandlerServiceImpl implements RptHomePagesDataHandlerService {
    @Autowired
    private OrderApi orderApi;
    @Autowired
    private HomePagesReportMapper homePagesReportMapper;

    @Autowired
    private EsHomePagesSalesTop10DataService esHomePagesSalesTop10DataService;

    @Autowired
    private IReportCacheService reportCacheService;
    @Override
    public void dayDataStatistics(HomePagesReqVO reqVO) {
        // 查询平台、入驻商再运营商中的销售数据
        reqVO.setSalesType("supplier")
                .setIsDc(NumberPool.INT_ONE)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesSalesTop10DataRespDTO> dataDcList = orderApi.getHomePagesSalesTop10Data(reqVO).getCheckedData();

        saveBatch(reqVO, dataDcList);
    }

    @Override
    public void monthDataStatistics(HomePagesReqVO reqVO) {
        /// 查询平台、入驻商再运营商中的销售数据
        reqVO.setSalesType("supplier")
                .setIsDc(NumberPool.INT_ONE)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesSalesTop10DataRespDTO> dataDcList = homePagesReportMapper.getHomePagesSupplierSalesTop10Data(reqVO);

        saveBatch(reqVO, dataDcList);
    }


    private void saveBatch(HomePagesReqVO reqVO, List<HomePagesSalesTop10DataRespDTO> dataDcList) {
        List<EsHomePagesSalesTop10Data> esHomePagesData = HomeReportConvert.INSTANCE.convortEsHomePagesSalesTop10Data(dataDcList);
        esHomePagesData.forEach(data -> {
            SupplierDTO supplierDTO = reportCacheService.getSupplierDTO(data.getSalesTypeId());
            data.setDateId(reqVO.getDateId())
                    .setRefreshEsTime(DateUtils.getNowDate())
                    .setSalesTypeName(ToolUtil.isNotEmpty(supplierDTO) ? supplierDTO.getSupplierName() : data.getSalesTypeId() + "")
                    .setSalesType("supplier")
                    .setDcId(ToolUtil.isEmptyReturn(data.getDcId(), NumberPool.LOWER_GROUND_LONG))
                    .setSupplierId(ToolUtil.isEmptyReturn(data.getSupplierId(), NumberPool.LOWER_GROUND_LONG))
                    .setId(StringUtils.format("{}_{}_{}_{}_{}_{}", reqVO.getDateId(), reqVO.getSysCode(), ToolUtil.isEmptyReturn(data.getDcId(), 1L), ToolUtil.isEmptyReturn(data.getSupplierId(), 1L), data.getSalesTypeId(), data.getSalesType()));
        });
        // ES批量保存
        esHomePagesSalesTop10DataService.saveBatchHomePagesSalesTop10Data(esHomePagesData);
    }
}
