package com.zksr.report.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.report.controller.dwsSupplierSales.dto.DwsTrdSupplierSalesDayRespDTO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.report.domain.DwsTrdSupplierSalesDay;
import com.zksr.report.controller.dwsSupplierSales.vo.DwsTrdSupplierSalesDayPageReqVO;

import java.util.List;


/**
 * 交易域入驻商粒度下单日汇总Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Mapper
public interface DwsTrdSupplierSalesDayMapper extends BaseMapperX<DwsTrdSupplierSalesDay> {

    /**
     * 交易域入驻商粒度下单汇总分页列表
     * @param pageReqVO
     * @return
     */
    List<DwsTrdSupplierSalesDayRespDTO> getSupplierSummaryPage(DwsTrdSupplierSalesDayPageReqVO pageReqVO);
}
