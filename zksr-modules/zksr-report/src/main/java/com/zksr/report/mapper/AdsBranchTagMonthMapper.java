package com.zksr.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.controller.branchTagMonth.vo.AdsBranchTagMonthPageReqVO;


/**
 * 门店标签月Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Mapper
public interface AdsBranchTagMonthMapper extends BaseMapperX<AdsBranchTagMonth> {
    default PageResult<AdsBranchTagMonth> selectPage(AdsBranchTagMonthPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AdsBranchTagMonth>()
                    .eqIfPresent(AdsBranchTagMonth::getMonthId, reqVO.getMonthId())
                    .eqIfPresent(AdsBranchTagMonth::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AdsBranchTagMonth::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(AdsBranchTagMonth::getLevelTagType, reqVO.getLevelTagType())
                    .eqIfPresent(AdsBranchTagMonth::getLevelTagDefId, reqVO.getLevelTagDefId())
                    .eqIfPresent(AdsBranchTagMonth::getLevelTagRule, reqVO.getLevelTagRule())
                    .likeIfPresent(AdsBranchTagMonth::getLevelTagName, reqVO.getLevelTagName())
                    .eqIfPresent(AdsBranchTagMonth::getLevelTagVal, reqVO.getLevelTagVal())
                    .eqIfPresent(AdsBranchTagMonth::getActiveTagType, reqVO.getActiveTagType())
                    .eqIfPresent(AdsBranchTagMonth::getActiveTagDefId, reqVO.getActiveTagDefId())
                    .eqIfPresent(AdsBranchTagMonth::getActiveTagRule, reqVO.getActiveTagRule())
                    .likeIfPresent(AdsBranchTagMonth::getActiveTagName, reqVO.getActiveTagName())
                    .eqIfPresent(AdsBranchTagMonth::getActiveTagVal, reqVO.getActiveTagVal())
                    .eqIfPresent(AdsBranchTagMonth::getLzLevelTagType, reqVO.getLzLevelTagType())
                    .eqIfPresent(AdsBranchTagMonth::getLzLevelTagDefId, reqVO.getLzLevelTagDefId())
                    .eqIfPresent(AdsBranchTagMonth::getLzLevelTagRule, reqVO.getLzLevelTagRule())
                    .likeIfPresent(AdsBranchTagMonth::getLzLevelTagName, reqVO.getLzLevelTagName())
                    .eqIfPresent(AdsBranchTagMonth::getLzLevelTagVal, reqVO.getLzLevelTagVal())
                    .eqIfPresent(AdsBranchTagMonth::getProfitLevelTagType, reqVO.getProfitLevelTagType())
                    .eqIfPresent(AdsBranchTagMonth::getProfitLevelTagDefId, reqVO.getProfitLevelTagDefId())
                    .eqIfPresent(AdsBranchTagMonth::getProfitLevelTagRule, reqVO.getProfitLevelTagRule())
                    .likeIfPresent(AdsBranchTagMonth::getProfitLevelTagName, reqVO.getProfitLevelTagName())
                    .eqIfPresent(AdsBranchTagMonth::getProfitLevelTagVal, reqVO.getProfitLevelTagVal())
                    .eqIfPresent(AdsBranchTagMonth::getFreqLevelTagType, reqVO.getFreqLevelTagType())
                    .eqIfPresent(AdsBranchTagMonth::getFreqLevelTagDefId, reqVO.getFreqLevelTagDefId())
                    .eqIfPresent(AdsBranchTagMonth::getFreqLevelTagRule, reqVO.getFreqLevelTagRule())
                    .likeIfPresent(AdsBranchTagMonth::getFreqLevelTagName, reqVO.getFreqLevelTagName())
                    .eqIfPresent(AdsBranchTagMonth::getFreqLevelTagVal, reqVO.getFreqLevelTagVal())
                    //.eqIfPresent(AdsBranchTagMonth::getDelFlag, reqVO.getDelFlag())
                .orderByDesc(AdsBranchTagMonth::getMonthId));
    }

    default void deleteMonthIdAndBranchId(AdsBranchTagMonth tagMonth) {
        LambdaQueryWrapperX<AdsBranchTagMonth> qw = new LambdaQueryWrapperX<>();
        qw.eq(AdsBranchTagMonth::getMonthId, tagMonth.getMonthId());
        qw.eq(AdsBranchTagMonth::getBranchId, tagMonth.getBranchId());
        /*AdsBranchTagMonth update = new AdsBranchTagMonth();
        update.setDelFlag(DelFlagConstants.DISABLE);
        update(update, qw);*/

        delete(qw);
    }

    default AdsBranchTagMonth selectBranchByMonthId(Long branchId, Integer monthId) {
        return selectOne(new LambdaQueryWrapperX<AdsBranchTagMonth>()
                .eqIfPresent(AdsBranchTagMonth::getMonthId, monthId)
                .eqIfPresent(AdsBranchTagMonth::getBranchId, branchId)
        );
    }
}
