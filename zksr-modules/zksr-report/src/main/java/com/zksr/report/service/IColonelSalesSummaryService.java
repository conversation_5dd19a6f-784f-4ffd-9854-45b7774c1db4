package com.zksr.report.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.api.export.dto.ColonelSalesSummaryExportDTO;
import com.zksr.report.api.export.vo.ColonelSalesSummaryExportPageVO;
import com.zksr.report.controller.colonelSalesSummary.dto.ColonelSalesSummaryDTO;
import com.zksr.report.controller.colonelSalesSummary.dto.ColonelSalesSummaryQueryDTO;

import java.util.List;

public interface IColonelSalesSummaryService {
    /**
     * 查询业务员月销售汇总报表
     *
     * @param queryDTO 查询条件
     * @return 汇总结果列表
     */
    PageResult<ColonelSalesSummaryDTO> getMonthlyColonelSalesSummary(ColonelSalesSummaryQueryDTO queryDTO);

    /**
     * 业务员月销售数据报表导出
     *
     * @param reqPageVo 查询条件
     * @return 汇总结果列表
     */
    List<ColonelSalesSummaryExportDTO> monthColonelSalesSummaryExport(ColonelSalesSummaryExportPageVO reqPageVo);
}