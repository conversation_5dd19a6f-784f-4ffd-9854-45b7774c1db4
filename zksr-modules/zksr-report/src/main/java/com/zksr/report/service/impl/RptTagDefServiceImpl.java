package com.zksr.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.report.api.branch.dto.*;
import com.zksr.report.controller.tagDefinit.vo.RptTagDefPageReqVO;
import com.zksr.report.controller.tagDefinit.vo.RptTagDefRespVO;
import com.zksr.report.controller.tagDefinit.vo.RptTagDefSaveReqVO;
import com.zksr.report.convert.tagDefinit.RptTagDefConvert;
import com.zksr.report.domain.RptTagDef;
import com.zksr.report.mapper.RptTagDefMapper;
import com.zksr.report.service.IRptTagDefService;
import com.zksr.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标签定义Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Service
public class RptTagDefServiceImpl implements IRptTagDefService {
    @Autowired
    private RptTagDefMapper rptTagDefMapper;

    /**
     * 新增标签定义
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertRptTagDef(RptTagDefSaveReqVO createReqVO) {
        // 插入
        RptTagDef rptTagDef = RptTagDefConvert.INSTANCE.convert(createReqVO);
        rptTagDefMapper.insert(rptTagDef);
        // 返回
        return rptTagDef.getTagDefId();
    }

    /**
     * 修改标签定义
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional
    public void updateRptTagDef(RptTagDefSaveReqVO updateReqVO) {
        LoginUser user = SecurityUtils.getLoginUser();
        // 先全部停用
        rptTagDefMapper.updateDisable(user.getSysCode());

        //客户活跃定义
        {
            BranchActiveConfigDTO branchActive = updateReqVO.getBranchActive();
            RptTagDef tagDef = RptTagDef.builder()
                    .tagRuleJson(JSON.toJSONString(branchActive))
                    .enabled(NumberPool.INT_ONE)
                    .build();
            tagDef.setTag(branchActive.getBranchTag());
            rptTagDefMapper.insert(tagDef);
        }
        //客户等级定义
        {
            List<BranchLevelConfigDTO> branchLevelConfig = updateReqVO.getBranchLevelConfig();
            for (BranchLevelConfigDTO configDTO : branchLevelConfig) {
                RptTagDef tagDef = RptTagDef.builder()
                        .tagRuleJson(JSON.toJSONString(configDTO))
                        .enabled(NumberPool.INT_ONE)
                        .build();
                tagDef.setTag(configDTO.getBranchTag());
                rptTagDefMapper.insert(tagDef);
            }
        }
        //客户销售类占比定义
        {
            for (BranchLevelConfigDTO configDTO : updateReqVO.getSaleClassConfig().getRuleList()) {
                // 规则集成分类数据
                configDTO.setCatgoryList(updateReqVO.getSaleClassConfig().getCatgoryList());
                RptTagDef tagDef = RptTagDef.builder()
                        .tagRuleJson(JSON.toJSONString(configDTO))
                        .enabled(NumberPool.INT_ONE)
                        .build();
                tagDef.setTag(configDTO.getBranchTag());
                rptTagDefMapper.insert(tagDef);
            }
        }
        // 客户毛利等级定义
        {
            List<BranchProfitLevelConfigDTO> branchLevelConfig = updateReqVO.getBranchProfitLevelConfig();
            for (BranchLevelConfigDTO configDTO : branchLevelConfig) {
                RptTagDef tagDef = RptTagDef.builder()
                        .tagRuleJson(JSON.toJSONString(configDTO))
                        .enabled(NumberPool.INT_ONE)
                        .build();
                tagDef.setTag(configDTO.getBranchTag());
                rptTagDefMapper.insert(tagDef);
            }
        }
        // 客户订货频次
        {
            List<BranchCreateOrderConfigDTO> branchLevelConfig = updateReqVO.getBranchCreateOrderConfig();
            for (BranchCreateOrderConfigDTO configDTO : branchLevelConfig) {
                RptTagDef tagDef = RptTagDef.builder()
                        .tagRuleJson(JSON.toJSONString(configDTO))
                        .enabled(NumberPool.INT_ONE)
                        .build();
                tagDef.setTag(configDTO.getBranchTag());
                rptTagDefMapper.insert(tagDef);
            }
        }
    }

    /**
     * 删除标签定义
     *
     * @param tagDefId 标签定义id
     */
    @Override
    public void deleteRptTagDef(Long tagDefId) {
        // 删除
        rptTagDefMapper.deleteById(tagDefId);
    }

    /**
     * 批量删除标签定义
     *
     * @param tagDefIds 需要删除的标签定义主键
     * @return 结果
     */
    @Override
    public void deleteRptTagDefByTagDefIds(Long[] tagDefIds) {
        for(Long tagDefId : tagDefIds){
            this.deleteRptTagDef(tagDefId);
        }
    }

    /**
     * 获得标签定义
     *
     * @param tagDefId 标签定义id
     * @return 标签定义
     */
    @Override
    public RptTagDef getRptTagDef(Long tagDefId) {
        return rptTagDefMapper.selectById(tagDefId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<RptTagDef> getRptTagDefPage(RptTagDefPageReqVO pageReqVO) {
        return rptTagDefMapper.selectPage(pageReqVO);
    }

    @Override
    @SuppressWarnings("all")
    public RptTagDefRespVO getRptTagDefSysCode(Long sysCode) {
        RptTagDefRespVO respVO = new RptTagDefRespVO();
        // 获取配置
        List<RptTagDef> rptTagDefList = rptTagDefMapper.selectEnable(sysCode);

        // 客户标签分组
        Map<String, List<RptTagDef>> tagTypeMap = rptTagDefList.stream().collect(Collectors.groupingBy(item ->
                BranchTagEnum.fromValue(item.getTagType()).getType()
        ));

        //客户活跃定义
        if (tagTypeMap.containsKey(BranchTagEnum.ACTIVE_CUST.getType())) {
            RptTagDef tagDef = tagTypeMap.get(BranchTagEnum.ACTIVE_CUST.getType()).get(0);
            BranchActiveConfigDTO configDTO = JSON.parseObject(tagDef.getTagRuleJson(), BranchActiveConfigDTO.class);
            configDTO.setTagDefId(tagDef.getTagDefId());
            respVO.setBranchActive(configDTO);
        }

        // 客户等级定义
        if (tagTypeMap.containsKey(BranchTagEnum.LEVEL_A.getType())) {
            List<BranchLevelConfigDTO> ruleList = new ArrayList<>();
            for (RptTagDef tagDef : tagTypeMap.get(BranchTagEnum.LEVEL_A.getType())) {
                BranchLevelConfigDTO configDTO = JSON.parseObject(tagDef.getTagRuleJson(), BranchLevelConfigDTO.class);
                configDTO.setTagDefId(tagDef.getTagDefId());
                ruleList.add(configDTO);
            }
            ruleList.sort(Comparator.comparing(BranchLevelConfigDTO::getSort));
            respVO.setBranchLevelConfig(ruleList);
        }

        // 客户销售类占比定义
        if (tagTypeMap.containsKey(BranchTagEnum.CLASS_A.getType())) {
            List<BranchLevelConfigDTO> ruleList = new ArrayList<>();
            for (RptTagDef tagDef : tagTypeMap.get(BranchTagEnum.CLASS_A.getType())) {
                BranchLevelConfigDTO configDTO = JSON.parseObject(tagDef.getTagRuleJson(), BranchLevelConfigDTO.class);
                configDTO.setTagDefId(tagDef.getTagDefId());
                ruleList.add(configDTO);
            }
            ruleList.sort(Comparator.comparing(BranchLevelConfigDTO::getSort));
            BranchSaleClassConfigDTO classConfigDTO = new BranchSaleClassConfigDTO();
            classConfigDTO.setRuleList(ruleList);
            classConfigDTO.setCatgoryList(ruleList.get(0).getCatgoryList());
            respVO.setSaleClassConfig(classConfigDTO);
        }

        // 客户毛利等级定义
        if (tagTypeMap.containsKey(BranchTagEnum.PROFIT_A.getType())) {
            List<BranchProfitLevelConfigDTO> ruleList = new ArrayList<>();
            for (RptTagDef tagDef : tagTypeMap.get(BranchTagEnum.PROFIT_A.getType())) {
                BranchProfitLevelConfigDTO configDTO = JSON.parseObject(tagDef.getTagRuleJson(), BranchProfitLevelConfigDTO.class);
                configDTO.setTagDefId(tagDef.getTagDefId());
                ruleList.add(configDTO);
            }
            ruleList.sort(Comparator.comparing(BranchLevelConfigDTO::getSort));
            respVO.setBranchProfitLevelConfig(ruleList);
        }

        // 客户订货频次
        if (tagTypeMap.containsKey(BranchTagEnum.CREATE_ORDER_A.getType())) {
            List<BranchCreateOrderConfigDTO> ruleList = new ArrayList<>();
            for (RptTagDef tagDef : tagTypeMap.get(BranchTagEnum.CREATE_ORDER_A.getType())) {
                BranchCreateOrderConfigDTO configDTO = JSON.parseObject(tagDef.getTagRuleJson(), BranchCreateOrderConfigDTO.class);
                configDTO.setTagDefId(tagDef.getTagDefId());
                ruleList.add(configDTO);
            }
            ruleList.sort(Comparator.comparing(BranchLevelConfigDTO::getSort));
            respVO.setBranchCreateOrderConfig(ruleList);
        }
        return respVO;
    }


}
