package com.zksr.report.service.impl;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.elasticsearch.domain.*;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;
import com.zksr.common.elasticsearch.service.*;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.report.api.homePages.dto.*;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.report.convert.homeReport.HomeReportConvert;
import com.zksr.report.service.IHomePageService;
import com.zksr.report.service.handler.RptHomePagesDataHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Slf4j
@Service
public class HomePageServiceimpl implements IHomePageService {

    @Autowired
    private EsHomePagesDataService esHomePagesDataService;
    @Autowired
    private EsHomePagesOrderSalesDataService esHomePagesOrderSalesDataService;
    @Autowired
    private EsHomePagesOrderAfterDataService esHomePagesOrderAfterDataService;
    @Autowired
    private EsHomePagesBranchDataService esHomePagesBranchDataService;
    @Autowired
    private EsHomePagesSkuDataService esHomePagesSkuDataService;
    @Autowired
    private EsHomePagesSalesTop10DataService esHomePagesSalesTop10DataService;

    @Resource
    private List<RptHomePagesDataHandlerService> homePagesDataHandlerServices;


    @Override
    public void refreshEsHomePagesDataJob(Long sysCode) {
        // 调用订单模块API查询实时数据
        HomePagesReqVO reqVO = new HomePagesReqVO();
        reqVO.setSysCode(sysCode)
                .setDateId(DateUtils.dateTime())
                .setStartDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, new Date()))
                .setEndDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, new Date()));
        homePagesDataHandlerServices.forEach(service -> service.dayDataStatistics(reqVO));
    }

    @Override
    public void refreshEsHomePagesMonthDataJob(Long sysCode, String startDate, String endDate) {
        // 调用订单模块API查询实时数据
        HomePagesReqVO reqVO = new HomePagesReqVO();
        List<String> dateString = DateUtils.getMonthRange(startDate, endDate);
        dateString.forEach(date -> {
            reqVO.setSysCode(sysCode)
                    .setStartDate(DateUtils.getMonthFirstDay(date))
                    .setEndDate(DateUtils.getMonthLastDay(date))
                    .setDateId(date.replace("-", ""))
            ;
            // 刷新 EsHomePagesData
            homePagesDataHandlerServices.forEach(service -> service.monthDataStatistics(reqVO));
        });


    }

    @Override
    public HomePagesCurrentSalesDataRespDTO getCurrentSalesData(HomePagesReqVO reqVO) {
        // 处理过滤查询条件
        checkSearchCondition(reqVO, NumberPool.LONG_ONE);

        HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
        // idKEY: 时间_平台商_运营商_入驻商
        searchDTO.setId(StringUtils.format("{}_{}_{}_{}", DateUtils.dateTime(), reqVO.getSysCode(), reqVO.getDcId(), reqVO.getSupplierId()));
        EsHomePagesData esHomePagesData = esHomePagesDataService.searchHomePagesData(searchDTO);

        HomePagesCurrentSalesDataRespDTO reqDto = HomeReportConvert.INSTANCE.convortHomePagesCurrentSalesData(esHomePagesData);
        if (ToolUtil.isNotEmpty(reqDto)) {
            // 这里默认当前查询月份数据为0 ， 应之前数据统计时，默认写入的是当前月份数据导致查询显示异常
            reqDto.setMonthOrderAmt(BigDecimal.ZERO)
                    .setBeforeMonthOrderAmt(BigDecimal.ZERO)
                    .setMonthOrderRate(BigDecimal.ZERO)
            ;
            // 获取当前选中月的销售数据
            searchDTO.setId(StringUtils.format("{}_{}_{}_{}", reqVO.getDateId().substring(0, 6), reqVO.getSysCode(), reqVO.getDcId(), reqVO.getSupplierId()));
            EsHomePagesOrderSalesData orderData = esHomePagesOrderSalesDataService.searchHomePagesOrderSalesData(searchDTO);
            if (ToolUtil.isNotEmpty(orderData)) {
                reqDto.setMonthOrderAmt(orderData.getOrderAmt())
                        .setBeforeMonthOrderAmt(orderData.getBeforeOrderAmt())
                        .setMonthOrderRate(
                                orderData.getOrderAmt().subtract(orderData.getBeforeOrderAmt())
                                        .divide(ToolUtil.isEmptyOrZeroReturn(orderData.getBeforeOrderAmt(), BigDecimal.ONE), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                        .multiply(BigDecimal.valueOf(100))
                        )
                ;
            }
        }
        return reqDto;
    }

    @Override
    public HomePagesOrderSalesDataRespDTO getOrderSalesData(HomePagesReqVO reqVO) {
        // 处理过滤查询条件
        checkSearchCondition(reqVO, NumberPool.LONG_ONE);

        HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
        // idKEY: 时间_平台商_运营商_入驻商
        searchDTO.setId(StringUtils.format("{}_{}_{}_{}", reqVO.getDateId(), reqVO.getSysCode(), reqVO.getDcId(), reqVO.getSupplierId()));
        EsHomePagesOrderSalesData data = esHomePagesOrderSalesDataService.searchHomePagesOrderSalesData(searchDTO);
        return HomeReportConvert.INSTANCE.convortHomePagesOrderSalesData0(data);
    }

    @Override
    public HomePagesBranchDataRespDTO getHomePagesBranchData(HomePagesReqVO reqVO) {
        // 处理过滤查询条件
        checkSearchCondition(reqVO, NumberPool.LONG_ONE);

        HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
        // idKEY: 时间_平台商_运营商_入驻商
        searchDTO.setId(StringUtils.format("{}_{}_{}", reqVO.getDateId(), reqVO.getSysCode(), reqVO.getDcId()));
        EsHomePagesBranchData data = esHomePagesBranchDataService.searchHomePagesBranchData(searchDTO);
        return HomeReportConvert.INSTANCE.convortHomePagesBranchData0(data);
    }

    @Override
    public HomePagesOrderAfterDataRespDTO getOrderAfterData(HomePagesReqVO reqVO) {
        // 处理过滤查询条件
        checkSearchCondition(reqVO, NumberPool.LONG_ONE);

        HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
        // idKEY: 时间_平台商_运营商_入驻商
        searchDTO.setId(StringUtils.format("{}_{}_{}_{}", reqVO.getDateId(), reqVO.getSysCode(), reqVO.getDcId(), reqVO.getSupplierId()));
        EsHomePagesOrderAfterData data = esHomePagesOrderAfterDataService.searchHomePagesOrderAfterData(searchDTO);
        return HomeReportConvert.INSTANCE.convortHomePagesOrderAfterData0(data);
    }

    @Override
    public HomePagesSkuDataRespDTO getHomePagesSkuData(HomePagesReqVO reqVO) {
        // 处理过滤查询条件
        checkSearchCondition(reqVO, NumberPool.LONG_ONE);

        HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
        // idKEY: 时间_平台商_运营商_入驻商
        searchDTO.setId(StringUtils.format("{}_{}_{}_{}", reqVO.getDateId(), reqVO.getSysCode(), reqVO.getDcId(), reqVO.getSupplierId()));
        EsHomePagesSkuData data = esHomePagesSkuDataService.searchHomePagesSkuData(searchDTO);
        return HomeReportConvert.INSTANCE.convortHomePagesSkuData0(data);
    }

    @Override
    public List<HomePagesSalesTop10DataRespDTO> getHomePagesAreaSalesTop10Data(HomePagesReqVO reqVO) {
        // 处理过滤查询条件
        checkSearchCondition(reqVO, NumberPool.LOWER_GROUND_LONG);

        // 调用订单模块API查询实时数据
        HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
        searchDTO.setSalesType("area")
                .setSysCode(reqVO.getSysCode())
                .setDateId(reqVO.getDateId())
                .setStartDate(reqVO.getStartDate()).setEndDate(reqVO.getEndDate());
        ;
        List<EsHomePagesSalesTop10Data> data = esHomePagesSalesTop10DataService.searchHomePagesSalesTop10Data(searchDTO);
        return HomeReportConvert.INSTANCE.convortHomePagesSalesTop10Data0(data);
    }

    @Override
    public List<HomePagesSalesTop10DataRespDTO> getHomePagesCategroySalesTop10Data(HomePagesReqVO reqVO) {
        // 处理过滤查询条件
        checkSearchCondition(reqVO, NumberPool.LOWER_GROUND_LONG);

        HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
        searchDTO.setSalesType("category")
                .setSysCode(reqVO.getSysCode())
                .setDateId(reqVO.getDateId())
                .setStartDate(reqVO.getStartDate()).setEndDate(reqVO.getEndDate());

        List<EsHomePagesSalesTop10Data> data = esHomePagesSalesTop10DataService.searchHomePagesSalesTop10Data(searchDTO);
        return HomeReportConvert.INSTANCE.convortHomePagesSalesTop10Data0(data);
    }

    @Override
    public List<HomePagesSalesTop10DataRespDTO> getHomePagesDcSalesTop10Data(HomePagesReqVO reqVO) {
        // 处理过滤查询条件
        checkSearchCondition(reqVO, NumberPool.LOWER_GROUND_LONG);

        HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
        searchDTO.setSalesType("dc")
                .setSysCode(reqVO.getSysCode())
                .setDateId(reqVO.getDateId())
                .setStartDate(reqVO.getStartDate()).setEndDate(reqVO.getEndDate());
        List<EsHomePagesSalesTop10Data> data = esHomePagesSalesTop10DataService.searchHomePagesSalesTop10Data(searchDTO);
        return HomeReportConvert.INSTANCE.convortHomePagesSalesTop10Data0(data);
    }

    @Override
    public List<HomePagesSalesTop10DataRespDTO> getHomePagesItemSalesTop10Data(HomePagesReqVO reqVO) {
        // 处理过滤查询条件
        checkSearchCondition(reqVO, NumberPool.LOWER_GROUND_LONG);

        HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
        searchDTO.setSalesType("item")
                .setSysCode(reqVO.getSysCode())
                .setDcId(reqVO.getDcId())
                .setSupplierId(reqVO.getSupplierId())
                .setDateId(reqVO.getDateId())
                .setStartDate(reqVO.getStartDate()).setEndDate(reqVO.getEndDate());
        List<EsHomePagesSalesTop10Data> data = esHomePagesSalesTop10DataService.searchHomePagesSalesTop10Data(searchDTO);
        return HomeReportConvert.INSTANCE.convortHomePagesSalesTop10Data0(data);
    }

    @Override
    public List<HomePagesSalesTop10DataRespDTO> getHomePagesSupplierSalesTop10Data(HomePagesReqVO reqVO) {
        // 处理过滤查询条件
        checkSearchCondition(reqVO, NumberPool.LOWER_GROUND_LONG);

        HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
        searchDTO.setSalesType("supplier")
                .setSysCode(reqVO.getSysCode())
                .setDcId(reqVO.getDcId())
                .setDateId(reqVO.getDateId())
                .setStartDate(reqVO.getStartDate()).setEndDate(reqVO.getEndDate());
        List<EsHomePagesSalesTop10Data> data = esHomePagesSalesTop10DataService.searchHomePagesSalesTop10Data(searchDTO);
        return HomeReportConvert.INSTANCE.convortHomePagesSalesTop10Data0(data);
    }

    @Override
    public List<HomePagesSalesTop10DataRespDTO> getHomePagesColonelSalesTop10Data(HomePagesReqVO reqVO) {
        // 处理过滤查询条件
        checkSearchCondition(reqVO, NumberPool.LOWER_GROUND_LONG);

        HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
        searchDTO.setSalesType("colonel")
                .setSysCode(reqVO.getSysCode())
                .setDcId(reqVO.getDcId())
                .setDateId(reqVO.getDateId())
                .setStartDate(reqVO.getStartDate()).setEndDate(reqVO.getEndDate());
        List<EsHomePagesSalesTop10Data> data = esHomePagesSalesTop10DataService.searchHomePagesSalesTop10Data(searchDTO);
        return HomeReportConvert.INSTANCE.convortHomePagesSalesTop10Data0(data);
    }

    @Override
    public List<HomePagesSalesTop10DataRespDTO> getHomePagesBranchSalesTop10Data(HomePagesReqVO reqVO) {
        // 处理过滤查询条件
        checkSearchCondition(reqVO, NumberPool.LOWER_GROUND_LONG);

        HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
        searchDTO.setSalesType("branch")
                .setSysCode(reqVO.getSysCode())
                .setDcId(reqVO.getDcId())
                .setSupplierId(reqVO.getSupplierId())
                .setDateId(reqVO.getDateId())
                .setStartDate(reqVO.getStartDate()).setEndDate(reqVO.getEndDate());
        List<EsHomePagesSalesTop10Data> data = esHomePagesSalesTop10DataService.searchHomePagesSalesTop10Data(searchDTO);
        return HomeReportConvert.INSTANCE.convortHomePagesSalesTop10Data0(data);
    }


    private void checkSearchCondition(HomePagesReqVO reqVO, Long salesPageNullLong) {

        if (reqVO.getStartDate() != null && reqVO.getStartDate().contains("-")) {
            reqVO.setStartDate(StringUtils.replace(reqVO.getStartDate(), "-", ""));
        }
        if (reqVO.getEndDate() != null && reqVO.getEndDate().contains("-")) {
            reqVO.setEndDate(StringUtils.replace(reqVO.getEndDate(), "-", ""));
        }

        // 平台商
        reqVO.setSysCode(ToolUtil.isEmptyReturn(SecurityUtils.getLoginUser().getSysCode(), NumberPool.LONG_ONE));
        // 运营商
        if (ToolUtil.isEmpty(reqVO.getDcId())) {
            reqVO.setDcId(ToolUtil.isEmptyReturn(SecurityUtils.getDcId(), salesPageNullLong));
        }
        // 入驻商 -- 平台、运营角色查看入驻商首页报表时，需要将运营商ID重置为 1， 不然查看不到数据。
        if (ToolUtil.isEmpty(reqVO.getSupplierId())) {
            reqVO.setSupplierId(ToolUtil.isEmptyReturn(SecurityUtils.getSupplierId(), salesPageNullLong));
        } else {
            reqVO.setDcId(salesPageNullLong);
        }

    }
}
