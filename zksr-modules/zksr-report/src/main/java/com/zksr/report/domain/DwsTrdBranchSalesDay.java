package com.zksr.report.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 交易域门店粒度下单日汇总对象 dws_trd_branch_sales_day
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@TableName(value = "dws_trd_branch_sales_day")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DwsTrdBranchSalesDay extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 日期ID */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dateId;

    /** 平台商id */
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 区域ID */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 门店ID */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 下单金额;订单成交金额（有效订单） */
    @Excel(name = "下单金额;订单成交金额", readConverterExp = "有=效订单")
    private BigDecimal orderAmt;

    /** 订单数量 */
    @Excel(name = "订单数量")
    private Integer orderQty;

    /** 动销SKU数;有效订单金额&gt;0 的sku数量 */
    @Excel(name = "动销SKU数;有效订单金额&gt;0 的sku数量")
    private Integer dxSkuQty;

    /** 动销SPU数;有效订单金额&gt;0 的spu数量 */
    @Excel(name = "动销SPU数;有效订单金额&gt;0 的spu数量")
    private Integer dxSpuQty;

    /** 动销门店数;有效订单金额&gt;0 的门店数量 */
    @Excel(name = "动销门店数;有效订单金额&gt;0 的门店数量")
    private Integer dxBranchQty;

    /** 入驻商总成本金额;有效订单的成本金额 */
    @Excel(name = "入驻商总成本金额;有效订单的成本金额")
    private BigDecimal supplierCostAmt;

    /** 总分润金额;有效订单的分润金额 */
    @Excel(name = "总分润金额;有效订单的分润金额")
    private BigDecimal profitAmt;

    /** 平台商分润;有效订单的平台商分润金额 */
    @Excel(name = "平台商分润;有效订单的平台商分润金额")
    private BigDecimal profitPartnerAmt;

    /** 运营商分润;有效订单的运营商分润金额 */
    @Excel(name = "运营商分润;有效订单的运营商分润金额")
    private BigDecimal profitDcAmt;

    /** 业务员分润;有效订单的业务员分润金额 */
    @Excel(name = "业务员分润;有效订单的业务员分润金额")
    private BigDecimal profitColonelAmt;

    /** 一级业务员分润;有效订单的一级业务员分润 */
    @Excel(name = "一级业务员分润;有效订单的一级业务员分润")
    private BigDecimal profitColonel1Amt;

    /** 二级业务员分润;有效订单的二级业务员分润 */
    @Excel(name = "二级业务员分润;有效订单的二级业务员分润")
    private BigDecimal profitColonel2Amt;

    /** 原订单金额;pre_order_amt = order_amt + discount_amt */
    @Excel(name = "原订单金额;pre_order_amt = order_amt + discount_amt")
    private BigDecimal preOrderAmt;

    /** 活动优惠金额(分摊的)（有效订单） */
    @Excel(name = "活动优惠金额(分摊的)", readConverterExp = "有=效订单")
    private BigDecimal activityDiscountAmt;

    /** 优惠金额(分摊的)（有效订单） */
    @Excel(name = "优惠金额(分摊的)", readConverterExp = "有=效订单")
    private BigDecimal couponDiscountAmt;

    /** 优惠金额(不分摊的)（有效订单） */
    @Excel(name = "优惠金额(不分摊的)", readConverterExp = "有=效订单")
    private BigDecimal couponDiscountAmt2;

    /** 订单优惠金额;有效订单的订单优惠金额 */
    @Excel(name = "订单优惠金额;有效订单的订单优惠金额")
    private BigDecimal discountAmt;

    /** 销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100 */
    @Excel(name = "销售毛利率;(order_amt - supplier_cost_amt) / order_amt * 100")
    private BigDecimal profitRate;

    /** 收货后售后金额（收货后售后订单） */
    @Excel(name = "收货后售后金额", readConverterExp = "收=货后售后订单")
    private BigDecimal returnAmt;

    /** 退单数量;收货后售后 */
    @Excel(name = "退单数量;收货后售后")
    private Integer returnQty;

    /** 售后SKU数;收货后售后 的sku数量 */
    @Excel(name = "售后SKU数;收货后售后 的sku数量")
    private Integer returnSkuQty;

    /** 售后SPU数;收货后售后 的spu数量 */
    @Excel(name = "售后SPU数;收货后售后 的spu数量")
    private Integer returnSpuQty;

    /** 售后门店数;收货后售后 的门店数量 */
    @Excel(name = "售后门店数;收货后售后 的门店数量")
    private Integer returnBranchQty;

    /** 售后入驻商总成本金额;收货后售后订单的成本金额 */
    @Excel(name = "售后入驻商总成本金额;收货后售后订单的成本金额")
    private BigDecimal returnSupplierCostAmt;

    /** 售后总分润金额; 收货后售后 */
    @Excel(name = "售后总分润金额; 收货后售后")
    private BigDecimal returnProfitAmt;

    /** 售后平台商分润;收货后售后 */
    @Excel(name = "售后平台商分润;收货后售后")
    private BigDecimal returnProfitPartnerAmt;

    /** 售后运营商分润;收货后售后 */
    @Excel(name = "售后运营商分润;收货后售后")
    private BigDecimal returnProfitDcAmt;

    /** 售后业务员分润;收货后售后 */
    @Excel(name = "售后业务员分润;收货后售后")
    private BigDecimal returnProfitColonelAmt;

    /** 售后一级业务员分润;收货后售后 */
    @Excel(name = "售后一级业务员分润;收货后售后")
    private BigDecimal returnProfitColonel1Amt;

    /** 售后二级业务员分润;收货后售后 */
    @Excel(name = "售后二级业务员分润;收货后售后")
    private BigDecimal returnProfitColonel2Amt;

    /** 原售后金额;pre_return_amt = return_amt + discount_amt */
    @Excel(name = "原售后金额;pre_return_amt = return_amt + discount_amt")
    private BigDecimal preReturnAmt;

    /** 活动优惠金额(分摊的)收货后售后 */
    @Excel(name = "活动优惠金额(分摊的)收货后售后")
    private BigDecimal returnActivityDiscountAmt;

    /** 优惠金额(分摊的)收货后售后 */
    @Excel(name = "优惠金额(分摊的)收货后售后")
    private BigDecimal returnCouponDiscountAmt;

    /** 优惠金额(不分摊的)收货后售后 */
    @Excel(name = "优惠金额(不分摊的)收货后售后")
    private BigDecimal returnCouponDiscountAmt2;

    /** 订单优惠金额;收货后售后 */
    @Excel(name = "订单优惠金额;收货后售后")
    private BigDecimal returnDiscountAmt;

    /** 售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100 */
    @Excel(name = "售后毛利率;(return_amt - return_supplier_cost_amt) / return_amt * 100")
    private BigDecimal returnProfitRate;

}
