package com.zksr.report.service.impl.handler;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.elasticsearch.domain.EsHomePagesBranchData;
import com.zksr.common.elasticsearch.domain.EsHomePagesOrderSalesData;
import com.zksr.common.elasticsearch.model.dto.HomePagesSearchDTO;
import com.zksr.common.elasticsearch.service.EsHomePagesBranchDataService;
import com.zksr.common.elasticsearch.service.EsHomePagesOrderSalesDataService;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.report.api.homePages.dto.HomePagesBranchDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.report.convert.homeReport.HomeReportConvert;
import com.zksr.report.mapper.HomePagesReportMapper;
import com.zksr.report.service.handler.RptHomePagesDataHandlerService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月26日 10:33
 * @description: PC 主页数据—— 门店变动信息
 */
@Component
@Order(RptHomePagesDataHandlerService.BRANCH_CHANGE_DATA)
@Slf4j
@SuppressWarnings("all")
public class RptHomePagesBranchDataHandlerServiceImpl implements RptHomePagesDataHandlerService {
    @Autowired
    private BranchApi branchApi;
    @Autowired
    private DcApi dcApi;
    @Autowired
    private AreaApi areaApi;
    @Autowired
    private HomePagesReportMapper homePagesReportMapper;

    @Autowired
    private EsHomePagesBranchDataService esHomePagesBranchDataService;
    @Autowired
    private EsHomePagesOrderSalesDataService esHomePagesOrderSalesDataService;
    @Override
    public void dayDataStatistics(HomePagesReqVO reqVO) {
        // 查询平台商 门店数据
        reqVO.setIsSupplier(NumberPool.INT_ZERO)
                .setIsDc(NumberPool.INT_ZERO);
        List<HomePagesBranchDataRespDTO> dataPartnerList = branchApi.getHomePagesBranchData(reqVO).getCheckedData();

        // 查询平台商、所有区域的 门店数据
        reqVO.setIsDc(NumberPool.INT_ONE)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesBranchDataRespDTO> dataAreaList = branchApi.getHomePagesBranchData(reqVO).getCheckedData();

        saveBatch(reqVO, dataPartnerList, dataAreaList);
    }

    @Override
    public void monthDataStatistics(HomePagesReqVO reqVO) {
        // 查询平台商数据
        reqVO.setIsDc(NumberPool.INT_ZERO)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesBranchDataRespDTO> dataPartnerList = homePagesReportMapper.getHomePagesBranchData(reqVO);

        // 查询平台商、所有区域的 门店数据
        reqVO.setIsDc(NumberPool.INT_ONE)
                .setIsSupplier(NumberPool.INT_ZERO);
        List<HomePagesBranchDataRespDTO> dataAreaList = homePagesReportMapper.getHomePagesBranchData(reqVO);

        saveBatch(reqVO, dataPartnerList, dataAreaList);


    }

    private void saveBatch(HomePagesReqVO reqVO, List<HomePagesBranchDataRespDTO> dataPartnerList, List<HomePagesBranchDataRespDTO> dataAreaList) {
        // 根据平台商编号获取该平台下所有得运营商
        List<DcDTO> dcList = dcApi.getDcBySysCode(reqVO.getSysCode()).getCheckedData();
        // 根据平台商编号获取该平台下所有得区域城市
        List<AreaDTO> areaList = areaApi.getListBySysCode(reqVO.getSysCode()).getCheckedData();
        // 根据运营商ID分组区域城市
        Map<Long, Set<Long>> dcAreaMap = areaList.stream()
                .filter(area -> ToolUtil.isNotEmpty(area.getDcId()))
                .collect(Collectors.groupingBy(AreaDTO::getDcId, Collectors.mapping(AreaDTO::getAreaId, Collectors.toSet())));

        // 根据查询 组装数据
        List<HomePagesBranchDataRespDTO> dataDcList = dcList.stream().map(dc -> {
            Set<Long> areaSet = dcAreaMap.get(dc.getDcId());
            HomePagesBranchDataRespDTO branchDataRespDTO = new HomePagesBranchDataRespDTO();
            branchDataRespDTO.setSysCode(reqVO.getSysCode())
                    .setDcId(dc.getDcId())
            ;
            dataAreaList.stream().filter(dataArea -> ToolUtil.isNotEmpty(areaSet) && areaSet.contains(dataArea.getAreaId()))
                    .forEach(dataArea -> {
                        branchDataRespDTO.setBranchAddQty(branchDataRespDTO.getBranchAddQty() + dataArea.getBranchAddQty())
                                .setBeforeBranchAddQty(branchDataRespDTO.getBeforeBranchAddQty() + dataArea.getBeforeBranchAddQty())
                                .setBranchTotalQty(branchDataRespDTO.getBranchTotalQty() + dataArea.getBranchTotalQty())
                                .setBeforeBranchTotalQty(branchDataRespDTO.getBeforeBranchTotalQty() + dataArea.getBeforeBranchTotalQty())
                                .setColonelTotalQty(branchDataRespDTO.getColonelTotalQty() + dataArea.getColonelTotalQty())
                                .setVisitBranchQty(branchDataRespDTO.getVisitBranchQty() + dataArea.getVisitBranchQty())
                                .setBeforeVisitBranchQty(branchDataRespDTO.getBeforeVisitBranchQty() + dataArea.getBeforeVisitBranchQty())
                                .setLoginBranchQty(branchDataRespDTO.getLoginBranchQty() + dataArea.getLoginBranchQty())
                                .setBeforeLoginBranchQty(branchDataRespDTO.getBeforeLoginBranchQty() + dataArea.getBeforeLoginBranchQty())
                        ;
                    });
            return branchDataRespDTO;
        }).collect(Collectors.toList());

        List<HomePagesBranchDataRespDTO> mergedList = new ArrayList<>();
        mergedList.addAll(dataPartnerList);
        mergedList.addAll(dataDcList);

        List<EsHomePagesBranchData> esHomePagesData = HomeReportConvert.INSTANCE.convortEsHomePagesBranchData(mergedList);

        esHomePagesData.forEach(data -> {
            // 获取当前天、月的销售数据
            HomePagesSearchDTO searchDTO = new HomePagesSearchDTO();
            searchDTO.setId(StringUtils.format("{}_{}_{}_{}", reqVO.getDateId(), reqVO.getSysCode(), ToolUtil.isEmptyReturn(data.getDcId(), 1L), 1L));
            EsHomePagesOrderSalesData orderData = esHomePagesOrderSalesDataService.searchHomePagesOrderSalesData(searchDTO);
            if (ToolUtil.isNotEmpty(orderData)) {
                data.setBranchSalesQty(orderData.getOrderBranchQty())
                        .setBeforeBranchSalesQty(orderData.getBeforeOrderBranchQty())
                ;
            }



            data.setDateId(reqVO.getDateId())
                    .setRefreshEsTime(DateUtils.getNowDate())
                    .setId(StringUtils.format("{}_{}_{}", reqVO.getDateId(), reqVO.getSysCode(), ToolUtil.isEmptyReturn(data.getDcId(), 1L)))
                    // 新增门店同比上升/下降率 = 新增门店数量 - 上次新增门店数量 / 上次新增门店数量 * 100
                    .setBranchAddRate(
                            BigDecimal.valueOf(data.getBranchAddQty() - data.getBeforeBranchAddQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeBranchAddQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
//                                    .setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                    )
                    // 动销门店同比上升/下降率 = 动销门店数量 - 上次动销门店数量 / 上次动销门店数量 * 100
                    .setBranchSalesRate(
                            BigDecimal.valueOf(data.getBranchSalesQty() - data.getBeforeBranchSalesQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeBranchSalesQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
//                                    .setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                    )
                    // 动销率 = 动销门店数量 / 总计门店数量 * 100
                    .setSalesRate(
                            BigDecimal.valueOf(data.getBranchSalesQty()).divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBranchTotalQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
//                                    .setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                    )
                    // 总计门店数量同比上升/下降率 = 总计门店数量 - 上次总计门店数量 / 上次总计门店数量 * 100
                    .setBranchTotalRate(
                            BigDecimal.valueOf(data.getBranchTotalQty() - data.getBeforeBranchTotalQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeBranchTotalQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // 登录门店数量同比上升/下降率 = 登录门店数量 - 上次登录门店数量 / 上次登录门店数量 * 100
                    .setLoginBranchRate(
                            BigDecimal.valueOf(data.getLoginBranchQty() - data.getBeforeLoginBranchQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeLoginBranchQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // 拜访门店数量同比上升/下降率 = 拜访门店数量 - 上次拜访门店数量 / 上次拜访门店数量 * 100
                    .setVisitBranchRate(
                            BigDecimal.valueOf(data.getVisitBranchQty() - data.getBeforeVisitBranchQty())
                                    .divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBeforeVisitBranchQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
                    // 拜访门店数量率 = 拜访门店数量 / 总计门店数量 * 100
                    .setVisitRate(
                            BigDecimal.valueOf(data.getVisitBranchQty()).divide(BigDecimal.valueOf(ToolUtil.isEmptyOrZeroReturn(data.getBranchTotalQty(), NumberPool.LONG_ONE)), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    )
            ;
        });
        // ES批量保存
        esHomePagesBranchDataService.saveBatchHomePagesBranchData(esHomePagesData);

    }
}
