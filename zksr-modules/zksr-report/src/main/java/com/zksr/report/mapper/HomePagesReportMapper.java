package com.zksr.report.mapper;

import com.zksr.report.api.homePages.dto.*;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface HomePagesReportMapper {
    /**
     *  获取首页订单销售数据
     * @param reqVO
     * @return
     */
    List<HomePagesOrderSalesDataRespDTO> getHomePagesOrderSalesData(HomePagesReqVO reqVO);

    /**
     * 获取PC首页门店数据
     * @param reqVO
     * @return
     */
    List<HomePagesBranchDataRespDTO> getHomePagesBranchData(HomePagesReqVO reqVO);

    /**
     *  获取订单退货销售数据
     * @param reqVO
     * @return
     */
    List<HomePagesOrderAfterDataRespDTO> getHomePagesOrderAfterData(HomePagesReqVO reqVO);

    /**
     * 获取PC首页SKU数据  TODO  暂无数据
     * 注：这里查询运营商上架数据时需单独查询，不可根据区域城市进行分组查询结果，因一个商品SKU可以上架到同一个运营商下的不同的区域城市下。
     * @param reqVO
     * @return
     */
    List<HomePagesSkuDataRespDTO> getHomePagesSkuData(HomePagesReqVO reqVO);

    /**
     * 查询区域销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesAreaSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 查询运营商销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesDcSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 查询商品销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesItemSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 查询一级品类销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesCategorySalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 查询入驻商销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesSupplierSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 查询业务员销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesColonelSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 查询门店销售数据TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesBranchSalesTop10Data(HomePagesReqVO reqVO);

    /**
     * 获取PC首页SKU数据 - 查询一级管理分类总数
     * @param reqVO
     * @return
     */
    HomePagesSkuDataRespDTO getHomePagesCategoryData(HomePagesReqVO reqVO);
}