package com.zksr.report.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.report.domain.DwsTrdBranchSalesMonth;
import com.zksr.report.controller.branchSaleMonth.vo.DwsTrdBranchSalesMonthPageReqVO;
import com.zksr.report.controller.branchSaleMonth.vo.DwsTrdBranchSalesMonthSaveReqVO;

/**
 * 交易域门店粒度下单月汇总Service接口
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
public interface IDwsTrdBranchSalesMonthService {

    /**
     * 新增交易域门店粒度下单月汇总
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertDwsTrdBranchSalesMonth(@Valid DwsTrdBranchSalesMonthSaveReqVO createReqVO);

    /**
     * 修改交易域门店粒度下单月汇总
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateDwsTrdBranchSalesMonth(@Valid DwsTrdBranchSalesMonthSaveReqVO updateReqVO);

    /**
     * 删除交易域门店粒度下单月汇总
     *
     * @param monthId 日期ID
     */
    public void deleteDwsTrdBranchSalesMonth(Long monthId);

    /**
     * 批量删除交易域门店粒度下单月汇总
     *
     * @param monthIds 需要删除的交易域门店粒度下单月汇总主键集合
     * @return 结果
     */
    public void deleteDwsTrdBranchSalesMonthByMonthIds(Long[] monthIds);

    /**
     * 获得交易域门店粒度下单月汇总
     *
     * @param monthId 日期ID
     * @return 交易域门店粒度下单月汇总
     */
    public DwsTrdBranchSalesMonth getDwsTrdBranchSalesMonth(Long monthId);

    /**
     * 获得交易域门店粒度下单月汇总分页
     *
     * @param pageReqVO 分页查询
     * @return 交易域门店粒度下单月汇总分页
     */
    PageResult<DwsTrdBranchSalesMonth> getDwsTrdBranchSalesMonthPage(DwsTrdBranchSalesMonthPageReqVO pageReqVO);

}
