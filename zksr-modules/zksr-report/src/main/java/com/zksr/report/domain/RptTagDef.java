package com.zksr.report.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 标签定义对象 rpt_tag_def
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@TableName(value = "rpt_tag_def")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RptTagDef extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 标签定义id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long tagDefId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 标签名 */
    @Excel(name = "标签名")
    private String tagName;

    /** 标签规则json */
    @Excel(name = "标签规则json")
    private String tagRuleJson;

    /** 是否启用 1-是 0-否 */
    @Excel(name = "是否启用 1-是 0-否")
    private Integer enabled;

    /** 标签类型（数据字典） */
    @Excel(name = "标签类型", readConverterExp = "数=据字典")
    private String tagType;

    /** 标签分类,(客户活跃, 客户等级, 类占比) */
    @Excel(name = "标签分类,(客户活跃, 客户等级, 类占比)")
    private String tagCategory;

    public void setTag(BranchTagEnum tag) {
        this.tagName = tag.getName();
        this.tagType = tag.getTag();
        this.tagCategory = tag.getType();
    }
}
