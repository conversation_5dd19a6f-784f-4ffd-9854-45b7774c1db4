package com.zksr.report.service.impl.handler;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.elasticsearch.domain.EsHomePagesSalesTop10Data;
import com.zksr.common.elasticsearch.service.EsHomePagesSalesTop10DataService;
import com.zksr.product.api.catgory.CatgoryApi;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.report.convert.homeReport.HomeReportConvert;
import com.zksr.report.mapper.HomePagesReportMapper;
import com.zksr.report.service.IReportCacheService;
import com.zksr.report.service.handler.RptHomePagesDataHandlerService;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月26日 10:33
 * @description: PC首页 一级品类销售TOP10数据
 */
@Component
@Order(RptHomePagesDataHandlerService.CATEGORY_SALES_TOP10_DATA)
@Slf4j
@SuppressWarnings("all")
public class RptHomePagesCategorySalesTop10DataHandlerServiceImpl implements RptHomePagesDataHandlerService {
    @Autowired
    private OrderApi orderApi;
    @Autowired
    private CatgoryApi catgoryApi;
    @Autowired
    private HomePagesReportMapper homePagesReportMapper;

    @Autowired
    private EsHomePagesSalesTop10DataService esHomePagesSalesTop10DataService;

    @Autowired
    private IReportCacheService reportCacheService;
    @Override
    public void dayDataStatistics(HomePagesReqVO reqVO) {
        // 查询平台商 一级管理分类销售数据
        reqVO.setIsSupplier(NumberPool.INT_ZERO)
                .setIsDc(NumberPool.INT_ZERO)
                .setSalesType("category");
        List<HomePagesSalesTop10DataRespDTO> dataPartnerList = new ArrayList<>();
        // 根据平台商查询出所有的管理分类
        List<CatgoryDTO> catgoryList = catgoryApi.getListBySysCode(reqVO.getSysCode()).getCheckedData();

        // 过滤出二、三级管理分类，并且按照上级管理分类ID进行分组
        Map<Long, List<CatgoryDTO>> catgoryMap = catgoryList.stream()
                .filter(category -> Objects.equals(category.getLevel(), NumberPool.INT_TWO) || Objects.equals(category.getLevel(), NumberPool.INT_THREE))
                .collect(Collectors.groupingBy(CatgoryDTO::getPid));
        catgoryList.stream().filter(category -> Objects.equals(category.getLevel(), NumberPool.INT_ONE))
                .forEach(category -> {
                    HomePagesSalesTop10DataRespDTO categoryTop10Data = new HomePagesSalesTop10DataRespDTO();
                    categoryTop10Data.setSalesTypeId(category.getCatgoryId())
                            .setSysCode(reqVO.getSysCode())
                    ;

                    reqVO.setCategoryIds(new HashSet<>());
                    List<CatgoryDTO> catgory2List = catgoryMap.get(category.getCatgoryId());
                    if (ToolUtil.isNotEmpty(catgory2List) && !catgory2List.isEmpty()) {
                        catgory2List.forEach(category2 -> {
                            List<CatgoryDTO> catgory3List = catgoryMap.get(category2.getCatgoryId());
                            if (ToolUtil.isNotEmpty(catgory3List) && !catgory3List.isEmpty()) {
                                reqVO.getCategoryIds().addAll(
                                        catgory3List.stream().map(CatgoryDTO::getCatgoryId).collect(Collectors.toSet())
                                );
                            }
                        });
                    }
                    if (!reqVO.getCategoryIds().isEmpty()) {
                        List<HomePagesSalesTop10DataRespDTO> dataList = orderApi.getHomePagesSalesTop10Data(reqVO).getCheckedData();
                        if (ToolUtil.isNotEmpty(dataList) && !dataList.isEmpty()) {
                            dataList.forEach(data -> {
                                categoryTop10Data.setOrderSalesAmt(categoryTop10Data.getOrderSalesAmt().add(data.getOrderSalesAmt()))
                                        .setBeforeOrderSalesAmt(categoryTop10Data.getBeforeOrderSalesAmt().add(data.getBeforeOrderSalesAmt()))
                                ;
                            });
                            dataPartnerList.add(categoryTop10Data);
                        }
                    }
                });

        saveBatch(reqVO, dataPartnerList);
    }

    @Override
    public void monthDataStatistics(HomePagesReqVO reqVO) {
        // 查询平台商 一级管理分类销售数据
        reqVO.setIsSupplier(NumberPool.INT_ZERO)
                .setIsDc(NumberPool.INT_ZERO)
                .setSalesType("category");
        List<HomePagesSalesTop10DataRespDTO> dataPartnerList = new ArrayList<>();

        List<HomePagesSalesTop10DataRespDTO> dataList = homePagesReportMapper.getHomePagesCategorySalesTop10Data(reqVO);
        if (ToolUtil.isNotEmpty(dataList) && !dataList.isEmpty()) {
            dataPartnerList.addAll(
                    dataList.stream().map(categoryData-> {
                        HomePagesSalesTop10DataRespDTO categoryTop10Data = new HomePagesSalesTop10DataRespDTO();
                        categoryTop10Data.setSalesTypeId(categoryData.getSalesTypeId())
                                .setSysCode(reqVO.getSysCode())
                                .setOrderSalesAmt(categoryTop10Data.getOrderSalesAmt().add(categoryData.getOrderSalesAmt()))
                                .setBeforeOrderSalesAmt(categoryTop10Data.getBeforeOrderSalesAmt().add(categoryData.getBeforeOrderSalesAmt()))
                        ;
                        return categoryTop10Data;
                    }).collect(Collectors.toList())
            );
        }


        saveBatch(reqVO, dataPartnerList);
    }

    private void saveBatch(HomePagesReqVO reqVO, List<HomePagesSalesTop10DataRespDTO> dataPartnerList) {
        List<EsHomePagesSalesTop10Data> esHomePagesData = HomeReportConvert.INSTANCE.convortEsHomePagesSalesTop10Data(dataPartnerList);
        esHomePagesData.forEach(data -> {
            CatgoryDTO catgoryDTO = reportCacheService.getCatgoryDTO(data.getSalesTypeId());
            data.setDateId( reqVO.getDateId())
                    .setSalesTypeName(ToolUtil.isNotEmpty(catgoryDTO) ? catgoryDTO.getCatgoryName() : data.getSalesTypeId() + "")
                    .setRefreshEsTime(DateUtils.getNowDate())
                    .setSalesType("category")
                    .setDcId(ToolUtil.isEmptyReturn(data.getDcId(), NumberPool.LOWER_GROUND_LONG))
                    .setSupplierId(ToolUtil.isEmptyReturn(data.getSupplierId(), NumberPool.LOWER_GROUND_LONG))
                    .setId(StringUtils.format("{}_{}_{}_{}_{}_{}", reqVO.getDateId(), reqVO.getSysCode(), ToolUtil.isEmptyReturn(data.getDcId(), 1L), ToolUtil.isEmptyReturn(data.getSupplierId(), 1L), data.getSalesTypeId(), data.getSalesType()));
        });
        // ES批量保存
        esHomePagesSalesTop10DataService.saveBatchHomePagesSalesTop10Data(esHomePagesData);
    }
}
