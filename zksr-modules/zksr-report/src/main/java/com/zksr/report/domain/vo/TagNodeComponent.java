package com.zksr.report.domain.vo;

import com.yomahub.liteflow.core.NodeComponent;
import com.zksr.common.core.enums.ConditionEnum;
import com.zksr.report.api.branch.dto.BranchLevelConfigDTO;
import com.zksr.report.mapper.RptTagDefMapper;
import com.zksr.report.slot.BranchTagContext;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/14 9:40
 */
public abstract class TagNodeComponent extends NodeComponent {


    @Autowired
    protected RptTagDefMapper rptTagDefMapper;

    /**
     *
     * @param levelConfig
     * @param val   X值
     * @return
     */
    public boolean condition(BranchLevelConfigDTO levelConfig, BigDecimal val) {
        // 判断条件
        ConditionEnum condition = levelConfig.getCondition();
        // 如果是介于需要使用 valA valB 来判断
        BigDecimal valA = levelConfig.getValA();
        BigDecimal valB = levelConfig.getValB();
        switch (condition) {
            case GT:
                return val.compareTo(valA) > 0;
            case LT:
                return val.compareTo(valA) < 0;
            case GE:
                return val.compareTo(valA) >= 0;
            case LE:
                return val.compareTo(valA) <= 0;
            case BT:
                return val.compareTo(valA) >= 0 && val.compareTo(valB) <= 0;
            case BTL:
                return val.compareTo(valA) >= 0 && val.compareTo(valB) < 0;
            case BTG:
                return val.compareTo(valA) > 0 && val.compareTo(valB) <= 0;
            default:
                return false;
        }
    }

    public BranchTagContext getContext() {
        return this.getSlot().getRequestData();
    }
}
