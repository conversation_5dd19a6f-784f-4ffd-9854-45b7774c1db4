package com.zksr.report.api.export;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.report.api.export.dto.BranchSalesSummaryExportDTO;
import com.zksr.report.api.export.dto.ColonelSalesSummaryExportDTO;
import com.zksr.report.api.export.vo.BranchSalesSummaryExportPageVO;
import com.zksr.report.api.export.vo.ColonelSalesSummaryExportPageVO;
import com.zksr.report.service.IBranchSalesSummaryService;
import com.zksr.report.service.IColonelSalesSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class RptExportApiImpl implements RptExportApi{

    @Autowired
    private IBranchSalesSummaryService branchSalesSummaryService;
    @Autowired
    private IColonelSalesSummaryService colonelSalesSummaryService;
    @Override
    public CommonResult<List<BranchSalesSummaryExportDTO>> getMonthBranchSalesSummaryExport(BranchSalesSummaryExportPageVO reqVo) {
        return CommonResult.success(branchSalesSummaryService.branchSalesMonthExport(reqVo));
    }

    @Override
    public CommonResult<List<ColonelSalesSummaryExportDTO>> getMonthColonelSalesSummaryExport(ColonelSalesSummaryExportPageVO reqVo) {
        return CommonResult.success(colonelSalesSummaryService.monthColonelSalesSummaryExport(reqVo));
    }
}
