package com.zksr.report.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.report.domain.AdsBranchTradeWeekdayDistribution;
import com.zksr.report.controller.branchTradeWeekday.vo.AdsBranchTradeWeekdayDistributionPageReqVO;
import com.zksr.report.controller.branchTradeWeekday.vo.AdsBranchTradeWeekdayDistributionSaveReqVO;

/**
 * 门店周下单分布月Service接口
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
public interface IAdsBranchTradeWeekdayDistributionService {

    /**
     * 新增门店周下单分布月
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertAdsBranchTradeWeekdayDistribution(@Valid AdsBranchTradeWeekdayDistributionSaveReqVO createReqVO);

    /**
     * 修改门店周下单分布月
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAdsBranchTradeWeekdayDistribution(@Valid AdsBranchTradeWeekdayDistributionSaveReqVO updateReqVO);

    /**
     * 删除门店周下单分布月
     *
     * @param monthId 月份ID
     */
    public void deleteAdsBranchTradeWeekdayDistribution(Long monthId);

    /**
     * 批量删除门店周下单分布月
     *
     * @param monthIds 需要删除的门店周下单分布月主键集合
     * @return 结果
     */
    public void deleteAdsBranchTradeWeekdayDistributionByMonthIds(Long[] monthIds);

    /**
     * 获得门店周下单分布月
     *
     * @param monthId 月份ID
     * @return 门店周下单分布月
     */
    public AdsBranchTradeWeekdayDistribution getAdsBranchTradeWeekdayDistribution(Long monthId);

    /**
     * 获得门店周下单分布月分页
     *
     * @param pageReqVO 分页查询
     * @return 门店周下单分布月分页
     */
    PageResult<AdsBranchTradeWeekdayDistribution> getAdsBranchTradeWeekdayDistributionPage(AdsBranchTradeWeekdayDistributionPageReqVO pageReqVO);

}
