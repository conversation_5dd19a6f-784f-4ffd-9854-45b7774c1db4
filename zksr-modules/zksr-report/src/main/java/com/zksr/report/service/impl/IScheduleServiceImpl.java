package com.zksr.report.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LFUCache;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.vo.BranchListForReqVO;
import com.zksr.report.api.branch.dto.BranchTagConfigDTO;
import com.zksr.report.controller.schedule.vo.ComputeBranchTagReqVO;
import com.zksr.report.controller.tagDefinit.vo.RptTagDefRespVO;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.service.IAdsBranchTagMonthService;
import com.zksr.report.service.IRptTagDefService;
import com.zksr.report.service.IScheduleService;
import com.zksr.report.slot.BranchTagContext;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/13 14:19
 */
@Slf4j
@Service
public class IScheduleServiceImpl implements IScheduleService {

    @Resource
    private BranchApi branchApi;

    @Resource
    private PartnerApi partnerApi;

    @Resource
    private FlowExecutor flowExecutor;

    @Autowired
    private IRptTagDefService rptTagDefService;

    @Autowired
    private IAdsBranchTagMonthService branchTagMonthService;

    @Override
    public void computeBranchTag(ComputeBranchTagReqVO computeBranchTagReqVO) {
        // 验证参数
        // 如果没有指定月, 那默认使用上月
        Long monthId = StringUtils.isNotEmpty(computeBranchTagReqVO.getMonthDate())
                ? Long.parseLong(computeBranchTagReqVO.getMonthDate()) : Long.parseLong(DateUtil.format(DateUtil.offsetMonth(DateUtil.date(), -1), "yyyyMM"));

        if (Objects.nonNull(computeBranchTagReqVO.getBranchId()) ) {
            // 如果是单门店
            BranchDTO branchDTO = branchApi.getByBranchId(computeBranchTagReqVO.getBranchId()).getCheckedData();
            // 执行数据
            executeTagProcess(monthId, branchDTO);
        } else {
            for (PartnerDto partnerDto : partnerApi.getPartnerInfo().getCheckedData()) {
                log.info("开始处理{}平台商门店标签计算", partnerDto.getPartnerName());

                // 获取平台商客户标签定义
                RptTagDefRespVO tagDefRespVO = rptTagDefService.getRptTagDefSysCode(partnerDto.getSysCode());
                if (Objects.isNull(tagDefRespVO)) {
                    log.info("{}平台商, 没有配置客户标签, 跳过计算", partnerDto.getPartnerName());
                    continue;
                }
                BranchListForReqVO reqVO = new BranchListForReqVO();
                reqVO.setSize(200L);
                reqVO.setSysCode(partnerDto.getSysCode());
                for (;;) {
                    List<BranchDTO> branchDTOList = branchApi.getBranchListForBy(reqVO).getCheckedData();
                    if (branchDTOList.isEmpty()) {
                        break;
                    }
                    // 执行数据
                    executeTagProcess(monthId, branchDTOList, tagDefRespVO);
                    // 更新下一批次数据
                    reqVO.setMinId(branchDTOList.get(branchDTOList.size() - 1).getBranchId());
                }
                log.info("结束处理{}平台商门店标签计算", partnerDto.getPartnerName());
            }

        }
    }

    @Override
    public void executeTagProcess(Long monthId, BranchDTO branchDTO) {
        this.executeTagProcess(monthId, ListUtil.toList(branchDTO), rptTagDefService.getRptTagDefSysCode(branchDTO.getSysCode()));
    }

    @Override
    public void executeTagProcess(Long monthId, List<BranchDTO> branchDTOList, BranchTagConfigDTO config) {
        BranchTagContext context = new BranchTagContext();
        context.setMonthId(monthId);
        context.setBranchList(branchDTOList, monthId);
        context.setTagConfig(config);
        executeTagProcess(context);
    }

    @Override
    public void executeTagProcess(BranchTagContext context) {
        // 数据会自动更新到 context里面, 只需要处理出来就行了
        flowExecutor.execute2Resp("branchTagChain", context, BranchTagContext.class);
        // 保存流执行结果
        branchTagMonthService.insertAdsBranchTagMonth(new ArrayList<>(context.getTagMonthMap().values()));
    }
}
