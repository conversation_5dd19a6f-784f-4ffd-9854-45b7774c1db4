package com.zksr.report.service;

import com.zksr.report.api.homePages.dto.*;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;

import java.util.List;

public interface IHomePageService {

    /**
     *  刷新ES首页报表数据 - day
     * @param sysCode
     */
    void refreshEsHomePagesDataJob(Long sysCode);

    /**
     *  刷新ES首页报表数据 - month
     * @param sysCode
     */
    void refreshEsHomePagesMonthDataJob(Long sysCode, String startDate, String endDate);

    /**
     * 获取首页当前销售、欠款、退单实时数据
     * @param reqVO
     * @return
     */
    HomePagesCurrentSalesDataRespDTO getCurrentSalesData(HomePagesReqVO reqVO);

    /**
     *  获取PC首页订单销售数据
     * @param reqVO
     * @return
     */
    HomePagesOrderSalesDataRespDTO getOrderSalesData(HomePagesReqVO reqVO);

    /**
     * 获取PC首页门店数据
     * @param reqVO
     * @return
     */
    HomePagesBranchDataRespDTO getHomePagesBranchData(HomePagesReqVO reqVO);

    /**
     *  获取订单退货销售数据
     * @param reqVO
     * @return
     */
    HomePagesOrderAfterDataRespDTO getOrderAfterData(HomePagesReqVO reqVO);

    /**
     *  获取订单退货销售数据
     * @param reqVO
     * @return
     */
    HomePagesSkuDataRespDTO getHomePagesSkuData(HomePagesReqVO reqVO);




    /**
     *  获取区域销售TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesAreaSalesTop10Data(HomePagesReqVO reqVO);

    /**
     *  获取一级品类销售TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesCategroySalesTop10Data(HomePagesReqVO reqVO);

    /**
     *  获取运营商销售TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesDcSalesTop10Data(HomePagesReqVO reqVO);

    /**
     *  获取商品销售TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesItemSalesTop10Data(HomePagesReqVO reqVO);


    /**
     *  获取入驻商销售TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesSupplierSalesTop10Data(HomePagesReqVO reqVO);

    /**
     *  获取业务员销售TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesColonelSalesTop10Data(HomePagesReqVO reqVO);

    /**
     *  获取门店销售TOP10数据
     * @param reqVO
     * @return
     */
    List<HomePagesSalesTop10DataRespDTO> getHomePagesBranchSalesTop10Data(HomePagesReqVO reqVO);






}


