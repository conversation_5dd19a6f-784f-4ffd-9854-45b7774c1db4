package com.zksr.report.api.branch;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zksr.common.core.enums.WeekEnum;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.report.api.branch.dto.BranchTagConfigDTO;
import com.zksr.report.api.branch.vo.*;
import com.zksr.report.convert.BranchTagMonth.AdsBranchTagMonthConvert;
import com.zksr.report.convert.branchTradeWeekday.AdsBranchTradeWeekdayDistributionConvert;
import com.zksr.report.domain.AdsBranchCat1OrderamtStatsMonth;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.domain.AdsBranchTradeWeekdayDistribution;
import com.zksr.report.domain.vo.BranchTagTotalVO;
import com.zksr.report.mapper.AdsBranchTagMonthMapper;
import com.zksr.report.mapper.AdsBranchTradeWeekdayDistributionMapper;
import com.zksr.report.mapper.ColonelRptMapper;
import com.zksr.report.mapper.DwsTrdBranchSalesMonthMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 报表门店
 * @date 2024/11/13 9:02
 */
@ApiIgnore
@InnerAuth
@RestController
public class RptBranchApiImpl implements RptBranchApi{

    @Autowired
    private DwsTrdBranchSalesMonthMapper branchSalesMonthMapper;

    @Autowired
    private ColonelRptMapper colonelRptMapper;

    @Autowired
    private AdsBranchTradeWeekdayDistributionMapper weekdayDistributionMapper;

    @Autowired
    private AdsBranchTagMonthMapper branchTagMonthMapper;

    @Override
    public CommonResult<Boolean> saveBranchTagDTO(BranchTagConfigDTO branchTagConfigDTO) {
        return null;
    }

    @Override
    public CommonResult<List<BranchTagEnum>> getBranchLastMonthTag(Long branchId) {
        // 上月
        String yyyyMM = DateUtil.format(DateUtil.offsetMonth(DateUtil.date(), -1), DatePattern.SIMPLE_MONTH_PATTERN);
        AdsBranchTagMonth adsBranchTagMonth = branchTagMonthMapper.selectBranchByMonthId(branchId, Integer.parseInt(yyyyMM));
        List<BranchTagEnum> tagEnums = new ArrayList<>();
        if (Objects.nonNull(adsBranchTagMonth)) {
            // 活跃客户
            if (StringUtils.isNotEmpty(adsBranchTagMonth.getActiveTagType())) {
                tagEnums.add(BranchTagEnum.ACTIVE_CUST);
            }
            // 客户等级
            tagEnums.add(BranchTagEnum.fromValue(adsBranchTagMonth.getLevelTagType()));
            // 类占比
            tagEnums.add(BranchTagEnum.fromValue(adsBranchTagMonth.getLzLevelTagType()));
            // 利润比
            tagEnums.add(BranchTagEnum.fromValue(adsBranchTagMonth.getProfitLevelTagType()));
            // 订货频次
            tagEnums.add(BranchTagEnum.fromValue(adsBranchTagMonth.getFreqLevelTagType()));
        }
        // 过滤空值
        tagEnums = tagEnums.stream().filter(Objects::nonNull).collect(Collectors.toList());
        return success(tagEnums);
    }

    @Override
    public CommonResult<BigDecimal> totalColonelBranchSale(RptColonelBranchReqVO reqVO) {
        return success(branchSalesMonthMapper.selectTotalColonelBranchSale(reqVO));
    }

    @Override
    public CommonResult<ColonelIndexBranchLevelTotalRespVO> indexBranchLevelTotal(RptColonelBranchReqVO reqVO) {
        ColonelIndexBranchLevelTotalRespVO respVO = new ColonelIndexBranchLevelTotalRespVO();
        respVO.setTotalBranchNum(colonelRptMapper.countByColonel(reqVO.getColonelIdList()));
        respVO.setTotalBranchSaleAmt(colonelRptMapper.sumMonthSaleAmtByColonel(reqVO.getColonelIdList(), reqVO.getMonthId()));
        respVO.setActiveBranchNum(colonelRptMapper.countMonthActiveByColonel(reqVO.getColonelIdList(), reqVO.getMonthId()));
        respVO.setProfit(colonelRptMapper.sumMonthColonelProfitByColonel(reqVO.getColonelIdList(), reqVO.getMonthId()));
        // 获取等级标签数据
        List<BranchTagTotalVO> tagMonthList = colonelRptMapper.selectMonthBranchTagByColonel(reqVO.getColonelIdList(), reqVO.getMonthId());
        Map<String, BranchTagTotalVO> tagTotalVOMap = tagMonthList.stream().collect(Collectors.toMap(BranchTagTotalVO::getBranchTag, item -> item));
        List<BranchTagEnum> levelList = BranchTagEnum.getLevelList();
        for (BranchTagEnum tagEnum : levelList) {
            if (tagTotalVOMap.containsKey(tagEnum.getTag())) {
                respVO.getLevelTagList().add(new ColonelIndexBranchLevelTotalRespVO.LevelTag(tagEnum, tagTotalVOMap.get(tagEnum.getTag()).getCnt(), tagTotalVOMap.get(tagEnum.getTag()).getSaleAmt()));
            } else {
                respVO.getLevelTagList().add(new ColonelIndexBranchLevelTotalRespVO.LevelTag(tagEnum, NumberPool.LONG_ZERO, BigDecimal.ZERO));
            }
        }
        // 计算利润比
        if (NumberUtil.isGreater(respVO.getTotalBranchSaleAmt(), BigDecimal.ZERO)) {
            respVO.setProfitOdd(respVO.getProfit().divide(respVO.getTotalBranchSaleAmt(), 2, RoundingMode.UP));
        } else {
            respVO.setProfitOdd(BigDecimal.ZERO);
        }
        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<ColonelBranchMonthWeekTotalRespVO> indexWeekSaleTotal(RptColonelBranchReqVO reqVO) {
        // 获取数据
        AdsBranchTradeWeekdayDistribution tradeWeekdayDistribution = colonelRptMapper.selectSumSaleWeek(reqVO.getColonelIdList(), reqVO.getMonthId());
        if (Objects.isNull(tradeWeekdayDistribution)) {
            tradeWeekdayDistribution = new AdsBranchTradeWeekdayDistribution();
        }
        // 封装列表数据
        ColonelBranchMonthWeekTotalRespVO respVO = new ColonelBranchMonthWeekTotalRespVO();
        List<ColonelBranchMonthWeekTotalRespVO.ChatLine> weekList = new ArrayList<>();
        for (WeekEnum weekEnum : WeekEnum.values()) {
            switch (weekEnum) {
                case MONDAY:
                    weekList.add(new ColonelBranchMonthWeekTotalRespVO.ChatLine(
                            weekEnum.getZh(),
                            tradeWeekdayDistribution.getMondayDxBranchQty(),
                            tradeWeekdayDistribution.getMondayOrderQty(),
                            tradeWeekdayDistribution.getMondayOrderAmt()
                    ));
                    break;
                case TUESDAY:
                    weekList.add(new ColonelBranchMonthWeekTotalRespVO.ChatLine(
                            weekEnum.getZh(),
                            tradeWeekdayDistribution.getTuesdayDxBranchQty(),
                            tradeWeekdayDistribution.getTuesdayOrderQty(),
                            tradeWeekdayDistribution.getTuesdayOrderAmt()
                    ));
                    break;
                case WEDNESDAY:
                    weekList.add(new ColonelBranchMonthWeekTotalRespVO.ChatLine(
                            weekEnum.getZh(),
                            tradeWeekdayDistribution.getWednesdayDxBranchQty(),
                            tradeWeekdayDistribution.getWednesdayOrderQty(),
                            tradeWeekdayDistribution.getWednesdayOrderAmt()
                    ));
                    break;
                case THURSDAY:
                    weekList.add(new ColonelBranchMonthWeekTotalRespVO.ChatLine(
                            weekEnum.getZh(),
                            tradeWeekdayDistribution.getThursdayDxBranchQty(),
                            tradeWeekdayDistribution.getThursdayOrderQty(),
                            tradeWeekdayDistribution.getThursdayOrderAmt()
                    ));
                    break;
                case FRIDAY:
                    weekList.add(new ColonelBranchMonthWeekTotalRespVO.ChatLine(
                            weekEnum.getZh(),
                            tradeWeekdayDistribution.getFridayDxBranchQty(),
                            tradeWeekdayDistribution.getFridayOrderQty(),
                            tradeWeekdayDistribution.getFridayOrderAmt()
                    ));
                    break;
                case SATURDAY:
                    weekList.add(new ColonelBranchMonthWeekTotalRespVO.ChatLine(
                            weekEnum.getZh(),
                            tradeWeekdayDistribution.getSaturdayDxBranchQty(),
                            tradeWeekdayDistribution.getSaturdayOrderQty(),
                            tradeWeekdayDistribution.getSaturdayOrderAmt()
                    ));
                    break;
                case SUNDAY:
                    weekList.add(new ColonelBranchMonthWeekTotalRespVO.ChatLine(
                            weekEnum.getZh(),
                            tradeWeekdayDistribution.getSundayDxBranchQty(),
                            tradeWeekdayDistribution.getSundayOrderQty(),
                            tradeWeekdayDistribution.getSundayOrderAmt()
                    ));
                    break;
            }
        }
        respVO.setWeekList(weekList);
        return success(respVO);
    }

    @Override
    public CommonResult<ColonelBranchSaleCategoryTotalRespVO> indexSaleCategoryTotal(RptColonelBranchReqVO reqVO) {
        // 获取数据
        List<AdsBranchCat1OrderamtStatsMonth> saleCategoryList = colonelRptMapper.selectSaleCategoryList(reqVO.getColonelIdList(), reqVO.getMonthId());
        ColonelBranchSaleCategoryTotalRespVO respVO = new ColonelBranchSaleCategoryTotalRespVO();
        saleCategoryList.forEach(item -> {
            respVO.getCategoryList().add(new CategorySaleTotalVO(item.getCat1Id(), item.getBranchOrderAmt()));
        });
        return success(respVO);
    }

    @Override
    public CommonResult<ColonelLevelTotalRespVO> branchLevelColonelList(ColonelBranchTagPageReqVO reqVO) {
        Long tagCnt = colonelRptMapper.countMonthBranchTagByColonelAndLevelTag(reqVO.getColonelIdList(), reqVO.getMonthId(), reqVO.getBranchTag().getTag());
        if (reqVO.getPageSize() > 0) {
            PageHelper.startPage(reqVO.getPageNo(), reqVO.getPageSize(), false);
        }
        List<BranchTagTotalVO> tagMonthList = colonelRptMapper.selectMonthBranchTagByColonelAndLevelTag(reqVO.getColonelIdList(), reqVO.getMonthId(), reqVO.getBranchTag().getTag());
        // 组装返回数据
        ColonelLevelTotalRespVO respVO = new ColonelLevelTotalRespVO(tagCnt);
        for (BranchTagTotalVO tagTotalVO : tagMonthList) {
            respVO.getList().add(AdsBranchTagMonthConvert.INSTANCE.convertColonelTotal(tagTotalVO));
        }
        return success(respVO);
    }

    @Override
    public CommonResult<PageResult<ColonelTagBranchRespVO>> tagLevelBranchList(TagBranchListReqVO reqVO) {
        if (Objects.nonNull(reqVO.getPageSize()) && reqVO.getPageSize() > 0) {
            Page<ColonelTagBranchRespVO> page = PageHelper.startPage(reqVO.getPageNo(), reqVO.getPageSize(), false);
            List<ColonelTagBranchRespVO> list = colonelRptMapper.selectTagBranchList(reqVO);
            return success(PageResult.result(page, list));
        }
        List<ColonelTagBranchRespVO> list = colonelRptMapper.selectTagBranchList(reqVO);
        return success(new PageResult<>(list, (long) list.size()));
    }

    @Override
    public CommonResult<BranchAnalyseInfoRespVO> branchAnalyseInfo(BranchAnalyseInfoReqVO reqVO) {
        BranchAnalyseInfoRespVO respVO = new BranchAnalyseInfoRespVO();

        // 获取当前来源月
        DateTime monthDate = DateUtil.parse(reqVO.getMonthDate(), DatePattern.NORM_MONTH_PATTERN);

        // 近一年销售数据
        List<BranchTargetProcessVO> targetProcessVOS = colonelRptMapper.selectBranchSaleByYear(reqVO.getBranchId(), monthDate.year());
        respVO.setTargetProcesses(targetProcessVOS);

        // 月订货习惯
        respVO.setWeekdayDistribution(AdsBranchTradeWeekdayDistributionConvert.INSTANCE.convert(weekdayDistributionMapper.selectBranchByMonthId(reqVO.getBranchId(), reqVO.getMonthId())));

        // 月标签数据
        respVO.setTagMonthRespVO(AdsBranchTagMonthConvert.INSTANCE.convert(branchTagMonthMapper.selectBranchByMonthId(reqVO.getBranchId(), reqVO.getMonthId())));

        // 门店一级管理分类订货占比
        // 获取数据
        List<AdsBranchCat1OrderamtStatsMonth> saleCategoryList = colonelRptMapper.selectBranchSaleCategoryList(reqVO.getBranchId(), reqVO.getMonthId());
        saleCategoryList.forEach(item -> {
            respVO.getBranchCategorySaleList().add(new CategorySaleTotalVO(item.getCat1Id(), item.getBranchOrderAmt()));
        });

        // 24小时下单分布
        // 获取数据
        List<HoursSaleVO> saleVOList = colonelRptMapper.selectBranchHoursSaleList(reqVO.getBranchId(), reqVO.getMonthId());
        Map<Integer, List<HoursSaleVO>> hoursMap = saleVOList.stream().collect(Collectors.groupingBy(item -> Integer.parseInt(item.getHour())));
        List<HoursSaleVO> hoursSaleVOList = new ArrayList<>();
        for (int i = 1; i < 25; i++) {
            if (hoursMap.containsKey(i)) {
                hoursSaleVOList.add(hoursMap.get(i).get(0));
            } else {
                HoursSaleVO hoursSaleVO = new HoursSaleVO();
                hoursSaleVO.setHour(i <= 9 ? ("0" + i) : String.valueOf(i));
                hoursSaleVOList.add(hoursSaleVO);
            }
        }
        respVO.setHoursSaleList(hoursSaleVOList);
        return success(respVO);
    }

    @Override
    public CommonResult<ColonelHoursSaleRespVO> getHoursSaleCount(RptColonelBranchReqVO reqVO) {
        // 获取数据
        List<HoursSaleVO> saleVOList = colonelRptMapper.selectColonelBranchHoursSaleList(reqVO.getColonelIdList(), reqVO.getMonthId());
        Map<Integer, List<HoursSaleVO>> hoursMap = saleVOList.stream().collect(Collectors.groupingBy(item -> Integer.parseInt(item.getHour())));
        List<HoursSaleVO> resultList = new ArrayList<>();
        for (int i = 1; i < 25; i++) {
            if (hoursMap.containsKey(i)) {
                resultList.add(hoursMap.get(i).get(0));
            } else {
                HoursSaleVO hoursSaleVO = new HoursSaleVO();
                hoursSaleVO.setHour(i <= 9 ? ("0" + i) : String.valueOf(i));
                resultList.add(hoursSaleVO);
            }
        }
        return success(new ColonelHoursSaleRespVO(resultList));
    }
}
