package com.zksr.report.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.report.domain.AdsBranchCat1OrderamtStatsMonth;
import com.zksr.report.controller.branchCategoryOrderAmt.vo.AdsBranchCat1OrderamtStatsMonthPageReqVO;
import com.zksr.report.controller.branchCategoryOrderAmt.vo.AdsBranchCat1OrderamtStatsMonthSaveReqVO;

/**
 * 门店一级类别销售金额统计月Service接口
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
public interface IAdsBranchCat1OrderamtStatsMonthService {

    /**
     * 新增门店一级类别销售金额统计月
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertAdsBranchCat1OrderamtStatsMonth(@Valid AdsBranchCat1OrderamtStatsMonthSaveReqVO createReqVO);

    /**
     * 修改门店一级类别销售金额统计月
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAdsBranchCat1OrderamtStatsMonth(@Valid AdsBranchCat1OrderamtStatsMonthSaveReqVO updateReqVO);

    /**
     * 删除门店一级类别销售金额统计月
     *
     * @param monthId 月份ID
     */
    public void deleteAdsBranchCat1OrderamtStatsMonth(Long monthId);

    /**
     * 批量删除门店一级类别销售金额统计月
     *
     * @param monthIds 需要删除的门店一级类别销售金额统计月主键集合
     * @return 结果
     */
    public void deleteAdsBranchCat1OrderamtStatsMonthByMonthIds(Long[] monthIds);

    /**
     * 获得门店一级类别销售金额统计月
     *
     * @param monthId 月份ID
     * @return 门店一级类别销售金额统计月
     */
    public AdsBranchCat1OrderamtStatsMonth getAdsBranchCat1OrderamtStatsMonth(Long monthId);

    /**
     * 获得门店一级类别销售金额统计月分页
     *
     * @param pageReqVO 分页查询
     * @return 门店一级类别销售金额统计月分页
     */
    PageResult<AdsBranchCat1OrderamtStatsMonth> getAdsBranchCat1OrderamtStatsMonthPage(AdsBranchCat1OrderamtStatsMonthPageReqVO pageReqVO);

}
