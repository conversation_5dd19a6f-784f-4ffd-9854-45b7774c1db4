package com.zksr.report.controller.branchSalesSummary.dto;

import cn.hutool.db.PageResult;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("门店月销售汇总 -  Request VO")
public class BranchSalesSummaryQueryDTO extends PageParam {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "选择查询月份，格式为 yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM")
    private Date monthId; // 查询月份，格式为 yyyy-MM

    @ApiModelProperty(value = "门店编号")
    private String branchId; // 门店编号

    @ApiModelProperty(value = "门店名称")
    private String branchName; // 门店名称

    //平台商编号
    private Long sysCode;
    //本月月份
    private String currentMonthId;
    //上个月份
    private String previousMonthId;
}