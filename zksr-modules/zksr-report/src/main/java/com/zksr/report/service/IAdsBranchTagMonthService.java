package com.zksr.report.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.controller.branchTagMonth.vo.AdsBranchTagMonthPageReqVO;
import com.zksr.report.controller.branchTagMonth.vo.AdsBranchTagMonthSaveReqVO;

import java.util.List;

/**
 * 门店标签月Service接口
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public interface IAdsBranchTagMonthService {

    /**
     * 新增门店标签月
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertAdsBranchTagMonth(@Valid AdsBranchTagMonthSaveReqVO createReqVO);


    /**
     * 批量新增门店标签月
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public void insertAdsBranchTagMonth(List<AdsBranchTagMonth> tagMonths);

    /**
     * 修改门店标签月
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAdsBranchTagMonth(@Valid AdsBranchTagMonthSaveReqVO updateReqVO);

    /**
     * 删除门店标签月
     *
     * @param monthId 月份ID
     */
    public void deleteAdsBranchTagMonth(Long monthId);

    /**
     * 批量删除门店标签月
     *
     * @param monthIds 需要删除的门店标签月主键集合
     * @return 结果
     */
    public void deleteAdsBranchTagMonthByMonthIds(Long[] monthIds);

    /**
     * 获得门店标签月
     *
     * @param monthId 月份ID
     * @return 门店标签月
     */
    public AdsBranchTagMonth getAdsBranchTagMonth(Long monthId);

    /**
     * 获得门店标签月分页
     *
     * @param pageReqVO 分页查询
     * @return 门店标签月分页
     */
    PageResult<AdsBranchTagMonth> getAdsBranchTagMonthPage(AdsBranchTagMonthPageReqVO pageReqVO);

}
