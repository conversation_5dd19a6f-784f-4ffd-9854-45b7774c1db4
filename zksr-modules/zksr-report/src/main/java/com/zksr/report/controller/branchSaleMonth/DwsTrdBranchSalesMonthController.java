package com.zksr.report.controller.branchSaleMonth;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.report.domain.DwsTrdBranchSalesMonth;
import com.zksr.report.service.IDwsTrdBranchSalesMonthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.report.controller.branchSaleMonth.vo.DwsTrdBranchSalesMonthPageReqVO;
import com.zksr.report.controller.branchSaleMonth.vo.DwsTrdBranchSalesMonthSaveReqVO;
import com.zksr.report.controller.branchSaleMonth.vo.DwsTrdBranchSalesMonthRespVO;
import com.zksr.report.convert.BranchSaleMonth.DwsTrdBranchSalesMonthConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 交易域门店粒度下单月汇总Controller
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Api(tags = "管理后台 - 交易域门店粒度下单月汇总接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/branchSaleMonth")
public class DwsTrdBranchSalesMonthController {
    @Autowired
    private IDwsTrdBranchSalesMonthService dwsTrdBranchSalesMonthService;

    /**
     * 新增交易域门店粒度下单月汇总
     */
    @ApiOperation(value = "新增交易域门店粒度下单月汇总", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "交易域门店粒度下单月汇总", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody DwsTrdBranchSalesMonthSaveReqVO createReqVO) {
        return success(dwsTrdBranchSalesMonthService.insertDwsTrdBranchSalesMonth(createReqVO));
    }

    /**
     * 修改交易域门店粒度下单月汇总
     */
    @ApiOperation(value = "修改交易域门店粒度下单月汇总", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "交易域门店粒度下单月汇总", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody DwsTrdBranchSalesMonthSaveReqVO updateReqVO) {
            dwsTrdBranchSalesMonthService.updateDwsTrdBranchSalesMonth(updateReqVO);
        return success(true);
    }

    /**
     * 删除交易域门店粒度下单月汇总
     */
    @ApiOperation(value = "删除交易域门店粒度下单月汇总", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "交易域门店粒度下单月汇总", businessType = BusinessType.DELETE)
    @DeleteMapping("/{monthIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] monthIds) {
        dwsTrdBranchSalesMonthService.deleteDwsTrdBranchSalesMonthByMonthIds(monthIds);
        return success(true);
    }

    /**
     * 获取交易域门店粒度下单月汇总详细信息
     */
    @ApiOperation(value = "获得交易域门店粒度下单月汇总详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{monthId}")
    public CommonResult<DwsTrdBranchSalesMonthRespVO> getInfo(@PathVariable("monthId") Long monthId) {
        DwsTrdBranchSalesMonth dwsTrdBranchSalesMonth = dwsTrdBranchSalesMonthService.getDwsTrdBranchSalesMonth(monthId);
        return success(DwsTrdBranchSalesMonthConvert.INSTANCE.convert(dwsTrdBranchSalesMonth));
    }

    /**
     * 分页查询交易域门店粒度下单月汇总
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得交易域门店粒度下单月汇总分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<DwsTrdBranchSalesMonthRespVO>> getPage(@Valid DwsTrdBranchSalesMonthPageReqVO pageReqVO) {
        PageResult<DwsTrdBranchSalesMonth> pageResult = dwsTrdBranchSalesMonthService.getDwsTrdBranchSalesMonthPage(pageReqVO);
        return success(DwsTrdBranchSalesMonthConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "report:branchSaleMonth:add";
        /** 编辑 */
        public static final String EDIT = "report:branchSaleMonth:edit";
        /** 删除 */
        public static final String DELETE = "report:branchSaleMonth:remove";
        /** 列表 */
        public static final String LIST = "report:branchSaleMonth:list";
        /** 查询 */
        public static final String GET = "report:branchSaleMonth:query";
        /** 停用 */
        public static final String DISABLE = "report:branchSaleMonth:disable";
        /** 启用 */
        public static final String ENABLE = "report:branchSaleMonth:enable";
    }
}
