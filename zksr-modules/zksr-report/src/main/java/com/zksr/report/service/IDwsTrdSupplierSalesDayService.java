package com.zksr.report.service;

import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.report.controller.dwsSupplierSales.dto.DwsTrdSupplierSalesDayRespDTO;
import com.zksr.report.controller.dwsSupplierSales.vo.DwsTrdSupplierSalesDayPageReqVO;

/**
 * 交易域入驻商粒度下单日汇总Service接口
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
public interface IDwsTrdSupplierSalesDayService {
    /**
     * 获得交易域入驻商粒度下单日汇总分页
     *
     * @param pageReqVO 分页查询
     * @return 交易域入驻商粒度下单日汇总分页
     */
    PageResult<DwsTrdSupplierSalesDayRespDTO> getDwsTrdSupplierSalesDayPage(DwsTrdSupplierSalesDayPageReqVO pageReqVO);

}
