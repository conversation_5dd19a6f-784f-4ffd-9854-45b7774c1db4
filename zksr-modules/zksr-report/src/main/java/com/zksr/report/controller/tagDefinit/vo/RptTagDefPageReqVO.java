package com.zksr.report.controller.tagDefinit.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 标签定义对象 rpt_tag_def
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@ApiModel("标签定义 - rpt_tag_def分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RptTagDefPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 标签定义id */
    @ApiModelProperty(value = "标签类型")
    private Long tagDefId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 标签名 */
    @Excel(name = "标签名")
    @ApiModelProperty(value = "标签名")
    private String tagName;

    /** 标签规则json */
    @Excel(name = "标签规则json")
    @ApiModelProperty(value = "标签规则json")
    private String tagRuleJson;

    /** 是否启用 1-是 0-否 */
    @Excel(name = "是否启用 1-是 0-否")
    @ApiModelProperty(value = "是否启用 1-是 0-否")
    private Integer enabled;

    /** 标签类型（数据字典） */
    @Excel(name = "标签类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "标签类型")
    private String tagType;


}
