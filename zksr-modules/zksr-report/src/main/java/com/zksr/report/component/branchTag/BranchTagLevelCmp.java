package com.zksr.report.component.branchTag;

import com.zksr.common.core.enums.branch.BranchTagEnum;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.report.api.branch.dto.BranchLevelConfigDTO;
import com.zksr.report.api.branch.dto.BranchTagConfigDTO;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.domain.vo.BranchSaleVO;
import com.zksr.report.domain.vo.TagNodeComponent;
import com.zksr.report.slot.BranchTagContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店等级计算
 * @date 2024/11/14 9:05
 */
@Slf4j
@Component("branchTagLevelCmp")
public class BranchTagLevelCmp extends TagNodeComponent {

    @Override
    public void process() throws Exception {
        BranchTagContext context = this.getContext();
        // 标签配置
        BranchTagConfigDTO tagConfig = context.getTagConfig();
        if (Objects.isNull(tagConfig.getBranchLevelConfig())) {
            return;
        }

        // 获取门店ID集合
        List<Long> branchIds = context.getBranchList().stream().map(BranchDTO::getBranchId).collect(Collectors.toList());

        // 查询销售数据
        List<BranchSaleVO> saleVOList = rptTagDefMapper.selectByMonthSale(context.getMonthId(), branchIds);
        Map<Long, BranchSaleVO> saleMap = saleVOList.stream().collect(Collectors.toMap(BranchSaleVO::getBranchId, item -> item));

        // 排序, 从顶级依次向下处理
        tagConfig.getBranchLevelConfig().sort(Comparator.comparing(BranchLevelConfigDTO::getSort));

        for (Long branchId : branchIds) {
            BigDecimal saleAmt = BigDecimal.ZERO;
            if (saleMap.containsKey(branchId)) {
                saleAmt = saleMap.get(branchId).getSaleAmt();
            }

            // 已经获取到了销售额, 开始定级
            for (BranchLevelConfigDTO configDTO : tagConfig.getBranchLevelConfig()) {
                // 验证规则
                if (this.condition(configDTO, saleAmt)) {
                    AdsBranchTagMonth adsBranchTagMonth = context.getTagMonthMap().get(branchId);
                    BranchTagEnum branchTag = configDTO.getBranchTag();
                    // 客户等级标签类型;例：客户等级
                    adsBranchTagMonth.setLevelTagType(branchTag.getTag());
                    // 客户等级标签定义id;例：id
                    adsBranchTagMonth.setLevelTagDefId(configDTO.getTagDefId());
                    // 客户等级规则;例：自然月销售金额 >= 2000 元
                    if (configDTO.getCondition().isOne()) {
                        adsBranchTagMonth.setLevelTagRule(StringUtils.format("自然月销售金额{}{}", configDTO.getCondition().getCondition(), configDTO.getValA()));
                    } else {
                        adsBranchTagMonth.setLevelTagRule(
                                StringUtils.format("自然月销售金额{}",
                                configDTO.getCondition().getCondition()
                                        .replace("A", configDTO.getValA().toString())
                                        .replace("X", saleAmt.toString())
                                        .replace("B", configDTO.getValB().toString()))
                        );
                    }
                    // 客户等级标签名;例：A级
                    adsBranchTagMonth.setLevelTagName(branchTag.getName());
                    // 客户等级标签指标值;例：2100元
                    adsBranchTagMonth.setLevelTagVal(StringUtils.format("{}元", saleAmt.toString()));
                    // 跳到下一个门店
                    break;
                }
            }
        }
    }
}
