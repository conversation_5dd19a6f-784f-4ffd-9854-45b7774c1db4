package com.zksr.report.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 门店周下单分布月对象 ads_branch_trade_weekday_distribution
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@TableName(value = "ads_branch_trade_weekday_distribution")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdsBranchTradeWeekdayDistribution {
    private static final long serialVersionUID=1L;

    /** 月份ID */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long monthId;

    /** 平台商id */
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 区域城市id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 门店id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 周一动销门店数 */
    @Excel(name = "周一动销门店数")
    private Integer mondayDxBranchQty;

    /** 周一订单数量 */
    @Excel(name = "周一订单数量")
    private Integer mondayOrderQty;

    /** 周一订单金额 */
    @Excel(name = "周一订单金额")
    private BigDecimal mondayOrderAmt;

    /** 周二动销门店数 */
    @Excel(name = "周二动销门店数")
    private Integer tuesdayDxBranchQty;

    /** 周二订单数量 */
    @Excel(name = "周二订单数量")
    private Integer tuesdayOrderQty;

    /** 周二订单金额 */
    @Excel(name = "周二订单金额")
    private BigDecimal tuesdayOrderAmt;

    /** 周三动销门店数 */
    @Excel(name = "周三动销门店数")
    private Integer wednesdayDxBranchQty;

    /** 周三订单数量 */
    @Excel(name = "周三订单数量")
    private Integer wednesdayOrderQty;

    /** 周三订单金额 */
    @Excel(name = "周三订单金额")
    private BigDecimal wednesdayOrderAmt;

    /** 周四动销门店数 */
    @Excel(name = "周四动销门店数")
    private Integer thursdayDxBranchQty;

    /** 周四订单数量 */
    @Excel(name = "周四订单数量")
    private Integer thursdayOrderQty;

    /** 周四订单金额 */
    @Excel(name = "周四订单金额")
    private BigDecimal thursdayOrderAmt;

    /** 周五动销门店数 */
    @Excel(name = "周五动销门店数")
    private Integer fridayDxBranchQty;

    /** 周五订单数量 */
    @Excel(name = "周五订单数量")
    private Integer fridayOrderQty;

    /** 周五订单金额 */
    @Excel(name = "周五订单金额")
    private BigDecimal fridayOrderAmt;

    /** 周六动销门店数 */
    @Excel(name = "周六动销门店数")
    private Integer saturdayDxBranchQty;

    /** 周六订单数量 */
    @Excel(name = "周六订单数量")
    private Integer saturdayOrderQty;

    /** 周六订单金额 */
    @Excel(name = "周六订单金额")
    private BigDecimal saturdayOrderAmt;

    /** 周日动销门店数 */
    @Excel(name = "周日动销门店数")
    private Integer sundayDxBranchQty;

    /** 周日订单数量 */
    @Excel(name = "周日订单数量")
    private Integer sundayOrderQty;

    /** 周日订单金额 */
    @Excel(name = "周日订单金额")
    private BigDecimal sundayOrderAmt;

    /** 插入时间id */
    @Excel(name = "插入时间id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long insertDateId;

}
