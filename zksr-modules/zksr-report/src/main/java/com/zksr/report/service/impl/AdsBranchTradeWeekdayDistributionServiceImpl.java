package com.zksr.report.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.report.mapper.AdsBranchTradeWeekdayDistributionMapper;
import com.zksr.report.convert.branchTradeWeekday.AdsBranchTradeWeekdayDistributionConvert;
import com.zksr.report.domain.AdsBranchTradeWeekdayDistribution;
import com.zksr.report.controller.branchTradeWeekday.vo.AdsBranchTradeWeekdayDistributionPageReqVO;
import com.zksr.report.controller.branchTradeWeekday.vo.AdsBranchTradeWeekdayDistributionSaveReqVO;
import com.zksr.report.service.IAdsBranchTradeWeekdayDistributionService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.report.enums.ErrorCodeConstants.*;

/**
 * 门店周下单分布月Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Service
public class AdsBranchTradeWeekdayDistributionServiceImpl implements IAdsBranchTradeWeekdayDistributionService {
    @Autowired
    private AdsBranchTradeWeekdayDistributionMapper adsBranchTradeWeekdayDistributionMapper;

    /**
     * 新增门店周下单分布月
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertAdsBranchTradeWeekdayDistribution(AdsBranchTradeWeekdayDistributionSaveReqVO createReqVO) {
        // 插入
        AdsBranchTradeWeekdayDistribution adsBranchTradeWeekdayDistribution = AdsBranchTradeWeekdayDistributionConvert.INSTANCE.convert(createReqVO);
        adsBranchTradeWeekdayDistributionMapper.insert(adsBranchTradeWeekdayDistribution);
        // 返回
        return adsBranchTradeWeekdayDistribution.getMonthId();
    }

    /**
     * 修改门店周下单分布月
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateAdsBranchTradeWeekdayDistribution(AdsBranchTradeWeekdayDistributionSaveReqVO updateReqVO) {
        adsBranchTradeWeekdayDistributionMapper.updateById(AdsBranchTradeWeekdayDistributionConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除门店周下单分布月
     *
     * @param monthId 月份ID
     */
    @Override
    public void deleteAdsBranchTradeWeekdayDistribution(Long monthId) {
        // 删除
        adsBranchTradeWeekdayDistributionMapper.deleteById(monthId);
    }

    /**
     * 批量删除门店周下单分布月
     *
     * @param monthIds 需要删除的门店周下单分布月主键
     * @return 结果
     */
    @Override
    public void deleteAdsBranchTradeWeekdayDistributionByMonthIds(Long[] monthIds) {
        for(Long monthId : monthIds){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deleteAdsBranchTradeWeekdayDistribution(monthId);
        }
    }

    /**
     * 获得门店周下单分布月
     *
     * @param monthId 月份ID
     * @return 门店周下单分布月
     */
    @Override
    public AdsBranchTradeWeekdayDistribution getAdsBranchTradeWeekdayDistribution(Long monthId) {
        return adsBranchTradeWeekdayDistributionMapper.selectById(monthId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AdsBranchTradeWeekdayDistribution> getAdsBranchTradeWeekdayDistributionPage(AdsBranchTradeWeekdayDistributionPageReqVO pageReqVO) {
        return adsBranchTradeWeekdayDistributionMapper.selectPage(pageReqVO);
    }
}
