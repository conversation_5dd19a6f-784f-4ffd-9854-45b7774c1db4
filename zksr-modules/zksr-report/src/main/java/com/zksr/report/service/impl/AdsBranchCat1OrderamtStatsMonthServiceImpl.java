package com.zksr.report.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.report.mapper.AdsBranchCat1OrderamtStatsMonthMapper;
import com.zksr.report.convert.branchCategoryOrderAmt.AdsBranchCat1OrderamtStatsMonthConvert;
import com.zksr.report.domain.AdsBranchCat1OrderamtStatsMonth;
import com.zksr.report.controller.branchCategoryOrderAmt.vo.AdsBranchCat1OrderamtStatsMonthPageReqVO;
import com.zksr.report.controller.branchCategoryOrderAmt.vo.AdsBranchCat1OrderamtStatsMonthSaveReqVO;
import com.zksr.report.service.IAdsBranchCat1OrderamtStatsMonthService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.report.enums.ErrorCodeConstants.*;

/**
 * 门店一级类别销售金额统计月Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Service
public class AdsBranchCat1OrderamtStatsMonthServiceImpl implements IAdsBranchCat1OrderamtStatsMonthService {
    @Autowired
    private AdsBranchCat1OrderamtStatsMonthMapper adsBranchCat1OrderamtStatsMonthMapper;

    /**
     * 新增门店一级类别销售金额统计月
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertAdsBranchCat1OrderamtStatsMonth(AdsBranchCat1OrderamtStatsMonthSaveReqVO createReqVO) {
        // 插入
        AdsBranchCat1OrderamtStatsMonth adsBranchCat1OrderamtStatsMonth = AdsBranchCat1OrderamtStatsMonthConvert.INSTANCE.convert(createReqVO);
        adsBranchCat1OrderamtStatsMonthMapper.insert(adsBranchCat1OrderamtStatsMonth);
        // 返回
        return adsBranchCat1OrderamtStatsMonth.getMonthId();
    }

    /**
     * 修改门店一级类别销售金额统计月
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateAdsBranchCat1OrderamtStatsMonth(AdsBranchCat1OrderamtStatsMonthSaveReqVO updateReqVO) {
        adsBranchCat1OrderamtStatsMonthMapper.updateById(AdsBranchCat1OrderamtStatsMonthConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除门店一级类别销售金额统计月
     *
     * @param monthId 月份ID
     */
    @Override
    public void deleteAdsBranchCat1OrderamtStatsMonth(Long monthId) {
        // 删除
        adsBranchCat1OrderamtStatsMonthMapper.deleteById(monthId);
    }

    /**
     * 批量删除门店一级类别销售金额统计月
     *
     * @param monthIds 需要删除的门店一级类别销售金额统计月主键
     * @return 结果
     */
    @Override
    public void deleteAdsBranchCat1OrderamtStatsMonthByMonthIds(Long[] monthIds) {
        for(Long monthId : monthIds){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deleteAdsBranchCat1OrderamtStatsMonth(monthId);
        }
    }

    /**
     * 获得门店一级类别销售金额统计月
     *
     * @param monthId 月份ID
     * @return 门店一级类别销售金额统计月
     */
    @Override
    public AdsBranchCat1OrderamtStatsMonth getAdsBranchCat1OrderamtStatsMonth(Long monthId) {
        return adsBranchCat1OrderamtStatsMonthMapper.selectById(monthId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AdsBranchCat1OrderamtStatsMonth> getAdsBranchCat1OrderamtStatsMonthPage(AdsBranchCat1OrderamtStatsMonthPageReqVO pageReqVO) {
        return adsBranchCat1OrderamtStatsMonthMapper.selectPage(pageReqVO);
    }


}
