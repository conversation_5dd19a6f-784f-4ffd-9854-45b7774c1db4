package com.zksr.report.controller.skuSalesSummary;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.report.controller.skuSalesSummary.dto.SkuSalesSummaryDTO;
import com.zksr.report.controller.skuSalesSummary.dto.SkuSalesSummaryQueryDTO;
import com.zksr.report.service.ISkuSalesSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "管理后台 - 商品月销售汇总接口", produces = "application/json")
@RestController
@Validated
@RequestMapping("/skuSalesSummary")
public class SkuSalesSummaryController {

    @Autowired
    private ISkuSalesSummaryService skuSalesSummaryService;

    /**
     * 获取商品月销售汇总
     *
     * @param queryDTO 查询条件
     * @return 汇总数据列表
     */
    @PostMapping("/monthlySku")
    @ApiOperation("获取商品月销售汇总")
    @DataScope(dcAlias = "ts")
    public CommonResult<PageResult<SkuSalesSummaryDTO>> getMonthlySkuSalesSummary(@RequestBody SkuSalesSummaryQueryDTO queryDTO) {
        return CommonResult.success(skuSalesSummaryService.getMonthlySkuSalesSummary(queryDTO));
    }
}