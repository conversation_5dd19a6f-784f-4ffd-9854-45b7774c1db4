package com.zksr.report.component.branchTag;

import com.zksr.common.core.enums.branch.BranchTagEnum;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.report.api.branch.dto.BranchLevelConfigDTO;
import com.zksr.report.api.branch.dto.BranchTagConfigDTO;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.domain.vo.BranchFreqVO;
import com.zksr.report.domain.vo.TagNodeComponent;
import com.zksr.report.slot.BranchTagContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店订货频次
 * @date 2024/11/14 9:05
 */
@Slf4j
@Component("branchTagFreqCmp")
public class BranchTagFreqCmp extends TagNodeComponent {

    @Override
    public void process() throws Exception {
        BranchTagContext context = this.getContext();
        // 标签配置
        BranchTagConfigDTO tagConfig = context.getTagConfig();
        if (Objects.isNull(tagConfig.getBranchCreateOrderConfig())) {
            return;
        }

        // 获取门店ID集合
        List<Long> branchIds = context.getBranchList().stream().map(BranchDTO::getBranchId).collect(Collectors.toList());

        // 查询销售数据
        Integer startDay = Integer.parseInt(context.getMonthId() + "01");
        Integer endDay = Integer.parseInt(context.getMonthId() + "32");
        List<BranchFreqVO> saleVOList = rptTagDefMapper.selectByMonthFreqSaleDay(startDay, endDay, branchIds);
        Map<Long, BranchFreqVO> saleMap = saleVOList.stream().collect(Collectors.toMap(BranchFreqVO::getBranchId, item -> item));

        // 排序, 从顶级依次向下处理
        tagConfig.getBranchCreateOrderConfig().sort(Comparator.comparing(BranchLevelConfigDTO::getSort));

        for (Long branchId : branchIds) {
            // 利润比例
            BigDecimal saleDayCnt = BigDecimal.ZERO;
            if (saleMap.containsKey(branchId)) {
                saleDayCnt = saleMap.get(branchId).getSaleDayCnt();
            }

            // 已经获取到了销售额, 开始定级
            for (BranchLevelConfigDTO configDTO : tagConfig.getBranchCreateOrderConfig()) {
                // 验证规则
                if (this.condition(configDTO, saleDayCnt)) {
                    AdsBranchTagMonth adsBranchTagMonth = context.getTagMonthMap().get(branchId);
                    BranchTagEnum branchTag = configDTO.getBranchTag();
                    // 频次等级标签类型;例：频次等级
                    adsBranchTagMonth.setFreqLevelTagType(branchTag.getTag());
                    // 频次等级标签定义id;例：id
                    adsBranchTagMonth.setFreqLevelTagDefId(configDTO.getTagDefId());
                    // 频次等级规则;例：月订货天数 > 10天
                    if (configDTO.getCondition().isOne()) {
                        adsBranchTagMonth.setFreqLevelTagRule(
                                StringUtils.format("订货频次{}{}天",
                                        configDTO.getCondition().getCondition(),
                                        configDTO.getValA().setScale(0, RoundingMode.HALF_UP)
                                )
                        );
                    } else {
                        adsBranchTagMonth.setFreqLevelTagRule(
                                StringUtils.format("订货频次{}",
                                        configDTO.getCondition().getCondition()
                                                .replace("A", configDTO.getValA().setScale(0, RoundingMode.HALF_UP).toString())
                                                .replace("X", saleDayCnt.toString())
                                                .replace("B", configDTO.getValB().setScale(0, RoundingMode.HALF_UP).toString()))
                        );
                    }
                    // 频次等级标签名;例：高频次
                    adsBranchTagMonth.setFreqLevelTagName(branchTag.getName());
                    // 频次等级标签指标值;例：11
                    adsBranchTagMonth.setFreqLevelTagVal(StringUtils.format("{}天", saleDayCnt.setScale(0, RoundingMode.HALF_UP)));
                    // 跳到下一个门店
                    break;
                }
            }
        }
    }
}
