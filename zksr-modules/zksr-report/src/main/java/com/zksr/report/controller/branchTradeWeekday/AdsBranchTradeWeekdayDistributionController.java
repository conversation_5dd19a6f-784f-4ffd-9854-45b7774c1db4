package com.zksr.report.controller.branchTradeWeekday;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.report.domain.AdsBranchTradeWeekdayDistribution;
import com.zksr.report.service.IAdsBranchTradeWeekdayDistributionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.report.controller.branchTradeWeekday.vo.AdsBranchTradeWeekdayDistributionPageReqVO;
import com.zksr.report.controller.branchTradeWeekday.vo.AdsBranchTradeWeekdayDistributionSaveReqVO;
import com.zksr.report.api.branch.vo.AdsBranchTradeWeekdayDistributionRespVO;
import com.zksr.report.convert.branchTradeWeekday.AdsBranchTradeWeekdayDistributionConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 门店周下单分布月Controller
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Api(tags = "管理后台 - 门店周下单分布月接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/branchTradeWeekday")
public class AdsBranchTradeWeekdayDistributionController {
    @Autowired
    private IAdsBranchTradeWeekdayDistributionService adsBranchTradeWeekdayDistributionService;

    /**
     * 新增门店周下单分布月
     */
    @ApiOperation(value = "新增门店周下单分布月", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "门店周下单分布月", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AdsBranchTradeWeekdayDistributionSaveReqVO createReqVO) {
        return success(adsBranchTradeWeekdayDistributionService.insertAdsBranchTradeWeekdayDistribution(createReqVO));
    }

    /**
     * 修改门店周下单分布月
     */
    @ApiOperation(value = "修改门店周下单分布月", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "门店周下单分布月", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody AdsBranchTradeWeekdayDistributionSaveReqVO updateReqVO) {
            adsBranchTradeWeekdayDistributionService.updateAdsBranchTradeWeekdayDistribution(updateReqVO);
        return success(true);
    }

    /**
     * 删除门店周下单分布月
     */
    @ApiOperation(value = "删除门店周下单分布月", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "门店周下单分布月", businessType = BusinessType.DELETE)
    @DeleteMapping("/{monthIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] monthIds) {
        adsBranchTradeWeekdayDistributionService.deleteAdsBranchTradeWeekdayDistributionByMonthIds(monthIds);
        return success(true);
    }

    /**
     * 获取门店周下单分布月详细信息
     */
    @ApiOperation(value = "获得门店周下单分布月详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{monthId}")
    public CommonResult<AdsBranchTradeWeekdayDistributionRespVO> getInfo(@PathVariable("monthId") Long monthId) {
        AdsBranchTradeWeekdayDistribution adsBranchTradeWeekdayDistribution = adsBranchTradeWeekdayDistributionService.getAdsBranchTradeWeekdayDistribution(monthId);
        return success(AdsBranchTradeWeekdayDistributionConvert.INSTANCE.convert(adsBranchTradeWeekdayDistribution));
    }

    /**
     * 分页查询门店周下单分布月
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得门店周下单分布月分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AdsBranchTradeWeekdayDistributionRespVO>> getPage(@Valid AdsBranchTradeWeekdayDistributionPageReqVO pageReqVO) {
        PageResult<AdsBranchTradeWeekdayDistribution> pageResult = adsBranchTradeWeekdayDistributionService.getAdsBranchTradeWeekdayDistributionPage(pageReqVO);
        return success(AdsBranchTradeWeekdayDistributionConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "report:branchTradeWeekday:add";
        /** 编辑 */
        public static final String EDIT = "report:branchTradeWeekday:edit";
        /** 删除 */
        public static final String DELETE = "report:branchTradeWeekday:remove";
        /** 列表 */
        public static final String LIST = "report:branchTradeWeekday:list";
        /** 查询 */
        public static final String GET = "report:branchTradeWeekday:query";
        /** 停用 */
        public static final String DISABLE = "report:branchTradeWeekday:disable";
        /** 启用 */
        public static final String ENABLE = "report:branchTradeWeekday:enable";
    }
}
