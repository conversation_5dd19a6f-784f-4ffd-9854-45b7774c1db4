package com.zksr.report.controller.skuSalesSummary.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("商品月销售汇总 -  Request VO")
public class SkuSalesSummaryQueryDTO extends PageParam {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "选择查询月份，格式为 yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM")
    private Date monthId; // 查询月份，格式为 yyyy-MM

    @ApiModelProperty(value = "商品编号")
    private String skuNo; //商品编号

    @ApiModelProperty(value = "商品名称")
    private String skuName; //商品名称

    //平台商编号
    private Long sysCode;
    //本月月份
    private String currentMonthId;
}