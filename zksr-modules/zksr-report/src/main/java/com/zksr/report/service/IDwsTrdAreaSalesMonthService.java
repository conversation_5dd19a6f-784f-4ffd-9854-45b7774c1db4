package com.zksr.report.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.report.controller.dwsAreaSales.dto.DwsTrdAreaSalesMonthRespDTO;
import com.zksr.report.domain.DwsTrdAreaSalesMonth;
import com.zksr.report.controller.dwsAreaSales.vo.DwsTrdAreaSalesMonthPageReqVO;
import com.zksr.report.controller.dwsAreaSales.vo.DwsTrdAreaSalesMonthSaveReqVO;

/**
 * 交易域区域城市粒度下单月汇总Service接口
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
public interface IDwsTrdAreaSalesMonthService {

    /**
     * 获得交易域区域城市粒度下单月汇总分页
     *
     * @param pageReqVO 分页查询
     * @return 交易域区域城市粒度下单月汇总分页
     */
    PageResult<DwsTrdAreaSalesMonthRespDTO> getDwsTrdAreaSalesMonthPage(DwsTrdAreaSalesMonthPageReqVO pageReqVO);

}
