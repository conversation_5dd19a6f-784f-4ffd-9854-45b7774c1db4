package com.zksr.report.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 门店一级类别销售金额统计月对象 ads_branch_cat1_orderamt_stats_month
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@TableName(value = "ads_branch_cat1_orderamt_stats_month")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdsBranchCat1OrderamtStatsMonth extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 月份ID */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long monthId;

    /** 平台商id */
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 区域城市id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 门店id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 一级类别id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long cat1Id;

    /** 一级类别id */
    @Excel(name = "一级类别id")
    private BigDecimal cat1OrderAmt;

    /** 门店下单金额 */
    @Excel(name = "门店下单金额")
    private BigDecimal branchOrderAmt;

    /** 占比 */
    @Excel(name = "占比")
    private BigDecimal proportion;

    /** 插入时间id */
    @Excel(name = "插入时间id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long insertDateId;

}
