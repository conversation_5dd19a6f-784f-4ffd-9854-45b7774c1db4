package com.zksr.report.service.impl;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.controller.branchTagMonth.vo.AdsBranchTagMonthPageReqVO;
import com.zksr.report.controller.branchTagMonth.vo.AdsBranchTagMonthSaveReqVO;
import com.zksr.report.convert.BranchTagMonth.AdsBranchTagMonthConvert;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.mapper.AdsBranchTagMonthMapper;
import com.zksr.report.service.IAdsBranchTagMonthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 门店标签月Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Service
public class AdsBranchTagMonthServiceImpl implements IAdsBranchTagMonthService {
    @Autowired
    private AdsBranchTagMonthMapper adsBranchTagMonthMapper;

    /**
     * 新增门店标签月
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertAdsBranchTagMonth(AdsBranchTagMonthSaveReqVO createReqVO) {
        // 插入
        AdsBranchTagMonth adsBranchTagMonth = AdsBranchTagMonthConvert.INSTANCE.convert(createReqVO);
        adsBranchTagMonthMapper.insert(adsBranchTagMonth);
        // 返回
        return adsBranchTagMonth.getMonthId();
    }

    @Override
    @Transactional
    public void insertAdsBranchTagMonth(List<AdsBranchTagMonth> tagMonths) {
        if (tagMonths.isEmpty()) {
            return;
        }
        // 先移除
        for (AdsBranchTagMonth tagMonth : tagMonths) {
            adsBranchTagMonthMapper.deleteMonthIdAndBranchId(tagMonth);
        }
        adsBranchTagMonthMapper.insertBatch(tagMonths);
    }

    /**
     * 修改门店标签月
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateAdsBranchTagMonth(AdsBranchTagMonthSaveReqVO updateReqVO) {
        adsBranchTagMonthMapper.updateById(AdsBranchTagMonthConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除门店标签月
     *
     * @param monthId 月份ID
     */
    @Override
    public void deleteAdsBranchTagMonth(Long monthId) {
        // 删除
        adsBranchTagMonthMapper.deleteById(monthId);
    }

    /**
     * 批量删除门店标签月
     *
     * @param monthIds 需要删除的门店标签月主键
     * @return 结果
     */
    @Override
    public void deleteAdsBranchTagMonthByMonthIds(Long[] monthIds) {
        for(Long monthId : monthIds){
            this.deleteAdsBranchTagMonth(monthId);
        }
    }

    /**
     * 获得门店标签月
     *
     * @param monthId 月份ID
     * @return 门店标签月
     */
    @Override
    public AdsBranchTagMonth getAdsBranchTagMonth(Long monthId) {
        return adsBranchTagMonthMapper.selectById(monthId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AdsBranchTagMonth> getAdsBranchTagMonthPage(AdsBranchTagMonthPageReqVO pageReqVO) {
        return adsBranchTagMonthMapper.selectPage(pageReqVO);
    }

}
