package com.zksr.report.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.report.controller.tagDefinit.vo.RptTagDefRespVO;
import com.zksr.report.domain.RptTagDef;
import com.zksr.report.controller.tagDefinit.vo.RptTagDefPageReqVO;
import com.zksr.report.controller.tagDefinit.vo.RptTagDefSaveReqVO;

/**
 * 标签定义Service接口
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public interface IRptTagDefService {

    /**
     * 新增标签定义
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertRptTagDef(@Valid RptTagDefSaveReqVO createReqVO);

    /**
     * 修改标签定义
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateRptTagDef(@Valid RptTagDefSaveReqVO updateReqVO);

    /**
     * 删除标签定义
     *
     * @param tagDefId 标签定义id
     */
    public void deleteRptTagDef(Long tagDefId);

    /**
     * 批量删除标签定义
     *
     * @param tagDefIds 需要删除的标签定义主键集合
     * @return 结果
     */
    public void deleteRptTagDefByTagDefIds(Long[] tagDefIds);

    /**
     * 获得标签定义
     *
     * @param tagDefId 标签定义id
     * @return 标签定义
     */
    public RptTagDef getRptTagDef(Long tagDefId);

    /**
     * 获得标签定义分页
     *
     * @param pageReqVO 分页查询
     * @return 标签定义分页
     */
    PageResult<RptTagDef> getRptTagDefPage(RptTagDefPageReqVO pageReqVO);

    /**
     * 获取大区客户标签配置
     * @return
     */
    RptTagDefRespVO getRptTagDefSysCode(Long sysCode);

}
