package com.zksr.report.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.domain.DwsTrdBranchSalesDay;
import com.zksr.report.controller.branchSaleDay.vo.DwsTrdBranchSalesDayPageReqVO;


/**
 * 交易域门店粒度下单日汇总Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Mapper
public interface DwsTrdBranchSalesDayMapper extends BaseMapperX<DwsTrdBranchSalesDay> {
    default PageResult<DwsTrdBranchSalesDay> selectPage(DwsTrdBranchSalesDayPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DwsTrdBranchSalesDay>()
                    .eqIfPresent(DwsTrdBranchSalesDay::getDateId, reqVO.getDateId())
                    .eqIfPresent(DwsTrdBranchSalesDay::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(DwsTrdBranchSalesDay::getAreaId, reqVO.getAreaId())
                    .eqIfPresent(DwsTrdBranchSalesDay::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(DwsTrdBranchSalesDay::getOrderAmt, reqVO.getOrderAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getOrderQty, reqVO.getOrderQty())
                    .eqIfPresent(DwsTrdBranchSalesDay::getDxSkuQty, reqVO.getDxSkuQty())
                    .eqIfPresent(DwsTrdBranchSalesDay::getDxSpuQty, reqVO.getDxSpuQty())
                    .eqIfPresent(DwsTrdBranchSalesDay::getDxBranchQty, reqVO.getDxBranchQty())
                    .eqIfPresent(DwsTrdBranchSalesDay::getSupplierCostAmt, reqVO.getSupplierCostAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getProfitAmt, reqVO.getProfitAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getProfitPartnerAmt, reqVO.getProfitPartnerAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getProfitDcAmt, reqVO.getProfitDcAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getProfitColonelAmt, reqVO.getProfitColonelAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getProfitColonel1Amt, reqVO.getProfitColonel1Amt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getProfitColonel2Amt, reqVO.getProfitColonel2Amt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getPreOrderAmt, reqVO.getPreOrderAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getActivityDiscountAmt, reqVO.getActivityDiscountAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getCouponDiscountAmt, reqVO.getCouponDiscountAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getCouponDiscountAmt2, reqVO.getCouponDiscountAmt2())
                    .eqIfPresent(DwsTrdBranchSalesDay::getDiscountAmt, reqVO.getDiscountAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getProfitRate, reqVO.getProfitRate())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnAmt, reqVO.getReturnAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnQty, reqVO.getReturnQty())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnSkuQty, reqVO.getReturnSkuQty())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnSpuQty, reqVO.getReturnSpuQty())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnBranchQty, reqVO.getReturnBranchQty())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnSupplierCostAmt, reqVO.getReturnSupplierCostAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnProfitAmt, reqVO.getReturnProfitAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnProfitPartnerAmt, reqVO.getReturnProfitPartnerAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnProfitDcAmt, reqVO.getReturnProfitDcAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnProfitColonelAmt, reqVO.getReturnProfitColonelAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnProfitColonel1Amt, reqVO.getReturnProfitColonel1Amt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnProfitColonel2Amt, reqVO.getReturnProfitColonel2Amt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getPreReturnAmt, reqVO.getPreReturnAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnActivityDiscountAmt, reqVO.getReturnActivityDiscountAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnCouponDiscountAmt, reqVO.getReturnCouponDiscountAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnCouponDiscountAmt2, reqVO.getReturnCouponDiscountAmt2())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnDiscountAmt, reqVO.getReturnDiscountAmt())
                    .eqIfPresent(DwsTrdBranchSalesDay::getReturnProfitRate, reqVO.getReturnProfitRate())
                .orderByDesc(DwsTrdBranchSalesDay::getDateId));
    }
}
