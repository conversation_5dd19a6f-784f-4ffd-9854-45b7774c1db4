package com.zksr.report.service.impl;

import com.github.pagehelper.Page;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.controller.dwsAreaSales.dto.DwsTrdAreaSalesMonthRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.report.mapper.DwsTrdAreaSalesMonthMapper;
import com.zksr.report.convert.dwsAreaSales.DwsTrdAreaSalesMonthConvert;
import com.zksr.report.domain.DwsTrdAreaSalesMonth;
import com.zksr.report.controller.dwsAreaSales.vo.DwsTrdAreaSalesMonthPageReqVO;
import com.zksr.report.controller.dwsAreaSales.vo.DwsTrdAreaSalesMonthSaveReqVO;
import com.zksr.report.service.IDwsTrdAreaSalesMonthService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.report.enums.ErrorCodeConstants.*;

/**
 * 交易域区域城市粒度下单月汇总Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Service
public class DwsTrdAreaSalesMonthServiceImpl implements IDwsTrdAreaSalesMonthService {
    @Autowired
    private DwsTrdAreaSalesMonthMapper dwsTrdAreaSalesMonthMapper;



    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<DwsTrdAreaSalesMonthRespDTO> getDwsTrdAreaSalesMonthPage(DwsTrdAreaSalesMonthPageReqVO pageReqVO) {
        Page<DwsTrdAreaSalesMonthRespDTO> page = PageUtils.startPage(pageReqVO);
        return new PageResult<>(dwsTrdAreaSalesMonthMapper.getAreaMonthSalesPage(pageReqVO), page.getTotal());
    }


    // TODO 待办：请将下面的错误码复制到 com.zksr.report.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 交易域区域城市粒度下单月汇总 TODO 补充编号 ==========
    // ErrorCode DWS_TRD_AREA_SALES_MONTH_NOT_EXISTS = new ErrorCode(TODO 补充编号, "交易域区域城市粒度下单月汇总不存在");


}
