package com.zksr.report.controller.dwsAreaSales;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.report.domain.DwsTrdAreaSalesMonth;
import com.zksr.report.service.IDwsTrdAreaSalesMonthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.report.controller.dwsAreaSales.vo.DwsTrdAreaSalesMonthPageReqVO;
import com.zksr.report.controller.dwsAreaSales.vo.DwsTrdAreaSalesMonthSaveReqVO;
import com.zksr.report.controller.dwsAreaSales.dto.DwsTrdAreaSalesMonthRespDTO;
import com.zksr.report.convert.dwsAreaSales.DwsTrdAreaSalesMonthConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 交易域区域城市粒度下单月汇总Controller
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Api(tags = "管理后台 - 交易域区域城市粒度下单月汇总接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/dwsAreaSalesMonth")
public class DwsTrdAreaSalesMonthController {
    @Autowired
    private IDwsTrdAreaSalesMonthService dwsTrdAreaSalesMonthService;

    /**
     * 分页查询交易域区域城市粒度下单月汇总
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得交易域区域城市粒度下单月汇总分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<DwsTrdAreaSalesMonthRespDTO>> getPage(@Valid DwsTrdAreaSalesMonthPageReqVO pageReqVO) {
        return success(dwsTrdAreaSalesMonthService.getDwsTrdAreaSalesMonthPage(pageReqVO));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "report:month:add";
        /** 编辑 */
        public static final String EDIT = "report:month:edit";
        /** 删除 */
        public static final String DELETE = "report:month:remove";
        /** 列表 */
        public static final String LIST = "report:month:list";
        /** 查询 */
        public static final String GET = "report:month:query";
        /** 停用 */
        public static final String DISABLE = "report:month:disable";
        /** 启用 */
        public static final String ENABLE = "report:month:enable";
    }
}
