package com.zksr.report.component.branchTag;

import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.report.api.branch.dto.BranchLevelConfigDTO;
import com.zksr.report.api.branch.dto.BranchTagConfigDTO;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.domain.vo.BranchSaleVO;
import com.zksr.report.domain.vo.TagNodeComponent;
import com.zksr.report.slot.BranchTagContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店等级计算
 * @date 2024/11/14 9:05
 */
@Slf4j
@Component("branchTagProfitCmp")
public class BranchTagProfitCmp extends TagNodeComponent {

    @Override
    public void process() throws Exception {
        // 标签配置
        BranchTagConfigDTO tagConfig = this.getContext().getTagConfig();
        if (Objects.isNull(tagConfig.getBranchProfitLevelConfig())) {
            return;
        }

        // 获取门店ID集合
        List<Long> branchIds = this.getContext().getBranchList().stream().map(BranchDTO::getBranchId).collect(Collectors.toList());

        // 查询销售数据
        List<BranchSaleVO> saleVOList = rptTagDefMapper.selectByMonthSale(this.getContext().getMonthId(), branchIds);
        Map<Long, BranchSaleVO> saleMap = saleVOList.stream().collect(Collectors.toMap(BranchSaleVO::getBranchId, item -> item));

        // 排序, 从顶级依次向下处理
        tagConfig.getBranchProfitLevelConfig().sort(Comparator.comparing(BranchLevelConfigDTO::getSort));

        for (Long branchId : branchIds) {
            // 利润比例
            BigDecimal profitOdd = BigDecimal.ZERO;
            if (saleMap.containsKey(branchId)) {
                BranchSaleVO branchSaleVO = saleMap.get(branchId);
                // 利润大于0, 销售额大于0
                if (NumberUtil.isGreater(branchSaleVO.getProfit(), BigDecimal.ZERO) && NumberUtil.isGreater(branchSaleVO.getSaleAmt(), BigDecimal.ZERO)) {
                    profitOdd = branchSaleVO.getProfit().divide(branchSaleVO.getSaleAmt(), 2, RoundingMode.HALF_UP);
                }
            }

            // 已经获取到了销售额, 开始定级
            for (BranchLevelConfigDTO configDTO : tagConfig.getBranchProfitLevelConfig()) {
                // 验证规则
                if (this.condition(configDTO, profitOdd)) {
                    AdsBranchTagMonth adsBranchTagMonth = this.getContext().getTagMonthMap().get(branchId);
                    BranchTagEnum branchTag = configDTO.getBranchTag();
                    // 毛利等级标签类型;例：毛利等级
                    adsBranchTagMonth.setProfitLevelTagType(branchTag.getTag());
                    // 毛利等级标签定义id;例：id
                    adsBranchTagMonth.setProfitLevelTagDefId(configDTO.getTagDefId());
                    // 毛利等级规则;例：毛利率 > 10%
                    if (configDTO.getCondition().isOne()) {
                        adsBranchTagMonth.setProfitLevelTagRule(
                                StringUtils.format("毛利率{}{}%",
                                        configDTO.getCondition().getCondition(),
                                        configDTO.getValA().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP)
                                )
                        );
                    } else {
                        adsBranchTagMonth.setProfitLevelTagRule(
                                StringUtils.format("毛利率{}",
                                        configDTO.getCondition().getCondition()
                                                .replace("A", configDTO.getValA().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).toString() + "%")
                                                //.replace("X", profitOdd.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).toString())
                                                .replace("B", configDTO.getValB().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).toString()) + "%")
                        );
                    }
                    // 毛利等级标签名;例：高毛利
                    adsBranchTagMonth.setProfitLevelTagName(branchTag.getName());
                    // 毛利等级标签指标值;例：10.88%
                    adsBranchTagMonth.setProfitLevelTagVal(StringUtils.format("{}%", profitOdd.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP)));
                    // 跳到下一个门店
                    break;
                }
            }
        }
    }
}
