package com.zksr.report.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.domain.AdsBranchCat1OrderamtStatsMonth;
import com.zksr.report.controller.branchCategoryOrderAmt.vo.AdsBranchCat1OrderamtStatsMonthPageReqVO;


/**
 * 门店一级类别销售金额统计月Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Mapper
public interface AdsBranchCat1OrderamtStatsMonthMapper extends BaseMapperX<AdsBranchCat1OrderamtStatsMonth> {
    default PageResult<AdsBranchCat1OrderamtStatsMonth> selectPage(AdsBranchCat1OrderamtStatsMonthPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AdsBranchCat1OrderamtStatsMonth>()
                    .eqIfPresent(AdsBranchCat1OrderamtStatsMonth::getMonthId, reqVO.getMonthId())
                    .eqIfPresent(AdsBranchCat1OrderamtStatsMonth::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AdsBranchCat1OrderamtStatsMonth::getAreaId, reqVO.getAreaId())
                    .eqIfPresent(AdsBranchCat1OrderamtStatsMonth::getBranchId, reqVO.getBranchId())
                    .eqIfPresent(AdsBranchCat1OrderamtStatsMonth::getCat1Id, reqVO.getCat1Id())
                    .eqIfPresent(AdsBranchCat1OrderamtStatsMonth::getCat1OrderAmt, reqVO.getCat1OrderAmt())
                    .eqIfPresent(AdsBranchCat1OrderamtStatsMonth::getBranchOrderAmt, reqVO.getBranchOrderAmt())
                    .eqIfPresent(AdsBranchCat1OrderamtStatsMonth::getProportion, reqVO.getProportion())
                    .eqIfPresent(AdsBranchCat1OrderamtStatsMonth::getInsertDateId, reqVO.getInsertDateId())
                .orderByDesc(AdsBranchCat1OrderamtStatsMonth::getMonthId));
    }
}
