package com.zksr.report.controller.branchTagMonth;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.report.controller.branchTagMonth.vo.AdsBranchTagMonthPageReqVO;
import com.zksr.report.api.branch.vo.AdsBranchTagMonthRespVO;
import com.zksr.report.controller.branchTagMonth.vo.AdsBranchTagMonthSaveReqVO;
import com.zksr.report.convert.BranchTagMonth.AdsBranchTagMonthConvert;
import com.zksr.report.domain.AdsBranchTagMonth;
import com.zksr.report.service.IAdsBranchTagMonthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 门店标签月Controller
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Api(tags = "管理后台 - 门店标签月接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/branchTagMonth")
public class AdsBranchTagMonthController {
    @Autowired
    private IAdsBranchTagMonthService adsBranchTagMonthService;

    /**
     * 新增门店标签月
     */
    @ApiOperation(value = "新增门店标签月", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "门店标签月", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AdsBranchTagMonthSaveReqVO createReqVO) {
        return success(adsBranchTagMonthService.insertAdsBranchTagMonth(createReqVO));
    }

    /**
     * 修改门店标签月
     */
    @ApiOperation(value = "修改门店标签月", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "门店标签月", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody AdsBranchTagMonthSaveReqVO updateReqVO) {
            adsBranchTagMonthService.updateAdsBranchTagMonth(updateReqVO);
        return success(true);
    }

    /**
     * 删除门店标签月
     */
    @ApiOperation(value = "删除门店标签月", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "门店标签月", businessType = BusinessType.DELETE)
    @DeleteMapping("/{monthIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] monthIds) {
        adsBranchTagMonthService.deleteAdsBranchTagMonthByMonthIds(monthIds);
        return success(true);
    }

    /**
     * 获取门店标签月详细信息
     */
    @ApiOperation(value = "获得门店标签月详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{monthId}")
    public CommonResult<AdsBranchTagMonthRespVO> getInfo(@PathVariable("monthId") Long monthId) {
        AdsBranchTagMonth adsBranchTagMonth = adsBranchTagMonthService.getAdsBranchTagMonth(monthId);
        return success(AdsBranchTagMonthConvert.INSTANCE.convert(adsBranchTagMonth));
    }

    /**
     * 分页查询门店标签月
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得门店标签月分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AdsBranchTagMonthRespVO>> getPage(@Valid AdsBranchTagMonthPageReqVO pageReqVO) {
        PageResult<AdsBranchTagMonth> pageResult = adsBranchTagMonthService.getAdsBranchTagMonthPage(pageReqVO);
        return success(AdsBranchTagMonthConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "report:branchTagMonth:add";
        /** 编辑 */
        public static final String EDIT = "report:branchTagMonth:edit";
        /** 删除 */
        public static final String DELETE = "report:branchTagMonth:remove";
        /** 列表 */
        public static final String LIST = "report:branchTagMonth:list";
        /** 查询 */
        public static final String GET = "report:branchTagMonth:query";
        /** 停用 */
        public static final String DISABLE = "report:branchTagMonth:disable";
        /** 启用 */
        public static final String ENABLE = "report:branchTagMonth:enable";
    }
}
