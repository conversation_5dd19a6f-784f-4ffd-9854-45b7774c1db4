package com.zksr.report.controller.dwsSupplierSales.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import io.swagger.annotations.ApiModel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;

/**
 * 交易域入驻商粒度下单日汇总对象 dws_trd_supplier_sales_day
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@ApiModel("交易域入驻商粒度下单日汇总 - dws_trd_supplier_sales_day分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DwsTrdSupplierSalesDayPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    @DateTimeFormat(pattern = YYYY_MM_DD)
    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    @DateTimeFormat(pattern = YYYY_MM_DD)
    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    @ApiModelProperty(value = "区域ID")
    private Long areaId;

    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

}
