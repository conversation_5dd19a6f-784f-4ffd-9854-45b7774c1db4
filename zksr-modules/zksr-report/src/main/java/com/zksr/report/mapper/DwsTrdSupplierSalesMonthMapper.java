package com.zksr.report.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.report.controller.dwsSupplierSales.dto.DwsTrdSupplierSalesMonthRespDTO;
import com.zksr.report.controller.dwsSupplierSales.vo.DwsTrdSupplierSalesMonthPageReqVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.report.domain.DwsTrdSupplierSalesMonth;

import java.util.List;


/**
 * 交易域入驻商粒度下单月汇总Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Mapper
public interface DwsTrdSupplierSalesMonthMapper extends BaseMapperX<DwsTrdSupplierSalesMonth> {
    /**
     * 交易域入驻商粒度下单月销售分页列表
     * @param pageReqVO
     * @return
     */
    List<DwsTrdSupplierSalesMonthRespDTO> getSupplierMonthSalesPage(DwsTrdSupplierSalesMonthPageReqVO pageReqVO);
}
