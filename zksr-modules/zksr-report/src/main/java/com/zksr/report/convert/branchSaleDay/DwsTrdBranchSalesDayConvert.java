package com.zksr.report.convert.branchSaleDay;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.domain.DwsTrdBranchSalesDay;
import com.zksr.report.controller.branchSaleDay.vo.DwsTrdBranchSalesDayRespVO;
import com.zksr.report.controller.branchSaleDay.vo.DwsTrdBranchSalesDaySaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 交易域门店粒度下单日汇总 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-25
*/
@Mapper
public interface DwsTrdBranchSalesDayConvert {

    DwsTrdBranchSalesDayConvert INSTANCE = Mappers.getMapper(DwsTrdBranchSalesDayConvert.class);

    DwsTrdBranchSalesDayRespVO convert(DwsTrdBranchSalesDay dwsTrdBranchSalesDay);

    DwsTrdBranchSalesDay convert(DwsTrdBranchSalesDaySaveReqVO dwsTrdBranchSalesDaySaveReq);

    PageResult<DwsTrdBranchSalesDayRespVO> convertPage(PageResult<DwsTrdBranchSalesDay> dwsTrdBranchSalesDayPage);
}