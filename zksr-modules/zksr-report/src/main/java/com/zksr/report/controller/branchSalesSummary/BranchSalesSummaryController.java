package com.zksr.report.controller.branchSalesSummary;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.report.controller.branchSalesSummary.dto.BranchSalesSummaryDTO;
import com.zksr.report.controller.branchSalesSummary.dto.BranchSalesSummaryQueryDTO;
import com.zksr.report.service.IBranchSalesSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(tags = "管理后台 - 门店月销售汇总接口", produces = "application/json")
@RestController
@Validated
@RequestMapping("/branchSalesSummary")
public class BranchSalesSummaryController {
    @Autowired
    private IBranchSalesSummaryService branchSalesSummaryService;


    /**
     * 获取门店月销售汇总
     *
     * @param queryDTO 查询条件
     * @return 汇总数据列表
     */
    @PostMapping("/monthlyBranch")
    @ApiOperation("获取门店月销售汇总")
    @DataScope(dcAlias = "t1")
    public CommonResult<PageResult<BranchSalesSummaryDTO>> getMonthlyBranchSalesSummary(@RequestBody BranchSalesSummaryQueryDTO queryDTO) {
        return CommonResult.success(branchSalesSummaryService.getMonthlyBranchSalesSummary(queryDTO));
    }
}
