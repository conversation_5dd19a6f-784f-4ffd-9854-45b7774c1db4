package com.zksr.report.service.impl;

import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.controller.skuSalesSummary.dto.SkuSalesSummaryDTO;
import com.zksr.report.controller.skuSalesSummary.dto.SkuSalesSummaryQueryDTO;
import com.zksr.report.mapper.SkuSalesSummaryMapper;
import com.zksr.report.service.ISkuSalesSummaryService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class SkuSalesSummaryServiceImpl implements ISkuSalesSummaryService {

    @Autowired
    private SkuSalesSummaryMapper skuSalesSummaryMapper;

    @Override
    public PageResult<SkuSalesSummaryDTO> getMonthlySkuSalesSummary(SkuSalesSummaryQueryDTO queryDTO) {
        PageResult<SkuSalesSummaryDTO> result = new PageResult<>();

        // 分页参数
        int pageNo = queryDTO.getPageNo();
        int pageSize = queryDTO.getPageSize();
        int offset = (pageNo - 1) * pageSize;

        queryDTO.setPageNo(offset);
        queryDTO.setPageSize(pageSize);

        // 日期处理
        Date queryMonth = queryDTO.getMonthId();
        if (queryMonth == null) {
            throw new ServiceException("选择日期查询时，查询月份不能为空");
        }
        String currentMonthId = DateFormatUtils.format(queryMonth, "yyyyMM");
        queryDTO.setCurrentMonthId(currentMonthId);

        // 查询总记录数
        Long total = skuSalesSummaryMapper.countMonthlySkuSalesSummary(queryDTO);
        if (total == 0L) {
            result.setList(Collections.emptyList());
            result.setTotal(0L);
            return result;
        }

        // 查询数据
        List<SkuSalesSummaryDTO> data = skuSalesSummaryMapper.getMonthlySkuSalesSummary(queryDTO);
        for (SkuSalesSummaryDTO sku : data) {
            sku.setProperties(PropertyAndValDTO.getProperties(sku.getProperties()));
        }
        result.setList(data);
        result.setTotal(total);
        return result;
    }
}