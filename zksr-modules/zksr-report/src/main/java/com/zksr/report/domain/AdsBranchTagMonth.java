package com.zksr.report.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

import java.util.Date;

/**
 * 门店标签月对象 ads_branch_tag_month
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@TableName(value = "ads_branch_tag_month")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdsBranchTagMonth extends BaseEntity{
    private static final long serialVersionUID=1L;

    @TableField(exist = false)
    private String createBy;
    @TableField(exist = false)
    private Date createTime;
    @TableField(exist = false)
    private String updateBy;
    @TableField(exist = false)
    private Date updateTime;

    /** 月份ID */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long monthId;

    /** 平台商id */
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 门店id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 区域ID */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 客户等级标签类型;例：客户等级 */
    @Excel(name = "客户等级标签类型;例：客户等级")
    private String levelTagType;

    /** 客户等级标签定义id;例：id */
    @Excel(name = "客户等级标签定义id;例：id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long levelTagDefId;

    /** 客户等级规则;例：自然月销售金额 &gt;= 2000 元 */
    @Excel(name = "客户等级规则;例：自然月销售金额 &gt;= 2000 元")
    private String levelTagRule;

    /** 客户等级标签名;例：A级 */
    @Excel(name = "客户等级标签名;例：A级")
    private String levelTagName;

    /** 客户等级标签指标值;例：2100元 */
    @Excel(name = "客户等级标签指标值;例：2100元")
    private String levelTagVal;

    /** 活跃客户标签类型;例：是否活跃客户 */
    @Excel(name = "活跃客户标签类型;例：是否活跃客户")
    private String activeTagType;

    /** 活跃客户标签定义id;例：id */
    @Excel(name = "活跃客户标签定义id;例：id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activeTagDefId;

    /** 活跃客户规则;例：自然月销售金额 &gt;= 2000元 */
    @Excel(name = "活跃客户规则;例：自然月销售金额 &gt;= 2000元")
    private String activeTagRule;

    /** 活跃客户标签名;例：是 */
    @Excel(name = "活跃客户标签名;例：是")
    private String activeTagName;

    /** 活跃客户标签指标值;例：2100元 */
    @Excel(name = "活跃客户标签指标值;例：2100元")
    private String activeTagVal;

    /** 类占等级标签类型;例：类占等级 */
    @Excel(name = "类占等级标签类型;例：类占等级")
    private String lzLevelTagType;

    /** 类占等级标签定义id;例：id */
    @Excel(name = "类占等级标签定义id;例：id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long lzLevelTagDefId;

    /** 类占等级规则;例：高类占比率 &gt; 50% */
    @Excel(name = "类占等级规则;例：高类占比率 &gt; 50%")
    private String lzLevelTagRule;

    /** 类占等级标签名;例：高类占 */
    @Excel(name = "类占等级标签名;例：高类占")
    private String lzLevelTagName;

    /** 类占等级标签指标值;例：60% */
    @Excel(name = "类占等级标签指标值;例：60%")
    private String lzLevelTagVal;

    /** 毛利等级标签类型;例：毛利等级 */
    @Excel(name = "毛利等级标签类型;例：毛利等级")
    private String profitLevelTagType;

    /** 毛利等级标签定义id;例：id */
    @Excel(name = "毛利等级标签定义id;例：id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long profitLevelTagDefId;

    /** 毛利等级规则;例：毛利率 &gt; 10% */
    @Excel(name = "毛利等级规则;例：毛利率 &gt; 10%")
    private String profitLevelTagRule;

    /** 毛利等级标签名;例：高毛利 */
    @Excel(name = "毛利等级标签名;例：高毛利")
    private String profitLevelTagName;

    /** 毛利等级标签指标值;例：10.88% */
    @Excel(name = "毛利等级标签指标值;例：10.88%")
    private String profitLevelTagVal;

    /** 频次等级标签类型;例：频次等级 */
    @Excel(name = "频次等级标签类型;例：频次等级")
    private String freqLevelTagType;

    /** 频次等级标签定义id;例：id */
    @Excel(name = "频次等级标签定义id;例：id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long freqLevelTagDefId;

    /** 频次等级规则;例：月订货天数 &gt; 10天 */
    @Excel(name = "频次等级规则;例：月订货天数 &gt; 10天")
    private String freqLevelTagRule;

    /** 频次等级标签名;例：高频次 */
    @Excel(name = "频次等级标签名;例：高频次")
    private String freqLevelTagName;

    /** 频次等级标签指标值;例：11 */
    @Excel(name = "频次等级标签指标值;例：11")
    private String freqLevelTagVal;

}
