package com.zksr.system.controller.dcArea.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 运营商-区域城市关联关系对象 sys_dc_area
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Data
@ApiModel("运营商-区域城市关联关系 - sys_dc_area Response VO")
@NoArgsConstructor
@AllArgsConstructor
public class SysDcAreaRespVO {
    private static final long serialVersionUID = 1L;

    /** 运营商id;运营商id */
    @Excel(name = "运营商id;运营商id")
    @ApiModelProperty(value = "运营商id;运营商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /** 区域城市id;区域城市id */
    @Excel(name = "区域城市id;区域城市id")
    @ApiModelProperty(value = "区域城市id;区域城市id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 平台商id;平台商id */
    @Excel(name = "平台商id;平台商id")
    @ApiModelProperty(value = "平台商id;平台商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;


    public SysDcAreaRespVO(Long dcId, Long areaId) {
        this.dcId = dcId;
        this.areaId = areaId;
    }

}
