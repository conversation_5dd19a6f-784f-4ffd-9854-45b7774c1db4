package com.zksr.system.controller.message.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 消息发送记录对象 sys_message_log
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@ApiModel("消息发送记录 - sys_message_log分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysMessageLogPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "消息内容")
    private Long msgId;

    /** 平台商ID */
    @Excel(name = "平台商ID")
    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    /** 消息模版ID */
    @Excel(name = "消息模版ID")
    @ApiModelProperty(value = "消息模版ID")
    private Long messageTemplateId;

    /** 接受商户ID */
    @Excel(name = "接受商户ID")
    @ApiModelProperty(value = "接受商户ID")
    private Long merchantId;

    /** 接受商户类型 */
    @Excel(name = "接受商户类型")
    @ApiModelProperty(value = "接受商户类型")
    private String merchantType;

    /** 0-未发送, 1-成功, 2-失败 */
    @Excel(name = "0-未发送, 1-成功, 2-失败")
    @ApiModelProperty(value = "0-未发送, 1-成功, 2-失败")
    private Integer state;

    /** 消息内容 */
    @Excel(name = "消息内容")
    @ApiModelProperty(value = "消息内容")
    private String content;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "消息发送开始时间")
    private Date startTime;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "消息发送结束时间")
    private Date endTime;
}
