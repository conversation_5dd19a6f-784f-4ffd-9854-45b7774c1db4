package com.zksr.system.visualSyn.model;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.zksr.common.core.constant.OpenApiConstants;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.enums.request.VisualSettingConstants;
import com.zksr.common.core.erpUtils.HttpUtils;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.api.visual.dto.VisualSettingTemplateDto;
import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;
import com.zksr.system.service.ISysInterfaceLogService;
import com.zksr.system.visualSyn.dto.SyncDataResult;
import com.zksr.system.api.EmailMessage.dto.SyncEmailData;
import com.zksr.system.visualSyn.service.IEmailMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;

import java.io.StringWriter;
import java.util.Map;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;


/**
*  对外同步业务处理抽象类
* @date 2024/10/24 11:06
* <AUTHOR>
*/
@Slf4j
public class SyncRequestModel<SYNCDTO> {

    public SyncDataDTO data;

    public VisualSettingMasterDto visualMasterDto;

    public VisualSettingDetailDto visualDetailDto;

    public VisualSettingTemplateDto visualTemplateDto;

    public OpensourceDto opensourceDto;

    public SYNCDTO syncdto;

    /**
     * 抽象类实体赋值
     * @param data
     * @param visualMasterDto
     * @param visualDetailDto
     * @param opensourceDto
     * @param syncdto
     */
    public SyncRequestModel(SyncDataDTO data, VisualSettingMasterDto visualMasterDto, VisualSettingDetailDto visualDetailDto,VisualSettingTemplateDto visualTemplateDto,OpensourceDto opensourceDto, SYNCDTO syncdto){
        this.data = data;
        this.visualMasterDto = visualMasterDto;
        this.visualDetailDto = visualDetailDto;
        this.visualTemplateDto = visualTemplateDto;
        this.opensourceDto = opensourceDto;
        this.syncdto = syncdto;
    }

    /**
     * 组装请求体数据 -- 根据B2B数据 转换成第三方模板数据
     * @return
     */
    protected String assembleBody(){
        try{

            //获取对接系统对应的模板格式
            String velocityTemplate = visualTemplateDto.getApiTemplate();

            if (ObjectUtil.isEmpty(syncdto)) {
                throw new RuntimeException("参数数据对象不能为空");
            }
            if (ObjectUtil.isEmpty(velocityTemplate)) {
                throw new RuntimeException("模版不能为空");
            }
            // 初始化VelocityEngine
            // 配置VelocityEngine（可选），这里直接使用默认配置
            VelocityEngine velocityEngine = new VelocityEngine();

            // 准备数据模型
            VelocityContext context = new VelocityContext();
            // 将数据对象先转换为json，再将json转换为Map对象
            String jsonStr = JSONUtil.toJsonStr(syncdto);
            log.info("开始替换参数: 参数：{}，转换模版：{}", jsonStr, velocityTemplate);
            Map paramMap = JSONUtil.toBean(jsonStr, Map.class);
            for (Object o : paramMap.keySet()) {
                context.put(o.toString(), paramMap.get(o));
            }
            // 合成输出
            StringWriter writer = new StringWriter();
            // 直接使用字符串模板内容，而非通过getTemplate方法
            boolean result = velocityEngine.evaluate(context, writer, "TemplateName", velocityTemplate);
            if (!result) {
                throw new RuntimeException("参数转换失败：使用velocity模板替换参数时出错");
            }
            log.info("组装请求体数据：{}", writer.toString());
            // 输出结果
            return writer.toString();

        }catch (Exception e){
            log.error(" SyncRequestModel.assembleBody失败,", e);
            throw exception(SYNC_ASSEMBLE_BODY_DATA_ERR,e.getMessage());
        }
    }

    /**
     * 组装请求头数据
     * @return
     */
    public String assembleHeader(){

        try{
            return null;
        }catch (Exception e){
            log.info("对外推送数据--组装请求头数据失败，失败原因：{}",e.getMessage());
            throw exception(SYNC_ASSEMBLE_HEADER_DATA_ERR,e.getMessage());
        }
    }

    /**
     * 组装鉴权信息
     * @param body
     * @return
     */
    public String assembleVerify(String body){
        try{
            return body;
        }catch (Exception e){
            log.info("对外推送数据--组装鉴权校验数据失败，失败原因：{}",e.getMessage());
            throw exception(SYNC_ASSEMBLE_VERIFY_DATA_ERR,e.getMessage());
        }
    }

    /**
     * 组装URL查询参数
     * @return
     */
    public Map<String, String> assembleQueryParms(){
        return null;
    }


    /**
     * 组装响应数据
     * @param emailData
     * @param responseStr
     * @return
     */
    public SyncDataResult assembleResponse(SyncEmailData emailData,String responseStr){
        try{
            SyncDataResult result = new SyncDataResult();
            if (ObjectUtil.isNotEmpty(responseStr)) {
                Map resultMap = JSONUtil.toBean(responseStr, Map.class);
                if (visualDetailDto.getRespCode().equals(String.valueOf(resultMap.get(visualDetailDto.getRespName())))) {
                    result.setCode(OpenApiConstants.SUCCESS_0);
                } else {
                    result.setCode(OpenApiConstants.ERROR);

                    //如果同步数据失败、或者请求接口异常  发送邮件通知
                    emailData.setErrMessage(responseStr);
                    sendErrEmail(emailData);

                }
                result.setMessage(JSONObject.toJSONString(resultMap));
                if(ToolUtil.isNotEmpty(visualDetailDto.getRespData())){
                    result.setData(resultMap.get(visualDetailDto.getRespData()).toString());
                }
            }

            log.info("组装响应数据：{}", result.toString());
            return result;
        }catch (Exception e){
            log.error(" SyncRequestModel.assembleResponse异常，", e);
            throw exception(SYNC_ASSEMBLE_RESPONSE_DATA_ERR,responseStr,e.getMessage());
        }

    }


    /**
     * 业务处理器
     * @return
     */
    public SyncDataResult processor(){
            //组装请求体数据
            String bodyStr = assembleBody();

            //更新请求体数据到日志
            SpringUtils.getBean(ISysInterfaceLogService.class).updateLogByResult(new SysInterfaceLogSaveReqVO(data.getReqId(),OpenApiConstants.REQ_STATUS_1,bodyStr));

            //组装URL查询参数
            Map<String, String> queryParams = assembleQueryParms();

            //组装请求头数据
            String headerStr = assembleHeader();

            //组装鉴权信息
            String verifyStr = assembleVerify(bodyStr);

            //邮件通知请求数据
            SyncEmailData emailData = new SyncEmailData(headerStr, bodyStr, verifyStr, null);

            //执行发送请求
            String responseStr = execute(emailData,headerStr, verifyStr, queryParams);

            //组装响应数据
            return assembleResponse(emailData,responseStr);
    }

    /**
     * 发送请求要捕获日志 ， 这里可能还是要捕获异常 确保发送前的日志能正常保存
     * @param headerStr
     * @param verifyStr
     * @return
     */
    private String execute(SyncEmailData emailData,String headerStr, String verifyStr, Map<String, String> queryParams){
        try{
            Map header = JSONUtil.toBean(headerStr, Map.class);
            String sendResult = null;

            // 构建请求URL
            String url = buildRequestUrl(queryParams);

            if (VisualSettingConstants.GET.equals(visualDetailDto.getReqType())) {
                HttpRequest httpGet = HttpUtil.createGet(url);
                httpGet.header(header);
                sendResult = httpGet.execute().body();
            } else if (VisualSettingConstants.POST.equals(visualDetailDto.getReqType())) {
                sendResult = HttpUtils.simplePost(url, header, verifyStr, visualDetailDto.getContentType());
            } else {
                throw new RuntimeException("暂未实现" + visualDetailDto.getReqType() + "请求类型调用方式");
            }

            log.info("执行发送请求响应结果：{}", sendResult);

            return sendResult;
        }catch(Exception e){
            log.error(" SyncRequestModel.execute异常，", e);
            //发送邮件
            emailData.setErrMessage(e.getMessage());
            sendErrEmail(emailData);
            throw exception(SYNC_ASSEMBLE_EXECUTE_DATA_ERR,e.getMessage());
        }
    }


    /**
     * 异常发送邮件通知
     * @param emailData
     */
    private void sendErrEmail(SyncEmailData emailData){
        SpringUtils.getBean(IEmailMessageService.class).sendSyncEmail(data,opensourceDto,emailData);
    }

    /**
     * 构建请求URL
     */
    private String buildRequestUrl(Map<String, String> queryParams) {
        String  url = opensourceDto.getSendUrl() + (ToolUtil.isEmpty(visualDetailDto.getApiUrl()) ? "" : visualDetailDto.getApiUrl());
        StringBuilder fullUrl = new StringBuilder(url);

        if (queryParams != null && !queryParams.isEmpty()) {
            fullUrl.append("?");
            queryParams.entrySet().stream()
                    .filter(entry -> StringUtils.isNotBlank(entry.getKey()) && StringUtils.isNotBlank(entry.getValue()))
                    .forEach(entry -> {
                        if (fullUrl.charAt(fullUrl.length() - 1) != '?') {
                            fullUrl.append("&");
                        }
                        fullUrl.append(entry.getKey()).append("=").append(entry.getValue());
                    });
        }

        return fullUrl.toString();
    }
}





