package com.zksr.system.api.dictData;

import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class DictDataApiImpl implements DictDataApi {


    @Autowired
    private ISysDictDataService sysDictDataService;


    /**
     * 根据条件分页查询字典数据
     *
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictData> selectDictDataList(SysDictData dictData) {
        return sysDictDataService.selectDictDataList(dictData);
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     *
     * @param dictType  字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @Override
    public String selectDictLabel(String dictType, String dictValue) {
        return sysDictDataService.selectDictLabel(dictType, dictValue);
    }

    /**
     * 根据字典数据ID查询信息
     *
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictData selectDictDataById(Long dictCode) {
        return sysDictDataService.selectDictDataById(dictCode);
    }

    /**
     * 批量删除字典数据信息
     *
     * @param dictCodes 需要删除的字典数据ID
     */
    @Override
    public void deleteDictDataByIds(Long[] dictCodes) {
        sysDictDataService.deleteDictDataByIds(dictCodes);
    }

    /**
     * 新增保存字典数据信息
     *
     * @param dictData 字典数据信息
     * @return 结果
     */
    @Override
    public int insertDictData(SysDictData dictData) {
        return sysDictDataService.insertDictData(dictData);
    }

    /**
     * 修改保存字典数据信息
     *
     * @param dictData 字典数据信息
     * @return 结果
     */
    @Override
    public int updateDictData(SysDictData dictData) {
        return sysDictDataService.updateDictData(dictData);
    }

    /**
     * @param dictType
     * @param smallUnit
     * @return
     */
    @Override
    public SysDictData getUnitByName(String dictType, String smallUnit) {
        return sysDictDataService.getUnitByName(dictType, smallUnit);
    }

    /**
     * 获取最大dict_value
     *
     * @param dictType
     * @return
     */
    @Override
    public String getMaxdictValue(String dictType) {
        return sysDictDataService.getMaxdictValue(dictType);
    }
}
