package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 运营商对象 sys_dc
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@TableName(value = "sys_dc")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysDc extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 运营商编号 */
    @TableId(type = IdType.AUTO)
    private Long dcId;

    /** 平台编号 */
    @Excel(name = "平台编号")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 帐号状态（0正常 1停用） */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除状态  (0正常  2已删除) */
    private String delFlag;

    /** 区域备注 */
    @Excel(name = "区域备注")
    private String memo;

    /** 运营商地址 */
    @Excel(name = "运营商地址")
    private String address;

    /** 运营商编号 */
    @Excel(name = "运营商编号")
    private String dcCode;

    /** 运营商名称 */
    @Excel(name = "运营商名称")
    private String dcName;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contractName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contractPhone;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    private String companyName;

    @Excel(name = "用户id")
    private Long userId;

    @Excel(name = "本地起送价")
    private BigDecimal minAmt;

    @Excel(name = "全国起送价")
    private BigDecimal globalMinAmt;
}
