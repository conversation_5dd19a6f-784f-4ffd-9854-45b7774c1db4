package com.zksr.system.controller.partnerConfig.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 开通微信b2b门店助手资料
 * @date 2024/8/13 11:20
 */
@Data
@ApiModel(description = "开通微信b2b门店助手资料")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SysSaveOpenB2bPayRespVO {
    @ApiModelProperty(
            "开通状态" +
            "0=成功开通," +
            "11=缺少设置费率的权限," +
            "12=不在可以设置费率的时间范围内," +
            "13=费率只能下调," +
            "14=费率不在范围内调," +
            "202=请重新授权," +
            "9404201=无效的主营商品类型," +
            "9404202=无效的主要线下销售渠道," +
            "9404203=无效的门店覆盖数," +
            "9404204=无效的所需服务类型," +
            "9404205=无效的小程序方案概述," +
            "9404206=无效的联系人姓名," +
            "9404207=无效的联系人手机号," +
            "9404208=无效的联系人邮箱," +
            "9404209=该账号不满足申请条件," +
            "9404210=已申请，请耐心等待"
    )
    private Integer state;

    @ApiModelProperty("消息")
    private String msg;
}
