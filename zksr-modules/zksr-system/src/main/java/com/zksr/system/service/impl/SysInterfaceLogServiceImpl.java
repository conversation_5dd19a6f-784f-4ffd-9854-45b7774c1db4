package com.zksr.system.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.constant.OpenApiConstants;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.domain.vo.openapi.receive.*;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.common.core.domain.vo.openapi.receive.PrdInventoryVO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuductOpenDTO;
import com.zksr.system.api.EmailMessage.dto.SyncEmailReportExcel;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.mapper.SysDictDataMapper;
import com.zksr.system.mq.SyncDataProducer;
import com.zksr.trade.api.status.ExpressStatusApi;
import com.zksr.trade.api.supplierAfter.SupplierAfterApi;
import com.zksr.trade.api.supplierOrder.SupplierOrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.system.mapper.SysInterfaceLogMapper;
import com.zksr.system.convert.log.SysInterfaceLogConvert;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.controller.log.vo.SysInterfaceLogPageReqVO;
import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;
import com.zksr.system.service.ISysInterfaceLogService;

import javax.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.OpenApiConstants.*;
import static com.zksr.common.core.constant.OpenApiConstants.REQ_STATUS_1;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 同步接口日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
@Service
@Slf4j
public class SysInterfaceLogServiceImpl implements ISysInterfaceLogService {
    @Autowired
    private SysInterfaceLogMapper sysInterfaceLogMapper;

    @Autowired
    private SyncDataProducer syncDataProducer;

    @Resource
    private ExpressStatusApi expressStatusApi;

    @Resource
    private SupplierOrderApi supplierOrderApi;

    @Resource
    private SupplierAfterApi supplierAfterApi;

    @Resource
    private SkuApi skuApi;

    @Resource
    private SpuApi spuApi;

    @Autowired
    private SysDictDataMapper sysDictDataMapper;

    /**
     * 新增同步接口日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysInterfaceLog(SysInterfaceLogSaveReqVO createReqVO) {
        // 插入
        SysInterfaceLog sysInterfaceLog = SysInterfaceLogConvert.INSTANCE.convert(createReqVO);
        sysInterfaceLogMapper.insert(sysInterfaceLog);
        // 返回
        return sysInterfaceLog.getId();
    }

    /**
     * 修改同步接口日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysInterfaceLog(SysInterfaceLogSaveReqVO updateReqVO) {
        sysInterfaceLogMapper.updateById(SysInterfaceLogConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除同步接口日志
     *
     * @param id id
     */
    @Override
    public void deleteSysInterfaceLog(Long id) {
        // 删除
        sysInterfaceLogMapper.deleteById(id);
    }

    /**
     * 批量删除同步接口日志
     *
     * @param ids 需要删除的同步接口日志主键
     * @return 结果
     */
    @Override
    public void deleteSysInterfaceLogByIds(Long[] ids) {
        for(Long id : ids){
            this.deleteSysInterfaceLog(id);
        }
    }

    /**
     * 获得同步接口日志
     *
     * @param id id
     * @return 同步接口日志
     */
    @Override
    public SysInterfaceLog getSysInterfaceLog(Long id) {
        return sysInterfaceLogMapper.selectById(id);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<SysInterfaceLogPageReqVO> getSysInterfaceLogPage(SysInterfaceLogPageReqVO pageReqVO) {
        Page<SysInterfaceLogPageReqVO> page = new Page<>(pageReqVO.getPageNo(),pageReqVO.getPageSize());

        //page 转换
        Page<SysInterfaceLogPageReqVO> logPage = sysInterfaceLogMapper.selectPageLog(pageReqVO, page);
        return new PageResult<>(logPage.getRecords(), logPage.getTotal());
    }

    @Override
    public SysInterfaceLog getSysInterfaceLogByReqId(String reqId) {
        return sysInterfaceLogMapper.selectByReqId(reqId);
    }

    @Override
    public void updateLogByResult(SysInterfaceLogSaveReqVO updateReqVO) {
        SysInterfaceLog interfaceLog = getSysInterfaceLogByReqId(updateReqVO.getReqId());

        //状态码
        Integer code = updateReqVO.getResultCode();

        if(Objects.isNull(interfaceLog)){
            log.info("发送日志更新失败未获取到日志信息reqId{}，code{}，message{}",updateReqVO.getReqId(),code,updateReqVO.getMessage());
            return;
        }

        if(OpenApiConstants.SUCCESS_0.equals(code) || OpenApiConstants.SUCCESS_1.equals(code)){
            interfaceLog.setStatus(LOG_STATUS_SUCCES);

        }else{
            //订单取消状态重新定义标识
            if(ToolUtil.isNotEmpty(updateReqVO.getMessage()) && updateReqVO.getMessage().contains( SYS_SYNC_ORDER_CANCEL_CHECK.getMsg())){
                interfaceLog.setStatus(LOG_STATUS_CANCEL);
            }else {
                interfaceLog.setStatus(LOG_STATUS_FAIN);
            }
        }
        interfaceLog.setMessage(Objects.nonNull(updateReqVO.getMessage()) ? updateReqVO.getMessage() : interfaceLog.getMessage());
        interfaceLog.setBizData(Objects.nonNull(updateReqVO.getBizData()) ? updateReqVO.getBizData() : interfaceLog.getBizData());
        interfaceLog.setReqStatus(Objects.nonNull(updateReqVO.getReqStatus()) ? updateReqVO.getReqStatus() : interfaceLog.getReqStatus());
        sysInterfaceLogMapper.updateById(interfaceLog);
    }

    @Override
    public void messageRetry(String reqId) {
        //消息重发
        //获取消息数据
        SysInterfaceLog sysInterfaceLog = getSysInterfaceLogByReqId(reqId);
        //校验
        if (Objects.isNull(sysInterfaceLog)) {
            throw exception(SYS_INTERFACE_LOG_NOT_EXISTS);
        }
        if (LOG_STATUS_SUCCES.equals(sysInterfaceLog.getStatus()) && REQ_STATUS_2.equals(sysInterfaceLog.getReqStatus())) {
            throw exception(SYS_INTERFACE_LOG_SUCCES);
        }
        //开始重发
        //更新日志重发状态
        sysInterfaceLog.setIsRetry(LOG_IS_RETRY_1);
        sysInterfaceLogMapper.updateById(sysInterfaceLog);

        if (LOG_TYPE_SYNC.equals(sysInterfaceLog.getLogType())) {
            //推送数据重发
            //异步处理需要同步的数据
            syncRetry(sysInterfaceLog);
        }else if(LOG_TYPE_RECEIVE.equals(sysInterfaceLog.getLogType())){
            //接收数据重发
            try {
                String result = receiveRetry(sysInterfaceLog);
                updateLogByResult(new SysInterfaceLogSaveReqVO(reqId, result, SUCCESS_1, REQ_STATUS_2));
                //更新重发状态
                updateLogRetryStatus(reqId);
            } catch (Exception e) {
                updateLogByResult(new SysInterfaceLogSaveReqVO(reqId, e.getMessage(), OpenApiConstants.ERROR, REQ_STATUS_1));
                //更新重发状态
                updateLogRetryStatus(reqId);
                throw new ServiceException("接收数据重发失败，错误信息：" +  e.getMessage());
            }
        }
    }

    /**
     * 更新重发状态
     * @param reqId
     */
    public void updateLogRetryStatus(String reqId){
        SysInterfaceLog sysInterfaceLog = sysInterfaceLogMapper.selectByReqId(reqId);
        //消息重发一次  将重发状态修改 并增加重发次数
        sysInterfaceLog.setIsRetry(LOG_IS_RETRY_0);
        sysInterfaceLog.setRetryCount(Long.sum(sysInterfaceLog.getRetryCount(), NumberPool.LONG_ONE));
        sysInterfaceLogMapper.updateById(sysInterfaceLog);
    }


    /**
     * 接收日志重发
     * @param sysInterfaceLog
     * @return
     */
    private String receiveRetry(SysInterfaceLog sysInterfaceLog){

        Long sysCode = sysInterfaceLog.getSysCode();
        Long opensourceId = sysInterfaceLog.getOpensourceId();

        //返回值
        String result = RECEIVE_SUCCESS;
        switch (sysInterfaceLog.getRequestType()){
            case 1:
                //开始重发---退货确认
                supplierAfterApi.receiveSalesReturn(sysCode, opensourceId, JSONObject.parseObject(sysInterfaceLog.getBizData(), AfterSalesReturnVO.class)).getCheckedData();;
                break;
            case 2:
                //开始重发---订单收货
                supplierAfterApi.receiveOrderTakeDelivery(sysCode, opensourceId, JSONObject.parseObject(sysInterfaceLog.getBizData(), ReceiveOrderTakeDeliveryVO.class)).getCheckedData();
                break;
            case 3:
                //开始重发---订单发货
                supplierOrderApi.receiveOrderOutboundReturn(sysCode, opensourceId, JSONObject.parseObject(sysInterfaceLog.getBizData(), OrderOutboundReturnVO.class)).getCheckedData();;
                break;
            case 4:
                //开始重发---订单状态
                expressStatusApi.saveExpressStatus(sysCode, opensourceId, JSONObject.parseObject(sysInterfaceLog.getBizData(), OrderStateReturnVO.class)).getCheckedData();;
                break;
            case 5:
                //开始重发---商品生产日期
                spuApi.updateProduct(sysCode,opensourceId,JSONObject.parseObject(sysInterfaceLog.getBizData(), SpuductOpenDTO.class)).getCheckedData();;
                break;
            case 6:
                //开始重发---商品库存
                PrdInventoryVO prdInventoryVO = JSON.parseObject(sysInterfaceLog.getBizData(), PrdInventoryVO.class);
                prdInventoryVO.setSupplierId(sysInterfaceLog.getSupplierId());
                prdInventoryVO.setSysCode(sysInterfaceLog.getSysCode());
                prdInventoryVO.setSource(SyncSourceType.matchingType(sysInterfaceLog.getSource()));
                skuApi.editInventory(sysCode, opensourceId,prdInventoryVO).getCheckedData();;
                break;
            case 7:
                //开始重发---商品信息
                //组装商品数据
                SpuReceiveVO spuReceiveVO = new SpuReceiveVO();
                SpuOpenDTO spu = JSONObject.parseObject(sysInterfaceLog.getBizData(), SpuOpenDTO.class);
                spuReceiveVO.setSpuOpenDTO(ListUtil.toList(spu));
                spuReceiveVO.setSupplierId(sysInterfaceLog.getSupplierId());
                spuReceiveVO.setSysCode(sysInterfaceLog.getSysCode());
                spuApi.addOrUpdateSpu(sysCode, opensourceId, spuReceiveVO).getCheckedData();;
                break;
            case 8:
                //开始重发---订单接收通知
                //执行订单操作  需要getCheckedData  不然获取不到异常
                supplierOrderApi.orderReceiveCallback(sysInterfaceLog.getSysCode(),
                        sysInterfaceLog.getOpensourceId(),
                        JSONObject.parseObject(sysInterfaceLog.getBizData(), OrderReceiveCallbackVO.class)).getCheckedData();
                break;
            case 12:
                //开始重发---发货前取消
                //执行订单操作  需要getCheckedData  不然获取不到异常
                supplierOrderApi.orderCancel(sysInterfaceLog.getSysCode(),
                        sysInterfaceLog.getOpensourceId(),
                        JSONObject.parseObject(sysInterfaceLog.getBizData(), OrderCancelVO.class)).getCheckedData();
                break;
            case 15:
                //开始重发---退货确认前取消(售后取消)
                //执行订单操作  需要getCheckedData  不然获取不到异常
                supplierAfterApi.afterCancel(sysInterfaceLog.getSysCode(),
                        sysInterfaceLog.getOpensourceId(),
                        JSONObject.parseObject(sysInterfaceLog.getBizData(), AfterCancelVO.class)).getCheckedData();
                break;
            case 16:
                //开始重发---售后状态
                expressStatusApi.saveExpressStatus(sysCode,
                        opensourceId,
                        JSONObject.parseObject(sysInterfaceLog.getBizData(), AfterStateVO.class)).getCheckedData();;
                break;
            default:
                throw exception(SYS_INTERFACE_LOG_NOT_EXISTS);

        }
        return result;
    }


    /**
     * 同步日志重发
     * @param sysInterfaceLog
     * @return
     */
    private void syncRetry(SysInterfaceLog sysInterfaceLog) {
        //推送数据重发
        SyncDataDTO dataDTO = JSONObject.parseObject(sysInterfaceLog.getReqData(), SyncDataDTO.class);
        //异步处理需要同步的数据
        switch (sysInterfaceLog.getRequestType()) {
            case 501:
                //开始重发---入驻商门店信息同步
                syncDataProducer.sendSupplierSyncDataBranchEvent(dataDTO);
                break;
            case 502:
                //开始重发---入驻商销售订单信息同步
                //实时异步重发
                dataDTO.setPropertyDelayTimeLevel(1);
                syncDataProducer.sendSupplierSyncDataOrderEvent(dataDTO);
                break;
            case 503:
                //开始重发---入驻商售后订单信息同步
                syncDataProducer.sendSupplierSyncDataAfterEvent(dataDTO);
                break;
            case 504:
                //开始重发---入驻商收款单信息同步
                syncDataProducer.sendSupplierSyncDataReceiptEvent(dataDTO);
                break;
            case 505:
                //开始重发---入驻商门店储值充值、提现信息同步
                syncDataProducer.sendSupplierSyncDataBranchValueInfoEvent(dataDTO);
                break;
            default:
                throw exception(SYS_INTERFACE_LOG_NOT_EXISTS);
        }
    }

    public boolean insertBatch(List<SysInterfaceLog> list){
        return sysInterfaceLogMapper.insertBatch(list);
    }

    @Override
    public int insert(SysInterfaceLog log){
        return sysInterfaceLogMapper.insert(log);
    }

    public boolean insertLog(SysInterfaceLog log){
        return sysInterfaceLogMapper.insertOrUpdate(log);
    }

    @Override
    public boolean updateLog(SysInterfaceLog log) {
        return sysInterfaceLogMapper.updateById(log) > 0;
    }

    @Override
    public List<SyncEmailReportExcel> getLogByDaySyncReport(Long sysCode, String date) {
        List<SyncEmailReportExcel> excelList = sysInterfaceLogMapper.getLogByDaySyncReport(sysCode, date);

        // 系统类型数据字典
        Map<String,String> sourceTypeMap = new HashMap<>();

        //请求接口类型数据字典
        Map<String,String> requestTypeMap = new HashMap<>();

        if(!excelList.isEmpty()){
            sourceTypeMap = sysDictDataMapper.selectDictDataByType("source_type").stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

            requestTypeMap = sysDictDataMapper.selectDictDataByType("log_request_type").stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        }

        //设置参数信息
        for (SyncEmailReportExcel excel : excelList) {
            //设置系统名称
            excel.setSourceName(sourceTypeMap.get(String.valueOf(excel.getSourceType())));
            //设置接口信息
            excel.setRequestInfo(requestTypeMap.get(String.valueOf(excel.getRequestType())));
        }
        return excelList;
    }

    @Override
    public SysInterfaceLog getById(Long logId) {
        return sysInterfaceLogMapper.selectById(logId);
    }

    private void validateSysInterfaceLogExists(Long id) {
        if (sysInterfaceLogMapper.selectById(id) == null) {
            throw exception(SYS_INTERFACE_LOG_NOT_EXISTS);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.system.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 同步接口日志 TODO 补充编号 ==========
    // ErrorCode SYS_INTERFACE_LOG_NOT_EXISTS = new ErrorCode(TODO 补充编号, "同步接口日志不存在");


}
