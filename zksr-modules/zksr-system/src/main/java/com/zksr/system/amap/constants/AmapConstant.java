package com.zksr.system.amap.constants;

/**
 * @Description: 高德地图Url常量类
 * @Author: liuxingyu
 * @Date: 2024/3/26 17:36
 */
public interface AmapConstant {
    /**
     * 查询服务
     */
    String GET_SERVICE = "https://tsapi.amap.com/v1/track/service/list";
    /**
     * 创建服务
     */
    String ADD_SERVICE = "https://tsapi.amap.com/v1/track/service/add";
    /**
     * 删除服务
     */
    String DELETE_SERVICE = "https://tsapi.amap.com/v1/track/service/delete";
    /**
     * 修改服务
     */
    String UPDATE_SERVICE = "https://tsapi.amap.com/v1/track/service/update";
    /**
     * 创建多边形围栏
     */
    String ADD_POLYGON = "https://tsapi.amap.com/v1/track/geofence/add/polygon";
    /**
     * 修改多边形围栏
     */
    String UPDATE_POLYGON = "https://tsapi.amap.com/v1/track/geofence/update/polygon";
    /**
     * 查询多边形围栏
     */
    String GET_POLYGON = "https://tsapi.amap.com/v1/track/geofence/list";
    /**
     * 删除多边形围栏
     */
    String DELETE_POLYGON = "https://tsapi.amap.com/v1/track/geofence/delete";
    /**
     * 查询指定坐标与围栏关系
     */
    String GEOFENCE_STATUS_LOCATION = "https://tsapi.amap.com/v1/track/geofence/status/location";

    /**
     * 查询地址所对应的具体位置信息
     */
    String LONGITUDE_AND_LATITUDE = "https://restapi.amap.com/v3/geocode/geo";
}
