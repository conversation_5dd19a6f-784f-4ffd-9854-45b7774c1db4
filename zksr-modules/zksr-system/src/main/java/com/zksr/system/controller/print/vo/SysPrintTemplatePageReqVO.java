package com.zksr.system.controller.print.vo;

import java.math.BigDecimal;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 打印模版对象 sys_print_template
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@ApiModel("打印模版 - sys_print_template分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysPrintTemplatePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty(value = "纸张类型")
    private Long printTemplateId;

    /** 模块名称 */
    @Excel(name = "模块名称")
    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    /** 模块类型 */
    @Excel(name = "模块类型")
    @ApiModelProperty(value = "模块类型")
    private String moduleType;

    /** 模板宽度 */
    @Excel(name = "模板宽度")
    @ApiModelProperty(value = "模板宽度")
    private BigDecimal templateWidth;

    /** 模板高度 */
    @Excel(name = "模板高度")
    @ApiModelProperty(value = "模板高度")
    private BigDecimal templateHeight;

    /** 纸张宽度 */
    @Excel(name = "纸张宽度")
    @ApiModelProperty(value = "纸张宽度")
    private BigDecimal paperWidth;

    /** 纸张高度 */
    @Excel(name = "纸张高度")
    @ApiModelProperty(value = "纸张高度")
    private BigDecimal paperHeight;

    /** 打印内容 */
    @Excel(name = "打印内容")
    @ApiModelProperty(value = "打印内容")
    private String printContent;

    /** 删除标志(0正常;1删除) */
    @ApiModelProperty(value = "打印内容")
    private Integer delFlag;

    /** 纸张类型 */
    @Excel(name = "纸张类型")
    @ApiModelProperty(value = "纸张类型")
    private String paperType;


}
