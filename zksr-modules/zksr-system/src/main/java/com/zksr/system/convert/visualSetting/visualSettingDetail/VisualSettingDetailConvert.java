package com.zksr.system.convert.visualSetting.visualSettingDetail;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailRespVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailSaveReqVO;
import com.zksr.system.domain.VisualSettingDetail;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 可视化配置详情 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-06-29
*/
@Mapper
public interface VisualSettingDetailConvert {

    VisualSettingDetailConvert INSTANCE = Mappers.getMapper(VisualSettingDetailConvert.class);

    VisualSettingDetailRespVO convert(VisualSettingDetail visualSettingDetail);

    VisualSettingDetail convert(VisualSettingDetailSaveReqVO visualSettingDetailSaveReq);

    PageResult<VisualSettingDetailRespVO> convertPage(PageResult<VisualSettingDetail> visualSettingDetailPage);
}