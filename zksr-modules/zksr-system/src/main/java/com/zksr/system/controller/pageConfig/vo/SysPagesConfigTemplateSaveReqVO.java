package com.zksr.system.controller.pageConfig.vo;

import lombok.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 平台页面配置模版对象 sys_pages_config_template
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Data
@ApiModel("平台页面配置模版 - sys_pages_config_template分页 Request VO")
public class SysPagesConfigTemplateSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 自定义页面ID */
    @ApiModelProperty(value = "0-没有子页面,1-有子页面")
    private Long pageTemplateId;

    /** 启用时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "启用时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "启用时间")
    private Date enableTime;

    /** 页面名称 */
    @Excel(name = "页面名称")
    @ApiModelProperty(value = "页面名称")
    private String pageName;

    /** 页面类型;页面类型,index-首页 */
    @Excel(name = "页面类型;页面类型,index-首页")
    @ApiModelProperty(value = "页面类型;页面类型,index-首页")
    private String pageType;

    /** 页面配置JSON */
    @Excel(name = "页面配置JSON")
    @ApiModelProperty(value = "页面配置JSON")
    private String pageConfig;

    /** 状态 (0正常 1停用) */
    @Excel(name = "状态 (0正常 1停用)")
    @ApiModelProperty(value = "状态 (0正常 1停用)")
    private String status;

    /** 删除状态 (0正常 2已删除) */
    @ApiModelProperty(value = "状态 (0正常 1停用)")
    private String delFlag;

    /** 父级页面 */
    @Excel(name = "父级页面")
    @ApiModelProperty(value = "父级页面")
    private Long pid;

    /** 1-一级页面, 2-二级页面 */
    @Excel(name = "1-一级页面, 2-二级页面")
    @ApiModelProperty(value = "1-一级页面, 2-二级页面")
    private Integer level;

    /** 0-没有子页面,1-有子页面 */
    @Excel(name = "0-没有子页面,1-有子页面")
    @ApiModelProperty(value = "0-没有子页面,1-有子页面")
    private Integer hasChild;

}
