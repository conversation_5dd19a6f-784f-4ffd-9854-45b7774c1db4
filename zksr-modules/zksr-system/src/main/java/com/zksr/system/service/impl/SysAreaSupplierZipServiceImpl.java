package com.zksr.system.service.impl;


import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.system.controller.supplier.vo.SysSupplierSaveReqVO;
import com.zksr.system.domain.SysAreaSupplierZip;
import com.zksr.system.domain.SysSupplier;
import com.zksr.system.mapper.SysAreaSupplierZipMapper;
import com.zksr.system.service.ISysAreaSupplierZipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 区域城市入驻商关系拉链表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Service
public class SysAreaSupplierZipServiceImpl implements ISysAreaSupplierZipService {
    @Autowired
    private SysAreaSupplierZipMapper sysAreaSupplierZipMapper;
    @Override
    @Transactional
    public void insertSysAreaSupplierZip(Long areaId, SysSupplierSaveReqVO reqVO, SysSupplier sysSupplier) {
         //获取时间
        Date nowDate = DateUtils.getNowDate();
        Date endOfYearDate = DateUtils.toDate(LocalDateTime.of(2099, 12, 30, 23, 59, 59));
        SysAreaSupplierZip currentRelation = sysAreaSupplierZipMapper.selectSysAreaSupplierZip(sysSupplier,areaId);
        if(ToolUtil.isEmpty(currentRelation)){
            // 插入新的区域城市和入驻商关联记录
            SysAreaSupplierZip newRelation = new SysAreaSupplierZip();
            newRelation.setSysCode(reqVO.getSysCode());
            newRelation.setAreaId(areaId);
            newRelation.setSupplierId(reqVO.getSupplierId());
            newRelation.setStartDate(nowDate);
            newRelation.setEndDate(endOfYearDate);
            sysAreaSupplierZipMapper.insert(newRelation);
        }else{
            if(Objects.equals(currentRelation.getSysCode(), reqVO.getSysCode())
            && Objects.equals(currentRelation.getAreaId(), areaId)
            && !Objects.equals(currentRelation.getSupplierId(), reqVO.getSupplierId())){
                currentRelation.setEndDate(nowDate);
                sysAreaSupplierZipMapper.updateSysAreaSupplierZip(currentRelation);

                SysAreaSupplierZip newRelation = HutoolBeanUtils.toBean(currentRelation, SysAreaSupplierZip.class);
                newRelation.setAreaSupplierZipId(null);
                newRelation.setCreateTime(nowDate);
                newRelation.setStartDate(nowDate);
                newRelation.setEndDate(endOfYearDate);
                newRelation.setSupplierId(reqVO.getSupplierId());
                sysAreaSupplierZipMapper.insert(newRelation);
            }
        }
    }
}
