package com.zksr.system.controller.partnerConfig;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.api.partnerConfig.dto.WxB2bPayAppletStatusDTO;
import com.zksr.system.api.partnerConfig.enums.PartnerConfigEnum;
import com.zksr.system.controller.partnerConfig.vo.*;
import com.zksr.system.domain.SysPartnerConfig;
import com.zksr.system.service.ISysPartnerConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.ibatis.annotations.Param;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台商配置(由软件商设置)Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Api(tags = "管理后台 - 平台商配置(由软件商设置)接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/partnerConfig")
public class SysPartnerConfigController {
    @Autowired
    private ISysPartnerConfigService sysPartnerConfigService;

    /**
     * @Description: 通过平台编号获取所有配置信息
     * @Param:
     * @return: SysPartnerConfigSaveReqVO
     * @Author: liuxingyu
     * @Date: 2024/3/13 14:48
     */
    @ApiOperation(value = "获取配置信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET_PARTNER_CONFIG)
    @RequiresPermissions(Permissions.GET_PARTNER_CONFIG)
    @GetMapping("/getPartnerConfig")
    public CommonResult<SysPartnerConfigRespVO> GetPartnerConfig(@RequestParam Long sysCode) {
        return success(sysPartnerConfigService.getPartnerConfig(sysCode));
    }

    /**
     * @Description: 保存配置信息
     * @Param: SysPartnerConfigSaveReqVO createReqVO,Long sysCode
     * @return:
     * @Author: liuxingyu
     * @Date: 2024/3/12 17:08
     */
    @ApiOperation(value = "保存配置信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SAVE_PARTNER_CONFIG)
    @RequiresPermissions(Permissions.SAVE_PARTNER_CONFIG)
    @Log(title = "保存配置信息", businessType = BusinessType.INSERT)
    @PostMapping("/savePartnerConfig/{sysCode}")
    public CommonResult<Boolean> SavePartnerConfig(@RequestBody SysPartnerConfigSaveReqVO createReqVO, @PathVariable Long sysCode) {
        return success(sysPartnerConfigService.savePartnerConfig(createReqVO, sysCode));
    }

    /**
     * 新增平台商配置(由软件商设置)
     */
    @ApiOperation(value = "新增平台商配置(由软件商设置)", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "平台商配置(由软件商设置)", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysPartnerConfigSaveReqVO createReqVO) {
        return success(sysPartnerConfigService.insertSysPartnerConfig(createReqVO));
    }

    /**
     * 修改平台商配置(由软件商设置)
     */
    @ApiOperation(value = "修改平台商配置(由软件商设置)", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "平台商配置(由软件商设置)", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysPartnerConfigSaveReqVO updateReqVO) {
        sysPartnerConfigService.updateSysPartnerConfig(updateReqVO);
        return success(true);
    }

    /**
     * 删除平台商配置(由软件商设置)
     */
    @ApiOperation(value = "删除平台商配置(由软件商设置)", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "平台商配置(由软件商设置)", businessType = BusinessType.DELETE)
    @DeleteMapping("/{partnerConfigIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] partnerConfigIds) {
        sysPartnerConfigService.deleteSysPartnerConfigByPartnerConfigIds(partnerConfigIds);
        return success(true);
    }

    /**
     * 获取平台商配置(由软件商设置)详细信息
     */
    @ApiOperation(value = "获得平台商配置(由软件商设置)详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{partnerConfigId}")
    public CommonResult<SysPartnerConfigRespVO> getInfo(@PathVariable("partnerConfigId") Long partnerConfigId) {
        SysPartnerConfig sysPartnerConfig = sysPartnerConfigService.getSysPartnerConfig(partnerConfigId);
        return success(HutoolBeanUtils.toBean(sysPartnerConfig, SysPartnerConfigRespVO.class));
    }

    /**
     * 分页查询平台商配置(由软件商设置)
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台商配置(由软件商设置)分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysPartnerConfigRespVO>> getPage(@Valid SysPartnerConfigPageReqVO pageReqVO) {
        PageResult<SysPartnerConfig> pageResult = sysPartnerConfigService.getSysPartnerConfigPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, SysPartnerConfigRespVO.class));
    }

    /**
     * 查询小程序b2b支付认证状态
     */
    @GetMapping("/getAppWxB2bState")
    @ApiOperation(value = "查询小程序b2b支付认证状态", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET_PARTNER_CONFIG)
    @RequiresPermissions(Permissions.GET_PARTNER_CONFIG)
    public CommonResult<WxB2bPayAppletStatusDTO> getAppWxB2bState(@RequestParam("appid") @ApiParam(name = "appid", value = "小程序appid") String appid) {
        return success(sysPartnerConfigService.getAppWxB2bState(appid));
    }

    /**
     * 获取小程序授权链接
     */
    @GetMapping("/getAppWxB2bAuthUrl")
    @ApiOperation(value = "获取小程序授权链接", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET_PARTNER_CONFIG)
    @RequiresPermissions(Permissions.GET_PARTNER_CONFIG)
    public CommonResult<String> getAppWxB2bAuthUrl(@RequestParam("appid") @ApiParam(name = "appid", value = "小程序appid") String appid) {
        return success(sysPartnerConfigService.getAppWxB2bAuthUrl(appid));
    }

    /**
     * 开通门店b2b支付助手
     */
    @PostMapping("/openWxB2bPay")
    @ApiOperation(value = "开通门店b2b支付助手", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SAVE_PARTNER_CONFIG)
    @RequiresPermissions(Permissions.SAVE_PARTNER_CONFIG)
    public CommonResult<SysSaveOpenB2bPayRespVO> openWxB2bPay(@RequestBody SysSaveOpenB2bPayReqVO reqVO) {
        return success(sysPartnerConfigService.openWxB2bPay(reqVO));
    }

    /**
     * 报名b2b支付技术服务费
     */
    @PostMapping("/setB2bPayProfitRate")
    @ApiOperation(value = "报名b2b支付技术服务费", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SAVE_PARTNER_CONFIG)
    @RequiresPermissions(Permissions.SAVE_PARTNER_CONFIG)
    public CommonResult<SysSaveOpenB2bPayRespVO> setB2bPayProfitRate(@RequestBody SysB2bPayProfitRateReqVO reqVO) {
        return success(sysPartnerConfigService.setB2bPayProfitRate(reqVO));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "system:partnerConfig:add";
        /**
         * 编辑
         */
        public static final String EDIT = "system:partnerConfig:edit";
        /**
         * 删除
         */
        public static final String DELETE = "system:partnerConfig:remove";
        /**
         * 列表
         */
        public static final String LIST = "system:partnerConfig:list";
        /**
         * 查询
         */
        public static final String GET = "system:partnerConfig:query";
        /**
         * 停用
         */
        public static final String DISABLE = "system:partnerConfig:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "system:partnerConfig:enable";
        /**
         * 保存配置信息
         */
        public static final String SAVE_PARTNER_CONFIG = "system:partnerConfig:savePartnerConfig";

        /**
         * 获取配置信息
         */
        public static final String GET_PARTNER_CONFIG = "system:partnerConfig:getPartnerConfig";
    }
}
