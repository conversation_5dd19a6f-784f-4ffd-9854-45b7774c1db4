package com.zksr.system.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysPartner;
import com.zksr.system.api.partner.vo.SysPartnerPageReqVO;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 平台商信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Mapper
public interface SysPartnerMapper extends BaseMapperX<SysPartner> {
    default PageResult<SysPartner> selectPage(SysPartnerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysPartner>()
                    .eqIfPresent(SysPartner::getSysCode, reqVO.getSysCode())
                    .likeIfPresent(SysPartner::getPartnerName, reqVO.getPartnerName())
                    .eqIfPresent(SysPartner::getPartnerUserId, reqVO.getPartnerUserId())
                    .likeIfPresent(SysPartner::getContactName, reqVO.getContactName())
                    .eqIfPresent(SysPartner::getContactPhone, reqVO.getContactPhone())
                    .eqIfPresent(SysPartner::getContactAddress, reqVO.getContactAddress())
                    .eqIfPresent(SysPartner::getStatus, reqVO.getStatus())
                    .eqIfPresent(SysPartner::getMemo, reqVO.getMemo())
                    .eqIfPresent(SysPartner::getSource, reqVO.getSource())
                    .eqIfPresent(SysPartner::getPartnerCode, reqVO.getPartnerCode())
                    .eqIfPresent(SysPartner::getSoftwareId, reqVO.getSoftwareId())
                .orderByDesc(SysPartner::getSysCode));
    }

    List<SysPartner> selectSysPartnerList(SysPartnerPageReqVO reqVO);

    default Integer selectByPartnerCode(String partnerCode) {
        Long count = selectCount(
                Wrappers.lambdaQuery(SysPartner.class)
                        .eq(SysPartner::getPartnerCode, partnerCode)
        );
        return count.intValue();
    }


    @InterceptorIgnore(tenantLine = "1")
    SysPartner getByCacheKey(String cacheKey);

    default List<Long> getSysCodeListBySoftwareId(Long softwareId){
        return selectList(
                Wrappers.lambdaQuery(SysPartner.class)
                        .eq(SysPartner::getSoftwareId, softwareId)
                        .select(SysPartner::getSysCode)
        ).stream().map(SysPartner::getSysCode).collect(Collectors.toList());
    }

    Long countSelectSysPartnerList(SysPartnerPageReqVO pageReqVO);
}
