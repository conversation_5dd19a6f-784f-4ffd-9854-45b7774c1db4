package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.page.dto.PagesConfigDTO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigCopyReqVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigPageReqVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigRespVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigSaveReqVO;
import com.zksr.system.domain.SysPagesConfig;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台页面配置Service接口
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
public interface ISysPagesConfigService {

    /**
     * 新增平台页面配置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysPagesConfig(@Valid SysPagesConfigSaveReqVO createReqVO);

    /**
     * 修改平台页面配置
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysPagesConfig(@Valid SysPagesConfigSaveReqVO updateReqVO);

    /**
     * 删除平台页面配置
     *
     * @param pageId 自定义页面ID
     */
    public void deleteSysPagesConfig(Long pageId);

    /**
     * 批量删除平台页面配置
     *
     * @param pageIds 需要删除的平台页面配置主键集合
     * @return 结果
     */
    public void deleteSysPagesConfigByPageIds(Long[] pageIds);

    /**
     * 获得平台页面配置
     *
     * @param pageId 自定义页面ID
     * @return 平台页面配置
     */
    public SysPagesConfig getSysPagesConfig(Long pageId);

    /**
     * 获得平台页面配置分页
     *
     * @param pageReqVO 分页查询
     * @return 平台页面配置分页
     */
    PageResult<SysPagesConfig> getSysPagesConfigPage(SysPagesConfigPageReqVO pageReqVO);

    /**
     * 获取平台配置扩展数据分页
     * @param pageReqVO
     * @return 平台页面配置分页
     */
    PageResult<SysPagesConfigRespVO> getSysPagesConfigPageExt(SysPagesConfigPageReqVO pageReqVO);

    /**
     * 更新配置是否启用
     * @param updateReqVO
     */
    void updateStatus(SysPagesConfigSaveReqVO updateReqVO);

    /**
     * 更新默认配置
     * @param updateReqVO
     */
    void updateDefFlag(SysPagesConfigSaveReqVO updateReqVO);

    /**
     * 获取默认首页配置
     * @param sysCode   平台商ID
     * @param areaId    区域城市ID
     * @param channelId 渠道ID
     * @return
     */
    List<PagesConfigDTO> getByAreaChannel(Long sysCode, Long areaId, Long channelId);

    /**
     * 重载缓存
     * @param pageId
     */
    void reloadCache(Long pageId);

    /**
     * 复制首页装修模版
     * @param configCopyReqVO   复制参数
     */
    void copy(SysPagesConfigCopyReqVO configCopyReqVO);
}
