package com.zksr.system.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 可视化配置主对象 visual_setting_master
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@TableName(value = "visual_setting_master")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisualSettingMaster extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** id */
    @TableId
    private Long visualMasterId;

    /** 平台名称 */
    @Excel(name = "平台名称")
    private String platformName;

    /** 加密类型（数据字典send_encrypt_type） */
    @Excel(name = "加密类型", readConverterExp = "数=据字典send_encrypt_type")
    private Long encryptType;

    /** 状态（0 停用  1启用） */
    @Excel(name = "状态", readConverterExp = "0=,停=用,1=启用")
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 对接类型(可视化对接、定制化对接) */
    @Excel(name = "对接类型(可视化对接、安得定制化对接)")
    private String sendType;

    /** 系统类型(数据字典sync_source_type 枚举：syncSourceType) */
    @Excel(name = "系统类型(安得ERP、安得WMS、新ERP)")
    private Integer sourceType;

    /** 获取公共配置方式 */
    @Excel(name = "获取公共配置方式(0可视化接口配置 1入驻商配置 2优先入驻商配置)")
    private Integer commonSettingType;

    /** -------------------- 公共配置 -------------------- */
    /** 公钥 */
    @Excel(name = "公钥")
    private String publicKey;

    /** 私钥 */
    @Excel(name = "私钥")
    private String privateKey;

    /** 对接地址 */
    @Excel(name = "对接地址")
    private String sendUrl;

    /** -------------------- 公共配置结束 -------------------- */
}
