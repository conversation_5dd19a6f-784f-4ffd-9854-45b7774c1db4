package com.zksr.system.controller.brand;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.controller.brand.vo.SysBrandMemberPageReqVO;
import com.zksr.system.controller.brand.vo.SysBrandMemberRespVO;
import com.zksr.system.controller.brand.vo.SysBrandMemberSaveReqVO;
import com.zksr.system.convert.brand.SysBrandMemberConvert;
import com.zksr.system.domain.SysBrandMember;
import com.zksr.system.service.ISysBrandMemberService;
import com.zksr.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 品牌商子账户子账户Controller
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Api(tags = "管理后台 - 品牌商子账户子账户接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/brandMember")
public class SysBrandMemberController {

    @Autowired
    private ISysBrandMemberService sysBrandMemberService;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 新增品牌商子账户子账户
     */
    @ApiOperation(value = "新增品牌商子账户子账户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "品牌商子账户子账户", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysBrandMemberSaveReqVO createReqVO) {
        return success(sysBrandMemberService.insertSysBrandMember(createReqVO));
    }

    /**
     * 修改品牌商子账户子账户
     */
    @ApiOperation(value = "修改品牌商子账户子账户", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "品牌商子账户子账户", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysBrandMemberSaveReqVO updateReqVO) {
        sysBrandMemberService.updateSysBrandMember(updateReqVO);
        return success(true);
    }

    /**
     * 删除品牌商子账户子账户
     */
    @ApiOperation(value = "删除品牌商子账户子账户", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "品牌商子账户子账户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{brandMemberIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] brandMemberIds) {
        sysBrandMemberService.deleteSysBrandMemberByBrandMemberIds(brandMemberIds);
        return success(true);
    }

    /**
     * 获取品牌商子账户子账户详细信息
     */
    @ApiOperation(value = "获得品牌商子账户子账户详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{brandMemberId}")
    public CommonResult<SysBrandMemberRespVO> getInfo(@PathVariable("brandMemberId") Long brandMemberId) {
        SysBrandMember sysBrandMember = sysBrandMemberService.getSysBrandMember(brandMemberId);
        SysBrandMemberRespVO respVO = SysBrandMemberConvert.INSTANCE.convert(sysBrandMember);
        respVO.setUsername(sysUserService.getBySysUserId(sysBrandMember.getSysUserId()));
        return success(respVO);
    }

    /**
     * 分页查询品牌商子账户子账户
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得品牌商子账户子账户分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysBrandMemberRespVO>> getPage(@Valid SysBrandMemberPageReqVO pageReqVO) {
        PageResult<SysBrandMemberRespVO> pageResult = sysBrandMemberService.getSysBrandMemberPageExt(pageReqVO);
        return success(pageResult);
    }
    /**
     * 停用品牌商子账户
     */
    @ApiOperation(value = "停用品牌商子账户", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "停用品牌商子账户", businessType = BusinessType.UPDATE)
    @PutMapping("/disable")
    public CommonResult<Boolean> disable(@ApiParam(name = "brandMemberId", value = "品牌商子账户ID", required = true) @RequestParam("brandMemberId") Long brandMemberId) {
        sysBrandMemberService.disable(brandMemberId);
        return success(true);
    }

    /**
     * 启用品牌商子账户
     */
    @ApiOperation(value = "启用品牌商子账户", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "启用品牌商子账户", businessType = BusinessType.UPDATE)
    @PutMapping("/enable")
    public CommonResult<Boolean> enable(@ApiParam(name = "brandMemberId", value = "品牌商子账户ID", required = true) @RequestParam("brandMemberId") Long brandMemberId) {
        sysBrandMemberService.enable(brandMemberId);
        return success(true);
    }

    /**
     * 修改品牌商子账户子账户密码
     */
    @ApiOperation(value = "修改品牌商子账户子账户密码", httpMethod = HttpMethod.PUT, notes = "system:brand-member:edit-password")
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "修改品牌商子账户子账户密码", businessType = BusinessType.UPDATE)
    @PutMapping("/editBrandMemberPassword")
    public CommonResult<Boolean> editBrandMemberPassword(@Valid @RequestBody SysBrandMemberSaveReqVO updateReqVO) {
        sysBrandMemberService.updateSysBrandMemberPassword(updateReqVO);
        return success(true);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:brand-member:add";
        /** 编辑 */
        public static final String EDIT = "system:brand-member:edit";
        /** 删除 */
        public static final String DELETE = "system:brand-member:remove";
        /** 列表 */
        public static final String LIST = "system:brand-member:list";
        /** 查询 */
        public static final String GET = "system:brand-member:query";
        /** 停用 */
        public static final String DISABLE = "system:brand-member:disable";
        /** 启用 */
        public static final String ENABLE = "system:brand-member:enable";
    }
}
