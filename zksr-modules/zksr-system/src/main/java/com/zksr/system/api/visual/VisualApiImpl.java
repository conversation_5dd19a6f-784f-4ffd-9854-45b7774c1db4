package com.zksr.system.api.visual;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.service.IVisualSettingDetailService;
import com.zksr.system.service.IVisualSettingMasterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class VisualApiImpl implements VisualApi{

    @Autowired
    private IVisualSettingMasterService visualSettingMasterService;

    @Autowired
    private IVisualSettingDetailService visualSettingDetailService;
    @Override
    public CommonResult<VisualSettingMasterDto> getVisualMasterById(Long visualMasterId) {
        return CommonResult.success(HutoolBeanUtils.toBean(visualSettingMasterService.getVisualSettingMaster(visualMasterId),VisualSettingMasterDto.class));
    }

    @Override
    public CommonResult<VisualSettingDetailDto> getVisualDetailByMasterIdAndTemplate(Long visualMasterId, Long templateType) {
        return CommonResult.success(HutoolBeanUtils.toBean(visualSettingDetailService.getVisualSettingDetailByMasterId(visualMasterId,templateType),VisualSettingDetailDto.class));
    }
}
