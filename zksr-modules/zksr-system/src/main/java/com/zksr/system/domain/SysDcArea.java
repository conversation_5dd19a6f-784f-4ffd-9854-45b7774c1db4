package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 运营商-区域城市关联关系对象 sys_dc_area
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@TableName(value = "sys_dc_area")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysDcArea{
    private static final long serialVersionUID=1L;

    /** 运营商id;运营商id */
    @Excel(name = "运营商id;运营商id")
    private Long dcId;

    /** 区域城市id;区域城市id */
    @Excel(name = "区域城市id;区域城市id")
    private Long areaId;

    /** 平台商id;平台商id */
    @Excel(name = "平台商id;平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

}
