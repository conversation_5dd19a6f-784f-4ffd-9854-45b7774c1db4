package com.zksr.system.amap.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 查询指定坐标与围栏关系请求对象
 * @Author: liuxingyu
 * @Date: 2024/3/27 16:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckLocationParam {
    /**
     * 用户在高德地图官网申请Web服务API类型Key
     */
    @ApiModelProperty("高德Key")
    private String key;

    /**
     * sid为猎鹰service唯一编号
     */
    @ApiModelProperty("服务唯一编号")
    private Long sid;

    /**
     * 格式：x,y
     */
    @ApiModelProperty("指定坐标")
    private String location;

    /**
     * 支持一次传入多个，以","分割；
     * 单次最多支持100个，如超出将只截取前100个作为输入。
     */
    @ApiModelProperty("围栏id")
    private String gfids;

    /**
     * 可选，默认1
     */
    @ApiModelProperty("查询页数")
    private Integer page;

    /**
     * 可选，默认50
     */
    @ApiModelProperty("每页数量")
    private Integer pagesize;
}
