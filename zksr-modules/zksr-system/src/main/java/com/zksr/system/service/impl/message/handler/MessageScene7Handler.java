package com.zksr.system.service.impl.message.handler;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.enums.CommonMessageSceneEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.system.api.commonMessage.dto.IFlowContext;
import com.zksr.system.api.commonMessage.dto.MessageTable;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.api.commonMessage.vo.SubscribeOrderReceiveVO;
import com.zksr.system.enums.MessageParamEum;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.impl.message.SubscribeMessageHandler;
import com.zksr.trade.api.after.dto.SupplierAfterDtlDTO;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.express.dto.OrderExpressResDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.TrdSupplierOrderDtlDTO;
import com.zksr.trade.api.order.vo.TrdOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *  全国, 本地, 货到付款, 订单收货
 */
@Slf4j
@Service
@SuppressWarnings("all")
public class MessageScene7Handler extends SubscribeMessageHandler<SubscribeOrderReceiveVO> {

    @Override
    public Integer scene() {
        return CommonMessageSceneEnum.ORDER_RECEIVE.getScene();
    }

    @Override
    protected void initFlowContext() {
        // 初始化本消息公用变量
        FlowContext context = new FlowContext();

        // 收集参数, 发送通知操作
        SubscribeOrderReceiveVO receiveVO = getEventData();
        // 入驻商订单
        List<TrdSupplierOrderDtlDTO> supplierOrderDtlDTOList = orderApi.getSupplierOrderDtlBatch(receiveVO.getSupplierOrderDtlIdList()).getCheckedData();

        TrdSupplierOrderDtlDTO orderDtlDTO = supplierOrderDtlDTOList.get(0);
        TrdSupplierOrder trdSupplierOrder = orderApi.getOrderBySupplierOrderId(orderDtlDTO.getSupplierOrderId()).getCheckedData();
        // 商品名称
        List<String> spuNames = supplierOrderDtlDTOList.stream().map(item -> item.getSpuName()).collect(Collectors.toList());
        BigDecimal totalAmt = supplierOrderDtlDTOList.stream().map(item -> item.getTotalAmt()).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 入驻商
        TrdOrder trdOrder = orderApi.getOrderByOrderId(trdSupplierOrder.getOrderId());
        // 用户门店信息
        MemberDTO memberDTO = memberApi.getMemBerByMemberId(trdOrder.getMemberId()).getCheckedData();
        BranchDTO branchDTO = sysCacheService.getBranchDTO(trdOrder.getBranchId());
        // 填充参数上下午支持模版自定义参数
        MessageTable messageTable = MessageTable.builder().build();
        messageTable.setOrderNo(trdOrder.getOrderNo())
                .setMemberName(memberDTO.getMemberName())
                .setMemberPhone(memberDTO.getMemberPhone())
                .setBranchName(memberDTO.getMemberPhone())
                .setBranchName(branchDTO.getBranchName())
                .setBranchAddr(branchDTO.getBranchAddr())
                .setSpuName(StringUtils.join(spuNames, StringPool.COMMA))
                .setPayAmt(totalAmt.toString())
                .setOrderCreateTime(DateUtil.formatDateTime(trdOrder.getCreateTime()))
                .put(MessageParamEum.ORDER_RECEIVE_TIME, DateUtil.formatDateTime(orderDtlDTO.getReceiveTime()))
                .put(MessageParamEum.ORDER_MEMO, trdOrder.getMemo())
        ;

        // 设置公共参数
        context.setTrdOrder(trdOrder);
        context.setBranch(branchDTO);
        context.setMember(memberDTO);
        context.setSupplierOrderDtlDTOList(supplierOrderDtlDTOList);
        // 设置到流程里面去
        messageContext().setFlowContext(context);
        messageContext().setMessageTable(messageTable);
    }

    @Override
    public List<CommonMessageDTO> memberHandler(MessageTemplateDTO messageTemplateDTO) {
        FlowContext flowContext = messageContext().getFlowContext(FlowContext.class);
        MessageTable messageTable = messageContext().getMessageTable();
        // 组装消息主体
        Map<String, String> body = messageTemplateDTO.buildSubscribeMessageDTO(messageTable);
        CommonMessageDTO messageDTO = CommonMessageDTO.builder()
                .merchantId(flowContext.getMember().getMemberId())
                .body(body)
                .build();
        return ListUtil.toList(messageDTO);
    }

    @Override
    public List<CommonMessageDTO> colonelHandler(MessageTemplateDTO messageTemplateDTO) {
        FlowContext flowContext = messageContext().getFlowContext(FlowContext.class);
        Long colonelId = flowContext.getBranch().getColonelId();
        // 获取业务员
        ColonelDTO colonel = sysCacheService.getColonel(colonelId);
        if (Objects.isNull(colonel)) {
            return ListUtil.empty();
        }
        // 组装消息主体
        CommonMessageDTO messageDTO = CommonMessageDTO.builder()
                .merchantId(colonel.getColonelId())
                .body(messageTemplateDTO.buildSubscribeMessageDTO(messageContext().getMessageTable()))
                .build();
        return ListUtil.toList(messageDTO);
    }

    @Override
    public List<CommonMessageDTO> supplierHandler(MessageTemplateDTO messageTemplateDTO) {
        FlowContext flowContext = messageContext().getFlowContext(FlowContext.class);
        List<TrdSupplierOrderDtlDTO> supplierOrderDtlDTOList = flowContext.getSupplierOrderDtlDTOList();
        ArrayList<CommonMessageDTO> messageDTOS = new ArrayList<>();
        // 拆分入驻商订单信息
        supplierOrderDtlDTOList.stream().collect(Collectors.groupingBy(TrdSupplierOrderDtlDTO::getSupplierOrderId)).forEach((supplierOrderId, supplierDtlList) -> {
            // 获取入驻商订单
            TrdSupplierOrder trdSupplierOrder = orderApi.getOrderBySupplierOrderId(supplierOrderId).getCheckedData();
            // 区分入驻商金额
            BigDecimal totalAmt = supplierDtlList.stream().map(TrdSupplierOrderDtlDTO::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            List<String> supplierSpuNames = supplierDtlList.stream().map(item -> StringUtils.format(item.getSpuName())).collect(Collectors.toList());
            // 复制参数容器
            MessageTable messageTable = ObjectUtil.cloneByStream(messageContext().getMessageTable());
            messageTable.setSpuName(StringUtils.join(supplierSpuNames, StringPool.COMMA));
            messageTable.setPayAmt(totalAmt.toString());
            // 组装消息主体
            messageDTOS.add(
                    CommonMessageDTO.builder()
                            .merchantId(trdSupplierOrder.getSupplierId())
                            .body(messageTemplateDTO.buildSubscribeMessageDTO(messageContext().getMessageTable()))
                            .build()
            );
        });
        return messageDTOS;
    }

    @Data
    @ApiModel(description = "消息内部容器")
    public static class FlowContext implements IFlowContext {

        @ApiModelProperty("交易订单")
        private TrdOrder trdOrder;

        @ApiModelProperty("门店")
        private BranchDTO branch;

        @ApiModelProperty("用户")
        private MemberDTO member;

        @ApiModelProperty("入驻商订单详情")
        private List<TrdSupplierOrderDtlDTO> supplierOrderDtlDTOList;
    }
}
