package com.zksr.system.api.supplierArea;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.supplierArea.dto.SupplierAreaDTO;
import com.zksr.system.api.supplierArea.dto.SupplierDzwlAreaDTO;
import com.zksr.system.domain.SysSupplier;
import com.zksr.system.service.ISysSupplierAreaService;
import com.zksr.system.service.ISysSupplierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class SupplierAreaApiImpl implements SupplierAreaApi {

    @Autowired
    private ISysSupplierAreaService sysSupplierAreaService;


    @Override
    public CommonResult<List<SupplierAreaDTO>> getSupplierAreaByAreaId(Long areaId) {
        return success(sysSupplierAreaService.getSupplierAreaByAreaId(areaId));
    }

    @Override
    public CommonResult<List<SupplierDzwlAreaDTO>> getDzwlSupplierInfoByAreaId(Long areaId) {
        return  success(sysSupplierAreaService.getDzwlSupplierInfoByAreaId(areaId));
    }
}
