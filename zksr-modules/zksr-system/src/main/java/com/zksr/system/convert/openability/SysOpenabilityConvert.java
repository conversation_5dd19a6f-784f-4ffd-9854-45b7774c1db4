package com.zksr.system.convert.openability;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysOpenability;
import com.zksr.system.controller.openability.vo.SysOpenabilityRespVO;
import com.zksr.system.controller.openability.vo.SysOpenabilitySaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 开放能力 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-27
*/
@Mapper
public interface SysOpenabilityConvert {

    SysOpenabilityConvert INSTANCE = Mappers.getMapper(SysOpenabilityConvert.class);

    SysOpenabilityRespVO convert(SysOpenability sysOpenability);

    SysOpenability convert(SysOpenabilitySaveReqVO sysOpenabilitySaveReq);

    PageResult<SysOpenabilityRespVO> convertPage(PageResult<SysOpenability> sysOpenabilityPage);

    List<SysOpenabilityRespVO> convertList(List<SysOpenability> sysOpenabilityList);
}
