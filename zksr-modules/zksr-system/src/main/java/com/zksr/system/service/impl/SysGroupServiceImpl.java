package com.zksr.system.service.impl;

import com.alicp.jetcache.Cache;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.system.api.group.dto.GroupDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.system.mapper.SysGroupMapper;
import com.zksr.system.domain.SysGroup;
import com.zksr.system.controller.group.vo.SysGroupPageReqVO;
import com.zksr.system.controller.group.vo.SysGroupSaveReqVO;
import com.zksr.system.service.ISysGroupService;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 平台商城市分组Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@Service
public class SysGroupServiceImpl implements ISysGroupService {
    @Autowired
    private SysGroupMapper sysGroupMapper;

    @Autowired
    private Cache<Long, GroupDTO> groupDTOCache;

    /**
     * 新增平台商城市分组
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @DistributedLock(lockName = RedisLockConstants.LOCK_CITY_GROUP)
    public Long insertSysGroup(SysGroupSaveReqVO createReqVO) {
        // 数据验证
        validateSysGroup(createReqVO);
        // 插入
        SysGroup sysGroup = HutoolBeanUtils.toBean(createReqVO, SysGroup.class);
        sysGroupMapper.insert(sysGroup);
        // 返回
        return sysGroup.getGroupId();
    }

    /**
     * 修改平台商城市分组
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysGroup(SysGroupSaveReqVO updateReqVO) {
        // 数据验证
        validateSysGroup(updateReqVO);
        sysGroupMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, SysGroup.class));
        groupDTOCache.remove(updateReqVO.getGroupId());
    }

    /**
     * 删除平台商城市分组
     *
     * @param groupId 平台商城市分组id
     */
    @Override
    public void deleteSysGroup(Long groupId) {
        // 删除
        sysGroupMapper.deleteById(groupId);
        groupDTOCache.remove(groupId);
    }

    /**
     * 批量删除平台商城市分组
     *
     * @param groupIds 需要删除的平台商城市分组主键
     * @return 结果
     */
    @Override
    public void deleteSysGroupByGroupIds(Long[] groupIds) {
        for(Long groupId : groupIds){
            this.deleteSysGroup(groupId);
        }
    }

    /**
     * 获得平台商城市分组
     *
     * @param groupId 平台商城市分组id
     * @return 平台商城市分组
     */
    @Override
    public SysGroup getSysGroup(Long groupId) {
        return sysGroupMapper.selectById(groupId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysGroup> getSysGroupPage(SysGroupPageReqVO pageReqVO) {
        return sysGroupMapper.selectPage(pageReqVO);
    }

    /**
    * @Description: 获取所有分组信息
    * @Author: liuxingyu
    * @Date: 2024/3/18 16:31
    */
    @Override
    public List<SysGroup> getGroupList() {
        return sysGroupMapper.selectList();
    }



    private void validateSysGroup(SysGroupSaveReqVO sysGroupSaveReqVO) {
        if (sysGroupMapper.selectExists(sysGroupSaveReqVO) > NumberPool.LONG_ZERO) {
            throw exception(SYS_GROUP_EXISTS);
        }
    }

}
