package com.zksr.system.convert.brand;

import com.zksr.common.core.domain.vo.openapi.receive.MemBranchRegisterReqDTO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.portal.api.dto.BranchRegisterReqDTO;
import com.zksr.system.domain.SysBrandMember;
import com.zksr.system.controller.brand.vo.SysBrandMemberRespVO;
import com.zksr.system.controller.brand.vo.SysBrandMemberSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 品牌商子账户 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-08-07
*/
@Mapper
public interface SysBrandMemberConvert {

    SysBrandMemberConvert INSTANCE = Mappers.getMapper(SysBrandMemberConvert.class);

    SysBrandMemberRespVO convert(SysBrandMember sysBrandMember);

    SysBrandMember convert(SysBrandMemberSaveReqVO sysBrandMemberSaveReq);

    PageResult<SysBrandMemberRespVO> convertPage(PageResult<SysBrandMember> sysBrandMemberPage);


    @Mappings({
            @Mapping(source = "registerReq.contactName", target = "memberName"),
            @Mapping(source = "registerReq.contactPhone", target = "userName")

    })
    BranchRegisterReqDTO convert2BranchRegisterReqDTO(MemBranchRegisterReqDTO registerReq);

}