package com.zksr.system.convert.printSettings;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysPrintSettings;
import com.zksr.system.controller.print.vo.SysPrintSettingsRespVO;
import com.zksr.system.controller.print.vo.SysPrintSettingsSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 打印设置 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-09-02
*/
@Mapper
public interface SysPrintSettingsConvert {

    SysPrintSettingsConvert INSTANCE = Mappers.getMapper(SysPrintSettingsConvert.class);

    SysPrintSettingsRespVO convert(SysPrintSettings sysPrintSettings);

    SysPrintSettings convert(SysPrintSettingsSaveReqVO sysPrintSettingsSaveReq);

    PageResult<SysPrintSettingsRespVO> convertPage(PageResult<SysPrintSettings> sysPrintSettingsPage);

    List<SysPrintSettingsRespVO> convertRespListVO(List<SysPrintSettings> list);
}