package com.zksr.system.service.impl;

import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.system.mapper.SysBankChannelMapper;
import com.zksr.system.convert.bank.SysBankChannelConvert;
import com.zksr.system.domain.SysBankChannel;
import com.zksr.system.api.bank.vo.SysBankChannelPageReqVO;
import com.zksr.system.controller.bank.vo.SysBankChannelSaveReqVO;
import com.zksr.system.service.ISysBankChannelService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 联行号信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@Service
public class SysBankChannelServiceImpl implements ISysBankChannelService {
    @Autowired
    private SysBankChannelMapper sysBankChannelMapper;

    /**
     * 新增联行号信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysBankChannel(SysBankChannelSaveReqVO createReqVO) {
        // 插入
        SysBankChannel sysBankChannel = SysBankChannelConvert.INSTANCE.convert(createReqVO);
        sysBankChannelMapper.insert(sysBankChannel);
        // 返回
        return sysBankChannel.getBankChannelId();
    }

    /**
     * 修改联行号信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysBankChannel(SysBankChannelSaveReqVO updateReqVO) {
        sysBankChannelMapper.updateById(SysBankChannelConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除联行号信息
     *
     * @param bankChannelId id
     */
    @Override
    public void deleteSysBankChannel(Long bankChannelId) {
        // 删除
        sysBankChannelMapper.deleteById(bankChannelId);
    }

    /**
     * 批量删除联行号信息
     *
     * @param bankChannelIds 需要删除的联行号信息主键
     * @return 结果
     */
    @Override
    public void deleteSysBankChannelByBankChannelIds(Long[] bankChannelIds) {
        for(Long bankChannelId : bankChannelIds){
            this.deleteSysBankChannel(bankChannelId);
        }
    }

    /**
     * 获得联行号信息
     *
     * @param bankChannelId id
     * @return 联行号信息
     */
    @Override
    public SysBankChannel getSysBankChannel(Long bankChannelId) {
        return sysBankChannelMapper.selectById(bankChannelId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysBankChannel> getSysBankChannelPage(SysBankChannelPageReqVO pageReqVO) {
        return sysBankChannelMapper.selectPage(pageReqVO);
    }

    @Override
    public SysBankChannel getChannelByNo(String bankChannelNo) {
        return sysBankChannelMapper.selectByChannelNo(bankChannelNo);
    }

}
