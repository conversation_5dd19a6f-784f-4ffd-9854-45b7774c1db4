package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 用户头列配置对象 sys_user_column
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@TableName(value = "sys_user_column")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysUserColumn extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 用户表头列配置id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long userColumnId;

    /** 平台商id */
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 表code */
    private String tableCode;

    /** 列key */
    private String filedKey;

    /** 名称 */
    private String filedName;

    /** 显示 */
    private Integer visibleFlag;

    /** 是否导出显示列 */
    private Integer exportVisibelFlag;

    /** 固定 */
    private Integer fixedFlag;

    /** 排序 */
    private Integer sort;

    /**
     * 对齐方式 （left，center，right）
     */
    private String align;

    /**
     * 列宽度
     */
    private Integer width;

    /**
     * 列最小宽度
     */
    private Integer minWidth;

    /**
     * 是否使用插槽
     */
    private String useSlot;

    /**
     * 是否隐藏多行
     */
    private String overflow;

}
