package com.zksr.system.controller.pageConfig;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigCopyReqVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigPageReqVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigRespVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigSaveReqVO;
import com.zksr.system.domain.SysPagesConfig;
import com.zksr.system.service.ISysPagesConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台页面配置Controller
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@Api(tags = "管理后台 - 平台页面配置接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/pageConfig")
public class SysPagesConfigController {
    @Autowired
    private ISysPagesConfigService sysPagesConfigService;

    /**
     * 新增平台页面配置
     */
    @ApiOperation(value = "新增平台页面配置", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "平台页面配置", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysPagesConfigSaveReqVO createReqVO) {
        Long pageId = sysPagesConfigService.insertSysPagesConfig(createReqVO);
        sysPagesConfigService.reloadCache(pageId);
        return success(pageId);
    }

    /**
     * 修改平台页面配置
     */
    @ApiOperation(value = "修改平台页面配置", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "平台页面配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysPagesConfigSaveReqVO updateReqVO) {
        sysPagesConfigService.updateSysPagesConfig(updateReqVO);
        sysPagesConfigService.reloadCache(updateReqVO.getPageId());
        return success(true);
    }

    /**
     * 删除平台页面配置
     */
    @ApiOperation(value = "删除平台页面配置", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "平台页面配置", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{pageIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] pageIds) {
        sysPagesConfigService.deleteSysPagesConfigByPageIds(pageIds);
        return success(true);
    }

    /**
     * 获取平台页面配置详细信息
     */
    @ApiOperation(value = "获得平台页面配置详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{pageId}")
    public CommonResult<SysPagesConfigRespVO> getInfo(@PathVariable("pageId") Long pageId) {
        SysPagesConfig sysPagesConfig = sysPagesConfigService.getSysPagesConfig(pageId);
        return success(HutoolBeanUtils.toBean(sysPagesConfig, SysPagesConfigRespVO.class));
    }

    /**
     * 分页查询平台页面配置
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台页面配置分页列表", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(dcAlias = "spc")
    public CommonResult<PageResult<SysPagesConfigRespVO>> getPage(@Valid SysPagesConfigPageReqVO pageReqVO) {
        return success(sysPagesConfigService.getSysPagesConfigPageExt(pageReqVO));
    }


    /**
     * 修改平台页面配置启用状态
     */
    @ApiOperation(value = "修改平台页面配置启用状态", httpMethod = HttpMethod.PUT, notes = "[pageId,status] 必须传入 权限字符:" + Permissions.UPDATE_STATUS)
    @RequiresPermissions(Permissions.UPDATE_STATUS)
    @Log(title = "修改平台页面配置启用状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public CommonResult<Boolean> changeStatus(@Valid @RequestBody SysPagesConfigSaveReqVO updateReqVO) {
        sysPagesConfigService.updateStatus(updateReqVO);
        sysPagesConfigService.reloadCache(updateReqVO.getPageId());
        return success(true);
    }

    /**
     * 修改平台页面配置是否默认
     */
    @ApiOperation(value = "修改平台页面配置是否默认", httpMethod = HttpMethod.PUT, notes = "[pageId,defFlag] 必须传入 权限字符:" + Permissions.UPDATE_DEFAULT)
    @RequiresPermissions(Permissions.UPDATE_DEFAULT)
    @Log(title = "修改平台页面配置是否默认", businessType = BusinessType.UPDATE)
    @PutMapping("/changeDefFlag")
    public CommonResult<Boolean> changeDefFlag(@Valid @RequestBody SysPagesConfigSaveReqVO updateReqVO) {
        // 设置默认
        sysPagesConfigService.updateDefFlag(updateReqVO);
        sysPagesConfigService.reloadCache(updateReqVO.getPageId());
        return success(true);
    }

    /**
     * 复制平台页面配置
     */
    @ApiOperation(value = "复制平台页面配置", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.COPY)
    @RequiresPermissions(Permissions.COPY)
    @Log(title = "复制平台页面配置", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public CommonResult<Boolean> copy(@Valid @RequestBody SysPagesConfigCopyReqVO configCopyReqVO) {
        sysPagesConfigService.copy(configCopyReqVO);
        return success(true);
    }


    /*@InnerAuth
    @ApiOperation(value = "根据城市渠道获取默认首页配置", httpMethod = "GET")
    @GetMapping("/getDefByAreaChannel")
    public CommonResult<SysPagesConfigVo> getDefByAreaChannel(Long areaId, Long channelId) {
        return CommonResult.success(sysPagesConfigService.getDefByAreaChannel(areaId, channelId));
    }*/


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:pageConfig:add";
        /** 编辑 */
        public static final String EDIT = "system:pageConfig:edit";
        /** 删除 */
        public static final String DELETE = "system:pageConfig:remove";
        /** 列表 */
        public static final String LIST = "system:pageConfig:list";
        /** 查询 */
        public static final String GET = "system:pageConfig:query";
        /** 修改状态 */
        public static final String UPDATE_STATUS = "system:pageConfig:changeStatus";
        /** 修改默认 */
        public static final String UPDATE_DEFAULT = "system:pageConfig:changeDefFlag";
        /** 复制模版 */
        public static final String COPY = "system:pageConfig:copy";
    }
}
