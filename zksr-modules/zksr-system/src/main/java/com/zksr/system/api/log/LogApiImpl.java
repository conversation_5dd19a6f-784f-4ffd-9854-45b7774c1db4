package com.zksr.system.api.log;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.domain.vo.openapi.SysInterfaceLogVO;
import com.zksr.system.api.EmailMessage.dto.SyncEmailReportExcel;
import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;
import com.zksr.system.service.ISysInterfaceLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/31 17:39
 * @注释
 */
@RestController
@ApiIgnore
public class LogApiImpl implements LogApi{

    @Autowired
    private ISysInterfaceLogService sysInterfaceLogServiceImpl;
    @Override
    public CommonResult<Boolean> insertSysInterfaceLog(SysInterfaceLogVO createReqVO) {
        sysInterfaceLogServiceImpl.insertSysInterfaceLog(HutoolBeanUtils.toBean(createReqVO, SysInterfaceLogSaveReqVO.class));
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> updateSysInterfaceLog(@Valid SysInterfaceLogVO updateReqVO) {
        sysInterfaceLogServiceImpl.updateSysInterfaceLog(HutoolBeanUtils.toBean(updateReqVO, SysInterfaceLogSaveReqVO.class));
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<SysInterfaceLogVO> getSysInterfaceLogByReqId(String reqId) {
        return CommonResult.success(HutoolBeanUtils.toBean(sysInterfaceLogServiceImpl.getSysInterfaceLogByReqId(reqId),SysInterfaceLogVO.class));
    }

    @Override
    public CommonResult<Boolean> updateLogByResult(@Valid SysInterfaceLogVO updateReqVO) {
        sysInterfaceLogServiceImpl.updateLogByResult(HutoolBeanUtils.toBean(updateReqVO, SysInterfaceLogSaveReqVO.class));
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<SyncEmailReportExcel>> getLogByDaySyncReport(Long sysCode, String date) {
        return CommonResult.success(sysInterfaceLogServiceImpl.getLogByDaySyncReport(sysCode, date));
    }
}
