package com.zksr.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.export.vo.SysExportJob;
import com.zksr.system.api.export.vo.SysExportJobPageReqVO;
import com.zksr.system.api.export.vo.SysExportJobRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/1/15
 * @desc
 */
public interface ISysExportJobService extends IService<SysExportJob> {

    /**
     * 获取等待执行任务列表
     * @return
     */
    List<SysExportJob> waitJobList();

    /**
     * 创建导出任务
     * @param wmsReportJob
     */
    void createdJob(SysExportJob wmsReportJob);

    /**
     * 更新导出任务
     * @param wmsReportJob
     */
    void updateJob(SysExportJob wmsReportJob);

    /**
     * 分页查询导出任务
     * @param pageReqVO
     * @return
     */
    PageResult<SysExportJobRespVO> jobPageList(SysExportJobPageReqVO pageReqVO);
}
