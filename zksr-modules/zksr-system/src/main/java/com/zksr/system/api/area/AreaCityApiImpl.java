package com.zksr.system.api.area;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.area.vo.SysAreaCityPageReqVO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.area.vo.SysAreaCitySaveReqVO;
import com.zksr.system.convert.area.SysAreaCityConvert;
import com.zksr.system.domain.SysAreaCity;
import com.zksr.system.service.ISysAreaCityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 省市区API
 * @date 2024/7/30 17:22
 */
@RestController
@ApiIgnore
@InnerAuth
public class AreaCityApiImpl implements AreaCityApi{

    @Autowired
    private ISysAreaCityService sysAreaCityService;

    @Override
    public CommonResult<PageResult<SysAreaCityRespVO>> getPage(SysAreaCityPageReqVO pageReqVO) {
        PageResult<SysAreaCity> pageResult = sysAreaCityService.getSysAreaCityPage(pageReqVO);
        return success(SysAreaCityConvert.INSTANCE.convertPage(pageResult));
    }

    @Override
    public CommonResult<SysAreaCityRespVO> getById(Long areaCityId) {
        return success(SysAreaCityConvert.INSTANCE.convert(sysAreaCityService.getSysAreaCity(areaCityId)));
    }

    @Override
    public CommonResult<SysAreaCityRespVO> getByNameAndParent(String name, Long pid, Integer deep) {
        return success(SysAreaCityConvert.INSTANCE.convert(sysAreaCityService.getByNameAndParent(name,pid,deep)));
    }

    @Override
    public CommonResult<Boolean> updateBatch(List<SysAreaCitySaveReqVO> updateReqVOList) {
        sysAreaCityService.updateBatch(updateReqVOList);
        return success(true);
    }

    @Override
    public CommonResult<Long> insert(SysAreaCitySaveReqVO insertVO) {
        Long id = sysAreaCityService.insert(insertVO);
        return success(id);
    }
}
