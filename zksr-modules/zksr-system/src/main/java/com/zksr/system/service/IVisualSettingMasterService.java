package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterPageReqVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterRespVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterSaveReqVO;
import com.zksr.system.domain.VisualSettingMaster;

import javax.validation.Valid;
import java.util.List;

/**
 * 可视化配置主Service接口
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
public interface IVisualSettingMasterService {

    /**
     * 新增可视化配置主
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertVisualSettingMaster(@Valid VisualSettingMasterSaveReqVO createReqVO);

    /**
     * 修改可视化配置主
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateVisualSettingMaster(@Valid VisualSettingMasterSaveReqVO updateReqVO);

    /**
     * 删除可视化配置主
     *
     * @param visualMasterId id
     */
    public void deleteVisualSettingMaster(Long visualMasterId);

    /**
     * 批量删除可视化配置主
     *
     * @param visualMasterIds 需要删除的可视化配置主主键集合
     * @return 结果
     */
    public void deleteVisualSettingMasterByVisualMasterIds(Long[] visualMasterIds);

    /**
     * 获得可视化配置主
     *
     * @param visualMasterId id
     * @return 可视化配置主
     */
    public VisualSettingMaster getVisualSettingMaster(Long visualMasterId);

    /**
     * 获得可视化配置主分页
     *
     * @param pageReqVO 分页查询
     * @return 可视化配置主分页
     */
    PageResult<VisualSettingMaster> getVisualSettingMasterPage(VisualSettingMasterPageReqVO pageReqVO);

    List<VisualSettingMasterRespVO> list(VisualSettingMasterPageReqVO pageReqVO);

}
