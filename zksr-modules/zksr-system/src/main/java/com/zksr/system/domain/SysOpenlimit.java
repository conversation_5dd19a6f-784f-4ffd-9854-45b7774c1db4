package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 开发能力qps对象 sys_openlimit
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
@TableName(value = "sys_openlimit")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysOpenlimit extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 开放资源id */
    @Excel(name = "开放资源id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long opensourceId;

    /** 开放能力id */
    @Excel(name = "开放能力id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long openabilityId;

    /** qps限制（每秒多少次请求） */
    @Excel(name = "qps限制", readConverterExp = "每=秒多少次请求")
    private Integer rateLimit;

}
