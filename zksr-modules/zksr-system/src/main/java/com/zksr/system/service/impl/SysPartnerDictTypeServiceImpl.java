package com.zksr.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.enums.PartnerDictTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.domain.SysDictType;
import com.zksr.system.api.domain.SysPartnerDictData;
import com.zksr.system.api.domain.SysPartnerDictType;
import com.zksr.system.domain.SysPartner;
import com.zksr.system.mapper.SysDictDataMapper;
import com.zksr.system.mapper.SysDictTypeMapper;
import com.zksr.system.mapper.SysPartnerDictDataMapper;
import com.zksr.system.mapper.SysPartnerDictTypeMapper;
import com.zksr.system.service.ISysDictTypeService;
import com.zksr.system.service.ISysPartnerDictDataService;
import com.zksr.system.service.ISysPartnerDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 字典 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysPartnerDictTypeServiceImpl implements ISysPartnerDictTypeService
{
    @Autowired
    private SysPartnerDictTypeMapper partnerDictTypeMapper;

    @Autowired
    private ISysPartnerDictDataService partnerDictDataService;

    @Autowired
    private SysDictTypeServiceImpl dictTypeService;
    @Resource
    private SysPartnerServiceImpl partnerService;


    /**
     * 查询字典类型列表
     * @param dictType
     * @return
     */
    public List<SysPartnerDictType> selectDictTypeList(SysPartnerDictType dictType){
        List<SysPartnerDictType> sysPartnerDictTypes = selectDictTypeAll();

        if (StringUtils.isEmpty(sysPartnerDictTypes)){
            for (PartnerDictTypeEnum value : PartnerDictTypeEnum.values()) {
                partnerDictTypeMapper.insert(initInsertData(value));
            }
        }
        LambdaQueryWrapper<SysPartnerDictType> queryWrapper = new LambdaQueryWrapper<>();
        Long sysCode = SecurityContextHolder.getSysCode();
        queryWrapper.eq(SysPartnerDictType::getSysCode,sysCode);
        queryWrapper.like(StringUtils.isNotEmpty(dictType.getDictName()),SysPartnerDictType::getDictName,dictType.getDictName());
        queryWrapper.eq(StringUtils.isNotEmpty(dictType.getStatus()),SysPartnerDictType::getStatus,dictType.getStatus());
        queryWrapper.eq(StringUtils.isNotEmpty(dictType.getDictType()),SysPartnerDictType::getDictType,dictType.getDictType());

        return partnerDictTypeMapper.selectList(queryWrapper);

    }

    /**
     * 根据所有字典类型
     * @return
     */
    public List<SysPartnerDictType> selectDictTypeAll()
    {
        LambdaQueryWrapper<SysPartnerDictType> queryWrapper = new LambdaQueryWrapper<>();
        Long sysCode = SecurityContextHolder.getSysCode();
        queryWrapper.eq(SysPartnerDictType::getSysCode,sysCode);
        return partnerDictTypeMapper.selectList(queryWrapper);
    }


    /**
     * 根据字典类型查询字典数据
     * @param dictType
     * @return
     */
    public List<SysPartnerDictData> selectDictDataByType(String dictType,String sysSource){
        Long sysCode = SecurityContextHolder.getSysCode();
        if (StringUtils.isNotEmpty(sysSource)){
            SysPartner sysPartnerBySource = partnerService.getSysPartnerBySource(sysSource);
            if (ObjectUtil.isNotNull(sysPartnerBySource)){
                sysCode = sysPartnerBySource.getSysCode();
            }

        }
        List<SysPartnerDictData> dictDatas = DictUtils.getPartnerDictCache(dictType,sysCode);
        if (StringUtils.isNotEmpty(dictDatas))
        {
            return dictDatas;
        }
        List<SysPartnerDictData> sysPartnerDictData = partnerDictDataService.selectDictDataByType(dictType,sysCode);
        if (StringUtils.isNotEmpty(sysPartnerDictData))
        {
            DictUtils.setPartnerDictCache(dictType, sysPartnerDictData,sysCode);
            return sysPartnerDictData;
        }
        List<SysDictData> sysDictData = dictTypeService.selectDictDataByType(dictType);
        if (StringUtils.isNotEmpty(sysDictData)){
            List<SysPartnerDictData> sysPartnerDictData1 = JSON.parseArray(JSON.toJSONString(sysDictData), SysPartnerDictData.class);
            return sysPartnerDictData1;
        }

        return null;
    }

    public SysPartnerDictType selectDictTypeById(Long dictId)
    {
        return partnerDictTypeMapper.selectOne(new LambdaQueryWrapper<SysPartnerDictType>().eq(SysPartnerDictType::getDictId,dictId));
    }

    /**
     * 根据dictType查询
     * @param dictType
     * @return
     */
    public SysPartnerDictType selectDictTypeByType(String dictType,Long dictId){
        LambdaQueryWrapper<SysPartnerDictType> queryWrapper = new LambdaQueryWrapper<>();
        Long sysCode = SecurityContextHolder.getSysCode();
        queryWrapper.eq(SysPartnerDictType::getSysCode,sysCode);
        queryWrapper.eq(SysPartnerDictType::getDictType,dictType);
        queryWrapper.ne(ObjectUtil.isNotNull(dictId),SysPartnerDictType::getDictId,dictId);
        return partnerDictTypeMapper.selectOne(queryWrapper);
    }

    /**
     * 删除
     * @param dictIds
     */
    public void deleteDictTypeByIds(Long[] dictIds){
        for (Long dictId : dictIds)
        {
            SysPartnerDictType dictType = selectDictTypeById(dictId);
            List<SysPartnerDictData> sysPartnerDictData = partnerDictDataService.selectDictDataByType(dictType.getDictType());
            if (StringUtils.isNotEmpty(sysPartnerDictData)){
                throw new ServiceException(String.format("%1$s已分配,不能删除", dictType.getDictName()));
            }

            partnerDictTypeMapper.delete(new LambdaQueryWrapper<SysPartnerDictType>().eq(SysPartnerDictType::getDictId,dictId));
            DictUtils.removeDictCache(dictType.getDictType());
        }
    }

    /**
     * 新增
     * @param dict
     * @return
     */
    public int insertDictType(SysPartnerDictType dict)
    {
        dict.setCreateTime(new Date());
        SysPartnerDictType sysPartnerDictType = selectDictTypeByType(dict.getDictType(),null);
        if (ObjectUtil.isNotNull(sysPartnerDictType)){
            throw new RuntimeException("该字典类型已存在");
        }
        Long sysCode = SecurityContextHolder.getSysCode();
        dict.setSysCode(sysCode);
        int row = partnerDictTypeMapper.insert(dict);
        if (row > 0)
        {
            DictUtils.setPartnerDictCache(dict.getDictType(), null,null);
        }
        return row;
    }

    public int updateDictType(SysPartnerDictType dict)
    {
        dict.setUpdateTime(new Date());
        SysPartnerDictType sysPartnerDictType = selectDictTypeByType(dict.getDictType(),dict.getDictId());
        if (ObjectUtil.isNotNull(sysPartnerDictType)){
            throw new RuntimeException("该字典类型已存在");
        }
        int row = partnerDictTypeMapper.update(dict,new LambdaQueryWrapper<SysPartnerDictType>().eq(SysPartnerDictType::getDictId,dict.getDictId()));
        if (row > 0)
        {
            List<SysPartnerDictData> dictDatas = partnerDictDataService.selectDictDataByType(dict.getDictType());
            DictUtils.setPartnerDictCache(dict.getDictType(), dictDatas,null);
        }
        return row;
    }

    @PostConstruct
    public void init()
    {
        loadingDictCache();
    }

    public void loadingDictCache(){
        List<SysPartnerDictType> sysPartnerDictTypes = selectDictTypeAll();
//
//        if (StringUtils.isEmpty(sysPartnerDictTypes)){
//            for (PartnerDictTypeEnum value : PartnerDictTypeEnum.values()) {
//                partnerDictTypeMapper.insert(initInsertData(value));
//            }
//        }
        for (SysPartnerDictType sysPartnerDictType : sysPartnerDictTypes) {
            if ("0".equals(sysPartnerDictType.getStatus())){
                List<SysPartnerDictData> sysPartnerDictData = selectDictDataByType(sysPartnerDictType.getDictType(),null);
                DictUtils.setPartnerDictCache(sysPartnerDictType.getDictType(),sysPartnerDictData,null);
            }

        }
    }

    /**
     * 初始化新增数据组装
     * @param dictType
     * @return
     */
    public SysPartnerDictType initInsertData(PartnerDictTypeEnum dictType){
        SysPartnerDictType sysPartnerDictType = new SysPartnerDictType();
        Long sysCode = SecurityContextHolder.getSysCode();
        sysPartnerDictType.setSysCode(sysCode);
        sysPartnerDictType.setDictType(dictType.getType());
        sysPartnerDictType.setDictName(dictType.getName());
        sysPartnerDictType.setStatus("0");
        sysPartnerDictType.setCreateTime(new Date());
        sysPartnerDictType.setCreateBy(SecurityUtils.getUsername());
        return sysPartnerDictType;
    }


    /**
     * 清空字典缓存数据
     */
    public void clearDictCache()
    {
        List<SysPartnerDictType> sysPartnerDictTypes = selectDictTypeAll();
        RedisService redisService = SpringUtils.getBean(RedisService.class);
        for (SysPartnerDictType sysPartnerDictType : sysPartnerDictTypes) {
            redisService.deleteObject(DictUtils.getPartnerCacheKey(sysPartnerDictType.getDictType(),null));
        }

    }


    /**
     * 重置字典缓存数据
     */
    public void resetDictCache()
    {
        clearDictCache();
        loadingDictCache();
    }

}
