package com.zksr.system.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysSoftware;
import com.zksr.system.controller.software.vo.SysSoftwarePageReqVO;


/**
 * 软件商信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Mapper
public interface SysSoftwareMapper extends BaseMapperX<SysSoftware> {
    default PageResult<SysSoftware> selectPage(SysSoftwarePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysSoftware>()
                    .eqIfPresent(SysSoftware::getSoftwareId, reqVO.getSoftwareId())
                    .likeIfPresent(SysSoftware::getSoftwareName, reqVO.getSoftwareName())
                    .likeIfPresent(SysSoftware::getContactName, reqVO.getContactName())
                    .eqIfPresent(SysSoftware::getContactPhone, reqVO.getContactPhone())
                    .eqIfPresent(SysSoftware::getContactAddress, reqVO.getContactAddress())
                    .eqIfPresent(SysSoftware::getSoftwareUserId, reqVO.getSoftwareUserId())
                    .eqIfPresent(SysSoftware::getSoftwareRate, reqVO.getSoftwareRate())
                .orderByDesc(SysSoftware::getSoftwareId));
    }

    default SysSoftware getBySysUserId(Long sysUserId){
        return selectOne(SysSoftware::getSoftwareUserId, sysUserId);
    }

}
