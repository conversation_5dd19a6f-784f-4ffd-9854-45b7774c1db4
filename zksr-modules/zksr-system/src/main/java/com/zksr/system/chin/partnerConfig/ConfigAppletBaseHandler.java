package com.zksr.system.chin.partnerConfig;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.chin.general.PartnerConfigUtil;
import com.zksr.system.controller.partnerConfig.vo.SysPartnerConfigRespVO;
import com.zksr.system.controller.partnerConfig.vo.SysPartnerConfigSaveReqVO;
import com.zksr.system.domain.SysPartnerConfig;
import com.zksr.system.mapper.SysPartnerConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class ConfigAppletBaseHandler extends ConfigPartnerChin {

    @Autowired
    private SysPartnerConfigMapper sysPartnerConfigMapper;

    @Autowired
    private Cache<Long, AppletBaseConfigDTO> appletBaseCache;


    private static final String key = "appletBaseConfig.";

    @Override
    public void saveConfig(SysPartnerConfigSaveReqVO sysPartnerConfigSaveReqVO, Long sysCode) {
        if (ObjectUtil.isNotNull(sysPartnerConfigSaveReqVO.getAppletBaseConfigDto())) {
            //获取参数数据集合
            List<SysPartnerConfig> sysPartnerConfigs =
                    PartnerConfigUtil.jointConfigParam(sysCode, key, sysPartnerConfigSaveReqVO.getAppletBaseConfigDto());
            //根据平台ID 获取所有配置信息
            List<SysPartnerConfig> configList = sysPartnerConfigMapper.selectBySysCode(sysCode);
            //需要修改的数据
            List<SysPartnerConfig> updateList = sysPartnerConfigs
                    .stream()
                    .filter(a -> configList.stream().anyMatch(b -> ObjectUtil.equal(a.getConfigKey(), b.getConfigKey())))
                    .map(a -> {
                        for (SysPartnerConfig sysPartnerConfig : configList) {
                            if (ObjectUtil.equal(sysPartnerConfig.getConfigKey(), a.getConfigKey())) {
                                a.setPartnerConfigId(sysPartnerConfig.getPartnerConfigId());
                                return a;
                            }
                        }
                        return a;
                    })
                    .collect(Collectors.toList());
            //需要新增的数据
            List<SysPartnerConfig> insertList = sysPartnerConfigs
                    .stream()
                    .filter(a -> configList.stream().noneMatch(b -> ObjectUtil.equal(a.getConfigKey(), b.getConfigKey())))
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(updateList)) {
                sysPartnerConfigMapper.updateBatch(updateList);
            }
            if (ObjectUtil.isNotEmpty(insertList)) {
                sysPartnerConfigMapper.insertBatch(insertList);
            }
            //删除缓存
            appletBaseCache.remove(sysCode);
        }
        if (ObjectUtil.isNotNull(next)) {
            next.saveConfig(sysPartnerConfigSaveReqVO, sysCode);
        }
    }

    @Override
    public SysPartnerConfigRespVO getConfig(List<SysPartnerConfig> configList, SysPartnerConfigRespVO sysPartnerConfigRespVO, Long sysCode) {
        if (ObjectUtil.isNull(sysPartnerConfigRespVO.getAppletBaseConfigDto())){
            List<SysPartnerConfig> appletBaseList = PartnerConfigUtil.matchingConfigKey(configList, key);
            if (ObjectUtil.isNotEmpty(appletBaseList)) {
                AppletBaseConfigDTO appletBaseConfigDto = PartnerConfigUtil.encapsulationConfigObj(appletBaseList, key, AppletBaseConfigDTO.class);
                sysPartnerConfigRespVO.setAppletBaseConfigDto(appletBaseConfigDto);
                //更新缓存
                appletBaseCache.put(sysCode, appletBaseConfigDto);
            }
        }
        if (ObjectUtil.isNotNull(next)) {
            next.getConfig(configList, sysPartnerConfigRespVO, sysCode);
        }
        return sysPartnerConfigRespVO;
    }

}
