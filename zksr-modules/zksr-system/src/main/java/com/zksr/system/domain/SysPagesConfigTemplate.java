package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 平台页面配置模版对象 sys_pages_config_template
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@TableName(value = "sys_pages_config_template")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysPagesConfigTemplate extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 自定义页面ID */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pageTemplateId;

    /** 启用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "启用时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date enableTime;

    /** 页面名称 */
    @Excel(name = "页面名称")
    private String pageName;

    /** 平台商code */
    @Excel(name = "页面类型;页面类型,index-首页")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 页面类型;页面类型,index-首页 */
    @Excel(name = "页面类型;页面类型,index-首页")
    private String pageType;

    /** 页面配置JSON */
    @Excel(name = "页面配置JSON")
    private String pageConfig;

    /** 状态 (0正常 1停用) */
    @Excel(name = "状态 (0正常 1停用)")
    private String status;

    /** 删除状态 (0正常 2已删除) */
    private String delFlag;

    /** 父级页面 */
    @Excel(name = "父级页面")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pid;

    /** 1-一级页面, 2-二级页面 */
    @Excel(name = "1-一级页面, 2-二级页面")
    private Integer level;

    /** 0-没有子页面,1-有子页面 */
    @Excel(name = "0-没有子页面,1-有子页面")
    private Integer hasChild;

}
