package com.zksr.system.visualSyn.factory;

import com.zksr.common.core.exception.ServiceException;
import com.zksr.system.visualSyn.service.ISyncDataNewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
*  可视化程序工厂
* @date 2024/10/24 10:05
* <AUTHOR>
*/
@Component("syncDataNewFactory")
public class SyncDataNewFactory {
    @Autowired
    private List<ISyncDataNewService> serviceList;

    public ISyncDataNewService getSyncService(String name){
        for(ISyncDataNewService service : serviceList){
            if(name.equals(service.getName())){
                return service;
            }
        }
        throw new ServiceException("可视化程序工厂未匹配到该对接/系统类型:" + name);
    }


}
