package com.zksr.system.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.enums.SmsSceneEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.system.api.partnerConfig.dto.SmsConfigDTO;
import com.zksr.system.api.sms.dto.SmsCodeReqDTO;
import com.zksr.system.api.sms.dto.SmsCodeRespDTO;
import com.zksr.system.api.sms.dto.SmsCodeValidDTO;
import com.zksr.system.api.sms.dto.SmsCodeValidRespDTO;
import com.zksr.system.controller.sms.vo.SysSmsPageReqVO;
import com.zksr.system.domain.SysSms;
import com.zksr.system.domain.SysSmsTemplate;
import com.zksr.system.mapper.SysSmsMapper;
import com.zksr.system.mq.SystemMqProducer;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysSmsChannelService;
import com.zksr.system.service.ISysSmsService;
import com.zksr.system.service.ISysSmsTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 短信消息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
@Service
@Slf4j
public class SysSmsServiceImpl implements ISysSmsService {

    @Autowired
    private SysSmsMapper sysSmsMapper;

    @Autowired
    private ISysSmsTemplateService smsTemplateService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SystemMqProducer systemMqProducer;

    @Autowired
    private List<ISysSmsChannelService> sysSmsChannelServices;

    @Autowired
    private ISysCacheService sysCacheService;

    /**
    * 查询分页数据
    * @param pageReqVO  请求参数
    * @return   分页数据
    */
    @Override
    public PageResult<SysSms> getSysSmsPage(SysSmsPageReqVO pageReqVO) {
        return sysSmsMapper.selectPage(pageReqVO);
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_MOBILE_SMS, condition = "#smsCodeReqDTO.createIp")
    public SmsCodeRespDTO sendSmsCode(SmsCodeReqDTO smsCodeReqDTO) {
        log.info("发送标准验证码短信消息 data={}", JSON.toJSONString(smsCodeReqDTO));
        // 验证模版
        SysSmsTemplate template = validateTemplate(smsCodeReqDTO.getSysCode(), SmsSceneEnum.COMMON_CODE.getScene());
        // 生成验证码
        String code = RandomUtil.randomNumbers(6);
        // 验证是否已经达到了限制条件
        validateLimit(template, smsCodeReqDTO.getPhone(), smsCodeReqDTO.getCreateIp());
        // 发送验证码
        SysSms sms = this.insertSms(
                template.getSmsTemplateId(),
                smsCodeReqDTO.getSysCode(),
                smsCodeReqDTO.getPhone(),
                smsCodeReqDTO.getCreateIp(),
                MapUtil.of("code", code),
                SmsSceneEnum.COMMON_CODE.getScene()
        );
        // 保存验证码5分钟有效
        redisService.setCacheObject(StringUtils.format("{}:{}", RedisConstants.SMS_CODE, sms.getMobile()), code, 60 * 5L, TimeUnit.SECONDS);
        // 异步处理消息发送
        systemMqProducer.sendSms(sms);
        return SmsCodeRespDTO.success(code);
    }

    @Override
    public SmsCodeValidRespDTO validateSmsCode(SmsCodeValidDTO codeReqDTO) {
        // 保存验证码5分钟有效
        String codeKey = StringUtils.format("{}:{}", RedisConstants.SMS_CODE, codeReqDTO.getPhone());
        String codeLimitKey = StringUtils.format("{}:{}", RedisConstants.SMS_CODE_LIMIT, codeReqDTO.getPhone());
        String code = redisService.getCacheObject(codeKey);
        Integer codeLimit = redisService.getCacheObject(codeLimitKey);
        if (Objects.isNull(codeLimit)){
            codeLimit = NumberPool.INT_ZERO;
        }
        // 验证次数超过3次
        if (codeLimit >= NumberPool.INT_THREE) {
            return SmsCodeValidRespDTO.fail(SMS_CODE_VALID_LIMIT.getMsg());
        }
        // 验证码不存在
        if (StringUtils.isEmpty(code)) {
            return SmsCodeValidRespDTO.fail(SMS_CODE_NONE_EXITS.getMsg());
        }
        // 验证码错误
        if (!code.equals(codeReqDTO.getCode())) {
            redisService.setCacheObject(codeLimitKey, codeLimit + NumberPool.INT_ONE, redisService.getExpire(codeKey), TimeUnit.SECONDS);
            return SmsCodeValidRespDTO.fail(SMS_CODE_ERR_OR_NONE_EXITS.getMsg());
        }
        // 删除验证拦截
        List<String> limitKeyList = ListUtil.toList(
                StringUtils.format("{}:{}", RedisConstants.SMS_LIMIT, codeReqDTO.getPhone()),
                StringUtils.format("{}:{}", RedisConstants.SMS_LIMIT, codeReqDTO.getCreateIp()),
                codeLimitKey
        );
        limitKeyList.forEach(item -> redisService.deleteObject(item));
        return SmsCodeValidRespDTO.success();
    }

    /**
     * 发送单条短信消息给用户
     * @param sms 短信消息
     * @return
     */
    @Override
    public Long sendSingleSms(SysSms sms) {
        SysSmsTemplate smsTemplate = smsTemplateService.getSysSmsTemplate(sms.getTemplateId());
        ISysSmsChannelService smsChannelService = getChannel(smsTemplate);
        smsChannelService.sendSms(sms, smsTemplate);
        return sms.getSmsId();
    }

    /**
     * 获取短信通道
     * @param sysSmsTemplate
     * @return
     */
    private ISysSmsChannelService getChannel(SysSmsTemplate sysSmsTemplate) {
        for (ISysSmsChannelService channelService : sysSmsChannelServices) {
            if (channelService.channel().equals(sysSmsTemplate.getPlatform())) {
                return channelService;
            }
        }
        throw exception(SMS_TEMPLATE_NONE_EXITS);
    }

    /**
     * 验证平台有没有配置短信模版消息
     * @param sysCode   平台商ID
     * @param scene     场景
     * @return  短信模版
     */
    private SysSmsTemplate validateTemplate(Long sysCode, Integer scene) {
        SmsConfigDTO smsConfig = sysCacheService.getSmsConfig(sysCode);
        if (Objects.isNull(smsConfig)) {
            throw exception(SMS_CONFIG_NOT_EXITS);
        }
        SysSmsTemplate template = smsTemplateService.getSysSmsTemplate(sysCode, scene, smsConfig.getSmsPlatform());
        if (Objects.isNull(template)) {
            throw exception(SMS_TEMPLATE_NONE_EXITS);
        }
        return template;
    }

    /**
     * 验证限流
     * @param template  消息模版
     * @param phone     手机号
     * @param createIp  请求ID
     */
    private void validateLimit(SysSmsTemplate template, String phone, String createIp) {
        // 半小时内允许请求次数
        Integer halfhourCount = template.getHalfhourCount();
        if (Objects.isNull(halfhourCount)) {
            // 无限制
            return;
        }
        List<String> limitKeyList = ListUtil.toList(
                StringUtils.format("{}:{}", RedisConstants.SMS_LIMIT, phone),
                StringUtils.format("{}:{}", RedisConstants.SMS_LIMIT, createIp)
        );
        for (String limitKey : limitKeyList) {
            // 使用redis对 phone 或者 createIp 限流
            Long count = redisService.incrByCacheObject(limitKey);
            if (count > halfhourCount) {
                // 请求过于频繁，请稍后再试
                throw exception(SMS_LIMIT);
            }
            // 设置过去时间
            redisService.expire(limitKey, 30 * 60, TimeUnit.SECONDS);
        }
    }

    /**
     * 新增短信消息
     * @param templateId    短信模版ID
     * @param sysCode       平台编号
     * @param mobile        手机号
     * @param remoteId      请求ID
     * @param params        模版参数
     * @param scene         场景 {@link SmsSceneEnum}
     * @return 短信消息
     */
    private SysSms insertSms(Long templateId, Long sysCode, String mobile, String remoteId, Object params, Integer scene) {
        SysSms sms = SysSms.builder()
                .templateId(templateId)
                .templateParams(JSON.toJSONString(params))
                .sysCode(sysCode)
                .mobile(mobile)
                .remoteIp(remoteId)
                .scene(scene)
                .build();
        sysSmsMapper.insert(sms);
        return sms;
    }
}
