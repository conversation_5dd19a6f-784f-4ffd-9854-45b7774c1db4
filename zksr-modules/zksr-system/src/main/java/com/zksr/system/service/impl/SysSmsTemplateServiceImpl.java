package com.zksr.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.controller.sms.vo.SysSmsTemplatePageReqVO;
import com.zksr.system.controller.sms.vo.SysSmsTemplateSaveReqVO;
import com.zksr.system.convert.template.SysSmsTemplateConvert;
import com.zksr.system.domain.SysSmsTemplate;
import com.zksr.system.mapper.SysSmsTemplateMapper;
import com.zksr.system.service.ISysSmsTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 短信模版配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
@Service
public class SysSmsTemplateServiceImpl implements ISysSmsTemplateService {
    @Autowired
    private SysSmsTemplateMapper sysSmsTemplateMapper;

    /**
     * 新增短信模版配置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysSmsTemplate(SysSmsTemplateSaveReqVO createReqVO) {
        // 插入
        SysSmsTemplate sysSmsTemplate = SysSmsTemplateConvert.INSTANCE.convert(createReqVO);
        sysSmsTemplateMapper.insert(sysSmsTemplate);
        // 返回
        return sysSmsTemplate.getSmsTemplateId();
    }

    /**
     * 修改短信模版配置
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysSmsTemplate(SysSmsTemplateSaveReqVO updateReqVO) {
        sysSmsTemplateMapper.updateById(SysSmsTemplateConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除短信模版配置
     *
     * @param smsTemplateId 
     */
    @Override
    public void deleteSysSmsTemplate(Long smsTemplateId) {
        // 删除
        sysSmsTemplateMapper.deleteById(smsTemplateId);
    }

    /**
     * 批量删除短信模版配置
     *
     * @param smsTemplateIds 需要删除的短信模版配置主键
     * @return 结果
     */
    @Override
    public void deleteSysSmsTemplateBySmsTemplateIds(Long[] smsTemplateIds) {
        for(Long smsTemplateId : smsTemplateIds){
            this.deleteSysSmsTemplate(smsTemplateId);
        }
    }

    /**
     * 获得短信模版配置
     *
     * @param smsTemplateId 
     * @return 短信模版配置
     */
    @Override
    public SysSmsTemplate getSysSmsTemplate(Long smsTemplateId) {
        return sysSmsTemplateMapper.selectById(smsTemplateId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysSmsTemplate> getSysSmsTemplatePage(SysSmsTemplatePageReqVO pageReqVO) {
        return sysSmsTemplateMapper.selectPage(pageReqVO);
    }

    @Override
    public SysSmsTemplate getSysSmsTemplate(Long sysCode, Integer scene, String platform) {
        return sysSmsTemplateMapper.selectTemplate(sysCode, scene, platform);
    }

    @Override
    public void disable(Long smsTemplateId) {
        SysSmsTemplate sysSmsTemplate = sysSmsTemplateMapper.selectById(smsTemplateId);
        sysSmsTemplate.setStatus(NumberPool.INT_ONE);
        sysSmsTemplateMapper.updateById(sysSmsTemplate);
    }

    @Override
    @Transactional
    public void enable(Long smsTemplateId) {
        SysSmsTemplate sysSmsTemplate = sysSmsTemplateMapper.selectById(smsTemplateId);
        // 停用其他的
        sysSmsTemplateMapper.disableSceneAndPlatform(sysSmsTemplate);
        // 启用当前的
        sysSmsTemplate.setStatus(NumberPool.INT_ZERO);
        sysSmsTemplateMapper.updateById(sysSmsTemplate);
    }
}
