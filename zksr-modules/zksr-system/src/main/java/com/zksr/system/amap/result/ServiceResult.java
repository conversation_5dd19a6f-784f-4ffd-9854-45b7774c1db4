package com.zksr.system.amap.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @Description: 猎鹰服务返回实体类
* @Author: liuxingyu
* @Date: 2024/3/27 16:10
*/
@Data
public class ServiceResult {

    @ApiModelProperty("结果状态码")
    private Long errcode;

    @ApiModelProperty("返回结果状态码描述")
    private String errmsg;

    @ApiModelProperty("错误细节")
    private String errdetail;

    @ApiModelProperty("结果对象")
    private Data data;

    @lombok.Data
    public static class Data {
        @ApiModelProperty("服务的唯一ID")
        private Long sid;

        @ApiModelProperty("服务名称")
        private String name;
    }
}
