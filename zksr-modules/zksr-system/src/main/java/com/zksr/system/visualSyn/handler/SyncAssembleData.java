package com.zksr.system.visualSyn.handler;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.zksr.account.api.account.AccountFlowApi;
import com.zksr.account.api.pay.PayFlowApi;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.vo.openapi.*;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.enums.request.VisualReceiptType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dictData.DictDataApi;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.service.ISysCacheService;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.api.hdfk.HdfkApi;
import com.zksr.trade.api.hdfk.dto.HdfkPayDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.OrderReceiptRespDTO;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.supplierAfter.SupplierAfterApi;
import com.zksr.trade.api.supplierOrder.SupplierOrderApi;
import com.zksr.trade.api.supplierOrder.dto.OrderDiscountDtlDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.OpenApiConstants.OPEN_FLAG_0;
import static com.zksr.common.core.constant.OpenApiConstants.OPEN_FLAG_1;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;
import static com.zksr.system.enums.ErrorCodeConstants.SYNC_ASSEMBLE_B2B_DATA_ERR;

/**
*  对外系统数据同步 -- 组装B2B内部数据
* @date 2024/10/24 15:46
* <AUTHOR>
*/
@Slf4j
@Component("syncAssembleData")
public class SyncAssembleData {
    @Resource
    private BranchApi branchApi;
    @Resource
    private ISysCacheService sysCacheService;
    @Resource
    private SupplierOrderApi supplierOrderApi;

    @Resource
    private OrderApi orderApi;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private SupplierAfterApi supplierAfterApi;

    @Resource
    private AfterApi afterApi;

    @Resource
    private HdfkApi hdfkApi;

    @Resource
    private PayFlowApi payFlowApi;

    @Autowired
    private AccountFlowApi accountFlowApi;


    /**
     * 组装门店信息
     * @param data
     * @param opensourceDto
     * @return
     */
    public BranchOpenDTO assembleBranch(SyncDataDTO data,OpensourceDto opensourceDto){

        try{
            // 获取门店信息
            BranchOpenDTO branchDTO = HutoolBeanUtils.toBean(branchApi.getByBranchId(Long.valueOf(data.getDataId())).getCheckedData(),BranchOpenDTO.class);
            if (Objects.isNull(branchDTO)) {
                log.info("同步门店信息失败，未查询到对应的门店信息data:{}", data);
                throw new ServiceException(StringUtils.format("同步门店信息失败，未查询到对应的门店信息data:{}", data));

            }
            //运营商ID
            SupplierDTO supplierDTO = sysCacheService.getSupplierDTO(data.getSupplierId());
            if(ToolUtil.isNotEmpty(supplierDTO)){
                branchDTO.setDcId(supplierDTO.getDcId());
            }
            //操作类型
            if(ToolUtil.isNotEmpty(data.getOperationTypeCode())){
                branchDTO.setOperationTypeCode(data.getOperationTypeCode());
            }
            //配送中心编号
            if(ToolUtil.isNotEmpty(opensourceDto.getSendCode())){
                branchDTO.setSendCode(opensourceDto.getSendCode());
            }
            //业务员信息
            if(ToolUtil.isNotEmpty(branchDTO.getColonelId())){
                ColonelDTO colonel = sysCacheService.getColonel(branchDTO.getColonelId());
                branchDTO.setColonelName(colonel.getColonelName());
                branchDTO.setColonelPhone(colonel.getColonelPhone());
            }
            //渠道信息
            if(ToolUtil.isNotEmpty(branchDTO.getChannelId())){
                ChannelDTO channel = sysCacheService.getChannelDto(branchDTO.getChannelId());
                branchDTO.setChannelName(channel.getChannelName());
            }
            //城市区域
            if(ToolUtil.isNotEmpty(branchDTO.getAreaId())){
                AreaDTO area = sysCacheService.getAreaDTO(branchDTO.getAreaId());
                branchDTO.setAreaName(area.getAreaName());
            }

            return branchDTO;
        }catch (Exception e){
            log.error(" 对外推送数据--组装B2B门店数据失败，失败原因，", e);
            throw exception(SYNC_ASSEMBLE_B2B_DATA_ERR,e.getMessage());
        }
    }


    /**
     * 组装销售订单信息
     * @param data
     * @param opensourceDto
     * @return
     */
    public OrderOpenDTO assembleOrder(SyncDataDTO data,OpensourceDto opensourceDto) {

        try {
        //获取入驻商订单信息
        OrderOpenDTO orderOpenDTO = supplierOrderApi.getSupplierOrderBySupplierOrderNo(data.getDataId()).getCheckedData();
        if(Objects.isNull(orderOpenDTO)){
            log.info("同步入驻商销售订单信息失败，未查询到对应的订单主表信息data:{}",data);
            throw new ServiceException(StringUtils.format("同步入驻商销售订单信息失败，未查询到对应的订单主表信息data:{}",data));
        }
        //获取订单信息
        TrdOrder trdOrder = orderApi.getOrderByOrderNo(orderOpenDTO.getOrderNo()).getCheckedData();
        //获取入驻商订单详情信息
        List<OrderDetailOpenDTO> detailList = supplierOrderApi.getSupplierOrderDtlBySupplierOrderNo(orderOpenDTO.getSupplierOrderNo()).getCheckedData();

        //校验该入驻商订单是否已取消
        boolean cancelFlag = detailList.stream().allMatch(x -> DeliveryStatusEnum.CANCEL.getCode().equals(x.getDeliveryState()));

        if(cancelFlag){
            throw new ServiceException(StringUtils.format("同步入驻商销售订单信息失败，该订单的订单详情已全部取消，不推送！data:{}",data));
        }

        //筛选掉数量为0 或已取消的订单
        detailList = detailList.stream().filter(x -> !DeliveryStatusEnum.CANCEL.getCode().equals(x.getDeliveryState()) && (x.getTotalNum().subtract(x.getCancelQty()).compareTo(BigDecimal.ZERO) > 0)).collect(Collectors.toList());

        //获取订单优惠信息 trd_order_discount_dtl
        Map<Long,List<OrderDiscountDtlDTO>> discountDtlMap = new HashMap<>();

        List<OrderDiscountDtlDTO> discountDtlList = supplierOrderApi.getOrderDiscountDtlListBySupplierOrderId(orderOpenDTO.getSupplierOrderId()).getCheckedData();

        if(ToolUtil.isNotEmpty(discountDtlList) && !discountDtlList.isEmpty()){
            discountDtlMap = discountDtlList.stream().filter(x->ToolUtil.isNotEmpty(x.getSupplierOrderDtlId())).collect(Collectors.groupingBy(OrderDiscountDtlDTO::getSupplierOrderDtlId));
        }

        //组装订单详情的商品数据
        List<OrderDetailOpenDTO> assembleDetailList = new ArrayList<>();
/*        //订单详情组装格式：订单同步是否合单（0否，1是）
        if(SysYesNoEnum.YES.getCode().equals(opensourceDto.getOrderMergeFlag())){
            assembleDetailList = assembleOrderDetailMerge(detailList,discountDtlMap);
        }else{
            assembleDetailList = assembleOrderDetail(detailList,discountDtlMap);
        }*/

        assembleDetailList = assembleOrderDetail(detailList,discountDtlMap);

        //组装订单数据
        orderOpenDTO.setDetailList(assembleDetailList);
        //设置门店信息
        BranchDTO branchDTO = sysCacheService.getBranchDTO(trdOrder.getBranchId());
        //设置门店地址
        orderOpenDTO.setBranchAddr(branchDTO.getBranchAddr());
        //联系电话
        orderOpenDTO.setContactPhone(branchDTO.getContactPhone());
        //联系人
        orderOpenDTO.setContactName(branchDTO.getContactName());
        //业务员
        orderOpenDTO.setColonelId(trdOrder.getColonelId());

        //业务员
        if (ObjectUtils.isNotEmpty(orderOpenDTO.getColonelId())) {
            ColonelDTO colonelDTO = sysCacheService.getColonel(orderOpenDTO.getColonelId());
            if (ObjectUtils.isNotEmpty(colonelDTO)) {
                orderOpenDTO.setColonelName(colonelDTO.getColonelName());
            }
        }

        orderOpenDTO.setBranchNo(Objects.isNull(branchDTO.getBranchNo()) ? orderOpenDTO.getBranchId().toString() : branchDTO.getBranchNo());
        orderOpenDTO.setBranchName(branchDTO.getBranchName());
        orderOpenDTO.setContactPhone(branchDTO.getContactPhone());
        orderOpenDTO.setProvinceName(branchDTO.getProvinceName());
        orderOpenDTO.setCityName(branchDTO.getCityName());
        orderOpenDTO.setDistrictName(branchDTO.getDistrictName());
        orderOpenDTO.setBranchAddr(branchDTO.getBranchAddr());
        orderOpenDTO.setBranchId(trdOrder.getBranchId());
        if(Objects.nonNull(trdOrder.getColonelId())){
            //设置业务员信息
            orderOpenDTO.setColonelId(trdOrder.getColonelId());
        }

        //组装支付方式
        orderOpenDTO.setPayWay(trdOrder.getPayWay());

        //组装下单方式（下单用户是否本身是业务员(0-否（默认）  1-是)）
        orderOpenDTO.setColonelFlag(trdOrder.getColonelFlag());

        //组装唯一标识数据
        orderOpenDTO.setSendCode(opensourceDto.getSendCode());

        //设置订单支付状态  是否支付成功
        orderOpenDTO.setPayState(trdOrder.getPayState());

        //需要计算的订单销售参数数据
        orderOpenDTO.setRealSaleAmt(orderOpenDTO.getSubPayAmt().subtract(orderOpenDTO.getSubCancelAmt()));
        orderOpenDTO.setRealOrderAmt(orderOpenDTO.getSubOrderAmt().subtract(orderOpenDTO.getSubCancelAmt()));
        orderOpenDTO.setRealSumUnitQty(detailList.stream().map(OrderDetailOpenDTO::getRealUnitQty).reduce(BigDecimal.ZERO,BigDecimal::add));

        return orderOpenDTO;

        }catch (Exception e){
            log.error(" 对外推送数据--组装B2B订单数据失败，失败原因：", e);
            throw exception(SYNC_ASSEMBLE_B2B_DATA_ERR,e.getMessage());
        }
    }

    /**
     * 组装售后订单信息
     * @param data
     * @param opensourceDto
     * @return
     */
    public AfterPushDTO assembleAfter(SyncDataDTO data,OpensourceDto opensourceDto) {

        try {
        //获取入驻商售后订单信息
        AfterPushDTO afterOpenDto = supplierAfterApi.getSupAfterBySupAfterNo(data.getDataId()).getCheckedData();
        if(Objects.isNull(afterOpenDto)){
            log.info("同步入驻商售后订单信息失败，未查询到对应的订单主表信息data:{}",data);
            throw new ServiceException(StringUtils.format("同步入驻商售后订单信息失败，未查询到对应的订单主表信息data:{}",data));
        }

        //获取售后订单信息
        TrdAfter trdAfter = afterApi.getAfterByAfterNo(afterOpenDto.getAfterNo()).getCheckedData();
        //获取入驻商售后订单详情信息
        List<AfterDetailPushDTO> detailList = supplierAfterApi.getSupplierAfterListBySupplierAfterNo(afterOpenDto.getSupplierAfterNo()).getCheckedData();

        //获取入驻商售后单对应的销售单详情
        List<OrderDetailOpenDTO> orderDetailOpenDTOList = supplierOrderApi.getSupplierOrderDtlBySupplierOrderNo(afterOpenDto.getSupplierOrderNo()).getCheckedData();

        //获取商品详情的原销售订单信息
        Map<Long, OrderDetailOpenDTO> orderDetailOpenDTOMap = orderDetailOpenDTOList.stream().collect(Collectors.toMap(OrderDetailOpenDTO::getSupplierOrderDtlId, x -> x));

        //获取退款类型/售后类型 数据字典
        List<SysDictData> afterTypeDictList = dictDataApi.selectDictDataList(new SysDictData("sys_after_type"));

        //退款类型/售后类型 数据字典
        Map<String, String> afterTypeMap = afterTypeDictList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

            //组装订单详情的商品数据
            List<AfterDetailPushDTO> assembleDetailList = new ArrayList<>();
/*            //订单详情组装格式：订单同步是否合单（0否，1是）
            if(SysYesNoEnum.YES.getCode().equals(opensourceDto.getOrderMergeFlag())){
                assembleDetailList = assembleAfterDetailMerge(detailList,trdAfter,orderDetailOpenDTOMap,afterTypeMap,data);
            }else{
                assembleDetailList = assembleAfterDetail(detailList,trdAfter,orderDetailOpenDTOMap,afterTypeMap,data);
            }*/

        assembleDetailList = assembleAfterDetail(detailList,trdAfter,orderDetailOpenDTOMap,afterTypeMap,data);

        //组装售后订单数据
        afterOpenDto.setDetailList(assembleDetailList);
        //设置门店信息
        BranchDTO branchDTO = sysCacheService.getBranchDTO(trdAfter.getBranchId());
        afterOpenDto.setBranchNo(Objects.isNull(branchDTO.getBranchNo()) ? afterOpenDto.getBranchId().toString() : branchDTO.getBranchNo());
        afterOpenDto.setBranchId(afterOpenDto.getBranchId());
        afterOpenDto.setBranchName(branchDTO.getBranchName());
        afterOpenDto.setBranchAddr(branchDTO.getBranchAddr());
        afterOpenDto.setContactPhone(branchDTO.getContactPhone());


        //组装支付方式
        afterOpenDto.setPayWay(trdAfter.getPayWay());

        //组装唯一标识数据
        afterOpenDto.setSendCode(opensourceDto.getSendCode());

        //设置该售后单对应入驻商订单对应第三方原订单单号
        TrdSupplierOrder supplierOrder = orderApi.selectOrderBySupplierOrderNo(afterOpenDto.getSupplierOrderNo());
        afterOpenDto.setSupplierSourceNo(supplierOrder.getSourceOrderNo());

        //设置订单的创建时间
        afterOpenDto.setCreateTimeString(DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS,afterOpenDto.getCreateTime()));

        //设置备注  目前取售后订单的备注信息
        afterOpenDto.setMemo(trdAfter.getReason());

        //需要计算的订单销售参数数据
        BigDecimal afterDiscountAmt = detailList.stream().map(x -> x.getReturnCouponDiscountAmt().add(x.getReturnCouponDiscountAmt2()).add(x.getReturnActivityDiscountAmt())).reduce(BigDecimal.ZERO, BigDecimal::add);
        afterOpenDto.setAfterDiscountAmt(afterDiscountAmt);
        //售后订单退货数量总和
        afterOpenDto.setReturnSumUnitQty(detailList.stream().map(AfterDetailPushDTO::getReturnQty).reduce(BigDecimal.ZERO,BigDecimal::add));

        return afterOpenDto;

        }catch (Exception e){
            log.error(" 对外推送数据--组装B2B售后数据失败，失败原因：", e);
            throw exception(SYNC_ASSEMBLE_B2B_DATA_ERR,e.getMessage());
        }
    }


    /**
     * 组装收款单信息
     * @param data
     * @param opensourceDto
     * @return
     */
    public ReceiptOpenDTO assembleReceipt(SyncDataDTO data,OpensourceDto opensourceDto) {

        try{
        //入驻商订单号
        String supplierSheetNo = data.getDataId();

        //订单类型
        String sheetType = data.getSheetType();

        //支付流水相关订单号
        String paySheetNo = null;

        //获取收款单信息
        List<OrderReceiptRespDTO> receiptRespDTOList = orderApi.getOrderReceiptInfoBySupplierOrderNo(new SyncReceiptSendDTO(sheetType, supplierSheetNo)).getCheckedData();

        if(ToolUtil.isEmpty(receiptRespDTOList) || receiptRespDTOList.isEmpty()){
            log.info("同步入驻商收款单信息失败，未查询到对应的收款单信息信息data:{}",data);
            throw new ServiceException(StringUtils.format("同步入驻商收款单信息失败，未查询到对应的收款单信息信息data:{}",data));
        }

        //收款单信息
        ReceiptOpenDTO receiptOpenDTO = new ReceiptOpenDTO();
        //收款单详情信息
        List<ReceiptDetailOpenDTO> detailList = new ArrayList<>();

        //收款总金额
        BigDecimal totalReceiptAmt = BigDecimal.ZERO;

        //收款精确总金额
        BigDecimal totalReceiptExactAmt = BigDecimal.ZERO;

        //获取订单信息
        if(SheetTypeConstants.XSS.equals(sheetType)){
            //获取入驻商销售订单信息
            OrderOpenDTO orderOpenDTO = supplierOrderApi.getSupplierOrderBySupplierOrderNo(supplierSheetNo).getCheckedData();
            //获取销售订单信息
            TrdOrder order = orderApi.getOrderByOrderId(orderOpenDTO.getOrderId());

            //设置门店ID
            receiptOpenDTO.setBranchId(order.getBranchId());

            //设置业务员ID
            receiptOpenDTO.setColonelId(order.getColonelId());

            //设置支付流水需要的订单号
            paySheetNo = order.getOrderNo();
        }else{
            //获取入驻商售后订单信息
            AfterPushDTO afterPushDTO = supplierAfterApi.getSupAfterBySupAfterNo(supplierSheetNo).getCheckedData();
            //获取售后订单信息
            TrdAfter trdAfter = afterApi.getAfterById(afterPushDTO.getAfterId()).getCheckedData();
            //设置门店ID
            receiptOpenDTO.setBranchId(trdAfter.getBranchId());

            //设置业务员ID
            receiptOpenDTO.setColonelId(trdAfter.getColonelId());

            //设置支付流水需要的订单号
            paySheetNo = trdAfter.getAfterNo();
        }

        //组装收款单详情信息
        for (OrderReceiptRespDTO receiptRespDTO : receiptRespDTOList) {

            //订单号
            String sheetNo = receiptRespDTO.getSupplierSheetNo();

            ReceiptDetailOpenDTO detail = new ReceiptDetailOpenDTO();
            detail.setSheetType(receiptRespDTO.getSupplierSheetType())
                    .setReceiptAmt(receiptRespDTO.getReceiptAmt())
                    .setReceiptExactAmt(receiptRespDTO.getReceiptExactAmt())
                    .setPayWay(receiptRespDTO.getSheetPayWay());

            //获取收款单对应订单信息
            //第三方原单据号 入驻商销售/售后订单对应的原订单号  拒收退货/差异退货对应的入驻商销售订单原订单号
            //兼容订单编号
            if(Objects.equals(receiptRespDTO.getSupplierSheetType(),SheetTypeConstants.SHJ) ||
                    Objects.equals(receiptRespDTO.getSupplierSheetType(),SheetTypeConstants.SHC) ||
                    Objects.equals(receiptRespDTO.getSupplierSheetType(),SheetTypeConstants.SHS) ||
                    Objects.equals(receiptRespDTO.getSupplierSheetType(),"SHJ") ||
                    Objects.equals(receiptRespDTO.getSupplierSheetType(),"SHC") ||
                    Objects.equals(receiptRespDTO.getSupplierSheetType(),"SHS") ){
                //获取入驻商拒收/差异订单信息
                AfterPushDTO afterPushDTO = supplierAfterApi.getSupAfterBySupAfterNo(sheetNo).getCheckedData();
                //获取对应的销售订单信息
                OrderOpenDTO orderOpenDTO = supplierOrderApi.getSupplierOrderBySupplierOrderNo(afterPushDTO.getSupplierOrderNo()).getCheckedData();

                //设置详情的订单信息
                detail.setSupplierAfterNo(afterPushDTO.getSupplierAfterNo())
                        .setSourceAfterNo(afterPushDTO.getSourceOrderNo())
                        .setSupplierOrderNo(orderOpenDTO.getSupplierOrderNo())
                        .setSourceOrderNo(orderOpenDTO.getSourceOrderNo())
                        .setSheetDate(afterPushDTO.getCreateTime())
                        .setSheetDateString(DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS,afterPushDTO.getCreateTime()));

                //如果是未付款的售后订单
                if((Objects.equals(sheetType,SheetTypeConstants.SHS) || Objects.equals(sheetType,"SHS"))
                        && OPEN_FLAG_0.equals(data.getIsProceeds())){
                    detail.setIsProceeds(data.getIsProceeds());
                }

            } else if (SheetTypeConstants.XSS.equals(receiptRespDTO.getSupplierSheetType())){
                //获取对应的销售订单信息
                OrderOpenDTO orderOpenDTO = supplierOrderApi.getSupplierOrderBySupplierOrderNo(sheetNo).getCheckedData();

                //设置详情的订单信息
                detail.setSourceOrderNo(orderOpenDTO.getSourceOrderNo())
                        .setSupplierOrderNo(orderOpenDTO.getSupplierOrderNo())
                        .setSheetDate(orderOpenDTO.getCreateTime())
                        .setSheetDateString(DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS,orderOpenDTO.getCreateTime()));

            }
            //总收款金额汇总
            totalReceiptAmt = totalReceiptAmt.add(receiptRespDTO.getReceiptAmt());

            //精确总收款金额汇总
            totalReceiptExactAmt = totalReceiptExactAmt.add(receiptRespDTO.getReceiptExactAmt());

            //获取支付信息 如果是已经收款/退款的订单信息
            if(OPEN_FLAG_1.equals(data.getIsProceeds())){
                getReceiptPayInfo(detail,paySheetNo,data.getReceilptType());
            }


            detailList.add(detail);
        }

        //组装主表数据
        //获取门店信息
        BranchDTO branchDTO = sysCacheService.getBranchDTO(receiptOpenDTO.getBranchId());
        receiptOpenDTO.setBranchName(branchDTO.getBranchName())
                .setBranchNo(branchDTO.getBranchNo())
                .setTotalReceiptAmt(totalReceiptAmt)
                .setTotalReceiptExactAmt(totalReceiptExactAmt)
                .setSendCode(opensourceDto.getSendCode())
                .setDetailList(detailList);


        return receiptOpenDTO;

        }catch (Exception e){
            log.error(" 对外推送数据--组装B2B收款单数据失败，失败原因：", e);
            throw exception(SYNC_ASSEMBLE_B2B_DATA_ERR,e.getMessage());
        }
    }

    /**
     * 组装门店储值提现、充值信息
     * @param data
     * @param opensourceDto
     * @return
     */
    public BranchValueInfoOpenDTO assembleBranchValueInfo(SyncDataDTO data,OpensourceDto opensourceDto) {
        try{
            //账户资金流水ID集合
            List<Long> accountFlowIds = JSONArray.parseArray(data.getJson(), Long.class);

            if(ToolUtil.isEmpty(accountFlowIds)){
                log.info("同步入驻商门店储值信息失败，未获取到对应的账户资金流水ID集合信息:{}",data);
                throw new ServiceException(StringUtils.format("同步门店储值信息失败，组装门店储值提现、充值信息，未获取到对应的账户资金流水ID集合信息:{}",data));
            }

            //获取门店储值信息
            BranchValueInfoOpenDTO branchValueInfoOpenDTO = accountFlowApi.getBranchValueInfoByIds(accountFlowIds).getCheckedData();

            if(ToolUtil.isEmpty(branchValueInfoOpenDTO)){
                log.info("同步入驻商门店储值信息失败，未获取到对应的门店储值充值/提现信息:{}",data);
                throw new ServiceException(StringUtils.format("同步入驻商门店储值信息失败，未获取到对应的门店储值充值/提现信息:{}",data));
            }

            //组装配置信息
            branchValueInfoOpenDTO.setBranchNo(sysCacheService.getBranchDTO(branchValueInfoOpenDTO.getBranchId()).getBranchNo());
            branchValueInfoOpenDTO.setSendCode(opensourceDto.getSendCode());

            return  branchValueInfoOpenDTO;
        }catch (Exception e){
            log.error(" 对外推送数据--组装门店储值提现、充值信息数据失败，失败原因：", e);
            throw exception(SYNC_ASSEMBLE_B2B_DATA_ERR,e.getMessage());
        }
    }


    /**
     * 获取收款单支付信息
     * @param detail
     * @return
     */
    private void getReceiptPayInfo(ReceiptDetailOpenDTO detail,String paySheetNo,String receilptType){
        AccPayReceiptReqDTO reqDTO = new AccPayReceiptReqDTO();

        //如果是线上支付的销售订单 直接取销售订单号
        if(VisualReceiptType.ZXZF_ORDER_XSS_1.getType().equals(receilptType) || VisualReceiptType.ZXZF_ORDER_XSS_JR1.getType().equals(receilptType)){
            reqDTO.setSheetType(SheetTypeConstants.XSS);
            reqDTO.setSheetNo(paySheetNo);
            reqDTO.setPayWay(OrderPayWayEnum.ONLINE.getPayWay());
        }else if(VisualReceiptType.HDFK_ORDER_XSS_1.getType().equals(receilptType) || VisualReceiptType.HDFK_ORDER_XSS_JR1.getType().equals(receilptType)){
            //货到付款的订单 取货到付款单号
            // 根据订单ID查询入驻商订单明细
            List<OrderDetailOpenDTO> orderDetailOpenDTOList = supplierOrderApi.getSupplierOrderDtlBySupplierOrderNo(detail.getSupplierOrderNo()).getCheckedData();

            List<Long> hdfkIdList = orderDetailOpenDTOList.stream().map(OrderDetailOpenDTO::getHdfkPayId).filter(ToolUtil::isNotEmpty).collect(Collectors.toList());


            List<HdfkPayDTO> hdfkPayList = hdfkApi.getHdfkPayListByIds(hdfkIdList).getCheckedData();

            reqDTO.setSheetType(SheetTypeConstants.XSS);
            reqDTO.setPayWay(OrderPayWayEnum.HDFK.getPayWay());
            reqDTO.setHdfkNoList(hdfkPayList.stream().map(HdfkPayDTO::getHdfkPayNo).collect(Collectors.toList()));
        }else{
            //售后单都是取售后退款单号
            List<AfterDetailPushDTO> afterDetailPushDTOList = supplierAfterApi.getSupplierAfterListBySupplierAfterNo(detail.getSupplierAfterNo()).getCheckedData();
            reqDTO.setHdfkNoList(afterDetailPushDTOList.stream().map(AfterDetailPushDTO::getRefundNo).collect(Collectors.toList()));
            reqDTO.setSheetType(SheetTypeConstants.SHS);
        }

        //校验 如果订单号、货到付款订单集合都为空 则不查询支付流水信息
        if(ToolUtil.isEmpty(reqDTO.getSheetNo()) && ToolUtil.isEmpty(reqDTO.getHdfkNoList())){
            log.info("该收款单：{}未查询到对应订单/售后单的支付相关单号",detail);
            return;
        }

        //获取支付流水信息
        detail.setPayOpenList(payFlowApi.getOrderPayInfoListBySyncOrder(reqDTO).getCheckedData());
    }


    /**
     * 组装订单详情数据
     * @param detailList 订单详情数据
     * @param discountDtlMap 优惠券信息
     */
    private List<OrderDetailOpenDTO> assembleOrderDetail(List<OrderDetailOpenDTO> detailList,Map<Long,List<OrderDiscountDtlDTO>> discountDtlMap){
        for (OrderDetailOpenDTO detail : detailList) {
            //获取商品Sku信息
            SkuDTO skuDTO = sysCacheService.getSkuDTO(detail.getSkuId());
            detail.setItemSourceNo(skuDTO.getSourceNo());

            //条码
            String itemBarcode = null;
            //设置大中小信息
            if(UnitTypeEnum.S(detail.getOrderUnitType())){
                itemBarcode = skuDTO.getBarcode();
            }else if(UnitTypeEnum.M(detail.getOrderUnitType())){
                itemBarcode = skuDTO.getMidBarcode();
            }else if(UnitTypeEnum.L(detail.getOrderUnitType())){
                itemBarcode = skuDTO.getLargeBarcode();
            }
            detail.setItemBarcode(itemBarcode);

            //设置优惠信息
            List<OrderDiscountDtlDTO> discountDtls = discountDtlMap.get(detail.getSupplierOrderDtlId());
            //将优惠类型放入备注中
            if(ToolUtil.isNotEmpty(discountDtls) && !discountDtls.isEmpty()){
                String discountName = discountDtls.stream().map(x -> TrdDiscountTypeEnum.getDiscountTypeName(x.getDiscountType())).collect(Collectors.joining(","));
                String discountMemo = String.format("该商品参与的优惠活动包括：[%s]", discountName);
                detail.setMemo(ToolUtil.isNotEmpty(detail.getMemo()) ? detail.getMemo() + discountMemo : discountMemo);
            }

            //商品数量
            BigDecimal totalNum = detail.getTotalNum();

            //需要计算的订单销售参数数据
/*            detail.setRealQty(detail.getTotalNum() - detail.getCancelQty());
            detail.setRealUnitQty( (detail.getTotalNum() - detail.getCancelQty()) / detail.getOrderUnitSize() );*/

            detail.setRealQty( totalNum.subtract(detail.getCancelQty()));
            detail.setRealUnitQty( detail.getRealQty().divide(detail.getOrderUnitSize(), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP) );

            detail.setRealTotalCouponAmt(detail.getCouponDiscountAmt().add(detail.getCouponDiscountAmt2()).add(detail.getActivityDiscountAmt()));

            detail.setRealSaleAmt(detail.getTotalAmt().subtract(detail.getCancelAmt()));
            detail.setRealOrderAmt(detail.getSubOrderAmt().subtract(detail.getCancelAmt()));

            BigDecimal qty = detail.getRealUnitQty().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : detail.getRealUnitQty();
            detail.setRealUnitPrice(detail.getRealOrderAmt().divide(qty,StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP));

        }

        return detailList;
    }


    /**
     * 组装订单详情数据： 合单  注： 目前只支持 单个商品单规格合单
     * @param detailList 订单详情数据
     * @param discountDtlMap 优惠券信息
     */
    private List<OrderDetailOpenDTO> assembleOrderDetailMerge(List<OrderDetailOpenDTO> detailList,Map<Long,List<OrderDiscountDtlDTO>> discountDtlMap){
        //合单后的订单详情数据
        List<OrderDetailOpenDTO> assembleDetailList = new ArrayList<>();

        //根据SpuID 合成 订单商品详情数据
        Map<Long,List<OrderDetailOpenDTO>> orderDetailMap = new HashMap<>();

        detailList.forEach(detail -> {
            //设置优惠信息
            List<OrderDiscountDtlDTO> discountDtls = discountDtlMap.get(detail.getSupplierOrderDtlId());
            //将优惠类型放入备注中
            if(ToolUtil.isNotEmpty(discountDtls) && !discountDtls.isEmpty()){
                String discountName = discountDtls.stream().map(x -> TrdDiscountTypeEnum.getDiscountTypeName(x.getDiscountType())).collect(Collectors.joining(","));
                //合单需要的促销活动相关备注
                detail.setActivityMemo(discountName);
                detail.setMemo(detail.getMemo());
            }

            //商品数量
            BigDecimal totalNum = detail.getTotalNum();

            //需要计算的订单销售参数数据
/*            detail.setRealQty(detail.getTotalNum() - detail.getCancelQty());
            detail.setRealUnitQty( (detail.getTotalNum() - detail.getCancelQty()) / detail.getOrderUnitSize() );*/

            detail.setRealQty( totalNum.subtract(detail.getCancelQty()));
            detail.setRealUnitQty( detail.getRealQty().divide(detail.getOrderUnitSize(), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP) );

            detail.setRealTotalCouponAmt(detail.getCouponDiscountAmt().add(detail.getCouponDiscountAmt2()).add(detail.getActivityDiscountAmt()));

            detail.setRealSaleAmt(detail.getTotalAmt().subtract(detail.getCancelAmt()));
            detail.setRealOrderAmt(detail.getSubOrderAmt().subtract(detail.getCancelAmt()));

            BigDecimal qty = detail.getRealUnitQty().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : detail.getRealUnitQty();
            detail.setRealUnitPrice(detail.getRealOrderAmt().divide(qty,StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP));

            //放入SpuId的订单Map中
            List<OrderDetailOpenDTO> list = orderDetailMap.get(detail.getSpuId());
            list.add(detail);
            orderDetailMap.put(detail.getSpuId(),list);

        });

        //组装合单数据
        orderDetailMap.forEach((spuId,detailItemList) -> {
            //根据商品信息组装合单数据
            OrderDetailOpenDTO assembleDetail = HutoolBeanUtils.toBean(detailItemList.get(NumberPool.INT_ZERO),OrderDetailOpenDTO.class);

            //由于是拿着第一条订单商品信息来组装合单商品数据  所以需要推送的数据都需要重新组装 不能直接使用 （只有基础信息可以使用）
            //detailOpenDTO.set

        });


        return assembleDetailList;
    }


    /**
     * 组装售后订单详情数据
     * @param detailList 售后订单详情集合
     * @param trdAfter 售后订单信息
     * @param orderDetailOpenDTOMap  获取商品详情的原销售订单信息
     * @param afterTypeMap 退款类型/售后类型
     * @param data 入驻商开放信息
     * @return
     */
    private List<AfterDetailPushDTO> assembleAfterDetail(List<AfterDetailPushDTO> detailList,TrdAfter trdAfter,Map<Long, OrderDetailOpenDTO> orderDetailOpenDTOMap,Map<String, String> afterTypeMap,SyncDataDTO data){
        //组装订单详情的商品数据
        detailList.forEach(detail ->{
            //获取商品Sku信息
            SkuDTO skuDTO = sysCacheService.getSkuDTO(detail.getSkuId());
            detail.setItemSourceNo(skuDTO.getSourceNo());

            //条码
            String itemBarcode = null;
            //设置大中小信息
            if(UnitTypeEnum.S(detail.getOrderUnitType())){
                itemBarcode = skuDTO.getBarcode();
            }else if(UnitTypeEnum.M(detail.getOrderUnitType())){
                itemBarcode = skuDTO.getMidBarcode();
            }else if(UnitTypeEnum.L(detail.getOrderUnitType())){
                itemBarcode = skuDTO.getLargeBarcode();
            }
            detail.setItemBarcode(itemBarcode);

            /**
             * 设置物流信息
             * 查询到说明是全国订单、有物流相关信息
             */
            TrdOrderExpress orderExpress = afterApi.getOrderExpressBySupAfterDtlId(detail.getSupplierAfterDtlId());
            if (ObjectUtil.isNotEmpty(orderExpress)){
                log.info("该售后单是全国类型订单：{}",trdAfter);
                detail.setExpressNo(orderExpress.getExpressNo());
                detail.setExpressCom(orderExpress.getExpressCom());
            }

            //设置退货商品的原销售价
            OrderDetailOpenDTO orderDetailOpenDTO = orderDetailOpenDTOMap.get(detail.getSupplierOrderDtlId());
            if(Objects.isNull(orderDetailOpenDTO)){
                log.info("同步入驻商售后订单信息失败，入驻商售后商品明细匹配入驻商销售订单详情失败 data:{},dtlId:{}",data,detail.getSupplierAfterDtlId());
                throw new ServiceException(StringUtils.format("同步入驻商售后订单信息失败，入驻商售后商品明细匹配入驻商销售订单详情失败 data:{},dtlId:{}",data,detail.getSupplierAfterDtlId()));
            }

            //退款原因中 增加 售后类型
            String afterType = String.format("该商品的售后类型为：[%s]",afterTypeMap.get(String.valueOf(detail.getAfterType())));
            detail.setReason( ToolUtil.isEmpty(detail.getReason()) ? afterType : detail.getReason() + ";" + afterType);

            //设置对应入驻商订单详情行号
            detail.setSupplierOrderLineNum(orderDetailOpenDTO.getLineNum());

            //=========需要计算的参数===============
            BigDecimal qty = orderDetailOpenDTO.getTotalNum();
            BigDecimal unitSize = orderDetailOpenDTO.getOrderUnitSize();
            detail.setItemSalePrice(orderDetailOpenDTO.getSubOrderAmt().multiply(unitSize).divide(qty,StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP));
        });

        return detailList;
    }


    /**
     * 组装售后订单详情数据： 合单
     * @param detailList 售后订单详情集合
     * @param trdAfter 售后订单信息
     * @param orderDetailOpenDTOMap  获取商品详情的原销售订单信息
     * @param afterTypeMap 退款类型/售后类型
     * @param data 入驻商开放信息
     * @return
     */
    private List<AfterDetailPushDTO> assembleAfterDetailMerge(List<AfterDetailPushDTO> detailList,TrdAfter trdAfter,Map<Long, OrderDetailOpenDTO> orderDetailOpenDTOMap,Map<String, String> afterTypeMap,SyncDataDTO data){
        //合单后的订单详情数据
        List<AfterDetailPushDTO> assembleDetailList = new ArrayList<>();

        //组装订单详情的商品数据
        detailList.forEach(detail ->{
            //获取商品Sku信息
            SkuDTO skuDTO = sysCacheService.getSkuDTO(detail.getSkuId());
            detail.setItemSourceNo(skuDTO.getSourceNo());

            //条码
            String itemBarcode = null;
            //设置大中小信息
            if(UnitTypeEnum.S(detail.getOrderUnitType())){
                itemBarcode = skuDTO.getBarcode();
            }else if(UnitTypeEnum.M(detail.getOrderUnitType())){
                itemBarcode = skuDTO.getMidBarcode();
            }else if(UnitTypeEnum.L(detail.getOrderUnitType())){
                itemBarcode = skuDTO.getLargeBarcode();
            }
            detail.setItemBarcode(itemBarcode);

            /**
             * 设置物流信息
             * 查询到说明是全国订单、有物流相关信息
             */
            TrdOrderExpress orderExpress = afterApi.getOrderExpressBySupAfterDtlId(detail.getSupplierAfterDtlId());
            if (ObjectUtil.isNotEmpty(orderExpress)){
                log.info("该售后单是全国类型订单：{}",trdAfter);
                detail.setExpressNo(orderExpress.getExpressNo());
                detail.setExpressCom(orderExpress.getExpressCom());
            }

            //设置退货商品的原销售价
            OrderDetailOpenDTO orderDetailOpenDTO = orderDetailOpenDTOMap.get(detail.getSupplierOrderDtlId());
            if(Objects.isNull(orderDetailOpenDTO)){
                log.info("同步入驻商售后订单信息失败，入驻商售后商品明细匹配入驻商销售订单详情失败 data:{},dtlId:{}",data,detail.getSupplierAfterDtlId());
                throw new ServiceException(StringUtils.format("同步入驻商售后订单信息失败，入驻商售后商品明细匹配入驻商销售订单详情失败 data:{},dtlId:{}",data,detail.getSupplierAfterDtlId()));
            }

            //退款原因中 增加 售后类型
            String afterType = String.format("该商品的售后类型为：[%s]",afterTypeMap.get(String.valueOf(detail.getAfterType())));
            detail.setReason( ToolUtil.isEmpty(detail.getReason()) ? afterType : detail.getReason() + ";" + afterType);

            //设置对应入驻商订单详情行号
            detail.setSupplierOrderLineNum(orderDetailOpenDTO.getLineNum());

            //=========需要计算的参数===============
            BigDecimal qty = orderDetailOpenDTO.getTotalNum();
            BigDecimal unitSize = orderDetailOpenDTO.getOrderUnitSize();
            detail.setItemSalePrice(orderDetailOpenDTO.getSubOrderAmt().multiply(unitSize).divide(qty,StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP));
        });

        return assembleDetailList;
    }
}
