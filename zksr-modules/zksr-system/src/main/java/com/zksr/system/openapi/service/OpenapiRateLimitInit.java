package com.zksr.system.openapi.service;

import com.zksr.common.security.rateLimit.RateLimitConfig;
import org.redisson.api.*;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class OpenapiRateLimitInit {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisTemplate<String, RateLimitConfig> redisTemplate;

    public void redisLimitConfig(List<RateLimitConfig> configs) {
        for (RateLimitConfig config : configs) {
            //先删除之前的设置
            RKeys keys = redissonClient.getKeys();
            keys.delete(config.getRateLimiterKey());

            RRateLimiter rRateLimiter = redissonClient.getRateLimiter(config.getRateLimiterKey());
            rRateLimiter.trySetRate(RateType.OVERALL, config.getSize(), config.getMicroSecond(), RateIntervalUnit.MILLISECONDS);
        }
    }
}
