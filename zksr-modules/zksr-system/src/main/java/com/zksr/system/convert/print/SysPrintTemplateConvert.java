package com.zksr.system.convert.print;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysPrintTemplate;
import com.zksr.system.controller.print.vo.SysPrintTemplateRespVO;
import com.zksr.system.controller.print.vo.SysPrintTemplateSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 打印模版 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-09-11
*/
@Mapper
public interface SysPrintTemplateConvert {

    SysPrintTemplateConvert INSTANCE = Mappers.getMapper(SysPrintTemplateConvert.class);

    SysPrintTemplateRespVO convert(SysPrintTemplate sysPrintTemplate);

    SysPrintTemplate convert(SysPrintTemplateSaveReqVO sysPrintTemplateSaveReq);

    PageResult<SysPrintTemplateRespVO> convertPage(PageResult<SysPrintTemplate> sysPrintTemplatePage);
}