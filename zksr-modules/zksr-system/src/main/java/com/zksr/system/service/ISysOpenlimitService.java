package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.domain.SysOpenlimit;
import com.zksr.system.controller.openlimit.vo.SysOpenlimitPageReqVO;
import com.zksr.system.controller.openlimit.vo.SysOpenlimitSaveReqVO;

/**
 * 开发能力qpsService接口
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
public interface ISysOpenlimitService {

    /**
     * 新增开发能力qps
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysOpenlimit(@Valid SysOpenlimitSaveReqVO createReqVO);

    /**
     * 修改开发能力qps
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysOpenlimit(@Valid SysOpenlimitSaveReqVO updateReqVO);

    /**
     * 删除开发能力qps
     *
     * @param sysCode 平台商id
     */
    public void deleteSysOpenlimit(Long sysCode);

    /**
     * 批量删除开发能力qps
     *
     * @param sysCodes 需要删除的开发能力qps主键集合
     * @return 结果
     */
    public void deleteSysOpenlimitBySysCodes(Long[] sysCodes);

    /**
     * 获得开发能力qps
     *
     * @param sysCode 平台商id
     * @return 开发能力qps
     */
    public SysOpenlimit getSysOpenlimit(Long sysCode);

    /**
     * 获得开发能力qps分页
     *
     * @param pageReqVO 分页查询
     * @return 开发能力qps分页
     */
    PageResult<SysOpenlimit> getSysOpenlimitPage(SysOpenlimitPageReqVO pageReqVO);

}
