package com.zksr.system.controller.area.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 区域城市对象 sys_area
 *
 * <AUTHOR>
 * @date 2024-03-01
 */
@Data
@ApiModel("区域城市 - sys_area Response VO")
@AllArgsConstructor
@NoArgsConstructor
public class SysAreaRespVO {
    private static final long serialVersionUID = 1L;

    /** 区域城市id */
    @Excel(name = "区域城市id")
    @ApiModelProperty(value = "区域城市id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 父id */
    @Excel(name = "父id")
    @ApiModelProperty(value = "父id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pid;

    /** 父名称 */
    @Excel(name = "父名称")
    @ApiModelProperty(value = "父名称", example = "示例值")
    private String parentName;

    /** 区域城市名 */
    @Excel(name = "区域城市名")
    @ApiModelProperty(value = "区域城市名", example = "示例值")
    private String areaName;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态", example = "示例值")
    private Long status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "示例值")
    private String memo;

    /** 运营商id */
    @Excel(name = "运营商id")
    @ApiModelProperty(value = "运营商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /** 是否开通本地配送业务 1-是 0-否 */
    @Excel(name = "是否开通本地配送业务 1-是 0-否")
    @ApiModelProperty(value = "是否开通本地配送业务 1-是 0-否", example = "示例值")
    private String localFlag;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id", example = "示例值")
    private Long groupId;

    /** 级别 */
    @Excel(name = "级别")
    @ApiModelProperty(value = "级别")
    private Integer level;

    /** 排序号 */
    @Excel(name = "排序号")
    @ApiModelProperty(value = "排序号")
    private Integer sortNum;

    @ApiModelProperty("一级区域城市ID, 省市区关联(省份)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long firstAreaCityId;

    @ApiModelProperty("二级区域城市ID, 省市区关联(城市)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long secondAreaCityId;

    @ApiModelProperty("三级区域城市ID, 省市区关联(区域)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long threeAreaCityId;


    @ApiModelProperty(value = "级别")
    private Integer deleted;

    public SysAreaRespVO(Long sysCode) {
        this.sysCode = sysCode;
    }
}
