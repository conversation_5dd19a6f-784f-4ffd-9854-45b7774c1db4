package com.zksr.system.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.domain.SysMenu;
import com.zksr.system.enums.SysRoleKeyEnum;
import com.zksr.system.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysRole;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.domain.SysRoleDept;
import com.zksr.system.domain.SysRoleMenu;
import com.zksr.system.domain.SysUserRole;
import com.zksr.system.service.ISysRoleService;

/**
 * 角色 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysRoleServiceImpl implements ISysRoleService
{
    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysRoleDeptMapper roleDeptMapper;

    @Autowired
    private SysMenuMapper menuMapper;

    /**
     * 根据条件分页查询角色数据
     *
     * @param role 角色信息
     * @return 角色数据集合信息
     */
    @Override
    @DataScope(dcAlias = "r", dcFieldAlias = SystemConstants.DC_ID)
    public List<SysRole> selectRoleList(SysRole role)
    {
        ArrayList<String> funScopes = new ArrayList<>();
        LoginUser user = SecurityUtils.getLoginUser();
        List<SysRole> roles = user.getSysUser().getRoles();
        if (Objects.nonNull(roles)) {
            roles.forEach(itemRole -> {
                if (!SystemConstants.FUNC_SCOPE_SOFTWARE.equals(itemRole.getFuncScop())) {
                    funScopes.add(itemRole.getFuncScop());
                }
            });
        }
        role.setFunScopes(funScopes);
        return roleMapper.selectRoleList(role);
    }

    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRolesByUserId(Long userId)
    {
        List<SysRole> userRoles = roleMapper.selectRolePermissionByUserId(userId);
        List<SysRole> roles = selectRoleAll();
        for (SysRole role : roles)
        {
            for (SysRole userRole : userRoles)
            {
                if (role.getRoleId().longValue() == userRole.getRoleId().longValue())
                {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectRolePermissionByUserId(Long userId)
    {
        List<SysRole> perms = roleMapper.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (SysRole perm : perms)
        {
            if (StringUtils.isNotNull(perm))
            {
                permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRoleAll()
    {
        return SpringUtils.getAopProxy(this).selectRoleList(new SysRole());
    }

    /**
     * 根据用户ID获取角色选择框列表
     *
     * @param userId 用户ID
     * @return 选中角色ID列表
     */
    @Override
    public List<Long> selectRoleListByUserId(Long userId)
    {
        return roleMapper.selectRoleListByUserId(userId);
    }

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    @Override
    public SysRole selectRoleById(Long roleId)
    {
        return roleMapper.selectRoleById(roleId);
    }

    /**
     * 校验角色名称是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkRoleNameUnique(SysRole role)
    {
        Long roleId = StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        SysRole info = roleMapper.checkRoleNameUnique(role.getRoleName());
        if (StringUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色权限是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkRoleKeyUnique(SysRole role)
    {
        Long roleId = StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        SysRole info = roleMapper.checkRoleKeyUnique(role.getRoleKey());
        if (StringUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色是否允许操作
     *
     * @param role 角色信息
     */
    @Override
    public void checkRoleAllowed(SysRole role)
    {
        if (StringUtils.isNotNull(role.getRoleId()) && role.isAdmin())
        {
            throw new ServiceException("不允许操作超级管理员角色");
        }
    }

    /**
     * 校验角色是否有数据权限
     *
     * @param roleId 角色id
     */
    @Override
    public void checkRoleDataScope(Long roleId)
    {
        if (!SysUser.isAdmin(SecurityUtils.getUserId()))
        {
            SysRole role = new SysRole();
            role.setRoleId(roleId);
            List<SysRole> roles = SpringUtils.getAopProxy(this).selectRoleList(role);
            if (StringUtils.isEmpty(roles))
            {
                throw new ServiceException("没有权限访问角色数据！");
            }
        }
    }

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    public int countUserRoleByRoleId(Long roleId)
    {
        return userRoleMapper.countUserRoleByRoleId(roleId);
    }

    /**
     * 新增保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertRole(SysRole role)
    {
        // 新增角色信息
        role.setDcId(SecurityUtils.getLoginUser().getDcId());
        if(ToolUtil.isNotEmpty(SecurityUtils.getLoginUser().getSysCode())){
            List<SysRole> roles = SecurityUtils.getLoginUser().getSysUser().getRoles();
            //需要确保用户赋予的角色的funcscope 只能是一个
            if(ToolUtil.isNotEmpty(roles)){
                SysRole prole = this.selectRoleById(roles.get(0).getRoleId());
                role.setFuncScop(prole.getFuncScop());
            }
        }

        roleMapper.insertRole(role);
        return insertRoleMenu(role);
    }

    /**
     * 修改保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRole(SysRole role)
    {
        // 修改角色信息
        roleMapper.updateRole(role);
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(role.getRoleId());
        int i = insertRoleMenu(role);
//        if(ToolUtil.isEmpty(role.getSysCode())){//如果是平台管理员或者超级管理员创建的权限，删除下面渠道的权限
//            updateChildRole();
//        }
        // 如果修改是内置角色, 需要同步内置角色的拓展角色
        if(SysRoleKeyEnum.isPrefabricateRole(role.getRoleKey())){
            syncRole(role);
        }
        return i;
    }

    private void syncRole(SysRole role) {
        List<SysRole> list = roleMapper.selectLikeRoleKey(role.getRoleKey());
        for (SysRole sysRole : list) {
            roleMenuMapper.deleteRoleMenuByRoleId(sysRole.getRoleId());
            initPartnerAdminRoleMenu(role.getRoleId(), sysRole.getRoleId());
        }
    }

//    @Transactional
//    public void updateChildRole(){
//        List<SysUserRole> userRoles = userRoleMapper.selectUserRoleByRoleId(SystemConstants.DEFAULT_PARTNER_ROLE_ID);//查询角色=100的所用用户=运营商超级管理员用户
//        userRoles.forEach(sysUserRole -> {
//            roleMenuMapper.deleteChildRoleMenu(sysUserRole.getSysCode(),sysUserRole.getUserId());
//        });
//    }

    /**
     * 修改角色状态
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public int updateRoleStatus(SysRole role)
    {
        return roleMapper.updateRole(role);
    }

    /**
     * 修改数据权限信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int authDataScope(SysRole role)
    {
        // 修改角色信息
        roleMapper.updateRole(role);
        // 删除角色与部门关联
        roleDeptMapper.deleteRoleDeptByRoleId(role.getRoleId());
        // 新增角色和部门信息（数据权限）
        return insertRoleDept(role);
    }

    /**
     * 新增角色菜单信息
     *
     * @param role 角色对象
     */
    public int insertRoleMenu(SysRole role)
    {
        int rows = 1;
        // 新增用户与角色管理
        List<SysRoleMenu> list = new ArrayList<SysRoleMenu>();
        for (Long menuId : role.getMenuIds())
        {
            checkFuncScop(role.getRoleId(), menuId);
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(role.getRoleId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        if (list.size() > 0)
        {
            rows = roleMenuMapper.batchRoleMenu(list);
        }
        return rows;
    }

    private void checkFuncScop(Long roleId, Long menuId) {
        SysRole role = roleMapper.selectRoleById(roleId);
        if(ToolUtil.isEmpty(role)){
            throw new ServiceException("角色不存在");
        }
        SysMenu menu = menuMapper.selectMenuById(menuId);
        if(ToolUtil.isEmpty(menu)){
            throw new ServiceException("资源不存在");
        }
        if(!menu.getFuncScop().contains(role.getFuncScop())){
            throw new ServiceException("该角色不允许分配资源："+menu.getMenuName());
        }
    }

    /**
     * 新增角色部门信息(数据权限)
     *
     * @param role 角色对象
     */
    public int insertRoleDept(SysRole role)
    {
        int rows = 1;
        // 新增角色与部门（数据权限）管理
        List<SysRoleDept> list = new ArrayList<SysRoleDept>();
        for (Long deptId : role.getDeptIds())
        {
            SysRoleDept rd = new SysRoleDept();
            rd.setRoleId(role.getRoleId());
            rd.setDeptId(deptId);
            list.add(rd);
        }
        if (list.size() > 0)
        {
            rows = roleDeptMapper.batchRoleDept(list);
        }
        return rows;
    }

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleById(Long roleId)
    {
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        // 删除角色与部门关联
        roleDeptMapper.deleteRoleDeptByRoleId(roleId);
        return roleMapper.deleteRoleById(roleId);
    }

    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleByIds(Long[] roleIds)
    {
        for (Long roleId : roleIds)
        {
            checkRoleAllowed(new SysRole(roleId));
            checkRoleDataScope(roleId);
            SysRole role = selectRoleById(roleId);
            if (countUserRoleByRoleId(roleId) > 0) {
                throw new ServiceException(String.format("%1$s已分配,不能删除", role.getRoleName()));
            }
            List<Long> roleKeylist = Arrays.stream(SysRoleKeyEnum.ARRAYS).boxed().collect(Collectors.toList());
            if(roleKeylist.contains(roleId)){
                throw new ServiceException(String.format("系统内置角色,不能删除", role.getRoleName()));
            }
        }
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenu(roleIds);
        // 删除角色与部门关联
        roleDeptMapper.deleteRoleDept(roleIds);
        return roleMapper.deleteRoleByIds(roleIds);
    }

    /**
     * 取消授权用户角色
     *
     * @param userRole 用户和角色关联信息
     * @return 结果
     */
    @Override
    public int deleteAuthUser(SysUserRole userRole)
    {
        return userRoleMapper.deleteUserRoleInfo(userRole);
    }

    /**
     * 批量取消授权用户角色
     *
     * @param roleId 角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
    @Override
    public int deleteAuthUsers(Long roleId, Long[] userIds)
    {
        return userRoleMapper.deleteUserRoleInfos(roleId, userIds);
    }

    /**
     * 批量选择授权用户角色
     *
     * @param roleId 角色ID
     * @param userIds 需要授权的用户数据ID
     * @return 结果
     */
    @Override
    public int insertAuthUsers(Long roleId, Long[] userIds)
    {
        // 新增用户与角色管理
        List<SysUserRole> list = new ArrayList<SysUserRole>();
        for (Long userId : userIds)
        {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            list.add(ur);
        }
        return userRoleMapper.batchUserRole(list);
    }

    @Override
    @Transactional
    public long createPartnerAdminRole(Long sysCode, String partnerName) {
        SysRole partnerAdminRole = this.selectRoleById(SysRoleKeyEnum.PARTNER_ADMIN_ROLE.getRoleId());
        Integer lastSort = roleMapper.selectLastSort() + 1;

        //平台商管理员角色
        SysRole subPartnerAdminRole = new SysRole();
        subPartnerAdminRole.setRoleSort(lastSort);
        subPartnerAdminRole.setDataScope(partnerAdminRole.getDataScope());
        subPartnerAdminRole.setMenuCheckStrictly(partnerAdminRole.isMenuCheckStrictly());
        subPartnerAdminRole.setDeptCheckStrictly(partnerAdminRole.isDeptCheckStrictly());
        subPartnerAdminRole.setStatus(partnerAdminRole.getStatus());
        subPartnerAdminRole.setDelFlag(partnerAdminRole.getDelFlag());

        subPartnerAdminRole.setRoleName("平台商管理员-" + partnerName);
        subPartnerAdminRole.setRoleKey(SysRoleKeyEnum.PARTNER_ADMIN_ROLE.getRoleKey()+"-"+sysCode);
        subPartnerAdminRole.setRemark("平台商管理员角色-" + partnerName);
        subPartnerAdminRole.setFuncScop(SystemConstants.FUNC_SCOPE_PARTNER);
        subPartnerAdminRole.setSysCode(sysCode);
        if (!this.checkRoleNameUnique(subPartnerAdminRole)) {
            throw new ServiceException("修改角色'" + subPartnerAdminRole.getRoleName() + "'失败，角色名称已存在");
        } else if (!this.checkRoleKeyUnique(subPartnerAdminRole)) {
            throw new ServiceException("修改角色'" + subPartnerAdminRole.getRoleName() + "'失败，角色权限已存在");
        }
        roleMapper.insertRole(subPartnerAdminRole);
        Long roleId = subPartnerAdminRole.getRoleId();
        initPartnerAdminRoleMenu(partnerAdminRole.getRoleId(), roleId);

        //平台商管理员角色
        SysRole dcAdminRole = this.selectRoleById(SysRoleKeyEnum.DC_ADMIN_ROLE.getRoleId());
        SysRole subDcAdminRole = new SysRole();
        subDcAdminRole.setRoleSort(lastSort);
        subDcAdminRole.setDataScope(dcAdminRole.getDataScope());
        subDcAdminRole.setMenuCheckStrictly(dcAdminRole.isMenuCheckStrictly());
        subDcAdminRole.setDeptCheckStrictly(dcAdminRole.isDeptCheckStrictly());
        subDcAdminRole.setStatus(dcAdminRole.getStatus());
        subDcAdminRole.setDelFlag(dcAdminRole.getDelFlag());

        subDcAdminRole.setRoleName("运营商管理员-" + partnerName);
        subDcAdminRole.setRoleKey(SysRoleKeyEnum.DC_ADMIN_ROLE.getRoleKey()+"-"+sysCode);
        subDcAdminRole.setRemark("运营商管理员角色-" + partnerName);
        subDcAdminRole.setFuncScop(SystemConstants.FUNC_SCOPE_DC);
        subDcAdminRole.setSysCode(sysCode);
        if (!this.checkRoleNameUnique(subDcAdminRole)) {
            throw new ServiceException("修改角色'" + subDcAdminRole.getRoleName() + "'失败，角色名称已存在");
        } else if (!this.checkRoleKeyUnique(subDcAdminRole)) {
            throw new ServiceException("修改角色'" + subDcAdminRole.getRoleName() + "'失败，角色权限已存在");
        }
        roleMapper.insertRole(subDcAdminRole);
        initPartnerAdminRoleMenu(dcAdminRole.getRoleId(), subDcAdminRole.getRoleId());

        //供应商管理员角色
        SysRole supplierAdminRole = this.selectRoleById(SysRoleKeyEnum.SUPPLIER_ADMIN_ROLE.getRoleId());
        SysRole subSupplierAdminRole = new SysRole();
        subSupplierAdminRole.setRoleSort(lastSort);
        subSupplierAdminRole.setDataScope(supplierAdminRole.getDataScope());
        subSupplierAdminRole.setMenuCheckStrictly(supplierAdminRole.isMenuCheckStrictly());
        subSupplierAdminRole.setDeptCheckStrictly(supplierAdminRole.isDeptCheckStrictly());
        subSupplierAdminRole.setStatus(supplierAdminRole.getStatus());
        subSupplierAdminRole.setDelFlag(supplierAdminRole.getDelFlag());

        subSupplierAdminRole.setRoleName("入驻商管理员-" + partnerName);
        subSupplierAdminRole.setRoleKey(SysRoleKeyEnum.SUPPLIER_ADMIN_ROLE.getRoleKey()+"-"+sysCode);
        subSupplierAdminRole.setRemark("入驻商管理员角色-" + partnerName);
        subSupplierAdminRole.setFuncScop(SystemConstants.FUNC_SCOPE_SUPPLIER);
        subSupplierAdminRole.setSysCode(sysCode);
        if (!this.checkRoleNameUnique(subSupplierAdminRole)) {
            throw new ServiceException("修改角色'" + subSupplierAdminRole.getRoleName() + "'失败，角色名称已存在");
        } else if (!this.checkRoleKeyUnique(subSupplierAdminRole)) {
            throw new ServiceException("修改角色'" + subSupplierAdminRole.getRoleName() + "'失败，角色权限已存在");
        }
        roleMapper.insertRole(subSupplierAdminRole);
        initPartnerAdminRoleMenu(supplierAdminRole.getRoleId(), subSupplierAdminRole.getRoleId());

        //业务员管理角色
        SysRole colonelAdminRole = this.selectRoleById(SysRoleKeyEnum.COLONEL_ADMIN_ROLE.getRoleId());
        SysRole subColonelAdminRole = new SysRole();
        subColonelAdminRole.setRoleSort(lastSort);
        subColonelAdminRole.setDataScope(colonelAdminRole.getDataScope());
        subColonelAdminRole.setMenuCheckStrictly(colonelAdminRole.isMenuCheckStrictly());
        subColonelAdminRole.setDeptCheckStrictly(colonelAdminRole.isDeptCheckStrictly());
        subColonelAdminRole.setStatus(colonelAdminRole.getStatus());
        subColonelAdminRole.setDelFlag(colonelAdminRole.getDelFlag());

        subColonelAdminRole.setRoleName("业务员管理员-" + partnerName);
        subColonelAdminRole.setRoleKey(SysRoleKeyEnum.COLONEL_ADMIN_ROLE.getRoleKey()+"-"+sysCode);
        subColonelAdminRole.setRemark("业务员管理员角色-" + partnerName);
        subColonelAdminRole.setFuncScop(SystemConstants.FUNC_SCOPE_COLONEL);
        subColonelAdminRole.setSysCode(sysCode);
        if (!this.checkRoleNameUnique(subColonelAdminRole)) {
            throw new ServiceException("修改角色'" + subColonelAdminRole.getRoleName() + "'失败，角色名称已存在");
        } else if (!this.checkRoleKeyUnique(subColonelAdminRole)) {
            throw new ServiceException("修改角色'" + subColonelAdminRole.getRoleName() + "'失败，角色权限已存在");
        }
        roleMapper.insertRole(subColonelAdminRole);
        initPartnerAdminRoleMenu(colonelAdminRole.getRoleId(), subColonelAdminRole.getRoleId());

        //品牌商管理角色
        SysRole brandAdminRole = roleMapper.selectByRoleKey(SysRoleKeyEnum.BRAND_ADMIN_ROLE.getRoleKey());
        SysRole subBrandAdminRole = new SysRole();
        subBrandAdminRole.setRoleSort(lastSort);
        subBrandAdminRole.setDataScope(colonelAdminRole.getDataScope());
        subBrandAdminRole.setMenuCheckStrictly(colonelAdminRole.isMenuCheckStrictly());
        subBrandAdminRole.setDeptCheckStrictly(colonelAdminRole.isDeptCheckStrictly());
        subBrandAdminRole.setStatus(colonelAdminRole.getStatus());
        subBrandAdminRole.setDelFlag(colonelAdminRole.getDelFlag());
        subBrandAdminRole.setRoleName("品牌商管理员-" + partnerName);
        subBrandAdminRole.setRoleKey(SysRoleKeyEnum.BRAND_ADMIN_ROLE.getRoleKey()+"-"+sysCode);
        subBrandAdminRole.setRemark("品牌商管理员角色-" + partnerName);
        subBrandAdminRole.setFuncScop(SystemConstants.FUNC_SCOPE_PARTNER);
        subBrandAdminRole.setSysCode(sysCode);
        if (!this.checkRoleNameUnique(subBrandAdminRole)) {
            throw new ServiceException("修改角色'" + subBrandAdminRole.getRoleName() + "'失败，角色名称已存在");
        } else if (!this.checkRoleKeyUnique(subBrandAdminRole)) {
            throw new ServiceException("修改角色'" + subBrandAdminRole.getRoleName() + "'失败，角色权限已存在");
        }
        roleMapper.insertRole(subBrandAdminRole);
        initPartnerAdminRoleMenu(brandAdminRole.getRoleId(), subBrandAdminRole.getRoleId());

        return roleId;
    }

    /**
     * 初始化平台商管理员菜单
     * @param partnerAdminRoleId
     * @param subRoleId
     * @return
     */
    public int initPartnerAdminRoleMenu(Long partnerAdminRoleId, Long subRoleId) {
        int rows = 1;
        SysRole subPartnerAdminRole = this.selectRoleById(subRoleId);
        if(ToolUtil.isEmpty(subPartnerAdminRole)){
            throw new ServiceException("不存在的平台商管理员角色");
        }
        if(ToolUtil.isEmpty(subPartnerAdminRole.getSysCode())){
            throw new ServiceException("该角色不属于平台商管理员角色");
        }
        Long sysCode = subPartnerAdminRole.getSysCode();

        // 新增用户与角色
        List<SysRoleMenu> list = new ArrayList<SysRoleMenu>();
        List<SysRoleMenu> roleMenuList = roleMenuMapper.selectRoleMenuByRoleId(partnerAdminRoleId);

        for (SysRoleMenu roleMenu : roleMenuList) {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(subRoleId);
            rm.setMenuId(roleMenu.getMenuId());
            rm.setSysCode(sysCode);
            list.add(rm);
        }
        if (list.size() > 0) {
            rows = roleMenuMapper.batchRoleMenu(list);
        }
        return rows;
    }
}
