package com.zksr.system.controller;

import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.controller.BaseController;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.core.web.page.TableDataInfo;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysDictType;
import com.zksr.system.api.domain.SysPartnerDictData;
import com.zksr.system.api.domain.SysPartnerDictType;
import com.zksr.system.service.ISysDictTypeService;
import com.zksr.system.service.ISysPartnerDictTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 数据字典信息
 * 
 * <AUTHOR>
 */
@RestController
@Api(tags = "管理后台 - 平台商字典类型接口", produces = "application/json")
@RequestMapping("/partnerDict/type")
public class SysPartnerDictTypeController extends BaseController
{
    @Autowired
    private ISysPartnerDictTypeService dictTypeService;

    @GetMapping("/list")
    @RequiresPermissions("system:partnerDict:list")
    @ApiOperation("查询列表")
    public TableDataInfo list(SysPartnerDictType dictType)
    {
        startPage();
        List<SysPartnerDictType> list = dictTypeService.selectDictTypeList(dictType);
        return getDataTable(list);
    }


    /**
     * 查询字典类型详细
     */
    @GetMapping(value = "/{dictId}")
    @RequiresPermissions("system:partnerDict:query")
    @ApiOperation("查询字典类型详细")
    public AjaxResult getInfo(@PathVariable Long dictId)
    {
        return success(dictTypeService.selectDictTypeById(dictId));
    }

    /**
     * 新增字典类型
     */
    @PostMapping
    @RequiresPermissions("system:partnerDict:add")
    @ApiOperation("新增字典类型")
    public AjaxResult add(@Validated @RequestBody SysPartnerDictType dict)
    {
        dict.setCreateBy(SecurityUtils.getUsername());
        return toAjax(dictTypeService.insertDictType(dict));
    }


    /**
     * 修改
     * @param dict
     * @return
     */
    @PutMapping
    @RequiresPermissions("system:partnerDict:edit")
    @ApiOperation("修改")
    public AjaxResult edit(@Validated @RequestBody SysPartnerDictType dict)
    {

        dict.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(dictTypeService.updateDictType(dict));
    }


    @DeleteMapping("/{dictIds}")
    @RequiresPermissions("system:partnerDict:remove")
    @ApiOperation("删除")
    public AjaxResult remove(@PathVariable Long[] dictIds)
    {
        dictTypeService.deleteDictTypeByIds(dictIds);
        return success();
    }

    /**
     * 刷新字典缓存
     */
    @DeleteMapping("/refreshCache")
    @RequiresPermissions("system:partnerDict:remove")
    @ApiOperation("刷新字典缓存")
    public AjaxResult refreshCache()
    {
        dictTypeService.resetDictCache();
        return success();
    }

    /**
     * 获取字典选择框列表
     */
    @GetMapping("/optionselect")
    @ApiOperation("获取字典选择框列表")
    public AjaxResult optionselect()
    {
        List<SysPartnerDictType> dictTypes = dictTypeService.selectDictTypeAll();
        return success(dictTypes);
    }

    @GetMapping("/selectDictDataByType")
    @ApiOperation("根据字典类型查询字典数据")
    public AjaxResult selectDictDataByType(@RequestParam("dictType") String dictType){
        return success(dictTypeService.selectDictDataByType(dictType,null));
    }
}
