package com.zksr.system.mapper;

import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;
import com.zksr.system.api.dc.vo.SysDcRespVO;
import com.zksr.system.domain.SysDc;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 运营商Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@Mapper
public interface SysDcMapper extends BaseMapperX<SysDc> {
    default PageResult<SysDc> selectPage(SysDcPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysDc>()
                    .eqIfPresent(SysDc::getDcId, reqVO.getDcId())
                    .eqIfPresent(SysDc::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(SysDc::getStatus, reqVO.getStatus())
                    .eqIfPresent(SysDc::getDelFlag, DelFlagConstants.NORMAL)
                    .likeIfPresent(SysDc::getAddress, reqVO.getAddress())
                    .likeIfPresent(SysDc::getDcCode, reqVO.getDcCode())
                    .likeIfPresent(SysDc::getDcName, reqVO.getDcName())
                    .likeIfPresent(SysDc::getContractName, reqVO.getContractName())
                    .likeIfPresent(SysDc::getContractPhone, reqVO.getContractPhone())
                    .applyScope(reqVO.getParams())
                .orderByDesc(SysDc::getDcId));
    }

    List<SysDcRespVO> selectPageExt(@Param("req") SysDcPageReqVO pageReqVO);

    /**
    * @Description: 获取可绑定的运营商
    * @Author: liuxingyu
    * @Date: 2024/3/18 16:20
    */
    List<SysDc> getNotBindDcList(Long areaId);

    /**
     * 根据平台商id查询运营商信息
     * @param sysCode
     * @return
     */
    default List<SysDc> getDcBySysCode(Long sysCode){
        LambdaQueryWrapperX<SysDc> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(SysDc::getSysCode, sysCode);
        return selectList(queryWrapperX);
    }

    /**
     * 查询运营商绑定入驻商ID 集合
     * @param dcId  运营商ID
     * @return  入驻商ID 集合
     */
    List<Long> selectDcSupplierIdList(@Param("dcId") Long dcId);
}
