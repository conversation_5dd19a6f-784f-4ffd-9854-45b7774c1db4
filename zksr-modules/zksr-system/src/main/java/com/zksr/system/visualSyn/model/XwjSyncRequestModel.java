package com.zksr.system.visualSyn.model;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.api.visual.dto.VisualSettingTemplateDto;
import lombok.extern.slf4j.Slf4j;

import java.security.Security;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.pool.NumberPool.INT_ONE;
import static com.zksr.common.core.pool.NumberPool.INT_ZERO;
import static com.zksr.common.core.utils.ToolUtil.removeMapNullEmpty;
import static com.zksr.system.enums.ErrorCodeConstants.SYNC_ASSEMBLE_VERIFY_DATA_ERR;

/**
*  对接湘无界业务抽象子类
* @date 2024/10/25 15:43
* <AUTHOR>
*/
@Slf4j
public class XwjSyncRequestModel<SYNCDTO> extends SyncRequestModel<SYNCDTO>{
    public XwjSyncRequestModel(SyncDataDTO data, VisualSettingMasterDto visualMasterDto, VisualSettingDetailDto visualDetailDto, VisualSettingTemplateDto visualTemplateDto, OpensourceDto opensourceDto, SYNCDTO syncdto) {
        super(data, visualMasterDto, visualDetailDto,visualTemplateDto,opensourceDto, syncdto);
    }

    @Override
    public String assembleVerify(String body){
        try{
            String verifyData = null;
            //MD5加密方式
            StringBuilder md5Builder = new StringBuilder();
            //解析系统唯一对接编码
            Map<String,Object> treeMap = Arrays.stream(opensourceDto.getPublicKey().replaceAll(" +","").split(StringPool.AMPERSAND)).map(x -> x.split(StringPool.EQUALS))
                    .collect(Collectors.toMap(key -> key[INT_ZERO], value -> value[INT_ONE],  (oldValue, newValue) -> newValue, TreeMap::new));

            //body数据转Map
            Map<String,Object> bodyMap = JSONObject.parseObject(body, Map.class);
            //过滤掉为空的数据 重新赋值
            bodyMap = removeMapNullEmpty(bodyMap);

            //放入treeMap排序
            treeMap.putAll(bodyMap);
            //过滤掉为空的数据并且组装数据
            treeMap.forEach((key, value) -> {
                //过滤掉空值和secret值
                if(ToolUtil.isNotEmpty(value) && !"secret".equalsIgnoreCase(key)){
                    md5Builder.append(key).append(StringPool.EQUALS).append(value).append(StringPool.AMPERSAND);
                }
            });

            String md5Data = md5Builder.substring(0, md5Builder.length() - 1);
            //组装好的加密数据 最后需要拼接一个 secret
            md5Data += treeMap.get("secret");

            //开始加密
            md5Data = SecureUtil.md5(md5Data).toUpperCase();

            Map<String,Object> reqMap = new HashMap<>();
            reqMap.put("shop_id",treeMap.get("shop_id"));
            reqMap.put("key",treeMap.get("key"));
            reqMap.put("sign",md5Data);
            reqMap.putAll(bodyMap);

            //组装数据结束
            verifyData = JSON.toJSONString(reqMap);

            return verifyData;
        }catch (Exception e){
            log.error(" XwjSyncRequestModel.assembleVerify失败,", e);
            throw exception(SYNC_ASSEMBLE_VERIFY_DATA_ERR,e.getMessage());
        }
    }
}
