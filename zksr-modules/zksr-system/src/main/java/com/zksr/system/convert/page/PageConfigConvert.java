package com.zksr.system.convert.page;

import com.zksr.system.api.page.dto.PagesConfigDTO;
import com.zksr.system.domain.SysPagesConfig;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/4/12
 * @desc
 */
@Mapper
public interface PageConfigConvert {

    PageConfigConvert INSTANCE = Mappers.getMapper(PageConfigConvert.class);

    PagesConfigDTO convert(SysPagesConfig pagesConfig);

    List<PagesConfigDTO> convertDTOList(List<SysPagesConfig> sysPagesConfigs);
}
