package com.zksr.system.convert.column;

import com.zksr.system.api.exportJob.dto.ExportUserColumnDTO;
import com.zksr.system.controller.column.vo.SysUserColumnRespVO;
import com.zksr.system.controller.column.vo.SysUserColumnSaveReqVO;
import com.zksr.system.domain.SysUserColumn;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 用户头列配置 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-08-14
*/
@Mapper
public interface SysUserColumnConvert {

    SysUserColumnConvert INSTANCE = Mappers.getMapper(SysUserColumnConvert.class);

    SysUserColumnRespVO convert(SysUserColumn sysUserColumn);

    SysUserColumn convert(SysUserColumnSaveReqVO sysUserColumnSaveReq);

    List<SysUserColumnRespVO> convertList(List<SysUserColumn> sysUserColumnPage);

    List<ExportUserColumnDTO> convertExportColumnDTOList(List<SysUserColumn> list);
}