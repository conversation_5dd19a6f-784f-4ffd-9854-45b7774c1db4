package com.zksr.system.visualSyn.service.impl;

import com.zksr.common.core.constant.OpenApiConstants;
import com.zksr.common.core.domain.vo.openapi.*;
import com.zksr.common.core.enums.SendTypeEnum;
import com.zksr.common.core.enums.WarimgPushEnvEnum;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.mx.MxWarningGroupHelper;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.api.visual.dto.VisualSettingTemplateDto;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.visualSyn.model.*;
import com.zksr.system.visualSyn.handler.SyncAssembleData;
import com.zksr.system.visualSyn.dto.SyncDataResult;
import com.zksr.system.visualSyn.service.ISyncDataNewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
*  可视化接口实现
* @date 2024/7/22 15:51
* <AUTHOR>
*/
@Slf4j
@Service("visualSyncServiceNewImpl")
public class VisualSyncServiceNewImpl implements ISyncDataNewService {
    @Resource
    private ISysCacheService sysCacheService;

    @Resource
    private SyncAssembleData syncAssembleData;

    @Resource
    private MxWarningGroupHelper mxWarningGroupHelper;

    @Override
    public String getName() {
        return SendTypeEnum.VISUAL.getCode();
    }


    /**
     * 创建对外同步业务数据处理抽象类子类
     * @param data
     * @param visualMaster
     * @param opensourceDto
     * @param syncdto
     * @return
     */
    private SyncRequestModel createSyncRequestModel(SyncDataDTO data, VisualSettingMasterDto visualMaster,OpensourceDto opensourceDto, Object syncdto)  {

        //获取可视化配置详情信息
        VisualSettingDetailDto visualDetail = sysCacheService.getVisualDetailBySupplier(data.getSupplierId() + StringPool.COLON + data.getTemplateType());

        //获取可视化模板信息
        VisualSettingTemplateDto visualTemplate = new VisualSettingTemplateDto();
        if(ToolUtil.isNotEmpty(visualDetail) && ToolUtil.isNotEmpty(visualDetail.getVisualTemplateId())){
            visualTemplate = sysCacheService.getVisualTemplate(visualDetail.getVisualTemplateId());
        }


        //根据系统类型配置对应的抽象子类
        if(ToolUtil.isNotEmpty(visualMaster.getSourceType())){
            if(SyncSourceType.ANNTOERP.getCode().equals(visualMaster.getSourceType())){
                //安得智联
                return new AnntoSyncRequestModel(data, visualMaster, visualDetail,visualTemplate,opensourceDto, syncdto);
            } else if(SyncSourceType.ERP11.getCode().equals(visualMaster.getSourceType())){
                //中科ERP11.0
                return new ZksrSyncRequestModel(data, visualMaster, visualDetail,visualTemplate,opensourceDto, syncdto);
            }else if(SyncSourceType.FSTERP.getCode().equals(visualMaster.getSourceType())){
                //福商通ERP
                return new FstSyncRequestModel(data, visualMaster, visualDetail,visualTemplate,opensourceDto, syncdto);
            }else if(SyncSourceType.XXERP.getCode().equals(visualMaster.getSourceType())){
                //小象ERP
                return new XxSyncRequestModel(data, visualMaster, visualDetail,visualTemplate,opensourceDto, syncdto);
            }else if(SyncSourceType.XWJERP.getCode().equals(visualMaster.getSourceType())){
                //湘无界ERP
                return new XwjSyncRequestModel(data, visualMaster, visualDetail,visualTemplate,opensourceDto, syncdto);
            }else if(SyncSourceType.HBNERP.getCode().equals(visualMaster.getSourceType())){
                //好帮你ERP
                return new HbnSyncRequestModel(data, visualMaster, visualDetail,visualTemplate,opensourceDto, syncdto);
            } else if (SyncSourceType.FUYUERP.getCode().equals(visualMaster.getSourceType())) {
                // 富屿ERP
                return new FuyuSyncRequestModel(data, visualMaster, visualDetail, visualTemplate, opensourceDto, syncdto);
            } else if (SyncSourceType.HISENSE.getCode().equals(visualMaster.getSourceType())) {
                // 海信ERP
                return new HisenseSyncRequestModel(data, visualMaster, visualDetail, visualTemplate, opensourceDto, syncdto);
            }

        }

        return new SyncRequestModel(data, visualMaster, visualDetail,visualTemplate,opensourceDto , syncdto);
    }

    @Override
    public SyncDataResult syncBranch(SyncDataDTO data, VisualSettingMasterDto master, OpensourceDto opensourceDto) {

        //组装门店信息
        BranchOpenDTO branchOpenDTO = syncAssembleData.assembleBranch(data, opensourceDto);

        //创建对外同步业务数据处理抽象类子类
        SyncRequestModel syncRequest = createSyncRequestModel(data, master,opensourceDto,branchOpenDTO);
        //执行业务逻辑处理
        return syncRequest.processor();
    }

    @Override
    public SyncDataResult syncOrder(SyncDataDTO data, VisualSettingMasterDto master, OpensourceDto opensourceDto) {

        //组装销售订单数据
        OrderOpenDTO orderOpenDTO = syncAssembleData.assembleOrder(data, opensourceDto);

        //创建对外同步业务数据处理抽象类子类
        SyncRequestModel syncRequest = createSyncRequestModel(data, master,opensourceDto,orderOpenDTO);
        //执行业务逻辑处理
        SyncDataResult syncDataResult = syncRequest.processor();
        try {
            if(null == syncDataResult || OpenApiConstants.ERROR.equals(syncDataResult.getCode())){
                String msg = String.format("入驻商[%s]订单[%s]推送失败,erp返回[%s]", orderOpenDTO.getSupplierName(), orderOpenDTO.getSupplierOrderNo(), syncDataResult.getMessage());
                mxWarningGroupHelper.sendMxMessage(msg, WarimgPushEnvEnum.ALL, orderOpenDTO.getSupplierOrderNo());
            }
        } catch (Exception e) {
            log.error("{}推送告警失败,",null == orderOpenDTO ? "null" : orderOpenDTO.getOrderNo(), e);
        }
        return syncDataResult;
    }

    @Override
    public SyncDataResult syncAfter(SyncDataDTO data, VisualSettingMasterDto master, OpensourceDto opensourceDto) {

        //组装售后订单数据
        AfterPushDTO afterPushDTO = syncAssembleData.assembleAfter(data, opensourceDto);

        //创建对外同步业务数据处理抽象类子类
        SyncRequestModel syncRequest = createSyncRequestModel(data, master,opensourceDto,afterPushDTO);
        //执行业务逻辑处理
        return syncRequest.processor();
    }

    @Override
    public SyncDataResult syncReceipt(SyncDataDTO data, VisualSettingMasterDto master, OpensourceDto opensourceDto) {

        //组装订单收款数据
        ReceiptOpenDTO receiptOpenDTO = syncAssembleData.assembleReceipt(data, opensourceDto);

        //创建对外同步业务数据处理抽象类子类
        SyncRequestModel syncRequest = createSyncRequestModel(data, master,opensourceDto,receiptOpenDTO);
        //执行业务逻辑处理
        return syncRequest.processor();
    }

    @Override
    public SyncDataResult syncBranchValueInfo(SyncDataDTO data, VisualSettingMasterDto master, OpensourceDto opensourceDto) {
        //组装订单收款数据
        BranchValueInfoOpenDTO branchValueInfoOpenDTO = syncAssembleData.assembleBranchValueInfo(data, opensourceDto);

        //创建对外同步业务数据处理抽象类子类
        SyncRequestModel syncRequest = createSyncRequestModel(data, master,opensourceDto,branchValueInfoOpenDTO);
        //执行业务逻辑处理
        return syncRequest.processor();
    }
}
