package com.zksr.system.controller.dc;

import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.controller.dc.vo.SysDcAccountRespVO;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;
import com.zksr.system.api.dc.vo.SysDcRespVO;
import com.zksr.system.controller.dc.vo.SysDcSaveReqVO;
import com.zksr.system.convert.dc.DcConvert;
import com.zksr.system.service.ISysDcAreaService;
import com.zksr.system.service.ISysDcService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 运营商Controller
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@Api(tags = "管理后台 - 运营商接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/dc")
public class SysDcController {
    @Autowired
    private ISysDcService sysDcService;

    @Autowired
    private ISysDcAreaService sysDcAreaService;

    @Resource
    private AccountApi accountApi;

    /**
     * @Description: 获取可绑定的运营商
     * @Param:
     * @return: CommonResult<List < SysDcRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/3/18 16:14
     */
    @ApiOperation(value = "获取可绑定的运营商", httpMethod = HttpMethod.GET)
    @GetMapping("/getNotBindDcList")
    public CommonResult<List<SysDcRespVO>> getNotBindDcList(@ApiParam(name = "areaId", value = "区域城市ID")
                                                                @RequestParam(required = false) Long areaId) {
        return success(HutoolBeanUtils.toBean(sysDcService.getNotBindDcList(areaId), SysDcRespVO.class));
    }

    /**
     * 新增运营商
     */
    @ApiOperation(value = "新增运营商", httpMethod = "POST")
    @RequiresPermissions("system:dc:add")
    @Log(title = "运营商", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysDcSaveReqVO createReqVO) {
        Long dcId = sysDcService.insertSysDc(createReqVO);
        sysDcAreaService.reloadCache(dcId);
        return success(dcId);
    }

    /**
     * 修改运营商
     */
    @ApiOperation(value = "修改运营商", httpMethod = "PUT")
    @RequiresPermissions("system:dc:edit")
    @Log(title = "运营商", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysDcSaveReqVO updateReqVO) {
        sysDcService.updateSysDc(updateReqVO);
        sysDcAreaService.reloadCache(updateReqVO.getDcId());
        return success(true);
    }

    /**
     * 删除运营商
     */
    @ApiOperation(value = "删除运营商", httpMethod = "GET")
    @RequiresPermissions("system:dc:remove")
    @Log(title = "运营商", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dcIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] dcIds) {
        sysDcService.deleteSysDcByDcIds(dcIds);
        return success(true);
    }

    /**
     * 获取运营商详细信息
     */
    @ApiOperation(value = "获得运营商详情", httpMethod = "GET")
    @RequiresPermissions("system:dc:query")
    @GetMapping(value = "/{dcId}")
    public CommonResult<SysDcRespVO> getInfo(@PathVariable("dcId") Long dcId) {
        return success(sysDcService.getSysDc(dcId));
    }

    /**
     * 分页查询运营商
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得运营商分页列表", httpMethod = "GET")
    @RequiresPermissions("system:dc:list")
    @DataScope(dcAlias = "sd", dcFieldAlias = SystemConstants.DC_ID)
    public CommonResult<PageResult<SysDcRespVO>> getPage(@Valid SysDcPageReqVO pageReqVO) {
        return success(sysDcService.getSysAreaPageExt(pageReqVO));
    }

    /**
     * 停用运营商
     */
    @ApiOperation(value = "停用运营商", httpMethod = "PUT")
    @RequiresPermissions("system:dc:disable")
    @Log(title = "运营商", businessType = BusinessType.UPDATE)
    @PutMapping("/disable")
    public CommonResult<Boolean> disable(@ApiParam(name = "dcId", value = "运营商ID", required = true) @RequestParam("dcId") Long dcId) {
        sysDcService.disable(dcId);
        return success(true);
    }

    /**
     * 启用运营商
     */
    @ApiOperation(value = "启用运营商", httpMethod = "PUT")
    @RequiresPermissions("system:dc:enable")
    @Log(title = "运营商", businessType = BusinessType.UPDATE)
    @PutMapping("/enable")
    public CommonResult<Boolean> enable(@ApiParam(name = "dcId", value = "运营商ID", required = true) @RequestParam("dcId") Long dcId) {
        sysDcService.enable(dcId);
        return success(true);
    }

    /**
     * 分页查询账户列表
     */
    @GetMapping("/accountList")
    @ApiOperation(value = "获得运营商账户分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ACCOUNT_LIST)
    @RequiresPermissions(Permissions.ACCOUNT_LIST)
    @DataScope(dcAlias = "sd", dcFieldAlias = SystemConstants.DC_ID)
    public CommonResult<PageResult<SysDcAccountRespVO>> getAccountPage(@Valid SysDcPageReqVO pageReqVO) {
        PageResult<SysDcRespVO> pageResult = sysDcService.getSysAreaPageExt(pageReqVO);
        // 转换成账户列表
        PageResult<SysDcAccountRespVO> accountResult = DcConvert.INSTANCE.convert(pageResult);
        // 获取账户余额
        accountResult.getList().forEach(item -> {
            AccAccountDTO account = accountApi.getAccount(item.getSysCode(), item.getDcId(), MerchantTypeEnum.DC.getType()).getCheckedData();
            // 写入金额
            DcConvert.INSTANCE.convert(item, account);
        });
        return success(accountResult);
    }

    /**
     * 修改运营商密码
     */
    @ApiOperation(value = "修改运营商密码", httpMethod = "PUT", notes = "system:dc:edit-password")
    @RequiresPermissions("system:dc:edit-password")
    @Log(title = "修改运营商密码", businessType = BusinessType.UPDATE)
    @PutMapping("/editDcPassword")
    public CommonResult<Boolean> editDcPassword(@Valid @RequestBody SysDcSaveReqVO updateReqVO) {
        sysDcService.updateSysDcPassword(updateReqVO);
        sysDcAreaService.reloadCache(updateReqVO.getDcId());
        return success(true);
    }
    /**
     * 权限字符
     */
    public static class Permissions {
        /** 账户列表 */
        public static final String ACCOUNT_LIST = "system:dc:accountList";
    }
}
