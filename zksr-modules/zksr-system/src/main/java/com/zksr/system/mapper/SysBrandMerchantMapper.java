package com.zksr.system.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.database.query.QueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysBrandMerchant;
import com.zksr.system.controller.brand.vo.SysBrandMerchantPageReqVO;

import java.util.Objects;


/**
 * 品牌商资料Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Mapper
public interface SysBrandMerchantMapper extends BaseMapperX<SysBrandMerchant> {
    default PageResult<SysBrandMerchant> selectPage(SysBrandMerchantPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysBrandMerchant>()
                    .eqIfPresent(SysBrandMerchant::getBrandMerchantId, reqVO.getBrandMerchantId())
                    .eqIfPresent(SysBrandMerchant::getSysCode, reqVO.getSysCode())
                    .likeIfPresent(SysBrandMerchant::getContactName, reqVO.getContactName())
                    .likeIfPresent(SysBrandMerchant::getContactPhone, reqVO.getContactPhone())
                    .likeIfPresent(SysBrandMerchant::getName, reqVO.getName())
                    .likeIfPresent(SysBrandMerchant::getSimpleName, reqVO.getSimpleName())
                    .eqIfPresent(SysBrandMerchant::getContactAddress, reqVO.getContactAddress())
                    .eqIfPresent(SysBrandMerchant::getStatus, reqVO.getStatus())
                    .eqIfPresent(SysBrandMerchant::getMemo, reqVO.getMemo())
                    .eqIfPresent(SysBrandMerchant::getBrandIds, reqVO.getBrandIds())
                    .eqIfPresent(SysBrandMerchant::getSysUserId, reqVO.getSysUserId())
                .orderByDesc(SysBrandMerchant::getBrandMerchantId));
    }

    default SysBrandMerchant selectByUserId(Long sysUserId) {
        return selectOne(new LambdaQueryWrapperX<SysBrandMerchant>().eq(SysBrandMerchant::getSysUserId, sysUserId));
    }
}
