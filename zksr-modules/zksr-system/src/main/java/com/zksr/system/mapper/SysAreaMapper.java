package com.zksr.system.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.controller.area.vo.AreaListReqVO;
import com.zksr.system.controller.area.vo.SysAreaPageReqVO;
import com.zksr.system.controller.area.vo.SysAreaRespVO;
import com.zksr.system.domain.SysArea;
import com.zksr.system.domain.SysAreaCity;
import io.swagger.models.auth.In;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_AREA_BIND_EXISTS;


/**
 * 区域城市Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-01
 */
@SuppressWarnings("all")
@Mapper
public interface SysAreaMapper extends BaseMapperX<SysArea> {
    default PageResult<SysArea> selectPage(SysAreaPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysArea>()
                .eqIfPresent(SysArea::getAreaId, reqVO.getAreaId())
                .eqIfPresent(SysArea::getSysCode, reqVO.getSysCode())
                .eqIfPresent(SysArea::getPid, reqVO.getPid())
                .likeIfPresent(SysArea::getAreaName, reqVO.getAreaName())
                .eqIfPresent(SysArea::getStatus, reqVO.getStatus())
                .eqIfPresent(SysArea::getMemo, reqVO.getMemo())
                .eqIfPresent(SysArea::getDcId, reqVO.getDcId())
                .eqIfPresent(SysArea::getLocalFlag, reqVO.getLocalFlag())
                .eqIfPresent(SysArea::getGroupId, reqVO.getGroupId())
                .eqIfPresent(SysArea::getLevel, reqVO.getLevel())
                .applyScope(reqVO.getParams())
                .orderByDesc(SysArea::getAreaId));
    }

    /**
     * @Description: 运营商获取可绑定的区域城市
     * @Author: liuxingyu
     * @Date: 2024/3/7 14:50
     */
    List<SysArea> getDcNotBindArea(Long dcId);

    /**
     * @Description: 区域城市绑定运营商
     * @Author: liuxingyu
     * @Date: 2024/3/11 16:17
     */
    default Boolean bindDcId(List<Long> sysAreaId, Long dcId) {
        if (ObjectUtil.isEmpty(sysAreaId) || ObjectUtil.isNull(dcId)) {
            throw exception(SYS_AREA_BIND_EXISTS);
        }
        List<SysArea> sysAreaList = sysAreaId.stream().map(x -> {
            SysArea sysArea = new SysArea();
            sysArea.setAreaId(x);
            sysArea.setDcId(dcId);
            return sysArea;
        }).collect(Collectors.toList());
        return updateBatch(sysAreaList);
    }

    /**
     * @Description: 根据运营商ID修改区域城市表DCiD值
     * @Author: liuxingyu
     * @Date: 2024/3/11 16:32
     */
    default Integer removeBindByDcId(Long dcId) {
        SysArea sysArea = new SysArea();
        return update(sysArea, new LambdaUpdateWrapper<SysArea>().eq(SysArea::getDcId, dcId).set(SysArea::getDcId, null));
    }

    /**
     * @Description: 获取所有的区域城市
     * @Author: liuxingyu
     * @Date: 2024/3/15 18:06
     */
    List<SysArea> getAreaAllList(AreaListReqVO reqVO);

    default List<SysArea> getAreaList(SysAreaRespVO respVO) {
        return selectList(new LambdaQueryWrapperX<SysArea>()
                .neIfPresent(SysArea::getAreaId, respVO.getAreaId())
                .eqIfPresent(SysArea::getDcId, respVO.getDcId())
                .eqIfPresent(SysArea::getSysCode, respVO.getSysCode()));
    }

    /**
     * 批量获取城市数据 ( 暂时用于选中回显 )
     *
     * @param areaIds 区域城市ID集合
     * @return
     */
    default List<SysArea> selectedSelectedSysArea(List<Long> areaIds) {
        return selectList(new LambdaQueryWrapperX<SysArea>()
                .in(SysArea::getAreaId, areaIds)
                .select(SysArea::getAreaId, SysArea::getAreaName));
    }

    /**
     * @Description: 获取区域城市分页数据
     * @Author: liuxingyu
     * @Date: 2024/4/15 16:58
     */
    Page<SysAreaRespVO> selectAreaPage(@Param("page") Page<SysAreaPageReqVO> page,
                                       @Param("pageReqVO") SysAreaPageReqVO pageReqVO,
                                       @Param("dcId") Long dcId);

    /**
     * @Description: 获取入驻商绑定是区域城市
     * @Author: liuxingyu
     * @Date: 2024/4/18 8:58
     */
    List<SysArea> getAreaListBySupplierId(Long supplierId);

    default SysArea getDefaultBySyscode(Long sysCode) {
        return selectOne(new LambdaQueryWrapperX<SysArea>()
                .eq(SysArea::getSysCode, sysCode)
                .eq(SysArea::getLevel, 2)
                .eq(SysArea::getStatus, 1)
                .eq(SysArea::getLocalFlag, 1)
                .orderByAsc(SysArea::getCreateTime)
                .last("limit 1"));
    }

    /**
     * @Description: 根据区域名称获取区域列表
     * @Author: liuxingyu
     * @Date: 2024/5/31 10:15
     */
    default Long getByAreaName(Integer level, String areaName, Long areaId) {
        return selectCount(new LambdaQueryWrapper<SysArea>()
                .eq(SysArea::getLevel, level)
                .eq(SysArea::getAreaName, areaName)
                .ne(ObjectUtil.isNotNull(areaId), SysArea::getAreaId, areaId));
    }

    /**
     * @Description: 根据PID获取城市区域集合
     * @Author: liuxingyu
     * @Date: 2024/6/6 14:52
     */
    default List<SysArea> getListByPid(Long areaId) {
        return selectList(new LambdaQueryWrapper<SysArea>().eq(SysArea::getPid, areaId));
    }


    /**
     * @Description: 获取所有的区域城市
     * @Author: liuxingyu
     * @Date: 2024/3/15 18:06
     */
    List<SysArea> getAreaDownList(@Param("areaId") Long areaId, @Param("dcId") Long dcId);


    List<AreaDTO> getAreaBySyscodeAndDcId(@Param("sysCode") Long sysCode, @Param("dcId") Long dcId);


    /**
     * 批量获取城市数据
     * @param areaIds 区域城市ID集合
     */
    default List<SysArea> selectedSysAreaList(List<Long> areaIds) {
        return selectList(new LambdaQueryWrapperX<SysArea>()
                .in(SysArea::getAreaId, areaIds));
    }

    Integer updateDeleted(@Param("areaId") Long areaId, @Param("sysCode") Long sysCode, @Param("deleted") Integer deleted);

    default List<SysArea> getAreaListByAreaId(Long areaId, Long sysCode){
        return selectList(new LambdaQueryWrapperX<SysArea>()
                .eq(SysArea::getPid, areaId)
                .eq(SysArea::getSysCode, sysCode));
    }

    default SysArea getSysAreaByThreeAreaCityId(Long threeAreaCityId){
        // 创建 LambdaQueryWrapper
        LambdaQueryWrapperX<SysArea> queryWrapper = new LambdaQueryWrapperX<SysArea>();

        // 构建查询条件
        queryWrapper.eq(SysArea::getThreeAreaCityId, threeAreaCityId);

        // 执行查询
        List<SysArea> result = selectList(queryWrapper);
        return CollectionUtils.isEmpty(result) ? null : result.get(0);
    }
}
