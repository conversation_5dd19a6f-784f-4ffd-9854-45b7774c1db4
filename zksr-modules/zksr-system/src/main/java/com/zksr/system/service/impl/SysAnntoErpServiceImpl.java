package com.zksr.system.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.zksr.common.core.domain.erp.ApiDataModel;
import com.zksr.common.core.domain.erp.ApiRequestBody;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.erpUtils.HttpUtils;
import com.zksr.common.core.erpUtils.SecretUtil;
import com.zksr.common.core.exception.enums.GlobalErrorCodeConstants;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.reflect.ReflectUtils;
import com.zksr.system.api.openapi.dto.AnntoErpRequestDTO;
import com.zksr.system.api.openapi.dto.AnntoErpResultDTO;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.mapper.SysInterfaceLogMapper;
import com.zksr.system.openapi.service.ISysTokenService;
import com.zksr.system.service.ISysAnntoErpService;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysInterfaceLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

import static com.zksr.common.core.constant.OpenApiConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS_SSS;
import static com.zksr.system.enums.ErrorCodeConstants.OPENAPI_CHECK_REQVO_EXISTS;

@Slf4j
@Service
@SuppressWarnings("all")
public class SysAnntoErpServiceImpl implements ISysAnntoErpService {
    @Autowired
    private ISysCacheService sysCacheService;

    @Resource
    private ISysTokenService sysTokenService;

    @Resource
    private ISysInterfaceLogService sysInterfaceLogService;

    @Autowired
    private SysInterfaceLogMapper sysInterfaceLogMapper;

    @Override
    public AnntoErpResultDTO<String> sendErp(AnntoErpRequestDTO requestDTO) {
        Long supplierId = requestDTO.getSupplierId();
        VisualSettingMasterDto visualMaster = sysCacheService.getVisualMasterBySupplierId(supplierId);
        // 没有对接安得ERP
        if (visualMaster == null || !SyncSourceType.ANNTOERP.getCode().equals(visualMaster.getSourceType())) {
            // 直接返回失败
            if (requestDTO.isAutoFail()) {
                return AnntoErpResultDTO.fail();
            } else {
                // 直接返回成功
                return AnntoErpResultDTO.ok();
            }
        }

        // 保存接口日志
        SysInterfaceLog interfaceLog = saveInterfaceLog(requestDTO, null);

        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            try {
                // 发送ERP
                String sendResult = send(requestDTO);

                // 更新接口日志
                updateLog(interfaceLog, true, sendResult);

                AnntoErpResultDTO<String> result = JSON.parseObject(sendResult, new TypeReference<AnntoErpResultDTO<String>>() {});
                return result;
            } catch (Exception e) {
                log.error("sendErp - {}:执行失败，异常信息：", requestDTO.getB2bRequestType().getInfo(), e);
                updateLog(interfaceLog, false, e.getMessage());
                throw e;
            }
        }
        return null;
    }

    private String send(AnntoErpRequestDTO requestDTO) {
        OpensourceDto opensourceDto = sysCacheService.getOpensourceByMerchantId(requestDTO.getSupplierId());
        String url = opensourceDto.getSendUrl() + requestDTO.getApi();
        Map<String, String> header = MapUtil.of("strategyId", opensourceDto.getSendCode());

        ApiDataModel dataModel = new ApiDataModel()
                .setType(requestDTO.getOperationType())
                .setData(requestDTO.getData());

        String bizData;
        try {
            bizData = SecretUtil.encrypt(dataModel, opensourceDto.getPublicKey());
        } catch (Exception e) {
            log.error("加密JSON数据失败, error: ", e);
            throw exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR);
        }

        ApiRequestBody apiRequestBody = new ApiRequestBody()
                .setReqId(requestDTO.getReqId())
                .setRequestType(requestDTO.getRequestType())
                .setStrategyId(opensourceDto.getSendCode())
                .setReqTime(DateUtils.dateTimeNow(YYYY_MM_DD_HH_MM_SS_SSS))
                .setDataModel(dataModel)
                .setBizData(bizData);

        log.info("{}发送ERP, request: {}", requestDTO.getRequestType().getInfo(), JSON.toJSONString(apiRequestBody));
        String sendResult = HttpUtils.simplePost(url, header, JSON.toJSONString(apiRequestBody), "application/json");
        log.info("{}发送ERP, result: {}", requestDTO.getRequestType().getInfo(), sendResult);
        return sendResult;
    }

    /**
     * 保存接口日志
     * @param requestDTO
     * @param code
     * @return
     */
    private SysInterfaceLog saveInterfaceLog(AnntoErpRequestDTO requestDTO, String code){
        SysInterfaceLog interfaceLog = new SysInterfaceLog();

        Object data = requestDTO.getData();
        B2BRequestType b2bRequestType = requestDTO.getB2bRequestType();
        String reqId = requestDTO.getReqId();

        if(ToolUtil.isEmpty(data)){
            log.info("OPENAPI - {}:入参为空", b2bRequestType.getInfo());
            throw exception(OPENAPI_CHECK_REQVO_EXISTS, b2bRequestType.getInfo());
        }

        // 幂等校验：如果已存在该reqId的日志记录 则拦截不往下执行
        if(sysTokenService.checkReceiveLogBefore(reqId)){
            return interfaceLog;
        }

        //组装日志公共参数
        interfaceLog = getOpenapiInitLog(requestDTO, reqId);
        log.info("sendErp saveInterfaceLog - {}:{}",b2bRequestType.getInfo(), JSON.toJSONString(data));

        //组装业务参数
        //新增或更新请求参数
        interfaceLog.setBizData(JSON.toJSONString(data));
        interfaceLog.setReqId(reqId);

        //新增日志信息
        sysInterfaceLogService.insert(interfaceLog);
        return interfaceLog;
    }

    /**
     * 更新接收日志信息
     * @param sysInterfaceLog
     * @param errMag
     */
    private void updateLog(SysInterfaceLog sysInterfaceLog, boolean result, String errMag) {
        if(B2BRequestType.matchingNotLogType(sysInterfaceLog.getRequestType()) && ToolUtil.isEmpty(sysInterfaceLog.getId())){
            log.error("sendErp:updateLog:sysInterfaceLog:{}", JSON.toJSONString(sysInterfaceLog));
            log.error("sendErp:updateLog:errMag:{}", errMag);
            return;
        }

        //如果是处理异常
        if(ObjectUtil.isNotNull(errMag)){
            sysInterfaceLog.setStatus(LOG_STATUS_ERR);
            sysInterfaceLog.setMessage(errMag);
        }else{
            if(result){
                sysInterfaceLog.setStatus(LOG_STATUS_SUCCES);
                sysInterfaceLog.setMessage(PROCESS_SUCCESS);
            }else{
                sysInterfaceLog.setStatus(LOG_STATUS_FAIN);
                sysInterfaceLog.setMessage(PROCESS_FAIN);
            }

        }

        sysInterfaceLogMapper.updateById(sysInterfaceLog);
    }

    private SysInterfaceLog getOpenapiInitLog(AnntoErpRequestDTO requestDTO, String reqId) {
        SysInterfaceLog logVO = null;

        //如果是根据reqID为唯一标识
        if(ObjectUtil.isNotNull(reqId)){
            logVO = sysInterfaceLogService.getSysInterfaceLogByReqId(reqId);
        }

        //校验
        if(ToolUtil.isEmpty(logVO)){
            logVO = new SysInterfaceLog();
            //获取入驻商编号
            Long supplierId = requestDTO.getSupplierId();

            OpensourceDto opensourceDto = sysCacheService.getOpensourceByMerchantId(requestDTO.getSupplierId());

            //获取系统编码
            Long syscode = opensourceDto.getSysCode();

            //获取开发能力ID
            Long opensourceId = opensourceDto.getOpensourceId();

            //设置系统来源 如果获取不到系统来源 默认B2B
            Integer source = SyncSourceType.B2B.getCode();

            //从缓存中获取入驻商开放配置、可视化配置
            VisualSettingMasterDto visualMaster = sysCacheService.getVisualMasterBySupplierId(supplierId);
            //设置系统来源
            if(Objects.nonNull(visualMaster) && Objects.nonNull(visualMaster.getSourceType())){
                source = visualMaster.getSourceType();
            }

            //设置开发能力ID
            logVO.setOpensourceId(opensourceId);

            logVO.setSysCode(syscode);
            logVO.setSource(source);
            logVO.setReceive(SyncSourceType.B2B.getCode());
            logVO.setStatus(LOG_STATUS_WAIT);
            logVO.setRequestType(requestDTO.getB2bRequestType().getB2bType());
            logVO.setSupplierId(supplierId);
            logVO.setOperationType(requestDTO.getOperationType().getCode());
            logVO.setLogType(LOG_TYPE_RECEIVE);
            logVO.setReqTime(new Date());
            //异步处理的接收接口  所以默认接收完成
            logVO.setReqStatus(REQ_STATUS_2);
            logVO.setMessage(RECEIVE_SUCCESS);
        }else{
            //如果是根据reqId 获取日志信息 能查询到时 说明已经异步处理过的 重新执行需要通过幂等校验
            // 则查询到信息后更改日志状态、（后续更新请求参数）  异步重新处理
            logVO.setStatus(LOG_STATUS_WAIT);
        }

        return logVO;
    }

    private void setOrgCode(Object data, String orgCode) {
        Method method = ReflectUtils.getAccessibleMethod(data, "setOrgCode", String.class);
        if (method != null) {
            try {
                method.invoke(data, orgCode);
            } catch (Exception e) {
                log.error("设置orgCode失败,error:", e);
            }
        }
    }
}
