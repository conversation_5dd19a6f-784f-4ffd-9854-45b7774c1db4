package com.zksr.system.api.partnerConfig;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.partnerConfig.dto.*;
import com.zksr.system.api.partnerConfig.enums.PartnerConfigEnum;
import com.zksr.system.controller.partnerConfig.vo.SysPartnerConfigRespVO;
import com.zksr.system.service.ISysPartnerConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Objects;

@RestController
@ApiIgnore
public class partnerConfigImpl implements PartnerConfigApi {

    @Autowired
    private ISysPartnerConfigService sysPartnerConfigService;

    /**
     * @Description: 获取合利宝配置
     * @Author: liuxingyu
     * @Date: 2024/3/22 11:10
     */
    @Override
    public CommonResult<HeLiBaoPayConfigDTO> getHeLiBaoConfig(Long sysCode) {
        SysPartnerConfigRespVO partnerConfig = sysPartnerConfigService.getPartnerConfig(sysCode, PartnerConfigEnum.HELIBAO_PAY_CONFIG.getType());
        return CommonResult.success(partnerConfig.getHeLiBaoPayConfigDto());
    }

    /**
     * @Description: 获取美的付配置
     *
     */
    @Override
    public CommonResult<MideaPayConfigDTO> getMideaPayConfig(Long sysCode) {
        SysPartnerConfigRespVO partnerConfig = sysPartnerConfigService.getPartnerConfig(sysCode, PartnerConfigEnum.MIDEA_PAY_CONFIG.getType());
        return CommonResult.success(partnerConfig.getMideaPayConfigDTO());
    }

    /**
     * @Description: 获取app配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/22 11:16
     */
    @Override
    public CommonResult<AppletBaseConfigDTO> getAppletBaseConfig(Long sysCode) {
        SysPartnerConfigRespVO partnerConfig = sysPartnerConfigService.getPartnerConfig(sysCode, PartnerConfigEnum.APPLET_BASE_CONFIG.getType());
        return CommonResult.success(partnerConfig.getAppletBaseConfigDto());
    }

    /**
     * @Description: 获取支付配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/26 15:09
     */
    @Override
    public CommonResult<PayConfigDTO> getPayConfig(Long sysCode) {
        SysPartnerConfigRespVO partnerConfig = sysPartnerConfigService.getPartnerConfig(sysCode, PartnerConfigEnum.PAY_CONFIG.getType());
        if (Objects.isNull(partnerConfig)) {
            return CommonResult.success(null);
        }
        return CommonResult.success(partnerConfig.getPayConfigDTO());
    }

    /**
     * @Description: 获取支付账号配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/26 15:10
     */
    @Override
    public CommonResult<PayAccountConfigDTO> getPayAccountConfig(Long sysCode) {
        SysPartnerConfigRespVO partnerConfig = sysPartnerConfigService.getPartnerConfig(sysCode, PartnerConfigEnum.PAY_ACCOUNT_CONFIG.getType());
        return CommonResult.success(partnerConfig.getPayAccountConfigDTO());
    }

    /**
     * @Description: 获取快递配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/26 15:10
     */
    @Override
    public CommonResult<CourierConfigDTO> getCourierConfig(Long sysCode) {
        SysPartnerConfigRespVO partnerConfig = sysPartnerConfigService.getPartnerConfig(sysCode, PartnerConfigEnum.COURIER_CONFIG.getType());
        return CommonResult.success(partnerConfig.getCourierConfigDTO());
    }

    /**
     * @Description: 获取平台商设备配置信息
     * @Author: liuxingyu
     * @Date: 2024/4/26 9:14
     */
    @Override
    @InnerAuth
    public CommonResult<DeviceSettingConfigDTO> getDeviceSettingConfig(Long sysCode) {
        SysPartnerConfigRespVO partnerConfig = sysPartnerConfigService.getPartnerConfig(sysCode, PartnerConfigEnum.DEVICE_SETTING_POLICY.getType());
        return CommonResult.success(partnerConfig.getDeviceSettingConfigDTO());
    }

    /**
     * @Description: 获取平台商设备配置信息
     * @Author: liuxingyu
     * @Date: 2024/4/26 9:14
     */
    @Override
    @InnerAuth
    public CommonResult<SmsConfigDTO> getSmsConfig(Long sysCode) {
        SysPartnerConfigRespVO partnerConfig = sysPartnerConfigService.getPartnerConfig(sysCode, PartnerConfigEnum.SMS_CONFIG.getType());
        return CommonResult.success(partnerConfig.getSmsConfigDTO());
    }

    @Override
    public CommonResult<WxB2bPayConfigDTO> getWxB2bPayConfig(Long sysCode) {
        SysPartnerConfigRespVO partnerConfig = sysPartnerConfigService.getPartnerConfig(sysCode, PartnerConfigEnum.WXB2B_PAY_CONFIG.getType());
        return CommonResult.success(partnerConfig.getWxB2bPayConfigDTO());
    }
}
