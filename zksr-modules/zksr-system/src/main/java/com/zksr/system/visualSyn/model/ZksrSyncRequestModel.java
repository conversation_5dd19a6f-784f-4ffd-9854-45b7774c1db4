package com.zksr.system.visualSyn.model;

import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.erpUtils.RSAUtils;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.api.visual.dto.VisualSettingTemplateDto;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS_SSS;
import static com.zksr.system.enums.ErrorCodeConstants.SYNC_ASSEMBLE_VERIFY_DATA_ERR;

/**
* 对接ERP11.0业务抽象子类
* @date 2024/10/25 15:43
* <AUTHOR>
*/
@Slf4j
public class ZksrSyncRequestModel<SYNCDTO> extends SyncRequestModel<SYNCDTO> {
    public ZksrSyncRequestModel(SyncDataDTO data, VisualSettingMasterDto visualMasterDto, VisualSettingDetailDto visualDetailDto, VisualSettingTemplateDto visualTemplateDto, OpensourceDto opensourceDto, SYNCDTO syncdto) {
        super(data, visualMasterDto, visualDetailDto,visualTemplateDto,opensourceDto, syncdto);
    }

    @Override
    public String assembleVerify(String body) {
        try{
            String verifyData = null;
            //RAS加密方式
            String encryptData = RSAUtils.publicEncrypt(body, RSAUtils.getPublicKey(opensourceDto.getPublicKey()));
            //组装请求体数据
            Map<String, Object> reqMap = new HashMap<>();
            reqMap.put("reqId", data.getReqId());
            reqMap.put("reqTime", DateUtils.dateTimeNow(YYYY_MM_DD_HH_MM_SS_SSS));
            reqMap.put("bizData",encryptData);
            verifyData = JSON.toJSONString(reqMap);
            return verifyData;
        }catch (Exception e){
            log.error(" ZksrSyncRequestModel.assembleVerify异常，", e);
            throw exception(SYNC_ASSEMBLE_VERIFY_DATA_ERR,e.getMessage());
        }
    }
}
