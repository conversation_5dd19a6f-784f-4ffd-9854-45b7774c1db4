package com.zksr.system.controller.partnerConfig.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 平台商配置(由软件商设置)对象 sys_partner_config
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@ApiModel("平台商配置(由软件商设置) - sys_partner_config分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysPartnerConfigPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商配置id */
    @ApiModelProperty(value = "配置类型")
    private Long partnerConfigId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 配置名 */
    @Excel(name = "配置名")
    @ApiModelProperty(value = "配置名")
    private String configName;

    /** 配置键名 */
    @Excel(name = "配置键名")
    @ApiModelProperty(value = "配置键名")
    private String configKey;

    /** 配置值 */
    @Excel(name = "配置值")
    @ApiModelProperty(value = "配置值")
    private String configValue;

    /** 配置类型（数据字典：sys_partner_config_type） */
    @Excel(name = "配置类型", readConverterExp = "数=据字典：sys_partner_config_type")
    @ApiModelProperty(value = "配置类型")
    private String configType;


}
