package com.zksr.system.api.group;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.group.dto.GroupDTO;
import com.zksr.system.controller.group.vo.SysGroupPageReqVO;
import com.zksr.system.domain.SysGroup;
import com.zksr.system.service.ISysGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class GroupApiImpl implements GroupApi {

    @Autowired
    private ISysGroupService sysGroupService;

    @InnerAuth
    @Override
    public CommonResult<GroupDTO> getByGroupId(Long groupId) {
        SysGroup sysGroup = sysGroupService.getSysGroup(groupId);
        return CommonResult.success(HutoolBeanUtils.toBean(sysGroup, GroupDTO.class));
    }

    @Override
    @InnerAuth
    public CommonResult<Map<Long, GroupDTO>> getGroupListBySysCode(@RequestParam(value = "sysCode",required = false) Long sysCode) {
        Map<Long, GroupDTO> groupMap = new HashMap<>();
        List<SysGroup> groupList = sysGroupService.getGroupList();
        if(ToolUtil.isNotEmpty(groupList)){
            List<GroupDTO> groupDTOList = HutoolBeanUtils.toBean(groupList, GroupDTO.class);
            groupMap = groupDTOList.stream().collect(Collectors.toMap(GroupDTO::getGroupId, x->x));
        }
        return success(groupMap);
    }

    @Override
    public CommonResult<GroupDTO> getDefaultGrouping() {
        PageResult<SysGroup> sysGroupPage = sysGroupService.getSysGroupPage(new SysGroupPageReqVO());
        SysGroup sysGroup = sysGroupPage.getList().get(0);
        return CommonResult.success(HutoolBeanUtils.toBean(sysGroup, GroupDTO.class));
    }

    @Override
    public CommonResult<List<GroupDTO>> getListBySysCode(Long sysCode) {
        List<SysGroup> groupList = sysGroupService.getGroupList();
        return success(HutoolBeanUtils.toBean(groupList, GroupDTO.class));
    }
}
