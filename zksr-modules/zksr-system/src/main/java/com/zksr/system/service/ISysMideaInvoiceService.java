package com.zksr.system.service;

import com.zksr.system.api.invoice.dto.InvoiceStandardRecognizeDTO;
import com.zksr.system.api.invoice.dto.TransBlueInvoiceRequest;
import com.zksr.system.api.invoice.dto.TransRedInvoiceRequest;
import com.zksr.system.api.invoice.dto.InvoiceQueryRequest;
import com.zksr.system.api.invoice.dto.InvoiceBaseQueryResDTO;
import com.zksr.system.api.invoice.dto.InvoiceVerifyRequest;
import com.zksr.system.api.invoice.dto.InvoiceVerifyResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ISysMideaInvoiceService {

    /**
     * 蓝票开具
     * @param request 蓝票开具请求
     * @return 执行结果
     */
    Boolean transBlueInvoice(TransBlueInvoiceRequest request);

    /**
     * 红票开具/红冲
     * @param request 红票开具请求
     * @return 执行结果
     */
    Boolean transRedInvoice(TransRedInvoiceRequest request);

    /**
     * 全票种OCR识别
     * @param file 发票文件（支持jpg/jpeg/png/pdf格式，最大6M）
     * @return OCR识别结果
     */
    List<InvoiceStandardRecognizeDTO> recognizeInvoiceOcr(MultipartFile file);

    /**
     * 发票查询
     * @param request 发票查询请求
     * @return 发票查询结果
     */
    InvoiceBaseQueryResDTO queryInvoice(InvoiceQueryRequest request);

    /**
     * 发票查验
     * @param request 发票查验请求
     * @return 发票查验结果
     */
    InvoiceVerifyResponse verifyInvoice(InvoiceVerifyRequest request);
}
