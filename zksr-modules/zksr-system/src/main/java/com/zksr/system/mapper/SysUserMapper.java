package com.zksr.system.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.zksr.system.api.domain.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 */
public interface SysUserMapper {
    /**
     * 根据条件分页查询用户列表
     *
     * @param sysUser 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUserList(SysUser sysUser);

    /**
     * 根据条件分页查询已配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUnallocatedList(SysUser user);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @InterceptorIgnore(tenantLine = "1")
    public SysUser selectUserByUserName(String userName);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @InterceptorIgnore(tenantLine = "1")
    public SysUser selectUserById(Long userId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int insertUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @InterceptorIgnore(tenantLine = "1")
    public int updateUser(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    public int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(@Param("userName") String userName, @Param("password") String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteUserByIds(Long[] userIds);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    @InterceptorIgnore(tenantLine = "1")
    public SysUser checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param phonenumber 手机号码
     * @return 结果
     */
    @InterceptorIgnore(tenantLine = "1")
    public SysUser checkPhoneUnique(String phonenumber);

    /**
     * 校验email是否唯一
     *
     * @param email 用户邮箱
     * @return 结果
     */
    public SysUser checkEmailUnique(String email);

    /**
     * @Description: 根据入驻商获取账号信息
     * @Author: liuxingyu
     * @Date: 2024/4/12 16:40
     */
    String getBySupplierId(Long supplierId);

    /**
     * @Description: 修改入驻商账号密码
     * @Author: liuxingyu
     * @Date: 2024/4/12 16:57
     */
    int updateBySupplierId(@Param("supplierId") Long supplierId, @Param("userName") String userName, @Param("password") String password);

    /**
     * @Description: 根据运营商ID获取账号
     * @Author: liuxingyu
     * @Date: 2024/4/12 16:56
     */
    String getByDcId(Long dcId);

    /**
     * @Description: 修改运营商账号密码
     * @Author: liuxingyu
     * @Date: 2024/4/12 17:16
     */
    int updateByDcId(@Param("dcId") Long dcId, @Param("userName") String userName, @Param("password") String password);

    /**
     * @Description: 根据用户id获取账号
     * @Author: liuxingyu
     * @Date: 2024/4/12 17:40
     */
    String getBySysUserId(Long userId);

    /**
     * @Description: 修改平台商账号密码
     * @Author: liuxingyu
     * @Date: 2024/4/12 17:53
     */
    int updateBySysCode(@Param("sysCode") Long sysCode, @Param("userName") String userName, @Param("password") String password);

    SysUser selectById(Long userId);

    /**
     * 根据手机号查询用户
     * @param phone
     * @return
     */
    SysUser selectByPhone (String phone);

    /**
     * 根据SAAS用户userCode获取用户信息
     * @param saasUserCode
     * @return
     */
    SysUser selectBySaasUserCode(String saasUserCode);

    /**
     * 查询当前运营商下所有的用户
     *
     * @param dcId
     * @return
     */
    List<SysUser> selectByDcId(@Param("dcId") Long dcId);
}
