package com.zksr.system.openapi.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("getToken 接口请求")
public class GetTokenRequest {

    @ApiModelProperty(value = "开放资源key 必填")
    @NotBlank(message = "sourceKey不能为空")
    private String sourceKey;//开放资源key

    @ApiModelProperty(value = "开放能力secret  必填")
    @NotBlank(message = "开放能力secret不能为空")
    private String sourceSecret;



}
