package com.zksr.system.controller.area.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

@Data
@ApiModel(description = "区域城市导入")
public class SysAreaImportExcel {




    @Excel(name = "省市区" , headerColor = IndexedColors.RED)
    private String areaS; // 城市名称

    @Excel(name = "区域名称" , headerColor = IndexedColors.RED)
    private String areaName;

    @Excel(name = "上级区域ID")
    private Long pid;

    @Excel(name = "排序")
    private String sortNum;

    private Long threeAreaCityId;  // 三级城市id

    private String areaId; // 区域城市id

    private  Integer level; // 级别

}