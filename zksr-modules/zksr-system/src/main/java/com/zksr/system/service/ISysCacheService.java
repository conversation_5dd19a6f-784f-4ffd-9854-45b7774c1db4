package com.zksr.system.service;

import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerConfig.dto.SmsConfigDTO;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.dto.VisualSettingTemplateDto;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/4/11
 * @desc
 */
public interface ISysCacheService {

    /**
     * 获取短信配置
     * @param sysCode
     * @return
     */
    SmsConfigDTO getSmsConfig(Long sysCode);

    OpensourceDto getOpensourceByMerchantId(Long merchantId);

    public SpuDTO getSpuDTO(Long spuId);

    public SkuDTO getSkuDTO(Long skuId);

    BranchDTO getBranchDTO(Long branchId);

    /**
     * 获取消息模版
     * @param sysCode   大区编号
     * @param scene     场景
     * @return  消息模版
     */
    List<MessageTemplateDTO> getMessageTemplateDTO(Long sysCode, Integer scene);

    /**
     * 获取入驻商缓存信息
     * @param supplierId    入驻商ID
     * @return
     */
    SupplierDTO getSupplierDTO(Long supplierId);

    /**
     * 获取小程序配置
     * @param sysCode
     * @return
     */
    public AppletBaseConfigDTO getAppletBaseConfigDTO(Long sysCode);

    ColonelDTO getColonel(Long colonelId);

    VisualSettingMasterDto getVisualMaster(Long visualMasterId);

    VisualSettingDetailDto getVisualDetailBySupplier(String key);

    /**
     * @param brandId   品牌ID
     * @return  品牌信息
     */
    BrandDTO getBrandDTO(Long brandId);

    /**
     * @param areaId    城市ID
     * @return  城市信息
     */
    AreaDTO getAreaDTO(Long areaId);

    public ChannelDTO getChannelDto(Long channelId);

    public VisualSettingMasterDto getVisualMasterBySupplierId(Long supplierId);

    public VisualSettingTemplateDto getVisualTemplate(Long visualTemplateId);

    MemberDTO getMember(Long merchantId);

    /**
     * @param sysCode   平台ID
     * @return  根据sysCode 获取支付配置
     */
    public PayConfigDTO getPayConfigDTO(Long sysCode);
}
