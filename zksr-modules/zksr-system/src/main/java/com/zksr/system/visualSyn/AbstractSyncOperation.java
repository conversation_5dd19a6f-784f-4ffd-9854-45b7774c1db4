package com.zksr.system.visualSyn;

import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;
import com.zksr.system.visualSyn.model.AnntoSyncRequestModel;
import com.zksr.system.visualSyn.model.SyncRequestModel;
import com.zksr.system.visualSyn.model.ZksrSyncRequestModel;

//这一步应该可以省略
public abstract class AbstractSyncOperation {
    private SyncRequestModel syncRequestModel;
    public AbstractSyncOperation(SyncRequestModel syncRequestModel){
        this.syncRequestModel = syncRequestModel;
    }


    public SysInterfaceLogSaveReqVO syncData(){
        return null;
    }

}

final class FstSyncOperation extends AbstractSyncOperation {

    public FstSyncOperation(AnntoSyncRequestModel syncRequestModel) {
        super(syncRequestModel);
    }

    @Override
    public SysInterfaceLogSaveReqVO syncData() {
        return null;
    }

}

final class ZksrSyncOperation extends AbstractSyncOperation {

    public ZksrSyncOperation(ZksrSyncRequestModel syncRequestModel) {
        super(syncRequestModel);
    }

    @Override
    public SysInterfaceLogSaveReqVO syncData() {
        return null;
    }

}
