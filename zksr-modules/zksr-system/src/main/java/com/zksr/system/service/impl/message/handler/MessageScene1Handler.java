package com.zksr.system.service.impl.message.handler;

import com.zksr.common.core.enums.CommonMessageSceneEnum;
import com.zksr.system.api.commonMessage.vo.SubscribeEventLocalDeliveryVO;
import com.zksr.system.service.impl.message.SubscribeMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class MessageScene1Handler extends SubscribeMessageHandler<SubscribeEventLocalDeliveryVO> {

    @Override
    public Integer scene() {
        return CommonMessageSceneEnum.LOCAL_DELIVERY_ORDER_PUBLISH.getScene();
    }

    @Override
    protected void initFlowContext() {

    }
}
