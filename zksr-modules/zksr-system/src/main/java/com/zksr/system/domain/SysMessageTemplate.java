package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 公众号, 小程序订阅消息模版对象 sys_message_template
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@TableName(value = "sys_message_template")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysMessageTemplate extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 消息模版id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long messageTemplateId;

    /** 平台商ID */
    @Excel(name = "平台商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 0-停用,1-启用 */
    @Excel(name = "0-停用,1-启用")
    private Integer status;

    /** 模版ID */
    @Excel(name = "模版ID")
    private String templateId;

    /** 模版配置 */
    @Excel(name = "模版配置")
    private String config;

    /**
     * 消息场景 0-用户下单,1-订单开始配送
     * {@link com.zksr.common.core.enums.CommonMessageSceneEnum}
     * */
    @Excel(name = "消息场景 0-用户下单,1-订单开始配送")
    private Integer scene;

    /**
     * 接收对象 member-门店用户,supplier-入驻商,colonel-业务员
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * */
    @Excel(name = "接收对象 member-门店用户,supplier-入驻商,colonel-业务员")
    private String receiveMerchant;

    /**
     * 0-小程序,1-公众号,2-微信门店助手,3-用户APP站内
     * {@link com.zksr.common.core.enums.CommonMessagePushModeEnum}
     */
    private Integer pushMode;
}
