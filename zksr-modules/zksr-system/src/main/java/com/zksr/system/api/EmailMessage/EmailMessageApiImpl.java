package com.zksr.system.api.EmailMessage;

import com.zksr.common.core.domain.vo.openapi.SysInterfaceLogVO;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.EmailMessage.dto.SyncEmailReportData;
import com.zksr.system.api.log.LogApi;
import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;
import com.zksr.system.service.ISysInterfaceLogService;
import com.zksr.system.visualSyn.service.IEmailMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

/**
*
* @date 2025/2/28 15:37
* <AUTHOR>
*/
@RestController
@ApiIgnore
public class EmailMessageApiImpl implements EmailMessageApi {

    @Autowired
    private IEmailMessageService emailMessageService;
    @Override
    public CommonResult<Boolean> sendDaySyncReportDataEmail(SyncEmailReportData emailReportData) {
        emailMessageService.sendDaySyncReportDataEmail(emailReportData);
        return CommonResult.success(true);
    }
}
