package com.zksr.system.visualSyn.model;

import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.api.visual.dto.VisualSettingTemplateDto;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.SYNC_ASSEMBLE_HEADER_DATA_ERR;

@Slf4j
public class FuyuSyncRequestModel<SYNCDTO> extends SyncRequestModel<SYNCDTO>{
    private static final String LTM_USER_KEY = "Ltm-User";
    private static final String LTM_TOKEN_KEY = "Ltm-Token";
    private static final String LTM_TIMESTAMP_KEY = "Ltm-Timestamp";

    public FuyuSyncRequestModel(SyncDataDTO data, VisualSettingMasterDto visualMasterDto, VisualSettingDetailDto visualDetailDto, VisualSettingTemplateDto visualTemplateDto, OpensourceDto opensourceDto, SYNCDTO syncdto) {
        super(data, visualMasterDto, visualDetailDto,visualTemplateDto,opensourceDto, syncdto);
    }

    @Override
    public String assembleHeader(){
        try{
            Map<String, String> header = new HashMap<>();
            header.put(LTM_USER_KEY, opensourceDto.getSendCode());
            header.put(LTM_TOKEN_KEY, getToken(opensourceDto.getSendCode()));
            header.put(LTM_TIMESTAMP_KEY, String.valueOf(System.currentTimeMillis()));
            return JSON.toJSONString(header);
        }catch (Exception e){
            log.error(" AnntoSyncRequestModel.assembleHeader对外推送数据--组装请求头数据失败，失败原因：,", e);
            throw exception(SYNC_ASSEMBLE_HEADER_DATA_ERR,e.getMessage());
        }
    }

    private String getToken(String ltmUser) {
        RedisService redisService = SpringUtils.getBean(RedisService.class);
        String key = RedisConstants.FUYU_TOKEN_KEY + ltmUser;
        String token = redisService.getCacheObject(key);
        if (StringUtils.isBlank(token)) {
            token = createToken();
            // token默认有效时间为5分钟，过期时间设为3分钟，预留缓冲时间
            redisService.setCacheObject(key, token, 3L, TimeUnit.MINUTES);
        }
        return token;
    }

    private String createToken() {
        String s = opensourceDto.getPrivateKey() + System.currentTimeMillis();

        // 使用 SHA-256 哈希算法生成签名
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(s.getBytes(StandardCharsets.UTF_8));

            // 将哈希字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (Exception e) {
            log.error("对接富屿生成签名失败, error:", e);
        }
        return null;
    }
}
