package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.dc.dto.ColonelUserDTO;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;
import com.zksr.system.api.dc.vo.SysDcRespVO;
import com.zksr.system.controller.dc.vo.SysDcSaveReqVO;
import com.zksr.system.domain.SysDc;

import javax.validation.Valid;
import java.util.List;

/**
 * 运营商Service接口
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
public interface ISysDcService {

    /**
     * 新增运营商
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysDc(@Valid SysDcSaveReqVO createReqVO);

    /**
     * 修改运营商
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysDc(@Valid SysDcSaveReqVO updateReqVO);

    /**
     * 删除运营商
     *
     * @param dcId 运营商编号
     */
    public void deleteSysDc(Long dcId);

    /**
     * 批量删除运营商
     *
     * @param dcIds 需要删除的运营商主键集合
     * @return 结果
     */
    public void deleteSysDcByDcIds(Long[] dcIds);

    /**
     * 获得运营商
     *
     * @param dcId 运营商编号
     * @return 运营商
     */
    public SysDcRespVO getSysDc(Long dcId);

    /**
     * 获得运营商分页
     *
     * @param pageReqVO 分页查询
     * @return 运营商分页
     */
    PageResult<SysDc> getSysDcPage(SysDcPageReqVO pageReqVO);

    /**
     * 停用运营商
     * @param dcId
     */
    void disable(Long dcId);

    /**
     * 启用运营商
     * @param dcId
     */
    void enable(Long dcId);

    /**
     * 扩展分页
     * @param pageReqVO
     * @return
     */
    PageResult<SysDcRespVO> getSysAreaPageExt(SysDcPageReqVO pageReqVO);

    /**
    * @Description: 获取可绑定的运营商
    * @Author: liuxingyu
    * @Date: 2024/3/18 16:19
    */
    List<SysDc> getNotBindDcList(Long areaId);

    /**
    * @Description: 根据Id获取运营商信息
    * @Author: liuxingyu
    * @Date: 2024/3/25 14:51
    */
    SysDc getDcById(Long dcId);

    /**
     * 根据平台商id查询运营商信息
     * @param sysCode
     * @return
     */
    List<SysDc> getDcBySysCode(Long sysCode);

    /**
     * 添加业务员账号及角色权限
     * @param colonelDTO
     * @return
     */
    public Long insertColonelUser(ColonelUserDTO colonelDTO);

    /**
     * 修改业务员账号信息
     * @param colonelDTO
     */
    public void updateColonelUser(ColonelUserDTO colonelDTO);

    /**
     * 修改业务员账户信息状态
     */
    public void updateColonelUserStatus(Long userId,String status);

    /**
     * 获取运营商关联的入驻商ID集合
     * @param dcId  运营商ID
     * @return
     */
    List<Long> getDcSupplierList(Long dcId);

    void updateSysDcPassword(SysDcSaveReqVO updateReqVO);
}
