package com.zksr.system.controller.software.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 软件商信息对象 sys_software
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Data
@ApiModel("软件商信息 - sys_software分页 Request VO")
public class SysSoftwareSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 软件商id */
    @ApiModelProperty(value = "软件商id")
    private Long softwareId;

    /** 软件商名 */
    @Excel(name = "软件商名")
    @ApiModelProperty(value = "软件商名")
    private String softwareName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 联系地址 */
    @Excel(name = "联系地址")
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

    /** 关联软件商管理员的账号id */
    @Excel(name = "关联软件商管理员的账号id")
    @ApiModelProperty(value = "关联软件商管理员的账号id")
    private Long softwareUserId;

    /** 软件商分润比例 */
    @Excel(name = "软件商分润比例")
    @ApiModelProperty(value = "软件商分润比例")
    private BigDecimal softwareRate;

    /**
     * 账号
     */
    @Excel(name = "账号")
    @ApiModelProperty(value = "账号", required = true, example = "xxxxx")
    private String userName;

    /**
     * 密码
     */
    @Excel(name = "后台管理密码(不可编辑, 只可新增使用)")
    @ApiModelProperty(value = "后台管理密码(不可编辑, 只可新增使用)", example = "123456")
    private String password;

        /**
     * 进件平台编号
     */
    @ApiModelProperty(value = "支付平台进件编号", required = true, notes = "支付平台进件编号", example = "E00001")
    private String payPlatformMerchantNo;


    /**
     * 支付平台进件单号
     */
    @ApiModelProperty(value = "支付平台进件单号", required = true, notes = "支付平台进件单号", example = "2024042010100")
    private String payPlatformOrderNo;

    /**
     * 进件平台商户名称
     */
    @ApiModelProperty(value = "支付平台进件商户名称", required = true, notes = "支付平台进件商户名称", example = "XXX有限公司")
    private String payPlatformMerchantName;

}
