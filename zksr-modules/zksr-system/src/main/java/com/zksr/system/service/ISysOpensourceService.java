package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.api.LoginOpensource;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.domain.SysOpensource;
import com.zksr.system.controller.opensource.vo.SysOpensourcePageReqVO;
import com.zksr.system.controller.opensource.vo.SysOpensourceSaveReqVO;

import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * 开放能力Service接口
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
public interface ISysOpensourceService {

    /**
     * 新增开放能力
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysOpensource(@Valid SysOpensourceSaveReqVO createReqVO) throws UnsupportedEncodingException;

    /**
     * 修改开放能力
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysOpensource(@Valid SysOpensourceSaveReqVO updateReqVO) throws UnsupportedEncodingException;

    /**
     * 删除开放能力
     *
     * @param opensourceId 开放能力
     */
    public void deleteSysOpensource(Long opensourceId);

    /**
     * 批量删除开放能力
     *
     * @param opensourceIds 需要删除的开放能力主键集合
     * @return 结果
     */
    public void deleteSysOpensourceByOpensourceIds(Long[] opensourceIds);

    /**
     * 获得开放能力
     *
     * @param opensourceId 开放能力
     * @return 开放能力
     */
    public SysOpensource getSysOpensource(Long opensourceId);

    /**
     * 获得开放能力分页
     *
     * @param pageReqVO 分页查询
     * @return 开放能力分页
     */
    PageResult<SysOpensource> getSysOpensourcePage(SysOpensourcePageReqVO pageReqVO);

    SysOpensource getInfoByOpensouceIdAndSourceSecret(String opensourceId, String sourceSecret);

    LoginOpensource getOpensourceInfo(String sourceKey, String sourceSecret);

    SysOpensource getAccountByMerchantIdAndTypeByDefPlatform(Long merchantId, String merchantType);

    /**
     * 修改ERP对接配置
     *
     * @param updateReqVO 创建信息
     * @return 结果
     */
    public void updateSysERPOpensource(@Valid SysOpensourceSaveReqVO updateReqVO) throws UnsupportedEncodingException;

    public void updateTokenUuid(Long opensourceId, String token);

    /**
     * 根据入驻商或平台商ID 获取配置
     * @param merchantId
     * @return
     */
    OpensourceDto getOpensourceByMerchantId(Long merchantId);

    /**
     * 根据入驻商ID 获取对应可视化主表配置
     * @param merchantId
     * @return
     */
    VisualSettingMasterDto getVisualSettingMasterByMerchantId(Long merchantId);

    /**
     * 根据入驻商ID + 模板类型 获取对应可视化详情配置
     * @param key
     * @return
     */
    VisualSettingDetailDto getVisualSettingDetailByMerchantId(String key);

    /**
     * 　openapi新增入驻商开放能力API
     *
     * @param opensourceDto 创建信息
     * @return 结果
     */
    public OpensourceDto insertOpensourceSupplierByApi(OpensourceDto opensourceDto);

    /**
     * 　根据平台商ID 获取当前平台下的开放能力信息  如果传null 则查询所有
     *
     * @param sysCode
     * @return 结果
     */
    public List<OpensourceDto> getOpenSourceBySysCode(Long sysCode);
}
