package com.zksr.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.controller.partnerConfig.vo.SysPartnerConfigPageReqVO;
import com.zksr.system.domain.SysPartnerConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Objects;


/**
 * 平台商配置(由软件商设置)Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Mapper
public interface SysPartnerConfigMapper extends BaseMapperX<SysPartnerConfig> {
    default PageResult<SysPartnerConfig> selectPage(SysPartnerConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysPartnerConfig>()
                .eqIfPresent(SysPartnerConfig::getPartnerConfigId, reqVO.getPartnerConfigId())
                .eqIfPresent(SysPartnerConfig::getSysCode, reqVO.getSysCode())
                .likeIfPresent(SysPartnerConfig::getConfigName, reqVO.getConfigName())
                .eqIfPresent(SysPartnerConfig::getConfigKey, reqVO.getConfigKey())
                .eqIfPresent(SysPartnerConfig::getConfigValue, reqVO.getConfigValue())
                .eqIfPresent(SysPartnerConfig::getConfigType, reqVO.getConfigType())
                .orderByDesc(SysPartnerConfig::getPartnerConfigId));
    }

    /**
     * @Description: 通过平台编号获取配置信息
     * @Param: Long sysCode
     * @return: List<SysPartnerConfig>
     * @Author: liuxingyu
     * @Date: 2024/3/13 15:54
     */
    default List<SysPartnerConfig> selectBySysCode(Long sysCode) {
        return selectList(new LambdaQueryWrapper<SysPartnerConfig>().eq(SysPartnerConfig::getSysCode, sysCode));
    }

    default SysPartnerConfig selectByConfigKey(String configKey, Long sysCode) {
        return selectOne(
                new LambdaQueryWrapper<SysPartnerConfig>()
                        .eq(SysPartnerConfig::getConfigKey, configKey)
                        .eq(Objects.nonNull(sysCode), SysPartnerConfig::getSysCode, sysCode)
                        .last(StringPool.LIMIT_ONE));
    }
}
