package com.zksr.system.controller.visualSetting.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 可视化接口模板对象 visual_setting_template
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@ApiModel("可视化接口模板 - visual_setting_template分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisualSettingTemplatePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(value = "接口模板类型")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long visualTemplateId;

    /** 状态（0 停用  1启用） */
    @Excel(name = "状态", readConverterExp = "0=,停=用,1=启用")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 接口模板名称 */
    @Excel(name = "接口模板名称")
    @ApiModelProperty(value = "接口模板名称")
    private String templateName;

    /** 接口模板JSON */
    @Excel(name = "接口模板JSON")
    @ApiModelProperty(value = "接口模板JSON")
    private String apiTemplate;

    /** 接口模板类型 */
    @Excel(name = "接口模板类型")
    @ApiModelProperty(value = "接口模板类型")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateType;

    /** 系统类型(数据字典sync_source_type 枚举：syncSourceType) */
    @ApiModelProperty(value = "系统类型(安得ERP、安得WMS、新ERP)")
    @Excel(name = "系统类型(安得ERP、安得WMS、新ERP)")
    private Integer sourceType;


}
