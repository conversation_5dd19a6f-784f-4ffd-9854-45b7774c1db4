package com.zksr.system.openapi.controller;


import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.domain.vo.openapi.receive.MemBranchRegisterReqDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncBranchCallDTO;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresOpenapiLogin;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.mq.OpenapiProducer;
import com.zksr.system.openapi.service.ISysTokenService;
import com.zksr.system.service.ISysInterfaceLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;


import static com.zksr.common.core.constant.OpenApiConstants.LOG_STATUS_FAIN;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.system.enums.ErrorCodeConstants.OPENAPI_CHECK_REQVO_EXISTS;


/**
 *
 * <AUTHOR>
 * @date 2024/9/20 11:15
 */
@RestController
@RequestMapping("/openapi/member")
@Api(tags = "OPENAPI - 门店相关接口")
@Slf4j
public class MemberController {
    @Resource
    private BranchApi branchApi;

    @Resource
    private ISysTokenService sysTokenService;

    @Resource
    private OpenapiProducer openapiProducer;

    @Resource
    private ISysInterfaceLogService sysInterfaceLogService;

    @ApiOperation(value = "获取门店数据")
    @GetMapping(value = "/getBranchData")
    @ResponseBody
    @RequiresOpenapiLogin(abilityKey = "getBranchData")
    public CommonResult<PageResult<SyncBranchCallDTO>> getBranchData(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                     @RequestParam(value = "pageSize", defaultValue = "100") Integer pageSize){
        PageResult<SyncBranchCallDTO> pageResult = branchApi.getBranchDataPage(pageNum, pageSize).getCheckedData();
        return success(HutoolBeanUtils.toBean(pageResult, SyncBranchCallDTO.class));
    }

    @ApiOperation(value = "门店注册信息")
    @PostMapping(value = "/registerBranch")
    @ResponseBody
    @RequiresOpenapiLogin(abilityKey = "registerBranch")
    public CommonResult<Boolean> registerBranch(@Valid @RequestBody MemBranchRegisterReqDTO reqDTO) {
        log.info(" 接收门店注册信息:{}", JsonUtils.toJsonString(reqDTO));
        if(ToolUtil.isEmpty(reqDTO)){
            log.info(" 接收门店注册信息:入参为空");
            return CommonResult.success(true);
        }
//        SysInterfaceLog interfaceLog = tokenService.getOpenapiInitLog(B2BRequestType.BRANCH_REGISTER.getB2bType(), OperationType.ADD_OR_UPDATE.getCode(),reqDTO.getReqId() );
//        interfaceLog.setReqId(StringUtils.isEmpty(reqDTO.getReqId()) ? IdUtils.fastSimpleUUID() : reqDTO.getReqId());

        SysInterfaceLog interfaceLog = saveLog(reqDTO, B2BRequestType.BRANCH_REGISTER, reqDTO.getContactPhone(),null);

        reqDTO.setSysCode(interfaceLog.getSysCode());
        interfaceLog.setBizData(JsonUtils.toJsonString(reqDTO));

        try {
            openapiProducer.sendRegisterBranchMsg(interfaceLog);
        } catch (Exception e) {
            log.error(" 接收门店注册信息消息发送失败: {}", e.getMessage(), e);
            interfaceLog.setStatus(LOG_STATUS_FAIN);
            interfaceLog.setMessage(e.getMessage());
            sysInterfaceLogService.updateSysInterfaceLog(HutoolBeanUtils.toBean(interfaceLog, SysInterfaceLogSaveReqVO.class));
        }

        return CommonResult.success(true);
    }


    public SysInterfaceLog saveLog(Object vo,B2BRequestType requestType,String dataId,String code){
        SysInterfaceLog interfaceLog = new SysInterfaceLog();

        if(ToolUtil.isEmpty(vo)){
            log.info("OPENAPI - {}:入参为空",requestType.getInfo());
            throw exception(OPENAPI_CHECK_REQVO_EXISTS,requestType.getInfo());
        }

        //设置reqId
        String reqId = dataId + StringPool.UNDERSCORE + requestType.getB2bType();
        if(B2BRequestType.ORDER_LOG.equals(requestType)){
            reqId = dataId + StringPool.UNDERSCORE + requestType.getB2bType() + StringPool.UNDERSCORE + code;
        }
        //如果已存在该reqId的日志记录 则拦截不往下执行
        if(sysTokenService.checkReceiveLogBefore(reqId)){
            return interfaceLog;
        }

        //组装日志公共参数
        interfaceLog = sysTokenService.getOpenapiInitLog(requestType.getB2bType(), OperationType.UPDATE.getCode(), reqId);
        log.info("OPENAPI - {}:{}",requestType.getInfo(), JSON.toJSONString(vo));

        //组装业务参数
        //新增或更新请求参数
        interfaceLog.setBizData(JSON.toJSONString(vo));
        interfaceLog.setReqId(reqId);

        //新增日志信息
        sysInterfaceLogService.insertLog(interfaceLog);
        return interfaceLog;
    }


}
