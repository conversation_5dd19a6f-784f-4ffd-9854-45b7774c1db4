package com.zksr.system.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.controller.group.vo.SysGroupSaveReqVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysGroup;
import com.zksr.system.controller.group.vo.SysGroupPageReqVO;


/**
 * 平台商城市分组Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@Mapper
public interface SysGroupMapper extends BaseMapperX<SysGroup> {
    default PageResult<SysGroup> selectPage(SysGroupPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysGroup>()
                    .eqIfPresent(SysGroup::getGroupId, reqVO.getGroupId())
                    .eqIfPresent(SysGroup::getSysCode, reqVO.getSysCode())
                    .likeIfPresent(SysGroup::getGroupName, reqVO.getGroupName())
                    .eqIfPresent(SysGroup::getMemo, reqVO.getMemo())
                .orderByDesc(SysGroup::getGroupId));
    }

    default long selectExists(SysGroupSaveReqVO sysGroupSaveReqVO) {
        return selectCount(
                new LambdaQueryWrapperX<SysGroup>()
                        .neIfPresent(SysGroup::getGroupId, sysGroupSaveReqVO.getGroupId())
                        .eq(SysGroup::getGroupName, sysGroupSaveReqVO.getGroupName())
        );
    }
}
