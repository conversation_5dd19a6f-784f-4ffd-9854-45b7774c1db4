package com.zksr.system.convert.partner;

import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.controller.partner.vo.SysPartnerAccountRespVO;
import com.zksr.system.api.partner.vo.SysPartnerRespVO;
import com.zksr.system.domain.SysPartner;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/4/12
 * @desc
 */
@Mapper
public interface PartnerConvert {

    PartnerConvert INSTANCE = Mappers.getMapper(PartnerConvert.class);

    PageResult<SysPartnerAccountRespVO> convert(PageResult<SysPartner> pageResult);

    @Mappings({
            @Mapping(source = "account.platform", target = "payPlatform"),
            @Mapping(source = "account.accountId", target = "accountId"),
            @Mapping(source = "account.withdrawableAmt", target = "withdrawableAmt"),
            @Mapping(source = "account.frozenAmt", target = "frozenAmt"),
            @Mapping(source = "account.creditAmt", target = "creditAmt")
    })
    @BeanMapping(ignoreByDefault = true)
    void convert(@MappingTarget SysPartnerAccountRespVO item, AccAccountDTO account);

    SysPartnerRespVO convert(SysPartner sysPartner);

    PageResult<SysPartnerRespVO> convertRespVO(PageResult<SysPartner> sysPartnerPage);

    PartnerDto convert2PartnerDto(SysPartner sysPartner);
}
