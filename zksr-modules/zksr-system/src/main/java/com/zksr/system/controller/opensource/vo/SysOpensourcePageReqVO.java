package com.zksr.system.controller.opensource.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 开放能力对象 sys_opensource
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@ApiModel("开放能力 - sys_opensource分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysOpensourcePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 开放能力 */
    @ApiModelProperty(value = "开放资源id")
    private Long opensourceId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 资源id */
    @Excel(name = "资源id")
    @ApiModelProperty(value = "资源id", required = true)
    private Long merchantId;

    /** 资源类型（数据字典 supplier-入驻商等） */
    @Excel(name = "资源类型", readConverterExp = "数=据字典,s=upplier-入驻商等")
    @ApiModelProperty(value = "资源类型", required = true)
    private String merchantType;

    /** 开发能力key */
    @Excel(name = "开发能力key")
    @ApiModelProperty(value = "开发能力key")
    private String sourceKey;

    /** 对外加密串 */
    @Excel(name = "对外加密串")
    @ApiModelProperty(value = "对外加密串")
    private String sourceSecret;

    /** ip白名单 */
    @Excel(name = "ip白名单")
    @ApiModelProperty(value = "ip白名单")
    private String ipWhiteList;

    /** ERP接口配置 公钥 */
    @Excel(name = "ERP接口配置 公钥")
    @ApiModelProperty(value = "ERP接口配置 公钥")
    private String publicKey;

    /** ERP接口配置 私钥 */
    @Excel(name = "ERP接口配置 私钥")
    @ApiModelProperty(value = "ERP接口配置 私钥")
    private String privateKey;

    /** 策略源ID */
    @Excel(name = "策略源ID")
    @ApiModelProperty(value = "策略源ID")
    private String strategyId;

    /** erp请求地址 */
    @Excel(name = "erp请求地址")
    @ApiModelProperty(value = "erp请求地址")
    private String erpUrl;

    /**
     * 赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）
     */
    @Excel(name = "赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）")
    @ApiModelProperty("赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）")
    private Integer giftPriceType;

    /**
     * 订单同步是否合单（0否，1是）
     */
    @Excel(name = "订单同步是否合单（0否，1是）")
    @ApiModelProperty("订单同步是否合单（0否，1是）")
    private Integer orderMergeFlag;

}
