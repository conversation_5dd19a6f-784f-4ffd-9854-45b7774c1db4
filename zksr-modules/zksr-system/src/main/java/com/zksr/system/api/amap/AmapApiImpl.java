package com.zksr.system.api.amap;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.amap.IAmapService;
import com.zksr.system.api.amap.dto.LongitudeAndLatitudeResult;
import com.zksr.system.api.amap.vo.LongitudeAndLatitudeParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class AmapApiImpl implements AmapApi{
    @Autowired
    private IAmapService amapService;
    @Override
    public CommonResult<LongitudeAndLatitudeResult> getBranchLongitudeAndLatitude(LongitudeAndLatitudeParam longitudeAndLatitudeParam) {
        return CommonResult.success(amapService.selectLongitudeAndLatitude(longitudeAndLatitudeParam));
    }
}
