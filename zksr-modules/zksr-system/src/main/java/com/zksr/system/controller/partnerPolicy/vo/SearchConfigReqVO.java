package com.zksr.system.controller.partnerPolicy.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/1/20 15:50
 * @注释
 */
@Data
@ApiModel("平台商搜索配置")
public class SearchConfigReqVO {

    /** 搜索配置id */
    @Excel(name = "搜索配置id")
    @ApiModelProperty(value = "搜索配置id")
    private Long partnerPolicyId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 城市区域ID */
    @ApiModelProperty(value = "城市区域ID")
    @Excel(name = "城市区域ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 组合Key */
    @ApiModelProperty(value = "组合Key")
    @Excel(name = "组合Key")
    private String customKey;

    /** 组合value */
    @ApiModelProperty(value = "组合value")
    @Excel(name = "组合value")
    private String customValue;

}
