package com.zksr.system.service;

import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.api.commonMessage.vo.SubscribeEventBodyVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订阅消息发送处理
 * @date 2024/6/13 15:54
 */
@SuppressWarnings("all")
public interface ISubscribeMessageHandler<T> {

    /**
     * 消息处理场景
     * @return
     */
    public Integer scene();

    /**
     * 消息处理场景
     * @return
     */
    public List<CommonMessageDTO> processConvert(SubscribeEventBodyVO<T> subscribeEventBodyVO, MessageTemplateDTO messageTemplateDTO);
}
