package com.zksr.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ip.IpUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.constant.ExportType;
import com.zksr.system.api.export.vo.SysExportJob;
import com.zksr.system.api.export.vo.SysExportJobPageReqVO;
import com.zksr.system.api.export.vo.SysExportJobRespVO;
import com.zksr.system.api.exportJob.dto.ExportUserColumnDTO;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.controller.column.vo.SysUserColumnPageReqVO;
import com.zksr.system.convert.column.SysUserColumnConvert;
import com.zksr.system.domain.SysUserColumn;
import com.zksr.system.mapper.SysExportJobMapper;
import com.zksr.system.service.ISysExportJobService;
import com.zksr.system.service.ISysUserColumnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/1/15 11:37
 */
@Slf4j
@Service
public class SysExportJobServiceImpl extends ServiceImpl<SysExportJobMapper, SysExportJob> implements ISysExportJobService {

    @Autowired
    private SysExportJobMapper sysExportJobMapper;

    @Autowired
    private ISysUserColumnService sysUserColumnService;

    @Override
    public List<SysExportJob> waitJobList() {
        return sysExportJobMapper.selectWaitJobList();
    }

    @Override
    public void createdJob(SysExportJob wmsReportJob) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        ExportType reportTypeName = ExportType.fromValue(wmsReportJob.getExportType());
        if (Objects.isNull(reportTypeName)) {
            log.error("导出类型不存在 {}", wmsReportJob.getExportType());
            return;
        }
        SysExportJob exportJob = SysExportJob.getInstance();
        exportJob.setJobKey(UuidUtils.generateUuid())
                .setName(reportTypeName.getName())
                .setExportType(reportTypeName.getType())
                .setRemoteIp(IpUtils.getIpAddr(ServletUtils.getRequest()))
                .setUserKey(SecurityUtils.getUserKey())
                .setUserInfo(JSON.toJSONString(loginUser))
                .setQueryData(wmsReportJob.getQueryData());
        exportJob.setCreateBy(SecurityUtils.getUsername());

        // 获取导出列配置
        if (StringUtils.isNotEmpty(reportTypeName.getColTableCode())) {
            SysUserColumnPageReqVO colReqVO = new SysUserColumnPageReqVO();
            colReqVO.setCreateBy(SecurityUtils.getUsername());
            colReqVO.setExportVisibelFlag(NumberPool.INT_ONE);
            colReqVO.setTableCode(reportTypeName.getColTableCode());
            List<SysUserColumn> list = sysUserColumnService.getSysUserColumnList(colReqVO);
            if (ObjectUtil.isNotEmpty(list)) {
                exportJob.setColConfig(JSON.toJSONString(SysUserColumnConvert.INSTANCE.convertExportColumnDTOList(list)));
            }
        }
        sysExportJobMapper.insert(exportJob);
    }

    @Override
    public void updateJob(SysExportJob wmsReportJob) {
        sysExportJobMapper.updateById(wmsReportJob);
    }

    @Override
    public PageResult<SysExportJobRespVO> jobPageList(SysExportJobPageReqVO pageReqVO) {
        // 转换返回
        Page<SysExportJobRespVO> page = PageUtils.startPage(pageReqVO);
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            pageReqVO.setUsername(SecurityUtils.getLoginUser().getUsername());
        }
        return new PageResult<>(sysExportJobMapper.selectPageExt(pageReqVO), page.getTotal());
    }
}
