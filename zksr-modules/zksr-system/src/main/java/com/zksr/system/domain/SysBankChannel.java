package com.zksr.system.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 联行号信息对象 sys_bank_channel
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@TableName(value = "sys_bank_channel")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysBankChannel extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long bankChannelId;

    /** 银行编号 */
    @Excel(name = "银行编号")
    private String bankNo;

    /** 银行简称 */
    @Excel(name = "银行简称")
    private String bankName;

    /** 支行名称 */
    @Excel(name = "支行名称")
    private String branchName;

    /** 联行号 */
    @Excel(name = "联行号")
    private String bankChannelNo;

    /** 0正常 1异常 */
    @Excel(name = "0正常 1异常")
    private Integer status;

    /** 省 */
    @Excel(name = "省")
    private String provinceName;

    /** 市 */
    @Excel(name = "市")
    private String cityName;

}
