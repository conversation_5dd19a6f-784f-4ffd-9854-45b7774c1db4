package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 区域城市对象 sys_area
 *
 * <AUTHOR>
 * @date 2024-03-01
 */
@TableName(value = "sys_area")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysArea extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 区域城市id */
    @TableId(type = IdType.AUTO)
    @Excel(name = "区域城市id")
    private Long areaId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 父id */
    @Excel(name = "父id")
    private Long pid;

    /** 区域城市名 */
    @Excel(name = "区域城市名")
    private String areaName;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 运营商id */
    @Excel(name = "运营商id")
    private Long dcId;

    /** 是否开通本地配送业务 1-是 0-否 */
    @Excel(name = "是否开通本地配送业务 1-是 0-否")
    private String localFlag;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    private Long groupId;

    /** 级别 */
    @Excel(name = "级别")
    private Integer level;

    /** 排序号 */
    @Excel(name = "排序号")
    private Integer sortNum;

    @ApiModelProperty("三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    private Long threeAreaCityId;

    /**
     * 逻辑删除字段
     */
    @TableLogic
    private Integer deleted;
}
