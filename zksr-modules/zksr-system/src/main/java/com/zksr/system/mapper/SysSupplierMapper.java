package com.zksr.system.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.domain.vo.openapi.CustomApiDetail;
import com.zksr.common.core.domain.vo.openapi.CustomApiMaster;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.api.supplier.vo.SysSupplierPageReqVO;
import com.zksr.system.api.supplier.vo.SysSupplierRespVO;
import com.zksr.system.domain.SysSupplier;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 入驻商信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface SysSupplierMapper extends BaseMapperX<SysSupplier> {
    Page<SysSupplierRespVO> selectPage(@Param("reqVO") SysSupplierPageReqVO reqVO
            , @Param("page") Page<SysSupplierPageReqVO> page, @Param("supplierIds") List<Long> supplierIds);

    /**
     * @Description: 通过名称查询入驻商数量
     * @Author: liuxingyu
     * @Date: 2024/3/4 10:57
     */
    default Long selectSupplierNameCount(String supplierName) {
        return selectCount(new LambdaQueryWrapper<SysSupplier>().eq(SysSupplier::getSupplierName, supplierName));
    }

    default Long selectSupplierNameCountNotSupplierId(Long supplierId, String supplierName) {
        return selectCount(
                new LambdaQueryWrapper<SysSupplier>()
                        .ne(SysSupplier::getSupplierId, supplierId)
                        .eq(SysSupplier::getSupplierName, supplierName)
        );
    }

    //根据查询条件查询入驻商列表
    default List<SysSupplier> selectSupplierList(Long dcId) {
        return selectList(new LambdaQueryWrapperX<SysSupplier>()
                .eqIfPresent(SysSupplier::getDcId, dcId)
                .eq(SysSupplier::getStatus, 1));
    }

    public List<SysSupplierRespVO> selectSupplierListByUser(@Param("dcId") Long dcId, @Param("supplierId") Long supplierId);

    /**
     * @Description: 获取入驻商基本信息
     * @Author: liuxingyu
     * @Date: 2024/3/26 16:51
     */
    SysSupplierRespVO selectSysSupplierById(@Param("supplierId") Long supplierId);

    /**
     * @Description: 获取电子围栏ID不为空的入驻商
     * @Author: liuxingyu
     * @Date: 2024/3/27 19:47
     */
    default List<SysSupplier> getGfidIsNotNull() {
        return selectList(new LambdaQueryWrapper<SysSupplier>().isNotNull(SysSupplier::getGfid));
    }

    /**
     * @Description: 根据电子围栏ID获取入驻商ID
     * @Author: liuxingyu
     * @Date: 2024/3/27 20:06
     */
    default List<SysSupplier> getByGfid(List<Long> amapGfids) {
        return selectList(new LambdaQueryWrapper<SysSupplier>().in(SysSupplier::getGfid, amapGfids));
    }

    /**
     * @Description: 根据运营商ID获取区域下所有的入驻商
     * @Author: liuxingyu
     * @Date: 2024/4/9 17:33
     */
    List<Long> getByDcId(@Param("dcId") Long dcId);

    /**
     * @Description: 根据查询条件获取入驻商
     * @Author: liuxingyu
     * @Date: 2024/4/10 17:01
     */
    default List<SysSupplier> getByOrderParam(List<Long> ids, String supplier) {
        return  selectList(new LambdaQueryWrapper<SysSupplier>().in(ObjectUtil.isNotEmpty(ids),SysSupplier::getSupplierId, ids)
                .and(StringUtils.isNotBlank(supplier), x -> x.like(SysSupplier::getSupplierName, supplier).or().like(SysSupplier::getSupplierId, supplier)));
    }

    CustomApiMaster getCustomApiMaster(@Param("customApiMaster")CustomApiMaster customApiMaster);

    List<CustomApiDetail> getCustomApiDetail(@Param("apiNo") String apiNo);

    default List<String> getSupplierNames(List<Long> supplierIds) {
        // 使用 LambdaQueryWrapper 批量查询入驻商名称
        return selectList(new LambdaQueryWrapper<SysSupplier>()
                .in(SysSupplier::getSupplierId, supplierIds)
                .select(SysSupplier::getSupplierName)
        ).stream()
                .map(SysSupplier::getSupplierName)
                .collect(Collectors.toList());
    }

    /**
     * 批量获取入驻商
     */
    default List<SysSupplier> getSupplierListByIds(List<Long> supplierIds){
        return selectList(new LambdaQueryWrapperX<SysSupplier>()
                .in(ObjectUtil.isNotEmpty(supplierIds), SysSupplier::getSupplierId, supplierIds)
        );
    }
}
