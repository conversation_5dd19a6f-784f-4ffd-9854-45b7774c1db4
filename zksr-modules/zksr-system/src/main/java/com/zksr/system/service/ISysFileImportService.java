package com.zksr.system.service;

import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysPartnerDictData;
import com.zksr.system.api.form.SysFileImportForm;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 导入记录 业务层
 * 
 * <AUTHOR>
 */
public interface ISysFileImportService
{

    /**
     * 创建导入任务
     * @param file
     * @param importType
     * @return
     * @throws Exception
     */
    SysFileImport createFileImportTask(MultipartFile file, String importType) throws Exception;

    /**
     * 增加接收mq
     * @param sysFileImport
     * @return
     */
    SysFileImport addMqReceiveNum(SysFileImport sysFileImport);

    SysFileImport getById(Long fileImportId);

    Integer updateSysFileImport(SysFileImport sysFileImport);

    Integer updateImportStatus(Long id,Integer status);

    List<SysFileImport> getList(SysFileImportForm form);

    /**
     * 消息重发
     * @param fileImportId
     * @return
     */
    SysFileImport eventRetry(Long fileImportId);

    List<SysFileImport> findGoingEvent();

}
