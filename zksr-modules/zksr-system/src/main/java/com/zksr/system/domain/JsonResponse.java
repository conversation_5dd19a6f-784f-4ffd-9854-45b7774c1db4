package com.zksr.system.domain;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: WebRequestInterceptor
 * Author: james.z
 * Date: 2018-12-19 11:49
 * Description: 统一返回，结果数据
 */
public class JsonResponse<T> implements Serializable {

    private String code = "0";
    private String msg = "success";
    private String timestamp;
    public T data;

    public JsonResponse() {
    }

    public JsonResponse(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }



    public void errorParam(String code, String msg, Logger log) {
        custom(code, msg, log);
    }

    public void custom(String code, String msg, Logger log) {
        this.code = code;
        this.msg = msg;
        log.error("error code|{}|msg:{}", code, msg);
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public void go(Logger logger) {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (StackTraceElement s : stackTrace) {
            logger.error(s.getClassName() + ",-" + s.getMethodName() + ":["
                    + " " + s.getLineNumber() + "]");
        }
    }


    public JsonResponse build(String code, String msg) {
        this.code = code;
        this.msg = msg;
        LocalDateTime arrivalDate = LocalDateTime.now();
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss:SSS");
        String timestamp = arrivalDate.format(format);
        this.timestamp = timestamp;
        return this;
    }

    @Override
    public String toString() {
        return JSON.toJSONStringWithDateFormat(this, DatePattern.PURE_DATETIME_PATTERN);
    }

    /**
     * @Description: 静态方法
     * @Param: [data]
     * @return: JsonResponse<java.lang.Object>
     * @Author: zhouxg2
     * @Date: 2019-2-19
     */
    public static JsonResponse<Object> success(Object data) {
        JsonResponse<Object> jsonResponse = new JsonResponse<>("0","success");
        jsonResponse.setData(data);
        return jsonResponse;
    }

    public static JsonResponse<Object> fail(String msg) {
        JsonResponse<Object> jsonResponse = new JsonResponse<>("1", "系统繁忙");
        return jsonResponse;
    }

    public static JsonResponse<Object> fail(String code, String msg) {

        JsonResponse<Object> jsonResponse = new JsonResponse<>(code, msg);
        return jsonResponse;
    }
}
