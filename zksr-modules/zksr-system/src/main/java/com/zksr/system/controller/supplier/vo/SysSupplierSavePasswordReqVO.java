package com.zksr.system.controller.supplier.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 入驻商信息对象 sys_supplier
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Data
@ApiModel("入驻商信息 - sys_supplier分页 Request VO")
public class SysSupplierSavePasswordReqVO {
    private static final long serialVersionUID = 1L;

    /** 入驻商id */
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 入驻商编号 */
    @ApiModelProperty(value = "入驻商编号")
    private String supplierCode;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /**
     * 密码
     */
    @Excel(name = "后台管理密码(不可编辑, 只可新增使用)")
    @ApiModelProperty(value = "后台管理密码(不可编辑, 只可新增使用)", example = "123456")
    private String password;

    /**
     * 账号
     */
    @Excel(name = "账号")
    @ApiModelProperty(value = "账号", example = "xxxxx")
    private String userName;

}
