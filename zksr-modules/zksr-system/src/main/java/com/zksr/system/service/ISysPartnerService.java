package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partner.vo.SysPartnerPageReqVO;
import com.zksr.system.api.partner.vo.SysPartnerRespVO;
import com.zksr.system.controller.partner.vo.SysPartnerSaveReqVO;
import com.zksr.system.domain.SysPartner;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台商信息Service接口
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
public interface ISysPartnerService {

    /**
     * 新增平台商信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysPartner(@Valid SysPartnerSaveReqVO createReqVO);

    /**
     * 修改平台商信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysPartner(@Valid SysPartnerSaveReqVO updateReqVO);

    /**
     * 删除平台商信息
     *
     * @param sysCode 平台商id
     */
    public void deleteSysPartner(Long sysCode);

    /**
     * 批量删除平台商信息
     *
     * @param sysCodes 需要删除的平台商信息主键集合
     * @return 结果
     */
    public void deleteSysPartnerBySysCodes(Long[] sysCodes);

    /**
     * 获得平台商信息
     *
     * @param sysCode 平台商id
     * @return 平台商信息
     */
    public SysPartner getSysPartner(Long sysCode);

    /**
     * 获得平台商信息分页
     *
     * @param pageReqVO 分页查询
     * @return 平台商信息分页
     */
    PageResult<SysPartner> getSysPartnerPage(SysPartnerPageReqVO pageReqVO);

    /**
     * 获得平台商信息
     *
     */
    PageResult<SysPartnerRespVO> getSysPartnerList(SysPartnerPageReqVO pageReqVO);

    /**
     * 启用平台
     * @param sysCode
     */
    void disable(Long sysCode);


    /**
     * 停用平台
     * @param sysCode
     */
    void enable(Long sysCode);

    void reloadPartnerDtoCache(Long sysCode);

    PartnerDto getByCacheKey(String source);

    /**
     * 获取所有平台商信息
     * @return
     */
    List<SysPartner> getPartnerInfo();

    /**
     * 根据软件商ID查询关联的平台商SysCode集合
     *
     *@param softwareId
     * @return sysCodeList
     */
    public List<Long> getSysCodeListBySoftwareId(Long softwareId);

    SysPartner getSysPartnerBySource(String source);

    void updateSysPartnerPassword(SysPartnerSaveReqVO updateReqVO);

    /**
     * 根据saas租户查询平台商
     * @param saasTenantCode
     * @return
     */
    SysPartner getBySaasTenantCode(String saasTenantCode);
}
