package com.zksr.system.controller.channel;

import cn.hutool.core.bean.BeanUtil;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.controller.channel.vo.SysChannelPageReqVO;
import com.zksr.system.controller.channel.vo.SysChannelRespVO;
import com.zksr.system.controller.channel.vo.SysChannelSaveReqVO;
import com.zksr.system.controller.channel.vo.SysChannelSelectedRespVO;
import com.zksr.system.domain.SysChannel;
import com.zksr.system.service.ISysChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Size;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 渠道信息Controller
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@Api(tags = "管理后台 - 渠道信息接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/channel")
public class SysChannelController {
    @Autowired
    private ISysChannelService sysChannelService;

    /**
     * @Description: 获取所有渠道
     * @Param:
     * @return: CommonResult<List < SysChannelRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/3/22 14:38
     */
    @ApiOperation(value = "获取所有渠道", httpMethod = HttpMethod.GET)
    @GetMapping("/getChannelList")
    public CommonResult<List<SysChannelRespVO>> getChannelList() {
        return success(HutoolBeanUtils.toBean(sysChannelService.getChannelList(),SysChannelRespVO.class));
    }

    /**
     * 新增渠道信息
     */
    @ApiOperation(value = "新增渠道信息", httpMethod = "POST", notes = "system:channel:add")
    @RequiresPermissions("system:channel:add")
    @Log(title = "渠道信息", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysChannelSaveReqVO createReqVO) {
        return success(sysChannelService.insertSysChannel(createReqVO));
    }

    /**
     * 修改渠道信息
     */
    @ApiOperation(value = "修改渠道信息", httpMethod = "PUT", notes = "system:channel:edit")
    @RequiresPermissions("system:channel:edit")
    @Log(title = "渠道信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysChannelSaveReqVO updateReqVO) {
        sysChannelService.updateSysChannel(updateReqVO);
        return success(true);
    }

    /**
     * 删除渠道信息
     */
    @ApiOperation(value = "删除渠道信息", httpMethod = "GET", notes = "system:channel:remove")
    @RequiresPermissions("system:channel:remove")
    @Log(title = "渠道信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{channelIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] channelIds) {
        sysChannelService.deleteSysChannelByChannelIds(channelIds);
        return success(true);
    }

    /**
     * 获取渠道信息详细信息
     */
    @ApiOperation(value = "获得渠道信息详情", httpMethod = "GET", notes = "system:channel:query")
    @RequiresPermissions("system:channel:query")
    @GetMapping(value = "/{channelId}")
    public CommonResult<SysChannelRespVO> getInfo(@PathVariable("channelId") Long channelId) {
        SysChannel sysChannel = sysChannelService.getSysChannel(channelId);
        return success(HutoolBeanUtils.toBean(sysChannel, SysChannelRespVO.class));
    }

    /**
     * 分页查询渠道信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得渠道信息分页列表", httpMethod = "GET", notes = "system:channel:list")
    @RequiresPermissions("system:channel:list")
    public CommonResult<PageResult<SysChannelRespVO>> getPage(@Valid SysChannelPageReqVO pageReqVO) {
        PageResult<SysChannel> pageResult = sysChannelService.getSysChannelPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, SysChannelRespVO.class));
    }

    /**
     * 获取渠道选中数据
     * @param channelIds    渠道ID集合
     * @return  获取渠道选中数据
     */
    @PostMapping("/getSelectedBatchInfo")
    @ApiOperation(value = "批量获取渠道简略信息", httpMethod = "POST")
    public CommonResult<List<SysChannelSelectedRespVO>> getSelectedBatchInfo(@Valid @Size(min = NumberPool.INT_ONE) @RequestBody List<Long> channelIds) {
        return success(sysChannelService.getSelectedSysChannel(channelIds));
    }
}
