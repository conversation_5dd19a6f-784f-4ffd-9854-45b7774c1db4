package com.zksr.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.enums.UserStatus;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.text.Convert;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.BeanValidators;
import com.zksr.common.core.utils.ip.IpUtils;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.service.TokenService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.RemoteUserService;
import com.zksr.system.api.domain.SysRole;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.domain.SysUserImportVO;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.domain.*;
import com.zksr.system.mapper.*;
import com.zksr.system.service.ISysConfigService;
import com.zksr.system.service.ISysUserService;
import com.zksr.system.tenant.SaasHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService
{
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

//    private Cache<String, R<LoginUser>> loginB2bUserCache;
//
//    private Cache<String, ReentrantLock> loginB2bUserlockCache;

    @Autowired
    private SysPartnerMapper sysPartnerMapper;

    @Autowired
    private SysDcMapper sysDcMapper;

    @Autowired
    private SysSupplierMapper sysSupplierMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    protected Validator validator;

    @Value("${b2b.saas.auth.switch:false}")
    private Boolean saasAuthSwitch;

    @Autowired
    private SaasHelper saasHelper;

    @Autowired
    private SysSoftwareMapper sysSoftwareMapper;



    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    //@DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user)
    {
        user.setFuncScop(SecurityUtils.getLoginUser().getFuncScop());
        if (saasAuthSwitch || SystemConstants.FUNC_SCOPE_SOFTWARE.equals(user.getFuncScop())) {
            // 软件商不作数据隔离
            user.setFuncScop(null);
        }
        return userMapper.selectUserList(user);
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
//    @DataScope(deptAlias = "d", userAlias = "u")
    @DataScope(userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user)
    {
        return userMapper.selectAllocatedList(user);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    //@DataScope(deptAlias = "d", userAlias = "u")
    @DataScope(userAlias = "u", supplierAlias = "u", dcFieldAlias = "u", dcAlias = SystemConstants.DC_ID)
    public List<SysUser> selectUnallocatedList(SysUser user)
    {
        return userMapper.selectUnallocatedList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName)
    {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId)
    {
        return userMapper.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName)
    {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list))
        {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName)
    {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list))
        {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user)
    {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkUserNameUnique(user.getUserName());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(SysUser user)
    {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkEmailUnique(SysUser user)
    {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user)
    {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin())
        {
            throw new ServiceException("不允许操作超级管理员用户");
        }
        if (Objects.nonNull(user.getRoleIds()) && !SecurityUtils.isAdmin(user.getUserId())) {
            for (Long roleId : user.getRoleIds()) {
                SysRole sysRole = roleMapper.selectRoleById(roleId);
                if (Objects.isNull(sysRole.getSysCode())) {
                    throw new ServiceException("无法分配内置角色, 角色名称: " + sysRole.getRoleName());
                }
            }
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId)
    {
        if (!SysUser.isAdmin(SecurityUtils.getUserId()))
        {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users))
            {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUser user)
    {
        // 新增用户信息
        int rows = saveUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows;
    }



    @Transactional
    public int saveUser(SysUser user) {
        // 新增用户信息
        createSaasUser(user);
        return userMapper.insertUser(user);
    }

    @Transactional
    public int updateUserV2(SysUser user) {
        //同步saas用户
        saasHelper.updateSaasUser(user.getUserId(), user.getNickName(), user.getStatus());
        return userMapper.updateUser(user);
    }


    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user)
    {
        return saveUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(SysUser user)
    {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);

        return updateUserV2(user);
    }


    /**
     * 用户授权角色
     *
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, Long[] roleIds)
    {
        userRoleMapper.deleteUserRoleByUserId(userId);
        SysUser user = this.selectUserById(userId);
        insertUserRole(userId, user.getSysCode(), roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user)
    {
        saasHelper.updateSaasUser(user.getUserId(), null, user.getStatus());
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user)
    {
        //更新saas系统用户的登陆账号
        updateSaasUserMobile(user);
        return userMapper.updateUser(user);
    }

    /**
     * 更新saas系统用户的登陆账号
     * @param sysUser
     */
    private void updateSaasUserMobile(SysUser sysUser) {
        Long userId = sysUser.getUserId();
        String phoneNumber = sysUser.getPhonenumber();
        String verificationCode = sysUser.getVerificationCode();
        if (saasAuthSwitch && StrUtil.isNotBlank(phoneNumber)) {
            SysUser user = userMapper.selectUserById(userId);
            if(user == null || StrUtil.isBlank(user.getSaasUserCode()) || Objects.equals(user.getPhonenumber(), phoneNumber)) return;
            if(StrUtil.isBlank(verificationCode))  throw new ServiceException("修改用户手机号验证码不可为空！");
            saasHelper.saasUpdateUserMobile(user.getPhonenumber(), phoneNumber, verificationCode);
            sysUser.setUserName(phoneNumber);
            SysSoftware sysSoftware = sysSoftwareMapper.selectOne(SysSoftware::getSoftwareUserId, userId);
            if(sysSoftware != null){
                sysSoftware.setContactPhone(phoneNumber);
                sysSoftwareMapper.updateById(sysSoftware);
            }

            SysPartner sysPartner = sysPartnerMapper.selectOne(SysPartner::getPartnerUserId, userId);
            if(sysPartner != null){
                sysPartner.setPartnerAccount(phoneNumber);
                sysPartner.setContactPhone(phoneNumber);
                sysPartnerMapper.updateById(sysPartner);
                return;
            }
            SysDc sysDc = sysDcMapper.selectOne(SysDc::getUserId, userId);
            if(sysDc != null){
                sysDc.setContractPhone(phoneNumber);
                sysDcMapper.updateById(sysDc);
                return;
            }
            SysSupplier sysSupplier = sysSupplierMapper.selectOne(SysSupplier::getUserId, userId);
            if(sysDc != null){
                sysSupplier.setContactPhone(phoneNumber);
                sysSupplierMapper.updateById(sysSupplier);
            }
        }
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar)
    {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user)
    {
        return updateUserV2(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password)
    {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    @Override
    public void insertUserRole(SysUser user)
    {
        this.insertUserRole(user.getUserId(), user.getSysCode(), user.getRoleIds());
    }

    @Override
    public String getBySysUserId(Long userId) {
        return userMapper.getBySysUserId(userId);
    }

    @Override
    public void changePass(Long sysUserId, String password) {
        if (StringUtils.isEmpty(password)) {
            return;
        }
        SysUser user = new SysUser();
        user.setPassword(saasAuthSwitch ? "" : SecurityUtils.encryptPassword(password));
        user.setUserId(sysUserId);
        updateUserV2(user);
    }

    @Override
    public SysUser getById(Long userId) {
        return userMapper.selectById(userId);
    }

    @Override
    public SysUser getUserByPhone(String phone) {
        return userMapper.selectByPhone(phone);
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user)
    {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts))
        {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>();
            for (Long postId : posts)
            {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long sysCode, Long[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            Set<String> roleFuncScope = new HashSet<>();

            for (Long roleId : roleIds) {
                SysRole role = roleMapper.selectRoleById(roleId);
                if (ToolUtil.isNotEmpty(role) && ToolUtil.isNotEmpty(role.getFuncScop())) {
                    roleFuncScope.add(role.getFuncScop());
                }
            }

            if(ToolUtil.isNotEmpty(roleFuncScope) && roleFuncScope.size() > 1){
                throw new ServiceException("用户不能被赋予多个funcScop");
            }

            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                ur.setSysCode(sysCode);
                list.add(ur);
            }
            userRoleMapper.batchUserRole(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId)
    {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds)
    {
        for (Long userId : userIds)
        {
            checkUserAllowed(new SysUser(userId));
            //checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        // userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        // userPostMapper.deleteUserPost(userIds);
        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userImportList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUserImportVO> userImportList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(userImportList) || userImportList.size() == 0)
        {
            throw new ServiceException("导入用户数据不能为空！");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        List<SysUser> userList = BeanUtils.toBean(userImportList, SysUser.class);
        for (int i = 0; i < userList.size(); i++) {
            SysUser user = userList.get(i);
            if (!saasAuthSwitch && StringUtils.isEmpty(user.getPassword())) {
                failureNum++;
                failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 密码不存在");
                continue;
            }
            if (StringUtils.isEmpty(user.getUserName())) {
                failureNum++;
                failureMsg.append("<br/>" + failureNum + "、第 " + i + 2 + " 行, 账号不存在");
                continue;
            }
            try
            {
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                //校验手机号码和登录名称是否相同, 手机号码是否更改
                checkPhoneAndUserName(user, u);
                if (StringUtils.isNull(u))
                {
                    BeanValidators.validateWithException(validator, user);
                    user.setPassword(saasAuthSwitch ? "" :SecurityUtils.encryptPassword(user.getPassword()));
                    user.setCreateBy(operName);
                    user.setSupplierId(SecurityUtils.getSupplierId());
                    user.setDcId(loginUser.getDcId());
                    user.setSex(StringPool.ZERO);
                    user.setSysCode(loginUser.getSysCode());
                    user.setStatus(StringPool.ZERO);
                    saveUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(u);
                    checkUserDataScope(u.getUserId());
                    user.setUserId(u.getUserId());
                    user.setUpdateBy(operName);
                    updateUserV2(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！");
        }
        return successMsg.toString();
    }

    private void checkPhoneAndUserName(SysUser user, SysUser dbUser){
        if(!saasAuthSwitch) return;
        if(StrUtil.isNotBlank(user.getUserName()) && StrUtil.isNotBlank(user.getPhonenumber()) && !Objects.equals(user.getUserName(),user.getPhonenumber())){
            throw new ServiceException("登录名称和手机号码必须相同");
        }
        if(dbUser!=null){
            String oldPhone = dbUser.getPhonenumber();
            if( (StrUtil.isNotBlank(user.getUserName()) && !Objects.equals(oldPhone, user.getUserName())) ||  (StrUtil.isNotBlank(user.getPhonenumber()) && !Objects.equals(oldPhone, user.getPhonenumber())) ){
                throw new ServiceException("登录名称和手机号码不可修改，请到个人中心修改登录账号");
            }
        }
    }

    @Override
    public R<LoginUser> userInfoForGateway(String userCode, String tenantCode, String b2bUserCacheKey) {
        log.info(String.format("/inner/userInfoForGateway接口, key=%s", b2bUserCacheKey));
        try {
            R<LoginUser> ok = getLoginUserBySaasUserCode(userCode,tenantCode);
            return ok;
        } catch (Exception e) {
            log.error("/inner/userInfoForGateway接口报错，报错原因：",e);
            return R.fail(e.getMessage());
        }
    }

    public List<SysPartner> getSysPartnerBySaasTenantCode(String saasTenantCode) {
        return sysPartnerMapper.selectList(new LambdaQueryWrapper<SysPartner>().eq(SysPartner::getSaasTenantCode,saasTenantCode));
    }


    public R<LoginUser> getLoginUserBySaasUserCode(String saasUserCode, String tenantCode){
        // 根据saas用户编码查询
        SysUser user = getUserBySaasUserCode(saasUserCode);
        // 不存在
        if (ObjectUtil.isNull(user)){
            throw new ServiceException(String.format("根据saas用户编码【%s】查询用户不存在",saasUserCode));
        }
        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        // 查询用户信息
        String username = user.getUserName();
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            throw new ServiceException("很遗憾，访问IP已被列入系统黑名单");
        }
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);

        if (R.FAIL == userResult.getCode())
        {
            throw new ServiceException(userResult.getMsg());
        }
        LoginUser loginUser = tokenService.createAndRefreshTokenBySaasLogin(userResult.getData());
        return R.ok(loginUser);
    }


    @Override
    public SysUser getUserBySaasUserCode(String saasUserCode) {
        return userMapper.selectBySaasUserCode(saasUserCode);
    }


    /**
     * 创建saas用户
     * @param sysUser
     * @return
     */
    @Override
    public SysUser createSaasUser(SysUser sysUser) {
        if(sysUser != null && saasAuthSwitch){
            saasHelper.saasAuthUserAdd(sysUser.getPhonenumber(), sysUser.getNickName(), saasHelper.getTenantCode(sysUser.getSysCode()), sysUser);
        }
        return sysUser;
    }

    @Override
    public List<SysUser> selectByDcId(Long dcId) {
        return userMapper.selectByDcId(dcId);
    }

//    /**
//     * 修改saas用户密码
//     * @param req
//     */
//    @Override
//    public void updateSaasUserPwd(PasswordForget req) {
//        saasHelper.saasForgetAndReset(req.getMobile(), req.getVerificationCode(), req.getNewPassword());
//    }

//    private ReentrantLock getLock(String key) {
//        ReentrantLock lock = loginB2bUserlockCache.getIfPresent(key);
//        if (Objects.nonNull(lock)) {
//            return lock;
//        }
//        ReentrantLock newLock = new ReentrantLock();
//        loginB2bUserlockCache.put(key, newLock);
//        return newLock;
//    }


//    @Override
//    public void afterPropertiesSet() throws Exception {
//        loginB2bUserCache = CacheBuilder.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS).maximumSize(8_000L).build();
//        loginB2bUserlockCache = CacheBuilder.newBuilder().expireAfterAccess(10, TimeUnit.HOURS).maximumSize(1_000L).build();
//    }
}
