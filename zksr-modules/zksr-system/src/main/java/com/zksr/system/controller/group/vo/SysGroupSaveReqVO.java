package com.zksr.system.controller.group.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 平台商城市分组对象 sys_group
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@Data
@ApiModel("平台商城市分组 - sys_group分页 Request VO")
public class SysGroupSaveReqVO {
    private static final long serialVersionUID = 1L;

    /**
     * 平台商城市分组id
     */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id", example = "示例值")
    private Long groupId;

    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", example = "示例值")
    private Long sysCode;

    /**
     * 平台商城市分组名
     */
    @Excel(name = "平台商城市分组名")
    @ApiModelProperty(value = "平台商城市分组名", example = "示例值")
    @Length(message = "城市分组名超出输入长度", max = 32)
    private String groupName;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "示例值")
    private String memo;


}
