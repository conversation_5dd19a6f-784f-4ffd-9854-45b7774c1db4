package com.zksr.system.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysOpensource;
import com.zksr.system.controller.opensource.vo.SysOpensourcePageReqVO;

import java.util.List;


/**
 * 开放能力Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Mapper
public interface SysOpensourceMapper extends BaseMapperX<SysOpensource> {
    default PageResult<SysOpensource> selectPage(SysOpensourcePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysOpensource>()
                .eqIfPresent(SysOpensource::getOpensourceId, reqVO.getOpensourceId())
                .eqIfPresent(SysOpensource::getSysCode, reqVO.getSysCode())
                .eqIfPresent(SysOpensource::getMerchantId, reqVO.getMerchantId())
                .eqIfPresent(SysOpensource::getMerchantType, reqVO.getMerchantType())
                .eqIfPresent(SysOpensource::getSourceKey, reqVO.getSourceKey())
                .eqIfPresent(SysOpensource::getSourceSecret, reqVO.getSourceSecret())
                .eqIfPresent(SysOpensource::getIpWhiteList, reqVO.getIpWhiteList())
                .orderByDesc(SysOpensource::getOpensourceId));
    }

    default SysOpensource getInfoByOpensouceIdAndSourceSecret(String opensourceId, String sourceSecret){
        return selectOne(SysOpensource::getOpensourceId, opensourceId, SysOpensource::getSourceSecret, sourceSecret);
    }

    default SysOpensource getInfoBySouceKeyAndSourceSecret(String sourceKey, String sourceSecret){
        return selectOne(SysOpensource::getSourceKey, sourceKey, SysOpensource::getSourceSecret, sourceSecret);
    }

    default SysOpensource getAccountByMerchantIdAndTypeByDefPlatform(Long merchantId, String merchantType) {
        return selectOne(new LambdaQueryWrapperX<SysOpensource>().eqIfPresent(SysOpensource::getMerchantId, merchantId).likeIfPresent(SysOpensource::getMerchantType, merchantType));
    }

    default SysOpensource getOpensourceByMerchantId(Long merchantId) {
        return selectOne(SysOpensource::getMerchantId, merchantId);
    }

    default List<SysOpensource> getOpensourceListByVisualMasterId(Long visualMasterId) {
        return selectList(new LambdaQueryWrapperX<SysOpensource>()
                .eq(SysOpensource::getVisualMasterId,visualMasterId));
    }

    default List<SysOpensource> getOpenSourceBySysCode(Long sysCode){
        return selectList(new LambdaQueryWrapperX<SysOpensource>()
                .eqIfPresent(SysOpensource::getSysCode,sysCode));
    }
}
