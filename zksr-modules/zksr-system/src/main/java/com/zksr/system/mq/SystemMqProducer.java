package com.zksr.system.mq;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.system.api.commonMessage.vo.SubscribeEventBodyVO;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.domain.SysSms;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.support.MessageBuilder;

/**
 * @Description: 猎鹰服务MQ
 * @Author: liuxingyu
 * @Date: 2024/3/28 9:31
 */
@Slf4j
@Configuration
public class SystemMqProducer {

    @Autowired
    private StreamBridge streamBridge;


//    /**
//     * @Description: 新增电子围栏(生产者)
//     * @Author: liuxingyu
//     * @Date: 2024/3/28 9:30
//     */
//    public void sendAddGeofence(SupplierDTO supplierDTO) {
//        log.info("发送新增电子围栏消息：{} ", supplierDTO);
//        streamBridge.send(MessageConstant.SYSTEM_GEOFENCE_ADD_TOPIC_OUT_PUT, MessageBuilder.withPayload(supplierDTO).build());
//    }
//
//
//    /**
//     * @Description: 修改电子围栏(生产者)
//     * @Author: liuxingyu
//     * @Date: 2024/3/28 9:30
//     */
//    public void sendUpdateGeofence(SupplierDTO supplierDTO){
//        log.info("发送修改电子围栏消息：{} ", supplierDTO);
//        streamBridge.send(MessageConstant.SYSTEM_GEOFENCE_UPDATE_TOPIC_OUT_PUT, MessageBuilder.withPayload(supplierDTO).build());
//    }
//
//    /**
//    * @Description: 新建服务(生产者)
//    * @Author: liuxingyu
//    * @Date: 2024/4/7 14:41
//    */
//    public void addService(SysPartner sysPartner){
//        log.info("发送新建服务消息：{} ", sysPartner);
//        streamBridge.send(MessageConstant.SYSTEM_SERVICE_ADD_OUT_PUT, MessageBuilder.withPayload(sysPartner).build());
//    }

    /**
     * 发送短信信息
     * 消费 {@linkplain SystemMqConsumer#smsEvent()}
     * @param sms   短信
     */
    public void sendSms(SysSms sms){
        log.info("发送短信信息：{} ", sms);
        streamBridge.send(MessageConstant.SMS_TOPIC_OUT_PUT, MessageBuilder.withPayload(sms).build());
    }

    /**
     * 发送公众号小程序订阅消息
     * 消费 {@link SystemMqConsumer#subscribeMessageEvent()}
     * @param subscribeEventBodyVO  消息事件
     */
    public void sendSubscribe(SubscribeEventBodyVO<Object> subscribeEventBodyVO){
        log.info("发送公众号/小程序订阅消息通知事件：{} ", subscribeEventBodyVO);
        streamBridge.send(
                MessageConstant.SUBSCRIBE_MESSAGE_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(subscribeEventBodyVO)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, NumberPool.INT_FOUR) // 延迟30秒
                .build()
        );
    }

    /**
     * 发送文件导入消息信息
     * 消费 {@link SystemMqConsumer#fileImportTaskEvent()}
     * @param sysFileImport
     */
    public void sendFileImportTaskEvent(SysFileImportVO sysFileImport){
        log.info("发送文件导入消息信息：{} ", sysFileImport);
        streamBridge.send(MessageConstant.FILE_IMPORT_TASK_TOPIC_OUT_PUT,MessageBuilder.withPayload(sysFileImport).build());

    }
}
