package com.zksr.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.controller.openability.vo.SysOpenabilityPageReqVO;
import com.zksr.system.controller.openability.vo.SysOpenabilityReqVO;
import com.zksr.system.controller.openability.vo.SysOpenabilitySaveReqVO;
import com.zksr.system.convert.openability.SysOpenabilityConvert;
import com.zksr.system.domain.SysOpenability;
import com.zksr.system.mapper.SysOpenabilityMapper;
import com.zksr.system.service.ISysOpenabilityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.pool.StringPool.LIMIT_ONE;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 开放能力Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
@Service
public class SysOpenabilityServiceImpl implements ISysOpenabilityService {
    @Autowired
    private SysOpenabilityMapper sysOpenabilityMapper;

    /**
     * 新增开放能力
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysOpenability(SysOpenabilitySaveReqVO createReqVO) {
        if (checkAbilityNameUnique(createReqVO.getOpenabilityId(), createReqVO.getAbilityName())) {
            throw exception(SYS_OPENABILITY_NAME);
        }
        if (checkAbilityKeyUnique(createReqVO.getOpenabilityId(), createReqVO.getAbilityName())) {
            throw exception(SYS_OPENABILITY_KEY);
        }
        // 插入
        SysOpenability sysOpenability = SysOpenabilityConvert.INSTANCE.convert(createReqVO);
        sysOpenabilityMapper.insert(sysOpenability);
        // 返回
        return sysOpenability.getOpenabilityId();
    }

    /**
     * 修改开放能力
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysOpenability(SysOpenabilitySaveReqVO updateReqVO) {
        sysOpenabilityMapper.updateById(SysOpenabilityConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除开放能力
     *
     * @param openabilityId 开放能力id
     */
    @Override
    public void deleteSysOpenability(Long openabilityId) {
        int result = sysOpenabilityMapper.hasChildByOpenabilityId(openabilityId);
        if (result > 0) {
            throw exception(SYS_OPENABILITY_HAS_CHILD);
        }
        // 删除
        sysOpenabilityMapper.deleteById(openabilityId);
    }

    /**
     * 批量删除开放能力
     *
     * @param openabilityIds 需要删除的开放能力主键
     * @return 结果
     */
    @Override
    public void deleteSysOpenabilityByOpenabilityIds(Long[] openabilityIds) {
        for (Long openabilityId : openabilityIds) {
            this.deleteSysOpenability(openabilityId);
        }
    }

    /**
     * 获得开放能力
     *
     * @param openabilityId 开放能力id
     * @return 开放能力
     */
    @Override
    public SysOpenability getSysOpenability(Long openabilityId) {
        return sysOpenabilityMapper.selectById(openabilityId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<SysOpenability> getSysOpenabilityPage(SysOpenabilityPageReqVO pageReqVO) {
        return sysOpenabilityMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SysOpenability> getSysOpenabilityList(SysOpenabilityReqVO pageReqVO) {
        return sysOpenabilityMapper.selectList(pageReqVO);
    }

    /**
     * 校验名称是否唯一
     *
     * @param openabilityId 开放能力id
     * @param abilityName   接口名
     * @return 数据对象
     */
    public boolean checkAbilityNameUnique(Long openabilityId, String abilityName) {
        openabilityId = ObjectUtil.isNull(openabilityId) ? -1 : openabilityId;
        SysOpenability sysOpenability = sysOpenabilityMapper.selectOne(Wrappers.<SysOpenability>query().lambda().ne(SysOpenability::getOpenabilityId, openabilityId).eq(SysOpenability::getAbilityName, abilityName).last(LIMIT_ONE));
        return ObjectUtil.isNotNull(sysOpenability);
    }

    /**
     * 校验接口key是否唯一
     *
     * @param openabilityId 开放能力id
     * @param abilityKey    接口key
     * @return 结果 | true/false 唯一/不唯一
     */
    public boolean checkAbilityKeyUnique(Long openabilityId, String abilityKey) {
        openabilityId = ObjectUtil.isNull(openabilityId) ? -1 : openabilityId;
        SysOpenability sysOpenability = sysOpenabilityMapper.selectOne(Wrappers.<SysOpenability>query().lambda().ne(SysOpenability::getOpenabilityId, openabilityId).eq(SysOpenability::getAbilityKey, abilityKey).last(LIMIT_ONE));
        return ObjectUtil.isNotNull(sysOpenability);
    }

    //    private void validateSysOpenabilityExists(Long openabilityId) {
//        if (sysOpenabilityMapper.selectById(openabilityId) == null) {
//            throw exception(SYS_OPENABILITY_NOT_EXISTS);
//        }
//    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.system.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 开放能力 TODO 补充编号 ==========
    // ErrorCode SYS_OPENABILITY_NOT_EXISTS = new ErrorCode(TODO 补充编号, "开放能力不存在");


}
