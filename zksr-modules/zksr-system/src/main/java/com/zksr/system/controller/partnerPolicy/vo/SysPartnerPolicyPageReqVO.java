package com.zksr.system.controller.partnerPolicy.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import java.util.List;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 平台商政策(由平台商设置)对象 sys_partner_policy
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@ApiModel("平台商政策(由平台商设置) - sys_partner_policy分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysPartnerPolicyPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商政策id */
    @ApiModelProperty(value = "配置类型")
    private Long partnerPolicyId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 配置名 */
    @Excel(name = "配置名")
    @ApiModelProperty(value = "配置名")
    private String policyName;

    /** 配置键名 */
    @Excel(name = "配置键名")
    @ApiModelProperty(value = "配置键名")
    private String policyKey;

    /** 配置值 */
    @Excel(name = "配置值")
    @ApiModelProperty(value = "配置值")
    private String policyValue;

    /** 配置类型（数据字典：sys_partner_policy_type） */
    @Excel(name = "配置类型", readConverterExp = "数=据字典：sys_partner_policy_type")
    @ApiModelProperty(value = "配置类型")
    private String policyType;

    /** 区域城市ID */
    @Excel(name = "区域城市ID")
    @ApiModelProperty(value = "区域城市ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 区域城市ID集合 */
    @ApiModelProperty(value = "区域城市ID集合")
    private List<Long> areaIds;



}
