package com.zksr.system.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import com.zksr.system.controller.dcArea.vo.SysDcAreaRespVO;
import com.zksr.system.domain.SysDc;
import com.zksr.system.mapper.SysDcMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.system.mapper.SysDcAreaMapper;
import com.zksr.system.domain.SysDcArea;
import com.zksr.system.controller.dcArea.vo.SysDcAreaPageReqVO;
import com.zksr.system.controller.dcArea.vo.SysDcAreaSaveReqVO;
import com.zksr.system.service.ISysDcAreaService;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 运营商-区域城市关联关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Service
public class SysDcAreaServiceImpl implements ISysDcAreaService {

    @Autowired
    private SysDcMapper sysDcMapper;

    @Autowired
    private RedisService redisService;

    @PostConstruct
    public void init(){
        List<SysDc> sysDcs = sysDcMapper.selectList(null);
        sysDcs.forEach(item -> reloadCache(item.getDcId()));
    }

    @Autowired
    private SysDcAreaMapper sysDcAreaMapper;


    /**
     * 新增运营商-区域城市关联关系
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysDcArea(SysDcAreaSaveReqVO createReqVO) {
        // 插入
        SysDcArea sysDcArea = HutoolBeanUtils.toBean(createReqVO, SysDcArea.class);
        sysDcAreaMapper.insert(sysDcArea);
        // 返回
        return sysDcArea.getDcId();
    }

    /**
     * 修改运营商-区域城市关联关系
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysDcArea(SysDcAreaSaveReqVO updateReqVO) {
        sysDcAreaMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, SysDcArea.class));
    }

    /**
     * 删除运营商-区域城市关联关系
     *
     * @param dcId 运营商id;运营商id
     */
    @Override
    public void deleteSysDcArea(Long dcId) {
        // 删除
        sysDcAreaMapper.deleteById(dcId);
    }

    /**
     * 批量删除运营商-区域城市关联关系
     *
     * @param dcIds 需要删除的运营商-区域城市关联关系主键
     * @return 结果
     */
    @Override
    public void deleteSysDcAreaByDcIds(Long[] dcIds) {
        for(Long dcId : dcIds){
            this.deleteSysDcArea(dcId);
        }
    }

    /**
     * 获得运营商-区域城市关联关系
     *
     * @param dcId 运营商id;运营商id
     * @return 运营商-区域城市关联关系
     */
    @Override
    public SysDcArea getSysDcArea(Long dcId) {
        return sysDcAreaMapper.selectById(dcId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysDcArea> getSysDcAreaPage(SysDcAreaPageReqVO pageReqVO) {
        return sysDcAreaMapper.selectPage(pageReqVO);
    }

    @Override
    public DcAreaGroupDTO reloadCache(Long dcId) {
        if (Objects.nonNull(dcId)) {
            DcAreaGroupDTO groupDTO = new DcAreaGroupDTO(dcId, sysDcAreaMapper.selectByDcId(dcId));
            redisService.setCacheObject(RedisConstants.DC_AREA_GROUP + dcId, groupDTO);
            return groupDTO;
        }
        return null;
    }

    @Override
    public DcAreaGroupDTO getDcAreaGroup(Long dcId) {
        DcAreaGroupDTO groupDTO = new DcAreaGroupDTO(dcId, sysDcAreaMapper.selectByDcId(dcId));
        return groupDTO;
    }

    @Override
    public SysDcArea getDcAreaByDcIdOrAreaId(Long dcId, Long areaId, Long sysCode) {
        return sysDcAreaMapper.getDcArea(new SysDcAreaRespVO(dcId,areaId,sysCode));
    }
}
