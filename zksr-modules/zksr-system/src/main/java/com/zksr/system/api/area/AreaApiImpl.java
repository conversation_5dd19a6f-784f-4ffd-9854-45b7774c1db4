package com.zksr.system.api.area;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.domain.SysArea;
import com.zksr.system.service.ISysAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class AreaApiImpl implements AreaApi{
    @Autowired
    private ISysAreaService sysAreaService;

    @InnerAuth
    @Override
    public CommonResult<AreaDTO> getAreaByAreaId(Long areaId) {
        SysArea sysArea = sysAreaService.getSysArea(areaId);
        return CommonResult.success(HutoolBeanUtils.toBean(sysArea, AreaDTO.class));
    }

    @Override
    @InnerAuth
    public CommonResult<Map<Long, AreaDTO>> getAreaMapBySysCode(@RequestParam(value = "sysCode",required = false) Long sysCode, @RequestParam(value =  "dcId",required = false) Long dcId) {
        Map<Long, AreaDTO> areaMap = new HashMap<>();

        List<SysArea> sysAreaList = sysAreaService.getAreaListBySysCode(sysCode);
        if(ToolUtil.isNotEmpty(sysAreaList)){
            List<AreaDTO> areaDTOS = HutoolBeanUtils.toBean(sysAreaList, AreaDTO.class);
            areaMap = areaDTOS.stream().collect(Collectors.toMap(AreaDTO::getAreaId, x->x));
        }
        return  CommonResult.success(areaMap);
    }

    /**
    * @Description: 根据sysCode获取区域城市
    * @Author: liuxingyu
    * @Date: 2024/3/26 9:10
    */
    @Override
    @InnerAuth
    public CommonResult<List<AreaDTO>> getListBySysCode(Long sysCode) {
        return CommonResult.success(HutoolBeanUtils.toBean(sysAreaService.getAreaListBySysCode(sysCode),AreaDTO.class));
    }

    /**
    * @Description: 根据入驻商获取区域城市
    * @Author: liuxingyu
    * @Date: 2024/4/18 9:36
    */
    @InnerAuth
    @Override
    public CommonResult<List<AreaDTO>> getBySupplierId(Long supplierId) {
        return CommonResult.success(HutoolBeanUtils.toBean(sysAreaService.getBySupplierId(supplierId),AreaDTO.class));
    }

    @InnerAuth
    @Override
    public CommonResult<AreaDTO> getDefaultBySyscode(Long sysCode) {
        SysArea sysArea = sysAreaService.getDefaultBySyscode(sysCode);
        return CommonResult.success(HutoolBeanUtils.toBean(sysArea, AreaDTO.class));
    }

    @Override
    public CommonResult<Boolean> checkAreaByAreaId(@RequestParam("areaId") Long areaId) {
        return CommonResult.success(sysAreaService.checkAreaByAreaId(areaId));
    }

    @Override
    public CommonResult<List<AreaDTO>> getAreaBySyscodeAndDcId(Long sysCode, Long dcId) {
        return CommonResult.success(sysAreaService.getAreaBySyscodeAndDcId(sysCode,dcId));
    }

    @Override
    public CommonResult<List<AreaDTO>> getAreaListByDcId(Long dcId) {
        return CommonResult.success(HutoolBeanUtils.toBean(sysAreaService.getAreaListByDcId(dcId), AreaDTO.class));
    }
}
