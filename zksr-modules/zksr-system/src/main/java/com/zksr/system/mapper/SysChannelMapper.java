package com.zksr.system.mapper;

import com.alibaba.nacos.common.http.param.Query;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.controller.area.vo.SysAreaPageReqVO;
import com.zksr.system.controller.area.vo.SysAreaRespVO;
import com.zksr.system.controller.channel.vo.SysChannelSaveReqVO;
import com.zksr.system.controller.channel.vo.SysChannelSelectedRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysChannel;
import com.zksr.system.controller.channel.vo.SysChannelPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 渠道信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@Mapper
@SuppressWarnings("all")
public interface SysChannelMapper extends BaseMapperX<SysChannel> {
    Page<SysChannel> selectPage(@Param("page") Page<SysAreaPageReqVO> page,
                                       @Param("param") SysChannelPageReqVO pageReqVO,
                                       @Param("sysCode") Long sysCode);
    default List<SysChannel> selectSelectedSysChannel(List<Long> channelIds) {
        return selectList(new LambdaQueryWrapperX<SysChannel>()
                .in(SysChannel::getChannelId, channelIds)
                .select(SysChannel::getChannelId, SysChannel::getChannelName)
        );
    }

    void updateDeleted(@Param("param") SysChannel param);

    default List<SysChannel> getChannelListBySysCode(Long sysCode, String channelName) {
        return selectList(new LambdaQueryWrapperX<SysChannel>()
                .eqIfPresent(SysChannel::getSysCode, sysCode)
                .likeIfPresent(SysChannel::getChannelName, channelName)
                .eqIfPresent(SysChannel::getStatus, NumberPool.INT_ONE)
        );
    }

    default long selectExists(SysChannelSaveReqVO saveReqVO) {
        return selectCount(
                new LambdaQueryWrapperX<SysChannel>()
                        .neIfPresent(SysChannel::getChannelId, saveReqVO.getChannelId())
                        .eq(SysChannel::getChannelName, saveReqVO.getChannelName())
        );
    }
}
