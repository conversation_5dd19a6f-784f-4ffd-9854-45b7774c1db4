package com.zksr.system.controller.supplier.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.system.api.supplier.vo.SysSupplierPageReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 入驻商信息对象 sys_supplier
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@ApiModel("入驻商信息 - sys_supplier account 分页")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysSupplierAccountPageReqVO extends SysSupplierPageReqVO {
    private static final long serialVersionUID = 1L;

    /** 可提现金额 */
    @ApiModelProperty(value = "可提现金额")
    private BigDecimal withdrawableAmt = BigDecimal.ZERO;

    /** 冻结金额 */
    @ApiModelProperty(value = "冻结金额")
    private BigDecimal frozenAmt = BigDecimal.ZERO;

    /** 授信额度 */
    @ApiModelProperty(value = "授信额度")
    private BigDecimal creditAmt = BigDecimal.ZERO;

    /** 账户ID */
    @ApiModelProperty(value = "账户ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long accountId;

    /** 支付平台 */
    @ApiModelProperty(value = "支付平台")
    private String payPlatform;
}
