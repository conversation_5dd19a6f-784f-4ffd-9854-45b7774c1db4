package com.zksr.system.chin.general;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.system.domain.SysPartnerConfig;
import com.zksr.system.domain.SysPartnerPolicy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
@Slf4j
public class PartnerConfigUtil {

    /**
     * @Description: 拼接参数（obj 成员变量只能为String）
     * @Param: Long sysCode 平台商ID,String key 配置唯一名称,Object obj 配置类对象
     * @return: List<SysPartnerConfig>
     * @Author: liuxingyu
     * @Date: 2024/3/12 15:54
     */
    public static List<SysPartnerConfig> jointConfigParam(Long sysCode, String key, Object obj) {
        HashMap<String, String> map = BeanUtil.copyProperties(obj, HashMap.class);
        //提出公共参数
        String configName = map.get("configName");
        String configType = map.get("configType");

        //返回参数集合
        List<SysPartnerConfig> sysPartnerList = new ArrayList<>();
        map.forEach((k, v) -> {
            if (ObjectUtil.notEqual(k, "configName") && ObjectUtil.notEqual(k, "configType")) {
                SysPartnerConfig sysPartnerConfig = new SysPartnerConfig();
                sysPartnerConfig.setConfigKey(key.concat(k));
                sysPartnerConfig.setConfigValue(v);
                sysPartnerConfig.setSysCode(sysCode);
                sysPartnerConfig.setConfigName(configName);
                sysPartnerConfig.setConfigType(configType);
                sysPartnerList.add(sysPartnerConfig);
            }
        });
        return sysPartnerList;
    }

    /**
     * @Description: 拼接参数（obj 成员变量只能为String）
     * @Param: Long sysCode 平台商ID,String key 配置唯一名称,Object obj 配置类对象
     * @return: List<SysPartnerConfig>
     * @Author: liuxingyu
     * @Date: 2024/3/12 15:54
     */
    public static List<SysPartnerPolicy> jointPolicyParam(Long sysCode, Long dcId, Long supplierId, String key, Object obj) {
        HashMap<String, String> map = BeanUtil.copyProperties(obj, HashMap.class);
        //提出公共参数
        String configName = map.get("configName");
        String configType = map.get("configType");
        //返回参数集合
        List<SysPartnerPolicy> sysPartnerList = new ArrayList<>();
        map.forEach((k, v) -> {
            if (ObjectUtil.notEqual(k, "configName") && ObjectUtil.notEqual(k, "configType") && ObjectUtil.notEqual(k, "dcId") && ObjectUtil.notEqual(k, "supplierId")) {
                SysPartnerPolicy sysPartnerPolicy = new SysPartnerPolicy();
                sysPartnerPolicy.setPolicyKey(key.concat(k));
                sysPartnerPolicy.setPolicyValue(v);
                sysPartnerPolicy.setSysCode(sysCode);
                sysPartnerPolicy.setPolicyName(configName);
                sysPartnerPolicy.setPolicyType(configType);
                if (ObjectUtil.isNotNull(dcId)) {
                    sysPartnerPolicy.setDcId(dcId);
                }
                if (ObjectUtil.isNotNull(supplierId)) {
                    sysPartnerPolicy.setSupplierId(supplierId);
                }
                sysPartnerList.add(sysPartnerPolicy);
            }
        });
        return sysPartnerList;
    }

    /**
     * @Description: 通过key匹配所有配置信息
     * @Param: List<SysPartnerConfig> configList, String key
     * @return: List<SysPartnerConfig>
     * @Author: liuxingyu
     * @Date: 2024/3/13 16:20
     */
    public static List<SysPartnerConfig> matchingConfigKey(List<SysPartnerConfig> configList, String key) {
        return configList.stream().filter(x -> StringUtils.contains(x.getConfigKey(), key)).collect(Collectors.toList());
    }

    /**
     * @Description: 通过key匹配所有配置信息
     * @Param: List<SysPartnerConfig> configList, String key
     * @return: List<SysPartnerConfig>
     * @Author: liuxingyu
     * @Date: 2024/3/13 16:20
     */
    public static List<SysPartnerPolicy> matchingPolicyKey(List<SysPartnerPolicy> configList, String key) {
        return configList.stream().filter(x -> StringUtils.contains(x.getPolicyKey(), key)).collect(Collectors.toList());
    }

    /**
     * @Description: 通过数据库数据封装实体
     * @Param: List<SysPartnerConfig> configList 数据库配置信息, String key 配置Key, Class<T> tClass
     * @return: <T> T
     * @Author: liuxingyu
     * @Date: 2024/3/13 16:25
     */
    public static <T> T encapsulationConfigObj(List<SysPartnerConfig> configList, String key, Class<T> tClass) {
        T t = null;
        try {
            //实例化对象后转换成map
            t = tClass.newInstance();
            HashMap<String, Object> map = BeanUtil.copyProperties(t, HashMap.class);
            //遍历数据填充value
            map.forEach((k, v) -> {
                for (SysPartnerConfig config : configList) {
                    if (StringUtils.isBlank(config.getConfigKey()) || ObjectUtil.isEmpty(config.getConfigKey().split(key))) {
                        continue;
                    }
                    String[] split = config.getConfigKey().split(key);
                    if (ObjectUtil.equal(split[1], k)) {
                        map.put(k, config.getConfigValue());
                        break;
                    }
                }
            });
            map.put("configName", configList.get(0).getConfigName());
            map.put("configType", configList.get(0).getConfigType());
            BeanUtil.copyProperties(map, t);
        } catch (Exception e) {
            log.error(" encapsulationConfigObj异常,", e);
            throw new RuntimeException(e);
        }
        return t;
    }

    /**
     * @Description: 通过数据库数据封装实体
     * @Param: List<SysPartnerConfig> configList 数据库配置信息, String key 配置Key, Class<T> tClass
     * @return: <T> T
     * @Author: liuxingyu
     * @Date: 2024/3/13 16:25
     */
    public static <T> T encapsulationPolicyObj(List<SysPartnerPolicy> configList, String key, Class<T> tClass) {
        T t = null;
        try {
            //实例化对象后转换成map
            t = tClass.newInstance();
            HashMap<String, Object> map = BeanUtil.copyProperties(t, HashMap.class);
            //遍历数据填充value
            map.forEach((k, v) -> {
                for (SysPartnerPolicy policy : configList) {
                    if (StringUtils.isBlank(policy.getPolicyKey()) || ObjectUtil.isEmpty(policy.getPolicyKey().split(key))) {
                        continue;
                    }
                    String[] split = policy.getPolicyKey().split(key);
                    if (ObjectUtil.equal(split[1], k)) {
                        map.put(k, policy.getPolicyValue());
                        break;
                    }
                }
            });
            map.put("configName", configList.get(0).getPolicyName());
            map.put("configType", configList.get(0).getPolicyType());
            BeanUtil.copyProperties(map, t);
        } catch (Exception e) {
            log.error(" encapsulationPolicyObj异常,", e);
            throw new RuntimeException(e);
        }
        return t;
    }
}
