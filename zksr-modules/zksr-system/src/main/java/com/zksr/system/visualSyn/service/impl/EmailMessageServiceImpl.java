package com.zksr.system.visualSyn.service.impl;

import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.visualSyn.config.EmailMessageSendConfig;
import com.zksr.system.api.EmailMessage.dto.SyncEmailData;
import com.zksr.system.api.EmailMessage.dto.SyncEmailReportData;
import com.zksr.system.visualSyn.handler.EmailAssembleTemplate;
import com.zksr.system.visualSyn.service.IEmailMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.UrlResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.stereotype.Service;
import org.springframework.core.io.FileSystemResource;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;

import java.util.List;
import java.util.stream.Collectors;

import static cn.hutool.core.io.FileUtil.newFile;
import static org.jacoco.agent.rt.internal_035b120.core.runtime.AgentOptions.OutputMode.file;

/**
* 发送邮箱 实现类
* @date 2025/2/16 18:59
* <AUTHOR>
*/
@Service
@Slf4j
public class EmailMessageServiceImpl implements IEmailMessageService {


    @Resource
    private JavaMailSender mailSender;

    @Autowired
    private EmailMessageSendConfig emailMessageSendConfig;

    @Autowired
    private EmailAssembleTemplate emailAssembleTemplate;


    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_SEND_EMAIL_MESSAGE, condition = "#data.supplierId  + #data.dataId")
    public void sendSyncEmail(SyncDataDTO data, OpensourceDto opensourceDto, SyncEmailData emailData) {
        try {
            //邮件发送者 告警邮箱
            String alarmEmail = opensourceDto.getAlarmEmail();

            //校验接口订阅信息  该异常是否推送邮件通知
            if (ToolUtil.isEmpty(opensourceDto.getSubscribeSendEmail())
                    || ToolUtil.isEmpty(alarmEmail)
                    || !opensourceDto.getSubscribeSendEmail().contains(String.valueOf(data.getTemplateType()))) {
                log.info("推送第三方数据-响应异常-发送邮件通知,未配置邮件订阅信息，不推送邮件。data：{}",data);
                return;
            }

            // 匹配Email主题
            String templateName = B2BRequestType.matchingTemplateName(data.getTemplateType());
            String emailSubject = templateName + "接口异常告警";

            //匹配Email内容信息
            emailData.setTemplateName(templateName);
            //第三方响应异常信息截取
            emailData.setErrMessage(emailData.getErrMessage().length() > 2000 ? emailData.getErrMessage().substring(0,2000) : emailData.getErrMessage());
            String sendData = emailAssembleTemplate.assembleEmailSendData(emailData, emailAssembleTemplate.SyncErrTemplate());

            //发送邮件
            sendHtmlEmail(alarmEmail,emailSubject, sendData);

            log.info("发送邮件成功,发送内容为：{},邮件内容为：{}",data, sendData);
        }catch (Exception e){
            log.error("推送第三方数据-响应异常-发送邮件通知,发送异常。请求参数：{}",data,e);
        }
    }

    @Override
    public void sendDaySyncReportDataEmail(SyncEmailReportData emailReportData) {
        try{
            //文件名
            String fileName = emailReportData.getFileName();

            //标题
            String emailSubject = fileName + "当前发送总条数：" + emailReportData.getSendCount() + "；失败条数：" + emailReportData.getFailCount();

            //获取开放能力配置信息
            List<OpensourceDto> opensourceList = emailReportData.getOpensourceDtoList();

            //匹配Email内容信息
            String sendData = emailAssembleTemplate.assembleEmailSendData(emailReportData, emailAssembleTemplate.DaySyncReportDataTemplate());

            //循环给各个对接了第三方配置的入驻商配置邮箱发送邮件
            opensourceList.forEach(opensourceDto -> {
                //发送邮件
                try {
                    sendFileEmail(opensourceDto.getAlarmEmail(),emailSubject, sendData,fileName,emailReportData.getFileUrl());
                } catch (Exception e) {
                    log.error("发送同步日志日同步报告-发送邮件通知,邮箱发送异常。入驻商开放能力信息：{}",opensourceDto,e);
                }
            });

            log.info("发送同步日志日同步报告成功,发送内容为：{},邮件内容为：{}",emailReportData, sendData);
        }catch (Exception e){
            log.error("发送同步日志日同步报告-发送邮件通知,发送异常。请求参数：{}",emailReportData,e);
        }
    }


    /**
     * 发送邮件信息 文本格式
     * @param emailTos 多个用逗号隔开
     * @param emailSubject
     * @param sendData
     */
    public void sendTextEmail(String emailTos, String emailSubject, String sendData){
            //发送邮件信息 文本格式
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(emailMessageSendConfig.getUsername());//发送者.
            message.setSubject(emailSubject);//邮件主题
            message.setText(sendData);//邮件内容.

        //循环发送
        for (String emailTo : emailTos.split(",")) {
            message.setTo(emailTo);//接收者.

            mailSender.send(message);//发送邮件
        }
    }

    /**
     * 发送邮件信息 html格式
     * @param emailTos 多个用逗号隔开
     * @param emailSubject
     * @param sendData
     */
    public void sendHtmlEmail(String emailTos, String emailSubject, String sendData) throws Exception{
        //发送邮件信息 html格式
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);

        helper.setFrom(emailMessageSendConfig.getUsername());//发送者.
        helper.setSubject(emailSubject);//邮件主题.
        helper.setText(sendData,true);//邮件内容.

        //循环发送
        for (String emailTo : emailTos.split(",")) {
            helper.setTo(emailTo);//接收者.
            mailSender.send(mimeMessage);//发送邮件
        }
    }

    /**
     * 发送邮件信息 附件格式
     * @param emailTos 多个用逗号隔开
     * @param emailSubject
     * @param sendData
     */
    public void sendFileEmail(String emailTos, String emailSubject, String sendData, String fileName, String filePath) throws Exception{
        //发送邮件信息 附件格式
        MimeMessage mimeMessage =  mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
        //基本设置.
        helper.setFrom(emailMessageSendConfig.getUsername());//发送者.
        helper.setSubject(emailSubject);//邮件主题.
        helper.setText(sendData,true);//邮件内容.


        //附件1,获取文件对象.  远程文件
        UrlResource file = new UrlResource(filePath);

        //添加附件，这里第一个参数是在邮件中显示的名称，也可以直接是head.jpg，但是一定要有文件后缀，不然就无法显示图片了。
        helper.addAttachment(fileName, file);

        /*//附件2  本地文件
        FileSystemResource file2 = new FileSystemResource(newFile("D:/test/head/head2.jpg"));
        helper.addAttachment("头像2.jpg", file2);*/

        //循环发送
        for (String emailTo : emailTos.split(",")) {
            helper.setTo(emailTo);//接收者.
            mailSender.send(mimeMessage);//发送邮件
        }
    }
}
