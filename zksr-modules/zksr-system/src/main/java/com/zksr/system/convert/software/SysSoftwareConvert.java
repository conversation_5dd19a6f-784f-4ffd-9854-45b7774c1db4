package com.zksr.system.convert.software;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.software.dto.SoftwareDTO;
import com.zksr.system.controller.software.vo.SysSoftwareAccountRespVO;
import com.zksr.system.domain.SysPartner;
import com.zksr.system.domain.SysSoftware;
import com.zksr.system.controller.software.vo.SysSoftwareRespVO;
import com.zksr.system.controller.software.vo.SysSoftwareSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 软件商信息 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-11-19
*/
@Mapper
public interface SysSoftwareConvert {

    SysSoftwareConvert INSTANCE = Mappers.getMapper(SysSoftwareConvert.class);

    SysSoftwareRespVO convert(SysSoftware sysSoftware);

    SysSoftware convert(SysSoftwareSaveReqVO sysSoftwareSaveReq);

    PageResult<SysSoftwareRespVO> convertPage(PageResult<SysSoftware> sysSoftwarePage);

    PageResult<SysSoftwareAccountRespVO> convert(PageResult<SysPartner> pageResult);

    SoftwareDTO convertDTO(SysSoftware sysSoftware);
}