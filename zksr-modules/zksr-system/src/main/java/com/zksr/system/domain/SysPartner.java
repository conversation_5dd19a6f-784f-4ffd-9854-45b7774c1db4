package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;

/**
 * 平台商信息对象 sys_partner
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@TableName(value = "sys_partner")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysPartner extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @TableId(type = IdType.ASSIGN_ID)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 平台名称 */
    @Excel(name = "平台名称")
    private String partnerName;

    /** 关联平台商管理员的账号id */
    @Excel(name = "关联平台商管理员的账号id")
    private Long partnerUserId;

    /** 负责人姓名 */
    @Excel(name = "负责人姓名")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 公司地址 */
    @Excel(name = "公司地址")
    private String contactAddress;

    /** 状态 数据字典sys_partner_status */
    @Excel(name = "状态 数据字典sys_partner_status")
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 来源 */
    @Excel(name = "来源")
    private String source;

    /** 城市数量 */
    @ApiModelProperty(value = "城市数量")
    private Integer areaNum;

    /** 运营商数量 */
    @ApiModelProperty(value = "运营商数量")
    private Integer dcNum;

    /** 入驻商数量 */
    @ApiModelProperty(value = "入驻商数量")
    private Integer supplierNum;

    /** 平台编码 */
    @ApiModelProperty(value = "平台编码")
    private String partnerCode;

    /** 租户编码 */
    @Excel(name = "租户编码")
    private String saasTenantCode;

    /**
     * 猎鹰服务ID
     */
    private Long sid;

    /** 用户名 */
    @ApiModelProperty(value = "用户名")
    @TableField(exist = false)
    private String userName;

    @ApiModelProperty(value = "平台商账户")
    @TableField(exist = false)
    private String partnerAccount;

    @ApiModelProperty(value = "平台商密码")
    @TableField(exist = false)
    private String partnerAccountPwd;

    /** 软件商ID */
    @Excel(name = "软件商ID")
    private Long softwareId;

    @Excel(name = "软件商分润比例")
    private BigDecimal softwareRate;
}
