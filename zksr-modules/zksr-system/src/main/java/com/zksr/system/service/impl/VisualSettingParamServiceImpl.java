package com.zksr.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.zksr.system.service.IVisualSettingParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.stereotype.Service;

import java.io.StringWriter;
import java.util.Map;

/**
 * 可视化配置参数Service业务层处理
 *
 * <AUTHOR>
 * @date 2024/7/1:10:34
 */
@Slf4j
@Service
public class VisualSettingParamServiceImpl implements IVisualSettingParamService {

    @Override
    public String replaceParam(Object paramData, String velocityTemplate) {
        if (ObjectUtil.isEmpty(paramData)) {
            throw new RuntimeException("参数数据对象不能为空");
        }
        if (ObjectUtil.isEmpty(velocityTemplate)) {
            throw new RuntimeException("模版不能为空");
        }
        // 初始化VelocityEngine
        // 配置VelocityEngine（可选），这里直接使用默认配置
        VelocityEngine velocityEngine = new VelocityEngine();

        // 准备数据模型
        VelocityContext context = new VelocityContext();
        // 将数据对象先转换为json，再将json转换为Map对象
        String jsonStr = JSONUtil.toJsonStr(paramData);
        System.out.println(jsonStr);
        log.info("开始替换参数: 参数：{}，转换模版：{}", jsonStr, velocityTemplate);
        Map paramMap = JSONUtil.toBean(jsonStr, Map.class);
        System.out.println(paramMap);
        for (Object o : paramMap.keySet()) {
            System.out.println(o.toString());
            System.out.println(paramMap.get(o));
            context.put(o.toString(), paramMap.get(o));
        }
        // 合成输出
        StringWriter writer = new StringWriter();
        // 直接使用字符串模板内容，而非通过getTemplate方法
        boolean result = velocityEngine.evaluate(context, writer, "TemplateName", velocityTemplate);
        if (!result) {
            throw new RuntimeException("参数转换失败：使用velocity模板替换参数时出错");
        }
        log.info("替换参数结果：{}", writer.toString());
        // 输出结果
        return writer.toString();
    }

}
