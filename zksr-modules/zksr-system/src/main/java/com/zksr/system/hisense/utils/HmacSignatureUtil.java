package com.zksr.system.hisense.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

public class HmacSignatureUtil {

    private static final String HMAC_SHA256 = "HmacSHA256";
    private static final String DATE_FORMAT = "EEE, d-MMM-yyyy HH:mm:ss z";

    /**
     * 生成签名
     * @param secretKey 密钥（对应app_key）
     * @param method HTTP方法（GET/POST/PUT等）
     * @param uri 请求路径
     * @param queryParams 查询参数
     * @param accessKey 访问密钥（对应app_id）
     * @param date GMT格式日期
     * @param signedHeaders 需要签名的请求头
     * @return Base64编码的签名
     */
    public static String generateSignature(String secretKey, String method, String uri,
                                           Map<String, String> queryParams, String accessKey,
                                           String date, Map<String, String> signedHeaders) {
        try {
            // 构建signing_string
            String signingString = buildSigningString(method, uri, queryParams, accessKey, date, signedHeaders);

            // 生成HMAC签名
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKeySpec);

            byte[] hash = mac.doFinal(signingString.getBytes(StandardCharsets.UTF_8));

            // 返回Base64编码
            return DatatypeConverter.printBase64Binary(hash);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("生成签名失败", e);
        }
    }

    /**
     * 构建签名字符串
     */
    private static String buildSigningString(String method, String uri, Map<String, String> queryParams,
                                             String accessKey, String date, Map<String, String> signedHeaders) {
        StringBuilder sb = new StringBuilder();

        // HTTP Method
        sb.append(method.toUpperCase()).append("\n");

        // HTTP URI
        sb.append(uri).append("\n");

        // Canonical Query String
        String canonicalQueryString = buildCanonicalQueryString(queryParams);
        sb.append(canonicalQueryString).append("\n");

        // Access Key
        sb.append(accessKey).append("\n");

        // Date
        sb.append(date).append("\n");

        // Signed Headers String
        String signedHeadersString = buildSignedHeadersString(signedHeaders);
        sb.append(signedHeadersString);

        return sb.toString();
    }

    /**
     * 构建规范化的查询字符串
     */
    private static String buildCanonicalQueryString(Map<String, String> queryParams) {
        if (queryParams == null || queryParams.isEmpty()) {
            return "";
        }

        // 按key字典序排序
        TreeMap<String, String> sortedParams = new TreeMap<>(queryParams);

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=");
            if (entry.getValue() != null) {
                sb.append(entry.getValue());
            }
        }

        return sb.toString();
    }

    /**
     * 构建签名头字符串
     */
    private static String buildSignedHeadersString(Map<String, String> signedHeaders) {
        if (signedHeaders == null || signedHeaders.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : signedHeaders.entrySet()) {
            sb.append(entry.getKey()).append(":").append(entry.getValue()).append("\n");
        }

        return sb.toString();
    }

    /**
     * 获取GMT格式的当前时间
     */
    public static String getCurrentGMTDate() {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT, Locale.ENGLISH);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        return sdf.format(new Date());
    }
}