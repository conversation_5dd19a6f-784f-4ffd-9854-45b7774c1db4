package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.third.message.dto.CommonMessageRespVO;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.controller.message.vo.SysMessageLogPageReqVO;
import com.zksr.system.controller.message.vo.SysMessageLogResendReqVO;
import com.zksr.system.controller.message.vo.SysMessageLogRespVO;
import com.zksr.system.domain.SysMessageLog;

import java.util.List;

/**
 * 消息发送记录Service接口
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
public interface ISysMessageLogService {

    /**
     * 获得消息发送记录
     *
     * @param msgId ${pkColumn.columnComment}
     * @return 消息发送记录
     */
    public SysMessageLog getSysMessageLog(Long msgId);

    /**
     * 获得消息发送记录分页
     *
     * @param pageReqVO 分页查询
     * @return 消息发送记录分页
     */
    PageResult<SysMessageLogRespVO> getSysMessageLogPage(SysMessageLogPageReqVO pageReqVO);

    /**
     * 保存发送记录
     * @param messageRespVOList 消息发送结果
     * @param messageTemplate   消息模版
     */
    void insertSysMessageLog(List<CommonMessageRespVO> messageRespVOList, MessageTemplateDTO messageTemplate);

    /**
     * 消息重发
     * @param resendReqVO   重发消息数据
     */
    void resend(SysMessageLogResendReqVO resendReqVO);
}
