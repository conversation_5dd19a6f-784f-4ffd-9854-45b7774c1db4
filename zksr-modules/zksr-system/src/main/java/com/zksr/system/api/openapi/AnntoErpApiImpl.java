package com.zksr.system.api.openapi;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.openapi.dto.AnntoErpRequestDTO;
import com.zksr.system.api.openapi.dto.AnntoErpResultDTO;
import com.zksr.system.service.impl.SysAnntoErpServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@InnerAuth
public class AnntoErpApiImpl implements AnntoErpApi {
    @Autowired
    private SysAnntoErpServiceImpl sysAnntoErpService;
    @Override
    public CommonResult<AnntoErpResultDTO<String>> sendErp(AnntoErpRequestDTO requestDTO) {
        return CommonResult.success(sysAnntoErpService.sendErp(requestDTO));
    }
}
