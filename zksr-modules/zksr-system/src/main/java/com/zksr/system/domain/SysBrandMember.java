package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 品牌商子账户对象 sys_brand_member
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@TableName(value = "sys_brand_member")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysBrandMember extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long brandMemberId;

    /** 运营商编号 */
    @Excel(name = "运营商编号")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 品牌商ID */
    @Excel(name = "品牌商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long brandMerchantId;

    /** 联系人手机号 */
    @Excel(name = "联系人手机号")
    private String contactPhone;

    /** 联系人名称 */
    @Excel(name = "联系人名称")
    private String contactName;

    /** 联系地址 */
    @Excel(name = "联系地址")
    private String contactAddress;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 关联管理员id */
    @Excel(name = "关联管理员id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysUserId;

    /** 关联品牌集合 */
    @Excel(name = "关联品牌集合")
    private String brandIds;

    /** 关联城市集合 */
    @Excel(name = "关联城市集合")
    private String areaIds;

    /** 帐号状态（0正常 1停用） */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private Integer status;

}
