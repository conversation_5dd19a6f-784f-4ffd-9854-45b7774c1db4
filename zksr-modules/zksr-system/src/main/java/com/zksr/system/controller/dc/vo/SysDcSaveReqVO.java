package com.zksr.system.controller.dc.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.system.api.partnerPolicy.dto.DcOtherSettingPolicyDTO;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 运营商对象 sys_dc
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@Data
@ApiModel("运营商 - sys_dc分页 Request VO")
public class SysDcSaveReqVO {
    private static final long serialVersionUID = 1L;

    @Excel(name = "运营商ID")
    @ApiModelProperty(value = "运营商ID", example = "0 (修改必填)")
    private Long dcId;

    /**
     * 运营商状态（0正常 1停用）
     */
    @Excel(name = "运营商状态", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "运营商状态0=正常,1=停用", required = true, example = "0")
    private String status;

    /**
     * 运营商备注
     */
    @Excel(name = "运营商备注")
    @ApiModelProperty(value = "运营商备注", example = "这里是备注")
    private String memo;

    /**
     * 运营商地址
     */
    @Excel(name = "运营商地址")
    @ApiModelProperty(value = "运营商地址", required = true, example = "长沙市雨花区嘉盛财政中心")
    private String address;

    /**
     * 运营商编号
     */
    @Excel(name = "运营商编号")
    @ApiModelProperty(value = "运营商编号", required = true, example = "zhzg001")
    private String dcCode;

    /**
     * 运营商名称
     */
    @Excel(name = "运营商名称")
    @ApiModelProperty(value = "运营商名称", required = true, example = "知花知果")
    private String dcName;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人", required = true, example = "王老五")
    private String contractName;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话(不可编辑)", required = true, example = "123456")
    private String contractPhone;

    /**
     * 联系电话
     */
    @Excel(name = "后台管理密码(不可编辑, 只可新增使用)")
    @ApiModelProperty(value = "后台管理密码(不可编辑, 只可新增使用)", example = "123456")
    private String password;

    /**
     * 业务区域(城市)ID
     */
    @Excel(name = "业务区域(城市)ID")
    @ApiModelProperty(value = "业务区域(城市)ID", required = true, example = "123456")
    private List<Long> sysAreaId;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    @ApiModelProperty(value = "公司名称", required = true, example = "xxxx公司")
    private String companyName;

    /**
     * 账号
     */
    @Excel(name = "账号")
    @ApiModelProperty(value = "账号", required = true, example = "xxxxx")
    private String userName;

    //支付账号配置
    /** 商户绑定信息 */
    @ApiModelProperty(value = "商户绑定信息")
    private List<PlatformSimpleBindVO> platformSimpleBindList;

    @ApiModelProperty("运营商其他配置")
    private DcOtherSettingPolicyDTO dcOtherSettingPolicyDTO;

    /**
     * 起送价
     */
    @ApiModelProperty("本地起送价")
    private BigDecimal minAmt;

    /**
     * 全国起送价
     */
    @ApiModelProperty("全国起送价")
    private BigDecimal globalMinAmt;

}
