package com.zksr.system.mapper;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.domain.SysPrintSettings;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysPrintTemplate;
import com.zksr.system.controller.print.vo.SysPrintTemplatePageReqVO;


/**
 * 打印模版Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Mapper
public interface SysPrintTemplateMapper extends BaseMapperX<SysPrintTemplate> {
    default PageResult<SysPrintTemplate> selectPage(SysPrintTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysPrintTemplate>()
                    .eqIfPresent(SysPrintTemplate::getPrintTemplateId, reqVO.getPrintTemplateId())
                    .likeIfPresent(SysPrintTemplate::getModuleName, reqVO.getModuleName())
                    .eqIfPresent(SysPrintTemplate::getModuleType, reqVO.getModuleType())
                    .eqIfPresent(SysPrintTemplate::getTemplateWidth, reqVO.getTemplateWidth())
                    .eqIfPresent(SysPrintTemplate::getTemplateHeight, reqVO.getTemplateHeight())
                    .eqIfPresent(SysPrintTemplate::getPaperWidth, reqVO.getPaperWidth())
                    .eqIfPresent(SysPrintTemplate::getPaperHeight, reqVO.getPaperHeight())
                    .eqIfPresent(SysPrintTemplate::getPrintContent, reqVO.getPrintContent())
                    .eqIfPresent(SysPrintTemplate::getPaperType, reqVO.getPaperType())
                    .eq(SysPrintTemplate::getDelFlag, NumberPool.INT_ZERO)
                .orderByDesc(SysPrintTemplate::getPrintTemplateId));
    }


    default Long selectCountByType(String moduleType, String paperType) {
        return selectCount(
                new LambdaQueryWrapperX<SysPrintTemplate>()
                        .eq(SysPrintTemplate::getModuleType, moduleType)
                        .eq(SysPrintTemplate::getPaperType, paperType)
                        .eq(SysPrintTemplate::getDelFlag, NumberPool.INT_ZERO)
        );
    }

    default SysPrintTemplate selectByType(String moduleType, String paperType) {
        return selectOne(
                new LambdaQueryWrapperX<SysPrintTemplate>()
                        .eq(SysPrintTemplate::getModuleType, moduleType)
                        .eq(SysPrintTemplate::getPaperType, paperType)
                        .eq(SysPrintTemplate::getDelFlag, NumberPool.INT_ZERO)
                        .last(StringPool.LIMIT_ONE)
        );
    }
}
