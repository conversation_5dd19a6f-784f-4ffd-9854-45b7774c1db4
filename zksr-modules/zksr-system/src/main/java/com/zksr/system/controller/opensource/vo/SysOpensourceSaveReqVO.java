package com.zksr.system.controller.opensource.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import javax.validation.constraints.NotBlank;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 开放能力对象 sys_opensource
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Data
@ApiModel("开放能力 - sys_opensource分页 Request VO")
public class SysOpensourceSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 开放能力 */
    @ApiModelProperty(value = "开放资源id")
    private Long opensourceId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 资源id */
    @Excel(name = "资源id")
    @ApiModelProperty(value = "资源id", required = true)
    private Long merchantId;

    /** 资源类型（数据字典 supplier-入驻商等） */
    @Excel(name = "资源类型", readConverterExp = "数=据字典,s=upplier-入驻商等")
    @ApiModelProperty(value = "资源类型", required = true)
    private String merchantType;

    /** 开发能力key */
    @Excel(name = "开发能力key")
    @ApiModelProperty(value = "开发能力key")
    private String sourceKey;

    /** 对外加密串 */
    @Excel(name = "对外加密串")
    @ApiModelProperty(value = "对外加密串")
    private String sourceSecret;

    /** ip白名单 */
    @Excel(name = "ip白名单")
    @ApiModelProperty(value = "ip白名单")
    private String ipWhiteList;

    /** ERP接口配置 公钥 */
    @Excel(name = "ERP接口配置 公钥")
    @ApiModelProperty(value = "ERP接口配置 公钥")
    private String publicKey;

    /** ERP接口配置 私钥 */
    @Excel(name = "ERP接口配置 私钥")
    @ApiModelProperty(value = "ERP接口配置 私钥")
    private String privateKey;

    /** erp请求地址 */
    @Excel(name = "商城显示物流信息 0第三方  1本地")
    @ApiModelProperty(value = "商城显示物流信息 0第三方  1本地")
    private String logisticsInfo;

    /**
     * 订单是否自动推送第三方
     */
    @ApiModelProperty("订单是否自动推送第三方 0手动推送  1 自动推送")
    private Integer orderAutoPush;

    /**
     * 订单延时推送时间
     */
    @ApiModelProperty("订单延时推送时间")
    private Integer orderDelayPushTime;

    /** 对接地址 */
    @ApiModelProperty("对接地址")
    @Excel(name = "对接地址")
    private String sendUrl;

    /** 对接唯一编码 */
    @ApiModelProperty("对接唯一编码")
    @Excel(name = "对接唯一编码")
    private String sendCode;

    /** 可视化接口配置主表ID */
    @ApiModelProperty("可视化接口配置主表ID")
    @Excel(name = "可视化接口配置主表ID")
    private Long visualMasterId;

    /**
     * 是否同步标准价格 0-不同步 1-同步（默认同步）
     */
    @Excel(name = "是否同步标准价格 0-不同步 1-同步（默认同步）")
    @ApiModelProperty("是否同步标准价格 0-不同步 1-同步（默认同步）")
    private Integer syncMarkPrice;

    /**
     * 是否同步供货价格 0-不同步 1-同步（默认同步）
     */
    @Excel(name = "是否同步供货价格 0-不同步 1-同步（默认同步）")
    @ApiModelProperty("是否同步供货价格 0-不同步 1-同步（默认同步）")
    private Integer syncCostPrice;


    /**
     * 是否开启统配入驻商  0否 1是
     */
    @Excel(name = "是否开启统配入驻商  0否 1是")
    @ApiModelProperty("是否开启统配入驻商  0否 1是")
    private Integer superSupplier;

    /**
     * 是否开启统配入驻商  0否 1是
     */
    @Excel(name = "是否开启货到付款清账功能  0否 1是")
    @ApiModelProperty("是否开启货到付款清账功能  0否 1是")
    private Integer isHdfkSettle;

    /**
     * 赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）
     */
    @Excel(name = "赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）")
    @ApiModelProperty("赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）")
    private Integer giftPriceType;

    /**
     * 订单同步是否合单（0否，1是）
     */
    @Excel(name = "订单同步是否合单（0否，1是）")
    @ApiModelProperty("订单同步是否合单（0否，1是）")
    private Integer orderMergeFlag;

    /** 告警邮箱  多个邮箱用逗号隔开*/
    @Excel(name = "告警邮箱")
    @ApiModelProperty("告警邮箱")
    private String alarmEmail;

    /** 接口发送邮件订阅 选择需要发送的接口 用逗号隔开 */
    @Excel(name = "接口发送邮件订阅 选择需要发送的接口 用逗号隔开")
    @ApiModelProperty("接口发送邮件订阅 选择需要发送的接口 用逗号隔开")
    private String subscribeSendEmail;

}
