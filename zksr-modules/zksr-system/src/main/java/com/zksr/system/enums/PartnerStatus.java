package com.zksr.system.enums;

public enum PartnerStatus {
	INIT(1, "未审核"), OK(2, "已审核"), STOP(3, "停用");

    long code;
    String message;

    PartnerStatus(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public long getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static String valueOf(Integer value) {
        if (value == null) {
            return "";
        } else {
            for (PartnerStatus ms : PartnerStatus.values()) {
                if (ms.getCode() == value) {
                    return ms.getMessage();
                }
            }
            return "";
        }
    }
}
