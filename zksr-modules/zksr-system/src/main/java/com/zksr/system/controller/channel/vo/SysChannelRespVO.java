package com.zksr.system.controller.channel.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 渠道信息对象 sys_channel
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@Data
@ApiModel("渠道信息 - sys_channel Response VO")
public class SysChannelRespVO {
    private static final long serialVersionUID = 1L;

    /** 渠道id */
    @ApiModelProperty(value = "备注", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long channelId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 渠道名 */
    @Excel(name = "渠道名")
    @ApiModelProperty(value = "渠道名", example = "示例值")
    private String channelName;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "示例值")
    private String memo;

    /** 是否支持货到付款(0,否 1,是) */
    @Excel(name = "是否支持货到付款(0,否 1,是)")
    @ApiModelProperty(value = "是否支持货到付款(0,否 1,是)", example = "0")
    private Integer hdfkSupport;

    @Excel(name = "是否停用")
    @ApiModelProperty(value = "0 启用 1 停用", example = "示例值")
    private Integer deleted;

    @Excel(name = "是否停用")
    @ApiModelProperty(value = "1 启用 0 停用", example = "示例值")
    private Integer status;
}
