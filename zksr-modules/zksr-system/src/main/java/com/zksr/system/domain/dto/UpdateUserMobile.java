package com.zksr.system.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2025/03/27 16:26
 */
@Data
public class UpdateUserMobile {
    @NotBlank(message = "旧的密保手机号不能为空")
    private String oldMobile;

    @NotBlank(message = "新的密保手机号不能为空")
    @Pattern(regexp = "^[1][3,4,5,6,7,8,9][0-9]{9}$",message = "新的密保手机号不正确")
    private String mobile;

    @NotBlank(message = "验证码不能为空")
    private String code;
}
