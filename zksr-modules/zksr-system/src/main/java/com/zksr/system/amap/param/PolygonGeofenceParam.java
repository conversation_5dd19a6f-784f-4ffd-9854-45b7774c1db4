package com.zksr.system.amap.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 多表现围栏请求实体类
 * @Author: liuxingyu
 * @Date: 2024/3/27 16:10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PolygonGeofenceParam {
    /**
     * 用户在高德地图官网申请Web服务API类型Key
     */
    @ApiModelProperty("高德Key")
    private String key;

    /**
     * sid为猎鹰service唯一编号
     */
    @ApiModelProperty("服务唯一编号")
    private Long sid;

    /**
     * 围栏唯一标识，指定要更新的围栏
     */
    @ApiModelProperty("围栏id")
    private Long gfid;

    /**
     * 在同一个 sid 下不可重复，不可为空。
     * 支持中文、英文大小字母、英文下划线"_"、英文横线"-"和数字，长度不大于128个字符
     */
    @ApiModelProperty("围栏名称")
    private String name;

    /**
     * 支持中文、英文大小字母、英文下划线"_"、英文横线"-"和数字，长度不大于128个字符
     */
    @ApiModelProperty("围栏描述")
    private String desc;

    /**
     * 格式 X1,Y1;X2,Y2;...
     * 顶点顺序可按顺时针或逆时针排列；
     * 1. 普通地理围栏：顶点个数在3-100个之间，外接矩形面积<100平方公里；
     * 2. 大范围地理围栏：顶点个数在3-100个之间，外接矩形面积小于1000平方公里，请您通过工单与我们联系。
     */
    @ApiModelProperty("多边形顶点坐标")
    private String points;
}
