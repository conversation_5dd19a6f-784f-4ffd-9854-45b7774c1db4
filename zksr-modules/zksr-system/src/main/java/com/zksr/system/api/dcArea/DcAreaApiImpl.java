package com.zksr.system.api.dcArea;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.dcArea.dto.DcAreaDTO;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import com.zksr.system.service.ISysDcAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@ApiIgnore
public class DcAreaApiImpl implements DcAreaApi{

    @Autowired
    private ISysDcAreaService sysDcAreaService;

    @Override
    public CommonResult<DcAreaDTO> getDcAreaByDcIdOrAreaId(@RequestParam(value = "dcId", required = false) Long dcId, @RequestParam(value = "areaId", required = false) Long areaId,@RequestParam(value = "sysCode",required = false) Long sysCode) {
        return CommonResult.success(HutoolBeanUtils.toBean(sysDcAreaService.getDcAreaByDcIdOrAreaId(dcId,areaId,sysCode), DcAreaDTO.class));
    }

    /**
    * @Description: 根据运营商ID获取绑定的区域
    * @Author: liuxingyu
    * @Date: 2024/4/12 10:59
    */
    @Override
    @InnerAuth
    public CommonResult<DcAreaGroupDTO> getByDcId(Long dcId) {
        return CommonResult.success(sysDcAreaService.getDcAreaGroup(dcId));
    }
}
