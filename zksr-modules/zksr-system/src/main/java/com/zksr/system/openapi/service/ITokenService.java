package com.zksr.system.openapi.service;


import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.openapi.controller.vo.GetTokenRequest;
import com.zksr.system.openapi.controller.vo.GetTokenResponse;

public interface ITokenService {
    GetTokenResponse getToken(GetTokenRequest request);

    SysInterfaceLog getOpenapiInitLog(Integer requestType, String operationType,String reqId);

    /**
     * 开放接口异步处理前校验
     *
     * @param reqId  推荐 唯一标识 + _ + 接口类型 + _ + 状态信息（如状态、时间戳等）
     */
    boolean checkReceiveLogBefore(String reqId);

    /**
     * 开放接口异步处理后校验
     * @param interfaceLog
     */
    boolean checkReceiveLogAfter(SysInterfaceLog interfaceLog);
}
